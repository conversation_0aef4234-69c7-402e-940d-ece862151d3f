/*** Autogenerated by WIDL 10.12 from include/objidlbase.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __objidlbase_h__
#define __objidlbase_h__

/* Forward declarations */

#ifndef __IMarshal_FWD_DEFINED__
#define __IMarshal_FWD_DEFINED__
typedef interface IMarshal IMarshal;
#ifdef __cplusplus
interface IMarshal;
#endif /* __cplusplus */
#endif

#ifndef __INoMarshal_FWD_DEFINED__
#define __INoMarshal_FWD_DEFINED__
typedef interface INoMarshal INoMarshal;
#ifdef __cplusplus
interface INoMarshal;
#endif /* __cplusplus */
#endif

#ifndef __IAgileObject_FWD_DEFINED__
#define __IAgileObject_FWD_DEFINED__
typedef interface IAgileObject IAgileObject;
#ifdef __cplusplus
interface IAgileObject;
#endif /* __cplusplus */
#endif

#ifndef __IAgileReference_FWD_DEFINED__
#define __IAgileReference_FWD_DEFINED__
typedef interface IAgileReference IAgileReference;
#ifdef __cplusplus
interface IAgileReference;
#endif /* __cplusplus */
#endif

#ifndef __IMarshal2_FWD_DEFINED__
#define __IMarshal2_FWD_DEFINED__
typedef interface IMarshal2 IMarshal2;
#ifdef __cplusplus
interface IMarshal2;
#endif /* __cplusplus */
#endif

#ifndef __IMalloc_FWD_DEFINED__
#define __IMalloc_FWD_DEFINED__
typedef interface IMalloc IMalloc;
#ifdef __cplusplus
interface IMalloc;
#endif /* __cplusplus */
#endif

#ifndef __IStdMarshalInfo_FWD_DEFINED__
#define __IStdMarshalInfo_FWD_DEFINED__
typedef interface IStdMarshalInfo IStdMarshalInfo;
#ifdef __cplusplus
interface IStdMarshalInfo;
#endif /* __cplusplus */
#endif

#ifndef __IExternalConnection_FWD_DEFINED__
#define __IExternalConnection_FWD_DEFINED__
typedef interface IExternalConnection IExternalConnection;
#ifdef __cplusplus
interface IExternalConnection;
#endif /* __cplusplus */
#endif

#ifndef __IMultiQI_FWD_DEFINED__
#define __IMultiQI_FWD_DEFINED__
typedef interface IMultiQI IMultiQI;
#ifdef __cplusplus
interface IMultiQI;
#endif /* __cplusplus */
#endif

#ifndef __AsyncIMultiQI_FWD_DEFINED__
#define __AsyncIMultiQI_FWD_DEFINED__
typedef interface AsyncIMultiQI AsyncIMultiQI;
#ifdef __cplusplus
interface AsyncIMultiQI;
#endif /* __cplusplus */
#endif

#ifndef __IInternalUnknown_FWD_DEFINED__
#define __IInternalUnknown_FWD_DEFINED__
typedef interface IInternalUnknown IInternalUnknown;
#ifdef __cplusplus
interface IInternalUnknown;
#endif /* __cplusplus */
#endif

#ifndef __IEnumUnknown_FWD_DEFINED__
#define __IEnumUnknown_FWD_DEFINED__
typedef interface IEnumUnknown IEnumUnknown;
#ifdef __cplusplus
interface IEnumUnknown;
#endif /* __cplusplus */
#endif

#ifndef __IEnumString_FWD_DEFINED__
#define __IEnumString_FWD_DEFINED__
typedef interface IEnumString IEnumString;
#ifdef __cplusplus
interface IEnumString;
#endif /* __cplusplus */
#endif

#ifndef __ISequentialStream_FWD_DEFINED__
#define __ISequentialStream_FWD_DEFINED__
typedef interface ISequentialStream ISequentialStream;
#ifdef __cplusplus
interface ISequentialStream;
#endif /* __cplusplus */
#endif

#ifndef __IStream_FWD_DEFINED__
#define __IStream_FWD_DEFINED__
typedef interface IStream IStream;
#ifdef __cplusplus
interface IStream;
#endif /* __cplusplus */
#endif

#ifndef __IRpcChannelBuffer_FWD_DEFINED__
#define __IRpcChannelBuffer_FWD_DEFINED__
typedef interface IRpcChannelBuffer IRpcChannelBuffer;
#ifdef __cplusplus
interface IRpcChannelBuffer;
#endif /* __cplusplus */
#endif

#ifndef __IRpcChannelBuffer2_FWD_DEFINED__
#define __IRpcChannelBuffer2_FWD_DEFINED__
typedef interface IRpcChannelBuffer2 IRpcChannelBuffer2;
#ifdef __cplusplus
interface IRpcChannelBuffer2;
#endif /* __cplusplus */
#endif

#ifndef __IAsyncRpcChannelBuffer_FWD_DEFINED__
#define __IAsyncRpcChannelBuffer_FWD_DEFINED__
typedef interface IAsyncRpcChannelBuffer IAsyncRpcChannelBuffer;
#ifdef __cplusplus
interface IAsyncRpcChannelBuffer;
#endif /* __cplusplus */
#endif

#ifndef __IRpcChannelBuffer3_FWD_DEFINED__
#define __IRpcChannelBuffer3_FWD_DEFINED__
typedef interface IRpcChannelBuffer3 IRpcChannelBuffer3;
#ifdef __cplusplus
interface IRpcChannelBuffer3;
#endif /* __cplusplus */
#endif

#ifndef __IRpcSyntaxNegotiate_FWD_DEFINED__
#define __IRpcSyntaxNegotiate_FWD_DEFINED__
typedef interface IRpcSyntaxNegotiate IRpcSyntaxNegotiate;
#ifdef __cplusplus
interface IRpcSyntaxNegotiate;
#endif /* __cplusplus */
#endif

#ifndef __IRpcProxyBuffer_FWD_DEFINED__
#define __IRpcProxyBuffer_FWD_DEFINED__
typedef interface IRpcProxyBuffer IRpcProxyBuffer;
#ifdef __cplusplus
interface IRpcProxyBuffer;
#endif /* __cplusplus */
#endif

#ifndef __IRpcStubBuffer_FWD_DEFINED__
#define __IRpcStubBuffer_FWD_DEFINED__
typedef interface IRpcStubBuffer IRpcStubBuffer;
#ifdef __cplusplus
interface IRpcStubBuffer;
#endif /* __cplusplus */
#endif

#ifndef __IPSFactoryBuffer_FWD_DEFINED__
#define __IPSFactoryBuffer_FWD_DEFINED__
typedef interface IPSFactoryBuffer IPSFactoryBuffer;
#ifdef __cplusplus
interface IPSFactoryBuffer;
#endif /* __cplusplus */
#endif

#ifndef __IChannelHook_FWD_DEFINED__
#define __IChannelHook_FWD_DEFINED__
typedef interface IChannelHook IChannelHook;
#ifdef __cplusplus
interface IChannelHook;
#endif /* __cplusplus */
#endif

#ifndef __IClientSecurity_FWD_DEFINED__
#define __IClientSecurity_FWD_DEFINED__
typedef interface IClientSecurity IClientSecurity;
#ifdef __cplusplus
interface IClientSecurity;
#endif /* __cplusplus */
#endif

#ifndef __IServerSecurity_FWD_DEFINED__
#define __IServerSecurity_FWD_DEFINED__
typedef interface IServerSecurity IServerSecurity;
#ifdef __cplusplus
interface IServerSecurity;
#endif /* __cplusplus */
#endif

#ifndef __IRpcOptions_FWD_DEFINED__
#define __IRpcOptions_FWD_DEFINED__
typedef interface IRpcOptions IRpcOptions;
#ifdef __cplusplus
interface IRpcOptions;
#endif /* __cplusplus */
#endif

#ifndef __IGlobalOptions_FWD_DEFINED__
#define __IGlobalOptions_FWD_DEFINED__
typedef interface IGlobalOptions IGlobalOptions;
#ifdef __cplusplus
interface IGlobalOptions;
#endif /* __cplusplus */
#endif

#ifndef __ISurrogate_FWD_DEFINED__
#define __ISurrogate_FWD_DEFINED__
typedef interface ISurrogate ISurrogate;
#ifdef __cplusplus
interface ISurrogate;
#endif /* __cplusplus */
#endif

#ifndef __IGlobalInterfaceTable_FWD_DEFINED__
#define __IGlobalInterfaceTable_FWD_DEFINED__
typedef interface IGlobalInterfaceTable IGlobalInterfaceTable;
#ifdef __cplusplus
interface IGlobalInterfaceTable;
#endif /* __cplusplus */
#endif

#ifndef __ISynchronize_FWD_DEFINED__
#define __ISynchronize_FWD_DEFINED__
typedef interface ISynchronize ISynchronize;
#ifdef __cplusplus
interface ISynchronize;
#endif /* __cplusplus */
#endif

#ifndef __ISynchronizeHandle_FWD_DEFINED__
#define __ISynchronizeHandle_FWD_DEFINED__
typedef interface ISynchronizeHandle ISynchronizeHandle;
#ifdef __cplusplus
interface ISynchronizeHandle;
#endif /* __cplusplus */
#endif

#ifndef __ISynchronizeEvent_FWD_DEFINED__
#define __ISynchronizeEvent_FWD_DEFINED__
typedef interface ISynchronizeEvent ISynchronizeEvent;
#ifdef __cplusplus
interface ISynchronizeEvent;
#endif /* __cplusplus */
#endif

#ifndef __ISynchronizeContainer_FWD_DEFINED__
#define __ISynchronizeContainer_FWD_DEFINED__
typedef interface ISynchronizeContainer ISynchronizeContainer;
#ifdef __cplusplus
interface ISynchronizeContainer;
#endif /* __cplusplus */
#endif

#ifndef __ISynchronizeMutex_FWD_DEFINED__
#define __ISynchronizeMutex_FWD_DEFINED__
typedef interface ISynchronizeMutex ISynchronizeMutex;
#ifdef __cplusplus
interface ISynchronizeMutex;
#endif /* __cplusplus */
#endif

#ifndef __ICancelMethodCalls_FWD_DEFINED__
#define __ICancelMethodCalls_FWD_DEFINED__
typedef interface ICancelMethodCalls ICancelMethodCalls;
#ifdef __cplusplus
interface ICancelMethodCalls;
#endif /* __cplusplus */
#endif

#ifndef __IAsyncManager_FWD_DEFINED__
#define __IAsyncManager_FWD_DEFINED__
typedef interface IAsyncManager IAsyncManager;
#ifdef __cplusplus
interface IAsyncManager;
#endif /* __cplusplus */
#endif

#ifndef __ICallFactory_FWD_DEFINED__
#define __ICallFactory_FWD_DEFINED__
typedef interface ICallFactory ICallFactory;
#ifdef __cplusplus
interface ICallFactory;
#endif /* __cplusplus */
#endif

#ifndef __IRpcHelper_FWD_DEFINED__
#define __IRpcHelper_FWD_DEFINED__
typedef interface IRpcHelper IRpcHelper;
#ifdef __cplusplus
interface IRpcHelper;
#endif /* __cplusplus */
#endif

#ifndef __IReleaseMarshalBuffers_FWD_DEFINED__
#define __IReleaseMarshalBuffers_FWD_DEFINED__
typedef interface IReleaseMarshalBuffers IReleaseMarshalBuffers;
#ifdef __cplusplus
interface IReleaseMarshalBuffers;
#endif /* __cplusplus */
#endif

#ifndef __IWaitMultiple_FWD_DEFINED__
#define __IWaitMultiple_FWD_DEFINED__
typedef interface IWaitMultiple IWaitMultiple;
#ifdef __cplusplus
interface IWaitMultiple;
#endif /* __cplusplus */
#endif

#ifndef __IAddrTrackingControl_FWD_DEFINED__
#define __IAddrTrackingControl_FWD_DEFINED__
typedef interface IAddrTrackingControl IAddrTrackingControl;
#ifdef __cplusplus
interface IAddrTrackingControl;
#endif /* __cplusplus */
#endif

#ifndef __IAddrExclusionControl_FWD_DEFINED__
#define __IAddrExclusionControl_FWD_DEFINED__
typedef interface IAddrExclusionControl IAddrExclusionControl;
#ifdef __cplusplus
interface IAddrExclusionControl;
#endif /* __cplusplus */
#endif

#ifndef __IPipeByte_FWD_DEFINED__
#define __IPipeByte_FWD_DEFINED__
typedef interface IPipeByte IPipeByte;
#ifdef __cplusplus
interface IPipeByte;
#endif /* __cplusplus */
#endif

#ifndef __IPipeLong_FWD_DEFINED__
#define __IPipeLong_FWD_DEFINED__
typedef interface IPipeLong IPipeLong;
#ifdef __cplusplus
interface IPipeLong;
#endif /* __cplusplus */
#endif

#ifndef __IPipeDouble_FWD_DEFINED__
#define __IPipeDouble_FWD_DEFINED__
typedef interface IPipeDouble IPipeDouble;
#ifdef __cplusplus
interface IPipeDouble;
#endif /* __cplusplus */
#endif

#ifndef __IEnumContextProps_FWD_DEFINED__
#define __IEnumContextProps_FWD_DEFINED__
typedef interface IEnumContextProps IEnumContextProps;
#ifdef __cplusplus
interface IEnumContextProps;
#endif /* __cplusplus */
#endif

#ifndef __IContext_FWD_DEFINED__
#define __IContext_FWD_DEFINED__
typedef interface IContext IContext;
#ifdef __cplusplus
interface IContext;
#endif /* __cplusplus */
#endif

#ifndef __IComThreadingInfo_FWD_DEFINED__
#define __IComThreadingInfo_FWD_DEFINED__
typedef interface IComThreadingInfo IComThreadingInfo;
#ifdef __cplusplus
interface IComThreadingInfo;
#endif /* __cplusplus */
#endif

#ifndef __IProcessInitControl_FWD_DEFINED__
#define __IProcessInitControl_FWD_DEFINED__
typedef interface IProcessInitControl IProcessInitControl;
#ifdef __cplusplus
interface IProcessInitControl;
#endif /* __cplusplus */
#endif

#ifndef __IFastRundown_FWD_DEFINED__
#define __IFastRundown_FWD_DEFINED__
typedef interface IFastRundown IFastRundown;
#ifdef __cplusplus
interface IFastRundown;
#endif /* __cplusplus */
#endif

#ifndef __IMarshalingStream_FWD_DEFINED__
#define __IMarshalingStream_FWD_DEFINED__
typedef interface IMarshalingStream IMarshalingStream;
#ifdef __cplusplus
interface IMarshalingStream;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwnbase.h>
#include <wtypesbase.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if (NTDDI_VERSION >= NTDDI_VISTA && !defined(_WIN32_WINNT))
#define _WIN32_WINNT 0x0600
#endif

#if (NTDDI_VERSION >= NTDDI_WS03 && !defined(_WIN32_WINNT))
#define _WIN32_WINNT 0x0502
#endif

#if (NTDDI_VERSION >= NTDDI_WINXP && !defined(_WIN32_WINNT))
#define _WIN32_WINNT 0x0501
#endif


#ifndef _OBJIDLBASE_
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#ifndef __IStream_FWD_DEFINED__
#define __IStream_FWD_DEFINED__
typedef interface IStream IStream;
#ifdef __cplusplus
interface IStream;
#endif /* __cplusplus */
#endif

#ifndef __IEnumString_FWD_DEFINED__
#define __IEnumString_FWD_DEFINED__
typedef interface IEnumString IEnumString;
#ifdef __cplusplus
interface IEnumString;
#endif /* __cplusplus */
#endif

#ifndef __IMultiQI_FWD_DEFINED__
#define __IMultiQI_FWD_DEFINED__
typedef interface IMultiQI IMultiQI;
#ifdef __cplusplus
interface IMultiQI;
#endif /* __cplusplus */
#endif

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IAsyncManager_FWD_DEFINED__
#define __IAsyncManager_FWD_DEFINED__
typedef interface IAsyncManager IAsyncManager;
#ifdef __cplusplus
interface IAsyncManager;
#endif /* __cplusplus */
#endif

#ifndef __ICallFactory_FWD_DEFINED__
#define __ICallFactory_FWD_DEFINED__
typedef interface ICallFactory ICallFactory;
#ifdef __cplusplus
interface ICallFactory;
#endif /* __cplusplus */
#endif

#ifndef __ISynchronize_FWD_DEFINED__
#define __ISynchronize_FWD_DEFINED__
typedef interface ISynchronize ISynchronize;
#ifdef __cplusplus
interface ISynchronize;
#endif /* __cplusplus */
#endif

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef struct _COSERVERINFO {
    DWORD dwReserved1;
    LPWSTR pwszName;
    COAUTHINFO *pAuthInfo;
    DWORD dwReserved2;
} COSERVERINFO;

/*****************************************************************************
 * IMarshal interface
 */
#ifndef __IMarshal_INTERFACE_DEFINED__
#define __IMarshal_INTERFACE_DEFINED__

typedef IMarshal *LPMARSHAL;
DEFINE_GUID(IID_IMarshal, 0x00000003, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000003-0000-0000-c000-000000000046")
IMarshal : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetUnmarshalClass(
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        CLSID *pCid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMarshalSizeMax(
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        DWORD *pSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE MarshalInterface(
        IStream *pStm,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnmarshalInterface(
        IStream *pStm,
        REFIID riid,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseMarshalData(
        IStream *pStm) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisconnectObject(
        DWORD dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMarshal, 0x00000003, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IMarshalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMarshal *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMarshal *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMarshal *This);

    /*** IMarshal methods ***/
    HRESULT (STDMETHODCALLTYPE *GetUnmarshalClass)(
        IMarshal *This,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        CLSID *pCid);

    HRESULT (STDMETHODCALLTYPE *GetMarshalSizeMax)(
        IMarshal *This,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        DWORD *pSize);

    HRESULT (STDMETHODCALLTYPE *MarshalInterface)(
        IMarshal *This,
        IStream *pStm,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags);

    HRESULT (STDMETHODCALLTYPE *UnmarshalInterface)(
        IMarshal *This,
        IStream *pStm,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *ReleaseMarshalData)(
        IMarshal *This,
        IStream *pStm);

    HRESULT (STDMETHODCALLTYPE *DisconnectObject)(
        IMarshal *This,
        DWORD dwReserved);

    END_INTERFACE
} IMarshalVtbl;

interface IMarshal {
    CONST_VTBL IMarshalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMarshal_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMarshal_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMarshal_Release(This) (This)->lpVtbl->Release(This)
/*** IMarshal methods ***/
#define IMarshal_GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid) (This)->lpVtbl->GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid)
#define IMarshal_GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize) (This)->lpVtbl->GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize)
#define IMarshal_MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags) (This)->lpVtbl->MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags)
#define IMarshal_UnmarshalInterface(This,pStm,riid,ppv) (This)->lpVtbl->UnmarshalInterface(This,pStm,riid,ppv)
#define IMarshal_ReleaseMarshalData(This,pStm) (This)->lpVtbl->ReleaseMarshalData(This,pStm)
#define IMarshal_DisconnectObject(This,dwReserved) (This)->lpVtbl->DisconnectObject(This,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IMarshal_QueryInterface(IMarshal* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMarshal_AddRef(IMarshal* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMarshal_Release(IMarshal* This) {
    return This->lpVtbl->Release(This);
}
/*** IMarshal methods ***/
static inline HRESULT IMarshal_GetUnmarshalClass(IMarshal* This,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags,CLSID *pCid) {
    return This->lpVtbl->GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid);
}
static inline HRESULT IMarshal_GetMarshalSizeMax(IMarshal* This,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags,DWORD *pSize) {
    return This->lpVtbl->GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize);
}
static inline HRESULT IMarshal_MarshalInterface(IMarshal* This,IStream *pStm,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags) {
    return This->lpVtbl->MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags);
}
static inline HRESULT IMarshal_UnmarshalInterface(IMarshal* This,IStream *pStm,REFIID riid,void **ppv) {
    return This->lpVtbl->UnmarshalInterface(This,pStm,riid,ppv);
}
static inline HRESULT IMarshal_ReleaseMarshalData(IMarshal* This,IStream *pStm) {
    return This->lpVtbl->ReleaseMarshalData(This,pStm);
}
static inline HRESULT IMarshal_DisconnectObject(IMarshal* This,DWORD dwReserved) {
    return This->lpVtbl->DisconnectObject(This,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IMarshal_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INoMarshal interface
 */
#ifndef __INoMarshal_INTERFACE_DEFINED__
#define __INoMarshal_INTERFACE_DEFINED__

DEFINE_GUID(IID_INoMarshal, 0xecc8691b, 0xc1db, 0x4dc0, 0x85,0x5e, 0x65,0xf6,0xc5,0x51,0xaf,0x49);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ecc8691b-c1db-4dc0-855e-65f6c551af49")
INoMarshal : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INoMarshal, 0xecc8691b, 0xc1db, 0x4dc0, 0x85,0x5e, 0x65,0xf6,0xc5,0x51,0xaf,0x49)
#endif
#else
typedef struct INoMarshalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INoMarshal *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INoMarshal *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INoMarshal *This);

    END_INTERFACE
} INoMarshalVtbl;

interface INoMarshal {
    CONST_VTBL INoMarshalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INoMarshal_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INoMarshal_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INoMarshal_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static inline HRESULT INoMarshal_QueryInterface(INoMarshal* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INoMarshal_AddRef(INoMarshal* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INoMarshal_Release(INoMarshal* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __INoMarshal_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAgileObject interface
 */
#ifndef __IAgileObject_INTERFACE_DEFINED__
#define __IAgileObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAgileObject, 0x94ea2b94, 0xe9cc, 0x49e0, 0xc0,0xff, 0xee,0x64,0xca,0x8f,0x5b,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("94ea2b94-e9cc-49e0-c0ff-ee64ca8f5b90")
IAgileObject : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAgileObject, 0x94ea2b94, 0xe9cc, 0x49e0, 0xc0,0xff, 0xee,0x64,0xca,0x8f,0x5b,0x90)
#endif
#else
typedef struct IAgileObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAgileObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAgileObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAgileObject *This);

    END_INTERFACE
} IAgileObjectVtbl;

interface IAgileObject {
    CONST_VTBL IAgileObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAgileObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAgileObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAgileObject_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IAgileObject_QueryInterface(IAgileObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAgileObject_AddRef(IAgileObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAgileObject_Release(IAgileObject* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __IAgileObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAgileReference interface
 */
#ifndef __IAgileReference_INTERFACE_DEFINED__
#define __IAgileReference_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAgileReference, 0xc03f6a43, 0x65a4, 0x9818, 0x98,0x7e, 0xe0,0xb8,0x10,0xd2,0xa6,0xf2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c03f6a43-65a4-9818-987e-e0b810d2a6f2")
IAgileReference : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Resolve(
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAgileReference, 0xc03f6a43, 0x65a4, 0x9818, 0x98,0x7e, 0xe0,0xb8,0x10,0xd2,0xa6,0xf2)
#endif
#else
typedef struct IAgileReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAgileReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAgileReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAgileReference *This);

    /*** IAgileReference methods ***/
    HRESULT (STDMETHODCALLTYPE *Resolve)(
        IAgileReference *This,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IAgileReferenceVtbl;

interface IAgileReference {
    CONST_VTBL IAgileReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAgileReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAgileReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAgileReference_Release(This) (This)->lpVtbl->Release(This)
/*** IAgileReference methods ***/
#define IAgileReference_Resolve(This,riid,ppv) (This)->lpVtbl->Resolve(This,riid,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT IAgileReference_QueryInterface(IAgileReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAgileReference_AddRef(IAgileReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAgileReference_Release(IAgileReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IAgileReference methods ***/
static inline HRESULT IAgileReference_Resolve(IAgileReference* This,REFIID riid,void **ppv) {
    return This->lpVtbl->Resolve(This,riid,ppv);
}
#endif
#endif

#endif


#endif  /* __IAgileReference_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IMarshal2 interface
 */
#ifndef __IMarshal2_INTERFACE_DEFINED__
#define __IMarshal2_INTERFACE_DEFINED__

typedef IMarshal2 *LPMARSHAL2;
DEFINE_GUID(IID_IMarshal2, 0x000001cf, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000001cf-0000-0000-c000-000000000046")
IMarshal2 : public IMarshal
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMarshal2, 0x000001cf, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IMarshal2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMarshal2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMarshal2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMarshal2 *This);

    /*** IMarshal methods ***/
    HRESULT (STDMETHODCALLTYPE *GetUnmarshalClass)(
        IMarshal2 *This,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        CLSID *pCid);

    HRESULT (STDMETHODCALLTYPE *GetMarshalSizeMax)(
        IMarshal2 *This,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags,
        DWORD *pSize);

    HRESULT (STDMETHODCALLTYPE *MarshalInterface)(
        IMarshal2 *This,
        IStream *pStm,
        REFIID riid,
        void *pv,
        DWORD dwDestContext,
        void *pvDestContext,
        DWORD mshlflags);

    HRESULT (STDMETHODCALLTYPE *UnmarshalInterface)(
        IMarshal2 *This,
        IStream *pStm,
        REFIID riid,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *ReleaseMarshalData)(
        IMarshal2 *This,
        IStream *pStm);

    HRESULT (STDMETHODCALLTYPE *DisconnectObject)(
        IMarshal2 *This,
        DWORD dwReserved);

    END_INTERFACE
} IMarshal2Vtbl;

interface IMarshal2 {
    CONST_VTBL IMarshal2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMarshal2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMarshal2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMarshal2_Release(This) (This)->lpVtbl->Release(This)
/*** IMarshal methods ***/
#define IMarshal2_GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid) (This)->lpVtbl->GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid)
#define IMarshal2_GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize) (This)->lpVtbl->GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize)
#define IMarshal2_MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags) (This)->lpVtbl->MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags)
#define IMarshal2_UnmarshalInterface(This,pStm,riid,ppv) (This)->lpVtbl->UnmarshalInterface(This,pStm,riid,ppv)
#define IMarshal2_ReleaseMarshalData(This,pStm) (This)->lpVtbl->ReleaseMarshalData(This,pStm)
#define IMarshal2_DisconnectObject(This,dwReserved) (This)->lpVtbl->DisconnectObject(This,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IMarshal2_QueryInterface(IMarshal2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMarshal2_AddRef(IMarshal2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMarshal2_Release(IMarshal2* This) {
    return This->lpVtbl->Release(This);
}
/*** IMarshal methods ***/
static inline HRESULT IMarshal2_GetUnmarshalClass(IMarshal2* This,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags,CLSID *pCid) {
    return This->lpVtbl->GetUnmarshalClass(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pCid);
}
static inline HRESULT IMarshal2_GetMarshalSizeMax(IMarshal2* This,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags,DWORD *pSize) {
    return This->lpVtbl->GetMarshalSizeMax(This,riid,pv,dwDestContext,pvDestContext,mshlflags,pSize);
}
static inline HRESULT IMarshal2_MarshalInterface(IMarshal2* This,IStream *pStm,REFIID riid,void *pv,DWORD dwDestContext,void *pvDestContext,DWORD mshlflags) {
    return This->lpVtbl->MarshalInterface(This,pStm,riid,pv,dwDestContext,pvDestContext,mshlflags);
}
static inline HRESULT IMarshal2_UnmarshalInterface(IMarshal2* This,IStream *pStm,REFIID riid,void **ppv) {
    return This->lpVtbl->UnmarshalInterface(This,pStm,riid,ppv);
}
static inline HRESULT IMarshal2_ReleaseMarshalData(IMarshal2* This,IStream *pStm) {
    return This->lpVtbl->ReleaseMarshalData(This,pStm);
}
static inline HRESULT IMarshal2_DisconnectObject(IMarshal2* This,DWORD dwReserved) {
    return This->lpVtbl->DisconnectObject(This,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IMarshal2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IMalloc interface
 */
#ifndef __IMalloc_INTERFACE_DEFINED__
#define __IMalloc_INTERFACE_DEFINED__

typedef IMalloc *LPMALLOC;

DEFINE_GUID(IID_IMalloc, 0x00000002, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000002-0000-0000-c000-000000000046")
IMalloc : public IUnknown
{
    virtual void * STDMETHODCALLTYPE Alloc(
        SIZE_T cb) = 0;

    virtual void * STDMETHODCALLTYPE Realloc(
        void *pv,
        SIZE_T cb) = 0;

    virtual void STDMETHODCALLTYPE Free(
        void *pv) = 0;

    virtual SIZE_T STDMETHODCALLTYPE GetSize(
        void *pv) = 0;

    virtual int STDMETHODCALLTYPE DidAlloc(
        void *pv) = 0;

    virtual void STDMETHODCALLTYPE HeapMinimize(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMalloc, 0x00000002, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IMallocVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMalloc *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMalloc *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMalloc *This);

    /*** IMalloc methods ***/
    void * (STDMETHODCALLTYPE *Alloc)(
        IMalloc *This,
        SIZE_T cb);

    void * (STDMETHODCALLTYPE *Realloc)(
        IMalloc *This,
        void *pv,
        SIZE_T cb);

    void (STDMETHODCALLTYPE *Free)(
        IMalloc *This,
        void *pv);

    SIZE_T (STDMETHODCALLTYPE *GetSize)(
        IMalloc *This,
        void *pv);

    int (STDMETHODCALLTYPE *DidAlloc)(
        IMalloc *This,
        void *pv);

    void (STDMETHODCALLTYPE *HeapMinimize)(
        IMalloc *This);

    END_INTERFACE
} IMallocVtbl;

interface IMalloc {
    CONST_VTBL IMallocVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMalloc_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMalloc_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMalloc_Release(This) (This)->lpVtbl->Release(This)
/*** IMalloc methods ***/
#define IMalloc_Alloc(This,cb) (This)->lpVtbl->Alloc(This,cb)
#define IMalloc_Realloc(This,pv,cb) (This)->lpVtbl->Realloc(This,pv,cb)
#define IMalloc_Free(This,pv) (This)->lpVtbl->Free(This,pv)
#define IMalloc_GetSize(This,pv) (This)->lpVtbl->GetSize(This,pv)
#define IMalloc_DidAlloc(This,pv) (This)->lpVtbl->DidAlloc(This,pv)
#define IMalloc_HeapMinimize(This) (This)->lpVtbl->HeapMinimize(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMalloc_QueryInterface(IMalloc* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMalloc_AddRef(IMalloc* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMalloc_Release(IMalloc* This) {
    return This->lpVtbl->Release(This);
}
/*** IMalloc methods ***/
static inline void * IMalloc_Alloc(IMalloc* This,SIZE_T cb) {
    return This->lpVtbl->Alloc(This,cb);
}
static inline void * IMalloc_Realloc(IMalloc* This,void *pv,SIZE_T cb) {
    return This->lpVtbl->Realloc(This,pv,cb);
}
static inline void IMalloc_Free(IMalloc* This,void *pv) {
    This->lpVtbl->Free(This,pv);
}
static inline SIZE_T IMalloc_GetSize(IMalloc* This,void *pv) {
    return This->lpVtbl->GetSize(This,pv);
}
static inline int IMalloc_DidAlloc(IMalloc* This,void *pv) {
    return This->lpVtbl->DidAlloc(This,pv);
}
static inline void IMalloc_HeapMinimize(IMalloc* This) {
    This->lpVtbl->HeapMinimize(This);
}
#endif
#endif

#endif


#endif  /* __IMalloc_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IStdMarshalInfo interface
 */
#ifndef __IStdMarshalInfo_INTERFACE_DEFINED__
#define __IStdMarshalInfo_INTERFACE_DEFINED__

typedef IStdMarshalInfo *LPSTDMARSHALINFO;

DEFINE_GUID(IID_IStdMarshalInfo, 0x00000018, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000018-0000-0000-c000-000000000046")
IStdMarshalInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetClassForHandler(
        DWORD dwDestContext,
        void *pvDestContext,
        CLSID *pClsid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IStdMarshalInfo, 0x00000018, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IStdMarshalInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IStdMarshalInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IStdMarshalInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IStdMarshalInfo *This);

    /*** IStdMarshalInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassForHandler)(
        IStdMarshalInfo *This,
        DWORD dwDestContext,
        void *pvDestContext,
        CLSID *pClsid);

    END_INTERFACE
} IStdMarshalInfoVtbl;

interface IStdMarshalInfo {
    CONST_VTBL IStdMarshalInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IStdMarshalInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IStdMarshalInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IStdMarshalInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IStdMarshalInfo methods ***/
#define IStdMarshalInfo_GetClassForHandler(This,dwDestContext,pvDestContext,pClsid) (This)->lpVtbl->GetClassForHandler(This,dwDestContext,pvDestContext,pClsid)
#else
/*** IUnknown methods ***/
static inline HRESULT IStdMarshalInfo_QueryInterface(IStdMarshalInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IStdMarshalInfo_AddRef(IStdMarshalInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IStdMarshalInfo_Release(IStdMarshalInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IStdMarshalInfo methods ***/
static inline HRESULT IStdMarshalInfo_GetClassForHandler(IStdMarshalInfo* This,DWORD dwDestContext,void *pvDestContext,CLSID *pClsid) {
    return This->lpVtbl->GetClassForHandler(This,dwDestContext,pvDestContext,pClsid);
}
#endif
#endif

#endif


#endif  /* __IStdMarshalInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IExternalConnection interface
 */
#ifndef __IExternalConnection_INTERFACE_DEFINED__
#define __IExternalConnection_INTERFACE_DEFINED__

typedef IExternalConnection *LPEXTERNALCONNECTION;

typedef enum tagEXTCONN {
    EXTCONN_STRONG = 0x1,
    EXTCONN_WEAK = 0x2,
    EXTCONN_CALLABLE = 0x4
} EXTCONN;

DEFINE_GUID(IID_IExternalConnection, 0x00000019, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000019-0000-0000-c000-000000000046")
IExternalConnection : public IUnknown
{
    virtual DWORD STDMETHODCALLTYPE AddConnection(
        DWORD extconn,
        DWORD reserved) = 0;

    virtual DWORD STDMETHODCALLTYPE ReleaseConnection(
        DWORD extconn,
        DWORD reserved,
        WINBOOL fLastReleaseCloses) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IExternalConnection, 0x00000019, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IExternalConnectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IExternalConnection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IExternalConnection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IExternalConnection *This);

    /*** IExternalConnection methods ***/
    DWORD (STDMETHODCALLTYPE *AddConnection)(
        IExternalConnection *This,
        DWORD extconn,
        DWORD reserved);

    DWORD (STDMETHODCALLTYPE *ReleaseConnection)(
        IExternalConnection *This,
        DWORD extconn,
        DWORD reserved,
        WINBOOL fLastReleaseCloses);

    END_INTERFACE
} IExternalConnectionVtbl;

interface IExternalConnection {
    CONST_VTBL IExternalConnectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IExternalConnection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IExternalConnection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IExternalConnection_Release(This) (This)->lpVtbl->Release(This)
/*** IExternalConnection methods ***/
#define IExternalConnection_AddConnection(This,extconn,reserved) (This)->lpVtbl->AddConnection(This,extconn,reserved)
#define IExternalConnection_ReleaseConnection(This,extconn,reserved,fLastReleaseCloses) (This)->lpVtbl->ReleaseConnection(This,extconn,reserved,fLastReleaseCloses)
#else
/*** IUnknown methods ***/
static inline HRESULT IExternalConnection_QueryInterface(IExternalConnection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IExternalConnection_AddRef(IExternalConnection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IExternalConnection_Release(IExternalConnection* This) {
    return This->lpVtbl->Release(This);
}
/*** IExternalConnection methods ***/
static inline DWORD IExternalConnection_AddConnection(IExternalConnection* This,DWORD extconn,DWORD reserved) {
    return This->lpVtbl->AddConnection(This,extconn,reserved);
}
static inline DWORD IExternalConnection_ReleaseConnection(IExternalConnection* This,DWORD extconn,DWORD reserved,WINBOOL fLastReleaseCloses) {
    return This->lpVtbl->ReleaseConnection(This,extconn,reserved,fLastReleaseCloses);
}
#endif
#endif

#endif


#endif  /* __IExternalConnection_INTERFACE_DEFINED__ */


typedef IMultiQI *LPMULTIQI;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef struct tagMULTI_QI {
    const IID *pIID;
    IUnknown *pItf;
    HRESULT hr;
} MULTI_QI;

/*****************************************************************************
 * IMultiQI interface
 */
#ifndef __IMultiQI_INTERFACE_DEFINED__
#define __IMultiQI_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMultiQI, 0x00000020, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000020-0000-0000-c000-000000000046")
IMultiQI : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryMultipleInterfaces(
        ULONG cMQIs,
        MULTI_QI *pMQIs) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMultiQI, 0x00000020, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IMultiQIVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMultiQI *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMultiQI *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMultiQI *This);

    /*** IMultiQI methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryMultipleInterfaces)(
        IMultiQI *This,
        ULONG cMQIs,
        MULTI_QI *pMQIs);

    END_INTERFACE
} IMultiQIVtbl;

interface IMultiQI {
    CONST_VTBL IMultiQIVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMultiQI_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMultiQI_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMultiQI_Release(This) (This)->lpVtbl->Release(This)
/*** IMultiQI methods ***/
#define IMultiQI_QueryMultipleInterfaces(This,cMQIs,pMQIs) (This)->lpVtbl->QueryMultipleInterfaces(This,cMQIs,pMQIs)
#else
/*** IUnknown methods ***/
static inline HRESULT IMultiQI_QueryInterface(IMultiQI* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMultiQI_AddRef(IMultiQI* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMultiQI_Release(IMultiQI* This) {
    return This->lpVtbl->Release(This);
}
/*** IMultiQI methods ***/
static inline HRESULT IMultiQI_QueryMultipleInterfaces(IMultiQI* This,ULONG cMQIs,MULTI_QI *pMQIs) {
    return This->lpVtbl->QueryMultipleInterfaces(This,cMQIs,pMQIs);
}
#endif
#endif

#endif


#endif  /* __IMultiQI_INTERFACE_DEFINED__ */

/*****************************************************************************
 * AsyncIMultiQI interface
 */
#ifndef __AsyncIMultiQI_INTERFACE_DEFINED__
#define __AsyncIMultiQI_INTERFACE_DEFINED__

DEFINE_GUID(IID_AsyncIMultiQI, 0x000e0020, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000e0020-0000-0000-c000-000000000046")
AsyncIMultiQI : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Begin_QueryMultipleInterfaces(
        ULONG cMQIs,
        MULTI_QI *pMQIs) = 0;

    virtual HRESULT STDMETHODCALLTYPE Finish_QueryMultipleInterfaces(
        MULTI_QI *pMQIs) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(AsyncIMultiQI, 0x000e0020, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct AsyncIMultiQIVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        AsyncIMultiQI *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        AsyncIMultiQI *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        AsyncIMultiQI *This);

    /*** AsyncIMultiQI methods ***/
    HRESULT (STDMETHODCALLTYPE *Begin_QueryMultipleInterfaces)(
        AsyncIMultiQI *This,
        ULONG cMQIs,
        MULTI_QI *pMQIs);

    HRESULT (STDMETHODCALLTYPE *Finish_QueryMultipleInterfaces)(
        AsyncIMultiQI *This,
        MULTI_QI *pMQIs);

    END_INTERFACE
} AsyncIMultiQIVtbl;

interface AsyncIMultiQI {
    CONST_VTBL AsyncIMultiQIVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define AsyncIMultiQI_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define AsyncIMultiQI_AddRef(This) (This)->lpVtbl->AddRef(This)
#define AsyncIMultiQI_Release(This) (This)->lpVtbl->Release(This)
/*** AsyncIMultiQI methods ***/
#define AsyncIMultiQI_Begin_QueryMultipleInterfaces(This,cMQIs,pMQIs) (This)->lpVtbl->Begin_QueryMultipleInterfaces(This,cMQIs,pMQIs)
#define AsyncIMultiQI_Finish_QueryMultipleInterfaces(This,pMQIs) (This)->lpVtbl->Finish_QueryMultipleInterfaces(This,pMQIs)
#else
/*** IUnknown methods ***/
static inline HRESULT AsyncIMultiQI_QueryInterface(AsyncIMultiQI* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG AsyncIMultiQI_AddRef(AsyncIMultiQI* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG AsyncIMultiQI_Release(AsyncIMultiQI* This) {
    return This->lpVtbl->Release(This);
}
/*** AsyncIMultiQI methods ***/
static inline HRESULT AsyncIMultiQI_Begin_QueryMultipleInterfaces(AsyncIMultiQI* This,ULONG cMQIs,MULTI_QI *pMQIs) {
    return This->lpVtbl->Begin_QueryMultipleInterfaces(This,cMQIs,pMQIs);
}
static inline HRESULT AsyncIMultiQI_Finish_QueryMultipleInterfaces(AsyncIMultiQI* This,MULTI_QI *pMQIs) {
    return This->lpVtbl->Finish_QueryMultipleInterfaces(This,pMQIs);
}
#endif
#endif

#endif


#endif  /* __AsyncIMultiQI_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IInternalUnknown interface
 */
#ifndef __IInternalUnknown_INTERFACE_DEFINED__
#define __IInternalUnknown_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInternalUnknown, 0x00000021, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000021-0000-0000-c000-000000000046")
IInternalUnknown : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryInternalInterface(
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInternalUnknown, 0x00000021, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IInternalUnknownVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInternalUnknown *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInternalUnknown *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInternalUnknown *This);

    /*** IInternalUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInternalInterface)(
        IInternalUnknown *This,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IInternalUnknownVtbl;

interface IInternalUnknown {
    CONST_VTBL IInternalUnknownVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInternalUnknown_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInternalUnknown_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInternalUnknown_Release(This) (This)->lpVtbl->Release(This)
/*** IInternalUnknown methods ***/
#define IInternalUnknown_QueryInternalInterface(This,riid,ppv) (This)->lpVtbl->QueryInternalInterface(This,riid,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT IInternalUnknown_QueryInterface(IInternalUnknown* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInternalUnknown_AddRef(IInternalUnknown* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInternalUnknown_Release(IInternalUnknown* This) {
    return This->lpVtbl->Release(This);
}
/*** IInternalUnknown methods ***/
static inline HRESULT IInternalUnknown_QueryInternalInterface(IInternalUnknown* This,REFIID riid,void **ppv) {
    return This->lpVtbl->QueryInternalInterface(This,riid,ppv);
}
#endif
#endif

#endif


#endif  /* __IInternalUnknown_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IEnumUnknown interface
 */
#ifndef __IEnumUnknown_INTERFACE_DEFINED__
#define __IEnumUnknown_INTERFACE_DEFINED__

typedef IEnumUnknown *LPENUMUNKNOWN;

DEFINE_GUID(IID_IEnumUnknown, 0x00000100, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000100-0000-0000-c000-000000000046")
IEnumUnknown : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        IUnknown **rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumUnknown **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumUnknown, 0x00000100, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumUnknownVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumUnknown *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumUnknown *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumUnknown *This);

    /*** IEnumUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumUnknown *This,
        ULONG celt,
        IUnknown **rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumUnknown *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumUnknown *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumUnknown *This,
        IEnumUnknown **ppenum);

    END_INTERFACE
} IEnumUnknownVtbl;

interface IEnumUnknown {
    CONST_VTBL IEnumUnknownVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumUnknown_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumUnknown_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumUnknown_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumUnknown methods ***/
#define IEnumUnknown_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumUnknown_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumUnknown_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumUnknown_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumUnknown_QueryInterface(IEnumUnknown* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumUnknown_AddRef(IEnumUnknown* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumUnknown_Release(IEnumUnknown* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumUnknown methods ***/
static inline HRESULT IEnumUnknown_Next(IEnumUnknown* This,ULONG celt,IUnknown **rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumUnknown_Skip(IEnumUnknown* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumUnknown_Reset(IEnumUnknown* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumUnknown_Clone(IEnumUnknown* This,IEnumUnknown **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumUnknown_RemoteNext_Proxy(
    IEnumUnknown* This,
    ULONG celt,
    IUnknown **rgelt,
    ULONG *pceltFetched);
void __RPC_STUB IEnumUnknown_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumUnknown_Next_Proxy(
    IEnumUnknown* This,
    ULONG celt,
    IUnknown **rgelt,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumUnknown_Next_Stub(
    IEnumUnknown* This,
    ULONG celt,
    IUnknown **rgelt,
    ULONG *pceltFetched);

#endif  /* __IEnumUnknown_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEnumString interface
 */
#ifndef __IEnumString_INTERFACE_DEFINED__
#define __IEnumString_INTERFACE_DEFINED__

typedef IEnumString *LPENUMSTRING;

DEFINE_GUID(IID_IEnumString, 0x00000101, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000101-0000-0000-c000-000000000046")
IEnumString : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        LPOLESTR *rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumString **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumString, 0x00000101, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumStringVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumString *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumString *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumString *This);

    /*** IEnumString methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumString *This,
        ULONG celt,
        LPOLESTR *rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumString *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumString *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumString *This,
        IEnumString **ppenum);

    END_INTERFACE
} IEnumStringVtbl;

interface IEnumString {
    CONST_VTBL IEnumStringVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumString_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumString_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumString_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumString methods ***/
#define IEnumString_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumString_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumString_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumString_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumString_QueryInterface(IEnumString* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumString_AddRef(IEnumString* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumString_Release(IEnumString* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumString methods ***/
static inline HRESULT IEnumString_Next(IEnumString* This,ULONG celt,LPOLESTR *rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumString_Skip(IEnumString* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumString_Reset(IEnumString* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumString_Clone(IEnumString* This,IEnumString **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IEnumString_RemoteNext_Proxy(
    IEnumString* This,
    ULONG celt,
    LPOLESTR *rgelt,
    ULONG *pceltFetched);
void __RPC_STUB IEnumString_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumString_Next_Proxy(
    IEnumString* This,
    ULONG celt,
    LPOLESTR *rgelt,
    ULONG *pceltFetched);
HRESULT __RPC_STUB IEnumString_Next_Stub(
    IEnumString* This,
    ULONG celt,
    LPOLESTR *rgelt,
    ULONG *pceltFetched);

#endif  /* __IEnumString_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISequentialStream interface
 */
#ifndef __ISequentialStream_INTERFACE_DEFINED__
#define __ISequentialStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISequentialStream, 0x0c733a30, 0x2a1c, 0x11ce, 0xad,0xe5, 0x00,0xaa,0x00,0x44,0x77,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0c733a30-2a1c-11ce-ade5-00aa0044773d")
ISequentialStream : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Read(
        void *pv,
        ULONG cb,
        ULONG *pcbRead) = 0;

    virtual HRESULT STDMETHODCALLTYPE Write(
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISequentialStream, 0x0c733a30, 0x2a1c, 0x11ce, 0xad,0xe5, 0x00,0xaa,0x00,0x44,0x77,0x3d)
#endif
#else
typedef struct ISequentialStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISequentialStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISequentialStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISequentialStream *This);

    /*** ISequentialStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        ISequentialStream *This,
        void *pv,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Write)(
        ISequentialStream *This,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten);

    END_INTERFACE
} ISequentialStreamVtbl;

interface ISequentialStream {
    CONST_VTBL ISequentialStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISequentialStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISequentialStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISequentialStream_Release(This) (This)->lpVtbl->Release(This)
/*** ISequentialStream methods ***/
#define ISequentialStream_Read(This,pv,cb,pcbRead) (This)->lpVtbl->Read(This,pv,cb,pcbRead)
#define ISequentialStream_Write(This,pv,cb,pcbWritten) (This)->lpVtbl->Write(This,pv,cb,pcbWritten)
#else
/*** IUnknown methods ***/
static inline HRESULT ISequentialStream_QueryInterface(ISequentialStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISequentialStream_AddRef(ISequentialStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISequentialStream_Release(ISequentialStream* This) {
    return This->lpVtbl->Release(This);
}
/*** ISequentialStream methods ***/
static inline HRESULT ISequentialStream_Read(ISequentialStream* This,void *pv,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->Read(This,pv,cb,pcbRead);
}
static inline HRESULT ISequentialStream_Write(ISequentialStream* This,const void *pv,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->Write(This,pv,cb,pcbWritten);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE ISequentialStream_RemoteRead_Proxy(
    ISequentialStream* This,
    byte *pv,
    ULONG cb,
    ULONG *pcbRead);
void __RPC_STUB ISequentialStream_RemoteRead_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE ISequentialStream_RemoteWrite_Proxy(
    ISequentialStream* This,
    const byte *pv,
    ULONG cb,
    ULONG *pcbWritten);
void __RPC_STUB ISequentialStream_RemoteWrite_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK ISequentialStream_Read_Proxy(
    ISequentialStream* This,
    void *pv,
    ULONG cb,
    ULONG *pcbRead);
HRESULT __RPC_STUB ISequentialStream_Read_Stub(
    ISequentialStream* This,
    byte *pv,
    ULONG cb,
    ULONG *pcbRead);
HRESULT CALLBACK ISequentialStream_Write_Proxy(
    ISequentialStream* This,
    const void *pv,
    ULONG cb,
    ULONG *pcbWritten);
HRESULT __RPC_STUB ISequentialStream_Write_Stub(
    ISequentialStream* This,
    const byte *pv,
    ULONG cb,
    ULONG *pcbWritten);

#endif  /* __ISequentialStream_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IStream interface
 */
#ifndef __IStream_INTERFACE_DEFINED__
#define __IStream_INTERFACE_DEFINED__

typedef IStream *LPSTREAM;

typedef struct tagSTATSTG {
    LPOLESTR pwcsName;
    DWORD type;
    ULARGE_INTEGER cbSize;
    FILETIME mtime;
    FILETIME ctime;
    FILETIME atime;
    DWORD grfMode;
    DWORD grfLocksSupported;
    CLSID clsid;
    DWORD grfStateBits;
    DWORD reserved;
} STATSTG;

typedef enum tagSTGTY {
    STGTY_STORAGE = 1,
    STGTY_STREAM = 2,
    STGTY_LOCKBYTES = 3,
    STGTY_PROPERTY = 4
} STGTY;

typedef enum tagSTREAM_SEEK {
    STREAM_SEEK_SET = 0,
    STREAM_SEEK_CUR = 1,
    STREAM_SEEK_END = 2
} STREAM_SEEK;

typedef enum tagLOCKTYPE {
    LOCK_WRITE = 1,
    LOCK_EXCLUSIVE = 2,
    LOCK_ONLYONCE = 4
} LOCKTYPE;

DEFINE_GUID(IID_IStream, 0x0000000c, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000000c-0000-0000-c000-000000000046")
IStream : public ISequentialStream
{
    virtual HRESULT STDMETHODCALLTYPE Seek(
        LARGE_INTEGER dlibMove,
        DWORD dwOrigin,
        ULARGE_INTEGER *plibNewPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSize(
        ULARGE_INTEGER libNewSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyTo(
        IStream *pstm,
        ULARGE_INTEGER cb,
        ULARGE_INTEGER *pcbRead,
        ULARGE_INTEGER *pcbWritten) = 0;

    virtual HRESULT STDMETHODCALLTYPE Commit(
        DWORD grfCommitFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Revert(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE LockRegion(
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnlockRegion(
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stat(
        STATSTG *pstatstg,
        DWORD grfStatFlag) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IStream **ppstm) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IStream, 0x0000000c, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IStream *This);

    /*** ISequentialStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        IStream *This,
        void *pv,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IStream *This,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten);

    /*** IStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Seek)(
        IStream *This,
        LARGE_INTEGER dlibMove,
        DWORD dwOrigin,
        ULARGE_INTEGER *plibNewPosition);

    HRESULT (STDMETHODCALLTYPE *SetSize)(
        IStream *This,
        ULARGE_INTEGER libNewSize);

    HRESULT (STDMETHODCALLTYPE *CopyTo)(
        IStream *This,
        IStream *pstm,
        ULARGE_INTEGER cb,
        ULARGE_INTEGER *pcbRead,
        ULARGE_INTEGER *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IStream *This,
        DWORD grfCommitFlags);

    HRESULT (STDMETHODCALLTYPE *Revert)(
        IStream *This);

    HRESULT (STDMETHODCALLTYPE *LockRegion)(
        IStream *This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *UnlockRegion)(
        IStream *This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *Stat)(
        IStream *This,
        STATSTG *pstatstg,
        DWORD grfStatFlag);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IStream *This,
        IStream **ppstm);

    END_INTERFACE
} IStreamVtbl;

interface IStream {
    CONST_VTBL IStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IStream_Release(This) (This)->lpVtbl->Release(This)
/*** ISequentialStream methods ***/
#define IStream_Read(This,pv,cb,pcbRead) (This)->lpVtbl->Read(This,pv,cb,pcbRead)
#define IStream_Write(This,pv,cb,pcbWritten) (This)->lpVtbl->Write(This,pv,cb,pcbWritten)
/*** IStream methods ***/
#define IStream_Seek(This,dlibMove,dwOrigin,plibNewPosition) (This)->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition)
#define IStream_SetSize(This,libNewSize) (This)->lpVtbl->SetSize(This,libNewSize)
#define IStream_CopyTo(This,pstm,cb,pcbRead,pcbWritten) (This)->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten)
#define IStream_Commit(This,grfCommitFlags) (This)->lpVtbl->Commit(This,grfCommitFlags)
#define IStream_Revert(This) (This)->lpVtbl->Revert(This)
#define IStream_LockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->LockRegion(This,libOffset,cb,dwLockType)
#define IStream_UnlockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType)
#define IStream_Stat(This,pstatstg,grfStatFlag) (This)->lpVtbl->Stat(This,pstatstg,grfStatFlag)
#define IStream_Clone(This,ppstm) (This)->lpVtbl->Clone(This,ppstm)
#else
/*** IUnknown methods ***/
static inline HRESULT IStream_QueryInterface(IStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IStream_AddRef(IStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IStream_Release(IStream* This) {
    return This->lpVtbl->Release(This);
}
/*** ISequentialStream methods ***/
static inline HRESULT IStream_Read(IStream* This,void *pv,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->Read(This,pv,cb,pcbRead);
}
static inline HRESULT IStream_Write(IStream* This,const void *pv,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->Write(This,pv,cb,pcbWritten);
}
/*** IStream methods ***/
static inline HRESULT IStream_Seek(IStream* This,LARGE_INTEGER dlibMove,DWORD dwOrigin,ULARGE_INTEGER *plibNewPosition) {
    return This->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition);
}
static inline HRESULT IStream_SetSize(IStream* This,ULARGE_INTEGER libNewSize) {
    return This->lpVtbl->SetSize(This,libNewSize);
}
static inline HRESULT IStream_CopyTo(IStream* This,IStream *pstm,ULARGE_INTEGER cb,ULARGE_INTEGER *pcbRead,ULARGE_INTEGER *pcbWritten) {
    return This->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten);
}
static inline HRESULT IStream_Commit(IStream* This,DWORD grfCommitFlags) {
    return This->lpVtbl->Commit(This,grfCommitFlags);
}
static inline HRESULT IStream_Revert(IStream* This) {
    return This->lpVtbl->Revert(This);
}
static inline HRESULT IStream_LockRegion(IStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->LockRegion(This,libOffset,cb,dwLockType);
}
static inline HRESULT IStream_UnlockRegion(IStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType);
}
static inline HRESULT IStream_Stat(IStream* This,STATSTG *pstatstg,DWORD grfStatFlag) {
    return This->lpVtbl->Stat(This,pstatstg,grfStatFlag);
}
static inline HRESULT IStream_Clone(IStream* This,IStream **ppstm) {
    return This->lpVtbl->Clone(This,ppstm);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IStream_RemoteSeek_Proxy(
    IStream* This,
    LARGE_INTEGER dlibMove,
    DWORD dwOrigin,
    ULARGE_INTEGER *plibNewPosition);
void __RPC_STUB IStream_RemoteSeek_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT STDMETHODCALLTYPE IStream_RemoteCopyTo_Proxy(
    IStream* This,
    IStream *pstm,
    ULARGE_INTEGER cb,
    ULARGE_INTEGER *pcbRead,
    ULARGE_INTEGER *pcbWritten);
void __RPC_STUB IStream_RemoteCopyTo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IStream_Seek_Proxy(
    IStream* This,
    LARGE_INTEGER dlibMove,
    DWORD dwOrigin,
    ULARGE_INTEGER *plibNewPosition);
HRESULT __RPC_STUB IStream_Seek_Stub(
    IStream* This,
    LARGE_INTEGER dlibMove,
    DWORD dwOrigin,
    ULARGE_INTEGER *plibNewPosition);
HRESULT CALLBACK IStream_CopyTo_Proxy(
    IStream* This,
    IStream *pstm,
    ULARGE_INTEGER cb,
    ULARGE_INTEGER *pcbRead,
    ULARGE_INTEGER *pcbWritten);
HRESULT __RPC_STUB IStream_CopyTo_Stub(
    IStream* This,
    IStream *pstm,
    ULARGE_INTEGER cb,
    ULARGE_INTEGER *pcbRead,
    ULARGE_INTEGER *pcbWritten);

#endif  /* __IStream_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IRpcChannelBuffer interface
 */
#ifndef __IRpcChannelBuffer_INTERFACE_DEFINED__
#define __IRpcChannelBuffer_INTERFACE_DEFINED__

typedef ULONG RPCOLEDATAREP;

typedef struct tagRPCOLEMESSAGE {
    void *reserved1;
    RPCOLEDATAREP dataRepresentation;
    void *Buffer;
    ULONG cbBuffer;
    ULONG iMethod;
    void * reserved2[5];
    ULONG rpcFlags;
} RPCOLEMESSAGE;

typedef RPCOLEMESSAGE *PRPCOLEMESSAGE;

DEFINE_GUID(IID_IRpcChannelBuffer, 0xd5f56b60, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d5f56b60-593b-101a-b569-08002b2dbf7a")
IRpcChannelBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetBuffer(
        RPCOLEMESSAGE *pMessage,
        REFIID riid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendReceive(
        RPCOLEMESSAGE *pMessage,
        ULONG *pStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeBuffer(
        RPCOLEMESSAGE *pMessage) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDestCtx(
        DWORD *pdwDestContext,
        void **ppvDestContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsConnected(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcChannelBuffer, 0xd5f56b60, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a)
#endif
#else
typedef struct IRpcChannelBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcChannelBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcChannelBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcChannelBuffer *This);

    /*** IRpcChannelBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IRpcChannelBuffer *This,
        RPCOLEMESSAGE *pMessage,
        REFIID riid);

    HRESULT (STDMETHODCALLTYPE *SendReceive)(
        IRpcChannelBuffer *This,
        RPCOLEMESSAGE *pMessage,
        ULONG *pStatus);

    HRESULT (STDMETHODCALLTYPE *FreeBuffer)(
        IRpcChannelBuffer *This,
        RPCOLEMESSAGE *pMessage);

    HRESULT (STDMETHODCALLTYPE *GetDestCtx)(
        IRpcChannelBuffer *This,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    HRESULT (STDMETHODCALLTYPE *IsConnected)(
        IRpcChannelBuffer *This);

    END_INTERFACE
} IRpcChannelBufferVtbl;

interface IRpcChannelBuffer {
    CONST_VTBL IRpcChannelBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcChannelBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcChannelBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcChannelBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcChannelBuffer methods ***/
#define IRpcChannelBuffer_GetBuffer(This,pMessage,riid) (This)->lpVtbl->GetBuffer(This,pMessage,riid)
#define IRpcChannelBuffer_SendReceive(This,pMessage,pStatus) (This)->lpVtbl->SendReceive(This,pMessage,pStatus)
#define IRpcChannelBuffer_FreeBuffer(This,pMessage) (This)->lpVtbl->FreeBuffer(This,pMessage)
#define IRpcChannelBuffer_GetDestCtx(This,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext)
#define IRpcChannelBuffer_IsConnected(This) (This)->lpVtbl->IsConnected(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IRpcChannelBuffer_QueryInterface(IRpcChannelBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRpcChannelBuffer_AddRef(IRpcChannelBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRpcChannelBuffer_Release(IRpcChannelBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcChannelBuffer methods ***/
static inline HRESULT IRpcChannelBuffer_GetBuffer(IRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage,REFIID riid) {
    return This->lpVtbl->GetBuffer(This,pMessage,riid);
}
static inline HRESULT IRpcChannelBuffer_SendReceive(IRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage,ULONG *pStatus) {
    return This->lpVtbl->SendReceive(This,pMessage,pStatus);
}
static inline HRESULT IRpcChannelBuffer_FreeBuffer(IRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage) {
    return This->lpVtbl->FreeBuffer(This,pMessage);
}
static inline HRESULT IRpcChannelBuffer_GetDestCtx(IRpcChannelBuffer* This,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext);
}
static inline HRESULT IRpcChannelBuffer_IsConnected(IRpcChannelBuffer* This) {
    return This->lpVtbl->IsConnected(This);
}
#endif
#endif

#endif


#endif  /* __IRpcChannelBuffer_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IRpcChannelBuffer2 interface
 */
#ifndef __IRpcChannelBuffer2_INTERFACE_DEFINED__
#define __IRpcChannelBuffer2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcChannelBuffer2, 0x594f31d0, 0x7f19, 0x11d0, 0xb1,0x94, 0x00,0xa0,0xc9,0x0d,0xc8,0xbf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("594f31d0-7f19-11d0-b194-00a0c90dc8bf")
IRpcChannelBuffer2 : public IRpcChannelBuffer
{
    virtual HRESULT STDMETHODCALLTYPE GetProtocolVersion(
        DWORD *pdwVersion) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcChannelBuffer2, 0x594f31d0, 0x7f19, 0x11d0, 0xb1,0x94, 0x00,0xa0,0xc9,0x0d,0xc8,0xbf)
#endif
#else
typedef struct IRpcChannelBuffer2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcChannelBuffer2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcChannelBuffer2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcChannelBuffer2 *This);

    /*** IRpcChannelBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IRpcChannelBuffer2 *This,
        RPCOLEMESSAGE *pMessage,
        REFIID riid);

    HRESULT (STDMETHODCALLTYPE *SendReceive)(
        IRpcChannelBuffer2 *This,
        RPCOLEMESSAGE *pMessage,
        ULONG *pStatus);

    HRESULT (STDMETHODCALLTYPE *FreeBuffer)(
        IRpcChannelBuffer2 *This,
        RPCOLEMESSAGE *pMessage);

    HRESULT (STDMETHODCALLTYPE *GetDestCtx)(
        IRpcChannelBuffer2 *This,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    HRESULT (STDMETHODCALLTYPE *IsConnected)(
        IRpcChannelBuffer2 *This);

    /*** IRpcChannelBuffer2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProtocolVersion)(
        IRpcChannelBuffer2 *This,
        DWORD *pdwVersion);

    END_INTERFACE
} IRpcChannelBuffer2Vtbl;

interface IRpcChannelBuffer2 {
    CONST_VTBL IRpcChannelBuffer2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcChannelBuffer2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcChannelBuffer2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcChannelBuffer2_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcChannelBuffer methods ***/
#define IRpcChannelBuffer2_GetBuffer(This,pMessage,riid) (This)->lpVtbl->GetBuffer(This,pMessage,riid)
#define IRpcChannelBuffer2_SendReceive(This,pMessage,pStatus) (This)->lpVtbl->SendReceive(This,pMessage,pStatus)
#define IRpcChannelBuffer2_FreeBuffer(This,pMessage) (This)->lpVtbl->FreeBuffer(This,pMessage)
#define IRpcChannelBuffer2_GetDestCtx(This,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext)
#define IRpcChannelBuffer2_IsConnected(This) (This)->lpVtbl->IsConnected(This)
/*** IRpcChannelBuffer2 methods ***/
#define IRpcChannelBuffer2_GetProtocolVersion(This,pdwVersion) (This)->lpVtbl->GetProtocolVersion(This,pdwVersion)
#else
/*** IUnknown methods ***/
static inline HRESULT IRpcChannelBuffer2_QueryInterface(IRpcChannelBuffer2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRpcChannelBuffer2_AddRef(IRpcChannelBuffer2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRpcChannelBuffer2_Release(IRpcChannelBuffer2* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcChannelBuffer methods ***/
static inline HRESULT IRpcChannelBuffer2_GetBuffer(IRpcChannelBuffer2* This,RPCOLEMESSAGE *pMessage,REFIID riid) {
    return This->lpVtbl->GetBuffer(This,pMessage,riid);
}
static inline HRESULT IRpcChannelBuffer2_SendReceive(IRpcChannelBuffer2* This,RPCOLEMESSAGE *pMessage,ULONG *pStatus) {
    return This->lpVtbl->SendReceive(This,pMessage,pStatus);
}
static inline HRESULT IRpcChannelBuffer2_FreeBuffer(IRpcChannelBuffer2* This,RPCOLEMESSAGE *pMessage) {
    return This->lpVtbl->FreeBuffer(This,pMessage);
}
static inline HRESULT IRpcChannelBuffer2_GetDestCtx(IRpcChannelBuffer2* This,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext);
}
static inline HRESULT IRpcChannelBuffer2_IsConnected(IRpcChannelBuffer2* This) {
    return This->lpVtbl->IsConnected(This);
}
/*** IRpcChannelBuffer2 methods ***/
static inline HRESULT IRpcChannelBuffer2_GetProtocolVersion(IRpcChannelBuffer2* This,DWORD *pdwVersion) {
    return This->lpVtbl->GetProtocolVersion(This,pdwVersion);
}
#endif
#endif

#endif


#endif  /* __IRpcChannelBuffer2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAsyncRpcChannelBuffer interface
 */
#ifndef __IAsyncRpcChannelBuffer_INTERFACE_DEFINED__
#define __IAsyncRpcChannelBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAsyncRpcChannelBuffer, 0xa5029fb6, 0x3c34, 0x11d1, 0x9c,0x99, 0x00,0xc0,0x4f,0xb9,0x98,0xaa);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a5029fb6-3c34-11d1-9c99-00c04fb998aa")
IAsyncRpcChannelBuffer : public IRpcChannelBuffer2
{
    virtual HRESULT STDMETHODCALLTYPE Send(
        RPCOLEMESSAGE *pMsg,
        ISynchronize *pSync,
        ULONG *pulStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE Receive(
        RPCOLEMESSAGE *pMsg,
        ULONG *pulStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDestCtxEx(
        RPCOLEMESSAGE *pMsg,
        DWORD *pdwDestContext,
        void **ppvDestContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAsyncRpcChannelBuffer, 0xa5029fb6, 0x3c34, 0x11d1, 0x9c,0x99, 0x00,0xc0,0x4f,0xb9,0x98,0xaa)
#endif
#else
typedef struct IAsyncRpcChannelBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAsyncRpcChannelBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAsyncRpcChannelBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAsyncRpcChannelBuffer *This);

    /*** IRpcChannelBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IAsyncRpcChannelBuffer *This,
        RPCOLEMESSAGE *pMessage,
        REFIID riid);

    HRESULT (STDMETHODCALLTYPE *SendReceive)(
        IAsyncRpcChannelBuffer *This,
        RPCOLEMESSAGE *pMessage,
        ULONG *pStatus);

    HRESULT (STDMETHODCALLTYPE *FreeBuffer)(
        IAsyncRpcChannelBuffer *This,
        RPCOLEMESSAGE *pMessage);

    HRESULT (STDMETHODCALLTYPE *GetDestCtx)(
        IAsyncRpcChannelBuffer *This,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    HRESULT (STDMETHODCALLTYPE *IsConnected)(
        IAsyncRpcChannelBuffer *This);

    /*** IRpcChannelBuffer2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProtocolVersion)(
        IAsyncRpcChannelBuffer *This,
        DWORD *pdwVersion);

    /*** IAsyncRpcChannelBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *Send)(
        IAsyncRpcChannelBuffer *This,
        RPCOLEMESSAGE *pMsg,
        ISynchronize *pSync,
        ULONG *pulStatus);

    HRESULT (STDMETHODCALLTYPE *Receive)(
        IAsyncRpcChannelBuffer *This,
        RPCOLEMESSAGE *pMsg,
        ULONG *pulStatus);

    HRESULT (STDMETHODCALLTYPE *GetDestCtxEx)(
        IAsyncRpcChannelBuffer *This,
        RPCOLEMESSAGE *pMsg,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    END_INTERFACE
} IAsyncRpcChannelBufferVtbl;

interface IAsyncRpcChannelBuffer {
    CONST_VTBL IAsyncRpcChannelBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAsyncRpcChannelBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAsyncRpcChannelBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAsyncRpcChannelBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcChannelBuffer methods ***/
#define IAsyncRpcChannelBuffer_GetBuffer(This,pMessage,riid) (This)->lpVtbl->GetBuffer(This,pMessage,riid)
#define IAsyncRpcChannelBuffer_SendReceive(This,pMessage,pStatus) (This)->lpVtbl->SendReceive(This,pMessage,pStatus)
#define IAsyncRpcChannelBuffer_FreeBuffer(This,pMessage) (This)->lpVtbl->FreeBuffer(This,pMessage)
#define IAsyncRpcChannelBuffer_GetDestCtx(This,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext)
#define IAsyncRpcChannelBuffer_IsConnected(This) (This)->lpVtbl->IsConnected(This)
/*** IRpcChannelBuffer2 methods ***/
#define IAsyncRpcChannelBuffer_GetProtocolVersion(This,pdwVersion) (This)->lpVtbl->GetProtocolVersion(This,pdwVersion)
/*** IAsyncRpcChannelBuffer methods ***/
#define IAsyncRpcChannelBuffer_Send(This,pMsg,pSync,pulStatus) (This)->lpVtbl->Send(This,pMsg,pSync,pulStatus)
#define IAsyncRpcChannelBuffer_Receive(This,pMsg,pulStatus) (This)->lpVtbl->Receive(This,pMsg,pulStatus)
#define IAsyncRpcChannelBuffer_GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IAsyncRpcChannelBuffer_QueryInterface(IAsyncRpcChannelBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAsyncRpcChannelBuffer_AddRef(IAsyncRpcChannelBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAsyncRpcChannelBuffer_Release(IAsyncRpcChannelBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcChannelBuffer methods ***/
static inline HRESULT IAsyncRpcChannelBuffer_GetBuffer(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage,REFIID riid) {
    return This->lpVtbl->GetBuffer(This,pMessage,riid);
}
static inline HRESULT IAsyncRpcChannelBuffer_SendReceive(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage,ULONG *pStatus) {
    return This->lpVtbl->SendReceive(This,pMessage,pStatus);
}
static inline HRESULT IAsyncRpcChannelBuffer_FreeBuffer(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMessage) {
    return This->lpVtbl->FreeBuffer(This,pMessage);
}
static inline HRESULT IAsyncRpcChannelBuffer_GetDestCtx(IAsyncRpcChannelBuffer* This,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext);
}
static inline HRESULT IAsyncRpcChannelBuffer_IsConnected(IAsyncRpcChannelBuffer* This) {
    return This->lpVtbl->IsConnected(This);
}
/*** IRpcChannelBuffer2 methods ***/
static inline HRESULT IAsyncRpcChannelBuffer_GetProtocolVersion(IAsyncRpcChannelBuffer* This,DWORD *pdwVersion) {
    return This->lpVtbl->GetProtocolVersion(This,pdwVersion);
}
/*** IAsyncRpcChannelBuffer methods ***/
static inline HRESULT IAsyncRpcChannelBuffer_Send(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMsg,ISynchronize *pSync,ULONG *pulStatus) {
    return This->lpVtbl->Send(This,pMsg,pSync,pulStatus);
}
static inline HRESULT IAsyncRpcChannelBuffer_Receive(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMsg,ULONG *pulStatus) {
    return This->lpVtbl->Receive(This,pMsg,pulStatus);
}
static inline HRESULT IAsyncRpcChannelBuffer_GetDestCtxEx(IAsyncRpcChannelBuffer* This,RPCOLEMESSAGE *pMsg,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext);
}
#endif
#endif

#endif


#endif  /* __IAsyncRpcChannelBuffer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IRpcChannelBuffer3 interface
 */
#ifndef __IRpcChannelBuffer3_INTERFACE_DEFINED__
#define __IRpcChannelBuffer3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcChannelBuffer3, 0x25b15600, 0x0115, 0x11d0, 0xbf,0x0d, 0x00,0xaa,0x00,0xb8,0xdf,0xd2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("25b15600-0115-11d0-bf0d-00aa00b8dfd2")
IRpcChannelBuffer3 : public IRpcChannelBuffer2
{
    virtual HRESULT STDMETHODCALLTYPE Send(
        RPCOLEMESSAGE *pMsg,
        ULONG *pulStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE Receive(
        RPCOLEMESSAGE *pMsg,
        ULONG ulSize,
        ULONG *pulStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        RPCOLEMESSAGE *pMsg) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCallContext(
        RPCOLEMESSAGE *pMsg,
        REFIID riid,
        void **pInterface) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDestCtxEx(
        RPCOLEMESSAGE *pMsg,
        DWORD *pdwDestContext,
        void **ppvDestContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetState(
        RPCOLEMESSAGE *pMsg,
        DWORD *pState) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterAsync(
        RPCOLEMESSAGE *pMsg,
        IAsyncManager *pAsyncMgr) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcChannelBuffer3, 0x25b15600, 0x0115, 0x11d0, 0xbf,0x0d, 0x00,0xaa,0x00,0xb8,0xdf,0xd2)
#endif
#else
typedef struct IRpcChannelBuffer3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcChannelBuffer3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcChannelBuffer3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcChannelBuffer3 *This);

    /*** IRpcChannelBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IRpcChannelBuffer3 *This,
        RPCOLEMESSAGE *pMessage,
        REFIID riid);

    HRESULT (STDMETHODCALLTYPE *SendReceive)(
        IRpcChannelBuffer3 *This,
        RPCOLEMESSAGE *pMessage,
        ULONG *pStatus);

    HRESULT (STDMETHODCALLTYPE *FreeBuffer)(
        IRpcChannelBuffer3 *This,
        RPCOLEMESSAGE *pMessage);

    HRESULT (STDMETHODCALLTYPE *GetDestCtx)(
        IRpcChannelBuffer3 *This,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    HRESULT (STDMETHODCALLTYPE *IsConnected)(
        IRpcChannelBuffer3 *This);

    /*** IRpcChannelBuffer2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProtocolVersion)(
        IRpcChannelBuffer3 *This,
        DWORD *pdwVersion);

    /*** IRpcChannelBuffer3 methods ***/
    HRESULT (STDMETHODCALLTYPE *Send)(
        IRpcChannelBuffer3 *This,
        RPCOLEMESSAGE *pMsg,
        ULONG *pulStatus);

    HRESULT (STDMETHODCALLTYPE *Receive)(
        IRpcChannelBuffer3 *This,
        RPCOLEMESSAGE *pMsg,
        ULONG ulSize,
        ULONG *pulStatus);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IRpcChannelBuffer3 *This,
        RPCOLEMESSAGE *pMsg);

    HRESULT (STDMETHODCALLTYPE *GetCallContext)(
        IRpcChannelBuffer3 *This,
        RPCOLEMESSAGE *pMsg,
        REFIID riid,
        void **pInterface);

    HRESULT (STDMETHODCALLTYPE *GetDestCtxEx)(
        IRpcChannelBuffer3 *This,
        RPCOLEMESSAGE *pMsg,
        DWORD *pdwDestContext,
        void **ppvDestContext);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IRpcChannelBuffer3 *This,
        RPCOLEMESSAGE *pMsg,
        DWORD *pState);

    HRESULT (STDMETHODCALLTYPE *RegisterAsync)(
        IRpcChannelBuffer3 *This,
        RPCOLEMESSAGE *pMsg,
        IAsyncManager *pAsyncMgr);

    END_INTERFACE
} IRpcChannelBuffer3Vtbl;

interface IRpcChannelBuffer3 {
    CONST_VTBL IRpcChannelBuffer3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcChannelBuffer3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcChannelBuffer3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcChannelBuffer3_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcChannelBuffer methods ***/
#define IRpcChannelBuffer3_GetBuffer(This,pMessage,riid) (This)->lpVtbl->GetBuffer(This,pMessage,riid)
#define IRpcChannelBuffer3_SendReceive(This,pMessage,pStatus) (This)->lpVtbl->SendReceive(This,pMessage,pStatus)
#define IRpcChannelBuffer3_FreeBuffer(This,pMessage) (This)->lpVtbl->FreeBuffer(This,pMessage)
#define IRpcChannelBuffer3_GetDestCtx(This,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext)
#define IRpcChannelBuffer3_IsConnected(This) (This)->lpVtbl->IsConnected(This)
/*** IRpcChannelBuffer2 methods ***/
#define IRpcChannelBuffer3_GetProtocolVersion(This,pdwVersion) (This)->lpVtbl->GetProtocolVersion(This,pdwVersion)
/*** IRpcChannelBuffer3 methods ***/
#define IRpcChannelBuffer3_Send(This,pMsg,pulStatus) (This)->lpVtbl->Send(This,pMsg,pulStatus)
#define IRpcChannelBuffer3_Receive(This,pMsg,ulSize,pulStatus) (This)->lpVtbl->Receive(This,pMsg,ulSize,pulStatus)
#define IRpcChannelBuffer3_Cancel(This,pMsg) (This)->lpVtbl->Cancel(This,pMsg)
#define IRpcChannelBuffer3_GetCallContext(This,pMsg,riid,pInterface) (This)->lpVtbl->GetCallContext(This,pMsg,riid,pInterface)
#define IRpcChannelBuffer3_GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext) (This)->lpVtbl->GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext)
#define IRpcChannelBuffer3_GetState(This,pMsg,pState) (This)->lpVtbl->GetState(This,pMsg,pState)
#define IRpcChannelBuffer3_RegisterAsync(This,pMsg,pAsyncMgr) (This)->lpVtbl->RegisterAsync(This,pMsg,pAsyncMgr)
#else
/*** IUnknown methods ***/
static inline HRESULT IRpcChannelBuffer3_QueryInterface(IRpcChannelBuffer3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRpcChannelBuffer3_AddRef(IRpcChannelBuffer3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRpcChannelBuffer3_Release(IRpcChannelBuffer3* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcChannelBuffer methods ***/
static inline HRESULT IRpcChannelBuffer3_GetBuffer(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMessage,REFIID riid) {
    return This->lpVtbl->GetBuffer(This,pMessage,riid);
}
static inline HRESULT IRpcChannelBuffer3_SendReceive(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMessage,ULONG *pStatus) {
    return This->lpVtbl->SendReceive(This,pMessage,pStatus);
}
static inline HRESULT IRpcChannelBuffer3_FreeBuffer(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMessage) {
    return This->lpVtbl->FreeBuffer(This,pMessage);
}
static inline HRESULT IRpcChannelBuffer3_GetDestCtx(IRpcChannelBuffer3* This,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtx(This,pdwDestContext,ppvDestContext);
}
static inline HRESULT IRpcChannelBuffer3_IsConnected(IRpcChannelBuffer3* This) {
    return This->lpVtbl->IsConnected(This);
}
/*** IRpcChannelBuffer2 methods ***/
static inline HRESULT IRpcChannelBuffer3_GetProtocolVersion(IRpcChannelBuffer3* This,DWORD *pdwVersion) {
    return This->lpVtbl->GetProtocolVersion(This,pdwVersion);
}
/*** IRpcChannelBuffer3 methods ***/
static inline HRESULT IRpcChannelBuffer3_Send(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,ULONG *pulStatus) {
    return This->lpVtbl->Send(This,pMsg,pulStatus);
}
static inline HRESULT IRpcChannelBuffer3_Receive(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,ULONG ulSize,ULONG *pulStatus) {
    return This->lpVtbl->Receive(This,pMsg,ulSize,pulStatus);
}
static inline HRESULT IRpcChannelBuffer3_Cancel(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg) {
    return This->lpVtbl->Cancel(This,pMsg);
}
static inline HRESULT IRpcChannelBuffer3_GetCallContext(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,REFIID riid,void **pInterface) {
    return This->lpVtbl->GetCallContext(This,pMsg,riid,pInterface);
}
static inline HRESULT IRpcChannelBuffer3_GetDestCtxEx(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,DWORD *pdwDestContext,void **ppvDestContext) {
    return This->lpVtbl->GetDestCtxEx(This,pMsg,pdwDestContext,ppvDestContext);
}
static inline HRESULT IRpcChannelBuffer3_GetState(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,DWORD *pState) {
    return This->lpVtbl->GetState(This,pMsg,pState);
}
static inline HRESULT IRpcChannelBuffer3_RegisterAsync(IRpcChannelBuffer3* This,RPCOLEMESSAGE *pMsg,IAsyncManager *pAsyncMgr) {
    return This->lpVtbl->RegisterAsync(This,pMsg,pAsyncMgr);
}
#endif
#endif

#endif


#endif  /* __IRpcChannelBuffer3_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IRpcSyntaxNegotiate interface
 */
#ifndef __IRpcSyntaxNegotiate_INTERFACE_DEFINED__
#define __IRpcSyntaxNegotiate_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcSyntaxNegotiate, 0x58a08519, 0x24c8, 0x4935, 0xb4,0x82, 0x3f,0xd8,0x23,0x33,0x3a,0x4f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("58a08519-24c8-4935-b482-3fd823333a4f")
IRpcSyntaxNegotiate : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE NegotiateSyntax(
        RPCOLEMESSAGE *pMsg) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcSyntaxNegotiate, 0x58a08519, 0x24c8, 0x4935, 0xb4,0x82, 0x3f,0xd8,0x23,0x33,0x3a,0x4f)
#endif
#else
typedef struct IRpcSyntaxNegotiateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcSyntaxNegotiate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcSyntaxNegotiate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcSyntaxNegotiate *This);

    /*** IRpcSyntaxNegotiate methods ***/
    HRESULT (STDMETHODCALLTYPE *NegotiateSyntax)(
        IRpcSyntaxNegotiate *This,
        RPCOLEMESSAGE *pMsg);

    END_INTERFACE
} IRpcSyntaxNegotiateVtbl;

interface IRpcSyntaxNegotiate {
    CONST_VTBL IRpcSyntaxNegotiateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcSyntaxNegotiate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcSyntaxNegotiate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcSyntaxNegotiate_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcSyntaxNegotiate methods ***/
#define IRpcSyntaxNegotiate_NegotiateSyntax(This,pMsg) (This)->lpVtbl->NegotiateSyntax(This,pMsg)
#else
/*** IUnknown methods ***/
static inline HRESULT IRpcSyntaxNegotiate_QueryInterface(IRpcSyntaxNegotiate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRpcSyntaxNegotiate_AddRef(IRpcSyntaxNegotiate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRpcSyntaxNegotiate_Release(IRpcSyntaxNegotiate* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcSyntaxNegotiate methods ***/
static inline HRESULT IRpcSyntaxNegotiate_NegotiateSyntax(IRpcSyntaxNegotiate* This,RPCOLEMESSAGE *pMsg) {
    return This->lpVtbl->NegotiateSyntax(This,pMsg);
}
#endif
#endif

#endif


#endif  /* __IRpcSyntaxNegotiate_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IRpcProxyBuffer interface
 */
#ifndef __IRpcProxyBuffer_INTERFACE_DEFINED__
#define __IRpcProxyBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcProxyBuffer, 0xd5f56a34, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d5f56a34-593b-101a-b569-08002b2dbf7a")
IRpcProxyBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Connect(
        IRpcChannelBuffer *pRpcChannelBuffer) = 0;

    virtual void STDMETHODCALLTYPE Disconnect(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcProxyBuffer, 0xd5f56a34, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a)
#endif
#else
typedef struct IRpcProxyBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcProxyBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcProxyBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcProxyBuffer *This);

    /*** IRpcProxyBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *Connect)(
        IRpcProxyBuffer *This,
        IRpcChannelBuffer *pRpcChannelBuffer);

    void (STDMETHODCALLTYPE *Disconnect)(
        IRpcProxyBuffer *This);

    END_INTERFACE
} IRpcProxyBufferVtbl;

interface IRpcProxyBuffer {
    CONST_VTBL IRpcProxyBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcProxyBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcProxyBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcProxyBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcProxyBuffer methods ***/
#define IRpcProxyBuffer_Connect(This,pRpcChannelBuffer) (This)->lpVtbl->Connect(This,pRpcChannelBuffer)
#define IRpcProxyBuffer_Disconnect(This) (This)->lpVtbl->Disconnect(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IRpcProxyBuffer_QueryInterface(IRpcProxyBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRpcProxyBuffer_AddRef(IRpcProxyBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRpcProxyBuffer_Release(IRpcProxyBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcProxyBuffer methods ***/
static inline HRESULT IRpcProxyBuffer_Connect(IRpcProxyBuffer* This,IRpcChannelBuffer *pRpcChannelBuffer) {
    return This->lpVtbl->Connect(This,pRpcChannelBuffer);
}
static inline void IRpcProxyBuffer_Disconnect(IRpcProxyBuffer* This) {
    This->lpVtbl->Disconnect(This);
}
#endif
#endif

#endif


#endif  /* __IRpcProxyBuffer_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IRpcStubBuffer interface
 */
#ifndef __IRpcStubBuffer_INTERFACE_DEFINED__
#define __IRpcStubBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcStubBuffer, 0xd5f56afc, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d5f56afc-593b-101a-b569-08002b2dbf7a")
IRpcStubBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Connect(
        IUnknown *pUnkServer) = 0;

    virtual void STDMETHODCALLTYPE Disconnect(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Invoke(
        RPCOLEMESSAGE *_prpcmsg,
        IRpcChannelBuffer *_pRpcChannelBuffer) = 0;

    virtual IRpcStubBuffer * STDMETHODCALLTYPE IsIIDSupported(
        REFIID riid) = 0;

    virtual ULONG STDMETHODCALLTYPE CountRefs(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DebugServerQueryInterface(
        void **ppv) = 0;

    virtual void STDMETHODCALLTYPE DebugServerRelease(
        void *pv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcStubBuffer, 0xd5f56afc, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a)
#endif
#else
typedef struct IRpcStubBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcStubBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcStubBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcStubBuffer *This);

    /*** IRpcStubBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *Connect)(
        IRpcStubBuffer *This,
        IUnknown *pUnkServer);

    void (STDMETHODCALLTYPE *Disconnect)(
        IRpcStubBuffer *This);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRpcStubBuffer *This,
        RPCOLEMESSAGE *_prpcmsg,
        IRpcChannelBuffer *_pRpcChannelBuffer);

    IRpcStubBuffer * (STDMETHODCALLTYPE *IsIIDSupported)(
        IRpcStubBuffer *This,
        REFIID riid);

    ULONG (STDMETHODCALLTYPE *CountRefs)(
        IRpcStubBuffer *This);

    HRESULT (STDMETHODCALLTYPE *DebugServerQueryInterface)(
        IRpcStubBuffer *This,
        void **ppv);

    void (STDMETHODCALLTYPE *DebugServerRelease)(
        IRpcStubBuffer *This,
        void *pv);

    END_INTERFACE
} IRpcStubBufferVtbl;

interface IRpcStubBuffer {
    CONST_VTBL IRpcStubBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcStubBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcStubBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcStubBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcStubBuffer methods ***/
#define IRpcStubBuffer_Connect(This,pUnkServer) (This)->lpVtbl->Connect(This,pUnkServer)
#define IRpcStubBuffer_Disconnect(This) (This)->lpVtbl->Disconnect(This)
#define IRpcStubBuffer_Invoke(This,_prpcmsg,_pRpcChannelBuffer) (This)->lpVtbl->Invoke(This,_prpcmsg,_pRpcChannelBuffer)
#define IRpcStubBuffer_IsIIDSupported(This,riid) (This)->lpVtbl->IsIIDSupported(This,riid)
#define IRpcStubBuffer_CountRefs(This) (This)->lpVtbl->CountRefs(This)
#define IRpcStubBuffer_DebugServerQueryInterface(This,ppv) (This)->lpVtbl->DebugServerQueryInterface(This,ppv)
#define IRpcStubBuffer_DebugServerRelease(This,pv) (This)->lpVtbl->DebugServerRelease(This,pv)
#else
/*** IUnknown methods ***/
static inline HRESULT IRpcStubBuffer_QueryInterface(IRpcStubBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRpcStubBuffer_AddRef(IRpcStubBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRpcStubBuffer_Release(IRpcStubBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcStubBuffer methods ***/
static inline HRESULT IRpcStubBuffer_Connect(IRpcStubBuffer* This,IUnknown *pUnkServer) {
    return This->lpVtbl->Connect(This,pUnkServer);
}
static inline void IRpcStubBuffer_Disconnect(IRpcStubBuffer* This) {
    This->lpVtbl->Disconnect(This);
}
static inline HRESULT IRpcStubBuffer_Invoke(IRpcStubBuffer* This,RPCOLEMESSAGE *_prpcmsg,IRpcChannelBuffer *_pRpcChannelBuffer) {
    return This->lpVtbl->Invoke(This,_prpcmsg,_pRpcChannelBuffer);
}
static inline IRpcStubBuffer * IRpcStubBuffer_IsIIDSupported(IRpcStubBuffer* This,REFIID riid) {
    return This->lpVtbl->IsIIDSupported(This,riid);
}
static inline ULONG IRpcStubBuffer_CountRefs(IRpcStubBuffer* This) {
    return This->lpVtbl->CountRefs(This);
}
static inline HRESULT IRpcStubBuffer_DebugServerQueryInterface(IRpcStubBuffer* This,void **ppv) {
    return This->lpVtbl->DebugServerQueryInterface(This,ppv);
}
static inline void IRpcStubBuffer_DebugServerRelease(IRpcStubBuffer* This,void *pv) {
    This->lpVtbl->DebugServerRelease(This,pv);
}
#endif
#endif

#endif


#endif  /* __IRpcStubBuffer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPSFactoryBuffer interface
 */
#ifndef __IPSFactoryBuffer_INTERFACE_DEFINED__
#define __IPSFactoryBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPSFactoryBuffer, 0xd5f569d0, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d5f569d0-593b-101a-b569-08002b2dbf7a")
IPSFactoryBuffer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateProxy(
        IUnknown *pUnkOuter,
        REFIID riid,
        IRpcProxyBuffer **ppProxy,
        void **ppv) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateStub(
        REFIID riid,
        IUnknown *pUnkServer,
        IRpcStubBuffer **ppStub) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPSFactoryBuffer, 0xd5f569d0, 0x593b, 0x101a, 0xb5,0x69, 0x08,0x00,0x2b,0x2d,0xbf,0x7a)
#endif
#else
typedef struct IPSFactoryBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPSFactoryBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPSFactoryBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPSFactoryBuffer *This);

    /*** IPSFactoryBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateProxy)(
        IPSFactoryBuffer *This,
        IUnknown *pUnkOuter,
        REFIID riid,
        IRpcProxyBuffer **ppProxy,
        void **ppv);

    HRESULT (STDMETHODCALLTYPE *CreateStub)(
        IPSFactoryBuffer *This,
        REFIID riid,
        IUnknown *pUnkServer,
        IRpcStubBuffer **ppStub);

    END_INTERFACE
} IPSFactoryBufferVtbl;

interface IPSFactoryBuffer {
    CONST_VTBL IPSFactoryBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPSFactoryBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPSFactoryBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPSFactoryBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IPSFactoryBuffer methods ***/
#define IPSFactoryBuffer_CreateProxy(This,pUnkOuter,riid,ppProxy,ppv) (This)->lpVtbl->CreateProxy(This,pUnkOuter,riid,ppProxy,ppv)
#define IPSFactoryBuffer_CreateStub(This,riid,pUnkServer,ppStub) (This)->lpVtbl->CreateStub(This,riid,pUnkServer,ppStub)
#else
/*** IUnknown methods ***/
static inline HRESULT IPSFactoryBuffer_QueryInterface(IPSFactoryBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPSFactoryBuffer_AddRef(IPSFactoryBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPSFactoryBuffer_Release(IPSFactoryBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IPSFactoryBuffer methods ***/
static inline HRESULT IPSFactoryBuffer_CreateProxy(IPSFactoryBuffer* This,IUnknown *pUnkOuter,REFIID riid,IRpcProxyBuffer **ppProxy,void **ppv) {
    return This->lpVtbl->CreateProxy(This,pUnkOuter,riid,ppProxy,ppv);
}
static inline HRESULT IPSFactoryBuffer_CreateStub(IPSFactoryBuffer* This,REFIID riid,IUnknown *pUnkServer,IRpcStubBuffer **ppStub) {
    return This->lpVtbl->CreateStub(This,riid,pUnkServer,ppStub);
}
#endif
#endif

#endif


#endif  /* __IPSFactoryBuffer_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#if  (_WIN32_WINNT >= 0x0400 ) || defined(_WIN32_DCOM)
typedef struct SChannelHookCallInfo {
    IID iid;
    DWORD cbSize;
    GUID uCausality;
    DWORD dwServerPid;
    DWORD iMethod;
    void *pObject;
} SChannelHookCallInfo;

/*****************************************************************************
 * IChannelHook interface
 */
#ifndef __IChannelHook_INTERFACE_DEFINED__
#define __IChannelHook_INTERFACE_DEFINED__

DEFINE_GUID(IID_IChannelHook, 0x1008c4a0, 0x7613, 0x11cf, 0x9a,0xf1, 0x00,0x20,0xaf,0x6e,0x72,0xf4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1008c4a0-7613-11cf-9af1-0020af6e72f4")
IChannelHook : public IUnknown
{
    virtual void STDMETHODCALLTYPE ClientGetSize(
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize) = 0;

    virtual void STDMETHODCALLTYPE ClientFillBuffer(
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize,
        void *pDataBuffer) = 0;

    virtual void STDMETHODCALLTYPE ClientNotify(
        REFGUID uExtent,
        REFIID riid,
        ULONG cbDataSize,
        void *pDataBuffer,
        DWORD lDataRep,
        HRESULT hrFault) = 0;

    virtual void STDMETHODCALLTYPE ServerNotify(
        REFGUID uExtent,
        REFIID riid,
        ULONG cbDataSize,
        void *pDataBuffer,
        DWORD lDataRep) = 0;

    virtual void STDMETHODCALLTYPE ServerGetSize(
        REFGUID uExtent,
        REFIID riid,
        HRESULT hrFault,
        ULONG *pDataSize) = 0;

    virtual void STDMETHODCALLTYPE ServerFillBuffer(
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize,
        void *pDataBuffer,
        HRESULT hrFault) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IChannelHook, 0x1008c4a0, 0x7613, 0x11cf, 0x9a,0xf1, 0x00,0x20,0xaf,0x6e,0x72,0xf4)
#endif
#else
typedef struct IChannelHookVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IChannelHook *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IChannelHook *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IChannelHook *This);

    /*** IChannelHook methods ***/
    void (STDMETHODCALLTYPE *ClientGetSize)(
        IChannelHook *This,
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize);

    void (STDMETHODCALLTYPE *ClientFillBuffer)(
        IChannelHook *This,
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize,
        void *pDataBuffer);

    void (STDMETHODCALLTYPE *ClientNotify)(
        IChannelHook *This,
        REFGUID uExtent,
        REFIID riid,
        ULONG cbDataSize,
        void *pDataBuffer,
        DWORD lDataRep,
        HRESULT hrFault);

    void (STDMETHODCALLTYPE *ServerNotify)(
        IChannelHook *This,
        REFGUID uExtent,
        REFIID riid,
        ULONG cbDataSize,
        void *pDataBuffer,
        DWORD lDataRep);

    void (STDMETHODCALLTYPE *ServerGetSize)(
        IChannelHook *This,
        REFGUID uExtent,
        REFIID riid,
        HRESULT hrFault,
        ULONG *pDataSize);

    void (STDMETHODCALLTYPE *ServerFillBuffer)(
        IChannelHook *This,
        REFGUID uExtent,
        REFIID riid,
        ULONG *pDataSize,
        void *pDataBuffer,
        HRESULT hrFault);

    END_INTERFACE
} IChannelHookVtbl;

interface IChannelHook {
    CONST_VTBL IChannelHookVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IChannelHook_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IChannelHook_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IChannelHook_Release(This) (This)->lpVtbl->Release(This)
/*** IChannelHook methods ***/
#define IChannelHook_ClientGetSize(This,uExtent,riid,pDataSize) (This)->lpVtbl->ClientGetSize(This,uExtent,riid,pDataSize)
#define IChannelHook_ClientFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer) (This)->lpVtbl->ClientFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer)
#define IChannelHook_ClientNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep,hrFault) (This)->lpVtbl->ClientNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep,hrFault)
#define IChannelHook_ServerNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep) (This)->lpVtbl->ServerNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep)
#define IChannelHook_ServerGetSize(This,uExtent,riid,hrFault,pDataSize) (This)->lpVtbl->ServerGetSize(This,uExtent,riid,hrFault,pDataSize)
#define IChannelHook_ServerFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer,hrFault) (This)->lpVtbl->ServerFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer,hrFault)
#else
/*** IUnknown methods ***/
static inline HRESULT IChannelHook_QueryInterface(IChannelHook* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IChannelHook_AddRef(IChannelHook* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IChannelHook_Release(IChannelHook* This) {
    return This->lpVtbl->Release(This);
}
/*** IChannelHook methods ***/
static inline void IChannelHook_ClientGetSize(IChannelHook* This,REFGUID uExtent,REFIID riid,ULONG *pDataSize) {
    This->lpVtbl->ClientGetSize(This,uExtent,riid,pDataSize);
}
static inline void IChannelHook_ClientFillBuffer(IChannelHook* This,REFGUID uExtent,REFIID riid,ULONG *pDataSize,void *pDataBuffer) {
    This->lpVtbl->ClientFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer);
}
static inline void IChannelHook_ClientNotify(IChannelHook* This,REFGUID uExtent,REFIID riid,ULONG cbDataSize,void *pDataBuffer,DWORD lDataRep,HRESULT hrFault) {
    This->lpVtbl->ClientNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep,hrFault);
}
static inline void IChannelHook_ServerNotify(IChannelHook* This,REFGUID uExtent,REFIID riid,ULONG cbDataSize,void *pDataBuffer,DWORD lDataRep) {
    This->lpVtbl->ServerNotify(This,uExtent,riid,cbDataSize,pDataBuffer,lDataRep);
}
static inline void IChannelHook_ServerGetSize(IChannelHook* This,REFGUID uExtent,REFIID riid,HRESULT hrFault,ULONG *pDataSize) {
    This->lpVtbl->ServerGetSize(This,uExtent,riid,hrFault,pDataSize);
}
static inline void IChannelHook_ServerFillBuffer(IChannelHook* This,REFGUID uExtent,REFIID riid,ULONG *pDataSize,void *pDataBuffer,HRESULT hrFault) {
    This->lpVtbl->ServerFillBuffer(This,uExtent,riid,pDataSize,pDataBuffer,hrFault);
}
#endif
#endif

#endif


#endif  /* __IChannelHook_INTERFACE_DEFINED__ */

#endif
#endif

#if  (_WIN32_WINNT >= 0x0400 ) || defined(_WIN32_DCOM)
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * IClientSecurity interface
 */
#ifndef __IClientSecurity_INTERFACE_DEFINED__
#define __IClientSecurity_INTERFACE_DEFINED__

typedef struct tagSOLE_AUTHENTICATION_SERVICE {
    DWORD dwAuthnSvc;
    DWORD dwAuthzSvc;
    OLECHAR *pPrincipalName;
    HRESULT hr;
} SOLE_AUTHENTICATION_SERVICE;

typedef SOLE_AUTHENTICATION_SERVICE *PSOLE_AUTHENTICATION_SERVICE;

typedef enum tagEOLE_AUTHENTICATION_CAPABILITIES {
    EOAC_NONE = 0x0,
    EOAC_MUTUAL_AUTH = 0x1,
    EOAC_STATIC_CLOAKING = 0x20,
    EOAC_DYNAMIC_CLOAKING = 0x40,
    EOAC_ANY_AUTHORITY = 0x80,
    EOAC_MAKE_FULLSIC = 0x100,
    EOAC_DEFAULT = 0x800,
    EOAC_SECURE_REFS = 0x2,
    EOAC_ACCESS_CONTROL = 0x4,
    EOAC_APPID = 0x8,
    EOAC_DYNAMIC = 0x10,
    EOAC_REQUIRE_FULLSIC = 0x200,
    EOAC_AUTO_IMPERSONATE = 0x400,
    EOAC_NO_CUSTOM_MARSHAL = 0x2000,
    EOAC_DISABLE_AAA = 0x1000
} EOLE_AUTHENTICATION_CAPABILITIES;

#define COLE_DEFAULT_PRINCIPAL ((OLECHAR *)(INT_PTR)-1)

#define COLE_DEFAULT_AUTHINFO ((void *)(INT_PTR)-1)


typedef struct tagSOLE_AUTHENTICATION_INFO {
    DWORD dwAuthnSvc;
    DWORD dwAuthzSvc;
    void *pAuthInfo;
} SOLE_AUTHENTICATION_INFO;
typedef struct tagSOLE_AUTHENTICATION_INFO *PSOLE_AUTHENTICATION_INFO;

typedef struct tagSOLE_AUTHENTICATION_LIST {
    DWORD cAuthInfo;
    SOLE_AUTHENTICATION_INFO *aAuthInfo;
} SOLE_AUTHENTICATION_LIST;
typedef struct tagSOLE_AUTHENTICATION_LIST *PSOLE_AUTHENTICATION_LIST;

DEFINE_GUID(IID_IClientSecurity, 0x0000013d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000013d-0000-0000-c000-000000000046")
IClientSecurity : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryBlanket(
        IUnknown *pProxy,
        DWORD *pAuthnSvc,
        DWORD *pAuthzSvc,
        OLECHAR **pServerPrincName,
        DWORD *pAuthnLevel,
        DWORD *pImpLevel,
        void **pAuthInfo,
        DWORD *pCapabilites) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBlanket(
        IUnknown *pProxy,
        DWORD dwAuthnSvc,
        DWORD dwAuthzSvc,
        OLECHAR *pServerPrincName,
        DWORD dwAuthnLevel,
        DWORD dwImpLevel,
        void *pAuthInfo,
        DWORD dwCapabilities) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyProxy(
        IUnknown *pProxy,
        IUnknown **ppCopy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IClientSecurity, 0x0000013d, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IClientSecurityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IClientSecurity *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IClientSecurity *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IClientSecurity *This);

    /*** IClientSecurity methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryBlanket)(
        IClientSecurity *This,
        IUnknown *pProxy,
        DWORD *pAuthnSvc,
        DWORD *pAuthzSvc,
        OLECHAR **pServerPrincName,
        DWORD *pAuthnLevel,
        DWORD *pImpLevel,
        void **pAuthInfo,
        DWORD *pCapabilites);

    HRESULT (STDMETHODCALLTYPE *SetBlanket)(
        IClientSecurity *This,
        IUnknown *pProxy,
        DWORD dwAuthnSvc,
        DWORD dwAuthzSvc,
        OLECHAR *pServerPrincName,
        DWORD dwAuthnLevel,
        DWORD dwImpLevel,
        void *pAuthInfo,
        DWORD dwCapabilities);

    HRESULT (STDMETHODCALLTYPE *CopyProxy)(
        IClientSecurity *This,
        IUnknown *pProxy,
        IUnknown **ppCopy);

    END_INTERFACE
} IClientSecurityVtbl;

interface IClientSecurity {
    CONST_VTBL IClientSecurityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IClientSecurity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IClientSecurity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IClientSecurity_Release(This) (This)->lpVtbl->Release(This)
/*** IClientSecurity methods ***/
#define IClientSecurity_QueryBlanket(This,pProxy,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pAuthInfo,pCapabilites) (This)->lpVtbl->QueryBlanket(This,pProxy,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pAuthInfo,pCapabilites)
#define IClientSecurity_SetBlanket(This,pProxy,dwAuthnSvc,dwAuthzSvc,pServerPrincName,dwAuthnLevel,dwImpLevel,pAuthInfo,dwCapabilities) (This)->lpVtbl->SetBlanket(This,pProxy,dwAuthnSvc,dwAuthzSvc,pServerPrincName,dwAuthnLevel,dwImpLevel,pAuthInfo,dwCapabilities)
#define IClientSecurity_CopyProxy(This,pProxy,ppCopy) (This)->lpVtbl->CopyProxy(This,pProxy,ppCopy)
#else
/*** IUnknown methods ***/
static inline HRESULT IClientSecurity_QueryInterface(IClientSecurity* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IClientSecurity_AddRef(IClientSecurity* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IClientSecurity_Release(IClientSecurity* This) {
    return This->lpVtbl->Release(This);
}
/*** IClientSecurity methods ***/
static inline HRESULT IClientSecurity_QueryBlanket(IClientSecurity* This,IUnknown *pProxy,DWORD *pAuthnSvc,DWORD *pAuthzSvc,OLECHAR **pServerPrincName,DWORD *pAuthnLevel,DWORD *pImpLevel,void **pAuthInfo,DWORD *pCapabilites) {
    return This->lpVtbl->QueryBlanket(This,pProxy,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pAuthInfo,pCapabilites);
}
static inline HRESULT IClientSecurity_SetBlanket(IClientSecurity* This,IUnknown *pProxy,DWORD dwAuthnSvc,DWORD dwAuthzSvc,OLECHAR *pServerPrincName,DWORD dwAuthnLevel,DWORD dwImpLevel,void *pAuthInfo,DWORD dwCapabilities) {
    return This->lpVtbl->SetBlanket(This,pProxy,dwAuthnSvc,dwAuthzSvc,pServerPrincName,dwAuthnLevel,dwImpLevel,pAuthInfo,dwCapabilities);
}
static inline HRESULT IClientSecurity_CopyProxy(IClientSecurity* This,IUnknown *pProxy,IUnknown **ppCopy) {
    return This->lpVtbl->CopyProxy(This,pProxy,ppCopy);
}
#endif
#endif

#endif


#endif  /* __IClientSecurity_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IServerSecurity interface
 */
#ifndef __IServerSecurity_INTERFACE_DEFINED__
#define __IServerSecurity_INTERFACE_DEFINED__

DEFINE_GUID(IID_IServerSecurity, 0x0000013e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000013e-0000-0000-c000-000000000046")
IServerSecurity : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryBlanket(
        DWORD *pAuthnSvc,
        DWORD *pAuthzSvc,
        OLECHAR **pServerPrincName,
        DWORD *pAuthnLevel,
        DWORD *pImpLevel,
        void **pPrivs,
        DWORD *pCapabilities) = 0;

    virtual HRESULT STDMETHODCALLTYPE ImpersonateClient(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RevertToSelf(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsImpersonating(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IServerSecurity, 0x0000013e, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IServerSecurityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IServerSecurity *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IServerSecurity *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IServerSecurity *This);

    /*** IServerSecurity methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryBlanket)(
        IServerSecurity *This,
        DWORD *pAuthnSvc,
        DWORD *pAuthzSvc,
        OLECHAR **pServerPrincName,
        DWORD *pAuthnLevel,
        DWORD *pImpLevel,
        void **pPrivs,
        DWORD *pCapabilities);

    HRESULT (STDMETHODCALLTYPE *ImpersonateClient)(
        IServerSecurity *This);

    HRESULT (STDMETHODCALLTYPE *RevertToSelf)(
        IServerSecurity *This);

    WINBOOL (STDMETHODCALLTYPE *IsImpersonating)(
        IServerSecurity *This);

    END_INTERFACE
} IServerSecurityVtbl;

interface IServerSecurity {
    CONST_VTBL IServerSecurityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IServerSecurity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IServerSecurity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IServerSecurity_Release(This) (This)->lpVtbl->Release(This)
/*** IServerSecurity methods ***/
#define IServerSecurity_QueryBlanket(This,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pPrivs,pCapabilities) (This)->lpVtbl->QueryBlanket(This,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pPrivs,pCapabilities)
#define IServerSecurity_ImpersonateClient(This) (This)->lpVtbl->ImpersonateClient(This)
#define IServerSecurity_RevertToSelf(This) (This)->lpVtbl->RevertToSelf(This)
#define IServerSecurity_IsImpersonating(This) (This)->lpVtbl->IsImpersonating(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IServerSecurity_QueryInterface(IServerSecurity* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IServerSecurity_AddRef(IServerSecurity* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IServerSecurity_Release(IServerSecurity* This) {
    return This->lpVtbl->Release(This);
}
/*** IServerSecurity methods ***/
static inline HRESULT IServerSecurity_QueryBlanket(IServerSecurity* This,DWORD *pAuthnSvc,DWORD *pAuthzSvc,OLECHAR **pServerPrincName,DWORD *pAuthnLevel,DWORD *pImpLevel,void **pPrivs,DWORD *pCapabilities) {
    return This->lpVtbl->QueryBlanket(This,pAuthnSvc,pAuthzSvc,pServerPrincName,pAuthnLevel,pImpLevel,pPrivs,pCapabilities);
}
static inline HRESULT IServerSecurity_ImpersonateClient(IServerSecurity* This) {
    return This->lpVtbl->ImpersonateClient(This);
}
static inline HRESULT IServerSecurity_RevertToSelf(IServerSecurity* This) {
    return This->lpVtbl->RevertToSelf(This);
}
static inline WINBOOL IServerSecurity_IsImpersonating(IServerSecurity* This) {
    return This->lpVtbl->IsImpersonating(This);
}
#endif
#endif

#endif


#endif  /* __IServerSecurity_INTERFACE_DEFINED__ */


typedef enum tagRPCOPT_PROPERTIES {
    COMBND_RPCTIMEOUT = 0x1,
    COMBND_SERVER_LOCALITY = 0x2,
    COMBND_RESERVED1 = 0x4
} RPCOPT_PROPERTIES;

typedef enum tagRPCOPT_SERVER_LOCALITY_VALUES {
    SERVER_LOCALITY_PROCESS_LOCAL = 0,
    SERVER_LOCALITY_MACHINE_LOCAL = 1,
    SERVER_LOCALITY_REMOTE = 2
} RPCOPT_SERVER_LOCALITY_VALUES;

/*****************************************************************************
 * IRpcOptions interface
 */
#ifndef __IRpcOptions_INTERFACE_DEFINED__
#define __IRpcOptions_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcOptions, 0x00000144, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000144-0000-0000-c000-000000000046")
IRpcOptions : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Set(
        IUnknown *pPrx,
        RPCOPT_PROPERTIES dwProperty,
        ULONG_PTR dwValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE Query(
        IUnknown *pPrx,
        RPCOPT_PROPERTIES dwProperty,
        ULONG_PTR *pdwValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcOptions, 0x00000144, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IRpcOptionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcOptions *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcOptions *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcOptions *This);

    /*** IRpcOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *Set)(
        IRpcOptions *This,
        IUnknown *pPrx,
        RPCOPT_PROPERTIES dwProperty,
        ULONG_PTR dwValue);

    HRESULT (STDMETHODCALLTYPE *Query)(
        IRpcOptions *This,
        IUnknown *pPrx,
        RPCOPT_PROPERTIES dwProperty,
        ULONG_PTR *pdwValue);

    END_INTERFACE
} IRpcOptionsVtbl;

interface IRpcOptions {
    CONST_VTBL IRpcOptionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcOptions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcOptions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcOptions_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcOptions methods ***/
#define IRpcOptions_Set(This,pPrx,dwProperty,dwValue) (This)->lpVtbl->Set(This,pPrx,dwProperty,dwValue)
#define IRpcOptions_Query(This,pPrx,dwProperty,pdwValue) (This)->lpVtbl->Query(This,pPrx,dwProperty,pdwValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IRpcOptions_QueryInterface(IRpcOptions* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRpcOptions_AddRef(IRpcOptions* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRpcOptions_Release(IRpcOptions* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcOptions methods ***/
static inline HRESULT IRpcOptions_Set(IRpcOptions* This,IUnknown *pPrx,RPCOPT_PROPERTIES dwProperty,ULONG_PTR dwValue) {
    return This->lpVtbl->Set(This,pPrx,dwProperty,dwValue);
}
static inline HRESULT IRpcOptions_Query(IRpcOptions* This,IUnknown *pPrx,RPCOPT_PROPERTIES dwProperty,ULONG_PTR *pdwValue) {
    return This->lpVtbl->Query(This,pPrx,dwProperty,pdwValue);
}
#endif
#endif

#endif


#endif  /* __IRpcOptions_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef enum tagGLOBALOPT_PROPERTIES {
    COMGLB_EXCEPTION_HANDLING = 1,
    COMGLB_APPID = 2,
    COMGLB_RPC_THREADPOOL_SETTING = 3,
    COMGLB_RO_SETTINGS = 4,
    COMGLB_UNMARSHALING_POLICY = 5
} GLOBALOPT_PROPERTIES;

typedef enum tagGLOBALOPT_EH_VALUES {
    COMGLB_EXCEPTION_HANDLE = 0,
    COMGLB_EXCEPTION_DONOT_HANDLE_FATAL = 1,
    COMGLB_EXCEPTION_DONOT_HANDLE = COMGLB_EXCEPTION_DONOT_HANDLE_FATAL,
    COMGLB_EXCEPTION_DONOT_HANDLE_ANY = 2
} GLOBALOPT_EH_VALUES;

typedef enum tagGLOBALOPT_RPCTP_VALUES {
    COMGLB_RPC_THREADPOOL_SETTING_DEFAULT_POOL = 0,
    COMGLB_RPC_THREADPOOL_SETTING_PRIVATE_POOL = 1
} GLOBALOPT_RPCTP_VALUES;

typedef enum tagGLOBALOPT_RO_FLAGS {
    COMGLB_STA_MODALLOOP_REMOVE_TOUCH_MESSAGES = 0x1,
    COMGLB_STA_MODALLOOP_SHARED_QUEUE_REMOVE_INPUT_MESSAGES = 0x2,
    COMGLB_STA_MODALLOOP_SHARED_QUEUE_DONOT_REMOVE_INPUT_MESSAGES = 0x4,
    COMGLB_FAST_RUNDOWN = 0x8,
    COMGLB_RESERVED1 = 0x10,
    COMGLB_RESERVED2 = 0x20,
    COMGLB_RESERVED3 = 0x40,
    COMGLB_STA_MODALLOOP_SHARED_QUEUE_REORDER_POINTER_MESSAGES = 0x80
} GLOBALOPT_RO_FLAGS;

typedef enum tagGLOBALOPT_UNMARSHALING_POLICY_VALUES {
    COMGLB_UNMARSHALING_POLICY_NORMAL = 0,
    COMGLB_UNMARSHALING_POLICY_STRONG = 1,
    COMGLB_UNMARSHALING_POLICY_HYBRID = 2
} GLOBALOPT_UNMARSHALING_POLICY_VALUES;

/*****************************************************************************
 * IGlobalOptions interface
 */
#ifndef __IGlobalOptions_INTERFACE_DEFINED__
#define __IGlobalOptions_INTERFACE_DEFINED__

DEFINE_GUID(IID_IGlobalOptions, 0x0000015b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000015b-0000-0000-c000-000000000046")
IGlobalOptions : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Set(
        GLOBALOPT_PROPERTIES dwProperty,
        ULONG_PTR dwValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE Query(
        GLOBALOPT_PROPERTIES dwProperty,
        ULONG_PTR *pdwValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IGlobalOptions, 0x0000015b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IGlobalOptionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IGlobalOptions *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IGlobalOptions *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IGlobalOptions *This);

    /*** IGlobalOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *Set)(
        IGlobalOptions *This,
        GLOBALOPT_PROPERTIES dwProperty,
        ULONG_PTR dwValue);

    HRESULT (STDMETHODCALLTYPE *Query)(
        IGlobalOptions *This,
        GLOBALOPT_PROPERTIES dwProperty,
        ULONG_PTR *pdwValue);

    END_INTERFACE
} IGlobalOptionsVtbl;

interface IGlobalOptions {
    CONST_VTBL IGlobalOptionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IGlobalOptions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IGlobalOptions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IGlobalOptions_Release(This) (This)->lpVtbl->Release(This)
/*** IGlobalOptions methods ***/
#define IGlobalOptions_Set(This,dwProperty,dwValue) (This)->lpVtbl->Set(This,dwProperty,dwValue)
#define IGlobalOptions_Query(This,dwProperty,pdwValue) (This)->lpVtbl->Query(This,dwProperty,pdwValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IGlobalOptions_QueryInterface(IGlobalOptions* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IGlobalOptions_AddRef(IGlobalOptions* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IGlobalOptions_Release(IGlobalOptions* This) {
    return This->lpVtbl->Release(This);
}
/*** IGlobalOptions methods ***/
static inline HRESULT IGlobalOptions_Set(IGlobalOptions* This,GLOBALOPT_PROPERTIES dwProperty,ULONG_PTR dwValue) {
    return This->lpVtbl->Set(This,dwProperty,dwValue);
}
static inline HRESULT IGlobalOptions_Query(IGlobalOptions* This,GLOBALOPT_PROPERTIES dwProperty,ULONG_PTR *pdwValue) {
    return This->lpVtbl->Query(This,dwProperty,pdwValue);
}
#endif
#endif

#endif


#endif  /* __IGlobalOptions_INTERFACE_DEFINED__ */

#endif
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * ISurrogate interface
 */
#ifndef __ISurrogate_INTERFACE_DEFINED__
#define __ISurrogate_INTERFACE_DEFINED__

typedef ISurrogate *LPSURROGATE;

DEFINE_GUID(IID_ISurrogate, 0x00000022, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000022-0000-0000-c000-000000000046")
ISurrogate : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE LoadDllServer(
        REFCLSID Clsid) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeSurrogate(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISurrogate, 0x00000022, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISurrogateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISurrogate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISurrogate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISurrogate *This);

    /*** ISurrogate methods ***/
    HRESULT (STDMETHODCALLTYPE *LoadDllServer)(
        ISurrogate *This,
        REFCLSID Clsid);

    HRESULT (STDMETHODCALLTYPE *FreeSurrogate)(
        ISurrogate *This);

    END_INTERFACE
} ISurrogateVtbl;

interface ISurrogate {
    CONST_VTBL ISurrogateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISurrogate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISurrogate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISurrogate_Release(This) (This)->lpVtbl->Release(This)
/*** ISurrogate methods ***/
#define ISurrogate_LoadDllServer(This,Clsid) (This)->lpVtbl->LoadDllServer(This,Clsid)
#define ISurrogate_FreeSurrogate(This) (This)->lpVtbl->FreeSurrogate(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ISurrogate_QueryInterface(ISurrogate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISurrogate_AddRef(ISurrogate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISurrogate_Release(ISurrogate* This) {
    return This->lpVtbl->Release(This);
}
/*** ISurrogate methods ***/
static inline HRESULT ISurrogate_LoadDllServer(ISurrogate* This,REFCLSID Clsid) {
    return This->lpVtbl->LoadDllServer(This,Clsid);
}
static inline HRESULT ISurrogate_FreeSurrogate(ISurrogate* This) {
    return This->lpVtbl->FreeSurrogate(This);
}
#endif
#endif

#endif


#endif  /* __ISurrogate_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IGlobalInterfaceTable interface
 */
#ifndef __IGlobalInterfaceTable_INTERFACE_DEFINED__
#define __IGlobalInterfaceTable_INTERFACE_DEFINED__

typedef IGlobalInterfaceTable *LPGLOBALINTERFACETABLE;

DEFINE_GUID(IID_IGlobalInterfaceTable, 0x00000146, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000146-0000-0000-c000-000000000046")
IGlobalInterfaceTable : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RegisterInterfaceInGlobal(
        IUnknown *pUnk,
        REFIID riid,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE RevokeInterfaceFromGlobal(
        DWORD dwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInterfaceFromGlobal(
        DWORD dwCookie,
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IGlobalInterfaceTable, 0x00000146, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IGlobalInterfaceTableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IGlobalInterfaceTable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IGlobalInterfaceTable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IGlobalInterfaceTable *This);

    /*** IGlobalInterfaceTable methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterInterfaceInGlobal)(
        IGlobalInterfaceTable *This,
        IUnknown *pUnk,
        REFIID riid,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RevokeInterfaceFromGlobal)(
        IGlobalInterfaceTable *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *GetInterfaceFromGlobal)(
        IGlobalInterfaceTable *This,
        DWORD dwCookie,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IGlobalInterfaceTableVtbl;

interface IGlobalInterfaceTable {
    CONST_VTBL IGlobalInterfaceTableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IGlobalInterfaceTable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IGlobalInterfaceTable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IGlobalInterfaceTable_Release(This) (This)->lpVtbl->Release(This)
/*** IGlobalInterfaceTable methods ***/
#define IGlobalInterfaceTable_RegisterInterfaceInGlobal(This,pUnk,riid,pdwCookie) (This)->lpVtbl->RegisterInterfaceInGlobal(This,pUnk,riid,pdwCookie)
#define IGlobalInterfaceTable_RevokeInterfaceFromGlobal(This,dwCookie) (This)->lpVtbl->RevokeInterfaceFromGlobal(This,dwCookie)
#define IGlobalInterfaceTable_GetInterfaceFromGlobal(This,dwCookie,riid,ppv) (This)->lpVtbl->GetInterfaceFromGlobal(This,dwCookie,riid,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT IGlobalInterfaceTable_QueryInterface(IGlobalInterfaceTable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IGlobalInterfaceTable_AddRef(IGlobalInterfaceTable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IGlobalInterfaceTable_Release(IGlobalInterfaceTable* This) {
    return This->lpVtbl->Release(This);
}
/*** IGlobalInterfaceTable methods ***/
static inline HRESULT IGlobalInterfaceTable_RegisterInterfaceInGlobal(IGlobalInterfaceTable* This,IUnknown *pUnk,REFIID riid,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterInterfaceInGlobal(This,pUnk,riid,pdwCookie);
}
static inline HRESULT IGlobalInterfaceTable_RevokeInterfaceFromGlobal(IGlobalInterfaceTable* This,DWORD dwCookie) {
    return This->lpVtbl->RevokeInterfaceFromGlobal(This,dwCookie);
}
static inline HRESULT IGlobalInterfaceTable_GetInterfaceFromGlobal(IGlobalInterfaceTable* This,DWORD dwCookie,REFIID riid,void **ppv) {
    return This->lpVtbl->GetInterfaceFromGlobal(This,dwCookie,riid,ppv);
}
#endif
#endif

#endif


#endif  /* __IGlobalInterfaceTable_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * ISynchronize interface
 */
#ifndef __ISynchronize_INTERFACE_DEFINED__
#define __ISynchronize_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISynchronize, 0x00000030, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000030-0000-0000-c000-000000000046")
ISynchronize : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Wait(
        DWORD dwFlags,
        DWORD dwMilliseconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE Signal(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISynchronize, 0x00000030, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISynchronizeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISynchronize *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISynchronize *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISynchronize *This);

    /*** ISynchronize methods ***/
    HRESULT (STDMETHODCALLTYPE *Wait)(
        ISynchronize *This,
        DWORD dwFlags,
        DWORD dwMilliseconds);

    HRESULT (STDMETHODCALLTYPE *Signal)(
        ISynchronize *This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        ISynchronize *This);

    END_INTERFACE
} ISynchronizeVtbl;

interface ISynchronize {
    CONST_VTBL ISynchronizeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISynchronize_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISynchronize_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISynchronize_Release(This) (This)->lpVtbl->Release(This)
/*** ISynchronize methods ***/
#define ISynchronize_Wait(This,dwFlags,dwMilliseconds) (This)->lpVtbl->Wait(This,dwFlags,dwMilliseconds)
#define ISynchronize_Signal(This) (This)->lpVtbl->Signal(This)
#define ISynchronize_Reset(This) (This)->lpVtbl->Reset(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ISynchronize_QueryInterface(ISynchronize* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISynchronize_AddRef(ISynchronize* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISynchronize_Release(ISynchronize* This) {
    return This->lpVtbl->Release(This);
}
/*** ISynchronize methods ***/
static inline HRESULT ISynchronize_Wait(ISynchronize* This,DWORD dwFlags,DWORD dwMilliseconds) {
    return This->lpVtbl->Wait(This,dwFlags,dwMilliseconds);
}
static inline HRESULT ISynchronize_Signal(ISynchronize* This) {
    return This->lpVtbl->Signal(This);
}
static inline HRESULT ISynchronize_Reset(ISynchronize* This) {
    return This->lpVtbl->Reset(This);
}
#endif
#endif

#endif


#endif  /* __ISynchronize_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISynchronizeHandle interface
 */
#ifndef __ISynchronizeHandle_INTERFACE_DEFINED__
#define __ISynchronizeHandle_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISynchronizeHandle, 0x00000031, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000031-0000-0000-c000-000000000046")
ISynchronizeHandle : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetHandle(
        HANDLE *ph) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISynchronizeHandle, 0x00000031, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISynchronizeHandleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISynchronizeHandle *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISynchronizeHandle *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISynchronizeHandle *This);

    /*** ISynchronizeHandle methods ***/
    HRESULT (STDMETHODCALLTYPE *GetHandle)(
        ISynchronizeHandle *This,
        HANDLE *ph);

    END_INTERFACE
} ISynchronizeHandleVtbl;

interface ISynchronizeHandle {
    CONST_VTBL ISynchronizeHandleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISynchronizeHandle_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISynchronizeHandle_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISynchronizeHandle_Release(This) (This)->lpVtbl->Release(This)
/*** ISynchronizeHandle methods ***/
#define ISynchronizeHandle_GetHandle(This,ph) (This)->lpVtbl->GetHandle(This,ph)
#else
/*** IUnknown methods ***/
static inline HRESULT ISynchronizeHandle_QueryInterface(ISynchronizeHandle* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISynchronizeHandle_AddRef(ISynchronizeHandle* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISynchronizeHandle_Release(ISynchronizeHandle* This) {
    return This->lpVtbl->Release(This);
}
/*** ISynchronizeHandle methods ***/
static inline HRESULT ISynchronizeHandle_GetHandle(ISynchronizeHandle* This,HANDLE *ph) {
    return This->lpVtbl->GetHandle(This,ph);
}
#endif
#endif

#endif


#endif  /* __ISynchronizeHandle_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISynchronizeEvent interface
 */
#ifndef __ISynchronizeEvent_INTERFACE_DEFINED__
#define __ISynchronizeEvent_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISynchronizeEvent, 0x00000032, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000032-0000-0000-c000-000000000046")
ISynchronizeEvent : public ISynchronizeHandle
{
    virtual HRESULT STDMETHODCALLTYPE SetEventHandle(
        HANDLE *ph) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISynchronizeEvent, 0x00000032, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISynchronizeEventVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISynchronizeEvent *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISynchronizeEvent *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISynchronizeEvent *This);

    /*** ISynchronizeHandle methods ***/
    HRESULT (STDMETHODCALLTYPE *GetHandle)(
        ISynchronizeEvent *This,
        HANDLE *ph);

    /*** ISynchronizeEvent methods ***/
    HRESULT (STDMETHODCALLTYPE *SetEventHandle)(
        ISynchronizeEvent *This,
        HANDLE *ph);

    END_INTERFACE
} ISynchronizeEventVtbl;

interface ISynchronizeEvent {
    CONST_VTBL ISynchronizeEventVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISynchronizeEvent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISynchronizeEvent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISynchronizeEvent_Release(This) (This)->lpVtbl->Release(This)
/*** ISynchronizeHandle methods ***/
#define ISynchronizeEvent_GetHandle(This,ph) (This)->lpVtbl->GetHandle(This,ph)
/*** ISynchronizeEvent methods ***/
#define ISynchronizeEvent_SetEventHandle(This,ph) (This)->lpVtbl->SetEventHandle(This,ph)
#else
/*** IUnknown methods ***/
static inline HRESULT ISynchronizeEvent_QueryInterface(ISynchronizeEvent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISynchronizeEvent_AddRef(ISynchronizeEvent* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISynchronizeEvent_Release(ISynchronizeEvent* This) {
    return This->lpVtbl->Release(This);
}
/*** ISynchronizeHandle methods ***/
static inline HRESULT ISynchronizeEvent_GetHandle(ISynchronizeEvent* This,HANDLE *ph) {
    return This->lpVtbl->GetHandle(This,ph);
}
/*** ISynchronizeEvent methods ***/
static inline HRESULT ISynchronizeEvent_SetEventHandle(ISynchronizeEvent* This,HANDLE *ph) {
    return This->lpVtbl->SetEventHandle(This,ph);
}
#endif
#endif

#endif


#endif  /* __ISynchronizeEvent_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISynchronizeContainer interface
 */
#ifndef __ISynchronizeContainer_INTERFACE_DEFINED__
#define __ISynchronizeContainer_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISynchronizeContainer, 0x00000033, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000033-0000-0000-c000-000000000046")
ISynchronizeContainer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddSynchronize(
        ISynchronize *pSync) = 0;

    virtual HRESULT STDMETHODCALLTYPE WaitMultiple(
        DWORD dwFlags,
        DWORD dwTimeOut,
        ISynchronize **ppSync) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISynchronizeContainer, 0x00000033, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISynchronizeContainerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISynchronizeContainer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISynchronizeContainer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISynchronizeContainer *This);

    /*** ISynchronizeContainer methods ***/
    HRESULT (STDMETHODCALLTYPE *AddSynchronize)(
        ISynchronizeContainer *This,
        ISynchronize *pSync);

    HRESULT (STDMETHODCALLTYPE *WaitMultiple)(
        ISynchronizeContainer *This,
        DWORD dwFlags,
        DWORD dwTimeOut,
        ISynchronize **ppSync);

    END_INTERFACE
} ISynchronizeContainerVtbl;

interface ISynchronizeContainer {
    CONST_VTBL ISynchronizeContainerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISynchronizeContainer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISynchronizeContainer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISynchronizeContainer_Release(This) (This)->lpVtbl->Release(This)
/*** ISynchronizeContainer methods ***/
#define ISynchronizeContainer_AddSynchronize(This,pSync) (This)->lpVtbl->AddSynchronize(This,pSync)
#define ISynchronizeContainer_WaitMultiple(This,dwFlags,dwTimeOut,ppSync) (This)->lpVtbl->WaitMultiple(This,dwFlags,dwTimeOut,ppSync)
#else
/*** IUnknown methods ***/
static inline HRESULT ISynchronizeContainer_QueryInterface(ISynchronizeContainer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISynchronizeContainer_AddRef(ISynchronizeContainer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISynchronizeContainer_Release(ISynchronizeContainer* This) {
    return This->lpVtbl->Release(This);
}
/*** ISynchronizeContainer methods ***/
static inline HRESULT ISynchronizeContainer_AddSynchronize(ISynchronizeContainer* This,ISynchronize *pSync) {
    return This->lpVtbl->AddSynchronize(This,pSync);
}
static inline HRESULT ISynchronizeContainer_WaitMultiple(ISynchronizeContainer* This,DWORD dwFlags,DWORD dwTimeOut,ISynchronize **ppSync) {
    return This->lpVtbl->WaitMultiple(This,dwFlags,dwTimeOut,ppSync);
}
#endif
#endif

#endif


#endif  /* __ISynchronizeContainer_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ISynchronizeMutex interface
 */
#ifndef __ISynchronizeMutex_INTERFACE_DEFINED__
#define __ISynchronizeMutex_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISynchronizeMutex, 0x00000025, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000025-0000-0000-c000-000000000046")
ISynchronizeMutex : public ISynchronize
{
    virtual HRESULT STDMETHODCALLTYPE ReleaseMutex(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISynchronizeMutex, 0x00000025, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ISynchronizeMutexVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISynchronizeMutex *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISynchronizeMutex *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISynchronizeMutex *This);

    /*** ISynchronize methods ***/
    HRESULT (STDMETHODCALLTYPE *Wait)(
        ISynchronizeMutex *This,
        DWORD dwFlags,
        DWORD dwMilliseconds);

    HRESULT (STDMETHODCALLTYPE *Signal)(
        ISynchronizeMutex *This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        ISynchronizeMutex *This);

    /*** ISynchronizeMutex methods ***/
    HRESULT (STDMETHODCALLTYPE *ReleaseMutex)(
        ISynchronizeMutex *This);

    END_INTERFACE
} ISynchronizeMutexVtbl;

interface ISynchronizeMutex {
    CONST_VTBL ISynchronizeMutexVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISynchronizeMutex_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISynchronizeMutex_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISynchronizeMutex_Release(This) (This)->lpVtbl->Release(This)
/*** ISynchronize methods ***/
#define ISynchronizeMutex_Wait(This,dwFlags,dwMilliseconds) (This)->lpVtbl->Wait(This,dwFlags,dwMilliseconds)
#define ISynchronizeMutex_Signal(This) (This)->lpVtbl->Signal(This)
#define ISynchronizeMutex_Reset(This) (This)->lpVtbl->Reset(This)
/*** ISynchronizeMutex methods ***/
#define ISynchronizeMutex_ReleaseMutex(This) (This)->lpVtbl->ReleaseMutex(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ISynchronizeMutex_QueryInterface(ISynchronizeMutex* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISynchronizeMutex_AddRef(ISynchronizeMutex* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISynchronizeMutex_Release(ISynchronizeMutex* This) {
    return This->lpVtbl->Release(This);
}
/*** ISynchronize methods ***/
static inline HRESULT ISynchronizeMutex_Wait(ISynchronizeMutex* This,DWORD dwFlags,DWORD dwMilliseconds) {
    return This->lpVtbl->Wait(This,dwFlags,dwMilliseconds);
}
static inline HRESULT ISynchronizeMutex_Signal(ISynchronizeMutex* This) {
    return This->lpVtbl->Signal(This);
}
static inline HRESULT ISynchronizeMutex_Reset(ISynchronizeMutex* This) {
    return This->lpVtbl->Reset(This);
}
/*** ISynchronizeMutex methods ***/
static inline HRESULT ISynchronizeMutex_ReleaseMutex(ISynchronizeMutex* This) {
    return This->lpVtbl->ReleaseMutex(This);
}
#endif
#endif

#endif


#endif  /* __ISynchronizeMutex_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ICancelMethodCalls interface
 */
#ifndef __ICancelMethodCalls_INTERFACE_DEFINED__
#define __ICancelMethodCalls_INTERFACE_DEFINED__

typedef ICancelMethodCalls *LPCANCELMETHODCALLS;

DEFINE_GUID(IID_ICancelMethodCalls, 0x00000029, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000029-0000-0000-c000-000000000046")
ICancelMethodCalls : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ULONG ulSeconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE TestCancel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICancelMethodCalls, 0x00000029, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct ICancelMethodCallsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICancelMethodCalls *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICancelMethodCalls *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICancelMethodCalls *This);

    /*** ICancelMethodCalls methods ***/
    HRESULT (STDMETHODCALLTYPE *Cancel)(
        ICancelMethodCalls *This,
        ULONG ulSeconds);

    HRESULT (STDMETHODCALLTYPE *TestCancel)(
        ICancelMethodCalls *This);

    END_INTERFACE
} ICancelMethodCallsVtbl;

interface ICancelMethodCalls {
    CONST_VTBL ICancelMethodCallsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICancelMethodCalls_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICancelMethodCalls_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICancelMethodCalls_Release(This) (This)->lpVtbl->Release(This)
/*** ICancelMethodCalls methods ***/
#define ICancelMethodCalls_Cancel(This,ulSeconds) (This)->lpVtbl->Cancel(This,ulSeconds)
#define ICancelMethodCalls_TestCancel(This) (This)->lpVtbl->TestCancel(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ICancelMethodCalls_QueryInterface(ICancelMethodCalls* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICancelMethodCalls_AddRef(ICancelMethodCalls* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICancelMethodCalls_Release(ICancelMethodCalls* This) {
    return This->lpVtbl->Release(This);
}
/*** ICancelMethodCalls methods ***/
static inline HRESULT ICancelMethodCalls_Cancel(ICancelMethodCalls* This,ULONG ulSeconds) {
    return This->lpVtbl->Cancel(This,ulSeconds);
}
static inline HRESULT ICancelMethodCalls_TestCancel(ICancelMethodCalls* This) {
    return This->lpVtbl->TestCancel(This);
}
#endif
#endif

#endif


#endif  /* __ICancelMethodCalls_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAsyncManager interface
 */
#ifndef __IAsyncManager_INTERFACE_DEFINED__
#define __IAsyncManager_INTERFACE_DEFINED__

typedef enum tagDCOM_CALL_STATE {
    DCOM_NONE = 0x0,
    DCOM_CALL_COMPLETE = 0x1,
    DCOM_CALL_CANCELED = 0x2
} DCOM_CALL_STATE;

DEFINE_GUID(IID_IAsyncManager, 0x0000002a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000002a-0000-0000-c000-000000000046")
IAsyncManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CompleteCall(
        HRESULT Result) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCallContext(
        REFIID riid,
        void **pInterface) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetState(
        ULONG *pulStateFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAsyncManager, 0x0000002a, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IAsyncManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAsyncManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAsyncManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAsyncManager *This);

    /*** IAsyncManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CompleteCall)(
        IAsyncManager *This,
        HRESULT Result);

    HRESULT (STDMETHODCALLTYPE *GetCallContext)(
        IAsyncManager *This,
        REFIID riid,
        void **pInterface);

    HRESULT (STDMETHODCALLTYPE *GetState)(
        IAsyncManager *This,
        ULONG *pulStateFlags);

    END_INTERFACE
} IAsyncManagerVtbl;

interface IAsyncManager {
    CONST_VTBL IAsyncManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAsyncManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAsyncManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAsyncManager_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncManager methods ***/
#define IAsyncManager_CompleteCall(This,Result) (This)->lpVtbl->CompleteCall(This,Result)
#define IAsyncManager_GetCallContext(This,riid,pInterface) (This)->lpVtbl->GetCallContext(This,riid,pInterface)
#define IAsyncManager_GetState(This,pulStateFlags) (This)->lpVtbl->GetState(This,pulStateFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IAsyncManager_QueryInterface(IAsyncManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAsyncManager_AddRef(IAsyncManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAsyncManager_Release(IAsyncManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncManager methods ***/
static inline HRESULT IAsyncManager_CompleteCall(IAsyncManager* This,HRESULT Result) {
    return This->lpVtbl->CompleteCall(This,Result);
}
static inline HRESULT IAsyncManager_GetCallContext(IAsyncManager* This,REFIID riid,void **pInterface) {
    return This->lpVtbl->GetCallContext(This,riid,pInterface);
}
static inline HRESULT IAsyncManager_GetState(IAsyncManager* This,ULONG *pulStateFlags) {
    return This->lpVtbl->GetState(This,pulStateFlags);
}
#endif
#endif

#endif


#endif  /* __IAsyncManager_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ICallFactory interface
 */
#ifndef __ICallFactory_INTERFACE_DEFINED__
#define __ICallFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICallFactory, 0x1c733a30, 0x2a1c, 0x11ce, 0xad,0xe5, 0x00,0xaa,0x00,0x44,0x77,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1c733a30-2a1c-11ce-ade5-00aa0044773d")
ICallFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateCall(
        REFIID riid,
        IUnknown *pCtrlUnk,
        REFIID riid2,
        IUnknown **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICallFactory, 0x1c733a30, 0x2a1c, 0x11ce, 0xad,0xe5, 0x00,0xaa,0x00,0x44,0x77,0x3d)
#endif
#else
typedef struct ICallFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICallFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICallFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICallFactory *This);

    /*** ICallFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateCall)(
        ICallFactory *This,
        REFIID riid,
        IUnknown *pCtrlUnk,
        REFIID riid2,
        IUnknown **ppv);

    END_INTERFACE
} ICallFactoryVtbl;

interface ICallFactory {
    CONST_VTBL ICallFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICallFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICallFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICallFactory_Release(This) (This)->lpVtbl->Release(This)
/*** ICallFactory methods ***/
#define ICallFactory_CreateCall(This,riid,pCtrlUnk,riid2,ppv) (This)->lpVtbl->CreateCall(This,riid,pCtrlUnk,riid2,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT ICallFactory_QueryInterface(ICallFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICallFactory_AddRef(ICallFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICallFactory_Release(ICallFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** ICallFactory methods ***/
static inline HRESULT ICallFactory_CreateCall(ICallFactory* This,REFIID riid,IUnknown *pCtrlUnk,REFIID riid2,IUnknown **ppv) {
    return This->lpVtbl->CreateCall(This,riid,pCtrlUnk,riid2,ppv);
}
#endif
#endif

#endif


#endif  /* __ICallFactory_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IRpcHelper interface
 */
#ifndef __IRpcHelper_INTERFACE_DEFINED__
#define __IRpcHelper_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRpcHelper, 0x00000149, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000149-0000-0000-c000-000000000046")
IRpcHelper : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDCOMProtocolVersion(
        DWORD *pComVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIIDFromOBJREF(
        void *pObjRef,
        IID **piid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRpcHelper, 0x00000149, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IRpcHelperVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRpcHelper *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRpcHelper *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRpcHelper *This);

    /*** IRpcHelper methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDCOMProtocolVersion)(
        IRpcHelper *This,
        DWORD *pComVersion);

    HRESULT (STDMETHODCALLTYPE *GetIIDFromOBJREF)(
        IRpcHelper *This,
        void *pObjRef,
        IID **piid);

    END_INTERFACE
} IRpcHelperVtbl;

interface IRpcHelper {
    CONST_VTBL IRpcHelperVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRpcHelper_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRpcHelper_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRpcHelper_Release(This) (This)->lpVtbl->Release(This)
/*** IRpcHelper methods ***/
#define IRpcHelper_GetDCOMProtocolVersion(This,pComVersion) (This)->lpVtbl->GetDCOMProtocolVersion(This,pComVersion)
#define IRpcHelper_GetIIDFromOBJREF(This,pObjRef,piid) (This)->lpVtbl->GetIIDFromOBJREF(This,pObjRef,piid)
#else
/*** IUnknown methods ***/
static inline HRESULT IRpcHelper_QueryInterface(IRpcHelper* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRpcHelper_AddRef(IRpcHelper* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRpcHelper_Release(IRpcHelper* This) {
    return This->lpVtbl->Release(This);
}
/*** IRpcHelper methods ***/
static inline HRESULT IRpcHelper_GetDCOMProtocolVersion(IRpcHelper* This,DWORD *pComVersion) {
    return This->lpVtbl->GetDCOMProtocolVersion(This,pComVersion);
}
static inline HRESULT IRpcHelper_GetIIDFromOBJREF(IRpcHelper* This,void *pObjRef,IID **piid) {
    return This->lpVtbl->GetIIDFromOBJREF(This,pObjRef,piid);
}
#endif
#endif

#endif


#endif  /* __IRpcHelper_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IReleaseMarshalBuffers interface
 */
#ifndef __IReleaseMarshalBuffers_INTERFACE_DEFINED__
#define __IReleaseMarshalBuffers_INTERFACE_DEFINED__

DEFINE_GUID(IID_IReleaseMarshalBuffers, 0xeb0cb9e8, 0x7996, 0x11d2, 0x87,0x2e, 0x00,0x00,0xf8,0x08,0x08,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eb0cb9e8-7996-11d2-872e-0000f8080859")
IReleaseMarshalBuffers : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ReleaseMarshalBuffer(
        RPCOLEMESSAGE *pMsg,
        DWORD dwFlags,
        IUnknown *pChnl) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IReleaseMarshalBuffers, 0xeb0cb9e8, 0x7996, 0x11d2, 0x87,0x2e, 0x00,0x00,0xf8,0x08,0x08,0x59)
#endif
#else
typedef struct IReleaseMarshalBuffersVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IReleaseMarshalBuffers *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IReleaseMarshalBuffers *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IReleaseMarshalBuffers *This);

    /*** IReleaseMarshalBuffers methods ***/
    HRESULT (STDMETHODCALLTYPE *ReleaseMarshalBuffer)(
        IReleaseMarshalBuffers *This,
        RPCOLEMESSAGE *pMsg,
        DWORD dwFlags,
        IUnknown *pChnl);

    END_INTERFACE
} IReleaseMarshalBuffersVtbl;

interface IReleaseMarshalBuffers {
    CONST_VTBL IReleaseMarshalBuffersVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IReleaseMarshalBuffers_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IReleaseMarshalBuffers_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IReleaseMarshalBuffers_Release(This) (This)->lpVtbl->Release(This)
/*** IReleaseMarshalBuffers methods ***/
#define IReleaseMarshalBuffers_ReleaseMarshalBuffer(This,pMsg,dwFlags,pChnl) (This)->lpVtbl->ReleaseMarshalBuffer(This,pMsg,dwFlags,pChnl)
#else
/*** IUnknown methods ***/
static inline HRESULT IReleaseMarshalBuffers_QueryInterface(IReleaseMarshalBuffers* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IReleaseMarshalBuffers_AddRef(IReleaseMarshalBuffers* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IReleaseMarshalBuffers_Release(IReleaseMarshalBuffers* This) {
    return This->lpVtbl->Release(This);
}
/*** IReleaseMarshalBuffers methods ***/
static inline HRESULT IReleaseMarshalBuffers_ReleaseMarshalBuffer(IReleaseMarshalBuffers* This,RPCOLEMESSAGE *pMsg,DWORD dwFlags,IUnknown *pChnl) {
    return This->lpVtbl->ReleaseMarshalBuffer(This,pMsg,dwFlags,pChnl);
}
#endif
#endif

#endif


#endif  /* __IReleaseMarshalBuffers_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IWaitMultiple interface
 */
#ifndef __IWaitMultiple_INTERFACE_DEFINED__
#define __IWaitMultiple_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWaitMultiple, 0x0000002b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0000002b-0000-0000-c000-000000000046")
IWaitMultiple : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE WaitMultiple(
        DWORD timeout,
        ISynchronize **pSync) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddSynchronize(
        ISynchronize *pSync) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWaitMultiple, 0x0000002b, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IWaitMultipleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWaitMultiple *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWaitMultiple *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWaitMultiple *This);

    /*** IWaitMultiple methods ***/
    HRESULT (STDMETHODCALLTYPE *WaitMultiple)(
        IWaitMultiple *This,
        DWORD timeout,
        ISynchronize **pSync);

    HRESULT (STDMETHODCALLTYPE *AddSynchronize)(
        IWaitMultiple *This,
        ISynchronize *pSync);

    END_INTERFACE
} IWaitMultipleVtbl;

interface IWaitMultiple {
    CONST_VTBL IWaitMultipleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWaitMultiple_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWaitMultiple_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWaitMultiple_Release(This) (This)->lpVtbl->Release(This)
/*** IWaitMultiple methods ***/
#define IWaitMultiple_WaitMultiple(This,timeout,pSync) (This)->lpVtbl->WaitMultiple(This,timeout,pSync)
#define IWaitMultiple_AddSynchronize(This,pSync) (This)->lpVtbl->AddSynchronize(This,pSync)
#else
/*** IUnknown methods ***/
static inline HRESULT IWaitMultiple_QueryInterface(IWaitMultiple* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWaitMultiple_AddRef(IWaitMultiple* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWaitMultiple_Release(IWaitMultiple* This) {
    return This->lpVtbl->Release(This);
}
/*** IWaitMultiple methods ***/
static inline HRESULT IWaitMultiple_WaitMultiple(IWaitMultiple* This,DWORD timeout,ISynchronize **pSync) {
    return This->lpVtbl->WaitMultiple(This,timeout,pSync);
}
static inline HRESULT IWaitMultiple_AddSynchronize(IWaitMultiple* This,ISynchronize *pSync) {
    return This->lpVtbl->AddSynchronize(This,pSync);
}
#endif
#endif

#endif


#endif  /* __IWaitMultiple_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAddrTrackingControl interface
 */
#ifndef __IAddrTrackingControl_INTERFACE_DEFINED__
#define __IAddrTrackingControl_INTERFACE_DEFINED__

typedef IAddrTrackingControl *LPADDRTRACKINGCONTROL;
DEFINE_GUID(IID_IAddrTrackingControl, 0x00000147, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000147-0000-0000-c000-000000000046")
IAddrTrackingControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EnableCOMDynamicAddrTracking(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisableCOMDynamicAddrTracking(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAddrTrackingControl, 0x00000147, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IAddrTrackingControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAddrTrackingControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAddrTrackingControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAddrTrackingControl *This);

    /*** IAddrTrackingControl methods ***/
    HRESULT (STDMETHODCALLTYPE *EnableCOMDynamicAddrTracking)(
        IAddrTrackingControl *This);

    HRESULT (STDMETHODCALLTYPE *DisableCOMDynamicAddrTracking)(
        IAddrTrackingControl *This);

    END_INTERFACE
} IAddrTrackingControlVtbl;

interface IAddrTrackingControl {
    CONST_VTBL IAddrTrackingControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAddrTrackingControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAddrTrackingControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAddrTrackingControl_Release(This) (This)->lpVtbl->Release(This)
/*** IAddrTrackingControl methods ***/
#define IAddrTrackingControl_EnableCOMDynamicAddrTracking(This) (This)->lpVtbl->EnableCOMDynamicAddrTracking(This)
#define IAddrTrackingControl_DisableCOMDynamicAddrTracking(This) (This)->lpVtbl->DisableCOMDynamicAddrTracking(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IAddrTrackingControl_QueryInterface(IAddrTrackingControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAddrTrackingControl_AddRef(IAddrTrackingControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAddrTrackingControl_Release(IAddrTrackingControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IAddrTrackingControl methods ***/
static inline HRESULT IAddrTrackingControl_EnableCOMDynamicAddrTracking(IAddrTrackingControl* This) {
    return This->lpVtbl->EnableCOMDynamicAddrTracking(This);
}
static inline HRESULT IAddrTrackingControl_DisableCOMDynamicAddrTracking(IAddrTrackingControl* This) {
    return This->lpVtbl->DisableCOMDynamicAddrTracking(This);
}
#endif
#endif

#endif


#endif  /* __IAddrTrackingControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAddrExclusionControl interface
 */
#ifndef __IAddrExclusionControl_INTERFACE_DEFINED__
#define __IAddrExclusionControl_INTERFACE_DEFINED__

typedef IAddrExclusionControl *LPADDREXCLUSIONCONTROL;
DEFINE_GUID(IID_IAddrExclusionControl, 0x00000148, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000148-0000-0000-c000-000000000046")
IAddrExclusionControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCurrentAddrExclusionList(
        REFIID riid,
        void **ppEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpdateAddrExclusionList(
        IUnknown *pEnumerator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAddrExclusionControl, 0x00000148, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IAddrExclusionControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAddrExclusionControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAddrExclusionControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAddrExclusionControl *This);

    /*** IAddrExclusionControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCurrentAddrExclusionList)(
        IAddrExclusionControl *This,
        REFIID riid,
        void **ppEnumerator);

    HRESULT (STDMETHODCALLTYPE *UpdateAddrExclusionList)(
        IAddrExclusionControl *This,
        IUnknown *pEnumerator);

    END_INTERFACE
} IAddrExclusionControlVtbl;

interface IAddrExclusionControl {
    CONST_VTBL IAddrExclusionControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAddrExclusionControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAddrExclusionControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAddrExclusionControl_Release(This) (This)->lpVtbl->Release(This)
/*** IAddrExclusionControl methods ***/
#define IAddrExclusionControl_GetCurrentAddrExclusionList(This,riid,ppEnumerator) (This)->lpVtbl->GetCurrentAddrExclusionList(This,riid,ppEnumerator)
#define IAddrExclusionControl_UpdateAddrExclusionList(This,pEnumerator) (This)->lpVtbl->UpdateAddrExclusionList(This,pEnumerator)
#else
/*** IUnknown methods ***/
static inline HRESULT IAddrExclusionControl_QueryInterface(IAddrExclusionControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAddrExclusionControl_AddRef(IAddrExclusionControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAddrExclusionControl_Release(IAddrExclusionControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IAddrExclusionControl methods ***/
static inline HRESULT IAddrExclusionControl_GetCurrentAddrExclusionList(IAddrExclusionControl* This,REFIID riid,void **ppEnumerator) {
    return This->lpVtbl->GetCurrentAddrExclusionList(This,riid,ppEnumerator);
}
static inline HRESULT IAddrExclusionControl_UpdateAddrExclusionList(IAddrExclusionControl* This,IUnknown *pEnumerator) {
    return This->lpVtbl->UpdateAddrExclusionList(This,pEnumerator);
}
#endif
#endif

#endif


#endif  /* __IAddrExclusionControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPipeByte interface
 */
#ifndef __IPipeByte_INTERFACE_DEFINED__
#define __IPipeByte_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPipeByte, 0xdb2f3aca, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("db2f3aca-2f86-11d1-8e04-00c04fb9989a")
IPipeByte : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Pull(
        BYTE *buf,
        ULONG cRequest,
        ULONG *pcReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE Push(
        BYTE *buf,
        ULONG cSent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPipeByte, 0xdb2f3aca, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a)
#endif
#else
typedef struct IPipeByteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPipeByte *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPipeByte *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPipeByte *This);

    /*** IPipeByte methods ***/
    HRESULT (STDMETHODCALLTYPE *Pull)(
        IPipeByte *This,
        BYTE *buf,
        ULONG cRequest,
        ULONG *pcReturned);

    HRESULT (STDMETHODCALLTYPE *Push)(
        IPipeByte *This,
        BYTE *buf,
        ULONG cSent);

    END_INTERFACE
} IPipeByteVtbl;

interface IPipeByte {
    CONST_VTBL IPipeByteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPipeByte_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPipeByte_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPipeByte_Release(This) (This)->lpVtbl->Release(This)
/*** IPipeByte methods ***/
#define IPipeByte_Pull(This,buf,cRequest,pcReturned) (This)->lpVtbl->Pull(This,buf,cRequest,pcReturned)
#define IPipeByte_Push(This,buf,cSent) (This)->lpVtbl->Push(This,buf,cSent)
#else
/*** IUnknown methods ***/
static inline HRESULT IPipeByte_QueryInterface(IPipeByte* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPipeByte_AddRef(IPipeByte* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPipeByte_Release(IPipeByte* This) {
    return This->lpVtbl->Release(This);
}
/*** IPipeByte methods ***/
static inline HRESULT IPipeByte_Pull(IPipeByte* This,BYTE *buf,ULONG cRequest,ULONG *pcReturned) {
    return This->lpVtbl->Pull(This,buf,cRequest,pcReturned);
}
static inline HRESULT IPipeByte_Push(IPipeByte* This,BYTE *buf,ULONG cSent) {
    return This->lpVtbl->Push(This,buf,cSent);
}
#endif
#endif

#endif


#endif  /* __IPipeByte_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPipeLong interface
 */
#ifndef __IPipeLong_INTERFACE_DEFINED__
#define __IPipeLong_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPipeLong, 0xdb2f3acc, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("db2f3acc-2f86-11d1-8e04-00c04fb9989a")
IPipeLong : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Pull(
        LONG *buf,
        ULONG cRequest,
        ULONG *pcReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE Push(
        LONG *buf,
        ULONG cSent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPipeLong, 0xdb2f3acc, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a)
#endif
#else
typedef struct IPipeLongVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPipeLong *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPipeLong *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPipeLong *This);

    /*** IPipeLong methods ***/
    HRESULT (STDMETHODCALLTYPE *Pull)(
        IPipeLong *This,
        LONG *buf,
        ULONG cRequest,
        ULONG *pcReturned);

    HRESULT (STDMETHODCALLTYPE *Push)(
        IPipeLong *This,
        LONG *buf,
        ULONG cSent);

    END_INTERFACE
} IPipeLongVtbl;

interface IPipeLong {
    CONST_VTBL IPipeLongVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPipeLong_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPipeLong_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPipeLong_Release(This) (This)->lpVtbl->Release(This)
/*** IPipeLong methods ***/
#define IPipeLong_Pull(This,buf,cRequest,pcReturned) (This)->lpVtbl->Pull(This,buf,cRequest,pcReturned)
#define IPipeLong_Push(This,buf,cSent) (This)->lpVtbl->Push(This,buf,cSent)
#else
/*** IUnknown methods ***/
static inline HRESULT IPipeLong_QueryInterface(IPipeLong* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPipeLong_AddRef(IPipeLong* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPipeLong_Release(IPipeLong* This) {
    return This->lpVtbl->Release(This);
}
/*** IPipeLong methods ***/
static inline HRESULT IPipeLong_Pull(IPipeLong* This,LONG *buf,ULONG cRequest,ULONG *pcReturned) {
    return This->lpVtbl->Pull(This,buf,cRequest,pcReturned);
}
static inline HRESULT IPipeLong_Push(IPipeLong* This,LONG *buf,ULONG cSent) {
    return This->lpVtbl->Push(This,buf,cSent);
}
#endif
#endif

#endif


#endif  /* __IPipeLong_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPipeDouble interface
 */
#ifndef __IPipeDouble_INTERFACE_DEFINED__
#define __IPipeDouble_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPipeDouble, 0xdb2f3ace, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("db2f3ace-2f86-11d1-8e04-00c04fb9989a")
IPipeDouble : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Pull(
        DOUBLE *buf,
        ULONG cRequest,
        ULONG *pcReturned) = 0;

    virtual HRESULT STDMETHODCALLTYPE Push(
        DOUBLE *buf,
        ULONG cSent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPipeDouble, 0xdb2f3ace, 0x2f86, 0x11d1, 0x8e,0x04, 0x00,0xc0,0x4f,0xb9,0x98,0x9a)
#endif
#else
typedef struct IPipeDoubleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPipeDouble *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPipeDouble *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPipeDouble *This);

    /*** IPipeDouble methods ***/
    HRESULT (STDMETHODCALLTYPE *Pull)(
        IPipeDouble *This,
        DOUBLE *buf,
        ULONG cRequest,
        ULONG *pcReturned);

    HRESULT (STDMETHODCALLTYPE *Push)(
        IPipeDouble *This,
        DOUBLE *buf,
        ULONG cSent);

    END_INTERFACE
} IPipeDoubleVtbl;

interface IPipeDouble {
    CONST_VTBL IPipeDoubleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPipeDouble_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPipeDouble_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPipeDouble_Release(This) (This)->lpVtbl->Release(This)
/*** IPipeDouble methods ***/
#define IPipeDouble_Pull(This,buf,cRequest,pcReturned) (This)->lpVtbl->Pull(This,buf,cRequest,pcReturned)
#define IPipeDouble_Push(This,buf,cSent) (This)->lpVtbl->Push(This,buf,cSent)
#else
/*** IUnknown methods ***/
static inline HRESULT IPipeDouble_QueryInterface(IPipeDouble* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPipeDouble_AddRef(IPipeDouble* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPipeDouble_Release(IPipeDouble* This) {
    return This->lpVtbl->Release(This);
}
/*** IPipeDouble methods ***/
static inline HRESULT IPipeDouble_Pull(IPipeDouble* This,DOUBLE *buf,ULONG cRequest,ULONG *pcReturned) {
    return This->lpVtbl->Pull(This,buf,cRequest,pcReturned);
}
static inline HRESULT IPipeDouble_Push(IPipeDouble* This,DOUBLE *buf,ULONG cSent) {
    return This->lpVtbl->Push(This,buf,cSent);
}
#endif
#endif

#endif


#endif  /* __IPipeDouble_INTERFACE_DEFINED__ */


#if defined USE_COM_CONTEXT_DEF || defined BUILDTYPE_COMSVCS || defined _COMBASEAPI_ || defined _OLE32_

typedef DWORD CPFLAGS;

typedef struct tagContextProperty {
    GUID policyId;
    CPFLAGS flags;
    IUnknown *pUnk;
} ContextProperty;

/*****************************************************************************
 * IEnumContextProps interface
 */
#ifndef __IEnumContextProps_INTERFACE_DEFINED__
#define __IEnumContextProps_INTERFACE_DEFINED__

typedef IEnumContextProps *LPENUMCONTEXTPROPS;

DEFINE_GUID(IID_IEnumContextProps, 0x000001c1, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000001c1-0000-0000-c000-000000000046")
IEnumContextProps : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        ContextProperty *pContextProperties,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumContextProps **ppEnumContextProps) = 0;

    virtual HRESULT STDMETHODCALLTYPE Count(
        ULONG *pcelt) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumContextProps, 0x000001c1, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IEnumContextPropsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumContextProps *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumContextProps *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumContextProps *This);

    /*** IEnumContextProps methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumContextProps *This,
        ULONG celt,
        ContextProperty *pContextProperties,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumContextProps *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumContextProps *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumContextProps *This,
        IEnumContextProps **ppEnumContextProps);

    HRESULT (STDMETHODCALLTYPE *Count)(
        IEnumContextProps *This,
        ULONG *pcelt);

    END_INTERFACE
} IEnumContextPropsVtbl;

interface IEnumContextProps {
    CONST_VTBL IEnumContextPropsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumContextProps_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumContextProps_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumContextProps_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumContextProps methods ***/
#define IEnumContextProps_Next(This,celt,pContextProperties,pceltFetched) (This)->lpVtbl->Next(This,celt,pContextProperties,pceltFetched)
#define IEnumContextProps_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumContextProps_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumContextProps_Clone(This,ppEnumContextProps) (This)->lpVtbl->Clone(This,ppEnumContextProps)
#define IEnumContextProps_Count(This,pcelt) (This)->lpVtbl->Count(This,pcelt)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumContextProps_QueryInterface(IEnumContextProps* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumContextProps_AddRef(IEnumContextProps* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumContextProps_Release(IEnumContextProps* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumContextProps methods ***/
static inline HRESULT IEnumContextProps_Next(IEnumContextProps* This,ULONG celt,ContextProperty *pContextProperties,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,pContextProperties,pceltFetched);
}
static inline HRESULT IEnumContextProps_Skip(IEnumContextProps* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumContextProps_Reset(IEnumContextProps* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumContextProps_Clone(IEnumContextProps* This,IEnumContextProps **ppEnumContextProps) {
    return This->lpVtbl->Clone(This,ppEnumContextProps);
}
static inline HRESULT IEnumContextProps_Count(IEnumContextProps* This,ULONG *pcelt) {
    return This->lpVtbl->Count(This,pcelt);
}
#endif
#endif

#endif


#endif  /* __IEnumContextProps_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IContext interface
 */
#ifndef __IContext_INTERFACE_DEFINED__
#define __IContext_INTERFACE_DEFINED__

DEFINE_GUID(IID_IContext, 0x000001c0, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000001c0-0000-0000-c000-000000000046")
IContext : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetProperty(
        REFGUID rpolicyId,
        CPFLAGS flags,
        IUnknown *pUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveProperty(
        REFGUID rPolicyId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        REFGUID rGuid,
        CPFLAGS *pFlags,
        IUnknown **ppUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumContextProps(
        IEnumContextProps **ppEnumContextProps) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IContext, 0x000001c0, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IContext *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IContext *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IContext *This);

    /*** IContext methods ***/
    HRESULT (STDMETHODCALLTYPE *SetProperty)(
        IContext *This,
        REFGUID rpolicyId,
        CPFLAGS flags,
        IUnknown *pUnk);

    HRESULT (STDMETHODCALLTYPE *RemoveProperty)(
        IContext *This,
        REFGUID rPolicyId);

    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IContext *This,
        REFGUID rGuid,
        CPFLAGS *pFlags,
        IUnknown **ppUnk);

    HRESULT (STDMETHODCALLTYPE *EnumContextProps)(
        IContext *This,
        IEnumContextProps **ppEnumContextProps);

    END_INTERFACE
} IContextVtbl;

interface IContext {
    CONST_VTBL IContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IContext_Release(This) (This)->lpVtbl->Release(This)
/*** IContext methods ***/
#define IContext_SetProperty(This,rpolicyId,flags,pUnk) (This)->lpVtbl->SetProperty(This,rpolicyId,flags,pUnk)
#define IContext_RemoveProperty(This,rPolicyId) (This)->lpVtbl->RemoveProperty(This,rPolicyId)
#define IContext_GetProperty(This,rGuid,pFlags,ppUnk) (This)->lpVtbl->GetProperty(This,rGuid,pFlags,ppUnk)
#define IContext_EnumContextProps(This,ppEnumContextProps) (This)->lpVtbl->EnumContextProps(This,ppEnumContextProps)
#else
/*** IUnknown methods ***/
static inline HRESULT IContext_QueryInterface(IContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IContext_AddRef(IContext* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IContext_Release(IContext* This) {
    return This->lpVtbl->Release(This);
}
/*** IContext methods ***/
static inline HRESULT IContext_SetProperty(IContext* This,REFGUID rpolicyId,CPFLAGS flags,IUnknown *pUnk) {
    return This->lpVtbl->SetProperty(This,rpolicyId,flags,pUnk);
}
static inline HRESULT IContext_RemoveProperty(IContext* This,REFGUID rPolicyId) {
    return This->lpVtbl->RemoveProperty(This,rPolicyId);
}
static inline HRESULT IContext_GetProperty(IContext* This,REFGUID rGuid,CPFLAGS *pFlags,IUnknown **ppUnk) {
    return This->lpVtbl->GetProperty(This,rGuid,pFlags,ppUnk);
}
static inline HRESULT IContext_EnumContextProps(IContext* This,IEnumContextProps **ppEnumContextProps) {
    return This->lpVtbl->EnumContextProps(This,ppEnumContextProps);
}
#endif
#endif

#endif


#endif  /* __IContext_INTERFACE_DEFINED__ */

#endif
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef enum _APTTYPEQUALIFIER {
    APTTYPEQUALIFIER_NONE = 0,
    APTTYPEQUALIFIER_IMPLICIT_MTA = 1,
    APTTYPEQUALIFIER_NA_ON_MTA = 2,
    APTTYPEQUALIFIER_NA_ON_STA = 3,
    APTTYPEQUALIFIER_NA_ON_IMPLICIT_MTA = 4,
    APTTYPEQUALIFIER_NA_ON_MAINSTA = 5,
    APTTYPEQUALIFIER_APPLICATION_STA = 6
} APTTYPEQUALIFIER;

typedef enum _APTTYPE {
    APTTYPE_CURRENT = -1,
    APTTYPE_STA = 0,
    APTTYPE_MTA = 1,
    APTTYPE_NA = 2,
    APTTYPE_MAINSTA = 3
} APTTYPE;
#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef enum _THDTYPE {
    THDTYPE_BLOCKMESSAGES = 0,
    THDTYPE_PROCESSMESSAGES = 1
} THDTYPE;

typedef DWORD APARTMENTID;

/*****************************************************************************
 * IComThreadingInfo interface
 */
#ifndef __IComThreadingInfo_INTERFACE_DEFINED__
#define __IComThreadingInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IComThreadingInfo, 0x000001ce, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("000001ce-0000-0000-c000-000000000046")
IComThreadingInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCurrentApartmentType(
        APTTYPE *pAptType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentThreadType(
        THDTYPE *pThreadType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentLogicalThreadId(
        GUID *pguidLogicalThreadId) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentLogicalThreadId(
        REFGUID rguid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IComThreadingInfo, 0x000001ce, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IComThreadingInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IComThreadingInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IComThreadingInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IComThreadingInfo *This);

    /*** IComThreadingInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCurrentApartmentType)(
        IComThreadingInfo *This,
        APTTYPE *pAptType);

    HRESULT (STDMETHODCALLTYPE *GetCurrentThreadType)(
        IComThreadingInfo *This,
        THDTYPE *pThreadType);

    HRESULT (STDMETHODCALLTYPE *GetCurrentLogicalThreadId)(
        IComThreadingInfo *This,
        GUID *pguidLogicalThreadId);

    HRESULT (STDMETHODCALLTYPE *SetCurrentLogicalThreadId)(
        IComThreadingInfo *This,
        REFGUID rguid);

    END_INTERFACE
} IComThreadingInfoVtbl;

interface IComThreadingInfo {
    CONST_VTBL IComThreadingInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IComThreadingInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IComThreadingInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IComThreadingInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IComThreadingInfo methods ***/
#define IComThreadingInfo_GetCurrentApartmentType(This,pAptType) (This)->lpVtbl->GetCurrentApartmentType(This,pAptType)
#define IComThreadingInfo_GetCurrentThreadType(This,pThreadType) (This)->lpVtbl->GetCurrentThreadType(This,pThreadType)
#define IComThreadingInfo_GetCurrentLogicalThreadId(This,pguidLogicalThreadId) (This)->lpVtbl->GetCurrentLogicalThreadId(This,pguidLogicalThreadId)
#define IComThreadingInfo_SetCurrentLogicalThreadId(This,rguid) (This)->lpVtbl->SetCurrentLogicalThreadId(This,rguid)
#else
/*** IUnknown methods ***/
static inline HRESULT IComThreadingInfo_QueryInterface(IComThreadingInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IComThreadingInfo_AddRef(IComThreadingInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IComThreadingInfo_Release(IComThreadingInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IComThreadingInfo methods ***/
static inline HRESULT IComThreadingInfo_GetCurrentApartmentType(IComThreadingInfo* This,APTTYPE *pAptType) {
    return This->lpVtbl->GetCurrentApartmentType(This,pAptType);
}
static inline HRESULT IComThreadingInfo_GetCurrentThreadType(IComThreadingInfo* This,THDTYPE *pThreadType) {
    return This->lpVtbl->GetCurrentThreadType(This,pThreadType);
}
static inline HRESULT IComThreadingInfo_GetCurrentLogicalThreadId(IComThreadingInfo* This,GUID *pguidLogicalThreadId) {
    return This->lpVtbl->GetCurrentLogicalThreadId(This,pguidLogicalThreadId);
}
static inline HRESULT IComThreadingInfo_SetCurrentLogicalThreadId(IComThreadingInfo* This,REFGUID rguid) {
    return This->lpVtbl->SetCurrentLogicalThreadId(This,rguid);
}
#endif
#endif

#endif


#endif  /* __IComThreadingInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IProcessInitControl interface
 */
#ifndef __IProcessInitControl_INTERFACE_DEFINED__
#define __IProcessInitControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IProcessInitControl, 0x72380d55, 0x8d2b, 0x43a3, 0x85,0x13, 0x2b,0x6e,0xf3,0x14,0x34,0xe9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("72380d55-8d2b-43a3-8513-2b6ef31434e9")
IProcessInitControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ResetInitializerTimeout(
        DWORD dwSecondsRemaining) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IProcessInitControl, 0x72380d55, 0x8d2b, 0x43a3, 0x85,0x13, 0x2b,0x6e,0xf3,0x14,0x34,0xe9)
#endif
#else
typedef struct IProcessInitControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IProcessInitControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IProcessInitControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IProcessInitControl *This);

    /*** IProcessInitControl methods ***/
    HRESULT (STDMETHODCALLTYPE *ResetInitializerTimeout)(
        IProcessInitControl *This,
        DWORD dwSecondsRemaining);

    END_INTERFACE
} IProcessInitControlVtbl;

interface IProcessInitControl {
    CONST_VTBL IProcessInitControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IProcessInitControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IProcessInitControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IProcessInitControl_Release(This) (This)->lpVtbl->Release(This)
/*** IProcessInitControl methods ***/
#define IProcessInitControl_ResetInitializerTimeout(This,dwSecondsRemaining) (This)->lpVtbl->ResetInitializerTimeout(This,dwSecondsRemaining)
#else
/*** IUnknown methods ***/
static inline HRESULT IProcessInitControl_QueryInterface(IProcessInitControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IProcessInitControl_AddRef(IProcessInitControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IProcessInitControl_Release(IProcessInitControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IProcessInitControl methods ***/
static inline HRESULT IProcessInitControl_ResetInitializerTimeout(IProcessInitControl* This,DWORD dwSecondsRemaining) {
    return This->lpVtbl->ResetInitializerTimeout(This,dwSecondsRemaining);
}
#endif
#endif

#endif


#endif  /* __IProcessInitControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IFastRundown interface
 */
#ifndef __IFastRundown_INTERFACE_DEFINED__
#define __IFastRundown_INTERFACE_DEFINED__

DEFINE_GUID(IID_IFastRundown, 0x00000040, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000040-0000-0000-c000-000000000046")
IFastRundown : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IFastRundown, 0x00000040, 0x0000, 0x0000, 0xc0,0x00, 0x00,0x00,0x00,0x00,0x00,0x46)
#endif
#else
typedef struct IFastRundownVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IFastRundown *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IFastRundown *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IFastRundown *This);

    END_INTERFACE
} IFastRundownVtbl;

interface IFastRundown {
    CONST_VTBL IFastRundownVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IFastRundown_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IFastRundown_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IFastRundown_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IFastRundown_QueryInterface(IFastRundown* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IFastRundown_AddRef(IFastRundown* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IFastRundown_Release(IFastRundown* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __IFastRundown_INTERFACE_DEFINED__ */


typedef enum CO_MARSHALING_CONTEXT_ATTRIBUTES {
    CO_MARSHALING_SOURCE_IS_APP_CONTAINER = 0
} CO_MARSHALING_CONTEXT_ATTRIBUTES;

/*****************************************************************************
 * IMarshalingStream interface
 */
#ifndef __IMarshalingStream_INTERFACE_DEFINED__
#define __IMarshalingStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMarshalingStream, 0xd8f2f5e6, 0x6102, 0x4863, 0x9f,0x26, 0x38,0x9a,0x46,0x76,0xef,0xde);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d8f2f5e6-6102-4863-9f26-389a4676efde")
IMarshalingStream : public IStream
{
    virtual HRESULT STDMETHODCALLTYPE GetMarshalingContextAttribute(
        CO_MARSHALING_CONTEXT_ATTRIBUTES attribute,
        ULONG_PTR *pAttributeValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMarshalingStream, 0xd8f2f5e6, 0x6102, 0x4863, 0x9f,0x26, 0x38,0x9a,0x46,0x76,0xef,0xde)
#endif
#else
typedef struct IMarshalingStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMarshalingStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMarshalingStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMarshalingStream *This);

    /*** ISequentialStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        IMarshalingStream *This,
        void *pv,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IMarshalingStream *This,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten);

    /*** IStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Seek)(
        IMarshalingStream *This,
        LARGE_INTEGER dlibMove,
        DWORD dwOrigin,
        ULARGE_INTEGER *plibNewPosition);

    HRESULT (STDMETHODCALLTYPE *SetSize)(
        IMarshalingStream *This,
        ULARGE_INTEGER libNewSize);

    HRESULT (STDMETHODCALLTYPE *CopyTo)(
        IMarshalingStream *This,
        IStream *pstm,
        ULARGE_INTEGER cb,
        ULARGE_INTEGER *pcbRead,
        ULARGE_INTEGER *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IMarshalingStream *This,
        DWORD grfCommitFlags);

    HRESULT (STDMETHODCALLTYPE *Revert)(
        IMarshalingStream *This);

    HRESULT (STDMETHODCALLTYPE *LockRegion)(
        IMarshalingStream *This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *UnlockRegion)(
        IMarshalingStream *This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *Stat)(
        IMarshalingStream *This,
        STATSTG *pstatstg,
        DWORD grfStatFlag);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IMarshalingStream *This,
        IStream **ppstm);

    /*** IMarshalingStream methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMarshalingContextAttribute)(
        IMarshalingStream *This,
        CO_MARSHALING_CONTEXT_ATTRIBUTES attribute,
        ULONG_PTR *pAttributeValue);

    END_INTERFACE
} IMarshalingStreamVtbl;

interface IMarshalingStream {
    CONST_VTBL IMarshalingStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMarshalingStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMarshalingStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMarshalingStream_Release(This) (This)->lpVtbl->Release(This)
/*** ISequentialStream methods ***/
#define IMarshalingStream_Read(This,pv,cb,pcbRead) (This)->lpVtbl->Read(This,pv,cb,pcbRead)
#define IMarshalingStream_Write(This,pv,cb,pcbWritten) (This)->lpVtbl->Write(This,pv,cb,pcbWritten)
/*** IStream methods ***/
#define IMarshalingStream_Seek(This,dlibMove,dwOrigin,plibNewPosition) (This)->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition)
#define IMarshalingStream_SetSize(This,libNewSize) (This)->lpVtbl->SetSize(This,libNewSize)
#define IMarshalingStream_CopyTo(This,pstm,cb,pcbRead,pcbWritten) (This)->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten)
#define IMarshalingStream_Commit(This,grfCommitFlags) (This)->lpVtbl->Commit(This,grfCommitFlags)
#define IMarshalingStream_Revert(This) (This)->lpVtbl->Revert(This)
#define IMarshalingStream_LockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->LockRegion(This,libOffset,cb,dwLockType)
#define IMarshalingStream_UnlockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType)
#define IMarshalingStream_Stat(This,pstatstg,grfStatFlag) (This)->lpVtbl->Stat(This,pstatstg,grfStatFlag)
#define IMarshalingStream_Clone(This,ppstm) (This)->lpVtbl->Clone(This,ppstm)
/*** IMarshalingStream methods ***/
#define IMarshalingStream_GetMarshalingContextAttribute(This,attribute,pAttributeValue) (This)->lpVtbl->GetMarshalingContextAttribute(This,attribute,pAttributeValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IMarshalingStream_QueryInterface(IMarshalingStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMarshalingStream_AddRef(IMarshalingStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMarshalingStream_Release(IMarshalingStream* This) {
    return This->lpVtbl->Release(This);
}
/*** ISequentialStream methods ***/
static inline HRESULT IMarshalingStream_Read(IMarshalingStream* This,void *pv,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->Read(This,pv,cb,pcbRead);
}
static inline HRESULT IMarshalingStream_Write(IMarshalingStream* This,const void *pv,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->Write(This,pv,cb,pcbWritten);
}
/*** IStream methods ***/
static inline HRESULT IMarshalingStream_Seek(IMarshalingStream* This,LARGE_INTEGER dlibMove,DWORD dwOrigin,ULARGE_INTEGER *plibNewPosition) {
    return This->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition);
}
static inline HRESULT IMarshalingStream_SetSize(IMarshalingStream* This,ULARGE_INTEGER libNewSize) {
    return This->lpVtbl->SetSize(This,libNewSize);
}
static inline HRESULT IMarshalingStream_CopyTo(IMarshalingStream* This,IStream *pstm,ULARGE_INTEGER cb,ULARGE_INTEGER *pcbRead,ULARGE_INTEGER *pcbWritten) {
    return This->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten);
}
static inline HRESULT IMarshalingStream_Commit(IMarshalingStream* This,DWORD grfCommitFlags) {
    return This->lpVtbl->Commit(This,grfCommitFlags);
}
static inline HRESULT IMarshalingStream_Revert(IMarshalingStream* This) {
    return This->lpVtbl->Revert(This);
}
static inline HRESULT IMarshalingStream_LockRegion(IMarshalingStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->LockRegion(This,libOffset,cb,dwLockType);
}
static inline HRESULT IMarshalingStream_UnlockRegion(IMarshalingStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType);
}
static inline HRESULT IMarshalingStream_Stat(IMarshalingStream* This,STATSTG *pstatstg,DWORD grfStatFlag) {
    return This->lpVtbl->Stat(This,pstatstg,grfStatFlag);
}
static inline HRESULT IMarshalingStream_Clone(IMarshalingStream* This,IStream **ppstm) {
    return This->lpVtbl->Clone(This,ppstm);
}
/*** IMarshalingStream methods ***/
static inline HRESULT IMarshalingStream_GetMarshalingContextAttribute(IMarshalingStream* This,CO_MARSHALING_CONTEXT_ATTRIBUTES attribute,ULONG_PTR *pAttributeValue) {
    return This->lpVtbl->GetMarshalingContextAttribute(This,attribute,pAttributeValue);
}
#endif
#endif

#endif


#endif  /* __IMarshalingStream_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
EXTERN_C const GUID  IID_ICallbackWithNoReentrancyToApplicationSTA;
#endif
#define _OBJIDLBASE_
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __objidlbase_h__ */
