/*** Autogenerated by WIDL 10.12 from include/taskschd.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __taskschd_h__
#define __taskschd_h__

/* Forward declarations */

#ifndef __ITaskService_FWD_DEFINED__
#define __ITaskService_FWD_DEFINED__
typedef interface ITaskService ITaskService;
#ifdef __cplusplus
interface ITaskService;
#endif /* __cplusplus */
#endif

#ifndef __IRegisteredTask_FWD_DEFINED__
#define __IRegisteredTask_FWD_DEFINED__
typedef interface IRegisteredTask IRegisteredTask;
#ifdef __cplusplus
interface IRegisteredTask;
#endif /* __cplusplus */
#endif

#ifndef __IRegisteredTaskCollection_FWD_DEFINED__
#define __IRegisteredTaskCollection_FWD_DEFINED__
typedef interface IRegisteredTaskCollection IRegisteredTaskCollection;
#ifdef __cplusplus
interface IRegisteredTaskCollection;
#endif /* __cplusplus */
#endif

#ifndef __IRegistrationInfo_FWD_DEFINED__
#define __IRegistrationInfo_FWD_DEFINED__
typedef interface IRegistrationInfo IRegistrationInfo;
#ifdef __cplusplus
interface IRegistrationInfo;
#endif /* __cplusplus */
#endif

#ifndef __ITaskFolder_FWD_DEFINED__
#define __ITaskFolder_FWD_DEFINED__
typedef interface ITaskFolder ITaskFolder;
#ifdef __cplusplus
interface ITaskFolder;
#endif /* __cplusplus */
#endif

#ifndef __ITaskFolderCollection_FWD_DEFINED__
#define __ITaskFolderCollection_FWD_DEFINED__
typedef interface ITaskFolderCollection ITaskFolderCollection;
#ifdef __cplusplus
interface ITaskFolderCollection;
#endif /* __cplusplus */
#endif

#ifndef __ITaskDefinition_FWD_DEFINED__
#define __ITaskDefinition_FWD_DEFINED__
typedef interface ITaskDefinition ITaskDefinition;
#ifdef __cplusplus
interface ITaskDefinition;
#endif /* __cplusplus */
#endif

#ifndef __ITaskSettings_FWD_DEFINED__
#define __ITaskSettings_FWD_DEFINED__
typedef interface ITaskSettings ITaskSettings;
#ifdef __cplusplus
interface ITaskSettings;
#endif /* __cplusplus */
#endif

#ifndef __IIdleSettings_FWD_DEFINED__
#define __IIdleSettings_FWD_DEFINED__
typedef interface IIdleSettings IIdleSettings;
#ifdef __cplusplus
interface IIdleSettings;
#endif /* __cplusplus */
#endif

#ifndef __IRunningTask_FWD_DEFINED__
#define __IRunningTask_FWD_DEFINED__
typedef interface IRunningTask IRunningTask;
#ifdef __cplusplus
interface IRunningTask;
#endif /* __cplusplus */
#endif

#ifndef __IRunningTaskCollection_FWD_DEFINED__
#define __IRunningTaskCollection_FWD_DEFINED__
typedef interface IRunningTaskCollection IRunningTaskCollection;
#ifdef __cplusplus
interface IRunningTaskCollection;
#endif /* __cplusplus */
#endif

#ifndef __ITaskNamedValuePair_FWD_DEFINED__
#define __ITaskNamedValuePair_FWD_DEFINED__
typedef interface ITaskNamedValuePair ITaskNamedValuePair;
#ifdef __cplusplus
interface ITaskNamedValuePair;
#endif /* __cplusplus */
#endif

#ifndef __ITaskNamedValueCollection_FWD_DEFINED__
#define __ITaskNamedValueCollection_FWD_DEFINED__
typedef interface ITaskNamedValueCollection ITaskNamedValueCollection;
#ifdef __cplusplus
interface ITaskNamedValueCollection;
#endif /* __cplusplus */
#endif

#ifndef __ITrigger_FWD_DEFINED__
#define __ITrigger_FWD_DEFINED__
typedef interface ITrigger ITrigger;
#ifdef __cplusplus
interface ITrigger;
#endif /* __cplusplus */
#endif

#ifndef __IIdleTrigger_FWD_DEFINED__
#define __IIdleTrigger_FWD_DEFINED__
typedef interface IIdleTrigger IIdleTrigger;
#ifdef __cplusplus
interface IIdleTrigger;
#endif /* __cplusplus */
#endif

#ifndef __ILogonTrigger_FWD_DEFINED__
#define __ILogonTrigger_FWD_DEFINED__
typedef interface ILogonTrigger ILogonTrigger;
#ifdef __cplusplus
interface ILogonTrigger;
#endif /* __cplusplus */
#endif

#ifndef __ISessionStateChangeTrigger_FWD_DEFINED__
#define __ISessionStateChangeTrigger_FWD_DEFINED__
typedef interface ISessionStateChangeTrigger ISessionStateChangeTrigger;
#ifdef __cplusplus
interface ISessionStateChangeTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IEventTrigger_FWD_DEFINED__
#define __IEventTrigger_FWD_DEFINED__
typedef interface IEventTrigger IEventTrigger;
#ifdef __cplusplus
interface IEventTrigger;
#endif /* __cplusplus */
#endif

#ifndef __ITimeTrigger_FWD_DEFINED__
#define __ITimeTrigger_FWD_DEFINED__
typedef interface ITimeTrigger ITimeTrigger;
#ifdef __cplusplus
interface ITimeTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IDailyTrigger_FWD_DEFINED__
#define __IDailyTrigger_FWD_DEFINED__
typedef interface IDailyTrigger IDailyTrigger;
#ifdef __cplusplus
interface IDailyTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IWeeklyTrigger_FWD_DEFINED__
#define __IWeeklyTrigger_FWD_DEFINED__
typedef interface IWeeklyTrigger IWeeklyTrigger;
#ifdef __cplusplus
interface IWeeklyTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IMonthlyTrigger_FWD_DEFINED__
#define __IMonthlyTrigger_FWD_DEFINED__
typedef interface IMonthlyTrigger IMonthlyTrigger;
#ifdef __cplusplus
interface IMonthlyTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IMonthlyDOWTrigger_FWD_DEFINED__
#define __IMonthlyDOWTrigger_FWD_DEFINED__
typedef interface IMonthlyDOWTrigger IMonthlyDOWTrigger;
#ifdef __cplusplus
interface IMonthlyDOWTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IBootTrigger_FWD_DEFINED__
#define __IBootTrigger_FWD_DEFINED__
typedef interface IBootTrigger IBootTrigger;
#ifdef __cplusplus
interface IBootTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IRegistrationTrigger_FWD_DEFINED__
#define __IRegistrationTrigger_FWD_DEFINED__
typedef interface IRegistrationTrigger IRegistrationTrigger;
#ifdef __cplusplus
interface IRegistrationTrigger;
#endif /* __cplusplus */
#endif

#ifndef __ITriggerCollection_FWD_DEFINED__
#define __ITriggerCollection_FWD_DEFINED__
typedef interface ITriggerCollection ITriggerCollection;
#ifdef __cplusplus
interface ITriggerCollection;
#endif /* __cplusplus */
#endif

#ifndef __IRepetitionPattern_FWD_DEFINED__
#define __IRepetitionPattern_FWD_DEFINED__
typedef interface IRepetitionPattern IRepetitionPattern;
#ifdef __cplusplus
interface IRepetitionPattern;
#endif /* __cplusplus */
#endif

#ifndef __IAction_FWD_DEFINED__
#define __IAction_FWD_DEFINED__
typedef interface IAction IAction;
#ifdef __cplusplus
interface IAction;
#endif /* __cplusplus */
#endif

#ifndef __IActionCollection_FWD_DEFINED__
#define __IActionCollection_FWD_DEFINED__
typedef interface IActionCollection IActionCollection;
#ifdef __cplusplus
interface IActionCollection;
#endif /* __cplusplus */
#endif

#ifndef __IExecAction_FWD_DEFINED__
#define __IExecAction_FWD_DEFINED__
typedef interface IExecAction IExecAction;
#ifdef __cplusplus
interface IExecAction;
#endif /* __cplusplus */
#endif

#ifndef __INetworkSettings_FWD_DEFINED__
#define __INetworkSettings_FWD_DEFINED__
typedef interface INetworkSettings INetworkSettings;
#ifdef __cplusplus
interface INetworkSettings;
#endif /* __cplusplus */
#endif

#ifndef __IPrincipal_FWD_DEFINED__
#define __IPrincipal_FWD_DEFINED__
typedef interface IPrincipal IPrincipal;
#ifdef __cplusplus
interface IPrincipal;
#endif /* __cplusplus */
#endif

#ifndef __TaskScheduler_FWD_DEFINED__
#define __TaskScheduler_FWD_DEFINED__
#ifdef __cplusplus
typedef class TaskScheduler TaskScheduler;
#else
typedef struct TaskScheduler TaskScheduler;
#endif /* defined __cplusplus */
#endif /* defined __TaskScheduler_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __TaskScheduler_LIBRARY_DEFINED__
#define __TaskScheduler_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_TaskScheduler, 0xe34cb9f1, 0xc7f7, 0x424c, 0xbe,0x29, 0x02,0x7d,0xcc,0x09,0x36,0x3a);

typedef enum _TASK_STATE {
    TASK_STATE_UNKNOWN = 0,
    TASK_STATE_DISABLED = 1,
    TASK_STATE_QUEUED = 2,
    TASK_STATE_READY = 3,
    TASK_STATE_RUNNING = 4
} TASK_STATE;
typedef enum _TASK_ENUM_FLAGS {
    TASK_ENUM_HIDDEN = 0x1
} TASK_ENUM_FLAGS;
typedef enum _TASK_LOGON_TYPE {
    TASK_LOGON_NONE = 0,
    TASK_LOGON_PASSWORD = 1,
    TASK_LOGON_S4U = 2,
    TASK_LOGON_INTERACTIVE_TOKEN = 3,
    TASK_LOGON_GROUP = 4,
    TASK_LOGON_SERVICE_ACCOUNT = 5,
    TASK_LOGON_INTERACTIVE_TOKEN_OR_PASSWORD = 6
} TASK_LOGON_TYPE;
typedef enum _TASK_RUNLEVEL {
    TASK_RUNLEVEL_LUA = 0,
    TASK_RUNLEVEL_HIGHEST = 1
} TASK_RUNLEVEL_TYPE;
typedef enum _TASK_TRIGGER_TYPE2 {
    TASK_TRIGGER_EVENT = 0,
    TASK_TRIGGER_TIME = 1,
    TASK_TRIGGER_DAILY = 2,
    TASK_TRIGGER_WEEKLY = 3,
    TASK_TRIGGER_MONTHLY = 4,
    TASK_TRIGGER_MONTHLYDOW = 5,
    TASK_TRIGGER_IDLE = 6,
    TASK_TRIGGER_REGISTRATION = 7,
    TASK_TRIGGER_BOOT = 8,
    TASK_TRIGGER_LOGON = 9,
    TASK_TRIGGER_SESSION_STATE_CHANGE = 11,
    TASK_TRIGGER_CUSTOM_TRIGGER_01 = 12
} TASK_TRIGGER_TYPE2;
typedef enum _TASK_SESSION_STATE_CHANGE_TYPE {
    TASK_CONSOLE_CONNECT = 1,
    TASK_CONSOLE_DISCONNECT = 2,
    TASK_REMOTE_CONNECT = 3,
    TASK_REMOTE_DISCONNECT = 4,
    TASK_SESSION_LOCK = 7,
    TASK_SESSION_UNLOCK = 8
} TASK_SESSION_STATE_CHANGE_TYPE;
typedef enum _TASK_ACTION_TYPE {
    TASK_ACTION_EXEC = 0,
    TASK_ACTION_COM_HANDLER = 5,
    TASK_ACTION_SEND_EMAIL = 6,
    TASK_ACTION_SHOW_MESSAGE = 7
} TASK_ACTION_TYPE;
typedef enum _TASK_INSTANCES_POLICY {
    TASK_INSTANCES_PARALLEL = 0,
    TASK_INSTANCES_QUEUE = 1,
    TASK_INSTANCES_IGNORE_NEW = 2,
    TASK_INSTANCES_STOP_EXISTING = 3
} TASK_INSTANCES_POLICY;
typedef enum _TASK_COMPATIBILITY {
    TASK_COMPATIBILITY_AT = 0,
    TASK_COMPATIBILITY_V1 = 1,
    TASK_COMPATIBILITY_V2 = 2,
    TASK_COMPATIBILITY_V2_1 = 3,
    TASK_COMPATIBILITY_V2_2 = 4,
    TASK_COMPATIBILITY_V2_3 = 5,
    TASK_COMPATIBILITY_V2_4 = 6
} TASK_COMPATIBILITY;
typedef enum _TASK_CREATION {
    TASK_VALIDATE_ONLY = 1,
    TASK_CREATE = 2,
    TASK_UPDATE = 4,
    TASK_CREATE_OR_UPDATE = 6,
    TASK_DISABLE = 8,
    TASK_DONT_ADD_PRINCIPAL_ACE = 16,
    TASK_IGNORE_REGISTRATION_TRIGGERS = 32
} TASK_CREATION;
#ifndef __ITaskService_FWD_DEFINED__
#define __ITaskService_FWD_DEFINED__
typedef interface ITaskService ITaskService;
#ifdef __cplusplus
interface ITaskService;
#endif /* __cplusplus */
#endif

#ifndef __IRegisteredTask_FWD_DEFINED__
#define __IRegisteredTask_FWD_DEFINED__
typedef interface IRegisteredTask IRegisteredTask;
#ifdef __cplusplus
interface IRegisteredTask;
#endif /* __cplusplus */
#endif

#ifndef __IRegisteredTaskCollection_FWD_DEFINED__
#define __IRegisteredTaskCollection_FWD_DEFINED__
typedef interface IRegisteredTaskCollection IRegisteredTaskCollection;
#ifdef __cplusplus
interface IRegisteredTaskCollection;
#endif /* __cplusplus */
#endif

#ifndef __IRegistrationInfo_FWD_DEFINED__
#define __IRegistrationInfo_FWD_DEFINED__
typedef interface IRegistrationInfo IRegistrationInfo;
#ifdef __cplusplus
interface IRegistrationInfo;
#endif /* __cplusplus */
#endif

#ifndef __ITaskFolder_FWD_DEFINED__
#define __ITaskFolder_FWD_DEFINED__
typedef interface ITaskFolder ITaskFolder;
#ifdef __cplusplus
interface ITaskFolder;
#endif /* __cplusplus */
#endif

#ifndef __ITaskFolderCollection_FWD_DEFINED__
#define __ITaskFolderCollection_FWD_DEFINED__
typedef interface ITaskFolderCollection ITaskFolderCollection;
#ifdef __cplusplus
interface ITaskFolderCollection;
#endif /* __cplusplus */
#endif

#ifndef __ITaskDefinition_FWD_DEFINED__
#define __ITaskDefinition_FWD_DEFINED__
typedef interface ITaskDefinition ITaskDefinition;
#ifdef __cplusplus
interface ITaskDefinition;
#endif /* __cplusplus */
#endif

#ifndef __ITaskSettings_FWD_DEFINED__
#define __ITaskSettings_FWD_DEFINED__
typedef interface ITaskSettings ITaskSettings;
#ifdef __cplusplus
interface ITaskSettings;
#endif /* __cplusplus */
#endif

#ifndef __IIdleSettings_FWD_DEFINED__
#define __IIdleSettings_FWD_DEFINED__
typedef interface IIdleSettings IIdleSettings;
#ifdef __cplusplus
interface IIdleSettings;
#endif /* __cplusplus */
#endif

#ifndef __IRunningTask_FWD_DEFINED__
#define __IRunningTask_FWD_DEFINED__
typedef interface IRunningTask IRunningTask;
#ifdef __cplusplus
interface IRunningTask;
#endif /* __cplusplus */
#endif

#ifndef __IRunningTaskCollection_FWD_DEFINED__
#define __IRunningTaskCollection_FWD_DEFINED__
typedef interface IRunningTaskCollection IRunningTaskCollection;
#ifdef __cplusplus
interface IRunningTaskCollection;
#endif /* __cplusplus */
#endif

#ifndef __ITaskNamedValuePair_FWD_DEFINED__
#define __ITaskNamedValuePair_FWD_DEFINED__
typedef interface ITaskNamedValuePair ITaskNamedValuePair;
#ifdef __cplusplus
interface ITaskNamedValuePair;
#endif /* __cplusplus */
#endif

#ifndef __ITaskNamedValueCollection_FWD_DEFINED__
#define __ITaskNamedValueCollection_FWD_DEFINED__
typedef interface ITaskNamedValueCollection ITaskNamedValueCollection;
#ifdef __cplusplus
interface ITaskNamedValueCollection;
#endif /* __cplusplus */
#endif

#ifndef __ITrigger_FWD_DEFINED__
#define __ITrigger_FWD_DEFINED__
typedef interface ITrigger ITrigger;
#ifdef __cplusplus
interface ITrigger;
#endif /* __cplusplus */
#endif

#ifndef __ITriggerCollection_FWD_DEFINED__
#define __ITriggerCollection_FWD_DEFINED__
typedef interface ITriggerCollection ITriggerCollection;
#ifdef __cplusplus
interface ITriggerCollection;
#endif /* __cplusplus */
#endif

#ifndef __IIdleTrigger_FWD_DEFINED__
#define __IIdleTrigger_FWD_DEFINED__
typedef interface IIdleTrigger IIdleTrigger;
#ifdef __cplusplus
interface IIdleTrigger;
#endif /* __cplusplus */
#endif

#ifndef __ILogonTrigger_FWD_DEFINED__
#define __ILogonTrigger_FWD_DEFINED__
typedef interface ILogonTrigger ILogonTrigger;
#ifdef __cplusplus
interface ILogonTrigger;
#endif /* __cplusplus */
#endif

#ifndef __ISessionStateChangeTrigger_FWD_DEFINED__
#define __ISessionStateChangeTrigger_FWD_DEFINED__
typedef interface ISessionStateChangeTrigger ISessionStateChangeTrigger;
#ifdef __cplusplus
interface ISessionStateChangeTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IEventTrigger_FWD_DEFINED__
#define __IEventTrigger_FWD_DEFINED__
typedef interface IEventTrigger IEventTrigger;
#ifdef __cplusplus
interface IEventTrigger;
#endif /* __cplusplus */
#endif

#ifndef __ITimeTrigger_FWD_DEFINED__
#define __ITimeTrigger_FWD_DEFINED__
typedef interface ITimeTrigger ITimeTrigger;
#ifdef __cplusplus
interface ITimeTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IDailyTrigger_FWD_DEFINED__
#define __IDailyTrigger_FWD_DEFINED__
typedef interface IDailyTrigger IDailyTrigger;
#ifdef __cplusplus
interface IDailyTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IWeeklyTrigger_FWD_DEFINED__
#define __IWeeklyTrigger_FWD_DEFINED__
typedef interface IWeeklyTrigger IWeeklyTrigger;
#ifdef __cplusplus
interface IWeeklyTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IMonthlyTrigger_FWD_DEFINED__
#define __IMonthlyTrigger_FWD_DEFINED__
typedef interface IMonthlyTrigger IMonthlyTrigger;
#ifdef __cplusplus
interface IMonthlyTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IMonthlyDOWTrigger_FWD_DEFINED__
#define __IMonthlyDOWTrigger_FWD_DEFINED__
typedef interface IMonthlyDOWTrigger IMonthlyDOWTrigger;
#ifdef __cplusplus
interface IMonthlyDOWTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IBootTrigger_FWD_DEFINED__
#define __IBootTrigger_FWD_DEFINED__
typedef interface IBootTrigger IBootTrigger;
#ifdef __cplusplus
interface IBootTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IRegistrationTrigger_FWD_DEFINED__
#define __IRegistrationTrigger_FWD_DEFINED__
typedef interface IRegistrationTrigger IRegistrationTrigger;
#ifdef __cplusplus
interface IRegistrationTrigger;
#endif /* __cplusplus */
#endif

#ifndef __IRepetitionPattern_FWD_DEFINED__
#define __IRepetitionPattern_FWD_DEFINED__
typedef interface IRepetitionPattern IRepetitionPattern;
#ifdef __cplusplus
interface IRepetitionPattern;
#endif /* __cplusplus */
#endif

#ifndef __IAction_FWD_DEFINED__
#define __IAction_FWD_DEFINED__
typedef interface IAction IAction;
#ifdef __cplusplus
interface IAction;
#endif /* __cplusplus */
#endif

#ifndef __IActionCollection_FWD_DEFINED__
#define __IActionCollection_FWD_DEFINED__
typedef interface IActionCollection IActionCollection;
#ifdef __cplusplus
interface IActionCollection;
#endif /* __cplusplus */
#endif

#ifndef __IExecAction_FWD_DEFINED__
#define __IExecAction_FWD_DEFINED__
typedef interface IExecAction IExecAction;
#ifdef __cplusplus
interface IExecAction;
#endif /* __cplusplus */
#endif

#ifndef __INetworkSettings_FWD_DEFINED__
#define __INetworkSettings_FWD_DEFINED__
typedef interface INetworkSettings INetworkSettings;
#ifdef __cplusplus
interface INetworkSettings;
#endif /* __cplusplus */
#endif

#ifndef __IPrincipal_FWD_DEFINED__
#define __IPrincipal_FWD_DEFINED__
typedef interface IPrincipal IPrincipal;
#ifdef __cplusplus
interface IPrincipal;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ITaskService interface
 */
#ifndef __ITaskService_INTERFACE_DEFINED__
#define __ITaskService_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITaskService, 0x2faba4c7, 0x4da9, 0x4013, 0x96,0x97, 0x20,0xcc,0x3f,0xd4,0x0f,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2faba4c7-4da9-4013-9697-20cc3fd40f85")
ITaskService : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE GetFolder(
        BSTR path,
        ITaskFolder **folder) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRunningTasks(
        LONG flags,
        IRunningTaskCollection **tasks) = 0;

    virtual HRESULT STDMETHODCALLTYPE NewTask(
        DWORD flags,
        ITaskDefinition **definition) = 0;

    virtual HRESULT STDMETHODCALLTYPE Connect(
        VARIANT server,
        VARIANT user,
        VARIANT domain,
        VARIANT password) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Connected(
        VARIANT_BOOL *connected) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TargetServer(
        BSTR *server) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ConnectedUser(
        BSTR *user) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ConnectedDomain(
        BSTR *domain) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HighestVersion(
        DWORD *version) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITaskService, 0x2faba4c7, 0x4da9, 0x4013, 0x96,0x97, 0x20,0xcc,0x3f,0xd4,0x0f,0x85)
#endif
#else
typedef struct ITaskServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITaskService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITaskService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITaskService *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITaskService *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITaskService *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITaskService *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITaskService *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITaskService methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFolder)(
        ITaskService *This,
        BSTR path,
        ITaskFolder **folder);

    HRESULT (STDMETHODCALLTYPE *GetRunningTasks)(
        ITaskService *This,
        LONG flags,
        IRunningTaskCollection **tasks);

    HRESULT (STDMETHODCALLTYPE *NewTask)(
        ITaskService *This,
        DWORD flags,
        ITaskDefinition **definition);

    HRESULT (STDMETHODCALLTYPE *Connect)(
        ITaskService *This,
        VARIANT server,
        VARIANT user,
        VARIANT domain,
        VARIANT password);

    HRESULT (STDMETHODCALLTYPE *get_Connected)(
        ITaskService *This,
        VARIANT_BOOL *connected);

    HRESULT (STDMETHODCALLTYPE *get_TargetServer)(
        ITaskService *This,
        BSTR *server);

    HRESULT (STDMETHODCALLTYPE *get_ConnectedUser)(
        ITaskService *This,
        BSTR *user);

    HRESULT (STDMETHODCALLTYPE *get_ConnectedDomain)(
        ITaskService *This,
        BSTR *domain);

    HRESULT (STDMETHODCALLTYPE *get_HighestVersion)(
        ITaskService *This,
        DWORD *version);

    END_INTERFACE
} ITaskServiceVtbl;

interface ITaskService {
    CONST_VTBL ITaskServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITaskService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITaskService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITaskService_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ITaskService_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ITaskService_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ITaskService_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ITaskService_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITaskService methods ***/
#define ITaskService_GetFolder(This,path,folder) (This)->lpVtbl->GetFolder(This,path,folder)
#define ITaskService_GetRunningTasks(This,flags,tasks) (This)->lpVtbl->GetRunningTasks(This,flags,tasks)
#define ITaskService_NewTask(This,flags,definition) (This)->lpVtbl->NewTask(This,flags,definition)
#define ITaskService_Connect(This,server,user,domain,password) (This)->lpVtbl->Connect(This,server,user,domain,password)
#define ITaskService_get_Connected(This,connected) (This)->lpVtbl->get_Connected(This,connected)
#define ITaskService_get_TargetServer(This,server) (This)->lpVtbl->get_TargetServer(This,server)
#define ITaskService_get_ConnectedUser(This,user) (This)->lpVtbl->get_ConnectedUser(This,user)
#define ITaskService_get_ConnectedDomain(This,domain) (This)->lpVtbl->get_ConnectedDomain(This,domain)
#define ITaskService_get_HighestVersion(This,version) (This)->lpVtbl->get_HighestVersion(This,version)
#else
/*** IUnknown methods ***/
static inline HRESULT ITaskService_QueryInterface(ITaskService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITaskService_AddRef(ITaskService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITaskService_Release(ITaskService* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ITaskService_GetTypeInfoCount(ITaskService* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ITaskService_GetTypeInfo(ITaskService* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ITaskService_GetIDsOfNames(ITaskService* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ITaskService_Invoke(ITaskService* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITaskService methods ***/
static inline HRESULT ITaskService_GetFolder(ITaskService* This,BSTR path,ITaskFolder **folder) {
    return This->lpVtbl->GetFolder(This,path,folder);
}
static inline HRESULT ITaskService_GetRunningTasks(ITaskService* This,LONG flags,IRunningTaskCollection **tasks) {
    return This->lpVtbl->GetRunningTasks(This,flags,tasks);
}
static inline HRESULT ITaskService_NewTask(ITaskService* This,DWORD flags,ITaskDefinition **definition) {
    return This->lpVtbl->NewTask(This,flags,definition);
}
static inline HRESULT ITaskService_Connect(ITaskService* This,VARIANT server,VARIANT user,VARIANT domain,VARIANT password) {
    return This->lpVtbl->Connect(This,server,user,domain,password);
}
static inline HRESULT ITaskService_get_Connected(ITaskService* This,VARIANT_BOOL *connected) {
    return This->lpVtbl->get_Connected(This,connected);
}
static inline HRESULT ITaskService_get_TargetServer(ITaskService* This,BSTR *server) {
    return This->lpVtbl->get_TargetServer(This,server);
}
static inline HRESULT ITaskService_get_ConnectedUser(ITaskService* This,BSTR *user) {
    return This->lpVtbl->get_ConnectedUser(This,user);
}
static inline HRESULT ITaskService_get_ConnectedDomain(ITaskService* This,BSTR *domain) {
    return This->lpVtbl->get_ConnectedDomain(This,domain);
}
static inline HRESULT ITaskService_get_HighestVersion(ITaskService* This,DWORD *version) {
    return This->lpVtbl->get_HighestVersion(This,version);
}
#endif
#endif

#endif


#endif  /* __ITaskService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRegisteredTask interface
 */
#ifndef __IRegisteredTask_INTERFACE_DEFINED__
#define __IRegisteredTask_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRegisteredTask, 0x9c86f320, 0xdee3, 0x4dd1, 0xb9,0x72, 0xa3,0x03,0xf2,0x6b,0x06,0x1e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9c86f320-dee3-4dd1-b972-a303f26b061e")
IRegisteredTask : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Path(
        BSTR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_State(
        TASK_STATE *state) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE Run(
        VARIANT params,
        IRunningTask **task) = 0;

    virtual HRESULT STDMETHODCALLTYPE RunEx(
        VARIANT params,
        LONG flags,
        LONG sessionID,
        BSTR user,
        IRunningTask **task) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInstances(
        LONG flags,
        IRunningTaskCollection **tasks) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LastRunTime(
        DATE *date) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LastTaskResult(
        LONG *result) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NumberOfMissedRuns(
        LONG *runs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NextRunTime(
        DATE *date) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Definition(
        ITaskDefinition **task) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Xml(
        BSTR *xml) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSecurityDescriptor(
        LONG info,
        BSTR *sddl) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSecurityDescriptor(
        BSTR sddl,
        LONG flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        LONG flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRunTimes(
        const LPSYSTEMTIME start,
        const LPSYSTEMTIME end,
        DWORD *count,
        LPSYSTEMTIME *time) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRegisteredTask, 0x9c86f320, 0xdee3, 0x4dd1, 0xb9,0x72, 0xa3,0x03,0xf2,0x6b,0x06,0x1e)
#endif
#else
typedef struct IRegisteredTaskVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRegisteredTask *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRegisteredTask *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRegisteredTask *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRegisteredTask *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRegisteredTask *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRegisteredTask *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRegisteredTask *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRegisteredTask methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IRegisteredTask *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IRegisteredTask *This,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *get_State)(
        IRegisteredTask *This,
        TASK_STATE *state);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IRegisteredTask *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IRegisteredTask *This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *Run)(
        IRegisteredTask *This,
        VARIANT params,
        IRunningTask **task);

    HRESULT (STDMETHODCALLTYPE *RunEx)(
        IRegisteredTask *This,
        VARIANT params,
        LONG flags,
        LONG sessionID,
        BSTR user,
        IRunningTask **task);

    HRESULT (STDMETHODCALLTYPE *GetInstances)(
        IRegisteredTask *This,
        LONG flags,
        IRunningTaskCollection **tasks);

    HRESULT (STDMETHODCALLTYPE *get_LastRunTime)(
        IRegisteredTask *This,
        DATE *date);

    HRESULT (STDMETHODCALLTYPE *get_LastTaskResult)(
        IRegisteredTask *This,
        LONG *result);

    HRESULT (STDMETHODCALLTYPE *get_NumberOfMissedRuns)(
        IRegisteredTask *This,
        LONG *runs);

    HRESULT (STDMETHODCALLTYPE *get_NextRunTime)(
        IRegisteredTask *This,
        DATE *date);

    HRESULT (STDMETHODCALLTYPE *get_Definition)(
        IRegisteredTask *This,
        ITaskDefinition **task);

    HRESULT (STDMETHODCALLTYPE *get_Xml)(
        IRegisteredTask *This,
        BSTR *xml);

    HRESULT (STDMETHODCALLTYPE *GetSecurityDescriptor)(
        IRegisteredTask *This,
        LONG info,
        BSTR *sddl);

    HRESULT (STDMETHODCALLTYPE *SetSecurityDescriptor)(
        IRegisteredTask *This,
        BSTR sddl,
        LONG flags);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IRegisteredTask *This,
        LONG flags);

    HRESULT (STDMETHODCALLTYPE *GetRunTimes)(
        IRegisteredTask *This,
        const LPSYSTEMTIME start,
        const LPSYSTEMTIME end,
        DWORD *count,
        LPSYSTEMTIME *time);

    END_INTERFACE
} IRegisteredTaskVtbl;

interface IRegisteredTask {
    CONST_VTBL IRegisteredTaskVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRegisteredTask_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRegisteredTask_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRegisteredTask_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRegisteredTask_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRegisteredTask_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRegisteredTask_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRegisteredTask_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRegisteredTask methods ***/
#define IRegisteredTask_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define IRegisteredTask_get_Path(This,path) (This)->lpVtbl->get_Path(This,path)
#define IRegisteredTask_get_State(This,state) (This)->lpVtbl->get_State(This,state)
#define IRegisteredTask_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define IRegisteredTask_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#define IRegisteredTask_Run(This,params,task) (This)->lpVtbl->Run(This,params,task)
#define IRegisteredTask_RunEx(This,params,flags,sessionID,user,task) (This)->lpVtbl->RunEx(This,params,flags,sessionID,user,task)
#define IRegisteredTask_GetInstances(This,flags,tasks) (This)->lpVtbl->GetInstances(This,flags,tasks)
#define IRegisteredTask_get_LastRunTime(This,date) (This)->lpVtbl->get_LastRunTime(This,date)
#define IRegisteredTask_get_LastTaskResult(This,result) (This)->lpVtbl->get_LastTaskResult(This,result)
#define IRegisteredTask_get_NumberOfMissedRuns(This,runs) (This)->lpVtbl->get_NumberOfMissedRuns(This,runs)
#define IRegisteredTask_get_NextRunTime(This,date) (This)->lpVtbl->get_NextRunTime(This,date)
#define IRegisteredTask_get_Definition(This,task) (This)->lpVtbl->get_Definition(This,task)
#define IRegisteredTask_get_Xml(This,xml) (This)->lpVtbl->get_Xml(This,xml)
#define IRegisteredTask_GetSecurityDescriptor(This,info,sddl) (This)->lpVtbl->GetSecurityDescriptor(This,info,sddl)
#define IRegisteredTask_SetSecurityDescriptor(This,sddl,flags) (This)->lpVtbl->SetSecurityDescriptor(This,sddl,flags)
#define IRegisteredTask_Stop(This,flags) (This)->lpVtbl->Stop(This,flags)
#define IRegisteredTask_GetRunTimes(This,start,end,count,time) (This)->lpVtbl->GetRunTimes(This,start,end,count,time)
#else
/*** IUnknown methods ***/
static inline HRESULT IRegisteredTask_QueryInterface(IRegisteredTask* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRegisteredTask_AddRef(IRegisteredTask* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRegisteredTask_Release(IRegisteredTask* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRegisteredTask_GetTypeInfoCount(IRegisteredTask* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRegisteredTask_GetTypeInfo(IRegisteredTask* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRegisteredTask_GetIDsOfNames(IRegisteredTask* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRegisteredTask_Invoke(IRegisteredTask* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRegisteredTask methods ***/
static inline HRESULT IRegisteredTask_get_Name(IRegisteredTask* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT IRegisteredTask_get_Path(IRegisteredTask* This,BSTR *path) {
    return This->lpVtbl->get_Path(This,path);
}
static inline HRESULT IRegisteredTask_get_State(IRegisteredTask* This,TASK_STATE *state) {
    return This->lpVtbl->get_State(This,state);
}
static inline HRESULT IRegisteredTask_get_Enabled(IRegisteredTask* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT IRegisteredTask_put_Enabled(IRegisteredTask* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
static inline HRESULT IRegisteredTask_Run(IRegisteredTask* This,VARIANT params,IRunningTask **task) {
    return This->lpVtbl->Run(This,params,task);
}
static inline HRESULT IRegisteredTask_RunEx(IRegisteredTask* This,VARIANT params,LONG flags,LONG sessionID,BSTR user,IRunningTask **task) {
    return This->lpVtbl->RunEx(This,params,flags,sessionID,user,task);
}
static inline HRESULT IRegisteredTask_GetInstances(IRegisteredTask* This,LONG flags,IRunningTaskCollection **tasks) {
    return This->lpVtbl->GetInstances(This,flags,tasks);
}
static inline HRESULT IRegisteredTask_get_LastRunTime(IRegisteredTask* This,DATE *date) {
    return This->lpVtbl->get_LastRunTime(This,date);
}
static inline HRESULT IRegisteredTask_get_LastTaskResult(IRegisteredTask* This,LONG *result) {
    return This->lpVtbl->get_LastTaskResult(This,result);
}
static inline HRESULT IRegisteredTask_get_NumberOfMissedRuns(IRegisteredTask* This,LONG *runs) {
    return This->lpVtbl->get_NumberOfMissedRuns(This,runs);
}
static inline HRESULT IRegisteredTask_get_NextRunTime(IRegisteredTask* This,DATE *date) {
    return This->lpVtbl->get_NextRunTime(This,date);
}
static inline HRESULT IRegisteredTask_get_Definition(IRegisteredTask* This,ITaskDefinition **task) {
    return This->lpVtbl->get_Definition(This,task);
}
static inline HRESULT IRegisteredTask_get_Xml(IRegisteredTask* This,BSTR *xml) {
    return This->lpVtbl->get_Xml(This,xml);
}
static inline HRESULT IRegisteredTask_GetSecurityDescriptor(IRegisteredTask* This,LONG info,BSTR *sddl) {
    return This->lpVtbl->GetSecurityDescriptor(This,info,sddl);
}
static inline HRESULT IRegisteredTask_SetSecurityDescriptor(IRegisteredTask* This,BSTR sddl,LONG flags) {
    return This->lpVtbl->SetSecurityDescriptor(This,sddl,flags);
}
static inline HRESULT IRegisteredTask_Stop(IRegisteredTask* This,LONG flags) {
    return This->lpVtbl->Stop(This,flags);
}
static inline HRESULT IRegisteredTask_GetRunTimes(IRegisteredTask* This,const LPSYSTEMTIME start,const LPSYSTEMTIME end,DWORD *count,LPSYSTEMTIME *time) {
    return This->lpVtbl->GetRunTimes(This,start,end,count,time);
}
#endif
#endif

#endif


#endif  /* __IRegisteredTask_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRegisteredTaskCollection interface
 */
#ifndef __IRegisteredTaskCollection_INTERFACE_DEFINED__
#define __IRegisteredTaskCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRegisteredTaskCollection, 0x86627eb4, 0x42a7, 0x41e4, 0xa4,0xd9, 0xac,0x33,0xa7,0x2f,0x2d,0x52);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("86627eb4-42a7-41e4-a4d9-ac33a72f2d52")
IRegisteredTaskCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        VARIANT index,
        IRegisteredTask **task) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **penum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRegisteredTaskCollection, 0x86627eb4, 0x42a7, 0x41e4, 0xa4,0xd9, 0xac,0x33,0xa7,0x2f,0x2d,0x52)
#endif
#else
typedef struct IRegisteredTaskCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRegisteredTaskCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRegisteredTaskCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRegisteredTaskCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRegisteredTaskCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRegisteredTaskCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRegisteredTaskCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRegisteredTaskCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRegisteredTaskCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IRegisteredTaskCollection *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IRegisteredTaskCollection *This,
        VARIANT index,
        IRegisteredTask **task);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IRegisteredTaskCollection *This,
        IUnknown **penum);

    END_INTERFACE
} IRegisteredTaskCollectionVtbl;

interface IRegisteredTaskCollection {
    CONST_VTBL IRegisteredTaskCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRegisteredTaskCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRegisteredTaskCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRegisteredTaskCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRegisteredTaskCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRegisteredTaskCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRegisteredTaskCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRegisteredTaskCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRegisteredTaskCollection methods ***/
#define IRegisteredTaskCollection_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define IRegisteredTaskCollection_get_Item(This,index,task) (This)->lpVtbl->get_Item(This,index,task)
#define IRegisteredTaskCollection_get__NewEnum(This,penum) (This)->lpVtbl->get__NewEnum(This,penum)
#else
/*** IUnknown methods ***/
static inline HRESULT IRegisteredTaskCollection_QueryInterface(IRegisteredTaskCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRegisteredTaskCollection_AddRef(IRegisteredTaskCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRegisteredTaskCollection_Release(IRegisteredTaskCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRegisteredTaskCollection_GetTypeInfoCount(IRegisteredTaskCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRegisteredTaskCollection_GetTypeInfo(IRegisteredTaskCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRegisteredTaskCollection_GetIDsOfNames(IRegisteredTaskCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRegisteredTaskCollection_Invoke(IRegisteredTaskCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRegisteredTaskCollection methods ***/
static inline HRESULT IRegisteredTaskCollection_get_Count(IRegisteredTaskCollection* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static inline HRESULT IRegisteredTaskCollection_get_Item(IRegisteredTaskCollection* This,VARIANT index,IRegisteredTask **task) {
    return This->lpVtbl->get_Item(This,index,task);
}
static inline HRESULT IRegisteredTaskCollection_get__NewEnum(IRegisteredTaskCollection* This,IUnknown **penum) {
    return This->lpVtbl->get__NewEnum(This,penum);
}
#endif
#endif

#endif


#endif  /* __IRegisteredTaskCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRegistrationInfo interface
 */
#ifndef __IRegistrationInfo_INTERFACE_DEFINED__
#define __IRegistrationInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRegistrationInfo, 0x416d8b73, 0xcb41, 0x4ea1, 0x80,0x5c, 0x9b,0xe9,0xa5,0xac,0x4a,0x74);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("416d8b73-cb41-4ea1-805c-9be9a5ac4a74")
IRegistrationInfo : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *description) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Description(
        BSTR description) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Author(
        BSTR *author) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Author(
        BSTR author) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Version(
        BSTR *version) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Version(
        BSTR version) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Date(
        BSTR *date) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Date(
        BSTR date) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Documentation(
        BSTR *doc) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Documentation(
        BSTR doc) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_XmlText(
        BSTR *xml) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_XmlText(
        BSTR xml) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_URI(
        BSTR *uri) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_URI(
        BSTR uri) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SecurityDescriptor(
        VARIANT *sddl) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SecurityDescriptor(
        VARIANT sddl) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Source(
        BSTR *source) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Source(
        BSTR source) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRegistrationInfo, 0x416d8b73, 0xcb41, 0x4ea1, 0x80,0x5c, 0x9b,0xe9,0xa5,0xac,0x4a,0x74)
#endif
#else
typedef struct IRegistrationInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRegistrationInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRegistrationInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRegistrationInfo *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRegistrationInfo *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRegistrationInfo *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRegistrationInfo *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRegistrationInfo *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRegistrationInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IRegistrationInfo *This,
        BSTR *description);

    HRESULT (STDMETHODCALLTYPE *put_Description)(
        IRegistrationInfo *This,
        BSTR description);

    HRESULT (STDMETHODCALLTYPE *get_Author)(
        IRegistrationInfo *This,
        BSTR *author);

    HRESULT (STDMETHODCALLTYPE *put_Author)(
        IRegistrationInfo *This,
        BSTR author);

    HRESULT (STDMETHODCALLTYPE *get_Version)(
        IRegistrationInfo *This,
        BSTR *version);

    HRESULT (STDMETHODCALLTYPE *put_Version)(
        IRegistrationInfo *This,
        BSTR version);

    HRESULT (STDMETHODCALLTYPE *get_Date)(
        IRegistrationInfo *This,
        BSTR *date);

    HRESULT (STDMETHODCALLTYPE *put_Date)(
        IRegistrationInfo *This,
        BSTR date);

    HRESULT (STDMETHODCALLTYPE *get_Documentation)(
        IRegistrationInfo *This,
        BSTR *doc);

    HRESULT (STDMETHODCALLTYPE *put_Documentation)(
        IRegistrationInfo *This,
        BSTR doc);

    HRESULT (STDMETHODCALLTYPE *get_XmlText)(
        IRegistrationInfo *This,
        BSTR *xml);

    HRESULT (STDMETHODCALLTYPE *put_XmlText)(
        IRegistrationInfo *This,
        BSTR xml);

    HRESULT (STDMETHODCALLTYPE *get_URI)(
        IRegistrationInfo *This,
        BSTR *uri);

    HRESULT (STDMETHODCALLTYPE *put_URI)(
        IRegistrationInfo *This,
        BSTR uri);

    HRESULT (STDMETHODCALLTYPE *get_SecurityDescriptor)(
        IRegistrationInfo *This,
        VARIANT *sddl);

    HRESULT (STDMETHODCALLTYPE *put_SecurityDescriptor)(
        IRegistrationInfo *This,
        VARIANT sddl);

    HRESULT (STDMETHODCALLTYPE *get_Source)(
        IRegistrationInfo *This,
        BSTR *source);

    HRESULT (STDMETHODCALLTYPE *put_Source)(
        IRegistrationInfo *This,
        BSTR source);

    END_INTERFACE
} IRegistrationInfoVtbl;

interface IRegistrationInfo {
    CONST_VTBL IRegistrationInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRegistrationInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRegistrationInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRegistrationInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRegistrationInfo_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRegistrationInfo_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRegistrationInfo_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRegistrationInfo_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRegistrationInfo methods ***/
#define IRegistrationInfo_get_Description(This,description) (This)->lpVtbl->get_Description(This,description)
#define IRegistrationInfo_put_Description(This,description) (This)->lpVtbl->put_Description(This,description)
#define IRegistrationInfo_get_Author(This,author) (This)->lpVtbl->get_Author(This,author)
#define IRegistrationInfo_put_Author(This,author) (This)->lpVtbl->put_Author(This,author)
#define IRegistrationInfo_get_Version(This,version) (This)->lpVtbl->get_Version(This,version)
#define IRegistrationInfo_put_Version(This,version) (This)->lpVtbl->put_Version(This,version)
#define IRegistrationInfo_get_Date(This,date) (This)->lpVtbl->get_Date(This,date)
#define IRegistrationInfo_put_Date(This,date) (This)->lpVtbl->put_Date(This,date)
#define IRegistrationInfo_get_Documentation(This,doc) (This)->lpVtbl->get_Documentation(This,doc)
#define IRegistrationInfo_put_Documentation(This,doc) (This)->lpVtbl->put_Documentation(This,doc)
#define IRegistrationInfo_get_XmlText(This,xml) (This)->lpVtbl->get_XmlText(This,xml)
#define IRegistrationInfo_put_XmlText(This,xml) (This)->lpVtbl->put_XmlText(This,xml)
#define IRegistrationInfo_get_URI(This,uri) (This)->lpVtbl->get_URI(This,uri)
#define IRegistrationInfo_put_URI(This,uri) (This)->lpVtbl->put_URI(This,uri)
#define IRegistrationInfo_get_SecurityDescriptor(This,sddl) (This)->lpVtbl->get_SecurityDescriptor(This,sddl)
#define IRegistrationInfo_put_SecurityDescriptor(This,sddl) (This)->lpVtbl->put_SecurityDescriptor(This,sddl)
#define IRegistrationInfo_get_Source(This,source) (This)->lpVtbl->get_Source(This,source)
#define IRegistrationInfo_put_Source(This,source) (This)->lpVtbl->put_Source(This,source)
#else
/*** IUnknown methods ***/
static inline HRESULT IRegistrationInfo_QueryInterface(IRegistrationInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRegistrationInfo_AddRef(IRegistrationInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRegistrationInfo_Release(IRegistrationInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRegistrationInfo_GetTypeInfoCount(IRegistrationInfo* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRegistrationInfo_GetTypeInfo(IRegistrationInfo* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRegistrationInfo_GetIDsOfNames(IRegistrationInfo* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRegistrationInfo_Invoke(IRegistrationInfo* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRegistrationInfo methods ***/
static inline HRESULT IRegistrationInfo_get_Description(IRegistrationInfo* This,BSTR *description) {
    return This->lpVtbl->get_Description(This,description);
}
static inline HRESULT IRegistrationInfo_put_Description(IRegistrationInfo* This,BSTR description) {
    return This->lpVtbl->put_Description(This,description);
}
static inline HRESULT IRegistrationInfo_get_Author(IRegistrationInfo* This,BSTR *author) {
    return This->lpVtbl->get_Author(This,author);
}
static inline HRESULT IRegistrationInfo_put_Author(IRegistrationInfo* This,BSTR author) {
    return This->lpVtbl->put_Author(This,author);
}
static inline HRESULT IRegistrationInfo_get_Version(IRegistrationInfo* This,BSTR *version) {
    return This->lpVtbl->get_Version(This,version);
}
static inline HRESULT IRegistrationInfo_put_Version(IRegistrationInfo* This,BSTR version) {
    return This->lpVtbl->put_Version(This,version);
}
static inline HRESULT IRegistrationInfo_get_Date(IRegistrationInfo* This,BSTR *date) {
    return This->lpVtbl->get_Date(This,date);
}
static inline HRESULT IRegistrationInfo_put_Date(IRegistrationInfo* This,BSTR date) {
    return This->lpVtbl->put_Date(This,date);
}
static inline HRESULT IRegistrationInfo_get_Documentation(IRegistrationInfo* This,BSTR *doc) {
    return This->lpVtbl->get_Documentation(This,doc);
}
static inline HRESULT IRegistrationInfo_put_Documentation(IRegistrationInfo* This,BSTR doc) {
    return This->lpVtbl->put_Documentation(This,doc);
}
static inline HRESULT IRegistrationInfo_get_XmlText(IRegistrationInfo* This,BSTR *xml) {
    return This->lpVtbl->get_XmlText(This,xml);
}
static inline HRESULT IRegistrationInfo_put_XmlText(IRegistrationInfo* This,BSTR xml) {
    return This->lpVtbl->put_XmlText(This,xml);
}
static inline HRESULT IRegistrationInfo_get_URI(IRegistrationInfo* This,BSTR *uri) {
    return This->lpVtbl->get_URI(This,uri);
}
static inline HRESULT IRegistrationInfo_put_URI(IRegistrationInfo* This,BSTR uri) {
    return This->lpVtbl->put_URI(This,uri);
}
static inline HRESULT IRegistrationInfo_get_SecurityDescriptor(IRegistrationInfo* This,VARIANT *sddl) {
    return This->lpVtbl->get_SecurityDescriptor(This,sddl);
}
static inline HRESULT IRegistrationInfo_put_SecurityDescriptor(IRegistrationInfo* This,VARIANT sddl) {
    return This->lpVtbl->put_SecurityDescriptor(This,sddl);
}
static inline HRESULT IRegistrationInfo_get_Source(IRegistrationInfo* This,BSTR *source) {
    return This->lpVtbl->get_Source(This,source);
}
static inline HRESULT IRegistrationInfo_put_Source(IRegistrationInfo* This,BSTR source) {
    return This->lpVtbl->put_Source(This,source);
}
#endif
#endif

#endif


#endif  /* __IRegistrationInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITaskFolder interface
 */
#ifndef __ITaskFolder_INTERFACE_DEFINED__
#define __ITaskFolder_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITaskFolder, 0x8cfac062, 0xa080, 0x4c15, 0x9a,0x88, 0xaa,0x7c,0x2a,0xf8,0x0d,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8cfac062-a080-4c15-9a88-aa7c2af80dfc")
ITaskFolder : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Path(
        BSTR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFolder(
        BSTR path,
        ITaskFolder **folder) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFolders(
        LONG flags,
        ITaskFolderCollection **folders) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFolder(
        BSTR name,
        VARIANT sddl,
        ITaskFolder **folder) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteFolder(
        BSTR name,
        LONG flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTask(
        BSTR path,
        IRegisteredTask **task) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTasks(
        LONG flags,
        IRegisteredTaskCollection **tasks) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteTask(
        BSTR name,
        LONG flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterTask(
        BSTR path,
        BSTR xml,
        LONG flags,
        VARIANT user,
        VARIANT password,
        TASK_LOGON_TYPE logonType,
        VARIANT sddl,
        IRegisteredTask **task) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterTaskDefinition(
        BSTR path,
        ITaskDefinition *definition,
        LONG flags,
        VARIANT user,
        VARIANT password,
        TASK_LOGON_TYPE logon,
        VARIANT sddl,
        IRegisteredTask **task) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSecurityDescriptor(
        LONG info,
        BSTR *sddl) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSecurityDescriptor(
        BSTR sddl,
        LONG flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITaskFolder, 0x8cfac062, 0xa080, 0x4c15, 0x9a,0x88, 0xaa,0x7c,0x2a,0xf8,0x0d,0xfc)
#endif
#else
typedef struct ITaskFolderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITaskFolder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITaskFolder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITaskFolder *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITaskFolder *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITaskFolder *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITaskFolder *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITaskFolder *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITaskFolder methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        ITaskFolder *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_Path)(
        ITaskFolder *This,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *GetFolder)(
        ITaskFolder *This,
        BSTR path,
        ITaskFolder **folder);

    HRESULT (STDMETHODCALLTYPE *GetFolders)(
        ITaskFolder *This,
        LONG flags,
        ITaskFolderCollection **folders);

    HRESULT (STDMETHODCALLTYPE *CreateFolder)(
        ITaskFolder *This,
        BSTR name,
        VARIANT sddl,
        ITaskFolder **folder);

    HRESULT (STDMETHODCALLTYPE *DeleteFolder)(
        ITaskFolder *This,
        BSTR name,
        LONG flags);

    HRESULT (STDMETHODCALLTYPE *GetTask)(
        ITaskFolder *This,
        BSTR path,
        IRegisteredTask **task);

    HRESULT (STDMETHODCALLTYPE *GetTasks)(
        ITaskFolder *This,
        LONG flags,
        IRegisteredTaskCollection **tasks);

    HRESULT (STDMETHODCALLTYPE *DeleteTask)(
        ITaskFolder *This,
        BSTR name,
        LONG flags);

    HRESULT (STDMETHODCALLTYPE *RegisterTask)(
        ITaskFolder *This,
        BSTR path,
        BSTR xml,
        LONG flags,
        VARIANT user,
        VARIANT password,
        TASK_LOGON_TYPE logonType,
        VARIANT sddl,
        IRegisteredTask **task);

    HRESULT (STDMETHODCALLTYPE *RegisterTaskDefinition)(
        ITaskFolder *This,
        BSTR path,
        ITaskDefinition *definition,
        LONG flags,
        VARIANT user,
        VARIANT password,
        TASK_LOGON_TYPE logon,
        VARIANT sddl,
        IRegisteredTask **task);

    HRESULT (STDMETHODCALLTYPE *GetSecurityDescriptor)(
        ITaskFolder *This,
        LONG info,
        BSTR *sddl);

    HRESULT (STDMETHODCALLTYPE *SetSecurityDescriptor)(
        ITaskFolder *This,
        BSTR sddl,
        LONG flags);

    END_INTERFACE
} ITaskFolderVtbl;

interface ITaskFolder {
    CONST_VTBL ITaskFolderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITaskFolder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITaskFolder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITaskFolder_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ITaskFolder_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ITaskFolder_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ITaskFolder_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ITaskFolder_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITaskFolder methods ***/
#define ITaskFolder_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define ITaskFolder_get_Path(This,path) (This)->lpVtbl->get_Path(This,path)
#define ITaskFolder_GetFolder(This,path,folder) (This)->lpVtbl->GetFolder(This,path,folder)
#define ITaskFolder_GetFolders(This,flags,folders) (This)->lpVtbl->GetFolders(This,flags,folders)
#define ITaskFolder_CreateFolder(This,name,sddl,folder) (This)->lpVtbl->CreateFolder(This,name,sddl,folder)
#define ITaskFolder_DeleteFolder(This,name,flags) (This)->lpVtbl->DeleteFolder(This,name,flags)
#define ITaskFolder_GetTask(This,path,task) (This)->lpVtbl->GetTask(This,path,task)
#define ITaskFolder_GetTasks(This,flags,tasks) (This)->lpVtbl->GetTasks(This,flags,tasks)
#define ITaskFolder_DeleteTask(This,name,flags) (This)->lpVtbl->DeleteTask(This,name,flags)
#define ITaskFolder_RegisterTask(This,path,xml,flags,user,password,logonType,sddl,task) (This)->lpVtbl->RegisterTask(This,path,xml,flags,user,password,logonType,sddl,task)
#define ITaskFolder_RegisterTaskDefinition(This,path,definition,flags,user,password,logon,sddl,task) (This)->lpVtbl->RegisterTaskDefinition(This,path,definition,flags,user,password,logon,sddl,task)
#define ITaskFolder_GetSecurityDescriptor(This,info,sddl) (This)->lpVtbl->GetSecurityDescriptor(This,info,sddl)
#define ITaskFolder_SetSecurityDescriptor(This,sddl,flags) (This)->lpVtbl->SetSecurityDescriptor(This,sddl,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT ITaskFolder_QueryInterface(ITaskFolder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITaskFolder_AddRef(ITaskFolder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITaskFolder_Release(ITaskFolder* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ITaskFolder_GetTypeInfoCount(ITaskFolder* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ITaskFolder_GetTypeInfo(ITaskFolder* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ITaskFolder_GetIDsOfNames(ITaskFolder* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ITaskFolder_Invoke(ITaskFolder* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITaskFolder methods ***/
static inline HRESULT ITaskFolder_get_Name(ITaskFolder* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT ITaskFolder_get_Path(ITaskFolder* This,BSTR *path) {
    return This->lpVtbl->get_Path(This,path);
}
static inline HRESULT ITaskFolder_GetFolder(ITaskFolder* This,BSTR path,ITaskFolder **folder) {
    return This->lpVtbl->GetFolder(This,path,folder);
}
static inline HRESULT ITaskFolder_GetFolders(ITaskFolder* This,LONG flags,ITaskFolderCollection **folders) {
    return This->lpVtbl->GetFolders(This,flags,folders);
}
static inline HRESULT ITaskFolder_CreateFolder(ITaskFolder* This,BSTR name,VARIANT sddl,ITaskFolder **folder) {
    return This->lpVtbl->CreateFolder(This,name,sddl,folder);
}
static inline HRESULT ITaskFolder_DeleteFolder(ITaskFolder* This,BSTR name,LONG flags) {
    return This->lpVtbl->DeleteFolder(This,name,flags);
}
static inline HRESULT ITaskFolder_GetTask(ITaskFolder* This,BSTR path,IRegisteredTask **task) {
    return This->lpVtbl->GetTask(This,path,task);
}
static inline HRESULT ITaskFolder_GetTasks(ITaskFolder* This,LONG flags,IRegisteredTaskCollection **tasks) {
    return This->lpVtbl->GetTasks(This,flags,tasks);
}
static inline HRESULT ITaskFolder_DeleteTask(ITaskFolder* This,BSTR name,LONG flags) {
    return This->lpVtbl->DeleteTask(This,name,flags);
}
static inline HRESULT ITaskFolder_RegisterTask(ITaskFolder* This,BSTR path,BSTR xml,LONG flags,VARIANT user,VARIANT password,TASK_LOGON_TYPE logonType,VARIANT sddl,IRegisteredTask **task) {
    return This->lpVtbl->RegisterTask(This,path,xml,flags,user,password,logonType,sddl,task);
}
static inline HRESULT ITaskFolder_RegisterTaskDefinition(ITaskFolder* This,BSTR path,ITaskDefinition *definition,LONG flags,VARIANT user,VARIANT password,TASK_LOGON_TYPE logon,VARIANT sddl,IRegisteredTask **task) {
    return This->lpVtbl->RegisterTaskDefinition(This,path,definition,flags,user,password,logon,sddl,task);
}
static inline HRESULT ITaskFolder_GetSecurityDescriptor(ITaskFolder* This,LONG info,BSTR *sddl) {
    return This->lpVtbl->GetSecurityDescriptor(This,info,sddl);
}
static inline HRESULT ITaskFolder_SetSecurityDescriptor(ITaskFolder* This,BSTR sddl,LONG flags) {
    return This->lpVtbl->SetSecurityDescriptor(This,sddl,flags);
}
#endif
#endif

#endif


#endif  /* __ITaskFolder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITaskFolderCollection interface
 */
#ifndef __ITaskFolderCollection_INTERFACE_DEFINED__
#define __ITaskFolderCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITaskFolderCollection, 0x79184a66, 0x8664, 0x423f, 0x97,0xf1, 0x63,0x73,0x56,0xa5,0xd8,0x12);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("79184a66-8664-423f-97f1-637356a5d812")
ITaskFolderCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        VARIANT index,
        ITaskFolder **folder) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **penum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITaskFolderCollection, 0x79184a66, 0x8664, 0x423f, 0x97,0xf1, 0x63,0x73,0x56,0xa5,0xd8,0x12)
#endif
#else
typedef struct ITaskFolderCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITaskFolderCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITaskFolderCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITaskFolderCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITaskFolderCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITaskFolderCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITaskFolderCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITaskFolderCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITaskFolderCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ITaskFolderCollection *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        ITaskFolderCollection *This,
        VARIANT index,
        ITaskFolder **folder);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ITaskFolderCollection *This,
        IUnknown **penum);

    END_INTERFACE
} ITaskFolderCollectionVtbl;

interface ITaskFolderCollection {
    CONST_VTBL ITaskFolderCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITaskFolderCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITaskFolderCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITaskFolderCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ITaskFolderCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ITaskFolderCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ITaskFolderCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ITaskFolderCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITaskFolderCollection methods ***/
#define ITaskFolderCollection_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define ITaskFolderCollection_get_Item(This,index,folder) (This)->lpVtbl->get_Item(This,index,folder)
#define ITaskFolderCollection_get__NewEnum(This,penum) (This)->lpVtbl->get__NewEnum(This,penum)
#else
/*** IUnknown methods ***/
static inline HRESULT ITaskFolderCollection_QueryInterface(ITaskFolderCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITaskFolderCollection_AddRef(ITaskFolderCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITaskFolderCollection_Release(ITaskFolderCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ITaskFolderCollection_GetTypeInfoCount(ITaskFolderCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ITaskFolderCollection_GetTypeInfo(ITaskFolderCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ITaskFolderCollection_GetIDsOfNames(ITaskFolderCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ITaskFolderCollection_Invoke(ITaskFolderCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITaskFolderCollection methods ***/
static inline HRESULT ITaskFolderCollection_get_Count(ITaskFolderCollection* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static inline HRESULT ITaskFolderCollection_get_Item(ITaskFolderCollection* This,VARIANT index,ITaskFolder **folder) {
    return This->lpVtbl->get_Item(This,index,folder);
}
static inline HRESULT ITaskFolderCollection_get__NewEnum(ITaskFolderCollection* This,IUnknown **penum) {
    return This->lpVtbl->get__NewEnum(This,penum);
}
#endif
#endif

#endif


#endif  /* __ITaskFolderCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITaskDefinition interface
 */
#ifndef __ITaskDefinition_INTERFACE_DEFINED__
#define __ITaskDefinition_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITaskDefinition, 0xf5bc8fc5, 0x536d, 0x4f77, 0xb8,0x52, 0xfb,0xc1,0x35,0x6f,0xde,0xb6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f5bc8fc5-536d-4f77-b852-fbc1356fdeb6")
ITaskDefinition : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_RegistrationInfo(
        IRegistrationInfo **info) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RegistrationInfo(
        IRegistrationInfo *info) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Triggers(
        ITriggerCollection **triggers) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Triggers(
        ITriggerCollection *triggers) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Settings(
        ITaskSettings **settings) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Settings(
        ITaskSettings *settings) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Data(
        BSTR *data) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Data(
        BSTR data) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Principal(
        IPrincipal **principal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Principal(
        IPrincipal *principal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Actions(
        IActionCollection **actions) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Actions(
        IActionCollection *actions) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_XmlText(
        BSTR *xml) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_XmlText(
        BSTR xml) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITaskDefinition, 0xf5bc8fc5, 0x536d, 0x4f77, 0xb8,0x52, 0xfb,0xc1,0x35,0x6f,0xde,0xb6)
#endif
#else
typedef struct ITaskDefinitionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITaskDefinition *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITaskDefinition *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITaskDefinition *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITaskDefinition *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITaskDefinition *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITaskDefinition *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITaskDefinition *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITaskDefinition methods ***/
    HRESULT (STDMETHODCALLTYPE *get_RegistrationInfo)(
        ITaskDefinition *This,
        IRegistrationInfo **info);

    HRESULT (STDMETHODCALLTYPE *put_RegistrationInfo)(
        ITaskDefinition *This,
        IRegistrationInfo *info);

    HRESULT (STDMETHODCALLTYPE *get_Triggers)(
        ITaskDefinition *This,
        ITriggerCollection **triggers);

    HRESULT (STDMETHODCALLTYPE *put_Triggers)(
        ITaskDefinition *This,
        ITriggerCollection *triggers);

    HRESULT (STDMETHODCALLTYPE *get_Settings)(
        ITaskDefinition *This,
        ITaskSettings **settings);

    HRESULT (STDMETHODCALLTYPE *put_Settings)(
        ITaskDefinition *This,
        ITaskSettings *settings);

    HRESULT (STDMETHODCALLTYPE *get_Data)(
        ITaskDefinition *This,
        BSTR *data);

    HRESULT (STDMETHODCALLTYPE *put_Data)(
        ITaskDefinition *This,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *get_Principal)(
        ITaskDefinition *This,
        IPrincipal **principal);

    HRESULT (STDMETHODCALLTYPE *put_Principal)(
        ITaskDefinition *This,
        IPrincipal *principal);

    HRESULT (STDMETHODCALLTYPE *get_Actions)(
        ITaskDefinition *This,
        IActionCollection **actions);

    HRESULT (STDMETHODCALLTYPE *put_Actions)(
        ITaskDefinition *This,
        IActionCollection *actions);

    HRESULT (STDMETHODCALLTYPE *get_XmlText)(
        ITaskDefinition *This,
        BSTR *xml);

    HRESULT (STDMETHODCALLTYPE *put_XmlText)(
        ITaskDefinition *This,
        BSTR xml);

    END_INTERFACE
} ITaskDefinitionVtbl;

interface ITaskDefinition {
    CONST_VTBL ITaskDefinitionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITaskDefinition_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITaskDefinition_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITaskDefinition_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ITaskDefinition_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ITaskDefinition_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ITaskDefinition_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ITaskDefinition_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITaskDefinition methods ***/
#define ITaskDefinition_get_RegistrationInfo(This,info) (This)->lpVtbl->get_RegistrationInfo(This,info)
#define ITaskDefinition_put_RegistrationInfo(This,info) (This)->lpVtbl->put_RegistrationInfo(This,info)
#define ITaskDefinition_get_Triggers(This,triggers) (This)->lpVtbl->get_Triggers(This,triggers)
#define ITaskDefinition_put_Triggers(This,triggers) (This)->lpVtbl->put_Triggers(This,triggers)
#define ITaskDefinition_get_Settings(This,settings) (This)->lpVtbl->get_Settings(This,settings)
#define ITaskDefinition_put_Settings(This,settings) (This)->lpVtbl->put_Settings(This,settings)
#define ITaskDefinition_get_Data(This,data) (This)->lpVtbl->get_Data(This,data)
#define ITaskDefinition_put_Data(This,data) (This)->lpVtbl->put_Data(This,data)
#define ITaskDefinition_get_Principal(This,principal) (This)->lpVtbl->get_Principal(This,principal)
#define ITaskDefinition_put_Principal(This,principal) (This)->lpVtbl->put_Principal(This,principal)
#define ITaskDefinition_get_Actions(This,actions) (This)->lpVtbl->get_Actions(This,actions)
#define ITaskDefinition_put_Actions(This,actions) (This)->lpVtbl->put_Actions(This,actions)
#define ITaskDefinition_get_XmlText(This,xml) (This)->lpVtbl->get_XmlText(This,xml)
#define ITaskDefinition_put_XmlText(This,xml) (This)->lpVtbl->put_XmlText(This,xml)
#else
/*** IUnknown methods ***/
static inline HRESULT ITaskDefinition_QueryInterface(ITaskDefinition* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITaskDefinition_AddRef(ITaskDefinition* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITaskDefinition_Release(ITaskDefinition* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ITaskDefinition_GetTypeInfoCount(ITaskDefinition* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ITaskDefinition_GetTypeInfo(ITaskDefinition* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ITaskDefinition_GetIDsOfNames(ITaskDefinition* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ITaskDefinition_Invoke(ITaskDefinition* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITaskDefinition methods ***/
static inline HRESULT ITaskDefinition_get_RegistrationInfo(ITaskDefinition* This,IRegistrationInfo **info) {
    return This->lpVtbl->get_RegistrationInfo(This,info);
}
static inline HRESULT ITaskDefinition_put_RegistrationInfo(ITaskDefinition* This,IRegistrationInfo *info) {
    return This->lpVtbl->put_RegistrationInfo(This,info);
}
static inline HRESULT ITaskDefinition_get_Triggers(ITaskDefinition* This,ITriggerCollection **triggers) {
    return This->lpVtbl->get_Triggers(This,triggers);
}
static inline HRESULT ITaskDefinition_put_Triggers(ITaskDefinition* This,ITriggerCollection *triggers) {
    return This->lpVtbl->put_Triggers(This,triggers);
}
static inline HRESULT ITaskDefinition_get_Settings(ITaskDefinition* This,ITaskSettings **settings) {
    return This->lpVtbl->get_Settings(This,settings);
}
static inline HRESULT ITaskDefinition_put_Settings(ITaskDefinition* This,ITaskSettings *settings) {
    return This->lpVtbl->put_Settings(This,settings);
}
static inline HRESULT ITaskDefinition_get_Data(ITaskDefinition* This,BSTR *data) {
    return This->lpVtbl->get_Data(This,data);
}
static inline HRESULT ITaskDefinition_put_Data(ITaskDefinition* This,BSTR data) {
    return This->lpVtbl->put_Data(This,data);
}
static inline HRESULT ITaskDefinition_get_Principal(ITaskDefinition* This,IPrincipal **principal) {
    return This->lpVtbl->get_Principal(This,principal);
}
static inline HRESULT ITaskDefinition_put_Principal(ITaskDefinition* This,IPrincipal *principal) {
    return This->lpVtbl->put_Principal(This,principal);
}
static inline HRESULT ITaskDefinition_get_Actions(ITaskDefinition* This,IActionCollection **actions) {
    return This->lpVtbl->get_Actions(This,actions);
}
static inline HRESULT ITaskDefinition_put_Actions(ITaskDefinition* This,IActionCollection *actions) {
    return This->lpVtbl->put_Actions(This,actions);
}
static inline HRESULT ITaskDefinition_get_XmlText(ITaskDefinition* This,BSTR *xml) {
    return This->lpVtbl->get_XmlText(This,xml);
}
static inline HRESULT ITaskDefinition_put_XmlText(ITaskDefinition* This,BSTR xml) {
    return This->lpVtbl->put_XmlText(This,xml);
}
#endif
#endif

#endif


#endif  /* __ITaskDefinition_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITaskSettings interface
 */
#ifndef __ITaskSettings_INTERFACE_DEFINED__
#define __ITaskSettings_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITaskSettings, 0x8fd4711d, 0x2d02, 0x4c8c, 0x87,0xe3, 0xef,0xf6,0x99,0xde,0x12,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8fd4711d-2d02-4c8c-87e3-eff699de127e")
ITaskSettings : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_AllowDemandStart(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowDemandStart(
        VARIANT_BOOL allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RestartInterval(
        BSTR *interval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RestartInterval(
        BSTR interval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RestartCount(
        INT *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RestartCount(
        INT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MultipleInstances(
        TASK_INSTANCES_POLICY *policy) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MultipleInstances(
        TASK_INSTANCES_POLICY policy) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StopIfGoingOnBatteries(
        VARIANT_BOOL *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_StopIfGoingOnBatteries(
        VARIANT_BOOL stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DisallowStartIfOnBatteries(
        VARIANT_BOOL *disallow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DisallowStartIfOnBatteries(
        VARIANT_BOOL disallow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowHardTerminate(
        VARIANT_BOOL *allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowHardTerminate(
        VARIANT_BOOL allow) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StartWhenAvailable(
        VARIANT_BOOL *start) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_StartWhenAvailable(
        VARIANT_BOOL start) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_XmlText(
        BSTR *xml) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_XmlText(
        BSTR xml) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RunOnlyIfNetworkAvailable(
        VARIANT_BOOL *run) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RunOnlyIfNetworkAvailable(
        VARIANT_BOOL run) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ExecutionTimeLimit(
        BSTR *limit) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ExecutionTimeLimit(
        BSTR limit) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DeleteExpiredTaskAfter(
        BSTR *delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DeleteExpiredTaskAfter(
        BSTR delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Priority(
        INT *priority) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Priority(
        INT priority) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Compatibility(
        TASK_COMPATIBILITY *level) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Compatibility(
        TASK_COMPATIBILITY level) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Hidden(
        VARIANT_BOOL *hidden) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Hidden(
        VARIANT_BOOL hidden) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IdleSettings(
        IIdleSettings **settings) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IdleSettings(
        IIdleSettings *settings) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RunOnlyIfIdle(
        VARIANT_BOOL *run) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RunOnlyIfIdle(
        VARIANT_BOOL run) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WakeToRun(
        VARIANT_BOOL *wake) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_WakeToRun(
        VARIANT_BOOL wake) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_NetworkSettings(
        INetworkSettings **settings) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_NetworkSettings(
        INetworkSettings *settings) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITaskSettings, 0x8fd4711d, 0x2d02, 0x4c8c, 0x87,0xe3, 0xef,0xf6,0x99,0xde,0x12,0x7e)
#endif
#else
typedef struct ITaskSettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITaskSettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITaskSettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITaskSettings *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITaskSettings *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITaskSettings *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITaskSettings *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITaskSettings *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITaskSettings methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AllowDemandStart)(
        ITaskSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowDemandStart)(
        ITaskSettings *This,
        VARIANT_BOOL allow);

    HRESULT (STDMETHODCALLTYPE *get_RestartInterval)(
        ITaskSettings *This,
        BSTR *interval);

    HRESULT (STDMETHODCALLTYPE *put_RestartInterval)(
        ITaskSettings *This,
        BSTR interval);

    HRESULT (STDMETHODCALLTYPE *get_RestartCount)(
        ITaskSettings *This,
        INT *count);

    HRESULT (STDMETHODCALLTYPE *put_RestartCount)(
        ITaskSettings *This,
        INT count);

    HRESULT (STDMETHODCALLTYPE *get_MultipleInstances)(
        ITaskSettings *This,
        TASK_INSTANCES_POLICY *policy);

    HRESULT (STDMETHODCALLTYPE *put_MultipleInstances)(
        ITaskSettings *This,
        TASK_INSTANCES_POLICY policy);

    HRESULT (STDMETHODCALLTYPE *get_StopIfGoingOnBatteries)(
        ITaskSettings *This,
        VARIANT_BOOL *stop);

    HRESULT (STDMETHODCALLTYPE *put_StopIfGoingOnBatteries)(
        ITaskSettings *This,
        VARIANT_BOOL stop);

    HRESULT (STDMETHODCALLTYPE *get_DisallowStartIfOnBatteries)(
        ITaskSettings *This,
        VARIANT_BOOL *disallow);

    HRESULT (STDMETHODCALLTYPE *put_DisallowStartIfOnBatteries)(
        ITaskSettings *This,
        VARIANT_BOOL disallow);

    HRESULT (STDMETHODCALLTYPE *get_AllowHardTerminate)(
        ITaskSettings *This,
        VARIANT_BOOL *allow);

    HRESULT (STDMETHODCALLTYPE *put_AllowHardTerminate)(
        ITaskSettings *This,
        VARIANT_BOOL allow);

    HRESULT (STDMETHODCALLTYPE *get_StartWhenAvailable)(
        ITaskSettings *This,
        VARIANT_BOOL *start);

    HRESULT (STDMETHODCALLTYPE *put_StartWhenAvailable)(
        ITaskSettings *This,
        VARIANT_BOOL start);

    HRESULT (STDMETHODCALLTYPE *get_XmlText)(
        ITaskSettings *This,
        BSTR *xml);

    HRESULT (STDMETHODCALLTYPE *put_XmlText)(
        ITaskSettings *This,
        BSTR xml);

    HRESULT (STDMETHODCALLTYPE *get_RunOnlyIfNetworkAvailable)(
        ITaskSettings *This,
        VARIANT_BOOL *run);

    HRESULT (STDMETHODCALLTYPE *put_RunOnlyIfNetworkAvailable)(
        ITaskSettings *This,
        VARIANT_BOOL run);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        ITaskSettings *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        ITaskSettings *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        ITaskSettings *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        ITaskSettings *This,
        VARIANT_BOOL enabled);

    HRESULT (STDMETHODCALLTYPE *get_DeleteExpiredTaskAfter)(
        ITaskSettings *This,
        BSTR *delay);

    HRESULT (STDMETHODCALLTYPE *put_DeleteExpiredTaskAfter)(
        ITaskSettings *This,
        BSTR delay);

    HRESULT (STDMETHODCALLTYPE *get_Priority)(
        ITaskSettings *This,
        INT *priority);

    HRESULT (STDMETHODCALLTYPE *put_Priority)(
        ITaskSettings *This,
        INT priority);

    HRESULT (STDMETHODCALLTYPE *get_Compatibility)(
        ITaskSettings *This,
        TASK_COMPATIBILITY *level);

    HRESULT (STDMETHODCALLTYPE *put_Compatibility)(
        ITaskSettings *This,
        TASK_COMPATIBILITY level);

    HRESULT (STDMETHODCALLTYPE *get_Hidden)(
        ITaskSettings *This,
        VARIANT_BOOL *hidden);

    HRESULT (STDMETHODCALLTYPE *put_Hidden)(
        ITaskSettings *This,
        VARIANT_BOOL hidden);

    HRESULT (STDMETHODCALLTYPE *get_IdleSettings)(
        ITaskSettings *This,
        IIdleSettings **settings);

    HRESULT (STDMETHODCALLTYPE *put_IdleSettings)(
        ITaskSettings *This,
        IIdleSettings *settings);

    HRESULT (STDMETHODCALLTYPE *get_RunOnlyIfIdle)(
        ITaskSettings *This,
        VARIANT_BOOL *run);

    HRESULT (STDMETHODCALLTYPE *put_RunOnlyIfIdle)(
        ITaskSettings *This,
        VARIANT_BOOL run);

    HRESULT (STDMETHODCALLTYPE *get_WakeToRun)(
        ITaskSettings *This,
        VARIANT_BOOL *wake);

    HRESULT (STDMETHODCALLTYPE *put_WakeToRun)(
        ITaskSettings *This,
        VARIANT_BOOL wake);

    HRESULT (STDMETHODCALLTYPE *get_NetworkSettings)(
        ITaskSettings *This,
        INetworkSettings **settings);

    HRESULT (STDMETHODCALLTYPE *put_NetworkSettings)(
        ITaskSettings *This,
        INetworkSettings *settings);

    END_INTERFACE
} ITaskSettingsVtbl;

interface ITaskSettings {
    CONST_VTBL ITaskSettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITaskSettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITaskSettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITaskSettings_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ITaskSettings_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ITaskSettings_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ITaskSettings_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ITaskSettings_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITaskSettings methods ***/
#define ITaskSettings_get_AllowDemandStart(This,allow) (This)->lpVtbl->get_AllowDemandStart(This,allow)
#define ITaskSettings_put_AllowDemandStart(This,allow) (This)->lpVtbl->put_AllowDemandStart(This,allow)
#define ITaskSettings_get_RestartInterval(This,interval) (This)->lpVtbl->get_RestartInterval(This,interval)
#define ITaskSettings_put_RestartInterval(This,interval) (This)->lpVtbl->put_RestartInterval(This,interval)
#define ITaskSettings_get_RestartCount(This,count) (This)->lpVtbl->get_RestartCount(This,count)
#define ITaskSettings_put_RestartCount(This,count) (This)->lpVtbl->put_RestartCount(This,count)
#define ITaskSettings_get_MultipleInstances(This,policy) (This)->lpVtbl->get_MultipleInstances(This,policy)
#define ITaskSettings_put_MultipleInstances(This,policy) (This)->lpVtbl->put_MultipleInstances(This,policy)
#define ITaskSettings_get_StopIfGoingOnBatteries(This,stop) (This)->lpVtbl->get_StopIfGoingOnBatteries(This,stop)
#define ITaskSettings_put_StopIfGoingOnBatteries(This,stop) (This)->lpVtbl->put_StopIfGoingOnBatteries(This,stop)
#define ITaskSettings_get_DisallowStartIfOnBatteries(This,disallow) (This)->lpVtbl->get_DisallowStartIfOnBatteries(This,disallow)
#define ITaskSettings_put_DisallowStartIfOnBatteries(This,disallow) (This)->lpVtbl->put_DisallowStartIfOnBatteries(This,disallow)
#define ITaskSettings_get_AllowHardTerminate(This,allow) (This)->lpVtbl->get_AllowHardTerminate(This,allow)
#define ITaskSettings_put_AllowHardTerminate(This,allow) (This)->lpVtbl->put_AllowHardTerminate(This,allow)
#define ITaskSettings_get_StartWhenAvailable(This,start) (This)->lpVtbl->get_StartWhenAvailable(This,start)
#define ITaskSettings_put_StartWhenAvailable(This,start) (This)->lpVtbl->put_StartWhenAvailable(This,start)
#define ITaskSettings_get_XmlText(This,xml) (This)->lpVtbl->get_XmlText(This,xml)
#define ITaskSettings_put_XmlText(This,xml) (This)->lpVtbl->put_XmlText(This,xml)
#define ITaskSettings_get_RunOnlyIfNetworkAvailable(This,run) (This)->lpVtbl->get_RunOnlyIfNetworkAvailable(This,run)
#define ITaskSettings_put_RunOnlyIfNetworkAvailable(This,run) (This)->lpVtbl->put_RunOnlyIfNetworkAvailable(This,run)
#define ITaskSettings_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define ITaskSettings_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define ITaskSettings_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define ITaskSettings_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#define ITaskSettings_get_DeleteExpiredTaskAfter(This,delay) (This)->lpVtbl->get_DeleteExpiredTaskAfter(This,delay)
#define ITaskSettings_put_DeleteExpiredTaskAfter(This,delay) (This)->lpVtbl->put_DeleteExpiredTaskAfter(This,delay)
#define ITaskSettings_get_Priority(This,priority) (This)->lpVtbl->get_Priority(This,priority)
#define ITaskSettings_put_Priority(This,priority) (This)->lpVtbl->put_Priority(This,priority)
#define ITaskSettings_get_Compatibility(This,level) (This)->lpVtbl->get_Compatibility(This,level)
#define ITaskSettings_put_Compatibility(This,level) (This)->lpVtbl->put_Compatibility(This,level)
#define ITaskSettings_get_Hidden(This,hidden) (This)->lpVtbl->get_Hidden(This,hidden)
#define ITaskSettings_put_Hidden(This,hidden) (This)->lpVtbl->put_Hidden(This,hidden)
#define ITaskSettings_get_IdleSettings(This,settings) (This)->lpVtbl->get_IdleSettings(This,settings)
#define ITaskSettings_put_IdleSettings(This,settings) (This)->lpVtbl->put_IdleSettings(This,settings)
#define ITaskSettings_get_RunOnlyIfIdle(This,run) (This)->lpVtbl->get_RunOnlyIfIdle(This,run)
#define ITaskSettings_put_RunOnlyIfIdle(This,run) (This)->lpVtbl->put_RunOnlyIfIdle(This,run)
#define ITaskSettings_get_WakeToRun(This,wake) (This)->lpVtbl->get_WakeToRun(This,wake)
#define ITaskSettings_put_WakeToRun(This,wake) (This)->lpVtbl->put_WakeToRun(This,wake)
#define ITaskSettings_get_NetworkSettings(This,settings) (This)->lpVtbl->get_NetworkSettings(This,settings)
#define ITaskSettings_put_NetworkSettings(This,settings) (This)->lpVtbl->put_NetworkSettings(This,settings)
#else
/*** IUnknown methods ***/
static inline HRESULT ITaskSettings_QueryInterface(ITaskSettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITaskSettings_AddRef(ITaskSettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITaskSettings_Release(ITaskSettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ITaskSettings_GetTypeInfoCount(ITaskSettings* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ITaskSettings_GetTypeInfo(ITaskSettings* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ITaskSettings_GetIDsOfNames(ITaskSettings* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ITaskSettings_Invoke(ITaskSettings* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITaskSettings methods ***/
static inline HRESULT ITaskSettings_get_AllowDemandStart(ITaskSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowDemandStart(This,allow);
}
static inline HRESULT ITaskSettings_put_AllowDemandStart(ITaskSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowDemandStart(This,allow);
}
static inline HRESULT ITaskSettings_get_RestartInterval(ITaskSettings* This,BSTR *interval) {
    return This->lpVtbl->get_RestartInterval(This,interval);
}
static inline HRESULT ITaskSettings_put_RestartInterval(ITaskSettings* This,BSTR interval) {
    return This->lpVtbl->put_RestartInterval(This,interval);
}
static inline HRESULT ITaskSettings_get_RestartCount(ITaskSettings* This,INT *count) {
    return This->lpVtbl->get_RestartCount(This,count);
}
static inline HRESULT ITaskSettings_put_RestartCount(ITaskSettings* This,INT count) {
    return This->lpVtbl->put_RestartCount(This,count);
}
static inline HRESULT ITaskSettings_get_MultipleInstances(ITaskSettings* This,TASK_INSTANCES_POLICY *policy) {
    return This->lpVtbl->get_MultipleInstances(This,policy);
}
static inline HRESULT ITaskSettings_put_MultipleInstances(ITaskSettings* This,TASK_INSTANCES_POLICY policy) {
    return This->lpVtbl->put_MultipleInstances(This,policy);
}
static inline HRESULT ITaskSettings_get_StopIfGoingOnBatteries(ITaskSettings* This,VARIANT_BOOL *stop) {
    return This->lpVtbl->get_StopIfGoingOnBatteries(This,stop);
}
static inline HRESULT ITaskSettings_put_StopIfGoingOnBatteries(ITaskSettings* This,VARIANT_BOOL stop) {
    return This->lpVtbl->put_StopIfGoingOnBatteries(This,stop);
}
static inline HRESULT ITaskSettings_get_DisallowStartIfOnBatteries(ITaskSettings* This,VARIANT_BOOL *disallow) {
    return This->lpVtbl->get_DisallowStartIfOnBatteries(This,disallow);
}
static inline HRESULT ITaskSettings_put_DisallowStartIfOnBatteries(ITaskSettings* This,VARIANT_BOOL disallow) {
    return This->lpVtbl->put_DisallowStartIfOnBatteries(This,disallow);
}
static inline HRESULT ITaskSettings_get_AllowHardTerminate(ITaskSettings* This,VARIANT_BOOL *allow) {
    return This->lpVtbl->get_AllowHardTerminate(This,allow);
}
static inline HRESULT ITaskSettings_put_AllowHardTerminate(ITaskSettings* This,VARIANT_BOOL allow) {
    return This->lpVtbl->put_AllowHardTerminate(This,allow);
}
static inline HRESULT ITaskSettings_get_StartWhenAvailable(ITaskSettings* This,VARIANT_BOOL *start) {
    return This->lpVtbl->get_StartWhenAvailable(This,start);
}
static inline HRESULT ITaskSettings_put_StartWhenAvailable(ITaskSettings* This,VARIANT_BOOL start) {
    return This->lpVtbl->put_StartWhenAvailable(This,start);
}
static inline HRESULT ITaskSettings_get_XmlText(ITaskSettings* This,BSTR *xml) {
    return This->lpVtbl->get_XmlText(This,xml);
}
static inline HRESULT ITaskSettings_put_XmlText(ITaskSettings* This,BSTR xml) {
    return This->lpVtbl->put_XmlText(This,xml);
}
static inline HRESULT ITaskSettings_get_RunOnlyIfNetworkAvailable(ITaskSettings* This,VARIANT_BOOL *run) {
    return This->lpVtbl->get_RunOnlyIfNetworkAvailable(This,run);
}
static inline HRESULT ITaskSettings_put_RunOnlyIfNetworkAvailable(ITaskSettings* This,VARIANT_BOOL run) {
    return This->lpVtbl->put_RunOnlyIfNetworkAvailable(This,run);
}
static inline HRESULT ITaskSettings_get_ExecutionTimeLimit(ITaskSettings* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT ITaskSettings_put_ExecutionTimeLimit(ITaskSettings* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT ITaskSettings_get_Enabled(ITaskSettings* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT ITaskSettings_put_Enabled(ITaskSettings* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
static inline HRESULT ITaskSettings_get_DeleteExpiredTaskAfter(ITaskSettings* This,BSTR *delay) {
    return This->lpVtbl->get_DeleteExpiredTaskAfter(This,delay);
}
static inline HRESULT ITaskSettings_put_DeleteExpiredTaskAfter(ITaskSettings* This,BSTR delay) {
    return This->lpVtbl->put_DeleteExpiredTaskAfter(This,delay);
}
static inline HRESULT ITaskSettings_get_Priority(ITaskSettings* This,INT *priority) {
    return This->lpVtbl->get_Priority(This,priority);
}
static inline HRESULT ITaskSettings_put_Priority(ITaskSettings* This,INT priority) {
    return This->lpVtbl->put_Priority(This,priority);
}
static inline HRESULT ITaskSettings_get_Compatibility(ITaskSettings* This,TASK_COMPATIBILITY *level) {
    return This->lpVtbl->get_Compatibility(This,level);
}
static inline HRESULT ITaskSettings_put_Compatibility(ITaskSettings* This,TASK_COMPATIBILITY level) {
    return This->lpVtbl->put_Compatibility(This,level);
}
static inline HRESULT ITaskSettings_get_Hidden(ITaskSettings* This,VARIANT_BOOL *hidden) {
    return This->lpVtbl->get_Hidden(This,hidden);
}
static inline HRESULT ITaskSettings_put_Hidden(ITaskSettings* This,VARIANT_BOOL hidden) {
    return This->lpVtbl->put_Hidden(This,hidden);
}
static inline HRESULT ITaskSettings_get_IdleSettings(ITaskSettings* This,IIdleSettings **settings) {
    return This->lpVtbl->get_IdleSettings(This,settings);
}
static inline HRESULT ITaskSettings_put_IdleSettings(ITaskSettings* This,IIdleSettings *settings) {
    return This->lpVtbl->put_IdleSettings(This,settings);
}
static inline HRESULT ITaskSettings_get_RunOnlyIfIdle(ITaskSettings* This,VARIANT_BOOL *run) {
    return This->lpVtbl->get_RunOnlyIfIdle(This,run);
}
static inline HRESULT ITaskSettings_put_RunOnlyIfIdle(ITaskSettings* This,VARIANT_BOOL run) {
    return This->lpVtbl->put_RunOnlyIfIdle(This,run);
}
static inline HRESULT ITaskSettings_get_WakeToRun(ITaskSettings* This,VARIANT_BOOL *wake) {
    return This->lpVtbl->get_WakeToRun(This,wake);
}
static inline HRESULT ITaskSettings_put_WakeToRun(ITaskSettings* This,VARIANT_BOOL wake) {
    return This->lpVtbl->put_WakeToRun(This,wake);
}
static inline HRESULT ITaskSettings_get_NetworkSettings(ITaskSettings* This,INetworkSettings **settings) {
    return This->lpVtbl->get_NetworkSettings(This,settings);
}
static inline HRESULT ITaskSettings_put_NetworkSettings(ITaskSettings* This,INetworkSettings *settings) {
    return This->lpVtbl->put_NetworkSettings(This,settings);
}
#endif
#endif

#endif


#endif  /* __ITaskSettings_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIdleSettings interface
 */
#ifndef __IIdleSettings_INTERFACE_DEFINED__
#define __IIdleSettings_INTERFACE_DEFINED__

DEFINE_GUID(IID_IIdleSettings, 0x84594461, 0x0053, 0x4342, 0xa8,0xfd, 0x08,0x8f,0xab,0xf1,0x1f,0x32);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("*************-4342-a8fd-088fabf11f32")
IIdleSettings : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_IdleDuration(
        BSTR *delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IdleDuration(
        BSTR delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WaitTimeout(
        BSTR *timeout) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_WaitTimeout(
        BSTR timeout) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StopOnIdleEnd(
        VARIANT_BOOL *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_StopOnIdleEnd(
        VARIANT_BOOL stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RestartOnIdle(
        VARIANT_BOOL *restart) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RestartOnIdle(
        VARIANT_BOOL restart) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IIdleSettings, 0x84594461, 0x0053, 0x4342, 0xa8,0xfd, 0x08,0x8f,0xab,0xf1,0x1f,0x32)
#endif
#else
typedef struct IIdleSettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IIdleSettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IIdleSettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IIdleSettings *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IIdleSettings *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IIdleSettings *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IIdleSettings *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IIdleSettings *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IIdleSettings methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IdleDuration)(
        IIdleSettings *This,
        BSTR *delay);

    HRESULT (STDMETHODCALLTYPE *put_IdleDuration)(
        IIdleSettings *This,
        BSTR delay);

    HRESULT (STDMETHODCALLTYPE *get_WaitTimeout)(
        IIdleSettings *This,
        BSTR *timeout);

    HRESULT (STDMETHODCALLTYPE *put_WaitTimeout)(
        IIdleSettings *This,
        BSTR timeout);

    HRESULT (STDMETHODCALLTYPE *get_StopOnIdleEnd)(
        IIdleSettings *This,
        VARIANT_BOOL *stop);

    HRESULT (STDMETHODCALLTYPE *put_StopOnIdleEnd)(
        IIdleSettings *This,
        VARIANT_BOOL stop);

    HRESULT (STDMETHODCALLTYPE *get_RestartOnIdle)(
        IIdleSettings *This,
        VARIANT_BOOL *restart);

    HRESULT (STDMETHODCALLTYPE *put_RestartOnIdle)(
        IIdleSettings *This,
        VARIANT_BOOL restart);

    END_INTERFACE
} IIdleSettingsVtbl;

interface IIdleSettings {
    CONST_VTBL IIdleSettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IIdleSettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IIdleSettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IIdleSettings_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IIdleSettings_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IIdleSettings_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IIdleSettings_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IIdleSettings_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IIdleSettings methods ***/
#define IIdleSettings_get_IdleDuration(This,delay) (This)->lpVtbl->get_IdleDuration(This,delay)
#define IIdleSettings_put_IdleDuration(This,delay) (This)->lpVtbl->put_IdleDuration(This,delay)
#define IIdleSettings_get_WaitTimeout(This,timeout) (This)->lpVtbl->get_WaitTimeout(This,timeout)
#define IIdleSettings_put_WaitTimeout(This,timeout) (This)->lpVtbl->put_WaitTimeout(This,timeout)
#define IIdleSettings_get_StopOnIdleEnd(This,stop) (This)->lpVtbl->get_StopOnIdleEnd(This,stop)
#define IIdleSettings_put_StopOnIdleEnd(This,stop) (This)->lpVtbl->put_StopOnIdleEnd(This,stop)
#define IIdleSettings_get_RestartOnIdle(This,restart) (This)->lpVtbl->get_RestartOnIdle(This,restart)
#define IIdleSettings_put_RestartOnIdle(This,restart) (This)->lpVtbl->put_RestartOnIdle(This,restart)
#else
/*** IUnknown methods ***/
static inline HRESULT IIdleSettings_QueryInterface(IIdleSettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IIdleSettings_AddRef(IIdleSettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IIdleSettings_Release(IIdleSettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IIdleSettings_GetTypeInfoCount(IIdleSettings* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IIdleSettings_GetTypeInfo(IIdleSettings* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IIdleSettings_GetIDsOfNames(IIdleSettings* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IIdleSettings_Invoke(IIdleSettings* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IIdleSettings methods ***/
static inline HRESULT IIdleSettings_get_IdleDuration(IIdleSettings* This,BSTR *delay) {
    return This->lpVtbl->get_IdleDuration(This,delay);
}
static inline HRESULT IIdleSettings_put_IdleDuration(IIdleSettings* This,BSTR delay) {
    return This->lpVtbl->put_IdleDuration(This,delay);
}
static inline HRESULT IIdleSettings_get_WaitTimeout(IIdleSettings* This,BSTR *timeout) {
    return This->lpVtbl->get_WaitTimeout(This,timeout);
}
static inline HRESULT IIdleSettings_put_WaitTimeout(IIdleSettings* This,BSTR timeout) {
    return This->lpVtbl->put_WaitTimeout(This,timeout);
}
static inline HRESULT IIdleSettings_get_StopOnIdleEnd(IIdleSettings* This,VARIANT_BOOL *stop) {
    return This->lpVtbl->get_StopOnIdleEnd(This,stop);
}
static inline HRESULT IIdleSettings_put_StopOnIdleEnd(IIdleSettings* This,VARIANT_BOOL stop) {
    return This->lpVtbl->put_StopOnIdleEnd(This,stop);
}
static inline HRESULT IIdleSettings_get_RestartOnIdle(IIdleSettings* This,VARIANT_BOOL *restart) {
    return This->lpVtbl->get_RestartOnIdle(This,restart);
}
static inline HRESULT IIdleSettings_put_RestartOnIdle(IIdleSettings* This,VARIANT_BOOL restart) {
    return This->lpVtbl->put_RestartOnIdle(This,restart);
}
#endif
#endif

#endif


#endif  /* __IIdleSettings_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRunningTask interface
 */
#ifndef __IRunningTask_INTERFACE_DEFINED__
#define __IRunningTask_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRunningTask, 0x653758fb, 0x7b9a, 0x4f1e, 0xa4,0x71, 0xbe,0xeb,0x8e,0x9b,0x83,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("653758fb-7b9a-4f1e-a471-beeb8e9b834e")
IRunningTask : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_InstanceGuid(
        BSTR *guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Path(
        BSTR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_State(
        TASK_STATE *state) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentAction(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Refresh(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_EnginePID(
        DWORD *pid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRunningTask, 0x653758fb, 0x7b9a, 0x4f1e, 0xa4,0x71, 0xbe,0xeb,0x8e,0x9b,0x83,0x4e)
#endif
#else
typedef struct IRunningTaskVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRunningTask *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRunningTask *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRunningTask *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRunningTask *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRunningTask *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRunningTask *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRunningTask *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRunningTask methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        IRunningTask *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_InstanceGuid)(
        IRunningTask *This,
        BSTR *guid);

    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IRunningTask *This,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *get_State)(
        IRunningTask *This,
        TASK_STATE *state);

    HRESULT (STDMETHODCALLTYPE *get_CurrentAction)(
        IRunningTask *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IRunningTask *This);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IRunningTask *This);

    HRESULT (STDMETHODCALLTYPE *get_EnginePID)(
        IRunningTask *This,
        DWORD *pid);

    END_INTERFACE
} IRunningTaskVtbl;

interface IRunningTask {
    CONST_VTBL IRunningTaskVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRunningTask_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRunningTask_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRunningTask_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRunningTask_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRunningTask_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRunningTask_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRunningTask_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRunningTask methods ***/
#define IRunningTask_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define IRunningTask_get_InstanceGuid(This,guid) (This)->lpVtbl->get_InstanceGuid(This,guid)
#define IRunningTask_get_Path(This,path) (This)->lpVtbl->get_Path(This,path)
#define IRunningTask_get_State(This,state) (This)->lpVtbl->get_State(This,state)
#define IRunningTask_get_CurrentAction(This,name) (This)->lpVtbl->get_CurrentAction(This,name)
#define IRunningTask_Stop(This) (This)->lpVtbl->Stop(This)
#define IRunningTask_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IRunningTask_get_EnginePID(This,pid) (This)->lpVtbl->get_EnginePID(This,pid)
#else
/*** IUnknown methods ***/
static inline HRESULT IRunningTask_QueryInterface(IRunningTask* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRunningTask_AddRef(IRunningTask* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRunningTask_Release(IRunningTask* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRunningTask_GetTypeInfoCount(IRunningTask* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRunningTask_GetTypeInfo(IRunningTask* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRunningTask_GetIDsOfNames(IRunningTask* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRunningTask_Invoke(IRunningTask* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRunningTask methods ***/
static inline HRESULT IRunningTask_get_Name(IRunningTask* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT IRunningTask_get_InstanceGuid(IRunningTask* This,BSTR *guid) {
    return This->lpVtbl->get_InstanceGuid(This,guid);
}
static inline HRESULT IRunningTask_get_Path(IRunningTask* This,BSTR *path) {
    return This->lpVtbl->get_Path(This,path);
}
static inline HRESULT IRunningTask_get_State(IRunningTask* This,TASK_STATE *state) {
    return This->lpVtbl->get_State(This,state);
}
static inline HRESULT IRunningTask_get_CurrentAction(IRunningTask* This,BSTR *name) {
    return This->lpVtbl->get_CurrentAction(This,name);
}
static inline HRESULT IRunningTask_Stop(IRunningTask* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IRunningTask_Refresh(IRunningTask* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IRunningTask_get_EnginePID(IRunningTask* This,DWORD *pid) {
    return This->lpVtbl->get_EnginePID(This,pid);
}
#endif
#endif

#endif


#endif  /* __IRunningTask_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRunningTaskCollection interface
 */
#ifndef __IRunningTaskCollection_INTERFACE_DEFINED__
#define __IRunningTaskCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRunningTaskCollection, 0x6a67614b, 0x6828, 0x4fec, 0xaa,0x54, 0x6d,0x52,0xe8,0xf1,0xf2,0xdb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6a67614b-6828-4fec-aa54-6d52e8f1f2db")
IRunningTaskCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        VARIANT index,
        IRunningTask **task) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **penum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRunningTaskCollection, 0x6a67614b, 0x6828, 0x4fec, 0xaa,0x54, 0x6d,0x52,0xe8,0xf1,0xf2,0xdb)
#endif
#else
typedef struct IRunningTaskCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRunningTaskCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRunningTaskCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRunningTaskCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRunningTaskCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRunningTaskCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRunningTaskCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRunningTaskCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRunningTaskCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IRunningTaskCollection *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IRunningTaskCollection *This,
        VARIANT index,
        IRunningTask **task);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IRunningTaskCollection *This,
        IUnknown **penum);

    END_INTERFACE
} IRunningTaskCollectionVtbl;

interface IRunningTaskCollection {
    CONST_VTBL IRunningTaskCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRunningTaskCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRunningTaskCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRunningTaskCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRunningTaskCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRunningTaskCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRunningTaskCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRunningTaskCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRunningTaskCollection methods ***/
#define IRunningTaskCollection_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define IRunningTaskCollection_get_Item(This,index,task) (This)->lpVtbl->get_Item(This,index,task)
#define IRunningTaskCollection_get__NewEnum(This,penum) (This)->lpVtbl->get__NewEnum(This,penum)
#else
/*** IUnknown methods ***/
static inline HRESULT IRunningTaskCollection_QueryInterface(IRunningTaskCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRunningTaskCollection_AddRef(IRunningTaskCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRunningTaskCollection_Release(IRunningTaskCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRunningTaskCollection_GetTypeInfoCount(IRunningTaskCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRunningTaskCollection_GetTypeInfo(IRunningTaskCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRunningTaskCollection_GetIDsOfNames(IRunningTaskCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRunningTaskCollection_Invoke(IRunningTaskCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRunningTaskCollection methods ***/
static inline HRESULT IRunningTaskCollection_get_Count(IRunningTaskCollection* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static inline HRESULT IRunningTaskCollection_get_Item(IRunningTaskCollection* This,VARIANT index,IRunningTask **task) {
    return This->lpVtbl->get_Item(This,index,task);
}
static inline HRESULT IRunningTaskCollection_get__NewEnum(IRunningTaskCollection* This,IUnknown **penum) {
    return This->lpVtbl->get__NewEnum(This,penum);
}
#endif
#endif

#endif


#endif  /* __IRunningTaskCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITaskNamedValuePair interface
 */
#ifndef __ITaskNamedValuePair_INTERFACE_DEFINED__
#define __ITaskNamedValuePair_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITaskNamedValuePair, 0x39038068, 0x2b46, 0x4afd, 0x86,0x62, 0x7b,0xb6,0xf8,0x68,0xd2,0x21);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("39038068-2b46-4afd-8662-7bb6f868d221")
ITaskNamedValuePair : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *pName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Value(
        BSTR *pValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Value(
        BSTR value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITaskNamedValuePair, 0x39038068, 0x2b46, 0x4afd, 0x86,0x62, 0x7b,0xb6,0xf8,0x68,0xd2,0x21)
#endif
#else
typedef struct ITaskNamedValuePairVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITaskNamedValuePair *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITaskNamedValuePair *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITaskNamedValuePair *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITaskNamedValuePair *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITaskNamedValuePair *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITaskNamedValuePair *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITaskNamedValuePair *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITaskNamedValuePair methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        ITaskNamedValuePair *This,
        BSTR *pName);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        ITaskNamedValuePair *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        ITaskNamedValuePair *This,
        BSTR *pValue);

    HRESULT (STDMETHODCALLTYPE *put_Value)(
        ITaskNamedValuePair *This,
        BSTR value);

    END_INTERFACE
} ITaskNamedValuePairVtbl;

interface ITaskNamedValuePair {
    CONST_VTBL ITaskNamedValuePairVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITaskNamedValuePair_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITaskNamedValuePair_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITaskNamedValuePair_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ITaskNamedValuePair_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ITaskNamedValuePair_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ITaskNamedValuePair_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ITaskNamedValuePair_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITaskNamedValuePair methods ***/
#define ITaskNamedValuePair_get_Name(This,pName) (This)->lpVtbl->get_Name(This,pName)
#define ITaskNamedValuePair_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define ITaskNamedValuePair_get_Value(This,pValue) (This)->lpVtbl->get_Value(This,pValue)
#define ITaskNamedValuePair_put_Value(This,value) (This)->lpVtbl->put_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT ITaskNamedValuePair_QueryInterface(ITaskNamedValuePair* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITaskNamedValuePair_AddRef(ITaskNamedValuePair* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITaskNamedValuePair_Release(ITaskNamedValuePair* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ITaskNamedValuePair_GetTypeInfoCount(ITaskNamedValuePair* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ITaskNamedValuePair_GetTypeInfo(ITaskNamedValuePair* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ITaskNamedValuePair_GetIDsOfNames(ITaskNamedValuePair* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ITaskNamedValuePair_Invoke(ITaskNamedValuePair* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITaskNamedValuePair methods ***/
static inline HRESULT ITaskNamedValuePair_get_Name(ITaskNamedValuePair* This,BSTR *pName) {
    return This->lpVtbl->get_Name(This,pName);
}
static inline HRESULT ITaskNamedValuePair_put_Name(ITaskNamedValuePair* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT ITaskNamedValuePair_get_Value(ITaskNamedValuePair* This,BSTR *pValue) {
    return This->lpVtbl->get_Value(This,pValue);
}
static inline HRESULT ITaskNamedValuePair_put_Value(ITaskNamedValuePair* This,BSTR value) {
    return This->lpVtbl->put_Value(This,value);
}
#endif
#endif

#endif


#endif  /* __ITaskNamedValuePair_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITaskNamedValueCollection interface
 */
#ifndef __ITaskNamedValueCollection_INTERFACE_DEFINED__
#define __ITaskNamedValueCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITaskNamedValueCollection, 0xb4ef826b, 0x63c3, 0x46e4, 0xa5,0x04, 0xef,0x69,0xe4,0xf7,0xea,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b4ef826b-63c3-46e4-a504-ef69e4f7ea4d")
ITaskNamedValueCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        VARIANT index,
        ITaskNamedValuePair **pair) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **penum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Create(
        BSTR name,
        BSTR value,
        ITaskNamedValuePair **pair) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        LONG index) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITaskNamedValueCollection, 0xb4ef826b, 0x63c3, 0x46e4, 0xa5,0x04, 0xef,0x69,0xe4,0xf7,0xea,0x4d)
#endif
#else
typedef struct ITaskNamedValueCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITaskNamedValueCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITaskNamedValueCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITaskNamedValueCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITaskNamedValueCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITaskNamedValueCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITaskNamedValueCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITaskNamedValueCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITaskNamedValueCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ITaskNamedValueCollection *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        ITaskNamedValueCollection *This,
        VARIANT index,
        ITaskNamedValuePair **pair);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ITaskNamedValueCollection *This,
        IUnknown **penum);

    HRESULT (STDMETHODCALLTYPE *Create)(
        ITaskNamedValueCollection *This,
        BSTR name,
        BSTR value,
        ITaskNamedValuePair **pair);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        ITaskNamedValueCollection *This,
        LONG index);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        ITaskNamedValueCollection *This);

    END_INTERFACE
} ITaskNamedValueCollectionVtbl;

interface ITaskNamedValueCollection {
    CONST_VTBL ITaskNamedValueCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITaskNamedValueCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITaskNamedValueCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITaskNamedValueCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ITaskNamedValueCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ITaskNamedValueCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ITaskNamedValueCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ITaskNamedValueCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITaskNamedValueCollection methods ***/
#define ITaskNamedValueCollection_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define ITaskNamedValueCollection_get_Item(This,index,pair) (This)->lpVtbl->get_Item(This,index,pair)
#define ITaskNamedValueCollection_get__NewEnum(This,penum) (This)->lpVtbl->get__NewEnum(This,penum)
#define ITaskNamedValueCollection_Create(This,name,value,pair) (This)->lpVtbl->Create(This,name,value,pair)
#define ITaskNamedValueCollection_Remove(This,index) (This)->lpVtbl->Remove(This,index)
#define ITaskNamedValueCollection_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ITaskNamedValueCollection_QueryInterface(ITaskNamedValueCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITaskNamedValueCollection_AddRef(ITaskNamedValueCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITaskNamedValueCollection_Release(ITaskNamedValueCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ITaskNamedValueCollection_GetTypeInfoCount(ITaskNamedValueCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ITaskNamedValueCollection_GetTypeInfo(ITaskNamedValueCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ITaskNamedValueCollection_GetIDsOfNames(ITaskNamedValueCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ITaskNamedValueCollection_Invoke(ITaskNamedValueCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITaskNamedValueCollection methods ***/
static inline HRESULT ITaskNamedValueCollection_get_Count(ITaskNamedValueCollection* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static inline HRESULT ITaskNamedValueCollection_get_Item(ITaskNamedValueCollection* This,VARIANT index,ITaskNamedValuePair **pair) {
    return This->lpVtbl->get_Item(This,index,pair);
}
static inline HRESULT ITaskNamedValueCollection_get__NewEnum(ITaskNamedValueCollection* This,IUnknown **penum) {
    return This->lpVtbl->get__NewEnum(This,penum);
}
static inline HRESULT ITaskNamedValueCollection_Create(ITaskNamedValueCollection* This,BSTR name,BSTR value,ITaskNamedValuePair **pair) {
    return This->lpVtbl->Create(This,name,value,pair);
}
static inline HRESULT ITaskNamedValueCollection_Remove(ITaskNamedValueCollection* This,LONG index) {
    return This->lpVtbl->Remove(This,index);
}
static inline HRESULT ITaskNamedValueCollection_Clear(ITaskNamedValueCollection* This) {
    return This->lpVtbl->Clear(This);
}
#endif
#endif

#endif


#endif  /* __ITaskNamedValueCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITrigger interface
 */
#ifndef __ITrigger_INTERFACE_DEFINED__
#define __ITrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITrigger, 0x09941815, 0xea89, 0x4b5b, 0x89,0xe0, 0x2a,0x77,0x38,0x01,0xfa,0xc3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("09941815-ea89-4b5b-89e0-2a773801fac3")
ITrigger : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Type(
        TASK_TRIGGER_TYPE2 *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Id(
        BSTR *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Id(
        BSTR id) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Repetition(
        IRepetitionPattern **repeat) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Repetition(
        IRepetitionPattern *repeat) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ExecutionTimeLimit(
        BSTR *limit) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ExecutionTimeLimit(
        BSTR limit) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StartBoundary(
        BSTR *start) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_StartBoundary(
        BSTR start) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_EndBoundary(
        BSTR *end) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_EndBoundary(
        BSTR end) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Enabled(
        VARIANT_BOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Enabled(
        VARIANT_BOOL enabled) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITrigger, 0x09941815, 0xea89, 0x4b5b, 0x89,0xe0, 0x2a,0x77,0x38,0x01,0xfa,0xc3)
#endif
#else
typedef struct ITriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        ITrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        ITrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        ITrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        ITrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        ITrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        ITrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        ITrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        ITrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        ITrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        ITrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        ITrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        ITrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        ITrigger *This,
        VARIANT_BOOL enabled);

    END_INTERFACE
} ITriggerVtbl;

interface ITrigger {
    CONST_VTBL ITriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ITrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ITrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ITrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ITrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define ITrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define ITrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define ITrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define ITrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define ITrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define ITrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define ITrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define ITrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define ITrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define ITrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define ITrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define ITrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define ITrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#else
/*** IUnknown methods ***/
static inline HRESULT ITrigger_QueryInterface(ITrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITrigger_AddRef(ITrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITrigger_Release(ITrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ITrigger_GetTypeInfoCount(ITrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ITrigger_GetTypeInfo(ITrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ITrigger_GetIDsOfNames(ITrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ITrigger_Invoke(ITrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT ITrigger_get_Type(ITrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT ITrigger_get_Id(ITrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT ITrigger_put_Id(ITrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT ITrigger_get_Repetition(ITrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT ITrigger_put_Repetition(ITrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT ITrigger_get_ExecutionTimeLimit(ITrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT ITrigger_put_ExecutionTimeLimit(ITrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT ITrigger_get_StartBoundary(ITrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT ITrigger_put_StartBoundary(ITrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT ITrigger_get_EndBoundary(ITrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT ITrigger_put_EndBoundary(ITrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT ITrigger_get_Enabled(ITrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT ITrigger_put_Enabled(ITrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
#endif
#endif

#endif


#endif  /* __ITrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIdleTrigger interface
 */
#ifndef __IIdleTrigger_INTERFACE_DEFINED__
#define __IIdleTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_IIdleTrigger, 0xd537d2b0, 0x9fb3, 0x4d34, 0x97,0x39, 0x1f,0xf5,0xce,0x7b,0x1e,0xf3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d537d2b0-9fb3-4d34-9739-1ff5ce7b1ef3")
IIdleTrigger : public ITrigger
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IIdleTrigger, 0xd537d2b0, 0x9fb3, 0x4d34, 0x97,0x39, 0x1f,0xf5,0xce,0x7b,0x1e,0xf3)
#endif
#else
typedef struct IIdleTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IIdleTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IIdleTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IIdleTrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IIdleTrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IIdleTrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IIdleTrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IIdleTrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IIdleTrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IIdleTrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        IIdleTrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        IIdleTrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        IIdleTrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        IIdleTrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        IIdleTrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        IIdleTrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        IIdleTrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        IIdleTrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        IIdleTrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IIdleTrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IIdleTrigger *This,
        VARIANT_BOOL enabled);

    END_INTERFACE
} IIdleTriggerVtbl;

interface IIdleTrigger {
    CONST_VTBL IIdleTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IIdleTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IIdleTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IIdleTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IIdleTrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IIdleTrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IIdleTrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IIdleTrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define IIdleTrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define IIdleTrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IIdleTrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define IIdleTrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define IIdleTrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define IIdleTrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define IIdleTrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define IIdleTrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define IIdleTrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define IIdleTrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define IIdleTrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define IIdleTrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define IIdleTrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
#else
/*** IUnknown methods ***/
static inline HRESULT IIdleTrigger_QueryInterface(IIdleTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IIdleTrigger_AddRef(IIdleTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IIdleTrigger_Release(IIdleTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IIdleTrigger_GetTypeInfoCount(IIdleTrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IIdleTrigger_GetTypeInfo(IIdleTrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IIdleTrigger_GetIDsOfNames(IIdleTrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IIdleTrigger_Invoke(IIdleTrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT IIdleTrigger_get_Type(IIdleTrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT IIdleTrigger_get_Id(IIdleTrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IIdleTrigger_put_Id(IIdleTrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT IIdleTrigger_get_Repetition(IIdleTrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT IIdleTrigger_put_Repetition(IIdleTrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT IIdleTrigger_get_ExecutionTimeLimit(IIdleTrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IIdleTrigger_put_ExecutionTimeLimit(IIdleTrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IIdleTrigger_get_StartBoundary(IIdleTrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT IIdleTrigger_put_StartBoundary(IIdleTrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT IIdleTrigger_get_EndBoundary(IIdleTrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT IIdleTrigger_put_EndBoundary(IIdleTrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT IIdleTrigger_get_Enabled(IIdleTrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT IIdleTrigger_put_Enabled(IIdleTrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
#endif
#endif

#endif


#endif  /* __IIdleTrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ILogonTrigger interface
 */
#ifndef __ILogonTrigger_INTERFACE_DEFINED__
#define __ILogonTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_ILogonTrigger, 0x72dade38, 0xfae4, 0x4b3e, 0xba,0xf4, 0x5d,0x00,0x9a,0xf0,0x2b,0x1c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("72dade38-fae4-4b3e-baf4-5d009af02b1c")
ILogonTrigger : public ITrigger
{
    virtual HRESULT STDMETHODCALLTYPE get_Delay(
        BSTR *pDelay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Delay(
        BSTR delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UserId(
        BSTR *pUser) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_UserId(
        BSTR user) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ILogonTrigger, 0x72dade38, 0xfae4, 0x4b3e, 0xba,0xf4, 0x5d,0x00,0x9a,0xf0,0x2b,0x1c)
#endif
#else
typedef struct ILogonTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ILogonTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ILogonTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ILogonTrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ILogonTrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ILogonTrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ILogonTrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ILogonTrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        ILogonTrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        ILogonTrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        ILogonTrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        ILogonTrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        ILogonTrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        ILogonTrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        ILogonTrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        ILogonTrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        ILogonTrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        ILogonTrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        ILogonTrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        ILogonTrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        ILogonTrigger *This,
        VARIANT_BOOL enabled);

    /*** ILogonTrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Delay)(
        ILogonTrigger *This,
        BSTR *pDelay);

    HRESULT (STDMETHODCALLTYPE *put_Delay)(
        ILogonTrigger *This,
        BSTR delay);

    HRESULT (STDMETHODCALLTYPE *get_UserId)(
        ILogonTrigger *This,
        BSTR *pUser);

    HRESULT (STDMETHODCALLTYPE *put_UserId)(
        ILogonTrigger *This,
        BSTR user);

    END_INTERFACE
} ILogonTriggerVtbl;

interface ILogonTrigger {
    CONST_VTBL ILogonTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ILogonTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ILogonTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ILogonTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ILogonTrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ILogonTrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ILogonTrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ILogonTrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define ILogonTrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define ILogonTrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define ILogonTrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define ILogonTrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define ILogonTrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define ILogonTrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define ILogonTrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define ILogonTrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define ILogonTrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define ILogonTrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define ILogonTrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define ILogonTrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define ILogonTrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
/*** ILogonTrigger methods ***/
#define ILogonTrigger_get_Delay(This,pDelay) (This)->lpVtbl->get_Delay(This,pDelay)
#define ILogonTrigger_put_Delay(This,delay) (This)->lpVtbl->put_Delay(This,delay)
#define ILogonTrigger_get_UserId(This,pUser) (This)->lpVtbl->get_UserId(This,pUser)
#define ILogonTrigger_put_UserId(This,user) (This)->lpVtbl->put_UserId(This,user)
#else
/*** IUnknown methods ***/
static inline HRESULT ILogonTrigger_QueryInterface(ILogonTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ILogonTrigger_AddRef(ILogonTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ILogonTrigger_Release(ILogonTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ILogonTrigger_GetTypeInfoCount(ILogonTrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ILogonTrigger_GetTypeInfo(ILogonTrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ILogonTrigger_GetIDsOfNames(ILogonTrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ILogonTrigger_Invoke(ILogonTrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT ILogonTrigger_get_Type(ILogonTrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT ILogonTrigger_get_Id(ILogonTrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT ILogonTrigger_put_Id(ILogonTrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT ILogonTrigger_get_Repetition(ILogonTrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT ILogonTrigger_put_Repetition(ILogonTrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT ILogonTrigger_get_ExecutionTimeLimit(ILogonTrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT ILogonTrigger_put_ExecutionTimeLimit(ILogonTrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT ILogonTrigger_get_StartBoundary(ILogonTrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT ILogonTrigger_put_StartBoundary(ILogonTrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT ILogonTrigger_get_EndBoundary(ILogonTrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT ILogonTrigger_put_EndBoundary(ILogonTrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT ILogonTrigger_get_Enabled(ILogonTrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT ILogonTrigger_put_Enabled(ILogonTrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
/*** ILogonTrigger methods ***/
static inline HRESULT ILogonTrigger_get_Delay(ILogonTrigger* This,BSTR *pDelay) {
    return This->lpVtbl->get_Delay(This,pDelay);
}
static inline HRESULT ILogonTrigger_put_Delay(ILogonTrigger* This,BSTR delay) {
    return This->lpVtbl->put_Delay(This,delay);
}
static inline HRESULT ILogonTrigger_get_UserId(ILogonTrigger* This,BSTR *pUser) {
    return This->lpVtbl->get_UserId(This,pUser);
}
static inline HRESULT ILogonTrigger_put_UserId(ILogonTrigger* This,BSTR user) {
    return This->lpVtbl->put_UserId(This,user);
}
#endif
#endif

#endif


#endif  /* __ILogonTrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISessionStateChangeTrigger interface
 */
#ifndef __ISessionStateChangeTrigger_INTERFACE_DEFINED__
#define __ISessionStateChangeTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISessionStateChangeTrigger, 0x754da71b, 0x4385, 0x4475, 0x9d,0xd9, 0x59,0x82,0x94,0xfa,0x36,0x41);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("754da71b-4385-4475-9dd9-598294fa3641")
ISessionStateChangeTrigger : public ITrigger
{
    virtual HRESULT STDMETHODCALLTYPE get_Delay(
        BSTR *pDelay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Delay(
        BSTR delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UserId(
        BSTR *pUser) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_UserId(
        BSTR user) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StateChange(
        TASK_SESSION_STATE_CHANGE_TYPE *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_StateChange(
        TASK_SESSION_STATE_CHANGE_TYPE type) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISessionStateChangeTrigger, 0x754da71b, 0x4385, 0x4475, 0x9d,0xd9, 0x59,0x82,0x94,0xfa,0x36,0x41)
#endif
#else
typedef struct ISessionStateChangeTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISessionStateChangeTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISessionStateChangeTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISessionStateChangeTrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISessionStateChangeTrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISessionStateChangeTrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISessionStateChangeTrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISessionStateChangeTrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        ISessionStateChangeTrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        ISessionStateChangeTrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        ISessionStateChangeTrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        ISessionStateChangeTrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        ISessionStateChangeTrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        ISessionStateChangeTrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        ISessionStateChangeTrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        ISessionStateChangeTrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        ISessionStateChangeTrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        ISessionStateChangeTrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        ISessionStateChangeTrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        ISessionStateChangeTrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        ISessionStateChangeTrigger *This,
        VARIANT_BOOL enabled);

    /*** ISessionStateChangeTrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Delay)(
        ISessionStateChangeTrigger *This,
        BSTR *pDelay);

    HRESULT (STDMETHODCALLTYPE *put_Delay)(
        ISessionStateChangeTrigger *This,
        BSTR delay);

    HRESULT (STDMETHODCALLTYPE *get_UserId)(
        ISessionStateChangeTrigger *This,
        BSTR *pUser);

    HRESULT (STDMETHODCALLTYPE *put_UserId)(
        ISessionStateChangeTrigger *This,
        BSTR user);

    HRESULT (STDMETHODCALLTYPE *get_StateChange)(
        ISessionStateChangeTrigger *This,
        TASK_SESSION_STATE_CHANGE_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *put_StateChange)(
        ISessionStateChangeTrigger *This,
        TASK_SESSION_STATE_CHANGE_TYPE type);

    END_INTERFACE
} ISessionStateChangeTriggerVtbl;

interface ISessionStateChangeTrigger {
    CONST_VTBL ISessionStateChangeTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISessionStateChangeTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISessionStateChangeTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISessionStateChangeTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISessionStateChangeTrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISessionStateChangeTrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISessionStateChangeTrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISessionStateChangeTrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define ISessionStateChangeTrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define ISessionStateChangeTrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define ISessionStateChangeTrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define ISessionStateChangeTrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define ISessionStateChangeTrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define ISessionStateChangeTrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define ISessionStateChangeTrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define ISessionStateChangeTrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define ISessionStateChangeTrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define ISessionStateChangeTrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define ISessionStateChangeTrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define ISessionStateChangeTrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define ISessionStateChangeTrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
/*** ISessionStateChangeTrigger methods ***/
#define ISessionStateChangeTrigger_get_Delay(This,pDelay) (This)->lpVtbl->get_Delay(This,pDelay)
#define ISessionStateChangeTrigger_put_Delay(This,delay) (This)->lpVtbl->put_Delay(This,delay)
#define ISessionStateChangeTrigger_get_UserId(This,pUser) (This)->lpVtbl->get_UserId(This,pUser)
#define ISessionStateChangeTrigger_put_UserId(This,user) (This)->lpVtbl->put_UserId(This,user)
#define ISessionStateChangeTrigger_get_StateChange(This,pType) (This)->lpVtbl->get_StateChange(This,pType)
#define ISessionStateChangeTrigger_put_StateChange(This,type) (This)->lpVtbl->put_StateChange(This,type)
#else
/*** IUnknown methods ***/
static inline HRESULT ISessionStateChangeTrigger_QueryInterface(ISessionStateChangeTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISessionStateChangeTrigger_AddRef(ISessionStateChangeTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISessionStateChangeTrigger_Release(ISessionStateChangeTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISessionStateChangeTrigger_GetTypeInfoCount(ISessionStateChangeTrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISessionStateChangeTrigger_GetTypeInfo(ISessionStateChangeTrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISessionStateChangeTrigger_GetIDsOfNames(ISessionStateChangeTrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISessionStateChangeTrigger_Invoke(ISessionStateChangeTrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT ISessionStateChangeTrigger_get_Type(ISessionStateChangeTrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT ISessionStateChangeTrigger_get_Id(ISessionStateChangeTrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT ISessionStateChangeTrigger_put_Id(ISessionStateChangeTrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT ISessionStateChangeTrigger_get_Repetition(ISessionStateChangeTrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT ISessionStateChangeTrigger_put_Repetition(ISessionStateChangeTrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT ISessionStateChangeTrigger_get_ExecutionTimeLimit(ISessionStateChangeTrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT ISessionStateChangeTrigger_put_ExecutionTimeLimit(ISessionStateChangeTrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT ISessionStateChangeTrigger_get_StartBoundary(ISessionStateChangeTrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT ISessionStateChangeTrigger_put_StartBoundary(ISessionStateChangeTrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT ISessionStateChangeTrigger_get_EndBoundary(ISessionStateChangeTrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT ISessionStateChangeTrigger_put_EndBoundary(ISessionStateChangeTrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT ISessionStateChangeTrigger_get_Enabled(ISessionStateChangeTrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT ISessionStateChangeTrigger_put_Enabled(ISessionStateChangeTrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
/*** ISessionStateChangeTrigger methods ***/
static inline HRESULT ISessionStateChangeTrigger_get_Delay(ISessionStateChangeTrigger* This,BSTR *pDelay) {
    return This->lpVtbl->get_Delay(This,pDelay);
}
static inline HRESULT ISessionStateChangeTrigger_put_Delay(ISessionStateChangeTrigger* This,BSTR delay) {
    return This->lpVtbl->put_Delay(This,delay);
}
static inline HRESULT ISessionStateChangeTrigger_get_UserId(ISessionStateChangeTrigger* This,BSTR *pUser) {
    return This->lpVtbl->get_UserId(This,pUser);
}
static inline HRESULT ISessionStateChangeTrigger_put_UserId(ISessionStateChangeTrigger* This,BSTR user) {
    return This->lpVtbl->put_UserId(This,user);
}
static inline HRESULT ISessionStateChangeTrigger_get_StateChange(ISessionStateChangeTrigger* This,TASK_SESSION_STATE_CHANGE_TYPE *pType) {
    return This->lpVtbl->get_StateChange(This,pType);
}
static inline HRESULT ISessionStateChangeTrigger_put_StateChange(ISessionStateChangeTrigger* This,TASK_SESSION_STATE_CHANGE_TYPE type) {
    return This->lpVtbl->put_StateChange(This,type);
}
#endif
#endif

#endif


#endif  /* __ISessionStateChangeTrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEventTrigger interface
 */
#ifndef __IEventTrigger_INTERFACE_DEFINED__
#define __IEventTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEventTrigger, 0xd45b0167, 0x9653, 0x4eef, 0xb9,0x4f, 0x07,0x32,0xca,0x7a,0xf2,0x51);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d45b0167-9653-4eef-b94f-0732ca7af251")
IEventTrigger : public ITrigger
{
    virtual HRESULT STDMETHODCALLTYPE get_Subscription(
        BSTR *pQuery) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Subscription(
        BSTR query) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Delay(
        BSTR *pDelay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Delay(
        BSTR delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ValueQueries(
        ITaskNamedValueCollection **ppNamedXPaths) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ValueQueries(
        ITaskNamedValueCollection *pNamedXPaths) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEventTrigger, 0xd45b0167, 0x9653, 0x4eef, 0xb9,0x4f, 0x07,0x32,0xca,0x7a,0xf2,0x51)
#endif
#else
typedef struct IEventTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEventTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEventTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEventTrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IEventTrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IEventTrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IEventTrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IEventTrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IEventTrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IEventTrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        IEventTrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        IEventTrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        IEventTrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        IEventTrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        IEventTrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        IEventTrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        IEventTrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        IEventTrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        IEventTrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IEventTrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IEventTrigger *This,
        VARIANT_BOOL enabled);

    /*** IEventTrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Subscription)(
        IEventTrigger *This,
        BSTR *pQuery);

    HRESULT (STDMETHODCALLTYPE *put_Subscription)(
        IEventTrigger *This,
        BSTR query);

    HRESULT (STDMETHODCALLTYPE *get_Delay)(
        IEventTrigger *This,
        BSTR *pDelay);

    HRESULT (STDMETHODCALLTYPE *put_Delay)(
        IEventTrigger *This,
        BSTR delay);

    HRESULT (STDMETHODCALLTYPE *get_ValueQueries)(
        IEventTrigger *This,
        ITaskNamedValueCollection **ppNamedXPaths);

    HRESULT (STDMETHODCALLTYPE *put_ValueQueries)(
        IEventTrigger *This,
        ITaskNamedValueCollection *pNamedXPaths);

    END_INTERFACE
} IEventTriggerVtbl;

interface IEventTrigger {
    CONST_VTBL IEventTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEventTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEventTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEventTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IEventTrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IEventTrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IEventTrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IEventTrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define IEventTrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define IEventTrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IEventTrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define IEventTrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define IEventTrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define IEventTrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define IEventTrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define IEventTrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define IEventTrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define IEventTrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define IEventTrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define IEventTrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define IEventTrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
/*** IEventTrigger methods ***/
#define IEventTrigger_get_Subscription(This,pQuery) (This)->lpVtbl->get_Subscription(This,pQuery)
#define IEventTrigger_put_Subscription(This,query) (This)->lpVtbl->put_Subscription(This,query)
#define IEventTrigger_get_Delay(This,pDelay) (This)->lpVtbl->get_Delay(This,pDelay)
#define IEventTrigger_put_Delay(This,delay) (This)->lpVtbl->put_Delay(This,delay)
#define IEventTrigger_get_ValueQueries(This,ppNamedXPaths) (This)->lpVtbl->get_ValueQueries(This,ppNamedXPaths)
#define IEventTrigger_put_ValueQueries(This,pNamedXPaths) (This)->lpVtbl->put_ValueQueries(This,pNamedXPaths)
#else
/*** IUnknown methods ***/
static inline HRESULT IEventTrigger_QueryInterface(IEventTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEventTrigger_AddRef(IEventTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEventTrigger_Release(IEventTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IEventTrigger_GetTypeInfoCount(IEventTrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IEventTrigger_GetTypeInfo(IEventTrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IEventTrigger_GetIDsOfNames(IEventTrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IEventTrigger_Invoke(IEventTrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT IEventTrigger_get_Type(IEventTrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT IEventTrigger_get_Id(IEventTrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IEventTrigger_put_Id(IEventTrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT IEventTrigger_get_Repetition(IEventTrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT IEventTrigger_put_Repetition(IEventTrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT IEventTrigger_get_ExecutionTimeLimit(IEventTrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IEventTrigger_put_ExecutionTimeLimit(IEventTrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IEventTrigger_get_StartBoundary(IEventTrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT IEventTrigger_put_StartBoundary(IEventTrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT IEventTrigger_get_EndBoundary(IEventTrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT IEventTrigger_put_EndBoundary(IEventTrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT IEventTrigger_get_Enabled(IEventTrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT IEventTrigger_put_Enabled(IEventTrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
/*** IEventTrigger methods ***/
static inline HRESULT IEventTrigger_get_Subscription(IEventTrigger* This,BSTR *pQuery) {
    return This->lpVtbl->get_Subscription(This,pQuery);
}
static inline HRESULT IEventTrigger_put_Subscription(IEventTrigger* This,BSTR query) {
    return This->lpVtbl->put_Subscription(This,query);
}
static inline HRESULT IEventTrigger_get_Delay(IEventTrigger* This,BSTR *pDelay) {
    return This->lpVtbl->get_Delay(This,pDelay);
}
static inline HRESULT IEventTrigger_put_Delay(IEventTrigger* This,BSTR delay) {
    return This->lpVtbl->put_Delay(This,delay);
}
static inline HRESULT IEventTrigger_get_ValueQueries(IEventTrigger* This,ITaskNamedValueCollection **ppNamedXPaths) {
    return This->lpVtbl->get_ValueQueries(This,ppNamedXPaths);
}
static inline HRESULT IEventTrigger_put_ValueQueries(IEventTrigger* This,ITaskNamedValueCollection *pNamedXPaths) {
    return This->lpVtbl->put_ValueQueries(This,pNamedXPaths);
}
#endif
#endif

#endif


#endif  /* __IEventTrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITimeTrigger interface
 */
#ifndef __ITimeTrigger_INTERFACE_DEFINED__
#define __ITimeTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITimeTrigger, 0xb45747e0, 0xeba7, 0x4276, 0x9f,0x29, 0x85,0xc5,0xbb,0x30,0x00,0x06);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b45747e0-eba7-4276-9f29-85c5bb300006")
ITimeTrigger : public ITrigger
{
    virtual HRESULT STDMETHODCALLTYPE get_RandomDelay(
        BSTR *delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RandomDelay(
        BSTR delay) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITimeTrigger, 0xb45747e0, 0xeba7, 0x4276, 0x9f,0x29, 0x85,0xc5,0xbb,0x30,0x00,0x06)
#endif
#else
typedef struct ITimeTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITimeTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITimeTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITimeTrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITimeTrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITimeTrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITimeTrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITimeTrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        ITimeTrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        ITimeTrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        ITimeTrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        ITimeTrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        ITimeTrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        ITimeTrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        ITimeTrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        ITimeTrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        ITimeTrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        ITimeTrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        ITimeTrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        ITimeTrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        ITimeTrigger *This,
        VARIANT_BOOL enabled);

    /*** ITimeTrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_RandomDelay)(
        ITimeTrigger *This,
        BSTR *delay);

    HRESULT (STDMETHODCALLTYPE *put_RandomDelay)(
        ITimeTrigger *This,
        BSTR delay);

    END_INTERFACE
} ITimeTriggerVtbl;

interface ITimeTrigger {
    CONST_VTBL ITimeTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITimeTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITimeTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITimeTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ITimeTrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ITimeTrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ITimeTrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ITimeTrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define ITimeTrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define ITimeTrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define ITimeTrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define ITimeTrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define ITimeTrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define ITimeTrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define ITimeTrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define ITimeTrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define ITimeTrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define ITimeTrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define ITimeTrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define ITimeTrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define ITimeTrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
/*** ITimeTrigger methods ***/
#define ITimeTrigger_get_RandomDelay(This,delay) (This)->lpVtbl->get_RandomDelay(This,delay)
#define ITimeTrigger_put_RandomDelay(This,delay) (This)->lpVtbl->put_RandomDelay(This,delay)
#else
/*** IUnknown methods ***/
static inline HRESULT ITimeTrigger_QueryInterface(ITimeTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITimeTrigger_AddRef(ITimeTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITimeTrigger_Release(ITimeTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ITimeTrigger_GetTypeInfoCount(ITimeTrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ITimeTrigger_GetTypeInfo(ITimeTrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ITimeTrigger_GetIDsOfNames(ITimeTrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ITimeTrigger_Invoke(ITimeTrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT ITimeTrigger_get_Type(ITimeTrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT ITimeTrigger_get_Id(ITimeTrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT ITimeTrigger_put_Id(ITimeTrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT ITimeTrigger_get_Repetition(ITimeTrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT ITimeTrigger_put_Repetition(ITimeTrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT ITimeTrigger_get_ExecutionTimeLimit(ITimeTrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT ITimeTrigger_put_ExecutionTimeLimit(ITimeTrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT ITimeTrigger_get_StartBoundary(ITimeTrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT ITimeTrigger_put_StartBoundary(ITimeTrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT ITimeTrigger_get_EndBoundary(ITimeTrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT ITimeTrigger_put_EndBoundary(ITimeTrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT ITimeTrigger_get_Enabled(ITimeTrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT ITimeTrigger_put_Enabled(ITimeTrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
/*** ITimeTrigger methods ***/
static inline HRESULT ITimeTrigger_get_RandomDelay(ITimeTrigger* This,BSTR *delay) {
    return This->lpVtbl->get_RandomDelay(This,delay);
}
static inline HRESULT ITimeTrigger_put_RandomDelay(ITimeTrigger* This,BSTR delay) {
    return This->lpVtbl->put_RandomDelay(This,delay);
}
#endif
#endif

#endif


#endif  /* __ITimeTrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDailyTrigger interface
 */
#ifndef __IDailyTrigger_INTERFACE_DEFINED__
#define __IDailyTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDailyTrigger, 0x126c5cd8, 0xb288, 0x41d5, 0x8d,0xbf, 0xe4,0x91,0x44,0x6a,0xdc,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("126c5cd8-b288-41d5-8dbf-e491446adc5c")
IDailyTrigger : public ITrigger
{
    virtual HRESULT STDMETHODCALLTYPE get_DaysInterval(
        short *pDays) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DaysInterval(
        short days) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RandomDelay(
        BSTR *pRandomDelay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RandomDelay(
        BSTR randomDelay) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDailyTrigger, 0x126c5cd8, 0xb288, 0x41d5, 0x8d,0xbf, 0xe4,0x91,0x44,0x6a,0xdc,0x5c)
#endif
#else
typedef struct IDailyTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDailyTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDailyTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDailyTrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IDailyTrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IDailyTrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IDailyTrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IDailyTrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IDailyTrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IDailyTrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        IDailyTrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        IDailyTrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        IDailyTrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        IDailyTrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        IDailyTrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        IDailyTrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        IDailyTrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        IDailyTrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        IDailyTrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IDailyTrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IDailyTrigger *This,
        VARIANT_BOOL enabled);

    /*** IDailyTrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DaysInterval)(
        IDailyTrigger *This,
        short *pDays);

    HRESULT (STDMETHODCALLTYPE *put_DaysInterval)(
        IDailyTrigger *This,
        short days);

    HRESULT (STDMETHODCALLTYPE *get_RandomDelay)(
        IDailyTrigger *This,
        BSTR *pRandomDelay);

    HRESULT (STDMETHODCALLTYPE *put_RandomDelay)(
        IDailyTrigger *This,
        BSTR randomDelay);

    END_INTERFACE
} IDailyTriggerVtbl;

interface IDailyTrigger {
    CONST_VTBL IDailyTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDailyTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDailyTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDailyTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IDailyTrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IDailyTrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IDailyTrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IDailyTrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define IDailyTrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define IDailyTrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IDailyTrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define IDailyTrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define IDailyTrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define IDailyTrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define IDailyTrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define IDailyTrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define IDailyTrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define IDailyTrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define IDailyTrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define IDailyTrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define IDailyTrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
/*** IDailyTrigger methods ***/
#define IDailyTrigger_get_DaysInterval(This,pDays) (This)->lpVtbl->get_DaysInterval(This,pDays)
#define IDailyTrigger_put_DaysInterval(This,days) (This)->lpVtbl->put_DaysInterval(This,days)
#define IDailyTrigger_get_RandomDelay(This,pRandomDelay) (This)->lpVtbl->get_RandomDelay(This,pRandomDelay)
#define IDailyTrigger_put_RandomDelay(This,randomDelay) (This)->lpVtbl->put_RandomDelay(This,randomDelay)
#else
/*** IUnknown methods ***/
static inline HRESULT IDailyTrigger_QueryInterface(IDailyTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDailyTrigger_AddRef(IDailyTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDailyTrigger_Release(IDailyTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IDailyTrigger_GetTypeInfoCount(IDailyTrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IDailyTrigger_GetTypeInfo(IDailyTrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IDailyTrigger_GetIDsOfNames(IDailyTrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IDailyTrigger_Invoke(IDailyTrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT IDailyTrigger_get_Type(IDailyTrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT IDailyTrigger_get_Id(IDailyTrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IDailyTrigger_put_Id(IDailyTrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT IDailyTrigger_get_Repetition(IDailyTrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT IDailyTrigger_put_Repetition(IDailyTrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT IDailyTrigger_get_ExecutionTimeLimit(IDailyTrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IDailyTrigger_put_ExecutionTimeLimit(IDailyTrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IDailyTrigger_get_StartBoundary(IDailyTrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT IDailyTrigger_put_StartBoundary(IDailyTrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT IDailyTrigger_get_EndBoundary(IDailyTrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT IDailyTrigger_put_EndBoundary(IDailyTrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT IDailyTrigger_get_Enabled(IDailyTrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT IDailyTrigger_put_Enabled(IDailyTrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
/*** IDailyTrigger methods ***/
static inline HRESULT IDailyTrigger_get_DaysInterval(IDailyTrigger* This,short *pDays) {
    return This->lpVtbl->get_DaysInterval(This,pDays);
}
static inline HRESULT IDailyTrigger_put_DaysInterval(IDailyTrigger* This,short days) {
    return This->lpVtbl->put_DaysInterval(This,days);
}
static inline HRESULT IDailyTrigger_get_RandomDelay(IDailyTrigger* This,BSTR *pRandomDelay) {
    return This->lpVtbl->get_RandomDelay(This,pRandomDelay);
}
static inline HRESULT IDailyTrigger_put_RandomDelay(IDailyTrigger* This,BSTR randomDelay) {
    return This->lpVtbl->put_RandomDelay(This,randomDelay);
}
#endif
#endif

#endif


#endif  /* __IDailyTrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWeeklyTrigger interface
 */
#ifndef __IWeeklyTrigger_INTERFACE_DEFINED__
#define __IWeeklyTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWeeklyTrigger, 0x5038fc98, 0x82ff, 0x436d, 0x87,0x28, 0xa5,0x12,0xa5,0x7c,0x9d,0xc1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5038fc98-82ff-436d-8728-a512a57c9dc1")
IWeeklyTrigger : public ITrigger
{
    virtual HRESULT STDMETHODCALLTYPE get_DaysOfWeek(
        short *pDays) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DaysOfWeek(
        short days) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WeeksInterval(
        short *pWeeks) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_WeeksInterval(
        short weeks) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RandomDelay(
        BSTR *pRandomDelay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RandomDelay(
        BSTR randomDelay) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWeeklyTrigger, 0x5038fc98, 0x82ff, 0x436d, 0x87,0x28, 0xa5,0x12,0xa5,0x7c,0x9d,0xc1)
#endif
#else
typedef struct IWeeklyTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWeeklyTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWeeklyTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWeeklyTrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWeeklyTrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWeeklyTrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWeeklyTrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWeeklyTrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IWeeklyTrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IWeeklyTrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        IWeeklyTrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        IWeeklyTrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        IWeeklyTrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        IWeeklyTrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        IWeeklyTrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        IWeeklyTrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        IWeeklyTrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        IWeeklyTrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        IWeeklyTrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IWeeklyTrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IWeeklyTrigger *This,
        VARIANT_BOOL enabled);

    /*** IWeeklyTrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DaysOfWeek)(
        IWeeklyTrigger *This,
        short *pDays);

    HRESULT (STDMETHODCALLTYPE *put_DaysOfWeek)(
        IWeeklyTrigger *This,
        short days);

    HRESULT (STDMETHODCALLTYPE *get_WeeksInterval)(
        IWeeklyTrigger *This,
        short *pWeeks);

    HRESULT (STDMETHODCALLTYPE *put_WeeksInterval)(
        IWeeklyTrigger *This,
        short weeks);

    HRESULT (STDMETHODCALLTYPE *get_RandomDelay)(
        IWeeklyTrigger *This,
        BSTR *pRandomDelay);

    HRESULT (STDMETHODCALLTYPE *put_RandomDelay)(
        IWeeklyTrigger *This,
        BSTR randomDelay);

    END_INTERFACE
} IWeeklyTriggerVtbl;

interface IWeeklyTrigger {
    CONST_VTBL IWeeklyTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWeeklyTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWeeklyTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWeeklyTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWeeklyTrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWeeklyTrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWeeklyTrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWeeklyTrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define IWeeklyTrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define IWeeklyTrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IWeeklyTrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define IWeeklyTrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define IWeeklyTrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define IWeeklyTrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define IWeeklyTrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define IWeeklyTrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define IWeeklyTrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define IWeeklyTrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define IWeeklyTrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define IWeeklyTrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define IWeeklyTrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
/*** IWeeklyTrigger methods ***/
#define IWeeklyTrigger_get_DaysOfWeek(This,pDays) (This)->lpVtbl->get_DaysOfWeek(This,pDays)
#define IWeeklyTrigger_put_DaysOfWeek(This,days) (This)->lpVtbl->put_DaysOfWeek(This,days)
#define IWeeklyTrigger_get_WeeksInterval(This,pWeeks) (This)->lpVtbl->get_WeeksInterval(This,pWeeks)
#define IWeeklyTrigger_put_WeeksInterval(This,weeks) (This)->lpVtbl->put_WeeksInterval(This,weeks)
#define IWeeklyTrigger_get_RandomDelay(This,pRandomDelay) (This)->lpVtbl->get_RandomDelay(This,pRandomDelay)
#define IWeeklyTrigger_put_RandomDelay(This,randomDelay) (This)->lpVtbl->put_RandomDelay(This,randomDelay)
#else
/*** IUnknown methods ***/
static inline HRESULT IWeeklyTrigger_QueryInterface(IWeeklyTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWeeklyTrigger_AddRef(IWeeklyTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWeeklyTrigger_Release(IWeeklyTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWeeklyTrigger_GetTypeInfoCount(IWeeklyTrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWeeklyTrigger_GetTypeInfo(IWeeklyTrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWeeklyTrigger_GetIDsOfNames(IWeeklyTrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWeeklyTrigger_Invoke(IWeeklyTrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT IWeeklyTrigger_get_Type(IWeeklyTrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT IWeeklyTrigger_get_Id(IWeeklyTrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IWeeklyTrigger_put_Id(IWeeklyTrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT IWeeklyTrigger_get_Repetition(IWeeklyTrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT IWeeklyTrigger_put_Repetition(IWeeklyTrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT IWeeklyTrigger_get_ExecutionTimeLimit(IWeeklyTrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IWeeklyTrigger_put_ExecutionTimeLimit(IWeeklyTrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IWeeklyTrigger_get_StartBoundary(IWeeklyTrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT IWeeklyTrigger_put_StartBoundary(IWeeklyTrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT IWeeklyTrigger_get_EndBoundary(IWeeklyTrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT IWeeklyTrigger_put_EndBoundary(IWeeklyTrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT IWeeklyTrigger_get_Enabled(IWeeklyTrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT IWeeklyTrigger_put_Enabled(IWeeklyTrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
/*** IWeeklyTrigger methods ***/
static inline HRESULT IWeeklyTrigger_get_DaysOfWeek(IWeeklyTrigger* This,short *pDays) {
    return This->lpVtbl->get_DaysOfWeek(This,pDays);
}
static inline HRESULT IWeeklyTrigger_put_DaysOfWeek(IWeeklyTrigger* This,short days) {
    return This->lpVtbl->put_DaysOfWeek(This,days);
}
static inline HRESULT IWeeklyTrigger_get_WeeksInterval(IWeeklyTrigger* This,short *pWeeks) {
    return This->lpVtbl->get_WeeksInterval(This,pWeeks);
}
static inline HRESULT IWeeklyTrigger_put_WeeksInterval(IWeeklyTrigger* This,short weeks) {
    return This->lpVtbl->put_WeeksInterval(This,weeks);
}
static inline HRESULT IWeeklyTrigger_get_RandomDelay(IWeeklyTrigger* This,BSTR *pRandomDelay) {
    return This->lpVtbl->get_RandomDelay(This,pRandomDelay);
}
static inline HRESULT IWeeklyTrigger_put_RandomDelay(IWeeklyTrigger* This,BSTR randomDelay) {
    return This->lpVtbl->put_RandomDelay(This,randomDelay);
}
#endif
#endif

#endif


#endif  /* __IWeeklyTrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMonthlyTrigger interface
 */
#ifndef __IMonthlyTrigger_INTERFACE_DEFINED__
#define __IMonthlyTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMonthlyTrigger, 0x97c45ef1, 0x6b02, 0x4a1a, 0x9c,0x0e, 0x1e,0xbf,0xba,0x15,0x00,0xac);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("97c45ef1-6b02-4a1a-9c0e-1ebfba1500ac")
IMonthlyTrigger : public ITrigger
{
    virtual HRESULT STDMETHODCALLTYPE get_DaysOfMonth(
        short *pDays) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DaysOfMonth(
        short days) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MonthsOfYear(
        short *pMonths) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MonthsOfYear(
        short months) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RunOnLastDayOfMonth(
        VARIANT_BOOL *pLastDay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RunOnLastDayOfMonth(
        VARIANT_BOOL lastDay) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RandomDelay(
        BSTR *pRandomDelay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RandomDelay(
        BSTR randomDelay) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMonthlyTrigger, 0x97c45ef1, 0x6b02, 0x4a1a, 0x9c,0x0e, 0x1e,0xbf,0xba,0x15,0x00,0xac)
#endif
#else
typedef struct IMonthlyTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMonthlyTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMonthlyTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMonthlyTrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IMonthlyTrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IMonthlyTrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IMonthlyTrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IMonthlyTrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IMonthlyTrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IMonthlyTrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        IMonthlyTrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        IMonthlyTrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        IMonthlyTrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        IMonthlyTrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        IMonthlyTrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        IMonthlyTrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        IMonthlyTrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        IMonthlyTrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        IMonthlyTrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IMonthlyTrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IMonthlyTrigger *This,
        VARIANT_BOOL enabled);

    /*** IMonthlyTrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DaysOfMonth)(
        IMonthlyTrigger *This,
        short *pDays);

    HRESULT (STDMETHODCALLTYPE *put_DaysOfMonth)(
        IMonthlyTrigger *This,
        short days);

    HRESULT (STDMETHODCALLTYPE *get_MonthsOfYear)(
        IMonthlyTrigger *This,
        short *pMonths);

    HRESULT (STDMETHODCALLTYPE *put_MonthsOfYear)(
        IMonthlyTrigger *This,
        short months);

    HRESULT (STDMETHODCALLTYPE *get_RunOnLastDayOfMonth)(
        IMonthlyTrigger *This,
        VARIANT_BOOL *pLastDay);

    HRESULT (STDMETHODCALLTYPE *put_RunOnLastDayOfMonth)(
        IMonthlyTrigger *This,
        VARIANT_BOOL lastDay);

    HRESULT (STDMETHODCALLTYPE *get_RandomDelay)(
        IMonthlyTrigger *This,
        BSTR *pRandomDelay);

    HRESULT (STDMETHODCALLTYPE *put_RandomDelay)(
        IMonthlyTrigger *This,
        BSTR randomDelay);

    END_INTERFACE
} IMonthlyTriggerVtbl;

interface IMonthlyTrigger {
    CONST_VTBL IMonthlyTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMonthlyTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMonthlyTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMonthlyTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IMonthlyTrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMonthlyTrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMonthlyTrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMonthlyTrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define IMonthlyTrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define IMonthlyTrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IMonthlyTrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define IMonthlyTrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define IMonthlyTrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define IMonthlyTrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define IMonthlyTrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define IMonthlyTrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define IMonthlyTrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define IMonthlyTrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define IMonthlyTrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define IMonthlyTrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define IMonthlyTrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
/*** IMonthlyTrigger methods ***/
#define IMonthlyTrigger_get_DaysOfMonth(This,pDays) (This)->lpVtbl->get_DaysOfMonth(This,pDays)
#define IMonthlyTrigger_put_DaysOfMonth(This,days) (This)->lpVtbl->put_DaysOfMonth(This,days)
#define IMonthlyTrigger_get_MonthsOfYear(This,pMonths) (This)->lpVtbl->get_MonthsOfYear(This,pMonths)
#define IMonthlyTrigger_put_MonthsOfYear(This,months) (This)->lpVtbl->put_MonthsOfYear(This,months)
#define IMonthlyTrigger_get_RunOnLastDayOfMonth(This,pLastDay) (This)->lpVtbl->get_RunOnLastDayOfMonth(This,pLastDay)
#define IMonthlyTrigger_put_RunOnLastDayOfMonth(This,lastDay) (This)->lpVtbl->put_RunOnLastDayOfMonth(This,lastDay)
#define IMonthlyTrigger_get_RandomDelay(This,pRandomDelay) (This)->lpVtbl->get_RandomDelay(This,pRandomDelay)
#define IMonthlyTrigger_put_RandomDelay(This,randomDelay) (This)->lpVtbl->put_RandomDelay(This,randomDelay)
#else
/*** IUnknown methods ***/
static inline HRESULT IMonthlyTrigger_QueryInterface(IMonthlyTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMonthlyTrigger_AddRef(IMonthlyTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMonthlyTrigger_Release(IMonthlyTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IMonthlyTrigger_GetTypeInfoCount(IMonthlyTrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IMonthlyTrigger_GetTypeInfo(IMonthlyTrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IMonthlyTrigger_GetIDsOfNames(IMonthlyTrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IMonthlyTrigger_Invoke(IMonthlyTrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT IMonthlyTrigger_get_Type(IMonthlyTrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT IMonthlyTrigger_get_Id(IMonthlyTrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IMonthlyTrigger_put_Id(IMonthlyTrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT IMonthlyTrigger_get_Repetition(IMonthlyTrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT IMonthlyTrigger_put_Repetition(IMonthlyTrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT IMonthlyTrigger_get_ExecutionTimeLimit(IMonthlyTrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IMonthlyTrigger_put_ExecutionTimeLimit(IMonthlyTrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IMonthlyTrigger_get_StartBoundary(IMonthlyTrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT IMonthlyTrigger_put_StartBoundary(IMonthlyTrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT IMonthlyTrigger_get_EndBoundary(IMonthlyTrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT IMonthlyTrigger_put_EndBoundary(IMonthlyTrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT IMonthlyTrigger_get_Enabled(IMonthlyTrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT IMonthlyTrigger_put_Enabled(IMonthlyTrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
/*** IMonthlyTrigger methods ***/
static inline HRESULT IMonthlyTrigger_get_DaysOfMonth(IMonthlyTrigger* This,short *pDays) {
    return This->lpVtbl->get_DaysOfMonth(This,pDays);
}
static inline HRESULT IMonthlyTrigger_put_DaysOfMonth(IMonthlyTrigger* This,short days) {
    return This->lpVtbl->put_DaysOfMonth(This,days);
}
static inline HRESULT IMonthlyTrigger_get_MonthsOfYear(IMonthlyTrigger* This,short *pMonths) {
    return This->lpVtbl->get_MonthsOfYear(This,pMonths);
}
static inline HRESULT IMonthlyTrigger_put_MonthsOfYear(IMonthlyTrigger* This,short months) {
    return This->lpVtbl->put_MonthsOfYear(This,months);
}
static inline HRESULT IMonthlyTrigger_get_RunOnLastDayOfMonth(IMonthlyTrigger* This,VARIANT_BOOL *pLastDay) {
    return This->lpVtbl->get_RunOnLastDayOfMonth(This,pLastDay);
}
static inline HRESULT IMonthlyTrigger_put_RunOnLastDayOfMonth(IMonthlyTrigger* This,VARIANT_BOOL lastDay) {
    return This->lpVtbl->put_RunOnLastDayOfMonth(This,lastDay);
}
static inline HRESULT IMonthlyTrigger_get_RandomDelay(IMonthlyTrigger* This,BSTR *pRandomDelay) {
    return This->lpVtbl->get_RandomDelay(This,pRandomDelay);
}
static inline HRESULT IMonthlyTrigger_put_RandomDelay(IMonthlyTrigger* This,BSTR randomDelay) {
    return This->lpVtbl->put_RandomDelay(This,randomDelay);
}
#endif
#endif

#endif


#endif  /* __IMonthlyTrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMonthlyDOWTrigger interface
 */
#ifndef __IMonthlyDOWTrigger_INTERFACE_DEFINED__
#define __IMonthlyDOWTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMonthlyDOWTrigger, 0x77d025a3, 0x90fa, 0x43aa, 0xb5,0x2e, 0xcd,0xa5,0x49,0x9b,0x94,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("77d025a3-90fa-43aa-b52e-cda5499b946a")
IMonthlyDOWTrigger : public ITrigger
{
    virtual HRESULT STDMETHODCALLTYPE get_DaysOfWeek(
        short *pDays) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DaysOfWeek(
        short days) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WeeksOfMonth(
        short *pWeeks) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_WeeksOfMonth(
        short weeks) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MonthsOfYear(
        short *pMonths) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MonthsOfYear(
        short months) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RunOnLastWeekOfMonth(
        VARIANT_BOOL *pLastWeek) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RunOnLastWeekOfMonth(
        VARIANT_BOOL lastWeek) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RandomDelay(
        BSTR *pRandomDelay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RandomDelay(
        BSTR randomDelay) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMonthlyDOWTrigger, 0x77d025a3, 0x90fa, 0x43aa, 0xb5,0x2e, 0xcd,0xa5,0x49,0x9b,0x94,0x6a)
#endif
#else
typedef struct IMonthlyDOWTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMonthlyDOWTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMonthlyDOWTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMonthlyDOWTrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IMonthlyDOWTrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IMonthlyDOWTrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IMonthlyDOWTrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IMonthlyDOWTrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IMonthlyDOWTrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IMonthlyDOWTrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        IMonthlyDOWTrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        IMonthlyDOWTrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        IMonthlyDOWTrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        IMonthlyDOWTrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        IMonthlyDOWTrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        IMonthlyDOWTrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        IMonthlyDOWTrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        IMonthlyDOWTrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        IMonthlyDOWTrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IMonthlyDOWTrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IMonthlyDOWTrigger *This,
        VARIANT_BOOL enabled);

    /*** IMonthlyDOWTrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DaysOfWeek)(
        IMonthlyDOWTrigger *This,
        short *pDays);

    HRESULT (STDMETHODCALLTYPE *put_DaysOfWeek)(
        IMonthlyDOWTrigger *This,
        short days);

    HRESULT (STDMETHODCALLTYPE *get_WeeksOfMonth)(
        IMonthlyDOWTrigger *This,
        short *pWeeks);

    HRESULT (STDMETHODCALLTYPE *put_WeeksOfMonth)(
        IMonthlyDOWTrigger *This,
        short weeks);

    HRESULT (STDMETHODCALLTYPE *get_MonthsOfYear)(
        IMonthlyDOWTrigger *This,
        short *pMonths);

    HRESULT (STDMETHODCALLTYPE *put_MonthsOfYear)(
        IMonthlyDOWTrigger *This,
        short months);

    HRESULT (STDMETHODCALLTYPE *get_RunOnLastWeekOfMonth)(
        IMonthlyDOWTrigger *This,
        VARIANT_BOOL *pLastWeek);

    HRESULT (STDMETHODCALLTYPE *put_RunOnLastWeekOfMonth)(
        IMonthlyDOWTrigger *This,
        VARIANT_BOOL lastWeek);

    HRESULT (STDMETHODCALLTYPE *get_RandomDelay)(
        IMonthlyDOWTrigger *This,
        BSTR *pRandomDelay);

    HRESULT (STDMETHODCALLTYPE *put_RandomDelay)(
        IMonthlyDOWTrigger *This,
        BSTR randomDelay);

    END_INTERFACE
} IMonthlyDOWTriggerVtbl;

interface IMonthlyDOWTrigger {
    CONST_VTBL IMonthlyDOWTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMonthlyDOWTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMonthlyDOWTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMonthlyDOWTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IMonthlyDOWTrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IMonthlyDOWTrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IMonthlyDOWTrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IMonthlyDOWTrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define IMonthlyDOWTrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define IMonthlyDOWTrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IMonthlyDOWTrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define IMonthlyDOWTrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define IMonthlyDOWTrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define IMonthlyDOWTrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define IMonthlyDOWTrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define IMonthlyDOWTrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define IMonthlyDOWTrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define IMonthlyDOWTrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define IMonthlyDOWTrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define IMonthlyDOWTrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define IMonthlyDOWTrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
/*** IMonthlyDOWTrigger methods ***/
#define IMonthlyDOWTrigger_get_DaysOfWeek(This,pDays) (This)->lpVtbl->get_DaysOfWeek(This,pDays)
#define IMonthlyDOWTrigger_put_DaysOfWeek(This,days) (This)->lpVtbl->put_DaysOfWeek(This,days)
#define IMonthlyDOWTrigger_get_WeeksOfMonth(This,pWeeks) (This)->lpVtbl->get_WeeksOfMonth(This,pWeeks)
#define IMonthlyDOWTrigger_put_WeeksOfMonth(This,weeks) (This)->lpVtbl->put_WeeksOfMonth(This,weeks)
#define IMonthlyDOWTrigger_get_MonthsOfYear(This,pMonths) (This)->lpVtbl->get_MonthsOfYear(This,pMonths)
#define IMonthlyDOWTrigger_put_MonthsOfYear(This,months) (This)->lpVtbl->put_MonthsOfYear(This,months)
#define IMonthlyDOWTrigger_get_RunOnLastWeekOfMonth(This,pLastWeek) (This)->lpVtbl->get_RunOnLastWeekOfMonth(This,pLastWeek)
#define IMonthlyDOWTrigger_put_RunOnLastWeekOfMonth(This,lastWeek) (This)->lpVtbl->put_RunOnLastWeekOfMonth(This,lastWeek)
#define IMonthlyDOWTrigger_get_RandomDelay(This,pRandomDelay) (This)->lpVtbl->get_RandomDelay(This,pRandomDelay)
#define IMonthlyDOWTrigger_put_RandomDelay(This,randomDelay) (This)->lpVtbl->put_RandomDelay(This,randomDelay)
#else
/*** IUnknown methods ***/
static inline HRESULT IMonthlyDOWTrigger_QueryInterface(IMonthlyDOWTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMonthlyDOWTrigger_AddRef(IMonthlyDOWTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMonthlyDOWTrigger_Release(IMonthlyDOWTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IMonthlyDOWTrigger_GetTypeInfoCount(IMonthlyDOWTrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IMonthlyDOWTrigger_GetTypeInfo(IMonthlyDOWTrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IMonthlyDOWTrigger_GetIDsOfNames(IMonthlyDOWTrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IMonthlyDOWTrigger_Invoke(IMonthlyDOWTrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT IMonthlyDOWTrigger_get_Type(IMonthlyDOWTrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT IMonthlyDOWTrigger_get_Id(IMonthlyDOWTrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IMonthlyDOWTrigger_put_Id(IMonthlyDOWTrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT IMonthlyDOWTrigger_get_Repetition(IMonthlyDOWTrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT IMonthlyDOWTrigger_put_Repetition(IMonthlyDOWTrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT IMonthlyDOWTrigger_get_ExecutionTimeLimit(IMonthlyDOWTrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IMonthlyDOWTrigger_put_ExecutionTimeLimit(IMonthlyDOWTrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IMonthlyDOWTrigger_get_StartBoundary(IMonthlyDOWTrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT IMonthlyDOWTrigger_put_StartBoundary(IMonthlyDOWTrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT IMonthlyDOWTrigger_get_EndBoundary(IMonthlyDOWTrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT IMonthlyDOWTrigger_put_EndBoundary(IMonthlyDOWTrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT IMonthlyDOWTrigger_get_Enabled(IMonthlyDOWTrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT IMonthlyDOWTrigger_put_Enabled(IMonthlyDOWTrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
/*** IMonthlyDOWTrigger methods ***/
static inline HRESULT IMonthlyDOWTrigger_get_DaysOfWeek(IMonthlyDOWTrigger* This,short *pDays) {
    return This->lpVtbl->get_DaysOfWeek(This,pDays);
}
static inline HRESULT IMonthlyDOWTrigger_put_DaysOfWeek(IMonthlyDOWTrigger* This,short days) {
    return This->lpVtbl->put_DaysOfWeek(This,days);
}
static inline HRESULT IMonthlyDOWTrigger_get_WeeksOfMonth(IMonthlyDOWTrigger* This,short *pWeeks) {
    return This->lpVtbl->get_WeeksOfMonth(This,pWeeks);
}
static inline HRESULT IMonthlyDOWTrigger_put_WeeksOfMonth(IMonthlyDOWTrigger* This,short weeks) {
    return This->lpVtbl->put_WeeksOfMonth(This,weeks);
}
static inline HRESULT IMonthlyDOWTrigger_get_MonthsOfYear(IMonthlyDOWTrigger* This,short *pMonths) {
    return This->lpVtbl->get_MonthsOfYear(This,pMonths);
}
static inline HRESULT IMonthlyDOWTrigger_put_MonthsOfYear(IMonthlyDOWTrigger* This,short months) {
    return This->lpVtbl->put_MonthsOfYear(This,months);
}
static inline HRESULT IMonthlyDOWTrigger_get_RunOnLastWeekOfMonth(IMonthlyDOWTrigger* This,VARIANT_BOOL *pLastWeek) {
    return This->lpVtbl->get_RunOnLastWeekOfMonth(This,pLastWeek);
}
static inline HRESULT IMonthlyDOWTrigger_put_RunOnLastWeekOfMonth(IMonthlyDOWTrigger* This,VARIANT_BOOL lastWeek) {
    return This->lpVtbl->put_RunOnLastWeekOfMonth(This,lastWeek);
}
static inline HRESULT IMonthlyDOWTrigger_get_RandomDelay(IMonthlyDOWTrigger* This,BSTR *pRandomDelay) {
    return This->lpVtbl->get_RandomDelay(This,pRandomDelay);
}
static inline HRESULT IMonthlyDOWTrigger_put_RandomDelay(IMonthlyDOWTrigger* This,BSTR randomDelay) {
    return This->lpVtbl->put_RandomDelay(This,randomDelay);
}
#endif
#endif

#endif


#endif  /* __IMonthlyDOWTrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IBootTrigger interface
 */
#ifndef __IBootTrigger_INTERFACE_DEFINED__
#define __IBootTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBootTrigger, 0x2a9c35da, 0xd357, 0x41f4, 0xbb,0xc1, 0x20,0x7a,0xc1,0xb1,0xf3,0xcb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2a9c35da-d357-41f4-bbc1-207ac1b1f3cb")
IBootTrigger : public ITrigger
{
    virtual HRESULT STDMETHODCALLTYPE get_Delay(
        BSTR *pDelay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Delay(
        BSTR delay) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBootTrigger, 0x2a9c35da, 0xd357, 0x41f4, 0xbb,0xc1, 0x20,0x7a,0xc1,0xb1,0xf3,0xcb)
#endif
#else
typedef struct IBootTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBootTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBootTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBootTrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IBootTrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IBootTrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IBootTrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IBootTrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IBootTrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IBootTrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        IBootTrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        IBootTrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        IBootTrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        IBootTrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        IBootTrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        IBootTrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        IBootTrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        IBootTrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        IBootTrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IBootTrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IBootTrigger *This,
        VARIANT_BOOL enabled);

    /*** IBootTrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Delay)(
        IBootTrigger *This,
        BSTR *pDelay);

    HRESULT (STDMETHODCALLTYPE *put_Delay)(
        IBootTrigger *This,
        BSTR delay);

    END_INTERFACE
} IBootTriggerVtbl;

interface IBootTrigger {
    CONST_VTBL IBootTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBootTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBootTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBootTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IBootTrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IBootTrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IBootTrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IBootTrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define IBootTrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define IBootTrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IBootTrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define IBootTrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define IBootTrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define IBootTrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define IBootTrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define IBootTrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define IBootTrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define IBootTrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define IBootTrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define IBootTrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define IBootTrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
/*** IBootTrigger methods ***/
#define IBootTrigger_get_Delay(This,pDelay) (This)->lpVtbl->get_Delay(This,pDelay)
#define IBootTrigger_put_Delay(This,delay) (This)->lpVtbl->put_Delay(This,delay)
#else
/*** IUnknown methods ***/
static inline HRESULT IBootTrigger_QueryInterface(IBootTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBootTrigger_AddRef(IBootTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBootTrigger_Release(IBootTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IBootTrigger_GetTypeInfoCount(IBootTrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IBootTrigger_GetTypeInfo(IBootTrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IBootTrigger_GetIDsOfNames(IBootTrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IBootTrigger_Invoke(IBootTrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT IBootTrigger_get_Type(IBootTrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT IBootTrigger_get_Id(IBootTrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IBootTrigger_put_Id(IBootTrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT IBootTrigger_get_Repetition(IBootTrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT IBootTrigger_put_Repetition(IBootTrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT IBootTrigger_get_ExecutionTimeLimit(IBootTrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IBootTrigger_put_ExecutionTimeLimit(IBootTrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IBootTrigger_get_StartBoundary(IBootTrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT IBootTrigger_put_StartBoundary(IBootTrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT IBootTrigger_get_EndBoundary(IBootTrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT IBootTrigger_put_EndBoundary(IBootTrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT IBootTrigger_get_Enabled(IBootTrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT IBootTrigger_put_Enabled(IBootTrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
/*** IBootTrigger methods ***/
static inline HRESULT IBootTrigger_get_Delay(IBootTrigger* This,BSTR *pDelay) {
    return This->lpVtbl->get_Delay(This,pDelay);
}
static inline HRESULT IBootTrigger_put_Delay(IBootTrigger* This,BSTR delay) {
    return This->lpVtbl->put_Delay(This,delay);
}
#endif
#endif

#endif


#endif  /* __IBootTrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRegistrationTrigger interface
 */
#ifndef __IRegistrationTrigger_INTERFACE_DEFINED__
#define __IRegistrationTrigger_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRegistrationTrigger, 0x4c8fec3a, 0xc218, 0x4e0c, 0xb2,0x3d, 0x62,0x90,0x24,0xdb,0x91,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4c8fec3a-c218-4e0c-b23d-629024db91a2")
IRegistrationTrigger : public ITrigger
{
    virtual HRESULT STDMETHODCALLTYPE get_Delay(
        BSTR *pDelay) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Delay(
        BSTR delay) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRegistrationTrigger, 0x4c8fec3a, 0xc218, 0x4e0c, 0xb2,0x3d, 0x62,0x90,0x24,0xdb,0x91,0xa2)
#endif
#else
typedef struct IRegistrationTriggerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRegistrationTrigger *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRegistrationTrigger *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRegistrationTrigger *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRegistrationTrigger *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRegistrationTrigger *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRegistrationTrigger *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRegistrationTrigger *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IRegistrationTrigger *This,
        TASK_TRIGGER_TYPE2 *type);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IRegistrationTrigger *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        IRegistrationTrigger *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Repetition)(
        IRegistrationTrigger *This,
        IRepetitionPattern **repeat);

    HRESULT (STDMETHODCALLTYPE *put_Repetition)(
        IRegistrationTrigger *This,
        IRepetitionPattern *repeat);

    HRESULT (STDMETHODCALLTYPE *get_ExecutionTimeLimit)(
        IRegistrationTrigger *This,
        BSTR *limit);

    HRESULT (STDMETHODCALLTYPE *put_ExecutionTimeLimit)(
        IRegistrationTrigger *This,
        BSTR limit);

    HRESULT (STDMETHODCALLTYPE *get_StartBoundary)(
        IRegistrationTrigger *This,
        BSTR *start);

    HRESULT (STDMETHODCALLTYPE *put_StartBoundary)(
        IRegistrationTrigger *This,
        BSTR start);

    HRESULT (STDMETHODCALLTYPE *get_EndBoundary)(
        IRegistrationTrigger *This,
        BSTR *end);

    HRESULT (STDMETHODCALLTYPE *put_EndBoundary)(
        IRegistrationTrigger *This,
        BSTR end);

    HRESULT (STDMETHODCALLTYPE *get_Enabled)(
        IRegistrationTrigger *This,
        VARIANT_BOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *put_Enabled)(
        IRegistrationTrigger *This,
        VARIANT_BOOL enabled);

    /*** IRegistrationTrigger methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Delay)(
        IRegistrationTrigger *This,
        BSTR *pDelay);

    HRESULT (STDMETHODCALLTYPE *put_Delay)(
        IRegistrationTrigger *This,
        BSTR delay);

    END_INTERFACE
} IRegistrationTriggerVtbl;

interface IRegistrationTrigger {
    CONST_VTBL IRegistrationTriggerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRegistrationTrigger_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRegistrationTrigger_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRegistrationTrigger_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRegistrationTrigger_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRegistrationTrigger_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRegistrationTrigger_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRegistrationTrigger_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITrigger methods ***/
#define IRegistrationTrigger_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#define IRegistrationTrigger_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IRegistrationTrigger_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define IRegistrationTrigger_get_Repetition(This,repeat) (This)->lpVtbl->get_Repetition(This,repeat)
#define IRegistrationTrigger_put_Repetition(This,repeat) (This)->lpVtbl->put_Repetition(This,repeat)
#define IRegistrationTrigger_get_ExecutionTimeLimit(This,limit) (This)->lpVtbl->get_ExecutionTimeLimit(This,limit)
#define IRegistrationTrigger_put_ExecutionTimeLimit(This,limit) (This)->lpVtbl->put_ExecutionTimeLimit(This,limit)
#define IRegistrationTrigger_get_StartBoundary(This,start) (This)->lpVtbl->get_StartBoundary(This,start)
#define IRegistrationTrigger_put_StartBoundary(This,start) (This)->lpVtbl->put_StartBoundary(This,start)
#define IRegistrationTrigger_get_EndBoundary(This,end) (This)->lpVtbl->get_EndBoundary(This,end)
#define IRegistrationTrigger_put_EndBoundary(This,end) (This)->lpVtbl->put_EndBoundary(This,end)
#define IRegistrationTrigger_get_Enabled(This,enabled) (This)->lpVtbl->get_Enabled(This,enabled)
#define IRegistrationTrigger_put_Enabled(This,enabled) (This)->lpVtbl->put_Enabled(This,enabled)
/*** IRegistrationTrigger methods ***/
#define IRegistrationTrigger_get_Delay(This,pDelay) (This)->lpVtbl->get_Delay(This,pDelay)
#define IRegistrationTrigger_put_Delay(This,delay) (This)->lpVtbl->put_Delay(This,delay)
#else
/*** IUnknown methods ***/
static inline HRESULT IRegistrationTrigger_QueryInterface(IRegistrationTrigger* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRegistrationTrigger_AddRef(IRegistrationTrigger* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRegistrationTrigger_Release(IRegistrationTrigger* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRegistrationTrigger_GetTypeInfoCount(IRegistrationTrigger* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRegistrationTrigger_GetTypeInfo(IRegistrationTrigger* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRegistrationTrigger_GetIDsOfNames(IRegistrationTrigger* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRegistrationTrigger_Invoke(IRegistrationTrigger* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITrigger methods ***/
static inline HRESULT IRegistrationTrigger_get_Type(IRegistrationTrigger* This,TASK_TRIGGER_TYPE2 *type) {
    return This->lpVtbl->get_Type(This,type);
}
static inline HRESULT IRegistrationTrigger_get_Id(IRegistrationTrigger* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IRegistrationTrigger_put_Id(IRegistrationTrigger* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT IRegistrationTrigger_get_Repetition(IRegistrationTrigger* This,IRepetitionPattern **repeat) {
    return This->lpVtbl->get_Repetition(This,repeat);
}
static inline HRESULT IRegistrationTrigger_put_Repetition(IRegistrationTrigger* This,IRepetitionPattern *repeat) {
    return This->lpVtbl->put_Repetition(This,repeat);
}
static inline HRESULT IRegistrationTrigger_get_ExecutionTimeLimit(IRegistrationTrigger* This,BSTR *limit) {
    return This->lpVtbl->get_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IRegistrationTrigger_put_ExecutionTimeLimit(IRegistrationTrigger* This,BSTR limit) {
    return This->lpVtbl->put_ExecutionTimeLimit(This,limit);
}
static inline HRESULT IRegistrationTrigger_get_StartBoundary(IRegistrationTrigger* This,BSTR *start) {
    return This->lpVtbl->get_StartBoundary(This,start);
}
static inline HRESULT IRegistrationTrigger_put_StartBoundary(IRegistrationTrigger* This,BSTR start) {
    return This->lpVtbl->put_StartBoundary(This,start);
}
static inline HRESULT IRegistrationTrigger_get_EndBoundary(IRegistrationTrigger* This,BSTR *end) {
    return This->lpVtbl->get_EndBoundary(This,end);
}
static inline HRESULT IRegistrationTrigger_put_EndBoundary(IRegistrationTrigger* This,BSTR end) {
    return This->lpVtbl->put_EndBoundary(This,end);
}
static inline HRESULT IRegistrationTrigger_get_Enabled(IRegistrationTrigger* This,VARIANT_BOOL *enabled) {
    return This->lpVtbl->get_Enabled(This,enabled);
}
static inline HRESULT IRegistrationTrigger_put_Enabled(IRegistrationTrigger* This,VARIANT_BOOL enabled) {
    return This->lpVtbl->put_Enabled(This,enabled);
}
/*** IRegistrationTrigger methods ***/
static inline HRESULT IRegistrationTrigger_get_Delay(IRegistrationTrigger* This,BSTR *pDelay) {
    return This->lpVtbl->get_Delay(This,pDelay);
}
static inline HRESULT IRegistrationTrigger_put_Delay(IRegistrationTrigger* This,BSTR delay) {
    return This->lpVtbl->put_Delay(This,delay);
}
#endif
#endif

#endif


#endif  /* __IRegistrationTrigger_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITriggerCollection interface
 */
#ifndef __ITriggerCollection_INTERFACE_DEFINED__
#define __ITriggerCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITriggerCollection, 0x85df5081, 0x1b24, 0x4f32, 0x87,0x8a, 0xd9,0xd1,0x4d,0xf4,0xcb,0x77);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("85df5081-1b24-4f32-878a-d9d14df4cb77")
ITriggerCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG index,
        ITrigger **trigger) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **penum) = 0;

    virtual HRESULT STDMETHODCALLTYPE Create(
        TASK_TRIGGER_TYPE2 type,
        ITrigger **trigger) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        VARIANT index) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITriggerCollection, 0x85df5081, 0x1b24, 0x4f32, 0x87,0x8a, 0xd9,0xd1,0x4d,0xf4,0xcb,0x77)
#endif
#else
typedef struct ITriggerCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITriggerCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITriggerCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITriggerCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ITriggerCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ITriggerCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ITriggerCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ITriggerCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ITriggerCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ITriggerCollection *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        ITriggerCollection *This,
        LONG index,
        ITrigger **trigger);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ITriggerCollection *This,
        IUnknown **penum);

    HRESULT (STDMETHODCALLTYPE *Create)(
        ITriggerCollection *This,
        TASK_TRIGGER_TYPE2 type,
        ITrigger **trigger);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        ITriggerCollection *This,
        VARIANT index);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        ITriggerCollection *This);

    END_INTERFACE
} ITriggerCollectionVtbl;

interface ITriggerCollection {
    CONST_VTBL ITriggerCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITriggerCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITriggerCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITriggerCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ITriggerCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ITriggerCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ITriggerCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ITriggerCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ITriggerCollection methods ***/
#define ITriggerCollection_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define ITriggerCollection_get_Item(This,index,trigger) (This)->lpVtbl->get_Item(This,index,trigger)
#define ITriggerCollection_get__NewEnum(This,penum) (This)->lpVtbl->get__NewEnum(This,penum)
#define ITriggerCollection_Create(This,type,trigger) (This)->lpVtbl->Create(This,type,trigger)
#define ITriggerCollection_Remove(This,index) (This)->lpVtbl->Remove(This,index)
#define ITriggerCollection_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ITriggerCollection_QueryInterface(ITriggerCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITriggerCollection_AddRef(ITriggerCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITriggerCollection_Release(ITriggerCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ITriggerCollection_GetTypeInfoCount(ITriggerCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ITriggerCollection_GetTypeInfo(ITriggerCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ITriggerCollection_GetIDsOfNames(ITriggerCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ITriggerCollection_Invoke(ITriggerCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ITriggerCollection methods ***/
static inline HRESULT ITriggerCollection_get_Count(ITriggerCollection* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static inline HRESULT ITriggerCollection_get_Item(ITriggerCollection* This,LONG index,ITrigger **trigger) {
    return This->lpVtbl->get_Item(This,index,trigger);
}
static inline HRESULT ITriggerCollection_get__NewEnum(ITriggerCollection* This,IUnknown **penum) {
    return This->lpVtbl->get__NewEnum(This,penum);
}
static inline HRESULT ITriggerCollection_Create(ITriggerCollection* This,TASK_TRIGGER_TYPE2 type,ITrigger **trigger) {
    return This->lpVtbl->Create(This,type,trigger);
}
static inline HRESULT ITriggerCollection_Remove(ITriggerCollection* This,VARIANT index) {
    return This->lpVtbl->Remove(This,index);
}
static inline HRESULT ITriggerCollection_Clear(ITriggerCollection* This) {
    return This->lpVtbl->Clear(This);
}
#endif
#endif

#endif


#endif  /* __ITriggerCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IRepetitionPattern interface
 */
#ifndef __IRepetitionPattern_INTERFACE_DEFINED__
#define __IRepetitionPattern_INTERFACE_DEFINED__

DEFINE_GUID(IID_IRepetitionPattern, 0x7fb9acf1, 0x26be, 0x400e, 0x85,0xb5, 0x29,0x4b,0x9c,0x75,0xdf,0xd6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7fb9acf1-26be-400e-85b5-294b9c75dfd6")
IRepetitionPattern : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Interval(
        BSTR *interval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Interval(
        BSTR interval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Duration(
        BSTR *duration) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Duration(
        BSTR duration) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StopAtDurationEnd(
        VARIANT_BOOL *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_StopAtDurationEnd(
        VARIANT_BOOL sop) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IRepetitionPattern, 0x7fb9acf1, 0x26be, 0x400e, 0x85,0xb5, 0x29,0x4b,0x9c,0x75,0xdf,0xd6)
#endif
#else
typedef struct IRepetitionPatternVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IRepetitionPattern *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IRepetitionPattern *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IRepetitionPattern *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IRepetitionPattern *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IRepetitionPattern *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IRepetitionPattern *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IRepetitionPattern *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IRepetitionPattern methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Interval)(
        IRepetitionPattern *This,
        BSTR *interval);

    HRESULT (STDMETHODCALLTYPE *put_Interval)(
        IRepetitionPattern *This,
        BSTR interval);

    HRESULT (STDMETHODCALLTYPE *get_Duration)(
        IRepetitionPattern *This,
        BSTR *duration);

    HRESULT (STDMETHODCALLTYPE *put_Duration)(
        IRepetitionPattern *This,
        BSTR duration);

    HRESULT (STDMETHODCALLTYPE *get_StopAtDurationEnd)(
        IRepetitionPattern *This,
        VARIANT_BOOL *stop);

    HRESULT (STDMETHODCALLTYPE *put_StopAtDurationEnd)(
        IRepetitionPattern *This,
        VARIANT_BOOL sop);

    END_INTERFACE
} IRepetitionPatternVtbl;

interface IRepetitionPattern {
    CONST_VTBL IRepetitionPatternVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IRepetitionPattern_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IRepetitionPattern_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IRepetitionPattern_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IRepetitionPattern_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IRepetitionPattern_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IRepetitionPattern_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IRepetitionPattern_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IRepetitionPattern methods ***/
#define IRepetitionPattern_get_Interval(This,interval) (This)->lpVtbl->get_Interval(This,interval)
#define IRepetitionPattern_put_Interval(This,interval) (This)->lpVtbl->put_Interval(This,interval)
#define IRepetitionPattern_get_Duration(This,duration) (This)->lpVtbl->get_Duration(This,duration)
#define IRepetitionPattern_put_Duration(This,duration) (This)->lpVtbl->put_Duration(This,duration)
#define IRepetitionPattern_get_StopAtDurationEnd(This,stop) (This)->lpVtbl->get_StopAtDurationEnd(This,stop)
#define IRepetitionPattern_put_StopAtDurationEnd(This,sop) (This)->lpVtbl->put_StopAtDurationEnd(This,sop)
#else
/*** IUnknown methods ***/
static inline HRESULT IRepetitionPattern_QueryInterface(IRepetitionPattern* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IRepetitionPattern_AddRef(IRepetitionPattern* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IRepetitionPattern_Release(IRepetitionPattern* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IRepetitionPattern_GetTypeInfoCount(IRepetitionPattern* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IRepetitionPattern_GetTypeInfo(IRepetitionPattern* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IRepetitionPattern_GetIDsOfNames(IRepetitionPattern* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IRepetitionPattern_Invoke(IRepetitionPattern* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IRepetitionPattern methods ***/
static inline HRESULT IRepetitionPattern_get_Interval(IRepetitionPattern* This,BSTR *interval) {
    return This->lpVtbl->get_Interval(This,interval);
}
static inline HRESULT IRepetitionPattern_put_Interval(IRepetitionPattern* This,BSTR interval) {
    return This->lpVtbl->put_Interval(This,interval);
}
static inline HRESULT IRepetitionPattern_get_Duration(IRepetitionPattern* This,BSTR *duration) {
    return This->lpVtbl->get_Duration(This,duration);
}
static inline HRESULT IRepetitionPattern_put_Duration(IRepetitionPattern* This,BSTR duration) {
    return This->lpVtbl->put_Duration(This,duration);
}
static inline HRESULT IRepetitionPattern_get_StopAtDurationEnd(IRepetitionPattern* This,VARIANT_BOOL *stop) {
    return This->lpVtbl->get_StopAtDurationEnd(This,stop);
}
static inline HRESULT IRepetitionPattern_put_StopAtDurationEnd(IRepetitionPattern* This,VARIANT_BOOL sop) {
    return This->lpVtbl->put_StopAtDurationEnd(This,sop);
}
#endif
#endif

#endif


#endif  /* __IRepetitionPattern_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAction interface
 */
#ifndef __IAction_INTERFACE_DEFINED__
#define __IAction_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAction, 0xbae54997, 0x48b1, 0x4cbe, 0x99,0x65, 0xd6,0xbe,0x26,0x3e,0xbe,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bae54997-48b1-4cbe-9965-d6be263ebea4")
IAction : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Id(
        BSTR *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Id(
        BSTR id) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Type(
        TASK_ACTION_TYPE *type) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAction, 0xbae54997, 0x48b1, 0x4cbe, 0x99,0x65, 0xd6,0xbe,0x26,0x3e,0xbe,0xa4)
#endif
#else
typedef struct IActionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAction *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAction *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAction *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IAction *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IAction *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IAction *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IAction *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IAction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IAction *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        IAction *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IAction *This,
        TASK_ACTION_TYPE *type);

    END_INTERFACE
} IActionVtbl;

interface IAction {
    CONST_VTBL IActionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAction_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAction_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAction_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IAction_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IAction_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IAction_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IAction_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IAction methods ***/
#define IAction_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IAction_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define IAction_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
#else
/*** IUnknown methods ***/
static inline HRESULT IAction_QueryInterface(IAction* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAction_AddRef(IAction* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAction_Release(IAction* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IAction_GetTypeInfoCount(IAction* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IAction_GetTypeInfo(IAction* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IAction_GetIDsOfNames(IAction* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IAction_Invoke(IAction* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IAction methods ***/
static inline HRESULT IAction_get_Id(IAction* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IAction_put_Id(IAction* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT IAction_get_Type(IAction* This,TASK_ACTION_TYPE *type) {
    return This->lpVtbl->get_Type(This,type);
}
#endif
#endif

#endif


#endif  /* __IAction_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActionCollection interface
 */
#ifndef __IActionCollection_INTERFACE_DEFINED__
#define __IActionCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActionCollection, 0x02820e19, 0x7b98, 0x4ed2, 0xb2,0xe8, 0xfd,0xcc,0xce,0xff,0x61,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("02820e19-7b98-4ed2-b2e8-fdccceff619b")
IActionCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG index,
        IAction **action) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **penum) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_XmlText(
        BSTR *xml) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_XmlText(
        BSTR xml) = 0;

    virtual HRESULT STDMETHODCALLTYPE Create(
        TASK_ACTION_TYPE Type,
        IAction **action) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        VARIANT index) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Context(
        BSTR *ctx) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Context(
        BSTR ctx) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActionCollection, 0x02820e19, 0x7b98, 0x4ed2, 0xb2,0xe8, 0xfd,0xcc,0xce,0xff,0x61,0x9b)
#endif
#else
typedef struct IActionCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActionCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActionCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActionCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IActionCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IActionCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IActionCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IActionCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IActionCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IActionCollection *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IActionCollection *This,
        LONG index,
        IAction **action);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IActionCollection *This,
        IUnknown **penum);

    HRESULT (STDMETHODCALLTYPE *get_XmlText)(
        IActionCollection *This,
        BSTR *xml);

    HRESULT (STDMETHODCALLTYPE *put_XmlText)(
        IActionCollection *This,
        BSTR xml);

    HRESULT (STDMETHODCALLTYPE *Create)(
        IActionCollection *This,
        TASK_ACTION_TYPE Type,
        IAction **action);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        IActionCollection *This,
        VARIANT index);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IActionCollection *This);

    HRESULT (STDMETHODCALLTYPE *get_Context)(
        IActionCollection *This,
        BSTR *ctx);

    HRESULT (STDMETHODCALLTYPE *put_Context)(
        IActionCollection *This,
        BSTR ctx);

    END_INTERFACE
} IActionCollectionVtbl;

interface IActionCollection {
    CONST_VTBL IActionCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActionCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActionCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActionCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IActionCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IActionCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IActionCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IActionCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IActionCollection methods ***/
#define IActionCollection_get_Count(This,count) (This)->lpVtbl->get_Count(This,count)
#define IActionCollection_get_Item(This,index,action) (This)->lpVtbl->get_Item(This,index,action)
#define IActionCollection_get__NewEnum(This,penum) (This)->lpVtbl->get__NewEnum(This,penum)
#define IActionCollection_get_XmlText(This,xml) (This)->lpVtbl->get_XmlText(This,xml)
#define IActionCollection_put_XmlText(This,xml) (This)->lpVtbl->put_XmlText(This,xml)
#define IActionCollection_Create(This,Type,action) (This)->lpVtbl->Create(This,Type,action)
#define IActionCollection_Remove(This,index) (This)->lpVtbl->Remove(This,index)
#define IActionCollection_Clear(This) (This)->lpVtbl->Clear(This)
#define IActionCollection_get_Context(This,ctx) (This)->lpVtbl->get_Context(This,ctx)
#define IActionCollection_put_Context(This,ctx) (This)->lpVtbl->put_Context(This,ctx)
#else
/*** IUnknown methods ***/
static inline HRESULT IActionCollection_QueryInterface(IActionCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActionCollection_AddRef(IActionCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActionCollection_Release(IActionCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IActionCollection_GetTypeInfoCount(IActionCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IActionCollection_GetTypeInfo(IActionCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IActionCollection_GetIDsOfNames(IActionCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IActionCollection_Invoke(IActionCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IActionCollection methods ***/
static inline HRESULT IActionCollection_get_Count(IActionCollection* This,LONG *count) {
    return This->lpVtbl->get_Count(This,count);
}
static inline HRESULT IActionCollection_get_Item(IActionCollection* This,LONG index,IAction **action) {
    return This->lpVtbl->get_Item(This,index,action);
}
static inline HRESULT IActionCollection_get__NewEnum(IActionCollection* This,IUnknown **penum) {
    return This->lpVtbl->get__NewEnum(This,penum);
}
static inline HRESULT IActionCollection_get_XmlText(IActionCollection* This,BSTR *xml) {
    return This->lpVtbl->get_XmlText(This,xml);
}
static inline HRESULT IActionCollection_put_XmlText(IActionCollection* This,BSTR xml) {
    return This->lpVtbl->put_XmlText(This,xml);
}
static inline HRESULT IActionCollection_Create(IActionCollection* This,TASK_ACTION_TYPE Type,IAction **action) {
    return This->lpVtbl->Create(This,Type,action);
}
static inline HRESULT IActionCollection_Remove(IActionCollection* This,VARIANT index) {
    return This->lpVtbl->Remove(This,index);
}
static inline HRESULT IActionCollection_Clear(IActionCollection* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT IActionCollection_get_Context(IActionCollection* This,BSTR *ctx) {
    return This->lpVtbl->get_Context(This,ctx);
}
static inline HRESULT IActionCollection_put_Context(IActionCollection* This,BSTR ctx) {
    return This->lpVtbl->put_Context(This,ctx);
}
#endif
#endif

#endif


#endif  /* __IActionCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IExecAction interface
 */
#ifndef __IExecAction_INTERFACE_DEFINED__
#define __IExecAction_INTERFACE_DEFINED__

DEFINE_GUID(IID_IExecAction, 0x4c3d624d, 0xfd6b, 0x49a3, 0xb9,0xb7, 0x09,0xcb,0x3c,0xd3,0xf0,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4c3d624d-fd6b-49a3-b9b7-09cb3cd3f047")
IExecAction : public IAction
{
    virtual HRESULT STDMETHODCALLTYPE get_Path(
        BSTR *path) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Path(
        BSTR path) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Arguments(
        BSTR *argument) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Arguments(
        BSTR argument) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WorkingDirectory(
        BSTR *directory) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_WorkingDirectory(
        BSTR directory) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IExecAction, 0x4c3d624d, 0xfd6b, 0x49a3, 0xb9,0xb7, 0x09,0xcb,0x3c,0xd3,0xf0,0x47)
#endif
#else
typedef struct IExecActionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IExecAction *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IExecAction *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IExecAction *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IExecAction *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IExecAction *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IExecAction *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IExecAction *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IAction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IExecAction *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        IExecAction *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IExecAction *This,
        TASK_ACTION_TYPE *type);

    /*** IExecAction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Path)(
        IExecAction *This,
        BSTR *path);

    HRESULT (STDMETHODCALLTYPE *put_Path)(
        IExecAction *This,
        BSTR path);

    HRESULT (STDMETHODCALLTYPE *get_Arguments)(
        IExecAction *This,
        BSTR *argument);

    HRESULT (STDMETHODCALLTYPE *put_Arguments)(
        IExecAction *This,
        BSTR argument);

    HRESULT (STDMETHODCALLTYPE *get_WorkingDirectory)(
        IExecAction *This,
        BSTR *directory);

    HRESULT (STDMETHODCALLTYPE *put_WorkingDirectory)(
        IExecAction *This,
        BSTR directory);

    END_INTERFACE
} IExecActionVtbl;

interface IExecAction {
    CONST_VTBL IExecActionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IExecAction_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IExecAction_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IExecAction_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IExecAction_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IExecAction_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IExecAction_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IExecAction_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IAction methods ***/
#define IExecAction_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IExecAction_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define IExecAction_get_Type(This,type) (This)->lpVtbl->get_Type(This,type)
/*** IExecAction methods ***/
#define IExecAction_get_Path(This,path) (This)->lpVtbl->get_Path(This,path)
#define IExecAction_put_Path(This,path) (This)->lpVtbl->put_Path(This,path)
#define IExecAction_get_Arguments(This,argument) (This)->lpVtbl->get_Arguments(This,argument)
#define IExecAction_put_Arguments(This,argument) (This)->lpVtbl->put_Arguments(This,argument)
#define IExecAction_get_WorkingDirectory(This,directory) (This)->lpVtbl->get_WorkingDirectory(This,directory)
#define IExecAction_put_WorkingDirectory(This,directory) (This)->lpVtbl->put_WorkingDirectory(This,directory)
#else
/*** IUnknown methods ***/
static inline HRESULT IExecAction_QueryInterface(IExecAction* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IExecAction_AddRef(IExecAction* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IExecAction_Release(IExecAction* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IExecAction_GetTypeInfoCount(IExecAction* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IExecAction_GetTypeInfo(IExecAction* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IExecAction_GetIDsOfNames(IExecAction* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IExecAction_Invoke(IExecAction* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IAction methods ***/
static inline HRESULT IExecAction_get_Id(IExecAction* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IExecAction_put_Id(IExecAction* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT IExecAction_get_Type(IExecAction* This,TASK_ACTION_TYPE *type) {
    return This->lpVtbl->get_Type(This,type);
}
/*** IExecAction methods ***/
static inline HRESULT IExecAction_get_Path(IExecAction* This,BSTR *path) {
    return This->lpVtbl->get_Path(This,path);
}
static inline HRESULT IExecAction_put_Path(IExecAction* This,BSTR path) {
    return This->lpVtbl->put_Path(This,path);
}
static inline HRESULT IExecAction_get_Arguments(IExecAction* This,BSTR *argument) {
    return This->lpVtbl->get_Arguments(This,argument);
}
static inline HRESULT IExecAction_put_Arguments(IExecAction* This,BSTR argument) {
    return This->lpVtbl->put_Arguments(This,argument);
}
static inline HRESULT IExecAction_get_WorkingDirectory(IExecAction* This,BSTR *directory) {
    return This->lpVtbl->get_WorkingDirectory(This,directory);
}
static inline HRESULT IExecAction_put_WorkingDirectory(IExecAction* This,BSTR directory) {
    return This->lpVtbl->put_WorkingDirectory(This,directory);
}
#endif
#endif

#endif


#endif  /* __IExecAction_INTERFACE_DEFINED__ */

/*****************************************************************************
 * INetworkSettings interface
 */
#ifndef __INetworkSettings_INTERFACE_DEFINED__
#define __INetworkSettings_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetworkSettings, 0x9f7dea84, 0xc30b, 0x4245, 0x80,0xb6, 0x00,0xe9,0xf6,0x46,0xf1,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9f7dea84-c30b-4245-80b6-00e9f646f1b4")
INetworkSettings : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Name(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Id(
        BSTR *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Id(
        BSTR id) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetworkSettings, 0x9f7dea84, 0xc30b, 0x4245, 0x80,0xb6, 0x00,0xe9,0xf6,0x46,0xf1,0xb4)
#endif
#else
typedef struct INetworkSettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetworkSettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetworkSettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetworkSettings *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        INetworkSettings *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        INetworkSettings *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        INetworkSettings *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        INetworkSettings *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** INetworkSettings methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        INetworkSettings *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_Name)(
        INetworkSettings *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        INetworkSettings *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        INetworkSettings *This,
        BSTR id);

    END_INTERFACE
} INetworkSettingsVtbl;

interface INetworkSettings {
    CONST_VTBL INetworkSettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetworkSettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetworkSettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetworkSettings_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define INetworkSettings_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define INetworkSettings_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define INetworkSettings_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define INetworkSettings_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** INetworkSettings methods ***/
#define INetworkSettings_get_Name(This,name) (This)->lpVtbl->get_Name(This,name)
#define INetworkSettings_put_Name(This,name) (This)->lpVtbl->put_Name(This,name)
#define INetworkSettings_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define INetworkSettings_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#else
/*** IUnknown methods ***/
static inline HRESULT INetworkSettings_QueryInterface(INetworkSettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetworkSettings_AddRef(INetworkSettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetworkSettings_Release(INetworkSettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT INetworkSettings_GetTypeInfoCount(INetworkSettings* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT INetworkSettings_GetTypeInfo(INetworkSettings* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT INetworkSettings_GetIDsOfNames(INetworkSettings* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT INetworkSettings_Invoke(INetworkSettings* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** INetworkSettings methods ***/
static inline HRESULT INetworkSettings_get_Name(INetworkSettings* This,BSTR *name) {
    return This->lpVtbl->get_Name(This,name);
}
static inline HRESULT INetworkSettings_put_Name(INetworkSettings* This,BSTR name) {
    return This->lpVtbl->put_Name(This,name);
}
static inline HRESULT INetworkSettings_get_Id(INetworkSettings* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT INetworkSettings_put_Id(INetworkSettings* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
#endif
#endif

#endif


#endif  /* __INetworkSettings_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPrincipal interface
 */
#ifndef __IPrincipal_INTERFACE_DEFINED__
#define __IPrincipal_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPrincipal, 0xd98d51e5, 0xc9b4, 0x496a, 0xa9,0xc1, 0x18,0x98,0x02,0x61,0xcf,0x0f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d98d51e5-c9b4-496a-a9c1-18980261cf0f")
IPrincipal : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Id(
        BSTR *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Id(
        BSTR id) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_DisplayName(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UserId(
        BSTR *user) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_UserId(
        BSTR user) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LogonType(
        TASK_LOGON_TYPE *logon) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_LogonType(
        TASK_LOGON_TYPE logon) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_GroupId(
        BSTR *group) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_GroupId(
        BSTR group) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RunLevel(
        TASK_RUNLEVEL_TYPE *level) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_RunLevel(
        TASK_RUNLEVEL_TYPE level) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPrincipal, 0xd98d51e5, 0xc9b4, 0x496a, 0xa9,0xc1, 0x18,0x98,0x02,0x61,0xcf,0x0f)
#endif
#else
typedef struct IPrincipalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPrincipal *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPrincipal *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPrincipal *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IPrincipal *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IPrincipal *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IPrincipal *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IPrincipal *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IPrincipal methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IPrincipal *This,
        BSTR *id);

    HRESULT (STDMETHODCALLTYPE *put_Id)(
        IPrincipal *This,
        BSTR id);

    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        IPrincipal *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_DisplayName)(
        IPrincipal *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_UserId)(
        IPrincipal *This,
        BSTR *user);

    HRESULT (STDMETHODCALLTYPE *put_UserId)(
        IPrincipal *This,
        BSTR user);

    HRESULT (STDMETHODCALLTYPE *get_LogonType)(
        IPrincipal *This,
        TASK_LOGON_TYPE *logon);

    HRESULT (STDMETHODCALLTYPE *put_LogonType)(
        IPrincipal *This,
        TASK_LOGON_TYPE logon);

    HRESULT (STDMETHODCALLTYPE *get_GroupId)(
        IPrincipal *This,
        BSTR *group);

    HRESULT (STDMETHODCALLTYPE *put_GroupId)(
        IPrincipal *This,
        BSTR group);

    HRESULT (STDMETHODCALLTYPE *get_RunLevel)(
        IPrincipal *This,
        TASK_RUNLEVEL_TYPE *level);

    HRESULT (STDMETHODCALLTYPE *put_RunLevel)(
        IPrincipal *This,
        TASK_RUNLEVEL_TYPE level);

    END_INTERFACE
} IPrincipalVtbl;

interface IPrincipal {
    CONST_VTBL IPrincipalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPrincipal_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPrincipal_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPrincipal_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IPrincipal_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IPrincipal_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IPrincipal_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IPrincipal_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IPrincipal methods ***/
#define IPrincipal_get_Id(This,id) (This)->lpVtbl->get_Id(This,id)
#define IPrincipal_put_Id(This,id) (This)->lpVtbl->put_Id(This,id)
#define IPrincipal_get_DisplayName(This,name) (This)->lpVtbl->get_DisplayName(This,name)
#define IPrincipal_put_DisplayName(This,name) (This)->lpVtbl->put_DisplayName(This,name)
#define IPrincipal_get_UserId(This,user) (This)->lpVtbl->get_UserId(This,user)
#define IPrincipal_put_UserId(This,user) (This)->lpVtbl->put_UserId(This,user)
#define IPrincipal_get_LogonType(This,logon) (This)->lpVtbl->get_LogonType(This,logon)
#define IPrincipal_put_LogonType(This,logon) (This)->lpVtbl->put_LogonType(This,logon)
#define IPrincipal_get_GroupId(This,group) (This)->lpVtbl->get_GroupId(This,group)
#define IPrincipal_put_GroupId(This,group) (This)->lpVtbl->put_GroupId(This,group)
#define IPrincipal_get_RunLevel(This,level) (This)->lpVtbl->get_RunLevel(This,level)
#define IPrincipal_put_RunLevel(This,level) (This)->lpVtbl->put_RunLevel(This,level)
#else
/*** IUnknown methods ***/
static inline HRESULT IPrincipal_QueryInterface(IPrincipal* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPrincipal_AddRef(IPrincipal* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPrincipal_Release(IPrincipal* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IPrincipal_GetTypeInfoCount(IPrincipal* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IPrincipal_GetTypeInfo(IPrincipal* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IPrincipal_GetIDsOfNames(IPrincipal* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IPrincipal_Invoke(IPrincipal* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IPrincipal methods ***/
static inline HRESULT IPrincipal_get_Id(IPrincipal* This,BSTR *id) {
    return This->lpVtbl->get_Id(This,id);
}
static inline HRESULT IPrincipal_put_Id(IPrincipal* This,BSTR id) {
    return This->lpVtbl->put_Id(This,id);
}
static inline HRESULT IPrincipal_get_DisplayName(IPrincipal* This,BSTR *name) {
    return This->lpVtbl->get_DisplayName(This,name);
}
static inline HRESULT IPrincipal_put_DisplayName(IPrincipal* This,BSTR name) {
    return This->lpVtbl->put_DisplayName(This,name);
}
static inline HRESULT IPrincipal_get_UserId(IPrincipal* This,BSTR *user) {
    return This->lpVtbl->get_UserId(This,user);
}
static inline HRESULT IPrincipal_put_UserId(IPrincipal* This,BSTR user) {
    return This->lpVtbl->put_UserId(This,user);
}
static inline HRESULT IPrincipal_get_LogonType(IPrincipal* This,TASK_LOGON_TYPE *logon) {
    return This->lpVtbl->get_LogonType(This,logon);
}
static inline HRESULT IPrincipal_put_LogonType(IPrincipal* This,TASK_LOGON_TYPE logon) {
    return This->lpVtbl->put_LogonType(This,logon);
}
static inline HRESULT IPrincipal_get_GroupId(IPrincipal* This,BSTR *group) {
    return This->lpVtbl->get_GroupId(This,group);
}
static inline HRESULT IPrincipal_put_GroupId(IPrincipal* This,BSTR group) {
    return This->lpVtbl->put_GroupId(This,group);
}
static inline HRESULT IPrincipal_get_RunLevel(IPrincipal* This,TASK_RUNLEVEL_TYPE *level) {
    return This->lpVtbl->get_RunLevel(This,level);
}
static inline HRESULT IPrincipal_put_RunLevel(IPrincipal* This,TASK_RUNLEVEL_TYPE level) {
    return This->lpVtbl->put_RunLevel(This,level);
}
#endif
#endif

#endif


#endif  /* __IPrincipal_INTERFACE_DEFINED__ */

/*****************************************************************************
 * TaskScheduler coclass
 */

DEFINE_GUID(CLSID_TaskScheduler, 0x0f87369f, 0xa4e5, 0x4cfc, 0xbd,0x3e, 0x73,0xe6,0x15,0x45,0x72,0xdd);

#ifdef __cplusplus
class DECLSPEC_UUID("0f87369f-a4e5-4cfc-bd3e-73e6154572dd") TaskScheduler;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(TaskScheduler, 0x0f87369f, 0xa4e5, 0x4cfc, 0xbd,0x3e, 0x73,0xe6,0x15,0x45,0x72,0xdd)
#endif
#endif

#endif /* __TaskScheduler_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __taskschd_h__ */
