/*** Autogenerated by WIDL 10.12 from include/windows.ui.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_ui_h__
#define __windows_ui_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CUI_CIColorHelper_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorHelper_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIColorHelper __x_ABI_CWindows_CUI_CIColorHelper;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIColorHelper ABI::Windows::UI::IColorHelper
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IColorHelper;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIColorHelperStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorHelperStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIColorHelperStatics __x_ABI_CWindows_CUI_CIColorHelperStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIColorHelperStatics ABI::Windows::UI::IColorHelperStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IColorHelperStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIColorHelperStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorHelperStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIColorHelperStatics2 __x_ABI_CWindows_CUI_CIColorHelperStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIColorHelperStatics2 ABI::Windows::UI::IColorHelperStatics2
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IColorHelperStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIColors_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColors_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIColors __x_ABI_CWindows_CUI_CIColors;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIColors ABI::Windows::UI::IColors
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IColors;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIColorsStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorsStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIColorsStatics __x_ABI_CWindows_CUI_CIColorsStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIColorsStatics ABI::Windows::UI::IColorsStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IColorsStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIUIContentRoot_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIUIContentRoot_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIUIContentRoot __x_ABI_CWindows_CUI_CIUIContentRoot;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIUIContentRoot ABI::Windows::UI::IUIContentRoot
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IUIContentRoot;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIUIContext_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIUIContext_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIUIContext __x_ABI_CWindows_CUI_CIUIContext;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIUIContext ABI::Windows::UI::IUIContext
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IUIContext;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CColorHelper_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CColorHelper_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            class ColorHelper;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CColorHelper __x_ABI_CWindows_CUI_CColorHelper;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CColorHelper_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CColors_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CColors_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            class Colors;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CColors __x_ABI_CWindows_CUI_CColors;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CColors_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CUIContentRoot_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CUIContentRoot_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            class UIContentRoot;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CUIContentRoot __x_ABI_CWindows_CUI_CUIContentRoot;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CUIContentRoot_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CUIContext_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CUIContext_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            class UIContext;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CUIContext __x_ABI_CWindows_CUI_CUIContext;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CUIContext_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Color_FWD_DEFINED__
#define ____FIIterable_1_Color_FWD_DEFINED__
typedef interface __FIIterable_1_Color __FIIterable_1_Color;
#ifdef __cplusplus
#define __FIIterable_1_Color ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::UI::Color >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_WindowId_FWD_DEFINED__
#define ____FIIterable_1_WindowId_FWD_DEFINED__
typedef interface __FIIterable_1_WindowId __FIIterable_1_WindowId;
#ifdef __cplusplus
#define __FIIterable_1_WindowId ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::UI::WindowId >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Color_FWD_DEFINED__
#define ____FIIterator_1_Color_FWD_DEFINED__
typedef interface __FIIterator_1_Color __FIIterator_1_Color;
#ifdef __cplusplus
#define __FIIterator_1_Color ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::UI::Color >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_WindowId_FWD_DEFINED__
#define ____FIIterator_1_WindowId_FWD_DEFINED__
typedef interface __FIIterator_1_WindowId __FIIterator_1_WindowId;
#ifdef __cplusplus
#define __FIIterator_1_WindowId ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::UI::WindowId >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_WindowId_FWD_DEFINED__
#define ____FIVectorView_1_WindowId_FWD_DEFINED__
typedef interface __FIVectorView_1_WindowId __FIVectorView_1_WindowId;
#ifdef __cplusplus
#define __FIVectorView_1_WindowId ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::UI::WindowId >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_Color_FWD_DEFINED__
#define ____FIReference_1_Color_FWD_DEFINED__
typedef interface __FIReference_1_Color __FIReference_1_Color;
#ifdef __cplusplus
#define __FIReference_1_Color ABI::Windows::Foundation::IReference<ABI::Windows::UI::Color >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CUI_CColor __x_ABI_CWindows_CUI_CColor;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace UI {
            typedef struct Color Color;
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CUI_CWindowId __x_ABI_CWindows_CUI_CWindowId;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace UI {
            typedef struct WindowId WindowId;
        }
    }
}
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CUI_CIColorHelper_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorHelper_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIColorHelper __x_ABI_CWindows_CUI_CIColorHelper;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIColorHelper ABI::Windows::UI::IColorHelper
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IColorHelper;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIColorHelperStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorHelperStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIColorHelperStatics __x_ABI_CWindows_CUI_CIColorHelperStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIColorHelperStatics ABI::Windows::UI::IColorHelperStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IColorHelperStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIColorHelperStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorHelperStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIColorHelperStatics2 __x_ABI_CWindows_CUI_CIColorHelperStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIColorHelperStatics2 ABI::Windows::UI::IColorHelperStatics2
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IColorHelperStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIColors_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColors_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIColors __x_ABI_CWindows_CUI_CIColors;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIColors ABI::Windows::UI::IColors
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IColors;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIColorsStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorsStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIColorsStatics __x_ABI_CWindows_CUI_CIColorsStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIColorsStatics ABI::Windows::UI::IColorsStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IColorsStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIUIContentRoot_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIUIContentRoot_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIUIContentRoot __x_ABI_CWindows_CUI_CIUIContentRoot;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIUIContentRoot ABI::Windows::UI::IUIContentRoot
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IUIContentRoot;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CIUIContext_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CIUIContext_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CIUIContext __x_ABI_CWindows_CUI_CIUIContext;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CIUIContext ABI::Windows::UI::IUIContext
namespace ABI {
    namespace Windows {
        namespace UI {
            interface IUIContext;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Color_FWD_DEFINED__
#define ____FIIterable_1_Color_FWD_DEFINED__
typedef interface __FIIterable_1_Color __FIIterable_1_Color;
#ifdef __cplusplus
#define __FIIterable_1_Color ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::UI::Color >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_WindowId_FWD_DEFINED__
#define ____FIIterable_1_WindowId_FWD_DEFINED__
typedef interface __FIIterable_1_WindowId __FIIterable_1_WindowId;
#ifdef __cplusplus
#define __FIIterable_1_WindowId ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::UI::WindowId >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Color_FWD_DEFINED__
#define ____FIIterator_1_Color_FWD_DEFINED__
typedef interface __FIIterator_1_Color __FIIterator_1_Color;
#ifdef __cplusplus
#define __FIIterator_1_Color ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::UI::Color >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_WindowId_FWD_DEFINED__
#define ____FIIterator_1_WindowId_FWD_DEFINED__
typedef interface __FIIterator_1_WindowId __FIIterator_1_WindowId;
#ifdef __cplusplus
#define __FIIterator_1_WindowId ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::UI::WindowId >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_WindowId_FWD_DEFINED__
#define ____FIVectorView_1_WindowId_FWD_DEFINED__
typedef interface __FIVectorView_1_WindowId __FIVectorView_1_WindowId;
#ifdef __cplusplus
#define __FIVectorView_1_WindowId ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::UI::WindowId >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_Color_FWD_DEFINED__
#define ____FIReference_1_Color_FWD_DEFINED__
typedef interface __FIReference_1_Color __FIReference_1_Color;
#ifdef __cplusplus
#define __FIReference_1_Color ABI::Windows::Foundation::IReference<ABI::Windows::UI::Color >
#endif /* __cplusplus */
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            struct Color {
                BYTE A;
                BYTE R;
                BYTE G;
                BYTE B;
            };
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CUI_CColor {
    BYTE A;
    BYTE R;
    BYTE G;
    BYTE B;
};
#ifdef WIDL_using_Windows_UI
#define Color __x_ABI_CWindows_CUI_CColor
#endif /* WIDL_using_Windows_UI */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            struct WindowId {
                UINT64 Value;
            };
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CUI_CWindowId {
    UINT64 Value;
};
#ifdef WIDL_using_Windows_UI
#define WindowId __x_ABI_CWindows_CUI_CWindowId
#endif /* WIDL_using_Windows_UI */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000 */
/*****************************************************************************
 * IColorHelper interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CIColorHelper_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorHelper_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CIColorHelper, 0x193cfbe7, 0x65c7, 0x4540, 0xad,0x08, 0x62,0x83,0xba,0x76,0x87,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            MIDL_INTERFACE("193cfbe7-65c7-4540-ad08-6283ba76879a")
            IColorHelper : public IInspectable
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CIColorHelper, 0x193cfbe7, 0x65c7, 0x4540, 0xad,0x08, 0x62,0x83,0xba,0x76,0x87,0x9a)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CIColorHelperVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CIColorHelper *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CIColorHelper *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CIColorHelper *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CIColorHelper *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CIColorHelper *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CIColorHelper *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CIColorHelperVtbl;

interface __x_ABI_CWindows_CUI_CIColorHelper {
    CONST_VTBL __x_ABI_CWindows_CUI_CIColorHelperVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CIColorHelper_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CIColorHelper_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CIColorHelper_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CIColorHelper_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CIColorHelper_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CIColorHelper_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelper_QueryInterface(__x_ABI_CWindows_CUI_CIColorHelper* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CIColorHelper_AddRef(__x_ABI_CWindows_CUI_CIColorHelper* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CIColorHelper_Release(__x_ABI_CWindows_CUI_CIColorHelper* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelper_GetIids(__x_ABI_CWindows_CUI_CIColorHelper* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelper_GetRuntimeClassName(__x_ABI_CWindows_CUI_CIColorHelper* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelper_GetTrustLevel(__x_ABI_CWindows_CUI_CIColorHelper* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_UI
#define IID_IColorHelper IID___x_ABI_CWindows_CUI_CIColorHelper
#define IColorHelperVtbl __x_ABI_CWindows_CUI_CIColorHelperVtbl
#define IColorHelper __x_ABI_CWindows_CUI_CIColorHelper
#define IColorHelper_QueryInterface __x_ABI_CWindows_CUI_CIColorHelper_QueryInterface
#define IColorHelper_AddRef __x_ABI_CWindows_CUI_CIColorHelper_AddRef
#define IColorHelper_Release __x_ABI_CWindows_CUI_CIColorHelper_Release
#define IColorHelper_GetIids __x_ABI_CWindows_CUI_CIColorHelper_GetIids
#define IColorHelper_GetRuntimeClassName __x_ABI_CWindows_CUI_CIColorHelper_GetRuntimeClassName
#define IColorHelper_GetTrustLevel __x_ABI_CWindows_CUI_CIColorHelper_GetTrustLevel
#endif /* WIDL_using_Windows_UI */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CIColorHelper_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IColorHelperStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CIColorHelperStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorHelperStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CIColorHelperStatics, 0x8504dbea, 0xfb6a, 0x4144, 0xa6,0xc2, 0x33,0x49,0x9c,0x92,0x84,0xf5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            MIDL_INTERFACE("8504dbea-fb6a-4144-a6c2-33499c9284f5")
            IColorHelperStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE FromArgb(
                    BYTE a,
                    BYTE r,
                    BYTE g,
                    BYTE b,
                    ABI::Windows::UI::Color *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CIColorHelperStatics, 0x8504dbea, 0xfb6a, 0x4144, 0xa6,0xc2, 0x33,0x49,0x9c,0x92,0x84,0xf5)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CIColorHelperStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics *This,
        TrustLevel *trustLevel);

    /*** IColorHelperStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *FromArgb)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics *This,
        BYTE a,
        BYTE r,
        BYTE g,
        BYTE b,
        __x_ABI_CWindows_CUI_CColor *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CIColorHelperStaticsVtbl;

interface __x_ABI_CWindows_CUI_CIColorHelperStatics {
    CONST_VTBL __x_ABI_CWindows_CUI_CIColorHelperStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CIColorHelperStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CIColorHelperStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CIColorHelperStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CIColorHelperStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CIColorHelperStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CIColorHelperStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IColorHelperStatics methods ***/
#define __x_ABI_CWindows_CUI_CIColorHelperStatics_FromArgb(This,a,r,g,b,value) (This)->lpVtbl->FromArgb(This,a,r,g,b,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelperStatics_QueryInterface(__x_ABI_CWindows_CUI_CIColorHelperStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CIColorHelperStatics_AddRef(__x_ABI_CWindows_CUI_CIColorHelperStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CIColorHelperStatics_Release(__x_ABI_CWindows_CUI_CIColorHelperStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelperStatics_GetIids(__x_ABI_CWindows_CUI_CIColorHelperStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelperStatics_GetRuntimeClassName(__x_ABI_CWindows_CUI_CIColorHelperStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelperStatics_GetTrustLevel(__x_ABI_CWindows_CUI_CIColorHelperStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IColorHelperStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelperStatics_FromArgb(__x_ABI_CWindows_CUI_CIColorHelperStatics* This,BYTE a,BYTE r,BYTE g,BYTE b,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->FromArgb(This,a,r,g,b,value);
}
#endif
#ifdef WIDL_using_Windows_UI
#define IID_IColorHelperStatics IID___x_ABI_CWindows_CUI_CIColorHelperStatics
#define IColorHelperStaticsVtbl __x_ABI_CWindows_CUI_CIColorHelperStaticsVtbl
#define IColorHelperStatics __x_ABI_CWindows_CUI_CIColorHelperStatics
#define IColorHelperStatics_QueryInterface __x_ABI_CWindows_CUI_CIColorHelperStatics_QueryInterface
#define IColorHelperStatics_AddRef __x_ABI_CWindows_CUI_CIColorHelperStatics_AddRef
#define IColorHelperStatics_Release __x_ABI_CWindows_CUI_CIColorHelperStatics_Release
#define IColorHelperStatics_GetIids __x_ABI_CWindows_CUI_CIColorHelperStatics_GetIids
#define IColorHelperStatics_GetRuntimeClassName __x_ABI_CWindows_CUI_CIColorHelperStatics_GetRuntimeClassName
#define IColorHelperStatics_GetTrustLevel __x_ABI_CWindows_CUI_CIColorHelperStatics_GetTrustLevel
#define IColorHelperStatics_FromArgb __x_ABI_CWindows_CUI_CIColorHelperStatics_FromArgb
#endif /* WIDL_using_Windows_UI */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CIColorHelperStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IColorHelperStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CUI_CIColorHelperStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorHelperStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CIColorHelperStatics2, 0x24d9af02, 0x6eb0, 0x4b94, 0x85,0x5c, 0xfc,0xf0,0x81,0x8d,0x9a,0x16);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            MIDL_INTERFACE("24d9af02-6eb0-4b94-855c-fcf0818d9a16")
            IColorHelperStatics2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE ToDisplayName(
                    ABI::Windows::UI::Color color,
                    HSTRING *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CIColorHelperStatics2, 0x24d9af02, 0x6eb0, 0x4b94, 0x85,0x5c, 0xfc,0xf0,0x81,0x8d,0x9a,0x16)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CIColorHelperStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics2 *This,
        TrustLevel *trustLevel);

    /*** IColorHelperStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *ToDisplayName)(
        __x_ABI_CWindows_CUI_CIColorHelperStatics2 *This,
        __x_ABI_CWindows_CUI_CColor color,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CIColorHelperStatics2Vtbl;

interface __x_ABI_CWindows_CUI_CIColorHelperStatics2 {
    CONST_VTBL __x_ABI_CWindows_CUI_CIColorHelperStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CIColorHelperStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CIColorHelperStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CIColorHelperStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CIColorHelperStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CIColorHelperStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CIColorHelperStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IColorHelperStatics2 methods ***/
#define __x_ABI_CWindows_CUI_CIColorHelperStatics2_ToDisplayName(This,color,value) (This)->lpVtbl->ToDisplayName(This,color,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelperStatics2_QueryInterface(__x_ABI_CWindows_CUI_CIColorHelperStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CIColorHelperStatics2_AddRef(__x_ABI_CWindows_CUI_CIColorHelperStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CIColorHelperStatics2_Release(__x_ABI_CWindows_CUI_CIColorHelperStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelperStatics2_GetIids(__x_ABI_CWindows_CUI_CIColorHelperStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelperStatics2_GetRuntimeClassName(__x_ABI_CWindows_CUI_CIColorHelperStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelperStatics2_GetTrustLevel(__x_ABI_CWindows_CUI_CIColorHelperStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IColorHelperStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColorHelperStatics2_ToDisplayName(__x_ABI_CWindows_CUI_CIColorHelperStatics2* This,__x_ABI_CWindows_CUI_CColor color,HSTRING *value) {
    return This->lpVtbl->ToDisplayName(This,color,value);
}
#endif
#ifdef WIDL_using_Windows_UI
#define IID_IColorHelperStatics2 IID___x_ABI_CWindows_CUI_CIColorHelperStatics2
#define IColorHelperStatics2Vtbl __x_ABI_CWindows_CUI_CIColorHelperStatics2Vtbl
#define IColorHelperStatics2 __x_ABI_CWindows_CUI_CIColorHelperStatics2
#define IColorHelperStatics2_QueryInterface __x_ABI_CWindows_CUI_CIColorHelperStatics2_QueryInterface
#define IColorHelperStatics2_AddRef __x_ABI_CWindows_CUI_CIColorHelperStatics2_AddRef
#define IColorHelperStatics2_Release __x_ABI_CWindows_CUI_CIColorHelperStatics2_Release
#define IColorHelperStatics2_GetIids __x_ABI_CWindows_CUI_CIColorHelperStatics2_GetIids
#define IColorHelperStatics2_GetRuntimeClassName __x_ABI_CWindows_CUI_CIColorHelperStatics2_GetRuntimeClassName
#define IColorHelperStatics2_GetTrustLevel __x_ABI_CWindows_CUI_CIColorHelperStatics2_GetTrustLevel
#define IColorHelperStatics2_ToDisplayName __x_ABI_CWindows_CUI_CIColorHelperStatics2_ToDisplayName
#endif /* WIDL_using_Windows_UI */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CIColorHelperStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IColors interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CIColors_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColors_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CIColors, 0x9b8c9326, 0x4ca6, 0x4ce5, 0x89,0x94, 0x9e,0xff,0x65,0xca,0xbd,0xcc);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            MIDL_INTERFACE("9b8c9326-4ca6-4ce5-8994-9eff65cabdcc")
            IColors : public IInspectable
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CIColors, 0x9b8c9326, 0x4ca6, 0x4ce5, 0x89,0x94, 0x9e,0xff,0x65,0xca,0xbd,0xcc)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CIColorsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CIColors *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CIColors *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CIColors *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CIColors *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CIColors *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CIColors *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CIColorsVtbl;

interface __x_ABI_CWindows_CUI_CIColors {
    CONST_VTBL __x_ABI_CWindows_CUI_CIColorsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CIColors_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CIColors_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CIColors_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CIColors_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CIColors_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CIColors_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColors_QueryInterface(__x_ABI_CWindows_CUI_CIColors* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CIColors_AddRef(__x_ABI_CWindows_CUI_CIColors* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CIColors_Release(__x_ABI_CWindows_CUI_CIColors* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColors_GetIids(__x_ABI_CWindows_CUI_CIColors* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColors_GetRuntimeClassName(__x_ABI_CWindows_CUI_CIColors* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColors_GetTrustLevel(__x_ABI_CWindows_CUI_CIColors* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_UI
#define IID_IColors IID___x_ABI_CWindows_CUI_CIColors
#define IColorsVtbl __x_ABI_CWindows_CUI_CIColorsVtbl
#define IColors __x_ABI_CWindows_CUI_CIColors
#define IColors_QueryInterface __x_ABI_CWindows_CUI_CIColors_QueryInterface
#define IColors_AddRef __x_ABI_CWindows_CUI_CIColors_AddRef
#define IColors_Release __x_ABI_CWindows_CUI_CIColors_Release
#define IColors_GetIids __x_ABI_CWindows_CUI_CIColors_GetIids
#define IColors_GetRuntimeClassName __x_ABI_CWindows_CUI_CIColors_GetRuntimeClassName
#define IColors_GetTrustLevel __x_ABI_CWindows_CUI_CIColors_GetTrustLevel
#endif /* WIDL_using_Windows_UI */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CIColors_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IColorsStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CIColorsStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CIColorsStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CIColorsStatics, 0xcff52e04, 0xcca6, 0x4614, 0xa1,0x7e, 0x75,0x49,0x10,0xc8,0x4a,0x99);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            MIDL_INTERFACE("cff52e04-cca6-4614-a17e-754910c84a99")
            IColorsStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_AliceBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_AntiqueWhite(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Aqua(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Aquamarine(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Azure(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Beige(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Bisque(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Black(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_BlanchedAlmond(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Blue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_BlueViolet(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Brown(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_BurlyWood(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_CadetBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Chartreuse(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Chocolate(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Coral(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_CornflowerBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Cornsilk(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Crimson(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Cyan(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkCyan(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkGoldenrod(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkGray(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkKhaki(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkMagenta(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkOliveGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkOrange(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkOrchid(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkRed(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkSalmon(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkSeaGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkSlateBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkSlateGray(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkTurquoise(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DarkViolet(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DeepPink(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DeepSkyBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DimGray(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DodgerBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Firebrick(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_FloralWhite(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_ForestGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Fuchsia(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Gainsboro(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_GhostWhite(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Gold(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Goldenrod(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Gray(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Green(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_GreenYellow(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Honeydew(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_HotPink(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IndianRed(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Indigo(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Ivory(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Khaki(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Lavender(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LavenderBlush(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LawnGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LemonChiffon(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightCoral(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightCyan(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightGoldenrodYellow(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightGray(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightPink(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightSalmon(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightSeaGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightSkyBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightSlateGray(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightSteelBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LightYellow(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Lime(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LimeGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Linen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Magenta(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Maroon(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MediumAquamarine(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MediumBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MediumOrchid(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MediumPurple(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MediumSeaGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MediumSlateBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MediumSpringGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MediumTurquoise(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MediumVioletRed(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MidnightBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MintCream(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MistyRose(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Moccasin(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_NavajoWhite(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Navy(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_OldLace(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Olive(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_OliveDrab(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Orange(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_OrangeRed(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Orchid(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PaleGoldenrod(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PaleGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PaleTurquoise(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PaleVioletRed(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PapayaWhip(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PeachPuff(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Peru(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Pink(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Plum(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PowderBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Purple(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Red(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_RosyBrown(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_RoyalBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_SaddleBrown(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Salmon(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_SandyBrown(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_SeaGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_SeaShell(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Sienna(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Silver(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_SkyBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_SlateBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_SlateGray(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Snow(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_SpringGreen(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_SteelBlue(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Tan(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Teal(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Thistle(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Tomato(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Transparent(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Turquoise(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Violet(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Wheat(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_White(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_WhiteSmoke(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Yellow(
                    ABI::Windows::UI::Color *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_YellowGreen(
                    ABI::Windows::UI::Color *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CIColorsStatics, 0xcff52e04, 0xcca6, 0x4614, 0xa1,0x7e, 0x75,0x49,0x10,0xc8,0x4a,0x99)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CIColorsStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        TrustLevel *trustLevel);

    /*** IColorsStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AliceBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_AntiqueWhite)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Aqua)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Aquamarine)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Azure)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Beige)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Bisque)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Black)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_BlanchedAlmond)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Blue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_BlueViolet)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Brown)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_BurlyWood)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_CadetBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Chartreuse)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Chocolate)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Coral)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_CornflowerBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Cornsilk)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Crimson)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Cyan)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkCyan)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkGoldenrod)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkGray)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkKhaki)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkMagenta)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkOliveGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkOrange)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkOrchid)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkRed)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkSalmon)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkSeaGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkSlateBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkSlateGray)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkTurquoise)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DarkViolet)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DeepPink)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DeepSkyBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DimGray)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_DodgerBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Firebrick)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_FloralWhite)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_ForestGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Fuchsia)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Gainsboro)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_GhostWhite)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Gold)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Goldenrod)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Gray)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Green)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_GreenYellow)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Honeydew)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_HotPink)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_IndianRed)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Indigo)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Ivory)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Khaki)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Lavender)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LavenderBlush)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LawnGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LemonChiffon)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightCoral)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightCyan)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightGoldenrodYellow)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightGray)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightPink)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightSalmon)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightSeaGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightSkyBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightSlateGray)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightSteelBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LightYellow)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Lime)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_LimeGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Linen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Magenta)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Maroon)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MediumAquamarine)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MediumBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MediumOrchid)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MediumPurple)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MediumSeaGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MediumSlateBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MediumSpringGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MediumTurquoise)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MediumVioletRed)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MidnightBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MintCream)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_MistyRose)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Moccasin)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_NavajoWhite)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Navy)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_OldLace)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Olive)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_OliveDrab)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Orange)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_OrangeRed)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Orchid)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_PaleGoldenrod)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_PaleGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_PaleTurquoise)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_PaleVioletRed)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_PapayaWhip)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_PeachPuff)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Peru)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Pink)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Plum)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_PowderBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Purple)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Red)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_RosyBrown)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_RoyalBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_SaddleBrown)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Salmon)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_SandyBrown)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_SeaGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_SeaShell)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Sienna)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Silver)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_SkyBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_SlateBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_SlateGray)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Snow)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_SpringGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_SteelBlue)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Tan)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Teal)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Thistle)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Tomato)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Transparent)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Turquoise)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Violet)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Wheat)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_White)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_WhiteSmoke)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_Yellow)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_YellowGreen)(
        __x_ABI_CWindows_CUI_CIColorsStatics *This,
        __x_ABI_CWindows_CUI_CColor *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CIColorsStaticsVtbl;

interface __x_ABI_CWindows_CUI_CIColorsStatics {
    CONST_VTBL __x_ABI_CWindows_CUI_CIColorsStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CIColorsStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CIColorsStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CIColorsStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CIColorsStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CIColorsStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CIColorsStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IColorsStatics methods ***/
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_AliceBlue(This,value) (This)->lpVtbl->get_AliceBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_AntiqueWhite(This,value) (This)->lpVtbl->get_AntiqueWhite(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Aqua(This,value) (This)->lpVtbl->get_Aqua(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Aquamarine(This,value) (This)->lpVtbl->get_Aquamarine(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Azure(This,value) (This)->lpVtbl->get_Azure(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Beige(This,value) (This)->lpVtbl->get_Beige(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Bisque(This,value) (This)->lpVtbl->get_Bisque(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Black(This,value) (This)->lpVtbl->get_Black(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_BlanchedAlmond(This,value) (This)->lpVtbl->get_BlanchedAlmond(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Blue(This,value) (This)->lpVtbl->get_Blue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_BlueViolet(This,value) (This)->lpVtbl->get_BlueViolet(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Brown(This,value) (This)->lpVtbl->get_Brown(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_BurlyWood(This,value) (This)->lpVtbl->get_BurlyWood(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_CadetBlue(This,value) (This)->lpVtbl->get_CadetBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Chartreuse(This,value) (This)->lpVtbl->get_Chartreuse(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Chocolate(This,value) (This)->lpVtbl->get_Chocolate(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Coral(This,value) (This)->lpVtbl->get_Coral(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_CornflowerBlue(This,value) (This)->lpVtbl->get_CornflowerBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Cornsilk(This,value) (This)->lpVtbl->get_Cornsilk(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Crimson(This,value) (This)->lpVtbl->get_Crimson(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Cyan(This,value) (This)->lpVtbl->get_Cyan(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkBlue(This,value) (This)->lpVtbl->get_DarkBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkCyan(This,value) (This)->lpVtbl->get_DarkCyan(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkGoldenrod(This,value) (This)->lpVtbl->get_DarkGoldenrod(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkGray(This,value) (This)->lpVtbl->get_DarkGray(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkGreen(This,value) (This)->lpVtbl->get_DarkGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkKhaki(This,value) (This)->lpVtbl->get_DarkKhaki(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkMagenta(This,value) (This)->lpVtbl->get_DarkMagenta(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkOliveGreen(This,value) (This)->lpVtbl->get_DarkOliveGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkOrange(This,value) (This)->lpVtbl->get_DarkOrange(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkOrchid(This,value) (This)->lpVtbl->get_DarkOrchid(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkRed(This,value) (This)->lpVtbl->get_DarkRed(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSalmon(This,value) (This)->lpVtbl->get_DarkSalmon(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSeaGreen(This,value) (This)->lpVtbl->get_DarkSeaGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSlateBlue(This,value) (This)->lpVtbl->get_DarkSlateBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSlateGray(This,value) (This)->lpVtbl->get_DarkSlateGray(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkTurquoise(This,value) (This)->lpVtbl->get_DarkTurquoise(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkViolet(This,value) (This)->lpVtbl->get_DarkViolet(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DeepPink(This,value) (This)->lpVtbl->get_DeepPink(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DeepSkyBlue(This,value) (This)->lpVtbl->get_DeepSkyBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DimGray(This,value) (This)->lpVtbl->get_DimGray(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_DodgerBlue(This,value) (This)->lpVtbl->get_DodgerBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Firebrick(This,value) (This)->lpVtbl->get_Firebrick(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_FloralWhite(This,value) (This)->lpVtbl->get_FloralWhite(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_ForestGreen(This,value) (This)->lpVtbl->get_ForestGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Fuchsia(This,value) (This)->lpVtbl->get_Fuchsia(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Gainsboro(This,value) (This)->lpVtbl->get_Gainsboro(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_GhostWhite(This,value) (This)->lpVtbl->get_GhostWhite(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Gold(This,value) (This)->lpVtbl->get_Gold(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Goldenrod(This,value) (This)->lpVtbl->get_Goldenrod(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Gray(This,value) (This)->lpVtbl->get_Gray(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Green(This,value) (This)->lpVtbl->get_Green(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_GreenYellow(This,value) (This)->lpVtbl->get_GreenYellow(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Honeydew(This,value) (This)->lpVtbl->get_Honeydew(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_HotPink(This,value) (This)->lpVtbl->get_HotPink(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_IndianRed(This,value) (This)->lpVtbl->get_IndianRed(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Indigo(This,value) (This)->lpVtbl->get_Indigo(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Ivory(This,value) (This)->lpVtbl->get_Ivory(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Khaki(This,value) (This)->lpVtbl->get_Khaki(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Lavender(This,value) (This)->lpVtbl->get_Lavender(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LavenderBlush(This,value) (This)->lpVtbl->get_LavenderBlush(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LawnGreen(This,value) (This)->lpVtbl->get_LawnGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LemonChiffon(This,value) (This)->lpVtbl->get_LemonChiffon(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightBlue(This,value) (This)->lpVtbl->get_LightBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightCoral(This,value) (This)->lpVtbl->get_LightCoral(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightCyan(This,value) (This)->lpVtbl->get_LightCyan(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightGoldenrodYellow(This,value) (This)->lpVtbl->get_LightGoldenrodYellow(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightGreen(This,value) (This)->lpVtbl->get_LightGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightGray(This,value) (This)->lpVtbl->get_LightGray(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightPink(This,value) (This)->lpVtbl->get_LightPink(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSalmon(This,value) (This)->lpVtbl->get_LightSalmon(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSeaGreen(This,value) (This)->lpVtbl->get_LightSeaGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSkyBlue(This,value) (This)->lpVtbl->get_LightSkyBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSlateGray(This,value) (This)->lpVtbl->get_LightSlateGray(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSteelBlue(This,value) (This)->lpVtbl->get_LightSteelBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LightYellow(This,value) (This)->lpVtbl->get_LightYellow(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Lime(This,value) (This)->lpVtbl->get_Lime(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_LimeGreen(This,value) (This)->lpVtbl->get_LimeGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Linen(This,value) (This)->lpVtbl->get_Linen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Magenta(This,value) (This)->lpVtbl->get_Magenta(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Maroon(This,value) (This)->lpVtbl->get_Maroon(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumAquamarine(This,value) (This)->lpVtbl->get_MediumAquamarine(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumBlue(This,value) (This)->lpVtbl->get_MediumBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumOrchid(This,value) (This)->lpVtbl->get_MediumOrchid(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumPurple(This,value) (This)->lpVtbl->get_MediumPurple(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumSeaGreen(This,value) (This)->lpVtbl->get_MediumSeaGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumSlateBlue(This,value) (This)->lpVtbl->get_MediumSlateBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumSpringGreen(This,value) (This)->lpVtbl->get_MediumSpringGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumTurquoise(This,value) (This)->lpVtbl->get_MediumTurquoise(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumVioletRed(This,value) (This)->lpVtbl->get_MediumVioletRed(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MidnightBlue(This,value) (This)->lpVtbl->get_MidnightBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MintCream(This,value) (This)->lpVtbl->get_MintCream(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_MistyRose(This,value) (This)->lpVtbl->get_MistyRose(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Moccasin(This,value) (This)->lpVtbl->get_Moccasin(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_NavajoWhite(This,value) (This)->lpVtbl->get_NavajoWhite(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Navy(This,value) (This)->lpVtbl->get_Navy(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_OldLace(This,value) (This)->lpVtbl->get_OldLace(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Olive(This,value) (This)->lpVtbl->get_Olive(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_OliveDrab(This,value) (This)->lpVtbl->get_OliveDrab(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Orange(This,value) (This)->lpVtbl->get_Orange(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_OrangeRed(This,value) (This)->lpVtbl->get_OrangeRed(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Orchid(This,value) (This)->lpVtbl->get_Orchid(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleGoldenrod(This,value) (This)->lpVtbl->get_PaleGoldenrod(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleGreen(This,value) (This)->lpVtbl->get_PaleGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleTurquoise(This,value) (This)->lpVtbl->get_PaleTurquoise(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleVioletRed(This,value) (This)->lpVtbl->get_PaleVioletRed(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_PapayaWhip(This,value) (This)->lpVtbl->get_PapayaWhip(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_PeachPuff(This,value) (This)->lpVtbl->get_PeachPuff(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Peru(This,value) (This)->lpVtbl->get_Peru(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Pink(This,value) (This)->lpVtbl->get_Pink(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Plum(This,value) (This)->lpVtbl->get_Plum(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_PowderBlue(This,value) (This)->lpVtbl->get_PowderBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Purple(This,value) (This)->lpVtbl->get_Purple(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Red(This,value) (This)->lpVtbl->get_Red(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_RosyBrown(This,value) (This)->lpVtbl->get_RosyBrown(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_RoyalBlue(This,value) (This)->lpVtbl->get_RoyalBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_SaddleBrown(This,value) (This)->lpVtbl->get_SaddleBrown(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Salmon(This,value) (This)->lpVtbl->get_Salmon(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_SandyBrown(This,value) (This)->lpVtbl->get_SandyBrown(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_SeaGreen(This,value) (This)->lpVtbl->get_SeaGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_SeaShell(This,value) (This)->lpVtbl->get_SeaShell(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Sienna(This,value) (This)->lpVtbl->get_Sienna(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Silver(This,value) (This)->lpVtbl->get_Silver(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_SkyBlue(This,value) (This)->lpVtbl->get_SkyBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_SlateBlue(This,value) (This)->lpVtbl->get_SlateBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_SlateGray(This,value) (This)->lpVtbl->get_SlateGray(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Snow(This,value) (This)->lpVtbl->get_Snow(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_SpringGreen(This,value) (This)->lpVtbl->get_SpringGreen(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_SteelBlue(This,value) (This)->lpVtbl->get_SteelBlue(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Tan(This,value) (This)->lpVtbl->get_Tan(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Teal(This,value) (This)->lpVtbl->get_Teal(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Thistle(This,value) (This)->lpVtbl->get_Thistle(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Tomato(This,value) (This)->lpVtbl->get_Tomato(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Transparent(This,value) (This)->lpVtbl->get_Transparent(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Turquoise(This,value) (This)->lpVtbl->get_Turquoise(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Violet(This,value) (This)->lpVtbl->get_Violet(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Wheat(This,value) (This)->lpVtbl->get_Wheat(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_White(This,value) (This)->lpVtbl->get_White(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_WhiteSmoke(This,value) (This)->lpVtbl->get_WhiteSmoke(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_Yellow(This,value) (This)->lpVtbl->get_Yellow(This,value)
#define __x_ABI_CWindows_CUI_CIColorsStatics_get_YellowGreen(This,value) (This)->lpVtbl->get_YellowGreen(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_QueryInterface(__x_ABI_CWindows_CUI_CIColorsStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CIColorsStatics_AddRef(__x_ABI_CWindows_CUI_CIColorsStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CIColorsStatics_Release(__x_ABI_CWindows_CUI_CIColorsStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_GetIids(__x_ABI_CWindows_CUI_CIColorsStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_GetRuntimeClassName(__x_ABI_CWindows_CUI_CIColorsStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_GetTrustLevel(__x_ABI_CWindows_CUI_CIColorsStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IColorsStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_AliceBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_AliceBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_AntiqueWhite(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_AntiqueWhite(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Aqua(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Aqua(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Aquamarine(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Aquamarine(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Azure(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Azure(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Beige(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Beige(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Bisque(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Bisque(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Black(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Black(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_BlanchedAlmond(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_BlanchedAlmond(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Blue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Blue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_BlueViolet(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_BlueViolet(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Brown(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Brown(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_BurlyWood(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_BurlyWood(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_CadetBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_CadetBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Chartreuse(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Chartreuse(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Chocolate(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Chocolate(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Coral(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Coral(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_CornflowerBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_CornflowerBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Cornsilk(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Cornsilk(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Crimson(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Crimson(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Cyan(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Cyan(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkCyan(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkCyan(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkGoldenrod(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkGoldenrod(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkGray(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkGray(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkKhaki(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkKhaki(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkMagenta(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkMagenta(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkOliveGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkOliveGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkOrange(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkOrange(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkOrchid(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkOrchid(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkRed(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkRed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSalmon(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkSalmon(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSeaGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkSeaGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSlateBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkSlateBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSlateGray(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkSlateGray(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkTurquoise(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkTurquoise(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkViolet(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DarkViolet(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DeepPink(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DeepPink(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DeepSkyBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DeepSkyBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DimGray(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DimGray(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_DodgerBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_DodgerBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Firebrick(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Firebrick(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_FloralWhite(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_FloralWhite(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_ForestGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_ForestGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Fuchsia(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Fuchsia(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Gainsboro(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Gainsboro(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_GhostWhite(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_GhostWhite(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Gold(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Gold(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Goldenrod(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Goldenrod(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Gray(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Gray(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Green(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Green(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_GreenYellow(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_GreenYellow(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Honeydew(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Honeydew(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_HotPink(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_HotPink(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_IndianRed(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_IndianRed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Indigo(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Indigo(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Ivory(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Ivory(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Khaki(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Khaki(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Lavender(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Lavender(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LavenderBlush(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LavenderBlush(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LawnGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LawnGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LemonChiffon(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LemonChiffon(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightCoral(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightCoral(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightCyan(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightCyan(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightGoldenrodYellow(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightGoldenrodYellow(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightGray(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightGray(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightPink(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightPink(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSalmon(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightSalmon(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSeaGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightSeaGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSkyBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightSkyBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSlateGray(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightSlateGray(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSteelBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightSteelBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LightYellow(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LightYellow(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Lime(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Lime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_LimeGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_LimeGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Linen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Linen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Magenta(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Magenta(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Maroon(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Maroon(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumAquamarine(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MediumAquamarine(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MediumBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumOrchid(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MediumOrchid(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumPurple(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MediumPurple(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumSeaGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MediumSeaGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumSlateBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MediumSlateBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumSpringGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MediumSpringGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumTurquoise(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MediumTurquoise(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumVioletRed(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MediumVioletRed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MidnightBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MidnightBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MintCream(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MintCream(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_MistyRose(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_MistyRose(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Moccasin(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Moccasin(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_NavajoWhite(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_NavajoWhite(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Navy(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Navy(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_OldLace(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_OldLace(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Olive(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Olive(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_OliveDrab(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_OliveDrab(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Orange(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Orange(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_OrangeRed(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_OrangeRed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Orchid(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Orchid(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleGoldenrod(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_PaleGoldenrod(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_PaleGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleTurquoise(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_PaleTurquoise(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleVioletRed(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_PaleVioletRed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_PapayaWhip(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_PapayaWhip(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_PeachPuff(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_PeachPuff(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Peru(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Peru(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Pink(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Pink(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Plum(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Plum(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_PowderBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_PowderBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Purple(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Purple(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Red(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Red(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_RosyBrown(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_RosyBrown(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_RoyalBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_RoyalBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_SaddleBrown(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_SaddleBrown(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Salmon(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Salmon(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_SandyBrown(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_SandyBrown(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_SeaGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_SeaGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_SeaShell(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_SeaShell(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Sienna(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Sienna(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Silver(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Silver(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_SkyBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_SkyBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_SlateBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_SlateBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_SlateGray(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_SlateGray(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Snow(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Snow(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_SpringGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_SpringGreen(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_SteelBlue(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_SteelBlue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Tan(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Tan(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Teal(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Teal(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Thistle(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Thistle(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Tomato(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Tomato(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Transparent(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Transparent(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Turquoise(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Turquoise(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Violet(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Violet(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Wheat(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Wheat(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_White(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_White(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_WhiteSmoke(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_WhiteSmoke(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_Yellow(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Yellow(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIColorsStatics_get_YellowGreen(__x_ABI_CWindows_CUI_CIColorsStatics* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_YellowGreen(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI
#define IID_IColorsStatics IID___x_ABI_CWindows_CUI_CIColorsStatics
#define IColorsStaticsVtbl __x_ABI_CWindows_CUI_CIColorsStaticsVtbl
#define IColorsStatics __x_ABI_CWindows_CUI_CIColorsStatics
#define IColorsStatics_QueryInterface __x_ABI_CWindows_CUI_CIColorsStatics_QueryInterface
#define IColorsStatics_AddRef __x_ABI_CWindows_CUI_CIColorsStatics_AddRef
#define IColorsStatics_Release __x_ABI_CWindows_CUI_CIColorsStatics_Release
#define IColorsStatics_GetIids __x_ABI_CWindows_CUI_CIColorsStatics_GetIids
#define IColorsStatics_GetRuntimeClassName __x_ABI_CWindows_CUI_CIColorsStatics_GetRuntimeClassName
#define IColorsStatics_GetTrustLevel __x_ABI_CWindows_CUI_CIColorsStatics_GetTrustLevel
#define IColorsStatics_get_AliceBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_AliceBlue
#define IColorsStatics_get_AntiqueWhite __x_ABI_CWindows_CUI_CIColorsStatics_get_AntiqueWhite
#define IColorsStatics_get_Aqua __x_ABI_CWindows_CUI_CIColorsStatics_get_Aqua
#define IColorsStatics_get_Aquamarine __x_ABI_CWindows_CUI_CIColorsStatics_get_Aquamarine
#define IColorsStatics_get_Azure __x_ABI_CWindows_CUI_CIColorsStatics_get_Azure
#define IColorsStatics_get_Beige __x_ABI_CWindows_CUI_CIColorsStatics_get_Beige
#define IColorsStatics_get_Bisque __x_ABI_CWindows_CUI_CIColorsStatics_get_Bisque
#define IColorsStatics_get_Black __x_ABI_CWindows_CUI_CIColorsStatics_get_Black
#define IColorsStatics_get_BlanchedAlmond __x_ABI_CWindows_CUI_CIColorsStatics_get_BlanchedAlmond
#define IColorsStatics_get_Blue __x_ABI_CWindows_CUI_CIColorsStatics_get_Blue
#define IColorsStatics_get_BlueViolet __x_ABI_CWindows_CUI_CIColorsStatics_get_BlueViolet
#define IColorsStatics_get_Brown __x_ABI_CWindows_CUI_CIColorsStatics_get_Brown
#define IColorsStatics_get_BurlyWood __x_ABI_CWindows_CUI_CIColorsStatics_get_BurlyWood
#define IColorsStatics_get_CadetBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_CadetBlue
#define IColorsStatics_get_Chartreuse __x_ABI_CWindows_CUI_CIColorsStatics_get_Chartreuse
#define IColorsStatics_get_Chocolate __x_ABI_CWindows_CUI_CIColorsStatics_get_Chocolate
#define IColorsStatics_get_Coral __x_ABI_CWindows_CUI_CIColorsStatics_get_Coral
#define IColorsStatics_get_CornflowerBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_CornflowerBlue
#define IColorsStatics_get_Cornsilk __x_ABI_CWindows_CUI_CIColorsStatics_get_Cornsilk
#define IColorsStatics_get_Crimson __x_ABI_CWindows_CUI_CIColorsStatics_get_Crimson
#define IColorsStatics_get_Cyan __x_ABI_CWindows_CUI_CIColorsStatics_get_Cyan
#define IColorsStatics_get_DarkBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkBlue
#define IColorsStatics_get_DarkCyan __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkCyan
#define IColorsStatics_get_DarkGoldenrod __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkGoldenrod
#define IColorsStatics_get_DarkGray __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkGray
#define IColorsStatics_get_DarkGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkGreen
#define IColorsStatics_get_DarkKhaki __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkKhaki
#define IColorsStatics_get_DarkMagenta __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkMagenta
#define IColorsStatics_get_DarkOliveGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkOliveGreen
#define IColorsStatics_get_DarkOrange __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkOrange
#define IColorsStatics_get_DarkOrchid __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkOrchid
#define IColorsStatics_get_DarkRed __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkRed
#define IColorsStatics_get_DarkSalmon __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSalmon
#define IColorsStatics_get_DarkSeaGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSeaGreen
#define IColorsStatics_get_DarkSlateBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSlateBlue
#define IColorsStatics_get_DarkSlateGray __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkSlateGray
#define IColorsStatics_get_DarkTurquoise __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkTurquoise
#define IColorsStatics_get_DarkViolet __x_ABI_CWindows_CUI_CIColorsStatics_get_DarkViolet
#define IColorsStatics_get_DeepPink __x_ABI_CWindows_CUI_CIColorsStatics_get_DeepPink
#define IColorsStatics_get_DeepSkyBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_DeepSkyBlue
#define IColorsStatics_get_DimGray __x_ABI_CWindows_CUI_CIColorsStatics_get_DimGray
#define IColorsStatics_get_DodgerBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_DodgerBlue
#define IColorsStatics_get_Firebrick __x_ABI_CWindows_CUI_CIColorsStatics_get_Firebrick
#define IColorsStatics_get_FloralWhite __x_ABI_CWindows_CUI_CIColorsStatics_get_FloralWhite
#define IColorsStatics_get_ForestGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_ForestGreen
#define IColorsStatics_get_Fuchsia __x_ABI_CWindows_CUI_CIColorsStatics_get_Fuchsia
#define IColorsStatics_get_Gainsboro __x_ABI_CWindows_CUI_CIColorsStatics_get_Gainsboro
#define IColorsStatics_get_GhostWhite __x_ABI_CWindows_CUI_CIColorsStatics_get_GhostWhite
#define IColorsStatics_get_Gold __x_ABI_CWindows_CUI_CIColorsStatics_get_Gold
#define IColorsStatics_get_Goldenrod __x_ABI_CWindows_CUI_CIColorsStatics_get_Goldenrod
#define IColorsStatics_get_Gray __x_ABI_CWindows_CUI_CIColorsStatics_get_Gray
#define IColorsStatics_get_Green __x_ABI_CWindows_CUI_CIColorsStatics_get_Green
#define IColorsStatics_get_GreenYellow __x_ABI_CWindows_CUI_CIColorsStatics_get_GreenYellow
#define IColorsStatics_get_Honeydew __x_ABI_CWindows_CUI_CIColorsStatics_get_Honeydew
#define IColorsStatics_get_HotPink __x_ABI_CWindows_CUI_CIColorsStatics_get_HotPink
#define IColorsStatics_get_IndianRed __x_ABI_CWindows_CUI_CIColorsStatics_get_IndianRed
#define IColorsStatics_get_Indigo __x_ABI_CWindows_CUI_CIColorsStatics_get_Indigo
#define IColorsStatics_get_Ivory __x_ABI_CWindows_CUI_CIColorsStatics_get_Ivory
#define IColorsStatics_get_Khaki __x_ABI_CWindows_CUI_CIColorsStatics_get_Khaki
#define IColorsStatics_get_Lavender __x_ABI_CWindows_CUI_CIColorsStatics_get_Lavender
#define IColorsStatics_get_LavenderBlush __x_ABI_CWindows_CUI_CIColorsStatics_get_LavenderBlush
#define IColorsStatics_get_LawnGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_LawnGreen
#define IColorsStatics_get_LemonChiffon __x_ABI_CWindows_CUI_CIColorsStatics_get_LemonChiffon
#define IColorsStatics_get_LightBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_LightBlue
#define IColorsStatics_get_LightCoral __x_ABI_CWindows_CUI_CIColorsStatics_get_LightCoral
#define IColorsStatics_get_LightCyan __x_ABI_CWindows_CUI_CIColorsStatics_get_LightCyan
#define IColorsStatics_get_LightGoldenrodYellow __x_ABI_CWindows_CUI_CIColorsStatics_get_LightGoldenrodYellow
#define IColorsStatics_get_LightGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_LightGreen
#define IColorsStatics_get_LightGray __x_ABI_CWindows_CUI_CIColorsStatics_get_LightGray
#define IColorsStatics_get_LightPink __x_ABI_CWindows_CUI_CIColorsStatics_get_LightPink
#define IColorsStatics_get_LightSalmon __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSalmon
#define IColorsStatics_get_LightSeaGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSeaGreen
#define IColorsStatics_get_LightSkyBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSkyBlue
#define IColorsStatics_get_LightSlateGray __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSlateGray
#define IColorsStatics_get_LightSteelBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_LightSteelBlue
#define IColorsStatics_get_LightYellow __x_ABI_CWindows_CUI_CIColorsStatics_get_LightYellow
#define IColorsStatics_get_Lime __x_ABI_CWindows_CUI_CIColorsStatics_get_Lime
#define IColorsStatics_get_LimeGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_LimeGreen
#define IColorsStatics_get_Linen __x_ABI_CWindows_CUI_CIColorsStatics_get_Linen
#define IColorsStatics_get_Magenta __x_ABI_CWindows_CUI_CIColorsStatics_get_Magenta
#define IColorsStatics_get_Maroon __x_ABI_CWindows_CUI_CIColorsStatics_get_Maroon
#define IColorsStatics_get_MediumAquamarine __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumAquamarine
#define IColorsStatics_get_MediumBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumBlue
#define IColorsStatics_get_MediumOrchid __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumOrchid
#define IColorsStatics_get_MediumPurple __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumPurple
#define IColorsStatics_get_MediumSeaGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumSeaGreen
#define IColorsStatics_get_MediumSlateBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumSlateBlue
#define IColorsStatics_get_MediumSpringGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumSpringGreen
#define IColorsStatics_get_MediumTurquoise __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumTurquoise
#define IColorsStatics_get_MediumVioletRed __x_ABI_CWindows_CUI_CIColorsStatics_get_MediumVioletRed
#define IColorsStatics_get_MidnightBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_MidnightBlue
#define IColorsStatics_get_MintCream __x_ABI_CWindows_CUI_CIColorsStatics_get_MintCream
#define IColorsStatics_get_MistyRose __x_ABI_CWindows_CUI_CIColorsStatics_get_MistyRose
#define IColorsStatics_get_Moccasin __x_ABI_CWindows_CUI_CIColorsStatics_get_Moccasin
#define IColorsStatics_get_NavajoWhite __x_ABI_CWindows_CUI_CIColorsStatics_get_NavajoWhite
#define IColorsStatics_get_Navy __x_ABI_CWindows_CUI_CIColorsStatics_get_Navy
#define IColorsStatics_get_OldLace __x_ABI_CWindows_CUI_CIColorsStatics_get_OldLace
#define IColorsStatics_get_Olive __x_ABI_CWindows_CUI_CIColorsStatics_get_Olive
#define IColorsStatics_get_OliveDrab __x_ABI_CWindows_CUI_CIColorsStatics_get_OliveDrab
#define IColorsStatics_get_Orange __x_ABI_CWindows_CUI_CIColorsStatics_get_Orange
#define IColorsStatics_get_OrangeRed __x_ABI_CWindows_CUI_CIColorsStatics_get_OrangeRed
#define IColorsStatics_get_Orchid __x_ABI_CWindows_CUI_CIColorsStatics_get_Orchid
#define IColorsStatics_get_PaleGoldenrod __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleGoldenrod
#define IColorsStatics_get_PaleGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleGreen
#define IColorsStatics_get_PaleTurquoise __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleTurquoise
#define IColorsStatics_get_PaleVioletRed __x_ABI_CWindows_CUI_CIColorsStatics_get_PaleVioletRed
#define IColorsStatics_get_PapayaWhip __x_ABI_CWindows_CUI_CIColorsStatics_get_PapayaWhip
#define IColorsStatics_get_PeachPuff __x_ABI_CWindows_CUI_CIColorsStatics_get_PeachPuff
#define IColorsStatics_get_Peru __x_ABI_CWindows_CUI_CIColorsStatics_get_Peru
#define IColorsStatics_get_Pink __x_ABI_CWindows_CUI_CIColorsStatics_get_Pink
#define IColorsStatics_get_Plum __x_ABI_CWindows_CUI_CIColorsStatics_get_Plum
#define IColorsStatics_get_PowderBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_PowderBlue
#define IColorsStatics_get_Purple __x_ABI_CWindows_CUI_CIColorsStatics_get_Purple
#define IColorsStatics_get_Red __x_ABI_CWindows_CUI_CIColorsStatics_get_Red
#define IColorsStatics_get_RosyBrown __x_ABI_CWindows_CUI_CIColorsStatics_get_RosyBrown
#define IColorsStatics_get_RoyalBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_RoyalBlue
#define IColorsStatics_get_SaddleBrown __x_ABI_CWindows_CUI_CIColorsStatics_get_SaddleBrown
#define IColorsStatics_get_Salmon __x_ABI_CWindows_CUI_CIColorsStatics_get_Salmon
#define IColorsStatics_get_SandyBrown __x_ABI_CWindows_CUI_CIColorsStatics_get_SandyBrown
#define IColorsStatics_get_SeaGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_SeaGreen
#define IColorsStatics_get_SeaShell __x_ABI_CWindows_CUI_CIColorsStatics_get_SeaShell
#define IColorsStatics_get_Sienna __x_ABI_CWindows_CUI_CIColorsStatics_get_Sienna
#define IColorsStatics_get_Silver __x_ABI_CWindows_CUI_CIColorsStatics_get_Silver
#define IColorsStatics_get_SkyBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_SkyBlue
#define IColorsStatics_get_SlateBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_SlateBlue
#define IColorsStatics_get_SlateGray __x_ABI_CWindows_CUI_CIColorsStatics_get_SlateGray
#define IColorsStatics_get_Snow __x_ABI_CWindows_CUI_CIColorsStatics_get_Snow
#define IColorsStatics_get_SpringGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_SpringGreen
#define IColorsStatics_get_SteelBlue __x_ABI_CWindows_CUI_CIColorsStatics_get_SteelBlue
#define IColorsStatics_get_Tan __x_ABI_CWindows_CUI_CIColorsStatics_get_Tan
#define IColorsStatics_get_Teal __x_ABI_CWindows_CUI_CIColorsStatics_get_Teal
#define IColorsStatics_get_Thistle __x_ABI_CWindows_CUI_CIColorsStatics_get_Thistle
#define IColorsStatics_get_Tomato __x_ABI_CWindows_CUI_CIColorsStatics_get_Tomato
#define IColorsStatics_get_Transparent __x_ABI_CWindows_CUI_CIColorsStatics_get_Transparent
#define IColorsStatics_get_Turquoise __x_ABI_CWindows_CUI_CIColorsStatics_get_Turquoise
#define IColorsStatics_get_Violet __x_ABI_CWindows_CUI_CIColorsStatics_get_Violet
#define IColorsStatics_get_Wheat __x_ABI_CWindows_CUI_CIColorsStatics_get_Wheat
#define IColorsStatics_get_White __x_ABI_CWindows_CUI_CIColorsStatics_get_White
#define IColorsStatics_get_WhiteSmoke __x_ABI_CWindows_CUI_CIColorsStatics_get_WhiteSmoke
#define IColorsStatics_get_Yellow __x_ABI_CWindows_CUI_CIColorsStatics_get_Yellow
#define IColorsStatics_get_YellowGreen __x_ABI_CWindows_CUI_CIColorsStatics_get_YellowGreen
#endif /* WIDL_using_Windows_UI */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CIColorsStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IUIContentRoot interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef ____x_ABI_CWindows_CUI_CIUIContentRoot_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CIUIContentRoot_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CIUIContentRoot, 0x1dfcbac6, 0xb36b, 0x5cb9, 0x9b,0xc5, 0x2b,0x7a,0x0e,0xdd,0xc3,0x78);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            MIDL_INTERFACE("1dfcbac6-b36b-5cb9-9bc5-2b7a0eddc378")
            IUIContentRoot : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_UIContext(
                    ABI::Windows::UI::IUIContext **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CIUIContentRoot, 0x1dfcbac6, 0xb36b, 0x5cb9, 0x9b,0xc5, 0x2b,0x7a,0x0e,0xdd,0xc3,0x78)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CIUIContentRootVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CIUIContentRoot *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CIUIContentRoot *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CIUIContentRoot *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CIUIContentRoot *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CIUIContentRoot *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CIUIContentRoot *This,
        TrustLevel *trustLevel);

    /*** IUIContentRoot methods ***/
    HRESULT (STDMETHODCALLTYPE *get_UIContext)(
        __x_ABI_CWindows_CUI_CIUIContentRoot *This,
        __x_ABI_CWindows_CUI_CIUIContext **value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CIUIContentRootVtbl;

interface __x_ABI_CWindows_CUI_CIUIContentRoot {
    CONST_VTBL __x_ABI_CWindows_CUI_CIUIContentRootVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CIUIContentRoot_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CIUIContentRoot_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CIUIContentRoot_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CIUIContentRoot_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CIUIContentRoot_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CIUIContentRoot_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUIContentRoot methods ***/
#define __x_ABI_CWindows_CUI_CIUIContentRoot_get_UIContext(This,value) (This)->lpVtbl->get_UIContext(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIUIContentRoot_QueryInterface(__x_ABI_CWindows_CUI_CIUIContentRoot* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CIUIContentRoot_AddRef(__x_ABI_CWindows_CUI_CIUIContentRoot* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CIUIContentRoot_Release(__x_ABI_CWindows_CUI_CIUIContentRoot* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIUIContentRoot_GetIids(__x_ABI_CWindows_CUI_CIUIContentRoot* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIUIContentRoot_GetRuntimeClassName(__x_ABI_CWindows_CUI_CIUIContentRoot* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIUIContentRoot_GetTrustLevel(__x_ABI_CWindows_CUI_CIUIContentRoot* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUIContentRoot methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIUIContentRoot_get_UIContext(__x_ABI_CWindows_CUI_CIUIContentRoot* This,__x_ABI_CWindows_CUI_CIUIContext **value) {
    return This->lpVtbl->get_UIContext(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI
#define IID_IUIContentRoot IID___x_ABI_CWindows_CUI_CIUIContentRoot
#define IUIContentRootVtbl __x_ABI_CWindows_CUI_CIUIContentRootVtbl
#define IUIContentRoot __x_ABI_CWindows_CUI_CIUIContentRoot
#define IUIContentRoot_QueryInterface __x_ABI_CWindows_CUI_CIUIContentRoot_QueryInterface
#define IUIContentRoot_AddRef __x_ABI_CWindows_CUI_CIUIContentRoot_AddRef
#define IUIContentRoot_Release __x_ABI_CWindows_CUI_CIUIContentRoot_Release
#define IUIContentRoot_GetIids __x_ABI_CWindows_CUI_CIUIContentRoot_GetIids
#define IUIContentRoot_GetRuntimeClassName __x_ABI_CWindows_CUI_CIUIContentRoot_GetRuntimeClassName
#define IUIContentRoot_GetTrustLevel __x_ABI_CWindows_CUI_CIUIContentRoot_GetTrustLevel
#define IUIContentRoot_get_UIContext __x_ABI_CWindows_CUI_CIUIContentRoot_get_UIContext
#endif /* WIDL_using_Windows_UI */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CIUIContentRoot_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*****************************************************************************
 * IUIContext interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef ____x_ABI_CWindows_CUI_CIUIContext_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CIUIContext_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CIUIContext, 0xbb5cfacd, 0x5bd8, 0x59d0, 0xa5,0x9e, 0x1c,0x17,0xa4,0xd6,0xd2,0x43);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            MIDL_INTERFACE("bb5cfacd-5bd8-59d0-a59e-1c17a4d6d243")
            IUIContext : public IInspectable
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CIUIContext, 0xbb5cfacd, 0x5bd8, 0x59d0, 0xa5,0x9e, 0x1c,0x17,0xa4,0xd6,0xd2,0x43)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CIUIContextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CIUIContext *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CIUIContext *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CIUIContext *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CIUIContext *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CIUIContext *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CIUIContext *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CIUIContextVtbl;

interface __x_ABI_CWindows_CUI_CIUIContext {
    CONST_VTBL __x_ABI_CWindows_CUI_CIUIContextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CIUIContext_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CIUIContext_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CIUIContext_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CIUIContext_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CIUIContext_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CIUIContext_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIUIContext_QueryInterface(__x_ABI_CWindows_CUI_CIUIContext* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CIUIContext_AddRef(__x_ABI_CWindows_CUI_CIUIContext* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CIUIContext_Release(__x_ABI_CWindows_CUI_CIUIContext* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CIUIContext_GetIids(__x_ABI_CWindows_CUI_CIUIContext* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIUIContext_GetRuntimeClassName(__x_ABI_CWindows_CUI_CIUIContext* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CIUIContext_GetTrustLevel(__x_ABI_CWindows_CUI_CIUIContext* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_UI
#define IID_IUIContext IID___x_ABI_CWindows_CUI_CIUIContext
#define IUIContextVtbl __x_ABI_CWindows_CUI_CIUIContextVtbl
#define IUIContext __x_ABI_CWindows_CUI_CIUIContext
#define IUIContext_QueryInterface __x_ABI_CWindows_CUI_CIUIContext_QueryInterface
#define IUIContext_AddRef __x_ABI_CWindows_CUI_CIUIContext_AddRef
#define IUIContext_Release __x_ABI_CWindows_CUI_CIUIContext_Release
#define IUIContext_GetIids __x_ABI_CWindows_CUI_CIUIContext_GetIids
#define IUIContext_GetRuntimeClassName __x_ABI_CWindows_CUI_CIUIContext_GetRuntimeClassName
#define IUIContext_GetTrustLevel __x_ABI_CWindows_CUI_CIUIContext_GetTrustLevel
#endif /* WIDL_using_Windows_UI */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CIUIContext_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*
 * Class Windows.UI.ColorHelper
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_ColorHelper_DEFINED
#define RUNTIMECLASS_Windows_UI_ColorHelper_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ColorHelper[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','l','o','r','H','e','l','p','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ColorHelper[] = L"Windows.UI.ColorHelper";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ColorHelper[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','l','o','r','H','e','l','p','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ColorHelper_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Colors
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Colors_DEFINED
#define RUNTIMECLASS_Windows_UI_Colors_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Colors[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','l','o','r','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Colors[] = L"Windows.UI.Colors";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Colors[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','l','o','r','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Colors_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.UIContentRoot
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef RUNTIMECLASS_Windows_UI_UIContentRoot_DEFINED
#define RUNTIMECLASS_Windows_UI_UIContentRoot_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_UIContentRoot[] = {'W','i','n','d','o','w','s','.','U','I','.','U','I','C','o','n','t','e','n','t','R','o','o','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_UIContentRoot[] = L"Windows.UI.UIContentRoot";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_UIContentRoot[] = {'W','i','n','d','o','w','s','.','U','I','.','U','I','C','o','n','t','e','n','t','R','o','o','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_UIContentRoot_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*
 * Class Windows.UI.UIContext
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef RUNTIMECLASS_Windows_UI_UIContext_DEFINED
#define RUNTIMECLASS_Windows_UI_UIContext_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_UIContext[] = {'W','i','n','d','o','w','s','.','U','I','.','U','I','C','o','n','t','e','x','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_UIContext[] = L"Windows.UI.UIContext";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_UIContext[] = {'W','i','n','d','o','w','s','.','U','I','.','U','I','C','o','n','t','e','x','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_UIContext_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*****************************************************************************
 * IIterable<ABI::Windows::UI::Color > interface
 */
#ifndef ____FIIterable_1_Color_INTERFACE_DEFINED__
#define ____FIIterable_1_Color_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Color, 0x932eef5e, 0x2c2f, 0x5eae, 0x92,0x9a, 0x74,0xe9,0x73,0xb5,0x7c,0x27);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("932eef5e-2c2f-5eae-929a-74e973b57c27")
                IIterable<ABI::Windows::UI::Color > : IIterable_impl<ABI::Windows::UI::Color >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Color, 0x932eef5e, 0x2c2f, 0x5eae, 0x92,0x9a, 0x74,0xe9,0x73,0xb5,0x7c,0x27)
#endif
#else
typedef struct __FIIterable_1_ColorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Color *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Color *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Color *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Color *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Color *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Color *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::UI::Color > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Color *This,
        __FIIterator_1_Color **value);

    END_INTERFACE
} __FIIterable_1_ColorVtbl;

interface __FIIterable_1_Color {
    CONST_VTBL __FIIterable_1_ColorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Color_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Color_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Color_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Color_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Color_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Color_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::UI::Color > methods ***/
#define __FIIterable_1_Color_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Color_QueryInterface(__FIIterable_1_Color* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Color_AddRef(__FIIterable_1_Color* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Color_Release(__FIIterable_1_Color* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Color_GetIids(__FIIterable_1_Color* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Color_GetRuntimeClassName(__FIIterable_1_Color* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Color_GetTrustLevel(__FIIterable_1_Color* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::UI::Color > methods ***/
static inline HRESULT __FIIterable_1_Color_First(__FIIterable_1_Color* This,__FIIterator_1_Color **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_Color IID___FIIterable_1_Color
#define IIterable_ColorVtbl __FIIterable_1_ColorVtbl
#define IIterable_Color __FIIterable_1_Color
#define IIterable_Color_QueryInterface __FIIterable_1_Color_QueryInterface
#define IIterable_Color_AddRef __FIIterable_1_Color_AddRef
#define IIterable_Color_Release __FIIterable_1_Color_Release
#define IIterable_Color_GetIids __FIIterable_1_Color_GetIids
#define IIterable_Color_GetRuntimeClassName __FIIterable_1_Color_GetRuntimeClassName
#define IIterable_Color_GetTrustLevel __FIIterable_1_Color_GetTrustLevel
#define IIterable_Color_First __FIIterable_1_Color_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Color_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::UI::WindowId > interface
 */
#ifndef ____FIIterable_1_WindowId_INTERFACE_DEFINED__
#define ____FIIterable_1_WindowId_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_WindowId, 0x745698bf, 0x22ad, 0x5c0d, 0xb0,0xe0, 0x07,0xd3,0x5a,0x1c,0x97,0x19);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("745698bf-22ad-5c0d-b0e0-07d35a1c9719")
                IIterable<ABI::Windows::UI::WindowId > : IIterable_impl<ABI::Windows::UI::WindowId >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_WindowId, 0x745698bf, 0x22ad, 0x5c0d, 0xb0,0xe0, 0x07,0xd3,0x5a,0x1c,0x97,0x19)
#endif
#else
typedef struct __FIIterable_1_WindowIdVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_WindowId *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_WindowId *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_WindowId *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_WindowId *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_WindowId *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_WindowId *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::UI::WindowId > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_WindowId *This,
        __FIIterator_1_WindowId **value);

    END_INTERFACE
} __FIIterable_1_WindowIdVtbl;

interface __FIIterable_1_WindowId {
    CONST_VTBL __FIIterable_1_WindowIdVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_WindowId_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_WindowId_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_WindowId_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_WindowId_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_WindowId_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_WindowId_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::UI::WindowId > methods ***/
#define __FIIterable_1_WindowId_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_WindowId_QueryInterface(__FIIterable_1_WindowId* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_WindowId_AddRef(__FIIterable_1_WindowId* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_WindowId_Release(__FIIterable_1_WindowId* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_WindowId_GetIids(__FIIterable_1_WindowId* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_WindowId_GetRuntimeClassName(__FIIterable_1_WindowId* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_WindowId_GetTrustLevel(__FIIterable_1_WindowId* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::UI::WindowId > methods ***/
static inline HRESULT __FIIterable_1_WindowId_First(__FIIterable_1_WindowId* This,__FIIterator_1_WindowId **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_WindowId IID___FIIterable_1_WindowId
#define IIterable_WindowIdVtbl __FIIterable_1_WindowIdVtbl
#define IIterable_WindowId __FIIterable_1_WindowId
#define IIterable_WindowId_QueryInterface __FIIterable_1_WindowId_QueryInterface
#define IIterable_WindowId_AddRef __FIIterable_1_WindowId_AddRef
#define IIterable_WindowId_Release __FIIterable_1_WindowId_Release
#define IIterable_WindowId_GetIids __FIIterable_1_WindowId_GetIids
#define IIterable_WindowId_GetRuntimeClassName __FIIterable_1_WindowId_GetRuntimeClassName
#define IIterable_WindowId_GetTrustLevel __FIIterable_1_WindowId_GetTrustLevel
#define IIterable_WindowId_First __FIIterable_1_WindowId_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_WindowId_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::UI::Color > interface
 */
#ifndef ____FIIterator_1_Color_INTERFACE_DEFINED__
#define ____FIIterator_1_Color_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Color, 0xc4310b12, 0x7ac2, 0x5e5b, 0xb5,0x11, 0xe5,0x46,0xee,0xa4,0x73,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("c4310b12-7ac2-5e5b-b511-e546eea473b4")
                IIterator<ABI::Windows::UI::Color > : IIterator_impl<ABI::Windows::UI::Color >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Color, 0xc4310b12, 0x7ac2, 0x5e5b, 0xb5,0x11, 0xe5,0x46,0xee,0xa4,0x73,0xb4)
#endif
#else
typedef struct __FIIterator_1_ColorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Color *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Color *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Color *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Color *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Color *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Color *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::UI::Color > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Color *This,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Color *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Color *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Color *This,
        UINT32 items_size,
        __x_ABI_CWindows_CUI_CColor *items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_ColorVtbl;

interface __FIIterator_1_Color {
    CONST_VTBL __FIIterator_1_ColorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Color_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Color_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Color_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Color_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Color_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Color_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::UI::Color > methods ***/
#define __FIIterator_1_Color_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Color_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Color_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Color_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Color_QueryInterface(__FIIterator_1_Color* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Color_AddRef(__FIIterator_1_Color* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Color_Release(__FIIterator_1_Color* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Color_GetIids(__FIIterator_1_Color* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Color_GetRuntimeClassName(__FIIterator_1_Color* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Color_GetTrustLevel(__FIIterator_1_Color* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::UI::Color > methods ***/
static inline HRESULT __FIIterator_1_Color_get_Current(__FIIterator_1_Color* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Color_get_HasCurrent(__FIIterator_1_Color* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Color_MoveNext(__FIIterator_1_Color* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Color_GetMany(__FIIterator_1_Color* This,UINT32 items_size,__x_ABI_CWindows_CUI_CColor *items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_Color IID___FIIterator_1_Color
#define IIterator_ColorVtbl __FIIterator_1_ColorVtbl
#define IIterator_Color __FIIterator_1_Color
#define IIterator_Color_QueryInterface __FIIterator_1_Color_QueryInterface
#define IIterator_Color_AddRef __FIIterator_1_Color_AddRef
#define IIterator_Color_Release __FIIterator_1_Color_Release
#define IIterator_Color_GetIids __FIIterator_1_Color_GetIids
#define IIterator_Color_GetRuntimeClassName __FIIterator_1_Color_GetRuntimeClassName
#define IIterator_Color_GetTrustLevel __FIIterator_1_Color_GetTrustLevel
#define IIterator_Color_get_Current __FIIterator_1_Color_get_Current
#define IIterator_Color_get_HasCurrent __FIIterator_1_Color_get_HasCurrent
#define IIterator_Color_MoveNext __FIIterator_1_Color_MoveNext
#define IIterator_Color_GetMany __FIIterator_1_Color_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Color_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::UI::WindowId > interface
 */
#ifndef ____FIIterator_1_WindowId_INTERFACE_DEFINED__
#define ____FIIterator_1_WindowId_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_WindowId, 0xba0a30a1, 0xc082, 0x5671, 0xac,0x07, 0x7a,0xaa,0x4f,0x26,0x96,0x70);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("ba0a30a1-c082-5671-ac07-7aaa4f269670")
                IIterator<ABI::Windows::UI::WindowId > : IIterator_impl<ABI::Windows::UI::WindowId >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_WindowId, 0xba0a30a1, 0xc082, 0x5671, 0xac,0x07, 0x7a,0xaa,0x4f,0x26,0x96,0x70)
#endif
#else
typedef struct __FIIterator_1_WindowIdVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_WindowId *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_WindowId *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_WindowId *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_WindowId *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_WindowId *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_WindowId *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::UI::WindowId > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_WindowId *This,
        __x_ABI_CWindows_CUI_CWindowId *value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_WindowId *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_WindowId *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_WindowId *This,
        UINT32 items_size,
        __x_ABI_CWindows_CUI_CWindowId *items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_WindowIdVtbl;

interface __FIIterator_1_WindowId {
    CONST_VTBL __FIIterator_1_WindowIdVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_WindowId_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_WindowId_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_WindowId_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_WindowId_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_WindowId_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_WindowId_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::UI::WindowId > methods ***/
#define __FIIterator_1_WindowId_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_WindowId_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_WindowId_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_WindowId_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_WindowId_QueryInterface(__FIIterator_1_WindowId* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_WindowId_AddRef(__FIIterator_1_WindowId* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_WindowId_Release(__FIIterator_1_WindowId* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_WindowId_GetIids(__FIIterator_1_WindowId* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_WindowId_GetRuntimeClassName(__FIIterator_1_WindowId* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_WindowId_GetTrustLevel(__FIIterator_1_WindowId* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::UI::WindowId > methods ***/
static inline HRESULT __FIIterator_1_WindowId_get_Current(__FIIterator_1_WindowId* This,__x_ABI_CWindows_CUI_CWindowId *value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_WindowId_get_HasCurrent(__FIIterator_1_WindowId* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_WindowId_MoveNext(__FIIterator_1_WindowId* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_WindowId_GetMany(__FIIterator_1_WindowId* This,UINT32 items_size,__x_ABI_CWindows_CUI_CWindowId *items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_WindowId IID___FIIterator_1_WindowId
#define IIterator_WindowIdVtbl __FIIterator_1_WindowIdVtbl
#define IIterator_WindowId __FIIterator_1_WindowId
#define IIterator_WindowId_QueryInterface __FIIterator_1_WindowId_QueryInterface
#define IIterator_WindowId_AddRef __FIIterator_1_WindowId_AddRef
#define IIterator_WindowId_Release __FIIterator_1_WindowId_Release
#define IIterator_WindowId_GetIids __FIIterator_1_WindowId_GetIids
#define IIterator_WindowId_GetRuntimeClassName __FIIterator_1_WindowId_GetRuntimeClassName
#define IIterator_WindowId_GetTrustLevel __FIIterator_1_WindowId_GetTrustLevel
#define IIterator_WindowId_get_Current __FIIterator_1_WindowId_get_Current
#define IIterator_WindowId_get_HasCurrent __FIIterator_1_WindowId_get_HasCurrent
#define IIterator_WindowId_MoveNext __FIIterator_1_WindowId_MoveNext
#define IIterator_WindowId_GetMany __FIIterator_1_WindowId_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_WindowId_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::UI::WindowId > interface
 */
#ifndef ____FIVectorView_1_WindowId_INTERFACE_DEFINED__
#define ____FIVectorView_1_WindowId_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_WindowId, 0xf49e7371, 0xb31a, 0x5620, 0xa4,0x2e, 0x7e,0x96,0x90,0x03,0xf0,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("f49e7371-b31a-5620-a42e-7e969003f0ff")
                IVectorView<ABI::Windows::UI::WindowId > : IVectorView_impl<ABI::Windows::UI::WindowId >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_WindowId, 0xf49e7371, 0xb31a, 0x5620, 0xa4,0x2e, 0x7e,0x96,0x90,0x03,0xf0,0xff)
#endif
#else
typedef struct __FIVectorView_1_WindowIdVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_WindowId *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_WindowId *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_WindowId *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_WindowId *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_WindowId *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_WindowId *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::UI::WindowId > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_WindowId *This,
        UINT32 index,
        __x_ABI_CWindows_CUI_CWindowId *value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_WindowId *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_WindowId *This,
        __x_ABI_CWindows_CUI_CWindowId element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_WindowId *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CUI_CWindowId *items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_WindowIdVtbl;

interface __FIVectorView_1_WindowId {
    CONST_VTBL __FIVectorView_1_WindowIdVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_WindowId_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_WindowId_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_WindowId_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_WindowId_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_WindowId_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_WindowId_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::UI::WindowId > methods ***/
#define __FIVectorView_1_WindowId_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_WindowId_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_WindowId_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_WindowId_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_WindowId_QueryInterface(__FIVectorView_1_WindowId* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_WindowId_AddRef(__FIVectorView_1_WindowId* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_WindowId_Release(__FIVectorView_1_WindowId* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_WindowId_GetIids(__FIVectorView_1_WindowId* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_WindowId_GetRuntimeClassName(__FIVectorView_1_WindowId* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_WindowId_GetTrustLevel(__FIVectorView_1_WindowId* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::UI::WindowId > methods ***/
static inline HRESULT __FIVectorView_1_WindowId_GetAt(__FIVectorView_1_WindowId* This,UINT32 index,__x_ABI_CWindows_CUI_CWindowId *value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_WindowId_get_Size(__FIVectorView_1_WindowId* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_WindowId_IndexOf(__FIVectorView_1_WindowId* This,__x_ABI_CWindows_CUI_CWindowId element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_WindowId_GetMany(__FIVectorView_1_WindowId* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CUI_CWindowId *items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_WindowId IID___FIVectorView_1_WindowId
#define IVectorView_WindowIdVtbl __FIVectorView_1_WindowIdVtbl
#define IVectorView_WindowId __FIVectorView_1_WindowId
#define IVectorView_WindowId_QueryInterface __FIVectorView_1_WindowId_QueryInterface
#define IVectorView_WindowId_AddRef __FIVectorView_1_WindowId_AddRef
#define IVectorView_WindowId_Release __FIVectorView_1_WindowId_Release
#define IVectorView_WindowId_GetIids __FIVectorView_1_WindowId_GetIids
#define IVectorView_WindowId_GetRuntimeClassName __FIVectorView_1_WindowId_GetRuntimeClassName
#define IVectorView_WindowId_GetTrustLevel __FIVectorView_1_WindowId_GetTrustLevel
#define IVectorView_WindowId_GetAt __FIVectorView_1_WindowId_GetAt
#define IVectorView_WindowId_get_Size __FIVectorView_1_WindowId_get_Size
#define IVectorView_WindowId_IndexOf __FIVectorView_1_WindowId_IndexOf
#define IVectorView_WindowId_GetMany __FIVectorView_1_WindowId_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_WindowId_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IReference<ABI::Windows::UI::Color > interface
 */
#ifndef ____FIReference_1_Color_INTERFACE_DEFINED__
#define ____FIReference_1_Color_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIReference_1_Color, 0xab8e5d11, 0xb0c1, 0x5a21, 0x95,0xae, 0xf1,0x6b,0xf3,0xa3,0x76,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("ab8e5d11-b0c1-5a21-95ae-f16bf3a37624")
            IReference<ABI::Windows::UI::Color > : IReference_impl<ABI::Windows::UI::Color >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIReference_1_Color, 0xab8e5d11, 0xb0c1, 0x5a21, 0x95,0xae, 0xf1,0x6b,0xf3,0xa3,0x76,0x24)
#endif
#else
typedef struct __FIReference_1_ColorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIReference_1_Color *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIReference_1_Color *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIReference_1_Color *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIReference_1_Color *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIReference_1_Color *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIReference_1_Color *This,
        TrustLevel *trustLevel);

    /*** IReference<ABI::Windows::UI::Color > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __FIReference_1_Color *This,
        __x_ABI_CWindows_CUI_CColor *value);

    END_INTERFACE
} __FIReference_1_ColorVtbl;

interface __FIReference_1_Color {
    CONST_VTBL __FIReference_1_ColorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIReference_1_Color_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIReference_1_Color_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIReference_1_Color_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIReference_1_Color_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIReference_1_Color_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIReference_1_Color_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IReference<ABI::Windows::UI::Color > methods ***/
#define __FIReference_1_Color_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIReference_1_Color_QueryInterface(__FIReference_1_Color* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIReference_1_Color_AddRef(__FIReference_1_Color* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIReference_1_Color_Release(__FIReference_1_Color* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIReference_1_Color_GetIids(__FIReference_1_Color* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIReference_1_Color_GetRuntimeClassName(__FIReference_1_Color* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIReference_1_Color_GetTrustLevel(__FIReference_1_Color* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IReference<ABI::Windows::UI::Color > methods ***/
static inline HRESULT __FIReference_1_Color_get_Value(__FIReference_1_Color* This,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IReference_Color IID___FIReference_1_Color
#define IReference_ColorVtbl __FIReference_1_ColorVtbl
#define IReference_Color __FIReference_1_Color
#define IReference_Color_QueryInterface __FIReference_1_Color_QueryInterface
#define IReference_Color_AddRef __FIReference_1_Color_AddRef
#define IReference_Color_Release __FIReference_1_Color_Release
#define IReference_Color_GetIids __FIReference_1_Color_GetIids
#define IReference_Color_GetRuntimeClassName __FIReference_1_Color_GetRuntimeClassName
#define IReference_Color_GetTrustLevel __FIReference_1_Color_GetTrustLevel
#define IReference_Color_get_Value __FIReference_1_Color_get_Value
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIReference_1_Color_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_ui_h__ */
