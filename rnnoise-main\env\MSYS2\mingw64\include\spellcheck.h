/*** Autogenerated by WIDL 10.12 from include/spellcheck.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __spellcheck_h__
#define __spellcheck_h__

/* Forward declarations */

#ifndef __ISpellingError_FWD_DEFINED__
#define __ISpellingError_FWD_DEFINED__
typedef interface ISpellingError ISpellingError;
#ifdef __cplusplus
interface ISpellingError;
#endif /* __cplusplus */
#endif

#ifndef __IEnumSpellingError_FWD_DEFINED__
#define __IEnumSpellingError_FWD_DEFINED__
typedef interface IEnumSpellingError IEnumSpellingError;
#ifdef __cplusplus
interface IEnumSpellingError;
#endif /* __cplusplus */
#endif

#ifndef __IOptionDescription_FWD_DEFINED__
#define __IOptionDescription_FWD_DEFINED__
typedef interface IOptionDescription IOptionDescription;
#ifdef __cplusplus
interface IOptionDescription;
#endif /* __cplusplus */
#endif

#ifndef __ISpellCheckerChangedEventHandler_FWD_DEFINED__
#define __ISpellCheckerChangedEventHandler_FWD_DEFINED__
typedef interface ISpellCheckerChangedEventHandler ISpellCheckerChangedEventHandler;
#ifdef __cplusplus
interface ISpellCheckerChangedEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __ISpellChecker_FWD_DEFINED__
#define __ISpellChecker_FWD_DEFINED__
typedef interface ISpellChecker ISpellChecker;
#ifdef __cplusplus
interface ISpellChecker;
#endif /* __cplusplus */
#endif

#ifndef __ISpellChecker2_FWD_DEFINED__
#define __ISpellChecker2_FWD_DEFINED__
typedef interface ISpellChecker2 ISpellChecker2;
#ifdef __cplusplus
interface ISpellChecker2;
#endif /* __cplusplus */
#endif

#ifndef __ISpellCheckerFactory_FWD_DEFINED__
#define __ISpellCheckerFactory_FWD_DEFINED__
typedef interface ISpellCheckerFactory ISpellCheckerFactory;
#ifdef __cplusplus
interface ISpellCheckerFactory;
#endif /* __cplusplus */
#endif

#ifndef __IUserDictionariesRegistrar_FWD_DEFINED__
#define __IUserDictionariesRegistrar_FWD_DEFINED__
typedef interface IUserDictionariesRegistrar IUserDictionariesRegistrar;
#ifdef __cplusplus
interface IUserDictionariesRegistrar;
#endif /* __cplusplus */
#endif

#ifndef __SpellCheckerFactory_FWD_DEFINED__
#define __SpellCheckerFactory_FWD_DEFINED__
#ifdef __cplusplus
typedef class SpellCheckerFactory SpellCheckerFactory;
#else
typedef struct SpellCheckerFactory SpellCheckerFactory;
#endif /* defined __cplusplus */
#endif /* defined __SpellCheckerFactory_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef MIN_SPELLING_NTDDI
#define MIN_SPELLING_NTDDI NTDDI_WIN8
#endif
#if NTDDI_VERSION >= MIN_SPELLING_NTDDI
#include <winapifamily.h>
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
typedef enum WORDLIST_TYPE {
    WORDLIST_TYPE_IGNORE = 0,
    WORDLIST_TYPE_ADD = 1,
    WORDLIST_TYPE_EXCLUDE = 2,
    WORDLIST_TYPE_AUTOCORRECT = 3
} WORDLIST_TYPE;
/*****************************************************************************
 * ISpellingError interface
 */
#ifndef __ISpellingError_INTERFACE_DEFINED__
#define __ISpellingError_INTERFACE_DEFINED__

typedef enum CORRECTIVE_ACTION {
    CORRECTIVE_ACTION_NONE = 0,
    CORRECTIVE_ACTION_GET_SUGGESTIONS = 1,
    CORRECTIVE_ACTION_REPLACE = 2,
    CORRECTIVE_ACTION_DELETE = 3
} CORRECTIVE_ACTION;
DEFINE_GUID(IID_ISpellingError, 0xb7c82d61, 0xfbe8, 0x4b47, 0x9b,0x27, 0x6c,0x0d,0x2e,0x0d,0xe0,0xa3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b7c82d61-fbe8-4b47-9b27-6c0d2e0de0a3")
ISpellingError : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_StartIndex(
        ULONG *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Length(
        ULONG *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CorrectiveAction(
        CORRECTIVE_ACTION *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Replacement(
        LPWSTR *val) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISpellingError, 0xb7c82d61, 0xfbe8, 0x4b47, 0x9b,0x27, 0x6c,0x0d,0x2e,0x0d,0xe0,0xa3)
#endif
#else
typedef struct ISpellingErrorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISpellingError *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISpellingError *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISpellingError *This);

    /*** ISpellingError methods ***/
    HRESULT (STDMETHODCALLTYPE *get_StartIndex)(
        ISpellingError *This,
        ULONG *val);

    HRESULT (STDMETHODCALLTYPE *get_Length)(
        ISpellingError *This,
        ULONG *val);

    HRESULT (STDMETHODCALLTYPE *get_CorrectiveAction)(
        ISpellingError *This,
        CORRECTIVE_ACTION *val);

    HRESULT (STDMETHODCALLTYPE *get_Replacement)(
        ISpellingError *This,
        LPWSTR *val);

    END_INTERFACE
} ISpellingErrorVtbl;

interface ISpellingError {
    CONST_VTBL ISpellingErrorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISpellingError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISpellingError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISpellingError_Release(This) (This)->lpVtbl->Release(This)
/*** ISpellingError methods ***/
#define ISpellingError_get_StartIndex(This,val) (This)->lpVtbl->get_StartIndex(This,val)
#define ISpellingError_get_Length(This,val) (This)->lpVtbl->get_Length(This,val)
#define ISpellingError_get_CorrectiveAction(This,val) (This)->lpVtbl->get_CorrectiveAction(This,val)
#define ISpellingError_get_Replacement(This,val) (This)->lpVtbl->get_Replacement(This,val)
#else
/*** IUnknown methods ***/
static inline HRESULT ISpellingError_QueryInterface(ISpellingError* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISpellingError_AddRef(ISpellingError* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISpellingError_Release(ISpellingError* This) {
    return This->lpVtbl->Release(This);
}
/*** ISpellingError methods ***/
static inline HRESULT ISpellingError_get_StartIndex(ISpellingError* This,ULONG *val) {
    return This->lpVtbl->get_StartIndex(This,val);
}
static inline HRESULT ISpellingError_get_Length(ISpellingError* This,ULONG *val) {
    return This->lpVtbl->get_Length(This,val);
}
static inline HRESULT ISpellingError_get_CorrectiveAction(ISpellingError* This,CORRECTIVE_ACTION *val) {
    return This->lpVtbl->get_CorrectiveAction(This,val);
}
static inline HRESULT ISpellingError_get_Replacement(ISpellingError* This,LPWSTR *val) {
    return This->lpVtbl->get_Replacement(This,val);
}
#endif
#endif

#endif


#endif  /* __ISpellingError_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumSpellingError interface
 */
#ifndef __IEnumSpellingError_INTERFACE_DEFINED__
#define __IEnumSpellingError_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumSpellingError, 0x803e3bd4, 0x2828, 0x4410, 0x82,0x90, 0x41,0x8d,0x1d,0x73,0xc7,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("803e3bd4-**************-418d1d73c762")
IEnumSpellingError : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ISpellingError **val) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumSpellingError, 0x803e3bd4, 0x2828, 0x4410, 0x82,0x90, 0x41,0x8d,0x1d,0x73,0xc7,0x62)
#endif
#else
typedef struct IEnumSpellingErrorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumSpellingError *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumSpellingError *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumSpellingError *This);

    /*** IEnumSpellingError methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumSpellingError *This,
        ISpellingError **val);

    END_INTERFACE
} IEnumSpellingErrorVtbl;

interface IEnumSpellingError {
    CONST_VTBL IEnumSpellingErrorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumSpellingError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumSpellingError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumSpellingError_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumSpellingError methods ***/
#define IEnumSpellingError_Next(This,val) (This)->lpVtbl->Next(This,val)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumSpellingError_QueryInterface(IEnumSpellingError* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumSpellingError_AddRef(IEnumSpellingError* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumSpellingError_Release(IEnumSpellingError* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumSpellingError methods ***/
static inline HRESULT IEnumSpellingError_Next(IEnumSpellingError* This,ISpellingError **val) {
    return This->lpVtbl->Next(This,val);
}
#endif
#endif

#endif


#endif  /* __IEnumSpellingError_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOptionDescription interface
 */
#ifndef __IOptionDescription_INTERFACE_DEFINED__
#define __IOptionDescription_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOptionDescription, 0x432e5f85, 0x35cf, 0x4606, 0xa8,0x01, 0x6f,0x70,0x27,0x7e,0x1d,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("432e5f85-35cf-4606-a801-6f70277e1d7a")
IOptionDescription : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_Id(
        LPWSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Heading(
        LPWSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        LPWSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Labels(
        IEnumString **val) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOptionDescription, 0x432e5f85, 0x35cf, 0x4606, 0xa8,0x01, 0x6f,0x70,0x27,0x7e,0x1d,0x7a)
#endif
#else
typedef struct IOptionDescriptionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOptionDescription *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOptionDescription *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOptionDescription *This);

    /*** IOptionDescription methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        IOptionDescription *This,
        LPWSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_Heading)(
        IOptionDescription *This,
        LPWSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IOptionDescription *This,
        LPWSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_Labels)(
        IOptionDescription *This,
        IEnumString **val);

    END_INTERFACE
} IOptionDescriptionVtbl;

interface IOptionDescription {
    CONST_VTBL IOptionDescriptionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOptionDescription_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOptionDescription_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOptionDescription_Release(This) (This)->lpVtbl->Release(This)
/*** IOptionDescription methods ***/
#define IOptionDescription_get_Id(This,val) (This)->lpVtbl->get_Id(This,val)
#define IOptionDescription_get_Heading(This,val) (This)->lpVtbl->get_Heading(This,val)
#define IOptionDescription_get_Description(This,val) (This)->lpVtbl->get_Description(This,val)
#define IOptionDescription_get_Labels(This,val) (This)->lpVtbl->get_Labels(This,val)
#else
/*** IUnknown methods ***/
static inline HRESULT IOptionDescription_QueryInterface(IOptionDescription* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOptionDescription_AddRef(IOptionDescription* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOptionDescription_Release(IOptionDescription* This) {
    return This->lpVtbl->Release(This);
}
/*** IOptionDescription methods ***/
static inline HRESULT IOptionDescription_get_Id(IOptionDescription* This,LPWSTR *val) {
    return This->lpVtbl->get_Id(This,val);
}
static inline HRESULT IOptionDescription_get_Heading(IOptionDescription* This,LPWSTR *val) {
    return This->lpVtbl->get_Heading(This,val);
}
static inline HRESULT IOptionDescription_get_Description(IOptionDescription* This,LPWSTR *val) {
    return This->lpVtbl->get_Description(This,val);
}
static inline HRESULT IOptionDescription_get_Labels(IOptionDescription* This,IEnumString **val) {
    return This->lpVtbl->get_Labels(This,val);
}
#endif
#endif

#endif


#endif  /* __IOptionDescription_INTERFACE_DEFINED__ */

#ifndef __ISpellChecker_FWD_DEFINED__
#define __ISpellChecker_FWD_DEFINED__
typedef interface ISpellChecker ISpellChecker;
#ifdef __cplusplus
interface ISpellChecker;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ISpellCheckerChangedEventHandler interface
 */
#ifndef __ISpellCheckerChangedEventHandler_INTERFACE_DEFINED__
#define __ISpellCheckerChangedEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISpellCheckerChangedEventHandler, 0x0b83a5b0, 0x792f, 0x4eab, 0x97,0x99, 0xac,0xf5,0x2c,0x5e,0xd0,0x8a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0b83a5b0-792f-4eab-9799-acf52c5ed08a")
ISpellCheckerChangedEventHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Invoke(
        ISpellChecker *sender) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISpellCheckerChangedEventHandler, 0x0b83a5b0, 0x792f, 0x4eab, 0x97,0x99, 0xac,0xf5,0x2c,0x5e,0xd0,0x8a)
#endif
#else
typedef struct ISpellCheckerChangedEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISpellCheckerChangedEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISpellCheckerChangedEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISpellCheckerChangedEventHandler *This);

    /*** ISpellCheckerChangedEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISpellCheckerChangedEventHandler *This,
        ISpellChecker *sender);

    END_INTERFACE
} ISpellCheckerChangedEventHandlerVtbl;

interface ISpellCheckerChangedEventHandler {
    CONST_VTBL ISpellCheckerChangedEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISpellCheckerChangedEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISpellCheckerChangedEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISpellCheckerChangedEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** ISpellCheckerChangedEventHandler methods ***/
#define ISpellCheckerChangedEventHandler_Invoke(This,sender) (This)->lpVtbl->Invoke(This,sender)
#else
/*** IUnknown methods ***/
static inline HRESULT ISpellCheckerChangedEventHandler_QueryInterface(ISpellCheckerChangedEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISpellCheckerChangedEventHandler_AddRef(ISpellCheckerChangedEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISpellCheckerChangedEventHandler_Release(ISpellCheckerChangedEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** ISpellCheckerChangedEventHandler methods ***/
static inline HRESULT ISpellCheckerChangedEventHandler_Invoke(ISpellCheckerChangedEventHandler* This,ISpellChecker *sender) {
    return This->lpVtbl->Invoke(This,sender);
}
#endif
#endif

#endif


#endif  /* __ISpellCheckerChangedEventHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISpellChecker interface
 */
#ifndef __ISpellChecker_INTERFACE_DEFINED__
#define __ISpellChecker_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISpellChecker, 0xb6fd0b71, 0xe2bc, 0x4653, 0x8d,0x05, 0xf1,0x97,0xe4,0x12,0x77,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b6fd0b71-e2bc-4653-8d05-f197e412770b")
ISpellChecker : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_LanguageTag(
        LPWSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE Check(
        LPCWSTR text,
        IEnumSpellingError **val) = 0;

    virtual HRESULT STDMETHODCALLTYPE Suggest(
        LPCWSTR word,
        IEnumString **val) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        LPCWSTR word) = 0;

    virtual HRESULT STDMETHODCALLTYPE Ignore(
        LPCWSTR word) = 0;

    virtual HRESULT STDMETHODCALLTYPE AutoCorrect(
        LPCWSTR from,
        LPCWSTR to) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOptionValue(
        LPCWSTR option_id,
        BYTE *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_OptionIds(
        IEnumString **val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Id(
        LPWSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LocalizedName(
        LPWSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE add_SpellCheckerChanged(
        ISpellCheckerChangedEventHandler *handler,
        DWORD *event_cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE remove_SpellCheckerChanged(
        DWORD event_cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOptionDescription(
        LPCWSTR optionId,
        IOptionDescription **val) = 0;

    virtual HRESULT STDMETHODCALLTYPE ComprehensiveCheck(
        LPCWSTR text,
        IEnumSpellingError **val) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISpellChecker, 0xb6fd0b71, 0xe2bc, 0x4653, 0x8d,0x05, 0xf1,0x97,0xe4,0x12,0x77,0x0b)
#endif
#else
typedef struct ISpellCheckerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISpellChecker *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISpellChecker *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISpellChecker *This);

    /*** ISpellChecker methods ***/
    HRESULT (STDMETHODCALLTYPE *get_LanguageTag)(
        ISpellChecker *This,
        LPWSTR *val);

    HRESULT (STDMETHODCALLTYPE *Check)(
        ISpellChecker *This,
        LPCWSTR text,
        IEnumSpellingError **val);

    HRESULT (STDMETHODCALLTYPE *Suggest)(
        ISpellChecker *This,
        LPCWSTR word,
        IEnumString **val);

    HRESULT (STDMETHODCALLTYPE *Add)(
        ISpellChecker *This,
        LPCWSTR word);

    HRESULT (STDMETHODCALLTYPE *Ignore)(
        ISpellChecker *This,
        LPCWSTR word);

    HRESULT (STDMETHODCALLTYPE *AutoCorrect)(
        ISpellChecker *This,
        LPCWSTR from,
        LPCWSTR to);

    HRESULT (STDMETHODCALLTYPE *GetOptionValue)(
        ISpellChecker *This,
        LPCWSTR option_id,
        BYTE *val);

    HRESULT (STDMETHODCALLTYPE *get_OptionIds)(
        ISpellChecker *This,
        IEnumString **val);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        ISpellChecker *This,
        LPWSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_LocalizedName)(
        ISpellChecker *This,
        LPWSTR *val);

    HRESULT (STDMETHODCALLTYPE *add_SpellCheckerChanged)(
        ISpellChecker *This,
        ISpellCheckerChangedEventHandler *handler,
        DWORD *event_cookie);

    HRESULT (STDMETHODCALLTYPE *remove_SpellCheckerChanged)(
        ISpellChecker *This,
        DWORD event_cookie);

    HRESULT (STDMETHODCALLTYPE *GetOptionDescription)(
        ISpellChecker *This,
        LPCWSTR optionId,
        IOptionDescription **val);

    HRESULT (STDMETHODCALLTYPE *ComprehensiveCheck)(
        ISpellChecker *This,
        LPCWSTR text,
        IEnumSpellingError **val);

    END_INTERFACE
} ISpellCheckerVtbl;

interface ISpellChecker {
    CONST_VTBL ISpellCheckerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISpellChecker_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISpellChecker_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISpellChecker_Release(This) (This)->lpVtbl->Release(This)
/*** ISpellChecker methods ***/
#define ISpellChecker_get_LanguageTag(This,val) (This)->lpVtbl->get_LanguageTag(This,val)
#define ISpellChecker_Check(This,text,val) (This)->lpVtbl->Check(This,text,val)
#define ISpellChecker_Suggest(This,word,val) (This)->lpVtbl->Suggest(This,word,val)
#define ISpellChecker_Add(This,word) (This)->lpVtbl->Add(This,word)
#define ISpellChecker_Ignore(This,word) (This)->lpVtbl->Ignore(This,word)
#define ISpellChecker_AutoCorrect(This,from,to) (This)->lpVtbl->AutoCorrect(This,from,to)
#define ISpellChecker_GetOptionValue(This,option_id,val) (This)->lpVtbl->GetOptionValue(This,option_id,val)
#define ISpellChecker_get_OptionIds(This,val) (This)->lpVtbl->get_OptionIds(This,val)
#define ISpellChecker_get_Id(This,val) (This)->lpVtbl->get_Id(This,val)
#define ISpellChecker_get_LocalizedName(This,val) (This)->lpVtbl->get_LocalizedName(This,val)
#define ISpellChecker_add_SpellCheckerChanged(This,handler,event_cookie) (This)->lpVtbl->add_SpellCheckerChanged(This,handler,event_cookie)
#define ISpellChecker_remove_SpellCheckerChanged(This,event_cookie) (This)->lpVtbl->remove_SpellCheckerChanged(This,event_cookie)
#define ISpellChecker_GetOptionDescription(This,optionId,val) (This)->lpVtbl->GetOptionDescription(This,optionId,val)
#define ISpellChecker_ComprehensiveCheck(This,text,val) (This)->lpVtbl->ComprehensiveCheck(This,text,val)
#else
/*** IUnknown methods ***/
static inline HRESULT ISpellChecker_QueryInterface(ISpellChecker* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISpellChecker_AddRef(ISpellChecker* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISpellChecker_Release(ISpellChecker* This) {
    return This->lpVtbl->Release(This);
}
/*** ISpellChecker methods ***/
static inline HRESULT ISpellChecker_get_LanguageTag(ISpellChecker* This,LPWSTR *val) {
    return This->lpVtbl->get_LanguageTag(This,val);
}
static inline HRESULT ISpellChecker_Check(ISpellChecker* This,LPCWSTR text,IEnumSpellingError **val) {
    return This->lpVtbl->Check(This,text,val);
}
static inline HRESULT ISpellChecker_Suggest(ISpellChecker* This,LPCWSTR word,IEnumString **val) {
    return This->lpVtbl->Suggest(This,word,val);
}
static inline HRESULT ISpellChecker_Add(ISpellChecker* This,LPCWSTR word) {
    return This->lpVtbl->Add(This,word);
}
static inline HRESULT ISpellChecker_Ignore(ISpellChecker* This,LPCWSTR word) {
    return This->lpVtbl->Ignore(This,word);
}
static inline HRESULT ISpellChecker_AutoCorrect(ISpellChecker* This,LPCWSTR from,LPCWSTR to) {
    return This->lpVtbl->AutoCorrect(This,from,to);
}
static inline HRESULT ISpellChecker_GetOptionValue(ISpellChecker* This,LPCWSTR option_id,BYTE *val) {
    return This->lpVtbl->GetOptionValue(This,option_id,val);
}
static inline HRESULT ISpellChecker_get_OptionIds(ISpellChecker* This,IEnumString **val) {
    return This->lpVtbl->get_OptionIds(This,val);
}
static inline HRESULT ISpellChecker_get_Id(ISpellChecker* This,LPWSTR *val) {
    return This->lpVtbl->get_Id(This,val);
}
static inline HRESULT ISpellChecker_get_LocalizedName(ISpellChecker* This,LPWSTR *val) {
    return This->lpVtbl->get_LocalizedName(This,val);
}
static inline HRESULT ISpellChecker_add_SpellCheckerChanged(ISpellChecker* This,ISpellCheckerChangedEventHandler *handler,DWORD *event_cookie) {
    return This->lpVtbl->add_SpellCheckerChanged(This,handler,event_cookie);
}
static inline HRESULT ISpellChecker_remove_SpellCheckerChanged(ISpellChecker* This,DWORD event_cookie) {
    return This->lpVtbl->remove_SpellCheckerChanged(This,event_cookie);
}
static inline HRESULT ISpellChecker_GetOptionDescription(ISpellChecker* This,LPCWSTR optionId,IOptionDescription **val) {
    return This->lpVtbl->GetOptionDescription(This,optionId,val);
}
static inline HRESULT ISpellChecker_ComprehensiveCheck(ISpellChecker* This,LPCWSTR text,IEnumSpellingError **val) {
    return This->lpVtbl->ComprehensiveCheck(This,text,val);
}
#endif
#endif

#endif


#endif  /* __ISpellChecker_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISpellChecker2 interface
 */
#ifndef __ISpellChecker2_INTERFACE_DEFINED__
#define __ISpellChecker2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISpellChecker2, 0xe7ed1c71, 0x87f7, 0x4378, 0xa8,0x40, 0xc9,0x20,0x0d,0xac,0xee,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e7ed1c71-87f7-4378-a840-c9200dacee47")
ISpellChecker2 : public ISpellChecker
{
    virtual HRESULT STDMETHODCALLTYPE Remove(
        LPCWSTR word) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISpellChecker2, 0xe7ed1c71, 0x87f7, 0x4378, 0xa8,0x40, 0xc9,0x20,0x0d,0xac,0xee,0x47)
#endif
#else
typedef struct ISpellChecker2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISpellChecker2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISpellChecker2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISpellChecker2 *This);

    /*** ISpellChecker methods ***/
    HRESULT (STDMETHODCALLTYPE *get_LanguageTag)(
        ISpellChecker2 *This,
        LPWSTR *val);

    HRESULT (STDMETHODCALLTYPE *Check)(
        ISpellChecker2 *This,
        LPCWSTR text,
        IEnumSpellingError **val);

    HRESULT (STDMETHODCALLTYPE *Suggest)(
        ISpellChecker2 *This,
        LPCWSTR word,
        IEnumString **val);

    HRESULT (STDMETHODCALLTYPE *Add)(
        ISpellChecker2 *This,
        LPCWSTR word);

    HRESULT (STDMETHODCALLTYPE *Ignore)(
        ISpellChecker2 *This,
        LPCWSTR word);

    HRESULT (STDMETHODCALLTYPE *AutoCorrect)(
        ISpellChecker2 *This,
        LPCWSTR from,
        LPCWSTR to);

    HRESULT (STDMETHODCALLTYPE *GetOptionValue)(
        ISpellChecker2 *This,
        LPCWSTR option_id,
        BYTE *val);

    HRESULT (STDMETHODCALLTYPE *get_OptionIds)(
        ISpellChecker2 *This,
        IEnumString **val);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        ISpellChecker2 *This,
        LPWSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_LocalizedName)(
        ISpellChecker2 *This,
        LPWSTR *val);

    HRESULT (STDMETHODCALLTYPE *add_SpellCheckerChanged)(
        ISpellChecker2 *This,
        ISpellCheckerChangedEventHandler *handler,
        DWORD *event_cookie);

    HRESULT (STDMETHODCALLTYPE *remove_SpellCheckerChanged)(
        ISpellChecker2 *This,
        DWORD event_cookie);

    HRESULT (STDMETHODCALLTYPE *GetOptionDescription)(
        ISpellChecker2 *This,
        LPCWSTR optionId,
        IOptionDescription **val);

    HRESULT (STDMETHODCALLTYPE *ComprehensiveCheck)(
        ISpellChecker2 *This,
        LPCWSTR text,
        IEnumSpellingError **val);

    /*** ISpellChecker2 methods ***/
    HRESULT (STDMETHODCALLTYPE *Remove)(
        ISpellChecker2 *This,
        LPCWSTR word);

    END_INTERFACE
} ISpellChecker2Vtbl;

interface ISpellChecker2 {
    CONST_VTBL ISpellChecker2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISpellChecker2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISpellChecker2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISpellChecker2_Release(This) (This)->lpVtbl->Release(This)
/*** ISpellChecker methods ***/
#define ISpellChecker2_get_LanguageTag(This,val) (This)->lpVtbl->get_LanguageTag(This,val)
#define ISpellChecker2_Check(This,text,val) (This)->lpVtbl->Check(This,text,val)
#define ISpellChecker2_Suggest(This,word,val) (This)->lpVtbl->Suggest(This,word,val)
#define ISpellChecker2_Add(This,word) (This)->lpVtbl->Add(This,word)
#define ISpellChecker2_Ignore(This,word) (This)->lpVtbl->Ignore(This,word)
#define ISpellChecker2_AutoCorrect(This,from,to) (This)->lpVtbl->AutoCorrect(This,from,to)
#define ISpellChecker2_GetOptionValue(This,option_id,val) (This)->lpVtbl->GetOptionValue(This,option_id,val)
#define ISpellChecker2_get_OptionIds(This,val) (This)->lpVtbl->get_OptionIds(This,val)
#define ISpellChecker2_get_Id(This,val) (This)->lpVtbl->get_Id(This,val)
#define ISpellChecker2_get_LocalizedName(This,val) (This)->lpVtbl->get_LocalizedName(This,val)
#define ISpellChecker2_add_SpellCheckerChanged(This,handler,event_cookie) (This)->lpVtbl->add_SpellCheckerChanged(This,handler,event_cookie)
#define ISpellChecker2_remove_SpellCheckerChanged(This,event_cookie) (This)->lpVtbl->remove_SpellCheckerChanged(This,event_cookie)
#define ISpellChecker2_GetOptionDescription(This,optionId,val) (This)->lpVtbl->GetOptionDescription(This,optionId,val)
#define ISpellChecker2_ComprehensiveCheck(This,text,val) (This)->lpVtbl->ComprehensiveCheck(This,text,val)
/*** ISpellChecker2 methods ***/
#define ISpellChecker2_Remove(This,word) (This)->lpVtbl->Remove(This,word)
#else
/*** IUnknown methods ***/
static inline HRESULT ISpellChecker2_QueryInterface(ISpellChecker2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISpellChecker2_AddRef(ISpellChecker2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISpellChecker2_Release(ISpellChecker2* This) {
    return This->lpVtbl->Release(This);
}
/*** ISpellChecker methods ***/
static inline HRESULT ISpellChecker2_get_LanguageTag(ISpellChecker2* This,LPWSTR *val) {
    return This->lpVtbl->get_LanguageTag(This,val);
}
static inline HRESULT ISpellChecker2_Check(ISpellChecker2* This,LPCWSTR text,IEnumSpellingError **val) {
    return This->lpVtbl->Check(This,text,val);
}
static inline HRESULT ISpellChecker2_Suggest(ISpellChecker2* This,LPCWSTR word,IEnumString **val) {
    return This->lpVtbl->Suggest(This,word,val);
}
static inline HRESULT ISpellChecker2_Add(ISpellChecker2* This,LPCWSTR word) {
    return This->lpVtbl->Add(This,word);
}
static inline HRESULT ISpellChecker2_Ignore(ISpellChecker2* This,LPCWSTR word) {
    return This->lpVtbl->Ignore(This,word);
}
static inline HRESULT ISpellChecker2_AutoCorrect(ISpellChecker2* This,LPCWSTR from,LPCWSTR to) {
    return This->lpVtbl->AutoCorrect(This,from,to);
}
static inline HRESULT ISpellChecker2_GetOptionValue(ISpellChecker2* This,LPCWSTR option_id,BYTE *val) {
    return This->lpVtbl->GetOptionValue(This,option_id,val);
}
static inline HRESULT ISpellChecker2_get_OptionIds(ISpellChecker2* This,IEnumString **val) {
    return This->lpVtbl->get_OptionIds(This,val);
}
static inline HRESULT ISpellChecker2_get_Id(ISpellChecker2* This,LPWSTR *val) {
    return This->lpVtbl->get_Id(This,val);
}
static inline HRESULT ISpellChecker2_get_LocalizedName(ISpellChecker2* This,LPWSTR *val) {
    return This->lpVtbl->get_LocalizedName(This,val);
}
static inline HRESULT ISpellChecker2_add_SpellCheckerChanged(ISpellChecker2* This,ISpellCheckerChangedEventHandler *handler,DWORD *event_cookie) {
    return This->lpVtbl->add_SpellCheckerChanged(This,handler,event_cookie);
}
static inline HRESULT ISpellChecker2_remove_SpellCheckerChanged(ISpellChecker2* This,DWORD event_cookie) {
    return This->lpVtbl->remove_SpellCheckerChanged(This,event_cookie);
}
static inline HRESULT ISpellChecker2_GetOptionDescription(ISpellChecker2* This,LPCWSTR optionId,IOptionDescription **val) {
    return This->lpVtbl->GetOptionDescription(This,optionId,val);
}
static inline HRESULT ISpellChecker2_ComprehensiveCheck(ISpellChecker2* This,LPCWSTR text,IEnumSpellingError **val) {
    return This->lpVtbl->ComprehensiveCheck(This,text,val);
}
/*** ISpellChecker2 methods ***/
static inline HRESULT ISpellChecker2_Remove(ISpellChecker2* This,LPCWSTR word) {
    return This->lpVtbl->Remove(This,word);
}
#endif
#endif

#endif


#endif  /* __ISpellChecker2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISpellCheckerFactory interface
 */
#ifndef __ISpellCheckerFactory_INTERFACE_DEFINED__
#define __ISpellCheckerFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISpellCheckerFactory, 0x8e018a9d, 0x2415, 0x4677, 0xbf,0x08, 0x79,0x4e,0xa6,0x1f,0x94,0xbb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8e018a9d-2415-4677-bf08-794ea61f94bb")
ISpellCheckerFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_SupportedLanguages(
        IEnumString **val) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSupported(
        LPCWSTR languageTag,
        WINBOOL *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSpellChecker(
        LPCWSTR languageTag,
        ISpellChecker **val) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISpellCheckerFactory, 0x8e018a9d, 0x2415, 0x4677, 0xbf,0x08, 0x79,0x4e,0xa6,0x1f,0x94,0xbb)
#endif
#else
typedef struct ISpellCheckerFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISpellCheckerFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISpellCheckerFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISpellCheckerFactory *This);

    /*** ISpellCheckerFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SupportedLanguages)(
        ISpellCheckerFactory *This,
        IEnumString **val);

    HRESULT (STDMETHODCALLTYPE *IsSupported)(
        ISpellCheckerFactory *This,
        LPCWSTR languageTag,
        WINBOOL *val);

    HRESULT (STDMETHODCALLTYPE *CreateSpellChecker)(
        ISpellCheckerFactory *This,
        LPCWSTR languageTag,
        ISpellChecker **val);

    END_INTERFACE
} ISpellCheckerFactoryVtbl;

interface ISpellCheckerFactory {
    CONST_VTBL ISpellCheckerFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISpellCheckerFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISpellCheckerFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISpellCheckerFactory_Release(This) (This)->lpVtbl->Release(This)
/*** ISpellCheckerFactory methods ***/
#define ISpellCheckerFactory_get_SupportedLanguages(This,val) (This)->lpVtbl->get_SupportedLanguages(This,val)
#define ISpellCheckerFactory_IsSupported(This,languageTag,val) (This)->lpVtbl->IsSupported(This,languageTag,val)
#define ISpellCheckerFactory_CreateSpellChecker(This,languageTag,val) (This)->lpVtbl->CreateSpellChecker(This,languageTag,val)
#else
/*** IUnknown methods ***/
static inline HRESULT ISpellCheckerFactory_QueryInterface(ISpellCheckerFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISpellCheckerFactory_AddRef(ISpellCheckerFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISpellCheckerFactory_Release(ISpellCheckerFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** ISpellCheckerFactory methods ***/
static inline HRESULT ISpellCheckerFactory_get_SupportedLanguages(ISpellCheckerFactory* This,IEnumString **val) {
    return This->lpVtbl->get_SupportedLanguages(This,val);
}
static inline HRESULT ISpellCheckerFactory_IsSupported(ISpellCheckerFactory* This,LPCWSTR languageTag,WINBOOL *val) {
    return This->lpVtbl->IsSupported(This,languageTag,val);
}
static inline HRESULT ISpellCheckerFactory_CreateSpellChecker(ISpellCheckerFactory* This,LPCWSTR languageTag,ISpellChecker **val) {
    return This->lpVtbl->CreateSpellChecker(This,languageTag,val);
}
#endif
#endif

#endif


#endif  /* __ISpellCheckerFactory_INTERFACE_DEFINED__ */

#endif /* WINAPI_PARTITION_APP */
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IUserDictionariesRegistrar interface
 */
#ifndef __IUserDictionariesRegistrar_INTERFACE_DEFINED__
#define __IUserDictionariesRegistrar_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUserDictionariesRegistrar, 0xaa176b85, 0x0e12, 0x4844, 0x8e,0x1a, 0xee,0xf1,0xda,0x77,0xf5,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa176b85-0e12-4844-8e1a-eef1da77f586")
IUserDictionariesRegistrar : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RegisterUserDictionary(
        LPCWSTR dictionaryPath,
        LPCWSTR languageTag) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterUserDictionary(
        LPCWSTR dictionaryPath,
        LPCWSTR languageTag) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUserDictionariesRegistrar, 0xaa176b85, 0x0e12, 0x4844, 0x8e,0x1a, 0xee,0xf1,0xda,0x77,0xf5,0x86)
#endif
#else
typedef struct IUserDictionariesRegistrarVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUserDictionariesRegistrar *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUserDictionariesRegistrar *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUserDictionariesRegistrar *This);

    /*** IUserDictionariesRegistrar methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterUserDictionary)(
        IUserDictionariesRegistrar *This,
        LPCWSTR dictionaryPath,
        LPCWSTR languageTag);

    HRESULT (STDMETHODCALLTYPE *UnregisterUserDictionary)(
        IUserDictionariesRegistrar *This,
        LPCWSTR dictionaryPath,
        LPCWSTR languageTag);

    END_INTERFACE
} IUserDictionariesRegistrarVtbl;

interface IUserDictionariesRegistrar {
    CONST_VTBL IUserDictionariesRegistrarVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUserDictionariesRegistrar_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUserDictionariesRegistrar_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUserDictionariesRegistrar_Release(This) (This)->lpVtbl->Release(This)
/*** IUserDictionariesRegistrar methods ***/
#define IUserDictionariesRegistrar_RegisterUserDictionary(This,dictionaryPath,languageTag) (This)->lpVtbl->RegisterUserDictionary(This,dictionaryPath,languageTag)
#define IUserDictionariesRegistrar_UnregisterUserDictionary(This,dictionaryPath,languageTag) (This)->lpVtbl->UnregisterUserDictionary(This,dictionaryPath,languageTag)
#else
/*** IUnknown methods ***/
static inline HRESULT IUserDictionariesRegistrar_QueryInterface(IUserDictionariesRegistrar* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUserDictionariesRegistrar_AddRef(IUserDictionariesRegistrar* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUserDictionariesRegistrar_Release(IUserDictionariesRegistrar* This) {
    return This->lpVtbl->Release(This);
}
/*** IUserDictionariesRegistrar methods ***/
static inline HRESULT IUserDictionariesRegistrar_RegisterUserDictionary(IUserDictionariesRegistrar* This,LPCWSTR dictionaryPath,LPCWSTR languageTag) {
    return This->lpVtbl->RegisterUserDictionary(This,dictionaryPath,languageTag);
}
static inline HRESULT IUserDictionariesRegistrar_UnregisterUserDictionary(IUserDictionariesRegistrar* This,LPCWSTR dictionaryPath,LPCWSTR languageTag) {
    return This->lpVtbl->UnregisterUserDictionary(This,dictionaryPath,languageTag);
}
#endif
#endif

#endif


#endif  /* __IUserDictionariesRegistrar_INTERFACE_DEFINED__ */

#endif /* WINAPI_PARTITION_DESKTOP */
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#ifndef __MsSpellCheckLib_LIBRARY_DEFINED__
#define __MsSpellCheckLib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_MsSpellCheckLib, 0x4a250e01, 0x61ea, 0x400b, 0xa2,0x7d, 0xbf,0x37,0x44,0xbc,0xc9,0xf5);

/*****************************************************************************
 * SpellCheckerFactory coclass
 */

DEFINE_GUID(CLSID_SpellCheckerFactory, 0x7ab36653, 0x1796, 0x484b, 0xbd,0xfa, 0xe7,0x4f,0x1d,0xb7,0xc1,0xdc);

#ifdef __cplusplus
class DECLSPEC_UUID("7ab36653-1796-484b-bdfa-e74f1db7c1dc") SpellCheckerFactory;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SpellCheckerFactory, 0x7ab36653, 0x1796, 0x484b, 0xbd,0xfa, 0xe7,0x4f,0x1d,0xb7,0xc1,0xdc)
#endif
#endif

#endif /* __MsSpellCheckLib_LIBRARY_DEFINED__ */
#endif /* WINAPI_PARTITION_APP */
#endif /* MIN_SPELLING_NTDDI */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __spellcheck_h__ */
