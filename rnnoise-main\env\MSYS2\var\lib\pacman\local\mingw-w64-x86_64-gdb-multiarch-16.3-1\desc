%NAME%
mingw-w64-x86_64-gdb-multiarch

%VERSION%
16.3-1

%BASE%
mingw-w64-gdb

%DESC%
GNU Debugger (supports all targets)

%URL%
https://www.gnu.org/software/gdb/

%ARCH%
any

%BUILDDATE%
**********

%INSTALLDATE%
**********

%PACKAGER%
CI (msys2/msys2-autobuild/e8d10d7e/14628098762)

%SIZE%
31095671

%GROUPS%
mingw-w64-x86_64-toolchain

%LICENSE%
spdx:GPL-3.0-or-later

%VALIDATION%
sha256
pgp

%REPLACES%
mingw-w64-x86_64-arm-none-eabi-gdb
mingw-w64-x86_64-avr-gdb

%DEPENDS%
mingw-w64-x86_64-gdb=16.3

%OPTDEPENDS%
mingw-w64-x86_64-python-pygments: for syntax highlighting

%CONFLICTS%
mingw-w64-x86_64-arm-none-eabi-gdb
mingw-w64-x86_64-avr-gdb

%PROVIDES%
mingw-w64-x86_64-arm-none-eabi-gdb=16.3
mingw-w64-x86_64-avr-gdb=16.3

%XDATA%
pkgtype=split

