/*** Autogenerated by WIDL 10.12 from include/wincodecsdk.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wincodecsdk_h__
#define __wincodecsdk_h__

/* Forward declarations */

#ifndef __IWICMetadataHandlerInfo_FWD_DEFINED__
#define __IWICMetadataHandlerInfo_FWD_DEFINED__
typedef interface IWICMetadataHandlerInfo IWICMetadataHandlerInfo;
#ifdef __cplusplus
interface IWICMetadataHandlerInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWICMetadataReader_FWD_DEFINED__
#define __IWICMetadataReader_FWD_DEFINED__
typedef interface IWICMetadataReader IWICMetadataReader;
#ifdef __cplusplus
interface IWICMetadataReader;
#endif /* __cplusplus */
#endif

#ifndef __IWICMetadataReaderInfo_FWD_DEFINED__
#define __IWICMetadataReaderInfo_FWD_DEFINED__
typedef interface IWICMetadataReaderInfo IWICMetadataReaderInfo;
#ifdef __cplusplus
interface IWICMetadataReaderInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWICMetadataWriter_FWD_DEFINED__
#define __IWICMetadataWriter_FWD_DEFINED__
typedef interface IWICMetadataWriter IWICMetadataWriter;
#ifdef __cplusplus
interface IWICMetadataWriter;
#endif /* __cplusplus */
#endif

#ifndef __IWICMetadataWriterInfo_FWD_DEFINED__
#define __IWICMetadataWriterInfo_FWD_DEFINED__
typedef interface IWICMetadataWriterInfo IWICMetadataWriterInfo;
#ifdef __cplusplus
interface IWICMetadataWriterInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWICMetadataBlockReader_FWD_DEFINED__
#define __IWICMetadataBlockReader_FWD_DEFINED__
typedef interface IWICMetadataBlockReader IWICMetadataBlockReader;
#ifdef __cplusplus
interface IWICMetadataBlockReader;
#endif /* __cplusplus */
#endif

#ifndef __IWICMetadataBlockWriter_FWD_DEFINED__
#define __IWICMetadataBlockWriter_FWD_DEFINED__
typedef interface IWICMetadataBlockWriter IWICMetadataBlockWriter;
#ifdef __cplusplus
interface IWICMetadataBlockWriter;
#endif /* __cplusplus */
#endif

#ifndef __IWICPersistStream_FWD_DEFINED__
#define __IWICPersistStream_FWD_DEFINED__
typedef interface IWICPersistStream IWICPersistStream;
#ifdef __cplusplus
interface IWICPersistStream;
#endif /* __cplusplus */
#endif

#ifndef __IWICStreamProvider_FWD_DEFINED__
#define __IWICStreamProvider_FWD_DEFINED__
typedef interface IWICStreamProvider IWICStreamProvider;
#ifdef __cplusplus
interface IWICStreamProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWICComponentFactory_FWD_DEFINED__
#define __IWICComponentFactory_FWD_DEFINED__
typedef interface IWICComponentFactory IWICComponentFactory;
#ifdef __cplusplus
interface IWICComponentFactory;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <wtypes.h>
#include <wincodec.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum WICPersistOptions {
    WICPersistOptionDefault = 0x0,
    WICPersistOptionLittleEndian = 0x0,
    WICPersistOptionBigEndian = 0x1,
    WICPersistOptionStrictFormat = 0x2,
    WICPersistOptionNoCacheStream = 0x4,
    WICPersistOptionPreferUTF8 = 0x8,
    WICPersistOptionMask = 0xffff
} WICPersistOptions;
typedef enum WICMetadataCreationOptions {
    WICMetadataCreationDefault = 0x0,
    WICMetadataCreationAllowUnknown = WICMetadataCreationDefault,
    WICMetadataCreationFailUnknown = 0x10000,
    WICMetadataCreationMask = 0xffff0000
} WICMetadataCreationOptions;
DEFINE_GUID(GUID_MetadataFormatUnknown, 0xa45e592f,0x9078,0x4a7c,0xad,0xb5,0x4e,0xdc,0x4f,0xd6,0x1b,0x1f);
DEFINE_GUID(GUID_MetadataFormatChunkcHRM, 0x9db3655b,0x2842,0x44b3,0x80,0x67,0x12,0xe9,0xb3,0x75,0x55,0x6a);
DEFINE_GUID(GUID_MetadataFormatChunkgAMA, 0xf00935a5,0x1d5d,0x4cd1,0x81,0xb2,0x93,0x24,0xd7,0xec,0xa7,0x81);
DEFINE_GUID(GUID_MetadataFormatChunktEXt, 0x568d8936,0xc0a9,0x4923,0x90,0x5d,0xdf,0x2b,0x38,0x23,0x8f,0xbc);
DEFINE_GUID(GUID_MetadataFormatChunktIME, 0x6b00ae2d,0xe24b,0x460a,0x98,0xb6,0x87,0x8b,0xd0,0x30,0x72,0xfd);
DEFINE_GUID(GUID_MetadataFormatIfd, 0x537396c6,0x2d8a,0x4bb6,0x9b,0xf8,0x2f,0x0a,0x8e,0x2a,0x3a,0xdf);
DEFINE_GUID(GUID_MetadataFormatExif, 0x1c3c4f9d,0xb84a,0x467d,0x94,0x93,0x36,0xcf,0xbd,0x59,0xea,0x57);
DEFINE_GUID(GUID_MetadataFormatXMP, 0xbb5acc38,0xf216,0x4cec,0xa6,0xc5,0x5f,0x6e,0x73,0x97,0x63,0xa9);
DEFINE_GUID(GUID_MetadataFormatXMPStruct, 0x22383cf1,0xed17,0x4e2e,0xaf,0x17,0xd8,0x5b,0x8f,0x6b,0x30,0xd0);
DEFINE_GUID(GUID_MetadataFormatIMD, 0xbd2bb086,0x4d52,0x48dd,0x96,0x77,0xdb,0x48,0x3e,0x85,0xae,0x8f);
DEFINE_GUID(GUID_MetadataFormatLSD, 0xe256031e,0x6299,0x4929,0xb9,0x8d,0x5a,0xc8,0x84,0xaf,0xba,0x92);
DEFINE_GUID(GUID_MetadataFormatGCE, 0x2a25cad8,0xdeeb,0x4c69,0xa7,0x88,0x0e,0xc2,0x26,0x6d,0xca,0xfd);
DEFINE_GUID(GUID_MetadataFormatAPE, 0x2e043dc2,0xc967,0x4e05,0x87,0x5e,0x61,0x8b,0xf6,0x7e,0x85,0xc3);
DEFINE_GUID(GUID_MetadataFormatGifComment, 0xc4b6e0e0,0xcfb4,0x4ad3,0xab,0x33,0x9a,0xad,0x23,0x55,0xa3,0x4a);
DEFINE_GUID(GUID_MetadataFormatSubIfd, 0x58a2e128,0x2db9,0x4e57,0xbb,0x14,0x51,0x77,0x89,0x1e,0xd3,0x31);
DEFINE_GUID(GUID_MetadataFormatGps, 0x7134ab8a,0x9351,0x44ad,0xaf,0x62,0x44,0x8d,0xb6,0xb5,0x02,0xec);
DEFINE_GUID(GUID_MetadataFormatInterop, 0xed686f8e,0x681f,0x4c8b,0xbd,0x41,0xa8,0xad,0xdb,0xf6,0xb3,0xfc);
DEFINE_GUID(GUID_MetadataFormatApp0, 0x79007028,0x268d,0x45d6,0xa3,0xc2,0x35,0x4e,0x6a,0x50,0x4b,0xc9);
DEFINE_GUID(GUID_MetadataFormatApp1, 0x8fd3dfc3,0xf951,0x492b,0x81,0x7f,0x69,0xc2,0xe6,0xd9,0xa5,0xb0);
DEFINE_GUID(GUID_MetadataFormatApp13, 0x326556a2,0xf502,0x4354,0x9c,0xc0,0x8e,0x3f,0x48,0xea,0xf6,0xb5);
DEFINE_GUID(GUID_MetadataFormatIPTC, 0x4fab0914,0xe129,0x4087,0xa1,0xd1,0xbc,0x81,0x2d,0x45,0xa7,0xb5);
DEFINE_GUID(GUID_MetadataFormatIRB, 0x16100d66,0x8570,0x4bb9,0xb9,0x2d,0xfd,0xa4,0xb2,0x3e,0xce,0x67);
DEFINE_GUID(GUID_MetadataFormat8BIMIPTC, 0x0010568c,0x0852,0x4e6a,0xb1,0x91,0x5c,0x33,0xac,0x5b,0x04,0x30);
DEFINE_GUID(GUID_MetadataFormat8BIMResolutionInfo, 0x739f305d,0x81db,0x43cb,0xac,0x5e,0x55,0x01,0x3e,0xf9,0xf0,0x03);
DEFINE_GUID(GUID_MetadataFormat8BIMIPTCDigest, 0x1ca32285,0x9ccd,0x4786,0x8b,0xd8,0x79,0x53,0x9d,0xb6,0xa0,0x06);
DEFINE_GUID(GUID_MetadataFormatThumbnail, 0x243dcee9,0x8703,0x40ee,0x8e,0xf0,0x22,0xa6,0x0,0xb8,0x5,0x8c);
DEFINE_GUID(GUID_MetadataFormatXMPBag, 0x833cca5f,0xdcb7,0x4516,0x80,0x6f,0x65,0x96,0xab,0x26,0xdc,0xe4);
DEFINE_GUID(GUID_MetadataFormatXMPSeq, 0x63e8df02,0xeb6c,0x456c,0xa2,0x24,0xb2,0x5e,0x79,0x4f,0xd6,0x48);
DEFINE_GUID(GUID_MetadataFormatXMPAlt, 0x7b08a675,0x91aa,0x481b,0xa7,0x98,0x4d,0xa9,0x49,0x08,0x61,0x3b);
DEFINE_GUID(GUID_MetadataFormatJpegChrominance, 0xf73d0dcf,0xcec6,0x4f85,0x9b,0x0e,0x1c,0x39,0x56,0xb1,0xbe,0xf7);
DEFINE_GUID(GUID_MetadataFormatJpegLuminance, 0x86908007,0xedfc,0x4860,0x8d,0x4b,0x4e,0xe6,0xe8,0x3e,0x60,0x58);
DEFINE_GUID(GUID_MetadataFormatJpegComment, 0x220e5f33,0xafd3,0x474e,0x9d,0x31,0x7d,0x4f,0xe7,0x30,0xf5,0x57);
DEFINE_GUID(GUID_MetadataFormatChunkbKGD, 0xe14d3571,0x6b47,0x4dea,0xb6,0xa,0x87,0xce,0xa,0x78,0xdf,0xb7);
DEFINE_GUID(GUID_MetadataFormatChunkiTXt, 0xc2bec729,0xb68,0x4b77,0xaa,0xe,0x62,0x95,0xa6,0xac,0x18,0x14);
DEFINE_GUID(GUID_MetadataFormatChunkhIST, 0xc59a82da,0xdb74,0x48a4,0xbd,0x6a,0xb6,0x9c,0x49,0x31,0xef,0x95);
DEFINE_GUID(GUID_MetadataFormatChunkiCCP, 0xeb4349ab,0xb685,0x450f,0x91,0xb5,0xe8,0x2,0xe8,0x92,0x53,0x6c);
DEFINE_GUID(GUID_MetadataFormatChunksRGB, 0xc115fd36,0xcc6f,0x4e3f,0x83,0x63,0x52,0x4b,0x87,0xc6,0xb0,0xd9);
DEFINE_GUID(GUID_MetadataFormatDds, 0x4a064603,0x8c33,0x4e60,0x9c,0x29,0x13,0x62,0x31,0x70,0x2d,0x08);
DEFINE_GUID(GUID_MetadataFormatHeif, 0x817ef3e1,0x1288,0x45f4,0xa8,0x52,0x26,0x0d,0x9e,0x7c,0xce,0x83);
DEFINE_GUID(GUID_MetadataFormatHeifHDR, 0x568b8d8a,0x1e65,0x438c,0x89,0x68,0xd6,0x0e,0x10,0x12,0xbe,0xb9);
DEFINE_GUID(GUID_MetadataFormatWebpANIM, 0x6dc4fda6,0x78e6,0x4102,0xae,0x35,0xbc,0xfa,0x1e,0xdc,0xc7,0x8b);
DEFINE_GUID(GUID_MetadataFormatWebpANMF, 0x43c105ee,0xb93b,0x4abb,0xb0,0x03,0xa0,0x8c,0x0d,0x87,0x04,0x71);
DEFINE_GUID(CLSID_WICUnknownMetadataReader, 0x699745c2,0x5066,0x4b82,0xa8,0xe3,0xd4,0x04,0x78,0xdb,0xec,0x8c);
DEFINE_GUID(CLSID_WICUnknownMetadataWriter, 0xa09cca86,0x27ba,0x4f39,0x90,0x53,0x12,0x1f,0xa4,0xdc,0x08,0xfc);
DEFINE_GUID(CLSID_WICPngBkgdMetadataReader, 0x0ce7a4a6,0x03e8,0x4a60,0x9d,0x15,0x28,0x2e,0xf3,0x2e,0xe7,0xda);
DEFINE_GUID(CLSID_WICPngBkgdMetadataWriter, 0x68e3f2fd,0x31ae,0x4441,0xbb,0x6a,0xfd,0x70,0x47,0x52,0x5f,0x90);
DEFINE_GUID(CLSID_WICPngChrmMetadataReader, 0xf90b5f36,0x367b,0x402a,0x9d,0xd1,0xbc,0x0f,0xd5,0x9d,0x8f,0x62);
DEFINE_GUID(CLSID_WICPngChrmMetadataWriter, 0xe23ce3eb,0x5608,0x4e83,0xbc,0xef,0x27,0xb1,0x98,0x7e,0x51,0xd7);
DEFINE_GUID(CLSID_WICPngGamaMetadataReader, 0x3692ca39,0xe082,0x4350,0x9e,0x1f,0x37,0x04,0xcb,0x08,0x3c,0xd5);
DEFINE_GUID(CLSID_WICPngGamaMetadataWriter, 0xff036d13,0x5d4b,0x46dd,0xb1,0x0f,0x10,0x66,0x93,0xd9,0xfe,0x4f);
DEFINE_GUID(CLSID_WICPngHistMetadataReader, 0x877a0bb7,0xa313,0x4491,0x87,0xb5,0x2e,0x6d,0x05,0x94,0xf5,0x20);
DEFINE_GUID(CLSID_WICPngHistMetadataWriter, 0x8a03e749,0x672e,0x446e,0xbf,0x1f,0x2c,0x11,0xd2,0x33,0xb6,0xff);
DEFINE_GUID(CLSID_WICPngIccpMetadataReader, 0xf5d3e63b,0xcb0f,0x4628,0xa4,0x78,0x6d,0x82,0x44,0xbe,0x36,0xb1);
DEFINE_GUID(CLSID_WICPngIccpMetadataWriter, 0x16671e5f,0x0ce6,0x4cc4,0x97,0x68,0xe8,0x9f,0xe5,0x01,0x8a,0xde);
DEFINE_GUID(CLSID_WICPngItxtMetadataReader, 0xaabfb2fa,0x3e1e,0x4a8f,0x89,0x77,0x55,0x56,0xfb,0x94,0xea,0x23);
DEFINE_GUID(CLSID_WICPngItxtMetadataWriter, 0x31879719,0xe751,0x4df8,0x98,0x1d,0x68,0xdf,0xf6,0x77,0x04,0xed);
DEFINE_GUID(CLSID_WICPngSrgbMetadataReader, 0xfb40360c,0x547e,0x4956,0xa3,0xb9,0xd4,0x41,0x88,0x59,0xba,0x66);
DEFINE_GUID(CLSID_WICPngSrgbMetadataWriter, 0xa6ee35c6,0x87ec,0x47df,0x9f,0x22,0x1d,0x5a,0xad,0x84,0x0c,0x82);
DEFINE_GUID(CLSID_WICPngTextMetadataReader, 0x4b59afcc,0xb8c3,0x408a,0xb6,0x70,0x89,0xe5,0xfa,0xb6,0xfd,0xa7);
DEFINE_GUID(CLSID_WICPngTextMetadataWriter, 0xb5ebafb9,0x253e,0x4a72,0xa7,0x44,0x07,0x62,0xd2,0x68,0x56,0x83);
DEFINE_GUID(CLSID_WICPngTimeMetadataReader, 0xd94edf02,0xefe5,0x4f0d,0x85,0xc8,0xf5,0xa6,0x8b,0x30,0x00,0xb1);
DEFINE_GUID(CLSID_WICPngTimeMetadataWriter, 0x1ab78400,0xb5a3,0x4d91,0x8a,0xce,0x33,0xfc,0xd1,0x49,0x9b,0xe6);
DEFINE_GUID(CLSID_WICIfdMetadataReader, 0x8f914656,0x9d0a,0x4eb2,0x90,0x19,0x0b,0xf9,0x6d,0x8a,0x9e,0xe6);
DEFINE_GUID(CLSID_WICIfdMetadataWriter, 0xb1ebfc28,0xc9bd,0x47a2,0x8d,0x33,0xb9,0x48,0x76,0x97,0x77,0xa7);
DEFINE_GUID(CLSID_WICExifMetadataReader, 0xd9403860,0x297f,0x4a49,0xbf,0x9b,0x77,0x89,0x81,0x50,0xa4,0x42);
DEFINE_GUID(CLSID_WICExifMetadataWriter, 0xc9a14cda,0xc339,0x460b,0x90,0x78,0xd4,0xde,0xbc,0xfa,0xbe,0x91);
DEFINE_GUID(CLSID_WICXMPMetadataReader, 0x72b624df,0xae11,0x4948,0xa6,0x5c,0x35,0x1e,0xb0,0x82,0x94,0x19);
DEFINE_GUID(CLSID_WICXMPMetadataWriter, 0x1765e14e,0x1bd4,0x462e,0xb6,0xb1,0x59,0x0b,0xf1,0x26,0x2a,0xc6);
DEFINE_GUID(CLSID_WICXMPAltMetadataReader, 0xaa94dcc2,0xb8b0,0x4898,0xb8,0x35,0x00,0x0a,0xab,0xd7,0x43,0x93);
DEFINE_GUID(CLSID_WICXMPAltMetadataWriter, 0x076c2a6c,0xf78f,0x4c46,0xa7,0x23,0x35,0x83,0xe7,0x08,0x76,0xea);
DEFINE_GUID(CLSID_WICXMPBagMetadataReader, 0xe7e79a30,0x4f2c,0x4fab,0x8d,0x00,0x39,0x4f,0x2d,0x6b,0xbe,0xbe);
DEFINE_GUID(CLSID_WICXMPBagMetadataWriter, 0xed822c8c,0xd6be,0x4301,0xa6,0x31,0x0e,0x14,0x16,0xba,0xd2,0x8f);
DEFINE_GUID(CLSID_WICXMPSeqMetadataReader, 0x7f12e753,0xfc71,0x43d7,0xa5,0x1d,0x92,0xf3,0x59,0x77,0xab,0xb5);
DEFINE_GUID(CLSID_WICXMPSeqMetadataWriter, 0x6d68d1de,0xd432,0x4b0f,0x92,0x3a,0x09,0x11,0x83,0xa9,0xbd,0xa7);
DEFINE_GUID(CLSID_WICXMPStructMetadataReader, 0x01b90d9a,0x8209,0x47f7,0x9c,0x52,0xe1,0x24,0x4b,0xf5,0x0c,0xed);
DEFINE_GUID(CLSID_WICXMPStructMetadataWriter, 0x22c21f93,0x7ddb,0x411c,0x9b,0x17,0xc5,0xb7,0xbd,0x06,0x4a,0xbc);
DEFINE_GUID(CLSID_WICLSDMetadataReader, 0x41070793,0x59e4,0x479a,0xa1,0xf7,0x95,0x4a,0xdc,0x2e,0xf5,0xfc);
DEFINE_GUID(CLSID_WICLSDMetadataWriter, 0x73c037e7,0xe5d9,0x4954,0x87,0x6a,0x6d,0xa8,0x1d,0x6e,0x57,0x68);
DEFINE_GUID(CLSID_WICIMDMetadataReader, 0x7447a267,0x0015,0x42c8,0xa8,0xf1,0xfb,0x3b,0x94,0xc6,0x83,0x61);
DEFINE_GUID(CLSID_WICIMDMetadataWriter, 0x8c89071f,0x452e,0x4e95,0x96,0x82,0x9d,0x10,0x24,0x62,0x71,0x72);
DEFINE_GUID(CLSID_WICGCEMetadataReader, 0xb92e345d,0xf52d,0x41f3,0xb5,0x62,0x08,0x1b,0xc7,0x72,0xe3,0xb9);
DEFINE_GUID(CLSID_WICGCEMetadataWriter, 0xaf95dc76,0x16b2,0x47f4,0xb3,0xea,0x3c,0x31,0x79,0x66,0x93,0xe7);
DEFINE_GUID(CLSID_WICAPEMetadataReader, 0x1767b93a,0xb021,0x44ea,0x92,0x0f,0x86,0x3c,0x11,0xf4,0xf7,0x68);
DEFINE_GUID(CLSID_WICAPEMetadataWriter, 0xbd6edfca,0x2890,0x482f,0xb2,0x33,0x8d,0x73,0x39,0xa1,0xcf,0x8d);
DEFINE_GUID(CLSID_WICGifCommentMetadataReader, 0x32557d3b,0x69dc,0x4f95,0x83,0x6e,0xf5,0x97,0x2b,0x2f,0x61,0x59);
DEFINE_GUID(CLSID_WICGifCommentMetadataWriter, 0xa02797fc,0xc4ae,0x418c,0xaf,0x95,0xe6,0x37,0xc7,0xea,0xd2,0xa1);
DEFINE_GUID(CLSID_WICApp0MetadataWriter, 0xf3c633a2,0x46c8,0x498e,0x8f,0xbb,0xcc,0x6f,0x72,0x1b,0xbc,0xde);
DEFINE_GUID(CLSID_WICApp0MetadataReader, 0x43324b33,0xa78f,0x480f,0x91,0x11,0x96,0x38,0xaa,0xcc,0xc8,0x32);
DEFINE_GUID(CLSID_WICApp1MetadataWriter, 0xee366069,0x1832,0x420f,0xb3,0x81,0x04,0x79,0xad,0x06,0x6f,0x19);
DEFINE_GUID(CLSID_WICApp1MetadataReader, 0xdde33513,0x774e,0x4bcd,0xae,0x79,0x02,0xf4,0xad,0xfe,0x62,0xfc);
DEFINE_GUID(CLSID_WICApp13MetadataWriter, 0x7b19a919,0xa9d6,0x49e5,0xbd,0x45,0x02,0xc3,0x4e,0x4e,0x4c,0xd5);
DEFINE_GUID(CLSID_WICApp13MetadataReader, 0xaa7e3c50,0x864c,0x4604,0xbc,0x04,0x8b,0x0b,0x76,0xe6,0x37,0xf6);
DEFINE_GUID(CLSID_WICSubIfdMetadataReader, 0x50d42f09,0xecd1,0x4b41,0xb6,0x5d,0xda,0x1f,0xda,0xa7,0x56,0x63);
DEFINE_GUID(CLSID_WICSubIfdMetadataWriter, 0x8ade5386,0x8e9b,0x4f4c,0xac,0xf2,0xf0,0x00,0x87,0x06,0xb2,0x38);
DEFINE_GUID(CLSID_WICGpsMetadataReader, 0x3697790b,0x223b,0x484e,0x99,0x25,0xc4,0x86,0x92,0x18,0xf1,0x7a);
DEFINE_GUID(CLSID_WICGpsMetadataWriter, 0xcb8c13e4,0x62b5,0x4c96,0xa4,0x8b,0x6b,0xa6,0xac,0xe3,0x9c,0x76);
DEFINE_GUID(CLSID_WICInteropMetadataReader, 0xb5c8b898,0x0074,0x459f,0xb7,0x00,0x86,0x0d,0x46,0x51,0xea,0x14);
DEFINE_GUID(CLSID_WICInteropMetadataWriter, 0x122ec645,0xcd7e,0x44d8,0xb1,0x86,0x2c,0x8c,0x20,0xc3,0xb5,0x0f);
DEFINE_GUID(CLSID_WICThumbnailMetadataReader, 0xfb012959,0xf4f6,0x44d7,0x9d,0x09,0xda,0xa0,0x87,0xa9,0xdb,0x57);
DEFINE_GUID(CLSID_WICThumbnailMetadataWriter, 0xd049b20c,0x5dd0,0x44fe,0xb0,0xb3,0x8f,0x92,0xc8,0xe6,0xd0,0x80);
DEFINE_GUID(CLSID_WICIPTCMetadataReader, 0x03012959,0xf4f6,0x44d7,0x9d,0x09,0xda,0xa0,0x87,0xa9,0xdb,0x57);
DEFINE_GUID(CLSID_WICIPTCMetadataWriter, 0x1249b20c,0x5dd0,0x44fe,0xb0,0xb3,0x8f,0x92,0xc8,0xe6,0xd0,0x80);
DEFINE_GUID(CLSID_WICIRBMetadataReader, 0xd4dcd3d7,0xb4c2,0x47d9,0xa6,0xbf,0xb8,0x9b,0xa3,0x96,0xa4,0xa3);
DEFINE_GUID(CLSID_WICIRBMetadataWriter, 0x5c5c1935,0x0235,0x4434,0x80,0xbc,0x25,0x1b,0xc1,0xec,0x39,0xc6);
DEFINE_GUID(CLSID_WIC8BIMIPTCMetadataReader, 0x0010668c,0x0801,0x4da6,0xa4,0xa4,0x82,0x65,0x22,0xb6,0xd2,0x8f);
DEFINE_GUID(CLSID_WIC8BIMIPTCMetadataWriter, 0x00108226,0xee41,0x44a2,0x9e,0x9c,0x4b,0xe4,0xd5,0xb1,0xd2,0xcd);
DEFINE_GUID(CLSID_WIC8BIMIPTCDigestMetadataReader, 0x02805f1e,0xd5aa,0x415b,0x82,0xc5,0x61,0xc0,0x33,0xa9,0x88,0xa6);
DEFINE_GUID(CLSID_WIC8BIMIPTCDigestMetadataWriter, 0x2db5e62b,0x0d67,0x495f,0x8f,0x9d,0xc2,0xf0,0x18,0x86,0x47,0xac);
DEFINE_GUID(CLSID_WIC8BIMResolutionInfoMetadataReader, 0x5805137a,0xe348,0x4f7c,0xb3,0xcc,0x6d,0xb9,0x96,0x5a,0x05,0x99);
DEFINE_GUID(CLSID_WIC8BIMResolutionInfoMetadataWriter, 0x4ff2fe0e,0xe74a,0x4b71,0x98,0xc4,0xab,0x7d,0xc1,0x67,0x07,0xba);
DEFINE_GUID(CLSID_WICJpegChrominanceMetadataReader, 0x50b1904b,0xf28f,0x4574,0x93,0xf4,0x0b,0xad,0xe8,0x2c,0x69,0xe9);
DEFINE_GUID(CLSID_WICJpegChrominanceMetadataWriter, 0x3ff566f0,0x6e6b,0x49d4,0x96,0xe6,0xb7,0x88,0x86,0x69,0x2c,0x62);
DEFINE_GUID(CLSID_WICJpegCommentMetadataReader, 0x9f66347c,0x60c4,0x4c4d,0xab,0x58,0xd2,0x35,0x86,0x85,0xf6,0x07);
DEFINE_GUID(CLSID_WICJpegCommentMetadataWriter, 0xe573236f,0x55b1,0x4eda,0x81,0xea,0x9f,0x65,0xdb,0x02,0x90,0xd3);
DEFINE_GUID(CLSID_WICJpegLuminanceMetadataReader, 0x356f2f88,0x05a6,0x4728,0xb9,0xa4,0x1b,0xfb,0xce,0x04,0xd8,0x38);
DEFINE_GUID(CLSID_WICJpegLuminanceMetadataWriter, 0x1d583abc,0x8a0e,0x4657,0x99,0x82,0xa3,0x80,0xca,0x58,0xfb,0x4b);
DEFINE_GUID(CLSID_WICDdsMetadataReader, 0x276c88ca,0x7533,0x4a86,0xb6,0x76,0x66,0xb3,0x60,0x80,0xd4,0x84);
DEFINE_GUID(CLSID_WICDdsMetadataWriter, 0xfd688bbd,0x31ed,0x4db7,0xa7,0x23,0x93,0x49,0x27,0xd3,0x83,0x67);
DEFINE_GUID(CLSID_WICHeifMetadataReader, 0xacddfc3f,0x85ec,0x41bc,0xbd,0xef,0x1b,0xc2,0x62,0xe4,0xdb,0x05);
DEFINE_GUID(CLSID_WICHeifMetadataWriter, 0x3ae45e79,0x40bc,0x4401,0xac,0xe5,0xdd,0x3c,0xb1,0x6e,0x6a,0xfe);
DEFINE_GUID(CLSID_WICHeifHDRMetadataReader, 0x2438de3d,0x94d9,0x4be8,0x84,0xa8,0x4d,0xe9,0x5a,0x57,0x5e,0x75);
DEFINE_GUID(CLSID_WICWebpAnimMetadataReader, 0x076f9911,0xa348,0x465c,0xa8,0x07,0xa2,0x52,0xf3,0xf2,0xd3,0xde);
DEFINE_GUID(CLSID_WICWebpAnmfMetadataReader, 0x85a10b03,0xc9f6,0x439f,0xbe,0x5e,0xc0,0xfb,0xef,0x67,0x80,0x7c);
typedef struct WICMetadataPattern {
    ULARGE_INTEGER Position;
    ULONG Length;
    BYTE *Pattern;
    BYTE *Mask;
    ULARGE_INTEGER DataOffset;
} WICMetadataPattern;
typedef struct WICMetadataHeader {
    ULARGE_INTEGER Position;
    ULONG Length;
    BYTE *Header;
    ULARGE_INTEGER DataOffset;
} WICMetadataHeader;
/*****************************************************************************
 * IWICMetadataHandlerInfo interface
 */
#ifndef __IWICMetadataHandlerInfo_INTERFACE_DEFINED__
#define __IWICMetadataHandlerInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICMetadataHandlerInfo, 0xaba958bf, 0xc672, 0x44d1, 0x8d,0x61, 0xce,0x6d,0xf2,0xe6,0x82,0xc2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aba958bf-c672-44d1-8d61-ce6df2e682c2")
IWICMetadataHandlerInfo : public IWICComponentInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetMetadataFormat(
        GUID *pguidMetadataFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContainerFormats(
        UINT cContainerFormats,
        GUID *pguidContainerFormats,
        UINT *pcchActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceManufacturer(
        UINT cchDeviceManufacturer,
        WCHAR *wzDeviceManufacturer,
        UINT *pcchActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceModels(
        UINT cchDeviceModels,
        WCHAR *wzDeviceModels,
        UINT *pcchActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoesRequireFullStream(
        WINBOOL *pfRequiresFullStream) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoesSupportPadding(
        WINBOOL *pfSupportsPadding) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoesRequireFixedSize(
        WINBOOL *pfFixedSize) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICMetadataHandlerInfo, 0xaba958bf, 0xc672, 0x44d1, 0x8d,0x61, 0xce,0x6d,0xf2,0xe6,0x82,0xc2)
#endif
#else
typedef struct IWICMetadataHandlerInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICMetadataHandlerInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICMetadataHandlerInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICMetadataHandlerInfo *This);

    /*** IWICComponentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IWICMetadataHandlerInfo *This,
        WICComponentType *pType);

    HRESULT (STDMETHODCALLTYPE *GetCLSID)(
        IWICMetadataHandlerInfo *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *GetSigningStatus)(
        IWICMetadataHandlerInfo *This,
        DWORD *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetAuthor)(
        IWICMetadataHandlerInfo *This,
        UINT cchAuthor,
        WCHAR *wzAuthor,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetVendorGUID)(
        IWICMetadataHandlerInfo *This,
        GUID *pguidVendor);

    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWICMetadataHandlerInfo *This,
        UINT cchVersion,
        WCHAR *wzVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetSpecVersion)(
        IWICMetadataHandlerInfo *This,
        UINT cchSpecVersion,
        WCHAR *wzSpecVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFriendlyName)(
        IWICMetadataHandlerInfo *This,
        UINT cchFriendlyName,
        WCHAR *wzFriendlyName,
        UINT *pcchActual);

    /*** IWICMetadataHandlerInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMetadataFormat)(
        IWICMetadataHandlerInfo *This,
        GUID *pguidMetadataFormat);

    HRESULT (STDMETHODCALLTYPE *GetContainerFormats)(
        IWICMetadataHandlerInfo *This,
        UINT cContainerFormats,
        GUID *pguidContainerFormats,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceManufacturer)(
        IWICMetadataHandlerInfo *This,
        UINT cchDeviceManufacturer,
        WCHAR *wzDeviceManufacturer,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceModels)(
        IWICMetadataHandlerInfo *This,
        UINT cchDeviceModels,
        WCHAR *wzDeviceModels,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *DoesRequireFullStream)(
        IWICMetadataHandlerInfo *This,
        WINBOOL *pfRequiresFullStream);

    HRESULT (STDMETHODCALLTYPE *DoesSupportPadding)(
        IWICMetadataHandlerInfo *This,
        WINBOOL *pfSupportsPadding);

    HRESULT (STDMETHODCALLTYPE *DoesRequireFixedSize)(
        IWICMetadataHandlerInfo *This,
        WINBOOL *pfFixedSize);

    END_INTERFACE
} IWICMetadataHandlerInfoVtbl;

interface IWICMetadataHandlerInfo {
    CONST_VTBL IWICMetadataHandlerInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICMetadataHandlerInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICMetadataHandlerInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICMetadataHandlerInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWICComponentInfo methods ***/
#define IWICMetadataHandlerInfo_GetComponentType(This,pType) (This)->lpVtbl->GetComponentType(This,pType)
#define IWICMetadataHandlerInfo_GetCLSID(This,pclsid) (This)->lpVtbl->GetCLSID(This,pclsid)
#define IWICMetadataHandlerInfo_GetSigningStatus(This,pStatus) (This)->lpVtbl->GetSigningStatus(This,pStatus)
#define IWICMetadataHandlerInfo_GetAuthor(This,cchAuthor,wzAuthor,pcchActual) (This)->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual)
#define IWICMetadataHandlerInfo_GetVendorGUID(This,pguidVendor) (This)->lpVtbl->GetVendorGUID(This,pguidVendor)
#define IWICMetadataHandlerInfo_GetVersion(This,cchVersion,wzVersion,pcchActual) (This)->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual)
#define IWICMetadataHandlerInfo_GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual) (This)->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual)
#define IWICMetadataHandlerInfo_GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual) (This)->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual)
/*** IWICMetadataHandlerInfo methods ***/
#define IWICMetadataHandlerInfo_GetMetadataFormat(This,pguidMetadataFormat) (This)->lpVtbl->GetMetadataFormat(This,pguidMetadataFormat)
#define IWICMetadataHandlerInfo_GetContainerFormats(This,cContainerFormats,pguidContainerFormats,pcchActual) (This)->lpVtbl->GetContainerFormats(This,cContainerFormats,pguidContainerFormats,pcchActual)
#define IWICMetadataHandlerInfo_GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual) (This)->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual)
#define IWICMetadataHandlerInfo_GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual) (This)->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual)
#define IWICMetadataHandlerInfo_DoesRequireFullStream(This,pfRequiresFullStream) (This)->lpVtbl->DoesRequireFullStream(This,pfRequiresFullStream)
#define IWICMetadataHandlerInfo_DoesSupportPadding(This,pfSupportsPadding) (This)->lpVtbl->DoesSupportPadding(This,pfSupportsPadding)
#define IWICMetadataHandlerInfo_DoesRequireFixedSize(This,pfFixedSize) (This)->lpVtbl->DoesRequireFixedSize(This,pfFixedSize)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICMetadataHandlerInfo_QueryInterface(IWICMetadataHandlerInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICMetadataHandlerInfo_AddRef(IWICMetadataHandlerInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICMetadataHandlerInfo_Release(IWICMetadataHandlerInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICComponentInfo methods ***/
static inline HRESULT IWICMetadataHandlerInfo_GetComponentType(IWICMetadataHandlerInfo* This,WICComponentType *pType) {
    return This->lpVtbl->GetComponentType(This,pType);
}
static inline HRESULT IWICMetadataHandlerInfo_GetCLSID(IWICMetadataHandlerInfo* This,CLSID *pclsid) {
    return This->lpVtbl->GetCLSID(This,pclsid);
}
static inline HRESULT IWICMetadataHandlerInfo_GetSigningStatus(IWICMetadataHandlerInfo* This,DWORD *pStatus) {
    return This->lpVtbl->GetSigningStatus(This,pStatus);
}
static inline HRESULT IWICMetadataHandlerInfo_GetAuthor(IWICMetadataHandlerInfo* This,UINT cchAuthor,WCHAR *wzAuthor,UINT *pcchActual) {
    return This->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual);
}
static inline HRESULT IWICMetadataHandlerInfo_GetVendorGUID(IWICMetadataHandlerInfo* This,GUID *pguidVendor) {
    return This->lpVtbl->GetVendorGUID(This,pguidVendor);
}
static inline HRESULT IWICMetadataHandlerInfo_GetVersion(IWICMetadataHandlerInfo* This,UINT cchVersion,WCHAR *wzVersion,UINT *pcchActual) {
    return This->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual);
}
static inline HRESULT IWICMetadataHandlerInfo_GetSpecVersion(IWICMetadataHandlerInfo* This,UINT cchSpecVersion,WCHAR *wzSpecVersion,UINT *pcchActual) {
    return This->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual);
}
static inline HRESULT IWICMetadataHandlerInfo_GetFriendlyName(IWICMetadataHandlerInfo* This,UINT cchFriendlyName,WCHAR *wzFriendlyName,UINT *pcchActual) {
    return This->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual);
}
/*** IWICMetadataHandlerInfo methods ***/
static inline HRESULT IWICMetadataHandlerInfo_GetMetadataFormat(IWICMetadataHandlerInfo* This,GUID *pguidMetadataFormat) {
    return This->lpVtbl->GetMetadataFormat(This,pguidMetadataFormat);
}
static inline HRESULT IWICMetadataHandlerInfo_GetContainerFormats(IWICMetadataHandlerInfo* This,UINT cContainerFormats,GUID *pguidContainerFormats,UINT *pcchActual) {
    return This->lpVtbl->GetContainerFormats(This,cContainerFormats,pguidContainerFormats,pcchActual);
}
static inline HRESULT IWICMetadataHandlerInfo_GetDeviceManufacturer(IWICMetadataHandlerInfo* This,UINT cchDeviceManufacturer,WCHAR *wzDeviceManufacturer,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual);
}
static inline HRESULT IWICMetadataHandlerInfo_GetDeviceModels(IWICMetadataHandlerInfo* This,UINT cchDeviceModels,WCHAR *wzDeviceModels,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual);
}
static inline HRESULT IWICMetadataHandlerInfo_DoesRequireFullStream(IWICMetadataHandlerInfo* This,WINBOOL *pfRequiresFullStream) {
    return This->lpVtbl->DoesRequireFullStream(This,pfRequiresFullStream);
}
static inline HRESULT IWICMetadataHandlerInfo_DoesSupportPadding(IWICMetadataHandlerInfo* This,WINBOOL *pfSupportsPadding) {
    return This->lpVtbl->DoesSupportPadding(This,pfSupportsPadding);
}
static inline HRESULT IWICMetadataHandlerInfo_DoesRequireFixedSize(IWICMetadataHandlerInfo* This,WINBOOL *pfFixedSize) {
    return This->lpVtbl->DoesRequireFixedSize(This,pfFixedSize);
}
#endif
#endif

#endif


#endif  /* __IWICMetadataHandlerInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICMetadataReader interface
 */
#ifndef __IWICMetadataReader_INTERFACE_DEFINED__
#define __IWICMetadataReader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICMetadataReader, 0x9204fe99, 0xd8fc, 0x4fd5, 0xa0,0x01, 0x95,0x36,0xb0,0x67,0xa8,0x99);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9204fe99-d8fc-4fd5-a001-9536b067a899")
IWICMetadataReader : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMetadataFormat(
        GUID *pguidMetadataFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMetadataHandlerInfo(
        IWICMetadataHandlerInfo **ppIHandler) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT *pcCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValueByIndex(
        UINT nIndex,
        PROPVARIANT *pvarSchema,
        PROPVARIANT *pvarId,
        PROPVARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        const PROPVARIANT *pvarSchema,
        const PROPVARIANT *pvarId,
        PROPVARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumerator(
        IWICEnumMetadataItem **ppIEnumMetadata) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICMetadataReader, 0x9204fe99, 0xd8fc, 0x4fd5, 0xa0,0x01, 0x95,0x36,0xb0,0x67,0xa8,0x99)
#endif
#else
typedef struct IWICMetadataReaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICMetadataReader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICMetadataReader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICMetadataReader *This);

    /*** IWICMetadataReader methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMetadataFormat)(
        IWICMetadataReader *This,
        GUID *pguidMetadataFormat);

    HRESULT (STDMETHODCALLTYPE *GetMetadataHandlerInfo)(
        IWICMetadataReader *This,
        IWICMetadataHandlerInfo **ppIHandler);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IWICMetadataReader *This,
        UINT *pcCount);

    HRESULT (STDMETHODCALLTYPE *GetValueByIndex)(
        IWICMetadataReader *This,
        UINT nIndex,
        PROPVARIANT *pvarSchema,
        PROPVARIANT *pvarId,
        PROPVARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IWICMetadataReader *This,
        const PROPVARIANT *pvarSchema,
        const PROPVARIANT *pvarId,
        PROPVARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IWICMetadataReader *This,
        IWICEnumMetadataItem **ppIEnumMetadata);

    END_INTERFACE
} IWICMetadataReaderVtbl;

interface IWICMetadataReader {
    CONST_VTBL IWICMetadataReaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICMetadataReader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICMetadataReader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICMetadataReader_Release(This) (This)->lpVtbl->Release(This)
/*** IWICMetadataReader methods ***/
#define IWICMetadataReader_GetMetadataFormat(This,pguidMetadataFormat) (This)->lpVtbl->GetMetadataFormat(This,pguidMetadataFormat)
#define IWICMetadataReader_GetMetadataHandlerInfo(This,ppIHandler) (This)->lpVtbl->GetMetadataHandlerInfo(This,ppIHandler)
#define IWICMetadataReader_GetCount(This,pcCount) (This)->lpVtbl->GetCount(This,pcCount)
#define IWICMetadataReader_GetValueByIndex(This,nIndex,pvarSchema,pvarId,pvarValue) (This)->lpVtbl->GetValueByIndex(This,nIndex,pvarSchema,pvarId,pvarValue)
#define IWICMetadataReader_GetValue(This,pvarSchema,pvarId,pvarValue) (This)->lpVtbl->GetValue(This,pvarSchema,pvarId,pvarValue)
#define IWICMetadataReader_GetEnumerator(This,ppIEnumMetadata) (This)->lpVtbl->GetEnumerator(This,ppIEnumMetadata)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICMetadataReader_QueryInterface(IWICMetadataReader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICMetadataReader_AddRef(IWICMetadataReader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICMetadataReader_Release(IWICMetadataReader* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICMetadataReader methods ***/
static inline HRESULT IWICMetadataReader_GetMetadataFormat(IWICMetadataReader* This,GUID *pguidMetadataFormat) {
    return This->lpVtbl->GetMetadataFormat(This,pguidMetadataFormat);
}
static inline HRESULT IWICMetadataReader_GetMetadataHandlerInfo(IWICMetadataReader* This,IWICMetadataHandlerInfo **ppIHandler) {
    return This->lpVtbl->GetMetadataHandlerInfo(This,ppIHandler);
}
static inline HRESULT IWICMetadataReader_GetCount(IWICMetadataReader* This,UINT *pcCount) {
    return This->lpVtbl->GetCount(This,pcCount);
}
static inline HRESULT IWICMetadataReader_GetValueByIndex(IWICMetadataReader* This,UINT nIndex,PROPVARIANT *pvarSchema,PROPVARIANT *pvarId,PROPVARIANT *pvarValue) {
    return This->lpVtbl->GetValueByIndex(This,nIndex,pvarSchema,pvarId,pvarValue);
}
static inline HRESULT IWICMetadataReader_GetValue(IWICMetadataReader* This,const PROPVARIANT *pvarSchema,const PROPVARIANT *pvarId,PROPVARIANT *pvarValue) {
    return This->lpVtbl->GetValue(This,pvarSchema,pvarId,pvarValue);
}
static inline HRESULT IWICMetadataReader_GetEnumerator(IWICMetadataReader* This,IWICEnumMetadataItem **ppIEnumMetadata) {
    return This->lpVtbl->GetEnumerator(This,ppIEnumMetadata);
}
#endif
#endif

#endif


#endif  /* __IWICMetadataReader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICMetadataReaderInfo interface
 */
#ifndef __IWICMetadataReaderInfo_INTERFACE_DEFINED__
#define __IWICMetadataReaderInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICMetadataReaderInfo, 0xeebf1f5b, 0x07c1, 0x4447, 0xa3,0xab, 0x22,0xac,0xaf,0x78,0xa8,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eebf1f5b-07c1-4447-a3ab-22acaf78a804")
IWICMetadataReaderInfo : public IWICMetadataHandlerInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetPatterns(
        REFGUID guidContainerFormat,
        UINT cbSize,
        WICMetadataPattern *pPattern,
        UINT *pcCount,
        UINT *pcbActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE MatchesPattern(
        REFGUID guidContainerFormat,
        IStream *pIStream,
        WINBOOL *pfMatches) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstance(
        IWICMetadataReader **ppIReader) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICMetadataReaderInfo, 0xeebf1f5b, 0x07c1, 0x4447, 0xa3,0xab, 0x22,0xac,0xaf,0x78,0xa8,0x04)
#endif
#else
typedef struct IWICMetadataReaderInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICMetadataReaderInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICMetadataReaderInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICMetadataReaderInfo *This);

    /*** IWICComponentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IWICMetadataReaderInfo *This,
        WICComponentType *pType);

    HRESULT (STDMETHODCALLTYPE *GetCLSID)(
        IWICMetadataReaderInfo *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *GetSigningStatus)(
        IWICMetadataReaderInfo *This,
        DWORD *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetAuthor)(
        IWICMetadataReaderInfo *This,
        UINT cchAuthor,
        WCHAR *wzAuthor,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetVendorGUID)(
        IWICMetadataReaderInfo *This,
        GUID *pguidVendor);

    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWICMetadataReaderInfo *This,
        UINT cchVersion,
        WCHAR *wzVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetSpecVersion)(
        IWICMetadataReaderInfo *This,
        UINT cchSpecVersion,
        WCHAR *wzSpecVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFriendlyName)(
        IWICMetadataReaderInfo *This,
        UINT cchFriendlyName,
        WCHAR *wzFriendlyName,
        UINT *pcchActual);

    /*** IWICMetadataHandlerInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMetadataFormat)(
        IWICMetadataReaderInfo *This,
        GUID *pguidMetadataFormat);

    HRESULT (STDMETHODCALLTYPE *GetContainerFormats)(
        IWICMetadataReaderInfo *This,
        UINT cContainerFormats,
        GUID *pguidContainerFormats,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceManufacturer)(
        IWICMetadataReaderInfo *This,
        UINT cchDeviceManufacturer,
        WCHAR *wzDeviceManufacturer,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceModels)(
        IWICMetadataReaderInfo *This,
        UINT cchDeviceModels,
        WCHAR *wzDeviceModels,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *DoesRequireFullStream)(
        IWICMetadataReaderInfo *This,
        WINBOOL *pfRequiresFullStream);

    HRESULT (STDMETHODCALLTYPE *DoesSupportPadding)(
        IWICMetadataReaderInfo *This,
        WINBOOL *pfSupportsPadding);

    HRESULT (STDMETHODCALLTYPE *DoesRequireFixedSize)(
        IWICMetadataReaderInfo *This,
        WINBOOL *pfFixedSize);

    /*** IWICMetadataReaderInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPatterns)(
        IWICMetadataReaderInfo *This,
        REFGUID guidContainerFormat,
        UINT cbSize,
        WICMetadataPattern *pPattern,
        UINT *pcCount,
        UINT *pcbActual);

    HRESULT (STDMETHODCALLTYPE *MatchesPattern)(
        IWICMetadataReaderInfo *This,
        REFGUID guidContainerFormat,
        IStream *pIStream,
        WINBOOL *pfMatches);

    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IWICMetadataReaderInfo *This,
        IWICMetadataReader **ppIReader);

    END_INTERFACE
} IWICMetadataReaderInfoVtbl;

interface IWICMetadataReaderInfo {
    CONST_VTBL IWICMetadataReaderInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICMetadataReaderInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICMetadataReaderInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICMetadataReaderInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWICComponentInfo methods ***/
#define IWICMetadataReaderInfo_GetComponentType(This,pType) (This)->lpVtbl->GetComponentType(This,pType)
#define IWICMetadataReaderInfo_GetCLSID(This,pclsid) (This)->lpVtbl->GetCLSID(This,pclsid)
#define IWICMetadataReaderInfo_GetSigningStatus(This,pStatus) (This)->lpVtbl->GetSigningStatus(This,pStatus)
#define IWICMetadataReaderInfo_GetAuthor(This,cchAuthor,wzAuthor,pcchActual) (This)->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual)
#define IWICMetadataReaderInfo_GetVendorGUID(This,pguidVendor) (This)->lpVtbl->GetVendorGUID(This,pguidVendor)
#define IWICMetadataReaderInfo_GetVersion(This,cchVersion,wzVersion,pcchActual) (This)->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual)
#define IWICMetadataReaderInfo_GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual) (This)->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual)
#define IWICMetadataReaderInfo_GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual) (This)->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual)
/*** IWICMetadataHandlerInfo methods ***/
#define IWICMetadataReaderInfo_GetMetadataFormat(This,pguidMetadataFormat) (This)->lpVtbl->GetMetadataFormat(This,pguidMetadataFormat)
#define IWICMetadataReaderInfo_GetContainerFormats(This,cContainerFormats,pguidContainerFormats,pcchActual) (This)->lpVtbl->GetContainerFormats(This,cContainerFormats,pguidContainerFormats,pcchActual)
#define IWICMetadataReaderInfo_GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual) (This)->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual)
#define IWICMetadataReaderInfo_GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual) (This)->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual)
#define IWICMetadataReaderInfo_DoesRequireFullStream(This,pfRequiresFullStream) (This)->lpVtbl->DoesRequireFullStream(This,pfRequiresFullStream)
#define IWICMetadataReaderInfo_DoesSupportPadding(This,pfSupportsPadding) (This)->lpVtbl->DoesSupportPadding(This,pfSupportsPadding)
#define IWICMetadataReaderInfo_DoesRequireFixedSize(This,pfFixedSize) (This)->lpVtbl->DoesRequireFixedSize(This,pfFixedSize)
/*** IWICMetadataReaderInfo methods ***/
#define IWICMetadataReaderInfo_GetPatterns(This,guidContainerFormat,cbSize,pPattern,pcCount,pcbActual) (This)->lpVtbl->GetPatterns(This,guidContainerFormat,cbSize,pPattern,pcCount,pcbActual)
#define IWICMetadataReaderInfo_MatchesPattern(This,guidContainerFormat,pIStream,pfMatches) (This)->lpVtbl->MatchesPattern(This,guidContainerFormat,pIStream,pfMatches)
#define IWICMetadataReaderInfo_CreateInstance(This,ppIReader) (This)->lpVtbl->CreateInstance(This,ppIReader)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICMetadataReaderInfo_QueryInterface(IWICMetadataReaderInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICMetadataReaderInfo_AddRef(IWICMetadataReaderInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICMetadataReaderInfo_Release(IWICMetadataReaderInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICComponentInfo methods ***/
static inline HRESULT IWICMetadataReaderInfo_GetComponentType(IWICMetadataReaderInfo* This,WICComponentType *pType) {
    return This->lpVtbl->GetComponentType(This,pType);
}
static inline HRESULT IWICMetadataReaderInfo_GetCLSID(IWICMetadataReaderInfo* This,CLSID *pclsid) {
    return This->lpVtbl->GetCLSID(This,pclsid);
}
static inline HRESULT IWICMetadataReaderInfo_GetSigningStatus(IWICMetadataReaderInfo* This,DWORD *pStatus) {
    return This->lpVtbl->GetSigningStatus(This,pStatus);
}
static inline HRESULT IWICMetadataReaderInfo_GetAuthor(IWICMetadataReaderInfo* This,UINT cchAuthor,WCHAR *wzAuthor,UINT *pcchActual) {
    return This->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual);
}
static inline HRESULT IWICMetadataReaderInfo_GetVendorGUID(IWICMetadataReaderInfo* This,GUID *pguidVendor) {
    return This->lpVtbl->GetVendorGUID(This,pguidVendor);
}
static inline HRESULT IWICMetadataReaderInfo_GetVersion(IWICMetadataReaderInfo* This,UINT cchVersion,WCHAR *wzVersion,UINT *pcchActual) {
    return This->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual);
}
static inline HRESULT IWICMetadataReaderInfo_GetSpecVersion(IWICMetadataReaderInfo* This,UINT cchSpecVersion,WCHAR *wzSpecVersion,UINT *pcchActual) {
    return This->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual);
}
static inline HRESULT IWICMetadataReaderInfo_GetFriendlyName(IWICMetadataReaderInfo* This,UINT cchFriendlyName,WCHAR *wzFriendlyName,UINT *pcchActual) {
    return This->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual);
}
/*** IWICMetadataHandlerInfo methods ***/
static inline HRESULT IWICMetadataReaderInfo_GetMetadataFormat(IWICMetadataReaderInfo* This,GUID *pguidMetadataFormat) {
    return This->lpVtbl->GetMetadataFormat(This,pguidMetadataFormat);
}
static inline HRESULT IWICMetadataReaderInfo_GetContainerFormats(IWICMetadataReaderInfo* This,UINT cContainerFormats,GUID *pguidContainerFormats,UINT *pcchActual) {
    return This->lpVtbl->GetContainerFormats(This,cContainerFormats,pguidContainerFormats,pcchActual);
}
static inline HRESULT IWICMetadataReaderInfo_GetDeviceManufacturer(IWICMetadataReaderInfo* This,UINT cchDeviceManufacturer,WCHAR *wzDeviceManufacturer,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual);
}
static inline HRESULT IWICMetadataReaderInfo_GetDeviceModels(IWICMetadataReaderInfo* This,UINT cchDeviceModels,WCHAR *wzDeviceModels,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual);
}
static inline HRESULT IWICMetadataReaderInfo_DoesRequireFullStream(IWICMetadataReaderInfo* This,WINBOOL *pfRequiresFullStream) {
    return This->lpVtbl->DoesRequireFullStream(This,pfRequiresFullStream);
}
static inline HRESULT IWICMetadataReaderInfo_DoesSupportPadding(IWICMetadataReaderInfo* This,WINBOOL *pfSupportsPadding) {
    return This->lpVtbl->DoesSupportPadding(This,pfSupportsPadding);
}
static inline HRESULT IWICMetadataReaderInfo_DoesRequireFixedSize(IWICMetadataReaderInfo* This,WINBOOL *pfFixedSize) {
    return This->lpVtbl->DoesRequireFixedSize(This,pfFixedSize);
}
/*** IWICMetadataReaderInfo methods ***/
static inline HRESULT IWICMetadataReaderInfo_GetPatterns(IWICMetadataReaderInfo* This,REFGUID guidContainerFormat,UINT cbSize,WICMetadataPattern *pPattern,UINT *pcCount,UINT *pcbActual) {
    return This->lpVtbl->GetPatterns(This,guidContainerFormat,cbSize,pPattern,pcCount,pcbActual);
}
static inline HRESULT IWICMetadataReaderInfo_MatchesPattern(IWICMetadataReaderInfo* This,REFGUID guidContainerFormat,IStream *pIStream,WINBOOL *pfMatches) {
    return This->lpVtbl->MatchesPattern(This,guidContainerFormat,pIStream,pfMatches);
}
static inline HRESULT IWICMetadataReaderInfo_CreateInstance(IWICMetadataReaderInfo* This,IWICMetadataReader **ppIReader) {
    return This->lpVtbl->CreateInstance(This,ppIReader);
}
#endif
#endif

#endif


#endif  /* __IWICMetadataReaderInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICMetadataWriter interface
 */
#ifndef __IWICMetadataWriter_INTERFACE_DEFINED__
#define __IWICMetadataWriter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICMetadataWriter, 0xf7836e16, 0x3be0, 0x470b, 0x86,0xbb, 0x16,0x0d,0x0a,0xec,0xd7,0xde);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f7836e16-3be0-470b-86bb-160d0aecd7de")
IWICMetadataWriter : public IWICMetadataReader
{
    virtual HRESULT STDMETHODCALLTYPE SetValue(
        const PROPVARIANT *pvarSchema,
        const PROPVARIANT *pvarId,
        const PROPVARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValueByIndex(
        UINT nIndex,
        const PROPVARIANT *pvarSchema,
        const PROPVARIANT *pvarId,
        const PROPVARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveValue(
        const PROPVARIANT *pvarSchema,
        const PROPVARIANT *pvarId) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveValueByIndex(
        UINT nIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICMetadataWriter, 0xf7836e16, 0x3be0, 0x470b, 0x86,0xbb, 0x16,0x0d,0x0a,0xec,0xd7,0xde)
#endif
#else
typedef struct IWICMetadataWriterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICMetadataWriter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICMetadataWriter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICMetadataWriter *This);

    /*** IWICMetadataReader methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMetadataFormat)(
        IWICMetadataWriter *This,
        GUID *pguidMetadataFormat);

    HRESULT (STDMETHODCALLTYPE *GetMetadataHandlerInfo)(
        IWICMetadataWriter *This,
        IWICMetadataHandlerInfo **ppIHandler);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IWICMetadataWriter *This,
        UINT *pcCount);

    HRESULT (STDMETHODCALLTYPE *GetValueByIndex)(
        IWICMetadataWriter *This,
        UINT nIndex,
        PROPVARIANT *pvarSchema,
        PROPVARIANT *pvarId,
        PROPVARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IWICMetadataWriter *This,
        const PROPVARIANT *pvarSchema,
        const PROPVARIANT *pvarId,
        PROPVARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IWICMetadataWriter *This,
        IWICEnumMetadataItem **ppIEnumMetadata);

    /*** IWICMetadataWriter methods ***/
    HRESULT (STDMETHODCALLTYPE *SetValue)(
        IWICMetadataWriter *This,
        const PROPVARIANT *pvarSchema,
        const PROPVARIANT *pvarId,
        const PROPVARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *SetValueByIndex)(
        IWICMetadataWriter *This,
        UINT nIndex,
        const PROPVARIANT *pvarSchema,
        const PROPVARIANT *pvarId,
        const PROPVARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *RemoveValue)(
        IWICMetadataWriter *This,
        const PROPVARIANT *pvarSchema,
        const PROPVARIANT *pvarId);

    HRESULT (STDMETHODCALLTYPE *RemoveValueByIndex)(
        IWICMetadataWriter *This,
        UINT nIndex);

    END_INTERFACE
} IWICMetadataWriterVtbl;

interface IWICMetadataWriter {
    CONST_VTBL IWICMetadataWriterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICMetadataWriter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICMetadataWriter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICMetadataWriter_Release(This) (This)->lpVtbl->Release(This)
/*** IWICMetadataReader methods ***/
#define IWICMetadataWriter_GetMetadataFormat(This,pguidMetadataFormat) (This)->lpVtbl->GetMetadataFormat(This,pguidMetadataFormat)
#define IWICMetadataWriter_GetMetadataHandlerInfo(This,ppIHandler) (This)->lpVtbl->GetMetadataHandlerInfo(This,ppIHandler)
#define IWICMetadataWriter_GetCount(This,pcCount) (This)->lpVtbl->GetCount(This,pcCount)
#define IWICMetadataWriter_GetValueByIndex(This,nIndex,pvarSchema,pvarId,pvarValue) (This)->lpVtbl->GetValueByIndex(This,nIndex,pvarSchema,pvarId,pvarValue)
#define IWICMetadataWriter_GetValue(This,pvarSchema,pvarId,pvarValue) (This)->lpVtbl->GetValue(This,pvarSchema,pvarId,pvarValue)
#define IWICMetadataWriter_GetEnumerator(This,ppIEnumMetadata) (This)->lpVtbl->GetEnumerator(This,ppIEnumMetadata)
/*** IWICMetadataWriter methods ***/
#define IWICMetadataWriter_SetValue(This,pvarSchema,pvarId,pvarValue) (This)->lpVtbl->SetValue(This,pvarSchema,pvarId,pvarValue)
#define IWICMetadataWriter_SetValueByIndex(This,nIndex,pvarSchema,pvarId,pvarValue) (This)->lpVtbl->SetValueByIndex(This,nIndex,pvarSchema,pvarId,pvarValue)
#define IWICMetadataWriter_RemoveValue(This,pvarSchema,pvarId) (This)->lpVtbl->RemoveValue(This,pvarSchema,pvarId)
#define IWICMetadataWriter_RemoveValueByIndex(This,nIndex) (This)->lpVtbl->RemoveValueByIndex(This,nIndex)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICMetadataWriter_QueryInterface(IWICMetadataWriter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICMetadataWriter_AddRef(IWICMetadataWriter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICMetadataWriter_Release(IWICMetadataWriter* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICMetadataReader methods ***/
static inline HRESULT IWICMetadataWriter_GetMetadataFormat(IWICMetadataWriter* This,GUID *pguidMetadataFormat) {
    return This->lpVtbl->GetMetadataFormat(This,pguidMetadataFormat);
}
static inline HRESULT IWICMetadataWriter_GetMetadataHandlerInfo(IWICMetadataWriter* This,IWICMetadataHandlerInfo **ppIHandler) {
    return This->lpVtbl->GetMetadataHandlerInfo(This,ppIHandler);
}
static inline HRESULT IWICMetadataWriter_GetCount(IWICMetadataWriter* This,UINT *pcCount) {
    return This->lpVtbl->GetCount(This,pcCount);
}
static inline HRESULT IWICMetadataWriter_GetValueByIndex(IWICMetadataWriter* This,UINT nIndex,PROPVARIANT *pvarSchema,PROPVARIANT *pvarId,PROPVARIANT *pvarValue) {
    return This->lpVtbl->GetValueByIndex(This,nIndex,pvarSchema,pvarId,pvarValue);
}
static inline HRESULT IWICMetadataWriter_GetValue(IWICMetadataWriter* This,const PROPVARIANT *pvarSchema,const PROPVARIANT *pvarId,PROPVARIANT *pvarValue) {
    return This->lpVtbl->GetValue(This,pvarSchema,pvarId,pvarValue);
}
static inline HRESULT IWICMetadataWriter_GetEnumerator(IWICMetadataWriter* This,IWICEnumMetadataItem **ppIEnumMetadata) {
    return This->lpVtbl->GetEnumerator(This,ppIEnumMetadata);
}
/*** IWICMetadataWriter methods ***/
static inline HRESULT IWICMetadataWriter_SetValue(IWICMetadataWriter* This,const PROPVARIANT *pvarSchema,const PROPVARIANT *pvarId,const PROPVARIANT *pvarValue) {
    return This->lpVtbl->SetValue(This,pvarSchema,pvarId,pvarValue);
}
static inline HRESULT IWICMetadataWriter_SetValueByIndex(IWICMetadataWriter* This,UINT nIndex,const PROPVARIANT *pvarSchema,const PROPVARIANT *pvarId,const PROPVARIANT *pvarValue) {
    return This->lpVtbl->SetValueByIndex(This,nIndex,pvarSchema,pvarId,pvarValue);
}
static inline HRESULT IWICMetadataWriter_RemoveValue(IWICMetadataWriter* This,const PROPVARIANT *pvarSchema,const PROPVARIANT *pvarId) {
    return This->lpVtbl->RemoveValue(This,pvarSchema,pvarId);
}
static inline HRESULT IWICMetadataWriter_RemoveValueByIndex(IWICMetadataWriter* This,UINT nIndex) {
    return This->lpVtbl->RemoveValueByIndex(This,nIndex);
}
#endif
#endif

#endif


#endif  /* __IWICMetadataWriter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICMetadataWriterInfo interface
 */
#ifndef __IWICMetadataWriterInfo_INTERFACE_DEFINED__
#define __IWICMetadataWriterInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICMetadataWriterInfo, 0xb22e3fba, 0x3925, 0x4323, 0xb5,0xc1, 0x9e,0xbf,0xc4,0x30,0xf2,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b22e3fba-3925-4323-b5c1-9ebfc430f236")
IWICMetadataWriterInfo : public IWICMetadataHandlerInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetHeader(
        REFGUID guidContainerFormat,
        UINT cbSize,
        WICMetadataHeader *pHeader,
        UINT *pcbActual) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstance(
        IWICMetadataWriter **ppIWriter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICMetadataWriterInfo, 0xb22e3fba, 0x3925, 0x4323, 0xb5,0xc1, 0x9e,0xbf,0xc4,0x30,0xf2,0x36)
#endif
#else
typedef struct IWICMetadataWriterInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICMetadataWriterInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICMetadataWriterInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICMetadataWriterInfo *This);

    /*** IWICComponentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentType)(
        IWICMetadataWriterInfo *This,
        WICComponentType *pType);

    HRESULT (STDMETHODCALLTYPE *GetCLSID)(
        IWICMetadataWriterInfo *This,
        CLSID *pclsid);

    HRESULT (STDMETHODCALLTYPE *GetSigningStatus)(
        IWICMetadataWriterInfo *This,
        DWORD *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetAuthor)(
        IWICMetadataWriterInfo *This,
        UINT cchAuthor,
        WCHAR *wzAuthor,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetVendorGUID)(
        IWICMetadataWriterInfo *This,
        GUID *pguidVendor);

    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWICMetadataWriterInfo *This,
        UINT cchVersion,
        WCHAR *wzVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetSpecVersion)(
        IWICMetadataWriterInfo *This,
        UINT cchSpecVersion,
        WCHAR *wzSpecVersion,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetFriendlyName)(
        IWICMetadataWriterInfo *This,
        UINT cchFriendlyName,
        WCHAR *wzFriendlyName,
        UINT *pcchActual);

    /*** IWICMetadataHandlerInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMetadataFormat)(
        IWICMetadataWriterInfo *This,
        GUID *pguidMetadataFormat);

    HRESULT (STDMETHODCALLTYPE *GetContainerFormats)(
        IWICMetadataWriterInfo *This,
        UINT cContainerFormats,
        GUID *pguidContainerFormats,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceManufacturer)(
        IWICMetadataWriterInfo *This,
        UINT cchDeviceManufacturer,
        WCHAR *wzDeviceManufacturer,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *GetDeviceModels)(
        IWICMetadataWriterInfo *This,
        UINT cchDeviceModels,
        WCHAR *wzDeviceModels,
        UINT *pcchActual);

    HRESULT (STDMETHODCALLTYPE *DoesRequireFullStream)(
        IWICMetadataWriterInfo *This,
        WINBOOL *pfRequiresFullStream);

    HRESULT (STDMETHODCALLTYPE *DoesSupportPadding)(
        IWICMetadataWriterInfo *This,
        WINBOOL *pfSupportsPadding);

    HRESULT (STDMETHODCALLTYPE *DoesRequireFixedSize)(
        IWICMetadataWriterInfo *This,
        WINBOOL *pfFixedSize);

    /*** IWICMetadataWriterInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetHeader)(
        IWICMetadataWriterInfo *This,
        REFGUID guidContainerFormat,
        UINT cbSize,
        WICMetadataHeader *pHeader,
        UINT *pcbActual);

    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IWICMetadataWriterInfo *This,
        IWICMetadataWriter **ppIWriter);

    END_INTERFACE
} IWICMetadataWriterInfoVtbl;

interface IWICMetadataWriterInfo {
    CONST_VTBL IWICMetadataWriterInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICMetadataWriterInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICMetadataWriterInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICMetadataWriterInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWICComponentInfo methods ***/
#define IWICMetadataWriterInfo_GetComponentType(This,pType) (This)->lpVtbl->GetComponentType(This,pType)
#define IWICMetadataWriterInfo_GetCLSID(This,pclsid) (This)->lpVtbl->GetCLSID(This,pclsid)
#define IWICMetadataWriterInfo_GetSigningStatus(This,pStatus) (This)->lpVtbl->GetSigningStatus(This,pStatus)
#define IWICMetadataWriterInfo_GetAuthor(This,cchAuthor,wzAuthor,pcchActual) (This)->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual)
#define IWICMetadataWriterInfo_GetVendorGUID(This,pguidVendor) (This)->lpVtbl->GetVendorGUID(This,pguidVendor)
#define IWICMetadataWriterInfo_GetVersion(This,cchVersion,wzVersion,pcchActual) (This)->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual)
#define IWICMetadataWriterInfo_GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual) (This)->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual)
#define IWICMetadataWriterInfo_GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual) (This)->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual)
/*** IWICMetadataHandlerInfo methods ***/
#define IWICMetadataWriterInfo_GetMetadataFormat(This,pguidMetadataFormat) (This)->lpVtbl->GetMetadataFormat(This,pguidMetadataFormat)
#define IWICMetadataWriterInfo_GetContainerFormats(This,cContainerFormats,pguidContainerFormats,pcchActual) (This)->lpVtbl->GetContainerFormats(This,cContainerFormats,pguidContainerFormats,pcchActual)
#define IWICMetadataWriterInfo_GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual) (This)->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual)
#define IWICMetadataWriterInfo_GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual) (This)->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual)
#define IWICMetadataWriterInfo_DoesRequireFullStream(This,pfRequiresFullStream) (This)->lpVtbl->DoesRequireFullStream(This,pfRequiresFullStream)
#define IWICMetadataWriterInfo_DoesSupportPadding(This,pfSupportsPadding) (This)->lpVtbl->DoesSupportPadding(This,pfSupportsPadding)
#define IWICMetadataWriterInfo_DoesRequireFixedSize(This,pfFixedSize) (This)->lpVtbl->DoesRequireFixedSize(This,pfFixedSize)
/*** IWICMetadataWriterInfo methods ***/
#define IWICMetadataWriterInfo_GetHeader(This,guidContainerFormat,cbSize,pHeader,pcbActual) (This)->lpVtbl->GetHeader(This,guidContainerFormat,cbSize,pHeader,pcbActual)
#define IWICMetadataWriterInfo_CreateInstance(This,ppIWriter) (This)->lpVtbl->CreateInstance(This,ppIWriter)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICMetadataWriterInfo_QueryInterface(IWICMetadataWriterInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICMetadataWriterInfo_AddRef(IWICMetadataWriterInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICMetadataWriterInfo_Release(IWICMetadataWriterInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICComponentInfo methods ***/
static inline HRESULT IWICMetadataWriterInfo_GetComponentType(IWICMetadataWriterInfo* This,WICComponentType *pType) {
    return This->lpVtbl->GetComponentType(This,pType);
}
static inline HRESULT IWICMetadataWriterInfo_GetCLSID(IWICMetadataWriterInfo* This,CLSID *pclsid) {
    return This->lpVtbl->GetCLSID(This,pclsid);
}
static inline HRESULT IWICMetadataWriterInfo_GetSigningStatus(IWICMetadataWriterInfo* This,DWORD *pStatus) {
    return This->lpVtbl->GetSigningStatus(This,pStatus);
}
static inline HRESULT IWICMetadataWriterInfo_GetAuthor(IWICMetadataWriterInfo* This,UINT cchAuthor,WCHAR *wzAuthor,UINT *pcchActual) {
    return This->lpVtbl->GetAuthor(This,cchAuthor,wzAuthor,pcchActual);
}
static inline HRESULT IWICMetadataWriterInfo_GetVendorGUID(IWICMetadataWriterInfo* This,GUID *pguidVendor) {
    return This->lpVtbl->GetVendorGUID(This,pguidVendor);
}
static inline HRESULT IWICMetadataWriterInfo_GetVersion(IWICMetadataWriterInfo* This,UINT cchVersion,WCHAR *wzVersion,UINT *pcchActual) {
    return This->lpVtbl->GetVersion(This,cchVersion,wzVersion,pcchActual);
}
static inline HRESULT IWICMetadataWriterInfo_GetSpecVersion(IWICMetadataWriterInfo* This,UINT cchSpecVersion,WCHAR *wzSpecVersion,UINT *pcchActual) {
    return This->lpVtbl->GetSpecVersion(This,cchSpecVersion,wzSpecVersion,pcchActual);
}
static inline HRESULT IWICMetadataWriterInfo_GetFriendlyName(IWICMetadataWriterInfo* This,UINT cchFriendlyName,WCHAR *wzFriendlyName,UINT *pcchActual) {
    return This->lpVtbl->GetFriendlyName(This,cchFriendlyName,wzFriendlyName,pcchActual);
}
/*** IWICMetadataHandlerInfo methods ***/
static inline HRESULT IWICMetadataWriterInfo_GetMetadataFormat(IWICMetadataWriterInfo* This,GUID *pguidMetadataFormat) {
    return This->lpVtbl->GetMetadataFormat(This,pguidMetadataFormat);
}
static inline HRESULT IWICMetadataWriterInfo_GetContainerFormats(IWICMetadataWriterInfo* This,UINT cContainerFormats,GUID *pguidContainerFormats,UINT *pcchActual) {
    return This->lpVtbl->GetContainerFormats(This,cContainerFormats,pguidContainerFormats,pcchActual);
}
static inline HRESULT IWICMetadataWriterInfo_GetDeviceManufacturer(IWICMetadataWriterInfo* This,UINT cchDeviceManufacturer,WCHAR *wzDeviceManufacturer,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceManufacturer(This,cchDeviceManufacturer,wzDeviceManufacturer,pcchActual);
}
static inline HRESULT IWICMetadataWriterInfo_GetDeviceModels(IWICMetadataWriterInfo* This,UINT cchDeviceModels,WCHAR *wzDeviceModels,UINT *pcchActual) {
    return This->lpVtbl->GetDeviceModels(This,cchDeviceModels,wzDeviceModels,pcchActual);
}
static inline HRESULT IWICMetadataWriterInfo_DoesRequireFullStream(IWICMetadataWriterInfo* This,WINBOOL *pfRequiresFullStream) {
    return This->lpVtbl->DoesRequireFullStream(This,pfRequiresFullStream);
}
static inline HRESULT IWICMetadataWriterInfo_DoesSupportPadding(IWICMetadataWriterInfo* This,WINBOOL *pfSupportsPadding) {
    return This->lpVtbl->DoesSupportPadding(This,pfSupportsPadding);
}
static inline HRESULT IWICMetadataWriterInfo_DoesRequireFixedSize(IWICMetadataWriterInfo* This,WINBOOL *pfFixedSize) {
    return This->lpVtbl->DoesRequireFixedSize(This,pfFixedSize);
}
/*** IWICMetadataWriterInfo methods ***/
static inline HRESULT IWICMetadataWriterInfo_GetHeader(IWICMetadataWriterInfo* This,REFGUID guidContainerFormat,UINT cbSize,WICMetadataHeader *pHeader,UINT *pcbActual) {
    return This->lpVtbl->GetHeader(This,guidContainerFormat,cbSize,pHeader,pcbActual);
}
static inline HRESULT IWICMetadataWriterInfo_CreateInstance(IWICMetadataWriterInfo* This,IWICMetadataWriter **ppIWriter) {
    return This->lpVtbl->CreateInstance(This,ppIWriter);
}
#endif
#endif

#endif


#endif  /* __IWICMetadataWriterInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICMetadataBlockReader interface
 */
#ifndef __IWICMetadataBlockReader_INTERFACE_DEFINED__
#define __IWICMetadataBlockReader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICMetadataBlockReader, 0xfeaa2a8d, 0xb3f3, 0x43e4, 0xb2,0x5c, 0xd1,0xde,0x99,0x0a,0x1a,0xe1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("feaa2a8d-b3f3-43e4-b25c-d1de990a1ae1")
IWICMetadataBlockReader : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetContainerFormat(
        GUID *pguidContainerFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT *pcCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetReaderByIndex(
        UINT nIndex,
        IWICMetadataReader **ppIMetadataReader) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumerator(
        IEnumUnknown **ppIEnumMetadata) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICMetadataBlockReader, 0xfeaa2a8d, 0xb3f3, 0x43e4, 0xb2,0x5c, 0xd1,0xde,0x99,0x0a,0x1a,0xe1)
#endif
#else
typedef struct IWICMetadataBlockReaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICMetadataBlockReader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICMetadataBlockReader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICMetadataBlockReader *This);

    /*** IWICMetadataBlockReader methods ***/
    HRESULT (STDMETHODCALLTYPE *GetContainerFormat)(
        IWICMetadataBlockReader *This,
        GUID *pguidContainerFormat);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IWICMetadataBlockReader *This,
        UINT *pcCount);

    HRESULT (STDMETHODCALLTYPE *GetReaderByIndex)(
        IWICMetadataBlockReader *This,
        UINT nIndex,
        IWICMetadataReader **ppIMetadataReader);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IWICMetadataBlockReader *This,
        IEnumUnknown **ppIEnumMetadata);

    END_INTERFACE
} IWICMetadataBlockReaderVtbl;

interface IWICMetadataBlockReader {
    CONST_VTBL IWICMetadataBlockReaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICMetadataBlockReader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICMetadataBlockReader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICMetadataBlockReader_Release(This) (This)->lpVtbl->Release(This)
/*** IWICMetadataBlockReader methods ***/
#define IWICMetadataBlockReader_GetContainerFormat(This,pguidContainerFormat) (This)->lpVtbl->GetContainerFormat(This,pguidContainerFormat)
#define IWICMetadataBlockReader_GetCount(This,pcCount) (This)->lpVtbl->GetCount(This,pcCount)
#define IWICMetadataBlockReader_GetReaderByIndex(This,nIndex,ppIMetadataReader) (This)->lpVtbl->GetReaderByIndex(This,nIndex,ppIMetadataReader)
#define IWICMetadataBlockReader_GetEnumerator(This,ppIEnumMetadata) (This)->lpVtbl->GetEnumerator(This,ppIEnumMetadata)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICMetadataBlockReader_QueryInterface(IWICMetadataBlockReader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICMetadataBlockReader_AddRef(IWICMetadataBlockReader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICMetadataBlockReader_Release(IWICMetadataBlockReader* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICMetadataBlockReader methods ***/
static inline HRESULT IWICMetadataBlockReader_GetContainerFormat(IWICMetadataBlockReader* This,GUID *pguidContainerFormat) {
    return This->lpVtbl->GetContainerFormat(This,pguidContainerFormat);
}
static inline HRESULT IWICMetadataBlockReader_GetCount(IWICMetadataBlockReader* This,UINT *pcCount) {
    return This->lpVtbl->GetCount(This,pcCount);
}
static inline HRESULT IWICMetadataBlockReader_GetReaderByIndex(IWICMetadataBlockReader* This,UINT nIndex,IWICMetadataReader **ppIMetadataReader) {
    return This->lpVtbl->GetReaderByIndex(This,nIndex,ppIMetadataReader);
}
static inline HRESULT IWICMetadataBlockReader_GetEnumerator(IWICMetadataBlockReader* This,IEnumUnknown **ppIEnumMetadata) {
    return This->lpVtbl->GetEnumerator(This,ppIEnumMetadata);
}
#endif
#endif

#endif


#endif  /* __IWICMetadataBlockReader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICMetadataBlockWriter interface
 */
#ifndef __IWICMetadataBlockWriter_INTERFACE_DEFINED__
#define __IWICMetadataBlockWriter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICMetadataBlockWriter, 0x08fb9676, 0xb444, 0x41e8, 0x8d,0xbe, 0x6a,0x53,0xa5,0x42,0xbf,0xf1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("08fb9676-b444-41e8-8dbe-6a53a542bff1")
IWICMetadataBlockWriter : public IWICMetadataBlockReader
{
    virtual HRESULT STDMETHODCALLTYPE InitializeFromBlockReader(
        IWICMetadataBlockReader *pIMDBlockReader) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWriterByIndex(
        UINT nIndex,
        IWICMetadataWriter **ppIMetadataWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddWriter(
        IWICMetadataWriter *pIMetadataWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetWriterByIndex(
        UINT nIndex,
        IWICMetadataWriter *pIMetadataWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveWriterByIndex(
        UINT nIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICMetadataBlockWriter, 0x08fb9676, 0xb444, 0x41e8, 0x8d,0xbe, 0x6a,0x53,0xa5,0x42,0xbf,0xf1)
#endif
#else
typedef struct IWICMetadataBlockWriterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICMetadataBlockWriter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICMetadataBlockWriter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICMetadataBlockWriter *This);

    /*** IWICMetadataBlockReader methods ***/
    HRESULT (STDMETHODCALLTYPE *GetContainerFormat)(
        IWICMetadataBlockWriter *This,
        GUID *pguidContainerFormat);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IWICMetadataBlockWriter *This,
        UINT *pcCount);

    HRESULT (STDMETHODCALLTYPE *GetReaderByIndex)(
        IWICMetadataBlockWriter *This,
        UINT nIndex,
        IWICMetadataReader **ppIMetadataReader);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IWICMetadataBlockWriter *This,
        IEnumUnknown **ppIEnumMetadata);

    /*** IWICMetadataBlockWriter methods ***/
    HRESULT (STDMETHODCALLTYPE *InitializeFromBlockReader)(
        IWICMetadataBlockWriter *This,
        IWICMetadataBlockReader *pIMDBlockReader);

    HRESULT (STDMETHODCALLTYPE *GetWriterByIndex)(
        IWICMetadataBlockWriter *This,
        UINT nIndex,
        IWICMetadataWriter **ppIMetadataWriter);

    HRESULT (STDMETHODCALLTYPE *AddWriter)(
        IWICMetadataBlockWriter *This,
        IWICMetadataWriter *pIMetadataWriter);

    HRESULT (STDMETHODCALLTYPE *SetWriterByIndex)(
        IWICMetadataBlockWriter *This,
        UINT nIndex,
        IWICMetadataWriter *pIMetadataWriter);

    HRESULT (STDMETHODCALLTYPE *RemoveWriterByIndex)(
        IWICMetadataBlockWriter *This,
        UINT nIndex);

    END_INTERFACE
} IWICMetadataBlockWriterVtbl;

interface IWICMetadataBlockWriter {
    CONST_VTBL IWICMetadataBlockWriterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICMetadataBlockWriter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICMetadataBlockWriter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICMetadataBlockWriter_Release(This) (This)->lpVtbl->Release(This)
/*** IWICMetadataBlockReader methods ***/
#define IWICMetadataBlockWriter_GetContainerFormat(This,pguidContainerFormat) (This)->lpVtbl->GetContainerFormat(This,pguidContainerFormat)
#define IWICMetadataBlockWriter_GetCount(This,pcCount) (This)->lpVtbl->GetCount(This,pcCount)
#define IWICMetadataBlockWriter_GetReaderByIndex(This,nIndex,ppIMetadataReader) (This)->lpVtbl->GetReaderByIndex(This,nIndex,ppIMetadataReader)
#define IWICMetadataBlockWriter_GetEnumerator(This,ppIEnumMetadata) (This)->lpVtbl->GetEnumerator(This,ppIEnumMetadata)
/*** IWICMetadataBlockWriter methods ***/
#define IWICMetadataBlockWriter_InitializeFromBlockReader(This,pIMDBlockReader) (This)->lpVtbl->InitializeFromBlockReader(This,pIMDBlockReader)
#define IWICMetadataBlockWriter_GetWriterByIndex(This,nIndex,ppIMetadataWriter) (This)->lpVtbl->GetWriterByIndex(This,nIndex,ppIMetadataWriter)
#define IWICMetadataBlockWriter_AddWriter(This,pIMetadataWriter) (This)->lpVtbl->AddWriter(This,pIMetadataWriter)
#define IWICMetadataBlockWriter_SetWriterByIndex(This,nIndex,pIMetadataWriter) (This)->lpVtbl->SetWriterByIndex(This,nIndex,pIMetadataWriter)
#define IWICMetadataBlockWriter_RemoveWriterByIndex(This,nIndex) (This)->lpVtbl->RemoveWriterByIndex(This,nIndex)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICMetadataBlockWriter_QueryInterface(IWICMetadataBlockWriter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICMetadataBlockWriter_AddRef(IWICMetadataBlockWriter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICMetadataBlockWriter_Release(IWICMetadataBlockWriter* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICMetadataBlockReader methods ***/
static inline HRESULT IWICMetadataBlockWriter_GetContainerFormat(IWICMetadataBlockWriter* This,GUID *pguidContainerFormat) {
    return This->lpVtbl->GetContainerFormat(This,pguidContainerFormat);
}
static inline HRESULT IWICMetadataBlockWriter_GetCount(IWICMetadataBlockWriter* This,UINT *pcCount) {
    return This->lpVtbl->GetCount(This,pcCount);
}
static inline HRESULT IWICMetadataBlockWriter_GetReaderByIndex(IWICMetadataBlockWriter* This,UINT nIndex,IWICMetadataReader **ppIMetadataReader) {
    return This->lpVtbl->GetReaderByIndex(This,nIndex,ppIMetadataReader);
}
static inline HRESULT IWICMetadataBlockWriter_GetEnumerator(IWICMetadataBlockWriter* This,IEnumUnknown **ppIEnumMetadata) {
    return This->lpVtbl->GetEnumerator(This,ppIEnumMetadata);
}
/*** IWICMetadataBlockWriter methods ***/
static inline HRESULT IWICMetadataBlockWriter_InitializeFromBlockReader(IWICMetadataBlockWriter* This,IWICMetadataBlockReader *pIMDBlockReader) {
    return This->lpVtbl->InitializeFromBlockReader(This,pIMDBlockReader);
}
static inline HRESULT IWICMetadataBlockWriter_GetWriterByIndex(IWICMetadataBlockWriter* This,UINT nIndex,IWICMetadataWriter **ppIMetadataWriter) {
    return This->lpVtbl->GetWriterByIndex(This,nIndex,ppIMetadataWriter);
}
static inline HRESULT IWICMetadataBlockWriter_AddWriter(IWICMetadataBlockWriter* This,IWICMetadataWriter *pIMetadataWriter) {
    return This->lpVtbl->AddWriter(This,pIMetadataWriter);
}
static inline HRESULT IWICMetadataBlockWriter_SetWriterByIndex(IWICMetadataBlockWriter* This,UINT nIndex,IWICMetadataWriter *pIMetadataWriter) {
    return This->lpVtbl->SetWriterByIndex(This,nIndex,pIMetadataWriter);
}
static inline HRESULT IWICMetadataBlockWriter_RemoveWriterByIndex(IWICMetadataBlockWriter* This,UINT nIndex) {
    return This->lpVtbl->RemoveWriterByIndex(This,nIndex);
}
#endif
#endif

#endif


#endif  /* __IWICMetadataBlockWriter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICPersistStream interface
 */
#ifndef __IWICPersistStream_INTERFACE_DEFINED__
#define __IWICPersistStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICPersistStream, 0x00675040, 0x6908, 0x45f8, 0x86,0xa3, 0x49,0xc7,0xdf,0xd6,0xd9,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00675040-6908-45f8-86a3-49c7dfd6d9ad")
IWICPersistStream : public IPersistStream
{
    virtual HRESULT STDMETHODCALLTYPE LoadEx(
        IStream *pIStream,
        const GUID *pguidPreferredVendor,
        DWORD dwPersistOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveEx(
        IStream *pIStream,
        DWORD dwPersistOptions,
        WINBOOL fClearDirty) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICPersistStream, 0x00675040, 0x6908, 0x45f8, 0x86,0xa3, 0x49,0xc7,0xdf,0xd6,0xd9,0xad)
#endif
#else
typedef struct IWICPersistStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICPersistStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICPersistStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICPersistStream *This);

    /*** IPersist methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClassID)(
        IWICPersistStream *This,
        CLSID *pClassID);

    /*** IPersistStream methods ***/
    HRESULT (STDMETHODCALLTYPE *IsDirty)(
        IWICPersistStream *This);

    HRESULT (STDMETHODCALLTYPE *Load)(
        IWICPersistStream *This,
        IStream *pStm);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IWICPersistStream *This,
        IStream *pStm,
        WINBOOL fClearDirty);

    HRESULT (STDMETHODCALLTYPE *GetSizeMax)(
        IWICPersistStream *This,
        ULARGE_INTEGER *pcbSize);

    /*** IWICPersistStream methods ***/
    HRESULT (STDMETHODCALLTYPE *LoadEx)(
        IWICPersistStream *This,
        IStream *pIStream,
        const GUID *pguidPreferredVendor,
        DWORD dwPersistOptions);

    HRESULT (STDMETHODCALLTYPE *SaveEx)(
        IWICPersistStream *This,
        IStream *pIStream,
        DWORD dwPersistOptions,
        WINBOOL fClearDirty);

    END_INTERFACE
} IWICPersistStreamVtbl;

interface IWICPersistStream {
    CONST_VTBL IWICPersistStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICPersistStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICPersistStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICPersistStream_Release(This) (This)->lpVtbl->Release(This)
/*** IPersist methods ***/
#define IWICPersistStream_GetClassID(This,pClassID) (This)->lpVtbl->GetClassID(This,pClassID)
/*** IPersistStream methods ***/
#define IWICPersistStream_IsDirty(This) (This)->lpVtbl->IsDirty(This)
#define IWICPersistStream_Load(This,pStm) (This)->lpVtbl->Load(This,pStm)
#define IWICPersistStream_Save(This,pStm,fClearDirty) (This)->lpVtbl->Save(This,pStm,fClearDirty)
#define IWICPersistStream_GetSizeMax(This,pcbSize) (This)->lpVtbl->GetSizeMax(This,pcbSize)
/*** IWICPersistStream methods ***/
#define IWICPersistStream_LoadEx(This,pIStream,pguidPreferredVendor,dwPersistOptions) (This)->lpVtbl->LoadEx(This,pIStream,pguidPreferredVendor,dwPersistOptions)
#define IWICPersistStream_SaveEx(This,pIStream,dwPersistOptions,fClearDirty) (This)->lpVtbl->SaveEx(This,pIStream,dwPersistOptions,fClearDirty)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICPersistStream_QueryInterface(IWICPersistStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICPersistStream_AddRef(IWICPersistStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICPersistStream_Release(IWICPersistStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersist methods ***/
static inline HRESULT IWICPersistStream_GetClassID(IWICPersistStream* This,CLSID *pClassID) {
    return This->lpVtbl->GetClassID(This,pClassID);
}
/*** IPersistStream methods ***/
static inline HRESULT IWICPersistStream_IsDirty(IWICPersistStream* This) {
    return This->lpVtbl->IsDirty(This);
}
static inline HRESULT IWICPersistStream_Load(IWICPersistStream* This,IStream *pStm) {
    return This->lpVtbl->Load(This,pStm);
}
static inline HRESULT IWICPersistStream_Save(IWICPersistStream* This,IStream *pStm,WINBOOL fClearDirty) {
    return This->lpVtbl->Save(This,pStm,fClearDirty);
}
static inline HRESULT IWICPersistStream_GetSizeMax(IWICPersistStream* This,ULARGE_INTEGER *pcbSize) {
    return This->lpVtbl->GetSizeMax(This,pcbSize);
}
/*** IWICPersistStream methods ***/
static inline HRESULT IWICPersistStream_LoadEx(IWICPersistStream* This,IStream *pIStream,const GUID *pguidPreferredVendor,DWORD dwPersistOptions) {
    return This->lpVtbl->LoadEx(This,pIStream,pguidPreferredVendor,dwPersistOptions);
}
static inline HRESULT IWICPersistStream_SaveEx(IWICPersistStream* This,IStream *pIStream,DWORD dwPersistOptions,WINBOOL fClearDirty) {
    return This->lpVtbl->SaveEx(This,pIStream,dwPersistOptions,fClearDirty);
}
#endif
#endif

#endif


#endif  /* __IWICPersistStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICStreamProvider interface
 */
#ifndef __IWICStreamProvider_INTERFACE_DEFINED__
#define __IWICStreamProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICStreamProvider, 0x449494bc, 0xb468, 0x4927, 0x96,0xd7, 0xba,0x90,0xd3,0x1a,0xb5,0x05);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("449494bc-b468-4927-96d7-ba90d31ab505")
IWICStreamProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStream(
        IStream **stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPersistOptions(
        DWORD *options) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreferredVendorGUID(
        GUID *guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE RefreshStream(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICStreamProvider, 0x449494bc, 0xb468, 0x4927, 0x96,0xd7, 0xba,0x90,0xd3,0x1a,0xb5,0x05)
#endif
#else
typedef struct IWICStreamProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICStreamProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICStreamProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICStreamProvider *This);

    /*** IWICStreamProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IWICStreamProvider *This,
        IStream **stream);

    HRESULT (STDMETHODCALLTYPE *GetPersistOptions)(
        IWICStreamProvider *This,
        DWORD *options);

    HRESULT (STDMETHODCALLTYPE *GetPreferredVendorGUID)(
        IWICStreamProvider *This,
        GUID *guid);

    HRESULT (STDMETHODCALLTYPE *RefreshStream)(
        IWICStreamProvider *This);

    END_INTERFACE
} IWICStreamProviderVtbl;

interface IWICStreamProvider {
    CONST_VTBL IWICStreamProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICStreamProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICStreamProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICStreamProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IWICStreamProvider methods ***/
#define IWICStreamProvider_GetStream(This,stream) (This)->lpVtbl->GetStream(This,stream)
#define IWICStreamProvider_GetPersistOptions(This,options) (This)->lpVtbl->GetPersistOptions(This,options)
#define IWICStreamProvider_GetPreferredVendorGUID(This,guid) (This)->lpVtbl->GetPreferredVendorGUID(This,guid)
#define IWICStreamProvider_RefreshStream(This) (This)->lpVtbl->RefreshStream(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICStreamProvider_QueryInterface(IWICStreamProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICStreamProvider_AddRef(IWICStreamProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICStreamProvider_Release(IWICStreamProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICStreamProvider methods ***/
static inline HRESULT IWICStreamProvider_GetStream(IWICStreamProvider* This,IStream **stream) {
    return This->lpVtbl->GetStream(This,stream);
}
static inline HRESULT IWICStreamProvider_GetPersistOptions(IWICStreamProvider* This,DWORD *options) {
    return This->lpVtbl->GetPersistOptions(This,options);
}
static inline HRESULT IWICStreamProvider_GetPreferredVendorGUID(IWICStreamProvider* This,GUID *guid) {
    return This->lpVtbl->GetPreferredVendorGUID(This,guid);
}
static inline HRESULT IWICStreamProvider_RefreshStream(IWICStreamProvider* This) {
    return This->lpVtbl->RefreshStream(This);
}
#endif
#endif

#endif


#endif  /* __IWICStreamProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWICComponentFactory interface
 */
#ifndef __IWICComponentFactory_INTERFACE_DEFINED__
#define __IWICComponentFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWICComponentFactory, 0x412d0c3a, 0x9650, 0x44fa, 0xaf,0x5b, 0xdd,0x2a,0x06,0xc8,0xe8,0xfb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("412d0c3a-9650-44fa-af5b-dd2a06c8e8fb")
IWICComponentFactory : public IWICImagingFactory
{
    virtual HRESULT STDMETHODCALLTYPE CreateMetadataReader(
        REFGUID guidMetadataFormat,
        const GUID *pguidVendor,
        DWORD dwOptions,
        IStream *pIStream,
        IWICMetadataReader **ppIReader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateMetadataReaderFromContainer(
        REFGUID guidMetadataFormat,
        const GUID *pguidVendor,
        DWORD dwOptions,
        IStream *pIStream,
        IWICMetadataReader **ppIReader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateMetadataWriter(
        REFGUID guidMetadataFormat,
        const GUID *pguidVendor,
        DWORD dwMetadataOptions,
        IWICMetadataWriter **ppIWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateMetadataWriterFromReader(
        IWICMetadataReader *pIReader,
        const GUID *pguidVendor,
        IWICMetadataWriter **ppIWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateQueryReaderFromBlockReader(
        IWICMetadataBlockReader *pIBlockReader,
        IWICMetadataQueryReader **ppIQueryReader) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateQueryWriterFromBlockWriter(
        IWICMetadataBlockWriter *pIBlockWriter,
        IWICMetadataQueryWriter **ppIQueryWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateEncoderPropertyBag(
        PROPBAG2 *ppropOptions,
        UINT cCount,
        IPropertyBag2 **ppIPropertyBag) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWICComponentFactory, 0x412d0c3a, 0x9650, 0x44fa, 0xaf,0x5b, 0xdd,0x2a,0x06,0xc8,0xe8,0xfb)
#endif
#else
typedef struct IWICComponentFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWICComponentFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWICComponentFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWICComponentFactory *This);

    /*** IWICImagingFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateDecoderFromFilename)(
        IWICComponentFactory *This,
        LPCWSTR wzFilename,
        const GUID *pguidVendor,
        DWORD dwDesiredAccess,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateDecoderFromStream)(
        IWICComponentFactory *This,
        IStream *pIStream,
        const GUID *pguidVendor,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateDecoderFromFileHandle)(
        IWICComponentFactory *This,
        ULONG_PTR hFile,
        const GUID *pguidVendor,
        WICDecodeOptions metadataOptions,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateComponentInfo)(
        IWICComponentFactory *This,
        REFCLSID clsidComponent,
        IWICComponentInfo **ppIInfo);

    HRESULT (STDMETHODCALLTYPE *CreateDecoder)(
        IWICComponentFactory *This,
        REFGUID guidContainerFormat,
        const GUID *pguidVendor,
        IWICBitmapDecoder **ppIDecoder);

    HRESULT (STDMETHODCALLTYPE *CreateEncoder)(
        IWICComponentFactory *This,
        REFGUID guidContainerFormat,
        const GUID *pguidVendor,
        IWICBitmapEncoder **ppIEncoder);

    HRESULT (STDMETHODCALLTYPE *CreatePalette)(
        IWICComponentFactory *This,
        IWICPalette **ppIPalette);

    HRESULT (STDMETHODCALLTYPE *CreateFormatConverter)(
        IWICComponentFactory *This,
        IWICFormatConverter **ppIFormatConverter);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapScaler)(
        IWICComponentFactory *This,
        IWICBitmapScaler **ppIBitmapScaler);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapClipper)(
        IWICComponentFactory *This,
        IWICBitmapClipper **ppIBitmapClipper);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFlipRotator)(
        IWICComponentFactory *This,
        IWICBitmapFlipRotator **ppIBitmapFlipRotator);

    HRESULT (STDMETHODCALLTYPE *CreateStream)(
        IWICComponentFactory *This,
        IWICStream **ppIWICStream);

    HRESULT (STDMETHODCALLTYPE *CreateColorContext)(
        IWICComponentFactory *This,
        IWICColorContext **ppIWICColorContext);

    HRESULT (STDMETHODCALLTYPE *CreateColorTransformer)(
        IWICComponentFactory *This,
        IWICColorTransform **ppIWICColorTransform);

    HRESULT (STDMETHODCALLTYPE *CreateBitmap)(
        IWICComponentFactory *This,
        UINT uiWidth,
        UINT uiHeight,
        REFWICPixelFormatGUID pixelFormat,
        WICBitmapCreateCacheOption option,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromSource)(
        IWICComponentFactory *This,
        IWICBitmapSource *piBitmapSource,
        WICBitmapCreateCacheOption option,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromSourceRect)(
        IWICComponentFactory *This,
        IWICBitmapSource *piBitmapSource,
        UINT x,
        UINT y,
        UINT width,
        UINT height,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromMemory)(
        IWICComponentFactory *This,
        UINT uiWidth,
        UINT uiHeight,
        REFWICPixelFormatGUID pixelFormat,
        UINT cbStride,
        UINT cbBufferSize,
        BYTE *pbBuffer,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromHBITMAP)(
        IWICComponentFactory *This,
        HBITMAP hBitmap,
        HPALETTE hPalette,
        WICBitmapAlphaChannelOption options,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateBitmapFromHICON)(
        IWICComponentFactory *This,
        HICON hIcon,
        IWICBitmap **ppIBitmap);

    HRESULT (STDMETHODCALLTYPE *CreateComponentEnumerator)(
        IWICComponentFactory *This,
        DWORD componentTypes,
        DWORD options,
        IEnumUnknown **ppIEnumUnknown);

    HRESULT (STDMETHODCALLTYPE *CreateFastMetadataEncoderFromDecoder)(
        IWICComponentFactory *This,
        IWICBitmapDecoder *pIDecoder,
        IWICFastMetadataEncoder **ppIFastEncoder);

    HRESULT (STDMETHODCALLTYPE *CreateFastMetadataEncoderFromFrameDecode)(
        IWICComponentFactory *This,
        IWICBitmapFrameDecode *pIFrameDecoder,
        IWICFastMetadataEncoder **ppIFastEncoder);

    HRESULT (STDMETHODCALLTYPE *CreateQueryWriter)(
        IWICComponentFactory *This,
        REFGUID guidMetadataFormat,
        const GUID *pguidVendor,
        IWICMetadataQueryWriter **ppIQueryWriter);

    HRESULT (STDMETHODCALLTYPE *CreateQueryWriterFromReader)(
        IWICComponentFactory *This,
        IWICMetadataQueryReader *pIQueryReader,
        const GUID *pguidVendor,
        IWICMetadataQueryWriter **ppIQueryWriter);

    /*** IWICComponentFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateMetadataReader)(
        IWICComponentFactory *This,
        REFGUID guidMetadataFormat,
        const GUID *pguidVendor,
        DWORD dwOptions,
        IStream *pIStream,
        IWICMetadataReader **ppIReader);

    HRESULT (STDMETHODCALLTYPE *CreateMetadataReaderFromContainer)(
        IWICComponentFactory *This,
        REFGUID guidMetadataFormat,
        const GUID *pguidVendor,
        DWORD dwOptions,
        IStream *pIStream,
        IWICMetadataReader **ppIReader);

    HRESULT (STDMETHODCALLTYPE *CreateMetadataWriter)(
        IWICComponentFactory *This,
        REFGUID guidMetadataFormat,
        const GUID *pguidVendor,
        DWORD dwMetadataOptions,
        IWICMetadataWriter **ppIWriter);

    HRESULT (STDMETHODCALLTYPE *CreateMetadataWriterFromReader)(
        IWICComponentFactory *This,
        IWICMetadataReader *pIReader,
        const GUID *pguidVendor,
        IWICMetadataWriter **ppIWriter);

    HRESULT (STDMETHODCALLTYPE *CreateQueryReaderFromBlockReader)(
        IWICComponentFactory *This,
        IWICMetadataBlockReader *pIBlockReader,
        IWICMetadataQueryReader **ppIQueryReader);

    HRESULT (STDMETHODCALLTYPE *CreateQueryWriterFromBlockWriter)(
        IWICComponentFactory *This,
        IWICMetadataBlockWriter *pIBlockWriter,
        IWICMetadataQueryWriter **ppIQueryWriter);

    HRESULT (STDMETHODCALLTYPE *CreateEncoderPropertyBag)(
        IWICComponentFactory *This,
        PROPBAG2 *ppropOptions,
        UINT cCount,
        IPropertyBag2 **ppIPropertyBag);

    END_INTERFACE
} IWICComponentFactoryVtbl;

interface IWICComponentFactory {
    CONST_VTBL IWICComponentFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWICComponentFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWICComponentFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWICComponentFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IWICImagingFactory methods ***/
#define IWICComponentFactory_CreateDecoderFromFilename(This,wzFilename,pguidVendor,dwDesiredAccess,metadataOptions,ppIDecoder) (This)->lpVtbl->CreateDecoderFromFilename(This,wzFilename,pguidVendor,dwDesiredAccess,metadataOptions,ppIDecoder)
#define IWICComponentFactory_CreateDecoderFromStream(This,pIStream,pguidVendor,metadataOptions,ppIDecoder) (This)->lpVtbl->CreateDecoderFromStream(This,pIStream,pguidVendor,metadataOptions,ppIDecoder)
#define IWICComponentFactory_CreateDecoderFromFileHandle(This,hFile,pguidVendor,metadataOptions,ppIDecoder) (This)->lpVtbl->CreateDecoderFromFileHandle(This,hFile,pguidVendor,metadataOptions,ppIDecoder)
#define IWICComponentFactory_CreateComponentInfo(This,clsidComponent,ppIInfo) (This)->lpVtbl->CreateComponentInfo(This,clsidComponent,ppIInfo)
#define IWICComponentFactory_CreateDecoder(This,guidContainerFormat,pguidVendor,ppIDecoder) (This)->lpVtbl->CreateDecoder(This,guidContainerFormat,pguidVendor,ppIDecoder)
#define IWICComponentFactory_CreateEncoder(This,guidContainerFormat,pguidVendor,ppIEncoder) (This)->lpVtbl->CreateEncoder(This,guidContainerFormat,pguidVendor,ppIEncoder)
#define IWICComponentFactory_CreatePalette(This,ppIPalette) (This)->lpVtbl->CreatePalette(This,ppIPalette)
#define IWICComponentFactory_CreateFormatConverter(This,ppIFormatConverter) (This)->lpVtbl->CreateFormatConverter(This,ppIFormatConverter)
#define IWICComponentFactory_CreateBitmapScaler(This,ppIBitmapScaler) (This)->lpVtbl->CreateBitmapScaler(This,ppIBitmapScaler)
#define IWICComponentFactory_CreateBitmapClipper(This,ppIBitmapClipper) (This)->lpVtbl->CreateBitmapClipper(This,ppIBitmapClipper)
#define IWICComponentFactory_CreateBitmapFlipRotator(This,ppIBitmapFlipRotator) (This)->lpVtbl->CreateBitmapFlipRotator(This,ppIBitmapFlipRotator)
#define IWICComponentFactory_CreateStream(This,ppIWICStream) (This)->lpVtbl->CreateStream(This,ppIWICStream)
#define IWICComponentFactory_CreateColorContext(This,ppIWICColorContext) (This)->lpVtbl->CreateColorContext(This,ppIWICColorContext)
#define IWICComponentFactory_CreateColorTransformer(This,ppIWICColorTransform) (This)->lpVtbl->CreateColorTransformer(This,ppIWICColorTransform)
#define IWICComponentFactory_CreateBitmap(This,uiWidth,uiHeight,pixelFormat,option,ppIBitmap) (This)->lpVtbl->CreateBitmap(This,uiWidth,uiHeight,pixelFormat,option,ppIBitmap)
#define IWICComponentFactory_CreateBitmapFromSource(This,piBitmapSource,option,ppIBitmap) (This)->lpVtbl->CreateBitmapFromSource(This,piBitmapSource,option,ppIBitmap)
#define IWICComponentFactory_CreateBitmapFromSourceRect(This,piBitmapSource,x,y,width,height,ppIBitmap) (This)->lpVtbl->CreateBitmapFromSourceRect(This,piBitmapSource,x,y,width,height,ppIBitmap)
#define IWICComponentFactory_CreateBitmapFromMemory(This,uiWidth,uiHeight,pixelFormat,cbStride,cbBufferSize,pbBuffer,ppIBitmap) (This)->lpVtbl->CreateBitmapFromMemory(This,uiWidth,uiHeight,pixelFormat,cbStride,cbBufferSize,pbBuffer,ppIBitmap)
#define IWICComponentFactory_CreateBitmapFromHBITMAP(This,hBitmap,hPalette,options,ppIBitmap) (This)->lpVtbl->CreateBitmapFromHBITMAP(This,hBitmap,hPalette,options,ppIBitmap)
#define IWICComponentFactory_CreateBitmapFromHICON(This,hIcon,ppIBitmap) (This)->lpVtbl->CreateBitmapFromHICON(This,hIcon,ppIBitmap)
#define IWICComponentFactory_CreateComponentEnumerator(This,componentTypes,options,ppIEnumUnknown) (This)->lpVtbl->CreateComponentEnumerator(This,componentTypes,options,ppIEnumUnknown)
#define IWICComponentFactory_CreateFastMetadataEncoderFromDecoder(This,pIDecoder,ppIFastEncoder) (This)->lpVtbl->CreateFastMetadataEncoderFromDecoder(This,pIDecoder,ppIFastEncoder)
#define IWICComponentFactory_CreateFastMetadataEncoderFromFrameDecode(This,pIFrameDecoder,ppIFastEncoder) (This)->lpVtbl->CreateFastMetadataEncoderFromFrameDecode(This,pIFrameDecoder,ppIFastEncoder)
#define IWICComponentFactory_CreateQueryWriter(This,guidMetadataFormat,pguidVendor,ppIQueryWriter) (This)->lpVtbl->CreateQueryWriter(This,guidMetadataFormat,pguidVendor,ppIQueryWriter)
#define IWICComponentFactory_CreateQueryWriterFromReader(This,pIQueryReader,pguidVendor,ppIQueryWriter) (This)->lpVtbl->CreateQueryWriterFromReader(This,pIQueryReader,pguidVendor,ppIQueryWriter)
/*** IWICComponentFactory methods ***/
#define IWICComponentFactory_CreateMetadataReader(This,guidMetadataFormat,pguidVendor,dwOptions,pIStream,ppIReader) (This)->lpVtbl->CreateMetadataReader(This,guidMetadataFormat,pguidVendor,dwOptions,pIStream,ppIReader)
#define IWICComponentFactory_CreateMetadataReaderFromContainer(This,guidMetadataFormat,pguidVendor,dwOptions,pIStream,ppIReader) (This)->lpVtbl->CreateMetadataReaderFromContainer(This,guidMetadataFormat,pguidVendor,dwOptions,pIStream,ppIReader)
#define IWICComponentFactory_CreateMetadataWriter(This,guidMetadataFormat,pguidVendor,dwMetadataOptions,ppIWriter) (This)->lpVtbl->CreateMetadataWriter(This,guidMetadataFormat,pguidVendor,dwMetadataOptions,ppIWriter)
#define IWICComponentFactory_CreateMetadataWriterFromReader(This,pIReader,pguidVendor,ppIWriter) (This)->lpVtbl->CreateMetadataWriterFromReader(This,pIReader,pguidVendor,ppIWriter)
#define IWICComponentFactory_CreateQueryReaderFromBlockReader(This,pIBlockReader,ppIQueryReader) (This)->lpVtbl->CreateQueryReaderFromBlockReader(This,pIBlockReader,ppIQueryReader)
#define IWICComponentFactory_CreateQueryWriterFromBlockWriter(This,pIBlockWriter,ppIQueryWriter) (This)->lpVtbl->CreateQueryWriterFromBlockWriter(This,pIBlockWriter,ppIQueryWriter)
#define IWICComponentFactory_CreateEncoderPropertyBag(This,ppropOptions,cCount,ppIPropertyBag) (This)->lpVtbl->CreateEncoderPropertyBag(This,ppropOptions,cCount,ppIPropertyBag)
#else
/*** IUnknown methods ***/
static inline HRESULT IWICComponentFactory_QueryInterface(IWICComponentFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWICComponentFactory_AddRef(IWICComponentFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWICComponentFactory_Release(IWICComponentFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IWICImagingFactory methods ***/
static inline HRESULT IWICComponentFactory_CreateDecoderFromFilename(IWICComponentFactory* This,LPCWSTR wzFilename,const GUID *pguidVendor,DWORD dwDesiredAccess,WICDecodeOptions metadataOptions,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoderFromFilename(This,wzFilename,pguidVendor,dwDesiredAccess,metadataOptions,ppIDecoder);
}
static inline HRESULT IWICComponentFactory_CreateDecoderFromStream(IWICComponentFactory* This,IStream *pIStream,const GUID *pguidVendor,WICDecodeOptions metadataOptions,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoderFromStream(This,pIStream,pguidVendor,metadataOptions,ppIDecoder);
}
static inline HRESULT IWICComponentFactory_CreateDecoderFromFileHandle(IWICComponentFactory* This,ULONG_PTR hFile,const GUID *pguidVendor,WICDecodeOptions metadataOptions,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoderFromFileHandle(This,hFile,pguidVendor,metadataOptions,ppIDecoder);
}
static inline HRESULT IWICComponentFactory_CreateComponentInfo(IWICComponentFactory* This,REFCLSID clsidComponent,IWICComponentInfo **ppIInfo) {
    return This->lpVtbl->CreateComponentInfo(This,clsidComponent,ppIInfo);
}
static inline HRESULT IWICComponentFactory_CreateDecoder(IWICComponentFactory* This,REFGUID guidContainerFormat,const GUID *pguidVendor,IWICBitmapDecoder **ppIDecoder) {
    return This->lpVtbl->CreateDecoder(This,guidContainerFormat,pguidVendor,ppIDecoder);
}
static inline HRESULT IWICComponentFactory_CreateEncoder(IWICComponentFactory* This,REFGUID guidContainerFormat,const GUID *pguidVendor,IWICBitmapEncoder **ppIEncoder) {
    return This->lpVtbl->CreateEncoder(This,guidContainerFormat,pguidVendor,ppIEncoder);
}
static inline HRESULT IWICComponentFactory_CreatePalette(IWICComponentFactory* This,IWICPalette **ppIPalette) {
    return This->lpVtbl->CreatePalette(This,ppIPalette);
}
static inline HRESULT IWICComponentFactory_CreateFormatConverter(IWICComponentFactory* This,IWICFormatConverter **ppIFormatConverter) {
    return This->lpVtbl->CreateFormatConverter(This,ppIFormatConverter);
}
static inline HRESULT IWICComponentFactory_CreateBitmapScaler(IWICComponentFactory* This,IWICBitmapScaler **ppIBitmapScaler) {
    return This->lpVtbl->CreateBitmapScaler(This,ppIBitmapScaler);
}
static inline HRESULT IWICComponentFactory_CreateBitmapClipper(IWICComponentFactory* This,IWICBitmapClipper **ppIBitmapClipper) {
    return This->lpVtbl->CreateBitmapClipper(This,ppIBitmapClipper);
}
static inline HRESULT IWICComponentFactory_CreateBitmapFlipRotator(IWICComponentFactory* This,IWICBitmapFlipRotator **ppIBitmapFlipRotator) {
    return This->lpVtbl->CreateBitmapFlipRotator(This,ppIBitmapFlipRotator);
}
static inline HRESULT IWICComponentFactory_CreateStream(IWICComponentFactory* This,IWICStream **ppIWICStream) {
    return This->lpVtbl->CreateStream(This,ppIWICStream);
}
static inline HRESULT IWICComponentFactory_CreateColorContext(IWICComponentFactory* This,IWICColorContext **ppIWICColorContext) {
    return This->lpVtbl->CreateColorContext(This,ppIWICColorContext);
}
static inline HRESULT IWICComponentFactory_CreateColorTransformer(IWICComponentFactory* This,IWICColorTransform **ppIWICColorTransform) {
    return This->lpVtbl->CreateColorTransformer(This,ppIWICColorTransform);
}
static inline HRESULT IWICComponentFactory_CreateBitmap(IWICComponentFactory* This,UINT uiWidth,UINT uiHeight,REFWICPixelFormatGUID pixelFormat,WICBitmapCreateCacheOption option,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmap(This,uiWidth,uiHeight,pixelFormat,option,ppIBitmap);
}
static inline HRESULT IWICComponentFactory_CreateBitmapFromSource(IWICComponentFactory* This,IWICBitmapSource *piBitmapSource,WICBitmapCreateCacheOption option,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromSource(This,piBitmapSource,option,ppIBitmap);
}
static inline HRESULT IWICComponentFactory_CreateBitmapFromSourceRect(IWICComponentFactory* This,IWICBitmapSource *piBitmapSource,UINT x,UINT y,UINT width,UINT height,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromSourceRect(This,piBitmapSource,x,y,width,height,ppIBitmap);
}
static inline HRESULT IWICComponentFactory_CreateBitmapFromMemory(IWICComponentFactory* This,UINT uiWidth,UINT uiHeight,REFWICPixelFormatGUID pixelFormat,UINT cbStride,UINT cbBufferSize,BYTE *pbBuffer,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromMemory(This,uiWidth,uiHeight,pixelFormat,cbStride,cbBufferSize,pbBuffer,ppIBitmap);
}
static inline HRESULT IWICComponentFactory_CreateBitmapFromHBITMAP(IWICComponentFactory* This,HBITMAP hBitmap,HPALETTE hPalette,WICBitmapAlphaChannelOption options,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromHBITMAP(This,hBitmap,hPalette,options,ppIBitmap);
}
static inline HRESULT IWICComponentFactory_CreateBitmapFromHICON(IWICComponentFactory* This,HICON hIcon,IWICBitmap **ppIBitmap) {
    return This->lpVtbl->CreateBitmapFromHICON(This,hIcon,ppIBitmap);
}
static inline HRESULT IWICComponentFactory_CreateComponentEnumerator(IWICComponentFactory* This,DWORD componentTypes,DWORD options,IEnumUnknown **ppIEnumUnknown) {
    return This->lpVtbl->CreateComponentEnumerator(This,componentTypes,options,ppIEnumUnknown);
}
static inline HRESULT IWICComponentFactory_CreateFastMetadataEncoderFromDecoder(IWICComponentFactory* This,IWICBitmapDecoder *pIDecoder,IWICFastMetadataEncoder **ppIFastEncoder) {
    return This->lpVtbl->CreateFastMetadataEncoderFromDecoder(This,pIDecoder,ppIFastEncoder);
}
static inline HRESULT IWICComponentFactory_CreateFastMetadataEncoderFromFrameDecode(IWICComponentFactory* This,IWICBitmapFrameDecode *pIFrameDecoder,IWICFastMetadataEncoder **ppIFastEncoder) {
    return This->lpVtbl->CreateFastMetadataEncoderFromFrameDecode(This,pIFrameDecoder,ppIFastEncoder);
}
static inline HRESULT IWICComponentFactory_CreateQueryWriter(IWICComponentFactory* This,REFGUID guidMetadataFormat,const GUID *pguidVendor,IWICMetadataQueryWriter **ppIQueryWriter) {
    return This->lpVtbl->CreateQueryWriter(This,guidMetadataFormat,pguidVendor,ppIQueryWriter);
}
static inline HRESULT IWICComponentFactory_CreateQueryWriterFromReader(IWICComponentFactory* This,IWICMetadataQueryReader *pIQueryReader,const GUID *pguidVendor,IWICMetadataQueryWriter **ppIQueryWriter) {
    return This->lpVtbl->CreateQueryWriterFromReader(This,pIQueryReader,pguidVendor,ppIQueryWriter);
}
/*** IWICComponentFactory methods ***/
static inline HRESULT IWICComponentFactory_CreateMetadataReader(IWICComponentFactory* This,REFGUID guidMetadataFormat,const GUID *pguidVendor,DWORD dwOptions,IStream *pIStream,IWICMetadataReader **ppIReader) {
    return This->lpVtbl->CreateMetadataReader(This,guidMetadataFormat,pguidVendor,dwOptions,pIStream,ppIReader);
}
static inline HRESULT IWICComponentFactory_CreateMetadataReaderFromContainer(IWICComponentFactory* This,REFGUID guidMetadataFormat,const GUID *pguidVendor,DWORD dwOptions,IStream *pIStream,IWICMetadataReader **ppIReader) {
    return This->lpVtbl->CreateMetadataReaderFromContainer(This,guidMetadataFormat,pguidVendor,dwOptions,pIStream,ppIReader);
}
static inline HRESULT IWICComponentFactory_CreateMetadataWriter(IWICComponentFactory* This,REFGUID guidMetadataFormat,const GUID *pguidVendor,DWORD dwMetadataOptions,IWICMetadataWriter **ppIWriter) {
    return This->lpVtbl->CreateMetadataWriter(This,guidMetadataFormat,pguidVendor,dwMetadataOptions,ppIWriter);
}
static inline HRESULT IWICComponentFactory_CreateMetadataWriterFromReader(IWICComponentFactory* This,IWICMetadataReader *pIReader,const GUID *pguidVendor,IWICMetadataWriter **ppIWriter) {
    return This->lpVtbl->CreateMetadataWriterFromReader(This,pIReader,pguidVendor,ppIWriter);
}
static inline HRESULT IWICComponentFactory_CreateQueryReaderFromBlockReader(IWICComponentFactory* This,IWICMetadataBlockReader *pIBlockReader,IWICMetadataQueryReader **ppIQueryReader) {
    return This->lpVtbl->CreateQueryReaderFromBlockReader(This,pIBlockReader,ppIQueryReader);
}
static inline HRESULT IWICComponentFactory_CreateQueryWriterFromBlockWriter(IWICComponentFactory* This,IWICMetadataBlockWriter *pIBlockWriter,IWICMetadataQueryWriter **ppIQueryWriter) {
    return This->lpVtbl->CreateQueryWriterFromBlockWriter(This,pIBlockWriter,ppIQueryWriter);
}
static inline HRESULT IWICComponentFactory_CreateEncoderPropertyBag(IWICComponentFactory* This,PROPBAG2 *ppropOptions,UINT cCount,IPropertyBag2 **ppIPropertyBag) {
    return This->lpVtbl->CreateEncoderPropertyBag(This,ppropOptions,cCount,ppIPropertyBag);
}
#endif
#endif

#endif


#endif  /* __IWICComponentFactory_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER LPSAFEARRAY_UserSize     (ULONG *, ULONG, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserMarshal  (ULONG *, unsigned char *, LPSAFEARRAY *);
unsigned char * __RPC_USER LPSAFEARRAY_UserUnmarshal(ULONG *, unsigned char *, LPSAFEARRAY *);
void            __RPC_USER LPSAFEARRAY_UserFree     (ULONG *, LPSAFEARRAY *);
ULONG           __RPC_USER CLIPFORMAT_UserSize     (ULONG *, ULONG, CLIPFORMAT *);
unsigned char * __RPC_USER CLIPFORMAT_UserMarshal  (ULONG *, unsigned char *, CLIPFORMAT *);
unsigned char * __RPC_USER CLIPFORMAT_UserUnmarshal(ULONG *, unsigned char *, CLIPFORMAT *);
void            __RPC_USER CLIPFORMAT_UserFree     (ULONG *, CLIPFORMAT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wincodecsdk_h__ */
