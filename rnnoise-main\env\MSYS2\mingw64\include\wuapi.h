/*** Autogenerated by WIDL 10.12 from include/wuapi.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wuapi_h__
#define __wuapi_h__

/* Forward declarations */

#ifndef __IAutomaticUpdatesSettings_FWD_DEFINED__
#define __IAutomaticUpdatesSettings_FWD_DEFINED__
typedef interface IAutomaticUpdatesSettings IAutomaticUpdatesSettings;
#ifdef __cplusplus
interface IAutomaticUpdatesSettings;
#endif /* __cplusplus */
#endif

#ifndef __IAutomaticUpdates_FWD_DEFINED__
#define __IAutomaticUpdates_FWD_DEFINED__
typedef interface IAutomaticUpdates IAutomaticUpdates;
#ifdef __cplusplus
interface IAutomaticUpdates;
#endif /* __cplusplus */
#endif

#ifndef __IWebProxy_FWD_DEFINED__
#define __IWebProxy_FWD_DEFINED__
typedef interface IWebProxy IWebProxy;
#ifdef __cplusplus
interface IWebProxy;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateSession_FWD_DEFINED__
#define __IUpdateSession_FWD_DEFINED__
typedef interface IUpdateSession IUpdateSession;
#ifdef __cplusplus
interface IUpdateSession;
#endif /* __cplusplus */
#endif

#ifndef __IImageInformation_FWD_DEFINED__
#define __IImageInformation_FWD_DEFINED__
typedef interface IImageInformation IImageInformation;
#ifdef __cplusplus
interface IImageInformation;
#endif /* __cplusplus */
#endif

#ifndef __ICategory_FWD_DEFINED__
#define __ICategory_FWD_DEFINED__
typedef interface ICategory ICategory;
#ifdef __cplusplus
interface ICategory;
#endif /* __cplusplus */
#endif

#ifndef __ICategoryCollection_FWD_DEFINED__
#define __ICategoryCollection_FWD_DEFINED__
typedef interface ICategoryCollection ICategoryCollection;
#ifdef __cplusplus
interface ICategoryCollection;
#endif /* __cplusplus */
#endif

#ifndef __IStringCollection_FWD_DEFINED__
#define __IStringCollection_FWD_DEFINED__
typedef interface IStringCollection IStringCollection;
#ifdef __cplusplus
interface IStringCollection;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateException_FWD_DEFINED__
#define __IUpdateException_FWD_DEFINED__
typedef interface IUpdateException IUpdateException;
#ifdef __cplusplus
interface IUpdateException;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateExceptionCollection_FWD_DEFINED__
#define __IUpdateExceptionCollection_FWD_DEFINED__
typedef interface IUpdateExceptionCollection IUpdateExceptionCollection;
#ifdef __cplusplus
interface IUpdateExceptionCollection;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateIdentity_FWD_DEFINED__
#define __IUpdateIdentity_FWD_DEFINED__
typedef interface IUpdateIdentity IUpdateIdentity;
#ifdef __cplusplus
interface IUpdateIdentity;
#endif /* __cplusplus */
#endif

#ifndef __IInstallationBehavior_FWD_DEFINED__
#define __IInstallationBehavior_FWD_DEFINED__
typedef interface IInstallationBehavior IInstallationBehavior;
#ifdef __cplusplus
interface IInstallationBehavior;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateDownloadContent_FWD_DEFINED__
#define __IUpdateDownloadContent_FWD_DEFINED__
typedef interface IUpdateDownloadContent IUpdateDownloadContent;
#ifdef __cplusplus
interface IUpdateDownloadContent;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateDownloadContentCollection_FWD_DEFINED__
#define __IUpdateDownloadContentCollection_FWD_DEFINED__
typedef interface IUpdateDownloadContentCollection IUpdateDownloadContentCollection;
#ifdef __cplusplus
interface IUpdateDownloadContentCollection;
#endif /* __cplusplus */
#endif

#ifndef __IUpdate_FWD_DEFINED__
#define __IUpdate_FWD_DEFINED__
typedef interface IUpdate IUpdate;
#ifdef __cplusplus
interface IUpdate;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateCollection_FWD_DEFINED__
#define __IUpdateCollection_FWD_DEFINED__
typedef interface IUpdateCollection IUpdateCollection;
#ifdef __cplusplus
interface IUpdateCollection;
#endif /* __cplusplus */
#endif

#ifndef __ISearchJob_FWD_DEFINED__
#define __ISearchJob_FWD_DEFINED__
typedef interface ISearchJob ISearchJob;
#ifdef __cplusplus
interface ISearchJob;
#endif /* __cplusplus */
#endif

#ifndef __ISearchResult_FWD_DEFINED__
#define __ISearchResult_FWD_DEFINED__
typedef interface ISearchResult ISearchResult;
#ifdef __cplusplus
interface ISearchResult;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateHistoryEntry_FWD_DEFINED__
#define __IUpdateHistoryEntry_FWD_DEFINED__
typedef interface IUpdateHistoryEntry IUpdateHistoryEntry;
#ifdef __cplusplus
interface IUpdateHistoryEntry;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateHistoryEntryCollection_FWD_DEFINED__
#define __IUpdateHistoryEntryCollection_FWD_DEFINED__
typedef interface IUpdateHistoryEntryCollection IUpdateHistoryEntryCollection;
#ifdef __cplusplus
interface IUpdateHistoryEntryCollection;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateSearcher_FWD_DEFINED__
#define __IUpdateSearcher_FWD_DEFINED__
typedef interface IUpdateSearcher IUpdateSearcher;
#ifdef __cplusplus
interface IUpdateSearcher;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateDownloadResult_FWD_DEFINED__
#define __IUpdateDownloadResult_FWD_DEFINED__
typedef interface IUpdateDownloadResult IUpdateDownloadResult;
#ifdef __cplusplus
interface IUpdateDownloadResult;
#endif /* __cplusplus */
#endif

#ifndef __IDownloadProgress_FWD_DEFINED__
#define __IDownloadProgress_FWD_DEFINED__
typedef interface IDownloadProgress IDownloadProgress;
#ifdef __cplusplus
interface IDownloadProgress;
#endif /* __cplusplus */
#endif

#ifndef __IDownloadJob_FWD_DEFINED__
#define __IDownloadJob_FWD_DEFINED__
typedef interface IDownloadJob IDownloadJob;
#ifdef __cplusplus
interface IDownloadJob;
#endif /* __cplusplus */
#endif

#ifndef __IDownloadResult_FWD_DEFINED__
#define __IDownloadResult_FWD_DEFINED__
typedef interface IDownloadResult IDownloadResult;
#ifdef __cplusplus
interface IDownloadResult;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateDownloader_FWD_DEFINED__
#define __IUpdateDownloader_FWD_DEFINED__
typedef interface IUpdateDownloader IUpdateDownloader;
#ifdef __cplusplus
interface IUpdateDownloader;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateInstallationResult_FWD_DEFINED__
#define __IUpdateInstallationResult_FWD_DEFINED__
typedef interface IUpdateInstallationResult IUpdateInstallationResult;
#ifdef __cplusplus
interface IUpdateInstallationResult;
#endif /* __cplusplus */
#endif

#ifndef __IInstallationProgress_FWD_DEFINED__
#define __IInstallationProgress_FWD_DEFINED__
typedef interface IInstallationProgress IInstallationProgress;
#ifdef __cplusplus
interface IInstallationProgress;
#endif /* __cplusplus */
#endif

#ifndef __IInstallationJob_FWD_DEFINED__
#define __IInstallationJob_FWD_DEFINED__
typedef interface IInstallationJob IInstallationJob;
#ifdef __cplusplus
interface IInstallationJob;
#endif /* __cplusplus */
#endif

#ifndef __IInstallationResult_FWD_DEFINED__
#define __IInstallationResult_FWD_DEFINED__
typedef interface IInstallationResult IInstallationResult;
#ifdef __cplusplus
interface IInstallationResult;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateInstaller_FWD_DEFINED__
#define __IUpdateInstaller_FWD_DEFINED__
typedef interface IUpdateInstaller IUpdateInstaller;
#ifdef __cplusplus
interface IUpdateInstaller;
#endif /* __cplusplus */
#endif

#ifndef __ISystemInformation_FWD_DEFINED__
#define __ISystemInformation_FWD_DEFINED__
typedef interface ISystemInformation ISystemInformation;
#ifdef __cplusplus
interface ISystemInformation;
#endif /* __cplusplus */
#endif

#ifndef __IWindowsUpdateAgentInfo_FWD_DEFINED__
#define __IWindowsUpdateAgentInfo_FWD_DEFINED__
typedef interface IWindowsUpdateAgentInfo IWindowsUpdateAgentInfo;
#ifdef __cplusplus
interface IWindowsUpdateAgentInfo;
#endif /* __cplusplus */
#endif

#ifndef __AutomaticUpdates_FWD_DEFINED__
#define __AutomaticUpdates_FWD_DEFINED__
#ifdef __cplusplus
typedef class AutomaticUpdates AutomaticUpdates;
#else
typedef struct AutomaticUpdates AutomaticUpdates;
#endif /* defined __cplusplus */
#endif /* defined __AutomaticUpdates_FWD_DEFINED__ */

#ifndef __UpdateInstaller_FWD_DEFINED__
#define __UpdateInstaller_FWD_DEFINED__
#ifdef __cplusplus
typedef class UpdateInstaller UpdateInstaller;
#else
typedef struct UpdateInstaller UpdateInstaller;
#endif /* defined __cplusplus */
#endif /* defined __UpdateInstaller_FWD_DEFINED__ */

#ifndef __UpdateSession_FWD_DEFINED__
#define __UpdateSession_FWD_DEFINED__
#ifdef __cplusplus
typedef class UpdateSession UpdateSession;
#else
typedef struct UpdateSession UpdateSession;
#endif /* defined __cplusplus */
#endif /* defined __UpdateSession_FWD_DEFINED__ */

#ifndef __SystemInformation_FWD_DEFINED__
#define __SystemInformation_FWD_DEFINED__
#ifdef __cplusplus
typedef class SystemInformation SystemInformation;
#else
typedef struct SystemInformation SystemInformation;
#endif /* defined __cplusplus */
#endif /* defined __SystemInformation_FWD_DEFINED__ */

#ifndef __WindowsUpdateAgentInfo_FWD_DEFINED__
#define __WindowsUpdateAgentInfo_FWD_DEFINED__
#ifdef __cplusplus
typedef class WindowsUpdateAgentInfo WindowsUpdateAgentInfo;
#else
typedef struct WindowsUpdateAgentInfo WindowsUpdateAgentInfo;
#endif /* defined __cplusplus */
#endif /* defined __WindowsUpdateAgentInfo_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __WUApiLib_LIBRARY_DEFINED__
#define __WUApiLib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_WUApiLib, 0xb596cc9f, 0x56e5, 0x419e, 0xa6,0x22, 0xe0,0x1b,0xb4,0x57,0x43,0x1e);

#ifndef __ICategoryCollection_FWD_DEFINED__
#define __ICategoryCollection_FWD_DEFINED__
typedef interface ICategoryCollection ICategoryCollection;
#ifdef __cplusplus
interface ICategoryCollection;
#endif /* __cplusplus */
#endif

#ifndef __IStringCollection_FWD_DEFINED__
#define __IStringCollection_FWD_DEFINED__
typedef interface IStringCollection IStringCollection;
#ifdef __cplusplus
interface IStringCollection;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateCollection_FWD_DEFINED__
#define __IUpdateCollection_FWD_DEFINED__
typedef interface IUpdateCollection IUpdateCollection;
#ifdef __cplusplus
interface IUpdateCollection;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateDownloader_FWD_DEFINED__
#define __IUpdateDownloader_FWD_DEFINED__
typedef interface IUpdateDownloader IUpdateDownloader;
#ifdef __cplusplus
interface IUpdateDownloader;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateInstaller_FWD_DEFINED__
#define __IUpdateInstaller_FWD_DEFINED__
typedef interface IUpdateInstaller IUpdateInstaller;
#ifdef __cplusplus
interface IUpdateInstaller;
#endif /* __cplusplus */
#endif

#ifndef __IUpdateSearcher_FWD_DEFINED__
#define __IUpdateSearcher_FWD_DEFINED__
typedef interface IUpdateSearcher IUpdateSearcher;
#ifdef __cplusplus
interface IUpdateSearcher;
#endif /* __cplusplus */
#endif

typedef enum tagDownloadPriority {
    dpLow = 1,
    dpNormal = 2,
    dpHigh = 3
} DownloadPriority;
typedef enum tagServerSelection {
    ssDefault = 0,
    ssManagedServer = 1,
    ssWindowsUpdate = 2,
    ssOthers = 3
} ServerSelection;
typedef enum tagAutomaticUpdatesNotificationLevel {
    aunlNotConfigured = 0,
    aunlDisabled = 1,
    aunlNotifyBeforeDownload = 2,
    aunlNotifyBeforeInstallation = 3,
    aunlScheduledInstallation = 4
} AutomaticUpdatesNotificationLevel;
typedef enum tagAutomaticUpdatesScheduledInstallationDay {
    ausidEveryDay = 0,
    ausidEverySunday = 1,
    ausidEveryMonday = 2,
    ausidEveryTuesday = 3,
    ausidEveryWednesday = 4,
    ausidEveryThursday = 5,
    ausidEveryFriday = 6,
    ausidEverySaturday = 7
} AutomaticUpdatesScheduledInstallationDay;
typedef enum tagDownloadPhase {
    dphInitializing = 0,
    dphDownloading = 1,
    dphVerifying = 2
} DownloadPhase;
typedef enum tagOperationResultCode {
    orcNotStarted = 0,
    orcInProgress = 1,
    orcSucceeded = 2,
    orcSucceededWithErrors = 3,
    orcFailed = 4,
    orcAborted = 5
} OperationResultCode;
typedef enum tagUpdateExceptionContext {
    uecGeneral = 1,
    uecWindowsDriver = 2,
    uecWindowsInstaller = 3
} UpdateExceptionContext;
typedef enum tagInstallationImpact {
    iiNormal = 0,
    iiMinor = 1,
    iiRequiresExclusiveHandling = 2
} InstallationImpact;
typedef enum tagInstallationRebootBehavior {
    irbNeverReboots = 0,
    irbAlwaysRequiresReboot = 1,
    irbCanRequestReboot = 2
} InstallationRebootBehavior;
typedef enum tagUpdateType {
    utSoftware = 1,
    utDriver = 2
} UpdateType;
typedef enum tagUpdateOperation {
    uoInstallation = 1,
    uoUninstallation = 2
} UpdateOperation;
typedef enum tagDeploymentAction {
    daNone = 0,
    daInstallation = 1,
    daUninstallation = 2,
    daDetection = 3
} DeploymentAction;
/*****************************************************************************
 * IAutomaticUpdatesSettings interface
 */
#ifndef __IAutomaticUpdatesSettings_INTERFACE_DEFINED__
#define __IAutomaticUpdatesSettings_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAutomaticUpdatesSettings, 0x2ee48f22, 0xaf3c, 0x405f, 0x89,0x70, 0xf7,0x1b,0xe1,0x2e,0xe9,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2ee48f22-af3c-405f-8970-f71be12ee9a2")
IAutomaticUpdatesSettings : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_NotificationLevel(
        AutomaticUpdatesNotificationLevel *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_NotificationLevel(
        AutomaticUpdatesNotificationLevel value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ReadOnly(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Required(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ScheduledInstallationDay(
        AutomaticUpdatesScheduledInstallationDay *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ScheduledInstallationDay(
        AutomaticUpdatesScheduledInstallationDay value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ScheduledInstallationTime(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ScheduledInstallationTime(
        LONG value) = 0;

    virtual HRESULT STDMETHODCALLTYPE Refresh(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Save(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAutomaticUpdatesSettings, 0x2ee48f22, 0xaf3c, 0x405f, 0x89,0x70, 0xf7,0x1b,0xe1,0x2e,0xe9,0xa2)
#endif
#else
typedef struct IAutomaticUpdatesSettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAutomaticUpdatesSettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAutomaticUpdatesSettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAutomaticUpdatesSettings *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IAutomaticUpdatesSettings *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IAutomaticUpdatesSettings *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IAutomaticUpdatesSettings *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IAutomaticUpdatesSettings *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IAutomaticUpdatesSettings methods ***/
    HRESULT (STDMETHODCALLTYPE *get_NotificationLevel)(
        IAutomaticUpdatesSettings *This,
        AutomaticUpdatesNotificationLevel *retval);

    HRESULT (STDMETHODCALLTYPE *put_NotificationLevel)(
        IAutomaticUpdatesSettings *This,
        AutomaticUpdatesNotificationLevel value);

    HRESULT (STDMETHODCALLTYPE *get_ReadOnly)(
        IAutomaticUpdatesSettings *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_Required)(
        IAutomaticUpdatesSettings *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_ScheduledInstallationDay)(
        IAutomaticUpdatesSettings *This,
        AutomaticUpdatesScheduledInstallationDay *retval);

    HRESULT (STDMETHODCALLTYPE *put_ScheduledInstallationDay)(
        IAutomaticUpdatesSettings *This,
        AutomaticUpdatesScheduledInstallationDay value);

    HRESULT (STDMETHODCALLTYPE *get_ScheduledInstallationTime)(
        IAutomaticUpdatesSettings *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *put_ScheduledInstallationTime)(
        IAutomaticUpdatesSettings *This,
        LONG value);

    HRESULT (STDMETHODCALLTYPE *Refresh)(
        IAutomaticUpdatesSettings *This);

    HRESULT (STDMETHODCALLTYPE *Save)(
        IAutomaticUpdatesSettings *This);

    END_INTERFACE
} IAutomaticUpdatesSettingsVtbl;

interface IAutomaticUpdatesSettings {
    CONST_VTBL IAutomaticUpdatesSettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAutomaticUpdatesSettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAutomaticUpdatesSettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAutomaticUpdatesSettings_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IAutomaticUpdatesSettings_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IAutomaticUpdatesSettings_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IAutomaticUpdatesSettings_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IAutomaticUpdatesSettings_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IAutomaticUpdatesSettings methods ***/
#define IAutomaticUpdatesSettings_get_NotificationLevel(This,retval) (This)->lpVtbl->get_NotificationLevel(This,retval)
#define IAutomaticUpdatesSettings_put_NotificationLevel(This,value) (This)->lpVtbl->put_NotificationLevel(This,value)
#define IAutomaticUpdatesSettings_get_ReadOnly(This,retval) (This)->lpVtbl->get_ReadOnly(This,retval)
#define IAutomaticUpdatesSettings_get_Required(This,retval) (This)->lpVtbl->get_Required(This,retval)
#define IAutomaticUpdatesSettings_get_ScheduledInstallationDay(This,retval) (This)->lpVtbl->get_ScheduledInstallationDay(This,retval)
#define IAutomaticUpdatesSettings_put_ScheduledInstallationDay(This,value) (This)->lpVtbl->put_ScheduledInstallationDay(This,value)
#define IAutomaticUpdatesSettings_get_ScheduledInstallationTime(This,retval) (This)->lpVtbl->get_ScheduledInstallationTime(This,retval)
#define IAutomaticUpdatesSettings_put_ScheduledInstallationTime(This,value) (This)->lpVtbl->put_ScheduledInstallationTime(This,value)
#define IAutomaticUpdatesSettings_Refresh(This) (This)->lpVtbl->Refresh(This)
#define IAutomaticUpdatesSettings_Save(This) (This)->lpVtbl->Save(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IAutomaticUpdatesSettings_QueryInterface(IAutomaticUpdatesSettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAutomaticUpdatesSettings_AddRef(IAutomaticUpdatesSettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAutomaticUpdatesSettings_Release(IAutomaticUpdatesSettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IAutomaticUpdatesSettings_GetTypeInfoCount(IAutomaticUpdatesSettings* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IAutomaticUpdatesSettings_GetTypeInfo(IAutomaticUpdatesSettings* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IAutomaticUpdatesSettings_GetIDsOfNames(IAutomaticUpdatesSettings* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IAutomaticUpdatesSettings_Invoke(IAutomaticUpdatesSettings* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IAutomaticUpdatesSettings methods ***/
static inline HRESULT IAutomaticUpdatesSettings_get_NotificationLevel(IAutomaticUpdatesSettings* This,AutomaticUpdatesNotificationLevel *retval) {
    return This->lpVtbl->get_NotificationLevel(This,retval);
}
static inline HRESULT IAutomaticUpdatesSettings_put_NotificationLevel(IAutomaticUpdatesSettings* This,AutomaticUpdatesNotificationLevel value) {
    return This->lpVtbl->put_NotificationLevel(This,value);
}
static inline HRESULT IAutomaticUpdatesSettings_get_ReadOnly(IAutomaticUpdatesSettings* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_ReadOnly(This,retval);
}
static inline HRESULT IAutomaticUpdatesSettings_get_Required(IAutomaticUpdatesSettings* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_Required(This,retval);
}
static inline HRESULT IAutomaticUpdatesSettings_get_ScheduledInstallationDay(IAutomaticUpdatesSettings* This,AutomaticUpdatesScheduledInstallationDay *retval) {
    return This->lpVtbl->get_ScheduledInstallationDay(This,retval);
}
static inline HRESULT IAutomaticUpdatesSettings_put_ScheduledInstallationDay(IAutomaticUpdatesSettings* This,AutomaticUpdatesScheduledInstallationDay value) {
    return This->lpVtbl->put_ScheduledInstallationDay(This,value);
}
static inline HRESULT IAutomaticUpdatesSettings_get_ScheduledInstallationTime(IAutomaticUpdatesSettings* This,LONG *retval) {
    return This->lpVtbl->get_ScheduledInstallationTime(This,retval);
}
static inline HRESULT IAutomaticUpdatesSettings_put_ScheduledInstallationTime(IAutomaticUpdatesSettings* This,LONG value) {
    return This->lpVtbl->put_ScheduledInstallationTime(This,value);
}
static inline HRESULT IAutomaticUpdatesSettings_Refresh(IAutomaticUpdatesSettings* This) {
    return This->lpVtbl->Refresh(This);
}
static inline HRESULT IAutomaticUpdatesSettings_Save(IAutomaticUpdatesSettings* This) {
    return This->lpVtbl->Save(This);
}
#endif
#endif

#endif


#endif  /* __IAutomaticUpdatesSettings_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAutomaticUpdates interface
 */
#ifndef __IAutomaticUpdates_INTERFACE_DEFINED__
#define __IAutomaticUpdates_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAutomaticUpdates, 0x673425bf, 0xc082, 0x4c7c, 0xbd,0xfd, 0x56,0x94,0x64,0xb8,0xe0,0xce);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("673425bf-c082-4c7c-bdfd-569464b8e0ce")
IAutomaticUpdates : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE DetectNow(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ShowSettingsDialog(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Settings(
        IAutomaticUpdatesSettings **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ServiceEnabled(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableService(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAutomaticUpdates, 0x673425bf, 0xc082, 0x4c7c, 0xbd,0xfd, 0x56,0x94,0x64,0xb8,0xe0,0xce)
#endif
#else
typedef struct IAutomaticUpdatesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAutomaticUpdates *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAutomaticUpdates *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAutomaticUpdates *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IAutomaticUpdates *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IAutomaticUpdates *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IAutomaticUpdates *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IAutomaticUpdates *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IAutomaticUpdates methods ***/
    HRESULT (STDMETHODCALLTYPE *DetectNow)(
        IAutomaticUpdates *This);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IAutomaticUpdates *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IAutomaticUpdates *This);

    HRESULT (STDMETHODCALLTYPE *ShowSettingsDialog)(
        IAutomaticUpdates *This);

    HRESULT (STDMETHODCALLTYPE *get_Settings)(
        IAutomaticUpdates *This,
        IAutomaticUpdatesSettings **retval);

    HRESULT (STDMETHODCALLTYPE *get_ServiceEnabled)(
        IAutomaticUpdates *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *EnableService)(
        IAutomaticUpdates *This);

    END_INTERFACE
} IAutomaticUpdatesVtbl;

interface IAutomaticUpdates {
    CONST_VTBL IAutomaticUpdatesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAutomaticUpdates_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAutomaticUpdates_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAutomaticUpdates_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IAutomaticUpdates_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IAutomaticUpdates_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IAutomaticUpdates_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IAutomaticUpdates_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IAutomaticUpdates methods ***/
#define IAutomaticUpdates_DetectNow(This) (This)->lpVtbl->DetectNow(This)
#define IAutomaticUpdates_Pause(This) (This)->lpVtbl->Pause(This)
#define IAutomaticUpdates_Resume(This) (This)->lpVtbl->Resume(This)
#define IAutomaticUpdates_ShowSettingsDialog(This) (This)->lpVtbl->ShowSettingsDialog(This)
#define IAutomaticUpdates_get_Settings(This,retval) (This)->lpVtbl->get_Settings(This,retval)
#define IAutomaticUpdates_get_ServiceEnabled(This,retval) (This)->lpVtbl->get_ServiceEnabled(This,retval)
#define IAutomaticUpdates_EnableService(This) (This)->lpVtbl->EnableService(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IAutomaticUpdates_QueryInterface(IAutomaticUpdates* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAutomaticUpdates_AddRef(IAutomaticUpdates* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAutomaticUpdates_Release(IAutomaticUpdates* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IAutomaticUpdates_GetTypeInfoCount(IAutomaticUpdates* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IAutomaticUpdates_GetTypeInfo(IAutomaticUpdates* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IAutomaticUpdates_GetIDsOfNames(IAutomaticUpdates* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IAutomaticUpdates_Invoke(IAutomaticUpdates* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IAutomaticUpdates methods ***/
static inline HRESULT IAutomaticUpdates_DetectNow(IAutomaticUpdates* This) {
    return This->lpVtbl->DetectNow(This);
}
static inline HRESULT IAutomaticUpdates_Pause(IAutomaticUpdates* This) {
    return This->lpVtbl->Pause(This);
}
static inline HRESULT IAutomaticUpdates_Resume(IAutomaticUpdates* This) {
    return This->lpVtbl->Resume(This);
}
static inline HRESULT IAutomaticUpdates_ShowSettingsDialog(IAutomaticUpdates* This) {
    return This->lpVtbl->ShowSettingsDialog(This);
}
static inline HRESULT IAutomaticUpdates_get_Settings(IAutomaticUpdates* This,IAutomaticUpdatesSettings **retval) {
    return This->lpVtbl->get_Settings(This,retval);
}
static inline HRESULT IAutomaticUpdates_get_ServiceEnabled(IAutomaticUpdates* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_ServiceEnabled(This,retval);
}
static inline HRESULT IAutomaticUpdates_EnableService(IAutomaticUpdates* This) {
    return This->lpVtbl->EnableService(This);
}
#endif
#endif

#endif


#endif  /* __IAutomaticUpdates_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWebProxy interface
 */
#ifndef __IWebProxy_INTERFACE_DEFINED__
#define __IWebProxy_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWebProxy, 0x174c81fe, 0xaecd, 0x4dae, 0xb8,0xa0, 0x2c,0x63,0x18,0xdd,0x86,0xa8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("174c81fe-aecd-4dae-b8a0-2c6318dd86a8")
IWebProxy : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Address(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Address(
        BSTR value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BypassList(
        IStringCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_BypassList(
        IStringCollection *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BypassProxyOnLocal(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_BypassProxyOnLocal(
        VARIANT_BOOL value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ReadOnly(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UserName(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_UserName(
        BSTR value) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPassword(
        BSTR value) = 0;

    virtual HRESULT STDMETHODCALLTYPE PromptForCredentials(
        IUnknown *parentWindow,
        BSTR title) = 0;

    virtual HRESULT STDMETHODCALLTYPE PromptForCredentialsFromHwnd(
        HWND parentWindow,
        BSTR title) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AutoDetect(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AutoDetect(
        VARIANT_BOOL value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWebProxy, 0x174c81fe, 0xaecd, 0x4dae, 0xb8,0xa0, 0x2c,0x63,0x18,0xdd,0x86,0xa8)
#endif
#else
typedef struct IWebProxyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWebProxy *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWebProxy *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWebProxy *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWebProxy *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWebProxy *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWebProxy *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWebProxy *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWebProxy methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Address)(
        IWebProxy *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *put_Address)(
        IWebProxy *This,
        BSTR value);

    HRESULT (STDMETHODCALLTYPE *get_BypassList)(
        IWebProxy *This,
        IStringCollection **retval);

    HRESULT (STDMETHODCALLTYPE *put_BypassList)(
        IWebProxy *This,
        IStringCollection *value);

    HRESULT (STDMETHODCALLTYPE *get_BypassProxyOnLocal)(
        IWebProxy *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *put_BypassProxyOnLocal)(
        IWebProxy *This,
        VARIANT_BOOL value);

    HRESULT (STDMETHODCALLTYPE *get_ReadOnly)(
        IWebProxy *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_UserName)(
        IWebProxy *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *put_UserName)(
        IWebProxy *This,
        BSTR value);

    HRESULT (STDMETHODCALLTYPE *SetPassword)(
        IWebProxy *This,
        BSTR value);

    HRESULT (STDMETHODCALLTYPE *PromptForCredentials)(
        IWebProxy *This,
        IUnknown *parentWindow,
        BSTR title);

    HRESULT (STDMETHODCALLTYPE *PromptForCredentialsFromHwnd)(
        IWebProxy *This,
        HWND parentWindow,
        BSTR title);

    HRESULT (STDMETHODCALLTYPE *get_AutoDetect)(
        IWebProxy *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *put_AutoDetect)(
        IWebProxy *This,
        VARIANT_BOOL value);

    END_INTERFACE
} IWebProxyVtbl;

interface IWebProxy {
    CONST_VTBL IWebProxyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWebProxy_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWebProxy_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWebProxy_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWebProxy_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWebProxy_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWebProxy_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWebProxy_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWebProxy methods ***/
#define IWebProxy_get_Address(This,retval) (This)->lpVtbl->get_Address(This,retval)
#define IWebProxy_put_Address(This,value) (This)->lpVtbl->put_Address(This,value)
#define IWebProxy_get_BypassList(This,retval) (This)->lpVtbl->get_BypassList(This,retval)
#define IWebProxy_put_BypassList(This,value) (This)->lpVtbl->put_BypassList(This,value)
#define IWebProxy_get_BypassProxyOnLocal(This,retval) (This)->lpVtbl->get_BypassProxyOnLocal(This,retval)
#define IWebProxy_put_BypassProxyOnLocal(This,value) (This)->lpVtbl->put_BypassProxyOnLocal(This,value)
#define IWebProxy_get_ReadOnly(This,retval) (This)->lpVtbl->get_ReadOnly(This,retval)
#define IWebProxy_get_UserName(This,retval) (This)->lpVtbl->get_UserName(This,retval)
#define IWebProxy_put_UserName(This,value) (This)->lpVtbl->put_UserName(This,value)
#define IWebProxy_SetPassword(This,value) (This)->lpVtbl->SetPassword(This,value)
#define IWebProxy_PromptForCredentials(This,parentWindow,title) (This)->lpVtbl->PromptForCredentials(This,parentWindow,title)
#define IWebProxy_PromptForCredentialsFromHwnd(This,parentWindow,title) (This)->lpVtbl->PromptForCredentialsFromHwnd(This,parentWindow,title)
#define IWebProxy_get_AutoDetect(This,retval) (This)->lpVtbl->get_AutoDetect(This,retval)
#define IWebProxy_put_AutoDetect(This,value) (This)->lpVtbl->put_AutoDetect(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT IWebProxy_QueryInterface(IWebProxy* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWebProxy_AddRef(IWebProxy* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWebProxy_Release(IWebProxy* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWebProxy_GetTypeInfoCount(IWebProxy* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWebProxy_GetTypeInfo(IWebProxy* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWebProxy_GetIDsOfNames(IWebProxy* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWebProxy_Invoke(IWebProxy* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWebProxy methods ***/
static inline HRESULT IWebProxy_get_Address(IWebProxy* This,BSTR *retval) {
    return This->lpVtbl->get_Address(This,retval);
}
static inline HRESULT IWebProxy_put_Address(IWebProxy* This,BSTR value) {
    return This->lpVtbl->put_Address(This,value);
}
static inline HRESULT IWebProxy_get_BypassList(IWebProxy* This,IStringCollection **retval) {
    return This->lpVtbl->get_BypassList(This,retval);
}
static inline HRESULT IWebProxy_put_BypassList(IWebProxy* This,IStringCollection *value) {
    return This->lpVtbl->put_BypassList(This,value);
}
static inline HRESULT IWebProxy_get_BypassProxyOnLocal(IWebProxy* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_BypassProxyOnLocal(This,retval);
}
static inline HRESULT IWebProxy_put_BypassProxyOnLocal(IWebProxy* This,VARIANT_BOOL value) {
    return This->lpVtbl->put_BypassProxyOnLocal(This,value);
}
static inline HRESULT IWebProxy_get_ReadOnly(IWebProxy* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_ReadOnly(This,retval);
}
static inline HRESULT IWebProxy_get_UserName(IWebProxy* This,BSTR *retval) {
    return This->lpVtbl->get_UserName(This,retval);
}
static inline HRESULT IWebProxy_put_UserName(IWebProxy* This,BSTR value) {
    return This->lpVtbl->put_UserName(This,value);
}
static inline HRESULT IWebProxy_SetPassword(IWebProxy* This,BSTR value) {
    return This->lpVtbl->SetPassword(This,value);
}
static inline HRESULT IWebProxy_PromptForCredentials(IWebProxy* This,IUnknown *parentWindow,BSTR title) {
    return This->lpVtbl->PromptForCredentials(This,parentWindow,title);
}
static inline HRESULT IWebProxy_PromptForCredentialsFromHwnd(IWebProxy* This,HWND parentWindow,BSTR title) {
    return This->lpVtbl->PromptForCredentialsFromHwnd(This,parentWindow,title);
}
static inline HRESULT IWebProxy_get_AutoDetect(IWebProxy* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_AutoDetect(This,retval);
}
static inline HRESULT IWebProxy_put_AutoDetect(IWebProxy* This,VARIANT_BOOL value) {
    return This->lpVtbl->put_AutoDetect(This,value);
}
#endif
#endif

#endif


#endif  /* __IWebProxy_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateSession interface
 */
#ifndef __IUpdateSession_INTERFACE_DEFINED__
#define __IUpdateSession_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateSession, 0x816858a4, 0x260d, 0x4260, 0x93,0x3a, 0x25,0x85,0xf1,0xab,0xc7,0x6b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("816858a4-260d-4260-933a-2585f1abc76b")
IUpdateSession : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ClientApplicationID(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ClientApplicationID(
        BSTR value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ReadOnly(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_WebProxy(
        IWebProxy **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_WebProxy(
        IWebProxy *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateUpdateSearcher(
        IUpdateSearcher **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateUpdateDownloader(
        IUpdateDownloader **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateUpdateInstaller(
        IUpdateInstaller **retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateSession, 0x816858a4, 0x260d, 0x4260, 0x93,0x3a, 0x25,0x85,0xf1,0xab,0xc7,0x6b)
#endif
#else
typedef struct IUpdateSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateSession *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateSession *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateSession *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateSession *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateSession *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateSession methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ClientApplicationID)(
        IUpdateSession *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *put_ClientApplicationID)(
        IUpdateSession *This,
        BSTR value);

    HRESULT (STDMETHODCALLTYPE *get_ReadOnly)(
        IUpdateSession *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_WebProxy)(
        IUpdateSession *This,
        IWebProxy **retval);

    HRESULT (STDMETHODCALLTYPE *put_WebProxy)(
        IUpdateSession *This,
        IWebProxy *value);

    HRESULT (STDMETHODCALLTYPE *CreateUpdateSearcher)(
        IUpdateSession *This,
        IUpdateSearcher **retval);

    HRESULT (STDMETHODCALLTYPE *CreateUpdateDownloader)(
        IUpdateSession *This,
        IUpdateDownloader **retval);

    HRESULT (STDMETHODCALLTYPE *CreateUpdateInstaller)(
        IUpdateSession *This,
        IUpdateInstaller **retval);

    END_INTERFACE
} IUpdateSessionVtbl;

interface IUpdateSession {
    CONST_VTBL IUpdateSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateSession_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateSession_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateSession_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateSession_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateSession_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateSession methods ***/
#define IUpdateSession_get_ClientApplicationID(This,retval) (This)->lpVtbl->get_ClientApplicationID(This,retval)
#define IUpdateSession_put_ClientApplicationID(This,value) (This)->lpVtbl->put_ClientApplicationID(This,value)
#define IUpdateSession_get_ReadOnly(This,retval) (This)->lpVtbl->get_ReadOnly(This,retval)
#define IUpdateSession_get_WebProxy(This,retval) (This)->lpVtbl->get_WebProxy(This,retval)
#define IUpdateSession_put_WebProxy(This,value) (This)->lpVtbl->put_WebProxy(This,value)
#define IUpdateSession_CreateUpdateSearcher(This,retval) (This)->lpVtbl->CreateUpdateSearcher(This,retval)
#define IUpdateSession_CreateUpdateDownloader(This,retval) (This)->lpVtbl->CreateUpdateDownloader(This,retval)
#define IUpdateSession_CreateUpdateInstaller(This,retval) (This)->lpVtbl->CreateUpdateInstaller(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateSession_QueryInterface(IUpdateSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateSession_AddRef(IUpdateSession* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateSession_Release(IUpdateSession* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateSession_GetTypeInfoCount(IUpdateSession* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateSession_GetTypeInfo(IUpdateSession* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateSession_GetIDsOfNames(IUpdateSession* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateSession_Invoke(IUpdateSession* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateSession methods ***/
static inline HRESULT IUpdateSession_get_ClientApplicationID(IUpdateSession* This,BSTR *retval) {
    return This->lpVtbl->get_ClientApplicationID(This,retval);
}
static inline HRESULT IUpdateSession_put_ClientApplicationID(IUpdateSession* This,BSTR value) {
    return This->lpVtbl->put_ClientApplicationID(This,value);
}
static inline HRESULT IUpdateSession_get_ReadOnly(IUpdateSession* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_ReadOnly(This,retval);
}
static inline HRESULT IUpdateSession_get_WebProxy(IUpdateSession* This,IWebProxy **retval) {
    return This->lpVtbl->get_WebProxy(This,retval);
}
static inline HRESULT IUpdateSession_put_WebProxy(IUpdateSession* This,IWebProxy *value) {
    return This->lpVtbl->put_WebProxy(This,value);
}
static inline HRESULT IUpdateSession_CreateUpdateSearcher(IUpdateSession* This,IUpdateSearcher **retval) {
    return This->lpVtbl->CreateUpdateSearcher(This,retval);
}
static inline HRESULT IUpdateSession_CreateUpdateDownloader(IUpdateSession* This,IUpdateDownloader **retval) {
    return This->lpVtbl->CreateUpdateDownloader(This,retval);
}
static inline HRESULT IUpdateSession_CreateUpdateInstaller(IUpdateSession* This,IUpdateInstaller **retval) {
    return This->lpVtbl->CreateUpdateInstaller(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateSession_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IImageInformation interface
 */
#ifndef __IImageInformation_INTERFACE_DEFINED__
#define __IImageInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IImageInformation, 0x7c907864, 0x346c, 0x4aeb, 0x8f,0x3f, 0x57,0xda,0x28,0x9f,0x96,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7c907864-346c-4aeb-8f3f-57da289f969f")
IImageInformation : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_AltText(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Height(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Source(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Width(
        LONG *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IImageInformation, 0x7c907864, 0x346c, 0x4aeb, 0x8f,0x3f, 0x57,0xda,0x28,0x9f,0x96,0x9f)
#endif
#else
typedef struct IImageInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IImageInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IImageInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IImageInformation *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IImageInformation *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IImageInformation *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IImageInformation *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IImageInformation *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IImageInformation methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AltText)(
        IImageInformation *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_Height)(
        IImageInformation *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_Source)(
        IImageInformation *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_Width)(
        IImageInformation *This,
        LONG *retval);

    END_INTERFACE
} IImageInformationVtbl;

interface IImageInformation {
    CONST_VTBL IImageInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IImageInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IImageInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IImageInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IImageInformation_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IImageInformation_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IImageInformation_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IImageInformation_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IImageInformation methods ***/
#define IImageInformation_get_AltText(This,retval) (This)->lpVtbl->get_AltText(This,retval)
#define IImageInformation_get_Height(This,retval) (This)->lpVtbl->get_Height(This,retval)
#define IImageInformation_get_Source(This,retval) (This)->lpVtbl->get_Source(This,retval)
#define IImageInformation_get_Width(This,retval) (This)->lpVtbl->get_Width(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IImageInformation_QueryInterface(IImageInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IImageInformation_AddRef(IImageInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IImageInformation_Release(IImageInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IImageInformation_GetTypeInfoCount(IImageInformation* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IImageInformation_GetTypeInfo(IImageInformation* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IImageInformation_GetIDsOfNames(IImageInformation* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IImageInformation_Invoke(IImageInformation* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IImageInformation methods ***/
static inline HRESULT IImageInformation_get_AltText(IImageInformation* This,BSTR *retval) {
    return This->lpVtbl->get_AltText(This,retval);
}
static inline HRESULT IImageInformation_get_Height(IImageInformation* This,LONG *retval) {
    return This->lpVtbl->get_Height(This,retval);
}
static inline HRESULT IImageInformation_get_Source(IImageInformation* This,BSTR *retval) {
    return This->lpVtbl->get_Source(This,retval);
}
static inline HRESULT IImageInformation_get_Width(IImageInformation* This,LONG *retval) {
    return This->lpVtbl->get_Width(This,retval);
}
#endif
#endif

#endif


#endif  /* __IImageInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICategory interface
 */
#ifndef __ICategory_INTERFACE_DEFINED__
#define __ICategory_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICategory, 0x81ddc1b8, 0x9d35, 0x47a6, 0xb4,0x71, 0x5b,0x80,0xf5,0x19,0x22,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("81ddc1b8-9d35-47a6-b471-5b80f519223b")
ICategory : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Name(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CategoryID(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Children(
        ICategoryCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Image(
        IImageInformation **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Order(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Parent(
        ICategory **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Type(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Updates(
        IUpdateCollection **retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICategory, 0x81ddc1b8, 0x9d35, 0x47a6, 0xb4,0x71, 0x5b,0x80,0xf5,0x19,0x22,0x3b)
#endif
#else
typedef struct ICategoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICategory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICategory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICategory *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ICategory *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ICategory *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ICategory *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ICategory *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ICategory methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        ICategory *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_CategoryID)(
        ICategory *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_Children)(
        ICategory *This,
        ICategoryCollection **retval);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        ICategory *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_Image)(
        ICategory *This,
        IImageInformation **retval);

    HRESULT (STDMETHODCALLTYPE *get_Order)(
        ICategory *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_Parent)(
        ICategory *This,
        ICategory **retval);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        ICategory *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_Updates)(
        ICategory *This,
        IUpdateCollection **retval);

    END_INTERFACE
} ICategoryVtbl;

interface ICategory {
    CONST_VTBL ICategoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICategory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICategory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICategory_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ICategory_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ICategory_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ICategory_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ICategory_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ICategory methods ***/
#define ICategory_get_Name(This,retval) (This)->lpVtbl->get_Name(This,retval)
#define ICategory_get_CategoryID(This,retval) (This)->lpVtbl->get_CategoryID(This,retval)
#define ICategory_get_Children(This,retval) (This)->lpVtbl->get_Children(This,retval)
#define ICategory_get_Description(This,retval) (This)->lpVtbl->get_Description(This,retval)
#define ICategory_get_Image(This,retval) (This)->lpVtbl->get_Image(This,retval)
#define ICategory_get_Order(This,retval) (This)->lpVtbl->get_Order(This,retval)
#define ICategory_get_Parent(This,retval) (This)->lpVtbl->get_Parent(This,retval)
#define ICategory_get_Type(This,retval) (This)->lpVtbl->get_Type(This,retval)
#define ICategory_get_Updates(This,retval) (This)->lpVtbl->get_Updates(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT ICategory_QueryInterface(ICategory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICategory_AddRef(ICategory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICategory_Release(ICategory* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ICategory_GetTypeInfoCount(ICategory* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ICategory_GetTypeInfo(ICategory* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ICategory_GetIDsOfNames(ICategory* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ICategory_Invoke(ICategory* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ICategory methods ***/
static inline HRESULT ICategory_get_Name(ICategory* This,BSTR *retval) {
    return This->lpVtbl->get_Name(This,retval);
}
static inline HRESULT ICategory_get_CategoryID(ICategory* This,BSTR *retval) {
    return This->lpVtbl->get_CategoryID(This,retval);
}
static inline HRESULT ICategory_get_Children(ICategory* This,ICategoryCollection **retval) {
    return This->lpVtbl->get_Children(This,retval);
}
static inline HRESULT ICategory_get_Description(ICategory* This,BSTR *retval) {
    return This->lpVtbl->get_Description(This,retval);
}
static inline HRESULT ICategory_get_Image(ICategory* This,IImageInformation **retval) {
    return This->lpVtbl->get_Image(This,retval);
}
static inline HRESULT ICategory_get_Order(ICategory* This,LONG *retval) {
    return This->lpVtbl->get_Order(This,retval);
}
static inline HRESULT ICategory_get_Parent(ICategory* This,ICategory **retval) {
    return This->lpVtbl->get_Parent(This,retval);
}
static inline HRESULT ICategory_get_Type(ICategory* This,BSTR *retval) {
    return This->lpVtbl->get_Type(This,retval);
}
static inline HRESULT ICategory_get_Updates(ICategory* This,IUpdateCollection **retval) {
    return This->lpVtbl->get_Updates(This,retval);
}
#endif
#endif

#endif


#endif  /* __ICategory_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ICategoryCollection interface
 */
#ifndef __ICategoryCollection_INTERFACE_DEFINED__
#define __ICategoryCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_ICategoryCollection, 0x3a56bfb8, 0x576c, 0x43f7, 0x93,0x35, 0xfe,0x48,0x38,0xfd,0x7e,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3a56bfb8-576c-43f7-9335-fe4838fd7e37")
ICategoryCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG index,
        ICategory **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ICategoryCollection, 0x3a56bfb8, 0x576c, 0x43f7, 0x93,0x35, 0xfe,0x48,0x38,0xfd,0x7e,0x37)
#endif
#else
typedef struct ICategoryCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ICategoryCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ICategoryCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ICategoryCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ICategoryCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ICategoryCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ICategoryCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ICategoryCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ICategoryCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Item)(
        ICategoryCollection *This,
        LONG index,
        ICategory **retval);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        ICategoryCollection *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        ICategoryCollection *This,
        LONG *retval);

    END_INTERFACE
} ICategoryCollectionVtbl;

interface ICategoryCollection {
    CONST_VTBL ICategoryCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ICategoryCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ICategoryCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ICategoryCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ICategoryCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ICategoryCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ICategoryCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ICategoryCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ICategoryCollection methods ***/
#define ICategoryCollection_get_Item(This,index,retval) (This)->lpVtbl->get_Item(This,index,retval)
#define ICategoryCollection_get__NewEnum(This,retval) (This)->lpVtbl->get__NewEnum(This,retval)
#define ICategoryCollection_get_Count(This,retval) (This)->lpVtbl->get_Count(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT ICategoryCollection_QueryInterface(ICategoryCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ICategoryCollection_AddRef(ICategoryCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ICategoryCollection_Release(ICategoryCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ICategoryCollection_GetTypeInfoCount(ICategoryCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ICategoryCollection_GetTypeInfo(ICategoryCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ICategoryCollection_GetIDsOfNames(ICategoryCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ICategoryCollection_Invoke(ICategoryCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ICategoryCollection methods ***/
static inline HRESULT ICategoryCollection_get_Item(ICategoryCollection* This,LONG index,ICategory **retval) {
    return This->lpVtbl->get_Item(This,index,retval);
}
static inline HRESULT ICategoryCollection_get__NewEnum(ICategoryCollection* This,IUnknown **retval) {
    return This->lpVtbl->get__NewEnum(This,retval);
}
static inline HRESULT ICategoryCollection_get_Count(ICategoryCollection* This,LONG *retval) {
    return This->lpVtbl->get_Count(This,retval);
}
#endif
#endif

#endif


#endif  /* __ICategoryCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IStringCollection interface
 */
#ifndef __IStringCollection_INTERFACE_DEFINED__
#define __IStringCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IStringCollection, 0xeff90582, 0x2ddc, 0x480f, 0xa0,0x6d, 0x60,0xf3,0xfb,0xc3,0x62,0xc3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eff90582-2ddc-480f-a06d-60f3fbc362c3")
IStringCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG index,
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Item(
        LONG index,
        BSTR value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ReadOnly(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        BSTR value,
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Copy(
        IStringCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE Insert(
        LONG index,
        BSTR value) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        LONG index) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IStringCollection, 0xeff90582, 0x2ddc, 0x480f, 0xa0,0x6d, 0x60,0xf3,0xfb,0xc3,0x62,0xc3)
#endif
#else
typedef struct IStringCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IStringCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IStringCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IStringCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IStringCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IStringCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IStringCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IStringCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IStringCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IStringCollection *This,
        LONG index,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *put_Item)(
        IStringCollection *This,
        LONG index,
        BSTR value);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IStringCollection *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IStringCollection *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_ReadOnly)(
        IStringCollection *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IStringCollection *This,
        BSTR value,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IStringCollection *This);

    HRESULT (STDMETHODCALLTYPE *Copy)(
        IStringCollection *This,
        IStringCollection **retval);

    HRESULT (STDMETHODCALLTYPE *Insert)(
        IStringCollection *This,
        LONG index,
        BSTR value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IStringCollection *This,
        LONG index);

    END_INTERFACE
} IStringCollectionVtbl;

interface IStringCollection {
    CONST_VTBL IStringCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IStringCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IStringCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IStringCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IStringCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IStringCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IStringCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IStringCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IStringCollection methods ***/
#define IStringCollection_get_Item(This,index,retval) (This)->lpVtbl->get_Item(This,index,retval)
#define IStringCollection_put_Item(This,index,value) (This)->lpVtbl->put_Item(This,index,value)
#define IStringCollection_get__NewEnum(This,retval) (This)->lpVtbl->get__NewEnum(This,retval)
#define IStringCollection_get_Count(This,retval) (This)->lpVtbl->get_Count(This,retval)
#define IStringCollection_get_ReadOnly(This,retval) (This)->lpVtbl->get_ReadOnly(This,retval)
#define IStringCollection_Add(This,value,retval) (This)->lpVtbl->Add(This,value,retval)
#define IStringCollection_Clear(This) (This)->lpVtbl->Clear(This)
#define IStringCollection_Copy(This,retval) (This)->lpVtbl->Copy(This,retval)
#define IStringCollection_Insert(This,index,value) (This)->lpVtbl->Insert(This,index,value)
#define IStringCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#else
/*** IUnknown methods ***/
static inline HRESULT IStringCollection_QueryInterface(IStringCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IStringCollection_AddRef(IStringCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IStringCollection_Release(IStringCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IStringCollection_GetTypeInfoCount(IStringCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IStringCollection_GetTypeInfo(IStringCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IStringCollection_GetIDsOfNames(IStringCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IStringCollection_Invoke(IStringCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IStringCollection methods ***/
static inline HRESULT IStringCollection_get_Item(IStringCollection* This,LONG index,BSTR *retval) {
    return This->lpVtbl->get_Item(This,index,retval);
}
static inline HRESULT IStringCollection_put_Item(IStringCollection* This,LONG index,BSTR value) {
    return This->lpVtbl->put_Item(This,index,value);
}
static inline HRESULT IStringCollection_get__NewEnum(IStringCollection* This,IUnknown **retval) {
    return This->lpVtbl->get__NewEnum(This,retval);
}
static inline HRESULT IStringCollection_get_Count(IStringCollection* This,LONG *retval) {
    return This->lpVtbl->get_Count(This,retval);
}
static inline HRESULT IStringCollection_get_ReadOnly(IStringCollection* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_ReadOnly(This,retval);
}
static inline HRESULT IStringCollection_Add(IStringCollection* This,BSTR value,LONG *retval) {
    return This->lpVtbl->Add(This,value,retval);
}
static inline HRESULT IStringCollection_Clear(IStringCollection* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT IStringCollection_Copy(IStringCollection* This,IStringCollection **retval) {
    return This->lpVtbl->Copy(This,retval);
}
static inline HRESULT IStringCollection_Insert(IStringCollection* This,LONG index,BSTR value) {
    return This->lpVtbl->Insert(This,index,value);
}
static inline HRESULT IStringCollection_RemoveAt(IStringCollection* This,LONG index) {
    return This->lpVtbl->RemoveAt(This,index);
}
#endif
#endif

#endif


#endif  /* __IStringCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateException interface
 */
#ifndef __IUpdateException_INTERFACE_DEFINED__
#define __IUpdateException_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateException, 0xa376dd5e, 0x09d4, 0x427f, 0xaf,0x7c, 0xfe,0xd5,0xb6,0xe1,0xc1,0xd6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a376dd5e-09d4-427f-af7c-fed5b6e1c1d6")
IUpdateException : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Message(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HResult(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Context(
        UpdateExceptionContext *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateException, 0xa376dd5e, 0x09d4, 0x427f, 0xaf,0x7c, 0xfe,0xd5,0xb6,0xe1,0xc1,0xd6)
#endif
#else
typedef struct IUpdateExceptionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateException *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateException *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateException *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateException *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateException *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateException *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateException *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateException methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Message)(
        IUpdateException *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_HResult)(
        IUpdateException *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_Context)(
        IUpdateException *This,
        UpdateExceptionContext *retval);

    END_INTERFACE
} IUpdateExceptionVtbl;

interface IUpdateException {
    CONST_VTBL IUpdateExceptionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateException_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateException_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateException_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateException_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateException_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateException_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateException_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateException methods ***/
#define IUpdateException_get_Message(This,retval) (This)->lpVtbl->get_Message(This,retval)
#define IUpdateException_get_HResult(This,retval) (This)->lpVtbl->get_HResult(This,retval)
#define IUpdateException_get_Context(This,retval) (This)->lpVtbl->get_Context(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateException_QueryInterface(IUpdateException* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateException_AddRef(IUpdateException* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateException_Release(IUpdateException* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateException_GetTypeInfoCount(IUpdateException* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateException_GetTypeInfo(IUpdateException* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateException_GetIDsOfNames(IUpdateException* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateException_Invoke(IUpdateException* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateException methods ***/
static inline HRESULT IUpdateException_get_Message(IUpdateException* This,BSTR *retval) {
    return This->lpVtbl->get_Message(This,retval);
}
static inline HRESULT IUpdateException_get_HResult(IUpdateException* This,LONG *retval) {
    return This->lpVtbl->get_HResult(This,retval);
}
static inline HRESULT IUpdateException_get_Context(IUpdateException* This,UpdateExceptionContext *retval) {
    return This->lpVtbl->get_Context(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateException_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateExceptionCollection interface
 */
#ifndef __IUpdateExceptionCollection_INTERFACE_DEFINED__
#define __IUpdateExceptionCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateExceptionCollection, 0x503626a3, 0x8e14, 0x4729, 0x93,0x55, 0x0f,0xe6,0x64,0xbd,0x23,0x21);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("503626a3-8e14-4729-9355-0fe664bd2321")
IUpdateExceptionCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG index,
        IUpdateException **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateExceptionCollection, 0x503626a3, 0x8e14, 0x4729, 0x93,0x55, 0x0f,0xe6,0x64,0xbd,0x23,0x21)
#endif
#else
typedef struct IUpdateExceptionCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateExceptionCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateExceptionCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateExceptionCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateExceptionCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateExceptionCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateExceptionCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateExceptionCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateExceptionCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IUpdateExceptionCollection *This,
        LONG index,
        IUpdateException **retval);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IUpdateExceptionCollection *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IUpdateExceptionCollection *This,
        LONG *retval);

    END_INTERFACE
} IUpdateExceptionCollectionVtbl;

interface IUpdateExceptionCollection {
    CONST_VTBL IUpdateExceptionCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateExceptionCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateExceptionCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateExceptionCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateExceptionCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateExceptionCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateExceptionCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateExceptionCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateExceptionCollection methods ***/
#define IUpdateExceptionCollection_get_Item(This,index,retval) (This)->lpVtbl->get_Item(This,index,retval)
#define IUpdateExceptionCollection_get__NewEnum(This,retval) (This)->lpVtbl->get__NewEnum(This,retval)
#define IUpdateExceptionCollection_get_Count(This,retval) (This)->lpVtbl->get_Count(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateExceptionCollection_QueryInterface(IUpdateExceptionCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateExceptionCollection_AddRef(IUpdateExceptionCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateExceptionCollection_Release(IUpdateExceptionCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateExceptionCollection_GetTypeInfoCount(IUpdateExceptionCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateExceptionCollection_GetTypeInfo(IUpdateExceptionCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateExceptionCollection_GetIDsOfNames(IUpdateExceptionCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateExceptionCollection_Invoke(IUpdateExceptionCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateExceptionCollection methods ***/
static inline HRESULT IUpdateExceptionCollection_get_Item(IUpdateExceptionCollection* This,LONG index,IUpdateException **retval) {
    return This->lpVtbl->get_Item(This,index,retval);
}
static inline HRESULT IUpdateExceptionCollection_get__NewEnum(IUpdateExceptionCollection* This,IUnknown **retval) {
    return This->lpVtbl->get__NewEnum(This,retval);
}
static inline HRESULT IUpdateExceptionCollection_get_Count(IUpdateExceptionCollection* This,LONG *retval) {
    return This->lpVtbl->get_Count(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateExceptionCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateIdentity interface
 */
#ifndef __IUpdateIdentity_INTERFACE_DEFINED__
#define __IUpdateIdentity_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateIdentity, 0x46297823, 0x9940, 0x4c09, 0xae,0xd9, 0xcd,0x3e,0xa6,0xd0,0x59,0x68);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("*************-4c09-aed9-cd3ea6d05968")
IUpdateIdentity : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_RevisionNumber(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UpdateID(
        BSTR *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateIdentity, 0x46297823, 0x9940, 0x4c09, 0xae,0xd9, 0xcd,0x3e,0xa6,0xd0,0x59,0x68)
#endif
#else
typedef struct IUpdateIdentityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateIdentity *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateIdentity *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateIdentity *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateIdentity *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateIdentity *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateIdentity *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateIdentity *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateIdentity methods ***/
    HRESULT (STDMETHODCALLTYPE *get_RevisionNumber)(
        IUpdateIdentity *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_UpdateID)(
        IUpdateIdentity *This,
        BSTR *retval);

    END_INTERFACE
} IUpdateIdentityVtbl;

interface IUpdateIdentity {
    CONST_VTBL IUpdateIdentityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateIdentity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateIdentity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateIdentity_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateIdentity_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateIdentity_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateIdentity_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateIdentity_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateIdentity methods ***/
#define IUpdateIdentity_get_RevisionNumber(This,retval) (This)->lpVtbl->get_RevisionNumber(This,retval)
#define IUpdateIdentity_get_UpdateID(This,retval) (This)->lpVtbl->get_UpdateID(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateIdentity_QueryInterface(IUpdateIdentity* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateIdentity_AddRef(IUpdateIdentity* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateIdentity_Release(IUpdateIdentity* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateIdentity_GetTypeInfoCount(IUpdateIdentity* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateIdentity_GetTypeInfo(IUpdateIdentity* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateIdentity_GetIDsOfNames(IUpdateIdentity* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateIdentity_Invoke(IUpdateIdentity* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateIdentity methods ***/
static inline HRESULT IUpdateIdentity_get_RevisionNumber(IUpdateIdentity* This,LONG *retval) {
    return This->lpVtbl->get_RevisionNumber(This,retval);
}
static inline HRESULT IUpdateIdentity_get_UpdateID(IUpdateIdentity* This,BSTR *retval) {
    return This->lpVtbl->get_UpdateID(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateIdentity_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInstallationBehavior interface
 */
#ifndef __IInstallationBehavior_INTERFACE_DEFINED__
#define __IInstallationBehavior_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInstallationBehavior, 0xd9a59339, 0xe245, 0x4dbd, 0x96,0x86, 0x4d,0x57,0x63,0xe3,0x96,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d9a59339-e245-4dbd-9686-4d5763e39624")
IInstallationBehavior : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_CanRequestUserInput(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Impact(
        InstallationImpact *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RebootBehavior(
        InstallationRebootBehavior *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RequiresNetworkConnectivity(
        VARIANT_BOOL *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInstallationBehavior, 0xd9a59339, 0xe245, 0x4dbd, 0x96,0x86, 0x4d,0x57,0x63,0xe3,0x96,0x24)
#endif
#else
typedef struct IInstallationBehaviorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInstallationBehavior *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInstallationBehavior *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInstallationBehavior *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInstallationBehavior *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInstallationBehavior *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInstallationBehavior *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInstallationBehavior *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInstallationBehavior methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CanRequestUserInput)(
        IInstallationBehavior *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_Impact)(
        IInstallationBehavior *This,
        InstallationImpact *retval);

    HRESULT (STDMETHODCALLTYPE *get_RebootBehavior)(
        IInstallationBehavior *This,
        InstallationRebootBehavior *retval);

    HRESULT (STDMETHODCALLTYPE *get_RequiresNetworkConnectivity)(
        IInstallationBehavior *This,
        VARIANT_BOOL *retval);

    END_INTERFACE
} IInstallationBehaviorVtbl;

interface IInstallationBehavior {
    CONST_VTBL IInstallationBehaviorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInstallationBehavior_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInstallationBehavior_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInstallationBehavior_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInstallationBehavior_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInstallationBehavior_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInstallationBehavior_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInstallationBehavior_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInstallationBehavior methods ***/
#define IInstallationBehavior_get_CanRequestUserInput(This,retval) (This)->lpVtbl->get_CanRequestUserInput(This,retval)
#define IInstallationBehavior_get_Impact(This,retval) (This)->lpVtbl->get_Impact(This,retval)
#define IInstallationBehavior_get_RebootBehavior(This,retval) (This)->lpVtbl->get_RebootBehavior(This,retval)
#define IInstallationBehavior_get_RequiresNetworkConnectivity(This,retval) (This)->lpVtbl->get_RequiresNetworkConnectivity(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IInstallationBehavior_QueryInterface(IInstallationBehavior* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInstallationBehavior_AddRef(IInstallationBehavior* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInstallationBehavior_Release(IInstallationBehavior* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInstallationBehavior_GetTypeInfoCount(IInstallationBehavior* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInstallationBehavior_GetTypeInfo(IInstallationBehavior* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInstallationBehavior_GetIDsOfNames(IInstallationBehavior* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInstallationBehavior_Invoke(IInstallationBehavior* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInstallationBehavior methods ***/
static inline HRESULT IInstallationBehavior_get_CanRequestUserInput(IInstallationBehavior* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_CanRequestUserInput(This,retval);
}
static inline HRESULT IInstallationBehavior_get_Impact(IInstallationBehavior* This,InstallationImpact *retval) {
    return This->lpVtbl->get_Impact(This,retval);
}
static inline HRESULT IInstallationBehavior_get_RebootBehavior(IInstallationBehavior* This,InstallationRebootBehavior *retval) {
    return This->lpVtbl->get_RebootBehavior(This,retval);
}
static inline HRESULT IInstallationBehavior_get_RequiresNetworkConnectivity(IInstallationBehavior* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_RequiresNetworkConnectivity(This,retval);
}
#endif
#endif

#endif


#endif  /* __IInstallationBehavior_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateDownloadContent interface
 */
#ifndef __IUpdateDownloadContent_INTERFACE_DEFINED__
#define __IUpdateDownloadContent_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateDownloadContent, 0x54a2cb2d, 0x9a0c, 0x48b6, 0x8a,0x50, 0x9a,0xbb,0x69,0xee,0x2d,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("54a2cb2d-9a0c-48b6-8a50-9abb69ee2d02")
IUpdateDownloadContent : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_DownloadUrl(
        BSTR *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateDownloadContent, 0x54a2cb2d, 0x9a0c, 0x48b6, 0x8a,0x50, 0x9a,0xbb,0x69,0xee,0x2d,0x02)
#endif
#else
typedef struct IUpdateDownloadContentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateDownloadContent *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateDownloadContent *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateDownloadContent *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateDownloadContent *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateDownloadContent *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateDownloadContent *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateDownloadContent *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateDownloadContent methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DownloadUrl)(
        IUpdateDownloadContent *This,
        BSTR *retval);

    END_INTERFACE
} IUpdateDownloadContentVtbl;

interface IUpdateDownloadContent {
    CONST_VTBL IUpdateDownloadContentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateDownloadContent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateDownloadContent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateDownloadContent_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateDownloadContent_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateDownloadContent_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateDownloadContent_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateDownloadContent_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateDownloadContent methods ***/
#define IUpdateDownloadContent_get_DownloadUrl(This,retval) (This)->lpVtbl->get_DownloadUrl(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateDownloadContent_QueryInterface(IUpdateDownloadContent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateDownloadContent_AddRef(IUpdateDownloadContent* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateDownloadContent_Release(IUpdateDownloadContent* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateDownloadContent_GetTypeInfoCount(IUpdateDownloadContent* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateDownloadContent_GetTypeInfo(IUpdateDownloadContent* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateDownloadContent_GetIDsOfNames(IUpdateDownloadContent* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateDownloadContent_Invoke(IUpdateDownloadContent* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateDownloadContent methods ***/
static inline HRESULT IUpdateDownloadContent_get_DownloadUrl(IUpdateDownloadContent* This,BSTR *retval) {
    return This->lpVtbl->get_DownloadUrl(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateDownloadContent_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateDownloadContentCollection interface
 */
#ifndef __IUpdateDownloadContentCollection_INTERFACE_DEFINED__
#define __IUpdateDownloadContentCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateDownloadContentCollection, 0xbc5513c8, 0xb3b8, 0x4bf7, 0xa4,0xd4, 0x36,0x1c,0x0d,0x8c,0x88,0xba);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bc5513c8-b3b8-4bf7-a4d4-361c0d8c88ba")
IUpdateDownloadContentCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG index,
        IUpdateDownloadContent **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateDownloadContentCollection, 0xbc5513c8, 0xb3b8, 0x4bf7, 0xa4,0xd4, 0x36,0x1c,0x0d,0x8c,0x88,0xba)
#endif
#else
typedef struct IUpdateDownloadContentCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateDownloadContentCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateDownloadContentCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateDownloadContentCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateDownloadContentCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateDownloadContentCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateDownloadContentCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateDownloadContentCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateDownloadContentCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IUpdateDownloadContentCollection *This,
        LONG index,
        IUpdateDownloadContent **retval);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IUpdateDownloadContentCollection *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IUpdateDownloadContentCollection *This,
        LONG *retval);

    END_INTERFACE
} IUpdateDownloadContentCollectionVtbl;

interface IUpdateDownloadContentCollection {
    CONST_VTBL IUpdateDownloadContentCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateDownloadContentCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateDownloadContentCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateDownloadContentCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateDownloadContentCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateDownloadContentCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateDownloadContentCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateDownloadContentCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateDownloadContentCollection methods ***/
#define IUpdateDownloadContentCollection_get_Item(This,index,retval) (This)->lpVtbl->get_Item(This,index,retval)
#define IUpdateDownloadContentCollection_get__NewEnum(This,retval) (This)->lpVtbl->get__NewEnum(This,retval)
#define IUpdateDownloadContentCollection_get_Count(This,retval) (This)->lpVtbl->get_Count(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateDownloadContentCollection_QueryInterface(IUpdateDownloadContentCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateDownloadContentCollection_AddRef(IUpdateDownloadContentCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateDownloadContentCollection_Release(IUpdateDownloadContentCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateDownloadContentCollection_GetTypeInfoCount(IUpdateDownloadContentCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateDownloadContentCollection_GetTypeInfo(IUpdateDownloadContentCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateDownloadContentCollection_GetIDsOfNames(IUpdateDownloadContentCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateDownloadContentCollection_Invoke(IUpdateDownloadContentCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateDownloadContentCollection methods ***/
static inline HRESULT IUpdateDownloadContentCollection_get_Item(IUpdateDownloadContentCollection* This,LONG index,IUpdateDownloadContent **retval) {
    return This->lpVtbl->get_Item(This,index,retval);
}
static inline HRESULT IUpdateDownloadContentCollection_get__NewEnum(IUpdateDownloadContentCollection* This,IUnknown **retval) {
    return This->lpVtbl->get__NewEnum(This,retval);
}
static inline HRESULT IUpdateDownloadContentCollection_get_Count(IUpdateDownloadContentCollection* This,LONG *retval) {
    return This->lpVtbl->get_Count(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateDownloadContentCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdate interface
 */
#ifndef __IUpdate_INTERFACE_DEFINED__
#define __IUpdate_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdate, 0x6a92b07a, 0xd821, 0x4682, 0xb4,0x23, 0x5c,0x80,0x50,0x22,0xcc,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6a92b07a-d821-4682-b423-5c805022cc4d")
IUpdate : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Title(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AutoSelectOnWebSites(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BundledUpdates(
        IUpdateCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CanRequireSource(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Categories(
        ICategoryCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Deadline(
        VARIANT *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DeltaCompressedContentAvailable(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DeltaCompressedContentPreferred(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_EulaAccepted(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_EulaText(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HandlerID(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Identity(
        IUpdateIdentity **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Image(
        IImageInformation **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_InstallationBehavior(
        IInstallationBehavior **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsBeta(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsDownloaded(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsHidden(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IsHidden(
        VARIANT_BOOL value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsInstalled(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsMandatory(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsUninstallable(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Languages(
        IStringCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_LastDeploymentChangeTime(
        DATE *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MaxDownloadSize(
        DECIMAL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MinDownloadSize(
        DECIMAL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MoreInfoUrls(
        IStringCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MsrcSeverity(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RecommendedCpuSpeed(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RecommendedHardDiskSpace(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RecommendedMemory(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ReleaseNotes(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SecurityBulletinIDs(
        IStringCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SupersededUpdateIDs(
        IStringCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SupportUrl(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Type(
        UpdateType *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UninstallationNotes(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UninstallationBehavior(
        IInstallationBehavior **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UninstallationSteps(
        IStringCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_KBArticleIDs(
        IStringCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE AcceptEula(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DeploymentAction(
        DeploymentAction *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyFromCache(
        BSTR path,
        VARIANT_BOOL toExtractCabFiles) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DownloadPriority(
        DownloadPriority *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_DownloadContents(
        IUpdateDownloadContentCollection **retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdate, 0x6a92b07a, 0xd821, 0x4682, 0xb4,0x23, 0x5c,0x80,0x50,0x22,0xcc,0x4d)
#endif
#else
typedef struct IUpdateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdate *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdate *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdate *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdate *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdate *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdate methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Title)(
        IUpdate *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_AutoSelectOnWebSites)(
        IUpdate *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_BundledUpdates)(
        IUpdate *This,
        IUpdateCollection **retval);

    HRESULT (STDMETHODCALLTYPE *get_CanRequireSource)(
        IUpdate *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_Categories)(
        IUpdate *This,
        ICategoryCollection **retval);

    HRESULT (STDMETHODCALLTYPE *get_Deadline)(
        IUpdate *This,
        VARIANT *retval);

    HRESULT (STDMETHODCALLTYPE *get_DeltaCompressedContentAvailable)(
        IUpdate *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_DeltaCompressedContentPreferred)(
        IUpdate *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IUpdate *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_EulaAccepted)(
        IUpdate *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_EulaText)(
        IUpdate *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_HandlerID)(
        IUpdate *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_Identity)(
        IUpdate *This,
        IUpdateIdentity **retval);

    HRESULT (STDMETHODCALLTYPE *get_Image)(
        IUpdate *This,
        IImageInformation **retval);

    HRESULT (STDMETHODCALLTYPE *get_InstallationBehavior)(
        IUpdate *This,
        IInstallationBehavior **retval);

    HRESULT (STDMETHODCALLTYPE *get_IsBeta)(
        IUpdate *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_IsDownloaded)(
        IUpdate *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_IsHidden)(
        IUpdate *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *put_IsHidden)(
        IUpdate *This,
        VARIANT_BOOL value);

    HRESULT (STDMETHODCALLTYPE *get_IsInstalled)(
        IUpdate *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_IsMandatory)(
        IUpdate *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_IsUninstallable)(
        IUpdate *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_Languages)(
        IUpdate *This,
        IStringCollection **retval);

    HRESULT (STDMETHODCALLTYPE *get_LastDeploymentChangeTime)(
        IUpdate *This,
        DATE *retval);

    HRESULT (STDMETHODCALLTYPE *get_MaxDownloadSize)(
        IUpdate *This,
        DECIMAL *retval);

    HRESULT (STDMETHODCALLTYPE *get_MinDownloadSize)(
        IUpdate *This,
        DECIMAL *retval);

    HRESULT (STDMETHODCALLTYPE *get_MoreInfoUrls)(
        IUpdate *This,
        IStringCollection **retval);

    HRESULT (STDMETHODCALLTYPE *get_MsrcSeverity)(
        IUpdate *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_RecommendedCpuSpeed)(
        IUpdate *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_RecommendedHardDiskSpace)(
        IUpdate *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_RecommendedMemory)(
        IUpdate *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_ReleaseNotes)(
        IUpdate *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_SecurityBulletinIDs)(
        IUpdate *This,
        IStringCollection **retval);

    HRESULT (STDMETHODCALLTYPE *get_SupersededUpdateIDs)(
        IUpdate *This,
        IStringCollection **retval);

    HRESULT (STDMETHODCALLTYPE *get_SupportUrl)(
        IUpdate *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        IUpdate *This,
        UpdateType *retval);

    HRESULT (STDMETHODCALLTYPE *get_UninstallationNotes)(
        IUpdate *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_UninstallationBehavior)(
        IUpdate *This,
        IInstallationBehavior **retval);

    HRESULT (STDMETHODCALLTYPE *get_UninstallationSteps)(
        IUpdate *This,
        IStringCollection **retval);

    HRESULT (STDMETHODCALLTYPE *get_KBArticleIDs)(
        IUpdate *This,
        IStringCollection **retval);

    HRESULT (STDMETHODCALLTYPE *AcceptEula)(
        IUpdate *This);

    HRESULT (STDMETHODCALLTYPE *get_DeploymentAction)(
        IUpdate *This,
        DeploymentAction *retval);

    HRESULT (STDMETHODCALLTYPE *CopyFromCache)(
        IUpdate *This,
        BSTR path,
        VARIANT_BOOL toExtractCabFiles);

    HRESULT (STDMETHODCALLTYPE *get_DownloadPriority)(
        IUpdate *This,
        DownloadPriority *retval);

    HRESULT (STDMETHODCALLTYPE *get_DownloadContents)(
        IUpdate *This,
        IUpdateDownloadContentCollection **retval);

    END_INTERFACE
} IUpdateVtbl;

interface IUpdate {
    CONST_VTBL IUpdateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdate_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdate_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdate_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdate_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdate_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdate methods ***/
#define IUpdate_get_Title(This,retval) (This)->lpVtbl->get_Title(This,retval)
#define IUpdate_get_AutoSelectOnWebSites(This,retval) (This)->lpVtbl->get_AutoSelectOnWebSites(This,retval)
#define IUpdate_get_BundledUpdates(This,retval) (This)->lpVtbl->get_BundledUpdates(This,retval)
#define IUpdate_get_CanRequireSource(This,retval) (This)->lpVtbl->get_CanRequireSource(This,retval)
#define IUpdate_get_Categories(This,retval) (This)->lpVtbl->get_Categories(This,retval)
#define IUpdate_get_Deadline(This,retval) (This)->lpVtbl->get_Deadline(This,retval)
#define IUpdate_get_DeltaCompressedContentAvailable(This,retval) (This)->lpVtbl->get_DeltaCompressedContentAvailable(This,retval)
#define IUpdate_get_DeltaCompressedContentPreferred(This,retval) (This)->lpVtbl->get_DeltaCompressedContentPreferred(This,retval)
#define IUpdate_get_Description(This,retval) (This)->lpVtbl->get_Description(This,retval)
#define IUpdate_get_EulaAccepted(This,retval) (This)->lpVtbl->get_EulaAccepted(This,retval)
#define IUpdate_get_EulaText(This,retval) (This)->lpVtbl->get_EulaText(This,retval)
#define IUpdate_get_HandlerID(This,retval) (This)->lpVtbl->get_HandlerID(This,retval)
#define IUpdate_get_Identity(This,retval) (This)->lpVtbl->get_Identity(This,retval)
#define IUpdate_get_Image(This,retval) (This)->lpVtbl->get_Image(This,retval)
#define IUpdate_get_InstallationBehavior(This,retval) (This)->lpVtbl->get_InstallationBehavior(This,retval)
#define IUpdate_get_IsBeta(This,retval) (This)->lpVtbl->get_IsBeta(This,retval)
#define IUpdate_get_IsDownloaded(This,retval) (This)->lpVtbl->get_IsDownloaded(This,retval)
#define IUpdate_get_IsHidden(This,retval) (This)->lpVtbl->get_IsHidden(This,retval)
#define IUpdate_put_IsHidden(This,value) (This)->lpVtbl->put_IsHidden(This,value)
#define IUpdate_get_IsInstalled(This,retval) (This)->lpVtbl->get_IsInstalled(This,retval)
#define IUpdate_get_IsMandatory(This,retval) (This)->lpVtbl->get_IsMandatory(This,retval)
#define IUpdate_get_IsUninstallable(This,retval) (This)->lpVtbl->get_IsUninstallable(This,retval)
#define IUpdate_get_Languages(This,retval) (This)->lpVtbl->get_Languages(This,retval)
#define IUpdate_get_LastDeploymentChangeTime(This,retval) (This)->lpVtbl->get_LastDeploymentChangeTime(This,retval)
#define IUpdate_get_MaxDownloadSize(This,retval) (This)->lpVtbl->get_MaxDownloadSize(This,retval)
#define IUpdate_get_MinDownloadSize(This,retval) (This)->lpVtbl->get_MinDownloadSize(This,retval)
#define IUpdate_get_MoreInfoUrls(This,retval) (This)->lpVtbl->get_MoreInfoUrls(This,retval)
#define IUpdate_get_MsrcSeverity(This,retval) (This)->lpVtbl->get_MsrcSeverity(This,retval)
#define IUpdate_get_RecommendedCpuSpeed(This,retval) (This)->lpVtbl->get_RecommendedCpuSpeed(This,retval)
#define IUpdate_get_RecommendedHardDiskSpace(This,retval) (This)->lpVtbl->get_RecommendedHardDiskSpace(This,retval)
#define IUpdate_get_RecommendedMemory(This,retval) (This)->lpVtbl->get_RecommendedMemory(This,retval)
#define IUpdate_get_ReleaseNotes(This,retval) (This)->lpVtbl->get_ReleaseNotes(This,retval)
#define IUpdate_get_SecurityBulletinIDs(This,retval) (This)->lpVtbl->get_SecurityBulletinIDs(This,retval)
#define IUpdate_get_SupersededUpdateIDs(This,retval) (This)->lpVtbl->get_SupersededUpdateIDs(This,retval)
#define IUpdate_get_SupportUrl(This,retval) (This)->lpVtbl->get_SupportUrl(This,retval)
#define IUpdate_get_Type(This,retval) (This)->lpVtbl->get_Type(This,retval)
#define IUpdate_get_UninstallationNotes(This,retval) (This)->lpVtbl->get_UninstallationNotes(This,retval)
#define IUpdate_get_UninstallationBehavior(This,retval) (This)->lpVtbl->get_UninstallationBehavior(This,retval)
#define IUpdate_get_UninstallationSteps(This,retval) (This)->lpVtbl->get_UninstallationSteps(This,retval)
#define IUpdate_get_KBArticleIDs(This,retval) (This)->lpVtbl->get_KBArticleIDs(This,retval)
#define IUpdate_AcceptEula(This) (This)->lpVtbl->AcceptEula(This)
#define IUpdate_get_DeploymentAction(This,retval) (This)->lpVtbl->get_DeploymentAction(This,retval)
#define IUpdate_CopyFromCache(This,path,toExtractCabFiles) (This)->lpVtbl->CopyFromCache(This,path,toExtractCabFiles)
#define IUpdate_get_DownloadPriority(This,retval) (This)->lpVtbl->get_DownloadPriority(This,retval)
#define IUpdate_get_DownloadContents(This,retval) (This)->lpVtbl->get_DownloadContents(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdate_QueryInterface(IUpdate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdate_AddRef(IUpdate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdate_Release(IUpdate* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdate_GetTypeInfoCount(IUpdate* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdate_GetTypeInfo(IUpdate* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdate_GetIDsOfNames(IUpdate* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdate_Invoke(IUpdate* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdate methods ***/
static inline HRESULT IUpdate_get_Title(IUpdate* This,BSTR *retval) {
    return This->lpVtbl->get_Title(This,retval);
}
static inline HRESULT IUpdate_get_AutoSelectOnWebSites(IUpdate* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_AutoSelectOnWebSites(This,retval);
}
static inline HRESULT IUpdate_get_BundledUpdates(IUpdate* This,IUpdateCollection **retval) {
    return This->lpVtbl->get_BundledUpdates(This,retval);
}
static inline HRESULT IUpdate_get_CanRequireSource(IUpdate* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_CanRequireSource(This,retval);
}
static inline HRESULT IUpdate_get_Categories(IUpdate* This,ICategoryCollection **retval) {
    return This->lpVtbl->get_Categories(This,retval);
}
static inline HRESULT IUpdate_get_Deadline(IUpdate* This,VARIANT *retval) {
    return This->lpVtbl->get_Deadline(This,retval);
}
static inline HRESULT IUpdate_get_DeltaCompressedContentAvailable(IUpdate* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_DeltaCompressedContentAvailable(This,retval);
}
static inline HRESULT IUpdate_get_DeltaCompressedContentPreferred(IUpdate* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_DeltaCompressedContentPreferred(This,retval);
}
static inline HRESULT IUpdate_get_Description(IUpdate* This,BSTR *retval) {
    return This->lpVtbl->get_Description(This,retval);
}
static inline HRESULT IUpdate_get_EulaAccepted(IUpdate* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_EulaAccepted(This,retval);
}
static inline HRESULT IUpdate_get_EulaText(IUpdate* This,BSTR *retval) {
    return This->lpVtbl->get_EulaText(This,retval);
}
static inline HRESULT IUpdate_get_HandlerID(IUpdate* This,BSTR *retval) {
    return This->lpVtbl->get_HandlerID(This,retval);
}
static inline HRESULT IUpdate_get_Identity(IUpdate* This,IUpdateIdentity **retval) {
    return This->lpVtbl->get_Identity(This,retval);
}
static inline HRESULT IUpdate_get_Image(IUpdate* This,IImageInformation **retval) {
    return This->lpVtbl->get_Image(This,retval);
}
static inline HRESULT IUpdate_get_InstallationBehavior(IUpdate* This,IInstallationBehavior **retval) {
    return This->lpVtbl->get_InstallationBehavior(This,retval);
}
static inline HRESULT IUpdate_get_IsBeta(IUpdate* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsBeta(This,retval);
}
static inline HRESULT IUpdate_get_IsDownloaded(IUpdate* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsDownloaded(This,retval);
}
static inline HRESULT IUpdate_get_IsHidden(IUpdate* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsHidden(This,retval);
}
static inline HRESULT IUpdate_put_IsHidden(IUpdate* This,VARIANT_BOOL value) {
    return This->lpVtbl->put_IsHidden(This,value);
}
static inline HRESULT IUpdate_get_IsInstalled(IUpdate* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsInstalled(This,retval);
}
static inline HRESULT IUpdate_get_IsMandatory(IUpdate* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsMandatory(This,retval);
}
static inline HRESULT IUpdate_get_IsUninstallable(IUpdate* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsUninstallable(This,retval);
}
static inline HRESULT IUpdate_get_Languages(IUpdate* This,IStringCollection **retval) {
    return This->lpVtbl->get_Languages(This,retval);
}
static inline HRESULT IUpdate_get_LastDeploymentChangeTime(IUpdate* This,DATE *retval) {
    return This->lpVtbl->get_LastDeploymentChangeTime(This,retval);
}
static inline HRESULT IUpdate_get_MaxDownloadSize(IUpdate* This,DECIMAL *retval) {
    return This->lpVtbl->get_MaxDownloadSize(This,retval);
}
static inline HRESULT IUpdate_get_MinDownloadSize(IUpdate* This,DECIMAL *retval) {
    return This->lpVtbl->get_MinDownloadSize(This,retval);
}
static inline HRESULT IUpdate_get_MoreInfoUrls(IUpdate* This,IStringCollection **retval) {
    return This->lpVtbl->get_MoreInfoUrls(This,retval);
}
static inline HRESULT IUpdate_get_MsrcSeverity(IUpdate* This,BSTR *retval) {
    return This->lpVtbl->get_MsrcSeverity(This,retval);
}
static inline HRESULT IUpdate_get_RecommendedCpuSpeed(IUpdate* This,LONG *retval) {
    return This->lpVtbl->get_RecommendedCpuSpeed(This,retval);
}
static inline HRESULT IUpdate_get_RecommendedHardDiskSpace(IUpdate* This,LONG *retval) {
    return This->lpVtbl->get_RecommendedHardDiskSpace(This,retval);
}
static inline HRESULT IUpdate_get_RecommendedMemory(IUpdate* This,LONG *retval) {
    return This->lpVtbl->get_RecommendedMemory(This,retval);
}
static inline HRESULT IUpdate_get_ReleaseNotes(IUpdate* This,BSTR *retval) {
    return This->lpVtbl->get_ReleaseNotes(This,retval);
}
static inline HRESULT IUpdate_get_SecurityBulletinIDs(IUpdate* This,IStringCollection **retval) {
    return This->lpVtbl->get_SecurityBulletinIDs(This,retval);
}
static inline HRESULT IUpdate_get_SupersededUpdateIDs(IUpdate* This,IStringCollection **retval) {
    return This->lpVtbl->get_SupersededUpdateIDs(This,retval);
}
static inline HRESULT IUpdate_get_SupportUrl(IUpdate* This,BSTR *retval) {
    return This->lpVtbl->get_SupportUrl(This,retval);
}
static inline HRESULT IUpdate_get_Type(IUpdate* This,UpdateType *retval) {
    return This->lpVtbl->get_Type(This,retval);
}
static inline HRESULT IUpdate_get_UninstallationNotes(IUpdate* This,BSTR *retval) {
    return This->lpVtbl->get_UninstallationNotes(This,retval);
}
static inline HRESULT IUpdate_get_UninstallationBehavior(IUpdate* This,IInstallationBehavior **retval) {
    return This->lpVtbl->get_UninstallationBehavior(This,retval);
}
static inline HRESULT IUpdate_get_UninstallationSteps(IUpdate* This,IStringCollection **retval) {
    return This->lpVtbl->get_UninstallationSteps(This,retval);
}
static inline HRESULT IUpdate_get_KBArticleIDs(IUpdate* This,IStringCollection **retval) {
    return This->lpVtbl->get_KBArticleIDs(This,retval);
}
static inline HRESULT IUpdate_AcceptEula(IUpdate* This) {
    return This->lpVtbl->AcceptEula(This);
}
static inline HRESULT IUpdate_get_DeploymentAction(IUpdate* This,DeploymentAction *retval) {
    return This->lpVtbl->get_DeploymentAction(This,retval);
}
static inline HRESULT IUpdate_CopyFromCache(IUpdate* This,BSTR path,VARIANT_BOOL toExtractCabFiles) {
    return This->lpVtbl->CopyFromCache(This,path,toExtractCabFiles);
}
static inline HRESULT IUpdate_get_DownloadPriority(IUpdate* This,DownloadPriority *retval) {
    return This->lpVtbl->get_DownloadPriority(This,retval);
}
static inline HRESULT IUpdate_get_DownloadContents(IUpdate* This,IUpdateDownloadContentCollection **retval) {
    return This->lpVtbl->get_DownloadContents(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdate_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateCollection interface
 */
#ifndef __IUpdateCollection_INTERFACE_DEFINED__
#define __IUpdateCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateCollection, 0x07f7438c, 0x7709, 0x4ca5, 0xb5,0x18, 0x91,0x27,0x92,0x88,0x13,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("07f7438c-7709-4ca5-b518-91279288134e")
IUpdateCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG index,
        IUpdate **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Item(
        LONG index,
        IUpdate *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ReadOnly(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE Add(
        IUpdate *value,
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Copy(
        IUpdateCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE Insert(
        LONG index,
        IUpdate *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        LONG index) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateCollection, 0x07f7438c, 0x7709, 0x4ca5, 0xb5,0x18, 0x91,0x27,0x92,0x88,0x13,0x4e)
#endif
#else
typedef struct IUpdateCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IUpdateCollection *This,
        LONG index,
        IUpdate **retval);

    HRESULT (STDMETHODCALLTYPE *put_Item)(
        IUpdateCollection *This,
        LONG index,
        IUpdate *value);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IUpdateCollection *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IUpdateCollection *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_ReadOnly)(
        IUpdateCollection *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *Add)(
        IUpdateCollection *This,
        IUpdate *value,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IUpdateCollection *This);

    HRESULT (STDMETHODCALLTYPE *Copy)(
        IUpdateCollection *This,
        IUpdateCollection **retval);

    HRESULT (STDMETHODCALLTYPE *Insert)(
        IUpdateCollection *This,
        LONG index,
        IUpdate *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IUpdateCollection *This,
        LONG index);

    END_INTERFACE
} IUpdateCollectionVtbl;

interface IUpdateCollection {
    CONST_VTBL IUpdateCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateCollection methods ***/
#define IUpdateCollection_get_Item(This,index,retval) (This)->lpVtbl->get_Item(This,index,retval)
#define IUpdateCollection_put_Item(This,index,value) (This)->lpVtbl->put_Item(This,index,value)
#define IUpdateCollection_get__NewEnum(This,retval) (This)->lpVtbl->get__NewEnum(This,retval)
#define IUpdateCollection_get_Count(This,retval) (This)->lpVtbl->get_Count(This,retval)
#define IUpdateCollection_get_ReadOnly(This,retval) (This)->lpVtbl->get_ReadOnly(This,retval)
#define IUpdateCollection_Add(This,value,retval) (This)->lpVtbl->Add(This,value,retval)
#define IUpdateCollection_Clear(This) (This)->lpVtbl->Clear(This)
#define IUpdateCollection_Copy(This,retval) (This)->lpVtbl->Copy(This,retval)
#define IUpdateCollection_Insert(This,index,value) (This)->lpVtbl->Insert(This,index,value)
#define IUpdateCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateCollection_QueryInterface(IUpdateCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateCollection_AddRef(IUpdateCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateCollection_Release(IUpdateCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateCollection_GetTypeInfoCount(IUpdateCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateCollection_GetTypeInfo(IUpdateCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateCollection_GetIDsOfNames(IUpdateCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateCollection_Invoke(IUpdateCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateCollection methods ***/
static inline HRESULT IUpdateCollection_get_Item(IUpdateCollection* This,LONG index,IUpdate **retval) {
    return This->lpVtbl->get_Item(This,index,retval);
}
static inline HRESULT IUpdateCollection_put_Item(IUpdateCollection* This,LONG index,IUpdate *value) {
    return This->lpVtbl->put_Item(This,index,value);
}
static inline HRESULT IUpdateCollection_get__NewEnum(IUpdateCollection* This,IUnknown **retval) {
    return This->lpVtbl->get__NewEnum(This,retval);
}
static inline HRESULT IUpdateCollection_get_Count(IUpdateCollection* This,LONG *retval) {
    return This->lpVtbl->get_Count(This,retval);
}
static inline HRESULT IUpdateCollection_get_ReadOnly(IUpdateCollection* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_ReadOnly(This,retval);
}
static inline HRESULT IUpdateCollection_Add(IUpdateCollection* This,IUpdate *value,LONG *retval) {
    return This->lpVtbl->Add(This,value,retval);
}
static inline HRESULT IUpdateCollection_Clear(IUpdateCollection* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT IUpdateCollection_Copy(IUpdateCollection* This,IUpdateCollection **retval) {
    return This->lpVtbl->Copy(This,retval);
}
static inline HRESULT IUpdateCollection_Insert(IUpdateCollection* This,LONG index,IUpdate *value) {
    return This->lpVtbl->Insert(This,index,value);
}
static inline HRESULT IUpdateCollection_RemoveAt(IUpdateCollection* This,LONG index) {
    return This->lpVtbl->RemoveAt(This,index);
}
#endif
#endif

#endif


#endif  /* __IUpdateCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISearchJob interface
 */
#ifndef __ISearchJob_INTERFACE_DEFINED__
#define __ISearchJob_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISearchJob, 0x7366ea16, 0x7a1a, 0x4ea2, 0xb0,0x42, 0x97,0x3d,0x3e,0x9c,0xd9,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7366ea16-7a1a-4ea2-b042-973d3e9cd99b")
ISearchJob : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_AsyncState(
        VARIANT *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsCompleted(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE CleanUp(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestAbort(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISearchJob, 0x7366ea16, 0x7a1a, 0x4ea2, 0xb0,0x42, 0x97,0x3d,0x3e,0x9c,0xd9,0x9b)
#endif
#else
typedef struct ISearchJobVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISearchJob *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISearchJob *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISearchJob *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISearchJob *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISearchJob *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISearchJob *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISearchJob *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISearchJob methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AsyncState)(
        ISearchJob *This,
        VARIANT *retval);

    HRESULT (STDMETHODCALLTYPE *get_IsCompleted)(
        ISearchJob *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *CleanUp)(
        ISearchJob *This);

    HRESULT (STDMETHODCALLTYPE *RequestAbort)(
        ISearchJob *This);

    END_INTERFACE
} ISearchJobVtbl;

interface ISearchJob {
    CONST_VTBL ISearchJobVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISearchJob_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISearchJob_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISearchJob_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISearchJob_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISearchJob_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISearchJob_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISearchJob_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISearchJob methods ***/
#define ISearchJob_get_AsyncState(This,retval) (This)->lpVtbl->get_AsyncState(This,retval)
#define ISearchJob_get_IsCompleted(This,retval) (This)->lpVtbl->get_IsCompleted(This,retval)
#define ISearchJob_CleanUp(This) (This)->lpVtbl->CleanUp(This)
#define ISearchJob_RequestAbort(This) (This)->lpVtbl->RequestAbort(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ISearchJob_QueryInterface(ISearchJob* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISearchJob_AddRef(ISearchJob* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISearchJob_Release(ISearchJob* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISearchJob_GetTypeInfoCount(ISearchJob* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISearchJob_GetTypeInfo(ISearchJob* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISearchJob_GetIDsOfNames(ISearchJob* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISearchJob_Invoke(ISearchJob* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISearchJob methods ***/
static inline HRESULT ISearchJob_get_AsyncState(ISearchJob* This,VARIANT *retval) {
    return This->lpVtbl->get_AsyncState(This,retval);
}
static inline HRESULT ISearchJob_get_IsCompleted(ISearchJob* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsCompleted(This,retval);
}
static inline HRESULT ISearchJob_CleanUp(ISearchJob* This) {
    return This->lpVtbl->CleanUp(This);
}
static inline HRESULT ISearchJob_RequestAbort(ISearchJob* This) {
    return This->lpVtbl->RequestAbort(This);
}
#endif
#endif

#endif


#endif  /* __ISearchJob_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISearchResult interface
 */
#ifndef __ISearchResult_INTERFACE_DEFINED__
#define __ISearchResult_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISearchResult, 0xd40cff62, 0xe08c, 0x4498, 0x94,0x1a, 0x01,0xe2,0x5f,0x0f,0xd3,0x3c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d40cff62-e08c-4498-941a-01e25f0fd33c")
ISearchResult : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ResultCode(
        OperationResultCode *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RootCategories(
        ICategoryCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Updates(
        IUpdateCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Warnings(
        IUpdateExceptionCollection **retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISearchResult, 0xd40cff62, 0xe08c, 0x4498, 0x94,0x1a, 0x01,0xe2,0x5f,0x0f,0xd3,0x3c)
#endif
#else
typedef struct ISearchResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISearchResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISearchResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISearchResult *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISearchResult *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISearchResult *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISearchResult *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISearchResult *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISearchResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ResultCode)(
        ISearchResult *This,
        OperationResultCode *retval);

    HRESULT (STDMETHODCALLTYPE *get_RootCategories)(
        ISearchResult *This,
        ICategoryCollection **retval);

    HRESULT (STDMETHODCALLTYPE *get_Updates)(
        ISearchResult *This,
        IUpdateCollection **retval);

    HRESULT (STDMETHODCALLTYPE *get_Warnings)(
        ISearchResult *This,
        IUpdateExceptionCollection **retval);

    END_INTERFACE
} ISearchResultVtbl;

interface ISearchResult {
    CONST_VTBL ISearchResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISearchResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISearchResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISearchResult_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISearchResult_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISearchResult_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISearchResult_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISearchResult_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISearchResult methods ***/
#define ISearchResult_get_ResultCode(This,retval) (This)->lpVtbl->get_ResultCode(This,retval)
#define ISearchResult_get_RootCategories(This,retval) (This)->lpVtbl->get_RootCategories(This,retval)
#define ISearchResult_get_Updates(This,retval) (This)->lpVtbl->get_Updates(This,retval)
#define ISearchResult_get_Warnings(This,retval) (This)->lpVtbl->get_Warnings(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT ISearchResult_QueryInterface(ISearchResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISearchResult_AddRef(ISearchResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISearchResult_Release(ISearchResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISearchResult_GetTypeInfoCount(ISearchResult* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISearchResult_GetTypeInfo(ISearchResult* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISearchResult_GetIDsOfNames(ISearchResult* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISearchResult_Invoke(ISearchResult* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISearchResult methods ***/
static inline HRESULT ISearchResult_get_ResultCode(ISearchResult* This,OperationResultCode *retval) {
    return This->lpVtbl->get_ResultCode(This,retval);
}
static inline HRESULT ISearchResult_get_RootCategories(ISearchResult* This,ICategoryCollection **retval) {
    return This->lpVtbl->get_RootCategories(This,retval);
}
static inline HRESULT ISearchResult_get_Updates(ISearchResult* This,IUpdateCollection **retval) {
    return This->lpVtbl->get_Updates(This,retval);
}
static inline HRESULT ISearchResult_get_Warnings(ISearchResult* This,IUpdateExceptionCollection **retval) {
    return This->lpVtbl->get_Warnings(This,retval);
}
#endif
#endif

#endif


#endif  /* __ISearchResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateHistoryEntry interface
 */
#ifndef __IUpdateHistoryEntry_INTERFACE_DEFINED__
#define __IUpdateHistoryEntry_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateHistoryEntry, 0xbe56a644, 0xaf0e, 0x4e0e, 0xa3,0x11, 0xc1,0xd8,0xe6,0x95,0xcb,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("be56a644-af0e-4e0e-a311-c1d8e695cbff")
IUpdateHistoryEntry : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Operation(
        UpdateOperation *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ResultCode(
        OperationResultCode *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_HResult(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Date(
        DATE *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UpdateIdentity(
        IUpdateIdentity **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Title(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Description(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UnmappedResultCode(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ClientApplicationID(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ServerSelection(
        ServerSelection *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ServiceID(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UninstallationSteps(
        IStringCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_UninstallationNotes(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SupportUrl(
        BSTR *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateHistoryEntry, 0xbe56a644, 0xaf0e, 0x4e0e, 0xa3,0x11, 0xc1,0xd8,0xe6,0x95,0xcb,0xff)
#endif
#else
typedef struct IUpdateHistoryEntryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateHistoryEntry *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateHistoryEntry *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateHistoryEntry *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateHistoryEntry *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateHistoryEntry *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateHistoryEntry *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateHistoryEntry *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateHistoryEntry methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Operation)(
        IUpdateHistoryEntry *This,
        UpdateOperation *retval);

    HRESULT (STDMETHODCALLTYPE *get_ResultCode)(
        IUpdateHistoryEntry *This,
        OperationResultCode *retval);

    HRESULT (STDMETHODCALLTYPE *get_HResult)(
        IUpdateHistoryEntry *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_Date)(
        IUpdateHistoryEntry *This,
        DATE *retval);

    HRESULT (STDMETHODCALLTYPE *get_UpdateIdentity)(
        IUpdateHistoryEntry *This,
        IUpdateIdentity **retval);

    HRESULT (STDMETHODCALLTYPE *get_Title)(
        IUpdateHistoryEntry *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        IUpdateHistoryEntry *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_UnmappedResultCode)(
        IUpdateHistoryEntry *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_ClientApplicationID)(
        IUpdateHistoryEntry *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_ServerSelection)(
        IUpdateHistoryEntry *This,
        ServerSelection *retval);

    HRESULT (STDMETHODCALLTYPE *get_ServiceID)(
        IUpdateHistoryEntry *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_UninstallationSteps)(
        IUpdateHistoryEntry *This,
        IStringCollection **retval);

    HRESULT (STDMETHODCALLTYPE *get_UninstallationNotes)(
        IUpdateHistoryEntry *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_SupportUrl)(
        IUpdateHistoryEntry *This,
        BSTR *retval);

    END_INTERFACE
} IUpdateHistoryEntryVtbl;

interface IUpdateHistoryEntry {
    CONST_VTBL IUpdateHistoryEntryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateHistoryEntry_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateHistoryEntry_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateHistoryEntry_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateHistoryEntry_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateHistoryEntry_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateHistoryEntry_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateHistoryEntry_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateHistoryEntry methods ***/
#define IUpdateHistoryEntry_get_Operation(This,retval) (This)->lpVtbl->get_Operation(This,retval)
#define IUpdateHistoryEntry_get_ResultCode(This,retval) (This)->lpVtbl->get_ResultCode(This,retval)
#define IUpdateHistoryEntry_get_HResult(This,retval) (This)->lpVtbl->get_HResult(This,retval)
#define IUpdateHistoryEntry_get_Date(This,retval) (This)->lpVtbl->get_Date(This,retval)
#define IUpdateHistoryEntry_get_UpdateIdentity(This,retval) (This)->lpVtbl->get_UpdateIdentity(This,retval)
#define IUpdateHistoryEntry_get_Title(This,retval) (This)->lpVtbl->get_Title(This,retval)
#define IUpdateHistoryEntry_get_Description(This,retval) (This)->lpVtbl->get_Description(This,retval)
#define IUpdateHistoryEntry_get_UnmappedResultCode(This,retval) (This)->lpVtbl->get_UnmappedResultCode(This,retval)
#define IUpdateHistoryEntry_get_ClientApplicationID(This,retval) (This)->lpVtbl->get_ClientApplicationID(This,retval)
#define IUpdateHistoryEntry_get_ServerSelection(This,retval) (This)->lpVtbl->get_ServerSelection(This,retval)
#define IUpdateHistoryEntry_get_ServiceID(This,retval) (This)->lpVtbl->get_ServiceID(This,retval)
#define IUpdateHistoryEntry_get_UninstallationSteps(This,retval) (This)->lpVtbl->get_UninstallationSteps(This,retval)
#define IUpdateHistoryEntry_get_UninstallationNotes(This,retval) (This)->lpVtbl->get_UninstallationNotes(This,retval)
#define IUpdateHistoryEntry_get_SupportUrl(This,retval) (This)->lpVtbl->get_SupportUrl(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateHistoryEntry_QueryInterface(IUpdateHistoryEntry* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateHistoryEntry_AddRef(IUpdateHistoryEntry* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateHistoryEntry_Release(IUpdateHistoryEntry* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateHistoryEntry_GetTypeInfoCount(IUpdateHistoryEntry* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateHistoryEntry_GetTypeInfo(IUpdateHistoryEntry* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateHistoryEntry_GetIDsOfNames(IUpdateHistoryEntry* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateHistoryEntry_Invoke(IUpdateHistoryEntry* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateHistoryEntry methods ***/
static inline HRESULT IUpdateHistoryEntry_get_Operation(IUpdateHistoryEntry* This,UpdateOperation *retval) {
    return This->lpVtbl->get_Operation(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_ResultCode(IUpdateHistoryEntry* This,OperationResultCode *retval) {
    return This->lpVtbl->get_ResultCode(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_HResult(IUpdateHistoryEntry* This,LONG *retval) {
    return This->lpVtbl->get_HResult(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_Date(IUpdateHistoryEntry* This,DATE *retval) {
    return This->lpVtbl->get_Date(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_UpdateIdentity(IUpdateHistoryEntry* This,IUpdateIdentity **retval) {
    return This->lpVtbl->get_UpdateIdentity(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_Title(IUpdateHistoryEntry* This,BSTR *retval) {
    return This->lpVtbl->get_Title(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_Description(IUpdateHistoryEntry* This,BSTR *retval) {
    return This->lpVtbl->get_Description(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_UnmappedResultCode(IUpdateHistoryEntry* This,LONG *retval) {
    return This->lpVtbl->get_UnmappedResultCode(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_ClientApplicationID(IUpdateHistoryEntry* This,BSTR *retval) {
    return This->lpVtbl->get_ClientApplicationID(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_ServerSelection(IUpdateHistoryEntry* This,ServerSelection *retval) {
    return This->lpVtbl->get_ServerSelection(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_ServiceID(IUpdateHistoryEntry* This,BSTR *retval) {
    return This->lpVtbl->get_ServiceID(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_UninstallationSteps(IUpdateHistoryEntry* This,IStringCollection **retval) {
    return This->lpVtbl->get_UninstallationSteps(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_UninstallationNotes(IUpdateHistoryEntry* This,BSTR *retval) {
    return This->lpVtbl->get_UninstallationNotes(This,retval);
}
static inline HRESULT IUpdateHistoryEntry_get_SupportUrl(IUpdateHistoryEntry* This,BSTR *retval) {
    return This->lpVtbl->get_SupportUrl(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateHistoryEntry_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateHistoryEntryCollection interface
 */
#ifndef __IUpdateHistoryEntryCollection_INTERFACE_DEFINED__
#define __IUpdateHistoryEntryCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateHistoryEntryCollection, 0xa7f04f3c, 0xa290, 0x435b, 0xaa,0xdf, 0xa1,0x16,0xc3,0x35,0x7a,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a7f04f3c-a290-435b-aadf-a116c3357a5c")
IUpdateHistoryEntryCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG index,
        IUpdateHistoryEntry **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__NewEnum(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateHistoryEntryCollection, 0xa7f04f3c, 0xa290, 0x435b, 0xaa,0xdf, 0xa1,0x16,0xc3,0x35,0x7a,0x5c)
#endif
#else
typedef struct IUpdateHistoryEntryCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateHistoryEntryCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateHistoryEntryCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateHistoryEntryCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateHistoryEntryCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateHistoryEntryCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateHistoryEntryCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateHistoryEntryCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateHistoryEntryCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IUpdateHistoryEntryCollection *This,
        LONG index,
        IUpdateHistoryEntry **retval);

    HRESULT (STDMETHODCALLTYPE *get__NewEnum)(
        IUpdateHistoryEntryCollection *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IUpdateHistoryEntryCollection *This,
        LONG *retval);

    END_INTERFACE
} IUpdateHistoryEntryCollectionVtbl;

interface IUpdateHistoryEntryCollection {
    CONST_VTBL IUpdateHistoryEntryCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateHistoryEntryCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateHistoryEntryCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateHistoryEntryCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateHistoryEntryCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateHistoryEntryCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateHistoryEntryCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateHistoryEntryCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateHistoryEntryCollection methods ***/
#define IUpdateHistoryEntryCollection_get_Item(This,index,retval) (This)->lpVtbl->get_Item(This,index,retval)
#define IUpdateHistoryEntryCollection_get__NewEnum(This,retval) (This)->lpVtbl->get__NewEnum(This,retval)
#define IUpdateHistoryEntryCollection_get_Count(This,retval) (This)->lpVtbl->get_Count(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateHistoryEntryCollection_QueryInterface(IUpdateHistoryEntryCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateHistoryEntryCollection_AddRef(IUpdateHistoryEntryCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateHistoryEntryCollection_Release(IUpdateHistoryEntryCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateHistoryEntryCollection_GetTypeInfoCount(IUpdateHistoryEntryCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateHistoryEntryCollection_GetTypeInfo(IUpdateHistoryEntryCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateHistoryEntryCollection_GetIDsOfNames(IUpdateHistoryEntryCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateHistoryEntryCollection_Invoke(IUpdateHistoryEntryCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateHistoryEntryCollection methods ***/
static inline HRESULT IUpdateHistoryEntryCollection_get_Item(IUpdateHistoryEntryCollection* This,LONG index,IUpdateHistoryEntry **retval) {
    return This->lpVtbl->get_Item(This,index,retval);
}
static inline HRESULT IUpdateHistoryEntryCollection_get__NewEnum(IUpdateHistoryEntryCollection* This,IUnknown **retval) {
    return This->lpVtbl->get__NewEnum(This,retval);
}
static inline HRESULT IUpdateHistoryEntryCollection_get_Count(IUpdateHistoryEntryCollection* This,LONG *retval) {
    return This->lpVtbl->get_Count(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateHistoryEntryCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateSearcher interface
 */
#ifndef __IUpdateSearcher_INTERFACE_DEFINED__
#define __IUpdateSearcher_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateSearcher, 0x8f45abf1, 0xf9ae, 0x4b95, 0xa9,0x33, 0xf0,0xf6,0x6e,0x50,0x56,0xea);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f45abf1-f9ae-4b95-a933-f0f66e5056ea")
IUpdateSearcher : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_CanAutomaticallyUpgradeService(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_CanAutomaticallyUpgradeService(
        VARIANT_BOOL value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ClientApplicationID(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ClientApplicationID(
        BSTR value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IncludePotentiallySupersededUpdates(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IncludePotentiallySupersededUpdates(
        VARIANT_BOOL value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ServerSelection(
        ServerSelection *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ServerSelection(
        ServerSelection value) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginSearch(
        BSTR criteria,
        IUnknown *onCompleted,
        VARIANT state,
        ISearchJob **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndSearch(
        ISearchJob *searchJob,
        ISearchResult **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE EscapeString(
        BSTR unescaped,
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryHistory(
        LONG startIndex,
        LONG count,
        IUpdateHistoryEntryCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE Search(
        BSTR criteria,
        ISearchResult **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Online(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Online(
        VARIANT_BOOL value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTotalHistoryCount(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ServiceID(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ServiceID(
        BSTR value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateSearcher, 0x8f45abf1, 0xf9ae, 0x4b95, 0xa9,0x33, 0xf0,0xf6,0x6e,0x50,0x56,0xea)
#endif
#else
typedef struct IUpdateSearcherVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateSearcher *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateSearcher *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateSearcher *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateSearcher *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateSearcher *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateSearcher *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateSearcher *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateSearcher methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CanAutomaticallyUpgradeService)(
        IUpdateSearcher *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *put_CanAutomaticallyUpgradeService)(
        IUpdateSearcher *This,
        VARIANT_BOOL value);

    HRESULT (STDMETHODCALLTYPE *get_ClientApplicationID)(
        IUpdateSearcher *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *put_ClientApplicationID)(
        IUpdateSearcher *This,
        BSTR value);

    HRESULT (STDMETHODCALLTYPE *get_IncludePotentiallySupersededUpdates)(
        IUpdateSearcher *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *put_IncludePotentiallySupersededUpdates)(
        IUpdateSearcher *This,
        VARIANT_BOOL value);

    HRESULT (STDMETHODCALLTYPE *get_ServerSelection)(
        IUpdateSearcher *This,
        ServerSelection *retval);

    HRESULT (STDMETHODCALLTYPE *put_ServerSelection)(
        IUpdateSearcher *This,
        ServerSelection value);

    HRESULT (STDMETHODCALLTYPE *BeginSearch)(
        IUpdateSearcher *This,
        BSTR criteria,
        IUnknown *onCompleted,
        VARIANT state,
        ISearchJob **retval);

    HRESULT (STDMETHODCALLTYPE *EndSearch)(
        IUpdateSearcher *This,
        ISearchJob *searchJob,
        ISearchResult **retval);

    HRESULT (STDMETHODCALLTYPE *EscapeString)(
        IUpdateSearcher *This,
        BSTR unescaped,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *QueryHistory)(
        IUpdateSearcher *This,
        LONG startIndex,
        LONG count,
        IUpdateHistoryEntryCollection **retval);

    HRESULT (STDMETHODCALLTYPE *Search)(
        IUpdateSearcher *This,
        BSTR criteria,
        ISearchResult **retval);

    HRESULT (STDMETHODCALLTYPE *get_Online)(
        IUpdateSearcher *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *put_Online)(
        IUpdateSearcher *This,
        VARIANT_BOOL value);

    HRESULT (STDMETHODCALLTYPE *GetTotalHistoryCount)(
        IUpdateSearcher *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_ServiceID)(
        IUpdateSearcher *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *put_ServiceID)(
        IUpdateSearcher *This,
        BSTR value);

    END_INTERFACE
} IUpdateSearcherVtbl;

interface IUpdateSearcher {
    CONST_VTBL IUpdateSearcherVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateSearcher_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateSearcher_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateSearcher_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateSearcher_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateSearcher_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateSearcher_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateSearcher_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateSearcher methods ***/
#define IUpdateSearcher_get_CanAutomaticallyUpgradeService(This,retval) (This)->lpVtbl->get_CanAutomaticallyUpgradeService(This,retval)
#define IUpdateSearcher_put_CanAutomaticallyUpgradeService(This,value) (This)->lpVtbl->put_CanAutomaticallyUpgradeService(This,value)
#define IUpdateSearcher_get_ClientApplicationID(This,retval) (This)->lpVtbl->get_ClientApplicationID(This,retval)
#define IUpdateSearcher_put_ClientApplicationID(This,value) (This)->lpVtbl->put_ClientApplicationID(This,value)
#define IUpdateSearcher_get_IncludePotentiallySupersededUpdates(This,retval) (This)->lpVtbl->get_IncludePotentiallySupersededUpdates(This,retval)
#define IUpdateSearcher_put_IncludePotentiallySupersededUpdates(This,value) (This)->lpVtbl->put_IncludePotentiallySupersededUpdates(This,value)
#define IUpdateSearcher_get_ServerSelection(This,retval) (This)->lpVtbl->get_ServerSelection(This,retval)
#define IUpdateSearcher_put_ServerSelection(This,value) (This)->lpVtbl->put_ServerSelection(This,value)
#define IUpdateSearcher_BeginSearch(This,criteria,onCompleted,state,retval) (This)->lpVtbl->BeginSearch(This,criteria,onCompleted,state,retval)
#define IUpdateSearcher_EndSearch(This,searchJob,retval) (This)->lpVtbl->EndSearch(This,searchJob,retval)
#define IUpdateSearcher_EscapeString(This,unescaped,retval) (This)->lpVtbl->EscapeString(This,unescaped,retval)
#define IUpdateSearcher_QueryHistory(This,startIndex,count,retval) (This)->lpVtbl->QueryHistory(This,startIndex,count,retval)
#define IUpdateSearcher_Search(This,criteria,retval) (This)->lpVtbl->Search(This,criteria,retval)
#define IUpdateSearcher_get_Online(This,retval) (This)->lpVtbl->get_Online(This,retval)
#define IUpdateSearcher_put_Online(This,value) (This)->lpVtbl->put_Online(This,value)
#define IUpdateSearcher_GetTotalHistoryCount(This,retval) (This)->lpVtbl->GetTotalHistoryCount(This,retval)
#define IUpdateSearcher_get_ServiceID(This,retval) (This)->lpVtbl->get_ServiceID(This,retval)
#define IUpdateSearcher_put_ServiceID(This,value) (This)->lpVtbl->put_ServiceID(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateSearcher_QueryInterface(IUpdateSearcher* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateSearcher_AddRef(IUpdateSearcher* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateSearcher_Release(IUpdateSearcher* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateSearcher_GetTypeInfoCount(IUpdateSearcher* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateSearcher_GetTypeInfo(IUpdateSearcher* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateSearcher_GetIDsOfNames(IUpdateSearcher* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateSearcher_Invoke(IUpdateSearcher* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateSearcher methods ***/
static inline HRESULT IUpdateSearcher_get_CanAutomaticallyUpgradeService(IUpdateSearcher* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_CanAutomaticallyUpgradeService(This,retval);
}
static inline HRESULT IUpdateSearcher_put_CanAutomaticallyUpgradeService(IUpdateSearcher* This,VARIANT_BOOL value) {
    return This->lpVtbl->put_CanAutomaticallyUpgradeService(This,value);
}
static inline HRESULT IUpdateSearcher_get_ClientApplicationID(IUpdateSearcher* This,BSTR *retval) {
    return This->lpVtbl->get_ClientApplicationID(This,retval);
}
static inline HRESULT IUpdateSearcher_put_ClientApplicationID(IUpdateSearcher* This,BSTR value) {
    return This->lpVtbl->put_ClientApplicationID(This,value);
}
static inline HRESULT IUpdateSearcher_get_IncludePotentiallySupersededUpdates(IUpdateSearcher* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IncludePotentiallySupersededUpdates(This,retval);
}
static inline HRESULT IUpdateSearcher_put_IncludePotentiallySupersededUpdates(IUpdateSearcher* This,VARIANT_BOOL value) {
    return This->lpVtbl->put_IncludePotentiallySupersededUpdates(This,value);
}
static inline HRESULT IUpdateSearcher_get_ServerSelection(IUpdateSearcher* This,ServerSelection *retval) {
    return This->lpVtbl->get_ServerSelection(This,retval);
}
static inline HRESULT IUpdateSearcher_put_ServerSelection(IUpdateSearcher* This,ServerSelection value) {
    return This->lpVtbl->put_ServerSelection(This,value);
}
static inline HRESULT IUpdateSearcher_BeginSearch(IUpdateSearcher* This,BSTR criteria,IUnknown *onCompleted,VARIANT state,ISearchJob **retval) {
    return This->lpVtbl->BeginSearch(This,criteria,onCompleted,state,retval);
}
static inline HRESULT IUpdateSearcher_EndSearch(IUpdateSearcher* This,ISearchJob *searchJob,ISearchResult **retval) {
    return This->lpVtbl->EndSearch(This,searchJob,retval);
}
static inline HRESULT IUpdateSearcher_EscapeString(IUpdateSearcher* This,BSTR unescaped,BSTR *retval) {
    return This->lpVtbl->EscapeString(This,unescaped,retval);
}
static inline HRESULT IUpdateSearcher_QueryHistory(IUpdateSearcher* This,LONG startIndex,LONG count,IUpdateHistoryEntryCollection **retval) {
    return This->lpVtbl->QueryHistory(This,startIndex,count,retval);
}
static inline HRESULT IUpdateSearcher_Search(IUpdateSearcher* This,BSTR criteria,ISearchResult **retval) {
    return This->lpVtbl->Search(This,criteria,retval);
}
static inline HRESULT IUpdateSearcher_get_Online(IUpdateSearcher* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_Online(This,retval);
}
static inline HRESULT IUpdateSearcher_put_Online(IUpdateSearcher* This,VARIANT_BOOL value) {
    return This->lpVtbl->put_Online(This,value);
}
static inline HRESULT IUpdateSearcher_GetTotalHistoryCount(IUpdateSearcher* This,LONG *retval) {
    return This->lpVtbl->GetTotalHistoryCount(This,retval);
}
static inline HRESULT IUpdateSearcher_get_ServiceID(IUpdateSearcher* This,BSTR *retval) {
    return This->lpVtbl->get_ServiceID(This,retval);
}
static inline HRESULT IUpdateSearcher_put_ServiceID(IUpdateSearcher* This,BSTR value) {
    return This->lpVtbl->put_ServiceID(This,value);
}
#endif
#endif

#endif


#endif  /* __IUpdateSearcher_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateDownloadResult interface
 */
#ifndef __IUpdateDownloadResult_INTERFACE_DEFINED__
#define __IUpdateDownloadResult_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateDownloadResult, 0xbf99af76, 0xb575, 0x42ad, 0x8a,0xa4, 0x33,0xcb,0xb5,0x47,0x7a,0xf1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bf99af76-b575-42ad-8aa4-33cbb5477af1")
IUpdateDownloadResult : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_HResult(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ResultCode(
        OperationResultCode *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateDownloadResult, 0xbf99af76, 0xb575, 0x42ad, 0x8a,0xa4, 0x33,0xcb,0xb5,0x47,0x7a,0xf1)
#endif
#else
typedef struct IUpdateDownloadResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateDownloadResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateDownloadResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateDownloadResult *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateDownloadResult *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateDownloadResult *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateDownloadResult *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateDownloadResult *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateDownloadResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_HResult)(
        IUpdateDownloadResult *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_ResultCode)(
        IUpdateDownloadResult *This,
        OperationResultCode *retval);

    END_INTERFACE
} IUpdateDownloadResultVtbl;

interface IUpdateDownloadResult {
    CONST_VTBL IUpdateDownloadResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateDownloadResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateDownloadResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateDownloadResult_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateDownloadResult_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateDownloadResult_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateDownloadResult_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateDownloadResult_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateDownloadResult methods ***/
#define IUpdateDownloadResult_get_HResult(This,retval) (This)->lpVtbl->get_HResult(This,retval)
#define IUpdateDownloadResult_get_ResultCode(This,retval) (This)->lpVtbl->get_ResultCode(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateDownloadResult_QueryInterface(IUpdateDownloadResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateDownloadResult_AddRef(IUpdateDownloadResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateDownloadResult_Release(IUpdateDownloadResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateDownloadResult_GetTypeInfoCount(IUpdateDownloadResult* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateDownloadResult_GetTypeInfo(IUpdateDownloadResult* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateDownloadResult_GetIDsOfNames(IUpdateDownloadResult* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateDownloadResult_Invoke(IUpdateDownloadResult* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateDownloadResult methods ***/
static inline HRESULT IUpdateDownloadResult_get_HResult(IUpdateDownloadResult* This,LONG *retval) {
    return This->lpVtbl->get_HResult(This,retval);
}
static inline HRESULT IUpdateDownloadResult_get_ResultCode(IUpdateDownloadResult* This,OperationResultCode *retval) {
    return This->lpVtbl->get_ResultCode(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateDownloadResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDownloadProgress interface
 */
#ifndef __IDownloadProgress_INTERFACE_DEFINED__
#define __IDownloadProgress_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDownloadProgress, 0xd31a5bac, 0xf719, 0x4178, 0x9d,0xbb, 0x5e,0x2c,0xb4,0x7f,0xd1,0x8a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d31a5bac-f719-4178-9dbb-5e2cb47fd18a")
IDownloadProgress : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_CurrentUpdateBytesDownloaded(
        DECIMAL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentUpdateBytesToDownload(
        DECIMAL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentUpdateIndex(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PercentComplete(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TotalBytesDownloaded(
        DECIMAL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_TotalBytesToDownload(
        DECIMAL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUpdateResult(
        LONG updateIndex,
        IUpdateDownloadResult **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentUpdateDownloadPhase(
        DownloadPhase *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentUpdatePercentComplete(
        LONG *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDownloadProgress, 0xd31a5bac, 0xf719, 0x4178, 0x9d,0xbb, 0x5e,0x2c,0xb4,0x7f,0xd1,0x8a)
#endif
#else
typedef struct IDownloadProgressVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDownloadProgress *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDownloadProgress *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDownloadProgress *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IDownloadProgress *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IDownloadProgress *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IDownloadProgress *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IDownloadProgress *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IDownloadProgress methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CurrentUpdateBytesDownloaded)(
        IDownloadProgress *This,
        DECIMAL *retval);

    HRESULT (STDMETHODCALLTYPE *get_CurrentUpdateBytesToDownload)(
        IDownloadProgress *This,
        DECIMAL *retval);

    HRESULT (STDMETHODCALLTYPE *get_CurrentUpdateIndex)(
        IDownloadProgress *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_PercentComplete)(
        IDownloadProgress *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_TotalBytesDownloaded)(
        IDownloadProgress *This,
        DECIMAL *retval);

    HRESULT (STDMETHODCALLTYPE *get_TotalBytesToDownload)(
        IDownloadProgress *This,
        DECIMAL *retval);

    HRESULT (STDMETHODCALLTYPE *GetUpdateResult)(
        IDownloadProgress *This,
        LONG updateIndex,
        IUpdateDownloadResult **retval);

    HRESULT (STDMETHODCALLTYPE *get_CurrentUpdateDownloadPhase)(
        IDownloadProgress *This,
        DownloadPhase *retval);

    HRESULT (STDMETHODCALLTYPE *get_CurrentUpdatePercentComplete)(
        IDownloadProgress *This,
        LONG *retval);

    END_INTERFACE
} IDownloadProgressVtbl;

interface IDownloadProgress {
    CONST_VTBL IDownloadProgressVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDownloadProgress_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDownloadProgress_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDownloadProgress_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IDownloadProgress_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IDownloadProgress_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IDownloadProgress_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IDownloadProgress_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IDownloadProgress methods ***/
#define IDownloadProgress_get_CurrentUpdateBytesDownloaded(This,retval) (This)->lpVtbl->get_CurrentUpdateBytesDownloaded(This,retval)
#define IDownloadProgress_get_CurrentUpdateBytesToDownload(This,retval) (This)->lpVtbl->get_CurrentUpdateBytesToDownload(This,retval)
#define IDownloadProgress_get_CurrentUpdateIndex(This,retval) (This)->lpVtbl->get_CurrentUpdateIndex(This,retval)
#define IDownloadProgress_get_PercentComplete(This,retval) (This)->lpVtbl->get_PercentComplete(This,retval)
#define IDownloadProgress_get_TotalBytesDownloaded(This,retval) (This)->lpVtbl->get_TotalBytesDownloaded(This,retval)
#define IDownloadProgress_get_TotalBytesToDownload(This,retval) (This)->lpVtbl->get_TotalBytesToDownload(This,retval)
#define IDownloadProgress_GetUpdateResult(This,updateIndex,retval) (This)->lpVtbl->GetUpdateResult(This,updateIndex,retval)
#define IDownloadProgress_get_CurrentUpdateDownloadPhase(This,retval) (This)->lpVtbl->get_CurrentUpdateDownloadPhase(This,retval)
#define IDownloadProgress_get_CurrentUpdatePercentComplete(This,retval) (This)->lpVtbl->get_CurrentUpdatePercentComplete(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IDownloadProgress_QueryInterface(IDownloadProgress* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDownloadProgress_AddRef(IDownloadProgress* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDownloadProgress_Release(IDownloadProgress* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IDownloadProgress_GetTypeInfoCount(IDownloadProgress* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IDownloadProgress_GetTypeInfo(IDownloadProgress* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IDownloadProgress_GetIDsOfNames(IDownloadProgress* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IDownloadProgress_Invoke(IDownloadProgress* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IDownloadProgress methods ***/
static inline HRESULT IDownloadProgress_get_CurrentUpdateBytesDownloaded(IDownloadProgress* This,DECIMAL *retval) {
    return This->lpVtbl->get_CurrentUpdateBytesDownloaded(This,retval);
}
static inline HRESULT IDownloadProgress_get_CurrentUpdateBytesToDownload(IDownloadProgress* This,DECIMAL *retval) {
    return This->lpVtbl->get_CurrentUpdateBytesToDownload(This,retval);
}
static inline HRESULT IDownloadProgress_get_CurrentUpdateIndex(IDownloadProgress* This,LONG *retval) {
    return This->lpVtbl->get_CurrentUpdateIndex(This,retval);
}
static inline HRESULT IDownloadProgress_get_PercentComplete(IDownloadProgress* This,LONG *retval) {
    return This->lpVtbl->get_PercentComplete(This,retval);
}
static inline HRESULT IDownloadProgress_get_TotalBytesDownloaded(IDownloadProgress* This,DECIMAL *retval) {
    return This->lpVtbl->get_TotalBytesDownloaded(This,retval);
}
static inline HRESULT IDownloadProgress_get_TotalBytesToDownload(IDownloadProgress* This,DECIMAL *retval) {
    return This->lpVtbl->get_TotalBytesToDownload(This,retval);
}
static inline HRESULT IDownloadProgress_GetUpdateResult(IDownloadProgress* This,LONG updateIndex,IUpdateDownloadResult **retval) {
    return This->lpVtbl->GetUpdateResult(This,updateIndex,retval);
}
static inline HRESULT IDownloadProgress_get_CurrentUpdateDownloadPhase(IDownloadProgress* This,DownloadPhase *retval) {
    return This->lpVtbl->get_CurrentUpdateDownloadPhase(This,retval);
}
static inline HRESULT IDownloadProgress_get_CurrentUpdatePercentComplete(IDownloadProgress* This,LONG *retval) {
    return This->lpVtbl->get_CurrentUpdatePercentComplete(This,retval);
}
#endif
#endif

#endif


#endif  /* __IDownloadProgress_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDownloadJob interface
 */
#ifndef __IDownloadJob_INTERFACE_DEFINED__
#define __IDownloadJob_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDownloadJob, 0xc574de85, 0x7358, 0x43f6, 0xaa,0xe8, 0x86,0x97,0xe6,0x2d,0x8b,0xa7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c574de85-7358-43f6-aae8-8697e62d8ba7")
IDownloadJob : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_AsyncState(
        VARIANT *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsCompleted(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Updates(
        IUpdateCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE CleanUp(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProgress(
        IDownloadProgress **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestAbort(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDownloadJob, 0xc574de85, 0x7358, 0x43f6, 0xaa,0xe8, 0x86,0x97,0xe6,0x2d,0x8b,0xa7)
#endif
#else
typedef struct IDownloadJobVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDownloadJob *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDownloadJob *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDownloadJob *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IDownloadJob *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IDownloadJob *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IDownloadJob *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IDownloadJob *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IDownloadJob methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AsyncState)(
        IDownloadJob *This,
        VARIANT *retval);

    HRESULT (STDMETHODCALLTYPE *get_IsCompleted)(
        IDownloadJob *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_Updates)(
        IDownloadJob *This,
        IUpdateCollection **retval);

    HRESULT (STDMETHODCALLTYPE *CleanUp)(
        IDownloadJob *This);

    HRESULT (STDMETHODCALLTYPE *GetProgress)(
        IDownloadJob *This,
        IDownloadProgress **retval);

    HRESULT (STDMETHODCALLTYPE *RequestAbort)(
        IDownloadJob *This);

    END_INTERFACE
} IDownloadJobVtbl;

interface IDownloadJob {
    CONST_VTBL IDownloadJobVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDownloadJob_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDownloadJob_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDownloadJob_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IDownloadJob_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IDownloadJob_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IDownloadJob_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IDownloadJob_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IDownloadJob methods ***/
#define IDownloadJob_get_AsyncState(This,retval) (This)->lpVtbl->get_AsyncState(This,retval)
#define IDownloadJob_get_IsCompleted(This,retval) (This)->lpVtbl->get_IsCompleted(This,retval)
#define IDownloadJob_get_Updates(This,retval) (This)->lpVtbl->get_Updates(This,retval)
#define IDownloadJob_CleanUp(This) (This)->lpVtbl->CleanUp(This)
#define IDownloadJob_GetProgress(This,retval) (This)->lpVtbl->GetProgress(This,retval)
#define IDownloadJob_RequestAbort(This) (This)->lpVtbl->RequestAbort(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDownloadJob_QueryInterface(IDownloadJob* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDownloadJob_AddRef(IDownloadJob* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDownloadJob_Release(IDownloadJob* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IDownloadJob_GetTypeInfoCount(IDownloadJob* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IDownloadJob_GetTypeInfo(IDownloadJob* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IDownloadJob_GetIDsOfNames(IDownloadJob* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IDownloadJob_Invoke(IDownloadJob* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IDownloadJob methods ***/
static inline HRESULT IDownloadJob_get_AsyncState(IDownloadJob* This,VARIANT *retval) {
    return This->lpVtbl->get_AsyncState(This,retval);
}
static inline HRESULT IDownloadJob_get_IsCompleted(IDownloadJob* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsCompleted(This,retval);
}
static inline HRESULT IDownloadJob_get_Updates(IDownloadJob* This,IUpdateCollection **retval) {
    return This->lpVtbl->get_Updates(This,retval);
}
static inline HRESULT IDownloadJob_CleanUp(IDownloadJob* This) {
    return This->lpVtbl->CleanUp(This);
}
static inline HRESULT IDownloadJob_GetProgress(IDownloadJob* This,IDownloadProgress **retval) {
    return This->lpVtbl->GetProgress(This,retval);
}
static inline HRESULT IDownloadJob_RequestAbort(IDownloadJob* This) {
    return This->lpVtbl->RequestAbort(This);
}
#endif
#endif

#endif


#endif  /* __IDownloadJob_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDownloadResult interface
 */
#ifndef __IDownloadResult_INTERFACE_DEFINED__
#define __IDownloadResult_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDownloadResult, 0xdaa4fdd0, 0x4727, 0x4dbe, 0xa1,0xe7, 0x74,0x5d,0xca,0x31,0x71,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("daa4fdd0-4727-4dbe-a1e7-745dca317144")
IDownloadResult : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_HResult(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ResultCode(
        OperationResultCode *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUpdateResult(
        LONG updateIndex,
        IUpdateDownloadResult **retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDownloadResult, 0xdaa4fdd0, 0x4727, 0x4dbe, 0xa1,0xe7, 0x74,0x5d,0xca,0x31,0x71,0x44)
#endif
#else
typedef struct IDownloadResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDownloadResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDownloadResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDownloadResult *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IDownloadResult *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IDownloadResult *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IDownloadResult *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IDownloadResult *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IDownloadResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_HResult)(
        IDownloadResult *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_ResultCode)(
        IDownloadResult *This,
        OperationResultCode *retval);

    HRESULT (STDMETHODCALLTYPE *GetUpdateResult)(
        IDownloadResult *This,
        LONG updateIndex,
        IUpdateDownloadResult **retval);

    END_INTERFACE
} IDownloadResultVtbl;

interface IDownloadResult {
    CONST_VTBL IDownloadResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDownloadResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDownloadResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDownloadResult_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IDownloadResult_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IDownloadResult_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IDownloadResult_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IDownloadResult_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IDownloadResult methods ***/
#define IDownloadResult_get_HResult(This,retval) (This)->lpVtbl->get_HResult(This,retval)
#define IDownloadResult_get_ResultCode(This,retval) (This)->lpVtbl->get_ResultCode(This,retval)
#define IDownloadResult_GetUpdateResult(This,updateIndex,retval) (This)->lpVtbl->GetUpdateResult(This,updateIndex,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IDownloadResult_QueryInterface(IDownloadResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDownloadResult_AddRef(IDownloadResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDownloadResult_Release(IDownloadResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IDownloadResult_GetTypeInfoCount(IDownloadResult* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IDownloadResult_GetTypeInfo(IDownloadResult* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IDownloadResult_GetIDsOfNames(IDownloadResult* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IDownloadResult_Invoke(IDownloadResult* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IDownloadResult methods ***/
static inline HRESULT IDownloadResult_get_HResult(IDownloadResult* This,LONG *retval) {
    return This->lpVtbl->get_HResult(This,retval);
}
static inline HRESULT IDownloadResult_get_ResultCode(IDownloadResult* This,OperationResultCode *retval) {
    return This->lpVtbl->get_ResultCode(This,retval);
}
static inline HRESULT IDownloadResult_GetUpdateResult(IDownloadResult* This,LONG updateIndex,IUpdateDownloadResult **retval) {
    return This->lpVtbl->GetUpdateResult(This,updateIndex,retval);
}
#endif
#endif

#endif


#endif  /* __IDownloadResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateDownloader interface
 */
#ifndef __IUpdateDownloader_INTERFACE_DEFINED__
#define __IUpdateDownloader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateDownloader, 0x68f1c6f9, 0x7ecc, 0x4666, 0xa4,0x64, 0x24,0x7f,0xe1,0x24,0x96,0xc3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("68f1c6f9-7ecc-4666-a464-247fe12496c3")
IUpdateDownloader : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ClientApplicationID(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ClientApplicationID(
        BSTR value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsForced(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IsForced(
        VARIANT_BOOL value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Priority(
        DownloadPriority *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Priority(
        DownloadPriority value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Updates(
        IUpdateCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Updates(
        IUpdateCollection *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginDownload(
        IUnknown *onProgressChanged,
        IUnknown *onCompleted,
        VARIANT state,
        IDownloadJob **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE Download(
        IDownloadResult **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndDownload(
        IDownloadJob *value,
        IDownloadResult **retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateDownloader, 0x68f1c6f9, 0x7ecc, 0x4666, 0xa4,0x64, 0x24,0x7f,0xe1,0x24,0x96,0xc3)
#endif
#else
typedef struct IUpdateDownloaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateDownloader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateDownloader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateDownloader *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateDownloader *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateDownloader *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateDownloader *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateDownloader *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateDownloader methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ClientApplicationID)(
        IUpdateDownloader *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *put_ClientApplicationID)(
        IUpdateDownloader *This,
        BSTR value);

    HRESULT (STDMETHODCALLTYPE *get_IsForced)(
        IUpdateDownloader *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *put_IsForced)(
        IUpdateDownloader *This,
        VARIANT_BOOL value);

    HRESULT (STDMETHODCALLTYPE *get_Priority)(
        IUpdateDownloader *This,
        DownloadPriority *retval);

    HRESULT (STDMETHODCALLTYPE *put_Priority)(
        IUpdateDownloader *This,
        DownloadPriority value);

    HRESULT (STDMETHODCALLTYPE *get_Updates)(
        IUpdateDownloader *This,
        IUpdateCollection **retval);

    HRESULT (STDMETHODCALLTYPE *put_Updates)(
        IUpdateDownloader *This,
        IUpdateCollection *value);

    HRESULT (STDMETHODCALLTYPE *BeginDownload)(
        IUpdateDownloader *This,
        IUnknown *onProgressChanged,
        IUnknown *onCompleted,
        VARIANT state,
        IDownloadJob **retval);

    HRESULT (STDMETHODCALLTYPE *Download)(
        IUpdateDownloader *This,
        IDownloadResult **retval);

    HRESULT (STDMETHODCALLTYPE *EndDownload)(
        IUpdateDownloader *This,
        IDownloadJob *value,
        IDownloadResult **retval);

    END_INTERFACE
} IUpdateDownloaderVtbl;

interface IUpdateDownloader {
    CONST_VTBL IUpdateDownloaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateDownloader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateDownloader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateDownloader_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateDownloader_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateDownloader_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateDownloader_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateDownloader_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateDownloader methods ***/
#define IUpdateDownloader_get_ClientApplicationID(This,retval) (This)->lpVtbl->get_ClientApplicationID(This,retval)
#define IUpdateDownloader_put_ClientApplicationID(This,value) (This)->lpVtbl->put_ClientApplicationID(This,value)
#define IUpdateDownloader_get_IsForced(This,retval) (This)->lpVtbl->get_IsForced(This,retval)
#define IUpdateDownloader_put_IsForced(This,value) (This)->lpVtbl->put_IsForced(This,value)
#define IUpdateDownloader_get_Priority(This,retval) (This)->lpVtbl->get_Priority(This,retval)
#define IUpdateDownloader_put_Priority(This,value) (This)->lpVtbl->put_Priority(This,value)
#define IUpdateDownloader_get_Updates(This,retval) (This)->lpVtbl->get_Updates(This,retval)
#define IUpdateDownloader_put_Updates(This,value) (This)->lpVtbl->put_Updates(This,value)
#define IUpdateDownloader_BeginDownload(This,onProgressChanged,onCompleted,state,retval) (This)->lpVtbl->BeginDownload(This,onProgressChanged,onCompleted,state,retval)
#define IUpdateDownloader_Download(This,retval) (This)->lpVtbl->Download(This,retval)
#define IUpdateDownloader_EndDownload(This,value,retval) (This)->lpVtbl->EndDownload(This,value,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateDownloader_QueryInterface(IUpdateDownloader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateDownloader_AddRef(IUpdateDownloader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateDownloader_Release(IUpdateDownloader* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateDownloader_GetTypeInfoCount(IUpdateDownloader* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateDownloader_GetTypeInfo(IUpdateDownloader* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateDownloader_GetIDsOfNames(IUpdateDownloader* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateDownloader_Invoke(IUpdateDownloader* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateDownloader methods ***/
static inline HRESULT IUpdateDownloader_get_ClientApplicationID(IUpdateDownloader* This,BSTR *retval) {
    return This->lpVtbl->get_ClientApplicationID(This,retval);
}
static inline HRESULT IUpdateDownloader_put_ClientApplicationID(IUpdateDownloader* This,BSTR value) {
    return This->lpVtbl->put_ClientApplicationID(This,value);
}
static inline HRESULT IUpdateDownloader_get_IsForced(IUpdateDownloader* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsForced(This,retval);
}
static inline HRESULT IUpdateDownloader_put_IsForced(IUpdateDownloader* This,VARIANT_BOOL value) {
    return This->lpVtbl->put_IsForced(This,value);
}
static inline HRESULT IUpdateDownloader_get_Priority(IUpdateDownloader* This,DownloadPriority *retval) {
    return This->lpVtbl->get_Priority(This,retval);
}
static inline HRESULT IUpdateDownloader_put_Priority(IUpdateDownloader* This,DownloadPriority value) {
    return This->lpVtbl->put_Priority(This,value);
}
static inline HRESULT IUpdateDownloader_get_Updates(IUpdateDownloader* This,IUpdateCollection **retval) {
    return This->lpVtbl->get_Updates(This,retval);
}
static inline HRESULT IUpdateDownloader_put_Updates(IUpdateDownloader* This,IUpdateCollection *value) {
    return This->lpVtbl->put_Updates(This,value);
}
static inline HRESULT IUpdateDownloader_BeginDownload(IUpdateDownloader* This,IUnknown *onProgressChanged,IUnknown *onCompleted,VARIANT state,IDownloadJob **retval) {
    return This->lpVtbl->BeginDownload(This,onProgressChanged,onCompleted,state,retval);
}
static inline HRESULT IUpdateDownloader_Download(IUpdateDownloader* This,IDownloadResult **retval) {
    return This->lpVtbl->Download(This,retval);
}
static inline HRESULT IUpdateDownloader_EndDownload(IUpdateDownloader* This,IDownloadJob *value,IDownloadResult **retval) {
    return This->lpVtbl->EndDownload(This,value,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateDownloader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateInstallationResult interface
 */
#ifndef __IUpdateInstallationResult_INTERFACE_DEFINED__
#define __IUpdateInstallationResult_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateInstallationResult, 0xd940f0f8, 0x3cbb, 0x4fd0, 0x99,0x3f, 0x47,0x1e,0x7f,0x23,0x28,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d940f0f8-3cbb-4fd0-993f-471e7f2328ad")
IUpdateInstallationResult : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_HResult(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RebootRequired(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ResultCode(
        OperationResultCode *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateInstallationResult, 0xd940f0f8, 0x3cbb, 0x4fd0, 0x99,0x3f, 0x47,0x1e,0x7f,0x23,0x28,0xad)
#endif
#else
typedef struct IUpdateInstallationResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateInstallationResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateInstallationResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateInstallationResult *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateInstallationResult *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateInstallationResult *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateInstallationResult *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateInstallationResult *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateInstallationResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_HResult)(
        IUpdateInstallationResult *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_RebootRequired)(
        IUpdateInstallationResult *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_ResultCode)(
        IUpdateInstallationResult *This,
        OperationResultCode *retval);

    END_INTERFACE
} IUpdateInstallationResultVtbl;

interface IUpdateInstallationResult {
    CONST_VTBL IUpdateInstallationResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateInstallationResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateInstallationResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateInstallationResult_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateInstallationResult_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateInstallationResult_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateInstallationResult_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateInstallationResult_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateInstallationResult methods ***/
#define IUpdateInstallationResult_get_HResult(This,retval) (This)->lpVtbl->get_HResult(This,retval)
#define IUpdateInstallationResult_get_RebootRequired(This,retval) (This)->lpVtbl->get_RebootRequired(This,retval)
#define IUpdateInstallationResult_get_ResultCode(This,retval) (This)->lpVtbl->get_ResultCode(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateInstallationResult_QueryInterface(IUpdateInstallationResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateInstallationResult_AddRef(IUpdateInstallationResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateInstallationResult_Release(IUpdateInstallationResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateInstallationResult_GetTypeInfoCount(IUpdateInstallationResult* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateInstallationResult_GetTypeInfo(IUpdateInstallationResult* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateInstallationResult_GetIDsOfNames(IUpdateInstallationResult* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateInstallationResult_Invoke(IUpdateInstallationResult* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateInstallationResult methods ***/
static inline HRESULT IUpdateInstallationResult_get_HResult(IUpdateInstallationResult* This,LONG *retval) {
    return This->lpVtbl->get_HResult(This,retval);
}
static inline HRESULT IUpdateInstallationResult_get_RebootRequired(IUpdateInstallationResult* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_RebootRequired(This,retval);
}
static inline HRESULT IUpdateInstallationResult_get_ResultCode(IUpdateInstallationResult* This,OperationResultCode *retval) {
    return This->lpVtbl->get_ResultCode(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateInstallationResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInstallationProgress interface
 */
#ifndef __IInstallationProgress_INTERFACE_DEFINED__
#define __IInstallationProgress_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInstallationProgress, 0x345c8244, 0x43a3, 0x4e32, 0xa3,0x68, 0x65,0xf0,0x73,0xb7,0x6f,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("345c8244-43a3-4e32-a368-65f073b76f36")
IInstallationProgress : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_CurrentUpdateIndex(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentUpdatePercentComplete(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_PercentComplete(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUpdateResult(
        LONG updateIndex,
        IUpdateInstallationResult **retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInstallationProgress, 0x345c8244, 0x43a3, 0x4e32, 0xa3,0x68, 0x65,0xf0,0x73,0xb7,0x6f,0x36)
#endif
#else
typedef struct IInstallationProgressVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInstallationProgress *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInstallationProgress *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInstallationProgress *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInstallationProgress *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInstallationProgress *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInstallationProgress *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInstallationProgress *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInstallationProgress methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CurrentUpdateIndex)(
        IInstallationProgress *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_CurrentUpdatePercentComplete)(
        IInstallationProgress *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_PercentComplete)(
        IInstallationProgress *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *GetUpdateResult)(
        IInstallationProgress *This,
        LONG updateIndex,
        IUpdateInstallationResult **retval);

    END_INTERFACE
} IInstallationProgressVtbl;

interface IInstallationProgress {
    CONST_VTBL IInstallationProgressVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInstallationProgress_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInstallationProgress_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInstallationProgress_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInstallationProgress_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInstallationProgress_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInstallationProgress_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInstallationProgress_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInstallationProgress methods ***/
#define IInstallationProgress_get_CurrentUpdateIndex(This,retval) (This)->lpVtbl->get_CurrentUpdateIndex(This,retval)
#define IInstallationProgress_get_CurrentUpdatePercentComplete(This,retval) (This)->lpVtbl->get_CurrentUpdatePercentComplete(This,retval)
#define IInstallationProgress_get_PercentComplete(This,retval) (This)->lpVtbl->get_PercentComplete(This,retval)
#define IInstallationProgress_GetUpdateResult(This,updateIndex,retval) (This)->lpVtbl->GetUpdateResult(This,updateIndex,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IInstallationProgress_QueryInterface(IInstallationProgress* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInstallationProgress_AddRef(IInstallationProgress* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInstallationProgress_Release(IInstallationProgress* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInstallationProgress_GetTypeInfoCount(IInstallationProgress* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInstallationProgress_GetTypeInfo(IInstallationProgress* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInstallationProgress_GetIDsOfNames(IInstallationProgress* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInstallationProgress_Invoke(IInstallationProgress* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInstallationProgress methods ***/
static inline HRESULT IInstallationProgress_get_CurrentUpdateIndex(IInstallationProgress* This,LONG *retval) {
    return This->lpVtbl->get_CurrentUpdateIndex(This,retval);
}
static inline HRESULT IInstallationProgress_get_CurrentUpdatePercentComplete(IInstallationProgress* This,LONG *retval) {
    return This->lpVtbl->get_CurrentUpdatePercentComplete(This,retval);
}
static inline HRESULT IInstallationProgress_get_PercentComplete(IInstallationProgress* This,LONG *retval) {
    return This->lpVtbl->get_PercentComplete(This,retval);
}
static inline HRESULT IInstallationProgress_GetUpdateResult(IInstallationProgress* This,LONG updateIndex,IUpdateInstallationResult **retval) {
    return This->lpVtbl->GetUpdateResult(This,updateIndex,retval);
}
#endif
#endif

#endif


#endif  /* __IInstallationProgress_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInstallationJob interface
 */
#ifndef __IInstallationJob_INTERFACE_DEFINED__
#define __IInstallationJob_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInstallationJob, 0x5c209f0b, 0xbad5, 0x432a, 0x95,0x56, 0x46,0x99,0xbe,0xd2,0x63,0x8a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5c209f0b-bad5-432a-9556-4699bed2638a")
IInstallationJob : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_AsyncState(
        VARIANT *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsCompleted(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Updates(
        IUpdateCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE CleanUp(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProgress(
        IInstallationProgress **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE RequestAbort(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInstallationJob, 0x5c209f0b, 0xbad5, 0x432a, 0x95,0x56, 0x46,0x99,0xbe,0xd2,0x63,0x8a)
#endif
#else
typedef struct IInstallationJobVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInstallationJob *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInstallationJob *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInstallationJob *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInstallationJob *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInstallationJob *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInstallationJob *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInstallationJob *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInstallationJob methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AsyncState)(
        IInstallationJob *This,
        VARIANT *retval);

    HRESULT (STDMETHODCALLTYPE *get_IsCompleted)(
        IInstallationJob *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_Updates)(
        IInstallationJob *This,
        IUpdateCollection **retval);

    HRESULT (STDMETHODCALLTYPE *CleanUp)(
        IInstallationJob *This);

    HRESULT (STDMETHODCALLTYPE *GetProgress)(
        IInstallationJob *This,
        IInstallationProgress **retval);

    HRESULT (STDMETHODCALLTYPE *RequestAbort)(
        IInstallationJob *This);

    END_INTERFACE
} IInstallationJobVtbl;

interface IInstallationJob {
    CONST_VTBL IInstallationJobVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInstallationJob_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInstallationJob_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInstallationJob_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInstallationJob_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInstallationJob_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInstallationJob_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInstallationJob_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInstallationJob methods ***/
#define IInstallationJob_get_AsyncState(This,retval) (This)->lpVtbl->get_AsyncState(This,retval)
#define IInstallationJob_get_IsCompleted(This,retval) (This)->lpVtbl->get_IsCompleted(This,retval)
#define IInstallationJob_get_Updates(This,retval) (This)->lpVtbl->get_Updates(This,retval)
#define IInstallationJob_CleanUp(This) (This)->lpVtbl->CleanUp(This)
#define IInstallationJob_GetProgress(This,retval) (This)->lpVtbl->GetProgress(This,retval)
#define IInstallationJob_RequestAbort(This) (This)->lpVtbl->RequestAbort(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IInstallationJob_QueryInterface(IInstallationJob* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInstallationJob_AddRef(IInstallationJob* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInstallationJob_Release(IInstallationJob* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInstallationJob_GetTypeInfoCount(IInstallationJob* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInstallationJob_GetTypeInfo(IInstallationJob* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInstallationJob_GetIDsOfNames(IInstallationJob* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInstallationJob_Invoke(IInstallationJob* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInstallationJob methods ***/
static inline HRESULT IInstallationJob_get_AsyncState(IInstallationJob* This,VARIANT *retval) {
    return This->lpVtbl->get_AsyncState(This,retval);
}
static inline HRESULT IInstallationJob_get_IsCompleted(IInstallationJob* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsCompleted(This,retval);
}
static inline HRESULT IInstallationJob_get_Updates(IInstallationJob* This,IUpdateCollection **retval) {
    return This->lpVtbl->get_Updates(This,retval);
}
static inline HRESULT IInstallationJob_CleanUp(IInstallationJob* This) {
    return This->lpVtbl->CleanUp(This);
}
static inline HRESULT IInstallationJob_GetProgress(IInstallationJob* This,IInstallationProgress **retval) {
    return This->lpVtbl->GetProgress(This,retval);
}
static inline HRESULT IInstallationJob_RequestAbort(IInstallationJob* This) {
    return This->lpVtbl->RequestAbort(This);
}
#endif
#endif

#endif


#endif  /* __IInstallationJob_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IInstallationResult interface
 */
#ifndef __IInstallationResult_INTERFACE_DEFINED__
#define __IInstallationResult_INTERFACE_DEFINED__

DEFINE_GUID(IID_IInstallationResult, 0xa43c56d6, 0x7451, 0x48d4, 0xaf,0x96, 0xb6,0xcd,0x2d,0x0d,0x9b,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a43c56d6-7451-48d4-af96-b6cd2d0d9b7a")
IInstallationResult : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_HResult(
        LONG *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RebootRequired(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ResultCode(
        OperationResultCode *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUpdateResult(
        LONG updateIndex,
        IUpdateInstallationResult **retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IInstallationResult, 0xa43c56d6, 0x7451, 0x48d4, 0xaf,0x96, 0xb6,0xcd,0x2d,0x0d,0x9b,0x7a)
#endif
#else
typedef struct IInstallationResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IInstallationResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IInstallationResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IInstallationResult *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IInstallationResult *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IInstallationResult *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IInstallationResult *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IInstallationResult *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IInstallationResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_HResult)(
        IInstallationResult *This,
        LONG *retval);

    HRESULT (STDMETHODCALLTYPE *get_RebootRequired)(
        IInstallationResult *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *get_ResultCode)(
        IInstallationResult *This,
        OperationResultCode *retval);

    HRESULT (STDMETHODCALLTYPE *GetUpdateResult)(
        IInstallationResult *This,
        LONG updateIndex,
        IUpdateInstallationResult **retval);

    END_INTERFACE
} IInstallationResultVtbl;

interface IInstallationResult {
    CONST_VTBL IInstallationResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IInstallationResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IInstallationResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IInstallationResult_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IInstallationResult_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IInstallationResult_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IInstallationResult_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IInstallationResult_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IInstallationResult methods ***/
#define IInstallationResult_get_HResult(This,retval) (This)->lpVtbl->get_HResult(This,retval)
#define IInstallationResult_get_RebootRequired(This,retval) (This)->lpVtbl->get_RebootRequired(This,retval)
#define IInstallationResult_get_ResultCode(This,retval) (This)->lpVtbl->get_ResultCode(This,retval)
#define IInstallationResult_GetUpdateResult(This,updateIndex,retval) (This)->lpVtbl->GetUpdateResult(This,updateIndex,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IInstallationResult_QueryInterface(IInstallationResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IInstallationResult_AddRef(IInstallationResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IInstallationResult_Release(IInstallationResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IInstallationResult_GetTypeInfoCount(IInstallationResult* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IInstallationResult_GetTypeInfo(IInstallationResult* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IInstallationResult_GetIDsOfNames(IInstallationResult* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IInstallationResult_Invoke(IInstallationResult* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IInstallationResult methods ***/
static inline HRESULT IInstallationResult_get_HResult(IInstallationResult* This,LONG *retval) {
    return This->lpVtbl->get_HResult(This,retval);
}
static inline HRESULT IInstallationResult_get_RebootRequired(IInstallationResult* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_RebootRequired(This,retval);
}
static inline HRESULT IInstallationResult_get_ResultCode(IInstallationResult* This,OperationResultCode *retval) {
    return This->lpVtbl->get_ResultCode(This,retval);
}
static inline HRESULT IInstallationResult_GetUpdateResult(IInstallationResult* This,LONG updateIndex,IUpdateInstallationResult **retval) {
    return This->lpVtbl->GetUpdateResult(This,updateIndex,retval);
}
#endif
#endif

#endif


#endif  /* __IInstallationResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUpdateInstaller interface
 */
#ifndef __IUpdateInstaller_INTERFACE_DEFINED__
#define __IUpdateInstaller_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUpdateInstaller, 0x7b929c68, 0xccdc, 0x4226, 0x96,0xb1, 0x87,0x24,0x60,0x0b,0x54,0xc2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7b929c68-ccdc-4226-96b1-8724600b54c2")
IUpdateInstaller : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ClientApplicationID(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ClientApplicationID(
        BSTR value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsForced(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_IsForced(
        VARIANT_BOOL value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ParentHwnd(
        HWND *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ParentHwnd(
        HWND value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ParentWindow(
        IUnknown *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ParentWindow(
        IUnknown **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Updates(
        IUpdateCollection **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Updates(
        IUpdateCollection *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginInstall(
        IUnknown *onProgressChanged,
        IUnknown *onCompleted,
        VARIANT state,
        IInstallationJob **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginUninstall(
        IUnknown *onProgressChanged,
        IUnknown *onCompleted,
        VARIANT state,
        IInstallationJob **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndInstall(
        IInstallationJob *value,
        IInstallationResult **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndUninstall(
        IInstallationJob *value,
        IInstallationResult **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE Install(
        IInstallationResult **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE RunWizard(
        BSTR dialogTitle,
        IInstallationResult **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_IsBusy(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE Uninstall(
        IInstallationResult **retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AllowSourcePrompts(
        VARIANT_BOOL *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_AllowSourcePrompts(
        VARIANT_BOOL value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RebootRequiredBeforeInstallation(
        VARIANT_BOOL *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUpdateInstaller, 0x7b929c68, 0xccdc, 0x4226, 0x96,0xb1, 0x87,0x24,0x60,0x0b,0x54,0xc2)
#endif
#else
typedef struct IUpdateInstallerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUpdateInstaller *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUpdateInstaller *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUpdateInstaller *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IUpdateInstaller *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IUpdateInstaller *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IUpdateInstaller *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IUpdateInstaller *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IUpdateInstaller methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ClientApplicationID)(
        IUpdateInstaller *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *put_ClientApplicationID)(
        IUpdateInstaller *This,
        BSTR value);

    HRESULT (STDMETHODCALLTYPE *get_IsForced)(
        IUpdateInstaller *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *put_IsForced)(
        IUpdateInstaller *This,
        VARIANT_BOOL value);

    HRESULT (STDMETHODCALLTYPE *get_ParentHwnd)(
        IUpdateInstaller *This,
        HWND *retval);

    HRESULT (STDMETHODCALLTYPE *put_ParentHwnd)(
        IUpdateInstaller *This,
        HWND value);

    HRESULT (STDMETHODCALLTYPE *put_ParentWindow)(
        IUpdateInstaller *This,
        IUnknown *value);

    HRESULT (STDMETHODCALLTYPE *get_ParentWindow)(
        IUpdateInstaller *This,
        IUnknown **retval);

    HRESULT (STDMETHODCALLTYPE *get_Updates)(
        IUpdateInstaller *This,
        IUpdateCollection **retval);

    HRESULT (STDMETHODCALLTYPE *put_Updates)(
        IUpdateInstaller *This,
        IUpdateCollection *value);

    HRESULT (STDMETHODCALLTYPE *BeginInstall)(
        IUpdateInstaller *This,
        IUnknown *onProgressChanged,
        IUnknown *onCompleted,
        VARIANT state,
        IInstallationJob **retval);

    HRESULT (STDMETHODCALLTYPE *BeginUninstall)(
        IUpdateInstaller *This,
        IUnknown *onProgressChanged,
        IUnknown *onCompleted,
        VARIANT state,
        IInstallationJob **retval);

    HRESULT (STDMETHODCALLTYPE *EndInstall)(
        IUpdateInstaller *This,
        IInstallationJob *value,
        IInstallationResult **retval);

    HRESULT (STDMETHODCALLTYPE *EndUninstall)(
        IUpdateInstaller *This,
        IInstallationJob *value,
        IInstallationResult **retval);

    HRESULT (STDMETHODCALLTYPE *Install)(
        IUpdateInstaller *This,
        IInstallationResult **retval);

    HRESULT (STDMETHODCALLTYPE *RunWizard)(
        IUpdateInstaller *This,
        BSTR dialogTitle,
        IInstallationResult **retval);

    HRESULT (STDMETHODCALLTYPE *get_IsBusy)(
        IUpdateInstaller *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *Uninstall)(
        IUpdateInstaller *This,
        IInstallationResult **retval);

    HRESULT (STDMETHODCALLTYPE *get_AllowSourcePrompts)(
        IUpdateInstaller *This,
        VARIANT_BOOL *retval);

    HRESULT (STDMETHODCALLTYPE *put_AllowSourcePrompts)(
        IUpdateInstaller *This,
        VARIANT_BOOL value);

    HRESULT (STDMETHODCALLTYPE *get_RebootRequiredBeforeInstallation)(
        IUpdateInstaller *This,
        VARIANT_BOOL *retval);

    END_INTERFACE
} IUpdateInstallerVtbl;

interface IUpdateInstaller {
    CONST_VTBL IUpdateInstallerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUpdateInstaller_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUpdateInstaller_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUpdateInstaller_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IUpdateInstaller_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IUpdateInstaller_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IUpdateInstaller_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IUpdateInstaller_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IUpdateInstaller methods ***/
#define IUpdateInstaller_get_ClientApplicationID(This,retval) (This)->lpVtbl->get_ClientApplicationID(This,retval)
#define IUpdateInstaller_put_ClientApplicationID(This,value) (This)->lpVtbl->put_ClientApplicationID(This,value)
#define IUpdateInstaller_get_IsForced(This,retval) (This)->lpVtbl->get_IsForced(This,retval)
#define IUpdateInstaller_put_IsForced(This,value) (This)->lpVtbl->put_IsForced(This,value)
#define IUpdateInstaller_get_ParentHwnd(This,retval) (This)->lpVtbl->get_ParentHwnd(This,retval)
#define IUpdateInstaller_put_ParentHwnd(This,value) (This)->lpVtbl->put_ParentHwnd(This,value)
#define IUpdateInstaller_put_ParentWindow(This,value) (This)->lpVtbl->put_ParentWindow(This,value)
#define IUpdateInstaller_get_ParentWindow(This,retval) (This)->lpVtbl->get_ParentWindow(This,retval)
#define IUpdateInstaller_get_Updates(This,retval) (This)->lpVtbl->get_Updates(This,retval)
#define IUpdateInstaller_put_Updates(This,value) (This)->lpVtbl->put_Updates(This,value)
#define IUpdateInstaller_BeginInstall(This,onProgressChanged,onCompleted,state,retval) (This)->lpVtbl->BeginInstall(This,onProgressChanged,onCompleted,state,retval)
#define IUpdateInstaller_BeginUninstall(This,onProgressChanged,onCompleted,state,retval) (This)->lpVtbl->BeginUninstall(This,onProgressChanged,onCompleted,state,retval)
#define IUpdateInstaller_EndInstall(This,value,retval) (This)->lpVtbl->EndInstall(This,value,retval)
#define IUpdateInstaller_EndUninstall(This,value,retval) (This)->lpVtbl->EndUninstall(This,value,retval)
#define IUpdateInstaller_Install(This,retval) (This)->lpVtbl->Install(This,retval)
#define IUpdateInstaller_RunWizard(This,dialogTitle,retval) (This)->lpVtbl->RunWizard(This,dialogTitle,retval)
#define IUpdateInstaller_get_IsBusy(This,retval) (This)->lpVtbl->get_IsBusy(This,retval)
#define IUpdateInstaller_Uninstall(This,retval) (This)->lpVtbl->Uninstall(This,retval)
#define IUpdateInstaller_get_AllowSourcePrompts(This,retval) (This)->lpVtbl->get_AllowSourcePrompts(This,retval)
#define IUpdateInstaller_put_AllowSourcePrompts(This,value) (This)->lpVtbl->put_AllowSourcePrompts(This,value)
#define IUpdateInstaller_get_RebootRequiredBeforeInstallation(This,retval) (This)->lpVtbl->get_RebootRequiredBeforeInstallation(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IUpdateInstaller_QueryInterface(IUpdateInstaller* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUpdateInstaller_AddRef(IUpdateInstaller* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUpdateInstaller_Release(IUpdateInstaller* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IUpdateInstaller_GetTypeInfoCount(IUpdateInstaller* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IUpdateInstaller_GetTypeInfo(IUpdateInstaller* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IUpdateInstaller_GetIDsOfNames(IUpdateInstaller* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IUpdateInstaller_Invoke(IUpdateInstaller* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IUpdateInstaller methods ***/
static inline HRESULT IUpdateInstaller_get_ClientApplicationID(IUpdateInstaller* This,BSTR *retval) {
    return This->lpVtbl->get_ClientApplicationID(This,retval);
}
static inline HRESULT IUpdateInstaller_put_ClientApplicationID(IUpdateInstaller* This,BSTR value) {
    return This->lpVtbl->put_ClientApplicationID(This,value);
}
static inline HRESULT IUpdateInstaller_get_IsForced(IUpdateInstaller* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsForced(This,retval);
}
static inline HRESULT IUpdateInstaller_put_IsForced(IUpdateInstaller* This,VARIANT_BOOL value) {
    return This->lpVtbl->put_IsForced(This,value);
}
static inline HRESULT IUpdateInstaller_get_ParentHwnd(IUpdateInstaller* This,HWND *retval) {
    return This->lpVtbl->get_ParentHwnd(This,retval);
}
static inline HRESULT IUpdateInstaller_put_ParentHwnd(IUpdateInstaller* This,HWND value) {
    return This->lpVtbl->put_ParentHwnd(This,value);
}
static inline HRESULT IUpdateInstaller_put_ParentWindow(IUpdateInstaller* This,IUnknown *value) {
    return This->lpVtbl->put_ParentWindow(This,value);
}
static inline HRESULT IUpdateInstaller_get_ParentWindow(IUpdateInstaller* This,IUnknown **retval) {
    return This->lpVtbl->get_ParentWindow(This,retval);
}
static inline HRESULT IUpdateInstaller_get_Updates(IUpdateInstaller* This,IUpdateCollection **retval) {
    return This->lpVtbl->get_Updates(This,retval);
}
static inline HRESULT IUpdateInstaller_put_Updates(IUpdateInstaller* This,IUpdateCollection *value) {
    return This->lpVtbl->put_Updates(This,value);
}
static inline HRESULT IUpdateInstaller_BeginInstall(IUpdateInstaller* This,IUnknown *onProgressChanged,IUnknown *onCompleted,VARIANT state,IInstallationJob **retval) {
    return This->lpVtbl->BeginInstall(This,onProgressChanged,onCompleted,state,retval);
}
static inline HRESULT IUpdateInstaller_BeginUninstall(IUpdateInstaller* This,IUnknown *onProgressChanged,IUnknown *onCompleted,VARIANT state,IInstallationJob **retval) {
    return This->lpVtbl->BeginUninstall(This,onProgressChanged,onCompleted,state,retval);
}
static inline HRESULT IUpdateInstaller_EndInstall(IUpdateInstaller* This,IInstallationJob *value,IInstallationResult **retval) {
    return This->lpVtbl->EndInstall(This,value,retval);
}
static inline HRESULT IUpdateInstaller_EndUninstall(IUpdateInstaller* This,IInstallationJob *value,IInstallationResult **retval) {
    return This->lpVtbl->EndUninstall(This,value,retval);
}
static inline HRESULT IUpdateInstaller_Install(IUpdateInstaller* This,IInstallationResult **retval) {
    return This->lpVtbl->Install(This,retval);
}
static inline HRESULT IUpdateInstaller_RunWizard(IUpdateInstaller* This,BSTR dialogTitle,IInstallationResult **retval) {
    return This->lpVtbl->RunWizard(This,dialogTitle,retval);
}
static inline HRESULT IUpdateInstaller_get_IsBusy(IUpdateInstaller* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_IsBusy(This,retval);
}
static inline HRESULT IUpdateInstaller_Uninstall(IUpdateInstaller* This,IInstallationResult **retval) {
    return This->lpVtbl->Uninstall(This,retval);
}
static inline HRESULT IUpdateInstaller_get_AllowSourcePrompts(IUpdateInstaller* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_AllowSourcePrompts(This,retval);
}
static inline HRESULT IUpdateInstaller_put_AllowSourcePrompts(IUpdateInstaller* This,VARIANT_BOOL value) {
    return This->lpVtbl->put_AllowSourcePrompts(This,value);
}
static inline HRESULT IUpdateInstaller_get_RebootRequiredBeforeInstallation(IUpdateInstaller* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_RebootRequiredBeforeInstallation(This,retval);
}
#endif
#endif

#endif


#endif  /* __IUpdateInstaller_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISystemInformation interface
 */
#ifndef __ISystemInformation_INTERFACE_DEFINED__
#define __ISystemInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISystemInformation, 0xade87bf7, 0x7b56, 0x4275, 0x8f,0xab, 0xb9,0xb0,0xe5,0x91,0x84,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ade87bf7-7b56-4275-8fab-b9b0e591844b")
ISystemInformation : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_OemHardwareSupportLink(
        BSTR *retval) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RebootRequired(
        VARIANT_BOOL *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISystemInformation, 0xade87bf7, 0x7b56, 0x4275, 0x8f,0xab, 0xb9,0xb0,0xe5,0x91,0x84,0x4b)
#endif
#else
typedef struct ISystemInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISystemInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISystemInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISystemInformation *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        ISystemInformation *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        ISystemInformation *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        ISystemInformation *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        ISystemInformation *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** ISystemInformation methods ***/
    HRESULT (STDMETHODCALLTYPE *get_OemHardwareSupportLink)(
        ISystemInformation *This,
        BSTR *retval);

    HRESULT (STDMETHODCALLTYPE *get_RebootRequired)(
        ISystemInformation *This,
        VARIANT_BOOL *retval);

    END_INTERFACE
} ISystemInformationVtbl;

interface ISystemInformation {
    CONST_VTBL ISystemInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISystemInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISystemInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISystemInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define ISystemInformation_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define ISystemInformation_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define ISystemInformation_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define ISystemInformation_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** ISystemInformation methods ***/
#define ISystemInformation_get_OemHardwareSupportLink(This,retval) (This)->lpVtbl->get_OemHardwareSupportLink(This,retval)
#define ISystemInformation_get_RebootRequired(This,retval) (This)->lpVtbl->get_RebootRequired(This,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT ISystemInformation_QueryInterface(ISystemInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISystemInformation_AddRef(ISystemInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISystemInformation_Release(ISystemInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT ISystemInformation_GetTypeInfoCount(ISystemInformation* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT ISystemInformation_GetTypeInfo(ISystemInformation* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT ISystemInformation_GetIDsOfNames(ISystemInformation* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT ISystemInformation_Invoke(ISystemInformation* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** ISystemInformation methods ***/
static inline HRESULT ISystemInformation_get_OemHardwareSupportLink(ISystemInformation* This,BSTR *retval) {
    return This->lpVtbl->get_OemHardwareSupportLink(This,retval);
}
static inline HRESULT ISystemInformation_get_RebootRequired(ISystemInformation* This,VARIANT_BOOL *retval) {
    return This->lpVtbl->get_RebootRequired(This,retval);
}
#endif
#endif

#endif


#endif  /* __ISystemInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWindowsUpdateAgentInfo interface
 */
#ifndef __IWindowsUpdateAgentInfo_INTERFACE_DEFINED__
#define __IWindowsUpdateAgentInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWindowsUpdateAgentInfo, 0x85713fa1, 0x7796, 0x4fa2, 0xbe,0x3b, 0xe2,0xd6,0x12,0x4d,0xd3,0x73);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("85713fa1-7796-4fa2-be3b-e2d6124dd373")
IWindowsUpdateAgentInfo : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE GetInfo(
        VARIANT varInfoIdentifier,
        VARIANT *retval) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWindowsUpdateAgentInfo, 0x85713fa1, 0x7796, 0x4fa2, 0xbe,0x3b, 0xe2,0xd6,0x12,0x4d,0xd3,0x73)
#endif
#else
typedef struct IWindowsUpdateAgentInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWindowsUpdateAgentInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWindowsUpdateAgentInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWindowsUpdateAgentInfo *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWindowsUpdateAgentInfo *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWindowsUpdateAgentInfo *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWindowsUpdateAgentInfo *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWindowsUpdateAgentInfo *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWindowsUpdateAgentInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetInfo)(
        IWindowsUpdateAgentInfo *This,
        VARIANT varInfoIdentifier,
        VARIANT *retval);

    END_INTERFACE
} IWindowsUpdateAgentInfoVtbl;

interface IWindowsUpdateAgentInfo {
    CONST_VTBL IWindowsUpdateAgentInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWindowsUpdateAgentInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWindowsUpdateAgentInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWindowsUpdateAgentInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWindowsUpdateAgentInfo_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWindowsUpdateAgentInfo_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWindowsUpdateAgentInfo_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWindowsUpdateAgentInfo_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWindowsUpdateAgentInfo methods ***/
#define IWindowsUpdateAgentInfo_GetInfo(This,varInfoIdentifier,retval) (This)->lpVtbl->GetInfo(This,varInfoIdentifier,retval)
#else
/*** IUnknown methods ***/
static inline HRESULT IWindowsUpdateAgentInfo_QueryInterface(IWindowsUpdateAgentInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWindowsUpdateAgentInfo_AddRef(IWindowsUpdateAgentInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWindowsUpdateAgentInfo_Release(IWindowsUpdateAgentInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWindowsUpdateAgentInfo_GetTypeInfoCount(IWindowsUpdateAgentInfo* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWindowsUpdateAgentInfo_GetTypeInfo(IWindowsUpdateAgentInfo* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWindowsUpdateAgentInfo_GetIDsOfNames(IWindowsUpdateAgentInfo* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWindowsUpdateAgentInfo_Invoke(IWindowsUpdateAgentInfo* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWindowsUpdateAgentInfo methods ***/
static inline HRESULT IWindowsUpdateAgentInfo_GetInfo(IWindowsUpdateAgentInfo* This,VARIANT varInfoIdentifier,VARIANT *retval) {
    return This->lpVtbl->GetInfo(This,varInfoIdentifier,retval);
}
#endif
#endif

#endif


#endif  /* __IWindowsUpdateAgentInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * AutomaticUpdates coclass
 */

DEFINE_GUID(CLSID_AutomaticUpdates, 0xbfe18e9c, 0x6d87, 0x4450, 0xb3,0x7c, 0xe0,0x2f,0x0b,0x37,0x38,0x03);

#ifdef __cplusplus
class DECLSPEC_UUID("bfe18e9c-6d87-4450-b37c-e02f0b373803") AutomaticUpdates;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(AutomaticUpdates, 0xbfe18e9c, 0x6d87, 0x4450, 0xb3,0x7c, 0xe0,0x2f,0x0b,0x37,0x38,0x03)
#endif
#endif

/*****************************************************************************
 * UpdateInstaller coclass
 */

DEFINE_GUID(CLSID_UpdateInstaller, 0xd2e0fe7f, 0xd23e, 0x48e1, 0x93,0xc0, 0x6f,0xa8,0xcc,0x34,0x64,0x74);

#ifdef __cplusplus
class DECLSPEC_UUID("d2e0fe7f-d23e-48e1-93c0-6fa8cc346474") UpdateInstaller;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(UpdateInstaller, 0xd2e0fe7f, 0xd23e, 0x48e1, 0x93,0xc0, 0x6f,0xa8,0xcc,0x34,0x64,0x74)
#endif
#endif

/*****************************************************************************
 * UpdateSession coclass
 */

DEFINE_GUID(CLSID_UpdateSession, 0x4cb43d7f, 0x7eee, 0x4906, 0x86,0x98, 0x60,0xda,0x1c,0x38,0xf2,0xfe);

#ifdef __cplusplus
class DECLSPEC_UUID("4cb43d7f-7eee-4906-8698-60da1c38f2fe") UpdateSession;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(UpdateSession, 0x4cb43d7f, 0x7eee, 0x4906, 0x86,0x98, 0x60,0xda,0x1c,0x38,0xf2,0xfe)
#endif
#endif

/*****************************************************************************
 * SystemInformation coclass
 */

DEFINE_GUID(CLSID_SystemInformation, 0xc01b9ba0, 0xbea7, 0x41ba, 0xb6,0x04, 0xd0,0xa3,0x6f,0x46,0x91,0x33);

#ifdef __cplusplus
class DECLSPEC_UUID("c01b9ba0-bea7-41ba-b604-d0a36f469133") SystemInformation;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SystemInformation, 0xc01b9ba0, 0xbea7, 0x41ba, 0xb6,0x04, 0xd0,0xa3,0x6f,0x46,0x91,0x33)
#endif
#endif

/*****************************************************************************
 * WindowsUpdateAgentInfo coclass
 */

DEFINE_GUID(CLSID_WindowsUpdateAgentInfo, 0xc2e88c2f, 0x6f5b, 0x4aaa, 0x89,0x4b, 0x55,0xc8,0x47,0xad,0x3a,0x2d);

#ifdef __cplusplus
class DECLSPEC_UUID("c2e88c2f-6f5b-4aaa-894b-55c847ad3a2d") WindowsUpdateAgentInfo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WindowsUpdateAgentInfo, 0xc2e88c2f, 0x6f5b, 0x4aaa, 0x89,0x4b, 0x55,0xc8,0x47,0xad,0x3a,0x2d)
#endif
#endif

#endif /* __WUApiLib_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wuapi_h__ */
