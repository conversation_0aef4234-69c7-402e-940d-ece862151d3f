/*** Autogenerated by WIDL 10.12 from include/activscp.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __activscp_h__
#define __activscp_h__

/* Forward declarations */

#ifndef __IActiveScriptSite_FWD_DEFINED__
#define __IActiveScriptSite_FWD_DEFINED__
typedef interface IActiveScriptSite IActiveScriptSite;
#ifdef __cplusplus
interface IActiveScriptSite;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptError_FWD_DEFINED__
#define __IActiveScriptError_FWD_DEFINED__
typedef interface IActiveScriptError IActiveScriptError;
#ifdef __cplusplus
interface IActiveScriptError;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptError64_FWD_DEFINED__
#define __IActiveScriptError64_FWD_DEFINED__
typedef interface IActiveScriptError64 IActiveScriptError64;
#ifdef __cplusplus
interface IActiveScriptError64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteWindow_FWD_DEFINED__
#define __IActiveScriptSiteWindow_FWD_DEFINED__
typedef interface IActiveScriptSiteWindow IActiveScriptSiteWindow;
#ifdef __cplusplus
interface IActiveScriptSiteWindow;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteUIControl_FWD_DEFINED__
#define __IActiveScriptSiteUIControl_FWD_DEFINED__
typedef interface IActiveScriptSiteUIControl IActiveScriptSiteUIControl;
#ifdef __cplusplus
interface IActiveScriptSiteUIControl;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteInterruptPoll_FWD_DEFINED__
#define __IActiveScriptSiteInterruptPoll_FWD_DEFINED__
typedef interface IActiveScriptSiteInterruptPoll IActiveScriptSiteInterruptPoll;
#ifdef __cplusplus
interface IActiveScriptSiteInterruptPoll;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScript_FWD_DEFINED__
#define __IActiveScript_FWD_DEFINED__
typedef interface IActiveScript IActiveScript;
#ifdef __cplusplus
interface IActiveScript;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParse32_FWD_DEFINED__
#define __IActiveScriptParse32_FWD_DEFINED__
typedef interface IActiveScriptParse32 IActiveScriptParse32;
#ifdef __cplusplus
interface IActiveScriptParse32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParse64_FWD_DEFINED__
#define __IActiveScriptParse64_FWD_DEFINED__
typedef interface IActiveScriptParse64 IActiveScriptParse64;
#ifdef __cplusplus
interface IActiveScriptParse64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedureOld32_FWD_DEFINED__
#define __IActiveScriptParseProcedureOld32_FWD_DEFINED__
typedef interface IActiveScriptParseProcedureOld32 IActiveScriptParseProcedureOld32;
#ifdef __cplusplus
interface IActiveScriptParseProcedureOld32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedureOld64_FWD_DEFINED__
#define __IActiveScriptParseProcedureOld64_FWD_DEFINED__
typedef interface IActiveScriptParseProcedureOld64 IActiveScriptParseProcedureOld64;
#ifdef __cplusplus
interface IActiveScriptParseProcedureOld64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedure32_FWD_DEFINED__
#define __IActiveScriptParseProcedure32_FWD_DEFINED__
typedef interface IActiveScriptParseProcedure32 IActiveScriptParseProcedure32;
#ifdef __cplusplus
interface IActiveScriptParseProcedure32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedure64_FWD_DEFINED__
#define __IActiveScriptParseProcedure64_FWD_DEFINED__
typedef interface IActiveScriptParseProcedure64 IActiveScriptParseProcedure64;
#ifdef __cplusplus
interface IActiveScriptParseProcedure64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedure2_32_FWD_DEFINED__
#define __IActiveScriptParseProcedure2_32_FWD_DEFINED__
typedef interface IActiveScriptParseProcedure2_32 IActiveScriptParseProcedure2_32;
#ifdef __cplusplus
interface IActiveScriptParseProcedure2_32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedure2_64_FWD_DEFINED__
#define __IActiveScriptParseProcedure2_64_FWD_DEFINED__
typedef interface IActiveScriptParseProcedure2_64 IActiveScriptParseProcedure2_64;
#ifdef __cplusplus
interface IActiveScriptParseProcedure2_64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptEncode_FWD_DEFINED__
#define __IActiveScriptEncode_FWD_DEFINED__
typedef interface IActiveScriptEncode IActiveScriptEncode;
#ifdef __cplusplus
interface IActiveScriptEncode;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptHostEncode_FWD_DEFINED__
#define __IActiveScriptHostEncode_FWD_DEFINED__
typedef interface IActiveScriptHostEncode IActiveScriptHostEncode;
#ifdef __cplusplus
interface IActiveScriptHostEncode;
#endif /* __cplusplus */
#endif

#ifndef __IBindEventHandler_FWD_DEFINED__
#define __IBindEventHandler_FWD_DEFINED__
typedef interface IBindEventHandler IBindEventHandler;
#ifdef __cplusplus
interface IBindEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptStats_FWD_DEFINED__
#define __IActiveScriptStats_FWD_DEFINED__
typedef interface IActiveScriptStats IActiveScriptStats;
#ifdef __cplusplus
interface IActiveScriptStats;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptProperty_FWD_DEFINED__
#define __IActiveScriptProperty_FWD_DEFINED__
typedef interface IActiveScriptProperty IActiveScriptProperty;
#ifdef __cplusplus
interface IActiveScriptProperty;
#endif /* __cplusplus */
#endif

#ifndef __ITridentEventSink_FWD_DEFINED__
#define __ITridentEventSink_FWD_DEFINED__
typedef interface ITridentEventSink ITridentEventSink;
#ifdef __cplusplus
interface ITridentEventSink;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptGarbageCollector_FWD_DEFINED__
#define __IActiveScriptGarbageCollector_FWD_DEFINED__
typedef interface IActiveScriptGarbageCollector IActiveScriptGarbageCollector;
#ifdef __cplusplus
interface IActiveScriptGarbageCollector;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSIPInfo_FWD_DEFINED__
#define __IActiveScriptSIPInfo_FWD_DEFINED__
typedef interface IActiveScriptSIPInfo IActiveScriptSIPInfo;
#ifdef __cplusplus
interface IActiveScriptSIPInfo;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteTraceInfo_FWD_DEFINED__
#define __IActiveScriptSiteTraceInfo_FWD_DEFINED__
typedef interface IActiveScriptSiteTraceInfo IActiveScriptSiteTraceInfo;
#ifdef __cplusplus
interface IActiveScriptSiteTraceInfo;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptTraceInfo_FWD_DEFINED__
#define __IActiveScriptTraceInfo_FWD_DEFINED__
typedef interface IActiveScriptTraceInfo IActiveScriptTraceInfo;
#ifdef __cplusplus
interface IActiveScriptTraceInfo;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptStringCompare_FWD_DEFINED__
#define __IActiveScriptStringCompare_FWD_DEFINED__
typedef interface IActiveScriptStringCompare IActiveScriptStringCompare;
#ifdef __cplusplus
interface IActiveScriptStringCompare;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <ocidl.h>
#include <oleidl.h>
#include <oaidl.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#ifndef __ActivScp_h
#define __ActivScp_h

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)


#ifndef _NO_SCRIPT_GUIDS

DEFINE_GUID(CATID_ActiveScript, 0xf0b7a1a1, 0x9847, 0x11cf, 0x8f, 0x20, 0x00, 0x80, 0x5f, 0x2c, 0xd0, 0x64);
DEFINE_GUID(CATID_ActiveScriptParse, 0xf0b7a1a2, 0x9847, 0x11cf, 0x8f, 0x20, 0x00, 0x80, 0x5f, 0x2c, 0xd0, 0x64);
DEFINE_GUID(CATID_ActiveScriptEncode, 0xf0b7a1a3, 0x9847, 0x11cf, 0x8f, 0x20, 0x00, 0x80, 0x5f, 0x2c, 0xd0, 0x64);
DEFINE_GUID(OID_VBSSIP, 0x1629f04e, 0x2799, 0x4db5, 0x8f, 0xe5, 0xac, 0xe1, 0x0f, 0x17, 0xeb, 0xab);
DEFINE_GUID(OID_JSSIP,  0x6c9e010, 0x38ce, 0x11d4, 0xa2, 0xa3, 0x00, 0x10, 0x4b, 0xd3, 0x50, 0x90);
DEFINE_GUID(OID_WSFSIP, 0x1a610570, 0x38ce, 0x11d4, 0xa2, 0xa3, 0x00, 0x10, 0x4b, 0xd3, 0x50, 0x90);
#endif

#define SCRIPTITEM_ISVISIBLE 0x00000002
#define SCRIPTITEM_ISSOURCE 0x00000004
#define SCRIPTITEM_GLOBALMEMBERS 0x00000008
#define SCRIPTITEM_ISPERSISTENT 0x00000040
#define SCRIPTITEM_CODEONLY 0x00000200
#define SCRIPTITEM_NOCODE 0x00000400

#define SCRIPTITEM_ALL_FLAGS (SCRIPTITEM_ISSOURCE | SCRIPTITEM_ISVISIBLE | SCRIPTITEM_ISPERSISTENT | SCRIPTITEM_GLOBALMEMBERS | SCRIPTITEM_NOCODE | SCRIPTITEM_CODEONLY)

#define SCRIPTTYPELIB_ISCONTROL 0x00000010
#define SCRIPTTYPELIB_ISPERSISTENT 0x00000040

#define SCRIPTTYPELIB_ALL_FLAGS (SCRIPTTYPELIB_ISCONTROL | SCRIPTTYPELIB_ISPERSISTENT)

#define SCRIPTTEXT_DELAYEXECUTION 0x00000001
#define SCRIPTTEXT_ISVISIBLE 0x00000002
#define SCRIPTTEXT_ISEXPRESSION 0x00000020
#define SCRIPTTEXT_ISPERSISTENT 0x00000040
#define SCRIPTTEXT_HOSTMANAGESSOURCE 0x00000080
#define SCRIPTTEXT_ISXDOMAIN 0x00000100

#define SCRIPTTEXT_ALL_FLAGS (SCRIPTTEXT_DELAYEXECUTION | SCRIPTTEXT_ISVISIBLE | SCRIPTTEXT_ISEXPRESSION | SCRIPTTEXT_ISPERSISTENT | SCRIPTTEXT_HOSTMANAGESSOURCE | SCRIPTTEXT_ISXDOMAIN)

#define SCRIPTPROC_ISEXPRESSION 0x00000020
#define SCRIPTPROC_HOSTMANAGESSOURCE 0x00000080
#define SCRIPTPROC_IMPLICIT_THIS 0x00000100
#define SCRIPTPROC_IMPLICIT_PARENTS 0x00000200
#define SCRIPTPROC_ISXDOMAIN 0x00000400

#define SCRIPTPROC_ALL_FLAGS (SCRIPTPROC_HOSTMANAGESSOURCE | SCRIPTPROC_ISEXPRESSION | SCRIPTPROC_IMPLICIT_THIS | SCRIPTPROC_IMPLICIT_PARENTS | SCRIPTPROC_ISXDOMAIN)

#define SCRIPTINFO_IUNKNOWN 0x00000001
#define SCRIPTINFO_ITYPEINFO 0x00000002

#define SCRIPTINFO_ALL_FLAGS (SCRIPTINFO_IUNKNOWN | SCRIPTINFO_ITYPEINFO)

#define SCRIPTINTERRUPT_DEBUG 0x00000001
#define SCRIPTINTERRUPT_RAISEEXCEPTION 0x00000002

#define SCRIPTINTERRUPT_ALL_FLAGS (SCRIPTINTERRUPT_DEBUG | SCRIPTINTERRUPT_RAISEEXCEPTION)

#define SCRIPTSTAT_STATEMENT_COUNT 0x1
#define SCRIPTSTAT_INSTRUCTION_COUNT 0x2
#define SCRIPTSTAT_INTSTRUCTION_TIME 0x3
#define SCRIPTSTAT_TOTAL_TIME 0x4

#define SCRIPT_ENCODE_SECTION 0x1

#define SCRIPT_ENCODE_DEFAULT_LANGUAGE 0x1
#define SCRIPT_ENCODE_NO_ASP_LANGUAGE 0x2

#define SCRIPTPROP_NAME 0x0
#define SCRIPTPROP_MAJORVERSION 0x1
#define SCRIPTPROP_MINORVERSION 0x2
#define SCRIPTPROP_BUILDNUMBER 0x3

#define SCRIPTPROP_DELAYEDEVENTSINKING 0x1000
#define SCRIPTPROP_CATCHEXCEPTION 0x1001
#define SCRIPTPROP_CONVERSIONLCID 0x1002
#define SCRIPTPROP_HOSTSTACKREQUIRED 0x1003

#define SCRIPTPROP_DEBUGGER 0x1100
#define SCRIPTPROP_JITDEBUG 0x1101

#define SCRIPTPROP_GCCONTROLSOFTCLOSE 0x2000

#define SCRIPTPROP_INTEGERMODE 0x3000
#define SCRIPTPROP_STRINGCOMPAREINSTANCE 0x3001

#define SCRIPTPROP_INVOKEVERSIONING 0x4000

#define SCRIPTPROP_HACK_FIBERSUPPORT 0x70000000
#define SCRIPTPROP_HACK_TRIDENTEVENTSINK 0x70000001
#define SCRIPTPROP_ABBREVIATE_GLOBALNAME_RESOLUTION 0x70000002
#define SCRIPTPROP_HOSTKEEPALIVE 0x70000004

#define SCRIPT_E_RECORDED __MSABI_LONG(0x86664004)
#define SCRIPT_E_REPORTED __MSABI_LONG(0x80020101)
#define SCRIPT_E_PROPAGATE __MSABI_LONG(0x80020102)

typedef enum tagSCRIPTLANGUAGEVERSION {
    SCRIPTLANGUAGEVERSION_DEFAULT = 0,
    SCRIPTLANGUAGEVERSION_5_7 = 1,
    SCRIPTLANGUAGEVERSION_5_8 = 2,
    SCRIPTLANGUAGEVERSION_MAX = 255
} SCRIPTLANGUAGEVERSION;

typedef enum tagSCRIPTSTATE {
    SCRIPTSTATE_UNINITIALIZED = 0,
    SCRIPTSTATE_INITIALIZED = 5,
    SCRIPTSTATE_STARTED = 1,
    SCRIPTSTATE_CONNECTED = 2,
    SCRIPTSTATE_DISCONNECTED = 3,
    SCRIPTSTATE_CLOSED = 4
} SCRIPTSTATE;

typedef enum tagSCRIPTTRACEINFO {
    SCRIPTTRACEINFO_SCRIPTSTART = 0,
    SCRIPTTRACEINFO_SCRIPTEND = 1,
    SCRIPTTRACEINFO_COMCALLSTART = 2,
    SCRIPTTRACEINFO_COMCALLEND = 3,
    SCRIPTTRACEINFO_CREATEOBJSTART = 4,
    SCRIPTTRACEINFO_CREATEOBJEND = 5,
    SCRIPTTRACEINFO_GETOBJSTART = 6,
    SCRIPTTRACEINFO_GETOBJEND = 7
} SCRIPTTRACEINFO;

typedef enum tagSCRIPTTHREADSTATE {
    SCRIPTTHREADSTATE_NOTINSCRIPT = 0,
    SCRIPTTHREADSTATE_RUNNING = 1
} SCRIPTTHREADSTATE;

typedef enum tagSCRIPTGCTYPE {
    SCRIPTGCTYPE_NORMAL = 0,
    SCRIPTGCTYPE_EXHAUSTIVE = 1
} SCRIPTGCTYPE;

typedef enum tagSCRIPTUICITEM {
    SCRIPTUICITEM_INPUTBOX = 1,
    SCRIPTUICITEM_MSGBOX = 2
} SCRIPTUICITEM;

typedef enum tagSCRIPTUICHANDLING {
    SCRIPTUICHANDLING_ALLOW = 0,
    SCRIPTUICHANDLING_NOUIERROR = 1,
    SCRIPTUICHANDLING_NOUIDEFAULT = 2
} SCRIPTUICHANDLING;

typedef DWORD SCRIPTTHREADID;

#define SCRIPTTHREADID_CURRENT ((SCRIPTTHREADID)-1)
#define SCRIPTTHREADID_BASE    ((SCRIPTTHREADID)-2)
#define SCRIPTTHREADID_ALL     ((SCRIPTTHREADID)-3)

#ifndef __IActiveScriptSite_FWD_DEFINED__
#define __IActiveScriptSite_FWD_DEFINED__
typedef interface IActiveScriptSite IActiveScriptSite;
#ifdef __cplusplus
interface IActiveScriptSite;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptError_FWD_DEFINED__
#define __IActiveScriptError_FWD_DEFINED__
typedef interface IActiveScriptError IActiveScriptError;
#ifdef __cplusplus
interface IActiveScriptError;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptError64_FWD_DEFINED__
#define __IActiveScriptError64_FWD_DEFINED__
typedef interface IActiveScriptError64 IActiveScriptError64;
#ifdef __cplusplus
interface IActiveScriptError64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteWindow_FWD_DEFINED__
#define __IActiveScriptSiteWindow_FWD_DEFINED__
typedef interface IActiveScriptSiteWindow IActiveScriptSiteWindow;
#ifdef __cplusplus
interface IActiveScriptSiteWindow;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteUIControl_FWD_DEFINED__
#define __IActiveScriptSiteUIControl_FWD_DEFINED__
typedef interface IActiveScriptSiteUIControl IActiveScriptSiteUIControl;
#ifdef __cplusplus
interface IActiveScriptSiteUIControl;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSiteInterruptPoll_FWD_DEFINED__
#define __IActiveScriptSiteInterruptPoll_FWD_DEFINED__
typedef interface IActiveScriptSiteInterruptPoll IActiveScriptSiteInterruptPoll;
#ifdef __cplusplus
interface IActiveScriptSiteInterruptPoll;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScript_FWD_DEFINED__
#define __IActiveScript_FWD_DEFINED__
typedef interface IActiveScript IActiveScript;
#ifdef __cplusplus
interface IActiveScript;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParse32_FWD_DEFINED__
#define __IActiveScriptParse32_FWD_DEFINED__
typedef interface IActiveScriptParse32 IActiveScriptParse32;
#ifdef __cplusplus
interface IActiveScriptParse32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParse64_FWD_DEFINED__
#define __IActiveScriptParse64_FWD_DEFINED__
typedef interface IActiveScriptParse64 IActiveScriptParse64;
#ifdef __cplusplus
interface IActiveScriptParse64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedureOld32_FWD_DEFINED__
#define __IActiveScriptParseProcedureOld32_FWD_DEFINED__
typedef interface IActiveScriptParseProcedureOld32 IActiveScriptParseProcedureOld32;
#ifdef __cplusplus
interface IActiveScriptParseProcedureOld32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedureOld64_FWD_DEFINED__
#define __IActiveScriptParseProcedureOld64_FWD_DEFINED__
typedef interface IActiveScriptParseProcedureOld64 IActiveScriptParseProcedureOld64;
#ifdef __cplusplus
interface IActiveScriptParseProcedureOld64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedure32_FWD_DEFINED__
#define __IActiveScriptParseProcedure32_FWD_DEFINED__
typedef interface IActiveScriptParseProcedure32 IActiveScriptParseProcedure32;
#ifdef __cplusplus
interface IActiveScriptParseProcedure32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedure64_FWD_DEFINED__
#define __IActiveScriptParseProcedure64_FWD_DEFINED__
typedef interface IActiveScriptParseProcedure64 IActiveScriptParseProcedure64;
#ifdef __cplusplus
interface IActiveScriptParseProcedure64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedure2_32_FWD_DEFINED__
#define __IActiveScriptParseProcedure2_32_FWD_DEFINED__
typedef interface IActiveScriptParseProcedure2_32 IActiveScriptParseProcedure2_32;
#ifdef __cplusplus
interface IActiveScriptParseProcedure2_32;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptParseProcedure2_64_FWD_DEFINED__
#define __IActiveScriptParseProcedure2_64_FWD_DEFINED__
typedef interface IActiveScriptParseProcedure2_64 IActiveScriptParseProcedure2_64;
#ifdef __cplusplus
interface IActiveScriptParseProcedure2_64;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptEncode_FWD_DEFINED__
#define __IActiveScriptEncode_FWD_DEFINED__
typedef interface IActiveScriptEncode IActiveScriptEncode;
#ifdef __cplusplus
interface IActiveScriptEncode;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptHostEncode_FWD_DEFINED__
#define __IActiveScriptHostEncode_FWD_DEFINED__
typedef interface IActiveScriptHostEncode IActiveScriptHostEncode;
#ifdef __cplusplus
interface IActiveScriptHostEncode;
#endif /* __cplusplus */
#endif

#ifndef __IBindEventHandler_FWD_DEFINED__
#define __IBindEventHandler_FWD_DEFINED__
typedef interface IBindEventHandler IBindEventHandler;
#ifdef __cplusplus
interface IBindEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptStats_FWD_DEFINED__
#define __IActiveScriptStats_FWD_DEFINED__
typedef interface IActiveScriptStats IActiveScriptStats;
#ifdef __cplusplus
interface IActiveScriptStats;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptProperty_FWD_DEFINED__
#define __IActiveScriptProperty_FWD_DEFINED__
typedef interface IActiveScriptProperty IActiveScriptProperty;
#ifdef __cplusplus
interface IActiveScriptProperty;
#endif /* __cplusplus */
#endif

#ifndef __ITridentEventSink_FWD_DEFINED__
#define __ITridentEventSink_FWD_DEFINED__
typedef interface ITridentEventSink ITridentEventSink;
#ifdef __cplusplus
interface ITridentEventSink;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptGarbageCollector_FWD_DEFINED__
#define __IActiveScriptGarbageCollector_FWD_DEFINED__
typedef interface IActiveScriptGarbageCollector IActiveScriptGarbageCollector;
#ifdef __cplusplus
interface IActiveScriptGarbageCollector;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptSIPInfo_FWD_DEFINED__
#define __IActiveScriptSIPInfo_FWD_DEFINED__
typedef interface IActiveScriptSIPInfo IActiveScriptSIPInfo;
#ifdef __cplusplus
interface IActiveScriptSIPInfo;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptStringCompare_FWD_DEFINED__
#define __IActiveScriptStringCompare_FWD_DEFINED__
typedef interface IActiveScriptStringCompare IActiveScriptStringCompare;
#ifdef __cplusplus
interface IActiveScriptStringCompare;
#endif /* __cplusplus */
#endif


/*****************************************************************************
 * IActiveScriptSite interface
 */
#ifndef __IActiveScriptSite_INTERFACE_DEFINED__
#define __IActiveScriptSite_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptSite, 0xdb01a1e3, 0xa42b, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("db01a1e3-a42b-11cf-8f20-00805f2cd064")
IActiveScriptSite : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetLCID(
        LCID *plcid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetItemInfo(
        LPCOLESTR pstrName,
        DWORD dwReturnMask,
        IUnknown **ppiunkItem,
        ITypeInfo **ppti) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocVersionString(
        BSTR *pbstrVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnScriptTerminate(
        const VARIANT *pvarResult,
        const EXCEPINFO *pexcepinfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnStateChange(
        SCRIPTSTATE ssScriptState) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnScriptError(
        IActiveScriptError *pscripterror) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnEnterScript(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnLeaveScript(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptSite, 0xdb01a1e3, 0xa42b, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64)
#endif
#else
typedef struct IActiveScriptSiteVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptSite *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptSite *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptSite *This);

    /*** IActiveScriptSite methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLCID)(
        IActiveScriptSite *This,
        LCID *plcid);

    HRESULT (STDMETHODCALLTYPE *GetItemInfo)(
        IActiveScriptSite *This,
        LPCOLESTR pstrName,
        DWORD dwReturnMask,
        IUnknown **ppiunkItem,
        ITypeInfo **ppti);

    HRESULT (STDMETHODCALLTYPE *GetDocVersionString)(
        IActiveScriptSite *This,
        BSTR *pbstrVersion);

    HRESULT (STDMETHODCALLTYPE *OnScriptTerminate)(
        IActiveScriptSite *This,
        const VARIANT *pvarResult,
        const EXCEPINFO *pexcepinfo);

    HRESULT (STDMETHODCALLTYPE *OnStateChange)(
        IActiveScriptSite *This,
        SCRIPTSTATE ssScriptState);

    HRESULT (STDMETHODCALLTYPE *OnScriptError)(
        IActiveScriptSite *This,
        IActiveScriptError *pscripterror);

    HRESULT (STDMETHODCALLTYPE *OnEnterScript)(
        IActiveScriptSite *This);

    HRESULT (STDMETHODCALLTYPE *OnLeaveScript)(
        IActiveScriptSite *This);

    END_INTERFACE
} IActiveScriptSiteVtbl;

interface IActiveScriptSite {
    CONST_VTBL IActiveScriptSiteVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptSite_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptSite_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptSite_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptSite methods ***/
#define IActiveScriptSite_GetLCID(This,plcid) (This)->lpVtbl->GetLCID(This,plcid)
#define IActiveScriptSite_GetItemInfo(This,pstrName,dwReturnMask,ppiunkItem,ppti) (This)->lpVtbl->GetItemInfo(This,pstrName,dwReturnMask,ppiunkItem,ppti)
#define IActiveScriptSite_GetDocVersionString(This,pbstrVersion) (This)->lpVtbl->GetDocVersionString(This,pbstrVersion)
#define IActiveScriptSite_OnScriptTerminate(This,pvarResult,pexcepinfo) (This)->lpVtbl->OnScriptTerminate(This,pvarResult,pexcepinfo)
#define IActiveScriptSite_OnStateChange(This,ssScriptState) (This)->lpVtbl->OnStateChange(This,ssScriptState)
#define IActiveScriptSite_OnScriptError(This,pscripterror) (This)->lpVtbl->OnScriptError(This,pscripterror)
#define IActiveScriptSite_OnEnterScript(This) (This)->lpVtbl->OnEnterScript(This)
#define IActiveScriptSite_OnLeaveScript(This) (This)->lpVtbl->OnLeaveScript(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptSite_QueryInterface(IActiveScriptSite* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptSite_AddRef(IActiveScriptSite* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptSite_Release(IActiveScriptSite* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptSite methods ***/
static inline HRESULT IActiveScriptSite_GetLCID(IActiveScriptSite* This,LCID *plcid) {
    return This->lpVtbl->GetLCID(This,plcid);
}
static inline HRESULT IActiveScriptSite_GetItemInfo(IActiveScriptSite* This,LPCOLESTR pstrName,DWORD dwReturnMask,IUnknown **ppiunkItem,ITypeInfo **ppti) {
    return This->lpVtbl->GetItemInfo(This,pstrName,dwReturnMask,ppiunkItem,ppti);
}
static inline HRESULT IActiveScriptSite_GetDocVersionString(IActiveScriptSite* This,BSTR *pbstrVersion) {
    return This->lpVtbl->GetDocVersionString(This,pbstrVersion);
}
static inline HRESULT IActiveScriptSite_OnScriptTerminate(IActiveScriptSite* This,const VARIANT *pvarResult,const EXCEPINFO *pexcepinfo) {
    return This->lpVtbl->OnScriptTerminate(This,pvarResult,pexcepinfo);
}
static inline HRESULT IActiveScriptSite_OnStateChange(IActiveScriptSite* This,SCRIPTSTATE ssScriptState) {
    return This->lpVtbl->OnStateChange(This,ssScriptState);
}
static inline HRESULT IActiveScriptSite_OnScriptError(IActiveScriptSite* This,IActiveScriptError *pscripterror) {
    return This->lpVtbl->OnScriptError(This,pscripterror);
}
static inline HRESULT IActiveScriptSite_OnEnterScript(IActiveScriptSite* This) {
    return This->lpVtbl->OnEnterScript(This);
}
static inline HRESULT IActiveScriptSite_OnLeaveScript(IActiveScriptSite* This) {
    return This->lpVtbl->OnLeaveScript(This);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptSite_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptError interface
 */
#ifndef __IActiveScriptError_INTERFACE_DEFINED__
#define __IActiveScriptError_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptError, 0xeae1ba61, 0xa4ed, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eae1ba61-a4ed-11cf-8f20-00805f2cd064")
IActiveScriptError : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetExceptionInfo(
        EXCEPINFO *pexcepinfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSourcePosition(
        DWORD *pdwSourceContext,
        ULONG *pulLineNumber,
        LONG *plCharacterPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSourceLineText(
        BSTR *pbstrSourceLine) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptError, 0xeae1ba61, 0xa4ed, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64)
#endif
#else
typedef struct IActiveScriptErrorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptError *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptError *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptError *This);

    /*** IActiveScriptError methods ***/
    HRESULT (STDMETHODCALLTYPE *GetExceptionInfo)(
        IActiveScriptError *This,
        EXCEPINFO *pexcepinfo);

    HRESULT (STDMETHODCALLTYPE *GetSourcePosition)(
        IActiveScriptError *This,
        DWORD *pdwSourceContext,
        ULONG *pulLineNumber,
        LONG *plCharacterPosition);

    HRESULT (STDMETHODCALLTYPE *GetSourceLineText)(
        IActiveScriptError *This,
        BSTR *pbstrSourceLine);

    END_INTERFACE
} IActiveScriptErrorVtbl;

interface IActiveScriptError {
    CONST_VTBL IActiveScriptErrorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptError_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptError methods ***/
#define IActiveScriptError_GetExceptionInfo(This,pexcepinfo) (This)->lpVtbl->GetExceptionInfo(This,pexcepinfo)
#define IActiveScriptError_GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition) (This)->lpVtbl->GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition)
#define IActiveScriptError_GetSourceLineText(This,pbstrSourceLine) (This)->lpVtbl->GetSourceLineText(This,pbstrSourceLine)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptError_QueryInterface(IActiveScriptError* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptError_AddRef(IActiveScriptError* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptError_Release(IActiveScriptError* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptError methods ***/
static inline HRESULT IActiveScriptError_GetExceptionInfo(IActiveScriptError* This,EXCEPINFO *pexcepinfo) {
    return This->lpVtbl->GetExceptionInfo(This,pexcepinfo);
}
static inline HRESULT IActiveScriptError_GetSourcePosition(IActiveScriptError* This,DWORD *pdwSourceContext,ULONG *pulLineNumber,LONG *plCharacterPosition) {
    return This->lpVtbl->GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition);
}
static inline HRESULT IActiveScriptError_GetSourceLineText(IActiveScriptError* This,BSTR *pbstrSourceLine) {
    return This->lpVtbl->GetSourceLineText(This,pbstrSourceLine);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IActiveScriptError_RemoteGetExceptionInfo_Proxy(
    IActiveScriptError* This,
    EXCEPINFO *pexcepinfo);
void __RPC_STUB IActiveScriptError_RemoteGetExceptionInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IActiveScriptError_GetExceptionInfo_Proxy(
    IActiveScriptError* This,
    EXCEPINFO *pexcepinfo);
HRESULT __RPC_STUB IActiveScriptError_GetExceptionInfo_Stub(
    IActiveScriptError* This,
    EXCEPINFO *pexcepinfo);

#endif  /* __IActiveScriptError_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptError64 interface
 */
#ifndef __IActiveScriptError64_INTERFACE_DEFINED__
#define __IActiveScriptError64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptError64, 0xb21fb2a1, 0x5b8f, 0x4963, 0x8c,0x21, 0x21,0x45,0x0f,0x84,0xed,0x7f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b21fb2a1-5b8f-4963-8c21-21450f84ed7f")
IActiveScriptError64 : public IActiveScriptError
{
    virtual HRESULT STDMETHODCALLTYPE GetSourcePosition64(
        DWORDLONG *pdwSourceContext,
        ULONG *pulLineNumber,
        LONG *plCharacterPosition) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptError64, 0xb21fb2a1, 0x5b8f, 0x4963, 0x8c,0x21, 0x21,0x45,0x0f,0x84,0xed,0x7f)
#endif
#else
typedef struct IActiveScriptError64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptError64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptError64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptError64 *This);

    /*** IActiveScriptError methods ***/
    HRESULT (STDMETHODCALLTYPE *GetExceptionInfo)(
        IActiveScriptError64 *This,
        EXCEPINFO *pexcepinfo);

    HRESULT (STDMETHODCALLTYPE *GetSourcePosition)(
        IActiveScriptError64 *This,
        DWORD *pdwSourceContext,
        ULONG *pulLineNumber,
        LONG *plCharacterPosition);

    HRESULT (STDMETHODCALLTYPE *GetSourceLineText)(
        IActiveScriptError64 *This,
        BSTR *pbstrSourceLine);

    /*** IActiveScriptError64 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSourcePosition64)(
        IActiveScriptError64 *This,
        DWORDLONG *pdwSourceContext,
        ULONG *pulLineNumber,
        LONG *plCharacterPosition);

    END_INTERFACE
} IActiveScriptError64Vtbl;

interface IActiveScriptError64 {
    CONST_VTBL IActiveScriptError64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptError64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptError64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptError64_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptError methods ***/
#define IActiveScriptError64_GetExceptionInfo(This,pexcepinfo) (This)->lpVtbl->GetExceptionInfo(This,pexcepinfo)
#define IActiveScriptError64_GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition) (This)->lpVtbl->GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition)
#define IActiveScriptError64_GetSourceLineText(This,pbstrSourceLine) (This)->lpVtbl->GetSourceLineText(This,pbstrSourceLine)
/*** IActiveScriptError64 methods ***/
#define IActiveScriptError64_GetSourcePosition64(This,pdwSourceContext,pulLineNumber,plCharacterPosition) (This)->lpVtbl->GetSourcePosition64(This,pdwSourceContext,pulLineNumber,plCharacterPosition)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptError64_QueryInterface(IActiveScriptError64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptError64_AddRef(IActiveScriptError64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptError64_Release(IActiveScriptError64* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptError methods ***/
static inline HRESULT IActiveScriptError64_GetExceptionInfo(IActiveScriptError64* This,EXCEPINFO *pexcepinfo) {
    return This->lpVtbl->GetExceptionInfo(This,pexcepinfo);
}
static inline HRESULT IActiveScriptError64_GetSourcePosition(IActiveScriptError64* This,DWORD *pdwSourceContext,ULONG *pulLineNumber,LONG *plCharacterPosition) {
    return This->lpVtbl->GetSourcePosition(This,pdwSourceContext,pulLineNumber,plCharacterPosition);
}
static inline HRESULT IActiveScriptError64_GetSourceLineText(IActiveScriptError64* This,BSTR *pbstrSourceLine) {
    return This->lpVtbl->GetSourceLineText(This,pbstrSourceLine);
}
/*** IActiveScriptError64 methods ***/
static inline HRESULT IActiveScriptError64_GetSourcePosition64(IActiveScriptError64* This,DWORDLONG *pdwSourceContext,ULONG *pulLineNumber,LONG *plCharacterPosition) {
    return This->lpVtbl->GetSourcePosition64(This,pdwSourceContext,pulLineNumber,plCharacterPosition);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptError64_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveScriptSiteWindow interface
 */
#ifndef __IActiveScriptSiteWindow_INTERFACE_DEFINED__
#define __IActiveScriptSiteWindow_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptSiteWindow, 0xd10f6761, 0x83e9, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d10f6761-83e9-11cf-8f20-00805f2cd064")
IActiveScriptSiteWindow : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetWindow(
        HWND *phwnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableModeless(
        WINBOOL fEnable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptSiteWindow, 0xd10f6761, 0x83e9, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64)
#endif
#else
typedef struct IActiveScriptSiteWindowVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptSiteWindow *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptSiteWindow *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptSiteWindow *This);

    /*** IActiveScriptSiteWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWindow)(
        IActiveScriptSiteWindow *This,
        HWND *phwnd);

    HRESULT (STDMETHODCALLTYPE *EnableModeless)(
        IActiveScriptSiteWindow *This,
        WINBOOL fEnable);

    END_INTERFACE
} IActiveScriptSiteWindowVtbl;

interface IActiveScriptSiteWindow {
    CONST_VTBL IActiveScriptSiteWindowVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptSiteWindow_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptSiteWindow_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptSiteWindow_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptSiteWindow methods ***/
#define IActiveScriptSiteWindow_GetWindow(This,phwnd) (This)->lpVtbl->GetWindow(This,phwnd)
#define IActiveScriptSiteWindow_EnableModeless(This,fEnable) (This)->lpVtbl->EnableModeless(This,fEnable)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptSiteWindow_QueryInterface(IActiveScriptSiteWindow* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptSiteWindow_AddRef(IActiveScriptSiteWindow* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptSiteWindow_Release(IActiveScriptSiteWindow* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptSiteWindow methods ***/
static inline HRESULT IActiveScriptSiteWindow_GetWindow(IActiveScriptSiteWindow* This,HWND *phwnd) {
    return This->lpVtbl->GetWindow(This,phwnd);
}
static inline HRESULT IActiveScriptSiteWindow_EnableModeless(IActiveScriptSiteWindow* This,WINBOOL fEnable) {
    return This->lpVtbl->EnableModeless(This,fEnable);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptSiteWindow_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptSiteUIControl interface
 */
#ifndef __IActiveScriptSiteUIControl_INTERFACE_DEFINED__
#define __IActiveScriptSiteUIControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptSiteUIControl, 0xaedae97e, 0xd7ee, 0x4796, 0xb9,0x60, 0x7f,0x09,0x2a,0xe8,0x44,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aedae97e-d7ee-4796-b960-7f092ae844ab")
IActiveScriptSiteUIControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetUIBehavior(
        SCRIPTUICITEM UicItem,
        SCRIPTUICHANDLING *pUicHandling) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptSiteUIControl, 0xaedae97e, 0xd7ee, 0x4796, 0xb9,0x60, 0x7f,0x09,0x2a,0xe8,0x44,0xab)
#endif
#else
typedef struct IActiveScriptSiteUIControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptSiteUIControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptSiteUIControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptSiteUIControl *This);

    /*** IActiveScriptSiteUIControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetUIBehavior)(
        IActiveScriptSiteUIControl *This,
        SCRIPTUICITEM UicItem,
        SCRIPTUICHANDLING *pUicHandling);

    END_INTERFACE
} IActiveScriptSiteUIControlVtbl;

interface IActiveScriptSiteUIControl {
    CONST_VTBL IActiveScriptSiteUIControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptSiteUIControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptSiteUIControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptSiteUIControl_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptSiteUIControl methods ***/
#define IActiveScriptSiteUIControl_GetUIBehavior(This,UicItem,pUicHandling) (This)->lpVtbl->GetUIBehavior(This,UicItem,pUicHandling)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptSiteUIControl_QueryInterface(IActiveScriptSiteUIControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptSiteUIControl_AddRef(IActiveScriptSiteUIControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptSiteUIControl_Release(IActiveScriptSiteUIControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptSiteUIControl methods ***/
static inline HRESULT IActiveScriptSiteUIControl_GetUIBehavior(IActiveScriptSiteUIControl* This,SCRIPTUICITEM UicItem,SCRIPTUICHANDLING *pUicHandling) {
    return This->lpVtbl->GetUIBehavior(This,UicItem,pUicHandling);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptSiteUIControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptSiteInterruptPoll interface
 */
#ifndef __IActiveScriptSiteInterruptPoll_INTERFACE_DEFINED__
#define __IActiveScriptSiteInterruptPoll_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptSiteInterruptPoll, 0x539698a0, 0xcdca, 0x11cf, 0xa5,0xeb, 0x00,0xaa,0x00,0x47,0xa0,0x63);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("539698a0-cdca-11cf-a5eb-00aa0047a063")
IActiveScriptSiteInterruptPoll : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryContinue(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptSiteInterruptPoll, 0x539698a0, 0xcdca, 0x11cf, 0xa5,0xeb, 0x00,0xaa,0x00,0x47,0xa0,0x63)
#endif
#else
typedef struct IActiveScriptSiteInterruptPollVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptSiteInterruptPoll *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptSiteInterruptPoll *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptSiteInterruptPoll *This);

    /*** IActiveScriptSiteInterruptPoll methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryContinue)(
        IActiveScriptSiteInterruptPoll *This);

    END_INTERFACE
} IActiveScriptSiteInterruptPollVtbl;

interface IActiveScriptSiteInterruptPoll {
    CONST_VTBL IActiveScriptSiteInterruptPollVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptSiteInterruptPoll_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptSiteInterruptPoll_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptSiteInterruptPoll_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptSiteInterruptPoll methods ***/
#define IActiveScriptSiteInterruptPoll_QueryContinue(This) (This)->lpVtbl->QueryContinue(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptSiteInterruptPoll_QueryInterface(IActiveScriptSiteInterruptPoll* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptSiteInterruptPoll_AddRef(IActiveScriptSiteInterruptPoll* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptSiteInterruptPoll_Release(IActiveScriptSiteInterruptPoll* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptSiteInterruptPoll methods ***/
static inline HRESULT IActiveScriptSiteInterruptPoll_QueryContinue(IActiveScriptSiteInterruptPoll* This) {
    return This->lpVtbl->QueryContinue(This);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptSiteInterruptPoll_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScript interface
 */
#ifndef __IActiveScript_INTERFACE_DEFINED__
#define __IActiveScript_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScript, 0xbb1a2ae1, 0xa4f9, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bb1a2ae1-a4f9-11cf-8f20-00805f2cd064")
IActiveScript : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetScriptSite(
        IActiveScriptSite *pass) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptSite(
        REFIID riid,
        void **ppvObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetScriptState(
        SCRIPTSTATE ss) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptState(
        SCRIPTSTATE *pssState) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddNamedItem(
        LPCOLESTR pstrName,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddTypeLib(
        REFGUID rguidTypeLib,
        DWORD dwMajor,
        DWORD dwMinor,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptDispatch(
        LPCOLESTR pstrItemName,
        IDispatch **ppdisp) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentScriptThreadID(
        SCRIPTTHREADID *pstidThread) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptThreadID(
        DWORD dwWin32ThreadId,
        SCRIPTTHREADID *pstidThread) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptThreadState(
        SCRIPTTHREADID stidThread,
        SCRIPTTHREADSTATE *pstsState) = 0;

    virtual HRESULT STDMETHODCALLTYPE InterruptScriptThread(
        SCRIPTTHREADID stidThread,
        const EXCEPINFO *pexcepinfo,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IActiveScript **ppscript) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScript, 0xbb1a2ae1, 0xa4f9, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64)
#endif
#else
typedef struct IActiveScriptVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScript *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScript *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScript *This);

    /*** IActiveScript methods ***/
    HRESULT (STDMETHODCALLTYPE *SetScriptSite)(
        IActiveScript *This,
        IActiveScriptSite *pass);

    HRESULT (STDMETHODCALLTYPE *GetScriptSite)(
        IActiveScript *This,
        REFIID riid,
        void **ppvObject);

    HRESULT (STDMETHODCALLTYPE *SetScriptState)(
        IActiveScript *This,
        SCRIPTSTATE ss);

    HRESULT (STDMETHODCALLTYPE *GetScriptState)(
        IActiveScript *This,
        SCRIPTSTATE *pssState);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IActiveScript *This);

    HRESULT (STDMETHODCALLTYPE *AddNamedItem)(
        IActiveScript *This,
        LPCOLESTR pstrName,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *AddTypeLib)(
        IActiveScript *This,
        REFGUID rguidTypeLib,
        DWORD dwMajor,
        DWORD dwMinor,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetScriptDispatch)(
        IActiveScript *This,
        LPCOLESTR pstrItemName,
        IDispatch **ppdisp);

    HRESULT (STDMETHODCALLTYPE *GetCurrentScriptThreadID)(
        IActiveScript *This,
        SCRIPTTHREADID *pstidThread);

    HRESULT (STDMETHODCALLTYPE *GetScriptThreadID)(
        IActiveScript *This,
        DWORD dwWin32ThreadId,
        SCRIPTTHREADID *pstidThread);

    HRESULT (STDMETHODCALLTYPE *GetScriptThreadState)(
        IActiveScript *This,
        SCRIPTTHREADID stidThread,
        SCRIPTTHREADSTATE *pstsState);

    HRESULT (STDMETHODCALLTYPE *InterruptScriptThread)(
        IActiveScript *This,
        SCRIPTTHREADID stidThread,
        const EXCEPINFO *pexcepinfo,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IActiveScript *This,
        IActiveScript **ppscript);

    END_INTERFACE
} IActiveScriptVtbl;

interface IActiveScript {
    CONST_VTBL IActiveScriptVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScript_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScript_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScript_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScript methods ***/
#define IActiveScript_SetScriptSite(This,pass) (This)->lpVtbl->SetScriptSite(This,pass)
#define IActiveScript_GetScriptSite(This,riid,ppvObject) (This)->lpVtbl->GetScriptSite(This,riid,ppvObject)
#define IActiveScript_SetScriptState(This,ss) (This)->lpVtbl->SetScriptState(This,ss)
#define IActiveScript_GetScriptState(This,pssState) (This)->lpVtbl->GetScriptState(This,pssState)
#define IActiveScript_Close(This) (This)->lpVtbl->Close(This)
#define IActiveScript_AddNamedItem(This,pstrName,dwFlags) (This)->lpVtbl->AddNamedItem(This,pstrName,dwFlags)
#define IActiveScript_AddTypeLib(This,rguidTypeLib,dwMajor,dwMinor,dwFlags) (This)->lpVtbl->AddTypeLib(This,rguidTypeLib,dwMajor,dwMinor,dwFlags)
#define IActiveScript_GetScriptDispatch(This,pstrItemName,ppdisp) (This)->lpVtbl->GetScriptDispatch(This,pstrItemName,ppdisp)
#define IActiveScript_GetCurrentScriptThreadID(This,pstidThread) (This)->lpVtbl->GetCurrentScriptThreadID(This,pstidThread)
#define IActiveScript_GetScriptThreadID(This,dwWin32ThreadId,pstidThread) (This)->lpVtbl->GetScriptThreadID(This,dwWin32ThreadId,pstidThread)
#define IActiveScript_GetScriptThreadState(This,stidThread,pstsState) (This)->lpVtbl->GetScriptThreadState(This,stidThread,pstsState)
#define IActiveScript_InterruptScriptThread(This,stidThread,pexcepinfo,dwFlags) (This)->lpVtbl->InterruptScriptThread(This,stidThread,pexcepinfo,dwFlags)
#define IActiveScript_Clone(This,ppscript) (This)->lpVtbl->Clone(This,ppscript)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScript_QueryInterface(IActiveScript* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScript_AddRef(IActiveScript* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScript_Release(IActiveScript* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScript methods ***/
static inline HRESULT IActiveScript_SetScriptSite(IActiveScript* This,IActiveScriptSite *pass) {
    return This->lpVtbl->SetScriptSite(This,pass);
}
static inline HRESULT IActiveScript_GetScriptSite(IActiveScript* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->GetScriptSite(This,riid,ppvObject);
}
static inline HRESULT IActiveScript_SetScriptState(IActiveScript* This,SCRIPTSTATE ss) {
    return This->lpVtbl->SetScriptState(This,ss);
}
static inline HRESULT IActiveScript_GetScriptState(IActiveScript* This,SCRIPTSTATE *pssState) {
    return This->lpVtbl->GetScriptState(This,pssState);
}
static inline HRESULT IActiveScript_Close(IActiveScript* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IActiveScript_AddNamedItem(IActiveScript* This,LPCOLESTR pstrName,DWORD dwFlags) {
    return This->lpVtbl->AddNamedItem(This,pstrName,dwFlags);
}
static inline HRESULT IActiveScript_AddTypeLib(IActiveScript* This,REFGUID rguidTypeLib,DWORD dwMajor,DWORD dwMinor,DWORD dwFlags) {
    return This->lpVtbl->AddTypeLib(This,rguidTypeLib,dwMajor,dwMinor,dwFlags);
}
static inline HRESULT IActiveScript_GetScriptDispatch(IActiveScript* This,LPCOLESTR pstrItemName,IDispatch **ppdisp) {
    return This->lpVtbl->GetScriptDispatch(This,pstrItemName,ppdisp);
}
static inline HRESULT IActiveScript_GetCurrentScriptThreadID(IActiveScript* This,SCRIPTTHREADID *pstidThread) {
    return This->lpVtbl->GetCurrentScriptThreadID(This,pstidThread);
}
static inline HRESULT IActiveScript_GetScriptThreadID(IActiveScript* This,DWORD dwWin32ThreadId,SCRIPTTHREADID *pstidThread) {
    return This->lpVtbl->GetScriptThreadID(This,dwWin32ThreadId,pstidThread);
}
static inline HRESULT IActiveScript_GetScriptThreadState(IActiveScript* This,SCRIPTTHREADID stidThread,SCRIPTTHREADSTATE *pstsState) {
    return This->lpVtbl->GetScriptThreadState(This,stidThread,pstsState);
}
static inline HRESULT IActiveScript_InterruptScriptThread(IActiveScript* This,SCRIPTTHREADID stidThread,const EXCEPINFO *pexcepinfo,DWORD dwFlags) {
    return This->lpVtbl->InterruptScriptThread(This,stidThread,pexcepinfo,dwFlags);
}
static inline HRESULT IActiveScript_Clone(IActiveScript* This,IActiveScript **ppscript) {
    return This->lpVtbl->Clone(This,ppscript);
}
#endif
#endif

#endif


#endif  /* __IActiveScript_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptParse32 interface
 */
#ifndef __IActiveScriptParse32_INTERFACE_DEFINED__
#define __IActiveScriptParse32_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptParse32, 0xbb1a2ae2, 0xa4f9, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bb1a2ae2-a4f9-11cf-8f20-00805f2cd064")
IActiveScriptParse32 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE InitNew(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddScriptlet(
        LPCOLESTR pstrDefaultName,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrItemName,
        LPCOLESTR pstrSubItemName,
        LPCOLESTR pstrEventName,
        LPCOLESTR pstrDelimiter,
        DWORD dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        BSTR *pbstrName,
        EXCEPINFO *pexcepinfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE ParseScriptText(
        LPCOLESTR pstrCode,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORD dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        VARIANT *pvarResult,
        EXCEPINFO *pexcepinfo) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptParse32, 0xbb1a2ae2, 0xa4f9, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64)
#endif
#else
typedef struct IActiveScriptParse32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptParse32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptParse32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptParse32 *This);

    /*** IActiveScriptParse32 methods ***/
    HRESULT (STDMETHODCALLTYPE *InitNew)(
        IActiveScriptParse32 *This);

    HRESULT (STDMETHODCALLTYPE *AddScriptlet)(
        IActiveScriptParse32 *This,
        LPCOLESTR pstrDefaultName,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrItemName,
        LPCOLESTR pstrSubItemName,
        LPCOLESTR pstrEventName,
        LPCOLESTR pstrDelimiter,
        DWORD dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        BSTR *pbstrName,
        EXCEPINFO *pexcepinfo);

    HRESULT (STDMETHODCALLTYPE *ParseScriptText)(
        IActiveScriptParse32 *This,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORD dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        VARIANT *pvarResult,
        EXCEPINFO *pexcepinfo);

    END_INTERFACE
} IActiveScriptParse32Vtbl;

interface IActiveScriptParse32 {
    CONST_VTBL IActiveScriptParse32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptParse32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptParse32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptParse32_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptParse32 methods ***/
#define IActiveScriptParse32_InitNew(This) (This)->lpVtbl->InitNew(This)
#define IActiveScriptParse32_AddScriptlet(This,pstrDefaultName,pstrCode,pstrItemName,pstrSubItemName,pstrEventName,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pbstrName,pexcepinfo) (This)->lpVtbl->AddScriptlet(This,pstrDefaultName,pstrCode,pstrItemName,pstrSubItemName,pstrEventName,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pbstrName,pexcepinfo)
#define IActiveScriptParse32_ParseScriptText(This,pstrCode,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pvarResult,pexcepinfo) (This)->lpVtbl->ParseScriptText(This,pstrCode,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pvarResult,pexcepinfo)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptParse32_QueryInterface(IActiveScriptParse32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptParse32_AddRef(IActiveScriptParse32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptParse32_Release(IActiveScriptParse32* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptParse32 methods ***/
static inline HRESULT IActiveScriptParse32_InitNew(IActiveScriptParse32* This) {
    return This->lpVtbl->InitNew(This);
}
static inline HRESULT IActiveScriptParse32_AddScriptlet(IActiveScriptParse32* This,LPCOLESTR pstrDefaultName,LPCOLESTR pstrCode,LPCOLESTR pstrItemName,LPCOLESTR pstrSubItemName,LPCOLESTR pstrEventName,LPCOLESTR pstrDelimiter,DWORD dwSourceContextCookie,ULONG ulStartingLineNumber,DWORD dwFlags,BSTR *pbstrName,EXCEPINFO *pexcepinfo) {
    return This->lpVtbl->AddScriptlet(This,pstrDefaultName,pstrCode,pstrItemName,pstrSubItemName,pstrEventName,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pbstrName,pexcepinfo);
}
static inline HRESULT IActiveScriptParse32_ParseScriptText(IActiveScriptParse32* This,LPCOLESTR pstrCode,LPCOLESTR pstrItemName,IUnknown *punkContext,LPCOLESTR pstrDelimiter,DWORD dwSourceContextCookie,ULONG ulStartingLineNumber,DWORD dwFlags,VARIANT *pvarResult,EXCEPINFO *pexcepinfo) {
    return This->lpVtbl->ParseScriptText(This,pstrCode,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pvarResult,pexcepinfo);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptParse32_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptParse64 interface
 */
#ifndef __IActiveScriptParse64_INTERFACE_DEFINED__
#define __IActiveScriptParse64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptParse64, 0xc7ef7658, 0xe1ee, 0x480e, 0x97,0xea, 0xd5,0x2c,0xb4,0xd7,0x6d,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c7ef7658-e1ee-480e-97ea-d52cb4d76d17")
IActiveScriptParse64 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE InitNew(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddScriptlet(
        LPCOLESTR pstrDefaultName,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrItemName,
        LPCOLESTR pstrSubItemName,
        LPCOLESTR pstrEventName,
        LPCOLESTR pstrDelimiter,
        DWORDLONG dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        BSTR *pbstrName,
        EXCEPINFO *pexcepinfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE ParseScriptText(
        LPCOLESTR pstrCode,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORDLONG dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        VARIANT *pvarResult,
        EXCEPINFO *pexcepinfo) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptParse64, 0xc7ef7658, 0xe1ee, 0x480e, 0x97,0xea, 0xd5,0x2c,0xb4,0xd7,0x6d,0x17)
#endif
#else
typedef struct IActiveScriptParse64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptParse64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptParse64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptParse64 *This);

    /*** IActiveScriptParse64 methods ***/
    HRESULT (STDMETHODCALLTYPE *InitNew)(
        IActiveScriptParse64 *This);

    HRESULT (STDMETHODCALLTYPE *AddScriptlet)(
        IActiveScriptParse64 *This,
        LPCOLESTR pstrDefaultName,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrItemName,
        LPCOLESTR pstrSubItemName,
        LPCOLESTR pstrEventName,
        LPCOLESTR pstrDelimiter,
        DWORDLONG dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        BSTR *pbstrName,
        EXCEPINFO *pexcepinfo);

    HRESULT (STDMETHODCALLTYPE *ParseScriptText)(
        IActiveScriptParse64 *This,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORDLONG dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        VARIANT *pvarResult,
        EXCEPINFO *pexcepinfo);

    END_INTERFACE
} IActiveScriptParse64Vtbl;

interface IActiveScriptParse64 {
    CONST_VTBL IActiveScriptParse64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptParse64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptParse64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptParse64_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptParse64 methods ***/
#define IActiveScriptParse64_InitNew(This) (This)->lpVtbl->InitNew(This)
#define IActiveScriptParse64_AddScriptlet(This,pstrDefaultName,pstrCode,pstrItemName,pstrSubItemName,pstrEventName,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pbstrName,pexcepinfo) (This)->lpVtbl->AddScriptlet(This,pstrDefaultName,pstrCode,pstrItemName,pstrSubItemName,pstrEventName,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pbstrName,pexcepinfo)
#define IActiveScriptParse64_ParseScriptText(This,pstrCode,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pvarResult,pexcepinfo) (This)->lpVtbl->ParseScriptText(This,pstrCode,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pvarResult,pexcepinfo)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptParse64_QueryInterface(IActiveScriptParse64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptParse64_AddRef(IActiveScriptParse64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptParse64_Release(IActiveScriptParse64* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptParse64 methods ***/
static inline HRESULT IActiveScriptParse64_InitNew(IActiveScriptParse64* This) {
    return This->lpVtbl->InitNew(This);
}
static inline HRESULT IActiveScriptParse64_AddScriptlet(IActiveScriptParse64* This,LPCOLESTR pstrDefaultName,LPCOLESTR pstrCode,LPCOLESTR pstrItemName,LPCOLESTR pstrSubItemName,LPCOLESTR pstrEventName,LPCOLESTR pstrDelimiter,DWORDLONG dwSourceContextCookie,ULONG ulStartingLineNumber,DWORD dwFlags,BSTR *pbstrName,EXCEPINFO *pexcepinfo) {
    return This->lpVtbl->AddScriptlet(This,pstrDefaultName,pstrCode,pstrItemName,pstrSubItemName,pstrEventName,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pbstrName,pexcepinfo);
}
static inline HRESULT IActiveScriptParse64_ParseScriptText(IActiveScriptParse64* This,LPCOLESTR pstrCode,LPCOLESTR pstrItemName,IUnknown *punkContext,LPCOLESTR pstrDelimiter,DWORDLONG dwSourceContextCookie,ULONG ulStartingLineNumber,DWORD dwFlags,VARIANT *pvarResult,EXCEPINFO *pexcepinfo) {
    return This->lpVtbl->ParseScriptText(This,pstrCode,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,pvarResult,pexcepinfo);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptParse64_INTERFACE_DEFINED__ */


#ifdef _WIN64
#define IActiveScriptParse IActiveScriptParse64
#define IID_IActiveScriptParse IID_IActiveScriptParse64
#else
#define IActiveScriptParse     IActiveScriptParse32
#define IID_IActiveScriptParse IID_IActiveScriptParse32
#endif

typedef IActiveScriptParse *PIActiveScriptParse;

/*****************************************************************************
 * IActiveScriptParseProcedureOld32 interface
 */
#ifndef __IActiveScriptParseProcedureOld32_INTERFACE_DEFINED__
#define __IActiveScriptParseProcedureOld32_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptParseProcedureOld32, 0x1cff0050, 0x6fdd, 0x11d0, 0x93,0x28, 0x00,0xa0,0xc9,0x0d,0xca,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1cff0050-6fdd-11d0-9328-00a0c90dcaa9")
IActiveScriptParseProcedureOld32 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ParseProcedureText(
        LPCOLESTR pstrCode,
        LPCOLESTR pstrFormalParams,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORD dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        IDispatch **ppdisp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptParseProcedureOld32, 0x1cff0050, 0x6fdd, 0x11d0, 0x93,0x28, 0x00,0xa0,0xc9,0x0d,0xca,0xa9)
#endif
#else
typedef struct IActiveScriptParseProcedureOld32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptParseProcedureOld32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptParseProcedureOld32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptParseProcedureOld32 *This);

    /*** IActiveScriptParseProcedureOld32 methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseProcedureText)(
        IActiveScriptParseProcedureOld32 *This,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrFormalParams,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORD dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        IDispatch **ppdisp);

    END_INTERFACE
} IActiveScriptParseProcedureOld32Vtbl;

interface IActiveScriptParseProcedureOld32 {
    CONST_VTBL IActiveScriptParseProcedureOld32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptParseProcedureOld32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptParseProcedureOld32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptParseProcedureOld32_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptParseProcedureOld32 methods ***/
#define IActiveScriptParseProcedureOld32_ParseProcedureText(This,pstrCode,pstrFormalParams,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp) (This)->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptParseProcedureOld32_QueryInterface(IActiveScriptParseProcedureOld32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptParseProcedureOld32_AddRef(IActiveScriptParseProcedureOld32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptParseProcedureOld32_Release(IActiveScriptParseProcedureOld32* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptParseProcedureOld32 methods ***/
static inline HRESULT IActiveScriptParseProcedureOld32_ParseProcedureText(IActiveScriptParseProcedureOld32* This,LPCOLESTR pstrCode,LPCOLESTR pstrFormalParams,LPCOLESTR pstrItemName,IUnknown *punkContext,LPCOLESTR pstrDelimiter,DWORD dwSourceContextCookie,ULONG ulStartingLineNumber,DWORD dwFlags,IDispatch **ppdisp) {
    return This->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptParseProcedureOld32_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptParseProcedureOld64 interface
 */
#ifndef __IActiveScriptParseProcedureOld64_INTERFACE_DEFINED__
#define __IActiveScriptParseProcedureOld64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptParseProcedureOld64, 0x21f57128, 0x08c9, 0x4638, 0xba,0x12, 0x22,0xd1,0x5d,0x88,0xdc,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("21f57128-08c9-4638-ba12-22d15d88dc5c")
IActiveScriptParseProcedureOld64 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ParseProcedureText(
        LPCOLESTR pstrCode,
        LPCOLESTR pstrFormalParams,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORDLONG dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        IDispatch **ppdisp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptParseProcedureOld64, 0x21f57128, 0x08c9, 0x4638, 0xba,0x12, 0x22,0xd1,0x5d,0x88,0xdc,0x5c)
#endif
#else
typedef struct IActiveScriptParseProcedureOld64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptParseProcedureOld64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptParseProcedureOld64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptParseProcedureOld64 *This);

    /*** IActiveScriptParseProcedureOld64 methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseProcedureText)(
        IActiveScriptParseProcedureOld64 *This,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrFormalParams,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORDLONG dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        IDispatch **ppdisp);

    END_INTERFACE
} IActiveScriptParseProcedureOld64Vtbl;

interface IActiveScriptParseProcedureOld64 {
    CONST_VTBL IActiveScriptParseProcedureOld64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptParseProcedureOld64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptParseProcedureOld64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptParseProcedureOld64_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptParseProcedureOld64 methods ***/
#define IActiveScriptParseProcedureOld64_ParseProcedureText(This,pstrCode,pstrFormalParams,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp) (This)->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptParseProcedureOld64_QueryInterface(IActiveScriptParseProcedureOld64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptParseProcedureOld64_AddRef(IActiveScriptParseProcedureOld64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptParseProcedureOld64_Release(IActiveScriptParseProcedureOld64* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptParseProcedureOld64 methods ***/
static inline HRESULT IActiveScriptParseProcedureOld64_ParseProcedureText(IActiveScriptParseProcedureOld64* This,LPCOLESTR pstrCode,LPCOLESTR pstrFormalParams,LPCOLESTR pstrItemName,IUnknown *punkContext,LPCOLESTR pstrDelimiter,DWORDLONG dwSourceContextCookie,ULONG ulStartingLineNumber,DWORD dwFlags,IDispatch **ppdisp) {
    return This->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptParseProcedureOld64_INTERFACE_DEFINED__ */


#ifdef _WIN64
#define IActiveScriptParseProcedureOld IActiveScriptParseProcedureOld64
#define IID_IActiveScriptParseProcedureOld IID_IActiveScriptParseProcedureOld64
#else
#define     IActiveScriptParseProcedureOld IActiveScriptParseProcedureOld32
#define IID_IActiveScriptParseProcedureOld IID_IActiveScriptParseProcedureOld32
#endif

typedef IActiveScriptParseProcedureOld *PIActiveScriptParseProcedureOld;

/*****************************************************************************
 * IActiveScriptParseProcedure32 interface
 */
#ifndef __IActiveScriptParseProcedure32_INTERFACE_DEFINED__
#define __IActiveScriptParseProcedure32_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptParseProcedure32, 0xaa5b6a80, 0xb834, 0x11d0, 0x93,0x2f, 0x00,0xa0,0xc9,0x0d,0xca,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa5b6a80-b834-11d0-932f-00a0c90dcaa9")
IActiveScriptParseProcedure32 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ParseProcedureText(
        LPCOLESTR pstrCode,
        LPCOLESTR pstrFormalParams,
        LPCOLESTR pstrProcedureName,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORD dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        IDispatch **ppdisp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptParseProcedure32, 0xaa5b6a80, 0xb834, 0x11d0, 0x93,0x2f, 0x00,0xa0,0xc9,0x0d,0xca,0xa9)
#endif
#else
typedef struct IActiveScriptParseProcedure32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptParseProcedure32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptParseProcedure32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptParseProcedure32 *This);

    /*** IActiveScriptParseProcedure32 methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseProcedureText)(
        IActiveScriptParseProcedure32 *This,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrFormalParams,
        LPCOLESTR pstrProcedureName,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORD dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        IDispatch **ppdisp);

    END_INTERFACE
} IActiveScriptParseProcedure32Vtbl;

interface IActiveScriptParseProcedure32 {
    CONST_VTBL IActiveScriptParseProcedure32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptParseProcedure32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptParseProcedure32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptParseProcedure32_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptParseProcedure32 methods ***/
#define IActiveScriptParseProcedure32_ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp) (This)->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptParseProcedure32_QueryInterface(IActiveScriptParseProcedure32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptParseProcedure32_AddRef(IActiveScriptParseProcedure32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptParseProcedure32_Release(IActiveScriptParseProcedure32* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptParseProcedure32 methods ***/
static inline HRESULT IActiveScriptParseProcedure32_ParseProcedureText(IActiveScriptParseProcedure32* This,LPCOLESTR pstrCode,LPCOLESTR pstrFormalParams,LPCOLESTR pstrProcedureName,LPCOLESTR pstrItemName,IUnknown *punkContext,LPCOLESTR pstrDelimiter,DWORD dwSourceContextCookie,ULONG ulStartingLineNumber,DWORD dwFlags,IDispatch **ppdisp) {
    return This->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptParseProcedure32_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptParseProcedure64 interface
 */
#ifndef __IActiveScriptParseProcedure64_INTERFACE_DEFINED__
#define __IActiveScriptParseProcedure64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptParseProcedure64, 0xc64713b6, 0xe029, 0x4cc5, 0x92,0x00, 0x43,0x8b,0x72,0x89,0x0b,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c64713b6-e029-4cc5-9200-438b72890b6a")
IActiveScriptParseProcedure64 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ParseProcedureText(
        LPCOLESTR pstrCode,
        LPCOLESTR pstrFormalParams,
        LPCOLESTR pstrProcedureName,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORDLONG dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        IDispatch **ppdisp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptParseProcedure64, 0xc64713b6, 0xe029, 0x4cc5, 0x92,0x00, 0x43,0x8b,0x72,0x89,0x0b,0x6a)
#endif
#else
typedef struct IActiveScriptParseProcedure64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptParseProcedure64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptParseProcedure64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptParseProcedure64 *This);

    /*** IActiveScriptParseProcedure64 methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseProcedureText)(
        IActiveScriptParseProcedure64 *This,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrFormalParams,
        LPCOLESTR pstrProcedureName,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORDLONG dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        IDispatch **ppdisp);

    END_INTERFACE
} IActiveScriptParseProcedure64Vtbl;

interface IActiveScriptParseProcedure64 {
    CONST_VTBL IActiveScriptParseProcedure64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptParseProcedure64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptParseProcedure64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptParseProcedure64_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptParseProcedure64 methods ***/
#define IActiveScriptParseProcedure64_ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp) (This)->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptParseProcedure64_QueryInterface(IActiveScriptParseProcedure64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptParseProcedure64_AddRef(IActiveScriptParseProcedure64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptParseProcedure64_Release(IActiveScriptParseProcedure64* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptParseProcedure64 methods ***/
static inline HRESULT IActiveScriptParseProcedure64_ParseProcedureText(IActiveScriptParseProcedure64* This,LPCOLESTR pstrCode,LPCOLESTR pstrFormalParams,LPCOLESTR pstrProcedureName,LPCOLESTR pstrItemName,IUnknown *punkContext,LPCOLESTR pstrDelimiter,DWORDLONG dwSourceContextCookie,ULONG ulStartingLineNumber,DWORD dwFlags,IDispatch **ppdisp) {
    return This->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptParseProcedure64_INTERFACE_DEFINED__ */


#ifdef _WIN64
#define IActiveScriptParseProcedure IActiveScriptParseProcedure64
#define IID_IActiveScriptParseProcedure IID_IActiveScriptParseProcedure64
#else
#define IActiveScriptParseProcedure IActiveScriptParseProcedure32
#define IID_IActiveScriptParseProcedure IID_IActiveScriptParseProcedure32
#endif

typedef IActiveScriptParseProcedure *PIActiveScriptParseProcedure;

/*****************************************************************************
 * IActiveScriptParseProcedure2_32 interface
 */
#ifndef __IActiveScriptParseProcedure2_32_INTERFACE_DEFINED__
#define __IActiveScriptParseProcedure2_32_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptParseProcedure2_32, 0x71ee5b20, 0xfb04, 0x11d1, 0xb3,0xa8, 0x00,0xa0,0xc9,0x11,0xe8,0xb2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("71ee5b20-fb04-11d1-b3a8-00a0c911e8b2")
IActiveScriptParseProcedure2_32 : public IActiveScriptParseProcedure32
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptParseProcedure2_32, 0x71ee5b20, 0xfb04, 0x11d1, 0xb3,0xa8, 0x00,0xa0,0xc9,0x11,0xe8,0xb2)
#endif
#else
typedef struct IActiveScriptParseProcedure2_32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptParseProcedure2_32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptParseProcedure2_32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptParseProcedure2_32 *This);

    /*** IActiveScriptParseProcedure32 methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseProcedureText)(
        IActiveScriptParseProcedure2_32 *This,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrFormalParams,
        LPCOLESTR pstrProcedureName,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORD dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        IDispatch **ppdisp);

    END_INTERFACE
} IActiveScriptParseProcedure2_32Vtbl;

interface IActiveScriptParseProcedure2_32 {
    CONST_VTBL IActiveScriptParseProcedure2_32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptParseProcedure2_32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptParseProcedure2_32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptParseProcedure2_32_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptParseProcedure32 methods ***/
#define IActiveScriptParseProcedure2_32_ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp) (This)->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptParseProcedure2_32_QueryInterface(IActiveScriptParseProcedure2_32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptParseProcedure2_32_AddRef(IActiveScriptParseProcedure2_32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptParseProcedure2_32_Release(IActiveScriptParseProcedure2_32* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptParseProcedure32 methods ***/
static inline HRESULT IActiveScriptParseProcedure2_32_ParseProcedureText(IActiveScriptParseProcedure2_32* This,LPCOLESTR pstrCode,LPCOLESTR pstrFormalParams,LPCOLESTR pstrProcedureName,LPCOLESTR pstrItemName,IUnknown *punkContext,LPCOLESTR pstrDelimiter,DWORD dwSourceContextCookie,ULONG ulStartingLineNumber,DWORD dwFlags,IDispatch **ppdisp) {
    return This->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptParseProcedure2_32_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptParseProcedure2_64 interface
 */
#ifndef __IActiveScriptParseProcedure2_64_INTERFACE_DEFINED__
#define __IActiveScriptParseProcedure2_64_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptParseProcedure2_64, 0xfe7c4271, 0x210c, 0x448d, 0x9f,0x54, 0x76,0xda,0xb7,0x04,0x7b,0x28);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fe7c4271-210c-448d-9f54-76dab7047b28")
IActiveScriptParseProcedure2_64 : public IActiveScriptParseProcedure64
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptParseProcedure2_64, 0xfe7c4271, 0x210c, 0x448d, 0x9f,0x54, 0x76,0xda,0xb7,0x04,0x7b,0x28)
#endif
#else
typedef struct IActiveScriptParseProcedure2_64Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptParseProcedure2_64 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptParseProcedure2_64 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptParseProcedure2_64 *This);

    /*** IActiveScriptParseProcedure64 methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseProcedureText)(
        IActiveScriptParseProcedure2_64 *This,
        LPCOLESTR pstrCode,
        LPCOLESTR pstrFormalParams,
        LPCOLESTR pstrProcedureName,
        LPCOLESTR pstrItemName,
        IUnknown *punkContext,
        LPCOLESTR pstrDelimiter,
        DWORDLONG dwSourceContextCookie,
        ULONG ulStartingLineNumber,
        DWORD dwFlags,
        IDispatch **ppdisp);

    END_INTERFACE
} IActiveScriptParseProcedure2_64Vtbl;

interface IActiveScriptParseProcedure2_64 {
    CONST_VTBL IActiveScriptParseProcedure2_64Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptParseProcedure2_64_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptParseProcedure2_64_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptParseProcedure2_64_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptParseProcedure64 methods ***/
#define IActiveScriptParseProcedure2_64_ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp) (This)->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptParseProcedure2_64_QueryInterface(IActiveScriptParseProcedure2_64* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptParseProcedure2_64_AddRef(IActiveScriptParseProcedure2_64* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptParseProcedure2_64_Release(IActiveScriptParseProcedure2_64* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptParseProcedure64 methods ***/
static inline HRESULT IActiveScriptParseProcedure2_64_ParseProcedureText(IActiveScriptParseProcedure2_64* This,LPCOLESTR pstrCode,LPCOLESTR pstrFormalParams,LPCOLESTR pstrProcedureName,LPCOLESTR pstrItemName,IUnknown *punkContext,LPCOLESTR pstrDelimiter,DWORDLONG dwSourceContextCookie,ULONG ulStartingLineNumber,DWORD dwFlags,IDispatch **ppdisp) {
    return This->lpVtbl->ParseProcedureText(This,pstrCode,pstrFormalParams,pstrProcedureName,pstrItemName,punkContext,pstrDelimiter,dwSourceContextCookie,ulStartingLineNumber,dwFlags,ppdisp);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptParseProcedure2_64_INTERFACE_DEFINED__ */


#ifdef _WIN64
#define IActiveScriptParseProcedure2 IActiveScriptParseProcedure2_64
#define IID_IActiveScriptParseProcedure2 IID_IActiveScriptParseProcedure2_64
#else
#define IActiveScriptParseProcedure2 IActiveScriptParseProcedure2_32
#define IID_IActiveScriptParseProcedure2 IID_IActiveScriptParseProcedure2_32
#endif

typedef IActiveScriptParseProcedure2 *PIActiveScriptParseProcedure2;

/*****************************************************************************
 * IActiveScriptEncode interface
 */
#ifndef __IActiveScriptEncode_INTERFACE_DEFINED__
#define __IActiveScriptEncode_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptEncode, 0xbb1a2ae3, 0xa4f9, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bb1a2ae3-a4f9-11cf-8f20-00805f2cd064")
IActiveScriptEncode : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EncodeSection(
        LPCOLESTR pchIn,
        DWORD cchIn,
        LPOLESTR pchOut,
        DWORD cchOut,
        DWORD *pcchRet) = 0;

    virtual HRESULT STDMETHODCALLTYPE DecodeScript(
        LPCOLESTR pchIn,
        DWORD cchIn,
        LPOLESTR pchOut,
        DWORD cchOut,
        DWORD *pcchRet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEncodeProgId(
        BSTR *pbstrOut) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptEncode, 0xbb1a2ae3, 0xa4f9, 0x11cf, 0x8f,0x20, 0x00,0x80,0x5f,0x2c,0xd0,0x64)
#endif
#else
typedef struct IActiveScriptEncodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptEncode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptEncode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptEncode *This);

    /*** IActiveScriptEncode methods ***/
    HRESULT (STDMETHODCALLTYPE *EncodeSection)(
        IActiveScriptEncode *This,
        LPCOLESTR pchIn,
        DWORD cchIn,
        LPOLESTR pchOut,
        DWORD cchOut,
        DWORD *pcchRet);

    HRESULT (STDMETHODCALLTYPE *DecodeScript)(
        IActiveScriptEncode *This,
        LPCOLESTR pchIn,
        DWORD cchIn,
        LPOLESTR pchOut,
        DWORD cchOut,
        DWORD *pcchRet);

    HRESULT (STDMETHODCALLTYPE *GetEncodeProgId)(
        IActiveScriptEncode *This,
        BSTR *pbstrOut);

    END_INTERFACE
} IActiveScriptEncodeVtbl;

interface IActiveScriptEncode {
    CONST_VTBL IActiveScriptEncodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptEncode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptEncode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptEncode_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptEncode methods ***/
#define IActiveScriptEncode_EncodeSection(This,pchIn,cchIn,pchOut,cchOut,pcchRet) (This)->lpVtbl->EncodeSection(This,pchIn,cchIn,pchOut,cchOut,pcchRet)
#define IActiveScriptEncode_DecodeScript(This,pchIn,cchIn,pchOut,cchOut,pcchRet) (This)->lpVtbl->DecodeScript(This,pchIn,cchIn,pchOut,cchOut,pcchRet)
#define IActiveScriptEncode_GetEncodeProgId(This,pbstrOut) (This)->lpVtbl->GetEncodeProgId(This,pbstrOut)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptEncode_QueryInterface(IActiveScriptEncode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptEncode_AddRef(IActiveScriptEncode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptEncode_Release(IActiveScriptEncode* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptEncode methods ***/
static inline HRESULT IActiveScriptEncode_EncodeSection(IActiveScriptEncode* This,LPCOLESTR pchIn,DWORD cchIn,LPOLESTR pchOut,DWORD cchOut,DWORD *pcchRet) {
    return This->lpVtbl->EncodeSection(This,pchIn,cchIn,pchOut,cchOut,pcchRet);
}
static inline HRESULT IActiveScriptEncode_DecodeScript(IActiveScriptEncode* This,LPCOLESTR pchIn,DWORD cchIn,LPOLESTR pchOut,DWORD cchOut,DWORD *pcchRet) {
    return This->lpVtbl->DecodeScript(This,pchIn,cchIn,pchOut,cchOut,pcchRet);
}
static inline HRESULT IActiveScriptEncode_GetEncodeProgId(IActiveScriptEncode* This,BSTR *pbstrOut) {
    return This->lpVtbl->GetEncodeProgId(This,pbstrOut);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptEncode_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptHostEncode interface
 */
#ifndef __IActiveScriptHostEncode_INTERFACE_DEFINED__
#define __IActiveScriptHostEncode_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptHostEncode, 0xbee9b76e, 0xcfe3, 0x11d1, 0xb7,0x47, 0x00,0xc0,0x4f,0xc2,0xb0,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bee9b76e-cfe3-11d1-b747-00c04fc2b085")
IActiveScriptHostEncode : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EncodeScriptHostFile(
        BSTR bstrInFile,
        BSTR *pbstrOutFile,
        ULONG cFlags,
        BSTR bstrDefaultLang) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptHostEncode, 0xbee9b76e, 0xcfe3, 0x11d1, 0xb7,0x47, 0x00,0xc0,0x4f,0xc2,0xb0,0x85)
#endif
#else
typedef struct IActiveScriptHostEncodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptHostEncode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptHostEncode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptHostEncode *This);

    /*** IActiveScriptHostEncode methods ***/
    HRESULT (STDMETHODCALLTYPE *EncodeScriptHostFile)(
        IActiveScriptHostEncode *This,
        BSTR bstrInFile,
        BSTR *pbstrOutFile,
        ULONG cFlags,
        BSTR bstrDefaultLang);

    END_INTERFACE
} IActiveScriptHostEncodeVtbl;

interface IActiveScriptHostEncode {
    CONST_VTBL IActiveScriptHostEncodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptHostEncode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptHostEncode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptHostEncode_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptHostEncode methods ***/
#define IActiveScriptHostEncode_EncodeScriptHostFile(This,bstrInFile,pbstrOutFile,cFlags,bstrDefaultLang) (This)->lpVtbl->EncodeScriptHostFile(This,bstrInFile,pbstrOutFile,cFlags,bstrDefaultLang)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptHostEncode_QueryInterface(IActiveScriptHostEncode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptHostEncode_AddRef(IActiveScriptHostEncode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptHostEncode_Release(IActiveScriptHostEncode* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptHostEncode methods ***/
static inline HRESULT IActiveScriptHostEncode_EncodeScriptHostFile(IActiveScriptHostEncode* This,BSTR bstrInFile,BSTR *pbstrOutFile,ULONG cFlags,BSTR bstrDefaultLang) {
    return This->lpVtbl->EncodeScriptHostFile(This,bstrInFile,pbstrOutFile,cFlags,bstrDefaultLang);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptHostEncode_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IBindEventHandler interface
 */
#ifndef __IBindEventHandler_INTERFACE_DEFINED__
#define __IBindEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IBindEventHandler, 0x63cdbcb0, 0xc1b1, 0x11d0, 0x93,0x36, 0x00,0xa0,0xc9,0x0d,0xca,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("63cdbcb0-c1b1-11d0-9336-00a0c90dcaa9")
IBindEventHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE BindHandler(
        LPCOLESTR pstrEvent,
        IDispatch *pdisp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IBindEventHandler, 0x63cdbcb0, 0xc1b1, 0x11d0, 0x93,0x36, 0x00,0xa0,0xc9,0x0d,0xca,0xa9)
#endif
#else
typedef struct IBindEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IBindEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IBindEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IBindEventHandler *This);

    /*** IBindEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *BindHandler)(
        IBindEventHandler *This,
        LPCOLESTR pstrEvent,
        IDispatch *pdisp);

    END_INTERFACE
} IBindEventHandlerVtbl;

interface IBindEventHandler {
    CONST_VTBL IBindEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IBindEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IBindEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IBindEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IBindEventHandler methods ***/
#define IBindEventHandler_BindHandler(This,pstrEvent,pdisp) (This)->lpVtbl->BindHandler(This,pstrEvent,pdisp)
#else
/*** IUnknown methods ***/
static inline HRESULT IBindEventHandler_QueryInterface(IBindEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IBindEventHandler_AddRef(IBindEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IBindEventHandler_Release(IBindEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IBindEventHandler methods ***/
static inline HRESULT IBindEventHandler_BindHandler(IBindEventHandler* This,LPCOLESTR pstrEvent,IDispatch *pdisp) {
    return This->lpVtbl->BindHandler(This,pstrEvent,pdisp);
}
#endif
#endif

#endif


#endif  /* __IBindEventHandler_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptStats interface
 */
#ifndef __IActiveScriptStats_INTERFACE_DEFINED__
#define __IActiveScriptStats_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptStats, 0xb8da6310, 0xe19b, 0x11d0, 0x93,0x3c, 0x00,0xa0,0xc9,0x0d,0xca,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b8da6310-e19b-11d0-933c-00a0c90dcaa9")
IActiveScriptStats : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStat(
        DWORD stid,
        ULONG *pluHi,
        ULONG *pluLo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatEx(
        REFGUID guid,
        ULONG *pluHi,
        ULONG *pluLo) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResetStats(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptStats, 0xb8da6310, 0xe19b, 0x11d0, 0x93,0x3c, 0x00,0xa0,0xc9,0x0d,0xca,0xa9)
#endif
#else
typedef struct IActiveScriptStatsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptStats *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptStats *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptStats *This);

    /*** IActiveScriptStats methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStat)(
        IActiveScriptStats *This,
        DWORD stid,
        ULONG *pluHi,
        ULONG *pluLo);

    HRESULT (STDMETHODCALLTYPE *GetStatEx)(
        IActiveScriptStats *This,
        REFGUID guid,
        ULONG *pluHi,
        ULONG *pluLo);

    HRESULT (STDMETHODCALLTYPE *ResetStats)(
        IActiveScriptStats *This);

    END_INTERFACE
} IActiveScriptStatsVtbl;

interface IActiveScriptStats {
    CONST_VTBL IActiveScriptStatsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptStats_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptStats_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptStats_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptStats methods ***/
#define IActiveScriptStats_GetStat(This,stid,pluHi,pluLo) (This)->lpVtbl->GetStat(This,stid,pluHi,pluLo)
#define IActiveScriptStats_GetStatEx(This,guid,pluHi,pluLo) (This)->lpVtbl->GetStatEx(This,guid,pluHi,pluLo)
#define IActiveScriptStats_ResetStats(This) (This)->lpVtbl->ResetStats(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptStats_QueryInterface(IActiveScriptStats* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptStats_AddRef(IActiveScriptStats* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptStats_Release(IActiveScriptStats* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptStats methods ***/
static inline HRESULT IActiveScriptStats_GetStat(IActiveScriptStats* This,DWORD stid,ULONG *pluHi,ULONG *pluLo) {
    return This->lpVtbl->GetStat(This,stid,pluHi,pluLo);
}
static inline HRESULT IActiveScriptStats_GetStatEx(IActiveScriptStats* This,REFGUID guid,ULONG *pluHi,ULONG *pluLo) {
    return This->lpVtbl->GetStatEx(This,guid,pluHi,pluLo);
}
static inline HRESULT IActiveScriptStats_ResetStats(IActiveScriptStats* This) {
    return This->lpVtbl->ResetStats(This);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptStats_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptProperty interface
 */
#ifndef __IActiveScriptProperty_INTERFACE_DEFINED__
#define __IActiveScriptProperty_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptProperty, 0x4954e0d0, 0xfbc7, 0x11d1, 0x84,0x10, 0x00,0x60,0x08,0xc3,0xfb,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4954e0d0-fbc7-11d1-8410-006008c3fbfc")
IActiveScriptProperty : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        DWORD dwProperty,
        VARIANT *pvarIndex,
        VARIANT *pvarValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProperty(
        DWORD dwProperty,
        VARIANT *pvarIndex,
        VARIANT *pvarValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptProperty, 0x4954e0d0, 0xfbc7, 0x11d1, 0x84,0x10, 0x00,0x60,0x08,0xc3,0xfb,0xfc)
#endif
#else
typedef struct IActiveScriptPropertyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptProperty *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptProperty *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptProperty *This);

    /*** IActiveScriptProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IActiveScriptProperty *This,
        DWORD dwProperty,
        VARIANT *pvarIndex,
        VARIANT *pvarValue);

    HRESULT (STDMETHODCALLTYPE *SetProperty)(
        IActiveScriptProperty *This,
        DWORD dwProperty,
        VARIANT *pvarIndex,
        VARIANT *pvarValue);

    END_INTERFACE
} IActiveScriptPropertyVtbl;

interface IActiveScriptProperty {
    CONST_VTBL IActiveScriptPropertyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptProperty_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptProperty_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptProperty_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptProperty methods ***/
#define IActiveScriptProperty_GetProperty(This,dwProperty,pvarIndex,pvarValue) (This)->lpVtbl->GetProperty(This,dwProperty,pvarIndex,pvarValue)
#define IActiveScriptProperty_SetProperty(This,dwProperty,pvarIndex,pvarValue) (This)->lpVtbl->SetProperty(This,dwProperty,pvarIndex,pvarValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptProperty_QueryInterface(IActiveScriptProperty* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptProperty_AddRef(IActiveScriptProperty* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptProperty_Release(IActiveScriptProperty* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptProperty methods ***/
static inline HRESULT IActiveScriptProperty_GetProperty(IActiveScriptProperty* This,DWORD dwProperty,VARIANT *pvarIndex,VARIANT *pvarValue) {
    return This->lpVtbl->GetProperty(This,dwProperty,pvarIndex,pvarValue);
}
static inline HRESULT IActiveScriptProperty_SetProperty(IActiveScriptProperty* This,DWORD dwProperty,VARIANT *pvarIndex,VARIANT *pvarValue) {
    return This->lpVtbl->SetProperty(This,dwProperty,pvarIndex,pvarValue);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptProperty_INTERFACE_DEFINED__ */


/*****************************************************************************
 * ITridentEventSink interface
 */
#ifndef __ITridentEventSink_INTERFACE_DEFINED__
#define __ITridentEventSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_ITridentEventSink, 0x1dc9ca50, 0x06ef, 0x11d2, 0x84,0x15, 0x00,0x60,0x08,0xc3,0xfb,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1dc9ca50-06ef-11d2-8415-006008c3fbfc")
ITridentEventSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE FireEvent(
        LPCOLESTR pstrEvent,
        DISPPARAMS *pdp,
        VARIANT *pvarRes,
        EXCEPINFO *pei) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ITridentEventSink, 0x1dc9ca50, 0x06ef, 0x11d2, 0x84,0x15, 0x00,0x60,0x08,0xc3,0xfb,0xfc)
#endif
#else
typedef struct ITridentEventSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ITridentEventSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ITridentEventSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ITridentEventSink *This);

    /*** ITridentEventSink methods ***/
    HRESULT (STDMETHODCALLTYPE *FireEvent)(
        ITridentEventSink *This,
        LPCOLESTR pstrEvent,
        DISPPARAMS *pdp,
        VARIANT *pvarRes,
        EXCEPINFO *pei);

    END_INTERFACE
} ITridentEventSinkVtbl;

interface ITridentEventSink {
    CONST_VTBL ITridentEventSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ITridentEventSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ITridentEventSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ITridentEventSink_Release(This) (This)->lpVtbl->Release(This)
/*** ITridentEventSink methods ***/
#define ITridentEventSink_FireEvent(This,pstrEvent,pdp,pvarRes,pei) (This)->lpVtbl->FireEvent(This,pstrEvent,pdp,pvarRes,pei)
#else
/*** IUnknown methods ***/
static inline HRESULT ITridentEventSink_QueryInterface(ITridentEventSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ITridentEventSink_AddRef(ITridentEventSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ITridentEventSink_Release(ITridentEventSink* This) {
    return This->lpVtbl->Release(This);
}
/*** ITridentEventSink methods ***/
static inline HRESULT ITridentEventSink_FireEvent(ITridentEventSink* This,LPCOLESTR pstrEvent,DISPPARAMS *pdp,VARIANT *pvarRes,EXCEPINFO *pei) {
    return This->lpVtbl->FireEvent(This,pstrEvent,pdp,pvarRes,pei);
}
#endif
#endif

#endif


#endif  /* __ITridentEventSink_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptGarbageCollector interface
 */
#ifndef __IActiveScriptGarbageCollector_INTERFACE_DEFINED__
#define __IActiveScriptGarbageCollector_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptGarbageCollector, 0x6aa2c4a0, 0x2b53, 0x11d4, 0xa2,0xa0, 0x00,0x10,0x4b,0xd3,0x50,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6aa2c4a0-2b53-11d4-a2a0-00104bd35090")
IActiveScriptGarbageCollector : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CollectGarbage(
        SCRIPTGCTYPE scriptgctype) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptGarbageCollector, 0x6aa2c4a0, 0x2b53, 0x11d4, 0xa2,0xa0, 0x00,0x10,0x4b,0xd3,0x50,0x90)
#endif
#else
typedef struct IActiveScriptGarbageCollectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptGarbageCollector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptGarbageCollector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptGarbageCollector *This);

    /*** IActiveScriptGarbageCollector methods ***/
    HRESULT (STDMETHODCALLTYPE *CollectGarbage)(
        IActiveScriptGarbageCollector *This,
        SCRIPTGCTYPE scriptgctype);

    END_INTERFACE
} IActiveScriptGarbageCollectorVtbl;

interface IActiveScriptGarbageCollector {
    CONST_VTBL IActiveScriptGarbageCollectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptGarbageCollector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptGarbageCollector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptGarbageCollector_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptGarbageCollector methods ***/
#define IActiveScriptGarbageCollector_CollectGarbage(This,scriptgctype) (This)->lpVtbl->CollectGarbage(This,scriptgctype)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptGarbageCollector_QueryInterface(IActiveScriptGarbageCollector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptGarbageCollector_AddRef(IActiveScriptGarbageCollector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptGarbageCollector_Release(IActiveScriptGarbageCollector* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptGarbageCollector methods ***/
static inline HRESULT IActiveScriptGarbageCollector_CollectGarbage(IActiveScriptGarbageCollector* This,SCRIPTGCTYPE scriptgctype) {
    return This->lpVtbl->CollectGarbage(This,scriptgctype);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptGarbageCollector_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptSIPInfo interface
 */
#ifndef __IActiveScriptSIPInfo_INTERFACE_DEFINED__
#define __IActiveScriptSIPInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptSIPInfo, 0x764651d0, 0x38de, 0x11d4, 0xa2,0xa3, 0x00,0x10,0x4b,0xd3,0x50,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("764651d0-38de-11d4-a2a3-00104bd35090")
IActiveScriptSIPInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSIPOID(
        GUID *poid_sip) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptSIPInfo, 0x764651d0, 0x38de, 0x11d4, 0xa2,0xa3, 0x00,0x10,0x4b,0xd3,0x50,0x90)
#endif
#else
typedef struct IActiveScriptSIPInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptSIPInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptSIPInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptSIPInfo *This);

    /*** IActiveScriptSIPInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSIPOID)(
        IActiveScriptSIPInfo *This,
        GUID *poid_sip);

    END_INTERFACE
} IActiveScriptSIPInfoVtbl;

interface IActiveScriptSIPInfo {
    CONST_VTBL IActiveScriptSIPInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptSIPInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptSIPInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptSIPInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptSIPInfo methods ***/
#define IActiveScriptSIPInfo_GetSIPOID(This,poid_sip) (This)->lpVtbl->GetSIPOID(This,poid_sip)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptSIPInfo_QueryInterface(IActiveScriptSIPInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptSIPInfo_AddRef(IActiveScriptSIPInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptSIPInfo_Release(IActiveScriptSIPInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptSIPInfo methods ***/
static inline HRESULT IActiveScriptSIPInfo_GetSIPOID(IActiveScriptSIPInfo* This,GUID *poid_sip) {
    return This->lpVtbl->GetSIPOID(This,poid_sip);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptSIPInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptSiteTraceInfo interface
 */
#ifndef __IActiveScriptSiteTraceInfo_INTERFACE_DEFINED__
#define __IActiveScriptSiteTraceInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptSiteTraceInfo, 0x4b7272ae, 0x1955, 0x4bfe, 0x98,0xb0, 0x78,0x06,0x21,0x88,0x85,0x69);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4b7272ae-1955-4bfe-98b0-************")
IActiveScriptSiteTraceInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SendScriptTraceInfo(
        SCRIPTTRACEINFO stiEventType,
        GUID guidContextID,
        DWORD dwScriptContextCookie,
        LONG lScriptStatementStart,
        LONG lScriptStatementEnd,
        DWORD64 dwReserved) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptSiteTraceInfo, 0x4b7272ae, 0x1955, 0x4bfe, 0x98,0xb0, 0x78,0x06,0x21,0x88,0x85,0x69)
#endif
#else
typedef struct IActiveScriptSiteTraceInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptSiteTraceInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptSiteTraceInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptSiteTraceInfo *This);

    /*** IActiveScriptSiteTraceInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *SendScriptTraceInfo)(
        IActiveScriptSiteTraceInfo *This,
        SCRIPTTRACEINFO stiEventType,
        GUID guidContextID,
        DWORD dwScriptContextCookie,
        LONG lScriptStatementStart,
        LONG lScriptStatementEnd,
        DWORD64 dwReserved);

    END_INTERFACE
} IActiveScriptSiteTraceInfoVtbl;

interface IActiveScriptSiteTraceInfo {
    CONST_VTBL IActiveScriptSiteTraceInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptSiteTraceInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptSiteTraceInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptSiteTraceInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptSiteTraceInfo methods ***/
#define IActiveScriptSiteTraceInfo_SendScriptTraceInfo(This,stiEventType,guidContextID,dwScriptContextCookie,lScriptStatementStart,lScriptStatementEnd,dwReserved) (This)->lpVtbl->SendScriptTraceInfo(This,stiEventType,guidContextID,dwScriptContextCookie,lScriptStatementStart,lScriptStatementEnd,dwReserved)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptSiteTraceInfo_QueryInterface(IActiveScriptSiteTraceInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptSiteTraceInfo_AddRef(IActiveScriptSiteTraceInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptSiteTraceInfo_Release(IActiveScriptSiteTraceInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptSiteTraceInfo methods ***/
static inline HRESULT IActiveScriptSiteTraceInfo_SendScriptTraceInfo(IActiveScriptSiteTraceInfo* This,SCRIPTTRACEINFO stiEventType,GUID guidContextID,DWORD dwScriptContextCookie,LONG lScriptStatementStart,LONG lScriptStatementEnd,DWORD64 dwReserved) {
    return This->lpVtbl->SendScriptTraceInfo(This,stiEventType,guidContextID,dwScriptContextCookie,lScriptStatementStart,lScriptStatementEnd,dwReserved);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptSiteTraceInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptTraceInfo interface
 */
#ifndef __IActiveScriptTraceInfo_INTERFACE_DEFINED__
#define __IActiveScriptTraceInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptTraceInfo, 0xc35456e7, 0xbebf, 0x4a1b, 0x86,0xa9, 0x24,0xd5,0x6b,0xe8,0xb3,0x69);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c35456e7-bebf-4a1b-86a9-24d56be8b369")
IActiveScriptTraceInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE StartScriptTracing(
        IActiveScriptSiteTraceInfo *pSiteTraceInfo,
        GUID guidContextID) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopScriptTracing(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptTraceInfo, 0xc35456e7, 0xbebf, 0x4a1b, 0x86,0xa9, 0x24,0xd5,0x6b,0xe8,0xb3,0x69)
#endif
#else
typedef struct IActiveScriptTraceInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptTraceInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptTraceInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptTraceInfo *This);

    /*** IActiveScriptTraceInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *StartScriptTracing)(
        IActiveScriptTraceInfo *This,
        IActiveScriptSiteTraceInfo *pSiteTraceInfo,
        GUID guidContextID);

    HRESULT (STDMETHODCALLTYPE *StopScriptTracing)(
        IActiveScriptTraceInfo *This);

    END_INTERFACE
} IActiveScriptTraceInfoVtbl;

interface IActiveScriptTraceInfo {
    CONST_VTBL IActiveScriptTraceInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptTraceInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptTraceInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptTraceInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptTraceInfo methods ***/
#define IActiveScriptTraceInfo_StartScriptTracing(This,pSiteTraceInfo,guidContextID) (This)->lpVtbl->StartScriptTracing(This,pSiteTraceInfo,guidContextID)
#define IActiveScriptTraceInfo_StopScriptTracing(This) (This)->lpVtbl->StopScriptTracing(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptTraceInfo_QueryInterface(IActiveScriptTraceInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptTraceInfo_AddRef(IActiveScriptTraceInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptTraceInfo_Release(IActiveScriptTraceInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptTraceInfo methods ***/
static inline HRESULT IActiveScriptTraceInfo_StartScriptTracing(IActiveScriptTraceInfo* This,IActiveScriptSiteTraceInfo *pSiteTraceInfo,GUID guidContextID) {
    return This->lpVtbl->StartScriptTracing(This,pSiteTraceInfo,guidContextID);
}
static inline HRESULT IActiveScriptTraceInfo_StopScriptTracing(IActiveScriptTraceInfo* This) {
    return This->lpVtbl->StopScriptTracing(This);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptTraceInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IActiveScriptStringCompare interface
 */
#ifndef __IActiveScriptStringCompare_INTERFACE_DEFINED__
#define __IActiveScriptStringCompare_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptStringCompare, 0x58562769, 0xed52, 0x42f7, 0x84,0x03, 0x49,0x63,0x51,0x4e,0x1f,0x11);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("58562769-ed52-42f7-8403-4963514e1f11")
IActiveScriptStringCompare : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE StrComp(
        BSTR bszStr1,
        BSTR bszStr2,
        LONG *iRet) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptStringCompare, 0x58562769, 0xed52, 0x42f7, 0x84,0x03, 0x49,0x63,0x51,0x4e,0x1f,0x11)
#endif
#else
typedef struct IActiveScriptStringCompareVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptStringCompare *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptStringCompare *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptStringCompare *This);

    /*** IActiveScriptStringCompare methods ***/
    HRESULT (STDMETHODCALLTYPE *StrComp)(
        IActiveScriptStringCompare *This,
        BSTR bszStr1,
        BSTR bszStr2,
        LONG *iRet);

    END_INTERFACE
} IActiveScriptStringCompareVtbl;

interface IActiveScriptStringCompare {
    CONST_VTBL IActiveScriptStringCompareVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptStringCompare_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptStringCompare_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptStringCompare_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptStringCompare methods ***/
#define IActiveScriptStringCompare_StrComp(This,bszStr1,bszStr2,iRet) (This)->lpVtbl->StrComp(This,bszStr1,bszStr2,iRet)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptStringCompare_QueryInterface(IActiveScriptStringCompare* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptStringCompare_AddRef(IActiveScriptStringCompare* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptStringCompare_Release(IActiveScriptStringCompare* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptStringCompare methods ***/
static inline HRESULT IActiveScriptStringCompare_StrComp(IActiveScriptStringCompare* This,BSTR bszStr1,BSTR bszStr2,LONG *iRet) {
    return This->lpVtbl->StrComp(This,bszStr1,bszStr2,iRet);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptStringCompare_INTERFACE_DEFINED__ */

#endif

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);
ULONG           __RPC_USER HWND_UserSize     (ULONG *, ULONG, HWND *);
unsigned char * __RPC_USER HWND_UserMarshal  (ULONG *, unsigned char *, HWND *);
unsigned char * __RPC_USER HWND_UserUnmarshal(ULONG *, unsigned char *, HWND *);
void            __RPC_USER HWND_UserFree     (ULONG *, HWND *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __activscp_h__ */
