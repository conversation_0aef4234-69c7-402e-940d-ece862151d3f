/*** Autogenerated by WIDL 10.12 from include/dinputd.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dinputd_h__
#define __dinputd_h__

/* Forward declarations */

#ifndef __IDirectInputEffectDriver_FWD_DEFINED__
#define __IDirectInputEffectDriver_FWD_DEFINED__
typedef interface IDirectInputEffectDriver IDirectInputEffectDriver;
#ifdef __cplusplus
interface IDirectInputEffectDriver;
#endif /* __cplusplus */
#endif

#ifndef __IDirectInputJoyConfig_FWD_DEFINED__
#define __IDirectInputJoyConfig_FWD_DEFINED__
typedef interface IDirectInputJoyConfig IDirectInputJoyConfig;
#ifdef __cplusplus
interface IDirectInputJoyConfig;
#endif /* __cplusplus */
#endif

#ifndef __IDirectInputJoyConfig8_FWD_DEFINED__
#define __IDirectInputJoyConfig8_FWD_DEFINED__
typedef interface IDirectInputJoyConfig8 IDirectInputJoyConfig8;
#ifdef __cplusplus
interface IDirectInputJoyConfig8;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>

#ifdef __cplusplus
extern "C" {
#endif

#include "winapifamily.h"
#ifndef DIRECTINPUT_VERSION
#define DIRECTINPUT_VERSION 0x0800
#endif
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

typedef struct DIOBJECTATTRIBUTES {
    DWORD dwFlags;
    WORD wUsagePage;
    WORD wUsage;
} DIOBJECTATTRIBUTES;
typedef struct DIOBJECTATTRIBUTES *LPDIOBJECTATTRIBUTES;
typedef const DIOBJECTATTRIBUTES *LPCDIOBJECTATTRIBUTES;

typedef struct DIFFOBJECTATTRIBUTES {
    DWORD dwFFMaxForce;
    DWORD dwFFForceResolution;
} DIFFOBJECTATTRIBUTES;
typedef struct DIFFOBJECTATTRIBUTES *LPDIFFOBJECTATTRIBUTES;
typedef const DIFFOBJECTATTRIBUTES *LPCDIFFOBJECTATTRIBUTES;

typedef struct DIOBJECTCALIBRATION {
    LONG lMin;
    LONG lCenter;
    LONG lMax;
} DIOBJECTCALIBRATION;
typedef struct DIOBJECTCALIBRATION *LPDIOBJECTCALIBRATION;
typedef const DIOBJECTCALIBRATION *LPCDIOBJECTCALIBRATION;

typedef struct DIPOVCALIBRATION {
    LONG lMin[5];
    LONG lMax[5];
} DIPOVCALIBRATION;
typedef struct DIPOVCALIBRATION *LPDIPOVCALIBRATION;
typedef const DIPOVCALIBRATION *LPCDIPOVCALIBRATION;

typedef struct DIEFFECTATTRIBUTES {
    DWORD dwEffectId;
    DWORD dwEffType;
    DWORD dwStaticParams;
    DWORD dwDynamicParams;
    DWORD dwCoords;
} DIEFFECTATTRIBUTES;
typedef struct DIEFFECTATTRIBUTES *LPDIEFFECTATTRIBUTES;
typedef const DIEFFECTATTRIBUTES *LPCDIEFFECTATTRIBUTES;

typedef struct DIFFDEVICEATTRIBUTES {
    DWORD dwFlags;
    DWORD dwFFSamplePeriod;
    DWORD dwFFMinTimeResolution;
} DIFFDEVICEATTRIBUTES;
typedef struct DIFFDEVICEATTRIBUTES *LPDIFFDEVICEATTRIBUTES;
typedef const DIFFDEVICEATTRIBUTES *LPCDIFFDEVICEATTRIBUTES;

typedef struct DIDRIVERVERSIONS {
    DWORD dwSize;
    DWORD dwFirmwareRevision;
    DWORD dwHardwareRevision;
    DWORD dwFFDriverVersion;
} DIDRIVERVERSIONS;
typedef struct DIDRIVERVERSIONS *LPDIDRIVERVERSIONS;
typedef const DIDRIVERVERSIONS *LPCDIDRIVERVERSIONS;

typedef struct DIDEVICESTATE {
    DWORD dwSize;
    DWORD dwState;
    DWORD dwLoad;
} DIDEVICESTATE;
typedef struct DIDEVICESTATE *LPDIDEVICESTATE;
#define DEV_STS_EFFECT_RUNNING DIEGES_PLAYING
#ifndef DIJ_RINGZERO

typedef struct DIHIDFFINITINFO {
    DWORD dwSize;
    LPWSTR pwszDeviceInterface;
    GUID GuidInstance;
} DIHIDFFINITINFO;
typedef struct DIHIDFFINITINFO *LPDIHIDFFINITINFO;
#if 0
typedef struct DIEFFESCAPE DIEFFESCAPE;
typedef struct DIEFFESCAPE *LPDIEFFESCAPE;
typedef struct DIEFFECT DIEFFECT;
typedef struct DIEFFECT *LPDIEFFECT;
typedef const DIEFFECT *LPCDIEFFECT;
#endif

/*****************************************************************************
 * IDirectInputEffectDriver interface
 */
#ifndef __IDirectInputEffectDriver_INTERFACE_DEFINED__
#define __IDirectInputEffectDriver_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectInputEffectDriver, 0x02538130, 0x898f, 0x11d0, 0x9a,0xd0, 0x00,0xa0,0xc9,0xa0,0x6e,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("02538130-898f-11d0-9ad0-00a0c9a06e35")
IDirectInputEffectDriver : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE DeviceID(
        DWORD a,
        DWORD b,
        DWORD c,
        DWORD d,
        LPVOID e) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVersions(
        LPDIDRIVERVERSIONS a) = 0;

    virtual HRESULT STDMETHODCALLTYPE Escape(
        DWORD a,
        DWORD b,
        LPDIEFFESCAPE c) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGain(
        DWORD a,
        DWORD b) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendForceFeedbackCommand(
        DWORD a,
        DWORD b) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetForceFeedbackState(
        DWORD a,
        LPDIDEVICESTATE b) = 0;

    virtual HRESULT STDMETHODCALLTYPE DownloadEffect(
        DWORD a,
        DWORD b,
        LPDWORD c,
        LPCDIEFFECT d,
        DWORD e) = 0;

    virtual HRESULT STDMETHODCALLTYPE DestroyEffect(
        DWORD a,
        DWORD b) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartEffect(
        DWORD a,
        DWORD b,
        DWORD c,
        DWORD d) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopEffect(
        DWORD a,
        DWORD b) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEffectStatus(
        DWORD a,
        DWORD b,
        LPDWORD c) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectInputEffectDriver, 0x02538130, 0x898f, 0x11d0, 0x9a,0xd0, 0x00,0xa0,0xc9,0xa0,0x6e,0x35)
#endif
#else
typedef struct IDirectInputEffectDriverVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectInputEffectDriver *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectInputEffectDriver *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectInputEffectDriver *This);

    /*** IDirectInputEffectDriver methods ***/
    HRESULT (STDMETHODCALLTYPE *DeviceID)(
        IDirectInputEffectDriver *This,
        DWORD a,
        DWORD b,
        DWORD c,
        DWORD d,
        LPVOID e);

    HRESULT (STDMETHODCALLTYPE *GetVersions)(
        IDirectInputEffectDriver *This,
        LPDIDRIVERVERSIONS a);

    HRESULT (STDMETHODCALLTYPE *Escape)(
        IDirectInputEffectDriver *This,
        DWORD a,
        DWORD b,
        LPDIEFFESCAPE c);

    HRESULT (STDMETHODCALLTYPE *SetGain)(
        IDirectInputEffectDriver *This,
        DWORD a,
        DWORD b);

    HRESULT (STDMETHODCALLTYPE *SendForceFeedbackCommand)(
        IDirectInputEffectDriver *This,
        DWORD a,
        DWORD b);

    HRESULT (STDMETHODCALLTYPE *GetForceFeedbackState)(
        IDirectInputEffectDriver *This,
        DWORD a,
        LPDIDEVICESTATE b);

    HRESULT (STDMETHODCALLTYPE *DownloadEffect)(
        IDirectInputEffectDriver *This,
        DWORD a,
        DWORD b,
        LPDWORD c,
        LPCDIEFFECT d,
        DWORD e);

    HRESULT (STDMETHODCALLTYPE *DestroyEffect)(
        IDirectInputEffectDriver *This,
        DWORD a,
        DWORD b);

    HRESULT (STDMETHODCALLTYPE *StartEffect)(
        IDirectInputEffectDriver *This,
        DWORD a,
        DWORD b,
        DWORD c,
        DWORD d);

    HRESULT (STDMETHODCALLTYPE *StopEffect)(
        IDirectInputEffectDriver *This,
        DWORD a,
        DWORD b);

    HRESULT (STDMETHODCALLTYPE *GetEffectStatus)(
        IDirectInputEffectDriver *This,
        DWORD a,
        DWORD b,
        LPDWORD c);

    END_INTERFACE
} IDirectInputEffectDriverVtbl;

interface IDirectInputEffectDriver {
    CONST_VTBL IDirectInputEffectDriverVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectInputEffectDriver_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectInputEffectDriver_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectInputEffectDriver_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectInputEffectDriver methods ***/
#define IDirectInputEffectDriver_DeviceID(This,a,b,c,d,e) (This)->lpVtbl->DeviceID(This,a,b,c,d,e)
#define IDirectInputEffectDriver_GetVersions(This,a) (This)->lpVtbl->GetVersions(This,a)
#define IDirectInputEffectDriver_Escape(This,a,b,c) (This)->lpVtbl->Escape(This,a,b,c)
#define IDirectInputEffectDriver_SetGain(This,a,b) (This)->lpVtbl->SetGain(This,a,b)
#define IDirectInputEffectDriver_SendForceFeedbackCommand(This,a,b) (This)->lpVtbl->SendForceFeedbackCommand(This,a,b)
#define IDirectInputEffectDriver_GetForceFeedbackState(This,a,b) (This)->lpVtbl->GetForceFeedbackState(This,a,b)
#define IDirectInputEffectDriver_DownloadEffect(This,a,b,c,d,e) (This)->lpVtbl->DownloadEffect(This,a,b,c,d,e)
#define IDirectInputEffectDriver_DestroyEffect(This,a,b) (This)->lpVtbl->DestroyEffect(This,a,b)
#define IDirectInputEffectDriver_StartEffect(This,a,b,c,d) (This)->lpVtbl->StartEffect(This,a,b,c,d)
#define IDirectInputEffectDriver_StopEffect(This,a,b) (This)->lpVtbl->StopEffect(This,a,b)
#define IDirectInputEffectDriver_GetEffectStatus(This,a,b,c) (This)->lpVtbl->GetEffectStatus(This,a,b,c)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectInputEffectDriver_QueryInterface(IDirectInputEffectDriver* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectInputEffectDriver_AddRef(IDirectInputEffectDriver* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectInputEffectDriver_Release(IDirectInputEffectDriver* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectInputEffectDriver methods ***/
static inline HRESULT IDirectInputEffectDriver_DeviceID(IDirectInputEffectDriver* This,DWORD a,DWORD b,DWORD c,DWORD d,LPVOID e) {
    return This->lpVtbl->DeviceID(This,a,b,c,d,e);
}
static inline HRESULT IDirectInputEffectDriver_GetVersions(IDirectInputEffectDriver* This,LPDIDRIVERVERSIONS a) {
    return This->lpVtbl->GetVersions(This,a);
}
static inline HRESULT IDirectInputEffectDriver_Escape(IDirectInputEffectDriver* This,DWORD a,DWORD b,LPDIEFFESCAPE c) {
    return This->lpVtbl->Escape(This,a,b,c);
}
static inline HRESULT IDirectInputEffectDriver_SetGain(IDirectInputEffectDriver* This,DWORD a,DWORD b) {
    return This->lpVtbl->SetGain(This,a,b);
}
static inline HRESULT IDirectInputEffectDriver_SendForceFeedbackCommand(IDirectInputEffectDriver* This,DWORD a,DWORD b) {
    return This->lpVtbl->SendForceFeedbackCommand(This,a,b);
}
static inline HRESULT IDirectInputEffectDriver_GetForceFeedbackState(IDirectInputEffectDriver* This,DWORD a,LPDIDEVICESTATE b) {
    return This->lpVtbl->GetForceFeedbackState(This,a,b);
}
static inline HRESULT IDirectInputEffectDriver_DownloadEffect(IDirectInputEffectDriver* This,DWORD a,DWORD b,LPDWORD c,LPCDIEFFECT d,DWORD e) {
    return This->lpVtbl->DownloadEffect(This,a,b,c,d,e);
}
static inline HRESULT IDirectInputEffectDriver_DestroyEffect(IDirectInputEffectDriver* This,DWORD a,DWORD b) {
    return This->lpVtbl->DestroyEffect(This,a,b);
}
static inline HRESULT IDirectInputEffectDriver_StartEffect(IDirectInputEffectDriver* This,DWORD a,DWORD b,DWORD c,DWORD d) {
    return This->lpVtbl->StartEffect(This,a,b,c,d);
}
static inline HRESULT IDirectInputEffectDriver_StopEffect(IDirectInputEffectDriver* This,DWORD a,DWORD b) {
    return This->lpVtbl->StopEffect(This,a,b);
}
static inline HRESULT IDirectInputEffectDriver_GetEffectStatus(IDirectInputEffectDriver* This,DWORD a,DWORD b,LPDWORD c) {
    return This->lpVtbl->GetEffectStatus(This,a,b,c);
}
#endif
#endif

#endif


#endif  /* __IDirectInputEffectDriver_INTERFACE_DEFINED__ */

#endif /* DIJ_RINGZERO */
#ifndef JOY_HW_NONE
#define JOY_HW_NONE 0
#define JOY_HW_CUSTOM 1
#define JOY_HW_2A_2B_GENERIC 2
#define JOY_HW_2A_4B_GENERIC 3
#define JOY_HW_2B_GAMEPAD 4
#define JOY_HW_2B_FLIGHTYOKE 5
#define JOY_HW_2B_FLIGHTYOKETHROTTLE 6
#define JOY_HW_3A_2B_GENERIC 7
#define JOY_HW_3A_4B_GENERIC 8
#define JOY_HW_4B_GAMEPAD 9
#define JOY_HW_4B_FLIGHTYOKE 10
#define JOY_HW_4B_FLIGHTYOKETHROTTLE 11
#define JOY_HW_TWO_2A_2B_WITH_Y 12
#define JOY_HW_LASTENTRY 13
#define JOY_ISCAL_XY 0x00000001
#define JOY_ISCAL_Z 0x00000002
#define JOY_ISCAL_R 0x00000004
#define JOY_ISCAL_U 0x00000008
#define JOY_ISCAL_V 0x00000010
#define JOY_ISCAL_POV 0x00000020
#define JOY_POVVAL_FORWARD (0)

#define JOY_POVVAL_BACKWARD (1)

#define JOY_POVVAL_LEFT (2)

#define JOY_POVVAL_RIGHT (3)

#define JOY_POV_NUMDIRS (4)

#define JOY_HWS_HASZ 0x00000001
#define JOY_HWS_HASPOV 0x00000002
#define JOY_HWS_POVISBUTTONCOMBOS 0x00000004
#define JOY_HWS_POVISPOLL 0x00000008
#define JOY_HWS_ISYOKE 0x00000010
#define JOY_HWS_ISGAMEPAD 0x00000020
#define JOY_HWS_ISCARCTRL 0x00000040
#define JOY_HWS_XISJ1Y 0x00000080
#define JOY_HWS_XISJ2X 0x00000100
#define JOY_HWS_XISJ2Y 0x00000200
#define JOY_HWS_YISJ1X 0x00000400
#define JOY_HWS_YISJ2X 0x00000800
#define JOY_HWS_YISJ2Y 0x00001000
#define JOY_HWS_ZISJ1X 0x00002000
#define JOY_HWS_ZISJ1Y 0x00004000
#define JOY_HWS_ZISJ2X 0x00008000
#define JOY_HWS_POVISJ1X 0x00010000
#define JOY_HWS_POVISJ1Y 0x00020000
#define JOY_HWS_POVISJ2X 0x00040000
#define JOY_HWS_HASR 0x00080000
#define JOY_HWS_RISJ1X 0x00100000
#define JOY_HWS_RISJ1Y 0x00200000
#define JOY_HWS_RISJ2Y 0x00400000
#define JOY_HWS_HASU 0x00800000
#define JOY_HWS_HASV 0x01000000
#define JOY_US_HASRUDDER 0x00000001
#define JOY_US_PRESENT 0x00000002
#define JOY_US_ISOEM 0x00000004
#define JOY_US_RESERVED 0x80000000
#define JOYTYPE_ZEROGAMEENUMOEMDATA 0x00000001
#define JOYTYPE_NOAUTODETECTGAMEPORT 0x00000002
#define JOYTYPE_NOHIDDIRECT 0x00000004
#define JOYTYPE_ANALOGCOMPAT 0x00000008
#define JOYTYPE_DEFAULTPROPSHEET 0x80000000
#define JOYTYPE_DEVICEHIDE 0x00010000
#define JOYTYPE_MOUSEHIDE 0x00020000
#define JOYTYPE_KEYBHIDE 0x00040000
#define JOYTYPE_GAMEHIDE 0x00080000
#define JOYTYPE_HIDEACTIVE 0x00100000
#define JOYTYPE_INFOMASK 0x00E00000
#define JOYTYPE_INFODEFAULT 0x00000000
#define JOYTYPE_INFOYYPEDALS 0x00200000
#define JOYTYPE_INFOZYPEDALS 0x00400000
#define JOYTYPE_INFOYRPEDALS 0x00600000
#define JOYTYPE_INFOZRPEDALS 0x00800000
#define JOYTYPE_INFOZISSLIDER 0x00200000
#define JOYTYPE_INFOZISZ 0x00400000
#define JOYTYPE_ENABLEINPUTREPORT 0x01000000

typedef struct joypos_tag {
    DWORD dwX;
    DWORD dwY;
    DWORD dwZ;
    DWORD dwR;
    DWORD dwU;
    DWORD dwV;
} JOYPOS;
typedef struct joypos_tag *LPJOYPOS;

typedef struct joyrange_tag {
    JOYPOS jpMin;
    JOYPOS jpMax;
    JOYPOS jpCenter;
} JOYRANGE;
typedef struct joyrange_tag *LPJOYRANGE;

typedef struct joyreguservalues_tag {
    DWORD dwTimeOut;
    JOYRANGE jrvRanges;
    JOYPOS jpDeadZone;
} JOYREGUSERVALUES;
typedef struct joyreguservalues_tag *LPJOYREGUSERVALUES;

typedef struct joyreghwsettings_tag {
    DWORD dwFlags;
    DWORD dwNumButtons;
} JOYREGHWSETTINGS;
typedef struct joyreghwsettings_tag *LPJOYHWSETTINGS;

typedef struct joyreghwvalues_tag {
    JOYRANGE jrvHardware;
    DWORD dwPOVValues[4];
    DWORD dwCalFlags;
} JOYREGHWVALUES;
typedef struct joyreghwvalues_tag *LPJOYREGHWVALUES;

typedef struct joyreghwconfig_tag {
    JOYREGHWSETTINGS hws;
    DWORD dwUsageSettings;
    JOYREGHWVALUES hwv;
    DWORD dwType;
    DWORD dwReserved;
} JOYREGHWCONFIG;
typedef struct joyreghwconfig_tag *LPJOYREGHWCONFIG;

typedef struct joycalibrate_tag {
    UINT wXbase;
    UINT wXdelta;
    UINT wYbase;
    UINT wYdelta;
    UINT wZbase;
    UINT wZdelta;
} JOYCALIBRATE;
typedef struct joycalibrate_tag *LPJOYCALIBRATE;
#endif /* JOY_HW_NONE */
#ifndef DIJ_RINGZERO
#define MAX_JOYSTRING (256)

typedef WINBOOL (WINAPI *LPDIJOYTYPECALLBACK)(LPCWSTR, LPVOID);
#if 0
#define MAX_JOYSTICKOEMVXDNAME (260)

#endif
#ifndef MAX_JOYSTICKOEMVXDNAME
#define MAX_JOYSTICKOEMVXDNAME 260
#endif
#define DITC_REGHWSETTINGS 0x00000001
#define DITC_CLSIDCONFIG 0x00000002
#define DITC_DISPLAYNAME 0x00000004
#define DITC_CALLOUT 0x00000008
#define DITC_HARDWAREID 0x00000010
#define DITC_FLAGS1 0x00000020
#define DITC_FLAGS2 0x00000040
#define DITC_MAPFILE 0x00000080

typedef struct DIJOYTYPEINFO_DX5 {
    DWORD dwSize;
    JOYREGHWSETTINGS hws;
    CLSID clsidConfig;
    WCHAR wszDisplayName[256];
    WCHAR wszCallout[260];
} DIJOYTYPEINFO_DX5;
typedef struct DIJOYTYPEINFO_DX5 *LPDIJOYTYPEINFO_DX5;
typedef const DIJOYTYPEINFO_DX5 *LPCDIJOYTYPEINFO_DX5;

typedef struct DIJOYTYPEINFO_DX6 {
    DWORD dwSize;
    JOYREGHWSETTINGS hws;
    CLSID clsidConfig;
    WCHAR wszDisplayName[256];
    WCHAR wszCallout[260];
    WCHAR wszHardwareId[256];
    DWORD dwFlags1;
} DIJOYTYPEINFO_DX6;
typedef struct DIJOYTYPEINFO_DX6 *LPDIJOYTYPEINFO_DX6;
typedef const DIJOYTYPEINFO_DX6 *LPCDIJOYTYPEINFO_DX6;

typedef struct DIJOYTYPEINFO {
    DWORD dwSize;
    JOYREGHWSETTINGS hws;
    CLSID clsidConfig;
    WCHAR wszDisplayName[256];
    WCHAR wszCallout[260];
    WCHAR wszHardwareId[256];
    DWORD dwFlags1;
    DWORD dwFlags2;
    WCHAR wszMapFile[256];
} DIJOYTYPEINFO;
typedef struct DIJOYTYPEINFO *LPDIJOYTYPEINFO;
typedef const DIJOYTYPEINFO *LPCDIJOYTYPEINFO;
#define DIJC_GUIDINSTANCE 0x00000001
#define DIJC_REGHWCONFIGTYPE 0x00000002
#define DIJC_GAIN 0x00000004
#define DIJC_CALLOUT 0x00000008
#define DIJC_WDMGAMEPORT 0x00000010

typedef struct DIJOYCONFIG_DX5 {
    DWORD dwSize;
    GUID guidInstance;
    JOYREGHWCONFIG hwc;
    DWORD dwGain;
    WCHAR wszType[256];
    WCHAR wszCallout[256];
} DIJOYCONFIG_DX5;
typedef struct DIJOYCONFIG_DX5 *LPDIJOYCONFIG_DX5;
typedef const DIJOYCONFIG_DX5 *LPCDIJOYCONFIG_DX5;

typedef struct DIJOYCONFIG {
    DWORD dwSize;
    GUID guidInstance;
    JOYREGHWCONFIG hwc;
    DWORD dwGain;
    WCHAR wszType[256];
    WCHAR wszCallout[256];
    GUID guidGameport;
} DIJOYCONFIG;
typedef struct DIJOYCONFIG *LPDIJOYCONFIG;
typedef const DIJOYCONFIG *LPCDIJOYCONFIG;
#define DIJU_USERVALUES 0x00000001
#define DIJU_GLOBALDRIVER 0x00000002
#define DIJU_GAMEPORTEMULATOR 0x00000004

typedef struct DIJOYUSERVALUES {
    DWORD dwSize;
    JOYREGUSERVALUES ruv;
    WCHAR wszGlobalDriver[256];
    WCHAR wszGameportEmulator[256];
} DIJOYUSERVALUES;
typedef struct DIJOYUSERVALUES *LPDIJOYUSERVALUES;
typedef const DIJOYUSERVALUES *LPCDIJOYUSERVALUES;
DEFINE_GUID(GUID_KeyboardClass, 0x4d36e96b,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_MediaClass, 0x4d36e96c,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_MouseClass, 0x4d36e96f,0xe325,0x11ce,0xbf,0xc1,0x08,0x00,0x2b,0xe1,0x03,0x18);
DEFINE_GUID(GUID_HIDClass, 0x745a17a0,0x74d3,0x11d0,0xb6,0xfe,0x00,0xa0,0xc9,0x0f,0x57,0xda);

/*****************************************************************************
 * IDirectInputJoyConfig interface
 */
#ifndef __IDirectInputJoyConfig_INTERFACE_DEFINED__
#define __IDirectInputJoyConfig_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectInputJoyConfig, 0x1de12ab1, 0xc9f5, 0x11cf, 0xbf,0xc7, 0x44,0x45,0x53,0x54,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1de12ab1-c9f5-11cf-bfc7-************")
IDirectInputJoyConfig : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Acquire(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unacquire(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCooperativeLevel(
        HWND a,
        DWORD b) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendNotify(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumTypes(
        LPVOID a,
        LPVOID b) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypeInfo(
        LPCWSTR a,
        LPDIJOYTYPEINFO b,
        DWORD c) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTypeInfo(
        LPCWSTR a,
        LPCDIJOYTYPEINFO b,
        DWORD c) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteType(
        LPCWSTR a) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConfig(
        UINT a,
        LPDIJOYCONFIG b,
        DWORD c) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetConfig(
        UINT a,
        LPCDIJOYCONFIG b,
        DWORD c) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteConfig(
        UINT a) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserValues(
        LPDIJOYUSERVALUES a,
        DWORD b) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUserValues(
        LPCDIJOYUSERVALUES a,
        DWORD b) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddNewHardware(
        HWND a,
        REFGUID b) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenTypeKey(
        LPCWSTR a,
        DWORD b,
        HKEY *c) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenConfigKey(
        UINT a,
        DWORD b,
        HKEY *c) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectInputJoyConfig, 0x1de12ab1, 0xc9f5, 0x11cf, 0xbf,0xc7, 0x44,0x45,0x53,0x54,0x00,0x00)
#endif
#else
typedef struct IDirectInputJoyConfigVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectInputJoyConfig *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectInputJoyConfig *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectInputJoyConfig *This);

    /*** IDirectInputJoyConfig methods ***/
    HRESULT (STDMETHODCALLTYPE *Acquire)(
        IDirectInputJoyConfig *This);

    HRESULT (STDMETHODCALLTYPE *Unacquire)(
        IDirectInputJoyConfig *This);

    HRESULT (STDMETHODCALLTYPE *SetCooperativeLevel)(
        IDirectInputJoyConfig *This,
        HWND a,
        DWORD b);

    HRESULT (STDMETHODCALLTYPE *SendNotify)(
        IDirectInputJoyConfig *This);

    HRESULT (STDMETHODCALLTYPE *EnumTypes)(
        IDirectInputJoyConfig *This,
        LPVOID a,
        LPVOID b);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IDirectInputJoyConfig *This,
        LPCWSTR a,
        LPDIJOYTYPEINFO b,
        DWORD c);

    HRESULT (STDMETHODCALLTYPE *SetTypeInfo)(
        IDirectInputJoyConfig *This,
        LPCWSTR a,
        LPCDIJOYTYPEINFO b,
        DWORD c);

    HRESULT (STDMETHODCALLTYPE *DeleteType)(
        IDirectInputJoyConfig *This,
        LPCWSTR a);

    HRESULT (STDMETHODCALLTYPE *GetConfig)(
        IDirectInputJoyConfig *This,
        UINT a,
        LPDIJOYCONFIG b,
        DWORD c);

    HRESULT (STDMETHODCALLTYPE *SetConfig)(
        IDirectInputJoyConfig *This,
        UINT a,
        LPCDIJOYCONFIG b,
        DWORD c);

    HRESULT (STDMETHODCALLTYPE *DeleteConfig)(
        IDirectInputJoyConfig *This,
        UINT a);

    HRESULT (STDMETHODCALLTYPE *GetUserValues)(
        IDirectInputJoyConfig *This,
        LPDIJOYUSERVALUES a,
        DWORD b);

    HRESULT (STDMETHODCALLTYPE *SetUserValues)(
        IDirectInputJoyConfig *This,
        LPCDIJOYUSERVALUES a,
        DWORD b);

    HRESULT (STDMETHODCALLTYPE *AddNewHardware)(
        IDirectInputJoyConfig *This,
        HWND a,
        REFGUID b);

    HRESULT (STDMETHODCALLTYPE *OpenTypeKey)(
        IDirectInputJoyConfig *This,
        LPCWSTR a,
        DWORD b,
        HKEY *c);

    HRESULT (STDMETHODCALLTYPE *OpenConfigKey)(
        IDirectInputJoyConfig *This,
        UINT a,
        DWORD b,
        HKEY *c);

    END_INTERFACE
} IDirectInputJoyConfigVtbl;

interface IDirectInputJoyConfig {
    CONST_VTBL IDirectInputJoyConfigVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectInputJoyConfig_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectInputJoyConfig_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectInputJoyConfig_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectInputJoyConfig methods ***/
#define IDirectInputJoyConfig_Acquire(This) (This)->lpVtbl->Acquire(This)
#define IDirectInputJoyConfig_Unacquire(This) (This)->lpVtbl->Unacquire(This)
#define IDirectInputJoyConfig_SetCooperativeLevel(This,a,b) (This)->lpVtbl->SetCooperativeLevel(This,a,b)
#define IDirectInputJoyConfig_SendNotify(This) (This)->lpVtbl->SendNotify(This)
#define IDirectInputJoyConfig_EnumTypes(This,a,b) (This)->lpVtbl->EnumTypes(This,a,b)
#define IDirectInputJoyConfig_GetTypeInfo(This,a,b,c) (This)->lpVtbl->GetTypeInfo(This,a,b,c)
#define IDirectInputJoyConfig_SetTypeInfo(This,a,b,c) (This)->lpVtbl->SetTypeInfo(This,a,b,c)
#define IDirectInputJoyConfig_DeleteType(This,a) (This)->lpVtbl->DeleteType(This,a)
#define IDirectInputJoyConfig_GetConfig(This,a,b,c) (This)->lpVtbl->GetConfig(This,a,b,c)
#define IDirectInputJoyConfig_SetConfig(This,a,b,c) (This)->lpVtbl->SetConfig(This,a,b,c)
#define IDirectInputJoyConfig_DeleteConfig(This,a) (This)->lpVtbl->DeleteConfig(This,a)
#define IDirectInputJoyConfig_GetUserValues(This,a,b) (This)->lpVtbl->GetUserValues(This,a,b)
#define IDirectInputJoyConfig_SetUserValues(This,a,b) (This)->lpVtbl->SetUserValues(This,a,b)
#define IDirectInputJoyConfig_AddNewHardware(This,a,b) (This)->lpVtbl->AddNewHardware(This,a,b)
#define IDirectInputJoyConfig_OpenTypeKey(This,a,b,c) (This)->lpVtbl->OpenTypeKey(This,a,b,c)
#define IDirectInputJoyConfig_OpenConfigKey(This,a,b,c) (This)->lpVtbl->OpenConfigKey(This,a,b,c)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectInputJoyConfig_QueryInterface(IDirectInputJoyConfig* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectInputJoyConfig_AddRef(IDirectInputJoyConfig* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectInputJoyConfig_Release(IDirectInputJoyConfig* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectInputJoyConfig methods ***/
static inline HRESULT IDirectInputJoyConfig_Acquire(IDirectInputJoyConfig* This) {
    return This->lpVtbl->Acquire(This);
}
static inline HRESULT IDirectInputJoyConfig_Unacquire(IDirectInputJoyConfig* This) {
    return This->lpVtbl->Unacquire(This);
}
static inline HRESULT IDirectInputJoyConfig_SetCooperativeLevel(IDirectInputJoyConfig* This,HWND a,DWORD b) {
    return This->lpVtbl->SetCooperativeLevel(This,a,b);
}
static inline HRESULT IDirectInputJoyConfig_SendNotify(IDirectInputJoyConfig* This) {
    return This->lpVtbl->SendNotify(This);
}
static inline HRESULT IDirectInputJoyConfig_EnumTypes(IDirectInputJoyConfig* This,LPVOID a,LPVOID b) {
    return This->lpVtbl->EnumTypes(This,a,b);
}
static inline HRESULT IDirectInputJoyConfig_GetTypeInfo(IDirectInputJoyConfig* This,LPCWSTR a,LPDIJOYTYPEINFO b,DWORD c) {
    return This->lpVtbl->GetTypeInfo(This,a,b,c);
}
static inline HRESULT IDirectInputJoyConfig_SetTypeInfo(IDirectInputJoyConfig* This,LPCWSTR a,LPCDIJOYTYPEINFO b,DWORD c) {
    return This->lpVtbl->SetTypeInfo(This,a,b,c);
}
static inline HRESULT IDirectInputJoyConfig_DeleteType(IDirectInputJoyConfig* This,LPCWSTR a) {
    return This->lpVtbl->DeleteType(This,a);
}
static inline HRESULT IDirectInputJoyConfig_GetConfig(IDirectInputJoyConfig* This,UINT a,LPDIJOYCONFIG b,DWORD c) {
    return This->lpVtbl->GetConfig(This,a,b,c);
}
static inline HRESULT IDirectInputJoyConfig_SetConfig(IDirectInputJoyConfig* This,UINT a,LPCDIJOYCONFIG b,DWORD c) {
    return This->lpVtbl->SetConfig(This,a,b,c);
}
static inline HRESULT IDirectInputJoyConfig_DeleteConfig(IDirectInputJoyConfig* This,UINT a) {
    return This->lpVtbl->DeleteConfig(This,a);
}
static inline HRESULT IDirectInputJoyConfig_GetUserValues(IDirectInputJoyConfig* This,LPDIJOYUSERVALUES a,DWORD b) {
    return This->lpVtbl->GetUserValues(This,a,b);
}
static inline HRESULT IDirectInputJoyConfig_SetUserValues(IDirectInputJoyConfig* This,LPCDIJOYUSERVALUES a,DWORD b) {
    return This->lpVtbl->SetUserValues(This,a,b);
}
static inline HRESULT IDirectInputJoyConfig_AddNewHardware(IDirectInputJoyConfig* This,HWND a,REFGUID b) {
    return This->lpVtbl->AddNewHardware(This,a,b);
}
static inline HRESULT IDirectInputJoyConfig_OpenTypeKey(IDirectInputJoyConfig* This,LPCWSTR a,DWORD b,HKEY *c) {
    return This->lpVtbl->OpenTypeKey(This,a,b,c);
}
static inline HRESULT IDirectInputJoyConfig_OpenConfigKey(IDirectInputJoyConfig* This,UINT a,DWORD b,HKEY *c) {
    return This->lpVtbl->OpenConfigKey(This,a,b,c);
}
#endif
#endif

#endif


#endif  /* __IDirectInputJoyConfig_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IDirectInputJoyConfig8 interface
 */
#ifndef __IDirectInputJoyConfig8_INTERFACE_DEFINED__
#define __IDirectInputJoyConfig8_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDirectInputJoyConfig8, 0xeb0d7dfa, 0x1990, 0x4f27, 0xb4,0xd6, 0xed,0xf2,0xee,0xc4,0xa4,0x4c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eb0d7dfa-1990-4f27-b4d6-edf2eec4a44c")
IDirectInputJoyConfig8 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Acquire(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unacquire(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCooperativeLevel(
        HWND a,
        DWORD b) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendNotify(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumTypes(
        LPVOID a,
        LPVOID b) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypeInfo(
        LPCWSTR a,
        LPDIJOYTYPEINFO b,
        DWORD c) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTypeInfo(
        LPCWSTR a,
        LPCDIJOYTYPEINFO b,
        DWORD c,
        LPWSTR d) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteType(
        LPCWSTR a) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConfig(
        UINT a,
        LPDIJOYCONFIG b,
        DWORD c) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetConfig(
        UINT a,
        LPCDIJOYCONFIG b,
        DWORD c) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteConfig(
        UINT a) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserValues(
        LPDIJOYUSERVALUES a,
        DWORD b) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUserValues(
        LPCDIJOYUSERVALUES a,
        DWORD b) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddNewHardware(
        HWND a,
        REFGUID b) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenTypeKey(
        LPCWSTR a,
        DWORD b,
        HKEY *c) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenAppStatusKey(
        HKEY *a) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDirectInputJoyConfig8, 0xeb0d7dfa, 0x1990, 0x4f27, 0xb4,0xd6, 0xed,0xf2,0xee,0xc4,0xa4,0x4c)
#endif
#else
typedef struct IDirectInputJoyConfig8Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDirectInputJoyConfig8 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDirectInputJoyConfig8 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDirectInputJoyConfig8 *This);

    /*** IDirectInputJoyConfig8 methods ***/
    HRESULT (STDMETHODCALLTYPE *Acquire)(
        IDirectInputJoyConfig8 *This);

    HRESULT (STDMETHODCALLTYPE *Unacquire)(
        IDirectInputJoyConfig8 *This);

    HRESULT (STDMETHODCALLTYPE *SetCooperativeLevel)(
        IDirectInputJoyConfig8 *This,
        HWND a,
        DWORD b);

    HRESULT (STDMETHODCALLTYPE *SendNotify)(
        IDirectInputJoyConfig8 *This);

    HRESULT (STDMETHODCALLTYPE *EnumTypes)(
        IDirectInputJoyConfig8 *This,
        LPVOID a,
        LPVOID b);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IDirectInputJoyConfig8 *This,
        LPCWSTR a,
        LPDIJOYTYPEINFO b,
        DWORD c);

    HRESULT (STDMETHODCALLTYPE *SetTypeInfo)(
        IDirectInputJoyConfig8 *This,
        LPCWSTR a,
        LPCDIJOYTYPEINFO b,
        DWORD c,
        LPWSTR d);

    HRESULT (STDMETHODCALLTYPE *DeleteType)(
        IDirectInputJoyConfig8 *This,
        LPCWSTR a);

    HRESULT (STDMETHODCALLTYPE *GetConfig)(
        IDirectInputJoyConfig8 *This,
        UINT a,
        LPDIJOYCONFIG b,
        DWORD c);

    HRESULT (STDMETHODCALLTYPE *SetConfig)(
        IDirectInputJoyConfig8 *This,
        UINT a,
        LPCDIJOYCONFIG b,
        DWORD c);

    HRESULT (STDMETHODCALLTYPE *DeleteConfig)(
        IDirectInputJoyConfig8 *This,
        UINT a);

    HRESULT (STDMETHODCALLTYPE *GetUserValues)(
        IDirectInputJoyConfig8 *This,
        LPDIJOYUSERVALUES a,
        DWORD b);

    HRESULT (STDMETHODCALLTYPE *SetUserValues)(
        IDirectInputJoyConfig8 *This,
        LPCDIJOYUSERVALUES a,
        DWORD b);

    HRESULT (STDMETHODCALLTYPE *AddNewHardware)(
        IDirectInputJoyConfig8 *This,
        HWND a,
        REFGUID b);

    HRESULT (STDMETHODCALLTYPE *OpenTypeKey)(
        IDirectInputJoyConfig8 *This,
        LPCWSTR a,
        DWORD b,
        HKEY *c);

    HRESULT (STDMETHODCALLTYPE *OpenAppStatusKey)(
        IDirectInputJoyConfig8 *This,
        HKEY *a);

    END_INTERFACE
} IDirectInputJoyConfig8Vtbl;

interface IDirectInputJoyConfig8 {
    CONST_VTBL IDirectInputJoyConfig8Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDirectInputJoyConfig8_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDirectInputJoyConfig8_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDirectInputJoyConfig8_Release(This) (This)->lpVtbl->Release(This)
/*** IDirectInputJoyConfig8 methods ***/
#define IDirectInputJoyConfig8_Acquire(This) (This)->lpVtbl->Acquire(This)
#define IDirectInputJoyConfig8_Unacquire(This) (This)->lpVtbl->Unacquire(This)
#define IDirectInputJoyConfig8_SetCooperativeLevel(This,a,b) (This)->lpVtbl->SetCooperativeLevel(This,a,b)
#define IDirectInputJoyConfig8_SendNotify(This) (This)->lpVtbl->SendNotify(This)
#define IDirectInputJoyConfig8_EnumTypes(This,a,b) (This)->lpVtbl->EnumTypes(This,a,b)
#define IDirectInputJoyConfig8_GetTypeInfo(This,a,b,c) (This)->lpVtbl->GetTypeInfo(This,a,b,c)
#define IDirectInputJoyConfig8_SetTypeInfo(This,a,b,c,d) (This)->lpVtbl->SetTypeInfo(This,a,b,c,d)
#define IDirectInputJoyConfig8_DeleteType(This,a) (This)->lpVtbl->DeleteType(This,a)
#define IDirectInputJoyConfig8_GetConfig(This,a,b,c) (This)->lpVtbl->GetConfig(This,a,b,c)
#define IDirectInputJoyConfig8_SetConfig(This,a,b,c) (This)->lpVtbl->SetConfig(This,a,b,c)
#define IDirectInputJoyConfig8_DeleteConfig(This,a) (This)->lpVtbl->DeleteConfig(This,a)
#define IDirectInputJoyConfig8_GetUserValues(This,a,b) (This)->lpVtbl->GetUserValues(This,a,b)
#define IDirectInputJoyConfig8_SetUserValues(This,a,b) (This)->lpVtbl->SetUserValues(This,a,b)
#define IDirectInputJoyConfig8_AddNewHardware(This,a,b) (This)->lpVtbl->AddNewHardware(This,a,b)
#define IDirectInputJoyConfig8_OpenTypeKey(This,a,b,c) (This)->lpVtbl->OpenTypeKey(This,a,b,c)
#define IDirectInputJoyConfig8_OpenAppStatusKey(This,a) (This)->lpVtbl->OpenAppStatusKey(This,a)
#else
/*** IUnknown methods ***/
static inline HRESULT IDirectInputJoyConfig8_QueryInterface(IDirectInputJoyConfig8* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDirectInputJoyConfig8_AddRef(IDirectInputJoyConfig8* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDirectInputJoyConfig8_Release(IDirectInputJoyConfig8* This) {
    return This->lpVtbl->Release(This);
}
/*** IDirectInputJoyConfig8 methods ***/
static inline HRESULT IDirectInputJoyConfig8_Acquire(IDirectInputJoyConfig8* This) {
    return This->lpVtbl->Acquire(This);
}
static inline HRESULT IDirectInputJoyConfig8_Unacquire(IDirectInputJoyConfig8* This) {
    return This->lpVtbl->Unacquire(This);
}
static inline HRESULT IDirectInputJoyConfig8_SetCooperativeLevel(IDirectInputJoyConfig8* This,HWND a,DWORD b) {
    return This->lpVtbl->SetCooperativeLevel(This,a,b);
}
static inline HRESULT IDirectInputJoyConfig8_SendNotify(IDirectInputJoyConfig8* This) {
    return This->lpVtbl->SendNotify(This);
}
static inline HRESULT IDirectInputJoyConfig8_EnumTypes(IDirectInputJoyConfig8* This,LPVOID a,LPVOID b) {
    return This->lpVtbl->EnumTypes(This,a,b);
}
static inline HRESULT IDirectInputJoyConfig8_GetTypeInfo(IDirectInputJoyConfig8* This,LPCWSTR a,LPDIJOYTYPEINFO b,DWORD c) {
    return This->lpVtbl->GetTypeInfo(This,a,b,c);
}
static inline HRESULT IDirectInputJoyConfig8_SetTypeInfo(IDirectInputJoyConfig8* This,LPCWSTR a,LPCDIJOYTYPEINFO b,DWORD c,LPWSTR d) {
    return This->lpVtbl->SetTypeInfo(This,a,b,c,d);
}
static inline HRESULT IDirectInputJoyConfig8_DeleteType(IDirectInputJoyConfig8* This,LPCWSTR a) {
    return This->lpVtbl->DeleteType(This,a);
}
static inline HRESULT IDirectInputJoyConfig8_GetConfig(IDirectInputJoyConfig8* This,UINT a,LPDIJOYCONFIG b,DWORD c) {
    return This->lpVtbl->GetConfig(This,a,b,c);
}
static inline HRESULT IDirectInputJoyConfig8_SetConfig(IDirectInputJoyConfig8* This,UINT a,LPCDIJOYCONFIG b,DWORD c) {
    return This->lpVtbl->SetConfig(This,a,b,c);
}
static inline HRESULT IDirectInputJoyConfig8_DeleteConfig(IDirectInputJoyConfig8* This,UINT a) {
    return This->lpVtbl->DeleteConfig(This,a);
}
static inline HRESULT IDirectInputJoyConfig8_GetUserValues(IDirectInputJoyConfig8* This,LPDIJOYUSERVALUES a,DWORD b) {
    return This->lpVtbl->GetUserValues(This,a,b);
}
static inline HRESULT IDirectInputJoyConfig8_SetUserValues(IDirectInputJoyConfig8* This,LPCDIJOYUSERVALUES a,DWORD b) {
    return This->lpVtbl->SetUserValues(This,a,b);
}
static inline HRESULT IDirectInputJoyConfig8_AddNewHardware(IDirectInputJoyConfig8* This,HWND a,REFGUID b) {
    return This->lpVtbl->AddNewHardware(This,a,b);
}
static inline HRESULT IDirectInputJoyConfig8_OpenTypeKey(IDirectInputJoyConfig8* This,LPCWSTR a,DWORD b,HKEY *c) {
    return This->lpVtbl->OpenTypeKey(This,a,b,c);
}
static inline HRESULT IDirectInputJoyConfig8_OpenAppStatusKey(IDirectInputJoyConfig8* This,HKEY *a) {
    return This->lpVtbl->OpenAppStatusKey(This,a);
}
#endif
#endif

#endif


#endif  /* __IDirectInputJoyConfig8_INTERFACE_DEFINED__ */

#endif /* DIJ_RINGZERO */
#define DIRECTINPUT_NOTIFICATION_MSGSTRINGA "DIRECTINPUT_NOTIFICATION_MSGSTRING"
#define DIRECTINPUT_NOTIFICATION_MSGSTRINGW L"DIRECTINPUT_NOTIFICATION_MSGSTRING"
#define DIRECTINPUT_NOTIFICATION_MSGSTRING __MINGW_NAME_AW(DIRECTINPUT_NOTIFICATION_MSGSTRING)
#define DIMSGWP_NEWAPPSTART 0x00000001
#define DIMSGWP_DX8APPSTART 0x00000002
#define DIMSGWP_DX8MAPPERAPPSTART 0x00000003
#define DIAPPIDFLAG_NOTIME 0x00000001
#define DIAPPIDFLAG_NOSIZE 0x00000002
#define DIRECTINPUT_REGSTR_VAL_APPIDFLAGA "AppIdFlag"
#define DIRECTINPUT_REGSTR_KEY_LASTAPPA "MostRecentApplication"
#define DIRECTINPUT_REGSTR_KEY_LASTMAPAPPA "MostRecentMapperApplication"
#define DIRECTINPUT_REGSTR_VAL_VERSIONA "Version"
#define DIRECTINPUT_REGSTR_VAL_NAMEA "Name"
#define DIRECTINPUT_REGSTR_VAL_IDA "Id"
#define DIRECTINPUT_REGSTR_VAL_MAPPERA "UsesMapper"
#define DIRECTINPUT_REGSTR_VAL_LASTSTARTA "MostRecentStart"
#define DIRECTINPUT_REGSTR_VAL_APPIDFLAGW L"AppIdFlag"
#define DIRECTINPUT_REGSTR_KEY_LASTAPPW L"MostRecentApplication"
#define DIRECTINPUT_REGSTR_KEY_LASTMAPAPPW L"MostRecentMapperApplication"
#define DIRECTINPUT_REGSTR_VAL_VERSIONW L"Version"
#define DIRECTINPUT_REGSTR_VAL_NAMEW L"Name"
#define DIRECTINPUT_REGSTR_VAL_IDW L"Id"
#define DIRECTINPUT_REGSTR_VAL_MAPPERW L"UsesMapper"
#define DIRECTINPUT_REGSTR_VAL_LASTSTARTW L"MostRecentStart"
#define DIRECTINPUT_REGSTR_VAL_APPIDFLAG __MINGW_NAME_AW(DIRECTINPUT_REGSTR_VAL_APPIDFLAG)
#define DIRECTINPUT_REGSTR_KEY_LASTAPP __MINGW_NAME_AW(DIRECTINPUT_REGSTR_KEY_LASTAPP)
#define DIRECTINPUT_REGSTR_KEY_LASTMAPAPP __MINGW_NAME_AW(DIRECTINPUT_REGSTR_KEY_LASTMAPAPP)
#define DIRECTINPUT_REGSTR_VAL_VERSION __MINGW_NAME_AW(DIRECTINPUT_REGSTR_VAL_VERSION)
#define DIRECTINPUT_REGSTR_VAL_NAME __MINGW_NAME_AW(DIRECTINPUT_REGSTR_VAL_NAME)
#define DIRECTINPUT_REGSTR_VAL_ID __MINGW_NAME_AW(DIRECTINPUT_REGSTR_VAL_ID)
#define DIRECTINPUT_REGSTR_VAL_MAPPER __MINGW_NAME_AW(DIRECTINPUT_REGSTR_VAL_MAPPER)
#define DIRECTINPUT_REGSTR_VAL_LASTSTART __MINGW_NAME_AW(DIRECTINPUT_REGSTR_VAL_LASTSTART)
#define DIERR_NOMOREITEMS MAKE_HRESULT(SEVERITY_ERROR, FACILITY_WIN32, ERROR_NO_MORE_ITEMS)
#define DIERR_DRIVERFIRST __MSABI_LONG(0x80040300)
#define DIERR_DRIVERLAST __MSABI_LONG(0x800403FF)
#define DIERR_INVALIDCLASSINSTALLER __MSABI_LONG(0x80040400)
#define DIERR_CANCELLED __MSABI_LONG(0x80040401)
#define DIERR_BADINF __MSABI_LONG(0x80040402)
#define DIDIFT_DELETE __MSABI_LONG(0x01000000)
#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dinputd_h__ */
