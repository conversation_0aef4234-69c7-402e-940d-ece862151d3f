#ifndef VERSION_H
#define VERSION_H

/* Generated automatically by genversion.  */

#define GCC_major_version 15

/* The complete version string, assembled from several pieces.
BASEVER, DATESTAMP, DEVPHASE, and REVISION are defined by the
Makefile.  */

#define version_string "15.1.0"
#define pkgversion_string "(Rev8, Built by MSYS2 project) "

/* This is the location of the online document giving instructions for
reporting bugs.  If you distribute a modified version of GCC,
please configure with --with-bugurl pointing to a document giving
instructions for reporting bugs to you, not us.  (You are of course
welcome to forward us bugs reported to you, if you determine that
they are not bugs in your modifications.)  */

#define bug_report_url "<https://github.com/msys2/MINGW-packages/issues>"

#define GCOV_VERSION ((gcov_unsigned_t)0x4235312a)  /* B51* */

#endif /* VERSION_H */
