/*** Autogenerated by WIDL 10.12 from include/windows.media.speechsynthesis.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_media_speechsynthesis_h__
#define __windows_media_speechsynthesis_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesisStream
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesisStream;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesizer
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesizer;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesizer2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesizer2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation ABI::Windows::Media::SpeechSynthesis::IVoiceInformation
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface IVoiceInformation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesizerOptions
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesizerOptions;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesizerOptions2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesizerOptions2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesizerOptions3
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesizerOptions3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic ABI::Windows::Media::SpeechSynthesis::IInstalledVoicesStatic
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface IInstalledVoicesStatic;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceInformation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                class VoiceInformation;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceInformation __x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceInformation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceInformation_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesizerOptions_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesizerOptions_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                class SpeechSynthesizerOptions;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesizerOptions __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesizerOptions;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesizerOptions_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesisStream_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesisStream_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                class SpeechSynthesisStream;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesisStream __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesisStream;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesisStream_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesizer_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesizer_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                class SpeechSynthesizer;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesizer __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesizer;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechSynthesizer_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <windows.foundation.h>
#include <windows.media.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechAppendedSilence_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechAppendedSilence_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                enum SpeechAppendedSilence {
                    SpeechAppendedSilence_Default = 0,
                    SpeechAppendedSilence_Min = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechAppendedSilence {
    SpeechAppendedSilence_Default = 0,
    SpeechAppendedSilence_Min = 1
};
#ifdef WIDL_using_Windows_Media_SpeechSynthesis
#define SpeechAppendedSilence __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechAppendedSilence
#endif /* WIDL_using_Windows_Media_SpeechSynthesis */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechAppendedSilence_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechAppendedSilence __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechAppendedSilence;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechPunctuationSilence_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechPunctuationSilence_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                enum SpeechPunctuationSilence {
                    SpeechPunctuationSilence_Default = 0,
                    SpeechPunctuationSilence_Min = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechPunctuationSilence {
    SpeechPunctuationSilence_Default = 0,
    SpeechPunctuationSilence_Min = 1
};
#ifdef WIDL_using_Windows_Media_SpeechSynthesis
#define SpeechPunctuationSilence __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechPunctuationSilence
#endif /* WIDL_using_Windows_Media_SpeechSynthesis */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechPunctuationSilence_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechPunctuationSilence __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechPunctuationSilence;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceGender_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceGender_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                enum VoiceGender {
                    VoiceGender_Male = 0,
                    VoiceGender_Female = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceGender {
    VoiceGender_Male = 0,
    VoiceGender_Female = 1
};
#ifdef WIDL_using_Windows_Media_SpeechSynthesis
#define VoiceGender __x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceGender
#endif /* WIDL_using_Windows_Media_SpeechSynthesis */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceGender_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceGender __x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceGender;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic ABI::Windows::Media::SpeechSynthesis::IInstalledVoicesStatic
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface IInstalledVoicesStatic;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic2 __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic2 ABI::Windows::Media::SpeechSynthesis::IInstalledVoicesStatic2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface IInstalledVoicesStatic2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesizer
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesizer;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesizer2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesizer2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesizerOptions
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesizerOptions;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesizerOptions2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesizerOptions2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesizerOptions3
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesizerOptions3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation ABI::Windows::Media::SpeechSynthesis::IVoiceInformation
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface IVoiceInformation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesisStream
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                interface ISpeechSynthesisStream;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ISpeechSynthesisStream interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream, 0x83e46e93, 0x244c, 0x4622, 0xba,0x0b, 0x62,0x29,0xc4,0xd0,0xd6,0x5d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                MIDL_INTERFACE("83e46e93-244c-4622-ba0b-6229c4d0d65d")
                ISpeechSynthesisStream : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Markers(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::IMediaMarker* > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream, 0x83e46e93, 0x244c, 0x4622, 0xba,0x0b, 0x62,0x29,0xc4,0xd0,0xd6,0x5d)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream *This,
        TrustLevel *trustLevel);

    /*** ISpeechSynthesisStream methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Markers)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream *This,
        __FIVectorView_1_Windows__CMedia__CIMediaMarker **value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStreamVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechSynthesisStream methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_get_Markers(This,value) (This)->lpVtbl->get_Markers(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_AddRef(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_Release(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_GetIids(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechSynthesisStream methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_get_Markers(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream* This,__FIVectorView_1_Windows__CMedia__CIMediaMarker **value) {
    return This->lpVtbl->get_Markers(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechSynthesis
#define IID_ISpeechSynthesisStream IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream
#define ISpeechSynthesisStreamVtbl __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStreamVtbl
#define ISpeechSynthesisStream __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream
#define ISpeechSynthesisStream_QueryInterface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_QueryInterface
#define ISpeechSynthesisStream_AddRef __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_AddRef
#define ISpeechSynthesisStream_Release __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_Release
#define ISpeechSynthesisStream_GetIids __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_GetIids
#define ISpeechSynthesisStream_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_GetRuntimeClassName
#define ISpeechSynthesisStream_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_GetTrustLevel
#define ISpeechSynthesisStream_get_Markers __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_get_Markers
#endif /* WIDL_using_Windows_Media_SpeechSynthesis */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechSynthesizer interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer, 0xce9f7c76, 0x97f4, 0x4ced, 0xad,0x68, 0xd5,0x1c,0x45,0x8e,0x45,0xc6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                MIDL_INTERFACE("ce9f7c76-97f4-4ced-ad68-d51c458e45c6")
                ISpeechSynthesizer : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE SynthesizeTextToStreamAsync(
                        HSTRING text,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SynthesizeSsmlToStreamAsync(
                        HSTRING Ssml,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_Voice(
                        ABI::Windows::Media::SpeechSynthesis::IVoiceInformation *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Voice(
                        ABI::Windows::Media::SpeechSynthesis::IVoiceInformation **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer, 0xce9f7c76, 0x97f4, 0x4ced, 0xad,0x68, 0xd5,0x1c,0x45,0x8e,0x45,0xc6)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer *This,
        TrustLevel *trustLevel);

    /*** ISpeechSynthesizer methods ***/
    HRESULT (STDMETHODCALLTYPE *SynthesizeTextToStreamAsync)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer *This,
        HSTRING text,
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream **operation);

    HRESULT (STDMETHODCALLTYPE *SynthesizeSsmlToStreamAsync)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer *This,
        HSTRING Ssml,
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream **operation);

    HRESULT (STDMETHODCALLTYPE *put_Voice)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *value);

    HRESULT (STDMETHODCALLTYPE *get_Voice)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechSynthesizer methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_SynthesizeTextToStreamAsync(This,text,operation) (This)->lpVtbl->SynthesizeTextToStreamAsync(This,text,operation)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_SynthesizeSsmlToStreamAsync(This,Ssml,operation) (This)->lpVtbl->SynthesizeSsmlToStreamAsync(This,Ssml,operation)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_put_Voice(This,value) (This)->lpVtbl->put_Voice(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_get_Voice(This,value) (This)->lpVtbl->get_Voice(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_AddRef(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_Release(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_GetIids(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechSynthesizer methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_SynthesizeTextToStreamAsync(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer* This,HSTRING text,__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream **operation) {
    return This->lpVtbl->SynthesizeTextToStreamAsync(This,text,operation);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_SynthesizeSsmlToStreamAsync(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer* This,HSTRING Ssml,__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream **operation) {
    return This->lpVtbl->SynthesizeSsmlToStreamAsync(This,Ssml,operation);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_put_Voice(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *value) {
    return This->lpVtbl->put_Voice(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_get_Voice(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **value) {
    return This->lpVtbl->get_Voice(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechSynthesis
#define IID_ISpeechSynthesizer IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer
#define ISpeechSynthesizerVtbl __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerVtbl
#define ISpeechSynthesizer __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer
#define ISpeechSynthesizer_QueryInterface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_QueryInterface
#define ISpeechSynthesizer_AddRef __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_AddRef
#define ISpeechSynthesizer_Release __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_Release
#define ISpeechSynthesizer_GetIids __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_GetIids
#define ISpeechSynthesizer_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_GetRuntimeClassName
#define ISpeechSynthesizer_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_GetTrustLevel
#define ISpeechSynthesizer_SynthesizeTextToStreamAsync __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_SynthesizeTextToStreamAsync
#define ISpeechSynthesizer_SynthesizeSsmlToStreamAsync __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_SynthesizeSsmlToStreamAsync
#define ISpeechSynthesizer_put_Voice __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_put_Voice
#define ISpeechSynthesizer_get_Voice __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_get_Voice
#endif /* WIDL_using_Windows_Media_SpeechSynthesis */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechSynthesizer2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2, 0xa7c5ecb2, 0x4339, 0x4d6a, 0xbb,0xf8, 0xc7,0xa4,0xf1,0x54,0x4c,0x2e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                MIDL_INTERFACE("a7c5ecb2-4339-4d6a-bbf8-c7a4f1544c2e")
                ISpeechSynthesizer2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Options(
                        ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesizerOptions **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2, 0xa7c5ecb2, 0x4339, 0x4d6a, 0xbb,0xf8, 0xc7,0xa4,0xf1,0x54,0x4c,0x2e)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 *This,
        TrustLevel *trustLevel);

    /*** ISpeechSynthesizer2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Options)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions **value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2Vtbl;

interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2 {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechSynthesizer2 methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_get_Options(This,value) (This)->lpVtbl->get_Options(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_AddRef(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_Release(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_GetIids(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechSynthesizer2 methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_get_Options(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions **value) {
    return This->lpVtbl->get_Options(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechSynthesis
#define IID_ISpeechSynthesizer2 IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2
#define ISpeechSynthesizer2Vtbl __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2Vtbl
#define ISpeechSynthesizer2 __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2
#define ISpeechSynthesizer2_QueryInterface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_QueryInterface
#define ISpeechSynthesizer2_AddRef __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_AddRef
#define ISpeechSynthesizer2_Release __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_Release
#define ISpeechSynthesizer2_GetIids __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_GetIids
#define ISpeechSynthesizer2_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_GetRuntimeClassName
#define ISpeechSynthesizer2_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_GetTrustLevel
#define ISpeechSynthesizer2_get_Options __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_get_Options
#endif /* WIDL_using_Windows_Media_SpeechSynthesis */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizer2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IVoiceInformation interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation, 0xb127d6a4, 0x1291, 0x4604, 0xaa,0x9c, 0x83,0x13,0x40,0x83,0x35,0x2c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                MIDL_INTERFACE("b127d6a4-1291-4604-aa9c-83134083352c")
                IVoiceInformation : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Id(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Language(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Description(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Gender(
                        ABI::Windows::Media::SpeechSynthesis::VoiceGender *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation, 0xb127d6a4, 0x1291, 0x4604, 0xaa,0x9c, 0x83,0x13,0x40,0x83,0x35,0x2c)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *This,
        TrustLevel *trustLevel);

    /*** IVoiceInformation methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Language)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Description)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Gender)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceGender *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformationVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVoiceInformation methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_DisplayName(This,value) (This)->lpVtbl->get_DisplayName(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Language(This,value) (This)->lpVtbl->get_Language(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Description(This,value) (This)->lpVtbl->get_Description(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Gender(This,value) (This)->lpVtbl->get_Gender(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_AddRef(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_Release(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_GetIids(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVoiceInformation methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_DisplayName(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation* This,HSTRING *value) {
    return This->lpVtbl->get_DisplayName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Id(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation* This,HSTRING *value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Language(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation* This,HSTRING *value) {
    return This->lpVtbl->get_Language(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Description(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation* This,HSTRING *value) {
    return This->lpVtbl->get_Description(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Gender(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CVoiceGender *value) {
    return This->lpVtbl->get_Gender(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechSynthesis
#define IID_IVoiceInformation IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation
#define IVoiceInformationVtbl __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformationVtbl
#define IVoiceInformation __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation
#define IVoiceInformation_QueryInterface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_QueryInterface
#define IVoiceInformation_AddRef __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_AddRef
#define IVoiceInformation_Release __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_Release
#define IVoiceInformation_GetIids __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_GetIids
#define IVoiceInformation_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_GetRuntimeClassName
#define IVoiceInformation_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_GetTrustLevel
#define IVoiceInformation_get_DisplayName __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_DisplayName
#define IVoiceInformation_get_Id __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Id
#define IVoiceInformation_get_Language __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Language
#define IVoiceInformation_get_Description __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Description
#define IVoiceInformation_get_Gender __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_get_Gender
#endif /* WIDL_using_Windows_Media_SpeechSynthesis */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechSynthesizerOptions interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions, 0xa0e23871, 0xcc3d, 0x43c9, 0x91,0xb1, 0xee,0x18,0x53,0x24,0xd8,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                MIDL_INTERFACE("a0e23871-cc3d-43c9-91b1-ee185324d83d")
                ISpeechSynthesizerOptions : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_IncludeWordBoundaryMetadata(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_IncludeWordBoundaryMetadata(
                        boolean value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IncludeSentenceBoundaryMetadata(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_IncludeSentenceBoundaryMetadata(
                        boolean value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions, 0xa0e23871, 0xcc3d, 0x43c9, 0x91,0xb1, 0xee,0x18,0x53,0x24,0xd8,0x3d)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions *This,
        TrustLevel *trustLevel);

    /*** ISpeechSynthesizerOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IncludeWordBoundaryMetadata)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IncludeWordBoundaryMetadata)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_IncludeSentenceBoundaryMetadata)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IncludeSentenceBoundaryMetadata)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions *This,
        boolean value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptionsVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechSynthesizerOptions methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_get_IncludeWordBoundaryMetadata(This,value) (This)->lpVtbl->get_IncludeWordBoundaryMetadata(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_put_IncludeWordBoundaryMetadata(This,value) (This)->lpVtbl->put_IncludeWordBoundaryMetadata(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_get_IncludeSentenceBoundaryMetadata(This,value) (This)->lpVtbl->get_IncludeSentenceBoundaryMetadata(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_put_IncludeSentenceBoundaryMetadata(This,value) (This)->lpVtbl->put_IncludeSentenceBoundaryMetadata(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_AddRef(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_Release(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_GetIids(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechSynthesizerOptions methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_get_IncludeWordBoundaryMetadata(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions* This,boolean *value) {
    return This->lpVtbl->get_IncludeWordBoundaryMetadata(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_put_IncludeWordBoundaryMetadata(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions* This,boolean value) {
    return This->lpVtbl->put_IncludeWordBoundaryMetadata(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_get_IncludeSentenceBoundaryMetadata(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions* This,boolean *value) {
    return This->lpVtbl->get_IncludeSentenceBoundaryMetadata(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_put_IncludeSentenceBoundaryMetadata(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions* This,boolean value) {
    return This->lpVtbl->put_IncludeSentenceBoundaryMetadata(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechSynthesis
#define IID_ISpeechSynthesizerOptions IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions
#define ISpeechSynthesizerOptionsVtbl __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptionsVtbl
#define ISpeechSynthesizerOptions __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions
#define ISpeechSynthesizerOptions_QueryInterface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_QueryInterface
#define ISpeechSynthesizerOptions_AddRef __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_AddRef
#define ISpeechSynthesizerOptions_Release __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_Release
#define ISpeechSynthesizerOptions_GetIids __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_GetIids
#define ISpeechSynthesizerOptions_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_GetRuntimeClassName
#define ISpeechSynthesizerOptions_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_GetTrustLevel
#define ISpeechSynthesizerOptions_get_IncludeWordBoundaryMetadata __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_get_IncludeWordBoundaryMetadata
#define ISpeechSynthesizerOptions_put_IncludeWordBoundaryMetadata __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_put_IncludeWordBoundaryMetadata
#define ISpeechSynthesizerOptions_get_IncludeSentenceBoundaryMetadata __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_get_IncludeSentenceBoundaryMetadata
#define ISpeechSynthesizerOptions_put_IncludeSentenceBoundaryMetadata __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_put_IncludeSentenceBoundaryMetadata
#endif /* WIDL_using_Windows_Media_SpeechSynthesis */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * ISpeechSynthesizerOptions2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2, 0x1cbef60e, 0x119c, 0x4bed, 0xb1,0x18, 0xd2,0x50,0xc3,0xa2,0x57,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                MIDL_INTERFACE("1cbef60e-119c-4bed-b118-d250c3a25793")
                ISpeechSynthesizerOptions2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AudioVolume(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_AudioVolume(
                        DOUBLE value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SpeakingRate(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_SpeakingRate(
                        DOUBLE value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_AudioPitch(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_AudioPitch(
                        DOUBLE value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2, 0x1cbef60e, 0x119c, 0x4bed, 0xb1,0x18, 0xd2,0x50,0xc3,0xa2,0x57,0x93)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This,
        TrustLevel *trustLevel);

    /*** ISpeechSynthesizerOptions2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AudioVolume)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *put_AudioVolume)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This,
        DOUBLE value);

    HRESULT (STDMETHODCALLTYPE *get_SpeakingRate)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *put_SpeakingRate)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This,
        DOUBLE value);

    HRESULT (STDMETHODCALLTYPE *get_AudioPitch)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *put_AudioPitch)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 *This,
        DOUBLE value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2Vtbl;

interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2 {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechSynthesizerOptions2 methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_get_AudioVolume(This,value) (This)->lpVtbl->get_AudioVolume(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_put_AudioVolume(This,value) (This)->lpVtbl->put_AudioVolume(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_get_SpeakingRate(This,value) (This)->lpVtbl->get_SpeakingRate(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_put_SpeakingRate(This,value) (This)->lpVtbl->put_SpeakingRate(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_get_AudioPitch(This,value) (This)->lpVtbl->get_AudioPitch(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_put_AudioPitch(This,value) (This)->lpVtbl->put_AudioPitch(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_AddRef(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_Release(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_GetIids(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechSynthesizerOptions2 methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_get_AudioVolume(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This,DOUBLE *value) {
    return This->lpVtbl->get_AudioVolume(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_put_AudioVolume(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This,DOUBLE value) {
    return This->lpVtbl->put_AudioVolume(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_get_SpeakingRate(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This,DOUBLE *value) {
    return This->lpVtbl->get_SpeakingRate(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_put_SpeakingRate(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This,DOUBLE value) {
    return This->lpVtbl->put_SpeakingRate(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_get_AudioPitch(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This,DOUBLE *value) {
    return This->lpVtbl->get_AudioPitch(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_put_AudioPitch(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2* This,DOUBLE value) {
    return This->lpVtbl->put_AudioPitch(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechSynthesis
#define IID_ISpeechSynthesizerOptions2 IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2
#define ISpeechSynthesizerOptions2Vtbl __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2Vtbl
#define ISpeechSynthesizerOptions2 __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2
#define ISpeechSynthesizerOptions2_QueryInterface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_QueryInterface
#define ISpeechSynthesizerOptions2_AddRef __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_AddRef
#define ISpeechSynthesizerOptions2_Release __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_Release
#define ISpeechSynthesizerOptions2_GetIids __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_GetIids
#define ISpeechSynthesizerOptions2_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_GetRuntimeClassName
#define ISpeechSynthesizerOptions2_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_GetTrustLevel
#define ISpeechSynthesizerOptions2_get_AudioVolume __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_get_AudioVolume
#define ISpeechSynthesizerOptions2_put_AudioVolume __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_put_AudioVolume
#define ISpeechSynthesizerOptions2_get_SpeakingRate __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_get_SpeakingRate
#define ISpeechSynthesizerOptions2_put_SpeakingRate __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_put_SpeakingRate
#define ISpeechSynthesizerOptions2_get_AudioPitch __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_get_AudioPitch
#define ISpeechSynthesizerOptions2_put_AudioPitch __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_put_AudioPitch
#endif /* WIDL_using_Windows_Media_SpeechSynthesis */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * ISpeechSynthesizerOptions3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3, 0x401ed877, 0x902c, 0x4814, 0xa5,0x82, 0xa5,0xd0,0xc0,0x76,0x9f,0xa8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                MIDL_INTERFACE("401ed877-902c-4814-a582-a5d0c0769fa8")
                ISpeechSynthesizerOptions3 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AppendedSilence(
                        ABI::Windows::Media::SpeechSynthesis::SpeechAppendedSilence *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_AppendedSilence(
                        ABI::Windows::Media::SpeechSynthesis::SpeechAppendedSilence value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PunctuationSilence(
                        ABI::Windows::Media::SpeechSynthesis::SpeechPunctuationSilence *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_PunctuationSilence(
                        ABI::Windows::Media::SpeechSynthesis::SpeechPunctuationSilence value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3, 0x401ed877, 0x902c, 0x4814, 0xa5,0x82, 0xa5,0xd0,0xc0,0x76,0x9f,0xa8)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 *This,
        TrustLevel *trustLevel);

    /*** ISpeechSynthesizerOptions3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AppendedSilence)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechAppendedSilence *value);

    HRESULT (STDMETHODCALLTYPE *put_AppendedSilence)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechAppendedSilence value);

    HRESULT (STDMETHODCALLTYPE *get_PunctuationSilence)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechPunctuationSilence *value);

    HRESULT (STDMETHODCALLTYPE *put_PunctuationSilence)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechPunctuationSilence value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3Vtbl;

interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3 {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechSynthesizerOptions3 methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_get_AppendedSilence(This,value) (This)->lpVtbl->get_AppendedSilence(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_put_AppendedSilence(This,value) (This)->lpVtbl->put_AppendedSilence(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_get_PunctuationSilence(This,value) (This)->lpVtbl->get_PunctuationSilence(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_put_PunctuationSilence(This,value) (This)->lpVtbl->put_PunctuationSilence(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_AddRef(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_Release(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_GetIids(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechSynthesizerOptions3 methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_get_AppendedSilence(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechAppendedSilence *value) {
    return This->lpVtbl->get_AppendedSilence(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_put_AppendedSilence(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechAppendedSilence value) {
    return This->lpVtbl->put_AppendedSilence(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_get_PunctuationSilence(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechPunctuationSilence *value) {
    return This->lpVtbl->get_PunctuationSilence(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_put_PunctuationSilence(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CSpeechPunctuationSilence value) {
    return This->lpVtbl->put_PunctuationSilence(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechSynthesis
#define IID_ISpeechSynthesizerOptions3 IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3
#define ISpeechSynthesizerOptions3Vtbl __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3Vtbl
#define ISpeechSynthesizerOptions3 __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3
#define ISpeechSynthesizerOptions3_QueryInterface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_QueryInterface
#define ISpeechSynthesizerOptions3_AddRef __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_AddRef
#define ISpeechSynthesizerOptions3_Release __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_Release
#define ISpeechSynthesizerOptions3_GetIids __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_GetIids
#define ISpeechSynthesizerOptions3_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_GetRuntimeClassName
#define ISpeechSynthesizerOptions3_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_GetTrustLevel
#define ISpeechSynthesizerOptions3_get_AppendedSilence __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_get_AppendedSilence
#define ISpeechSynthesizerOptions3_put_AppendedSilence __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_put_AppendedSilence
#define ISpeechSynthesizerOptions3_get_PunctuationSilence __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_get_PunctuationSilence
#define ISpeechSynthesizerOptions3_put_PunctuationSilence __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_put_PunctuationSilence
#endif /* WIDL_using_Windows_Media_SpeechSynthesis */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesizerOptions3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * IInstalledVoicesStatic interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic, 0x7d526ecc, 0x7533, 0x4c3f, 0x85,0xbe, 0x88,0x8c,0x2b,0xae,0xeb,0xdc);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechSynthesis {
                MIDL_INTERFACE("7d526ecc-7533-4c3f-85be-888c2baeebdc")
                IInstalledVoicesStatic : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AllVoices(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_DefaultVoice(
                        ABI::Windows::Media::SpeechSynthesis::IVoiceInformation **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic, 0x7d526ecc, 0x7533, 0x4c3f, 0x85,0xbe, 0x88,0x8c,0x2b,0xae,0xeb,0xdc)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStaticVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic *This,
        TrustLevel *trustLevel);

    /*** IInstalledVoicesStatic methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AllVoices)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic *This,
        __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation **value);

    HRESULT (STDMETHODCALLTYPE *get_DefaultVoice)(
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStaticVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStaticVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IInstalledVoicesStatic methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_get_AllVoices(This,value) (This)->lpVtbl->get_AllVoices(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_get_DefaultVoice(This,value) (This)->lpVtbl->get_DefaultVoice(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_AddRef(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_Release(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_GetIids(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IInstalledVoicesStatic methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_get_AllVoices(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic* This,__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation **value) {
    return This->lpVtbl->get_AllVoices(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_get_DefaultVoice(__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **value) {
    return This->lpVtbl->get_DefaultVoice(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechSynthesis
#define IID_IInstalledVoicesStatic IID___x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic
#define IInstalledVoicesStaticVtbl __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStaticVtbl
#define IInstalledVoicesStatic __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic
#define IInstalledVoicesStatic_QueryInterface __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_QueryInterface
#define IInstalledVoicesStatic_AddRef __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_AddRef
#define IInstalledVoicesStatic_Release __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_Release
#define IInstalledVoicesStatic_GetIids __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_GetIids
#define IInstalledVoicesStatic_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_GetRuntimeClassName
#define IInstalledVoicesStatic_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_GetTrustLevel
#define IInstalledVoicesStatic_get_AllVoices __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_get_AllVoices
#define IInstalledVoicesStatic_get_DefaultVoice __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_get_DefaultVoice
#endif /* WIDL_using_Windows_Media_SpeechSynthesis */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechSynthesis_CIInstalledVoicesStatic_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechSynthesis.VoiceInformation
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechSynthesis_VoiceInformation_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechSynthesis_VoiceInformation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_VoiceInformation[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','s','.','V','o','i','c','e','I','n','f','o','r','m','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_VoiceInformation[] = L"Windows.Media.SpeechSynthesis.VoiceInformation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_VoiceInformation[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','s','.','V','o','i','c','e','I','n','f','o','r','m','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechSynthesis_VoiceInformation_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechSynthesis.SpeechSynthesizerOptions
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Media_SpeechSynthesis_SpeechSynthesizerOptions_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechSynthesis_SpeechSynthesizerOptions_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_SpeechSynthesizerOptions[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','s','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','z','e','r','O','p','t','i','o','n','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_SpeechSynthesizerOptions[] = L"Windows.Media.SpeechSynthesis.SpeechSynthesizerOptions";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_SpeechSynthesizerOptions[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','s','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','z','e','r','O','p','t','i','o','n','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechSynthesis_SpeechSynthesizerOptions_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Media.SpeechSynthesis.SpeechSynthesisStream
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechSynthesis_SpeechSynthesisStream_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechSynthesis_SpeechSynthesisStream_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_SpeechSynthesisStream[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','s','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','s','S','t','r','e','a','m',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_SpeechSynthesisStream[] = L"Windows.Media.SpeechSynthesis.SpeechSynthesisStream";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_SpeechSynthesisStream[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','s','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','s','S','t','r','e','a','m',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechSynthesis_SpeechSynthesisStream_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechSynthesis.SpeechSynthesizer
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechSynthesis_SpeechSynthesizer_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechSynthesis_SpeechSynthesizer_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_SpeechSynthesizer[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','s','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','z','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_SpeechSynthesizer[] = L"Windows.Media.SpeechSynthesis.SpeechSynthesizer";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechSynthesis_SpeechSynthesizer[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','s','.','S','p','e','e','c','h','S','y','n','t','h','e','s','i','z','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechSynthesis_SpeechSynthesizer_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > interface
 */
#ifndef ____FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation, 0x3c33bb52, 0xbd98, 0x5c8c, 0xad,0xee, 0xee,0x8d,0xa0,0x62,0x8e,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("3c33bb52-bd98-5c8c-adee-ee8da0628efc")
                IIterable<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechSynthesis::VoiceInformation*, ABI::Windows::Media::SpeechSynthesis::IVoiceInformation* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation, 0x3c33bb52, 0xbd98, 0x5c8c, 0xad,0xee, 0xee,0x8d,0xa0,0x62,0x8e,0xfc)
#endif
#else
typedef struct __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation **value);

    END_INTERFACE
} __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl;

interface __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation {
    CONST_VTBL __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > methods ***/
#define __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_QueryInterface(__FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_AddRef(__FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_Release(__FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetIids(__FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetRuntimeClassName(__FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetTrustLevel(__FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_First(__FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_VoiceInformation IID___FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation
#define IIterable_VoiceInformationVtbl __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl
#define IIterable_VoiceInformation __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation
#define IIterable_VoiceInformation_QueryInterface __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_QueryInterface
#define IIterable_VoiceInformation_AddRef __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_AddRef
#define IIterable_VoiceInformation_Release __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_Release
#define IIterable_VoiceInformation_GetIids __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetIids
#define IIterable_VoiceInformation_GetRuntimeClassName __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetRuntimeClassName
#define IIterable_VoiceInformation_GetTrustLevel __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetTrustLevel
#define IIterable_VoiceInformation_First __FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > interface
 */
#ifndef ____FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation, 0x12d40a27, 0xae8d, 0x5fb0, 0x8f,0xed, 0x00,0x16,0x5d,0x59,0xc6,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("12d40a27-ae8d-5fb0-8fed-00165d59c6ab")
                IIterator<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechSynthesis::VoiceInformation*, ABI::Windows::Media::SpeechSynthesis::IVoiceInformation* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation, 0x12d40a27, 0xae8d, 0x5fb0, 0x8f,0xed, 0x00,0x16,0x5d,0x59,0xc6,0xab)
#endif
#else
typedef struct __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl;

interface __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation {
    CONST_VTBL __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > methods ***/
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_QueryInterface(__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_AddRef(__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_Release(__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetIids(__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetRuntimeClassName(__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetTrustLevel(__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_get_Current(__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_get_HasCurrent(__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_MoveNext(__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetMany(__FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,UINT32 items_size,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_VoiceInformation IID___FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation
#define IIterator_VoiceInformationVtbl __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl
#define IIterator_VoiceInformation __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation
#define IIterator_VoiceInformation_QueryInterface __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_QueryInterface
#define IIterator_VoiceInformation_AddRef __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_AddRef
#define IIterator_VoiceInformation_Release __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_Release
#define IIterator_VoiceInformation_GetIids __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetIids
#define IIterator_VoiceInformation_GetRuntimeClassName __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetRuntimeClassName
#define IIterator_VoiceInformation_GetTrustLevel __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetTrustLevel
#define IIterator_VoiceInformation_get_Current __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_get_Current
#define IIterator_VoiceInformation_get_HasCurrent __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_get_HasCurrent
#define IIterator_VoiceInformation_MoveNext __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_MoveNext
#define IIterator_VoiceInformation_GetMany __FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > interface
 */
#ifndef ____FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation, 0xee8d63ce, 0x51ac, 0x5984, 0x89,0x1b, 0xd2,0x32,0xfa,0x7f,0x64,0x53);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("ee8d63ce-51ac-5984-891b-d232fa7f6453")
                IVectorView<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechSynthesis::VoiceInformation*, ABI::Windows::Media::SpeechSynthesis::IVoiceInformation* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation, 0xee8d63ce, 0x51ac, 0x5984, 0x89,0x1b, 0xd2,0x32,0xfa,0x7f,0x64,0x53)
#endif
#else
typedef struct __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl;

interface __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation {
    CONST_VTBL __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > methods ***/
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_QueryInterface(__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_AddRef(__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_Release(__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetIids(__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetRuntimeClassName(__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetTrustLevel(__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Media::SpeechSynthesis::VoiceInformation* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetAt(__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,UINT32 index,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_get_Size(__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_IndexOf(__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetMany(__FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CIVoiceInformation **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_VoiceInformation IID___FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation
#define IVectorView_VoiceInformationVtbl __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformationVtbl
#define IVectorView_VoiceInformation __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation
#define IVectorView_VoiceInformation_QueryInterface __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_QueryInterface
#define IVectorView_VoiceInformation_AddRef __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_AddRef
#define IVectorView_VoiceInformation_Release __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_Release
#define IVectorView_VoiceInformation_GetIids __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetIids
#define IVectorView_VoiceInformation_GetRuntimeClassName __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetRuntimeClassName
#define IVectorView_VoiceInformation_GetTrustLevel __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetTrustLevel
#define IVectorView_VoiceInformation_GetAt __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetAt
#define IVectorView_VoiceInformation_get_Size __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_get_Size
#define IVectorView_VoiceInformation_IndexOf __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_IndexOf
#define IVectorView_VoiceInformation_GetMany __FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CMedia__CSpeechSynthesis__CVoiceInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream, 0xdf9d48ad, 0x9cea, 0x560c, 0x9e,0xdc, 0xcb,0x88,0x52,0xcb,0x55,0xe3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("df9d48ad-9cea-560c-9edc-cb8852cb55e3")
            IAsyncOperation<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream*, ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesisStream* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream, 0xdf9d48ad, 0x9cea, 0x560c, 0x9e,0xdc, 0xcb,0x88,0x52,0xcb,0x55,0xe3)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This,
        __x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStreamVtbl;

interface __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream {
    CONST_VTBL __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_QueryInterface(__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_AddRef(__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_Release(__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetIids(__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetTrustLevel(__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_put_Completed(__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This,__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_get_Completed(__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This,__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetResults(__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This,__x_ABI_CWindows_CMedia_CSpeechSynthesis_CISpeechSynthesisStream **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_SpeechSynthesisStream IID___FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream
#define IAsyncOperation_SpeechSynthesisStreamVtbl __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStreamVtbl
#define IAsyncOperation_SpeechSynthesisStream __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream
#define IAsyncOperation_SpeechSynthesisStream_QueryInterface __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_QueryInterface
#define IAsyncOperation_SpeechSynthesisStream_AddRef __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_AddRef
#define IAsyncOperation_SpeechSynthesisStream_Release __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_Release
#define IAsyncOperation_SpeechSynthesisStream_GetIids __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetIids
#define IAsyncOperation_SpeechSynthesisStream_GetRuntimeClassName __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetRuntimeClassName
#define IAsyncOperation_SpeechSynthesisStream_GetTrustLevel __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetTrustLevel
#define IAsyncOperation_SpeechSynthesisStream_put_Completed __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_put_Completed
#define IAsyncOperation_SpeechSynthesisStream_get_Completed __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_get_Completed
#define IAsyncOperation_SpeechSynthesisStream_GetResults __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream, 0xc972b996, 0x6165, 0x50d4, 0xaf,0x60, 0xa8,0xc3,0xdf,0x51,0xd0,0x92);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("c972b996-6165-50d4-af60-a8c3df51d092")
            IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream*, ABI::Windows::Media::SpeechSynthesis::ISpeechSynthesisStream* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream, 0xc972b996, 0x6165, 0x50d4, 0xaf,0x60, 0xa8,0xc3,0xdf,0x51,0xd0,0x92)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *This,
        __FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStreamVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_Release(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechSynthesis::SpeechSynthesisStream* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream* This,__FIAsyncOperation_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_SpeechSynthesisStream IID___FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream
#define IAsyncOperationCompletedHandler_SpeechSynthesisStreamVtbl __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStreamVtbl
#define IAsyncOperationCompletedHandler_SpeechSynthesisStream __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream
#define IAsyncOperationCompletedHandler_SpeechSynthesisStream_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_QueryInterface
#define IAsyncOperationCompletedHandler_SpeechSynthesisStream_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_AddRef
#define IAsyncOperationCompletedHandler_SpeechSynthesisStream_Release __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_Release
#define IAsyncOperationCompletedHandler_SpeechSynthesisStream_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechSynthesis__CSpeechSynthesisStream_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_media_speechsynthesis_h__ */
