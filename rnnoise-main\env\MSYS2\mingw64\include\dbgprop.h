/*** Autogenerated by WIDL 10.12 from include/dbgprop.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dbgprop_h__
#define __dbgprop_h__

/* Forward declarations */

#ifndef __IDebugProperty_FWD_DEFINED__
#define __IDebugProperty_FWD_DEFINED__
typedef interface IDebugProperty IDebugProperty;
#ifdef __cplusplus
interface IDebugProperty;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugPropertyInfo_FWD_DEFINED__
#define __IEnumDebugPropertyInfo_FWD_DEFINED__
typedef interface IEnumDebugPropertyInfo IEnumDebugPropertyInfo;
#ifdef __cplusplus
interface IEnumDebugPropertyInfo;
#endif /* __cplusplus */
#endif

#ifndef __IDebugExtendedProperty_FWD_DEFINED__
#define __IDebugExtendedProperty_FWD_DEFINED__
typedef interface IDebugExtendedProperty IDebugExtendedProperty;
#ifdef __cplusplus
interface IDebugExtendedProperty;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugExtendedPropertyInfo_FWD_DEFINED__
#define __IEnumDebugExtendedPropertyInfo_FWD_DEFINED__
typedef interface IEnumDebugExtendedPropertyInfo IEnumDebugExtendedPropertyInfo;
#ifdef __cplusplus
interface IEnumDebugExtendedPropertyInfo;
#endif /* __cplusplus */
#endif

#ifndef __IPerPropertyBrowsing2_FWD_DEFINED__
#define __IPerPropertyBrowsing2_FWD_DEFINED__
typedef interface IPerPropertyBrowsing2 IPerPropertyBrowsing2;
#ifdef __cplusplus
interface IPerPropertyBrowsing2;
#endif /* __cplusplus */
#endif

#ifndef __IDebugPropertyEnumType_All_FWD_DEFINED__
#define __IDebugPropertyEnumType_All_FWD_DEFINED__
typedef interface IDebugPropertyEnumType_All IDebugPropertyEnumType_All;
#ifdef __cplusplus
interface IDebugPropertyEnumType_All;
#endif /* __cplusplus */
#endif

#ifndef __IDebugPropertyEnumType_Locals_FWD_DEFINED__
#define __IDebugPropertyEnumType_Locals_FWD_DEFINED__
typedef interface IDebugPropertyEnumType_Locals IDebugPropertyEnumType_Locals;
#ifdef __cplusplus
interface IDebugPropertyEnumType_Locals;
#endif /* __cplusplus */
#endif

#ifndef __IDebugPropertyEnumType_Arguments_FWD_DEFINED__
#define __IDebugPropertyEnumType_Arguments_FWD_DEFINED__
typedef interface IDebugPropertyEnumType_Arguments IDebugPropertyEnumType_Arguments;
#ifdef __cplusplus
interface IDebugPropertyEnumType_Arguments;
#endif /* __cplusplus */
#endif

#ifndef __IDebugPropertyEnumType_LocalsPlusArgs_FWD_DEFINED__
#define __IDebugPropertyEnumType_LocalsPlusArgs_FWD_DEFINED__
typedef interface IDebugPropertyEnumType_LocalsPlusArgs IDebugPropertyEnumType_LocalsPlusArgs;
#ifdef __cplusplus
interface IDebugPropertyEnumType_LocalsPlusArgs;
#endif /* __cplusplus */
#endif

#ifndef __IDebugPropertyEnumType_Registers_FWD_DEFINED__
#define __IDebugPropertyEnumType_Registers_FWD_DEFINED__
typedef interface IDebugPropertyEnumType_Registers IDebugPropertyEnumType_Registers;
#ifdef __cplusplus
interface IDebugPropertyEnumType_Registers;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <ocidl.h>
#include <oleidl.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)


#ifndef __IDebugProperty_FWD_DEFINED__
#define __IDebugProperty_FWD_DEFINED__
typedef interface IDebugProperty IDebugProperty;
#ifdef __cplusplus
interface IDebugProperty;
#endif /* __cplusplus */
#endif

#ifndef __IDebugExtendedProperty_FWD_DEFINED__
#define __IDebugExtendedProperty_FWD_DEFINED__
typedef interface IDebugExtendedProperty IDebugExtendedProperty;
#ifdef __cplusplus
interface IDebugExtendedProperty;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugPropertyInfo_FWD_DEFINED__
#define __IEnumDebugPropertyInfo_FWD_DEFINED__
typedef interface IEnumDebugPropertyInfo IEnumDebugPropertyInfo;
#ifdef __cplusplus
interface IEnumDebugPropertyInfo;
#endif /* __cplusplus */
#endif

#ifndef __IEnumDebugExtendedPropertyInfo_FWD_DEFINED__
#define __IEnumDebugExtendedPropertyInfo_FWD_DEFINED__
typedef interface IEnumDebugExtendedPropertyInfo IEnumDebugExtendedPropertyInfo;
#ifdef __cplusplus
interface IEnumDebugExtendedPropertyInfo;
#endif /* __cplusplus */
#endif

#ifndef __IPerPropertyBrowsing2_FWD_DEFINED__
#define __IPerPropertyBrowsing2_FWD_DEFINED__
typedef interface IPerPropertyBrowsing2 IPerPropertyBrowsing2;
#ifdef __cplusplus
interface IPerPropertyBrowsing2;
#endif /* __cplusplus */
#endif


extern GUID guidDocument;
extern GUID guidCodeContext;

enum {
    DBGPROP_ATTRIB_NO_ATTRIB = 0x0,
    DBGPROP_ATTRIB_VALUE_IS_INVALID = 0x8,
    DBGPROP_ATTRIB_VALUE_IS_EXPANDABLE = 0x10,
    DBGPROP_ATTRIB_VALUE_IS_FAKE = 0x20,
    DBGPROP_ATTRIB_VALUE_IS_METHOD = 0x100,
    DBGPROP_ATTRIB_VALUE_IS_EVENT = 0x200,
    DBGPROP_ATTRIB_VALUE_IS_RAW_STRING = 0x400,
    DBGPROP_ATTRIB_VALUE_READONLY = 0x800,
    DBGPROP_ATTRIB_ACCESS_PUBLIC = 0x1000,
    DBGPROP_ATTRIB_ACCESS_PRIVATE = 0x2000,
    DBGPROP_ATTRIB_ACCESS_PROTECTED = 0x4000,
    DBGPROP_ATTRIB_ACCESS_FINAL = 0x8000,
    DBGPROP_ATTRIB_STORAGE_GLOBAL = 0x10000,
    DBGPROP_ATTRIB_STORAGE_STATIC = 0x20000,
    DBGPROP_ATTRIB_STORAGE_FIELD = 0x40000,
    DBGPROP_ATTRIB_STORAGE_VIRTUAL = 0x80000,
    DBGPROP_ATTRIB_TYPE_IS_CONSTANT = 0x100000,
    DBGPROP_ATTRIB_TYPE_IS_SYNCHRONIZED = 0x200000,
    DBGPROP_ATTRIB_TYPE_IS_VOLATILE = 0x400000,
    DBGPROP_ATTRIB_HAS_EXTENDED_ATTRIBS = 0x800000
};

typedef DWORD DBGPROP_ATTRIB_FLAGS;

enum {
    DBGPROP_INFO_NAME = 0x1,
    DBGPROP_INFO_TYPE = 0x2,
    DBGPROP_INFO_VALUE = 0x4,
    DBGPROP_INFO_FULLNAME = 0x20,
    DBGPROP_INFO_ATTRIBUTES = 0x8,
    DBGPROP_INFO_DEBUGPROP = 0x10,
    DBGPROP_INFO_BEAUTIFY = 0x2000000,
    DBGPROP_INFO_CALLTOSTRING = 0x4000000,
    DBGPROP_INFO_AUTOEXPAND = 0x8000000
};

typedef DWORD DBGPROP_INFO_FLAGS;

#define DBGPROP_INFO_STANDARD (((DBGPROP_INFO_NAME | DBGPROP_INFO_TYPE) | DBGPROP_INFO_VALUE) | DBGPROP_INFO_ATTRIBUTES)

#define DBGPROP_INFO_ALL (((((DBGPROP_INFO_NAME | DBGPROP_INFO_TYPE) | DBGPROP_INFO_VALUE) | DBGPROP_INFO_FULLNAME) | DBGPROP_INFO_ATTRIBUTES) | DBGPROP_INFO_DEBUGPROP)


typedef enum tagOBJECT_ATTRIB_FLAG {
    OBJECT_ATTRIB_NO_ATTRIB = 0x0,
    OBJECT_ATTRIB_NO_NAME = 0x1,
    OBJECT_ATTRIB_NO_TYPE = 0x2,
    OBJECT_ATTRIB_NO_VALUE = 0x4,
    OBJECT_ATTRIB_VALUE_IS_INVALID = 0x8,
    OBJECT_ATTRIB_VALUE_IS_OBJECT = 0x10,
    OBJECT_ATTRIB_VALUE_IS_ENUM = 0x20,
    OBJECT_ATTRIB_VALUE_IS_CUSTOM = 0x40,
    OBJECT_ATTRIB_OBJECT_IS_EXPANDABLE = 0x70,
    OBJECT_ATTRIB_VALUE_HAS_CODE = 0x80,
    OBJECT_ATTRIB_TYPE_IS_OBJECT = 0x100,
    OBJECT_ATTRIB_TYPE_HAS_CODE = 0x200,
    OBJECT_ATTRIB_TYPE_IS_EXPANDABLE = 0x100,
    OBJECT_ATTRIB_SLOT_IS_CATEGORY = 0x400,
    OBJECT_ATTRIB_VALUE_READONLY = 0x800,
    OBJECT_ATTRIB_ACCESS_PUBLIC = 0x1000,
    OBJECT_ATTRIB_ACCESS_PRIVATE = 0x2000,
    OBJECT_ATTRIB_ACCESS_PROTECTED = 0x4000,
    OBJECT_ATTRIB_ACCESS_FINAL = 0x8000,
    OBJECT_ATTRIB_STORAGE_GLOBAL = 0x10000,
    OBJECT_ATTRIB_STORAGE_STATIC = 0x20000,
    OBJECT_ATTRIB_STORAGE_FIELD = 0x40000,
    OBJECT_ATTRIB_STORAGE_VIRTUAL = 0x80000,
    OBJECT_ATTRIB_TYPE_IS_CONSTANT = 0x100000,
    OBJECT_ATTRIB_TYPE_IS_SYNCHRONIZED = 0x200000,
    OBJECT_ATTRIB_TYPE_IS_VOLATILE = 0x400000,
    OBJECT_ATTRIB_HAS_EXTENDED_ATTRIBS = 0x800000,
    OBJECT_ATTRIB_IS_CLASS = 0x1000000,
    OBJECT_ATTRIB_IS_FUNCTION = 0x2000000,
    OBJECT_ATTRIB_IS_VARIABLE = 0x4000000,
    OBJECT_ATTRIB_IS_PROPERTY = 0x8000000,
    OBJECT_ATTRIB_IS_MACRO = 0x10000000,
    OBJECT_ATTRIB_IS_TYPE = 0x20000000,
    OBJECT_ATTRIB_IS_INHERITED = 0x40000000,
    OBJECT_ATTRIB_IS_INTERFACE = 0x80000000
} OBJECT_ATTRIB_FLAGS;

typedef enum tagPROP_INFO_FLAGS {
    PROP_INFO_NAME = 0x1,
    PROP_INFO_TYPE = 0x2,
    PROP_INFO_VALUE = 0x4,
    PROP_INFO_FULLNAME = 0x20,
    PROP_INFO_ATTRIBUTES = 0x8,
    PROP_INFO_DEBUGPROP = 0x10,
    PROP_INFO_AUTOEXPAND = 0x8000000
} PROP_INFO_FLAGS;

#define PROP_INFO_STANDARD (((PROP_INFO_NAME | PROP_INFO_TYPE) | PROP_INFO_VALUE) | PROP_INFO_ATTRIBUTES)

#define PROP_INFO_ALL (((((PROP_INFO_NAME | PROP_INFO_TYPE) | PROP_INFO_VALUE) | PROP_INFO_FULLNAME) | PROP_INFO_ATTRIBUTES) | PROP_INFO_DEBUGPROP)


typedef struct tagDebugPropertyInfo {
    DWORD m_dwValidFields;
    BSTR m_bstrName;
    BSTR m_bstrType;
    BSTR m_bstrValue;
    BSTR m_bstrFullName;
    DWORD m_dwAttrib;
    IDebugProperty *m_pDebugProp;
} DebugPropertyInfo;

typedef enum tagEX_PROP_INFO_FLAGS {
    EX_PROP_INFO_ID = 0x100,
    EX_PROP_INFO_NTYPE = 0x200,
    EX_PROP_INFO_NVALUE = 0x400,
    EX_PROP_INFO_LOCKBYTES = 0x800,
    EX_PROP_INFO_DEBUGEXTPROP = 0x1000
} EX_PROP_INFO_FLAGS;
typedef struct tagExtendedDebugPropertyInfo {
    DWORD dwValidFields;
    LPOLESTR pszName;
    LPOLESTR pszType;
    LPOLESTR pszValue;
    LPOLESTR pszFullName;
    DWORD dwAttrib;
    IDebugProperty *pDebugProp;
    DWORD nDISPID;
    DWORD nType;
    VARIANT varValue;
    ILockBytes *plbValue;
    IDebugExtendedProperty *pDebugExtProp;
} ExtendedDebugPropertyInfo;

/*****************************************************************************
 * IDebugProperty interface
 */
#ifndef __IDebugProperty_INTERFACE_DEFINED__
#define __IDebugProperty_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugProperty, 0x51973c50, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c50-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugProperty : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPropertyInfo(
        DWORD dwFieldSpec,
        UINT nRadix,
        DebugPropertyInfo *pPropertyInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExtendedInfo(
        ULONG cInfos,
        GUID *rgguidExtendedInfo,
        VARIANT *rgvar) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValueAsString(
        LPCOLESTR pszValue,
        UINT nRadix) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumMembers(
        DWORD dwFieldSpec,
        UINT nRadix,
        REFIID refiid,
        IEnumDebugPropertyInfo **ppepi) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetParent(
        IDebugProperty **ppDebugProp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugProperty, 0x51973c50, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugPropertyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugProperty *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugProperty *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugProperty *This);

    /*** IDebugProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyInfo)(
        IDebugProperty *This,
        DWORD dwFieldSpec,
        UINT nRadix,
        DebugPropertyInfo *pPropertyInfo);

    HRESULT (STDMETHODCALLTYPE *GetExtendedInfo)(
        IDebugProperty *This,
        ULONG cInfos,
        GUID *rgguidExtendedInfo,
        VARIANT *rgvar);

    HRESULT (STDMETHODCALLTYPE *SetValueAsString)(
        IDebugProperty *This,
        LPCOLESTR pszValue,
        UINT nRadix);

    HRESULT (STDMETHODCALLTYPE *EnumMembers)(
        IDebugProperty *This,
        DWORD dwFieldSpec,
        UINT nRadix,
        REFIID refiid,
        IEnumDebugPropertyInfo **ppepi);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDebugProperty *This,
        IDebugProperty **ppDebugProp);

    END_INTERFACE
} IDebugPropertyVtbl;

interface IDebugProperty {
    CONST_VTBL IDebugPropertyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugProperty_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugProperty_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugProperty_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugProperty methods ***/
#define IDebugProperty_GetPropertyInfo(This,dwFieldSpec,nRadix,pPropertyInfo) (This)->lpVtbl->GetPropertyInfo(This,dwFieldSpec,nRadix,pPropertyInfo)
#define IDebugProperty_GetExtendedInfo(This,cInfos,rgguidExtendedInfo,rgvar) (This)->lpVtbl->GetExtendedInfo(This,cInfos,rgguidExtendedInfo,rgvar)
#define IDebugProperty_SetValueAsString(This,pszValue,nRadix) (This)->lpVtbl->SetValueAsString(This,pszValue,nRadix)
#define IDebugProperty_EnumMembers(This,dwFieldSpec,nRadix,refiid,ppepi) (This)->lpVtbl->EnumMembers(This,dwFieldSpec,nRadix,refiid,ppepi)
#define IDebugProperty_GetParent(This,ppDebugProp) (This)->lpVtbl->GetParent(This,ppDebugProp)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugProperty_QueryInterface(IDebugProperty* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugProperty_AddRef(IDebugProperty* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugProperty_Release(IDebugProperty* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugProperty methods ***/
static inline HRESULT IDebugProperty_GetPropertyInfo(IDebugProperty* This,DWORD dwFieldSpec,UINT nRadix,DebugPropertyInfo *pPropertyInfo) {
    return This->lpVtbl->GetPropertyInfo(This,dwFieldSpec,nRadix,pPropertyInfo);
}
static inline HRESULT IDebugProperty_GetExtendedInfo(IDebugProperty* This,ULONG cInfos,GUID *rgguidExtendedInfo,VARIANT *rgvar) {
    return This->lpVtbl->GetExtendedInfo(This,cInfos,rgguidExtendedInfo,rgvar);
}
static inline HRESULT IDebugProperty_SetValueAsString(IDebugProperty* This,LPCOLESTR pszValue,UINT nRadix) {
    return This->lpVtbl->SetValueAsString(This,pszValue,nRadix);
}
static inline HRESULT IDebugProperty_EnumMembers(IDebugProperty* This,DWORD dwFieldSpec,UINT nRadix,REFIID refiid,IEnumDebugPropertyInfo **ppepi) {
    return This->lpVtbl->EnumMembers(This,dwFieldSpec,nRadix,refiid,ppepi);
}
static inline HRESULT IDebugProperty_GetParent(IDebugProperty* This,IDebugProperty **ppDebugProp) {
    return This->lpVtbl->GetParent(This,ppDebugProp);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IDebugProperty_RemoteGetPropertyInfo_Proxy(
    IDebugProperty* This,
    DWORD dwFieldSpec,
    UINT nRadix,
    DWORD *dwValidFields,
    BSTR *pbstrName,
    BSTR *pbstrType,
    BSTR *pbstrValue,
    BSTR *pbstrFullName,
    DWORD *pdwAttrib,
    IDebugProperty **ppDebugProperty);
void __RPC_STUB IDebugProperty_RemoteGetPropertyInfo_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IDebugProperty_GetPropertyInfo_Proxy(
    IDebugProperty* This,
    DWORD dwFieldSpec,
    UINT nRadix,
    DebugPropertyInfo *pPropertyInfo);
HRESULT __RPC_STUB IDebugProperty_GetPropertyInfo_Stub(
    IDebugProperty* This,
    DWORD dwFieldSpec,
    UINT nRadix,
    DWORD *dwValidFields,
    BSTR *pbstrName,
    BSTR *pbstrType,
    BSTR *pbstrValue,
    BSTR *pbstrFullName,
    DWORD *pdwAttrib,
    IDebugProperty **ppDebugProperty);

#endif  /* __IDebugProperty_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEnumDebugPropertyInfo interface
 */
#ifndef __IEnumDebugPropertyInfo_INTERFACE_DEFINED__
#define __IEnumDebugPropertyInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumDebugPropertyInfo, 0x51973c51, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c51-cb0c-11d0-b5c9-00a0244a0e7a")
IEnumDebugPropertyInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        DebugPropertyInfo *pi,
        ULONG *pcEltsfetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumDebugPropertyInfo **ppepi) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        ULONG *pcelt) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumDebugPropertyInfo, 0x51973c51, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IEnumDebugPropertyInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumDebugPropertyInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumDebugPropertyInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumDebugPropertyInfo *This);

    /*** IEnumDebugPropertyInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumDebugPropertyInfo *This,
        ULONG celt,
        DebugPropertyInfo *pi,
        ULONG *pcEltsfetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumDebugPropertyInfo *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumDebugPropertyInfo *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumDebugPropertyInfo *This,
        IEnumDebugPropertyInfo **ppepi);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IEnumDebugPropertyInfo *This,
        ULONG *pcelt);

    END_INTERFACE
} IEnumDebugPropertyInfoVtbl;

interface IEnumDebugPropertyInfo {
    CONST_VTBL IEnumDebugPropertyInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumDebugPropertyInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumDebugPropertyInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumDebugPropertyInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumDebugPropertyInfo methods ***/
#define IEnumDebugPropertyInfo_Next(This,celt,pi,pcEltsfetched) (This)->lpVtbl->Next(This,celt,pi,pcEltsfetched)
#define IEnumDebugPropertyInfo_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumDebugPropertyInfo_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumDebugPropertyInfo_Clone(This,ppepi) (This)->lpVtbl->Clone(This,ppepi)
#define IEnumDebugPropertyInfo_GetCount(This,pcelt) (This)->lpVtbl->GetCount(This,pcelt)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumDebugPropertyInfo_QueryInterface(IEnumDebugPropertyInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumDebugPropertyInfo_AddRef(IEnumDebugPropertyInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumDebugPropertyInfo_Release(IEnumDebugPropertyInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumDebugPropertyInfo methods ***/
static inline HRESULT IEnumDebugPropertyInfo_Next(IEnumDebugPropertyInfo* This,ULONG celt,DebugPropertyInfo *pi,ULONG *pcEltsfetched) {
    return This->lpVtbl->Next(This,celt,pi,pcEltsfetched);
}
static inline HRESULT IEnumDebugPropertyInfo_Skip(IEnumDebugPropertyInfo* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumDebugPropertyInfo_Reset(IEnumDebugPropertyInfo* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumDebugPropertyInfo_Clone(IEnumDebugPropertyInfo* This,IEnumDebugPropertyInfo **ppepi) {
    return This->lpVtbl->Clone(This,ppepi);
}
static inline HRESULT IEnumDebugPropertyInfo_GetCount(IEnumDebugPropertyInfo* This,ULONG *pcelt) {
    return This->lpVtbl->GetCount(This,pcelt);
}
#endif
#endif

#endif

HRESULT __stdcall IEnumDebugPropertyInfo_RemoteNext_Proxy(
    IEnumDebugPropertyInfo* This,
    ULONG celt,
    DebugPropertyInfo *pinfo,
    ULONG *pcEltsfetched);
void __RPC_STUB IEnumDebugPropertyInfo_RemoteNext_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IEnumDebugPropertyInfo_Next_Proxy(
    IEnumDebugPropertyInfo* This,
    ULONG celt,
    DebugPropertyInfo *pi,
    ULONG *pcEltsfetched);
HRESULT __RPC_STUB IEnumDebugPropertyInfo_Next_Stub(
    IEnumDebugPropertyInfo* This,
    ULONG celt,
    DebugPropertyInfo *pinfo,
    ULONG *pcEltsfetched);

#endif  /* __IEnumDebugPropertyInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IDebugExtendedProperty interface
 */
#ifndef __IDebugExtendedProperty_INTERFACE_DEFINED__
#define __IDebugExtendedProperty_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugExtendedProperty, 0x51973c52, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c52-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugExtendedProperty : public IDebugProperty
{
    virtual HRESULT STDMETHODCALLTYPE GetExtendedPropertyInfo(
        DWORD dwFieldSpec,
        UINT nRadix,
        ExtendedDebugPropertyInfo *pExtendedPropertyInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumExtendedMembers(
        DWORD dwFieldSpec,
        UINT nRadix,
        IEnumDebugExtendedPropertyInfo **ppeepi) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugExtendedProperty, 0x51973c52, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugExtendedPropertyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugExtendedProperty *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugExtendedProperty *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugExtendedProperty *This);

    /*** IDebugProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyInfo)(
        IDebugExtendedProperty *This,
        DWORD dwFieldSpec,
        UINT nRadix,
        DebugPropertyInfo *pPropertyInfo);

    HRESULT (STDMETHODCALLTYPE *GetExtendedInfo)(
        IDebugExtendedProperty *This,
        ULONG cInfos,
        GUID *rgguidExtendedInfo,
        VARIANT *rgvar);

    HRESULT (STDMETHODCALLTYPE *SetValueAsString)(
        IDebugExtendedProperty *This,
        LPCOLESTR pszValue,
        UINT nRadix);

    HRESULT (STDMETHODCALLTYPE *EnumMembers)(
        IDebugExtendedProperty *This,
        DWORD dwFieldSpec,
        UINT nRadix,
        REFIID refiid,
        IEnumDebugPropertyInfo **ppepi);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDebugExtendedProperty *This,
        IDebugProperty **ppDebugProp);

    /*** IDebugExtendedProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *GetExtendedPropertyInfo)(
        IDebugExtendedProperty *This,
        DWORD dwFieldSpec,
        UINT nRadix,
        ExtendedDebugPropertyInfo *pExtendedPropertyInfo);

    HRESULT (STDMETHODCALLTYPE *EnumExtendedMembers)(
        IDebugExtendedProperty *This,
        DWORD dwFieldSpec,
        UINT nRadix,
        IEnumDebugExtendedPropertyInfo **ppeepi);

    END_INTERFACE
} IDebugExtendedPropertyVtbl;

interface IDebugExtendedProperty {
    CONST_VTBL IDebugExtendedPropertyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugExtendedProperty_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugExtendedProperty_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugExtendedProperty_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugProperty methods ***/
#define IDebugExtendedProperty_GetPropertyInfo(This,dwFieldSpec,nRadix,pPropertyInfo) (This)->lpVtbl->GetPropertyInfo(This,dwFieldSpec,nRadix,pPropertyInfo)
#define IDebugExtendedProperty_GetExtendedInfo(This,cInfos,rgguidExtendedInfo,rgvar) (This)->lpVtbl->GetExtendedInfo(This,cInfos,rgguidExtendedInfo,rgvar)
#define IDebugExtendedProperty_SetValueAsString(This,pszValue,nRadix) (This)->lpVtbl->SetValueAsString(This,pszValue,nRadix)
#define IDebugExtendedProperty_EnumMembers(This,dwFieldSpec,nRadix,refiid,ppepi) (This)->lpVtbl->EnumMembers(This,dwFieldSpec,nRadix,refiid,ppepi)
#define IDebugExtendedProperty_GetParent(This,ppDebugProp) (This)->lpVtbl->GetParent(This,ppDebugProp)
/*** IDebugExtendedProperty methods ***/
#define IDebugExtendedProperty_GetExtendedPropertyInfo(This,dwFieldSpec,nRadix,pExtendedPropertyInfo) (This)->lpVtbl->GetExtendedPropertyInfo(This,dwFieldSpec,nRadix,pExtendedPropertyInfo)
#define IDebugExtendedProperty_EnumExtendedMembers(This,dwFieldSpec,nRadix,ppeepi) (This)->lpVtbl->EnumExtendedMembers(This,dwFieldSpec,nRadix,ppeepi)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugExtendedProperty_QueryInterface(IDebugExtendedProperty* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugExtendedProperty_AddRef(IDebugExtendedProperty* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugExtendedProperty_Release(IDebugExtendedProperty* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugProperty methods ***/
static inline HRESULT IDebugExtendedProperty_GetPropertyInfo(IDebugExtendedProperty* This,DWORD dwFieldSpec,UINT nRadix,DebugPropertyInfo *pPropertyInfo) {
    return This->lpVtbl->GetPropertyInfo(This,dwFieldSpec,nRadix,pPropertyInfo);
}
static inline HRESULT IDebugExtendedProperty_GetExtendedInfo(IDebugExtendedProperty* This,ULONG cInfos,GUID *rgguidExtendedInfo,VARIANT *rgvar) {
    return This->lpVtbl->GetExtendedInfo(This,cInfos,rgguidExtendedInfo,rgvar);
}
static inline HRESULT IDebugExtendedProperty_SetValueAsString(IDebugExtendedProperty* This,LPCOLESTR pszValue,UINT nRadix) {
    return This->lpVtbl->SetValueAsString(This,pszValue,nRadix);
}
static inline HRESULT IDebugExtendedProperty_EnumMembers(IDebugExtendedProperty* This,DWORD dwFieldSpec,UINT nRadix,REFIID refiid,IEnumDebugPropertyInfo **ppepi) {
    return This->lpVtbl->EnumMembers(This,dwFieldSpec,nRadix,refiid,ppepi);
}
static inline HRESULT IDebugExtendedProperty_GetParent(IDebugExtendedProperty* This,IDebugProperty **ppDebugProp) {
    return This->lpVtbl->GetParent(This,ppDebugProp);
}
/*** IDebugExtendedProperty methods ***/
static inline HRESULT IDebugExtendedProperty_GetExtendedPropertyInfo(IDebugExtendedProperty* This,DWORD dwFieldSpec,UINT nRadix,ExtendedDebugPropertyInfo *pExtendedPropertyInfo) {
    return This->lpVtbl->GetExtendedPropertyInfo(This,dwFieldSpec,nRadix,pExtendedPropertyInfo);
}
static inline HRESULT IDebugExtendedProperty_EnumExtendedMembers(IDebugExtendedProperty* This,DWORD dwFieldSpec,UINT nRadix,IEnumDebugExtendedPropertyInfo **ppeepi) {
    return This->lpVtbl->EnumExtendedMembers(This,dwFieldSpec,nRadix,ppeepi);
}
#endif
#endif

#endif


#endif  /* __IDebugExtendedProperty_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEnumDebugExtendedPropertyInfo interface
 */
#ifndef __IEnumDebugExtendedPropertyInfo_INTERFACE_DEFINED__
#define __IEnumDebugExtendedPropertyInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumDebugExtendedPropertyInfo, 0x51973c53, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c53-cb0c-11d0-b5c9-00a0244a0e7a")
IEnumDebugExtendedPropertyInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        ExtendedDebugPropertyInfo *rgExtendedPropertyInfo,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumDebugExtendedPropertyInfo **pedpe) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCount(
        ULONG *pcelt) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumDebugExtendedPropertyInfo, 0x51973c53, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IEnumDebugExtendedPropertyInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumDebugExtendedPropertyInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumDebugExtendedPropertyInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumDebugExtendedPropertyInfo *This);

    /*** IEnumDebugExtendedPropertyInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumDebugExtendedPropertyInfo *This,
        ULONG celt,
        ExtendedDebugPropertyInfo *rgExtendedPropertyInfo,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumDebugExtendedPropertyInfo *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumDebugExtendedPropertyInfo *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumDebugExtendedPropertyInfo *This,
        IEnumDebugExtendedPropertyInfo **pedpe);

    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IEnumDebugExtendedPropertyInfo *This,
        ULONG *pcelt);

    END_INTERFACE
} IEnumDebugExtendedPropertyInfoVtbl;

interface IEnumDebugExtendedPropertyInfo {
    CONST_VTBL IEnumDebugExtendedPropertyInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumDebugExtendedPropertyInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumDebugExtendedPropertyInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumDebugExtendedPropertyInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumDebugExtendedPropertyInfo methods ***/
#define IEnumDebugExtendedPropertyInfo_Next(This,celt,rgExtendedPropertyInfo,pceltFetched) (This)->lpVtbl->Next(This,celt,rgExtendedPropertyInfo,pceltFetched)
#define IEnumDebugExtendedPropertyInfo_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumDebugExtendedPropertyInfo_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumDebugExtendedPropertyInfo_Clone(This,pedpe) (This)->lpVtbl->Clone(This,pedpe)
#define IEnumDebugExtendedPropertyInfo_GetCount(This,pcelt) (This)->lpVtbl->GetCount(This,pcelt)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumDebugExtendedPropertyInfo_QueryInterface(IEnumDebugExtendedPropertyInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumDebugExtendedPropertyInfo_AddRef(IEnumDebugExtendedPropertyInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumDebugExtendedPropertyInfo_Release(IEnumDebugExtendedPropertyInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumDebugExtendedPropertyInfo methods ***/
static inline HRESULT IEnumDebugExtendedPropertyInfo_Next(IEnumDebugExtendedPropertyInfo* This,ULONG celt,ExtendedDebugPropertyInfo *rgExtendedPropertyInfo,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgExtendedPropertyInfo,pceltFetched);
}
static inline HRESULT IEnumDebugExtendedPropertyInfo_Skip(IEnumDebugExtendedPropertyInfo* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumDebugExtendedPropertyInfo_Reset(IEnumDebugExtendedPropertyInfo* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumDebugExtendedPropertyInfo_Clone(IEnumDebugExtendedPropertyInfo* This,IEnumDebugExtendedPropertyInfo **pedpe) {
    return This->lpVtbl->Clone(This,pedpe);
}
static inline HRESULT IEnumDebugExtendedPropertyInfo_GetCount(IEnumDebugExtendedPropertyInfo* This,ULONG *pcelt) {
    return This->lpVtbl->GetCount(This,pcelt);
}
#endif
#endif

#endif


#endif  /* __IEnumDebugExtendedPropertyInfo_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IPerPropertyBrowsing2 interface
 */
#ifndef __IPerPropertyBrowsing2_INTERFACE_DEFINED__
#define __IPerPropertyBrowsing2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPerPropertyBrowsing2, 0x51973c54, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c54-cb0c-11d0-b5c9-00a0244a0e7a")
IPerPropertyBrowsing2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDisplayString(
        DISPID dispid,
        BSTR *pBstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE MapPropertyToPage(
        DISPID dispid,
        CLSID *pClsidPropPage) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPredefinedStrings(
        DISPID dispid,
        CALPOLESTR *pCaStrings,
        CADWORD *pCaCookies) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPredefinedValue(
        DISPID dispid,
        DWORD dwCookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPerPropertyBrowsing2, 0x51973c54, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IPerPropertyBrowsing2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPerPropertyBrowsing2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPerPropertyBrowsing2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPerPropertyBrowsing2 *This);

    /*** IPerPropertyBrowsing2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDisplayString)(
        IPerPropertyBrowsing2 *This,
        DISPID dispid,
        BSTR *pBstr);

    HRESULT (STDMETHODCALLTYPE *MapPropertyToPage)(
        IPerPropertyBrowsing2 *This,
        DISPID dispid,
        CLSID *pClsidPropPage);

    HRESULT (STDMETHODCALLTYPE *GetPredefinedStrings)(
        IPerPropertyBrowsing2 *This,
        DISPID dispid,
        CALPOLESTR *pCaStrings,
        CADWORD *pCaCookies);

    HRESULT (STDMETHODCALLTYPE *SetPredefinedValue)(
        IPerPropertyBrowsing2 *This,
        DISPID dispid,
        DWORD dwCookie);

    END_INTERFACE
} IPerPropertyBrowsing2Vtbl;

interface IPerPropertyBrowsing2 {
    CONST_VTBL IPerPropertyBrowsing2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPerPropertyBrowsing2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPerPropertyBrowsing2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPerPropertyBrowsing2_Release(This) (This)->lpVtbl->Release(This)
/*** IPerPropertyBrowsing2 methods ***/
#define IPerPropertyBrowsing2_GetDisplayString(This,dispid,pBstr) (This)->lpVtbl->GetDisplayString(This,dispid,pBstr)
#define IPerPropertyBrowsing2_MapPropertyToPage(This,dispid,pClsidPropPage) (This)->lpVtbl->MapPropertyToPage(This,dispid,pClsidPropPage)
#define IPerPropertyBrowsing2_GetPredefinedStrings(This,dispid,pCaStrings,pCaCookies) (This)->lpVtbl->GetPredefinedStrings(This,dispid,pCaStrings,pCaCookies)
#define IPerPropertyBrowsing2_SetPredefinedValue(This,dispid,dwCookie) (This)->lpVtbl->SetPredefinedValue(This,dispid,dwCookie)
#else
/*** IUnknown methods ***/
static inline HRESULT IPerPropertyBrowsing2_QueryInterface(IPerPropertyBrowsing2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPerPropertyBrowsing2_AddRef(IPerPropertyBrowsing2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPerPropertyBrowsing2_Release(IPerPropertyBrowsing2* This) {
    return This->lpVtbl->Release(This);
}
/*** IPerPropertyBrowsing2 methods ***/
static inline HRESULT IPerPropertyBrowsing2_GetDisplayString(IPerPropertyBrowsing2* This,DISPID dispid,BSTR *pBstr) {
    return This->lpVtbl->GetDisplayString(This,dispid,pBstr);
}
static inline HRESULT IPerPropertyBrowsing2_MapPropertyToPage(IPerPropertyBrowsing2* This,DISPID dispid,CLSID *pClsidPropPage) {
    return This->lpVtbl->MapPropertyToPage(This,dispid,pClsidPropPage);
}
static inline HRESULT IPerPropertyBrowsing2_GetPredefinedStrings(IPerPropertyBrowsing2* This,DISPID dispid,CALPOLESTR *pCaStrings,CADWORD *pCaCookies) {
    return This->lpVtbl->GetPredefinedStrings(This,dispid,pCaStrings,pCaCookies);
}
static inline HRESULT IPerPropertyBrowsing2_SetPredefinedValue(IPerPropertyBrowsing2* This,DISPID dispid,DWORD dwCookie) {
    return This->lpVtbl->SetPredefinedValue(This,dispid,dwCookie);
}
#endif
#endif

#endif


#endif  /* __IPerPropertyBrowsing2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IDebugPropertyEnumType_All interface
 */
#ifndef __IDebugPropertyEnumType_All_INTERFACE_DEFINED__
#define __IDebugPropertyEnumType_All_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugPropertyEnumType_All, 0x51973c55, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c55-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugPropertyEnumType_All : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetName(
        BSTR *a) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugPropertyEnumType_All, 0x51973c55, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugPropertyEnumType_AllVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugPropertyEnumType_All *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugPropertyEnumType_All *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugPropertyEnumType_All *This);

    /*** IDebugPropertyEnumType_All methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugPropertyEnumType_All *This,
        BSTR *a);

    END_INTERFACE
} IDebugPropertyEnumType_AllVtbl;

interface IDebugPropertyEnumType_All {
    CONST_VTBL IDebugPropertyEnumType_AllVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugPropertyEnumType_All_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugPropertyEnumType_All_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugPropertyEnumType_All_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugPropertyEnumType_All methods ***/
#define IDebugPropertyEnumType_All_GetName(This,a) (This)->lpVtbl->GetName(This,a)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugPropertyEnumType_All_QueryInterface(IDebugPropertyEnumType_All* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugPropertyEnumType_All_AddRef(IDebugPropertyEnumType_All* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugPropertyEnumType_All_Release(IDebugPropertyEnumType_All* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugPropertyEnumType_All methods ***/
static inline HRESULT IDebugPropertyEnumType_All_GetName(IDebugPropertyEnumType_All* This,BSTR *a) {
    return This->lpVtbl->GetName(This,a);
}
#endif
#endif

#endif


#endif  /* __IDebugPropertyEnumType_All_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IDebugPropertyEnumType_Locals interface
 */
#ifndef __IDebugPropertyEnumType_Locals_INTERFACE_DEFINED__
#define __IDebugPropertyEnumType_Locals_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugPropertyEnumType_Locals, 0x51973c56, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c56-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugPropertyEnumType_Locals : public IDebugPropertyEnumType_All
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugPropertyEnumType_Locals, 0x51973c56, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugPropertyEnumType_LocalsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugPropertyEnumType_Locals *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugPropertyEnumType_Locals *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugPropertyEnumType_Locals *This);

    /*** IDebugPropertyEnumType_All methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugPropertyEnumType_Locals *This,
        BSTR *a);

    END_INTERFACE
} IDebugPropertyEnumType_LocalsVtbl;

interface IDebugPropertyEnumType_Locals {
    CONST_VTBL IDebugPropertyEnumType_LocalsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugPropertyEnumType_Locals_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugPropertyEnumType_Locals_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugPropertyEnumType_Locals_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugPropertyEnumType_All methods ***/
#define IDebugPropertyEnumType_Locals_GetName(This,a) (This)->lpVtbl->GetName(This,a)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugPropertyEnumType_Locals_QueryInterface(IDebugPropertyEnumType_Locals* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugPropertyEnumType_Locals_AddRef(IDebugPropertyEnumType_Locals* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugPropertyEnumType_Locals_Release(IDebugPropertyEnumType_Locals* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugPropertyEnumType_All methods ***/
static inline HRESULT IDebugPropertyEnumType_Locals_GetName(IDebugPropertyEnumType_Locals* This,BSTR *a) {
    return This->lpVtbl->GetName(This,a);
}
#endif
#endif

#endif


#endif  /* __IDebugPropertyEnumType_Locals_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IDebugPropertyEnumType_Arguments interface
 */
#ifndef __IDebugPropertyEnumType_Arguments_INTERFACE_DEFINED__
#define __IDebugPropertyEnumType_Arguments_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugPropertyEnumType_Arguments, 0x51973c57, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c57-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugPropertyEnumType_Arguments : public IDebugPropertyEnumType_All
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugPropertyEnumType_Arguments, 0x51973c57, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugPropertyEnumType_ArgumentsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugPropertyEnumType_Arguments *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugPropertyEnumType_Arguments *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugPropertyEnumType_Arguments *This);

    /*** IDebugPropertyEnumType_All methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugPropertyEnumType_Arguments *This,
        BSTR *a);

    END_INTERFACE
} IDebugPropertyEnumType_ArgumentsVtbl;

interface IDebugPropertyEnumType_Arguments {
    CONST_VTBL IDebugPropertyEnumType_ArgumentsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugPropertyEnumType_Arguments_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugPropertyEnumType_Arguments_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugPropertyEnumType_Arguments_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugPropertyEnumType_All methods ***/
#define IDebugPropertyEnumType_Arguments_GetName(This,a) (This)->lpVtbl->GetName(This,a)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugPropertyEnumType_Arguments_QueryInterface(IDebugPropertyEnumType_Arguments* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugPropertyEnumType_Arguments_AddRef(IDebugPropertyEnumType_Arguments* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugPropertyEnumType_Arguments_Release(IDebugPropertyEnumType_Arguments* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugPropertyEnumType_All methods ***/
static inline HRESULT IDebugPropertyEnumType_Arguments_GetName(IDebugPropertyEnumType_Arguments* This,BSTR *a) {
    return This->lpVtbl->GetName(This,a);
}
#endif
#endif

#endif


#endif  /* __IDebugPropertyEnumType_Arguments_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IDebugPropertyEnumType_LocalsPlusArgs interface
 */
#ifndef __IDebugPropertyEnumType_LocalsPlusArgs_INTERFACE_DEFINED__
#define __IDebugPropertyEnumType_LocalsPlusArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugPropertyEnumType_LocalsPlusArgs, 0x51973c58, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c58-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugPropertyEnumType_LocalsPlusArgs : public IDebugPropertyEnumType_All
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugPropertyEnumType_LocalsPlusArgs, 0x51973c58, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugPropertyEnumType_LocalsPlusArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugPropertyEnumType_LocalsPlusArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugPropertyEnumType_LocalsPlusArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugPropertyEnumType_LocalsPlusArgs *This);

    /*** IDebugPropertyEnumType_All methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugPropertyEnumType_LocalsPlusArgs *This,
        BSTR *a);

    END_INTERFACE
} IDebugPropertyEnumType_LocalsPlusArgsVtbl;

interface IDebugPropertyEnumType_LocalsPlusArgs {
    CONST_VTBL IDebugPropertyEnumType_LocalsPlusArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugPropertyEnumType_LocalsPlusArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugPropertyEnumType_LocalsPlusArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugPropertyEnumType_LocalsPlusArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugPropertyEnumType_All methods ***/
#define IDebugPropertyEnumType_LocalsPlusArgs_GetName(This,a) (This)->lpVtbl->GetName(This,a)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugPropertyEnumType_LocalsPlusArgs_QueryInterface(IDebugPropertyEnumType_LocalsPlusArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugPropertyEnumType_LocalsPlusArgs_AddRef(IDebugPropertyEnumType_LocalsPlusArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugPropertyEnumType_LocalsPlusArgs_Release(IDebugPropertyEnumType_LocalsPlusArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugPropertyEnumType_All methods ***/
static inline HRESULT IDebugPropertyEnumType_LocalsPlusArgs_GetName(IDebugPropertyEnumType_LocalsPlusArgs* This,BSTR *a) {
    return This->lpVtbl->GetName(This,a);
}
#endif
#endif

#endif


#endif  /* __IDebugPropertyEnumType_LocalsPlusArgs_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IDebugPropertyEnumType_Registers interface
 */
#ifndef __IDebugPropertyEnumType_Registers_INTERFACE_DEFINED__
#define __IDebugPropertyEnumType_Registers_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDebugPropertyEnumType_Registers, 0x51973c59, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51973c59-cb0c-11d0-b5c9-00a0244a0e7a")
IDebugPropertyEnumType_Registers : public IDebugPropertyEnumType_All
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDebugPropertyEnumType_Registers, 0x51973c59, 0xcb0c, 0x11d0, 0xb5,0xc9, 0x00,0xa0,0x24,0x4a,0x0e,0x7a)
#endif
#else
typedef struct IDebugPropertyEnumType_RegistersVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDebugPropertyEnumType_Registers *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDebugPropertyEnumType_Registers *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDebugPropertyEnumType_Registers *This);

    /*** IDebugPropertyEnumType_All methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        IDebugPropertyEnumType_Registers *This,
        BSTR *a);

    END_INTERFACE
} IDebugPropertyEnumType_RegistersVtbl;

interface IDebugPropertyEnumType_Registers {
    CONST_VTBL IDebugPropertyEnumType_RegistersVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDebugPropertyEnumType_Registers_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDebugPropertyEnumType_Registers_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDebugPropertyEnumType_Registers_Release(This) (This)->lpVtbl->Release(This)
/*** IDebugPropertyEnumType_All methods ***/
#define IDebugPropertyEnumType_Registers_GetName(This,a) (This)->lpVtbl->GetName(This,a)
#else
/*** IUnknown methods ***/
static inline HRESULT IDebugPropertyEnumType_Registers_QueryInterface(IDebugPropertyEnumType_Registers* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDebugPropertyEnumType_Registers_AddRef(IDebugPropertyEnumType_Registers* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDebugPropertyEnumType_Registers_Release(IDebugPropertyEnumType_Registers* This) {
    return This->lpVtbl->Release(This);
}
/*** IDebugPropertyEnumType_All methods ***/
static inline HRESULT IDebugPropertyEnumType_Registers_GetName(IDebugPropertyEnumType_Registers* This,BSTR *a) {
    return This->lpVtbl->GetName(This,a);
}
#endif
#endif

#endif


#endif  /* __IDebugPropertyEnumType_Registers_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dbgprop_h__ */
