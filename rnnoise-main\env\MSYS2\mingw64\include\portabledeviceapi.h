/*** Autogenerated by WIDL 10.12 from include/portabledeviceapi.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __portabledeviceapi_h__
#define __portabledeviceapi_h__

/* Forward declarations */

#ifndef __IPortableDeviceManager_FWD_DEFINED__
#define __IPortableDeviceManager_FWD_DEFINED__
typedef interface IPortableDeviceManager IPortableDeviceManager;
#ifdef __cplusplus
interface IPortableDeviceManager;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDevice_FWD_DEFINED__
#define __IPortableDevice_FWD_DEFINED__
typedef interface IPortableDevice IPortableDevice;
#ifdef __cplusplus
interface IPortableDevice;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceContent_FWD_DEFINED__
#define __IPortableDeviceContent_FWD_DEFINED__
typedef interface IPortableDeviceContent IPortableDeviceContent;
#ifdef __cplusplus
interface IPortableDeviceContent;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceContent2_FWD_DEFINED__
#define __IPortableDeviceContent2_FWD_DEFINED__
typedef interface IPortableDeviceContent2 IPortableDeviceContent2;
#ifdef __cplusplus
interface IPortableDeviceContent2;
#endif /* __cplusplus */
#endif

#ifndef __IEnumPortableDeviceObjectIDs_FWD_DEFINED__
#define __IEnumPortableDeviceObjectIDs_FWD_DEFINED__
typedef interface IEnumPortableDeviceObjectIDs IEnumPortableDeviceObjectIDs;
#ifdef __cplusplus
interface IEnumPortableDeviceObjectIDs;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceProperties_FWD_DEFINED__
#define __IPortableDeviceProperties_FWD_DEFINED__
typedef interface IPortableDeviceProperties IPortableDeviceProperties;
#ifdef __cplusplus
interface IPortableDeviceProperties;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceResources_FWD_DEFINED__
#define __IPortableDeviceResources_FWD_DEFINED__
typedef interface IPortableDeviceResources IPortableDeviceResources;
#ifdef __cplusplus
interface IPortableDeviceResources;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceCapabilities_FWD_DEFINED__
#define __IPortableDeviceCapabilities_FWD_DEFINED__
typedef interface IPortableDeviceCapabilities IPortableDeviceCapabilities;
#ifdef __cplusplus
interface IPortableDeviceCapabilities;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceEventCallback_FWD_DEFINED__
#define __IPortableDeviceEventCallback_FWD_DEFINED__
typedef interface IPortableDeviceEventCallback IPortableDeviceEventCallback;
#ifdef __cplusplus
interface IPortableDeviceEventCallback;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceDataStream_FWD_DEFINED__
#define __IPortableDeviceDataStream_FWD_DEFINED__
typedef interface IPortableDeviceDataStream IPortableDeviceDataStream;
#ifdef __cplusplus
interface IPortableDeviceDataStream;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceUnitsStream_FWD_DEFINED__
#define __IPortableDeviceUnitsStream_FWD_DEFINED__
typedef interface IPortableDeviceUnitsStream IPortableDeviceUnitsStream;
#ifdef __cplusplus
interface IPortableDeviceUnitsStream;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDevicePropertiesBulk_FWD_DEFINED__
#define __IPortableDevicePropertiesBulk_FWD_DEFINED__
typedef interface IPortableDevicePropertiesBulk IPortableDevicePropertiesBulk;
#ifdef __cplusplus
interface IPortableDevicePropertiesBulk;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDevicePropertiesBulkCallback_FWD_DEFINED__
#define __IPortableDevicePropertiesBulkCallback_FWD_DEFINED__
typedef interface IPortableDevicePropertiesBulkCallback IPortableDevicePropertiesBulkCallback;
#ifdef __cplusplus
interface IPortableDevicePropertiesBulkCallback;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceManager_FWD_DEFINED__
#define __IPortableDeviceServiceManager_FWD_DEFINED__
typedef interface IPortableDeviceServiceManager IPortableDeviceServiceManager;
#ifdef __cplusplus
interface IPortableDeviceServiceManager;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceService_FWD_DEFINED__
#define __IPortableDeviceService_FWD_DEFINED__
typedef interface IPortableDeviceService IPortableDeviceService;
#ifdef __cplusplus
interface IPortableDeviceService;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceCapabilities_FWD_DEFINED__
#define __IPortableDeviceServiceCapabilities_FWD_DEFINED__
typedef interface IPortableDeviceServiceCapabilities IPortableDeviceServiceCapabilities;
#ifdef __cplusplus
interface IPortableDeviceServiceCapabilities;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceMethods_FWD_DEFINED__
#define __IPortableDeviceServiceMethods_FWD_DEFINED__
typedef interface IPortableDeviceServiceMethods IPortableDeviceServiceMethods;
#ifdef __cplusplus
interface IPortableDeviceServiceMethods;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceMethodCallback_FWD_DEFINED__
#define __IPortableDeviceServiceMethodCallback_FWD_DEFINED__
typedef interface IPortableDeviceServiceMethodCallback IPortableDeviceServiceMethodCallback;
#ifdef __cplusplus
interface IPortableDeviceServiceMethodCallback;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceActivation_FWD_DEFINED__
#define __IPortableDeviceServiceActivation_FWD_DEFINED__
typedef interface IPortableDeviceServiceActivation IPortableDeviceServiceActivation;
#ifdef __cplusplus
interface IPortableDeviceServiceActivation;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceOpenCallback_FWD_DEFINED__
#define __IPortableDeviceServiceOpenCallback_FWD_DEFINED__
typedef interface IPortableDeviceServiceOpenCallback IPortableDeviceServiceOpenCallback;
#ifdef __cplusplus
interface IPortableDeviceServiceOpenCallback;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceDispatchFactory_FWD_DEFINED__
#define __IPortableDeviceDispatchFactory_FWD_DEFINED__
typedef interface IPortableDeviceDispatchFactory IPortableDeviceDispatchFactory;
#ifdef __cplusplus
interface IPortableDeviceDispatchFactory;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceWebControl_FWD_DEFINED__
#define __IPortableDeviceWebControl_FWD_DEFINED__
typedef interface IPortableDeviceWebControl IPortableDeviceWebControl;
#ifdef __cplusplus
interface IPortableDeviceWebControl;
#endif /* __cplusplus */
#endif

#ifndef __PortableDevice_FWD_DEFINED__
#define __PortableDevice_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDevice PortableDevice;
#else
typedef struct PortableDevice PortableDevice;
#endif /* defined __cplusplus */
#endif /* defined __PortableDevice_FWD_DEFINED__ */

#ifndef __PortableDeviceManager_FWD_DEFINED__
#define __PortableDeviceManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDeviceManager PortableDeviceManager;
#else
typedef struct PortableDeviceManager PortableDeviceManager;
#endif /* defined __cplusplus */
#endif /* defined __PortableDeviceManager_FWD_DEFINED__ */

#ifndef __PortableDeviceService_FWD_DEFINED__
#define __PortableDeviceService_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDeviceService PortableDeviceService;
#else
typedef struct PortableDeviceService PortableDeviceService;
#endif /* defined __cplusplus */
#endif /* defined __PortableDeviceService_FWD_DEFINED__ */

#ifndef __PortableDeviceDispatchFactory_FWD_DEFINED__
#define __PortableDeviceDispatchFactory_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDeviceDispatchFactory PortableDeviceDispatchFactory;
#else
typedef struct PortableDeviceDispatchFactory PortableDeviceDispatchFactory;
#endif /* defined __cplusplus */
#endif /* defined __PortableDeviceDispatchFactory_FWD_DEFINED__ */

#ifndef __PortableDeviceFTM_FWD_DEFINED__
#define __PortableDeviceFTM_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDeviceFTM PortableDeviceFTM;
#else
typedef struct PortableDeviceFTM PortableDeviceFTM;
#endif /* defined __cplusplus */
#endif /* defined __PortableDeviceFTM_FWD_DEFINED__ */

#ifndef __PortableDeviceServiceFTM_FWD_DEFINED__
#define __PortableDeviceServiceFTM_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDeviceServiceFTM PortableDeviceServiceFTM;
#else
typedef struct PortableDeviceServiceFTM PortableDeviceServiceFTM;
#endif /* defined __cplusplus */
#endif /* defined __PortableDeviceServiceFTM_FWD_DEFINED__ */

#ifndef __PortableDeviceWebControl_FWD_DEFINED__
#define __PortableDeviceWebControl_FWD_DEFINED__
#ifdef __cplusplus
typedef class PortableDeviceWebControl PortableDeviceWebControl;
#else
typedef struct PortableDeviceWebControl PortableDeviceWebControl;
#endif /* defined __cplusplus */
#endif /* defined __PortableDeviceWebControl_FWD_DEFINED__ */

/* Headers for imported files */

#include <propidl.h>
#include <wtypes.h>
#include <portabledevicetypes.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __IPortableDeviceManager_FWD_DEFINED__
#define __IPortableDeviceManager_FWD_DEFINED__
typedef interface IPortableDeviceManager IPortableDeviceManager;
#ifdef __cplusplus
interface IPortableDeviceManager;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDevice_FWD_DEFINED__
#define __IPortableDevice_FWD_DEFINED__
typedef interface IPortableDevice IPortableDevice;
#ifdef __cplusplus
interface IPortableDevice;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceContent_FWD_DEFINED__
#define __IPortableDeviceContent_FWD_DEFINED__
typedef interface IPortableDeviceContent IPortableDeviceContent;
#ifdef __cplusplus
interface IPortableDeviceContent;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceContent2_FWD_DEFINED__
#define __IPortableDeviceContent2_FWD_DEFINED__
typedef interface IPortableDeviceContent2 IPortableDeviceContent2;
#ifdef __cplusplus
interface IPortableDeviceContent2;
#endif /* __cplusplus */
#endif

#ifndef __IEnumPortableDeviceObjectIDs_FWD_DEFINED__
#define __IEnumPortableDeviceObjectIDs_FWD_DEFINED__
typedef interface IEnumPortableDeviceObjectIDs IEnumPortableDeviceObjectIDs;
#ifdef __cplusplus
interface IEnumPortableDeviceObjectIDs;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceProperties_FWD_DEFINED__
#define __IPortableDeviceProperties_FWD_DEFINED__
typedef interface IPortableDeviceProperties IPortableDeviceProperties;
#ifdef __cplusplus
interface IPortableDeviceProperties;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceResources_FWD_DEFINED__
#define __IPortableDeviceResources_FWD_DEFINED__
typedef interface IPortableDeviceResources IPortableDeviceResources;
#ifdef __cplusplus
interface IPortableDeviceResources;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceCapabilities_FWD_DEFINED__
#define __IPortableDeviceCapabilities_FWD_DEFINED__
typedef interface IPortableDeviceCapabilities IPortableDeviceCapabilities;
#ifdef __cplusplus
interface IPortableDeviceCapabilities;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceEventCallback_FWD_DEFINED__
#define __IPortableDeviceEventCallback_FWD_DEFINED__
typedef interface IPortableDeviceEventCallback IPortableDeviceEventCallback;
#ifdef __cplusplus
interface IPortableDeviceEventCallback;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceDataStream_FWD_DEFINED__
#define __IPortableDeviceDataStream_FWD_DEFINED__
typedef interface IPortableDeviceDataStream IPortableDeviceDataStream;
#ifdef __cplusplus
interface IPortableDeviceDataStream;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDevicePropertiesBulk_FWD_DEFINED__
#define __IPortableDevicePropertiesBulk_FWD_DEFINED__
typedef interface IPortableDevicePropertiesBulk IPortableDevicePropertiesBulk;
#ifdef __cplusplus
interface IPortableDevicePropertiesBulk;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDevicePropertiesBulkCallback_FWD_DEFINED__
#define __IPortableDevicePropertiesBulkCallback_FWD_DEFINED__
typedef interface IPortableDevicePropertiesBulkCallback IPortableDevicePropertiesBulkCallback;
#ifdef __cplusplus
interface IPortableDevicePropertiesBulkCallback;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceManager_FWD_DEFINED__
#define __IPortableDeviceServiceManager_FWD_DEFINED__
typedef interface IPortableDeviceServiceManager IPortableDeviceServiceManager;
#ifdef __cplusplus
interface IPortableDeviceServiceManager;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceService_FWD_DEFINED__
#define __IPortableDeviceService_FWD_DEFINED__
typedef interface IPortableDeviceService IPortableDeviceService;
#ifdef __cplusplus
interface IPortableDeviceService;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceCapabilities_FWD_DEFINED__
#define __IPortableDeviceServiceCapabilities_FWD_DEFINED__
typedef interface IPortableDeviceServiceCapabilities IPortableDeviceServiceCapabilities;
#ifdef __cplusplus
interface IPortableDeviceServiceCapabilities;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceMethods_FWD_DEFINED__
#define __IPortableDeviceServiceMethods_FWD_DEFINED__
typedef interface IPortableDeviceServiceMethods IPortableDeviceServiceMethods;
#ifdef __cplusplus
interface IPortableDeviceServiceMethods;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceMethodCallback_FWD_DEFINED__
#define __IPortableDeviceServiceMethodCallback_FWD_DEFINED__
typedef interface IPortableDeviceServiceMethodCallback IPortableDeviceServiceMethodCallback;
#ifdef __cplusplus
interface IPortableDeviceServiceMethodCallback;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceDispatchFactory_FWD_DEFINED__
#define __IPortableDeviceDispatchFactory_FWD_DEFINED__
typedef interface IPortableDeviceDispatchFactory IPortableDeviceDispatchFactory;
#ifdef __cplusplus
interface IPortableDeviceDispatchFactory;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceActivation_FWD_DEFINED__
#define __IPortableDeviceServiceActivation_FWD_DEFINED__
typedef interface IPortableDeviceServiceActivation IPortableDeviceServiceActivation;
#ifdef __cplusplus
interface IPortableDeviceServiceActivation;
#endif /* __cplusplus */
#endif

#ifndef __IPortableDeviceServiceOpenCallback_FWD_DEFINED__
#define __IPortableDeviceServiceOpenCallback_FWD_DEFINED__
typedef interface IPortableDeviceServiceOpenCallback IPortableDeviceServiceOpenCallback;
#ifdef __cplusplus
interface IPortableDeviceServiceOpenCallback;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IPortableDeviceManager interface
 */
#ifndef __IPortableDeviceManager_INTERFACE_DEFINED__
#define __IPortableDeviceManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceManager, 0xa1567595, 0x4c2f, 0x4574, 0xa6,0xfa, 0xec,0xef,0x91,0x7b,0x9a,0x40);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a1567595-4c2f-4574-a6fa-ecef917b9a40")
IPortableDeviceManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDevices(
        LPWSTR *device_ids,
        DWORD *device_ids_count) = 0;

    virtual HRESULT STDMETHODCALLTYPE RefreshDeviceList(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceFriendlyName(
        LPCWSTR device_id,
        WCHAR *device_friendly_name,
        DWORD *device_friendly_name_size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceDescription(
        LPCWSTR device_id,
        WCHAR *device_description,
        DWORD *device_description_size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceManufacturer(
        LPCWSTR device_id,
        WCHAR *device_manufacturer,
        DWORD *device_manufacturer_size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceProperty(
        LPCWSTR device_id,
        LPCWSTR device_property_name,
        BYTE *data,
        DWORD *data_size,
        DWORD *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPrivateDevices(
        LPWSTR *device_ids,
        DWORD *device_ids_count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceManager, 0xa1567595, 0x4c2f, 0x4574, 0xa6,0xfa, 0xec,0xef,0x91,0x7b,0x9a,0x40)
#endif
#else
typedef struct IPortableDeviceManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceManager *This);

    /*** IPortableDeviceManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevices)(
        IPortableDeviceManager *This,
        LPWSTR *device_ids,
        DWORD *device_ids_count);

    HRESULT (STDMETHODCALLTYPE *RefreshDeviceList)(
        IPortableDeviceManager *This);

    HRESULT (STDMETHODCALLTYPE *GetDeviceFriendlyName)(
        IPortableDeviceManager *This,
        LPCWSTR device_id,
        WCHAR *device_friendly_name,
        DWORD *device_friendly_name_size);

    HRESULT (STDMETHODCALLTYPE *GetDeviceDescription)(
        IPortableDeviceManager *This,
        LPCWSTR device_id,
        WCHAR *device_description,
        DWORD *device_description_size);

    HRESULT (STDMETHODCALLTYPE *GetDeviceManufacturer)(
        IPortableDeviceManager *This,
        LPCWSTR device_id,
        WCHAR *device_manufacturer,
        DWORD *device_manufacturer_size);

    HRESULT (STDMETHODCALLTYPE *GetDeviceProperty)(
        IPortableDeviceManager *This,
        LPCWSTR device_id,
        LPCWSTR device_property_name,
        BYTE *data,
        DWORD *data_size,
        DWORD *type);

    HRESULT (STDMETHODCALLTYPE *GetPrivateDevices)(
        IPortableDeviceManager *This,
        LPWSTR *device_ids,
        DWORD *device_ids_count);

    END_INTERFACE
} IPortableDeviceManagerVtbl;

interface IPortableDeviceManager {
    CONST_VTBL IPortableDeviceManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceManager_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceManager methods ***/
#define IPortableDeviceManager_GetDevices(This,device_ids,device_ids_count) (This)->lpVtbl->GetDevices(This,device_ids,device_ids_count)
#define IPortableDeviceManager_RefreshDeviceList(This) (This)->lpVtbl->RefreshDeviceList(This)
#define IPortableDeviceManager_GetDeviceFriendlyName(This,device_id,device_friendly_name,device_friendly_name_size) (This)->lpVtbl->GetDeviceFriendlyName(This,device_id,device_friendly_name,device_friendly_name_size)
#define IPortableDeviceManager_GetDeviceDescription(This,device_id,device_description,device_description_size) (This)->lpVtbl->GetDeviceDescription(This,device_id,device_description,device_description_size)
#define IPortableDeviceManager_GetDeviceManufacturer(This,device_id,device_manufacturer,device_manufacturer_size) (This)->lpVtbl->GetDeviceManufacturer(This,device_id,device_manufacturer,device_manufacturer_size)
#define IPortableDeviceManager_GetDeviceProperty(This,device_id,device_property_name,data,data_size,type) (This)->lpVtbl->GetDeviceProperty(This,device_id,device_property_name,data,data_size,type)
#define IPortableDeviceManager_GetPrivateDevices(This,device_ids,device_ids_count) (This)->lpVtbl->GetPrivateDevices(This,device_ids,device_ids_count)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceManager_QueryInterface(IPortableDeviceManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceManager_AddRef(IPortableDeviceManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceManager_Release(IPortableDeviceManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceManager methods ***/
static inline HRESULT IPortableDeviceManager_GetDevices(IPortableDeviceManager* This,LPWSTR *device_ids,DWORD *device_ids_count) {
    return This->lpVtbl->GetDevices(This,device_ids,device_ids_count);
}
static inline HRESULT IPortableDeviceManager_RefreshDeviceList(IPortableDeviceManager* This) {
    return This->lpVtbl->RefreshDeviceList(This);
}
static inline HRESULT IPortableDeviceManager_GetDeviceFriendlyName(IPortableDeviceManager* This,LPCWSTR device_id,WCHAR *device_friendly_name,DWORD *device_friendly_name_size) {
    return This->lpVtbl->GetDeviceFriendlyName(This,device_id,device_friendly_name,device_friendly_name_size);
}
static inline HRESULT IPortableDeviceManager_GetDeviceDescription(IPortableDeviceManager* This,LPCWSTR device_id,WCHAR *device_description,DWORD *device_description_size) {
    return This->lpVtbl->GetDeviceDescription(This,device_id,device_description,device_description_size);
}
static inline HRESULT IPortableDeviceManager_GetDeviceManufacturer(IPortableDeviceManager* This,LPCWSTR device_id,WCHAR *device_manufacturer,DWORD *device_manufacturer_size) {
    return This->lpVtbl->GetDeviceManufacturer(This,device_id,device_manufacturer,device_manufacturer_size);
}
static inline HRESULT IPortableDeviceManager_GetDeviceProperty(IPortableDeviceManager* This,LPCWSTR device_id,LPCWSTR device_property_name,BYTE *data,DWORD *data_size,DWORD *type) {
    return This->lpVtbl->GetDeviceProperty(This,device_id,device_property_name,data,data_size,type);
}
static inline HRESULT IPortableDeviceManager_GetPrivateDevices(IPortableDeviceManager* This,LPWSTR *device_ids,DWORD *device_ids_count) {
    return This->lpVtbl->GetPrivateDevices(This,device_ids,device_ids_count);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDevice interface
 */
#ifndef __IPortableDevice_INTERFACE_DEFINED__
#define __IPortableDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDevice, 0x625e2df8, 0x6392, 0x4cf0, 0x9a,0xd1, 0x3c,0xfa,0x5f,0x17,0x77,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("625e2df8-6392-4cf0-9ad1-3cfa5f17775c")
IPortableDevice : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Open(
        LPCWSTR device_id,
        IPortableDeviceValues *client_info) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendCommand(
        const DWORD flags,
        IPortableDeviceValues *parameters,
        IPortableDeviceValues **results) = 0;

    virtual HRESULT STDMETHODCALLTYPE Content(
        IPortableDeviceContent **content) = 0;

    virtual HRESULT STDMETHODCALLTYPE Capabilities(
        IPortableDeviceCapabilities **capabilities) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Advise(
        const DWORD flags,
        IPortableDeviceEventCallback *callback,
        IPortableDeviceValues *parameters,
        LPWSTR *cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unadvise(
        LPCWSTR cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPnPDeviceID(
        LPWSTR *device_id) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDevice, 0x625e2df8, 0x6392, 0x4cf0, 0x9a,0xd1, 0x3c,0xfa,0x5f,0x17,0x77,0x5c)
#endif
#else
typedef struct IPortableDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDevice *This);

    /*** IPortableDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *Open)(
        IPortableDevice *This,
        LPCWSTR device_id,
        IPortableDeviceValues *client_info);

    HRESULT (STDMETHODCALLTYPE *SendCommand)(
        IPortableDevice *This,
        const DWORD flags,
        IPortableDeviceValues *parameters,
        IPortableDeviceValues **results);

    HRESULT (STDMETHODCALLTYPE *Content)(
        IPortableDevice *This,
        IPortableDeviceContent **content);

    HRESULT (STDMETHODCALLTYPE *Capabilities)(
        IPortableDevice *This,
        IPortableDeviceCapabilities **capabilities);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDevice *This);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IPortableDevice *This);

    HRESULT (STDMETHODCALLTYPE *Advise)(
        IPortableDevice *This,
        const DWORD flags,
        IPortableDeviceEventCallback *callback,
        IPortableDeviceValues *parameters,
        LPWSTR *cookie);

    HRESULT (STDMETHODCALLTYPE *Unadvise)(
        IPortableDevice *This,
        LPCWSTR cookie);

    HRESULT (STDMETHODCALLTYPE *GetPnPDeviceID)(
        IPortableDevice *This,
        LPWSTR *device_id);

    END_INTERFACE
} IPortableDeviceVtbl;

interface IPortableDevice {
    CONST_VTBL IPortableDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDevice methods ***/
#define IPortableDevice_Open(This,device_id,client_info) (This)->lpVtbl->Open(This,device_id,client_info)
#define IPortableDevice_SendCommand(This,flags,parameters,results) (This)->lpVtbl->SendCommand(This,flags,parameters,results)
#define IPortableDevice_Content(This,content) (This)->lpVtbl->Content(This,content)
#define IPortableDevice_Capabilities(This,capabilities) (This)->lpVtbl->Capabilities(This,capabilities)
#define IPortableDevice_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IPortableDevice_Close(This) (This)->lpVtbl->Close(This)
#define IPortableDevice_Advise(This,flags,callback,parameters,cookie) (This)->lpVtbl->Advise(This,flags,callback,parameters,cookie)
#define IPortableDevice_Unadvise(This,cookie) (This)->lpVtbl->Unadvise(This,cookie)
#define IPortableDevice_GetPnPDeviceID(This,device_id) (This)->lpVtbl->GetPnPDeviceID(This,device_id)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDevice_QueryInterface(IPortableDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDevice_AddRef(IPortableDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDevice_Release(IPortableDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDevice methods ***/
static inline HRESULT IPortableDevice_Open(IPortableDevice* This,LPCWSTR device_id,IPortableDeviceValues *client_info) {
    return This->lpVtbl->Open(This,device_id,client_info);
}
static inline HRESULT IPortableDevice_SendCommand(IPortableDevice* This,const DWORD flags,IPortableDeviceValues *parameters,IPortableDeviceValues **results) {
    return This->lpVtbl->SendCommand(This,flags,parameters,results);
}
static inline HRESULT IPortableDevice_Content(IPortableDevice* This,IPortableDeviceContent **content) {
    return This->lpVtbl->Content(This,content);
}
static inline HRESULT IPortableDevice_Capabilities(IPortableDevice* This,IPortableDeviceCapabilities **capabilities) {
    return This->lpVtbl->Capabilities(This,capabilities);
}
static inline HRESULT IPortableDevice_Cancel(IPortableDevice* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT IPortableDevice_Close(IPortableDevice* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IPortableDevice_Advise(IPortableDevice* This,const DWORD flags,IPortableDeviceEventCallback *callback,IPortableDeviceValues *parameters,LPWSTR *cookie) {
    return This->lpVtbl->Advise(This,flags,callback,parameters,cookie);
}
static inline HRESULT IPortableDevice_Unadvise(IPortableDevice* This,LPCWSTR cookie) {
    return This->lpVtbl->Unadvise(This,cookie);
}
static inline HRESULT IPortableDevice_GetPnPDeviceID(IPortableDevice* This,LPWSTR *device_id) {
    return This->lpVtbl->GetPnPDeviceID(This,device_id);
}
#endif
#endif

#endif


#endif  /* __IPortableDevice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceContent interface
 */
#ifndef __IPortableDeviceContent_INTERFACE_DEFINED__
#define __IPortableDeviceContent_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceContent, 0x6a96ed84, 0x7c73, 0x4480, 0x99,0x38, 0xbf,0x5a,0xf4,0x77,0xd4,0x26);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6a96ed84-7c73-4480-9938-bf5af477d426")
IPortableDeviceContent : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EnumObjects(
        const DWORD flags,
        LPCWSTR parent_object_id,
        IPortableDeviceValues *filter,
        IEnumPortableDeviceObjectIDs **enum_device_object) = 0;

    virtual HRESULT STDMETHODCALLTYPE Properties(
        IPortableDeviceProperties **device_properties) = 0;

    virtual HRESULT STDMETHODCALLTYPE Transfer(
        IPortableDeviceResources **resources) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateObjectWithPropertiesOnly(
        IPortableDeviceValues *values,
        LPWSTR *object_id) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateObjectWithPropertiesAndData(
        IPortableDeviceValues *values,
        IStream **data,
        DWORD *optimal_write_buffer_size,
        LPWSTR *cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        const DWORD options,
        IPortableDevicePropVariantCollection *object_ids,
        IPortableDevicePropVariantCollection **results) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObjectIDsFromPersistentUniqueIDs(
        IPortableDevicePropVariantCollection *persistent_unique_ids,
        IPortableDevicePropVariantCollection **object_ids) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Move(
        IPortableDevicePropVariantCollection *object_ids,
        LPCWSTR destination_folder_object_id,
        IPortableDevicePropVariantCollection **results) = 0;

    virtual HRESULT STDMETHODCALLTYPE Copy(
        IPortableDevicePropVariantCollection *object_ids,
        LPCWSTR destination_folder_object_id,
        IPortableDevicePropVariantCollection **results) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceContent, 0x6a96ed84, 0x7c73, 0x4480, 0x99,0x38, 0xbf,0x5a,0xf4,0x77,0xd4,0x26)
#endif
#else
typedef struct IPortableDeviceContentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceContent *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceContent *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceContent *This);

    /*** IPortableDeviceContent methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumObjects)(
        IPortableDeviceContent *This,
        const DWORD flags,
        LPCWSTR parent_object_id,
        IPortableDeviceValues *filter,
        IEnumPortableDeviceObjectIDs **enum_device_object);

    HRESULT (STDMETHODCALLTYPE *Properties)(
        IPortableDeviceContent *This,
        IPortableDeviceProperties **device_properties);

    HRESULT (STDMETHODCALLTYPE *Transfer)(
        IPortableDeviceContent *This,
        IPortableDeviceResources **resources);

    HRESULT (STDMETHODCALLTYPE *CreateObjectWithPropertiesOnly)(
        IPortableDeviceContent *This,
        IPortableDeviceValues *values,
        LPWSTR *object_id);

    HRESULT (STDMETHODCALLTYPE *CreateObjectWithPropertiesAndData)(
        IPortableDeviceContent *This,
        IPortableDeviceValues *values,
        IStream **data,
        DWORD *optimal_write_buffer_size,
        LPWSTR *cookie);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IPortableDeviceContent *This,
        const DWORD options,
        IPortableDevicePropVariantCollection *object_ids,
        IPortableDevicePropVariantCollection **results);

    HRESULT (STDMETHODCALLTYPE *GetObjectIDsFromPersistentUniqueIDs)(
        IPortableDeviceContent *This,
        IPortableDevicePropVariantCollection *persistent_unique_ids,
        IPortableDevicePropVariantCollection **object_ids);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDeviceContent *This);

    HRESULT (STDMETHODCALLTYPE *Move)(
        IPortableDeviceContent *This,
        IPortableDevicePropVariantCollection *object_ids,
        LPCWSTR destination_folder_object_id,
        IPortableDevicePropVariantCollection **results);

    HRESULT (STDMETHODCALLTYPE *Copy)(
        IPortableDeviceContent *This,
        IPortableDevicePropVariantCollection *object_ids,
        LPCWSTR destination_folder_object_id,
        IPortableDevicePropVariantCollection **results);

    END_INTERFACE
} IPortableDeviceContentVtbl;

interface IPortableDeviceContent {
    CONST_VTBL IPortableDeviceContentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceContent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceContent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceContent_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceContent methods ***/
#define IPortableDeviceContent_EnumObjects(This,flags,parent_object_id,filter,enum_device_object) (This)->lpVtbl->EnumObjects(This,flags,parent_object_id,filter,enum_device_object)
#define IPortableDeviceContent_Properties(This,device_properties) (This)->lpVtbl->Properties(This,device_properties)
#define IPortableDeviceContent_Transfer(This,resources) (This)->lpVtbl->Transfer(This,resources)
#define IPortableDeviceContent_CreateObjectWithPropertiesOnly(This,values,object_id) (This)->lpVtbl->CreateObjectWithPropertiesOnly(This,values,object_id)
#define IPortableDeviceContent_CreateObjectWithPropertiesAndData(This,values,data,optimal_write_buffer_size,cookie) (This)->lpVtbl->CreateObjectWithPropertiesAndData(This,values,data,optimal_write_buffer_size,cookie)
#define IPortableDeviceContent_Delete(This,options,object_ids,results) (This)->lpVtbl->Delete(This,options,object_ids,results)
#define IPortableDeviceContent_GetObjectIDsFromPersistentUniqueIDs(This,persistent_unique_ids,object_ids) (This)->lpVtbl->GetObjectIDsFromPersistentUniqueIDs(This,persistent_unique_ids,object_ids)
#define IPortableDeviceContent_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IPortableDeviceContent_Move(This,object_ids,destination_folder_object_id,results) (This)->lpVtbl->Move(This,object_ids,destination_folder_object_id,results)
#define IPortableDeviceContent_Copy(This,object_ids,destination_folder_object_id,results) (This)->lpVtbl->Copy(This,object_ids,destination_folder_object_id,results)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceContent_QueryInterface(IPortableDeviceContent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceContent_AddRef(IPortableDeviceContent* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceContent_Release(IPortableDeviceContent* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceContent methods ***/
static inline HRESULT IPortableDeviceContent_EnumObjects(IPortableDeviceContent* This,const DWORD flags,LPCWSTR parent_object_id,IPortableDeviceValues *filter,IEnumPortableDeviceObjectIDs **enum_device_object) {
    return This->lpVtbl->EnumObjects(This,flags,parent_object_id,filter,enum_device_object);
}
static inline HRESULT IPortableDeviceContent_Properties(IPortableDeviceContent* This,IPortableDeviceProperties **device_properties) {
    return This->lpVtbl->Properties(This,device_properties);
}
static inline HRESULT IPortableDeviceContent_Transfer(IPortableDeviceContent* This,IPortableDeviceResources **resources) {
    return This->lpVtbl->Transfer(This,resources);
}
static inline HRESULT IPortableDeviceContent_CreateObjectWithPropertiesOnly(IPortableDeviceContent* This,IPortableDeviceValues *values,LPWSTR *object_id) {
    return This->lpVtbl->CreateObjectWithPropertiesOnly(This,values,object_id);
}
static inline HRESULT IPortableDeviceContent_CreateObjectWithPropertiesAndData(IPortableDeviceContent* This,IPortableDeviceValues *values,IStream **data,DWORD *optimal_write_buffer_size,LPWSTR *cookie) {
    return This->lpVtbl->CreateObjectWithPropertiesAndData(This,values,data,optimal_write_buffer_size,cookie);
}
static inline HRESULT IPortableDeviceContent_Delete(IPortableDeviceContent* This,const DWORD options,IPortableDevicePropVariantCollection *object_ids,IPortableDevicePropVariantCollection **results) {
    return This->lpVtbl->Delete(This,options,object_ids,results);
}
static inline HRESULT IPortableDeviceContent_GetObjectIDsFromPersistentUniqueIDs(IPortableDeviceContent* This,IPortableDevicePropVariantCollection *persistent_unique_ids,IPortableDevicePropVariantCollection **object_ids) {
    return This->lpVtbl->GetObjectIDsFromPersistentUniqueIDs(This,persistent_unique_ids,object_ids);
}
static inline HRESULT IPortableDeviceContent_Cancel(IPortableDeviceContent* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT IPortableDeviceContent_Move(IPortableDeviceContent* This,IPortableDevicePropVariantCollection *object_ids,LPCWSTR destination_folder_object_id,IPortableDevicePropVariantCollection **results) {
    return This->lpVtbl->Move(This,object_ids,destination_folder_object_id,results);
}
static inline HRESULT IPortableDeviceContent_Copy(IPortableDeviceContent* This,IPortableDevicePropVariantCollection *object_ids,LPCWSTR destination_folder_object_id,IPortableDevicePropVariantCollection **results) {
    return This->lpVtbl->Copy(This,object_ids,destination_folder_object_id,results);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceContent_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceContent2 interface
 */
#ifndef __IPortableDeviceContent2_INTERFACE_DEFINED__
#define __IPortableDeviceContent2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceContent2, 0x9b4add96, 0xf6bf, 0x4034, 0x87,0x08, 0xec,0xa7,0x2b,0xf1,0x05,0x54);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9b4add96-f6bf-4034-8708-eca72bf10554")
IPortableDeviceContent2 : public IPortableDeviceContent
{
    virtual HRESULT STDMETHODCALLTYPE UpdateObjectWithPropertiesAndData(
        LPCWSTR object_id,
        IPortableDeviceValues *device_properties,
        IStream **data,
        DWORD *optimal_write_buffer_size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceContent2, 0x9b4add96, 0xf6bf, 0x4034, 0x87,0x08, 0xec,0xa7,0x2b,0xf1,0x05,0x54)
#endif
#else
typedef struct IPortableDeviceContent2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceContent2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceContent2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceContent2 *This);

    /*** IPortableDeviceContent methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumObjects)(
        IPortableDeviceContent2 *This,
        const DWORD flags,
        LPCWSTR parent_object_id,
        IPortableDeviceValues *filter,
        IEnumPortableDeviceObjectIDs **enum_device_object);

    HRESULT (STDMETHODCALLTYPE *Properties)(
        IPortableDeviceContent2 *This,
        IPortableDeviceProperties **device_properties);

    HRESULT (STDMETHODCALLTYPE *Transfer)(
        IPortableDeviceContent2 *This,
        IPortableDeviceResources **resources);

    HRESULT (STDMETHODCALLTYPE *CreateObjectWithPropertiesOnly)(
        IPortableDeviceContent2 *This,
        IPortableDeviceValues *values,
        LPWSTR *object_id);

    HRESULT (STDMETHODCALLTYPE *CreateObjectWithPropertiesAndData)(
        IPortableDeviceContent2 *This,
        IPortableDeviceValues *values,
        IStream **data,
        DWORD *optimal_write_buffer_size,
        LPWSTR *cookie);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IPortableDeviceContent2 *This,
        const DWORD options,
        IPortableDevicePropVariantCollection *object_ids,
        IPortableDevicePropVariantCollection **results);

    HRESULT (STDMETHODCALLTYPE *GetObjectIDsFromPersistentUniqueIDs)(
        IPortableDeviceContent2 *This,
        IPortableDevicePropVariantCollection *persistent_unique_ids,
        IPortableDevicePropVariantCollection **object_ids);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDeviceContent2 *This);

    HRESULT (STDMETHODCALLTYPE *Move)(
        IPortableDeviceContent2 *This,
        IPortableDevicePropVariantCollection *object_ids,
        LPCWSTR destination_folder_object_id,
        IPortableDevicePropVariantCollection **results);

    HRESULT (STDMETHODCALLTYPE *Copy)(
        IPortableDeviceContent2 *This,
        IPortableDevicePropVariantCollection *object_ids,
        LPCWSTR destination_folder_object_id,
        IPortableDevicePropVariantCollection **results);

    /*** IPortableDeviceContent2 methods ***/
    HRESULT (STDMETHODCALLTYPE *UpdateObjectWithPropertiesAndData)(
        IPortableDeviceContent2 *This,
        LPCWSTR object_id,
        IPortableDeviceValues *device_properties,
        IStream **data,
        DWORD *optimal_write_buffer_size);

    END_INTERFACE
} IPortableDeviceContent2Vtbl;

interface IPortableDeviceContent2 {
    CONST_VTBL IPortableDeviceContent2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceContent2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceContent2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceContent2_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceContent methods ***/
#define IPortableDeviceContent2_EnumObjects(This,flags,parent_object_id,filter,enum_device_object) (This)->lpVtbl->EnumObjects(This,flags,parent_object_id,filter,enum_device_object)
#define IPortableDeviceContent2_Properties(This,device_properties) (This)->lpVtbl->Properties(This,device_properties)
#define IPortableDeviceContent2_Transfer(This,resources) (This)->lpVtbl->Transfer(This,resources)
#define IPortableDeviceContent2_CreateObjectWithPropertiesOnly(This,values,object_id) (This)->lpVtbl->CreateObjectWithPropertiesOnly(This,values,object_id)
#define IPortableDeviceContent2_CreateObjectWithPropertiesAndData(This,values,data,optimal_write_buffer_size,cookie) (This)->lpVtbl->CreateObjectWithPropertiesAndData(This,values,data,optimal_write_buffer_size,cookie)
#define IPortableDeviceContent2_Delete(This,options,object_ids,results) (This)->lpVtbl->Delete(This,options,object_ids,results)
#define IPortableDeviceContent2_GetObjectIDsFromPersistentUniqueIDs(This,persistent_unique_ids,object_ids) (This)->lpVtbl->GetObjectIDsFromPersistentUniqueIDs(This,persistent_unique_ids,object_ids)
#define IPortableDeviceContent2_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IPortableDeviceContent2_Move(This,object_ids,destination_folder_object_id,results) (This)->lpVtbl->Move(This,object_ids,destination_folder_object_id,results)
#define IPortableDeviceContent2_Copy(This,object_ids,destination_folder_object_id,results) (This)->lpVtbl->Copy(This,object_ids,destination_folder_object_id,results)
/*** IPortableDeviceContent2 methods ***/
#define IPortableDeviceContent2_UpdateObjectWithPropertiesAndData(This,object_id,device_properties,data,optimal_write_buffer_size) (This)->lpVtbl->UpdateObjectWithPropertiesAndData(This,object_id,device_properties,data,optimal_write_buffer_size)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceContent2_QueryInterface(IPortableDeviceContent2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceContent2_AddRef(IPortableDeviceContent2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceContent2_Release(IPortableDeviceContent2* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceContent methods ***/
static inline HRESULT IPortableDeviceContent2_EnumObjects(IPortableDeviceContent2* This,const DWORD flags,LPCWSTR parent_object_id,IPortableDeviceValues *filter,IEnumPortableDeviceObjectIDs **enum_device_object) {
    return This->lpVtbl->EnumObjects(This,flags,parent_object_id,filter,enum_device_object);
}
static inline HRESULT IPortableDeviceContent2_Properties(IPortableDeviceContent2* This,IPortableDeviceProperties **device_properties) {
    return This->lpVtbl->Properties(This,device_properties);
}
static inline HRESULT IPortableDeviceContent2_Transfer(IPortableDeviceContent2* This,IPortableDeviceResources **resources) {
    return This->lpVtbl->Transfer(This,resources);
}
static inline HRESULT IPortableDeviceContent2_CreateObjectWithPropertiesOnly(IPortableDeviceContent2* This,IPortableDeviceValues *values,LPWSTR *object_id) {
    return This->lpVtbl->CreateObjectWithPropertiesOnly(This,values,object_id);
}
static inline HRESULT IPortableDeviceContent2_CreateObjectWithPropertiesAndData(IPortableDeviceContent2* This,IPortableDeviceValues *values,IStream **data,DWORD *optimal_write_buffer_size,LPWSTR *cookie) {
    return This->lpVtbl->CreateObjectWithPropertiesAndData(This,values,data,optimal_write_buffer_size,cookie);
}
static inline HRESULT IPortableDeviceContent2_Delete(IPortableDeviceContent2* This,const DWORD options,IPortableDevicePropVariantCollection *object_ids,IPortableDevicePropVariantCollection **results) {
    return This->lpVtbl->Delete(This,options,object_ids,results);
}
static inline HRESULT IPortableDeviceContent2_GetObjectIDsFromPersistentUniqueIDs(IPortableDeviceContent2* This,IPortableDevicePropVariantCollection *persistent_unique_ids,IPortableDevicePropVariantCollection **object_ids) {
    return This->lpVtbl->GetObjectIDsFromPersistentUniqueIDs(This,persistent_unique_ids,object_ids);
}
static inline HRESULT IPortableDeviceContent2_Cancel(IPortableDeviceContent2* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT IPortableDeviceContent2_Move(IPortableDeviceContent2* This,IPortableDevicePropVariantCollection *object_ids,LPCWSTR destination_folder_object_id,IPortableDevicePropVariantCollection **results) {
    return This->lpVtbl->Move(This,object_ids,destination_folder_object_id,results);
}
static inline HRESULT IPortableDeviceContent2_Copy(IPortableDeviceContent2* This,IPortableDevicePropVariantCollection *object_ids,LPCWSTR destination_folder_object_id,IPortableDevicePropVariantCollection **results) {
    return This->lpVtbl->Copy(This,object_ids,destination_folder_object_id,results);
}
/*** IPortableDeviceContent2 methods ***/
static inline HRESULT IPortableDeviceContent2_UpdateObjectWithPropertiesAndData(IPortableDeviceContent2* This,LPCWSTR object_id,IPortableDeviceValues *device_properties,IStream **data,DWORD *optimal_write_buffer_size) {
    return This->lpVtbl->UpdateObjectWithPropertiesAndData(This,object_id,device_properties,data,optimal_write_buffer_size);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceContent2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumPortableDeviceObjectIDs interface
 */
#ifndef __IEnumPortableDeviceObjectIDs_INTERFACE_DEFINED__
#define __IEnumPortableDeviceObjectIDs_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumPortableDeviceObjectIDs, 0x10ece955, 0xcf41, 0x4728, 0xbf,0xa0, 0x41,0xee,0xdf,0x1b,0xbf,0x19);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("10ece955-cf41-4728-bfa0-41eedf1bbf19")
IEnumPortableDeviceObjectIDs : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG objects,
        LPWSTR *obj_ids,
        ULONG *fetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG objects) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumPortableDeviceObjectIDs **enum_device_object) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumPortableDeviceObjectIDs, 0x10ece955, 0xcf41, 0x4728, 0xbf,0xa0, 0x41,0xee,0xdf,0x1b,0xbf,0x19)
#endif
#else
typedef struct IEnumPortableDeviceObjectIDsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumPortableDeviceObjectIDs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumPortableDeviceObjectIDs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumPortableDeviceObjectIDs *This);

    /*** IEnumPortableDeviceObjectIDs methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumPortableDeviceObjectIDs *This,
        ULONG objects,
        LPWSTR *obj_ids,
        ULONG *fetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumPortableDeviceObjectIDs *This,
        ULONG objects);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumPortableDeviceObjectIDs *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumPortableDeviceObjectIDs *This,
        IEnumPortableDeviceObjectIDs **enum_device_object);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IEnumPortableDeviceObjectIDs *This);

    END_INTERFACE
} IEnumPortableDeviceObjectIDsVtbl;

interface IEnumPortableDeviceObjectIDs {
    CONST_VTBL IEnumPortableDeviceObjectIDsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumPortableDeviceObjectIDs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumPortableDeviceObjectIDs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumPortableDeviceObjectIDs_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumPortableDeviceObjectIDs methods ***/
#define IEnumPortableDeviceObjectIDs_Next(This,objects,obj_ids,fetched) (This)->lpVtbl->Next(This,objects,obj_ids,fetched)
#define IEnumPortableDeviceObjectIDs_Skip(This,objects) (This)->lpVtbl->Skip(This,objects)
#define IEnumPortableDeviceObjectIDs_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumPortableDeviceObjectIDs_Clone(This,enum_device_object) (This)->lpVtbl->Clone(This,enum_device_object)
#define IEnumPortableDeviceObjectIDs_Cancel(This) (This)->lpVtbl->Cancel(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumPortableDeviceObjectIDs_QueryInterface(IEnumPortableDeviceObjectIDs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumPortableDeviceObjectIDs_AddRef(IEnumPortableDeviceObjectIDs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumPortableDeviceObjectIDs_Release(IEnumPortableDeviceObjectIDs* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumPortableDeviceObjectIDs methods ***/
static inline HRESULT IEnumPortableDeviceObjectIDs_Next(IEnumPortableDeviceObjectIDs* This,ULONG objects,LPWSTR *obj_ids,ULONG *fetched) {
    return This->lpVtbl->Next(This,objects,obj_ids,fetched);
}
static inline HRESULT IEnumPortableDeviceObjectIDs_Skip(IEnumPortableDeviceObjectIDs* This,ULONG objects) {
    return This->lpVtbl->Skip(This,objects);
}
static inline HRESULT IEnumPortableDeviceObjectIDs_Reset(IEnumPortableDeviceObjectIDs* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumPortableDeviceObjectIDs_Clone(IEnumPortableDeviceObjectIDs* This,IEnumPortableDeviceObjectIDs **enum_device_object) {
    return This->lpVtbl->Clone(This,enum_device_object);
}
static inline HRESULT IEnumPortableDeviceObjectIDs_Cancel(IEnumPortableDeviceObjectIDs* This) {
    return This->lpVtbl->Cancel(This);
}
#endif
#endif

#endif


#endif  /* __IEnumPortableDeviceObjectIDs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceProperties interface
 */
#ifndef __IPortableDeviceProperties_INTERFACE_DEFINED__
#define __IPortableDeviceProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceProperties, 0x7f6d695c, 0x03df, 0x4439, 0xa8,0x09, 0x59,0x26,0x6b,0xee,0xe3,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7f6d695c-03df-4439-a809-59266beee3a6")
IPortableDeviceProperties : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSupportedProperties(
        LPCWSTR object_id,
        IPortableDeviceKeyCollection **keys) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyAttributes(
        LPCWSTR object_id,
        REFPROPERTYKEY key,
        IPortableDeviceValues **attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValues(
        LPCWSTR object_id,
        IPortableDeviceKeyCollection *keys,
        IPortableDeviceValues **values) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetValues(
        LPCWSTR object_id,
        IPortableDeviceValues *values,
        IPortableDeviceValues **results) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        LPCWSTR object_id,
        IPortableDeviceKeyCollection *keys) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceProperties, 0x7f6d695c, 0x03df, 0x4439, 0xa8,0x09, 0x59,0x26,0x6b,0xee,0xe3,0xa6)
#endif
#else
typedef struct IPortableDevicePropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceProperties *This);

    /*** IPortableDeviceProperties methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSupportedProperties)(
        IPortableDeviceProperties *This,
        LPCWSTR object_id,
        IPortableDeviceKeyCollection **keys);

    HRESULT (STDMETHODCALLTYPE *GetPropertyAttributes)(
        IPortableDeviceProperties *This,
        LPCWSTR object_id,
        REFPROPERTYKEY key,
        IPortableDeviceValues **attributes);

    HRESULT (STDMETHODCALLTYPE *GetValues)(
        IPortableDeviceProperties *This,
        LPCWSTR object_id,
        IPortableDeviceKeyCollection *keys,
        IPortableDeviceValues **values);

    HRESULT (STDMETHODCALLTYPE *SetValues)(
        IPortableDeviceProperties *This,
        LPCWSTR object_id,
        IPortableDeviceValues *values,
        IPortableDeviceValues **results);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IPortableDeviceProperties *This,
        LPCWSTR object_id,
        IPortableDeviceKeyCollection *keys);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDeviceProperties *This);

    END_INTERFACE
} IPortableDevicePropertiesVtbl;

interface IPortableDeviceProperties {
    CONST_VTBL IPortableDevicePropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceProperties methods ***/
#define IPortableDeviceProperties_GetSupportedProperties(This,object_id,keys) (This)->lpVtbl->GetSupportedProperties(This,object_id,keys)
#define IPortableDeviceProperties_GetPropertyAttributes(This,object_id,key,attributes) (This)->lpVtbl->GetPropertyAttributes(This,object_id,key,attributes)
#define IPortableDeviceProperties_GetValues(This,object_id,keys,values) (This)->lpVtbl->GetValues(This,object_id,keys,values)
#define IPortableDeviceProperties_SetValues(This,object_id,values,results) (This)->lpVtbl->SetValues(This,object_id,values,results)
#define IPortableDeviceProperties_Delete(This,object_id,keys) (This)->lpVtbl->Delete(This,object_id,keys)
#define IPortableDeviceProperties_Cancel(This) (This)->lpVtbl->Cancel(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceProperties_QueryInterface(IPortableDeviceProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceProperties_AddRef(IPortableDeviceProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceProperties_Release(IPortableDeviceProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceProperties methods ***/
static inline HRESULT IPortableDeviceProperties_GetSupportedProperties(IPortableDeviceProperties* This,LPCWSTR object_id,IPortableDeviceKeyCollection **keys) {
    return This->lpVtbl->GetSupportedProperties(This,object_id,keys);
}
static inline HRESULT IPortableDeviceProperties_GetPropertyAttributes(IPortableDeviceProperties* This,LPCWSTR object_id,REFPROPERTYKEY key,IPortableDeviceValues **attributes) {
    return This->lpVtbl->GetPropertyAttributes(This,object_id,key,attributes);
}
static inline HRESULT IPortableDeviceProperties_GetValues(IPortableDeviceProperties* This,LPCWSTR object_id,IPortableDeviceKeyCollection *keys,IPortableDeviceValues **values) {
    return This->lpVtbl->GetValues(This,object_id,keys,values);
}
static inline HRESULT IPortableDeviceProperties_SetValues(IPortableDeviceProperties* This,LPCWSTR object_id,IPortableDeviceValues *values,IPortableDeviceValues **results) {
    return This->lpVtbl->SetValues(This,object_id,values,results);
}
static inline HRESULT IPortableDeviceProperties_Delete(IPortableDeviceProperties* This,LPCWSTR object_id,IPortableDeviceKeyCollection *keys) {
    return This->lpVtbl->Delete(This,object_id,keys);
}
static inline HRESULT IPortableDeviceProperties_Cancel(IPortableDeviceProperties* This) {
    return This->lpVtbl->Cancel(This);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceProperties_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceResources interface
 */
#ifndef __IPortableDeviceResources_INTERFACE_DEFINED__
#define __IPortableDeviceResources_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceResources, 0xfd8878ac, 0xd841, 0x4d17, 0x89,0x1c, 0xe6,0x82,0x9c,0xdb,0x69,0x34);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fd8878ac-d841-4d17-891c-e6829cdb6934")
IPortableDeviceResources : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSupportedResources(
        LPCWSTR object_id,
        IPortableDeviceKeyCollection **keys) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResourceAttributes(
        LPCWSTR object_id,
        REFPROPERTYKEY key,
        IPortableDeviceValues **resource_attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStream(
        LPCWSTR object_id,
        REFPROPERTYKEY key,
        const DWORD mode,
        DWORD *optimal_buffer_size,
        IStream **stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        LPCWSTR object_id,
        IPortableDeviceKeyCollection *keys) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateResource(
        IPortableDeviceValues *resource_attributes,
        IStream **data,
        DWORD *optimal_write_buffer_size,
        LPWSTR *cookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceResources, 0xfd8878ac, 0xd841, 0x4d17, 0x89,0x1c, 0xe6,0x82,0x9c,0xdb,0x69,0x34)
#endif
#else
typedef struct IPortableDeviceResourcesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceResources *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceResources *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceResources *This);

    /*** IPortableDeviceResources methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSupportedResources)(
        IPortableDeviceResources *This,
        LPCWSTR object_id,
        IPortableDeviceKeyCollection **keys);

    HRESULT (STDMETHODCALLTYPE *GetResourceAttributes)(
        IPortableDeviceResources *This,
        LPCWSTR object_id,
        REFPROPERTYKEY key,
        IPortableDeviceValues **resource_attributes);

    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IPortableDeviceResources *This,
        LPCWSTR object_id,
        REFPROPERTYKEY key,
        const DWORD mode,
        DWORD *optimal_buffer_size,
        IStream **stream);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IPortableDeviceResources *This,
        LPCWSTR object_id,
        IPortableDeviceKeyCollection *keys);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDeviceResources *This);

    HRESULT (STDMETHODCALLTYPE *CreateResource)(
        IPortableDeviceResources *This,
        IPortableDeviceValues *resource_attributes,
        IStream **data,
        DWORD *optimal_write_buffer_size,
        LPWSTR *cookie);

    END_INTERFACE
} IPortableDeviceResourcesVtbl;

interface IPortableDeviceResources {
    CONST_VTBL IPortableDeviceResourcesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceResources_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceResources_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceResources_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceResources methods ***/
#define IPortableDeviceResources_GetSupportedResources(This,object_id,keys) (This)->lpVtbl->GetSupportedResources(This,object_id,keys)
#define IPortableDeviceResources_GetResourceAttributes(This,object_id,key,resource_attributes) (This)->lpVtbl->GetResourceAttributes(This,object_id,key,resource_attributes)
#define IPortableDeviceResources_GetStream(This,object_id,key,mode,optimal_buffer_size,stream) (This)->lpVtbl->GetStream(This,object_id,key,mode,optimal_buffer_size,stream)
#define IPortableDeviceResources_Delete(This,object_id,keys) (This)->lpVtbl->Delete(This,object_id,keys)
#define IPortableDeviceResources_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IPortableDeviceResources_CreateResource(This,resource_attributes,data,optimal_write_buffer_size,cookie) (This)->lpVtbl->CreateResource(This,resource_attributes,data,optimal_write_buffer_size,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceResources_QueryInterface(IPortableDeviceResources* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceResources_AddRef(IPortableDeviceResources* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceResources_Release(IPortableDeviceResources* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceResources methods ***/
static inline HRESULT IPortableDeviceResources_GetSupportedResources(IPortableDeviceResources* This,LPCWSTR object_id,IPortableDeviceKeyCollection **keys) {
    return This->lpVtbl->GetSupportedResources(This,object_id,keys);
}
static inline HRESULT IPortableDeviceResources_GetResourceAttributes(IPortableDeviceResources* This,LPCWSTR object_id,REFPROPERTYKEY key,IPortableDeviceValues **resource_attributes) {
    return This->lpVtbl->GetResourceAttributes(This,object_id,key,resource_attributes);
}
static inline HRESULT IPortableDeviceResources_GetStream(IPortableDeviceResources* This,LPCWSTR object_id,REFPROPERTYKEY key,const DWORD mode,DWORD *optimal_buffer_size,IStream **stream) {
    return This->lpVtbl->GetStream(This,object_id,key,mode,optimal_buffer_size,stream);
}
static inline HRESULT IPortableDeviceResources_Delete(IPortableDeviceResources* This,LPCWSTR object_id,IPortableDeviceKeyCollection *keys) {
    return This->lpVtbl->Delete(This,object_id,keys);
}
static inline HRESULT IPortableDeviceResources_Cancel(IPortableDeviceResources* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT IPortableDeviceResources_CreateResource(IPortableDeviceResources* This,IPortableDeviceValues *resource_attributes,IStream **data,DWORD *optimal_write_buffer_size,LPWSTR *cookie) {
    return This->lpVtbl->CreateResource(This,resource_attributes,data,optimal_write_buffer_size,cookie);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceResources_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceCapabilities interface
 */
#ifndef __IPortableDeviceCapabilities_INTERFACE_DEFINED__
#define __IPortableDeviceCapabilities_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceCapabilities, 0x2c8c6dbf, 0xe3dc, 0x4061, 0xbe,0xcc, 0x85,0x42,0xe8,0x10,0xd1,0x26);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2c8c6dbf-e3dc-4061-becc-8542e810d126")
IPortableDeviceCapabilities : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSupportedCommands(
        IPortableDeviceKeyCollection **commands) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCommandOptions(
        REFPROPERTYKEY command,
        IPortableDeviceValues **options) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFunctionalCategories(
        IPortableDevicePropVariantCollection **categories) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFunctionalObjects(
        REFGUID category,
        IPortableDevicePropVariantCollection **object_ids) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedContentTypes(
        REFGUID category,
        IPortableDevicePropVariantCollection **content_types) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedFormats(
        REFGUID content_type,
        IPortableDevicePropVariantCollection **formats) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedFormatProperties(
        REFGUID format,
        IPortableDeviceKeyCollection **keys) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFixedPropertyAttributes(
        REFGUID format,
        REFPROPERTYKEY key,
        IPortableDeviceValues **attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedEvents(
        IPortableDevicePropVariantCollection **events_collection) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEventOptions(
        REFGUID event_id,
        IPortableDeviceValues **options) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceCapabilities, 0x2c8c6dbf, 0xe3dc, 0x4061, 0xbe,0xcc, 0x85,0x42,0xe8,0x10,0xd1,0x26)
#endif
#else
typedef struct IPortableDeviceCapabilitiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceCapabilities *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceCapabilities *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceCapabilities *This);

    /*** IPortableDeviceCapabilities methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSupportedCommands)(
        IPortableDeviceCapabilities *This,
        IPortableDeviceKeyCollection **commands);

    HRESULT (STDMETHODCALLTYPE *GetCommandOptions)(
        IPortableDeviceCapabilities *This,
        REFPROPERTYKEY command,
        IPortableDeviceValues **options);

    HRESULT (STDMETHODCALLTYPE *GetFunctionalCategories)(
        IPortableDeviceCapabilities *This,
        IPortableDevicePropVariantCollection **categories);

    HRESULT (STDMETHODCALLTYPE *GetFunctionalObjects)(
        IPortableDeviceCapabilities *This,
        REFGUID category,
        IPortableDevicePropVariantCollection **object_ids);

    HRESULT (STDMETHODCALLTYPE *GetSupportedContentTypes)(
        IPortableDeviceCapabilities *This,
        REFGUID category,
        IPortableDevicePropVariantCollection **content_types);

    HRESULT (STDMETHODCALLTYPE *GetSupportedFormats)(
        IPortableDeviceCapabilities *This,
        REFGUID content_type,
        IPortableDevicePropVariantCollection **formats);

    HRESULT (STDMETHODCALLTYPE *GetSupportedFormatProperties)(
        IPortableDeviceCapabilities *This,
        REFGUID format,
        IPortableDeviceKeyCollection **keys);

    HRESULT (STDMETHODCALLTYPE *GetFixedPropertyAttributes)(
        IPortableDeviceCapabilities *This,
        REFGUID format,
        REFPROPERTYKEY key,
        IPortableDeviceValues **attributes);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDeviceCapabilities *This);

    HRESULT (STDMETHODCALLTYPE *GetSupportedEvents)(
        IPortableDeviceCapabilities *This,
        IPortableDevicePropVariantCollection **events_collection);

    HRESULT (STDMETHODCALLTYPE *GetEventOptions)(
        IPortableDeviceCapabilities *This,
        REFGUID event_id,
        IPortableDeviceValues **options);

    END_INTERFACE
} IPortableDeviceCapabilitiesVtbl;

interface IPortableDeviceCapabilities {
    CONST_VTBL IPortableDeviceCapabilitiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceCapabilities_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceCapabilities_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceCapabilities_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceCapabilities methods ***/
#define IPortableDeviceCapabilities_GetSupportedCommands(This,commands) (This)->lpVtbl->GetSupportedCommands(This,commands)
#define IPortableDeviceCapabilities_GetCommandOptions(This,command,options) (This)->lpVtbl->GetCommandOptions(This,command,options)
#define IPortableDeviceCapabilities_GetFunctionalCategories(This,categories) (This)->lpVtbl->GetFunctionalCategories(This,categories)
#define IPortableDeviceCapabilities_GetFunctionalObjects(This,category,object_ids) (This)->lpVtbl->GetFunctionalObjects(This,category,object_ids)
#define IPortableDeviceCapabilities_GetSupportedContentTypes(This,category,content_types) (This)->lpVtbl->GetSupportedContentTypes(This,category,content_types)
#define IPortableDeviceCapabilities_GetSupportedFormats(This,content_type,formats) (This)->lpVtbl->GetSupportedFormats(This,content_type,formats)
#define IPortableDeviceCapabilities_GetSupportedFormatProperties(This,format,keys) (This)->lpVtbl->GetSupportedFormatProperties(This,format,keys)
#define IPortableDeviceCapabilities_GetFixedPropertyAttributes(This,format,key,attributes) (This)->lpVtbl->GetFixedPropertyAttributes(This,format,key,attributes)
#define IPortableDeviceCapabilities_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IPortableDeviceCapabilities_GetSupportedEvents(This,events_collection) (This)->lpVtbl->GetSupportedEvents(This,events_collection)
#define IPortableDeviceCapabilities_GetEventOptions(This,event_id,options) (This)->lpVtbl->GetEventOptions(This,event_id,options)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceCapabilities_QueryInterface(IPortableDeviceCapabilities* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceCapabilities_AddRef(IPortableDeviceCapabilities* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceCapabilities_Release(IPortableDeviceCapabilities* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceCapabilities methods ***/
static inline HRESULT IPortableDeviceCapabilities_GetSupportedCommands(IPortableDeviceCapabilities* This,IPortableDeviceKeyCollection **commands) {
    return This->lpVtbl->GetSupportedCommands(This,commands);
}
static inline HRESULT IPortableDeviceCapabilities_GetCommandOptions(IPortableDeviceCapabilities* This,REFPROPERTYKEY command,IPortableDeviceValues **options) {
    return This->lpVtbl->GetCommandOptions(This,command,options);
}
static inline HRESULT IPortableDeviceCapabilities_GetFunctionalCategories(IPortableDeviceCapabilities* This,IPortableDevicePropVariantCollection **categories) {
    return This->lpVtbl->GetFunctionalCategories(This,categories);
}
static inline HRESULT IPortableDeviceCapabilities_GetFunctionalObjects(IPortableDeviceCapabilities* This,REFGUID category,IPortableDevicePropVariantCollection **object_ids) {
    return This->lpVtbl->GetFunctionalObjects(This,category,object_ids);
}
static inline HRESULT IPortableDeviceCapabilities_GetSupportedContentTypes(IPortableDeviceCapabilities* This,REFGUID category,IPortableDevicePropVariantCollection **content_types) {
    return This->lpVtbl->GetSupportedContentTypes(This,category,content_types);
}
static inline HRESULT IPortableDeviceCapabilities_GetSupportedFormats(IPortableDeviceCapabilities* This,REFGUID content_type,IPortableDevicePropVariantCollection **formats) {
    return This->lpVtbl->GetSupportedFormats(This,content_type,formats);
}
static inline HRESULT IPortableDeviceCapabilities_GetSupportedFormatProperties(IPortableDeviceCapabilities* This,REFGUID format,IPortableDeviceKeyCollection **keys) {
    return This->lpVtbl->GetSupportedFormatProperties(This,format,keys);
}
static inline HRESULT IPortableDeviceCapabilities_GetFixedPropertyAttributes(IPortableDeviceCapabilities* This,REFGUID format,REFPROPERTYKEY key,IPortableDeviceValues **attributes) {
    return This->lpVtbl->GetFixedPropertyAttributes(This,format,key,attributes);
}
static inline HRESULT IPortableDeviceCapabilities_Cancel(IPortableDeviceCapabilities* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT IPortableDeviceCapabilities_GetSupportedEvents(IPortableDeviceCapabilities* This,IPortableDevicePropVariantCollection **events_collection) {
    return This->lpVtbl->GetSupportedEvents(This,events_collection);
}
static inline HRESULT IPortableDeviceCapabilities_GetEventOptions(IPortableDeviceCapabilities* This,REFGUID event_id,IPortableDeviceValues **options) {
    return This->lpVtbl->GetEventOptions(This,event_id,options);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceCapabilities_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceEventCallback interface
 */
#ifndef __IPortableDeviceEventCallback_INTERFACE_DEFINED__
#define __IPortableDeviceEventCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceEventCallback, 0xa8792a31, 0xf385, 0x493c, 0xa8,0x93, 0x40,0xf6,0x4e,0xb4,0x5f,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a8792a31-f385-493c-a893-40f64eb45f6e")
IPortableDeviceEventCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnEvent(
        IPortableDeviceValues *event_parameters) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceEventCallback, 0xa8792a31, 0xf385, 0x493c, 0xa8,0x93, 0x40,0xf6,0x4e,0xb4,0x5f,0x6e)
#endif
#else
typedef struct IPortableDeviceEventCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceEventCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceEventCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceEventCallback *This);

    /*** IPortableDeviceEventCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnEvent)(
        IPortableDeviceEventCallback *This,
        IPortableDeviceValues *event_parameters);

    END_INTERFACE
} IPortableDeviceEventCallbackVtbl;

interface IPortableDeviceEventCallback {
    CONST_VTBL IPortableDeviceEventCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceEventCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceEventCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceEventCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceEventCallback methods ***/
#define IPortableDeviceEventCallback_OnEvent(This,event_parameters) (This)->lpVtbl->OnEvent(This,event_parameters)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceEventCallback_QueryInterface(IPortableDeviceEventCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceEventCallback_AddRef(IPortableDeviceEventCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceEventCallback_Release(IPortableDeviceEventCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceEventCallback methods ***/
static inline HRESULT IPortableDeviceEventCallback_OnEvent(IPortableDeviceEventCallback* This,IPortableDeviceValues *event_parameters) {
    return This->lpVtbl->OnEvent(This,event_parameters);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceEventCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceDataStream interface
 */
#ifndef __IPortableDeviceDataStream_INTERFACE_DEFINED__
#define __IPortableDeviceDataStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceDataStream, 0x88e04db3, 0x1012, 0x4d64, 0x99,0x96, 0xf7,0x03,0xa9,0x50,0xd3,0xf4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("88e04db3-1012-4d64-9996-f703a950d3f4")
IPortableDeviceDataStream : public IStream
{
    virtual HRESULT STDMETHODCALLTYPE GetObjectID(
        LPWSTR *object_id) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceDataStream, 0x88e04db3, 0x1012, 0x4d64, 0x99,0x96, 0xf7,0x03,0xa9,0x50,0xd3,0xf4)
#endif
#else
typedef struct IPortableDeviceDataStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceDataStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceDataStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceDataStream *This);

    /*** ISequentialStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Read)(
        IPortableDeviceDataStream *This,
        void *pv,
        ULONG cb,
        ULONG *pcbRead);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IPortableDeviceDataStream *This,
        const void *pv,
        ULONG cb,
        ULONG *pcbWritten);

    /*** IStream methods ***/
    HRESULT (STDMETHODCALLTYPE *Seek)(
        IPortableDeviceDataStream *This,
        LARGE_INTEGER dlibMove,
        DWORD dwOrigin,
        ULARGE_INTEGER *plibNewPosition);

    HRESULT (STDMETHODCALLTYPE *SetSize)(
        IPortableDeviceDataStream *This,
        ULARGE_INTEGER libNewSize);

    HRESULT (STDMETHODCALLTYPE *CopyTo)(
        IPortableDeviceDataStream *This,
        IStream *pstm,
        ULARGE_INTEGER cb,
        ULARGE_INTEGER *pcbRead,
        ULARGE_INTEGER *pcbWritten);

    HRESULT (STDMETHODCALLTYPE *Commit)(
        IPortableDeviceDataStream *This,
        DWORD grfCommitFlags);

    HRESULT (STDMETHODCALLTYPE *Revert)(
        IPortableDeviceDataStream *This);

    HRESULT (STDMETHODCALLTYPE *LockRegion)(
        IPortableDeviceDataStream *This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *UnlockRegion)(
        IPortableDeviceDataStream *This,
        ULARGE_INTEGER libOffset,
        ULARGE_INTEGER cb,
        DWORD dwLockType);

    HRESULT (STDMETHODCALLTYPE *Stat)(
        IPortableDeviceDataStream *This,
        STATSTG *pstatstg,
        DWORD grfStatFlag);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IPortableDeviceDataStream *This,
        IStream **ppstm);

    /*** IPortableDeviceDataStream methods ***/
    HRESULT (STDMETHODCALLTYPE *GetObjectID)(
        IPortableDeviceDataStream *This,
        LPWSTR *object_id);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDeviceDataStream *This);

    END_INTERFACE
} IPortableDeviceDataStreamVtbl;

interface IPortableDeviceDataStream {
    CONST_VTBL IPortableDeviceDataStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceDataStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceDataStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceDataStream_Release(This) (This)->lpVtbl->Release(This)
/*** ISequentialStream methods ***/
#define IPortableDeviceDataStream_Read(This,pv,cb,pcbRead) (This)->lpVtbl->Read(This,pv,cb,pcbRead)
#define IPortableDeviceDataStream_Write(This,pv,cb,pcbWritten) (This)->lpVtbl->Write(This,pv,cb,pcbWritten)
/*** IStream methods ***/
#define IPortableDeviceDataStream_Seek(This,dlibMove,dwOrigin,plibNewPosition) (This)->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition)
#define IPortableDeviceDataStream_SetSize(This,libNewSize) (This)->lpVtbl->SetSize(This,libNewSize)
#define IPortableDeviceDataStream_CopyTo(This,pstm,cb,pcbRead,pcbWritten) (This)->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten)
#define IPortableDeviceDataStream_Commit(This,grfCommitFlags) (This)->lpVtbl->Commit(This,grfCommitFlags)
#define IPortableDeviceDataStream_Revert(This) (This)->lpVtbl->Revert(This)
#define IPortableDeviceDataStream_LockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->LockRegion(This,libOffset,cb,dwLockType)
#define IPortableDeviceDataStream_UnlockRegion(This,libOffset,cb,dwLockType) (This)->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType)
#define IPortableDeviceDataStream_Stat(This,pstatstg,grfStatFlag) (This)->lpVtbl->Stat(This,pstatstg,grfStatFlag)
#define IPortableDeviceDataStream_Clone(This,ppstm) (This)->lpVtbl->Clone(This,ppstm)
/*** IPortableDeviceDataStream methods ***/
#define IPortableDeviceDataStream_GetObjectID(This,object_id) (This)->lpVtbl->GetObjectID(This,object_id)
#define IPortableDeviceDataStream_Cancel(This) (This)->lpVtbl->Cancel(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceDataStream_QueryInterface(IPortableDeviceDataStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceDataStream_AddRef(IPortableDeviceDataStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceDataStream_Release(IPortableDeviceDataStream* This) {
    return This->lpVtbl->Release(This);
}
/*** ISequentialStream methods ***/
static inline HRESULT IPortableDeviceDataStream_Read(IPortableDeviceDataStream* This,void *pv,ULONG cb,ULONG *pcbRead) {
    return This->lpVtbl->Read(This,pv,cb,pcbRead);
}
static inline HRESULT IPortableDeviceDataStream_Write(IPortableDeviceDataStream* This,const void *pv,ULONG cb,ULONG *pcbWritten) {
    return This->lpVtbl->Write(This,pv,cb,pcbWritten);
}
/*** IStream methods ***/
static inline HRESULT IPortableDeviceDataStream_Seek(IPortableDeviceDataStream* This,LARGE_INTEGER dlibMove,DWORD dwOrigin,ULARGE_INTEGER *plibNewPosition) {
    return This->lpVtbl->Seek(This,dlibMove,dwOrigin,plibNewPosition);
}
static inline HRESULT IPortableDeviceDataStream_SetSize(IPortableDeviceDataStream* This,ULARGE_INTEGER libNewSize) {
    return This->lpVtbl->SetSize(This,libNewSize);
}
static inline HRESULT IPortableDeviceDataStream_CopyTo(IPortableDeviceDataStream* This,IStream *pstm,ULARGE_INTEGER cb,ULARGE_INTEGER *pcbRead,ULARGE_INTEGER *pcbWritten) {
    return This->lpVtbl->CopyTo(This,pstm,cb,pcbRead,pcbWritten);
}
static inline HRESULT IPortableDeviceDataStream_Commit(IPortableDeviceDataStream* This,DWORD grfCommitFlags) {
    return This->lpVtbl->Commit(This,grfCommitFlags);
}
static inline HRESULT IPortableDeviceDataStream_Revert(IPortableDeviceDataStream* This) {
    return This->lpVtbl->Revert(This);
}
static inline HRESULT IPortableDeviceDataStream_LockRegion(IPortableDeviceDataStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->LockRegion(This,libOffset,cb,dwLockType);
}
static inline HRESULT IPortableDeviceDataStream_UnlockRegion(IPortableDeviceDataStream* This,ULARGE_INTEGER libOffset,ULARGE_INTEGER cb,DWORD dwLockType) {
    return This->lpVtbl->UnlockRegion(This,libOffset,cb,dwLockType);
}
static inline HRESULT IPortableDeviceDataStream_Stat(IPortableDeviceDataStream* This,STATSTG *pstatstg,DWORD grfStatFlag) {
    return This->lpVtbl->Stat(This,pstatstg,grfStatFlag);
}
static inline HRESULT IPortableDeviceDataStream_Clone(IPortableDeviceDataStream* This,IStream **ppstm) {
    return This->lpVtbl->Clone(This,ppstm);
}
/*** IPortableDeviceDataStream methods ***/
static inline HRESULT IPortableDeviceDataStream_GetObjectID(IPortableDeviceDataStream* This,LPWSTR *object_id) {
    return This->lpVtbl->GetObjectID(This,object_id);
}
static inline HRESULT IPortableDeviceDataStream_Cancel(IPortableDeviceDataStream* This) {
    return This->lpVtbl->Cancel(This);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceDataStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceUnitsStream interface
 */
#ifndef __IPortableDeviceUnitsStream_INTERFACE_DEFINED__
#define __IPortableDeviceUnitsStream_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceUnitsStream, 0x5e98025f, 0xbfc4, 0x47a2, 0x9a,0x5f, 0xbc,0x90,0x0a,0x50,0x7c,0x67);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5e98025f-bfc4-47a2-9a5f-bc900a507c67")
IPortableDeviceUnitsStream : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SeekInUnits(
        LARGE_INTEGER dlib_move,
        WPD_STREAM_UNITS units,
        DWORD origin,
        ULARGE_INTEGER *lib_new_position) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceUnitsStream, 0x5e98025f, 0xbfc4, 0x47a2, 0x9a,0x5f, 0xbc,0x90,0x0a,0x50,0x7c,0x67)
#endif
#else
typedef struct IPortableDeviceUnitsStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceUnitsStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceUnitsStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceUnitsStream *This);

    /*** IPortableDeviceUnitsStream methods ***/
    HRESULT (STDMETHODCALLTYPE *SeekInUnits)(
        IPortableDeviceUnitsStream *This,
        LARGE_INTEGER dlib_move,
        WPD_STREAM_UNITS units,
        DWORD origin,
        ULARGE_INTEGER *lib_new_position);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDeviceUnitsStream *This);

    END_INTERFACE
} IPortableDeviceUnitsStreamVtbl;

interface IPortableDeviceUnitsStream {
    CONST_VTBL IPortableDeviceUnitsStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceUnitsStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceUnitsStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceUnitsStream_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceUnitsStream methods ***/
#define IPortableDeviceUnitsStream_SeekInUnits(This,dlib_move,units,origin,lib_new_position) (This)->lpVtbl->SeekInUnits(This,dlib_move,units,origin,lib_new_position)
#define IPortableDeviceUnitsStream_Cancel(This) (This)->lpVtbl->Cancel(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceUnitsStream_QueryInterface(IPortableDeviceUnitsStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceUnitsStream_AddRef(IPortableDeviceUnitsStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceUnitsStream_Release(IPortableDeviceUnitsStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceUnitsStream methods ***/
static inline HRESULT IPortableDeviceUnitsStream_SeekInUnits(IPortableDeviceUnitsStream* This,LARGE_INTEGER dlib_move,WPD_STREAM_UNITS units,DWORD origin,ULARGE_INTEGER *lib_new_position) {
    return This->lpVtbl->SeekInUnits(This,dlib_move,units,origin,lib_new_position);
}
static inline HRESULT IPortableDeviceUnitsStream_Cancel(IPortableDeviceUnitsStream* This) {
    return This->lpVtbl->Cancel(This);
}
#endif
#endif

#endif

HRESULT STDMETHODCALLTYPE IPortableDeviceUnitsStream_RemoteSeekInUnits_Proxy(
    IPortableDeviceUnitsStream* This,
    LARGE_INTEGER dlib_move,
    WPD_STREAM_UNITS units,
    DWORD origin,
    ULARGE_INTEGER *lib_new_position);
void __RPC_STUB IPortableDeviceUnitsStream_RemoteSeekInUnits_Stub(
    IRpcStubBuffer* This,
    IRpcChannelBuffer* pRpcChannelBuffer,
    PRPC_MESSAGE pRpcMessage,
    DWORD* pdwStubPhase);
HRESULT CALLBACK IPortableDeviceUnitsStream_SeekInUnits_Proxy(
    IPortableDeviceUnitsStream* This,
    LARGE_INTEGER dlib_move,
    WPD_STREAM_UNITS units,
    DWORD origin,
    ULARGE_INTEGER *lib_new_position);
HRESULT __RPC_STUB IPortableDeviceUnitsStream_SeekInUnits_Stub(
    IPortableDeviceUnitsStream* This,
    LARGE_INTEGER dlib_move,
    WPD_STREAM_UNITS units,
    DWORD origin,
    ULARGE_INTEGER *lib_new_position);

#endif  /* __IPortableDeviceUnitsStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDevicePropertiesBulk interface
 */
#ifndef __IPortableDevicePropertiesBulk_INTERFACE_DEFINED__
#define __IPortableDevicePropertiesBulk_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDevicePropertiesBulk, 0x482b05c0, 0x4056, 0x44ed, 0x9e,0x0f, 0x5e,0x23,0xb0,0x09,0xda,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("482b05c0-4056-44ed-9e0f-5e23b009da93")
IPortableDevicePropertiesBulk : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueueGetValuesByObjectList(
        IPortableDevicePropVariantCollection *object_ids,
        IPortableDeviceKeyCollection *keys,
        IPortableDevicePropertiesBulkCallback *callback,
        GUID *context) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueueGetValuesByObjectFormat(
        REFGUID guid_object_format,
        LPCWSTR parent_object_id,
        const DWORD depth,
        IPortableDeviceKeyCollection *keys,
        IPortableDevicePropertiesBulkCallback *callback,
        GUID *context) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueueSetValuesByObjectList(
        IPortableDeviceValuesCollection *object_values,
        IPortableDevicePropertiesBulkCallback *callback,
        GUID *context) = 0;

    virtual HRESULT STDMETHODCALLTYPE Start(
        REFGUID context) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        REFGUID context) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDevicePropertiesBulk, 0x482b05c0, 0x4056, 0x44ed, 0x9e,0x0f, 0x5e,0x23,0xb0,0x09,0xda,0x93)
#endif
#else
typedef struct IPortableDevicePropertiesBulkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDevicePropertiesBulk *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDevicePropertiesBulk *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDevicePropertiesBulk *This);

    /*** IPortableDevicePropertiesBulk methods ***/
    HRESULT (STDMETHODCALLTYPE *QueueGetValuesByObjectList)(
        IPortableDevicePropertiesBulk *This,
        IPortableDevicePropVariantCollection *object_ids,
        IPortableDeviceKeyCollection *keys,
        IPortableDevicePropertiesBulkCallback *callback,
        GUID *context);

    HRESULT (STDMETHODCALLTYPE *QueueGetValuesByObjectFormat)(
        IPortableDevicePropertiesBulk *This,
        REFGUID guid_object_format,
        LPCWSTR parent_object_id,
        const DWORD depth,
        IPortableDeviceKeyCollection *keys,
        IPortableDevicePropertiesBulkCallback *callback,
        GUID *context);

    HRESULT (STDMETHODCALLTYPE *QueueSetValuesByObjectList)(
        IPortableDevicePropertiesBulk *This,
        IPortableDeviceValuesCollection *object_values,
        IPortableDevicePropertiesBulkCallback *callback,
        GUID *context);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IPortableDevicePropertiesBulk *This,
        REFGUID context);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDevicePropertiesBulk *This,
        REFGUID context);

    END_INTERFACE
} IPortableDevicePropertiesBulkVtbl;

interface IPortableDevicePropertiesBulk {
    CONST_VTBL IPortableDevicePropertiesBulkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDevicePropertiesBulk_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDevicePropertiesBulk_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDevicePropertiesBulk_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDevicePropertiesBulk methods ***/
#define IPortableDevicePropertiesBulk_QueueGetValuesByObjectList(This,object_ids,keys,callback,context) (This)->lpVtbl->QueueGetValuesByObjectList(This,object_ids,keys,callback,context)
#define IPortableDevicePropertiesBulk_QueueGetValuesByObjectFormat(This,guid_object_format,parent_object_id,depth,keys,callback,context) (This)->lpVtbl->QueueGetValuesByObjectFormat(This,guid_object_format,parent_object_id,depth,keys,callback,context)
#define IPortableDevicePropertiesBulk_QueueSetValuesByObjectList(This,object_values,callback,context) (This)->lpVtbl->QueueSetValuesByObjectList(This,object_values,callback,context)
#define IPortableDevicePropertiesBulk_Start(This,context) (This)->lpVtbl->Start(This,context)
#define IPortableDevicePropertiesBulk_Cancel(This,context) (This)->lpVtbl->Cancel(This,context)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDevicePropertiesBulk_QueryInterface(IPortableDevicePropertiesBulk* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDevicePropertiesBulk_AddRef(IPortableDevicePropertiesBulk* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDevicePropertiesBulk_Release(IPortableDevicePropertiesBulk* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDevicePropertiesBulk methods ***/
static inline HRESULT IPortableDevicePropertiesBulk_QueueGetValuesByObjectList(IPortableDevicePropertiesBulk* This,IPortableDevicePropVariantCollection *object_ids,IPortableDeviceKeyCollection *keys,IPortableDevicePropertiesBulkCallback *callback,GUID *context) {
    return This->lpVtbl->QueueGetValuesByObjectList(This,object_ids,keys,callback,context);
}
static inline HRESULT IPortableDevicePropertiesBulk_QueueGetValuesByObjectFormat(IPortableDevicePropertiesBulk* This,REFGUID guid_object_format,LPCWSTR parent_object_id,const DWORD depth,IPortableDeviceKeyCollection *keys,IPortableDevicePropertiesBulkCallback *callback,GUID *context) {
    return This->lpVtbl->QueueGetValuesByObjectFormat(This,guid_object_format,parent_object_id,depth,keys,callback,context);
}
static inline HRESULT IPortableDevicePropertiesBulk_QueueSetValuesByObjectList(IPortableDevicePropertiesBulk* This,IPortableDeviceValuesCollection *object_values,IPortableDevicePropertiesBulkCallback *callback,GUID *context) {
    return This->lpVtbl->QueueSetValuesByObjectList(This,object_values,callback,context);
}
static inline HRESULT IPortableDevicePropertiesBulk_Start(IPortableDevicePropertiesBulk* This,REFGUID context) {
    return This->lpVtbl->Start(This,context);
}
static inline HRESULT IPortableDevicePropertiesBulk_Cancel(IPortableDevicePropertiesBulk* This,REFGUID context) {
    return This->lpVtbl->Cancel(This,context);
}
#endif
#endif

#endif


#endif  /* __IPortableDevicePropertiesBulk_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDevicePropertiesBulkCallback interface
 */
#ifndef __IPortableDevicePropertiesBulkCallback_INTERFACE_DEFINED__
#define __IPortableDevicePropertiesBulkCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDevicePropertiesBulkCallback, 0x9deacb80, 0x11e8, 0x40e3, 0xa9,0xf3, 0xf5,0x57,0x98,0x6a,0x78,0x45);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9deacb80-11e8-40e3-a9f3-f557986a7845")
IPortableDevicePropertiesBulkCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnStart(
        REFGUID context) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnProgress(
        REFGUID context,
        IPortableDeviceValuesCollection *results) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnEnd(
        REFGUID context,
        HRESULT hr_status) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDevicePropertiesBulkCallback, 0x9deacb80, 0x11e8, 0x40e3, 0xa9,0xf3, 0xf5,0x57,0x98,0x6a,0x78,0x45)
#endif
#else
typedef struct IPortableDevicePropertiesBulkCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDevicePropertiesBulkCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDevicePropertiesBulkCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDevicePropertiesBulkCallback *This);

    /*** IPortableDevicePropertiesBulkCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnStart)(
        IPortableDevicePropertiesBulkCallback *This,
        REFGUID context);

    HRESULT (STDMETHODCALLTYPE *OnProgress)(
        IPortableDevicePropertiesBulkCallback *This,
        REFGUID context,
        IPortableDeviceValuesCollection *results);

    HRESULT (STDMETHODCALLTYPE *OnEnd)(
        IPortableDevicePropertiesBulkCallback *This,
        REFGUID context,
        HRESULT hr_status);

    END_INTERFACE
} IPortableDevicePropertiesBulkCallbackVtbl;

interface IPortableDevicePropertiesBulkCallback {
    CONST_VTBL IPortableDevicePropertiesBulkCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDevicePropertiesBulkCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDevicePropertiesBulkCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDevicePropertiesBulkCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDevicePropertiesBulkCallback methods ***/
#define IPortableDevicePropertiesBulkCallback_OnStart(This,context) (This)->lpVtbl->OnStart(This,context)
#define IPortableDevicePropertiesBulkCallback_OnProgress(This,context,results) (This)->lpVtbl->OnProgress(This,context,results)
#define IPortableDevicePropertiesBulkCallback_OnEnd(This,context,hr_status) (This)->lpVtbl->OnEnd(This,context,hr_status)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDevicePropertiesBulkCallback_QueryInterface(IPortableDevicePropertiesBulkCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDevicePropertiesBulkCallback_AddRef(IPortableDevicePropertiesBulkCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDevicePropertiesBulkCallback_Release(IPortableDevicePropertiesBulkCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDevicePropertiesBulkCallback methods ***/
static inline HRESULT IPortableDevicePropertiesBulkCallback_OnStart(IPortableDevicePropertiesBulkCallback* This,REFGUID context) {
    return This->lpVtbl->OnStart(This,context);
}
static inline HRESULT IPortableDevicePropertiesBulkCallback_OnProgress(IPortableDevicePropertiesBulkCallback* This,REFGUID context,IPortableDeviceValuesCollection *results) {
    return This->lpVtbl->OnProgress(This,context,results);
}
static inline HRESULT IPortableDevicePropertiesBulkCallback_OnEnd(IPortableDevicePropertiesBulkCallback* This,REFGUID context,HRESULT hr_status) {
    return This->lpVtbl->OnEnd(This,context,hr_status);
}
#endif
#endif

#endif


#endif  /* __IPortableDevicePropertiesBulkCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceServiceManager interface
 */
#ifndef __IPortableDeviceServiceManager_INTERFACE_DEFINED__
#define __IPortableDeviceServiceManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceServiceManager, 0xa8abc4e9, 0xa84a, 0x47a9, 0x80,0xb3, 0xc5,0xd9,0xb1,0x72,0xa9,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a8abc4e9-a84a-47a9-80b3-c5d9b172a961")
IPortableDeviceServiceManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDeviceServices(
        LPCWSTR device_id,
        REFGUID guid_service_category,
        LPWSTR *services,
        DWORD *services_count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceForService(
        LPCWSTR service_id,
        LPWSTR *device_id) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceServiceManager, 0xa8abc4e9, 0xa84a, 0x47a9, 0x80,0xb3, 0xc5,0xd9,0xb1,0x72,0xa9,0x61)
#endif
#else
typedef struct IPortableDeviceServiceManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceServiceManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceServiceManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceServiceManager *This);

    /*** IPortableDeviceServiceManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeviceServices)(
        IPortableDeviceServiceManager *This,
        LPCWSTR device_id,
        REFGUID guid_service_category,
        LPWSTR *services,
        DWORD *services_count);

    HRESULT (STDMETHODCALLTYPE *GetDeviceForService)(
        IPortableDeviceServiceManager *This,
        LPCWSTR service_id,
        LPWSTR *device_id);

    END_INTERFACE
} IPortableDeviceServiceManagerVtbl;

interface IPortableDeviceServiceManager {
    CONST_VTBL IPortableDeviceServiceManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceServiceManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceServiceManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceServiceManager_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceServiceManager methods ***/
#define IPortableDeviceServiceManager_GetDeviceServices(This,device_id,guid_service_category,services,services_count) (This)->lpVtbl->GetDeviceServices(This,device_id,guid_service_category,services,services_count)
#define IPortableDeviceServiceManager_GetDeviceForService(This,service_id,device_id) (This)->lpVtbl->GetDeviceForService(This,service_id,device_id)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceServiceManager_QueryInterface(IPortableDeviceServiceManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceServiceManager_AddRef(IPortableDeviceServiceManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceServiceManager_Release(IPortableDeviceServiceManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceServiceManager methods ***/
static inline HRESULT IPortableDeviceServiceManager_GetDeviceServices(IPortableDeviceServiceManager* This,LPCWSTR device_id,REFGUID guid_service_category,LPWSTR *services,DWORD *services_count) {
    return This->lpVtbl->GetDeviceServices(This,device_id,guid_service_category,services,services_count);
}
static inline HRESULT IPortableDeviceServiceManager_GetDeviceForService(IPortableDeviceServiceManager* This,LPCWSTR service_id,LPWSTR *device_id) {
    return This->lpVtbl->GetDeviceForService(This,service_id,device_id);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceServiceManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceService interface
 */
#ifndef __IPortableDeviceService_INTERFACE_DEFINED__
#define __IPortableDeviceService_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceService, 0xd3bd3a44, 0xd7b5, 0x40a9, 0x98,0xb7, 0x2f,0xa4,0xd0,0x1d,0xec,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d3bd3a44-d7b5-40a9-98b7-2fa4d01dec08")
IPortableDeviceService : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Open(
        LPCWSTR service_id,
        IPortableDeviceValues *client_info) = 0;

    virtual HRESULT STDMETHODCALLTYPE Capabilities(
        IPortableDeviceServiceCapabilities **capabilities) = 0;

    virtual HRESULT STDMETHODCALLTYPE Content(
        IPortableDeviceContent2 **content) = 0;

    virtual HRESULT STDMETHODCALLTYPE Methods(
        IPortableDeviceServiceMethods **service_methods) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetServiceObjectID(
        LPWSTR *service_object_id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPnPServiceID(
        LPWSTR *service_id) = 0;

    virtual HRESULT STDMETHODCALLTYPE Advise(
        const DWORD flags,
        IPortableDeviceEventCallback *callback,
        IPortableDeviceValues *parameters,
        LPWSTR *cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE Unadvise(
        LPCWSTR cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendCommand(
        const DWORD flags,
        IPortableDeviceValues *parameters,
        IPortableDeviceValues **results) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceService, 0xd3bd3a44, 0xd7b5, 0x40a9, 0x98,0xb7, 0x2f,0xa4,0xd0,0x1d,0xec,0x08)
#endif
#else
typedef struct IPortableDeviceServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceService *This);

    /*** IPortableDeviceService methods ***/
    HRESULT (STDMETHODCALLTYPE *Open)(
        IPortableDeviceService *This,
        LPCWSTR service_id,
        IPortableDeviceValues *client_info);

    HRESULT (STDMETHODCALLTYPE *Capabilities)(
        IPortableDeviceService *This,
        IPortableDeviceServiceCapabilities **capabilities);

    HRESULT (STDMETHODCALLTYPE *Content)(
        IPortableDeviceService *This,
        IPortableDeviceContent2 **content);

    HRESULT (STDMETHODCALLTYPE *Methods)(
        IPortableDeviceService *This,
        IPortableDeviceServiceMethods **service_methods);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDeviceService *This);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IPortableDeviceService *This);

    HRESULT (STDMETHODCALLTYPE *GetServiceObjectID)(
        IPortableDeviceService *This,
        LPWSTR *service_object_id);

    HRESULT (STDMETHODCALLTYPE *GetPnPServiceID)(
        IPortableDeviceService *This,
        LPWSTR *service_id);

    HRESULT (STDMETHODCALLTYPE *Advise)(
        IPortableDeviceService *This,
        const DWORD flags,
        IPortableDeviceEventCallback *callback,
        IPortableDeviceValues *parameters,
        LPWSTR *cookie);

    HRESULT (STDMETHODCALLTYPE *Unadvise)(
        IPortableDeviceService *This,
        LPCWSTR cookie);

    HRESULT (STDMETHODCALLTYPE *SendCommand)(
        IPortableDeviceService *This,
        const DWORD flags,
        IPortableDeviceValues *parameters,
        IPortableDeviceValues **results);

    END_INTERFACE
} IPortableDeviceServiceVtbl;

interface IPortableDeviceService {
    CONST_VTBL IPortableDeviceServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceService_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceService methods ***/
#define IPortableDeviceService_Open(This,service_id,client_info) (This)->lpVtbl->Open(This,service_id,client_info)
#define IPortableDeviceService_Capabilities(This,capabilities) (This)->lpVtbl->Capabilities(This,capabilities)
#define IPortableDeviceService_Content(This,content) (This)->lpVtbl->Content(This,content)
#define IPortableDeviceService_Methods(This,service_methods) (This)->lpVtbl->Methods(This,service_methods)
#define IPortableDeviceService_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IPortableDeviceService_Close(This) (This)->lpVtbl->Close(This)
#define IPortableDeviceService_GetServiceObjectID(This,service_object_id) (This)->lpVtbl->GetServiceObjectID(This,service_object_id)
#define IPortableDeviceService_GetPnPServiceID(This,service_id) (This)->lpVtbl->GetPnPServiceID(This,service_id)
#define IPortableDeviceService_Advise(This,flags,callback,parameters,cookie) (This)->lpVtbl->Advise(This,flags,callback,parameters,cookie)
#define IPortableDeviceService_Unadvise(This,cookie) (This)->lpVtbl->Unadvise(This,cookie)
#define IPortableDeviceService_SendCommand(This,flags,parameters,results) (This)->lpVtbl->SendCommand(This,flags,parameters,results)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceService_QueryInterface(IPortableDeviceService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceService_AddRef(IPortableDeviceService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceService_Release(IPortableDeviceService* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceService methods ***/
static inline HRESULT IPortableDeviceService_Open(IPortableDeviceService* This,LPCWSTR service_id,IPortableDeviceValues *client_info) {
    return This->lpVtbl->Open(This,service_id,client_info);
}
static inline HRESULT IPortableDeviceService_Capabilities(IPortableDeviceService* This,IPortableDeviceServiceCapabilities **capabilities) {
    return This->lpVtbl->Capabilities(This,capabilities);
}
static inline HRESULT IPortableDeviceService_Content(IPortableDeviceService* This,IPortableDeviceContent2 **content) {
    return This->lpVtbl->Content(This,content);
}
static inline HRESULT IPortableDeviceService_Methods(IPortableDeviceService* This,IPortableDeviceServiceMethods **service_methods) {
    return This->lpVtbl->Methods(This,service_methods);
}
static inline HRESULT IPortableDeviceService_Cancel(IPortableDeviceService* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT IPortableDeviceService_Close(IPortableDeviceService* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IPortableDeviceService_GetServiceObjectID(IPortableDeviceService* This,LPWSTR *service_object_id) {
    return This->lpVtbl->GetServiceObjectID(This,service_object_id);
}
static inline HRESULT IPortableDeviceService_GetPnPServiceID(IPortableDeviceService* This,LPWSTR *service_id) {
    return This->lpVtbl->GetPnPServiceID(This,service_id);
}
static inline HRESULT IPortableDeviceService_Advise(IPortableDeviceService* This,const DWORD flags,IPortableDeviceEventCallback *callback,IPortableDeviceValues *parameters,LPWSTR *cookie) {
    return This->lpVtbl->Advise(This,flags,callback,parameters,cookie);
}
static inline HRESULT IPortableDeviceService_Unadvise(IPortableDeviceService* This,LPCWSTR cookie) {
    return This->lpVtbl->Unadvise(This,cookie);
}
static inline HRESULT IPortableDeviceService_SendCommand(IPortableDeviceService* This,const DWORD flags,IPortableDeviceValues *parameters,IPortableDeviceValues **results) {
    return This->lpVtbl->SendCommand(This,flags,parameters,results);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceServiceCapabilities interface
 */
#ifndef __IPortableDeviceServiceCapabilities_INTERFACE_DEFINED__
#define __IPortableDeviceServiceCapabilities_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceServiceCapabilities, 0x24dbd89d, 0x413e, 0x43e0, 0xbd,0x5b, 0x19,0x7f,0x3c,0x56,0xc8,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("24dbd89d-413e-43e0-bd5b-197f3c56c886")
IPortableDeviceServiceCapabilities : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSupportedMethods(
        IPortableDevicePropVariantCollection **supported_methods) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedMethodsByFormat(
        REFGUID format,
        IPortableDevicePropVariantCollection **supported_methods) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMethodAttributes(
        REFGUID method,
        IPortableDeviceValues **attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMethodParameterAttributes(
        REFGUID method,
        REFPROPERTYKEY parameter,
        IPortableDeviceValues **attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedFormats(
        IPortableDevicePropVariantCollection **formats) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFormatAttributes(
        REFGUID format,
        IPortableDeviceValues **attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedFormatProperties(
        REFGUID format,
        IPortableDeviceKeyCollection **keys) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFormatPropertyAttributes(
        REFGUID format,
        REFPROPERTYKEY property,
        IPortableDeviceValues **attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedEvents(
        IPortableDevicePropVariantCollection **events) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEventAttributes(
        REFGUID event_id,
        IPortableDeviceValues **attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEventParameterAttributes(
        REFGUID event_id,
        REFPROPERTYKEY parameter,
        IPortableDeviceValues **attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInheritedServices(
        const DWORD inheritance_type,
        IPortableDevicePropVariantCollection **services) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFormatRenderingProfiles(
        REFGUID format,
        IPortableDeviceValuesCollection **rendering_profiles) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedCommands(
        IPortableDeviceKeyCollection **commands) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCommandOptions(
        REFPROPERTYKEY command,
        IPortableDeviceValues **options) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceServiceCapabilities, 0x24dbd89d, 0x413e, 0x43e0, 0xbd,0x5b, 0x19,0x7f,0x3c,0x56,0xc8,0x86)
#endif
#else
typedef struct IPortableDeviceServiceCapabilitiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceServiceCapabilities *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceServiceCapabilities *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceServiceCapabilities *This);

    /*** IPortableDeviceServiceCapabilities methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSupportedMethods)(
        IPortableDeviceServiceCapabilities *This,
        IPortableDevicePropVariantCollection **supported_methods);

    HRESULT (STDMETHODCALLTYPE *GetSupportedMethodsByFormat)(
        IPortableDeviceServiceCapabilities *This,
        REFGUID format,
        IPortableDevicePropVariantCollection **supported_methods);

    HRESULT (STDMETHODCALLTYPE *GetMethodAttributes)(
        IPortableDeviceServiceCapabilities *This,
        REFGUID method,
        IPortableDeviceValues **attributes);

    HRESULT (STDMETHODCALLTYPE *GetMethodParameterAttributes)(
        IPortableDeviceServiceCapabilities *This,
        REFGUID method,
        REFPROPERTYKEY parameter,
        IPortableDeviceValues **attributes);

    HRESULT (STDMETHODCALLTYPE *GetSupportedFormats)(
        IPortableDeviceServiceCapabilities *This,
        IPortableDevicePropVariantCollection **formats);

    HRESULT (STDMETHODCALLTYPE *GetFormatAttributes)(
        IPortableDeviceServiceCapabilities *This,
        REFGUID format,
        IPortableDeviceValues **attributes);

    HRESULT (STDMETHODCALLTYPE *GetSupportedFormatProperties)(
        IPortableDeviceServiceCapabilities *This,
        REFGUID format,
        IPortableDeviceKeyCollection **keys);

    HRESULT (STDMETHODCALLTYPE *GetFormatPropertyAttributes)(
        IPortableDeviceServiceCapabilities *This,
        REFGUID format,
        REFPROPERTYKEY property,
        IPortableDeviceValues **attributes);

    HRESULT (STDMETHODCALLTYPE *GetSupportedEvents)(
        IPortableDeviceServiceCapabilities *This,
        IPortableDevicePropVariantCollection **events);

    HRESULT (STDMETHODCALLTYPE *GetEventAttributes)(
        IPortableDeviceServiceCapabilities *This,
        REFGUID event_id,
        IPortableDeviceValues **attributes);

    HRESULT (STDMETHODCALLTYPE *GetEventParameterAttributes)(
        IPortableDeviceServiceCapabilities *This,
        REFGUID event_id,
        REFPROPERTYKEY parameter,
        IPortableDeviceValues **attributes);

    HRESULT (STDMETHODCALLTYPE *GetInheritedServices)(
        IPortableDeviceServiceCapabilities *This,
        const DWORD inheritance_type,
        IPortableDevicePropVariantCollection **services);

    HRESULT (STDMETHODCALLTYPE *GetFormatRenderingProfiles)(
        IPortableDeviceServiceCapabilities *This,
        REFGUID format,
        IPortableDeviceValuesCollection **rendering_profiles);

    HRESULT (STDMETHODCALLTYPE *GetSupportedCommands)(
        IPortableDeviceServiceCapabilities *This,
        IPortableDeviceKeyCollection **commands);

    HRESULT (STDMETHODCALLTYPE *GetCommandOptions)(
        IPortableDeviceServiceCapabilities *This,
        REFPROPERTYKEY command,
        IPortableDeviceValues **options);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDeviceServiceCapabilities *This);

    END_INTERFACE
} IPortableDeviceServiceCapabilitiesVtbl;

interface IPortableDeviceServiceCapabilities {
    CONST_VTBL IPortableDeviceServiceCapabilitiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceServiceCapabilities_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceServiceCapabilities_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceServiceCapabilities_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceServiceCapabilities methods ***/
#define IPortableDeviceServiceCapabilities_GetSupportedMethods(This,supported_methods) (This)->lpVtbl->GetSupportedMethods(This,supported_methods)
#define IPortableDeviceServiceCapabilities_GetSupportedMethodsByFormat(This,format,supported_methods) (This)->lpVtbl->GetSupportedMethodsByFormat(This,format,supported_methods)
#define IPortableDeviceServiceCapabilities_GetMethodAttributes(This,method,attributes) (This)->lpVtbl->GetMethodAttributes(This,method,attributes)
#define IPortableDeviceServiceCapabilities_GetMethodParameterAttributes(This,method,parameter,attributes) (This)->lpVtbl->GetMethodParameterAttributes(This,method,parameter,attributes)
#define IPortableDeviceServiceCapabilities_GetSupportedFormats(This,formats) (This)->lpVtbl->GetSupportedFormats(This,formats)
#define IPortableDeviceServiceCapabilities_GetFormatAttributes(This,format,attributes) (This)->lpVtbl->GetFormatAttributes(This,format,attributes)
#define IPortableDeviceServiceCapabilities_GetSupportedFormatProperties(This,format,keys) (This)->lpVtbl->GetSupportedFormatProperties(This,format,keys)
#define IPortableDeviceServiceCapabilities_GetFormatPropertyAttributes(This,format,property,attributes) (This)->lpVtbl->GetFormatPropertyAttributes(This,format,property,attributes)
#define IPortableDeviceServiceCapabilities_GetSupportedEvents(This,events) (This)->lpVtbl->GetSupportedEvents(This,events)
#define IPortableDeviceServiceCapabilities_GetEventAttributes(This,event_id,attributes) (This)->lpVtbl->GetEventAttributes(This,event_id,attributes)
#define IPortableDeviceServiceCapabilities_GetEventParameterAttributes(This,event_id,parameter,attributes) (This)->lpVtbl->GetEventParameterAttributes(This,event_id,parameter,attributes)
#define IPortableDeviceServiceCapabilities_GetInheritedServices(This,inheritance_type,services) (This)->lpVtbl->GetInheritedServices(This,inheritance_type,services)
#define IPortableDeviceServiceCapabilities_GetFormatRenderingProfiles(This,format,rendering_profiles) (This)->lpVtbl->GetFormatRenderingProfiles(This,format,rendering_profiles)
#define IPortableDeviceServiceCapabilities_GetSupportedCommands(This,commands) (This)->lpVtbl->GetSupportedCommands(This,commands)
#define IPortableDeviceServiceCapabilities_GetCommandOptions(This,command,options) (This)->lpVtbl->GetCommandOptions(This,command,options)
#define IPortableDeviceServiceCapabilities_Cancel(This) (This)->lpVtbl->Cancel(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceServiceCapabilities_QueryInterface(IPortableDeviceServiceCapabilities* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceServiceCapabilities_AddRef(IPortableDeviceServiceCapabilities* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceServiceCapabilities_Release(IPortableDeviceServiceCapabilities* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceServiceCapabilities methods ***/
static inline HRESULT IPortableDeviceServiceCapabilities_GetSupportedMethods(IPortableDeviceServiceCapabilities* This,IPortableDevicePropVariantCollection **supported_methods) {
    return This->lpVtbl->GetSupportedMethods(This,supported_methods);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetSupportedMethodsByFormat(IPortableDeviceServiceCapabilities* This,REFGUID format,IPortableDevicePropVariantCollection **supported_methods) {
    return This->lpVtbl->GetSupportedMethodsByFormat(This,format,supported_methods);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetMethodAttributes(IPortableDeviceServiceCapabilities* This,REFGUID method,IPortableDeviceValues **attributes) {
    return This->lpVtbl->GetMethodAttributes(This,method,attributes);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetMethodParameterAttributes(IPortableDeviceServiceCapabilities* This,REFGUID method,REFPROPERTYKEY parameter,IPortableDeviceValues **attributes) {
    return This->lpVtbl->GetMethodParameterAttributes(This,method,parameter,attributes);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetSupportedFormats(IPortableDeviceServiceCapabilities* This,IPortableDevicePropVariantCollection **formats) {
    return This->lpVtbl->GetSupportedFormats(This,formats);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetFormatAttributes(IPortableDeviceServiceCapabilities* This,REFGUID format,IPortableDeviceValues **attributes) {
    return This->lpVtbl->GetFormatAttributes(This,format,attributes);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetSupportedFormatProperties(IPortableDeviceServiceCapabilities* This,REFGUID format,IPortableDeviceKeyCollection **keys) {
    return This->lpVtbl->GetSupportedFormatProperties(This,format,keys);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetFormatPropertyAttributes(IPortableDeviceServiceCapabilities* This,REFGUID format,REFPROPERTYKEY property,IPortableDeviceValues **attributes) {
    return This->lpVtbl->GetFormatPropertyAttributes(This,format,property,attributes);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetSupportedEvents(IPortableDeviceServiceCapabilities* This,IPortableDevicePropVariantCollection **events) {
    return This->lpVtbl->GetSupportedEvents(This,events);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetEventAttributes(IPortableDeviceServiceCapabilities* This,REFGUID event_id,IPortableDeviceValues **attributes) {
    return This->lpVtbl->GetEventAttributes(This,event_id,attributes);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetEventParameterAttributes(IPortableDeviceServiceCapabilities* This,REFGUID event_id,REFPROPERTYKEY parameter,IPortableDeviceValues **attributes) {
    return This->lpVtbl->GetEventParameterAttributes(This,event_id,parameter,attributes);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetInheritedServices(IPortableDeviceServiceCapabilities* This,const DWORD inheritance_type,IPortableDevicePropVariantCollection **services) {
    return This->lpVtbl->GetInheritedServices(This,inheritance_type,services);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetFormatRenderingProfiles(IPortableDeviceServiceCapabilities* This,REFGUID format,IPortableDeviceValuesCollection **rendering_profiles) {
    return This->lpVtbl->GetFormatRenderingProfiles(This,format,rendering_profiles);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetSupportedCommands(IPortableDeviceServiceCapabilities* This,IPortableDeviceKeyCollection **commands) {
    return This->lpVtbl->GetSupportedCommands(This,commands);
}
static inline HRESULT IPortableDeviceServiceCapabilities_GetCommandOptions(IPortableDeviceServiceCapabilities* This,REFPROPERTYKEY command,IPortableDeviceValues **options) {
    return This->lpVtbl->GetCommandOptions(This,command,options);
}
static inline HRESULT IPortableDeviceServiceCapabilities_Cancel(IPortableDeviceServiceCapabilities* This) {
    return This->lpVtbl->Cancel(This);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceServiceCapabilities_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceServiceMethods interface
 */
#ifndef __IPortableDeviceServiceMethods_INTERFACE_DEFINED__
#define __IPortableDeviceServiceMethods_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceServiceMethods, 0xe20333c9, 0xfd34, 0x412d, 0xa3,0x81, 0xcc,0x6f,0x2d,0x82,0x0d,0xf7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e20333c9-fd34-412d-a381-cc6f2d820df7")
IPortableDeviceServiceMethods : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Invoke(
        REFGUID method_id,
        IPortableDeviceValues *parameters,
        IPortableDeviceValues **results) = 0;

    virtual HRESULT STDMETHODCALLTYPE InvokeAsync(
        REFGUID method_id,
        IPortableDeviceValues *parameters,
        IPortableDeviceServiceMethodCallback *callback) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        IPortableDeviceServiceMethodCallback *callback) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceServiceMethods, 0xe20333c9, 0xfd34, 0x412d, 0xa3,0x81, 0xcc,0x6f,0x2d,0x82,0x0d,0xf7)
#endif
#else
typedef struct IPortableDeviceServiceMethodsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceServiceMethods *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceServiceMethods *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceServiceMethods *This);

    /*** IPortableDeviceServiceMethods methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IPortableDeviceServiceMethods *This,
        REFGUID method_id,
        IPortableDeviceValues *parameters,
        IPortableDeviceValues **results);

    HRESULT (STDMETHODCALLTYPE *InvokeAsync)(
        IPortableDeviceServiceMethods *This,
        REFGUID method_id,
        IPortableDeviceValues *parameters,
        IPortableDeviceServiceMethodCallback *callback);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPortableDeviceServiceMethods *This,
        IPortableDeviceServiceMethodCallback *callback);

    END_INTERFACE
} IPortableDeviceServiceMethodsVtbl;

interface IPortableDeviceServiceMethods {
    CONST_VTBL IPortableDeviceServiceMethodsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceServiceMethods_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceServiceMethods_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceServiceMethods_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceServiceMethods methods ***/
#define IPortableDeviceServiceMethods_Invoke(This,method_id,parameters,results) (This)->lpVtbl->Invoke(This,method_id,parameters,results)
#define IPortableDeviceServiceMethods_InvokeAsync(This,method_id,parameters,callback) (This)->lpVtbl->InvokeAsync(This,method_id,parameters,callback)
#define IPortableDeviceServiceMethods_Cancel(This,callback) (This)->lpVtbl->Cancel(This,callback)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceServiceMethods_QueryInterface(IPortableDeviceServiceMethods* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceServiceMethods_AddRef(IPortableDeviceServiceMethods* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceServiceMethods_Release(IPortableDeviceServiceMethods* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceServiceMethods methods ***/
static inline HRESULT IPortableDeviceServiceMethods_Invoke(IPortableDeviceServiceMethods* This,REFGUID method_id,IPortableDeviceValues *parameters,IPortableDeviceValues **results) {
    return This->lpVtbl->Invoke(This,method_id,parameters,results);
}
static inline HRESULT IPortableDeviceServiceMethods_InvokeAsync(IPortableDeviceServiceMethods* This,REFGUID method_id,IPortableDeviceValues *parameters,IPortableDeviceServiceMethodCallback *callback) {
    return This->lpVtbl->InvokeAsync(This,method_id,parameters,callback);
}
static inline HRESULT IPortableDeviceServiceMethods_Cancel(IPortableDeviceServiceMethods* This,IPortableDeviceServiceMethodCallback *callback) {
    return This->lpVtbl->Cancel(This,callback);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceServiceMethods_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceServiceMethodCallback interface
 */
#ifndef __IPortableDeviceServiceMethodCallback_INTERFACE_DEFINED__
#define __IPortableDeviceServiceMethodCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceServiceMethodCallback, 0xc424233c, 0xafce, 0x4828, 0xa7,0x56, 0x7e,0xd7,0xa2,0x35,0x00,0x83);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c424233c-afce-4828-a756-7ed7a2350083")
IPortableDeviceServiceMethodCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnComplete(
        HRESULT hr_status,
        IPortableDeviceValues *results) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceServiceMethodCallback, 0xc424233c, 0xafce, 0x4828, 0xa7,0x56, 0x7e,0xd7,0xa2,0x35,0x00,0x83)
#endif
#else
typedef struct IPortableDeviceServiceMethodCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceServiceMethodCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceServiceMethodCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceServiceMethodCallback *This);

    /*** IPortableDeviceServiceMethodCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnComplete)(
        IPortableDeviceServiceMethodCallback *This,
        HRESULT hr_status,
        IPortableDeviceValues *results);

    END_INTERFACE
} IPortableDeviceServiceMethodCallbackVtbl;

interface IPortableDeviceServiceMethodCallback {
    CONST_VTBL IPortableDeviceServiceMethodCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceServiceMethodCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceServiceMethodCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceServiceMethodCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceServiceMethodCallback methods ***/
#define IPortableDeviceServiceMethodCallback_OnComplete(This,hr_status,results) (This)->lpVtbl->OnComplete(This,hr_status,results)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceServiceMethodCallback_QueryInterface(IPortableDeviceServiceMethodCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceServiceMethodCallback_AddRef(IPortableDeviceServiceMethodCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceServiceMethodCallback_Release(IPortableDeviceServiceMethodCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceServiceMethodCallback methods ***/
static inline HRESULT IPortableDeviceServiceMethodCallback_OnComplete(IPortableDeviceServiceMethodCallback* This,HRESULT hr_status,IPortableDeviceValues *results) {
    return This->lpVtbl->OnComplete(This,hr_status,results);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceServiceMethodCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceServiceActivation interface
 */
#ifndef __IPortableDeviceServiceActivation_INTERFACE_DEFINED__
#define __IPortableDeviceServiceActivation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceServiceActivation, 0xe56b0534, 0xd9b9, 0x425c, 0x9b,0x99, 0x75,0xf9,0x7c,0xb3,0xd7,0xc8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e56b0534-d9b9-425c-9b99-75f97cb3d7c8")
IPortableDeviceServiceActivation : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OpenAsync(
        LPCWSTR service_id,
        IPortableDeviceValues *client_info,
        IPortableDeviceServiceOpenCallback *callback) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelOpenAsync(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceServiceActivation, 0xe56b0534, 0xd9b9, 0x425c, 0x9b,0x99, 0x75,0xf9,0x7c,0xb3,0xd7,0xc8)
#endif
#else
typedef struct IPortableDeviceServiceActivationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceServiceActivation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceServiceActivation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceServiceActivation *This);

    /*** IPortableDeviceServiceActivation methods ***/
    HRESULT (STDMETHODCALLTYPE *OpenAsync)(
        IPortableDeviceServiceActivation *This,
        LPCWSTR service_id,
        IPortableDeviceValues *client_info,
        IPortableDeviceServiceOpenCallback *callback);

    HRESULT (STDMETHODCALLTYPE *CancelOpenAsync)(
        IPortableDeviceServiceActivation *This);

    END_INTERFACE
} IPortableDeviceServiceActivationVtbl;

interface IPortableDeviceServiceActivation {
    CONST_VTBL IPortableDeviceServiceActivationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceServiceActivation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceServiceActivation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceServiceActivation_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceServiceActivation methods ***/
#define IPortableDeviceServiceActivation_OpenAsync(This,service_id,client_info,callback) (This)->lpVtbl->OpenAsync(This,service_id,client_info,callback)
#define IPortableDeviceServiceActivation_CancelOpenAsync(This) (This)->lpVtbl->CancelOpenAsync(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceServiceActivation_QueryInterface(IPortableDeviceServiceActivation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceServiceActivation_AddRef(IPortableDeviceServiceActivation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceServiceActivation_Release(IPortableDeviceServiceActivation* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceServiceActivation methods ***/
static inline HRESULT IPortableDeviceServiceActivation_OpenAsync(IPortableDeviceServiceActivation* This,LPCWSTR service_id,IPortableDeviceValues *client_info,IPortableDeviceServiceOpenCallback *callback) {
    return This->lpVtbl->OpenAsync(This,service_id,client_info,callback);
}
static inline HRESULT IPortableDeviceServiceActivation_CancelOpenAsync(IPortableDeviceServiceActivation* This) {
    return This->lpVtbl->CancelOpenAsync(This);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceServiceActivation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceServiceOpenCallback interface
 */
#ifndef __IPortableDeviceServiceOpenCallback_INTERFACE_DEFINED__
#define __IPortableDeviceServiceOpenCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceServiceOpenCallback, 0xbced49c8, 0x8efe, 0x41ed, 0x96,0x0b, 0x61,0x31,0x3a,0xbd,0x47,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bced49c8-8efe-41ed-960b-61313abd47a9")
IPortableDeviceServiceOpenCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnComplete(
        HRESULT hr_status) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceServiceOpenCallback, 0xbced49c8, 0x8efe, 0x41ed, 0x96,0x0b, 0x61,0x31,0x3a,0xbd,0x47,0xa9)
#endif
#else
typedef struct IPortableDeviceServiceOpenCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceServiceOpenCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceServiceOpenCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceServiceOpenCallback *This);

    /*** IPortableDeviceServiceOpenCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnComplete)(
        IPortableDeviceServiceOpenCallback *This,
        HRESULT hr_status);

    END_INTERFACE
} IPortableDeviceServiceOpenCallbackVtbl;

interface IPortableDeviceServiceOpenCallback {
    CONST_VTBL IPortableDeviceServiceOpenCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceServiceOpenCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceServiceOpenCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceServiceOpenCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceServiceOpenCallback methods ***/
#define IPortableDeviceServiceOpenCallback_OnComplete(This,hr_status) (This)->lpVtbl->OnComplete(This,hr_status)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceServiceOpenCallback_QueryInterface(IPortableDeviceServiceOpenCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceServiceOpenCallback_AddRef(IPortableDeviceServiceOpenCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceServiceOpenCallback_Release(IPortableDeviceServiceOpenCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceServiceOpenCallback methods ***/
static inline HRESULT IPortableDeviceServiceOpenCallback_OnComplete(IPortableDeviceServiceOpenCallback* This,HRESULT hr_status) {
    return This->lpVtbl->OnComplete(This,hr_status);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceServiceOpenCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceDispatchFactory interface
 */
#ifndef __IPortableDeviceDispatchFactory_INTERFACE_DEFINED__
#define __IPortableDeviceDispatchFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceDispatchFactory, 0x5e1eafc3, 0xe3d7, 0x4132, 0x96,0xfa, 0x75,0x9c,0x0f,0x9d,0x1e,0x0f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5e1eafc3-e3d7-4132-96fa-759c0f9d1e0f")
IPortableDeviceDispatchFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDeviceDispatch(
        LPCWSTR device_id,
        IDispatch **device_dispatch) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceDispatchFactory, 0x5e1eafc3, 0xe3d7, 0x4132, 0x96,0xfa, 0x75,0x9c,0x0f,0x9d,0x1e,0x0f)
#endif
#else
typedef struct IPortableDeviceDispatchFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceDispatchFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceDispatchFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceDispatchFactory *This);

    /*** IPortableDeviceDispatchFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeviceDispatch)(
        IPortableDeviceDispatchFactory *This,
        LPCWSTR device_id,
        IDispatch **device_dispatch);

    END_INTERFACE
} IPortableDeviceDispatchFactoryVtbl;

interface IPortableDeviceDispatchFactory {
    CONST_VTBL IPortableDeviceDispatchFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceDispatchFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceDispatchFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceDispatchFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IPortableDeviceDispatchFactory methods ***/
#define IPortableDeviceDispatchFactory_GetDeviceDispatch(This,device_id,device_dispatch) (This)->lpVtbl->GetDeviceDispatch(This,device_id,device_dispatch)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceDispatchFactory_QueryInterface(IPortableDeviceDispatchFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceDispatchFactory_AddRef(IPortableDeviceDispatchFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceDispatchFactory_Release(IPortableDeviceDispatchFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IPortableDeviceDispatchFactory methods ***/
static inline HRESULT IPortableDeviceDispatchFactory_GetDeviceDispatch(IPortableDeviceDispatchFactory* This,LPCWSTR device_id,IDispatch **device_dispatch) {
    return This->lpVtbl->GetDeviceDispatch(This,device_id,device_dispatch);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceDispatchFactory_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPortableDeviceWebControl interface
 */
#ifndef __IPortableDeviceWebControl_INTERFACE_DEFINED__
#define __IPortableDeviceWebControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPortableDeviceWebControl, 0x94fc7953, 0x5ca1, 0x483a, 0x8a,0xee, 0xdf,0x52,0xe7,0x74,0x7d,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("94fc7953-5ca1-483a-8aee-df52e7747d00")
IPortableDeviceWebControl : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE GetDeviceFromId(
        BSTR device_id,
        IDispatch **device) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceFromIdAsync(
        BSTR device_id,
        IDispatch *completion_handler,
        IDispatch *error_handler) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPortableDeviceWebControl, 0x94fc7953, 0x5ca1, 0x483a, 0x8a,0xee, 0xdf,0x52,0xe7,0x74,0x7d,0x00)
#endif
#else
typedef struct IPortableDeviceWebControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPortableDeviceWebControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPortableDeviceWebControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPortableDeviceWebControl *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IPortableDeviceWebControl *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IPortableDeviceWebControl *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IPortableDeviceWebControl *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IPortableDeviceWebControl *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IPortableDeviceWebControl methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeviceFromId)(
        IPortableDeviceWebControl *This,
        BSTR device_id,
        IDispatch **device);

    HRESULT (STDMETHODCALLTYPE *GetDeviceFromIdAsync)(
        IPortableDeviceWebControl *This,
        BSTR device_id,
        IDispatch *completion_handler,
        IDispatch *error_handler);

    END_INTERFACE
} IPortableDeviceWebControlVtbl;

interface IPortableDeviceWebControl {
    CONST_VTBL IPortableDeviceWebControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPortableDeviceWebControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPortableDeviceWebControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPortableDeviceWebControl_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IPortableDeviceWebControl_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IPortableDeviceWebControl_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IPortableDeviceWebControl_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IPortableDeviceWebControl_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IPortableDeviceWebControl methods ***/
#define IPortableDeviceWebControl_GetDeviceFromId(This,device_id,device) (This)->lpVtbl->GetDeviceFromId(This,device_id,device)
#define IPortableDeviceWebControl_GetDeviceFromIdAsync(This,device_id,completion_handler,error_handler) (This)->lpVtbl->GetDeviceFromIdAsync(This,device_id,completion_handler,error_handler)
#else
/*** IUnknown methods ***/
static inline HRESULT IPortableDeviceWebControl_QueryInterface(IPortableDeviceWebControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPortableDeviceWebControl_AddRef(IPortableDeviceWebControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPortableDeviceWebControl_Release(IPortableDeviceWebControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IPortableDeviceWebControl_GetTypeInfoCount(IPortableDeviceWebControl* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IPortableDeviceWebControl_GetTypeInfo(IPortableDeviceWebControl* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IPortableDeviceWebControl_GetIDsOfNames(IPortableDeviceWebControl* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IPortableDeviceWebControl_Invoke(IPortableDeviceWebControl* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IPortableDeviceWebControl methods ***/
static inline HRESULT IPortableDeviceWebControl_GetDeviceFromId(IPortableDeviceWebControl* This,BSTR device_id,IDispatch **device) {
    return This->lpVtbl->GetDeviceFromId(This,device_id,device);
}
static inline HRESULT IPortableDeviceWebControl_GetDeviceFromIdAsync(IPortableDeviceWebControl* This,BSTR device_id,IDispatch *completion_handler,IDispatch *error_handler) {
    return This->lpVtbl->GetDeviceFromIdAsync(This,device_id,completion_handler,error_handler);
}
#endif
#endif

#endif


#endif  /* __IPortableDeviceWebControl_INTERFACE_DEFINED__ */

#ifndef __PortableDeviceApiLib_LIBRARY_DEFINED__
#define __PortableDeviceApiLib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_PortableDeviceApiLib, 0x1f001332, 0x1a57, 0x4934, 0xbe,0x31, 0xaf,0xfc,0x99,0xf4,0xee,0x0a);

/*****************************************************************************
 * PortableDevice coclass
 */

DEFINE_GUID(CLSID_PortableDevice, 0x728a21c5, 0x3d9e, 0x48d7, 0x98,0x10, 0x86,0x48,0x48,0xf0,0xf4,0x04);

#ifdef __cplusplus
class DECLSPEC_UUID("728a21c5-3d9e-48d7-9810-864848f0f404") PortableDevice;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDevice, 0x728a21c5, 0x3d9e, 0x48d7, 0x98,0x10, 0x86,0x48,0x48,0xf0,0xf4,0x04)
#endif
#endif

/*****************************************************************************
 * PortableDeviceManager coclass
 */

DEFINE_GUID(CLSID_PortableDeviceManager, 0x0af10cec, 0x2ecd, 0x4b92, 0x95,0x81, 0x34,0xf6,0xae,0x06,0x37,0xf3);

#ifdef __cplusplus
class DECLSPEC_UUID("0af10cec-2ecd-4b92-9581-34f6ae0637f3") PortableDeviceManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDeviceManager, 0x0af10cec, 0x2ecd, 0x4b92, 0x95,0x81, 0x34,0xf6,0xae,0x06,0x37,0xf3)
#endif
#endif

/*****************************************************************************
 * PortableDeviceService coclass
 */

DEFINE_GUID(CLSID_PortableDeviceService, 0xef5db4c2, 0x9312, 0x422c, 0x91,0x52, 0x41,0x1c,0xd9,0xc4,0xdd,0x84);

#ifdef __cplusplus
class DECLSPEC_UUID("ef5db4c2-9312-422c-9152-411cd9c4dd84") PortableDeviceService;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDeviceService, 0xef5db4c2, 0x9312, 0x422c, 0x91,0x52, 0x41,0x1c,0xd9,0xc4,0xdd,0x84)
#endif
#endif

/*****************************************************************************
 * PortableDeviceDispatchFactory coclass
 */

DEFINE_GUID(CLSID_PortableDeviceDispatchFactory, 0x43232233, 0x8338, 0x4658, 0xae,0x01, 0x0b,0x4a,0xe8,0x30,0xb6,0xb0);

#ifdef __cplusplus
class DECLSPEC_UUID("*************-4658-ae01-0b4ae830b6b0") PortableDeviceDispatchFactory;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDeviceDispatchFactory, 0x43232233, 0x8338, 0x4658, 0xae,0x01, 0x0b,0x4a,0xe8,0x30,0xb6,0xb0)
#endif
#endif

/*****************************************************************************
 * PortableDeviceFTM coclass
 */

DEFINE_GUID(CLSID_PortableDeviceFTM, 0xf7c0039a, 0x4762, 0x488a, 0xb4,0xb3, 0x76,0x0e,0xf9,0xa1,0xba,0x9b);

#ifdef __cplusplus
class DECLSPEC_UUID("f7c0039a-4762-488a-b4b3-760ef9a1ba9b") PortableDeviceFTM;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDeviceFTM, 0xf7c0039a, 0x4762, 0x488a, 0xb4,0xb3, 0x76,0x0e,0xf9,0xa1,0xba,0x9b)
#endif
#endif

/*****************************************************************************
 * PortableDeviceServiceFTM coclass
 */

DEFINE_GUID(CLSID_PortableDeviceServiceFTM, 0x1649b154, 0xc794, 0x497a, 0x9b,0x03, 0xf3,0xf0,0x12,0x13,0x02,0xf3);

#ifdef __cplusplus
class DECLSPEC_UUID("1649b154-c794-497a-9b03-f3f0121302f3") PortableDeviceServiceFTM;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDeviceServiceFTM, 0x1649b154, 0xc794, 0x497a, 0x9b,0x03, 0xf3,0xf0,0x12,0x13,0x02,0xf3)
#endif
#endif

/*****************************************************************************
 * PortableDeviceWebControl coclass
 */

DEFINE_GUID(CLSID_PortableDeviceWebControl, 0x186dd02c, 0x2dec, 0x41b5, 0xa7,0xd4, 0xb5,0x90,0x56,0xfa,0xde,0x51);

#ifdef __cplusplus
class DECLSPEC_UUID("186dd02c-2dec-41b5-a7d4-b59056fade51") PortableDeviceWebControl;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PortableDeviceWebControl, 0x186dd02c, 0x2dec, 0x41b5, 0xa7,0xd4, 0xb5,0x90,0x56,0xfa,0xde,0x51)
#endif
#endif

#endif /* __PortableDeviceApiLib_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __portabledeviceapi_h__ */
