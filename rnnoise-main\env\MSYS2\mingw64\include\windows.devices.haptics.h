/*** Autogenerated by WIDL 10.12 from include/windows.devices.haptics.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_devices_haptics_h__
#define __windows_devices_haptics_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback ABI::Windows::Devices::Haptics::ISimpleHapticsControllerFeedback
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Haptics {
                interface ISimpleHapticsControllerFeedback;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController ABI::Windows::Devices::Haptics::ISimpleHapticsController
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Haptics {
                interface ISimpleHapticsController;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CHaptics_CSimpleHapticsControllerFeedback_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CHaptics_CSimpleHapticsControllerFeedback_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Haptics {
                class SimpleHapticsControllerFeedback;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CHaptics_CSimpleHapticsControllerFeedback __x_ABI_CWindows_CDevices_CHaptics_CSimpleHapticsControllerFeedback;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CHaptics_CSimpleHapticsControllerFeedback_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CHaptics_CSimpleHapticsController_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CHaptics_CSimpleHapticsController_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Haptics {
                class SimpleHapticsController;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CHaptics_CSimpleHapticsController __x_ABI_CWindows_CDevices_CHaptics_CSimpleHapticsController;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CHaptics_CSimpleHapticsController_FWD_DEFINED__ */

#ifndef ____FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
#define ____FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Devices::Haptics::SimpleHapticsController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
#define ____FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Devices::Haptics::SimpleHapticsController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
#define ____FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController;
#ifdef __cplusplus
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Devices::Haptics::SimpleHapticsController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsControllerFeedback* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback ABI::Windows::Devices::Haptics::ISimpleHapticsControllerFeedback
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Haptics {
                interface ISimpleHapticsControllerFeedback;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController ABI::Windows::Devices::Haptics::ISimpleHapticsController
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Haptics {
                interface ISimpleHapticsController;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
#define ____FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Devices::Haptics::SimpleHapticsController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
#define ____FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Devices::Haptics::SimpleHapticsController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
#define ____FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController;
#ifdef __cplusplus
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Devices::Haptics::SimpleHapticsController* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsControllerFeedback* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ISimpleHapticsControllerFeedback interface
 */
#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback, 0x3d577ef8, 0x4cee, 0x11e6, 0xb5,0x35, 0x00,0x1b,0xdc,0x06,0xab,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Haptics {
                MIDL_INTERFACE("3d577ef8-4cee-11e6-b535-001bdc06ab3b")
                ISimpleHapticsControllerFeedback : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Waveform(
                        UINT16 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Duration(
                        ABI::Windows::Foundation::TimeSpan *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback, 0x3d577ef8, 0x4cee, 0x11e6, 0xb5,0x35, 0x00,0x1b,0xdc,0x06,0xab,0x3b)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *This,
        TrustLevel *trustLevel);

    /*** ISimpleHapticsControllerFeedback methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Waveform)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *This,
        UINT16 *value);

    HRESULT (STDMETHODCALLTYPE *get_Duration)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedbackVtbl;

interface __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback {
    CONST_VTBL __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISimpleHapticsControllerFeedback methods ***/
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_get_Waveform(This,value) (This)->lpVtbl->get_Waveform(This,value)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_get_Duration(This,value) (This)->lpVtbl->get_Duration(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_QueryInterface(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_AddRef(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_Release(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_GetIids(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_GetTrustLevel(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISimpleHapticsControllerFeedback methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_get_Waveform(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback* This,UINT16 *value) {
    return This->lpVtbl->get_Waveform(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_get_Duration(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback* This,__x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_Duration(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Haptics
#define IID_ISimpleHapticsControllerFeedback IID___x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback
#define ISimpleHapticsControllerFeedbackVtbl __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedbackVtbl
#define ISimpleHapticsControllerFeedback __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback
#define ISimpleHapticsControllerFeedback_QueryInterface __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_QueryInterface
#define ISimpleHapticsControllerFeedback_AddRef __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_AddRef
#define ISimpleHapticsControllerFeedback_Release __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_Release
#define ISimpleHapticsControllerFeedback_GetIids __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_GetIids
#define ISimpleHapticsControllerFeedback_GetRuntimeClassName __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_GetRuntimeClassName
#define ISimpleHapticsControllerFeedback_GetTrustLevel __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_GetTrustLevel
#define ISimpleHapticsControllerFeedback_get_Waveform __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_get_Waveform
#define ISimpleHapticsControllerFeedback_get_Duration __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_get_Duration
#endif /* WIDL_using_Windows_Devices_Haptics */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * ISimpleHapticsController interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController, 0x3d577ef9, 0x4cee, 0x11e6, 0xb5,0x35, 0x00,0x1b,0xdc,0x06,0xab,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Haptics {
                MIDL_INTERFACE("3d577ef9-4cee-11e6-b535-001bdc06ab3b")
                ISimpleHapticsController : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Id(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SupportedFeedback(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsControllerFeedback* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsIntensitySupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsPlayCountSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsPlayDurationSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsReplayPauseIntervalSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE StopFeedback(
                        ) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SendHapticFeedback(
                        ABI::Windows::Devices::Haptics::ISimpleHapticsControllerFeedback *feedback) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SendHapticFeedbackWithIntensity(
                        ABI::Windows::Devices::Haptics::ISimpleHapticsControllerFeedback *feedback,
                        DOUBLE intensity) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SendHapticFeedbackForDuration(
                        ABI::Windows::Devices::Haptics::ISimpleHapticsControllerFeedback *feedback,
                        DOUBLE intensity,
                        ABI::Windows::Foundation::TimeSpan duration) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SendHapticFeedbackForPlayCount(
                        ABI::Windows::Devices::Haptics::ISimpleHapticsControllerFeedback *feedback,
                        DOUBLE intensity,
                        INT32 count,
                        ABI::Windows::Foundation::TimeSpan interval) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController, 0x3d577ef9, 0x4cee, 0x11e6, 0xb5,0x35, 0x00,0x1b,0xdc,0x06,0xab,0x3b)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        TrustLevel *trustLevel);

    /*** ISimpleHapticsController methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_SupportedFeedback)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback **value);

    HRESULT (STDMETHODCALLTYPE *get_IsIntensitySupported)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsPlayCountSupported)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsPlayDurationSupported)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsReplayPauseIntervalSupported)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *StopFeedback)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This);

    HRESULT (STDMETHODCALLTYPE *SendHapticFeedback)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *feedback);

    HRESULT (STDMETHODCALLTYPE *SendHapticFeedbackWithIntensity)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *feedback,
        DOUBLE intensity);

    HRESULT (STDMETHODCALLTYPE *SendHapticFeedbackForDuration)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *feedback,
        DOUBLE intensity,
        __x_ABI_CWindows_CFoundation_CTimeSpan duration);

    HRESULT (STDMETHODCALLTYPE *SendHapticFeedbackForPlayCount)(
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *This,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *feedback,
        DOUBLE intensity,
        INT32 count,
        __x_ABI_CWindows_CFoundation_CTimeSpan interval);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerVtbl;

interface __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController {
    CONST_VTBL __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISimpleHapticsController methods ***/
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_SupportedFeedback(This,value) (This)->lpVtbl->get_SupportedFeedback(This,value)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsIntensitySupported(This,value) (This)->lpVtbl->get_IsIntensitySupported(This,value)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsPlayCountSupported(This,value) (This)->lpVtbl->get_IsPlayCountSupported(This,value)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsPlayDurationSupported(This,value) (This)->lpVtbl->get_IsPlayDurationSupported(This,value)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsReplayPauseIntervalSupported(This,value) (This)->lpVtbl->get_IsReplayPauseIntervalSupported(This,value)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_StopFeedback(This) (This)->lpVtbl->StopFeedback(This)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedback(This,feedback) (This)->lpVtbl->SendHapticFeedback(This,feedback)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedbackWithIntensity(This,feedback,intensity) (This)->lpVtbl->SendHapticFeedbackWithIntensity(This,feedback,intensity)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedbackForDuration(This,feedback,intensity,duration) (This)->lpVtbl->SendHapticFeedbackForDuration(This,feedback,intensity,duration)
#define __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedbackForPlayCount(This,feedback,intensity,count,interval) (This)->lpVtbl->SendHapticFeedbackForPlayCount(This,feedback,intensity,count,interval)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_QueryInterface(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_AddRef(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_Release(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_GetIids(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_GetTrustLevel(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISimpleHapticsController methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_Id(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,HSTRING *value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_SupportedFeedback(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback **value) {
    return This->lpVtbl->get_SupportedFeedback(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsIntensitySupported(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,boolean *value) {
    return This->lpVtbl->get_IsIntensitySupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsPlayCountSupported(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,boolean *value) {
    return This->lpVtbl->get_IsPlayCountSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsPlayDurationSupported(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,boolean *value) {
    return This->lpVtbl->get_IsPlayDurationSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsReplayPauseIntervalSupported(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,boolean *value) {
    return This->lpVtbl->get_IsReplayPauseIntervalSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_StopFeedback(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This) {
    return This->lpVtbl->StopFeedback(This);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedback(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *feedback) {
    return This->lpVtbl->SendHapticFeedback(This,feedback);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedbackWithIntensity(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *feedback,DOUBLE intensity) {
    return This->lpVtbl->SendHapticFeedbackWithIntensity(This,feedback,intensity);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedbackForDuration(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *feedback,DOUBLE intensity,__x_ABI_CWindows_CFoundation_CTimeSpan duration) {
    return This->lpVtbl->SendHapticFeedbackForDuration(This,feedback,intensity,duration);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedbackForPlayCount(__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController* This,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *feedback,DOUBLE intensity,INT32 count,__x_ABI_CWindows_CFoundation_CTimeSpan interval) {
    return This->lpVtbl->SendHapticFeedbackForPlayCount(This,feedback,intensity,count,interval);
}
#endif
#ifdef WIDL_using_Windows_Devices_Haptics
#define IID_ISimpleHapticsController IID___x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController
#define ISimpleHapticsControllerVtbl __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerVtbl
#define ISimpleHapticsController __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController
#define ISimpleHapticsController_QueryInterface __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_QueryInterface
#define ISimpleHapticsController_AddRef __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_AddRef
#define ISimpleHapticsController_Release __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_Release
#define ISimpleHapticsController_GetIids __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_GetIids
#define ISimpleHapticsController_GetRuntimeClassName __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_GetRuntimeClassName
#define ISimpleHapticsController_GetTrustLevel __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_GetTrustLevel
#define ISimpleHapticsController_get_Id __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_Id
#define ISimpleHapticsController_get_SupportedFeedback __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_SupportedFeedback
#define ISimpleHapticsController_get_IsIntensitySupported __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsIntensitySupported
#define ISimpleHapticsController_get_IsPlayCountSupported __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsPlayCountSupported
#define ISimpleHapticsController_get_IsPlayDurationSupported __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsPlayDurationSupported
#define ISimpleHapticsController_get_IsReplayPauseIntervalSupported __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_get_IsReplayPauseIntervalSupported
#define ISimpleHapticsController_StopFeedback __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_StopFeedback
#define ISimpleHapticsController_SendHapticFeedback __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedback
#define ISimpleHapticsController_SendHapticFeedbackWithIntensity __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedbackWithIntensity
#define ISimpleHapticsController_SendHapticFeedbackForDuration __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedbackForDuration
#define ISimpleHapticsController_SendHapticFeedbackForPlayCount __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_SendHapticFeedbackForPlayCount
#endif /* WIDL_using_Windows_Devices_Haptics */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Devices.Haptics.SimpleHapticsControllerFeedback
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Devices_Haptics_SimpleHapticsControllerFeedback_DEFINED
#define RUNTIMECLASS_Windows_Devices_Haptics_SimpleHapticsControllerFeedback_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Haptics_SimpleHapticsControllerFeedback[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','H','a','p','t','i','c','s','.','S','i','m','p','l','e','H','a','p','t','i','c','s','C','o','n','t','r','o','l','l','e','r','F','e','e','d','b','a','c','k',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Haptics_SimpleHapticsControllerFeedback[] = L"Windows.Devices.Haptics.SimpleHapticsControllerFeedback";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Haptics_SimpleHapticsControllerFeedback[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','H','a','p','t','i','c','s','.','S','i','m','p','l','e','H','a','p','t','i','c','s','C','o','n','t','r','o','l','l','e','r','F','e','e','d','b','a','c','k',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Haptics_SimpleHapticsControllerFeedback_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Devices.Haptics.SimpleHapticsController
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Devices_Haptics_SimpleHapticsController_DEFINED
#define RUNTIMECLASS_Windows_Devices_Haptics_SimpleHapticsController_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Haptics_SimpleHapticsController[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','H','a','p','t','i','c','s','.','S','i','m','p','l','e','H','a','p','t','i','c','s','C','o','n','t','r','o','l','l','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Haptics_SimpleHapticsController[] = L"Windows.Devices.Haptics.SimpleHapticsController";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Haptics_SimpleHapticsController[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','H','a','p','t','i','c','s','.','S','i','m','p','l','e','H','a','p','t','i','c','s','C','o','n','t','r','o','l','l','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Haptics_SimpleHapticsController_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IIterator<ABI::Windows::Devices::Haptics::SimpleHapticsController* > interface
 */
#ifndef ____FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController, 0x3c501ba4, 0xeda4, 0x5238, 0xbd,0xb7, 0xd1,0x0b,0xa3,0x50,0xcd,0x83);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("3c501ba4-eda4-5238-bdb7-d10ba350cd83")
                IIterator<ABI::Windows::Devices::Haptics::SimpleHapticsController* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Haptics::SimpleHapticsController*, ABI::Windows::Devices::Haptics::ISimpleHapticsController* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController, 0x3c501ba4, 0xeda4, 0x5238, 0xbd,0xb7, 0xd1,0x0b,0xa3,0x50,0xcd,0x83)
#endif
#else
typedef struct __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl;

interface __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController {
    CONST_VTBL __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface(__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef(__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release(__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids(__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName(__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel(__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_Current(__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_HasCurrent(__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_MoveNext(__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetMany(__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,UINT32 items_size,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_SimpleHapticsController IID___FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController
#define IIterator_SimpleHapticsControllerVtbl __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl
#define IIterator_SimpleHapticsController __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController
#define IIterator_SimpleHapticsController_QueryInterface __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface
#define IIterator_SimpleHapticsController_AddRef __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef
#define IIterator_SimpleHapticsController_Release __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release
#define IIterator_SimpleHapticsController_GetIids __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids
#define IIterator_SimpleHapticsController_GetRuntimeClassName __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName
#define IIterator_SimpleHapticsController_GetTrustLevel __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel
#define IIterator_SimpleHapticsController_get_Current __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_Current
#define IIterator_SimpleHapticsController_get_HasCurrent __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_HasCurrent
#define IIterator_SimpleHapticsController_MoveNext __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_MoveNext
#define IIterator_SimpleHapticsController_GetMany __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Devices::Haptics::SimpleHapticsController* > interface
 */
#ifndef ____FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController, 0xb50da692, 0x4a2b, 0x5c8a, 0x8e,0x14, 0x04,0x39,0xc0,0xb1,0xdb,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("b50da692-4a2b-5c8a-8e14-0439c0b1dba4")
                IIterable<ABI::Windows::Devices::Haptics::SimpleHapticsController* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Haptics::SimpleHapticsController*, ABI::Windows::Devices::Haptics::ISimpleHapticsController* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController, 0xb50da692, 0x4a2b, 0x5c8a, 0x8e,0x14, 0x04,0x39,0xc0,0xb1,0xdb,0xa4)
#endif
#else
typedef struct __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        __FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController **value);

    END_INTERFACE
} __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl;

interface __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController {
    CONST_VTBL __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
#define __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface(__FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef(__FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release(__FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids(__FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName(__FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel(__FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_First(__FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,__FIIterator_1_Windows__CDevices__CHaptics__CSimpleHapticsController **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_SimpleHapticsController IID___FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController
#define IIterable_SimpleHapticsControllerVtbl __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl
#define IIterable_SimpleHapticsController __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController
#define IIterable_SimpleHapticsController_QueryInterface __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface
#define IIterable_SimpleHapticsController_AddRef __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef
#define IIterable_SimpleHapticsController_Release __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release
#define IIterable_SimpleHapticsController_GetIids __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids
#define IIterable_SimpleHapticsController_GetRuntimeClassName __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName
#define IIterable_SimpleHapticsController_GetTrustLevel __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel
#define IIterable_SimpleHapticsController_First __FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsController* > interface
 */
#ifndef ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController, 0x5390f01e, 0xc701, 0x5382, 0x97,0xcc, 0x94,0xea,0xac,0x4b,0x6c,0xbf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("5390f01e-c701-5382-97cc-94eaac4b6cbf")
                IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsController* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Haptics::SimpleHapticsController*, ABI::Windows::Devices::Haptics::ISimpleHapticsController* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController, 0x5390f01e, 0xc701, 0x5382, 0x97,0xcc, 0x94,0xea,0xac,0x4b,0x6c,0xbf)
#endif
#else
typedef struct __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        UINT32 index,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl;

interface __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController {
    CONST_VTBL __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetAt(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,UINT32 index,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_Size(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_IndexOf(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetMany(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_SimpleHapticsController IID___FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController
#define IVectorView_SimpleHapticsControllerVtbl __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl
#define IVectorView_SimpleHapticsController __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController
#define IVectorView_SimpleHapticsController_QueryInterface __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface
#define IVectorView_SimpleHapticsController_AddRef __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef
#define IVectorView_SimpleHapticsController_Release __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release
#define IVectorView_SimpleHapticsController_GetIids __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids
#define IVectorView_SimpleHapticsController_GetRuntimeClassName __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName
#define IVectorView_SimpleHapticsController_GetTrustLevel __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel
#define IVectorView_SimpleHapticsController_GetAt __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetAt
#define IVectorView_SimpleHapticsController_get_Size __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_Size
#define IVectorView_SimpleHapticsController_IndexOf __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_IndexOf
#define IVectorView_SimpleHapticsController_GetMany __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Devices::Haptics::SimpleHapticsController* > interface
 */
#ifndef ____FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController, 0xdd88b8e7, 0x6821, 0x56fc, 0xb1,0xe7, 0x9c,0xa4,0xa7,0x85,0x5a,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("dd88b8e7-6821-56fc-b1e7-9ca4a7855ae0")
                IVector<ABI::Windows::Devices::Haptics::SimpleHapticsController* > : IVector_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Haptics::SimpleHapticsController*, ABI::Windows::Devices::Haptics::ISimpleHapticsController* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController, 0xdd88b8e7, 0x6821, 0x56fc, 0xb1,0xe7, 0x9c,0xa4,0xa7,0x85,0x5a,0xe0)
#endif
#else
typedef struct __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        UINT32 index,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        UINT32 index,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        UINT32 index,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController *This,
        UINT32 count,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **items);

    END_INTERFACE
} __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl;

interface __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController {
    CONST_VTBL __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Devices::Haptics::SimpleHapticsController* > methods ***/
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetAt(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,UINT32 index,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_Size(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetView(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsController **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_IndexOf(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_SetAt(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,UINT32 index,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_InsertAt(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,UINT32 index,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_RemoveAt(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Append(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_RemoveAtEnd(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Clear(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetMany(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_ReplaceAll(__FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController* This,UINT32 count,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsController **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_SimpleHapticsController IID___FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController
#define IVector_SimpleHapticsControllerVtbl __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerVtbl
#define IVector_SimpleHapticsController __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController
#define IVector_SimpleHapticsController_QueryInterface __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_QueryInterface
#define IVector_SimpleHapticsController_AddRef __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_AddRef
#define IVector_SimpleHapticsController_Release __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Release
#define IVector_SimpleHapticsController_GetIids __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetIids
#define IVector_SimpleHapticsController_GetRuntimeClassName __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetRuntimeClassName
#define IVector_SimpleHapticsController_GetTrustLevel __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetTrustLevel
#define IVector_SimpleHapticsController_GetAt __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetAt
#define IVector_SimpleHapticsController_get_Size __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_get_Size
#define IVector_SimpleHapticsController_GetView __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetView
#define IVector_SimpleHapticsController_IndexOf __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_IndexOf
#define IVector_SimpleHapticsController_SetAt __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_SetAt
#define IVector_SimpleHapticsController_InsertAt __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_InsertAt
#define IVector_SimpleHapticsController_RemoveAt __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_RemoveAt
#define IVector_SimpleHapticsController_Append __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Append
#define IVector_SimpleHapticsController_RemoveAtEnd __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_RemoveAtEnd
#define IVector_SimpleHapticsController_Clear __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_Clear
#define IVector_SimpleHapticsController_GetMany __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_GetMany
#define IVector_SimpleHapticsController_ReplaceAll __FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CDevices__CHaptics__CSimpleHapticsController_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsControllerFeedback* > interface
 */
#ifndef ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback, 0x51f54b04, 0xbb9d, 0x5c7b, 0x8f,0x5f, 0x67,0xf8,0xca,0xf4,0xb0,0x03);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("51f54b04-bb9d-5c7b-8f5f-67f8caf4b003")
                IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsControllerFeedback* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Haptics::SimpleHapticsControllerFeedback*, ABI::Windows::Devices::Haptics::ISimpleHapticsControllerFeedback* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback, 0x51f54b04, 0xbb9d, 0x5c7b, 0x8f,0x5f, 0x67,0xf8,0xca,0xf4,0xb0,0x03)
#endif
#else
typedef struct __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsControllerFeedback* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback *This,
        UINT32 index,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback *This,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedbackVtbl;

interface __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback {
    CONST_VTBL __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsControllerFeedback* > methods ***/
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_QueryInterface(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_AddRef(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_Release(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetIids(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetRuntimeClassName(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetTrustLevel(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Devices::Haptics::SimpleHapticsControllerFeedback* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetAt(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback* This,UINT32 index,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_get_Size(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_IndexOf(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback* This,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetMany(__FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CDevices_CHaptics_CISimpleHapticsControllerFeedback **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_SimpleHapticsControllerFeedback IID___FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback
#define IVectorView_SimpleHapticsControllerFeedbackVtbl __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedbackVtbl
#define IVectorView_SimpleHapticsControllerFeedback __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback
#define IVectorView_SimpleHapticsControllerFeedback_QueryInterface __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_QueryInterface
#define IVectorView_SimpleHapticsControllerFeedback_AddRef __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_AddRef
#define IVectorView_SimpleHapticsControllerFeedback_Release __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_Release
#define IVectorView_SimpleHapticsControllerFeedback_GetIids __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetIids
#define IVectorView_SimpleHapticsControllerFeedback_GetRuntimeClassName __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetRuntimeClassName
#define IVectorView_SimpleHapticsControllerFeedback_GetTrustLevel __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetTrustLevel
#define IVectorView_SimpleHapticsControllerFeedback_GetAt __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetAt
#define IVectorView_SimpleHapticsControllerFeedback_get_Size __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_get_Size
#define IVectorView_SimpleHapticsControllerFeedback_IndexOf __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_IndexOf
#define IVectorView_SimpleHapticsControllerFeedback_GetMany __FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CDevices__CHaptics__CSimpleHapticsControllerFeedback_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_devices_haptics_h__ */
