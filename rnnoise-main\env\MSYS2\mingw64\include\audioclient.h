/*** Autogenerated by WIDL 10.12 from include/audioclient.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __audioclient_h__
#define __audioclient_h__

/* Forward declarations */

#ifndef __IAudioClient_FWD_DEFINED__
#define __IAudioClient_FWD_DEFINED__
typedef interface IAudioClient IAudioClient;
#ifdef __cplusplus
interface IAudioClient;
#endif /* __cplusplus */
#endif

#ifndef __IAudioClient2_FWD_DEFINED__
#define __IAudioClient2_FWD_DEFINED__
typedef interface IAudioClient2 IAudioClient2;
#ifdef __cplusplus
interface IAudioClient2;
#endif /* __cplusplus */
#endif

#ifndef __IAudioClient3_FWD_DEFINED__
#define __IAudioClient3_FWD_DEFINED__
typedef interface IAudioClient3 IAudioClient3;
#ifdef __cplusplus
interface IAudioClient3;
#endif /* __cplusplus */
#endif

#ifndef __IAudioRenderClient_FWD_DEFINED__
#define __IAudioRenderClient_FWD_DEFINED__
typedef interface IAudioRenderClient IAudioRenderClient;
#ifdef __cplusplus
interface IAudioRenderClient;
#endif /* __cplusplus */
#endif

#ifndef __IAudioCaptureClient_FWD_DEFINED__
#define __IAudioCaptureClient_FWD_DEFINED__
typedef interface IAudioCaptureClient IAudioCaptureClient;
#ifdef __cplusplus
interface IAudioCaptureClient;
#endif /* __cplusplus */
#endif

#ifndef __IAudioClock_FWD_DEFINED__
#define __IAudioClock_FWD_DEFINED__
typedef interface IAudioClock IAudioClock;
#ifdef __cplusplus
interface IAudioClock;
#endif /* __cplusplus */
#endif

#ifndef __IAudioClock2_FWD_DEFINED__
#define __IAudioClock2_FWD_DEFINED__
typedef interface IAudioClock2 IAudioClock2;
#ifdef __cplusplus
interface IAudioClock2;
#endif /* __cplusplus */
#endif

#ifndef __IAudioClockAdjustment_FWD_DEFINED__
#define __IAudioClockAdjustment_FWD_DEFINED__
typedef interface IAudioClockAdjustment IAudioClockAdjustment;
#ifdef __cplusplus
interface IAudioClockAdjustment;
#endif /* __cplusplus */
#endif

#ifndef __ISimpleAudioVolume_FWD_DEFINED__
#define __ISimpleAudioVolume_FWD_DEFINED__
typedef interface ISimpleAudioVolume ISimpleAudioVolume;
#ifdef __cplusplus
interface ISimpleAudioVolume;
#endif /* __cplusplus */
#endif

#ifndef __IAudioStreamVolume_FWD_DEFINED__
#define __IAudioStreamVolume_FWD_DEFINED__
typedef interface IAudioStreamVolume IAudioStreamVolume;
#ifdef __cplusplus
interface IAudioStreamVolume;
#endif /* __cplusplus */
#endif

#ifndef __IAudioAmbisonicsControl_FWD_DEFINED__
#define __IAudioAmbisonicsControl_FWD_DEFINED__
typedef interface IAudioAmbisonicsControl IAudioAmbisonicsControl;
#ifdef __cplusplus
interface IAudioAmbisonicsControl;
#endif /* __cplusplus */
#endif

#ifndef __IChannelAudioVolume_FWD_DEFINED__
#define __IChannelAudioVolume_FWD_DEFINED__
typedef interface IChannelAudioVolume IChannelAudioVolume;
#ifdef __cplusplus
interface IChannelAudioVolume;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <wtypes.h>
#include <unknwn.h>
#include <audiosessiontypes.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>


#if 0
typedef struct WAVEFORMATEX {
    WORD wFormatTag;
    WORD nChannels;
    DWORD nSamplesPerSec;
    DWORD nAvgBytesPerSec;
    WORD nBlockAlign;
    WORD wBitsPerSample;
    WORD cbSize;
} WAVEFORMATEX;

typedef LONGLONG REFERENCE_TIME;
#else
#define _IKsControl_

#include <mmreg.h>
#include <ks.h>
#include <ksmedia.h>
#endif


#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
enum _AUDCLNT_BUFFERFLAGS {
    AUDCLNT_BUFFERFLAGS_DATA_DISCONTINUITY = 0x1,
    AUDCLNT_BUFFERFLAGS_SILENT = 0x2,
    AUDCLNT_BUFFERFLAGS_TIMESTAMP_ERROR = 0x4
};
typedef enum AUDCLNT_STREAMOPTIONS {
    AUDCLNT_STREAMOPTIONS_NONE = 0x0,
    AUDCLNT_STREAMOPTIONS_RAW = 0x1,
    AUDCLNT_STREAMOPTIONS_MATCH_FORMAT = 0x2,
    AUDCLNT_STREAMOPTIONS_AMBISONICS = 0x4
} AUDCLNT_STREAMOPTIONS;
DEFINE_ENUM_FLAG_OPERATORS(AUDCLNT_STREAMOPTIONS);
typedef enum AMBISONICS_TYPE {
    AMBISONICS_TYPE_FULL3D = 0x0
} AMBISONICS_TYPE;
typedef enum AMBISONICS_CHANNEL_ORDERING {
    AMBISONICS_CHANNEL_ORDERING_ACN = 0x0
} AMBISONICS_CHANNEL_ORDERING;
typedef enum AMBISONICS_NORMALIZATION {
    AMBISONICS_NORMALIZATION_SN3D = 0x0,
    AMBISONICS_NORMALIZATION_N3D = 0x1
} AMBISONICS_NORMALIZATION;

typedef struct AudioClientProperties {
    UINT32 cbSize;
    WINBOOL bIsOffload;
    AUDIO_STREAM_CATEGORY eCategory;
    AUDCLNT_STREAMOPTIONS Options;
} AudioClientProperties;
typedef struct AudioClient3ActivationParams {
    GUID tracingContextId;
} AudioClient3ActivationParams;
typedef struct AMBISONICS_PARAMS {
    UINT32 u32Size;
    UINT32 u32Version;
    AMBISONICS_TYPE u32Type;
    AMBISONICS_CHANNEL_ORDERING u32ChannelOrdering;
    AMBISONICS_NORMALIZATION u32Normalization;
    UINT32 u32Order;
    UINT32 u32NumChannels;
    UINT32 *pu32ChannelMap;
} AMBISONICS_PARAMS;

/*****************************************************************************
 * IAudioClient interface
 */
#ifndef __IAudioClient_INTERFACE_DEFINED__
#define __IAudioClient_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioClient, 0x1cb9ad4c, 0xdbfa, 0x4c32, 0xb1,0x78, 0xc2,0xf5,0x68,0xa7,0x03,0xb2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1cb9ad4c-dbfa-4c32-b178-c2f568a703b2")
IAudioClient : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        AUDCLNT_SHAREMODE ShareMode,
        DWORD StreamFlags,
        REFERENCE_TIME hnsBufferDuration,
        REFERENCE_TIME hnsPeriodicity,
        const WAVEFORMATEX *pFormat,
        LPCGUID AudioSessionGuid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBufferSize(
        UINT32 *pNumBufferFrames) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamLatency(
        REFERENCE_TIME *phnsLatency) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentPadding(
        UINT32 *pNumPaddingFrames) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsFormatSupported(
        AUDCLNT_SHAREMODE ShareMode,
        const WAVEFORMATEX *pFormat,
        WAVEFORMATEX **ppClosestMatch) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMixFormat(
        WAVEFORMATEX **ppDeviceFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDevicePeriod(
        REFERENCE_TIME *phnsDefaultDevicePeriod,
        REFERENCE_TIME *phnsMinimumDevicePeriod) = 0;

    virtual HRESULT STDMETHODCALLTYPE Start(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEventHandle(
        HANDLE eventHandle) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetService(
        REFIID riid,
        void **ppv) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioClient, 0x1cb9ad4c, 0xdbfa, 0x4c32, 0xb1,0x78, 0xc2,0xf5,0x68,0xa7,0x03,0xb2)
#endif
#else
typedef struct IAudioClientVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioClient *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioClient *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioClient *This);

    /*** IAudioClient methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IAudioClient *This,
        AUDCLNT_SHAREMODE ShareMode,
        DWORD StreamFlags,
        REFERENCE_TIME hnsBufferDuration,
        REFERENCE_TIME hnsPeriodicity,
        const WAVEFORMATEX *pFormat,
        LPCGUID AudioSessionGuid);

    HRESULT (STDMETHODCALLTYPE *GetBufferSize)(
        IAudioClient *This,
        UINT32 *pNumBufferFrames);

    HRESULT (STDMETHODCALLTYPE *GetStreamLatency)(
        IAudioClient *This,
        REFERENCE_TIME *phnsLatency);

    HRESULT (STDMETHODCALLTYPE *GetCurrentPadding)(
        IAudioClient *This,
        UINT32 *pNumPaddingFrames);

    HRESULT (STDMETHODCALLTYPE *IsFormatSupported)(
        IAudioClient *This,
        AUDCLNT_SHAREMODE ShareMode,
        const WAVEFORMATEX *pFormat,
        WAVEFORMATEX **ppClosestMatch);

    HRESULT (STDMETHODCALLTYPE *GetMixFormat)(
        IAudioClient *This,
        WAVEFORMATEX **ppDeviceFormat);

    HRESULT (STDMETHODCALLTYPE *GetDevicePeriod)(
        IAudioClient *This,
        REFERENCE_TIME *phnsDefaultDevicePeriod,
        REFERENCE_TIME *phnsMinimumDevicePeriod);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IAudioClient *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IAudioClient *This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IAudioClient *This);

    HRESULT (STDMETHODCALLTYPE *SetEventHandle)(
        IAudioClient *This,
        HANDLE eventHandle);

    HRESULT (STDMETHODCALLTYPE *GetService)(
        IAudioClient *This,
        REFIID riid,
        void **ppv);

    END_INTERFACE
} IAudioClientVtbl;

interface IAudioClient {
    CONST_VTBL IAudioClientVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioClient_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioClient_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioClient_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioClient methods ***/
#define IAudioClient_Initialize(This,ShareMode,StreamFlags,hnsBufferDuration,hnsPeriodicity,pFormat,AudioSessionGuid) (This)->lpVtbl->Initialize(This,ShareMode,StreamFlags,hnsBufferDuration,hnsPeriodicity,pFormat,AudioSessionGuid)
#define IAudioClient_GetBufferSize(This,pNumBufferFrames) (This)->lpVtbl->GetBufferSize(This,pNumBufferFrames)
#define IAudioClient_GetStreamLatency(This,phnsLatency) (This)->lpVtbl->GetStreamLatency(This,phnsLatency)
#define IAudioClient_GetCurrentPadding(This,pNumPaddingFrames) (This)->lpVtbl->GetCurrentPadding(This,pNumPaddingFrames)
#define IAudioClient_IsFormatSupported(This,ShareMode,pFormat,ppClosestMatch) (This)->lpVtbl->IsFormatSupported(This,ShareMode,pFormat,ppClosestMatch)
#define IAudioClient_GetMixFormat(This,ppDeviceFormat) (This)->lpVtbl->GetMixFormat(This,ppDeviceFormat)
#define IAudioClient_GetDevicePeriod(This,phnsDefaultDevicePeriod,phnsMinimumDevicePeriod) (This)->lpVtbl->GetDevicePeriod(This,phnsDefaultDevicePeriod,phnsMinimumDevicePeriod)
#define IAudioClient_Start(This) (This)->lpVtbl->Start(This)
#define IAudioClient_Stop(This) (This)->lpVtbl->Stop(This)
#define IAudioClient_Reset(This) (This)->lpVtbl->Reset(This)
#define IAudioClient_SetEventHandle(This,eventHandle) (This)->lpVtbl->SetEventHandle(This,eventHandle)
#define IAudioClient_GetService(This,riid,ppv) (This)->lpVtbl->GetService(This,riid,ppv)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioClient_QueryInterface(IAudioClient* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioClient_AddRef(IAudioClient* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioClient_Release(IAudioClient* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioClient methods ***/
static inline HRESULT IAudioClient_Initialize(IAudioClient* This,AUDCLNT_SHAREMODE ShareMode,DWORD StreamFlags,REFERENCE_TIME hnsBufferDuration,REFERENCE_TIME hnsPeriodicity,const WAVEFORMATEX *pFormat,LPCGUID AudioSessionGuid) {
    return This->lpVtbl->Initialize(This,ShareMode,StreamFlags,hnsBufferDuration,hnsPeriodicity,pFormat,AudioSessionGuid);
}
static inline HRESULT IAudioClient_GetBufferSize(IAudioClient* This,UINT32 *pNumBufferFrames) {
    return This->lpVtbl->GetBufferSize(This,pNumBufferFrames);
}
static inline HRESULT IAudioClient_GetStreamLatency(IAudioClient* This,REFERENCE_TIME *phnsLatency) {
    return This->lpVtbl->GetStreamLatency(This,phnsLatency);
}
static inline HRESULT IAudioClient_GetCurrentPadding(IAudioClient* This,UINT32 *pNumPaddingFrames) {
    return This->lpVtbl->GetCurrentPadding(This,pNumPaddingFrames);
}
static inline HRESULT IAudioClient_IsFormatSupported(IAudioClient* This,AUDCLNT_SHAREMODE ShareMode,const WAVEFORMATEX *pFormat,WAVEFORMATEX **ppClosestMatch) {
    return This->lpVtbl->IsFormatSupported(This,ShareMode,pFormat,ppClosestMatch);
}
static inline HRESULT IAudioClient_GetMixFormat(IAudioClient* This,WAVEFORMATEX **ppDeviceFormat) {
    return This->lpVtbl->GetMixFormat(This,ppDeviceFormat);
}
static inline HRESULT IAudioClient_GetDevicePeriod(IAudioClient* This,REFERENCE_TIME *phnsDefaultDevicePeriod,REFERENCE_TIME *phnsMinimumDevicePeriod) {
    return This->lpVtbl->GetDevicePeriod(This,phnsDefaultDevicePeriod,phnsMinimumDevicePeriod);
}
static inline HRESULT IAudioClient_Start(IAudioClient* This) {
    return This->lpVtbl->Start(This);
}
static inline HRESULT IAudioClient_Stop(IAudioClient* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IAudioClient_Reset(IAudioClient* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IAudioClient_SetEventHandle(IAudioClient* This,HANDLE eventHandle) {
    return This->lpVtbl->SetEventHandle(This,eventHandle);
}
static inline HRESULT IAudioClient_GetService(IAudioClient* This,REFIID riid,void **ppv) {
    return This->lpVtbl->GetService(This,riid,ppv);
}
#endif
#endif

#endif


#endif  /* __IAudioClient_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAudioClient2 interface
 */
#ifndef __IAudioClient2_INTERFACE_DEFINED__
#define __IAudioClient2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioClient2, 0x726778cd, 0xf60a, 0x4eda, 0x82,0xde, 0xe4,0x76,0x10,0xcd,0x78,0xaa);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("726778cd-f60a-4eda-82de-e47610cd78aa")
IAudioClient2 : public IAudioClient
{
    virtual HRESULT STDMETHODCALLTYPE IsOffloadCapable(
        AUDIO_STREAM_CATEGORY Category,
        WINBOOL *pbOffloadCapable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetClientProperties(
        const AudioClientProperties *pProperties) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBufferSizeLimits(
        const WAVEFORMATEX *pFormat,
        WINBOOL bEventDriven,
        REFERENCE_TIME *phnsMinBufferDuration,
        REFERENCE_TIME *phnsMaxBufferDuration) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioClient2, 0x726778cd, 0xf60a, 0x4eda, 0x82,0xde, 0xe4,0x76,0x10,0xcd,0x78,0xaa)
#endif
#else
typedef struct IAudioClient2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioClient2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioClient2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioClient2 *This);

    /*** IAudioClient methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IAudioClient2 *This,
        AUDCLNT_SHAREMODE ShareMode,
        DWORD StreamFlags,
        REFERENCE_TIME hnsBufferDuration,
        REFERENCE_TIME hnsPeriodicity,
        const WAVEFORMATEX *pFormat,
        LPCGUID AudioSessionGuid);

    HRESULT (STDMETHODCALLTYPE *GetBufferSize)(
        IAudioClient2 *This,
        UINT32 *pNumBufferFrames);

    HRESULT (STDMETHODCALLTYPE *GetStreamLatency)(
        IAudioClient2 *This,
        REFERENCE_TIME *phnsLatency);

    HRESULT (STDMETHODCALLTYPE *GetCurrentPadding)(
        IAudioClient2 *This,
        UINT32 *pNumPaddingFrames);

    HRESULT (STDMETHODCALLTYPE *IsFormatSupported)(
        IAudioClient2 *This,
        AUDCLNT_SHAREMODE ShareMode,
        const WAVEFORMATEX *pFormat,
        WAVEFORMATEX **ppClosestMatch);

    HRESULT (STDMETHODCALLTYPE *GetMixFormat)(
        IAudioClient2 *This,
        WAVEFORMATEX **ppDeviceFormat);

    HRESULT (STDMETHODCALLTYPE *GetDevicePeriod)(
        IAudioClient2 *This,
        REFERENCE_TIME *phnsDefaultDevicePeriod,
        REFERENCE_TIME *phnsMinimumDevicePeriod);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IAudioClient2 *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IAudioClient2 *This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IAudioClient2 *This);

    HRESULT (STDMETHODCALLTYPE *SetEventHandle)(
        IAudioClient2 *This,
        HANDLE eventHandle);

    HRESULT (STDMETHODCALLTYPE *GetService)(
        IAudioClient2 *This,
        REFIID riid,
        void **ppv);

    /*** IAudioClient2 methods ***/
    HRESULT (STDMETHODCALLTYPE *IsOffloadCapable)(
        IAudioClient2 *This,
        AUDIO_STREAM_CATEGORY Category,
        WINBOOL *pbOffloadCapable);

    HRESULT (STDMETHODCALLTYPE *SetClientProperties)(
        IAudioClient2 *This,
        const AudioClientProperties *pProperties);

    HRESULT (STDMETHODCALLTYPE *GetBufferSizeLimits)(
        IAudioClient2 *This,
        const WAVEFORMATEX *pFormat,
        WINBOOL bEventDriven,
        REFERENCE_TIME *phnsMinBufferDuration,
        REFERENCE_TIME *phnsMaxBufferDuration);

    END_INTERFACE
} IAudioClient2Vtbl;

interface IAudioClient2 {
    CONST_VTBL IAudioClient2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioClient2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioClient2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioClient2_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioClient methods ***/
#define IAudioClient2_Initialize(This,ShareMode,StreamFlags,hnsBufferDuration,hnsPeriodicity,pFormat,AudioSessionGuid) (This)->lpVtbl->Initialize(This,ShareMode,StreamFlags,hnsBufferDuration,hnsPeriodicity,pFormat,AudioSessionGuid)
#define IAudioClient2_GetBufferSize(This,pNumBufferFrames) (This)->lpVtbl->GetBufferSize(This,pNumBufferFrames)
#define IAudioClient2_GetStreamLatency(This,phnsLatency) (This)->lpVtbl->GetStreamLatency(This,phnsLatency)
#define IAudioClient2_GetCurrentPadding(This,pNumPaddingFrames) (This)->lpVtbl->GetCurrentPadding(This,pNumPaddingFrames)
#define IAudioClient2_IsFormatSupported(This,ShareMode,pFormat,ppClosestMatch) (This)->lpVtbl->IsFormatSupported(This,ShareMode,pFormat,ppClosestMatch)
#define IAudioClient2_GetMixFormat(This,ppDeviceFormat) (This)->lpVtbl->GetMixFormat(This,ppDeviceFormat)
#define IAudioClient2_GetDevicePeriod(This,phnsDefaultDevicePeriod,phnsMinimumDevicePeriod) (This)->lpVtbl->GetDevicePeriod(This,phnsDefaultDevicePeriod,phnsMinimumDevicePeriod)
#define IAudioClient2_Start(This) (This)->lpVtbl->Start(This)
#define IAudioClient2_Stop(This) (This)->lpVtbl->Stop(This)
#define IAudioClient2_Reset(This) (This)->lpVtbl->Reset(This)
#define IAudioClient2_SetEventHandle(This,eventHandle) (This)->lpVtbl->SetEventHandle(This,eventHandle)
#define IAudioClient2_GetService(This,riid,ppv) (This)->lpVtbl->GetService(This,riid,ppv)
/*** IAudioClient2 methods ***/
#define IAudioClient2_IsOffloadCapable(This,Category,pbOffloadCapable) (This)->lpVtbl->IsOffloadCapable(This,Category,pbOffloadCapable)
#define IAudioClient2_SetClientProperties(This,pProperties) (This)->lpVtbl->SetClientProperties(This,pProperties)
#define IAudioClient2_GetBufferSizeLimits(This,pFormat,bEventDriven,phnsMinBufferDuration,phnsMaxBufferDuration) (This)->lpVtbl->GetBufferSizeLimits(This,pFormat,bEventDriven,phnsMinBufferDuration,phnsMaxBufferDuration)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioClient2_QueryInterface(IAudioClient2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioClient2_AddRef(IAudioClient2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioClient2_Release(IAudioClient2* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioClient methods ***/
static inline HRESULT IAudioClient2_Initialize(IAudioClient2* This,AUDCLNT_SHAREMODE ShareMode,DWORD StreamFlags,REFERENCE_TIME hnsBufferDuration,REFERENCE_TIME hnsPeriodicity,const WAVEFORMATEX *pFormat,LPCGUID AudioSessionGuid) {
    return This->lpVtbl->Initialize(This,ShareMode,StreamFlags,hnsBufferDuration,hnsPeriodicity,pFormat,AudioSessionGuid);
}
static inline HRESULT IAudioClient2_GetBufferSize(IAudioClient2* This,UINT32 *pNumBufferFrames) {
    return This->lpVtbl->GetBufferSize(This,pNumBufferFrames);
}
static inline HRESULT IAudioClient2_GetStreamLatency(IAudioClient2* This,REFERENCE_TIME *phnsLatency) {
    return This->lpVtbl->GetStreamLatency(This,phnsLatency);
}
static inline HRESULT IAudioClient2_GetCurrentPadding(IAudioClient2* This,UINT32 *pNumPaddingFrames) {
    return This->lpVtbl->GetCurrentPadding(This,pNumPaddingFrames);
}
static inline HRESULT IAudioClient2_IsFormatSupported(IAudioClient2* This,AUDCLNT_SHAREMODE ShareMode,const WAVEFORMATEX *pFormat,WAVEFORMATEX **ppClosestMatch) {
    return This->lpVtbl->IsFormatSupported(This,ShareMode,pFormat,ppClosestMatch);
}
static inline HRESULT IAudioClient2_GetMixFormat(IAudioClient2* This,WAVEFORMATEX **ppDeviceFormat) {
    return This->lpVtbl->GetMixFormat(This,ppDeviceFormat);
}
static inline HRESULT IAudioClient2_GetDevicePeriod(IAudioClient2* This,REFERENCE_TIME *phnsDefaultDevicePeriod,REFERENCE_TIME *phnsMinimumDevicePeriod) {
    return This->lpVtbl->GetDevicePeriod(This,phnsDefaultDevicePeriod,phnsMinimumDevicePeriod);
}
static inline HRESULT IAudioClient2_Start(IAudioClient2* This) {
    return This->lpVtbl->Start(This);
}
static inline HRESULT IAudioClient2_Stop(IAudioClient2* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IAudioClient2_Reset(IAudioClient2* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IAudioClient2_SetEventHandle(IAudioClient2* This,HANDLE eventHandle) {
    return This->lpVtbl->SetEventHandle(This,eventHandle);
}
static inline HRESULT IAudioClient2_GetService(IAudioClient2* This,REFIID riid,void **ppv) {
    return This->lpVtbl->GetService(This,riid,ppv);
}
/*** IAudioClient2 methods ***/
static inline HRESULT IAudioClient2_IsOffloadCapable(IAudioClient2* This,AUDIO_STREAM_CATEGORY Category,WINBOOL *pbOffloadCapable) {
    return This->lpVtbl->IsOffloadCapable(This,Category,pbOffloadCapable);
}
static inline HRESULT IAudioClient2_SetClientProperties(IAudioClient2* This,const AudioClientProperties *pProperties) {
    return This->lpVtbl->SetClientProperties(This,pProperties);
}
static inline HRESULT IAudioClient2_GetBufferSizeLimits(IAudioClient2* This,const WAVEFORMATEX *pFormat,WINBOOL bEventDriven,REFERENCE_TIME *phnsMinBufferDuration,REFERENCE_TIME *phnsMaxBufferDuration) {
    return This->lpVtbl->GetBufferSizeLimits(This,pFormat,bEventDriven,phnsMinBufferDuration,phnsMaxBufferDuration);
}
#endif
#endif

#endif


#endif  /* __IAudioClient2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioClient3 interface
 */
#ifndef __IAudioClient3_INTERFACE_DEFINED__
#define __IAudioClient3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioClient3, 0x7ed4ee07, 0x8e67, 0x4cd4, 0x8c,0x1a, 0x2b,0x7a,0x59,0x87,0xad,0x42);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7ed4ee07-8e67-4cd4-8c1a-2b7a5987ad42")
IAudioClient3 : public IAudioClient2
{
    virtual HRESULT STDMETHODCALLTYPE GetSharedModeEnginePeriod(
        const WAVEFORMATEX *pFormat,
        UINT32 *pDefaultPeriodInFrames,
        UINT32 *pFundamentalPeriodInFrames,
        UINT32 *pMinPeriodInFrames,
        UINT32 *pMaxPeriodInFrames) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentSharedModeEnginePeriod(
        WAVEFORMATEX **ppFormat,
        UINT32 *pCurrentPeriodInFrames) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeSharedAudioStream(
        DWORD StreamFlags,
        UINT32 PeriodInFrames,
        const WAVEFORMATEX *pFormat,
        LPCGUID AudioSessionGuid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioClient3, 0x7ed4ee07, 0x8e67, 0x4cd4, 0x8c,0x1a, 0x2b,0x7a,0x59,0x87,0xad,0x42)
#endif
#else
typedef struct IAudioClient3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioClient3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioClient3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioClient3 *This);

    /*** IAudioClient methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IAudioClient3 *This,
        AUDCLNT_SHAREMODE ShareMode,
        DWORD StreamFlags,
        REFERENCE_TIME hnsBufferDuration,
        REFERENCE_TIME hnsPeriodicity,
        const WAVEFORMATEX *pFormat,
        LPCGUID AudioSessionGuid);

    HRESULT (STDMETHODCALLTYPE *GetBufferSize)(
        IAudioClient3 *This,
        UINT32 *pNumBufferFrames);

    HRESULT (STDMETHODCALLTYPE *GetStreamLatency)(
        IAudioClient3 *This,
        REFERENCE_TIME *phnsLatency);

    HRESULT (STDMETHODCALLTYPE *GetCurrentPadding)(
        IAudioClient3 *This,
        UINT32 *pNumPaddingFrames);

    HRESULT (STDMETHODCALLTYPE *IsFormatSupported)(
        IAudioClient3 *This,
        AUDCLNT_SHAREMODE ShareMode,
        const WAVEFORMATEX *pFormat,
        WAVEFORMATEX **ppClosestMatch);

    HRESULT (STDMETHODCALLTYPE *GetMixFormat)(
        IAudioClient3 *This,
        WAVEFORMATEX **ppDeviceFormat);

    HRESULT (STDMETHODCALLTYPE *GetDevicePeriod)(
        IAudioClient3 *This,
        REFERENCE_TIME *phnsDefaultDevicePeriod,
        REFERENCE_TIME *phnsMinimumDevicePeriod);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IAudioClient3 *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IAudioClient3 *This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IAudioClient3 *This);

    HRESULT (STDMETHODCALLTYPE *SetEventHandle)(
        IAudioClient3 *This,
        HANDLE eventHandle);

    HRESULT (STDMETHODCALLTYPE *GetService)(
        IAudioClient3 *This,
        REFIID riid,
        void **ppv);

    /*** IAudioClient2 methods ***/
    HRESULT (STDMETHODCALLTYPE *IsOffloadCapable)(
        IAudioClient3 *This,
        AUDIO_STREAM_CATEGORY Category,
        WINBOOL *pbOffloadCapable);

    HRESULT (STDMETHODCALLTYPE *SetClientProperties)(
        IAudioClient3 *This,
        const AudioClientProperties *pProperties);

    HRESULT (STDMETHODCALLTYPE *GetBufferSizeLimits)(
        IAudioClient3 *This,
        const WAVEFORMATEX *pFormat,
        WINBOOL bEventDriven,
        REFERENCE_TIME *phnsMinBufferDuration,
        REFERENCE_TIME *phnsMaxBufferDuration);

    /*** IAudioClient3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSharedModeEnginePeriod)(
        IAudioClient3 *This,
        const WAVEFORMATEX *pFormat,
        UINT32 *pDefaultPeriodInFrames,
        UINT32 *pFundamentalPeriodInFrames,
        UINT32 *pMinPeriodInFrames,
        UINT32 *pMaxPeriodInFrames);

    HRESULT (STDMETHODCALLTYPE *GetCurrentSharedModeEnginePeriod)(
        IAudioClient3 *This,
        WAVEFORMATEX **ppFormat,
        UINT32 *pCurrentPeriodInFrames);

    HRESULT (STDMETHODCALLTYPE *InitializeSharedAudioStream)(
        IAudioClient3 *This,
        DWORD StreamFlags,
        UINT32 PeriodInFrames,
        const WAVEFORMATEX *pFormat,
        LPCGUID AudioSessionGuid);

    END_INTERFACE
} IAudioClient3Vtbl;

interface IAudioClient3 {
    CONST_VTBL IAudioClient3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioClient3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioClient3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioClient3_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioClient methods ***/
#define IAudioClient3_Initialize(This,ShareMode,StreamFlags,hnsBufferDuration,hnsPeriodicity,pFormat,AudioSessionGuid) (This)->lpVtbl->Initialize(This,ShareMode,StreamFlags,hnsBufferDuration,hnsPeriodicity,pFormat,AudioSessionGuid)
#define IAudioClient3_GetBufferSize(This,pNumBufferFrames) (This)->lpVtbl->GetBufferSize(This,pNumBufferFrames)
#define IAudioClient3_GetStreamLatency(This,phnsLatency) (This)->lpVtbl->GetStreamLatency(This,phnsLatency)
#define IAudioClient3_GetCurrentPadding(This,pNumPaddingFrames) (This)->lpVtbl->GetCurrentPadding(This,pNumPaddingFrames)
#define IAudioClient3_IsFormatSupported(This,ShareMode,pFormat,ppClosestMatch) (This)->lpVtbl->IsFormatSupported(This,ShareMode,pFormat,ppClosestMatch)
#define IAudioClient3_GetMixFormat(This,ppDeviceFormat) (This)->lpVtbl->GetMixFormat(This,ppDeviceFormat)
#define IAudioClient3_GetDevicePeriod(This,phnsDefaultDevicePeriod,phnsMinimumDevicePeriod) (This)->lpVtbl->GetDevicePeriod(This,phnsDefaultDevicePeriod,phnsMinimumDevicePeriod)
#define IAudioClient3_Start(This) (This)->lpVtbl->Start(This)
#define IAudioClient3_Stop(This) (This)->lpVtbl->Stop(This)
#define IAudioClient3_Reset(This) (This)->lpVtbl->Reset(This)
#define IAudioClient3_SetEventHandle(This,eventHandle) (This)->lpVtbl->SetEventHandle(This,eventHandle)
#define IAudioClient3_GetService(This,riid,ppv) (This)->lpVtbl->GetService(This,riid,ppv)
/*** IAudioClient2 methods ***/
#define IAudioClient3_IsOffloadCapable(This,Category,pbOffloadCapable) (This)->lpVtbl->IsOffloadCapable(This,Category,pbOffloadCapable)
#define IAudioClient3_SetClientProperties(This,pProperties) (This)->lpVtbl->SetClientProperties(This,pProperties)
#define IAudioClient3_GetBufferSizeLimits(This,pFormat,bEventDriven,phnsMinBufferDuration,phnsMaxBufferDuration) (This)->lpVtbl->GetBufferSizeLimits(This,pFormat,bEventDriven,phnsMinBufferDuration,phnsMaxBufferDuration)
/*** IAudioClient3 methods ***/
#define IAudioClient3_GetSharedModeEnginePeriod(This,pFormat,pDefaultPeriodInFrames,pFundamentalPeriodInFrames,pMinPeriodInFrames,pMaxPeriodInFrames) (This)->lpVtbl->GetSharedModeEnginePeriod(This,pFormat,pDefaultPeriodInFrames,pFundamentalPeriodInFrames,pMinPeriodInFrames,pMaxPeriodInFrames)
#define IAudioClient3_GetCurrentSharedModeEnginePeriod(This,ppFormat,pCurrentPeriodInFrames) (This)->lpVtbl->GetCurrentSharedModeEnginePeriod(This,ppFormat,pCurrentPeriodInFrames)
#define IAudioClient3_InitializeSharedAudioStream(This,StreamFlags,PeriodInFrames,pFormat,AudioSessionGuid) (This)->lpVtbl->InitializeSharedAudioStream(This,StreamFlags,PeriodInFrames,pFormat,AudioSessionGuid)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioClient3_QueryInterface(IAudioClient3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioClient3_AddRef(IAudioClient3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioClient3_Release(IAudioClient3* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioClient methods ***/
static inline HRESULT IAudioClient3_Initialize(IAudioClient3* This,AUDCLNT_SHAREMODE ShareMode,DWORD StreamFlags,REFERENCE_TIME hnsBufferDuration,REFERENCE_TIME hnsPeriodicity,const WAVEFORMATEX *pFormat,LPCGUID AudioSessionGuid) {
    return This->lpVtbl->Initialize(This,ShareMode,StreamFlags,hnsBufferDuration,hnsPeriodicity,pFormat,AudioSessionGuid);
}
static inline HRESULT IAudioClient3_GetBufferSize(IAudioClient3* This,UINT32 *pNumBufferFrames) {
    return This->lpVtbl->GetBufferSize(This,pNumBufferFrames);
}
static inline HRESULT IAudioClient3_GetStreamLatency(IAudioClient3* This,REFERENCE_TIME *phnsLatency) {
    return This->lpVtbl->GetStreamLatency(This,phnsLatency);
}
static inline HRESULT IAudioClient3_GetCurrentPadding(IAudioClient3* This,UINT32 *pNumPaddingFrames) {
    return This->lpVtbl->GetCurrentPadding(This,pNumPaddingFrames);
}
static inline HRESULT IAudioClient3_IsFormatSupported(IAudioClient3* This,AUDCLNT_SHAREMODE ShareMode,const WAVEFORMATEX *pFormat,WAVEFORMATEX **ppClosestMatch) {
    return This->lpVtbl->IsFormatSupported(This,ShareMode,pFormat,ppClosestMatch);
}
static inline HRESULT IAudioClient3_GetMixFormat(IAudioClient3* This,WAVEFORMATEX **ppDeviceFormat) {
    return This->lpVtbl->GetMixFormat(This,ppDeviceFormat);
}
static inline HRESULT IAudioClient3_GetDevicePeriod(IAudioClient3* This,REFERENCE_TIME *phnsDefaultDevicePeriod,REFERENCE_TIME *phnsMinimumDevicePeriod) {
    return This->lpVtbl->GetDevicePeriod(This,phnsDefaultDevicePeriod,phnsMinimumDevicePeriod);
}
static inline HRESULT IAudioClient3_Start(IAudioClient3* This) {
    return This->lpVtbl->Start(This);
}
static inline HRESULT IAudioClient3_Stop(IAudioClient3* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IAudioClient3_Reset(IAudioClient3* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IAudioClient3_SetEventHandle(IAudioClient3* This,HANDLE eventHandle) {
    return This->lpVtbl->SetEventHandle(This,eventHandle);
}
static inline HRESULT IAudioClient3_GetService(IAudioClient3* This,REFIID riid,void **ppv) {
    return This->lpVtbl->GetService(This,riid,ppv);
}
/*** IAudioClient2 methods ***/
static inline HRESULT IAudioClient3_IsOffloadCapable(IAudioClient3* This,AUDIO_STREAM_CATEGORY Category,WINBOOL *pbOffloadCapable) {
    return This->lpVtbl->IsOffloadCapable(This,Category,pbOffloadCapable);
}
static inline HRESULT IAudioClient3_SetClientProperties(IAudioClient3* This,const AudioClientProperties *pProperties) {
    return This->lpVtbl->SetClientProperties(This,pProperties);
}
static inline HRESULT IAudioClient3_GetBufferSizeLimits(IAudioClient3* This,const WAVEFORMATEX *pFormat,WINBOOL bEventDriven,REFERENCE_TIME *phnsMinBufferDuration,REFERENCE_TIME *phnsMaxBufferDuration) {
    return This->lpVtbl->GetBufferSizeLimits(This,pFormat,bEventDriven,phnsMinBufferDuration,phnsMaxBufferDuration);
}
/*** IAudioClient3 methods ***/
static inline HRESULT IAudioClient3_GetSharedModeEnginePeriod(IAudioClient3* This,const WAVEFORMATEX *pFormat,UINT32 *pDefaultPeriodInFrames,UINT32 *pFundamentalPeriodInFrames,UINT32 *pMinPeriodInFrames,UINT32 *pMaxPeriodInFrames) {
    return This->lpVtbl->GetSharedModeEnginePeriod(This,pFormat,pDefaultPeriodInFrames,pFundamentalPeriodInFrames,pMinPeriodInFrames,pMaxPeriodInFrames);
}
static inline HRESULT IAudioClient3_GetCurrentSharedModeEnginePeriod(IAudioClient3* This,WAVEFORMATEX **ppFormat,UINT32 *pCurrentPeriodInFrames) {
    return This->lpVtbl->GetCurrentSharedModeEnginePeriod(This,ppFormat,pCurrentPeriodInFrames);
}
static inline HRESULT IAudioClient3_InitializeSharedAudioStream(IAudioClient3* This,DWORD StreamFlags,UINT32 PeriodInFrames,const WAVEFORMATEX *pFormat,LPCGUID AudioSessionGuid) {
    return This->lpVtbl->InitializeSharedAudioStream(This,StreamFlags,PeriodInFrames,pFormat,AudioSessionGuid);
}
#endif
#endif

#endif


#endif  /* __IAudioClient3_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAudioRenderClient interface
 */
#ifndef __IAudioRenderClient_INTERFACE_DEFINED__
#define __IAudioRenderClient_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioRenderClient, 0xf294acfc, 0x3146, 0x4483, 0xa7,0xbf, 0xad,0xdc,0xa7,0xc2,0x60,0xe2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f294acfc-3146-4483-a7bf-addca7c260e2")
IAudioRenderClient : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetBuffer(
        UINT32 NumFramesRequested,
        BYTE **ppData) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseBuffer(
        UINT32 NumFramesWritten,
        DWORD dwFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioRenderClient, 0xf294acfc, 0x3146, 0x4483, 0xa7,0xbf, 0xad,0xdc,0xa7,0xc2,0x60,0xe2)
#endif
#else
typedef struct IAudioRenderClientVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioRenderClient *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioRenderClient *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioRenderClient *This);

    /*** IAudioRenderClient methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IAudioRenderClient *This,
        UINT32 NumFramesRequested,
        BYTE **ppData);

    HRESULT (STDMETHODCALLTYPE *ReleaseBuffer)(
        IAudioRenderClient *This,
        UINT32 NumFramesWritten,
        DWORD dwFlags);

    END_INTERFACE
} IAudioRenderClientVtbl;

interface IAudioRenderClient {
    CONST_VTBL IAudioRenderClientVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioRenderClient_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioRenderClient_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioRenderClient_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioRenderClient methods ***/
#define IAudioRenderClient_GetBuffer(This,NumFramesRequested,ppData) (This)->lpVtbl->GetBuffer(This,NumFramesRequested,ppData)
#define IAudioRenderClient_ReleaseBuffer(This,NumFramesWritten,dwFlags) (This)->lpVtbl->ReleaseBuffer(This,NumFramesWritten,dwFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioRenderClient_QueryInterface(IAudioRenderClient* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioRenderClient_AddRef(IAudioRenderClient* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioRenderClient_Release(IAudioRenderClient* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioRenderClient methods ***/
static inline HRESULT IAudioRenderClient_GetBuffer(IAudioRenderClient* This,UINT32 NumFramesRequested,BYTE **ppData) {
    return This->lpVtbl->GetBuffer(This,NumFramesRequested,ppData);
}
static inline HRESULT IAudioRenderClient_ReleaseBuffer(IAudioRenderClient* This,UINT32 NumFramesWritten,DWORD dwFlags) {
    return This->lpVtbl->ReleaseBuffer(This,NumFramesWritten,dwFlags);
}
#endif
#endif

#endif


#endif  /* __IAudioRenderClient_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAudioCaptureClient interface
 */
#ifndef __IAudioCaptureClient_INTERFACE_DEFINED__
#define __IAudioCaptureClient_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioCaptureClient, 0xc8adbd64, 0xe71e, 0x48a0, 0xa4,0xde, 0x18,0x5c,0x39,0x5c,0xd3,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c8adbd64-e71e-48a0-a4de-185c395cd317")
IAudioCaptureClient : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetBuffer(
        BYTE **ppData,
        UINT32 *pNumFramesToRead,
        DWORD *pdwFlags,
        UINT64 *pu64DevicePosition,
        UINT64 *pu64QPCPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseBuffer(
        UINT32 NumFramesRead) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNextPacketSize(
        UINT32 *pNumFramesInNextPacket) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioCaptureClient, 0xc8adbd64, 0xe71e, 0x48a0, 0xa4,0xde, 0x18,0x5c,0x39,0x5c,0xd3,0x17)
#endif
#else
typedef struct IAudioCaptureClientVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioCaptureClient *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioCaptureClient *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioCaptureClient *This);

    /*** IAudioCaptureClient methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IAudioCaptureClient *This,
        BYTE **ppData,
        UINT32 *pNumFramesToRead,
        DWORD *pdwFlags,
        UINT64 *pu64DevicePosition,
        UINT64 *pu64QPCPosition);

    HRESULT (STDMETHODCALLTYPE *ReleaseBuffer)(
        IAudioCaptureClient *This,
        UINT32 NumFramesRead);

    HRESULT (STDMETHODCALLTYPE *GetNextPacketSize)(
        IAudioCaptureClient *This,
        UINT32 *pNumFramesInNextPacket);

    END_INTERFACE
} IAudioCaptureClientVtbl;

interface IAudioCaptureClient {
    CONST_VTBL IAudioCaptureClientVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioCaptureClient_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioCaptureClient_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioCaptureClient_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioCaptureClient methods ***/
#define IAudioCaptureClient_GetBuffer(This,ppData,pNumFramesToRead,pdwFlags,pu64DevicePosition,pu64QPCPosition) (This)->lpVtbl->GetBuffer(This,ppData,pNumFramesToRead,pdwFlags,pu64DevicePosition,pu64QPCPosition)
#define IAudioCaptureClient_ReleaseBuffer(This,NumFramesRead) (This)->lpVtbl->ReleaseBuffer(This,NumFramesRead)
#define IAudioCaptureClient_GetNextPacketSize(This,pNumFramesInNextPacket) (This)->lpVtbl->GetNextPacketSize(This,pNumFramesInNextPacket)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioCaptureClient_QueryInterface(IAudioCaptureClient* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioCaptureClient_AddRef(IAudioCaptureClient* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioCaptureClient_Release(IAudioCaptureClient* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioCaptureClient methods ***/
static inline HRESULT IAudioCaptureClient_GetBuffer(IAudioCaptureClient* This,BYTE **ppData,UINT32 *pNumFramesToRead,DWORD *pdwFlags,UINT64 *pu64DevicePosition,UINT64 *pu64QPCPosition) {
    return This->lpVtbl->GetBuffer(This,ppData,pNumFramesToRead,pdwFlags,pu64DevicePosition,pu64QPCPosition);
}
static inline HRESULT IAudioCaptureClient_ReleaseBuffer(IAudioCaptureClient* This,UINT32 NumFramesRead) {
    return This->lpVtbl->ReleaseBuffer(This,NumFramesRead);
}
static inline HRESULT IAudioCaptureClient_GetNextPacketSize(IAudioCaptureClient* This,UINT32 *pNumFramesInNextPacket) {
    return This->lpVtbl->GetNextPacketSize(This,pNumFramesInNextPacket);
}
#endif
#endif

#endif


#endif  /* __IAudioCaptureClient_INTERFACE_DEFINED__ */


#define AUDIOCLOCK_CHARACTERISTIC_FIXED_FREQ  0x1

/*****************************************************************************
 * IAudioClock interface
 */
#ifndef __IAudioClock_INTERFACE_DEFINED__
#define __IAudioClock_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioClock, 0xcd63314f, 0x3fba, 0x4a1b, 0x81,0x2c, 0xef,0x96,0x35,0x87,0x28,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cd63314f-3fba-4a1b-812c-ef96358728e7")
IAudioClock : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetFrequency(
        UINT64 *pu64Frequency) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPosition(
        UINT64 *pu64Position,
        UINT64 *pu64QPCPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCharacteristics(
        DWORD *pdwCharacteristics) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioClock, 0xcd63314f, 0x3fba, 0x4a1b, 0x81,0x2c, 0xef,0x96,0x35,0x87,0x28,0xe7)
#endif
#else
typedef struct IAudioClockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioClock *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioClock *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioClock *This);

    /*** IAudioClock methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFrequency)(
        IAudioClock *This,
        UINT64 *pu64Frequency);

    HRESULT (STDMETHODCALLTYPE *GetPosition)(
        IAudioClock *This,
        UINT64 *pu64Position,
        UINT64 *pu64QPCPosition);

    HRESULT (STDMETHODCALLTYPE *GetCharacteristics)(
        IAudioClock *This,
        DWORD *pdwCharacteristics);

    END_INTERFACE
} IAudioClockVtbl;

interface IAudioClock {
    CONST_VTBL IAudioClockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioClock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioClock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioClock_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioClock methods ***/
#define IAudioClock_GetFrequency(This,pu64Frequency) (This)->lpVtbl->GetFrequency(This,pu64Frequency)
#define IAudioClock_GetPosition(This,pu64Position,pu64QPCPosition) (This)->lpVtbl->GetPosition(This,pu64Position,pu64QPCPosition)
#define IAudioClock_GetCharacteristics(This,pdwCharacteristics) (This)->lpVtbl->GetCharacteristics(This,pdwCharacteristics)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioClock_QueryInterface(IAudioClock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioClock_AddRef(IAudioClock* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioClock_Release(IAudioClock* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioClock methods ***/
static inline HRESULT IAudioClock_GetFrequency(IAudioClock* This,UINT64 *pu64Frequency) {
    return This->lpVtbl->GetFrequency(This,pu64Frequency);
}
static inline HRESULT IAudioClock_GetPosition(IAudioClock* This,UINT64 *pu64Position,UINT64 *pu64QPCPosition) {
    return This->lpVtbl->GetPosition(This,pu64Position,pu64QPCPosition);
}
static inline HRESULT IAudioClock_GetCharacteristics(IAudioClock* This,DWORD *pdwCharacteristics) {
    return This->lpVtbl->GetCharacteristics(This,pdwCharacteristics);
}
#endif
#endif

#endif


#endif  /* __IAudioClock_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IAudioClock2 interface
 */
#ifndef __IAudioClock2_INTERFACE_DEFINED__
#define __IAudioClock2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioClock2, 0x6f49ff73, 0x6727, 0x49ac, 0xa0,0x08, 0xd9,0x8c,0xf5,0xe7,0x00,0x48);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6f49ff73-6727-49ac-a008-d98cf5e70048")
IAudioClock2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDevicePosition(
        UINT64 *DevicePosition,
        UINT64 *QPCPosition) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioClock2, 0x6f49ff73, 0x6727, 0x49ac, 0xa0,0x08, 0xd9,0x8c,0xf5,0xe7,0x00,0x48)
#endif
#else
typedef struct IAudioClock2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioClock2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioClock2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioClock2 *This);

    /*** IAudioClock2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevicePosition)(
        IAudioClock2 *This,
        UINT64 *DevicePosition,
        UINT64 *QPCPosition);

    END_INTERFACE
} IAudioClock2Vtbl;

interface IAudioClock2 {
    CONST_VTBL IAudioClock2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioClock2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioClock2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioClock2_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioClock2 methods ***/
#define IAudioClock2_GetDevicePosition(This,DevicePosition,QPCPosition) (This)->lpVtbl->GetDevicePosition(This,DevicePosition,QPCPosition)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioClock2_QueryInterface(IAudioClock2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioClock2_AddRef(IAudioClock2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioClock2_Release(IAudioClock2* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioClock2 methods ***/
static inline HRESULT IAudioClock2_GetDevicePosition(IAudioClock2* This,UINT64 *DevicePosition,UINT64 *QPCPosition) {
    return This->lpVtbl->GetDevicePosition(This,DevicePosition,QPCPosition);
}
#endif
#endif

#endif


#endif  /* __IAudioClock2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IAudioClockAdjustment interface
 */
#ifndef __IAudioClockAdjustment_INTERFACE_DEFINED__
#define __IAudioClockAdjustment_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioClockAdjustment, 0xf6e4c0a0, 0x46d9, 0x4fb8, 0xbe,0x21, 0x57,0xa3,0xef,0x2b,0x62,0x6c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f6e4c0a0-46d9-4fb8-be21-57a3ef2b626c")
IAudioClockAdjustment : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetSampleRate(
        float flSampleRate) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioClockAdjustment, 0xf6e4c0a0, 0x46d9, 0x4fb8, 0xbe,0x21, 0x57,0xa3,0xef,0x2b,0x62,0x6c)
#endif
#else
typedef struct IAudioClockAdjustmentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioClockAdjustment *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioClockAdjustment *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioClockAdjustment *This);

    /*** IAudioClockAdjustment methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSampleRate)(
        IAudioClockAdjustment *This,
        float flSampleRate);

    END_INTERFACE
} IAudioClockAdjustmentVtbl;

interface IAudioClockAdjustment {
    CONST_VTBL IAudioClockAdjustmentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioClockAdjustment_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioClockAdjustment_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioClockAdjustment_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioClockAdjustment methods ***/
#define IAudioClockAdjustment_SetSampleRate(This,flSampleRate) (This)->lpVtbl->SetSampleRate(This,flSampleRate)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioClockAdjustment_QueryInterface(IAudioClockAdjustment* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioClockAdjustment_AddRef(IAudioClockAdjustment* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioClockAdjustment_Release(IAudioClockAdjustment* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioClockAdjustment methods ***/
static inline HRESULT IAudioClockAdjustment_SetSampleRate(IAudioClockAdjustment* This,float flSampleRate) {
    return This->lpVtbl->SetSampleRate(This,flSampleRate);
}
#endif
#endif

#endif


#endif  /* __IAudioClockAdjustment_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
/*****************************************************************************
 * ISimpleAudioVolume interface
 */
#ifndef __ISimpleAudioVolume_INTERFACE_DEFINED__
#define __ISimpleAudioVolume_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISimpleAudioVolume, 0x87ce5498, 0x68d6, 0x44e5, 0x92,0x15, 0x6d,0xa4,0x7e,0xf8,0x83,0xd8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("87ce5498-68d6-44e5-9215-6da47ef883d8")
ISimpleAudioVolume : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetMasterVolume(
        float fLevel,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMasterVolume(
        float *pfLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMute(
        const WINBOOL bMute,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMute(
        WINBOOL *pbMute) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISimpleAudioVolume, 0x87ce5498, 0x68d6, 0x44e5, 0x92,0x15, 0x6d,0xa4,0x7e,0xf8,0x83,0xd8)
#endif
#else
typedef struct ISimpleAudioVolumeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISimpleAudioVolume *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISimpleAudioVolume *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISimpleAudioVolume *This);

    /*** ISimpleAudioVolume methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMasterVolume)(
        ISimpleAudioVolume *This,
        float fLevel,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *GetMasterVolume)(
        ISimpleAudioVolume *This,
        float *pfLevel);

    HRESULT (STDMETHODCALLTYPE *SetMute)(
        ISimpleAudioVolume *This,
        const WINBOOL bMute,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *GetMute)(
        ISimpleAudioVolume *This,
        WINBOOL *pbMute);

    END_INTERFACE
} ISimpleAudioVolumeVtbl;

interface ISimpleAudioVolume {
    CONST_VTBL ISimpleAudioVolumeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISimpleAudioVolume_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISimpleAudioVolume_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISimpleAudioVolume_Release(This) (This)->lpVtbl->Release(This)
/*** ISimpleAudioVolume methods ***/
#define ISimpleAudioVolume_SetMasterVolume(This,fLevel,EventContext) (This)->lpVtbl->SetMasterVolume(This,fLevel,EventContext)
#define ISimpleAudioVolume_GetMasterVolume(This,pfLevel) (This)->lpVtbl->GetMasterVolume(This,pfLevel)
#define ISimpleAudioVolume_SetMute(This,bMute,EventContext) (This)->lpVtbl->SetMute(This,bMute,EventContext)
#define ISimpleAudioVolume_GetMute(This,pbMute) (This)->lpVtbl->GetMute(This,pbMute)
#else
/*** IUnknown methods ***/
static inline HRESULT ISimpleAudioVolume_QueryInterface(ISimpleAudioVolume* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISimpleAudioVolume_AddRef(ISimpleAudioVolume* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISimpleAudioVolume_Release(ISimpleAudioVolume* This) {
    return This->lpVtbl->Release(This);
}
/*** ISimpleAudioVolume methods ***/
static inline HRESULT ISimpleAudioVolume_SetMasterVolume(ISimpleAudioVolume* This,float fLevel,LPCGUID EventContext) {
    return This->lpVtbl->SetMasterVolume(This,fLevel,EventContext);
}
static inline HRESULT ISimpleAudioVolume_GetMasterVolume(ISimpleAudioVolume* This,float *pfLevel) {
    return This->lpVtbl->GetMasterVolume(This,pfLevel);
}
static inline HRESULT ISimpleAudioVolume_SetMute(ISimpleAudioVolume* This,const WINBOOL bMute,LPCGUID EventContext) {
    return This->lpVtbl->SetMute(This,bMute,EventContext);
}
static inline HRESULT ISimpleAudioVolume_GetMute(ISimpleAudioVolume* This,WINBOOL *pbMute) {
    return This->lpVtbl->GetMute(This,pbMute);
}
#endif
#endif

#endif


#endif  /* __ISimpleAudioVolume_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
/*****************************************************************************
 * IAudioStreamVolume interface
 */
#ifndef __IAudioStreamVolume_INTERFACE_DEFINED__
#define __IAudioStreamVolume_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioStreamVolume, 0x93014887, 0x242d, 0x4068, 0x8a,0x15, 0xcf,0x5e,0x93,0xb9,0x0f,0xe3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("93014887-242d-4068-8a15-cf5e93b90fe3")
IAudioStreamVolume : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetChannelCount(
        UINT32 *pdwCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetChannelVolume(
        UINT32 dwIndex,
        const float fLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChannelVolume(
        UINT32 dwIndex,
        float *pfLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAllVolumes(
        UINT32 dwCount,
        const float *pfVolumes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllVolumes(
        UINT32 dwCount,
        float *pfVolumes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioStreamVolume, 0x93014887, 0x242d, 0x4068, 0x8a,0x15, 0xcf,0x5e,0x93,0xb9,0x0f,0xe3)
#endif
#else
typedef struct IAudioStreamVolumeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioStreamVolume *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioStreamVolume *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioStreamVolume *This);

    /*** IAudioStreamVolume methods ***/
    HRESULT (STDMETHODCALLTYPE *GetChannelCount)(
        IAudioStreamVolume *This,
        UINT32 *pdwCount);

    HRESULT (STDMETHODCALLTYPE *SetChannelVolume)(
        IAudioStreamVolume *This,
        UINT32 dwIndex,
        const float fLevel);

    HRESULT (STDMETHODCALLTYPE *GetChannelVolume)(
        IAudioStreamVolume *This,
        UINT32 dwIndex,
        float *pfLevel);

    HRESULT (STDMETHODCALLTYPE *SetAllVolumes)(
        IAudioStreamVolume *This,
        UINT32 dwCount,
        const float *pfVolumes);

    HRESULT (STDMETHODCALLTYPE *GetAllVolumes)(
        IAudioStreamVolume *This,
        UINT32 dwCount,
        float *pfVolumes);

    END_INTERFACE
} IAudioStreamVolumeVtbl;

interface IAudioStreamVolume {
    CONST_VTBL IAudioStreamVolumeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioStreamVolume_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioStreamVolume_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioStreamVolume_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioStreamVolume methods ***/
#define IAudioStreamVolume_GetChannelCount(This,pdwCount) (This)->lpVtbl->GetChannelCount(This,pdwCount)
#define IAudioStreamVolume_SetChannelVolume(This,dwIndex,fLevel) (This)->lpVtbl->SetChannelVolume(This,dwIndex,fLevel)
#define IAudioStreamVolume_GetChannelVolume(This,dwIndex,pfLevel) (This)->lpVtbl->GetChannelVolume(This,dwIndex,pfLevel)
#define IAudioStreamVolume_SetAllVolumes(This,dwCount,pfVolumes) (This)->lpVtbl->SetAllVolumes(This,dwCount,pfVolumes)
#define IAudioStreamVolume_GetAllVolumes(This,dwCount,pfVolumes) (This)->lpVtbl->GetAllVolumes(This,dwCount,pfVolumes)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioStreamVolume_QueryInterface(IAudioStreamVolume* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioStreamVolume_AddRef(IAudioStreamVolume* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioStreamVolume_Release(IAudioStreamVolume* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioStreamVolume methods ***/
static inline HRESULT IAudioStreamVolume_GetChannelCount(IAudioStreamVolume* This,UINT32 *pdwCount) {
    return This->lpVtbl->GetChannelCount(This,pdwCount);
}
static inline HRESULT IAudioStreamVolume_SetChannelVolume(IAudioStreamVolume* This,UINT32 dwIndex,const float fLevel) {
    return This->lpVtbl->SetChannelVolume(This,dwIndex,fLevel);
}
static inline HRESULT IAudioStreamVolume_GetChannelVolume(IAudioStreamVolume* This,UINT32 dwIndex,float *pfLevel) {
    return This->lpVtbl->GetChannelVolume(This,dwIndex,pfLevel);
}
static inline HRESULT IAudioStreamVolume_SetAllVolumes(IAudioStreamVolume* This,UINT32 dwCount,const float *pfVolumes) {
    return This->lpVtbl->SetAllVolumes(This,dwCount,pfVolumes);
}
static inline HRESULT IAudioStreamVolume_GetAllVolumes(IAudioStreamVolume* This,UINT32 dwCount,float *pfVolumes) {
    return This->lpVtbl->GetAllVolumes(This,dwCount,pfVolumes);
}
#endif
#endif

#endif


#endif  /* __IAudioStreamVolume_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAudioAmbisonicsControl interface
 */
#ifndef __IAudioAmbisonicsControl_INTERFACE_DEFINED__
#define __IAudioAmbisonicsControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAudioAmbisonicsControl, 0x28724c91, 0xdf35, 0x4856, 0x9f,0x76, 0xd6,0xa2,0x64,0x13,0xf3,0xdf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("28724c91-df35-4856-9f76-d6a26413f3df")
IAudioAmbisonicsControl : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetData(
        const AMBISONICS_PARAMS *pAmbisonicsParams,
        UINT32 cbAmbisonicsParams) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetHeadTracking(
        WINBOOL bEnableHeadTracking) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHeadTracking(
        WINBOOL *pbEnableHeadTracking) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRotation(
        float X,
        float Y,
        float Z,
        float W) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAudioAmbisonicsControl, 0x28724c91, 0xdf35, 0x4856, 0x9f,0x76, 0xd6,0xa2,0x64,0x13,0xf3,0xdf)
#endif
#else
typedef struct IAudioAmbisonicsControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAudioAmbisonicsControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAudioAmbisonicsControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAudioAmbisonicsControl *This);

    /*** IAudioAmbisonicsControl methods ***/
    HRESULT (STDMETHODCALLTYPE *SetData)(
        IAudioAmbisonicsControl *This,
        const AMBISONICS_PARAMS *pAmbisonicsParams,
        UINT32 cbAmbisonicsParams);

    HRESULT (STDMETHODCALLTYPE *SetHeadTracking)(
        IAudioAmbisonicsControl *This,
        WINBOOL bEnableHeadTracking);

    HRESULT (STDMETHODCALLTYPE *GetHeadTracking)(
        IAudioAmbisonicsControl *This,
        WINBOOL *pbEnableHeadTracking);

    HRESULT (STDMETHODCALLTYPE *SetRotation)(
        IAudioAmbisonicsControl *This,
        float X,
        float Y,
        float Z,
        float W);

    END_INTERFACE
} IAudioAmbisonicsControlVtbl;

interface IAudioAmbisonicsControl {
    CONST_VTBL IAudioAmbisonicsControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAudioAmbisonicsControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAudioAmbisonicsControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAudioAmbisonicsControl_Release(This) (This)->lpVtbl->Release(This)
/*** IAudioAmbisonicsControl methods ***/
#define IAudioAmbisonicsControl_SetData(This,pAmbisonicsParams,cbAmbisonicsParams) (This)->lpVtbl->SetData(This,pAmbisonicsParams,cbAmbisonicsParams)
#define IAudioAmbisonicsControl_SetHeadTracking(This,bEnableHeadTracking) (This)->lpVtbl->SetHeadTracking(This,bEnableHeadTracking)
#define IAudioAmbisonicsControl_GetHeadTracking(This,pbEnableHeadTracking) (This)->lpVtbl->GetHeadTracking(This,pbEnableHeadTracking)
#define IAudioAmbisonicsControl_SetRotation(This,X,Y,Z,W) (This)->lpVtbl->SetRotation(This,X,Y,Z,W)
#else
/*** IUnknown methods ***/
static inline HRESULT IAudioAmbisonicsControl_QueryInterface(IAudioAmbisonicsControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAudioAmbisonicsControl_AddRef(IAudioAmbisonicsControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAudioAmbisonicsControl_Release(IAudioAmbisonicsControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IAudioAmbisonicsControl methods ***/
static inline HRESULT IAudioAmbisonicsControl_SetData(IAudioAmbisonicsControl* This,const AMBISONICS_PARAMS *pAmbisonicsParams,UINT32 cbAmbisonicsParams) {
    return This->lpVtbl->SetData(This,pAmbisonicsParams,cbAmbisonicsParams);
}
static inline HRESULT IAudioAmbisonicsControl_SetHeadTracking(IAudioAmbisonicsControl* This,WINBOOL bEnableHeadTracking) {
    return This->lpVtbl->SetHeadTracking(This,bEnableHeadTracking);
}
static inline HRESULT IAudioAmbisonicsControl_GetHeadTracking(IAudioAmbisonicsControl* This,WINBOOL *pbEnableHeadTracking) {
    return This->lpVtbl->GetHeadTracking(This,pbEnableHeadTracking);
}
static inline HRESULT IAudioAmbisonicsControl_SetRotation(IAudioAmbisonicsControl* This,float X,float Y,float Z,float W) {
    return This->lpVtbl->SetRotation(This,X,Y,Z,W);
}
#endif
#endif

#endif


#endif  /* __IAudioAmbisonicsControl_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IChannelAudioVolume interface
 */
#ifndef __IChannelAudioVolume_INTERFACE_DEFINED__
#define __IChannelAudioVolume_INTERFACE_DEFINED__

DEFINE_GUID(IID_IChannelAudioVolume, 0x1c158861, 0xb533, 0x4b30, 0xb1,0xcf, 0xe8,0x53,0xe5,0x1c,0x59,0xb8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1c158861-b533-4b30-b1cf-e853e51c59b8")
IChannelAudioVolume : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetChannelCount(
        UINT32 *pdwCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetChannelVolume(
        UINT32 dwIndex,
        const float fLevel,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChannelVolume(
        UINT32 dwIndex,
        float *pfLevel) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAllVolumes(
        UINT32 dwCount,
        const float *pfVolumes,
        LPCGUID EventContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllVolumes(
        UINT32 dwCount,
        float *pfVolumes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IChannelAudioVolume, 0x1c158861, 0xb533, 0x4b30, 0xb1,0xcf, 0xe8,0x53,0xe5,0x1c,0x59,0xb8)
#endif
#else
typedef struct IChannelAudioVolumeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IChannelAudioVolume *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IChannelAudioVolume *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IChannelAudioVolume *This);

    /*** IChannelAudioVolume methods ***/
    HRESULT (STDMETHODCALLTYPE *GetChannelCount)(
        IChannelAudioVolume *This,
        UINT32 *pdwCount);

    HRESULT (STDMETHODCALLTYPE *SetChannelVolume)(
        IChannelAudioVolume *This,
        UINT32 dwIndex,
        const float fLevel,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *GetChannelVolume)(
        IChannelAudioVolume *This,
        UINT32 dwIndex,
        float *pfLevel);

    HRESULT (STDMETHODCALLTYPE *SetAllVolumes)(
        IChannelAudioVolume *This,
        UINT32 dwCount,
        const float *pfVolumes,
        LPCGUID EventContext);

    HRESULT (STDMETHODCALLTYPE *GetAllVolumes)(
        IChannelAudioVolume *This,
        UINT32 dwCount,
        float *pfVolumes);

    END_INTERFACE
} IChannelAudioVolumeVtbl;

interface IChannelAudioVolume {
    CONST_VTBL IChannelAudioVolumeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IChannelAudioVolume_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IChannelAudioVolume_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IChannelAudioVolume_Release(This) (This)->lpVtbl->Release(This)
/*** IChannelAudioVolume methods ***/
#define IChannelAudioVolume_GetChannelCount(This,pdwCount) (This)->lpVtbl->GetChannelCount(This,pdwCount)
#define IChannelAudioVolume_SetChannelVolume(This,dwIndex,fLevel,EventContext) (This)->lpVtbl->SetChannelVolume(This,dwIndex,fLevel,EventContext)
#define IChannelAudioVolume_GetChannelVolume(This,dwIndex,pfLevel) (This)->lpVtbl->GetChannelVolume(This,dwIndex,pfLevel)
#define IChannelAudioVolume_SetAllVolumes(This,dwCount,pfVolumes,EventContext) (This)->lpVtbl->SetAllVolumes(This,dwCount,pfVolumes,EventContext)
#define IChannelAudioVolume_GetAllVolumes(This,dwCount,pfVolumes) (This)->lpVtbl->GetAllVolumes(This,dwCount,pfVolumes)
#else
/*** IUnknown methods ***/
static inline HRESULT IChannelAudioVolume_QueryInterface(IChannelAudioVolume* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IChannelAudioVolume_AddRef(IChannelAudioVolume* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IChannelAudioVolume_Release(IChannelAudioVolume* This) {
    return This->lpVtbl->Release(This);
}
/*** IChannelAudioVolume methods ***/
static inline HRESULT IChannelAudioVolume_GetChannelCount(IChannelAudioVolume* This,UINT32 *pdwCount) {
    return This->lpVtbl->GetChannelCount(This,pdwCount);
}
static inline HRESULT IChannelAudioVolume_SetChannelVolume(IChannelAudioVolume* This,UINT32 dwIndex,const float fLevel,LPCGUID EventContext) {
    return This->lpVtbl->SetChannelVolume(This,dwIndex,fLevel,EventContext);
}
static inline HRESULT IChannelAudioVolume_GetChannelVolume(IChannelAudioVolume* This,UINT32 dwIndex,float *pfLevel) {
    return This->lpVtbl->GetChannelVolume(This,dwIndex,pfLevel);
}
static inline HRESULT IChannelAudioVolume_SetAllVolumes(IChannelAudioVolume* This,UINT32 dwCount,const float *pfVolumes,LPCGUID EventContext) {
    return This->lpVtbl->SetAllVolumes(This,dwCount,pfVolumes,EventContext);
}
static inline HRESULT IChannelAudioVolume_GetAllVolumes(IChannelAudioVolume* This,UINT32 dwCount,float *pfVolumes) {
    return This->lpVtbl->GetAllVolumes(This,dwCount,pfVolumes);
}
#endif
#endif

#endif


#endif  /* __IChannelAudioVolume_INTERFACE_DEFINED__ */

#endif

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#define AUDCLNT_ERR(n) MAKE_HRESULT(SEVERITY_ERROR, FACILITY_AUDCLNT, n)
#define AUDCLNT_SUCCESS(n) MAKE_SCODE(SEVERITY_SUCCESS, FACILITY_AUDCLNT, n)

#define AUDCLNT_E_NOT_INITIALIZED AUDCLNT_ERR(0x1)
#define AUDCLNT_E_ALREADY_INITIALIZED AUDCLNT_ERR(0x2)
#define AUDCLNT_E_WRONG_ENDPOINT_TYPE AUDCLNT_ERR(0x3)
#define AUDCLNT_E_DEVICE_INVALIDATED AUDCLNT_ERR(0x4)
#define AUDCLNT_E_NOT_STOPPED AUDCLNT_ERR(0x5)
#define AUDCLNT_E_BUFFER_TOO_LARGE AUDCLNT_ERR(0x6)
#define AUDCLNT_E_OUT_OF_ORDER AUDCLNT_ERR(0x7)
#define AUDCLNT_E_UNSUPPORTED_FORMAT AUDCLNT_ERR(0x8)
#define AUDCLNT_E_INVALID_SIZE AUDCLNT_ERR(0x9)
#define AUDCLNT_E_DEVICE_IN_USE AUDCLNT_ERR(0xa)
#define AUDCLNT_E_BUFFER_OPERATION_PENDING AUDCLNT_ERR(0xb)
#define AUDCLNT_E_THREAD_NOT_REGISTERED AUDCLNT_ERR(0xc)
#define AUDCLNT_E_EXCLUSIVE_MODE_NOT_ALLOWED AUDCLNT_ERR(0xe)
#define AUDCLNT_E_ENDPOINT_CREATE_FAILED AUDCLNT_ERR(0xf)
#define AUDCLNT_E_SERVICE_NOT_RUNNING AUDCLNT_ERR(0x10)
#define AUDCLNT_E_EVENTHANDLE_NOT_EXPECTED AUDCLNT_ERR(0x11)
#define AUDCLNT_E_EXCLUSIVE_MODE_ONLY AUDCLNT_ERR(0x12)
#define AUDCLNT_E_BUFDURATION_PERIOD_NOT_EQUAL AUDCLNT_ERR(0x13)
#define AUDCLNT_E_EVENTHANDLE_NOT_SET AUDCLNT_ERR(0x14)
#define AUDCLNT_E_INCORRECT_BUFFER_SIZE AUDCLNT_ERR(0x15)
#define AUDCLNT_E_BUFFER_SIZE_ERROR AUDCLNT_ERR(0x16)
#define AUDCLNT_E_CPUUSAGE_EXCEEDED AUDCLNT_ERR(0x17)
#define AUDCLNT_E_BUFFER_ERROR AUDCLNT_ERR(0x18)
#define AUDCLNT_E_BUFFER_SIZE_NOT_ALIGNED AUDCLNT_ERR(0x19)
#define AUDCLNT_E_INVALID_DEVICE_PERIOD AUDCLNT_ERR(0x20)
#define AUDCLNT_E_INVALID_STREAM_FLAG AUDCLNT_ERR(0x21)
#define AUDCLNT_E_ENDPOINT_OFFLOAD_NOT_CAPABLE AUDCLNT_ERR(0x22)
#define AUDCLNT_E_OUT_OF_OFFLOAD_RESOURCES AUDCLNT_ERR(0x23)
#define AUDCLNT_E_OFFLOAD_MODE_ONLY AUDCLNT_ERR(0x24)
#define AUDCLNT_E_NONOFFLOAD_MODE_ONLY AUDCLNT_ERR(0x25)
#define AUDCLNT_E_RESOURCES_INVALIDATED AUDCLNT_ERR(0x26)
#define AUDCLNT_E_RAW_MODE_UNSUPPORTED AUDCLNT_ERR(0x027)
#define AUDCLNT_E_ENGINE_PERIODICITY_LOCKED AUDCLNT_ERR(0x028)
#define AUDCLNT_E_ENGINE_FORMAT_LOCKED AUDCLNT_ERR(0x029)
#define AUDCLNT_E_HEADTRACKING_ENABLED AUDCLNT_ERR(0x030)
#define AUDCLNT_E_HEADTRACKING_UNSUPPORTED AUDCLNT_ERR(0x040)
#define AUDCLNT_S_BUFFER_EMPTY AUDCLNT_SUCCESS(0x1)
#define AUDCLNT_S_THREAD_ALREADY_REGISTERED AUDCLNT_SUCCESS(0x2)
#define AUDCLNT_S_POSITION_STALLED AUDCLNT_SUCCESS(0x3)
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __audioclient_h__ */
