/*** Autogenerated by WIDL 10.12 from include/windows.ui.viewmanagement.core.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_ui_viewmanagement_core_h__
#define __windows_ui_viewmanagement_core_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView ABI::Windows::UI::ViewManagement::Core::ICoreInputView
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputView;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 ABI::Windows::UI::ViewManagement::Core::ICoreInputView2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputView2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 ABI::Windows::UI::ViewManagement::Core::ICoreInputView3
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputView3;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 ABI::Windows::UI::ViewManagement::Core::ICoreInputView4
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputView4;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs ABI::Windows::UI::ViewManagement::Core::ICoreInputViewHidingEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewHidingEventArgs;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion ABI::Windows::UI::ViewManagement::Core::ICoreInputViewOcclusion
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewOcclusion;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs ABI::Windows::UI::ViewManagement::Core::ICoreInputViewOcclusionsChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewOcclusionsChangedEventArgs;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs ABI::Windows::UI::ViewManagement::Core::ICoreInputViewShowingEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewShowingEventArgs;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics ABI::Windows::UI::ViewManagement::Core::ICoreInputViewStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 ABI::Windows::UI::ViewManagement::Core::ICoreInputViewStatics2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewStatics2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs ABI::Windows::UI::ViewManagement::Core::ICoreInputViewTransferringXYFocusEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewTransferringXYFocusEventArgs;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputView_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputView_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    class CoreInputView;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputView __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputView;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputView_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewHidingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewHidingEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    class CoreInputViewHidingEventArgs;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewHidingEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewHidingEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewHidingEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusion_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusion_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    class CoreInputViewOcclusion;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusion __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusion;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusion_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionsChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionsChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    class CoreInputViewOcclusionsChangedEventArgs;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionsChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionsChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionsChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewShowingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewShowingEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    class CoreInputViewShowingEventArgs;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewShowingEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewShowingEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewShowingEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewTransferringXYFocusEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewTransferringXYFocusEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    class CoreInputViewTransferringXYFocusEventArgs;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewTransferringXYFocusEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewTransferringXYFocusEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewTransferringXYFocusEventArgs_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
#define ____FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
#define ____FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewHidingEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusionsChangedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewShowingEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewTransferringXYFocusEventArgs* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.ui.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewKind_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewKind_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    enum CoreInputViewKind {
                        CoreInputViewKind_Default = 0,
                        CoreInputViewKind_Keyboard = 1,
                        CoreInputViewKind_Handwriting = 2,
                        CoreInputViewKind_Emoji = 3,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
                        CoreInputViewKind_Symbols = 4
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewKind {
    CoreInputViewKind_Default = 0,
    CoreInputViewKind_Keyboard = 1,
    CoreInputViewKind_Handwriting = 2,
    CoreInputViewKind_Emoji = 3,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
    CoreInputViewKind_Symbols = 4
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
};
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define CoreInputViewKind __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewKind
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewKind_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewKind __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewKind;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionKind_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionKind_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    enum CoreInputViewOcclusionKind {
                        CoreInputViewOcclusionKind_Docked = 0,
                        CoreInputViewOcclusionKind_Floating = 1,
                        CoreInputViewOcclusionKind_Overlay = 2
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionKind {
    CoreInputViewOcclusionKind_Docked = 0,
    CoreInputViewOcclusionKind_Floating = 1,
    CoreInputViewOcclusionKind_Overlay = 2
};
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define CoreInputViewOcclusionKind __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionKind
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionKind_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionKind __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionKind;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewXYFocusTransferDirection_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewXYFocusTransferDirection_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    enum CoreInputViewXYFocusTransferDirection {
                        CoreInputViewXYFocusTransferDirection_Up = 0,
                        CoreInputViewXYFocusTransferDirection_Right = 1,
                        CoreInputViewXYFocusTransferDirection_Down = 2,
                        CoreInputViewXYFocusTransferDirection_Left = 3
                    };
                }
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewXYFocusTransferDirection {
    CoreInputViewXYFocusTransferDirection_Up = 0,
    CoreInputViewXYFocusTransferDirection_Right = 1,
    CoreInputViewXYFocusTransferDirection_Down = 2,
    CoreInputViewXYFocusTransferDirection_Left = 3
};
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define CoreInputViewXYFocusTransferDirection __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewXYFocusTransferDirection
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewXYFocusTransferDirection_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewXYFocusTransferDirection __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewXYFocusTransferDirection;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView ABI::Windows::UI::ViewManagement::Core::ICoreInputView
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputView;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 ABI::Windows::UI::ViewManagement::Core::ICoreInputView2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputView2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 ABI::Windows::UI::ViewManagement::Core::ICoreInputView3
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputView3;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 ABI::Windows::UI::ViewManagement::Core::ICoreInputView4
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputView4;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs ABI::Windows::UI::ViewManagement::Core::ICoreInputViewHidingEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewHidingEventArgs;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion ABI::Windows::UI::ViewManagement::Core::ICoreInputViewOcclusion
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewOcclusion;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs ABI::Windows::UI::ViewManagement::Core::ICoreInputViewOcclusionsChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewOcclusionsChangedEventArgs;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs ABI::Windows::UI::ViewManagement::Core::ICoreInputViewShowingEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewShowingEventArgs;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics ABI::Windows::UI::ViewManagement::Core::ICoreInputViewStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 ABI::Windows::UI::ViewManagement::Core::ICoreInputViewStatics2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewStatics2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs ABI::Windows::UI::ViewManagement::Core::ICoreInputViewTransferringXYFocusEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    interface ICoreInputViewTransferringXYFocusEventArgs;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
#define ____FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
#define ____FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * ICoreInputView interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView, 0xc770cd7a, 0x7001, 0x4c32, 0xbf,0x94, 0x25,0xc1,0xf5,0x54,0xcb,0xf1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    MIDL_INTERFACE("c770cd7a-7001-4c32-bf94-25c1f554cbf1")
                    ICoreInputView : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE add_OcclusionsChanged(
                            ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusionsChangedEventArgs* > *handler,
                            EventRegistrationToken *token) = 0;

                        virtual HRESULT STDMETHODCALLTYPE remove_OcclusionsChanged(
                            EventRegistrationToken token) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetCoreInputViewOcclusions(
                            ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > **result) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryShowPrimaryView(
                            boolean *result) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryHidePrimaryView(
                            boolean *result) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView, 0xc770cd7a, 0x7001, 0x4c32, 0xbf,0x94, 0x25,0xc1,0xf5,0x54,0xcb,0xf1)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *This,
        TrustLevel *trustLevel);

    /*** ICoreInputView methods ***/
    HRESULT (STDMETHODCALLTYPE *add_OcclusionsChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_OcclusionsChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *GetCoreInputViewOcclusions)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *This,
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion **result);

    HRESULT (STDMETHODCALLTYPE *TryShowPrimaryView)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *This,
        boolean *result);

    HRESULT (STDMETHODCALLTYPE *TryHidePrimaryView)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *This,
        boolean *result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreInputView methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_add_OcclusionsChanged(This,handler,token) (This)->lpVtbl->add_OcclusionsChanged(This,handler,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_remove_OcclusionsChanged(This,token) (This)->lpVtbl->remove_OcclusionsChanged(This,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetCoreInputViewOcclusions(This,result) (This)->lpVtbl->GetCoreInputViewOcclusions(This,result)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_TryShowPrimaryView(This,result) (This)->lpVtbl->TryShowPrimaryView(This,result)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_TryHidePrimaryView(This,result) (This)->lpVtbl->TryHidePrimaryView(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_Release(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreInputView methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_add_OcclusionsChanged(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_OcclusionsChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_remove_OcclusionsChanged(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_OcclusionsChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetCoreInputViewOcclusions(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView* This,__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion **result) {
    return This->lpVtbl->GetCoreInputViewOcclusions(This,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_TryShowPrimaryView(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView* This,boolean *result) {
    return This->lpVtbl->TryShowPrimaryView(This,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_TryHidePrimaryView(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView* This,boolean *result) {
    return This->lpVtbl->TryHidePrimaryView(This,result);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define IID_ICoreInputView IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView
#define ICoreInputViewVtbl __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewVtbl
#define ICoreInputView __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView
#define ICoreInputView_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_QueryInterface
#define ICoreInputView_AddRef __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_AddRef
#define ICoreInputView_Release __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_Release
#define ICoreInputView_GetIids __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetIids
#define ICoreInputView_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetRuntimeClassName
#define ICoreInputView_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetTrustLevel
#define ICoreInputView_add_OcclusionsChanged __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_add_OcclusionsChanged
#define ICoreInputView_remove_OcclusionsChanged __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_remove_OcclusionsChanged
#define ICoreInputView_GetCoreInputViewOcclusions __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_GetCoreInputViewOcclusions
#define ICoreInputView_TryShowPrimaryView __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_TryShowPrimaryView
#define ICoreInputView_TryHidePrimaryView __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_TryHidePrimaryView
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * ICoreInputView2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2, 0x0ed726c1, 0xe09a, 0x4ae8, 0xae,0xdf, 0xdf,0xa4,0x85,0x7d,0x1a,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    MIDL_INTERFACE("0ed726c1-e09a-4ae8-aedf-dfa4857d1a01")
                    ICoreInputView2 : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE add_XYFocusTransferringFromPrimaryView(
                            ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewTransferringXYFocusEventArgs* > *handler,
                            EventRegistrationToken *token) = 0;

                        virtual HRESULT STDMETHODCALLTYPE remove_XYFocusTransferringFromPrimaryView(
                            EventRegistrationToken token) = 0;

                        virtual HRESULT STDMETHODCALLTYPE add_XYFocusTransferredToPrimaryView(
                            ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,IInspectable* > *handler,
                            EventRegistrationToken *token) = 0;

                        virtual HRESULT STDMETHODCALLTYPE remove_XYFocusTransferredToPrimaryView(
                            EventRegistrationToken token) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryTransferXYFocusToPrimaryView(
                            ABI::Windows::Foundation::Rect origin,
                            ABI::Windows::UI::ViewManagement::Core::CoreInputViewXYFocusTransferDirection direction,
                            boolean *result) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2, 0x0ed726c1, 0xe09a, 0x4ae8, 0xae,0xdf, 0xdf,0xa4,0x85,0x7d,0x1a,0x01)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 *This,
        TrustLevel *trustLevel);

    /*** ICoreInputView2 methods ***/
    HRESULT (STDMETHODCALLTYPE *add_XYFocusTransferringFromPrimaryView)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_XYFocusTransferringFromPrimaryView)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_XYFocusTransferredToPrimaryView)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_XYFocusTransferredToPrimaryView)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *TryTransferXYFocusToPrimaryView)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 *This,
        __x_ABI_CWindows_CFoundation_CRect origin,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewXYFocusTransferDirection direction,
        boolean *result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2Vtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2 {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreInputView2 methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_add_XYFocusTransferringFromPrimaryView(This,handler,token) (This)->lpVtbl->add_XYFocusTransferringFromPrimaryView(This,handler,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_remove_XYFocusTransferringFromPrimaryView(This,token) (This)->lpVtbl->remove_XYFocusTransferringFromPrimaryView(This,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_add_XYFocusTransferredToPrimaryView(This,handler,token) (This)->lpVtbl->add_XYFocusTransferredToPrimaryView(This,handler,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_remove_XYFocusTransferredToPrimaryView(This,token) (This)->lpVtbl->remove_XYFocusTransferredToPrimaryView(This,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_TryTransferXYFocusToPrimaryView(This,origin,direction,result) (This)->lpVtbl->TryTransferXYFocusToPrimaryView(This,origin,direction,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_Release(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreInputView2 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_add_XYFocusTransferringFromPrimaryView(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_XYFocusTransferringFromPrimaryView(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_remove_XYFocusTransferringFromPrimaryView(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_XYFocusTransferringFromPrimaryView(This,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_add_XYFocusTransferredToPrimaryView(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_XYFocusTransferredToPrimaryView(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_remove_XYFocusTransferredToPrimaryView(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_XYFocusTransferredToPrimaryView(This,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_TryTransferXYFocusToPrimaryView(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2* This,__x_ABI_CWindows_CFoundation_CRect origin,__x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewXYFocusTransferDirection direction,boolean *result) {
    return This->lpVtbl->TryTransferXYFocusToPrimaryView(This,origin,direction,result);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define IID_ICoreInputView2 IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2
#define ICoreInputView2Vtbl __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2Vtbl
#define ICoreInputView2 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2
#define ICoreInputView2_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_QueryInterface
#define ICoreInputView2_AddRef __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_AddRef
#define ICoreInputView2_Release __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_Release
#define ICoreInputView2_GetIids __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_GetIids
#define ICoreInputView2_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_GetRuntimeClassName
#define ICoreInputView2_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_GetTrustLevel
#define ICoreInputView2_add_XYFocusTransferringFromPrimaryView __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_add_XYFocusTransferringFromPrimaryView
#define ICoreInputView2_remove_XYFocusTransferringFromPrimaryView __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_remove_XYFocusTransferringFromPrimaryView
#define ICoreInputView2_add_XYFocusTransferredToPrimaryView __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_add_XYFocusTransferredToPrimaryView
#define ICoreInputView2_remove_XYFocusTransferredToPrimaryView __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_remove_XYFocusTransferredToPrimaryView
#define ICoreInputView2_TryTransferXYFocusToPrimaryView __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_TryTransferXYFocusToPrimaryView
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * ICoreInputView3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3, 0xbc941653, 0x3ab9, 0x4849, 0x8f,0x58, 0x46,0xe7,0xf0,0x35,0x3c,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    MIDL_INTERFACE("bc941653-3ab9-4849-8f58-46e7f0353cfc")
                    ICoreInputView3 : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE TryShow(
                            boolean *result) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryShowWithKind(
                            ABI::Windows::UI::ViewManagement::Core::CoreInputViewKind type,
                            boolean *result) = 0;

                        virtual HRESULT STDMETHODCALLTYPE TryHide(
                            boolean *result) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3, 0xbc941653, 0x3ab9, 0x4849, 0x8f,0x58, 0x46,0xe7,0xf0,0x35,0x3c,0xfc)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 *This,
        TrustLevel *trustLevel);

    /*** ICoreInputView3 methods ***/
    HRESULT (STDMETHODCALLTYPE *TryShow)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 *This,
        boolean *result);

    HRESULT (STDMETHODCALLTYPE *TryShowWithKind)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 *This,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewKind type,
        boolean *result);

    HRESULT (STDMETHODCALLTYPE *TryHide)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 *This,
        boolean *result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3Vtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3 {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreInputView3 methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_TryShow(This,result) (This)->lpVtbl->TryShow(This,result)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_TryShowWithKind(This,type,result) (This)->lpVtbl->TryShowWithKind(This,type,result)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_TryHide(This,result) (This)->lpVtbl->TryHide(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_Release(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreInputView3 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_TryShow(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3* This,boolean *result) {
    return This->lpVtbl->TryShow(This,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_TryShowWithKind(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3* This,__x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewKind type,boolean *result) {
    return This->lpVtbl->TryShowWithKind(This,type,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_TryHide(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3* This,boolean *result) {
    return This->lpVtbl->TryHide(This,result);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define IID_ICoreInputView3 IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3
#define ICoreInputView3Vtbl __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3Vtbl
#define ICoreInputView3 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3
#define ICoreInputView3_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_QueryInterface
#define ICoreInputView3_AddRef __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_AddRef
#define ICoreInputView3_Release __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_Release
#define ICoreInputView3_GetIids __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_GetIids
#define ICoreInputView3_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_GetRuntimeClassName
#define ICoreInputView3_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_GetTrustLevel
#define ICoreInputView3_TryShow __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_TryShow
#define ICoreInputView3_TryShowWithKind __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_TryShowWithKind
#define ICoreInputView3_TryHide __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_TryHide
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000 */

/*****************************************************************************
 * ICoreInputView4 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4, 0x002863d6, 0xd9ef, 0x57eb, 0x8c,0xef, 0x77,0xf6,0xce,0x1b,0x7e,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    MIDL_INTERFACE("002863d6-d9ef-57eb-8cef-77f6ce1b7ee7")
                    ICoreInputView4 : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE add_PrimaryViewShowing(
                            ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewShowingEventArgs* > *handler,
                            EventRegistrationToken *token) = 0;

                        virtual HRESULT STDMETHODCALLTYPE remove_PrimaryViewShowing(
                            EventRegistrationToken token) = 0;

                        virtual HRESULT STDMETHODCALLTYPE add_PrimaryViewHiding(
                            ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewHidingEventArgs* > *handler,
                            EventRegistrationToken *token) = 0;

                        virtual HRESULT STDMETHODCALLTYPE remove_PrimaryViewHiding(
                            EventRegistrationToken token) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4, 0x002863d6, 0xd9ef, 0x57eb, 0x8c,0xef, 0x77,0xf6,0xce,0x1b,0x7e,0xe7)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 *This,
        TrustLevel *trustLevel);

    /*** ICoreInputView4 methods ***/
    HRESULT (STDMETHODCALLTYPE *add_PrimaryViewShowing)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_PrimaryViewShowing)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_PrimaryViewHiding)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_PrimaryViewHiding)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4Vtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4 {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreInputView4 methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_add_PrimaryViewShowing(This,handler,token) (This)->lpVtbl->add_PrimaryViewShowing(This,handler,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_remove_PrimaryViewShowing(This,token) (This)->lpVtbl->remove_PrimaryViewShowing(This,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_add_PrimaryViewHiding(This,handler,token) (This)->lpVtbl->add_PrimaryViewHiding(This,handler,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_remove_PrimaryViewHiding(This,token) (This)->lpVtbl->remove_PrimaryViewHiding(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_Release(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreInputView4 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_add_PrimaryViewShowing(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_PrimaryViewShowing(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_remove_PrimaryViewShowing(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_PrimaryViewShowing(This,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_add_PrimaryViewHiding(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_PrimaryViewHiding(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_remove_PrimaryViewHiding(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_PrimaryViewHiding(This,token);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define IID_ICoreInputView4 IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4
#define ICoreInputView4Vtbl __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4Vtbl
#define ICoreInputView4 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4
#define ICoreInputView4_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_QueryInterface
#define ICoreInputView4_AddRef __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_AddRef
#define ICoreInputView4_Release __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_Release
#define ICoreInputView4_GetIids __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_GetIids
#define ICoreInputView4_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_GetRuntimeClassName
#define ICoreInputView4_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_GetTrustLevel
#define ICoreInputView4_add_PrimaryViewShowing __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_add_PrimaryViewShowing
#define ICoreInputView4_remove_PrimaryViewShowing __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_remove_PrimaryViewShowing
#define ICoreInputView4_add_PrimaryViewHiding __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_add_PrimaryViewHiding
#define ICoreInputView4_remove_PrimaryViewHiding __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_remove_PrimaryViewHiding
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView4_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * ICoreInputViewHidingEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs, 0xeada47bd, 0xbac5, 0x5336, 0x84,0x8d, 0x41,0x08,0x35,0x84,0xda,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    MIDL_INTERFACE("eada47bd-bac5-5336-848d-41083584daad")
                    ICoreInputViewHidingEventArgs : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE TryCancel(
                            boolean *result) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs, 0xeada47bd, 0xbac5, 0x5336, 0x84,0x8d, 0x41,0x08,0x35,0x84,0xda,0xad)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs *This,
        TrustLevel *trustLevel);

    /*** ICoreInputViewHidingEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *TryCancel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs *This,
        boolean *result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreInputViewHidingEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_TryCancel(This,result) (This)->lpVtbl->TryCancel(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_Release(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreInputViewHidingEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_TryCancel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs* This,boolean *result) {
    return This->lpVtbl->TryCancel(This,result);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define IID_ICoreInputViewHidingEventArgs IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs
#define ICoreInputViewHidingEventArgsVtbl __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgsVtbl
#define ICoreInputViewHidingEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs
#define ICoreInputViewHidingEventArgs_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_QueryInterface
#define ICoreInputViewHidingEventArgs_AddRef __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_AddRef
#define ICoreInputViewHidingEventArgs_Release __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_Release
#define ICoreInputViewHidingEventArgs_GetIids __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_GetIids
#define ICoreInputViewHidingEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_GetRuntimeClassName
#define ICoreInputViewHidingEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_GetTrustLevel
#define ICoreInputViewHidingEventArgs_TryCancel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_TryCancel
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * ICoreInputViewOcclusion interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion, 0xcc36ce06, 0x3865, 0x4177, 0xb5,0xf5, 0x8b,0x65,0xe0,0xb9,0xce,0x84);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    MIDL_INTERFACE("cc36ce06-3865-4177-b5f5-8b65e0b9ce84")
                    ICoreInputViewOcclusion : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_OccludingRect(
                            ABI::Windows::Foundation::Rect *rect) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_OcclusionKind(
                            ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusionKind *kind) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion, 0xcc36ce06, 0x3865, 0x4177, 0xb5,0xf5, 0x8b,0x65,0xe0,0xb9,0xce,0x84)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion *This,
        TrustLevel *trustLevel);

    /*** ICoreInputViewOcclusion methods ***/
    HRESULT (STDMETHODCALLTYPE *get_OccludingRect)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion *This,
        __x_ABI_CWindows_CFoundation_CRect *rect);

    HRESULT (STDMETHODCALLTYPE *get_OcclusionKind)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion *This,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionKind *kind);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreInputViewOcclusion methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_get_OccludingRect(This,rect) (This)->lpVtbl->get_OccludingRect(This,rect)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_get_OcclusionKind(This,kind) (This)->lpVtbl->get_OcclusionKind(This,kind)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_Release(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreInputViewOcclusion methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_get_OccludingRect(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion* This,__x_ABI_CWindows_CFoundation_CRect *rect) {
    return This->lpVtbl->get_OccludingRect(This,rect);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_get_OcclusionKind(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion* This,__x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewOcclusionKind *kind) {
    return This->lpVtbl->get_OcclusionKind(This,kind);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define IID_ICoreInputViewOcclusion IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion
#define ICoreInputViewOcclusionVtbl __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionVtbl
#define ICoreInputViewOcclusion __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion
#define ICoreInputViewOcclusion_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_QueryInterface
#define ICoreInputViewOcclusion_AddRef __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_AddRef
#define ICoreInputViewOcclusion_Release __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_Release
#define ICoreInputViewOcclusion_GetIids __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_GetIids
#define ICoreInputViewOcclusion_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_GetRuntimeClassName
#define ICoreInputViewOcclusion_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_GetTrustLevel
#define ICoreInputViewOcclusion_get_OccludingRect __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_get_OccludingRect
#define ICoreInputViewOcclusion_get_OcclusionKind __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_get_OcclusionKind
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * ICoreInputViewOcclusionsChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs, 0xbe1027e8, 0xb3ee, 0x4df7, 0x95,0x54, 0x89,0xcd,0xc6,0x60,0x82,0xc2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    MIDL_INTERFACE("be1027e8-b3ee-4df7-9554-89cdc66082c2")
                    ICoreInputViewOcclusionsChangedEventArgs : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Occlusions(
                            ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Handled(
                            boolean *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_Handled(
                            boolean value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs, 0xbe1027e8, 0xb3ee, 0x4df7, 0x95,0x54, 0x89,0xcd,0xc6,0x60,0x82,0xc2)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs *This,
        TrustLevel *trustLevel);

    /*** ICoreInputViewOcclusionsChangedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Occlusions)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs *This,
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion **value);

    HRESULT (STDMETHODCALLTYPE *get_Handled)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_Handled)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs *This,
        boolean value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreInputViewOcclusionsChangedEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_get_Occlusions(This,value) (This)->lpVtbl->get_Occlusions(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_get_Handled(This,value) (This)->lpVtbl->get_Handled(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_put_Handled(This,value) (This)->lpVtbl->put_Handled(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_Release(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreInputViewOcclusionsChangedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_get_Occlusions(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs* This,__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion **value) {
    return This->lpVtbl->get_Occlusions(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_get_Handled(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs* This,boolean *value) {
    return This->lpVtbl->get_Handled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_put_Handled(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs* This,boolean value) {
    return This->lpVtbl->put_Handled(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define IID_ICoreInputViewOcclusionsChangedEventArgs IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs
#define ICoreInputViewOcclusionsChangedEventArgsVtbl __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgsVtbl
#define ICoreInputViewOcclusionsChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs
#define ICoreInputViewOcclusionsChangedEventArgs_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_QueryInterface
#define ICoreInputViewOcclusionsChangedEventArgs_AddRef __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_AddRef
#define ICoreInputViewOcclusionsChangedEventArgs_Release __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_Release
#define ICoreInputViewOcclusionsChangedEventArgs_GetIids __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_GetIids
#define ICoreInputViewOcclusionsChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_GetRuntimeClassName
#define ICoreInputViewOcclusionsChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_GetTrustLevel
#define ICoreInputViewOcclusionsChangedEventArgs_get_Occlusions __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_get_Occlusions
#define ICoreInputViewOcclusionsChangedEventArgs_get_Handled __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_get_Handled
#define ICoreInputViewOcclusionsChangedEventArgs_put_Handled __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_put_Handled
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * ICoreInputViewShowingEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs, 0xca52261b, 0xfb9e, 0x5daf, 0xa9,0x8c, 0x26,0x2b,0x8b,0x76,0xaf,0x50);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    MIDL_INTERFACE("ca52261b-fb9e-5daf-a98c-262b8b76af50")
                    ICoreInputViewShowingEventArgs : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE TryCancel(
                            boolean *result) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs, 0xca52261b, 0xfb9e, 0x5daf, 0xa9,0x8c, 0x26,0x2b,0x8b,0x76,0xaf,0x50)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs *This,
        TrustLevel *trustLevel);

    /*** ICoreInputViewShowingEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *TryCancel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs *This,
        boolean *result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreInputViewShowingEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_TryCancel(This,result) (This)->lpVtbl->TryCancel(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_Release(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreInputViewShowingEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_TryCancel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs* This,boolean *result) {
    return This->lpVtbl->TryCancel(This,result);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define IID_ICoreInputViewShowingEventArgs IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs
#define ICoreInputViewShowingEventArgsVtbl __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgsVtbl
#define ICoreInputViewShowingEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs
#define ICoreInputViewShowingEventArgs_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_QueryInterface
#define ICoreInputViewShowingEventArgs_AddRef __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_AddRef
#define ICoreInputViewShowingEventArgs_Release __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_Release
#define ICoreInputViewShowingEventArgs_GetIids __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_GetIids
#define ICoreInputViewShowingEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_GetRuntimeClassName
#define ICoreInputViewShowingEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_GetTrustLevel
#define ICoreInputViewShowingEventArgs_TryCancel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_TryCancel
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * ICoreInputViewStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics, 0x7d9b97cd, 0xedbe, 0x49cf, 0xa5,0x4f, 0x33,0x7d,0xe0,0x52,0x90,0x7f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    MIDL_INTERFACE("7d9b97cd-edbe-49cf-a54f-337de052907f")
                    ICoreInputViewStatics : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE GetForCurrentView(
                            ABI::Windows::UI::ViewManagement::Core::ICoreInputView **result) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics, 0x7d9b97cd, 0xedbe, 0x49cf, 0xa5,0x4f, 0x33,0x7d,0xe0,0x52,0x90,0x7f)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics *This,
        TrustLevel *trustLevel);

    /*** ICoreInputViewStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetForCurrentView)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics *This,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView **result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStaticsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreInputViewStatics methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetForCurrentView(This,result) (This)->lpVtbl->GetForCurrentView(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_Release(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreInputViewStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetForCurrentView(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics* This,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView **result) {
    return This->lpVtbl->GetForCurrentView(This,result);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define IID_ICoreInputViewStatics IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics
#define ICoreInputViewStaticsVtbl __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStaticsVtbl
#define ICoreInputViewStatics __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics
#define ICoreInputViewStatics_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_QueryInterface
#define ICoreInputViewStatics_AddRef __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_AddRef
#define ICoreInputViewStatics_Release __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_Release
#define ICoreInputViewStatics_GetIids __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetIids
#define ICoreInputViewStatics_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetRuntimeClassName
#define ICoreInputViewStatics_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetTrustLevel
#define ICoreInputViewStatics_GetForCurrentView __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_GetForCurrentView
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * ICoreInputViewStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2, 0x7ebc0862, 0xd049, 0x4e52, 0x87,0xb0, 0x1e,0x90,0xe9,0x8c,0x49,0xed);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    MIDL_INTERFACE("7ebc0862-d049-4e52-87b0-1e90e98c49ed")
                    ICoreInputViewStatics2 : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE GetForUIContext(
                            ABI::Windows::UI::IUIContext *context,
                            ABI::Windows::UI::ViewManagement::Core::ICoreInputView **result) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2, 0x7ebc0862, 0xd049, 0x4e52, 0x87,0xb0, 0x1e,0x90,0xe9,0x8c,0x49,0xed)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 *This,
        TrustLevel *trustLevel);

    /*** ICoreInputViewStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetForUIContext)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 *This,
        __x_ABI_CWindows_CUI_CIUIContext *context,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView **result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2Vtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2 {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreInputViewStatics2 methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetForUIContext(This,context,result) (This)->lpVtbl->GetForUIContext(This,context,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_Release(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreInputViewStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetForUIContext(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2* This,__x_ABI_CWindows_CUI_CIUIContext *context,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView **result) {
    return This->lpVtbl->GetForUIContext(This,context,result);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define IID_ICoreInputViewStatics2 IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2
#define ICoreInputViewStatics2Vtbl __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2Vtbl
#define ICoreInputViewStatics2 __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2
#define ICoreInputViewStatics2_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_QueryInterface
#define ICoreInputViewStatics2_AddRef __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_AddRef
#define ICoreInputViewStatics2_Release __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_Release
#define ICoreInputViewStatics2_GetIids __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetIids
#define ICoreInputViewStatics2_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetRuntimeClassName
#define ICoreInputViewStatics2_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetTrustLevel
#define ICoreInputViewStatics2_GetForUIContext __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_GetForUIContext
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*****************************************************************************
 * ICoreInputViewTransferringXYFocusEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs, 0x04de169f, 0xba02, 0x4850, 0x8b,0x55, 0xd8,0x2d,0x03,0xba,0x6d,0x7f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                namespace Core {
                    MIDL_INTERFACE("04de169f-ba02-4850-8b55-d82d03ba6d7f")
                    ICoreInputViewTransferringXYFocusEventArgs : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_Origin(
                            ABI::Windows::Foundation::Rect *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_Direction(
                            ABI::Windows::UI::ViewManagement::Core::CoreInputViewXYFocusTransferDirection *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_TransferHandled(
                            boolean value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_TransferHandled(
                            boolean *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE put_KeepPrimaryViewVisible(
                            boolean value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_KeepPrimaryViewVisible(
                            boolean *value) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs, 0x04de169f, 0xba02, 0x4850, 0x8b,0x55, 0xd8,0x2d,0x03,0xba,0x6d,0x7f)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This,
        TrustLevel *trustLevel);

    /*** ICoreInputViewTransferringXYFocusEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Origin)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    HRESULT (STDMETHODCALLTYPE *get_Direction)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewXYFocusTransferDirection *value);

    HRESULT (STDMETHODCALLTYPE *put_TransferHandled)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_TransferHandled)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_KeepPrimaryViewVisible)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_KeepPrimaryViewVisible)(
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreInputViewTransferringXYFocusEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_Origin(This,value) (This)->lpVtbl->get_Origin(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_Direction(This,value) (This)->lpVtbl->get_Direction(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_put_TransferHandled(This,value) (This)->lpVtbl->put_TransferHandled(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_TransferHandled(This,value) (This)->lpVtbl->get_TransferHandled(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_put_KeepPrimaryViewVisible(This,value) (This)->lpVtbl->put_KeepPrimaryViewVisible(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_KeepPrimaryViewVisible(This,value) (This)->lpVtbl->get_KeepPrimaryViewVisible(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_Release(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreInputViewTransferringXYFocusEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_Origin(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_Origin(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_Direction(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This,__x_ABI_CWindows_CUI_CViewManagement_CCore_CCoreInputViewXYFocusTransferDirection *value) {
    return This->lpVtbl->get_Direction(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_put_TransferHandled(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This,boolean value) {
    return This->lpVtbl->put_TransferHandled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_TransferHandled(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This,boolean *value) {
    return This->lpVtbl->get_TransferHandled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_put_KeepPrimaryViewVisible(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This,boolean value) {
    return This->lpVtbl->put_KeepPrimaryViewVisible(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_KeepPrimaryViewVisible(__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs* This,boolean *value) {
    return This->lpVtbl->get_KeepPrimaryViewVisible(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement_Core
#define IID_ICoreInputViewTransferringXYFocusEventArgs IID___x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs
#define ICoreInputViewTransferringXYFocusEventArgsVtbl __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgsVtbl
#define ICoreInputViewTransferringXYFocusEventArgs __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs
#define ICoreInputViewTransferringXYFocusEventArgs_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_QueryInterface
#define ICoreInputViewTransferringXYFocusEventArgs_AddRef __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_AddRef
#define ICoreInputViewTransferringXYFocusEventArgs_Release __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_Release
#define ICoreInputViewTransferringXYFocusEventArgs_GetIids __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_GetIids
#define ICoreInputViewTransferringXYFocusEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_GetRuntimeClassName
#define ICoreInputViewTransferringXYFocusEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_GetTrustLevel
#define ICoreInputViewTransferringXYFocusEventArgs_get_Origin __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_Origin
#define ICoreInputViewTransferringXYFocusEventArgs_get_Direction __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_Direction
#define ICoreInputViewTransferringXYFocusEventArgs_put_TransferHandled __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_put_TransferHandled
#define ICoreInputViewTransferringXYFocusEventArgs_get_TransferHandled __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_TransferHandled
#define ICoreInputViewTransferringXYFocusEventArgs_put_KeepPrimaryViewVisible __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_put_KeepPrimaryViewVisible
#define ICoreInputViewTransferringXYFocusEventArgs_get_KeepPrimaryViewVisible __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_get_KeepPrimaryViewVisible
#endif /* WIDL_using_Windows_UI_ViewManagement_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*
 * Class Windows.UI.ViewManagement.Core.CoreInputView
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputView_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputView_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputView[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputView[] = L"Windows.UI.ViewManagement.Core.CoreInputView";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputView[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputView_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*
 * Class Windows.UI.ViewManagement.Core.CoreInputViewHidingEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewHidingEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewHidingEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewHidingEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w','H','i','d','i','n','g','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewHidingEventArgs[] = L"Windows.UI.ViewManagement.Core.CoreInputViewHidingEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewHidingEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w','H','i','d','i','n','g','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewHidingEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*
 * Class Windows.UI.ViewManagement.Core.CoreInputViewOcclusion
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewOcclusion_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewOcclusion_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewOcclusion[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w','O','c','c','l','u','s','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewOcclusion[] = L"Windows.UI.ViewManagement.Core.CoreInputViewOcclusion";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewOcclusion[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w','O','c','c','l','u','s','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewOcclusion_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*
 * Class Windows.UI.ViewManagement.Core.CoreInputViewOcclusionsChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewOcclusionsChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewOcclusionsChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewOcclusionsChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w','O','c','c','l','u','s','i','o','n','s','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewOcclusionsChangedEventArgs[] = L"Windows.UI.ViewManagement.Core.CoreInputViewOcclusionsChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewOcclusionsChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w','O','c','c','l','u','s','i','o','n','s','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewOcclusionsChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*
 * Class Windows.UI.ViewManagement.Core.CoreInputViewShowingEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewShowingEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewShowingEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewShowingEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w','S','h','o','w','i','n','g','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewShowingEventArgs[] = L"Windows.UI.ViewManagement.Core.CoreInputViewShowingEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewShowingEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w','S','h','o','w','i','n','g','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewShowingEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*
 * Class Windows.UI.ViewManagement.Core.CoreInputViewTransferringXYFocusEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewTransferringXYFocusEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewTransferringXYFocusEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewTransferringXYFocusEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w','T','r','a','n','s','f','e','r','r','i','n','g','X','Y','F','o','c','u','s','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewTransferringXYFocusEventArgs[] = L"Windows.UI.ViewManagement.Core.CoreInputViewTransferringXYFocusEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_Core_CoreInputViewTransferringXYFocusEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','C','o','r','e','.','C','o','r','e','I','n','p','u','t','V','i','e','w','T','r','a','n','s','f','e','r','r','i','n','g','X','Y','F','o','c','u','s','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_Core_CoreInputViewTransferringXYFocusEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * IIterable<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > interface
 */
#ifndef ____FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion, 0x0a11958b, 0x63da, 0x5566, 0x91,0x3a, 0x18,0x05,0x50,0xda,0xd2,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("0a11958b-63da-5566-913a-180550dad26a")
                IIterable<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion*, ABI::Windows::UI::ViewManagement::Core::ICoreInputViewOcclusion* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion, 0x0a11958b, 0x63da, 0x5566, 0x91,0x3a, 0x18,0x05,0x50,0xda,0xd2,0x6a)
#endif
#else
typedef struct __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion **value);

    END_INTERFACE
} __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl;

interface __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion {
    CONST_VTBL __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > methods ***/
#define __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_QueryInterface(__FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_AddRef(__FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_Release(__FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetIids(__FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetRuntimeClassName(__FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetTrustLevel(__FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_First(__FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_CoreInputViewOcclusion IID___FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion
#define IIterable_CoreInputViewOcclusionVtbl __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl
#define IIterable_CoreInputViewOcclusion __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion
#define IIterable_CoreInputViewOcclusion_QueryInterface __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_QueryInterface
#define IIterable_CoreInputViewOcclusion_AddRef __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_AddRef
#define IIterable_CoreInputViewOcclusion_Release __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_Release
#define IIterable_CoreInputViewOcclusion_GetIids __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetIids
#define IIterable_CoreInputViewOcclusion_GetRuntimeClassName __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetRuntimeClassName
#define IIterable_CoreInputViewOcclusion_GetTrustLevel __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetTrustLevel
#define IIterable_CoreInputViewOcclusion_First __FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > interface
 */
#ifndef ____FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion, 0x5bb57354, 0x4f40, 0x5ef3, 0xa5,0xd1, 0x6a,0x60,0x49,0xf9,0x05,0xa1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("5bb57354-4f40-5ef3-a5d1-6a6049f905a1")
                IIterator<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion*, ABI::Windows::UI::ViewManagement::Core::ICoreInputViewOcclusion* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion, 0x5bb57354, 0x4f40, 0x5ef3, 0xa5,0xd1, 0x6a,0x60,0x49,0xf9,0x05,0xa1)
#endif
#else
typedef struct __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        UINT32 items_size,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl;

interface __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion {
    CONST_VTBL __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > methods ***/
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_QueryInterface(__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_AddRef(__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_Release(__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetIids(__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetRuntimeClassName(__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetTrustLevel(__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_get_Current(__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_get_HasCurrent(__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_MoveNext(__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetMany(__FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,UINT32 items_size,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_CoreInputViewOcclusion IID___FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion
#define IIterator_CoreInputViewOcclusionVtbl __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl
#define IIterator_CoreInputViewOcclusion __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion
#define IIterator_CoreInputViewOcclusion_QueryInterface __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_QueryInterface
#define IIterator_CoreInputViewOcclusion_AddRef __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_AddRef
#define IIterator_CoreInputViewOcclusion_Release __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_Release
#define IIterator_CoreInputViewOcclusion_GetIids __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetIids
#define IIterator_CoreInputViewOcclusion_GetRuntimeClassName __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetRuntimeClassName
#define IIterator_CoreInputViewOcclusion_GetTrustLevel __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetTrustLevel
#define IIterator_CoreInputViewOcclusion_get_Current __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_get_Current
#define IIterator_CoreInputViewOcclusion_get_HasCurrent __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_get_HasCurrent
#define IIterator_CoreInputViewOcclusion_MoveNext __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_MoveNext
#define IIterator_CoreInputViewOcclusion_GetMany __FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > interface
 */
#ifndef ____FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion, 0xe0963578, 0xa246, 0x5680, 0x86,0xd1, 0x27,0x51,0x94,0x23,0xe2,0x12);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("e0963578-a246-5680-86d1-27519423e212")
                IVectorView<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion*, ABI::Windows::UI::ViewManagement::Core::ICoreInputViewOcclusion* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion, 0xe0963578, 0xa246, 0x5680, 0x86,0xd1, 0x27,0x51,0x94,0x23,0xe2,0x12)
#endif
#else
typedef struct __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        UINT32 index,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl;

interface __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion {
    CONST_VTBL __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > methods ***/
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_QueryInterface(__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_AddRef(__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_Release(__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetIids(__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetRuntimeClassName(__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetTrustLevel(__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusion* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetAt(__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,UINT32 index,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_get_Size(__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_IndexOf(__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetMany(__FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusion **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_CoreInputViewOcclusion IID___FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion
#define IVectorView_CoreInputViewOcclusionVtbl __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionVtbl
#define IVectorView_CoreInputViewOcclusion __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion
#define IVectorView_CoreInputViewOcclusion_QueryInterface __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_QueryInterface
#define IVectorView_CoreInputViewOcclusion_AddRef __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_AddRef
#define IVectorView_CoreInputViewOcclusion_Release __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_Release
#define IVectorView_CoreInputViewOcclusion_GetIids __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetIids
#define IVectorView_CoreInputViewOcclusion_GetRuntimeClassName __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetRuntimeClassName
#define IVectorView_CoreInputViewOcclusion_GetTrustLevel __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetTrustLevel
#define IVectorView_CoreInputViewOcclusion_GetAt __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetAt
#define IVectorView_CoreInputViewOcclusion_get_Size __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_get_Size
#define IVectorView_CoreInputViewOcclusion_IndexOf __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_IndexOf
#define IVectorView_CoreInputViewOcclusion_GetMany __FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusion_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable, 0x2bc0e7f6, 0xc772, 0x56e0, 0x94,0x39, 0x65,0x06,0x66,0xc7,0x8d,0x0c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("2bc0e7f6-c772-56e0-9439-650666c78d0c")
            ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputView*, ABI::Windows::UI::ViewManagement::Core::ICoreInputView* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable, 0x2bc0e7f6, 0xc772, 0x56e0, 0x94,0x39, 0x65,0x06,0x66,0xc7,0x8d,0x0c)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable *This,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_Release(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable* This,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreInputView_IInspectable IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable
#define ITypedEventHandler_CoreInputView_IInspectableVtbl __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectableVtbl
#define ITypedEventHandler_CoreInputView_IInspectable __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable
#define ITypedEventHandler_CoreInputView_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_QueryInterface
#define ITypedEventHandler_CoreInputView_IInspectable_AddRef __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_AddRef
#define ITypedEventHandler_CoreInputView_IInspectable_Release __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_Release
#define ITypedEventHandler_CoreInputView_IInspectable_Invoke __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewHidingEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs, 0x3188afc6, 0x4b93, 0x58a0, 0xb5,0xb0, 0xcb,0xed,0x65,0xbe,0x0c,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("3188afc6-4b93-58a0-b5b0-cbed65be0c7e")
            ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewHidingEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputView*, ABI::Windows::UI::ViewManagement::Core::ICoreInputView* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputViewHidingEventArgs*, ABI::Windows::UI::ViewManagement::Core::ICoreInputViewHidingEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs, 0x3188afc6, 0x4b93, 0x58a0, 0xb5,0xb0, 0xcb,0xed,0x65,0xbe,0x0c,0x7e)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewHidingEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs *This,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *sender,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewHidingEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewHidingEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs* This,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *sender,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewHidingEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreInputView_CoreInputViewHidingEventArgs IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs
#define ITypedEventHandler_CoreInputView_CoreInputViewHidingEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgsVtbl
#define ITypedEventHandler_CoreInputView_CoreInputViewHidingEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs
#define ITypedEventHandler_CoreInputView_CoreInputViewHidingEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_QueryInterface
#define ITypedEventHandler_CoreInputView_CoreInputViewHidingEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_AddRef
#define ITypedEventHandler_CoreInputView_CoreInputViewHidingEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_Release
#define ITypedEventHandler_CoreInputView_CoreInputViewHidingEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewHidingEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusionsChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs, 0x5adecf04, 0xedd1, 0x5133, 0xab,0xc7, 0x58,0x2a,0x02,0x7f,0x09,0xbb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("5adecf04-edd1-5133-abc7-582a027f09bb")
            ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusionsChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputView*, ABI::Windows::UI::ViewManagement::Core::ICoreInputView* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusionsChangedEventArgs*, ABI::Windows::UI::ViewManagement::Core::ICoreInputViewOcclusionsChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs, 0x5adecf04, 0xedd1, 0x5133, 0xab,0xc7, 0x58,0x2a,0x02,0x7f,0x09,0xbb)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusionsChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs *This,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *sender,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusionsChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewOcclusionsChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs* This,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *sender,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewOcclusionsChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreInputView_CoreInputViewOcclusionsChangedEventArgs IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs
#define ITypedEventHandler_CoreInputView_CoreInputViewOcclusionsChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgsVtbl
#define ITypedEventHandler_CoreInputView_CoreInputViewOcclusionsChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs
#define ITypedEventHandler_CoreInputView_CoreInputViewOcclusionsChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_QueryInterface
#define ITypedEventHandler_CoreInputView_CoreInputViewOcclusionsChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_AddRef
#define ITypedEventHandler_CoreInputView_CoreInputViewOcclusionsChangedEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_Release
#define ITypedEventHandler_CoreInputView_CoreInputViewOcclusionsChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewOcclusionsChangedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewShowingEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs, 0xa300e2f9, 0x737b, 0x5330, 0x8e,0xa2, 0x68,0xe7,0xa3,0xaa,0xed,0xb2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("a300e2f9-737b-5330-8ea2-68e7a3aaedb2")
            ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewShowingEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputView*, ABI::Windows::UI::ViewManagement::Core::ICoreInputView* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputViewShowingEventArgs*, ABI::Windows::UI::ViewManagement::Core::ICoreInputViewShowingEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs, 0xa300e2f9, 0x737b, 0x5330, 0x8e,0xa2, 0x68,0xe7,0xa3,0xaa,0xed,0xb2)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewShowingEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs *This,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *sender,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewShowingEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewShowingEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs* This,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *sender,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewShowingEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreInputView_CoreInputViewShowingEventArgs IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs
#define ITypedEventHandler_CoreInputView_CoreInputViewShowingEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgsVtbl
#define ITypedEventHandler_CoreInputView_CoreInputViewShowingEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs
#define ITypedEventHandler_CoreInputView_CoreInputViewShowingEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_QueryInterface
#define ITypedEventHandler_CoreInputView_CoreInputViewShowingEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_AddRef
#define ITypedEventHandler_CoreInputView_CoreInputViewShowingEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_Release
#define ITypedEventHandler_CoreInputView_CoreInputViewShowingEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewShowingEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewTransferringXYFocusEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs, 0x881e8198, 0x7ff6, 0x5cd9, 0x8a,0x64, 0x6d,0xd4,0x29,0x22,0x67,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("881e8198-7ff6-5cd9-8a64-6dd4292267ad")
            ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewTransferringXYFocusEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputView*, ABI::Windows::UI::ViewManagement::Core::ICoreInputView* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::Core::CoreInputViewTransferringXYFocusEventArgs*, ABI::Windows::UI::ViewManagement::Core::ICoreInputViewTransferringXYFocusEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs, 0x881e8198, 0x7ff6, 0x5cd9, 0x8a,0x64, 0x6d,0xd4,0x29,0x22,0x67,0xad)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewTransferringXYFocusEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs *This,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *sender,
        __x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewTransferringXYFocusEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::Core::CoreInputView*,ABI::Windows::UI::ViewManagement::Core::CoreInputViewTransferringXYFocusEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs* This,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputView *sender,__x_ABI_CWindows_CUI_CViewManagement_CCore_CICoreInputViewTransferringXYFocusEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreInputView_CoreInputViewTransferringXYFocusEventArgs IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs
#define ITypedEventHandler_CoreInputView_CoreInputViewTransferringXYFocusEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgsVtbl
#define ITypedEventHandler_CoreInputView_CoreInputViewTransferringXYFocusEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs
#define ITypedEventHandler_CoreInputView_CoreInputViewTransferringXYFocusEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_QueryInterface
#define ITypedEventHandler_CoreInputView_CoreInputViewTransferringXYFocusEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_AddRef
#define ITypedEventHandler_CoreInputView_CoreInputViewTransferringXYFocusEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_Release
#define ITypedEventHandler_CoreInputView_CoreInputViewTransferringXYFocusEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CCore__CCoreInputView_Windows__CUI__CViewManagement__CCore__CCoreInputViewTransferringXYFocusEventArgs_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_ui_viewmanagement_core_h__ */
