/*** Autogenerated by WIDL 10.12 from include/msxml.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __msxml_h__
#define __msxml_h__

/* Forward declarations */

#ifndef __IXMLDOMNode_FWD_DEFINED__
#define __IXMLDOMNode_FWD_DEFINED__
typedef interface IXMLDOMNode IXMLDOMNode;
#ifdef __cplusplus
interface IXMLDOMNode;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMDocument_FWD_DEFINED__
#define __IXMLDOMDocument_FWD_DEFINED__
typedef interface IXMLDOMDocument IXMLDOMDocument;
#ifdef __cplusplus
interface IXMLDOMDocument;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMNodeList_FWD_DEFINED__
#define __IXMLDOMNodeList_FWD_DEFINED__
typedef interface IXMLDOMNodeList IXMLDOMNodeList;
#ifdef __cplusplus
interface IXMLDOMNodeList;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMNamedNodeMap_FWD_DEFINED__
#define __IXMLDOMNamedNodeMap_FWD_DEFINED__
typedef interface IXMLDOMNamedNodeMap IXMLDOMNamedNodeMap;
#ifdef __cplusplus
interface IXMLDOMNamedNodeMap;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMDocumentFragment_FWD_DEFINED__
#define __IXMLDOMDocumentFragment_FWD_DEFINED__
typedef interface IXMLDOMDocumentFragment IXMLDOMDocumentFragment;
#ifdef __cplusplus
interface IXMLDOMDocumentFragment;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMCharacterData_FWD_DEFINED__
#define __IXMLDOMCharacterData_FWD_DEFINED__
typedef interface IXMLDOMCharacterData IXMLDOMCharacterData;
#ifdef __cplusplus
interface IXMLDOMCharacterData;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMAttribute_FWD_DEFINED__
#define __IXMLDOMAttribute_FWD_DEFINED__
typedef interface IXMLDOMAttribute IXMLDOMAttribute;
#ifdef __cplusplus
interface IXMLDOMAttribute;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMElement_FWD_DEFINED__
#define __IXMLDOMElement_FWD_DEFINED__
typedef interface IXMLDOMElement IXMLDOMElement;
#ifdef __cplusplus
interface IXMLDOMElement;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMText_FWD_DEFINED__
#define __IXMLDOMText_FWD_DEFINED__
typedef interface IXMLDOMText IXMLDOMText;
#ifdef __cplusplus
interface IXMLDOMText;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMComment_FWD_DEFINED__
#define __IXMLDOMComment_FWD_DEFINED__
typedef interface IXMLDOMComment IXMLDOMComment;
#ifdef __cplusplus
interface IXMLDOMComment;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMProcessingInstruction_FWD_DEFINED__
#define __IXMLDOMProcessingInstruction_FWD_DEFINED__
typedef interface IXMLDOMProcessingInstruction IXMLDOMProcessingInstruction;
#ifdef __cplusplus
interface IXMLDOMProcessingInstruction;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMCDATASection_FWD_DEFINED__
#define __IXMLDOMCDATASection_FWD_DEFINED__
typedef interface IXMLDOMCDATASection IXMLDOMCDATASection;
#ifdef __cplusplus
interface IXMLDOMCDATASection;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMDocumentType_FWD_DEFINED__
#define __IXMLDOMDocumentType_FWD_DEFINED__
typedef interface IXMLDOMDocumentType IXMLDOMDocumentType;
#ifdef __cplusplus
interface IXMLDOMDocumentType;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMNotation_FWD_DEFINED__
#define __IXMLDOMNotation_FWD_DEFINED__
typedef interface IXMLDOMNotation IXMLDOMNotation;
#ifdef __cplusplus
interface IXMLDOMNotation;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMEntity_FWD_DEFINED__
#define __IXMLDOMEntity_FWD_DEFINED__
typedef interface IXMLDOMEntity IXMLDOMEntity;
#ifdef __cplusplus
interface IXMLDOMEntity;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMEntityReference_FWD_DEFINED__
#define __IXMLDOMEntityReference_FWD_DEFINED__
typedef interface IXMLDOMEntityReference IXMLDOMEntityReference;
#ifdef __cplusplus
interface IXMLDOMEntityReference;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMImplementation_FWD_DEFINED__
#define __IXMLDOMImplementation_FWD_DEFINED__
typedef interface IXMLDOMImplementation IXMLDOMImplementation;
#ifdef __cplusplus
interface IXMLDOMImplementation;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMParseError_FWD_DEFINED__
#define __IXMLDOMParseError_FWD_DEFINED__
typedef interface IXMLDOMParseError IXMLDOMParseError;
#ifdef __cplusplus
interface IXMLDOMParseError;
#endif /* __cplusplus */
#endif

#ifndef __XMLDOMDocumentEvents_FWD_DEFINED__
#define __XMLDOMDocumentEvents_FWD_DEFINED__
typedef interface XMLDOMDocumentEvents XMLDOMDocumentEvents;
#ifdef __cplusplus
interface XMLDOMDocumentEvents;
#endif /* __cplusplus */
#endif

#ifndef __DOMDocument_FWD_DEFINED__
#define __DOMDocument_FWD_DEFINED__
#ifdef __cplusplus
typedef class DOMDocument DOMDocument;
#else
typedef struct DOMDocument DOMDocument;
#endif /* defined __cplusplus */
#endif /* defined __DOMDocument_FWD_DEFINED__ */

#ifndef __DOMFreeThreadedDocument_FWD_DEFINED__
#define __DOMFreeThreadedDocument_FWD_DEFINED__
#ifdef __cplusplus
typedef class DOMFreeThreadedDocument DOMFreeThreadedDocument;
#else
typedef struct DOMFreeThreadedDocument DOMFreeThreadedDocument;
#endif /* defined __cplusplus */
#endif /* defined __DOMFreeThreadedDocument_FWD_DEFINED__ */

#ifndef __IXMLHttpRequest_FWD_DEFINED__
#define __IXMLHttpRequest_FWD_DEFINED__
typedef interface IXMLHttpRequest IXMLHttpRequest;
#ifdef __cplusplus
interface IXMLHttpRequest;
#endif /* __cplusplus */
#endif

#ifndef __XMLHTTPRequest_FWD_DEFINED__
#define __XMLHTTPRequest_FWD_DEFINED__
#ifdef __cplusplus
typedef class XMLHTTPRequest XMLHTTPRequest;
#else
typedef struct XMLHTTPRequest XMLHTTPRequest;
#endif /* defined __cplusplus */
#endif /* defined __XMLHTTPRequest_FWD_DEFINED__ */

#ifndef __IXMLDSOControl_FWD_DEFINED__
#define __IXMLDSOControl_FWD_DEFINED__
typedef interface IXMLDSOControl IXMLDSOControl;
#ifdef __cplusplus
interface IXMLDSOControl;
#endif /* __cplusplus */
#endif

#ifndef __XMLDSOControl_FWD_DEFINED__
#define __XMLDSOControl_FWD_DEFINED__
#ifdef __cplusplus
typedef class XMLDSOControl XMLDSOControl;
#else
typedef struct XMLDSOControl XMLDSOControl;
#endif /* defined __cplusplus */
#endif /* defined __XMLDSOControl_FWD_DEFINED__ */

#ifndef __IXMLElementCollection_FWD_DEFINED__
#define __IXMLElementCollection_FWD_DEFINED__
typedef interface IXMLElementCollection IXMLElementCollection;
#ifdef __cplusplus
interface IXMLElementCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXMLElement_FWD_DEFINED__
#define __IXMLElement_FWD_DEFINED__
typedef interface IXMLElement IXMLElement;
#ifdef __cplusplus
interface IXMLElement;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDocument_FWD_DEFINED__
#define __IXMLDocument_FWD_DEFINED__
typedef interface IXMLDocument IXMLDocument;
#ifdef __cplusplus
interface IXMLDocument;
#endif /* __cplusplus */
#endif

#ifndef __IXMLElement2_FWD_DEFINED__
#define __IXMLElement2_FWD_DEFINED__
typedef interface IXMLElement2 IXMLElement2;
#ifdef __cplusplus
interface IXMLElement2;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDocument2_FWD_DEFINED__
#define __IXMLDocument2_FWD_DEFINED__
typedef interface IXMLDocument2 IXMLDocument2;
#ifdef __cplusplus
interface IXMLDocument2;
#endif /* __cplusplus */
#endif

#ifndef __IXMLAttribute_FWD_DEFINED__
#define __IXMLAttribute_FWD_DEFINED__
typedef interface IXMLAttribute IXMLAttribute;
#ifdef __cplusplus
interface IXMLAttribute;
#endif /* __cplusplus */
#endif

#ifndef __IXMLError_FWD_DEFINED__
#define __IXMLError_FWD_DEFINED__
typedef interface IXMLError IXMLError;
#ifdef __cplusplus
interface IXMLError;
#endif /* __cplusplus */
#endif

#ifndef __IXMLElementNotificationSink_FWD_DEFINED__
#define __IXMLElementNotificationSink_FWD_DEFINED__
typedef interface IXMLElementNotificationSink IXMLElementNotificationSink;
#ifdef __cplusplus
interface IXMLElementNotificationSink;
#endif /* __cplusplus */
#endif

#ifndef __XMLDocument_FWD_DEFINED__
#define __XMLDocument_FWD_DEFINED__
#ifdef __cplusplus
typedef class XMLDocument XMLDocument;
#else
typedef struct XMLDocument XMLDocument;
#endif /* defined __cplusplus */
#endif /* defined __XMLDocument_FWD_DEFINED__ */

/* Headers for imported files */

#include <unknwn.h>
#include <wtypes.h>
#include <objidl.h>
#include <oaidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __MSXML_LIBRARY_DEFINED__
#define __MSXML_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_MSXML, 0xd63e0ce2, 0xa0a2, 0x11d0, 0x9c,0x02, 0x00,0xc0,0x4f,0xc9,0x9c,0x8e);

#ifndef __IXMLDOMImplementation_FWD_DEFINED__
#define __IXMLDOMImplementation_FWD_DEFINED__
typedef interface IXMLDOMImplementation IXMLDOMImplementation;
#ifdef __cplusplus
interface IXMLDOMImplementation;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMNode_FWD_DEFINED__
#define __IXMLDOMNode_FWD_DEFINED__
typedef interface IXMLDOMNode IXMLDOMNode;
#ifdef __cplusplus
interface IXMLDOMNode;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMDocumentFragment_FWD_DEFINED__
#define __IXMLDOMDocumentFragment_FWD_DEFINED__
typedef interface IXMLDOMDocumentFragment IXMLDOMDocumentFragment;
#ifdef __cplusplus
interface IXMLDOMDocumentFragment;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMDocument_FWD_DEFINED__
#define __IXMLDOMDocument_FWD_DEFINED__
typedef interface IXMLDOMDocument IXMLDOMDocument;
#ifdef __cplusplus
interface IXMLDOMDocument;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMNodeList_FWD_DEFINED__
#define __IXMLDOMNodeList_FWD_DEFINED__
typedef interface IXMLDOMNodeList IXMLDOMNodeList;
#ifdef __cplusplus
interface IXMLDOMNodeList;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMNamedNodeMap_FWD_DEFINED__
#define __IXMLDOMNamedNodeMap_FWD_DEFINED__
typedef interface IXMLDOMNamedNodeMap IXMLDOMNamedNodeMap;
#ifdef __cplusplus
interface IXMLDOMNamedNodeMap;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMCharacterData_FWD_DEFINED__
#define __IXMLDOMCharacterData_FWD_DEFINED__
typedef interface IXMLDOMCharacterData IXMLDOMCharacterData;
#ifdef __cplusplus
interface IXMLDOMCharacterData;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMAttribute_FWD_DEFINED__
#define __IXMLDOMAttribute_FWD_DEFINED__
typedef interface IXMLDOMAttribute IXMLDOMAttribute;
#ifdef __cplusplus
interface IXMLDOMAttribute;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMElement_FWD_DEFINED__
#define __IXMLDOMElement_FWD_DEFINED__
typedef interface IXMLDOMElement IXMLDOMElement;
#ifdef __cplusplus
interface IXMLDOMElement;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMText_FWD_DEFINED__
#define __IXMLDOMText_FWD_DEFINED__
typedef interface IXMLDOMText IXMLDOMText;
#ifdef __cplusplus
interface IXMLDOMText;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMComment_FWD_DEFINED__
#define __IXMLDOMComment_FWD_DEFINED__
typedef interface IXMLDOMComment IXMLDOMComment;
#ifdef __cplusplus
interface IXMLDOMComment;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMProcessingInstruction_FWD_DEFINED__
#define __IXMLDOMProcessingInstruction_FWD_DEFINED__
typedef interface IXMLDOMProcessingInstruction IXMLDOMProcessingInstruction;
#ifdef __cplusplus
interface IXMLDOMProcessingInstruction;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMCDATASection_FWD_DEFINED__
#define __IXMLDOMCDATASection_FWD_DEFINED__
typedef interface IXMLDOMCDATASection IXMLDOMCDATASection;
#ifdef __cplusplus
interface IXMLDOMCDATASection;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMDocumentType_FWD_DEFINED__
#define __IXMLDOMDocumentType_FWD_DEFINED__
typedef interface IXMLDOMDocumentType IXMLDOMDocumentType;
#ifdef __cplusplus
interface IXMLDOMDocumentType;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMNotation_FWD_DEFINED__
#define __IXMLDOMNotation_FWD_DEFINED__
typedef interface IXMLDOMNotation IXMLDOMNotation;
#ifdef __cplusplus
interface IXMLDOMNotation;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMEntity_FWD_DEFINED__
#define __IXMLDOMEntity_FWD_DEFINED__
typedef interface IXMLDOMEntity IXMLDOMEntity;
#ifdef __cplusplus
interface IXMLDOMEntity;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMEntityReference_FWD_DEFINED__
#define __IXMLDOMEntityReference_FWD_DEFINED__
typedef interface IXMLDOMEntityReference IXMLDOMEntityReference;
#ifdef __cplusplus
interface IXMLDOMEntityReference;
#endif /* __cplusplus */
#endif

#ifndef __IXMLDOMParseError_FWD_DEFINED__
#define __IXMLDOMParseError_FWD_DEFINED__
typedef interface IXMLDOMParseError IXMLDOMParseError;
#ifdef __cplusplus
interface IXMLDOMParseError;
#endif /* __cplusplus */
#endif

#ifndef __MSXML_DOMNODETYPE_DEFINED
#define __MSXML_DOMNODETYPE_DEFINED
typedef enum tagDOMNodeType {
    NODE_INVALID = 0,
    NODE_ELEMENT = 1,
    NODE_ATTRIBUTE = 2,
    NODE_TEXT = 3,
    NODE_CDATA_SECTION = 4,
    NODE_ENTITY_REFERENCE = 5,
    NODE_ENTITY = 6,
    NODE_PROCESSING_INSTRUCTION = 7,
    NODE_COMMENT = 8,
    NODE_DOCUMENT = 9,
    NODE_DOCUMENT_TYPE = 10,
    NODE_DOCUMENT_FRAGMENT = 11,
    NODE_NOTATION = 12
} DOMNodeType;
#endif
/*****************************************************************************
 * IXMLDOMNode interface
 */
#ifndef __IXMLDOMNode_INTERFACE_DEFINED__
#define __IXMLDOMNode_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMNode, 0x2933bf80, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf80-7b36-11d2-b20e-00c04f983e60")
IXMLDOMNode : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_nodeName(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_nodeValue(
        VARIANT *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_nodeValue(
        VARIANT value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_nodeType(
        DOMNodeType *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_parentNode(
        IXMLDOMNode **parent) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_childNodes(
        IXMLDOMNodeList **childList) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_firstChild(
        IXMLDOMNode **firstChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_lastChild(
        IXMLDOMNode **lastChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_previousSibling(
        IXMLDOMNode **previousSibling) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_nextSibling(
        IXMLDOMNode **nextSibling) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_attributes(
        IXMLDOMNamedNodeMap **attributeMap) = 0;

    virtual HRESULT STDMETHODCALLTYPE insertBefore(
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE replaceChild(
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE removeChild(
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE appendChild(
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE hasChildNodes(
        VARIANT_BOOL *hasChild) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ownerDocument(
        IXMLDOMDocument **DOMDocument) = 0;

    virtual HRESULT STDMETHODCALLTYPE cloneNode(
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_nodeTypeString(
        BSTR *nodeType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_text(
        BSTR *text) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_text(
        BSTR text) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_specified(
        VARIANT_BOOL *isSpecified) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_definition(
        IXMLDOMNode **definitionNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_nodeTypedValue(
        VARIANT *typedValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_nodeTypedValue(
        VARIANT typedValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_dataType(
        VARIANT *dataTypeName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_dataType(
        BSTR dataTypeName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_xml(
        BSTR *xmlString) = 0;

    virtual HRESULT STDMETHODCALLTYPE transformNode(
        IXMLDOMNode *styleSheet,
        BSTR *xmlString) = 0;

    virtual HRESULT STDMETHODCALLTYPE selectNodes(
        BSTR queryString,
        IXMLDOMNodeList **resultList) = 0;

    virtual HRESULT STDMETHODCALLTYPE selectSingleNode(
        BSTR queryString,
        IXMLDOMNode **resultNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_parsed(
        VARIANT_BOOL *isParsed) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_namespaceURI(
        BSTR *namespaceURI) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_prefix(
        BSTR *prefixString) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_baseName(
        BSTR *nameString) = 0;

    virtual HRESULT STDMETHODCALLTYPE transformNodeToObject(
        IXMLDOMNode *stylesheet,
        VARIANT outputObject) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMNode, 0x2933bf80, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMNodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMNode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMNode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMNode *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMNode *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMNode *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMNode *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMNode *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMNode *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMNode *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMNode *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMNode *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMNode *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMNode *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMNode *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMNode *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMNode *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMNode *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMNode *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMNode *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMNode *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMNode *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMNode *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMNode *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMNode *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMNode *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMNode *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMNode *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMNode *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMNode *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMNode *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMNode *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMNode *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMNode *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMNode *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMNode *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMNode *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMNode *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMNode *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMNode *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMNode *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMNode *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMNode *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMNode *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    END_INTERFACE
} IXMLDOMNodeVtbl;

interface IXMLDOMNode {
    CONST_VTBL IXMLDOMNodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMNode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMNode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMNode_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMNode_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMNode_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMNode_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMNode_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMNode_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMNode_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMNode_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMNode_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMNode_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMNode_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMNode_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMNode_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMNode_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMNode_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMNode_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMNode_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMNode_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMNode_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMNode_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMNode_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMNode_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMNode_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMNode_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMNode_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMNode_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMNode_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMNode_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMNode_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMNode_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMNode_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMNode_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMNode_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMNode_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMNode_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMNode_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMNode_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMNode_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMNode_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMNode_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMNode_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMNode_QueryInterface(IXMLDOMNode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMNode_AddRef(IXMLDOMNode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMNode_Release(IXMLDOMNode* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMNode_GetTypeInfoCount(IXMLDOMNode* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMNode_GetTypeInfo(IXMLDOMNode* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMNode_GetIDsOfNames(IXMLDOMNode* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMNode_Invoke(IXMLDOMNode* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMNode_get_nodeName(IXMLDOMNode* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMNode_get_nodeValue(IXMLDOMNode* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMNode_put_nodeValue(IXMLDOMNode* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMNode_get_nodeType(IXMLDOMNode* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMNode_get_parentNode(IXMLDOMNode* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMNode_get_childNodes(IXMLDOMNode* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMNode_get_firstChild(IXMLDOMNode* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMNode_get_lastChild(IXMLDOMNode* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMNode_get_previousSibling(IXMLDOMNode* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMNode_get_nextSibling(IXMLDOMNode* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMNode_get_attributes(IXMLDOMNode* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMNode_insertBefore(IXMLDOMNode* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMNode_replaceChild(IXMLDOMNode* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMNode_removeChild(IXMLDOMNode* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMNode_appendChild(IXMLDOMNode* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMNode_hasChildNodes(IXMLDOMNode* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMNode_get_ownerDocument(IXMLDOMNode* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMNode_cloneNode(IXMLDOMNode* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMNode_get_nodeTypeString(IXMLDOMNode* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMNode_get_text(IXMLDOMNode* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMNode_put_text(IXMLDOMNode* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMNode_get_specified(IXMLDOMNode* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMNode_get_definition(IXMLDOMNode* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMNode_get_nodeTypedValue(IXMLDOMNode* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMNode_put_nodeTypedValue(IXMLDOMNode* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMNode_get_dataType(IXMLDOMNode* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMNode_put_dataType(IXMLDOMNode* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMNode_get_xml(IXMLDOMNode* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMNode_transformNode(IXMLDOMNode* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMNode_selectNodes(IXMLDOMNode* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMNode_selectSingleNode(IXMLDOMNode* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMNode_get_parsed(IXMLDOMNode* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMNode_get_namespaceURI(IXMLDOMNode* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMNode_get_prefix(IXMLDOMNode* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMNode_get_baseName(IXMLDOMNode* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMNode_transformNodeToObject(IXMLDOMNode* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMNode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMDocument interface
 */
#ifndef __IXMLDOMDocument_INTERFACE_DEFINED__
#define __IXMLDOMDocument_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMDocument, 0x2933bf81, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf81-7b36-11d2-b20e-00c04f983e60")
IXMLDOMDocument : public IXMLDOMNode
{
    virtual HRESULT STDMETHODCALLTYPE get_doctype(
        IXMLDOMDocumentType **documentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_implementation(
        IXMLDOMImplementation **impl) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_documentElement(
        IXMLDOMElement **DOMElement) = 0;

    virtual HRESULT STDMETHODCALLTYPE putref_documentElement(
        IXMLDOMElement *DOMElement) = 0;

    virtual HRESULT STDMETHODCALLTYPE createElement(
        BSTR tagname,
        IXMLDOMElement **element) = 0;

    virtual HRESULT STDMETHODCALLTYPE createDocumentFragment(
        IXMLDOMDocumentFragment **docFrag) = 0;

    virtual HRESULT STDMETHODCALLTYPE createTextNode(
        BSTR data,
        IXMLDOMText **text) = 0;

    virtual HRESULT STDMETHODCALLTYPE createComment(
        BSTR data,
        IXMLDOMComment **comment) = 0;

    virtual HRESULT STDMETHODCALLTYPE createCDATASection(
        BSTR data,
        IXMLDOMCDATASection **cdata) = 0;

    virtual HRESULT STDMETHODCALLTYPE createProcessingInstruction(
        BSTR target,
        BSTR data,
        IXMLDOMProcessingInstruction **pi) = 0;

    virtual HRESULT STDMETHODCALLTYPE createAttribute(
        BSTR name,
        IXMLDOMAttribute **attribute) = 0;

    virtual HRESULT STDMETHODCALLTYPE createEntityReference(
        BSTR name,
        IXMLDOMEntityReference **entityRef) = 0;

    virtual HRESULT STDMETHODCALLTYPE getElementsByTagName(
        BSTR tagName,
        IXMLDOMNodeList **resultList) = 0;

    virtual HRESULT STDMETHODCALLTYPE createNode(
        VARIANT Type,
        BSTR name,
        BSTR namespaceURI,
        IXMLDOMNode **node) = 0;

    virtual HRESULT STDMETHODCALLTYPE nodeFromID(
        BSTR idString,
        IXMLDOMNode **node) = 0;

    virtual HRESULT STDMETHODCALLTYPE load(
        VARIANT xmlSource,
        VARIANT_BOOL *isSuccessful) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_readyState(
        LONG *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_parseError(
        IXMLDOMParseError **errorObj) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_url(
        BSTR *urlString) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_async(
        VARIANT_BOOL *isAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_async(
        VARIANT_BOOL isAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE abort(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE loadXML(
        BSTR bstrXML,
        VARIANT_BOOL *isSuccessful) = 0;

    virtual HRESULT STDMETHODCALLTYPE save(
        VARIANT destination) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_validateOnParse(
        VARIANT_BOOL *isValidating) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_validateOnParse(
        VARIANT_BOOL isValidating) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_resolveExternals(
        VARIANT_BOOL *isResolving) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_resolveExternals(
        VARIANT_BOOL isValidating) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_preserveWhiteSpace(
        VARIANT_BOOL *isPreserving) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_preserveWhiteSpace(
        VARIANT_BOOL isPreserving) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_onreadystatechange(
        VARIANT readystatechangeSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ondataavailable(
        VARIANT ondataavailableSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ontransformnode(
        VARIANT ontransformnodeSink) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMDocument, 0x2933bf81, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMDocumentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMDocument *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMDocument *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMDocument *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMDocument *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMDocument *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMDocument *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMDocument *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMDocument *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMDocument *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMDocument *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMDocument *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMDocument *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMDocument *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMDocument *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMDocument *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMDocument *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMDocument *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMDocument *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMDocument *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMDocument *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMDocument *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMDocument *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMDocument *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMDocument *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMDocument *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMDocument *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMDocument *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMDocument *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMDocument *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMDocument *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMDocument *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMDocument *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMDocument *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMDocument *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMDocument *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMDocument *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMDocument *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMDocument *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMDocument *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMDocument *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMDocument *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMDocument *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMDocument *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    /*** IXMLDOMDocument methods ***/
    HRESULT (STDMETHODCALLTYPE *get_doctype)(
        IXMLDOMDocument *This,
        IXMLDOMDocumentType **documentType);

    HRESULT (STDMETHODCALLTYPE *get_implementation)(
        IXMLDOMDocument *This,
        IXMLDOMImplementation **impl);

    HRESULT (STDMETHODCALLTYPE *get_documentElement)(
        IXMLDOMDocument *This,
        IXMLDOMElement **DOMElement);

    HRESULT (STDMETHODCALLTYPE *putref_documentElement)(
        IXMLDOMDocument *This,
        IXMLDOMElement *DOMElement);

    HRESULT (STDMETHODCALLTYPE *createElement)(
        IXMLDOMDocument *This,
        BSTR tagname,
        IXMLDOMElement **element);

    HRESULT (STDMETHODCALLTYPE *createDocumentFragment)(
        IXMLDOMDocument *This,
        IXMLDOMDocumentFragment **docFrag);

    HRESULT (STDMETHODCALLTYPE *createTextNode)(
        IXMLDOMDocument *This,
        BSTR data,
        IXMLDOMText **text);

    HRESULT (STDMETHODCALLTYPE *createComment)(
        IXMLDOMDocument *This,
        BSTR data,
        IXMLDOMComment **comment);

    HRESULT (STDMETHODCALLTYPE *createCDATASection)(
        IXMLDOMDocument *This,
        BSTR data,
        IXMLDOMCDATASection **cdata);

    HRESULT (STDMETHODCALLTYPE *createProcessingInstruction)(
        IXMLDOMDocument *This,
        BSTR target,
        BSTR data,
        IXMLDOMProcessingInstruction **pi);

    HRESULT (STDMETHODCALLTYPE *createAttribute)(
        IXMLDOMDocument *This,
        BSTR name,
        IXMLDOMAttribute **attribute);

    HRESULT (STDMETHODCALLTYPE *createEntityReference)(
        IXMLDOMDocument *This,
        BSTR name,
        IXMLDOMEntityReference **entityRef);

    HRESULT (STDMETHODCALLTYPE *getElementsByTagName)(
        IXMLDOMDocument *This,
        BSTR tagName,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *createNode)(
        IXMLDOMDocument *This,
        VARIANT Type,
        BSTR name,
        BSTR namespaceURI,
        IXMLDOMNode **node);

    HRESULT (STDMETHODCALLTYPE *nodeFromID)(
        IXMLDOMDocument *This,
        BSTR idString,
        IXMLDOMNode **node);

    HRESULT (STDMETHODCALLTYPE *load)(
        IXMLDOMDocument *This,
        VARIANT xmlSource,
        VARIANT_BOOL *isSuccessful);

    HRESULT (STDMETHODCALLTYPE *get_readyState)(
        IXMLDOMDocument *This,
        LONG *value);

    HRESULT (STDMETHODCALLTYPE *get_parseError)(
        IXMLDOMDocument *This,
        IXMLDOMParseError **errorObj);

    HRESULT (STDMETHODCALLTYPE *get_url)(
        IXMLDOMDocument *This,
        BSTR *urlString);

    HRESULT (STDMETHODCALLTYPE *get_async)(
        IXMLDOMDocument *This,
        VARIANT_BOOL *isAsync);

    HRESULT (STDMETHODCALLTYPE *put_async)(
        IXMLDOMDocument *This,
        VARIANT_BOOL isAsync);

    HRESULT (STDMETHODCALLTYPE *abort)(
        IXMLDOMDocument *This);

    HRESULT (STDMETHODCALLTYPE *loadXML)(
        IXMLDOMDocument *This,
        BSTR bstrXML,
        VARIANT_BOOL *isSuccessful);

    HRESULT (STDMETHODCALLTYPE *save)(
        IXMLDOMDocument *This,
        VARIANT destination);

    HRESULT (STDMETHODCALLTYPE *get_validateOnParse)(
        IXMLDOMDocument *This,
        VARIANT_BOOL *isValidating);

    HRESULT (STDMETHODCALLTYPE *put_validateOnParse)(
        IXMLDOMDocument *This,
        VARIANT_BOOL isValidating);

    HRESULT (STDMETHODCALLTYPE *get_resolveExternals)(
        IXMLDOMDocument *This,
        VARIANT_BOOL *isResolving);

    HRESULT (STDMETHODCALLTYPE *put_resolveExternals)(
        IXMLDOMDocument *This,
        VARIANT_BOOL isValidating);

    HRESULT (STDMETHODCALLTYPE *get_preserveWhiteSpace)(
        IXMLDOMDocument *This,
        VARIANT_BOOL *isPreserving);

    HRESULT (STDMETHODCALLTYPE *put_preserveWhiteSpace)(
        IXMLDOMDocument *This,
        VARIANT_BOOL isPreserving);

    HRESULT (STDMETHODCALLTYPE *put_onreadystatechange)(
        IXMLDOMDocument *This,
        VARIANT readystatechangeSink);

    HRESULT (STDMETHODCALLTYPE *put_ondataavailable)(
        IXMLDOMDocument *This,
        VARIANT ondataavailableSink);

    HRESULT (STDMETHODCALLTYPE *put_ontransformnode)(
        IXMLDOMDocument *This,
        VARIANT ontransformnodeSink);

    END_INTERFACE
} IXMLDOMDocumentVtbl;

interface IXMLDOMDocument {
    CONST_VTBL IXMLDOMDocumentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMDocument_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMDocument_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMDocument_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMDocument_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMDocument_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMDocument_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMDocument_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMDocument_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMDocument_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMDocument_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMDocument_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMDocument_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMDocument_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMDocument_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMDocument_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMDocument_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMDocument_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMDocument_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMDocument_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMDocument_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMDocument_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMDocument_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMDocument_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMDocument_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMDocument_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMDocument_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMDocument_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMDocument_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMDocument_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMDocument_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMDocument_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMDocument_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMDocument_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMDocument_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMDocument_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMDocument_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMDocument_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMDocument_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMDocument_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMDocument_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMDocument_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMDocument_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMDocument_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
/*** IXMLDOMDocument methods ***/
#define IXMLDOMDocument_get_doctype(This,documentType) (This)->lpVtbl->get_doctype(This,documentType)
#define IXMLDOMDocument_get_implementation(This,impl) (This)->lpVtbl->get_implementation(This,impl)
#define IXMLDOMDocument_get_documentElement(This,DOMElement) (This)->lpVtbl->get_documentElement(This,DOMElement)
#define IXMLDOMDocument_putref_documentElement(This,DOMElement) (This)->lpVtbl->putref_documentElement(This,DOMElement)
#define IXMLDOMDocument_createElement(This,tagname,element) (This)->lpVtbl->createElement(This,tagname,element)
#define IXMLDOMDocument_createDocumentFragment(This,docFrag) (This)->lpVtbl->createDocumentFragment(This,docFrag)
#define IXMLDOMDocument_createTextNode(This,data,text) (This)->lpVtbl->createTextNode(This,data,text)
#define IXMLDOMDocument_createComment(This,data,comment) (This)->lpVtbl->createComment(This,data,comment)
#define IXMLDOMDocument_createCDATASection(This,data,cdata) (This)->lpVtbl->createCDATASection(This,data,cdata)
#define IXMLDOMDocument_createProcessingInstruction(This,target,data,pi) (This)->lpVtbl->createProcessingInstruction(This,target,data,pi)
#define IXMLDOMDocument_createAttribute(This,name,attribute) (This)->lpVtbl->createAttribute(This,name,attribute)
#define IXMLDOMDocument_createEntityReference(This,name,entityRef) (This)->lpVtbl->createEntityReference(This,name,entityRef)
#define IXMLDOMDocument_getElementsByTagName(This,tagName,resultList) (This)->lpVtbl->getElementsByTagName(This,tagName,resultList)
#define IXMLDOMDocument_createNode(This,Type,name,namespaceURI,node) (This)->lpVtbl->createNode(This,Type,name,namespaceURI,node)
#define IXMLDOMDocument_nodeFromID(This,idString,node) (This)->lpVtbl->nodeFromID(This,idString,node)
#define IXMLDOMDocument_load(This,xmlSource,isSuccessful) (This)->lpVtbl->load(This,xmlSource,isSuccessful)
#define IXMLDOMDocument_get_readyState(This,value) (This)->lpVtbl->get_readyState(This,value)
#define IXMLDOMDocument_get_parseError(This,errorObj) (This)->lpVtbl->get_parseError(This,errorObj)
#define IXMLDOMDocument_get_url(This,urlString) (This)->lpVtbl->get_url(This,urlString)
#define IXMLDOMDocument_get_async(This,isAsync) (This)->lpVtbl->get_async(This,isAsync)
#define IXMLDOMDocument_put_async(This,isAsync) (This)->lpVtbl->put_async(This,isAsync)
#define IXMLDOMDocument_abort(This) (This)->lpVtbl->abort(This)
#define IXMLDOMDocument_loadXML(This,bstrXML,isSuccessful) (This)->lpVtbl->loadXML(This,bstrXML,isSuccessful)
#define IXMLDOMDocument_save(This,destination) (This)->lpVtbl->save(This,destination)
#define IXMLDOMDocument_get_validateOnParse(This,isValidating) (This)->lpVtbl->get_validateOnParse(This,isValidating)
#define IXMLDOMDocument_put_validateOnParse(This,isValidating) (This)->lpVtbl->put_validateOnParse(This,isValidating)
#define IXMLDOMDocument_get_resolveExternals(This,isResolving) (This)->lpVtbl->get_resolveExternals(This,isResolving)
#define IXMLDOMDocument_put_resolveExternals(This,isValidating) (This)->lpVtbl->put_resolveExternals(This,isValidating)
#define IXMLDOMDocument_get_preserveWhiteSpace(This,isPreserving) (This)->lpVtbl->get_preserveWhiteSpace(This,isPreserving)
#define IXMLDOMDocument_put_preserveWhiteSpace(This,isPreserving) (This)->lpVtbl->put_preserveWhiteSpace(This,isPreserving)
#define IXMLDOMDocument_put_onreadystatechange(This,readystatechangeSink) (This)->lpVtbl->put_onreadystatechange(This,readystatechangeSink)
#define IXMLDOMDocument_put_ondataavailable(This,ondataavailableSink) (This)->lpVtbl->put_ondataavailable(This,ondataavailableSink)
#define IXMLDOMDocument_put_ontransformnode(This,ontransformnodeSink) (This)->lpVtbl->put_ontransformnode(This,ontransformnodeSink)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMDocument_QueryInterface(IXMLDOMDocument* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMDocument_AddRef(IXMLDOMDocument* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMDocument_Release(IXMLDOMDocument* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMDocument_GetTypeInfoCount(IXMLDOMDocument* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMDocument_GetTypeInfo(IXMLDOMDocument* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMDocument_GetIDsOfNames(IXMLDOMDocument* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMDocument_Invoke(IXMLDOMDocument* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMDocument_get_nodeName(IXMLDOMDocument* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMDocument_get_nodeValue(IXMLDOMDocument* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMDocument_put_nodeValue(IXMLDOMDocument* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMDocument_get_nodeType(IXMLDOMDocument* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMDocument_get_parentNode(IXMLDOMDocument* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMDocument_get_childNodes(IXMLDOMDocument* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMDocument_get_firstChild(IXMLDOMDocument* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMDocument_get_lastChild(IXMLDOMDocument* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMDocument_get_previousSibling(IXMLDOMDocument* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMDocument_get_nextSibling(IXMLDOMDocument* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMDocument_get_attributes(IXMLDOMDocument* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMDocument_insertBefore(IXMLDOMDocument* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMDocument_replaceChild(IXMLDOMDocument* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMDocument_removeChild(IXMLDOMDocument* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMDocument_appendChild(IXMLDOMDocument* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMDocument_hasChildNodes(IXMLDOMDocument* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMDocument_get_ownerDocument(IXMLDOMDocument* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMDocument_cloneNode(IXMLDOMDocument* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMDocument_get_nodeTypeString(IXMLDOMDocument* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMDocument_get_text(IXMLDOMDocument* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMDocument_put_text(IXMLDOMDocument* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMDocument_get_specified(IXMLDOMDocument* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMDocument_get_definition(IXMLDOMDocument* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMDocument_get_nodeTypedValue(IXMLDOMDocument* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMDocument_put_nodeTypedValue(IXMLDOMDocument* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMDocument_get_dataType(IXMLDOMDocument* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMDocument_put_dataType(IXMLDOMDocument* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMDocument_get_xml(IXMLDOMDocument* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMDocument_transformNode(IXMLDOMDocument* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMDocument_selectNodes(IXMLDOMDocument* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMDocument_selectSingleNode(IXMLDOMDocument* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMDocument_get_parsed(IXMLDOMDocument* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMDocument_get_namespaceURI(IXMLDOMDocument* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMDocument_get_prefix(IXMLDOMDocument* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMDocument_get_baseName(IXMLDOMDocument* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMDocument_transformNodeToObject(IXMLDOMDocument* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
/*** IXMLDOMDocument methods ***/
static inline HRESULT IXMLDOMDocument_get_doctype(IXMLDOMDocument* This,IXMLDOMDocumentType **documentType) {
    return This->lpVtbl->get_doctype(This,documentType);
}
static inline HRESULT IXMLDOMDocument_get_implementation(IXMLDOMDocument* This,IXMLDOMImplementation **impl) {
    return This->lpVtbl->get_implementation(This,impl);
}
static inline HRESULT IXMLDOMDocument_get_documentElement(IXMLDOMDocument* This,IXMLDOMElement **DOMElement) {
    return This->lpVtbl->get_documentElement(This,DOMElement);
}
static inline HRESULT IXMLDOMDocument_putref_documentElement(IXMLDOMDocument* This,IXMLDOMElement *DOMElement) {
    return This->lpVtbl->putref_documentElement(This,DOMElement);
}
static inline HRESULT IXMLDOMDocument_createElement(IXMLDOMDocument* This,BSTR tagname,IXMLDOMElement **element) {
    return This->lpVtbl->createElement(This,tagname,element);
}
static inline HRESULT IXMLDOMDocument_createDocumentFragment(IXMLDOMDocument* This,IXMLDOMDocumentFragment **docFrag) {
    return This->lpVtbl->createDocumentFragment(This,docFrag);
}
static inline HRESULT IXMLDOMDocument_createTextNode(IXMLDOMDocument* This,BSTR data,IXMLDOMText **text) {
    return This->lpVtbl->createTextNode(This,data,text);
}
static inline HRESULT IXMLDOMDocument_createComment(IXMLDOMDocument* This,BSTR data,IXMLDOMComment **comment) {
    return This->lpVtbl->createComment(This,data,comment);
}
static inline HRESULT IXMLDOMDocument_createCDATASection(IXMLDOMDocument* This,BSTR data,IXMLDOMCDATASection **cdata) {
    return This->lpVtbl->createCDATASection(This,data,cdata);
}
static inline HRESULT IXMLDOMDocument_createProcessingInstruction(IXMLDOMDocument* This,BSTR target,BSTR data,IXMLDOMProcessingInstruction **pi) {
    return This->lpVtbl->createProcessingInstruction(This,target,data,pi);
}
static inline HRESULT IXMLDOMDocument_createAttribute(IXMLDOMDocument* This,BSTR name,IXMLDOMAttribute **attribute) {
    return This->lpVtbl->createAttribute(This,name,attribute);
}
static inline HRESULT IXMLDOMDocument_createEntityReference(IXMLDOMDocument* This,BSTR name,IXMLDOMEntityReference **entityRef) {
    return This->lpVtbl->createEntityReference(This,name,entityRef);
}
static inline HRESULT IXMLDOMDocument_getElementsByTagName(IXMLDOMDocument* This,BSTR tagName,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->getElementsByTagName(This,tagName,resultList);
}
static inline HRESULT IXMLDOMDocument_createNode(IXMLDOMDocument* This,VARIANT Type,BSTR name,BSTR namespaceURI,IXMLDOMNode **node) {
    return This->lpVtbl->createNode(This,Type,name,namespaceURI,node);
}
static inline HRESULT IXMLDOMDocument_nodeFromID(IXMLDOMDocument* This,BSTR idString,IXMLDOMNode **node) {
    return This->lpVtbl->nodeFromID(This,idString,node);
}
static inline HRESULT IXMLDOMDocument_load(IXMLDOMDocument* This,VARIANT xmlSource,VARIANT_BOOL *isSuccessful) {
    return This->lpVtbl->load(This,xmlSource,isSuccessful);
}
static inline HRESULT IXMLDOMDocument_get_readyState(IXMLDOMDocument* This,LONG *value) {
    return This->lpVtbl->get_readyState(This,value);
}
static inline HRESULT IXMLDOMDocument_get_parseError(IXMLDOMDocument* This,IXMLDOMParseError **errorObj) {
    return This->lpVtbl->get_parseError(This,errorObj);
}
static inline HRESULT IXMLDOMDocument_get_url(IXMLDOMDocument* This,BSTR *urlString) {
    return This->lpVtbl->get_url(This,urlString);
}
static inline HRESULT IXMLDOMDocument_get_async(IXMLDOMDocument* This,VARIANT_BOOL *isAsync) {
    return This->lpVtbl->get_async(This,isAsync);
}
static inline HRESULT IXMLDOMDocument_put_async(IXMLDOMDocument* This,VARIANT_BOOL isAsync) {
    return This->lpVtbl->put_async(This,isAsync);
}
static inline HRESULT IXMLDOMDocument_abort(IXMLDOMDocument* This) {
    return This->lpVtbl->abort(This);
}
static inline HRESULT IXMLDOMDocument_loadXML(IXMLDOMDocument* This,BSTR bstrXML,VARIANT_BOOL *isSuccessful) {
    return This->lpVtbl->loadXML(This,bstrXML,isSuccessful);
}
static inline HRESULT IXMLDOMDocument_save(IXMLDOMDocument* This,VARIANT destination) {
    return This->lpVtbl->save(This,destination);
}
static inline HRESULT IXMLDOMDocument_get_validateOnParse(IXMLDOMDocument* This,VARIANT_BOOL *isValidating) {
    return This->lpVtbl->get_validateOnParse(This,isValidating);
}
static inline HRESULT IXMLDOMDocument_put_validateOnParse(IXMLDOMDocument* This,VARIANT_BOOL isValidating) {
    return This->lpVtbl->put_validateOnParse(This,isValidating);
}
static inline HRESULT IXMLDOMDocument_get_resolveExternals(IXMLDOMDocument* This,VARIANT_BOOL *isResolving) {
    return This->lpVtbl->get_resolveExternals(This,isResolving);
}
static inline HRESULT IXMLDOMDocument_put_resolveExternals(IXMLDOMDocument* This,VARIANT_BOOL isValidating) {
    return This->lpVtbl->put_resolveExternals(This,isValidating);
}
static inline HRESULT IXMLDOMDocument_get_preserveWhiteSpace(IXMLDOMDocument* This,VARIANT_BOOL *isPreserving) {
    return This->lpVtbl->get_preserveWhiteSpace(This,isPreserving);
}
static inline HRESULT IXMLDOMDocument_put_preserveWhiteSpace(IXMLDOMDocument* This,VARIANT_BOOL isPreserving) {
    return This->lpVtbl->put_preserveWhiteSpace(This,isPreserving);
}
static inline HRESULT IXMLDOMDocument_put_onreadystatechange(IXMLDOMDocument* This,VARIANT readystatechangeSink) {
    return This->lpVtbl->put_onreadystatechange(This,readystatechangeSink);
}
static inline HRESULT IXMLDOMDocument_put_ondataavailable(IXMLDOMDocument* This,VARIANT ondataavailableSink) {
    return This->lpVtbl->put_ondataavailable(This,ondataavailableSink);
}
static inline HRESULT IXMLDOMDocument_put_ontransformnode(IXMLDOMDocument* This,VARIANT ontransformnodeSink) {
    return This->lpVtbl->put_ontransformnode(This,ontransformnodeSink);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMDocument_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMNodeList interface
 */
#ifndef __IXMLDOMNodeList_INTERFACE_DEFINED__
#define __IXMLDOMNodeList_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMNodeList, 0x2933bf82, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf82-7b36-11d2-b20e-00c04f983e60")
IXMLDOMNodeList : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_item(
        LONG index,
        IXMLDOMNode **listItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_length(
        LONG *listLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE nextNode(
        IXMLDOMNode **nextItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE _newEnum(
        IUnknown **ppUnk) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMNodeList, 0x2933bf82, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMNodeListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMNodeList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMNodeList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMNodeList *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMNodeList *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMNodeList *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMNodeList *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMNodeList *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNodeList methods ***/
    HRESULT (STDMETHODCALLTYPE *get_item)(
        IXMLDOMNodeList *This,
        LONG index,
        IXMLDOMNode **listItem);

    HRESULT (STDMETHODCALLTYPE *get_length)(
        IXMLDOMNodeList *This,
        LONG *listLength);

    HRESULT (STDMETHODCALLTYPE *nextNode)(
        IXMLDOMNodeList *This,
        IXMLDOMNode **nextItem);

    HRESULT (STDMETHODCALLTYPE *reset)(
        IXMLDOMNodeList *This);

    HRESULT (STDMETHODCALLTYPE *_newEnum)(
        IXMLDOMNodeList *This,
        IUnknown **ppUnk);

    END_INTERFACE
} IXMLDOMNodeListVtbl;

interface IXMLDOMNodeList {
    CONST_VTBL IXMLDOMNodeListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMNodeList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMNodeList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMNodeList_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMNodeList_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMNodeList_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMNodeList_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMNodeList_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNodeList methods ***/
#define IXMLDOMNodeList_get_item(This,index,listItem) (This)->lpVtbl->get_item(This,index,listItem)
#define IXMLDOMNodeList_get_length(This,listLength) (This)->lpVtbl->get_length(This,listLength)
#define IXMLDOMNodeList_nextNode(This,nextItem) (This)->lpVtbl->nextNode(This,nextItem)
#define IXMLDOMNodeList_reset(This) (This)->lpVtbl->reset(This)
#define IXMLDOMNodeList__newEnum(This,ppUnk) (This)->lpVtbl->_newEnum(This,ppUnk)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMNodeList_QueryInterface(IXMLDOMNodeList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMNodeList_AddRef(IXMLDOMNodeList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMNodeList_Release(IXMLDOMNodeList* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMNodeList_GetTypeInfoCount(IXMLDOMNodeList* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMNodeList_GetTypeInfo(IXMLDOMNodeList* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMNodeList_GetIDsOfNames(IXMLDOMNodeList* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMNodeList_Invoke(IXMLDOMNodeList* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNodeList methods ***/
static inline HRESULT IXMLDOMNodeList_get_item(IXMLDOMNodeList* This,LONG index,IXMLDOMNode **listItem) {
    return This->lpVtbl->get_item(This,index,listItem);
}
static inline HRESULT IXMLDOMNodeList_get_length(IXMLDOMNodeList* This,LONG *listLength) {
    return This->lpVtbl->get_length(This,listLength);
}
static inline HRESULT IXMLDOMNodeList_nextNode(IXMLDOMNodeList* This,IXMLDOMNode **nextItem) {
    return This->lpVtbl->nextNode(This,nextItem);
}
static inline HRESULT IXMLDOMNodeList_reset(IXMLDOMNodeList* This) {
    return This->lpVtbl->reset(This);
}
static inline HRESULT IXMLDOMNodeList__newEnum(IXMLDOMNodeList* This,IUnknown **ppUnk) {
    return This->lpVtbl->_newEnum(This,ppUnk);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMNodeList_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMNamedNodeMap interface
 */
#ifndef __IXMLDOMNamedNodeMap_INTERFACE_DEFINED__
#define __IXMLDOMNamedNodeMap_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMNamedNodeMap, 0x2933bf83, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf83-7b36-11d2-b20e-00c04f983e60")
IXMLDOMNamedNodeMap : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE getNamedItem(
        BSTR name,
        IXMLDOMNode **namedItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE setNamedItem(
        IXMLDOMNode *newItem,
        IXMLDOMNode **namedItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE removeNamedItem(
        BSTR name,
        IXMLDOMNode **namedItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_item(
        LONG index,
        IXMLDOMNode **listItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_length(
        LONG *listLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE getQualifiedItem(
        BSTR baseName,
        BSTR namespaceURI,
        IXMLDOMNode **qualifiedItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE removeQualifiedItem(
        BSTR baseName,
        BSTR namespaceURI,
        IXMLDOMNode **qualifiedItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE nextNode(
        IXMLDOMNode **nextItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE _newEnum(
        IUnknown **ppUnk) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMNamedNodeMap, 0x2933bf83, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMNamedNodeMapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMNamedNodeMap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMNamedNodeMap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMNamedNodeMap *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMNamedNodeMap *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMNamedNodeMap *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMNamedNodeMap *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMNamedNodeMap *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNamedNodeMap methods ***/
    HRESULT (STDMETHODCALLTYPE *getNamedItem)(
        IXMLDOMNamedNodeMap *This,
        BSTR name,
        IXMLDOMNode **namedItem);

    HRESULT (STDMETHODCALLTYPE *setNamedItem)(
        IXMLDOMNamedNodeMap *This,
        IXMLDOMNode *newItem,
        IXMLDOMNode **namedItem);

    HRESULT (STDMETHODCALLTYPE *removeNamedItem)(
        IXMLDOMNamedNodeMap *This,
        BSTR name,
        IXMLDOMNode **namedItem);

    HRESULT (STDMETHODCALLTYPE *get_item)(
        IXMLDOMNamedNodeMap *This,
        LONG index,
        IXMLDOMNode **listItem);

    HRESULT (STDMETHODCALLTYPE *get_length)(
        IXMLDOMNamedNodeMap *This,
        LONG *listLength);

    HRESULT (STDMETHODCALLTYPE *getQualifiedItem)(
        IXMLDOMNamedNodeMap *This,
        BSTR baseName,
        BSTR namespaceURI,
        IXMLDOMNode **qualifiedItem);

    HRESULT (STDMETHODCALLTYPE *removeQualifiedItem)(
        IXMLDOMNamedNodeMap *This,
        BSTR baseName,
        BSTR namespaceURI,
        IXMLDOMNode **qualifiedItem);

    HRESULT (STDMETHODCALLTYPE *nextNode)(
        IXMLDOMNamedNodeMap *This,
        IXMLDOMNode **nextItem);

    HRESULT (STDMETHODCALLTYPE *reset)(
        IXMLDOMNamedNodeMap *This);

    HRESULT (STDMETHODCALLTYPE *_newEnum)(
        IXMLDOMNamedNodeMap *This,
        IUnknown **ppUnk);

    END_INTERFACE
} IXMLDOMNamedNodeMapVtbl;

interface IXMLDOMNamedNodeMap {
    CONST_VTBL IXMLDOMNamedNodeMapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMNamedNodeMap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMNamedNodeMap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMNamedNodeMap_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMNamedNodeMap_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMNamedNodeMap_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMNamedNodeMap_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMNamedNodeMap_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNamedNodeMap methods ***/
#define IXMLDOMNamedNodeMap_getNamedItem(This,name,namedItem) (This)->lpVtbl->getNamedItem(This,name,namedItem)
#define IXMLDOMNamedNodeMap_setNamedItem(This,newItem,namedItem) (This)->lpVtbl->setNamedItem(This,newItem,namedItem)
#define IXMLDOMNamedNodeMap_removeNamedItem(This,name,namedItem) (This)->lpVtbl->removeNamedItem(This,name,namedItem)
#define IXMLDOMNamedNodeMap_get_item(This,index,listItem) (This)->lpVtbl->get_item(This,index,listItem)
#define IXMLDOMNamedNodeMap_get_length(This,listLength) (This)->lpVtbl->get_length(This,listLength)
#define IXMLDOMNamedNodeMap_getQualifiedItem(This,baseName,namespaceURI,qualifiedItem) (This)->lpVtbl->getQualifiedItem(This,baseName,namespaceURI,qualifiedItem)
#define IXMLDOMNamedNodeMap_removeQualifiedItem(This,baseName,namespaceURI,qualifiedItem) (This)->lpVtbl->removeQualifiedItem(This,baseName,namespaceURI,qualifiedItem)
#define IXMLDOMNamedNodeMap_nextNode(This,nextItem) (This)->lpVtbl->nextNode(This,nextItem)
#define IXMLDOMNamedNodeMap_reset(This) (This)->lpVtbl->reset(This)
#define IXMLDOMNamedNodeMap__newEnum(This,ppUnk) (This)->lpVtbl->_newEnum(This,ppUnk)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMNamedNodeMap_QueryInterface(IXMLDOMNamedNodeMap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMNamedNodeMap_AddRef(IXMLDOMNamedNodeMap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMNamedNodeMap_Release(IXMLDOMNamedNodeMap* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMNamedNodeMap_GetTypeInfoCount(IXMLDOMNamedNodeMap* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMNamedNodeMap_GetTypeInfo(IXMLDOMNamedNodeMap* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMNamedNodeMap_GetIDsOfNames(IXMLDOMNamedNodeMap* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMNamedNodeMap_Invoke(IXMLDOMNamedNodeMap* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNamedNodeMap methods ***/
static inline HRESULT IXMLDOMNamedNodeMap_getNamedItem(IXMLDOMNamedNodeMap* This,BSTR name,IXMLDOMNode **namedItem) {
    return This->lpVtbl->getNamedItem(This,name,namedItem);
}
static inline HRESULT IXMLDOMNamedNodeMap_setNamedItem(IXMLDOMNamedNodeMap* This,IXMLDOMNode *newItem,IXMLDOMNode **namedItem) {
    return This->lpVtbl->setNamedItem(This,newItem,namedItem);
}
static inline HRESULT IXMLDOMNamedNodeMap_removeNamedItem(IXMLDOMNamedNodeMap* This,BSTR name,IXMLDOMNode **namedItem) {
    return This->lpVtbl->removeNamedItem(This,name,namedItem);
}
static inline HRESULT IXMLDOMNamedNodeMap_get_item(IXMLDOMNamedNodeMap* This,LONG index,IXMLDOMNode **listItem) {
    return This->lpVtbl->get_item(This,index,listItem);
}
static inline HRESULT IXMLDOMNamedNodeMap_get_length(IXMLDOMNamedNodeMap* This,LONG *listLength) {
    return This->lpVtbl->get_length(This,listLength);
}
static inline HRESULT IXMLDOMNamedNodeMap_getQualifiedItem(IXMLDOMNamedNodeMap* This,BSTR baseName,BSTR namespaceURI,IXMLDOMNode **qualifiedItem) {
    return This->lpVtbl->getQualifiedItem(This,baseName,namespaceURI,qualifiedItem);
}
static inline HRESULT IXMLDOMNamedNodeMap_removeQualifiedItem(IXMLDOMNamedNodeMap* This,BSTR baseName,BSTR namespaceURI,IXMLDOMNode **qualifiedItem) {
    return This->lpVtbl->removeQualifiedItem(This,baseName,namespaceURI,qualifiedItem);
}
static inline HRESULT IXMLDOMNamedNodeMap_nextNode(IXMLDOMNamedNodeMap* This,IXMLDOMNode **nextItem) {
    return This->lpVtbl->nextNode(This,nextItem);
}
static inline HRESULT IXMLDOMNamedNodeMap_reset(IXMLDOMNamedNodeMap* This) {
    return This->lpVtbl->reset(This);
}
static inline HRESULT IXMLDOMNamedNodeMap__newEnum(IXMLDOMNamedNodeMap* This,IUnknown **ppUnk) {
    return This->lpVtbl->_newEnum(This,ppUnk);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMNamedNodeMap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMDocumentFragment interface
 */
#ifndef __IXMLDOMDocumentFragment_INTERFACE_DEFINED__
#define __IXMLDOMDocumentFragment_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMDocumentFragment, 0x3efaa413, 0x272f, 0x11d2, 0x83,0x6f, 0x00,0x00,0xf8,0x7a,0x77,0x82);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3efaa413-272f-11d2-836f-0000f87a7782")
IXMLDOMDocumentFragment : public IXMLDOMNode
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMDocumentFragment, 0x3efaa413, 0x272f, 0x11d2, 0x83,0x6f, 0x00,0x00,0xf8,0x7a,0x77,0x82)
#endif
#else
typedef struct IXMLDOMDocumentFragmentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMDocumentFragment *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMDocumentFragment *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMDocumentFragment *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMDocumentFragment *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMDocumentFragment *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMDocumentFragment *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMDocumentFragment *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMDocumentFragment *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMDocumentFragment *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMDocumentFragment *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMDocumentFragment *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMDocumentFragment *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMDocumentFragment *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMDocumentFragment *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMDocumentFragment *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMDocumentFragment *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMDocumentFragment *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMDocumentFragment *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMDocumentFragment *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMDocumentFragment *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMDocumentFragment *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMDocumentFragment *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMDocumentFragment *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMDocumentFragment *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMDocumentFragment *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMDocumentFragment *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMDocumentFragment *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMDocumentFragment *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMDocumentFragment *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    END_INTERFACE
} IXMLDOMDocumentFragmentVtbl;

interface IXMLDOMDocumentFragment {
    CONST_VTBL IXMLDOMDocumentFragmentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMDocumentFragment_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMDocumentFragment_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMDocumentFragment_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMDocumentFragment_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMDocumentFragment_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMDocumentFragment_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMDocumentFragment_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMDocumentFragment_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMDocumentFragment_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMDocumentFragment_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMDocumentFragment_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMDocumentFragment_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMDocumentFragment_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMDocumentFragment_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMDocumentFragment_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMDocumentFragment_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMDocumentFragment_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMDocumentFragment_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMDocumentFragment_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMDocumentFragment_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMDocumentFragment_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMDocumentFragment_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMDocumentFragment_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMDocumentFragment_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMDocumentFragment_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMDocumentFragment_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMDocumentFragment_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMDocumentFragment_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMDocumentFragment_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMDocumentFragment_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMDocumentFragment_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMDocumentFragment_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMDocumentFragment_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMDocumentFragment_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMDocumentFragment_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMDocumentFragment_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMDocumentFragment_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMDocumentFragment_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMDocumentFragment_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMDocumentFragment_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMDocumentFragment_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMDocumentFragment_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMDocumentFragment_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMDocumentFragment_QueryInterface(IXMLDOMDocumentFragment* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMDocumentFragment_AddRef(IXMLDOMDocumentFragment* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMDocumentFragment_Release(IXMLDOMDocumentFragment* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMDocumentFragment_GetTypeInfoCount(IXMLDOMDocumentFragment* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMDocumentFragment_GetTypeInfo(IXMLDOMDocumentFragment* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMDocumentFragment_GetIDsOfNames(IXMLDOMDocumentFragment* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMDocumentFragment_Invoke(IXMLDOMDocumentFragment* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMDocumentFragment_get_nodeName(IXMLDOMDocumentFragment* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMDocumentFragment_get_nodeValue(IXMLDOMDocumentFragment* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMDocumentFragment_put_nodeValue(IXMLDOMDocumentFragment* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMDocumentFragment_get_nodeType(IXMLDOMDocumentFragment* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMDocumentFragment_get_parentNode(IXMLDOMDocumentFragment* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMDocumentFragment_get_childNodes(IXMLDOMDocumentFragment* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMDocumentFragment_get_firstChild(IXMLDOMDocumentFragment* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMDocumentFragment_get_lastChild(IXMLDOMDocumentFragment* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMDocumentFragment_get_previousSibling(IXMLDOMDocumentFragment* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMDocumentFragment_get_nextSibling(IXMLDOMDocumentFragment* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMDocumentFragment_get_attributes(IXMLDOMDocumentFragment* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMDocumentFragment_insertBefore(IXMLDOMDocumentFragment* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMDocumentFragment_replaceChild(IXMLDOMDocumentFragment* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMDocumentFragment_removeChild(IXMLDOMDocumentFragment* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMDocumentFragment_appendChild(IXMLDOMDocumentFragment* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMDocumentFragment_hasChildNodes(IXMLDOMDocumentFragment* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMDocumentFragment_get_ownerDocument(IXMLDOMDocumentFragment* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMDocumentFragment_cloneNode(IXMLDOMDocumentFragment* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMDocumentFragment_get_nodeTypeString(IXMLDOMDocumentFragment* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMDocumentFragment_get_text(IXMLDOMDocumentFragment* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMDocumentFragment_put_text(IXMLDOMDocumentFragment* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMDocumentFragment_get_specified(IXMLDOMDocumentFragment* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMDocumentFragment_get_definition(IXMLDOMDocumentFragment* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMDocumentFragment_get_nodeTypedValue(IXMLDOMDocumentFragment* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMDocumentFragment_put_nodeTypedValue(IXMLDOMDocumentFragment* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMDocumentFragment_get_dataType(IXMLDOMDocumentFragment* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMDocumentFragment_put_dataType(IXMLDOMDocumentFragment* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMDocumentFragment_get_xml(IXMLDOMDocumentFragment* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMDocumentFragment_transformNode(IXMLDOMDocumentFragment* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMDocumentFragment_selectNodes(IXMLDOMDocumentFragment* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMDocumentFragment_selectSingleNode(IXMLDOMDocumentFragment* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMDocumentFragment_get_parsed(IXMLDOMDocumentFragment* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMDocumentFragment_get_namespaceURI(IXMLDOMDocumentFragment* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMDocumentFragment_get_prefix(IXMLDOMDocumentFragment* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMDocumentFragment_get_baseName(IXMLDOMDocumentFragment* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMDocumentFragment_transformNodeToObject(IXMLDOMDocumentFragment* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMDocumentFragment_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMCharacterData interface
 */
#ifndef __IXMLDOMCharacterData_INTERFACE_DEFINED__
#define __IXMLDOMCharacterData_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMCharacterData, 0x2933bf84, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf84-7b36-11d2-b20e-00c04f983e60")
IXMLDOMCharacterData : public IXMLDOMNode
{
    virtual HRESULT STDMETHODCALLTYPE get_data(
        BSTR *data) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_data(
        BSTR data) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_length(
        LONG *dataLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE substringData(
        LONG offset,
        LONG count,
        BSTR *data) = 0;

    virtual HRESULT STDMETHODCALLTYPE appendData(
        BSTR data) = 0;

    virtual HRESULT STDMETHODCALLTYPE insertData(
        LONG offset,
        BSTR data) = 0;

    virtual HRESULT STDMETHODCALLTYPE deleteData(
        LONG offset,
        LONG count) = 0;

    virtual HRESULT STDMETHODCALLTYPE replaceData(
        LONG offset,
        LONG count,
        BSTR data) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMCharacterData, 0x2933bf84, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMCharacterDataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMCharacterData *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMCharacterData *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMCharacterData *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMCharacterData *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMCharacterData *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMCharacterData *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMCharacterData *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMCharacterData *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMCharacterData *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMCharacterData *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMCharacterData *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMCharacterData *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMCharacterData *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMCharacterData *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMCharacterData *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMCharacterData *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMCharacterData *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMCharacterData *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMCharacterData *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMCharacterData *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMCharacterData *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMCharacterData *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMCharacterData *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMCharacterData *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMCharacterData *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMCharacterData *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMCharacterData *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMCharacterData *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMCharacterData *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMCharacterData *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMCharacterData *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMCharacterData *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    /*** IXMLDOMCharacterData methods ***/
    HRESULT (STDMETHODCALLTYPE *get_data)(
        IXMLDOMCharacterData *This,
        BSTR *data);

    HRESULT (STDMETHODCALLTYPE *put_data)(
        IXMLDOMCharacterData *This,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *get_length)(
        IXMLDOMCharacterData *This,
        LONG *dataLength);

    HRESULT (STDMETHODCALLTYPE *substringData)(
        IXMLDOMCharacterData *This,
        LONG offset,
        LONG count,
        BSTR *data);

    HRESULT (STDMETHODCALLTYPE *appendData)(
        IXMLDOMCharacterData *This,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *insertData)(
        IXMLDOMCharacterData *This,
        LONG offset,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *deleteData)(
        IXMLDOMCharacterData *This,
        LONG offset,
        LONG count);

    HRESULT (STDMETHODCALLTYPE *replaceData)(
        IXMLDOMCharacterData *This,
        LONG offset,
        LONG count,
        BSTR data);

    END_INTERFACE
} IXMLDOMCharacterDataVtbl;

interface IXMLDOMCharacterData {
    CONST_VTBL IXMLDOMCharacterDataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMCharacterData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMCharacterData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMCharacterData_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMCharacterData_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMCharacterData_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMCharacterData_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMCharacterData_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMCharacterData_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMCharacterData_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMCharacterData_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMCharacterData_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMCharacterData_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMCharacterData_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMCharacterData_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMCharacterData_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMCharacterData_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMCharacterData_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMCharacterData_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMCharacterData_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMCharacterData_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMCharacterData_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMCharacterData_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMCharacterData_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMCharacterData_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMCharacterData_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMCharacterData_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMCharacterData_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMCharacterData_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMCharacterData_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMCharacterData_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMCharacterData_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMCharacterData_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMCharacterData_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMCharacterData_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMCharacterData_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMCharacterData_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMCharacterData_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMCharacterData_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMCharacterData_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMCharacterData_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMCharacterData_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMCharacterData_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMCharacterData_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
/*** IXMLDOMCharacterData methods ***/
#define IXMLDOMCharacterData_get_data(This,data) (This)->lpVtbl->get_data(This,data)
#define IXMLDOMCharacterData_put_data(This,data) (This)->lpVtbl->put_data(This,data)
#define IXMLDOMCharacterData_get_length(This,dataLength) (This)->lpVtbl->get_length(This,dataLength)
#define IXMLDOMCharacterData_substringData(This,offset,count,data) (This)->lpVtbl->substringData(This,offset,count,data)
#define IXMLDOMCharacterData_appendData(This,data) (This)->lpVtbl->appendData(This,data)
#define IXMLDOMCharacterData_insertData(This,offset,data) (This)->lpVtbl->insertData(This,offset,data)
#define IXMLDOMCharacterData_deleteData(This,offset,count) (This)->lpVtbl->deleteData(This,offset,count)
#define IXMLDOMCharacterData_replaceData(This,offset,count,data) (This)->lpVtbl->replaceData(This,offset,count,data)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMCharacterData_QueryInterface(IXMLDOMCharacterData* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMCharacterData_AddRef(IXMLDOMCharacterData* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMCharacterData_Release(IXMLDOMCharacterData* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMCharacterData_GetTypeInfoCount(IXMLDOMCharacterData* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMCharacterData_GetTypeInfo(IXMLDOMCharacterData* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMCharacterData_GetIDsOfNames(IXMLDOMCharacterData* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMCharacterData_Invoke(IXMLDOMCharacterData* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMCharacterData_get_nodeName(IXMLDOMCharacterData* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMCharacterData_get_nodeValue(IXMLDOMCharacterData* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMCharacterData_put_nodeValue(IXMLDOMCharacterData* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMCharacterData_get_nodeType(IXMLDOMCharacterData* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMCharacterData_get_parentNode(IXMLDOMCharacterData* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMCharacterData_get_childNodes(IXMLDOMCharacterData* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMCharacterData_get_firstChild(IXMLDOMCharacterData* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMCharacterData_get_lastChild(IXMLDOMCharacterData* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMCharacterData_get_previousSibling(IXMLDOMCharacterData* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMCharacterData_get_nextSibling(IXMLDOMCharacterData* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMCharacterData_get_attributes(IXMLDOMCharacterData* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMCharacterData_insertBefore(IXMLDOMCharacterData* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMCharacterData_replaceChild(IXMLDOMCharacterData* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMCharacterData_removeChild(IXMLDOMCharacterData* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMCharacterData_appendChild(IXMLDOMCharacterData* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMCharacterData_hasChildNodes(IXMLDOMCharacterData* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMCharacterData_get_ownerDocument(IXMLDOMCharacterData* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMCharacterData_cloneNode(IXMLDOMCharacterData* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMCharacterData_get_nodeTypeString(IXMLDOMCharacterData* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMCharacterData_get_text(IXMLDOMCharacterData* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMCharacterData_put_text(IXMLDOMCharacterData* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMCharacterData_get_specified(IXMLDOMCharacterData* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMCharacterData_get_definition(IXMLDOMCharacterData* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMCharacterData_get_nodeTypedValue(IXMLDOMCharacterData* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMCharacterData_put_nodeTypedValue(IXMLDOMCharacterData* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMCharacterData_get_dataType(IXMLDOMCharacterData* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMCharacterData_put_dataType(IXMLDOMCharacterData* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMCharacterData_get_xml(IXMLDOMCharacterData* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMCharacterData_transformNode(IXMLDOMCharacterData* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMCharacterData_selectNodes(IXMLDOMCharacterData* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMCharacterData_selectSingleNode(IXMLDOMCharacterData* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMCharacterData_get_parsed(IXMLDOMCharacterData* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMCharacterData_get_namespaceURI(IXMLDOMCharacterData* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMCharacterData_get_prefix(IXMLDOMCharacterData* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMCharacterData_get_baseName(IXMLDOMCharacterData* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMCharacterData_transformNodeToObject(IXMLDOMCharacterData* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
/*** IXMLDOMCharacterData methods ***/
static inline HRESULT IXMLDOMCharacterData_get_data(IXMLDOMCharacterData* This,BSTR *data) {
    return This->lpVtbl->get_data(This,data);
}
static inline HRESULT IXMLDOMCharacterData_put_data(IXMLDOMCharacterData* This,BSTR data) {
    return This->lpVtbl->put_data(This,data);
}
static inline HRESULT IXMLDOMCharacterData_get_length(IXMLDOMCharacterData* This,LONG *dataLength) {
    return This->lpVtbl->get_length(This,dataLength);
}
static inline HRESULT IXMLDOMCharacterData_substringData(IXMLDOMCharacterData* This,LONG offset,LONG count,BSTR *data) {
    return This->lpVtbl->substringData(This,offset,count,data);
}
static inline HRESULT IXMLDOMCharacterData_appendData(IXMLDOMCharacterData* This,BSTR data) {
    return This->lpVtbl->appendData(This,data);
}
static inline HRESULT IXMLDOMCharacterData_insertData(IXMLDOMCharacterData* This,LONG offset,BSTR data) {
    return This->lpVtbl->insertData(This,offset,data);
}
static inline HRESULT IXMLDOMCharacterData_deleteData(IXMLDOMCharacterData* This,LONG offset,LONG count) {
    return This->lpVtbl->deleteData(This,offset,count);
}
static inline HRESULT IXMLDOMCharacterData_replaceData(IXMLDOMCharacterData* This,LONG offset,LONG count,BSTR data) {
    return This->lpVtbl->replaceData(This,offset,count,data);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMCharacterData_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMAttribute interface
 */
#ifndef __IXMLDOMAttribute_INTERFACE_DEFINED__
#define __IXMLDOMAttribute_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMAttribute, 0x2933bf85, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf85-7b36-11d2-b20e-00c04f983e60")
IXMLDOMAttribute : public IXMLDOMNode
{
    virtual HRESULT STDMETHODCALLTYPE get_name(
        BSTR *attributeName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_value(
        VARIANT *attributeValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_value(
        VARIANT attributeValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMAttribute, 0x2933bf85, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMAttributeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMAttribute *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMAttribute *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMAttribute *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMAttribute *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMAttribute *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMAttribute *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMAttribute *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMAttribute *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMAttribute *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMAttribute *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMAttribute *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMAttribute *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMAttribute *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMAttribute *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMAttribute *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMAttribute *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMAttribute *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMAttribute *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMAttribute *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMAttribute *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMAttribute *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMAttribute *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMAttribute *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMAttribute *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMAttribute *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMAttribute *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMAttribute *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMAttribute *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMAttribute *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMAttribute *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMAttribute *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMAttribute *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMAttribute *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMAttribute *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMAttribute *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMAttribute *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMAttribute *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMAttribute *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMAttribute *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMAttribute *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMAttribute *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMAttribute *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMAttribute *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    /*** IXMLDOMAttribute methods ***/
    HRESULT (STDMETHODCALLTYPE *get_name)(
        IXMLDOMAttribute *This,
        BSTR *attributeName);

    HRESULT (STDMETHODCALLTYPE *get_value)(
        IXMLDOMAttribute *This,
        VARIANT *attributeValue);

    HRESULT (STDMETHODCALLTYPE *put_value)(
        IXMLDOMAttribute *This,
        VARIANT attributeValue);

    END_INTERFACE
} IXMLDOMAttributeVtbl;

interface IXMLDOMAttribute {
    CONST_VTBL IXMLDOMAttributeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMAttribute_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMAttribute_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMAttribute_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMAttribute_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMAttribute_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMAttribute_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMAttribute_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMAttribute_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMAttribute_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMAttribute_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMAttribute_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMAttribute_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMAttribute_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMAttribute_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMAttribute_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMAttribute_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMAttribute_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMAttribute_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMAttribute_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMAttribute_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMAttribute_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMAttribute_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMAttribute_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMAttribute_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMAttribute_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMAttribute_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMAttribute_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMAttribute_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMAttribute_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMAttribute_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMAttribute_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMAttribute_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMAttribute_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMAttribute_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMAttribute_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMAttribute_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMAttribute_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMAttribute_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMAttribute_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMAttribute_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMAttribute_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMAttribute_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMAttribute_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
/*** IXMLDOMAttribute methods ***/
#define IXMLDOMAttribute_get_name(This,attributeName) (This)->lpVtbl->get_name(This,attributeName)
#define IXMLDOMAttribute_get_value(This,attributeValue) (This)->lpVtbl->get_value(This,attributeValue)
#define IXMLDOMAttribute_put_value(This,attributeValue) (This)->lpVtbl->put_value(This,attributeValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMAttribute_QueryInterface(IXMLDOMAttribute* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMAttribute_AddRef(IXMLDOMAttribute* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMAttribute_Release(IXMLDOMAttribute* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMAttribute_GetTypeInfoCount(IXMLDOMAttribute* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMAttribute_GetTypeInfo(IXMLDOMAttribute* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMAttribute_GetIDsOfNames(IXMLDOMAttribute* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMAttribute_Invoke(IXMLDOMAttribute* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMAttribute_get_nodeName(IXMLDOMAttribute* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMAttribute_get_nodeValue(IXMLDOMAttribute* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMAttribute_put_nodeValue(IXMLDOMAttribute* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMAttribute_get_nodeType(IXMLDOMAttribute* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMAttribute_get_parentNode(IXMLDOMAttribute* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMAttribute_get_childNodes(IXMLDOMAttribute* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMAttribute_get_firstChild(IXMLDOMAttribute* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMAttribute_get_lastChild(IXMLDOMAttribute* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMAttribute_get_previousSibling(IXMLDOMAttribute* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMAttribute_get_nextSibling(IXMLDOMAttribute* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMAttribute_get_attributes(IXMLDOMAttribute* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMAttribute_insertBefore(IXMLDOMAttribute* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMAttribute_replaceChild(IXMLDOMAttribute* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMAttribute_removeChild(IXMLDOMAttribute* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMAttribute_appendChild(IXMLDOMAttribute* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMAttribute_hasChildNodes(IXMLDOMAttribute* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMAttribute_get_ownerDocument(IXMLDOMAttribute* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMAttribute_cloneNode(IXMLDOMAttribute* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMAttribute_get_nodeTypeString(IXMLDOMAttribute* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMAttribute_get_text(IXMLDOMAttribute* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMAttribute_put_text(IXMLDOMAttribute* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMAttribute_get_specified(IXMLDOMAttribute* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMAttribute_get_definition(IXMLDOMAttribute* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMAttribute_get_nodeTypedValue(IXMLDOMAttribute* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMAttribute_put_nodeTypedValue(IXMLDOMAttribute* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMAttribute_get_dataType(IXMLDOMAttribute* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMAttribute_put_dataType(IXMLDOMAttribute* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMAttribute_get_xml(IXMLDOMAttribute* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMAttribute_transformNode(IXMLDOMAttribute* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMAttribute_selectNodes(IXMLDOMAttribute* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMAttribute_selectSingleNode(IXMLDOMAttribute* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMAttribute_get_parsed(IXMLDOMAttribute* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMAttribute_get_namespaceURI(IXMLDOMAttribute* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMAttribute_get_prefix(IXMLDOMAttribute* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMAttribute_get_baseName(IXMLDOMAttribute* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMAttribute_transformNodeToObject(IXMLDOMAttribute* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
/*** IXMLDOMAttribute methods ***/
static inline HRESULT IXMLDOMAttribute_get_name(IXMLDOMAttribute* This,BSTR *attributeName) {
    return This->lpVtbl->get_name(This,attributeName);
}
static inline HRESULT IXMLDOMAttribute_get_value(IXMLDOMAttribute* This,VARIANT *attributeValue) {
    return This->lpVtbl->get_value(This,attributeValue);
}
static inline HRESULT IXMLDOMAttribute_put_value(IXMLDOMAttribute* This,VARIANT attributeValue) {
    return This->lpVtbl->put_value(This,attributeValue);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMAttribute_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMElement interface
 */
#ifndef __IXMLDOMElement_INTERFACE_DEFINED__
#define __IXMLDOMElement_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMElement, 0x2933bf86, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf86-7b36-11d2-b20e-00c04f983e60")
IXMLDOMElement : public IXMLDOMNode
{
    virtual HRESULT STDMETHODCALLTYPE get_tagName(
        BSTR *tagName) = 0;

    virtual HRESULT STDMETHODCALLTYPE getAttribute(
        BSTR name,
        VARIANT *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE setAttribute(
        BSTR name,
        VARIANT value) = 0;

    virtual HRESULT STDMETHODCALLTYPE removeAttribute(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE getAttributeNode(
        BSTR name,
        IXMLDOMAttribute **attributeNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE setAttributeNode(
        IXMLDOMAttribute *DOMAttribute,
        IXMLDOMAttribute **attributeNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE removeAttributeNode(
        IXMLDOMAttribute *DOMAttribute,
        IXMLDOMAttribute **attributeNode) = 0;

    virtual HRESULT STDMETHODCALLTYPE getElementsByTagName(
        BSTR tagName,
        IXMLDOMNodeList **resultList) = 0;

    virtual HRESULT STDMETHODCALLTYPE normalize(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMElement, 0x2933bf86, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMElementVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMElement *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMElement *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMElement *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMElement *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMElement *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMElement *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMElement *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMElement *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMElement *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMElement *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMElement *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMElement *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMElement *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMElement *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMElement *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMElement *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMElement *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMElement *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMElement *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMElement *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMElement *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMElement *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMElement *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMElement *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMElement *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMElement *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMElement *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMElement *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMElement *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMElement *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMElement *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMElement *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMElement *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMElement *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMElement *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMElement *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMElement *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMElement *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMElement *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMElement *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMElement *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMElement *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMElement *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    /*** IXMLDOMElement methods ***/
    HRESULT (STDMETHODCALLTYPE *get_tagName)(
        IXMLDOMElement *This,
        BSTR *tagName);

    HRESULT (STDMETHODCALLTYPE *getAttribute)(
        IXMLDOMElement *This,
        BSTR name,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *setAttribute)(
        IXMLDOMElement *This,
        BSTR name,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *removeAttribute)(
        IXMLDOMElement *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *getAttributeNode)(
        IXMLDOMElement *This,
        BSTR name,
        IXMLDOMAttribute **attributeNode);

    HRESULT (STDMETHODCALLTYPE *setAttributeNode)(
        IXMLDOMElement *This,
        IXMLDOMAttribute *DOMAttribute,
        IXMLDOMAttribute **attributeNode);

    HRESULT (STDMETHODCALLTYPE *removeAttributeNode)(
        IXMLDOMElement *This,
        IXMLDOMAttribute *DOMAttribute,
        IXMLDOMAttribute **attributeNode);

    HRESULT (STDMETHODCALLTYPE *getElementsByTagName)(
        IXMLDOMElement *This,
        BSTR tagName,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *normalize)(
        IXMLDOMElement *This);

    END_INTERFACE
} IXMLDOMElementVtbl;

interface IXMLDOMElement {
    CONST_VTBL IXMLDOMElementVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMElement_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMElement_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMElement_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMElement_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMElement_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMElement_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMElement_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMElement_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMElement_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMElement_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMElement_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMElement_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMElement_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMElement_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMElement_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMElement_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMElement_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMElement_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMElement_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMElement_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMElement_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMElement_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMElement_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMElement_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMElement_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMElement_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMElement_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMElement_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMElement_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMElement_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMElement_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMElement_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMElement_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMElement_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMElement_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMElement_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMElement_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMElement_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMElement_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMElement_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMElement_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMElement_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMElement_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
/*** IXMLDOMElement methods ***/
#define IXMLDOMElement_get_tagName(This,tagName) (This)->lpVtbl->get_tagName(This,tagName)
#define IXMLDOMElement_getAttribute(This,name,value) (This)->lpVtbl->getAttribute(This,name,value)
#define IXMLDOMElement_setAttribute(This,name,value) (This)->lpVtbl->setAttribute(This,name,value)
#define IXMLDOMElement_removeAttribute(This,name) (This)->lpVtbl->removeAttribute(This,name)
#define IXMLDOMElement_getAttributeNode(This,name,attributeNode) (This)->lpVtbl->getAttributeNode(This,name,attributeNode)
#define IXMLDOMElement_setAttributeNode(This,DOMAttribute,attributeNode) (This)->lpVtbl->setAttributeNode(This,DOMAttribute,attributeNode)
#define IXMLDOMElement_removeAttributeNode(This,DOMAttribute,attributeNode) (This)->lpVtbl->removeAttributeNode(This,DOMAttribute,attributeNode)
#define IXMLDOMElement_getElementsByTagName(This,tagName,resultList) (This)->lpVtbl->getElementsByTagName(This,tagName,resultList)
#define IXMLDOMElement_normalize(This) (This)->lpVtbl->normalize(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMElement_QueryInterface(IXMLDOMElement* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMElement_AddRef(IXMLDOMElement* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMElement_Release(IXMLDOMElement* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMElement_GetTypeInfoCount(IXMLDOMElement* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMElement_GetTypeInfo(IXMLDOMElement* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMElement_GetIDsOfNames(IXMLDOMElement* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMElement_Invoke(IXMLDOMElement* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMElement_get_nodeName(IXMLDOMElement* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMElement_get_nodeValue(IXMLDOMElement* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMElement_put_nodeValue(IXMLDOMElement* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMElement_get_nodeType(IXMLDOMElement* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMElement_get_parentNode(IXMLDOMElement* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMElement_get_childNodes(IXMLDOMElement* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMElement_get_firstChild(IXMLDOMElement* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMElement_get_lastChild(IXMLDOMElement* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMElement_get_previousSibling(IXMLDOMElement* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMElement_get_nextSibling(IXMLDOMElement* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMElement_get_attributes(IXMLDOMElement* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMElement_insertBefore(IXMLDOMElement* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMElement_replaceChild(IXMLDOMElement* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMElement_removeChild(IXMLDOMElement* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMElement_appendChild(IXMLDOMElement* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMElement_hasChildNodes(IXMLDOMElement* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMElement_get_ownerDocument(IXMLDOMElement* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMElement_cloneNode(IXMLDOMElement* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMElement_get_nodeTypeString(IXMLDOMElement* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMElement_get_text(IXMLDOMElement* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMElement_put_text(IXMLDOMElement* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMElement_get_specified(IXMLDOMElement* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMElement_get_definition(IXMLDOMElement* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMElement_get_nodeTypedValue(IXMLDOMElement* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMElement_put_nodeTypedValue(IXMLDOMElement* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMElement_get_dataType(IXMLDOMElement* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMElement_put_dataType(IXMLDOMElement* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMElement_get_xml(IXMLDOMElement* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMElement_transformNode(IXMLDOMElement* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMElement_selectNodes(IXMLDOMElement* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMElement_selectSingleNode(IXMLDOMElement* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMElement_get_parsed(IXMLDOMElement* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMElement_get_namespaceURI(IXMLDOMElement* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMElement_get_prefix(IXMLDOMElement* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMElement_get_baseName(IXMLDOMElement* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMElement_transformNodeToObject(IXMLDOMElement* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
/*** IXMLDOMElement methods ***/
static inline HRESULT IXMLDOMElement_get_tagName(IXMLDOMElement* This,BSTR *tagName) {
    return This->lpVtbl->get_tagName(This,tagName);
}
static inline HRESULT IXMLDOMElement_getAttribute(IXMLDOMElement* This,BSTR name,VARIANT *value) {
    return This->lpVtbl->getAttribute(This,name,value);
}
static inline HRESULT IXMLDOMElement_setAttribute(IXMLDOMElement* This,BSTR name,VARIANT value) {
    return This->lpVtbl->setAttribute(This,name,value);
}
static inline HRESULT IXMLDOMElement_removeAttribute(IXMLDOMElement* This,BSTR name) {
    return This->lpVtbl->removeAttribute(This,name);
}
static inline HRESULT IXMLDOMElement_getAttributeNode(IXMLDOMElement* This,BSTR name,IXMLDOMAttribute **attributeNode) {
    return This->lpVtbl->getAttributeNode(This,name,attributeNode);
}
static inline HRESULT IXMLDOMElement_setAttributeNode(IXMLDOMElement* This,IXMLDOMAttribute *DOMAttribute,IXMLDOMAttribute **attributeNode) {
    return This->lpVtbl->setAttributeNode(This,DOMAttribute,attributeNode);
}
static inline HRESULT IXMLDOMElement_removeAttributeNode(IXMLDOMElement* This,IXMLDOMAttribute *DOMAttribute,IXMLDOMAttribute **attributeNode) {
    return This->lpVtbl->removeAttributeNode(This,DOMAttribute,attributeNode);
}
static inline HRESULT IXMLDOMElement_getElementsByTagName(IXMLDOMElement* This,BSTR tagName,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->getElementsByTagName(This,tagName,resultList);
}
static inline HRESULT IXMLDOMElement_normalize(IXMLDOMElement* This) {
    return This->lpVtbl->normalize(This);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMElement_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMText interface
 */
#ifndef __IXMLDOMText_INTERFACE_DEFINED__
#define __IXMLDOMText_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMText, 0x2933bf87, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf87-7b36-11d2-b20e-00c04f983e60")
IXMLDOMText : public IXMLDOMCharacterData
{
    virtual HRESULT STDMETHODCALLTYPE splitText(
        LONG offset,
        IXMLDOMText **rightHandTextNode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMText, 0x2933bf87, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMTextVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMText *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMText *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMText *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMText *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMText *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMText *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMText *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMText *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMText *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMText *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMText *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMText *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMText *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMText *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMText *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMText *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMText *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMText *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMText *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMText *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMText *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMText *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMText *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMText *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMText *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMText *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMText *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMText *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMText *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMText *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMText *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMText *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMText *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMText *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMText *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMText *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMText *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMText *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMText *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMText *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMText *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMText *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMText *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    /*** IXMLDOMCharacterData methods ***/
    HRESULT (STDMETHODCALLTYPE *get_data)(
        IXMLDOMText *This,
        BSTR *data);

    HRESULT (STDMETHODCALLTYPE *put_data)(
        IXMLDOMText *This,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *get_length)(
        IXMLDOMText *This,
        LONG *dataLength);

    HRESULT (STDMETHODCALLTYPE *substringData)(
        IXMLDOMText *This,
        LONG offset,
        LONG count,
        BSTR *data);

    HRESULT (STDMETHODCALLTYPE *appendData)(
        IXMLDOMText *This,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *insertData)(
        IXMLDOMText *This,
        LONG offset,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *deleteData)(
        IXMLDOMText *This,
        LONG offset,
        LONG count);

    HRESULT (STDMETHODCALLTYPE *replaceData)(
        IXMLDOMText *This,
        LONG offset,
        LONG count,
        BSTR data);

    /*** IXMLDOMText methods ***/
    HRESULT (STDMETHODCALLTYPE *splitText)(
        IXMLDOMText *This,
        LONG offset,
        IXMLDOMText **rightHandTextNode);

    END_INTERFACE
} IXMLDOMTextVtbl;

interface IXMLDOMText {
    CONST_VTBL IXMLDOMTextVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMText_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMText_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMText_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMText_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMText_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMText_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMText_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMText_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMText_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMText_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMText_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMText_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMText_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMText_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMText_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMText_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMText_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMText_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMText_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMText_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMText_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMText_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMText_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMText_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMText_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMText_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMText_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMText_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMText_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMText_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMText_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMText_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMText_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMText_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMText_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMText_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMText_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMText_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMText_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMText_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMText_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMText_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMText_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
/*** IXMLDOMCharacterData methods ***/
#define IXMLDOMText_get_data(This,data) (This)->lpVtbl->get_data(This,data)
#define IXMLDOMText_put_data(This,data) (This)->lpVtbl->put_data(This,data)
#define IXMLDOMText_get_length(This,dataLength) (This)->lpVtbl->get_length(This,dataLength)
#define IXMLDOMText_substringData(This,offset,count,data) (This)->lpVtbl->substringData(This,offset,count,data)
#define IXMLDOMText_appendData(This,data) (This)->lpVtbl->appendData(This,data)
#define IXMLDOMText_insertData(This,offset,data) (This)->lpVtbl->insertData(This,offset,data)
#define IXMLDOMText_deleteData(This,offset,count) (This)->lpVtbl->deleteData(This,offset,count)
#define IXMLDOMText_replaceData(This,offset,count,data) (This)->lpVtbl->replaceData(This,offset,count,data)
/*** IXMLDOMText methods ***/
#define IXMLDOMText_splitText(This,offset,rightHandTextNode) (This)->lpVtbl->splitText(This,offset,rightHandTextNode)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMText_QueryInterface(IXMLDOMText* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMText_AddRef(IXMLDOMText* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMText_Release(IXMLDOMText* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMText_GetTypeInfoCount(IXMLDOMText* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMText_GetTypeInfo(IXMLDOMText* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMText_GetIDsOfNames(IXMLDOMText* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMText_Invoke(IXMLDOMText* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMText_get_nodeName(IXMLDOMText* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMText_get_nodeValue(IXMLDOMText* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMText_put_nodeValue(IXMLDOMText* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMText_get_nodeType(IXMLDOMText* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMText_get_parentNode(IXMLDOMText* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMText_get_childNodes(IXMLDOMText* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMText_get_firstChild(IXMLDOMText* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMText_get_lastChild(IXMLDOMText* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMText_get_previousSibling(IXMLDOMText* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMText_get_nextSibling(IXMLDOMText* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMText_get_attributes(IXMLDOMText* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMText_insertBefore(IXMLDOMText* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMText_replaceChild(IXMLDOMText* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMText_removeChild(IXMLDOMText* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMText_appendChild(IXMLDOMText* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMText_hasChildNodes(IXMLDOMText* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMText_get_ownerDocument(IXMLDOMText* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMText_cloneNode(IXMLDOMText* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMText_get_nodeTypeString(IXMLDOMText* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMText_get_text(IXMLDOMText* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMText_put_text(IXMLDOMText* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMText_get_specified(IXMLDOMText* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMText_get_definition(IXMLDOMText* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMText_get_nodeTypedValue(IXMLDOMText* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMText_put_nodeTypedValue(IXMLDOMText* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMText_get_dataType(IXMLDOMText* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMText_put_dataType(IXMLDOMText* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMText_get_xml(IXMLDOMText* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMText_transformNode(IXMLDOMText* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMText_selectNodes(IXMLDOMText* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMText_selectSingleNode(IXMLDOMText* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMText_get_parsed(IXMLDOMText* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMText_get_namespaceURI(IXMLDOMText* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMText_get_prefix(IXMLDOMText* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMText_get_baseName(IXMLDOMText* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMText_transformNodeToObject(IXMLDOMText* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
/*** IXMLDOMCharacterData methods ***/
static inline HRESULT IXMLDOMText_get_data(IXMLDOMText* This,BSTR *data) {
    return This->lpVtbl->get_data(This,data);
}
static inline HRESULT IXMLDOMText_put_data(IXMLDOMText* This,BSTR data) {
    return This->lpVtbl->put_data(This,data);
}
static inline HRESULT IXMLDOMText_get_length(IXMLDOMText* This,LONG *dataLength) {
    return This->lpVtbl->get_length(This,dataLength);
}
static inline HRESULT IXMLDOMText_substringData(IXMLDOMText* This,LONG offset,LONG count,BSTR *data) {
    return This->lpVtbl->substringData(This,offset,count,data);
}
static inline HRESULT IXMLDOMText_appendData(IXMLDOMText* This,BSTR data) {
    return This->lpVtbl->appendData(This,data);
}
static inline HRESULT IXMLDOMText_insertData(IXMLDOMText* This,LONG offset,BSTR data) {
    return This->lpVtbl->insertData(This,offset,data);
}
static inline HRESULT IXMLDOMText_deleteData(IXMLDOMText* This,LONG offset,LONG count) {
    return This->lpVtbl->deleteData(This,offset,count);
}
static inline HRESULT IXMLDOMText_replaceData(IXMLDOMText* This,LONG offset,LONG count,BSTR data) {
    return This->lpVtbl->replaceData(This,offset,count,data);
}
/*** IXMLDOMText methods ***/
static inline HRESULT IXMLDOMText_splitText(IXMLDOMText* This,LONG offset,IXMLDOMText **rightHandTextNode) {
    return This->lpVtbl->splitText(This,offset,rightHandTextNode);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMText_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMComment interface
 */
#ifndef __IXMLDOMComment_INTERFACE_DEFINED__
#define __IXMLDOMComment_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMComment, 0x2933bf88, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf88-7b36-11d2-b20e-00c04f983e60")
IXMLDOMComment : public IXMLDOMCharacterData
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMComment, 0x2933bf88, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMCommentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMComment *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMComment *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMComment *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMComment *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMComment *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMComment *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMComment *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMComment *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMComment *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMComment *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMComment *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMComment *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMComment *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMComment *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMComment *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMComment *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMComment *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMComment *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMComment *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMComment *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMComment *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMComment *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMComment *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMComment *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMComment *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMComment *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMComment *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMComment *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMComment *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMComment *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMComment *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMComment *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMComment *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMComment *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMComment *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMComment *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMComment *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMComment *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMComment *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMComment *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMComment *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMComment *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMComment *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    /*** IXMLDOMCharacterData methods ***/
    HRESULT (STDMETHODCALLTYPE *get_data)(
        IXMLDOMComment *This,
        BSTR *data);

    HRESULT (STDMETHODCALLTYPE *put_data)(
        IXMLDOMComment *This,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *get_length)(
        IXMLDOMComment *This,
        LONG *dataLength);

    HRESULT (STDMETHODCALLTYPE *substringData)(
        IXMLDOMComment *This,
        LONG offset,
        LONG count,
        BSTR *data);

    HRESULT (STDMETHODCALLTYPE *appendData)(
        IXMLDOMComment *This,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *insertData)(
        IXMLDOMComment *This,
        LONG offset,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *deleteData)(
        IXMLDOMComment *This,
        LONG offset,
        LONG count);

    HRESULT (STDMETHODCALLTYPE *replaceData)(
        IXMLDOMComment *This,
        LONG offset,
        LONG count,
        BSTR data);

    END_INTERFACE
} IXMLDOMCommentVtbl;

interface IXMLDOMComment {
    CONST_VTBL IXMLDOMCommentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMComment_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMComment_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMComment_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMComment_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMComment_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMComment_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMComment_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMComment_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMComment_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMComment_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMComment_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMComment_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMComment_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMComment_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMComment_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMComment_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMComment_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMComment_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMComment_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMComment_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMComment_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMComment_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMComment_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMComment_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMComment_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMComment_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMComment_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMComment_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMComment_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMComment_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMComment_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMComment_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMComment_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMComment_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMComment_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMComment_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMComment_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMComment_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMComment_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMComment_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMComment_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMComment_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMComment_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
/*** IXMLDOMCharacterData methods ***/
#define IXMLDOMComment_get_data(This,data) (This)->lpVtbl->get_data(This,data)
#define IXMLDOMComment_put_data(This,data) (This)->lpVtbl->put_data(This,data)
#define IXMLDOMComment_get_length(This,dataLength) (This)->lpVtbl->get_length(This,dataLength)
#define IXMLDOMComment_substringData(This,offset,count,data) (This)->lpVtbl->substringData(This,offset,count,data)
#define IXMLDOMComment_appendData(This,data) (This)->lpVtbl->appendData(This,data)
#define IXMLDOMComment_insertData(This,offset,data) (This)->lpVtbl->insertData(This,offset,data)
#define IXMLDOMComment_deleteData(This,offset,count) (This)->lpVtbl->deleteData(This,offset,count)
#define IXMLDOMComment_replaceData(This,offset,count,data) (This)->lpVtbl->replaceData(This,offset,count,data)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMComment_QueryInterface(IXMLDOMComment* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMComment_AddRef(IXMLDOMComment* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMComment_Release(IXMLDOMComment* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMComment_GetTypeInfoCount(IXMLDOMComment* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMComment_GetTypeInfo(IXMLDOMComment* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMComment_GetIDsOfNames(IXMLDOMComment* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMComment_Invoke(IXMLDOMComment* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMComment_get_nodeName(IXMLDOMComment* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMComment_get_nodeValue(IXMLDOMComment* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMComment_put_nodeValue(IXMLDOMComment* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMComment_get_nodeType(IXMLDOMComment* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMComment_get_parentNode(IXMLDOMComment* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMComment_get_childNodes(IXMLDOMComment* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMComment_get_firstChild(IXMLDOMComment* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMComment_get_lastChild(IXMLDOMComment* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMComment_get_previousSibling(IXMLDOMComment* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMComment_get_nextSibling(IXMLDOMComment* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMComment_get_attributes(IXMLDOMComment* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMComment_insertBefore(IXMLDOMComment* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMComment_replaceChild(IXMLDOMComment* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMComment_removeChild(IXMLDOMComment* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMComment_appendChild(IXMLDOMComment* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMComment_hasChildNodes(IXMLDOMComment* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMComment_get_ownerDocument(IXMLDOMComment* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMComment_cloneNode(IXMLDOMComment* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMComment_get_nodeTypeString(IXMLDOMComment* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMComment_get_text(IXMLDOMComment* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMComment_put_text(IXMLDOMComment* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMComment_get_specified(IXMLDOMComment* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMComment_get_definition(IXMLDOMComment* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMComment_get_nodeTypedValue(IXMLDOMComment* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMComment_put_nodeTypedValue(IXMLDOMComment* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMComment_get_dataType(IXMLDOMComment* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMComment_put_dataType(IXMLDOMComment* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMComment_get_xml(IXMLDOMComment* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMComment_transformNode(IXMLDOMComment* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMComment_selectNodes(IXMLDOMComment* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMComment_selectSingleNode(IXMLDOMComment* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMComment_get_parsed(IXMLDOMComment* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMComment_get_namespaceURI(IXMLDOMComment* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMComment_get_prefix(IXMLDOMComment* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMComment_get_baseName(IXMLDOMComment* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMComment_transformNodeToObject(IXMLDOMComment* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
/*** IXMLDOMCharacterData methods ***/
static inline HRESULT IXMLDOMComment_get_data(IXMLDOMComment* This,BSTR *data) {
    return This->lpVtbl->get_data(This,data);
}
static inline HRESULT IXMLDOMComment_put_data(IXMLDOMComment* This,BSTR data) {
    return This->lpVtbl->put_data(This,data);
}
static inline HRESULT IXMLDOMComment_get_length(IXMLDOMComment* This,LONG *dataLength) {
    return This->lpVtbl->get_length(This,dataLength);
}
static inline HRESULT IXMLDOMComment_substringData(IXMLDOMComment* This,LONG offset,LONG count,BSTR *data) {
    return This->lpVtbl->substringData(This,offset,count,data);
}
static inline HRESULT IXMLDOMComment_appendData(IXMLDOMComment* This,BSTR data) {
    return This->lpVtbl->appendData(This,data);
}
static inline HRESULT IXMLDOMComment_insertData(IXMLDOMComment* This,LONG offset,BSTR data) {
    return This->lpVtbl->insertData(This,offset,data);
}
static inline HRESULT IXMLDOMComment_deleteData(IXMLDOMComment* This,LONG offset,LONG count) {
    return This->lpVtbl->deleteData(This,offset,count);
}
static inline HRESULT IXMLDOMComment_replaceData(IXMLDOMComment* This,LONG offset,LONG count,BSTR data) {
    return This->lpVtbl->replaceData(This,offset,count,data);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMComment_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMProcessingInstruction interface
 */
#ifndef __IXMLDOMProcessingInstruction_INTERFACE_DEFINED__
#define __IXMLDOMProcessingInstruction_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMProcessingInstruction, 0x2933bf89, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf89-7b36-11d2-b20e-00c04f983e60")
IXMLDOMProcessingInstruction : public IXMLDOMNode
{
    virtual HRESULT STDMETHODCALLTYPE get_target(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_data(
        BSTR *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_data(
        BSTR value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMProcessingInstruction, 0x2933bf89, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMProcessingInstructionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMProcessingInstruction *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMProcessingInstruction *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMProcessingInstruction *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMProcessingInstruction *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMProcessingInstruction *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMProcessingInstruction *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMProcessingInstruction *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMProcessingInstruction *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMProcessingInstruction *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMProcessingInstruction *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMProcessingInstruction *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMProcessingInstruction *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMProcessingInstruction *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMProcessingInstruction *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMProcessingInstruction *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMProcessingInstruction *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMProcessingInstruction *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMProcessingInstruction *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMProcessingInstruction *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMProcessingInstruction *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMProcessingInstruction *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMProcessingInstruction *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMProcessingInstruction *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMProcessingInstruction *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMProcessingInstruction *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMProcessingInstruction *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMProcessingInstruction *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMProcessingInstruction *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMProcessingInstruction *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    /*** IXMLDOMProcessingInstruction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_target)(
        IXMLDOMProcessingInstruction *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_data)(
        IXMLDOMProcessingInstruction *This,
        BSTR *value);

    HRESULT (STDMETHODCALLTYPE *put_data)(
        IXMLDOMProcessingInstruction *This,
        BSTR value);

    END_INTERFACE
} IXMLDOMProcessingInstructionVtbl;

interface IXMLDOMProcessingInstruction {
    CONST_VTBL IXMLDOMProcessingInstructionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMProcessingInstruction_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMProcessingInstruction_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMProcessingInstruction_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMProcessingInstruction_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMProcessingInstruction_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMProcessingInstruction_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMProcessingInstruction_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMProcessingInstruction_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMProcessingInstruction_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMProcessingInstruction_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMProcessingInstruction_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMProcessingInstruction_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMProcessingInstruction_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMProcessingInstruction_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMProcessingInstruction_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMProcessingInstruction_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMProcessingInstruction_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMProcessingInstruction_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMProcessingInstruction_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMProcessingInstruction_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMProcessingInstruction_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMProcessingInstruction_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMProcessingInstruction_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMProcessingInstruction_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMProcessingInstruction_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMProcessingInstruction_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMProcessingInstruction_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMProcessingInstruction_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMProcessingInstruction_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMProcessingInstruction_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMProcessingInstruction_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMProcessingInstruction_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMProcessingInstruction_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMProcessingInstruction_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMProcessingInstruction_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMProcessingInstruction_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMProcessingInstruction_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMProcessingInstruction_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMProcessingInstruction_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMProcessingInstruction_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMProcessingInstruction_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMProcessingInstruction_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMProcessingInstruction_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
/*** IXMLDOMProcessingInstruction methods ***/
#define IXMLDOMProcessingInstruction_get_target(This,name) (This)->lpVtbl->get_target(This,name)
#define IXMLDOMProcessingInstruction_get_data(This,value) (This)->lpVtbl->get_data(This,value)
#define IXMLDOMProcessingInstruction_put_data(This,value) (This)->lpVtbl->put_data(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMProcessingInstruction_QueryInterface(IXMLDOMProcessingInstruction* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMProcessingInstruction_AddRef(IXMLDOMProcessingInstruction* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMProcessingInstruction_Release(IXMLDOMProcessingInstruction* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMProcessingInstruction_GetTypeInfoCount(IXMLDOMProcessingInstruction* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMProcessingInstruction_GetTypeInfo(IXMLDOMProcessingInstruction* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMProcessingInstruction_GetIDsOfNames(IXMLDOMProcessingInstruction* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMProcessingInstruction_Invoke(IXMLDOMProcessingInstruction* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMProcessingInstruction_get_nodeName(IXMLDOMProcessingInstruction* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_nodeValue(IXMLDOMProcessingInstruction* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMProcessingInstruction_put_nodeValue(IXMLDOMProcessingInstruction* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_nodeType(IXMLDOMProcessingInstruction* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_parentNode(IXMLDOMProcessingInstruction* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_childNodes(IXMLDOMProcessingInstruction* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_firstChild(IXMLDOMProcessingInstruction* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_lastChild(IXMLDOMProcessingInstruction* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_previousSibling(IXMLDOMProcessingInstruction* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_nextSibling(IXMLDOMProcessingInstruction* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_attributes(IXMLDOMProcessingInstruction* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMProcessingInstruction_insertBefore(IXMLDOMProcessingInstruction* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMProcessingInstruction_replaceChild(IXMLDOMProcessingInstruction* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMProcessingInstruction_removeChild(IXMLDOMProcessingInstruction* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMProcessingInstruction_appendChild(IXMLDOMProcessingInstruction* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMProcessingInstruction_hasChildNodes(IXMLDOMProcessingInstruction* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_ownerDocument(IXMLDOMProcessingInstruction* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMProcessingInstruction_cloneNode(IXMLDOMProcessingInstruction* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_nodeTypeString(IXMLDOMProcessingInstruction* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_text(IXMLDOMProcessingInstruction* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMProcessingInstruction_put_text(IXMLDOMProcessingInstruction* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_specified(IXMLDOMProcessingInstruction* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_definition(IXMLDOMProcessingInstruction* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_nodeTypedValue(IXMLDOMProcessingInstruction* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMProcessingInstruction_put_nodeTypedValue(IXMLDOMProcessingInstruction* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_dataType(IXMLDOMProcessingInstruction* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMProcessingInstruction_put_dataType(IXMLDOMProcessingInstruction* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_xml(IXMLDOMProcessingInstruction* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMProcessingInstruction_transformNode(IXMLDOMProcessingInstruction* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMProcessingInstruction_selectNodes(IXMLDOMProcessingInstruction* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMProcessingInstruction_selectSingleNode(IXMLDOMProcessingInstruction* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_parsed(IXMLDOMProcessingInstruction* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_namespaceURI(IXMLDOMProcessingInstruction* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_prefix(IXMLDOMProcessingInstruction* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_baseName(IXMLDOMProcessingInstruction* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMProcessingInstruction_transformNodeToObject(IXMLDOMProcessingInstruction* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
/*** IXMLDOMProcessingInstruction methods ***/
static inline HRESULT IXMLDOMProcessingInstruction_get_target(IXMLDOMProcessingInstruction* This,BSTR *name) {
    return This->lpVtbl->get_target(This,name);
}
static inline HRESULT IXMLDOMProcessingInstruction_get_data(IXMLDOMProcessingInstruction* This,BSTR *value) {
    return This->lpVtbl->get_data(This,value);
}
static inline HRESULT IXMLDOMProcessingInstruction_put_data(IXMLDOMProcessingInstruction* This,BSTR value) {
    return This->lpVtbl->put_data(This,value);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMProcessingInstruction_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMCDATASection interface
 */
#ifndef __IXMLDOMCDATASection_INTERFACE_DEFINED__
#define __IXMLDOMCDATASection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMCDATASection, 0x2933bf8a, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf8a-7b36-11d2-b20e-00c04f983e60")
IXMLDOMCDATASection : public IXMLDOMText
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMCDATASection, 0x2933bf8a, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMCDATASectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMCDATASection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMCDATASection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMCDATASection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMCDATASection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMCDATASection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMCDATASection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMCDATASection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMCDATASection *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMCDATASection *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMCDATASection *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMCDATASection *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMCDATASection *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMCDATASection *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMCDATASection *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMCDATASection *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMCDATASection *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMCDATASection *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMCDATASection *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMCDATASection *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMCDATASection *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMCDATASection *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMCDATASection *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMCDATASection *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMCDATASection *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMCDATASection *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMCDATASection *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMCDATASection *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMCDATASection *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMCDATASection *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMCDATASection *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMCDATASection *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMCDATASection *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    /*** IXMLDOMCharacterData methods ***/
    HRESULT (STDMETHODCALLTYPE *get_data)(
        IXMLDOMCDATASection *This,
        BSTR *data);

    HRESULT (STDMETHODCALLTYPE *put_data)(
        IXMLDOMCDATASection *This,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *get_length)(
        IXMLDOMCDATASection *This,
        LONG *dataLength);

    HRESULT (STDMETHODCALLTYPE *substringData)(
        IXMLDOMCDATASection *This,
        LONG offset,
        LONG count,
        BSTR *data);

    HRESULT (STDMETHODCALLTYPE *appendData)(
        IXMLDOMCDATASection *This,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *insertData)(
        IXMLDOMCDATASection *This,
        LONG offset,
        BSTR data);

    HRESULT (STDMETHODCALLTYPE *deleteData)(
        IXMLDOMCDATASection *This,
        LONG offset,
        LONG count);

    HRESULT (STDMETHODCALLTYPE *replaceData)(
        IXMLDOMCDATASection *This,
        LONG offset,
        LONG count,
        BSTR data);

    /*** IXMLDOMText methods ***/
    HRESULT (STDMETHODCALLTYPE *splitText)(
        IXMLDOMCDATASection *This,
        LONG offset,
        IXMLDOMText **rightHandTextNode);

    END_INTERFACE
} IXMLDOMCDATASectionVtbl;

interface IXMLDOMCDATASection {
    CONST_VTBL IXMLDOMCDATASectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMCDATASection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMCDATASection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMCDATASection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMCDATASection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMCDATASection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMCDATASection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMCDATASection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMCDATASection_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMCDATASection_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMCDATASection_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMCDATASection_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMCDATASection_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMCDATASection_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMCDATASection_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMCDATASection_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMCDATASection_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMCDATASection_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMCDATASection_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMCDATASection_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMCDATASection_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMCDATASection_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMCDATASection_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMCDATASection_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMCDATASection_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMCDATASection_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMCDATASection_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMCDATASection_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMCDATASection_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMCDATASection_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMCDATASection_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMCDATASection_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMCDATASection_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMCDATASection_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMCDATASection_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMCDATASection_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMCDATASection_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMCDATASection_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMCDATASection_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMCDATASection_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMCDATASection_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMCDATASection_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMCDATASection_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMCDATASection_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
/*** IXMLDOMCharacterData methods ***/
#define IXMLDOMCDATASection_get_data(This,data) (This)->lpVtbl->get_data(This,data)
#define IXMLDOMCDATASection_put_data(This,data) (This)->lpVtbl->put_data(This,data)
#define IXMLDOMCDATASection_get_length(This,dataLength) (This)->lpVtbl->get_length(This,dataLength)
#define IXMLDOMCDATASection_substringData(This,offset,count,data) (This)->lpVtbl->substringData(This,offset,count,data)
#define IXMLDOMCDATASection_appendData(This,data) (This)->lpVtbl->appendData(This,data)
#define IXMLDOMCDATASection_insertData(This,offset,data) (This)->lpVtbl->insertData(This,offset,data)
#define IXMLDOMCDATASection_deleteData(This,offset,count) (This)->lpVtbl->deleteData(This,offset,count)
#define IXMLDOMCDATASection_replaceData(This,offset,count,data) (This)->lpVtbl->replaceData(This,offset,count,data)
/*** IXMLDOMText methods ***/
#define IXMLDOMCDATASection_splitText(This,offset,rightHandTextNode) (This)->lpVtbl->splitText(This,offset,rightHandTextNode)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMCDATASection_QueryInterface(IXMLDOMCDATASection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMCDATASection_AddRef(IXMLDOMCDATASection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMCDATASection_Release(IXMLDOMCDATASection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMCDATASection_GetTypeInfoCount(IXMLDOMCDATASection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMCDATASection_GetTypeInfo(IXMLDOMCDATASection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMCDATASection_GetIDsOfNames(IXMLDOMCDATASection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMCDATASection_Invoke(IXMLDOMCDATASection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMCDATASection_get_nodeName(IXMLDOMCDATASection* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMCDATASection_get_nodeValue(IXMLDOMCDATASection* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMCDATASection_put_nodeValue(IXMLDOMCDATASection* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMCDATASection_get_nodeType(IXMLDOMCDATASection* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMCDATASection_get_parentNode(IXMLDOMCDATASection* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMCDATASection_get_childNodes(IXMLDOMCDATASection* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMCDATASection_get_firstChild(IXMLDOMCDATASection* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMCDATASection_get_lastChild(IXMLDOMCDATASection* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMCDATASection_get_previousSibling(IXMLDOMCDATASection* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMCDATASection_get_nextSibling(IXMLDOMCDATASection* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMCDATASection_get_attributes(IXMLDOMCDATASection* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMCDATASection_insertBefore(IXMLDOMCDATASection* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMCDATASection_replaceChild(IXMLDOMCDATASection* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMCDATASection_removeChild(IXMLDOMCDATASection* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMCDATASection_appendChild(IXMLDOMCDATASection* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMCDATASection_hasChildNodes(IXMLDOMCDATASection* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMCDATASection_get_ownerDocument(IXMLDOMCDATASection* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMCDATASection_cloneNode(IXMLDOMCDATASection* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMCDATASection_get_nodeTypeString(IXMLDOMCDATASection* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMCDATASection_get_text(IXMLDOMCDATASection* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMCDATASection_put_text(IXMLDOMCDATASection* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMCDATASection_get_specified(IXMLDOMCDATASection* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMCDATASection_get_definition(IXMLDOMCDATASection* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMCDATASection_get_nodeTypedValue(IXMLDOMCDATASection* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMCDATASection_put_nodeTypedValue(IXMLDOMCDATASection* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMCDATASection_get_dataType(IXMLDOMCDATASection* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMCDATASection_put_dataType(IXMLDOMCDATASection* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMCDATASection_get_xml(IXMLDOMCDATASection* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMCDATASection_transformNode(IXMLDOMCDATASection* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMCDATASection_selectNodes(IXMLDOMCDATASection* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMCDATASection_selectSingleNode(IXMLDOMCDATASection* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMCDATASection_get_parsed(IXMLDOMCDATASection* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMCDATASection_get_namespaceURI(IXMLDOMCDATASection* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMCDATASection_get_prefix(IXMLDOMCDATASection* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMCDATASection_get_baseName(IXMLDOMCDATASection* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMCDATASection_transformNodeToObject(IXMLDOMCDATASection* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
/*** IXMLDOMCharacterData methods ***/
static inline HRESULT IXMLDOMCDATASection_get_data(IXMLDOMCDATASection* This,BSTR *data) {
    return This->lpVtbl->get_data(This,data);
}
static inline HRESULT IXMLDOMCDATASection_put_data(IXMLDOMCDATASection* This,BSTR data) {
    return This->lpVtbl->put_data(This,data);
}
static inline HRESULT IXMLDOMCDATASection_get_length(IXMLDOMCDATASection* This,LONG *dataLength) {
    return This->lpVtbl->get_length(This,dataLength);
}
static inline HRESULT IXMLDOMCDATASection_substringData(IXMLDOMCDATASection* This,LONG offset,LONG count,BSTR *data) {
    return This->lpVtbl->substringData(This,offset,count,data);
}
static inline HRESULT IXMLDOMCDATASection_appendData(IXMLDOMCDATASection* This,BSTR data) {
    return This->lpVtbl->appendData(This,data);
}
static inline HRESULT IXMLDOMCDATASection_insertData(IXMLDOMCDATASection* This,LONG offset,BSTR data) {
    return This->lpVtbl->insertData(This,offset,data);
}
static inline HRESULT IXMLDOMCDATASection_deleteData(IXMLDOMCDATASection* This,LONG offset,LONG count) {
    return This->lpVtbl->deleteData(This,offset,count);
}
static inline HRESULT IXMLDOMCDATASection_replaceData(IXMLDOMCDATASection* This,LONG offset,LONG count,BSTR data) {
    return This->lpVtbl->replaceData(This,offset,count,data);
}
/*** IXMLDOMText methods ***/
static inline HRESULT IXMLDOMCDATASection_splitText(IXMLDOMCDATASection* This,LONG offset,IXMLDOMText **rightHandTextNode) {
    return This->lpVtbl->splitText(This,offset,rightHandTextNode);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMCDATASection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMDocumentType interface
 */
#ifndef __IXMLDOMDocumentType_INTERFACE_DEFINED__
#define __IXMLDOMDocumentType_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMDocumentType, 0x2933bf8b, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf8b-7b36-11d2-b20e-00c04f983e60")
IXMLDOMDocumentType : public IXMLDOMNode
{
    virtual HRESULT STDMETHODCALLTYPE get_name(
        BSTR *rootName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_entities(
        IXMLDOMNamedNodeMap **entityMap) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_notations(
        IXMLDOMNamedNodeMap **notationMap) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMDocumentType, 0x2933bf8b, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMDocumentTypeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMDocumentType *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMDocumentType *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMDocumentType *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMDocumentType *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMDocumentType *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMDocumentType *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMDocumentType *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMDocumentType *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMDocumentType *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMDocumentType *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMDocumentType *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMDocumentType *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMDocumentType *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMDocumentType *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMDocumentType *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMDocumentType *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMDocumentType *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMDocumentType *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMDocumentType *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMDocumentType *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMDocumentType *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMDocumentType *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMDocumentType *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMDocumentType *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMDocumentType *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMDocumentType *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMDocumentType *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMDocumentType *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMDocumentType *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMDocumentType *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMDocumentType *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMDocumentType *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    /*** IXMLDOMDocumentType methods ***/
    HRESULT (STDMETHODCALLTYPE *get_name)(
        IXMLDOMDocumentType *This,
        BSTR *rootName);

    HRESULT (STDMETHODCALLTYPE *get_entities)(
        IXMLDOMDocumentType *This,
        IXMLDOMNamedNodeMap **entityMap);

    HRESULT (STDMETHODCALLTYPE *get_notations)(
        IXMLDOMDocumentType *This,
        IXMLDOMNamedNodeMap **notationMap);

    END_INTERFACE
} IXMLDOMDocumentTypeVtbl;

interface IXMLDOMDocumentType {
    CONST_VTBL IXMLDOMDocumentTypeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMDocumentType_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMDocumentType_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMDocumentType_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMDocumentType_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMDocumentType_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMDocumentType_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMDocumentType_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMDocumentType_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMDocumentType_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMDocumentType_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMDocumentType_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMDocumentType_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMDocumentType_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMDocumentType_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMDocumentType_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMDocumentType_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMDocumentType_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMDocumentType_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMDocumentType_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMDocumentType_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMDocumentType_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMDocumentType_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMDocumentType_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMDocumentType_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMDocumentType_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMDocumentType_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMDocumentType_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMDocumentType_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMDocumentType_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMDocumentType_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMDocumentType_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMDocumentType_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMDocumentType_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMDocumentType_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMDocumentType_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMDocumentType_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMDocumentType_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMDocumentType_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMDocumentType_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMDocumentType_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMDocumentType_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMDocumentType_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMDocumentType_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
/*** IXMLDOMDocumentType methods ***/
#define IXMLDOMDocumentType_get_name(This,rootName) (This)->lpVtbl->get_name(This,rootName)
#define IXMLDOMDocumentType_get_entities(This,entityMap) (This)->lpVtbl->get_entities(This,entityMap)
#define IXMLDOMDocumentType_get_notations(This,notationMap) (This)->lpVtbl->get_notations(This,notationMap)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMDocumentType_QueryInterface(IXMLDOMDocumentType* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMDocumentType_AddRef(IXMLDOMDocumentType* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMDocumentType_Release(IXMLDOMDocumentType* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMDocumentType_GetTypeInfoCount(IXMLDOMDocumentType* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMDocumentType_GetTypeInfo(IXMLDOMDocumentType* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMDocumentType_GetIDsOfNames(IXMLDOMDocumentType* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMDocumentType_Invoke(IXMLDOMDocumentType* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMDocumentType_get_nodeName(IXMLDOMDocumentType* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMDocumentType_get_nodeValue(IXMLDOMDocumentType* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMDocumentType_put_nodeValue(IXMLDOMDocumentType* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMDocumentType_get_nodeType(IXMLDOMDocumentType* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMDocumentType_get_parentNode(IXMLDOMDocumentType* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMDocumentType_get_childNodes(IXMLDOMDocumentType* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMDocumentType_get_firstChild(IXMLDOMDocumentType* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMDocumentType_get_lastChild(IXMLDOMDocumentType* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMDocumentType_get_previousSibling(IXMLDOMDocumentType* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMDocumentType_get_nextSibling(IXMLDOMDocumentType* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMDocumentType_get_attributes(IXMLDOMDocumentType* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMDocumentType_insertBefore(IXMLDOMDocumentType* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMDocumentType_replaceChild(IXMLDOMDocumentType* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMDocumentType_removeChild(IXMLDOMDocumentType* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMDocumentType_appendChild(IXMLDOMDocumentType* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMDocumentType_hasChildNodes(IXMLDOMDocumentType* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMDocumentType_get_ownerDocument(IXMLDOMDocumentType* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMDocumentType_cloneNode(IXMLDOMDocumentType* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMDocumentType_get_nodeTypeString(IXMLDOMDocumentType* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMDocumentType_get_text(IXMLDOMDocumentType* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMDocumentType_put_text(IXMLDOMDocumentType* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMDocumentType_get_specified(IXMLDOMDocumentType* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMDocumentType_get_definition(IXMLDOMDocumentType* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMDocumentType_get_nodeTypedValue(IXMLDOMDocumentType* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMDocumentType_put_nodeTypedValue(IXMLDOMDocumentType* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMDocumentType_get_dataType(IXMLDOMDocumentType* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMDocumentType_put_dataType(IXMLDOMDocumentType* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMDocumentType_get_xml(IXMLDOMDocumentType* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMDocumentType_transformNode(IXMLDOMDocumentType* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMDocumentType_selectNodes(IXMLDOMDocumentType* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMDocumentType_selectSingleNode(IXMLDOMDocumentType* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMDocumentType_get_parsed(IXMLDOMDocumentType* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMDocumentType_get_namespaceURI(IXMLDOMDocumentType* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMDocumentType_get_prefix(IXMLDOMDocumentType* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMDocumentType_get_baseName(IXMLDOMDocumentType* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMDocumentType_transformNodeToObject(IXMLDOMDocumentType* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
/*** IXMLDOMDocumentType methods ***/
static inline HRESULT IXMLDOMDocumentType_get_name(IXMLDOMDocumentType* This,BSTR *rootName) {
    return This->lpVtbl->get_name(This,rootName);
}
static inline HRESULT IXMLDOMDocumentType_get_entities(IXMLDOMDocumentType* This,IXMLDOMNamedNodeMap **entityMap) {
    return This->lpVtbl->get_entities(This,entityMap);
}
static inline HRESULT IXMLDOMDocumentType_get_notations(IXMLDOMDocumentType* This,IXMLDOMNamedNodeMap **notationMap) {
    return This->lpVtbl->get_notations(This,notationMap);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMDocumentType_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMNotation interface
 */
#ifndef __IXMLDOMNotation_INTERFACE_DEFINED__
#define __IXMLDOMNotation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMNotation, 0x2933bf8c, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf8c-7b36-11d2-b20e-00c04f983e60")
IXMLDOMNotation : public IXMLDOMNode
{
    virtual HRESULT STDMETHODCALLTYPE get_publicId(
        VARIANT *publicId) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_systemId(
        VARIANT *systemId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMNotation, 0x2933bf8c, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMNotationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMNotation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMNotation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMNotation *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMNotation *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMNotation *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMNotation *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMNotation *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMNotation *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMNotation *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMNotation *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMNotation *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMNotation *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMNotation *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMNotation *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMNotation *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMNotation *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMNotation *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMNotation *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMNotation *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMNotation *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMNotation *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMNotation *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMNotation *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMNotation *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMNotation *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMNotation *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMNotation *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMNotation *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMNotation *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMNotation *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMNotation *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMNotation *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMNotation *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMNotation *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMNotation *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMNotation *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMNotation *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMNotation *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMNotation *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMNotation *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMNotation *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMNotation *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMNotation *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    /*** IXMLDOMNotation methods ***/
    HRESULT (STDMETHODCALLTYPE *get_publicId)(
        IXMLDOMNotation *This,
        VARIANT *publicId);

    HRESULT (STDMETHODCALLTYPE *get_systemId)(
        IXMLDOMNotation *This,
        VARIANT *systemId);

    END_INTERFACE
} IXMLDOMNotationVtbl;

interface IXMLDOMNotation {
    CONST_VTBL IXMLDOMNotationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMNotation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMNotation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMNotation_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMNotation_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMNotation_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMNotation_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMNotation_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMNotation_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMNotation_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMNotation_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMNotation_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMNotation_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMNotation_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMNotation_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMNotation_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMNotation_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMNotation_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMNotation_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMNotation_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMNotation_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMNotation_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMNotation_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMNotation_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMNotation_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMNotation_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMNotation_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMNotation_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMNotation_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMNotation_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMNotation_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMNotation_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMNotation_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMNotation_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMNotation_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMNotation_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMNotation_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMNotation_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMNotation_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMNotation_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMNotation_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMNotation_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMNotation_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMNotation_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
/*** IXMLDOMNotation methods ***/
#define IXMLDOMNotation_get_publicId(This,publicId) (This)->lpVtbl->get_publicId(This,publicId)
#define IXMLDOMNotation_get_systemId(This,systemId) (This)->lpVtbl->get_systemId(This,systemId)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMNotation_QueryInterface(IXMLDOMNotation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMNotation_AddRef(IXMLDOMNotation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMNotation_Release(IXMLDOMNotation* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMNotation_GetTypeInfoCount(IXMLDOMNotation* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMNotation_GetTypeInfo(IXMLDOMNotation* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMNotation_GetIDsOfNames(IXMLDOMNotation* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMNotation_Invoke(IXMLDOMNotation* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMNotation_get_nodeName(IXMLDOMNotation* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMNotation_get_nodeValue(IXMLDOMNotation* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMNotation_put_nodeValue(IXMLDOMNotation* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMNotation_get_nodeType(IXMLDOMNotation* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMNotation_get_parentNode(IXMLDOMNotation* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMNotation_get_childNodes(IXMLDOMNotation* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMNotation_get_firstChild(IXMLDOMNotation* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMNotation_get_lastChild(IXMLDOMNotation* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMNotation_get_previousSibling(IXMLDOMNotation* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMNotation_get_nextSibling(IXMLDOMNotation* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMNotation_get_attributes(IXMLDOMNotation* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMNotation_insertBefore(IXMLDOMNotation* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMNotation_replaceChild(IXMLDOMNotation* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMNotation_removeChild(IXMLDOMNotation* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMNotation_appendChild(IXMLDOMNotation* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMNotation_hasChildNodes(IXMLDOMNotation* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMNotation_get_ownerDocument(IXMLDOMNotation* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMNotation_cloneNode(IXMLDOMNotation* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMNotation_get_nodeTypeString(IXMLDOMNotation* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMNotation_get_text(IXMLDOMNotation* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMNotation_put_text(IXMLDOMNotation* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMNotation_get_specified(IXMLDOMNotation* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMNotation_get_definition(IXMLDOMNotation* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMNotation_get_nodeTypedValue(IXMLDOMNotation* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMNotation_put_nodeTypedValue(IXMLDOMNotation* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMNotation_get_dataType(IXMLDOMNotation* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMNotation_put_dataType(IXMLDOMNotation* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMNotation_get_xml(IXMLDOMNotation* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMNotation_transformNode(IXMLDOMNotation* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMNotation_selectNodes(IXMLDOMNotation* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMNotation_selectSingleNode(IXMLDOMNotation* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMNotation_get_parsed(IXMLDOMNotation* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMNotation_get_namespaceURI(IXMLDOMNotation* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMNotation_get_prefix(IXMLDOMNotation* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMNotation_get_baseName(IXMLDOMNotation* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMNotation_transformNodeToObject(IXMLDOMNotation* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
/*** IXMLDOMNotation methods ***/
static inline HRESULT IXMLDOMNotation_get_publicId(IXMLDOMNotation* This,VARIANT *publicId) {
    return This->lpVtbl->get_publicId(This,publicId);
}
static inline HRESULT IXMLDOMNotation_get_systemId(IXMLDOMNotation* This,VARIANT *systemId) {
    return This->lpVtbl->get_systemId(This,systemId);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMNotation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMEntity interface
 */
#ifndef __IXMLDOMEntity_INTERFACE_DEFINED__
#define __IXMLDOMEntity_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMEntity, 0x2933bf8d, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf8d-7b36-11d2-b20e-00c04f983e60")
IXMLDOMEntity : public IXMLDOMNode
{
    virtual HRESULT STDMETHODCALLTYPE get_publicId(
        VARIANT *publicId) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_systemId(
        VARIANT *systemId) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_notationName(
        BSTR *name) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMEntity, 0x2933bf8d, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMEntityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMEntity *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMEntity *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMEntity *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMEntity *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMEntity *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMEntity *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMEntity *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMEntity *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMEntity *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMEntity *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMEntity *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMEntity *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMEntity *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMEntity *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMEntity *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMEntity *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMEntity *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMEntity *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMEntity *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMEntity *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMEntity *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMEntity *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMEntity *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMEntity *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMEntity *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMEntity *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMEntity *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMEntity *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMEntity *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMEntity *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMEntity *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMEntity *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMEntity *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMEntity *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMEntity *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMEntity *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMEntity *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMEntity *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMEntity *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMEntity *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMEntity *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMEntity *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMEntity *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    /*** IXMLDOMEntity methods ***/
    HRESULT (STDMETHODCALLTYPE *get_publicId)(
        IXMLDOMEntity *This,
        VARIANT *publicId);

    HRESULT (STDMETHODCALLTYPE *get_systemId)(
        IXMLDOMEntity *This,
        VARIANT *systemId);

    HRESULT (STDMETHODCALLTYPE *get_notationName)(
        IXMLDOMEntity *This,
        BSTR *name);

    END_INTERFACE
} IXMLDOMEntityVtbl;

interface IXMLDOMEntity {
    CONST_VTBL IXMLDOMEntityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMEntity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMEntity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMEntity_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMEntity_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMEntity_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMEntity_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMEntity_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMEntity_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMEntity_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMEntity_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMEntity_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMEntity_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMEntity_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMEntity_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMEntity_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMEntity_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMEntity_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMEntity_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMEntity_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMEntity_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMEntity_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMEntity_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMEntity_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMEntity_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMEntity_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMEntity_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMEntity_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMEntity_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMEntity_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMEntity_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMEntity_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMEntity_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMEntity_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMEntity_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMEntity_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMEntity_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMEntity_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMEntity_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMEntity_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMEntity_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMEntity_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMEntity_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMEntity_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
/*** IXMLDOMEntity methods ***/
#define IXMLDOMEntity_get_publicId(This,publicId) (This)->lpVtbl->get_publicId(This,publicId)
#define IXMLDOMEntity_get_systemId(This,systemId) (This)->lpVtbl->get_systemId(This,systemId)
#define IXMLDOMEntity_get_notationName(This,name) (This)->lpVtbl->get_notationName(This,name)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMEntity_QueryInterface(IXMLDOMEntity* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMEntity_AddRef(IXMLDOMEntity* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMEntity_Release(IXMLDOMEntity* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMEntity_GetTypeInfoCount(IXMLDOMEntity* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMEntity_GetTypeInfo(IXMLDOMEntity* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMEntity_GetIDsOfNames(IXMLDOMEntity* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMEntity_Invoke(IXMLDOMEntity* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMEntity_get_nodeName(IXMLDOMEntity* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMEntity_get_nodeValue(IXMLDOMEntity* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMEntity_put_nodeValue(IXMLDOMEntity* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMEntity_get_nodeType(IXMLDOMEntity* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMEntity_get_parentNode(IXMLDOMEntity* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMEntity_get_childNodes(IXMLDOMEntity* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMEntity_get_firstChild(IXMLDOMEntity* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMEntity_get_lastChild(IXMLDOMEntity* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMEntity_get_previousSibling(IXMLDOMEntity* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMEntity_get_nextSibling(IXMLDOMEntity* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMEntity_get_attributes(IXMLDOMEntity* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMEntity_insertBefore(IXMLDOMEntity* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMEntity_replaceChild(IXMLDOMEntity* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMEntity_removeChild(IXMLDOMEntity* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMEntity_appendChild(IXMLDOMEntity* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMEntity_hasChildNodes(IXMLDOMEntity* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMEntity_get_ownerDocument(IXMLDOMEntity* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMEntity_cloneNode(IXMLDOMEntity* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMEntity_get_nodeTypeString(IXMLDOMEntity* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMEntity_get_text(IXMLDOMEntity* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMEntity_put_text(IXMLDOMEntity* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMEntity_get_specified(IXMLDOMEntity* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMEntity_get_definition(IXMLDOMEntity* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMEntity_get_nodeTypedValue(IXMLDOMEntity* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMEntity_put_nodeTypedValue(IXMLDOMEntity* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMEntity_get_dataType(IXMLDOMEntity* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMEntity_put_dataType(IXMLDOMEntity* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMEntity_get_xml(IXMLDOMEntity* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMEntity_transformNode(IXMLDOMEntity* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMEntity_selectNodes(IXMLDOMEntity* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMEntity_selectSingleNode(IXMLDOMEntity* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMEntity_get_parsed(IXMLDOMEntity* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMEntity_get_namespaceURI(IXMLDOMEntity* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMEntity_get_prefix(IXMLDOMEntity* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMEntity_get_baseName(IXMLDOMEntity* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMEntity_transformNodeToObject(IXMLDOMEntity* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
/*** IXMLDOMEntity methods ***/
static inline HRESULT IXMLDOMEntity_get_publicId(IXMLDOMEntity* This,VARIANT *publicId) {
    return This->lpVtbl->get_publicId(This,publicId);
}
static inline HRESULT IXMLDOMEntity_get_systemId(IXMLDOMEntity* This,VARIANT *systemId) {
    return This->lpVtbl->get_systemId(This,systemId);
}
static inline HRESULT IXMLDOMEntity_get_notationName(IXMLDOMEntity* This,BSTR *name) {
    return This->lpVtbl->get_notationName(This,name);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMEntity_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMEntityReference interface
 */
#ifndef __IXMLDOMEntityReference_INTERFACE_DEFINED__
#define __IXMLDOMEntityReference_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMEntityReference, 0x2933bf8e, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf8e-7b36-11d2-b20e-00c04f983e60")
IXMLDOMEntityReference : public IXMLDOMNode
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMEntityReference, 0x2933bf8e, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMEntityReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMEntityReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMEntityReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMEntityReference *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMEntityReference *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMEntityReference *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMEntityReference *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMEntityReference *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMNode methods ***/
    HRESULT (STDMETHODCALLTYPE *get_nodeName)(
        IXMLDOMEntityReference *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_nodeValue)(
        IXMLDOMEntityReference *This,
        VARIANT *value);

    HRESULT (STDMETHODCALLTYPE *put_nodeValue)(
        IXMLDOMEntityReference *This,
        VARIANT value);

    HRESULT (STDMETHODCALLTYPE *get_nodeType)(
        IXMLDOMEntityReference *This,
        DOMNodeType *type);

    HRESULT (STDMETHODCALLTYPE *get_parentNode)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode **parent);

    HRESULT (STDMETHODCALLTYPE *get_childNodes)(
        IXMLDOMEntityReference *This,
        IXMLDOMNodeList **childList);

    HRESULT (STDMETHODCALLTYPE *get_firstChild)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode **firstChild);

    HRESULT (STDMETHODCALLTYPE *get_lastChild)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode **lastChild);

    HRESULT (STDMETHODCALLTYPE *get_previousSibling)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode **previousSibling);

    HRESULT (STDMETHODCALLTYPE *get_nextSibling)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode **nextSibling);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLDOMEntityReference *This,
        IXMLDOMNamedNodeMap **attributeMap);

    HRESULT (STDMETHODCALLTYPE *insertBefore)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode *newChild,
        VARIANT refChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *replaceChild)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode *oldChild,
        IXMLDOMNode **outOldChild);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode *childNode,
        IXMLDOMNode **oldChild);

    HRESULT (STDMETHODCALLTYPE *appendChild)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode *newChild,
        IXMLDOMNode **outNewChild);

    HRESULT (STDMETHODCALLTYPE *hasChildNodes)(
        IXMLDOMEntityReference *This,
        VARIANT_BOOL *hasChild);

    HRESULT (STDMETHODCALLTYPE *get_ownerDocument)(
        IXMLDOMEntityReference *This,
        IXMLDOMDocument **DOMDocument);

    HRESULT (STDMETHODCALLTYPE *cloneNode)(
        IXMLDOMEntityReference *This,
        VARIANT_BOOL deep,
        IXMLDOMNode **cloneRoot);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypeString)(
        IXMLDOMEntityReference *This,
        BSTR *nodeType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLDOMEntityReference *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLDOMEntityReference *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_specified)(
        IXMLDOMEntityReference *This,
        VARIANT_BOOL *isSpecified);

    HRESULT (STDMETHODCALLTYPE *get_definition)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode **definitionNode);

    HRESULT (STDMETHODCALLTYPE *get_nodeTypedValue)(
        IXMLDOMEntityReference *This,
        VARIANT *typedValue);

    HRESULT (STDMETHODCALLTYPE *put_nodeTypedValue)(
        IXMLDOMEntityReference *This,
        VARIANT typedValue);

    HRESULT (STDMETHODCALLTYPE *get_dataType)(
        IXMLDOMEntityReference *This,
        VARIANT *dataTypeName);

    HRESULT (STDMETHODCALLTYPE *put_dataType)(
        IXMLDOMEntityReference *This,
        BSTR dataTypeName);

    HRESULT (STDMETHODCALLTYPE *get_xml)(
        IXMLDOMEntityReference *This,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *transformNode)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode *styleSheet,
        BSTR *xmlString);

    HRESULT (STDMETHODCALLTYPE *selectNodes)(
        IXMLDOMEntityReference *This,
        BSTR queryString,
        IXMLDOMNodeList **resultList);

    HRESULT (STDMETHODCALLTYPE *selectSingleNode)(
        IXMLDOMEntityReference *This,
        BSTR queryString,
        IXMLDOMNode **resultNode);

    HRESULT (STDMETHODCALLTYPE *get_parsed)(
        IXMLDOMEntityReference *This,
        VARIANT_BOOL *isParsed);

    HRESULT (STDMETHODCALLTYPE *get_namespaceURI)(
        IXMLDOMEntityReference *This,
        BSTR *namespaceURI);

    HRESULT (STDMETHODCALLTYPE *get_prefix)(
        IXMLDOMEntityReference *This,
        BSTR *prefixString);

    HRESULT (STDMETHODCALLTYPE *get_baseName)(
        IXMLDOMEntityReference *This,
        BSTR *nameString);

    HRESULT (STDMETHODCALLTYPE *transformNodeToObject)(
        IXMLDOMEntityReference *This,
        IXMLDOMNode *stylesheet,
        VARIANT outputObject);

    END_INTERFACE
} IXMLDOMEntityReferenceVtbl;

interface IXMLDOMEntityReference {
    CONST_VTBL IXMLDOMEntityReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMEntityReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMEntityReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMEntityReference_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMEntityReference_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMEntityReference_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMEntityReference_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMEntityReference_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMNode methods ***/
#define IXMLDOMEntityReference_get_nodeName(This,name) (This)->lpVtbl->get_nodeName(This,name)
#define IXMLDOMEntityReference_get_nodeValue(This,value) (This)->lpVtbl->get_nodeValue(This,value)
#define IXMLDOMEntityReference_put_nodeValue(This,value) (This)->lpVtbl->put_nodeValue(This,value)
#define IXMLDOMEntityReference_get_nodeType(This,type) (This)->lpVtbl->get_nodeType(This,type)
#define IXMLDOMEntityReference_get_parentNode(This,parent) (This)->lpVtbl->get_parentNode(This,parent)
#define IXMLDOMEntityReference_get_childNodes(This,childList) (This)->lpVtbl->get_childNodes(This,childList)
#define IXMLDOMEntityReference_get_firstChild(This,firstChild) (This)->lpVtbl->get_firstChild(This,firstChild)
#define IXMLDOMEntityReference_get_lastChild(This,lastChild) (This)->lpVtbl->get_lastChild(This,lastChild)
#define IXMLDOMEntityReference_get_previousSibling(This,previousSibling) (This)->lpVtbl->get_previousSibling(This,previousSibling)
#define IXMLDOMEntityReference_get_nextSibling(This,nextSibling) (This)->lpVtbl->get_nextSibling(This,nextSibling)
#define IXMLDOMEntityReference_get_attributes(This,attributeMap) (This)->lpVtbl->get_attributes(This,attributeMap)
#define IXMLDOMEntityReference_insertBefore(This,newChild,refChild,outNewChild) (This)->lpVtbl->insertBefore(This,newChild,refChild,outNewChild)
#define IXMLDOMEntityReference_replaceChild(This,newChild,oldChild,outOldChild) (This)->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild)
#define IXMLDOMEntityReference_removeChild(This,childNode,oldChild) (This)->lpVtbl->removeChild(This,childNode,oldChild)
#define IXMLDOMEntityReference_appendChild(This,newChild,outNewChild) (This)->lpVtbl->appendChild(This,newChild,outNewChild)
#define IXMLDOMEntityReference_hasChildNodes(This,hasChild) (This)->lpVtbl->hasChildNodes(This,hasChild)
#define IXMLDOMEntityReference_get_ownerDocument(This,DOMDocument) (This)->lpVtbl->get_ownerDocument(This,DOMDocument)
#define IXMLDOMEntityReference_cloneNode(This,deep,cloneRoot) (This)->lpVtbl->cloneNode(This,deep,cloneRoot)
#define IXMLDOMEntityReference_get_nodeTypeString(This,nodeType) (This)->lpVtbl->get_nodeTypeString(This,nodeType)
#define IXMLDOMEntityReference_get_text(This,text) (This)->lpVtbl->get_text(This,text)
#define IXMLDOMEntityReference_put_text(This,text) (This)->lpVtbl->put_text(This,text)
#define IXMLDOMEntityReference_get_specified(This,isSpecified) (This)->lpVtbl->get_specified(This,isSpecified)
#define IXMLDOMEntityReference_get_definition(This,definitionNode) (This)->lpVtbl->get_definition(This,definitionNode)
#define IXMLDOMEntityReference_get_nodeTypedValue(This,typedValue) (This)->lpVtbl->get_nodeTypedValue(This,typedValue)
#define IXMLDOMEntityReference_put_nodeTypedValue(This,typedValue) (This)->lpVtbl->put_nodeTypedValue(This,typedValue)
#define IXMLDOMEntityReference_get_dataType(This,dataTypeName) (This)->lpVtbl->get_dataType(This,dataTypeName)
#define IXMLDOMEntityReference_put_dataType(This,dataTypeName) (This)->lpVtbl->put_dataType(This,dataTypeName)
#define IXMLDOMEntityReference_get_xml(This,xmlString) (This)->lpVtbl->get_xml(This,xmlString)
#define IXMLDOMEntityReference_transformNode(This,styleSheet,xmlString) (This)->lpVtbl->transformNode(This,styleSheet,xmlString)
#define IXMLDOMEntityReference_selectNodes(This,queryString,resultList) (This)->lpVtbl->selectNodes(This,queryString,resultList)
#define IXMLDOMEntityReference_selectSingleNode(This,queryString,resultNode) (This)->lpVtbl->selectSingleNode(This,queryString,resultNode)
#define IXMLDOMEntityReference_get_parsed(This,isParsed) (This)->lpVtbl->get_parsed(This,isParsed)
#define IXMLDOMEntityReference_get_namespaceURI(This,namespaceURI) (This)->lpVtbl->get_namespaceURI(This,namespaceURI)
#define IXMLDOMEntityReference_get_prefix(This,prefixString) (This)->lpVtbl->get_prefix(This,prefixString)
#define IXMLDOMEntityReference_get_baseName(This,nameString) (This)->lpVtbl->get_baseName(This,nameString)
#define IXMLDOMEntityReference_transformNodeToObject(This,stylesheet,outputObject) (This)->lpVtbl->transformNodeToObject(This,stylesheet,outputObject)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMEntityReference_QueryInterface(IXMLDOMEntityReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMEntityReference_AddRef(IXMLDOMEntityReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMEntityReference_Release(IXMLDOMEntityReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMEntityReference_GetTypeInfoCount(IXMLDOMEntityReference* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMEntityReference_GetTypeInfo(IXMLDOMEntityReference* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMEntityReference_GetIDsOfNames(IXMLDOMEntityReference* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMEntityReference_Invoke(IXMLDOMEntityReference* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMNode methods ***/
static inline HRESULT IXMLDOMEntityReference_get_nodeName(IXMLDOMEntityReference* This,BSTR *name) {
    return This->lpVtbl->get_nodeName(This,name);
}
static inline HRESULT IXMLDOMEntityReference_get_nodeValue(IXMLDOMEntityReference* This,VARIANT *value) {
    return This->lpVtbl->get_nodeValue(This,value);
}
static inline HRESULT IXMLDOMEntityReference_put_nodeValue(IXMLDOMEntityReference* This,VARIANT value) {
    return This->lpVtbl->put_nodeValue(This,value);
}
static inline HRESULT IXMLDOMEntityReference_get_nodeType(IXMLDOMEntityReference* This,DOMNodeType *type) {
    return This->lpVtbl->get_nodeType(This,type);
}
static inline HRESULT IXMLDOMEntityReference_get_parentNode(IXMLDOMEntityReference* This,IXMLDOMNode **parent) {
    return This->lpVtbl->get_parentNode(This,parent);
}
static inline HRESULT IXMLDOMEntityReference_get_childNodes(IXMLDOMEntityReference* This,IXMLDOMNodeList **childList) {
    return This->lpVtbl->get_childNodes(This,childList);
}
static inline HRESULT IXMLDOMEntityReference_get_firstChild(IXMLDOMEntityReference* This,IXMLDOMNode **firstChild) {
    return This->lpVtbl->get_firstChild(This,firstChild);
}
static inline HRESULT IXMLDOMEntityReference_get_lastChild(IXMLDOMEntityReference* This,IXMLDOMNode **lastChild) {
    return This->lpVtbl->get_lastChild(This,lastChild);
}
static inline HRESULT IXMLDOMEntityReference_get_previousSibling(IXMLDOMEntityReference* This,IXMLDOMNode **previousSibling) {
    return This->lpVtbl->get_previousSibling(This,previousSibling);
}
static inline HRESULT IXMLDOMEntityReference_get_nextSibling(IXMLDOMEntityReference* This,IXMLDOMNode **nextSibling) {
    return This->lpVtbl->get_nextSibling(This,nextSibling);
}
static inline HRESULT IXMLDOMEntityReference_get_attributes(IXMLDOMEntityReference* This,IXMLDOMNamedNodeMap **attributeMap) {
    return This->lpVtbl->get_attributes(This,attributeMap);
}
static inline HRESULT IXMLDOMEntityReference_insertBefore(IXMLDOMEntityReference* This,IXMLDOMNode *newChild,VARIANT refChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->insertBefore(This,newChild,refChild,outNewChild);
}
static inline HRESULT IXMLDOMEntityReference_replaceChild(IXMLDOMEntityReference* This,IXMLDOMNode *newChild,IXMLDOMNode *oldChild,IXMLDOMNode **outOldChild) {
    return This->lpVtbl->replaceChild(This,newChild,oldChild,outOldChild);
}
static inline HRESULT IXMLDOMEntityReference_removeChild(IXMLDOMEntityReference* This,IXMLDOMNode *childNode,IXMLDOMNode **oldChild) {
    return This->lpVtbl->removeChild(This,childNode,oldChild);
}
static inline HRESULT IXMLDOMEntityReference_appendChild(IXMLDOMEntityReference* This,IXMLDOMNode *newChild,IXMLDOMNode **outNewChild) {
    return This->lpVtbl->appendChild(This,newChild,outNewChild);
}
static inline HRESULT IXMLDOMEntityReference_hasChildNodes(IXMLDOMEntityReference* This,VARIANT_BOOL *hasChild) {
    return This->lpVtbl->hasChildNodes(This,hasChild);
}
static inline HRESULT IXMLDOMEntityReference_get_ownerDocument(IXMLDOMEntityReference* This,IXMLDOMDocument **DOMDocument) {
    return This->lpVtbl->get_ownerDocument(This,DOMDocument);
}
static inline HRESULT IXMLDOMEntityReference_cloneNode(IXMLDOMEntityReference* This,VARIANT_BOOL deep,IXMLDOMNode **cloneRoot) {
    return This->lpVtbl->cloneNode(This,deep,cloneRoot);
}
static inline HRESULT IXMLDOMEntityReference_get_nodeTypeString(IXMLDOMEntityReference* This,BSTR *nodeType) {
    return This->lpVtbl->get_nodeTypeString(This,nodeType);
}
static inline HRESULT IXMLDOMEntityReference_get_text(IXMLDOMEntityReference* This,BSTR *text) {
    return This->lpVtbl->get_text(This,text);
}
static inline HRESULT IXMLDOMEntityReference_put_text(IXMLDOMEntityReference* This,BSTR text) {
    return This->lpVtbl->put_text(This,text);
}
static inline HRESULT IXMLDOMEntityReference_get_specified(IXMLDOMEntityReference* This,VARIANT_BOOL *isSpecified) {
    return This->lpVtbl->get_specified(This,isSpecified);
}
static inline HRESULT IXMLDOMEntityReference_get_definition(IXMLDOMEntityReference* This,IXMLDOMNode **definitionNode) {
    return This->lpVtbl->get_definition(This,definitionNode);
}
static inline HRESULT IXMLDOMEntityReference_get_nodeTypedValue(IXMLDOMEntityReference* This,VARIANT *typedValue) {
    return This->lpVtbl->get_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMEntityReference_put_nodeTypedValue(IXMLDOMEntityReference* This,VARIANT typedValue) {
    return This->lpVtbl->put_nodeTypedValue(This,typedValue);
}
static inline HRESULT IXMLDOMEntityReference_get_dataType(IXMLDOMEntityReference* This,VARIANT *dataTypeName) {
    return This->lpVtbl->get_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMEntityReference_put_dataType(IXMLDOMEntityReference* This,BSTR dataTypeName) {
    return This->lpVtbl->put_dataType(This,dataTypeName);
}
static inline HRESULT IXMLDOMEntityReference_get_xml(IXMLDOMEntityReference* This,BSTR *xmlString) {
    return This->lpVtbl->get_xml(This,xmlString);
}
static inline HRESULT IXMLDOMEntityReference_transformNode(IXMLDOMEntityReference* This,IXMLDOMNode *styleSheet,BSTR *xmlString) {
    return This->lpVtbl->transformNode(This,styleSheet,xmlString);
}
static inline HRESULT IXMLDOMEntityReference_selectNodes(IXMLDOMEntityReference* This,BSTR queryString,IXMLDOMNodeList **resultList) {
    return This->lpVtbl->selectNodes(This,queryString,resultList);
}
static inline HRESULT IXMLDOMEntityReference_selectSingleNode(IXMLDOMEntityReference* This,BSTR queryString,IXMLDOMNode **resultNode) {
    return This->lpVtbl->selectSingleNode(This,queryString,resultNode);
}
static inline HRESULT IXMLDOMEntityReference_get_parsed(IXMLDOMEntityReference* This,VARIANT_BOOL *isParsed) {
    return This->lpVtbl->get_parsed(This,isParsed);
}
static inline HRESULT IXMLDOMEntityReference_get_namespaceURI(IXMLDOMEntityReference* This,BSTR *namespaceURI) {
    return This->lpVtbl->get_namespaceURI(This,namespaceURI);
}
static inline HRESULT IXMLDOMEntityReference_get_prefix(IXMLDOMEntityReference* This,BSTR *prefixString) {
    return This->lpVtbl->get_prefix(This,prefixString);
}
static inline HRESULT IXMLDOMEntityReference_get_baseName(IXMLDOMEntityReference* This,BSTR *nameString) {
    return This->lpVtbl->get_baseName(This,nameString);
}
static inline HRESULT IXMLDOMEntityReference_transformNodeToObject(IXMLDOMEntityReference* This,IXMLDOMNode *stylesheet,VARIANT outputObject) {
    return This->lpVtbl->transformNodeToObject(This,stylesheet,outputObject);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMEntityReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMImplementation interface
 */
#ifndef __IXMLDOMImplementation_INTERFACE_DEFINED__
#define __IXMLDOMImplementation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMImplementation, 0x2933bf8f, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2933bf8f-7b36-11d2-b20e-00c04f983e60")
IXMLDOMImplementation : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE hasFeature(
        BSTR feature,
        BSTR version,
        VARIANT_BOOL *pbool) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMImplementation, 0x2933bf8f, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#else
typedef struct IXMLDOMImplementationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMImplementation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMImplementation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMImplementation *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMImplementation *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMImplementation *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMImplementation *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMImplementation *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMImplementation methods ***/
    HRESULT (STDMETHODCALLTYPE *hasFeature)(
        IXMLDOMImplementation *This,
        BSTR feature,
        BSTR version,
        VARIANT_BOOL *pbool);

    END_INTERFACE
} IXMLDOMImplementationVtbl;

interface IXMLDOMImplementation {
    CONST_VTBL IXMLDOMImplementationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMImplementation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMImplementation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMImplementation_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMImplementation_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMImplementation_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMImplementation_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMImplementation_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMImplementation methods ***/
#define IXMLDOMImplementation_hasFeature(This,feature,version,pbool) (This)->lpVtbl->hasFeature(This,feature,version,pbool)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMImplementation_QueryInterface(IXMLDOMImplementation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMImplementation_AddRef(IXMLDOMImplementation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMImplementation_Release(IXMLDOMImplementation* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMImplementation_GetTypeInfoCount(IXMLDOMImplementation* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMImplementation_GetTypeInfo(IXMLDOMImplementation* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMImplementation_GetIDsOfNames(IXMLDOMImplementation* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMImplementation_Invoke(IXMLDOMImplementation* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMImplementation methods ***/
static inline HRESULT IXMLDOMImplementation_hasFeature(IXMLDOMImplementation* This,BSTR feature,BSTR version,VARIANT_BOOL *pbool) {
    return This->lpVtbl->hasFeature(This,feature,version,pbool);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMImplementation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDOMParseError interface
 */
#ifndef __IXMLDOMParseError_INTERFACE_DEFINED__
#define __IXMLDOMParseError_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDOMParseError, 0x3efaa426, 0x272f, 0x11d2, 0x83,0x6f, 0x00,0x00,0xf8,0x7a,0x77,0x82);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3efaa426-272f-11d2-836f-0000f87a7782")
IXMLDOMParseError : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_errorCode(
        LONG *errCode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_url(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_reason(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_srcText(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_line(
        LONG *lineNo) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_linepos(
        LONG *linePos) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_filepos(
        LONG *filePos) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDOMParseError, 0x3efaa426, 0x272f, 0x11d2, 0x83,0x6f, 0x00,0x00,0xf8,0x7a,0x77,0x82)
#endif
#else
typedef struct IXMLDOMParseErrorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDOMParseError *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDOMParseError *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDOMParseError *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDOMParseError *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDOMParseError *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDOMParseError *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDOMParseError *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDOMParseError methods ***/
    HRESULT (STDMETHODCALLTYPE *get_errorCode)(
        IXMLDOMParseError *This,
        LONG *errCode);

    HRESULT (STDMETHODCALLTYPE *get_url)(
        IXMLDOMParseError *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_reason)(
        IXMLDOMParseError *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_srcText)(
        IXMLDOMParseError *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_line)(
        IXMLDOMParseError *This,
        LONG *lineNo);

    HRESULT (STDMETHODCALLTYPE *get_linepos)(
        IXMLDOMParseError *This,
        LONG *linePos);

    HRESULT (STDMETHODCALLTYPE *get_filepos)(
        IXMLDOMParseError *This,
        LONG *filePos);

    END_INTERFACE
} IXMLDOMParseErrorVtbl;

interface IXMLDOMParseError {
    CONST_VTBL IXMLDOMParseErrorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDOMParseError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDOMParseError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDOMParseError_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDOMParseError_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDOMParseError_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDOMParseError_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDOMParseError_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDOMParseError methods ***/
#define IXMLDOMParseError_get_errorCode(This,errCode) (This)->lpVtbl->get_errorCode(This,errCode)
#define IXMLDOMParseError_get_url(This,p) (This)->lpVtbl->get_url(This,p)
#define IXMLDOMParseError_get_reason(This,p) (This)->lpVtbl->get_reason(This,p)
#define IXMLDOMParseError_get_srcText(This,p) (This)->lpVtbl->get_srcText(This,p)
#define IXMLDOMParseError_get_line(This,lineNo) (This)->lpVtbl->get_line(This,lineNo)
#define IXMLDOMParseError_get_linepos(This,linePos) (This)->lpVtbl->get_linepos(This,linePos)
#define IXMLDOMParseError_get_filepos(This,filePos) (This)->lpVtbl->get_filepos(This,filePos)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDOMParseError_QueryInterface(IXMLDOMParseError* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDOMParseError_AddRef(IXMLDOMParseError* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDOMParseError_Release(IXMLDOMParseError* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDOMParseError_GetTypeInfoCount(IXMLDOMParseError* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDOMParseError_GetTypeInfo(IXMLDOMParseError* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDOMParseError_GetIDsOfNames(IXMLDOMParseError* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDOMParseError_Invoke(IXMLDOMParseError* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDOMParseError methods ***/
static inline HRESULT IXMLDOMParseError_get_errorCode(IXMLDOMParseError* This,LONG *errCode) {
    return This->lpVtbl->get_errorCode(This,errCode);
}
static inline HRESULT IXMLDOMParseError_get_url(IXMLDOMParseError* This,BSTR *p) {
    return This->lpVtbl->get_url(This,p);
}
static inline HRESULT IXMLDOMParseError_get_reason(IXMLDOMParseError* This,BSTR *p) {
    return This->lpVtbl->get_reason(This,p);
}
static inline HRESULT IXMLDOMParseError_get_srcText(IXMLDOMParseError* This,BSTR *p) {
    return This->lpVtbl->get_srcText(This,p);
}
static inline HRESULT IXMLDOMParseError_get_line(IXMLDOMParseError* This,LONG *lineNo) {
    return This->lpVtbl->get_line(This,lineNo);
}
static inline HRESULT IXMLDOMParseError_get_linepos(IXMLDOMParseError* This,LONG *linePos) {
    return This->lpVtbl->get_linepos(This,linePos);
}
static inline HRESULT IXMLDOMParseError_get_filepos(IXMLDOMParseError* This,LONG *filePos) {
    return This->lpVtbl->get_filepos(This,filePos);
}
#endif
#endif

#endif


#endif  /* __IXMLDOMParseError_INTERFACE_DEFINED__ */

/*****************************************************************************
 * XMLDOMDocumentEvents dispinterface
 */
#ifndef __XMLDOMDocumentEvents_DISPINTERFACE_DEFINED__
#define __XMLDOMDocumentEvents_DISPINTERFACE_DEFINED__

DEFINE_GUID(DIID_XMLDOMDocumentEvents, 0x3efaa427, 0x272f, 0x11d2, 0x83,0x6f, 0x00,0x00,0xf8,0x7a,0x77,0x82);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3efaa427-272f-11d2-836f-0000f87a7782")
XMLDOMDocumentEvents : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(XMLDOMDocumentEvents, 0x3efaa427, 0x272f, 0x11d2, 0x83,0x6f, 0x00,0x00,0xf8,0x7a,0x77,0x82)
#endif
#else
typedef struct XMLDOMDocumentEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        XMLDOMDocumentEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        XMLDOMDocumentEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        XMLDOMDocumentEvents *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        XMLDOMDocumentEvents *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        XMLDOMDocumentEvents *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        XMLDOMDocumentEvents *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        XMLDOMDocumentEvents *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} XMLDOMDocumentEventsVtbl;

interface XMLDOMDocumentEvents {
    CONST_VTBL XMLDOMDocumentEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define XMLDOMDocumentEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define XMLDOMDocumentEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define XMLDOMDocumentEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define XMLDOMDocumentEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define XMLDOMDocumentEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define XMLDOMDocumentEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define XMLDOMDocumentEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT XMLDOMDocumentEvents_QueryInterface(XMLDOMDocumentEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG XMLDOMDocumentEvents_AddRef(XMLDOMDocumentEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG XMLDOMDocumentEvents_Release(XMLDOMDocumentEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT XMLDOMDocumentEvents_GetTypeInfoCount(XMLDOMDocumentEvents* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT XMLDOMDocumentEvents_GetTypeInfo(XMLDOMDocumentEvents* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT XMLDOMDocumentEvents_GetIDsOfNames(XMLDOMDocumentEvents* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT XMLDOMDocumentEvents_Invoke(XMLDOMDocumentEvents* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif

#endif  /* __XMLDOMDocumentEvents_DISPINTERFACE_DEFINED__ */

/*****************************************************************************
 * DOMDocument coclass
 */

DEFINE_GUID(CLSID_DOMDocument, 0x2933bf90, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);

#ifdef __cplusplus
class DECLSPEC_UUID("2933bf90-7b36-11d2-b20e-00c04f983e60") DOMDocument;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DOMDocument, 0x2933bf90, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#endif

/*****************************************************************************
 * DOMFreeThreadedDocument coclass
 */

DEFINE_GUID(CLSID_DOMFreeThreadedDocument, 0x2933bf91, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60);

#ifdef __cplusplus
class DECLSPEC_UUID("2933bf91-7b36-11d2-b20e-00c04f983e60") DOMFreeThreadedDocument;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DOMFreeThreadedDocument, 0x2933bf91, 0x7b36, 0x11d2, 0xb2,0x0e, 0x00,0xc0,0x4f,0x98,0x3e,0x60)
#endif
#endif

/*****************************************************************************
 * IXMLHttpRequest interface
 */
#ifndef __IXMLHttpRequest_INTERFACE_DEFINED__
#define __IXMLHttpRequest_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLHttpRequest, 0xed8c108d, 0x4349, 0x11d2, 0x91,0xa4, 0x00,0xc0,0x4f,0x79,0x69,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ed8c108d-4349-11d2-91a4-00c04f7969e8")
IXMLHttpRequest : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE open(
        BSTR bstrMethod,
        BSTR bstrUrl,
        VARIANT varAsync,
        VARIANT varUser,
        VARIANT varPassword) = 0;

    virtual HRESULT STDMETHODCALLTYPE setRequestHeader(
        BSTR bstrHeader,
        BSTR bstrValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE getResponseHeader(
        BSTR bstrHeader,
        BSTR *pbstrValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE getAllResponseHeaders(
        BSTR *pbstrHeaders) = 0;

    virtual HRESULT STDMETHODCALLTYPE send(
        VARIANT varBody) = 0;

    virtual HRESULT STDMETHODCALLTYPE abort(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_status(
        LONG *plStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_statusText(
        BSTR *bstrStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_responseXML(
        IDispatch **ppBody) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_responseText(
        BSTR *pbstrBody) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_responseBody(
        VARIANT *pvarBody) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_responseStream(
        VARIANT *pvarBody) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_readyState(
        LONG *plState) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_onreadystatechange(
        IDispatch *pReadyStateSink) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLHttpRequest, 0xed8c108d, 0x4349, 0x11d2, 0x91,0xa4, 0x00,0xc0,0x4f,0x79,0x69,0xe8)
#endif
#else
typedef struct IXMLHttpRequestVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLHttpRequest *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLHttpRequest *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLHttpRequest *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLHttpRequest *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLHttpRequest *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLHttpRequest *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLHttpRequest *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLHttpRequest methods ***/
    HRESULT (STDMETHODCALLTYPE *open)(
        IXMLHttpRequest *This,
        BSTR bstrMethod,
        BSTR bstrUrl,
        VARIANT varAsync,
        VARIANT varUser,
        VARIANT varPassword);

    HRESULT (STDMETHODCALLTYPE *setRequestHeader)(
        IXMLHttpRequest *This,
        BSTR bstrHeader,
        BSTR bstrValue);

    HRESULT (STDMETHODCALLTYPE *getResponseHeader)(
        IXMLHttpRequest *This,
        BSTR bstrHeader,
        BSTR *pbstrValue);

    HRESULT (STDMETHODCALLTYPE *getAllResponseHeaders)(
        IXMLHttpRequest *This,
        BSTR *pbstrHeaders);

    HRESULT (STDMETHODCALLTYPE *send)(
        IXMLHttpRequest *This,
        VARIANT varBody);

    HRESULT (STDMETHODCALLTYPE *abort)(
        IXMLHttpRequest *This);

    HRESULT (STDMETHODCALLTYPE *get_status)(
        IXMLHttpRequest *This,
        LONG *plStatus);

    HRESULT (STDMETHODCALLTYPE *get_statusText)(
        IXMLHttpRequest *This,
        BSTR *bstrStatus);

    HRESULT (STDMETHODCALLTYPE *get_responseXML)(
        IXMLHttpRequest *This,
        IDispatch **ppBody);

    HRESULT (STDMETHODCALLTYPE *get_responseText)(
        IXMLHttpRequest *This,
        BSTR *pbstrBody);

    HRESULT (STDMETHODCALLTYPE *get_responseBody)(
        IXMLHttpRequest *This,
        VARIANT *pvarBody);

    HRESULT (STDMETHODCALLTYPE *get_responseStream)(
        IXMLHttpRequest *This,
        VARIANT *pvarBody);

    HRESULT (STDMETHODCALLTYPE *get_readyState)(
        IXMLHttpRequest *This,
        LONG *plState);

    HRESULT (STDMETHODCALLTYPE *put_onreadystatechange)(
        IXMLHttpRequest *This,
        IDispatch *pReadyStateSink);

    END_INTERFACE
} IXMLHttpRequestVtbl;

interface IXMLHttpRequest {
    CONST_VTBL IXMLHttpRequestVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLHttpRequest_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLHttpRequest_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLHttpRequest_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLHttpRequest_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLHttpRequest_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLHttpRequest_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLHttpRequest_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLHttpRequest methods ***/
#define IXMLHttpRequest_open(This,bstrMethod,bstrUrl,varAsync,varUser,varPassword) (This)->lpVtbl->open(This,bstrMethod,bstrUrl,varAsync,varUser,varPassword)
#define IXMLHttpRequest_setRequestHeader(This,bstrHeader,bstrValue) (This)->lpVtbl->setRequestHeader(This,bstrHeader,bstrValue)
#define IXMLHttpRequest_getResponseHeader(This,bstrHeader,pbstrValue) (This)->lpVtbl->getResponseHeader(This,bstrHeader,pbstrValue)
#define IXMLHttpRequest_getAllResponseHeaders(This,pbstrHeaders) (This)->lpVtbl->getAllResponseHeaders(This,pbstrHeaders)
#define IXMLHttpRequest_send(This,varBody) (This)->lpVtbl->send(This,varBody)
#define IXMLHttpRequest_abort(This) (This)->lpVtbl->abort(This)
#define IXMLHttpRequest_get_status(This,plStatus) (This)->lpVtbl->get_status(This,plStatus)
#define IXMLHttpRequest_get_statusText(This,bstrStatus) (This)->lpVtbl->get_statusText(This,bstrStatus)
#define IXMLHttpRequest_get_responseXML(This,ppBody) (This)->lpVtbl->get_responseXML(This,ppBody)
#define IXMLHttpRequest_get_responseText(This,pbstrBody) (This)->lpVtbl->get_responseText(This,pbstrBody)
#define IXMLHttpRequest_get_responseBody(This,pvarBody) (This)->lpVtbl->get_responseBody(This,pvarBody)
#define IXMLHttpRequest_get_responseStream(This,pvarBody) (This)->lpVtbl->get_responseStream(This,pvarBody)
#define IXMLHttpRequest_get_readyState(This,plState) (This)->lpVtbl->get_readyState(This,plState)
#define IXMLHttpRequest_put_onreadystatechange(This,pReadyStateSink) (This)->lpVtbl->put_onreadystatechange(This,pReadyStateSink)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLHttpRequest_QueryInterface(IXMLHttpRequest* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLHttpRequest_AddRef(IXMLHttpRequest* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLHttpRequest_Release(IXMLHttpRequest* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLHttpRequest_GetTypeInfoCount(IXMLHttpRequest* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLHttpRequest_GetTypeInfo(IXMLHttpRequest* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLHttpRequest_GetIDsOfNames(IXMLHttpRequest* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLHttpRequest_Invoke(IXMLHttpRequest* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLHttpRequest methods ***/
static inline HRESULT IXMLHttpRequest_open(IXMLHttpRequest* This,BSTR bstrMethod,BSTR bstrUrl,VARIANT varAsync,VARIANT varUser,VARIANT varPassword) {
    return This->lpVtbl->open(This,bstrMethod,bstrUrl,varAsync,varUser,varPassword);
}
static inline HRESULT IXMLHttpRequest_setRequestHeader(IXMLHttpRequest* This,BSTR bstrHeader,BSTR bstrValue) {
    return This->lpVtbl->setRequestHeader(This,bstrHeader,bstrValue);
}
static inline HRESULT IXMLHttpRequest_getResponseHeader(IXMLHttpRequest* This,BSTR bstrHeader,BSTR *pbstrValue) {
    return This->lpVtbl->getResponseHeader(This,bstrHeader,pbstrValue);
}
static inline HRESULT IXMLHttpRequest_getAllResponseHeaders(IXMLHttpRequest* This,BSTR *pbstrHeaders) {
    return This->lpVtbl->getAllResponseHeaders(This,pbstrHeaders);
}
static inline HRESULT IXMLHttpRequest_send(IXMLHttpRequest* This,VARIANT varBody) {
    return This->lpVtbl->send(This,varBody);
}
static inline HRESULT IXMLHttpRequest_abort(IXMLHttpRequest* This) {
    return This->lpVtbl->abort(This);
}
static inline HRESULT IXMLHttpRequest_get_status(IXMLHttpRequest* This,LONG *plStatus) {
    return This->lpVtbl->get_status(This,plStatus);
}
static inline HRESULT IXMLHttpRequest_get_statusText(IXMLHttpRequest* This,BSTR *bstrStatus) {
    return This->lpVtbl->get_statusText(This,bstrStatus);
}
static inline HRESULT IXMLHttpRequest_get_responseXML(IXMLHttpRequest* This,IDispatch **ppBody) {
    return This->lpVtbl->get_responseXML(This,ppBody);
}
static inline HRESULT IXMLHttpRequest_get_responseText(IXMLHttpRequest* This,BSTR *pbstrBody) {
    return This->lpVtbl->get_responseText(This,pbstrBody);
}
static inline HRESULT IXMLHttpRequest_get_responseBody(IXMLHttpRequest* This,VARIANT *pvarBody) {
    return This->lpVtbl->get_responseBody(This,pvarBody);
}
static inline HRESULT IXMLHttpRequest_get_responseStream(IXMLHttpRequest* This,VARIANT *pvarBody) {
    return This->lpVtbl->get_responseStream(This,pvarBody);
}
static inline HRESULT IXMLHttpRequest_get_readyState(IXMLHttpRequest* This,LONG *plState) {
    return This->lpVtbl->get_readyState(This,plState);
}
static inline HRESULT IXMLHttpRequest_put_onreadystatechange(IXMLHttpRequest* This,IDispatch *pReadyStateSink) {
    return This->lpVtbl->put_onreadystatechange(This,pReadyStateSink);
}
#endif
#endif

#endif


#endif  /* __IXMLHttpRequest_INTERFACE_DEFINED__ */

/*****************************************************************************
 * XMLHTTPRequest coclass
 */

DEFINE_GUID(CLSID_XMLHTTPRequest, 0xed8c108e, 0x4349, 0x11d2, 0x91,0xa4, 0x00,0xc0,0x4f,0x79,0x69,0xe8);

#ifdef __cplusplus
class DECLSPEC_UUID("ed8c108e-4349-11d2-91a4-00c04f7969e8") XMLHTTPRequest;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(XMLHTTPRequest, 0xed8c108e, 0x4349, 0x11d2, 0x91,0xa4, 0x00,0xc0,0x4f,0x79,0x69,0xe8)
#endif
#endif

#ifndef __IXMLDOMDocument_FWD_DEFINED__
#define __IXMLDOMDocument_FWD_DEFINED__
typedef interface IXMLDOMDocument IXMLDOMDocument;
#ifdef __cplusplus
interface IXMLDOMDocument;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IXMLDSOControl interface
 */
#ifndef __IXMLDSOControl_INTERFACE_DEFINED__
#define __IXMLDSOControl_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDSOControl, 0x310afa62, 0x0575, 0x11d2, 0x9c,0xa9, 0x00,0x60,0xb0,0xec,0x3d,0x39);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("310afa62-0575-11d2-9ca9-0060b0ec3d39")
IXMLDSOControl : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_XMLDocument(
        IXMLDOMDocument **ppDoc) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_XMLDocument(
        IXMLDOMDocument *ppDoc) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_JavaDSOCompatible(
        WINBOOL *fJavaDSOCompatible) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_JavaDSOCompatible(
        WINBOOL fJavaDSOCompatible) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_readyState(
        LONG *state) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDSOControl, 0x310afa62, 0x0575, 0x11d2, 0x9c,0xa9, 0x00,0x60,0xb0,0xec,0x3d,0x39)
#endif
#else
typedef struct IXMLDSOControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDSOControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDSOControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDSOControl *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDSOControl *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDSOControl *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDSOControl *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDSOControl *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDSOControl methods ***/
    HRESULT (STDMETHODCALLTYPE *get_XMLDocument)(
        IXMLDSOControl *This,
        IXMLDOMDocument **ppDoc);

    HRESULT (STDMETHODCALLTYPE *put_XMLDocument)(
        IXMLDSOControl *This,
        IXMLDOMDocument *ppDoc);

    HRESULT (STDMETHODCALLTYPE *get_JavaDSOCompatible)(
        IXMLDSOControl *This,
        WINBOOL *fJavaDSOCompatible);

    HRESULT (STDMETHODCALLTYPE *put_JavaDSOCompatible)(
        IXMLDSOControl *This,
        WINBOOL fJavaDSOCompatible);

    HRESULT (STDMETHODCALLTYPE *get_readyState)(
        IXMLDSOControl *This,
        LONG *state);

    END_INTERFACE
} IXMLDSOControlVtbl;

interface IXMLDSOControl {
    CONST_VTBL IXMLDSOControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDSOControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDSOControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDSOControl_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDSOControl_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDSOControl_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDSOControl_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDSOControl_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDSOControl methods ***/
#define IXMLDSOControl_get_XMLDocument(This,ppDoc) (This)->lpVtbl->get_XMLDocument(This,ppDoc)
#define IXMLDSOControl_put_XMLDocument(This,ppDoc) (This)->lpVtbl->put_XMLDocument(This,ppDoc)
#define IXMLDSOControl_get_JavaDSOCompatible(This,fJavaDSOCompatible) (This)->lpVtbl->get_JavaDSOCompatible(This,fJavaDSOCompatible)
#define IXMLDSOControl_put_JavaDSOCompatible(This,fJavaDSOCompatible) (This)->lpVtbl->put_JavaDSOCompatible(This,fJavaDSOCompatible)
#define IXMLDSOControl_get_readyState(This,state) (This)->lpVtbl->get_readyState(This,state)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDSOControl_QueryInterface(IXMLDSOControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDSOControl_AddRef(IXMLDSOControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDSOControl_Release(IXMLDSOControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDSOControl_GetTypeInfoCount(IXMLDSOControl* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDSOControl_GetTypeInfo(IXMLDSOControl* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDSOControl_GetIDsOfNames(IXMLDSOControl* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDSOControl_Invoke(IXMLDSOControl* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDSOControl methods ***/
static inline HRESULT IXMLDSOControl_get_XMLDocument(IXMLDSOControl* This,IXMLDOMDocument **ppDoc) {
    return This->lpVtbl->get_XMLDocument(This,ppDoc);
}
static inline HRESULT IXMLDSOControl_put_XMLDocument(IXMLDSOControl* This,IXMLDOMDocument *ppDoc) {
    return This->lpVtbl->put_XMLDocument(This,ppDoc);
}
static inline HRESULT IXMLDSOControl_get_JavaDSOCompatible(IXMLDSOControl* This,WINBOOL *fJavaDSOCompatible) {
    return This->lpVtbl->get_JavaDSOCompatible(This,fJavaDSOCompatible);
}
static inline HRESULT IXMLDSOControl_put_JavaDSOCompatible(IXMLDSOControl* This,WINBOOL fJavaDSOCompatible) {
    return This->lpVtbl->put_JavaDSOCompatible(This,fJavaDSOCompatible);
}
static inline HRESULT IXMLDSOControl_get_readyState(IXMLDSOControl* This,LONG *state) {
    return This->lpVtbl->get_readyState(This,state);
}
#endif
#endif

#endif


#endif  /* __IXMLDSOControl_INTERFACE_DEFINED__ */

/*****************************************************************************
 * XMLDSOControl coclass
 */

DEFINE_GUID(CLSID_XMLDSOControl, 0x550dda30, 0x0541, 0x11d2, 0x9c,0xa9, 0x00,0x60,0xb0,0xec,0x3d,0x39);

#ifdef __cplusplus
class DECLSPEC_UUID("550dda30-0541-11d2-9ca9-0060b0ec3d39") XMLDSOControl;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(XMLDSOControl, 0x550dda30, 0x0541, 0x11d2, 0x9c,0xa9, 0x00,0x60,0xb0,0xec,0x3d,0x39)
#endif
#endif

/*****************************************************************************
 * IXMLElementCollection interface
 */
#ifndef __IXMLElementCollection_INTERFACE_DEFINED__
#define __IXMLElementCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLElementCollection, 0x65725580, 0x9b5d, 0x11d0, 0x9b,0xfe, 0x00,0xc0,0x4f,0xc9,0x9c,0x8e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("65725580-9b5d-11d0-9bfe-00c04fc99c8e")
IXMLElementCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE put_length(
        LONG v) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_length(
        LONG *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get__newEnum(
        IUnknown **ppUnk) = 0;

    virtual HRESULT STDMETHODCALLTYPE item(
        VARIANT var1,
        VARIANT var2,
        IDispatch **ppDisp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLElementCollection, 0x65725580, 0x9b5d, 0x11d0, 0x9b,0xfe, 0x00,0xc0,0x4f,0xc9,0x9c,0x8e)
#endif
#else
typedef struct IXMLElementCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLElementCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLElementCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLElementCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLElementCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLElementCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLElementCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLElementCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLElementCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *put_length)(
        IXMLElementCollection *This,
        LONG v);

    HRESULT (STDMETHODCALLTYPE *get_length)(
        IXMLElementCollection *This,
        LONG *p);

    HRESULT (STDMETHODCALLTYPE *get__newEnum)(
        IXMLElementCollection *This,
        IUnknown **ppUnk);

    HRESULT (STDMETHODCALLTYPE *item)(
        IXMLElementCollection *This,
        VARIANT var1,
        VARIANT var2,
        IDispatch **ppDisp);

    END_INTERFACE
} IXMLElementCollectionVtbl;

interface IXMLElementCollection {
    CONST_VTBL IXMLElementCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLElementCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLElementCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLElementCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLElementCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLElementCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLElementCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLElementCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLElementCollection methods ***/
#define IXMLElementCollection_put_length(This,v) (This)->lpVtbl->put_length(This,v)
#define IXMLElementCollection_get_length(This,p) (This)->lpVtbl->get_length(This,p)
#define IXMLElementCollection_get__newEnum(This,ppUnk) (This)->lpVtbl->get__newEnum(This,ppUnk)
#define IXMLElementCollection_item(This,var1,var2,ppDisp) (This)->lpVtbl->item(This,var1,var2,ppDisp)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLElementCollection_QueryInterface(IXMLElementCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLElementCollection_AddRef(IXMLElementCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLElementCollection_Release(IXMLElementCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLElementCollection_GetTypeInfoCount(IXMLElementCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLElementCollection_GetTypeInfo(IXMLElementCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLElementCollection_GetIDsOfNames(IXMLElementCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLElementCollection_Invoke(IXMLElementCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLElementCollection methods ***/
static inline HRESULT IXMLElementCollection_put_length(IXMLElementCollection* This,LONG v) {
    return This->lpVtbl->put_length(This,v);
}
static inline HRESULT IXMLElementCollection_get_length(IXMLElementCollection* This,LONG *p) {
    return This->lpVtbl->get_length(This,p);
}
static inline HRESULT IXMLElementCollection_get__newEnum(IXMLElementCollection* This,IUnknown **ppUnk) {
    return This->lpVtbl->get__newEnum(This,ppUnk);
}
static inline HRESULT IXMLElementCollection_item(IXMLElementCollection* This,VARIANT var1,VARIANT var2,IDispatch **ppDisp) {
    return This->lpVtbl->item(This,var1,var2,ppDisp);
}
#endif
#endif

#endif


#endif  /* __IXMLElementCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLElement interface
 */
#ifndef __IXMLElement_INTERFACE_DEFINED__
#define __IXMLElement_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLElement, 0x3f7f31ac, 0xe15f, 0x11d0, 0x9c,0x25, 0x00,0xc0,0x4f,0xc9,0x9c,0x8e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3f7f31ac-e15f-11d0-9c25-00c04fc99c8e")
IXMLElement : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_tagName(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_tagName(
        BSTR p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_parent(
        IXMLElement **parent) = 0;

    virtual HRESULT STDMETHODCALLTYPE setAttribute(
        BSTR strPropertyName,
        VARIANT PropertyValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE getAttribute(
        BSTR strPropertyName,
        VARIANT *PropertyValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE removeAttribute(
        BSTR strPropertyName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_children(
        IXMLElementCollection **p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_type(
        LONG *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_text(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_text(
        BSTR p) = 0;

    virtual HRESULT STDMETHODCALLTYPE addChild(
        IXMLElement *pChildElem,
        LONG lIndex,
        LONG lreserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE removeChild(
        IXMLElement *pChildElem) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLElement, 0x3f7f31ac, 0xe15f, 0x11d0, 0x9c,0x25, 0x00,0xc0,0x4f,0xc9,0x9c,0x8e)
#endif
#else
typedef struct IXMLElementVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLElement *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLElement *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLElement *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLElement *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLElement *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLElement *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLElement *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLElement methods ***/
    HRESULT (STDMETHODCALLTYPE *get_tagName)(
        IXMLElement *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *put_tagName)(
        IXMLElement *This,
        BSTR p);

    HRESULT (STDMETHODCALLTYPE *get_parent)(
        IXMLElement *This,
        IXMLElement **parent);

    HRESULT (STDMETHODCALLTYPE *setAttribute)(
        IXMLElement *This,
        BSTR strPropertyName,
        VARIANT PropertyValue);

    HRESULT (STDMETHODCALLTYPE *getAttribute)(
        IXMLElement *This,
        BSTR strPropertyName,
        VARIANT *PropertyValue);

    HRESULT (STDMETHODCALLTYPE *removeAttribute)(
        IXMLElement *This,
        BSTR strPropertyName);

    HRESULT (STDMETHODCALLTYPE *get_children)(
        IXMLElement *This,
        IXMLElementCollection **p);

    HRESULT (STDMETHODCALLTYPE *get_type)(
        IXMLElement *This,
        LONG *p);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLElement *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLElement *This,
        BSTR p);

    HRESULT (STDMETHODCALLTYPE *addChild)(
        IXMLElement *This,
        IXMLElement *pChildElem,
        LONG lIndex,
        LONG lreserved);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLElement *This,
        IXMLElement *pChildElem);

    END_INTERFACE
} IXMLElementVtbl;

interface IXMLElement {
    CONST_VTBL IXMLElementVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLElement_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLElement_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLElement_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLElement_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLElement_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLElement_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLElement_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLElement methods ***/
#define IXMLElement_get_tagName(This,p) (This)->lpVtbl->get_tagName(This,p)
#define IXMLElement_put_tagName(This,p) (This)->lpVtbl->put_tagName(This,p)
#define IXMLElement_get_parent(This,parent) (This)->lpVtbl->get_parent(This,parent)
#define IXMLElement_setAttribute(This,strPropertyName,PropertyValue) (This)->lpVtbl->setAttribute(This,strPropertyName,PropertyValue)
#define IXMLElement_getAttribute(This,strPropertyName,PropertyValue) (This)->lpVtbl->getAttribute(This,strPropertyName,PropertyValue)
#define IXMLElement_removeAttribute(This,strPropertyName) (This)->lpVtbl->removeAttribute(This,strPropertyName)
#define IXMLElement_get_children(This,p) (This)->lpVtbl->get_children(This,p)
#define IXMLElement_get_type(This,p) (This)->lpVtbl->get_type(This,p)
#define IXMLElement_get_text(This,p) (This)->lpVtbl->get_text(This,p)
#define IXMLElement_put_text(This,p) (This)->lpVtbl->put_text(This,p)
#define IXMLElement_addChild(This,pChildElem,lIndex,lreserved) (This)->lpVtbl->addChild(This,pChildElem,lIndex,lreserved)
#define IXMLElement_removeChild(This,pChildElem) (This)->lpVtbl->removeChild(This,pChildElem)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLElement_QueryInterface(IXMLElement* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLElement_AddRef(IXMLElement* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLElement_Release(IXMLElement* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLElement_GetTypeInfoCount(IXMLElement* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLElement_GetTypeInfo(IXMLElement* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLElement_GetIDsOfNames(IXMLElement* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLElement_Invoke(IXMLElement* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLElement methods ***/
static inline HRESULT IXMLElement_get_tagName(IXMLElement* This,BSTR *p) {
    return This->lpVtbl->get_tagName(This,p);
}
static inline HRESULT IXMLElement_put_tagName(IXMLElement* This,BSTR p) {
    return This->lpVtbl->put_tagName(This,p);
}
static inline HRESULT IXMLElement_get_parent(IXMLElement* This,IXMLElement **parent) {
    return This->lpVtbl->get_parent(This,parent);
}
static inline HRESULT IXMLElement_setAttribute(IXMLElement* This,BSTR strPropertyName,VARIANT PropertyValue) {
    return This->lpVtbl->setAttribute(This,strPropertyName,PropertyValue);
}
static inline HRESULT IXMLElement_getAttribute(IXMLElement* This,BSTR strPropertyName,VARIANT *PropertyValue) {
    return This->lpVtbl->getAttribute(This,strPropertyName,PropertyValue);
}
static inline HRESULT IXMLElement_removeAttribute(IXMLElement* This,BSTR strPropertyName) {
    return This->lpVtbl->removeAttribute(This,strPropertyName);
}
static inline HRESULT IXMLElement_get_children(IXMLElement* This,IXMLElementCollection **p) {
    return This->lpVtbl->get_children(This,p);
}
static inline HRESULT IXMLElement_get_type(IXMLElement* This,LONG *p) {
    return This->lpVtbl->get_type(This,p);
}
static inline HRESULT IXMLElement_get_text(IXMLElement* This,BSTR *p) {
    return This->lpVtbl->get_text(This,p);
}
static inline HRESULT IXMLElement_put_text(IXMLElement* This,BSTR p) {
    return This->lpVtbl->put_text(This,p);
}
static inline HRESULT IXMLElement_addChild(IXMLElement* This,IXMLElement *pChildElem,LONG lIndex,LONG lreserved) {
    return This->lpVtbl->addChild(This,pChildElem,lIndex,lreserved);
}
static inline HRESULT IXMLElement_removeChild(IXMLElement* This,IXMLElement *pChildElem) {
    return This->lpVtbl->removeChild(This,pChildElem);
}
#endif
#endif

#endif


#endif  /* __IXMLElement_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDocument interface
 */
#ifndef __IXMLDocument_INTERFACE_DEFINED__
#define __IXMLDocument_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDocument, 0xf52e2b61, 0x18a1, 0x11d1, 0xb1,0x05, 0x00,0x80,0x5f,0x49,0x91,0x6b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f52e2b61-18a1-11d1-b105-00805f49916b")
IXMLDocument : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_root(
        IXMLElement **p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_fileSize(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_fileModifiedDate(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_fileUpdatedDate(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_URL(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_URL(
        BSTR p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_mimeType(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_readyState(
        LONG *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_charset(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_charset(
        BSTR p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_version(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_doctype(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_dtdURl(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE createElement(
        VARIANT vType,
        VARIANT var1,
        IXMLElement **ppElem) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDocument, 0xf52e2b61, 0x18a1, 0x11d1, 0xb1,0x05, 0x00,0x80,0x5f,0x49,0x91,0x6b)
#endif
#else
typedef struct IXMLDocumentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDocument *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDocument *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDocument *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDocument *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDocument *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDocument *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDocument *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDocument methods ***/
    HRESULT (STDMETHODCALLTYPE *get_root)(
        IXMLDocument *This,
        IXMLElement **p);

    HRESULT (STDMETHODCALLTYPE *get_fileSize)(
        IXMLDocument *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_fileModifiedDate)(
        IXMLDocument *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_fileUpdatedDate)(
        IXMLDocument *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_URL)(
        IXMLDocument *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *put_URL)(
        IXMLDocument *This,
        BSTR p);

    HRESULT (STDMETHODCALLTYPE *get_mimeType)(
        IXMLDocument *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_readyState)(
        IXMLDocument *This,
        LONG *p);

    HRESULT (STDMETHODCALLTYPE *get_charset)(
        IXMLDocument *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *put_charset)(
        IXMLDocument *This,
        BSTR p);

    HRESULT (STDMETHODCALLTYPE *get_version)(
        IXMLDocument *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_doctype)(
        IXMLDocument *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_dtdURl)(
        IXMLDocument *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *createElement)(
        IXMLDocument *This,
        VARIANT vType,
        VARIANT var1,
        IXMLElement **ppElem);

    END_INTERFACE
} IXMLDocumentVtbl;

interface IXMLDocument {
    CONST_VTBL IXMLDocumentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDocument_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDocument_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDocument_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDocument_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDocument_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDocument_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDocument_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDocument methods ***/
#define IXMLDocument_get_root(This,p) (This)->lpVtbl->get_root(This,p)
#define IXMLDocument_get_fileSize(This,p) (This)->lpVtbl->get_fileSize(This,p)
#define IXMLDocument_get_fileModifiedDate(This,p) (This)->lpVtbl->get_fileModifiedDate(This,p)
#define IXMLDocument_get_fileUpdatedDate(This,p) (This)->lpVtbl->get_fileUpdatedDate(This,p)
#define IXMLDocument_get_URL(This,p) (This)->lpVtbl->get_URL(This,p)
#define IXMLDocument_put_URL(This,p) (This)->lpVtbl->put_URL(This,p)
#define IXMLDocument_get_mimeType(This,p) (This)->lpVtbl->get_mimeType(This,p)
#define IXMLDocument_get_readyState(This,p) (This)->lpVtbl->get_readyState(This,p)
#define IXMLDocument_get_charset(This,p) (This)->lpVtbl->get_charset(This,p)
#define IXMLDocument_put_charset(This,p) (This)->lpVtbl->put_charset(This,p)
#define IXMLDocument_get_version(This,p) (This)->lpVtbl->get_version(This,p)
#define IXMLDocument_get_doctype(This,p) (This)->lpVtbl->get_doctype(This,p)
#define IXMLDocument_get_dtdURl(This,p) (This)->lpVtbl->get_dtdURl(This,p)
#define IXMLDocument_createElement(This,vType,var1,ppElem) (This)->lpVtbl->createElement(This,vType,var1,ppElem)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDocument_QueryInterface(IXMLDocument* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDocument_AddRef(IXMLDocument* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDocument_Release(IXMLDocument* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDocument_GetTypeInfoCount(IXMLDocument* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDocument_GetTypeInfo(IXMLDocument* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDocument_GetIDsOfNames(IXMLDocument* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDocument_Invoke(IXMLDocument* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDocument methods ***/
static inline HRESULT IXMLDocument_get_root(IXMLDocument* This,IXMLElement **p) {
    return This->lpVtbl->get_root(This,p);
}
static inline HRESULT IXMLDocument_get_fileSize(IXMLDocument* This,BSTR *p) {
    return This->lpVtbl->get_fileSize(This,p);
}
static inline HRESULT IXMLDocument_get_fileModifiedDate(IXMLDocument* This,BSTR *p) {
    return This->lpVtbl->get_fileModifiedDate(This,p);
}
static inline HRESULT IXMLDocument_get_fileUpdatedDate(IXMLDocument* This,BSTR *p) {
    return This->lpVtbl->get_fileUpdatedDate(This,p);
}
static inline HRESULT IXMLDocument_get_URL(IXMLDocument* This,BSTR *p) {
    return This->lpVtbl->get_URL(This,p);
}
static inline HRESULT IXMLDocument_put_URL(IXMLDocument* This,BSTR p) {
    return This->lpVtbl->put_URL(This,p);
}
static inline HRESULT IXMLDocument_get_mimeType(IXMLDocument* This,BSTR *p) {
    return This->lpVtbl->get_mimeType(This,p);
}
static inline HRESULT IXMLDocument_get_readyState(IXMLDocument* This,LONG *p) {
    return This->lpVtbl->get_readyState(This,p);
}
static inline HRESULT IXMLDocument_get_charset(IXMLDocument* This,BSTR *p) {
    return This->lpVtbl->get_charset(This,p);
}
static inline HRESULT IXMLDocument_put_charset(IXMLDocument* This,BSTR p) {
    return This->lpVtbl->put_charset(This,p);
}
static inline HRESULT IXMLDocument_get_version(IXMLDocument* This,BSTR *p) {
    return This->lpVtbl->get_version(This,p);
}
static inline HRESULT IXMLDocument_get_doctype(IXMLDocument* This,BSTR *p) {
    return This->lpVtbl->get_doctype(This,p);
}
static inline HRESULT IXMLDocument_get_dtdURl(IXMLDocument* This,BSTR *p) {
    return This->lpVtbl->get_dtdURl(This,p);
}
static inline HRESULT IXMLDocument_createElement(IXMLDocument* This,VARIANT vType,VARIANT var1,IXMLElement **ppElem) {
    return This->lpVtbl->createElement(This,vType,var1,ppElem);
}
#endif
#endif

#endif


#endif  /* __IXMLDocument_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLElement2 interface
 */
#ifndef __IXMLElement2_INTERFACE_DEFINED__
#define __IXMLElement2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLElement2, 0x2b8de2ff, 0x8d2d, 0x11d1, 0xb2,0xfc, 0x00,0xc0,0x4f,0xd9,0x15,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2b8de2ff-8d2d-11d1-b2fc-00c04fd915a9")
IXMLElement2 : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_tagName(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_tagName(
        BSTR p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_parent(
        IXMLElement2 **ppParent) = 0;

    virtual HRESULT STDMETHODCALLTYPE setAttribute(
        BSTR strPropertyName,
        VARIANT PropertyValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE getAttribute(
        BSTR strPropertyName,
        VARIANT *PropertyValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE removeAttribute(
        BSTR strPropertyName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_children(
        IXMLElementCollection **pp) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_type(
        LONG *plType) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_text(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_text(
        BSTR p) = 0;

    virtual HRESULT STDMETHODCALLTYPE addChild(
        IXMLElement2 *pChildElem,
        LONG lIndex,
        LONG lReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE removeChild(
        IXMLElement2 *pChildElem) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_attributes(
        IXMLElementCollection **pp) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLElement2, 0x2b8de2ff, 0x8d2d, 0x11d1, 0xb2,0xfc, 0x00,0xc0,0x4f,0xd9,0x15,0xa9)
#endif
#else
typedef struct IXMLElement2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLElement2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLElement2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLElement2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLElement2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLElement2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLElement2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLElement2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLElement2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_tagName)(
        IXMLElement2 *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *put_tagName)(
        IXMLElement2 *This,
        BSTR p);

    HRESULT (STDMETHODCALLTYPE *get_parent)(
        IXMLElement2 *This,
        IXMLElement2 **ppParent);

    HRESULT (STDMETHODCALLTYPE *setAttribute)(
        IXMLElement2 *This,
        BSTR strPropertyName,
        VARIANT PropertyValue);

    HRESULT (STDMETHODCALLTYPE *getAttribute)(
        IXMLElement2 *This,
        BSTR strPropertyName,
        VARIANT *PropertyValue);

    HRESULT (STDMETHODCALLTYPE *removeAttribute)(
        IXMLElement2 *This,
        BSTR strPropertyName);

    HRESULT (STDMETHODCALLTYPE *get_children)(
        IXMLElement2 *This,
        IXMLElementCollection **pp);

    HRESULT (STDMETHODCALLTYPE *get_type)(
        IXMLElement2 *This,
        LONG *plType);

    HRESULT (STDMETHODCALLTYPE *get_text)(
        IXMLElement2 *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *put_text)(
        IXMLElement2 *This,
        BSTR p);

    HRESULT (STDMETHODCALLTYPE *addChild)(
        IXMLElement2 *This,
        IXMLElement2 *pChildElem,
        LONG lIndex,
        LONG lReserved);

    HRESULT (STDMETHODCALLTYPE *removeChild)(
        IXMLElement2 *This,
        IXMLElement2 *pChildElem);

    HRESULT (STDMETHODCALLTYPE *get_attributes)(
        IXMLElement2 *This,
        IXMLElementCollection **pp);

    END_INTERFACE
} IXMLElement2Vtbl;

interface IXMLElement2 {
    CONST_VTBL IXMLElement2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLElement2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLElement2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLElement2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLElement2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLElement2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLElement2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLElement2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLElement2 methods ***/
#define IXMLElement2_get_tagName(This,p) (This)->lpVtbl->get_tagName(This,p)
#define IXMLElement2_put_tagName(This,p) (This)->lpVtbl->put_tagName(This,p)
#define IXMLElement2_get_parent(This,ppParent) (This)->lpVtbl->get_parent(This,ppParent)
#define IXMLElement2_setAttribute(This,strPropertyName,PropertyValue) (This)->lpVtbl->setAttribute(This,strPropertyName,PropertyValue)
#define IXMLElement2_getAttribute(This,strPropertyName,PropertyValue) (This)->lpVtbl->getAttribute(This,strPropertyName,PropertyValue)
#define IXMLElement2_removeAttribute(This,strPropertyName) (This)->lpVtbl->removeAttribute(This,strPropertyName)
#define IXMLElement2_get_children(This,pp) (This)->lpVtbl->get_children(This,pp)
#define IXMLElement2_get_type(This,plType) (This)->lpVtbl->get_type(This,plType)
#define IXMLElement2_get_text(This,p) (This)->lpVtbl->get_text(This,p)
#define IXMLElement2_put_text(This,p) (This)->lpVtbl->put_text(This,p)
#define IXMLElement2_addChild(This,pChildElem,lIndex,lReserved) (This)->lpVtbl->addChild(This,pChildElem,lIndex,lReserved)
#define IXMLElement2_removeChild(This,pChildElem) (This)->lpVtbl->removeChild(This,pChildElem)
#define IXMLElement2_get_attributes(This,pp) (This)->lpVtbl->get_attributes(This,pp)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLElement2_QueryInterface(IXMLElement2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLElement2_AddRef(IXMLElement2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLElement2_Release(IXMLElement2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLElement2_GetTypeInfoCount(IXMLElement2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLElement2_GetTypeInfo(IXMLElement2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLElement2_GetIDsOfNames(IXMLElement2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLElement2_Invoke(IXMLElement2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLElement2 methods ***/
static inline HRESULT IXMLElement2_get_tagName(IXMLElement2* This,BSTR *p) {
    return This->lpVtbl->get_tagName(This,p);
}
static inline HRESULT IXMLElement2_put_tagName(IXMLElement2* This,BSTR p) {
    return This->lpVtbl->put_tagName(This,p);
}
static inline HRESULT IXMLElement2_get_parent(IXMLElement2* This,IXMLElement2 **ppParent) {
    return This->lpVtbl->get_parent(This,ppParent);
}
static inline HRESULT IXMLElement2_setAttribute(IXMLElement2* This,BSTR strPropertyName,VARIANT PropertyValue) {
    return This->lpVtbl->setAttribute(This,strPropertyName,PropertyValue);
}
static inline HRESULT IXMLElement2_getAttribute(IXMLElement2* This,BSTR strPropertyName,VARIANT *PropertyValue) {
    return This->lpVtbl->getAttribute(This,strPropertyName,PropertyValue);
}
static inline HRESULT IXMLElement2_removeAttribute(IXMLElement2* This,BSTR strPropertyName) {
    return This->lpVtbl->removeAttribute(This,strPropertyName);
}
static inline HRESULT IXMLElement2_get_children(IXMLElement2* This,IXMLElementCollection **pp) {
    return This->lpVtbl->get_children(This,pp);
}
static inline HRESULT IXMLElement2_get_type(IXMLElement2* This,LONG *plType) {
    return This->lpVtbl->get_type(This,plType);
}
static inline HRESULT IXMLElement2_get_text(IXMLElement2* This,BSTR *p) {
    return This->lpVtbl->get_text(This,p);
}
static inline HRESULT IXMLElement2_put_text(IXMLElement2* This,BSTR p) {
    return This->lpVtbl->put_text(This,p);
}
static inline HRESULT IXMLElement2_addChild(IXMLElement2* This,IXMLElement2 *pChildElem,LONG lIndex,LONG lReserved) {
    return This->lpVtbl->addChild(This,pChildElem,lIndex,lReserved);
}
static inline HRESULT IXMLElement2_removeChild(IXMLElement2* This,IXMLElement2 *pChildElem) {
    return This->lpVtbl->removeChild(This,pChildElem);
}
static inline HRESULT IXMLElement2_get_attributes(IXMLElement2* This,IXMLElementCollection **pp) {
    return This->lpVtbl->get_attributes(This,pp);
}
#endif
#endif

#endif


#endif  /* __IXMLElement2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLDocument2 interface
 */
#ifndef __IXMLDocument2_INTERFACE_DEFINED__
#define __IXMLDocument2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLDocument2, 0x2b8de2fe, 0x8d2d, 0x11d1, 0xb2,0xfc, 0x00,0xc0,0x4f,0xd9,0x15,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2b8de2fe-8d2d-11d1-b2fc-00c04fd915a9")
IXMLDocument2 : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_root(
        IXMLElement2 **p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_fileSize(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_fileModifiedDate(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_fileUpdatedDate(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_URL(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_URL(
        BSTR p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_mimeType(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_readyState(
        LONG *pl) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_charset(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_charset(
        BSTR p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_version(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_doctype(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_dtdURL(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE createElement(
        VARIANT vType,
        VARIANT var,
        IXMLElement2 **ppElem) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_async(
        VARIANT_BOOL *pf) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_async(
        VARIANT_BOOL f) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLDocument2, 0x2b8de2fe, 0x8d2d, 0x11d1, 0xb2,0xfc, 0x00,0xc0,0x4f,0xd9,0x15,0xa9)
#endif
#else
typedef struct IXMLDocument2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLDocument2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLDocument2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLDocument2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLDocument2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLDocument2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLDocument2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLDocument2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLDocument2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_root)(
        IXMLDocument2 *This,
        IXMLElement2 **p);

    HRESULT (STDMETHODCALLTYPE *get_fileSize)(
        IXMLDocument2 *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_fileModifiedDate)(
        IXMLDocument2 *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_fileUpdatedDate)(
        IXMLDocument2 *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_URL)(
        IXMLDocument2 *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *put_URL)(
        IXMLDocument2 *This,
        BSTR p);

    HRESULT (STDMETHODCALLTYPE *get_mimeType)(
        IXMLDocument2 *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_readyState)(
        IXMLDocument2 *This,
        LONG *pl);

    HRESULT (STDMETHODCALLTYPE *get_charset)(
        IXMLDocument2 *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *put_charset)(
        IXMLDocument2 *This,
        BSTR p);

    HRESULT (STDMETHODCALLTYPE *get_version)(
        IXMLDocument2 *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_doctype)(
        IXMLDocument2 *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_dtdURL)(
        IXMLDocument2 *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *createElement)(
        IXMLDocument2 *This,
        VARIANT vType,
        VARIANT var,
        IXMLElement2 **ppElem);

    HRESULT (STDMETHODCALLTYPE *get_async)(
        IXMLDocument2 *This,
        VARIANT_BOOL *pf);

    HRESULT (STDMETHODCALLTYPE *put_async)(
        IXMLDocument2 *This,
        VARIANT_BOOL f);

    END_INTERFACE
} IXMLDocument2Vtbl;

interface IXMLDocument2 {
    CONST_VTBL IXMLDocument2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLDocument2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLDocument2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLDocument2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLDocument2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLDocument2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLDocument2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLDocument2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLDocument2 methods ***/
#define IXMLDocument2_get_root(This,p) (This)->lpVtbl->get_root(This,p)
#define IXMLDocument2_get_fileSize(This,p) (This)->lpVtbl->get_fileSize(This,p)
#define IXMLDocument2_get_fileModifiedDate(This,p) (This)->lpVtbl->get_fileModifiedDate(This,p)
#define IXMLDocument2_get_fileUpdatedDate(This,p) (This)->lpVtbl->get_fileUpdatedDate(This,p)
#define IXMLDocument2_get_URL(This,p) (This)->lpVtbl->get_URL(This,p)
#define IXMLDocument2_put_URL(This,p) (This)->lpVtbl->put_URL(This,p)
#define IXMLDocument2_get_mimeType(This,p) (This)->lpVtbl->get_mimeType(This,p)
#define IXMLDocument2_get_readyState(This,pl) (This)->lpVtbl->get_readyState(This,pl)
#define IXMLDocument2_get_charset(This,p) (This)->lpVtbl->get_charset(This,p)
#define IXMLDocument2_put_charset(This,p) (This)->lpVtbl->put_charset(This,p)
#define IXMLDocument2_get_version(This,p) (This)->lpVtbl->get_version(This,p)
#define IXMLDocument2_get_doctype(This,p) (This)->lpVtbl->get_doctype(This,p)
#define IXMLDocument2_get_dtdURL(This,p) (This)->lpVtbl->get_dtdURL(This,p)
#define IXMLDocument2_createElement(This,vType,var,ppElem) (This)->lpVtbl->createElement(This,vType,var,ppElem)
#define IXMLDocument2_get_async(This,pf) (This)->lpVtbl->get_async(This,pf)
#define IXMLDocument2_put_async(This,f) (This)->lpVtbl->put_async(This,f)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLDocument2_QueryInterface(IXMLDocument2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLDocument2_AddRef(IXMLDocument2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLDocument2_Release(IXMLDocument2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLDocument2_GetTypeInfoCount(IXMLDocument2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLDocument2_GetTypeInfo(IXMLDocument2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLDocument2_GetIDsOfNames(IXMLDocument2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLDocument2_Invoke(IXMLDocument2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLDocument2 methods ***/
static inline HRESULT IXMLDocument2_get_root(IXMLDocument2* This,IXMLElement2 **p) {
    return This->lpVtbl->get_root(This,p);
}
static inline HRESULT IXMLDocument2_get_fileSize(IXMLDocument2* This,BSTR *p) {
    return This->lpVtbl->get_fileSize(This,p);
}
static inline HRESULT IXMLDocument2_get_fileModifiedDate(IXMLDocument2* This,BSTR *p) {
    return This->lpVtbl->get_fileModifiedDate(This,p);
}
static inline HRESULT IXMLDocument2_get_fileUpdatedDate(IXMLDocument2* This,BSTR *p) {
    return This->lpVtbl->get_fileUpdatedDate(This,p);
}
static inline HRESULT IXMLDocument2_get_URL(IXMLDocument2* This,BSTR *p) {
    return This->lpVtbl->get_URL(This,p);
}
static inline HRESULT IXMLDocument2_put_URL(IXMLDocument2* This,BSTR p) {
    return This->lpVtbl->put_URL(This,p);
}
static inline HRESULT IXMLDocument2_get_mimeType(IXMLDocument2* This,BSTR *p) {
    return This->lpVtbl->get_mimeType(This,p);
}
static inline HRESULT IXMLDocument2_get_readyState(IXMLDocument2* This,LONG *pl) {
    return This->lpVtbl->get_readyState(This,pl);
}
static inline HRESULT IXMLDocument2_get_charset(IXMLDocument2* This,BSTR *p) {
    return This->lpVtbl->get_charset(This,p);
}
static inline HRESULT IXMLDocument2_put_charset(IXMLDocument2* This,BSTR p) {
    return This->lpVtbl->put_charset(This,p);
}
static inline HRESULT IXMLDocument2_get_version(IXMLDocument2* This,BSTR *p) {
    return This->lpVtbl->get_version(This,p);
}
static inline HRESULT IXMLDocument2_get_doctype(IXMLDocument2* This,BSTR *p) {
    return This->lpVtbl->get_doctype(This,p);
}
static inline HRESULT IXMLDocument2_get_dtdURL(IXMLDocument2* This,BSTR *p) {
    return This->lpVtbl->get_dtdURL(This,p);
}
static inline HRESULT IXMLDocument2_createElement(IXMLDocument2* This,VARIANT vType,VARIANT var,IXMLElement2 **ppElem) {
    return This->lpVtbl->createElement(This,vType,var,ppElem);
}
static inline HRESULT IXMLDocument2_get_async(IXMLDocument2* This,VARIANT_BOOL *pf) {
    return This->lpVtbl->get_async(This,pf);
}
static inline HRESULT IXMLDocument2_put_async(IXMLDocument2* This,VARIANT_BOOL f) {
    return This->lpVtbl->put_async(This,f);
}
#endif
#endif

#endif


#endif  /* __IXMLDocument2_INTERFACE_DEFINED__ */

typedef enum tagXMLEMEM_TYPE {
    XMLELEMTYPE_ELEMENT = 0,
    XMLELEMTYPE_TEXT = 1,
    XMLELEMTYPE_COMMENT = 2,
    XMLELEMTYPE_DOCUMENT = 3,
    XMLELEMTYPE_DTD = 4,
    XMLELEMTYPE_PI = 5,
    XMLELEMTYPE_OTHER = 6
} XMLELEM_TYPE;
typedef struct _xml_error {
    UINT _nLine;
    BSTR _pchBuf;
    BSTR _cchBuf;
    UINT _ich;
    BSTR _pszFound;
    BSTR _pszExpected;
    DWORD _reserved1;
    DWORD _reserved2;
} XML_ERROR;
/*****************************************************************************
 * IXMLAttribute interface
 */
#ifndef __IXMLAttribute_INTERFACE_DEFINED__
#define __IXMLAttribute_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLAttribute, 0xd4d4a0fc, 0x3b73, 0x11d1, 0xb2,0xb4, 0x00,0xc0,0x4f,0xb9,0x25,0x96);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d4d4a0fc-3b73-11d1-b2b4-00c04fb92596")
IXMLAttribute : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_name(
        BSTR *p) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_value(
        BSTR *p) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLAttribute, 0xd4d4a0fc, 0x3b73, 0x11d1, 0xb2,0xb4, 0x00,0xc0,0x4f,0xb9,0x25,0x96)
#endif
#else
typedef struct IXMLAttributeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLAttribute *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLAttribute *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLAttribute *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLAttribute *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLAttribute *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLAttribute *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLAttribute *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLAttribute methods ***/
    HRESULT (STDMETHODCALLTYPE *get_name)(
        IXMLAttribute *This,
        BSTR *p);

    HRESULT (STDMETHODCALLTYPE *get_value)(
        IXMLAttribute *This,
        BSTR *p);

    END_INTERFACE
} IXMLAttributeVtbl;

interface IXMLAttribute {
    CONST_VTBL IXMLAttributeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLAttribute_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLAttribute_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLAttribute_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLAttribute_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLAttribute_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLAttribute_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLAttribute_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLAttribute methods ***/
#define IXMLAttribute_get_name(This,p) (This)->lpVtbl->get_name(This,p)
#define IXMLAttribute_get_value(This,p) (This)->lpVtbl->get_value(This,p)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLAttribute_QueryInterface(IXMLAttribute* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLAttribute_AddRef(IXMLAttribute* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLAttribute_Release(IXMLAttribute* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLAttribute_GetTypeInfoCount(IXMLAttribute* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLAttribute_GetTypeInfo(IXMLAttribute* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLAttribute_GetIDsOfNames(IXMLAttribute* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLAttribute_Invoke(IXMLAttribute* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLAttribute methods ***/
static inline HRESULT IXMLAttribute_get_name(IXMLAttribute* This,BSTR *p) {
    return This->lpVtbl->get_name(This,p);
}
static inline HRESULT IXMLAttribute_get_value(IXMLAttribute* This,BSTR *p) {
    return This->lpVtbl->get_value(This,p);
}
#endif
#endif

#endif


#endif  /* __IXMLAttribute_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLError interface
 */
#ifndef __IXMLError_INTERFACE_DEFINED__
#define __IXMLError_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLError, 0x948c5ad3, 0xc58d, 0x11d0, 0x9c,0x0b, 0x00,0xc0,0x4f,0xc9,0x9c,0x8e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("948c5ad3-c58d-11d0-9c0b-00c04fc99c8e")
IXMLError : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetErrorInfo(
        XML_ERROR *pErrorReturn) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLError, 0x948c5ad3, 0xc58d, 0x11d0, 0x9c,0x0b, 0x00,0xc0,0x4f,0xc9,0x9c,0x8e)
#endif
#else
typedef struct IXMLErrorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLError *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLError *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLError *This);

    /*** IXMLError methods ***/
    HRESULT (STDMETHODCALLTYPE *GetErrorInfo)(
        IXMLError *This,
        XML_ERROR *pErrorReturn);

    END_INTERFACE
} IXMLErrorVtbl;

interface IXMLError {
    CONST_VTBL IXMLErrorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLError_Release(This) (This)->lpVtbl->Release(This)
/*** IXMLError methods ***/
#define IXMLError_GetErrorInfo(This,pErrorReturn) (This)->lpVtbl->GetErrorInfo(This,pErrorReturn)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLError_QueryInterface(IXMLError* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLError_AddRef(IXMLError* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLError_Release(IXMLError* This) {
    return This->lpVtbl->Release(This);
}
/*** IXMLError methods ***/
static inline HRESULT IXMLError_GetErrorInfo(IXMLError* This,XML_ERROR *pErrorReturn) {
    return This->lpVtbl->GetErrorInfo(This,pErrorReturn);
}
#endif
#endif

#endif


#endif  /* __IXMLError_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXMLElementNotificationSink interface
 */
#ifndef __IXMLElementNotificationSink_INTERFACE_DEFINED__
#define __IXMLElementNotificationSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXMLElementNotificationSink, 0xd9f1e15a, 0xccdb, 0x11d0, 0x9c,0x0c, 0x00,0xc0,0x4f,0xc9,0x9c,0x8e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d9f1e15a-ccdb-11d0-9c0c-00c04fc99c8e")
IXMLElementNotificationSink : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE ChildAdded(
        IDispatch *pChildElem) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXMLElementNotificationSink, 0xd9f1e15a, 0xccdb, 0x11d0, 0x9c,0x0c, 0x00,0xc0,0x4f,0xc9,0x9c,0x8e)
#endif
#else
typedef struct IXMLElementNotificationSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXMLElementNotificationSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXMLElementNotificationSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXMLElementNotificationSink *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IXMLElementNotificationSink *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IXMLElementNotificationSink *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IXMLElementNotificationSink *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IXMLElementNotificationSink *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IXMLElementNotificationSink methods ***/
    HRESULT (STDMETHODCALLTYPE *ChildAdded)(
        IXMLElementNotificationSink *This,
        IDispatch *pChildElem);

    END_INTERFACE
} IXMLElementNotificationSinkVtbl;

interface IXMLElementNotificationSink {
    CONST_VTBL IXMLElementNotificationSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXMLElementNotificationSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXMLElementNotificationSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXMLElementNotificationSink_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IXMLElementNotificationSink_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IXMLElementNotificationSink_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IXMLElementNotificationSink_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IXMLElementNotificationSink_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IXMLElementNotificationSink methods ***/
#define IXMLElementNotificationSink_ChildAdded(This,pChildElem) (This)->lpVtbl->ChildAdded(This,pChildElem)
#else
/*** IUnknown methods ***/
static inline HRESULT IXMLElementNotificationSink_QueryInterface(IXMLElementNotificationSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXMLElementNotificationSink_AddRef(IXMLElementNotificationSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXMLElementNotificationSink_Release(IXMLElementNotificationSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IXMLElementNotificationSink_GetTypeInfoCount(IXMLElementNotificationSink* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IXMLElementNotificationSink_GetTypeInfo(IXMLElementNotificationSink* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IXMLElementNotificationSink_GetIDsOfNames(IXMLElementNotificationSink* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IXMLElementNotificationSink_Invoke(IXMLElementNotificationSink* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IXMLElementNotificationSink methods ***/
static inline HRESULT IXMLElementNotificationSink_ChildAdded(IXMLElementNotificationSink* This,IDispatch *pChildElem) {
    return This->lpVtbl->ChildAdded(This,pChildElem);
}
#endif
#endif

#endif


#endif  /* __IXMLElementNotificationSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * XMLDocument coclass
 */

DEFINE_GUID(CLSID_XMLDocument, 0xcfc399af, 0xd876, 0x11d0, 0x9c,0x10, 0x00,0xc0,0x4f,0xc9,0x9c,0x8e);

#ifdef __cplusplus
class DECLSPEC_UUID("cfc399af-d876-11d0-9c10-00c04fc99c8e") XMLDocument;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(XMLDocument, 0xcfc399af, 0xd876, 0x11d0, 0x9c,0x10, 0x00,0xc0,0x4f,0xc9,0x9c,0x8e)
#endif
#endif

#endif /* __MSXML_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __msxml_h__ */
