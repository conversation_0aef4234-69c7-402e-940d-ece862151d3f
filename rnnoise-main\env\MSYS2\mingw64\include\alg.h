/*** Autogenerated by WIDL 10.12 from include/alg.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __alg_h__
#define __alg_h__

/* Forward declarations */

#ifndef __IAdapterInfo_FWD_DEFINED__
#define __IAdapterInfo_FWD_DEFINED__
typedef interface IAdapterInfo IAdapterInfo;
#ifdef __cplusplus
interface IAdapterInfo;
#endif /* __cplusplus */
#endif

#ifndef __IPendingProxyConnection_FWD_DEFINED__
#define __IPendingProxyConnection_FWD_DEFINED__
typedef interface IPendingProxyConnection IPendingProxyConnection;
#ifdef __cplusplus
interface IPendingProxyConnection;
#endif /* __cplusplus */
#endif

#ifndef __IDataChannel_FWD_DEFINED__
#define __IDataChannel_FWD_DEFINED__
typedef interface IDataChannel IDataChannel;
#ifdef __cplusplus
interface IDataChannel;
#endif /* __cplusplus */
#endif

#ifndef __IPersistentDataChannel_FWD_DEFINED__
#define __IPersistentDataChannel_FWD_DEFINED__
typedef interface IPersistentDataChannel IPersistentDataChannel;
#ifdef __cplusplus
interface IPersistentDataChannel;
#endif /* __cplusplus */
#endif

#ifndef __IPrimaryControlChannel_FWD_DEFINED__
#define __IPrimaryControlChannel_FWD_DEFINED__
typedef interface IPrimaryControlChannel IPrimaryControlChannel;
#ifdef __cplusplus
interface IPrimaryControlChannel;
#endif /* __cplusplus */
#endif

#ifndef __ISecondaryControlChannel_FWD_DEFINED__
#define __ISecondaryControlChannel_FWD_DEFINED__
typedef interface ISecondaryControlChannel ISecondaryControlChannel;
#ifdef __cplusplus
interface ISecondaryControlChannel;
#endif /* __cplusplus */
#endif

#ifndef __IEnumAdapterInfo_FWD_DEFINED__
#define __IEnumAdapterInfo_FWD_DEFINED__
typedef interface IEnumAdapterInfo IEnumAdapterInfo;
#ifdef __cplusplus
interface IEnumAdapterInfo;
#endif /* __cplusplus */
#endif

#ifndef __IAdapterNotificationSink_FWD_DEFINED__
#define __IAdapterNotificationSink_FWD_DEFINED__
typedef interface IAdapterNotificationSink IAdapterNotificationSink;
#ifdef __cplusplus
interface IAdapterNotificationSink;
#endif /* __cplusplus */
#endif

#ifndef __IApplicationGatewayServices_FWD_DEFINED__
#define __IApplicationGatewayServices_FWD_DEFINED__
typedef interface IApplicationGatewayServices IApplicationGatewayServices;
#ifdef __cplusplus
interface IApplicationGatewayServices;
#endif /* __cplusplus */
#endif

#ifndef __IApplicationGateway_FWD_DEFINED__
#define __IApplicationGateway_FWD_DEFINED__
typedef interface IApplicationGateway IApplicationGateway;
#ifdef __cplusplus
interface IApplicationGateway;
#endif /* __cplusplus */
#endif

#ifndef __ApplicationGatewayServices_FWD_DEFINED__
#define __ApplicationGatewayServices_FWD_DEFINED__
#ifdef __cplusplus
typedef class ApplicationGatewayServices ApplicationGatewayServices;
#else
typedef struct ApplicationGatewayServices ApplicationGatewayServices;
#endif /* defined __cplusplus */
#endif /* defined __ApplicationGatewayServices_FWD_DEFINED__ */

#ifndef __PrimaryControlChannel_FWD_DEFINED__
#define __PrimaryControlChannel_FWD_DEFINED__
#ifdef __cplusplus
typedef class PrimaryControlChannel PrimaryControlChannel;
#else
typedef struct PrimaryControlChannel PrimaryControlChannel;
#endif /* defined __cplusplus */
#endif /* defined __PrimaryControlChannel_FWD_DEFINED__ */

#ifndef __SecondaryControlChannel_FWD_DEFINED__
#define __SecondaryControlChannel_FWD_DEFINED__
#ifdef __cplusplus
typedef class SecondaryControlChannel SecondaryControlChannel;
#else
typedef struct SecondaryControlChannel SecondaryControlChannel;
#endif /* defined __cplusplus */
#endif /* defined __SecondaryControlChannel_FWD_DEFINED__ */

#ifndef __AdapterInfo_FWD_DEFINED__
#define __AdapterInfo_FWD_DEFINED__
#ifdef __cplusplus
typedef class AdapterInfo AdapterInfo;
#else
typedef struct AdapterInfo AdapterInfo;
#endif /* defined __cplusplus */
#endif /* defined __AdapterInfo_FWD_DEFINED__ */

#ifndef __EnumAdapterInfo_FWD_DEFINED__
#define __EnumAdapterInfo_FWD_DEFINED__
#ifdef __cplusplus
typedef class EnumAdapterInfo EnumAdapterInfo;
#else
typedef struct EnumAdapterInfo EnumAdapterInfo;
#endif /* defined __cplusplus */
#endif /* defined __EnumAdapterInfo_FWD_DEFINED__ */

#ifndef __PendingProxyConnection_FWD_DEFINED__
#define __PendingProxyConnection_FWD_DEFINED__
#ifdef __cplusplus
typedef class PendingProxyConnection PendingProxyConnection;
#else
typedef struct PendingProxyConnection PendingProxyConnection;
#endif /* defined __cplusplus */
#endif /* defined __PendingProxyConnection_FWD_DEFINED__ */

#ifndef __DataChannel_FWD_DEFINED__
#define __DataChannel_FWD_DEFINED__
#ifdef __cplusplus
typedef class DataChannel DataChannel;
#else
typedef struct DataChannel DataChannel;
#endif /* defined __cplusplus */
#endif /* defined __DataChannel_FWD_DEFINED__ */

#ifndef __PersistentDataChannel_FWD_DEFINED__
#define __PersistentDataChannel_FWD_DEFINED__
#ifdef __cplusplus
typedef class PersistentDataChannel PersistentDataChannel;
#else
typedef struct PersistentDataChannel PersistentDataChannel;
#endif /* defined __cplusplus */
#endif /* defined __PersistentDataChannel_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef enum _ALG_ADAPTER_TYPE {
    eALG_PRIVATE = 0x1,
    eALG_BOUNDARY = 0x2,
    eALG_FIREWALLED = 0x4
} ALG_ADAPTER_TYPE;
typedef enum _ALG_CAPTURE {
    eALG_SOURCE_CAPTURE = 0x1,
    eALG_DESTINATION_CAPTURE = 0x2
} ALG_CAPTURE;
typedef enum _ALG_DIRECTION {
    eALG_INBOUND = 0x1,
    eALG_OUTBOUND = 0x2,
    eALG_BOTH = 0x3
} ALG_DIRECTION;
typedef enum _ALG_NOTIFICATION {
    eALG_NONE = 0x0,
    eALG_SESSION_CREATION = 0x1,
    eALG_SESSION_DELETION = 0x2,
    eALG_SESSION_BOTH = 0x3
} ALG_NOTIFICATION;
typedef enum _ALG_PROTOCOL {
    eALG_TCP = 0x1,
    eALG_UDP = 0x2
} ALG_PROTOCOL;
typedef struct _ALG_PRIMARY_CHANNEL_PROPERTIES {
    ALG_PROTOCOL eProtocol;
    USHORT usCapturePort;
    ALG_CAPTURE eCaptureType;
    WINBOOL fCaptureInbound;
    ULONG ulListeningAddress;
    USHORT usListeningPort;
    ULONG ulAdapterIndex;
} ALG_PRIMARY_CHANNEL_PROPERTIES;
typedef struct _ALG_SECONDARY_CHANNEL_PROPERTIES {
    ALG_PROTOCOL eProtocol;
    ULONG ulPrivateAddress;
    USHORT usPrivatePort;
    ULONG ulPublicAddress;
    USHORT usPublicPort;
    ULONG ulRemoteAddress;
    USHORT usRemotePort;
    ULONG ulListenAddress;
    USHORT usListenPort;
    ALG_DIRECTION eDirection;
    WINBOOL fPersistent;
} ALG_SECONDARY_CHANNEL_PROPERTIES;
typedef struct _ALG_DATA_CHANNEL_PROPERTIES {
    ALG_PROTOCOL eProtocol;
    ULONG ulPrivateAddress;
    USHORT usPrivatePort;
    ULONG ulPublicAddress;
    USHORT usPublicPort;
    ULONG ulRemoteAddress;
    USHORT usRemotePort;
    ALG_DIRECTION eDirection;
    ALG_NOTIFICATION eDesiredNotification;
} ALG_DATA_CHANNEL_PROPERTIES;
typedef struct _ALG_PERSISTENT_DATA_CHANNEL_PROPERTIES {
    ALG_PROTOCOL eProtocol;
    ULONG ulPrivateAddress;
    USHORT usPrivatePort;
    ULONG ulPublicAddress;
    USHORT usPublicPort;
    ULONG ulRemoteAddress;
    USHORT usRemotePort;
    ALG_DIRECTION eDirection;
} ALG_PERSISTENT_DATA_CHANNEL_PROPERTIES;
#define ALG_MAXIMUM_PORT_RANGE_SIZE (10)

/*****************************************************************************
 * IAdapterInfo interface
 */
#ifndef __IAdapterInfo_INTERFACE_DEFINED__
#define __IAdapterInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAdapterInfo, 0x480bf94a, 0x09fd, 0x4f8a, 0xa3,0xe0, 0xb0,0x70,0x02,0x82,0xd8,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("480bf94a-09fd-4f8a-a3e0-b0700282d84d")
IAdapterInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetAdapterIndex(
        ULONG *pulIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAdapterType(
        ALG_ADAPTER_TYPE *pAdapterType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAdapterAddresses(
        ULONG *pulAddressCount,
        ULONG **prgAddresses) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAdapterInfo, 0x480bf94a, 0x09fd, 0x4f8a, 0xa3,0xe0, 0xb0,0x70,0x02,0x82,0xd8,0x4d)
#endif
#else
typedef struct IAdapterInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAdapterInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAdapterInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAdapterInfo *This);

    /*** IAdapterInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAdapterIndex)(
        IAdapterInfo *This,
        ULONG *pulIndex);

    HRESULT (STDMETHODCALLTYPE *GetAdapterType)(
        IAdapterInfo *This,
        ALG_ADAPTER_TYPE *pAdapterType);

    HRESULT (STDMETHODCALLTYPE *GetAdapterAddresses)(
        IAdapterInfo *This,
        ULONG *pulAddressCount,
        ULONG **prgAddresses);

    END_INTERFACE
} IAdapterInfoVtbl;

interface IAdapterInfo {
    CONST_VTBL IAdapterInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAdapterInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAdapterInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAdapterInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IAdapterInfo methods ***/
#define IAdapterInfo_GetAdapterIndex(This,pulIndex) (This)->lpVtbl->GetAdapterIndex(This,pulIndex)
#define IAdapterInfo_GetAdapterType(This,pAdapterType) (This)->lpVtbl->GetAdapterType(This,pAdapterType)
#define IAdapterInfo_GetAdapterAddresses(This,pulAddressCount,prgAddresses) (This)->lpVtbl->GetAdapterAddresses(This,pulAddressCount,prgAddresses)
#else
/*** IUnknown methods ***/
static inline HRESULT IAdapterInfo_QueryInterface(IAdapterInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAdapterInfo_AddRef(IAdapterInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAdapterInfo_Release(IAdapterInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IAdapterInfo methods ***/
static inline HRESULT IAdapterInfo_GetAdapterIndex(IAdapterInfo* This,ULONG *pulIndex) {
    return This->lpVtbl->GetAdapterIndex(This,pulIndex);
}
static inline HRESULT IAdapterInfo_GetAdapterType(IAdapterInfo* This,ALG_ADAPTER_TYPE *pAdapterType) {
    return This->lpVtbl->GetAdapterType(This,pAdapterType);
}
static inline HRESULT IAdapterInfo_GetAdapterAddresses(IAdapterInfo* This,ULONG *pulAddressCount,ULONG **prgAddresses) {
    return This->lpVtbl->GetAdapterAddresses(This,pulAddressCount,prgAddresses);
}
#endif
#endif

#endif


#endif  /* __IAdapterInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPendingProxyConnection interface
 */
#ifndef __IPendingProxyConnection_INTERFACE_DEFINED__
#define __IPendingProxyConnection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPendingProxyConnection, 0xb68e5043, 0x3e3d, 0x4cc2, 0xb9,0xc1, 0x5f,0x8f,0x88,0xfe,0xe8,0x1c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b68e5043-3e3d-4cc2-b9c1-5f8f88fee81c")
IPendingProxyConnection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPendingProxyConnection, 0xb68e5043, 0x3e3d, 0x4cc2, 0xb9,0xc1, 0x5f,0x8f,0x88,0xfe,0xe8,0x1c)
#endif
#else
typedef struct IPendingProxyConnectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPendingProxyConnection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPendingProxyConnection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPendingProxyConnection *This);

    /*** IPendingProxyConnection methods ***/
    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPendingProxyConnection *This);

    END_INTERFACE
} IPendingProxyConnectionVtbl;

interface IPendingProxyConnection {
    CONST_VTBL IPendingProxyConnectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPendingProxyConnection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPendingProxyConnection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPendingProxyConnection_Release(This) (This)->lpVtbl->Release(This)
/*** IPendingProxyConnection methods ***/
#define IPendingProxyConnection_Cancel(This) (This)->lpVtbl->Cancel(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IPendingProxyConnection_QueryInterface(IPendingProxyConnection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPendingProxyConnection_AddRef(IPendingProxyConnection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPendingProxyConnection_Release(IPendingProxyConnection* This) {
    return This->lpVtbl->Release(This);
}
/*** IPendingProxyConnection methods ***/
static inline HRESULT IPendingProxyConnection_Cancel(IPendingProxyConnection* This) {
    return This->lpVtbl->Cancel(This);
}
#endif
#endif

#endif


#endif  /* __IPendingProxyConnection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDataChannel interface
 */
#ifndef __IDataChannel_INTERFACE_DEFINED__
#define __IDataChannel_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDataChannel, 0xad42d12a, 0x4ad0, 0x4856, 0x91,0x9e, 0xe8,0x54,0xc9,0x1d,0x18,0x56);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ad42d12a-4ad0-4856-919e-e854c91d1856")
IDataChannel : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChannelProperties(
        ALG_DATA_CHANNEL_PROPERTIES **ppProperties) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSessionCreationEventHandle(
        HANDLE *pHandle) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSessionDeletionEventHandle(
        HANDLE *pHandle) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDataChannel, 0xad42d12a, 0x4ad0, 0x4856, 0x91,0x9e, 0xe8,0x54,0xc9,0x1d,0x18,0x56)
#endif
#else
typedef struct IDataChannelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDataChannel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDataChannel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDataChannel *This);

    /*** IDataChannel methods ***/
    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IDataChannel *This);

    HRESULT (STDMETHODCALLTYPE *GetChannelProperties)(
        IDataChannel *This,
        ALG_DATA_CHANNEL_PROPERTIES **ppProperties);

    HRESULT (STDMETHODCALLTYPE *GetSessionCreationEventHandle)(
        IDataChannel *This,
        HANDLE *pHandle);

    HRESULT (STDMETHODCALLTYPE *GetSessionDeletionEventHandle)(
        IDataChannel *This,
        HANDLE *pHandle);

    END_INTERFACE
} IDataChannelVtbl;

interface IDataChannel {
    CONST_VTBL IDataChannelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDataChannel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDataChannel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDataChannel_Release(This) (This)->lpVtbl->Release(This)
/*** IDataChannel methods ***/
#define IDataChannel_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IDataChannel_GetChannelProperties(This,ppProperties) (This)->lpVtbl->GetChannelProperties(This,ppProperties)
#define IDataChannel_GetSessionCreationEventHandle(This,pHandle) (This)->lpVtbl->GetSessionCreationEventHandle(This,pHandle)
#define IDataChannel_GetSessionDeletionEventHandle(This,pHandle) (This)->lpVtbl->GetSessionDeletionEventHandle(This,pHandle)
#else
/*** IUnknown methods ***/
static inline HRESULT IDataChannel_QueryInterface(IDataChannel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDataChannel_AddRef(IDataChannel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDataChannel_Release(IDataChannel* This) {
    return This->lpVtbl->Release(This);
}
/*** IDataChannel methods ***/
static inline HRESULT IDataChannel_Cancel(IDataChannel* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT IDataChannel_GetChannelProperties(IDataChannel* This,ALG_DATA_CHANNEL_PROPERTIES **ppProperties) {
    return This->lpVtbl->GetChannelProperties(This,ppProperties);
}
static inline HRESULT IDataChannel_GetSessionCreationEventHandle(IDataChannel* This,HANDLE *pHandle) {
    return This->lpVtbl->GetSessionCreationEventHandle(This,pHandle);
}
static inline HRESULT IDataChannel_GetSessionDeletionEventHandle(IDataChannel* This,HANDLE *pHandle) {
    return This->lpVtbl->GetSessionDeletionEventHandle(This,pHandle);
}
#endif
#endif

#endif


#endif  /* __IDataChannel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPersistentDataChannel interface
 */
#ifndef __IPersistentDataChannel_INTERFACE_DEFINED__
#define __IPersistentDataChannel_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPersistentDataChannel, 0xa180e934, 0xd92a, 0x415d, 0x91,0x44, 0x75,0x9f,0x80,0x54,0xe8,0xf6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a180e934-d92a-415d-9144-759f8054e8f6")
IPersistentDataChannel : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChannelProperties(
        ALG_PERSISTENT_DATA_CHANNEL_PROPERTIES **ppProperties) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPersistentDataChannel, 0xa180e934, 0xd92a, 0x415d, 0x91,0x44, 0x75,0x9f,0x80,0x54,0xe8,0xf6)
#endif
#else
typedef struct IPersistentDataChannelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPersistentDataChannel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPersistentDataChannel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPersistentDataChannel *This);

    /*** IPersistentDataChannel methods ***/
    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPersistentDataChannel *This);

    HRESULT (STDMETHODCALLTYPE *GetChannelProperties)(
        IPersistentDataChannel *This,
        ALG_PERSISTENT_DATA_CHANNEL_PROPERTIES **ppProperties);

    END_INTERFACE
} IPersistentDataChannelVtbl;

interface IPersistentDataChannel {
    CONST_VTBL IPersistentDataChannelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPersistentDataChannel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPersistentDataChannel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPersistentDataChannel_Release(This) (This)->lpVtbl->Release(This)
/*** IPersistentDataChannel methods ***/
#define IPersistentDataChannel_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IPersistentDataChannel_GetChannelProperties(This,ppProperties) (This)->lpVtbl->GetChannelProperties(This,ppProperties)
#else
/*** IUnknown methods ***/
static inline HRESULT IPersistentDataChannel_QueryInterface(IPersistentDataChannel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPersistentDataChannel_AddRef(IPersistentDataChannel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPersistentDataChannel_Release(IPersistentDataChannel* This) {
    return This->lpVtbl->Release(This);
}
/*** IPersistentDataChannel methods ***/
static inline HRESULT IPersistentDataChannel_Cancel(IPersistentDataChannel* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT IPersistentDataChannel_GetChannelProperties(IPersistentDataChannel* This,ALG_PERSISTENT_DATA_CHANNEL_PROPERTIES **ppProperties) {
    return This->lpVtbl->GetChannelProperties(This,ppProperties);
}
#endif
#endif

#endif


#endif  /* __IPersistentDataChannel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IPrimaryControlChannel interface
 */
#ifndef __IPrimaryControlChannel_INTERFACE_DEFINED__
#define __IPrimaryControlChannel_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPrimaryControlChannel, 0x1a2e8b62, 0x9012, 0x4be6, 0x84,0xae, 0x32,0xbd,0x66,0xba,0x65,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1a2e8b62-9012-4be6-84ae-32bd66ba657a")
IPrimaryControlChannel : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChannelProperties(
        ALG_PRIMARY_CHANNEL_PROPERTIES **ppProperties) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOriginalDestinationInformation(
        ULONG ulSourceAddress,
        USHORT usSourcePort,
        ULONG *pulOriginalDestinationAddress,
        USHORT *pusOriginalDestinationPort,
        ULONG *pulRemapDestinationAddress,
        USHORT *pulRemapDestinationPort,
        IAdapterInfo **ppReceiveAdapter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPrimaryControlChannel, 0x1a2e8b62, 0x9012, 0x4be6, 0x84,0xae, 0x32,0xbd,0x66,0xba,0x65,0x7a)
#endif
#else
typedef struct IPrimaryControlChannelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPrimaryControlChannel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPrimaryControlChannel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPrimaryControlChannel *This);

    /*** IPrimaryControlChannel methods ***/
    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IPrimaryControlChannel *This);

    HRESULT (STDMETHODCALLTYPE *GetChannelProperties)(
        IPrimaryControlChannel *This,
        ALG_PRIMARY_CHANNEL_PROPERTIES **ppProperties);

    HRESULT (STDMETHODCALLTYPE *GetOriginalDestinationInformation)(
        IPrimaryControlChannel *This,
        ULONG ulSourceAddress,
        USHORT usSourcePort,
        ULONG *pulOriginalDestinationAddress,
        USHORT *pusOriginalDestinationPort,
        ULONG *pulRemapDestinationAddress,
        USHORT *pulRemapDestinationPort,
        IAdapterInfo **ppReceiveAdapter);

    END_INTERFACE
} IPrimaryControlChannelVtbl;

interface IPrimaryControlChannel {
    CONST_VTBL IPrimaryControlChannelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPrimaryControlChannel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPrimaryControlChannel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPrimaryControlChannel_Release(This) (This)->lpVtbl->Release(This)
/*** IPrimaryControlChannel methods ***/
#define IPrimaryControlChannel_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IPrimaryControlChannel_GetChannelProperties(This,ppProperties) (This)->lpVtbl->GetChannelProperties(This,ppProperties)
#define IPrimaryControlChannel_GetOriginalDestinationInformation(This,ulSourceAddress,usSourcePort,pulOriginalDestinationAddress,pusOriginalDestinationPort,pulRemapDestinationAddress,pulRemapDestinationPort,ppReceiveAdapter) (This)->lpVtbl->GetOriginalDestinationInformation(This,ulSourceAddress,usSourcePort,pulOriginalDestinationAddress,pusOriginalDestinationPort,pulRemapDestinationAddress,pulRemapDestinationPort,ppReceiveAdapter)
#else
/*** IUnknown methods ***/
static inline HRESULT IPrimaryControlChannel_QueryInterface(IPrimaryControlChannel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPrimaryControlChannel_AddRef(IPrimaryControlChannel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPrimaryControlChannel_Release(IPrimaryControlChannel* This) {
    return This->lpVtbl->Release(This);
}
/*** IPrimaryControlChannel methods ***/
static inline HRESULT IPrimaryControlChannel_Cancel(IPrimaryControlChannel* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT IPrimaryControlChannel_GetChannelProperties(IPrimaryControlChannel* This,ALG_PRIMARY_CHANNEL_PROPERTIES **ppProperties) {
    return This->lpVtbl->GetChannelProperties(This,ppProperties);
}
static inline HRESULT IPrimaryControlChannel_GetOriginalDestinationInformation(IPrimaryControlChannel* This,ULONG ulSourceAddress,USHORT usSourcePort,ULONG *pulOriginalDestinationAddress,USHORT *pusOriginalDestinationPort,ULONG *pulRemapDestinationAddress,USHORT *pulRemapDestinationPort,IAdapterInfo **ppReceiveAdapter) {
    return This->lpVtbl->GetOriginalDestinationInformation(This,ulSourceAddress,usSourcePort,pulOriginalDestinationAddress,pusOriginalDestinationPort,pulRemapDestinationAddress,pulRemapDestinationPort,ppReceiveAdapter);
}
#endif
#endif

#endif


#endif  /* __IPrimaryControlChannel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISecondaryControlChannel interface
 */
#ifndef __ISecondaryControlChannel_INTERFACE_DEFINED__
#define __ISecondaryControlChannel_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISecondaryControlChannel, 0xa23f9d10, 0x714c, 0x41fe, 0x84,0x71, 0xff,0xb1,0x9b,0xc2,0x84,0x54);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a23f9d10-714c-41fe-8471-ffb19bc28454")
ISecondaryControlChannel : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChannelProperties(
        ALG_SECONDARY_CHANNEL_PROPERTIES **ppProperties) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOriginalDestinationInformation(
        ULONG ulSourceAddress,
        USHORT usSourcePort,
        ULONG *pulOriginalDestinationAddress,
        USHORT *pusOriginalDestinationPort,
        IAdapterInfo **ppReceiveAdapter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISecondaryControlChannel, 0xa23f9d10, 0x714c, 0x41fe, 0x84,0x71, 0xff,0xb1,0x9b,0xc2,0x84,0x54)
#endif
#else
typedef struct ISecondaryControlChannelVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISecondaryControlChannel *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISecondaryControlChannel *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISecondaryControlChannel *This);

    /*** ISecondaryControlChannel methods ***/
    HRESULT (STDMETHODCALLTYPE *Cancel)(
        ISecondaryControlChannel *This);

    HRESULT (STDMETHODCALLTYPE *GetChannelProperties)(
        ISecondaryControlChannel *This,
        ALG_SECONDARY_CHANNEL_PROPERTIES **ppProperties);

    HRESULT (STDMETHODCALLTYPE *GetOriginalDestinationInformation)(
        ISecondaryControlChannel *This,
        ULONG ulSourceAddress,
        USHORT usSourcePort,
        ULONG *pulOriginalDestinationAddress,
        USHORT *pusOriginalDestinationPort,
        IAdapterInfo **ppReceiveAdapter);

    END_INTERFACE
} ISecondaryControlChannelVtbl;

interface ISecondaryControlChannel {
    CONST_VTBL ISecondaryControlChannelVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISecondaryControlChannel_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISecondaryControlChannel_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISecondaryControlChannel_Release(This) (This)->lpVtbl->Release(This)
/*** ISecondaryControlChannel methods ***/
#define ISecondaryControlChannel_Cancel(This) (This)->lpVtbl->Cancel(This)
#define ISecondaryControlChannel_GetChannelProperties(This,ppProperties) (This)->lpVtbl->GetChannelProperties(This,ppProperties)
#define ISecondaryControlChannel_GetOriginalDestinationInformation(This,ulSourceAddress,usSourcePort,pulOriginalDestinationAddress,pusOriginalDestinationPort,ppReceiveAdapter) (This)->lpVtbl->GetOriginalDestinationInformation(This,ulSourceAddress,usSourcePort,pulOriginalDestinationAddress,pusOriginalDestinationPort,ppReceiveAdapter)
#else
/*** IUnknown methods ***/
static inline HRESULT ISecondaryControlChannel_QueryInterface(ISecondaryControlChannel* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISecondaryControlChannel_AddRef(ISecondaryControlChannel* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISecondaryControlChannel_Release(ISecondaryControlChannel* This) {
    return This->lpVtbl->Release(This);
}
/*** ISecondaryControlChannel methods ***/
static inline HRESULT ISecondaryControlChannel_Cancel(ISecondaryControlChannel* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT ISecondaryControlChannel_GetChannelProperties(ISecondaryControlChannel* This,ALG_SECONDARY_CHANNEL_PROPERTIES **ppProperties) {
    return This->lpVtbl->GetChannelProperties(This,ppProperties);
}
static inline HRESULT ISecondaryControlChannel_GetOriginalDestinationInformation(ISecondaryControlChannel* This,ULONG ulSourceAddress,USHORT usSourcePort,ULONG *pulOriginalDestinationAddress,USHORT *pusOriginalDestinationPort,IAdapterInfo **ppReceiveAdapter) {
    return This->lpVtbl->GetOriginalDestinationInformation(This,ulSourceAddress,usSourcePort,pulOriginalDestinationAddress,pusOriginalDestinationPort,ppReceiveAdapter);
}
#endif
#endif

#endif


#endif  /* __ISecondaryControlChannel_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnumAdapterInfo interface
 */
#ifndef __IEnumAdapterInfo_INTERFACE_DEFINED__
#define __IEnumAdapterInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumAdapterInfo, 0xa23f9d11, 0x714c, 0x41fe, 0x84,0x71, 0xff,0xb1,0x9b,0xc2,0x84,0x54);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a23f9d11-714c-41fe-8471-ffb19bc28454")
IEnumAdapterInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        IAdapterInfo **rgAI,
        ULONG *pCeltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumAdapterInfo **ppEnum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumAdapterInfo, 0xa23f9d11, 0x714c, 0x41fe, 0x84,0x71, 0xff,0xb1,0x9b,0xc2,0x84,0x54)
#endif
#else
typedef struct IEnumAdapterInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumAdapterInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumAdapterInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumAdapterInfo *This);

    /*** IEnumAdapterInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumAdapterInfo *This,
        ULONG celt,
        IAdapterInfo **rgAI,
        ULONG *pCeltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumAdapterInfo *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumAdapterInfo *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumAdapterInfo *This,
        IEnumAdapterInfo **ppEnum);

    END_INTERFACE
} IEnumAdapterInfoVtbl;

interface IEnumAdapterInfo {
    CONST_VTBL IEnumAdapterInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumAdapterInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumAdapterInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumAdapterInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumAdapterInfo methods ***/
#define IEnumAdapterInfo_Next(This,celt,rgAI,pCeltFetched) (This)->lpVtbl->Next(This,celt,rgAI,pCeltFetched)
#define IEnumAdapterInfo_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumAdapterInfo_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumAdapterInfo_Clone(This,ppEnum) (This)->lpVtbl->Clone(This,ppEnum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumAdapterInfo_QueryInterface(IEnumAdapterInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumAdapterInfo_AddRef(IEnumAdapterInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumAdapterInfo_Release(IEnumAdapterInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumAdapterInfo methods ***/
static inline HRESULT IEnumAdapterInfo_Next(IEnumAdapterInfo* This,ULONG celt,IAdapterInfo **rgAI,ULONG *pCeltFetched) {
    return This->lpVtbl->Next(This,celt,rgAI,pCeltFetched);
}
static inline HRESULT IEnumAdapterInfo_Skip(IEnumAdapterInfo* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumAdapterInfo_Reset(IEnumAdapterInfo* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumAdapterInfo_Clone(IEnumAdapterInfo* This,IEnumAdapterInfo **ppEnum) {
    return This->lpVtbl->Clone(This,ppEnum);
}
#endif
#endif

#endif


#endif  /* __IEnumAdapterInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAdapterNotificationSink interface
 */
#ifndef __IAdapterNotificationSink_INTERFACE_DEFINED__
#define __IAdapterNotificationSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAdapterNotificationSink, 0x44ab2dc3, 0x23b2, 0x47de, 0x82,0x28, 0x2e,0x1c,0xce,0xeb,0x99,0x11);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("44ab2dc3-23b2-47de-8228-2e1cceeb9911")
IAdapterNotificationSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AdapterAdded(
        IAdapterInfo *pAdapter) = 0;

    virtual HRESULT STDMETHODCALLTYPE AdapterRemoved(
        IAdapterInfo *pAdapter) = 0;

    virtual HRESULT STDMETHODCALLTYPE AdapterModified(
        IAdapterInfo *pAdapter) = 0;

    virtual HRESULT STDMETHODCALLTYPE AdapterUpdatePortMapping(
        IAdapterInfo *pAdapter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAdapterNotificationSink, 0x44ab2dc3, 0x23b2, 0x47de, 0x82,0x28, 0x2e,0x1c,0xce,0xeb,0x99,0x11)
#endif
#else
typedef struct IAdapterNotificationSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAdapterNotificationSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAdapterNotificationSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAdapterNotificationSink *This);

    /*** IAdapterNotificationSink methods ***/
    HRESULT (STDMETHODCALLTYPE *AdapterAdded)(
        IAdapterNotificationSink *This,
        IAdapterInfo *pAdapter);

    HRESULT (STDMETHODCALLTYPE *AdapterRemoved)(
        IAdapterNotificationSink *This,
        IAdapterInfo *pAdapter);

    HRESULT (STDMETHODCALLTYPE *AdapterModified)(
        IAdapterNotificationSink *This,
        IAdapterInfo *pAdapter);

    HRESULT (STDMETHODCALLTYPE *AdapterUpdatePortMapping)(
        IAdapterNotificationSink *This,
        IAdapterInfo *pAdapter);

    END_INTERFACE
} IAdapterNotificationSinkVtbl;

interface IAdapterNotificationSink {
    CONST_VTBL IAdapterNotificationSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAdapterNotificationSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAdapterNotificationSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAdapterNotificationSink_Release(This) (This)->lpVtbl->Release(This)
/*** IAdapterNotificationSink methods ***/
#define IAdapterNotificationSink_AdapterAdded(This,pAdapter) (This)->lpVtbl->AdapterAdded(This,pAdapter)
#define IAdapterNotificationSink_AdapterRemoved(This,pAdapter) (This)->lpVtbl->AdapterRemoved(This,pAdapter)
#define IAdapterNotificationSink_AdapterModified(This,pAdapter) (This)->lpVtbl->AdapterModified(This,pAdapter)
#define IAdapterNotificationSink_AdapterUpdatePortMapping(This,pAdapter) (This)->lpVtbl->AdapterUpdatePortMapping(This,pAdapter)
#else
/*** IUnknown methods ***/
static inline HRESULT IAdapterNotificationSink_QueryInterface(IAdapterNotificationSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAdapterNotificationSink_AddRef(IAdapterNotificationSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAdapterNotificationSink_Release(IAdapterNotificationSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IAdapterNotificationSink methods ***/
static inline HRESULT IAdapterNotificationSink_AdapterAdded(IAdapterNotificationSink* This,IAdapterInfo *pAdapter) {
    return This->lpVtbl->AdapterAdded(This,pAdapter);
}
static inline HRESULT IAdapterNotificationSink_AdapterRemoved(IAdapterNotificationSink* This,IAdapterInfo *pAdapter) {
    return This->lpVtbl->AdapterRemoved(This,pAdapter);
}
static inline HRESULT IAdapterNotificationSink_AdapterModified(IAdapterNotificationSink* This,IAdapterInfo *pAdapter) {
    return This->lpVtbl->AdapterModified(This,pAdapter);
}
static inline HRESULT IAdapterNotificationSink_AdapterUpdatePortMapping(IAdapterNotificationSink* This,IAdapterInfo *pAdapter) {
    return This->lpVtbl->AdapterUpdatePortMapping(This,pAdapter);
}
#endif
#endif

#endif


#endif  /* __IAdapterNotificationSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IApplicationGatewayServices interface
 */
#ifndef __IApplicationGatewayServices_INTERFACE_DEFINED__
#define __IApplicationGatewayServices_INTERFACE_DEFINED__

DEFINE_GUID(IID_IApplicationGatewayServices, 0x5134842a, 0xfdce, 0x485d, 0x93,0xcd, 0xde,0x16,0x40,0x64,0x3b,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5134842a-fdce-485d-93cd-de1640643bbe")
IApplicationGatewayServices : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreatePrimaryControlChannel(
        ULONG uAdapterIndex,
        ALG_PROTOCOL eProtocol,
        USHORT usPortToCapture,
        ALG_CAPTURE eCaptureType,
        WINBOOL fCaptureInbound,
        ULONG ulListenAddress,
        USHORT usListenPort,
        IPrimaryControlChannel **ppIControlChannel) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSecondaryControlChannel(
        ALG_PROTOCOL eProtocol,
        ULONG ulPrivateAddress,
        USHORT usPrivatePort,
        ULONG ulPublicAddress,
        USHORT usPublicPort,
        ULONG ulRemoteAddress,
        USHORT usRemotePort,
        ULONG ulListenAddress,
        USHORT usListenPort,
        ALG_DIRECTION eDirection,
        WINBOOL fPersistent,
        ISecondaryControlChannel **ppControlChannel) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBestSourceAddressForDestinationAddress(
        ULONG ulDstAddress,
        WINBOOL fDemandDial,
        ULONG *pulBestSrcAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE PrepareProxyConnection(
        ALG_PROTOCOL eProtocol,
        ULONG ulSrcAddress,
        USHORT usSrcPort,
        ULONG ulDstAddress,
        USHORT usDstPort,
        WINBOOL fNoTimeout,
        IPendingProxyConnection **ppPendingConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE PrepareSourceModifiedProxyConnection(
        ALG_PROTOCOL eProtocol,
        ULONG ulSrcAddress,
        USHORT usSrcPort,
        ULONG ulDstAddress,
        USHORT usDstPort,
        ULONG ulNewSrcAddress,
        USHORT usNewSourcePort,
        IPendingProxyConnection **ppPendingConnection) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDataChannel(
        ALG_PROTOCOL eProtocol,
        ULONG ulPrivateAddress,
        USHORT usPrivatePort,
        ULONG ulPublicAddress,
        USHORT usPublicPort,
        ULONG ulRemoteAddress,
        USHORT usRemotePort,
        ALG_DIRECTION eDirection,
        ALG_NOTIFICATION eDesiredNotification,
        WINBOOL fNoTimeout,
        IDataChannel **ppDataChannel) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePersistentDataChannel(
        ALG_PROTOCOL eProtocol,
        ULONG ulPrivateAddress,
        USHORT usPrivatePort,
        ULONG ulPublicAddress,
        USHORT usPublicPort,
        ULONG ulRemoteAddress,
        USHORT usRemotePort,
        ALG_DIRECTION eDirection,
        IPersistentDataChannel **ppIPersistentDataChannel) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReservePort(
        USHORT usPortCount,
        USHORT *pusReservedPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseReservedPort(
        USHORT usReservedPortBase,
        USHORT usPortCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumerateAdapters(
        IEnumAdapterInfo **ppIEnumAdapterInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartAdapterNotifications(
        IAdapterNotificationSink *pSink,
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopAdapterNotifications(
        DWORD dwCookieOfSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE LookupAdapterPortMapping(
        ULONG ulAdapterIndex,
        UCHAR Protocol,
        ULONG ulDestinationAddress,
        USHORT usDestinationPort,
        ULONG *pulRemapAddress,
        USHORT *pusRemapPort) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IApplicationGatewayServices, 0x5134842a, 0xfdce, 0x485d, 0x93,0xcd, 0xde,0x16,0x40,0x64,0x3b,0xbe)
#endif
#else
typedef struct IApplicationGatewayServicesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IApplicationGatewayServices *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IApplicationGatewayServices *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IApplicationGatewayServices *This);

    /*** IApplicationGatewayServices methods ***/
    HRESULT (STDMETHODCALLTYPE *CreatePrimaryControlChannel)(
        IApplicationGatewayServices *This,
        ULONG uAdapterIndex,
        ALG_PROTOCOL eProtocol,
        USHORT usPortToCapture,
        ALG_CAPTURE eCaptureType,
        WINBOOL fCaptureInbound,
        ULONG ulListenAddress,
        USHORT usListenPort,
        IPrimaryControlChannel **ppIControlChannel);

    HRESULT (STDMETHODCALLTYPE *CreateSecondaryControlChannel)(
        IApplicationGatewayServices *This,
        ALG_PROTOCOL eProtocol,
        ULONG ulPrivateAddress,
        USHORT usPrivatePort,
        ULONG ulPublicAddress,
        USHORT usPublicPort,
        ULONG ulRemoteAddress,
        USHORT usRemotePort,
        ULONG ulListenAddress,
        USHORT usListenPort,
        ALG_DIRECTION eDirection,
        WINBOOL fPersistent,
        ISecondaryControlChannel **ppControlChannel);

    HRESULT (STDMETHODCALLTYPE *GetBestSourceAddressForDestinationAddress)(
        IApplicationGatewayServices *This,
        ULONG ulDstAddress,
        WINBOOL fDemandDial,
        ULONG *pulBestSrcAddress);

    HRESULT (STDMETHODCALLTYPE *PrepareProxyConnection)(
        IApplicationGatewayServices *This,
        ALG_PROTOCOL eProtocol,
        ULONG ulSrcAddress,
        USHORT usSrcPort,
        ULONG ulDstAddress,
        USHORT usDstPort,
        WINBOOL fNoTimeout,
        IPendingProxyConnection **ppPendingConnection);

    HRESULT (STDMETHODCALLTYPE *PrepareSourceModifiedProxyConnection)(
        IApplicationGatewayServices *This,
        ALG_PROTOCOL eProtocol,
        ULONG ulSrcAddress,
        USHORT usSrcPort,
        ULONG ulDstAddress,
        USHORT usDstPort,
        ULONG ulNewSrcAddress,
        USHORT usNewSourcePort,
        IPendingProxyConnection **ppPendingConnection);

    HRESULT (STDMETHODCALLTYPE *CreateDataChannel)(
        IApplicationGatewayServices *This,
        ALG_PROTOCOL eProtocol,
        ULONG ulPrivateAddress,
        USHORT usPrivatePort,
        ULONG ulPublicAddress,
        USHORT usPublicPort,
        ULONG ulRemoteAddress,
        USHORT usRemotePort,
        ALG_DIRECTION eDirection,
        ALG_NOTIFICATION eDesiredNotification,
        WINBOOL fNoTimeout,
        IDataChannel **ppDataChannel);

    HRESULT (STDMETHODCALLTYPE *CreatePersistentDataChannel)(
        IApplicationGatewayServices *This,
        ALG_PROTOCOL eProtocol,
        ULONG ulPrivateAddress,
        USHORT usPrivatePort,
        ULONG ulPublicAddress,
        USHORT usPublicPort,
        ULONG ulRemoteAddress,
        USHORT usRemotePort,
        ALG_DIRECTION eDirection,
        IPersistentDataChannel **ppIPersistentDataChannel);

    HRESULT (STDMETHODCALLTYPE *ReservePort)(
        IApplicationGatewayServices *This,
        USHORT usPortCount,
        USHORT *pusReservedPort);

    HRESULT (STDMETHODCALLTYPE *ReleaseReservedPort)(
        IApplicationGatewayServices *This,
        USHORT usReservedPortBase,
        USHORT usPortCount);

    HRESULT (STDMETHODCALLTYPE *EnumerateAdapters)(
        IApplicationGatewayServices *This,
        IEnumAdapterInfo **ppIEnumAdapterInfo);

    HRESULT (STDMETHODCALLTYPE *StartAdapterNotifications)(
        IApplicationGatewayServices *This,
        IAdapterNotificationSink *pSink,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *StopAdapterNotifications)(
        IApplicationGatewayServices *This,
        DWORD dwCookieOfSink);

    HRESULT (STDMETHODCALLTYPE *LookupAdapterPortMapping)(
        IApplicationGatewayServices *This,
        ULONG ulAdapterIndex,
        UCHAR Protocol,
        ULONG ulDestinationAddress,
        USHORT usDestinationPort,
        ULONG *pulRemapAddress,
        USHORT *pusRemapPort);

    END_INTERFACE
} IApplicationGatewayServicesVtbl;

interface IApplicationGatewayServices {
    CONST_VTBL IApplicationGatewayServicesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IApplicationGatewayServices_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IApplicationGatewayServices_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IApplicationGatewayServices_Release(This) (This)->lpVtbl->Release(This)
/*** IApplicationGatewayServices methods ***/
#define IApplicationGatewayServices_CreatePrimaryControlChannel(This,uAdapterIndex,eProtocol,usPortToCapture,eCaptureType,fCaptureInbound,ulListenAddress,usListenPort,ppIControlChannel) (This)->lpVtbl->CreatePrimaryControlChannel(This,uAdapterIndex,eProtocol,usPortToCapture,eCaptureType,fCaptureInbound,ulListenAddress,usListenPort,ppIControlChannel)
#define IApplicationGatewayServices_CreateSecondaryControlChannel(This,eProtocol,ulPrivateAddress,usPrivatePort,ulPublicAddress,usPublicPort,ulRemoteAddress,usRemotePort,ulListenAddress,usListenPort,eDirection,fPersistent,ppControlChannel) (This)->lpVtbl->CreateSecondaryControlChannel(This,eProtocol,ulPrivateAddress,usPrivatePort,ulPublicAddress,usPublicPort,ulRemoteAddress,usRemotePort,ulListenAddress,usListenPort,eDirection,fPersistent,ppControlChannel)
#define IApplicationGatewayServices_GetBestSourceAddressForDestinationAddress(This,ulDstAddress,fDemandDial,pulBestSrcAddress) (This)->lpVtbl->GetBestSourceAddressForDestinationAddress(This,ulDstAddress,fDemandDial,pulBestSrcAddress)
#define IApplicationGatewayServices_PrepareProxyConnection(This,eProtocol,ulSrcAddress,usSrcPort,ulDstAddress,usDstPort,fNoTimeout,ppPendingConnection) (This)->lpVtbl->PrepareProxyConnection(This,eProtocol,ulSrcAddress,usSrcPort,ulDstAddress,usDstPort,fNoTimeout,ppPendingConnection)
#define IApplicationGatewayServices_PrepareSourceModifiedProxyConnection(This,eProtocol,ulSrcAddress,usSrcPort,ulDstAddress,usDstPort,ulNewSrcAddress,usNewSourcePort,ppPendingConnection) (This)->lpVtbl->PrepareSourceModifiedProxyConnection(This,eProtocol,ulSrcAddress,usSrcPort,ulDstAddress,usDstPort,ulNewSrcAddress,usNewSourcePort,ppPendingConnection)
#define IApplicationGatewayServices_CreateDataChannel(This,eProtocol,ulPrivateAddress,usPrivatePort,ulPublicAddress,usPublicPort,ulRemoteAddress,usRemotePort,eDirection,eDesiredNotification,fNoTimeout,ppDataChannel) (This)->lpVtbl->CreateDataChannel(This,eProtocol,ulPrivateAddress,usPrivatePort,ulPublicAddress,usPublicPort,ulRemoteAddress,usRemotePort,eDirection,eDesiredNotification,fNoTimeout,ppDataChannel)
#define IApplicationGatewayServices_CreatePersistentDataChannel(This,eProtocol,ulPrivateAddress,usPrivatePort,ulPublicAddress,usPublicPort,ulRemoteAddress,usRemotePort,eDirection,ppIPersistentDataChannel) (This)->lpVtbl->CreatePersistentDataChannel(This,eProtocol,ulPrivateAddress,usPrivatePort,ulPublicAddress,usPublicPort,ulRemoteAddress,usRemotePort,eDirection,ppIPersistentDataChannel)
#define IApplicationGatewayServices_ReservePort(This,usPortCount,pusReservedPort) (This)->lpVtbl->ReservePort(This,usPortCount,pusReservedPort)
#define IApplicationGatewayServices_ReleaseReservedPort(This,usReservedPortBase,usPortCount) (This)->lpVtbl->ReleaseReservedPort(This,usReservedPortBase,usPortCount)
#define IApplicationGatewayServices_EnumerateAdapters(This,ppIEnumAdapterInfo) (This)->lpVtbl->EnumerateAdapters(This,ppIEnumAdapterInfo)
#define IApplicationGatewayServices_StartAdapterNotifications(This,pSink,pdwCookie) (This)->lpVtbl->StartAdapterNotifications(This,pSink,pdwCookie)
#define IApplicationGatewayServices_StopAdapterNotifications(This,dwCookieOfSink) (This)->lpVtbl->StopAdapterNotifications(This,dwCookieOfSink)
#define IApplicationGatewayServices_LookupAdapterPortMapping(This,ulAdapterIndex,Protocol,ulDestinationAddress,usDestinationPort,pulRemapAddress,pusRemapPort) (This)->lpVtbl->LookupAdapterPortMapping(This,ulAdapterIndex,Protocol,ulDestinationAddress,usDestinationPort,pulRemapAddress,pusRemapPort)
#else
/*** IUnknown methods ***/
static inline HRESULT IApplicationGatewayServices_QueryInterface(IApplicationGatewayServices* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IApplicationGatewayServices_AddRef(IApplicationGatewayServices* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IApplicationGatewayServices_Release(IApplicationGatewayServices* This) {
    return This->lpVtbl->Release(This);
}
/*** IApplicationGatewayServices methods ***/
static inline HRESULT IApplicationGatewayServices_CreatePrimaryControlChannel(IApplicationGatewayServices* This,ULONG uAdapterIndex,ALG_PROTOCOL eProtocol,USHORT usPortToCapture,ALG_CAPTURE eCaptureType,WINBOOL fCaptureInbound,ULONG ulListenAddress,USHORT usListenPort,IPrimaryControlChannel **ppIControlChannel) {
    return This->lpVtbl->CreatePrimaryControlChannel(This,uAdapterIndex,eProtocol,usPortToCapture,eCaptureType,fCaptureInbound,ulListenAddress,usListenPort,ppIControlChannel);
}
static inline HRESULT IApplicationGatewayServices_CreateSecondaryControlChannel(IApplicationGatewayServices* This,ALG_PROTOCOL eProtocol,ULONG ulPrivateAddress,USHORT usPrivatePort,ULONG ulPublicAddress,USHORT usPublicPort,ULONG ulRemoteAddress,USHORT usRemotePort,ULONG ulListenAddress,USHORT usListenPort,ALG_DIRECTION eDirection,WINBOOL fPersistent,ISecondaryControlChannel **ppControlChannel) {
    return This->lpVtbl->CreateSecondaryControlChannel(This,eProtocol,ulPrivateAddress,usPrivatePort,ulPublicAddress,usPublicPort,ulRemoteAddress,usRemotePort,ulListenAddress,usListenPort,eDirection,fPersistent,ppControlChannel);
}
static inline HRESULT IApplicationGatewayServices_GetBestSourceAddressForDestinationAddress(IApplicationGatewayServices* This,ULONG ulDstAddress,WINBOOL fDemandDial,ULONG *pulBestSrcAddress) {
    return This->lpVtbl->GetBestSourceAddressForDestinationAddress(This,ulDstAddress,fDemandDial,pulBestSrcAddress);
}
static inline HRESULT IApplicationGatewayServices_PrepareProxyConnection(IApplicationGatewayServices* This,ALG_PROTOCOL eProtocol,ULONG ulSrcAddress,USHORT usSrcPort,ULONG ulDstAddress,USHORT usDstPort,WINBOOL fNoTimeout,IPendingProxyConnection **ppPendingConnection) {
    return This->lpVtbl->PrepareProxyConnection(This,eProtocol,ulSrcAddress,usSrcPort,ulDstAddress,usDstPort,fNoTimeout,ppPendingConnection);
}
static inline HRESULT IApplicationGatewayServices_PrepareSourceModifiedProxyConnection(IApplicationGatewayServices* This,ALG_PROTOCOL eProtocol,ULONG ulSrcAddress,USHORT usSrcPort,ULONG ulDstAddress,USHORT usDstPort,ULONG ulNewSrcAddress,USHORT usNewSourcePort,IPendingProxyConnection **ppPendingConnection) {
    return This->lpVtbl->PrepareSourceModifiedProxyConnection(This,eProtocol,ulSrcAddress,usSrcPort,ulDstAddress,usDstPort,ulNewSrcAddress,usNewSourcePort,ppPendingConnection);
}
static inline HRESULT IApplicationGatewayServices_CreateDataChannel(IApplicationGatewayServices* This,ALG_PROTOCOL eProtocol,ULONG ulPrivateAddress,USHORT usPrivatePort,ULONG ulPublicAddress,USHORT usPublicPort,ULONG ulRemoteAddress,USHORT usRemotePort,ALG_DIRECTION eDirection,ALG_NOTIFICATION eDesiredNotification,WINBOOL fNoTimeout,IDataChannel **ppDataChannel) {
    return This->lpVtbl->CreateDataChannel(This,eProtocol,ulPrivateAddress,usPrivatePort,ulPublicAddress,usPublicPort,ulRemoteAddress,usRemotePort,eDirection,eDesiredNotification,fNoTimeout,ppDataChannel);
}
static inline HRESULT IApplicationGatewayServices_CreatePersistentDataChannel(IApplicationGatewayServices* This,ALG_PROTOCOL eProtocol,ULONG ulPrivateAddress,USHORT usPrivatePort,ULONG ulPublicAddress,USHORT usPublicPort,ULONG ulRemoteAddress,USHORT usRemotePort,ALG_DIRECTION eDirection,IPersistentDataChannel **ppIPersistentDataChannel) {
    return This->lpVtbl->CreatePersistentDataChannel(This,eProtocol,ulPrivateAddress,usPrivatePort,ulPublicAddress,usPublicPort,ulRemoteAddress,usRemotePort,eDirection,ppIPersistentDataChannel);
}
static inline HRESULT IApplicationGatewayServices_ReservePort(IApplicationGatewayServices* This,USHORT usPortCount,USHORT *pusReservedPort) {
    return This->lpVtbl->ReservePort(This,usPortCount,pusReservedPort);
}
static inline HRESULT IApplicationGatewayServices_ReleaseReservedPort(IApplicationGatewayServices* This,USHORT usReservedPortBase,USHORT usPortCount) {
    return This->lpVtbl->ReleaseReservedPort(This,usReservedPortBase,usPortCount);
}
static inline HRESULT IApplicationGatewayServices_EnumerateAdapters(IApplicationGatewayServices* This,IEnumAdapterInfo **ppIEnumAdapterInfo) {
    return This->lpVtbl->EnumerateAdapters(This,ppIEnumAdapterInfo);
}
static inline HRESULT IApplicationGatewayServices_StartAdapterNotifications(IApplicationGatewayServices* This,IAdapterNotificationSink *pSink,DWORD *pdwCookie) {
    return This->lpVtbl->StartAdapterNotifications(This,pSink,pdwCookie);
}
static inline HRESULT IApplicationGatewayServices_StopAdapterNotifications(IApplicationGatewayServices* This,DWORD dwCookieOfSink) {
    return This->lpVtbl->StopAdapterNotifications(This,dwCookieOfSink);
}
static inline HRESULT IApplicationGatewayServices_LookupAdapterPortMapping(IApplicationGatewayServices* This,ULONG ulAdapterIndex,UCHAR Protocol,ULONG ulDestinationAddress,USHORT usDestinationPort,ULONG *pulRemapAddress,USHORT *pusRemapPort) {
    return This->lpVtbl->LookupAdapterPortMapping(This,ulAdapterIndex,Protocol,ulDestinationAddress,usDestinationPort,pulRemapAddress,pusRemapPort);
}
#endif
#endif

#endif


#endif  /* __IApplicationGatewayServices_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IApplicationGateway interface
 */
#ifndef __IApplicationGateway_INTERFACE_DEFINED__
#define __IApplicationGateway_INTERFACE_DEFINED__

DEFINE_GUID(IID_IApplicationGateway, 0x5134842b, 0xfdce, 0x485d, 0x93,0xcd, 0xde,0x16,0x40,0x64,0x3b,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5134842b-fdce-485d-93cd-de1640643bbe")
IApplicationGateway : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IApplicationGatewayServices *pAlgServices) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IApplicationGateway, 0x5134842b, 0xfdce, 0x485d, 0x93,0xcd, 0xde,0x16,0x40,0x64,0x3b,0xbe)
#endif
#else
typedef struct IApplicationGatewayVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IApplicationGateway *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IApplicationGateway *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IApplicationGateway *This);

    /*** IApplicationGateway methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IApplicationGateway *This,
        IApplicationGatewayServices *pAlgServices);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IApplicationGateway *This);

    END_INTERFACE
} IApplicationGatewayVtbl;

interface IApplicationGateway {
    CONST_VTBL IApplicationGatewayVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IApplicationGateway_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IApplicationGateway_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IApplicationGateway_Release(This) (This)->lpVtbl->Release(This)
/*** IApplicationGateway methods ***/
#define IApplicationGateway_Initialize(This,pAlgServices) (This)->lpVtbl->Initialize(This,pAlgServices)
#define IApplicationGateway_Stop(This) (This)->lpVtbl->Stop(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IApplicationGateway_QueryInterface(IApplicationGateway* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IApplicationGateway_AddRef(IApplicationGateway* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IApplicationGateway_Release(IApplicationGateway* This) {
    return This->lpVtbl->Release(This);
}
/*** IApplicationGateway methods ***/
static inline HRESULT IApplicationGateway_Initialize(IApplicationGateway* This,IApplicationGatewayServices *pAlgServices) {
    return This->lpVtbl->Initialize(This,pAlgServices);
}
static inline HRESULT IApplicationGateway_Stop(IApplicationGateway* This) {
    return This->lpVtbl->Stop(This);
}
#endif
#endif

#endif


#endif  /* __IApplicationGateway_INTERFACE_DEFINED__ */

#ifndef __ALGLib_LIBRARY_DEFINED__
#define __ALGLib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_ALGLib, 0xb6d1d098, 0xe235, 0x4b99, 0xba,0x98, 0x7c,0x62,0x4f,0xd8,0x75,0xdb);

/*****************************************************************************
 * ApplicationGatewayServices coclass
 */

DEFINE_GUID(CLSID_ApplicationGatewayServices, 0xf8ade1d3, 0x49df, 0x4b75, 0x90,0x05, 0xef,0x95,0x08,0xe6,0xa3,0x37);

#ifdef __cplusplus
class DECLSPEC_UUID("f8ade1d3-49df-4b75-9005-ef9508e6a337") ApplicationGatewayServices;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ApplicationGatewayServices, 0xf8ade1d3, 0x49df, 0x4b75, 0x90,0x05, 0xef,0x95,0x08,0xe6,0xa3,0x37)
#endif
#endif

/*****************************************************************************
 * PrimaryControlChannel coclass
 */

DEFINE_GUID(CLSID_PrimaryControlChannel, 0x3ceb5509, 0xc1cd, 0x432f, 0x9d,0x8f, 0x65,0xd1,0xe2,0x86,0xaa,0x80);

#ifdef __cplusplus
class DECLSPEC_UUID("3ceb5509-c1cd-432f-9d8f-65d1e286aa80") PrimaryControlChannel;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PrimaryControlChannel, 0x3ceb5509, 0xc1cd, 0x432f, 0x9d,0x8f, 0x65,0xd1,0xe2,0x86,0xaa,0x80)
#endif
#endif

/*****************************************************************************
 * SecondaryControlChannel coclass
 */

DEFINE_GUID(CLSID_SecondaryControlChannel, 0x7b3181a0, 0xc92f, 0x4567, 0xb0,0xfa, 0xcd,0x9a,0x10,0xec,0xd7,0xd1);

#ifdef __cplusplus
class DECLSPEC_UUID("7b3181a0-c92f-4567-b0fa-cd9a10ecd7d1") SecondaryControlChannel;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SecondaryControlChannel, 0x7b3181a0, 0xc92f, 0x4567, 0xb0,0xfa, 0xcd,0x9a,0x10,0xec,0xd7,0xd1)
#endif
#endif

/*****************************************************************************
 * AdapterInfo coclass
 */

DEFINE_GUID(CLSID_AdapterInfo, 0x6f9942c9, 0xc1b1, 0x4ab5, 0x93,0xda, 0x60,0x58,0x99,0x1d,0xc8,0xf3);

#ifdef __cplusplus
class DECLSPEC_UUID("6f9942c9-c1b1-4ab5-93da-6058991dc8f3") AdapterInfo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(AdapterInfo, 0x6f9942c9, 0xc1b1, 0x4ab5, 0x93,0xda, 0x60,0x58,0x99,0x1d,0xc8,0xf3)
#endif
#endif

/*****************************************************************************
 * EnumAdapterInfo coclass
 */

DEFINE_GUID(CLSID_EnumAdapterInfo, 0x6f9942ca, 0xc1b1, 0x4ab5, 0x93,0xda, 0x60,0x58,0x99,0x1d,0xc8,0xf3);

#ifdef __cplusplus
class DECLSPEC_UUID("6f9942ca-c1b1-4ab5-93da-6058991dc8f3") EnumAdapterInfo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(EnumAdapterInfo, 0x6f9942ca, 0xc1b1, 0x4ab5, 0x93,0xda, 0x60,0x58,0x99,0x1d,0xc8,0xf3)
#endif
#endif

/*****************************************************************************
 * PendingProxyConnection coclass
 */

DEFINE_GUID(CLSID_PendingProxyConnection, 0xd8a68e5e, 0x2b37, 0x426c, 0xa3,0x29, 0xc1,0x17,0xc1,0x4c,0x42,0x9e);

#ifdef __cplusplus
class DECLSPEC_UUID("d8a68e5e-2b37-426c-a329-c117c14c429e") PendingProxyConnection;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PendingProxyConnection, 0xd8a68e5e, 0x2b37, 0x426c, 0xa3,0x29, 0xc1,0x17,0xc1,0x4c,0x42,0x9e)
#endif
#endif

/*****************************************************************************
 * DataChannel coclass
 */

DEFINE_GUID(CLSID_DataChannel, 0xbbb36f15, 0x408d, 0x4056, 0x8c,0x27, 0x92,0x08,0x43,0xd4,0x0b,0xe5);

#ifdef __cplusplus
class DECLSPEC_UUID("bbb36f15-408d-4056-8c27-920843d40be5") DataChannel;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(DataChannel, 0xbbb36f15, 0x408d, 0x4056, 0x8c,0x27, 0x92,0x08,0x43,0xd4,0x0b,0xe5)
#endif
#endif

/*****************************************************************************
 * PersistentDataChannel coclass
 */

DEFINE_GUID(CLSID_PersistentDataChannel, 0xbc9b54ab, 0x7883, 0x4c13, 0x90,0x9f, 0x03,0x3d,0x03,0x26,0x79,0x90);

#ifdef __cplusplus
class DECLSPEC_UUID("bc9b54ab-7883-4c13-909f-033d03267990") PersistentDataChannel;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(PersistentDataChannel, 0xbc9b54ab, 0x7883, 0x4c13, 0x90,0x9f, 0x03,0x3d,0x03,0x26,0x79,0x90)
#endif
#endif

#endif /* __ALGLib_LIBRARY_DEFINED__ */
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __alg_h__ */
