/*** Autogenerated by WIDL 10.12 from include/msopc.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __msopc_h__
#define __msopc_h__

/* Forward declarations */

#ifndef __IOpcUri_FWD_DEFINED__
#define __IOpcUri_FWD_DEFINED__
typedef interface IOpcUri IOpcUri;
#ifdef __cplusplus
interface IOpcUri;
#endif /* __cplusplus */
#endif

#ifndef __IOpcPartUri_FWD_DEFINED__
#define __IOpcPartUri_FWD_DEFINED__
typedef interface IOpcPartUri IOpcPartUri;
#ifdef __cplusplus
interface IOpcPartUri;
#endif /* __cplusplus */
#endif

#ifndef __IOpcPart_FWD_DEFINED__
#define __IOpcPart_FWD_DEFINED__
typedef interface IOpcPart IOpcPart;
#ifdef __cplusplus
interface IOpcPart;
#endif /* __cplusplus */
#endif

#ifndef __IOpcPartEnumerator_FWD_DEFINED__
#define __IOpcPartEnumerator_FWD_DEFINED__
typedef interface IOpcPartEnumerator IOpcPartEnumerator;
#ifdef __cplusplus
interface IOpcPartEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcPartSet_FWD_DEFINED__
#define __IOpcPartSet_FWD_DEFINED__
typedef interface IOpcPartSet IOpcPartSet;
#ifdef __cplusplus
interface IOpcPartSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcPackage_FWD_DEFINED__
#define __IOpcPackage_FWD_DEFINED__
typedef interface IOpcPackage IOpcPackage;
#ifdef __cplusplus
interface IOpcPackage;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationship_FWD_DEFINED__
#define __IOpcRelationship_FWD_DEFINED__
typedef interface IOpcRelationship IOpcRelationship;
#ifdef __cplusplus
interface IOpcRelationship;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationshipEnumerator_FWD_DEFINED__
#define __IOpcRelationshipEnumerator_FWD_DEFINED__
typedef interface IOpcRelationshipEnumerator IOpcRelationshipEnumerator;
#ifdef __cplusplus
interface IOpcRelationshipEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationshipSelector_FWD_DEFINED__
#define __IOpcRelationshipSelector_FWD_DEFINED__
typedef interface IOpcRelationshipSelector IOpcRelationshipSelector;
#ifdef __cplusplus
interface IOpcRelationshipSelector;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationshipSet_FWD_DEFINED__
#define __IOpcRelationshipSet_FWD_DEFINED__
typedef interface IOpcRelationshipSet IOpcRelationshipSet;
#ifdef __cplusplus
interface IOpcRelationshipSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureCustomObject_FWD_DEFINED__
#define __IOpcSignatureCustomObject_FWD_DEFINED__
typedef interface IOpcSignatureCustomObject IOpcSignatureCustomObject;
#ifdef __cplusplus
interface IOpcSignatureCustomObject;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureCustomObjectEnumerator_FWD_DEFINED__
#define __IOpcSignatureCustomObjectEnumerator_FWD_DEFINED__
typedef interface IOpcSignatureCustomObjectEnumerator IOpcSignatureCustomObjectEnumerator;
#ifdef __cplusplus
interface IOpcSignatureCustomObjectEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignaturePartReference_FWD_DEFINED__
#define __IOpcSignaturePartReference_FWD_DEFINED__
typedef interface IOpcSignaturePartReference IOpcSignaturePartReference;
#ifdef __cplusplus
interface IOpcSignaturePartReference;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignaturePartReferenceEnumerator_FWD_DEFINED__
#define __IOpcSignaturePartReferenceEnumerator_FWD_DEFINED__
typedef interface IOpcSignaturePartReferenceEnumerator IOpcSignaturePartReferenceEnumerator;
#ifdef __cplusplus
interface IOpcSignaturePartReferenceEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureReference_FWD_DEFINED__
#define __IOpcSignatureReference_FWD_DEFINED__
typedef interface IOpcSignatureReference IOpcSignatureReference;
#ifdef __cplusplus
interface IOpcSignatureReference;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureReferenceEnumerator_FWD_DEFINED__
#define __IOpcSignatureReferenceEnumerator_FWD_DEFINED__
typedef interface IOpcSignatureReferenceEnumerator IOpcSignatureReferenceEnumerator;
#ifdef __cplusplus
interface IOpcSignatureReferenceEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureRelationshipReferenceEnumerator_FWD_DEFINED__
#define __IOpcSignatureRelationshipReferenceEnumerator_FWD_DEFINED__
typedef interface IOpcSignatureRelationshipReferenceEnumerator IOpcSignatureRelationshipReferenceEnumerator;
#ifdef __cplusplus
interface IOpcSignatureRelationshipReferenceEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureRelationshipReference_FWD_DEFINED__
#define __IOpcSignatureRelationshipReference_FWD_DEFINED__
typedef interface IOpcSignatureRelationshipReference IOpcSignatureRelationshipReference;
#ifdef __cplusplus
interface IOpcSignatureRelationshipReference;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationshipSelectorEnumerator_FWD_DEFINED__
#define __IOpcRelationshipSelectorEnumerator_FWD_DEFINED__
typedef interface IOpcRelationshipSelectorEnumerator IOpcRelationshipSelectorEnumerator;
#ifdef __cplusplus
interface IOpcRelationshipSelectorEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcCertificateEnumerator_FWD_DEFINED__
#define __IOpcCertificateEnumerator_FWD_DEFINED__
typedef interface IOpcCertificateEnumerator IOpcCertificateEnumerator;
#ifdef __cplusplus
interface IOpcCertificateEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcCertificateSet_FWD_DEFINED__
#define __IOpcCertificateSet_FWD_DEFINED__
typedef interface IOpcCertificateSet IOpcCertificateSet;
#ifdef __cplusplus
interface IOpcCertificateSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcDigitalSignatureEnumerator_FWD_DEFINED__
#define __IOpcDigitalSignatureEnumerator_FWD_DEFINED__
typedef interface IOpcDigitalSignatureEnumerator IOpcDigitalSignatureEnumerator;
#ifdef __cplusplus
interface IOpcDigitalSignatureEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcDigitalSignatureManager_FWD_DEFINED__
#define __IOpcDigitalSignatureManager_FWD_DEFINED__
typedef interface IOpcDigitalSignatureManager IOpcDigitalSignatureManager;
#ifdef __cplusplus
interface IOpcDigitalSignatureManager;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignaturePartReferenceSet_FWD_DEFINED__
#define __IOpcSignaturePartReferenceSet_FWD_DEFINED__
typedef interface IOpcSignaturePartReferenceSet IOpcSignaturePartReferenceSet;
#ifdef __cplusplus
interface IOpcSignaturePartReferenceSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationshipSelectorSet_FWD_DEFINED__
#define __IOpcRelationshipSelectorSet_FWD_DEFINED__
typedef interface IOpcRelationshipSelectorSet IOpcRelationshipSelectorSet;
#ifdef __cplusplus
interface IOpcRelationshipSelectorSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureRelationshipReferenceSet_FWD_DEFINED__
#define __IOpcSignatureRelationshipReferenceSet_FWD_DEFINED__
typedef interface IOpcSignatureRelationshipReferenceSet IOpcSignatureRelationshipReferenceSet;
#ifdef __cplusplus
interface IOpcSignatureRelationshipReferenceSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureReferenceSet_FWD_DEFINED__
#define __IOpcSignatureReferenceSet_FWD_DEFINED__
typedef interface IOpcSignatureReferenceSet IOpcSignatureReferenceSet;
#ifdef __cplusplus
interface IOpcSignatureReferenceSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureCustomObjectSet_FWD_DEFINED__
#define __IOpcSignatureCustomObjectSet_FWD_DEFINED__
typedef interface IOpcSignatureCustomObjectSet IOpcSignatureCustomObjectSet;
#ifdef __cplusplus
interface IOpcSignatureCustomObjectSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcDigitalSignature_FWD_DEFINED__
#define __IOpcDigitalSignature_FWD_DEFINED__
typedef interface IOpcDigitalSignature IOpcDigitalSignature;
#ifdef __cplusplus
interface IOpcDigitalSignature;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSigningOptions_FWD_DEFINED__
#define __IOpcSigningOptions_FWD_DEFINED__
typedef interface IOpcSigningOptions IOpcSigningOptions;
#ifdef __cplusplus
interface IOpcSigningOptions;
#endif /* __cplusplus */
#endif

#ifndef __IOpcFactory_FWD_DEFINED__
#define __IOpcFactory_FWD_DEFINED__
typedef interface IOpcFactory IOpcFactory;
#ifdef __cplusplus
interface IOpcFactory;
#endif /* __cplusplus */
#endif

#ifndef __OpcFactory_FWD_DEFINED__
#define __OpcFactory_FWD_DEFINED__
#ifdef __cplusplus
typedef class OpcFactory OpcFactory;
#else
typedef struct OpcFactory OpcFactory;
#endif /* defined __cplusplus */
#endif /* defined __OpcFactory_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <urlmon.h>
#include <wincrypt.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>
#if NTDDI_VERSION >= 0x06010000
#ifndef __IOpcUri_FWD_DEFINED__
#define __IOpcUri_FWD_DEFINED__
typedef interface IOpcUri IOpcUri;
#ifdef __cplusplus
interface IOpcUri;
#endif /* __cplusplus */
#endif

#ifndef __IOpcPartUri_FWD_DEFINED__
#define __IOpcPartUri_FWD_DEFINED__
typedef interface IOpcPartUri IOpcPartUri;
#ifdef __cplusplus
interface IOpcPartUri;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IOpcUri interface
 */
#ifndef __IOpcUri_INTERFACE_DEFINED__
#define __IOpcUri_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcUri, 0xbc9c1b9b, 0xd62c, 0x49eb, 0xae,0xf0, 0x3b,0x4e,0x0b,0x28,0xeb,0xed);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bc9c1b9b-d62c-49eb-aef0-3b4e0b28ebed")
IOpcUri : public IUri
{
    virtual HRESULT STDMETHODCALLTYPE GetRelationshipsPartUri(
        IOpcPartUri **relationshipPartUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRelativeUri(
        IOpcPartUri *targetPartUri,
        IUri **relativeUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE CombinePartUri(
        IUri *relativeUri,
        IOpcPartUri **combinedUri) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcUri, 0xbc9c1b9b, 0xd62c, 0x49eb, 0xae,0xf0, 0x3b,0x4e,0x0b,0x28,0xeb,0xed)
#endif
#else
typedef struct IOpcUriVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcUri *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcUri *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcUri *This);

    /*** IUri methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyBSTR)(
        IOpcUri *This,
        Uri_PROPERTY uriProp,
        BSTR *pbstrProperty,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetPropertyLength)(
        IOpcUri *This,
        Uri_PROPERTY uriProp,
        DWORD *pcchProperty,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetPropertyDWORD)(
        IOpcUri *This,
        Uri_PROPERTY uriProp,
        DWORD *pdwProperty,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *HasProperty)(
        IOpcUri *This,
        Uri_PROPERTY uriProp,
        WINBOOL *pfHasProperty);

    HRESULT (STDMETHODCALLTYPE *GetAbsoluteUri)(
        IOpcUri *This,
        BSTR *pbstrAbsoluteUri);

    HRESULT (STDMETHODCALLTYPE *GetAuthority)(
        IOpcUri *This,
        BSTR *pbstrAuthority);

    HRESULT (STDMETHODCALLTYPE *GetDisplayUri)(
        IOpcUri *This,
        BSTR *pbstrDisplayString);

    HRESULT (STDMETHODCALLTYPE *GetDomain)(
        IOpcUri *This,
        BSTR *pbstrDomain);

    HRESULT (STDMETHODCALLTYPE *GetExtension)(
        IOpcUri *This,
        BSTR *pbstrExtension);

    HRESULT (STDMETHODCALLTYPE *GetFragment)(
        IOpcUri *This,
        BSTR *pbstrFragment);

    HRESULT (STDMETHODCALLTYPE *GetHost)(
        IOpcUri *This,
        BSTR *pbstrHost);

    HRESULT (STDMETHODCALLTYPE *GetPassword)(
        IOpcUri *This,
        BSTR *pbstrPassword);

    HRESULT (STDMETHODCALLTYPE *GetPath)(
        IOpcUri *This,
        BSTR *pbstrPath);

    HRESULT (STDMETHODCALLTYPE *GetPathAndQuery)(
        IOpcUri *This,
        BSTR *pbstrPathAndQuery);

    HRESULT (STDMETHODCALLTYPE *GetQuery)(
        IOpcUri *This,
        BSTR *pbstrQuery);

    HRESULT (STDMETHODCALLTYPE *GetRawUri)(
        IOpcUri *This,
        BSTR *pbstrRawUri);

    HRESULT (STDMETHODCALLTYPE *GetSchemeName)(
        IOpcUri *This,
        BSTR *pbstrSchemeName);

    HRESULT (STDMETHODCALLTYPE *GetUserInfo)(
        IOpcUri *This,
        BSTR *pbstrUserInfo);

    HRESULT (STDMETHODCALLTYPE *GetUserName)(
        IOpcUri *This,
        BSTR *pbstrUserName);

    HRESULT (STDMETHODCALLTYPE *GetHostType)(
        IOpcUri *This,
        DWORD *pdwHostType);

    HRESULT (STDMETHODCALLTYPE *GetPort)(
        IOpcUri *This,
        DWORD *pdwPort);

    HRESULT (STDMETHODCALLTYPE *GetScheme)(
        IOpcUri *This,
        DWORD *pdwScheme);

    HRESULT (STDMETHODCALLTYPE *GetZone)(
        IOpcUri *This,
        DWORD *pdwZone);

    HRESULT (STDMETHODCALLTYPE *GetProperties)(
        IOpcUri *This,
        LPDWORD pdwFlags);

    HRESULT (STDMETHODCALLTYPE *IsEqual)(
        IOpcUri *This,
        IUri *pUri,
        WINBOOL *pfEqual);

    /*** IOpcUri methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRelationshipsPartUri)(
        IOpcUri *This,
        IOpcPartUri **relationshipPartUri);

    HRESULT (STDMETHODCALLTYPE *GetRelativeUri)(
        IOpcUri *This,
        IOpcPartUri *targetPartUri,
        IUri **relativeUri);

    HRESULT (STDMETHODCALLTYPE *CombinePartUri)(
        IOpcUri *This,
        IUri *relativeUri,
        IOpcPartUri **combinedUri);

    END_INTERFACE
} IOpcUriVtbl;

interface IOpcUri {
    CONST_VTBL IOpcUriVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcUri_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcUri_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcUri_Release(This) (This)->lpVtbl->Release(This)
/*** IUri methods ***/
#define IOpcUri_GetPropertyBSTR(This,uriProp,pbstrProperty,dwFlags) (This)->lpVtbl->GetPropertyBSTR(This,uriProp,pbstrProperty,dwFlags)
#define IOpcUri_GetPropertyLength(This,uriProp,pcchProperty,dwFlags) (This)->lpVtbl->GetPropertyLength(This,uriProp,pcchProperty,dwFlags)
#define IOpcUri_GetPropertyDWORD(This,uriProp,pdwProperty,dwFlags) (This)->lpVtbl->GetPropertyDWORD(This,uriProp,pdwProperty,dwFlags)
#define IOpcUri_HasProperty(This,uriProp,pfHasProperty) (This)->lpVtbl->HasProperty(This,uriProp,pfHasProperty)
#define IOpcUri_GetAbsoluteUri(This,pbstrAbsoluteUri) (This)->lpVtbl->GetAbsoluteUri(This,pbstrAbsoluteUri)
#define IOpcUri_GetAuthority(This,pbstrAuthority) (This)->lpVtbl->GetAuthority(This,pbstrAuthority)
#define IOpcUri_GetDisplayUri(This,pbstrDisplayString) (This)->lpVtbl->GetDisplayUri(This,pbstrDisplayString)
#define IOpcUri_GetDomain(This,pbstrDomain) (This)->lpVtbl->GetDomain(This,pbstrDomain)
#define IOpcUri_GetExtension(This,pbstrExtension) (This)->lpVtbl->GetExtension(This,pbstrExtension)
#define IOpcUri_GetFragment(This,pbstrFragment) (This)->lpVtbl->GetFragment(This,pbstrFragment)
#define IOpcUri_GetHost(This,pbstrHost) (This)->lpVtbl->GetHost(This,pbstrHost)
#define IOpcUri_GetPassword(This,pbstrPassword) (This)->lpVtbl->GetPassword(This,pbstrPassword)
#define IOpcUri_GetPath(This,pbstrPath) (This)->lpVtbl->GetPath(This,pbstrPath)
#define IOpcUri_GetPathAndQuery(This,pbstrPathAndQuery) (This)->lpVtbl->GetPathAndQuery(This,pbstrPathAndQuery)
#define IOpcUri_GetQuery(This,pbstrQuery) (This)->lpVtbl->GetQuery(This,pbstrQuery)
#define IOpcUri_GetRawUri(This,pbstrRawUri) (This)->lpVtbl->GetRawUri(This,pbstrRawUri)
#define IOpcUri_GetSchemeName(This,pbstrSchemeName) (This)->lpVtbl->GetSchemeName(This,pbstrSchemeName)
#define IOpcUri_GetUserInfo(This,pbstrUserInfo) (This)->lpVtbl->GetUserInfo(This,pbstrUserInfo)
#define IOpcUri_GetUserName(This,pbstrUserName) (This)->lpVtbl->GetUserName(This,pbstrUserName)
#define IOpcUri_GetHostType(This,pdwHostType) (This)->lpVtbl->GetHostType(This,pdwHostType)
#define IOpcUri_GetPort(This,pdwPort) (This)->lpVtbl->GetPort(This,pdwPort)
#define IOpcUri_GetScheme(This,pdwScheme) (This)->lpVtbl->GetScheme(This,pdwScheme)
#define IOpcUri_GetZone(This,pdwZone) (This)->lpVtbl->GetZone(This,pdwZone)
#define IOpcUri_GetProperties(This,pdwFlags) (This)->lpVtbl->GetProperties(This,pdwFlags)
#define IOpcUri_IsEqual(This,pUri,pfEqual) (This)->lpVtbl->IsEqual(This,pUri,pfEqual)
/*** IOpcUri methods ***/
#define IOpcUri_GetRelationshipsPartUri(This,relationshipPartUri) (This)->lpVtbl->GetRelationshipsPartUri(This,relationshipPartUri)
#define IOpcUri_GetRelativeUri(This,targetPartUri,relativeUri) (This)->lpVtbl->GetRelativeUri(This,targetPartUri,relativeUri)
#define IOpcUri_CombinePartUri(This,relativeUri,combinedUri) (This)->lpVtbl->CombinePartUri(This,relativeUri,combinedUri)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcUri_QueryInterface(IOpcUri* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcUri_AddRef(IOpcUri* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcUri_Release(IOpcUri* This) {
    return This->lpVtbl->Release(This);
}
/*** IUri methods ***/
static inline HRESULT IOpcUri_GetPropertyBSTR(IOpcUri* This,Uri_PROPERTY uriProp,BSTR *pbstrProperty,DWORD dwFlags) {
    return This->lpVtbl->GetPropertyBSTR(This,uriProp,pbstrProperty,dwFlags);
}
static inline HRESULT IOpcUri_GetPropertyLength(IOpcUri* This,Uri_PROPERTY uriProp,DWORD *pcchProperty,DWORD dwFlags) {
    return This->lpVtbl->GetPropertyLength(This,uriProp,pcchProperty,dwFlags);
}
static inline HRESULT IOpcUri_GetPropertyDWORD(IOpcUri* This,Uri_PROPERTY uriProp,DWORD *pdwProperty,DWORD dwFlags) {
    return This->lpVtbl->GetPropertyDWORD(This,uriProp,pdwProperty,dwFlags);
}
static inline HRESULT IOpcUri_HasProperty(IOpcUri* This,Uri_PROPERTY uriProp,WINBOOL *pfHasProperty) {
    return This->lpVtbl->HasProperty(This,uriProp,pfHasProperty);
}
static inline HRESULT IOpcUri_GetAbsoluteUri(IOpcUri* This,BSTR *pbstrAbsoluteUri) {
    return This->lpVtbl->GetAbsoluteUri(This,pbstrAbsoluteUri);
}
static inline HRESULT IOpcUri_GetAuthority(IOpcUri* This,BSTR *pbstrAuthority) {
    return This->lpVtbl->GetAuthority(This,pbstrAuthority);
}
static inline HRESULT IOpcUri_GetDisplayUri(IOpcUri* This,BSTR *pbstrDisplayString) {
    return This->lpVtbl->GetDisplayUri(This,pbstrDisplayString);
}
static inline HRESULT IOpcUri_GetDomain(IOpcUri* This,BSTR *pbstrDomain) {
    return This->lpVtbl->GetDomain(This,pbstrDomain);
}
static inline HRESULT IOpcUri_GetExtension(IOpcUri* This,BSTR *pbstrExtension) {
    return This->lpVtbl->GetExtension(This,pbstrExtension);
}
static inline HRESULT IOpcUri_GetFragment(IOpcUri* This,BSTR *pbstrFragment) {
    return This->lpVtbl->GetFragment(This,pbstrFragment);
}
static inline HRESULT IOpcUri_GetHost(IOpcUri* This,BSTR *pbstrHost) {
    return This->lpVtbl->GetHost(This,pbstrHost);
}
static inline HRESULT IOpcUri_GetPassword(IOpcUri* This,BSTR *pbstrPassword) {
    return This->lpVtbl->GetPassword(This,pbstrPassword);
}
static inline HRESULT IOpcUri_GetPath(IOpcUri* This,BSTR *pbstrPath) {
    return This->lpVtbl->GetPath(This,pbstrPath);
}
static inline HRESULT IOpcUri_GetPathAndQuery(IOpcUri* This,BSTR *pbstrPathAndQuery) {
    return This->lpVtbl->GetPathAndQuery(This,pbstrPathAndQuery);
}
static inline HRESULT IOpcUri_GetQuery(IOpcUri* This,BSTR *pbstrQuery) {
    return This->lpVtbl->GetQuery(This,pbstrQuery);
}
static inline HRESULT IOpcUri_GetRawUri(IOpcUri* This,BSTR *pbstrRawUri) {
    return This->lpVtbl->GetRawUri(This,pbstrRawUri);
}
static inline HRESULT IOpcUri_GetSchemeName(IOpcUri* This,BSTR *pbstrSchemeName) {
    return This->lpVtbl->GetSchemeName(This,pbstrSchemeName);
}
static inline HRESULT IOpcUri_GetUserInfo(IOpcUri* This,BSTR *pbstrUserInfo) {
    return This->lpVtbl->GetUserInfo(This,pbstrUserInfo);
}
static inline HRESULT IOpcUri_GetUserName(IOpcUri* This,BSTR *pbstrUserName) {
    return This->lpVtbl->GetUserName(This,pbstrUserName);
}
static inline HRESULT IOpcUri_GetHostType(IOpcUri* This,DWORD *pdwHostType) {
    return This->lpVtbl->GetHostType(This,pdwHostType);
}
static inline HRESULT IOpcUri_GetPort(IOpcUri* This,DWORD *pdwPort) {
    return This->lpVtbl->GetPort(This,pdwPort);
}
static inline HRESULT IOpcUri_GetScheme(IOpcUri* This,DWORD *pdwScheme) {
    return This->lpVtbl->GetScheme(This,pdwScheme);
}
static inline HRESULT IOpcUri_GetZone(IOpcUri* This,DWORD *pdwZone) {
    return This->lpVtbl->GetZone(This,pdwZone);
}
static inline HRESULT IOpcUri_GetProperties(IOpcUri* This,LPDWORD pdwFlags) {
    return This->lpVtbl->GetProperties(This,pdwFlags);
}
static inline HRESULT IOpcUri_IsEqual(IOpcUri* This,IUri *pUri,WINBOOL *pfEqual) {
    return This->lpVtbl->IsEqual(This,pUri,pfEqual);
}
/*** IOpcUri methods ***/
static inline HRESULT IOpcUri_GetRelationshipsPartUri(IOpcUri* This,IOpcPartUri **relationshipPartUri) {
    return This->lpVtbl->GetRelationshipsPartUri(This,relationshipPartUri);
}
static inline HRESULT IOpcUri_GetRelativeUri(IOpcUri* This,IOpcPartUri *targetPartUri,IUri **relativeUri) {
    return This->lpVtbl->GetRelativeUri(This,targetPartUri,relativeUri);
}
static inline HRESULT IOpcUri_CombinePartUri(IOpcUri* This,IUri *relativeUri,IOpcPartUri **combinedUri) {
    return This->lpVtbl->CombinePartUri(This,relativeUri,combinedUri);
}
#endif
#endif

#endif


#endif  /* __IOpcUri_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcPartUri interface
 */
#ifndef __IOpcPartUri_INTERFACE_DEFINED__
#define __IOpcPartUri_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcPartUri, 0x7d3babe7, 0x88b2, 0x46ba, 0x85,0xcb, 0x42,0x03,0xcb,0x01,0x6c,0x87);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7d3babe7-88b2-46ba-85cb-4203cb016c87")
IOpcPartUri : public IOpcUri
{
    virtual HRESULT STDMETHODCALLTYPE ComparePartUri(
        IOpcPartUri *partUri,
        INT32 *comparisonResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSourceUri(
        IOpcUri **sourceUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsRelationshipsPartUri(
        WINBOOL *isRelationshipUri) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcPartUri, 0x7d3babe7, 0x88b2, 0x46ba, 0x85,0xcb, 0x42,0x03,0xcb,0x01,0x6c,0x87)
#endif
#else
typedef struct IOpcPartUriVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcPartUri *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcPartUri *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcPartUri *This);

    /*** IUri methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyBSTR)(
        IOpcPartUri *This,
        Uri_PROPERTY uriProp,
        BSTR *pbstrProperty,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetPropertyLength)(
        IOpcPartUri *This,
        Uri_PROPERTY uriProp,
        DWORD *pcchProperty,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetPropertyDWORD)(
        IOpcPartUri *This,
        Uri_PROPERTY uriProp,
        DWORD *pdwProperty,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *HasProperty)(
        IOpcPartUri *This,
        Uri_PROPERTY uriProp,
        WINBOOL *pfHasProperty);

    HRESULT (STDMETHODCALLTYPE *GetAbsoluteUri)(
        IOpcPartUri *This,
        BSTR *pbstrAbsoluteUri);

    HRESULT (STDMETHODCALLTYPE *GetAuthority)(
        IOpcPartUri *This,
        BSTR *pbstrAuthority);

    HRESULT (STDMETHODCALLTYPE *GetDisplayUri)(
        IOpcPartUri *This,
        BSTR *pbstrDisplayString);

    HRESULT (STDMETHODCALLTYPE *GetDomain)(
        IOpcPartUri *This,
        BSTR *pbstrDomain);

    HRESULT (STDMETHODCALLTYPE *GetExtension)(
        IOpcPartUri *This,
        BSTR *pbstrExtension);

    HRESULT (STDMETHODCALLTYPE *GetFragment)(
        IOpcPartUri *This,
        BSTR *pbstrFragment);

    HRESULT (STDMETHODCALLTYPE *GetHost)(
        IOpcPartUri *This,
        BSTR *pbstrHost);

    HRESULT (STDMETHODCALLTYPE *GetPassword)(
        IOpcPartUri *This,
        BSTR *pbstrPassword);

    HRESULT (STDMETHODCALLTYPE *GetPath)(
        IOpcPartUri *This,
        BSTR *pbstrPath);

    HRESULT (STDMETHODCALLTYPE *GetPathAndQuery)(
        IOpcPartUri *This,
        BSTR *pbstrPathAndQuery);

    HRESULT (STDMETHODCALLTYPE *GetQuery)(
        IOpcPartUri *This,
        BSTR *pbstrQuery);

    HRESULT (STDMETHODCALLTYPE *GetRawUri)(
        IOpcPartUri *This,
        BSTR *pbstrRawUri);

    HRESULT (STDMETHODCALLTYPE *GetSchemeName)(
        IOpcPartUri *This,
        BSTR *pbstrSchemeName);

    HRESULT (STDMETHODCALLTYPE *GetUserInfo)(
        IOpcPartUri *This,
        BSTR *pbstrUserInfo);

    HRESULT (STDMETHODCALLTYPE *GetUserName)(
        IOpcPartUri *This,
        BSTR *pbstrUserName);

    HRESULT (STDMETHODCALLTYPE *GetHostType)(
        IOpcPartUri *This,
        DWORD *pdwHostType);

    HRESULT (STDMETHODCALLTYPE *GetPort)(
        IOpcPartUri *This,
        DWORD *pdwPort);

    HRESULT (STDMETHODCALLTYPE *GetScheme)(
        IOpcPartUri *This,
        DWORD *pdwScheme);

    HRESULT (STDMETHODCALLTYPE *GetZone)(
        IOpcPartUri *This,
        DWORD *pdwZone);

    HRESULT (STDMETHODCALLTYPE *GetProperties)(
        IOpcPartUri *This,
        LPDWORD pdwFlags);

    HRESULT (STDMETHODCALLTYPE *IsEqual)(
        IOpcPartUri *This,
        IUri *pUri,
        WINBOOL *pfEqual);

    /*** IOpcUri methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRelationshipsPartUri)(
        IOpcPartUri *This,
        IOpcPartUri **relationshipPartUri);

    HRESULT (STDMETHODCALLTYPE *GetRelativeUri)(
        IOpcPartUri *This,
        IOpcPartUri *targetPartUri,
        IUri **relativeUri);

    HRESULT (STDMETHODCALLTYPE *CombinePartUri)(
        IOpcPartUri *This,
        IUri *relativeUri,
        IOpcPartUri **combinedUri);

    /*** IOpcPartUri methods ***/
    HRESULT (STDMETHODCALLTYPE *ComparePartUri)(
        IOpcPartUri *This,
        IOpcPartUri *partUri,
        INT32 *comparisonResult);

    HRESULT (STDMETHODCALLTYPE *GetSourceUri)(
        IOpcPartUri *This,
        IOpcUri **sourceUri);

    HRESULT (STDMETHODCALLTYPE *IsRelationshipsPartUri)(
        IOpcPartUri *This,
        WINBOOL *isRelationshipUri);

    END_INTERFACE
} IOpcPartUriVtbl;

interface IOpcPartUri {
    CONST_VTBL IOpcPartUriVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcPartUri_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcPartUri_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcPartUri_Release(This) (This)->lpVtbl->Release(This)
/*** IUri methods ***/
#define IOpcPartUri_GetPropertyBSTR(This,uriProp,pbstrProperty,dwFlags) (This)->lpVtbl->GetPropertyBSTR(This,uriProp,pbstrProperty,dwFlags)
#define IOpcPartUri_GetPropertyLength(This,uriProp,pcchProperty,dwFlags) (This)->lpVtbl->GetPropertyLength(This,uriProp,pcchProperty,dwFlags)
#define IOpcPartUri_GetPropertyDWORD(This,uriProp,pdwProperty,dwFlags) (This)->lpVtbl->GetPropertyDWORD(This,uriProp,pdwProperty,dwFlags)
#define IOpcPartUri_HasProperty(This,uriProp,pfHasProperty) (This)->lpVtbl->HasProperty(This,uriProp,pfHasProperty)
#define IOpcPartUri_GetAbsoluteUri(This,pbstrAbsoluteUri) (This)->lpVtbl->GetAbsoluteUri(This,pbstrAbsoluteUri)
#define IOpcPartUri_GetAuthority(This,pbstrAuthority) (This)->lpVtbl->GetAuthority(This,pbstrAuthority)
#define IOpcPartUri_GetDisplayUri(This,pbstrDisplayString) (This)->lpVtbl->GetDisplayUri(This,pbstrDisplayString)
#define IOpcPartUri_GetDomain(This,pbstrDomain) (This)->lpVtbl->GetDomain(This,pbstrDomain)
#define IOpcPartUri_GetExtension(This,pbstrExtension) (This)->lpVtbl->GetExtension(This,pbstrExtension)
#define IOpcPartUri_GetFragment(This,pbstrFragment) (This)->lpVtbl->GetFragment(This,pbstrFragment)
#define IOpcPartUri_GetHost(This,pbstrHost) (This)->lpVtbl->GetHost(This,pbstrHost)
#define IOpcPartUri_GetPassword(This,pbstrPassword) (This)->lpVtbl->GetPassword(This,pbstrPassword)
#define IOpcPartUri_GetPath(This,pbstrPath) (This)->lpVtbl->GetPath(This,pbstrPath)
#define IOpcPartUri_GetPathAndQuery(This,pbstrPathAndQuery) (This)->lpVtbl->GetPathAndQuery(This,pbstrPathAndQuery)
#define IOpcPartUri_GetQuery(This,pbstrQuery) (This)->lpVtbl->GetQuery(This,pbstrQuery)
#define IOpcPartUri_GetRawUri(This,pbstrRawUri) (This)->lpVtbl->GetRawUri(This,pbstrRawUri)
#define IOpcPartUri_GetSchemeName(This,pbstrSchemeName) (This)->lpVtbl->GetSchemeName(This,pbstrSchemeName)
#define IOpcPartUri_GetUserInfo(This,pbstrUserInfo) (This)->lpVtbl->GetUserInfo(This,pbstrUserInfo)
#define IOpcPartUri_GetUserName(This,pbstrUserName) (This)->lpVtbl->GetUserName(This,pbstrUserName)
#define IOpcPartUri_GetHostType(This,pdwHostType) (This)->lpVtbl->GetHostType(This,pdwHostType)
#define IOpcPartUri_GetPort(This,pdwPort) (This)->lpVtbl->GetPort(This,pdwPort)
#define IOpcPartUri_GetScheme(This,pdwScheme) (This)->lpVtbl->GetScheme(This,pdwScheme)
#define IOpcPartUri_GetZone(This,pdwZone) (This)->lpVtbl->GetZone(This,pdwZone)
#define IOpcPartUri_GetProperties(This,pdwFlags) (This)->lpVtbl->GetProperties(This,pdwFlags)
#define IOpcPartUri_IsEqual(This,pUri,pfEqual) (This)->lpVtbl->IsEqual(This,pUri,pfEqual)
/*** IOpcUri methods ***/
#define IOpcPartUri_GetRelationshipsPartUri(This,relationshipPartUri) (This)->lpVtbl->GetRelationshipsPartUri(This,relationshipPartUri)
#define IOpcPartUri_GetRelativeUri(This,targetPartUri,relativeUri) (This)->lpVtbl->GetRelativeUri(This,targetPartUri,relativeUri)
#define IOpcPartUri_CombinePartUri(This,relativeUri,combinedUri) (This)->lpVtbl->CombinePartUri(This,relativeUri,combinedUri)
/*** IOpcPartUri methods ***/
#define IOpcPartUri_ComparePartUri(This,partUri,comparisonResult) (This)->lpVtbl->ComparePartUri(This,partUri,comparisonResult)
#define IOpcPartUri_GetSourceUri(This,sourceUri) (This)->lpVtbl->GetSourceUri(This,sourceUri)
#define IOpcPartUri_IsRelationshipsPartUri(This,isRelationshipUri) (This)->lpVtbl->IsRelationshipsPartUri(This,isRelationshipUri)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcPartUri_QueryInterface(IOpcPartUri* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcPartUri_AddRef(IOpcPartUri* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcPartUri_Release(IOpcPartUri* This) {
    return This->lpVtbl->Release(This);
}
/*** IUri methods ***/
static inline HRESULT IOpcPartUri_GetPropertyBSTR(IOpcPartUri* This,Uri_PROPERTY uriProp,BSTR *pbstrProperty,DWORD dwFlags) {
    return This->lpVtbl->GetPropertyBSTR(This,uriProp,pbstrProperty,dwFlags);
}
static inline HRESULT IOpcPartUri_GetPropertyLength(IOpcPartUri* This,Uri_PROPERTY uriProp,DWORD *pcchProperty,DWORD dwFlags) {
    return This->lpVtbl->GetPropertyLength(This,uriProp,pcchProperty,dwFlags);
}
static inline HRESULT IOpcPartUri_GetPropertyDWORD(IOpcPartUri* This,Uri_PROPERTY uriProp,DWORD *pdwProperty,DWORD dwFlags) {
    return This->lpVtbl->GetPropertyDWORD(This,uriProp,pdwProperty,dwFlags);
}
static inline HRESULT IOpcPartUri_HasProperty(IOpcPartUri* This,Uri_PROPERTY uriProp,WINBOOL *pfHasProperty) {
    return This->lpVtbl->HasProperty(This,uriProp,pfHasProperty);
}
static inline HRESULT IOpcPartUri_GetAbsoluteUri(IOpcPartUri* This,BSTR *pbstrAbsoluteUri) {
    return This->lpVtbl->GetAbsoluteUri(This,pbstrAbsoluteUri);
}
static inline HRESULT IOpcPartUri_GetAuthority(IOpcPartUri* This,BSTR *pbstrAuthority) {
    return This->lpVtbl->GetAuthority(This,pbstrAuthority);
}
static inline HRESULT IOpcPartUri_GetDisplayUri(IOpcPartUri* This,BSTR *pbstrDisplayString) {
    return This->lpVtbl->GetDisplayUri(This,pbstrDisplayString);
}
static inline HRESULT IOpcPartUri_GetDomain(IOpcPartUri* This,BSTR *pbstrDomain) {
    return This->lpVtbl->GetDomain(This,pbstrDomain);
}
static inline HRESULT IOpcPartUri_GetExtension(IOpcPartUri* This,BSTR *pbstrExtension) {
    return This->lpVtbl->GetExtension(This,pbstrExtension);
}
static inline HRESULT IOpcPartUri_GetFragment(IOpcPartUri* This,BSTR *pbstrFragment) {
    return This->lpVtbl->GetFragment(This,pbstrFragment);
}
static inline HRESULT IOpcPartUri_GetHost(IOpcPartUri* This,BSTR *pbstrHost) {
    return This->lpVtbl->GetHost(This,pbstrHost);
}
static inline HRESULT IOpcPartUri_GetPassword(IOpcPartUri* This,BSTR *pbstrPassword) {
    return This->lpVtbl->GetPassword(This,pbstrPassword);
}
static inline HRESULT IOpcPartUri_GetPath(IOpcPartUri* This,BSTR *pbstrPath) {
    return This->lpVtbl->GetPath(This,pbstrPath);
}
static inline HRESULT IOpcPartUri_GetPathAndQuery(IOpcPartUri* This,BSTR *pbstrPathAndQuery) {
    return This->lpVtbl->GetPathAndQuery(This,pbstrPathAndQuery);
}
static inline HRESULT IOpcPartUri_GetQuery(IOpcPartUri* This,BSTR *pbstrQuery) {
    return This->lpVtbl->GetQuery(This,pbstrQuery);
}
static inline HRESULT IOpcPartUri_GetRawUri(IOpcPartUri* This,BSTR *pbstrRawUri) {
    return This->lpVtbl->GetRawUri(This,pbstrRawUri);
}
static inline HRESULT IOpcPartUri_GetSchemeName(IOpcPartUri* This,BSTR *pbstrSchemeName) {
    return This->lpVtbl->GetSchemeName(This,pbstrSchemeName);
}
static inline HRESULT IOpcPartUri_GetUserInfo(IOpcPartUri* This,BSTR *pbstrUserInfo) {
    return This->lpVtbl->GetUserInfo(This,pbstrUserInfo);
}
static inline HRESULT IOpcPartUri_GetUserName(IOpcPartUri* This,BSTR *pbstrUserName) {
    return This->lpVtbl->GetUserName(This,pbstrUserName);
}
static inline HRESULT IOpcPartUri_GetHostType(IOpcPartUri* This,DWORD *pdwHostType) {
    return This->lpVtbl->GetHostType(This,pdwHostType);
}
static inline HRESULT IOpcPartUri_GetPort(IOpcPartUri* This,DWORD *pdwPort) {
    return This->lpVtbl->GetPort(This,pdwPort);
}
static inline HRESULT IOpcPartUri_GetScheme(IOpcPartUri* This,DWORD *pdwScheme) {
    return This->lpVtbl->GetScheme(This,pdwScheme);
}
static inline HRESULT IOpcPartUri_GetZone(IOpcPartUri* This,DWORD *pdwZone) {
    return This->lpVtbl->GetZone(This,pdwZone);
}
static inline HRESULT IOpcPartUri_GetProperties(IOpcPartUri* This,LPDWORD pdwFlags) {
    return This->lpVtbl->GetProperties(This,pdwFlags);
}
static inline HRESULT IOpcPartUri_IsEqual(IOpcPartUri* This,IUri *pUri,WINBOOL *pfEqual) {
    return This->lpVtbl->IsEqual(This,pUri,pfEqual);
}
/*** IOpcUri methods ***/
static inline HRESULT IOpcPartUri_GetRelationshipsPartUri(IOpcPartUri* This,IOpcPartUri **relationshipPartUri) {
    return This->lpVtbl->GetRelationshipsPartUri(This,relationshipPartUri);
}
static inline HRESULT IOpcPartUri_GetRelativeUri(IOpcPartUri* This,IOpcPartUri *targetPartUri,IUri **relativeUri) {
    return This->lpVtbl->GetRelativeUri(This,targetPartUri,relativeUri);
}
static inline HRESULT IOpcPartUri_CombinePartUri(IOpcPartUri* This,IUri *relativeUri,IOpcPartUri **combinedUri) {
    return This->lpVtbl->CombinePartUri(This,relativeUri,combinedUri);
}
/*** IOpcPartUri methods ***/
static inline HRESULT IOpcPartUri_ComparePartUri(IOpcPartUri* This,IOpcPartUri *partUri,INT32 *comparisonResult) {
    return This->lpVtbl->ComparePartUri(This,partUri,comparisonResult);
}
static inline HRESULT IOpcPartUri_GetSourceUri(IOpcPartUri* This,IOpcUri **sourceUri) {
    return This->lpVtbl->GetSourceUri(This,sourceUri);
}
static inline HRESULT IOpcPartUri_IsRelationshipsPartUri(IOpcPartUri* This,WINBOOL *isRelationshipUri) {
    return This->lpVtbl->IsRelationshipsPartUri(This,isRelationshipUri);
}
#endif
#endif

#endif


#endif  /* __IOpcPartUri_INTERFACE_DEFINED__ */

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#define IS_OPC_CONFORMANCE_ERROR(x) (((x) & 0x1ffff000) == (0x0000 + (FACILITY_OPC << 16)))
#define IS_ZIP_CONFORMANCE_ERROR(x) (((x) & 0x1ffff000) == (0x1000 + (FACILITY_OPC << 16)))
#define OPC_E_NONCONFORMING_URI MAKE_HRESULT(1, FACILITY_OPC, 0x1)
#define OPC_E_RELATIVE_URI_REQUIRED MAKE_HRESULT(1, FACILITY_OPC, 0x2)
#define OPC_E_RELATIONSHIP_URI_REQUIRED MAKE_HRESULT(1, FACILITY_OPC, 0x3)
#define OPC_E_PART_CANNOT_BE_DIRECTORY MAKE_HRESULT(1, FACILITY_OPC, 0x4)
#define OPC_E_UNEXPECTED_CONTENT_TYPE MAKE_HRESULT(1, FACILITY_OPC, 0x5)
#define OPC_E_INVALID_CONTENT_TYPE_XML MAKE_HRESULT(1, FACILITY_OPC, 0x6)
#define OPC_E_MISSING_CONTENT_TYPES MAKE_HRESULT(1, FACILITY_OPC, 0x7)
#define OPC_E_NONCONFORMING_CONTENT_TYPES_XML MAKE_HRESULT(1, FACILITY_OPC, 0x8)
#define OPC_E_NONCONFORMING_RELS_XML MAKE_HRESULT(1, FACILITY_OPC, 0x9)
#define OPC_E_INVALID_RELS_XML MAKE_HRESULT(1, FACILITY_OPC, 0xa)
#define OPC_E_DUPLICATE_PART MAKE_HRESULT(1, FACILITY_OPC, 0xb)
#define OPC_E_INVALID_OVERRIDE_PART_NAME MAKE_HRESULT(1, FACILITY_OPC, 0xc)
#define OPC_E_DUPLICATE_OVERRIDE_PART MAKE_HRESULT(1, FACILITY_OPC, 0xd)
#define OPC_E_INVALID_DEFAULT_EXTENSION MAKE_HRESULT(1, FACILITY_OPC, 0xe)
#define OPC_E_DUPLICATE_DEFAULT_EXTENSION MAKE_HRESULT(1, FACILITY_OPC, 0xf)
#define OPC_E_INVALID_RELATIONSHIP_ID MAKE_HRESULT(1, FACILITY_OPC, 0x10)
#define OPC_E_INVALID_RELATIONSHIP_TYPE MAKE_HRESULT(1, FACILITY_OPC, 0x11)
#define OPC_E_INVALID_RELATIONSHIP_TARGET MAKE_HRESULT(1, FACILITY_OPC, 0x12)
#define OPC_E_DUPLICATE_RELATIONSHIP MAKE_HRESULT(1, FACILITY_OPC, 0x13)
#define OPC_E_CONFLICTING_SETTINGS MAKE_HRESULT(1, FACILITY_OPC, 0x14)
#define OPC_E_DUPLICATE_PIECE MAKE_HRESULT(1, FACILITY_OPC, 0x15)
#define OPC_E_INVALID_PIECE MAKE_HRESULT(1, FACILITY_OPC, 0x16)
#define OPC_E_MISSING_PIECE MAKE_HRESULT(1, FACILITY_OPC, 0x17)
#define OPC_E_NO_SUCH_PART MAKE_HRESULT(1, FACILITY_OPC, 0x18)
#define OPC_E_DS_SIGNATURE_CORRUPT MAKE_HRESULT(1, FACILITY_OPC, 0x19)
#define OPC_E_DS_DIGEST_VALUE_ERROR MAKE_HRESULT(1, FACILITY_OPC, 0x1a)
#define OPC_E_DS_DUPLICATE_SIGNATURE_ORIGIN_RELATIONSHIP MAKE_HRESULT(1, FACILITY_OPC, 0x1b)
#define OPC_E_DS_INVALID_SIGNATURE_ORIGIN_RELATIONSHIP MAKE_HRESULT(1, FACILITY_OPC, 0x1c)
#define OPC_E_DS_INVALID_CERTIFICATE_RELATIONSHIP MAKE_HRESULT(1, FACILITY_OPC, 0x1d)
#define OPC_E_DS_EXTERNAL_SIGNATURE MAKE_HRESULT(1, FACILITY_OPC, 0x1e)
#define OPC_E_DS_MISSING_SIGNATURE_ORIGIN_PART MAKE_HRESULT(1, FACILITY_OPC, 0x1f)
#define OPC_E_DS_MISSING_SIGNATURE_PART MAKE_HRESULT(1, FACILITY_OPC, 0x20)
#define OPC_E_DS_INVALID_RELATIONSHIP_TRANSFORM_XML MAKE_HRESULT(1, FACILITY_OPC, 0x21)
#define OPC_E_DS_INVALID_CANONICALIZATION_METHOD MAKE_HRESULT(1, FACILITY_OPC, 0x22)
#define OPC_E_DS_INVALID_RELATIONSHIPS_SIGNING_OPTION MAKE_HRESULT(1, FACILITY_OPC, 0x23)
#define OPC_E_DS_INVALID_OPC_SIGNATURE_TIME_FORMAT MAKE_HRESULT(1, FACILITY_OPC, 0x24)
#define OPC_E_DS_PACKAGE_REFERENCE_URI_RESERVED MAKE_HRESULT(1, FACILITY_OPC, 0x25)
#define OPC_E_DS_MISSING_SIGNATURE_PROPERTIES_ELEMENT MAKE_HRESULT(1, FACILITY_OPC, 0x26)
#define OPC_E_DS_MISSING_SIGNATURE_PROPERTY_ELEMENT MAKE_HRESULT(1, FACILITY_OPC, 0x27)
#define OPC_E_DS_DUPLICATE_SIGNATURE_PROPERTY_ELEMENT MAKE_HRESULT(1, FACILITY_OPC, 0x28)
#define OPC_E_DS_MISSING_SIGNATURE_TIME_PROPERTY MAKE_HRESULT(1, FACILITY_OPC, 0x29)
#define OPC_E_DS_INVALID_SIGNATURE_XML MAKE_HRESULT(1, FACILITY_OPC, 0x2a)
#define OPC_E_DS_INVALID_SIGNATURE_COUNT MAKE_HRESULT(1, FACILITY_OPC, 0x2b)
#define OPC_E_DS_MISSING_SIGNATURE_ALGORITHM MAKE_HRESULT(1, FACILITY_OPC, 0x2c)
#define OPC_E_DS_DUPLICATE_PACKAGE_OBJECT_REFERENCES MAKE_HRESULT(1, FACILITY_OPC, 0x2d)
#define OPC_E_DS_MISSING_PACKAGE_OBJECT_REFERENCE MAKE_HRESULT(1, FACILITY_OPC, 0x2e)
#define OPC_E_DS_EXTERNAL_SIGNATURE_REFERENCE MAKE_HRESULT(1, FACILITY_OPC, 0x2f)
#define OPC_E_DS_REFERENCE_MISSING_CONTENT_TYPE MAKE_HRESULT(1, FACILITY_OPC, 0x30)
#define OPC_E_DS_MULTIPLE_RELATIONSHIP_TRANSFORMS MAKE_HRESULT(1, FACILITY_OPC, 0x31)
#define OPC_E_DS_MISSING_CANONICALIZATION_TRANSFORM MAKE_HRESULT(1, FACILITY_OPC, 0x32)
#define OPC_E_MC_UNEXPECTED_ELEMENT MAKE_HRESULT(1, FACILITY_OPC, 0x33)
#define OPC_E_MC_UNEXPECTED_REQUIRES_ATTR MAKE_HRESULT(1, FACILITY_OPC, 0x34)
#define OPC_E_MC_MISSING_REQUIRES_ATTR MAKE_HRESULT(1, FACILITY_OPC, 0x35)
#define OPC_E_MC_UNEXPECTED_ATTR MAKE_HRESULT(1, FACILITY_OPC, 0x36)
#define OPC_E_MC_INVALID_PREFIX_LIST MAKE_HRESULT(1, FACILITY_OPC, 0x37)
#define OPC_E_MC_INVALID_QNAME_LIST MAKE_HRESULT(1, FACILITY_OPC, 0x38)
#define OPC_E_MC_NESTED_ALTERNATE_CONTENT MAKE_HRESULT(1, FACILITY_OPC, 0x39)
#define OPC_E_MC_UNEXPECTED_CHOICE MAKE_HRESULT(1, FACILITY_OPC, 0x3a)
#define OPC_E_MC_MISSING_CHOICE MAKE_HRESULT(1, FACILITY_OPC, 0x3b)
#define OPC_E_MC_INVALID_ENUM_TYPE MAKE_HRESULT(1, FACILITY_OPC, 0x3c)
#define OPC_E_MC_UNKNOWN_NAMESPACE MAKE_HRESULT(1, FACILITY_OPC, 0x3e)
#define OPC_E_MC_UNKNOWN_PREFIX MAKE_HRESULT(1, FACILITY_OPC, 0x3f)
#define OPC_E_MC_INVALID_ATTRIBUTES_ON_IGNORABLE_ELEMENT MAKE_HRESULT(1, FACILITY_OPC, 0x40)
#define OPC_E_MC_INVALID_XMLNS_ATTRIBUTE MAKE_HRESULT(1, FACILITY_OPC, 0x41)
#define OPC_E_INVALID_XML_ENCODING MAKE_HRESULT(1, FACILITY_OPC, 0x42)
#define OPC_E_DS_SIGNATURE_REFERENCE_MISSING_URI MAKE_HRESULT(1, FACILITY_OPC, 0x43)
#define OPC_E_INVALID_CONTENT_TYPE MAKE_HRESULT(1, FACILITY_OPC, 0x44)
#define OPC_E_DS_SIGNATURE_PROPERTY_MISSING_TARGET MAKE_HRESULT(1, FACILITY_OPC, 0x45)
#define OPC_E_DS_SIGNATURE_METHOD_NOT_SET MAKE_HRESULT(1, FACILITY_OPC, 0x46)
#define OPC_E_DS_DEFAULT_DIGEST_METHOD_NOT_SET MAKE_HRESULT(1, FACILITY_OPC, 0x47)
#define OPC_E_NO_SUCH_RELATIONSHIP MAKE_HRESULT(1, FACILITY_OPC, 0x48)
#define OPC_E_MC_MULTIPLE_FALLBACK_ELEMENTS MAKE_HRESULT(1, FACILITY_OPC, 0x49)
#define OPC_E_MC_INCONSISTENT_PROCESS_CONTENT MAKE_HRESULT(1, FACILITY_OPC, 0x4a)
#define OPC_E_MC_INCONSISTENT_PRESERVE_ATTRIBUTES MAKE_HRESULT(1, FACILITY_OPC, 0x4b)
#define OPC_E_MC_INCONSISTENT_PRESERVE_ELEMENTS MAKE_HRESULT(1, FACILITY_OPC, 0x4c)
#define OPC_E_INVALID_RELATIONSHIP_TARGET_MODE MAKE_HRESULT(1, FACILITY_OPC, 0x4d)
#define OPC_E_COULD_NOT_RECOVER MAKE_HRESULT(1, FACILITY_OPC, 0x4e)
#define OPC_E_UNSUPPORTED_PACKAGE MAKE_HRESULT(1, FACILITY_OPC, 0x4f)
#define OPC_E_ENUM_COLLECTION_CHANGED MAKE_HRESULT(1, FACILITY_OPC, 0x50)
#define OPC_E_ENUM_CANNOT_MOVE_NEXT MAKE_HRESULT(1, FACILITY_OPC, 0x51)
#define OPC_E_ENUM_CANNOT_MOVE_PREVIOUS MAKE_HRESULT(1, FACILITY_OPC, 0x52)
#define OPC_E_ENUM_INVALID_POSITION MAKE_HRESULT(1, FACILITY_OPC, 0x53)
#define OPC_E_DS_SIGNATURE_ORIGIN_EXISTS MAKE_HRESULT(1, FACILITY_OPC, 0x54)
#define OPC_E_DS_UNSIGNED_PACKAGE MAKE_HRESULT(1, FACILITY_OPC, 0x55)
#define OPC_E_DS_MISSING_CERTIFICATE_PART MAKE_HRESULT(1, FACILITY_OPC, 0x56)
#define OPC_E_NO_SUCH_SETTINGS MAKE_HRESULT(1, FACILITY_OPC, 0x57)
#define OPC_E_ZIP_INCORRECT_DATA_SIZE MAKE_HRESULT(1, FACILITY_OPC, 0x1001)
#define OPC_E_ZIP_CORRUPTED_ARCHIVE MAKE_HRESULT(1, FACILITY_OPC, 0x1002)
#define OPC_E_ZIP_COMPRESSION_FAILED MAKE_HRESULT(1, FACILITY_OPC, 0x1003)
#define OPC_E_ZIP_DECOMPRESSION_FAILED MAKE_HRESULT(1, FACILITY_OPC, 0x1004)
#define OPC_E_ZIP_INCONSISTENT_FILEITEM MAKE_HRESULT(1, FACILITY_OPC, 0x1005)
#define OPC_E_ZIP_INCONSISTENT_DIRECTORY MAKE_HRESULT(1, FACILITY_OPC, 0x1006)
#define OPC_E_ZIP_MISSING_DATA_DESCRIPTOR MAKE_HRESULT(1, FACILITY_OPC, 0x1007)
#define OPC_E_ZIP_UNSUPPORTEDARCHIVE MAKE_HRESULT(1, FACILITY_OPC, 0x1008)
#define OPC_E_ZIP_CENTRAL_DIRECTORY_TOO_LARGE MAKE_HRESULT(1, FACILITY_OPC, 0x1009)
#define OPC_E_ZIP_NAME_TOO_LARGE MAKE_HRESULT(1, FACILITY_OPC, 0x100a)
#define OPC_E_ZIP_DUPLICATE_NAME MAKE_HRESULT(1, FACILITY_OPC, 0x100b)
#define OPC_E_ZIP_COMMENT_TOO_LARGE MAKE_HRESULT(1, FACILITY_OPC, 0x100c)
#define OPC_E_ZIP_EXTRA_FIELDS_TOO_LARGE MAKE_HRESULT(1, FACILITY_OPC, 0x100d)
#define OPC_E_ZIP_FILE_HEADER_TOO_LARGE MAKE_HRESULT(1, FACILITY_OPC, 0x100e)
#define OPC_E_ZIP_MISSING_END_OF_CENTRAL_DIRECTORY MAKE_HRESULT(1, FACILITY_OPC, 0x100f)
#define OPC_E_ZIP_REQUIRES_64_BIT MAKE_HRESULT(1, FACILITY_OPC, 0x1010)
#ifndef __MSOPC_LIBRARY_DEFINED__
#define __MSOPC_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_MSOPC, 0x3d8d6062, 0x2749, 0x442b, 0x9e,0x32, 0xe4,0x0e,0xf8,0x01,0xa7,0x66);

#ifndef __IOpcCertificateEnumerator_FWD_DEFINED__
#define __IOpcCertificateEnumerator_FWD_DEFINED__
typedef interface IOpcCertificateEnumerator IOpcCertificateEnumerator;
#ifdef __cplusplus
interface IOpcCertificateEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcCertificateSet_FWD_DEFINED__
#define __IOpcCertificateSet_FWD_DEFINED__
typedef interface IOpcCertificateSet IOpcCertificateSet;
#ifdef __cplusplus
interface IOpcCertificateSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcDigitalSignature_FWD_DEFINED__
#define __IOpcDigitalSignature_FWD_DEFINED__
typedef interface IOpcDigitalSignature IOpcDigitalSignature;
#ifdef __cplusplus
interface IOpcDigitalSignature;
#endif /* __cplusplus */
#endif

#ifndef __IOpcDigitalSignatureEnumerator_FWD_DEFINED__
#define __IOpcDigitalSignatureEnumerator_FWD_DEFINED__
typedef interface IOpcDigitalSignatureEnumerator IOpcDigitalSignatureEnumerator;
#ifdef __cplusplus
interface IOpcDigitalSignatureEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcDigitalSignatureManager_FWD_DEFINED__
#define __IOpcDigitalSignatureManager_FWD_DEFINED__
typedef interface IOpcDigitalSignatureManager IOpcDigitalSignatureManager;
#ifdef __cplusplus
interface IOpcDigitalSignatureManager;
#endif /* __cplusplus */
#endif

#ifndef __IOpcPackage_FWD_DEFINED__
#define __IOpcPackage_FWD_DEFINED__
typedef interface IOpcPackage IOpcPackage;
#ifdef __cplusplus
interface IOpcPackage;
#endif /* __cplusplus */
#endif

#ifndef __IOpcPart_FWD_DEFINED__
#define __IOpcPart_FWD_DEFINED__
typedef interface IOpcPart IOpcPart;
#ifdef __cplusplus
interface IOpcPart;
#endif /* __cplusplus */
#endif

#ifndef __IOpcPartEnumerator_FWD_DEFINED__
#define __IOpcPartEnumerator_FWD_DEFINED__
typedef interface IOpcPartEnumerator IOpcPartEnumerator;
#ifdef __cplusplus
interface IOpcPartEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcPartSet_FWD_DEFINED__
#define __IOpcPartSet_FWD_DEFINED__
typedef interface IOpcPartSet IOpcPartSet;
#ifdef __cplusplus
interface IOpcPartSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcPartUri_FWD_DEFINED__
#define __IOpcPartUri_FWD_DEFINED__
typedef interface IOpcPartUri IOpcPartUri;
#ifdef __cplusplus
interface IOpcPartUri;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationship_FWD_DEFINED__
#define __IOpcRelationship_FWD_DEFINED__
typedef interface IOpcRelationship IOpcRelationship;
#ifdef __cplusplus
interface IOpcRelationship;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationshipEnumerator_FWD_DEFINED__
#define __IOpcRelationshipEnumerator_FWD_DEFINED__
typedef interface IOpcRelationshipEnumerator IOpcRelationshipEnumerator;
#ifdef __cplusplus
interface IOpcRelationshipEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationshipSelector_FWD_DEFINED__
#define __IOpcRelationshipSelector_FWD_DEFINED__
typedef interface IOpcRelationshipSelector IOpcRelationshipSelector;
#ifdef __cplusplus
interface IOpcRelationshipSelector;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationshipSelectorSet_FWD_DEFINED__
#define __IOpcRelationshipSelectorSet_FWD_DEFINED__
typedef interface IOpcRelationshipSelectorSet IOpcRelationshipSelectorSet;
#ifdef __cplusplus
interface IOpcRelationshipSelectorSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationshipSet_FWD_DEFINED__
#define __IOpcRelationshipSet_FWD_DEFINED__
typedef interface IOpcRelationshipSet IOpcRelationshipSet;
#ifdef __cplusplus
interface IOpcRelationshipSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSigningOptions_FWD_DEFINED__
#define __IOpcSigningOptions_FWD_DEFINED__
typedef interface IOpcSigningOptions IOpcSigningOptions;
#ifdef __cplusplus
interface IOpcSigningOptions;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureCustomObject_FWD_DEFINED__
#define __IOpcSignatureCustomObject_FWD_DEFINED__
typedef interface IOpcSignatureCustomObject IOpcSignatureCustomObject;
#ifdef __cplusplus
interface IOpcSignatureCustomObject;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureCustomObjectSet_FWD_DEFINED__
#define __IOpcSignatureCustomObjectSet_FWD_DEFINED__
typedef interface IOpcSignatureCustomObjectSet IOpcSignatureCustomObjectSet;
#ifdef __cplusplus
interface IOpcSignatureCustomObjectSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignaturePartReference_FWD_DEFINED__
#define __IOpcSignaturePartReference_FWD_DEFINED__
typedef interface IOpcSignaturePartReference IOpcSignaturePartReference;
#ifdef __cplusplus
interface IOpcSignaturePartReference;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignaturePartReferenceEnumerator_FWD_DEFINED__
#define __IOpcSignaturePartReferenceEnumerator_FWD_DEFINED__
typedef interface IOpcSignaturePartReferenceEnumerator IOpcSignaturePartReferenceEnumerator;
#ifdef __cplusplus
interface IOpcSignaturePartReferenceEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignaturePartReferenceSet_FWD_DEFINED__
#define __IOpcSignaturePartReferenceSet_FWD_DEFINED__
typedef interface IOpcSignaturePartReferenceSet IOpcSignaturePartReferenceSet;
#ifdef __cplusplus
interface IOpcSignaturePartReferenceSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureReference_FWD_DEFINED__
#define __IOpcSignatureReference_FWD_DEFINED__
typedef interface IOpcSignatureReference IOpcSignatureReference;
#ifdef __cplusplus
interface IOpcSignatureReference;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureReferenceSet_FWD_DEFINED__
#define __IOpcSignatureReferenceSet_FWD_DEFINED__
typedef interface IOpcSignatureReferenceSet IOpcSignatureReferenceSet;
#ifdef __cplusplus
interface IOpcSignatureReferenceSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureRelationshipReference_FWD_DEFINED__
#define __IOpcSignatureRelationshipReference_FWD_DEFINED__
typedef interface IOpcSignatureRelationshipReference IOpcSignatureRelationshipReference;
#ifdef __cplusplus
interface IOpcSignatureRelationshipReference;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureRelationshipReferenceEnumerator_FWD_DEFINED__
#define __IOpcSignatureRelationshipReferenceEnumerator_FWD_DEFINED__
typedef interface IOpcSignatureRelationshipReferenceEnumerator IOpcSignatureRelationshipReferenceEnumerator;
#ifdef __cplusplus
interface IOpcSignatureRelationshipReferenceEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureRelationshipReferenceSet_FWD_DEFINED__
#define __IOpcSignatureRelationshipReferenceSet_FWD_DEFINED__
typedef interface IOpcSignatureRelationshipReferenceSet IOpcSignatureRelationshipReferenceSet;
#ifdef __cplusplus
interface IOpcSignatureRelationshipReferenceSet;
#endif /* __cplusplus */
#endif

#ifndef __IOpcRelationshipSelectorEnumerator_FWD_DEFINED__
#define __IOpcRelationshipSelectorEnumerator_FWD_DEFINED__
typedef interface IOpcRelationshipSelectorEnumerator IOpcRelationshipSelectorEnumerator;
#ifdef __cplusplus
interface IOpcRelationshipSelectorEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureReferenceEnumerator_FWD_DEFINED__
#define __IOpcSignatureReferenceEnumerator_FWD_DEFINED__
typedef interface IOpcSignatureReferenceEnumerator IOpcSignatureReferenceEnumerator;
#ifdef __cplusplus
interface IOpcSignatureReferenceEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcSignatureCustomObjectEnumerator_FWD_DEFINED__
#define __IOpcSignatureCustomObjectEnumerator_FWD_DEFINED__
typedef interface IOpcSignatureCustomObjectEnumerator IOpcSignatureCustomObjectEnumerator;
#ifdef __cplusplus
interface IOpcSignatureCustomObjectEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IOpcUri_FWD_DEFINED__
#define __IOpcUri_FWD_DEFINED__
typedef interface IOpcUri IOpcUri;
#ifdef __cplusplus
interface IOpcUri;
#endif /* __cplusplus */
#endif

typedef enum __WIDL_msopc_generated_name_00000027 {
    OPC_CANONICALIZATION_NONE = 0,
    OPC_CANONICALIZATION_C14N = 1,
    OPC_CANONICALIZATION_C14N_WITH_COMMENTS = 2
} OPC_CANONICALIZATION_METHOD;
typedef enum __WIDL_msopc_generated_name_00000028 {
    OPC_CERTIFICATE_IN_CERTIFICATE_PART = 0,
    OPC_CERTIFICATE_IN_SIGNATURE_PART = 1,
    OPC_CERTIFICATE_NOT_EMBEDDED = 2
} OPC_CERTIFICATE_EMBEDDING_OPTION;
typedef enum __WIDL_msopc_generated_name_00000029 {
    OPC_COMPRESSION_NONE = -1,
    OPC_COMPRESSION_NORMAL = 0,
    OPC_COMPRESSION_MAXIMUM = 1,
    OPC_COMPRESSION_FAST = 2,
    OPC_COMPRESSION_SUPERFAST = 3
} OPC_COMPRESSION_OPTIONS;
typedef enum __WIDL_msopc_generated_name_0000002A {
    OPC_READ_DEFAULT = 0x0,
    OPC_VALIDATE_ON_LOAD = 0x1,
    OPC_CACHE_ON_ACCESS = 0x2
} OPC_READ_FLAGS;
typedef enum __WIDL_msopc_generated_name_0000002B {
    OPC_RELATIONSHIP_SELECT_BY_ID = 0,
    OPC_RELATIONSHIP_SELECT_BY_TYPE = 1
} OPC_RELATIONSHIP_SELECTOR;
typedef enum __WIDL_msopc_generated_name_0000002C {
    OPC_RELATIONSHIP_SIGN_USING_SELECTORS = 0,
    OPC_RELATIONSHIP_SIGN_PART = 1
} OPC_RELATIONSHIPS_SIGNING_OPTION;
typedef enum OPC_SIGNATURE_VALIDATION_RESULT {
    OPC_SIGNATURE_VALID = 0,
    OPC_SIGNATURE_INVALID = -1
} OPC_SIGNATURE_VALIDATION_RESULT;
typedef enum __WIDL_msopc_generated_name_0000002D {
    OPC_SIGNATURE_TIME_FORMAT_MILLISECONDS = 0,
    OPC_SIGNATURE_TIME_FORMAT_SECONDS = 1,
    OPC_SIGNATURE_TIME_FORMAT_MINUTES = 2,
    OPC_SIGNATURE_TIME_FORMAT_DAYS = 3,
    OPC_SIGNATURE_TIME_FORMAT_MONTHS = 4,
    OPC_SIGNATURE_TIME_FORMAT_YEARS = 5
} OPC_SIGNATURE_TIME_FORMAT;
typedef enum __WIDL_msopc_generated_name_0000002E {
    OPC_STREAM_IO_READ = 1,
    OPC_STREAM_IO_WRITE = 2
} OPC_STREAM_IO_MODE;
typedef enum __WIDL_msopc_generated_name_0000002F {
    OPC_URI_TARGET_MODE_INTERNAL = 0,
    OPC_URI_TARGET_MODE_EXTERNAL = 1
} OPC_URI_TARGET_MODE;
typedef enum __WIDL_msopc_generated_name_00000030 {
    OPC_WRITE_DEFAULT = 0x0,
    OPC_WRITE_FORCE_ZIP32 = 0x1
} OPC_WRITE_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(OPC_READ_FLAGS);
DEFINE_ENUM_FLAG_OPERATORS(OPC_WRITE_FLAGS);
/*****************************************************************************
 * IOpcPart interface
 */
#ifndef __IOpcPart_INTERFACE_DEFINED__
#define __IOpcPart_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcPart, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x71);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("42195949-3b79-4fc8-89c6-fc7fb979ee71")
IOpcPart : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetRelationshipSet(
        IOpcRelationshipSet **relationshipSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContentStream(
        IStream **stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetName(
        IOpcPartUri **name) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContentType(
        LPWSTR *contentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCompressionOptions(
        OPC_COMPRESSION_OPTIONS *compressionOptions) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcPart, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x71)
#endif
#else
typedef struct IOpcPartVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcPart *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcPart *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcPart *This);

    /*** IOpcPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRelationshipSet)(
        IOpcPart *This,
        IOpcRelationshipSet **relationshipSet);

    HRESULT (STDMETHODCALLTYPE *GetContentStream)(
        IOpcPart *This,
        IStream **stream);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IOpcPart *This,
        IOpcPartUri **name);

    HRESULT (STDMETHODCALLTYPE *GetContentType)(
        IOpcPart *This,
        LPWSTR *contentType);

    HRESULT (STDMETHODCALLTYPE *GetCompressionOptions)(
        IOpcPart *This,
        OPC_COMPRESSION_OPTIONS *compressionOptions);

    END_INTERFACE
} IOpcPartVtbl;

interface IOpcPart {
    CONST_VTBL IOpcPartVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcPart_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcPart_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcPart_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcPart methods ***/
#define IOpcPart_GetRelationshipSet(This,relationshipSet) (This)->lpVtbl->GetRelationshipSet(This,relationshipSet)
#define IOpcPart_GetContentStream(This,stream) (This)->lpVtbl->GetContentStream(This,stream)
#define IOpcPart_GetName(This,name) (This)->lpVtbl->GetName(This,name)
#define IOpcPart_GetContentType(This,contentType) (This)->lpVtbl->GetContentType(This,contentType)
#define IOpcPart_GetCompressionOptions(This,compressionOptions) (This)->lpVtbl->GetCompressionOptions(This,compressionOptions)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcPart_QueryInterface(IOpcPart* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcPart_AddRef(IOpcPart* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcPart_Release(IOpcPart* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcPart methods ***/
static inline HRESULT IOpcPart_GetRelationshipSet(IOpcPart* This,IOpcRelationshipSet **relationshipSet) {
    return This->lpVtbl->GetRelationshipSet(This,relationshipSet);
}
static inline HRESULT IOpcPart_GetContentStream(IOpcPart* This,IStream **stream) {
    return This->lpVtbl->GetContentStream(This,stream);
}
static inline HRESULT IOpcPart_GetName(IOpcPart* This,IOpcPartUri **name) {
    return This->lpVtbl->GetName(This,name);
}
static inline HRESULT IOpcPart_GetContentType(IOpcPart* This,LPWSTR *contentType) {
    return This->lpVtbl->GetContentType(This,contentType);
}
static inline HRESULT IOpcPart_GetCompressionOptions(IOpcPart* This,OPC_COMPRESSION_OPTIONS *compressionOptions) {
    return This->lpVtbl->GetCompressionOptions(This,compressionOptions);
}
#endif
#endif

#endif


#endif  /* __IOpcPart_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcPartEnumerator interface
 */
#ifndef __IOpcPartEnumerator_INTERFACE_DEFINED__
#define __IOpcPartEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcPartEnumerator, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x75);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("42195949-3b79-4fc8-89c6-fc7fb979ee75")
IOpcPartEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MoveNext(
        WINBOOL *hasNext) = 0;

    virtual HRESULT STDMETHODCALLTYPE MovePrevious(
        WINBOOL *hasPrevious) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrent(
        IOpcPart **part) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IOpcPartEnumerator **copy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcPartEnumerator, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x75)
#endif
#else
typedef struct IOpcPartEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcPartEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcPartEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcPartEnumerator *This);

    /*** IOpcPartEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        IOpcPartEnumerator *This,
        WINBOOL *hasNext);

    HRESULT (STDMETHODCALLTYPE *MovePrevious)(
        IOpcPartEnumerator *This,
        WINBOOL *hasPrevious);

    HRESULT (STDMETHODCALLTYPE *GetCurrent)(
        IOpcPartEnumerator *This,
        IOpcPart **part);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IOpcPartEnumerator *This,
        IOpcPartEnumerator **copy);

    END_INTERFACE
} IOpcPartEnumeratorVtbl;

interface IOpcPartEnumerator {
    CONST_VTBL IOpcPartEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcPartEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcPartEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcPartEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcPartEnumerator methods ***/
#define IOpcPartEnumerator_MoveNext(This,hasNext) (This)->lpVtbl->MoveNext(This,hasNext)
#define IOpcPartEnumerator_MovePrevious(This,hasPrevious) (This)->lpVtbl->MovePrevious(This,hasPrevious)
#define IOpcPartEnumerator_GetCurrent(This,part) (This)->lpVtbl->GetCurrent(This,part)
#define IOpcPartEnumerator_Clone(This,copy) (This)->lpVtbl->Clone(This,copy)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcPartEnumerator_QueryInterface(IOpcPartEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcPartEnumerator_AddRef(IOpcPartEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcPartEnumerator_Release(IOpcPartEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcPartEnumerator methods ***/
static inline HRESULT IOpcPartEnumerator_MoveNext(IOpcPartEnumerator* This,WINBOOL *hasNext) {
    return This->lpVtbl->MoveNext(This,hasNext);
}
static inline HRESULT IOpcPartEnumerator_MovePrevious(IOpcPartEnumerator* This,WINBOOL *hasPrevious) {
    return This->lpVtbl->MovePrevious(This,hasPrevious);
}
static inline HRESULT IOpcPartEnumerator_GetCurrent(IOpcPartEnumerator* This,IOpcPart **part) {
    return This->lpVtbl->GetCurrent(This,part);
}
static inline HRESULT IOpcPartEnumerator_Clone(IOpcPartEnumerator* This,IOpcPartEnumerator **copy) {
    return This->lpVtbl->Clone(This,copy);
}
#endif
#endif

#endif


#endif  /* __IOpcPartEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcPartSet interface
 */
#ifndef __IOpcPartSet_INTERFACE_DEFINED__
#define __IOpcPartSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcPartSet, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x73);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("42195949-3b79-4fc8-89c6-fc7fb979ee73")
IOpcPartSet : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPart(
        IOpcPartUri *name,
        IOpcPart **part) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePart(
        IOpcPartUri *name,
        LPCWSTR contentType,
        OPC_COMPRESSION_OPTIONS compressionOptions,
        IOpcPart **part) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeletePart(
        IOpcPartUri *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE PartExists(
        IOpcPartUri *name,
        WINBOOL *partExists) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumerator(
        IOpcPartEnumerator **partEnumerator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcPartSet, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x73)
#endif
#else
typedef struct IOpcPartSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcPartSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcPartSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcPartSet *This);

    /*** IOpcPartSet methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPart)(
        IOpcPartSet *This,
        IOpcPartUri *name,
        IOpcPart **part);

    HRESULT (STDMETHODCALLTYPE *CreatePart)(
        IOpcPartSet *This,
        IOpcPartUri *name,
        LPCWSTR contentType,
        OPC_COMPRESSION_OPTIONS compressionOptions,
        IOpcPart **part);

    HRESULT (STDMETHODCALLTYPE *DeletePart)(
        IOpcPartSet *This,
        IOpcPartUri *name);

    HRESULT (STDMETHODCALLTYPE *PartExists)(
        IOpcPartSet *This,
        IOpcPartUri *name,
        WINBOOL *partExists);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IOpcPartSet *This,
        IOpcPartEnumerator **partEnumerator);

    END_INTERFACE
} IOpcPartSetVtbl;

interface IOpcPartSet {
    CONST_VTBL IOpcPartSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcPartSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcPartSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcPartSet_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcPartSet methods ***/
#define IOpcPartSet_GetPart(This,name,part) (This)->lpVtbl->GetPart(This,name,part)
#define IOpcPartSet_CreatePart(This,name,contentType,compressionOptions,part) (This)->lpVtbl->CreatePart(This,name,contentType,compressionOptions,part)
#define IOpcPartSet_DeletePart(This,name) (This)->lpVtbl->DeletePart(This,name)
#define IOpcPartSet_PartExists(This,name,partExists) (This)->lpVtbl->PartExists(This,name,partExists)
#define IOpcPartSet_GetEnumerator(This,partEnumerator) (This)->lpVtbl->GetEnumerator(This,partEnumerator)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcPartSet_QueryInterface(IOpcPartSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcPartSet_AddRef(IOpcPartSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcPartSet_Release(IOpcPartSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcPartSet methods ***/
static inline HRESULT IOpcPartSet_GetPart(IOpcPartSet* This,IOpcPartUri *name,IOpcPart **part) {
    return This->lpVtbl->GetPart(This,name,part);
}
static inline HRESULT IOpcPartSet_CreatePart(IOpcPartSet* This,IOpcPartUri *name,LPCWSTR contentType,OPC_COMPRESSION_OPTIONS compressionOptions,IOpcPart **part) {
    return This->lpVtbl->CreatePart(This,name,contentType,compressionOptions,part);
}
static inline HRESULT IOpcPartSet_DeletePart(IOpcPartSet* This,IOpcPartUri *name) {
    return This->lpVtbl->DeletePart(This,name);
}
static inline HRESULT IOpcPartSet_PartExists(IOpcPartSet* This,IOpcPartUri *name,WINBOOL *partExists) {
    return This->lpVtbl->PartExists(This,name,partExists);
}
static inline HRESULT IOpcPartSet_GetEnumerator(IOpcPartSet* This,IOpcPartEnumerator **partEnumerator) {
    return This->lpVtbl->GetEnumerator(This,partEnumerator);
}
#endif
#endif

#endif


#endif  /* __IOpcPartSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcPackage interface
 */
#ifndef __IOpcPackage_INTERFACE_DEFINED__
#define __IOpcPackage_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcPackage, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x70);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("42195949-3b79-4fc8-89c6-fc7fb979ee70")
IOpcPackage : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPartSet(
        IOpcPartSet **partSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRelationshipSet(
        IOpcRelationshipSet **relationshipSet) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcPackage, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x70)
#endif
#else
typedef struct IOpcPackageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcPackage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcPackage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcPackage *This);

    /*** IOpcPackage methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartSet)(
        IOpcPackage *This,
        IOpcPartSet **partSet);

    HRESULT (STDMETHODCALLTYPE *GetRelationshipSet)(
        IOpcPackage *This,
        IOpcRelationshipSet **relationshipSet);

    END_INTERFACE
} IOpcPackageVtbl;

interface IOpcPackage {
    CONST_VTBL IOpcPackageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcPackage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcPackage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcPackage_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcPackage methods ***/
#define IOpcPackage_GetPartSet(This,partSet) (This)->lpVtbl->GetPartSet(This,partSet)
#define IOpcPackage_GetRelationshipSet(This,relationshipSet) (This)->lpVtbl->GetRelationshipSet(This,relationshipSet)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcPackage_QueryInterface(IOpcPackage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcPackage_AddRef(IOpcPackage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcPackage_Release(IOpcPackage* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcPackage methods ***/
static inline HRESULT IOpcPackage_GetPartSet(IOpcPackage* This,IOpcPartSet **partSet) {
    return This->lpVtbl->GetPartSet(This,partSet);
}
static inline HRESULT IOpcPackage_GetRelationshipSet(IOpcPackage* This,IOpcRelationshipSet **relationshipSet) {
    return This->lpVtbl->GetRelationshipSet(This,relationshipSet);
}
#endif
#endif

#endif


#endif  /* __IOpcPackage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcRelationship interface
 */
#ifndef __IOpcRelationship_INTERFACE_DEFINED__
#define __IOpcRelationship_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcRelationship, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x72);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("42195949-3b79-4fc8-89c6-fc7fb979ee72")
IOpcRelationship : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetId(
        LPWSTR *relationshipIdentifier) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRelationshipType(
        LPWSTR *relationshipType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSourceUri(
        IOpcUri **sourceUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTargetUri(
        IUri **targetUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTargetMode(
        OPC_URI_TARGET_MODE *targetMode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcRelationship, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x72)
#endif
#else
typedef struct IOpcRelationshipVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcRelationship *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcRelationship *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcRelationship *This);

    /*** IOpcRelationship methods ***/
    HRESULT (STDMETHODCALLTYPE *GetId)(
        IOpcRelationship *This,
        LPWSTR *relationshipIdentifier);

    HRESULT (STDMETHODCALLTYPE *GetRelationshipType)(
        IOpcRelationship *This,
        LPWSTR *relationshipType);

    HRESULT (STDMETHODCALLTYPE *GetSourceUri)(
        IOpcRelationship *This,
        IOpcUri **sourceUri);

    HRESULT (STDMETHODCALLTYPE *GetTargetUri)(
        IOpcRelationship *This,
        IUri **targetUri);

    HRESULT (STDMETHODCALLTYPE *GetTargetMode)(
        IOpcRelationship *This,
        OPC_URI_TARGET_MODE *targetMode);

    END_INTERFACE
} IOpcRelationshipVtbl;

interface IOpcRelationship {
    CONST_VTBL IOpcRelationshipVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcRelationship_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcRelationship_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcRelationship_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcRelationship methods ***/
#define IOpcRelationship_GetId(This,relationshipIdentifier) (This)->lpVtbl->GetId(This,relationshipIdentifier)
#define IOpcRelationship_GetRelationshipType(This,relationshipType) (This)->lpVtbl->GetRelationshipType(This,relationshipType)
#define IOpcRelationship_GetSourceUri(This,sourceUri) (This)->lpVtbl->GetSourceUri(This,sourceUri)
#define IOpcRelationship_GetTargetUri(This,targetUri) (This)->lpVtbl->GetTargetUri(This,targetUri)
#define IOpcRelationship_GetTargetMode(This,targetMode) (This)->lpVtbl->GetTargetMode(This,targetMode)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcRelationship_QueryInterface(IOpcRelationship* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcRelationship_AddRef(IOpcRelationship* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcRelationship_Release(IOpcRelationship* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcRelationship methods ***/
static inline HRESULT IOpcRelationship_GetId(IOpcRelationship* This,LPWSTR *relationshipIdentifier) {
    return This->lpVtbl->GetId(This,relationshipIdentifier);
}
static inline HRESULT IOpcRelationship_GetRelationshipType(IOpcRelationship* This,LPWSTR *relationshipType) {
    return This->lpVtbl->GetRelationshipType(This,relationshipType);
}
static inline HRESULT IOpcRelationship_GetSourceUri(IOpcRelationship* This,IOpcUri **sourceUri) {
    return This->lpVtbl->GetSourceUri(This,sourceUri);
}
static inline HRESULT IOpcRelationship_GetTargetUri(IOpcRelationship* This,IUri **targetUri) {
    return This->lpVtbl->GetTargetUri(This,targetUri);
}
static inline HRESULT IOpcRelationship_GetTargetMode(IOpcRelationship* This,OPC_URI_TARGET_MODE *targetMode) {
    return This->lpVtbl->GetTargetMode(This,targetMode);
}
#endif
#endif

#endif


#endif  /* __IOpcRelationship_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcRelationshipEnumerator interface
 */
#ifndef __IOpcRelationshipEnumerator_INTERFACE_DEFINED__
#define __IOpcRelationshipEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcRelationshipEnumerator, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x76);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("42195949-3b79-4fc8-89c6-fc7fb979ee76")
IOpcRelationshipEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MoveNext(
        WINBOOL *hasNext) = 0;

    virtual HRESULT STDMETHODCALLTYPE MovePrevious(
        WINBOOL *hasPrevious) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrent(
        IOpcRelationship **relationship) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IOpcRelationshipEnumerator **copy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcRelationshipEnumerator, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x76)
#endif
#else
typedef struct IOpcRelationshipEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcRelationshipEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcRelationshipEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcRelationshipEnumerator *This);

    /*** IOpcRelationshipEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        IOpcRelationshipEnumerator *This,
        WINBOOL *hasNext);

    HRESULT (STDMETHODCALLTYPE *MovePrevious)(
        IOpcRelationshipEnumerator *This,
        WINBOOL *hasPrevious);

    HRESULT (STDMETHODCALLTYPE *GetCurrent)(
        IOpcRelationshipEnumerator *This,
        IOpcRelationship **relationship);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IOpcRelationshipEnumerator *This,
        IOpcRelationshipEnumerator **copy);

    END_INTERFACE
} IOpcRelationshipEnumeratorVtbl;

interface IOpcRelationshipEnumerator {
    CONST_VTBL IOpcRelationshipEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcRelationshipEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcRelationshipEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcRelationshipEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcRelationshipEnumerator methods ***/
#define IOpcRelationshipEnumerator_MoveNext(This,hasNext) (This)->lpVtbl->MoveNext(This,hasNext)
#define IOpcRelationshipEnumerator_MovePrevious(This,hasPrevious) (This)->lpVtbl->MovePrevious(This,hasPrevious)
#define IOpcRelationshipEnumerator_GetCurrent(This,relationship) (This)->lpVtbl->GetCurrent(This,relationship)
#define IOpcRelationshipEnumerator_Clone(This,copy) (This)->lpVtbl->Clone(This,copy)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcRelationshipEnumerator_QueryInterface(IOpcRelationshipEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcRelationshipEnumerator_AddRef(IOpcRelationshipEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcRelationshipEnumerator_Release(IOpcRelationshipEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcRelationshipEnumerator methods ***/
static inline HRESULT IOpcRelationshipEnumerator_MoveNext(IOpcRelationshipEnumerator* This,WINBOOL *hasNext) {
    return This->lpVtbl->MoveNext(This,hasNext);
}
static inline HRESULT IOpcRelationshipEnumerator_MovePrevious(IOpcRelationshipEnumerator* This,WINBOOL *hasPrevious) {
    return This->lpVtbl->MovePrevious(This,hasPrevious);
}
static inline HRESULT IOpcRelationshipEnumerator_GetCurrent(IOpcRelationshipEnumerator* This,IOpcRelationship **relationship) {
    return This->lpVtbl->GetCurrent(This,relationship);
}
static inline HRESULT IOpcRelationshipEnumerator_Clone(IOpcRelationshipEnumerator* This,IOpcRelationshipEnumerator **copy) {
    return This->lpVtbl->Clone(This,copy);
}
#endif
#endif

#endif


#endif  /* __IOpcRelationshipEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcRelationshipSelector interface
 */
#ifndef __IOpcRelationshipSelector_INTERFACE_DEFINED__
#define __IOpcRelationshipSelector_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcRelationshipSelector, 0xf8f26c7f, 0xb28f, 0x4899, 0x84,0xc8, 0x5d,0x56,0x39,0xed,0xe7,0x5f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f8f26c7f-b28f-4899-84c8-5d5639ede75f")
IOpcRelationshipSelector : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSelectorType(
        OPC_RELATIONSHIP_SELECTOR *selector) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSelectionCriterion(
        LPWSTR *selectionCriterion) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcRelationshipSelector, 0xf8f26c7f, 0xb28f, 0x4899, 0x84,0xc8, 0x5d,0x56,0x39,0xed,0xe7,0x5f)
#endif
#else
typedef struct IOpcRelationshipSelectorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcRelationshipSelector *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcRelationshipSelector *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcRelationshipSelector *This);

    /*** IOpcRelationshipSelector methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSelectorType)(
        IOpcRelationshipSelector *This,
        OPC_RELATIONSHIP_SELECTOR *selector);

    HRESULT (STDMETHODCALLTYPE *GetSelectionCriterion)(
        IOpcRelationshipSelector *This,
        LPWSTR *selectionCriterion);

    END_INTERFACE
} IOpcRelationshipSelectorVtbl;

interface IOpcRelationshipSelector {
    CONST_VTBL IOpcRelationshipSelectorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcRelationshipSelector_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcRelationshipSelector_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcRelationshipSelector_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcRelationshipSelector methods ***/
#define IOpcRelationshipSelector_GetSelectorType(This,selector) (This)->lpVtbl->GetSelectorType(This,selector)
#define IOpcRelationshipSelector_GetSelectionCriterion(This,selectionCriterion) (This)->lpVtbl->GetSelectionCriterion(This,selectionCriterion)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcRelationshipSelector_QueryInterface(IOpcRelationshipSelector* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcRelationshipSelector_AddRef(IOpcRelationshipSelector* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcRelationshipSelector_Release(IOpcRelationshipSelector* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcRelationshipSelector methods ***/
static inline HRESULT IOpcRelationshipSelector_GetSelectorType(IOpcRelationshipSelector* This,OPC_RELATIONSHIP_SELECTOR *selector) {
    return This->lpVtbl->GetSelectorType(This,selector);
}
static inline HRESULT IOpcRelationshipSelector_GetSelectionCriterion(IOpcRelationshipSelector* This,LPWSTR *selectionCriterion) {
    return This->lpVtbl->GetSelectionCriterion(This,selectionCriterion);
}
#endif
#endif

#endif


#endif  /* __IOpcRelationshipSelector_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcRelationshipSet interface
 */
#ifndef __IOpcRelationshipSet_INTERFACE_DEFINED__
#define __IOpcRelationshipSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcRelationshipSet, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x74);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("42195949-3b79-4fc8-89c6-fc7fb979ee74")
IOpcRelationshipSet : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetRelationship(
        LPCWSTR relationshipIdentifier,
        IOpcRelationship **relationship) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRelationship(
        LPCWSTR relationshipIdentifier,
        LPCWSTR relationshipType,
        IUri *targetUri,
        OPC_URI_TARGET_MODE targetMode,
        IOpcRelationship **relationship) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteRelationship(
        LPCWSTR relationshipIdentifier) = 0;

    virtual HRESULT STDMETHODCALLTYPE RelationshipExists(
        LPCWSTR relationshipIdentifier,
        WINBOOL *relationshipExists) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumerator(
        IOpcRelationshipEnumerator **relationshipEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumeratorForType(
        LPCWSTR relationshipType,
        IOpcRelationshipEnumerator **relationshipEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRelationshipsContentStream(
        IStream **contents) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcRelationshipSet, 0x42195949, 0x3b79, 0x4fc8, 0x89,0xc6, 0xfc,0x7f,0xb9,0x79,0xee,0x74)
#endif
#else
typedef struct IOpcRelationshipSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcRelationshipSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcRelationshipSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcRelationshipSet *This);

    /*** IOpcRelationshipSet methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRelationship)(
        IOpcRelationshipSet *This,
        LPCWSTR relationshipIdentifier,
        IOpcRelationship **relationship);

    HRESULT (STDMETHODCALLTYPE *CreateRelationship)(
        IOpcRelationshipSet *This,
        LPCWSTR relationshipIdentifier,
        LPCWSTR relationshipType,
        IUri *targetUri,
        OPC_URI_TARGET_MODE targetMode,
        IOpcRelationship **relationship);

    HRESULT (STDMETHODCALLTYPE *DeleteRelationship)(
        IOpcRelationshipSet *This,
        LPCWSTR relationshipIdentifier);

    HRESULT (STDMETHODCALLTYPE *RelationshipExists)(
        IOpcRelationshipSet *This,
        LPCWSTR relationshipIdentifier,
        WINBOOL *relationshipExists);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IOpcRelationshipSet *This,
        IOpcRelationshipEnumerator **relationshipEnumerator);

    HRESULT (STDMETHODCALLTYPE *GetEnumeratorForType)(
        IOpcRelationshipSet *This,
        LPCWSTR relationshipType,
        IOpcRelationshipEnumerator **relationshipEnumerator);

    HRESULT (STDMETHODCALLTYPE *GetRelationshipsContentStream)(
        IOpcRelationshipSet *This,
        IStream **contents);

    END_INTERFACE
} IOpcRelationshipSetVtbl;

interface IOpcRelationshipSet {
    CONST_VTBL IOpcRelationshipSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcRelationshipSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcRelationshipSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcRelationshipSet_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcRelationshipSet methods ***/
#define IOpcRelationshipSet_GetRelationship(This,relationshipIdentifier,relationship) (This)->lpVtbl->GetRelationship(This,relationshipIdentifier,relationship)
#define IOpcRelationshipSet_CreateRelationship(This,relationshipIdentifier,relationshipType,targetUri,targetMode,relationship) (This)->lpVtbl->CreateRelationship(This,relationshipIdentifier,relationshipType,targetUri,targetMode,relationship)
#define IOpcRelationshipSet_DeleteRelationship(This,relationshipIdentifier) (This)->lpVtbl->DeleteRelationship(This,relationshipIdentifier)
#define IOpcRelationshipSet_RelationshipExists(This,relationshipIdentifier,relationshipExists) (This)->lpVtbl->RelationshipExists(This,relationshipIdentifier,relationshipExists)
#define IOpcRelationshipSet_GetEnumerator(This,relationshipEnumerator) (This)->lpVtbl->GetEnumerator(This,relationshipEnumerator)
#define IOpcRelationshipSet_GetEnumeratorForType(This,relationshipType,relationshipEnumerator) (This)->lpVtbl->GetEnumeratorForType(This,relationshipType,relationshipEnumerator)
#define IOpcRelationshipSet_GetRelationshipsContentStream(This,contents) (This)->lpVtbl->GetRelationshipsContentStream(This,contents)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcRelationshipSet_QueryInterface(IOpcRelationshipSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcRelationshipSet_AddRef(IOpcRelationshipSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcRelationshipSet_Release(IOpcRelationshipSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcRelationshipSet methods ***/
static inline HRESULT IOpcRelationshipSet_GetRelationship(IOpcRelationshipSet* This,LPCWSTR relationshipIdentifier,IOpcRelationship **relationship) {
    return This->lpVtbl->GetRelationship(This,relationshipIdentifier,relationship);
}
static inline HRESULT IOpcRelationshipSet_CreateRelationship(IOpcRelationshipSet* This,LPCWSTR relationshipIdentifier,LPCWSTR relationshipType,IUri *targetUri,OPC_URI_TARGET_MODE targetMode,IOpcRelationship **relationship) {
    return This->lpVtbl->CreateRelationship(This,relationshipIdentifier,relationshipType,targetUri,targetMode,relationship);
}
static inline HRESULT IOpcRelationshipSet_DeleteRelationship(IOpcRelationshipSet* This,LPCWSTR relationshipIdentifier) {
    return This->lpVtbl->DeleteRelationship(This,relationshipIdentifier);
}
static inline HRESULT IOpcRelationshipSet_RelationshipExists(IOpcRelationshipSet* This,LPCWSTR relationshipIdentifier,WINBOOL *relationshipExists) {
    return This->lpVtbl->RelationshipExists(This,relationshipIdentifier,relationshipExists);
}
static inline HRESULT IOpcRelationshipSet_GetEnumerator(IOpcRelationshipSet* This,IOpcRelationshipEnumerator **relationshipEnumerator) {
    return This->lpVtbl->GetEnumerator(This,relationshipEnumerator);
}
static inline HRESULT IOpcRelationshipSet_GetEnumeratorForType(IOpcRelationshipSet* This,LPCWSTR relationshipType,IOpcRelationshipEnumerator **relationshipEnumerator) {
    return This->lpVtbl->GetEnumeratorForType(This,relationshipType,relationshipEnumerator);
}
static inline HRESULT IOpcRelationshipSet_GetRelationshipsContentStream(IOpcRelationshipSet* This,IStream **contents) {
    return This->lpVtbl->GetRelationshipsContentStream(This,contents);
}
#endif
#endif

#endif


#endif  /* __IOpcRelationshipSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignatureCustomObject interface
 */
#ifndef __IOpcSignatureCustomObject_INTERFACE_DEFINED__
#define __IOpcSignatureCustomObject_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignatureCustomObject, 0x5d77a19e, 0x62c1, 0x44e7, 0xbe,0xcd, 0x45,0xda,0x5a,0xe5,0x1a,0x56);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5d77a19e-62c1-44e7-becd-45da5ae51a56")
IOpcSignatureCustomObject : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetXml(
        UINT8 **xmlMarkup,
        UINT32 *count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignatureCustomObject, 0x5d77a19e, 0x62c1, 0x44e7, 0xbe,0xcd, 0x45,0xda,0x5a,0xe5,0x1a,0x56)
#endif
#else
typedef struct IOpcSignatureCustomObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignatureCustomObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignatureCustomObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignatureCustomObject *This);

    /*** IOpcSignatureCustomObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetXml)(
        IOpcSignatureCustomObject *This,
        UINT8 **xmlMarkup,
        UINT32 *count);

    END_INTERFACE
} IOpcSignatureCustomObjectVtbl;

interface IOpcSignatureCustomObject {
    CONST_VTBL IOpcSignatureCustomObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignatureCustomObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignatureCustomObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignatureCustomObject_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignatureCustomObject methods ***/
#define IOpcSignatureCustomObject_GetXml(This,xmlMarkup,count) (This)->lpVtbl->GetXml(This,xmlMarkup,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignatureCustomObject_QueryInterface(IOpcSignatureCustomObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignatureCustomObject_AddRef(IOpcSignatureCustomObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignatureCustomObject_Release(IOpcSignatureCustomObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignatureCustomObject methods ***/
static inline HRESULT IOpcSignatureCustomObject_GetXml(IOpcSignatureCustomObject* This,UINT8 **xmlMarkup,UINT32 *count) {
    return This->lpVtbl->GetXml(This,xmlMarkup,count);
}
#endif
#endif

#endif


#endif  /* __IOpcSignatureCustomObject_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignatureCustomObjectEnumerator interface
 */
#ifndef __IOpcSignatureCustomObjectEnumerator_INTERFACE_DEFINED__
#define __IOpcSignatureCustomObjectEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignatureCustomObjectEnumerator, 0x5ee4fe1d, 0xe1b0, 0x4683, 0x80,0x79, 0x7e,0xa0,0xfc,0xf8,0x0b,0x4c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5ee4fe1d-e1b0-4683-8079-7ea0fcf80b4c")
IOpcSignatureCustomObjectEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MoveNext(
        WINBOOL *hasNext) = 0;

    virtual HRESULT STDMETHODCALLTYPE MovePrevious(
        WINBOOL *hasPrevious) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrent(
        IOpcSignatureCustomObject **customObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IOpcSignatureCustomObjectEnumerator **copy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignatureCustomObjectEnumerator, 0x5ee4fe1d, 0xe1b0, 0x4683, 0x80,0x79, 0x7e,0xa0,0xfc,0xf8,0x0b,0x4c)
#endif
#else
typedef struct IOpcSignatureCustomObjectEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignatureCustomObjectEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignatureCustomObjectEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignatureCustomObjectEnumerator *This);

    /*** IOpcSignatureCustomObjectEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        IOpcSignatureCustomObjectEnumerator *This,
        WINBOOL *hasNext);

    HRESULT (STDMETHODCALLTYPE *MovePrevious)(
        IOpcSignatureCustomObjectEnumerator *This,
        WINBOOL *hasPrevious);

    HRESULT (STDMETHODCALLTYPE *GetCurrent)(
        IOpcSignatureCustomObjectEnumerator *This,
        IOpcSignatureCustomObject **customObject);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IOpcSignatureCustomObjectEnumerator *This,
        IOpcSignatureCustomObjectEnumerator **copy);

    END_INTERFACE
} IOpcSignatureCustomObjectEnumeratorVtbl;

interface IOpcSignatureCustomObjectEnumerator {
    CONST_VTBL IOpcSignatureCustomObjectEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignatureCustomObjectEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignatureCustomObjectEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignatureCustomObjectEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignatureCustomObjectEnumerator methods ***/
#define IOpcSignatureCustomObjectEnumerator_MoveNext(This,hasNext) (This)->lpVtbl->MoveNext(This,hasNext)
#define IOpcSignatureCustomObjectEnumerator_MovePrevious(This,hasPrevious) (This)->lpVtbl->MovePrevious(This,hasPrevious)
#define IOpcSignatureCustomObjectEnumerator_GetCurrent(This,customObject) (This)->lpVtbl->GetCurrent(This,customObject)
#define IOpcSignatureCustomObjectEnumerator_Clone(This,copy) (This)->lpVtbl->Clone(This,copy)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignatureCustomObjectEnumerator_QueryInterface(IOpcSignatureCustomObjectEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignatureCustomObjectEnumerator_AddRef(IOpcSignatureCustomObjectEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignatureCustomObjectEnumerator_Release(IOpcSignatureCustomObjectEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignatureCustomObjectEnumerator methods ***/
static inline HRESULT IOpcSignatureCustomObjectEnumerator_MoveNext(IOpcSignatureCustomObjectEnumerator* This,WINBOOL *hasNext) {
    return This->lpVtbl->MoveNext(This,hasNext);
}
static inline HRESULT IOpcSignatureCustomObjectEnumerator_MovePrevious(IOpcSignatureCustomObjectEnumerator* This,WINBOOL *hasPrevious) {
    return This->lpVtbl->MovePrevious(This,hasPrevious);
}
static inline HRESULT IOpcSignatureCustomObjectEnumerator_GetCurrent(IOpcSignatureCustomObjectEnumerator* This,IOpcSignatureCustomObject **customObject) {
    return This->lpVtbl->GetCurrent(This,customObject);
}
static inline HRESULT IOpcSignatureCustomObjectEnumerator_Clone(IOpcSignatureCustomObjectEnumerator* This,IOpcSignatureCustomObjectEnumerator **copy) {
    return This->lpVtbl->Clone(This,copy);
}
#endif
#endif

#endif


#endif  /* __IOpcSignatureCustomObjectEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignaturePartReference interface
 */
#ifndef __IOpcSignaturePartReference_INTERFACE_DEFINED__
#define __IOpcSignaturePartReference_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignaturePartReference, 0xe24231ca, 0x59f4, 0x484e, 0xb6,0x4b, 0x36,0xee,0xda,0x36,0x07,0x2c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e24231ca-59f4-484e-b64b-36eeda36072c")
IOpcSignaturePartReference : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPartName(
        IOpcPartUri **partName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContentType(
        LPWSTR *contentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDigestMethod(
        LPWSTR *digestMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDigestValue(
        UINT8 **digestValue,
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformMethod(
        OPC_CANONICALIZATION_METHOD *transformMethod) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignaturePartReference, 0xe24231ca, 0x59f4, 0x484e, 0xb6,0x4b, 0x36,0xee,0xda,0x36,0x07,0x2c)
#endif
#else
typedef struct IOpcSignaturePartReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignaturePartReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignaturePartReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignaturePartReference *This);

    /*** IOpcSignaturePartReference methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IOpcSignaturePartReference *This,
        IOpcPartUri **partName);

    HRESULT (STDMETHODCALLTYPE *GetContentType)(
        IOpcSignaturePartReference *This,
        LPWSTR *contentType);

    HRESULT (STDMETHODCALLTYPE *GetDigestMethod)(
        IOpcSignaturePartReference *This,
        LPWSTR *digestMethod);

    HRESULT (STDMETHODCALLTYPE *GetDigestValue)(
        IOpcSignaturePartReference *This,
        UINT8 **digestValue,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetTransformMethod)(
        IOpcSignaturePartReference *This,
        OPC_CANONICALIZATION_METHOD *transformMethod);

    END_INTERFACE
} IOpcSignaturePartReferenceVtbl;

interface IOpcSignaturePartReference {
    CONST_VTBL IOpcSignaturePartReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignaturePartReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignaturePartReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignaturePartReference_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignaturePartReference methods ***/
#define IOpcSignaturePartReference_GetPartName(This,partName) (This)->lpVtbl->GetPartName(This,partName)
#define IOpcSignaturePartReference_GetContentType(This,contentType) (This)->lpVtbl->GetContentType(This,contentType)
#define IOpcSignaturePartReference_GetDigestMethod(This,digestMethod) (This)->lpVtbl->GetDigestMethod(This,digestMethod)
#define IOpcSignaturePartReference_GetDigestValue(This,digestValue,count) (This)->lpVtbl->GetDigestValue(This,digestValue,count)
#define IOpcSignaturePartReference_GetTransformMethod(This,transformMethod) (This)->lpVtbl->GetTransformMethod(This,transformMethod)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignaturePartReference_QueryInterface(IOpcSignaturePartReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignaturePartReference_AddRef(IOpcSignaturePartReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignaturePartReference_Release(IOpcSignaturePartReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignaturePartReference methods ***/
static inline HRESULT IOpcSignaturePartReference_GetPartName(IOpcSignaturePartReference* This,IOpcPartUri **partName) {
    return This->lpVtbl->GetPartName(This,partName);
}
static inline HRESULT IOpcSignaturePartReference_GetContentType(IOpcSignaturePartReference* This,LPWSTR *contentType) {
    return This->lpVtbl->GetContentType(This,contentType);
}
static inline HRESULT IOpcSignaturePartReference_GetDigestMethod(IOpcSignaturePartReference* This,LPWSTR *digestMethod) {
    return This->lpVtbl->GetDigestMethod(This,digestMethod);
}
static inline HRESULT IOpcSignaturePartReference_GetDigestValue(IOpcSignaturePartReference* This,UINT8 **digestValue,UINT32 *count) {
    return This->lpVtbl->GetDigestValue(This,digestValue,count);
}
static inline HRESULT IOpcSignaturePartReference_GetTransformMethod(IOpcSignaturePartReference* This,OPC_CANONICALIZATION_METHOD *transformMethod) {
    return This->lpVtbl->GetTransformMethod(This,transformMethod);
}
#endif
#endif

#endif


#endif  /* __IOpcSignaturePartReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignaturePartReferenceEnumerator interface
 */
#ifndef __IOpcSignaturePartReferenceEnumerator_INTERFACE_DEFINED__
#define __IOpcSignaturePartReferenceEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignaturePartReferenceEnumerator, 0x80eb1561, 0x8c77, 0x49cf, 0x82,0x66, 0x45,0x9b,0x35,0x6e,0xe9,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("80eb1561-8c77-49cf-8266-459b356ee99a")
IOpcSignaturePartReferenceEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MoveNext(
        WINBOOL *hasNext) = 0;

    virtual HRESULT STDMETHODCALLTYPE MovePrevious(
        WINBOOL *hasPrevious) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrent(
        IOpcSignaturePartReference **partReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IOpcSignaturePartReferenceEnumerator **copy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignaturePartReferenceEnumerator, 0x80eb1561, 0x8c77, 0x49cf, 0x82,0x66, 0x45,0x9b,0x35,0x6e,0xe9,0x9a)
#endif
#else
typedef struct IOpcSignaturePartReferenceEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignaturePartReferenceEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignaturePartReferenceEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignaturePartReferenceEnumerator *This);

    /*** IOpcSignaturePartReferenceEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        IOpcSignaturePartReferenceEnumerator *This,
        WINBOOL *hasNext);

    HRESULT (STDMETHODCALLTYPE *MovePrevious)(
        IOpcSignaturePartReferenceEnumerator *This,
        WINBOOL *hasPrevious);

    HRESULT (STDMETHODCALLTYPE *GetCurrent)(
        IOpcSignaturePartReferenceEnumerator *This,
        IOpcSignaturePartReference **partReference);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IOpcSignaturePartReferenceEnumerator *This,
        IOpcSignaturePartReferenceEnumerator **copy);

    END_INTERFACE
} IOpcSignaturePartReferenceEnumeratorVtbl;

interface IOpcSignaturePartReferenceEnumerator {
    CONST_VTBL IOpcSignaturePartReferenceEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignaturePartReferenceEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignaturePartReferenceEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignaturePartReferenceEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignaturePartReferenceEnumerator methods ***/
#define IOpcSignaturePartReferenceEnumerator_MoveNext(This,hasNext) (This)->lpVtbl->MoveNext(This,hasNext)
#define IOpcSignaturePartReferenceEnumerator_MovePrevious(This,hasPrevious) (This)->lpVtbl->MovePrevious(This,hasPrevious)
#define IOpcSignaturePartReferenceEnumerator_GetCurrent(This,partReference) (This)->lpVtbl->GetCurrent(This,partReference)
#define IOpcSignaturePartReferenceEnumerator_Clone(This,copy) (This)->lpVtbl->Clone(This,copy)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignaturePartReferenceEnumerator_QueryInterface(IOpcSignaturePartReferenceEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignaturePartReferenceEnumerator_AddRef(IOpcSignaturePartReferenceEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignaturePartReferenceEnumerator_Release(IOpcSignaturePartReferenceEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignaturePartReferenceEnumerator methods ***/
static inline HRESULT IOpcSignaturePartReferenceEnumerator_MoveNext(IOpcSignaturePartReferenceEnumerator* This,WINBOOL *hasNext) {
    return This->lpVtbl->MoveNext(This,hasNext);
}
static inline HRESULT IOpcSignaturePartReferenceEnumerator_MovePrevious(IOpcSignaturePartReferenceEnumerator* This,WINBOOL *hasPrevious) {
    return This->lpVtbl->MovePrevious(This,hasPrevious);
}
static inline HRESULT IOpcSignaturePartReferenceEnumerator_GetCurrent(IOpcSignaturePartReferenceEnumerator* This,IOpcSignaturePartReference **partReference) {
    return This->lpVtbl->GetCurrent(This,partReference);
}
static inline HRESULT IOpcSignaturePartReferenceEnumerator_Clone(IOpcSignaturePartReferenceEnumerator* This,IOpcSignaturePartReferenceEnumerator **copy) {
    return This->lpVtbl->Clone(This,copy);
}
#endif
#endif

#endif


#endif  /* __IOpcSignaturePartReferenceEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignatureReference interface
 */
#ifndef __IOpcSignatureReference_INTERFACE_DEFINED__
#define __IOpcSignatureReference_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignatureReference, 0x1b47005e, 0x3011, 0x4edc, 0xbe,0x6f, 0x0f,0x65,0xe5,0xab,0x03,0x42);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1b47005e-3011-4edc-be6f-0f65e5ab0342")
IOpcSignatureReference : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetId(
        LPWSTR *referenceId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUri(
        IUri **referenceUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetType(
        LPWSTR *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformMethod(
        OPC_CANONICALIZATION_METHOD *transformMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDigestMethod(
        LPWSTR *digestMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDigestValue(
        UINT8 **digestValue,
        UINT32 *count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignatureReference, 0x1b47005e, 0x3011, 0x4edc, 0xbe,0x6f, 0x0f,0x65,0xe5,0xab,0x03,0x42)
#endif
#else
typedef struct IOpcSignatureReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignatureReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignatureReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignatureReference *This);

    /*** IOpcSignatureReference methods ***/
    HRESULT (STDMETHODCALLTYPE *GetId)(
        IOpcSignatureReference *This,
        LPWSTR *referenceId);

    HRESULT (STDMETHODCALLTYPE *GetUri)(
        IOpcSignatureReference *This,
        IUri **referenceUri);

    HRESULT (STDMETHODCALLTYPE *GetType)(
        IOpcSignatureReference *This,
        LPWSTR *type);

    HRESULT (STDMETHODCALLTYPE *GetTransformMethod)(
        IOpcSignatureReference *This,
        OPC_CANONICALIZATION_METHOD *transformMethod);

    HRESULT (STDMETHODCALLTYPE *GetDigestMethod)(
        IOpcSignatureReference *This,
        LPWSTR *digestMethod);

    HRESULT (STDMETHODCALLTYPE *GetDigestValue)(
        IOpcSignatureReference *This,
        UINT8 **digestValue,
        UINT32 *count);

    END_INTERFACE
} IOpcSignatureReferenceVtbl;

interface IOpcSignatureReference {
    CONST_VTBL IOpcSignatureReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignatureReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignatureReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignatureReference_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignatureReference methods ***/
#define IOpcSignatureReference_GetId(This,referenceId) (This)->lpVtbl->GetId(This,referenceId)
#define IOpcSignatureReference_GetUri(This,referenceUri) (This)->lpVtbl->GetUri(This,referenceUri)
#define IOpcSignatureReference_GetType(This,type) (This)->lpVtbl->GetType(This,type)
#define IOpcSignatureReference_GetTransformMethod(This,transformMethod) (This)->lpVtbl->GetTransformMethod(This,transformMethod)
#define IOpcSignatureReference_GetDigestMethod(This,digestMethod) (This)->lpVtbl->GetDigestMethod(This,digestMethod)
#define IOpcSignatureReference_GetDigestValue(This,digestValue,count) (This)->lpVtbl->GetDigestValue(This,digestValue,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignatureReference_QueryInterface(IOpcSignatureReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignatureReference_AddRef(IOpcSignatureReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignatureReference_Release(IOpcSignatureReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignatureReference methods ***/
static inline HRESULT IOpcSignatureReference_GetId(IOpcSignatureReference* This,LPWSTR *referenceId) {
    return This->lpVtbl->GetId(This,referenceId);
}
static inline HRESULT IOpcSignatureReference_GetUri(IOpcSignatureReference* This,IUri **referenceUri) {
    return This->lpVtbl->GetUri(This,referenceUri);
}
static inline HRESULT IOpcSignatureReference_GetType(IOpcSignatureReference* This,LPWSTR *type) {
    return This->lpVtbl->GetType(This,type);
}
static inline HRESULT IOpcSignatureReference_GetTransformMethod(IOpcSignatureReference* This,OPC_CANONICALIZATION_METHOD *transformMethod) {
    return This->lpVtbl->GetTransformMethod(This,transformMethod);
}
static inline HRESULT IOpcSignatureReference_GetDigestMethod(IOpcSignatureReference* This,LPWSTR *digestMethod) {
    return This->lpVtbl->GetDigestMethod(This,digestMethod);
}
static inline HRESULT IOpcSignatureReference_GetDigestValue(IOpcSignatureReference* This,UINT8 **digestValue,UINT32 *count) {
    return This->lpVtbl->GetDigestValue(This,digestValue,count);
}
#endif
#endif

#endif


#endif  /* __IOpcSignatureReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignatureReferenceEnumerator interface
 */
#ifndef __IOpcSignatureReferenceEnumerator_INTERFACE_DEFINED__
#define __IOpcSignatureReferenceEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignatureReferenceEnumerator, 0xcfa59a45, 0x28b1, 0x4868, 0x96,0x9e, 0xfa,0x80,0x97,0xfd,0xc1,0x2a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cfa59a45-28b1-4868-969e-fa8097fdc12a")
IOpcSignatureReferenceEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MoveNext(
        WINBOOL *hasNext) = 0;

    virtual HRESULT STDMETHODCALLTYPE MovePrevious(
        WINBOOL *hasPrevious) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrent(
        IOpcSignatureReference **reference) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IOpcSignatureReferenceEnumerator **copy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignatureReferenceEnumerator, 0xcfa59a45, 0x28b1, 0x4868, 0x96,0x9e, 0xfa,0x80,0x97,0xfd,0xc1,0x2a)
#endif
#else
typedef struct IOpcSignatureReferenceEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignatureReferenceEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignatureReferenceEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignatureReferenceEnumerator *This);

    /*** IOpcSignatureReferenceEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        IOpcSignatureReferenceEnumerator *This,
        WINBOOL *hasNext);

    HRESULT (STDMETHODCALLTYPE *MovePrevious)(
        IOpcSignatureReferenceEnumerator *This,
        WINBOOL *hasPrevious);

    HRESULT (STDMETHODCALLTYPE *GetCurrent)(
        IOpcSignatureReferenceEnumerator *This,
        IOpcSignatureReference **reference);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IOpcSignatureReferenceEnumerator *This,
        IOpcSignatureReferenceEnumerator **copy);

    END_INTERFACE
} IOpcSignatureReferenceEnumeratorVtbl;

interface IOpcSignatureReferenceEnumerator {
    CONST_VTBL IOpcSignatureReferenceEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignatureReferenceEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignatureReferenceEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignatureReferenceEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignatureReferenceEnumerator methods ***/
#define IOpcSignatureReferenceEnumerator_MoveNext(This,hasNext) (This)->lpVtbl->MoveNext(This,hasNext)
#define IOpcSignatureReferenceEnumerator_MovePrevious(This,hasPrevious) (This)->lpVtbl->MovePrevious(This,hasPrevious)
#define IOpcSignatureReferenceEnumerator_GetCurrent(This,reference) (This)->lpVtbl->GetCurrent(This,reference)
#define IOpcSignatureReferenceEnumerator_Clone(This,copy) (This)->lpVtbl->Clone(This,copy)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignatureReferenceEnumerator_QueryInterface(IOpcSignatureReferenceEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignatureReferenceEnumerator_AddRef(IOpcSignatureReferenceEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignatureReferenceEnumerator_Release(IOpcSignatureReferenceEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignatureReferenceEnumerator methods ***/
static inline HRESULT IOpcSignatureReferenceEnumerator_MoveNext(IOpcSignatureReferenceEnumerator* This,WINBOOL *hasNext) {
    return This->lpVtbl->MoveNext(This,hasNext);
}
static inline HRESULT IOpcSignatureReferenceEnumerator_MovePrevious(IOpcSignatureReferenceEnumerator* This,WINBOOL *hasPrevious) {
    return This->lpVtbl->MovePrevious(This,hasPrevious);
}
static inline HRESULT IOpcSignatureReferenceEnumerator_GetCurrent(IOpcSignatureReferenceEnumerator* This,IOpcSignatureReference **reference) {
    return This->lpVtbl->GetCurrent(This,reference);
}
static inline HRESULT IOpcSignatureReferenceEnumerator_Clone(IOpcSignatureReferenceEnumerator* This,IOpcSignatureReferenceEnumerator **copy) {
    return This->lpVtbl->Clone(This,copy);
}
#endif
#endif

#endif


#endif  /* __IOpcSignatureReferenceEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignatureRelationshipReferenceEnumerator interface
 */
#ifndef __IOpcSignatureRelationshipReferenceEnumerator_INTERFACE_DEFINED__
#define __IOpcSignatureRelationshipReferenceEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignatureRelationshipReferenceEnumerator, 0x773ba3e4, 0xf021, 0x48e4, 0xaa,0x04, 0x98,0x16,0xdb,0x5d,0x34,0x95);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("773ba3e4-f021-48e4-aa04-9816db5d3495")
IOpcSignatureRelationshipReferenceEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MoveNext(
        WINBOOL *hasNext) = 0;

    virtual HRESULT STDMETHODCALLTYPE MovePrevious(
        WINBOOL *hasPrevious) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrent(
        IOpcSignatureRelationshipReference **relationshipReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IOpcSignatureRelationshipReferenceEnumerator **copy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignatureRelationshipReferenceEnumerator, 0x773ba3e4, 0xf021, 0x48e4, 0xaa,0x04, 0x98,0x16,0xdb,0x5d,0x34,0x95)
#endif
#else
typedef struct IOpcSignatureRelationshipReferenceEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignatureRelationshipReferenceEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignatureRelationshipReferenceEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignatureRelationshipReferenceEnumerator *This);

    /*** IOpcSignatureRelationshipReferenceEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        IOpcSignatureRelationshipReferenceEnumerator *This,
        WINBOOL *hasNext);

    HRESULT (STDMETHODCALLTYPE *MovePrevious)(
        IOpcSignatureRelationshipReferenceEnumerator *This,
        WINBOOL *hasPrevious);

    HRESULT (STDMETHODCALLTYPE *GetCurrent)(
        IOpcSignatureRelationshipReferenceEnumerator *This,
        IOpcSignatureRelationshipReference **relationshipReference);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IOpcSignatureRelationshipReferenceEnumerator *This,
        IOpcSignatureRelationshipReferenceEnumerator **copy);

    END_INTERFACE
} IOpcSignatureRelationshipReferenceEnumeratorVtbl;

interface IOpcSignatureRelationshipReferenceEnumerator {
    CONST_VTBL IOpcSignatureRelationshipReferenceEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignatureRelationshipReferenceEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignatureRelationshipReferenceEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignatureRelationshipReferenceEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignatureRelationshipReferenceEnumerator methods ***/
#define IOpcSignatureRelationshipReferenceEnumerator_MoveNext(This,hasNext) (This)->lpVtbl->MoveNext(This,hasNext)
#define IOpcSignatureRelationshipReferenceEnumerator_MovePrevious(This,hasPrevious) (This)->lpVtbl->MovePrevious(This,hasPrevious)
#define IOpcSignatureRelationshipReferenceEnumerator_GetCurrent(This,relationshipReference) (This)->lpVtbl->GetCurrent(This,relationshipReference)
#define IOpcSignatureRelationshipReferenceEnumerator_Clone(This,copy) (This)->lpVtbl->Clone(This,copy)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignatureRelationshipReferenceEnumerator_QueryInterface(IOpcSignatureRelationshipReferenceEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignatureRelationshipReferenceEnumerator_AddRef(IOpcSignatureRelationshipReferenceEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignatureRelationshipReferenceEnumerator_Release(IOpcSignatureRelationshipReferenceEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignatureRelationshipReferenceEnumerator methods ***/
static inline HRESULT IOpcSignatureRelationshipReferenceEnumerator_MoveNext(IOpcSignatureRelationshipReferenceEnumerator* This,WINBOOL *hasNext) {
    return This->lpVtbl->MoveNext(This,hasNext);
}
static inline HRESULT IOpcSignatureRelationshipReferenceEnumerator_MovePrevious(IOpcSignatureRelationshipReferenceEnumerator* This,WINBOOL *hasPrevious) {
    return This->lpVtbl->MovePrevious(This,hasPrevious);
}
static inline HRESULT IOpcSignatureRelationshipReferenceEnumerator_GetCurrent(IOpcSignatureRelationshipReferenceEnumerator* This,IOpcSignatureRelationshipReference **relationshipReference) {
    return This->lpVtbl->GetCurrent(This,relationshipReference);
}
static inline HRESULT IOpcSignatureRelationshipReferenceEnumerator_Clone(IOpcSignatureRelationshipReferenceEnumerator* This,IOpcSignatureRelationshipReferenceEnumerator **copy) {
    return This->lpVtbl->Clone(This,copy);
}
#endif
#endif

#endif


#endif  /* __IOpcSignatureRelationshipReferenceEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignatureRelationshipReference interface
 */
#ifndef __IOpcSignatureRelationshipReference_INTERFACE_DEFINED__
#define __IOpcSignatureRelationshipReference_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignatureRelationshipReference, 0x57babac6, 0x9d4a, 0x4e50, 0x8b,0x86, 0xe5,0xd4,0x05,0x1e,0xae,0x7c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("57babac6-9d4a-4e50-8b86-e5d4051eae7c")
IOpcSignatureRelationshipReference : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSourceUri(
        IOpcUri **sourceUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDigestMethod(
        LPWSTR *digestMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDigestValue(
        UINT8 **digestValue,
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformMethod(
        OPC_CANONICALIZATION_METHOD *transformMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRelationshipSigningOption(
        OPC_RELATIONSHIPS_SIGNING_OPTION *relationshipSigningOption) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRelationshipSelectorEnumerator(
        IOpcRelationshipSelectorEnumerator **selectorEnumerator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignatureRelationshipReference, 0x57babac6, 0x9d4a, 0x4e50, 0x8b,0x86, 0xe5,0xd4,0x05,0x1e,0xae,0x7c)
#endif
#else
typedef struct IOpcSignatureRelationshipReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignatureRelationshipReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignatureRelationshipReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignatureRelationshipReference *This);

    /*** IOpcSignatureRelationshipReference methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSourceUri)(
        IOpcSignatureRelationshipReference *This,
        IOpcUri **sourceUri);

    HRESULT (STDMETHODCALLTYPE *GetDigestMethod)(
        IOpcSignatureRelationshipReference *This,
        LPWSTR *digestMethod);

    HRESULT (STDMETHODCALLTYPE *GetDigestValue)(
        IOpcSignatureRelationshipReference *This,
        UINT8 **digestValue,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetTransformMethod)(
        IOpcSignatureRelationshipReference *This,
        OPC_CANONICALIZATION_METHOD *transformMethod);

    HRESULT (STDMETHODCALLTYPE *GetRelationshipSigningOption)(
        IOpcSignatureRelationshipReference *This,
        OPC_RELATIONSHIPS_SIGNING_OPTION *relationshipSigningOption);

    HRESULT (STDMETHODCALLTYPE *GetRelationshipSelectorEnumerator)(
        IOpcSignatureRelationshipReference *This,
        IOpcRelationshipSelectorEnumerator **selectorEnumerator);

    END_INTERFACE
} IOpcSignatureRelationshipReferenceVtbl;

interface IOpcSignatureRelationshipReference {
    CONST_VTBL IOpcSignatureRelationshipReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignatureRelationshipReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignatureRelationshipReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignatureRelationshipReference_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignatureRelationshipReference methods ***/
#define IOpcSignatureRelationshipReference_GetSourceUri(This,sourceUri) (This)->lpVtbl->GetSourceUri(This,sourceUri)
#define IOpcSignatureRelationshipReference_GetDigestMethod(This,digestMethod) (This)->lpVtbl->GetDigestMethod(This,digestMethod)
#define IOpcSignatureRelationshipReference_GetDigestValue(This,digestValue,count) (This)->lpVtbl->GetDigestValue(This,digestValue,count)
#define IOpcSignatureRelationshipReference_GetTransformMethod(This,transformMethod) (This)->lpVtbl->GetTransformMethod(This,transformMethod)
#define IOpcSignatureRelationshipReference_GetRelationshipSigningOption(This,relationshipSigningOption) (This)->lpVtbl->GetRelationshipSigningOption(This,relationshipSigningOption)
#define IOpcSignatureRelationshipReference_GetRelationshipSelectorEnumerator(This,selectorEnumerator) (This)->lpVtbl->GetRelationshipSelectorEnumerator(This,selectorEnumerator)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignatureRelationshipReference_QueryInterface(IOpcSignatureRelationshipReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignatureRelationshipReference_AddRef(IOpcSignatureRelationshipReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignatureRelationshipReference_Release(IOpcSignatureRelationshipReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignatureRelationshipReference methods ***/
static inline HRESULT IOpcSignatureRelationshipReference_GetSourceUri(IOpcSignatureRelationshipReference* This,IOpcUri **sourceUri) {
    return This->lpVtbl->GetSourceUri(This,sourceUri);
}
static inline HRESULT IOpcSignatureRelationshipReference_GetDigestMethod(IOpcSignatureRelationshipReference* This,LPWSTR *digestMethod) {
    return This->lpVtbl->GetDigestMethod(This,digestMethod);
}
static inline HRESULT IOpcSignatureRelationshipReference_GetDigestValue(IOpcSignatureRelationshipReference* This,UINT8 **digestValue,UINT32 *count) {
    return This->lpVtbl->GetDigestValue(This,digestValue,count);
}
static inline HRESULT IOpcSignatureRelationshipReference_GetTransformMethod(IOpcSignatureRelationshipReference* This,OPC_CANONICALIZATION_METHOD *transformMethod) {
    return This->lpVtbl->GetTransformMethod(This,transformMethod);
}
static inline HRESULT IOpcSignatureRelationshipReference_GetRelationshipSigningOption(IOpcSignatureRelationshipReference* This,OPC_RELATIONSHIPS_SIGNING_OPTION *relationshipSigningOption) {
    return This->lpVtbl->GetRelationshipSigningOption(This,relationshipSigningOption);
}
static inline HRESULT IOpcSignatureRelationshipReference_GetRelationshipSelectorEnumerator(IOpcSignatureRelationshipReference* This,IOpcRelationshipSelectorEnumerator **selectorEnumerator) {
    return This->lpVtbl->GetRelationshipSelectorEnumerator(This,selectorEnumerator);
}
#endif
#endif

#endif


#endif  /* __IOpcSignatureRelationshipReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcRelationshipSelectorEnumerator interface
 */
#ifndef __IOpcRelationshipSelectorEnumerator_INTERFACE_DEFINED__
#define __IOpcRelationshipSelectorEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcRelationshipSelectorEnumerator, 0x5e50a181, 0xa91b, 0x48ac, 0x88,0xd2, 0xbc,0xa3,0xd8,0xf8,0xc0,0xb1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5e50a181-a91b-48ac-88d2-bca3d8f8c0b1")
IOpcRelationshipSelectorEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MoveNext(
        WINBOOL *hasNext) = 0;

    virtual HRESULT STDMETHODCALLTYPE MovePrevious(
        WINBOOL *hasPrevious) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrent(
        IOpcRelationshipSelector **relationshipSelector) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IOpcRelationshipSelectorEnumerator **copy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcRelationshipSelectorEnumerator, 0x5e50a181, 0xa91b, 0x48ac, 0x88,0xd2, 0xbc,0xa3,0xd8,0xf8,0xc0,0xb1)
#endif
#else
typedef struct IOpcRelationshipSelectorEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcRelationshipSelectorEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcRelationshipSelectorEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcRelationshipSelectorEnumerator *This);

    /*** IOpcRelationshipSelectorEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        IOpcRelationshipSelectorEnumerator *This,
        WINBOOL *hasNext);

    HRESULT (STDMETHODCALLTYPE *MovePrevious)(
        IOpcRelationshipSelectorEnumerator *This,
        WINBOOL *hasPrevious);

    HRESULT (STDMETHODCALLTYPE *GetCurrent)(
        IOpcRelationshipSelectorEnumerator *This,
        IOpcRelationshipSelector **relationshipSelector);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IOpcRelationshipSelectorEnumerator *This,
        IOpcRelationshipSelectorEnumerator **copy);

    END_INTERFACE
} IOpcRelationshipSelectorEnumeratorVtbl;

interface IOpcRelationshipSelectorEnumerator {
    CONST_VTBL IOpcRelationshipSelectorEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcRelationshipSelectorEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcRelationshipSelectorEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcRelationshipSelectorEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcRelationshipSelectorEnumerator methods ***/
#define IOpcRelationshipSelectorEnumerator_MoveNext(This,hasNext) (This)->lpVtbl->MoveNext(This,hasNext)
#define IOpcRelationshipSelectorEnumerator_MovePrevious(This,hasPrevious) (This)->lpVtbl->MovePrevious(This,hasPrevious)
#define IOpcRelationshipSelectorEnumerator_GetCurrent(This,relationshipSelector) (This)->lpVtbl->GetCurrent(This,relationshipSelector)
#define IOpcRelationshipSelectorEnumerator_Clone(This,copy) (This)->lpVtbl->Clone(This,copy)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcRelationshipSelectorEnumerator_QueryInterface(IOpcRelationshipSelectorEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcRelationshipSelectorEnumerator_AddRef(IOpcRelationshipSelectorEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcRelationshipSelectorEnumerator_Release(IOpcRelationshipSelectorEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcRelationshipSelectorEnumerator methods ***/
static inline HRESULT IOpcRelationshipSelectorEnumerator_MoveNext(IOpcRelationshipSelectorEnumerator* This,WINBOOL *hasNext) {
    return This->lpVtbl->MoveNext(This,hasNext);
}
static inline HRESULT IOpcRelationshipSelectorEnumerator_MovePrevious(IOpcRelationshipSelectorEnumerator* This,WINBOOL *hasPrevious) {
    return This->lpVtbl->MovePrevious(This,hasPrevious);
}
static inline HRESULT IOpcRelationshipSelectorEnumerator_GetCurrent(IOpcRelationshipSelectorEnumerator* This,IOpcRelationshipSelector **relationshipSelector) {
    return This->lpVtbl->GetCurrent(This,relationshipSelector);
}
static inline HRESULT IOpcRelationshipSelectorEnumerator_Clone(IOpcRelationshipSelectorEnumerator* This,IOpcRelationshipSelectorEnumerator **copy) {
    return This->lpVtbl->Clone(This,copy);
}
#endif
#endif

#endif


#endif  /* __IOpcRelationshipSelectorEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcCertificateEnumerator interface
 */
#ifndef __IOpcCertificateEnumerator_INTERFACE_DEFINED__
#define __IOpcCertificateEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcCertificateEnumerator, 0x85131937, 0x8f24, 0x421f, 0xb4,0x39, 0x59,0xab,0x24,0xd1,0x40,0xb8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("85131937-8f24-421f-b439-59ab24d140b8")
IOpcCertificateEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MoveNext(
        WINBOOL *hasNext) = 0;

    virtual HRESULT STDMETHODCALLTYPE MovePrevious(
        WINBOOL *hasPrevious) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrent(
        const CERT_CONTEXT **certificate) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IOpcCertificateEnumerator **copy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcCertificateEnumerator, 0x85131937, 0x8f24, 0x421f, 0xb4,0x39, 0x59,0xab,0x24,0xd1,0x40,0xb8)
#endif
#else
typedef struct IOpcCertificateEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcCertificateEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcCertificateEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcCertificateEnumerator *This);

    /*** IOpcCertificateEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        IOpcCertificateEnumerator *This,
        WINBOOL *hasNext);

    HRESULT (STDMETHODCALLTYPE *MovePrevious)(
        IOpcCertificateEnumerator *This,
        WINBOOL *hasPrevious);

    HRESULT (STDMETHODCALLTYPE *GetCurrent)(
        IOpcCertificateEnumerator *This,
        const CERT_CONTEXT **certificate);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IOpcCertificateEnumerator *This,
        IOpcCertificateEnumerator **copy);

    END_INTERFACE
} IOpcCertificateEnumeratorVtbl;

interface IOpcCertificateEnumerator {
    CONST_VTBL IOpcCertificateEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcCertificateEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcCertificateEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcCertificateEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcCertificateEnumerator methods ***/
#define IOpcCertificateEnumerator_MoveNext(This,hasNext) (This)->lpVtbl->MoveNext(This,hasNext)
#define IOpcCertificateEnumerator_MovePrevious(This,hasPrevious) (This)->lpVtbl->MovePrevious(This,hasPrevious)
#define IOpcCertificateEnumerator_GetCurrent(This,certificate) (This)->lpVtbl->GetCurrent(This,certificate)
#define IOpcCertificateEnumerator_Clone(This,copy) (This)->lpVtbl->Clone(This,copy)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcCertificateEnumerator_QueryInterface(IOpcCertificateEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcCertificateEnumerator_AddRef(IOpcCertificateEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcCertificateEnumerator_Release(IOpcCertificateEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcCertificateEnumerator methods ***/
static inline HRESULT IOpcCertificateEnumerator_MoveNext(IOpcCertificateEnumerator* This,WINBOOL *hasNext) {
    return This->lpVtbl->MoveNext(This,hasNext);
}
static inline HRESULT IOpcCertificateEnumerator_MovePrevious(IOpcCertificateEnumerator* This,WINBOOL *hasPrevious) {
    return This->lpVtbl->MovePrevious(This,hasPrevious);
}
static inline HRESULT IOpcCertificateEnumerator_GetCurrent(IOpcCertificateEnumerator* This,const CERT_CONTEXT **certificate) {
    return This->lpVtbl->GetCurrent(This,certificate);
}
static inline HRESULT IOpcCertificateEnumerator_Clone(IOpcCertificateEnumerator* This,IOpcCertificateEnumerator **copy) {
    return This->lpVtbl->Clone(This,copy);
}
#endif
#endif

#endif


#endif  /* __IOpcCertificateEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcCertificateSet interface
 */
#ifndef __IOpcCertificateSet_INTERFACE_DEFINED__
#define __IOpcCertificateSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcCertificateSet, 0x56ea4325, 0x8e2d, 0x4167, 0xb1,0xa4, 0xe4,0x86,0xd2,0x4c,0x8f,0xa7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56ea4325-8e2d-4167-b1a4-e486d24c8fa7")
IOpcCertificateSet : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Add(
        const CERT_CONTEXT *certificate) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        const CERT_CONTEXT *certificate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumerator(
        IOpcCertificateEnumerator **certificateEnumerator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcCertificateSet, 0x56ea4325, 0x8e2d, 0x4167, 0xb1,0xa4, 0xe4,0x86,0xd2,0x4c,0x8f,0xa7)
#endif
#else
typedef struct IOpcCertificateSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcCertificateSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcCertificateSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcCertificateSet *This);

    /*** IOpcCertificateSet methods ***/
    HRESULT (STDMETHODCALLTYPE *Add)(
        IOpcCertificateSet *This,
        const CERT_CONTEXT *certificate);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        IOpcCertificateSet *This,
        const CERT_CONTEXT *certificate);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IOpcCertificateSet *This,
        IOpcCertificateEnumerator **certificateEnumerator);

    END_INTERFACE
} IOpcCertificateSetVtbl;

interface IOpcCertificateSet {
    CONST_VTBL IOpcCertificateSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcCertificateSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcCertificateSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcCertificateSet_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcCertificateSet methods ***/
#define IOpcCertificateSet_Add(This,certificate) (This)->lpVtbl->Add(This,certificate)
#define IOpcCertificateSet_Remove(This,certificate) (This)->lpVtbl->Remove(This,certificate)
#define IOpcCertificateSet_GetEnumerator(This,certificateEnumerator) (This)->lpVtbl->GetEnumerator(This,certificateEnumerator)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcCertificateSet_QueryInterface(IOpcCertificateSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcCertificateSet_AddRef(IOpcCertificateSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcCertificateSet_Release(IOpcCertificateSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcCertificateSet methods ***/
static inline HRESULT IOpcCertificateSet_Add(IOpcCertificateSet* This,const CERT_CONTEXT *certificate) {
    return This->lpVtbl->Add(This,certificate);
}
static inline HRESULT IOpcCertificateSet_Remove(IOpcCertificateSet* This,const CERT_CONTEXT *certificate) {
    return This->lpVtbl->Remove(This,certificate);
}
static inline HRESULT IOpcCertificateSet_GetEnumerator(IOpcCertificateSet* This,IOpcCertificateEnumerator **certificateEnumerator) {
    return This->lpVtbl->GetEnumerator(This,certificateEnumerator);
}
#endif
#endif

#endif


#endif  /* __IOpcCertificateSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcDigitalSignatureEnumerator interface
 */
#ifndef __IOpcDigitalSignatureEnumerator_INTERFACE_DEFINED__
#define __IOpcDigitalSignatureEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcDigitalSignatureEnumerator, 0x967b6882, 0x0ba3, 0x4358, 0xb9,0xe7, 0xb6,0x4c,0x75,0x06,0x3c,0x5e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("967b6882-0ba3-4358-b9e7-b64c75063c5e")
IOpcDigitalSignatureEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MoveNext(
        WINBOOL *hasNext) = 0;

    virtual HRESULT STDMETHODCALLTYPE MovePrevious(
        WINBOOL *hasPrevious) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrent(
        IOpcDigitalSignature **digitalSignature) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IOpcDigitalSignatureEnumerator **copy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcDigitalSignatureEnumerator, 0x967b6882, 0x0ba3, 0x4358, 0xb9,0xe7, 0xb6,0x4c,0x75,0x06,0x3c,0x5e)
#endif
#else
typedef struct IOpcDigitalSignatureEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcDigitalSignatureEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcDigitalSignatureEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcDigitalSignatureEnumerator *This);

    /*** IOpcDigitalSignatureEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        IOpcDigitalSignatureEnumerator *This,
        WINBOOL *hasNext);

    HRESULT (STDMETHODCALLTYPE *MovePrevious)(
        IOpcDigitalSignatureEnumerator *This,
        WINBOOL *hasPrevious);

    HRESULT (STDMETHODCALLTYPE *GetCurrent)(
        IOpcDigitalSignatureEnumerator *This,
        IOpcDigitalSignature **digitalSignature);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IOpcDigitalSignatureEnumerator *This,
        IOpcDigitalSignatureEnumerator **copy);

    END_INTERFACE
} IOpcDigitalSignatureEnumeratorVtbl;

interface IOpcDigitalSignatureEnumerator {
    CONST_VTBL IOpcDigitalSignatureEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcDigitalSignatureEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcDigitalSignatureEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcDigitalSignatureEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcDigitalSignatureEnumerator methods ***/
#define IOpcDigitalSignatureEnumerator_MoveNext(This,hasNext) (This)->lpVtbl->MoveNext(This,hasNext)
#define IOpcDigitalSignatureEnumerator_MovePrevious(This,hasPrevious) (This)->lpVtbl->MovePrevious(This,hasPrevious)
#define IOpcDigitalSignatureEnumerator_GetCurrent(This,digitalSignature) (This)->lpVtbl->GetCurrent(This,digitalSignature)
#define IOpcDigitalSignatureEnumerator_Clone(This,copy) (This)->lpVtbl->Clone(This,copy)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcDigitalSignatureEnumerator_QueryInterface(IOpcDigitalSignatureEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcDigitalSignatureEnumerator_AddRef(IOpcDigitalSignatureEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcDigitalSignatureEnumerator_Release(IOpcDigitalSignatureEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcDigitalSignatureEnumerator methods ***/
static inline HRESULT IOpcDigitalSignatureEnumerator_MoveNext(IOpcDigitalSignatureEnumerator* This,WINBOOL *hasNext) {
    return This->lpVtbl->MoveNext(This,hasNext);
}
static inline HRESULT IOpcDigitalSignatureEnumerator_MovePrevious(IOpcDigitalSignatureEnumerator* This,WINBOOL *hasPrevious) {
    return This->lpVtbl->MovePrevious(This,hasPrevious);
}
static inline HRESULT IOpcDigitalSignatureEnumerator_GetCurrent(IOpcDigitalSignatureEnumerator* This,IOpcDigitalSignature **digitalSignature) {
    return This->lpVtbl->GetCurrent(This,digitalSignature);
}
static inline HRESULT IOpcDigitalSignatureEnumerator_Clone(IOpcDigitalSignatureEnumerator* This,IOpcDigitalSignatureEnumerator **copy) {
    return This->lpVtbl->Clone(This,copy);
}
#endif
#endif

#endif


#endif  /* __IOpcDigitalSignatureEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcDigitalSignatureManager interface
 */
#ifndef __IOpcDigitalSignatureManager_INTERFACE_DEFINED__
#define __IOpcDigitalSignatureManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcDigitalSignatureManager, 0xd5e62a0b, 0x696d, 0x462f, 0x94,0xdf, 0x72,0xe3,0x3c,0xef,0x26,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d5e62a0b-696d-462f-94df-72e33cef2659")
IOpcDigitalSignatureManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSignatureOriginPartName(
        IOpcPartUri **signatureOriginPartName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignatureOriginPartName(
        IOpcPartUri *signatureOriginPartName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureEnumerator(
        IOpcDigitalSignatureEnumerator **signatureEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveSignature(
        IOpcPartUri *signaturePartName) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSigningOptions(
        IOpcSigningOptions **signingOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE Validate(
        IOpcDigitalSignature *signature,
        const CERT_CONTEXT *certificate,
        OPC_SIGNATURE_VALIDATION_RESULT *validationResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE Sign(
        const CERT_CONTEXT *certificate,
        IOpcSigningOptions *signingOptions,
        IOpcDigitalSignature **digitalSignature) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReplaceSignatureXml(
        IOpcPartUri *signaturePartName,
        const UINT8 *newSignatureXml,
        UINT32 count,
        IOpcDigitalSignature **digitalSignature) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcDigitalSignatureManager, 0xd5e62a0b, 0x696d, 0x462f, 0x94,0xdf, 0x72,0xe3,0x3c,0xef,0x26,0x59)
#endif
#else
typedef struct IOpcDigitalSignatureManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcDigitalSignatureManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcDigitalSignatureManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcDigitalSignatureManager *This);

    /*** IOpcDigitalSignatureManager methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSignatureOriginPartName)(
        IOpcDigitalSignatureManager *This,
        IOpcPartUri **signatureOriginPartName);

    HRESULT (STDMETHODCALLTYPE *SetSignatureOriginPartName)(
        IOpcDigitalSignatureManager *This,
        IOpcPartUri *signatureOriginPartName);

    HRESULT (STDMETHODCALLTYPE *GetSignatureEnumerator)(
        IOpcDigitalSignatureManager *This,
        IOpcDigitalSignatureEnumerator **signatureEnumerator);

    HRESULT (STDMETHODCALLTYPE *RemoveSignature)(
        IOpcDigitalSignatureManager *This,
        IOpcPartUri *signaturePartName);

    HRESULT (STDMETHODCALLTYPE *CreateSigningOptions)(
        IOpcDigitalSignatureManager *This,
        IOpcSigningOptions **signingOptions);

    HRESULT (STDMETHODCALLTYPE *Validate)(
        IOpcDigitalSignatureManager *This,
        IOpcDigitalSignature *signature,
        const CERT_CONTEXT *certificate,
        OPC_SIGNATURE_VALIDATION_RESULT *validationResult);

    HRESULT (STDMETHODCALLTYPE *Sign)(
        IOpcDigitalSignatureManager *This,
        const CERT_CONTEXT *certificate,
        IOpcSigningOptions *signingOptions,
        IOpcDigitalSignature **digitalSignature);

    HRESULT (STDMETHODCALLTYPE *ReplaceSignatureXml)(
        IOpcDigitalSignatureManager *This,
        IOpcPartUri *signaturePartName,
        const UINT8 *newSignatureXml,
        UINT32 count,
        IOpcDigitalSignature **digitalSignature);

    END_INTERFACE
} IOpcDigitalSignatureManagerVtbl;

interface IOpcDigitalSignatureManager {
    CONST_VTBL IOpcDigitalSignatureManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcDigitalSignatureManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcDigitalSignatureManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcDigitalSignatureManager_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcDigitalSignatureManager methods ***/
#define IOpcDigitalSignatureManager_GetSignatureOriginPartName(This,signatureOriginPartName) (This)->lpVtbl->GetSignatureOriginPartName(This,signatureOriginPartName)
#define IOpcDigitalSignatureManager_SetSignatureOriginPartName(This,signatureOriginPartName) (This)->lpVtbl->SetSignatureOriginPartName(This,signatureOriginPartName)
#define IOpcDigitalSignatureManager_GetSignatureEnumerator(This,signatureEnumerator) (This)->lpVtbl->GetSignatureEnumerator(This,signatureEnumerator)
#define IOpcDigitalSignatureManager_RemoveSignature(This,signaturePartName) (This)->lpVtbl->RemoveSignature(This,signaturePartName)
#define IOpcDigitalSignatureManager_CreateSigningOptions(This,signingOptions) (This)->lpVtbl->CreateSigningOptions(This,signingOptions)
#define IOpcDigitalSignatureManager_Validate(This,signature,certificate,validationResult) (This)->lpVtbl->Validate(This,signature,certificate,validationResult)
#define IOpcDigitalSignatureManager_Sign(This,certificate,signingOptions,digitalSignature) (This)->lpVtbl->Sign(This,certificate,signingOptions,digitalSignature)
#define IOpcDigitalSignatureManager_ReplaceSignatureXml(This,signaturePartName,newSignatureXml,count,digitalSignature) (This)->lpVtbl->ReplaceSignatureXml(This,signaturePartName,newSignatureXml,count,digitalSignature)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcDigitalSignatureManager_QueryInterface(IOpcDigitalSignatureManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcDigitalSignatureManager_AddRef(IOpcDigitalSignatureManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcDigitalSignatureManager_Release(IOpcDigitalSignatureManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcDigitalSignatureManager methods ***/
static inline HRESULT IOpcDigitalSignatureManager_GetSignatureOriginPartName(IOpcDigitalSignatureManager* This,IOpcPartUri **signatureOriginPartName) {
    return This->lpVtbl->GetSignatureOriginPartName(This,signatureOriginPartName);
}
static inline HRESULT IOpcDigitalSignatureManager_SetSignatureOriginPartName(IOpcDigitalSignatureManager* This,IOpcPartUri *signatureOriginPartName) {
    return This->lpVtbl->SetSignatureOriginPartName(This,signatureOriginPartName);
}
static inline HRESULT IOpcDigitalSignatureManager_GetSignatureEnumerator(IOpcDigitalSignatureManager* This,IOpcDigitalSignatureEnumerator **signatureEnumerator) {
    return This->lpVtbl->GetSignatureEnumerator(This,signatureEnumerator);
}
static inline HRESULT IOpcDigitalSignatureManager_RemoveSignature(IOpcDigitalSignatureManager* This,IOpcPartUri *signaturePartName) {
    return This->lpVtbl->RemoveSignature(This,signaturePartName);
}
static inline HRESULT IOpcDigitalSignatureManager_CreateSigningOptions(IOpcDigitalSignatureManager* This,IOpcSigningOptions **signingOptions) {
    return This->lpVtbl->CreateSigningOptions(This,signingOptions);
}
static inline HRESULT IOpcDigitalSignatureManager_Validate(IOpcDigitalSignatureManager* This,IOpcDigitalSignature *signature,const CERT_CONTEXT *certificate,OPC_SIGNATURE_VALIDATION_RESULT *validationResult) {
    return This->lpVtbl->Validate(This,signature,certificate,validationResult);
}
static inline HRESULT IOpcDigitalSignatureManager_Sign(IOpcDigitalSignatureManager* This,const CERT_CONTEXT *certificate,IOpcSigningOptions *signingOptions,IOpcDigitalSignature **digitalSignature) {
    return This->lpVtbl->Sign(This,certificate,signingOptions,digitalSignature);
}
static inline HRESULT IOpcDigitalSignatureManager_ReplaceSignatureXml(IOpcDigitalSignatureManager* This,IOpcPartUri *signaturePartName,const UINT8 *newSignatureXml,UINT32 count,IOpcDigitalSignature **digitalSignature) {
    return This->lpVtbl->ReplaceSignatureXml(This,signaturePartName,newSignatureXml,count,digitalSignature);
}
#endif
#endif

#endif


#endif  /* __IOpcDigitalSignatureManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignaturePartReferenceSet interface
 */
#ifndef __IOpcSignaturePartReferenceSet_INTERFACE_DEFINED__
#define __IOpcSignaturePartReferenceSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignaturePartReferenceSet, 0x6c9fe28c, 0xecd9, 0x4b22, 0x9d,0x36, 0x7f,0xdd,0xe6,0x70,0xfe,0xc0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6c9fe28c-ecd9-4b22-9d36-7fdde670fec0")
IOpcSignaturePartReferenceSet : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Create(
        IOpcPartUri *partUri,
        LPCWSTR digestMethod,
        OPC_CANONICALIZATION_METHOD transformMethod,
        IOpcSignaturePartReference **partReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        IOpcSignaturePartReference *partReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumerator(
        IOpcSignaturePartReferenceEnumerator **partReferenceEnumerator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignaturePartReferenceSet, 0x6c9fe28c, 0xecd9, 0x4b22, 0x9d,0x36, 0x7f,0xdd,0xe6,0x70,0xfe,0xc0)
#endif
#else
typedef struct IOpcSignaturePartReferenceSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignaturePartReferenceSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignaturePartReferenceSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignaturePartReferenceSet *This);

    /*** IOpcSignaturePartReferenceSet methods ***/
    HRESULT (STDMETHODCALLTYPE *Create)(
        IOpcSignaturePartReferenceSet *This,
        IOpcPartUri *partUri,
        LPCWSTR digestMethod,
        OPC_CANONICALIZATION_METHOD transformMethod,
        IOpcSignaturePartReference **partReference);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IOpcSignaturePartReferenceSet *This,
        IOpcSignaturePartReference *partReference);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IOpcSignaturePartReferenceSet *This,
        IOpcSignaturePartReferenceEnumerator **partReferenceEnumerator);

    END_INTERFACE
} IOpcSignaturePartReferenceSetVtbl;

interface IOpcSignaturePartReferenceSet {
    CONST_VTBL IOpcSignaturePartReferenceSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignaturePartReferenceSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignaturePartReferenceSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignaturePartReferenceSet_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignaturePartReferenceSet methods ***/
#define IOpcSignaturePartReferenceSet_Create(This,partUri,digestMethod,transformMethod,partReference) (This)->lpVtbl->Create(This,partUri,digestMethod,transformMethod,partReference)
#define IOpcSignaturePartReferenceSet_Delete(This,partReference) (This)->lpVtbl->Delete(This,partReference)
#define IOpcSignaturePartReferenceSet_GetEnumerator(This,partReferenceEnumerator) (This)->lpVtbl->GetEnumerator(This,partReferenceEnumerator)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignaturePartReferenceSet_QueryInterface(IOpcSignaturePartReferenceSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignaturePartReferenceSet_AddRef(IOpcSignaturePartReferenceSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignaturePartReferenceSet_Release(IOpcSignaturePartReferenceSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignaturePartReferenceSet methods ***/
static inline HRESULT IOpcSignaturePartReferenceSet_Create(IOpcSignaturePartReferenceSet* This,IOpcPartUri *partUri,LPCWSTR digestMethod,OPC_CANONICALIZATION_METHOD transformMethod,IOpcSignaturePartReference **partReference) {
    return This->lpVtbl->Create(This,partUri,digestMethod,transformMethod,partReference);
}
static inline HRESULT IOpcSignaturePartReferenceSet_Delete(IOpcSignaturePartReferenceSet* This,IOpcSignaturePartReference *partReference) {
    return This->lpVtbl->Delete(This,partReference);
}
static inline HRESULT IOpcSignaturePartReferenceSet_GetEnumerator(IOpcSignaturePartReferenceSet* This,IOpcSignaturePartReferenceEnumerator **partReferenceEnumerator) {
    return This->lpVtbl->GetEnumerator(This,partReferenceEnumerator);
}
#endif
#endif

#endif


#endif  /* __IOpcSignaturePartReferenceSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcRelationshipSelectorSet interface
 */
#ifndef __IOpcRelationshipSelectorSet_INTERFACE_DEFINED__
#define __IOpcRelationshipSelectorSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcRelationshipSelectorSet, 0x6e34c269, 0xa4d3, 0x47c0, 0xb5,0xc4, 0x87,0xff,0x2b,0x3b,0x61,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6e34c269-a4d3-47c0-b5c4-87ff2b3b6136")
IOpcRelationshipSelectorSet : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Create(
        OPC_RELATIONSHIP_SELECTOR selector,
        LPCWSTR selectionCriterion,
        IOpcRelationshipSelector **relationshipSelector) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        IOpcRelationshipSelector *relationshipSelector) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumerator(
        IOpcRelationshipSelectorEnumerator **relationshipSelectorEnumerator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcRelationshipSelectorSet, 0x6e34c269, 0xa4d3, 0x47c0, 0xb5,0xc4, 0x87,0xff,0x2b,0x3b,0x61,0x36)
#endif
#else
typedef struct IOpcRelationshipSelectorSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcRelationshipSelectorSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcRelationshipSelectorSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcRelationshipSelectorSet *This);

    /*** IOpcRelationshipSelectorSet methods ***/
    HRESULT (STDMETHODCALLTYPE *Create)(
        IOpcRelationshipSelectorSet *This,
        OPC_RELATIONSHIP_SELECTOR selector,
        LPCWSTR selectionCriterion,
        IOpcRelationshipSelector **relationshipSelector);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IOpcRelationshipSelectorSet *This,
        IOpcRelationshipSelector *relationshipSelector);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IOpcRelationshipSelectorSet *This,
        IOpcRelationshipSelectorEnumerator **relationshipSelectorEnumerator);

    END_INTERFACE
} IOpcRelationshipSelectorSetVtbl;

interface IOpcRelationshipSelectorSet {
    CONST_VTBL IOpcRelationshipSelectorSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcRelationshipSelectorSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcRelationshipSelectorSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcRelationshipSelectorSet_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcRelationshipSelectorSet methods ***/
#define IOpcRelationshipSelectorSet_Create(This,selector,selectionCriterion,relationshipSelector) (This)->lpVtbl->Create(This,selector,selectionCriterion,relationshipSelector)
#define IOpcRelationshipSelectorSet_Delete(This,relationshipSelector) (This)->lpVtbl->Delete(This,relationshipSelector)
#define IOpcRelationshipSelectorSet_GetEnumerator(This,relationshipSelectorEnumerator) (This)->lpVtbl->GetEnumerator(This,relationshipSelectorEnumerator)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcRelationshipSelectorSet_QueryInterface(IOpcRelationshipSelectorSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcRelationshipSelectorSet_AddRef(IOpcRelationshipSelectorSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcRelationshipSelectorSet_Release(IOpcRelationshipSelectorSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcRelationshipSelectorSet methods ***/
static inline HRESULT IOpcRelationshipSelectorSet_Create(IOpcRelationshipSelectorSet* This,OPC_RELATIONSHIP_SELECTOR selector,LPCWSTR selectionCriterion,IOpcRelationshipSelector **relationshipSelector) {
    return This->lpVtbl->Create(This,selector,selectionCriterion,relationshipSelector);
}
static inline HRESULT IOpcRelationshipSelectorSet_Delete(IOpcRelationshipSelectorSet* This,IOpcRelationshipSelector *relationshipSelector) {
    return This->lpVtbl->Delete(This,relationshipSelector);
}
static inline HRESULT IOpcRelationshipSelectorSet_GetEnumerator(IOpcRelationshipSelectorSet* This,IOpcRelationshipSelectorEnumerator **relationshipSelectorEnumerator) {
    return This->lpVtbl->GetEnumerator(This,relationshipSelectorEnumerator);
}
#endif
#endif

#endif


#endif  /* __IOpcRelationshipSelectorSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignatureRelationshipReferenceSet interface
 */
#ifndef __IOpcSignatureRelationshipReferenceSet_INTERFACE_DEFINED__
#define __IOpcSignatureRelationshipReferenceSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignatureRelationshipReferenceSet, 0x9f863ca5, 0x3631, 0x404c, 0x82,0x8d, 0x80,0x7e,0x07,0x15,0x06,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9f863ca5-3631-404c-828d-807e0715069b")
IOpcSignatureRelationshipReferenceSet : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Create(
        IOpcUri *sourceUri,
        LPCWSTR digestMethod,
        OPC_RELATIONSHIPS_SIGNING_OPTION relationshipSigningOption,
        IOpcRelationshipSelectorSet *selectorSet,
        OPC_CANONICALIZATION_METHOD transformMethod,
        IOpcSignatureRelationshipReference **relationshipReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRelationshipSelectorSet(
        IOpcRelationshipSelectorSet **selectorSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        IOpcSignatureRelationshipReference *relationshipReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumerator(
        IOpcSignatureRelationshipReferenceEnumerator **relationshipReferenceEnumerator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignatureRelationshipReferenceSet, 0x9f863ca5, 0x3631, 0x404c, 0x82,0x8d, 0x80,0x7e,0x07,0x15,0x06,0x9b)
#endif
#else
typedef struct IOpcSignatureRelationshipReferenceSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignatureRelationshipReferenceSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignatureRelationshipReferenceSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignatureRelationshipReferenceSet *This);

    /*** IOpcSignatureRelationshipReferenceSet methods ***/
    HRESULT (STDMETHODCALLTYPE *Create)(
        IOpcSignatureRelationshipReferenceSet *This,
        IOpcUri *sourceUri,
        LPCWSTR digestMethod,
        OPC_RELATIONSHIPS_SIGNING_OPTION relationshipSigningOption,
        IOpcRelationshipSelectorSet *selectorSet,
        OPC_CANONICALIZATION_METHOD transformMethod,
        IOpcSignatureRelationshipReference **relationshipReference);

    HRESULT (STDMETHODCALLTYPE *CreateRelationshipSelectorSet)(
        IOpcSignatureRelationshipReferenceSet *This,
        IOpcRelationshipSelectorSet **selectorSet);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IOpcSignatureRelationshipReferenceSet *This,
        IOpcSignatureRelationshipReference *relationshipReference);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IOpcSignatureRelationshipReferenceSet *This,
        IOpcSignatureRelationshipReferenceEnumerator **relationshipReferenceEnumerator);

    END_INTERFACE
} IOpcSignatureRelationshipReferenceSetVtbl;

interface IOpcSignatureRelationshipReferenceSet {
    CONST_VTBL IOpcSignatureRelationshipReferenceSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignatureRelationshipReferenceSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignatureRelationshipReferenceSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignatureRelationshipReferenceSet_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignatureRelationshipReferenceSet methods ***/
#define IOpcSignatureRelationshipReferenceSet_Create(This,sourceUri,digestMethod,relationshipSigningOption,selectorSet,transformMethod,relationshipReference) (This)->lpVtbl->Create(This,sourceUri,digestMethod,relationshipSigningOption,selectorSet,transformMethod,relationshipReference)
#define IOpcSignatureRelationshipReferenceSet_CreateRelationshipSelectorSet(This,selectorSet) (This)->lpVtbl->CreateRelationshipSelectorSet(This,selectorSet)
#define IOpcSignatureRelationshipReferenceSet_Delete(This,relationshipReference) (This)->lpVtbl->Delete(This,relationshipReference)
#define IOpcSignatureRelationshipReferenceSet_GetEnumerator(This,relationshipReferenceEnumerator) (This)->lpVtbl->GetEnumerator(This,relationshipReferenceEnumerator)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignatureRelationshipReferenceSet_QueryInterface(IOpcSignatureRelationshipReferenceSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignatureRelationshipReferenceSet_AddRef(IOpcSignatureRelationshipReferenceSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignatureRelationshipReferenceSet_Release(IOpcSignatureRelationshipReferenceSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignatureRelationshipReferenceSet methods ***/
static inline HRESULT IOpcSignatureRelationshipReferenceSet_Create(IOpcSignatureRelationshipReferenceSet* This,IOpcUri *sourceUri,LPCWSTR digestMethod,OPC_RELATIONSHIPS_SIGNING_OPTION relationshipSigningOption,IOpcRelationshipSelectorSet *selectorSet,OPC_CANONICALIZATION_METHOD transformMethod,IOpcSignatureRelationshipReference **relationshipReference) {
    return This->lpVtbl->Create(This,sourceUri,digestMethod,relationshipSigningOption,selectorSet,transformMethod,relationshipReference);
}
static inline HRESULT IOpcSignatureRelationshipReferenceSet_CreateRelationshipSelectorSet(IOpcSignatureRelationshipReferenceSet* This,IOpcRelationshipSelectorSet **selectorSet) {
    return This->lpVtbl->CreateRelationshipSelectorSet(This,selectorSet);
}
static inline HRESULT IOpcSignatureRelationshipReferenceSet_Delete(IOpcSignatureRelationshipReferenceSet* This,IOpcSignatureRelationshipReference *relationshipReference) {
    return This->lpVtbl->Delete(This,relationshipReference);
}
static inline HRESULT IOpcSignatureRelationshipReferenceSet_GetEnumerator(IOpcSignatureRelationshipReferenceSet* This,IOpcSignatureRelationshipReferenceEnumerator **relationshipReferenceEnumerator) {
    return This->lpVtbl->GetEnumerator(This,relationshipReferenceEnumerator);
}
#endif
#endif

#endif


#endif  /* __IOpcSignatureRelationshipReferenceSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignatureReferenceSet interface
 */
#ifndef __IOpcSignatureReferenceSet_INTERFACE_DEFINED__
#define __IOpcSignatureReferenceSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignatureReferenceSet, 0xf3b02d31, 0xab12, 0x42dd, 0x9e,0x2f, 0x2b,0x16,0x76,0x1c,0x3c,0x1e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f3b02d31-ab12-42dd-9e2f-2b16761c3c1e")
IOpcSignatureReferenceSet : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Create(
        IUri *referenceUri,
        LPCWSTR referenceId,
        LPCWSTR type,
        LPCWSTR digestMethod,
        OPC_CANONICALIZATION_METHOD transformMethod,
        IOpcSignatureReference **reference) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        IOpcSignatureReference *reference) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumerator(
        IOpcSignatureReferenceEnumerator **referenceEnumerator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignatureReferenceSet, 0xf3b02d31, 0xab12, 0x42dd, 0x9e,0x2f, 0x2b,0x16,0x76,0x1c,0x3c,0x1e)
#endif
#else
typedef struct IOpcSignatureReferenceSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignatureReferenceSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignatureReferenceSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignatureReferenceSet *This);

    /*** IOpcSignatureReferenceSet methods ***/
    HRESULT (STDMETHODCALLTYPE *Create)(
        IOpcSignatureReferenceSet *This,
        IUri *referenceUri,
        LPCWSTR referenceId,
        LPCWSTR type,
        LPCWSTR digestMethod,
        OPC_CANONICALIZATION_METHOD transformMethod,
        IOpcSignatureReference **reference);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IOpcSignatureReferenceSet *This,
        IOpcSignatureReference *reference);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IOpcSignatureReferenceSet *This,
        IOpcSignatureReferenceEnumerator **referenceEnumerator);

    END_INTERFACE
} IOpcSignatureReferenceSetVtbl;

interface IOpcSignatureReferenceSet {
    CONST_VTBL IOpcSignatureReferenceSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignatureReferenceSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignatureReferenceSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignatureReferenceSet_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignatureReferenceSet methods ***/
#define IOpcSignatureReferenceSet_Create(This,referenceUri,referenceId,type,digestMethod,transformMethod,reference) (This)->lpVtbl->Create(This,referenceUri,referenceId,type,digestMethod,transformMethod,reference)
#define IOpcSignatureReferenceSet_Delete(This,reference) (This)->lpVtbl->Delete(This,reference)
#define IOpcSignatureReferenceSet_GetEnumerator(This,referenceEnumerator) (This)->lpVtbl->GetEnumerator(This,referenceEnumerator)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignatureReferenceSet_QueryInterface(IOpcSignatureReferenceSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignatureReferenceSet_AddRef(IOpcSignatureReferenceSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignatureReferenceSet_Release(IOpcSignatureReferenceSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignatureReferenceSet methods ***/
static inline HRESULT IOpcSignatureReferenceSet_Create(IOpcSignatureReferenceSet* This,IUri *referenceUri,LPCWSTR referenceId,LPCWSTR type,LPCWSTR digestMethod,OPC_CANONICALIZATION_METHOD transformMethod,IOpcSignatureReference **reference) {
    return This->lpVtbl->Create(This,referenceUri,referenceId,type,digestMethod,transformMethod,reference);
}
static inline HRESULT IOpcSignatureReferenceSet_Delete(IOpcSignatureReferenceSet* This,IOpcSignatureReference *reference) {
    return This->lpVtbl->Delete(This,reference);
}
static inline HRESULT IOpcSignatureReferenceSet_GetEnumerator(IOpcSignatureReferenceSet* This,IOpcSignatureReferenceEnumerator **referenceEnumerator) {
    return This->lpVtbl->GetEnumerator(This,referenceEnumerator);
}
#endif
#endif

#endif


#endif  /* __IOpcSignatureReferenceSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSignatureCustomObjectSet interface
 */
#ifndef __IOpcSignatureCustomObjectSet_INTERFACE_DEFINED__
#define __IOpcSignatureCustomObjectSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSignatureCustomObjectSet, 0x8f792ac5, 0x7947, 0x4e11, 0xbc,0x3d, 0x26,0x59,0xff,0x04,0x6a,0xe1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f792ac5-7947-4e11-bc3d-2659ff046ae1")
IOpcSignatureCustomObjectSet : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Create(
        const UINT8 *xmlMarkup,
        UINT32 count,
        IOpcSignatureCustomObject **customObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        IOpcSignatureCustomObject *customObject) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnumerator(
        IOpcSignatureCustomObjectEnumerator **customObjectEnumerator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSignatureCustomObjectSet, 0x8f792ac5, 0x7947, 0x4e11, 0xbc,0x3d, 0x26,0x59,0xff,0x04,0x6a,0xe1)
#endif
#else
typedef struct IOpcSignatureCustomObjectSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSignatureCustomObjectSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSignatureCustomObjectSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSignatureCustomObjectSet *This);

    /*** IOpcSignatureCustomObjectSet methods ***/
    HRESULT (STDMETHODCALLTYPE *Create)(
        IOpcSignatureCustomObjectSet *This,
        const UINT8 *xmlMarkup,
        UINT32 count,
        IOpcSignatureCustomObject **customObject);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IOpcSignatureCustomObjectSet *This,
        IOpcSignatureCustomObject *customObject);

    HRESULT (STDMETHODCALLTYPE *GetEnumerator)(
        IOpcSignatureCustomObjectSet *This,
        IOpcSignatureCustomObjectEnumerator **customObjectEnumerator);

    END_INTERFACE
} IOpcSignatureCustomObjectSetVtbl;

interface IOpcSignatureCustomObjectSet {
    CONST_VTBL IOpcSignatureCustomObjectSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSignatureCustomObjectSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSignatureCustomObjectSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSignatureCustomObjectSet_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSignatureCustomObjectSet methods ***/
#define IOpcSignatureCustomObjectSet_Create(This,xmlMarkup,count,customObject) (This)->lpVtbl->Create(This,xmlMarkup,count,customObject)
#define IOpcSignatureCustomObjectSet_Delete(This,customObject) (This)->lpVtbl->Delete(This,customObject)
#define IOpcSignatureCustomObjectSet_GetEnumerator(This,customObjectEnumerator) (This)->lpVtbl->GetEnumerator(This,customObjectEnumerator)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSignatureCustomObjectSet_QueryInterface(IOpcSignatureCustomObjectSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSignatureCustomObjectSet_AddRef(IOpcSignatureCustomObjectSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSignatureCustomObjectSet_Release(IOpcSignatureCustomObjectSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSignatureCustomObjectSet methods ***/
static inline HRESULT IOpcSignatureCustomObjectSet_Create(IOpcSignatureCustomObjectSet* This,const UINT8 *xmlMarkup,UINT32 count,IOpcSignatureCustomObject **customObject) {
    return This->lpVtbl->Create(This,xmlMarkup,count,customObject);
}
static inline HRESULT IOpcSignatureCustomObjectSet_Delete(IOpcSignatureCustomObjectSet* This,IOpcSignatureCustomObject *customObject) {
    return This->lpVtbl->Delete(This,customObject);
}
static inline HRESULT IOpcSignatureCustomObjectSet_GetEnumerator(IOpcSignatureCustomObjectSet* This,IOpcSignatureCustomObjectEnumerator **customObjectEnumerator) {
    return This->lpVtbl->GetEnumerator(This,customObjectEnumerator);
}
#endif
#endif

#endif


#endif  /* __IOpcSignatureCustomObjectSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcDigitalSignature interface
 */
#ifndef __IOpcDigitalSignature_INTERFACE_DEFINED__
#define __IOpcDigitalSignature_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcDigitalSignature, 0x52ab21dd, 0x1cd0, 0x4949, 0xbc,0x80, 0x0c,0x12,0x32,0xd0,0x0c,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("52ab21dd-1cd0-4949-bc80-0c1232d00cb4")
IOpcDigitalSignature : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetNamespaces(
        LPWSTR **prefixes,
        LPWSTR **namespaces,
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureId(
        LPWSTR *signatureId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignaturePartName(
        IOpcPartUri **signaturePartName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureMethod(
        LPWSTR *signatureMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCanonicalizationMethod(
        OPC_CANONICALIZATION_METHOD *canonicalizationMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureValue(
        UINT8 **signatureValue,
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignaturePartReferenceEnumerator(
        IOpcSignaturePartReferenceEnumerator **partReferenceEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureRelationshipReferenceEnumerator(
        IOpcSignatureRelationshipReferenceEnumerator **relationshipReferenceEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSigningTime(
        LPWSTR *signingTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimeFormat(
        OPC_SIGNATURE_TIME_FORMAT *timeFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPackageObjectReference(
        IOpcSignatureReference **packageObjectReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCertificateEnumerator(
        IOpcCertificateEnumerator **certificateEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCustomReferenceEnumerator(
        IOpcSignatureReferenceEnumerator **customReferenceEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCustomObjectEnumerator(
        IOpcSignatureCustomObjectEnumerator **customObjectEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureXml(
        UINT8 **signatureXml,
        UINT32 *count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcDigitalSignature, 0x52ab21dd, 0x1cd0, 0x4949, 0xbc,0x80, 0x0c,0x12,0x32,0xd0,0x0c,0xb4)
#endif
#else
typedef struct IOpcDigitalSignatureVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcDigitalSignature *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcDigitalSignature *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcDigitalSignature *This);

    /*** IOpcDigitalSignature methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNamespaces)(
        IOpcDigitalSignature *This,
        LPWSTR **prefixes,
        LPWSTR **namespaces,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetSignatureId)(
        IOpcDigitalSignature *This,
        LPWSTR *signatureId);

    HRESULT (STDMETHODCALLTYPE *GetSignaturePartName)(
        IOpcDigitalSignature *This,
        IOpcPartUri **signaturePartName);

    HRESULT (STDMETHODCALLTYPE *GetSignatureMethod)(
        IOpcDigitalSignature *This,
        LPWSTR *signatureMethod);

    HRESULT (STDMETHODCALLTYPE *GetCanonicalizationMethod)(
        IOpcDigitalSignature *This,
        OPC_CANONICALIZATION_METHOD *canonicalizationMethod);

    HRESULT (STDMETHODCALLTYPE *GetSignatureValue)(
        IOpcDigitalSignature *This,
        UINT8 **signatureValue,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetSignaturePartReferenceEnumerator)(
        IOpcDigitalSignature *This,
        IOpcSignaturePartReferenceEnumerator **partReferenceEnumerator);

    HRESULT (STDMETHODCALLTYPE *GetSignatureRelationshipReferenceEnumerator)(
        IOpcDigitalSignature *This,
        IOpcSignatureRelationshipReferenceEnumerator **relationshipReferenceEnumerator);

    HRESULT (STDMETHODCALLTYPE *GetSigningTime)(
        IOpcDigitalSignature *This,
        LPWSTR *signingTime);

    HRESULT (STDMETHODCALLTYPE *GetTimeFormat)(
        IOpcDigitalSignature *This,
        OPC_SIGNATURE_TIME_FORMAT *timeFormat);

    HRESULT (STDMETHODCALLTYPE *GetPackageObjectReference)(
        IOpcDigitalSignature *This,
        IOpcSignatureReference **packageObjectReference);

    HRESULT (STDMETHODCALLTYPE *GetCertificateEnumerator)(
        IOpcDigitalSignature *This,
        IOpcCertificateEnumerator **certificateEnumerator);

    HRESULT (STDMETHODCALLTYPE *GetCustomReferenceEnumerator)(
        IOpcDigitalSignature *This,
        IOpcSignatureReferenceEnumerator **customReferenceEnumerator);

    HRESULT (STDMETHODCALLTYPE *GetCustomObjectEnumerator)(
        IOpcDigitalSignature *This,
        IOpcSignatureCustomObjectEnumerator **customObjectEnumerator);

    HRESULT (STDMETHODCALLTYPE *GetSignatureXml)(
        IOpcDigitalSignature *This,
        UINT8 **signatureXml,
        UINT32 *count);

    END_INTERFACE
} IOpcDigitalSignatureVtbl;

interface IOpcDigitalSignature {
    CONST_VTBL IOpcDigitalSignatureVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcDigitalSignature_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcDigitalSignature_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcDigitalSignature_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcDigitalSignature methods ***/
#define IOpcDigitalSignature_GetNamespaces(This,prefixes,namespaces,count) (This)->lpVtbl->GetNamespaces(This,prefixes,namespaces,count)
#define IOpcDigitalSignature_GetSignatureId(This,signatureId) (This)->lpVtbl->GetSignatureId(This,signatureId)
#define IOpcDigitalSignature_GetSignaturePartName(This,signaturePartName) (This)->lpVtbl->GetSignaturePartName(This,signaturePartName)
#define IOpcDigitalSignature_GetSignatureMethod(This,signatureMethod) (This)->lpVtbl->GetSignatureMethod(This,signatureMethod)
#define IOpcDigitalSignature_GetCanonicalizationMethod(This,canonicalizationMethod) (This)->lpVtbl->GetCanonicalizationMethod(This,canonicalizationMethod)
#define IOpcDigitalSignature_GetSignatureValue(This,signatureValue,count) (This)->lpVtbl->GetSignatureValue(This,signatureValue,count)
#define IOpcDigitalSignature_GetSignaturePartReferenceEnumerator(This,partReferenceEnumerator) (This)->lpVtbl->GetSignaturePartReferenceEnumerator(This,partReferenceEnumerator)
#define IOpcDigitalSignature_GetSignatureRelationshipReferenceEnumerator(This,relationshipReferenceEnumerator) (This)->lpVtbl->GetSignatureRelationshipReferenceEnumerator(This,relationshipReferenceEnumerator)
#define IOpcDigitalSignature_GetSigningTime(This,signingTime) (This)->lpVtbl->GetSigningTime(This,signingTime)
#define IOpcDigitalSignature_GetTimeFormat(This,timeFormat) (This)->lpVtbl->GetTimeFormat(This,timeFormat)
#define IOpcDigitalSignature_GetPackageObjectReference(This,packageObjectReference) (This)->lpVtbl->GetPackageObjectReference(This,packageObjectReference)
#define IOpcDigitalSignature_GetCertificateEnumerator(This,certificateEnumerator) (This)->lpVtbl->GetCertificateEnumerator(This,certificateEnumerator)
#define IOpcDigitalSignature_GetCustomReferenceEnumerator(This,customReferenceEnumerator) (This)->lpVtbl->GetCustomReferenceEnumerator(This,customReferenceEnumerator)
#define IOpcDigitalSignature_GetCustomObjectEnumerator(This,customObjectEnumerator) (This)->lpVtbl->GetCustomObjectEnumerator(This,customObjectEnumerator)
#define IOpcDigitalSignature_GetSignatureXml(This,signatureXml,count) (This)->lpVtbl->GetSignatureXml(This,signatureXml,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcDigitalSignature_QueryInterface(IOpcDigitalSignature* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcDigitalSignature_AddRef(IOpcDigitalSignature* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcDigitalSignature_Release(IOpcDigitalSignature* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcDigitalSignature methods ***/
static inline HRESULT IOpcDigitalSignature_GetNamespaces(IOpcDigitalSignature* This,LPWSTR **prefixes,LPWSTR **namespaces,UINT32 *count) {
    return This->lpVtbl->GetNamespaces(This,prefixes,namespaces,count);
}
static inline HRESULT IOpcDigitalSignature_GetSignatureId(IOpcDigitalSignature* This,LPWSTR *signatureId) {
    return This->lpVtbl->GetSignatureId(This,signatureId);
}
static inline HRESULT IOpcDigitalSignature_GetSignaturePartName(IOpcDigitalSignature* This,IOpcPartUri **signaturePartName) {
    return This->lpVtbl->GetSignaturePartName(This,signaturePartName);
}
static inline HRESULT IOpcDigitalSignature_GetSignatureMethod(IOpcDigitalSignature* This,LPWSTR *signatureMethod) {
    return This->lpVtbl->GetSignatureMethod(This,signatureMethod);
}
static inline HRESULT IOpcDigitalSignature_GetCanonicalizationMethod(IOpcDigitalSignature* This,OPC_CANONICALIZATION_METHOD *canonicalizationMethod) {
    return This->lpVtbl->GetCanonicalizationMethod(This,canonicalizationMethod);
}
static inline HRESULT IOpcDigitalSignature_GetSignatureValue(IOpcDigitalSignature* This,UINT8 **signatureValue,UINT32 *count) {
    return This->lpVtbl->GetSignatureValue(This,signatureValue,count);
}
static inline HRESULT IOpcDigitalSignature_GetSignaturePartReferenceEnumerator(IOpcDigitalSignature* This,IOpcSignaturePartReferenceEnumerator **partReferenceEnumerator) {
    return This->lpVtbl->GetSignaturePartReferenceEnumerator(This,partReferenceEnumerator);
}
static inline HRESULT IOpcDigitalSignature_GetSignatureRelationshipReferenceEnumerator(IOpcDigitalSignature* This,IOpcSignatureRelationshipReferenceEnumerator **relationshipReferenceEnumerator) {
    return This->lpVtbl->GetSignatureRelationshipReferenceEnumerator(This,relationshipReferenceEnumerator);
}
static inline HRESULT IOpcDigitalSignature_GetSigningTime(IOpcDigitalSignature* This,LPWSTR *signingTime) {
    return This->lpVtbl->GetSigningTime(This,signingTime);
}
static inline HRESULT IOpcDigitalSignature_GetTimeFormat(IOpcDigitalSignature* This,OPC_SIGNATURE_TIME_FORMAT *timeFormat) {
    return This->lpVtbl->GetTimeFormat(This,timeFormat);
}
static inline HRESULT IOpcDigitalSignature_GetPackageObjectReference(IOpcDigitalSignature* This,IOpcSignatureReference **packageObjectReference) {
    return This->lpVtbl->GetPackageObjectReference(This,packageObjectReference);
}
static inline HRESULT IOpcDigitalSignature_GetCertificateEnumerator(IOpcDigitalSignature* This,IOpcCertificateEnumerator **certificateEnumerator) {
    return This->lpVtbl->GetCertificateEnumerator(This,certificateEnumerator);
}
static inline HRESULT IOpcDigitalSignature_GetCustomReferenceEnumerator(IOpcDigitalSignature* This,IOpcSignatureReferenceEnumerator **customReferenceEnumerator) {
    return This->lpVtbl->GetCustomReferenceEnumerator(This,customReferenceEnumerator);
}
static inline HRESULT IOpcDigitalSignature_GetCustomObjectEnumerator(IOpcDigitalSignature* This,IOpcSignatureCustomObjectEnumerator **customObjectEnumerator) {
    return This->lpVtbl->GetCustomObjectEnumerator(This,customObjectEnumerator);
}
static inline HRESULT IOpcDigitalSignature_GetSignatureXml(IOpcDigitalSignature* This,UINT8 **signatureXml,UINT32 *count) {
    return This->lpVtbl->GetSignatureXml(This,signatureXml,count);
}
#endif
#endif

#endif


#endif  /* __IOpcDigitalSignature_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcSigningOptions interface
 */
#ifndef __IOpcSigningOptions_INTERFACE_DEFINED__
#define __IOpcSigningOptions_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcSigningOptions, 0x50d2d6a5, 0x7aeb, 0x46c0, 0xb2,0x41, 0x43,0xab,0x0e,0x9b,0x40,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("50d2d6a5-7aeb-46c0-b241-43ab0e9b407e")
IOpcSigningOptions : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSignatureId(
        LPWSTR *signatureId) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignatureId(
        LPCWSTR signatureId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureMethod(
        LPWSTR *signatureMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignatureMethod(
        LPCWSTR signatureMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultDigestMethod(
        LPWSTR *digestMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultDigestMethod(
        LPCWSTR digestMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCertificateEmbeddingOption(
        OPC_CERTIFICATE_EMBEDDING_OPTION *embeddingOption) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCertificateEmbeddingOption(
        OPC_CERTIFICATE_EMBEDDING_OPTION embeddingOption) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimeFormat(
        OPC_SIGNATURE_TIME_FORMAT *timeFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTimeFormat(
        OPC_SIGNATURE_TIME_FORMAT timeFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignaturePartReferenceSet(
        IOpcSignaturePartReferenceSet **partReferenceSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureRelationshipReferenceSet(
        IOpcSignatureRelationshipReferenceSet **relationshipReferenceSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCustomObjectSet(
        IOpcSignatureCustomObjectSet **customObjectSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCustomReferenceSet(
        IOpcSignatureReferenceSet **customReferenceSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCertificateSet(
        IOpcCertificateSet **certificateSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignaturePartName(
        IOpcPartUri **signaturePartName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignaturePartName(
        IOpcPartUri *signaturePartName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcSigningOptions, 0x50d2d6a5, 0x7aeb, 0x46c0, 0xb2,0x41, 0x43,0xab,0x0e,0x9b,0x40,0x7e)
#endif
#else
typedef struct IOpcSigningOptionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcSigningOptions *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcSigningOptions *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcSigningOptions *This);

    /*** IOpcSigningOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSignatureId)(
        IOpcSigningOptions *This,
        LPWSTR *signatureId);

    HRESULT (STDMETHODCALLTYPE *SetSignatureId)(
        IOpcSigningOptions *This,
        LPCWSTR signatureId);

    HRESULT (STDMETHODCALLTYPE *GetSignatureMethod)(
        IOpcSigningOptions *This,
        LPWSTR *signatureMethod);

    HRESULT (STDMETHODCALLTYPE *SetSignatureMethod)(
        IOpcSigningOptions *This,
        LPCWSTR signatureMethod);

    HRESULT (STDMETHODCALLTYPE *GetDefaultDigestMethod)(
        IOpcSigningOptions *This,
        LPWSTR *digestMethod);

    HRESULT (STDMETHODCALLTYPE *SetDefaultDigestMethod)(
        IOpcSigningOptions *This,
        LPCWSTR digestMethod);

    HRESULT (STDMETHODCALLTYPE *GetCertificateEmbeddingOption)(
        IOpcSigningOptions *This,
        OPC_CERTIFICATE_EMBEDDING_OPTION *embeddingOption);

    HRESULT (STDMETHODCALLTYPE *SetCertificateEmbeddingOption)(
        IOpcSigningOptions *This,
        OPC_CERTIFICATE_EMBEDDING_OPTION embeddingOption);

    HRESULT (STDMETHODCALLTYPE *GetTimeFormat)(
        IOpcSigningOptions *This,
        OPC_SIGNATURE_TIME_FORMAT *timeFormat);

    HRESULT (STDMETHODCALLTYPE *SetTimeFormat)(
        IOpcSigningOptions *This,
        OPC_SIGNATURE_TIME_FORMAT timeFormat);

    HRESULT (STDMETHODCALLTYPE *GetSignaturePartReferenceSet)(
        IOpcSigningOptions *This,
        IOpcSignaturePartReferenceSet **partReferenceSet);

    HRESULT (STDMETHODCALLTYPE *GetSignatureRelationshipReferenceSet)(
        IOpcSigningOptions *This,
        IOpcSignatureRelationshipReferenceSet **relationshipReferenceSet);

    HRESULT (STDMETHODCALLTYPE *GetCustomObjectSet)(
        IOpcSigningOptions *This,
        IOpcSignatureCustomObjectSet **customObjectSet);

    HRESULT (STDMETHODCALLTYPE *GetCustomReferenceSet)(
        IOpcSigningOptions *This,
        IOpcSignatureReferenceSet **customReferenceSet);

    HRESULT (STDMETHODCALLTYPE *GetCertificateSet)(
        IOpcSigningOptions *This,
        IOpcCertificateSet **certificateSet);

    HRESULT (STDMETHODCALLTYPE *GetSignaturePartName)(
        IOpcSigningOptions *This,
        IOpcPartUri **signaturePartName);

    HRESULT (STDMETHODCALLTYPE *SetSignaturePartName)(
        IOpcSigningOptions *This,
        IOpcPartUri *signaturePartName);

    END_INTERFACE
} IOpcSigningOptionsVtbl;

interface IOpcSigningOptions {
    CONST_VTBL IOpcSigningOptionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcSigningOptions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcSigningOptions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcSigningOptions_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcSigningOptions methods ***/
#define IOpcSigningOptions_GetSignatureId(This,signatureId) (This)->lpVtbl->GetSignatureId(This,signatureId)
#define IOpcSigningOptions_SetSignatureId(This,signatureId) (This)->lpVtbl->SetSignatureId(This,signatureId)
#define IOpcSigningOptions_GetSignatureMethod(This,signatureMethod) (This)->lpVtbl->GetSignatureMethod(This,signatureMethod)
#define IOpcSigningOptions_SetSignatureMethod(This,signatureMethod) (This)->lpVtbl->SetSignatureMethod(This,signatureMethod)
#define IOpcSigningOptions_GetDefaultDigestMethod(This,digestMethod) (This)->lpVtbl->GetDefaultDigestMethod(This,digestMethod)
#define IOpcSigningOptions_SetDefaultDigestMethod(This,digestMethod) (This)->lpVtbl->SetDefaultDigestMethod(This,digestMethod)
#define IOpcSigningOptions_GetCertificateEmbeddingOption(This,embeddingOption) (This)->lpVtbl->GetCertificateEmbeddingOption(This,embeddingOption)
#define IOpcSigningOptions_SetCertificateEmbeddingOption(This,embeddingOption) (This)->lpVtbl->SetCertificateEmbeddingOption(This,embeddingOption)
#define IOpcSigningOptions_GetTimeFormat(This,timeFormat) (This)->lpVtbl->GetTimeFormat(This,timeFormat)
#define IOpcSigningOptions_SetTimeFormat(This,timeFormat) (This)->lpVtbl->SetTimeFormat(This,timeFormat)
#define IOpcSigningOptions_GetSignaturePartReferenceSet(This,partReferenceSet) (This)->lpVtbl->GetSignaturePartReferenceSet(This,partReferenceSet)
#define IOpcSigningOptions_GetSignatureRelationshipReferenceSet(This,relationshipReferenceSet) (This)->lpVtbl->GetSignatureRelationshipReferenceSet(This,relationshipReferenceSet)
#define IOpcSigningOptions_GetCustomObjectSet(This,customObjectSet) (This)->lpVtbl->GetCustomObjectSet(This,customObjectSet)
#define IOpcSigningOptions_GetCustomReferenceSet(This,customReferenceSet) (This)->lpVtbl->GetCustomReferenceSet(This,customReferenceSet)
#define IOpcSigningOptions_GetCertificateSet(This,certificateSet) (This)->lpVtbl->GetCertificateSet(This,certificateSet)
#define IOpcSigningOptions_GetSignaturePartName(This,signaturePartName) (This)->lpVtbl->GetSignaturePartName(This,signaturePartName)
#define IOpcSigningOptions_SetSignaturePartName(This,signaturePartName) (This)->lpVtbl->SetSignaturePartName(This,signaturePartName)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcSigningOptions_QueryInterface(IOpcSigningOptions* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcSigningOptions_AddRef(IOpcSigningOptions* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcSigningOptions_Release(IOpcSigningOptions* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcSigningOptions methods ***/
static inline HRESULT IOpcSigningOptions_GetSignatureId(IOpcSigningOptions* This,LPWSTR *signatureId) {
    return This->lpVtbl->GetSignatureId(This,signatureId);
}
static inline HRESULT IOpcSigningOptions_SetSignatureId(IOpcSigningOptions* This,LPCWSTR signatureId) {
    return This->lpVtbl->SetSignatureId(This,signatureId);
}
static inline HRESULT IOpcSigningOptions_GetSignatureMethod(IOpcSigningOptions* This,LPWSTR *signatureMethod) {
    return This->lpVtbl->GetSignatureMethod(This,signatureMethod);
}
static inline HRESULT IOpcSigningOptions_SetSignatureMethod(IOpcSigningOptions* This,LPCWSTR signatureMethod) {
    return This->lpVtbl->SetSignatureMethod(This,signatureMethod);
}
static inline HRESULT IOpcSigningOptions_GetDefaultDigestMethod(IOpcSigningOptions* This,LPWSTR *digestMethod) {
    return This->lpVtbl->GetDefaultDigestMethod(This,digestMethod);
}
static inline HRESULT IOpcSigningOptions_SetDefaultDigestMethod(IOpcSigningOptions* This,LPCWSTR digestMethod) {
    return This->lpVtbl->SetDefaultDigestMethod(This,digestMethod);
}
static inline HRESULT IOpcSigningOptions_GetCertificateEmbeddingOption(IOpcSigningOptions* This,OPC_CERTIFICATE_EMBEDDING_OPTION *embeddingOption) {
    return This->lpVtbl->GetCertificateEmbeddingOption(This,embeddingOption);
}
static inline HRESULT IOpcSigningOptions_SetCertificateEmbeddingOption(IOpcSigningOptions* This,OPC_CERTIFICATE_EMBEDDING_OPTION embeddingOption) {
    return This->lpVtbl->SetCertificateEmbeddingOption(This,embeddingOption);
}
static inline HRESULT IOpcSigningOptions_GetTimeFormat(IOpcSigningOptions* This,OPC_SIGNATURE_TIME_FORMAT *timeFormat) {
    return This->lpVtbl->GetTimeFormat(This,timeFormat);
}
static inline HRESULT IOpcSigningOptions_SetTimeFormat(IOpcSigningOptions* This,OPC_SIGNATURE_TIME_FORMAT timeFormat) {
    return This->lpVtbl->SetTimeFormat(This,timeFormat);
}
static inline HRESULT IOpcSigningOptions_GetSignaturePartReferenceSet(IOpcSigningOptions* This,IOpcSignaturePartReferenceSet **partReferenceSet) {
    return This->lpVtbl->GetSignaturePartReferenceSet(This,partReferenceSet);
}
static inline HRESULT IOpcSigningOptions_GetSignatureRelationshipReferenceSet(IOpcSigningOptions* This,IOpcSignatureRelationshipReferenceSet **relationshipReferenceSet) {
    return This->lpVtbl->GetSignatureRelationshipReferenceSet(This,relationshipReferenceSet);
}
static inline HRESULT IOpcSigningOptions_GetCustomObjectSet(IOpcSigningOptions* This,IOpcSignatureCustomObjectSet **customObjectSet) {
    return This->lpVtbl->GetCustomObjectSet(This,customObjectSet);
}
static inline HRESULT IOpcSigningOptions_GetCustomReferenceSet(IOpcSigningOptions* This,IOpcSignatureReferenceSet **customReferenceSet) {
    return This->lpVtbl->GetCustomReferenceSet(This,customReferenceSet);
}
static inline HRESULT IOpcSigningOptions_GetCertificateSet(IOpcSigningOptions* This,IOpcCertificateSet **certificateSet) {
    return This->lpVtbl->GetCertificateSet(This,certificateSet);
}
static inline HRESULT IOpcSigningOptions_GetSignaturePartName(IOpcSigningOptions* This,IOpcPartUri **signaturePartName) {
    return This->lpVtbl->GetSignaturePartName(This,signaturePartName);
}
static inline HRESULT IOpcSigningOptions_SetSignaturePartName(IOpcSigningOptions* This,IOpcPartUri *signaturePartName) {
    return This->lpVtbl->SetSignaturePartName(This,signaturePartName);
}
#endif
#endif

#endif


#endif  /* __IOpcSigningOptions_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IOpcFactory interface
 */
#ifndef __IOpcFactory_INTERFACE_DEFINED__
#define __IOpcFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IOpcFactory, 0x6d0b4446, 0xcd73, 0x4ab3, 0x94,0xf4, 0x8c,0xcd,0xf6,0x11,0x61,0x54);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6d0b4446-cd73-4ab3-94f4-8ccdf6116154")
IOpcFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreatePackageRootUri(
        IOpcUri **rootUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePartUri(
        LPCWSTR pwzUri,
        IOpcPartUri **partUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateStreamOnFile(
        LPCWSTR filename,
        OPC_STREAM_IO_MODE ioMode,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD dwFlagsAndAttributes,
        IStream **stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePackage(
        IOpcPackage **package) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReadPackageFromStream(
        IStream *stream,
        OPC_READ_FLAGS flags,
        IOpcPackage **package) = 0;

    virtual HRESULT STDMETHODCALLTYPE WritePackageToStream(
        IOpcPackage *package,
        OPC_WRITE_FLAGS flags,
        IStream *stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDigitalSignatureManager(
        IOpcPackage *package,
        IOpcDigitalSignatureManager **signatureManager) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IOpcFactory, 0x6d0b4446, 0xcd73, 0x4ab3, 0x94,0xf4, 0x8c,0xcd,0xf6,0x11,0x61,0x54)
#endif
#else
typedef struct IOpcFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IOpcFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IOpcFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IOpcFactory *This);

    /*** IOpcFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreatePackageRootUri)(
        IOpcFactory *This,
        IOpcUri **rootUri);

    HRESULT (STDMETHODCALLTYPE *CreatePartUri)(
        IOpcFactory *This,
        LPCWSTR pwzUri,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *CreateStreamOnFile)(
        IOpcFactory *This,
        LPCWSTR filename,
        OPC_STREAM_IO_MODE ioMode,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD dwFlagsAndAttributes,
        IStream **stream);

    HRESULT (STDMETHODCALLTYPE *CreatePackage)(
        IOpcFactory *This,
        IOpcPackage **package);

    HRESULT (STDMETHODCALLTYPE *ReadPackageFromStream)(
        IOpcFactory *This,
        IStream *stream,
        OPC_READ_FLAGS flags,
        IOpcPackage **package);

    HRESULT (STDMETHODCALLTYPE *WritePackageToStream)(
        IOpcFactory *This,
        IOpcPackage *package,
        OPC_WRITE_FLAGS flags,
        IStream *stream);

    HRESULT (STDMETHODCALLTYPE *CreateDigitalSignatureManager)(
        IOpcFactory *This,
        IOpcPackage *package,
        IOpcDigitalSignatureManager **signatureManager);

    END_INTERFACE
} IOpcFactoryVtbl;

interface IOpcFactory {
    CONST_VTBL IOpcFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IOpcFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IOpcFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IOpcFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IOpcFactory methods ***/
#define IOpcFactory_CreatePackageRootUri(This,rootUri) (This)->lpVtbl->CreatePackageRootUri(This,rootUri)
#define IOpcFactory_CreatePartUri(This,pwzUri,partUri) (This)->lpVtbl->CreatePartUri(This,pwzUri,partUri)
#define IOpcFactory_CreateStreamOnFile(This,filename,ioMode,securityAttributes,dwFlagsAndAttributes,stream) (This)->lpVtbl->CreateStreamOnFile(This,filename,ioMode,securityAttributes,dwFlagsAndAttributes,stream)
#define IOpcFactory_CreatePackage(This,package) (This)->lpVtbl->CreatePackage(This,package)
#define IOpcFactory_ReadPackageFromStream(This,stream,flags,package) (This)->lpVtbl->ReadPackageFromStream(This,stream,flags,package)
#define IOpcFactory_WritePackageToStream(This,package,flags,stream) (This)->lpVtbl->WritePackageToStream(This,package,flags,stream)
#define IOpcFactory_CreateDigitalSignatureManager(This,package,signatureManager) (This)->lpVtbl->CreateDigitalSignatureManager(This,package,signatureManager)
#else
/*** IUnknown methods ***/
static inline HRESULT IOpcFactory_QueryInterface(IOpcFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IOpcFactory_AddRef(IOpcFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IOpcFactory_Release(IOpcFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IOpcFactory methods ***/
static inline HRESULT IOpcFactory_CreatePackageRootUri(IOpcFactory* This,IOpcUri **rootUri) {
    return This->lpVtbl->CreatePackageRootUri(This,rootUri);
}
static inline HRESULT IOpcFactory_CreatePartUri(IOpcFactory* This,LPCWSTR pwzUri,IOpcPartUri **partUri) {
    return This->lpVtbl->CreatePartUri(This,pwzUri,partUri);
}
static inline HRESULT IOpcFactory_CreateStreamOnFile(IOpcFactory* This,LPCWSTR filename,OPC_STREAM_IO_MODE ioMode,LPSECURITY_ATTRIBUTES securityAttributes,DWORD dwFlagsAndAttributes,IStream **stream) {
    return This->lpVtbl->CreateStreamOnFile(This,filename,ioMode,securityAttributes,dwFlagsAndAttributes,stream);
}
static inline HRESULT IOpcFactory_CreatePackage(IOpcFactory* This,IOpcPackage **package) {
    return This->lpVtbl->CreatePackage(This,package);
}
static inline HRESULT IOpcFactory_ReadPackageFromStream(IOpcFactory* This,IStream *stream,OPC_READ_FLAGS flags,IOpcPackage **package) {
    return This->lpVtbl->ReadPackageFromStream(This,stream,flags,package);
}
static inline HRESULT IOpcFactory_WritePackageToStream(IOpcFactory* This,IOpcPackage *package,OPC_WRITE_FLAGS flags,IStream *stream) {
    return This->lpVtbl->WritePackageToStream(This,package,flags,stream);
}
static inline HRESULT IOpcFactory_CreateDigitalSignatureManager(IOpcFactory* This,IOpcPackage *package,IOpcDigitalSignatureManager **signatureManager) {
    return This->lpVtbl->CreateDigitalSignatureManager(This,package,signatureManager);
}
#endif
#endif

#endif


#endif  /* __IOpcFactory_INTERFACE_DEFINED__ */

/*****************************************************************************
 * OpcFactory coclass
 */

DEFINE_GUID(CLSID_OpcFactory, 0x6b2d6ba0, 0x9f3e, 0x4f27, 0x92,0x0b, 0x31,0x3c,0xc4,0x26,0xa3,0x9e);

#ifdef __cplusplus
class DECLSPEC_UUID("6b2d6ba0-9f3e-4f27-920b-313cc426a39e") OpcFactory;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(OpcFactory, 0x6b2d6ba0, 0x9f3e, 0x4f27, 0x92,0x0b, 0x31,0x3c,0xc4,0x26,0xa3,0x9e)
#endif
#endif

#endif /* __MSOPC_LIBRARY_DEFINED__ */
#endif
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __msopc_h__ */
