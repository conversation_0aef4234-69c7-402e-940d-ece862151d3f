/*** Autogenerated by WIDL 10.12 from include/windows.security.credentials.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_security_credentials_h__
#define __windows_security_credentials_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics ABI::Windows::Security::Credentials::IKeyCredentialManagerStatics
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                interface IKeyCredentialManagerStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult ABI::Windows::Security::Credentials::IKeyCredentialRetrievalResult
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                interface IKeyCredentialRetrievalResult;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredential_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredential_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                class KeyCredential;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredential __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredential;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredential_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialManager_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                class KeyCredentialManager;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialManager __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialManager;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialManager_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialRetrievalResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialRetrievalResult_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                class KeyCredentialRetrievalResult;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialRetrievalResult __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialRetrievalResult;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialRetrievalResult_FWD_DEFINED__ */

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.storage.streams.h>
#include <windows.system.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialCreationOption_ENUM_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialCreationOption_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                enum KeyCredentialCreationOption {
                    KeyCredentialCreationOption_ReplaceExisting = 0,
                    KeyCredentialCreationOption_FailIfExists = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialCreationOption {
    KeyCredentialCreationOption_ReplaceExisting = 0,
    KeyCredentialCreationOption_FailIfExists = 1
};
#ifdef WIDL_using_Windows_Security_Credentials
#define KeyCredentialCreationOption __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialCreationOption
#endif /* WIDL_using_Windows_Security_Credentials */
#endif

#endif /* ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialCreationOption_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialCreationOption __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialCreationOption;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialStatus_ENUM_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialStatus_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                enum KeyCredentialStatus {
                    KeyCredentialStatus_Success = 0,
                    KeyCredentialStatus_UnknownError = 1,
                    KeyCredentialStatus_NotFound = 2,
                    KeyCredentialStatus_UserCanceled = 3,
                    KeyCredentialStatus_UserPrefersPassword = 4,
                    KeyCredentialStatus_CredentialAlreadyExists = 5,
                    KeyCredentialStatus_SecurityDeviceLocked = 6
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialStatus {
    KeyCredentialStatus_Success = 0,
    KeyCredentialStatus_UnknownError = 1,
    KeyCredentialStatus_NotFound = 2,
    KeyCredentialStatus_UserCanceled = 3,
    KeyCredentialStatus_UserPrefersPassword = 4,
    KeyCredentialStatus_CredentialAlreadyExists = 5,
    KeyCredentialStatus_SecurityDeviceLocked = 6
};
#ifdef WIDL_using_Windows_Security_Credentials
#define KeyCredentialStatus __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialStatus
#endif /* WIDL_using_Windows_Security_Credentials */
#endif

#endif /* ____x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialStatus_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialStatus __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialStatus;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredential_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredential_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredential __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredential;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredential ABI::Windows::Security::Credentials::IKeyCredential
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                interface IKeyCredential;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics ABI::Windows::Security::Credentials::IKeyCredentialManagerStatics
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                interface IKeyCredentialManagerStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult;
#ifdef __cplusplus
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult ABI::Windows::Security::Credentials::IKeyCredentialRetrievalResult
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                interface IKeyCredentialRetrievalResult;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IKeyCredentialManagerStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics, 0x6aac468b, 0x0ef1, 0x4ce0, 0x82,0x90, 0x41,0x06,0xda,0x6a,0x63,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                MIDL_INTERFACE("6aac468b-0ef1-4ce0-8290-4106da6a63b5")
                IKeyCredentialManagerStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE IsSupportedAsync(
                        ABI::Windows::Foundation::IAsyncOperation<boolean > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RenewAttestationAsync(
                        ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RequestCreateAsync(
                        HSTRING name,
                        ABI::Windows::Security::Credentials::KeyCredentialCreationOption option,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE OpenAsync(
                        HSTRING name,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE DeleteAsync(
                        HSTRING name,
                        ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics, 0x6aac468b, 0x0ef1, 0x4ce0, 0x82,0x90, 0x41,0x06,0xda,0x6a,0x63,0xb5)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics *This,
        TrustLevel *trustLevel);

    /*** IKeyCredentialManagerStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *IsSupportedAsync)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics *This,
        __FIAsyncOperation_1_boolean **value);

    HRESULT (STDMETHODCALLTYPE *RenewAttestationAsync)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics *This,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *RequestCreateAsync)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics *This,
        HSTRING name,
        __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialCreationOption option,
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult **value);

    HRESULT (STDMETHODCALLTYPE *OpenAsync)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics *This,
        HSTRING name,
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult **value);

    HRESULT (STDMETHODCALLTYPE *DeleteAsync)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics *This,
        HSTRING name,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStaticsVtbl;

interface __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKeyCredentialManagerStatics methods ***/
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_IsSupportedAsync(This,value) (This)->lpVtbl->IsSupportedAsync(This,value)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_RenewAttestationAsync(This,operation) (This)->lpVtbl->RenewAttestationAsync(This,operation)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_RequestCreateAsync(This,name,option,value) (This)->lpVtbl->RequestCreateAsync(This,name,option,value)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_OpenAsync(This,name,value) (This)->lpVtbl->OpenAsync(This,name,value)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_DeleteAsync(This,name,operation) (This)->lpVtbl->DeleteAsync(This,name,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_QueryInterface(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_AddRef(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_Release(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_GetIids(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_GetTrustLevel(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKeyCredentialManagerStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_IsSupportedAsync(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics* This,__FIAsyncOperation_1_boolean **value) {
    return This->lpVtbl->IsSupportedAsync(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_RenewAttestationAsync(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics* This,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->RenewAttestationAsync(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_RequestCreateAsync(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics* This,HSTRING name,__x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialCreationOption option,__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult **value) {
    return This->lpVtbl->RequestCreateAsync(This,name,option,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_OpenAsync(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics* This,HSTRING name,__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult **value) {
    return This->lpVtbl->OpenAsync(This,name,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_DeleteAsync(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics* This,HSTRING name,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->DeleteAsync(This,name,operation);
}
#endif
#ifdef WIDL_using_Windows_Security_Credentials
#define IID_IKeyCredentialManagerStatics IID___x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics
#define IKeyCredentialManagerStaticsVtbl __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStaticsVtbl
#define IKeyCredentialManagerStatics __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics
#define IKeyCredentialManagerStatics_QueryInterface __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_QueryInterface
#define IKeyCredentialManagerStatics_AddRef __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_AddRef
#define IKeyCredentialManagerStatics_Release __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_Release
#define IKeyCredentialManagerStatics_GetIids __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_GetIids
#define IKeyCredentialManagerStatics_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_GetRuntimeClassName
#define IKeyCredentialManagerStatics_GetTrustLevel __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_GetTrustLevel
#define IKeyCredentialManagerStatics_IsSupportedAsync __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_IsSupportedAsync
#define IKeyCredentialManagerStatics_RenewAttestationAsync __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_RenewAttestationAsync
#define IKeyCredentialManagerStatics_RequestCreateAsync __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_RequestCreateAsync
#define IKeyCredentialManagerStatics_OpenAsync __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_OpenAsync
#define IKeyCredentialManagerStatics_DeleteAsync __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_DeleteAsync
#endif /* WIDL_using_Windows_Security_Credentials */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialManagerStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IKeyCredentialRetrievalResult interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult, 0x58cd7703, 0x8d87, 0x4249, 0x9b,0x58, 0xf6,0x59,0x8c,0xc9,0x64,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Security {
            namespace Credentials {
                MIDL_INTERFACE("58cd7703-8d87-4249-9b58-f6598cc9644e")
                IKeyCredentialRetrievalResult : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Credential(
                        ABI::Windows::Security::Credentials::IKeyCredential **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Status(
                        ABI::Windows::Security::Credentials::KeyCredentialStatus *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult, 0x58cd7703, 0x8d87, 0x4249, 0x9b,0x58, 0xf6,0x59,0x8c,0xc9,0x64,0x4e)
#endif
#else
typedef struct __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult *This,
        TrustLevel *trustLevel);

    /*** IKeyCredentialRetrievalResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Credential)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult *This,
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredential **value);

    HRESULT (STDMETHODCALLTYPE *get_Status)(
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult *This,
        __x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialStatus *value);

    END_INTERFACE
} __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResultVtbl;

interface __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult {
    CONST_VTBL __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKeyCredentialRetrievalResult methods ***/
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_get_Credential(This,value) (This)->lpVtbl->get_Credential(This,value)
#define __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_get_Status(This,value) (This)->lpVtbl->get_Status(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_QueryInterface(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_AddRef(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_Release(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_GetIids(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_GetRuntimeClassName(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_GetTrustLevel(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKeyCredentialRetrievalResult methods ***/
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_get_Credential(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult* This,__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredential **value) {
    return This->lpVtbl->get_Credential(This,value);
}
static inline HRESULT __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_get_Status(__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult* This,__x_ABI_CWindows_CSecurity_CCredentials_CKeyCredentialStatus *value) {
    return This->lpVtbl->get_Status(This,value);
}
#endif
#ifdef WIDL_using_Windows_Security_Credentials
#define IID_IKeyCredentialRetrievalResult IID___x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult
#define IKeyCredentialRetrievalResultVtbl __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResultVtbl
#define IKeyCredentialRetrievalResult __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult
#define IKeyCredentialRetrievalResult_QueryInterface __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_QueryInterface
#define IKeyCredentialRetrievalResult_AddRef __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_AddRef
#define IKeyCredentialRetrievalResult_Release __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_Release
#define IKeyCredentialRetrievalResult_GetIids __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_GetIids
#define IKeyCredentialRetrievalResult_GetRuntimeClassName __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_GetRuntimeClassName
#define IKeyCredentialRetrievalResult_GetTrustLevel __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_GetTrustLevel
#define IKeyCredentialRetrievalResult_get_Credential __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_get_Credential
#define IKeyCredentialRetrievalResult_get_Status __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_get_Status
#endif /* WIDL_using_Windows_Security_Credentials */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Security.Credentials.KeyCredential
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Security_Credentials_KeyCredential_DEFINED
#define RUNTIMECLASS_Windows_Security_Credentials_KeyCredential_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Security_Credentials_KeyCredential[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','C','r','e','d','e','n','t','i','a','l','s','.','K','e','y','C','r','e','d','e','n','t','i','a','l',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Credentials_KeyCredential[] = L"Windows.Security.Credentials.KeyCredential";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Credentials_KeyCredential[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','C','r','e','d','e','n','t','i','a','l','s','.','K','e','y','C','r','e','d','e','n','t','i','a','l',0};
#endif
#endif /* RUNTIMECLASS_Windows_Security_Credentials_KeyCredential_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Security.Credentials.KeyCredentialManager
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Security_Credentials_KeyCredentialManager_DEFINED
#define RUNTIMECLASS_Windows_Security_Credentials_KeyCredentialManager_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Security_Credentials_KeyCredentialManager[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','C','r','e','d','e','n','t','i','a','l','s','.','K','e','y','C','r','e','d','e','n','t','i','a','l','M','a','n','a','g','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Credentials_KeyCredentialManager[] = L"Windows.Security.Credentials.KeyCredentialManager";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Credentials_KeyCredentialManager[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','C','r','e','d','e','n','t','i','a','l','s','.','K','e','y','C','r','e','d','e','n','t','i','a','l','M','a','n','a','g','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Security_Credentials_KeyCredentialManager_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Security.Credentials.KeyCredentialRetrievalResult
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Security_Credentials_KeyCredentialRetrievalResult_DEFINED
#define RUNTIMECLASS_Windows_Security_Credentials_KeyCredentialRetrievalResult_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Security_Credentials_KeyCredentialRetrievalResult[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','C','r','e','d','e','n','t','i','a','l','s','.','K','e','y','C','r','e','d','e','n','t','i','a','l','R','e','t','r','i','e','v','a','l','R','e','s','u','l','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Credentials_KeyCredentialRetrievalResult[] = L"Windows.Security.Credentials.KeyCredentialRetrievalResult";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Security_Credentials_KeyCredentialRetrievalResult[] = {'W','i','n','d','o','w','s','.','S','e','c','u','r','i','t','y','.','C','r','e','d','e','n','t','i','a','l','s','.','K','e','y','C','r','e','d','e','n','t','i','a','l','R','e','t','r','i','e','v','a','l','R','e','s','u','l','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Security_Credentials_KeyCredentialRetrievalResult_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult, 0x03ea60b1, 0xa874, 0x58ce, 0x8e,0x8e, 0xff,0xf4,0x48,0xb6,0x73,0x3e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("03ea60b1-a874-58ce-8e8e-fff448b6733e")
            IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult*, ABI::Windows::Security::Credentials::IKeyCredentialRetrievalResult* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult, 0x03ea60b1, 0xa874, 0x58ce, 0x8e,0x8e, 0xff,0xf4,0x48,0xb6,0x73,0x3e)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This,
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResultVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_Release(__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This,__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_KeyCredentialRetrievalResult IID___FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult
#define IAsyncOperationCompletedHandler_KeyCredentialRetrievalResultVtbl __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResultVtbl
#define IAsyncOperationCompletedHandler_KeyCredentialRetrievalResult __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult
#define IAsyncOperationCompletedHandler_KeyCredentialRetrievalResult_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_QueryInterface
#define IAsyncOperationCompletedHandler_KeyCredentialRetrievalResult_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_AddRef
#define IAsyncOperationCompletedHandler_KeyCredentialRetrievalResult_Release __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_Release
#define IAsyncOperationCompletedHandler_KeyCredentialRetrievalResult_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult, 0x89d0ad1e, 0xbd4c, 0x55b4, 0x81,0x0e, 0xbd,0xdd,0x4c,0xec,0x7a,0x2a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("89d0ad1e-bd4c-55b4-810e-bddd4cec7a2a")
            IAsyncOperation<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult*, ABI::Windows::Security::Credentials::IKeyCredentialRetrievalResult* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult, 0x89d0ad1e, 0xbd4c, 0x55b4, 0x81,0x0e, 0xbd,0xdd,0x4c,0xec,0x7a,0x2a)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *This,
        __x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResultVtbl;

interface __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult {
    CONST_VTBL __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > methods ***/
#define __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_QueryInterface(__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_AddRef(__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_Release(__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetIids(__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetTrustLevel(__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Security::Credentials::KeyCredentialRetrievalResult* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_put_Completed(__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This,__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_get_Completed(__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This,__FIAsyncOperationCompletedHandler_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetResults(__FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult* This,__x_ABI_CWindows_CSecurity_CCredentials_CIKeyCredentialRetrievalResult **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_KeyCredentialRetrievalResult IID___FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult
#define IAsyncOperation_KeyCredentialRetrievalResultVtbl __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResultVtbl
#define IAsyncOperation_KeyCredentialRetrievalResult __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult
#define IAsyncOperation_KeyCredentialRetrievalResult_QueryInterface __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_QueryInterface
#define IAsyncOperation_KeyCredentialRetrievalResult_AddRef __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_AddRef
#define IAsyncOperation_KeyCredentialRetrievalResult_Release __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_Release
#define IAsyncOperation_KeyCredentialRetrievalResult_GetIids __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetIids
#define IAsyncOperation_KeyCredentialRetrievalResult_GetRuntimeClassName __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetRuntimeClassName
#define IAsyncOperation_KeyCredentialRetrievalResult_GetTrustLevel __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetTrustLevel
#define IAsyncOperation_KeyCredentialRetrievalResult_put_Completed __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_put_Completed
#define IAsyncOperation_KeyCredentialRetrievalResult_get_Completed __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_get_Completed
#define IAsyncOperation_KeyCredentialRetrievalResult_GetResults __FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CSecurity__CCredentials__CKeyCredentialRetrievalResult_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_security_credentials_h__ */
