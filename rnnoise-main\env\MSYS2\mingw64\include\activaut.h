/*** Autogenerated by WIDL 10.12 from include/activaut.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __activaut_h__
#define __activaut_h__

/* Forward declarations */

#ifndef __IScriptNode_FWD_DEFINED__
#define __IScriptNode_FWD_DEFINED__
typedef interface IScriptNode IScriptNode;
#ifdef __cplusplus
interface IScriptNode;
#endif /* __cplusplus */
#endif

#ifndef __IScriptEntry_FWD_DEFINED__
#define __IScriptEntry_FWD_DEFINED__
typedef interface IScriptEntry IScriptEntry;
#ifdef __cplusplus
interface IScriptEntry;
#endif /* __cplusplus */
#endif

#ifndef __IScriptScriptlet_FWD_DEFINED__
#define __IScriptScriptlet_FWD_DEFINED__
typedef interface IScriptScriptlet IScriptScriptlet;
#ifdef __cplusplus
interface IScriptScriptlet;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptAuthor_FWD_DEFINED__
#define __IActiveScriptAuthor_FWD_DEFINED__
typedef interface IActiveScriptAuthor IActiveScriptAuthor;
#ifdef __cplusplus
interface IActiveScriptAuthor;
#endif /* __cplusplus */
#endif

#ifndef __IActiveScriptAuthorProcedure_FWD_DEFINED__
#define __IActiveScriptAuthorProcedure_FWD_DEFINED__
typedef interface IActiveScriptAuthorProcedure IActiveScriptAuthorProcedure;
#ifdef __cplusplus
interface IActiveScriptAuthorProcedure;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <ocidl.h>
#include <oleidl.h>
#include <oaidl.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

#ifndef __ActivAut_h
#define __ActivAut_h


#ifndef _NO_AUTHOR_GUIDS
DEFINE_GUID(CATID_ActiveScriptAuthor, 0xaee2a92, 0xbcbb, 0x11d0, 0x8c, 0x72, 0x0, 0xc0, 0x4f, 0xc2, 0xb0, 0x85);
#endif

const DWORD fasaPreferInternalHandler = 1;
const DWORD fasaSupportInternalHandler = 2;
const DWORD fasaCaseSensitive = 4;

const DWORD SCRIPT_CMPL_NOLIST = 0;
const DWORD SCRIPT_CMPL_MEMBERLIST = 1;
const DWORD SCRIPT_CMPL_ENUMLIST = 2;
const DWORD SCRIPT_CMPL_PARAMTIP = 4;
const DWORD SCRIPT_CMPL_GLOBALLIST = 8;

const DWORD SCRIPT_CMPL_ENUM_TRIGGER = 1;
const DWORD SCRIPT_CMPL_MEMBER_TRIGGER = 2;
const DWORD SCRIPT_CMPL_PARAM_TRIGGER = 3;
const DWORD SCRIPT_CMPL_COMMIT = 4;

const DWORD GETATTRTYPE_NORMAL = 0;
const DWORD GETATTRTYPE_DEPSCAN = 1;

const DWORD GETATTRFLAG_THIS = 0x100;
const DWORD GETATTRFLAG_HUMANTEXT = 0x8000;

const DWORD SOURCETEXT_ATTR_IDENTIFIER = 0x100;
const DWORD SOURCETEXT_ATTR_MEMBERLOOKUP = 0x200;
const DWORD SOURCETEXT_ATTR_THIS = 0x400;
const DWORD SOURCETEXT_ATTR_HUMANTEXT = 0x8000;

#ifndef __IActiveScriptAuthor_FWD_DEFINED__
#define __IActiveScriptAuthor_FWD_DEFINED__
typedef interface IActiveScriptAuthor IActiveScriptAuthor;
#ifdef __cplusplus
interface IActiveScriptAuthor;
#endif /* __cplusplus */
#endif

#ifndef __IScriptNode_FWD_DEFINED__
#define __IScriptNode_FWD_DEFINED__
typedef interface IScriptNode IScriptNode;
#ifdef __cplusplus
interface IScriptNode;
#endif /* __cplusplus */
#endif

#ifndef __IScriptEntry_FWD_DEFINED__
#define __IScriptEntry_FWD_DEFINED__
typedef interface IScriptEntry IScriptEntry;
#ifdef __cplusplus
interface IScriptEntry;
#endif /* __cplusplus */
#endif

#ifndef __IScriptScriptlet_FWD_DEFINED__
#define __IScriptScriptlet_FWD_DEFINED__
typedef interface IScriptScriptlet IScriptScriptlet;
#ifdef __cplusplus
interface IScriptScriptlet;
#endif /* __cplusplus */
#endif

typedef WORD SOURCE_TEXT_ATTR;
/*****************************************************************************
 * IScriptNode interface
 */
#ifndef __IScriptNode_INTERFACE_DEFINED__
#define __IScriptNode_INTERFACE_DEFINED__

DEFINE_GUID(IID_IScriptNode, 0x0aee2a94, 0xbcbb, 0x11d0, 0x8c,0x72, 0x00,0xc0,0x4f,0xc2,0xb0,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0aee2a94-bcbb-11d0-8c72-00c04fc2b085")
IScriptNode : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Alive(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetParent(
        IScriptNode **ppsnParent) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIndexInParent(
        ULONG *pisn) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCookie(
        DWORD *pdwCookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNumberOfChildren(
        ULONG *pcsn) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChild(
        ULONG isn,
        IScriptNode **ppsn) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguage(
        BSTR *pbstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateChildEntry(
        ULONG isn,
        DWORD dwCookie,
        LPCOLESTR pszDelimiter,
        IScriptEntry **ppse) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateChildHandler(
        LPCOLESTR pszDefaultName,
        LPCOLESTR *prgpszNames,
        ULONG cpszNames,
        LPCOLESTR pszEvent,
        LPCOLESTR pszDelimiter,
        ITypeInfo *ptiSignature,
        ULONG iMethodSignature,
        ULONG isn,
        DWORD dwCookie,
        IScriptEntry **ppse) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IScriptNode, 0x0aee2a94, 0xbcbb, 0x11d0, 0x8c,0x72, 0x00,0xc0,0x4f,0xc2,0xb0,0x85)
#endif
#else
typedef struct IScriptNodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IScriptNode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IScriptNode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IScriptNode *This);

    /*** IScriptNode methods ***/
    HRESULT (STDMETHODCALLTYPE *Alive)(
        IScriptNode *This);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IScriptNode *This);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IScriptNode *This,
        IScriptNode **ppsnParent);

    HRESULT (STDMETHODCALLTYPE *GetIndexInParent)(
        IScriptNode *This,
        ULONG *pisn);

    HRESULT (STDMETHODCALLTYPE *GetCookie)(
        IScriptNode *This,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *GetNumberOfChildren)(
        IScriptNode *This,
        ULONG *pcsn);

    HRESULT (STDMETHODCALLTYPE *GetChild)(
        IScriptNode *This,
        ULONG isn,
        IScriptNode **ppsn);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IScriptNode *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *CreateChildEntry)(
        IScriptNode *This,
        ULONG isn,
        DWORD dwCookie,
        LPCOLESTR pszDelimiter,
        IScriptEntry **ppse);

    HRESULT (STDMETHODCALLTYPE *CreateChildHandler)(
        IScriptNode *This,
        LPCOLESTR pszDefaultName,
        LPCOLESTR *prgpszNames,
        ULONG cpszNames,
        LPCOLESTR pszEvent,
        LPCOLESTR pszDelimiter,
        ITypeInfo *ptiSignature,
        ULONG iMethodSignature,
        ULONG isn,
        DWORD dwCookie,
        IScriptEntry **ppse);

    END_INTERFACE
} IScriptNodeVtbl;

interface IScriptNode {
    CONST_VTBL IScriptNodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IScriptNode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IScriptNode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IScriptNode_Release(This) (This)->lpVtbl->Release(This)
/*** IScriptNode methods ***/
#define IScriptNode_Alive(This) (This)->lpVtbl->Alive(This)
#define IScriptNode_Delete(This) (This)->lpVtbl->Delete(This)
#define IScriptNode_GetParent(This,ppsnParent) (This)->lpVtbl->GetParent(This,ppsnParent)
#define IScriptNode_GetIndexInParent(This,pisn) (This)->lpVtbl->GetIndexInParent(This,pisn)
#define IScriptNode_GetCookie(This,pdwCookie) (This)->lpVtbl->GetCookie(This,pdwCookie)
#define IScriptNode_GetNumberOfChildren(This,pcsn) (This)->lpVtbl->GetNumberOfChildren(This,pcsn)
#define IScriptNode_GetChild(This,isn,ppsn) (This)->lpVtbl->GetChild(This,isn,ppsn)
#define IScriptNode_GetLanguage(This,pbstr) (This)->lpVtbl->GetLanguage(This,pbstr)
#define IScriptNode_CreateChildEntry(This,isn,dwCookie,pszDelimiter,ppse) (This)->lpVtbl->CreateChildEntry(This,isn,dwCookie,pszDelimiter,ppse)
#define IScriptNode_CreateChildHandler(This,pszDefaultName,prgpszNames,cpszNames,pszEvent,pszDelimiter,ptiSignature,iMethodSignature,isn,dwCookie,ppse) (This)->lpVtbl->CreateChildHandler(This,pszDefaultName,prgpszNames,cpszNames,pszEvent,pszDelimiter,ptiSignature,iMethodSignature,isn,dwCookie,ppse)
#else
/*** IUnknown methods ***/
static inline HRESULT IScriptNode_QueryInterface(IScriptNode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IScriptNode_AddRef(IScriptNode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IScriptNode_Release(IScriptNode* This) {
    return This->lpVtbl->Release(This);
}
/*** IScriptNode methods ***/
static inline HRESULT IScriptNode_Alive(IScriptNode* This) {
    return This->lpVtbl->Alive(This);
}
static inline HRESULT IScriptNode_Delete(IScriptNode* This) {
    return This->lpVtbl->Delete(This);
}
static inline HRESULT IScriptNode_GetParent(IScriptNode* This,IScriptNode **ppsnParent) {
    return This->lpVtbl->GetParent(This,ppsnParent);
}
static inline HRESULT IScriptNode_GetIndexInParent(IScriptNode* This,ULONG *pisn) {
    return This->lpVtbl->GetIndexInParent(This,pisn);
}
static inline HRESULT IScriptNode_GetCookie(IScriptNode* This,DWORD *pdwCookie) {
    return This->lpVtbl->GetCookie(This,pdwCookie);
}
static inline HRESULT IScriptNode_GetNumberOfChildren(IScriptNode* This,ULONG *pcsn) {
    return This->lpVtbl->GetNumberOfChildren(This,pcsn);
}
static inline HRESULT IScriptNode_GetChild(IScriptNode* This,ULONG isn,IScriptNode **ppsn) {
    return This->lpVtbl->GetChild(This,isn,ppsn);
}
static inline HRESULT IScriptNode_GetLanguage(IScriptNode* This,BSTR *pbstr) {
    return This->lpVtbl->GetLanguage(This,pbstr);
}
static inline HRESULT IScriptNode_CreateChildEntry(IScriptNode* This,ULONG isn,DWORD dwCookie,LPCOLESTR pszDelimiter,IScriptEntry **ppse) {
    return This->lpVtbl->CreateChildEntry(This,isn,dwCookie,pszDelimiter,ppse);
}
static inline HRESULT IScriptNode_CreateChildHandler(IScriptNode* This,LPCOLESTR pszDefaultName,LPCOLESTR *prgpszNames,ULONG cpszNames,LPCOLESTR pszEvent,LPCOLESTR pszDelimiter,ITypeInfo *ptiSignature,ULONG iMethodSignature,ULONG isn,DWORD dwCookie,IScriptEntry **ppse) {
    return This->lpVtbl->CreateChildHandler(This,pszDefaultName,prgpszNames,cpszNames,pszEvent,pszDelimiter,ptiSignature,iMethodSignature,isn,dwCookie,ppse);
}
#endif
#endif

#endif


#endif  /* __IScriptNode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IScriptEntry interface
 */
#ifndef __IScriptEntry_INTERFACE_DEFINED__
#define __IScriptEntry_INTERFACE_DEFINED__

DEFINE_GUID(IID_IScriptEntry, 0x0aee2a95, 0xbcbb, 0x11d0, 0x8c,0x72, 0x00,0xc0,0x4f,0xc2,0xb0,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0aee2a95-bcbb-11d0-8c72-00c04fc2b085")
IScriptEntry : public IScriptNode
{
    virtual HRESULT STDMETHODCALLTYPE GetText(
        BSTR *pbstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetText(
        LPCOLESTR psz) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBody(
        BSTR *pbstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBody(
        LPCOLESTR psz) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetName(
        BSTR *pbstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetName(
        LPCOLESTR psz) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetItemName(
        BSTR *pbstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetItemName(
        LPCOLESTR psz) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignature(
        ITypeInfo **ppti,
        ULONG *piMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignature(
        ITypeInfo *pti,
        ULONG iMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRange(
        ULONG *pichMin,
        ULONG *pcch) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IScriptEntry, 0x0aee2a95, 0xbcbb, 0x11d0, 0x8c,0x72, 0x00,0xc0,0x4f,0xc2,0xb0,0x85)
#endif
#else
typedef struct IScriptEntryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IScriptEntry *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IScriptEntry *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IScriptEntry *This);

    /*** IScriptNode methods ***/
    HRESULT (STDMETHODCALLTYPE *Alive)(
        IScriptEntry *This);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IScriptEntry *This);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IScriptEntry *This,
        IScriptNode **ppsnParent);

    HRESULT (STDMETHODCALLTYPE *GetIndexInParent)(
        IScriptEntry *This,
        ULONG *pisn);

    HRESULT (STDMETHODCALLTYPE *GetCookie)(
        IScriptEntry *This,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *GetNumberOfChildren)(
        IScriptEntry *This,
        ULONG *pcsn);

    HRESULT (STDMETHODCALLTYPE *GetChild)(
        IScriptEntry *This,
        ULONG isn,
        IScriptNode **ppsn);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IScriptEntry *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *CreateChildEntry)(
        IScriptEntry *This,
        ULONG isn,
        DWORD dwCookie,
        LPCOLESTR pszDelimiter,
        IScriptEntry **ppse);

    HRESULT (STDMETHODCALLTYPE *CreateChildHandler)(
        IScriptEntry *This,
        LPCOLESTR pszDefaultName,
        LPCOLESTR *prgpszNames,
        ULONG cpszNames,
        LPCOLESTR pszEvent,
        LPCOLESTR pszDelimiter,
        ITypeInfo *ptiSignature,
        ULONG iMethodSignature,
        ULONG isn,
        DWORD dwCookie,
        IScriptEntry **ppse);

    /*** IScriptEntry methods ***/
    HRESULT (STDMETHODCALLTYPE *GetText)(
        IScriptEntry *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *SetText)(
        IScriptEntry *This,
        LPCOLESTR psz);

    HRESULT (STDMETHODCALLTYPE *GetBody)(
        IScriptEntry *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *SetBody)(
        IScriptEntry *This,
        LPCOLESTR psz);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IScriptEntry *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        IScriptEntry *This,
        LPCOLESTR psz);

    HRESULT (STDMETHODCALLTYPE *GetItemName)(
        IScriptEntry *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *SetItemName)(
        IScriptEntry *This,
        LPCOLESTR psz);

    HRESULT (STDMETHODCALLTYPE *GetSignature)(
        IScriptEntry *This,
        ITypeInfo **ppti,
        ULONG *piMethod);

    HRESULT (STDMETHODCALLTYPE *SetSignature)(
        IScriptEntry *This,
        ITypeInfo *pti,
        ULONG iMethod);

    HRESULT (STDMETHODCALLTYPE *GetRange)(
        IScriptEntry *This,
        ULONG *pichMin,
        ULONG *pcch);

    END_INTERFACE
} IScriptEntryVtbl;

interface IScriptEntry {
    CONST_VTBL IScriptEntryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IScriptEntry_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IScriptEntry_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IScriptEntry_Release(This) (This)->lpVtbl->Release(This)
/*** IScriptNode methods ***/
#define IScriptEntry_Alive(This) (This)->lpVtbl->Alive(This)
#define IScriptEntry_Delete(This) (This)->lpVtbl->Delete(This)
#define IScriptEntry_GetParent(This,ppsnParent) (This)->lpVtbl->GetParent(This,ppsnParent)
#define IScriptEntry_GetIndexInParent(This,pisn) (This)->lpVtbl->GetIndexInParent(This,pisn)
#define IScriptEntry_GetCookie(This,pdwCookie) (This)->lpVtbl->GetCookie(This,pdwCookie)
#define IScriptEntry_GetNumberOfChildren(This,pcsn) (This)->lpVtbl->GetNumberOfChildren(This,pcsn)
#define IScriptEntry_GetChild(This,isn,ppsn) (This)->lpVtbl->GetChild(This,isn,ppsn)
#define IScriptEntry_GetLanguage(This,pbstr) (This)->lpVtbl->GetLanguage(This,pbstr)
#define IScriptEntry_CreateChildEntry(This,isn,dwCookie,pszDelimiter,ppse) (This)->lpVtbl->CreateChildEntry(This,isn,dwCookie,pszDelimiter,ppse)
#define IScriptEntry_CreateChildHandler(This,pszDefaultName,prgpszNames,cpszNames,pszEvent,pszDelimiter,ptiSignature,iMethodSignature,isn,dwCookie,ppse) (This)->lpVtbl->CreateChildHandler(This,pszDefaultName,prgpszNames,cpszNames,pszEvent,pszDelimiter,ptiSignature,iMethodSignature,isn,dwCookie,ppse)
/*** IScriptEntry methods ***/
#define IScriptEntry_GetText(This,pbstr) (This)->lpVtbl->GetText(This,pbstr)
#define IScriptEntry_SetText(This,psz) (This)->lpVtbl->SetText(This,psz)
#define IScriptEntry_GetBody(This,pbstr) (This)->lpVtbl->GetBody(This,pbstr)
#define IScriptEntry_SetBody(This,psz) (This)->lpVtbl->SetBody(This,psz)
#define IScriptEntry_GetName(This,pbstr) (This)->lpVtbl->GetName(This,pbstr)
#define IScriptEntry_SetName(This,psz) (This)->lpVtbl->SetName(This,psz)
#define IScriptEntry_GetItemName(This,pbstr) (This)->lpVtbl->GetItemName(This,pbstr)
#define IScriptEntry_SetItemName(This,psz) (This)->lpVtbl->SetItemName(This,psz)
#define IScriptEntry_GetSignature(This,ppti,piMethod) (This)->lpVtbl->GetSignature(This,ppti,piMethod)
#define IScriptEntry_SetSignature(This,pti,iMethod) (This)->lpVtbl->SetSignature(This,pti,iMethod)
#define IScriptEntry_GetRange(This,pichMin,pcch) (This)->lpVtbl->GetRange(This,pichMin,pcch)
#else
/*** IUnknown methods ***/
static inline HRESULT IScriptEntry_QueryInterface(IScriptEntry* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IScriptEntry_AddRef(IScriptEntry* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IScriptEntry_Release(IScriptEntry* This) {
    return This->lpVtbl->Release(This);
}
/*** IScriptNode methods ***/
static inline HRESULT IScriptEntry_Alive(IScriptEntry* This) {
    return This->lpVtbl->Alive(This);
}
static inline HRESULT IScriptEntry_Delete(IScriptEntry* This) {
    return This->lpVtbl->Delete(This);
}
static inline HRESULT IScriptEntry_GetParent(IScriptEntry* This,IScriptNode **ppsnParent) {
    return This->lpVtbl->GetParent(This,ppsnParent);
}
static inline HRESULT IScriptEntry_GetIndexInParent(IScriptEntry* This,ULONG *pisn) {
    return This->lpVtbl->GetIndexInParent(This,pisn);
}
static inline HRESULT IScriptEntry_GetCookie(IScriptEntry* This,DWORD *pdwCookie) {
    return This->lpVtbl->GetCookie(This,pdwCookie);
}
static inline HRESULT IScriptEntry_GetNumberOfChildren(IScriptEntry* This,ULONG *pcsn) {
    return This->lpVtbl->GetNumberOfChildren(This,pcsn);
}
static inline HRESULT IScriptEntry_GetChild(IScriptEntry* This,ULONG isn,IScriptNode **ppsn) {
    return This->lpVtbl->GetChild(This,isn,ppsn);
}
static inline HRESULT IScriptEntry_GetLanguage(IScriptEntry* This,BSTR *pbstr) {
    return This->lpVtbl->GetLanguage(This,pbstr);
}
static inline HRESULT IScriptEntry_CreateChildEntry(IScriptEntry* This,ULONG isn,DWORD dwCookie,LPCOLESTR pszDelimiter,IScriptEntry **ppse) {
    return This->lpVtbl->CreateChildEntry(This,isn,dwCookie,pszDelimiter,ppse);
}
static inline HRESULT IScriptEntry_CreateChildHandler(IScriptEntry* This,LPCOLESTR pszDefaultName,LPCOLESTR *prgpszNames,ULONG cpszNames,LPCOLESTR pszEvent,LPCOLESTR pszDelimiter,ITypeInfo *ptiSignature,ULONG iMethodSignature,ULONG isn,DWORD dwCookie,IScriptEntry **ppse) {
    return This->lpVtbl->CreateChildHandler(This,pszDefaultName,prgpszNames,cpszNames,pszEvent,pszDelimiter,ptiSignature,iMethodSignature,isn,dwCookie,ppse);
}
/*** IScriptEntry methods ***/
static inline HRESULT IScriptEntry_GetText(IScriptEntry* This,BSTR *pbstr) {
    return This->lpVtbl->GetText(This,pbstr);
}
static inline HRESULT IScriptEntry_SetText(IScriptEntry* This,LPCOLESTR psz) {
    return This->lpVtbl->SetText(This,psz);
}
static inline HRESULT IScriptEntry_GetBody(IScriptEntry* This,BSTR *pbstr) {
    return This->lpVtbl->GetBody(This,pbstr);
}
static inline HRESULT IScriptEntry_SetBody(IScriptEntry* This,LPCOLESTR psz) {
    return This->lpVtbl->SetBody(This,psz);
}
static inline HRESULT IScriptEntry_GetName(IScriptEntry* This,BSTR *pbstr) {
    return This->lpVtbl->GetName(This,pbstr);
}
static inline HRESULT IScriptEntry_SetName(IScriptEntry* This,LPCOLESTR psz) {
    return This->lpVtbl->SetName(This,psz);
}
static inline HRESULT IScriptEntry_GetItemName(IScriptEntry* This,BSTR *pbstr) {
    return This->lpVtbl->GetItemName(This,pbstr);
}
static inline HRESULT IScriptEntry_SetItemName(IScriptEntry* This,LPCOLESTR psz) {
    return This->lpVtbl->SetItemName(This,psz);
}
static inline HRESULT IScriptEntry_GetSignature(IScriptEntry* This,ITypeInfo **ppti,ULONG *piMethod) {
    return This->lpVtbl->GetSignature(This,ppti,piMethod);
}
static inline HRESULT IScriptEntry_SetSignature(IScriptEntry* This,ITypeInfo *pti,ULONG iMethod) {
    return This->lpVtbl->SetSignature(This,pti,iMethod);
}
static inline HRESULT IScriptEntry_GetRange(IScriptEntry* This,ULONG *pichMin,ULONG *pcch) {
    return This->lpVtbl->GetRange(This,pichMin,pcch);
}
#endif
#endif

#endif


#endif  /* __IScriptEntry_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IScriptScriptlet interface
 */
#ifndef __IScriptScriptlet_INTERFACE_DEFINED__
#define __IScriptScriptlet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IScriptScriptlet, 0x0aee2a96, 0xbcbb, 0x11d0, 0x8c,0x72, 0x00,0xc0,0x4f,0xc2,0xb0,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0aee2a96-bcbb-11d0-8c72-00c04fc2b085")
IScriptScriptlet : public IScriptEntry
{
    virtual HRESULT STDMETHODCALLTYPE GetSubItemName(
        BSTR *pbstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSubItemName(
        LPCOLESTR psz) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEventName(
        BSTR *pbstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEventName(
        LPCOLESTR psz) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSimpleEventName(
        BSTR *pbstr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSimpleEventName(
        LPCOLESTR psz) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IScriptScriptlet, 0x0aee2a96, 0xbcbb, 0x11d0, 0x8c,0x72, 0x00,0xc0,0x4f,0xc2,0xb0,0x85)
#endif
#else
typedef struct IScriptScriptletVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IScriptScriptlet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IScriptScriptlet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IScriptScriptlet *This);

    /*** IScriptNode methods ***/
    HRESULT (STDMETHODCALLTYPE *Alive)(
        IScriptScriptlet *This);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IScriptScriptlet *This);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IScriptScriptlet *This,
        IScriptNode **ppsnParent);

    HRESULT (STDMETHODCALLTYPE *GetIndexInParent)(
        IScriptScriptlet *This,
        ULONG *pisn);

    HRESULT (STDMETHODCALLTYPE *GetCookie)(
        IScriptScriptlet *This,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *GetNumberOfChildren)(
        IScriptScriptlet *This,
        ULONG *pcsn);

    HRESULT (STDMETHODCALLTYPE *GetChild)(
        IScriptScriptlet *This,
        ULONG isn,
        IScriptNode **ppsn);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IScriptScriptlet *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *CreateChildEntry)(
        IScriptScriptlet *This,
        ULONG isn,
        DWORD dwCookie,
        LPCOLESTR pszDelimiter,
        IScriptEntry **ppse);

    HRESULT (STDMETHODCALLTYPE *CreateChildHandler)(
        IScriptScriptlet *This,
        LPCOLESTR pszDefaultName,
        LPCOLESTR *prgpszNames,
        ULONG cpszNames,
        LPCOLESTR pszEvent,
        LPCOLESTR pszDelimiter,
        ITypeInfo *ptiSignature,
        ULONG iMethodSignature,
        ULONG isn,
        DWORD dwCookie,
        IScriptEntry **ppse);

    /*** IScriptEntry methods ***/
    HRESULT (STDMETHODCALLTYPE *GetText)(
        IScriptScriptlet *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *SetText)(
        IScriptScriptlet *This,
        LPCOLESTR psz);

    HRESULT (STDMETHODCALLTYPE *GetBody)(
        IScriptScriptlet *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *SetBody)(
        IScriptScriptlet *This,
        LPCOLESTR psz);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IScriptScriptlet *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        IScriptScriptlet *This,
        LPCOLESTR psz);

    HRESULT (STDMETHODCALLTYPE *GetItemName)(
        IScriptScriptlet *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *SetItemName)(
        IScriptScriptlet *This,
        LPCOLESTR psz);

    HRESULT (STDMETHODCALLTYPE *GetSignature)(
        IScriptScriptlet *This,
        ITypeInfo **ppti,
        ULONG *piMethod);

    HRESULT (STDMETHODCALLTYPE *SetSignature)(
        IScriptScriptlet *This,
        ITypeInfo *pti,
        ULONG iMethod);

    HRESULT (STDMETHODCALLTYPE *GetRange)(
        IScriptScriptlet *This,
        ULONG *pichMin,
        ULONG *pcch);

    /*** IScriptScriptlet methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSubItemName)(
        IScriptScriptlet *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *SetSubItemName)(
        IScriptScriptlet *This,
        LPCOLESTR psz);

    HRESULT (STDMETHODCALLTYPE *GetEventName)(
        IScriptScriptlet *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *SetEventName)(
        IScriptScriptlet *This,
        LPCOLESTR psz);

    HRESULT (STDMETHODCALLTYPE *GetSimpleEventName)(
        IScriptScriptlet *This,
        BSTR *pbstr);

    HRESULT (STDMETHODCALLTYPE *SetSimpleEventName)(
        IScriptScriptlet *This,
        LPCOLESTR psz);

    END_INTERFACE
} IScriptScriptletVtbl;

interface IScriptScriptlet {
    CONST_VTBL IScriptScriptletVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IScriptScriptlet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IScriptScriptlet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IScriptScriptlet_Release(This) (This)->lpVtbl->Release(This)
/*** IScriptNode methods ***/
#define IScriptScriptlet_Alive(This) (This)->lpVtbl->Alive(This)
#define IScriptScriptlet_Delete(This) (This)->lpVtbl->Delete(This)
#define IScriptScriptlet_GetParent(This,ppsnParent) (This)->lpVtbl->GetParent(This,ppsnParent)
#define IScriptScriptlet_GetIndexInParent(This,pisn) (This)->lpVtbl->GetIndexInParent(This,pisn)
#define IScriptScriptlet_GetCookie(This,pdwCookie) (This)->lpVtbl->GetCookie(This,pdwCookie)
#define IScriptScriptlet_GetNumberOfChildren(This,pcsn) (This)->lpVtbl->GetNumberOfChildren(This,pcsn)
#define IScriptScriptlet_GetChild(This,isn,ppsn) (This)->lpVtbl->GetChild(This,isn,ppsn)
#define IScriptScriptlet_GetLanguage(This,pbstr) (This)->lpVtbl->GetLanguage(This,pbstr)
#define IScriptScriptlet_CreateChildEntry(This,isn,dwCookie,pszDelimiter,ppse) (This)->lpVtbl->CreateChildEntry(This,isn,dwCookie,pszDelimiter,ppse)
#define IScriptScriptlet_CreateChildHandler(This,pszDefaultName,prgpszNames,cpszNames,pszEvent,pszDelimiter,ptiSignature,iMethodSignature,isn,dwCookie,ppse) (This)->lpVtbl->CreateChildHandler(This,pszDefaultName,prgpszNames,cpszNames,pszEvent,pszDelimiter,ptiSignature,iMethodSignature,isn,dwCookie,ppse)
/*** IScriptEntry methods ***/
#define IScriptScriptlet_GetText(This,pbstr) (This)->lpVtbl->GetText(This,pbstr)
#define IScriptScriptlet_SetText(This,psz) (This)->lpVtbl->SetText(This,psz)
#define IScriptScriptlet_GetBody(This,pbstr) (This)->lpVtbl->GetBody(This,pbstr)
#define IScriptScriptlet_SetBody(This,psz) (This)->lpVtbl->SetBody(This,psz)
#define IScriptScriptlet_GetName(This,pbstr) (This)->lpVtbl->GetName(This,pbstr)
#define IScriptScriptlet_SetName(This,psz) (This)->lpVtbl->SetName(This,psz)
#define IScriptScriptlet_GetItemName(This,pbstr) (This)->lpVtbl->GetItemName(This,pbstr)
#define IScriptScriptlet_SetItemName(This,psz) (This)->lpVtbl->SetItemName(This,psz)
#define IScriptScriptlet_GetSignature(This,ppti,piMethod) (This)->lpVtbl->GetSignature(This,ppti,piMethod)
#define IScriptScriptlet_SetSignature(This,pti,iMethod) (This)->lpVtbl->SetSignature(This,pti,iMethod)
#define IScriptScriptlet_GetRange(This,pichMin,pcch) (This)->lpVtbl->GetRange(This,pichMin,pcch)
/*** IScriptScriptlet methods ***/
#define IScriptScriptlet_GetSubItemName(This,pbstr) (This)->lpVtbl->GetSubItemName(This,pbstr)
#define IScriptScriptlet_SetSubItemName(This,psz) (This)->lpVtbl->SetSubItemName(This,psz)
#define IScriptScriptlet_GetEventName(This,pbstr) (This)->lpVtbl->GetEventName(This,pbstr)
#define IScriptScriptlet_SetEventName(This,psz) (This)->lpVtbl->SetEventName(This,psz)
#define IScriptScriptlet_GetSimpleEventName(This,pbstr) (This)->lpVtbl->GetSimpleEventName(This,pbstr)
#define IScriptScriptlet_SetSimpleEventName(This,psz) (This)->lpVtbl->SetSimpleEventName(This,psz)
#else
/*** IUnknown methods ***/
static inline HRESULT IScriptScriptlet_QueryInterface(IScriptScriptlet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IScriptScriptlet_AddRef(IScriptScriptlet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IScriptScriptlet_Release(IScriptScriptlet* This) {
    return This->lpVtbl->Release(This);
}
/*** IScriptNode methods ***/
static inline HRESULT IScriptScriptlet_Alive(IScriptScriptlet* This) {
    return This->lpVtbl->Alive(This);
}
static inline HRESULT IScriptScriptlet_Delete(IScriptScriptlet* This) {
    return This->lpVtbl->Delete(This);
}
static inline HRESULT IScriptScriptlet_GetParent(IScriptScriptlet* This,IScriptNode **ppsnParent) {
    return This->lpVtbl->GetParent(This,ppsnParent);
}
static inline HRESULT IScriptScriptlet_GetIndexInParent(IScriptScriptlet* This,ULONG *pisn) {
    return This->lpVtbl->GetIndexInParent(This,pisn);
}
static inline HRESULT IScriptScriptlet_GetCookie(IScriptScriptlet* This,DWORD *pdwCookie) {
    return This->lpVtbl->GetCookie(This,pdwCookie);
}
static inline HRESULT IScriptScriptlet_GetNumberOfChildren(IScriptScriptlet* This,ULONG *pcsn) {
    return This->lpVtbl->GetNumberOfChildren(This,pcsn);
}
static inline HRESULT IScriptScriptlet_GetChild(IScriptScriptlet* This,ULONG isn,IScriptNode **ppsn) {
    return This->lpVtbl->GetChild(This,isn,ppsn);
}
static inline HRESULT IScriptScriptlet_GetLanguage(IScriptScriptlet* This,BSTR *pbstr) {
    return This->lpVtbl->GetLanguage(This,pbstr);
}
static inline HRESULT IScriptScriptlet_CreateChildEntry(IScriptScriptlet* This,ULONG isn,DWORD dwCookie,LPCOLESTR pszDelimiter,IScriptEntry **ppse) {
    return This->lpVtbl->CreateChildEntry(This,isn,dwCookie,pszDelimiter,ppse);
}
static inline HRESULT IScriptScriptlet_CreateChildHandler(IScriptScriptlet* This,LPCOLESTR pszDefaultName,LPCOLESTR *prgpszNames,ULONG cpszNames,LPCOLESTR pszEvent,LPCOLESTR pszDelimiter,ITypeInfo *ptiSignature,ULONG iMethodSignature,ULONG isn,DWORD dwCookie,IScriptEntry **ppse) {
    return This->lpVtbl->CreateChildHandler(This,pszDefaultName,prgpszNames,cpszNames,pszEvent,pszDelimiter,ptiSignature,iMethodSignature,isn,dwCookie,ppse);
}
/*** IScriptEntry methods ***/
static inline HRESULT IScriptScriptlet_GetText(IScriptScriptlet* This,BSTR *pbstr) {
    return This->lpVtbl->GetText(This,pbstr);
}
static inline HRESULT IScriptScriptlet_SetText(IScriptScriptlet* This,LPCOLESTR psz) {
    return This->lpVtbl->SetText(This,psz);
}
static inline HRESULT IScriptScriptlet_GetBody(IScriptScriptlet* This,BSTR *pbstr) {
    return This->lpVtbl->GetBody(This,pbstr);
}
static inline HRESULT IScriptScriptlet_SetBody(IScriptScriptlet* This,LPCOLESTR psz) {
    return This->lpVtbl->SetBody(This,psz);
}
static inline HRESULT IScriptScriptlet_GetName(IScriptScriptlet* This,BSTR *pbstr) {
    return This->lpVtbl->GetName(This,pbstr);
}
static inline HRESULT IScriptScriptlet_SetName(IScriptScriptlet* This,LPCOLESTR psz) {
    return This->lpVtbl->SetName(This,psz);
}
static inline HRESULT IScriptScriptlet_GetItemName(IScriptScriptlet* This,BSTR *pbstr) {
    return This->lpVtbl->GetItemName(This,pbstr);
}
static inline HRESULT IScriptScriptlet_SetItemName(IScriptScriptlet* This,LPCOLESTR psz) {
    return This->lpVtbl->SetItemName(This,psz);
}
static inline HRESULT IScriptScriptlet_GetSignature(IScriptScriptlet* This,ITypeInfo **ppti,ULONG *piMethod) {
    return This->lpVtbl->GetSignature(This,ppti,piMethod);
}
static inline HRESULT IScriptScriptlet_SetSignature(IScriptScriptlet* This,ITypeInfo *pti,ULONG iMethod) {
    return This->lpVtbl->SetSignature(This,pti,iMethod);
}
static inline HRESULT IScriptScriptlet_GetRange(IScriptScriptlet* This,ULONG *pichMin,ULONG *pcch) {
    return This->lpVtbl->GetRange(This,pichMin,pcch);
}
/*** IScriptScriptlet methods ***/
static inline HRESULT IScriptScriptlet_GetSubItemName(IScriptScriptlet* This,BSTR *pbstr) {
    return This->lpVtbl->GetSubItemName(This,pbstr);
}
static inline HRESULT IScriptScriptlet_SetSubItemName(IScriptScriptlet* This,LPCOLESTR psz) {
    return This->lpVtbl->SetSubItemName(This,psz);
}
static inline HRESULT IScriptScriptlet_GetEventName(IScriptScriptlet* This,BSTR *pbstr) {
    return This->lpVtbl->GetEventName(This,pbstr);
}
static inline HRESULT IScriptScriptlet_SetEventName(IScriptScriptlet* This,LPCOLESTR psz) {
    return This->lpVtbl->SetEventName(This,psz);
}
static inline HRESULT IScriptScriptlet_GetSimpleEventName(IScriptScriptlet* This,BSTR *pbstr) {
    return This->lpVtbl->GetSimpleEventName(This,pbstr);
}
static inline HRESULT IScriptScriptlet_SetSimpleEventName(IScriptScriptlet* This,LPCOLESTR psz) {
    return This->lpVtbl->SetSimpleEventName(This,psz);
}
#endif
#endif

#endif


#endif  /* __IScriptScriptlet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveScriptAuthor interface
 */
#ifndef __IActiveScriptAuthor_INTERFACE_DEFINED__
#define __IActiveScriptAuthor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptAuthor, 0x9c109da0, 0x7006, 0x11d1, 0xb3,0x6c, 0x00,0xa0,0xc9,0x11,0xe8,0xb2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9c109da0-7006-11d1-b36c-00a0c911e8b2")
IActiveScriptAuthor : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddNamedItem(
        LPCOLESTR pszName,
        DWORD dwFlags,
        IDispatch *pdisp) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddScriptlet(
        LPCOLESTR pszDefaultName,
        LPCOLESTR pszCode,
        LPCOLESTR pszItemName,
        LPCOLESTR pszSubItemName,
        LPCOLESTR pszEventName,
        LPCOLESTR pszDelimiter,
        DWORD dwCookie,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE ParseScriptText(
        LPCOLESTR pszCode,
        LPCOLESTR pszItemName,
        LPCOLESTR pszDelimiter,
        DWORD dwCookie,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptTextAttributes(
        LPCOLESTR pszCode,
        ULONG cch,
        LPCOLESTR pszDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptletTextAttributes(
        LPCOLESTR pszCode,
        ULONG cch,
        LPCOLESTR pszDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRoot(
        IScriptNode **ppsp) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguageFlags(
        DWORD *pgrfasa) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEventHandler(
        IDispatch *pdisp,
        LPCOLESTR pszItem,
        LPCOLESTR pszSubItem,
        LPCOLESTR pszEvent,
        IScriptEntry **ppse) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveNamedItem(
        LPCOLESTR pszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddTypeLib(
        REFGUID rguidTypeLib,
        DWORD dwMajor,
        DWORD dwMinor,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveTypeLib(
        REFGUID rguidTypeLib,
        DWORD dwMajor,
        DWORD dwMinor) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetChars(
        DWORD fRequestedList,
        BSTR *pbstrChars) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInfoFromContext(
        LPCOLESTR pszCode,
        ULONG cchCode,
        ULONG ichCurrentPosition,
        DWORD dwListTypesRequested,
        DWORD *pdwListTypesProvided,
        ULONG *pichListAnchorPosition,
        ULONG *pichFuncAnchorPosition,
        MEMBERID *pmemid,
        LONG *piCurrentParameter,
        IUnknown **ppunk) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsCommitChar(
        OLECHAR ch,
        WINBOOL *pfcommit) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptAuthor, 0x9c109da0, 0x7006, 0x11d1, 0xb3,0x6c, 0x00,0xa0,0xc9,0x11,0xe8,0xb2)
#endif
#else
typedef struct IActiveScriptAuthorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptAuthor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptAuthor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptAuthor *This);

    /*** IActiveScriptAuthor methods ***/
    HRESULT (STDMETHODCALLTYPE *AddNamedItem)(
        IActiveScriptAuthor *This,
        LPCOLESTR pszName,
        DWORD dwFlags,
        IDispatch *pdisp);

    HRESULT (STDMETHODCALLTYPE *AddScriptlet)(
        IActiveScriptAuthor *This,
        LPCOLESTR pszDefaultName,
        LPCOLESTR pszCode,
        LPCOLESTR pszItemName,
        LPCOLESTR pszSubItemName,
        LPCOLESTR pszEventName,
        LPCOLESTR pszDelimiter,
        DWORD dwCookie,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *ParseScriptText)(
        IActiveScriptAuthor *This,
        LPCOLESTR pszCode,
        LPCOLESTR pszItemName,
        LPCOLESTR pszDelimiter,
        DWORD dwCookie,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *GetScriptTextAttributes)(
        IActiveScriptAuthor *This,
        LPCOLESTR pszCode,
        ULONG cch,
        LPCOLESTR pszDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr);

    HRESULT (STDMETHODCALLTYPE *GetScriptletTextAttributes)(
        IActiveScriptAuthor *This,
        LPCOLESTR pszCode,
        ULONG cch,
        LPCOLESTR pszDelimiter,
        DWORD dwFlags,
        SOURCE_TEXT_ATTR *pattr);

    HRESULT (STDMETHODCALLTYPE *GetRoot)(
        IActiveScriptAuthor *This,
        IScriptNode **ppsp);

    HRESULT (STDMETHODCALLTYPE *GetLanguageFlags)(
        IActiveScriptAuthor *This,
        DWORD *pgrfasa);

    HRESULT (STDMETHODCALLTYPE *GetEventHandler)(
        IActiveScriptAuthor *This,
        IDispatch *pdisp,
        LPCOLESTR pszItem,
        LPCOLESTR pszSubItem,
        LPCOLESTR pszEvent,
        IScriptEntry **ppse);

    HRESULT (STDMETHODCALLTYPE *RemoveNamedItem)(
        IActiveScriptAuthor *This,
        LPCOLESTR pszName);

    HRESULT (STDMETHODCALLTYPE *AddTypeLib)(
        IActiveScriptAuthor *This,
        REFGUID rguidTypeLib,
        DWORD dwMajor,
        DWORD dwMinor,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *RemoveTypeLib)(
        IActiveScriptAuthor *This,
        REFGUID rguidTypeLib,
        DWORD dwMajor,
        DWORD dwMinor);

    HRESULT (STDMETHODCALLTYPE *GetChars)(
        IActiveScriptAuthor *This,
        DWORD fRequestedList,
        BSTR *pbstrChars);

    HRESULT (STDMETHODCALLTYPE *GetInfoFromContext)(
        IActiveScriptAuthor *This,
        LPCOLESTR pszCode,
        ULONG cchCode,
        ULONG ichCurrentPosition,
        DWORD dwListTypesRequested,
        DWORD *pdwListTypesProvided,
        ULONG *pichListAnchorPosition,
        ULONG *pichFuncAnchorPosition,
        MEMBERID *pmemid,
        LONG *piCurrentParameter,
        IUnknown **ppunk);

    HRESULT (STDMETHODCALLTYPE *IsCommitChar)(
        IActiveScriptAuthor *This,
        OLECHAR ch,
        WINBOOL *pfcommit);

    END_INTERFACE
} IActiveScriptAuthorVtbl;

interface IActiveScriptAuthor {
    CONST_VTBL IActiveScriptAuthorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptAuthor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptAuthor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptAuthor_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptAuthor methods ***/
#define IActiveScriptAuthor_AddNamedItem(This,pszName,dwFlags,pdisp) (This)->lpVtbl->AddNamedItem(This,pszName,dwFlags,pdisp)
#define IActiveScriptAuthor_AddScriptlet(This,pszDefaultName,pszCode,pszItemName,pszSubItemName,pszEventName,pszDelimiter,dwCookie,dwFlags) (This)->lpVtbl->AddScriptlet(This,pszDefaultName,pszCode,pszItemName,pszSubItemName,pszEventName,pszDelimiter,dwCookie,dwFlags)
#define IActiveScriptAuthor_ParseScriptText(This,pszCode,pszItemName,pszDelimiter,dwCookie,dwFlags) (This)->lpVtbl->ParseScriptText(This,pszCode,pszItemName,pszDelimiter,dwCookie,dwFlags)
#define IActiveScriptAuthor_GetScriptTextAttributes(This,pszCode,cch,pszDelimiter,dwFlags,pattr) (This)->lpVtbl->GetScriptTextAttributes(This,pszCode,cch,pszDelimiter,dwFlags,pattr)
#define IActiveScriptAuthor_GetScriptletTextAttributes(This,pszCode,cch,pszDelimiter,dwFlags,pattr) (This)->lpVtbl->GetScriptletTextAttributes(This,pszCode,cch,pszDelimiter,dwFlags,pattr)
#define IActiveScriptAuthor_GetRoot(This,ppsp) (This)->lpVtbl->GetRoot(This,ppsp)
#define IActiveScriptAuthor_GetLanguageFlags(This,pgrfasa) (This)->lpVtbl->GetLanguageFlags(This,pgrfasa)
#define IActiveScriptAuthor_GetEventHandler(This,pdisp,pszItem,pszSubItem,pszEvent,ppse) (This)->lpVtbl->GetEventHandler(This,pdisp,pszItem,pszSubItem,pszEvent,ppse)
#define IActiveScriptAuthor_RemoveNamedItem(This,pszName) (This)->lpVtbl->RemoveNamedItem(This,pszName)
#define IActiveScriptAuthor_AddTypeLib(This,rguidTypeLib,dwMajor,dwMinor,dwFlags) (This)->lpVtbl->AddTypeLib(This,rguidTypeLib,dwMajor,dwMinor,dwFlags)
#define IActiveScriptAuthor_RemoveTypeLib(This,rguidTypeLib,dwMajor,dwMinor) (This)->lpVtbl->RemoveTypeLib(This,rguidTypeLib,dwMajor,dwMinor)
#define IActiveScriptAuthor_GetChars(This,fRequestedList,pbstrChars) (This)->lpVtbl->GetChars(This,fRequestedList,pbstrChars)
#define IActiveScriptAuthor_GetInfoFromContext(This,pszCode,cchCode,ichCurrentPosition,dwListTypesRequested,pdwListTypesProvided,pichListAnchorPosition,pichFuncAnchorPosition,pmemid,piCurrentParameter,ppunk) (This)->lpVtbl->GetInfoFromContext(This,pszCode,cchCode,ichCurrentPosition,dwListTypesRequested,pdwListTypesProvided,pichListAnchorPosition,pichFuncAnchorPosition,pmemid,piCurrentParameter,ppunk)
#define IActiveScriptAuthor_IsCommitChar(This,ch,pfcommit) (This)->lpVtbl->IsCommitChar(This,ch,pfcommit)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptAuthor_QueryInterface(IActiveScriptAuthor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptAuthor_AddRef(IActiveScriptAuthor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptAuthor_Release(IActiveScriptAuthor* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptAuthor methods ***/
static inline HRESULT IActiveScriptAuthor_AddNamedItem(IActiveScriptAuthor* This,LPCOLESTR pszName,DWORD dwFlags,IDispatch *pdisp) {
    return This->lpVtbl->AddNamedItem(This,pszName,dwFlags,pdisp);
}
static inline HRESULT IActiveScriptAuthor_AddScriptlet(IActiveScriptAuthor* This,LPCOLESTR pszDefaultName,LPCOLESTR pszCode,LPCOLESTR pszItemName,LPCOLESTR pszSubItemName,LPCOLESTR pszEventName,LPCOLESTR pszDelimiter,DWORD dwCookie,DWORD dwFlags) {
    return This->lpVtbl->AddScriptlet(This,pszDefaultName,pszCode,pszItemName,pszSubItemName,pszEventName,pszDelimiter,dwCookie,dwFlags);
}
static inline HRESULT IActiveScriptAuthor_ParseScriptText(IActiveScriptAuthor* This,LPCOLESTR pszCode,LPCOLESTR pszItemName,LPCOLESTR pszDelimiter,DWORD dwCookie,DWORD dwFlags) {
    return This->lpVtbl->ParseScriptText(This,pszCode,pszItemName,pszDelimiter,dwCookie,dwFlags);
}
static inline HRESULT IActiveScriptAuthor_GetScriptTextAttributes(IActiveScriptAuthor* This,LPCOLESTR pszCode,ULONG cch,LPCOLESTR pszDelimiter,DWORD dwFlags,SOURCE_TEXT_ATTR *pattr) {
    return This->lpVtbl->GetScriptTextAttributes(This,pszCode,cch,pszDelimiter,dwFlags,pattr);
}
static inline HRESULT IActiveScriptAuthor_GetScriptletTextAttributes(IActiveScriptAuthor* This,LPCOLESTR pszCode,ULONG cch,LPCOLESTR pszDelimiter,DWORD dwFlags,SOURCE_TEXT_ATTR *pattr) {
    return This->lpVtbl->GetScriptletTextAttributes(This,pszCode,cch,pszDelimiter,dwFlags,pattr);
}
static inline HRESULT IActiveScriptAuthor_GetRoot(IActiveScriptAuthor* This,IScriptNode **ppsp) {
    return This->lpVtbl->GetRoot(This,ppsp);
}
static inline HRESULT IActiveScriptAuthor_GetLanguageFlags(IActiveScriptAuthor* This,DWORD *pgrfasa) {
    return This->lpVtbl->GetLanguageFlags(This,pgrfasa);
}
static inline HRESULT IActiveScriptAuthor_GetEventHandler(IActiveScriptAuthor* This,IDispatch *pdisp,LPCOLESTR pszItem,LPCOLESTR pszSubItem,LPCOLESTR pszEvent,IScriptEntry **ppse) {
    return This->lpVtbl->GetEventHandler(This,pdisp,pszItem,pszSubItem,pszEvent,ppse);
}
static inline HRESULT IActiveScriptAuthor_RemoveNamedItem(IActiveScriptAuthor* This,LPCOLESTR pszName) {
    return This->lpVtbl->RemoveNamedItem(This,pszName);
}
static inline HRESULT IActiveScriptAuthor_AddTypeLib(IActiveScriptAuthor* This,REFGUID rguidTypeLib,DWORD dwMajor,DWORD dwMinor,DWORD dwFlags) {
    return This->lpVtbl->AddTypeLib(This,rguidTypeLib,dwMajor,dwMinor,dwFlags);
}
static inline HRESULT IActiveScriptAuthor_RemoveTypeLib(IActiveScriptAuthor* This,REFGUID rguidTypeLib,DWORD dwMajor,DWORD dwMinor) {
    return This->lpVtbl->RemoveTypeLib(This,rguidTypeLib,dwMajor,dwMinor);
}
static inline HRESULT IActiveScriptAuthor_GetChars(IActiveScriptAuthor* This,DWORD fRequestedList,BSTR *pbstrChars) {
    return This->lpVtbl->GetChars(This,fRequestedList,pbstrChars);
}
static inline HRESULT IActiveScriptAuthor_GetInfoFromContext(IActiveScriptAuthor* This,LPCOLESTR pszCode,ULONG cchCode,ULONG ichCurrentPosition,DWORD dwListTypesRequested,DWORD *pdwListTypesProvided,ULONG *pichListAnchorPosition,ULONG *pichFuncAnchorPosition,MEMBERID *pmemid,LONG *piCurrentParameter,IUnknown **ppunk) {
    return This->lpVtbl->GetInfoFromContext(This,pszCode,cchCode,ichCurrentPosition,dwListTypesRequested,pdwListTypesProvided,pichListAnchorPosition,pichFuncAnchorPosition,pmemid,piCurrentParameter,ppunk);
}
static inline HRESULT IActiveScriptAuthor_IsCommitChar(IActiveScriptAuthor* This,OLECHAR ch,WINBOOL *pfcommit) {
    return This->lpVtbl->IsCommitChar(This,ch,pfcommit);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptAuthor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IActiveScriptAuthorProcedure interface
 */
#ifndef __IActiveScriptAuthorProcedure_INTERFACE_DEFINED__
#define __IActiveScriptAuthorProcedure_INTERFACE_DEFINED__

DEFINE_GUID(IID_IActiveScriptAuthorProcedure, 0x7e2d4b70, 0xbd9a, 0x11d0, 0x93,0x36, 0x00,0xa0,0xc9,0x0d,0xca,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7e2d4b70-bd9a-11d0-9336-00a0c90dcaa9")
IActiveScriptAuthorProcedure : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ParseProcedureText(
        LPCOLESTR pszCode,
        LPCOLESTR pszFormalParams,
        LPCOLESTR pszProcedureName,
        LPCOLESTR pszItemName,
        LPCOLESTR pszDelimiter,
        DWORD dwCookie,
        DWORD dwFlags,
        IDispatch *pdispFor) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IActiveScriptAuthorProcedure, 0x7e2d4b70, 0xbd9a, 0x11d0, 0x93,0x36, 0x00,0xa0,0xc9,0x0d,0xca,0xa9)
#endif
#else
typedef struct IActiveScriptAuthorProcedureVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IActiveScriptAuthorProcedure *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IActiveScriptAuthorProcedure *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IActiveScriptAuthorProcedure *This);

    /*** IActiveScriptAuthorProcedure methods ***/
    HRESULT (STDMETHODCALLTYPE *ParseProcedureText)(
        IActiveScriptAuthorProcedure *This,
        LPCOLESTR pszCode,
        LPCOLESTR pszFormalParams,
        LPCOLESTR pszProcedureName,
        LPCOLESTR pszItemName,
        LPCOLESTR pszDelimiter,
        DWORD dwCookie,
        DWORD dwFlags,
        IDispatch *pdispFor);

    END_INTERFACE
} IActiveScriptAuthorProcedureVtbl;

interface IActiveScriptAuthorProcedure {
    CONST_VTBL IActiveScriptAuthorProcedureVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IActiveScriptAuthorProcedure_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IActiveScriptAuthorProcedure_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IActiveScriptAuthorProcedure_Release(This) (This)->lpVtbl->Release(This)
/*** IActiveScriptAuthorProcedure methods ***/
#define IActiveScriptAuthorProcedure_ParseProcedureText(This,pszCode,pszFormalParams,pszProcedureName,pszItemName,pszDelimiter,dwCookie,dwFlags,pdispFor) (This)->lpVtbl->ParseProcedureText(This,pszCode,pszFormalParams,pszProcedureName,pszItemName,pszDelimiter,dwCookie,dwFlags,pdispFor)
#else
/*** IUnknown methods ***/
static inline HRESULT IActiveScriptAuthorProcedure_QueryInterface(IActiveScriptAuthorProcedure* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IActiveScriptAuthorProcedure_AddRef(IActiveScriptAuthorProcedure* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IActiveScriptAuthorProcedure_Release(IActiveScriptAuthorProcedure* This) {
    return This->lpVtbl->Release(This);
}
/*** IActiveScriptAuthorProcedure methods ***/
static inline HRESULT IActiveScriptAuthorProcedure_ParseProcedureText(IActiveScriptAuthorProcedure* This,LPCOLESTR pszCode,LPCOLESTR pszFormalParams,LPCOLESTR pszProcedureName,LPCOLESTR pszItemName,LPCOLESTR pszDelimiter,DWORD dwCookie,DWORD dwFlags,IDispatch *pdispFor) {
    return This->lpVtbl->ParseProcedureText(This,pszCode,pszFormalParams,pszProcedureName,pszItemName,pszDelimiter,dwCookie,dwFlags,pdispFor);
}
#endif
#endif

#endif


#endif  /* __IActiveScriptAuthorProcedure_INTERFACE_DEFINED__ */

#endif

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __activaut_h__ */
