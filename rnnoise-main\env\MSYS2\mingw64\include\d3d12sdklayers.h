/*** Autogenerated by WIDL 10.12 from include/d3d12sdklayers.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __d3d12sdklayers_h__
#define __d3d12sdklayers_h__

/* Forward declarations */

#ifndef __ID3D12Debug_FWD_DEFINED__
#define __ID3D12Debug_FWD_DEFINED__
typedef interface ID3D12Debug ID3D12Debug;
#ifdef __cplusplus
interface ID3D12Debug;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12Debug1_FWD_DEFINED__
#define __ID3D12Debug1_FWD_DEFINED__
typedef interface ID3D12Debug1 ID3D12Debug1;
#ifdef __cplusplus
interface ID3D12Debug1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12Debug2_FWD_DEFINED__
#define __ID3D12Debug2_FWD_DEFINED__
typedef interface ID3D12Debug2 ID3D12Debug2;
#ifdef __cplusplus
interface ID3D12Debug2;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12Debug3_FWD_DEFINED__
#define __ID3D12Debug3_FWD_DEFINED__
typedef interface ID3D12Debug3 ID3D12Debug3;
#ifdef __cplusplus
interface ID3D12Debug3;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12Debug4_FWD_DEFINED__
#define __ID3D12Debug4_FWD_DEFINED__
typedef interface ID3D12Debug4 ID3D12Debug4;
#ifdef __cplusplus
interface ID3D12Debug4;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12Debug5_FWD_DEFINED__
#define __ID3D12Debug5_FWD_DEFINED__
typedef interface ID3D12Debug5 ID3D12Debug5;
#ifdef __cplusplus
interface ID3D12Debug5;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12DebugDevice_FWD_DEFINED__
#define __ID3D12DebugDevice_FWD_DEFINED__
typedef interface ID3D12DebugDevice ID3D12DebugDevice;
#ifdef __cplusplus
interface ID3D12DebugDevice;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12DebugDevice1_FWD_DEFINED__
#define __ID3D12DebugDevice1_FWD_DEFINED__
typedef interface ID3D12DebugDevice1 ID3D12DebugDevice1;
#ifdef __cplusplus
interface ID3D12DebugDevice1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12DebugDevice2_FWD_DEFINED__
#define __ID3D12DebugDevice2_FWD_DEFINED__
typedef interface ID3D12DebugDevice2 ID3D12DebugDevice2;
#ifdef __cplusplus
interface ID3D12DebugDevice2;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12SharingContract_FWD_DEFINED__
#define __ID3D12SharingContract_FWD_DEFINED__
typedef interface ID3D12SharingContract ID3D12SharingContract;
#ifdef __cplusplus
interface ID3D12SharingContract;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12InfoQueue_FWD_DEFINED__
#define __ID3D12InfoQueue_FWD_DEFINED__
typedef interface ID3D12InfoQueue ID3D12InfoQueue;
#ifdef __cplusplus
interface ID3D12InfoQueue;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12InfoQueue1_FWD_DEFINED__
#define __ID3D12InfoQueue1_FWD_DEFINED__
typedef interface ID3D12InfoQueue1 ID3D12InfoQueue1;
#ifdef __cplusplus
interface ID3D12InfoQueue1;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <d3d12.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifdef WINE_NO_UNICODE_MACROS
#undef GetMessage
#endif
typedef enum D3D12_MESSAGE_CATEGORY {
    D3D12_MESSAGE_CATEGORY_APPLICATION_DEFINED = 0x0,
    D3D12_MESSAGE_CATEGORY_MISCELLANEOUS = 0x1,
    D3D12_MESSAGE_CATEGORY_INITIALIZATION = 0x2,
    D3D12_MESSAGE_CATEGORY_CLEANUP = 0x3,
    D3D12_MESSAGE_CATEGORY_COMPILATION = 0x4,
    D3D12_MESSAGE_CATEGORY_STATE_CREATION = 0x5,
    D3D12_MESSAGE_CATEGORY_STATE_SETTING = 0x6,
    D3D12_MESSAGE_CATEGORY_STATE_GETTING = 0x7,
    D3D12_MESSAGE_CATEGORY_RESOURCE_MANIPULATION = 0x8,
    D3D12_MESSAGE_CATEGORY_EXECUTION = 0x9,
    D3D12_MESSAGE_CATEGORY_SHADER = 0xa
} D3D12_MESSAGE_CATEGORY;
typedef enum D3D12_MESSAGE_SEVERITY {
    D3D12_MESSAGE_SEVERITY_CORRUPTION = 0x0,
    D3D12_MESSAGE_SEVERITY_ERROR = 0x1,
    D3D12_MESSAGE_SEVERITY_WARNING = 0x2,
    D3D12_MESSAGE_SEVERITY_INFO = 0x3,
    D3D12_MESSAGE_SEVERITY_MESSAGE = 0x4
} D3D12_MESSAGE_SEVERITY;
typedef enum D3D12_MESSAGE_ID {
    D3D12_MESSAGE_ID_UNKNOWN = 0x0,
    D3D12_MESSAGE_ID_STRING_FROM_APPLICATION = 0x1,
    D3D12_MESSAGE_ID_CORRUPTED_THIS = 0x2,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER1 = 0x3,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER2 = 0x4,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER3 = 0x5,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER4 = 0x6,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER5 = 0x7,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER6 = 0x8,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER7 = 0x9,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER8 = 0xa,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER9 = 0xb,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER10 = 0xc,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER11 = 0xd,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER12 = 0xe,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER13 = 0xf,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER14 = 0x10,
    D3D12_MESSAGE_ID_CORRUPTED_PARAMETER15 = 0x11,
    D3D12_MESSAGE_ID_CORRUPTED_MULTITHREADING = 0x12,
    D3D12_MESSAGE_ID_MESSAGE_REPORTING_OUTOFMEMORY = 0x13,
    D3D12_MESSAGE_ID_GETPRIVATEDATA_MOREDATA = 0x14,
    D3D12_MESSAGE_ID_SETPRIVATEDATA_INVALIDFREEDATA = 0x15,
    D3D12_MESSAGE_ID_SETPRIVATEDATA_CHANGINGPARAMS = 0x18,
    D3D12_MESSAGE_ID_SETPRIVATEDATA_OUTOFMEMORY = 0x19,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_UNRECOGNIZEDFORMAT = 0x1a,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDESC = 0x1b,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDFORMAT = 0x1c,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDVIDEOPLANESLICE = 0x1d,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDPLANESLICE = 0x1e,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDDIMENSIONS = 0x1f,
    D3D12_MESSAGE_ID_CREATESHADERRESOURCEVIEW_INVALIDRESOURCE = 0x20,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_UNRECOGNIZEDFORMAT = 0x23,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_UNSUPPORTEDFORMAT = 0x24,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDESC = 0x25,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDFORMAT = 0x26,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDVIDEOPLANESLICE = 0x27,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDPLANESLICE = 0x28,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDDIMENSIONS = 0x29,
    D3D12_MESSAGE_ID_CREATERENDERTARGETVIEW_INVALIDRESOURCE = 0x2a,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_UNRECOGNIZEDFORMAT = 0x2d,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDDESC = 0x2e,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDFORMAT = 0x2f,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDDIMENSIONS = 0x30,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDRESOURCE = 0x31,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_OUTOFMEMORY = 0x34,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_TOOMANYELEMENTS = 0x35,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDFORMAT = 0x36,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INCOMPATIBLEFORMAT = 0x37,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSLOT = 0x38,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDINPUTSLOTCLASS = 0x39,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_STEPRATESLOTCLASSMISMATCH = 0x3a,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSLOTCLASSCHANGE = 0x3b,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDSTEPRATECHANGE = 0x3c,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_INVALIDALIGNMENT = 0x3d,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_DUPLICATESEMANTIC = 0x3e,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_UNPARSEABLEINPUTSIGNATURE = 0x3f,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_NULLSEMANTIC = 0x40,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_MISSINGELEMENT = 0x41,
    D3D12_MESSAGE_ID_CREATEVERTEXSHADER_OUTOFMEMORY = 0x42,
    D3D12_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDSHADERBYTECODE = 0x43,
    D3D12_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDSHADERTYPE = 0x44,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_OUTOFMEMORY = 0x45,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDSHADERBYTECODE = 0x46,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDSHADERTYPE = 0x47,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTOFMEMORY = 0x48,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSHADERBYTECODE = 0x49,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSHADERTYPE = 0x4a,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMENTRIES = 0x4b,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTPUTSTREAMSTRIDEUNUSED = 0x4c,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_OUTPUTSLOT0EXPECTED = 0x4f,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDOUTPUTSLOT = 0x50,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_ONLYONEELEMENTPERSLOT = 0x51,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDCOMPONENTCOUNT = 0x52,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTARTCOMPONENTANDCOMPONENTCOUNT = 0x53,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDGAPDEFINITION = 0x54,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_REPEATEDOUTPUT = 0x55,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDOUTPUTSTREAMSTRIDE = 0x56,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MISSINGSEMANTIC = 0x57,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MASKMISMATCH = 0x58,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_CANTHAVEONLYGAPS = 0x59,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DECLTOOCOMPLEX = 0x5a,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_MISSINGOUTPUTSIGNATURE = 0x5b,
    D3D12_MESSAGE_ID_CREATEPIXELSHADER_OUTOFMEMORY = 0x5c,
    D3D12_MESSAGE_ID_CREATEPIXELSHADER_INVALIDSHADERBYTECODE = 0x5d,
    D3D12_MESSAGE_ID_CREATEPIXELSHADER_INVALIDSHADERTYPE = 0x5e,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDFILLMODE = 0x5f,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDCULLMODE = 0x60,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDDEPTHBIASCLAMP = 0x61,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDSLOPESCALEDDEPTHBIAS = 0x62,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDDEPTHWRITEMASK = 0x64,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDDEPTHFUNC = 0x65,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILFAILOP = 0x66,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILZFAILOP = 0x67,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILPASSOP = 0x68,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDFRONTFACESTENCILFUNC = 0x69,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILFAILOP = 0x6a,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILZFAILOP = 0x6b,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILPASSOP = 0x6c,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_INVALIDBACKFACESTENCILFUNC = 0x6d,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDSRCBLEND = 0x6f,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDDESTBLEND = 0x70,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDBLENDOP = 0x71,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDSRCBLENDALPHA = 0x72,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDDESTBLENDALPHA = 0x73,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDBLENDOPALPHA = 0x74,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDRENDERTARGETWRITEMASK = 0x75,
    D3D12_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_INVALID = 0x87,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_ROOT_SIGNATURE_NOT_SET = 0xc8,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_ROOT_SIGNATURE_MISMATCH = 0xc9,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_BUFFER_NOT_SET = 0xca,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_BUFFER_STRIDE_TOO_SMALL = 0xd1,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_BUFFER_TOO_SMALL = 0xd2,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_BUFFER_NOT_SET = 0xd3,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_BUFFER_FORMAT_INVALID = 0xd4,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_BUFFER_TOO_SMALL = 0xd5,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INVALID_PRIMITIVETOPOLOGY = 0xdb,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_VERTEX_STRIDE_UNALIGNED = 0xdd,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_INDEX_OFFSET_UNALIGNED = 0xde,
    D3D12_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_AT_FAULT = 0xe8,
    D3D12_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_POSSIBLY_AT_FAULT = 0xe9,
    D3D12_MESSAGE_ID_DEVICE_REMOVAL_PROCESS_NOT_AT_FAULT = 0xea,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_TRAILING_DIGIT_IN_SEMANTIC = 0xef,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_TRAILING_DIGIT_IN_SEMANTIC = 0xf0,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_TYPE_MISMATCH = 0xf5,
    D3D12_MESSAGE_ID_CREATEINPUTLAYOUT_EMPTY_LAYOUT = 0xfd,
    D3D12_MESSAGE_ID_LIVE_OBJECT_SUMMARY = 0xff,
    D3D12_MESSAGE_ID_LIVE_DEVICE = 0x112,
    D3D12_MESSAGE_ID_LIVE_SWAPCHAIN = 0x113,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILVIEW_INVALIDFLAGS = 0x114,
    D3D12_MESSAGE_ID_CREATEVERTEXSHADER_INVALIDCLASSLINKAGE = 0x115,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADER_INVALIDCLASSLINKAGE = 0x116,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTREAMTORASTERIZER = 0x118,
    D3D12_MESSAGE_ID_CREATEPIXELSHADER_INVALIDCLASSLINKAGE = 0x11b,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDSTREAM = 0x11c,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDENTRIES = 0x11d,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UNEXPECTEDSTRIDES = 0x11e,
    D3D12_MESSAGE_ID_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_INVALIDNUMSTRIDES = 0x11f,
    D3D12_MESSAGE_ID_CREATEHULLSHADER_OUTOFMEMORY = 0x121,
    D3D12_MESSAGE_ID_CREATEHULLSHADER_INVALIDSHADERBYTECODE = 0x122,
    D3D12_MESSAGE_ID_CREATEHULLSHADER_INVALIDSHADERTYPE = 0x123,
    D3D12_MESSAGE_ID_CREATEHULLSHADER_INVALIDCLASSLINKAGE = 0x124,
    D3D12_MESSAGE_ID_CREATEDOMAINSHADER_OUTOFMEMORY = 0x126,
    D3D12_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDSHADERBYTECODE = 0x127,
    D3D12_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDSHADERTYPE = 0x128,
    D3D12_MESSAGE_ID_CREATEDOMAINSHADER_INVALIDCLASSLINKAGE = 0x129,
    D3D12_MESSAGE_ID_RESOURCE_UNMAP_NOTMAPPED = 0x136,
    D3D12_MESSAGE_ID_DEVICE_CHECKFEATURESUPPORT_MISMATCHED_DATA_SIZE = 0x13e,
    D3D12_MESSAGE_ID_CREATECOMPUTESHADER_OUTOFMEMORY = 0x141,
    D3D12_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDSHADERBYTECODE = 0x142,
    D3D12_MESSAGE_ID_CREATECOMPUTESHADER_INVALIDCLASSLINKAGE = 0x143,
    D3D12_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x14b,
    D3D12_MESSAGE_ID_DEVICE_CREATEHULLSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x14c,
    D3D12_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x14d,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x14e,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DOUBLEFLOATOPSNOTSUPPORTED = 0x14f,
    D3D12_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x150,
    D3D12_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_DOUBLEFLOATOPSNOTSUPPORTED = 0x151,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDRESOURCE = 0x154,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDESC = 0x155,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDFORMAT = 0x156,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDVIDEOPLANESLICE = 0x157,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDPLANESLICE = 0x158,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDDIMENSIONS = 0x159,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_UNRECOGNIZEDFORMAT = 0x15a,
    D3D12_MESSAGE_ID_CREATEUNORDEREDACCESSVIEW_INVALIDFLAGS = 0x162,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALIDFORCEDSAMPLECOUNT = 0x191,
    D3D12_MESSAGE_ID_CREATEBLENDSTATE_INVALIDLOGICOPS = 0x193,
    D3D12_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x19a,
    D3D12_MESSAGE_ID_DEVICE_CREATEHULLSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x19c,
    D3D12_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x19e,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x1a0,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_DOUBLEEXTENSIONSNOTSUPPORTED = 0x1a2,
    D3D12_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x1a4,
    D3D12_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_DOUBLEEXTENSIONSNOTSUPPORTED = 0x1a6,
    D3D12_MESSAGE_ID_DEVICE_CREATEVERTEXSHADER_UAVSNOTSUPPORTED = 0x1a9,
    D3D12_MESSAGE_ID_DEVICE_CREATEHULLSHADER_UAVSNOTSUPPORTED = 0x1aa,
    D3D12_MESSAGE_ID_DEVICE_CREATEDOMAINSHADER_UAVSNOTSUPPORTED = 0x1ab,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADER_UAVSNOTSUPPORTED = 0x1ac,
    D3D12_MESSAGE_ID_DEVICE_CREATEGEOMETRYSHADERWITHSTREAMOUTPUT_UAVSNOTSUPPORTED = 0x1ad,
    D3D12_MESSAGE_ID_DEVICE_CREATEPIXELSHADER_UAVSNOTSUPPORTED = 0x1ae,
    D3D12_MESSAGE_ID_DEVICE_CREATECOMPUTESHADER_UAVSNOTSUPPORTED = 0x1af,
    D3D12_MESSAGE_ID_DEVICE_CLEARVIEW_INVALIDSOURCERECT = 0x1bf,
    D3D12_MESSAGE_ID_DEVICE_CLEARVIEW_EMPTYRECT = 0x1c0,
    D3D12_MESSAGE_ID_UPDATETILEMAPPINGS_INVALID_PARAMETER = 0x1ed,
    D3D12_MESSAGE_ID_COPYTILEMAPPINGS_INVALID_PARAMETER = 0x1ee,
    D3D12_MESSAGE_ID_CREATEDEVICE_INVALIDARGS = 0x1fa,
    D3D12_MESSAGE_ID_CREATEDEVICE_WARNING = 0x1fb,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_TYPE = 0x207,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_NULL_POINTER = 0x208,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_SUBRESOURCE = 0x209,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_RESERVED_BITS = 0x20a,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISSING_BIND_FLAGS = 0x20b,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISMATCHING_MISC_FLAGS = 0x20c,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_MATCHING_STATES = 0x20d,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_COMBINATION = 0x20e,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_BEFORE_AFTER_MISMATCH = 0x20f,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_RESOURCE = 0x210,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_SAMPLE_COUNT = 0x211,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_FLAGS = 0x212,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_COMBINED_FLAGS = 0x213,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_FLAGS_FOR_FORMAT = 0x214,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_SPLIT_BARRIER = 0x215,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_UNMATCHED_END = 0x216,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_UNMATCHED_BEGIN = 0x217,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_FLAG = 0x218,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_COMMAND_LIST_TYPE = 0x219,
    D3D12_MESSAGE_ID_INVALID_SUBRESOURCE_STATE = 0x21a,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_CONTENTION = 0x21c,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_RESET = 0x21d,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_RESET_BUNDLE = 0x21e,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_CANNOT_RESET = 0x21f,
    D3D12_MESSAGE_ID_COMMAND_LIST_OPEN = 0x220,
    D3D12_MESSAGE_ID_INVALID_BUNDLE_API = 0x222,
    D3D12_MESSAGE_ID_COMMAND_LIST_CLOSED = 0x223,
    D3D12_MESSAGE_ID_WRONG_COMMAND_ALLOCATOR_TYPE = 0x225,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_SYNC = 0x228,
    D3D12_MESSAGE_ID_COMMAND_LIST_SYNC = 0x229,
    D3D12_MESSAGE_ID_SET_DESCRIPTOR_HEAP_INVALID = 0x22a,
    D3D12_MESSAGE_ID_CREATE_COMMANDQUEUE = 0x22d,
    D3D12_MESSAGE_ID_CREATE_COMMANDALLOCATOR = 0x22e,
    D3D12_MESSAGE_ID_CREATE_PIPELINESTATE = 0x22f,
    D3D12_MESSAGE_ID_CREATE_COMMANDLIST12 = 0x230,
    D3D12_MESSAGE_ID_CREATE_RESOURCE = 0x232,
    D3D12_MESSAGE_ID_CREATE_DESCRIPTORHEAP = 0x233,
    D3D12_MESSAGE_ID_CREATE_ROOTSIGNATURE = 0x234,
    D3D12_MESSAGE_ID_CREATE_LIBRARY = 0x235,
    D3D12_MESSAGE_ID_CREATE_HEAP = 0x236,
    D3D12_MESSAGE_ID_CREATE_MONITOREDFENCE = 0x237,
    D3D12_MESSAGE_ID_CREATE_QUERYHEAP = 0x238,
    D3D12_MESSAGE_ID_CREATE_COMMANDSIGNATURE = 0x239,
    D3D12_MESSAGE_ID_LIVE_COMMANDQUEUE = 0x23a,
    D3D12_MESSAGE_ID_LIVE_COMMANDALLOCATOR = 0x23b,
    D3D12_MESSAGE_ID_LIVE_PIPELINESTATE = 0x23c,
    D3D12_MESSAGE_ID_LIVE_COMMANDLIST12 = 0x23d,
    D3D12_MESSAGE_ID_LIVE_RESOURCE = 0x23f,
    D3D12_MESSAGE_ID_LIVE_DESCRIPTORHEAP = 0x240,
    D3D12_MESSAGE_ID_LIVE_ROOTSIGNATURE = 0x241,
    D3D12_MESSAGE_ID_LIVE_LIBRARY = 0x242,
    D3D12_MESSAGE_ID_LIVE_HEAP = 0x243,
    D3D12_MESSAGE_ID_LIVE_MONITOREDFENCE = 0x244,
    D3D12_MESSAGE_ID_LIVE_QUERYHEAP = 0x245,
    D3D12_MESSAGE_ID_LIVE_COMMANDSIGNATURE = 0x246,
    D3D12_MESSAGE_ID_DESTROY_COMMANDQUEUE = 0x247,
    D3D12_MESSAGE_ID_DESTROY_COMMANDALLOCATOR = 0x248,
    D3D12_MESSAGE_ID_DESTROY_PIPELINESTATE = 0x249,
    D3D12_MESSAGE_ID_DESTROY_COMMANDLIST12 = 0x24a,
    D3D12_MESSAGE_ID_DESTROY_RESOURCE = 0x24c,
    D3D12_MESSAGE_ID_DESTROY_DESCRIPTORHEAP = 0x24d,
    D3D12_MESSAGE_ID_DESTROY_ROOTSIGNATURE = 0x24e,
    D3D12_MESSAGE_ID_DESTROY_LIBRARY = 0x24f,
    D3D12_MESSAGE_ID_DESTROY_HEAP = 0x250,
    D3D12_MESSAGE_ID_DESTROY_MONITOREDFENCE = 0x251,
    D3D12_MESSAGE_ID_DESTROY_QUERYHEAP = 0x252,
    D3D12_MESSAGE_ID_DESTROY_COMMANDSIGNATURE = 0x253,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDDIMENSIONS = 0x255,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDMISCFLAGS = 0x257,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDARG_RETURN = 0x25a,
    D3D12_MESSAGE_ID_CREATERESOURCE_OUTOFMEMORY_RETURN = 0x25b,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDDESC = 0x25c,
    D3D12_MESSAGE_ID_POSSIBLY_INVALID_SUBRESOURCE_STATE = 0x25f,
    D3D12_MESSAGE_ID_INVALID_USE_OF_NON_RESIDENT_RESOURCE = 0x260,
    D3D12_MESSAGE_ID_POSSIBLE_INVALID_USE_OF_NON_RESIDENT_RESOURCE = 0x261,
    D3D12_MESSAGE_ID_BUNDLE_PIPELINE_STATE_MISMATCH = 0x262,
    D3D12_MESSAGE_ID_PRIMITIVE_TOPOLOGY_MISMATCH_PIPELINE_STATE = 0x263,
    D3D12_MESSAGE_ID_RENDER_TARGET_FORMAT_MISMATCH_PIPELINE_STATE = 0x265,
    D3D12_MESSAGE_ID_RENDER_TARGET_SAMPLE_DESC_MISMATCH_PIPELINE_STATE = 0x266,
    D3D12_MESSAGE_ID_DEPTH_STENCIL_FORMAT_MISMATCH_PIPELINE_STATE = 0x267,
    D3D12_MESSAGE_ID_DEPTH_STENCIL_SAMPLE_DESC_MISMATCH_PIPELINE_STATE = 0x268,
    D3D12_MESSAGE_ID_CREATESHADER_INVALIDBYTECODE = 0x26e,
    D3D12_MESSAGE_ID_CREATEHEAP_NULLDESC = 0x26f,
    D3D12_MESSAGE_ID_CREATEHEAP_INVALIDSIZE = 0x270,
    D3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDHEAPTYPE = 0x271,
    D3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDCPUPAGEPROPERTIES = 0x272,
    D3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDMEMORYPOOL = 0x273,
    D3D12_MESSAGE_ID_CREATEHEAP_INVALIDPROPERTIES = 0x274,
    D3D12_MESSAGE_ID_CREATEHEAP_INVALIDALIGNMENT = 0x275,
    D3D12_MESSAGE_ID_CREATEHEAP_UNRECOGNIZEDMISCFLAGS = 0x276,
    D3D12_MESSAGE_ID_CREATEHEAP_INVALIDMISCFLAGS = 0x277,
    D3D12_MESSAGE_ID_CREATEHEAP_INVALIDARG_RETURN = 0x278,
    D3D12_MESSAGE_ID_CREATEHEAP_OUTOFMEMORY_RETURN = 0x279,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_NULLHEAPPROPERTIES = 0x27a,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDHEAPTYPE = 0x27b,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDCPUPAGEPROPERTIES = 0x27c,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDMEMORYPOOL = 0x27d,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_INVALIDHEAPPROPERTIES = 0x27e,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_UNRECOGNIZEDHEAPMISCFLAGS = 0x27f,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_INVALIDHEAPMISCFLAGS = 0x280,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_INVALIDARG_RETURN = 0x281,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_OUTOFMEMORY_RETURN = 0x282,
    D3D12_MESSAGE_ID_GETCUSTOMHEAPPROPERTIES_UNRECOGNIZEDHEAPTYPE = 0x283,
    D3D12_MESSAGE_ID_GETCUSTOMHEAPPROPERTIES_INVALIDHEAPTYPE = 0x284,
    D3D12_MESSAGE_ID_CREATE_DESCRIPTOR_HEAP_INVALID_DESC = 0x285,
    D3D12_MESSAGE_ID_INVALID_DESCRIPTOR_HANDLE = 0x286,
    D3D12_MESSAGE_ID_CREATERASTERIZERSTATE_INVALID_CONSERVATIVERASTERMODE = 0x287,
    D3D12_MESSAGE_ID_CREATE_CONSTANT_BUFFER_VIEW_INVALID_RESOURCE = 0x289,
    D3D12_MESSAGE_ID_CREATE_CONSTANT_BUFFER_VIEW_INVALID_DESC = 0x28a,
    D3D12_MESSAGE_ID_CREATE_UNORDEREDACCESS_VIEW_INVALID_COUNTER_USAGE = 0x28c,
    D3D12_MESSAGE_ID_COPY_DESCRIPTORS_INVALID_RANGES = 0x28d,
    D3D12_MESSAGE_ID_COPY_DESCRIPTORS_WRITE_ONLY_DESCRIPTOR = 0x28e,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_RTV_FORMAT_NOT_UNKNOWN = 0x28f,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_RENDER_TARGET_COUNT = 0x290,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_VERTEX_SHADER_NOT_SET = 0x291,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INPUTLAYOUT_NOT_SET = 0x292,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_HS_DS_SIGNATURE_MISMATCH = 0x293,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_REGISTERINDEX = 0x294,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_COMPONENTTYPE = 0x295,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_REGISTERMASK = 0x296,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_SYSTEMVALUE = 0x297,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_NEVERWRITTEN_ALWAYSREADS = 0x298,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_MINPRECISION = 0x299,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_LINKAGE_SEMANTICNAME_NOT_FOUND = 0x29a,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_XOR_DS_MISMATCH = 0x29b,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HULL_SHADER_INPUT_TOPOLOGY_MISMATCH = 0x29c,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_DS_CONTROL_POINT_COUNT_MISMATCH = 0x29d,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_DS_TESSELLATOR_DOMAIN_MISMATCH = 0x29e,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_USE_OF_CENTER_MULTISAMPLE_PATTERN = 0x29f,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_USE_OF_FORCED_SAMPLE_COUNT = 0x2a0,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_PRIMITIVETOPOLOGY = 0x2a1,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_SYSTEMVALUE = 0x2a2,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_OM_DUAL_SOURCE_BLENDING_CAN_ONLY_HAVE_RENDER_TARGET_0 = 0x2a3,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_OM_RENDER_TARGET_DOES_NOT_SUPPORT_BLENDING = 0x2a4,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_PS_OUTPUT_TYPE_MISMATCH = 0x2a5,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_OM_RENDER_TARGET_DOES_NOT_SUPPORT_LOGIC_OPS = 0x2a6,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_RENDERTARGETVIEW_NOT_SET = 0x2a7,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_DEPTHSTENCILVIEW_NOT_SET = 0x2a8,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_GS_INPUT_PRIMITIVE_MISMATCH = 0x2a9,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_POSITION_NOT_PRESENT = 0x2aa,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MISSING_ROOT_SIGNATURE_FLAGS = 0x2ab,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_INDEX_BUFFER_PROPERTIES = 0x2ac,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INVALID_SAMPLE_DESC = 0x2ad,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_HS_ROOT_SIGNATURE_MISMATCH = 0x2ae,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_DS_ROOT_SIGNATURE_MISMATCH = 0x2af,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_VS_ROOT_SIGNATURE_MISMATCH = 0x2b0,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_GS_ROOT_SIGNATURE_MISMATCH = 0x2b1,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_PS_ROOT_SIGNATURE_MISMATCH = 0x2b2,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MISSING_ROOT_SIGNATURE = 0x2b3,
    D3D12_MESSAGE_ID_EXECUTE_BUNDLE_OPEN_BUNDLE = 0x2b4,
    D3D12_MESSAGE_ID_EXECUTE_BUNDLE_DESCRIPTOR_HEAP_MISMATCH = 0x2b5,
    D3D12_MESSAGE_ID_EXECUTE_BUNDLE_TYPE = 0x2b6,
    D3D12_MESSAGE_ID_DRAW_EMPTY_SCISSOR_RECTANGLE = 0x2b7,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_BLOB_NOT_FOUND = 0x2b8,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_DESERIALIZE_FAILED = 0x2b9,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_INVALID_CONFIGURATION = 0x2ba,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_NOT_SUPPORTED_ON_DEVICE = 0x2bb,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_NULLRESOURCEPROPERTIES = 0x2bc,
    D3D12_MESSAGE_ID_CREATERESOURCEANDHEAP_NULLHEAP = 0x2bd,
    D3D12_MESSAGE_ID_GETRESOURCEALLOCATIONINFO_INVALIDRDESCS = 0x2be,
    D3D12_MESSAGE_ID_MAKERESIDENT_NULLOBJECTARRAY = 0x2bf,
    D3D12_MESSAGE_ID_EVICT_NULLOBJECTARRAY = 0x2c1,
    D3D12_MESSAGE_ID_SET_DESCRIPTOR_TABLE_INVALID = 0x2c4,
    D3D12_MESSAGE_ID_SET_ROOT_CONSTANT_INVALID = 0x2c5,
    D3D12_MESSAGE_ID_SET_ROOT_CONSTANT_BUFFER_VIEW_INVALID = 0x2c6,
    D3D12_MESSAGE_ID_SET_ROOT_SHADER_RESOURCE_VIEW_INVALID = 0x2c7,
    D3D12_MESSAGE_ID_SET_ROOT_UNORDERED_ACCESS_VIEW_INVALID = 0x2c8,
    D3D12_MESSAGE_ID_SET_VERTEX_BUFFERS_INVALID_DESC = 0x2c9,
    D3D12_MESSAGE_ID_SET_INDEX_BUFFER_INVALID_DESC = 0x2cb,
    D3D12_MESSAGE_ID_SET_STREAM_OUTPUT_BUFFERS_INVALID_DESC = 0x2cd,
    D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDDIMENSIONALITY = 0x2ce,
    D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDLAYOUT = 0x2cf,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDDIMENSIONALITY = 0x2d0,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDALIGNMENT = 0x2d1,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDMIPLEVELS = 0x2d2,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDSAMPLEDESC = 0x2d3,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDLAYOUT = 0x2d4,
    D3D12_MESSAGE_ID_SET_INDEX_BUFFER_INVALID = 0x2d5,
    D3D12_MESSAGE_ID_SET_VERTEX_BUFFERS_INVALID = 0x2d6,
    D3D12_MESSAGE_ID_SET_STREAM_OUTPUT_BUFFERS_INVALID = 0x2d7,
    D3D12_MESSAGE_ID_SET_RENDER_TARGETS_INVALID = 0x2d8,
    D3D12_MESSAGE_ID_CREATEQUERY_HEAP_INVALID_PARAMETERS = 0x2d9,
    D3D12_MESSAGE_ID_BEGIN_END_QUERY_INVALID_PARAMETERS = 0x2db,
    D3D12_MESSAGE_ID_CLOSE_COMMAND_LIST_OPEN_QUERY = 0x2dc,
    D3D12_MESSAGE_ID_RESOLVE_QUERY_DATA_INVALID_PARAMETERS = 0x2dd,
    D3D12_MESSAGE_ID_SET_PREDICATION_INVALID_PARAMETERS = 0x2de,
    D3D12_MESSAGE_ID_TIMESTAMPS_NOT_SUPPORTED = 0x2df,
    D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDFORMAT = 0x2e1,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDFORMAT = 0x2e2,
    D3D12_MESSAGE_ID_GETCOPYABLEFOOTPRINTS_INVALIDSUBRESOURCERANGE = 0x2e3,
    D3D12_MESSAGE_ID_GETCOPYABLEFOOTPRINTS_INVALIDBASEOFFSET = 0x2e4,
    D3D12_MESSAGE_ID_GETCOPYABLELAYOUT_INVALIDSUBRESOURCERANGE = 0x2e3,
    D3D12_MESSAGE_ID_GETCOPYABLELAYOUT_INVALIDBASEOFFSET = 0x2e4,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_INVALID_HEAP = 0x2e5,
    D3D12_MESSAGE_ID_CREATE_SAMPLER_INVALID = 0x2e6,
    D3D12_MESSAGE_ID_CREATECOMMANDSIGNATURE_INVALID = 0x2e7,
    D3D12_MESSAGE_ID_EXECUTE_INDIRECT_INVALID_PARAMETERS = 0x2e8,
    D3D12_MESSAGE_ID_GETGPUVIRTUALADDRESS_INVALID_RESOURCE_DIMENSION = 0x2e9,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDCLEARVALUE = 0x32f,
    D3D12_MESSAGE_ID_CREATERESOURCE_UNRECOGNIZEDCLEARVALUEFORMAT = 0x330,
    D3D12_MESSAGE_ID_CREATERESOURCE_INVALIDCLEARVALUEFORMAT = 0x331,
    D3D12_MESSAGE_ID_CREATERESOURCE_CLEARVALUEDENORMFLUSH = 0x332,
    D3D12_MESSAGE_ID_CLEARRENDERTARGETVIEW_MISMATCHINGCLEARVALUE = 0x334,
    D3D12_MESSAGE_ID_CLEARDEPTHSTENCILVIEW_MISMATCHINGCLEARVALUE = 0x335,
    D3D12_MESSAGE_ID_MAP_INVALIDHEAP = 0x336,
    D3D12_MESSAGE_ID_UNMAP_INVALIDHEAP = 0x337,
    D3D12_MESSAGE_ID_MAP_INVALIDRESOURCE = 0x338,
    D3D12_MESSAGE_ID_UNMAP_INVALIDRESOURCE = 0x339,
    D3D12_MESSAGE_ID_MAP_INVALIDSUBRESOURCE = 0x33a,
    D3D12_MESSAGE_ID_UNMAP_INVALIDSUBRESOURCE = 0x33b,
    D3D12_MESSAGE_ID_MAP_INVALIDRANGE = 0x33c,
    D3D12_MESSAGE_ID_UNMAP_INVALIDRANGE = 0x33d,
    D3D12_MESSAGE_ID_MAP_INVALIDDATAPOINTER = 0x340,
    D3D12_MESSAGE_ID_MAP_INVALIDARG_RETURN = 0x341,
    D3D12_MESSAGE_ID_MAP_OUTOFMEMORY_RETURN = 0x342,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_BUNDLENOTSUPPORTED = 0x343,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_COMMANDLISTMISMATCH = 0x344,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_OPENCOMMANDLIST = 0x345,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_FAILEDCOMMANDLIST = 0x346,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_NULLDST = 0x347,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_INVALIDDSTRESOURCEDIMENSION = 0x348,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_DSTRANGEOUTOFBOUNDS = 0x349,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_NULLSRC = 0x34a,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_INVALIDSRCRESOURCEDIMENSION = 0x34b,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_SRCRANGEOUTOFBOUNDS = 0x34c,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_INVALIDCOPYFLAGS = 0x34d,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_NULLDST = 0x34e,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDDSTTYPE = 0x34f,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTRESOURCEDIMENSION = 0x350,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTRESOURCE = 0x351,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTSUBRESOURCE = 0x352,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTOFFSET = 0x353,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDDSTFORMAT = 0x354,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTFORMAT = 0x355,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTDIMENSIONS = 0x356,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTROWPITCH = 0x357,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTPLACEMENT = 0x358,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTDSPLACEDFOOTPRINTFORMAT = 0x359,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_DSTREGIONOUTOFBOUNDS = 0x35a,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_NULLSRC = 0x35b,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDSRCTYPE = 0x35c,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCRESOURCEDIMENSION = 0x35d,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCRESOURCE = 0x35e,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCSUBRESOURCE = 0x35f,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCOFFSET = 0x360,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_UNRECOGNIZEDSRCFORMAT = 0x361,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCFORMAT = 0x362,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCDIMENSIONS = 0x363,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCROWPITCH = 0x364,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCPLACEMENT = 0x365,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCDSPLACEDFOOTPRINTFORMAT = 0x366,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_SRCREGIONOUTOFBOUNDS = 0x367,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDDSTCOORDINATES = 0x368,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDSRCBOX = 0x369,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_FORMATMISMATCH = 0x36a,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_EMPTYBOX = 0x36b,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_INVALIDCOPYFLAGS = 0x36c,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALID_SUBRESOURCE_INDEX = 0x36d,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALID_FORMAT = 0x36e,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_RESOURCE_MISMATCH = 0x36f,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALID_SAMPLE_COUNT = 0x370,
    D3D12_MESSAGE_ID_CREATECOMPUTEPIPELINESTATE_INVALID_SHADER = 0x371,
    D3D12_MESSAGE_ID_CREATECOMPUTEPIPELINESTATE_CS_ROOT_SIGNATURE_MISMATCH = 0x372,
    D3D12_MESSAGE_ID_CREATECOMPUTEPIPELINESTATE_MISSING_ROOT_SIGNATURE = 0x373,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_INVALIDCACHEDBLOB = 0x374,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBADAPTERMISMATCH = 0x375,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBDRIVERVERSIONMISMATCH = 0x376,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBDESCMISMATCH = 0x377,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_CACHEDBLOBIGNORED = 0x378,
    D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDHEAP = 0x379,
    D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDRESOURCE = 0x37a,
    D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDBOX = 0x37b,
    D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_INVALIDSUBRESOURCE = 0x37c,
    D3D12_MESSAGE_ID_WRITETOSUBRESOURCE_EMPTYBOX = 0x37d,
    D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDHEAP = 0x37e,
    D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDRESOURCE = 0x37f,
    D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDBOX = 0x380,
    D3D12_MESSAGE_ID_READFROMSUBRESOURCE_INVALIDSUBRESOURCE = 0x381,
    D3D12_MESSAGE_ID_READFROMSUBRESOURCE_EMPTYBOX = 0x382,
    D3D12_MESSAGE_ID_TOO_MANY_NODES_SPECIFIED = 0x383,
    D3D12_MESSAGE_ID_INVALID_NODE_INDEX = 0x384,
    D3D12_MESSAGE_ID_GETHEAPPROPERTIES_INVALIDRESOURCE = 0x385,
    D3D12_MESSAGE_ID_NODE_MASK_MISMATCH = 0x386,
    D3D12_MESSAGE_ID_COMMAND_LIST_OUTOFMEMORY = 0x387,
    D3D12_MESSAGE_ID_COMMAND_LIST_MULTIPLE_SWAPCHAIN_BUFFER_REFERENCES = 0x388,
    D3D12_MESSAGE_ID_COMMAND_LIST_TOO_MANY_SWAPCHAIN_REFERENCES = 0x389,
    D3D12_MESSAGE_ID_COMMAND_QUEUE_TOO_MANY_SWAPCHAIN_REFERENCES = 0x38a,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_WRONGSWAPCHAINBUFFERREFERENCE = 0x38b,
    D3D12_MESSAGE_ID_COMMAND_LIST_SETRENDERTARGETS_INVALIDNUMRENDERTARGETS = 0x38c,
    D3D12_MESSAGE_ID_CREATE_QUEUE_INVALID_TYPE = 0x38d,
    D3D12_MESSAGE_ID_CREATE_QUEUE_INVALID_FLAGS = 0x38e,
    D3D12_MESSAGE_ID_CREATESHAREDRESOURCE_INVALIDFLAGS = 0x38f,
    D3D12_MESSAGE_ID_CREATESHAREDRESOURCE_INVALIDFORMAT = 0x390,
    D3D12_MESSAGE_ID_CREATESHAREDHEAP_INVALIDFLAGS = 0x391,
    D3D12_MESSAGE_ID_REFLECTSHAREDPROPERTIES_UNRECOGNIZEDPROPERTIES = 0x392,
    D3D12_MESSAGE_ID_REFLECTSHAREDPROPERTIES_INVALIDSIZE = 0x393,
    D3D12_MESSAGE_ID_REFLECTSHAREDPROPERTIES_INVALIDOBJECT = 0x394,
    D3D12_MESSAGE_ID_KEYEDMUTEX_INVALIDOBJECT = 0x395,
    D3D12_MESSAGE_ID_KEYEDMUTEX_INVALIDKEY = 0x396,
    D3D12_MESSAGE_ID_KEYEDMUTEX_WRONGSTATE = 0x397,
    D3D12_MESSAGE_ID_CREATE_QUEUE_INVALID_PRIORITY = 0x398,
    D3D12_MESSAGE_ID_OBJECT_DELETED_WHILE_STILL_IN_USE = 0x399,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_INVALID_FLAGS = 0x39a,
    D3D12_MESSAGE_ID_HEAP_ADDRESS_RANGE_HAS_NO_RESOURCE = 0x39b,
    D3D12_MESSAGE_ID_COMMAND_LIST_DRAW_RENDER_TARGET_DELETED = 0x39c,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_ALL_RENDER_TARGETS_HAVE_UNKNOWN_FORMAT = 0x39d,
    D3D12_MESSAGE_ID_HEAP_ADDRESS_RANGE_INTERSECTS_MULTIPLE_BUFFERS = 0x39e,
    D3D12_MESSAGE_ID_EXECUTECOMMANDLISTS_GPU_WRITTEN_READBACK_RESOURCE_MAPPED = 0x39f,
    D3D12_MESSAGE_ID_UNMAP_RANGE_NOT_EMPTY = 0x3a1,
    D3D12_MESSAGE_ID_MAP_INVALID_NULLRANGE = 0x3a2,
    D3D12_MESSAGE_ID_UNMAP_INVALID_NULLRANGE = 0x3a3,
    D3D12_MESSAGE_ID_NO_GRAPHICS_API_SUPPORT = 0x3a4,
    D3D12_MESSAGE_ID_NO_COMPUTE_API_SUPPORT = 0x3a5,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_RESOURCE_FLAGS_NOT_SUPPORTED = 0x3a6,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_ROOT_ARGUMENT_UNINITIALIZED = 0x3a7,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_HEAP_INDEX_OUT_OF_BOUNDS = 0x3a8,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_TABLE_REGISTER_INDEX_OUT_OF_BOUNDS = 0x3a9,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_UNINITIALIZED = 0x3aa,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_DESCRIPTOR_TYPE_MISMATCH = 0x3ab,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_SRV_RESOURCE_DIMENSION_MISMATCH = 0x3ac,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_UAV_RESOURCE_DIMENSION_MISMATCH = 0x3ad,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_INCOMPATIBLE_RESOURCE_STATE = 0x3ae,
    D3D12_MESSAGE_ID_COPYRESOURCE_NULLDST = 0x3af,
    D3D12_MESSAGE_ID_COPYRESOURCE_INVALIDDSTRESOURCE = 0x3b0,
    D3D12_MESSAGE_ID_COPYRESOURCE_NULLSRC = 0x3b1,
    D3D12_MESSAGE_ID_COPYRESOURCE_INVALIDSRCRESOURCE = 0x3b2,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_NULLDST = 0x3b3,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALIDDSTRESOURCE = 0x3b4,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_NULLSRC = 0x3b5,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_INVALIDSRCRESOURCE = 0x3b6,
    D3D12_MESSAGE_ID_PIPELINE_STATE_TYPE_MISMATCH = 0x3b7,
    D3D12_MESSAGE_ID_COMMAND_LIST_DISPATCH_ROOT_SIGNATURE_NOT_SET = 0x3b8,
    D3D12_MESSAGE_ID_COMMAND_LIST_DISPATCH_ROOT_SIGNATURE_MISMATCH = 0x3b9,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_ZERO_BARRIERS = 0x3ba,
    D3D12_MESSAGE_ID_BEGIN_END_EVENT_MISMATCH = 0x3bb,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_POSSIBLE_BEFORE_AFTER_MISMATCH = 0x3bc,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISMATCHING_BEGIN_END = 0x3bd,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_INVALID_RESOURCE = 0x3be,
    D3D12_MESSAGE_ID_USE_OF_ZERO_REFCOUNT_OBJECT = 0x3bf,
    D3D12_MESSAGE_ID_OBJECT_EVICTED_WHILE_STILL_IN_USE = 0x3c0,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_ROOT_DESCRIPTOR_ACCESS_OUT_OF_BOUNDS = 0x3c1,
    D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_INVALIDLIBRARYBLOB = 0x3c2,
    D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_DRIVERVERSIONMISMATCH = 0x3c3,
    D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_ADAPTERVERSIONMISMATCH = 0x3c4,
    D3D12_MESSAGE_ID_CREATEPIPELINELIBRARY_UNSUPPORTED = 0x3c5,
    D3D12_MESSAGE_ID_CREATE_PIPELINELIBRARY = 0x3c6,
    D3D12_MESSAGE_ID_LIVE_PIPELINELIBRARY = 0x3c7,
    D3D12_MESSAGE_ID_DESTROY_PIPELINELIBRARY = 0x3c8,
    D3D12_MESSAGE_ID_STOREPIPELINE_NONAME = 0x3c9,
    D3D12_MESSAGE_ID_STOREPIPELINE_DUPLICATENAME = 0x3ca,
    D3D12_MESSAGE_ID_LOADPIPELINE_NAMENOTFOUND = 0x3cb,
    D3D12_MESSAGE_ID_LOADPIPELINE_INVALIDDESC = 0x3cc,
    D3D12_MESSAGE_ID_PIPELINELIBRARY_SERIALIZE_NOTENOUGHMEMORY = 0x3cd,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_PS_OUTPUT_RT_OUTPUT_MISMATCH = 0x3ce,
    D3D12_MESSAGE_ID_SETEVENTONMULTIPLEFENCECOMPLETION_INVALIDFLAGS = 0x3cf,
    D3D12_MESSAGE_ID_CREATE_QUEUE_VIDEO_NOT_SUPPORTED = 0x3d0,
    D3D12_MESSAGE_ID_CREATE_COMMAND_ALLOCATOR_VIDEO_NOT_SUPPORTED = 0x3d1,
    D3D12_MESSAGE_ID_CREATEQUERY_HEAP_VIDEO_DECODE_STATISTICS_NOT_SUPPORTED = 0x3d2,
    D3D12_MESSAGE_ID_CREATE_VIDEODECODECOMMANDLIST = 0x3d3,
    D3D12_MESSAGE_ID_CREATE_VIDEODECODER = 0x3d4,
    D3D12_MESSAGE_ID_CREATE_VIDEODECODESTREAM = 0x3d5,
    D3D12_MESSAGE_ID_LIVE_VIDEODECODECOMMANDLIST = 0x3d6,
    D3D12_MESSAGE_ID_LIVE_VIDEODECODER = 0x3d7,
    D3D12_MESSAGE_ID_LIVE_VIDEODECODESTREAM = 0x3d8,
    D3D12_MESSAGE_ID_DESTROY_VIDEODECODECOMMANDLIST = 0x3d9,
    D3D12_MESSAGE_ID_DESTROY_VIDEODECODER = 0x3da,
    D3D12_MESSAGE_ID_DESTROY_VIDEODECODESTREAM = 0x3db,
    D3D12_MESSAGE_ID_DECODE_FRAME_INVALID_PARAMETERS = 0x3dc,
    D3D12_MESSAGE_ID_DEPRECATED_API = 0x3dd,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_MISMATCHING_COMMAND_LIST_TYPE = 0x3de,
    D3D12_MESSAGE_ID_COMMAND_LIST_DESCRIPTOR_TABLE_NOT_SET = 0x3df,
    D3D12_MESSAGE_ID_COMMAND_LIST_ROOT_CONSTANT_BUFFER_VIEW_NOT_SET = 0x3e0,
    D3D12_MESSAGE_ID_COMMAND_LIST_ROOT_SHADER_RESOURCE_VIEW_NOT_SET = 0x3e1,
    D3D12_MESSAGE_ID_COMMAND_LIST_ROOT_UNORDERED_ACCESS_VIEW_NOT_SET = 0x3e2,
    D3D12_MESSAGE_ID_DISCARD_INVALID_SUBRESOURCE_RANGE = 0x3e3,
    D3D12_MESSAGE_ID_DISCARD_ONE_SUBRESOURCE_FOR_MIPS_WITH_RECTS = 0x3e4,
    D3D12_MESSAGE_ID_DISCARD_NO_RECTS_FOR_NON_TEXTURE2D = 0x3e5,
    D3D12_MESSAGE_ID_COPY_ON_SAME_SUBRESOURCE = 0x3e6,
    D3D12_MESSAGE_ID_SETRESIDENCYPRIORITY_INVALID_PAGEABLE = 0x3e7,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_UNSUPPORTED = 0x3e8,
    D3D12_MESSAGE_ID_STATIC_DESCRIPTOR_INVALID_DESCRIPTOR_CHANGE = 0x3e9,
    D3D12_MESSAGE_ID_DATA_STATIC_DESCRIPTOR_INVALID_DATA_CHANGE = 0x3ea,
    D3D12_MESSAGE_ID_DATA_STATIC_WHILE_SET_AT_EXECUTE_DESCRIPTOR_INVALID_DATA_CHANGE = 0x3eb,
    D3D12_MESSAGE_ID_EXECUTE_BUNDLE_STATIC_DESCRIPTOR_DATA_STATIC_NOT_SET = 0x3ec,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_RESOURCE_ACCESS_OUT_OF_BOUNDS = 0x3ed,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_SAMPLER_MODE_MISMATCH = 0x3ee,
    D3D12_MESSAGE_ID_CREATE_FENCE_INVALID_FLAGS = 0x3ef,
    D3D12_MESSAGE_ID_RESOURCE_BARRIER_DUPLICATE_SUBRESOURCE_TRANSITIONS = 0x3f0,
    D3D12_MESSAGE_ID_SETRESIDENCYPRIORITY_INVALID_PRIORITY = 0x3f1,
    D3D12_MESSAGE_ID_CREATE_DESCRIPTOR_HEAP_LARGE_NUM_DESCRIPTORS = 0x3f5,
    D3D12_MESSAGE_ID_BEGIN_EVENT = 0x3f6,
    D3D12_MESSAGE_ID_END_EVENT = 0x3f7,
    D3D12_MESSAGE_ID_CREATEDEVICE_DEBUG_LAYER_STARTUP_OPTIONS = 0x3f8,
    D3D12_MESSAGE_ID_CREATEDEPTHSTENCILSTATE_DEPTHBOUNDSTEST_UNSUPPORTED = 0x3f9,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_DUPLICATE_SUBOBJECT = 0x3fa,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_UNKNOWN_SUBOBJECT = 0x3fb,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_ZERO_SIZE_STREAM = 0x3fc,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_INVALID_STREAM = 0x3fd,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_CANNOT_DEDUCE_TYPE = 0x3fe,
    D3D12_MESSAGE_ID_COMMAND_LIST_STATIC_DESCRIPTOR_RESOURCE_DIMENSION_MISMATCH = 0x3ff,
    D3D12_MESSAGE_ID_CREATE_COMMAND_QUEUE_INSUFFICIENT_PRIVILEGE_FOR_GLOBAL_REALTIME = 0x400,
    D3D12_MESSAGE_ID_CREATE_COMMAND_QUEUE_INSUFFICIENT_HARDWARE_SUPPORT_FOR_GLOBAL_REALTIME = 0x401,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_ARCHITECTURE = 0x402,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_DST = 0x403,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DST_RESOURCE_DIMENSION = 0x404,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_DST_RANGE_OUT_OF_BOUNDS = 0x405,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_SRC = 0x406,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_SRC_RESOURCE_DIMENSION = 0x407,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_SRC_RANGE_OUT_OF_BOUNDS = 0x408,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_OFFSET_ALIGNMENT = 0x409,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_DEPENDENT_RESOURCES = 0x40a,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_NULL_DEPENDENT_SUBRESOURCE_RANGES = 0x40b,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DEPENDENT_RESOURCE = 0x40c,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DEPENDENT_SUBRESOURCE_RANGE = 0x40d,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_DEPENDENT_SUBRESOURCE_OUT_OF_BOUNDS = 0x40e,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_DEPENDENT_RANGE_OUT_OF_BOUNDS = 0x40f,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_ZERO_DEPENDENCIES = 0x410,
    D3D12_MESSAGE_ID_DEVICE_CREATE_SHARED_HANDLE_INVALIDARG = 0x411,
    D3D12_MESSAGE_ID_DESCRIPTOR_HANDLE_WITH_INVALID_RESOURCE = 0x412,
    D3D12_MESSAGE_ID_SETDEPTHBOUNDS_INVALIDARGS = 0x413,
    D3D12_MESSAGE_ID_GPU_BASED_VALIDATION_RESOURCE_STATE_IMPRECISE = 0x414,
    D3D12_MESSAGE_ID_COMMAND_LIST_PIPELINE_STATE_NOT_SET = 0x415,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_SHADER_MODEL_MISMATCH = 0x416,
    D3D12_MESSAGE_ID_OBJECT_ACCESSED_WHILE_STILL_IN_USE = 0x417,
    D3D12_MESSAGE_ID_PROGRAMMABLE_MSAA_UNSUPPORTED = 0x418,
    D3D12_MESSAGE_ID_SETSAMPLEPOSITIONS_INVALIDARGS = 0x419,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCEREGION_INVALID_RECT = 0x41a,
    D3D12_MESSAGE_ID_CREATE_VIDEODECODECOMMANDQUEUE = 0x41b,
    D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSCOMMANDLIST = 0x41c,
    D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSCOMMANDQUEUE = 0x41d,
    D3D12_MESSAGE_ID_LIVE_VIDEODECODECOMMANDQUEUE = 0x41e,
    D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSCOMMANDLIST = 0x41f,
    D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSCOMMANDQUEUE = 0x420,
    D3D12_MESSAGE_ID_DESTROY_VIDEODECODECOMMANDQUEUE = 0x421,
    D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSCOMMANDLIST = 0x422,
    D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSCOMMANDQUEUE = 0x423,
    D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSOR = 0x424,
    D3D12_MESSAGE_ID_CREATE_VIDEOPROCESSSTREAM = 0x425,
    D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSOR = 0x426,
    D3D12_MESSAGE_ID_LIVE_VIDEOPROCESSSTREAM = 0x427,
    D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSOR = 0x428,
    D3D12_MESSAGE_ID_DESTROY_VIDEOPROCESSSTREAM = 0x429,
    D3D12_MESSAGE_ID_PROCESS_FRAME_INVALID_PARAMETERS = 0x42a,
    D3D12_MESSAGE_ID_COPY_INVALIDLAYOUT = 0x42b,
    D3D12_MESSAGE_ID_CREATE_CRYPTO_SESSION = 0x42c,
    D3D12_MESSAGE_ID_CREATE_CRYPTO_SESSION_POLICY = 0x42d,
    D3D12_MESSAGE_ID_CREATE_PROTECTED_RESOURCE_SESSION = 0x42e,
    D3D12_MESSAGE_ID_LIVE_CRYPTO_SESSION = 0x42f,
    D3D12_MESSAGE_ID_LIVE_CRYPTO_SESSION_POLICY = 0x430,
    D3D12_MESSAGE_ID_LIVE_PROTECTED_RESOURCE_SESSION = 0x431,
    D3D12_MESSAGE_ID_DESTROY_CRYPTO_SESSION = 0x432,
    D3D12_MESSAGE_ID_DESTROY_CRYPTO_SESSION_POLICY = 0x433,
    D3D12_MESSAGE_ID_DESTROY_PROTECTED_RESOURCE_SESSION = 0x434,
    D3D12_MESSAGE_ID_PROTECTED_RESOURCE_SESSION_UNSUPPORTED = 0x435,
    D3D12_MESSAGE_ID_FENCE_INVALIDOPERATION = 0x436,
    D3D12_MESSAGE_ID_CREATEQUERY_HEAP_COPY_QUEUE_TIMESTAMPS_NOT_SUPPORTED = 0x437,
    D3D12_MESSAGE_ID_SAMPLEPOSITIONS_MISMATCH_DEFERRED = 0x438,
    D3D12_MESSAGE_ID_SAMPLEPOSITIONS_MISMATCH_RECORDTIME_ASSUMEDFROMFIRSTUSE = 0x439,
    D3D12_MESSAGE_ID_SAMPLEPOSITIONS_MISMATCH_RECORDTIME_ASSUMEDFROMCLEAR = 0x43a,
    D3D12_MESSAGE_ID_CREATE_VIDEODECODERHEAP = 0x43b,
    D3D12_MESSAGE_ID_LIVE_VIDEODECODERHEAP = 0x43c,
    D3D12_MESSAGE_ID_DESTROY_VIDEODECODERHEAP = 0x43d,
    D3D12_MESSAGE_ID_OPENEXISTINGHEAP_INVALIDARG_RETURN = 0x43e,
    D3D12_MESSAGE_ID_OPENEXISTINGHEAP_OUTOFMEMORY_RETURN = 0x43f,
    D3D12_MESSAGE_ID_OPENEXISTINGHEAP_INVALIDADDRESS = 0x440,
    D3D12_MESSAGE_ID_OPENEXISTINGHEAP_INVALIDHANDLE = 0x441,
    D3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_INVALID_DEST = 0x442,
    D3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_INVALID_MODE = 0x443,
    D3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_INVALID_ALIGNMENT = 0x444,
    D3D12_MESSAGE_ID_WRITEBUFFERIMMEDIATE_NOT_SUPPORTED = 0x445,
    D3D12_MESSAGE_ID_SETVIEWINSTANCEMASK_INVALIDARGS = 0x446,
    D3D12_MESSAGE_ID_VIEW_INSTANCING_UNSUPPORTED = 0x447,
    D3D12_MESSAGE_ID_VIEW_INSTANCING_INVALIDARGS = 0x448,
    D3D12_MESSAGE_ID_COPYTEXTUREREGION_MISMATCH_DECODE_REFERENCE_ONLY_FLAG = 0x449,
    D3D12_MESSAGE_ID_COPYRESOURCE_MISMATCH_DECODE_REFERENCE_ONLY_FLAG = 0x44a,
    D3D12_MESSAGE_ID_CREATE_VIDEO_DECODE_HEAP_CAPS_FAILURE = 0x44b,
    D3D12_MESSAGE_ID_CREATE_VIDEO_DECODE_HEAP_CAPS_UNSUPPORTED = 0x44c,
    D3D12_MESSAGE_ID_VIDEO_DECODE_SUPPORT_INVALID_INPUT = 0x44d,
    D3D12_MESSAGE_ID_CREATE_VIDEO_DECODER_UNSUPPORTED = 0x44e,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_METADATA_ERROR = 0x44f,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_VIEW_INSTANCING_VERTEX_SIZE_EXCEEDED = 0x450,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_RUNTIME_INTERNAL_ERROR = 0x451,
    D3D12_MESSAGE_ID_NO_VIDEO_API_SUPPORT = 0x452,
    D3D12_MESSAGE_ID_VIDEO_PROCESS_SUPPORT_INVALID_INPUT = 0x453,
    D3D12_MESSAGE_ID_CREATE_VIDEO_PROCESSOR_CAPS_FAILURE = 0x454,
    D3D12_MESSAGE_ID_VIDEO_PROCESS_SUPPORT_UNSUPPORTED_FORMAT = 0x455,
    D3D12_MESSAGE_ID_VIDEO_DECODE_FRAME_INVALID_ARGUMENT = 0x456,
    D3D12_MESSAGE_ID_ENQUEUE_MAKE_RESIDENT_INVALID_FLAGS = 0x457,
    D3D12_MESSAGE_ID_OPENEXISTINGHEAP_UNSUPPORTED = 0x458,
    D3D12_MESSAGE_ID_VIDEO_PROCESS_FRAMES_INVALID_ARGUMENT = 0x459,
    D3D12_MESSAGE_ID_VIDEO_DECODE_SUPPORT_UNSUPPORTED = 0x45a,
    D3D12_MESSAGE_ID_CREATE_COMMANDRECORDER = 0x45b,
    D3D12_MESSAGE_ID_LIVE_COMMANDRECORDER = 0x45c,
    D3D12_MESSAGE_ID_DESTROY_COMMANDRECORDER = 0x45d,
    D3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_VIDEO_NOT_SUPPORTED = 0x45e,
    D3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_INVALID_SUPPORT_FLAGS = 0x45f,
    D3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_INVALID_FLAGS = 0x460,
    D3D12_MESSAGE_ID_CREATE_COMMAND_RECORDER_MORE_RECORDERS_THAN_LOGICAL_PROCESSORS = 0x461,
    D3D12_MESSAGE_ID_CREATE_COMMANDPOOL = 0x462,
    D3D12_MESSAGE_ID_LIVE_COMMANDPOOL = 0x463,
    D3D12_MESSAGE_ID_DESTROY_COMMANDPOOL = 0x464,
    D3D12_MESSAGE_ID_CREATE_COMMAND_POOL_INVALID_FLAGS = 0x465,
    D3D12_MESSAGE_ID_CREATE_COMMAND_LIST_VIDEO_NOT_SUPPORTED = 0x466,
    D3D12_MESSAGE_ID_COMMAND_RECORDER_SUPPORT_FLAGS_MISMATCH = 0x467,
    D3D12_MESSAGE_ID_COMMAND_RECORDER_CONTENTION = 0x468,
    D3D12_MESSAGE_ID_COMMAND_RECORDER_USAGE_WITH_CREATECOMMANDLIST_COMMAND_LIST = 0x469,
    D3D12_MESSAGE_ID_COMMAND_ALLOCATOR_USAGE_WITH_CREATECOMMANDLIST1_COMMAND_LIST = 0x46a,
    D3D12_MESSAGE_ID_CANNOT_EXECUTE_EMPTY_COMMAND_LIST = 0x46b,
    D3D12_MESSAGE_ID_CANNOT_RESET_COMMAND_POOL_WITH_OPEN_COMMAND_LISTS = 0x46c,
    D3D12_MESSAGE_ID_CANNOT_USE_COMMAND_RECORDER_WITHOUT_CURRENT_TARGET = 0x46d,
    D3D12_MESSAGE_ID_CANNOT_CHANGE_COMMAND_RECORDER_TARGET_WHILE_RECORDING = 0x46e,
    D3D12_MESSAGE_ID_COMMAND_POOL_SYNC = 0x46f,
    D3D12_MESSAGE_ID_EVICT_UNDERFLOW = 0x470,
    D3D12_MESSAGE_ID_CREATE_META_COMMAND = 0x471,
    D3D12_MESSAGE_ID_LIVE_META_COMMAND = 0x472,
    D3D12_MESSAGE_ID_DESTROY_META_COMMAND = 0x473,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_INVALID_DST_RESOURCE = 0x474,
    D3D12_MESSAGE_ID_COPYBUFFERREGION_INVALID_SRC_RESOURCE = 0x475,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_DST_RESOURCE = 0x476,
    D3D12_MESSAGE_ID_ATOMICCOPYBUFFER_INVALID_SRC_RESOURCE = 0x477,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_NULL_BUFFER = 0x478,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_NULL_RESOURCE_DESC = 0x479,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_UNSUPPORTED = 0x47a,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_BUFFER_DIMENSION = 0x47b,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_BUFFER_FLAGS = 0x47c,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_BUFFER_OFFSET = 0x47d,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_RESOURCE_DIMENSION = 0x47e,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_INVALID_RESOURCE_FLAGS = 0x47f,
    D3D12_MESSAGE_ID_CREATEPLACEDRESOURCEONBUFFER_OUTOFMEMORY_RETURN = 0x480,
    D3D12_MESSAGE_ID_CANNOT_CREATE_GRAPHICS_AND_VIDEO_COMMAND_RECORDER = 0x481,
    D3D12_MESSAGE_ID_UPDATETILEMAPPINGS_POSSIBLY_MISMATCHING_PROPERTIES = 0x482,
    D3D12_MESSAGE_ID_CREATE_COMMAND_LIST_INVALID_COMMAND_LIST_TYPE = 0x483,
    D3D12_MESSAGE_ID_CLEARUNORDEREDACCESSVIEW_INCOMPATIBLE_WITH_STRUCTURED_BUFFERS = 0x484,
    D3D12_MESSAGE_ID_COMPUTE_ONLY_DEVICE_OPERATION_UNSUPPORTED = 0x485,
    D3D12_MESSAGE_ID_BUILD_RAYTRACING_ACCELERATION_STRUCTURE_INVALID = 0x486,
    D3D12_MESSAGE_ID_EMIT_RAYTRACING_ACCELERATION_STRUCTURE_POSTBUILD_INFO_INVALID = 0x487,
    D3D12_MESSAGE_ID_COPY_RAYTRACING_ACCELERATION_STRUCTURE_INVALID = 0x488,
    D3D12_MESSAGE_ID_DISPATCH_RAYS_INVALID = 0x489,
    D3D12_MESSAGE_ID_GET_RAYTRACING_ACCELERATION_STRUCTURE_PREBUILD_INFO_INVALID = 0x48a,
    D3D12_MESSAGE_ID_CREATE_LIFETIMETRACKER = 0x48b,
    D3D12_MESSAGE_ID_LIVE_LIFETIMETRACKER = 0x48c,
    D3D12_MESSAGE_ID_DESTROY_LIFETIMETRACKER = 0x48d,
    D3D12_MESSAGE_ID_DESTROYOWNEDOBJECT_OBJECTNOTOWNED = 0x48e,
    D3D12_MESSAGE_ID_CREATE_TRACKEDWORKLOAD = 0x48f,
    D3D12_MESSAGE_ID_LIVE_TRACKEDWORKLOAD = 0x490,
    D3D12_MESSAGE_ID_DESTROY_TRACKEDWORKLOAD = 0x491,
    D3D12_MESSAGE_ID_RENDER_PASS_ERROR = 0x492,
    D3D12_MESSAGE_ID_META_COMMAND_ID_INVALID = 0x493,
    D3D12_MESSAGE_ID_META_COMMAND_UNSUPPORTED_PARAMS = 0x494,
    D3D12_MESSAGE_ID_META_COMMAND_FAILED_ENUMERATION = 0x495,
    D3D12_MESSAGE_ID_META_COMMAND_PARAMETER_SIZE_MISMATCH = 0x496,
    D3D12_MESSAGE_ID_UNINITIALIZED_META_COMMAND = 0x497,
    D3D12_MESSAGE_ID_META_COMMAND_INVALID_GPU_VIRTUAL_ADDRESS = 0x498,
    D3D12_MESSAGE_ID_CREATE_VIDEOENCODECOMMANDLIST = 0x499,
    D3D12_MESSAGE_ID_LIVE_VIDEOENCODECOMMANDLIST = 0x49a,
    D3D12_MESSAGE_ID_DESTROY_VIDEOENCODECOMMANDLIST = 0x49b,
    D3D12_MESSAGE_ID_CREATE_VIDEOENCODECOMMANDQUEUE = 0x49c,
    D3D12_MESSAGE_ID_LIVE_VIDEOENCODECOMMANDQUEUE = 0x49d,
    D3D12_MESSAGE_ID_DESTROY_VIDEOENCODECOMMANDQUEUE = 0x49e,
    D3D12_MESSAGE_ID_CREATE_VIDEOMOTIONESTIMATOR = 0x49f,
    D3D12_MESSAGE_ID_LIVE_VIDEOMOTIONESTIMATOR = 0x4a0,
    D3D12_MESSAGE_ID_DESTROY_VIDEOMOTIONESTIMATOR = 0x4a1,
    D3D12_MESSAGE_ID_CREATE_VIDEOMOTIONVECTORHEAP = 0x4a2,
    D3D12_MESSAGE_ID_LIVE_VIDEOMOTIONVECTORHEAP = 0x4a3,
    D3D12_MESSAGE_ID_DESTROY_VIDEOMOTIONVECTORHEAP = 0x4a4,
    D3D12_MESSAGE_ID_MULTIPLE_TRACKED_WORKLOADS = 0x4a5,
    D3D12_MESSAGE_ID_MULTIPLE_TRACKED_WORKLOAD_PAIRS = 0x4a6,
    D3D12_MESSAGE_ID_OUT_OF_ORDER_TRACKED_WORKLOAD_PAIR = 0x4a7,
    D3D12_MESSAGE_ID_CANNOT_ADD_TRACKED_WORKLOAD = 0x4a8,
    D3D12_MESSAGE_ID_INCOMPLETE_TRACKED_WORKLOAD_PAIR = 0x4a9,
    D3D12_MESSAGE_ID_CREATE_STATE_OBJECT_ERROR = 0x4aa,
    D3D12_MESSAGE_ID_GET_SHADER_IDENTIFIER_ERROR = 0x4ab,
    D3D12_MESSAGE_ID_GET_SHADER_STACK_SIZE_ERROR = 0x4ac,
    D3D12_MESSAGE_ID_GET_PIPELINE_STACK_SIZE_ERROR = 0x4ad,
    D3D12_MESSAGE_ID_SET_PIPELINE_STACK_SIZE_ERROR = 0x4ae,
    D3D12_MESSAGE_ID_GET_SHADER_IDENTIFIER_SIZE_INVALID = 0x4af,
    D3D12_MESSAGE_ID_CHECK_DRIVER_MATCHING_IDENTIFIER_INVALID = 0x4b0,
    D3D12_MESSAGE_ID_CHECK_DRIVER_MATCHING_IDENTIFIER_DRIVER_REPORTED_ISSUE = 0x4b1,
    D3D12_MESSAGE_ID_RENDER_PASS_INVALID_RESOURCE_BARRIER = 0x4b2,
    D3D12_MESSAGE_ID_RENDER_PASS_DISALLOWED_API_CALLED = 0x4b3,
    D3D12_MESSAGE_ID_RENDER_PASS_CANNOT_NEST_RENDER_PASSES = 0x4b4,
    D3D12_MESSAGE_ID_RENDER_PASS_CANNOT_END_WITHOUT_BEGIN = 0x4b5,
    D3D12_MESSAGE_ID_RENDER_PASS_CANNOT_CLOSE_COMMAND_LIST = 0x4b6,
    D3D12_MESSAGE_ID_RENDER_PASS_GPU_WORK_WHILE_SUSPENDED = 0x4b7,
    D3D12_MESSAGE_ID_RENDER_PASS_MISMATCHING_SUSPEND_RESUME = 0x4b8,
    D3D12_MESSAGE_ID_RENDER_PASS_NO_PRIOR_SUSPEND_WITHIN_EXECUTECOMMANDLISTS = 0x4b9,
    D3D12_MESSAGE_ID_RENDER_PASS_NO_SUBSEQUENT_RESUME_WITHIN_EXECUTECOMMANDLISTS = 0x4ba,
    D3D12_MESSAGE_ID_TRACKED_WORKLOAD_COMMAND_QUEUE_MISMATCH = 0x4bb,
    D3D12_MESSAGE_ID_TRACKED_WORKLOAD_NOT_SUPPORTED = 0x4bc,
    D3D12_MESSAGE_ID_RENDER_PASS_MISMATCHING_NO_ACCESS = 0x4bd,
    D3D12_MESSAGE_ID_RENDER_PASS_UNSUPPORTED_RESOLVE = 0x4be,
    D3D12_MESSAGE_ID_CLEARUNORDEREDACCESSVIEW_INVALID_RESOURCE_PTR = 0x4bf,
    D3D12_MESSAGE_ID_WINDOWS7_FENCE_OUTOFORDER_SIGNAL = 0x4c0,
    D3D12_MESSAGE_ID_WINDOWS7_FENCE_OUTOFORDER_WAIT = 0x4c1,
    D3D12_MESSAGE_ID_VIDEO_CREATE_MOTION_ESTIMATOR_INVALID_ARGUMENT = 0x4c2,
    D3D12_MESSAGE_ID_VIDEO_CREATE_MOTION_VECTOR_HEAP_INVALID_ARGUMENT = 0x4c3,
    D3D12_MESSAGE_ID_ESTIMATE_MOTION_INVALID_ARGUMENT = 0x4c4,
    D3D12_MESSAGE_ID_RESOLVE_MOTION_VECTOR_HEAP_INVALID_ARGUMENT = 0x4c5,
    D3D12_MESSAGE_ID_GETGPUVIRTUALADDRESS_INVALID_HEAP_TYPE = 0x4c6,
    D3D12_MESSAGE_ID_SET_BACKGROUND_PROCESSING_MODE_INVALID_ARGUMENT = 0x4c7,
    D3D12_MESSAGE_ID_CREATE_COMMAND_LIST_INVALID_COMMAND_LIST_TYPE_FOR_FEATURE_LEVEL = 0x4c8,
    D3D12_MESSAGE_ID_CREATE_VIDEOEXTENSIONCOMMAND = 0x4c9,
    D3D12_MESSAGE_ID_LIVE_VIDEOEXTENSIONCOMMAND = 0x4ca,
    D3D12_MESSAGE_ID_DESTROY_VIDEOEXTENSIONCOMMAND = 0x4cb,
    D3D12_MESSAGE_ID_INVALID_VIDEO_EXTENSION_COMMAND_ID = 0x4cc,
    D3D12_MESSAGE_ID_VIDEO_EXTENSION_COMMAND_INVALID_ARGUMENT = 0x4cd,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_NOT_UNIQUE_IN_DXIL_LIBRARY = 0x4ce,
    D3D12_MESSAGE_ID_VARIABLE_SHADING_RATE_NOT_ALLOWED_WITH_TIR = 0x4cf,
    D3D12_MESSAGE_ID_GEOMETRY_SHADER_OUTPUTTING_BOTH_VIEWPORT_ARRAY_INDEX_AND_SHADING_RATE_NOT_SUPPORTED_ON_DEVICE = 0x4d0,
    D3D12_MESSAGE_ID_RSSETSHADING_RATE_INVALID_SHADING_RATE = 0x4d1,
    D3D12_MESSAGE_ID_RSSETSHADING_RATE_SHADING_RATE_NOT_PERMITTED_BY_CAP = 0x4d2,
    D3D12_MESSAGE_ID_RSSETSHADING_RATE_INVALID_COMBINER = 0x4d3,
    D3D12_MESSAGE_ID_RSSETSHADINGRATEIMAGE_REQUIRES_TIER_2 = 0x4d4,
    D3D12_MESSAGE_ID_RSSETSHADINGRATE_REQUIRES_TIER_1 = 0x4d5,
    D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_FORMAT = 0x4d6,
    D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_ARRAY_SIZE = 0x4d7,
    D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_MIP_LEVEL = 0x4d8,
    D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_SAMPLE_COUNT = 0x4d9,
    D3D12_MESSAGE_ID_SHADING_RATE_IMAGE_INCORRECT_SAMPLE_QUALITY = 0x4da,
    D3D12_MESSAGE_ID_NON_RETAIL_SHADER_MODEL_WONT_VALIDATE = 0x4db,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_AS_ROOT_SIGNATURE_MISMATCH = 0x4dc,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MS_ROOT_SIGNATURE_MISMATCH = 0x4dd,
    D3D12_MESSAGE_ID_ADD_TO_STATE_OBJECT_ERROR = 0x4de,
    D3D12_MESSAGE_ID_CREATE_PROTECTED_RESOURCE_SESSION_INVALID_ARGUMENT = 0x4df,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MS_PSO_DESC_MISMATCH = 0x4e0,
    D3D12_MESSAGE_ID_CREATEPIPELINESTATE_MS_INCOMPLETE_TYPE = 0x4e1,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_AS_NOT_MS_MISMATCH = 0x4e2,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_MS_NOT_PS_MISMATCH = 0x4e3,
    D3D12_MESSAGE_ID_NONZERO_SAMPLER_FEEDBACK_MIP_REGION_WITH_INCOMPATIBLE_FORMAT = 0x4e4,
    D3D12_MESSAGE_ID_CREATEGRAPHICSPIPELINESTATE_INPUTLAYOUT_SHADER_MISMATCH = 0x4e5,
    D3D12_MESSAGE_ID_EMPTY_DISPATCH = 0x4e6,
    D3D12_MESSAGE_ID_RESOURCE_FORMAT_REQUIRES_SAMPLER_FEEDBACK_CAPABILITY = 0x4e7,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_MIP_REGION = 0x4e8,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_DIMENSION = 0x4e9,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_SAMPLE_COUNT = 0x4ea,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_SAMPLE_QUALITY = 0x4eb,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_INVALID_LAYOUT = 0x4ec,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_MAP_REQUIRES_UNORDERED_ACCESS_FLAG = 0x4ed,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_CREATE_UAV_NULL_ARGUMENTS = 0x4ee,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_UAV_REQUIRES_SAMPLER_FEEDBACK_CAPABILITY = 0x4ef,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_CREATE_UAV_REQUIRES_FEEDBACK_MAP_FORMAT = 0x4f0,
    D3D12_MESSAGE_ID_CREATEMESHSHADER_INVALIDSHADERBYTECODE = 0x4f1,
    D3D12_MESSAGE_ID_CREATEMESHSHADER_OUTOFMEMORY = 0x4f2,
    D3D12_MESSAGE_ID_CREATEMESHSHADERWITHSTREAMOUTPUT_INVALIDSHADERTYPE = 0x4f3,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_SAMPLER_FEEDBACK_TRANSCODE_INVALID_FORMAT = 0x4f4,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_SAMPLER_FEEDBACK_INVALID_MIP_LEVEL_COUNT = 0x4f5,
    D3D12_MESSAGE_ID_RESOLVESUBRESOURCE_SAMPLER_FEEDBACK_TRANSCODE_ARRAY_SIZE_MISMATCH = 0x4f6,
    D3D12_MESSAGE_ID_SAMPLER_FEEDBACK_CREATE_UAV_MISMATCHING_TARGETED_RESOURCE = 0x4f7,
    D3D12_MESSAGE_ID_CREATEMESHSHADER_OUTPUTEXCEEDSMAXSIZE = 0x4f8,
    D3D12_MESSAGE_ID_CREATEMESHSHADER_GROUPSHAREDEXCEEDSMAXSIZE = 0x4f9,
    D3D12_MESSAGE_ID_VERTEX_SHADER_OUTPUTTING_BOTH_VIEWPORT_ARRAY_INDEX_AND_SHADING_RATE_NOT_SUPPORTED_ON_DEVICE = 0x4fa,
    D3D12_MESSAGE_ID_MESH_SHADER_OUTPUTTING_BOTH_VIEWPORT_ARRAY_INDEX_AND_SHADING_RATE_NOT_SUPPORTED_ON_DEVICE = 0x4fb,
    D3D12_MESSAGE_ID_CREATEMESHSHADER_MISMATCHEDASMSPAYLOADSIZE = 0x4fc,
    D3D12_MESSAGE_ID_CREATE_ROOT_SIGNATURE_UNBOUNDED_STATIC_DESCRIPTORS = 0x4fd,
    D3D12_MESSAGE_ID_CREATEAMPLIFICATIONSHADER_INVALIDSHADERBYTECODE = 0x4fe,
    D3D12_MESSAGE_ID_CREATEAMPLIFICATIONSHADER_OUTOFMEMORY = 0x4ff,
    D3D12_MESSAGE_ID_D3D12_MESSAGES_END = 0x500
} D3D12_MESSAGE_ID;
typedef enum D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE {
    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_NONE = 0x0,
    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_STATE_TRACKING_ONLY = 0x1,
    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_UNGUARDED_VALIDATION = 0x2,
    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE_GUARDED_VALIDATION = 0x3,
    NUM_D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODES = 0x4
} D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE;
typedef enum D3D12_GPU_BASED_VALIDATION_FLAGS {
    D3D12_GPU_BASED_VALIDATION_FLAGS_NONE = 0x0,
    D3D12_GPU_BASED_VALIDATION_FLAGS_DISABLE_STATE_TRACKING = 0x1
} D3D12_GPU_BASED_VALIDATION_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_GPU_BASED_VALIDATION_FLAGS)
typedef enum D3D12_RLDO_FLAGS {
    D3D12_RLDO_NONE = 0x0,
    D3D12_RLDO_SUMMARY = 0x1,
    D3D12_RLDO_DETAIL = 0x2,
    D3D12_RLDO_IGNORE_INTERNAL = 0x4
} D3D12_RLDO_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_RLDO_FLAGS)
typedef enum D3D12_DEBUG_DEVICE_PARAMETER_TYPE {
    D3D12_DEBUG_DEVICE_PARAMETER_FEATURE_FLAGS = 0,
    D3D12_DEBUG_DEVICE_PARAMETER_GPU_BASED_VALIDATION_SETTINGS = 1,
    D3D12_DEBUG_DEVICE_PARAMETER_GPU_SLOWDOWN_PERFORMANCE_FACTOR = 2
} D3D12_DEBUG_DEVICE_PARAMETER_TYPE;
typedef enum D3D12_DEBUG_FEATURE {
    D3D12_DEBUG_FEATURE_NONE = 0x0,
    D3D12_DEBUG_FEATURE_ALLOW_BEHAVIOR_CHANGING_DEBUG_AIDS = 0x1,
    D3D12_DEBUG_FEATURE_CONSERVATIVE_RESOURCE_STATE_TRACKING = 0x2,
    D3D12_DEBUG_FEATURE_DISABLE_VIRTUALIZED_BUNDLES_VALIDATION = 0x4,
    D3D12_DEBUG_FEATURE_EMULATE_WINDOWS7 = 0x8
} D3D12_DEBUG_FEATURE;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_DEBUG_FEATURE)
typedef enum D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS {
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_NONE = 0x0,
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_FRONT_LOAD_CREATE_TRACKING_ONLY_SHADERS = 0x1,
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_FRONT_LOAD_CREATE_UNGUARDED_VALIDATION_SHADERS = 0x2,
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAG_FRONT_LOAD_CREATE_GUARDED_VALIDATION_SHADERS = 0x4,
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS_VALID_MASK = 0x7
} D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS)
typedef enum D3D12_MESSAGE_CALLBACK_FLAGS {
    D3D12_MESSAGE_CALLBACK_FLAG_NONE = 0x0,
    D3D12_MESSAGE_CALLBACK_IGNORE_FILTERS = 0x1
} D3D12_MESSAGE_CALLBACK_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_MESSAGE_CALLBACK_FLAGS)
typedef struct D3D12_DEBUG_DEVICE_GPU_BASED_VALIDATION_SETTINGS {
    UINT MaxMessagesPerCommandList;
    D3D12_GPU_BASED_VALIDATION_SHADER_PATCH_MODE DefaultShaderPatchMode;
    D3D12_GPU_BASED_VALIDATION_PIPELINE_STATE_CREATE_FLAGS PipelineStateCreateFlags;
} D3D12_DEBUG_DEVICE_GPU_BASED_VALIDATION_SETTINGS;
typedef struct D3D12_DEBUG_DEVICE_GPU_SLOWDOWN_PERFORMANCE_FACTOR {
    FLOAT SlowdownFactor;
} D3D12_DEBUG_DEVICE_GPU_SLOWDOWN_PERFORMANCE_FACTOR;
typedef struct D3D12_MESSAGE {
    D3D12_MESSAGE_CATEGORY Category;
    D3D12_MESSAGE_SEVERITY Severity;
    D3D12_MESSAGE_ID ID;
    const char *pDescription;
    SIZE_T DescriptionByteLength;
} D3D12_MESSAGE;
typedef struct D3D12_INFO_QUEUE_FILTER_DESC {
    UINT NumCategories;
    D3D12_MESSAGE_CATEGORY *pCategoryList;
    UINT NumSeverities;
    D3D12_MESSAGE_SEVERITY *pSeverityList;
    UINT NumIDs;
    D3D12_MESSAGE_ID *pIDList;
} D3D12_INFO_QUEUE_FILTER_DESC;
typedef struct D3D12_INFO_QUEUE_FILTER {
    D3D12_INFO_QUEUE_FILTER_DESC AllowList;
    D3D12_INFO_QUEUE_FILTER_DESC DenyList;
} D3D12_INFO_QUEUE_FILTER;
/*****************************************************************************
 * ID3D12Debug interface
 */
#ifndef __ID3D12Debug_INTERFACE_DEFINED__
#define __ID3D12Debug_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12Debug, 0x344488b7, 0x6846, 0x474b, 0xb9,0x89, 0xf0,0x27,0x44,0x82,0x45,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("344488b7-6846-474b-b989-f027448245e0")
ID3D12Debug : public IUnknown
{
    virtual void STDMETHODCALLTYPE EnableDebugLayer(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12Debug, 0x344488b7, 0x6846, 0x474b, 0xb9,0x89, 0xf0,0x27,0x44,0x82,0x45,0xe0)
#endif
#else
typedef struct ID3D12DebugVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12Debug *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12Debug *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12Debug *This);

    /*** ID3D12Debug methods ***/
    void (STDMETHODCALLTYPE *EnableDebugLayer)(
        ID3D12Debug *This);

    END_INTERFACE
} ID3D12DebugVtbl;

interface ID3D12Debug {
    CONST_VTBL ID3D12DebugVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12Debug_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12Debug_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12Debug_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Debug methods ***/
#define ID3D12Debug_EnableDebugLayer(This) (This)->lpVtbl->EnableDebugLayer(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12Debug_QueryInterface(ID3D12Debug* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12Debug_AddRef(ID3D12Debug* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12Debug_Release(ID3D12Debug* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Debug methods ***/
static inline void ID3D12Debug_EnableDebugLayer(ID3D12Debug* This) {
    This->lpVtbl->EnableDebugLayer(This);
}
#endif
#endif

#endif


#endif  /* __ID3D12Debug_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12Debug1 interface
 */
#ifndef __ID3D12Debug1_INTERFACE_DEFINED__
#define __ID3D12Debug1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12Debug1, 0xaffaa4ca, 0x63fe, 0x4d8e, 0xb8,0xad, 0x15,0x90,0x00,0xaf,0x43,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("affaa4ca-63fe-4d8e-b8ad-159000af4304")
ID3D12Debug1 : public IUnknown
{
    virtual void STDMETHODCALLTYPE EnableDebugLayer(
        ) = 0;

    virtual void STDMETHODCALLTYPE SetEnableGPUBasedValidation(
        WINBOOL enable) = 0;

    virtual void STDMETHODCALLTYPE SetEnableSynchronizedCommandQueueValidation(
        WINBOOL enable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12Debug1, 0xaffaa4ca, 0x63fe, 0x4d8e, 0xb8,0xad, 0x15,0x90,0x00,0xaf,0x43,0x04)
#endif
#else
typedef struct ID3D12Debug1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12Debug1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12Debug1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12Debug1 *This);

    /*** ID3D12Debug1 methods ***/
    void (STDMETHODCALLTYPE *EnableDebugLayer)(
        ID3D12Debug1 *This);

    void (STDMETHODCALLTYPE *SetEnableGPUBasedValidation)(
        ID3D12Debug1 *This,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *SetEnableSynchronizedCommandQueueValidation)(
        ID3D12Debug1 *This,
        WINBOOL enable);

    END_INTERFACE
} ID3D12Debug1Vtbl;

interface ID3D12Debug1 {
    CONST_VTBL ID3D12Debug1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12Debug1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12Debug1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12Debug1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Debug1 methods ***/
#define ID3D12Debug1_EnableDebugLayer(This) (This)->lpVtbl->EnableDebugLayer(This)
#define ID3D12Debug1_SetEnableGPUBasedValidation(This,enable) (This)->lpVtbl->SetEnableGPUBasedValidation(This,enable)
#define ID3D12Debug1_SetEnableSynchronizedCommandQueueValidation(This,enable) (This)->lpVtbl->SetEnableSynchronizedCommandQueueValidation(This,enable)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12Debug1_QueryInterface(ID3D12Debug1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12Debug1_AddRef(ID3D12Debug1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12Debug1_Release(ID3D12Debug1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Debug1 methods ***/
static inline void ID3D12Debug1_EnableDebugLayer(ID3D12Debug1* This) {
    This->lpVtbl->EnableDebugLayer(This);
}
static inline void ID3D12Debug1_SetEnableGPUBasedValidation(ID3D12Debug1* This,WINBOOL enable) {
    This->lpVtbl->SetEnableGPUBasedValidation(This,enable);
}
static inline void ID3D12Debug1_SetEnableSynchronizedCommandQueueValidation(ID3D12Debug1* This,WINBOOL enable) {
    This->lpVtbl->SetEnableSynchronizedCommandQueueValidation(This,enable);
}
#endif
#endif

#endif


#endif  /* __ID3D12Debug1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12Debug2 interface
 */
#ifndef __ID3D12Debug2_INTERFACE_DEFINED__
#define __ID3D12Debug2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12Debug2, 0x93a665c4, 0xa3b2, 0x4e5d, 0xb6,0x92, 0xa2,0x6a,0xe1,0x4e,0x33,0x74);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("93a665c4-a3b2-4e5d-b692-a26ae14e3374")
ID3D12Debug2 : public IUnknown
{
    virtual void STDMETHODCALLTYPE SetGPUBasedValidationFlags(
        D3D12_GPU_BASED_VALIDATION_FLAGS flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12Debug2, 0x93a665c4, 0xa3b2, 0x4e5d, 0xb6,0x92, 0xa2,0x6a,0xe1,0x4e,0x33,0x74)
#endif
#else
typedef struct ID3D12Debug2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12Debug2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12Debug2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12Debug2 *This);

    /*** ID3D12Debug2 methods ***/
    void (STDMETHODCALLTYPE *SetGPUBasedValidationFlags)(
        ID3D12Debug2 *This,
        D3D12_GPU_BASED_VALIDATION_FLAGS flags);

    END_INTERFACE
} ID3D12Debug2Vtbl;

interface ID3D12Debug2 {
    CONST_VTBL ID3D12Debug2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12Debug2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12Debug2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12Debug2_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Debug2 methods ***/
#define ID3D12Debug2_SetGPUBasedValidationFlags(This,flags) (This)->lpVtbl->SetGPUBasedValidationFlags(This,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12Debug2_QueryInterface(ID3D12Debug2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12Debug2_AddRef(ID3D12Debug2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12Debug2_Release(ID3D12Debug2* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Debug2 methods ***/
static inline void ID3D12Debug2_SetGPUBasedValidationFlags(ID3D12Debug2* This,D3D12_GPU_BASED_VALIDATION_FLAGS flags) {
    This->lpVtbl->SetGPUBasedValidationFlags(This,flags);
}
#endif
#endif

#endif


#endif  /* __ID3D12Debug2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12Debug3 interface
 */
#ifndef __ID3D12Debug3_INTERFACE_DEFINED__
#define __ID3D12Debug3_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12Debug3, 0x5cf4e58f, 0xf671, 0x4ff1, 0xa5,0x42, 0x36,0x86,0xe3,0xd1,0x53,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5cf4e58f-f671-4ff1-a542-3686e3d153d1")
ID3D12Debug3 : public ID3D12Debug
{
    virtual void STDMETHODCALLTYPE SetEnableGPUBasedValidation(
        WINBOOL enable) = 0;

    virtual void STDMETHODCALLTYPE SetEnableSynchronizedCommandQueueValidation(
        WINBOOL enable) = 0;

    virtual void STDMETHODCALLTYPE SetGPUBasedValidationFlags(
        D3D12_GPU_BASED_VALIDATION_FLAGS flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12Debug3, 0x5cf4e58f, 0xf671, 0x4ff1, 0xa5,0x42, 0x36,0x86,0xe3,0xd1,0x53,0xd1)
#endif
#else
typedef struct ID3D12Debug3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12Debug3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12Debug3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12Debug3 *This);

    /*** ID3D12Debug methods ***/
    void (STDMETHODCALLTYPE *EnableDebugLayer)(
        ID3D12Debug3 *This);

    /*** ID3D12Debug3 methods ***/
    void (STDMETHODCALLTYPE *SetEnableGPUBasedValidation)(
        ID3D12Debug3 *This,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *SetEnableSynchronizedCommandQueueValidation)(
        ID3D12Debug3 *This,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *SetGPUBasedValidationFlags)(
        ID3D12Debug3 *This,
        D3D12_GPU_BASED_VALIDATION_FLAGS flags);

    END_INTERFACE
} ID3D12Debug3Vtbl;

interface ID3D12Debug3 {
    CONST_VTBL ID3D12Debug3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12Debug3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12Debug3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12Debug3_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Debug methods ***/
#define ID3D12Debug3_EnableDebugLayer(This) (This)->lpVtbl->EnableDebugLayer(This)
/*** ID3D12Debug3 methods ***/
#define ID3D12Debug3_SetEnableGPUBasedValidation(This,enable) (This)->lpVtbl->SetEnableGPUBasedValidation(This,enable)
#define ID3D12Debug3_SetEnableSynchronizedCommandQueueValidation(This,enable) (This)->lpVtbl->SetEnableSynchronizedCommandQueueValidation(This,enable)
#define ID3D12Debug3_SetGPUBasedValidationFlags(This,flags) (This)->lpVtbl->SetGPUBasedValidationFlags(This,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12Debug3_QueryInterface(ID3D12Debug3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12Debug3_AddRef(ID3D12Debug3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12Debug3_Release(ID3D12Debug3* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Debug methods ***/
static inline void ID3D12Debug3_EnableDebugLayer(ID3D12Debug3* This) {
    This->lpVtbl->EnableDebugLayer(This);
}
/*** ID3D12Debug3 methods ***/
static inline void ID3D12Debug3_SetEnableGPUBasedValidation(ID3D12Debug3* This,WINBOOL enable) {
    This->lpVtbl->SetEnableGPUBasedValidation(This,enable);
}
static inline void ID3D12Debug3_SetEnableSynchronizedCommandQueueValidation(ID3D12Debug3* This,WINBOOL enable) {
    This->lpVtbl->SetEnableSynchronizedCommandQueueValidation(This,enable);
}
static inline void ID3D12Debug3_SetGPUBasedValidationFlags(ID3D12Debug3* This,D3D12_GPU_BASED_VALIDATION_FLAGS flags) {
    This->lpVtbl->SetGPUBasedValidationFlags(This,flags);
}
#endif
#endif

#endif


#endif  /* __ID3D12Debug3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12Debug4 interface
 */
#ifndef __ID3D12Debug4_INTERFACE_DEFINED__
#define __ID3D12Debug4_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12Debug4, 0x014b816e, 0x9ec5, 0x4a2f, 0xa8,0x45, 0xff,0xbe,0x44,0x1c,0xe1,0x3a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("014b816e-9ec5-4a2f-a845-ffbe441ce13a")
ID3D12Debug4 : public ID3D12Debug3
{
    virtual void STDMETHODCALLTYPE DisableDebugLayer(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12Debug4, 0x014b816e, 0x9ec5, 0x4a2f, 0xa8,0x45, 0xff,0xbe,0x44,0x1c,0xe1,0x3a)
#endif
#else
typedef struct ID3D12Debug4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12Debug4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12Debug4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12Debug4 *This);

    /*** ID3D12Debug methods ***/
    void (STDMETHODCALLTYPE *EnableDebugLayer)(
        ID3D12Debug4 *This);

    /*** ID3D12Debug3 methods ***/
    void (STDMETHODCALLTYPE *SetEnableGPUBasedValidation)(
        ID3D12Debug4 *This,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *SetEnableSynchronizedCommandQueueValidation)(
        ID3D12Debug4 *This,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *SetGPUBasedValidationFlags)(
        ID3D12Debug4 *This,
        D3D12_GPU_BASED_VALIDATION_FLAGS flags);

    /*** ID3D12Debug4 methods ***/
    void (STDMETHODCALLTYPE *DisableDebugLayer)(
        ID3D12Debug4 *This);

    END_INTERFACE
} ID3D12Debug4Vtbl;

interface ID3D12Debug4 {
    CONST_VTBL ID3D12Debug4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12Debug4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12Debug4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12Debug4_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Debug methods ***/
#define ID3D12Debug4_EnableDebugLayer(This) (This)->lpVtbl->EnableDebugLayer(This)
/*** ID3D12Debug3 methods ***/
#define ID3D12Debug4_SetEnableGPUBasedValidation(This,enable) (This)->lpVtbl->SetEnableGPUBasedValidation(This,enable)
#define ID3D12Debug4_SetEnableSynchronizedCommandQueueValidation(This,enable) (This)->lpVtbl->SetEnableSynchronizedCommandQueueValidation(This,enable)
#define ID3D12Debug4_SetGPUBasedValidationFlags(This,flags) (This)->lpVtbl->SetGPUBasedValidationFlags(This,flags)
/*** ID3D12Debug4 methods ***/
#define ID3D12Debug4_DisableDebugLayer(This) (This)->lpVtbl->DisableDebugLayer(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12Debug4_QueryInterface(ID3D12Debug4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12Debug4_AddRef(ID3D12Debug4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12Debug4_Release(ID3D12Debug4* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Debug methods ***/
static inline void ID3D12Debug4_EnableDebugLayer(ID3D12Debug4* This) {
    This->lpVtbl->EnableDebugLayer(This);
}
/*** ID3D12Debug3 methods ***/
static inline void ID3D12Debug4_SetEnableGPUBasedValidation(ID3D12Debug4* This,WINBOOL enable) {
    This->lpVtbl->SetEnableGPUBasedValidation(This,enable);
}
static inline void ID3D12Debug4_SetEnableSynchronizedCommandQueueValidation(ID3D12Debug4* This,WINBOOL enable) {
    This->lpVtbl->SetEnableSynchronizedCommandQueueValidation(This,enable);
}
static inline void ID3D12Debug4_SetGPUBasedValidationFlags(ID3D12Debug4* This,D3D12_GPU_BASED_VALIDATION_FLAGS flags) {
    This->lpVtbl->SetGPUBasedValidationFlags(This,flags);
}
/*** ID3D12Debug4 methods ***/
static inline void ID3D12Debug4_DisableDebugLayer(ID3D12Debug4* This) {
    This->lpVtbl->DisableDebugLayer(This);
}
#endif
#endif

#endif


#endif  /* __ID3D12Debug4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12Debug5 interface
 */
#ifndef __ID3D12Debug5_INTERFACE_DEFINED__
#define __ID3D12Debug5_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12Debug5, 0x548d6b12, 0x09fa, 0x40e0, 0x90,0x69, 0x5d,0xcd,0x58,0x9a,0x52,0xc9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("548d6b12-09fa-40e0-9069-5dcd589a52c9")
ID3D12Debug5 : public ID3D12Debug4
{
    virtual void STDMETHODCALLTYPE SetEnableAutoName(
        WINBOOL enable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12Debug5, 0x548d6b12, 0x09fa, 0x40e0, 0x90,0x69, 0x5d,0xcd,0x58,0x9a,0x52,0xc9)
#endif
#else
typedef struct ID3D12Debug5Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12Debug5 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12Debug5 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12Debug5 *This);

    /*** ID3D12Debug methods ***/
    void (STDMETHODCALLTYPE *EnableDebugLayer)(
        ID3D12Debug5 *This);

    /*** ID3D12Debug3 methods ***/
    void (STDMETHODCALLTYPE *SetEnableGPUBasedValidation)(
        ID3D12Debug5 *This,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *SetEnableSynchronizedCommandQueueValidation)(
        ID3D12Debug5 *This,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *SetGPUBasedValidationFlags)(
        ID3D12Debug5 *This,
        D3D12_GPU_BASED_VALIDATION_FLAGS flags);

    /*** ID3D12Debug4 methods ***/
    void (STDMETHODCALLTYPE *DisableDebugLayer)(
        ID3D12Debug5 *This);

    /*** ID3D12Debug5 methods ***/
    void (STDMETHODCALLTYPE *SetEnableAutoName)(
        ID3D12Debug5 *This,
        WINBOOL enable);

    END_INTERFACE
} ID3D12Debug5Vtbl;

interface ID3D12Debug5 {
    CONST_VTBL ID3D12Debug5Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12Debug5_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12Debug5_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12Debug5_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Debug methods ***/
#define ID3D12Debug5_EnableDebugLayer(This) (This)->lpVtbl->EnableDebugLayer(This)
/*** ID3D12Debug3 methods ***/
#define ID3D12Debug5_SetEnableGPUBasedValidation(This,enable) (This)->lpVtbl->SetEnableGPUBasedValidation(This,enable)
#define ID3D12Debug5_SetEnableSynchronizedCommandQueueValidation(This,enable) (This)->lpVtbl->SetEnableSynchronizedCommandQueueValidation(This,enable)
#define ID3D12Debug5_SetGPUBasedValidationFlags(This,flags) (This)->lpVtbl->SetGPUBasedValidationFlags(This,flags)
/*** ID3D12Debug4 methods ***/
#define ID3D12Debug5_DisableDebugLayer(This) (This)->lpVtbl->DisableDebugLayer(This)
/*** ID3D12Debug5 methods ***/
#define ID3D12Debug5_SetEnableAutoName(This,enable) (This)->lpVtbl->SetEnableAutoName(This,enable)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12Debug5_QueryInterface(ID3D12Debug5* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12Debug5_AddRef(ID3D12Debug5* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12Debug5_Release(ID3D12Debug5* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Debug methods ***/
static inline void ID3D12Debug5_EnableDebugLayer(ID3D12Debug5* This) {
    This->lpVtbl->EnableDebugLayer(This);
}
/*** ID3D12Debug3 methods ***/
static inline void ID3D12Debug5_SetEnableGPUBasedValidation(ID3D12Debug5* This,WINBOOL enable) {
    This->lpVtbl->SetEnableGPUBasedValidation(This,enable);
}
static inline void ID3D12Debug5_SetEnableSynchronizedCommandQueueValidation(ID3D12Debug5* This,WINBOOL enable) {
    This->lpVtbl->SetEnableSynchronizedCommandQueueValidation(This,enable);
}
static inline void ID3D12Debug5_SetGPUBasedValidationFlags(ID3D12Debug5* This,D3D12_GPU_BASED_VALIDATION_FLAGS flags) {
    This->lpVtbl->SetGPUBasedValidationFlags(This,flags);
}
/*** ID3D12Debug4 methods ***/
static inline void ID3D12Debug5_DisableDebugLayer(ID3D12Debug5* This) {
    This->lpVtbl->DisableDebugLayer(This);
}
/*** ID3D12Debug5 methods ***/
static inline void ID3D12Debug5_SetEnableAutoName(ID3D12Debug5* This,WINBOOL enable) {
    This->lpVtbl->SetEnableAutoName(This,enable);
}
#endif
#endif

#endif


#endif  /* __ID3D12Debug5_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12DebugDevice interface
 */
#ifndef __ID3D12DebugDevice_INTERFACE_DEFINED__
#define __ID3D12DebugDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12DebugDevice, 0x3febd6dd, 0x4973, 0x4787, 0x81,0x94, 0xe4,0x5f,0x9e,0x28,0x92,0x3e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3febd6dd-**************-e45f9e28923e")
ID3D12DebugDevice : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetFeatureMask(
        D3D12_DEBUG_FEATURE mask) = 0;

    virtual D3D12_DEBUG_FEATURE STDMETHODCALLTYPE GetFeatureMask(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReportLiveDeviceObjects(
        D3D12_RLDO_FLAGS flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12DebugDevice, 0x3febd6dd, 0x4973, 0x4787, 0x81,0x94, 0xe4,0x5f,0x9e,0x28,0x92,0x3e)
#endif
#else
typedef struct ID3D12DebugDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12DebugDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12DebugDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12DebugDevice *This);

    /*** ID3D12DebugDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFeatureMask)(
        ID3D12DebugDevice *This,
        D3D12_DEBUG_FEATURE mask);

    D3D12_DEBUG_FEATURE (STDMETHODCALLTYPE *GetFeatureMask)(
        ID3D12DebugDevice *This);

    HRESULT (STDMETHODCALLTYPE *ReportLiveDeviceObjects)(
        ID3D12DebugDevice *This,
        D3D12_RLDO_FLAGS flags);

    END_INTERFACE
} ID3D12DebugDeviceVtbl;

interface ID3D12DebugDevice {
    CONST_VTBL ID3D12DebugDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12DebugDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12DebugDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12DebugDevice_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12DebugDevice methods ***/
#define ID3D12DebugDevice_SetFeatureMask(This,mask) (This)->lpVtbl->SetFeatureMask(This,mask)
#define ID3D12DebugDevice_GetFeatureMask(This) (This)->lpVtbl->GetFeatureMask(This)
#define ID3D12DebugDevice_ReportLiveDeviceObjects(This,flags) (This)->lpVtbl->ReportLiveDeviceObjects(This,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12DebugDevice_QueryInterface(ID3D12DebugDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12DebugDevice_AddRef(ID3D12DebugDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12DebugDevice_Release(ID3D12DebugDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12DebugDevice methods ***/
static inline HRESULT ID3D12DebugDevice_SetFeatureMask(ID3D12DebugDevice* This,D3D12_DEBUG_FEATURE mask) {
    return This->lpVtbl->SetFeatureMask(This,mask);
}
static inline D3D12_DEBUG_FEATURE ID3D12DebugDevice_GetFeatureMask(ID3D12DebugDevice* This) {
    return This->lpVtbl->GetFeatureMask(This);
}
static inline HRESULT ID3D12DebugDevice_ReportLiveDeviceObjects(ID3D12DebugDevice* This,D3D12_RLDO_FLAGS flags) {
    return This->lpVtbl->ReportLiveDeviceObjects(This,flags);
}
#endif
#endif

#endif


#endif  /* __ID3D12DebugDevice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12DebugDevice1 interface
 */
#ifndef __ID3D12DebugDevice1_INTERFACE_DEFINED__
#define __ID3D12DebugDevice1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12DebugDevice1, 0xa9b71770, 0xd099, 0x4a65, 0xa6,0x98, 0x3d,0xee,0x10,0x02,0x0f,0x88);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a9b71770-d099-4a65-a698-3dee10020f88")
ID3D12DebugDevice1 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetDebugParameter(
        D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,
        const void *data,
        UINT size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDebugParameter(
        D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,
        void *data,
        UINT size) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReportLiveDeviceObjects(
        D3D12_RLDO_FLAGS flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12DebugDevice1, 0xa9b71770, 0xd099, 0x4a65, 0xa6,0x98, 0x3d,0xee,0x10,0x02,0x0f,0x88)
#endif
#else
typedef struct ID3D12DebugDevice1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12DebugDevice1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12DebugDevice1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12DebugDevice1 *This);

    /*** ID3D12DebugDevice1 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDebugParameter)(
        ID3D12DebugDevice1 *This,
        D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,
        const void *data,
        UINT size);

    HRESULT (STDMETHODCALLTYPE *GetDebugParameter)(
        ID3D12DebugDevice1 *This,
        D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,
        void *data,
        UINT size);

    HRESULT (STDMETHODCALLTYPE *ReportLiveDeviceObjects)(
        ID3D12DebugDevice1 *This,
        D3D12_RLDO_FLAGS flags);

    END_INTERFACE
} ID3D12DebugDevice1Vtbl;

interface ID3D12DebugDevice1 {
    CONST_VTBL ID3D12DebugDevice1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12DebugDevice1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12DebugDevice1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12DebugDevice1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12DebugDevice1 methods ***/
#define ID3D12DebugDevice1_SetDebugParameter(This,type,data,size) (This)->lpVtbl->SetDebugParameter(This,type,data,size)
#define ID3D12DebugDevice1_GetDebugParameter(This,type,data,size) (This)->lpVtbl->GetDebugParameter(This,type,data,size)
#define ID3D12DebugDevice1_ReportLiveDeviceObjects(This,flags) (This)->lpVtbl->ReportLiveDeviceObjects(This,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12DebugDevice1_QueryInterface(ID3D12DebugDevice1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12DebugDevice1_AddRef(ID3D12DebugDevice1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12DebugDevice1_Release(ID3D12DebugDevice1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12DebugDevice1 methods ***/
static inline HRESULT ID3D12DebugDevice1_SetDebugParameter(ID3D12DebugDevice1* This,D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,const void *data,UINT size) {
    return This->lpVtbl->SetDebugParameter(This,type,data,size);
}
static inline HRESULT ID3D12DebugDevice1_GetDebugParameter(ID3D12DebugDevice1* This,D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,void *data,UINT size) {
    return This->lpVtbl->GetDebugParameter(This,type,data,size);
}
static inline HRESULT ID3D12DebugDevice1_ReportLiveDeviceObjects(ID3D12DebugDevice1* This,D3D12_RLDO_FLAGS flags) {
    return This->lpVtbl->ReportLiveDeviceObjects(This,flags);
}
#endif
#endif

#endif


#endif  /* __ID3D12DebugDevice1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12DebugDevice2 interface
 */
#ifndef __ID3D12DebugDevice2_INTERFACE_DEFINED__
#define __ID3D12DebugDevice2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12DebugDevice2, 0x60eccbc1, 0x378d, 0x4df1, 0x89,0x4c, 0xf8,0xac,0x5c,0xe4,0xd7,0xdd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("60eccbc1-378d-4df1-894c-f8ac5ce4d7dd")
ID3D12DebugDevice2 : public ID3D12DebugDevice
{
    virtual HRESULT STDMETHODCALLTYPE SetDebugParameter(
        D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,
        const void *data,
        UINT size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDebugParameter(
        D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,
        void *data,
        UINT size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12DebugDevice2, 0x60eccbc1, 0x378d, 0x4df1, 0x89,0x4c, 0xf8,0xac,0x5c,0xe4,0xd7,0xdd)
#endif
#else
typedef struct ID3D12DebugDevice2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12DebugDevice2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12DebugDevice2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12DebugDevice2 *This);

    /*** ID3D12DebugDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFeatureMask)(
        ID3D12DebugDevice2 *This,
        D3D12_DEBUG_FEATURE mask);

    D3D12_DEBUG_FEATURE (STDMETHODCALLTYPE *GetFeatureMask)(
        ID3D12DebugDevice2 *This);

    HRESULT (STDMETHODCALLTYPE *ReportLiveDeviceObjects)(
        ID3D12DebugDevice2 *This,
        D3D12_RLDO_FLAGS flags);

    /*** ID3D12DebugDevice2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDebugParameter)(
        ID3D12DebugDevice2 *This,
        D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,
        const void *data,
        UINT size);

    HRESULT (STDMETHODCALLTYPE *GetDebugParameter)(
        ID3D12DebugDevice2 *This,
        D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,
        void *data,
        UINT size);

    END_INTERFACE
} ID3D12DebugDevice2Vtbl;

interface ID3D12DebugDevice2 {
    CONST_VTBL ID3D12DebugDevice2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12DebugDevice2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12DebugDevice2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12DebugDevice2_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12DebugDevice methods ***/
#define ID3D12DebugDevice2_SetFeatureMask(This,mask) (This)->lpVtbl->SetFeatureMask(This,mask)
#define ID3D12DebugDevice2_GetFeatureMask(This) (This)->lpVtbl->GetFeatureMask(This)
#define ID3D12DebugDevice2_ReportLiveDeviceObjects(This,flags) (This)->lpVtbl->ReportLiveDeviceObjects(This,flags)
/*** ID3D12DebugDevice2 methods ***/
#define ID3D12DebugDevice2_SetDebugParameter(This,type,data,size) (This)->lpVtbl->SetDebugParameter(This,type,data,size)
#define ID3D12DebugDevice2_GetDebugParameter(This,type,data,size) (This)->lpVtbl->GetDebugParameter(This,type,data,size)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12DebugDevice2_QueryInterface(ID3D12DebugDevice2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12DebugDevice2_AddRef(ID3D12DebugDevice2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12DebugDevice2_Release(ID3D12DebugDevice2* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12DebugDevice methods ***/
static inline HRESULT ID3D12DebugDevice2_SetFeatureMask(ID3D12DebugDevice2* This,D3D12_DEBUG_FEATURE mask) {
    return This->lpVtbl->SetFeatureMask(This,mask);
}
static inline D3D12_DEBUG_FEATURE ID3D12DebugDevice2_GetFeatureMask(ID3D12DebugDevice2* This) {
    return This->lpVtbl->GetFeatureMask(This);
}
static inline HRESULT ID3D12DebugDevice2_ReportLiveDeviceObjects(ID3D12DebugDevice2* This,D3D12_RLDO_FLAGS flags) {
    return This->lpVtbl->ReportLiveDeviceObjects(This,flags);
}
/*** ID3D12DebugDevice2 methods ***/
static inline HRESULT ID3D12DebugDevice2_SetDebugParameter(ID3D12DebugDevice2* This,D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,const void *data,UINT size) {
    return This->lpVtbl->SetDebugParameter(This,type,data,size);
}
static inline HRESULT ID3D12DebugDevice2_GetDebugParameter(ID3D12DebugDevice2* This,D3D12_DEBUG_DEVICE_PARAMETER_TYPE type,void *data,UINT size) {
    return This->lpVtbl->GetDebugParameter(This,type,data,size);
}
#endif
#endif

#endif


#endif  /* __ID3D12DebugDevice2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12SharingContract interface
 */
#ifndef __ID3D12SharingContract_INTERFACE_DEFINED__
#define __ID3D12SharingContract_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12SharingContract, 0x0adf7d52, 0x929c, 0x4e61, 0xad,0xdb, 0xff,0xed,0x30,0xde,0x66,0xef);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0adf7d52-929c-4e61-addb-ffed30de66ef")
ID3D12SharingContract : public IUnknown
{
    virtual void STDMETHODCALLTYPE Present(
        ID3D12Resource *resource,
        UINT sub_resource,
        HWND window) = 0;

    virtual void STDMETHODCALLTYPE SharedFenceSignal(
        ID3D12Fence *fence,
        UINT64 fence_value) = 0;

    virtual void STDMETHODCALLTYPE BeginCapturableWork(
        REFGUID guid) = 0;

    virtual void STDMETHODCALLTYPE EndCapturableWork(
        REFGUID guid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12SharingContract, 0x0adf7d52, 0x929c, 0x4e61, 0xad,0xdb, 0xff,0xed,0x30,0xde,0x66,0xef)
#endif
#else
typedef struct ID3D12SharingContractVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12SharingContract *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12SharingContract *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12SharingContract *This);

    /*** ID3D12SharingContract methods ***/
    void (STDMETHODCALLTYPE *Present)(
        ID3D12SharingContract *This,
        ID3D12Resource *resource,
        UINT sub_resource,
        HWND window);

    void (STDMETHODCALLTYPE *SharedFenceSignal)(
        ID3D12SharingContract *This,
        ID3D12Fence *fence,
        UINT64 fence_value);

    void (STDMETHODCALLTYPE *BeginCapturableWork)(
        ID3D12SharingContract *This,
        REFGUID guid);

    void (STDMETHODCALLTYPE *EndCapturableWork)(
        ID3D12SharingContract *This,
        REFGUID guid);

    END_INTERFACE
} ID3D12SharingContractVtbl;

interface ID3D12SharingContract {
    CONST_VTBL ID3D12SharingContractVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12SharingContract_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12SharingContract_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12SharingContract_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12SharingContract methods ***/
#define ID3D12SharingContract_Present(This,resource,sub_resource,window) (This)->lpVtbl->Present(This,resource,sub_resource,window)
#define ID3D12SharingContract_SharedFenceSignal(This,fence,fence_value) (This)->lpVtbl->SharedFenceSignal(This,fence,fence_value)
#define ID3D12SharingContract_BeginCapturableWork(This,guid) (This)->lpVtbl->BeginCapturableWork(This,guid)
#define ID3D12SharingContract_EndCapturableWork(This,guid) (This)->lpVtbl->EndCapturableWork(This,guid)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12SharingContract_QueryInterface(ID3D12SharingContract* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12SharingContract_AddRef(ID3D12SharingContract* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12SharingContract_Release(ID3D12SharingContract* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12SharingContract methods ***/
static inline void ID3D12SharingContract_Present(ID3D12SharingContract* This,ID3D12Resource *resource,UINT sub_resource,HWND window) {
    This->lpVtbl->Present(This,resource,sub_resource,window);
}
static inline void ID3D12SharingContract_SharedFenceSignal(ID3D12SharingContract* This,ID3D12Fence *fence,UINT64 fence_value) {
    This->lpVtbl->SharedFenceSignal(This,fence,fence_value);
}
static inline void ID3D12SharingContract_BeginCapturableWork(ID3D12SharingContract* This,REFGUID guid) {
    This->lpVtbl->BeginCapturableWork(This,guid);
}
static inline void ID3D12SharingContract_EndCapturableWork(ID3D12SharingContract* This,REFGUID guid) {
    This->lpVtbl->EndCapturableWork(This,guid);
}
#endif
#endif

#endif


#endif  /* __ID3D12SharingContract_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12InfoQueue interface
 */
#ifndef __ID3D12InfoQueue_INTERFACE_DEFINED__
#define __ID3D12InfoQueue_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12InfoQueue, 0x0742a90b, 0xc387, 0x483f, 0xb9,0x46, 0x30,0xa7,0xe4,0xe6,0x14,0x58);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0742a90b-c387-483f-b946-30a7e4e61458")
ID3D12InfoQueue : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetMessageCountLimit(
        UINT64 limit) = 0;

    virtual void STDMETHODCALLTYPE ClearStoredMessages(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMessage(
        UINT64 index,
        D3D12_MESSAGE *message,
        SIZE_T *length) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetNumMessagesAllowedByStorageFilter(
        ) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetNumMessagesDeniedByStorageFilter(
        ) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetNumStoredMessages(
        ) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetNumStoredMessagesAllowedByRetrievalFilter(
        ) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetNumMessagesDiscardedByMessageCountLimit(
        ) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetMessageCountLimit(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStorageFilterEntries(
        D3D12_INFO_QUEUE_FILTER *filter) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStorageFilter(
        D3D12_INFO_QUEUE_FILTER *filter,
        SIZE_T *length) = 0;

    virtual void STDMETHODCALLTYPE ClearStorageFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushEmptyStorageFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushCopyOfStorageFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushStorageFilter(
        D3D12_INFO_QUEUE_FILTER *filter) = 0;

    virtual void STDMETHODCALLTYPE PopStorageFilter(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetStorageFilterStackSize(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddRetrievalFilterEntries(
        D3D12_INFO_QUEUE_FILTER *filter) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRetrievalFilter(
        D3D12_INFO_QUEUE_FILTER *filter,
        SIZE_T *length) = 0;

    virtual void STDMETHODCALLTYPE ClearRetrievalFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushEmptyRetrievalFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushCopyOfRetrievalFilter(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE PushRetrievalFilter(
        D3D12_INFO_QUEUE_FILTER *filter) = 0;

    virtual void STDMETHODCALLTYPE PopRetrievalFilter(
        ) = 0;

    virtual UINT STDMETHODCALLTYPE GetRetrievalFilterStackSize(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddMessage(
        D3D12_MESSAGE_CATEGORY category,
        D3D12_MESSAGE_SEVERITY severity,
        D3D12_MESSAGE_ID id,
        const char *description) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddApplicationMessage(
        D3D12_MESSAGE_SEVERITY severity,
        const char *description) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBreakOnCategory(
        D3D12_MESSAGE_CATEGORY category,
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBreakOnSeverity(
        D3D12_MESSAGE_SEVERITY severity,
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBreakOnID(
        D3D12_MESSAGE_ID id,
        WINBOOL enable) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetBreakOnCategory(
        D3D12_MESSAGE_CATEGORY category) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetBreakOnSeverity(
        D3D12_MESSAGE_SEVERITY severity) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetBreakOnID(
        D3D12_MESSAGE_ID id) = 0;

    virtual void STDMETHODCALLTYPE SetMuteDebugOutput(
        WINBOOL mute) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetMuteDebugOutput(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12InfoQueue, 0x0742a90b, 0xc387, 0x483f, 0xb9,0x46, 0x30,0xa7,0xe4,0xe6,0x14,0x58)
#endif
#else
typedef struct ID3D12InfoQueueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12InfoQueue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12InfoQueue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12InfoQueue *This);

    /*** ID3D12InfoQueue methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMessageCountLimit)(
        ID3D12InfoQueue *This,
        UINT64 limit);

    void (STDMETHODCALLTYPE *ClearStoredMessages)(
        ID3D12InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *GetMessage)(
        ID3D12InfoQueue *This,
        UINT64 index,
        D3D12_MESSAGE *message,
        SIZE_T *length);

    UINT64 (STDMETHODCALLTYPE *GetNumMessagesAllowedByStorageFilter)(
        ID3D12InfoQueue *This);

    UINT64 (STDMETHODCALLTYPE *GetNumMessagesDeniedByStorageFilter)(
        ID3D12InfoQueue *This);

    UINT64 (STDMETHODCALLTYPE *GetNumStoredMessages)(
        ID3D12InfoQueue *This);

    UINT64 (STDMETHODCALLTYPE *GetNumStoredMessagesAllowedByRetrievalFilter)(
        ID3D12InfoQueue *This);

    UINT64 (STDMETHODCALLTYPE *GetNumMessagesDiscardedByMessageCountLimit)(
        ID3D12InfoQueue *This);

    UINT64 (STDMETHODCALLTYPE *GetMessageCountLimit)(
        ID3D12InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *AddStorageFilterEntries)(
        ID3D12InfoQueue *This,
        D3D12_INFO_QUEUE_FILTER *filter);

    HRESULT (STDMETHODCALLTYPE *GetStorageFilter)(
        ID3D12InfoQueue *This,
        D3D12_INFO_QUEUE_FILTER *filter,
        SIZE_T *length);

    void (STDMETHODCALLTYPE *ClearStorageFilter)(
        ID3D12InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushEmptyStorageFilter)(
        ID3D12InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushCopyOfStorageFilter)(
        ID3D12InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushStorageFilter)(
        ID3D12InfoQueue *This,
        D3D12_INFO_QUEUE_FILTER *filter);

    void (STDMETHODCALLTYPE *PopStorageFilter)(
        ID3D12InfoQueue *This);

    UINT (STDMETHODCALLTYPE *GetStorageFilterStackSize)(
        ID3D12InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *AddRetrievalFilterEntries)(
        ID3D12InfoQueue *This,
        D3D12_INFO_QUEUE_FILTER *filter);

    HRESULT (STDMETHODCALLTYPE *GetRetrievalFilter)(
        ID3D12InfoQueue *This,
        D3D12_INFO_QUEUE_FILTER *filter,
        SIZE_T *length);

    void (STDMETHODCALLTYPE *ClearRetrievalFilter)(
        ID3D12InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushEmptyRetrievalFilter)(
        ID3D12InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushCopyOfRetrievalFilter)(
        ID3D12InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *PushRetrievalFilter)(
        ID3D12InfoQueue *This,
        D3D12_INFO_QUEUE_FILTER *filter);

    void (STDMETHODCALLTYPE *PopRetrievalFilter)(
        ID3D12InfoQueue *This);

    UINT (STDMETHODCALLTYPE *GetRetrievalFilterStackSize)(
        ID3D12InfoQueue *This);

    HRESULT (STDMETHODCALLTYPE *AddMessage)(
        ID3D12InfoQueue *This,
        D3D12_MESSAGE_CATEGORY category,
        D3D12_MESSAGE_SEVERITY severity,
        D3D12_MESSAGE_ID id,
        const char *description);

    HRESULT (STDMETHODCALLTYPE *AddApplicationMessage)(
        ID3D12InfoQueue *This,
        D3D12_MESSAGE_SEVERITY severity,
        const char *description);

    HRESULT (STDMETHODCALLTYPE *SetBreakOnCategory)(
        ID3D12InfoQueue *This,
        D3D12_MESSAGE_CATEGORY category,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *SetBreakOnSeverity)(
        ID3D12InfoQueue *This,
        D3D12_MESSAGE_SEVERITY severity,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *SetBreakOnID)(
        ID3D12InfoQueue *This,
        D3D12_MESSAGE_ID id,
        WINBOOL enable);

    WINBOOL (STDMETHODCALLTYPE *GetBreakOnCategory)(
        ID3D12InfoQueue *This,
        D3D12_MESSAGE_CATEGORY category);

    WINBOOL (STDMETHODCALLTYPE *GetBreakOnSeverity)(
        ID3D12InfoQueue *This,
        D3D12_MESSAGE_SEVERITY severity);

    WINBOOL (STDMETHODCALLTYPE *GetBreakOnID)(
        ID3D12InfoQueue *This,
        D3D12_MESSAGE_ID id);

    void (STDMETHODCALLTYPE *SetMuteDebugOutput)(
        ID3D12InfoQueue *This,
        WINBOOL mute);

    WINBOOL (STDMETHODCALLTYPE *GetMuteDebugOutput)(
        ID3D12InfoQueue *This);

    END_INTERFACE
} ID3D12InfoQueueVtbl;

interface ID3D12InfoQueue {
    CONST_VTBL ID3D12InfoQueueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12InfoQueue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12InfoQueue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12InfoQueue_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12InfoQueue methods ***/
#define ID3D12InfoQueue_SetMessageCountLimit(This,limit) (This)->lpVtbl->SetMessageCountLimit(This,limit)
#define ID3D12InfoQueue_ClearStoredMessages(This) (This)->lpVtbl->ClearStoredMessages(This)
#define ID3D12InfoQueue_GetMessage(This,index,message,length) (This)->lpVtbl->GetMessage(This,index,message,length)
#define ID3D12InfoQueue_GetNumMessagesAllowedByStorageFilter(This) (This)->lpVtbl->GetNumMessagesAllowedByStorageFilter(This)
#define ID3D12InfoQueue_GetNumMessagesDeniedByStorageFilter(This) (This)->lpVtbl->GetNumMessagesDeniedByStorageFilter(This)
#define ID3D12InfoQueue_GetNumStoredMessages(This) (This)->lpVtbl->GetNumStoredMessages(This)
#define ID3D12InfoQueue_GetNumStoredMessagesAllowedByRetrievalFilter(This) (This)->lpVtbl->GetNumStoredMessagesAllowedByRetrievalFilter(This)
#define ID3D12InfoQueue_GetNumMessagesDiscardedByMessageCountLimit(This) (This)->lpVtbl->GetNumMessagesDiscardedByMessageCountLimit(This)
#define ID3D12InfoQueue_GetMessageCountLimit(This) (This)->lpVtbl->GetMessageCountLimit(This)
#define ID3D12InfoQueue_AddStorageFilterEntries(This,filter) (This)->lpVtbl->AddStorageFilterEntries(This,filter)
#define ID3D12InfoQueue_GetStorageFilter(This,filter,length) (This)->lpVtbl->GetStorageFilter(This,filter,length)
#define ID3D12InfoQueue_ClearStorageFilter(This) (This)->lpVtbl->ClearStorageFilter(This)
#define ID3D12InfoQueue_PushEmptyStorageFilter(This) (This)->lpVtbl->PushEmptyStorageFilter(This)
#define ID3D12InfoQueue_PushCopyOfStorageFilter(This) (This)->lpVtbl->PushCopyOfStorageFilter(This)
#define ID3D12InfoQueue_PushStorageFilter(This,filter) (This)->lpVtbl->PushStorageFilter(This,filter)
#define ID3D12InfoQueue_PopStorageFilter(This) (This)->lpVtbl->PopStorageFilter(This)
#define ID3D12InfoQueue_GetStorageFilterStackSize(This) (This)->lpVtbl->GetStorageFilterStackSize(This)
#define ID3D12InfoQueue_AddRetrievalFilterEntries(This,filter) (This)->lpVtbl->AddRetrievalFilterEntries(This,filter)
#define ID3D12InfoQueue_GetRetrievalFilter(This,filter,length) (This)->lpVtbl->GetRetrievalFilter(This,filter,length)
#define ID3D12InfoQueue_ClearRetrievalFilter(This) (This)->lpVtbl->ClearRetrievalFilter(This)
#define ID3D12InfoQueue_PushEmptyRetrievalFilter(This) (This)->lpVtbl->PushEmptyRetrievalFilter(This)
#define ID3D12InfoQueue_PushCopyOfRetrievalFilter(This) (This)->lpVtbl->PushCopyOfRetrievalFilter(This)
#define ID3D12InfoQueue_PushRetrievalFilter(This,filter) (This)->lpVtbl->PushRetrievalFilter(This,filter)
#define ID3D12InfoQueue_PopRetrievalFilter(This) (This)->lpVtbl->PopRetrievalFilter(This)
#define ID3D12InfoQueue_GetRetrievalFilterStackSize(This) (This)->lpVtbl->GetRetrievalFilterStackSize(This)
#define ID3D12InfoQueue_AddMessage(This,category,severity,id,description) (This)->lpVtbl->AddMessage(This,category,severity,id,description)
#define ID3D12InfoQueue_AddApplicationMessage(This,severity,description) (This)->lpVtbl->AddApplicationMessage(This,severity,description)
#define ID3D12InfoQueue_SetBreakOnCategory(This,category,enable) (This)->lpVtbl->SetBreakOnCategory(This,category,enable)
#define ID3D12InfoQueue_SetBreakOnSeverity(This,severity,enable) (This)->lpVtbl->SetBreakOnSeverity(This,severity,enable)
#define ID3D12InfoQueue_SetBreakOnID(This,id,enable) (This)->lpVtbl->SetBreakOnID(This,id,enable)
#define ID3D12InfoQueue_GetBreakOnCategory(This,category) (This)->lpVtbl->GetBreakOnCategory(This,category)
#define ID3D12InfoQueue_GetBreakOnSeverity(This,severity) (This)->lpVtbl->GetBreakOnSeverity(This,severity)
#define ID3D12InfoQueue_GetBreakOnID(This,id) (This)->lpVtbl->GetBreakOnID(This,id)
#define ID3D12InfoQueue_SetMuteDebugOutput(This,mute) (This)->lpVtbl->SetMuteDebugOutput(This,mute)
#define ID3D12InfoQueue_GetMuteDebugOutput(This) (This)->lpVtbl->GetMuteDebugOutput(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12InfoQueue_QueryInterface(ID3D12InfoQueue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12InfoQueue_AddRef(ID3D12InfoQueue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12InfoQueue_Release(ID3D12InfoQueue* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12InfoQueue methods ***/
static inline HRESULT ID3D12InfoQueue_SetMessageCountLimit(ID3D12InfoQueue* This,UINT64 limit) {
    return This->lpVtbl->SetMessageCountLimit(This,limit);
}
static inline void ID3D12InfoQueue_ClearStoredMessages(ID3D12InfoQueue* This) {
    This->lpVtbl->ClearStoredMessages(This);
}
static inline HRESULT ID3D12InfoQueue_GetMessage(ID3D12InfoQueue* This,UINT64 index,D3D12_MESSAGE *message,SIZE_T *length) {
    return This->lpVtbl->GetMessage(This,index,message,length);
}
static inline UINT64 ID3D12InfoQueue_GetNumMessagesAllowedByStorageFilter(ID3D12InfoQueue* This) {
    return This->lpVtbl->GetNumMessagesAllowedByStorageFilter(This);
}
static inline UINT64 ID3D12InfoQueue_GetNumMessagesDeniedByStorageFilter(ID3D12InfoQueue* This) {
    return This->lpVtbl->GetNumMessagesDeniedByStorageFilter(This);
}
static inline UINT64 ID3D12InfoQueue_GetNumStoredMessages(ID3D12InfoQueue* This) {
    return This->lpVtbl->GetNumStoredMessages(This);
}
static inline UINT64 ID3D12InfoQueue_GetNumStoredMessagesAllowedByRetrievalFilter(ID3D12InfoQueue* This) {
    return This->lpVtbl->GetNumStoredMessagesAllowedByRetrievalFilter(This);
}
static inline UINT64 ID3D12InfoQueue_GetNumMessagesDiscardedByMessageCountLimit(ID3D12InfoQueue* This) {
    return This->lpVtbl->GetNumMessagesDiscardedByMessageCountLimit(This);
}
static inline UINT64 ID3D12InfoQueue_GetMessageCountLimit(ID3D12InfoQueue* This) {
    return This->lpVtbl->GetMessageCountLimit(This);
}
static inline HRESULT ID3D12InfoQueue_AddStorageFilterEntries(ID3D12InfoQueue* This,D3D12_INFO_QUEUE_FILTER *filter) {
    return This->lpVtbl->AddStorageFilterEntries(This,filter);
}
static inline HRESULT ID3D12InfoQueue_GetStorageFilter(ID3D12InfoQueue* This,D3D12_INFO_QUEUE_FILTER *filter,SIZE_T *length) {
    return This->lpVtbl->GetStorageFilter(This,filter,length);
}
static inline void ID3D12InfoQueue_ClearStorageFilter(ID3D12InfoQueue* This) {
    This->lpVtbl->ClearStorageFilter(This);
}
static inline HRESULT ID3D12InfoQueue_PushEmptyStorageFilter(ID3D12InfoQueue* This) {
    return This->lpVtbl->PushEmptyStorageFilter(This);
}
static inline HRESULT ID3D12InfoQueue_PushCopyOfStorageFilter(ID3D12InfoQueue* This) {
    return This->lpVtbl->PushCopyOfStorageFilter(This);
}
static inline HRESULT ID3D12InfoQueue_PushStorageFilter(ID3D12InfoQueue* This,D3D12_INFO_QUEUE_FILTER *filter) {
    return This->lpVtbl->PushStorageFilter(This,filter);
}
static inline void ID3D12InfoQueue_PopStorageFilter(ID3D12InfoQueue* This) {
    This->lpVtbl->PopStorageFilter(This);
}
static inline UINT ID3D12InfoQueue_GetStorageFilterStackSize(ID3D12InfoQueue* This) {
    return This->lpVtbl->GetStorageFilterStackSize(This);
}
static inline HRESULT ID3D12InfoQueue_AddRetrievalFilterEntries(ID3D12InfoQueue* This,D3D12_INFO_QUEUE_FILTER *filter) {
    return This->lpVtbl->AddRetrievalFilterEntries(This,filter);
}
static inline HRESULT ID3D12InfoQueue_GetRetrievalFilter(ID3D12InfoQueue* This,D3D12_INFO_QUEUE_FILTER *filter,SIZE_T *length) {
    return This->lpVtbl->GetRetrievalFilter(This,filter,length);
}
static inline void ID3D12InfoQueue_ClearRetrievalFilter(ID3D12InfoQueue* This) {
    This->lpVtbl->ClearRetrievalFilter(This);
}
static inline HRESULT ID3D12InfoQueue_PushEmptyRetrievalFilter(ID3D12InfoQueue* This) {
    return This->lpVtbl->PushEmptyRetrievalFilter(This);
}
static inline HRESULT ID3D12InfoQueue_PushCopyOfRetrievalFilter(ID3D12InfoQueue* This) {
    return This->lpVtbl->PushCopyOfRetrievalFilter(This);
}
static inline HRESULT ID3D12InfoQueue_PushRetrievalFilter(ID3D12InfoQueue* This,D3D12_INFO_QUEUE_FILTER *filter) {
    return This->lpVtbl->PushRetrievalFilter(This,filter);
}
static inline void ID3D12InfoQueue_PopRetrievalFilter(ID3D12InfoQueue* This) {
    This->lpVtbl->PopRetrievalFilter(This);
}
static inline UINT ID3D12InfoQueue_GetRetrievalFilterStackSize(ID3D12InfoQueue* This) {
    return This->lpVtbl->GetRetrievalFilterStackSize(This);
}
static inline HRESULT ID3D12InfoQueue_AddMessage(ID3D12InfoQueue* This,D3D12_MESSAGE_CATEGORY category,D3D12_MESSAGE_SEVERITY severity,D3D12_MESSAGE_ID id,const char *description) {
    return This->lpVtbl->AddMessage(This,category,severity,id,description);
}
static inline HRESULT ID3D12InfoQueue_AddApplicationMessage(ID3D12InfoQueue* This,D3D12_MESSAGE_SEVERITY severity,const char *description) {
    return This->lpVtbl->AddApplicationMessage(This,severity,description);
}
static inline HRESULT ID3D12InfoQueue_SetBreakOnCategory(ID3D12InfoQueue* This,D3D12_MESSAGE_CATEGORY category,WINBOOL enable) {
    return This->lpVtbl->SetBreakOnCategory(This,category,enable);
}
static inline HRESULT ID3D12InfoQueue_SetBreakOnSeverity(ID3D12InfoQueue* This,D3D12_MESSAGE_SEVERITY severity,WINBOOL enable) {
    return This->lpVtbl->SetBreakOnSeverity(This,severity,enable);
}
static inline HRESULT ID3D12InfoQueue_SetBreakOnID(ID3D12InfoQueue* This,D3D12_MESSAGE_ID id,WINBOOL enable) {
    return This->lpVtbl->SetBreakOnID(This,id,enable);
}
static inline WINBOOL ID3D12InfoQueue_GetBreakOnCategory(ID3D12InfoQueue* This,D3D12_MESSAGE_CATEGORY category) {
    return This->lpVtbl->GetBreakOnCategory(This,category);
}
static inline WINBOOL ID3D12InfoQueue_GetBreakOnSeverity(ID3D12InfoQueue* This,D3D12_MESSAGE_SEVERITY severity) {
    return This->lpVtbl->GetBreakOnSeverity(This,severity);
}
static inline WINBOOL ID3D12InfoQueue_GetBreakOnID(ID3D12InfoQueue* This,D3D12_MESSAGE_ID id) {
    return This->lpVtbl->GetBreakOnID(This,id);
}
static inline void ID3D12InfoQueue_SetMuteDebugOutput(ID3D12InfoQueue* This,WINBOOL mute) {
    This->lpVtbl->SetMuteDebugOutput(This,mute);
}
static inline WINBOOL ID3D12InfoQueue_GetMuteDebugOutput(ID3D12InfoQueue* This) {
    return This->lpVtbl->GetMuteDebugOutput(This);
}
#endif
#endif

#endif


#endif  /* __ID3D12InfoQueue_INTERFACE_DEFINED__ */

typedef void (__stdcall *D3D12MessageFunc)(D3D12_MESSAGE_CATEGORY category,D3D12_MESSAGE_SEVERITY severity,D3D12_MESSAGE_ID id,const char *description,void *context);
/*****************************************************************************
 * ID3D12InfoQueue1 interface
 */
#ifndef __ID3D12InfoQueue1_INTERFACE_DEFINED__
#define __ID3D12InfoQueue1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12InfoQueue1, 0x2852dd88, 0xb484, 0x4c0c, 0xb6,0xb1, 0x67,0x16,0x85,0x00,0xe6,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2852dd88-b484-4c0c-b6b1-67168500e600")
ID3D12InfoQueue1 : public ID3D12InfoQueue
{
    virtual HRESULT STDMETHODCALLTYPE RegisterMessageCallback(
        D3D12MessageFunc func,
        D3D12_MESSAGE_CALLBACK_FLAGS flags,
        void *context,
        DWORD *cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnregisterMessageCallback(
        DWORD cookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12InfoQueue1, 0x2852dd88, 0xb484, 0x4c0c, 0xb6,0xb1, 0x67,0x16,0x85,0x00,0xe6,0x00)
#endif
#else
typedef struct ID3D12InfoQueue1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12InfoQueue1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12InfoQueue1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12InfoQueue1 *This);

    /*** ID3D12InfoQueue methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMessageCountLimit)(
        ID3D12InfoQueue1 *This,
        UINT64 limit);

    void (STDMETHODCALLTYPE *ClearStoredMessages)(
        ID3D12InfoQueue1 *This);

    HRESULT (STDMETHODCALLTYPE *GetMessage)(
        ID3D12InfoQueue1 *This,
        UINT64 index,
        D3D12_MESSAGE *message,
        SIZE_T *length);

    UINT64 (STDMETHODCALLTYPE *GetNumMessagesAllowedByStorageFilter)(
        ID3D12InfoQueue1 *This);

    UINT64 (STDMETHODCALLTYPE *GetNumMessagesDeniedByStorageFilter)(
        ID3D12InfoQueue1 *This);

    UINT64 (STDMETHODCALLTYPE *GetNumStoredMessages)(
        ID3D12InfoQueue1 *This);

    UINT64 (STDMETHODCALLTYPE *GetNumStoredMessagesAllowedByRetrievalFilter)(
        ID3D12InfoQueue1 *This);

    UINT64 (STDMETHODCALLTYPE *GetNumMessagesDiscardedByMessageCountLimit)(
        ID3D12InfoQueue1 *This);

    UINT64 (STDMETHODCALLTYPE *GetMessageCountLimit)(
        ID3D12InfoQueue1 *This);

    HRESULT (STDMETHODCALLTYPE *AddStorageFilterEntries)(
        ID3D12InfoQueue1 *This,
        D3D12_INFO_QUEUE_FILTER *filter);

    HRESULT (STDMETHODCALLTYPE *GetStorageFilter)(
        ID3D12InfoQueue1 *This,
        D3D12_INFO_QUEUE_FILTER *filter,
        SIZE_T *length);

    void (STDMETHODCALLTYPE *ClearStorageFilter)(
        ID3D12InfoQueue1 *This);

    HRESULT (STDMETHODCALLTYPE *PushEmptyStorageFilter)(
        ID3D12InfoQueue1 *This);

    HRESULT (STDMETHODCALLTYPE *PushCopyOfStorageFilter)(
        ID3D12InfoQueue1 *This);

    HRESULT (STDMETHODCALLTYPE *PushStorageFilter)(
        ID3D12InfoQueue1 *This,
        D3D12_INFO_QUEUE_FILTER *filter);

    void (STDMETHODCALLTYPE *PopStorageFilter)(
        ID3D12InfoQueue1 *This);

    UINT (STDMETHODCALLTYPE *GetStorageFilterStackSize)(
        ID3D12InfoQueue1 *This);

    HRESULT (STDMETHODCALLTYPE *AddRetrievalFilterEntries)(
        ID3D12InfoQueue1 *This,
        D3D12_INFO_QUEUE_FILTER *filter);

    HRESULT (STDMETHODCALLTYPE *GetRetrievalFilter)(
        ID3D12InfoQueue1 *This,
        D3D12_INFO_QUEUE_FILTER *filter,
        SIZE_T *length);

    void (STDMETHODCALLTYPE *ClearRetrievalFilter)(
        ID3D12InfoQueue1 *This);

    HRESULT (STDMETHODCALLTYPE *PushEmptyRetrievalFilter)(
        ID3D12InfoQueue1 *This);

    HRESULT (STDMETHODCALLTYPE *PushCopyOfRetrievalFilter)(
        ID3D12InfoQueue1 *This);

    HRESULT (STDMETHODCALLTYPE *PushRetrievalFilter)(
        ID3D12InfoQueue1 *This,
        D3D12_INFO_QUEUE_FILTER *filter);

    void (STDMETHODCALLTYPE *PopRetrievalFilter)(
        ID3D12InfoQueue1 *This);

    UINT (STDMETHODCALLTYPE *GetRetrievalFilterStackSize)(
        ID3D12InfoQueue1 *This);

    HRESULT (STDMETHODCALLTYPE *AddMessage)(
        ID3D12InfoQueue1 *This,
        D3D12_MESSAGE_CATEGORY category,
        D3D12_MESSAGE_SEVERITY severity,
        D3D12_MESSAGE_ID id,
        const char *description);

    HRESULT (STDMETHODCALLTYPE *AddApplicationMessage)(
        ID3D12InfoQueue1 *This,
        D3D12_MESSAGE_SEVERITY severity,
        const char *description);

    HRESULT (STDMETHODCALLTYPE *SetBreakOnCategory)(
        ID3D12InfoQueue1 *This,
        D3D12_MESSAGE_CATEGORY category,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *SetBreakOnSeverity)(
        ID3D12InfoQueue1 *This,
        D3D12_MESSAGE_SEVERITY severity,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *SetBreakOnID)(
        ID3D12InfoQueue1 *This,
        D3D12_MESSAGE_ID id,
        WINBOOL enable);

    WINBOOL (STDMETHODCALLTYPE *GetBreakOnCategory)(
        ID3D12InfoQueue1 *This,
        D3D12_MESSAGE_CATEGORY category);

    WINBOOL (STDMETHODCALLTYPE *GetBreakOnSeverity)(
        ID3D12InfoQueue1 *This,
        D3D12_MESSAGE_SEVERITY severity);

    WINBOOL (STDMETHODCALLTYPE *GetBreakOnID)(
        ID3D12InfoQueue1 *This,
        D3D12_MESSAGE_ID id);

    void (STDMETHODCALLTYPE *SetMuteDebugOutput)(
        ID3D12InfoQueue1 *This,
        WINBOOL mute);

    WINBOOL (STDMETHODCALLTYPE *GetMuteDebugOutput)(
        ID3D12InfoQueue1 *This);

    /*** ID3D12InfoQueue1 methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterMessageCallback)(
        ID3D12InfoQueue1 *This,
        D3D12MessageFunc func,
        D3D12_MESSAGE_CALLBACK_FLAGS flags,
        void *context,
        DWORD *cookie);

    HRESULT (STDMETHODCALLTYPE *UnregisterMessageCallback)(
        ID3D12InfoQueue1 *This,
        DWORD cookie);

    END_INTERFACE
} ID3D12InfoQueue1Vtbl;

interface ID3D12InfoQueue1 {
    CONST_VTBL ID3D12InfoQueue1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12InfoQueue1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12InfoQueue1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12InfoQueue1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12InfoQueue methods ***/
#define ID3D12InfoQueue1_SetMessageCountLimit(This,limit) (This)->lpVtbl->SetMessageCountLimit(This,limit)
#define ID3D12InfoQueue1_ClearStoredMessages(This) (This)->lpVtbl->ClearStoredMessages(This)
#define ID3D12InfoQueue1_GetMessage(This,index,message,length) (This)->lpVtbl->GetMessage(This,index,message,length)
#define ID3D12InfoQueue1_GetNumMessagesAllowedByStorageFilter(This) (This)->lpVtbl->GetNumMessagesAllowedByStorageFilter(This)
#define ID3D12InfoQueue1_GetNumMessagesDeniedByStorageFilter(This) (This)->lpVtbl->GetNumMessagesDeniedByStorageFilter(This)
#define ID3D12InfoQueue1_GetNumStoredMessages(This) (This)->lpVtbl->GetNumStoredMessages(This)
#define ID3D12InfoQueue1_GetNumStoredMessagesAllowedByRetrievalFilter(This) (This)->lpVtbl->GetNumStoredMessagesAllowedByRetrievalFilter(This)
#define ID3D12InfoQueue1_GetNumMessagesDiscardedByMessageCountLimit(This) (This)->lpVtbl->GetNumMessagesDiscardedByMessageCountLimit(This)
#define ID3D12InfoQueue1_GetMessageCountLimit(This) (This)->lpVtbl->GetMessageCountLimit(This)
#define ID3D12InfoQueue1_AddStorageFilterEntries(This,filter) (This)->lpVtbl->AddStorageFilterEntries(This,filter)
#define ID3D12InfoQueue1_GetStorageFilter(This,filter,length) (This)->lpVtbl->GetStorageFilter(This,filter,length)
#define ID3D12InfoQueue1_ClearStorageFilter(This) (This)->lpVtbl->ClearStorageFilter(This)
#define ID3D12InfoQueue1_PushEmptyStorageFilter(This) (This)->lpVtbl->PushEmptyStorageFilter(This)
#define ID3D12InfoQueue1_PushCopyOfStorageFilter(This) (This)->lpVtbl->PushCopyOfStorageFilter(This)
#define ID3D12InfoQueue1_PushStorageFilter(This,filter) (This)->lpVtbl->PushStorageFilter(This,filter)
#define ID3D12InfoQueue1_PopStorageFilter(This) (This)->lpVtbl->PopStorageFilter(This)
#define ID3D12InfoQueue1_GetStorageFilterStackSize(This) (This)->lpVtbl->GetStorageFilterStackSize(This)
#define ID3D12InfoQueue1_AddRetrievalFilterEntries(This,filter) (This)->lpVtbl->AddRetrievalFilterEntries(This,filter)
#define ID3D12InfoQueue1_GetRetrievalFilter(This,filter,length) (This)->lpVtbl->GetRetrievalFilter(This,filter,length)
#define ID3D12InfoQueue1_ClearRetrievalFilter(This) (This)->lpVtbl->ClearRetrievalFilter(This)
#define ID3D12InfoQueue1_PushEmptyRetrievalFilter(This) (This)->lpVtbl->PushEmptyRetrievalFilter(This)
#define ID3D12InfoQueue1_PushCopyOfRetrievalFilter(This) (This)->lpVtbl->PushCopyOfRetrievalFilter(This)
#define ID3D12InfoQueue1_PushRetrievalFilter(This,filter) (This)->lpVtbl->PushRetrievalFilter(This,filter)
#define ID3D12InfoQueue1_PopRetrievalFilter(This) (This)->lpVtbl->PopRetrievalFilter(This)
#define ID3D12InfoQueue1_GetRetrievalFilterStackSize(This) (This)->lpVtbl->GetRetrievalFilterStackSize(This)
#define ID3D12InfoQueue1_AddMessage(This,category,severity,id,description) (This)->lpVtbl->AddMessage(This,category,severity,id,description)
#define ID3D12InfoQueue1_AddApplicationMessage(This,severity,description) (This)->lpVtbl->AddApplicationMessage(This,severity,description)
#define ID3D12InfoQueue1_SetBreakOnCategory(This,category,enable) (This)->lpVtbl->SetBreakOnCategory(This,category,enable)
#define ID3D12InfoQueue1_SetBreakOnSeverity(This,severity,enable) (This)->lpVtbl->SetBreakOnSeverity(This,severity,enable)
#define ID3D12InfoQueue1_SetBreakOnID(This,id,enable) (This)->lpVtbl->SetBreakOnID(This,id,enable)
#define ID3D12InfoQueue1_GetBreakOnCategory(This,category) (This)->lpVtbl->GetBreakOnCategory(This,category)
#define ID3D12InfoQueue1_GetBreakOnSeverity(This,severity) (This)->lpVtbl->GetBreakOnSeverity(This,severity)
#define ID3D12InfoQueue1_GetBreakOnID(This,id) (This)->lpVtbl->GetBreakOnID(This,id)
#define ID3D12InfoQueue1_SetMuteDebugOutput(This,mute) (This)->lpVtbl->SetMuteDebugOutput(This,mute)
#define ID3D12InfoQueue1_GetMuteDebugOutput(This) (This)->lpVtbl->GetMuteDebugOutput(This)
/*** ID3D12InfoQueue1 methods ***/
#define ID3D12InfoQueue1_RegisterMessageCallback(This,func,flags,context,cookie) (This)->lpVtbl->RegisterMessageCallback(This,func,flags,context,cookie)
#define ID3D12InfoQueue1_UnregisterMessageCallback(This,cookie) (This)->lpVtbl->UnregisterMessageCallback(This,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12InfoQueue1_QueryInterface(ID3D12InfoQueue1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12InfoQueue1_AddRef(ID3D12InfoQueue1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12InfoQueue1_Release(ID3D12InfoQueue1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12InfoQueue methods ***/
static inline HRESULT ID3D12InfoQueue1_SetMessageCountLimit(ID3D12InfoQueue1* This,UINT64 limit) {
    return This->lpVtbl->SetMessageCountLimit(This,limit);
}
static inline void ID3D12InfoQueue1_ClearStoredMessages(ID3D12InfoQueue1* This) {
    This->lpVtbl->ClearStoredMessages(This);
}
static inline HRESULT ID3D12InfoQueue1_GetMessage(ID3D12InfoQueue1* This,UINT64 index,D3D12_MESSAGE *message,SIZE_T *length) {
    return This->lpVtbl->GetMessage(This,index,message,length);
}
static inline UINT64 ID3D12InfoQueue1_GetNumMessagesAllowedByStorageFilter(ID3D12InfoQueue1* This) {
    return This->lpVtbl->GetNumMessagesAllowedByStorageFilter(This);
}
static inline UINT64 ID3D12InfoQueue1_GetNumMessagesDeniedByStorageFilter(ID3D12InfoQueue1* This) {
    return This->lpVtbl->GetNumMessagesDeniedByStorageFilter(This);
}
static inline UINT64 ID3D12InfoQueue1_GetNumStoredMessages(ID3D12InfoQueue1* This) {
    return This->lpVtbl->GetNumStoredMessages(This);
}
static inline UINT64 ID3D12InfoQueue1_GetNumStoredMessagesAllowedByRetrievalFilter(ID3D12InfoQueue1* This) {
    return This->lpVtbl->GetNumStoredMessagesAllowedByRetrievalFilter(This);
}
static inline UINT64 ID3D12InfoQueue1_GetNumMessagesDiscardedByMessageCountLimit(ID3D12InfoQueue1* This) {
    return This->lpVtbl->GetNumMessagesDiscardedByMessageCountLimit(This);
}
static inline UINT64 ID3D12InfoQueue1_GetMessageCountLimit(ID3D12InfoQueue1* This) {
    return This->lpVtbl->GetMessageCountLimit(This);
}
static inline HRESULT ID3D12InfoQueue1_AddStorageFilterEntries(ID3D12InfoQueue1* This,D3D12_INFO_QUEUE_FILTER *filter) {
    return This->lpVtbl->AddStorageFilterEntries(This,filter);
}
static inline HRESULT ID3D12InfoQueue1_GetStorageFilter(ID3D12InfoQueue1* This,D3D12_INFO_QUEUE_FILTER *filter,SIZE_T *length) {
    return This->lpVtbl->GetStorageFilter(This,filter,length);
}
static inline void ID3D12InfoQueue1_ClearStorageFilter(ID3D12InfoQueue1* This) {
    This->lpVtbl->ClearStorageFilter(This);
}
static inline HRESULT ID3D12InfoQueue1_PushEmptyStorageFilter(ID3D12InfoQueue1* This) {
    return This->lpVtbl->PushEmptyStorageFilter(This);
}
static inline HRESULT ID3D12InfoQueue1_PushCopyOfStorageFilter(ID3D12InfoQueue1* This) {
    return This->lpVtbl->PushCopyOfStorageFilter(This);
}
static inline HRESULT ID3D12InfoQueue1_PushStorageFilter(ID3D12InfoQueue1* This,D3D12_INFO_QUEUE_FILTER *filter) {
    return This->lpVtbl->PushStorageFilter(This,filter);
}
static inline void ID3D12InfoQueue1_PopStorageFilter(ID3D12InfoQueue1* This) {
    This->lpVtbl->PopStorageFilter(This);
}
static inline UINT ID3D12InfoQueue1_GetStorageFilterStackSize(ID3D12InfoQueue1* This) {
    return This->lpVtbl->GetStorageFilterStackSize(This);
}
static inline HRESULT ID3D12InfoQueue1_AddRetrievalFilterEntries(ID3D12InfoQueue1* This,D3D12_INFO_QUEUE_FILTER *filter) {
    return This->lpVtbl->AddRetrievalFilterEntries(This,filter);
}
static inline HRESULT ID3D12InfoQueue1_GetRetrievalFilter(ID3D12InfoQueue1* This,D3D12_INFO_QUEUE_FILTER *filter,SIZE_T *length) {
    return This->lpVtbl->GetRetrievalFilter(This,filter,length);
}
static inline void ID3D12InfoQueue1_ClearRetrievalFilter(ID3D12InfoQueue1* This) {
    This->lpVtbl->ClearRetrievalFilter(This);
}
static inline HRESULT ID3D12InfoQueue1_PushEmptyRetrievalFilter(ID3D12InfoQueue1* This) {
    return This->lpVtbl->PushEmptyRetrievalFilter(This);
}
static inline HRESULT ID3D12InfoQueue1_PushCopyOfRetrievalFilter(ID3D12InfoQueue1* This) {
    return This->lpVtbl->PushCopyOfRetrievalFilter(This);
}
static inline HRESULT ID3D12InfoQueue1_PushRetrievalFilter(ID3D12InfoQueue1* This,D3D12_INFO_QUEUE_FILTER *filter) {
    return This->lpVtbl->PushRetrievalFilter(This,filter);
}
static inline void ID3D12InfoQueue1_PopRetrievalFilter(ID3D12InfoQueue1* This) {
    This->lpVtbl->PopRetrievalFilter(This);
}
static inline UINT ID3D12InfoQueue1_GetRetrievalFilterStackSize(ID3D12InfoQueue1* This) {
    return This->lpVtbl->GetRetrievalFilterStackSize(This);
}
static inline HRESULT ID3D12InfoQueue1_AddMessage(ID3D12InfoQueue1* This,D3D12_MESSAGE_CATEGORY category,D3D12_MESSAGE_SEVERITY severity,D3D12_MESSAGE_ID id,const char *description) {
    return This->lpVtbl->AddMessage(This,category,severity,id,description);
}
static inline HRESULT ID3D12InfoQueue1_AddApplicationMessage(ID3D12InfoQueue1* This,D3D12_MESSAGE_SEVERITY severity,const char *description) {
    return This->lpVtbl->AddApplicationMessage(This,severity,description);
}
static inline HRESULT ID3D12InfoQueue1_SetBreakOnCategory(ID3D12InfoQueue1* This,D3D12_MESSAGE_CATEGORY category,WINBOOL enable) {
    return This->lpVtbl->SetBreakOnCategory(This,category,enable);
}
static inline HRESULT ID3D12InfoQueue1_SetBreakOnSeverity(ID3D12InfoQueue1* This,D3D12_MESSAGE_SEVERITY severity,WINBOOL enable) {
    return This->lpVtbl->SetBreakOnSeverity(This,severity,enable);
}
static inline HRESULT ID3D12InfoQueue1_SetBreakOnID(ID3D12InfoQueue1* This,D3D12_MESSAGE_ID id,WINBOOL enable) {
    return This->lpVtbl->SetBreakOnID(This,id,enable);
}
static inline WINBOOL ID3D12InfoQueue1_GetBreakOnCategory(ID3D12InfoQueue1* This,D3D12_MESSAGE_CATEGORY category) {
    return This->lpVtbl->GetBreakOnCategory(This,category);
}
static inline WINBOOL ID3D12InfoQueue1_GetBreakOnSeverity(ID3D12InfoQueue1* This,D3D12_MESSAGE_SEVERITY severity) {
    return This->lpVtbl->GetBreakOnSeverity(This,severity);
}
static inline WINBOOL ID3D12InfoQueue1_GetBreakOnID(ID3D12InfoQueue1* This,D3D12_MESSAGE_ID id) {
    return This->lpVtbl->GetBreakOnID(This,id);
}
static inline void ID3D12InfoQueue1_SetMuteDebugOutput(ID3D12InfoQueue1* This,WINBOOL mute) {
    This->lpVtbl->SetMuteDebugOutput(This,mute);
}
static inline WINBOOL ID3D12InfoQueue1_GetMuteDebugOutput(ID3D12InfoQueue1* This) {
    return This->lpVtbl->GetMuteDebugOutput(This);
}
/*** ID3D12InfoQueue1 methods ***/
static inline HRESULT ID3D12InfoQueue1_RegisterMessageCallback(ID3D12InfoQueue1* This,D3D12MessageFunc func,D3D12_MESSAGE_CALLBACK_FLAGS flags,void *context,DWORD *cookie) {
    return This->lpVtbl->RegisterMessageCallback(This,func,flags,context,cookie);
}
static inline HRESULT ID3D12InfoQueue1_UnregisterMessageCallback(ID3D12InfoQueue1* This,DWORD cookie) {
    return This->lpVtbl->UnregisterMessageCallback(This,cookie);
}
#endif
#endif

#endif


#endif  /* __ID3D12InfoQueue1_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __d3d12sdklayers_h__ */
