/*** Autogenerated by WIDL 10.12 from include/windows.storage.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_storage_h__
#define __windows_storage_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler ABI::Windows::Storage::IApplicationDataSetVersionHandler
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationDataSetVersionHandler;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationData_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationData_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationData __x_ABI_CWindows_CStorage_CIApplicationData;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationData ABI::Windows::Storage::IApplicationData
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationData;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationData2_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationData2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationData2 __x_ABI_CWindows_CStorage_CIApplicationData2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationData2 ABI::Windows::Storage::IApplicationData2
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationData2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationData3_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationData3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationData3 __x_ABI_CWindows_CStorage_CIApplicationData3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationData3 ABI::Windows::Storage::IApplicationData3
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationData3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationDataContainer_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationDataContainer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationDataContainer __x_ABI_CWindows_CStorage_CIApplicationDataContainer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer ABI::Windows::Storage::IApplicationDataContainer
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationDataContainer;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationDataStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationDataStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationDataStatics __x_ABI_CWindows_CStorage_CIApplicationDataStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics ABI::Windows::Storage::IApplicationDataStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationDataStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationDataStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationDataStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 __x_ABI_CWindows_CStorage_CIApplicationDataStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 ABI::Windows::Storage::IApplicationDataStatics2
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationDataStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics ABI::Windows::Storage::IKnownFoldersCameraRollStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersCameraRollStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics ABI::Windows::Storage::IKnownFoldersPlaylistsStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersPlaylistsStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics ABI::Windows::Storage::IKnownFoldersSavedPicturesStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersSavedPicturesStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics __x_ABI_CWindows_CStorage_CIKnownFoldersStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics ABI::Windows::Storage::IKnownFoldersStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 ABI::Windows::Storage::IKnownFoldersStatics2
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 ABI::Windows::Storage::IKnownFoldersStatics3
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersStatics3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 ABI::Windows::Storage::IKnownFoldersStatics4
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersStatics4;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CISetVersionDeferral_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CISetVersionDeferral_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CISetVersionDeferral __x_ABI_CWindows_CStorage_CISetVersionDeferral;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CISetVersionDeferral ABI::Windows::Storage::ISetVersionDeferral
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface ISetVersionDeferral;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CISetVersionRequest_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CISetVersionRequest_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CISetVersionRequest __x_ABI_CWindows_CStorage_CISetVersionRequest;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CISetVersionRequest ABI::Windows::Storage::ISetVersionRequest
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface ISetVersionRequest;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIStorageFile_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageFile_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIStorageFile __x_ABI_CWindows_CStorage_CIStorageFile;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIStorageFile ABI::Windows::Storage::IStorageFile
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IStorageFile;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIStorageFolder_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageFolder_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIStorageFolder __x_ABI_CWindows_CStorage_CIStorageFolder;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIStorageFolder ABI::Windows::Storage::IStorageFolder
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IStorageFolder;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIStorageItem_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageItem_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIStorageItem __x_ABI_CWindows_CStorage_CIStorageItem;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIStorageItem ABI::Windows::Storage::IStorageItem
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IStorageItem;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIStorageStreamTransaction_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageStreamTransaction_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIStorageStreamTransaction __x_ABI_CWindows_CStorage_CIStorageStreamTransaction;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIStorageStreamTransaction ABI::Windows::Storage::IStorageStreamTransaction
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IStorageStreamTransaction;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CApplicationData_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CApplicationData_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            class ApplicationData;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CApplicationData __x_ABI_CWindows_CStorage_CApplicationData;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CApplicationData_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CApplicationDataContainer_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CApplicationDataContainer_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            class ApplicationDataContainer;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CApplicationDataContainer __x_ABI_CWindows_CStorage_CApplicationDataContainer;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CApplicationDataContainer_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CKnownFolders_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CKnownFolders_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            class KnownFolders;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CKnownFolders __x_ABI_CWindows_CStorage_CKnownFolders;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CKnownFolders_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CSetVersionDeferral_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CSetVersionDeferral_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            class SetVersionDeferral;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CSetVersionDeferral __x_ABI_CWindows_CStorage_CSetVersionDeferral;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CSetVersionDeferral_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CSetVersionRequest_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CSetVersionRequest_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            class SetVersionRequest;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CSetVersionRequest __x_ABI_CWindows_CStorage_CSetVersionRequest;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CSetVersionRequest_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CStorageFile_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStorageFile_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            class StorageFile;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CStorageFile __x_ABI_CWindows_CStorage_CStorageFile;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CStorageFile_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CStorageFolder_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStorageFolder_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            class StorageFolder;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CStorageFolder __x_ABI_CWindows_CStorage_CStorageFolder;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CStorageFolder_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CStorageStreamTransaction_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStorageStreamTransaction_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            class StorageStreamTransaction;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CStorageStreamTransaction __x_ABI_CWindows_CStorage_CStorageStreamTransaction;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CStorageStreamTransaction_FWD_DEFINED__ */

#ifndef ____FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
#define ____FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
typedef interface __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer;
#ifdef __cplusplus
#define __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
typedef interface __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer;
#ifdef __cplusplus
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIIterable_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CStorage__CIStorageItem __FIIterable_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Storage::IStorageItem* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
typedef interface __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer;
#ifdef __cplusplus
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIIterator_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CStorage__CIStorageItem __FIIterator_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Storage::IStorageItem* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CStorage__CIStorageItem __FIVectorView_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CStorage__CStorageFile __FIVectorView_1_Windows__CStorage__CStorageFile;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CStorage__CStorageFile ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CStorage__CStorageFolder __FIVectorView_1_Windows__CStorage__CStorageFolder;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CStorage__CStorageFolder ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::ApplicationData* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::KnownFoldersAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::IStorageItem* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFile* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFolder* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageStreamTransaction* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CApplicationData_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CApplicationData_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CApplicationData __FIAsyncOperation_1_Windows__CStorage__CApplicationData;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CApplicationData ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::ApplicationData* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_KnownFoldersAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperation_1_KnownFoldersAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_KnownFoldersAccessStatus __FIAsyncOperation_1_KnownFoldersAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperation_1_KnownFoldersAccessStatus ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::KnownFoldersAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CIStorageItem __FIAsyncOperation_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::IStorageItem* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStorageFile __FIAsyncOperation_1_Windows__CStorage__CStorageFile;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFile ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFile* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStorageFolder __FIAsyncOperation_1_Windows__CStorage__CStorageFolder;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFolder ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFolder* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageStreamTransaction* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Storage::ApplicationData*,IInspectable* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.storage.fileproperties.h>
#include <windows.storage.search.h>
#include <windows.storage.streams.h>
#include <windows.system.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CApplicationDataCreateDisposition_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CApplicationDataCreateDisposition_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            enum ApplicationDataCreateDisposition {
                ApplicationDataCreateDisposition_Always = 0,
                ApplicationDataCreateDisposition_Existing = 1
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CApplicationDataCreateDisposition {
    ApplicationDataCreateDisposition_Always = 0,
    ApplicationDataCreateDisposition_Existing = 1
};
#ifdef WIDL_using_Windows_Storage
#define ApplicationDataCreateDisposition __x_ABI_CWindows_CStorage_CApplicationDataCreateDisposition
#endif /* WIDL_using_Windows_Storage */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CApplicationDataCreateDisposition_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CApplicationDataCreateDisposition __x_ABI_CWindows_CStorage_CApplicationDataCreateDisposition;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CApplicationDataLocality_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CApplicationDataLocality_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            enum ApplicationDataLocality {
                ApplicationDataLocality_Local = 0,
                ApplicationDataLocality_Roaming = 1,
                ApplicationDataLocality_Temporary = 2,
                ApplicationDataLocality_LocalCache = 3,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000
                ApplicationDataLocality_SharedLocal = 4
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000 */
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CApplicationDataLocality {
    ApplicationDataLocality_Local = 0,
    ApplicationDataLocality_Roaming = 1,
    ApplicationDataLocality_Temporary = 2,
    ApplicationDataLocality_LocalCache = 3,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000
    ApplicationDataLocality_SharedLocal = 4
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000 */
};
#ifdef WIDL_using_Windows_Storage
#define ApplicationDataLocality __x_ABI_CWindows_CStorage_CApplicationDataLocality
#endif /* WIDL_using_Windows_Storage */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CApplicationDataLocality_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CApplicationDataLocality __x_ABI_CWindows_CStorage_CApplicationDataLocality;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CCreationCollisionOption_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CCreationCollisionOption_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            enum CreationCollisionOption {
                CreationCollisionOption_GenerateUniqueName = 0,
                CreationCollisionOption_ReplaceExisting = 1,
                CreationCollisionOption_FailIfExists = 2,
                CreationCollisionOption_OpenIfExists = 3
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CCreationCollisionOption {
    CreationCollisionOption_GenerateUniqueName = 0,
    CreationCollisionOption_ReplaceExisting = 1,
    CreationCollisionOption_FailIfExists = 2,
    CreationCollisionOption_OpenIfExists = 3
};
#ifdef WIDL_using_Windows_Storage
#define CreationCollisionOption __x_ABI_CWindows_CStorage_CCreationCollisionOption
#endif /* WIDL_using_Windows_Storage */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CCreationCollisionOption_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CCreationCollisionOption __x_ABI_CWindows_CStorage_CCreationCollisionOption;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CFileAccessMode_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CFileAccessMode_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            enum FileAccessMode {
                FileAccessMode_Read = 0,
                FileAccessMode_ReadWrite = 1
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CFileAccessMode {
    FileAccessMode_Read = 0,
    FileAccessMode_ReadWrite = 1
};
#ifdef WIDL_using_Windows_Storage
#define FileAccessMode __x_ABI_CWindows_CStorage_CFileAccessMode
#endif /* WIDL_using_Windows_Storage */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CFileAccessMode_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CFileAccessMode __x_ABI_CWindows_CStorage_CFileAccessMode;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CFileAttributes_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CFileAttributes_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            enum FileAttributes {
                FileAttributes_Normal = 0x0,
                FileAttributes_ReadOnly = 0x1,
                FileAttributes_Directory = 0x10,
                FileAttributes_Archive = 0x20,
                FileAttributes_Temporary = 0x100,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                FileAttributes_LocallyIncomplete = 0x200
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CFileAttributes {
    FileAttributes_Normal = 0x0,
    FileAttributes_ReadOnly = 0x1,
    FileAttributes_Directory = 0x10,
    FileAttributes_Archive = 0x20,
    FileAttributes_Temporary = 0x100,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    FileAttributes_LocallyIncomplete = 0x200
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
};
#ifdef WIDL_using_Windows_Storage
#define FileAttributes __x_ABI_CWindows_CStorage_CFileAttributes
#endif /* WIDL_using_Windows_Storage */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CFileAttributes_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CFileAttributes __x_ABI_CWindows_CStorage_CFileAttributes;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CStorage_CKnownFolderId_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CKnownFolderId_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            enum KnownFolderId {
                KnownFolderId_AppCaptures = 0,
                KnownFolderId_CameraRoll = 1,
                KnownFolderId_DocumentsLibrary = 2,
                KnownFolderId_HomeGroup = 3,
                KnownFolderId_MediaServerDevices = 4,
                KnownFolderId_MusicLibrary = 5,
                KnownFolderId_Objects3D = 6,
                KnownFolderId_PicturesLibrary = 7,
                KnownFolderId_Playlists = 8,
                KnownFolderId_RecordedCalls = 9,
                KnownFolderId_RemovableDevices = 10,
                KnownFolderId_SavedPictures = 11,
                KnownFolderId_Screenshots = 12,
                KnownFolderId_VideosLibrary = 13,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
                KnownFolderId_AllAppMods = 14,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
                KnownFolderId_CurrentAppMods = 15,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000
                KnownFolderId_DownloadsFolder = 16
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000 */
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CKnownFolderId {
    KnownFolderId_AppCaptures = 0,
    KnownFolderId_CameraRoll = 1,
    KnownFolderId_DocumentsLibrary = 2,
    KnownFolderId_HomeGroup = 3,
    KnownFolderId_MediaServerDevices = 4,
    KnownFolderId_MusicLibrary = 5,
    KnownFolderId_Objects3D = 6,
    KnownFolderId_PicturesLibrary = 7,
    KnownFolderId_Playlists = 8,
    KnownFolderId_RecordedCalls = 9,
    KnownFolderId_RemovableDevices = 10,
    KnownFolderId_SavedPictures = 11,
    KnownFolderId_Screenshots = 12,
    KnownFolderId_VideosLibrary = 13,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
    KnownFolderId_AllAppMods = 14,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
    KnownFolderId_CurrentAppMods = 15,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000
    KnownFolderId_DownloadsFolder = 16
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000 */
};
#ifdef WIDL_using_Windows_Storage
#define KnownFolderId __x_ABI_CWindows_CStorage_CKnownFolderId
#endif /* WIDL_using_Windows_Storage */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CKnownFolderId_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CKnownFolderId __x_ABI_CWindows_CStorage_CKnownFolderId;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CStorage_CKnownFoldersAccessStatus_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CKnownFoldersAccessStatus_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            enum KnownFoldersAccessStatus {
                KnownFoldersAccessStatus_DeniedBySystem = 0,
                KnownFoldersAccessStatus_NotDeclaredByApp = 1,
                KnownFoldersAccessStatus_DeniedByUser = 2,
                KnownFoldersAccessStatus_UserPromptRequired = 3,
                KnownFoldersAccessStatus_Allowed = 4,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000
                KnownFoldersAccessStatus_AllowedPerAppFolder = 5
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000 */
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CKnownFoldersAccessStatus {
    KnownFoldersAccessStatus_DeniedBySystem = 0,
    KnownFoldersAccessStatus_NotDeclaredByApp = 1,
    KnownFoldersAccessStatus_DeniedByUser = 2,
    KnownFoldersAccessStatus_UserPromptRequired = 3,
    KnownFoldersAccessStatus_Allowed = 4,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000
    KnownFoldersAccessStatus_AllowedPerAppFolder = 5
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xb0000 */
};
#ifdef WIDL_using_Windows_Storage
#define KnownFoldersAccessStatus __x_ABI_CWindows_CStorage_CKnownFoldersAccessStatus
#endif /* WIDL_using_Windows_Storage */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CKnownFoldersAccessStatus_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CKnownFoldersAccessStatus __x_ABI_CWindows_CStorage_CKnownFoldersAccessStatus;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CNameCollisionOption_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CNameCollisionOption_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            enum NameCollisionOption {
                NameCollisionOption_GenerateUniqueName = 0,
                NameCollisionOption_ReplaceExisting = 1,
                NameCollisionOption_FailIfExists = 2
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CNameCollisionOption {
    NameCollisionOption_GenerateUniqueName = 0,
    NameCollisionOption_ReplaceExisting = 1,
    NameCollisionOption_FailIfExists = 2
};
#ifdef WIDL_using_Windows_Storage
#define NameCollisionOption __x_ABI_CWindows_CStorage_CNameCollisionOption
#endif /* WIDL_using_Windows_Storage */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CNameCollisionOption_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CNameCollisionOption __x_ABI_CWindows_CStorage_CNameCollisionOption;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStorageDeleteOption_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStorageDeleteOption_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            enum StorageDeleteOption {
                StorageDeleteOption_Default = 0,
                StorageDeleteOption_PermanentDelete = 1
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CStorageDeleteOption {
    StorageDeleteOption_Default = 0,
    StorageDeleteOption_PermanentDelete = 1
};
#ifdef WIDL_using_Windows_Storage
#define StorageDeleteOption __x_ABI_CWindows_CStorage_CStorageDeleteOption
#endif /* WIDL_using_Windows_Storage */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CStorageDeleteOption_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CStorageDeleteOption __x_ABI_CWindows_CStorage_CStorageDeleteOption;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStorageItemTypes_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStorageItemTypes_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            enum StorageItemTypes {
                StorageItemTypes_None = 0x0,
                StorageItemTypes_File = 0x1,
                StorageItemTypes_Folder = 0x2
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CStorageItemTypes {
    StorageItemTypes_None = 0x0,
    StorageItemTypes_File = 0x1,
    StorageItemTypes_Folder = 0x2
};
#ifdef WIDL_using_Windows_Storage
#define StorageItemTypes __x_ABI_CWindows_CStorage_CStorageItemTypes
#endif /* WIDL_using_Windows_Storage */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CStorageItemTypes_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CStorageItemTypes __x_ABI_CWindows_CStorage_CStorageItemTypes;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationData_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationData_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationData __x_ABI_CWindows_CStorage_CIApplicationData;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationData ABI::Windows::Storage::IApplicationData
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationData;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationData2_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationData2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationData2 __x_ABI_CWindows_CStorage_CIApplicationData2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationData2 ABI::Windows::Storage::IApplicationData2
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationData2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationData3_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationData3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationData3 __x_ABI_CWindows_CStorage_CIApplicationData3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationData3 ABI::Windows::Storage::IApplicationData3
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationData3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationDataContainer_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationDataContainer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationDataContainer __x_ABI_CWindows_CStorage_CIApplicationDataContainer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer ABI::Windows::Storage::IApplicationDataContainer
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationDataContainer;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationDataStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationDataStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationDataStatics __x_ABI_CWindows_CStorage_CIApplicationDataStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics ABI::Windows::Storage::IApplicationDataStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationDataStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIApplicationDataStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationDataStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 __x_ABI_CWindows_CStorage_CIApplicationDataStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 ABI::Windows::Storage::IApplicationDataStatics2
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IApplicationDataStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics ABI::Windows::Storage::IKnownFoldersCameraRollStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersCameraRollStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics ABI::Windows::Storage::IKnownFoldersPlaylistsStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersPlaylistsStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics ABI::Windows::Storage::IKnownFoldersSavedPicturesStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersSavedPicturesStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics __x_ABI_CWindows_CStorage_CIKnownFoldersStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics ABI::Windows::Storage::IKnownFoldersStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 ABI::Windows::Storage::IKnownFoldersStatics2
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 ABI::Windows::Storage::IKnownFoldersStatics3
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersStatics3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 ABI::Windows::Storage::IKnownFoldersStatics4
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IKnownFoldersStatics4;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CISetVersionDeferral_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CISetVersionDeferral_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CISetVersionDeferral __x_ABI_CWindows_CStorage_CISetVersionDeferral;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CISetVersionDeferral ABI::Windows::Storage::ISetVersionDeferral
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface ISetVersionDeferral;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CISetVersionRequest_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CISetVersionRequest_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CISetVersionRequest __x_ABI_CWindows_CStorage_CISetVersionRequest;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CISetVersionRequest ABI::Windows::Storage::ISetVersionRequest
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface ISetVersionRequest;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIStorageFolder_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageFolder_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIStorageFolder __x_ABI_CWindows_CStorage_CIStorageFolder;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIStorageFolder ABI::Windows::Storage::IStorageFolder
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IStorageFolder;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIStorageFolderStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageFolderStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIStorageFolderStatics __x_ABI_CWindows_CStorage_CIStorageFolderStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIStorageFolderStatics ABI::Windows::Storage::IStorageFolderStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IStorageFolderStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIStorageFolderStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageFolderStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIStorageFolderStatics2 __x_ABI_CWindows_CStorage_CIStorageFolderStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIStorageFolderStatics2 ABI::Windows::Storage::IStorageFolderStatics2
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IStorageFolderStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIStorageFileStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageFileStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIStorageFileStatics __x_ABI_CWindows_CStorage_CIStorageFileStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIStorageFileStatics ABI::Windows::Storage::IStorageFileStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IStorageFileStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIStorageFileStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageFileStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIStorageFileStatics2 __x_ABI_CWindows_CStorage_CIStorageFileStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIStorageFileStatics2 ABI::Windows::Storage::IStorageFileStatics2
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IStorageFileStatics2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIStorageItem_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageItem_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIStorageItem __x_ABI_CWindows_CStorage_CIStorageItem;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIStorageItem ABI::Windows::Storage::IStorageItem
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IStorageItem;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
#define ____FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
typedef interface __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer;
#ifdef __cplusplus
#define __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
typedef interface __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer;
#ifdef __cplusplus
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIIterable_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CStorage__CIStorageItem __FIIterable_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Storage::IStorageItem* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
typedef interface __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer;
#ifdef __cplusplus
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIIterator_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CStorage__CIStorageItem __FIIterator_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Storage::IStorageItem* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CStorage__CIStorageItem __FIVectorView_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CStorage__CStorageFile __FIVectorView_1_Windows__CStorage__CStorageFile;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CStorage__CStorageFile ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CStorage__CStorageFolder __FIVectorView_1_Windows__CStorage__CStorageFolder;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CStorage__CStorageFolder ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CApplicationData_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CApplicationData_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CApplicationData __FIAsyncOperation_1_Windows__CStorage__CApplicationData;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CApplicationData ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::ApplicationData* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_KnownFoldersAccessStatus_FWD_DEFINED__
#define ____FIAsyncOperation_1_KnownFoldersAccessStatus_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_KnownFoldersAccessStatus __FIAsyncOperation_1_KnownFoldersAccessStatus;
#ifdef __cplusplus
#define __FIAsyncOperation_1_KnownFoldersAccessStatus ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::KnownFoldersAccessStatus >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CIStorageItem_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CIStorageItem __FIAsyncOperation_1_Windows__CStorage__CIStorageItem;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CIStorageItem ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::IStorageItem* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStorageFile_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStorageFile __FIAsyncOperation_1_Windows__CStorage__CStorageFile;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFile ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFile* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStorageFolder_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStorageFolder __FIAsyncOperation_1_Windows__CStorage__CStorageFolder;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFolder ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFolder* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageStreamTransaction* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IApplicationDataSetVersionHandler interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler, 0xa05791e6, 0xcc9f, 0x4687, 0xac,0xab, 0xa3,0x64,0xfd,0x78,0x54,0x63);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("a05791e6-cc9f-4687-acab-a364fd785463")
            IApplicationDataSetVersionHandler : public IUnknown
            {
                virtual HRESULT STDMETHODCALLTYPE Invoke(
                    ABI::Windows::Storage::ISetVersionRequest *version) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler, 0xa05791e6, 0xcc9f, 0x4687, 0xac,0xab, 0xa3,0x64,0xfd,0x78,0x54,0x63)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler *This);

    /*** IApplicationDataSetVersionHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler *This,
        __x_ABI_CWindows_CStorage_CISetVersionRequest *version);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandlerVtbl;

interface __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IApplicationDataSetVersionHandler methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_Invoke(This,version) (This)->lpVtbl->Invoke(This,version)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_QueryInterface(__x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_AddRef(__x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_Release(__x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IApplicationDataSetVersionHandler methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_Invoke(__x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler* This,__x_ABI_CWindows_CStorage_CISetVersionRequest *version) {
    return This->lpVtbl->Invoke(This,version);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IApplicationDataSetVersionHandler IID___x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler
#define IApplicationDataSetVersionHandlerVtbl __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandlerVtbl
#define IApplicationDataSetVersionHandler __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler
#define IApplicationDataSetVersionHandler_QueryInterface __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_QueryInterface
#define IApplicationDataSetVersionHandler_AddRef __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_AddRef
#define IApplicationDataSetVersionHandler_Release __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_Release
#define IApplicationDataSetVersionHandler_Invoke __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_Invoke
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IApplicationData interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIApplicationData_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationData_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIApplicationData, 0xc3da6fb7, 0xb744, 0x4b45, 0xb0,0xb8, 0x22,0x3a,0x09,0x38,0xd0,0xdc);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("c3da6fb7-b744-4b45-b0b8-223a0938d0dc")
            IApplicationData : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Version(
                    UINT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE SetVersionAsync(
                    UINT32 version,
                    ABI::Windows::Storage::IApplicationDataSetVersionHandler *handler,
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE ClearAllAsync(
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE ClearAsync(
                    ABI::Windows::Storage::ApplicationDataLocality locality,
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LocalSettings(
                    ABI::Windows::Storage::IApplicationDataContainer **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_RoamingSettings(
                    ABI::Windows::Storage::IApplicationDataContainer **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_LocalFolder(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_RoamingFolder(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_TemporaryFolder(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE add_DataChanged(
                    ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Storage::ApplicationData*,IInspectable* > *handler,
                    EventRegistrationToken *token) = 0;

                virtual HRESULT STDMETHODCALLTYPE remove_DataChanged(
                    EventRegistrationToken token) = 0;

                virtual HRESULT STDMETHODCALLTYPE SignalDataChanged(
                    ) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_RoamingStorageQuota(
                    UINT64 *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIApplicationData, 0xc3da6fb7, 0xb744, 0x4b45, 0xb0,0xb8, 0x22,0x3a,0x09,0x38,0xd0,0xdc)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIApplicationDataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        TrustLevel *trustLevel);

    /*** IApplicationData methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Version)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *SetVersionAsync)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        UINT32 version,
        __x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler *handler,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *ClearAllAsync)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *ClearAsync)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        __x_ABI_CWindows_CStorage_CApplicationDataLocality locality,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *get_LocalSettings)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer **value);

    HRESULT (STDMETHODCALLTYPE *get_RoamingSettings)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer **value);

    HRESULT (STDMETHODCALLTYPE *get_LocalFolder)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_RoamingFolder)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_TemporaryFolder)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *add_DataChanged)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_DataChanged)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *SignalDataChanged)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This);

    HRESULT (STDMETHODCALLTYPE *get_RoamingStorageQuota)(
        __x_ABI_CWindows_CStorage_CIApplicationData *This,
        UINT64 *value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIApplicationDataVtbl;

interface __x_ABI_CWindows_CStorage_CIApplicationData {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIApplicationDataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIApplicationData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIApplicationData_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationData_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIApplicationData_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIApplicationData_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IApplicationData methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationData_get_Version(This,value) (This)->lpVtbl->get_Version(This,value)
#define __x_ABI_CWindows_CStorage_CIApplicationData_SetVersionAsync(This,version,handler,operation) (This)->lpVtbl->SetVersionAsync(This,version,handler,operation)
#define __x_ABI_CWindows_CStorage_CIApplicationData_ClearAllAsync(This,operation) (This)->lpVtbl->ClearAllAsync(This,operation)
#define __x_ABI_CWindows_CStorage_CIApplicationData_ClearAsync(This,locality,operation) (This)->lpVtbl->ClearAsync(This,locality,operation)
#define __x_ABI_CWindows_CStorage_CIApplicationData_get_LocalSettings(This,value) (This)->lpVtbl->get_LocalSettings(This,value)
#define __x_ABI_CWindows_CStorage_CIApplicationData_get_RoamingSettings(This,value) (This)->lpVtbl->get_RoamingSettings(This,value)
#define __x_ABI_CWindows_CStorage_CIApplicationData_get_LocalFolder(This,value) (This)->lpVtbl->get_LocalFolder(This,value)
#define __x_ABI_CWindows_CStorage_CIApplicationData_get_RoamingFolder(This,value) (This)->lpVtbl->get_RoamingFolder(This,value)
#define __x_ABI_CWindows_CStorage_CIApplicationData_get_TemporaryFolder(This,value) (This)->lpVtbl->get_TemporaryFolder(This,value)
#define __x_ABI_CWindows_CStorage_CIApplicationData_add_DataChanged(This,handler,token) (This)->lpVtbl->add_DataChanged(This,handler,token)
#define __x_ABI_CWindows_CStorage_CIApplicationData_remove_DataChanged(This,token) (This)->lpVtbl->remove_DataChanged(This,token)
#define __x_ABI_CWindows_CStorage_CIApplicationData_SignalDataChanged(This) (This)->lpVtbl->SignalDataChanged(This)
#define __x_ABI_CWindows_CStorage_CIApplicationData_get_RoamingStorageQuota(This,value) (This)->lpVtbl->get_RoamingStorageQuota(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_QueryInterface(__x_ABI_CWindows_CStorage_CIApplicationData* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationData_AddRef(__x_ABI_CWindows_CStorage_CIApplicationData* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationData_Release(__x_ABI_CWindows_CStorage_CIApplicationData* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_GetIids(__x_ABI_CWindows_CStorage_CIApplicationData* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIApplicationData* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_GetTrustLevel(__x_ABI_CWindows_CStorage_CIApplicationData* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IApplicationData methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_get_Version(__x_ABI_CWindows_CStorage_CIApplicationData* This,UINT32 *value) {
    return This->lpVtbl->get_Version(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_SetVersionAsync(__x_ABI_CWindows_CStorage_CIApplicationData* This,UINT32 version,__x_ABI_CWindows_CStorage_CIApplicationDataSetVersionHandler *handler,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->SetVersionAsync(This,version,handler,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_ClearAllAsync(__x_ABI_CWindows_CStorage_CIApplicationData* This,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->ClearAllAsync(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_ClearAsync(__x_ABI_CWindows_CStorage_CIApplicationData* This,__x_ABI_CWindows_CStorage_CApplicationDataLocality locality,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->ClearAsync(This,locality,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_get_LocalSettings(__x_ABI_CWindows_CStorage_CIApplicationData* This,__x_ABI_CWindows_CStorage_CIApplicationDataContainer **value) {
    return This->lpVtbl->get_LocalSettings(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_get_RoamingSettings(__x_ABI_CWindows_CStorage_CIApplicationData* This,__x_ABI_CWindows_CStorage_CIApplicationDataContainer **value) {
    return This->lpVtbl->get_RoamingSettings(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_get_LocalFolder(__x_ABI_CWindows_CStorage_CIApplicationData* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_LocalFolder(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_get_RoamingFolder(__x_ABI_CWindows_CStorage_CIApplicationData* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_RoamingFolder(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_get_TemporaryFolder(__x_ABI_CWindows_CStorage_CIApplicationData* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_TemporaryFolder(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_add_DataChanged(__x_ABI_CWindows_CStorage_CIApplicationData* This,__FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_DataChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_remove_DataChanged(__x_ABI_CWindows_CStorage_CIApplicationData* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_DataChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_SignalDataChanged(__x_ABI_CWindows_CStorage_CIApplicationData* This) {
    return This->lpVtbl->SignalDataChanged(This);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData_get_RoamingStorageQuota(__x_ABI_CWindows_CStorage_CIApplicationData* This,UINT64 *value) {
    return This->lpVtbl->get_RoamingStorageQuota(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IApplicationData IID___x_ABI_CWindows_CStorage_CIApplicationData
#define IApplicationDataVtbl __x_ABI_CWindows_CStorage_CIApplicationDataVtbl
#define IApplicationData __x_ABI_CWindows_CStorage_CIApplicationData
#define IApplicationData_QueryInterface __x_ABI_CWindows_CStorage_CIApplicationData_QueryInterface
#define IApplicationData_AddRef __x_ABI_CWindows_CStorage_CIApplicationData_AddRef
#define IApplicationData_Release __x_ABI_CWindows_CStorage_CIApplicationData_Release
#define IApplicationData_GetIids __x_ABI_CWindows_CStorage_CIApplicationData_GetIids
#define IApplicationData_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIApplicationData_GetRuntimeClassName
#define IApplicationData_GetTrustLevel __x_ABI_CWindows_CStorage_CIApplicationData_GetTrustLevel
#define IApplicationData_get_Version __x_ABI_CWindows_CStorage_CIApplicationData_get_Version
#define IApplicationData_SetVersionAsync __x_ABI_CWindows_CStorage_CIApplicationData_SetVersionAsync
#define IApplicationData_ClearAllAsync __x_ABI_CWindows_CStorage_CIApplicationData_ClearAllAsync
#define IApplicationData_ClearAsync __x_ABI_CWindows_CStorage_CIApplicationData_ClearAsync
#define IApplicationData_get_LocalSettings __x_ABI_CWindows_CStorage_CIApplicationData_get_LocalSettings
#define IApplicationData_get_RoamingSettings __x_ABI_CWindows_CStorage_CIApplicationData_get_RoamingSettings
#define IApplicationData_get_LocalFolder __x_ABI_CWindows_CStorage_CIApplicationData_get_LocalFolder
#define IApplicationData_get_RoamingFolder __x_ABI_CWindows_CStorage_CIApplicationData_get_RoamingFolder
#define IApplicationData_get_TemporaryFolder __x_ABI_CWindows_CStorage_CIApplicationData_get_TemporaryFolder
#define IApplicationData_add_DataChanged __x_ABI_CWindows_CStorage_CIApplicationData_add_DataChanged
#define IApplicationData_remove_DataChanged __x_ABI_CWindows_CStorage_CIApplicationData_remove_DataChanged
#define IApplicationData_SignalDataChanged __x_ABI_CWindows_CStorage_CIApplicationData_SignalDataChanged
#define IApplicationData_get_RoamingStorageQuota __x_ABI_CWindows_CStorage_CIApplicationData_get_RoamingStorageQuota
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIApplicationData_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IApplicationData2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIApplicationData2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationData2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIApplicationData2, 0x9e65cd69, 0x0ba3, 0x4e32, 0xbe,0x29, 0xb0,0x2d,0xe6,0x60,0x76,0x38);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("9e65cd69-0ba3-4e32-be29-b02de6607638")
            IApplicationData2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_LocalCacheFolder(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIApplicationData2, 0x9e65cd69, 0x0ba3, 0x4e32, 0xbe,0x29, 0xb0,0x2d,0xe6,0x60,0x76,0x38)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIApplicationData2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIApplicationData2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIApplicationData2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIApplicationData2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIApplicationData2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIApplicationData2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIApplicationData2 *This,
        TrustLevel *trustLevel);

    /*** IApplicationData2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_LocalCacheFolder)(
        __x_ABI_CWindows_CStorage_CIApplicationData2 *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIApplicationData2Vtbl;

interface __x_ABI_CWindows_CStorage_CIApplicationData2 {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIApplicationData2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationData2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIApplicationData2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIApplicationData2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationData2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIApplicationData2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIApplicationData2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IApplicationData2 methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationData2_get_LocalCacheFolder(This,value) (This)->lpVtbl->get_LocalCacheFolder(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData2_QueryInterface(__x_ABI_CWindows_CStorage_CIApplicationData2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationData2_AddRef(__x_ABI_CWindows_CStorage_CIApplicationData2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationData2_Release(__x_ABI_CWindows_CStorage_CIApplicationData2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData2_GetIids(__x_ABI_CWindows_CStorage_CIApplicationData2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData2_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIApplicationData2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData2_GetTrustLevel(__x_ABI_CWindows_CStorage_CIApplicationData2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IApplicationData2 methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData2_get_LocalCacheFolder(__x_ABI_CWindows_CStorage_CIApplicationData2* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_LocalCacheFolder(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IApplicationData2 IID___x_ABI_CWindows_CStorage_CIApplicationData2
#define IApplicationData2Vtbl __x_ABI_CWindows_CStorage_CIApplicationData2Vtbl
#define IApplicationData2 __x_ABI_CWindows_CStorage_CIApplicationData2
#define IApplicationData2_QueryInterface __x_ABI_CWindows_CStorage_CIApplicationData2_QueryInterface
#define IApplicationData2_AddRef __x_ABI_CWindows_CStorage_CIApplicationData2_AddRef
#define IApplicationData2_Release __x_ABI_CWindows_CStorage_CIApplicationData2_Release
#define IApplicationData2_GetIids __x_ABI_CWindows_CStorage_CIApplicationData2_GetIids
#define IApplicationData2_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIApplicationData2_GetRuntimeClassName
#define IApplicationData2_GetTrustLevel __x_ABI_CWindows_CStorage_CIApplicationData2_GetTrustLevel
#define IApplicationData2_get_LocalCacheFolder __x_ABI_CWindows_CStorage_CIApplicationData2_get_LocalCacheFolder
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIApplicationData2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IApplicationData3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIApplicationData3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationData3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIApplicationData3, 0xdc222cf4, 0x2772, 0x4c1d, 0xaa,0x2c, 0xc9,0xf7,0x43,0xad,0xe8,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("dc222cf4-2772-4c1d-aa2c-c9f743ade8d1")
            IApplicationData3 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetPublisherCacheFolder(
                    HSTRING folder_name,
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE ClearPublisherCacheFolderAsync(
                    HSTRING folder_name,
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_SharedLocalFolder(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIApplicationData3, 0xdc222cf4, 0x2772, 0x4c1d, 0xaa,0x2c, 0xc9,0xf7,0x43,0xad,0xe8,0xd1)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIApplicationData3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIApplicationData3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIApplicationData3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIApplicationData3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIApplicationData3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIApplicationData3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIApplicationData3 *This,
        TrustLevel *trustLevel);

    /*** IApplicationData3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPublisherCacheFolder)(
        __x_ABI_CWindows_CStorage_CIApplicationData3 *This,
        HSTRING folder_name,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *ClearPublisherCacheFolderAsync)(
        __x_ABI_CWindows_CStorage_CIApplicationData3 *This,
        HSTRING folder_name,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *get_SharedLocalFolder)(
        __x_ABI_CWindows_CStorage_CIApplicationData3 *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIApplicationData3Vtbl;

interface __x_ABI_CWindows_CStorage_CIApplicationData3 {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIApplicationData3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationData3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIApplicationData3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIApplicationData3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationData3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIApplicationData3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIApplicationData3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IApplicationData3 methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationData3_GetPublisherCacheFolder(This,folder_name,value) (This)->lpVtbl->GetPublisherCacheFolder(This,folder_name,value)
#define __x_ABI_CWindows_CStorage_CIApplicationData3_ClearPublisherCacheFolderAsync(This,folder_name,operation) (This)->lpVtbl->ClearPublisherCacheFolderAsync(This,folder_name,operation)
#define __x_ABI_CWindows_CStorage_CIApplicationData3_get_SharedLocalFolder(This,value) (This)->lpVtbl->get_SharedLocalFolder(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData3_QueryInterface(__x_ABI_CWindows_CStorage_CIApplicationData3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationData3_AddRef(__x_ABI_CWindows_CStorage_CIApplicationData3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationData3_Release(__x_ABI_CWindows_CStorage_CIApplicationData3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData3_GetIids(__x_ABI_CWindows_CStorage_CIApplicationData3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData3_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIApplicationData3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData3_GetTrustLevel(__x_ABI_CWindows_CStorage_CIApplicationData3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IApplicationData3 methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData3_GetPublisherCacheFolder(__x_ABI_CWindows_CStorage_CIApplicationData3* This,HSTRING folder_name,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->GetPublisherCacheFolder(This,folder_name,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData3_ClearPublisherCacheFolderAsync(__x_ABI_CWindows_CStorage_CIApplicationData3* This,HSTRING folder_name,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->ClearPublisherCacheFolderAsync(This,folder_name,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationData3_get_SharedLocalFolder(__x_ABI_CWindows_CStorage_CIApplicationData3* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_SharedLocalFolder(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IApplicationData3 IID___x_ABI_CWindows_CStorage_CIApplicationData3
#define IApplicationData3Vtbl __x_ABI_CWindows_CStorage_CIApplicationData3Vtbl
#define IApplicationData3 __x_ABI_CWindows_CStorage_CIApplicationData3
#define IApplicationData3_QueryInterface __x_ABI_CWindows_CStorage_CIApplicationData3_QueryInterface
#define IApplicationData3_AddRef __x_ABI_CWindows_CStorage_CIApplicationData3_AddRef
#define IApplicationData3_Release __x_ABI_CWindows_CStorage_CIApplicationData3_Release
#define IApplicationData3_GetIids __x_ABI_CWindows_CStorage_CIApplicationData3_GetIids
#define IApplicationData3_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIApplicationData3_GetRuntimeClassName
#define IApplicationData3_GetTrustLevel __x_ABI_CWindows_CStorage_CIApplicationData3_GetTrustLevel
#define IApplicationData3_GetPublisherCacheFolder __x_ABI_CWindows_CStorage_CIApplicationData3_GetPublisherCacheFolder
#define IApplicationData3_ClearPublisherCacheFolderAsync __x_ABI_CWindows_CStorage_CIApplicationData3_ClearPublisherCacheFolderAsync
#define IApplicationData3_get_SharedLocalFolder __x_ABI_CWindows_CStorage_CIApplicationData3_get_SharedLocalFolder
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIApplicationData3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IApplicationDataContainer interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIApplicationDataContainer_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationDataContainer_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIApplicationDataContainer, 0xc5aefd1e, 0xf467, 0x40ba, 0x85,0x66, 0xab,0x64,0x0a,0x44,0x1e,0x1d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("c5aefd1e-f467-40ba-8566-ab640a441e1d")
            IApplicationDataContainer : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Name(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Locality(
                    ABI::Windows::Storage::ApplicationDataLocality *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Values(
                    ABI::Windows::Foundation::Collections::IPropertySet **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Containers(
                    ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* > **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE CreateContainer(
                    HSTRING name,
                    ABI::Windows::Storage::ApplicationDataCreateDisposition disposition,
                    ABI::Windows::Storage::IApplicationDataContainer **container) = 0;

                virtual HRESULT STDMETHODCALLTYPE DeleteContainer(
                    HSTRING name) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIApplicationDataContainer, 0xc5aefd1e, 0xf467, 0x40ba, 0x85,0x66, 0xab,0x64,0x0a,0x44,0x1e,0x1d)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIApplicationDataContainerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This,
        TrustLevel *trustLevel);

    /*** IApplicationDataContainer methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Name)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Locality)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This,
        __x_ABI_CWindows_CStorage_CApplicationDataLocality *value);

    HRESULT (STDMETHODCALLTYPE *get_Values)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This,
        __x_ABI_CWindows_CFoundation_CCollections_CIPropertySet **value);

    HRESULT (STDMETHODCALLTYPE *get_Containers)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This,
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer **value);

    HRESULT (STDMETHODCALLTYPE *CreateContainer)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This,
        HSTRING name,
        __x_ABI_CWindows_CStorage_CApplicationDataCreateDisposition disposition,
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer **container);

    HRESULT (STDMETHODCALLTYPE *DeleteContainer)(
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer *This,
        HSTRING name);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIApplicationDataContainerVtbl;

interface __x_ABI_CWindows_CStorage_CIApplicationDataContainer {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIApplicationDataContainerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IApplicationDataContainer methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Name(This,value) (This)->lpVtbl->get_Name(This,value)
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Locality(This,value) (This)->lpVtbl->get_Locality(This,value)
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Values(This,value) (This)->lpVtbl->get_Values(This,value)
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Containers(This,value) (This)->lpVtbl->get_Containers(This,value)
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_CreateContainer(This,name,disposition,container) (This)->lpVtbl->CreateContainer(This,name,disposition,container)
#define __x_ABI_CWindows_CStorage_CIApplicationDataContainer_DeleteContainer(This,name) (This)->lpVtbl->DeleteContainer(This,name)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataContainer_QueryInterface(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationDataContainer_AddRef(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationDataContainer_Release(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataContainer_GetIids(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataContainer_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataContainer_GetTrustLevel(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IApplicationDataContainer methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Name(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This,HSTRING *value) {
    return This->lpVtbl->get_Name(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Locality(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This,__x_ABI_CWindows_CStorage_CApplicationDataLocality *value) {
    return This->lpVtbl->get_Locality(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Values(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This,__x_ABI_CWindows_CFoundation_CCollections_CIPropertySet **value) {
    return This->lpVtbl->get_Values(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Containers(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This,__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer **value) {
    return This->lpVtbl->get_Containers(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataContainer_CreateContainer(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This,HSTRING name,__x_ABI_CWindows_CStorage_CApplicationDataCreateDisposition disposition,__x_ABI_CWindows_CStorage_CIApplicationDataContainer **container) {
    return This->lpVtbl->CreateContainer(This,name,disposition,container);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataContainer_DeleteContainer(__x_ABI_CWindows_CStorage_CIApplicationDataContainer* This,HSTRING name) {
    return This->lpVtbl->DeleteContainer(This,name);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IApplicationDataContainer IID___x_ABI_CWindows_CStorage_CIApplicationDataContainer
#define IApplicationDataContainerVtbl __x_ABI_CWindows_CStorage_CIApplicationDataContainerVtbl
#define IApplicationDataContainer __x_ABI_CWindows_CStorage_CIApplicationDataContainer
#define IApplicationDataContainer_QueryInterface __x_ABI_CWindows_CStorage_CIApplicationDataContainer_QueryInterface
#define IApplicationDataContainer_AddRef __x_ABI_CWindows_CStorage_CIApplicationDataContainer_AddRef
#define IApplicationDataContainer_Release __x_ABI_CWindows_CStorage_CIApplicationDataContainer_Release
#define IApplicationDataContainer_GetIids __x_ABI_CWindows_CStorage_CIApplicationDataContainer_GetIids
#define IApplicationDataContainer_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIApplicationDataContainer_GetRuntimeClassName
#define IApplicationDataContainer_GetTrustLevel __x_ABI_CWindows_CStorage_CIApplicationDataContainer_GetTrustLevel
#define IApplicationDataContainer_get_Name __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Name
#define IApplicationDataContainer_get_Locality __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Locality
#define IApplicationDataContainer_get_Values __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Values
#define IApplicationDataContainer_get_Containers __x_ABI_CWindows_CStorage_CIApplicationDataContainer_get_Containers
#define IApplicationDataContainer_CreateContainer __x_ABI_CWindows_CStorage_CIApplicationDataContainer_CreateContainer
#define IApplicationDataContainer_DeleteContainer __x_ABI_CWindows_CStorage_CIApplicationDataContainer_DeleteContainer
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIApplicationDataContainer_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IApplicationDataStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIApplicationDataStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationDataStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIApplicationDataStatics, 0x5612147b, 0xe843, 0x45e3, 0x94,0xd8, 0x06,0x16,0x9e,0x3c,0x8e,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("5612147b-e843-45e3-94d8-06169e3c8e17")
            IApplicationDataStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Current(
                    ABI::Windows::Storage::IApplicationData **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIApplicationDataStatics, 0x5612147b, 0xe843, 0x45e3, 0x94,0xd8, 0x06,0x16,0x9e,0x3c,0x8e,0x17)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIApplicationDataStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics *This,
        TrustLevel *trustLevel);

    /*** IApplicationDataStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics *This,
        __x_ABI_CWindows_CStorage_CIApplicationData **value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIApplicationDataStaticsVtbl;

interface __x_ABI_CWindows_CStorage_CIApplicationDataStatics {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIApplicationDataStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IApplicationDataStatics methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataStatics_QueryInterface(__x_ABI_CWindows_CStorage_CIApplicationDataStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationDataStatics_AddRef(__x_ABI_CWindows_CStorage_CIApplicationDataStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationDataStatics_Release(__x_ABI_CWindows_CStorage_CIApplicationDataStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataStatics_GetIids(__x_ABI_CWindows_CStorage_CIApplicationDataStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataStatics_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIApplicationDataStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataStatics_GetTrustLevel(__x_ABI_CWindows_CStorage_CIApplicationDataStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IApplicationDataStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataStatics_get_Current(__x_ABI_CWindows_CStorage_CIApplicationDataStatics* This,__x_ABI_CWindows_CStorage_CIApplicationData **value) {
    return This->lpVtbl->get_Current(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IApplicationDataStatics IID___x_ABI_CWindows_CStorage_CIApplicationDataStatics
#define IApplicationDataStaticsVtbl __x_ABI_CWindows_CStorage_CIApplicationDataStaticsVtbl
#define IApplicationDataStatics __x_ABI_CWindows_CStorage_CIApplicationDataStatics
#define IApplicationDataStatics_QueryInterface __x_ABI_CWindows_CStorage_CIApplicationDataStatics_QueryInterface
#define IApplicationDataStatics_AddRef __x_ABI_CWindows_CStorage_CIApplicationDataStatics_AddRef
#define IApplicationDataStatics_Release __x_ABI_CWindows_CStorage_CIApplicationDataStatics_Release
#define IApplicationDataStatics_GetIids __x_ABI_CWindows_CStorage_CIApplicationDataStatics_GetIids
#define IApplicationDataStatics_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIApplicationDataStatics_GetRuntimeClassName
#define IApplicationDataStatics_GetTrustLevel __x_ABI_CWindows_CStorage_CIApplicationDataStatics_GetTrustLevel
#define IApplicationDataStatics_get_Current __x_ABI_CWindows_CStorage_CIApplicationDataStatics_get_Current
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIApplicationDataStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IApplicationDataStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIApplicationDataStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIApplicationDataStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIApplicationDataStatics2, 0xcd606211, 0xcf49, 0x40a4, 0xa4,0x7c, 0xc7,0xf0,0xdb,0xba,0x81,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("cd606211-cf49-40a4-a47c-c7f0dbba8107")
            IApplicationDataStatics2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetForUserAsync(
                    ABI::Windows::System::IUser *user,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::ApplicationData* > **operation) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIApplicationDataStatics2, 0xcd606211, 0xcf49, 0x40a4, 0xa4,0x7c, 0xc7,0xf0,0xdb,0xba,0x81,0x07)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIApplicationDataStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 *This,
        TrustLevel *trustLevel);

    /*** IApplicationDataStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetForUserAsync)(
        __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 *This,
        __x_ABI_CWindows_CSystem_CIUser *user,
        __FIAsyncOperation_1_Windows__CStorage__CApplicationData **operation);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIApplicationDataStatics2Vtbl;

interface __x_ABI_CWindows_CStorage_CIApplicationDataStatics2 {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIApplicationDataStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IApplicationDataStatics2 methods ***/
#define __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetForUserAsync(This,user,operation) (This)->lpVtbl->GetForUserAsync(This,user,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_QueryInterface(__x_ABI_CWindows_CStorage_CIApplicationDataStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_AddRef(__x_ABI_CWindows_CStorage_CIApplicationDataStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_Release(__x_ABI_CWindows_CStorage_CIApplicationDataStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetIids(__x_ABI_CWindows_CStorage_CIApplicationDataStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIApplicationDataStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetTrustLevel(__x_ABI_CWindows_CStorage_CIApplicationDataStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IApplicationDataStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetForUserAsync(__x_ABI_CWindows_CStorage_CIApplicationDataStatics2* This,__x_ABI_CWindows_CSystem_CIUser *user,__FIAsyncOperation_1_Windows__CStorage__CApplicationData **operation) {
    return This->lpVtbl->GetForUserAsync(This,user,operation);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IApplicationDataStatics2 IID___x_ABI_CWindows_CStorage_CIApplicationDataStatics2
#define IApplicationDataStatics2Vtbl __x_ABI_CWindows_CStorage_CIApplicationDataStatics2Vtbl
#define IApplicationDataStatics2 __x_ABI_CWindows_CStorage_CIApplicationDataStatics2
#define IApplicationDataStatics2_QueryInterface __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_QueryInterface
#define IApplicationDataStatics2_AddRef __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_AddRef
#define IApplicationDataStatics2_Release __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_Release
#define IApplicationDataStatics2_GetIids __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetIids
#define IApplicationDataStatics2_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetRuntimeClassName
#define IApplicationDataStatics2_GetTrustLevel __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetTrustLevel
#define IApplicationDataStatics2_GetForUserAsync __x_ABI_CWindows_CStorage_CIApplicationDataStatics2_GetForUserAsync
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIApplicationDataStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IKnownFoldersCameraRollStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics, 0x5d115e66, 0x27e8, 0x492f, 0xb8,0xe5, 0x2f,0x90,0x89,0x6c,0xd4,0xcd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("5d115e66-27e8-492f-b8e5-2f90896cd4cd")
            IKnownFoldersCameraRollStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_CameraRoll(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics, 0x5d115e66, 0x27e8, 0x492f, 0xb8,0xe5, 0x2f,0x90,0x89,0x6c,0xd4,0xcd)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics *This,
        TrustLevel *trustLevel);

    /*** IKnownFoldersCameraRollStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CameraRoll)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStaticsVtbl;

interface __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKnownFoldersCameraRollStatics methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_get_CameraRoll(This,value) (This)->lpVtbl->get_CameraRoll(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_QueryInterface(__x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_AddRef(__x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_Release(__x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_GetIids(__x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_GetTrustLevel(__x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKnownFoldersCameraRollStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_get_CameraRoll(__x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_CameraRoll(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IKnownFoldersCameraRollStatics IID___x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics
#define IKnownFoldersCameraRollStaticsVtbl __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStaticsVtbl
#define IKnownFoldersCameraRollStatics __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics
#define IKnownFoldersCameraRollStatics_QueryInterface __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_QueryInterface
#define IKnownFoldersCameraRollStatics_AddRef __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_AddRef
#define IKnownFoldersCameraRollStatics_Release __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_Release
#define IKnownFoldersCameraRollStatics_GetIids __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_GetIids
#define IKnownFoldersCameraRollStatics_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_GetRuntimeClassName
#define IKnownFoldersCameraRollStatics_GetTrustLevel __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_GetTrustLevel
#define IKnownFoldersCameraRollStatics_get_CameraRoll __x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_get_CameraRoll
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIKnownFoldersCameraRollStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IKnownFoldersPlaylistsStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics, 0xdad5ecd6, 0x306f, 0x4d6a, 0xb4,0x96, 0x46,0xba,0x8e,0xb1,0x06,0xce);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("dad5ecd6-306f-4d6a-b496-46ba8eb106ce")
            IKnownFoldersPlaylistsStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Playlists(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics, 0xdad5ecd6, 0x306f, 0x4d6a, 0xb4,0x96, 0x46,0xba,0x8e,0xb1,0x06,0xce)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics *This,
        TrustLevel *trustLevel);

    /*** IKnownFoldersPlaylistsStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Playlists)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStaticsVtbl;

interface __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKnownFoldersPlaylistsStatics methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_get_Playlists(This,value) (This)->lpVtbl->get_Playlists(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_QueryInterface(__x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_AddRef(__x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_Release(__x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_GetIids(__x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_GetTrustLevel(__x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKnownFoldersPlaylistsStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_get_Playlists(__x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_Playlists(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IKnownFoldersPlaylistsStatics IID___x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics
#define IKnownFoldersPlaylistsStaticsVtbl __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStaticsVtbl
#define IKnownFoldersPlaylistsStatics __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics
#define IKnownFoldersPlaylistsStatics_QueryInterface __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_QueryInterface
#define IKnownFoldersPlaylistsStatics_AddRef __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_AddRef
#define IKnownFoldersPlaylistsStatics_Release __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_Release
#define IKnownFoldersPlaylistsStatics_GetIids __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_GetIids
#define IKnownFoldersPlaylistsStatics_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_GetRuntimeClassName
#define IKnownFoldersPlaylistsStatics_GetTrustLevel __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_GetTrustLevel
#define IKnownFoldersPlaylistsStatics_get_Playlists __x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_get_Playlists
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIKnownFoldersPlaylistsStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IKnownFoldersSavedPicturesStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics, 0x055c93ea, 0x253d, 0x467c, 0xb6,0xca, 0xa9,0x7d,0xa1,0xe9,0xa1,0x8d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("055c93ea-253d-467c-b6ca-a97da1e9a18d")
            IKnownFoldersSavedPicturesStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_SavedPictures(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics, 0x055c93ea, 0x253d, 0x467c, 0xb6,0xca, 0xa9,0x7d,0xa1,0xe9,0xa1,0x8d)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics *This,
        TrustLevel *trustLevel);

    /*** IKnownFoldersSavedPicturesStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SavedPictures)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStaticsVtbl;

interface __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKnownFoldersSavedPicturesStatics methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_get_SavedPictures(This,value) (This)->lpVtbl->get_SavedPictures(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_QueryInterface(__x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_AddRef(__x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_Release(__x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_GetIids(__x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_GetTrustLevel(__x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKnownFoldersSavedPicturesStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_get_SavedPictures(__x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_SavedPictures(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IKnownFoldersSavedPicturesStatics IID___x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics
#define IKnownFoldersSavedPicturesStaticsVtbl __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStaticsVtbl
#define IKnownFoldersSavedPicturesStatics __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics
#define IKnownFoldersSavedPicturesStatics_QueryInterface __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_QueryInterface
#define IKnownFoldersSavedPicturesStatics_AddRef __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_AddRef
#define IKnownFoldersSavedPicturesStatics_Release __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_Release
#define IKnownFoldersSavedPicturesStatics_GetIids __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_GetIids
#define IKnownFoldersSavedPicturesStatics_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_GetRuntimeClassName
#define IKnownFoldersSavedPicturesStatics_GetTrustLevel __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_GetTrustLevel
#define IKnownFoldersSavedPicturesStatics_get_SavedPictures __x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_get_SavedPictures
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIKnownFoldersSavedPicturesStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IKnownFoldersStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIKnownFoldersStatics, 0x5a2a7520, 0x4802, 0x452d, 0x9a,0xd9, 0x43,0x51,0xad,0xa7,0xec,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("5a2a7520-4802-452d-9ad9-4351ada7ec35")
            IKnownFoldersStatics : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_MusicLibrary(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PicturesLibrary(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_VideosLibrary(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DocumentsLibrary(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_HomeGroup(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_RemovableDevices(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MediaServerDevices(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics, 0x5a2a7520, 0x4802, 0x452d, 0x9a,0xd9, 0x43,0x51,0xad,0xa7,0xec,0x35)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIKnownFoldersStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This,
        TrustLevel *trustLevel);

    /*** IKnownFoldersStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_MusicLibrary)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_PicturesLibrary)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_VideosLibrary)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_DocumentsLibrary)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_HomeGroup)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_RemovableDevices)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_MediaServerDevices)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIKnownFoldersStaticsVtbl;

interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIKnownFoldersStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKnownFoldersStatics methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_MusicLibrary(This,value) (This)->lpVtbl->get_MusicLibrary(This,value)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_PicturesLibrary(This,value) (This)->lpVtbl->get_PicturesLibrary(This,value)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_VideosLibrary(This,value) (This)->lpVtbl->get_VideosLibrary(This,value)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_DocumentsLibrary(This,value) (This)->lpVtbl->get_DocumentsLibrary(This,value)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_HomeGroup(This,value) (This)->lpVtbl->get_HomeGroup(This,value)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_RemovableDevices(This,value) (This)->lpVtbl->get_RemovableDevices(This,value)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_MediaServerDevices(This,value) (This)->lpVtbl->get_MediaServerDevices(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_QueryInterface(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_AddRef(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_Release(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_GetIids(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_GetTrustLevel(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKnownFoldersStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_MusicLibrary(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_MusicLibrary(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_PicturesLibrary(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_PicturesLibrary(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_VideosLibrary(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_VideosLibrary(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_DocumentsLibrary(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_DocumentsLibrary(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_HomeGroup(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_HomeGroup(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_RemovableDevices(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_RemovableDevices(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_MediaServerDevices(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_MediaServerDevices(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IKnownFoldersStatics IID___x_ABI_CWindows_CStorage_CIKnownFoldersStatics
#define IKnownFoldersStaticsVtbl __x_ABI_CWindows_CStorage_CIKnownFoldersStaticsVtbl
#define IKnownFoldersStatics __x_ABI_CWindows_CStorage_CIKnownFoldersStatics
#define IKnownFoldersStatics_QueryInterface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_QueryInterface
#define IKnownFoldersStatics_AddRef __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_AddRef
#define IKnownFoldersStatics_Release __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_Release
#define IKnownFoldersStatics_GetIids __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_GetIids
#define IKnownFoldersStatics_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_GetRuntimeClassName
#define IKnownFoldersStatics_GetTrustLevel __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_GetTrustLevel
#define IKnownFoldersStatics_get_MusicLibrary __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_MusicLibrary
#define IKnownFoldersStatics_get_PicturesLibrary __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_PicturesLibrary
#define IKnownFoldersStatics_get_VideosLibrary __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_VideosLibrary
#define IKnownFoldersStatics_get_DocumentsLibrary __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_DocumentsLibrary
#define IKnownFoldersStatics_get_HomeGroup __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_HomeGroup
#define IKnownFoldersStatics_get_RemovableDevices __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_RemovableDevices
#define IKnownFoldersStatics_get_MediaServerDevices __x_ABI_CWindows_CStorage_CIKnownFoldersStatics_get_MediaServerDevices
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IKnownFoldersStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIKnownFoldersStatics2, 0x194bd0cd, 0xcf6e, 0x4d07, 0x9d,0x53, 0xe9,0x16,0x3a,0x25,0x36,0xe9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("194bd0cd-cf6e-4d07-9d53-e9163a2536e9")
            IKnownFoldersStatics2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Objects3D(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_AppCaptures(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_RecordedCalls(
                    ABI::Windows::Storage::IStorageFolder **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics2, 0x194bd0cd, 0xcf6e, 0x4d07, 0x9d,0x53, 0xe9,0x16,0x3a,0x25,0x36,0xe9)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 *This,
        TrustLevel *trustLevel);

    /*** IKnownFoldersStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Objects3D)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_AppCaptures)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_RecordedCalls)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2Vtbl;

interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2 {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKnownFoldersStatics2 methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_get_Objects3D(This,value) (This)->lpVtbl->get_Objects3D(This,value)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_get_AppCaptures(This,value) (This)->lpVtbl->get_AppCaptures(This,value)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_get_RecordedCalls(This,value) (This)->lpVtbl->get_RecordedCalls(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_QueryInterface(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_AddRef(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_Release(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_GetIids(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_GetTrustLevel(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKnownFoldersStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_get_Objects3D(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics2* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_Objects3D(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_get_AppCaptures(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics2* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_AppCaptures(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_get_RecordedCalls(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics2* This,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->get_RecordedCalls(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IKnownFoldersStatics2 IID___x_ABI_CWindows_CStorage_CIKnownFoldersStatics2
#define IKnownFoldersStatics2Vtbl __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2Vtbl
#define IKnownFoldersStatics2 __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2
#define IKnownFoldersStatics2_QueryInterface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_QueryInterface
#define IKnownFoldersStatics2_AddRef __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_AddRef
#define IKnownFoldersStatics2_Release __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_Release
#define IKnownFoldersStatics2_GetIids __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_GetIids
#define IKnownFoldersStatics2_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_GetRuntimeClassName
#define IKnownFoldersStatics2_GetTrustLevel __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_GetTrustLevel
#define IKnownFoldersStatics2_get_Objects3D __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_get_Objects3D
#define IKnownFoldersStatics2_get_AppCaptures __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_get_AppCaptures
#define IKnownFoldersStatics2_get_RecordedCalls __x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_get_RecordedCalls
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IKnownFoldersStatics3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIKnownFoldersStatics3, 0xc5194341, 0x9742, 0x4ed5, 0x82,0x3d, 0xfc,0x14,0x01,0x14,0x87,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("c5194341-9742-4ed5-823d-fc1401148764")
            IKnownFoldersStatics3 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE GetFolderForUserAsync(
                    ABI::Windows::System::IUser *user,
                    ABI::Windows::Storage::KnownFolderId folder_id,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFolder* > **operation) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics3, 0xc5194341, 0x9742, 0x4ed5, 0x82,0x3d, 0xfc,0x14,0x01,0x14,0x87,0x64)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 *This,
        TrustLevel *trustLevel);

    /*** IKnownFoldersStatics3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFolderForUserAsync)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 *This,
        __x_ABI_CWindows_CSystem_CIUser *user,
        __x_ABI_CWindows_CStorage_CKnownFolderId folder_id,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder **operation);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3Vtbl;

interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3 {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKnownFoldersStatics3 methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetFolderForUserAsync(This,user,folder_id,operation) (This)->lpVtbl->GetFolderForUserAsync(This,user,folder_id,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_QueryInterface(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_AddRef(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_Release(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetIids(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetTrustLevel(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKnownFoldersStatics3 methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetFolderForUserAsync(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics3* This,__x_ABI_CWindows_CSystem_CIUser *user,__x_ABI_CWindows_CStorage_CKnownFolderId folder_id,__FIAsyncOperation_1_Windows__CStorage__CStorageFolder **operation) {
    return This->lpVtbl->GetFolderForUserAsync(This,user,folder_id,operation);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IKnownFoldersStatics3 IID___x_ABI_CWindows_CStorage_CIKnownFoldersStatics3
#define IKnownFoldersStatics3Vtbl __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3Vtbl
#define IKnownFoldersStatics3 __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3
#define IKnownFoldersStatics3_QueryInterface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_QueryInterface
#define IKnownFoldersStatics3_AddRef __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_AddRef
#define IKnownFoldersStatics3_Release __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_Release
#define IKnownFoldersStatics3_GetIids __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetIids
#define IKnownFoldersStatics3_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetRuntimeClassName
#define IKnownFoldersStatics3_GetTrustLevel __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetTrustLevel
#define IKnownFoldersStatics3_GetFolderForUserAsync __x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_GetFolderForUserAsync
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */

/*****************************************************************************
 * IKnownFoldersStatics4 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIKnownFoldersStatics4, 0x1722e6bf, 0x9ff9, 0x4b21, 0xbe,0xd5, 0x90,0xec,0xb1,0x3a,0x19,0x2e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("1722e6bf-9ff9-4b21-bed5-90ecb13a192e")
            IKnownFoldersStatics4 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE RequestAccessAsync(
                    ABI::Windows::Storage::KnownFolderId folder_id,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::KnownFoldersAccessStatus > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE RequestAccessForUserAsync(
                    ABI::Windows::System::IUser *user,
                    ABI::Windows::Storage::KnownFolderId folder_id,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::KnownFoldersAccessStatus > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetFolderAsync(
                    ABI::Windows::Storage::KnownFolderId folder_id,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFolder* > **operation) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics4, 0x1722e6bf, 0x9ff9, 0x4b21, 0xbe,0xd5, 0x90,0xec,0xb1,0x3a,0x19,0x2e)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 *This,
        TrustLevel *trustLevel);

    /*** IKnownFoldersStatics4 methods ***/
    HRESULT (STDMETHODCALLTYPE *RequestAccessAsync)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 *This,
        __x_ABI_CWindows_CStorage_CKnownFolderId folder_id,
        __FIAsyncOperation_1_KnownFoldersAccessStatus **operation);

    HRESULT (STDMETHODCALLTYPE *RequestAccessForUserAsync)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 *This,
        __x_ABI_CWindows_CSystem_CIUser *user,
        __x_ABI_CWindows_CStorage_CKnownFolderId folder_id,
        __FIAsyncOperation_1_KnownFoldersAccessStatus **operation);

    HRESULT (STDMETHODCALLTYPE *GetFolderAsync)(
        __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 *This,
        __x_ABI_CWindows_CStorage_CKnownFolderId folder_id,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder **operation);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4Vtbl;

interface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4 {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKnownFoldersStatics4 methods ***/
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_RequestAccessAsync(This,folder_id,operation) (This)->lpVtbl->RequestAccessAsync(This,folder_id,operation)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_RequestAccessForUserAsync(This,user,folder_id,operation) (This)->lpVtbl->RequestAccessForUserAsync(This,user,folder_id,operation)
#define __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetFolderAsync(This,folder_id,operation) (This)->lpVtbl->GetFolderAsync(This,folder_id,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_QueryInterface(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_AddRef(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_Release(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics4* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetIids(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics4* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics4* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetTrustLevel(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics4* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKnownFoldersStatics4 methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_RequestAccessAsync(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics4* This,__x_ABI_CWindows_CStorage_CKnownFolderId folder_id,__FIAsyncOperation_1_KnownFoldersAccessStatus **operation) {
    return This->lpVtbl->RequestAccessAsync(This,folder_id,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_RequestAccessForUserAsync(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics4* This,__x_ABI_CWindows_CSystem_CIUser *user,__x_ABI_CWindows_CStorage_CKnownFolderId folder_id,__FIAsyncOperation_1_KnownFoldersAccessStatus **operation) {
    return This->lpVtbl->RequestAccessForUserAsync(This,user,folder_id,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetFolderAsync(__x_ABI_CWindows_CStorage_CIKnownFoldersStatics4* This,__x_ABI_CWindows_CStorage_CKnownFolderId folder_id,__FIAsyncOperation_1_Windows__CStorage__CStorageFolder **operation) {
    return This->lpVtbl->GetFolderAsync(This,folder_id,operation);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IKnownFoldersStatics4 IID___x_ABI_CWindows_CStorage_CIKnownFoldersStatics4
#define IKnownFoldersStatics4Vtbl __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4Vtbl
#define IKnownFoldersStatics4 __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4
#define IKnownFoldersStatics4_QueryInterface __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_QueryInterface
#define IKnownFoldersStatics4_AddRef __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_AddRef
#define IKnownFoldersStatics4_Release __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_Release
#define IKnownFoldersStatics4_GetIids __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetIids
#define IKnownFoldersStatics4_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetRuntimeClassName
#define IKnownFoldersStatics4_GetTrustLevel __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetTrustLevel
#define IKnownFoldersStatics4_RequestAccessAsync __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_RequestAccessAsync
#define IKnownFoldersStatics4_RequestAccessForUserAsync __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_RequestAccessForUserAsync
#define IKnownFoldersStatics4_GetFolderAsync __x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_GetFolderAsync
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIKnownFoldersStatics4_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * ISetVersionDeferral interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CISetVersionDeferral_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CISetVersionDeferral_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CISetVersionDeferral, 0x033508a2, 0x781a, 0x437a, 0xb0,0x78, 0x3f,0x32,0xba,0xdc,0xfe,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("033508a2-781a-437a-b078-3f32badcfe47")
            ISetVersionDeferral : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE Complete(
                    ) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CISetVersionDeferral, 0x033508a2, 0x781a, 0x437a, 0xb0,0x78, 0x3f,0x32,0xba,0xdc,0xfe,0x47)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CISetVersionDeferralVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CISetVersionDeferral *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CISetVersionDeferral *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CISetVersionDeferral *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CISetVersionDeferral *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CISetVersionDeferral *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CISetVersionDeferral *This,
        TrustLevel *trustLevel);

    /*** ISetVersionDeferral methods ***/
    HRESULT (STDMETHODCALLTYPE *Complete)(
        __x_ABI_CWindows_CStorage_CISetVersionDeferral *This);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CISetVersionDeferralVtbl;

interface __x_ABI_CWindows_CStorage_CISetVersionDeferral {
    CONST_VTBL __x_ABI_CWindows_CStorage_CISetVersionDeferralVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CISetVersionDeferral_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CISetVersionDeferral_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CISetVersionDeferral_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CISetVersionDeferral_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CISetVersionDeferral_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CISetVersionDeferral_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISetVersionDeferral methods ***/
#define __x_ABI_CWindows_CStorage_CISetVersionDeferral_Complete(This) (This)->lpVtbl->Complete(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionDeferral_QueryInterface(__x_ABI_CWindows_CStorage_CISetVersionDeferral* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CISetVersionDeferral_AddRef(__x_ABI_CWindows_CStorage_CISetVersionDeferral* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CISetVersionDeferral_Release(__x_ABI_CWindows_CStorage_CISetVersionDeferral* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionDeferral_GetIids(__x_ABI_CWindows_CStorage_CISetVersionDeferral* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionDeferral_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CISetVersionDeferral* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionDeferral_GetTrustLevel(__x_ABI_CWindows_CStorage_CISetVersionDeferral* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISetVersionDeferral methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionDeferral_Complete(__x_ABI_CWindows_CStorage_CISetVersionDeferral* This) {
    return This->lpVtbl->Complete(This);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_ISetVersionDeferral IID___x_ABI_CWindows_CStorage_CISetVersionDeferral
#define ISetVersionDeferralVtbl __x_ABI_CWindows_CStorage_CISetVersionDeferralVtbl
#define ISetVersionDeferral __x_ABI_CWindows_CStorage_CISetVersionDeferral
#define ISetVersionDeferral_QueryInterface __x_ABI_CWindows_CStorage_CISetVersionDeferral_QueryInterface
#define ISetVersionDeferral_AddRef __x_ABI_CWindows_CStorage_CISetVersionDeferral_AddRef
#define ISetVersionDeferral_Release __x_ABI_CWindows_CStorage_CISetVersionDeferral_Release
#define ISetVersionDeferral_GetIids __x_ABI_CWindows_CStorage_CISetVersionDeferral_GetIids
#define ISetVersionDeferral_GetRuntimeClassName __x_ABI_CWindows_CStorage_CISetVersionDeferral_GetRuntimeClassName
#define ISetVersionDeferral_GetTrustLevel __x_ABI_CWindows_CStorage_CISetVersionDeferral_GetTrustLevel
#define ISetVersionDeferral_Complete __x_ABI_CWindows_CStorage_CISetVersionDeferral_Complete
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CISetVersionDeferral_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISetVersionRequest interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CISetVersionRequest_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CISetVersionRequest_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CISetVersionRequest, 0xb9c76b9b, 0x1056, 0x4e69, 0x83,0x30, 0x16,0x26,0x19,0x95,0x6f,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("b9c76b9b-1056-4e69-8330-162619956f9b")
            ISetVersionRequest : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_CurrentVersion(
                    UINT32 *version) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DesiredVersion(
                    UINT32 *version) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetDeferral(
                    ABI::Windows::Storage::ISetVersionDeferral **deferral) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CISetVersionRequest, 0xb9c76b9b, 0x1056, 0x4e69, 0x83,0x30, 0x16,0x26,0x19,0x95,0x6f,0x9b)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CISetVersionRequestVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CISetVersionRequest *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CISetVersionRequest *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CISetVersionRequest *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CISetVersionRequest *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CISetVersionRequest *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CISetVersionRequest *This,
        TrustLevel *trustLevel);

    /*** ISetVersionRequest methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CurrentVersion)(
        __x_ABI_CWindows_CStorage_CISetVersionRequest *This,
        UINT32 *version);

    HRESULT (STDMETHODCALLTYPE *get_DesiredVersion)(
        __x_ABI_CWindows_CStorage_CISetVersionRequest *This,
        UINT32 *version);

    HRESULT (STDMETHODCALLTYPE *GetDeferral)(
        __x_ABI_CWindows_CStorage_CISetVersionRequest *This,
        __x_ABI_CWindows_CStorage_CISetVersionDeferral **deferral);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CISetVersionRequestVtbl;

interface __x_ABI_CWindows_CStorage_CISetVersionRequest {
    CONST_VTBL __x_ABI_CWindows_CStorage_CISetVersionRequestVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CISetVersionRequest_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CISetVersionRequest_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CISetVersionRequest_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CISetVersionRequest_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CISetVersionRequest_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CISetVersionRequest_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISetVersionRequest methods ***/
#define __x_ABI_CWindows_CStorage_CISetVersionRequest_get_CurrentVersion(This,version) (This)->lpVtbl->get_CurrentVersion(This,version)
#define __x_ABI_CWindows_CStorage_CISetVersionRequest_get_DesiredVersion(This,version) (This)->lpVtbl->get_DesiredVersion(This,version)
#define __x_ABI_CWindows_CStorage_CISetVersionRequest_GetDeferral(This,deferral) (This)->lpVtbl->GetDeferral(This,deferral)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionRequest_QueryInterface(__x_ABI_CWindows_CStorage_CISetVersionRequest* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CISetVersionRequest_AddRef(__x_ABI_CWindows_CStorage_CISetVersionRequest* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CISetVersionRequest_Release(__x_ABI_CWindows_CStorage_CISetVersionRequest* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionRequest_GetIids(__x_ABI_CWindows_CStorage_CISetVersionRequest* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionRequest_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CISetVersionRequest* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionRequest_GetTrustLevel(__x_ABI_CWindows_CStorage_CISetVersionRequest* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISetVersionRequest methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionRequest_get_CurrentVersion(__x_ABI_CWindows_CStorage_CISetVersionRequest* This,UINT32 *version) {
    return This->lpVtbl->get_CurrentVersion(This,version);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionRequest_get_DesiredVersion(__x_ABI_CWindows_CStorage_CISetVersionRequest* This,UINT32 *version) {
    return This->lpVtbl->get_DesiredVersion(This,version);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CISetVersionRequest_GetDeferral(__x_ABI_CWindows_CStorage_CISetVersionRequest* This,__x_ABI_CWindows_CStorage_CISetVersionDeferral **deferral) {
    return This->lpVtbl->GetDeferral(This,deferral);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_ISetVersionRequest IID___x_ABI_CWindows_CStorage_CISetVersionRequest
#define ISetVersionRequestVtbl __x_ABI_CWindows_CStorage_CISetVersionRequestVtbl
#define ISetVersionRequest __x_ABI_CWindows_CStorage_CISetVersionRequest
#define ISetVersionRequest_QueryInterface __x_ABI_CWindows_CStorage_CISetVersionRequest_QueryInterface
#define ISetVersionRequest_AddRef __x_ABI_CWindows_CStorage_CISetVersionRequest_AddRef
#define ISetVersionRequest_Release __x_ABI_CWindows_CStorage_CISetVersionRequest_Release
#define ISetVersionRequest_GetIids __x_ABI_CWindows_CStorage_CISetVersionRequest_GetIids
#define ISetVersionRequest_GetRuntimeClassName __x_ABI_CWindows_CStorage_CISetVersionRequest_GetRuntimeClassName
#define ISetVersionRequest_GetTrustLevel __x_ABI_CWindows_CStorage_CISetVersionRequest_GetTrustLevel
#define ISetVersionRequest_get_CurrentVersion __x_ABI_CWindows_CStorage_CISetVersionRequest_get_CurrentVersion
#define ISetVersionRequest_get_DesiredVersion __x_ABI_CWindows_CStorage_CISetVersionRequest_get_DesiredVersion
#define ISetVersionRequest_GetDeferral __x_ABI_CWindows_CStorage_CISetVersionRequest_GetDeferral
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CISetVersionRequest_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IStorageFile interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIStorageFile_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageFile_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIStorageFile, 0xfa3f6186, 0x4214, 0x428c, 0xa6,0x4c, 0x14,0xc9,0xac,0x73,0x15,0xea);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("fa3f6186-4214-428c-a64c-14c9ac7315ea")
            IStorageFile : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_FileType(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_ContentType(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE OpenAsync(
                    ABI::Windows::Storage::FileAccessMode mode,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStream* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE OpenTransactedWriteAsync(
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageStreamTransaction* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE CopyOverloadDefaultNameAndOptions(
                    ABI::Windows::Storage::IStorageFolder *folder,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFile* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE CopyOverloadDefaultOptions(
                    ABI::Windows::Storage::IStorageFolder *folder,
                    HSTRING name,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFile* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE CopyOverload(
                    ABI::Windows::Storage::IStorageFolder *folder,
                    HSTRING name,
                    ABI::Windows::Storage::NameCollisionOption option,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFile* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE CopyAndReplaceAsync(
                    ABI::Windows::Storage::IStorageFile *file,
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE MoveOverloadDefaultNameAndOptions(
                    ABI::Windows::Storage::IStorageFolder *folder,
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE MoveOverloadDefaultOptions(
                    ABI::Windows::Storage::IStorageFolder *folder,
                    HSTRING name,
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE MoveOverload(
                    ABI::Windows::Storage::IStorageFolder *folder,
                    HSTRING name,
                    ABI::Windows::Storage::NameCollisionOption option,
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE MoveAndReplaceAsync(
                    ABI::Windows::Storage::IStorageFile *file,
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIStorageFile, 0xfa3f6186, 0x4214, 0x428c, 0xa6,0x4c, 0x14,0xc9,0xac,0x73,0x15,0xea)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIStorageFileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        TrustLevel *trustLevel);

    /*** IStorageFile methods ***/
    HRESULT (STDMETHODCALLTYPE *get_FileType)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_ContentType)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *OpenAsync)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        __x_ABI_CWindows_CStorage_CFileAccessMode mode,
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream **operation);

    HRESULT (STDMETHODCALLTYPE *OpenTransactedWriteAsync)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction **operation);

    HRESULT (STDMETHODCALLTYPE *CopyOverloadDefaultNameAndOptions)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder *folder,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation);

    HRESULT (STDMETHODCALLTYPE *CopyOverloadDefaultOptions)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder *folder,
        HSTRING name,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation);

    HRESULT (STDMETHODCALLTYPE *CopyOverload)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder *folder,
        HSTRING name,
        __x_ABI_CWindows_CStorage_CNameCollisionOption option,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation);

    HRESULT (STDMETHODCALLTYPE *CopyAndReplaceAsync)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        __x_ABI_CWindows_CStorage_CIStorageFile *file,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *MoveOverloadDefaultNameAndOptions)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder *folder,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *MoveOverloadDefaultOptions)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder *folder,
        HSTRING name,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *MoveOverload)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder *folder,
        HSTRING name,
        __x_ABI_CWindows_CStorage_CNameCollisionOption option,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *MoveAndReplaceAsync)(
        __x_ABI_CWindows_CStorage_CIStorageFile *This,
        __x_ABI_CWindows_CStorage_CIStorageFile *file,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIStorageFileVtbl;

interface __x_ABI_CWindows_CStorage_CIStorageFile {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIStorageFileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageFile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIStorageFile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIStorageFile_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageFile_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIStorageFile_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIStorageFile_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IStorageFile methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageFile_get_FileType(This,value) (This)->lpVtbl->get_FileType(This,value)
#define __x_ABI_CWindows_CStorage_CIStorageFile_get_ContentType(This,value) (This)->lpVtbl->get_ContentType(This,value)
#define __x_ABI_CWindows_CStorage_CIStorageFile_OpenAsync(This,mode,operation) (This)->lpVtbl->OpenAsync(This,mode,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFile_OpenTransactedWriteAsync(This,operation) (This)->lpVtbl->OpenTransactedWriteAsync(This,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFile_CopyOverloadDefaultNameAndOptions(This,folder,operation) (This)->lpVtbl->CopyOverloadDefaultNameAndOptions(This,folder,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFile_CopyOverloadDefaultOptions(This,folder,name,operation) (This)->lpVtbl->CopyOverloadDefaultOptions(This,folder,name,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFile_CopyOverload(This,folder,name,option,operation) (This)->lpVtbl->CopyOverload(This,folder,name,option,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFile_CopyAndReplaceAsync(This,file,operation) (This)->lpVtbl->CopyAndReplaceAsync(This,file,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFile_MoveOverloadDefaultNameAndOptions(This,folder,operation) (This)->lpVtbl->MoveOverloadDefaultNameAndOptions(This,folder,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFile_MoveOverloadDefaultOptions(This,folder,name,operation) (This)->lpVtbl->MoveOverloadDefaultOptions(This,folder,name,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFile_MoveOverload(This,folder,name,option,operation) (This)->lpVtbl->MoveOverload(This,folder,name,option,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFile_MoveAndReplaceAsync(This,file,operation) (This)->lpVtbl->MoveAndReplaceAsync(This,file,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_QueryInterface(__x_ABI_CWindows_CStorage_CIStorageFile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIStorageFile_AddRef(__x_ABI_CWindows_CStorage_CIStorageFile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIStorageFile_Release(__x_ABI_CWindows_CStorage_CIStorageFile* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_GetIids(__x_ABI_CWindows_CStorage_CIStorageFile* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIStorageFile* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_GetTrustLevel(__x_ABI_CWindows_CStorage_CIStorageFile* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IStorageFile methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_get_FileType(__x_ABI_CWindows_CStorage_CIStorageFile* This,HSTRING *value) {
    return This->lpVtbl->get_FileType(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_get_ContentType(__x_ABI_CWindows_CStorage_CIStorageFile* This,HSTRING *value) {
    return This->lpVtbl->get_ContentType(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_OpenAsync(__x_ABI_CWindows_CStorage_CIStorageFile* This,__x_ABI_CWindows_CStorage_CFileAccessMode mode,__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream **operation) {
    return This->lpVtbl->OpenAsync(This,mode,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_OpenTransactedWriteAsync(__x_ABI_CWindows_CStorage_CIStorageFile* This,__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction **operation) {
    return This->lpVtbl->OpenTransactedWriteAsync(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_CopyOverloadDefaultNameAndOptions(__x_ABI_CWindows_CStorage_CIStorageFile* This,__x_ABI_CWindows_CStorage_CIStorageFolder *folder,__FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation) {
    return This->lpVtbl->CopyOverloadDefaultNameAndOptions(This,folder,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_CopyOverloadDefaultOptions(__x_ABI_CWindows_CStorage_CIStorageFile* This,__x_ABI_CWindows_CStorage_CIStorageFolder *folder,HSTRING name,__FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation) {
    return This->lpVtbl->CopyOverloadDefaultOptions(This,folder,name,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_CopyOverload(__x_ABI_CWindows_CStorage_CIStorageFile* This,__x_ABI_CWindows_CStorage_CIStorageFolder *folder,HSTRING name,__x_ABI_CWindows_CStorage_CNameCollisionOption option,__FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation) {
    return This->lpVtbl->CopyOverload(This,folder,name,option,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_CopyAndReplaceAsync(__x_ABI_CWindows_CStorage_CIStorageFile* This,__x_ABI_CWindows_CStorage_CIStorageFile *file,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->CopyAndReplaceAsync(This,file,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_MoveOverloadDefaultNameAndOptions(__x_ABI_CWindows_CStorage_CIStorageFile* This,__x_ABI_CWindows_CStorage_CIStorageFolder *folder,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->MoveOverloadDefaultNameAndOptions(This,folder,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_MoveOverloadDefaultOptions(__x_ABI_CWindows_CStorage_CIStorageFile* This,__x_ABI_CWindows_CStorage_CIStorageFolder *folder,HSTRING name,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->MoveOverloadDefaultOptions(This,folder,name,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_MoveOverload(__x_ABI_CWindows_CStorage_CIStorageFile* This,__x_ABI_CWindows_CStorage_CIStorageFolder *folder,HSTRING name,__x_ABI_CWindows_CStorage_CNameCollisionOption option,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->MoveOverload(This,folder,name,option,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFile_MoveAndReplaceAsync(__x_ABI_CWindows_CStorage_CIStorageFile* This,__x_ABI_CWindows_CStorage_CIStorageFile *file,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->MoveAndReplaceAsync(This,file,operation);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IStorageFile IID___x_ABI_CWindows_CStorage_CIStorageFile
#define IStorageFileVtbl __x_ABI_CWindows_CStorage_CIStorageFileVtbl
#define IStorageFile __x_ABI_CWindows_CStorage_CIStorageFile
#define IStorageFile_QueryInterface __x_ABI_CWindows_CStorage_CIStorageFile_QueryInterface
#define IStorageFile_AddRef __x_ABI_CWindows_CStorage_CIStorageFile_AddRef
#define IStorageFile_Release __x_ABI_CWindows_CStorage_CIStorageFile_Release
#define IStorageFile_GetIids __x_ABI_CWindows_CStorage_CIStorageFile_GetIids
#define IStorageFile_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIStorageFile_GetRuntimeClassName
#define IStorageFile_GetTrustLevel __x_ABI_CWindows_CStorage_CIStorageFile_GetTrustLevel
#define IStorageFile_get_FileType __x_ABI_CWindows_CStorage_CIStorageFile_get_FileType
#define IStorageFile_get_ContentType __x_ABI_CWindows_CStorage_CIStorageFile_get_ContentType
#define IStorageFile_OpenAsync __x_ABI_CWindows_CStorage_CIStorageFile_OpenAsync
#define IStorageFile_OpenTransactedWriteAsync __x_ABI_CWindows_CStorage_CIStorageFile_OpenTransactedWriteAsync
#define IStorageFile_CopyOverloadDefaultNameAndOptions __x_ABI_CWindows_CStorage_CIStorageFile_CopyOverloadDefaultNameAndOptions
#define IStorageFile_CopyOverloadDefaultOptions __x_ABI_CWindows_CStorage_CIStorageFile_CopyOverloadDefaultOptions
#define IStorageFile_CopyOverload __x_ABI_CWindows_CStorage_CIStorageFile_CopyOverload
#define IStorageFile_CopyAndReplaceAsync __x_ABI_CWindows_CStorage_CIStorageFile_CopyAndReplaceAsync
#define IStorageFile_MoveOverloadDefaultNameAndOptions __x_ABI_CWindows_CStorage_CIStorageFile_MoveOverloadDefaultNameAndOptions
#define IStorageFile_MoveOverloadDefaultOptions __x_ABI_CWindows_CStorage_CIStorageFile_MoveOverloadDefaultOptions
#define IStorageFile_MoveOverload __x_ABI_CWindows_CStorage_CIStorageFile_MoveOverload
#define IStorageFile_MoveAndReplaceAsync __x_ABI_CWindows_CStorage_CIStorageFile_MoveAndReplaceAsync
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIStorageFile_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IStorageFolder interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIStorageFolder_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageFolder_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIStorageFolder, 0x72d1cb78, 0xb3ef, 0x4f75, 0xa8,0x0b, 0x6f,0xd9,0xda,0xe2,0x94,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("72d1cb78-b3ef-4f75-a80b-6fd9dae2944b")
            IStorageFolder : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE CreateFileAsyncOverloadDefaultOptions(
                    HSTRING name,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFile* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE CreateFileAsync(
                    HSTRING name,
                    ABI::Windows::Storage::CreationCollisionOption options,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFile* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE CreateFolderAsyncOverloadDefaultOptions(
                    HSTRING name,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFolder* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE CreateFolderAsync(
                    HSTRING name,
                    ABI::Windows::Storage::CreationCollisionOption options,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFolder* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetFileAsync(
                    HSTRING name,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFile* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetFolderAsync(
                    HSTRING name,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::StorageFolder* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetItemAsync(
                    HSTRING name,
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::IStorageItem* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetFilesAsyncOverloadDefaultOptionsStartAndCount(
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetFoldersAsyncOverloadDefaultOptionsStartAndCount(
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetItemsAsyncOverloadDefaultStartAndCount(
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* > **operation) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIStorageFolder, 0x72d1cb78, 0xb3ef, 0x4f75, 0xa8,0x0b, 0x6f,0xd9,0xda,0xe2,0x94,0x4b)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIStorageFolderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        TrustLevel *trustLevel);

    /*** IStorageFolder methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateFileAsyncOverloadDefaultOptions)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        HSTRING name,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation);

    HRESULT (STDMETHODCALLTYPE *CreateFileAsync)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        HSTRING name,
        __x_ABI_CWindows_CStorage_CCreationCollisionOption options,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation);

    HRESULT (STDMETHODCALLTYPE *CreateFolderAsyncOverloadDefaultOptions)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        HSTRING name,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder **operation);

    HRESULT (STDMETHODCALLTYPE *CreateFolderAsync)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        HSTRING name,
        __x_ABI_CWindows_CStorage_CCreationCollisionOption options,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder **operation);

    HRESULT (STDMETHODCALLTYPE *GetFileAsync)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        HSTRING name,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation);

    HRESULT (STDMETHODCALLTYPE *GetFolderAsync)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        HSTRING name,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder **operation);

    HRESULT (STDMETHODCALLTYPE *GetItemAsync)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        HSTRING name,
        __FIAsyncOperation_1_Windows__CStorage__CIStorageItem **operation);

    HRESULT (STDMETHODCALLTYPE *GetFilesAsyncOverloadDefaultOptionsStartAndCount)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile **operation);

    HRESULT (STDMETHODCALLTYPE *GetFoldersAsyncOverloadDefaultOptionsStartAndCount)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder **operation);

    HRESULT (STDMETHODCALLTYPE *GetItemsAsyncOverloadDefaultStartAndCount)(
        __x_ABI_CWindows_CStorage_CIStorageFolder *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem **operation);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIStorageFolderVtbl;

interface __x_ABI_CWindows_CStorage_CIStorageFolder {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIStorageFolderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageFolder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageFolder_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IStorageFolder methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFileAsyncOverloadDefaultOptions(This,name,operation) (This)->lpVtbl->CreateFileAsyncOverloadDefaultOptions(This,name,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFileAsync(This,name,options,operation) (This)->lpVtbl->CreateFileAsync(This,name,options,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFolderAsyncOverloadDefaultOptions(This,name,operation) (This)->lpVtbl->CreateFolderAsyncOverloadDefaultOptions(This,name,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFolderAsync(This,name,options,operation) (This)->lpVtbl->CreateFolderAsync(This,name,options,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_GetFileAsync(This,name,operation) (This)->lpVtbl->GetFileAsync(This,name,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_GetFolderAsync(This,name,operation) (This)->lpVtbl->GetFolderAsync(This,name,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_GetItemAsync(This,name,operation) (This)->lpVtbl->GetItemAsync(This,name,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_GetFilesAsyncOverloadDefaultOptionsStartAndCount(This,operation) (This)->lpVtbl->GetFilesAsyncOverloadDefaultOptionsStartAndCount(This,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_GetFoldersAsyncOverloadDefaultOptionsStartAndCount(This,operation) (This)->lpVtbl->GetFoldersAsyncOverloadDefaultOptionsStartAndCount(This,operation)
#define __x_ABI_CWindows_CStorage_CIStorageFolder_GetItemsAsyncOverloadDefaultStartAndCount(This,operation) (This)->lpVtbl->GetItemsAsyncOverloadDefaultStartAndCount(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_QueryInterface(__x_ABI_CWindows_CStorage_CIStorageFolder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIStorageFolder_AddRef(__x_ABI_CWindows_CStorage_CIStorageFolder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIStorageFolder_Release(__x_ABI_CWindows_CStorage_CIStorageFolder* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_GetIids(__x_ABI_CWindows_CStorage_CIStorageFolder* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIStorageFolder* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_GetTrustLevel(__x_ABI_CWindows_CStorage_CIStorageFolder* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IStorageFolder methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFileAsyncOverloadDefaultOptions(__x_ABI_CWindows_CStorage_CIStorageFolder* This,HSTRING name,__FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation) {
    return This->lpVtbl->CreateFileAsyncOverloadDefaultOptions(This,name,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFileAsync(__x_ABI_CWindows_CStorage_CIStorageFolder* This,HSTRING name,__x_ABI_CWindows_CStorage_CCreationCollisionOption options,__FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation) {
    return This->lpVtbl->CreateFileAsync(This,name,options,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFolderAsyncOverloadDefaultOptions(__x_ABI_CWindows_CStorage_CIStorageFolder* This,HSTRING name,__FIAsyncOperation_1_Windows__CStorage__CStorageFolder **operation) {
    return This->lpVtbl->CreateFolderAsyncOverloadDefaultOptions(This,name,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFolderAsync(__x_ABI_CWindows_CStorage_CIStorageFolder* This,HSTRING name,__x_ABI_CWindows_CStorage_CCreationCollisionOption options,__FIAsyncOperation_1_Windows__CStorage__CStorageFolder **operation) {
    return This->lpVtbl->CreateFolderAsync(This,name,options,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_GetFileAsync(__x_ABI_CWindows_CStorage_CIStorageFolder* This,HSTRING name,__FIAsyncOperation_1_Windows__CStorage__CStorageFile **operation) {
    return This->lpVtbl->GetFileAsync(This,name,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_GetFolderAsync(__x_ABI_CWindows_CStorage_CIStorageFolder* This,HSTRING name,__FIAsyncOperation_1_Windows__CStorage__CStorageFolder **operation) {
    return This->lpVtbl->GetFolderAsync(This,name,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_GetItemAsync(__x_ABI_CWindows_CStorage_CIStorageFolder* This,HSTRING name,__FIAsyncOperation_1_Windows__CStorage__CIStorageItem **operation) {
    return This->lpVtbl->GetItemAsync(This,name,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_GetFilesAsyncOverloadDefaultOptionsStartAndCount(__x_ABI_CWindows_CStorage_CIStorageFolder* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile **operation) {
    return This->lpVtbl->GetFilesAsyncOverloadDefaultOptionsStartAndCount(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_GetFoldersAsyncOverloadDefaultOptionsStartAndCount(__x_ABI_CWindows_CStorage_CIStorageFolder* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder **operation) {
    return This->lpVtbl->GetFoldersAsyncOverloadDefaultOptionsStartAndCount(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageFolder_GetItemsAsyncOverloadDefaultStartAndCount(__x_ABI_CWindows_CStorage_CIStorageFolder* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem **operation) {
    return This->lpVtbl->GetItemsAsyncOverloadDefaultStartAndCount(This,operation);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IStorageFolder IID___x_ABI_CWindows_CStorage_CIStorageFolder
#define IStorageFolderVtbl __x_ABI_CWindows_CStorage_CIStorageFolderVtbl
#define IStorageFolder __x_ABI_CWindows_CStorage_CIStorageFolder
#define IStorageFolder_QueryInterface __x_ABI_CWindows_CStorage_CIStorageFolder_QueryInterface
#define IStorageFolder_AddRef __x_ABI_CWindows_CStorage_CIStorageFolder_AddRef
#define IStorageFolder_Release __x_ABI_CWindows_CStorage_CIStorageFolder_Release
#define IStorageFolder_GetIids __x_ABI_CWindows_CStorage_CIStorageFolder_GetIids
#define IStorageFolder_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIStorageFolder_GetRuntimeClassName
#define IStorageFolder_GetTrustLevel __x_ABI_CWindows_CStorage_CIStorageFolder_GetTrustLevel
#define IStorageFolder_CreateFileAsyncOverloadDefaultOptions __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFileAsyncOverloadDefaultOptions
#define IStorageFolder_CreateFileAsync __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFileAsync
#define IStorageFolder_CreateFolderAsyncOverloadDefaultOptions __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFolderAsyncOverloadDefaultOptions
#define IStorageFolder_CreateFolderAsync __x_ABI_CWindows_CStorage_CIStorageFolder_CreateFolderAsync
#define IStorageFolder_GetFileAsync __x_ABI_CWindows_CStorage_CIStorageFolder_GetFileAsync
#define IStorageFolder_GetFolderAsync __x_ABI_CWindows_CStorage_CIStorageFolder_GetFolderAsync
#define IStorageFolder_GetItemAsync __x_ABI_CWindows_CStorage_CIStorageFolder_GetItemAsync
#define IStorageFolder_GetFilesAsyncOverloadDefaultOptionsStartAndCount __x_ABI_CWindows_CStorage_CIStorageFolder_GetFilesAsyncOverloadDefaultOptionsStartAndCount
#define IStorageFolder_GetFoldersAsyncOverloadDefaultOptionsStartAndCount __x_ABI_CWindows_CStorage_CIStorageFolder_GetFoldersAsyncOverloadDefaultOptionsStartAndCount
#define IStorageFolder_GetItemsAsyncOverloadDefaultStartAndCount __x_ABI_CWindows_CStorage_CIStorageFolder_GetItemsAsyncOverloadDefaultStartAndCount
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIStorageFolder_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IStorageItem interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIStorageItem_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIStorageItem, 0x4207a996, 0xca2f, 0x42f7, 0xbd,0xe8, 0x8b,0x10,0x45,0x7a,0x7f,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("4207a996-ca2f-42f7-bde8-8b10457a7f30")
            IStorageItem : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE RenameAsyncOverloadDefaultOptions(
                    HSTRING name,
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE RenameAsync(
                    HSTRING name,
                    ABI::Windows::Storage::NameCollisionOption option,
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE DeleteAsyncOverloadDefaultOptions(
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE DeleteAsync(
                    ABI::Windows::Storage::StorageDeleteOption option,
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE GetBasicPropertiesAsync(
                    ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::FileProperties::BasicProperties* > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Name(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Path(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Attributes(
                    ABI::Windows::Storage::FileAttributes *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DateCreated(
                    ABI::Windows::Foundation::DateTime *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE IsOfType(
                    ABI::Windows::Storage::StorageItemTypes type,
                    boolean *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIStorageItem, 0x4207a996, 0xca2f, 0x42f7, 0xbd,0xe8, 0x8b,0x10,0x45,0x7a,0x7f,0x30)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIStorageItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        TrustLevel *trustLevel);

    /*** IStorageItem methods ***/
    HRESULT (STDMETHODCALLTYPE *RenameAsyncOverloadDefaultOptions)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        HSTRING name,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *RenameAsync)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        HSTRING name,
        __x_ABI_CWindows_CStorage_CNameCollisionOption option,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *DeleteAsyncOverloadDefaultOptions)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *DeleteAsync)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        __x_ABI_CWindows_CStorage_CStorageDeleteOption option,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    HRESULT (STDMETHODCALLTYPE *GetBasicPropertiesAsync)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties **operation);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Path)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Attributes)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        __x_ABI_CWindows_CStorage_CFileAttributes *value);

    HRESULT (STDMETHODCALLTYPE *get_DateCreated)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        __x_ABI_CWindows_CFoundation_CDateTime *value);

    HRESULT (STDMETHODCALLTYPE *IsOfType)(
        __x_ABI_CWindows_CStorage_CIStorageItem *This,
        __x_ABI_CWindows_CStorage_CStorageItemTypes type,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIStorageItemVtbl;

interface __x_ABI_CWindows_CStorage_CIStorageItem {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIStorageItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIStorageItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIStorageItem_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageItem_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIStorageItem_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIStorageItem_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IStorageItem methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageItem_RenameAsyncOverloadDefaultOptions(This,name,operation) (This)->lpVtbl->RenameAsyncOverloadDefaultOptions(This,name,operation)
#define __x_ABI_CWindows_CStorage_CIStorageItem_RenameAsync(This,name,option,operation) (This)->lpVtbl->RenameAsync(This,name,option,operation)
#define __x_ABI_CWindows_CStorage_CIStorageItem_DeleteAsyncOverloadDefaultOptions(This,operation) (This)->lpVtbl->DeleteAsyncOverloadDefaultOptions(This,operation)
#define __x_ABI_CWindows_CStorage_CIStorageItem_DeleteAsync(This,option,operation) (This)->lpVtbl->DeleteAsync(This,option,operation)
#define __x_ABI_CWindows_CStorage_CIStorageItem_GetBasicPropertiesAsync(This,operation) (This)->lpVtbl->GetBasicPropertiesAsync(This,operation)
#define __x_ABI_CWindows_CStorage_CIStorageItem_get_Name(This,value) (This)->lpVtbl->get_Name(This,value)
#define __x_ABI_CWindows_CStorage_CIStorageItem_get_Path(This,value) (This)->lpVtbl->get_Path(This,value)
#define __x_ABI_CWindows_CStorage_CIStorageItem_get_Attributes(This,value) (This)->lpVtbl->get_Attributes(This,value)
#define __x_ABI_CWindows_CStorage_CIStorageItem_get_DateCreated(This,value) (This)->lpVtbl->get_DateCreated(This,value)
#define __x_ABI_CWindows_CStorage_CIStorageItem_IsOfType(This,type,value) (This)->lpVtbl->IsOfType(This,type,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_QueryInterface(__x_ABI_CWindows_CStorage_CIStorageItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIStorageItem_AddRef(__x_ABI_CWindows_CStorage_CIStorageItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIStorageItem_Release(__x_ABI_CWindows_CStorage_CIStorageItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_GetIids(__x_ABI_CWindows_CStorage_CIStorageItem* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIStorageItem* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_GetTrustLevel(__x_ABI_CWindows_CStorage_CIStorageItem* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IStorageItem methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_RenameAsyncOverloadDefaultOptions(__x_ABI_CWindows_CStorage_CIStorageItem* This,HSTRING name,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->RenameAsyncOverloadDefaultOptions(This,name,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_RenameAsync(__x_ABI_CWindows_CStorage_CIStorageItem* This,HSTRING name,__x_ABI_CWindows_CStorage_CNameCollisionOption option,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->RenameAsync(This,name,option,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_DeleteAsyncOverloadDefaultOptions(__x_ABI_CWindows_CStorage_CIStorageItem* This,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->DeleteAsyncOverloadDefaultOptions(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_DeleteAsync(__x_ABI_CWindows_CStorage_CIStorageItem* This,__x_ABI_CWindows_CStorage_CStorageDeleteOption option,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->DeleteAsync(This,option,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_GetBasicPropertiesAsync(__x_ABI_CWindows_CStorage_CIStorageItem* This,__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties **operation) {
    return This->lpVtbl->GetBasicPropertiesAsync(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_get_Name(__x_ABI_CWindows_CStorage_CIStorageItem* This,HSTRING *value) {
    return This->lpVtbl->get_Name(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_get_Path(__x_ABI_CWindows_CStorage_CIStorageItem* This,HSTRING *value) {
    return This->lpVtbl->get_Path(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_get_Attributes(__x_ABI_CWindows_CStorage_CIStorageItem* This,__x_ABI_CWindows_CStorage_CFileAttributes *value) {
    return This->lpVtbl->get_Attributes(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_get_DateCreated(__x_ABI_CWindows_CStorage_CIStorageItem* This,__x_ABI_CWindows_CFoundation_CDateTime *value) {
    return This->lpVtbl->get_DateCreated(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageItem_IsOfType(__x_ABI_CWindows_CStorage_CIStorageItem* This,__x_ABI_CWindows_CStorage_CStorageItemTypes type,boolean *value) {
    return This->lpVtbl->IsOfType(This,type,value);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IStorageItem IID___x_ABI_CWindows_CStorage_CIStorageItem
#define IStorageItemVtbl __x_ABI_CWindows_CStorage_CIStorageItemVtbl
#define IStorageItem __x_ABI_CWindows_CStorage_CIStorageItem
#define IStorageItem_QueryInterface __x_ABI_CWindows_CStorage_CIStorageItem_QueryInterface
#define IStorageItem_AddRef __x_ABI_CWindows_CStorage_CIStorageItem_AddRef
#define IStorageItem_Release __x_ABI_CWindows_CStorage_CIStorageItem_Release
#define IStorageItem_GetIids __x_ABI_CWindows_CStorage_CIStorageItem_GetIids
#define IStorageItem_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIStorageItem_GetRuntimeClassName
#define IStorageItem_GetTrustLevel __x_ABI_CWindows_CStorage_CIStorageItem_GetTrustLevel
#define IStorageItem_RenameAsyncOverloadDefaultOptions __x_ABI_CWindows_CStorage_CIStorageItem_RenameAsyncOverloadDefaultOptions
#define IStorageItem_RenameAsync __x_ABI_CWindows_CStorage_CIStorageItem_RenameAsync
#define IStorageItem_DeleteAsyncOverloadDefaultOptions __x_ABI_CWindows_CStorage_CIStorageItem_DeleteAsyncOverloadDefaultOptions
#define IStorageItem_DeleteAsync __x_ABI_CWindows_CStorage_CIStorageItem_DeleteAsync
#define IStorageItem_GetBasicPropertiesAsync __x_ABI_CWindows_CStorage_CIStorageItem_GetBasicPropertiesAsync
#define IStorageItem_get_Name __x_ABI_CWindows_CStorage_CIStorageItem_get_Name
#define IStorageItem_get_Path __x_ABI_CWindows_CStorage_CIStorageItem_get_Path
#define IStorageItem_get_Attributes __x_ABI_CWindows_CStorage_CIStorageItem_get_Attributes
#define IStorageItem_get_DateCreated __x_ABI_CWindows_CStorage_CIStorageItem_get_DateCreated
#define IStorageItem_IsOfType __x_ABI_CWindows_CStorage_CIStorageItem_IsOfType
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIStorageItem_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IStorageStreamTransaction interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CIStorageStreamTransaction_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageStreamTransaction_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CIStorageStreamTransaction, 0xf67cf363, 0xa53d, 0x4d94, 0xae,0x2c, 0x67,0x23,0x2d,0x93,0xac,0xdd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            MIDL_INTERFACE("f67cf363-a53d-4d94-ae2c-67232d93acdd")
            IStorageStreamTransaction : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Stream(
                    ABI::Windows::Storage::Streams::IRandomAccessStream **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE CommitAsync(
                    ABI::Windows::Foundation::IAsyncAction **operation) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CIStorageStreamTransaction, 0xf67cf363, 0xa53d, 0x4d94, 0xae,0x2c, 0x67,0x23,0x2d,0x93,0xac,0xdd)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CIStorageStreamTransactionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CIStorageStreamTransaction *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CIStorageStreamTransaction *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CIStorageStreamTransaction *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CIStorageStreamTransaction *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CIStorageStreamTransaction *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CIStorageStreamTransaction *This,
        TrustLevel *trustLevel);

    /*** IStorageStreamTransaction methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Stream)(
        __x_ABI_CWindows_CStorage_CIStorageStreamTransaction *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **value);

    HRESULT (STDMETHODCALLTYPE *CommitAsync)(
        __x_ABI_CWindows_CStorage_CIStorageStreamTransaction *This,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **operation);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CIStorageStreamTransactionVtbl;

interface __x_ABI_CWindows_CStorage_CIStorageStreamTransaction {
    CONST_VTBL __x_ABI_CWindows_CStorage_CIStorageStreamTransactionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IStorageStreamTransaction methods ***/
#define __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_get_Stream(This,value) (This)->lpVtbl->get_Stream(This,value)
#define __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_CommitAsync(This,operation) (This)->lpVtbl->CommitAsync(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_QueryInterface(__x_ABI_CWindows_CStorage_CIStorageStreamTransaction* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_AddRef(__x_ABI_CWindows_CStorage_CIStorageStreamTransaction* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_Release(__x_ABI_CWindows_CStorage_CIStorageStreamTransaction* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_GetIids(__x_ABI_CWindows_CStorage_CIStorageStreamTransaction* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CIStorageStreamTransaction* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_GetTrustLevel(__x_ABI_CWindows_CStorage_CIStorageStreamTransaction* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IStorageStreamTransaction methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_get_Stream(__x_ABI_CWindows_CStorage_CIStorageStreamTransaction* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **value) {
    return This->lpVtbl->get_Stream(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_CommitAsync(__x_ABI_CWindows_CStorage_CIStorageStreamTransaction* This,__x_ABI_CWindows_CFoundation_CIAsyncAction **operation) {
    return This->lpVtbl->CommitAsync(This,operation);
}
#endif
#ifdef WIDL_using_Windows_Storage
#define IID_IStorageStreamTransaction IID___x_ABI_CWindows_CStorage_CIStorageStreamTransaction
#define IStorageStreamTransactionVtbl __x_ABI_CWindows_CStorage_CIStorageStreamTransactionVtbl
#define IStorageStreamTransaction __x_ABI_CWindows_CStorage_CIStorageStreamTransaction
#define IStorageStreamTransaction_QueryInterface __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_QueryInterface
#define IStorageStreamTransaction_AddRef __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_AddRef
#define IStorageStreamTransaction_Release __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_Release
#define IStorageStreamTransaction_GetIids __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_GetIids
#define IStorageStreamTransaction_GetRuntimeClassName __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_GetRuntimeClassName
#define IStorageStreamTransaction_GetTrustLevel __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_GetTrustLevel
#define IStorageStreamTransaction_get_Stream __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_get_Stream
#define IStorageStreamTransaction_CommitAsync __x_ABI_CWindows_CStorage_CIStorageStreamTransaction_CommitAsync
#endif /* WIDL_using_Windows_Storage */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CIStorageStreamTransaction_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.ApplicationData
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_ApplicationData_DEFINED
#define RUNTIMECLASS_Windows_Storage_ApplicationData_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_ApplicationData[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','A','p','p','l','i','c','a','t','i','o','n','D','a','t','a',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_ApplicationData[] = L"Windows.Storage.ApplicationData";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_ApplicationData[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','A','p','p','l','i','c','a','t','i','o','n','D','a','t','a',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_ApplicationData_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.ApplicationDataContainer
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_ApplicationDataContainer_DEFINED
#define RUNTIMECLASS_Windows_Storage_ApplicationDataContainer_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_ApplicationDataContainer[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','A','p','p','l','i','c','a','t','i','o','n','D','a','t','a','C','o','n','t','a','i','n','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_ApplicationDataContainer[] = L"Windows.Storage.ApplicationDataContainer";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_ApplicationDataContainer[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','A','p','p','l','i','c','a','t','i','o','n','D','a','t','a','C','o','n','t','a','i','n','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_ApplicationDataContainer_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.KnownFolders
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_KnownFolders_DEFINED
#define RUNTIMECLASS_Windows_Storage_KnownFolders_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_KnownFolders[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','K','n','o','w','n','F','o','l','d','e','r','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_KnownFolders[] = L"Windows.Storage.KnownFolders";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_KnownFolders[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','K','n','o','w','n','F','o','l','d','e','r','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_KnownFolders_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.SetVersionDeferral
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_SetVersionDeferral_DEFINED
#define RUNTIMECLASS_Windows_Storage_SetVersionDeferral_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_SetVersionDeferral[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','e','t','V','e','r','s','i','o','n','D','e','f','e','r','r','a','l',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_SetVersionDeferral[] = L"Windows.Storage.SetVersionDeferral";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_SetVersionDeferral[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','e','t','V','e','r','s','i','o','n','D','e','f','e','r','r','a','l',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_SetVersionDeferral_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.SetVersionRequest
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_SetVersionRequest_DEFINED
#define RUNTIMECLASS_Windows_Storage_SetVersionRequest_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_SetVersionRequest[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','e','t','V','e','r','s','i','o','n','R','e','q','u','e','s','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_SetVersionRequest[] = L"Windows.Storage.SetVersionRequest";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_SetVersionRequest[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','e','t','V','e','r','s','i','o','n','R','e','q','u','e','s','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_SetVersionRequest_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.StorageFile
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_StorageFile_DEFINED
#define RUNTIMECLASS_Windows_Storage_StorageFile_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_StorageFile[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','o','r','a','g','e','F','i','l','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_StorageFile[] = L"Windows.Storage.StorageFile";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_StorageFile[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','o','r','a','g','e','F','i','l','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_StorageFile_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.StorageFolder
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_StorageFolder_DEFINED
#define RUNTIMECLASS_Windows_Storage_StorageFolder_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_StorageFolder[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','o','r','a','g','e','F','o','l','d','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_StorageFolder[] = L"Windows.Storage.StorageFolder";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_StorageFolder[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','o','r','a','g','e','F','o','l','d','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_StorageFolder_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.StorageStreamTransaction
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_StorageStreamTransaction_DEFINED
#define RUNTIMECLASS_Windows_Storage_StorageStreamTransaction_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_StorageStreamTransaction[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','o','r','a','g','e','S','t','r','e','a','m','T','r','a','n','s','a','c','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_StorageStreamTransaction[] = L"Windows.Storage.StorageStreamTransaction";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_StorageStreamTransaction[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','o','r','a','g','e','S','t','r','e','a','m','T','r','a','n','s','a','c','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_StorageStreamTransaction_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* > interface
 */
#ifndef ____FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__
#define ____FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer, 0x5adbc543, 0x2170, 0x5ad9, 0xb3,0x5e, 0x96,0x8c,0xdb,0x78,0xfb,0x30);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("5adbc543-2170-5ad9-b35e-968cdb78fb30")
                IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* > : IKeyValuePair_impl<HSTRING, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::ApplicationDataContainer*, ABI::Windows::Storage::IApplicationDataContainer* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer, 0x5adbc543, 0x2170, 0x5ad9, 0xb3,0x5e, 0x96,0x8c,0xdb,0x78,0xfb,0x30)
#endif
#else
typedef struct __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        TrustLevel *trustLevel);

    /*** IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Key)(
        __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        HSTRING *key);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer **value);

    END_INTERFACE
} __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl;

interface __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer {
    CONST_VTBL __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* > methods ***/
#define __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Key(This,key) (This)->lpVtbl->get_Key(This,key)
#define __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface(__FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef(__FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release(__FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids(__FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName(__FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel(__FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* > methods ***/
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Key(__FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,HSTRING *key) {
    return This->lpVtbl->get_Key(This,key);
}
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Value(__FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,__x_ABI_CWindows_CStorage_CIApplicationDataContainer **value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IKeyValuePair_HSTRING_ApplicationDataContainer IID___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer
#define IKeyValuePair_HSTRING_ApplicationDataContainerVtbl __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl
#define IKeyValuePair_HSTRING_ApplicationDataContainer __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer
#define IKeyValuePair_HSTRING_ApplicationDataContainer_QueryInterface __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface
#define IKeyValuePair_HSTRING_ApplicationDataContainer_AddRef __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef
#define IKeyValuePair_HSTRING_ApplicationDataContainer_Release __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release
#define IKeyValuePair_HSTRING_ApplicationDataContainer_GetIids __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids
#define IKeyValuePair_HSTRING_ApplicationDataContainer_GetRuntimeClassName __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName
#define IKeyValuePair_HSTRING_ApplicationDataContainer_GetTrustLevel __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel
#define IKeyValuePair_HSTRING_ApplicationDataContainer_get_Key __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Key
#define IKeyValuePair_HSTRING_ApplicationDataContainer_get_Value __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Value
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* > interface
 */
#ifndef ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer, 0xa785be1d, 0x159e, 0x53ad, 0x95,0x53, 0x59,0x8b,0x03,0xdc,0xa0,0x48);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("a785be1d-159e-53ad-9553-598b03dca048")
                IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* > : IIterable_impl<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer, 0xa785be1d, 0x159e, 0x53ad, 0x95,0x53, 0x59,0x8b,0x03,0xdc,0xa0,0x48)
#endif
#else
typedef struct __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer **value);

    END_INTERFACE
} __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl;

interface __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer {
    CONST_VTBL __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* > methods ***/
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* > methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_First(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_IKeyValuePair_HSTRING_ApplicationDataContainer IID___FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer
#define IIterable_IKeyValuePair_HSTRING_ApplicationDataContainerVtbl __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl
#define IIterable_IKeyValuePair_HSTRING_ApplicationDataContainer __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer
#define IIterable_IKeyValuePair_HSTRING_ApplicationDataContainer_QueryInterface __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface
#define IIterable_IKeyValuePair_HSTRING_ApplicationDataContainer_AddRef __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef
#define IIterable_IKeyValuePair_HSTRING_ApplicationDataContainer_Release __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release
#define IIterable_IKeyValuePair_HSTRING_ApplicationDataContainer_GetIids __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids
#define IIterable_IKeyValuePair_HSTRING_ApplicationDataContainer_GetRuntimeClassName __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName
#define IIterable_IKeyValuePair_HSTRING_ApplicationDataContainer_GetTrustLevel __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel
#define IIterable_IKeyValuePair_HSTRING_ApplicationDataContainer_First __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Storage::IStorageItem* > interface
 */
#ifndef ____FIIterable_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CStorage__CIStorageItem, 0xbb8b8418, 0x65d1, 0x544b, 0xb0,0x83, 0x6d,0x17,0x2f,0x56,0x8c,0x73);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("bb8b8418-65d1-544b-b083-6d172f568c73")
                IIterable<ABI::Windows::Storage::IStorageItem* > : IIterable_impl<ABI::Windows::Storage::IStorageItem* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CStorage__CIStorageItem, 0xbb8b8418, 0x65d1, 0x544b, 0xb0,0x83, 0x6d,0x17,0x2f,0x56,0x8c,0x73)
#endif
#else
typedef struct __FIIterable_1_Windows__CStorage__CIStorageItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CStorage__CIStorageItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CStorage__CIStorageItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CStorage__CIStorageItem *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CStorage__CIStorageItem *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CStorage__CIStorageItem *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CStorage__CIStorageItem *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Storage::IStorageItem* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CStorage__CIStorageItem *This,
        __FIIterator_1_Windows__CStorage__CIStorageItem **value);

    END_INTERFACE
} __FIIterable_1_Windows__CStorage__CIStorageItemVtbl;

interface __FIIterable_1_Windows__CStorage__CIStorageItem {
    CONST_VTBL __FIIterable_1_Windows__CStorage__CIStorageItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CStorage__CIStorageItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CStorage__CIStorageItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CStorage__CIStorageItem_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CStorage__CIStorageItem_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CStorage__CIStorageItem_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Storage::IStorageItem* > methods ***/
#define __FIIterable_1_Windows__CStorage__CIStorageItem_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CStorage__CIStorageItem_QueryInterface(__FIIterable_1_Windows__CStorage__CIStorageItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CStorage__CIStorageItem_AddRef(__FIIterable_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CStorage__CIStorageItem_Release(__FIIterable_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CStorage__CIStorageItem_GetIids(__FIIterable_1_Windows__CStorage__CIStorageItem* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName(__FIIterable_1_Windows__CStorage__CIStorageItem* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CStorage__CIStorageItem_GetTrustLevel(__FIIterable_1_Windows__CStorage__CIStorageItem* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Storage::IStorageItem* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CStorage__CIStorageItem_First(__FIIterable_1_Windows__CStorage__CIStorageItem* This,__FIIterator_1_Windows__CStorage__CIStorageItem **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_IStorageItem IID___FIIterable_1_Windows__CStorage__CIStorageItem
#define IIterable_IStorageItemVtbl __FIIterable_1_Windows__CStorage__CIStorageItemVtbl
#define IIterable_IStorageItem __FIIterable_1_Windows__CStorage__CIStorageItem
#define IIterable_IStorageItem_QueryInterface __FIIterable_1_Windows__CStorage__CIStorageItem_QueryInterface
#define IIterable_IStorageItem_AddRef __FIIterable_1_Windows__CStorage__CIStorageItem_AddRef
#define IIterable_IStorageItem_Release __FIIterable_1_Windows__CStorage__CIStorageItem_Release
#define IIterable_IStorageItem_GetIids __FIIterable_1_Windows__CStorage__CIStorageItem_GetIids
#define IIterable_IStorageItem_GetRuntimeClassName __FIIterable_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName
#define IIterable_IStorageItem_GetTrustLevel __FIIterable_1_Windows__CStorage__CIStorageItem_GetTrustLevel
#define IIterable_IStorageItem_First __FIIterable_1_Windows__CStorage__CIStorageItem_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* > interface
 */
#ifndef ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer, 0xaf3c131d, 0x67aa, 0x5c8d, 0xae,0x0e, 0x27,0x2b,0xa2,0x4a,0xe7,0x4f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("af3c131d-67aa-5c8d-ae0e-272ba24ae74f")
                IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* > : IIterator_impl<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer, 0xaf3c131d, 0x67aa, 0x5c8d, 0xae,0x0e, 0x27,0x2b,0xa2,0x4a,0xe7,0x4f)
#endif
#else
typedef struct __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        UINT32 items_size,
        __FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl;

interface __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer {
    CONST_VTBL __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* > methods ***/
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* >* > methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Current(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,__FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_HasCurrent(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_MoveNext(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetMany(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,UINT32 items_size,__FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer IID___FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainerVtbl __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer_QueryInterface __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer_AddRef __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer_Release __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer_GetIids __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer_GetRuntimeClassName __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer_GetTrustLevel __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer_get_Current __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Current
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer_get_HasCurrent __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_HasCurrent
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer_MoveNext __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_MoveNext
#define IIterator_IKeyValuePair_HSTRING_ApplicationDataContainer_GetMany __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Storage::IStorageItem* > interface
 */
#ifndef ____FIIterator_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CStorage__CIStorageItem, 0x05b487c2, 0x3830, 0x5d3c, 0x98,0xda, 0x25,0xfa,0x11,0x54,0x2d,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("05b487c2-3830-5d3c-98da-25fa11542dbd")
                IIterator<ABI::Windows::Storage::IStorageItem* > : IIterator_impl<ABI::Windows::Storage::IStorageItem* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CStorage__CIStorageItem, 0x05b487c2, 0x3830, 0x5d3c, 0x98,0xda, 0x25,0xfa,0x11,0x54,0x2d,0xbd)
#endif
#else
typedef struct __FIIterator_1_Windows__CStorage__CIStorageItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CStorage__CIStorageItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CStorage__CIStorageItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CStorage__CIStorageItem *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CStorage__CIStorageItem *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CStorage__CIStorageItem *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CStorage__CIStorageItem *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Storage::IStorageItem* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CStorage__CIStorageItem *This,
        __x_ABI_CWindows_CStorage_CIStorageItem **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CStorage__CIStorageItem *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CStorage__CIStorageItem *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CStorage__CIStorageItem *This,
        UINT32 items_size,
        __x_ABI_CWindows_CStorage_CIStorageItem **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CStorage__CIStorageItemVtbl;

interface __FIIterator_1_Windows__CStorage__CIStorageItem {
    CONST_VTBL __FIIterator_1_Windows__CStorage__CIStorageItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CStorage__CIStorageItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CStorage__CIStorageItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CStorage__CIStorageItem_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CStorage__CIStorageItem_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CStorage__CIStorageItem_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Storage::IStorageItem* > methods ***/
#define __FIIterator_1_Windows__CStorage__CIStorageItem_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CStorage__CIStorageItem_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CStorage__CIStorageItem_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CStorage__CIStorageItem_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CStorage__CIStorageItem_QueryInterface(__FIIterator_1_Windows__CStorage__CIStorageItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CStorage__CIStorageItem_AddRef(__FIIterator_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CStorage__CIStorageItem_Release(__FIIterator_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CStorage__CIStorageItem_GetIids(__FIIterator_1_Windows__CStorage__CIStorageItem* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName(__FIIterator_1_Windows__CStorage__CIStorageItem* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CStorage__CIStorageItem_GetTrustLevel(__FIIterator_1_Windows__CStorage__CIStorageItem* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Storage::IStorageItem* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CStorage__CIStorageItem_get_Current(__FIIterator_1_Windows__CStorage__CIStorageItem* This,__x_ABI_CWindows_CStorage_CIStorageItem **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CStorage__CIStorageItem_get_HasCurrent(__FIIterator_1_Windows__CStorage__CIStorageItem* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CStorage__CIStorageItem_MoveNext(__FIIterator_1_Windows__CStorage__CIStorageItem* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CStorage__CIStorageItem_GetMany(__FIIterator_1_Windows__CStorage__CIStorageItem* This,UINT32 items_size,__x_ABI_CWindows_CStorage_CIStorageItem **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_IStorageItem IID___FIIterator_1_Windows__CStorage__CIStorageItem
#define IIterator_IStorageItemVtbl __FIIterator_1_Windows__CStorage__CIStorageItemVtbl
#define IIterator_IStorageItem __FIIterator_1_Windows__CStorage__CIStorageItem
#define IIterator_IStorageItem_QueryInterface __FIIterator_1_Windows__CStorage__CIStorageItem_QueryInterface
#define IIterator_IStorageItem_AddRef __FIIterator_1_Windows__CStorage__CIStorageItem_AddRef
#define IIterator_IStorageItem_Release __FIIterator_1_Windows__CStorage__CIStorageItem_Release
#define IIterator_IStorageItem_GetIids __FIIterator_1_Windows__CStorage__CIStorageItem_GetIids
#define IIterator_IStorageItem_GetRuntimeClassName __FIIterator_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName
#define IIterator_IStorageItem_GetTrustLevel __FIIterator_1_Windows__CStorage__CIStorageItem_GetTrustLevel
#define IIterator_IStorageItem_get_Current __FIIterator_1_Windows__CStorage__CIStorageItem_get_Current
#define IIterator_IStorageItem_get_HasCurrent __FIIterator_1_Windows__CStorage__CIStorageItem_get_HasCurrent
#define IIterator_IStorageItem_MoveNext __FIIterator_1_Windows__CStorage__CIStorageItem_MoveNext
#define IIterator_IStorageItem_GetMany __FIIterator_1_Windows__CStorage__CIStorageItem_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMapView<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* > interface
 */
#ifndef ____FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer, 0x13624f8d, 0x85cc, 0x5780, 0xa7,0x8d, 0x64,0xdb,0xa5,0x8f,0x2c,0x3c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("13624f8d-85cc-5780-a78d-64dba58f2c3c")
                IMapView<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* > : IMapView_impl<HSTRING, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::ApplicationDataContainer*, ABI::Windows::Storage::IApplicationDataContainer* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer, 0x13624f8d, 0x85cc, 0x5780, 0xa7,0x8d, 0x64,0xdb,0xa5,0x8f,0x2c,0x3c)
#endif
#else
typedef struct __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        TrustLevel *trustLevel);

    /*** IMapView<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        HSTRING key,
        __x_ABI_CWindows_CStorage_CIApplicationDataContainer **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        HSTRING key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *Split)(
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer *This,
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer **first,
        __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer **second);

    END_INTERFACE
} __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl;

interface __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer {
    CONST_VTBL __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMapView<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* > methods ***/
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Split(This,first,second) (This)->lpVtbl->Split(This,first,second)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface(__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef(__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release(__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids(__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName(__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel(__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMapView<HSTRING,ABI::Windows::Storage::ApplicationDataContainer* > methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Lookup(__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,HSTRING key,__x_ABI_CWindows_CStorage_CIApplicationDataContainer **value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Size(__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_HasKey(__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,HSTRING key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Split(__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer* This,__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer **first,__FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer **second) {
    return This->lpVtbl->Split(This,first,second);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMapView_HSTRING_ApplicationDataContainer IID___FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer
#define IMapView_HSTRING_ApplicationDataContainerVtbl __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainerVtbl
#define IMapView_HSTRING_ApplicationDataContainer __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer
#define IMapView_HSTRING_ApplicationDataContainer_QueryInterface __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_QueryInterface
#define IMapView_HSTRING_ApplicationDataContainer_AddRef __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_AddRef
#define IMapView_HSTRING_ApplicationDataContainer_Release __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Release
#define IMapView_HSTRING_ApplicationDataContainer_GetIids __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetIids
#define IMapView_HSTRING_ApplicationDataContainer_GetRuntimeClassName __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetRuntimeClassName
#define IMapView_HSTRING_ApplicationDataContainer_GetTrustLevel __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_GetTrustLevel
#define IMapView_HSTRING_ApplicationDataContainer_Lookup __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Lookup
#define IMapView_HSTRING_ApplicationDataContainer_get_Size __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_get_Size
#define IMapView_HSTRING_ApplicationDataContainer_HasKey __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_HasKey
#define IMapView_HSTRING_ApplicationDataContainer_Split __FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_Split
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMapView_2_HSTRING_Windows__CStorage__CApplicationDataContainer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Storage::IStorageItem* > interface
 */
#ifndef ____FIVectorView_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CStorage__CIStorageItem, 0x85575a41, 0x06cb, 0x58d0, 0xb9,0x8a, 0x7c,0x8f,0x06,0xe6,0xe9,0xd7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("85575a41-06cb-58d0-b98a-7c8f06e6e9d7")
                IVectorView<ABI::Windows::Storage::IStorageItem* > : IVectorView_impl<ABI::Windows::Storage::IStorageItem* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CStorage__CIStorageItem, 0x85575a41, 0x06cb, 0x58d0, 0xb9,0x8a, 0x7c,0x8f,0x06,0xe6,0xe9,0xd7)
#endif
#else
typedef struct __FIVectorView_1_Windows__CStorage__CIStorageItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CStorage__CIStorageItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CStorage__CIStorageItem *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Storage::IStorageItem* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        UINT32 index,
        __x_ABI_CWindows_CStorage_CIStorageItem **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        __x_ABI_CWindows_CStorage_CIStorageItem *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CStorage_CIStorageItem **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CStorage__CIStorageItemVtbl;

interface __FIVectorView_1_Windows__CStorage__CIStorageItem {
    CONST_VTBL __FIVectorView_1_Windows__CStorage__CIStorageItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CStorage__CIStorageItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CStorage__CIStorageItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CStorage__CIStorageItem_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CStorage__CIStorageItem_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CStorage__CIStorageItem_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Storage::IStorageItem* > methods ***/
#define __FIVectorView_1_Windows__CStorage__CIStorageItem_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CStorage__CIStorageItem_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CStorage__CIStorageItem_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CStorage__CIStorageItem_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CIStorageItem_QueryInterface(__FIVectorView_1_Windows__CStorage__CIStorageItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CStorage__CIStorageItem_AddRef(__FIVectorView_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CStorage__CIStorageItem_Release(__FIVectorView_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CIStorageItem_GetIids(__FIVectorView_1_Windows__CStorage__CIStorageItem* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName(__FIVectorView_1_Windows__CStorage__CIStorageItem* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CIStorageItem_GetTrustLevel(__FIVectorView_1_Windows__CStorage__CIStorageItem* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Storage::IStorageItem* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CIStorageItem_GetAt(__FIVectorView_1_Windows__CStorage__CIStorageItem* This,UINT32 index,__x_ABI_CWindows_CStorage_CIStorageItem **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CIStorageItem_get_Size(__FIVectorView_1_Windows__CStorage__CIStorageItem* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CIStorageItem_IndexOf(__FIVectorView_1_Windows__CStorage__CIStorageItem* This,__x_ABI_CWindows_CStorage_CIStorageItem *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CIStorageItem_GetMany(__FIVectorView_1_Windows__CStorage__CIStorageItem* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CStorage_CIStorageItem **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_IStorageItem IID___FIVectorView_1_Windows__CStorage__CIStorageItem
#define IVectorView_IStorageItemVtbl __FIVectorView_1_Windows__CStorage__CIStorageItemVtbl
#define IVectorView_IStorageItem __FIVectorView_1_Windows__CStorage__CIStorageItem
#define IVectorView_IStorageItem_QueryInterface __FIVectorView_1_Windows__CStorage__CIStorageItem_QueryInterface
#define IVectorView_IStorageItem_AddRef __FIVectorView_1_Windows__CStorage__CIStorageItem_AddRef
#define IVectorView_IStorageItem_Release __FIVectorView_1_Windows__CStorage__CIStorageItem_Release
#define IVectorView_IStorageItem_GetIids __FIVectorView_1_Windows__CStorage__CIStorageItem_GetIids
#define IVectorView_IStorageItem_GetRuntimeClassName __FIVectorView_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName
#define IVectorView_IStorageItem_GetTrustLevel __FIVectorView_1_Windows__CStorage__CIStorageItem_GetTrustLevel
#define IVectorView_IStorageItem_GetAt __FIVectorView_1_Windows__CStorage__CIStorageItem_GetAt
#define IVectorView_IStorageItem_get_Size __FIVectorView_1_Windows__CStorage__CIStorageItem_get_Size
#define IVectorView_IStorageItem_IndexOf __FIVectorView_1_Windows__CStorage__CIStorageItem_IndexOf
#define IVectorView_IStorageItem_GetMany __FIVectorView_1_Windows__CStorage__CIStorageItem_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Storage::StorageFile* > interface
 */
#ifndef ____FIVectorView_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CStorage__CStorageFile, 0x80646519, 0x5e2a, 0x595d, 0xa8,0xcd, 0x2a,0x24,0xb4,0x06,0x7f,0x1b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("80646519-5e2a-595d-a8cd-2a24b4067f1b")
                IVectorView<ABI::Windows::Storage::StorageFile* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::StorageFile*, ABI::Windows::Storage::IStorageFile* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CStorage__CStorageFile, 0x80646519, 0x5e2a, 0x595d, 0xa8,0xcd, 0x2a,0x24,0xb4,0x06,0x7f,0x1b)
#endif
#else
typedef struct __FIVectorView_1_Windows__CStorage__CStorageFileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CStorage__CStorageFile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CStorage__CStorageFile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CStorage__CStorageFile *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CStorage__CStorageFile *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CStorage__CStorageFile *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CStorage__CStorageFile *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Storage::StorageFile* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CStorage__CStorageFile *This,
        UINT32 index,
        __x_ABI_CWindows_CStorage_CIStorageFile **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CStorage__CStorageFile *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CStorage__CStorageFile *This,
        __x_ABI_CWindows_CStorage_CIStorageFile *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CStorage__CStorageFile *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CStorage_CIStorageFile **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CStorage__CStorageFileVtbl;

interface __FIVectorView_1_Windows__CStorage__CStorageFile {
    CONST_VTBL __FIVectorView_1_Windows__CStorage__CStorageFileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CStorage__CStorageFile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CStorage__CStorageFile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CStorage__CStorageFile_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CStorage__CStorageFile_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CStorage__CStorageFile_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CStorage__CStorageFile_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Storage::StorageFile* > methods ***/
#define __FIVectorView_1_Windows__CStorage__CStorageFile_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CStorage__CStorageFile_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CStorage__CStorageFile_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CStorage__CStorageFile_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFile_QueryInterface(__FIVectorView_1_Windows__CStorage__CStorageFile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CStorage__CStorageFile_AddRef(__FIVectorView_1_Windows__CStorage__CStorageFile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CStorage__CStorageFile_Release(__FIVectorView_1_Windows__CStorage__CStorageFile* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFile_GetIids(__FIVectorView_1_Windows__CStorage__CStorageFile* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFile_GetRuntimeClassName(__FIVectorView_1_Windows__CStorage__CStorageFile* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFile_GetTrustLevel(__FIVectorView_1_Windows__CStorage__CStorageFile* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Storage::StorageFile* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFile_GetAt(__FIVectorView_1_Windows__CStorage__CStorageFile* This,UINT32 index,__x_ABI_CWindows_CStorage_CIStorageFile **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFile_get_Size(__FIVectorView_1_Windows__CStorage__CStorageFile* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFile_IndexOf(__FIVectorView_1_Windows__CStorage__CStorageFile* This,__x_ABI_CWindows_CStorage_CIStorageFile *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFile_GetMany(__FIVectorView_1_Windows__CStorage__CStorageFile* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CStorage_CIStorageFile **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_StorageFile IID___FIVectorView_1_Windows__CStorage__CStorageFile
#define IVectorView_StorageFileVtbl __FIVectorView_1_Windows__CStorage__CStorageFileVtbl
#define IVectorView_StorageFile __FIVectorView_1_Windows__CStorage__CStorageFile
#define IVectorView_StorageFile_QueryInterface __FIVectorView_1_Windows__CStorage__CStorageFile_QueryInterface
#define IVectorView_StorageFile_AddRef __FIVectorView_1_Windows__CStorage__CStorageFile_AddRef
#define IVectorView_StorageFile_Release __FIVectorView_1_Windows__CStorage__CStorageFile_Release
#define IVectorView_StorageFile_GetIids __FIVectorView_1_Windows__CStorage__CStorageFile_GetIids
#define IVectorView_StorageFile_GetRuntimeClassName __FIVectorView_1_Windows__CStorage__CStorageFile_GetRuntimeClassName
#define IVectorView_StorageFile_GetTrustLevel __FIVectorView_1_Windows__CStorage__CStorageFile_GetTrustLevel
#define IVectorView_StorageFile_GetAt __FIVectorView_1_Windows__CStorage__CStorageFile_GetAt
#define IVectorView_StorageFile_get_Size __FIVectorView_1_Windows__CStorage__CStorageFile_get_Size
#define IVectorView_StorageFile_IndexOf __FIVectorView_1_Windows__CStorage__CStorageFile_IndexOf
#define IVectorView_StorageFile_GetMany __FIVectorView_1_Windows__CStorage__CStorageFile_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Storage::StorageFolder* > interface
 */
#ifndef ____FIVectorView_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CStorage__CStorageFolder, 0xe20debc6, 0xdc4e, 0x542e, 0xa2,0xe7, 0xa2,0x4d,0x19,0xc8,0xdd,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("e20debc6-dc4e-542e-a2e7-a24d19c8dd62")
                IVectorView<ABI::Windows::Storage::StorageFolder* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::StorageFolder*, ABI::Windows::Storage::IStorageFolder* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CStorage__CStorageFolder, 0xe20debc6, 0xdc4e, 0x542e, 0xa2,0xe7, 0xa2,0x4d,0x19,0xc8,0xdd,0x62)
#endif
#else
typedef struct __FIVectorView_1_Windows__CStorage__CStorageFolderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CStorage__CStorageFolder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CStorage__CStorageFolder *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Storage::StorageFolder* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        UINT32 index,
        __x_ABI_CWindows_CStorage_CIStorageFolder **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CStorage_CIStorageFolder **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CStorage__CStorageFolderVtbl;

interface __FIVectorView_1_Windows__CStorage__CStorageFolder {
    CONST_VTBL __FIVectorView_1_Windows__CStorage__CStorageFolderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CStorage__CStorageFolder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CStorage__CStorageFolder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CStorage__CStorageFolder_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CStorage__CStorageFolder_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CStorage__CStorageFolder_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CStorage__CStorageFolder_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Storage::StorageFolder* > methods ***/
#define __FIVectorView_1_Windows__CStorage__CStorageFolder_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CStorage__CStorageFolder_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CStorage__CStorageFolder_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CStorage__CStorageFolder_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFolder_QueryInterface(__FIVectorView_1_Windows__CStorage__CStorageFolder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CStorage__CStorageFolder_AddRef(__FIVectorView_1_Windows__CStorage__CStorageFolder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CStorage__CStorageFolder_Release(__FIVectorView_1_Windows__CStorage__CStorageFolder* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFolder_GetIids(__FIVectorView_1_Windows__CStorage__CStorageFolder* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFolder_GetRuntimeClassName(__FIVectorView_1_Windows__CStorage__CStorageFolder* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFolder_GetTrustLevel(__FIVectorView_1_Windows__CStorage__CStorageFolder* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Storage::StorageFolder* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFolder_GetAt(__FIVectorView_1_Windows__CStorage__CStorageFolder* This,UINT32 index,__x_ABI_CWindows_CStorage_CIStorageFolder **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFolder_get_Size(__FIVectorView_1_Windows__CStorage__CStorageFolder* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFolder_IndexOf(__FIVectorView_1_Windows__CStorage__CStorageFolder* This,__x_ABI_CWindows_CStorage_CIStorageFolder *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStorageFolder_GetMany(__FIVectorView_1_Windows__CStorage__CStorageFolder* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CStorage_CIStorageFolder **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_StorageFolder IID___FIVectorView_1_Windows__CStorage__CStorageFolder
#define IVectorView_StorageFolderVtbl __FIVectorView_1_Windows__CStorage__CStorageFolderVtbl
#define IVectorView_StorageFolder __FIVectorView_1_Windows__CStorage__CStorageFolder
#define IVectorView_StorageFolder_QueryInterface __FIVectorView_1_Windows__CStorage__CStorageFolder_QueryInterface
#define IVectorView_StorageFolder_AddRef __FIVectorView_1_Windows__CStorage__CStorageFolder_AddRef
#define IVectorView_StorageFolder_Release __FIVectorView_1_Windows__CStorage__CStorageFolder_Release
#define IVectorView_StorageFolder_GetIids __FIVectorView_1_Windows__CStorage__CStorageFolder_GetIids
#define IVectorView_StorageFolder_GetRuntimeClassName __FIVectorView_1_Windows__CStorage__CStorageFolder_GetRuntimeClassName
#define IVectorView_StorageFolder_GetTrustLevel __FIVectorView_1_Windows__CStorage__CStorageFolder_GetTrustLevel
#define IVectorView_StorageFolder_GetAt __FIVectorView_1_Windows__CStorage__CStorageFolder_GetAt
#define IVectorView_StorageFolder_get_Size __FIVectorView_1_Windows__CStorage__CStorageFolder_get_Size
#define IVectorView_StorageFolder_IndexOf __FIVectorView_1_Windows__CStorage__CStorageFolder_IndexOf
#define IVectorView_StorageFolder_GetMany __FIVectorView_1_Windows__CStorage__CStorageFolder_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem, 0x51436e75, 0xace1, 0x5a68, 0xb2,0x60, 0xf8,0x43,0xb8,0x46,0xf0,0xdb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("51436e75-ace1-5a68-b260-f843b846f0db")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem, 0x51436e75, 0xace1, 0x5a68, 0xb2,0x60, 0xf8,0x43,0xb8,0x46,0xf0,0xdb)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItemVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_AddRef(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_Release(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_Invoke(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IVectorView_IStorageItem IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem
#define IAsyncOperationCompletedHandler_IVectorView_IStorageItemVtbl __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItemVtbl
#define IAsyncOperationCompletedHandler_IVectorView_IStorageItem __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem
#define IAsyncOperationCompletedHandler_IVectorView_IStorageItem_QueryInterface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_QueryInterface
#define IAsyncOperationCompletedHandler_IVectorView_IStorageItem_AddRef __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_AddRef
#define IAsyncOperationCompletedHandler_IVectorView_IStorageItem_Release __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_Release
#define IAsyncOperationCompletedHandler_IVectorView_IStorageItem_Invoke __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile, 0xcb4206c5, 0x0988, 0x5104, 0xaf,0xa9, 0x25,0x3c,0x29,0x8f,0x86,0xfd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("cb4206c5-0988-5104-afa9-253c298f86fd")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile, 0xcb4206c5, 0x0988, 0x5104, 0xaf,0xa9, 0x25,0x3c,0x29,0x8f,0x86,0xfd)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFileVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_AddRef(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_Release(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_Invoke(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IVectorView_StorageFile IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile
#define IAsyncOperationCompletedHandler_IVectorView_StorageFileVtbl __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFileVtbl
#define IAsyncOperationCompletedHandler_IVectorView_StorageFile __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile
#define IAsyncOperationCompletedHandler_IVectorView_StorageFile_QueryInterface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_QueryInterface
#define IAsyncOperationCompletedHandler_IVectorView_StorageFile_AddRef __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_AddRef
#define IAsyncOperationCompletedHandler_IVectorView_StorageFile_Release __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_Release
#define IAsyncOperationCompletedHandler_IVectorView_StorageFile_Invoke __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder, 0xed2d1d9b, 0x26ec, 0x5be7, 0xa8,0xa3, 0x56,0x45,0x89,0x33,0xd2,0x5f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("ed2d1d9b-26ec-5be7-a8a3-56458933d25f")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder, 0xed2d1d9b, 0x26ec, 0x5be7, 0xa8,0xa3, 0x56,0x45,0x89,0x33,0xd2,0x5f)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolderVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_AddRef(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_Release(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_Invoke(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IVectorView_StorageFolder IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder
#define IAsyncOperationCompletedHandler_IVectorView_StorageFolderVtbl __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolderVtbl
#define IAsyncOperationCompletedHandler_IVectorView_StorageFolder __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder
#define IAsyncOperationCompletedHandler_IVectorView_StorageFolder_QueryInterface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_QueryInterface
#define IAsyncOperationCompletedHandler_IVectorView_StorageFolder_AddRef __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_AddRef
#define IAsyncOperationCompletedHandler_IVectorView_StorageFolder_Release __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_Release
#define IAsyncOperationCompletedHandler_IVectorView_StorageFolder_Invoke __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::ApplicationData* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData, 0xabafe590, 0x65fe, 0x520a, 0x9d,0x7c, 0x6a,0xb5,0xf1,0x88,0x22,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("abafe590-65fe-520a-9d7c-6ab5f1882237")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::ApplicationData* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::ApplicationData*, ABI::Windows::Storage::IApplicationData* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData, 0xabafe590, 0x65fe, 0x520a, 0x9d,0x7c, 0x6a,0xb5,0xf1,0x88,0x22,0x37)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationDataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::ApplicationData* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData *This,
        __FIAsyncOperation_1_Windows__CStorage__CApplicationData *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationDataVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationDataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::ApplicationData* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::ApplicationData* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData* This,__FIAsyncOperation_1_Windows__CStorage__CApplicationData *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_ApplicationData IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData
#define IAsyncOperationCompletedHandler_ApplicationDataVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationDataVtbl
#define IAsyncOperationCompletedHandler_ApplicationData __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData
#define IAsyncOperationCompletedHandler_ApplicationData_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_QueryInterface
#define IAsyncOperationCompletedHandler_ApplicationData_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_AddRef
#define IAsyncOperationCompletedHandler_ApplicationData_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_Release
#define IAsyncOperationCompletedHandler_ApplicationData_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::KnownFoldersAccessStatus > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus, 0xa2f87fc6, 0x4ea5, 0x58cf, 0x94,0x90, 0x18,0x16,0x04,0xfb,0xd6,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("a2f87fc6-4ea5-58cf-9490-181604fbd6a2")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::KnownFoldersAccessStatus > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Storage::KnownFoldersAccessStatus >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus, 0xa2f87fc6, 0x4ea5, 0x58cf, 0x94,0x90, 0x18,0x16,0x04,0xfb,0xd6,0xa2)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::KnownFoldersAccessStatus > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus *This,
        __FIAsyncOperation_1_KnownFoldersAccessStatus *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatusVtbl;

interface __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::KnownFoldersAccessStatus > methods ***/
#define __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_QueryInterface(__FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_AddRef(__FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_Release(__FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::KnownFoldersAccessStatus > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_Invoke(__FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus* This,__FIAsyncOperation_1_KnownFoldersAccessStatus *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_KnownFoldersAccessStatus IID___FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus
#define IAsyncOperationCompletedHandler_KnownFoldersAccessStatusVtbl __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatusVtbl
#define IAsyncOperationCompletedHandler_KnownFoldersAccessStatus __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus
#define IAsyncOperationCompletedHandler_KnownFoldersAccessStatus_QueryInterface __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_QueryInterface
#define IAsyncOperationCompletedHandler_KnownFoldersAccessStatus_AddRef __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_AddRef
#define IAsyncOperationCompletedHandler_KnownFoldersAccessStatus_Release __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_Release
#define IAsyncOperationCompletedHandler_KnownFoldersAccessStatus_Invoke __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::IStorageItem* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem, 0x92c3102f, 0xa327, 0x5318, 0xa6,0xc1, 0x76,0xf6,0xb2,0xa0,0xab,0xfb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("92c3102f-a327-5318-a6c1-76f6b2a0abfb")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::IStorageItem* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Storage::IStorageItem* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem, 0x92c3102f, 0xa327, 0x5318, 0xa6,0xc1, 0x76,0xf6,0xb2,0xa0,0xab,0xfb)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::IStorageItem* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem *This,
        __FIAsyncOperation_1_Windows__CStorage__CIStorageItem *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItemVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::IStorageItem* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::IStorageItem* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem* This,__FIAsyncOperation_1_Windows__CStorage__CIStorageItem *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IStorageItem IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem
#define IAsyncOperationCompletedHandler_IStorageItemVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItemVtbl
#define IAsyncOperationCompletedHandler_IStorageItem __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem
#define IAsyncOperationCompletedHandler_IStorageItem_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_QueryInterface
#define IAsyncOperationCompletedHandler_IStorageItem_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_AddRef
#define IAsyncOperationCompletedHandler_IStorageItem_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_Release
#define IAsyncOperationCompletedHandler_IStorageItem_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFile* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile, 0xe521c894, 0x2c26, 0x5946, 0x9e,0x61, 0x2b,0x5e,0x18,0x8d,0x01,0xed);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("e521c894-2c26-5946-9e61-2b5e188d01ed")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFile* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::StorageFile*, ABI::Windows::Storage::IStorageFile* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile, 0xe521c894, 0x2c26, 0x5946, 0x9e,0x61, 0x2b,0x5e,0x18,0x8d,0x01,0xed)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFile* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile *This,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFileVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFile* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFile* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile* This,__FIAsyncOperation_1_Windows__CStorage__CStorageFile *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_StorageFile IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile
#define IAsyncOperationCompletedHandler_StorageFileVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFileVtbl
#define IAsyncOperationCompletedHandler_StorageFile __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile
#define IAsyncOperationCompletedHandler_StorageFile_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_QueryInterface
#define IAsyncOperationCompletedHandler_StorageFile_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_AddRef
#define IAsyncOperationCompletedHandler_StorageFile_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_Release
#define IAsyncOperationCompletedHandler_StorageFile_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFolder* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder, 0xc211026e, 0x9e63, 0x5452, 0xba,0x54, 0x3a,0x07,0xd6,0xa9,0x68,0x74);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("c211026e-9e63-5452-ba54-3a07d6a96874")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFolder* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::StorageFolder*, ABI::Windows::Storage::IStorageFolder* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder, 0xc211026e, 0x9e63, 0x5452, 0xba,0x54, 0x3a,0x07,0xd6,0xa9,0x68,0x74)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFolder* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder *This,
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolderVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFolder* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageFolder* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder* This,__FIAsyncOperation_1_Windows__CStorage__CStorageFolder *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_StorageFolder IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder
#define IAsyncOperationCompletedHandler_StorageFolderVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolderVtbl
#define IAsyncOperationCompletedHandler_StorageFolder __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder
#define IAsyncOperationCompletedHandler_StorageFolder_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_QueryInterface
#define IAsyncOperationCompletedHandler_StorageFolder_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_AddRef
#define IAsyncOperationCompletedHandler_StorageFolder_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_Release
#define IAsyncOperationCompletedHandler_StorageFolder_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageStreamTransaction* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction, 0xd11739e6, 0x2995, 0x5d33, 0xbf,0xff, 0x51,0xb6,0x04,0x1f,0x68,0xc1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d11739e6-2995-5d33-bfff-51b6041f68c1")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageStreamTransaction* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::StorageStreamTransaction*, ABI::Windows::Storage::IStorageStreamTransaction* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction, 0xd11739e6, 0x2995, 0x5d33, 0xbf,0xff, 0x51,0xb6,0x04,0x1f,0x68,0xc1)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransactionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageStreamTransaction* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction *This,
        __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransactionVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransactionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageStreamTransaction* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::StorageStreamTransaction* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction* This,__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_StorageStreamTransaction IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction
#define IAsyncOperationCompletedHandler_StorageStreamTransactionVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransactionVtbl
#define IAsyncOperationCompletedHandler_StorageStreamTransaction __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction
#define IAsyncOperationCompletedHandler_StorageStreamTransaction_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_QueryInterface
#define IAsyncOperationCompletedHandler_StorageStreamTransaction_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_AddRef
#define IAsyncOperationCompletedHandler_StorageStreamTransaction_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_Release
#define IAsyncOperationCompletedHandler_StorageStreamTransaction_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem, 0x4b1c0fd7, 0x7a01, 0x5e7a, 0xa6,0xfe, 0xbe,0x45,0x00,0x28,0x3f,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("4b1c0fd7-7a01-5e7a-a6fe-be4500283f23")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem, 0x4b1c0fd7, 0x7a01, 0x5e7a, 0xa6,0xfe, 0xbe,0x45,0x00,0x28,0x3f,0x23)
#endif
#else
typedef struct __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem *This,
        __FIVectorView_1_Windows__CStorage__CIStorageItem **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItemVtbl;

interface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem {
    CONST_VTBL __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* > methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_QueryInterface(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_AddRef(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_Release(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetIids(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetTrustLevel(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::IStorageItem* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_put_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_get_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CIStorageItem **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetResults(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem* This,__FIVectorView_1_Windows__CStorage__CIStorageItem **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IVectorView_IStorageItem IID___FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem
#define IAsyncOperation_IVectorView_IStorageItemVtbl __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItemVtbl
#define IAsyncOperation_IVectorView_IStorageItem __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem
#define IAsyncOperation_IVectorView_IStorageItem_QueryInterface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_QueryInterface
#define IAsyncOperation_IVectorView_IStorageItem_AddRef __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_AddRef
#define IAsyncOperation_IVectorView_IStorageItem_Release __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_Release
#define IAsyncOperation_IVectorView_IStorageItem_GetIids __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetIids
#define IAsyncOperation_IVectorView_IStorageItem_GetRuntimeClassName __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName
#define IAsyncOperation_IVectorView_IStorageItem_GetTrustLevel __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetTrustLevel
#define IAsyncOperation_IVectorView_IStorageItem_put_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_put_Completed
#define IAsyncOperation_IVectorView_IStorageItem_get_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_get_Completed
#define IAsyncOperation_IVectorView_IStorageItem_GetResults __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile, 0x03362e33, 0xe413, 0x5f29, 0x97,0xd0, 0x48,0xa4,0x78,0x09,0x35,0xf9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("03362e33-e413-5f29-97d0-48a4780935f9")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile, 0x03362e33, 0xe413, 0x5f29, 0x97,0xd0, 0x48,0xa4,0x78,0x09,0x35,0xf9)
#endif
#else
typedef struct __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile *This,
        __FIVectorView_1_Windows__CStorage__CStorageFile **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFileVtbl;

interface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile {
    CONST_VTBL __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* > methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_QueryInterface(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_AddRef(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_Release(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetIids(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetRuntimeClassName(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetTrustLevel(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFile* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_put_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_get_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFile **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetResults(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile* This,__FIVectorView_1_Windows__CStorage__CStorageFile **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IVectorView_StorageFile IID___FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile
#define IAsyncOperation_IVectorView_StorageFileVtbl __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFileVtbl
#define IAsyncOperation_IVectorView_StorageFile __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile
#define IAsyncOperation_IVectorView_StorageFile_QueryInterface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_QueryInterface
#define IAsyncOperation_IVectorView_StorageFile_AddRef __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_AddRef
#define IAsyncOperation_IVectorView_StorageFile_Release __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_Release
#define IAsyncOperation_IVectorView_StorageFile_GetIids __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetIids
#define IAsyncOperation_IVectorView_StorageFile_GetRuntimeClassName __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetRuntimeClassName
#define IAsyncOperation_IVectorView_StorageFile_GetTrustLevel __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetTrustLevel
#define IAsyncOperation_IVectorView_StorageFile_put_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_put_Completed
#define IAsyncOperation_IVectorView_StorageFile_get_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_get_Completed
#define IAsyncOperation_IVectorView_StorageFile_GetResults __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder, 0xca40b21b, 0xaeb1, 0x5a61, 0x9e,0x08, 0x3b,0xd5,0xd9,0x59,0x40,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("ca40b21b-aeb1-5a61-9e08-3bd5d9594023")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder, 0xca40b21b, 0xaeb1, 0x5a61, 0x9e,0x08, 0x3b,0xd5,0xd9,0x59,0x40,0x23)
#endif
#else
typedef struct __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder *This,
        __FIVectorView_1_Windows__CStorage__CStorageFolder **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolderVtbl;

interface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder {
    CONST_VTBL __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* > methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_QueryInterface(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_AddRef(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_Release(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetIids(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetRuntimeClassName(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetTrustLevel(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::StorageFolder* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_put_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_get_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CStorage__CStorageFolder **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetResults(__FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder* This,__FIVectorView_1_Windows__CStorage__CStorageFolder **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IVectorView_StorageFolder IID___FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder
#define IAsyncOperation_IVectorView_StorageFolderVtbl __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolderVtbl
#define IAsyncOperation_IVectorView_StorageFolder __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder
#define IAsyncOperation_IVectorView_StorageFolder_QueryInterface __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_QueryInterface
#define IAsyncOperation_IVectorView_StorageFolder_AddRef __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_AddRef
#define IAsyncOperation_IVectorView_StorageFolder_Release __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_Release
#define IAsyncOperation_IVectorView_StorageFolder_GetIids __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetIids
#define IAsyncOperation_IVectorView_StorageFolder_GetRuntimeClassName __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetRuntimeClassName
#define IAsyncOperation_IVectorView_StorageFolder_GetTrustLevel __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetTrustLevel
#define IAsyncOperation_IVectorView_StorageFolder_put_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_put_Completed
#define IAsyncOperation_IVectorView_StorageFolder_get_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_get_Completed
#define IAsyncOperation_IVectorView_StorageFolder_GetResults __FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIVectorView_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::ApplicationData* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CApplicationData_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CApplicationData_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CApplicationData, 0x31456b58, 0xa5cb, 0x5c5b, 0xbd,0x6e, 0xcc,0xce,0x3a,0x7b,0xf4,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("31456b58-a5cb-5c5b-bd6e-ccce3a7bf4b4")
            IAsyncOperation<ABI::Windows::Storage::ApplicationData* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::ApplicationData*, ABI::Windows::Storage::IApplicationData* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CApplicationData, 0x31456b58, 0xa5cb, 0x5c5b, 0xbd,0x6e, 0xcc,0xce,0x3a,0x7b,0xf4,0xb4)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CApplicationDataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CApplicationData *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CApplicationData *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CApplicationData *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CApplicationData *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CApplicationData *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CApplicationData *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::ApplicationData* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CApplicationData *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CApplicationData *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CApplicationData *This,
        __x_ABI_CWindows_CStorage_CIApplicationData **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CApplicationDataVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CApplicationData {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CApplicationDataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CApplicationData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CApplicationData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CApplicationData_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::ApplicationData* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CApplicationData_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CApplicationData_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CApplicationData_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CApplicationData* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CApplicationData_AddRef(__FIAsyncOperation_1_Windows__CStorage__CApplicationData* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CApplicationData_Release(__FIAsyncOperation_1_Windows__CStorage__CApplicationData* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetIids(__FIAsyncOperation_1_Windows__CStorage__CApplicationData* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CApplicationData* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CApplicationData* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::ApplicationData* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CApplicationData_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CApplicationData* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CApplicationData_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CApplicationData* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CApplicationData **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetResults(__FIAsyncOperation_1_Windows__CStorage__CApplicationData* This,__x_ABI_CWindows_CStorage_CIApplicationData **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_ApplicationData IID___FIAsyncOperation_1_Windows__CStorage__CApplicationData
#define IAsyncOperation_ApplicationDataVtbl __FIAsyncOperation_1_Windows__CStorage__CApplicationDataVtbl
#define IAsyncOperation_ApplicationData __FIAsyncOperation_1_Windows__CStorage__CApplicationData
#define IAsyncOperation_ApplicationData_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CApplicationData_QueryInterface
#define IAsyncOperation_ApplicationData_AddRef __FIAsyncOperation_1_Windows__CStorage__CApplicationData_AddRef
#define IAsyncOperation_ApplicationData_Release __FIAsyncOperation_1_Windows__CStorage__CApplicationData_Release
#define IAsyncOperation_ApplicationData_GetIids __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetIids
#define IAsyncOperation_ApplicationData_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetRuntimeClassName
#define IAsyncOperation_ApplicationData_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetTrustLevel
#define IAsyncOperation_ApplicationData_put_Completed __FIAsyncOperation_1_Windows__CStorage__CApplicationData_put_Completed
#define IAsyncOperation_ApplicationData_get_Completed __FIAsyncOperation_1_Windows__CStorage__CApplicationData_get_Completed
#define IAsyncOperation_ApplicationData_GetResults __FIAsyncOperation_1_Windows__CStorage__CApplicationData_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CApplicationData_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::KnownFoldersAccessStatus > interface
 */
#ifndef ____FIAsyncOperation_1_KnownFoldersAccessStatus_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_KnownFoldersAccessStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_KnownFoldersAccessStatus, 0xd7f094b5, 0x0ea2, 0x5654, 0x85,0xb9, 0x38,0xee,0x5d,0xe9,0x0f,0xfa);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d7f094b5-0ea2-5654-85b9-38ee5de90ffa")
            IAsyncOperation<ABI::Windows::Storage::KnownFoldersAccessStatus > : IAsyncOperation_impl<ABI::Windows::Storage::KnownFoldersAccessStatus >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_KnownFoldersAccessStatus, 0xd7f094b5, 0x0ea2, 0x5654, 0x85,0xb9, 0x38,0xee,0x5d,0xe9,0x0f,0xfa)
#endif
#else
typedef struct __FIAsyncOperation_1_KnownFoldersAccessStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_KnownFoldersAccessStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_KnownFoldersAccessStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_KnownFoldersAccessStatus *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_KnownFoldersAccessStatus *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_KnownFoldersAccessStatus *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_KnownFoldersAccessStatus *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::KnownFoldersAccessStatus > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_KnownFoldersAccessStatus *This,
        __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_KnownFoldersAccessStatus *This,
        __FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_KnownFoldersAccessStatus *This,
        __x_ABI_CWindows_CStorage_CKnownFoldersAccessStatus *results);

    END_INTERFACE
} __FIAsyncOperation_1_KnownFoldersAccessStatusVtbl;

interface __FIAsyncOperation_1_KnownFoldersAccessStatus {
    CONST_VTBL __FIAsyncOperation_1_KnownFoldersAccessStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_KnownFoldersAccessStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_KnownFoldersAccessStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_KnownFoldersAccessStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_KnownFoldersAccessStatus_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_KnownFoldersAccessStatus_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_KnownFoldersAccessStatus_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::KnownFoldersAccessStatus > methods ***/
#define __FIAsyncOperation_1_KnownFoldersAccessStatus_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_KnownFoldersAccessStatus_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_KnownFoldersAccessStatus_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_KnownFoldersAccessStatus_QueryInterface(__FIAsyncOperation_1_KnownFoldersAccessStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_KnownFoldersAccessStatus_AddRef(__FIAsyncOperation_1_KnownFoldersAccessStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_KnownFoldersAccessStatus_Release(__FIAsyncOperation_1_KnownFoldersAccessStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_KnownFoldersAccessStatus_GetIids(__FIAsyncOperation_1_KnownFoldersAccessStatus* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_KnownFoldersAccessStatus_GetRuntimeClassName(__FIAsyncOperation_1_KnownFoldersAccessStatus* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_KnownFoldersAccessStatus_GetTrustLevel(__FIAsyncOperation_1_KnownFoldersAccessStatus* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::KnownFoldersAccessStatus > methods ***/
static inline HRESULT __FIAsyncOperation_1_KnownFoldersAccessStatus_put_Completed(__FIAsyncOperation_1_KnownFoldersAccessStatus* This,__FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_KnownFoldersAccessStatus_get_Completed(__FIAsyncOperation_1_KnownFoldersAccessStatus* This,__FIAsyncOperationCompletedHandler_1_KnownFoldersAccessStatus **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_KnownFoldersAccessStatus_GetResults(__FIAsyncOperation_1_KnownFoldersAccessStatus* This,__x_ABI_CWindows_CStorage_CKnownFoldersAccessStatus *results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_KnownFoldersAccessStatus IID___FIAsyncOperation_1_KnownFoldersAccessStatus
#define IAsyncOperation_KnownFoldersAccessStatusVtbl __FIAsyncOperation_1_KnownFoldersAccessStatusVtbl
#define IAsyncOperation_KnownFoldersAccessStatus __FIAsyncOperation_1_KnownFoldersAccessStatus
#define IAsyncOperation_KnownFoldersAccessStatus_QueryInterface __FIAsyncOperation_1_KnownFoldersAccessStatus_QueryInterface
#define IAsyncOperation_KnownFoldersAccessStatus_AddRef __FIAsyncOperation_1_KnownFoldersAccessStatus_AddRef
#define IAsyncOperation_KnownFoldersAccessStatus_Release __FIAsyncOperation_1_KnownFoldersAccessStatus_Release
#define IAsyncOperation_KnownFoldersAccessStatus_GetIids __FIAsyncOperation_1_KnownFoldersAccessStatus_GetIids
#define IAsyncOperation_KnownFoldersAccessStatus_GetRuntimeClassName __FIAsyncOperation_1_KnownFoldersAccessStatus_GetRuntimeClassName
#define IAsyncOperation_KnownFoldersAccessStatus_GetTrustLevel __FIAsyncOperation_1_KnownFoldersAccessStatus_GetTrustLevel
#define IAsyncOperation_KnownFoldersAccessStatus_put_Completed __FIAsyncOperation_1_KnownFoldersAccessStatus_put_Completed
#define IAsyncOperation_KnownFoldersAccessStatus_get_Completed __FIAsyncOperation_1_KnownFoldersAccessStatus_get_Completed
#define IAsyncOperation_KnownFoldersAccessStatus_GetResults __FIAsyncOperation_1_KnownFoldersAccessStatus_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_KnownFoldersAccessStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::IStorageItem* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CIStorageItem, 0x5fc9c137, 0xebb7, 0x5e6c, 0x9c,0xba, 0x68,0x6f,0x2e,0xc2,0xb0,0xbb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("5fc9c137-ebb7-5e6c-9cba-686f2ec2b0bb")
            IAsyncOperation<ABI::Windows::Storage::IStorageItem* > : IAsyncOperation_impl<ABI::Windows::Storage::IStorageItem* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CIStorageItem, 0x5fc9c137, 0xebb7, 0x5e6c, 0x9c,0xba, 0x68,0x6f,0x2e,0xc2,0xb0,0xbb)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CIStorageItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CIStorageItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CIStorageItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CIStorageItem *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CIStorageItem *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CIStorageItem *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CIStorageItem *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::IStorageItem* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CIStorageItem *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CIStorageItem *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CIStorageItem *This,
        __x_ABI_CWindows_CStorage_CIStorageItem **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CIStorageItemVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CIStorageItem {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CIStorageItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::IStorageItem* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CIStorageItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_AddRef(__FIAsyncOperation_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_Release(__FIAsyncOperation_1_Windows__CStorage__CIStorageItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetIids(__FIAsyncOperation_1_Windows__CStorage__CIStorageItem* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CIStorageItem* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CIStorageItem* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::IStorageItem* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CIStorageItem* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CIStorageItem* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CIStorageItem **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetResults(__FIAsyncOperation_1_Windows__CStorage__CIStorageItem* This,__x_ABI_CWindows_CStorage_CIStorageItem **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IStorageItem IID___FIAsyncOperation_1_Windows__CStorage__CIStorageItem
#define IAsyncOperation_IStorageItemVtbl __FIAsyncOperation_1_Windows__CStorage__CIStorageItemVtbl
#define IAsyncOperation_IStorageItem __FIAsyncOperation_1_Windows__CStorage__CIStorageItem
#define IAsyncOperation_IStorageItem_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_QueryInterface
#define IAsyncOperation_IStorageItem_AddRef __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_AddRef
#define IAsyncOperation_IStorageItem_Release __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_Release
#define IAsyncOperation_IStorageItem_GetIids __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetIids
#define IAsyncOperation_IStorageItem_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetRuntimeClassName
#define IAsyncOperation_IStorageItem_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetTrustLevel
#define IAsyncOperation_IStorageItem_put_Completed __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_put_Completed
#define IAsyncOperation_IStorageItem_get_Completed __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_get_Completed
#define IAsyncOperation_IStorageItem_GetResults __FIAsyncOperation_1_Windows__CStorage__CIStorageItem_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CIStorageItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::StorageFile* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CStorageFile, 0x5e52f8ce, 0xaced, 0x5a42, 0x95,0xb4, 0xf6,0x74,0xdd,0x84,0x88,0x5e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("5e52f8ce-aced-5a42-95b4-f674dd84885e")
            IAsyncOperation<ABI::Windows::Storage::StorageFile* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::StorageFile*, ABI::Windows::Storage::IStorageFile* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CStorageFile, 0x5e52f8ce, 0xaced, 0x5a42, 0x95,0xb4, 0xf6,0x74,0xdd,0x84,0x88,0x5e)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CStorageFileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::StorageFile* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFile *This,
        __x_ABI_CWindows_CStorage_CIStorageFile **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CStorageFileVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CStorageFile {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CStorageFileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFile_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::StorageFile* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFile_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFile_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFile_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CStorageFile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStorageFile_AddRef(__FIAsyncOperation_1_Windows__CStorage__CStorageFile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStorageFile_Release(__FIAsyncOperation_1_Windows__CStorage__CStorageFile* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetIids(__FIAsyncOperation_1_Windows__CStorage__CStorageFile* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CStorageFile* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CStorageFile* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::StorageFile* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFile_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CStorageFile* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFile_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CStorageFile* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFile **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetResults(__FIAsyncOperation_1_Windows__CStorage__CStorageFile* This,__x_ABI_CWindows_CStorage_CIStorageFile **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_StorageFile IID___FIAsyncOperation_1_Windows__CStorage__CStorageFile
#define IAsyncOperation_StorageFileVtbl __FIAsyncOperation_1_Windows__CStorage__CStorageFileVtbl
#define IAsyncOperation_StorageFile __FIAsyncOperation_1_Windows__CStorage__CStorageFile
#define IAsyncOperation_StorageFile_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CStorageFile_QueryInterface
#define IAsyncOperation_StorageFile_AddRef __FIAsyncOperation_1_Windows__CStorage__CStorageFile_AddRef
#define IAsyncOperation_StorageFile_Release __FIAsyncOperation_1_Windows__CStorage__CStorageFile_Release
#define IAsyncOperation_StorageFile_GetIids __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetIids
#define IAsyncOperation_StorageFile_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetRuntimeClassName
#define IAsyncOperation_StorageFile_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetTrustLevel
#define IAsyncOperation_StorageFile_put_Completed __FIAsyncOperation_1_Windows__CStorage__CStorageFile_put_Completed
#define IAsyncOperation_StorageFile_get_Completed __FIAsyncOperation_1_Windows__CStorage__CStorageFile_get_Completed
#define IAsyncOperation_StorageFile_GetResults __FIAsyncOperation_1_Windows__CStorage__CStorageFile_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CStorageFile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::StorageFolder* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CStorageFolder, 0x6be9e7d7, 0xe83a, 0x5cbc, 0x80,0x2c, 0x17,0x68,0x96,0x0b,0x52,0xc3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("6be9e7d7-e83a-5cbc-802c-1768960b52c3")
            IAsyncOperation<ABI::Windows::Storage::StorageFolder* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::StorageFolder*, ABI::Windows::Storage::IStorageFolder* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CStorageFolder, 0x6be9e7d7, 0xe83a, 0x5cbc, 0x80,0x2c, 0x17,0x68,0x96,0x0b,0x52,0xc3)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CStorageFolderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::StorageFolder* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageFolder *This,
        __x_ABI_CWindows_CStorage_CIStorageFolder **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CStorageFolderVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CStorageFolder {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CStorageFolderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::StorageFolder* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CStorageFolder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_AddRef(__FIAsyncOperation_1_Windows__CStorage__CStorageFolder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_Release(__FIAsyncOperation_1_Windows__CStorage__CStorageFolder* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetIids(__FIAsyncOperation_1_Windows__CStorage__CStorageFolder* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CStorageFolder* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CStorageFolder* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::StorageFolder* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CStorageFolder* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CStorageFolder* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageFolder **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetResults(__FIAsyncOperation_1_Windows__CStorage__CStorageFolder* This,__x_ABI_CWindows_CStorage_CIStorageFolder **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_StorageFolder IID___FIAsyncOperation_1_Windows__CStorage__CStorageFolder
#define IAsyncOperation_StorageFolderVtbl __FIAsyncOperation_1_Windows__CStorage__CStorageFolderVtbl
#define IAsyncOperation_StorageFolder __FIAsyncOperation_1_Windows__CStorage__CStorageFolder
#define IAsyncOperation_StorageFolder_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_QueryInterface
#define IAsyncOperation_StorageFolder_AddRef __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_AddRef
#define IAsyncOperation_StorageFolder_Release __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_Release
#define IAsyncOperation_StorageFolder_GetIids __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetIids
#define IAsyncOperation_StorageFolder_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetRuntimeClassName
#define IAsyncOperation_StorageFolder_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetTrustLevel
#define IAsyncOperation_StorageFolder_put_Completed __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_put_Completed
#define IAsyncOperation_StorageFolder_get_Completed __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_get_Completed
#define IAsyncOperation_StorageFolder_GetResults __FIAsyncOperation_1_Windows__CStorage__CStorageFolder_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CStorageFolder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::StorageStreamTransaction* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction, 0x0d81405a, 0x9bd3, 0x5e87, 0x82,0xf4, 0x9b,0x41,0x28,0xa8,0x87,0xeb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("0d81405a-9bd3-5e87-82f4-9b4128a887eb")
            IAsyncOperation<ABI::Windows::Storage::StorageStreamTransaction* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::StorageStreamTransaction*, ABI::Windows::Storage::IStorageStreamTransaction* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction, 0x0d81405a, 0x9bd3, 0x5e87, 0x82,0xf4, 0x9b,0x41,0x28,0xa8,0x87,0xeb)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransactionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::StorageStreamTransaction* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction *This,
        __x_ABI_CWindows_CStorage_CIStorageStreamTransaction **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransactionVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransactionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::StorageStreamTransaction* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_AddRef(__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_Release(__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetIids(__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::StorageStreamTransaction* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStorageStreamTransaction **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetResults(__FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction* This,__x_ABI_CWindows_CStorage_CIStorageStreamTransaction **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_StorageStreamTransaction IID___FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction
#define IAsyncOperation_StorageStreamTransactionVtbl __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransactionVtbl
#define IAsyncOperation_StorageStreamTransaction __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction
#define IAsyncOperation_StorageStreamTransaction_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_QueryInterface
#define IAsyncOperation_StorageStreamTransaction_AddRef __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_AddRef
#define IAsyncOperation_StorageStreamTransaction_Release __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_Release
#define IAsyncOperation_StorageStreamTransaction_GetIids __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetIids
#define IAsyncOperation_StorageStreamTransaction_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetRuntimeClassName
#define IAsyncOperation_StorageStreamTransaction_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetTrustLevel
#define IAsyncOperation_StorageStreamTransaction_put_Completed __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_put_Completed
#define IAsyncOperation_StorageStreamTransaction_get_Completed __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_get_Completed
#define IAsyncOperation_StorageStreamTransaction_GetResults __FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CStorageStreamTransaction_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Storage::ApplicationData*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable, 0xb5348b3b, 0x5081, 0x5ae9, 0x8f,0xa3, 0x4d,0x22,0xd6,0x8f,0xb0,0xea);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("b5348b3b-5081-5ae9-8fa3-4d22d68fb0ea")
            ITypedEventHandler<ABI::Windows::Storage::ApplicationData*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::ApplicationData*, ABI::Windows::Storage::IApplicationData* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable, 0xb5348b3b, 0x5081, 0x5ae9, 0x8f,0xa3, 0x4d,0x22,0xd6,0x8f,0xb0,0xea)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::Storage::ApplicationData*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable *This,
        __x_ABI_CWindows_CStorage_CIApplicationData *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Storage::ApplicationData*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_Release(__FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Storage::ApplicationData*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable* This,__x_ABI_CWindows_CStorage_CIApplicationData *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_ApplicationData_IInspectable IID___FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable
#define ITypedEventHandler_ApplicationData_IInspectableVtbl __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectableVtbl
#define ITypedEventHandler_ApplicationData_IInspectable __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable
#define ITypedEventHandler_ApplicationData_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_QueryInterface
#define ITypedEventHandler_ApplicationData_IInspectable_AddRef __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_AddRef
#define ITypedEventHandler_ApplicationData_IInspectable_Release __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_Release
#define ITypedEventHandler_ApplicationData_IInspectable_Invoke __FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CStorage__CApplicationData_IInspectable_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_storage_h__ */
