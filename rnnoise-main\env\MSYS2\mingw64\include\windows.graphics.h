/*** Autogenerated by WIDL 10.12 from include/windows.graphics.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_graphics_h__
#define __windows_graphics_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CGraphics_CIGeometrySource2D_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CIGeometrySource2D_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CIGeometrySource2D __x_ABI_CWindows_CGraphics_CIGeometrySource2D;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CIGeometrySource2D ABI::Windows::Graphics::IGeometrySource2D
namespace ABI {
    namespace Windows {
        namespace Graphics {
            interface IGeometrySource2D;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_RectInt32_FWD_DEFINED__
#define ____FIIterable_1_RectInt32_FWD_DEFINED__
typedef interface __FIIterable_1_RectInt32 __FIIterable_1_RectInt32;
#ifdef __cplusplus
#define __FIIterable_1_RectInt32 ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Graphics::RectInt32 >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_RectInt32_FWD_DEFINED__
#define ____FIIterator_1_RectInt32_FWD_DEFINED__
typedef interface __FIIterator_1_RectInt32 __FIIterator_1_RectInt32;
#ifdef __cplusplus
#define __FIIterator_1_RectInt32 ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Graphics::RectInt32 >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_RectInt32_FWD_DEFINED__
#define ____FIVectorView_1_RectInt32_FWD_DEFINED__
typedef interface __FIVectorView_1_RectInt32 __FIVectorView_1_RectInt32;
#ifdef __cplusplus
#define __FIVectorView_1_RectInt32 ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Graphics::RectInt32 >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_SizeInt32_FWD_DEFINED__
#define ____FIReference_1_SizeInt32_FWD_DEFINED__
typedef interface __FIReference_1_SizeInt32 __FIReference_1_SizeInt32;
#ifdef __cplusplus
#define __FIReference_1_SizeInt32 ABI::Windows::Foundation::IReference<ABI::Windows::Graphics::SizeInt32 >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGraphics_CDisplayAdapterId __x_ABI_CWindows_CGraphics_CDisplayAdapterId;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            typedef struct DisplayAdapterId DisplayAdapterId;
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGraphics_CDisplayId __x_ABI_CWindows_CGraphics_CDisplayId;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            typedef struct DisplayId DisplayId;
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGraphics_CPointInt32 __x_ABI_CWindows_CGraphics_CPointInt32;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            typedef struct PointInt32 PointInt32;
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGraphics_CRectInt32 __x_ABI_CWindows_CGraphics_CRectInt32;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            typedef struct RectInt32 RectInt32;
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CGraphics_CSizeInt32 __x_ABI_CWindows_CGraphics_CSizeInt32;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            typedef struct SizeInt32 SizeInt32;
        }
    }
}
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CGraphics_CIGeometrySource2D_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CIGeometrySource2D_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CIGeometrySource2D __x_ABI_CWindows_CGraphics_CIGeometrySource2D;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CIGeometrySource2D ABI::Windows::Graphics::IGeometrySource2D
namespace ABI {
    namespace Windows {
        namespace Graphics {
            interface IGeometrySource2D;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_RectInt32_FWD_DEFINED__
#define ____FIIterable_1_RectInt32_FWD_DEFINED__
typedef interface __FIIterable_1_RectInt32 __FIIterable_1_RectInt32;
#ifdef __cplusplus
#define __FIIterable_1_RectInt32 ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Graphics::RectInt32 >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_RectInt32_FWD_DEFINED__
#define ____FIIterator_1_RectInt32_FWD_DEFINED__
typedef interface __FIIterator_1_RectInt32 __FIIterator_1_RectInt32;
#ifdef __cplusplus
#define __FIIterator_1_RectInt32 ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Graphics::RectInt32 >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_RectInt32_FWD_DEFINED__
#define ____FIVectorView_1_RectInt32_FWD_DEFINED__
typedef interface __FIVectorView_1_RectInt32 __FIVectorView_1_RectInt32;
#ifdef __cplusplus
#define __FIVectorView_1_RectInt32 ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Graphics::RectInt32 >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_SizeInt32_FWD_DEFINED__
#define ____FIReference_1_SizeInt32_FWD_DEFINED__
typedef interface __FIReference_1_SizeInt32 __FIReference_1_SizeInt32;
#ifdef __cplusplus
#define __FIReference_1_SizeInt32 ABI::Windows::Foundation::IReference<ABI::Windows::Graphics::SizeInt32 >
#endif /* __cplusplus */
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            struct DisplayAdapterId {
                UINT32 LowPart;
                INT32 HighPart;
            };
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGraphics_CDisplayAdapterId {
    UINT32 LowPart;
    INT32 HighPart;
};
#ifdef WIDL_using_Windows_Graphics
#define DisplayAdapterId __x_ABI_CWindows_CGraphics_CDisplayAdapterId
#endif /* WIDL_using_Windows_Graphics */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            struct DisplayId {
                UINT64 Value;
            };
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGraphics_CDisplayId {
    UINT64 Value;
};
#ifdef WIDL_using_Windows_Graphics
#define DisplayId __x_ABI_CWindows_CGraphics_CDisplayId
#endif /* WIDL_using_Windows_Graphics */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            struct PointInt32 {
                INT32 X;
                INT32 Y;
            };
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGraphics_CPointInt32 {
    INT32 X;
    INT32 Y;
};
#ifdef WIDL_using_Windows_Graphics
#define PointInt32 __x_ABI_CWindows_CGraphics_CPointInt32
#endif /* WIDL_using_Windows_Graphics */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            struct RectInt32 {
                INT32 X;
                INT32 Y;
                INT32 Width;
                INT32 Height;
            };
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGraphics_CRectInt32 {
    INT32 X;
    INT32 Y;
    INT32 Width;
    INT32 Height;
};
#ifdef WIDL_using_Windows_Graphics
#define RectInt32 __x_ABI_CWindows_CGraphics_CRectInt32
#endif /* WIDL_using_Windows_Graphics */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            struct SizeInt32 {
                INT32 Width;
                INT32 Height;
            };
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CGraphics_CSizeInt32 {
    INT32 Width;
    INT32 Height;
};
#ifdef WIDL_using_Windows_Graphics
#define SizeInt32 __x_ABI_CWindows_CGraphics_CSizeInt32
#endif /* WIDL_using_Windows_Graphics */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
/*****************************************************************************
 * IGeometrySource2D interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CGraphics_CIGeometrySource2D_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CIGeometrySource2D_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CIGeometrySource2D, 0xcaff7902, 0x670c, 0x4181, 0xa6,0x24, 0xda,0x97,0x72,0x03,0xb8,0x45);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            MIDL_INTERFACE("caff7902-670c-4181-a624-da977203b845")
            IGeometrySource2D : public IInspectable
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CIGeometrySource2D, 0xcaff7902, 0x670c, 0x4181, 0xa6,0x24, 0xda,0x97,0x72,0x03,0xb8,0x45)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CIGeometrySource2DVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CIGeometrySource2D *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CIGeometrySource2D *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CIGeometrySource2D *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CIGeometrySource2D *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CIGeometrySource2D *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CIGeometrySource2D *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CIGeometrySource2DVtbl;

interface __x_ABI_CWindows_CGraphics_CIGeometrySource2D {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CIGeometrySource2DVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CIGeometrySource2D_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CIGeometrySource2D_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CIGeometrySource2D_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CIGeometrySource2D_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CIGeometrySource2D_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CIGeometrySource2D_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CIGeometrySource2D_QueryInterface(__x_ABI_CWindows_CGraphics_CIGeometrySource2D* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CIGeometrySource2D_AddRef(__x_ABI_CWindows_CGraphics_CIGeometrySource2D* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CIGeometrySource2D_Release(__x_ABI_CWindows_CGraphics_CIGeometrySource2D* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CIGeometrySource2D_GetIids(__x_ABI_CWindows_CGraphics_CIGeometrySource2D* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CIGeometrySource2D_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CIGeometrySource2D* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CIGeometrySource2D_GetTrustLevel(__x_ABI_CWindows_CGraphics_CIGeometrySource2D* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_Graphics
#define IID_IGeometrySource2D IID___x_ABI_CWindows_CGraphics_CIGeometrySource2D
#define IGeometrySource2DVtbl __x_ABI_CWindows_CGraphics_CIGeometrySource2DVtbl
#define IGeometrySource2D __x_ABI_CWindows_CGraphics_CIGeometrySource2D
#define IGeometrySource2D_QueryInterface __x_ABI_CWindows_CGraphics_CIGeometrySource2D_QueryInterface
#define IGeometrySource2D_AddRef __x_ABI_CWindows_CGraphics_CIGeometrySource2D_AddRef
#define IGeometrySource2D_Release __x_ABI_CWindows_CGraphics_CIGeometrySource2D_Release
#define IGeometrySource2D_GetIids __x_ABI_CWindows_CGraphics_CIGeometrySource2D_GetIids
#define IGeometrySource2D_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CIGeometrySource2D_GetRuntimeClassName
#define IGeometrySource2D_GetTrustLevel __x_ABI_CWindows_CGraphics_CIGeometrySource2D_GetTrustLevel
#endif /* WIDL_using_Windows_Graphics */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CIGeometrySource2D_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Graphics::RectInt32 > interface
 */
#ifndef ____FIIterable_1_RectInt32_INTERFACE_DEFINED__
#define ____FIIterable_1_RectInt32_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_RectInt32, 0xd6222360, 0xb82e, 0x5eed, 0x9e,0xab, 0x2e,0x27,0x5b,0x36,0xe4,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("d6222360-b82e-5eed-9eab-2e275b36e47e")
                IIterable<ABI::Windows::Graphics::RectInt32 > : IIterable_impl<ABI::Windows::Graphics::RectInt32 >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_RectInt32, 0xd6222360, 0xb82e, 0x5eed, 0x9e,0xab, 0x2e,0x27,0x5b,0x36,0xe4,0x7e)
#endif
#else
typedef struct __FIIterable_1_RectInt32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_RectInt32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_RectInt32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_RectInt32 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_RectInt32 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_RectInt32 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_RectInt32 *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Graphics::RectInt32 > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_RectInt32 *This,
        __FIIterator_1_RectInt32 **value);

    END_INTERFACE
} __FIIterable_1_RectInt32Vtbl;

interface __FIIterable_1_RectInt32 {
    CONST_VTBL __FIIterable_1_RectInt32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_RectInt32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_RectInt32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_RectInt32_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_RectInt32_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_RectInt32_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_RectInt32_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Graphics::RectInt32 > methods ***/
#define __FIIterable_1_RectInt32_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_RectInt32_QueryInterface(__FIIterable_1_RectInt32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_RectInt32_AddRef(__FIIterable_1_RectInt32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_RectInt32_Release(__FIIterable_1_RectInt32* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_RectInt32_GetIids(__FIIterable_1_RectInt32* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_RectInt32_GetRuntimeClassName(__FIIterable_1_RectInt32* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_RectInt32_GetTrustLevel(__FIIterable_1_RectInt32* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Graphics::RectInt32 > methods ***/
static inline HRESULT __FIIterable_1_RectInt32_First(__FIIterable_1_RectInt32* This,__FIIterator_1_RectInt32 **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_RectInt32 IID___FIIterable_1_RectInt32
#define IIterable_RectInt32Vtbl __FIIterable_1_RectInt32Vtbl
#define IIterable_RectInt32 __FIIterable_1_RectInt32
#define IIterable_RectInt32_QueryInterface __FIIterable_1_RectInt32_QueryInterface
#define IIterable_RectInt32_AddRef __FIIterable_1_RectInt32_AddRef
#define IIterable_RectInt32_Release __FIIterable_1_RectInt32_Release
#define IIterable_RectInt32_GetIids __FIIterable_1_RectInt32_GetIids
#define IIterable_RectInt32_GetRuntimeClassName __FIIterable_1_RectInt32_GetRuntimeClassName
#define IIterable_RectInt32_GetTrustLevel __FIIterable_1_RectInt32_GetTrustLevel
#define IIterable_RectInt32_First __FIIterable_1_RectInt32_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_RectInt32_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Graphics::RectInt32 > interface
 */
#ifndef ____FIIterator_1_RectInt32_INTERFACE_DEFINED__
#define ____FIIterator_1_RectInt32_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_RectInt32, 0x1abdf3f6, 0x23f1, 0x55ad, 0xba,0xbd, 0xf4,0xcd,0x90,0x84,0x06,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("1abdf3f6-23f1-55ad-babd-f4cd908406e7")
                IIterator<ABI::Windows::Graphics::RectInt32 > : IIterator_impl<ABI::Windows::Graphics::RectInt32 >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_RectInt32, 0x1abdf3f6, 0x23f1, 0x55ad, 0xba,0xbd, 0xf4,0xcd,0x90,0x84,0x06,0xe7)
#endif
#else
typedef struct __FIIterator_1_RectInt32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_RectInt32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_RectInt32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_RectInt32 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_RectInt32 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_RectInt32 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_RectInt32 *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Graphics::RectInt32 > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_RectInt32 *This,
        __x_ABI_CWindows_CGraphics_CRectInt32 *value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_RectInt32 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_RectInt32 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_RectInt32 *This,
        UINT32 items_size,
        __x_ABI_CWindows_CGraphics_CRectInt32 *items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_RectInt32Vtbl;

interface __FIIterator_1_RectInt32 {
    CONST_VTBL __FIIterator_1_RectInt32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_RectInt32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_RectInt32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_RectInt32_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_RectInt32_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_RectInt32_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_RectInt32_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Graphics::RectInt32 > methods ***/
#define __FIIterator_1_RectInt32_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_RectInt32_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_RectInt32_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_RectInt32_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_RectInt32_QueryInterface(__FIIterator_1_RectInt32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_RectInt32_AddRef(__FIIterator_1_RectInt32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_RectInt32_Release(__FIIterator_1_RectInt32* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_RectInt32_GetIids(__FIIterator_1_RectInt32* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_RectInt32_GetRuntimeClassName(__FIIterator_1_RectInt32* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_RectInt32_GetTrustLevel(__FIIterator_1_RectInt32* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Graphics::RectInt32 > methods ***/
static inline HRESULT __FIIterator_1_RectInt32_get_Current(__FIIterator_1_RectInt32* This,__x_ABI_CWindows_CGraphics_CRectInt32 *value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_RectInt32_get_HasCurrent(__FIIterator_1_RectInt32* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_RectInt32_MoveNext(__FIIterator_1_RectInt32* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_RectInt32_GetMany(__FIIterator_1_RectInt32* This,UINT32 items_size,__x_ABI_CWindows_CGraphics_CRectInt32 *items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_RectInt32 IID___FIIterator_1_RectInt32
#define IIterator_RectInt32Vtbl __FIIterator_1_RectInt32Vtbl
#define IIterator_RectInt32 __FIIterator_1_RectInt32
#define IIterator_RectInt32_QueryInterface __FIIterator_1_RectInt32_QueryInterface
#define IIterator_RectInt32_AddRef __FIIterator_1_RectInt32_AddRef
#define IIterator_RectInt32_Release __FIIterator_1_RectInt32_Release
#define IIterator_RectInt32_GetIids __FIIterator_1_RectInt32_GetIids
#define IIterator_RectInt32_GetRuntimeClassName __FIIterator_1_RectInt32_GetRuntimeClassName
#define IIterator_RectInt32_GetTrustLevel __FIIterator_1_RectInt32_GetTrustLevel
#define IIterator_RectInt32_get_Current __FIIterator_1_RectInt32_get_Current
#define IIterator_RectInt32_get_HasCurrent __FIIterator_1_RectInt32_get_HasCurrent
#define IIterator_RectInt32_MoveNext __FIIterator_1_RectInt32_MoveNext
#define IIterator_RectInt32_GetMany __FIIterator_1_RectInt32_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_RectInt32_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Graphics::RectInt32 > interface
 */
#ifndef ____FIVectorView_1_RectInt32_INTERFACE_DEFINED__
#define ____FIVectorView_1_RectInt32_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_RectInt32, 0x4f5e243f, 0x3812, 0x5200, 0xb7,0x0c, 0x30,0xdc,0xfc,0x61,0x71,0x7b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("4f5e243f-3812-5200-b70c-30dcfc61717b")
                IVectorView<ABI::Windows::Graphics::RectInt32 > : IVectorView_impl<ABI::Windows::Graphics::RectInt32 >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_RectInt32, 0x4f5e243f, 0x3812, 0x5200, 0xb7,0x0c, 0x30,0xdc,0xfc,0x61,0x71,0x7b)
#endif
#else
typedef struct __FIVectorView_1_RectInt32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_RectInt32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_RectInt32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_RectInt32 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_RectInt32 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_RectInt32 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_RectInt32 *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Graphics::RectInt32 > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_RectInt32 *This,
        UINT32 index,
        __x_ABI_CWindows_CGraphics_CRectInt32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_RectInt32 *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_RectInt32 *This,
        __x_ABI_CWindows_CGraphics_CRectInt32 element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_RectInt32 *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CGraphics_CRectInt32 *items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_RectInt32Vtbl;

interface __FIVectorView_1_RectInt32 {
    CONST_VTBL __FIVectorView_1_RectInt32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_RectInt32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_RectInt32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_RectInt32_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_RectInt32_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_RectInt32_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_RectInt32_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Graphics::RectInt32 > methods ***/
#define __FIVectorView_1_RectInt32_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_RectInt32_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_RectInt32_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_RectInt32_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_RectInt32_QueryInterface(__FIVectorView_1_RectInt32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_RectInt32_AddRef(__FIVectorView_1_RectInt32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_RectInt32_Release(__FIVectorView_1_RectInt32* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_RectInt32_GetIids(__FIVectorView_1_RectInt32* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_RectInt32_GetRuntimeClassName(__FIVectorView_1_RectInt32* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_RectInt32_GetTrustLevel(__FIVectorView_1_RectInt32* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Graphics::RectInt32 > methods ***/
static inline HRESULT __FIVectorView_1_RectInt32_GetAt(__FIVectorView_1_RectInt32* This,UINT32 index,__x_ABI_CWindows_CGraphics_CRectInt32 *value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_RectInt32_get_Size(__FIVectorView_1_RectInt32* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_RectInt32_IndexOf(__FIVectorView_1_RectInt32* This,__x_ABI_CWindows_CGraphics_CRectInt32 element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_RectInt32_GetMany(__FIVectorView_1_RectInt32* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CGraphics_CRectInt32 *items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_RectInt32 IID___FIVectorView_1_RectInt32
#define IVectorView_RectInt32Vtbl __FIVectorView_1_RectInt32Vtbl
#define IVectorView_RectInt32 __FIVectorView_1_RectInt32
#define IVectorView_RectInt32_QueryInterface __FIVectorView_1_RectInt32_QueryInterface
#define IVectorView_RectInt32_AddRef __FIVectorView_1_RectInt32_AddRef
#define IVectorView_RectInt32_Release __FIVectorView_1_RectInt32_Release
#define IVectorView_RectInt32_GetIids __FIVectorView_1_RectInt32_GetIids
#define IVectorView_RectInt32_GetRuntimeClassName __FIVectorView_1_RectInt32_GetRuntimeClassName
#define IVectorView_RectInt32_GetTrustLevel __FIVectorView_1_RectInt32_GetTrustLevel
#define IVectorView_RectInt32_GetAt __FIVectorView_1_RectInt32_GetAt
#define IVectorView_RectInt32_get_Size __FIVectorView_1_RectInt32_get_Size
#define IVectorView_RectInt32_IndexOf __FIVectorView_1_RectInt32_IndexOf
#define IVectorView_RectInt32_GetMany __FIVectorView_1_RectInt32_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_RectInt32_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IReference<ABI::Windows::Graphics::SizeInt32 > interface
 */
#ifndef ____FIReference_1_SizeInt32_INTERFACE_DEFINED__
#define ____FIReference_1_SizeInt32_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIReference_1_SizeInt32, 0xb77aa86d, 0x2d6e, 0x55f1, 0x8f,0x99, 0x64,0xac,0x5c,0x05,0x32,0x8b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("b77aa86d-2d6e-55f1-8f99-64ac5c05328b")
            IReference<ABI::Windows::Graphics::SizeInt32 > : IReference_impl<ABI::Windows::Graphics::SizeInt32 >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIReference_1_SizeInt32, 0xb77aa86d, 0x2d6e, 0x55f1, 0x8f,0x99, 0x64,0xac,0x5c,0x05,0x32,0x8b)
#endif
#else
typedef struct __FIReference_1_SizeInt32Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIReference_1_SizeInt32 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIReference_1_SizeInt32 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIReference_1_SizeInt32 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIReference_1_SizeInt32 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIReference_1_SizeInt32 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIReference_1_SizeInt32 *This,
        TrustLevel *trustLevel);

    /*** IReference<ABI::Windows::Graphics::SizeInt32 > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __FIReference_1_SizeInt32 *This,
        __x_ABI_CWindows_CGraphics_CSizeInt32 *value);

    END_INTERFACE
} __FIReference_1_SizeInt32Vtbl;

interface __FIReference_1_SizeInt32 {
    CONST_VTBL __FIReference_1_SizeInt32Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIReference_1_SizeInt32_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIReference_1_SizeInt32_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIReference_1_SizeInt32_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIReference_1_SizeInt32_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIReference_1_SizeInt32_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIReference_1_SizeInt32_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IReference<ABI::Windows::Graphics::SizeInt32 > methods ***/
#define __FIReference_1_SizeInt32_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIReference_1_SizeInt32_QueryInterface(__FIReference_1_SizeInt32* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIReference_1_SizeInt32_AddRef(__FIReference_1_SizeInt32* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIReference_1_SizeInt32_Release(__FIReference_1_SizeInt32* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIReference_1_SizeInt32_GetIids(__FIReference_1_SizeInt32* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIReference_1_SizeInt32_GetRuntimeClassName(__FIReference_1_SizeInt32* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIReference_1_SizeInt32_GetTrustLevel(__FIReference_1_SizeInt32* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IReference<ABI::Windows::Graphics::SizeInt32 > methods ***/
static inline HRESULT __FIReference_1_SizeInt32_get_Value(__FIReference_1_SizeInt32* This,__x_ABI_CWindows_CGraphics_CSizeInt32 *value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IReference_SizeInt32 IID___FIReference_1_SizeInt32
#define IReference_SizeInt32Vtbl __FIReference_1_SizeInt32Vtbl
#define IReference_SizeInt32 __FIReference_1_SizeInt32
#define IReference_SizeInt32_QueryInterface __FIReference_1_SizeInt32_QueryInterface
#define IReference_SizeInt32_AddRef __FIReference_1_SizeInt32_AddRef
#define IReference_SizeInt32_Release __FIReference_1_SizeInt32_Release
#define IReference_SizeInt32_GetIids __FIReference_1_SizeInt32_GetIids
#define IReference_SizeInt32_GetRuntimeClassName __FIReference_1_SizeInt32_GetRuntimeClassName
#define IReference_SizeInt32_GetTrustLevel __FIReference_1_SizeInt32_GetTrustLevel
#define IReference_SizeInt32_get_Value __FIReference_1_SizeInt32_get_Value
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIReference_1_SizeInt32_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_graphics_h__ */
