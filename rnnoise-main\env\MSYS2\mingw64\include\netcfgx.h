/*** Autogenerated by WIDL 10.12 from include/netcfgx.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __netcfgx_h__
#define __netcfgx_h__

/* Forward declarations */

#ifndef __IEnumNetCfgBindingInterface_FWD_DEFINED__
#define __IEnumNetCfgBindingInterface_FWD_DEFINED__
typedef interface IEnumNetCfgBindingInterface IEnumNetCfgBindingInterface;
#ifdef __cplusplus
interface IEnumNetCfgBindingInterface;
#endif /* __cplusplus */
#endif

#ifndef __IEnumNetCfgBindingPath_FWD_DEFINED__
#define __IEnumNetCfgBindingPath_FWD_DEFINED__
typedef interface IEnumNetCfgBindingPath IEnumNetCfgBindingPath;
#ifdef __cplusplus
interface IEnumNetCfgBindingPath;
#endif /* __cplusplus */
#endif

#ifndef __IEnumNetCfgComponent_FWD_DEFINED__
#define __IEnumNetCfgComponent_FWD_DEFINED__
typedef interface IEnumNetCfgComponent IEnumNetCfgComponent;
#ifdef __cplusplus
interface IEnumNetCfgComponent;
#endif /* __cplusplus */
#endif

#ifndef __INetCfg_FWD_DEFINED__
#define __INetCfg_FWD_DEFINED__
typedef interface INetCfg INetCfg;
#ifdef __cplusplus
interface INetCfg;
#endif /* __cplusplus */
#endif

#ifndef __CNetCfg_FWD_DEFINED__
#define __CNetCfg_FWD_DEFINED__
#ifdef __cplusplus
typedef class CNetCfg CNetCfg;
#else
typedef struct CNetCfg CNetCfg;
#endif /* defined __cplusplus */
#endif /* defined __CNetCfg_FWD_DEFINED__ */

#ifndef __INetCfgLock_FWD_DEFINED__
#define __INetCfgLock_FWD_DEFINED__
typedef interface INetCfgLock INetCfgLock;
#ifdef __cplusplus
interface INetCfgLock;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgBindingInterface_FWD_DEFINED__
#define __INetCfgBindingInterface_FWD_DEFINED__
typedef interface INetCfgBindingInterface INetCfgBindingInterface;
#ifdef __cplusplus
interface INetCfgBindingInterface;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgBindingPath_FWD_DEFINED__
#define __INetCfgBindingPath_FWD_DEFINED__
typedef interface INetCfgBindingPath INetCfgBindingPath;
#ifdef __cplusplus
interface INetCfgBindingPath;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgClass_FWD_DEFINED__
#define __INetCfgClass_FWD_DEFINED__
typedef interface INetCfgClass INetCfgClass;
#ifdef __cplusplus
interface INetCfgClass;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgClassSetup_FWD_DEFINED__
#define __INetCfgClassSetup_FWD_DEFINED__
typedef interface INetCfgClassSetup INetCfgClassSetup;
#ifdef __cplusplus
interface INetCfgClassSetup;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgClassSetup2_FWD_DEFINED__
#define __INetCfgClassSetup2_FWD_DEFINED__
typedef interface INetCfgClassSetup2 INetCfgClassSetup2;
#ifdef __cplusplus
interface INetCfgClassSetup2;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgComponent_FWD_DEFINED__
#define __INetCfgComponent_FWD_DEFINED__
typedef interface INetCfgComponent INetCfgComponent;
#ifdef __cplusplus
interface INetCfgComponent;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgComponentBindings_FWD_DEFINED__
#define __INetCfgComponentBindings_FWD_DEFINED__
typedef interface INetCfgComponentBindings INetCfgComponentBindings;
#ifdef __cplusplus
interface INetCfgComponentBindings;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgSysPrep_FWD_DEFINED__
#define __INetCfgSysPrep_FWD_DEFINED__
typedef interface INetCfgSysPrep INetCfgSysPrep;
#ifdef __cplusplus
interface INetCfgSysPrep;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>
#include <wtypes.h>
#include <prsht.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

#define NETCFG_E_ALREADY_INITIALIZED MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa020)
#define NETCFG_E_NOT_INITIALIZED MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa021)
#define NETCFG_E_IN_USE MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa022)
#define NETCFG_E_NO_WRITE_LOCK MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa024)
#define NETCFG_E_NEED_REBOOT MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa025)
#define NETCFG_E_ACTIVE_RAS_CONNECTIONS MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa026)
#define NETCFG_E_ADAPTER_NOT_FOUND MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa027)
#define NETCFG_E_COMPONENT_REMOVED_PENDING_REBOOT MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa028)
#define NETCFG_E_MAX_FILTER_LIMIT MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa029)
#define NETCFG_E_VMSWITCH_ACTIVE_OVER_ADAPTER MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa02a)
#define NETCFG_E_DUPLICATE_INSTANCEID MAKE_HRESULT(SEVERITY_ERROR, FACILITY_ITF, 0xa02b)

#define NETCFG_S_REBOOT MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0xa020)
#define NETCFG_S_DISABLE_QUERY MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0xa022)
#define NETCFG_S_STILL_REFERENCED MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0xa023)
#define NETCFG_S_CAUSED_SETUP_CHANGE MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0xa024)
#define NETCFG_S_COMMIT_NOW MAKE_HRESULT(SEVERITY_SUCCESS, FACILITY_ITF, 0xa025)

#define NETCFG_CLIENT_CID_MS_MSClient TEXT("ms_msclient")
#define NETCFG_SERVICE_CID_MS_SERVER TEXT("ms_server")
#define NETCFG_SERVICE_CID_MS_NETBIOS TEXT("ms_netbios")
#define NETCFG_SERVICE_CID_MS_PSCHED TEXT("ms_pschedpc")
#define NETCFG_SERVICE_CID_MS_WLBS TEXT("ms_wlbs")
#define NETCFG_TRANS_CID_MS_APPLETALK TEXT("ms_appletalk")
#define NETCFG_TRANS_CID_MS_NETBEUI TEXT("ms_netbeui")
#define NETCFG_TRANS_CID_MS_NETMON TEXT("ms_netmon")
#define NETCFG_TRANS_CID_MS_NWIPX TEXT("ms_nwipx")
#define NETCFG_TRANS_CID_MS_NWSPX TEXT("ms_nwspx")
#define NETCFG_TRANS_CID_MS_TCPIP TEXT("ms_tcpip")

#ifndef __IEnumNetCfgBindingInterface_FWD_DEFINED__
#define __IEnumNetCfgBindingInterface_FWD_DEFINED__
typedef interface IEnumNetCfgBindingInterface IEnumNetCfgBindingInterface;
#ifdef __cplusplus
interface IEnumNetCfgBindingInterface;
#endif /* __cplusplus */
#endif

#ifndef __IEnumNetCfgBindingPath_FWD_DEFINED__
#define __IEnumNetCfgBindingPath_FWD_DEFINED__
typedef interface IEnumNetCfgBindingPath IEnumNetCfgBindingPath;
#ifdef __cplusplus
interface IEnumNetCfgBindingPath;
#endif /* __cplusplus */
#endif

#ifndef __IEnumNetCfgComponent_FWD_DEFINED__
#define __IEnumNetCfgComponent_FWD_DEFINED__
typedef interface IEnumNetCfgComponent IEnumNetCfgComponent;
#ifdef __cplusplus
interface IEnumNetCfgComponent;
#endif /* __cplusplus */
#endif

#ifndef __INetCfg_FWD_DEFINED__
#define __INetCfg_FWD_DEFINED__
typedef interface INetCfg INetCfg;
#ifdef __cplusplus
interface INetCfg;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgProperties_FWD_DEFINED__
#define __INetCfgProperties_FWD_DEFINED__
typedef interface INetCfgProperties INetCfgProperties;
#ifdef __cplusplus
interface INetCfgProperties;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgLock_FWD_DEFINED__
#define __INetCfgLock_FWD_DEFINED__
typedef interface INetCfgLock INetCfgLock;
#ifdef __cplusplus
interface INetCfgLock;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgBindingInterface_FWD_DEFINED__
#define __INetCfgBindingInterface_FWD_DEFINED__
typedef interface INetCfgBindingInterface INetCfgBindingInterface;
#ifdef __cplusplus
interface INetCfgBindingInterface;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgBindingPath_FWD_DEFINED__
#define __INetCfgBindingPath_FWD_DEFINED__
typedef interface INetCfgBindingPath INetCfgBindingPath;
#ifdef __cplusplus
interface INetCfgBindingPath;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgComponentBindings_FWD_DEFINED__
#define __INetCfgComponentBindings_FWD_DEFINED__
typedef interface INetCfgComponentBindings INetCfgComponentBindings;
#ifdef __cplusplus
interface INetCfgComponentBindings;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgBindingPath_FWD_DEFINED__
#define __INetCfgBindingPath_FWD_DEFINED__
typedef interface INetCfgBindingPath INetCfgBindingPath;
#ifdef __cplusplus
interface INetCfgBindingPath;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgClass_FWD_DEFINED__
#define __INetCfgClass_FWD_DEFINED__
typedef interface INetCfgClass INetCfgClass;
#ifdef __cplusplus
interface INetCfgClass;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgComponent_FWD_DEFINED__
#define __INetCfgComponent_FWD_DEFINED__
typedef interface INetCfgComponent INetCfgComponent;
#ifdef __cplusplus
interface INetCfgComponent;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgIdentification_FWD_DEFINED__
#define __INetCfgIdentification_FWD_DEFINED__
typedef interface INetCfgIdentification INetCfgIdentification;
#ifdef __cplusplus
interface INetCfgIdentification;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgClassSetup_FWD_DEFINED__
#define __INetCfgClassSetup_FWD_DEFINED__
typedef interface INetCfgClassSetup INetCfgClassSetup;
#ifdef __cplusplus
interface INetCfgClassSetup;
#endif /* __cplusplus */
#endif

#ifndef __INetCfgClassSetup2_FWD_DEFINED__
#define __INetCfgClassSetup2_FWD_DEFINED__
typedef interface INetCfgClassSetup2 INetCfgClassSetup2;
#ifdef __cplusplus
interface INetCfgClassSetup2;
#endif /* __cplusplus */
#endif


/*****************************************************************************
 * IEnumNetCfgBindingInterface interface
 */
#ifndef __IEnumNetCfgBindingInterface_INTERFACE_DEFINED__
#define __IEnumNetCfgBindingInterface_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumNetCfgBindingInterface, 0xc0e8ae90, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae90-306e-11d1-aacf-00805fc1270e")
IEnumNetCfgBindingInterface : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        INetCfgBindingInterface **rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumNetCfgBindingInterface **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumNetCfgBindingInterface, 0xc0e8ae90, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct IEnumNetCfgBindingInterfaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumNetCfgBindingInterface *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumNetCfgBindingInterface *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumNetCfgBindingInterface *This);

    /*** IEnumNetCfgBindingInterface methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumNetCfgBindingInterface *This,
        ULONG celt,
        INetCfgBindingInterface **rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumNetCfgBindingInterface *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumNetCfgBindingInterface *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumNetCfgBindingInterface *This,
        IEnumNetCfgBindingInterface **ppenum);

    END_INTERFACE
} IEnumNetCfgBindingInterfaceVtbl;

interface IEnumNetCfgBindingInterface {
    CONST_VTBL IEnumNetCfgBindingInterfaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumNetCfgBindingInterface_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumNetCfgBindingInterface_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumNetCfgBindingInterface_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumNetCfgBindingInterface methods ***/
#define IEnumNetCfgBindingInterface_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumNetCfgBindingInterface_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumNetCfgBindingInterface_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumNetCfgBindingInterface_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumNetCfgBindingInterface_QueryInterface(IEnumNetCfgBindingInterface* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumNetCfgBindingInterface_AddRef(IEnumNetCfgBindingInterface* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumNetCfgBindingInterface_Release(IEnumNetCfgBindingInterface* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumNetCfgBindingInterface methods ***/
static inline HRESULT IEnumNetCfgBindingInterface_Next(IEnumNetCfgBindingInterface* This,ULONG celt,INetCfgBindingInterface **rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumNetCfgBindingInterface_Skip(IEnumNetCfgBindingInterface* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumNetCfgBindingInterface_Reset(IEnumNetCfgBindingInterface* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumNetCfgBindingInterface_Clone(IEnumNetCfgBindingInterface* This,IEnumNetCfgBindingInterface **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif


#endif  /* __IEnumNetCfgBindingInterface_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEnumNetCfgBindingPath interface
 */
#ifndef __IEnumNetCfgBindingPath_INTERFACE_DEFINED__
#define __IEnumNetCfgBindingPath_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumNetCfgBindingPath, 0xc0e8ae91, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae91-306e-11d1-aacf-00805fc1270e")
IEnumNetCfgBindingPath : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        INetCfgBindingPath **rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumNetCfgBindingPath **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumNetCfgBindingPath, 0xc0e8ae91, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct IEnumNetCfgBindingPathVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumNetCfgBindingPath *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumNetCfgBindingPath *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumNetCfgBindingPath *This);

    /*** IEnumNetCfgBindingPath methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumNetCfgBindingPath *This,
        ULONG celt,
        INetCfgBindingPath **rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumNetCfgBindingPath *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumNetCfgBindingPath *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumNetCfgBindingPath *This,
        IEnumNetCfgBindingPath **ppenum);

    END_INTERFACE
} IEnumNetCfgBindingPathVtbl;

interface IEnumNetCfgBindingPath {
    CONST_VTBL IEnumNetCfgBindingPathVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumNetCfgBindingPath_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumNetCfgBindingPath_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumNetCfgBindingPath_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumNetCfgBindingPath methods ***/
#define IEnumNetCfgBindingPath_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumNetCfgBindingPath_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumNetCfgBindingPath_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumNetCfgBindingPath_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumNetCfgBindingPath_QueryInterface(IEnumNetCfgBindingPath* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumNetCfgBindingPath_AddRef(IEnumNetCfgBindingPath* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumNetCfgBindingPath_Release(IEnumNetCfgBindingPath* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumNetCfgBindingPath methods ***/
static inline HRESULT IEnumNetCfgBindingPath_Next(IEnumNetCfgBindingPath* This,ULONG celt,INetCfgBindingPath **rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumNetCfgBindingPath_Skip(IEnumNetCfgBindingPath* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumNetCfgBindingPath_Reset(IEnumNetCfgBindingPath* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumNetCfgBindingPath_Clone(IEnumNetCfgBindingPath* This,IEnumNetCfgBindingPath **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif


#endif  /* __IEnumNetCfgBindingPath_INTERFACE_DEFINED__ */


/*****************************************************************************
 * IEnumNetCfgComponent interface
 */
#ifndef __IEnumNetCfgComponent_INTERFACE_DEFINED__
#define __IEnumNetCfgComponent_INTERFACE_DEFINED__

DEFINE_GUID(IID_IEnumNetCfgComponent, 0xc0e8ae92, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae92-306e-11d1-aacf-00805fc1270e")
IEnumNetCfgComponent : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Next(
        ULONG celt,
        INetCfgComponent **rgelt,
        ULONG *pceltFetched) = 0;

    virtual HRESULT STDMETHODCALLTYPE Skip(
        ULONG celt) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clone(
        IEnumNetCfgComponent **ppenum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IEnumNetCfgComponent, 0xc0e8ae92, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct IEnumNetCfgComponentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IEnumNetCfgComponent *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IEnumNetCfgComponent *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IEnumNetCfgComponent *This);

    /*** IEnumNetCfgComponent methods ***/
    HRESULT (STDMETHODCALLTYPE *Next)(
        IEnumNetCfgComponent *This,
        ULONG celt,
        INetCfgComponent **rgelt,
        ULONG *pceltFetched);

    HRESULT (STDMETHODCALLTYPE *Skip)(
        IEnumNetCfgComponent *This,
        ULONG celt);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        IEnumNetCfgComponent *This);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IEnumNetCfgComponent *This,
        IEnumNetCfgComponent **ppenum);

    END_INTERFACE
} IEnumNetCfgComponentVtbl;

interface IEnumNetCfgComponent {
    CONST_VTBL IEnumNetCfgComponentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IEnumNetCfgComponent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IEnumNetCfgComponent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IEnumNetCfgComponent_Release(This) (This)->lpVtbl->Release(This)
/*** IEnumNetCfgComponent methods ***/
#define IEnumNetCfgComponent_Next(This,celt,rgelt,pceltFetched) (This)->lpVtbl->Next(This,celt,rgelt,pceltFetched)
#define IEnumNetCfgComponent_Skip(This,celt) (This)->lpVtbl->Skip(This,celt)
#define IEnumNetCfgComponent_Reset(This) (This)->lpVtbl->Reset(This)
#define IEnumNetCfgComponent_Clone(This,ppenum) (This)->lpVtbl->Clone(This,ppenum)
#else
/*** IUnknown methods ***/
static inline HRESULT IEnumNetCfgComponent_QueryInterface(IEnumNetCfgComponent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IEnumNetCfgComponent_AddRef(IEnumNetCfgComponent* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IEnumNetCfgComponent_Release(IEnumNetCfgComponent* This) {
    return This->lpVtbl->Release(This);
}
/*** IEnumNetCfgComponent methods ***/
static inline HRESULT IEnumNetCfgComponent_Next(IEnumNetCfgComponent* This,ULONG celt,INetCfgComponent **rgelt,ULONG *pceltFetched) {
    return This->lpVtbl->Next(This,celt,rgelt,pceltFetched);
}
static inline HRESULT IEnumNetCfgComponent_Skip(IEnumNetCfgComponent* This,ULONG celt) {
    return This->lpVtbl->Skip(This,celt);
}
static inline HRESULT IEnumNetCfgComponent_Reset(IEnumNetCfgComponent* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IEnumNetCfgComponent_Clone(IEnumNetCfgComponent* This,IEnumNetCfgComponent **ppenum) {
    return This->lpVtbl->Clone(This,ppenum);
}
#endif
#endif

#endif


#endif  /* __IEnumNetCfgComponent_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfg interface
 */
#ifndef __INetCfg_INTERFACE_DEFINED__
#define __INetCfg_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetCfg, 0xc0e8ae93, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae93-306e-11d1-aacf-00805fc1270e")
INetCfg : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        PVOID pvReserved) = 0;

    virtual HRESULT STDMETHODCALLTYPE Uninitialize(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Apply(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumComponents(
        const GUID *pguidClass,
        IEnumNetCfgComponent **ppenumComponent) = 0;

    virtual HRESULT STDMETHODCALLTYPE FindComponent(
        LPCWSTR pszwInfId,
        INetCfgComponent **pComponent) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryNetCfgClass(
        const GUID *pguidClass,
        REFIID riid,
        void **ppvObject) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfg, 0xc0e8ae93, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetCfgVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfg *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfg *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfg *This);

    /*** INetCfg methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        INetCfg *This,
        PVOID pvReserved);

    HRESULT (STDMETHODCALLTYPE *Uninitialize)(
        INetCfg *This);

    HRESULT (STDMETHODCALLTYPE *Apply)(
        INetCfg *This);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        INetCfg *This);

    HRESULT (STDMETHODCALLTYPE *EnumComponents)(
        INetCfg *This,
        const GUID *pguidClass,
        IEnumNetCfgComponent **ppenumComponent);

    HRESULT (STDMETHODCALLTYPE *FindComponent)(
        INetCfg *This,
        LPCWSTR pszwInfId,
        INetCfgComponent **pComponent);

    HRESULT (STDMETHODCALLTYPE *QueryNetCfgClass)(
        INetCfg *This,
        const GUID *pguidClass,
        REFIID riid,
        void **ppvObject);

    END_INTERFACE
} INetCfgVtbl;

interface INetCfg {
    CONST_VTBL INetCfgVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfg_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfg_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfg_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfg methods ***/
#define INetCfg_Initialize(This,pvReserved) (This)->lpVtbl->Initialize(This,pvReserved)
#define INetCfg_Uninitialize(This) (This)->lpVtbl->Uninitialize(This)
#define INetCfg_Apply(This) (This)->lpVtbl->Apply(This)
#define INetCfg_Cancel(This) (This)->lpVtbl->Cancel(This)
#define INetCfg_EnumComponents(This,pguidClass,ppenumComponent) (This)->lpVtbl->EnumComponents(This,pguidClass,ppenumComponent)
#define INetCfg_FindComponent(This,pszwInfId,pComponent) (This)->lpVtbl->FindComponent(This,pszwInfId,pComponent)
#define INetCfg_QueryNetCfgClass(This,pguidClass,riid,ppvObject) (This)->lpVtbl->QueryNetCfgClass(This,pguidClass,riid,ppvObject)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfg_QueryInterface(INetCfg* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfg_AddRef(INetCfg* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfg_Release(INetCfg* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfg methods ***/
static inline HRESULT INetCfg_Initialize(INetCfg* This,PVOID pvReserved) {
    return This->lpVtbl->Initialize(This,pvReserved);
}
static inline HRESULT INetCfg_Uninitialize(INetCfg* This) {
    return This->lpVtbl->Uninitialize(This);
}
static inline HRESULT INetCfg_Apply(INetCfg* This) {
    return This->lpVtbl->Apply(This);
}
static inline HRESULT INetCfg_Cancel(INetCfg* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT INetCfg_EnumComponents(INetCfg* This,const GUID *pguidClass,IEnumNetCfgComponent **ppenumComponent) {
    return This->lpVtbl->EnumComponents(This,pguidClass,ppenumComponent);
}
static inline HRESULT INetCfg_FindComponent(INetCfg* This,LPCWSTR pszwInfId,INetCfgComponent **pComponent) {
    return This->lpVtbl->FindComponent(This,pszwInfId,pComponent);
}
static inline HRESULT INetCfg_QueryNetCfgClass(INetCfg* This,const GUID *pguidClass,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryNetCfgClass(This,pguidClass,riid,ppvObject);
}
#endif
#endif

#endif


#endif  /* __INetCfg_INTERFACE_DEFINED__ */


/*****************************************************************************
 * CNetCfg coclass
 */

DEFINE_GUID(CLSID_CNetCfg, 0x5b035261, 0x40f9, 0x11d1, 0xaa,0xec, 0x00,0x80,0x5f,0xc1,0x27,0x0e);

#ifdef __cplusplus
class DECLSPEC_UUID("5b035261-40f9-11d1-aaec-00805fc1270e") CNetCfg;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CNetCfg, 0x5b035261, 0x40f9, 0x11d1, 0xaa,0xec, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#endif


/*****************************************************************************
 * INetCfgLock interface
 */
#ifndef __INetCfgLock_INTERFACE_DEFINED__
#define __INetCfgLock_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetCfgLock, 0xc0e8ae9f, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae9f-306e-11d1-aacf-00805fc1270e")
INetCfgLock : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AcquireWriteLock(
        DWORD cmsTimeout,
        LPCWSTR pszwClientDescription,
        LPWSTR *ppszwClientDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseWriteLock(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsWriteLocked(
        LPWSTR *ppszwClientDescription) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgLock, 0xc0e8ae9f, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetCfgLockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgLock *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgLock *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgLock *This);

    /*** INetCfgLock methods ***/
    HRESULT (STDMETHODCALLTYPE *AcquireWriteLock)(
        INetCfgLock *This,
        DWORD cmsTimeout,
        LPCWSTR pszwClientDescription,
        LPWSTR *ppszwClientDescription);

    HRESULT (STDMETHODCALLTYPE *ReleaseWriteLock)(
        INetCfgLock *This);

    HRESULT (STDMETHODCALLTYPE *IsWriteLocked)(
        INetCfgLock *This,
        LPWSTR *ppszwClientDescription);

    END_INTERFACE
} INetCfgLockVtbl;

interface INetCfgLock {
    CONST_VTBL INetCfgLockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgLock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgLock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgLock_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgLock methods ***/
#define INetCfgLock_AcquireWriteLock(This,cmsTimeout,pszwClientDescription,ppszwClientDescription) (This)->lpVtbl->AcquireWriteLock(This,cmsTimeout,pszwClientDescription,ppszwClientDescription)
#define INetCfgLock_ReleaseWriteLock(This) (This)->lpVtbl->ReleaseWriteLock(This)
#define INetCfgLock_IsWriteLocked(This,ppszwClientDescription) (This)->lpVtbl->IsWriteLocked(This,ppszwClientDescription)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgLock_QueryInterface(INetCfgLock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgLock_AddRef(INetCfgLock* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgLock_Release(INetCfgLock* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgLock methods ***/
static inline HRESULT INetCfgLock_AcquireWriteLock(INetCfgLock* This,DWORD cmsTimeout,LPCWSTR pszwClientDescription,LPWSTR *ppszwClientDescription) {
    return This->lpVtbl->AcquireWriteLock(This,cmsTimeout,pszwClientDescription,ppszwClientDescription);
}
static inline HRESULT INetCfgLock_ReleaseWriteLock(INetCfgLock* This) {
    return This->lpVtbl->ReleaseWriteLock(This);
}
static inline HRESULT INetCfgLock_IsWriteLocked(INetCfgLock* This,LPWSTR *ppszwClientDescription) {
    return This->lpVtbl->IsWriteLocked(This,ppszwClientDescription);
}
#endif
#endif

#endif


#endif  /* __INetCfgLock_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgBindingInterface interface
 */
#ifndef __INetCfgBindingInterface_INTERFACE_DEFINED__
#define __INetCfgBindingInterface_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetCfgBindingInterface, 0xc0e8ae94, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae94-306e-11d1-aacf-00805fc1270e")
INetCfgBindingInterface : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetName(
        LPWSTR *ppszwInterfaceName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUpperComponent(
        INetCfgComponent **ppnccItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLowerComponent(
        INetCfgComponent **ppnccItem) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgBindingInterface, 0xc0e8ae94, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetCfgBindingInterfaceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgBindingInterface *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgBindingInterface *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgBindingInterface *This);

    /*** INetCfgBindingInterface methods ***/
    HRESULT (STDMETHODCALLTYPE *GetName)(
        INetCfgBindingInterface *This,
        LPWSTR *ppszwInterfaceName);

    HRESULT (STDMETHODCALLTYPE *GetUpperComponent)(
        INetCfgBindingInterface *This,
        INetCfgComponent **ppnccItem);

    HRESULT (STDMETHODCALLTYPE *GetLowerComponent)(
        INetCfgBindingInterface *This,
        INetCfgComponent **ppnccItem);

    END_INTERFACE
} INetCfgBindingInterfaceVtbl;

interface INetCfgBindingInterface {
    CONST_VTBL INetCfgBindingInterfaceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgBindingInterface_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgBindingInterface_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgBindingInterface_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgBindingInterface methods ***/
#define INetCfgBindingInterface_GetName(This,ppszwInterfaceName) (This)->lpVtbl->GetName(This,ppszwInterfaceName)
#define INetCfgBindingInterface_GetUpperComponent(This,ppnccItem) (This)->lpVtbl->GetUpperComponent(This,ppnccItem)
#define INetCfgBindingInterface_GetLowerComponent(This,ppnccItem) (This)->lpVtbl->GetLowerComponent(This,ppnccItem)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgBindingInterface_QueryInterface(INetCfgBindingInterface* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgBindingInterface_AddRef(INetCfgBindingInterface* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgBindingInterface_Release(INetCfgBindingInterface* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgBindingInterface methods ***/
static inline HRESULT INetCfgBindingInterface_GetName(INetCfgBindingInterface* This,LPWSTR *ppszwInterfaceName) {
    return This->lpVtbl->GetName(This,ppszwInterfaceName);
}
static inline HRESULT INetCfgBindingInterface_GetUpperComponent(INetCfgBindingInterface* This,INetCfgComponent **ppnccItem) {
    return This->lpVtbl->GetUpperComponent(This,ppnccItem);
}
static inline HRESULT INetCfgBindingInterface_GetLowerComponent(INetCfgBindingInterface* This,INetCfgComponent **ppnccItem) {
    return This->lpVtbl->GetLowerComponent(This,ppnccItem);
}
#endif
#endif

#endif


#endif  /* __INetCfgBindingInterface_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgBindingPath interface
 */
#ifndef __INetCfgBindingPath_INTERFACE_DEFINED__
#define __INetCfgBindingPath_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetCfgBindingPath, 0xc0e8ae96, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae96-306e-11d1-aacf-00805fc1270e")
INetCfgBindingPath : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IsSamePathAs(
        INetCfgBindingPath *pPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSubPathOf(
        INetCfgBindingPath *pPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEnabled(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Enable(
        WINBOOL fEnable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPathToken(
        LPWSTR *ppszwPathToken) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOwner(
        INetCfgComponent **ppComponent) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDepth(
        ULONG *pcInterfaces) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumBindingInterfaces(
        IEnumNetCfgBindingInterface **ppenumInterface) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgBindingPath, 0xc0e8ae96, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetCfgBindingPathVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgBindingPath *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgBindingPath *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgBindingPath *This);

    /*** INetCfgBindingPath methods ***/
    HRESULT (STDMETHODCALLTYPE *IsSamePathAs)(
        INetCfgBindingPath *This,
        INetCfgBindingPath *pPath);

    HRESULT (STDMETHODCALLTYPE *IsSubPathOf)(
        INetCfgBindingPath *This,
        INetCfgBindingPath *pPath);

    HRESULT (STDMETHODCALLTYPE *IsEnabled)(
        INetCfgBindingPath *This);

    HRESULT (STDMETHODCALLTYPE *Enable)(
        INetCfgBindingPath *This,
        WINBOOL fEnable);

    HRESULT (STDMETHODCALLTYPE *GetPathToken)(
        INetCfgBindingPath *This,
        LPWSTR *ppszwPathToken);

    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        INetCfgBindingPath *This,
        INetCfgComponent **ppComponent);

    HRESULT (STDMETHODCALLTYPE *GetDepth)(
        INetCfgBindingPath *This,
        ULONG *pcInterfaces);

    HRESULT (STDMETHODCALLTYPE *EnumBindingInterfaces)(
        INetCfgBindingPath *This,
        IEnumNetCfgBindingInterface **ppenumInterface);

    END_INTERFACE
} INetCfgBindingPathVtbl;

interface INetCfgBindingPath {
    CONST_VTBL INetCfgBindingPathVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgBindingPath_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgBindingPath_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgBindingPath_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgBindingPath methods ***/
#define INetCfgBindingPath_IsSamePathAs(This,pPath) (This)->lpVtbl->IsSamePathAs(This,pPath)
#define INetCfgBindingPath_IsSubPathOf(This,pPath) (This)->lpVtbl->IsSubPathOf(This,pPath)
#define INetCfgBindingPath_IsEnabled(This) (This)->lpVtbl->IsEnabled(This)
#define INetCfgBindingPath_Enable(This,fEnable) (This)->lpVtbl->Enable(This,fEnable)
#define INetCfgBindingPath_GetPathToken(This,ppszwPathToken) (This)->lpVtbl->GetPathToken(This,ppszwPathToken)
#define INetCfgBindingPath_GetOwner(This,ppComponent) (This)->lpVtbl->GetOwner(This,ppComponent)
#define INetCfgBindingPath_GetDepth(This,pcInterfaces) (This)->lpVtbl->GetDepth(This,pcInterfaces)
#define INetCfgBindingPath_EnumBindingInterfaces(This,ppenumInterface) (This)->lpVtbl->EnumBindingInterfaces(This,ppenumInterface)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgBindingPath_QueryInterface(INetCfgBindingPath* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgBindingPath_AddRef(INetCfgBindingPath* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgBindingPath_Release(INetCfgBindingPath* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgBindingPath methods ***/
static inline HRESULT INetCfgBindingPath_IsSamePathAs(INetCfgBindingPath* This,INetCfgBindingPath *pPath) {
    return This->lpVtbl->IsSamePathAs(This,pPath);
}
static inline HRESULT INetCfgBindingPath_IsSubPathOf(INetCfgBindingPath* This,INetCfgBindingPath *pPath) {
    return This->lpVtbl->IsSubPathOf(This,pPath);
}
static inline HRESULT INetCfgBindingPath_IsEnabled(INetCfgBindingPath* This) {
    return This->lpVtbl->IsEnabled(This);
}
static inline HRESULT INetCfgBindingPath_Enable(INetCfgBindingPath* This,WINBOOL fEnable) {
    return This->lpVtbl->Enable(This,fEnable);
}
static inline HRESULT INetCfgBindingPath_GetPathToken(INetCfgBindingPath* This,LPWSTR *ppszwPathToken) {
    return This->lpVtbl->GetPathToken(This,ppszwPathToken);
}
static inline HRESULT INetCfgBindingPath_GetOwner(INetCfgBindingPath* This,INetCfgComponent **ppComponent) {
    return This->lpVtbl->GetOwner(This,ppComponent);
}
static inline HRESULT INetCfgBindingPath_GetDepth(INetCfgBindingPath* This,ULONG *pcInterfaces) {
    return This->lpVtbl->GetDepth(This,pcInterfaces);
}
static inline HRESULT INetCfgBindingPath_EnumBindingInterfaces(INetCfgBindingPath* This,IEnumNetCfgBindingInterface **ppenumInterface) {
    return This->lpVtbl->EnumBindingInterfaces(This,ppenumInterface);
}
#endif
#endif

#endif


#endif  /* __INetCfgBindingPath_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgClass interface
 */
#ifndef __INetCfgClass_INTERFACE_DEFINED__
#define __INetCfgClass_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetCfgClass, 0xc0e8ae97, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae97-306e-11d1-aacf-00805fc1270e")
INetCfgClass : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE FindComponent(
        LPCWSTR pszwInfId,
        INetCfgComponent **ppnccItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumComponents(
        IEnumNetCfgComponent **ppenumComponent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgClass, 0xc0e8ae97, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetCfgClassVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgClass *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgClass *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgClass *This);

    /*** INetCfgClass methods ***/
    HRESULT (STDMETHODCALLTYPE *FindComponent)(
        INetCfgClass *This,
        LPCWSTR pszwInfId,
        INetCfgComponent **ppnccItem);

    HRESULT (STDMETHODCALLTYPE *EnumComponents)(
        INetCfgClass *This,
        IEnumNetCfgComponent **ppenumComponent);

    END_INTERFACE
} INetCfgClassVtbl;

interface INetCfgClass {
    CONST_VTBL INetCfgClassVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgClass_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgClass_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgClass_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgClass methods ***/
#define INetCfgClass_FindComponent(This,pszwInfId,ppnccItem) (This)->lpVtbl->FindComponent(This,pszwInfId,ppnccItem)
#define INetCfgClass_EnumComponents(This,ppenumComponent) (This)->lpVtbl->EnumComponents(This,ppenumComponent)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgClass_QueryInterface(INetCfgClass* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgClass_AddRef(INetCfgClass* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgClass_Release(INetCfgClass* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgClass methods ***/
static inline HRESULT INetCfgClass_FindComponent(INetCfgClass* This,LPCWSTR pszwInfId,INetCfgComponent **ppnccItem) {
    return This->lpVtbl->FindComponent(This,pszwInfId,ppnccItem);
}
static inline HRESULT INetCfgClass_EnumComponents(INetCfgClass* This,IEnumNetCfgComponent **ppenumComponent) {
    return This->lpVtbl->EnumComponents(This,ppenumComponent);
}
#endif
#endif

#endif


#endif  /* __INetCfgClass_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgClassSetup interface
 */
#ifndef __INetCfgClassSetup_INTERFACE_DEFINED__
#define __INetCfgClassSetup_INTERFACE_DEFINED__

typedef enum tagOBO_TOKEN_TYPE {
    OBO_USER = 1,
    OBO_COMPONENT = 2,
    OBO_SOFTWARE = 3
} OBO_TOKEN_TYPE;
typedef struct tagOBO_TOKEN {
    OBO_TOKEN_TYPE Type;
    INetCfgComponent *pncc;
    LPCWSTR pszwManufacturer;
    LPCWSTR pszwProduct;
    LPCWSTR pszwDisplayName;
    WINBOOL fRegistered;
} OBO_TOKEN;
DEFINE_GUID(IID_INetCfgClassSetup, 0xc0e8ae9d, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae9d-306e-11d1-aacf-00805fc1270e")
INetCfgClassSetup : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SelectAndInstall(
        HWND hwndParent,
        OBO_TOKEN *pOboToken,
        INetCfgComponent **ppnccItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE Install(
        LPCWSTR pszwInfId,
        OBO_TOKEN *pOboToken,
        DWORD dwSetupFlags,
        DWORD dwUpgradeFromBuildNo,
        LPCWSTR pszwAnswerFile,
        LPCWSTR pszwAnswerSections,
        INetCfgComponent **ppnccItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeInstall(
        INetCfgComponent *pComponent,
        OBO_TOKEN *pOboToken,
        LPWSTR *pmszwRefs) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgClassSetup, 0xc0e8ae9d, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetCfgClassSetupVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgClassSetup *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgClassSetup *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgClassSetup *This);

    /*** INetCfgClassSetup methods ***/
    HRESULT (STDMETHODCALLTYPE *SelectAndInstall)(
        INetCfgClassSetup *This,
        HWND hwndParent,
        OBO_TOKEN *pOboToken,
        INetCfgComponent **ppnccItem);

    HRESULT (STDMETHODCALLTYPE *Install)(
        INetCfgClassSetup *This,
        LPCWSTR pszwInfId,
        OBO_TOKEN *pOboToken,
        DWORD dwSetupFlags,
        DWORD dwUpgradeFromBuildNo,
        LPCWSTR pszwAnswerFile,
        LPCWSTR pszwAnswerSections,
        INetCfgComponent **ppnccItem);

    HRESULT (STDMETHODCALLTYPE *DeInstall)(
        INetCfgClassSetup *This,
        INetCfgComponent *pComponent,
        OBO_TOKEN *pOboToken,
        LPWSTR *pmszwRefs);

    END_INTERFACE
} INetCfgClassSetupVtbl;

interface INetCfgClassSetup {
    CONST_VTBL INetCfgClassSetupVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgClassSetup_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgClassSetup_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgClassSetup_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgClassSetup methods ***/
#define INetCfgClassSetup_SelectAndInstall(This,hwndParent,pOboToken,ppnccItem) (This)->lpVtbl->SelectAndInstall(This,hwndParent,pOboToken,ppnccItem)
#define INetCfgClassSetup_Install(This,pszwInfId,pOboToken,dwSetupFlags,dwUpgradeFromBuildNo,pszwAnswerFile,pszwAnswerSections,ppnccItem) (This)->lpVtbl->Install(This,pszwInfId,pOboToken,dwSetupFlags,dwUpgradeFromBuildNo,pszwAnswerFile,pszwAnswerSections,ppnccItem)
#define INetCfgClassSetup_DeInstall(This,pComponent,pOboToken,pmszwRefs) (This)->lpVtbl->DeInstall(This,pComponent,pOboToken,pmszwRefs)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgClassSetup_QueryInterface(INetCfgClassSetup* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgClassSetup_AddRef(INetCfgClassSetup* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgClassSetup_Release(INetCfgClassSetup* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgClassSetup methods ***/
static inline HRESULT INetCfgClassSetup_SelectAndInstall(INetCfgClassSetup* This,HWND hwndParent,OBO_TOKEN *pOboToken,INetCfgComponent **ppnccItem) {
    return This->lpVtbl->SelectAndInstall(This,hwndParent,pOboToken,ppnccItem);
}
static inline HRESULT INetCfgClassSetup_Install(INetCfgClassSetup* This,LPCWSTR pszwInfId,OBO_TOKEN *pOboToken,DWORD dwSetupFlags,DWORD dwUpgradeFromBuildNo,LPCWSTR pszwAnswerFile,LPCWSTR pszwAnswerSections,INetCfgComponent **ppnccItem) {
    return This->lpVtbl->Install(This,pszwInfId,pOboToken,dwSetupFlags,dwUpgradeFromBuildNo,pszwAnswerFile,pszwAnswerSections,ppnccItem);
}
static inline HRESULT INetCfgClassSetup_DeInstall(INetCfgClassSetup* This,INetCfgComponent *pComponent,OBO_TOKEN *pOboToken,LPWSTR *pmszwRefs) {
    return This->lpVtbl->DeInstall(This,pComponent,pOboToken,pmszwRefs);
}
#endif
#endif

#endif


#endif  /* __INetCfgClassSetup_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgClassSetup2 interface
 */
#ifndef __INetCfgClassSetup2_INTERFACE_DEFINED__
#define __INetCfgClassSetup2_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetCfgClassSetup2, 0xc0e8aea0, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8aea0-306e-11d1-aacf-00805fc1270e")
INetCfgClassSetup2 : public INetCfgClassSetup
{
    virtual HRESULT STDMETHODCALLTYPE UpdateNonEnumeratedComponent(
        INetCfgComponent *pIComp,
        DWORD dwSetupFlags,
        DWORD dwUpgradeFromBuildNo) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgClassSetup2, 0xc0e8aea0, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetCfgClassSetup2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgClassSetup2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgClassSetup2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgClassSetup2 *This);

    /*** INetCfgClassSetup methods ***/
    HRESULT (STDMETHODCALLTYPE *SelectAndInstall)(
        INetCfgClassSetup2 *This,
        HWND hwndParent,
        OBO_TOKEN *pOboToken,
        INetCfgComponent **ppnccItem);

    HRESULT (STDMETHODCALLTYPE *Install)(
        INetCfgClassSetup2 *This,
        LPCWSTR pszwInfId,
        OBO_TOKEN *pOboToken,
        DWORD dwSetupFlags,
        DWORD dwUpgradeFromBuildNo,
        LPCWSTR pszwAnswerFile,
        LPCWSTR pszwAnswerSections,
        INetCfgComponent **ppnccItem);

    HRESULT (STDMETHODCALLTYPE *DeInstall)(
        INetCfgClassSetup2 *This,
        INetCfgComponent *pComponent,
        OBO_TOKEN *pOboToken,
        LPWSTR *pmszwRefs);

    /*** INetCfgClassSetup2 methods ***/
    HRESULT (STDMETHODCALLTYPE *UpdateNonEnumeratedComponent)(
        INetCfgClassSetup2 *This,
        INetCfgComponent *pIComp,
        DWORD dwSetupFlags,
        DWORD dwUpgradeFromBuildNo);

    END_INTERFACE
} INetCfgClassSetup2Vtbl;

interface INetCfgClassSetup2 {
    CONST_VTBL INetCfgClassSetup2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgClassSetup2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgClassSetup2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgClassSetup2_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgClassSetup methods ***/
#define INetCfgClassSetup2_SelectAndInstall(This,hwndParent,pOboToken,ppnccItem) (This)->lpVtbl->SelectAndInstall(This,hwndParent,pOboToken,ppnccItem)
#define INetCfgClassSetup2_Install(This,pszwInfId,pOboToken,dwSetupFlags,dwUpgradeFromBuildNo,pszwAnswerFile,pszwAnswerSections,ppnccItem) (This)->lpVtbl->Install(This,pszwInfId,pOboToken,dwSetupFlags,dwUpgradeFromBuildNo,pszwAnswerFile,pszwAnswerSections,ppnccItem)
#define INetCfgClassSetup2_DeInstall(This,pComponent,pOboToken,pmszwRefs) (This)->lpVtbl->DeInstall(This,pComponent,pOboToken,pmszwRefs)
/*** INetCfgClassSetup2 methods ***/
#define INetCfgClassSetup2_UpdateNonEnumeratedComponent(This,pIComp,dwSetupFlags,dwUpgradeFromBuildNo) (This)->lpVtbl->UpdateNonEnumeratedComponent(This,pIComp,dwSetupFlags,dwUpgradeFromBuildNo)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgClassSetup2_QueryInterface(INetCfgClassSetup2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgClassSetup2_AddRef(INetCfgClassSetup2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgClassSetup2_Release(INetCfgClassSetup2* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgClassSetup methods ***/
static inline HRESULT INetCfgClassSetup2_SelectAndInstall(INetCfgClassSetup2* This,HWND hwndParent,OBO_TOKEN *pOboToken,INetCfgComponent **ppnccItem) {
    return This->lpVtbl->SelectAndInstall(This,hwndParent,pOboToken,ppnccItem);
}
static inline HRESULT INetCfgClassSetup2_Install(INetCfgClassSetup2* This,LPCWSTR pszwInfId,OBO_TOKEN *pOboToken,DWORD dwSetupFlags,DWORD dwUpgradeFromBuildNo,LPCWSTR pszwAnswerFile,LPCWSTR pszwAnswerSections,INetCfgComponent **ppnccItem) {
    return This->lpVtbl->Install(This,pszwInfId,pOboToken,dwSetupFlags,dwUpgradeFromBuildNo,pszwAnswerFile,pszwAnswerSections,ppnccItem);
}
static inline HRESULT INetCfgClassSetup2_DeInstall(INetCfgClassSetup2* This,INetCfgComponent *pComponent,OBO_TOKEN *pOboToken,LPWSTR *pmszwRefs) {
    return This->lpVtbl->DeInstall(This,pComponent,pOboToken,pmszwRefs);
}
/*** INetCfgClassSetup2 methods ***/
static inline HRESULT INetCfgClassSetup2_UpdateNonEnumeratedComponent(INetCfgClassSetup2* This,INetCfgComponent *pIComp,DWORD dwSetupFlags,DWORD dwUpgradeFromBuildNo) {
    return This->lpVtbl->UpdateNonEnumeratedComponent(This,pIComp,dwSetupFlags,dwUpgradeFromBuildNo);
}
#endif
#endif

#endif


#endif  /* __INetCfgClassSetup2_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgComponent interface
 */
#ifndef __INetCfgComponent_INTERFACE_DEFINED__
#define __INetCfgComponent_INTERFACE_DEFINED__

typedef enum tagCOMPONENT_CHARACTERISTICS {
    NCF_VIRTUAL = 0x1,
    NCF_SOFTWARE_ENUMERATED = 0x2,
    NCF_PHYSICAL = 0x4,
    NCF_HIDDEN = 0x8,
    NCF_NO_SERVICE = 0x10,
    NCF_NOT_USER_REMOVABLE = 0x20,
    NCF_MULTIPORT_INSTANCED_ADAPTER = 0x40,
    NCF_HAS_UI = 0x80,
    NCF_SINGLE_INSTANCE = 0x100,
    NCF_FILTER = 0x400,
    NCF_DONTEXPOSELOWER = 0x1000,
    NCF_HIDE_BINDING = 0x2000,
    NCF_NDIS_PROTOCOL = 0x4000,
    NCF_FIXED_BINDING = 0x20000,
    NCF_LW_FILTER = 0x40000
} COMPONENT_CHARACTERISTICS;
typedef enum tagNCRP_FLAGS {
    NCRP_QUERY_PROPERTY_UI = 0x1,
    NCRP_SHOW_PROPERTY_UI = 0x2
} NCRP_FLAGS;
DEFINE_GUID(IID_INetCfgComponent, 0xc0e8ae99, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae99-306e-11d1-aacf-00805fc1270e")
INetCfgComponent : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDisplayName(
        LPWSTR *ppszwDisplayName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDisplayName(
        LPCWSTR pszwDisplayName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHelpText(
        LPWSTR *pszwHelpText) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetId(
        LPWSTR *ppszwId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCharacteristics(
        LPDWORD pdwCharacteristics) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInstanceGuid(
        GUID *pGuid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPnpDevNodeId(
        LPWSTR *ppszwDevNodeId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetClassGuid(
        GUID *pGuid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBindName(
        LPWSTR *ppszwBindName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceStatus(
        ULONG *pulStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenParamKey(
        HKEY *phkey) = 0;

    virtual HRESULT STDMETHODCALLTYPE RaisePropertyUi(
        HWND hwndParent,
        DWORD dwFlags,
        IUnknown *punkContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgComponent, 0xc0e8ae99, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetCfgComponentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgComponent *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgComponent *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgComponent *This);

    /*** INetCfgComponent methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDisplayName)(
        INetCfgComponent *This,
        LPWSTR *ppszwDisplayName);

    HRESULT (STDMETHODCALLTYPE *SetDisplayName)(
        INetCfgComponent *This,
        LPCWSTR pszwDisplayName);

    HRESULT (STDMETHODCALLTYPE *GetHelpText)(
        INetCfgComponent *This,
        LPWSTR *pszwHelpText);

    HRESULT (STDMETHODCALLTYPE *GetId)(
        INetCfgComponent *This,
        LPWSTR *ppszwId);

    HRESULT (STDMETHODCALLTYPE *GetCharacteristics)(
        INetCfgComponent *This,
        LPDWORD pdwCharacteristics);

    HRESULT (STDMETHODCALLTYPE *GetInstanceGuid)(
        INetCfgComponent *This,
        GUID *pGuid);

    HRESULT (STDMETHODCALLTYPE *GetPnpDevNodeId)(
        INetCfgComponent *This,
        LPWSTR *ppszwDevNodeId);

    HRESULT (STDMETHODCALLTYPE *GetClassGuid)(
        INetCfgComponent *This,
        GUID *pGuid);

    HRESULT (STDMETHODCALLTYPE *GetBindName)(
        INetCfgComponent *This,
        LPWSTR *ppszwBindName);

    HRESULT (STDMETHODCALLTYPE *GetDeviceStatus)(
        INetCfgComponent *This,
        ULONG *pulStatus);

    HRESULT (STDMETHODCALLTYPE *OpenParamKey)(
        INetCfgComponent *This,
        HKEY *phkey);

    HRESULT (STDMETHODCALLTYPE *RaisePropertyUi)(
        INetCfgComponent *This,
        HWND hwndParent,
        DWORD dwFlags,
        IUnknown *punkContext);

    END_INTERFACE
} INetCfgComponentVtbl;

interface INetCfgComponent {
    CONST_VTBL INetCfgComponentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgComponent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgComponent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgComponent_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgComponent methods ***/
#define INetCfgComponent_GetDisplayName(This,ppszwDisplayName) (This)->lpVtbl->GetDisplayName(This,ppszwDisplayName)
#define INetCfgComponent_SetDisplayName(This,pszwDisplayName) (This)->lpVtbl->SetDisplayName(This,pszwDisplayName)
#define INetCfgComponent_GetHelpText(This,pszwHelpText) (This)->lpVtbl->GetHelpText(This,pszwHelpText)
#define INetCfgComponent_GetId(This,ppszwId) (This)->lpVtbl->GetId(This,ppszwId)
#define INetCfgComponent_GetCharacteristics(This,pdwCharacteristics) (This)->lpVtbl->GetCharacteristics(This,pdwCharacteristics)
#define INetCfgComponent_GetInstanceGuid(This,pGuid) (This)->lpVtbl->GetInstanceGuid(This,pGuid)
#define INetCfgComponent_GetPnpDevNodeId(This,ppszwDevNodeId) (This)->lpVtbl->GetPnpDevNodeId(This,ppszwDevNodeId)
#define INetCfgComponent_GetClassGuid(This,pGuid) (This)->lpVtbl->GetClassGuid(This,pGuid)
#define INetCfgComponent_GetBindName(This,ppszwBindName) (This)->lpVtbl->GetBindName(This,ppszwBindName)
#define INetCfgComponent_GetDeviceStatus(This,pulStatus) (This)->lpVtbl->GetDeviceStatus(This,pulStatus)
#define INetCfgComponent_OpenParamKey(This,phkey) (This)->lpVtbl->OpenParamKey(This,phkey)
#define INetCfgComponent_RaisePropertyUi(This,hwndParent,dwFlags,punkContext) (This)->lpVtbl->RaisePropertyUi(This,hwndParent,dwFlags,punkContext)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgComponent_QueryInterface(INetCfgComponent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgComponent_AddRef(INetCfgComponent* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgComponent_Release(INetCfgComponent* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgComponent methods ***/
static inline HRESULT INetCfgComponent_GetDisplayName(INetCfgComponent* This,LPWSTR *ppszwDisplayName) {
    return This->lpVtbl->GetDisplayName(This,ppszwDisplayName);
}
static inline HRESULT INetCfgComponent_SetDisplayName(INetCfgComponent* This,LPCWSTR pszwDisplayName) {
    return This->lpVtbl->SetDisplayName(This,pszwDisplayName);
}
static inline HRESULT INetCfgComponent_GetHelpText(INetCfgComponent* This,LPWSTR *pszwHelpText) {
    return This->lpVtbl->GetHelpText(This,pszwHelpText);
}
static inline HRESULT INetCfgComponent_GetId(INetCfgComponent* This,LPWSTR *ppszwId) {
    return This->lpVtbl->GetId(This,ppszwId);
}
static inline HRESULT INetCfgComponent_GetCharacteristics(INetCfgComponent* This,LPDWORD pdwCharacteristics) {
    return This->lpVtbl->GetCharacteristics(This,pdwCharacteristics);
}
static inline HRESULT INetCfgComponent_GetInstanceGuid(INetCfgComponent* This,GUID *pGuid) {
    return This->lpVtbl->GetInstanceGuid(This,pGuid);
}
static inline HRESULT INetCfgComponent_GetPnpDevNodeId(INetCfgComponent* This,LPWSTR *ppszwDevNodeId) {
    return This->lpVtbl->GetPnpDevNodeId(This,ppszwDevNodeId);
}
static inline HRESULT INetCfgComponent_GetClassGuid(INetCfgComponent* This,GUID *pGuid) {
    return This->lpVtbl->GetClassGuid(This,pGuid);
}
static inline HRESULT INetCfgComponent_GetBindName(INetCfgComponent* This,LPWSTR *ppszwBindName) {
    return This->lpVtbl->GetBindName(This,ppszwBindName);
}
static inline HRESULT INetCfgComponent_GetDeviceStatus(INetCfgComponent* This,ULONG *pulStatus) {
    return This->lpVtbl->GetDeviceStatus(This,pulStatus);
}
static inline HRESULT INetCfgComponent_OpenParamKey(INetCfgComponent* This,HKEY *phkey) {
    return This->lpVtbl->OpenParamKey(This,phkey);
}
static inline HRESULT INetCfgComponent_RaisePropertyUi(INetCfgComponent* This,HWND hwndParent,DWORD dwFlags,IUnknown *punkContext) {
    return This->lpVtbl->RaisePropertyUi(This,hwndParent,dwFlags,punkContext);
}
#endif
#endif

#endif


#endif  /* __INetCfgComponent_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgComponentBindings interface
 */
#ifndef __INetCfgComponentBindings_INTERFACE_DEFINED__
#define __INetCfgComponentBindings_INTERFACE_DEFINED__

typedef enum tagSUPPORTS_BINDING_INTERFACE_FLAGS {
    NCF_LOWER = 0x1,
    NCF_UPPER = 0x2
} SUPPORTS_BINDING_INTERFACE_FLAGS;
typedef enum tagENUM_BINDING_PATHS_FLAGS {
    EBP_ABOVE = 0x1,
    EBP_BELOW = 0x2
} ENUM_BINDING_PATHS_FLAGS;
DEFINE_GUID(IID_INetCfgComponentBindings, 0xc0e8ae9e, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae9e-306e-11d1-aacf-00805fc1270e")
INetCfgComponentBindings : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE BindTo(
        INetCfgComponent *pnccItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnbindFrom(
        INetCfgComponent *pnccItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE SupportsBindingInterface(
        DWORD dwFlags,
        LPCWSTR pszwInterfaceName) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsBoundTo(
        INetCfgComponent *pnccItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsBindableTo(
        INetCfgComponent *pnccItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumBindingPaths(
        DWORD dwFlags,
        IEnumNetCfgBindingPath **ppIEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE MoveBefore(
        INetCfgBindingPath *pncbItemSrc,
        INetCfgBindingPath *pncbItemDest) = 0;

    virtual HRESULT STDMETHODCALLTYPE MoveAfter(
        INetCfgBindingPath *pncbItemSrc,
        INetCfgBindingPath *pncbItemDest) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgComponentBindings, 0xc0e8ae9e, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetCfgComponentBindingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgComponentBindings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgComponentBindings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgComponentBindings *This);

    /*** INetCfgComponentBindings methods ***/
    HRESULT (STDMETHODCALLTYPE *BindTo)(
        INetCfgComponentBindings *This,
        INetCfgComponent *pnccItem);

    HRESULT (STDMETHODCALLTYPE *UnbindFrom)(
        INetCfgComponentBindings *This,
        INetCfgComponent *pnccItem);

    HRESULT (STDMETHODCALLTYPE *SupportsBindingInterface)(
        INetCfgComponentBindings *This,
        DWORD dwFlags,
        LPCWSTR pszwInterfaceName);

    HRESULT (STDMETHODCALLTYPE *IsBoundTo)(
        INetCfgComponentBindings *This,
        INetCfgComponent *pnccItem);

    HRESULT (STDMETHODCALLTYPE *IsBindableTo)(
        INetCfgComponentBindings *This,
        INetCfgComponent *pnccItem);

    HRESULT (STDMETHODCALLTYPE *EnumBindingPaths)(
        INetCfgComponentBindings *This,
        DWORD dwFlags,
        IEnumNetCfgBindingPath **ppIEnum);

    HRESULT (STDMETHODCALLTYPE *MoveBefore)(
        INetCfgComponentBindings *This,
        INetCfgBindingPath *pncbItemSrc,
        INetCfgBindingPath *pncbItemDest);

    HRESULT (STDMETHODCALLTYPE *MoveAfter)(
        INetCfgComponentBindings *This,
        INetCfgBindingPath *pncbItemSrc,
        INetCfgBindingPath *pncbItemDest);

    END_INTERFACE
} INetCfgComponentBindingsVtbl;

interface INetCfgComponentBindings {
    CONST_VTBL INetCfgComponentBindingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgComponentBindings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgComponentBindings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgComponentBindings_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgComponentBindings methods ***/
#define INetCfgComponentBindings_BindTo(This,pnccItem) (This)->lpVtbl->BindTo(This,pnccItem)
#define INetCfgComponentBindings_UnbindFrom(This,pnccItem) (This)->lpVtbl->UnbindFrom(This,pnccItem)
#define INetCfgComponentBindings_SupportsBindingInterface(This,dwFlags,pszwInterfaceName) (This)->lpVtbl->SupportsBindingInterface(This,dwFlags,pszwInterfaceName)
#define INetCfgComponentBindings_IsBoundTo(This,pnccItem) (This)->lpVtbl->IsBoundTo(This,pnccItem)
#define INetCfgComponentBindings_IsBindableTo(This,pnccItem) (This)->lpVtbl->IsBindableTo(This,pnccItem)
#define INetCfgComponentBindings_EnumBindingPaths(This,dwFlags,ppIEnum) (This)->lpVtbl->EnumBindingPaths(This,dwFlags,ppIEnum)
#define INetCfgComponentBindings_MoveBefore(This,pncbItemSrc,pncbItemDest) (This)->lpVtbl->MoveBefore(This,pncbItemSrc,pncbItemDest)
#define INetCfgComponentBindings_MoveAfter(This,pncbItemSrc,pncbItemDest) (This)->lpVtbl->MoveAfter(This,pncbItemSrc,pncbItemDest)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgComponentBindings_QueryInterface(INetCfgComponentBindings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgComponentBindings_AddRef(INetCfgComponentBindings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgComponentBindings_Release(INetCfgComponentBindings* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgComponentBindings methods ***/
static inline HRESULT INetCfgComponentBindings_BindTo(INetCfgComponentBindings* This,INetCfgComponent *pnccItem) {
    return This->lpVtbl->BindTo(This,pnccItem);
}
static inline HRESULT INetCfgComponentBindings_UnbindFrom(INetCfgComponentBindings* This,INetCfgComponent *pnccItem) {
    return This->lpVtbl->UnbindFrom(This,pnccItem);
}
static inline HRESULT INetCfgComponentBindings_SupportsBindingInterface(INetCfgComponentBindings* This,DWORD dwFlags,LPCWSTR pszwInterfaceName) {
    return This->lpVtbl->SupportsBindingInterface(This,dwFlags,pszwInterfaceName);
}
static inline HRESULT INetCfgComponentBindings_IsBoundTo(INetCfgComponentBindings* This,INetCfgComponent *pnccItem) {
    return This->lpVtbl->IsBoundTo(This,pnccItem);
}
static inline HRESULT INetCfgComponentBindings_IsBindableTo(INetCfgComponentBindings* This,INetCfgComponent *pnccItem) {
    return This->lpVtbl->IsBindableTo(This,pnccItem);
}
static inline HRESULT INetCfgComponentBindings_EnumBindingPaths(INetCfgComponentBindings* This,DWORD dwFlags,IEnumNetCfgBindingPath **ppIEnum) {
    return This->lpVtbl->EnumBindingPaths(This,dwFlags,ppIEnum);
}
static inline HRESULT INetCfgComponentBindings_MoveBefore(INetCfgComponentBindings* This,INetCfgBindingPath *pncbItemSrc,INetCfgBindingPath *pncbItemDest) {
    return This->lpVtbl->MoveBefore(This,pncbItemSrc,pncbItemDest);
}
static inline HRESULT INetCfgComponentBindings_MoveAfter(INetCfgComponentBindings* This,INetCfgBindingPath *pncbItemSrc,INetCfgBindingPath *pncbItemDest) {
    return This->lpVtbl->MoveAfter(This,pncbItemSrc,pncbItemDest);
}
#endif
#endif

#endif


#endif  /* __INetCfgComponentBindings_INTERFACE_DEFINED__ */


/*****************************************************************************
 * INetCfgSysPrep interface
 */
#ifndef __INetCfgSysPrep_INTERFACE_DEFINED__
#define __INetCfgSysPrep_INTERFACE_DEFINED__

DEFINE_GUID(IID_INetCfgSysPrep, 0xc0e8ae98, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0e8ae98-306e-11d1-aacf-00805fc1270e")
INetCfgSysPrep : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE HrSetupSetFirstDword(
        LPCWSTR pwszSection,
        LPCWSTR pwszKey,
        DWORD dwValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE HrSetupSetFirstString(
        LPCWSTR pwszSection,
        LPCWSTR pwszKey,
        LPCWSTR pwszValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE HrSetupSetFirstStringAsBool(
        LPCWSTR pwszSection,
        LPCWSTR pwszKey,
        WINBOOL fValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE HrSetupSetFirstMultiSzField(
        LPCWSTR pwszSection,
        LPCWSTR pwszKey,
        LPCWSTR pmszValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(INetCfgSysPrep, 0xc0e8ae98, 0x306e, 0x11d1, 0xaa,0xcf, 0x00,0x80,0x5f,0xc1,0x27,0x0e)
#endif
#else
typedef struct INetCfgSysPrepVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        INetCfgSysPrep *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        INetCfgSysPrep *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        INetCfgSysPrep *This);

    /*** INetCfgSysPrep methods ***/
    HRESULT (STDMETHODCALLTYPE *HrSetupSetFirstDword)(
        INetCfgSysPrep *This,
        LPCWSTR pwszSection,
        LPCWSTR pwszKey,
        DWORD dwValue);

    HRESULT (STDMETHODCALLTYPE *HrSetupSetFirstString)(
        INetCfgSysPrep *This,
        LPCWSTR pwszSection,
        LPCWSTR pwszKey,
        LPCWSTR pwszValue);

    HRESULT (STDMETHODCALLTYPE *HrSetupSetFirstStringAsBool)(
        INetCfgSysPrep *This,
        LPCWSTR pwszSection,
        LPCWSTR pwszKey,
        WINBOOL fValue);

    HRESULT (STDMETHODCALLTYPE *HrSetupSetFirstMultiSzField)(
        INetCfgSysPrep *This,
        LPCWSTR pwszSection,
        LPCWSTR pwszKey,
        LPCWSTR pmszValue);

    END_INTERFACE
} INetCfgSysPrepVtbl;

interface INetCfgSysPrep {
    CONST_VTBL INetCfgSysPrepVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define INetCfgSysPrep_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define INetCfgSysPrep_AddRef(This) (This)->lpVtbl->AddRef(This)
#define INetCfgSysPrep_Release(This) (This)->lpVtbl->Release(This)
/*** INetCfgSysPrep methods ***/
#define INetCfgSysPrep_HrSetupSetFirstDword(This,pwszSection,pwszKey,dwValue) (This)->lpVtbl->HrSetupSetFirstDword(This,pwszSection,pwszKey,dwValue)
#define INetCfgSysPrep_HrSetupSetFirstString(This,pwszSection,pwszKey,pwszValue) (This)->lpVtbl->HrSetupSetFirstString(This,pwszSection,pwszKey,pwszValue)
#define INetCfgSysPrep_HrSetupSetFirstStringAsBool(This,pwszSection,pwszKey,fValue) (This)->lpVtbl->HrSetupSetFirstStringAsBool(This,pwszSection,pwszKey,fValue)
#define INetCfgSysPrep_HrSetupSetFirstMultiSzField(This,pwszSection,pwszKey,pmszValue) (This)->lpVtbl->HrSetupSetFirstMultiSzField(This,pwszSection,pwszKey,pmszValue)
#else
/*** IUnknown methods ***/
static inline HRESULT INetCfgSysPrep_QueryInterface(INetCfgSysPrep* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG INetCfgSysPrep_AddRef(INetCfgSysPrep* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG INetCfgSysPrep_Release(INetCfgSysPrep* This) {
    return This->lpVtbl->Release(This);
}
/*** INetCfgSysPrep methods ***/
static inline HRESULT INetCfgSysPrep_HrSetupSetFirstDword(INetCfgSysPrep* This,LPCWSTR pwszSection,LPCWSTR pwszKey,DWORD dwValue) {
    return This->lpVtbl->HrSetupSetFirstDword(This,pwszSection,pwszKey,dwValue);
}
static inline HRESULT INetCfgSysPrep_HrSetupSetFirstString(INetCfgSysPrep* This,LPCWSTR pwszSection,LPCWSTR pwszKey,LPCWSTR pwszValue) {
    return This->lpVtbl->HrSetupSetFirstString(This,pwszSection,pwszKey,pwszValue);
}
static inline HRESULT INetCfgSysPrep_HrSetupSetFirstStringAsBool(INetCfgSysPrep* This,LPCWSTR pwszSection,LPCWSTR pwszKey,WINBOOL fValue) {
    return This->lpVtbl->HrSetupSetFirstStringAsBool(This,pwszSection,pwszKey,fValue);
}
static inline HRESULT INetCfgSysPrep_HrSetupSetFirstMultiSzField(INetCfgSysPrep* This,LPCWSTR pwszSection,LPCWSTR pwszKey,LPCWSTR pmszValue) {
    return This->lpVtbl->HrSetupSetFirstMultiSzField(This,pwszSection,pwszKey,pmszValue);
}
#endif
#endif

#endif


#endif  /* __INetCfgSysPrep_INTERFACE_DEFINED__ */

#endif /* WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP) */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __netcfgx_h__ */
