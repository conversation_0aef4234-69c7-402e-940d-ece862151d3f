/*** Autogenerated by WIDL 10.12 from include/uianimation.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __uianimation_h__
#define __uianimation_h__

/* Forward declarations */

#ifndef __UIAnimationManager_FWD_DEFINED__
#define __UIAnimationManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class UIAnimationManager UIAnimationManager;
#else
typedef struct UIAnimationManager UIAnimationManager;
#endif /* defined __cplusplus */
#endif /* defined __UIAnimationManager_FWD_DEFINED__ */

#ifndef __UIAnimationManager2_FWD_DEFINED__
#define __UIAnimationManager2_FWD_DEFINED__
#ifdef __cplusplus
typedef class UIAnimationManager2 UIAnimationManager2;
#else
typedef struct UIAnimationManager2 UIAnimationManager2;
#endif /* defined __cplusplus */
#endif /* defined __UIAnimationManager2_FWD_DEFINED__ */

#ifndef __UIAnimationTransitionLibrary_FWD_DEFINED__
#define __UIAnimationTransitionLibrary_FWD_DEFINED__
#ifdef __cplusplus
typedef class UIAnimationTransitionLibrary UIAnimationTransitionLibrary;
#else
typedef struct UIAnimationTransitionLibrary UIAnimationTransitionLibrary;
#endif /* defined __cplusplus */
#endif /* defined __UIAnimationTransitionLibrary_FWD_DEFINED__ */

#ifndef __UIAnimationTransitionLibrary2_FWD_DEFINED__
#define __UIAnimationTransitionLibrary2_FWD_DEFINED__
#ifdef __cplusplus
typedef class UIAnimationTransitionLibrary2 UIAnimationTransitionLibrary2;
#else
typedef struct UIAnimationTransitionLibrary2 UIAnimationTransitionLibrary2;
#endif /* defined __cplusplus */
#endif /* defined __UIAnimationTransitionLibrary2_FWD_DEFINED__ */

#ifndef __UIAnimationTransitionFactory_FWD_DEFINED__
#define __UIAnimationTransitionFactory_FWD_DEFINED__
#ifdef __cplusplus
typedef class UIAnimationTransitionFactory UIAnimationTransitionFactory;
#else
typedef struct UIAnimationTransitionFactory UIAnimationTransitionFactory;
#endif /* defined __cplusplus */
#endif /* defined __UIAnimationTransitionFactory_FWD_DEFINED__ */

#ifndef __UIAnimationTransitionFactory2_FWD_DEFINED__
#define __UIAnimationTransitionFactory2_FWD_DEFINED__
#ifdef __cplusplus
typedef class UIAnimationTransitionFactory2 UIAnimationTransitionFactory2;
#else
typedef struct UIAnimationTransitionFactory2 UIAnimationTransitionFactory2;
#endif /* defined __cplusplus */
#endif /* defined __UIAnimationTransitionFactory2_FWD_DEFINED__ */

#ifndef __UIAnimationTimer_FWD_DEFINED__
#define __UIAnimationTimer_FWD_DEFINED__
#ifdef __cplusplus
typedef class UIAnimationTimer UIAnimationTimer;
#else
typedef struct UIAnimationTimer UIAnimationTimer;
#endif /* defined __cplusplus */
#endif /* defined __UIAnimationTimer_FWD_DEFINED__ */

#ifndef __IUIAnimationManager_FWD_DEFINED__
#define __IUIAnimationManager_FWD_DEFINED__
typedef interface IUIAnimationManager IUIAnimationManager;
#ifdef __cplusplus
interface IUIAnimationManager;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariable_FWD_DEFINED__
#define __IUIAnimationVariable_FWD_DEFINED__
typedef interface IUIAnimationVariable IUIAnimationVariable;
#ifdef __cplusplus
interface IUIAnimationVariable;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationStoryboard_FWD_DEFINED__
#define __IUIAnimationStoryboard_FWD_DEFINED__
typedef interface IUIAnimationStoryboard IUIAnimationStoryboard;
#ifdef __cplusplus
interface IUIAnimationStoryboard;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransition_FWD_DEFINED__
#define __IUIAnimationTransition_FWD_DEFINED__
typedef interface IUIAnimationTransition IUIAnimationTransition;
#ifdef __cplusplus
interface IUIAnimationTransition;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationStoryboardEventHandler_FWD_DEFINED__
#define __IUIAnimationStoryboardEventHandler_FWD_DEFINED__
typedef interface IUIAnimationStoryboardEventHandler IUIAnimationStoryboardEventHandler;
#ifdef __cplusplus
interface IUIAnimationStoryboardEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariableChangeHandler_FWD_DEFINED__
#define __IUIAnimationVariableChangeHandler_FWD_DEFINED__
typedef interface IUIAnimationVariableChangeHandler IUIAnimationVariableChangeHandler;
#ifdef __cplusplus
interface IUIAnimationVariableChangeHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariableIntegerChangeHandler_FWD_DEFINED__
#define __IUIAnimationVariableIntegerChangeHandler_FWD_DEFINED__
typedef interface IUIAnimationVariableIntegerChangeHandler IUIAnimationVariableIntegerChangeHandler;
#ifdef __cplusplus
interface IUIAnimationVariableIntegerChangeHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationManagerEventHandler_FWD_DEFINED__
#define __IUIAnimationManagerEventHandler_FWD_DEFINED__
typedef interface IUIAnimationManagerEventHandler IUIAnimationManagerEventHandler;
#ifdef __cplusplus
interface IUIAnimationManagerEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationPriorityComparison_FWD_DEFINED__
#define __IUIAnimationPriorityComparison_FWD_DEFINED__
typedef interface IUIAnimationPriorityComparison IUIAnimationPriorityComparison;
#ifdef __cplusplus
interface IUIAnimationPriorityComparison;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationManager2_FWD_DEFINED__
#define __IUIAnimationManager2_FWD_DEFINED__
typedef interface IUIAnimationManager2 IUIAnimationManager2;
#ifdef __cplusplus
interface IUIAnimationManager2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariable2_FWD_DEFINED__
#define __IUIAnimationVariable2_FWD_DEFINED__
typedef interface IUIAnimationVariable2 IUIAnimationVariable2;
#ifdef __cplusplus
interface IUIAnimationVariable2;
#endif /* __cplusplus */
#endif

#ifndef __IDCompositionAnimation_FWD_DEFINED__
#define __IDCompositionAnimation_FWD_DEFINED__
typedef interface IDCompositionAnimation IDCompositionAnimation;
#ifdef __cplusplus
interface IDCompositionAnimation;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationStoryboard2_FWD_DEFINED__
#define __IUIAnimationStoryboard2_FWD_DEFINED__
typedef interface IUIAnimationStoryboard2 IUIAnimationStoryboard2;
#ifdef __cplusplus
interface IUIAnimationStoryboard2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransition2_FWD_DEFINED__
#define __IUIAnimationTransition2_FWD_DEFINED__
typedef interface IUIAnimationTransition2 IUIAnimationTransition2;
#ifdef __cplusplus
interface IUIAnimationTransition2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationLoopIterationChangeHandler2_FWD_DEFINED__
#define __IUIAnimationLoopIterationChangeHandler2_FWD_DEFINED__
typedef interface IUIAnimationLoopIterationChangeHandler2 IUIAnimationLoopIterationChangeHandler2;
#ifdef __cplusplus
interface IUIAnimationLoopIterationChangeHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationStoryboardEventHandler2_FWD_DEFINED__
#define __IUIAnimationStoryboardEventHandler2_FWD_DEFINED__
typedef interface IUIAnimationStoryboardEventHandler2 IUIAnimationStoryboardEventHandler2;
#ifdef __cplusplus
interface IUIAnimationStoryboardEventHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariableChangeHandler2_FWD_DEFINED__
#define __IUIAnimationVariableChangeHandler2_FWD_DEFINED__
typedef interface IUIAnimationVariableChangeHandler2 IUIAnimationVariableChangeHandler2;
#ifdef __cplusplus
interface IUIAnimationVariableChangeHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariableIntegerChangeHandler2_FWD_DEFINED__
#define __IUIAnimationVariableIntegerChangeHandler2_FWD_DEFINED__
typedef interface IUIAnimationVariableIntegerChangeHandler2 IUIAnimationVariableIntegerChangeHandler2;
#ifdef __cplusplus
interface IUIAnimationVariableIntegerChangeHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariableCurveChangeHandler2_FWD_DEFINED__
#define __IUIAnimationVariableCurveChangeHandler2_FWD_DEFINED__
typedef interface IUIAnimationVariableCurveChangeHandler2 IUIAnimationVariableCurveChangeHandler2;
#ifdef __cplusplus
interface IUIAnimationVariableCurveChangeHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationManagerEventHandler2_FWD_DEFINED__
#define __IUIAnimationManagerEventHandler2_FWD_DEFINED__
typedef interface IUIAnimationManagerEventHandler2 IUIAnimationManagerEventHandler2;
#ifdef __cplusplus
interface IUIAnimationManagerEventHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationPriorityComparison2_FWD_DEFINED__
#define __IUIAnimationPriorityComparison2_FWD_DEFINED__
typedef interface IUIAnimationPriorityComparison2 IUIAnimationPriorityComparison2;
#ifdef __cplusplus
interface IUIAnimationPriorityComparison2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransitionLibrary_FWD_DEFINED__
#define __IUIAnimationTransitionLibrary_FWD_DEFINED__
typedef interface IUIAnimationTransitionLibrary IUIAnimationTransitionLibrary;
#ifdef __cplusplus
interface IUIAnimationTransitionLibrary;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransitionLibrary2_FWD_DEFINED__
#define __IUIAnimationTransitionLibrary2_FWD_DEFINED__
typedef interface IUIAnimationTransitionLibrary2 IUIAnimationTransitionLibrary2;
#ifdef __cplusplus
interface IUIAnimationTransitionLibrary2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransitionFactory_FWD_DEFINED__
#define __IUIAnimationTransitionFactory_FWD_DEFINED__
typedef interface IUIAnimationTransitionFactory IUIAnimationTransitionFactory;
#ifdef __cplusplus
interface IUIAnimationTransitionFactory;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationInterpolator_FWD_DEFINED__
#define __IUIAnimationInterpolator_FWD_DEFINED__
typedef interface IUIAnimationInterpolator IUIAnimationInterpolator;
#ifdef __cplusplus
interface IUIAnimationInterpolator;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransitionFactory2_FWD_DEFINED__
#define __IUIAnimationTransitionFactory2_FWD_DEFINED__
typedef interface IUIAnimationTransitionFactory2 IUIAnimationTransitionFactory2;
#ifdef __cplusplus
interface IUIAnimationTransitionFactory2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationInterpolator2_FWD_DEFINED__
#define __IUIAnimationInterpolator2_FWD_DEFINED__
typedef interface IUIAnimationInterpolator2 IUIAnimationInterpolator2;
#ifdef __cplusplus
interface IUIAnimationInterpolator2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationPrimitiveInterpolation_FWD_DEFINED__
#define __IUIAnimationPrimitiveInterpolation_FWD_DEFINED__
typedef interface IUIAnimationPrimitiveInterpolation IUIAnimationPrimitiveInterpolation;
#ifdef __cplusplus
interface IUIAnimationPrimitiveInterpolation;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTimer_FWD_DEFINED__
#define __IUIAnimationTimer_FWD_DEFINED__
typedef interface IUIAnimationTimer IUIAnimationTimer;
#ifdef __cplusplus
interface IUIAnimationTimer;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTimerUpdateHandler_FWD_DEFINED__
#define __IUIAnimationTimerUpdateHandler_FWD_DEFINED__
typedef interface IUIAnimationTimerUpdateHandler IUIAnimationTimerUpdateHandler;
#ifdef __cplusplus
interface IUIAnimationTimerUpdateHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTimerClientEventHandler_FWD_DEFINED__
#define __IUIAnimationTimerClientEventHandler_FWD_DEFINED__
typedef interface IUIAnimationTimerClientEventHandler IUIAnimationTimerClientEventHandler;
#ifdef __cplusplus
interface IUIAnimationTimerClientEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTimerEventHandler_FWD_DEFINED__
#define __IUIAnimationTimerEventHandler_FWD_DEFINED__
typedef interface IUIAnimationTimerEventHandler IUIAnimationTimerEventHandler;
#ifdef __cplusplus
interface IUIAnimationTimerEventHandler;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <unknwn.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __IUIAnimationManager_FWD_DEFINED__
#define __IUIAnimationManager_FWD_DEFINED__
typedef interface IUIAnimationManager IUIAnimationManager;
#ifdef __cplusplus
interface IUIAnimationManager;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariable_FWD_DEFINED__
#define __IUIAnimationVariable_FWD_DEFINED__
typedef interface IUIAnimationVariable IUIAnimationVariable;
#ifdef __cplusplus
interface IUIAnimationVariable;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationStoryboard_FWD_DEFINED__
#define __IUIAnimationStoryboard_FWD_DEFINED__
typedef interface IUIAnimationStoryboard IUIAnimationStoryboard;
#ifdef __cplusplus
interface IUIAnimationStoryboard;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransition_FWD_DEFINED__
#define __IUIAnimationTransition_FWD_DEFINED__
typedef interface IUIAnimationTransition IUIAnimationTransition;
#ifdef __cplusplus
interface IUIAnimationTransition;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationStoryboardEventHandler_FWD_DEFINED__
#define __IUIAnimationStoryboardEventHandler_FWD_DEFINED__
typedef interface IUIAnimationStoryboardEventHandler IUIAnimationStoryboardEventHandler;
#ifdef __cplusplus
interface IUIAnimationStoryboardEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariableChangeHandler_FWD_DEFINED__
#define __IUIAnimationVariableChangeHandler_FWD_DEFINED__
typedef interface IUIAnimationVariableChangeHandler IUIAnimationVariableChangeHandler;
#ifdef __cplusplus
interface IUIAnimationVariableChangeHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariableIntegerChangeHandler_FWD_DEFINED__
#define __IUIAnimationVariableIntegerChangeHandler_FWD_DEFINED__
typedef interface IUIAnimationVariableIntegerChangeHandler IUIAnimationVariableIntegerChangeHandler;
#ifdef __cplusplus
interface IUIAnimationVariableIntegerChangeHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationManagerEventHandler_FWD_DEFINED__
#define __IUIAnimationManagerEventHandler_FWD_DEFINED__
typedef interface IUIAnimationManagerEventHandler IUIAnimationManagerEventHandler;
#ifdef __cplusplus
interface IUIAnimationManagerEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationPriorityComparison_FWD_DEFINED__
#define __IUIAnimationPriorityComparison_FWD_DEFINED__
typedef interface IUIAnimationPriorityComparison IUIAnimationPriorityComparison;
#ifdef __cplusplus
interface IUIAnimationPriorityComparison;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationManager2_FWD_DEFINED__
#define __IUIAnimationManager2_FWD_DEFINED__
typedef interface IUIAnimationManager2 IUIAnimationManager2;
#ifdef __cplusplus
interface IUIAnimationManager2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariable2_FWD_DEFINED__
#define __IUIAnimationVariable2_FWD_DEFINED__
typedef interface IUIAnimationVariable2 IUIAnimationVariable2;
#ifdef __cplusplus
interface IUIAnimationVariable2;
#endif /* __cplusplus */
#endif

#ifndef __IDCompositionAnimation_FWD_DEFINED__
#define __IDCompositionAnimation_FWD_DEFINED__
typedef interface IDCompositionAnimation IDCompositionAnimation;
#ifdef __cplusplus
interface IDCompositionAnimation;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationStoryboard2_FWD_DEFINED__
#define __IUIAnimationStoryboard2_FWD_DEFINED__
typedef interface IUIAnimationStoryboard2 IUIAnimationStoryboard2;
#ifdef __cplusplus
interface IUIAnimationStoryboard2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransition2_FWD_DEFINED__
#define __IUIAnimationTransition2_FWD_DEFINED__
typedef interface IUIAnimationTransition2 IUIAnimationTransition2;
#ifdef __cplusplus
interface IUIAnimationTransition2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationLoopIterationChangeHandler2_FWD_DEFINED__
#define __IUIAnimationLoopIterationChangeHandler2_FWD_DEFINED__
typedef interface IUIAnimationLoopIterationChangeHandler2 IUIAnimationLoopIterationChangeHandler2;
#ifdef __cplusplus
interface IUIAnimationLoopIterationChangeHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationStoryboardEventHandler2_FWD_DEFINED__
#define __IUIAnimationStoryboardEventHandler2_FWD_DEFINED__
typedef interface IUIAnimationStoryboardEventHandler2 IUIAnimationStoryboardEventHandler2;
#ifdef __cplusplus
interface IUIAnimationStoryboardEventHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariableChangeHandler2_FWD_DEFINED__
#define __IUIAnimationVariableChangeHandler2_FWD_DEFINED__
typedef interface IUIAnimationVariableChangeHandler2 IUIAnimationVariableChangeHandler2;
#ifdef __cplusplus
interface IUIAnimationVariableChangeHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariableIntegerChangeHandler2_FWD_DEFINED__
#define __IUIAnimationVariableIntegerChangeHandler2_FWD_DEFINED__
typedef interface IUIAnimationVariableIntegerChangeHandler2 IUIAnimationVariableIntegerChangeHandler2;
#ifdef __cplusplus
interface IUIAnimationVariableIntegerChangeHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationVariableCurveChangeHandler2_FWD_DEFINED__
#define __IUIAnimationVariableCurveChangeHandler2_FWD_DEFINED__
typedef interface IUIAnimationVariableCurveChangeHandler2 IUIAnimationVariableCurveChangeHandler2;
#ifdef __cplusplus
interface IUIAnimationVariableCurveChangeHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationManagerEventHandler2_FWD_DEFINED__
#define __IUIAnimationManagerEventHandler2_FWD_DEFINED__
typedef interface IUIAnimationManagerEventHandler2 IUIAnimationManagerEventHandler2;
#ifdef __cplusplus
interface IUIAnimationManagerEventHandler2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationPriorityComparison2_FWD_DEFINED__
#define __IUIAnimationPriorityComparison2_FWD_DEFINED__
typedef interface IUIAnimationPriorityComparison2 IUIAnimationPriorityComparison2;
#ifdef __cplusplus
interface IUIAnimationPriorityComparison2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransitionLibrary_FWD_DEFINED__
#define __IUIAnimationTransitionLibrary_FWD_DEFINED__
typedef interface IUIAnimationTransitionLibrary IUIAnimationTransitionLibrary;
#ifdef __cplusplus
interface IUIAnimationTransitionLibrary;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransitionLibrary2_FWD_DEFINED__
#define __IUIAnimationTransitionLibrary2_FWD_DEFINED__
typedef interface IUIAnimationTransitionLibrary2 IUIAnimationTransitionLibrary2;
#ifdef __cplusplus
interface IUIAnimationTransitionLibrary2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransitionFactory_FWD_DEFINED__
#define __IUIAnimationTransitionFactory_FWD_DEFINED__
typedef interface IUIAnimationTransitionFactory IUIAnimationTransitionFactory;
#ifdef __cplusplus
interface IUIAnimationTransitionFactory;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationInterpolator_FWD_DEFINED__
#define __IUIAnimationInterpolator_FWD_DEFINED__
typedef interface IUIAnimationInterpolator IUIAnimationInterpolator;
#ifdef __cplusplus
interface IUIAnimationInterpolator;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTransitionFactory2_FWD_DEFINED__
#define __IUIAnimationTransitionFactory2_FWD_DEFINED__
typedef interface IUIAnimationTransitionFactory2 IUIAnimationTransitionFactory2;
#ifdef __cplusplus
interface IUIAnimationTransitionFactory2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationInterpolator2_FWD_DEFINED__
#define __IUIAnimationInterpolator2_FWD_DEFINED__
typedef interface IUIAnimationInterpolator2 IUIAnimationInterpolator2;
#ifdef __cplusplus
interface IUIAnimationInterpolator2;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationPrimitiveInterpolation_FWD_DEFINED__
#define __IUIAnimationPrimitiveInterpolation_FWD_DEFINED__
typedef interface IUIAnimationPrimitiveInterpolation IUIAnimationPrimitiveInterpolation;
#ifdef __cplusplus
interface IUIAnimationPrimitiveInterpolation;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTimer_FWD_DEFINED__
#define __IUIAnimationTimer_FWD_DEFINED__
typedef interface IUIAnimationTimer IUIAnimationTimer;
#ifdef __cplusplus
interface IUIAnimationTimer;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTimerUpdateHandler_FWD_DEFINED__
#define __IUIAnimationTimerUpdateHandler_FWD_DEFINED__
typedef interface IUIAnimationTimerUpdateHandler IUIAnimationTimerUpdateHandler;
#ifdef __cplusplus
interface IUIAnimationTimerUpdateHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTimerClientEventHandler_FWD_DEFINED__
#define __IUIAnimationTimerClientEventHandler_FWD_DEFINED__
typedef interface IUIAnimationTimerClientEventHandler IUIAnimationTimerClientEventHandler;
#ifdef __cplusplus
interface IUIAnimationTimerClientEventHandler;
#endif /* __cplusplus */
#endif

#ifndef __IUIAnimationTimerEventHandler_FWD_DEFINED__
#define __IUIAnimationTimerEventHandler_FWD_DEFINED__
typedef interface IUIAnimationTimerEventHandler IUIAnimationTimerEventHandler;
#ifdef __cplusplus
interface IUIAnimationTimerEventHandler;
#endif /* __cplusplus */
#endif

typedef DOUBLE UI_ANIMATION_SECONDS;
typedef enum __WIDL_uianimation_generated_name_0000000C {
    UI_ANIMATION_SCHEDULING_UNEXPECTED_FAILURE = 0,
    UI_ANIMATION_SCHEDULING_INSUFFICIENT_PRIORITY = 1,
    UI_ANIMATION_SCHEDULING_ALREADY_SCHEDULED = 2,
    UI_ANIMATION_SCHEDULING_SUCCEEDED = 3,
    UI_ANIMATION_SCHEDULING_DEFERRED = 4
} UI_ANIMATION_SCHEDULING_RESULT;
typedef enum __WIDL_uianimation_generated_name_0000000D {
    UI_ANIMATION_STORYBOARD_BUILDING = 0,
    UI_ANIMATION_STORYBOARD_SCHEDULED = 1,
    UI_ANIMATION_STORYBOARD_CANCELLED = 2,
    UI_ANIMATION_STORYBOARD_PLAYING = 3,
    UI_ANIMATION_STORYBOARD_TRUNCATED = 4,
    UI_ANIMATION_STORYBOARD_FINISHED = 5,
    UI_ANIMATION_STORYBOARD_READY = 6,
    UI_ANIMATION_STORYBOARD_INSUFFICIENT_PRIORITY = 7
} UI_ANIMATION_STORYBOARD_STATUS;
typedef enum __WIDL_uianimation_generated_name_0000000E {
    UI_ANIMATION_ROUNDING_NEAREST = 0,
    UI_ANIMATION_ROUNDING_FLOOR = 1,
    UI_ANIMATION_ROUNDING_CEILING = 2
} UI_ANIMATION_ROUNDING_MODE;
typedef enum __WIDL_uianimation_generated_name_0000000F {
    UI_ANIMATION_UPDATE_NO_CHANGE = 0,
    UI_ANIMATION_UPDATE_VARIABLES_CHANGED = 1
} UI_ANIMATION_UPDATE_RESULT;
typedef enum __WIDL_uianimation_generated_name_00000010 {
    UI_ANIMATION_MANAGER_IDLE = 0,
    UI_ANIMATION_MANAGER_BUSY = 1
} UI_ANIMATION_MANAGER_STATUS;
typedef enum __WIDL_uianimation_generated_name_00000011 {
    UI_ANIMATION_MODE_DISABLED = 0,
    UI_ANIMATION_MODE_SYSTEM_DEFAULT = 1,
    UI_ANIMATION_MODE_ENABLED = 2
} UI_ANIMATION_MODE;
typedef enum __WIDL_uianimation_generated_name_00000012 {
    UI_ANIMATION_PRIORITY_EFFECT_FAILURE = 0,
    UI_ANIMATION_PRIORITY_EFFECT_DELAY = 1
} UI_ANIMATION_PRIORITY_EFFECT;
typedef enum __WIDL_uianimation_generated_name_00000013 {
    UI_ANIMATION_REPEAT_MODE_NORMAL = 0,
    UI_ANIMATION_REPEAT_MODE_ALTERNATE = 1
} UI_ANIMATION_REPEAT_MODE;
typedef enum __WIDL_uianimation_generated_name_00000014 {
    UI_ANIMATION_SLOPE_INCREASING = 0,
    UI_ANIMATION_SLOPE_DECREASING = 1
} UI_ANIMATION_SLOPE;
typedef enum __WIDL_uianimation_generated_name_00000015 {
    UI_ANIMATION_DEPENDENCY_NONE = 0,
    UI_ANIMATION_DEPENDENCY_INTERMEDIATE_VALUES = 0x1,
    UI_ANIMATION_DEPENDENCY_FINAL_VALUE = 0x2,
    UI_ANIMATION_DEPENDENCY_FINAL_VELOCITY = 0x4,
    UI_ANIMATION_DEPENDENCY_DURATION = 0x8
} UI_ANIMATION_DEPENDENCIES;
typedef enum __WIDL_uianimation_generated_name_00000016 {
    UI_ANIMATION_TIMER_CLIENT_IDLE = 0,
    UI_ANIMATION_TIMER_CLIENT_BUSY = 1
} UI_ANIMATION_TIMER_CLIENT_STATUS;
typedef enum __WIDL_uianimation_generated_name_00000017 {
    UI_ANIMATION_IDLE_BEHAVIOR_CONTINUE = 0,
    UI_ANIMATION_IDLE_BEHAVIOR_DISABLE = 1
} UI_ANIMATION_IDLE_BEHAVIOR;
typedef struct __WIDL_uianimation_generated_name_00000018 {
    int _;
} *UI_ANIMATION_KEYFRAME;
#define UI_ANIMATION_KEYFRAME_STORYBOARD_START ((UI_ANIMATION_KEYFRAME)-1)

#define UI_ANIMATION_REPEAT_INDEFINITELY (-1)

#define UI_ANIMATION_REPEAT_INDEFINITELY_CONCLUDE_AT_END (UI_ANIMATION_REPEAT_INDEFINITELY)

#define UI_ANIMATION_REPEAT_INDEFINITELY_CONCLUDE_AT_START (-2)

#ifndef __uianimation_LIBRARY_DEFINED__
#define __uianimation_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_uianimation, 0x44ca24db, 0x1a92, 0x4149, 0xba,0xb5, 0xfb,0x14,0xd6,0x4b,0x40,0x1e);

/*****************************************************************************
 * UIAnimationManager coclass
 */

DEFINE_GUID(CLSID_UIAnimationManager, 0x4c1fc63a, 0x695c, 0x47e8, 0xa3,0x39, 0x1a,0x19,0x4b,0xe3,0xd0,0xb8);

#ifdef __cplusplus
class DECLSPEC_UUID("4c1fc63a-695c-47e8-a339-1a194be3d0b8") UIAnimationManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(UIAnimationManager, 0x4c1fc63a, 0x695c, 0x47e8, 0xa3,0x39, 0x1a,0x19,0x4b,0xe3,0xd0,0xb8)
#endif
#endif

/*****************************************************************************
 * UIAnimationManager2 coclass
 */

DEFINE_GUID(CLSID_UIAnimationManager2, 0xd25d8842, 0x8884, 0x4a4a, 0xb3,0x21, 0x09,0x13,0x14,0x37,0x9b,0xdd);

#ifdef __cplusplus
class DECLSPEC_UUID("d25d8842-8884-4a4a-b321-091314379bdd") UIAnimationManager2;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(UIAnimationManager2, 0xd25d8842, 0x8884, 0x4a4a, 0xb3,0x21, 0x09,0x13,0x14,0x37,0x9b,0xdd)
#endif
#endif

/*****************************************************************************
 * UIAnimationTransitionLibrary coclass
 */

DEFINE_GUID(CLSID_UIAnimationTransitionLibrary, 0x1d6322ad, 0xaa85, 0x4ef5, 0xa8,0x28, 0x86,0xd7,0x10,0x67,0xd1,0x45);

#ifdef __cplusplus
class DECLSPEC_UUID("1d6322ad-aa85-4ef5-a828-86d71067d145") UIAnimationTransitionLibrary;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(UIAnimationTransitionLibrary, 0x1d6322ad, 0xaa85, 0x4ef5, 0xa8,0x28, 0x86,0xd7,0x10,0x67,0xd1,0x45)
#endif
#endif

/*****************************************************************************
 * UIAnimationTransitionLibrary2 coclass
 */

DEFINE_GUID(CLSID_UIAnimationTransitionLibrary2, 0x812f944a, 0xc5c8, 0x4cd9, 0xb0,0xa6, 0xb3,0xda,0x80,0x2f,0x22,0x8d);

#ifdef __cplusplus
class DECLSPEC_UUID("812f944a-c5c8-4cd9-b0a6-b3da802f228d") UIAnimationTransitionLibrary2;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(UIAnimationTransitionLibrary2, 0x812f944a, 0xc5c8, 0x4cd9, 0xb0,0xa6, 0xb3,0xda,0x80,0x2f,0x22,0x8d)
#endif
#endif

/*****************************************************************************
 * UIAnimationTransitionFactory coclass
 */

DEFINE_GUID(CLSID_UIAnimationTransitionFactory, 0x8a9b1cdd, 0xfcd7, 0x419c, 0x8b,0x44, 0x42,0xfd,0x17,0xdb,0x18,0x87);

#ifdef __cplusplus
class DECLSPEC_UUID("8a9b1cdd-fcd7-419c-8b44-42fd17db1887") UIAnimationTransitionFactory;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(UIAnimationTransitionFactory, 0x8a9b1cdd, 0xfcd7, 0x419c, 0x8b,0x44, 0x42,0xfd,0x17,0xdb,0x18,0x87)
#endif
#endif

/*****************************************************************************
 * UIAnimationTransitionFactory2 coclass
 */

DEFINE_GUID(CLSID_UIAnimationTransitionFactory2, 0x84302f97, 0x7f7b, 0x4040, 0xb1,0x90, 0x72,0xac,0x9d,0x18,0xe4,0x20);

#ifdef __cplusplus
class DECLSPEC_UUID("84302f97-7f7b-4040-b190-72ac9d18e420") UIAnimationTransitionFactory2;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(UIAnimationTransitionFactory2, 0x84302f97, 0x7f7b, 0x4040, 0xb1,0x90, 0x72,0xac,0x9d,0x18,0xe4,0x20)
#endif
#endif

/*****************************************************************************
 * UIAnimationTimer coclass
 */

DEFINE_GUID(CLSID_UIAnimationTimer, 0xbfcd4a0c, 0x06b6, 0x4384, 0xb7,0x68, 0x0d,0xaa,0x79,0x2c,0x38,0x0e);

#ifdef __cplusplus
class DECLSPEC_UUID("bfcd4a0c-06b6-4384-b768-0daa792c380e") UIAnimationTimer;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(UIAnimationTimer, 0xbfcd4a0c, 0x06b6, 0x4384, 0xb7,0x68, 0x0d,0xaa,0x79,0x2c,0x38,0x0e)
#endif
#endif

#endif /* __uianimation_LIBRARY_DEFINED__ */
/*****************************************************************************
 * IUIAnimationManager interface
 */
#ifndef __IUIAnimationManager_INTERFACE_DEFINED__
#define __IUIAnimationManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationManager, 0x9169896c, 0xac8d, 0x4e7d, 0x94,0xe5, 0x67,0xfa,0x4d,0xc2,0xf2,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9169896c-ac8d-4e7d-94e5-67fa4dc2f2e8")
IUIAnimationManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateAnimationVariable(
        double initialValue,
        IUIAnimationVariable **variable) = 0;

    virtual HRESULT STDMETHODCALLTYPE ScheduleTransition(
        IUIAnimationVariable *variable,
        IUIAnimationTransition *transition,
        double timeNow) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateStoryboard(
        IUIAnimationStoryboard **storyboard) = 0;

    virtual HRESULT STDMETHODCALLTYPE FinishAllStoryboards(
        double completionDeadline) = 0;

    virtual HRESULT STDMETHODCALLTYPE AbandonAllStoryboards(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Update(
        double timeNow,
        UI_ANIMATION_UPDATE_RESULT *updateResult = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVariableFromTag(
        IUnknown *object,
        UINT32 id,
        IUIAnimationVariable **variable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStoryboardFromTag(
        IUnknown *object,
        UINT32 id,
        IUIAnimationStoryboard **storyboard) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        UI_ANIMATION_MANAGER_STATUS *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAnimationMode(
        UI_ANIMATION_MODE mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetManagerEventHandler(
        IUIAnimationManagerEventHandler *handler) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCancelPriorityComparison(
        IUIAnimationPriorityComparison *comparison) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTrimPriorityComparison(
        IUIAnimationPriorityComparison *comparison) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompressPriorityComparison(
        IUIAnimationPriorityComparison *comparison) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetConcludePriorityComparison(
        IUIAnimationPriorityComparison *comparison) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultLongestAcceptableDelay(
        double delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationManager, 0x9169896c, 0xac8d, 0x4e7d, 0x94,0xe5, 0x67,0xfa,0x4d,0xc2,0xf2,0xe8)
#endif
#else
typedef struct IUIAnimationManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationManager *This);

    /*** IUIAnimationManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateAnimationVariable)(
        IUIAnimationManager *This,
        double initialValue,
        IUIAnimationVariable **variable);

    HRESULT (STDMETHODCALLTYPE *ScheduleTransition)(
        IUIAnimationManager *This,
        IUIAnimationVariable *variable,
        IUIAnimationTransition *transition,
        double timeNow);

    HRESULT (STDMETHODCALLTYPE *CreateStoryboard)(
        IUIAnimationManager *This,
        IUIAnimationStoryboard **storyboard);

    HRESULT (STDMETHODCALLTYPE *FinishAllStoryboards)(
        IUIAnimationManager *This,
        double completionDeadline);

    HRESULT (STDMETHODCALLTYPE *AbandonAllStoryboards)(
        IUIAnimationManager *This);

    HRESULT (STDMETHODCALLTYPE *Update)(
        IUIAnimationManager *This,
        double timeNow,
        UI_ANIMATION_UPDATE_RESULT *updateResult);

    HRESULT (STDMETHODCALLTYPE *GetVariableFromTag)(
        IUIAnimationManager *This,
        IUnknown *object,
        UINT32 id,
        IUIAnimationVariable **variable);

    HRESULT (STDMETHODCALLTYPE *GetStoryboardFromTag)(
        IUIAnimationManager *This,
        IUnknown *object,
        UINT32 id,
        IUIAnimationStoryboard **storyboard);

    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IUIAnimationManager *This,
        UI_ANIMATION_MANAGER_STATUS *status);

    HRESULT (STDMETHODCALLTYPE *SetAnimationMode)(
        IUIAnimationManager *This,
        UI_ANIMATION_MODE mode);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IUIAnimationManager *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IUIAnimationManager *This);

    HRESULT (STDMETHODCALLTYPE *SetManagerEventHandler)(
        IUIAnimationManager *This,
        IUIAnimationManagerEventHandler *handler);

    HRESULT (STDMETHODCALLTYPE *SetCancelPriorityComparison)(
        IUIAnimationManager *This,
        IUIAnimationPriorityComparison *comparison);

    HRESULT (STDMETHODCALLTYPE *SetTrimPriorityComparison)(
        IUIAnimationManager *This,
        IUIAnimationPriorityComparison *comparison);

    HRESULT (STDMETHODCALLTYPE *SetCompressPriorityComparison)(
        IUIAnimationManager *This,
        IUIAnimationPriorityComparison *comparison);

    HRESULT (STDMETHODCALLTYPE *SetConcludePriorityComparison)(
        IUIAnimationManager *This,
        IUIAnimationPriorityComparison *comparison);

    HRESULT (STDMETHODCALLTYPE *SetDefaultLongestAcceptableDelay)(
        IUIAnimationManager *This,
        double delay);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IUIAnimationManager *This);

    END_INTERFACE
} IUIAnimationManagerVtbl;

interface IUIAnimationManager {
    CONST_VTBL IUIAnimationManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationManager_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationManager methods ***/
#define IUIAnimationManager_CreateAnimationVariable(This,initialValue,variable) (This)->lpVtbl->CreateAnimationVariable(This,initialValue,variable)
#define IUIAnimationManager_ScheduleTransition(This,variable,transition,timeNow) (This)->lpVtbl->ScheduleTransition(This,variable,transition,timeNow)
#define IUIAnimationManager_CreateStoryboard(This,storyboard) (This)->lpVtbl->CreateStoryboard(This,storyboard)
#define IUIAnimationManager_FinishAllStoryboards(This,completionDeadline) (This)->lpVtbl->FinishAllStoryboards(This,completionDeadline)
#define IUIAnimationManager_AbandonAllStoryboards(This) (This)->lpVtbl->AbandonAllStoryboards(This)
#define IUIAnimationManager_Update(This,timeNow,updateResult) (This)->lpVtbl->Update(This,timeNow,updateResult)
#define IUIAnimationManager_GetVariableFromTag(This,object,id,variable) (This)->lpVtbl->GetVariableFromTag(This,object,id,variable)
#define IUIAnimationManager_GetStoryboardFromTag(This,object,id,storyboard) (This)->lpVtbl->GetStoryboardFromTag(This,object,id,storyboard)
#define IUIAnimationManager_GetStatus(This,status) (This)->lpVtbl->GetStatus(This,status)
#define IUIAnimationManager_SetAnimationMode(This,mode) (This)->lpVtbl->SetAnimationMode(This,mode)
#define IUIAnimationManager_Pause(This) (This)->lpVtbl->Pause(This)
#define IUIAnimationManager_Resume(This) (This)->lpVtbl->Resume(This)
#define IUIAnimationManager_SetManagerEventHandler(This,handler) (This)->lpVtbl->SetManagerEventHandler(This,handler)
#define IUIAnimationManager_SetCancelPriorityComparison(This,comparison) (This)->lpVtbl->SetCancelPriorityComparison(This,comparison)
#define IUIAnimationManager_SetTrimPriorityComparison(This,comparison) (This)->lpVtbl->SetTrimPriorityComparison(This,comparison)
#define IUIAnimationManager_SetCompressPriorityComparison(This,comparison) (This)->lpVtbl->SetCompressPriorityComparison(This,comparison)
#define IUIAnimationManager_SetConcludePriorityComparison(This,comparison) (This)->lpVtbl->SetConcludePriorityComparison(This,comparison)
#define IUIAnimationManager_SetDefaultLongestAcceptableDelay(This,delay) (This)->lpVtbl->SetDefaultLongestAcceptableDelay(This,delay)
#define IUIAnimationManager_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationManager_QueryInterface(IUIAnimationManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationManager_AddRef(IUIAnimationManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationManager_Release(IUIAnimationManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationManager methods ***/
static inline HRESULT IUIAnimationManager_CreateAnimationVariable(IUIAnimationManager* This,double initialValue,IUIAnimationVariable **variable) {
    return This->lpVtbl->CreateAnimationVariable(This,initialValue,variable);
}
static inline HRESULT IUIAnimationManager_ScheduleTransition(IUIAnimationManager* This,IUIAnimationVariable *variable,IUIAnimationTransition *transition,double timeNow) {
    return This->lpVtbl->ScheduleTransition(This,variable,transition,timeNow);
}
static inline HRESULT IUIAnimationManager_CreateStoryboard(IUIAnimationManager* This,IUIAnimationStoryboard **storyboard) {
    return This->lpVtbl->CreateStoryboard(This,storyboard);
}
static inline HRESULT IUIAnimationManager_FinishAllStoryboards(IUIAnimationManager* This,double completionDeadline) {
    return This->lpVtbl->FinishAllStoryboards(This,completionDeadline);
}
static inline HRESULT IUIAnimationManager_AbandonAllStoryboards(IUIAnimationManager* This) {
    return This->lpVtbl->AbandonAllStoryboards(This);
}
static inline HRESULT IUIAnimationManager_Update(IUIAnimationManager* This,double timeNow,UI_ANIMATION_UPDATE_RESULT *updateResult) {
    return This->lpVtbl->Update(This,timeNow,updateResult);
}
static inline HRESULT IUIAnimationManager_GetVariableFromTag(IUIAnimationManager* This,IUnknown *object,UINT32 id,IUIAnimationVariable **variable) {
    return This->lpVtbl->GetVariableFromTag(This,object,id,variable);
}
static inline HRESULT IUIAnimationManager_GetStoryboardFromTag(IUIAnimationManager* This,IUnknown *object,UINT32 id,IUIAnimationStoryboard **storyboard) {
    return This->lpVtbl->GetStoryboardFromTag(This,object,id,storyboard);
}
static inline HRESULT IUIAnimationManager_GetStatus(IUIAnimationManager* This,UI_ANIMATION_MANAGER_STATUS *status) {
    return This->lpVtbl->GetStatus(This,status);
}
static inline HRESULT IUIAnimationManager_SetAnimationMode(IUIAnimationManager* This,UI_ANIMATION_MODE mode) {
    return This->lpVtbl->SetAnimationMode(This,mode);
}
static inline HRESULT IUIAnimationManager_Pause(IUIAnimationManager* This) {
    return This->lpVtbl->Pause(This);
}
static inline HRESULT IUIAnimationManager_Resume(IUIAnimationManager* This) {
    return This->lpVtbl->Resume(This);
}
static inline HRESULT IUIAnimationManager_SetManagerEventHandler(IUIAnimationManager* This,IUIAnimationManagerEventHandler *handler) {
    return This->lpVtbl->SetManagerEventHandler(This,handler);
}
static inline HRESULT IUIAnimationManager_SetCancelPriorityComparison(IUIAnimationManager* This,IUIAnimationPriorityComparison *comparison) {
    return This->lpVtbl->SetCancelPriorityComparison(This,comparison);
}
static inline HRESULT IUIAnimationManager_SetTrimPriorityComparison(IUIAnimationManager* This,IUIAnimationPriorityComparison *comparison) {
    return This->lpVtbl->SetTrimPriorityComparison(This,comparison);
}
static inline HRESULT IUIAnimationManager_SetCompressPriorityComparison(IUIAnimationManager* This,IUIAnimationPriorityComparison *comparison) {
    return This->lpVtbl->SetCompressPriorityComparison(This,comparison);
}
static inline HRESULT IUIAnimationManager_SetConcludePriorityComparison(IUIAnimationManager* This,IUIAnimationPriorityComparison *comparison) {
    return This->lpVtbl->SetConcludePriorityComparison(This,comparison);
}
static inline HRESULT IUIAnimationManager_SetDefaultLongestAcceptableDelay(IUIAnimationManager* This,double delay) {
    return This->lpVtbl->SetDefaultLongestAcceptableDelay(This,delay);
}
static inline HRESULT IUIAnimationManager_Shutdown(IUIAnimationManager* This) {
    return This->lpVtbl->Shutdown(This);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationVariable interface
 */
#ifndef __IUIAnimationVariable_INTERFACE_DEFINED__
#define __IUIAnimationVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationVariable, 0x8ceeb155, 0x2849, 0x4ce5, 0x94,0x48, 0x91,0xff,0x70,0xe1,0xe4,0xd9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8ceeb155-2849-4ce5-9448-91ff70e1e4d9")
IUIAnimationVariable : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetValue(
        double *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFinalValue(
        double *finalValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreviousValue(
        double *previousValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIntegerValue(
        int *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFinalIntegerValue(
        int *finalValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreviousIntegerValue(
        int *previousValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentStoryboard(
        IUIAnimationStoryboard **storyboard) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLowerBound(
        double bound) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUpperBound(
        double bound) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRoundingMode(
        UI_ANIMATION_ROUNDING_MODE mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTag(
        IUnknown *object,
        unsigned int id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTag(
        IUnknown **object,
        unsigned int *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVariableChangeHandler(
        IUIAnimationVariableChangeHandler *handler) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVariableIntegerChangeHandler(
        IUIAnimationVariableIntegerChangeHandler *handler) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationVariable, 0x8ceeb155, 0x2849, 0x4ce5, 0x94,0x48, 0x91,0xff,0x70,0xe1,0xe4,0xd9)
#endif
#else
typedef struct IUIAnimationVariableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationVariable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationVariable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationVariable *This);

    /*** IUIAnimationVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IUIAnimationVariable *This,
        double *value);

    HRESULT (STDMETHODCALLTYPE *GetFinalValue)(
        IUIAnimationVariable *This,
        double *finalValue);

    HRESULT (STDMETHODCALLTYPE *GetPreviousValue)(
        IUIAnimationVariable *This,
        double *previousValue);

    HRESULT (STDMETHODCALLTYPE *GetIntegerValue)(
        IUIAnimationVariable *This,
        int *value);

    HRESULT (STDMETHODCALLTYPE *GetFinalIntegerValue)(
        IUIAnimationVariable *This,
        int *finalValue);

    HRESULT (STDMETHODCALLTYPE *GetPreviousIntegerValue)(
        IUIAnimationVariable *This,
        int *previousValue);

    HRESULT (STDMETHODCALLTYPE *GetCurrentStoryboard)(
        IUIAnimationVariable *This,
        IUIAnimationStoryboard **storyboard);

    HRESULT (STDMETHODCALLTYPE *SetLowerBound)(
        IUIAnimationVariable *This,
        double bound);

    HRESULT (STDMETHODCALLTYPE *SetUpperBound)(
        IUIAnimationVariable *This,
        double bound);

    HRESULT (STDMETHODCALLTYPE *SetRoundingMode)(
        IUIAnimationVariable *This,
        UI_ANIMATION_ROUNDING_MODE mode);

    HRESULT (STDMETHODCALLTYPE *SetTag)(
        IUIAnimationVariable *This,
        IUnknown *object,
        unsigned int id);

    HRESULT (STDMETHODCALLTYPE *GetTag)(
        IUIAnimationVariable *This,
        IUnknown **object,
        unsigned int *id);

    HRESULT (STDMETHODCALLTYPE *SetVariableChangeHandler)(
        IUIAnimationVariable *This,
        IUIAnimationVariableChangeHandler *handler);

    HRESULT (STDMETHODCALLTYPE *SetVariableIntegerChangeHandler)(
        IUIAnimationVariable *This,
        IUIAnimationVariableIntegerChangeHandler *handler);

    END_INTERFACE
} IUIAnimationVariableVtbl;

interface IUIAnimationVariable {
    CONST_VTBL IUIAnimationVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationVariable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationVariable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationVariable_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationVariable methods ***/
#define IUIAnimationVariable_GetValue(This,value) (This)->lpVtbl->GetValue(This,value)
#define IUIAnimationVariable_GetFinalValue(This,finalValue) (This)->lpVtbl->GetFinalValue(This,finalValue)
#define IUIAnimationVariable_GetPreviousValue(This,previousValue) (This)->lpVtbl->GetPreviousValue(This,previousValue)
#define IUIAnimationVariable_GetIntegerValue(This,value) (This)->lpVtbl->GetIntegerValue(This,value)
#define IUIAnimationVariable_GetFinalIntegerValue(This,finalValue) (This)->lpVtbl->GetFinalIntegerValue(This,finalValue)
#define IUIAnimationVariable_GetPreviousIntegerValue(This,previousValue) (This)->lpVtbl->GetPreviousIntegerValue(This,previousValue)
#define IUIAnimationVariable_GetCurrentStoryboard(This,storyboard) (This)->lpVtbl->GetCurrentStoryboard(This,storyboard)
#define IUIAnimationVariable_SetLowerBound(This,bound) (This)->lpVtbl->SetLowerBound(This,bound)
#define IUIAnimationVariable_SetUpperBound(This,bound) (This)->lpVtbl->SetUpperBound(This,bound)
#define IUIAnimationVariable_SetRoundingMode(This,mode) (This)->lpVtbl->SetRoundingMode(This,mode)
#define IUIAnimationVariable_SetTag(This,object,id) (This)->lpVtbl->SetTag(This,object,id)
#define IUIAnimationVariable_GetTag(This,object,id) (This)->lpVtbl->GetTag(This,object,id)
#define IUIAnimationVariable_SetVariableChangeHandler(This,handler) (This)->lpVtbl->SetVariableChangeHandler(This,handler)
#define IUIAnimationVariable_SetVariableIntegerChangeHandler(This,handler) (This)->lpVtbl->SetVariableIntegerChangeHandler(This,handler)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationVariable_QueryInterface(IUIAnimationVariable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationVariable_AddRef(IUIAnimationVariable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationVariable_Release(IUIAnimationVariable* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationVariable methods ***/
static inline HRESULT IUIAnimationVariable_GetValue(IUIAnimationVariable* This,double *value) {
    return This->lpVtbl->GetValue(This,value);
}
static inline HRESULT IUIAnimationVariable_GetFinalValue(IUIAnimationVariable* This,double *finalValue) {
    return This->lpVtbl->GetFinalValue(This,finalValue);
}
static inline HRESULT IUIAnimationVariable_GetPreviousValue(IUIAnimationVariable* This,double *previousValue) {
    return This->lpVtbl->GetPreviousValue(This,previousValue);
}
static inline HRESULT IUIAnimationVariable_GetIntegerValue(IUIAnimationVariable* This,int *value) {
    return This->lpVtbl->GetIntegerValue(This,value);
}
static inline HRESULT IUIAnimationVariable_GetFinalIntegerValue(IUIAnimationVariable* This,int *finalValue) {
    return This->lpVtbl->GetFinalIntegerValue(This,finalValue);
}
static inline HRESULT IUIAnimationVariable_GetPreviousIntegerValue(IUIAnimationVariable* This,int *previousValue) {
    return This->lpVtbl->GetPreviousIntegerValue(This,previousValue);
}
static inline HRESULT IUIAnimationVariable_GetCurrentStoryboard(IUIAnimationVariable* This,IUIAnimationStoryboard **storyboard) {
    return This->lpVtbl->GetCurrentStoryboard(This,storyboard);
}
static inline HRESULT IUIAnimationVariable_SetLowerBound(IUIAnimationVariable* This,double bound) {
    return This->lpVtbl->SetLowerBound(This,bound);
}
static inline HRESULT IUIAnimationVariable_SetUpperBound(IUIAnimationVariable* This,double bound) {
    return This->lpVtbl->SetUpperBound(This,bound);
}
static inline HRESULT IUIAnimationVariable_SetRoundingMode(IUIAnimationVariable* This,UI_ANIMATION_ROUNDING_MODE mode) {
    return This->lpVtbl->SetRoundingMode(This,mode);
}
static inline HRESULT IUIAnimationVariable_SetTag(IUIAnimationVariable* This,IUnknown *object,unsigned int id) {
    return This->lpVtbl->SetTag(This,object,id);
}
static inline HRESULT IUIAnimationVariable_GetTag(IUIAnimationVariable* This,IUnknown **object,unsigned int *id) {
    return This->lpVtbl->GetTag(This,object,id);
}
static inline HRESULT IUIAnimationVariable_SetVariableChangeHandler(IUIAnimationVariable* This,IUIAnimationVariableChangeHandler *handler) {
    return This->lpVtbl->SetVariableChangeHandler(This,handler);
}
static inline HRESULT IUIAnimationVariable_SetVariableIntegerChangeHandler(IUIAnimationVariable* This,IUIAnimationVariableIntegerChangeHandler *handler) {
    return This->lpVtbl->SetVariableIntegerChangeHandler(This,handler);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationStoryboard interface
 */
#ifndef __IUIAnimationStoryboard_INTERFACE_DEFINED__
#define __IUIAnimationStoryboard_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationStoryboard, 0xa8ff128f, 0x9bf9, 0x4af1, 0x9e,0x67, 0xe5,0xe4,0x10,0xde,0xfb,0x84);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a8ff128f-9bf9-4af1-9e67-e5e410defb84")
IUIAnimationStoryboard : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddTransition(
        IUIAnimationVariable *variable,
        IUIAnimationTransition *transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddKeyframeAtOffset(
        UI_ANIMATION_KEYFRAME existingKeyframe,
        double offset,
        UI_ANIMATION_KEYFRAME *keyframe) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddKeyframeAfterTransition(
        IUIAnimationTransition *transition,
        UI_ANIMATION_KEYFRAME *keyframe) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddTransitionAtKeyframe(
        IUIAnimationVariable *variable,
        IUIAnimationTransition *transition,
        UI_ANIMATION_KEYFRAME startKeyframe) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddTransitionBetweenKeyframes(
        IUIAnimationVariable *variable,
        IUIAnimationTransition *transition,
        UI_ANIMATION_KEYFRAME startKeyframe,
        UI_ANIMATION_KEYFRAME endKeyframe) = 0;

    virtual HRESULT STDMETHODCALLTYPE RepeatBetweenKeyframes(
        UI_ANIMATION_KEYFRAME startKeyframe,
        UI_ANIMATION_KEYFRAME endKeyframe,
        int repetitionCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE HoldVariable(
        IUIAnimationVariable *variable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLongestAcceptableDelay(
        double delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE Schedule(
        double timeNow,
        UI_ANIMATION_SCHEDULING_RESULT *schedulingResult = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Conclude(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Finish(
        double completionDeadline) = 0;

    virtual HRESULT STDMETHODCALLTYPE Abandon(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTag(
        IUnknown *object,
        unsigned int id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTag(
        IUnknown **object,
        unsigned int *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        UI_ANIMATION_STORYBOARD_STATUS *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetElapsedTime(
        double *elapsedTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStoryboardEventHandler(
        IUIAnimationStoryboardEventHandler *handler) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationStoryboard, 0xa8ff128f, 0x9bf9, 0x4af1, 0x9e,0x67, 0xe5,0xe4,0x10,0xde,0xfb,0x84)
#endif
#else
typedef struct IUIAnimationStoryboardVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationStoryboard *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationStoryboard *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationStoryboard *This);

    /*** IUIAnimationStoryboard methods ***/
    HRESULT (STDMETHODCALLTYPE *AddTransition)(
        IUIAnimationStoryboard *This,
        IUIAnimationVariable *variable,
        IUIAnimationTransition *transition);

    HRESULT (STDMETHODCALLTYPE *AddKeyframeAtOffset)(
        IUIAnimationStoryboard *This,
        UI_ANIMATION_KEYFRAME existingKeyframe,
        double offset,
        UI_ANIMATION_KEYFRAME *keyframe);

    HRESULT (STDMETHODCALLTYPE *AddKeyframeAfterTransition)(
        IUIAnimationStoryboard *This,
        IUIAnimationTransition *transition,
        UI_ANIMATION_KEYFRAME *keyframe);

    HRESULT (STDMETHODCALLTYPE *AddTransitionAtKeyframe)(
        IUIAnimationStoryboard *This,
        IUIAnimationVariable *variable,
        IUIAnimationTransition *transition,
        UI_ANIMATION_KEYFRAME startKeyframe);

    HRESULT (STDMETHODCALLTYPE *AddTransitionBetweenKeyframes)(
        IUIAnimationStoryboard *This,
        IUIAnimationVariable *variable,
        IUIAnimationTransition *transition,
        UI_ANIMATION_KEYFRAME startKeyframe,
        UI_ANIMATION_KEYFRAME endKeyframe);

    HRESULT (STDMETHODCALLTYPE *RepeatBetweenKeyframes)(
        IUIAnimationStoryboard *This,
        UI_ANIMATION_KEYFRAME startKeyframe,
        UI_ANIMATION_KEYFRAME endKeyframe,
        int repetitionCount);

    HRESULT (STDMETHODCALLTYPE *HoldVariable)(
        IUIAnimationStoryboard *This,
        IUIAnimationVariable *variable);

    HRESULT (STDMETHODCALLTYPE *SetLongestAcceptableDelay)(
        IUIAnimationStoryboard *This,
        double delay);

    HRESULT (STDMETHODCALLTYPE *Schedule)(
        IUIAnimationStoryboard *This,
        double timeNow,
        UI_ANIMATION_SCHEDULING_RESULT *schedulingResult);

    HRESULT (STDMETHODCALLTYPE *Conclude)(
        IUIAnimationStoryboard *This);

    HRESULT (STDMETHODCALLTYPE *Finish)(
        IUIAnimationStoryboard *This,
        double completionDeadline);

    HRESULT (STDMETHODCALLTYPE *Abandon)(
        IUIAnimationStoryboard *This);

    HRESULT (STDMETHODCALLTYPE *SetTag)(
        IUIAnimationStoryboard *This,
        IUnknown *object,
        unsigned int id);

    HRESULT (STDMETHODCALLTYPE *GetTag)(
        IUIAnimationStoryboard *This,
        IUnknown **object,
        unsigned int *id);

    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IUIAnimationStoryboard *This,
        UI_ANIMATION_STORYBOARD_STATUS *status);

    HRESULT (STDMETHODCALLTYPE *GetElapsedTime)(
        IUIAnimationStoryboard *This,
        double *elapsedTime);

    HRESULT (STDMETHODCALLTYPE *SetStoryboardEventHandler)(
        IUIAnimationStoryboard *This,
        IUIAnimationStoryboardEventHandler *handler);

    END_INTERFACE
} IUIAnimationStoryboardVtbl;

interface IUIAnimationStoryboard {
    CONST_VTBL IUIAnimationStoryboardVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationStoryboard_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationStoryboard_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationStoryboard_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationStoryboard methods ***/
#define IUIAnimationStoryboard_AddTransition(This,variable,transition) (This)->lpVtbl->AddTransition(This,variable,transition)
#define IUIAnimationStoryboard_AddKeyframeAtOffset(This,existingKeyframe,offset,keyframe) (This)->lpVtbl->AddKeyframeAtOffset(This,existingKeyframe,offset,keyframe)
#define IUIAnimationStoryboard_AddKeyframeAfterTransition(This,transition,keyframe) (This)->lpVtbl->AddKeyframeAfterTransition(This,transition,keyframe)
#define IUIAnimationStoryboard_AddTransitionAtKeyframe(This,variable,transition,startKeyframe) (This)->lpVtbl->AddTransitionAtKeyframe(This,variable,transition,startKeyframe)
#define IUIAnimationStoryboard_AddTransitionBetweenKeyframes(This,variable,transition,startKeyframe,endKeyframe) (This)->lpVtbl->AddTransitionBetweenKeyframes(This,variable,transition,startKeyframe,endKeyframe)
#define IUIAnimationStoryboard_RepeatBetweenKeyframes(This,startKeyframe,endKeyframe,repetitionCount) (This)->lpVtbl->RepeatBetweenKeyframes(This,startKeyframe,endKeyframe,repetitionCount)
#define IUIAnimationStoryboard_HoldVariable(This,variable) (This)->lpVtbl->HoldVariable(This,variable)
#define IUIAnimationStoryboard_SetLongestAcceptableDelay(This,delay) (This)->lpVtbl->SetLongestAcceptableDelay(This,delay)
#define IUIAnimationStoryboard_Schedule(This,timeNow,schedulingResult) (This)->lpVtbl->Schedule(This,timeNow,schedulingResult)
#define IUIAnimationStoryboard_Conclude(This) (This)->lpVtbl->Conclude(This)
#define IUIAnimationStoryboard_Finish(This,completionDeadline) (This)->lpVtbl->Finish(This,completionDeadline)
#define IUIAnimationStoryboard_Abandon(This) (This)->lpVtbl->Abandon(This)
#define IUIAnimationStoryboard_SetTag(This,object,id) (This)->lpVtbl->SetTag(This,object,id)
#define IUIAnimationStoryboard_GetTag(This,object,id) (This)->lpVtbl->GetTag(This,object,id)
#define IUIAnimationStoryboard_GetStatus(This,status) (This)->lpVtbl->GetStatus(This,status)
#define IUIAnimationStoryboard_GetElapsedTime(This,elapsedTime) (This)->lpVtbl->GetElapsedTime(This,elapsedTime)
#define IUIAnimationStoryboard_SetStoryboardEventHandler(This,handler) (This)->lpVtbl->SetStoryboardEventHandler(This,handler)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationStoryboard_QueryInterface(IUIAnimationStoryboard* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationStoryboard_AddRef(IUIAnimationStoryboard* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationStoryboard_Release(IUIAnimationStoryboard* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationStoryboard methods ***/
static inline HRESULT IUIAnimationStoryboard_AddTransition(IUIAnimationStoryboard* This,IUIAnimationVariable *variable,IUIAnimationTransition *transition) {
    return This->lpVtbl->AddTransition(This,variable,transition);
}
static inline HRESULT IUIAnimationStoryboard_AddKeyframeAtOffset(IUIAnimationStoryboard* This,UI_ANIMATION_KEYFRAME existingKeyframe,double offset,UI_ANIMATION_KEYFRAME *keyframe) {
    return This->lpVtbl->AddKeyframeAtOffset(This,existingKeyframe,offset,keyframe);
}
static inline HRESULT IUIAnimationStoryboard_AddKeyframeAfterTransition(IUIAnimationStoryboard* This,IUIAnimationTransition *transition,UI_ANIMATION_KEYFRAME *keyframe) {
    return This->lpVtbl->AddKeyframeAfterTransition(This,transition,keyframe);
}
static inline HRESULT IUIAnimationStoryboard_AddTransitionAtKeyframe(IUIAnimationStoryboard* This,IUIAnimationVariable *variable,IUIAnimationTransition *transition,UI_ANIMATION_KEYFRAME startKeyframe) {
    return This->lpVtbl->AddTransitionAtKeyframe(This,variable,transition,startKeyframe);
}
static inline HRESULT IUIAnimationStoryboard_AddTransitionBetweenKeyframes(IUIAnimationStoryboard* This,IUIAnimationVariable *variable,IUIAnimationTransition *transition,UI_ANIMATION_KEYFRAME startKeyframe,UI_ANIMATION_KEYFRAME endKeyframe) {
    return This->lpVtbl->AddTransitionBetweenKeyframes(This,variable,transition,startKeyframe,endKeyframe);
}
static inline HRESULT IUIAnimationStoryboard_RepeatBetweenKeyframes(IUIAnimationStoryboard* This,UI_ANIMATION_KEYFRAME startKeyframe,UI_ANIMATION_KEYFRAME endKeyframe,int repetitionCount) {
    return This->lpVtbl->RepeatBetweenKeyframes(This,startKeyframe,endKeyframe,repetitionCount);
}
static inline HRESULT IUIAnimationStoryboard_HoldVariable(IUIAnimationStoryboard* This,IUIAnimationVariable *variable) {
    return This->lpVtbl->HoldVariable(This,variable);
}
static inline HRESULT IUIAnimationStoryboard_SetLongestAcceptableDelay(IUIAnimationStoryboard* This,double delay) {
    return This->lpVtbl->SetLongestAcceptableDelay(This,delay);
}
static inline HRESULT IUIAnimationStoryboard_Schedule(IUIAnimationStoryboard* This,double timeNow,UI_ANIMATION_SCHEDULING_RESULT *schedulingResult) {
    return This->lpVtbl->Schedule(This,timeNow,schedulingResult);
}
static inline HRESULT IUIAnimationStoryboard_Conclude(IUIAnimationStoryboard* This) {
    return This->lpVtbl->Conclude(This);
}
static inline HRESULT IUIAnimationStoryboard_Finish(IUIAnimationStoryboard* This,double completionDeadline) {
    return This->lpVtbl->Finish(This,completionDeadline);
}
static inline HRESULT IUIAnimationStoryboard_Abandon(IUIAnimationStoryboard* This) {
    return This->lpVtbl->Abandon(This);
}
static inline HRESULT IUIAnimationStoryboard_SetTag(IUIAnimationStoryboard* This,IUnknown *object,unsigned int id) {
    return This->lpVtbl->SetTag(This,object,id);
}
static inline HRESULT IUIAnimationStoryboard_GetTag(IUIAnimationStoryboard* This,IUnknown **object,unsigned int *id) {
    return This->lpVtbl->GetTag(This,object,id);
}
static inline HRESULT IUIAnimationStoryboard_GetStatus(IUIAnimationStoryboard* This,UI_ANIMATION_STORYBOARD_STATUS *status) {
    return This->lpVtbl->GetStatus(This,status);
}
static inline HRESULT IUIAnimationStoryboard_GetElapsedTime(IUIAnimationStoryboard* This,double *elapsedTime) {
    return This->lpVtbl->GetElapsedTime(This,elapsedTime);
}
static inline HRESULT IUIAnimationStoryboard_SetStoryboardEventHandler(IUIAnimationStoryboard* This,IUIAnimationStoryboardEventHandler *handler) {
    return This->lpVtbl->SetStoryboardEventHandler(This,handler);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationStoryboard_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationTransition interface
 */
#ifndef __IUIAnimationTransition_INTERFACE_DEFINED__
#define __IUIAnimationTransition_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationTransition, 0xdc6ce252, 0xf731, 0x41cf, 0xb6,0x10, 0x61,0x4b,0x6c,0xa0,0x49,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dc6ce252-f731-41cf-b610-614b6ca049ad")
IUIAnimationTransition : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetInitialValue(
        double value) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInitialVelocity(
        double velocity) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsDurationKnown(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDuration(
        double *duration) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationTransition, 0xdc6ce252, 0xf731, 0x41cf, 0xb6,0x10, 0x61,0x4b,0x6c,0xa0,0x49,0xad)
#endif
#else
typedef struct IUIAnimationTransitionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationTransition *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationTransition *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationTransition *This);

    /*** IUIAnimationTransition methods ***/
    HRESULT (STDMETHODCALLTYPE *SetInitialValue)(
        IUIAnimationTransition *This,
        double value);

    HRESULT (STDMETHODCALLTYPE *SetInitialVelocity)(
        IUIAnimationTransition *This,
        double velocity);

    HRESULT (STDMETHODCALLTYPE *IsDurationKnown)(
        IUIAnimationTransition *This);

    HRESULT (STDMETHODCALLTYPE *GetDuration)(
        IUIAnimationTransition *This,
        double *duration);

    END_INTERFACE
} IUIAnimationTransitionVtbl;

interface IUIAnimationTransition {
    CONST_VTBL IUIAnimationTransitionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationTransition_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationTransition_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationTransition_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationTransition methods ***/
#define IUIAnimationTransition_SetInitialValue(This,value) (This)->lpVtbl->SetInitialValue(This,value)
#define IUIAnimationTransition_SetInitialVelocity(This,velocity) (This)->lpVtbl->SetInitialVelocity(This,velocity)
#define IUIAnimationTransition_IsDurationKnown(This) (This)->lpVtbl->IsDurationKnown(This)
#define IUIAnimationTransition_GetDuration(This,duration) (This)->lpVtbl->GetDuration(This,duration)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationTransition_QueryInterface(IUIAnimationTransition* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationTransition_AddRef(IUIAnimationTransition* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationTransition_Release(IUIAnimationTransition* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationTransition methods ***/
static inline HRESULT IUIAnimationTransition_SetInitialValue(IUIAnimationTransition* This,double value) {
    return This->lpVtbl->SetInitialValue(This,value);
}
static inline HRESULT IUIAnimationTransition_SetInitialVelocity(IUIAnimationTransition* This,double velocity) {
    return This->lpVtbl->SetInitialVelocity(This,velocity);
}
static inline HRESULT IUIAnimationTransition_IsDurationKnown(IUIAnimationTransition* This) {
    return This->lpVtbl->IsDurationKnown(This);
}
static inline HRESULT IUIAnimationTransition_GetDuration(IUIAnimationTransition* This,double *duration) {
    return This->lpVtbl->GetDuration(This,duration);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationTransition_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationStoryboardEventHandler interface
 */
#ifndef __IUIAnimationStoryboardEventHandler_INTERFACE_DEFINED__
#define __IUIAnimationStoryboardEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationStoryboardEventHandler, 0x3d5c9008, 0xec7c, 0x4364, 0x9f,0x8a, 0x9a,0xf3,0xc5,0x8c,0xba,0xe6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3d5c9008-ec7c-4364-9f8a-9af3c58cbae6")
IUIAnimationStoryboardEventHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnStoryboardStatusChanged(
        IUIAnimationStoryboard *storyboard,
        UI_ANIMATION_STORYBOARD_STATUS newStatus,
        UI_ANIMATION_STORYBOARD_STATUS previousStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnStoryboardUpdated(
        IUIAnimationStoryboard *storyboard) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationStoryboardEventHandler, 0x3d5c9008, 0xec7c, 0x4364, 0x9f,0x8a, 0x9a,0xf3,0xc5,0x8c,0xba,0xe6)
#endif
#else
typedef struct IUIAnimationStoryboardEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationStoryboardEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationStoryboardEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationStoryboardEventHandler *This);

    /*** IUIAnimationStoryboardEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *OnStoryboardStatusChanged)(
        IUIAnimationStoryboardEventHandler *This,
        IUIAnimationStoryboard *storyboard,
        UI_ANIMATION_STORYBOARD_STATUS newStatus,
        UI_ANIMATION_STORYBOARD_STATUS previousStatus);

    HRESULT (STDMETHODCALLTYPE *OnStoryboardUpdated)(
        IUIAnimationStoryboardEventHandler *This,
        IUIAnimationStoryboard *storyboard);

    END_INTERFACE
} IUIAnimationStoryboardEventHandlerVtbl;

interface IUIAnimationStoryboardEventHandler {
    CONST_VTBL IUIAnimationStoryboardEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationStoryboardEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationStoryboardEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationStoryboardEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationStoryboardEventHandler methods ***/
#define IUIAnimationStoryboardEventHandler_OnStoryboardStatusChanged(This,storyboard,newStatus,previousStatus) (This)->lpVtbl->OnStoryboardStatusChanged(This,storyboard,newStatus,previousStatus)
#define IUIAnimationStoryboardEventHandler_OnStoryboardUpdated(This,storyboard) (This)->lpVtbl->OnStoryboardUpdated(This,storyboard)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationStoryboardEventHandler_QueryInterface(IUIAnimationStoryboardEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationStoryboardEventHandler_AddRef(IUIAnimationStoryboardEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationStoryboardEventHandler_Release(IUIAnimationStoryboardEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationStoryboardEventHandler methods ***/
static inline HRESULT IUIAnimationStoryboardEventHandler_OnStoryboardStatusChanged(IUIAnimationStoryboardEventHandler* This,IUIAnimationStoryboard *storyboard,UI_ANIMATION_STORYBOARD_STATUS newStatus,UI_ANIMATION_STORYBOARD_STATUS previousStatus) {
    return This->lpVtbl->OnStoryboardStatusChanged(This,storyboard,newStatus,previousStatus);
}
static inline HRESULT IUIAnimationStoryboardEventHandler_OnStoryboardUpdated(IUIAnimationStoryboardEventHandler* This,IUIAnimationStoryboard *storyboard) {
    return This->lpVtbl->OnStoryboardUpdated(This,storyboard);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationStoryboardEventHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationVariableChangeHandler interface
 */
#ifndef __IUIAnimationVariableChangeHandler_INTERFACE_DEFINED__
#define __IUIAnimationVariableChangeHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationVariableChangeHandler, 0x6358b7ba, 0x87d2, 0x42d5, 0xbf,0x71, 0x82,0xe9,0x19,0xdd,0x58,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6358b7ba-87d2-42d5-bf71-82e919dd5862")
IUIAnimationVariableChangeHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnValueChanged(
        IUIAnimationStoryboard *storyboard,
        IUIAnimationVariable *variable,
        double newValue,
        double previousValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationVariableChangeHandler, 0x6358b7ba, 0x87d2, 0x42d5, 0xbf,0x71, 0x82,0xe9,0x19,0xdd,0x58,0x62)
#endif
#else
typedef struct IUIAnimationVariableChangeHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationVariableChangeHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationVariableChangeHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationVariableChangeHandler *This);

    /*** IUIAnimationVariableChangeHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *OnValueChanged)(
        IUIAnimationVariableChangeHandler *This,
        IUIAnimationStoryboard *storyboard,
        IUIAnimationVariable *variable,
        double newValue,
        double previousValue);

    END_INTERFACE
} IUIAnimationVariableChangeHandlerVtbl;

interface IUIAnimationVariableChangeHandler {
    CONST_VTBL IUIAnimationVariableChangeHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationVariableChangeHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationVariableChangeHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationVariableChangeHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationVariableChangeHandler methods ***/
#define IUIAnimationVariableChangeHandler_OnValueChanged(This,storyboard,variable,newValue,previousValue) (This)->lpVtbl->OnValueChanged(This,storyboard,variable,newValue,previousValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationVariableChangeHandler_QueryInterface(IUIAnimationVariableChangeHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationVariableChangeHandler_AddRef(IUIAnimationVariableChangeHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationVariableChangeHandler_Release(IUIAnimationVariableChangeHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationVariableChangeHandler methods ***/
static inline HRESULT IUIAnimationVariableChangeHandler_OnValueChanged(IUIAnimationVariableChangeHandler* This,IUIAnimationStoryboard *storyboard,IUIAnimationVariable *variable,double newValue,double previousValue) {
    return This->lpVtbl->OnValueChanged(This,storyboard,variable,newValue,previousValue);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationVariableChangeHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationVariableIntegerChangeHandler interface
 */
#ifndef __IUIAnimationVariableIntegerChangeHandler_INTERFACE_DEFINED__
#define __IUIAnimationVariableIntegerChangeHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationVariableIntegerChangeHandler, 0xbb3e1550, 0x356e, 0x44b0, 0x99,0xda, 0x85,0xac,0x60,0x17,0x86,0x5e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bb3e1550-356e-44b0-99da-85ac6017865e")
IUIAnimationVariableIntegerChangeHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnIntegerValueChanged(
        IUIAnimationStoryboard *storyboard,
        IUIAnimationVariable *variable,
        int newValue,
        int previousValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationVariableIntegerChangeHandler, 0xbb3e1550, 0x356e, 0x44b0, 0x99,0xda, 0x85,0xac,0x60,0x17,0x86,0x5e)
#endif
#else
typedef struct IUIAnimationVariableIntegerChangeHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationVariableIntegerChangeHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationVariableIntegerChangeHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationVariableIntegerChangeHandler *This);

    /*** IUIAnimationVariableIntegerChangeHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *OnIntegerValueChanged)(
        IUIAnimationVariableIntegerChangeHandler *This,
        IUIAnimationStoryboard *storyboard,
        IUIAnimationVariable *variable,
        int newValue,
        int previousValue);

    END_INTERFACE
} IUIAnimationVariableIntegerChangeHandlerVtbl;

interface IUIAnimationVariableIntegerChangeHandler {
    CONST_VTBL IUIAnimationVariableIntegerChangeHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationVariableIntegerChangeHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationVariableIntegerChangeHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationVariableIntegerChangeHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationVariableIntegerChangeHandler methods ***/
#define IUIAnimationVariableIntegerChangeHandler_OnIntegerValueChanged(This,storyboard,variable,newValue,previousValue) (This)->lpVtbl->OnIntegerValueChanged(This,storyboard,variable,newValue,previousValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationVariableIntegerChangeHandler_QueryInterface(IUIAnimationVariableIntegerChangeHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationVariableIntegerChangeHandler_AddRef(IUIAnimationVariableIntegerChangeHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationVariableIntegerChangeHandler_Release(IUIAnimationVariableIntegerChangeHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationVariableIntegerChangeHandler methods ***/
static inline HRESULT IUIAnimationVariableIntegerChangeHandler_OnIntegerValueChanged(IUIAnimationVariableIntegerChangeHandler* This,IUIAnimationStoryboard *storyboard,IUIAnimationVariable *variable,int newValue,int previousValue) {
    return This->lpVtbl->OnIntegerValueChanged(This,storyboard,variable,newValue,previousValue);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationVariableIntegerChangeHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationManagerEventHandler interface
 */
#ifndef __IUIAnimationManagerEventHandler_INTERFACE_DEFINED__
#define __IUIAnimationManagerEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationManagerEventHandler, 0x783321ed, 0x78a3, 0x4366, 0xb5,0x74, 0x6a,0xf6,0x07,0xa6,0x47,0x88);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("783321ed-78a3-4366-b574-6af607a64788")
IUIAnimationManagerEventHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnManagerStatusChanged(
        UI_ANIMATION_MANAGER_STATUS newStatus,
        UI_ANIMATION_MANAGER_STATUS previousStatus) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationManagerEventHandler, 0x783321ed, 0x78a3, 0x4366, 0xb5,0x74, 0x6a,0xf6,0x07,0xa6,0x47,0x88)
#endif
#else
typedef struct IUIAnimationManagerEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationManagerEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationManagerEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationManagerEventHandler *This);

    /*** IUIAnimationManagerEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *OnManagerStatusChanged)(
        IUIAnimationManagerEventHandler *This,
        UI_ANIMATION_MANAGER_STATUS newStatus,
        UI_ANIMATION_MANAGER_STATUS previousStatus);

    END_INTERFACE
} IUIAnimationManagerEventHandlerVtbl;

interface IUIAnimationManagerEventHandler {
    CONST_VTBL IUIAnimationManagerEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationManagerEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationManagerEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationManagerEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationManagerEventHandler methods ***/
#define IUIAnimationManagerEventHandler_OnManagerStatusChanged(This,newStatus,previousStatus) (This)->lpVtbl->OnManagerStatusChanged(This,newStatus,previousStatus)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationManagerEventHandler_QueryInterface(IUIAnimationManagerEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationManagerEventHandler_AddRef(IUIAnimationManagerEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationManagerEventHandler_Release(IUIAnimationManagerEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationManagerEventHandler methods ***/
static inline HRESULT IUIAnimationManagerEventHandler_OnManagerStatusChanged(IUIAnimationManagerEventHandler* This,UI_ANIMATION_MANAGER_STATUS newStatus,UI_ANIMATION_MANAGER_STATUS previousStatus) {
    return This->lpVtbl->OnManagerStatusChanged(This,newStatus,previousStatus);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationManagerEventHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationPriorityComparison interface
 */
#ifndef __IUIAnimationPriorityComparison_INTERFACE_DEFINED__
#define __IUIAnimationPriorityComparison_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationPriorityComparison, 0x83fa9b74, 0x5f86, 0x4618, 0xbc,0x6a, 0xa2,0xfa,0xc1,0x9b,0x3f,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("83fa9b74-5f86-4618-bc6a-a2fac19b3f44")
IUIAnimationPriorityComparison : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE HasPriority(
        IUIAnimationStoryboard *scheduledStoryboard,
        IUIAnimationStoryboard *newStoryboard,
        UI_ANIMATION_PRIORITY_EFFECT priorityEffect) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationPriorityComparison, 0x83fa9b74, 0x5f86, 0x4618, 0xbc,0x6a, 0xa2,0xfa,0xc1,0x9b,0x3f,0x44)
#endif
#else
typedef struct IUIAnimationPriorityComparisonVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationPriorityComparison *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationPriorityComparison *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationPriorityComparison *This);

    /*** IUIAnimationPriorityComparison methods ***/
    HRESULT (STDMETHODCALLTYPE *HasPriority)(
        IUIAnimationPriorityComparison *This,
        IUIAnimationStoryboard *scheduledStoryboard,
        IUIAnimationStoryboard *newStoryboard,
        UI_ANIMATION_PRIORITY_EFFECT priorityEffect);

    END_INTERFACE
} IUIAnimationPriorityComparisonVtbl;

interface IUIAnimationPriorityComparison {
    CONST_VTBL IUIAnimationPriorityComparisonVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationPriorityComparison_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationPriorityComparison_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationPriorityComparison_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationPriorityComparison methods ***/
#define IUIAnimationPriorityComparison_HasPriority(This,scheduledStoryboard,newStoryboard,priorityEffect) (This)->lpVtbl->HasPriority(This,scheduledStoryboard,newStoryboard,priorityEffect)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationPriorityComparison_QueryInterface(IUIAnimationPriorityComparison* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationPriorityComparison_AddRef(IUIAnimationPriorityComparison* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationPriorityComparison_Release(IUIAnimationPriorityComparison* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationPriorityComparison methods ***/
static inline HRESULT IUIAnimationPriorityComparison_HasPriority(IUIAnimationPriorityComparison* This,IUIAnimationStoryboard *scheduledStoryboard,IUIAnimationStoryboard *newStoryboard,UI_ANIMATION_PRIORITY_EFFECT priorityEffect) {
    return This->lpVtbl->HasPriority(This,scheduledStoryboard,newStoryboard,priorityEffect);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationPriorityComparison_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationManager2 interface
 */
#ifndef __IUIAnimationManager2_INTERFACE_DEFINED__
#define __IUIAnimationManager2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationManager2, 0xd8b6f7d4, 0x4109, 0x4d3f, 0xac,0xee, 0x87,0x99,0x26,0x96,0x8c,0xb1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d8b6f7d4-4109-4d3f-acee-879926968cb1")
IUIAnimationManager2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateAnimationVectorVariable(
        double *initialValue,
        unsigned int cDimension,
        IUIAnimationVariable2 **variable) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateAnimationVariable(
        double initialValue,
        IUIAnimationVariable2 **variable) = 0;

    virtual HRESULT STDMETHODCALLTYPE ScheduleTransition(
        IUIAnimationVariable2 *variable,
        IUIAnimationTransition2 *transition,
        double timeNow) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateStoryboard(
        IUIAnimationStoryboard2 **storyboard) = 0;

    virtual HRESULT STDMETHODCALLTYPE FinishAllStoryboards(
        double completionDeadline) = 0;

    virtual HRESULT STDMETHODCALLTYPE AbandonAllStoryboards(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Update(
        double timeNow,
        UI_ANIMATION_UPDATE_RESULT *updateResult = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVariableFromTag(
        IUnknown *object,
        UINT32 id,
        IUIAnimationVariable2 **variable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStoryboardFromTag(
        IUnknown *object,
        unsigned int id,
        IUIAnimationStoryboard2 **storyboard) = 0;

    virtual HRESULT STDMETHODCALLTYPE EstimateNextEventTime(
        double *seconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        UI_ANIMATION_MANAGER_STATUS *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAnimationMode(
        UI_ANIMATION_MODE mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetManagerEventHandler(
        IUIAnimationManagerEventHandler2 *handler,
        LONG fRegisterForNextAnimationEvent = FALSE) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCancelPriorityComparison(
        IUIAnimationPriorityComparison2 *comparison) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTrimPriorityComparison(
        IUIAnimationPriorityComparison2 *comparison) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCompressPriorityComparison(
        IUIAnimationPriorityComparison2 *comparison) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetConcludePriorityComparison(
        IUIAnimationPriorityComparison2 *comparison) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultLongestAcceptableDelay(
        double delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE Shutdown(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationManager2, 0xd8b6f7d4, 0x4109, 0x4d3f, 0xac,0xee, 0x87,0x99,0x26,0x96,0x8c,0xb1)
#endif
#else
typedef struct IUIAnimationManager2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationManager2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationManager2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationManager2 *This);

    /*** IUIAnimationManager2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateAnimationVectorVariable)(
        IUIAnimationManager2 *This,
        double *initialValue,
        unsigned int cDimension,
        IUIAnimationVariable2 **variable);

    HRESULT (STDMETHODCALLTYPE *CreateAnimationVariable)(
        IUIAnimationManager2 *This,
        double initialValue,
        IUIAnimationVariable2 **variable);

    HRESULT (STDMETHODCALLTYPE *ScheduleTransition)(
        IUIAnimationManager2 *This,
        IUIAnimationVariable2 *variable,
        IUIAnimationTransition2 *transition,
        double timeNow);

    HRESULT (STDMETHODCALLTYPE *CreateStoryboard)(
        IUIAnimationManager2 *This,
        IUIAnimationStoryboard2 **storyboard);

    HRESULT (STDMETHODCALLTYPE *FinishAllStoryboards)(
        IUIAnimationManager2 *This,
        double completionDeadline);

    HRESULT (STDMETHODCALLTYPE *AbandonAllStoryboards)(
        IUIAnimationManager2 *This);

    HRESULT (STDMETHODCALLTYPE *Update)(
        IUIAnimationManager2 *This,
        double timeNow,
        UI_ANIMATION_UPDATE_RESULT *updateResult);

    HRESULT (STDMETHODCALLTYPE *GetVariableFromTag)(
        IUIAnimationManager2 *This,
        IUnknown *object,
        UINT32 id,
        IUIAnimationVariable2 **variable);

    HRESULT (STDMETHODCALLTYPE *GetStoryboardFromTag)(
        IUIAnimationManager2 *This,
        IUnknown *object,
        unsigned int id,
        IUIAnimationStoryboard2 **storyboard);

    HRESULT (STDMETHODCALLTYPE *EstimateNextEventTime)(
        IUIAnimationManager2 *This,
        double *seconds);

    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IUIAnimationManager2 *This,
        UI_ANIMATION_MANAGER_STATUS *status);

    HRESULT (STDMETHODCALLTYPE *SetAnimationMode)(
        IUIAnimationManager2 *This,
        UI_ANIMATION_MODE mode);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IUIAnimationManager2 *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IUIAnimationManager2 *This);

    HRESULT (STDMETHODCALLTYPE *SetManagerEventHandler)(
        IUIAnimationManager2 *This,
        IUIAnimationManagerEventHandler2 *handler,
        LONG fRegisterForNextAnimationEvent);

    HRESULT (STDMETHODCALLTYPE *SetCancelPriorityComparison)(
        IUIAnimationManager2 *This,
        IUIAnimationPriorityComparison2 *comparison);

    HRESULT (STDMETHODCALLTYPE *SetTrimPriorityComparison)(
        IUIAnimationManager2 *This,
        IUIAnimationPriorityComparison2 *comparison);

    HRESULT (STDMETHODCALLTYPE *SetCompressPriorityComparison)(
        IUIAnimationManager2 *This,
        IUIAnimationPriorityComparison2 *comparison);

    HRESULT (STDMETHODCALLTYPE *SetConcludePriorityComparison)(
        IUIAnimationManager2 *This,
        IUIAnimationPriorityComparison2 *comparison);

    HRESULT (STDMETHODCALLTYPE *SetDefaultLongestAcceptableDelay)(
        IUIAnimationManager2 *This,
        double delay);

    HRESULT (STDMETHODCALLTYPE *Shutdown)(
        IUIAnimationManager2 *This);

    END_INTERFACE
} IUIAnimationManager2Vtbl;

interface IUIAnimationManager2 {
    CONST_VTBL IUIAnimationManager2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationManager2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationManager2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationManager2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationManager2 methods ***/
#define IUIAnimationManager2_CreateAnimationVectorVariable(This,initialValue,cDimension,variable) (This)->lpVtbl->CreateAnimationVectorVariable(This,initialValue,cDimension,variable)
#define IUIAnimationManager2_CreateAnimationVariable(This,initialValue,variable) (This)->lpVtbl->CreateAnimationVariable(This,initialValue,variable)
#define IUIAnimationManager2_ScheduleTransition(This,variable,transition,timeNow) (This)->lpVtbl->ScheduleTransition(This,variable,transition,timeNow)
#define IUIAnimationManager2_CreateStoryboard(This,storyboard) (This)->lpVtbl->CreateStoryboard(This,storyboard)
#define IUIAnimationManager2_FinishAllStoryboards(This,completionDeadline) (This)->lpVtbl->FinishAllStoryboards(This,completionDeadline)
#define IUIAnimationManager2_AbandonAllStoryboards(This) (This)->lpVtbl->AbandonAllStoryboards(This)
#define IUIAnimationManager2_Update(This,timeNow,updateResult) (This)->lpVtbl->Update(This,timeNow,updateResult)
#define IUIAnimationManager2_GetVariableFromTag(This,object,id,variable) (This)->lpVtbl->GetVariableFromTag(This,object,id,variable)
#define IUIAnimationManager2_GetStoryboardFromTag(This,object,id,storyboard) (This)->lpVtbl->GetStoryboardFromTag(This,object,id,storyboard)
#define IUIAnimationManager2_EstimateNextEventTime(This,seconds) (This)->lpVtbl->EstimateNextEventTime(This,seconds)
#define IUIAnimationManager2_GetStatus(This,status) (This)->lpVtbl->GetStatus(This,status)
#define IUIAnimationManager2_SetAnimationMode(This,mode) (This)->lpVtbl->SetAnimationMode(This,mode)
#define IUIAnimationManager2_Pause(This) (This)->lpVtbl->Pause(This)
#define IUIAnimationManager2_Resume(This) (This)->lpVtbl->Resume(This)
#define IUIAnimationManager2_SetManagerEventHandler(This,handler,fRegisterForNextAnimationEvent) (This)->lpVtbl->SetManagerEventHandler(This,handler,fRegisterForNextAnimationEvent)
#define IUIAnimationManager2_SetCancelPriorityComparison(This,comparison) (This)->lpVtbl->SetCancelPriorityComparison(This,comparison)
#define IUIAnimationManager2_SetTrimPriorityComparison(This,comparison) (This)->lpVtbl->SetTrimPriorityComparison(This,comparison)
#define IUIAnimationManager2_SetCompressPriorityComparison(This,comparison) (This)->lpVtbl->SetCompressPriorityComparison(This,comparison)
#define IUIAnimationManager2_SetConcludePriorityComparison(This,comparison) (This)->lpVtbl->SetConcludePriorityComparison(This,comparison)
#define IUIAnimationManager2_SetDefaultLongestAcceptableDelay(This,delay) (This)->lpVtbl->SetDefaultLongestAcceptableDelay(This,delay)
#define IUIAnimationManager2_Shutdown(This) (This)->lpVtbl->Shutdown(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationManager2_QueryInterface(IUIAnimationManager2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationManager2_AddRef(IUIAnimationManager2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationManager2_Release(IUIAnimationManager2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationManager2 methods ***/
static inline HRESULT IUIAnimationManager2_CreateAnimationVectorVariable(IUIAnimationManager2* This,double *initialValue,unsigned int cDimension,IUIAnimationVariable2 **variable) {
    return This->lpVtbl->CreateAnimationVectorVariable(This,initialValue,cDimension,variable);
}
static inline HRESULT IUIAnimationManager2_CreateAnimationVariable(IUIAnimationManager2* This,double initialValue,IUIAnimationVariable2 **variable) {
    return This->lpVtbl->CreateAnimationVariable(This,initialValue,variable);
}
static inline HRESULT IUIAnimationManager2_ScheduleTransition(IUIAnimationManager2* This,IUIAnimationVariable2 *variable,IUIAnimationTransition2 *transition,double timeNow) {
    return This->lpVtbl->ScheduleTransition(This,variable,transition,timeNow);
}
static inline HRESULT IUIAnimationManager2_CreateStoryboard(IUIAnimationManager2* This,IUIAnimationStoryboard2 **storyboard) {
    return This->lpVtbl->CreateStoryboard(This,storyboard);
}
static inline HRESULT IUIAnimationManager2_FinishAllStoryboards(IUIAnimationManager2* This,double completionDeadline) {
    return This->lpVtbl->FinishAllStoryboards(This,completionDeadline);
}
static inline HRESULT IUIAnimationManager2_AbandonAllStoryboards(IUIAnimationManager2* This) {
    return This->lpVtbl->AbandonAllStoryboards(This);
}
static inline HRESULT IUIAnimationManager2_Update(IUIAnimationManager2* This,double timeNow,UI_ANIMATION_UPDATE_RESULT *updateResult) {
    return This->lpVtbl->Update(This,timeNow,updateResult);
}
static inline HRESULT IUIAnimationManager2_GetVariableFromTag(IUIAnimationManager2* This,IUnknown *object,UINT32 id,IUIAnimationVariable2 **variable) {
    return This->lpVtbl->GetVariableFromTag(This,object,id,variable);
}
static inline HRESULT IUIAnimationManager2_GetStoryboardFromTag(IUIAnimationManager2* This,IUnknown *object,unsigned int id,IUIAnimationStoryboard2 **storyboard) {
    return This->lpVtbl->GetStoryboardFromTag(This,object,id,storyboard);
}
static inline HRESULT IUIAnimationManager2_EstimateNextEventTime(IUIAnimationManager2* This,double *seconds) {
    return This->lpVtbl->EstimateNextEventTime(This,seconds);
}
static inline HRESULT IUIAnimationManager2_GetStatus(IUIAnimationManager2* This,UI_ANIMATION_MANAGER_STATUS *status) {
    return This->lpVtbl->GetStatus(This,status);
}
static inline HRESULT IUIAnimationManager2_SetAnimationMode(IUIAnimationManager2* This,UI_ANIMATION_MODE mode) {
    return This->lpVtbl->SetAnimationMode(This,mode);
}
static inline HRESULT IUIAnimationManager2_Pause(IUIAnimationManager2* This) {
    return This->lpVtbl->Pause(This);
}
static inline HRESULT IUIAnimationManager2_Resume(IUIAnimationManager2* This) {
    return This->lpVtbl->Resume(This);
}
static inline HRESULT IUIAnimationManager2_SetManagerEventHandler(IUIAnimationManager2* This,IUIAnimationManagerEventHandler2 *handler,LONG fRegisterForNextAnimationEvent) {
    return This->lpVtbl->SetManagerEventHandler(This,handler,fRegisterForNextAnimationEvent);
}
static inline HRESULT IUIAnimationManager2_SetCancelPriorityComparison(IUIAnimationManager2* This,IUIAnimationPriorityComparison2 *comparison) {
    return This->lpVtbl->SetCancelPriorityComparison(This,comparison);
}
static inline HRESULT IUIAnimationManager2_SetTrimPriorityComparison(IUIAnimationManager2* This,IUIAnimationPriorityComparison2 *comparison) {
    return This->lpVtbl->SetTrimPriorityComparison(This,comparison);
}
static inline HRESULT IUIAnimationManager2_SetCompressPriorityComparison(IUIAnimationManager2* This,IUIAnimationPriorityComparison2 *comparison) {
    return This->lpVtbl->SetCompressPriorityComparison(This,comparison);
}
static inline HRESULT IUIAnimationManager2_SetConcludePriorityComparison(IUIAnimationManager2* This,IUIAnimationPriorityComparison2 *comparison) {
    return This->lpVtbl->SetConcludePriorityComparison(This,comparison);
}
static inline HRESULT IUIAnimationManager2_SetDefaultLongestAcceptableDelay(IUIAnimationManager2* This,double delay) {
    return This->lpVtbl->SetDefaultLongestAcceptableDelay(This,delay);
}
static inline HRESULT IUIAnimationManager2_Shutdown(IUIAnimationManager2* This) {
    return This->lpVtbl->Shutdown(This);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationManager2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationVariable2 interface
 */
#ifndef __IUIAnimationVariable2_INTERFACE_DEFINED__
#define __IUIAnimationVariable2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationVariable2, 0x4914b304, 0x96ab, 0x44d9, 0x9e,0x77, 0xd5,0x10,0x9b,0x7e,0x74,0x66);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4914b304-96ab-44d9-9e77-d5109b7e7466")
IUIAnimationVariable2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDimension(
        unsigned int *dimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetValue(
        double *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVectorValue(
        double *value,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurve(
        IDCompositionAnimation *animation) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVectorCurve(
        IDCompositionAnimation **animation,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFinalValue(
        double *finalValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFinalVectorValue(
        double *finalValue,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreviousValue(
        double *previousValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreviousVectorValue(
        double *previousValue,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIntegerValue(
        int *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIntegerVectorValue(
        int *value,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFinalIntegerValue(
        int *finalValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFinalIntegerVectorValue(
        int *finalValue,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreviousIntegerValue(
        int *previousValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreviousIntegerVectorValue(
        int *previousValue,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentStoryboard(
        IUIAnimationStoryboard2 **storyboard) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLowerBound(
        double bound) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLowerBoundVector(
        double *bound,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUpperBound(
        double bound) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUpperBoundVector(
        double *bound,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRoundingMode(
        UI_ANIMATION_ROUNDING_MODE mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTag(
        IUnknown *object,
        unsigned int id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTag(
        IUnknown **object,
        unsigned int *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVariableChangeHandler(
        IUIAnimationVariableChangeHandler2 *handler,
        LONG fRegisterForNextAnimationEvent = FALSE) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVariableIntegerChangeHandler(
        IUIAnimationVariableIntegerChangeHandler2 *handler,
        LONG fRegisterForNextAnimationEvent = FALSE) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVariableCurveChangeHandler(
        IUIAnimationVariableCurveChangeHandler2 *handler) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationVariable2, 0x4914b304, 0x96ab, 0x44d9, 0x9e,0x77, 0xd5,0x10,0x9b,0x7e,0x74,0x66)
#endif
#else
typedef struct IUIAnimationVariable2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationVariable2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationVariable2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationVariable2 *This);

    /*** IUIAnimationVariable2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDimension)(
        IUIAnimationVariable2 *This,
        unsigned int *dimension);

    HRESULT (STDMETHODCALLTYPE *GetValue)(
        IUIAnimationVariable2 *This,
        double *value);

    HRESULT (STDMETHODCALLTYPE *GetVectorValue)(
        IUIAnimationVariable2 *This,
        double *value,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *GetCurve)(
        IUIAnimationVariable2 *This,
        IDCompositionAnimation *animation);

    HRESULT (STDMETHODCALLTYPE *GetVectorCurve)(
        IUIAnimationVariable2 *This,
        IDCompositionAnimation **animation,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *GetFinalValue)(
        IUIAnimationVariable2 *This,
        double *finalValue);

    HRESULT (STDMETHODCALLTYPE *GetFinalVectorValue)(
        IUIAnimationVariable2 *This,
        double *finalValue,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *GetPreviousValue)(
        IUIAnimationVariable2 *This,
        double *previousValue);

    HRESULT (STDMETHODCALLTYPE *GetPreviousVectorValue)(
        IUIAnimationVariable2 *This,
        double *previousValue,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *GetIntegerValue)(
        IUIAnimationVariable2 *This,
        int *value);

    HRESULT (STDMETHODCALLTYPE *GetIntegerVectorValue)(
        IUIAnimationVariable2 *This,
        int *value,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *GetFinalIntegerValue)(
        IUIAnimationVariable2 *This,
        int *finalValue);

    HRESULT (STDMETHODCALLTYPE *GetFinalIntegerVectorValue)(
        IUIAnimationVariable2 *This,
        int *finalValue,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *GetPreviousIntegerValue)(
        IUIAnimationVariable2 *This,
        int *previousValue);

    HRESULT (STDMETHODCALLTYPE *GetPreviousIntegerVectorValue)(
        IUIAnimationVariable2 *This,
        int *previousValue,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *GetCurrentStoryboard)(
        IUIAnimationVariable2 *This,
        IUIAnimationStoryboard2 **storyboard);

    HRESULT (STDMETHODCALLTYPE *SetLowerBound)(
        IUIAnimationVariable2 *This,
        double bound);

    HRESULT (STDMETHODCALLTYPE *SetLowerBoundVector)(
        IUIAnimationVariable2 *This,
        double *bound,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *SetUpperBound)(
        IUIAnimationVariable2 *This,
        double bound);

    HRESULT (STDMETHODCALLTYPE *SetUpperBoundVector)(
        IUIAnimationVariable2 *This,
        double *bound,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *SetRoundingMode)(
        IUIAnimationVariable2 *This,
        UI_ANIMATION_ROUNDING_MODE mode);

    HRESULT (STDMETHODCALLTYPE *SetTag)(
        IUIAnimationVariable2 *This,
        IUnknown *object,
        unsigned int id);

    HRESULT (STDMETHODCALLTYPE *GetTag)(
        IUIAnimationVariable2 *This,
        IUnknown **object,
        unsigned int *id);

    HRESULT (STDMETHODCALLTYPE *SetVariableChangeHandler)(
        IUIAnimationVariable2 *This,
        IUIAnimationVariableChangeHandler2 *handler,
        LONG fRegisterForNextAnimationEvent);

    HRESULT (STDMETHODCALLTYPE *SetVariableIntegerChangeHandler)(
        IUIAnimationVariable2 *This,
        IUIAnimationVariableIntegerChangeHandler2 *handler,
        LONG fRegisterForNextAnimationEvent);

    HRESULT (STDMETHODCALLTYPE *SetVariableCurveChangeHandler)(
        IUIAnimationVariable2 *This,
        IUIAnimationVariableCurveChangeHandler2 *handler);

    END_INTERFACE
} IUIAnimationVariable2Vtbl;

interface IUIAnimationVariable2 {
    CONST_VTBL IUIAnimationVariable2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationVariable2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationVariable2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationVariable2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationVariable2 methods ***/
#define IUIAnimationVariable2_GetDimension(This,dimension) (This)->lpVtbl->GetDimension(This,dimension)
#define IUIAnimationVariable2_GetValue(This,value) (This)->lpVtbl->GetValue(This,value)
#define IUIAnimationVariable2_GetVectorValue(This,value,cDimension) (This)->lpVtbl->GetVectorValue(This,value,cDimension)
#define IUIAnimationVariable2_GetCurve(This,animation) (This)->lpVtbl->GetCurve(This,animation)
#define IUIAnimationVariable2_GetVectorCurve(This,animation,cDimension) (This)->lpVtbl->GetVectorCurve(This,animation,cDimension)
#define IUIAnimationVariable2_GetFinalValue(This,finalValue) (This)->lpVtbl->GetFinalValue(This,finalValue)
#define IUIAnimationVariable2_GetFinalVectorValue(This,finalValue,cDimension) (This)->lpVtbl->GetFinalVectorValue(This,finalValue,cDimension)
#define IUIAnimationVariable2_GetPreviousValue(This,previousValue) (This)->lpVtbl->GetPreviousValue(This,previousValue)
#define IUIAnimationVariable2_GetPreviousVectorValue(This,previousValue,cDimension) (This)->lpVtbl->GetPreviousVectorValue(This,previousValue,cDimension)
#define IUIAnimationVariable2_GetIntegerValue(This,value) (This)->lpVtbl->GetIntegerValue(This,value)
#define IUIAnimationVariable2_GetIntegerVectorValue(This,value,cDimension) (This)->lpVtbl->GetIntegerVectorValue(This,value,cDimension)
#define IUIAnimationVariable2_GetFinalIntegerValue(This,finalValue) (This)->lpVtbl->GetFinalIntegerValue(This,finalValue)
#define IUIAnimationVariable2_GetFinalIntegerVectorValue(This,finalValue,cDimension) (This)->lpVtbl->GetFinalIntegerVectorValue(This,finalValue,cDimension)
#define IUIAnimationVariable2_GetPreviousIntegerValue(This,previousValue) (This)->lpVtbl->GetPreviousIntegerValue(This,previousValue)
#define IUIAnimationVariable2_GetPreviousIntegerVectorValue(This,previousValue,cDimension) (This)->lpVtbl->GetPreviousIntegerVectorValue(This,previousValue,cDimension)
#define IUIAnimationVariable2_GetCurrentStoryboard(This,storyboard) (This)->lpVtbl->GetCurrentStoryboard(This,storyboard)
#define IUIAnimationVariable2_SetLowerBound(This,bound) (This)->lpVtbl->SetLowerBound(This,bound)
#define IUIAnimationVariable2_SetLowerBoundVector(This,bound,cDimension) (This)->lpVtbl->SetLowerBoundVector(This,bound,cDimension)
#define IUIAnimationVariable2_SetUpperBound(This,bound) (This)->lpVtbl->SetUpperBound(This,bound)
#define IUIAnimationVariable2_SetUpperBoundVector(This,bound,cDimension) (This)->lpVtbl->SetUpperBoundVector(This,bound,cDimension)
#define IUIAnimationVariable2_SetRoundingMode(This,mode) (This)->lpVtbl->SetRoundingMode(This,mode)
#define IUIAnimationVariable2_SetTag(This,object,id) (This)->lpVtbl->SetTag(This,object,id)
#define IUIAnimationVariable2_GetTag(This,object,id) (This)->lpVtbl->GetTag(This,object,id)
#define IUIAnimationVariable2_SetVariableChangeHandler(This,handler,fRegisterForNextAnimationEvent) (This)->lpVtbl->SetVariableChangeHandler(This,handler,fRegisterForNextAnimationEvent)
#define IUIAnimationVariable2_SetVariableIntegerChangeHandler(This,handler,fRegisterForNextAnimationEvent) (This)->lpVtbl->SetVariableIntegerChangeHandler(This,handler,fRegisterForNextAnimationEvent)
#define IUIAnimationVariable2_SetVariableCurveChangeHandler(This,handler) (This)->lpVtbl->SetVariableCurveChangeHandler(This,handler)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationVariable2_QueryInterface(IUIAnimationVariable2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationVariable2_AddRef(IUIAnimationVariable2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationVariable2_Release(IUIAnimationVariable2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationVariable2 methods ***/
static inline HRESULT IUIAnimationVariable2_GetDimension(IUIAnimationVariable2* This,unsigned int *dimension) {
    return This->lpVtbl->GetDimension(This,dimension);
}
static inline HRESULT IUIAnimationVariable2_GetValue(IUIAnimationVariable2* This,double *value) {
    return This->lpVtbl->GetValue(This,value);
}
static inline HRESULT IUIAnimationVariable2_GetVectorValue(IUIAnimationVariable2* This,double *value,unsigned int cDimension) {
    return This->lpVtbl->GetVectorValue(This,value,cDimension);
}
static inline HRESULT IUIAnimationVariable2_GetCurve(IUIAnimationVariable2* This,IDCompositionAnimation *animation) {
    return This->lpVtbl->GetCurve(This,animation);
}
static inline HRESULT IUIAnimationVariable2_GetVectorCurve(IUIAnimationVariable2* This,IDCompositionAnimation **animation,unsigned int cDimension) {
    return This->lpVtbl->GetVectorCurve(This,animation,cDimension);
}
static inline HRESULT IUIAnimationVariable2_GetFinalValue(IUIAnimationVariable2* This,double *finalValue) {
    return This->lpVtbl->GetFinalValue(This,finalValue);
}
static inline HRESULT IUIAnimationVariable2_GetFinalVectorValue(IUIAnimationVariable2* This,double *finalValue,unsigned int cDimension) {
    return This->lpVtbl->GetFinalVectorValue(This,finalValue,cDimension);
}
static inline HRESULT IUIAnimationVariable2_GetPreviousValue(IUIAnimationVariable2* This,double *previousValue) {
    return This->lpVtbl->GetPreviousValue(This,previousValue);
}
static inline HRESULT IUIAnimationVariable2_GetPreviousVectorValue(IUIAnimationVariable2* This,double *previousValue,unsigned int cDimension) {
    return This->lpVtbl->GetPreviousVectorValue(This,previousValue,cDimension);
}
static inline HRESULT IUIAnimationVariable2_GetIntegerValue(IUIAnimationVariable2* This,int *value) {
    return This->lpVtbl->GetIntegerValue(This,value);
}
static inline HRESULT IUIAnimationVariable2_GetIntegerVectorValue(IUIAnimationVariable2* This,int *value,unsigned int cDimension) {
    return This->lpVtbl->GetIntegerVectorValue(This,value,cDimension);
}
static inline HRESULT IUIAnimationVariable2_GetFinalIntegerValue(IUIAnimationVariable2* This,int *finalValue) {
    return This->lpVtbl->GetFinalIntegerValue(This,finalValue);
}
static inline HRESULT IUIAnimationVariable2_GetFinalIntegerVectorValue(IUIAnimationVariable2* This,int *finalValue,unsigned int cDimension) {
    return This->lpVtbl->GetFinalIntegerVectorValue(This,finalValue,cDimension);
}
static inline HRESULT IUIAnimationVariable2_GetPreviousIntegerValue(IUIAnimationVariable2* This,int *previousValue) {
    return This->lpVtbl->GetPreviousIntegerValue(This,previousValue);
}
static inline HRESULT IUIAnimationVariable2_GetPreviousIntegerVectorValue(IUIAnimationVariable2* This,int *previousValue,unsigned int cDimension) {
    return This->lpVtbl->GetPreviousIntegerVectorValue(This,previousValue,cDimension);
}
static inline HRESULT IUIAnimationVariable2_GetCurrentStoryboard(IUIAnimationVariable2* This,IUIAnimationStoryboard2 **storyboard) {
    return This->lpVtbl->GetCurrentStoryboard(This,storyboard);
}
static inline HRESULT IUIAnimationVariable2_SetLowerBound(IUIAnimationVariable2* This,double bound) {
    return This->lpVtbl->SetLowerBound(This,bound);
}
static inline HRESULT IUIAnimationVariable2_SetLowerBoundVector(IUIAnimationVariable2* This,double *bound,unsigned int cDimension) {
    return This->lpVtbl->SetLowerBoundVector(This,bound,cDimension);
}
static inline HRESULT IUIAnimationVariable2_SetUpperBound(IUIAnimationVariable2* This,double bound) {
    return This->lpVtbl->SetUpperBound(This,bound);
}
static inline HRESULT IUIAnimationVariable2_SetUpperBoundVector(IUIAnimationVariable2* This,double *bound,unsigned int cDimension) {
    return This->lpVtbl->SetUpperBoundVector(This,bound,cDimension);
}
static inline HRESULT IUIAnimationVariable2_SetRoundingMode(IUIAnimationVariable2* This,UI_ANIMATION_ROUNDING_MODE mode) {
    return This->lpVtbl->SetRoundingMode(This,mode);
}
static inline HRESULT IUIAnimationVariable2_SetTag(IUIAnimationVariable2* This,IUnknown *object,unsigned int id) {
    return This->lpVtbl->SetTag(This,object,id);
}
static inline HRESULT IUIAnimationVariable2_GetTag(IUIAnimationVariable2* This,IUnknown **object,unsigned int *id) {
    return This->lpVtbl->GetTag(This,object,id);
}
static inline HRESULT IUIAnimationVariable2_SetVariableChangeHandler(IUIAnimationVariable2* This,IUIAnimationVariableChangeHandler2 *handler,LONG fRegisterForNextAnimationEvent) {
    return This->lpVtbl->SetVariableChangeHandler(This,handler,fRegisterForNextAnimationEvent);
}
static inline HRESULT IUIAnimationVariable2_SetVariableIntegerChangeHandler(IUIAnimationVariable2* This,IUIAnimationVariableIntegerChangeHandler2 *handler,LONG fRegisterForNextAnimationEvent) {
    return This->lpVtbl->SetVariableIntegerChangeHandler(This,handler,fRegisterForNextAnimationEvent);
}
static inline HRESULT IUIAnimationVariable2_SetVariableCurveChangeHandler(IUIAnimationVariable2* This,IUIAnimationVariableCurveChangeHandler2 *handler) {
    return This->lpVtbl->SetVariableCurveChangeHandler(This,handler);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationVariable2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDCompositionAnimation interface
 */
#ifndef __IDCompositionAnimation_INTERFACE_DEFINED__
#define __IDCompositionAnimation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDCompositionAnimation, 0xcbfd91d9, 0x51b2, 0x45e4, 0xb3,0xde, 0xd1,0x9c,0xcf,0xb8,0x63,0xc5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cbfd91d9-51b2-45e4-b3de-d19ccfb863c5")
IDCompositionAnimation : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Reset(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAbsoluteBeginTime(
        LARGE_INTEGER beginTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddCubic(
        double beginOffset,
        float constantCoefficient,
        float linearCoefficient,
        float quadraticCoefficient,
        float cubicCoefficient) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddSinusoidal(
        double beginOffset,
        float bias,
        float amplitude,
        float frequency,
        float phase) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddRepeat(
        double beginOffset,
        double durationToRepeat) = 0;

    virtual HRESULT STDMETHODCALLTYPE End(
        double endOffset,
        float endValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDCompositionAnimation, 0xcbfd91d9, 0x51b2, 0x45e4, 0xb3,0xde, 0xd1,0x9c,0xcf,0xb8,0x63,0xc5)
#endif
#else
typedef struct IDCompositionAnimationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDCompositionAnimation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDCompositionAnimation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDCompositionAnimation *This);

    /*** IDCompositionAnimation methods ***/
    HRESULT (STDMETHODCALLTYPE *Reset)(
        IDCompositionAnimation *This);

    HRESULT (STDMETHODCALLTYPE *SetAbsoluteBeginTime)(
        IDCompositionAnimation *This,
        LARGE_INTEGER beginTime);

    HRESULT (STDMETHODCALLTYPE *AddCubic)(
        IDCompositionAnimation *This,
        double beginOffset,
        float constantCoefficient,
        float linearCoefficient,
        float quadraticCoefficient,
        float cubicCoefficient);

    HRESULT (STDMETHODCALLTYPE *AddSinusoidal)(
        IDCompositionAnimation *This,
        double beginOffset,
        float bias,
        float amplitude,
        float frequency,
        float phase);

    HRESULT (STDMETHODCALLTYPE *AddRepeat)(
        IDCompositionAnimation *This,
        double beginOffset,
        double durationToRepeat);

    HRESULT (STDMETHODCALLTYPE *End)(
        IDCompositionAnimation *This,
        double endOffset,
        float endValue);

    END_INTERFACE
} IDCompositionAnimationVtbl;

interface IDCompositionAnimation {
    CONST_VTBL IDCompositionAnimationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDCompositionAnimation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDCompositionAnimation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDCompositionAnimation_Release(This) (This)->lpVtbl->Release(This)
/*** IDCompositionAnimation methods ***/
#define IDCompositionAnimation_Reset(This) (This)->lpVtbl->Reset(This)
#define IDCompositionAnimation_SetAbsoluteBeginTime(This,beginTime) (This)->lpVtbl->SetAbsoluteBeginTime(This,beginTime)
#define IDCompositionAnimation_AddCubic(This,beginOffset,constantCoefficient,linearCoefficient,quadraticCoefficient,cubicCoefficient) (This)->lpVtbl->AddCubic(This,beginOffset,constantCoefficient,linearCoefficient,quadraticCoefficient,cubicCoefficient)
#define IDCompositionAnimation_AddSinusoidal(This,beginOffset,bias,amplitude,frequency,phase) (This)->lpVtbl->AddSinusoidal(This,beginOffset,bias,amplitude,frequency,phase)
#define IDCompositionAnimation_AddRepeat(This,beginOffset,durationToRepeat) (This)->lpVtbl->AddRepeat(This,beginOffset,durationToRepeat)
#define IDCompositionAnimation_End(This,endOffset,endValue) (This)->lpVtbl->End(This,endOffset,endValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IDCompositionAnimation_QueryInterface(IDCompositionAnimation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDCompositionAnimation_AddRef(IDCompositionAnimation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDCompositionAnimation_Release(IDCompositionAnimation* This) {
    return This->lpVtbl->Release(This);
}
/*** IDCompositionAnimation methods ***/
static inline HRESULT IDCompositionAnimation_Reset(IDCompositionAnimation* This) {
    return This->lpVtbl->Reset(This);
}
static inline HRESULT IDCompositionAnimation_SetAbsoluteBeginTime(IDCompositionAnimation* This,LARGE_INTEGER beginTime) {
    return This->lpVtbl->SetAbsoluteBeginTime(This,beginTime);
}
static inline HRESULT IDCompositionAnimation_AddCubic(IDCompositionAnimation* This,double beginOffset,float constantCoefficient,float linearCoefficient,float quadraticCoefficient,float cubicCoefficient) {
    return This->lpVtbl->AddCubic(This,beginOffset,constantCoefficient,linearCoefficient,quadraticCoefficient,cubicCoefficient);
}
static inline HRESULT IDCompositionAnimation_AddSinusoidal(IDCompositionAnimation* This,double beginOffset,float bias,float amplitude,float frequency,float phase) {
    return This->lpVtbl->AddSinusoidal(This,beginOffset,bias,amplitude,frequency,phase);
}
static inline HRESULT IDCompositionAnimation_AddRepeat(IDCompositionAnimation* This,double beginOffset,double durationToRepeat) {
    return This->lpVtbl->AddRepeat(This,beginOffset,durationToRepeat);
}
static inline HRESULT IDCompositionAnimation_End(IDCompositionAnimation* This,double endOffset,float endValue) {
    return This->lpVtbl->End(This,endOffset,endValue);
}
#endif
#endif

#endif


#endif  /* __IDCompositionAnimation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationStoryboard2 interface
 */
#ifndef __IUIAnimationStoryboard2_INTERFACE_DEFINED__
#define __IUIAnimationStoryboard2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationStoryboard2, 0xae289cd2, 0x12d4, 0x4945, 0x94,0x19, 0x9e,0x41,0xbe,0x03,0x4d,0xf2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ae289cd2-12d4-4945-9419-9e41be034df2")
IUIAnimationStoryboard2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddTransition(
        IUIAnimationVariable2 *variable,
        IUIAnimationTransition2 *transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddKeyframeAtOffset(
        UI_ANIMATION_KEYFRAME existingKeyframe,
        double offset,
        UI_ANIMATION_KEYFRAME *keyframe) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddKeyframeAfterTransition(
        IUIAnimationTransition2 *transition,
        UI_ANIMATION_KEYFRAME *keyframe) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddTransitionAtKeyframe(
        IUIAnimationVariable2 *variable,
        IUIAnimationTransition2 *transition,
        UI_ANIMATION_KEYFRAME startKeyframe) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddTransitionBetweenKeyframes(
        IUIAnimationVariable2 *variable,
        IUIAnimationTransition2 *transition,
        UI_ANIMATION_KEYFRAME startKeyframe,
        UI_ANIMATION_KEYFRAME endKeyframe) = 0;

    virtual HRESULT STDMETHODCALLTYPE RepeatBetweenKeyframes(
        UI_ANIMATION_KEYFRAME startKeyframe,
        UI_ANIMATION_KEYFRAME endKeyframe,
        double cRepetition,
        UI_ANIMATION_REPEAT_MODE repeatMode,
        IUIAnimationLoopIterationChangeHandler2 *pIterationChangeHandler = 0,
        UINT_PTR id = 0,
        LONG fRegisterForNextAnimationEvent = FALSE) = 0;

    virtual HRESULT STDMETHODCALLTYPE HoldVariable(
        IUIAnimationVariable2 *variable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLongestAcceptableDelay(
        double delay) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSkipDuration(
        double secondsDuration) = 0;

    virtual HRESULT STDMETHODCALLTYPE Schedule(
        double timeNow,
        UI_ANIMATION_SCHEDULING_RESULT *schedulingResult = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Conclude(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Finish(
        double completionDeadline) = 0;

    virtual HRESULT STDMETHODCALLTYPE Abandon(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTag(
        IUnknown *object,
        unsigned int id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTag(
        IUnknown **object,
        unsigned int *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatus(
        UI_ANIMATION_STORYBOARD_STATUS *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetElapsedTime(
        double *elapsedTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStoryboardEventHandler(
        IUIAnimationStoryboardEventHandler2 *handler,
        LONG fRegisterStatusChangeForNextAnimationEvent = FALSE,
        LONG fRegisterUpdateForNextAnimationEvent = FALSE) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationStoryboard2, 0xae289cd2, 0x12d4, 0x4945, 0x94,0x19, 0x9e,0x41,0xbe,0x03,0x4d,0xf2)
#endif
#else
typedef struct IUIAnimationStoryboard2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationStoryboard2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationStoryboard2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationStoryboard2 *This);

    /*** IUIAnimationStoryboard2 methods ***/
    HRESULT (STDMETHODCALLTYPE *AddTransition)(
        IUIAnimationStoryboard2 *This,
        IUIAnimationVariable2 *variable,
        IUIAnimationTransition2 *transition);

    HRESULT (STDMETHODCALLTYPE *AddKeyframeAtOffset)(
        IUIAnimationStoryboard2 *This,
        UI_ANIMATION_KEYFRAME existingKeyframe,
        double offset,
        UI_ANIMATION_KEYFRAME *keyframe);

    HRESULT (STDMETHODCALLTYPE *AddKeyframeAfterTransition)(
        IUIAnimationStoryboard2 *This,
        IUIAnimationTransition2 *transition,
        UI_ANIMATION_KEYFRAME *keyframe);

    HRESULT (STDMETHODCALLTYPE *AddTransitionAtKeyframe)(
        IUIAnimationStoryboard2 *This,
        IUIAnimationVariable2 *variable,
        IUIAnimationTransition2 *transition,
        UI_ANIMATION_KEYFRAME startKeyframe);

    HRESULT (STDMETHODCALLTYPE *AddTransitionBetweenKeyframes)(
        IUIAnimationStoryboard2 *This,
        IUIAnimationVariable2 *variable,
        IUIAnimationTransition2 *transition,
        UI_ANIMATION_KEYFRAME startKeyframe,
        UI_ANIMATION_KEYFRAME endKeyframe);

    HRESULT (STDMETHODCALLTYPE *RepeatBetweenKeyframes)(
        IUIAnimationStoryboard2 *This,
        UI_ANIMATION_KEYFRAME startKeyframe,
        UI_ANIMATION_KEYFRAME endKeyframe,
        double cRepetition,
        UI_ANIMATION_REPEAT_MODE repeatMode,
        IUIAnimationLoopIterationChangeHandler2 *pIterationChangeHandler,
        UINT_PTR id,
        LONG fRegisterForNextAnimationEvent);

    HRESULT (STDMETHODCALLTYPE *HoldVariable)(
        IUIAnimationStoryboard2 *This,
        IUIAnimationVariable2 *variable);

    HRESULT (STDMETHODCALLTYPE *SetLongestAcceptableDelay)(
        IUIAnimationStoryboard2 *This,
        double delay);

    HRESULT (STDMETHODCALLTYPE *SetSkipDuration)(
        IUIAnimationStoryboard2 *This,
        double secondsDuration);

    HRESULT (STDMETHODCALLTYPE *Schedule)(
        IUIAnimationStoryboard2 *This,
        double timeNow,
        UI_ANIMATION_SCHEDULING_RESULT *schedulingResult);

    HRESULT (STDMETHODCALLTYPE *Conclude)(
        IUIAnimationStoryboard2 *This);

    HRESULT (STDMETHODCALLTYPE *Finish)(
        IUIAnimationStoryboard2 *This,
        double completionDeadline);

    HRESULT (STDMETHODCALLTYPE *Abandon)(
        IUIAnimationStoryboard2 *This);

    HRESULT (STDMETHODCALLTYPE *SetTag)(
        IUIAnimationStoryboard2 *This,
        IUnknown *object,
        unsigned int id);

    HRESULT (STDMETHODCALLTYPE *GetTag)(
        IUIAnimationStoryboard2 *This,
        IUnknown **object,
        unsigned int *id);

    HRESULT (STDMETHODCALLTYPE *GetStatus)(
        IUIAnimationStoryboard2 *This,
        UI_ANIMATION_STORYBOARD_STATUS *status);

    HRESULT (STDMETHODCALLTYPE *GetElapsedTime)(
        IUIAnimationStoryboard2 *This,
        double *elapsedTime);

    HRESULT (STDMETHODCALLTYPE *SetStoryboardEventHandler)(
        IUIAnimationStoryboard2 *This,
        IUIAnimationStoryboardEventHandler2 *handler,
        LONG fRegisterStatusChangeForNextAnimationEvent,
        LONG fRegisterUpdateForNextAnimationEvent);

    END_INTERFACE
} IUIAnimationStoryboard2Vtbl;

interface IUIAnimationStoryboard2 {
    CONST_VTBL IUIAnimationStoryboard2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationStoryboard2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationStoryboard2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationStoryboard2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationStoryboard2 methods ***/
#define IUIAnimationStoryboard2_AddTransition(This,variable,transition) (This)->lpVtbl->AddTransition(This,variable,transition)
#define IUIAnimationStoryboard2_AddKeyframeAtOffset(This,existingKeyframe,offset,keyframe) (This)->lpVtbl->AddKeyframeAtOffset(This,existingKeyframe,offset,keyframe)
#define IUIAnimationStoryboard2_AddKeyframeAfterTransition(This,transition,keyframe) (This)->lpVtbl->AddKeyframeAfterTransition(This,transition,keyframe)
#define IUIAnimationStoryboard2_AddTransitionAtKeyframe(This,variable,transition,startKeyframe) (This)->lpVtbl->AddTransitionAtKeyframe(This,variable,transition,startKeyframe)
#define IUIAnimationStoryboard2_AddTransitionBetweenKeyframes(This,variable,transition,startKeyframe,endKeyframe) (This)->lpVtbl->AddTransitionBetweenKeyframes(This,variable,transition,startKeyframe,endKeyframe)
#define IUIAnimationStoryboard2_RepeatBetweenKeyframes(This,startKeyframe,endKeyframe,cRepetition,repeatMode,pIterationChangeHandler,id,fRegisterForNextAnimationEvent) (This)->lpVtbl->RepeatBetweenKeyframes(This,startKeyframe,endKeyframe,cRepetition,repeatMode,pIterationChangeHandler,id,fRegisterForNextAnimationEvent)
#define IUIAnimationStoryboard2_HoldVariable(This,variable) (This)->lpVtbl->HoldVariable(This,variable)
#define IUIAnimationStoryboard2_SetLongestAcceptableDelay(This,delay) (This)->lpVtbl->SetLongestAcceptableDelay(This,delay)
#define IUIAnimationStoryboard2_SetSkipDuration(This,secondsDuration) (This)->lpVtbl->SetSkipDuration(This,secondsDuration)
#define IUIAnimationStoryboard2_Schedule(This,timeNow,schedulingResult) (This)->lpVtbl->Schedule(This,timeNow,schedulingResult)
#define IUIAnimationStoryboard2_Conclude(This) (This)->lpVtbl->Conclude(This)
#define IUIAnimationStoryboard2_Finish(This,completionDeadline) (This)->lpVtbl->Finish(This,completionDeadline)
#define IUIAnimationStoryboard2_Abandon(This) (This)->lpVtbl->Abandon(This)
#define IUIAnimationStoryboard2_SetTag(This,object,id) (This)->lpVtbl->SetTag(This,object,id)
#define IUIAnimationStoryboard2_GetTag(This,object,id) (This)->lpVtbl->GetTag(This,object,id)
#define IUIAnimationStoryboard2_GetStatus(This,status) (This)->lpVtbl->GetStatus(This,status)
#define IUIAnimationStoryboard2_GetElapsedTime(This,elapsedTime) (This)->lpVtbl->GetElapsedTime(This,elapsedTime)
#define IUIAnimationStoryboard2_SetStoryboardEventHandler(This,handler,fRegisterStatusChangeForNextAnimationEvent,fRegisterUpdateForNextAnimationEvent) (This)->lpVtbl->SetStoryboardEventHandler(This,handler,fRegisterStatusChangeForNextAnimationEvent,fRegisterUpdateForNextAnimationEvent)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationStoryboard2_QueryInterface(IUIAnimationStoryboard2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationStoryboard2_AddRef(IUIAnimationStoryboard2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationStoryboard2_Release(IUIAnimationStoryboard2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationStoryboard2 methods ***/
static inline HRESULT IUIAnimationStoryboard2_AddTransition(IUIAnimationStoryboard2* This,IUIAnimationVariable2 *variable,IUIAnimationTransition2 *transition) {
    return This->lpVtbl->AddTransition(This,variable,transition);
}
static inline HRESULT IUIAnimationStoryboard2_AddKeyframeAtOffset(IUIAnimationStoryboard2* This,UI_ANIMATION_KEYFRAME existingKeyframe,double offset,UI_ANIMATION_KEYFRAME *keyframe) {
    return This->lpVtbl->AddKeyframeAtOffset(This,existingKeyframe,offset,keyframe);
}
static inline HRESULT IUIAnimationStoryboard2_AddKeyframeAfterTransition(IUIAnimationStoryboard2* This,IUIAnimationTransition2 *transition,UI_ANIMATION_KEYFRAME *keyframe) {
    return This->lpVtbl->AddKeyframeAfterTransition(This,transition,keyframe);
}
static inline HRESULT IUIAnimationStoryboard2_AddTransitionAtKeyframe(IUIAnimationStoryboard2* This,IUIAnimationVariable2 *variable,IUIAnimationTransition2 *transition,UI_ANIMATION_KEYFRAME startKeyframe) {
    return This->lpVtbl->AddTransitionAtKeyframe(This,variable,transition,startKeyframe);
}
static inline HRESULT IUIAnimationStoryboard2_AddTransitionBetweenKeyframes(IUIAnimationStoryboard2* This,IUIAnimationVariable2 *variable,IUIAnimationTransition2 *transition,UI_ANIMATION_KEYFRAME startKeyframe,UI_ANIMATION_KEYFRAME endKeyframe) {
    return This->lpVtbl->AddTransitionBetweenKeyframes(This,variable,transition,startKeyframe,endKeyframe);
}
static inline HRESULT IUIAnimationStoryboard2_RepeatBetweenKeyframes(IUIAnimationStoryboard2* This,UI_ANIMATION_KEYFRAME startKeyframe,UI_ANIMATION_KEYFRAME endKeyframe,double cRepetition,UI_ANIMATION_REPEAT_MODE repeatMode,IUIAnimationLoopIterationChangeHandler2 *pIterationChangeHandler,UINT_PTR id,LONG fRegisterForNextAnimationEvent) {
    return This->lpVtbl->RepeatBetweenKeyframes(This,startKeyframe,endKeyframe,cRepetition,repeatMode,pIterationChangeHandler,id,fRegisterForNextAnimationEvent);
}
static inline HRESULT IUIAnimationStoryboard2_HoldVariable(IUIAnimationStoryboard2* This,IUIAnimationVariable2 *variable) {
    return This->lpVtbl->HoldVariable(This,variable);
}
static inline HRESULT IUIAnimationStoryboard2_SetLongestAcceptableDelay(IUIAnimationStoryboard2* This,double delay) {
    return This->lpVtbl->SetLongestAcceptableDelay(This,delay);
}
static inline HRESULT IUIAnimationStoryboard2_SetSkipDuration(IUIAnimationStoryboard2* This,double secondsDuration) {
    return This->lpVtbl->SetSkipDuration(This,secondsDuration);
}
static inline HRESULT IUIAnimationStoryboard2_Schedule(IUIAnimationStoryboard2* This,double timeNow,UI_ANIMATION_SCHEDULING_RESULT *schedulingResult) {
    return This->lpVtbl->Schedule(This,timeNow,schedulingResult);
}
static inline HRESULT IUIAnimationStoryboard2_Conclude(IUIAnimationStoryboard2* This) {
    return This->lpVtbl->Conclude(This);
}
static inline HRESULT IUIAnimationStoryboard2_Finish(IUIAnimationStoryboard2* This,double completionDeadline) {
    return This->lpVtbl->Finish(This,completionDeadline);
}
static inline HRESULT IUIAnimationStoryboard2_Abandon(IUIAnimationStoryboard2* This) {
    return This->lpVtbl->Abandon(This);
}
static inline HRESULT IUIAnimationStoryboard2_SetTag(IUIAnimationStoryboard2* This,IUnknown *object,unsigned int id) {
    return This->lpVtbl->SetTag(This,object,id);
}
static inline HRESULT IUIAnimationStoryboard2_GetTag(IUIAnimationStoryboard2* This,IUnknown **object,unsigned int *id) {
    return This->lpVtbl->GetTag(This,object,id);
}
static inline HRESULT IUIAnimationStoryboard2_GetStatus(IUIAnimationStoryboard2* This,UI_ANIMATION_STORYBOARD_STATUS *status) {
    return This->lpVtbl->GetStatus(This,status);
}
static inline HRESULT IUIAnimationStoryboard2_GetElapsedTime(IUIAnimationStoryboard2* This,double *elapsedTime) {
    return This->lpVtbl->GetElapsedTime(This,elapsedTime);
}
static inline HRESULT IUIAnimationStoryboard2_SetStoryboardEventHandler(IUIAnimationStoryboard2* This,IUIAnimationStoryboardEventHandler2 *handler,LONG fRegisterStatusChangeForNextAnimationEvent,LONG fRegisterUpdateForNextAnimationEvent) {
    return This->lpVtbl->SetStoryboardEventHandler(This,handler,fRegisterStatusChangeForNextAnimationEvent,fRegisterUpdateForNextAnimationEvent);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationStoryboard2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationTransition2 interface
 */
#ifndef __IUIAnimationTransition2_INTERFACE_DEFINED__
#define __IUIAnimationTransition2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationTransition2, 0x62ff9123, 0xa85a, 0x4e9b, 0xa2,0x18, 0x43,0x5a,0x93,0xe2,0x68,0xfd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("62ff9123-a85a-4e9b-a218-435a93e268fd")
IUIAnimationTransition2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDimension(
        unsigned int *dimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInitialValue(
        double value) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInitialVectorValue(
        double *value,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInitialVelocity(
        double velocity) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInitialVectorVelocity(
        double *velocity,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsDurationKnown(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDuration(
        double *duration) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationTransition2, 0x62ff9123, 0xa85a, 0x4e9b, 0xa2,0x18, 0x43,0x5a,0x93,0xe2,0x68,0xfd)
#endif
#else
typedef struct IUIAnimationTransition2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationTransition2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationTransition2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationTransition2 *This);

    /*** IUIAnimationTransition2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDimension)(
        IUIAnimationTransition2 *This,
        unsigned int *dimension);

    HRESULT (STDMETHODCALLTYPE *SetInitialValue)(
        IUIAnimationTransition2 *This,
        double value);

    HRESULT (STDMETHODCALLTYPE *SetInitialVectorValue)(
        IUIAnimationTransition2 *This,
        double *value,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *SetInitialVelocity)(
        IUIAnimationTransition2 *This,
        double velocity);

    HRESULT (STDMETHODCALLTYPE *SetInitialVectorVelocity)(
        IUIAnimationTransition2 *This,
        double *velocity,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *IsDurationKnown)(
        IUIAnimationTransition2 *This);

    HRESULT (STDMETHODCALLTYPE *GetDuration)(
        IUIAnimationTransition2 *This,
        double *duration);

    END_INTERFACE
} IUIAnimationTransition2Vtbl;

interface IUIAnimationTransition2 {
    CONST_VTBL IUIAnimationTransition2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationTransition2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationTransition2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationTransition2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationTransition2 methods ***/
#define IUIAnimationTransition2_GetDimension(This,dimension) (This)->lpVtbl->GetDimension(This,dimension)
#define IUIAnimationTransition2_SetInitialValue(This,value) (This)->lpVtbl->SetInitialValue(This,value)
#define IUIAnimationTransition2_SetInitialVectorValue(This,value,cDimension) (This)->lpVtbl->SetInitialVectorValue(This,value,cDimension)
#define IUIAnimationTransition2_SetInitialVelocity(This,velocity) (This)->lpVtbl->SetInitialVelocity(This,velocity)
#define IUIAnimationTransition2_SetInitialVectorVelocity(This,velocity,cDimension) (This)->lpVtbl->SetInitialVectorVelocity(This,velocity,cDimension)
#define IUIAnimationTransition2_IsDurationKnown(This) (This)->lpVtbl->IsDurationKnown(This)
#define IUIAnimationTransition2_GetDuration(This,duration) (This)->lpVtbl->GetDuration(This,duration)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationTransition2_QueryInterface(IUIAnimationTransition2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationTransition2_AddRef(IUIAnimationTransition2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationTransition2_Release(IUIAnimationTransition2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationTransition2 methods ***/
static inline HRESULT IUIAnimationTransition2_GetDimension(IUIAnimationTransition2* This,unsigned int *dimension) {
    return This->lpVtbl->GetDimension(This,dimension);
}
static inline HRESULT IUIAnimationTransition2_SetInitialValue(IUIAnimationTransition2* This,double value) {
    return This->lpVtbl->SetInitialValue(This,value);
}
static inline HRESULT IUIAnimationTransition2_SetInitialVectorValue(IUIAnimationTransition2* This,double *value,unsigned int cDimension) {
    return This->lpVtbl->SetInitialVectorValue(This,value,cDimension);
}
static inline HRESULT IUIAnimationTransition2_SetInitialVelocity(IUIAnimationTransition2* This,double velocity) {
    return This->lpVtbl->SetInitialVelocity(This,velocity);
}
static inline HRESULT IUIAnimationTransition2_SetInitialVectorVelocity(IUIAnimationTransition2* This,double *velocity,unsigned int cDimension) {
    return This->lpVtbl->SetInitialVectorVelocity(This,velocity,cDimension);
}
static inline HRESULT IUIAnimationTransition2_IsDurationKnown(IUIAnimationTransition2* This) {
    return This->lpVtbl->IsDurationKnown(This);
}
static inline HRESULT IUIAnimationTransition2_GetDuration(IUIAnimationTransition2* This,double *duration) {
    return This->lpVtbl->GetDuration(This,duration);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationTransition2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationLoopIterationChangeHandler2 interface
 */
#ifndef __IUIAnimationLoopIterationChangeHandler2_INTERFACE_DEFINED__
#define __IUIAnimationLoopIterationChangeHandler2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationLoopIterationChangeHandler2, 0x2d3b15a4, 0x4762, 0x47ab, 0xa0,0x30, 0xb2,0x32,0x21,0xdf,0x3a,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2d3b15a4-4762-47ab-a030-b23221df3ae0")
IUIAnimationLoopIterationChangeHandler2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnLoopIterationChanged(
        IUIAnimationStoryboard2 *storyboard,
        UINT_PTR id,
        unsigned int newIterationCount,
        unsigned int oldIterationCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationLoopIterationChangeHandler2, 0x2d3b15a4, 0x4762, 0x47ab, 0xa0,0x30, 0xb2,0x32,0x21,0xdf,0x3a,0xe0)
#endif
#else
typedef struct IUIAnimationLoopIterationChangeHandler2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationLoopIterationChangeHandler2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationLoopIterationChangeHandler2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationLoopIterationChangeHandler2 *This);

    /*** IUIAnimationLoopIterationChangeHandler2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnLoopIterationChanged)(
        IUIAnimationLoopIterationChangeHandler2 *This,
        IUIAnimationStoryboard2 *storyboard,
        UINT_PTR id,
        unsigned int newIterationCount,
        unsigned int oldIterationCount);

    END_INTERFACE
} IUIAnimationLoopIterationChangeHandler2Vtbl;

interface IUIAnimationLoopIterationChangeHandler2 {
    CONST_VTBL IUIAnimationLoopIterationChangeHandler2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationLoopIterationChangeHandler2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationLoopIterationChangeHandler2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationLoopIterationChangeHandler2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationLoopIterationChangeHandler2 methods ***/
#define IUIAnimationLoopIterationChangeHandler2_OnLoopIterationChanged(This,storyboard,id,newIterationCount,oldIterationCount) (This)->lpVtbl->OnLoopIterationChanged(This,storyboard,id,newIterationCount,oldIterationCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationLoopIterationChangeHandler2_QueryInterface(IUIAnimationLoopIterationChangeHandler2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationLoopIterationChangeHandler2_AddRef(IUIAnimationLoopIterationChangeHandler2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationLoopIterationChangeHandler2_Release(IUIAnimationLoopIterationChangeHandler2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationLoopIterationChangeHandler2 methods ***/
static inline HRESULT IUIAnimationLoopIterationChangeHandler2_OnLoopIterationChanged(IUIAnimationLoopIterationChangeHandler2* This,IUIAnimationStoryboard2 *storyboard,UINT_PTR id,unsigned int newIterationCount,unsigned int oldIterationCount) {
    return This->lpVtbl->OnLoopIterationChanged(This,storyboard,id,newIterationCount,oldIterationCount);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationLoopIterationChangeHandler2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationStoryboardEventHandler2 interface
 */
#ifndef __IUIAnimationStoryboardEventHandler2_INTERFACE_DEFINED__
#define __IUIAnimationStoryboardEventHandler2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationStoryboardEventHandler2, 0xbac5f55a, 0xba7c, 0x414c, 0xb5,0x99, 0xfb,0xf8,0x50,0xf5,0x53,0xc6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bac5f55a-ba7c-414c-b599-fbf850f553c6")
IUIAnimationStoryboardEventHandler2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnStoryboardStatusChanged(
        IUIAnimationStoryboard2 *storyboard,
        UI_ANIMATION_STORYBOARD_STATUS newStatus,
        UI_ANIMATION_STORYBOARD_STATUS previousStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnStoryboardUpdated(
        IUIAnimationStoryboard2 *storyboard) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationStoryboardEventHandler2, 0xbac5f55a, 0xba7c, 0x414c, 0xb5,0x99, 0xfb,0xf8,0x50,0xf5,0x53,0xc6)
#endif
#else
typedef struct IUIAnimationStoryboardEventHandler2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationStoryboardEventHandler2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationStoryboardEventHandler2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationStoryboardEventHandler2 *This);

    /*** IUIAnimationStoryboardEventHandler2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnStoryboardStatusChanged)(
        IUIAnimationStoryboardEventHandler2 *This,
        IUIAnimationStoryboard2 *storyboard,
        UI_ANIMATION_STORYBOARD_STATUS newStatus,
        UI_ANIMATION_STORYBOARD_STATUS previousStatus);

    HRESULT (STDMETHODCALLTYPE *OnStoryboardUpdated)(
        IUIAnimationStoryboardEventHandler2 *This,
        IUIAnimationStoryboard2 *storyboard);

    END_INTERFACE
} IUIAnimationStoryboardEventHandler2Vtbl;

interface IUIAnimationStoryboardEventHandler2 {
    CONST_VTBL IUIAnimationStoryboardEventHandler2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationStoryboardEventHandler2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationStoryboardEventHandler2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationStoryboardEventHandler2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationStoryboardEventHandler2 methods ***/
#define IUIAnimationStoryboardEventHandler2_OnStoryboardStatusChanged(This,storyboard,newStatus,previousStatus) (This)->lpVtbl->OnStoryboardStatusChanged(This,storyboard,newStatus,previousStatus)
#define IUIAnimationStoryboardEventHandler2_OnStoryboardUpdated(This,storyboard) (This)->lpVtbl->OnStoryboardUpdated(This,storyboard)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationStoryboardEventHandler2_QueryInterface(IUIAnimationStoryboardEventHandler2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationStoryboardEventHandler2_AddRef(IUIAnimationStoryboardEventHandler2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationStoryboardEventHandler2_Release(IUIAnimationStoryboardEventHandler2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationStoryboardEventHandler2 methods ***/
static inline HRESULT IUIAnimationStoryboardEventHandler2_OnStoryboardStatusChanged(IUIAnimationStoryboardEventHandler2* This,IUIAnimationStoryboard2 *storyboard,UI_ANIMATION_STORYBOARD_STATUS newStatus,UI_ANIMATION_STORYBOARD_STATUS previousStatus) {
    return This->lpVtbl->OnStoryboardStatusChanged(This,storyboard,newStatus,previousStatus);
}
static inline HRESULT IUIAnimationStoryboardEventHandler2_OnStoryboardUpdated(IUIAnimationStoryboardEventHandler2* This,IUIAnimationStoryboard2 *storyboard) {
    return This->lpVtbl->OnStoryboardUpdated(This,storyboard);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationStoryboardEventHandler2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationVariableChangeHandler2 interface
 */
#ifndef __IUIAnimationVariableChangeHandler2_INTERFACE_DEFINED__
#define __IUIAnimationVariableChangeHandler2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationVariableChangeHandler2, 0x63acc8d2, 0x6eae, 0x4bb0, 0xb8,0x79, 0x58,0x6d,0xd8,0xcf,0xbe,0x42);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("63acc8d2-6eae-4bb0-b879-586dd8cfbe42")
IUIAnimationVariableChangeHandler2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnValueChanged(
        IUIAnimationStoryboard2 *storyboard,
        IUIAnimationVariable2 *variable,
        double *newValue,
        double *previousValue,
        unsigned int cDimension) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationVariableChangeHandler2, 0x63acc8d2, 0x6eae, 0x4bb0, 0xb8,0x79, 0x58,0x6d,0xd8,0xcf,0xbe,0x42)
#endif
#else
typedef struct IUIAnimationVariableChangeHandler2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationVariableChangeHandler2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationVariableChangeHandler2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationVariableChangeHandler2 *This);

    /*** IUIAnimationVariableChangeHandler2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnValueChanged)(
        IUIAnimationVariableChangeHandler2 *This,
        IUIAnimationStoryboard2 *storyboard,
        IUIAnimationVariable2 *variable,
        double *newValue,
        double *previousValue,
        unsigned int cDimension);

    END_INTERFACE
} IUIAnimationVariableChangeHandler2Vtbl;

interface IUIAnimationVariableChangeHandler2 {
    CONST_VTBL IUIAnimationVariableChangeHandler2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationVariableChangeHandler2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationVariableChangeHandler2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationVariableChangeHandler2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationVariableChangeHandler2 methods ***/
#define IUIAnimationVariableChangeHandler2_OnValueChanged(This,storyboard,variable,newValue,previousValue,cDimension) (This)->lpVtbl->OnValueChanged(This,storyboard,variable,newValue,previousValue,cDimension)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationVariableChangeHandler2_QueryInterface(IUIAnimationVariableChangeHandler2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationVariableChangeHandler2_AddRef(IUIAnimationVariableChangeHandler2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationVariableChangeHandler2_Release(IUIAnimationVariableChangeHandler2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationVariableChangeHandler2 methods ***/
static inline HRESULT IUIAnimationVariableChangeHandler2_OnValueChanged(IUIAnimationVariableChangeHandler2* This,IUIAnimationStoryboard2 *storyboard,IUIAnimationVariable2 *variable,double *newValue,double *previousValue,unsigned int cDimension) {
    return This->lpVtbl->OnValueChanged(This,storyboard,variable,newValue,previousValue,cDimension);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationVariableChangeHandler2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationVariableIntegerChangeHandler2 interface
 */
#ifndef __IUIAnimationVariableIntegerChangeHandler2_INTERFACE_DEFINED__
#define __IUIAnimationVariableIntegerChangeHandler2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationVariableIntegerChangeHandler2, 0x829b6cf1, 0x4f3a, 0x4412, 0xae,0x09, 0xb2,0x43,0xeb,0x4c,0x6b,0x58);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("829b6cf1-4f3a-4412-ae09-b243eb4c6b58")
IUIAnimationVariableIntegerChangeHandler2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnIntegerValueChanged(
        IUIAnimationStoryboard2 *storyboard,
        IUIAnimationVariable2 *variable,
        int *newValue,
        int *previousValue,
        unsigned int cDimension) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationVariableIntegerChangeHandler2, 0x829b6cf1, 0x4f3a, 0x4412, 0xae,0x09, 0xb2,0x43,0xeb,0x4c,0x6b,0x58)
#endif
#else
typedef struct IUIAnimationVariableIntegerChangeHandler2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationVariableIntegerChangeHandler2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationVariableIntegerChangeHandler2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationVariableIntegerChangeHandler2 *This);

    /*** IUIAnimationVariableIntegerChangeHandler2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnIntegerValueChanged)(
        IUIAnimationVariableIntegerChangeHandler2 *This,
        IUIAnimationStoryboard2 *storyboard,
        IUIAnimationVariable2 *variable,
        int *newValue,
        int *previousValue,
        unsigned int cDimension);

    END_INTERFACE
} IUIAnimationVariableIntegerChangeHandler2Vtbl;

interface IUIAnimationVariableIntegerChangeHandler2 {
    CONST_VTBL IUIAnimationVariableIntegerChangeHandler2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationVariableIntegerChangeHandler2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationVariableIntegerChangeHandler2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationVariableIntegerChangeHandler2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationVariableIntegerChangeHandler2 methods ***/
#define IUIAnimationVariableIntegerChangeHandler2_OnIntegerValueChanged(This,storyboard,variable,newValue,previousValue,cDimension) (This)->lpVtbl->OnIntegerValueChanged(This,storyboard,variable,newValue,previousValue,cDimension)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationVariableIntegerChangeHandler2_QueryInterface(IUIAnimationVariableIntegerChangeHandler2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationVariableIntegerChangeHandler2_AddRef(IUIAnimationVariableIntegerChangeHandler2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationVariableIntegerChangeHandler2_Release(IUIAnimationVariableIntegerChangeHandler2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationVariableIntegerChangeHandler2 methods ***/
static inline HRESULT IUIAnimationVariableIntegerChangeHandler2_OnIntegerValueChanged(IUIAnimationVariableIntegerChangeHandler2* This,IUIAnimationStoryboard2 *storyboard,IUIAnimationVariable2 *variable,int *newValue,int *previousValue,unsigned int cDimension) {
    return This->lpVtbl->OnIntegerValueChanged(This,storyboard,variable,newValue,previousValue,cDimension);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationVariableIntegerChangeHandler2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationVariableCurveChangeHandler2 interface
 */
#ifndef __IUIAnimationVariableCurveChangeHandler2_INTERFACE_DEFINED__
#define __IUIAnimationVariableCurveChangeHandler2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationVariableCurveChangeHandler2, 0x72895e91, 0x0145, 0x4c21, 0x91,0x92, 0x5a,0xab,0x40,0xed,0xdf,0x80);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("72895e91-0145-4c21-9192-5aab40eddf80")
IUIAnimationVariableCurveChangeHandler2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnCurveChanged(
        IUIAnimationVariable2 *variable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationVariableCurveChangeHandler2, 0x72895e91, 0x0145, 0x4c21, 0x91,0x92, 0x5a,0xab,0x40,0xed,0xdf,0x80)
#endif
#else
typedef struct IUIAnimationVariableCurveChangeHandler2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationVariableCurveChangeHandler2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationVariableCurveChangeHandler2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationVariableCurveChangeHandler2 *This);

    /*** IUIAnimationVariableCurveChangeHandler2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnCurveChanged)(
        IUIAnimationVariableCurveChangeHandler2 *This,
        IUIAnimationVariable2 *variable);

    END_INTERFACE
} IUIAnimationVariableCurveChangeHandler2Vtbl;

interface IUIAnimationVariableCurveChangeHandler2 {
    CONST_VTBL IUIAnimationVariableCurveChangeHandler2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationVariableCurveChangeHandler2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationVariableCurveChangeHandler2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationVariableCurveChangeHandler2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationVariableCurveChangeHandler2 methods ***/
#define IUIAnimationVariableCurveChangeHandler2_OnCurveChanged(This,variable) (This)->lpVtbl->OnCurveChanged(This,variable)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationVariableCurveChangeHandler2_QueryInterface(IUIAnimationVariableCurveChangeHandler2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationVariableCurveChangeHandler2_AddRef(IUIAnimationVariableCurveChangeHandler2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationVariableCurveChangeHandler2_Release(IUIAnimationVariableCurveChangeHandler2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationVariableCurveChangeHandler2 methods ***/
static inline HRESULT IUIAnimationVariableCurveChangeHandler2_OnCurveChanged(IUIAnimationVariableCurveChangeHandler2* This,IUIAnimationVariable2 *variable) {
    return This->lpVtbl->OnCurveChanged(This,variable);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationVariableCurveChangeHandler2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationManagerEventHandler2 interface
 */
#ifndef __IUIAnimationManagerEventHandler2_INTERFACE_DEFINED__
#define __IUIAnimationManagerEventHandler2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationManagerEventHandler2, 0xf6e022ba, 0xbff3, 0x42ec, 0x90,0x33, 0xe0,0x73,0xf3,0x3e,0x83,0xc3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f6e022ba-bff3-42ec-9033-e073f33e83c3")
IUIAnimationManagerEventHandler2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnManagerStatusChanged(
        UI_ANIMATION_MANAGER_STATUS newStatus,
        UI_ANIMATION_MANAGER_STATUS previousStatus) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationManagerEventHandler2, 0xf6e022ba, 0xbff3, 0x42ec, 0x90,0x33, 0xe0,0x73,0xf3,0x3e,0x83,0xc3)
#endif
#else
typedef struct IUIAnimationManagerEventHandler2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationManagerEventHandler2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationManagerEventHandler2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationManagerEventHandler2 *This);

    /*** IUIAnimationManagerEventHandler2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnManagerStatusChanged)(
        IUIAnimationManagerEventHandler2 *This,
        UI_ANIMATION_MANAGER_STATUS newStatus,
        UI_ANIMATION_MANAGER_STATUS previousStatus);

    END_INTERFACE
} IUIAnimationManagerEventHandler2Vtbl;

interface IUIAnimationManagerEventHandler2 {
    CONST_VTBL IUIAnimationManagerEventHandler2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationManagerEventHandler2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationManagerEventHandler2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationManagerEventHandler2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationManagerEventHandler2 methods ***/
#define IUIAnimationManagerEventHandler2_OnManagerStatusChanged(This,newStatus,previousStatus) (This)->lpVtbl->OnManagerStatusChanged(This,newStatus,previousStatus)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationManagerEventHandler2_QueryInterface(IUIAnimationManagerEventHandler2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationManagerEventHandler2_AddRef(IUIAnimationManagerEventHandler2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationManagerEventHandler2_Release(IUIAnimationManagerEventHandler2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationManagerEventHandler2 methods ***/
static inline HRESULT IUIAnimationManagerEventHandler2_OnManagerStatusChanged(IUIAnimationManagerEventHandler2* This,UI_ANIMATION_MANAGER_STATUS newStatus,UI_ANIMATION_MANAGER_STATUS previousStatus) {
    return This->lpVtbl->OnManagerStatusChanged(This,newStatus,previousStatus);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationManagerEventHandler2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationPriorityComparison2 interface
 */
#ifndef __IUIAnimationPriorityComparison2_INTERFACE_DEFINED__
#define __IUIAnimationPriorityComparison2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationPriorityComparison2, 0x5b6d7a37, 0x4621, 0x467c, 0x8b,0x05, 0x70,0x13,0x1d,0xe6,0x2d,0xdb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5b6d7a37-4621-467c-8b05-70131de62ddb")
IUIAnimationPriorityComparison2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE HasPriority(
        IUIAnimationStoryboard2 *scheduledStoryboard,
        IUIAnimationStoryboard2 *newStoryboard,
        UI_ANIMATION_PRIORITY_EFFECT priorityEffect) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationPriorityComparison2, 0x5b6d7a37, 0x4621, 0x467c, 0x8b,0x05, 0x70,0x13,0x1d,0xe6,0x2d,0xdb)
#endif
#else
typedef struct IUIAnimationPriorityComparison2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationPriorityComparison2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationPriorityComparison2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationPriorityComparison2 *This);

    /*** IUIAnimationPriorityComparison2 methods ***/
    HRESULT (STDMETHODCALLTYPE *HasPriority)(
        IUIAnimationPriorityComparison2 *This,
        IUIAnimationStoryboard2 *scheduledStoryboard,
        IUIAnimationStoryboard2 *newStoryboard,
        UI_ANIMATION_PRIORITY_EFFECT priorityEffect);

    END_INTERFACE
} IUIAnimationPriorityComparison2Vtbl;

interface IUIAnimationPriorityComparison2 {
    CONST_VTBL IUIAnimationPriorityComparison2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationPriorityComparison2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationPriorityComparison2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationPriorityComparison2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationPriorityComparison2 methods ***/
#define IUIAnimationPriorityComparison2_HasPriority(This,scheduledStoryboard,newStoryboard,priorityEffect) (This)->lpVtbl->HasPriority(This,scheduledStoryboard,newStoryboard,priorityEffect)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationPriorityComparison2_QueryInterface(IUIAnimationPriorityComparison2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationPriorityComparison2_AddRef(IUIAnimationPriorityComparison2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationPriorityComparison2_Release(IUIAnimationPriorityComparison2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationPriorityComparison2 methods ***/
static inline HRESULT IUIAnimationPriorityComparison2_HasPriority(IUIAnimationPriorityComparison2* This,IUIAnimationStoryboard2 *scheduledStoryboard,IUIAnimationStoryboard2 *newStoryboard,UI_ANIMATION_PRIORITY_EFFECT priorityEffect) {
    return This->lpVtbl->HasPriority(This,scheduledStoryboard,newStoryboard,priorityEffect);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationPriorityComparison2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationTransitionLibrary interface
 */
#ifndef __IUIAnimationTransitionLibrary_INTERFACE_DEFINED__
#define __IUIAnimationTransitionLibrary_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationTransitionLibrary, 0xca5a14b1, 0xd24f, 0x48b8, 0x8f,0xe4, 0xc7,0x81,0x69,0xba,0x95,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ca5a14b1-d24f-48b8-8fe4-c78169ba954e")
IUIAnimationTransitionLibrary : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateInstantaneousTransition(
        double finalValue,
        IUIAnimationTransition **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateConstantTransition(
        double duration,
        IUIAnimationTransition **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDiscreteTransition(
        double delay,
        double finalValue,
        double hold,
        IUIAnimationTransition **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateLinearTransition(
        double duration,
        double finalValue,
        IUIAnimationTransition **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateLinearTransitionFromSpeed(
        double speed,
        double finalValue,
        IUIAnimationTransition **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSinusoidalTransitionFromVelocity(
        double duration,
        double period,
        IUIAnimationTransition **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSinusoidalTransitionFromRange(
        double duration,
        double minimumValue,
        double maximumValue,
        double period,
        UI_ANIMATION_SLOPE slope,
        IUIAnimationTransition **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateAccelerateDecelerateTransition(
        double duration,
        double finalValue,
        double accelerationRatio,
        double decelerationRatio,
        IUIAnimationTransition **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateReversalTransition(
        double duration,
        IUIAnimationTransition **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCubicTransition(
        double duration,
        double finalValue,
        double finalVelocity,
        IUIAnimationTransition **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSmoothStopTransition(
        double maximumDuration,
        double finalValue,
        IUIAnimationTransition **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateParabolicTransitionFromAcceleration(
        double finalValue,
        double finalVelocity,
        double acceleration,
        IUIAnimationTransition **transition) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationTransitionLibrary, 0xca5a14b1, 0xd24f, 0x48b8, 0x8f,0xe4, 0xc7,0x81,0x69,0xba,0x95,0x4e)
#endif
#else
typedef struct IUIAnimationTransitionLibraryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationTransitionLibrary *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationTransitionLibrary *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationTransitionLibrary *This);

    /*** IUIAnimationTransitionLibrary methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstantaneousTransition)(
        IUIAnimationTransitionLibrary *This,
        double finalValue,
        IUIAnimationTransition **transition);

    HRESULT (STDMETHODCALLTYPE *CreateConstantTransition)(
        IUIAnimationTransitionLibrary *This,
        double duration,
        IUIAnimationTransition **transition);

    HRESULT (STDMETHODCALLTYPE *CreateDiscreteTransition)(
        IUIAnimationTransitionLibrary *This,
        double delay,
        double finalValue,
        double hold,
        IUIAnimationTransition **transition);

    HRESULT (STDMETHODCALLTYPE *CreateLinearTransition)(
        IUIAnimationTransitionLibrary *This,
        double duration,
        double finalValue,
        IUIAnimationTransition **transition);

    HRESULT (STDMETHODCALLTYPE *CreateLinearTransitionFromSpeed)(
        IUIAnimationTransitionLibrary *This,
        double speed,
        double finalValue,
        IUIAnimationTransition **transition);

    HRESULT (STDMETHODCALLTYPE *CreateSinusoidalTransitionFromVelocity)(
        IUIAnimationTransitionLibrary *This,
        double duration,
        double period,
        IUIAnimationTransition **transition);

    HRESULT (STDMETHODCALLTYPE *CreateSinusoidalTransitionFromRange)(
        IUIAnimationTransitionLibrary *This,
        double duration,
        double minimumValue,
        double maximumValue,
        double period,
        UI_ANIMATION_SLOPE slope,
        IUIAnimationTransition **transition);

    HRESULT (STDMETHODCALLTYPE *CreateAccelerateDecelerateTransition)(
        IUIAnimationTransitionLibrary *This,
        double duration,
        double finalValue,
        double accelerationRatio,
        double decelerationRatio,
        IUIAnimationTransition **transition);

    HRESULT (STDMETHODCALLTYPE *CreateReversalTransition)(
        IUIAnimationTransitionLibrary *This,
        double duration,
        IUIAnimationTransition **transition);

    HRESULT (STDMETHODCALLTYPE *CreateCubicTransition)(
        IUIAnimationTransitionLibrary *This,
        double duration,
        double finalValue,
        double finalVelocity,
        IUIAnimationTransition **transition);

    HRESULT (STDMETHODCALLTYPE *CreateSmoothStopTransition)(
        IUIAnimationTransitionLibrary *This,
        double maximumDuration,
        double finalValue,
        IUIAnimationTransition **transition);

    HRESULT (STDMETHODCALLTYPE *CreateParabolicTransitionFromAcceleration)(
        IUIAnimationTransitionLibrary *This,
        double finalValue,
        double finalVelocity,
        double acceleration,
        IUIAnimationTransition **transition);

    END_INTERFACE
} IUIAnimationTransitionLibraryVtbl;

interface IUIAnimationTransitionLibrary {
    CONST_VTBL IUIAnimationTransitionLibraryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationTransitionLibrary_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationTransitionLibrary_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationTransitionLibrary_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationTransitionLibrary methods ***/
#define IUIAnimationTransitionLibrary_CreateInstantaneousTransition(This,finalValue,transition) (This)->lpVtbl->CreateInstantaneousTransition(This,finalValue,transition)
#define IUIAnimationTransitionLibrary_CreateConstantTransition(This,duration,transition) (This)->lpVtbl->CreateConstantTransition(This,duration,transition)
#define IUIAnimationTransitionLibrary_CreateDiscreteTransition(This,delay,finalValue,hold,transition) (This)->lpVtbl->CreateDiscreteTransition(This,delay,finalValue,hold,transition)
#define IUIAnimationTransitionLibrary_CreateLinearTransition(This,duration,finalValue,transition) (This)->lpVtbl->CreateLinearTransition(This,duration,finalValue,transition)
#define IUIAnimationTransitionLibrary_CreateLinearTransitionFromSpeed(This,speed,finalValue,transition) (This)->lpVtbl->CreateLinearTransitionFromSpeed(This,speed,finalValue,transition)
#define IUIAnimationTransitionLibrary_CreateSinusoidalTransitionFromVelocity(This,duration,period,transition) (This)->lpVtbl->CreateSinusoidalTransitionFromVelocity(This,duration,period,transition)
#define IUIAnimationTransitionLibrary_CreateSinusoidalTransitionFromRange(This,duration,minimumValue,maximumValue,period,slope,transition) (This)->lpVtbl->CreateSinusoidalTransitionFromRange(This,duration,minimumValue,maximumValue,period,slope,transition)
#define IUIAnimationTransitionLibrary_CreateAccelerateDecelerateTransition(This,duration,finalValue,accelerationRatio,decelerationRatio,transition) (This)->lpVtbl->CreateAccelerateDecelerateTransition(This,duration,finalValue,accelerationRatio,decelerationRatio,transition)
#define IUIAnimationTransitionLibrary_CreateReversalTransition(This,duration,transition) (This)->lpVtbl->CreateReversalTransition(This,duration,transition)
#define IUIAnimationTransitionLibrary_CreateCubicTransition(This,duration,finalValue,finalVelocity,transition) (This)->lpVtbl->CreateCubicTransition(This,duration,finalValue,finalVelocity,transition)
#define IUIAnimationTransitionLibrary_CreateSmoothStopTransition(This,maximumDuration,finalValue,transition) (This)->lpVtbl->CreateSmoothStopTransition(This,maximumDuration,finalValue,transition)
#define IUIAnimationTransitionLibrary_CreateParabolicTransitionFromAcceleration(This,finalValue,finalVelocity,acceleration,transition) (This)->lpVtbl->CreateParabolicTransitionFromAcceleration(This,finalValue,finalVelocity,acceleration,transition)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationTransitionLibrary_QueryInterface(IUIAnimationTransitionLibrary* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationTransitionLibrary_AddRef(IUIAnimationTransitionLibrary* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationTransitionLibrary_Release(IUIAnimationTransitionLibrary* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationTransitionLibrary methods ***/
static inline HRESULT IUIAnimationTransitionLibrary_CreateInstantaneousTransition(IUIAnimationTransitionLibrary* This,double finalValue,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateInstantaneousTransition(This,finalValue,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary_CreateConstantTransition(IUIAnimationTransitionLibrary* This,double duration,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateConstantTransition(This,duration,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary_CreateDiscreteTransition(IUIAnimationTransitionLibrary* This,double delay,double finalValue,double hold,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateDiscreteTransition(This,delay,finalValue,hold,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary_CreateLinearTransition(IUIAnimationTransitionLibrary* This,double duration,double finalValue,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateLinearTransition(This,duration,finalValue,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary_CreateLinearTransitionFromSpeed(IUIAnimationTransitionLibrary* This,double speed,double finalValue,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateLinearTransitionFromSpeed(This,speed,finalValue,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary_CreateSinusoidalTransitionFromVelocity(IUIAnimationTransitionLibrary* This,double duration,double period,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateSinusoidalTransitionFromVelocity(This,duration,period,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary_CreateSinusoidalTransitionFromRange(IUIAnimationTransitionLibrary* This,double duration,double minimumValue,double maximumValue,double period,UI_ANIMATION_SLOPE slope,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateSinusoidalTransitionFromRange(This,duration,minimumValue,maximumValue,period,slope,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary_CreateAccelerateDecelerateTransition(IUIAnimationTransitionLibrary* This,double duration,double finalValue,double accelerationRatio,double decelerationRatio,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateAccelerateDecelerateTransition(This,duration,finalValue,accelerationRatio,decelerationRatio,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary_CreateReversalTransition(IUIAnimationTransitionLibrary* This,double duration,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateReversalTransition(This,duration,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary_CreateCubicTransition(IUIAnimationTransitionLibrary* This,double duration,double finalValue,double finalVelocity,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateCubicTransition(This,duration,finalValue,finalVelocity,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary_CreateSmoothStopTransition(IUIAnimationTransitionLibrary* This,double maximumDuration,double finalValue,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateSmoothStopTransition(This,maximumDuration,finalValue,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary_CreateParabolicTransitionFromAcceleration(IUIAnimationTransitionLibrary* This,double finalValue,double finalVelocity,double acceleration,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateParabolicTransitionFromAcceleration(This,finalValue,finalVelocity,acceleration,transition);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationTransitionLibrary_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationTransitionLibrary2 interface
 */
#ifndef __IUIAnimationTransitionLibrary2_INTERFACE_DEFINED__
#define __IUIAnimationTransitionLibrary2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationTransitionLibrary2, 0x03cfae53, 0x9580, 0x4ee3, 0xb3,0x63, 0x2e,0xce,0x51,0xb4,0xaf,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("03cfae53-9580-4ee3-b363-2ece51b4af6a")
IUIAnimationTransitionLibrary2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateInstantaneousTransition(
        double finalValue,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstantaneousVectorTransition(
        double *finalValue,
        unsigned int cDimension,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateConstantTransition(
        double duration,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDiscreteTransition(
        double delay,
        double finalValue,
        double hold,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDiscreteVectorTransition(
        double delay,
        double *finalValue,
        unsigned int cDimension,
        double hold,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateLinearTransition(
        double duration,
        double finalValue,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateLinearVectorTransition(
        double duration,
        double *finalValue,
        unsigned int cDimension,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateLinearTransitionFromSpeed(
        double speed,
        double finalValue,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateLinearVectorTransitionFromSpeed(
        double speed,
        double *finalValue,
        unsigned int cDimension,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSinusoidalTransitionFromVelocity(
        double duration,
        double period,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSinusoidalTransitionFromRange(
        double duration,
        double minimumValue,
        double maximumValue,
        double period,
        UI_ANIMATION_SLOPE slope,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateAccelerateDecelerateTransition(
        double duration,
        double finalValue,
        double accelerationRatio,
        double decelerationRatio,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateReversalTransition(
        double duration,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCubicTransition(
        double duration,
        double finalValue,
        double finalVelocity,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCubicVectorTransition(
        double duration,
        double *finalValue,
        double *finalVelocity,
        unsigned int cDimension,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSmoothStopTransition(
        double maximumDuration,
        double finalValue,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateParabolicTransitionFromAcceleration(
        double finalValue,
        double finalVelocity,
        double acceleration,
        IUIAnimationTransition2 **transition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCubicBezierLinearTransition(
        double duration,
        double finalValue,
        double x1,
        double y1,
        double x2,
        double y2,
        IUIAnimationTransition2 **ppTransition) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCubicBezierLinearVectorTransition(
        double duration,
        double *finalValue,
        unsigned int cDimension,
        double x1,
        double y1,
        double x2,
        double y2,
        IUIAnimationTransition2 **ppTransition) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationTransitionLibrary2, 0x03cfae53, 0x9580, 0x4ee3, 0xb3,0x63, 0x2e,0xce,0x51,0xb4,0xaf,0x6a)
#endif
#else
typedef struct IUIAnimationTransitionLibrary2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationTransitionLibrary2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationTransitionLibrary2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationTransitionLibrary2 *This);

    /*** IUIAnimationTransitionLibrary2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstantaneousTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double finalValue,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateInstantaneousVectorTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double *finalValue,
        unsigned int cDimension,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateConstantTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double duration,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateDiscreteTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double delay,
        double finalValue,
        double hold,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateDiscreteVectorTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double delay,
        double *finalValue,
        unsigned int cDimension,
        double hold,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateLinearTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double duration,
        double finalValue,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateLinearVectorTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double duration,
        double *finalValue,
        unsigned int cDimension,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateLinearTransitionFromSpeed)(
        IUIAnimationTransitionLibrary2 *This,
        double speed,
        double finalValue,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateLinearVectorTransitionFromSpeed)(
        IUIAnimationTransitionLibrary2 *This,
        double speed,
        double *finalValue,
        unsigned int cDimension,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateSinusoidalTransitionFromVelocity)(
        IUIAnimationTransitionLibrary2 *This,
        double duration,
        double period,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateSinusoidalTransitionFromRange)(
        IUIAnimationTransitionLibrary2 *This,
        double duration,
        double minimumValue,
        double maximumValue,
        double period,
        UI_ANIMATION_SLOPE slope,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateAccelerateDecelerateTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double duration,
        double finalValue,
        double accelerationRatio,
        double decelerationRatio,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateReversalTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double duration,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateCubicTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double duration,
        double finalValue,
        double finalVelocity,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateCubicVectorTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double duration,
        double *finalValue,
        double *finalVelocity,
        unsigned int cDimension,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateSmoothStopTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double maximumDuration,
        double finalValue,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateParabolicTransitionFromAcceleration)(
        IUIAnimationTransitionLibrary2 *This,
        double finalValue,
        double finalVelocity,
        double acceleration,
        IUIAnimationTransition2 **transition);

    HRESULT (STDMETHODCALLTYPE *CreateCubicBezierLinearTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double duration,
        double finalValue,
        double x1,
        double y1,
        double x2,
        double y2,
        IUIAnimationTransition2 **ppTransition);

    HRESULT (STDMETHODCALLTYPE *CreateCubicBezierLinearVectorTransition)(
        IUIAnimationTransitionLibrary2 *This,
        double duration,
        double *finalValue,
        unsigned int cDimension,
        double x1,
        double y1,
        double x2,
        double y2,
        IUIAnimationTransition2 **ppTransition);

    END_INTERFACE
} IUIAnimationTransitionLibrary2Vtbl;

interface IUIAnimationTransitionLibrary2 {
    CONST_VTBL IUIAnimationTransitionLibrary2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationTransitionLibrary2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationTransitionLibrary2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationTransitionLibrary2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationTransitionLibrary2 methods ***/
#define IUIAnimationTransitionLibrary2_CreateInstantaneousTransition(This,finalValue,transition) (This)->lpVtbl->CreateInstantaneousTransition(This,finalValue,transition)
#define IUIAnimationTransitionLibrary2_CreateInstantaneousVectorTransition(This,finalValue,cDimension,transition) (This)->lpVtbl->CreateInstantaneousVectorTransition(This,finalValue,cDimension,transition)
#define IUIAnimationTransitionLibrary2_CreateConstantTransition(This,duration,transition) (This)->lpVtbl->CreateConstantTransition(This,duration,transition)
#define IUIAnimationTransitionLibrary2_CreateDiscreteTransition(This,delay,finalValue,hold,transition) (This)->lpVtbl->CreateDiscreteTransition(This,delay,finalValue,hold,transition)
#define IUIAnimationTransitionLibrary2_CreateDiscreteVectorTransition(This,delay,finalValue,cDimension,hold,transition) (This)->lpVtbl->CreateDiscreteVectorTransition(This,delay,finalValue,cDimension,hold,transition)
#define IUIAnimationTransitionLibrary2_CreateLinearTransition(This,duration,finalValue,transition) (This)->lpVtbl->CreateLinearTransition(This,duration,finalValue,transition)
#define IUIAnimationTransitionLibrary2_CreateLinearVectorTransition(This,duration,finalValue,cDimension,transition) (This)->lpVtbl->CreateLinearVectorTransition(This,duration,finalValue,cDimension,transition)
#define IUIAnimationTransitionLibrary2_CreateLinearTransitionFromSpeed(This,speed,finalValue,transition) (This)->lpVtbl->CreateLinearTransitionFromSpeed(This,speed,finalValue,transition)
#define IUIAnimationTransitionLibrary2_CreateLinearVectorTransitionFromSpeed(This,speed,finalValue,cDimension,transition) (This)->lpVtbl->CreateLinearVectorTransitionFromSpeed(This,speed,finalValue,cDimension,transition)
#define IUIAnimationTransitionLibrary2_CreateSinusoidalTransitionFromVelocity(This,duration,period,transition) (This)->lpVtbl->CreateSinusoidalTransitionFromVelocity(This,duration,period,transition)
#define IUIAnimationTransitionLibrary2_CreateSinusoidalTransitionFromRange(This,duration,minimumValue,maximumValue,period,slope,transition) (This)->lpVtbl->CreateSinusoidalTransitionFromRange(This,duration,minimumValue,maximumValue,period,slope,transition)
#define IUIAnimationTransitionLibrary2_CreateAccelerateDecelerateTransition(This,duration,finalValue,accelerationRatio,decelerationRatio,transition) (This)->lpVtbl->CreateAccelerateDecelerateTransition(This,duration,finalValue,accelerationRatio,decelerationRatio,transition)
#define IUIAnimationTransitionLibrary2_CreateReversalTransition(This,duration,transition) (This)->lpVtbl->CreateReversalTransition(This,duration,transition)
#define IUIAnimationTransitionLibrary2_CreateCubicTransition(This,duration,finalValue,finalVelocity,transition) (This)->lpVtbl->CreateCubicTransition(This,duration,finalValue,finalVelocity,transition)
#define IUIAnimationTransitionLibrary2_CreateCubicVectorTransition(This,duration,finalValue,finalVelocity,cDimension,transition) (This)->lpVtbl->CreateCubicVectorTransition(This,duration,finalValue,finalVelocity,cDimension,transition)
#define IUIAnimationTransitionLibrary2_CreateSmoothStopTransition(This,maximumDuration,finalValue,transition) (This)->lpVtbl->CreateSmoothStopTransition(This,maximumDuration,finalValue,transition)
#define IUIAnimationTransitionLibrary2_CreateParabolicTransitionFromAcceleration(This,finalValue,finalVelocity,acceleration,transition) (This)->lpVtbl->CreateParabolicTransitionFromAcceleration(This,finalValue,finalVelocity,acceleration,transition)
#define IUIAnimationTransitionLibrary2_CreateCubicBezierLinearTransition(This,duration,finalValue,x1,y1,x2,y2,ppTransition) (This)->lpVtbl->CreateCubicBezierLinearTransition(This,duration,finalValue,x1,y1,x2,y2,ppTransition)
#define IUIAnimationTransitionLibrary2_CreateCubicBezierLinearVectorTransition(This,duration,finalValue,cDimension,x1,y1,x2,y2,ppTransition) (This)->lpVtbl->CreateCubicBezierLinearVectorTransition(This,duration,finalValue,cDimension,x1,y1,x2,y2,ppTransition)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationTransitionLibrary2_QueryInterface(IUIAnimationTransitionLibrary2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationTransitionLibrary2_AddRef(IUIAnimationTransitionLibrary2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationTransitionLibrary2_Release(IUIAnimationTransitionLibrary2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationTransitionLibrary2 methods ***/
static inline HRESULT IUIAnimationTransitionLibrary2_CreateInstantaneousTransition(IUIAnimationTransitionLibrary2* This,double finalValue,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateInstantaneousTransition(This,finalValue,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateInstantaneousVectorTransition(IUIAnimationTransitionLibrary2* This,double *finalValue,unsigned int cDimension,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateInstantaneousVectorTransition(This,finalValue,cDimension,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateConstantTransition(IUIAnimationTransitionLibrary2* This,double duration,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateConstantTransition(This,duration,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateDiscreteTransition(IUIAnimationTransitionLibrary2* This,double delay,double finalValue,double hold,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateDiscreteTransition(This,delay,finalValue,hold,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateDiscreteVectorTransition(IUIAnimationTransitionLibrary2* This,double delay,double *finalValue,unsigned int cDimension,double hold,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateDiscreteVectorTransition(This,delay,finalValue,cDimension,hold,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateLinearTransition(IUIAnimationTransitionLibrary2* This,double duration,double finalValue,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateLinearTransition(This,duration,finalValue,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateLinearVectorTransition(IUIAnimationTransitionLibrary2* This,double duration,double *finalValue,unsigned int cDimension,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateLinearVectorTransition(This,duration,finalValue,cDimension,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateLinearTransitionFromSpeed(IUIAnimationTransitionLibrary2* This,double speed,double finalValue,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateLinearTransitionFromSpeed(This,speed,finalValue,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateLinearVectorTransitionFromSpeed(IUIAnimationTransitionLibrary2* This,double speed,double *finalValue,unsigned int cDimension,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateLinearVectorTransitionFromSpeed(This,speed,finalValue,cDimension,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateSinusoidalTransitionFromVelocity(IUIAnimationTransitionLibrary2* This,double duration,double period,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateSinusoidalTransitionFromVelocity(This,duration,period,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateSinusoidalTransitionFromRange(IUIAnimationTransitionLibrary2* This,double duration,double minimumValue,double maximumValue,double period,UI_ANIMATION_SLOPE slope,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateSinusoidalTransitionFromRange(This,duration,minimumValue,maximumValue,period,slope,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateAccelerateDecelerateTransition(IUIAnimationTransitionLibrary2* This,double duration,double finalValue,double accelerationRatio,double decelerationRatio,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateAccelerateDecelerateTransition(This,duration,finalValue,accelerationRatio,decelerationRatio,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateReversalTransition(IUIAnimationTransitionLibrary2* This,double duration,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateReversalTransition(This,duration,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateCubicTransition(IUIAnimationTransitionLibrary2* This,double duration,double finalValue,double finalVelocity,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateCubicTransition(This,duration,finalValue,finalVelocity,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateCubicVectorTransition(IUIAnimationTransitionLibrary2* This,double duration,double *finalValue,double *finalVelocity,unsigned int cDimension,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateCubicVectorTransition(This,duration,finalValue,finalVelocity,cDimension,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateSmoothStopTransition(IUIAnimationTransitionLibrary2* This,double maximumDuration,double finalValue,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateSmoothStopTransition(This,maximumDuration,finalValue,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateParabolicTransitionFromAcceleration(IUIAnimationTransitionLibrary2* This,double finalValue,double finalVelocity,double acceleration,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateParabolicTransitionFromAcceleration(This,finalValue,finalVelocity,acceleration,transition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateCubicBezierLinearTransition(IUIAnimationTransitionLibrary2* This,double duration,double finalValue,double x1,double y1,double x2,double y2,IUIAnimationTransition2 **ppTransition) {
    return This->lpVtbl->CreateCubicBezierLinearTransition(This,duration,finalValue,x1,y1,x2,y2,ppTransition);
}
static inline HRESULT IUIAnimationTransitionLibrary2_CreateCubicBezierLinearVectorTransition(IUIAnimationTransitionLibrary2* This,double duration,double *finalValue,unsigned int cDimension,double x1,double y1,double x2,double y2,IUIAnimationTransition2 **ppTransition) {
    return This->lpVtbl->CreateCubicBezierLinearVectorTransition(This,duration,finalValue,cDimension,x1,y1,x2,y2,ppTransition);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationTransitionLibrary2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationTransitionFactory interface
 */
#ifndef __IUIAnimationTransitionFactory_INTERFACE_DEFINED__
#define __IUIAnimationTransitionFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationTransitionFactory, 0xfcd91e03, 0x3e3b, 0x45ad, 0xbb,0xb1, 0x6d,0xfc,0x81,0x53,0x74,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fcd91e03-3e3b-45ad-bbb1-6dfc8153743d")
IUIAnimationTransitionFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateTransition(
        IUIAnimationInterpolator *interpolator,
        IUIAnimationTransition **transition) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationTransitionFactory, 0xfcd91e03, 0x3e3b, 0x45ad, 0xbb,0xb1, 0x6d,0xfc,0x81,0x53,0x74,0x3d)
#endif
#else
typedef struct IUIAnimationTransitionFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationTransitionFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationTransitionFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationTransitionFactory *This);

    /*** IUIAnimationTransitionFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateTransition)(
        IUIAnimationTransitionFactory *This,
        IUIAnimationInterpolator *interpolator,
        IUIAnimationTransition **transition);

    END_INTERFACE
} IUIAnimationTransitionFactoryVtbl;

interface IUIAnimationTransitionFactory {
    CONST_VTBL IUIAnimationTransitionFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationTransitionFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationTransitionFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationTransitionFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationTransitionFactory methods ***/
#define IUIAnimationTransitionFactory_CreateTransition(This,interpolator,transition) (This)->lpVtbl->CreateTransition(This,interpolator,transition)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationTransitionFactory_QueryInterface(IUIAnimationTransitionFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationTransitionFactory_AddRef(IUIAnimationTransitionFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationTransitionFactory_Release(IUIAnimationTransitionFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationTransitionFactory methods ***/
static inline HRESULT IUIAnimationTransitionFactory_CreateTransition(IUIAnimationTransitionFactory* This,IUIAnimationInterpolator *interpolator,IUIAnimationTransition **transition) {
    return This->lpVtbl->CreateTransition(This,interpolator,transition);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationTransitionFactory_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationInterpolator interface
 */
#ifndef __IUIAnimationInterpolator_INTERFACE_DEFINED__
#define __IUIAnimationInterpolator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationInterpolator, 0x7815cbba, 0xddf7, 0x478c, 0xa4,0x6c, 0x7b,0x6c,0x73,0x8b,0x79,0x78);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7815cbba-ddf7-478c-a46c-7b6c738b7978")
IUIAnimationInterpolator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetInitialValueAndVelocity(
        double initialValue,
        double initialVelocity) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDuration(
        double duration) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDuration(
        double *duration) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFinalValue(
        double *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE InterpolateValue(
        double offset,
        double *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE InterpolateVelocity(
        double offset,
        double *velocity) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDependencies(
        UI_ANIMATION_DEPENDENCIES *initialValueDependencies,
        UI_ANIMATION_DEPENDENCIES *initialVelocityDependencies,
        UI_ANIMATION_DEPENDENCIES *durationDependencies) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationInterpolator, 0x7815cbba, 0xddf7, 0x478c, 0xa4,0x6c, 0x7b,0x6c,0x73,0x8b,0x79,0x78)
#endif
#else
typedef struct IUIAnimationInterpolatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationInterpolator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationInterpolator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationInterpolator *This);

    /*** IUIAnimationInterpolator methods ***/
    HRESULT (STDMETHODCALLTYPE *SetInitialValueAndVelocity)(
        IUIAnimationInterpolator *This,
        double initialValue,
        double initialVelocity);

    HRESULT (STDMETHODCALLTYPE *SetDuration)(
        IUIAnimationInterpolator *This,
        double duration);

    HRESULT (STDMETHODCALLTYPE *GetDuration)(
        IUIAnimationInterpolator *This,
        double *duration);

    HRESULT (STDMETHODCALLTYPE *GetFinalValue)(
        IUIAnimationInterpolator *This,
        double *value);

    HRESULT (STDMETHODCALLTYPE *InterpolateValue)(
        IUIAnimationInterpolator *This,
        double offset,
        double *value);

    HRESULT (STDMETHODCALLTYPE *InterpolateVelocity)(
        IUIAnimationInterpolator *This,
        double offset,
        double *velocity);

    HRESULT (STDMETHODCALLTYPE *GetDependencies)(
        IUIAnimationInterpolator *This,
        UI_ANIMATION_DEPENDENCIES *initialValueDependencies,
        UI_ANIMATION_DEPENDENCIES *initialVelocityDependencies,
        UI_ANIMATION_DEPENDENCIES *durationDependencies);

    END_INTERFACE
} IUIAnimationInterpolatorVtbl;

interface IUIAnimationInterpolator {
    CONST_VTBL IUIAnimationInterpolatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationInterpolator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationInterpolator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationInterpolator_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationInterpolator methods ***/
#define IUIAnimationInterpolator_SetInitialValueAndVelocity(This,initialValue,initialVelocity) (This)->lpVtbl->SetInitialValueAndVelocity(This,initialValue,initialVelocity)
#define IUIAnimationInterpolator_SetDuration(This,duration) (This)->lpVtbl->SetDuration(This,duration)
#define IUIAnimationInterpolator_GetDuration(This,duration) (This)->lpVtbl->GetDuration(This,duration)
#define IUIAnimationInterpolator_GetFinalValue(This,value) (This)->lpVtbl->GetFinalValue(This,value)
#define IUIAnimationInterpolator_InterpolateValue(This,offset,value) (This)->lpVtbl->InterpolateValue(This,offset,value)
#define IUIAnimationInterpolator_InterpolateVelocity(This,offset,velocity) (This)->lpVtbl->InterpolateVelocity(This,offset,velocity)
#define IUIAnimationInterpolator_GetDependencies(This,initialValueDependencies,initialVelocityDependencies,durationDependencies) (This)->lpVtbl->GetDependencies(This,initialValueDependencies,initialVelocityDependencies,durationDependencies)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationInterpolator_QueryInterface(IUIAnimationInterpolator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationInterpolator_AddRef(IUIAnimationInterpolator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationInterpolator_Release(IUIAnimationInterpolator* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationInterpolator methods ***/
static inline HRESULT IUIAnimationInterpolator_SetInitialValueAndVelocity(IUIAnimationInterpolator* This,double initialValue,double initialVelocity) {
    return This->lpVtbl->SetInitialValueAndVelocity(This,initialValue,initialVelocity);
}
static inline HRESULT IUIAnimationInterpolator_SetDuration(IUIAnimationInterpolator* This,double duration) {
    return This->lpVtbl->SetDuration(This,duration);
}
static inline HRESULT IUIAnimationInterpolator_GetDuration(IUIAnimationInterpolator* This,double *duration) {
    return This->lpVtbl->GetDuration(This,duration);
}
static inline HRESULT IUIAnimationInterpolator_GetFinalValue(IUIAnimationInterpolator* This,double *value) {
    return This->lpVtbl->GetFinalValue(This,value);
}
static inline HRESULT IUIAnimationInterpolator_InterpolateValue(IUIAnimationInterpolator* This,double offset,double *value) {
    return This->lpVtbl->InterpolateValue(This,offset,value);
}
static inline HRESULT IUIAnimationInterpolator_InterpolateVelocity(IUIAnimationInterpolator* This,double offset,double *velocity) {
    return This->lpVtbl->InterpolateVelocity(This,offset,velocity);
}
static inline HRESULT IUIAnimationInterpolator_GetDependencies(IUIAnimationInterpolator* This,UI_ANIMATION_DEPENDENCIES *initialValueDependencies,UI_ANIMATION_DEPENDENCIES *initialVelocityDependencies,UI_ANIMATION_DEPENDENCIES *durationDependencies) {
    return This->lpVtbl->GetDependencies(This,initialValueDependencies,initialVelocityDependencies,durationDependencies);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationInterpolator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationTransitionFactory2 interface
 */
#ifndef __IUIAnimationTransitionFactory2_INTERFACE_DEFINED__
#define __IUIAnimationTransitionFactory2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationTransitionFactory2, 0x937d4916, 0xc1a6, 0x42d5, 0x88,0xd8, 0x30,0x34,0x4d,0x6e,0xfe,0x31);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("937d4916-c1a6-42d5-88d8-30344d6efe31")
IUIAnimationTransitionFactory2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateTransition(
        IUIAnimationInterpolator2 *interpolator,
        IUIAnimationTransition2 **transition) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationTransitionFactory2, 0x937d4916, 0xc1a6, 0x42d5, 0x88,0xd8, 0x30,0x34,0x4d,0x6e,0xfe,0x31)
#endif
#else
typedef struct IUIAnimationTransitionFactory2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationTransitionFactory2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationTransitionFactory2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationTransitionFactory2 *This);

    /*** IUIAnimationTransitionFactory2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateTransition)(
        IUIAnimationTransitionFactory2 *This,
        IUIAnimationInterpolator2 *interpolator,
        IUIAnimationTransition2 **transition);

    END_INTERFACE
} IUIAnimationTransitionFactory2Vtbl;

interface IUIAnimationTransitionFactory2 {
    CONST_VTBL IUIAnimationTransitionFactory2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationTransitionFactory2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationTransitionFactory2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationTransitionFactory2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationTransitionFactory2 methods ***/
#define IUIAnimationTransitionFactory2_CreateTransition(This,interpolator,transition) (This)->lpVtbl->CreateTransition(This,interpolator,transition)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationTransitionFactory2_QueryInterface(IUIAnimationTransitionFactory2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationTransitionFactory2_AddRef(IUIAnimationTransitionFactory2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationTransitionFactory2_Release(IUIAnimationTransitionFactory2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationTransitionFactory2 methods ***/
static inline HRESULT IUIAnimationTransitionFactory2_CreateTransition(IUIAnimationTransitionFactory2* This,IUIAnimationInterpolator2 *interpolator,IUIAnimationTransition2 **transition) {
    return This->lpVtbl->CreateTransition(This,interpolator,transition);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationTransitionFactory2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationInterpolator2 interface
 */
#ifndef __IUIAnimationInterpolator2_INTERFACE_DEFINED__
#define __IUIAnimationInterpolator2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationInterpolator2, 0xea76aff8, 0xea22, 0x4a23, 0xa0,0xef, 0xa6,0xa9,0x66,0x70,0x35,0x18);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ea76aff8-ea22-4a23-a0ef-a6a966703518")
IUIAnimationInterpolator2 : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetDimension(
        unsigned int *dimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInitialValueAndVelocity(
        double *initialValue,
        double *initialVelocity,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDuration(
        double duration) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDuration(
        double *duration) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFinalValue(
        double *value,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE InterpolateValue(
        double offset,
        double *value,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE InterpolateVelocity(
        double offset,
        double *velocity,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPrimitiveInterpolation(
        IUIAnimationPrimitiveInterpolation *interpolation,
        unsigned int cDimension) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDependencies(
        UI_ANIMATION_DEPENDENCIES *initialValueDependencies,
        UI_ANIMATION_DEPENDENCIES *initialVelocityDependencies,
        UI_ANIMATION_DEPENDENCIES *durationDependencies) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationInterpolator2, 0xea76aff8, 0xea22, 0x4a23, 0xa0,0xef, 0xa6,0xa9,0x66,0x70,0x35,0x18)
#endif
#else
typedef struct IUIAnimationInterpolator2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationInterpolator2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationInterpolator2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationInterpolator2 *This);

    /*** IUIAnimationInterpolator2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDimension)(
        IUIAnimationInterpolator2 *This,
        unsigned int *dimension);

    HRESULT (STDMETHODCALLTYPE *SetInitialValueAndVelocity)(
        IUIAnimationInterpolator2 *This,
        double *initialValue,
        double *initialVelocity,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *SetDuration)(
        IUIAnimationInterpolator2 *This,
        double duration);

    HRESULT (STDMETHODCALLTYPE *GetDuration)(
        IUIAnimationInterpolator2 *This,
        double *duration);

    HRESULT (STDMETHODCALLTYPE *GetFinalValue)(
        IUIAnimationInterpolator2 *This,
        double *value,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *InterpolateValue)(
        IUIAnimationInterpolator2 *This,
        double offset,
        double *value,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *InterpolateVelocity)(
        IUIAnimationInterpolator2 *This,
        double offset,
        double *velocity,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *GetPrimitiveInterpolation)(
        IUIAnimationInterpolator2 *This,
        IUIAnimationPrimitiveInterpolation *interpolation,
        unsigned int cDimension);

    HRESULT (STDMETHODCALLTYPE *GetDependencies)(
        IUIAnimationInterpolator2 *This,
        UI_ANIMATION_DEPENDENCIES *initialValueDependencies,
        UI_ANIMATION_DEPENDENCIES *initialVelocityDependencies,
        UI_ANIMATION_DEPENDENCIES *durationDependencies);

    END_INTERFACE
} IUIAnimationInterpolator2Vtbl;

interface IUIAnimationInterpolator2 {
    CONST_VTBL IUIAnimationInterpolator2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationInterpolator2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationInterpolator2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationInterpolator2_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationInterpolator2 methods ***/
#define IUIAnimationInterpolator2_GetDimension(This,dimension) (This)->lpVtbl->GetDimension(This,dimension)
#define IUIAnimationInterpolator2_SetInitialValueAndVelocity(This,initialValue,initialVelocity,cDimension) (This)->lpVtbl->SetInitialValueAndVelocity(This,initialValue,initialVelocity,cDimension)
#define IUIAnimationInterpolator2_SetDuration(This,duration) (This)->lpVtbl->SetDuration(This,duration)
#define IUIAnimationInterpolator2_GetDuration(This,duration) (This)->lpVtbl->GetDuration(This,duration)
#define IUIAnimationInterpolator2_GetFinalValue(This,value,cDimension) (This)->lpVtbl->GetFinalValue(This,value,cDimension)
#define IUIAnimationInterpolator2_InterpolateValue(This,offset,value,cDimension) (This)->lpVtbl->InterpolateValue(This,offset,value,cDimension)
#define IUIAnimationInterpolator2_InterpolateVelocity(This,offset,velocity,cDimension) (This)->lpVtbl->InterpolateVelocity(This,offset,velocity,cDimension)
#define IUIAnimationInterpolator2_GetPrimitiveInterpolation(This,interpolation,cDimension) (This)->lpVtbl->GetPrimitiveInterpolation(This,interpolation,cDimension)
#define IUIAnimationInterpolator2_GetDependencies(This,initialValueDependencies,initialVelocityDependencies,durationDependencies) (This)->lpVtbl->GetDependencies(This,initialValueDependencies,initialVelocityDependencies,durationDependencies)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationInterpolator2_QueryInterface(IUIAnimationInterpolator2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationInterpolator2_AddRef(IUIAnimationInterpolator2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationInterpolator2_Release(IUIAnimationInterpolator2* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationInterpolator2 methods ***/
static inline HRESULT IUIAnimationInterpolator2_GetDimension(IUIAnimationInterpolator2* This,unsigned int *dimension) {
    return This->lpVtbl->GetDimension(This,dimension);
}
static inline HRESULT IUIAnimationInterpolator2_SetInitialValueAndVelocity(IUIAnimationInterpolator2* This,double *initialValue,double *initialVelocity,unsigned int cDimension) {
    return This->lpVtbl->SetInitialValueAndVelocity(This,initialValue,initialVelocity,cDimension);
}
static inline HRESULT IUIAnimationInterpolator2_SetDuration(IUIAnimationInterpolator2* This,double duration) {
    return This->lpVtbl->SetDuration(This,duration);
}
static inline HRESULT IUIAnimationInterpolator2_GetDuration(IUIAnimationInterpolator2* This,double *duration) {
    return This->lpVtbl->GetDuration(This,duration);
}
static inline HRESULT IUIAnimationInterpolator2_GetFinalValue(IUIAnimationInterpolator2* This,double *value,unsigned int cDimension) {
    return This->lpVtbl->GetFinalValue(This,value,cDimension);
}
static inline HRESULT IUIAnimationInterpolator2_InterpolateValue(IUIAnimationInterpolator2* This,double offset,double *value,unsigned int cDimension) {
    return This->lpVtbl->InterpolateValue(This,offset,value,cDimension);
}
static inline HRESULT IUIAnimationInterpolator2_InterpolateVelocity(IUIAnimationInterpolator2* This,double offset,double *velocity,unsigned int cDimension) {
    return This->lpVtbl->InterpolateVelocity(This,offset,velocity,cDimension);
}
static inline HRESULT IUIAnimationInterpolator2_GetPrimitiveInterpolation(IUIAnimationInterpolator2* This,IUIAnimationPrimitiveInterpolation *interpolation,unsigned int cDimension) {
    return This->lpVtbl->GetPrimitiveInterpolation(This,interpolation,cDimension);
}
static inline HRESULT IUIAnimationInterpolator2_GetDependencies(IUIAnimationInterpolator2* This,UI_ANIMATION_DEPENDENCIES *initialValueDependencies,UI_ANIMATION_DEPENDENCIES *initialVelocityDependencies,UI_ANIMATION_DEPENDENCIES *durationDependencies) {
    return This->lpVtbl->GetDependencies(This,initialValueDependencies,initialVelocityDependencies,durationDependencies);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationInterpolator2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationPrimitiveInterpolation interface
 */
#ifndef __IUIAnimationPrimitiveInterpolation_INTERFACE_DEFINED__
#define __IUIAnimationPrimitiveInterpolation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationPrimitiveInterpolation, 0xbab20d63, 0x4361, 0x45da, 0xa2,0x4f, 0xab,0x85,0x08,0x84,0x6b,0x5b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bab20d63-4361-45da-a24f-ab8508846b5b")
IUIAnimationPrimitiveInterpolation : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddCubic(
        unsigned int dimension,
        double beginOffset,
        float constantCoefficient,
        float linearCoefficient,
        float quadraticCoefficient,
        float cubicCoefficient) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddSinusoidal(
        unsigned int dimension,
        double beginOffset,
        float bias,
        float amplitude,
        float frequency,
        float phase) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationPrimitiveInterpolation, 0xbab20d63, 0x4361, 0x45da, 0xa2,0x4f, 0xab,0x85,0x08,0x84,0x6b,0x5b)
#endif
#else
typedef struct IUIAnimationPrimitiveInterpolationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationPrimitiveInterpolation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationPrimitiveInterpolation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationPrimitiveInterpolation *This);

    /*** IUIAnimationPrimitiveInterpolation methods ***/
    HRESULT (STDMETHODCALLTYPE *AddCubic)(
        IUIAnimationPrimitiveInterpolation *This,
        unsigned int dimension,
        double beginOffset,
        float constantCoefficient,
        float linearCoefficient,
        float quadraticCoefficient,
        float cubicCoefficient);

    HRESULT (STDMETHODCALLTYPE *AddSinusoidal)(
        IUIAnimationPrimitiveInterpolation *This,
        unsigned int dimension,
        double beginOffset,
        float bias,
        float amplitude,
        float frequency,
        float phase);

    END_INTERFACE
} IUIAnimationPrimitiveInterpolationVtbl;

interface IUIAnimationPrimitiveInterpolation {
    CONST_VTBL IUIAnimationPrimitiveInterpolationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationPrimitiveInterpolation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationPrimitiveInterpolation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationPrimitiveInterpolation_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationPrimitiveInterpolation methods ***/
#define IUIAnimationPrimitiveInterpolation_AddCubic(This,dimension,beginOffset,constantCoefficient,linearCoefficient,quadraticCoefficient,cubicCoefficient) (This)->lpVtbl->AddCubic(This,dimension,beginOffset,constantCoefficient,linearCoefficient,quadraticCoefficient,cubicCoefficient)
#define IUIAnimationPrimitiveInterpolation_AddSinusoidal(This,dimension,beginOffset,bias,amplitude,frequency,phase) (This)->lpVtbl->AddSinusoidal(This,dimension,beginOffset,bias,amplitude,frequency,phase)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationPrimitiveInterpolation_QueryInterface(IUIAnimationPrimitiveInterpolation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationPrimitiveInterpolation_AddRef(IUIAnimationPrimitiveInterpolation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationPrimitiveInterpolation_Release(IUIAnimationPrimitiveInterpolation* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationPrimitiveInterpolation methods ***/
static inline HRESULT IUIAnimationPrimitiveInterpolation_AddCubic(IUIAnimationPrimitiveInterpolation* This,unsigned int dimension,double beginOffset,float constantCoefficient,float linearCoefficient,float quadraticCoefficient,float cubicCoefficient) {
    return This->lpVtbl->AddCubic(This,dimension,beginOffset,constantCoefficient,linearCoefficient,quadraticCoefficient,cubicCoefficient);
}
static inline HRESULT IUIAnimationPrimitiveInterpolation_AddSinusoidal(IUIAnimationPrimitiveInterpolation* This,unsigned int dimension,double beginOffset,float bias,float amplitude,float frequency,float phase) {
    return This->lpVtbl->AddSinusoidal(This,dimension,beginOffset,bias,amplitude,frequency,phase);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationPrimitiveInterpolation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationTimer interface
 */
#ifndef __IUIAnimationTimer_INTERFACE_DEFINED__
#define __IUIAnimationTimer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationTimer, 0x6b0efad1, 0xa053, 0x41d6, 0x90,0x85, 0x33,0xa6,0x89,0x14,0x46,0x65);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6b0efad1-a053-41d6-9085-33a689144665")
IUIAnimationTimer : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetTimerUpdateHandler(
        IUIAnimationTimerUpdateHandler *updateHandler,
        UI_ANIMATION_IDLE_BEHAVIOR idleBehavior) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTimerEventHandler(
        IUIAnimationTimerEventHandler *handler) = 0;

    virtual HRESULT STDMETHODCALLTYPE Enable(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Disable(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsEnabled(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTime(
        double *seconds) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFrameRateThreshold(
        unsigned int framesPerSecond) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationTimer, 0x6b0efad1, 0xa053, 0x41d6, 0x90,0x85, 0x33,0xa6,0x89,0x14,0x46,0x65)
#endif
#else
typedef struct IUIAnimationTimerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationTimer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationTimer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationTimer *This);

    /*** IUIAnimationTimer methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTimerUpdateHandler)(
        IUIAnimationTimer *This,
        IUIAnimationTimerUpdateHandler *updateHandler,
        UI_ANIMATION_IDLE_BEHAVIOR idleBehavior);

    HRESULT (STDMETHODCALLTYPE *SetTimerEventHandler)(
        IUIAnimationTimer *This,
        IUIAnimationTimerEventHandler *handler);

    HRESULT (STDMETHODCALLTYPE *Enable)(
        IUIAnimationTimer *This);

    HRESULT (STDMETHODCALLTYPE *Disable)(
        IUIAnimationTimer *This);

    HRESULT (STDMETHODCALLTYPE *IsEnabled)(
        IUIAnimationTimer *This);

    HRESULT (STDMETHODCALLTYPE *GetTime)(
        IUIAnimationTimer *This,
        double *seconds);

    HRESULT (STDMETHODCALLTYPE *SetFrameRateThreshold)(
        IUIAnimationTimer *This,
        unsigned int framesPerSecond);

    END_INTERFACE
} IUIAnimationTimerVtbl;

interface IUIAnimationTimer {
    CONST_VTBL IUIAnimationTimerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationTimer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationTimer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationTimer_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationTimer methods ***/
#define IUIAnimationTimer_SetTimerUpdateHandler(This,updateHandler,idleBehavior) (This)->lpVtbl->SetTimerUpdateHandler(This,updateHandler,idleBehavior)
#define IUIAnimationTimer_SetTimerEventHandler(This,handler) (This)->lpVtbl->SetTimerEventHandler(This,handler)
#define IUIAnimationTimer_Enable(This) (This)->lpVtbl->Enable(This)
#define IUIAnimationTimer_Disable(This) (This)->lpVtbl->Disable(This)
#define IUIAnimationTimer_IsEnabled(This) (This)->lpVtbl->IsEnabled(This)
#define IUIAnimationTimer_GetTime(This,seconds) (This)->lpVtbl->GetTime(This,seconds)
#define IUIAnimationTimer_SetFrameRateThreshold(This,framesPerSecond) (This)->lpVtbl->SetFrameRateThreshold(This,framesPerSecond)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationTimer_QueryInterface(IUIAnimationTimer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationTimer_AddRef(IUIAnimationTimer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationTimer_Release(IUIAnimationTimer* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationTimer methods ***/
static inline HRESULT IUIAnimationTimer_SetTimerUpdateHandler(IUIAnimationTimer* This,IUIAnimationTimerUpdateHandler *updateHandler,UI_ANIMATION_IDLE_BEHAVIOR idleBehavior) {
    return This->lpVtbl->SetTimerUpdateHandler(This,updateHandler,idleBehavior);
}
static inline HRESULT IUIAnimationTimer_SetTimerEventHandler(IUIAnimationTimer* This,IUIAnimationTimerEventHandler *handler) {
    return This->lpVtbl->SetTimerEventHandler(This,handler);
}
static inline HRESULT IUIAnimationTimer_Enable(IUIAnimationTimer* This) {
    return This->lpVtbl->Enable(This);
}
static inline HRESULT IUIAnimationTimer_Disable(IUIAnimationTimer* This) {
    return This->lpVtbl->Disable(This);
}
static inline HRESULT IUIAnimationTimer_IsEnabled(IUIAnimationTimer* This) {
    return This->lpVtbl->IsEnabled(This);
}
static inline HRESULT IUIAnimationTimer_GetTime(IUIAnimationTimer* This,double *seconds) {
    return This->lpVtbl->GetTime(This,seconds);
}
static inline HRESULT IUIAnimationTimer_SetFrameRateThreshold(IUIAnimationTimer* This,unsigned int framesPerSecond) {
    return This->lpVtbl->SetFrameRateThreshold(This,framesPerSecond);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationTimer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationTimerUpdateHandler interface
 */
#ifndef __IUIAnimationTimerUpdateHandler_INTERFACE_DEFINED__
#define __IUIAnimationTimerUpdateHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationTimerUpdateHandler, 0x195509b7, 0x5d5e, 0x4e3e, 0xb2,0x78, 0xee,0x37,0x59,0xb3,0x67,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("195509b7-5d5e-4e3e-b278-ee3759b367ad")
IUIAnimationTimerUpdateHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnUpdate(
        double timeNow,
        UI_ANIMATION_UPDATE_RESULT *result) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTimerClientEventHandler(
        IUIAnimationTimerClientEventHandler *handler) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearTimerClientEventHandler(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationTimerUpdateHandler, 0x195509b7, 0x5d5e, 0x4e3e, 0xb2,0x78, 0xee,0x37,0x59,0xb3,0x67,0xad)
#endif
#else
typedef struct IUIAnimationTimerUpdateHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationTimerUpdateHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationTimerUpdateHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationTimerUpdateHandler *This);

    /*** IUIAnimationTimerUpdateHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *OnUpdate)(
        IUIAnimationTimerUpdateHandler *This,
        double timeNow,
        UI_ANIMATION_UPDATE_RESULT *result);

    HRESULT (STDMETHODCALLTYPE *SetTimerClientEventHandler)(
        IUIAnimationTimerUpdateHandler *This,
        IUIAnimationTimerClientEventHandler *handler);

    HRESULT (STDMETHODCALLTYPE *ClearTimerClientEventHandler)(
        IUIAnimationTimerUpdateHandler *This);

    END_INTERFACE
} IUIAnimationTimerUpdateHandlerVtbl;

interface IUIAnimationTimerUpdateHandler {
    CONST_VTBL IUIAnimationTimerUpdateHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationTimerUpdateHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationTimerUpdateHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationTimerUpdateHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationTimerUpdateHandler methods ***/
#define IUIAnimationTimerUpdateHandler_OnUpdate(This,timeNow,result) (This)->lpVtbl->OnUpdate(This,timeNow,result)
#define IUIAnimationTimerUpdateHandler_SetTimerClientEventHandler(This,handler) (This)->lpVtbl->SetTimerClientEventHandler(This,handler)
#define IUIAnimationTimerUpdateHandler_ClearTimerClientEventHandler(This) (This)->lpVtbl->ClearTimerClientEventHandler(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationTimerUpdateHandler_QueryInterface(IUIAnimationTimerUpdateHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationTimerUpdateHandler_AddRef(IUIAnimationTimerUpdateHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationTimerUpdateHandler_Release(IUIAnimationTimerUpdateHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationTimerUpdateHandler methods ***/
static inline HRESULT IUIAnimationTimerUpdateHandler_OnUpdate(IUIAnimationTimerUpdateHandler* This,double timeNow,UI_ANIMATION_UPDATE_RESULT *result) {
    return This->lpVtbl->OnUpdate(This,timeNow,result);
}
static inline HRESULT IUIAnimationTimerUpdateHandler_SetTimerClientEventHandler(IUIAnimationTimerUpdateHandler* This,IUIAnimationTimerClientEventHandler *handler) {
    return This->lpVtbl->SetTimerClientEventHandler(This,handler);
}
static inline HRESULT IUIAnimationTimerUpdateHandler_ClearTimerClientEventHandler(IUIAnimationTimerUpdateHandler* This) {
    return This->lpVtbl->ClearTimerClientEventHandler(This);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationTimerUpdateHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationTimerClientEventHandler interface
 */
#ifndef __IUIAnimationTimerClientEventHandler_INTERFACE_DEFINED__
#define __IUIAnimationTimerClientEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationTimerClientEventHandler, 0xbedb4db6, 0x94fa, 0x4bfb, 0xa4,0x7f, 0xef,0x2d,0x9e,0x40,0x8c,0x25);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bedb4db6-94fa-4bfb-a47f-ef2d9e408c25")
IUIAnimationTimerClientEventHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnTimerClientStatusChanged(
        UI_ANIMATION_TIMER_CLIENT_STATUS newStatus,
        UI_ANIMATION_TIMER_CLIENT_STATUS previousStatus) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationTimerClientEventHandler, 0xbedb4db6, 0x94fa, 0x4bfb, 0xa4,0x7f, 0xef,0x2d,0x9e,0x40,0x8c,0x25)
#endif
#else
typedef struct IUIAnimationTimerClientEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationTimerClientEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationTimerClientEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationTimerClientEventHandler *This);

    /*** IUIAnimationTimerClientEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *OnTimerClientStatusChanged)(
        IUIAnimationTimerClientEventHandler *This,
        UI_ANIMATION_TIMER_CLIENT_STATUS newStatus,
        UI_ANIMATION_TIMER_CLIENT_STATUS previousStatus);

    END_INTERFACE
} IUIAnimationTimerClientEventHandlerVtbl;

interface IUIAnimationTimerClientEventHandler {
    CONST_VTBL IUIAnimationTimerClientEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationTimerClientEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationTimerClientEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationTimerClientEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationTimerClientEventHandler methods ***/
#define IUIAnimationTimerClientEventHandler_OnTimerClientStatusChanged(This,newStatus,previousStatus) (This)->lpVtbl->OnTimerClientStatusChanged(This,newStatus,previousStatus)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationTimerClientEventHandler_QueryInterface(IUIAnimationTimerClientEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationTimerClientEventHandler_AddRef(IUIAnimationTimerClientEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationTimerClientEventHandler_Release(IUIAnimationTimerClientEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationTimerClientEventHandler methods ***/
static inline HRESULT IUIAnimationTimerClientEventHandler_OnTimerClientStatusChanged(IUIAnimationTimerClientEventHandler* This,UI_ANIMATION_TIMER_CLIENT_STATUS newStatus,UI_ANIMATION_TIMER_CLIENT_STATUS previousStatus) {
    return This->lpVtbl->OnTimerClientStatusChanged(This,newStatus,previousStatus);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationTimerClientEventHandler_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IUIAnimationTimerEventHandler interface
 */
#ifndef __IUIAnimationTimerEventHandler_INTERFACE_DEFINED__
#define __IUIAnimationTimerEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID_IUIAnimationTimerEventHandler, 0x274a7dea, 0xd771, 0x4095, 0xab,0xbd, 0x8d,0xf7,0xab,0xd2,0x3c,0xe3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("274a7dea-d771-4095-abbd-8df7abd23ce3")
IUIAnimationTimerEventHandler : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnPreUpdate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnPostUpdate(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnRenderingTooSlow(
        UINT32 framesPerSecond) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IUIAnimationTimerEventHandler, 0x274a7dea, 0xd771, 0x4095, 0xab,0xbd, 0x8d,0xf7,0xab,0xd2,0x3c,0xe3)
#endif
#else
typedef struct IUIAnimationTimerEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IUIAnimationTimerEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IUIAnimationTimerEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IUIAnimationTimerEventHandler *This);

    /*** IUIAnimationTimerEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *OnPreUpdate)(
        IUIAnimationTimerEventHandler *This);

    HRESULT (STDMETHODCALLTYPE *OnPostUpdate)(
        IUIAnimationTimerEventHandler *This);

    HRESULT (STDMETHODCALLTYPE *OnRenderingTooSlow)(
        IUIAnimationTimerEventHandler *This,
        UINT32 framesPerSecond);

    END_INTERFACE
} IUIAnimationTimerEventHandlerVtbl;

interface IUIAnimationTimerEventHandler {
    CONST_VTBL IUIAnimationTimerEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IUIAnimationTimerEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IUIAnimationTimerEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IUIAnimationTimerEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IUIAnimationTimerEventHandler methods ***/
#define IUIAnimationTimerEventHandler_OnPreUpdate(This) (This)->lpVtbl->OnPreUpdate(This)
#define IUIAnimationTimerEventHandler_OnPostUpdate(This) (This)->lpVtbl->OnPostUpdate(This)
#define IUIAnimationTimerEventHandler_OnRenderingTooSlow(This,framesPerSecond) (This)->lpVtbl->OnRenderingTooSlow(This,framesPerSecond)
#else
/*** IUnknown methods ***/
static inline HRESULT IUIAnimationTimerEventHandler_QueryInterface(IUIAnimationTimerEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IUIAnimationTimerEventHandler_AddRef(IUIAnimationTimerEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IUIAnimationTimerEventHandler_Release(IUIAnimationTimerEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IUIAnimationTimerEventHandler methods ***/
static inline HRESULT IUIAnimationTimerEventHandler_OnPreUpdate(IUIAnimationTimerEventHandler* This) {
    return This->lpVtbl->OnPreUpdate(This);
}
static inline HRESULT IUIAnimationTimerEventHandler_OnPostUpdate(IUIAnimationTimerEventHandler* This) {
    return This->lpVtbl->OnPostUpdate(This);
}
static inline HRESULT IUIAnimationTimerEventHandler_OnRenderingTooSlow(IUIAnimationTimerEventHandler* This,UINT32 framesPerSecond) {
    return This->lpVtbl->OnRenderingTooSlow(This,framesPerSecond);
}
#endif
#endif

#endif


#endif  /* __IUIAnimationTimerEventHandler_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __uianimation_h__ */
