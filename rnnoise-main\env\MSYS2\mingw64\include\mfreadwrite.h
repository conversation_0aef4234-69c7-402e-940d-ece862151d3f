/*** Autogenerated by WIDL 10.12 from include/mfreadwrite.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __mfreadwrite_h__
#define __mfreadwrite_h__

/* Forward declarations */

#ifndef __IMFSourceReaderCallback_FWD_DEFINED__
#define __IMFSourceReaderCallback_FWD_DEFINED__
typedef interface IMFSourceReaderCallback IMFSourceReaderCallback;
#ifdef __cplusplus
interface IMFSourceReaderCallback;
#endif /* __cplusplus */
#endif

#ifndef __IMFSourceReaderCallback2_FWD_DEFINED__
#define __IMFSourceReaderCallback2_FWD_DEFINED__
typedef interface IMFSourceReaderCallback2 IMFSourceReaderCallback2;
#ifdef __cplusplus
interface IMFSourceReaderCallback2;
#endif /* __cplusplus */
#endif

#ifndef __IMFSourceReader_FWD_DEFINED__
#define __IMFSourceReader_FWD_DEFINED__
typedef interface IMFSourceReader IMFSourceReader;
#ifdef __cplusplus
interface IMFSourceReader;
#endif /* __cplusplus */
#endif

#ifndef __IMFSourceReaderEx_FWD_DEFINED__
#define __IMFSourceReaderEx_FWD_DEFINED__
typedef interface IMFSourceReaderEx IMFSourceReaderEx;
#ifdef __cplusplus
interface IMFSourceReaderEx;
#endif /* __cplusplus */
#endif

#ifndef __IMFSinkWriter_FWD_DEFINED__
#define __IMFSinkWriter_FWD_DEFINED__
typedef interface IMFSinkWriter IMFSinkWriter;
#ifdef __cplusplus
interface IMFSinkWriter;
#endif /* __cplusplus */
#endif

#ifndef __IMFSinkWriterEx_FWD_DEFINED__
#define __IMFSinkWriterEx_FWD_DEFINED__
typedef interface IMFSinkWriterEx IMFSinkWriterEx;
#ifdef __cplusplus
interface IMFSinkWriterEx;
#endif /* __cplusplus */
#endif

#ifndef __IMFSinkWriterEncoderConfig_FWD_DEFINED__
#define __IMFSinkWriterEncoderConfig_FWD_DEFINED__
typedef interface IMFSinkWriterEncoderConfig IMFSinkWriterEncoderConfig;
#ifdef __cplusplus
interface IMFSinkWriterEncoderConfig;
#endif /* __cplusplus */
#endif

#ifndef __IMFSinkWriterCallback_FWD_DEFINED__
#define __IMFSinkWriterCallback_FWD_DEFINED__
typedef interface IMFSinkWriterCallback IMFSinkWriterCallback;
#ifdef __cplusplus
interface IMFSinkWriterCallback;
#endif /* __cplusplus */
#endif

#ifndef __IMFSinkWriterCallback2_FWD_DEFINED__
#define __IMFSinkWriterCallback2_FWD_DEFINED__
typedef interface IMFSinkWriterCallback2 IMFSinkWriterCallback2;
#ifdef __cplusplus
interface IMFSinkWriterCallback2;
#endif /* __cplusplus */
#endif

#ifndef __IMFReadWriteClassFactory_FWD_DEFINED__
#define __IMFReadWriteClassFactory_FWD_DEFINED__
typedef interface IMFReadWriteClassFactory IMFReadWriteClassFactory;
#ifdef __cplusplus
interface IMFReadWriteClassFactory;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <mfobjects.h>
#include <mftransform.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum MF_SOURCE_READER_CONTROL_FLAG {
    MF_SOURCE_READER_CONTROLF_DRAIN = 0x1
} MF_SOURCE_READER_CONTROL_FLAG;
DEFINE_ENUM_FLAG_OPERATORS(MF_SOURCE_READER_CONTROL_FLAG)
enum {
    MF_SOURCE_READER_INVALID_STREAM_INDEX = 0xffffffff,
    MF_SOURCE_READER_ALL_STREAMS = 0xfffffffe,
    MF_SOURCE_READER_ANY_STREAM = 0xfffffffe,
    MF_SOURCE_READER_FIRST_AUDIO_STREAM = 0xfffffffd,
    MF_SOURCE_READER_FIRST_VIDEO_STREAM = 0xfffffffc,
    MF_SOURCE_READER_MEDIASOURCE = 0xffffffff,
    MF_SOURCE_READER_CURRENT_TYPE_INDEX = 0xffffffff
};
typedef enum MF_SOURCE_READER_FLAG {
    MF_SOURCE_READERF_ERROR = 0x1,
    MF_SOURCE_READERF_ENDOFSTREAM = 0x2,
    MF_SOURCE_READERF_NEWSTREAM = 0x4,
    MF_SOURCE_READERF_NATIVEMEDIATYPECHANGED = 0x10,
    MF_SOURCE_READERF_CURRENTMEDIATYPECHANGED = 0x20,
    MF_SOURCE_READERF_STREAMTICK = 0x100,
    MF_SOURCE_READERF_ALLEFFECTSREMOVED = 0x200
} MF_SOURCE_READER_FLAG;
DEFINE_ENUM_FLAG_OPERATORS(MF_SOURCE_READER_FLAG)
enum {
    MF_SINK_WRITER_INVALID_STREAM_INDEX = 0xffffffff,
    MF_SINK_WRITER_ALL_STREAMS = 0xfffffffe,
    MF_SINK_WRITER_MEDIASINK = 0xffffffff
};
typedef struct _MF_SINK_WRITER_STATISTICS {
    DWORD cb;
    LONGLONG llLastTimestampReceived;
    LONGLONG llLastTimestampEncoded;
    LONGLONG llLastTimestampProcessed;
    LONGLONG llLastStreamTickReceived;
    LONGLONG llLastSinkSampleRequest;
    QWORD qwNumSamplesReceived;
    QWORD qwNumSamplesEncoded;
    QWORD qwNumSamplesProcessed;
    QWORD qwNumStreamTicksReceived;
    DWORD dwByteCountQueued;
    QWORD qwByteCountProcessed;
    DWORD dwNumOutstandingSinkSampleRequests;
    DWORD dwAverageSampleRateReceived;
    DWORD dwAverageSampleRateEncoded;
    DWORD dwAverageSampleRateProcessed;
} MF_SINK_WRITER_STATISTICS;
EXTERN_GUID(CLSID_MFReadWriteClassFactory,           0x48e2ed0f, 0x98c2, 0x4a37, 0xbe, 0xd5, 0x16, 0x63, 0x12, 0xdd, 0xd8, 0x3f);
EXTERN_GUID(CLSID_MFSourceReader,                    0x1777133c, 0x0881, 0x411b, 0xa5, 0x77, 0xad, 0x54, 0x5f, 0x07, 0x14, 0xc4);
EXTERN_GUID(CLSID_MFSinkWriter,                      0xa3bbfb17, 0x8273, 0x4e52, 0x9e, 0x0e, 0x97, 0x39, 0xdc, 0x88, 0x79, 0x90);
EXTERN_GUID(MF_MEDIASINK_AUTOFINALIZE_SUPPORTED,     0x48c131be, 0x135a, 0x41cb, 0x82, 0x90, 0x03, 0x65, 0x25, 0x09, 0xc9, 0x99);
EXTERN_GUID(MF_MEDIASINK_ENABLE_AUTOFINALIZE,        0x34014265, 0xcb7e, 0x4cde, 0xac, 0x7c, 0xef, 0xfd, 0x3b, 0x3c, 0x25, 0x30);
EXTERN_GUID(MF_SINK_WRITER_ASYNC_CALLBACK,           0x48cb183e, 0x7b0b, 0x46f4, 0x82, 0x2e, 0x5e, 0x1d, 0x2d, 0xda, 0x43, 0x54);
EXTERN_GUID(MF_SINK_WRITER_DISABLE_THROTTLING,       0x08b845d8, 0x2b74, 0x4afe, 0x9d, 0x53, 0xbe, 0x16, 0xd2, 0xd5, 0xae, 0x4f);
EXTERN_GUID(MF_SINK_WRITER_D3D_MANAGER,              0xec822da2, 0xe1e9, 0x4b29, 0xa0, 0xd8, 0x56, 0x3c, 0x71, 0x9f, 0x52, 0x69);
EXTERN_GUID(MF_SINK_WRITER_ENCODER_CONFIG,           0xad91cd04, 0xa7cc, 0x4ac7, 0x99, 0xb6, 0xa5, 0x7b, 0x9a, 0x4a, 0x7c, 0x70);
EXTERN_GUID(MF_READWRITE_DISABLE_CONVERTERS,         0x98d5b065, 0x1374, 0x4847, 0x8d, 0x5d, 0x31, 0x52, 0x0f, 0xee, 0x71, 0x56);
EXTERN_GUID(MF_READWRITE_ENABLE_AUTOFINALIZE,        0xdd7ca129, 0x8cd1, 0x4dc5, 0x9d, 0xde, 0xce, 0x16, 0x86, 0x75, 0xde, 0x61);
EXTERN_GUID(MF_READWRITE_ENABLE_HARDWARE_TRANSFORMS, 0xa634a91c, 0x822b, 0x41b9, 0xa4, 0x94, 0x4d, 0xe4, 0x64, 0x36, 0x12, 0xb0);
EXTERN_GUID(MF_READWRITE_MMCSS_CLASS,                0x39384300, 0xd0eb, 0x40b1, 0x87, 0xa0, 0x33, 0x18, 0x87, 0x1b, 0x5a, 0x53);
EXTERN_GUID(MF_READWRITE_MMCSS_PRIORITY,             0x43ad19ce, 0xf33f, 0x4ba9, 0xa5, 0x80, 0xe4, 0xcd, 0x12, 0xf2, 0xd1, 0x44);
EXTERN_GUID(MF_READWRITE_MMCSS_CLASS_AUDIO,          0x430847da, 0x0890, 0x4b0e, 0x93, 0x8c, 0x05, 0x43, 0x32, 0xc5, 0x47, 0xe1);
EXTERN_GUID(MF_READWRITE_MMCSS_PRIORITY_AUDIO,       0x273db885, 0x2de2, 0x4db2, 0xa6, 0xa7, 0xfd, 0xb6, 0x6f, 0xb4, 0x0b, 0x61);
EXTERN_GUID(MF_READWRITE_D3D_OPTIONAL,               0x216479d9, 0x3071, 0x42ca, 0xbb, 0x6c, 0x4c, 0x22, 0x10, 0x2e, 0x1d, 0x18);
EXTERN_GUID(MF_SOURCE_READER_ASYNC_CALLBACK,                     0x1e3dbeac, 0xbb43, 0x4c35, 0xb5, 0x07, 0xcd, 0x64, 0x44, 0x64, 0xc9, 0x65);
EXTERN_GUID(MF_SOURCE_READER_D3D_MANAGER,                        0xec822da2, 0xe1e9, 0x4b29, 0xa0, 0xd8, 0x56, 0x3c, 0x71, 0x9f, 0x52, 0x69);
EXTERN_GUID(MF_SOURCE_READER_D3D11_BIND_FLAGS,                   0x33f3197b, 0xf73a, 0x4e14, 0x8d, 0x85, 0x0e, 0x4c, 0x43, 0x68, 0x78, 0x8d);
EXTERN_GUID(MF_SOURCE_READER_DISABLE_CAMERA_PLUGINS,             0x9d3365dd, 0x058f, 0x4cfb, 0x9f, 0x97, 0xb3, 0x14, 0xcc, 0x99, 0xc8, 0xad);
EXTERN_GUID(MF_SOURCE_READER_DISABLE_DXVA,                       0xaa456cfd, 0x3943, 0x4a1e, 0xa7, 0x7d, 0x18, 0x38, 0xc0, 0xea, 0x2e, 0x35);
EXTERN_GUID(MF_SOURCE_READER_DISCONNECT_MEDIASOURCE_ON_SHUTDOWN, 0x56b67165, 0x219e, 0x456d, 0xa2, 0x2e, 0x2d, 0x30, 0x04, 0xc7, 0xfe, 0x56);
EXTERN_GUID(MF_SOURCE_READER_ENABLE_ADVANCED_VIDEO_PROCESSING,   0x0f81da2c, 0xb537, 0x4672, 0xa8, 0xb2, 0xa6, 0x81, 0xb1, 0x73, 0x07, 0xa3);
EXTERN_GUID(MF_SOURCE_READER_ENABLE_TRANSCODE_ONLY_TRANSFORMS,   0xdfd4f008, 0xb5fd, 0x4e78, 0xae, 0x44, 0x62, 0xa1, 0xe6, 0x7b, 0xbe, 0x27);
EXTERN_GUID(MF_SOURCE_READER_ENABLE_VIDEO_PROCESSING,            0xfb394f3d, 0xccf1, 0x42ee, 0xbb, 0xb3, 0xf9, 0xb8, 0x45, 0xd5, 0x68, 0x1d);
EXTERN_GUID(MF_SOURCE_READER_MEDIASOURCE_CHARACTERISTICS,        0x6d23f5c8, 0xc5d7, 0x4a9b, 0x99, 0x71, 0x5d, 0x11, 0xf8, 0xbc, 0xa8, 0x80);
EXTERN_GUID(MF_SOURCE_READER_MEDIASOURCE_CONFIG,                 0x9085abeb, 0x0354, 0x48f9, 0xab, 0xb5, 0x20, 0x0d, 0xf8, 0x38, 0xc6, 0x8e);
/*****************************************************************************
 * IMFSourceReaderCallback interface
 */
#ifndef __IMFSourceReaderCallback_INTERFACE_DEFINED__
#define __IMFSourceReaderCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSourceReaderCallback, 0xdeec8d99, 0xfa1d, 0x4d82, 0x84,0xc2, 0x2c,0x89,0x69,0x94,0x48,0x67);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("deec8d99-fa1d-4d82-84c2-2c8969944867")
IMFSourceReaderCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnReadSample(
        HRESULT hr,
        DWORD stream_index,
        DWORD stream_flags,
        LONGLONG timestamp,
        IMFSample *sample) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnFlush(
        DWORD stream_index) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnEvent(
        DWORD stream_index,
        IMFMediaEvent *event) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSourceReaderCallback, 0xdeec8d99, 0xfa1d, 0x4d82, 0x84,0xc2, 0x2c,0x89,0x69,0x94,0x48,0x67)
#endif
#else
typedef struct IMFSourceReaderCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSourceReaderCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSourceReaderCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSourceReaderCallback *This);

    /*** IMFSourceReaderCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnReadSample)(
        IMFSourceReaderCallback *This,
        HRESULT hr,
        DWORD stream_index,
        DWORD stream_flags,
        LONGLONG timestamp,
        IMFSample *sample);

    HRESULT (STDMETHODCALLTYPE *OnFlush)(
        IMFSourceReaderCallback *This,
        DWORD stream_index);

    HRESULT (STDMETHODCALLTYPE *OnEvent)(
        IMFSourceReaderCallback *This,
        DWORD stream_index,
        IMFMediaEvent *event);

    END_INTERFACE
} IMFSourceReaderCallbackVtbl;

interface IMFSourceReaderCallback {
    CONST_VTBL IMFSourceReaderCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSourceReaderCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSourceReaderCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSourceReaderCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSourceReaderCallback methods ***/
#define IMFSourceReaderCallback_OnReadSample(This,hr,stream_index,stream_flags,timestamp,sample) (This)->lpVtbl->OnReadSample(This,hr,stream_index,stream_flags,timestamp,sample)
#define IMFSourceReaderCallback_OnFlush(This,stream_index) (This)->lpVtbl->OnFlush(This,stream_index)
#define IMFSourceReaderCallback_OnEvent(This,stream_index,event) (This)->lpVtbl->OnEvent(This,stream_index,event)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSourceReaderCallback_QueryInterface(IMFSourceReaderCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSourceReaderCallback_AddRef(IMFSourceReaderCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSourceReaderCallback_Release(IMFSourceReaderCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSourceReaderCallback methods ***/
static inline HRESULT IMFSourceReaderCallback_OnReadSample(IMFSourceReaderCallback* This,HRESULT hr,DWORD stream_index,DWORD stream_flags,LONGLONG timestamp,IMFSample *sample) {
    return This->lpVtbl->OnReadSample(This,hr,stream_index,stream_flags,timestamp,sample);
}
static inline HRESULT IMFSourceReaderCallback_OnFlush(IMFSourceReaderCallback* This,DWORD stream_index) {
    return This->lpVtbl->OnFlush(This,stream_index);
}
static inline HRESULT IMFSourceReaderCallback_OnEvent(IMFSourceReaderCallback* This,DWORD stream_index,IMFMediaEvent *event) {
    return This->lpVtbl->OnEvent(This,stream_index,event);
}
#endif
#endif

#endif


#endif  /* __IMFSourceReaderCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFSourceReaderCallback2 interface
 */
#ifndef __IMFSourceReaderCallback2_INTERFACE_DEFINED__
#define __IMFSourceReaderCallback2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSourceReaderCallback2, 0xcf839fe6, 0x8c2a, 0x4dd2, 0xb6,0xea, 0xc2,0x2d,0x69,0x61,0xaf,0x05);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cf839fe6-8c2a-4dd2-b6ea-c22d6961af05")
IMFSourceReaderCallback2 : public IMFSourceReaderCallback
{
    virtual HRESULT STDMETHODCALLTYPE OnTransformChange(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnStreamError(
        DWORD stream_index,
        HRESULT status) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSourceReaderCallback2, 0xcf839fe6, 0x8c2a, 0x4dd2, 0xb6,0xea, 0xc2,0x2d,0x69,0x61,0xaf,0x05)
#endif
#else
typedef struct IMFSourceReaderCallback2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSourceReaderCallback2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSourceReaderCallback2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSourceReaderCallback2 *This);

    /*** IMFSourceReaderCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnReadSample)(
        IMFSourceReaderCallback2 *This,
        HRESULT hr,
        DWORD stream_index,
        DWORD stream_flags,
        LONGLONG timestamp,
        IMFSample *sample);

    HRESULT (STDMETHODCALLTYPE *OnFlush)(
        IMFSourceReaderCallback2 *This,
        DWORD stream_index);

    HRESULT (STDMETHODCALLTYPE *OnEvent)(
        IMFSourceReaderCallback2 *This,
        DWORD stream_index,
        IMFMediaEvent *event);

    /*** IMFSourceReaderCallback2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnTransformChange)(
        IMFSourceReaderCallback2 *This);

    HRESULT (STDMETHODCALLTYPE *OnStreamError)(
        IMFSourceReaderCallback2 *This,
        DWORD stream_index,
        HRESULT status);

    END_INTERFACE
} IMFSourceReaderCallback2Vtbl;

interface IMFSourceReaderCallback2 {
    CONST_VTBL IMFSourceReaderCallback2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSourceReaderCallback2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSourceReaderCallback2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSourceReaderCallback2_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSourceReaderCallback methods ***/
#define IMFSourceReaderCallback2_OnReadSample(This,hr,stream_index,stream_flags,timestamp,sample) (This)->lpVtbl->OnReadSample(This,hr,stream_index,stream_flags,timestamp,sample)
#define IMFSourceReaderCallback2_OnFlush(This,stream_index) (This)->lpVtbl->OnFlush(This,stream_index)
#define IMFSourceReaderCallback2_OnEvent(This,stream_index,event) (This)->lpVtbl->OnEvent(This,stream_index,event)
/*** IMFSourceReaderCallback2 methods ***/
#define IMFSourceReaderCallback2_OnTransformChange(This) (This)->lpVtbl->OnTransformChange(This)
#define IMFSourceReaderCallback2_OnStreamError(This,stream_index,status) (This)->lpVtbl->OnStreamError(This,stream_index,status)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSourceReaderCallback2_QueryInterface(IMFSourceReaderCallback2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSourceReaderCallback2_AddRef(IMFSourceReaderCallback2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSourceReaderCallback2_Release(IMFSourceReaderCallback2* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSourceReaderCallback methods ***/
static inline HRESULT IMFSourceReaderCallback2_OnReadSample(IMFSourceReaderCallback2* This,HRESULT hr,DWORD stream_index,DWORD stream_flags,LONGLONG timestamp,IMFSample *sample) {
    return This->lpVtbl->OnReadSample(This,hr,stream_index,stream_flags,timestamp,sample);
}
static inline HRESULT IMFSourceReaderCallback2_OnFlush(IMFSourceReaderCallback2* This,DWORD stream_index) {
    return This->lpVtbl->OnFlush(This,stream_index);
}
static inline HRESULT IMFSourceReaderCallback2_OnEvent(IMFSourceReaderCallback2* This,DWORD stream_index,IMFMediaEvent *event) {
    return This->lpVtbl->OnEvent(This,stream_index,event);
}
/*** IMFSourceReaderCallback2 methods ***/
static inline HRESULT IMFSourceReaderCallback2_OnTransformChange(IMFSourceReaderCallback2* This) {
    return This->lpVtbl->OnTransformChange(This);
}
static inline HRESULT IMFSourceReaderCallback2_OnStreamError(IMFSourceReaderCallback2* This,DWORD stream_index,HRESULT status) {
    return This->lpVtbl->OnStreamError(This,stream_index,status);
}
#endif
#endif

#endif


#endif  /* __IMFSourceReaderCallback2_INTERFACE_DEFINED__ */

#ifndef __IMFMediaSource_FWD_DEFINED__
#define __IMFMediaSource_FWD_DEFINED__
typedef interface IMFMediaSource IMFMediaSource;
#ifdef __cplusplus
interface IMFMediaSource;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IMFSourceReader interface
 */
#ifndef __IMFSourceReader_INTERFACE_DEFINED__
#define __IMFSourceReader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSourceReader, 0x70ae66f2, 0xc809, 0x4e4f, 0x89,0x15, 0xbd,0xcb,0x40,0x6b,0x79,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("70ae66f2-c809-4e4f-8915-bdcb406b7993")
IMFSourceReader : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStreamSelection(
        DWORD index,
        WINBOOL *selected) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamSelection(
        DWORD index,
        WINBOOL selected) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNativeMediaType(
        DWORD index,
        DWORD typeindex,
        IMFMediaType **type) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentMediaType(
        DWORD index,
        IMFMediaType **type) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentMediaType(
        DWORD index,
        DWORD *reserved,
        IMFMediaType *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentPosition(
        REFGUID format,
        REFPROPVARIANT position) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReadSample(
        DWORD index,
        DWORD flags,
        DWORD *actualindex,
        DWORD *sampleflags,
        LONGLONG *timestamp,
        IMFSample **sample) = 0;

    virtual HRESULT STDMETHODCALLTYPE Flush(
        DWORD index) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetServiceForStream(
        DWORD index,
        REFGUID service,
        REFIID riid,
        void **object) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPresentationAttribute(
        DWORD index,
        REFGUID guid,
        PROPVARIANT *attr) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSourceReader, 0x70ae66f2, 0xc809, 0x4e4f, 0x89,0x15, 0xbd,0xcb,0x40,0x6b,0x79,0x93)
#endif
#else
typedef struct IMFSourceReaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSourceReader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSourceReader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSourceReader *This);

    /*** IMFSourceReader methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreamSelection)(
        IMFSourceReader *This,
        DWORD index,
        WINBOOL *selected);

    HRESULT (STDMETHODCALLTYPE *SetStreamSelection)(
        IMFSourceReader *This,
        DWORD index,
        WINBOOL selected);

    HRESULT (STDMETHODCALLTYPE *GetNativeMediaType)(
        IMFSourceReader *This,
        DWORD index,
        DWORD typeindex,
        IMFMediaType **type);

    HRESULT (STDMETHODCALLTYPE *GetCurrentMediaType)(
        IMFSourceReader *This,
        DWORD index,
        IMFMediaType **type);

    HRESULT (STDMETHODCALLTYPE *SetCurrentMediaType)(
        IMFSourceReader *This,
        DWORD index,
        DWORD *reserved,
        IMFMediaType *type);

    HRESULT (STDMETHODCALLTYPE *SetCurrentPosition)(
        IMFSourceReader *This,
        REFGUID format,
        REFPROPVARIANT position);

    HRESULT (STDMETHODCALLTYPE *ReadSample)(
        IMFSourceReader *This,
        DWORD index,
        DWORD flags,
        DWORD *actualindex,
        DWORD *sampleflags,
        LONGLONG *timestamp,
        IMFSample **sample);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        IMFSourceReader *This,
        DWORD index);

    HRESULT (STDMETHODCALLTYPE *GetServiceForStream)(
        IMFSourceReader *This,
        DWORD index,
        REFGUID service,
        REFIID riid,
        void **object);

    HRESULT (STDMETHODCALLTYPE *GetPresentationAttribute)(
        IMFSourceReader *This,
        DWORD index,
        REFGUID guid,
        PROPVARIANT *attr);

    END_INTERFACE
} IMFSourceReaderVtbl;

interface IMFSourceReader {
    CONST_VTBL IMFSourceReaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSourceReader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSourceReader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSourceReader_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSourceReader methods ***/
#define IMFSourceReader_GetStreamSelection(This,index,selected) (This)->lpVtbl->GetStreamSelection(This,index,selected)
#define IMFSourceReader_SetStreamSelection(This,index,selected) (This)->lpVtbl->SetStreamSelection(This,index,selected)
#define IMFSourceReader_GetNativeMediaType(This,index,typeindex,type) (This)->lpVtbl->GetNativeMediaType(This,index,typeindex,type)
#define IMFSourceReader_GetCurrentMediaType(This,index,type) (This)->lpVtbl->GetCurrentMediaType(This,index,type)
#define IMFSourceReader_SetCurrentMediaType(This,index,reserved,type) (This)->lpVtbl->SetCurrentMediaType(This,index,reserved,type)
#define IMFSourceReader_SetCurrentPosition(This,format,position) (This)->lpVtbl->SetCurrentPosition(This,format,position)
#define IMFSourceReader_ReadSample(This,index,flags,actualindex,sampleflags,timestamp,sample) (This)->lpVtbl->ReadSample(This,index,flags,actualindex,sampleflags,timestamp,sample)
#define IMFSourceReader_Flush(This,index) (This)->lpVtbl->Flush(This,index)
#define IMFSourceReader_GetServiceForStream(This,index,service,riid,object) (This)->lpVtbl->GetServiceForStream(This,index,service,riid,object)
#define IMFSourceReader_GetPresentationAttribute(This,index,guid,attr) (This)->lpVtbl->GetPresentationAttribute(This,index,guid,attr)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSourceReader_QueryInterface(IMFSourceReader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSourceReader_AddRef(IMFSourceReader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSourceReader_Release(IMFSourceReader* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSourceReader methods ***/
static inline HRESULT IMFSourceReader_GetStreamSelection(IMFSourceReader* This,DWORD index,WINBOOL *selected) {
    return This->lpVtbl->GetStreamSelection(This,index,selected);
}
static inline HRESULT IMFSourceReader_SetStreamSelection(IMFSourceReader* This,DWORD index,WINBOOL selected) {
    return This->lpVtbl->SetStreamSelection(This,index,selected);
}
static inline HRESULT IMFSourceReader_GetNativeMediaType(IMFSourceReader* This,DWORD index,DWORD typeindex,IMFMediaType **type) {
    return This->lpVtbl->GetNativeMediaType(This,index,typeindex,type);
}
static inline HRESULT IMFSourceReader_GetCurrentMediaType(IMFSourceReader* This,DWORD index,IMFMediaType **type) {
    return This->lpVtbl->GetCurrentMediaType(This,index,type);
}
static inline HRESULT IMFSourceReader_SetCurrentMediaType(IMFSourceReader* This,DWORD index,DWORD *reserved,IMFMediaType *type) {
    return This->lpVtbl->SetCurrentMediaType(This,index,reserved,type);
}
static inline HRESULT IMFSourceReader_SetCurrentPosition(IMFSourceReader* This,REFGUID format,REFPROPVARIANT position) {
    return This->lpVtbl->SetCurrentPosition(This,format,position);
}
static inline HRESULT IMFSourceReader_ReadSample(IMFSourceReader* This,DWORD index,DWORD flags,DWORD *actualindex,DWORD *sampleflags,LONGLONG *timestamp,IMFSample **sample) {
    return This->lpVtbl->ReadSample(This,index,flags,actualindex,sampleflags,timestamp,sample);
}
static inline HRESULT IMFSourceReader_Flush(IMFSourceReader* This,DWORD index) {
    return This->lpVtbl->Flush(This,index);
}
static inline HRESULT IMFSourceReader_GetServiceForStream(IMFSourceReader* This,DWORD index,REFGUID service,REFIID riid,void **object) {
    return This->lpVtbl->GetServiceForStream(This,index,service,riid,object);
}
static inline HRESULT IMFSourceReader_GetPresentationAttribute(IMFSourceReader* This,DWORD index,REFGUID guid,PROPVARIANT *attr) {
    return This->lpVtbl->GetPresentationAttribute(This,index,guid,attr);
}
#endif
#endif

#endif


#endif  /* __IMFSourceReader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFSourceReaderEx interface
 */
#ifndef __IMFSourceReaderEx_INTERFACE_DEFINED__
#define __IMFSourceReaderEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSourceReaderEx, 0x7b981cf0, 0x560e, 0x4116, 0x98,0x75, 0xb0,0x99,0x89,0x5f,0x23,0xd7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7b981cf0-560e-4116-9875-b099895f23d7")
IMFSourceReaderEx : public IMFSourceReader
{
    virtual HRESULT STDMETHODCALLTYPE SetNativeMediaType(
        DWORD stream_index,
        IMFMediaType *media_type,
        DWORD *stream_flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddTransformForStream(
        DWORD stream_index,
        IUnknown *transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAllTransformsForStream(
        DWORD stream_index) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransformForStream(
        DWORD stream_index,
        DWORD transform_index,
        GUID *category,
        IMFTransform **transform) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSourceReaderEx, 0x7b981cf0, 0x560e, 0x4116, 0x98,0x75, 0xb0,0x99,0x89,0x5f,0x23,0xd7)
#endif
#else
typedef struct IMFSourceReaderExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSourceReaderEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSourceReaderEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSourceReaderEx *This);

    /*** IMFSourceReader methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreamSelection)(
        IMFSourceReaderEx *This,
        DWORD index,
        WINBOOL *selected);

    HRESULT (STDMETHODCALLTYPE *SetStreamSelection)(
        IMFSourceReaderEx *This,
        DWORD index,
        WINBOOL selected);

    HRESULT (STDMETHODCALLTYPE *GetNativeMediaType)(
        IMFSourceReaderEx *This,
        DWORD index,
        DWORD typeindex,
        IMFMediaType **type);

    HRESULT (STDMETHODCALLTYPE *GetCurrentMediaType)(
        IMFSourceReaderEx *This,
        DWORD index,
        IMFMediaType **type);

    HRESULT (STDMETHODCALLTYPE *SetCurrentMediaType)(
        IMFSourceReaderEx *This,
        DWORD index,
        DWORD *reserved,
        IMFMediaType *type);

    HRESULT (STDMETHODCALLTYPE *SetCurrentPosition)(
        IMFSourceReaderEx *This,
        REFGUID format,
        REFPROPVARIANT position);

    HRESULT (STDMETHODCALLTYPE *ReadSample)(
        IMFSourceReaderEx *This,
        DWORD index,
        DWORD flags,
        DWORD *actualindex,
        DWORD *sampleflags,
        LONGLONG *timestamp,
        IMFSample **sample);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        IMFSourceReaderEx *This,
        DWORD index);

    HRESULT (STDMETHODCALLTYPE *GetServiceForStream)(
        IMFSourceReaderEx *This,
        DWORD index,
        REFGUID service,
        REFIID riid,
        void **object);

    HRESULT (STDMETHODCALLTYPE *GetPresentationAttribute)(
        IMFSourceReaderEx *This,
        DWORD index,
        REFGUID guid,
        PROPVARIANT *attr);

    /*** IMFSourceReaderEx methods ***/
    HRESULT (STDMETHODCALLTYPE *SetNativeMediaType)(
        IMFSourceReaderEx *This,
        DWORD stream_index,
        IMFMediaType *media_type,
        DWORD *stream_flags);

    HRESULT (STDMETHODCALLTYPE *AddTransformForStream)(
        IMFSourceReaderEx *This,
        DWORD stream_index,
        IUnknown *transform);

    HRESULT (STDMETHODCALLTYPE *RemoveAllTransformsForStream)(
        IMFSourceReaderEx *This,
        DWORD stream_index);

    HRESULT (STDMETHODCALLTYPE *GetTransformForStream)(
        IMFSourceReaderEx *This,
        DWORD stream_index,
        DWORD transform_index,
        GUID *category,
        IMFTransform **transform);

    END_INTERFACE
} IMFSourceReaderExVtbl;

interface IMFSourceReaderEx {
    CONST_VTBL IMFSourceReaderExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSourceReaderEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSourceReaderEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSourceReaderEx_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSourceReader methods ***/
#define IMFSourceReaderEx_GetStreamSelection(This,index,selected) (This)->lpVtbl->GetStreamSelection(This,index,selected)
#define IMFSourceReaderEx_SetStreamSelection(This,index,selected) (This)->lpVtbl->SetStreamSelection(This,index,selected)
#define IMFSourceReaderEx_GetNativeMediaType(This,index,typeindex,type) (This)->lpVtbl->GetNativeMediaType(This,index,typeindex,type)
#define IMFSourceReaderEx_GetCurrentMediaType(This,index,type) (This)->lpVtbl->GetCurrentMediaType(This,index,type)
#define IMFSourceReaderEx_SetCurrentMediaType(This,index,reserved,type) (This)->lpVtbl->SetCurrentMediaType(This,index,reserved,type)
#define IMFSourceReaderEx_SetCurrentPosition(This,format,position) (This)->lpVtbl->SetCurrentPosition(This,format,position)
#define IMFSourceReaderEx_ReadSample(This,index,flags,actualindex,sampleflags,timestamp,sample) (This)->lpVtbl->ReadSample(This,index,flags,actualindex,sampleflags,timestamp,sample)
#define IMFSourceReaderEx_Flush(This,index) (This)->lpVtbl->Flush(This,index)
#define IMFSourceReaderEx_GetServiceForStream(This,index,service,riid,object) (This)->lpVtbl->GetServiceForStream(This,index,service,riid,object)
#define IMFSourceReaderEx_GetPresentationAttribute(This,index,guid,attr) (This)->lpVtbl->GetPresentationAttribute(This,index,guid,attr)
/*** IMFSourceReaderEx methods ***/
#define IMFSourceReaderEx_SetNativeMediaType(This,stream_index,media_type,stream_flags) (This)->lpVtbl->SetNativeMediaType(This,stream_index,media_type,stream_flags)
#define IMFSourceReaderEx_AddTransformForStream(This,stream_index,transform) (This)->lpVtbl->AddTransformForStream(This,stream_index,transform)
#define IMFSourceReaderEx_RemoveAllTransformsForStream(This,stream_index) (This)->lpVtbl->RemoveAllTransformsForStream(This,stream_index)
#define IMFSourceReaderEx_GetTransformForStream(This,stream_index,transform_index,category,transform) (This)->lpVtbl->GetTransformForStream(This,stream_index,transform_index,category,transform)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSourceReaderEx_QueryInterface(IMFSourceReaderEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSourceReaderEx_AddRef(IMFSourceReaderEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSourceReaderEx_Release(IMFSourceReaderEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSourceReader methods ***/
static inline HRESULT IMFSourceReaderEx_GetStreamSelection(IMFSourceReaderEx* This,DWORD index,WINBOOL *selected) {
    return This->lpVtbl->GetStreamSelection(This,index,selected);
}
static inline HRESULT IMFSourceReaderEx_SetStreamSelection(IMFSourceReaderEx* This,DWORD index,WINBOOL selected) {
    return This->lpVtbl->SetStreamSelection(This,index,selected);
}
static inline HRESULT IMFSourceReaderEx_GetNativeMediaType(IMFSourceReaderEx* This,DWORD index,DWORD typeindex,IMFMediaType **type) {
    return This->lpVtbl->GetNativeMediaType(This,index,typeindex,type);
}
static inline HRESULT IMFSourceReaderEx_GetCurrentMediaType(IMFSourceReaderEx* This,DWORD index,IMFMediaType **type) {
    return This->lpVtbl->GetCurrentMediaType(This,index,type);
}
static inline HRESULT IMFSourceReaderEx_SetCurrentMediaType(IMFSourceReaderEx* This,DWORD index,DWORD *reserved,IMFMediaType *type) {
    return This->lpVtbl->SetCurrentMediaType(This,index,reserved,type);
}
static inline HRESULT IMFSourceReaderEx_SetCurrentPosition(IMFSourceReaderEx* This,REFGUID format,REFPROPVARIANT position) {
    return This->lpVtbl->SetCurrentPosition(This,format,position);
}
static inline HRESULT IMFSourceReaderEx_ReadSample(IMFSourceReaderEx* This,DWORD index,DWORD flags,DWORD *actualindex,DWORD *sampleflags,LONGLONG *timestamp,IMFSample **sample) {
    return This->lpVtbl->ReadSample(This,index,flags,actualindex,sampleflags,timestamp,sample);
}
static inline HRESULT IMFSourceReaderEx_Flush(IMFSourceReaderEx* This,DWORD index) {
    return This->lpVtbl->Flush(This,index);
}
static inline HRESULT IMFSourceReaderEx_GetServiceForStream(IMFSourceReaderEx* This,DWORD index,REFGUID service,REFIID riid,void **object) {
    return This->lpVtbl->GetServiceForStream(This,index,service,riid,object);
}
static inline HRESULT IMFSourceReaderEx_GetPresentationAttribute(IMFSourceReaderEx* This,DWORD index,REFGUID guid,PROPVARIANT *attr) {
    return This->lpVtbl->GetPresentationAttribute(This,index,guid,attr);
}
/*** IMFSourceReaderEx methods ***/
static inline HRESULT IMFSourceReaderEx_SetNativeMediaType(IMFSourceReaderEx* This,DWORD stream_index,IMFMediaType *media_type,DWORD *stream_flags) {
    return This->lpVtbl->SetNativeMediaType(This,stream_index,media_type,stream_flags);
}
static inline HRESULT IMFSourceReaderEx_AddTransformForStream(IMFSourceReaderEx* This,DWORD stream_index,IUnknown *transform) {
    return This->lpVtbl->AddTransformForStream(This,stream_index,transform);
}
static inline HRESULT IMFSourceReaderEx_RemoveAllTransformsForStream(IMFSourceReaderEx* This,DWORD stream_index) {
    return This->lpVtbl->RemoveAllTransformsForStream(This,stream_index);
}
static inline HRESULT IMFSourceReaderEx_GetTransformForStream(IMFSourceReaderEx* This,DWORD stream_index,DWORD transform_index,GUID *category,IMFTransform **transform) {
    return This->lpVtbl->GetTransformForStream(This,stream_index,transform_index,category,transform);
}
#endif
#endif

#endif


#endif  /* __IMFSourceReaderEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFSinkWriter interface
 */
#ifndef __IMFSinkWriter_INTERFACE_DEFINED__
#define __IMFSinkWriter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSinkWriter, 0x3137f1cd, 0xfe5e, 0x4805, 0xa5,0xd8, 0xfb,0x47,0x74,0x48,0xcb,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3137f1cd-fe5e-4805-a5d8-fb477448cb3d")
IMFSinkWriter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddStream(
        IMFMediaType *type,
        DWORD *index) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInputMediaType(
        DWORD index,
        IMFMediaType *type,
        IMFAttributes *parameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginWriting(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteSample(
        DWORD index,
        IMFSample *sample) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendStreamTick(
        DWORD index,
        LONGLONG timestamp) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlaceMarker(
        DWORD index,
        void *context) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifyEndOfSegment(
        DWORD index) = 0;

    virtual HRESULT STDMETHODCALLTYPE Flush(
        DWORD index) = 0;

    virtual HRESULT STDMETHODCALLTYPE Finalize(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetServiceForStream(
        DWORD index,
        REFGUID service,
        REFIID riid,
        void **object) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatistics(
        DWORD index,
        MF_SINK_WRITER_STATISTICS *stats) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSinkWriter, 0x3137f1cd, 0xfe5e, 0x4805, 0xa5,0xd8, 0xfb,0x47,0x74,0x48,0xcb,0x3d)
#endif
#else
typedef struct IMFSinkWriterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSinkWriter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSinkWriter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSinkWriter *This);

    /*** IMFSinkWriter methods ***/
    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IMFSinkWriter *This,
        IMFMediaType *type,
        DWORD *index);

    HRESULT (STDMETHODCALLTYPE *SetInputMediaType)(
        IMFSinkWriter *This,
        DWORD index,
        IMFMediaType *type,
        IMFAttributes *parameters);

    HRESULT (STDMETHODCALLTYPE *BeginWriting)(
        IMFSinkWriter *This);

    HRESULT (STDMETHODCALLTYPE *WriteSample)(
        IMFSinkWriter *This,
        DWORD index,
        IMFSample *sample);

    HRESULT (STDMETHODCALLTYPE *SendStreamTick)(
        IMFSinkWriter *This,
        DWORD index,
        LONGLONG timestamp);

    HRESULT (STDMETHODCALLTYPE *PlaceMarker)(
        IMFSinkWriter *This,
        DWORD index,
        void *context);

    HRESULT (STDMETHODCALLTYPE *NotifyEndOfSegment)(
        IMFSinkWriter *This,
        DWORD index);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        IMFSinkWriter *This,
        DWORD index);

    HRESULT (STDMETHODCALLTYPE *Finalize)(
        IMFSinkWriter *This);

    HRESULT (STDMETHODCALLTYPE *GetServiceForStream)(
        IMFSinkWriter *This,
        DWORD index,
        REFGUID service,
        REFIID riid,
        void **object);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IMFSinkWriter *This,
        DWORD index,
        MF_SINK_WRITER_STATISTICS *stats);

    END_INTERFACE
} IMFSinkWriterVtbl;

interface IMFSinkWriter {
    CONST_VTBL IMFSinkWriterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSinkWriter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSinkWriter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSinkWriter_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSinkWriter methods ***/
#define IMFSinkWriter_AddStream(This,type,index) (This)->lpVtbl->AddStream(This,type,index)
#define IMFSinkWriter_SetInputMediaType(This,index,type,parameters) (This)->lpVtbl->SetInputMediaType(This,index,type,parameters)
#define IMFSinkWriter_BeginWriting(This) (This)->lpVtbl->BeginWriting(This)
#define IMFSinkWriter_WriteSample(This,index,sample) (This)->lpVtbl->WriteSample(This,index,sample)
#define IMFSinkWriter_SendStreamTick(This,index,timestamp) (This)->lpVtbl->SendStreamTick(This,index,timestamp)
#define IMFSinkWriter_PlaceMarker(This,index,context) (This)->lpVtbl->PlaceMarker(This,index,context)
#define IMFSinkWriter_NotifyEndOfSegment(This,index) (This)->lpVtbl->NotifyEndOfSegment(This,index)
#define IMFSinkWriter_Flush(This,index) (This)->lpVtbl->Flush(This,index)
#define IMFSinkWriter_Finalize(This) (This)->lpVtbl->Finalize(This)
#define IMFSinkWriter_GetServiceForStream(This,index,service,riid,object) (This)->lpVtbl->GetServiceForStream(This,index,service,riid,object)
#define IMFSinkWriter_GetStatistics(This,index,stats) (This)->lpVtbl->GetStatistics(This,index,stats)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSinkWriter_QueryInterface(IMFSinkWriter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSinkWriter_AddRef(IMFSinkWriter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSinkWriter_Release(IMFSinkWriter* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSinkWriter methods ***/
static inline HRESULT IMFSinkWriter_AddStream(IMFSinkWriter* This,IMFMediaType *type,DWORD *index) {
    return This->lpVtbl->AddStream(This,type,index);
}
static inline HRESULT IMFSinkWriter_SetInputMediaType(IMFSinkWriter* This,DWORD index,IMFMediaType *type,IMFAttributes *parameters) {
    return This->lpVtbl->SetInputMediaType(This,index,type,parameters);
}
static inline HRESULT IMFSinkWriter_BeginWriting(IMFSinkWriter* This) {
    return This->lpVtbl->BeginWriting(This);
}
static inline HRESULT IMFSinkWriter_WriteSample(IMFSinkWriter* This,DWORD index,IMFSample *sample) {
    return This->lpVtbl->WriteSample(This,index,sample);
}
static inline HRESULT IMFSinkWriter_SendStreamTick(IMFSinkWriter* This,DWORD index,LONGLONG timestamp) {
    return This->lpVtbl->SendStreamTick(This,index,timestamp);
}
static inline HRESULT IMFSinkWriter_PlaceMarker(IMFSinkWriter* This,DWORD index,void *context) {
    return This->lpVtbl->PlaceMarker(This,index,context);
}
static inline HRESULT IMFSinkWriter_NotifyEndOfSegment(IMFSinkWriter* This,DWORD index) {
    return This->lpVtbl->NotifyEndOfSegment(This,index);
}
static inline HRESULT IMFSinkWriter_Flush(IMFSinkWriter* This,DWORD index) {
    return This->lpVtbl->Flush(This,index);
}
static inline HRESULT IMFSinkWriter_Finalize(IMFSinkWriter* This) {
    return This->lpVtbl->Finalize(This);
}
static inline HRESULT IMFSinkWriter_GetServiceForStream(IMFSinkWriter* This,DWORD index,REFGUID service,REFIID riid,void **object) {
    return This->lpVtbl->GetServiceForStream(This,index,service,riid,object);
}
static inline HRESULT IMFSinkWriter_GetStatistics(IMFSinkWriter* This,DWORD index,MF_SINK_WRITER_STATISTICS *stats) {
    return This->lpVtbl->GetStatistics(This,index,stats);
}
#endif
#endif

#endif


#endif  /* __IMFSinkWriter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFSinkWriterEx interface
 */
#ifndef __IMFSinkWriterEx_INTERFACE_DEFINED__
#define __IMFSinkWriterEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSinkWriterEx, 0x588d72ab, 0x5bc1, 0x496a, 0x87,0x14, 0xb7,0x06,0x17,0x14,0x1b,0x25);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("588d72ab-5bc1-496a-8714-b70617141b25")
IMFSinkWriterEx : public IMFSinkWriter
{
    virtual HRESULT STDMETHODCALLTYPE GetTransformForStream(
        DWORD index,
        DWORD tindex,
        GUID *category,
        IMFTransform **transform) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSinkWriterEx, 0x588d72ab, 0x5bc1, 0x496a, 0x87,0x14, 0xb7,0x06,0x17,0x14,0x1b,0x25)
#endif
#else
typedef struct IMFSinkWriterExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSinkWriterEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSinkWriterEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSinkWriterEx *This);

    /*** IMFSinkWriter methods ***/
    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IMFSinkWriterEx *This,
        IMFMediaType *type,
        DWORD *index);

    HRESULT (STDMETHODCALLTYPE *SetInputMediaType)(
        IMFSinkWriterEx *This,
        DWORD index,
        IMFMediaType *type,
        IMFAttributes *parameters);

    HRESULT (STDMETHODCALLTYPE *BeginWriting)(
        IMFSinkWriterEx *This);

    HRESULT (STDMETHODCALLTYPE *WriteSample)(
        IMFSinkWriterEx *This,
        DWORD index,
        IMFSample *sample);

    HRESULT (STDMETHODCALLTYPE *SendStreamTick)(
        IMFSinkWriterEx *This,
        DWORD index,
        LONGLONG timestamp);

    HRESULT (STDMETHODCALLTYPE *PlaceMarker)(
        IMFSinkWriterEx *This,
        DWORD index,
        void *context);

    HRESULT (STDMETHODCALLTYPE *NotifyEndOfSegment)(
        IMFSinkWriterEx *This,
        DWORD index);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        IMFSinkWriterEx *This,
        DWORD index);

    HRESULT (STDMETHODCALLTYPE *Finalize)(
        IMFSinkWriterEx *This);

    HRESULT (STDMETHODCALLTYPE *GetServiceForStream)(
        IMFSinkWriterEx *This,
        DWORD index,
        REFGUID service,
        REFIID riid,
        void **object);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IMFSinkWriterEx *This,
        DWORD index,
        MF_SINK_WRITER_STATISTICS *stats);

    /*** IMFSinkWriterEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTransformForStream)(
        IMFSinkWriterEx *This,
        DWORD index,
        DWORD tindex,
        GUID *category,
        IMFTransform **transform);

    END_INTERFACE
} IMFSinkWriterExVtbl;

interface IMFSinkWriterEx {
    CONST_VTBL IMFSinkWriterExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSinkWriterEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSinkWriterEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSinkWriterEx_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSinkWriter methods ***/
#define IMFSinkWriterEx_AddStream(This,type,index) (This)->lpVtbl->AddStream(This,type,index)
#define IMFSinkWriterEx_SetInputMediaType(This,index,type,parameters) (This)->lpVtbl->SetInputMediaType(This,index,type,parameters)
#define IMFSinkWriterEx_BeginWriting(This) (This)->lpVtbl->BeginWriting(This)
#define IMFSinkWriterEx_WriteSample(This,index,sample) (This)->lpVtbl->WriteSample(This,index,sample)
#define IMFSinkWriterEx_SendStreamTick(This,index,timestamp) (This)->lpVtbl->SendStreamTick(This,index,timestamp)
#define IMFSinkWriterEx_PlaceMarker(This,index,context) (This)->lpVtbl->PlaceMarker(This,index,context)
#define IMFSinkWriterEx_NotifyEndOfSegment(This,index) (This)->lpVtbl->NotifyEndOfSegment(This,index)
#define IMFSinkWriterEx_Flush(This,index) (This)->lpVtbl->Flush(This,index)
#define IMFSinkWriterEx_Finalize(This) (This)->lpVtbl->Finalize(This)
#define IMFSinkWriterEx_GetServiceForStream(This,index,service,riid,object) (This)->lpVtbl->GetServiceForStream(This,index,service,riid,object)
#define IMFSinkWriterEx_GetStatistics(This,index,stats) (This)->lpVtbl->GetStatistics(This,index,stats)
/*** IMFSinkWriterEx methods ***/
#define IMFSinkWriterEx_GetTransformForStream(This,index,tindex,category,transform) (This)->lpVtbl->GetTransformForStream(This,index,tindex,category,transform)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSinkWriterEx_QueryInterface(IMFSinkWriterEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSinkWriterEx_AddRef(IMFSinkWriterEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSinkWriterEx_Release(IMFSinkWriterEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSinkWriter methods ***/
static inline HRESULT IMFSinkWriterEx_AddStream(IMFSinkWriterEx* This,IMFMediaType *type,DWORD *index) {
    return This->lpVtbl->AddStream(This,type,index);
}
static inline HRESULT IMFSinkWriterEx_SetInputMediaType(IMFSinkWriterEx* This,DWORD index,IMFMediaType *type,IMFAttributes *parameters) {
    return This->lpVtbl->SetInputMediaType(This,index,type,parameters);
}
static inline HRESULT IMFSinkWriterEx_BeginWriting(IMFSinkWriterEx* This) {
    return This->lpVtbl->BeginWriting(This);
}
static inline HRESULT IMFSinkWriterEx_WriteSample(IMFSinkWriterEx* This,DWORD index,IMFSample *sample) {
    return This->lpVtbl->WriteSample(This,index,sample);
}
static inline HRESULT IMFSinkWriterEx_SendStreamTick(IMFSinkWriterEx* This,DWORD index,LONGLONG timestamp) {
    return This->lpVtbl->SendStreamTick(This,index,timestamp);
}
static inline HRESULT IMFSinkWriterEx_PlaceMarker(IMFSinkWriterEx* This,DWORD index,void *context) {
    return This->lpVtbl->PlaceMarker(This,index,context);
}
static inline HRESULT IMFSinkWriterEx_NotifyEndOfSegment(IMFSinkWriterEx* This,DWORD index) {
    return This->lpVtbl->NotifyEndOfSegment(This,index);
}
static inline HRESULT IMFSinkWriterEx_Flush(IMFSinkWriterEx* This,DWORD index) {
    return This->lpVtbl->Flush(This,index);
}
static inline HRESULT IMFSinkWriterEx_Finalize(IMFSinkWriterEx* This) {
    return This->lpVtbl->Finalize(This);
}
static inline HRESULT IMFSinkWriterEx_GetServiceForStream(IMFSinkWriterEx* This,DWORD index,REFGUID service,REFIID riid,void **object) {
    return This->lpVtbl->GetServiceForStream(This,index,service,riid,object);
}
static inline HRESULT IMFSinkWriterEx_GetStatistics(IMFSinkWriterEx* This,DWORD index,MF_SINK_WRITER_STATISTICS *stats) {
    return This->lpVtbl->GetStatistics(This,index,stats);
}
/*** IMFSinkWriterEx methods ***/
static inline HRESULT IMFSinkWriterEx_GetTransformForStream(IMFSinkWriterEx* This,DWORD index,DWORD tindex,GUID *category,IMFTransform **transform) {
    return This->lpVtbl->GetTransformForStream(This,index,tindex,category,transform);
}
#endif
#endif

#endif


#endif  /* __IMFSinkWriterEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFSinkWriterEncoderConfig interface
 */
#ifndef __IMFSinkWriterEncoderConfig_INTERFACE_DEFINED__
#define __IMFSinkWriterEncoderConfig_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSinkWriterEncoderConfig, 0x17c3779e, 0x3cde, 0x4ede, 0x8c,0x60, 0x38,0x99,0xf5,0xf5,0x3a,0xd6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("17c3779e-3cde-4ede-8c60-3899f5f53ad6")
IMFSinkWriterEncoderConfig : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetTargetMediaType(
        DWORD stream_index,
        IMFMediaType *media_type,
        IMFAttributes *encoding_parameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE PlaceEncodingParameters(
        DWORD stream_index,
        IMFAttributes *encoding_parameters) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSinkWriterEncoderConfig, 0x17c3779e, 0x3cde, 0x4ede, 0x8c,0x60, 0x38,0x99,0xf5,0xf5,0x3a,0xd6)
#endif
#else
typedef struct IMFSinkWriterEncoderConfigVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSinkWriterEncoderConfig *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSinkWriterEncoderConfig *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSinkWriterEncoderConfig *This);

    /*** IMFSinkWriterEncoderConfig methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTargetMediaType)(
        IMFSinkWriterEncoderConfig *This,
        DWORD stream_index,
        IMFMediaType *media_type,
        IMFAttributes *encoding_parameters);

    HRESULT (STDMETHODCALLTYPE *PlaceEncodingParameters)(
        IMFSinkWriterEncoderConfig *This,
        DWORD stream_index,
        IMFAttributes *encoding_parameters);

    END_INTERFACE
} IMFSinkWriterEncoderConfigVtbl;

interface IMFSinkWriterEncoderConfig {
    CONST_VTBL IMFSinkWriterEncoderConfigVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSinkWriterEncoderConfig_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSinkWriterEncoderConfig_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSinkWriterEncoderConfig_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSinkWriterEncoderConfig methods ***/
#define IMFSinkWriterEncoderConfig_SetTargetMediaType(This,stream_index,media_type,encoding_parameters) (This)->lpVtbl->SetTargetMediaType(This,stream_index,media_type,encoding_parameters)
#define IMFSinkWriterEncoderConfig_PlaceEncodingParameters(This,stream_index,encoding_parameters) (This)->lpVtbl->PlaceEncodingParameters(This,stream_index,encoding_parameters)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSinkWriterEncoderConfig_QueryInterface(IMFSinkWriterEncoderConfig* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSinkWriterEncoderConfig_AddRef(IMFSinkWriterEncoderConfig* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSinkWriterEncoderConfig_Release(IMFSinkWriterEncoderConfig* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSinkWriterEncoderConfig methods ***/
static inline HRESULT IMFSinkWriterEncoderConfig_SetTargetMediaType(IMFSinkWriterEncoderConfig* This,DWORD stream_index,IMFMediaType *media_type,IMFAttributes *encoding_parameters) {
    return This->lpVtbl->SetTargetMediaType(This,stream_index,media_type,encoding_parameters);
}
static inline HRESULT IMFSinkWriterEncoderConfig_PlaceEncodingParameters(IMFSinkWriterEncoderConfig* This,DWORD stream_index,IMFAttributes *encoding_parameters) {
    return This->lpVtbl->PlaceEncodingParameters(This,stream_index,encoding_parameters);
}
#endif
#endif

#endif


#endif  /* __IMFSinkWriterEncoderConfig_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFSinkWriterCallback interface
 */
#ifndef __IMFSinkWriterCallback_INTERFACE_DEFINED__
#define __IMFSinkWriterCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSinkWriterCallback, 0x666f76de, 0x33d2, 0x41b9, 0xa4,0x58, 0x29,0xed,0x0a,0x97,0x2c,0x58);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("666f76de-33d2-41b9-a458-29ed0a972c58")
IMFSinkWriterCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnFinalize(
        HRESULT status) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnMarker(
        DWORD stream_index,
        void *context) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSinkWriterCallback, 0x666f76de, 0x33d2, 0x41b9, 0xa4,0x58, 0x29,0xed,0x0a,0x97,0x2c,0x58)
#endif
#else
typedef struct IMFSinkWriterCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSinkWriterCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSinkWriterCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSinkWriterCallback *This);

    /*** IMFSinkWriterCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnFinalize)(
        IMFSinkWriterCallback *This,
        HRESULT status);

    HRESULT (STDMETHODCALLTYPE *OnMarker)(
        IMFSinkWriterCallback *This,
        DWORD stream_index,
        void *context);

    END_INTERFACE
} IMFSinkWriterCallbackVtbl;

interface IMFSinkWriterCallback {
    CONST_VTBL IMFSinkWriterCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSinkWriterCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSinkWriterCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSinkWriterCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSinkWriterCallback methods ***/
#define IMFSinkWriterCallback_OnFinalize(This,status) (This)->lpVtbl->OnFinalize(This,status)
#define IMFSinkWriterCallback_OnMarker(This,stream_index,context) (This)->lpVtbl->OnMarker(This,stream_index,context)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSinkWriterCallback_QueryInterface(IMFSinkWriterCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSinkWriterCallback_AddRef(IMFSinkWriterCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSinkWriterCallback_Release(IMFSinkWriterCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSinkWriterCallback methods ***/
static inline HRESULT IMFSinkWriterCallback_OnFinalize(IMFSinkWriterCallback* This,HRESULT status) {
    return This->lpVtbl->OnFinalize(This,status);
}
static inline HRESULT IMFSinkWriterCallback_OnMarker(IMFSinkWriterCallback* This,DWORD stream_index,void *context) {
    return This->lpVtbl->OnMarker(This,stream_index,context);
}
#endif
#endif

#endif


#endif  /* __IMFSinkWriterCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFSinkWriterCallback2 interface
 */
#ifndef __IMFSinkWriterCallback2_INTERFACE_DEFINED__
#define __IMFSinkWriterCallback2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFSinkWriterCallback2, 0x2456bd58, 0xc067, 0x4513, 0x84,0xfe, 0x8d,0x0c,0x88,0xff,0xdc,0x61);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2456bd58-c067-4513-84fe-8d0c88ffdc61")
IMFSinkWriterCallback2 : public IMFSinkWriterCallback
{
    virtual HRESULT STDMETHODCALLTYPE OnTransformChange(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnStreamError(
        DWORD stream_index,
        HRESULT status) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFSinkWriterCallback2, 0x2456bd58, 0xc067, 0x4513, 0x84,0xfe, 0x8d,0x0c,0x88,0xff,0xdc,0x61)
#endif
#else
typedef struct IMFSinkWriterCallback2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFSinkWriterCallback2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFSinkWriterCallback2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFSinkWriterCallback2 *This);

    /*** IMFSinkWriterCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnFinalize)(
        IMFSinkWriterCallback2 *This,
        HRESULT status);

    HRESULT (STDMETHODCALLTYPE *OnMarker)(
        IMFSinkWriterCallback2 *This,
        DWORD stream_index,
        void *context);

    /*** IMFSinkWriterCallback2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnTransformChange)(
        IMFSinkWriterCallback2 *This);

    HRESULT (STDMETHODCALLTYPE *OnStreamError)(
        IMFSinkWriterCallback2 *This,
        DWORD stream_index,
        HRESULT status);

    END_INTERFACE
} IMFSinkWriterCallback2Vtbl;

interface IMFSinkWriterCallback2 {
    CONST_VTBL IMFSinkWriterCallback2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFSinkWriterCallback2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFSinkWriterCallback2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFSinkWriterCallback2_Release(This) (This)->lpVtbl->Release(This)
/*** IMFSinkWriterCallback methods ***/
#define IMFSinkWriterCallback2_OnFinalize(This,status) (This)->lpVtbl->OnFinalize(This,status)
#define IMFSinkWriterCallback2_OnMarker(This,stream_index,context) (This)->lpVtbl->OnMarker(This,stream_index,context)
/*** IMFSinkWriterCallback2 methods ***/
#define IMFSinkWriterCallback2_OnTransformChange(This) (This)->lpVtbl->OnTransformChange(This)
#define IMFSinkWriterCallback2_OnStreamError(This,stream_index,status) (This)->lpVtbl->OnStreamError(This,stream_index,status)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFSinkWriterCallback2_QueryInterface(IMFSinkWriterCallback2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFSinkWriterCallback2_AddRef(IMFSinkWriterCallback2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFSinkWriterCallback2_Release(IMFSinkWriterCallback2* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFSinkWriterCallback methods ***/
static inline HRESULT IMFSinkWriterCallback2_OnFinalize(IMFSinkWriterCallback2* This,HRESULT status) {
    return This->lpVtbl->OnFinalize(This,status);
}
static inline HRESULT IMFSinkWriterCallback2_OnMarker(IMFSinkWriterCallback2* This,DWORD stream_index,void *context) {
    return This->lpVtbl->OnMarker(This,stream_index,context);
}
/*** IMFSinkWriterCallback2 methods ***/
static inline HRESULT IMFSinkWriterCallback2_OnTransformChange(IMFSinkWriterCallback2* This) {
    return This->lpVtbl->OnTransformChange(This);
}
static inline HRESULT IMFSinkWriterCallback2_OnStreamError(IMFSinkWriterCallback2* This,DWORD stream_index,HRESULT status) {
    return This->lpVtbl->OnStreamError(This,stream_index,status);
}
#endif
#endif

#endif


#endif  /* __IMFSinkWriterCallback2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFReadWriteClassFactory interface
 */
#ifndef __IMFReadWriteClassFactory_INTERFACE_DEFINED__
#define __IMFReadWriteClassFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFReadWriteClassFactory, 0xe7fe2e12, 0x661c, 0x40da, 0x92,0xf9, 0x4f,0x00,0x2a,0xb6,0x76,0x27);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e7fe2e12-661c-40da-92f9-4f002ab67627")
IMFReadWriteClassFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateInstanceFromURL(
        REFCLSID clsid,
        LPCWSTR url,
        IMFAttributes *attributes,
        REFIID riid,
        void **object) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateInstanceFromObject(
        REFCLSID clsid,
        IUnknown *unk,
        IMFAttributes *attributes,
        REFIID riid,
        void **object) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFReadWriteClassFactory, 0xe7fe2e12, 0x661c, 0x40da, 0x92,0xf9, 0x4f,0x00,0x2a,0xb6,0x76,0x27)
#endif
#else
typedef struct IMFReadWriteClassFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFReadWriteClassFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFReadWriteClassFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFReadWriteClassFactory *This);

    /*** IMFReadWriteClassFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstanceFromURL)(
        IMFReadWriteClassFactory *This,
        REFCLSID clsid,
        LPCWSTR url,
        IMFAttributes *attributes,
        REFIID riid,
        void **object);

    HRESULT (STDMETHODCALLTYPE *CreateInstanceFromObject)(
        IMFReadWriteClassFactory *This,
        REFCLSID clsid,
        IUnknown *unk,
        IMFAttributes *attributes,
        REFIID riid,
        void **object);

    END_INTERFACE
} IMFReadWriteClassFactoryVtbl;

interface IMFReadWriteClassFactory {
    CONST_VTBL IMFReadWriteClassFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFReadWriteClassFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFReadWriteClassFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFReadWriteClassFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IMFReadWriteClassFactory methods ***/
#define IMFReadWriteClassFactory_CreateInstanceFromURL(This,clsid,url,attributes,riid,object) (This)->lpVtbl->CreateInstanceFromURL(This,clsid,url,attributes,riid,object)
#define IMFReadWriteClassFactory_CreateInstanceFromObject(This,clsid,unk,attributes,riid,object) (This)->lpVtbl->CreateInstanceFromObject(This,clsid,unk,attributes,riid,object)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFReadWriteClassFactory_QueryInterface(IMFReadWriteClassFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFReadWriteClassFactory_AddRef(IMFReadWriteClassFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFReadWriteClassFactory_Release(IMFReadWriteClassFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFReadWriteClassFactory methods ***/
static inline HRESULT IMFReadWriteClassFactory_CreateInstanceFromURL(IMFReadWriteClassFactory* This,REFCLSID clsid,LPCWSTR url,IMFAttributes *attributes,REFIID riid,void **object) {
    return This->lpVtbl->CreateInstanceFromURL(This,clsid,url,attributes,riid,object);
}
static inline HRESULT IMFReadWriteClassFactory_CreateInstanceFromObject(IMFReadWriteClassFactory* This,REFCLSID clsid,IUnknown *unk,IMFAttributes *attributes,REFIID riid,void **object) {
    return This->lpVtbl->CreateInstanceFromObject(This,clsid,unk,attributes,riid,object);
}
#endif
#endif

#endif


#endif  /* __IMFReadWriteClassFactory_INTERFACE_DEFINED__ */

HRESULT WINAPI MFCreateSinkWriterFromMediaSink(IMFMediaSink *sink, IMFAttributes *attributes,
        IMFSinkWriter **writer);
HRESULT WINAPI MFCreateSinkWriterFromURL(const WCHAR *url, IMFByteStream *bytestream,
        IMFAttributes *attributes, IMFSinkWriter **writer);
HRESULT WINAPI MFCreateSourceReaderFromByteStream(IMFByteStream *stream, IMFAttributes *attributes,
                                                  IMFSourceReader **reader);
HRESULT WINAPI MFCreateSourceReaderFromMediaSource(IMFMediaSource *source, IMFAttributes *attributes,
                                                   IMFSourceReader **reader);
HRESULT WINAPI MFCreateSourceReaderFromURL(const WCHAR *url, IMFAttributes *attributes, IMFSourceReader **reader);
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __mfreadwrite_h__ */
