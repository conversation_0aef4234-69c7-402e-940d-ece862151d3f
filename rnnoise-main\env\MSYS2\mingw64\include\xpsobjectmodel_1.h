/*** Autogenerated by WIDL 10.12 from include/xpsobjectmodel_1.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __xpsobjectmodel_1_h__
#define __xpsobjectmodel_1_h__

/* Forward declarations */

#ifndef __IXpsOMObjectFactory1_FWD_DEFINED__
#define __IXpsOMObjectFactory1_FWD_DEFINED__
typedef interface IXpsOMObjectFactory1 IXpsOMObjectFactory1;
#ifdef __cplusplus
interface IXpsOMObjectFactory1;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPackage1_FWD_DEFINED__
#define __IXpsOMPackage1_FWD_DEFINED__
typedef interface IXpsOMPackage1 IXpsOMPackage1;
#ifdef __cplusplus
interface IXpsOMPackage1;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPage1_FWD_DEFINED__
#define __IXpsOMPage1_FWD_DEFINED__
typedef interface IXpsOMPage1 IXpsOMPage1;
#ifdef __cplusplus
interface IXpsOMPage1;
#endif /* __cplusplus */
#endif

#ifndef __IXpsDocumentPackageTarget_FWD_DEFINED__
#define __IXpsDocumentPackageTarget_FWD_DEFINED__
typedef interface IXpsDocumentPackageTarget IXpsDocumentPackageTarget;
#ifdef __cplusplus
interface IXpsDocumentPackageTarget;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMRemoteDictionaryResource1_FWD_DEFINED__
#define __IXpsOMRemoteDictionaryResource1_FWD_DEFINED__
typedef interface IXpsOMRemoteDictionaryResource1 IXpsOMRemoteDictionaryResource1;
#ifdef __cplusplus
interface IXpsOMRemoteDictionaryResource1;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <xpsobjectmodel.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if NTDDI_VERSION >= 0x06020000
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_APP)
#define XPS_E_INVALID_NUMBER_OF_POINTS_IN_CURVE_SEGMENTS MAKE_HRESULT(1, FACILITY_XPS, 0x600)
#define XPS_E_ABSOLUTE_REFERENCE MAKE_HRESULT(1, FACILITY_XPS, 0x601)
#define XPS_E_INVALID_NUMBER_OF_COLOR_CHANNELS MAKE_HRESULT(1, FACILITY_XPS, 0x602)

#ifndef __IXpsOMObjectFactory1_FWD_DEFINED__
#define __IXpsOMObjectFactory1_FWD_DEFINED__
typedef interface IXpsOMObjectFactory1 IXpsOMObjectFactory1;
#ifdef __cplusplus
interface IXpsOMObjectFactory1;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPackage1_FWD_DEFINED__
#define __IXpsOMPackage1_FWD_DEFINED__
typedef interface IXpsOMPackage1 IXpsOMPackage1;
#ifdef __cplusplus
interface IXpsOMPackage1;
#endif /* __cplusplus */
#endif

#ifndef __IXpsOMPage1_FWD_DEFINED__
#define __IXpsOMPage1_FWD_DEFINED__
typedef interface IXpsOMPage1 IXpsOMPage1;
#ifdef __cplusplus
interface IXpsOMPage1;
#endif /* __cplusplus */
#endif

typedef enum __WIDL_xpsobjectmodel_1_generated_name_0000004A {
    XPS_DOCUMENT_TYPE_UNSPECIFIED = 1,
    XPS_DOCUMENT_TYPE_XPS = 2,
    XPS_DOCUMENT_TYPE_OPENXPS = 3
} XPS_DOCUMENT_TYPE;
/*****************************************************************************
 * IXpsOMObjectFactory1 interface
 */
#ifndef __IXpsOMObjectFactory1_INTERFACE_DEFINED__
#define __IXpsOMObjectFactory1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMObjectFactory1, 0x0a91b617, 0xd612, 0x4181, 0xbf,0x7c, 0xbe,0x58,0x24,0xe9,0xcc,0x8f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0a91b617-d612-4181-bf7c-be5824e9cc8f")
IXpsOMObjectFactory1 : public IXpsOMObjectFactory
{
    virtual HRESULT STDMETHODCALLTYPE GetDocumentTypeFromFile(
        LPCWSTR filename,
        XPS_DOCUMENT_TYPE *documentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocumentTypeFromStream(
        IStream *xpsDocumentStream,
        XPS_DOCUMENT_TYPE *documentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConvertHDPhotoToJpegXR(
        IXpsOMImageResource *imageResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE ConvertJpegXRToHDPhoto(
        IXpsOMImageResource *imageResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePackageWriterOnFile1(
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes,
        WINBOOL optimizeMarkupSize,
        XPS_INTERLEAVING interleaving,
        IOpcPartUri *documentSequencePartName,
        IXpsOMCoreProperties *coreProperties,
        IXpsOMImageResource *packageThumbnail,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        XPS_DOCUMENT_TYPE documentType,
        IXpsOMPackageWriter **packageWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePackageWriterOnStream1(
        ISequentialStream *outputStream,
        WINBOOL optimizeMarkupSize,
        XPS_INTERLEAVING interleaving,
        IOpcPartUri *documentSequencePartName,
        IXpsOMCoreProperties *coreProperties,
        IXpsOMImageResource *packageThumbnail,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        XPS_DOCUMENT_TYPE documentType,
        IXpsOMPackageWriter **packageWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePackage1(
        IXpsOMPackage1 **package) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePackageFromStream1(
        IStream *stream,
        WINBOOL reuseObjects,
        IXpsOMPackage1 **package) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePackageFromFile1(
        LPCWSTR filename,
        WINBOOL reuseObjects,
        IXpsOMPackage1 **package) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePage1(
        const XPS_SIZE *pageDimensions,
        LPCWSTR language,
        IOpcPartUri *partUri,
        IXpsOMPage1 **page) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreatePageFromStream1(
        IStream *pageMarkupStream,
        IOpcPartUri *partUri,
        IXpsOMPartResources *resources,
        WINBOOL reuseObjects,
        IXpsOMPage1 **page) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRemoteDictionaryResourceFromStream1(
        IStream *dictionaryMarkupStream,
        IOpcPartUri *partUri,
        IXpsOMPartResources *resources,
        IXpsOMRemoteDictionaryResource **dictionaryResource) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMObjectFactory1, 0x0a91b617, 0xd612, 0x4181, 0xbf,0x7c, 0xbe,0x58,0x24,0xe9,0xcc,0x8f)
#endif
#else
typedef struct IXpsOMObjectFactory1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMObjectFactory1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMObjectFactory1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMObjectFactory1 *This);

    /*** IXpsOMObjectFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreatePackage)(
        IXpsOMObjectFactory1 *This,
        IXpsOMPackage **package);

    HRESULT (STDMETHODCALLTYPE *CreatePackageFromFile)(
        IXpsOMObjectFactory1 *This,
        LPCWSTR filename,
        WINBOOL reuseObjects,
        IXpsOMPackage **package);

    HRESULT (STDMETHODCALLTYPE *CreatePackageFromStream)(
        IXpsOMObjectFactory1 *This,
        IStream *stream,
        WINBOOL reuseObjects,
        IXpsOMPackage **package);

    HRESULT (STDMETHODCALLTYPE *CreateStoryFragmentsResource)(
        IXpsOMObjectFactory1 *This,
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMStoryFragmentsResource **storyFragmentsResource);

    HRESULT (STDMETHODCALLTYPE *CreateDocumentStructureResource)(
        IXpsOMObjectFactory1 *This,
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMDocumentStructureResource **documentStructureResource);

    HRESULT (STDMETHODCALLTYPE *CreateSignatureBlockResource)(
        IXpsOMObjectFactory1 *This,
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMSignatureBlockResource **signatureBlockResource);

    HRESULT (STDMETHODCALLTYPE *CreateRemoteDictionaryResource)(
        IXpsOMObjectFactory1 *This,
        IXpsOMDictionary *dictionary,
        IOpcPartUri *partUri,
        IXpsOMRemoteDictionaryResource **remoteDictionaryResource);

    HRESULT (STDMETHODCALLTYPE *CreateRemoteDictionaryResourceFromStream)(
        IXpsOMObjectFactory1 *This,
        IStream *dictionaryMarkupStream,
        IOpcPartUri *dictionaryPartUri,
        IXpsOMPartResources *resources,
        IXpsOMRemoteDictionaryResource **dictionaryResource);

    HRESULT (STDMETHODCALLTYPE *CreatePartResources)(
        IXpsOMObjectFactory1 *This,
        IXpsOMPartResources **partResources);

    HRESULT (STDMETHODCALLTYPE *CreateDocumentSequence)(
        IXpsOMObjectFactory1 *This,
        IOpcPartUri *partUri,
        IXpsOMDocumentSequence **documentSequence);

    HRESULT (STDMETHODCALLTYPE *CreateDocument)(
        IXpsOMObjectFactory1 *This,
        IOpcPartUri *partUri,
        IXpsOMDocument **document);

    HRESULT (STDMETHODCALLTYPE *CreatePageReference)(
        IXpsOMObjectFactory1 *This,
        const XPS_SIZE *advisoryPageDimensions,
        IXpsOMPageReference **pageReference);

    HRESULT (STDMETHODCALLTYPE *CreatePage)(
        IXpsOMObjectFactory1 *This,
        const XPS_SIZE *pageDimensions,
        LPCWSTR language,
        IOpcPartUri *partUri,
        IXpsOMPage **page);

    HRESULT (STDMETHODCALLTYPE *CreatePageFromStream)(
        IXpsOMObjectFactory1 *This,
        IStream *pageMarkupStream,
        IOpcPartUri *partUri,
        IXpsOMPartResources *resources,
        WINBOOL reuseObjects,
        IXpsOMPage **page);

    HRESULT (STDMETHODCALLTYPE *CreateCanvas)(
        IXpsOMObjectFactory1 *This,
        IXpsOMCanvas **canvas);

    HRESULT (STDMETHODCALLTYPE *CreateGlyphs)(
        IXpsOMObjectFactory1 *This,
        IXpsOMFontResource *fontResource,
        IXpsOMGlyphs **glyphs);

    HRESULT (STDMETHODCALLTYPE *CreatePath)(
        IXpsOMObjectFactory1 *This,
        IXpsOMPath **path);

    HRESULT (STDMETHODCALLTYPE *CreateGeometry)(
        IXpsOMObjectFactory1 *This,
        IXpsOMGeometry **geometry);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryFigure)(
        IXpsOMObjectFactory1 *This,
        const XPS_POINT *startPoint,
        IXpsOMGeometryFigure **figure);

    HRESULT (STDMETHODCALLTYPE *CreateMatrixTransform)(
        IXpsOMObjectFactory1 *This,
        const XPS_MATRIX *matrix,
        IXpsOMMatrixTransform **transform);

    HRESULT (STDMETHODCALLTYPE *CreateSolidColorBrush)(
        IXpsOMObjectFactory1 *This,
        const XPS_COLOR *color,
        IXpsOMColorProfileResource *colorProfile,
        IXpsOMSolidColorBrush **solidColorBrush);

    HRESULT (STDMETHODCALLTYPE *CreateColorProfileResource)(
        IXpsOMObjectFactory1 *This,
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMColorProfileResource **colorProfileResource);

    HRESULT (STDMETHODCALLTYPE *CreateImageBrush)(
        IXpsOMObjectFactory1 *This,
        IXpsOMImageResource *image,
        const XPS_RECT *viewBox,
        const XPS_RECT *viewPort,
        IXpsOMImageBrush **imageBrush);

    HRESULT (STDMETHODCALLTYPE *CreateVisualBrush)(
        IXpsOMObjectFactory1 *This,
        const XPS_RECT *viewBox,
        const XPS_RECT *viewPort,
        IXpsOMVisualBrush **visualBrush);

    HRESULT (STDMETHODCALLTYPE *CreateImageResource)(
        IXpsOMObjectFactory1 *This,
        IStream *acquiredStream,
        XPS_IMAGE_TYPE contentType,
        IOpcPartUri *partUri,
        IXpsOMImageResource **imageResource);

    HRESULT (STDMETHODCALLTYPE *CreatePrintTicketResource)(
        IXpsOMObjectFactory1 *This,
        IStream *acquiredStream,
        IOpcPartUri *partUri,
        IXpsOMPrintTicketResource **printTicketResource);

    HRESULT (STDMETHODCALLTYPE *CreateFontResource)(
        IXpsOMObjectFactory1 *This,
        IStream *acquiredStream,
        XPS_FONT_EMBEDDING fontEmbedding,
        IOpcPartUri *partUri,
        WINBOOL isObfSourceStream,
        IXpsOMFontResource **fontResource);

    HRESULT (STDMETHODCALLTYPE *CreateGradientStop)(
        IXpsOMObjectFactory1 *This,
        const XPS_COLOR *color,
        IXpsOMColorProfileResource *colorProfile,
        FLOAT offset,
        IXpsOMGradientStop **gradientStop);

    HRESULT (STDMETHODCALLTYPE *CreateLinearGradientBrush)(
        IXpsOMObjectFactory1 *This,
        IXpsOMGradientStop *gradStop1,
        IXpsOMGradientStop *gradStop2,
        const XPS_POINT *startPoint,
        const XPS_POINT *endPoint,
        IXpsOMLinearGradientBrush **linearGradientBrush);

    HRESULT (STDMETHODCALLTYPE *CreateRadialGradientBrush)(
        IXpsOMObjectFactory1 *This,
        IXpsOMGradientStop *gradStop1,
        IXpsOMGradientStop *gradStop2,
        const XPS_POINT *centerPoint,
        const XPS_POINT *gradientOrigin,
        const XPS_SIZE *radiiSizes,
        IXpsOMRadialGradientBrush **radialGradientBrush);

    HRESULT (STDMETHODCALLTYPE *CreateCoreProperties)(
        IXpsOMObjectFactory1 *This,
        IOpcPartUri *partUri,
        IXpsOMCoreProperties **coreProperties);

    HRESULT (STDMETHODCALLTYPE *CreateDictionary)(
        IXpsOMObjectFactory1 *This,
        IXpsOMDictionary **dictionary);

    HRESULT (STDMETHODCALLTYPE *CreatePartUriCollection)(
        IXpsOMObjectFactory1 *This,
        IXpsOMPartUriCollection **partUriCollection);

    HRESULT (STDMETHODCALLTYPE *CreatePackageWriterOnFile)(
        IXpsOMObjectFactory1 *This,
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes,
        WINBOOL optimizeMarkupSize,
        XPS_INTERLEAVING interleaving,
        IOpcPartUri *documentSequencePartName,
        IXpsOMCoreProperties *coreProperties,
        IXpsOMImageResource *packageThumbnail,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        IXpsOMPackageWriter **packageWriter);

    HRESULT (STDMETHODCALLTYPE *CreatePackageWriterOnStream)(
        IXpsOMObjectFactory1 *This,
        ISequentialStream *outputStream,
        WINBOOL optimizeMarkupSize,
        XPS_INTERLEAVING interleaving,
        IOpcPartUri *documentSequencePartName,
        IXpsOMCoreProperties *coreProperties,
        IXpsOMImageResource *packageThumbnail,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        IXpsOMPackageWriter **packageWriter);

    HRESULT (STDMETHODCALLTYPE *CreatePartUri)(
        IXpsOMObjectFactory1 *This,
        LPCWSTR uri,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *CreateReadOnlyStreamOnFile)(
        IXpsOMObjectFactory1 *This,
        LPCWSTR filename,
        IStream **stream);

    /*** IXpsOMObjectFactory1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentTypeFromFile)(
        IXpsOMObjectFactory1 *This,
        LPCWSTR filename,
        XPS_DOCUMENT_TYPE *documentType);

    HRESULT (STDMETHODCALLTYPE *GetDocumentTypeFromStream)(
        IXpsOMObjectFactory1 *This,
        IStream *xpsDocumentStream,
        XPS_DOCUMENT_TYPE *documentType);

    HRESULT (STDMETHODCALLTYPE *ConvertHDPhotoToJpegXR)(
        IXpsOMObjectFactory1 *This,
        IXpsOMImageResource *imageResource);

    HRESULT (STDMETHODCALLTYPE *ConvertJpegXRToHDPhoto)(
        IXpsOMObjectFactory1 *This,
        IXpsOMImageResource *imageResource);

    HRESULT (STDMETHODCALLTYPE *CreatePackageWriterOnFile1)(
        IXpsOMObjectFactory1 *This,
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes,
        WINBOOL optimizeMarkupSize,
        XPS_INTERLEAVING interleaving,
        IOpcPartUri *documentSequencePartName,
        IXpsOMCoreProperties *coreProperties,
        IXpsOMImageResource *packageThumbnail,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        XPS_DOCUMENT_TYPE documentType,
        IXpsOMPackageWriter **packageWriter);

    HRESULT (STDMETHODCALLTYPE *CreatePackageWriterOnStream1)(
        IXpsOMObjectFactory1 *This,
        ISequentialStream *outputStream,
        WINBOOL optimizeMarkupSize,
        XPS_INTERLEAVING interleaving,
        IOpcPartUri *documentSequencePartName,
        IXpsOMCoreProperties *coreProperties,
        IXpsOMImageResource *packageThumbnail,
        IXpsOMPrintTicketResource *documentSequencePrintTicket,
        IOpcPartUri *discardControlPartName,
        XPS_DOCUMENT_TYPE documentType,
        IXpsOMPackageWriter **packageWriter);

    HRESULT (STDMETHODCALLTYPE *CreatePackage1)(
        IXpsOMObjectFactory1 *This,
        IXpsOMPackage1 **package);

    HRESULT (STDMETHODCALLTYPE *CreatePackageFromStream1)(
        IXpsOMObjectFactory1 *This,
        IStream *stream,
        WINBOOL reuseObjects,
        IXpsOMPackage1 **package);

    HRESULT (STDMETHODCALLTYPE *CreatePackageFromFile1)(
        IXpsOMObjectFactory1 *This,
        LPCWSTR filename,
        WINBOOL reuseObjects,
        IXpsOMPackage1 **package);

    HRESULT (STDMETHODCALLTYPE *CreatePage1)(
        IXpsOMObjectFactory1 *This,
        const XPS_SIZE *pageDimensions,
        LPCWSTR language,
        IOpcPartUri *partUri,
        IXpsOMPage1 **page);

    HRESULT (STDMETHODCALLTYPE *CreatePageFromStream1)(
        IXpsOMObjectFactory1 *This,
        IStream *pageMarkupStream,
        IOpcPartUri *partUri,
        IXpsOMPartResources *resources,
        WINBOOL reuseObjects,
        IXpsOMPage1 **page);

    HRESULT (STDMETHODCALLTYPE *CreateRemoteDictionaryResourceFromStream1)(
        IXpsOMObjectFactory1 *This,
        IStream *dictionaryMarkupStream,
        IOpcPartUri *partUri,
        IXpsOMPartResources *resources,
        IXpsOMRemoteDictionaryResource **dictionaryResource);

    END_INTERFACE
} IXpsOMObjectFactory1Vtbl;

interface IXpsOMObjectFactory1 {
    CONST_VTBL IXpsOMObjectFactory1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMObjectFactory1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMObjectFactory1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMObjectFactory1_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMObjectFactory methods ***/
#define IXpsOMObjectFactory1_CreatePackage(This,package) (This)->lpVtbl->CreatePackage(This,package)
#define IXpsOMObjectFactory1_CreatePackageFromFile(This,filename,reuseObjects,package) (This)->lpVtbl->CreatePackageFromFile(This,filename,reuseObjects,package)
#define IXpsOMObjectFactory1_CreatePackageFromStream(This,stream,reuseObjects,package) (This)->lpVtbl->CreatePackageFromStream(This,stream,reuseObjects,package)
#define IXpsOMObjectFactory1_CreateStoryFragmentsResource(This,acquiredStream,partUri,storyFragmentsResource) (This)->lpVtbl->CreateStoryFragmentsResource(This,acquiredStream,partUri,storyFragmentsResource)
#define IXpsOMObjectFactory1_CreateDocumentStructureResource(This,acquiredStream,partUri,documentStructureResource) (This)->lpVtbl->CreateDocumentStructureResource(This,acquiredStream,partUri,documentStructureResource)
#define IXpsOMObjectFactory1_CreateSignatureBlockResource(This,acquiredStream,partUri,signatureBlockResource) (This)->lpVtbl->CreateSignatureBlockResource(This,acquiredStream,partUri,signatureBlockResource)
#define IXpsOMObjectFactory1_CreateRemoteDictionaryResource(This,dictionary,partUri,remoteDictionaryResource) (This)->lpVtbl->CreateRemoteDictionaryResource(This,dictionary,partUri,remoteDictionaryResource)
#define IXpsOMObjectFactory1_CreateRemoteDictionaryResourceFromStream(This,dictionaryMarkupStream,dictionaryPartUri,resources,dictionaryResource) (This)->lpVtbl->CreateRemoteDictionaryResourceFromStream(This,dictionaryMarkupStream,dictionaryPartUri,resources,dictionaryResource)
#define IXpsOMObjectFactory1_CreatePartResources(This,partResources) (This)->lpVtbl->CreatePartResources(This,partResources)
#define IXpsOMObjectFactory1_CreateDocumentSequence(This,partUri,documentSequence) (This)->lpVtbl->CreateDocumentSequence(This,partUri,documentSequence)
#define IXpsOMObjectFactory1_CreateDocument(This,partUri,document) (This)->lpVtbl->CreateDocument(This,partUri,document)
#define IXpsOMObjectFactory1_CreatePageReference(This,advisoryPageDimensions,pageReference) (This)->lpVtbl->CreatePageReference(This,advisoryPageDimensions,pageReference)
#define IXpsOMObjectFactory1_CreatePage(This,pageDimensions,language,partUri,page) (This)->lpVtbl->CreatePage(This,pageDimensions,language,partUri,page)
#define IXpsOMObjectFactory1_CreatePageFromStream(This,pageMarkupStream,partUri,resources,reuseObjects,page) (This)->lpVtbl->CreatePageFromStream(This,pageMarkupStream,partUri,resources,reuseObjects,page)
#define IXpsOMObjectFactory1_CreateCanvas(This,canvas) (This)->lpVtbl->CreateCanvas(This,canvas)
#define IXpsOMObjectFactory1_CreateGlyphs(This,fontResource,glyphs) (This)->lpVtbl->CreateGlyphs(This,fontResource,glyphs)
#define IXpsOMObjectFactory1_CreatePath(This,path) (This)->lpVtbl->CreatePath(This,path)
#define IXpsOMObjectFactory1_CreateGeometry(This,geometry) (This)->lpVtbl->CreateGeometry(This,geometry)
#define IXpsOMObjectFactory1_CreateGeometryFigure(This,startPoint,figure) (This)->lpVtbl->CreateGeometryFigure(This,startPoint,figure)
#define IXpsOMObjectFactory1_CreateMatrixTransform(This,matrix,transform) (This)->lpVtbl->CreateMatrixTransform(This,matrix,transform)
#define IXpsOMObjectFactory1_CreateSolidColorBrush(This,color,colorProfile,solidColorBrush) (This)->lpVtbl->CreateSolidColorBrush(This,color,colorProfile,solidColorBrush)
#define IXpsOMObjectFactory1_CreateColorProfileResource(This,acquiredStream,partUri,colorProfileResource) (This)->lpVtbl->CreateColorProfileResource(This,acquiredStream,partUri,colorProfileResource)
#define IXpsOMObjectFactory1_CreateImageBrush(This,image,viewBox,viewPort,imageBrush) (This)->lpVtbl->CreateImageBrush(This,image,viewBox,viewPort,imageBrush)
#define IXpsOMObjectFactory1_CreateVisualBrush(This,viewBox,viewPort,visualBrush) (This)->lpVtbl->CreateVisualBrush(This,viewBox,viewPort,visualBrush)
#define IXpsOMObjectFactory1_CreateImageResource(This,acquiredStream,contentType,partUri,imageResource) (This)->lpVtbl->CreateImageResource(This,acquiredStream,contentType,partUri,imageResource)
#define IXpsOMObjectFactory1_CreatePrintTicketResource(This,acquiredStream,partUri,printTicketResource) (This)->lpVtbl->CreatePrintTicketResource(This,acquiredStream,partUri,printTicketResource)
#define IXpsOMObjectFactory1_CreateFontResource(This,acquiredStream,fontEmbedding,partUri,isObfSourceStream,fontResource) (This)->lpVtbl->CreateFontResource(This,acquiredStream,fontEmbedding,partUri,isObfSourceStream,fontResource)
#define IXpsOMObjectFactory1_CreateGradientStop(This,color,colorProfile,offset,gradientStop) (This)->lpVtbl->CreateGradientStop(This,color,colorProfile,offset,gradientStop)
#define IXpsOMObjectFactory1_CreateLinearGradientBrush(This,gradStop1,gradStop2,startPoint,endPoint,linearGradientBrush) (This)->lpVtbl->CreateLinearGradientBrush(This,gradStop1,gradStop2,startPoint,endPoint,linearGradientBrush)
#define IXpsOMObjectFactory1_CreateRadialGradientBrush(This,gradStop1,gradStop2,centerPoint,gradientOrigin,radiiSizes,radialGradientBrush) (This)->lpVtbl->CreateRadialGradientBrush(This,gradStop1,gradStop2,centerPoint,gradientOrigin,radiiSizes,radialGradientBrush)
#define IXpsOMObjectFactory1_CreateCoreProperties(This,partUri,coreProperties) (This)->lpVtbl->CreateCoreProperties(This,partUri,coreProperties)
#define IXpsOMObjectFactory1_CreateDictionary(This,dictionary) (This)->lpVtbl->CreateDictionary(This,dictionary)
#define IXpsOMObjectFactory1_CreatePartUriCollection(This,partUriCollection) (This)->lpVtbl->CreatePartUriCollection(This,partUriCollection)
#define IXpsOMObjectFactory1_CreatePackageWriterOnFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter) (This)->lpVtbl->CreatePackageWriterOnFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter)
#define IXpsOMObjectFactory1_CreatePackageWriterOnStream(This,outputStream,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter) (This)->lpVtbl->CreatePackageWriterOnStream(This,outputStream,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter)
#define IXpsOMObjectFactory1_CreatePartUri(This,uri,partUri) (This)->lpVtbl->CreatePartUri(This,uri,partUri)
#define IXpsOMObjectFactory1_CreateReadOnlyStreamOnFile(This,filename,stream) (This)->lpVtbl->CreateReadOnlyStreamOnFile(This,filename,stream)
/*** IXpsOMObjectFactory1 methods ***/
#define IXpsOMObjectFactory1_GetDocumentTypeFromFile(This,filename,documentType) (This)->lpVtbl->GetDocumentTypeFromFile(This,filename,documentType)
#define IXpsOMObjectFactory1_GetDocumentTypeFromStream(This,xpsDocumentStream,documentType) (This)->lpVtbl->GetDocumentTypeFromStream(This,xpsDocumentStream,documentType)
#define IXpsOMObjectFactory1_ConvertHDPhotoToJpegXR(This,imageResource) (This)->lpVtbl->ConvertHDPhotoToJpegXR(This,imageResource)
#define IXpsOMObjectFactory1_ConvertJpegXRToHDPhoto(This,imageResource) (This)->lpVtbl->ConvertJpegXRToHDPhoto(This,imageResource)
#define IXpsOMObjectFactory1_CreatePackageWriterOnFile1(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,documentType,packageWriter) (This)->lpVtbl->CreatePackageWriterOnFile1(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,documentType,packageWriter)
#define IXpsOMObjectFactory1_CreatePackageWriterOnStream1(This,outputStream,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,documentType,packageWriter) (This)->lpVtbl->CreatePackageWriterOnStream1(This,outputStream,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,documentType,packageWriter)
#define IXpsOMObjectFactory1_CreatePackage1(This,package) (This)->lpVtbl->CreatePackage1(This,package)
#define IXpsOMObjectFactory1_CreatePackageFromStream1(This,stream,reuseObjects,package) (This)->lpVtbl->CreatePackageFromStream1(This,stream,reuseObjects,package)
#define IXpsOMObjectFactory1_CreatePackageFromFile1(This,filename,reuseObjects,package) (This)->lpVtbl->CreatePackageFromFile1(This,filename,reuseObjects,package)
#define IXpsOMObjectFactory1_CreatePage1(This,pageDimensions,language,partUri,page) (This)->lpVtbl->CreatePage1(This,pageDimensions,language,partUri,page)
#define IXpsOMObjectFactory1_CreatePageFromStream1(This,pageMarkupStream,partUri,resources,reuseObjects,page) (This)->lpVtbl->CreatePageFromStream1(This,pageMarkupStream,partUri,resources,reuseObjects,page)
#define IXpsOMObjectFactory1_CreateRemoteDictionaryResourceFromStream1(This,dictionaryMarkupStream,partUri,resources,dictionaryResource) (This)->lpVtbl->CreateRemoteDictionaryResourceFromStream1(This,dictionaryMarkupStream,partUri,resources,dictionaryResource)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMObjectFactory1_QueryInterface(IXpsOMObjectFactory1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMObjectFactory1_AddRef(IXpsOMObjectFactory1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMObjectFactory1_Release(IXpsOMObjectFactory1* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMObjectFactory methods ***/
static inline HRESULT IXpsOMObjectFactory1_CreatePackage(IXpsOMObjectFactory1* This,IXpsOMPackage **package) {
    return This->lpVtbl->CreatePackage(This,package);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePackageFromFile(IXpsOMObjectFactory1* This,LPCWSTR filename,WINBOOL reuseObjects,IXpsOMPackage **package) {
    return This->lpVtbl->CreatePackageFromFile(This,filename,reuseObjects,package);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePackageFromStream(IXpsOMObjectFactory1* This,IStream *stream,WINBOOL reuseObjects,IXpsOMPackage **package) {
    return This->lpVtbl->CreatePackageFromStream(This,stream,reuseObjects,package);
}
static inline HRESULT IXpsOMObjectFactory1_CreateStoryFragmentsResource(IXpsOMObjectFactory1* This,IStream *acquiredStream,IOpcPartUri *partUri,IXpsOMStoryFragmentsResource **storyFragmentsResource) {
    return This->lpVtbl->CreateStoryFragmentsResource(This,acquiredStream,partUri,storyFragmentsResource);
}
static inline HRESULT IXpsOMObjectFactory1_CreateDocumentStructureResource(IXpsOMObjectFactory1* This,IStream *acquiredStream,IOpcPartUri *partUri,IXpsOMDocumentStructureResource **documentStructureResource) {
    return This->lpVtbl->CreateDocumentStructureResource(This,acquiredStream,partUri,documentStructureResource);
}
static inline HRESULT IXpsOMObjectFactory1_CreateSignatureBlockResource(IXpsOMObjectFactory1* This,IStream *acquiredStream,IOpcPartUri *partUri,IXpsOMSignatureBlockResource **signatureBlockResource) {
    return This->lpVtbl->CreateSignatureBlockResource(This,acquiredStream,partUri,signatureBlockResource);
}
static inline HRESULT IXpsOMObjectFactory1_CreateRemoteDictionaryResource(IXpsOMObjectFactory1* This,IXpsOMDictionary *dictionary,IOpcPartUri *partUri,IXpsOMRemoteDictionaryResource **remoteDictionaryResource) {
    return This->lpVtbl->CreateRemoteDictionaryResource(This,dictionary,partUri,remoteDictionaryResource);
}
static inline HRESULT IXpsOMObjectFactory1_CreateRemoteDictionaryResourceFromStream(IXpsOMObjectFactory1* This,IStream *dictionaryMarkupStream,IOpcPartUri *dictionaryPartUri,IXpsOMPartResources *resources,IXpsOMRemoteDictionaryResource **dictionaryResource) {
    return This->lpVtbl->CreateRemoteDictionaryResourceFromStream(This,dictionaryMarkupStream,dictionaryPartUri,resources,dictionaryResource);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePartResources(IXpsOMObjectFactory1* This,IXpsOMPartResources **partResources) {
    return This->lpVtbl->CreatePartResources(This,partResources);
}
static inline HRESULT IXpsOMObjectFactory1_CreateDocumentSequence(IXpsOMObjectFactory1* This,IOpcPartUri *partUri,IXpsOMDocumentSequence **documentSequence) {
    return This->lpVtbl->CreateDocumentSequence(This,partUri,documentSequence);
}
static inline HRESULT IXpsOMObjectFactory1_CreateDocument(IXpsOMObjectFactory1* This,IOpcPartUri *partUri,IXpsOMDocument **document) {
    return This->lpVtbl->CreateDocument(This,partUri,document);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePageReference(IXpsOMObjectFactory1* This,const XPS_SIZE *advisoryPageDimensions,IXpsOMPageReference **pageReference) {
    return This->lpVtbl->CreatePageReference(This,advisoryPageDimensions,pageReference);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePage(IXpsOMObjectFactory1* This,const XPS_SIZE *pageDimensions,LPCWSTR language,IOpcPartUri *partUri,IXpsOMPage **page) {
    return This->lpVtbl->CreatePage(This,pageDimensions,language,partUri,page);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePageFromStream(IXpsOMObjectFactory1* This,IStream *pageMarkupStream,IOpcPartUri *partUri,IXpsOMPartResources *resources,WINBOOL reuseObjects,IXpsOMPage **page) {
    return This->lpVtbl->CreatePageFromStream(This,pageMarkupStream,partUri,resources,reuseObjects,page);
}
static inline HRESULT IXpsOMObjectFactory1_CreateCanvas(IXpsOMObjectFactory1* This,IXpsOMCanvas **canvas) {
    return This->lpVtbl->CreateCanvas(This,canvas);
}
static inline HRESULT IXpsOMObjectFactory1_CreateGlyphs(IXpsOMObjectFactory1* This,IXpsOMFontResource *fontResource,IXpsOMGlyphs **glyphs) {
    return This->lpVtbl->CreateGlyphs(This,fontResource,glyphs);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePath(IXpsOMObjectFactory1* This,IXpsOMPath **path) {
    return This->lpVtbl->CreatePath(This,path);
}
static inline HRESULT IXpsOMObjectFactory1_CreateGeometry(IXpsOMObjectFactory1* This,IXpsOMGeometry **geometry) {
    return This->lpVtbl->CreateGeometry(This,geometry);
}
static inline HRESULT IXpsOMObjectFactory1_CreateGeometryFigure(IXpsOMObjectFactory1* This,const XPS_POINT *startPoint,IXpsOMGeometryFigure **figure) {
    return This->lpVtbl->CreateGeometryFigure(This,startPoint,figure);
}
static inline HRESULT IXpsOMObjectFactory1_CreateMatrixTransform(IXpsOMObjectFactory1* This,const XPS_MATRIX *matrix,IXpsOMMatrixTransform **transform) {
    return This->lpVtbl->CreateMatrixTransform(This,matrix,transform);
}
static inline HRESULT IXpsOMObjectFactory1_CreateSolidColorBrush(IXpsOMObjectFactory1* This,const XPS_COLOR *color,IXpsOMColorProfileResource *colorProfile,IXpsOMSolidColorBrush **solidColorBrush) {
    return This->lpVtbl->CreateSolidColorBrush(This,color,colorProfile,solidColorBrush);
}
static inline HRESULT IXpsOMObjectFactory1_CreateColorProfileResource(IXpsOMObjectFactory1* This,IStream *acquiredStream,IOpcPartUri *partUri,IXpsOMColorProfileResource **colorProfileResource) {
    return This->lpVtbl->CreateColorProfileResource(This,acquiredStream,partUri,colorProfileResource);
}
static inline HRESULT IXpsOMObjectFactory1_CreateImageBrush(IXpsOMObjectFactory1* This,IXpsOMImageResource *image,const XPS_RECT *viewBox,const XPS_RECT *viewPort,IXpsOMImageBrush **imageBrush) {
    return This->lpVtbl->CreateImageBrush(This,image,viewBox,viewPort,imageBrush);
}
static inline HRESULT IXpsOMObjectFactory1_CreateVisualBrush(IXpsOMObjectFactory1* This,const XPS_RECT *viewBox,const XPS_RECT *viewPort,IXpsOMVisualBrush **visualBrush) {
    return This->lpVtbl->CreateVisualBrush(This,viewBox,viewPort,visualBrush);
}
static inline HRESULT IXpsOMObjectFactory1_CreateImageResource(IXpsOMObjectFactory1* This,IStream *acquiredStream,XPS_IMAGE_TYPE contentType,IOpcPartUri *partUri,IXpsOMImageResource **imageResource) {
    return This->lpVtbl->CreateImageResource(This,acquiredStream,contentType,partUri,imageResource);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePrintTicketResource(IXpsOMObjectFactory1* This,IStream *acquiredStream,IOpcPartUri *partUri,IXpsOMPrintTicketResource **printTicketResource) {
    return This->lpVtbl->CreatePrintTicketResource(This,acquiredStream,partUri,printTicketResource);
}
static inline HRESULT IXpsOMObjectFactory1_CreateFontResource(IXpsOMObjectFactory1* This,IStream *acquiredStream,XPS_FONT_EMBEDDING fontEmbedding,IOpcPartUri *partUri,WINBOOL isObfSourceStream,IXpsOMFontResource **fontResource) {
    return This->lpVtbl->CreateFontResource(This,acquiredStream,fontEmbedding,partUri,isObfSourceStream,fontResource);
}
static inline HRESULT IXpsOMObjectFactory1_CreateGradientStop(IXpsOMObjectFactory1* This,const XPS_COLOR *color,IXpsOMColorProfileResource *colorProfile,FLOAT offset,IXpsOMGradientStop **gradientStop) {
    return This->lpVtbl->CreateGradientStop(This,color,colorProfile,offset,gradientStop);
}
static inline HRESULT IXpsOMObjectFactory1_CreateLinearGradientBrush(IXpsOMObjectFactory1* This,IXpsOMGradientStop *gradStop1,IXpsOMGradientStop *gradStop2,const XPS_POINT *startPoint,const XPS_POINT *endPoint,IXpsOMLinearGradientBrush **linearGradientBrush) {
    return This->lpVtbl->CreateLinearGradientBrush(This,gradStop1,gradStop2,startPoint,endPoint,linearGradientBrush);
}
static inline HRESULT IXpsOMObjectFactory1_CreateRadialGradientBrush(IXpsOMObjectFactory1* This,IXpsOMGradientStop *gradStop1,IXpsOMGradientStop *gradStop2,const XPS_POINT *centerPoint,const XPS_POINT *gradientOrigin,const XPS_SIZE *radiiSizes,IXpsOMRadialGradientBrush **radialGradientBrush) {
    return This->lpVtbl->CreateRadialGradientBrush(This,gradStop1,gradStop2,centerPoint,gradientOrigin,radiiSizes,radialGradientBrush);
}
static inline HRESULT IXpsOMObjectFactory1_CreateCoreProperties(IXpsOMObjectFactory1* This,IOpcPartUri *partUri,IXpsOMCoreProperties **coreProperties) {
    return This->lpVtbl->CreateCoreProperties(This,partUri,coreProperties);
}
static inline HRESULT IXpsOMObjectFactory1_CreateDictionary(IXpsOMObjectFactory1* This,IXpsOMDictionary **dictionary) {
    return This->lpVtbl->CreateDictionary(This,dictionary);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePartUriCollection(IXpsOMObjectFactory1* This,IXpsOMPartUriCollection **partUriCollection) {
    return This->lpVtbl->CreatePartUriCollection(This,partUriCollection);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePackageWriterOnFile(IXpsOMObjectFactory1* This,LPCWSTR fileName,LPSECURITY_ATTRIBUTES securityAttributes,DWORD flagsAndAttributes,WINBOOL optimizeMarkupSize,XPS_INTERLEAVING interleaving,IOpcPartUri *documentSequencePartName,IXpsOMCoreProperties *coreProperties,IXpsOMImageResource *packageThumbnail,IXpsOMPrintTicketResource *documentSequencePrintTicket,IOpcPartUri *discardControlPartName,IXpsOMPackageWriter **packageWriter) {
    return This->lpVtbl->CreatePackageWriterOnFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePackageWriterOnStream(IXpsOMObjectFactory1* This,ISequentialStream *outputStream,WINBOOL optimizeMarkupSize,XPS_INTERLEAVING interleaving,IOpcPartUri *documentSequencePartName,IXpsOMCoreProperties *coreProperties,IXpsOMImageResource *packageThumbnail,IXpsOMPrintTicketResource *documentSequencePrintTicket,IOpcPartUri *discardControlPartName,IXpsOMPackageWriter **packageWriter) {
    return This->lpVtbl->CreatePackageWriterOnStream(This,outputStream,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,packageWriter);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePartUri(IXpsOMObjectFactory1* This,LPCWSTR uri,IOpcPartUri **partUri) {
    return This->lpVtbl->CreatePartUri(This,uri,partUri);
}
static inline HRESULT IXpsOMObjectFactory1_CreateReadOnlyStreamOnFile(IXpsOMObjectFactory1* This,LPCWSTR filename,IStream **stream) {
    return This->lpVtbl->CreateReadOnlyStreamOnFile(This,filename,stream);
}
/*** IXpsOMObjectFactory1 methods ***/
static inline HRESULT IXpsOMObjectFactory1_GetDocumentTypeFromFile(IXpsOMObjectFactory1* This,LPCWSTR filename,XPS_DOCUMENT_TYPE *documentType) {
    return This->lpVtbl->GetDocumentTypeFromFile(This,filename,documentType);
}
static inline HRESULT IXpsOMObjectFactory1_GetDocumentTypeFromStream(IXpsOMObjectFactory1* This,IStream *xpsDocumentStream,XPS_DOCUMENT_TYPE *documentType) {
    return This->lpVtbl->GetDocumentTypeFromStream(This,xpsDocumentStream,documentType);
}
static inline HRESULT IXpsOMObjectFactory1_ConvertHDPhotoToJpegXR(IXpsOMObjectFactory1* This,IXpsOMImageResource *imageResource) {
    return This->lpVtbl->ConvertHDPhotoToJpegXR(This,imageResource);
}
static inline HRESULT IXpsOMObjectFactory1_ConvertJpegXRToHDPhoto(IXpsOMObjectFactory1* This,IXpsOMImageResource *imageResource) {
    return This->lpVtbl->ConvertJpegXRToHDPhoto(This,imageResource);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePackageWriterOnFile1(IXpsOMObjectFactory1* This,LPCWSTR fileName,LPSECURITY_ATTRIBUTES securityAttributes,DWORD flagsAndAttributes,WINBOOL optimizeMarkupSize,XPS_INTERLEAVING interleaving,IOpcPartUri *documentSequencePartName,IXpsOMCoreProperties *coreProperties,IXpsOMImageResource *packageThumbnail,IXpsOMPrintTicketResource *documentSequencePrintTicket,IOpcPartUri *discardControlPartName,XPS_DOCUMENT_TYPE documentType,IXpsOMPackageWriter **packageWriter) {
    return This->lpVtbl->CreatePackageWriterOnFile1(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,documentType,packageWriter);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePackageWriterOnStream1(IXpsOMObjectFactory1* This,ISequentialStream *outputStream,WINBOOL optimizeMarkupSize,XPS_INTERLEAVING interleaving,IOpcPartUri *documentSequencePartName,IXpsOMCoreProperties *coreProperties,IXpsOMImageResource *packageThumbnail,IXpsOMPrintTicketResource *documentSequencePrintTicket,IOpcPartUri *discardControlPartName,XPS_DOCUMENT_TYPE documentType,IXpsOMPackageWriter **packageWriter) {
    return This->lpVtbl->CreatePackageWriterOnStream1(This,outputStream,optimizeMarkupSize,interleaving,documentSequencePartName,coreProperties,packageThumbnail,documentSequencePrintTicket,discardControlPartName,documentType,packageWriter);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePackage1(IXpsOMObjectFactory1* This,IXpsOMPackage1 **package) {
    return This->lpVtbl->CreatePackage1(This,package);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePackageFromStream1(IXpsOMObjectFactory1* This,IStream *stream,WINBOOL reuseObjects,IXpsOMPackage1 **package) {
    return This->lpVtbl->CreatePackageFromStream1(This,stream,reuseObjects,package);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePackageFromFile1(IXpsOMObjectFactory1* This,LPCWSTR filename,WINBOOL reuseObjects,IXpsOMPackage1 **package) {
    return This->lpVtbl->CreatePackageFromFile1(This,filename,reuseObjects,package);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePage1(IXpsOMObjectFactory1* This,const XPS_SIZE *pageDimensions,LPCWSTR language,IOpcPartUri *partUri,IXpsOMPage1 **page) {
    return This->lpVtbl->CreatePage1(This,pageDimensions,language,partUri,page);
}
static inline HRESULT IXpsOMObjectFactory1_CreatePageFromStream1(IXpsOMObjectFactory1* This,IStream *pageMarkupStream,IOpcPartUri *partUri,IXpsOMPartResources *resources,WINBOOL reuseObjects,IXpsOMPage1 **page) {
    return This->lpVtbl->CreatePageFromStream1(This,pageMarkupStream,partUri,resources,reuseObjects,page);
}
static inline HRESULT IXpsOMObjectFactory1_CreateRemoteDictionaryResourceFromStream1(IXpsOMObjectFactory1* This,IStream *dictionaryMarkupStream,IOpcPartUri *partUri,IXpsOMPartResources *resources,IXpsOMRemoteDictionaryResource **dictionaryResource) {
    return This->lpVtbl->CreateRemoteDictionaryResourceFromStream1(This,dictionaryMarkupStream,partUri,resources,dictionaryResource);
}
#endif
#endif

#endif


#endif  /* __IXpsOMObjectFactory1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPackage1 interface
 */
#ifndef __IXpsOMPackage1_INTERFACE_DEFINED__
#define __IXpsOMPackage1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPackage1, 0x95a9435e, 0x12bb, 0x461b, 0x8e,0x7f, 0xc6,0xad,0xb0,0x4c,0xd9,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("95a9435e-12bb-461b-8e7f-c6adb04cd96a")
IXpsOMPackage1 : public IXpsOMPackage
{
    virtual HRESULT STDMETHODCALLTYPE GetDocumentType(
        XPS_DOCUMENT_TYPE *documentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteToFile1(
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes,
        WINBOOL optimizeMarkupSize,
        XPS_DOCUMENT_TYPE documentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteToStream1(
        ISequentialStream *outputStream,
        WINBOOL optimizeMarkupSize,
        XPS_DOCUMENT_TYPE documentType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPackage1, 0x95a9435e, 0x12bb, 0x461b, 0x8e,0x7f, 0xc6,0xad,0xb0,0x4c,0xd9,0x6a)
#endif
#else
typedef struct IXpsOMPackage1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPackage1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPackage1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPackage1 *This);

    /*** IXpsOMPackage methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentSequence)(
        IXpsOMPackage1 *This,
        IXpsOMDocumentSequence **documentSequence);

    HRESULT (STDMETHODCALLTYPE *SetDocumentSequence)(
        IXpsOMPackage1 *This,
        IXpsOMDocumentSequence *documentSequence);

    HRESULT (STDMETHODCALLTYPE *GetCoreProperties)(
        IXpsOMPackage1 *This,
        IXpsOMCoreProperties **coreProperties);

    HRESULT (STDMETHODCALLTYPE *SetCoreProperties)(
        IXpsOMPackage1 *This,
        IXpsOMCoreProperties *coreProperties);

    HRESULT (STDMETHODCALLTYPE *GetDiscardControlPartName)(
        IXpsOMPackage1 *This,
        IOpcPartUri **discardControlPartUri);

    HRESULT (STDMETHODCALLTYPE *SetDiscardControlPartName)(
        IXpsOMPackage1 *This,
        IOpcPartUri *discardControlPartUri);

    HRESULT (STDMETHODCALLTYPE *GetThumbnailResource)(
        IXpsOMPackage1 *This,
        IXpsOMImageResource **imageResource);

    HRESULT (STDMETHODCALLTYPE *SetThumbnailResource)(
        IXpsOMPackage1 *This,
        IXpsOMImageResource *imageResource);

    HRESULT (STDMETHODCALLTYPE *WriteToFile)(
        IXpsOMPackage1 *This,
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes,
        WINBOOL optimizeMarkupSize);

    HRESULT (STDMETHODCALLTYPE *WriteToStream)(
        IXpsOMPackage1 *This,
        ISequentialStream *stream,
        WINBOOL optimizeMarkupSize);

    /*** IXpsOMPackage1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentType)(
        IXpsOMPackage1 *This,
        XPS_DOCUMENT_TYPE *documentType);

    HRESULT (STDMETHODCALLTYPE *WriteToFile1)(
        IXpsOMPackage1 *This,
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes,
        WINBOOL optimizeMarkupSize,
        XPS_DOCUMENT_TYPE documentType);

    HRESULT (STDMETHODCALLTYPE *WriteToStream1)(
        IXpsOMPackage1 *This,
        ISequentialStream *outputStream,
        WINBOOL optimizeMarkupSize,
        XPS_DOCUMENT_TYPE documentType);

    END_INTERFACE
} IXpsOMPackage1Vtbl;

interface IXpsOMPackage1 {
    CONST_VTBL IXpsOMPackage1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPackage1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPackage1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPackage1_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPackage methods ***/
#define IXpsOMPackage1_GetDocumentSequence(This,documentSequence) (This)->lpVtbl->GetDocumentSequence(This,documentSequence)
#define IXpsOMPackage1_SetDocumentSequence(This,documentSequence) (This)->lpVtbl->SetDocumentSequence(This,documentSequence)
#define IXpsOMPackage1_GetCoreProperties(This,coreProperties) (This)->lpVtbl->GetCoreProperties(This,coreProperties)
#define IXpsOMPackage1_SetCoreProperties(This,coreProperties) (This)->lpVtbl->SetCoreProperties(This,coreProperties)
#define IXpsOMPackage1_GetDiscardControlPartName(This,discardControlPartUri) (This)->lpVtbl->GetDiscardControlPartName(This,discardControlPartUri)
#define IXpsOMPackage1_SetDiscardControlPartName(This,discardControlPartUri) (This)->lpVtbl->SetDiscardControlPartName(This,discardControlPartUri)
#define IXpsOMPackage1_GetThumbnailResource(This,imageResource) (This)->lpVtbl->GetThumbnailResource(This,imageResource)
#define IXpsOMPackage1_SetThumbnailResource(This,imageResource) (This)->lpVtbl->SetThumbnailResource(This,imageResource)
#define IXpsOMPackage1_WriteToFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize) (This)->lpVtbl->WriteToFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize)
#define IXpsOMPackage1_WriteToStream(This,stream,optimizeMarkupSize) (This)->lpVtbl->WriteToStream(This,stream,optimizeMarkupSize)
/*** IXpsOMPackage1 methods ***/
#define IXpsOMPackage1_GetDocumentType(This,documentType) (This)->lpVtbl->GetDocumentType(This,documentType)
#define IXpsOMPackage1_WriteToFile1(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,documentType) (This)->lpVtbl->WriteToFile1(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,documentType)
#define IXpsOMPackage1_WriteToStream1(This,outputStream,optimizeMarkupSize,documentType) (This)->lpVtbl->WriteToStream1(This,outputStream,optimizeMarkupSize,documentType)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPackage1_QueryInterface(IXpsOMPackage1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPackage1_AddRef(IXpsOMPackage1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPackage1_Release(IXpsOMPackage1* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPackage methods ***/
static inline HRESULT IXpsOMPackage1_GetDocumentSequence(IXpsOMPackage1* This,IXpsOMDocumentSequence **documentSequence) {
    return This->lpVtbl->GetDocumentSequence(This,documentSequence);
}
static inline HRESULT IXpsOMPackage1_SetDocumentSequence(IXpsOMPackage1* This,IXpsOMDocumentSequence *documentSequence) {
    return This->lpVtbl->SetDocumentSequence(This,documentSequence);
}
static inline HRESULT IXpsOMPackage1_GetCoreProperties(IXpsOMPackage1* This,IXpsOMCoreProperties **coreProperties) {
    return This->lpVtbl->GetCoreProperties(This,coreProperties);
}
static inline HRESULT IXpsOMPackage1_SetCoreProperties(IXpsOMPackage1* This,IXpsOMCoreProperties *coreProperties) {
    return This->lpVtbl->SetCoreProperties(This,coreProperties);
}
static inline HRESULT IXpsOMPackage1_GetDiscardControlPartName(IXpsOMPackage1* This,IOpcPartUri **discardControlPartUri) {
    return This->lpVtbl->GetDiscardControlPartName(This,discardControlPartUri);
}
static inline HRESULT IXpsOMPackage1_SetDiscardControlPartName(IXpsOMPackage1* This,IOpcPartUri *discardControlPartUri) {
    return This->lpVtbl->SetDiscardControlPartName(This,discardControlPartUri);
}
static inline HRESULT IXpsOMPackage1_GetThumbnailResource(IXpsOMPackage1* This,IXpsOMImageResource **imageResource) {
    return This->lpVtbl->GetThumbnailResource(This,imageResource);
}
static inline HRESULT IXpsOMPackage1_SetThumbnailResource(IXpsOMPackage1* This,IXpsOMImageResource *imageResource) {
    return This->lpVtbl->SetThumbnailResource(This,imageResource);
}
static inline HRESULT IXpsOMPackage1_WriteToFile(IXpsOMPackage1* This,LPCWSTR fileName,LPSECURITY_ATTRIBUTES securityAttributes,DWORD flagsAndAttributes,WINBOOL optimizeMarkupSize) {
    return This->lpVtbl->WriteToFile(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize);
}
static inline HRESULT IXpsOMPackage1_WriteToStream(IXpsOMPackage1* This,ISequentialStream *stream,WINBOOL optimizeMarkupSize) {
    return This->lpVtbl->WriteToStream(This,stream,optimizeMarkupSize);
}
/*** IXpsOMPackage1 methods ***/
static inline HRESULT IXpsOMPackage1_GetDocumentType(IXpsOMPackage1* This,XPS_DOCUMENT_TYPE *documentType) {
    return This->lpVtbl->GetDocumentType(This,documentType);
}
static inline HRESULT IXpsOMPackage1_WriteToFile1(IXpsOMPackage1* This,LPCWSTR fileName,LPSECURITY_ATTRIBUTES securityAttributes,DWORD flagsAndAttributes,WINBOOL optimizeMarkupSize,XPS_DOCUMENT_TYPE documentType) {
    return This->lpVtbl->WriteToFile1(This,fileName,securityAttributes,flagsAndAttributes,optimizeMarkupSize,documentType);
}
static inline HRESULT IXpsOMPackage1_WriteToStream1(IXpsOMPackage1* This,ISequentialStream *outputStream,WINBOOL optimizeMarkupSize,XPS_DOCUMENT_TYPE documentType) {
    return This->lpVtbl->WriteToStream1(This,outputStream,optimizeMarkupSize,documentType);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPackage1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMPage1 interface
 */
#ifndef __IXpsOMPage1_INTERFACE_DEFINED__
#define __IXpsOMPage1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMPage1, 0x305b60ef, 0x6892, 0x4dda, 0x9c,0xbb, 0x3a,0xa6,0x59,0x74,0x50,0x8a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("305b60ef-6892-4dda-9cbb-3aa65974508a")
IXpsOMPage1 : public IXpsOMPage
{
    virtual HRESULT STDMETHODCALLTYPE GetDocumentType(
        XPS_DOCUMENT_TYPE *documentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE Write1(
        ISequentialStream *stream,
        WINBOOL optimizeMarkupSize,
        XPS_DOCUMENT_TYPE documentType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMPage1, 0x305b60ef, 0x6892, 0x4dda, 0x9c,0xbb, 0x3a,0xa6,0x59,0x74,0x50,0x8a)
#endif
#else
typedef struct IXpsOMPage1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMPage1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMPage1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMPage1 *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMPage1 *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMPage1 *This,
        IOpcPartUri *partUri);

    /*** IXpsOMPage methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOwner)(
        IXpsOMPage1 *This,
        IXpsOMPageReference **pageReference);

    HRESULT (STDMETHODCALLTYPE *GetVisuals)(
        IXpsOMPage1 *This,
        IXpsOMVisualCollection **visuals);

    HRESULT (STDMETHODCALLTYPE *GetPageDimensions)(
        IXpsOMPage1 *This,
        XPS_SIZE *pageDimensions);

    HRESULT (STDMETHODCALLTYPE *SetPageDimensions)(
        IXpsOMPage1 *This,
        const XPS_SIZE *pageDimensions);

    HRESULT (STDMETHODCALLTYPE *GetContentBox)(
        IXpsOMPage1 *This,
        XPS_RECT *contentBox);

    HRESULT (STDMETHODCALLTYPE *SetContentBox)(
        IXpsOMPage1 *This,
        const XPS_RECT *contentBox);

    HRESULT (STDMETHODCALLTYPE *GetBleedBox)(
        IXpsOMPage1 *This,
        XPS_RECT *bleedBox);

    HRESULT (STDMETHODCALLTYPE *SetBleedBox)(
        IXpsOMPage1 *This,
        const XPS_RECT *bleedBox);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IXpsOMPage1 *This,
        LPWSTR *language);

    HRESULT (STDMETHODCALLTYPE *SetLanguage)(
        IXpsOMPage1 *This,
        LPCWSTR language);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IXpsOMPage1 *This,
        LPWSTR *name);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        IXpsOMPage1 *This,
        LPCWSTR name);

    HRESULT (STDMETHODCALLTYPE *GetIsHyperlinkTarget)(
        IXpsOMPage1 *This,
        WINBOOL *isHyperlinkTarget);

    HRESULT (STDMETHODCALLTYPE *SetIsHyperlinkTarget)(
        IXpsOMPage1 *This,
        WINBOOL isHyperlinkTarget);

    HRESULT (STDMETHODCALLTYPE *GetDictionary)(
        IXpsOMPage1 *This,
        IXpsOMDictionary **resourceDictionary);

    HRESULT (STDMETHODCALLTYPE *GetDictionaryLocal)(
        IXpsOMPage1 *This,
        IXpsOMDictionary **resourceDictionary);

    HRESULT (STDMETHODCALLTYPE *SetDictionaryLocal)(
        IXpsOMPage1 *This,
        IXpsOMDictionary *resourceDictionary);

    HRESULT (STDMETHODCALLTYPE *GetDictionaryResource)(
        IXpsOMPage1 *This,
        IXpsOMRemoteDictionaryResource **remoteDictionaryResource);

    HRESULT (STDMETHODCALLTYPE *SetDictionaryResource)(
        IXpsOMPage1 *This,
        IXpsOMRemoteDictionaryResource *remoteDictionaryResource);

    HRESULT (STDMETHODCALLTYPE *Write)(
        IXpsOMPage1 *This,
        ISequentialStream *stream,
        WINBOOL optimizeMarkupSize);

    HRESULT (STDMETHODCALLTYPE *GenerateUnusedLookupKey)(
        IXpsOMPage1 *This,
        XPS_OBJECT_TYPE type,
        LPWSTR *key);

    HRESULT (STDMETHODCALLTYPE *Clone)(
        IXpsOMPage1 *This,
        IXpsOMPage **page);

    /*** IXpsOMPage1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentType)(
        IXpsOMPage1 *This,
        XPS_DOCUMENT_TYPE *documentType);

    HRESULT (STDMETHODCALLTYPE *Write1)(
        IXpsOMPage1 *This,
        ISequentialStream *stream,
        WINBOOL optimizeMarkupSize,
        XPS_DOCUMENT_TYPE documentType);

    END_INTERFACE
} IXpsOMPage1Vtbl;

interface IXpsOMPage1 {
    CONST_VTBL IXpsOMPage1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMPage1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMPage1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMPage1_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMPage1_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMPage1_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMPage methods ***/
#define IXpsOMPage1_GetOwner(This,pageReference) (This)->lpVtbl->GetOwner(This,pageReference)
#define IXpsOMPage1_GetVisuals(This,visuals) (This)->lpVtbl->GetVisuals(This,visuals)
#define IXpsOMPage1_GetPageDimensions(This,pageDimensions) (This)->lpVtbl->GetPageDimensions(This,pageDimensions)
#define IXpsOMPage1_SetPageDimensions(This,pageDimensions) (This)->lpVtbl->SetPageDimensions(This,pageDimensions)
#define IXpsOMPage1_GetContentBox(This,contentBox) (This)->lpVtbl->GetContentBox(This,contentBox)
#define IXpsOMPage1_SetContentBox(This,contentBox) (This)->lpVtbl->SetContentBox(This,contentBox)
#define IXpsOMPage1_GetBleedBox(This,bleedBox) (This)->lpVtbl->GetBleedBox(This,bleedBox)
#define IXpsOMPage1_SetBleedBox(This,bleedBox) (This)->lpVtbl->SetBleedBox(This,bleedBox)
#define IXpsOMPage1_GetLanguage(This,language) (This)->lpVtbl->GetLanguage(This,language)
#define IXpsOMPage1_SetLanguage(This,language) (This)->lpVtbl->SetLanguage(This,language)
#define IXpsOMPage1_GetName(This,name) (This)->lpVtbl->GetName(This,name)
#define IXpsOMPage1_SetName(This,name) (This)->lpVtbl->SetName(This,name)
#define IXpsOMPage1_GetIsHyperlinkTarget(This,isHyperlinkTarget) (This)->lpVtbl->GetIsHyperlinkTarget(This,isHyperlinkTarget)
#define IXpsOMPage1_SetIsHyperlinkTarget(This,isHyperlinkTarget) (This)->lpVtbl->SetIsHyperlinkTarget(This,isHyperlinkTarget)
#define IXpsOMPage1_GetDictionary(This,resourceDictionary) (This)->lpVtbl->GetDictionary(This,resourceDictionary)
#define IXpsOMPage1_GetDictionaryLocal(This,resourceDictionary) (This)->lpVtbl->GetDictionaryLocal(This,resourceDictionary)
#define IXpsOMPage1_SetDictionaryLocal(This,resourceDictionary) (This)->lpVtbl->SetDictionaryLocal(This,resourceDictionary)
#define IXpsOMPage1_GetDictionaryResource(This,remoteDictionaryResource) (This)->lpVtbl->GetDictionaryResource(This,remoteDictionaryResource)
#define IXpsOMPage1_SetDictionaryResource(This,remoteDictionaryResource) (This)->lpVtbl->SetDictionaryResource(This,remoteDictionaryResource)
#define IXpsOMPage1_Write(This,stream,optimizeMarkupSize) (This)->lpVtbl->Write(This,stream,optimizeMarkupSize)
#define IXpsOMPage1_GenerateUnusedLookupKey(This,type,key) (This)->lpVtbl->GenerateUnusedLookupKey(This,type,key)
#define IXpsOMPage1_Clone(This,page) (This)->lpVtbl->Clone(This,page)
/*** IXpsOMPage1 methods ***/
#define IXpsOMPage1_GetDocumentType(This,documentType) (This)->lpVtbl->GetDocumentType(This,documentType)
#define IXpsOMPage1_Write1(This,stream,optimizeMarkupSize,documentType) (This)->lpVtbl->Write1(This,stream,optimizeMarkupSize,documentType)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMPage1_QueryInterface(IXpsOMPage1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMPage1_AddRef(IXpsOMPage1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMPage1_Release(IXpsOMPage1* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMPage1_GetPartName(IXpsOMPage1* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMPage1_SetPartName(IXpsOMPage1* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMPage methods ***/
static inline HRESULT IXpsOMPage1_GetOwner(IXpsOMPage1* This,IXpsOMPageReference **pageReference) {
    return This->lpVtbl->GetOwner(This,pageReference);
}
static inline HRESULT IXpsOMPage1_GetVisuals(IXpsOMPage1* This,IXpsOMVisualCollection **visuals) {
    return This->lpVtbl->GetVisuals(This,visuals);
}
static inline HRESULT IXpsOMPage1_GetPageDimensions(IXpsOMPage1* This,XPS_SIZE *pageDimensions) {
    return This->lpVtbl->GetPageDimensions(This,pageDimensions);
}
static inline HRESULT IXpsOMPage1_SetPageDimensions(IXpsOMPage1* This,const XPS_SIZE *pageDimensions) {
    return This->lpVtbl->SetPageDimensions(This,pageDimensions);
}
static inline HRESULT IXpsOMPage1_GetContentBox(IXpsOMPage1* This,XPS_RECT *contentBox) {
    return This->lpVtbl->GetContentBox(This,contentBox);
}
static inline HRESULT IXpsOMPage1_SetContentBox(IXpsOMPage1* This,const XPS_RECT *contentBox) {
    return This->lpVtbl->SetContentBox(This,contentBox);
}
static inline HRESULT IXpsOMPage1_GetBleedBox(IXpsOMPage1* This,XPS_RECT *bleedBox) {
    return This->lpVtbl->GetBleedBox(This,bleedBox);
}
static inline HRESULT IXpsOMPage1_SetBleedBox(IXpsOMPage1* This,const XPS_RECT *bleedBox) {
    return This->lpVtbl->SetBleedBox(This,bleedBox);
}
static inline HRESULT IXpsOMPage1_GetLanguage(IXpsOMPage1* This,LPWSTR *language) {
    return This->lpVtbl->GetLanguage(This,language);
}
static inline HRESULT IXpsOMPage1_SetLanguage(IXpsOMPage1* This,LPCWSTR language) {
    return This->lpVtbl->SetLanguage(This,language);
}
static inline HRESULT IXpsOMPage1_GetName(IXpsOMPage1* This,LPWSTR *name) {
    return This->lpVtbl->GetName(This,name);
}
static inline HRESULT IXpsOMPage1_SetName(IXpsOMPage1* This,LPCWSTR name) {
    return This->lpVtbl->SetName(This,name);
}
static inline HRESULT IXpsOMPage1_GetIsHyperlinkTarget(IXpsOMPage1* This,WINBOOL *isHyperlinkTarget) {
    return This->lpVtbl->GetIsHyperlinkTarget(This,isHyperlinkTarget);
}
static inline HRESULT IXpsOMPage1_SetIsHyperlinkTarget(IXpsOMPage1* This,WINBOOL isHyperlinkTarget) {
    return This->lpVtbl->SetIsHyperlinkTarget(This,isHyperlinkTarget);
}
static inline HRESULT IXpsOMPage1_GetDictionary(IXpsOMPage1* This,IXpsOMDictionary **resourceDictionary) {
    return This->lpVtbl->GetDictionary(This,resourceDictionary);
}
static inline HRESULT IXpsOMPage1_GetDictionaryLocal(IXpsOMPage1* This,IXpsOMDictionary **resourceDictionary) {
    return This->lpVtbl->GetDictionaryLocal(This,resourceDictionary);
}
static inline HRESULT IXpsOMPage1_SetDictionaryLocal(IXpsOMPage1* This,IXpsOMDictionary *resourceDictionary) {
    return This->lpVtbl->SetDictionaryLocal(This,resourceDictionary);
}
static inline HRESULT IXpsOMPage1_GetDictionaryResource(IXpsOMPage1* This,IXpsOMRemoteDictionaryResource **remoteDictionaryResource) {
    return This->lpVtbl->GetDictionaryResource(This,remoteDictionaryResource);
}
static inline HRESULT IXpsOMPage1_SetDictionaryResource(IXpsOMPage1* This,IXpsOMRemoteDictionaryResource *remoteDictionaryResource) {
    return This->lpVtbl->SetDictionaryResource(This,remoteDictionaryResource);
}
static inline HRESULT IXpsOMPage1_Write(IXpsOMPage1* This,ISequentialStream *stream,WINBOOL optimizeMarkupSize) {
    return This->lpVtbl->Write(This,stream,optimizeMarkupSize);
}
static inline HRESULT IXpsOMPage1_GenerateUnusedLookupKey(IXpsOMPage1* This,XPS_OBJECT_TYPE type,LPWSTR *key) {
    return This->lpVtbl->GenerateUnusedLookupKey(This,type,key);
}
static inline HRESULT IXpsOMPage1_Clone(IXpsOMPage1* This,IXpsOMPage **page) {
    return This->lpVtbl->Clone(This,page);
}
/*** IXpsOMPage1 methods ***/
static inline HRESULT IXpsOMPage1_GetDocumentType(IXpsOMPage1* This,XPS_DOCUMENT_TYPE *documentType) {
    return This->lpVtbl->GetDocumentType(This,documentType);
}
static inline HRESULT IXpsOMPage1_Write1(IXpsOMPage1* This,ISequentialStream *stream,WINBOOL optimizeMarkupSize,XPS_DOCUMENT_TYPE documentType) {
    return This->lpVtbl->Write1(This,stream,optimizeMarkupSize,documentType);
}
#endif
#endif

#endif


#endif  /* __IXpsOMPage1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsDocumentPackageTarget interface
 */
#ifndef __IXpsDocumentPackageTarget_INTERFACE_DEFINED__
#define __IXpsDocumentPackageTarget_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsDocumentPackageTarget, 0x3b0b6d38, 0x53ad, 0x41da, 0xb2,0x12, 0xd3,0x76,0x37,0xa6,0x71,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3b0b6d38-53ad-41da-b212-d37637a6714e")
IXpsDocumentPackageTarget : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetXpsOMPackageWriter(
        IOpcPartUri *documentSequencePartName,
        IOpcPartUri *discardControlPartName,
        IXpsOMPackageWriter **packageWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetXpsOMFactory(
        IXpsOMObjectFactory **xpsFactory) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetXpsType(
        XPS_DOCUMENT_TYPE *documentType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsDocumentPackageTarget, 0x3b0b6d38, 0x53ad, 0x41da, 0xb2,0x12, 0xd3,0x76,0x37,0xa6,0x71,0x4e)
#endif
#else
typedef struct IXpsDocumentPackageTargetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsDocumentPackageTarget *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsDocumentPackageTarget *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsDocumentPackageTarget *This);

    /*** IXpsDocumentPackageTarget methods ***/
    HRESULT (STDMETHODCALLTYPE *GetXpsOMPackageWriter)(
        IXpsDocumentPackageTarget *This,
        IOpcPartUri *documentSequencePartName,
        IOpcPartUri *discardControlPartName,
        IXpsOMPackageWriter **packageWriter);

    HRESULT (STDMETHODCALLTYPE *GetXpsOMFactory)(
        IXpsDocumentPackageTarget *This,
        IXpsOMObjectFactory **xpsFactory);

    HRESULT (STDMETHODCALLTYPE *GetXpsType)(
        IXpsDocumentPackageTarget *This,
        XPS_DOCUMENT_TYPE *documentType);

    END_INTERFACE
} IXpsDocumentPackageTargetVtbl;

interface IXpsDocumentPackageTarget {
    CONST_VTBL IXpsDocumentPackageTargetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsDocumentPackageTarget_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsDocumentPackageTarget_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsDocumentPackageTarget_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsDocumentPackageTarget methods ***/
#define IXpsDocumentPackageTarget_GetXpsOMPackageWriter(This,documentSequencePartName,discardControlPartName,packageWriter) (This)->lpVtbl->GetXpsOMPackageWriter(This,documentSequencePartName,discardControlPartName,packageWriter)
#define IXpsDocumentPackageTarget_GetXpsOMFactory(This,xpsFactory) (This)->lpVtbl->GetXpsOMFactory(This,xpsFactory)
#define IXpsDocumentPackageTarget_GetXpsType(This,documentType) (This)->lpVtbl->GetXpsType(This,documentType)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsDocumentPackageTarget_QueryInterface(IXpsDocumentPackageTarget* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsDocumentPackageTarget_AddRef(IXpsDocumentPackageTarget* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsDocumentPackageTarget_Release(IXpsDocumentPackageTarget* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsDocumentPackageTarget methods ***/
static inline HRESULT IXpsDocumentPackageTarget_GetXpsOMPackageWriter(IXpsDocumentPackageTarget* This,IOpcPartUri *documentSequencePartName,IOpcPartUri *discardControlPartName,IXpsOMPackageWriter **packageWriter) {
    return This->lpVtbl->GetXpsOMPackageWriter(This,documentSequencePartName,discardControlPartName,packageWriter);
}
static inline HRESULT IXpsDocumentPackageTarget_GetXpsOMFactory(IXpsDocumentPackageTarget* This,IXpsOMObjectFactory **xpsFactory) {
    return This->lpVtbl->GetXpsOMFactory(This,xpsFactory);
}
static inline HRESULT IXpsDocumentPackageTarget_GetXpsType(IXpsDocumentPackageTarget* This,XPS_DOCUMENT_TYPE *documentType) {
    return This->lpVtbl->GetXpsType(This,documentType);
}
#endif
#endif

#endif


#endif  /* __IXpsDocumentPackageTarget_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsOMRemoteDictionaryResource1 interface
 */
#ifndef __IXpsOMRemoteDictionaryResource1_INTERFACE_DEFINED__
#define __IXpsOMRemoteDictionaryResource1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsOMRemoteDictionaryResource1, 0xbf8fc1d4, 0x9d46, 0x4141, 0xba,0x5f, 0x94,0xbb,0x92,0x50,0xd0,0x41);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bf8fc1d4-9d46-4141-ba5f-94bb9250d041")
IXpsOMRemoteDictionaryResource1 : public IXpsOMRemoteDictionaryResource
{
    virtual HRESULT STDMETHODCALLTYPE GetDocumentType(
        XPS_DOCUMENT_TYPE *documentType) = 0;

    virtual HRESULT STDMETHODCALLTYPE Write1(
        ISequentialStream *stream,
        XPS_DOCUMENT_TYPE documentType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsOMRemoteDictionaryResource1, 0xbf8fc1d4, 0x9d46, 0x4141, 0xba,0x5f, 0x94,0xbb,0x92,0x50,0xd0,0x41)
#endif
#else
typedef struct IXpsOMRemoteDictionaryResource1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsOMRemoteDictionaryResource1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsOMRemoteDictionaryResource1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsOMRemoteDictionaryResource1 *This);

    /*** IXpsOMPart methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsOMRemoteDictionaryResource1 *This,
        IOpcPartUri **partUri);

    HRESULT (STDMETHODCALLTYPE *SetPartName)(
        IXpsOMRemoteDictionaryResource1 *This,
        IOpcPartUri *partUri);

    /*** IXpsOMRemoteDictionaryResource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDictionary)(
        IXpsOMRemoteDictionaryResource1 *This,
        IXpsOMDictionary **dictionary);

    HRESULT (STDMETHODCALLTYPE *SetDictionary)(
        IXpsOMRemoteDictionaryResource1 *This,
        IXpsOMDictionary *dictionary);

    /*** IXpsOMRemoteDictionaryResource1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDocumentType)(
        IXpsOMRemoteDictionaryResource1 *This,
        XPS_DOCUMENT_TYPE *documentType);

    HRESULT (STDMETHODCALLTYPE *Write1)(
        IXpsOMRemoteDictionaryResource1 *This,
        ISequentialStream *stream,
        XPS_DOCUMENT_TYPE documentType);

    END_INTERFACE
} IXpsOMRemoteDictionaryResource1Vtbl;

interface IXpsOMRemoteDictionaryResource1 {
    CONST_VTBL IXpsOMRemoteDictionaryResource1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsOMRemoteDictionaryResource1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsOMRemoteDictionaryResource1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsOMRemoteDictionaryResource1_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsOMPart methods ***/
#define IXpsOMRemoteDictionaryResource1_GetPartName(This,partUri) (This)->lpVtbl->GetPartName(This,partUri)
#define IXpsOMRemoteDictionaryResource1_SetPartName(This,partUri) (This)->lpVtbl->SetPartName(This,partUri)
/*** IXpsOMRemoteDictionaryResource methods ***/
#define IXpsOMRemoteDictionaryResource1_GetDictionary(This,dictionary) (This)->lpVtbl->GetDictionary(This,dictionary)
#define IXpsOMRemoteDictionaryResource1_SetDictionary(This,dictionary) (This)->lpVtbl->SetDictionary(This,dictionary)
/*** IXpsOMRemoteDictionaryResource1 methods ***/
#define IXpsOMRemoteDictionaryResource1_GetDocumentType(This,documentType) (This)->lpVtbl->GetDocumentType(This,documentType)
#define IXpsOMRemoteDictionaryResource1_Write1(This,stream,documentType) (This)->lpVtbl->Write1(This,stream,documentType)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsOMRemoteDictionaryResource1_QueryInterface(IXpsOMRemoteDictionaryResource1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsOMRemoteDictionaryResource1_AddRef(IXpsOMRemoteDictionaryResource1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsOMRemoteDictionaryResource1_Release(IXpsOMRemoteDictionaryResource1* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsOMPart methods ***/
static inline HRESULT IXpsOMRemoteDictionaryResource1_GetPartName(IXpsOMRemoteDictionaryResource1* This,IOpcPartUri **partUri) {
    return This->lpVtbl->GetPartName(This,partUri);
}
static inline HRESULT IXpsOMRemoteDictionaryResource1_SetPartName(IXpsOMRemoteDictionaryResource1* This,IOpcPartUri *partUri) {
    return This->lpVtbl->SetPartName(This,partUri);
}
/*** IXpsOMRemoteDictionaryResource methods ***/
static inline HRESULT IXpsOMRemoteDictionaryResource1_GetDictionary(IXpsOMRemoteDictionaryResource1* This,IXpsOMDictionary **dictionary) {
    return This->lpVtbl->GetDictionary(This,dictionary);
}
static inline HRESULT IXpsOMRemoteDictionaryResource1_SetDictionary(IXpsOMRemoteDictionaryResource1* This,IXpsOMDictionary *dictionary) {
    return This->lpVtbl->SetDictionary(This,dictionary);
}
/*** IXpsOMRemoteDictionaryResource1 methods ***/
static inline HRESULT IXpsOMRemoteDictionaryResource1_GetDocumentType(IXpsOMRemoteDictionaryResource1* This,XPS_DOCUMENT_TYPE *documentType) {
    return This->lpVtbl->GetDocumentType(This,documentType);
}
static inline HRESULT IXpsOMRemoteDictionaryResource1_Write1(IXpsOMRemoteDictionaryResource1* This,ISequentialStream *stream,XPS_DOCUMENT_TYPE documentType) {
    return This->lpVtbl->Write1(This,stream,documentType);
}
#endif
#endif

#endif


#endif  /* __IXpsOMRemoteDictionaryResource1_INTERFACE_DEFINED__ */

#endif
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __xpsobjectmodel_1_h__ */
