/*** Autogenerated by WIDL 10.12 from include/wbemprov.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wbemprov_h__
#define __wbemprov_h__

/* Forward declarations */

#ifndef __WbemAdministrativeLocator_FWD_DEFINED__
#define __WbemAdministrativeLocator_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemAdministrativeLocator WbemAdministrativeLocator;
#else
typedef struct WbemAdministrativeLocator WbemAdministrativeLocator;
#endif /* defined __cplusplus */
#endif /* defined __WbemAdministrativeLocator_FWD_DEFINED__ */

#ifndef __WbemAuthenticatedLocator_FWD_DEFINED__
#define __WbemAuthenticatedLocator_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemAuthenticatedLocator WbemAuthenticatedLocator;
#else
typedef struct WbemAuthenticatedLocator WbemAuthenticatedLocator;
#endif /* defined __cplusplus */
#endif /* defined __WbemAuthenticatedLocator_FWD_DEFINED__ */

#ifndef __WbemUnauthenticatedLocator_FWD_DEFINED__
#define __WbemUnauthenticatedLocator_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemUnauthenticatedLocator WbemUnauthenticatedLocator;
#else
typedef struct WbemUnauthenticatedLocator WbemUnauthenticatedLocator;
#endif /* defined __cplusplus */
#endif /* defined __WbemUnauthenticatedLocator_FWD_DEFINED__ */

#ifndef __WbemDecoupledRegistrar_FWD_DEFINED__
#define __WbemDecoupledRegistrar_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemDecoupledRegistrar WbemDecoupledRegistrar;
#else
typedef struct WbemDecoupledRegistrar WbemDecoupledRegistrar;
#endif /* defined __cplusplus */
#endif /* defined __WbemDecoupledRegistrar_FWD_DEFINED__ */

#ifndef __WbemDecoupledBasicEventProvider_FWD_DEFINED__
#define __WbemDecoupledBasicEventProvider_FWD_DEFINED__
#ifdef __cplusplus
typedef class WbemDecoupledBasicEventProvider WbemDecoupledBasicEventProvider;
#else
typedef struct WbemDecoupledBasicEventProvider WbemDecoupledBasicEventProvider;
#endif /* defined __cplusplus */
#endif /* defined __WbemDecoupledBasicEventProvider_FWD_DEFINED__ */

#ifndef __IWbemUnboundObjectSink_FWD_DEFINED__
#define __IWbemUnboundObjectSink_FWD_DEFINED__
typedef interface IWbemUnboundObjectSink IWbemUnboundObjectSink;
#ifdef __cplusplus
interface IWbemUnboundObjectSink;
#endif /* __cplusplus */
#endif

#ifndef __IWbemPropertyProvider_FWD_DEFINED__
#define __IWbemPropertyProvider_FWD_DEFINED__
typedef interface IWbemPropertyProvider IWbemPropertyProvider;
#ifdef __cplusplus
interface IWbemPropertyProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWbemEventProvider_FWD_DEFINED__
#define __IWbemEventProvider_FWD_DEFINED__
typedef interface IWbemEventProvider IWbemEventProvider;
#ifdef __cplusplus
interface IWbemEventProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWbemEventProviderQuerySink_FWD_DEFINED__
#define __IWbemEventProviderQuerySink_FWD_DEFINED__
typedef interface IWbemEventProviderQuerySink IWbemEventProviderQuerySink;
#ifdef __cplusplus
interface IWbemEventProviderQuerySink;
#endif /* __cplusplus */
#endif

#ifndef __IWbemEventProviderSecurity_FWD_DEFINED__
#define __IWbemEventProviderSecurity_FWD_DEFINED__
typedef interface IWbemEventProviderSecurity IWbemEventProviderSecurity;
#ifdef __cplusplus
interface IWbemEventProviderSecurity;
#endif /* __cplusplus */
#endif

#ifndef __IWbemProviderIdentity_FWD_DEFINED__
#define __IWbemProviderIdentity_FWD_DEFINED__
typedef interface IWbemProviderIdentity IWbemProviderIdentity;
#ifdef __cplusplus
interface IWbemProviderIdentity;
#endif /* __cplusplus */
#endif

#ifndef __IWbemEventConsumerProvider_FWD_DEFINED__
#define __IWbemEventConsumerProvider_FWD_DEFINED__
typedef interface IWbemEventConsumerProvider IWbemEventConsumerProvider;
#ifdef __cplusplus
interface IWbemEventConsumerProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWbemProviderInitSink_FWD_DEFINED__
#define __IWbemProviderInitSink_FWD_DEFINED__
typedef interface IWbemProviderInitSink IWbemProviderInitSink;
#ifdef __cplusplus
interface IWbemProviderInitSink;
#endif /* __cplusplus */
#endif

#ifndef __IWbemProviderInit_FWD_DEFINED__
#define __IWbemProviderInit_FWD_DEFINED__
typedef interface IWbemProviderInit IWbemProviderInit;
#ifdef __cplusplus
interface IWbemProviderInit;
#endif /* __cplusplus */
#endif

#ifndef __IWbemHiPerfProvider_FWD_DEFINED__
#define __IWbemHiPerfProvider_FWD_DEFINED__
typedef interface IWbemHiPerfProvider IWbemHiPerfProvider;
#ifdef __cplusplus
interface IWbemHiPerfProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWbemDecoupledRegistrar_FWD_DEFINED__
#define __IWbemDecoupledRegistrar_FWD_DEFINED__
typedef interface IWbemDecoupledRegistrar IWbemDecoupledRegistrar;
#ifdef __cplusplus
interface IWbemDecoupledRegistrar;
#endif /* __cplusplus */
#endif

#ifndef __IWbemDecoupledBasicEventProvider_FWD_DEFINED__
#define __IWbemDecoupledBasicEventProvider_FWD_DEFINED__
typedef interface IWbemDecoupledBasicEventProvider IWbemDecoupledBasicEventProvider;
#ifdef __cplusplus
interface IWbemDecoupledBasicEventProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWbemEventSink_FWD_DEFINED__
#define __IWbemEventSink_FWD_DEFINED__
typedef interface IWbemEventSink IWbemEventSink;
#ifdef __cplusplus
interface IWbemEventSink;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <objidl.h>
#include <oleidl.h>
#include <oaidl.h>
#include <wbemcli.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER within this package.
 */

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef VARIANT WBEM_VARIANT;
typedef LPWSTR WBEM_WSTR;
typedef LPCWSTR WBEM_CWSTR;
typedef enum tag_WBEM_PROVIDER_REQUIREMENTS_TYPE {
    WBEM_REQUIREMENTS_START_POSTFILTER = 0,
    WBEM_REQUIREMENTS_STOP_POSTFILTER = 1,
    WBEM_REQUIREMENTS_RECHECK_SUBSCRIPTIONS = 2
} WBEM_PROVIDER_REQUIREMENTS_TYPE;
#ifndef __IWbemPropertyProvider_FWD_DEFINED__
#define __IWbemPropertyProvider_FWD_DEFINED__
typedef interface IWbemPropertyProvider IWbemPropertyProvider;
#ifdef __cplusplus
interface IWbemPropertyProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWbemUnboundObjectSink_FWD_DEFINED__
#define __IWbemUnboundObjectSink_FWD_DEFINED__
typedef interface IWbemUnboundObjectSink IWbemUnboundObjectSink;
#ifdef __cplusplus
interface IWbemUnboundObjectSink;
#endif /* __cplusplus */
#endif

#ifndef __IWbemEventProvider_FWD_DEFINED__
#define __IWbemEventProvider_FWD_DEFINED__
typedef interface IWbemEventProvider IWbemEventProvider;
#ifdef __cplusplus
interface IWbemEventProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWbemEventProviderQuerySink_FWD_DEFINED__
#define __IWbemEventProviderQuerySink_FWD_DEFINED__
typedef interface IWbemEventProviderQuerySink IWbemEventProviderQuerySink;
#ifdef __cplusplus
interface IWbemEventProviderQuerySink;
#endif /* __cplusplus */
#endif

#ifndef __IWbemEventProviderSecurity_FWD_DEFINED__
#define __IWbemEventProviderSecurity_FWD_DEFINED__
typedef interface IWbemEventProviderSecurity IWbemEventProviderSecurity;
#ifdef __cplusplus
interface IWbemEventProviderSecurity;
#endif /* __cplusplus */
#endif

#ifndef __IWbemEventConsumerProvider_FWD_DEFINED__
#define __IWbemEventConsumerProvider_FWD_DEFINED__
typedef interface IWbemEventConsumerProvider IWbemEventConsumerProvider;
#ifdef __cplusplus
interface IWbemEventConsumerProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWbemProviderInitSink_FWD_DEFINED__
#define __IWbemProviderInitSink_FWD_DEFINED__
typedef interface IWbemProviderInitSink IWbemProviderInitSink;
#ifdef __cplusplus
interface IWbemProviderInitSink;
#endif /* __cplusplus */
#endif

#ifndef __IWbemProviderInit_FWD_DEFINED__
#define __IWbemProviderInit_FWD_DEFINED__
typedef interface IWbemProviderInit IWbemProviderInit;
#ifdef __cplusplus
interface IWbemProviderInit;
#endif /* __cplusplus */
#endif

#ifndef __IWbemHiPerfProvider_FWD_DEFINED__
#define __IWbemHiPerfProvider_FWD_DEFINED__
typedef interface IWbemHiPerfProvider IWbemHiPerfProvider;
#ifdef __cplusplus
interface IWbemHiPerfProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWbemDecoupledRegistrar_FWD_DEFINED__
#define __IWbemDecoupledRegistrar_FWD_DEFINED__
typedef interface IWbemDecoupledRegistrar IWbemDecoupledRegistrar;
#ifdef __cplusplus
interface IWbemDecoupledRegistrar;
#endif /* __cplusplus */
#endif

#ifndef __WbemProviders_v1_LIBRARY_DEFINED__
#define __WbemProviders_v1_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_WbemProviders_v1, 0x092df710, 0x7010, 0x11d1, 0xad,0x90, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);

/*****************************************************************************
 * WbemAdministrativeLocator coclass
 */

DEFINE_GUID(CLSID_WbemAdministrativeLocator, 0xcb8555cc, 0x9128, 0x11d1, 0xad,0x9b, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);

#ifdef __cplusplus
class DECLSPEC_UUID("cb8555cc-9128-11d1-ad9b-00c04fd8fdff") WbemAdministrativeLocator;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemAdministrativeLocator, 0xcb8555cc, 0x9128, 0x11d1, 0xad,0x9b, 0x00,0xc0,0x4f,0xd8,0xfd,0xff)
#endif
#endif

/*****************************************************************************
 * WbemAuthenticatedLocator coclass
 */

DEFINE_GUID(CLSID_WbemAuthenticatedLocator, 0xcd184336, 0x9128, 0x11d1, 0xad,0x9b, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);

#ifdef __cplusplus
class DECLSPEC_UUID("cd184336-9128-11d1-ad9b-00c04fd8fdff") WbemAuthenticatedLocator;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemAuthenticatedLocator, 0xcd184336, 0x9128, 0x11d1, 0xad,0x9b, 0x00,0xc0,0x4f,0xd8,0xfd,0xff)
#endif
#endif

/*****************************************************************************
 * WbemUnauthenticatedLocator coclass
 */

DEFINE_GUID(CLSID_WbemUnauthenticatedLocator, 0x443e7b79, 0xde31, 0x11d2, 0xb3,0x40, 0x00,0x10,0x4b,0xcc,0x4b,0x4a);

#ifdef __cplusplus
class DECLSPEC_UUID("443e7b79-de31-11d2-b340-00104bcc4b4a") WbemUnauthenticatedLocator;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemUnauthenticatedLocator, 0x443e7b79, 0xde31, 0x11d2, 0xb3,0x40, 0x00,0x10,0x4b,0xcc,0x4b,0x4a)
#endif
#endif

/*****************************************************************************
 * WbemDecoupledRegistrar coclass
 */

DEFINE_GUID(CLSID_WbemDecoupledRegistrar, 0x4cfc7932, 0x0f9d, 0x4bef, 0x9c,0x32, 0x8e,0xa2,0xa6,0xb5,0x6f,0xcb);

#ifdef __cplusplus
class DECLSPEC_UUID("4cfc7932-0f9d-4bef-9c32-8ea2a6b56fcb") WbemDecoupledRegistrar;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemDecoupledRegistrar, 0x4cfc7932, 0x0f9d, 0x4bef, 0x9c,0x32, 0x8e,0xa2,0xa6,0xb5,0x6f,0xcb)
#endif
#endif

/*****************************************************************************
 * WbemDecoupledBasicEventProvider coclass
 */

DEFINE_GUID(CLSID_WbemDecoupledBasicEventProvider, 0xf5f75737, 0x2843, 0x4f22, 0x93,0x3d, 0xc7,0x6a,0x97,0xcd,0xa6,0x2f);

#ifdef __cplusplus
class DECLSPEC_UUID("f5f75737-2843-4f22-933d-c76a97cda62f") WbemDecoupledBasicEventProvider;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WbemDecoupledBasicEventProvider, 0xf5f75737, 0x2843, 0x4f22, 0x93,0x3d, 0xc7,0x6a,0x97,0xcd,0xa6,0x2f)
#endif
#endif

#endif /* __WbemProviders_v1_LIBRARY_DEFINED__ */
typedef enum tag_WBEM_BATCH_TYPE {
    WBEM_FLAG_BATCH_IF_NEEDED = 0,
    WBEM_FLAG_MUST_BATCH = 0x1,
    WBEM_FLAG_MUST_NOT_BATCH = 0x2
} WBEM_BATCH_TYPE;
typedef enum tag_WBEM_PROVIDER_FLAGS {
    WBEM_FLAG_OWNER_UPDATE = 0x10000
} WBEM_PROVIDER_FLAGS;
typedef enum tag_WBEM_EXTRA_RETURN_CODES {
    WBEM_S_INITIALIZED = 0,
    WBEM_S_LIMITED_SERVICE = 0x43001,
    WBEM_S_INDIRECTLY_UPDATED = 0x43002,
    WBEM_S_SUBJECT_TO_SDS = 0x43003,
    WBEM_E_RETRY_LATER = 0x80043001,
    WBEM_E_RESOURCE_CONTENTION = 0x80043002
} WBEM_EXTRA_RETURN_CODES;
/*****************************************************************************
 * IWbemUnboundObjectSink interface
 */
#ifndef __IWbemUnboundObjectSink_INTERFACE_DEFINED__
#define __IWbemUnboundObjectSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemUnboundObjectSink, 0xe246107b, 0xb06e, 0x11d0, 0xad,0x61, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e246107b-b06e-11d0-ad61-00c04fd8fdff")
IWbemUnboundObjectSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IndicateToConsumer(
        IWbemClassObject *pLogicalConsumer,
        LONG lNumObjects,
        IWbemClassObject **apObjects) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemUnboundObjectSink, 0xe246107b, 0xb06e, 0x11d0, 0xad,0x61, 0x00,0xc0,0x4f,0xd8,0xfd,0xff)
#endif
#else
typedef struct IWbemUnboundObjectSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemUnboundObjectSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemUnboundObjectSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemUnboundObjectSink *This);

    /*** IWbemUnboundObjectSink methods ***/
    HRESULT (STDMETHODCALLTYPE *IndicateToConsumer)(
        IWbemUnboundObjectSink *This,
        IWbemClassObject *pLogicalConsumer,
        LONG lNumObjects,
        IWbemClassObject **apObjects);

    END_INTERFACE
} IWbemUnboundObjectSinkVtbl;

interface IWbemUnboundObjectSink {
    CONST_VTBL IWbemUnboundObjectSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemUnboundObjectSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemUnboundObjectSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemUnboundObjectSink_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemUnboundObjectSink methods ***/
#define IWbemUnboundObjectSink_IndicateToConsumer(This,pLogicalConsumer,lNumObjects,apObjects) (This)->lpVtbl->IndicateToConsumer(This,pLogicalConsumer,lNumObjects,apObjects)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemUnboundObjectSink_QueryInterface(IWbemUnboundObjectSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemUnboundObjectSink_AddRef(IWbemUnboundObjectSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemUnboundObjectSink_Release(IWbemUnboundObjectSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemUnboundObjectSink methods ***/
static inline HRESULT IWbemUnboundObjectSink_IndicateToConsumer(IWbemUnboundObjectSink* This,IWbemClassObject *pLogicalConsumer,LONG lNumObjects,IWbemClassObject **apObjects) {
    return This->lpVtbl->IndicateToConsumer(This,pLogicalConsumer,lNumObjects,apObjects);
}
#endif
#endif

#endif


#endif  /* __IWbemUnboundObjectSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemPropertyProvider interface
 */
#ifndef __IWbemPropertyProvider_INTERFACE_DEFINED__
#define __IWbemPropertyProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemPropertyProvider, 0xce61e841, 0x65bc, 0x11d0, 0xb6,0xbd, 0x00,0xaa,0x00,0x32,0x40,0xc7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ce61e841-65bc-11d0-b6bd-00aa003240c7")
IWbemPropertyProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        LONG lFlags,
        const BSTR strLocale,
        const BSTR strClassMapping,
        const BSTR strInstMapping,
        const BSTR strPropMapping,
        VARIANT *pvValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE PutProperty(
        LONG lFlags,
        const BSTR strLocale,
        const BSTR strClassMapping,
        const BSTR strInstMapping,
        const BSTR strPropMapping,
        const VARIANT *pvValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemPropertyProvider, 0xce61e841, 0x65bc, 0x11d0, 0xb6,0xbd, 0x00,0xaa,0x00,0x32,0x40,0xc7)
#endif
#else
typedef struct IWbemPropertyProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemPropertyProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemPropertyProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemPropertyProvider *This);

    /*** IWbemPropertyProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IWbemPropertyProvider *This,
        LONG lFlags,
        const BSTR strLocale,
        const BSTR strClassMapping,
        const BSTR strInstMapping,
        const BSTR strPropMapping,
        VARIANT *pvValue);

    HRESULT (STDMETHODCALLTYPE *PutProperty)(
        IWbemPropertyProvider *This,
        LONG lFlags,
        const BSTR strLocale,
        const BSTR strClassMapping,
        const BSTR strInstMapping,
        const BSTR strPropMapping,
        const VARIANT *pvValue);

    END_INTERFACE
} IWbemPropertyProviderVtbl;

interface IWbemPropertyProvider {
    CONST_VTBL IWbemPropertyProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemPropertyProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemPropertyProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemPropertyProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemPropertyProvider methods ***/
#define IWbemPropertyProvider_GetProperty(This,lFlags,strLocale,strClassMapping,strInstMapping,strPropMapping,pvValue) (This)->lpVtbl->GetProperty(This,lFlags,strLocale,strClassMapping,strInstMapping,strPropMapping,pvValue)
#define IWbemPropertyProvider_PutProperty(This,lFlags,strLocale,strClassMapping,strInstMapping,strPropMapping,pvValue) (This)->lpVtbl->PutProperty(This,lFlags,strLocale,strClassMapping,strInstMapping,strPropMapping,pvValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemPropertyProvider_QueryInterface(IWbemPropertyProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemPropertyProvider_AddRef(IWbemPropertyProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemPropertyProvider_Release(IWbemPropertyProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemPropertyProvider methods ***/
static inline HRESULT IWbemPropertyProvider_GetProperty(IWbemPropertyProvider* This,LONG lFlags,const BSTR strLocale,const BSTR strClassMapping,const BSTR strInstMapping,const BSTR strPropMapping,VARIANT *pvValue) {
    return This->lpVtbl->GetProperty(This,lFlags,strLocale,strClassMapping,strInstMapping,strPropMapping,pvValue);
}
static inline HRESULT IWbemPropertyProvider_PutProperty(IWbemPropertyProvider* This,LONG lFlags,const BSTR strLocale,const BSTR strClassMapping,const BSTR strInstMapping,const BSTR strPropMapping,const VARIANT *pvValue) {
    return This->lpVtbl->PutProperty(This,lFlags,strLocale,strClassMapping,strInstMapping,strPropMapping,pvValue);
}
#endif
#endif

#endif


#endif  /* __IWbemPropertyProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemEventProvider interface
 */
#ifndef __IWbemEventProvider_INTERFACE_DEFINED__
#define __IWbemEventProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemEventProvider, 0xe245105b, 0xb06e, 0x11d0, 0xad,0x61, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e245105b-b06e-11d0-ad61-00c04fd8fdff")
IWbemEventProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ProvideEvents(
        IWbemObjectSink *pSink,
        LONG lFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemEventProvider, 0xe245105b, 0xb06e, 0x11d0, 0xad,0x61, 0x00,0xc0,0x4f,0xd8,0xfd,0xff)
#endif
#else
typedef struct IWbemEventProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemEventProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemEventProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemEventProvider *This);

    /*** IWbemEventProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *ProvideEvents)(
        IWbemEventProvider *This,
        IWbemObjectSink *pSink,
        LONG lFlags);

    END_INTERFACE
} IWbemEventProviderVtbl;

interface IWbemEventProvider {
    CONST_VTBL IWbemEventProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemEventProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemEventProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemEventProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemEventProvider methods ***/
#define IWbemEventProvider_ProvideEvents(This,pSink,lFlags) (This)->lpVtbl->ProvideEvents(This,pSink,lFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemEventProvider_QueryInterface(IWbemEventProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemEventProvider_AddRef(IWbemEventProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemEventProvider_Release(IWbemEventProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemEventProvider methods ***/
static inline HRESULT IWbemEventProvider_ProvideEvents(IWbemEventProvider* This,IWbemObjectSink *pSink,LONG lFlags) {
    return This->lpVtbl->ProvideEvents(This,pSink,lFlags);
}
#endif
#endif

#endif


#endif  /* __IWbemEventProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemEventProviderQuerySink interface
 */
#ifndef __IWbemEventProviderQuerySink_INTERFACE_DEFINED__
#define __IWbemEventProviderQuerySink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemEventProviderQuerySink, 0x580acaf8, 0xfa1c, 0x11d0, 0xad,0x72, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("580acaf8-fa1c-11d0-ad72-00c04fd8fdff")
IWbemEventProviderQuerySink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE NewQuery(
        ULONG dwId,
        WBEM_WSTR wszQueryLanguage,
        WBEM_WSTR wszQuery) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelQuery(
        ULONG dwId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemEventProviderQuerySink, 0x580acaf8, 0xfa1c, 0x11d0, 0xad,0x72, 0x00,0xc0,0x4f,0xd8,0xfd,0xff)
#endif
#else
typedef struct IWbemEventProviderQuerySinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemEventProviderQuerySink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemEventProviderQuerySink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemEventProviderQuerySink *This);

    /*** IWbemEventProviderQuerySink methods ***/
    HRESULT (STDMETHODCALLTYPE *NewQuery)(
        IWbemEventProviderQuerySink *This,
        ULONG dwId,
        WBEM_WSTR wszQueryLanguage,
        WBEM_WSTR wszQuery);

    HRESULT (STDMETHODCALLTYPE *CancelQuery)(
        IWbemEventProviderQuerySink *This,
        ULONG dwId);

    END_INTERFACE
} IWbemEventProviderQuerySinkVtbl;

interface IWbemEventProviderQuerySink {
    CONST_VTBL IWbemEventProviderQuerySinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemEventProviderQuerySink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemEventProviderQuerySink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemEventProviderQuerySink_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemEventProviderQuerySink methods ***/
#define IWbemEventProviderQuerySink_NewQuery(This,dwId,wszQueryLanguage,wszQuery) (This)->lpVtbl->NewQuery(This,dwId,wszQueryLanguage,wszQuery)
#define IWbemEventProviderQuerySink_CancelQuery(This,dwId) (This)->lpVtbl->CancelQuery(This,dwId)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemEventProviderQuerySink_QueryInterface(IWbemEventProviderQuerySink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemEventProviderQuerySink_AddRef(IWbemEventProviderQuerySink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemEventProviderQuerySink_Release(IWbemEventProviderQuerySink* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemEventProviderQuerySink methods ***/
static inline HRESULT IWbemEventProviderQuerySink_NewQuery(IWbemEventProviderQuerySink* This,ULONG dwId,WBEM_WSTR wszQueryLanguage,WBEM_WSTR wszQuery) {
    return This->lpVtbl->NewQuery(This,dwId,wszQueryLanguage,wszQuery);
}
static inline HRESULT IWbemEventProviderQuerySink_CancelQuery(IWbemEventProviderQuerySink* This,ULONG dwId) {
    return This->lpVtbl->CancelQuery(This,dwId);
}
#endif
#endif

#endif


#endif  /* __IWbemEventProviderQuerySink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemEventProviderSecurity interface
 */
#ifndef __IWbemEventProviderSecurity_INTERFACE_DEFINED__
#define __IWbemEventProviderSecurity_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemEventProviderSecurity, 0x631f7d96, 0xd993, 0x11d2, 0xb3,0x39, 0x00,0x10,0x5a,0x1f,0x4a,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("631f7d96-d993-11d2-b339-00105a1f4aaf")
IWbemEventProviderSecurity : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AccessCheck(
        WBEM_CWSTR wszQueryLanguage,
        WBEM_CWSTR wszQuery,
        LONG lSidLength,
        const BYTE *pSid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemEventProviderSecurity, 0x631f7d96, 0xd993, 0x11d2, 0xb3,0x39, 0x00,0x10,0x5a,0x1f,0x4a,0xaf)
#endif
#else
typedef struct IWbemEventProviderSecurityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemEventProviderSecurity *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemEventProviderSecurity *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemEventProviderSecurity *This);

    /*** IWbemEventProviderSecurity methods ***/
    HRESULT (STDMETHODCALLTYPE *AccessCheck)(
        IWbemEventProviderSecurity *This,
        WBEM_CWSTR wszQueryLanguage,
        WBEM_CWSTR wszQuery,
        LONG lSidLength,
        const BYTE *pSid);

    END_INTERFACE
} IWbemEventProviderSecurityVtbl;

interface IWbemEventProviderSecurity {
    CONST_VTBL IWbemEventProviderSecurityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemEventProviderSecurity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemEventProviderSecurity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemEventProviderSecurity_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemEventProviderSecurity methods ***/
#define IWbemEventProviderSecurity_AccessCheck(This,wszQueryLanguage,wszQuery,lSidLength,pSid) (This)->lpVtbl->AccessCheck(This,wszQueryLanguage,wszQuery,lSidLength,pSid)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemEventProviderSecurity_QueryInterface(IWbemEventProviderSecurity* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemEventProviderSecurity_AddRef(IWbemEventProviderSecurity* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemEventProviderSecurity_Release(IWbemEventProviderSecurity* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemEventProviderSecurity methods ***/
static inline HRESULT IWbemEventProviderSecurity_AccessCheck(IWbemEventProviderSecurity* This,WBEM_CWSTR wszQueryLanguage,WBEM_CWSTR wszQuery,LONG lSidLength,const BYTE *pSid) {
    return This->lpVtbl->AccessCheck(This,wszQueryLanguage,wszQuery,lSidLength,pSid);
}
#endif
#endif

#endif


#endif  /* __IWbemEventProviderSecurity_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemProviderIdentity interface
 */
#ifndef __IWbemProviderIdentity_INTERFACE_DEFINED__
#define __IWbemProviderIdentity_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemProviderIdentity, 0x631f7d97, 0xd993, 0x11d2, 0xb3,0x39, 0x00,0x10,0x5a,0x1f,0x4a,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("631f7d97-d993-11d2-b339-00105a1f4aaf")
IWbemProviderIdentity : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetRegistrationObject(
        LONG lFlags,
        IWbemClassObject *pProvReg) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemProviderIdentity, 0x631f7d97, 0xd993, 0x11d2, 0xb3,0x39, 0x00,0x10,0x5a,0x1f,0x4a,0xaf)
#endif
#else
typedef struct IWbemProviderIdentityVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemProviderIdentity *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemProviderIdentity *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemProviderIdentity *This);

    /*** IWbemProviderIdentity methods ***/
    HRESULT (STDMETHODCALLTYPE *SetRegistrationObject)(
        IWbemProviderIdentity *This,
        LONG lFlags,
        IWbemClassObject *pProvReg);

    END_INTERFACE
} IWbemProviderIdentityVtbl;

interface IWbemProviderIdentity {
    CONST_VTBL IWbemProviderIdentityVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemProviderIdentity_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemProviderIdentity_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemProviderIdentity_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemProviderIdentity methods ***/
#define IWbemProviderIdentity_SetRegistrationObject(This,lFlags,pProvReg) (This)->lpVtbl->SetRegistrationObject(This,lFlags,pProvReg)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemProviderIdentity_QueryInterface(IWbemProviderIdentity* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemProviderIdentity_AddRef(IWbemProviderIdentity* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemProviderIdentity_Release(IWbemProviderIdentity* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemProviderIdentity methods ***/
static inline HRESULT IWbemProviderIdentity_SetRegistrationObject(IWbemProviderIdentity* This,LONG lFlags,IWbemClassObject *pProvReg) {
    return This->lpVtbl->SetRegistrationObject(This,lFlags,pProvReg);
}
#endif
#endif

#endif


#endif  /* __IWbemProviderIdentity_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemEventConsumerProvider interface
 */
#ifndef __IWbemEventConsumerProvider_INTERFACE_DEFINED__
#define __IWbemEventConsumerProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemEventConsumerProvider, 0xe246107a, 0xb06e, 0x11d0, 0xad,0x61, 0x00,0xc0,0x4f,0xd8,0xfd,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e246107a-b06e-11d0-ad61-00c04fd8fdff")
IWbemEventConsumerProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE FindConsumer(
        IWbemClassObject *pLogicalConsumer,
        IWbemUnboundObjectSink **ppConsumer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemEventConsumerProvider, 0xe246107a, 0xb06e, 0x11d0, 0xad,0x61, 0x00,0xc0,0x4f,0xd8,0xfd,0xff)
#endif
#else
typedef struct IWbemEventConsumerProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemEventConsumerProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemEventConsumerProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemEventConsumerProvider *This);

    /*** IWbemEventConsumerProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *FindConsumer)(
        IWbemEventConsumerProvider *This,
        IWbemClassObject *pLogicalConsumer,
        IWbemUnboundObjectSink **ppConsumer);

    END_INTERFACE
} IWbemEventConsumerProviderVtbl;

interface IWbemEventConsumerProvider {
    CONST_VTBL IWbemEventConsumerProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemEventConsumerProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemEventConsumerProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemEventConsumerProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemEventConsumerProvider methods ***/
#define IWbemEventConsumerProvider_FindConsumer(This,pLogicalConsumer,ppConsumer) (This)->lpVtbl->FindConsumer(This,pLogicalConsumer,ppConsumer)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemEventConsumerProvider_QueryInterface(IWbemEventConsumerProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemEventConsumerProvider_AddRef(IWbemEventConsumerProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemEventConsumerProvider_Release(IWbemEventConsumerProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemEventConsumerProvider methods ***/
static inline HRESULT IWbemEventConsumerProvider_FindConsumer(IWbemEventConsumerProvider* This,IWbemClassObject *pLogicalConsumer,IWbemUnboundObjectSink **ppConsumer) {
    return This->lpVtbl->FindConsumer(This,pLogicalConsumer,ppConsumer);
}
#endif
#endif

#endif


#endif  /* __IWbemEventConsumerProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemProviderInitSink interface
 */
#ifndef __IWbemProviderInitSink_INTERFACE_DEFINED__
#define __IWbemProviderInitSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemProviderInitSink, 0x1be41571, 0x91dd, 0x11d1, 0xae,0xb2, 0x00,0xc0,0x4f,0xb6,0x88,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1be41571-91dd-11d1-aeb2-00c04fb68820")
IWbemProviderInitSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetStatus(
        LONG lStatus,
        LONG lFlags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemProviderInitSink, 0x1be41571, 0x91dd, 0x11d1, 0xae,0xb2, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#else
typedef struct IWbemProviderInitSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemProviderInitSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemProviderInitSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemProviderInitSink *This);

    /*** IWbemProviderInitSink methods ***/
    HRESULT (STDMETHODCALLTYPE *SetStatus)(
        IWbemProviderInitSink *This,
        LONG lStatus,
        LONG lFlags);

    END_INTERFACE
} IWbemProviderInitSinkVtbl;

interface IWbemProviderInitSink {
    CONST_VTBL IWbemProviderInitSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemProviderInitSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemProviderInitSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemProviderInitSink_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemProviderInitSink methods ***/
#define IWbemProviderInitSink_SetStatus(This,lStatus,lFlags) (This)->lpVtbl->SetStatus(This,lStatus,lFlags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemProviderInitSink_QueryInterface(IWbemProviderInitSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemProviderInitSink_AddRef(IWbemProviderInitSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemProviderInitSink_Release(IWbemProviderInitSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemProviderInitSink methods ***/
static inline HRESULT IWbemProviderInitSink_SetStatus(IWbemProviderInitSink* This,LONG lStatus,LONG lFlags) {
    return This->lpVtbl->SetStatus(This,lStatus,lFlags);
}
#endif
#endif

#endif


#endif  /* __IWbemProviderInitSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemProviderInit interface
 */
#ifndef __IWbemProviderInit_INTERFACE_DEFINED__
#define __IWbemProviderInit_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemProviderInit, 0x1be41572, 0x91dd, 0x11d1, 0xae,0xb2, 0x00,0xc0,0x4f,0xb6,0x88,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1be41572-91dd-11d1-aeb2-00c04fb68820")
IWbemProviderInit : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        LPWSTR wszUser,
        LONG lFlags,
        LPWSTR wszNamespace,
        LPWSTR wszLocale,
        IWbemServices *pNamespace,
        IWbemContext *pCtx,
        IWbemProviderInitSink *pInitSink) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemProviderInit, 0x1be41572, 0x91dd, 0x11d1, 0xae,0xb2, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#else
typedef struct IWbemProviderInitVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemProviderInit *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemProviderInit *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemProviderInit *This);

    /*** IWbemProviderInit methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWbemProviderInit *This,
        LPWSTR wszUser,
        LONG lFlags,
        LPWSTR wszNamespace,
        LPWSTR wszLocale,
        IWbemServices *pNamespace,
        IWbemContext *pCtx,
        IWbemProviderInitSink *pInitSink);

    END_INTERFACE
} IWbemProviderInitVtbl;

interface IWbemProviderInit {
    CONST_VTBL IWbemProviderInitVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemProviderInit_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemProviderInit_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemProviderInit_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemProviderInit methods ***/
#define IWbemProviderInit_Initialize(This,wszUser,lFlags,wszNamespace,wszLocale,pNamespace,pCtx,pInitSink) (This)->lpVtbl->Initialize(This,wszUser,lFlags,wszNamespace,wszLocale,pNamespace,pCtx,pInitSink)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemProviderInit_QueryInterface(IWbemProviderInit* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemProviderInit_AddRef(IWbemProviderInit* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemProviderInit_Release(IWbemProviderInit* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemProviderInit methods ***/
static inline HRESULT IWbemProviderInit_Initialize(IWbemProviderInit* This,LPWSTR wszUser,LONG lFlags,LPWSTR wszNamespace,LPWSTR wszLocale,IWbemServices *pNamespace,IWbemContext *pCtx,IWbemProviderInitSink *pInitSink) {
    return This->lpVtbl->Initialize(This,wszUser,lFlags,wszNamespace,wszLocale,pNamespace,pCtx,pInitSink);
}
#endif
#endif

#endif


#endif  /* __IWbemProviderInit_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemHiPerfProvider interface
 */
#ifndef __IWbemHiPerfProvider_INTERFACE_DEFINED__
#define __IWbemHiPerfProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemHiPerfProvider, 0x49353c93, 0x516b, 0x11d1, 0xae,0xa6, 0x00,0xc0,0x4f,0xb6,0x88,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("49353c93-516b-11d1-aea6-00c04fb68820")
IWbemHiPerfProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE QueryInstances(
        IWbemServices *pNamespace,
        WCHAR *wszClass,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRefresher(
        IWbemServices *pNamespace,
        LONG lFlags,
        IWbemRefresher **ppRefresher) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRefreshableObject(
        IWbemServices *pNamespace,
        IWbemObjectAccess *pTemplate,
        IWbemRefresher *pRefresher,
        LONG lFlags,
        IWbemContext *pContext,
        IWbemObjectAccess **ppRefreshable,
        LONG *plId) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopRefreshing(
        IWbemRefresher *pRefresher,
        LONG lId,
        LONG lFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRefreshableEnum(
        IWbemServices *pNamespace,
        LPCWSTR wszClass,
        IWbemRefresher *pRefresher,
        LONG lFlags,
        IWbemContext *pContext,
        IWbemHiPerfEnum *pHiPerfEnum,
        LONG *plId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetObjects(
        IWbemServices *pNamespace,
        LONG lNumObjects,
        IWbemObjectAccess **apObj,
        LONG lFlags,
        IWbemContext *pContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemHiPerfProvider, 0x49353c93, 0x516b, 0x11d1, 0xae,0xa6, 0x00,0xc0,0x4f,0xb6,0x88,0x20)
#endif
#else
typedef struct IWbemHiPerfProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemHiPerfProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemHiPerfProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemHiPerfProvider *This);

    /*** IWbemHiPerfProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInstances)(
        IWbemHiPerfProvider *This,
        IWbemServices *pNamespace,
        WCHAR *wszClass,
        LONG lFlags,
        IWbemContext *pCtx,
        IWbemObjectSink *pSink);

    HRESULT (STDMETHODCALLTYPE *CreateRefresher)(
        IWbemHiPerfProvider *This,
        IWbemServices *pNamespace,
        LONG lFlags,
        IWbemRefresher **ppRefresher);

    HRESULT (STDMETHODCALLTYPE *CreateRefreshableObject)(
        IWbemHiPerfProvider *This,
        IWbemServices *pNamespace,
        IWbemObjectAccess *pTemplate,
        IWbemRefresher *pRefresher,
        LONG lFlags,
        IWbemContext *pContext,
        IWbemObjectAccess **ppRefreshable,
        LONG *plId);

    HRESULT (STDMETHODCALLTYPE *StopRefreshing)(
        IWbemHiPerfProvider *This,
        IWbemRefresher *pRefresher,
        LONG lId,
        LONG lFlags);

    HRESULT (STDMETHODCALLTYPE *CreateRefreshableEnum)(
        IWbemHiPerfProvider *This,
        IWbemServices *pNamespace,
        LPCWSTR wszClass,
        IWbemRefresher *pRefresher,
        LONG lFlags,
        IWbemContext *pContext,
        IWbemHiPerfEnum *pHiPerfEnum,
        LONG *plId);

    HRESULT (STDMETHODCALLTYPE *GetObjects)(
        IWbemHiPerfProvider *This,
        IWbemServices *pNamespace,
        LONG lNumObjects,
        IWbemObjectAccess **apObj,
        LONG lFlags,
        IWbemContext *pContext);

    END_INTERFACE
} IWbemHiPerfProviderVtbl;

interface IWbemHiPerfProvider {
    CONST_VTBL IWbemHiPerfProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemHiPerfProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemHiPerfProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemHiPerfProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemHiPerfProvider methods ***/
#define IWbemHiPerfProvider_QueryInstances(This,pNamespace,wszClass,lFlags,pCtx,pSink) (This)->lpVtbl->QueryInstances(This,pNamespace,wszClass,lFlags,pCtx,pSink)
#define IWbemHiPerfProvider_CreateRefresher(This,pNamespace,lFlags,ppRefresher) (This)->lpVtbl->CreateRefresher(This,pNamespace,lFlags,ppRefresher)
#define IWbemHiPerfProvider_CreateRefreshableObject(This,pNamespace,pTemplate,pRefresher,lFlags,pContext,ppRefreshable,plId) (This)->lpVtbl->CreateRefreshableObject(This,pNamespace,pTemplate,pRefresher,lFlags,pContext,ppRefreshable,plId)
#define IWbemHiPerfProvider_StopRefreshing(This,pRefresher,lId,lFlags) (This)->lpVtbl->StopRefreshing(This,pRefresher,lId,lFlags)
#define IWbemHiPerfProvider_CreateRefreshableEnum(This,pNamespace,wszClass,pRefresher,lFlags,pContext,pHiPerfEnum,plId) (This)->lpVtbl->CreateRefreshableEnum(This,pNamespace,wszClass,pRefresher,lFlags,pContext,pHiPerfEnum,plId)
#define IWbemHiPerfProvider_GetObjects(This,pNamespace,lNumObjects,apObj,lFlags,pContext) (This)->lpVtbl->GetObjects(This,pNamespace,lNumObjects,apObj,lFlags,pContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemHiPerfProvider_QueryInterface(IWbemHiPerfProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemHiPerfProvider_AddRef(IWbemHiPerfProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemHiPerfProvider_Release(IWbemHiPerfProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemHiPerfProvider methods ***/
static inline HRESULT IWbemHiPerfProvider_QueryInstances(IWbemHiPerfProvider* This,IWbemServices *pNamespace,WCHAR *wszClass,LONG lFlags,IWbemContext *pCtx,IWbemObjectSink *pSink) {
    return This->lpVtbl->QueryInstances(This,pNamespace,wszClass,lFlags,pCtx,pSink);
}
static inline HRESULT IWbemHiPerfProvider_CreateRefresher(IWbemHiPerfProvider* This,IWbemServices *pNamespace,LONG lFlags,IWbemRefresher **ppRefresher) {
    return This->lpVtbl->CreateRefresher(This,pNamespace,lFlags,ppRefresher);
}
static inline HRESULT IWbemHiPerfProvider_CreateRefreshableObject(IWbemHiPerfProvider* This,IWbemServices *pNamespace,IWbemObjectAccess *pTemplate,IWbemRefresher *pRefresher,LONG lFlags,IWbemContext *pContext,IWbemObjectAccess **ppRefreshable,LONG *plId) {
    return This->lpVtbl->CreateRefreshableObject(This,pNamespace,pTemplate,pRefresher,lFlags,pContext,ppRefreshable,plId);
}
static inline HRESULT IWbemHiPerfProvider_StopRefreshing(IWbemHiPerfProvider* This,IWbemRefresher *pRefresher,LONG lId,LONG lFlags) {
    return This->lpVtbl->StopRefreshing(This,pRefresher,lId,lFlags);
}
static inline HRESULT IWbemHiPerfProvider_CreateRefreshableEnum(IWbemHiPerfProvider* This,IWbemServices *pNamespace,LPCWSTR wszClass,IWbemRefresher *pRefresher,LONG lFlags,IWbemContext *pContext,IWbemHiPerfEnum *pHiPerfEnum,LONG *plId) {
    return This->lpVtbl->CreateRefreshableEnum(This,pNamespace,wszClass,pRefresher,lFlags,pContext,pHiPerfEnum,plId);
}
static inline HRESULT IWbemHiPerfProvider_GetObjects(IWbemHiPerfProvider* This,IWbemServices *pNamespace,LONG lNumObjects,IWbemObjectAccess **apObj,LONG lFlags,IWbemContext *pContext) {
    return This->lpVtbl->GetObjects(This,pNamespace,lNumObjects,apObj,lFlags,pContext);
}
#endif
#endif

#endif


#endif  /* __IWbemHiPerfProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemDecoupledRegistrar interface
 */
#ifndef __IWbemDecoupledRegistrar_INTERFACE_DEFINED__
#define __IWbemDecoupledRegistrar_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemDecoupledRegistrar, 0x1005cbcf, 0xe64f, 0x4646, 0xbc,0xd3, 0x3a,0x08,0x9d,0x8a,0x84,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1005cbcf-e64f-4646-bcd3-3a089d8a84b4")
IWbemDecoupledRegistrar : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Register(
        LONG a_Flags,
        IWbemContext *a_Context,
        LPCWSTR a_User,
        LPCWSTR a_Locale,
        LPCWSTR a_Scope,
        LPCWSTR a_Registration,
        IUnknown *pIUnknown) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnRegister(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemDecoupledRegistrar, 0x1005cbcf, 0xe64f, 0x4646, 0xbc,0xd3, 0x3a,0x08,0x9d,0x8a,0x84,0xb4)
#endif
#else
typedef struct IWbemDecoupledRegistrarVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemDecoupledRegistrar *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemDecoupledRegistrar *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemDecoupledRegistrar *This);

    /*** IWbemDecoupledRegistrar methods ***/
    HRESULT (STDMETHODCALLTYPE *Register)(
        IWbemDecoupledRegistrar *This,
        LONG a_Flags,
        IWbemContext *a_Context,
        LPCWSTR a_User,
        LPCWSTR a_Locale,
        LPCWSTR a_Scope,
        LPCWSTR a_Registration,
        IUnknown *pIUnknown);

    HRESULT (STDMETHODCALLTYPE *UnRegister)(
        IWbemDecoupledRegistrar *This);

    END_INTERFACE
} IWbemDecoupledRegistrarVtbl;

interface IWbemDecoupledRegistrar {
    CONST_VTBL IWbemDecoupledRegistrarVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemDecoupledRegistrar_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemDecoupledRegistrar_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemDecoupledRegistrar_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemDecoupledRegistrar methods ***/
#define IWbemDecoupledRegistrar_Register(This,a_Flags,a_Context,a_User,a_Locale,a_Scope,a_Registration,pIUnknown) (This)->lpVtbl->Register(This,a_Flags,a_Context,a_User,a_Locale,a_Scope,a_Registration,pIUnknown)
#define IWbemDecoupledRegistrar_UnRegister(This) (This)->lpVtbl->UnRegister(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemDecoupledRegistrar_QueryInterface(IWbemDecoupledRegistrar* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemDecoupledRegistrar_AddRef(IWbemDecoupledRegistrar* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemDecoupledRegistrar_Release(IWbemDecoupledRegistrar* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemDecoupledRegistrar methods ***/
static inline HRESULT IWbemDecoupledRegistrar_Register(IWbemDecoupledRegistrar* This,LONG a_Flags,IWbemContext *a_Context,LPCWSTR a_User,LPCWSTR a_Locale,LPCWSTR a_Scope,LPCWSTR a_Registration,IUnknown *pIUnknown) {
    return This->lpVtbl->Register(This,a_Flags,a_Context,a_User,a_Locale,a_Scope,a_Registration,pIUnknown);
}
static inline HRESULT IWbemDecoupledRegistrar_UnRegister(IWbemDecoupledRegistrar* This) {
    return This->lpVtbl->UnRegister(This);
}
#endif
#endif

#endif


#endif  /* __IWbemDecoupledRegistrar_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemDecoupledBasicEventProvider interface
 */
#ifndef __IWbemDecoupledBasicEventProvider_INTERFACE_DEFINED__
#define __IWbemDecoupledBasicEventProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemDecoupledBasicEventProvider, 0x86336d20, 0xca11, 0x4786, 0x9e,0xf1, 0xbc,0x8a,0x94,0x6b,0x42,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("86336d20-ca11-4786-9ef1-bc8a946b42fc")
IWbemDecoupledBasicEventProvider : public IWbemDecoupledRegistrar
{
    virtual HRESULT STDMETHODCALLTYPE GetSink(
        LONG a_Flags,
        IWbemContext *a_Context,
        IWbemObjectSink **a_Sink) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetService(
        LONG a_Flags,
        IWbemContext *a_Context,
        IWbemServices **a_Service) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemDecoupledBasicEventProvider, 0x86336d20, 0xca11, 0x4786, 0x9e,0xf1, 0xbc,0x8a,0x94,0x6b,0x42,0xfc)
#endif
#else
typedef struct IWbemDecoupledBasicEventProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemDecoupledBasicEventProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemDecoupledBasicEventProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemDecoupledBasicEventProvider *This);

    /*** IWbemDecoupledRegistrar methods ***/
    HRESULT (STDMETHODCALLTYPE *Register)(
        IWbemDecoupledBasicEventProvider *This,
        LONG a_Flags,
        IWbemContext *a_Context,
        LPCWSTR a_User,
        LPCWSTR a_Locale,
        LPCWSTR a_Scope,
        LPCWSTR a_Registration,
        IUnknown *pIUnknown);

    HRESULT (STDMETHODCALLTYPE *UnRegister)(
        IWbemDecoupledBasicEventProvider *This);

    /*** IWbemDecoupledBasicEventProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSink)(
        IWbemDecoupledBasicEventProvider *This,
        LONG a_Flags,
        IWbemContext *a_Context,
        IWbemObjectSink **a_Sink);

    HRESULT (STDMETHODCALLTYPE *GetService)(
        IWbemDecoupledBasicEventProvider *This,
        LONG a_Flags,
        IWbemContext *a_Context,
        IWbemServices **a_Service);

    END_INTERFACE
} IWbemDecoupledBasicEventProviderVtbl;

interface IWbemDecoupledBasicEventProvider {
    CONST_VTBL IWbemDecoupledBasicEventProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemDecoupledBasicEventProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemDecoupledBasicEventProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemDecoupledBasicEventProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemDecoupledRegistrar methods ***/
#define IWbemDecoupledBasicEventProvider_Register(This,a_Flags,a_Context,a_User,a_Locale,a_Scope,a_Registration,pIUnknown) (This)->lpVtbl->Register(This,a_Flags,a_Context,a_User,a_Locale,a_Scope,a_Registration,pIUnknown)
#define IWbemDecoupledBasicEventProvider_UnRegister(This) (This)->lpVtbl->UnRegister(This)
/*** IWbemDecoupledBasicEventProvider methods ***/
#define IWbemDecoupledBasicEventProvider_GetSink(This,a_Flags,a_Context,a_Sink) (This)->lpVtbl->GetSink(This,a_Flags,a_Context,a_Sink)
#define IWbemDecoupledBasicEventProvider_GetService(This,a_Flags,a_Context,a_Service) (This)->lpVtbl->GetService(This,a_Flags,a_Context,a_Service)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemDecoupledBasicEventProvider_QueryInterface(IWbemDecoupledBasicEventProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemDecoupledBasicEventProvider_AddRef(IWbemDecoupledBasicEventProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemDecoupledBasicEventProvider_Release(IWbemDecoupledBasicEventProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemDecoupledRegistrar methods ***/
static inline HRESULT IWbemDecoupledBasicEventProvider_Register(IWbemDecoupledBasicEventProvider* This,LONG a_Flags,IWbemContext *a_Context,LPCWSTR a_User,LPCWSTR a_Locale,LPCWSTR a_Scope,LPCWSTR a_Registration,IUnknown *pIUnknown) {
    return This->lpVtbl->Register(This,a_Flags,a_Context,a_User,a_Locale,a_Scope,a_Registration,pIUnknown);
}
static inline HRESULT IWbemDecoupledBasicEventProvider_UnRegister(IWbemDecoupledBasicEventProvider* This) {
    return This->lpVtbl->UnRegister(This);
}
/*** IWbemDecoupledBasicEventProvider methods ***/
static inline HRESULT IWbemDecoupledBasicEventProvider_GetSink(IWbemDecoupledBasicEventProvider* This,LONG a_Flags,IWbemContext *a_Context,IWbemObjectSink **a_Sink) {
    return This->lpVtbl->GetSink(This,a_Flags,a_Context,a_Sink);
}
static inline HRESULT IWbemDecoupledBasicEventProvider_GetService(IWbemDecoupledBasicEventProvider* This,LONG a_Flags,IWbemContext *a_Context,IWbemServices **a_Service) {
    return This->lpVtbl->GetService(This,a_Flags,a_Context,a_Service);
}
#endif
#endif

#endif


#endif  /* __IWbemDecoupledBasicEventProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWbemEventSink interface
 */
#ifndef __IWbemEventSink_INTERFACE_DEFINED__
#define __IWbemEventSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWbemEventSink, 0x3ae0080a, 0x7e3a, 0x4366, 0xbf,0x89, 0x0f,0xee,0xdc,0x93,0x16,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3ae0080a-7e3a-4366-bf89-0feedc931659")
IWbemEventSink : public IWbemObjectSink
{
    virtual HRESULT STDMETHODCALLTYPE SetSinkSecurity(
        LONG lSDLength,
        BYTE *pSD) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsActive(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRestrictedSink(
        LONG lNumQueries,
        const LPCWSTR *awszQueries,
        IUnknown *pCallback,
        IWbemEventSink **ppSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBatchingParameters(
        LONG lFlags,
        DWORD dwMaxBufferSize,
        DWORD dwMaxSendLatency) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWbemEventSink, 0x3ae0080a, 0x7e3a, 0x4366, 0xbf,0x89, 0x0f,0xee,0xdc,0x93,0x16,0x59)
#endif
#else
typedef struct IWbemEventSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWbemEventSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWbemEventSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWbemEventSink *This);

    /*** IWbemObjectSink methods ***/
    HRESULT (STDMETHODCALLTYPE *Indicate)(
        IWbemEventSink *This,
        LONG lObjectCount,
        IWbemClassObject **apObjArray);

    HRESULT (STDMETHODCALLTYPE *SetStatus)(
        IWbemEventSink *This,
        LONG lFlags,
        HRESULT hResult,
        BSTR strParam,
        IWbemClassObject *pObjParam);

    /*** IWbemEventSink methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSinkSecurity)(
        IWbemEventSink *This,
        LONG lSDLength,
        BYTE *pSD);

    HRESULT (STDMETHODCALLTYPE *IsActive)(
        IWbemEventSink *This);

    HRESULT (STDMETHODCALLTYPE *GetRestrictedSink)(
        IWbemEventSink *This,
        LONG lNumQueries,
        const LPCWSTR *awszQueries,
        IUnknown *pCallback,
        IWbemEventSink **ppSink);

    HRESULT (STDMETHODCALLTYPE *SetBatchingParameters)(
        IWbemEventSink *This,
        LONG lFlags,
        DWORD dwMaxBufferSize,
        DWORD dwMaxSendLatency);

    END_INTERFACE
} IWbemEventSinkVtbl;

interface IWbemEventSink {
    CONST_VTBL IWbemEventSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWbemEventSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWbemEventSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWbemEventSink_Release(This) (This)->lpVtbl->Release(This)
/*** IWbemObjectSink methods ***/
#define IWbemEventSink_Indicate(This,lObjectCount,apObjArray) (This)->lpVtbl->Indicate(This,lObjectCount,apObjArray)
#define IWbemEventSink_SetStatus(This,lFlags,hResult,strParam,pObjParam) (This)->lpVtbl->SetStatus(This,lFlags,hResult,strParam,pObjParam)
/*** IWbemEventSink methods ***/
#define IWbemEventSink_SetSinkSecurity(This,lSDLength,pSD) (This)->lpVtbl->SetSinkSecurity(This,lSDLength,pSD)
#define IWbemEventSink_IsActive(This) (This)->lpVtbl->IsActive(This)
#define IWbemEventSink_GetRestrictedSink(This,lNumQueries,awszQueries,pCallback,ppSink) (This)->lpVtbl->GetRestrictedSink(This,lNumQueries,awszQueries,pCallback,ppSink)
#define IWbemEventSink_SetBatchingParameters(This,lFlags,dwMaxBufferSize,dwMaxSendLatency) (This)->lpVtbl->SetBatchingParameters(This,lFlags,dwMaxBufferSize,dwMaxSendLatency)
#else
/*** IUnknown methods ***/
static inline HRESULT IWbemEventSink_QueryInterface(IWbemEventSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWbemEventSink_AddRef(IWbemEventSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWbemEventSink_Release(IWbemEventSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IWbemObjectSink methods ***/
static inline HRESULT IWbemEventSink_Indicate(IWbemEventSink* This,LONG lObjectCount,IWbemClassObject **apObjArray) {
    return This->lpVtbl->Indicate(This,lObjectCount,apObjArray);
}
static inline HRESULT IWbemEventSink_SetStatus(IWbemEventSink* This,LONG lFlags,HRESULT hResult,BSTR strParam,IWbemClassObject *pObjParam) {
    return This->lpVtbl->SetStatus(This,lFlags,hResult,strParam,pObjParam);
}
/*** IWbemEventSink methods ***/
static inline HRESULT IWbemEventSink_SetSinkSecurity(IWbemEventSink* This,LONG lSDLength,BYTE *pSD) {
    return This->lpVtbl->SetSinkSecurity(This,lSDLength,pSD);
}
static inline HRESULT IWbemEventSink_IsActive(IWbemEventSink* This) {
    return This->lpVtbl->IsActive(This);
}
static inline HRESULT IWbemEventSink_GetRestrictedSink(IWbemEventSink* This,LONG lNumQueries,const LPCWSTR *awszQueries,IUnknown *pCallback,IWbemEventSink **ppSink) {
    return This->lpVtbl->GetRestrictedSink(This,lNumQueries,awszQueries,pCallback,ppSink);
}
static inline HRESULT IWbemEventSink_SetBatchingParameters(IWbemEventSink* This,LONG lFlags,DWORD dwMaxBufferSize,DWORD dwMaxSendLatency) {
    return This->lpVtbl->SetBatchingParameters(This,lFlags,dwMaxBufferSize,dwMaxSendLatency);
}
#endif
#endif

#endif


#endif  /* __IWbemEventSink_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wbemprov_h__ */
