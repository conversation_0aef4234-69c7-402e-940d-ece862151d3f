/*** Autogenerated by WIDL 10.12 from include/windows.devices.bluetooth.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_devices_bluetooth_h__
#define __windows_devices_bluetooth_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter ABI::Windows::Devices::Bluetooth::IBluetoothAdapter
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapter;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics ABI::Windows::Devices::Bluetooth::IBluetoothAdapterStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapterStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothAdapter_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothAdapter_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                class BluetoothAdapter;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothAdapter __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothAdapter;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothAdapter_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice ABI::Windows::Devices::Bluetooth::IBluetoothClassOfDevice
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothClassOfDevice;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics ABI::Windows::Devices::Bluetooth::IBluetoothClassOfDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothClassOfDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothClassOfDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothClassOfDevice_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                class BluetoothClassOfDevice;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothClassOfDevice __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothClassOfDevice;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothClassOfDevice_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice ABI::Windows::Devices::Bluetooth::IBluetoothDevice
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDevice;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics ABI::Windows::Devices::Bluetooth::IBluetoothDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothDevice_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                class BluetoothDevice;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothDevice __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothDevice;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothDevice_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId ABI::Windows::Devices::Bluetooth::IBluetoothDeviceId
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDeviceId;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics ABI::Windows::Devices::Bluetooth::IBluetoothDeviceIdStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDeviceIdStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothDeviceId_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothDeviceId_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                class BluetoothDeviceId;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothDeviceId __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothDeviceId;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothDeviceId_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice ABI::Windows::Devices::Bluetooth::IBluetoothLEDevice
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothLEDevice;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics ABI::Windows::Devices::Bluetooth::IBluetoothLEDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothLEDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothLEDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothLEDevice_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                class BluetoothLEDevice;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothLEDevice __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothLEDevice;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothLEDevice_FWD_DEFINED__ */

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice*,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothDevice* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::Streams::IBuffer* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Bluetooth::GenericAttributeProfile::GattDeviceService* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.devices.bluetooth.genericattributeprofile.h>
#include <windows.devices.bluetooth.rfcomm.h>
#include <windows.devices.enumeration.h>
#include <windows.devices.radios.h>
#include <windows.networking.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                enum BluetoothCacheMode {
                    BluetoothCacheMode_Cached = 0,
                    BluetoothCacheMode_Uncached = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode {
    BluetoothCacheMode_Cached = 0,
    BluetoothCacheMode_Uncached = 1
};
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define BluetoothCacheMode __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothConnectionStatus_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothConnectionStatus_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                enum BluetoothConnectionStatus {
                    BluetoothConnectionStatus_Disconnected = 0,
                    BluetoothConnectionStatus_Connected = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothConnectionStatus {
    BluetoothConnectionStatus_Disconnected = 0,
    BluetoothConnectionStatus_Connected = 1
};
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define BluetoothConnectionStatus __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothConnectionStatus
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothConnectionStatus_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothConnectionStatus __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothConnectionStatus;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMajorClass_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMajorClass_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                enum BluetoothMajorClass {
                    BluetoothMajorClass_Miscellaneous = 0,
                    BluetoothMajorClass_Computer = 1,
                    BluetoothMajorClass_Phone = 2,
                    BluetoothMajorClass_NetworkAccessPoint = 3,
                    BluetoothMajorClass_AudioVideo = 4,
                    BluetoothMajorClass_Peripheral = 5,
                    BluetoothMajorClass_Imaging = 6,
                    BluetoothMajorClass_Wearable = 7,
                    BluetoothMajorClass_Toy = 8,
                    BluetoothMajorClass_Health = 9
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMajorClass {
    BluetoothMajorClass_Miscellaneous = 0,
    BluetoothMajorClass_Computer = 1,
    BluetoothMajorClass_Phone = 2,
    BluetoothMajorClass_NetworkAccessPoint = 3,
    BluetoothMajorClass_AudioVideo = 4,
    BluetoothMajorClass_Peripheral = 5,
    BluetoothMajorClass_Imaging = 6,
    BluetoothMajorClass_Wearable = 7,
    BluetoothMajorClass_Toy = 8,
    BluetoothMajorClass_Health = 9
};
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define BluetoothMajorClass __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMajorClass
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMajorClass_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMajorClass __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMajorClass;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMinorClass_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMinorClass_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                enum BluetoothMinorClass {
                    BluetoothMinorClass_Uncategorized = 0,
                    BluetoothMinorClass_ComputerDesktop = 1,
                    BluetoothMinorClass_ComputerServer = 2,
                    BluetoothMinorClass_ComputerLaptop = 3,
                    BluetoothMinorClass_ComputerHandheld = 4,
                    BluetoothMinorClass_ComputerPalmSize = 5,
                    BluetoothMinorClass_ComputerWearable = 6,
                    BluetoothMinorClass_ComputerTablet = 7,
                    BluetoothMinorClass_PhoneCellular = 1,
                    BluetoothMinorClass_PhoneCordless = 2,
                    BluetoothMinorClass_PhoneSmartPhone = 3,
                    BluetoothMinorClass_PhoneWired = 4,
                    BluetoothMinorClass_PhoneIsdn = 5,
                    BluetoothMinorClass_NetworkFullyAvailable = 0,
                    BluetoothMinorClass_NetworkUsed01To17Percent = 8,
                    BluetoothMinorClass_NetworkUsed17To33Percent = 16,
                    BluetoothMinorClass_NetworkUsed33To50Percent = 24,
                    BluetoothMinorClass_NetworkUsed50To67Percent = 32,
                    BluetoothMinorClass_NetworkUsed67To83Percent = 40,
                    BluetoothMinorClass_NetworkUsed83To99Percent = 48,
                    BluetoothMinorClass_NetworkNoServiceAvailable = 56,
                    BluetoothMinorClass_AudioVideoWearableHeadset = 1,
                    BluetoothMinorClass_AudioVideoHandsFree = 2,
                    BluetoothMinorClass_AudioVideoMicrophone = 4,
                    BluetoothMinorClass_AudioVideoLoudspeaker = 5,
                    BluetoothMinorClass_AudioVideoHeadphones = 6,
                    BluetoothMinorClass_AudioVideoPortableAudio = 7,
                    BluetoothMinorClass_AudioVideoCarAudio = 8,
                    BluetoothMinorClass_AudioVideoSetTopBox = 9,
                    BluetoothMinorClass_AudioVideoHifiAudioDevice = 10,
                    BluetoothMinorClass_AudioVideoVcr = 11,
                    BluetoothMinorClass_AudioVideoVideoCamera = 12,
                    BluetoothMinorClass_AudioVideoCamcorder = 13,
                    BluetoothMinorClass_AudioVideoVideoMonitor = 14,
                    BluetoothMinorClass_AudioVideoVideoDisplayAndLoudspeaker = 15,
                    BluetoothMinorClass_AudioVideoVideoConferencing = 16,
                    BluetoothMinorClass_AudioVideoGamingOrToy = 18,
                    BluetoothMinorClass_PeripheralJoystick = 1,
                    BluetoothMinorClass_PeripheralGamepad = 2,
                    BluetoothMinorClass_PeripheralRemoteControl = 3,
                    BluetoothMinorClass_PeripheralSensing = 4,
                    BluetoothMinorClass_PeripheralDigitizerTablet = 5,
                    BluetoothMinorClass_PeripheralCardReader = 6,
                    BluetoothMinorClass_PeripheralDigitalPen = 7,
                    BluetoothMinorClass_PeripheralHandheldScanner = 8,
                    BluetoothMinorClass_PeripheralHandheldGesture = 9,
                    BluetoothMinorClass_WearableWristwatch = 1,
                    BluetoothMinorClass_WearablePager = 2,
                    BluetoothMinorClass_WearableJacket = 3,
                    BluetoothMinorClass_WearableHelmet = 4,
                    BluetoothMinorClass_WearableGlasses = 5,
                    BluetoothMinorClass_ToyRobot = 1,
                    BluetoothMinorClass_ToyVehicle = 2,
                    BluetoothMinorClass_ToyDoll = 3,
                    BluetoothMinorClass_ToyController = 4,
                    BluetoothMinorClass_ToyGame = 5,
                    BluetoothMinorClass_HealthBloodPressureMonitor = 1,
                    BluetoothMinorClass_HealthThermometer = 2,
                    BluetoothMinorClass_HealthWeighingScale = 3,
                    BluetoothMinorClass_HealthGlucoseMeter = 4,
                    BluetoothMinorClass_HealthPulseOximeter = 5,
                    BluetoothMinorClass_HealthHeartRateMonitor = 6,
                    BluetoothMinorClass_HealthHealthDataDisplay = 7,
                    BluetoothMinorClass_HealthStepCounter = 8,
                    BluetoothMinorClass_HealthBodyCompositionAnalyzer = 9,
                    BluetoothMinorClass_HealthPeakFlowMonitor = 10,
                    BluetoothMinorClass_HealthMedicationMonitor = 11,
                    BluetoothMinorClass_HealthKneeProsthesis = 12,
                    BluetoothMinorClass_HealthAnkleProsthesis = 13,
                    BluetoothMinorClass_HealthGenericHealthManager = 14,
                    BluetoothMinorClass_HealthPersonalMobilityDevice = 15
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMinorClass {
    BluetoothMinorClass_Uncategorized = 0,
    BluetoothMinorClass_ComputerDesktop = 1,
    BluetoothMinorClass_ComputerServer = 2,
    BluetoothMinorClass_ComputerLaptop = 3,
    BluetoothMinorClass_ComputerHandheld = 4,
    BluetoothMinorClass_ComputerPalmSize = 5,
    BluetoothMinorClass_ComputerWearable = 6,
    BluetoothMinorClass_ComputerTablet = 7,
    BluetoothMinorClass_PhoneCellular = 1,
    BluetoothMinorClass_PhoneCordless = 2,
    BluetoothMinorClass_PhoneSmartPhone = 3,
    BluetoothMinorClass_PhoneWired = 4,
    BluetoothMinorClass_PhoneIsdn = 5,
    BluetoothMinorClass_NetworkFullyAvailable = 0,
    BluetoothMinorClass_NetworkUsed01To17Percent = 8,
    BluetoothMinorClass_NetworkUsed17To33Percent = 16,
    BluetoothMinorClass_NetworkUsed33To50Percent = 24,
    BluetoothMinorClass_NetworkUsed50To67Percent = 32,
    BluetoothMinorClass_NetworkUsed67To83Percent = 40,
    BluetoothMinorClass_NetworkUsed83To99Percent = 48,
    BluetoothMinorClass_NetworkNoServiceAvailable = 56,
    BluetoothMinorClass_AudioVideoWearableHeadset = 1,
    BluetoothMinorClass_AudioVideoHandsFree = 2,
    BluetoothMinorClass_AudioVideoMicrophone = 4,
    BluetoothMinorClass_AudioVideoLoudspeaker = 5,
    BluetoothMinorClass_AudioVideoHeadphones = 6,
    BluetoothMinorClass_AudioVideoPortableAudio = 7,
    BluetoothMinorClass_AudioVideoCarAudio = 8,
    BluetoothMinorClass_AudioVideoSetTopBox = 9,
    BluetoothMinorClass_AudioVideoHifiAudioDevice = 10,
    BluetoothMinorClass_AudioVideoVcr = 11,
    BluetoothMinorClass_AudioVideoVideoCamera = 12,
    BluetoothMinorClass_AudioVideoCamcorder = 13,
    BluetoothMinorClass_AudioVideoVideoMonitor = 14,
    BluetoothMinorClass_AudioVideoVideoDisplayAndLoudspeaker = 15,
    BluetoothMinorClass_AudioVideoVideoConferencing = 16,
    BluetoothMinorClass_AudioVideoGamingOrToy = 18,
    BluetoothMinorClass_PeripheralJoystick = 1,
    BluetoothMinorClass_PeripheralGamepad = 2,
    BluetoothMinorClass_PeripheralRemoteControl = 3,
    BluetoothMinorClass_PeripheralSensing = 4,
    BluetoothMinorClass_PeripheralDigitizerTablet = 5,
    BluetoothMinorClass_PeripheralCardReader = 6,
    BluetoothMinorClass_PeripheralDigitalPen = 7,
    BluetoothMinorClass_PeripheralHandheldScanner = 8,
    BluetoothMinorClass_PeripheralHandheldGesture = 9,
    BluetoothMinorClass_WearableWristwatch = 1,
    BluetoothMinorClass_WearablePager = 2,
    BluetoothMinorClass_WearableJacket = 3,
    BluetoothMinorClass_WearableHelmet = 4,
    BluetoothMinorClass_WearableGlasses = 5,
    BluetoothMinorClass_ToyRobot = 1,
    BluetoothMinorClass_ToyVehicle = 2,
    BluetoothMinorClass_ToyDoll = 3,
    BluetoothMinorClass_ToyController = 4,
    BluetoothMinorClass_ToyGame = 5,
    BluetoothMinorClass_HealthBloodPressureMonitor = 1,
    BluetoothMinorClass_HealthThermometer = 2,
    BluetoothMinorClass_HealthWeighingScale = 3,
    BluetoothMinorClass_HealthGlucoseMeter = 4,
    BluetoothMinorClass_HealthPulseOximeter = 5,
    BluetoothMinorClass_HealthHeartRateMonitor = 6,
    BluetoothMinorClass_HealthHealthDataDisplay = 7,
    BluetoothMinorClass_HealthStepCounter = 8,
    BluetoothMinorClass_HealthBodyCompositionAnalyzer = 9,
    BluetoothMinorClass_HealthPeakFlowMonitor = 10,
    BluetoothMinorClass_HealthMedicationMonitor = 11,
    BluetoothMinorClass_HealthKneeProsthesis = 12,
    BluetoothMinorClass_HealthAnkleProsthesis = 13,
    BluetoothMinorClass_HealthGenericHealthManager = 14,
    BluetoothMinorClass_HealthPersonalMobilityDevice = 15
};
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define BluetoothMinorClass __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMinorClass
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMinorClass_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMinorClass __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMinorClass;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothServiceCapabilities_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothServiceCapabilities_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                enum BluetoothServiceCapabilities {
                    BluetoothServiceCapabilities_None = 0x0,
                    BluetoothServiceCapabilities_LimitedDiscoverableMode = 0x1,
                    BluetoothServiceCapabilities_PositioningService = 0x8,
                    BluetoothServiceCapabilities_NetworkingService = 0x10,
                    BluetoothServiceCapabilities_RenderingService = 0x20,
                    BluetoothServiceCapabilities_CapturingService = 0x40,
                    BluetoothServiceCapabilities_ObjectTransferService = 0x80,
                    BluetoothServiceCapabilities_AudioService = 0x100,
                    BluetoothServiceCapabilities_TelephoneService = 0x200,
                    BluetoothServiceCapabilities_InformationService = 0x400
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothServiceCapabilities {
    BluetoothServiceCapabilities_None = 0x0,
    BluetoothServiceCapabilities_LimitedDiscoverableMode = 0x1,
    BluetoothServiceCapabilities_PositioningService = 0x8,
    BluetoothServiceCapabilities_NetworkingService = 0x10,
    BluetoothServiceCapabilities_RenderingService = 0x20,
    BluetoothServiceCapabilities_CapturingService = 0x40,
    BluetoothServiceCapabilities_ObjectTransferService = 0x80,
    BluetoothServiceCapabilities_AudioService = 0x100,
    BluetoothServiceCapabilities_TelephoneService = 0x200,
    BluetoothServiceCapabilities_InformationService = 0x400
};
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define BluetoothServiceCapabilities __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothServiceCapabilities
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothServiceCapabilities_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothServiceCapabilities __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothServiceCapabilities;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothError_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothError_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                enum BluetoothError {
                    BluetoothError_Success = 0,
                    BluetoothError_RadioNotAvailable = 1,
                    BluetoothError_ResourceInUse = 2,
                    BluetoothError_DeviceNotConnected = 3,
                    BluetoothError_OtherError = 4,
                    BluetoothError_DisabledByPolicy = 5,
                    BluetoothError_NotSupported = 6,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
                    BluetoothError_DisabledByUser = 7,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
                    BluetoothError_ConsentRequired = 8,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
                    BluetoothError_TransportNotSupported = 9
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothError {
    BluetoothError_Success = 0,
    BluetoothError_RadioNotAvailable = 1,
    BluetoothError_ResourceInUse = 2,
    BluetoothError_DeviceNotConnected = 3,
    BluetoothError_OtherError = 4,
    BluetoothError_DisabledByPolicy = 5,
    BluetoothError_NotSupported = 6,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000
    BluetoothError_DisabledByUser = 7,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x20000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
    BluetoothError_ConsentRequired = 8,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
    BluetoothError_TransportNotSupported = 9
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */
};
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define BluetoothError __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothError
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothError_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothError __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothError;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter ABI::Windows::Devices::Bluetooth::IBluetoothAdapter
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapter;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter2 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter2 ABI::Windows::Devices::Bluetooth::IBluetoothAdapter2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapter2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter3_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter3 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter3 ABI::Windows::Devices::Bluetooth::IBluetoothAdapter3
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapter3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics ABI::Windows::Devices::Bluetooth::IBluetoothAdapterStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothAdapterStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice ABI::Windows::Devices::Bluetooth::IBluetoothClassOfDevice
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothClassOfDevice;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics ABI::Windows::Devices::Bluetooth::IBluetoothClassOfDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothClassOfDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice ABI::Windows::Devices::Bluetooth::IBluetoothDevice
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDevice;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice2 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice2 ABI::Windows::Devices::Bluetooth::IBluetoothDevice2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDevice2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice3_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice3 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice3 ABI::Windows::Devices::Bluetooth::IBluetoothDevice3
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDevice3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice4_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice4 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice4 ABI::Windows::Devices::Bluetooth::IBluetoothDevice4
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDevice4;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice5_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice5_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice5 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice5;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice5 ABI::Windows::Devices::Bluetooth::IBluetoothDevice5
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDevice5;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics ABI::Windows::Devices::Bluetooth::IBluetoothDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics2 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics2 ABI::Windows::Devices::Bluetooth::IBluetoothDeviceStatics2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDeviceStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId ABI::Windows::Devices::Bluetooth::IBluetoothDeviceId
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDeviceId;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics ABI::Windows::Devices::Bluetooth::IBluetoothDeviceIdStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothDeviceIdStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice ABI::Windows::Devices::Bluetooth::IBluetoothLEDevice
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothLEDevice;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice2 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice2 ABI::Windows::Devices::Bluetooth::IBluetoothLEDevice2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothLEDevice2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice3_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice3 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice3 ABI::Windows::Devices::Bluetooth::IBluetoothLEDevice3
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothLEDevice3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice4_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice4 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice4 ABI::Windows::Devices::Bluetooth::IBluetoothLEDevice4
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothLEDevice4;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice5_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice5_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice5 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice5;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice5 ABI::Windows::Devices::Bluetooth::IBluetoothLEDevice5
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothLEDevice5;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice6_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice6_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice6 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice6;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice6 ABI::Windows::Devices::Bluetooth::IBluetoothLEDevice6
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothLEDevice6;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics ABI::Windows::Devices::Bluetooth::IBluetoothLEDeviceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothLEDeviceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics2 __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics2 ABI::Windows::Devices::Bluetooth::IBluetoothLEDeviceStatics2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                interface IBluetoothLEDeviceStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothDevice* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::Streams::IBuffer* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Bluetooth::GenericAttributeProfile::GattDeviceService* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IBluetoothAdapter interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter, 0x7974f04c, 0x5f7a, 0x4a34, 0x92,0x25, 0xa8,0x55,0xf8,0x4b,0x1a,0x8b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("7974f04c-5f7a-4a34-9225-a855f84b1a8b")
                IBluetoothAdapter : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DeviceId(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_BluetoothAddress(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsClassicSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsLowEnergySupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsPeripheralRoleSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsCentralRoleSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsAdvertisementOffloadSupported(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetRadioAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Radios::Radio* > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter, 0x7974f04c, 0x5f7a, 0x4a34, 0x92,0x25, 0xa8,0x55,0xf8,0x4b,0x1a,0x8b)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        TrustLevel *trustLevel);

    /*** IBluetoothAdapter methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DeviceId)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_BluetoothAddress)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *get_IsClassicSupported)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsLowEnergySupported)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsPeripheralRoleSupported)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsCentralRoleSupported)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsAdvertisementOffloadSupported)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetRadioAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter *This,
        __FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio **operation);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothAdapter methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_DeviceId(This,value) (This)->lpVtbl->get_DeviceId(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_BluetoothAddress(This,value) (This)->lpVtbl->get_BluetoothAddress(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsClassicSupported(This,value) (This)->lpVtbl->get_IsClassicSupported(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsLowEnergySupported(This,value) (This)->lpVtbl->get_IsLowEnergySupported(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsPeripheralRoleSupported(This,value) (This)->lpVtbl->get_IsPeripheralRoleSupported(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsCentralRoleSupported(This,value) (This)->lpVtbl->get_IsCentralRoleSupported(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsAdvertisementOffloadSupported(This,value) (This)->lpVtbl->get_IsAdvertisementOffloadSupported(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRadioAsync(This,operation) (This)->lpVtbl->GetRadioAsync(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothAdapter methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_DeviceId(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,HSTRING *value) {
    return This->lpVtbl->get_DeviceId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_BluetoothAddress(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,UINT64 *value) {
    return This->lpVtbl->get_BluetoothAddress(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsClassicSupported(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,boolean *value) {
    return This->lpVtbl->get_IsClassicSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsLowEnergySupported(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,boolean *value) {
    return This->lpVtbl->get_IsLowEnergySupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsPeripheralRoleSupported(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,boolean *value) {
    return This->lpVtbl->get_IsPeripheralRoleSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsCentralRoleSupported(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,boolean *value) {
    return This->lpVtbl->get_IsCentralRoleSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsAdvertisementOffloadSupported(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,boolean *value) {
    return This->lpVtbl->get_IsAdvertisementOffloadSupported(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRadioAsync(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter* This,__FIAsyncOperation_1_Windows__CDevices__CRadios__CRadio **operation) {
    return This->lpVtbl->GetRadioAsync(This,operation);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothAdapter IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter
#define IBluetoothAdapterVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterVtbl
#define IBluetoothAdapter __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter
#define IBluetoothAdapter_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_QueryInterface
#define IBluetoothAdapter_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_AddRef
#define IBluetoothAdapter_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_Release
#define IBluetoothAdapter_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetIids
#define IBluetoothAdapter_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRuntimeClassName
#define IBluetoothAdapter_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetTrustLevel
#define IBluetoothAdapter_get_DeviceId __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_DeviceId
#define IBluetoothAdapter_get_BluetoothAddress __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_BluetoothAddress
#define IBluetoothAdapter_get_IsClassicSupported __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsClassicSupported
#define IBluetoothAdapter_get_IsLowEnergySupported __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsLowEnergySupported
#define IBluetoothAdapter_get_IsPeripheralRoleSupported __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsPeripheralRoleSupported
#define IBluetoothAdapter_get_IsCentralRoleSupported __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsCentralRoleSupported
#define IBluetoothAdapter_get_IsAdvertisementOffloadSupported __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_get_IsAdvertisementOffloadSupported
#define IBluetoothAdapter_GetRadioAsync __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_GetRadioAsync
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IBluetoothAdapterStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics, 0x8b02fb6a, 0xac4c, 0x4741, 0x86,0x61, 0x8e,0xab,0x7d,0x17,0xea,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("8b02fb6a-ac4c-4741-8661-8eab7d17ea9f")
                IBluetoothAdapterStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetDeviceSelector(
                        HSTRING *result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromIdAsync(
                        HSTRING id,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDefaultAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics, 0x8b02fb6a, 0xac4c, 0x4741, 0x86,0x61, 0x8e,0xab,0x7d,0x17,0xea,0x9f)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        TrustLevel *trustLevel);

    /*** IBluetoothAdapterStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDeviceSelector)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        HSTRING *result);

    HRESULT (STDMETHODCALLTYPE *FromIdAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        HSTRING id,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **operation);

    HRESULT (STDMETHODCALLTYPE *GetDefaultAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics *This,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **operation);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothAdapterStatics methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDeviceSelector(This,result) (This)->lpVtbl->GetDeviceSelector(This,result)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FromIdAsync(This,id,operation) (This)->lpVtbl->FromIdAsync(This,id,operation)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDefaultAsync(This,operation) (This)->lpVtbl->GetDefaultAsync(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothAdapterStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDeviceSelector(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,HSTRING *result) {
    return This->lpVtbl->GetDeviceSelector(This,result);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FromIdAsync(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,HSTRING id,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **operation) {
    return This->lpVtbl->FromIdAsync(This,id,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDefaultAsync(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics* This,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **operation) {
    return This->lpVtbl->GetDefaultAsync(This,operation);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothAdapterStatics IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics
#define IBluetoothAdapterStaticsVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStaticsVtbl
#define IBluetoothAdapterStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics
#define IBluetoothAdapterStatics_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_QueryInterface
#define IBluetoothAdapterStatics_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_AddRef
#define IBluetoothAdapterStatics_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_Release
#define IBluetoothAdapterStatics_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetIids
#define IBluetoothAdapterStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetRuntimeClassName
#define IBluetoothAdapterStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetTrustLevel
#define IBluetoothAdapterStatics_GetDeviceSelector __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDeviceSelector
#define IBluetoothAdapterStatics_FromIdAsync __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_FromIdAsync
#define IBluetoothAdapterStatics_GetDefaultAsync __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_GetDefaultAsync
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapterStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*
 * Class Windows.Devices.Bluetooth.BluetoothAdapter
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothAdapter_DEFINED
#define RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothAdapter_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothAdapter[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','A','d','a','p','t','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothAdapter[] = L"Windows.Devices.Bluetooth.BluetoothAdapter";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothAdapter[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','A','d','a','p','t','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothAdapter_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IBluetoothClassOfDevice interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice, 0xd640227e, 0xd7d7, 0x4661, 0x94,0x54, 0x65,0x03,0x9c,0xa1,0x7a,0x2b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("d640227e-d7d7-4661-9454-65039ca17a2b")
                IBluetoothClassOfDevice : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_RawValue(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MajorClass(
                        ABI::Windows::Devices::Bluetooth::BluetoothMajorClass *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MinorClass(
                        ABI::Windows::Devices::Bluetooth::BluetoothMinorClass *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ServiceCapabilities(
                        ABI::Windows::Devices::Bluetooth::BluetoothServiceCapabilities *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice, 0xd640227e, 0xd7d7, 0x4661, 0x94,0x54, 0x65,0x03,0x9c,0xa1,0x7a,0x2b)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice *This,
        TrustLevel *trustLevel);

    /*** IBluetoothClassOfDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *get_RawValue)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_MajorClass)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMajorClass *value);

    HRESULT (STDMETHODCALLTYPE *get_MinorClass)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMinorClass *value);

    HRESULT (STDMETHODCALLTYPE *get_ServiceCapabilities)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothServiceCapabilities *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothClassOfDevice methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_RawValue(This,value) (This)->lpVtbl->get_RawValue(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_MajorClass(This,value) (This)->lpVtbl->get_MajorClass(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_MinorClass(This,value) (This)->lpVtbl->get_MinorClass(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_ServiceCapabilities(This,value) (This)->lpVtbl->get_ServiceCapabilities(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothClassOfDevice methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_RawValue(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice* This,UINT32 *value) {
    return This->lpVtbl->get_RawValue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_MajorClass(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice* This,__x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMajorClass *value) {
    return This->lpVtbl->get_MajorClass(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_MinorClass(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice* This,__x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMinorClass *value) {
    return This->lpVtbl->get_MinorClass(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_ServiceCapabilities(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice* This,__x_ABI_CWindows_CDevices_CBluetooth_CBluetoothServiceCapabilities *value) {
    return This->lpVtbl->get_ServiceCapabilities(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothClassOfDevice IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice
#define IBluetoothClassOfDeviceVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceVtbl
#define IBluetoothClassOfDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice
#define IBluetoothClassOfDevice_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_QueryInterface
#define IBluetoothClassOfDevice_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_AddRef
#define IBluetoothClassOfDevice_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_Release
#define IBluetoothClassOfDevice_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_GetIids
#define IBluetoothClassOfDevice_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_GetRuntimeClassName
#define IBluetoothClassOfDevice_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_GetTrustLevel
#define IBluetoothClassOfDevice_get_RawValue __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_RawValue
#define IBluetoothClassOfDevice_get_MajorClass __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_MajorClass
#define IBluetoothClassOfDevice_get_MinorClass __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_MinorClass
#define IBluetoothClassOfDevice_get_ServiceCapabilities __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_get_ServiceCapabilities
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBluetoothClassOfDeviceStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics, 0xe46135bd, 0x0fa2, 0x416c, 0x91,0xb4, 0xc1,0xe4,0x8c,0xa0,0x61,0xc1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("e46135bd-0fa2-416c-91b4-c1e48ca061c1")
                IBluetoothClassOfDeviceStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE FromRawValue(
                        UINT32 raw_value,
                        ABI::Windows::Devices::Bluetooth::IBluetoothClassOfDevice **cod) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromParts(
                        ABI::Windows::Devices::Bluetooth::BluetoothMajorClass major,
                        ABI::Windows::Devices::Bluetooth::BluetoothMinorClass minor,
                        ABI::Windows::Devices::Bluetooth::BluetoothServiceCapabilities capabilities,
                        ABI::Windows::Devices::Bluetooth::IBluetoothClassOfDevice **cod) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics, 0xe46135bd, 0x0fa2, 0x416c, 0x91,0xb4, 0xc1,0xe4,0x8c,0xa0,0x61,0xc1)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics *This,
        TrustLevel *trustLevel);

    /*** IBluetoothClassOfDeviceStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *FromRawValue)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics *This,
        UINT32 raw_value,
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice **cod);

    HRESULT (STDMETHODCALLTYPE *FromParts)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMajorClass major,
        __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMinorClass minor,
        __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothServiceCapabilities capabilities,
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice **cod);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothClassOfDeviceStatics methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_FromRawValue(This,raw_value,cod) (This)->lpVtbl->FromRawValue(This,raw_value,cod)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_FromParts(This,major,minor,capabilities,cod) (This)->lpVtbl->FromParts(This,major,minor,capabilities,cod)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothClassOfDeviceStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_FromRawValue(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics* This,UINT32 raw_value,__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice **cod) {
    return This->lpVtbl->FromRawValue(This,raw_value,cod);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_FromParts(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics* This,__x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMajorClass major,__x_ABI_CWindows_CDevices_CBluetooth_CBluetoothMinorClass minor,__x_ABI_CWindows_CDevices_CBluetooth_CBluetoothServiceCapabilities capabilities,__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice **cod) {
    return This->lpVtbl->FromParts(This,major,minor,capabilities,cod);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothClassOfDeviceStatics IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics
#define IBluetoothClassOfDeviceStaticsVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStaticsVtbl
#define IBluetoothClassOfDeviceStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics
#define IBluetoothClassOfDeviceStatics_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_QueryInterface
#define IBluetoothClassOfDeviceStatics_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_AddRef
#define IBluetoothClassOfDeviceStatics_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_Release
#define IBluetoothClassOfDeviceStatics_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_GetIids
#define IBluetoothClassOfDeviceStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_GetRuntimeClassName
#define IBluetoothClassOfDeviceStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_GetTrustLevel
#define IBluetoothClassOfDeviceStatics_FromRawValue __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_FromRawValue
#define IBluetoothClassOfDeviceStatics_FromParts __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_FromParts
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDeviceStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Bluetooth.BluetoothClassOfDevice
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothClassOfDevice_DEFINED
#define RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothClassOfDevice_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothClassOfDevice[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','C','l','a','s','s','O','f','D','e','v','i','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothClassOfDevice[] = L"Windows.Devices.Bluetooth.BluetoothClassOfDevice";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothClassOfDevice[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','C','l','a','s','s','O','f','D','e','v','i','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothClassOfDevice_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBluetoothDevice interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice, 0x2335b156, 0x90d2, 0x4a04, 0xae,0xf5, 0x0e,0x20,0xb9,0xe6,0xb7,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("2335b156-90d2-4a04-aef5-0e20b9e6b707")
                IBluetoothDevice : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DeviceId(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_HostName(
                        ABI::Windows::Networking::IHostName **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Name(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ClassOfDevice(
                        ABI::Windows::Devices::Bluetooth::IBluetoothClassOfDevice **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SdpRecords(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::Streams::IBuffer* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_RfcommServices(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ConnectionStatus(
                        ABI::Windows::Devices::Bluetooth::BluetoothConnectionStatus *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_BluetoothAddress(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_NameChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_NameChanged(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_SdpRecordsChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_SdpRecordsChanged(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_ConnectionStatusChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_ConnectionStatusChanged(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice, 0x2335b156, 0x90d2, 0x4a04, 0xae,0xf5, 0x0e,0x20,0xb9,0xe6,0xb7,0x07)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        TrustLevel *trustLevel);

    /*** IBluetoothDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DeviceId)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_HostName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        __x_ABI_CWindows_CNetworking_CIHostName **value);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_ClassOfDevice)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice **value);

    HRESULT (STDMETHODCALLTYPE *get_SdpRecords)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer **value);

    HRESULT (STDMETHODCALLTYPE *get_RfcommServices)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService **value);

    HRESULT (STDMETHODCALLTYPE *get_ConnectionStatus)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothConnectionStatus *value);

    HRESULT (STDMETHODCALLTYPE *get_BluetoothAddress)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *add_NameChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_NameChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_SdpRecordsChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_SdpRecordsChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_ConnectionStatusChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_ConnectionStatusChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothDevice methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_DeviceId(This,value) (This)->lpVtbl->get_DeviceId(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_HostName(This,value) (This)->lpVtbl->get_HostName(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_Name(This,value) (This)->lpVtbl->get_Name(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_ClassOfDevice(This,value) (This)->lpVtbl->get_ClassOfDevice(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_SdpRecords(This,value) (This)->lpVtbl->get_SdpRecords(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_RfcommServices(This,value) (This)->lpVtbl->get_RfcommServices(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_ConnectionStatus(This,value) (This)->lpVtbl->get_ConnectionStatus(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_BluetoothAddress(This,value) (This)->lpVtbl->get_BluetoothAddress(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_add_NameChanged(This,handler,token) (This)->lpVtbl->add_NameChanged(This,handler,token)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_remove_NameChanged(This,token) (This)->lpVtbl->remove_NameChanged(This,token)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_add_SdpRecordsChanged(This,handler,token) (This)->lpVtbl->add_SdpRecordsChanged(This,handler,token)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_remove_SdpRecordsChanged(This,token) (This)->lpVtbl->remove_SdpRecordsChanged(This,token)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_add_ConnectionStatusChanged(This,handler,token) (This)->lpVtbl->add_ConnectionStatusChanged(This,handler,token)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_remove_ConnectionStatusChanged(This,token) (This)->lpVtbl->remove_ConnectionStatusChanged(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothDevice methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_DeviceId(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,HSTRING *value) {
    return This->lpVtbl->get_DeviceId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_HostName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,__x_ABI_CWindows_CNetworking_CIHostName **value) {
    return This->lpVtbl->get_HostName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_Name(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,HSTRING *value) {
    return This->lpVtbl->get_Name(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_ClassOfDevice(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothClassOfDevice **value) {
    return This->lpVtbl->get_ClassOfDevice(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_SdpRecords(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer **value) {
    return This->lpVtbl->get_SdpRecords(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_RfcommServices(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService **value) {
    return This->lpVtbl->get_RfcommServices(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_ConnectionStatus(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,__x_ABI_CWindows_CDevices_CBluetooth_CBluetoothConnectionStatus *value) {
    return This->lpVtbl->get_ConnectionStatus(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_BluetoothAddress(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,UINT64 *value) {
    return This->lpVtbl->get_BluetoothAddress(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_add_NameChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_NameChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_remove_NameChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_NameChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_add_SdpRecordsChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_SdpRecordsChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_remove_SdpRecordsChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_SdpRecordsChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_add_ConnectionStatusChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_ConnectionStatusChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_remove_ConnectionStatusChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_ConnectionStatusChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothDevice IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice
#define IBluetoothDeviceVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceVtbl
#define IBluetoothDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice
#define IBluetoothDevice_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_QueryInterface
#define IBluetoothDevice_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_AddRef
#define IBluetoothDevice_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_Release
#define IBluetoothDevice_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_GetIids
#define IBluetoothDevice_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_GetRuntimeClassName
#define IBluetoothDevice_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_GetTrustLevel
#define IBluetoothDevice_get_DeviceId __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_DeviceId
#define IBluetoothDevice_get_HostName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_HostName
#define IBluetoothDevice_get_Name __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_Name
#define IBluetoothDevice_get_ClassOfDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_ClassOfDevice
#define IBluetoothDevice_get_SdpRecords __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_SdpRecords
#define IBluetoothDevice_get_RfcommServices __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_RfcommServices
#define IBluetoothDevice_get_ConnectionStatus __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_ConnectionStatus
#define IBluetoothDevice_get_BluetoothAddress __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_get_BluetoothAddress
#define IBluetoothDevice_add_NameChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_add_NameChanged
#define IBluetoothDevice_remove_NameChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_remove_NameChanged
#define IBluetoothDevice_add_SdpRecordsChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_add_SdpRecordsChanged
#define IBluetoothDevice_remove_SdpRecordsChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_remove_SdpRecordsChanged
#define IBluetoothDevice_add_ConnectionStatusChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_add_ConnectionStatusChanged
#define IBluetoothDevice_remove_ConnectionStatusChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_remove_ConnectionStatusChanged
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBluetoothDeviceStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics, 0x0991df51, 0x57db, 0x4725, 0xbb,0xd7, 0x84,0xf6,0x43,0x27,0xec,0x2c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("0991df51-57db-4725-bbd7-84f64327ec2c")
                IBluetoothDeviceStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE FromIdAsync(
                        HSTRING id,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromHostNameAsync(
                        ABI::Windows::Networking::IHostName *name,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromBluetoothAddressAsync(
                        UINT64 address,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDeviceSelector(
                        HSTRING *selector) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics, 0x0991df51, 0x57db, 0x4725, 0xbb,0xd7, 0x84,0xf6,0x43,0x27,0xec,0x2c)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics *This,
        TrustLevel *trustLevel);

    /*** IBluetoothDeviceStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *FromIdAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics *This,
        HSTRING id,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice **operation);

    HRESULT (STDMETHODCALLTYPE *FromHostNameAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics *This,
        __x_ABI_CWindows_CNetworking_CIHostName *name,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice **operation);

    HRESULT (STDMETHODCALLTYPE *FromBluetoothAddressAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics *This,
        UINT64 address,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice **operation);

    HRESULT (STDMETHODCALLTYPE *GetDeviceSelector)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics *This,
        HSTRING *selector);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothDeviceStatics methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FromIdAsync(This,id,operation) (This)->lpVtbl->FromIdAsync(This,id,operation)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FromHostNameAsync(This,name,operation) (This)->lpVtbl->FromHostNameAsync(This,name,operation)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FromBluetoothAddressAsync(This,address,operation) (This)->lpVtbl->FromBluetoothAddressAsync(This,address,operation)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetDeviceSelector(This,selector) (This)->lpVtbl->GetDeviceSelector(This,selector)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothDeviceStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FromIdAsync(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics* This,HSTRING id,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice **operation) {
    return This->lpVtbl->FromIdAsync(This,id,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FromHostNameAsync(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics* This,__x_ABI_CWindows_CNetworking_CIHostName *name,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice **operation) {
    return This->lpVtbl->FromHostNameAsync(This,name,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FromBluetoothAddressAsync(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics* This,UINT64 address,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice **operation) {
    return This->lpVtbl->FromBluetoothAddressAsync(This,address,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetDeviceSelector(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics* This,HSTRING *selector) {
    return This->lpVtbl->GetDeviceSelector(This,selector);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothDeviceStatics IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics
#define IBluetoothDeviceStaticsVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStaticsVtbl
#define IBluetoothDeviceStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics
#define IBluetoothDeviceStatics_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_QueryInterface
#define IBluetoothDeviceStatics_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_AddRef
#define IBluetoothDeviceStatics_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_Release
#define IBluetoothDeviceStatics_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetIids
#define IBluetoothDeviceStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetRuntimeClassName
#define IBluetoothDeviceStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetTrustLevel
#define IBluetoothDeviceStatics_FromIdAsync __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FromIdAsync
#define IBluetoothDeviceStatics_FromHostNameAsync __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FromHostNameAsync
#define IBluetoothDeviceStatics_FromBluetoothAddressAsync __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_FromBluetoothAddressAsync
#define IBluetoothDeviceStatics_GetDeviceSelector __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_GetDeviceSelector
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Bluetooth.BluetoothDevice
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothDevice_DEFINED
#define RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothDevice_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothDevice[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','D','e','v','i','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothDevice[] = L"Windows.Devices.Bluetooth.BluetoothDevice";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothDevice[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','D','e','v','i','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothDevice_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBluetoothDeviceId interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId, 0xc17949af, 0x57c1, 0x4642, 0xbc,0xce, 0xe6,0xc0,0x6b,0x20,0xae,0x76);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("c17949af-57c1-4642-bcce-e6c06b20ae76")
                IBluetoothDeviceId : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Id(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsClassicDevice(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsLowEnergyDevice(
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId, 0xc17949af, 0x57c1, 0x4642, 0xbc,0xce, 0xe6,0xc0,0x6b,0x20,0xae,0x76)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId *This,
        TrustLevel *trustLevel);

    /*** IBluetoothDeviceId methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_IsClassicDevice)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsLowEnergyDevice)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothDeviceId methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_get_IsClassicDevice(This,value) (This)->lpVtbl->get_IsClassicDevice(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_get_IsLowEnergyDevice(This,value) (This)->lpVtbl->get_IsLowEnergyDevice(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothDeviceId methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_get_Id(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId* This,HSTRING *value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_get_IsClassicDevice(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId* This,boolean *value) {
    return This->lpVtbl->get_IsClassicDevice(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_get_IsLowEnergyDevice(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId* This,boolean *value) {
    return This->lpVtbl->get_IsLowEnergyDevice(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothDeviceId IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId
#define IBluetoothDeviceIdVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdVtbl
#define IBluetoothDeviceId __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId
#define IBluetoothDeviceId_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_QueryInterface
#define IBluetoothDeviceId_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_AddRef
#define IBluetoothDeviceId_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_Release
#define IBluetoothDeviceId_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_GetIids
#define IBluetoothDeviceId_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_GetRuntimeClassName
#define IBluetoothDeviceId_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_GetTrustLevel
#define IBluetoothDeviceId_get_Id __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_get_Id
#define IBluetoothDeviceId_get_IsClassicDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_get_IsClassicDevice
#define IBluetoothDeviceId_get_IsLowEnergyDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_get_IsLowEnergyDevice
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IBluetoothDeviceIdStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics, 0xa7884e67, 0x3efb, 0x4f31, 0xbb,0xc2, 0x81,0x0e,0x09,0x97,0x74,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("a7884e67-3efb-4f31-bbc2-810e09977404")
                IBluetoothDeviceIdStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE FromId(
                        HSTRING id,
                        ABI::Windows::Devices::Bluetooth::IBluetoothDeviceId **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics, 0xa7884e67, 0x3efb, 0x4f31, 0xbb,0xc2, 0x81,0x0e,0x09,0x97,0x74,0x04)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics *This,
        TrustLevel *trustLevel);

    /*** IBluetoothDeviceIdStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *FromId)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics *This,
        HSTRING id,
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId **result);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothDeviceIdStatics methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_FromId(This,id,result) (This)->lpVtbl->FromId(This,id,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothDeviceIdStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_FromId(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics* This,HSTRING id,__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceId **result) {
    return This->lpVtbl->FromId(This,id,result);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothDeviceIdStatics IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics
#define IBluetoothDeviceIdStaticsVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStaticsVtbl
#define IBluetoothDeviceIdStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics
#define IBluetoothDeviceIdStatics_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_QueryInterface
#define IBluetoothDeviceIdStatics_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_AddRef
#define IBluetoothDeviceIdStatics_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_Release
#define IBluetoothDeviceIdStatics_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_GetIids
#define IBluetoothDeviceIdStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_GetRuntimeClassName
#define IBluetoothDeviceIdStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_GetTrustLevel
#define IBluetoothDeviceIdStatics_FromId __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_FromId
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDeviceIdStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*
 * Class Windows.Devices.Bluetooth.BluetoothDeviceId
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothDeviceId_DEFINED
#define RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothDeviceId_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothDeviceId[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','D','e','v','i','c','e','I','d',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothDeviceId[] = L"Windows.Devices.Bluetooth.BluetoothDeviceId";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothDeviceId[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','D','e','v','i','c','e','I','d',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothDeviceId_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IBluetoothLEDevice interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice, 0xb5ee2f7b, 0x4ad8, 0x4642, 0xac,0x48, 0x80,0xa0,0xb5,0x00,0xe8,0x87);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("b5ee2f7b-4ad8-4642-ac48-80a0b500e887")
                IBluetoothLEDevice : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DeviceId(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Name(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_GattServices(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Bluetooth::GenericAttributeProfile::GattDeviceService* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ConnectionStatus(
                        ABI::Windows::Devices::Bluetooth::BluetoothConnectionStatus *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_BluetoothAddress(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetGattService(
                        GUID uuid,
                        ABI::Windows::Devices::Bluetooth::GenericAttributeProfile::IGattDeviceService **service) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_NameChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_NameChanged(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_GattServicesChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_GattServicesChanged(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_ConnectionStatusChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_ConnectionStatusChanged(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice, 0xb5ee2f7b, 0x4ad8, 0x4642, 0xac,0x48, 0x80,0xa0,0xb5,0x00,0xe8,0x87)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        TrustLevel *trustLevel);

    /*** IBluetoothLEDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DeviceId)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_GattServices)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService **value);

    HRESULT (STDMETHODCALLTYPE *get_ConnectionStatus)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothConnectionStatus *value);

    HRESULT (STDMETHODCALLTYPE *get_BluetoothAddress)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *GetGattService)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        GUID uuid,
        __x_ABI_CWindows_CDevices_CBluetooth_CGenericAttributeProfile_CIGattDeviceService **service);

    HRESULT (STDMETHODCALLTYPE *add_NameChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_NameChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_GattServicesChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_GattServicesChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_ConnectionStatusChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_ConnectionStatusChanged)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothLEDevice methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_DeviceId(This,value) (This)->lpVtbl->get_DeviceId(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_Name(This,value) (This)->lpVtbl->get_Name(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_GattServices(This,value) (This)->lpVtbl->get_GattServices(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_ConnectionStatus(This,value) (This)->lpVtbl->get_ConnectionStatus(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_BluetoothAddress(This,value) (This)->lpVtbl->get_BluetoothAddress(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetGattService(This,uuid,service) (This)->lpVtbl->GetGattService(This,uuid,service)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_add_NameChanged(This,handler,token) (This)->lpVtbl->add_NameChanged(This,handler,token)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_remove_NameChanged(This,token) (This)->lpVtbl->remove_NameChanged(This,token)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_add_GattServicesChanged(This,handler,token) (This)->lpVtbl->add_GattServicesChanged(This,handler,token)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_remove_GattServicesChanged(This,token) (This)->lpVtbl->remove_GattServicesChanged(This,token)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_add_ConnectionStatusChanged(This,handler,token) (This)->lpVtbl->add_ConnectionStatusChanged(This,handler,token)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_remove_ConnectionStatusChanged(This,token) (This)->lpVtbl->remove_ConnectionStatusChanged(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothLEDevice methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_DeviceId(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,HSTRING *value) {
    return This->lpVtbl->get_DeviceId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_Name(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,HSTRING *value) {
    return This->lpVtbl->get_Name(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_GattServices(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService **value) {
    return This->lpVtbl->get_GattServices(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_ConnectionStatus(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,__x_ABI_CWindows_CDevices_CBluetooth_CBluetoothConnectionStatus *value) {
    return This->lpVtbl->get_ConnectionStatus(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_BluetoothAddress(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,UINT64 *value) {
    return This->lpVtbl->get_BluetoothAddress(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetGattService(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,GUID uuid,__x_ABI_CWindows_CDevices_CBluetooth_CGenericAttributeProfile_CIGattDeviceService **service) {
    return This->lpVtbl->GetGattService(This,uuid,service);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_add_NameChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_NameChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_remove_NameChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_NameChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_add_GattServicesChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_GattServicesChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_remove_GattServicesChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_GattServicesChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_add_ConnectionStatusChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_ConnectionStatusChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_remove_ConnectionStatusChanged(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_ConnectionStatusChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothLEDevice IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice
#define IBluetoothLEDeviceVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceVtbl
#define IBluetoothLEDevice __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice
#define IBluetoothLEDevice_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_QueryInterface
#define IBluetoothLEDevice_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_AddRef
#define IBluetoothLEDevice_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_Release
#define IBluetoothLEDevice_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetIids
#define IBluetoothLEDevice_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetRuntimeClassName
#define IBluetoothLEDevice_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetTrustLevel
#define IBluetoothLEDevice_get_DeviceId __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_DeviceId
#define IBluetoothLEDevice_get_Name __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_Name
#define IBluetoothLEDevice_get_GattServices __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_GattServices
#define IBluetoothLEDevice_get_ConnectionStatus __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_ConnectionStatus
#define IBluetoothLEDevice_get_BluetoothAddress __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_get_BluetoothAddress
#define IBluetoothLEDevice_GetGattService __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_GetGattService
#define IBluetoothLEDevice_add_NameChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_add_NameChanged
#define IBluetoothLEDevice_remove_NameChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_remove_NameChanged
#define IBluetoothLEDevice_add_GattServicesChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_add_GattServicesChanged
#define IBluetoothLEDevice_remove_GattServicesChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_remove_GattServicesChanged
#define IBluetoothLEDevice_add_ConnectionStatusChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_add_ConnectionStatusChanged
#define IBluetoothLEDevice_remove_ConnectionStatusChanged __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_remove_ConnectionStatusChanged
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBluetoothLEDeviceStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics, 0xc8cf1a19, 0xf0b6, 0x4bf0, 0x86,0x89, 0x41,0x30,0x3d,0xe2,0xd9,0xf4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                MIDL_INTERFACE("c8cf1a19-f0b6-4bf0-8689-41303de2d9f4")
                IBluetoothLEDeviceStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE FromIdAsync(
                        HSTRING id,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > **async) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FromBluetoothAddressAsync(
                        UINT64 addr,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > **async) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDeviceSelector(
                        HSTRING *result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics, 0xc8cf1a19, 0xf0b6, 0x4bf0, 0x86,0x89, 0x41,0x30,0x3d,0xe2,0xd9,0xf4)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics *This,
        TrustLevel *trustLevel);

    /*** IBluetoothLEDeviceStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *FromIdAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics *This,
        HSTRING id,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice **async);

    HRESULT (STDMETHODCALLTYPE *FromBluetoothAddressAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics *This,
        UINT64 addr,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice **async);

    HRESULT (STDMETHODCALLTYPE *GetDeviceSelector)(
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics *This,
        HSTRING *result);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBluetoothLEDeviceStatics methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_FromIdAsync(This,id,async) (This)->lpVtbl->FromIdAsync(This,id,async)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_FromBluetoothAddressAsync(This,addr,async) (This)->lpVtbl->FromBluetoothAddressAsync(This,addr,async)
#define __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetDeviceSelector(This,result) (This)->lpVtbl->GetDeviceSelector(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_Release(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBluetoothLEDeviceStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_FromIdAsync(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics* This,HSTRING id,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice **async) {
    return This->lpVtbl->FromIdAsync(This,id,async);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_FromBluetoothAddressAsync(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics* This,UINT64 addr,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice **async) {
    return This->lpVtbl->FromBluetoothAddressAsync(This,addr,async);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetDeviceSelector(__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics* This,HSTRING *result) {
    return This->lpVtbl->GetDeviceSelector(This,result);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define IID_IBluetoothLEDeviceStatics IID___x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics
#define IBluetoothLEDeviceStaticsVtbl __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStaticsVtbl
#define IBluetoothLEDeviceStatics __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics
#define IBluetoothLEDeviceStatics_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_QueryInterface
#define IBluetoothLEDeviceStatics_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_AddRef
#define IBluetoothLEDeviceStatics_Release __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_Release
#define IBluetoothLEDeviceStatics_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetIids
#define IBluetoothLEDeviceStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetRuntimeClassName
#define IBluetoothLEDeviceStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetTrustLevel
#define IBluetoothLEDeviceStatics_FromIdAsync __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_FromIdAsync
#define IBluetoothLEDeviceStatics_FromBluetoothAddressAsync __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_FromBluetoothAddressAsync
#define IBluetoothLEDeviceStatics_GetDeviceSelector __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_GetDeviceSelector
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDeviceStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Bluetooth.BluetoothLEDevice
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothLEDevice_DEFINED
#define RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothLEDevice_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothLEDevice[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','L','E','D','e','v','i','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothLEDevice[] = L"Windows.Devices.Bluetooth.BluetoothLEDevice";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_BluetoothLEDevice[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','B','l','u','e','t','o','o','t','h','L','E','D','e','v','i','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Bluetooth_BluetoothLEDevice_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter, 0x10a10a88, 0x90e0, 0x511a, 0x9a,0x08, 0xd7,0x5f,0xeb,0x52,0xa1,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("10a10a88-90e0-511a-9a08-d75feb52a19f")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::BluetoothAdapter*, ABI::Windows::Devices::Bluetooth::IBluetoothAdapter* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter, 0x10a10a88, 0x90e0, 0x511a, 0x9a,0x08, 0xd7,0x5f,0xeb,0x52,0xa1,0x9f)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_BluetoothAdapter IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter
#define IAsyncOperationCompletedHandler_BluetoothAdapterVtbl __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl
#define IAsyncOperationCompletedHandler_BluetoothAdapter __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter
#define IAsyncOperationCompletedHandler_BluetoothAdapter_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface
#define IAsyncOperationCompletedHandler_BluetoothAdapter_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef
#define IAsyncOperationCompletedHandler_BluetoothAdapter_Release __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release
#define IAsyncOperationCompletedHandler_BluetoothAdapter_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter, 0x46fce70c, 0x6c07, 0x5a3a, 0xb7,0x75, 0x26,0xf9,0x94,0x02,0x55,0x3f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("46fce70c-6c07-5a3a-b775-26f99402553f")
            IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::BluetoothAdapter*, ABI::Windows::Devices::Bluetooth::IBluetoothAdapter* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter, 0x46fce70c, 0x6c07, 0x5a3a, 0xb7,0x75, 0x26,0xf9,0x94,0x02,0x55,0x3f)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl;

interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter {
    CONST_VTBL __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetIids(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetTrustLevel(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothAdapter* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_put_Completed(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_get_Completed(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothAdapter **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetResults(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter* This,__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothAdapter **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_BluetoothAdapter IID___FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter
#define IAsyncOperation_BluetoothAdapterVtbl __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapterVtbl
#define IAsyncOperation_BluetoothAdapter __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter
#define IAsyncOperation_BluetoothAdapter_QueryInterface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_QueryInterface
#define IAsyncOperation_BluetoothAdapter_AddRef __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_AddRef
#define IAsyncOperation_BluetoothAdapter_Release __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_Release
#define IAsyncOperation_BluetoothAdapter_GetIids __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetIids
#define IAsyncOperation_BluetoothAdapter_GetRuntimeClassName __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetRuntimeClassName
#define IAsyncOperation_BluetoothAdapter_GetTrustLevel __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetTrustLevel
#define IAsyncOperation_BluetoothAdapter_put_Completed __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_put_Completed
#define IAsyncOperation_BluetoothAdapter_get_Completed __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_get_Completed
#define IAsyncOperation_BluetoothAdapter_GetResults __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothAdapter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable, 0xdb56ce1c, 0x5e9f, 0x5138, 0x92,0x27, 0xb1,0xa6,0x6d,0x60,0xbc,0x1b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("db56ce1c-5e9f-5138-9227-b1a66d60bc1b")
            ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::BluetoothDevice*, ABI::Windows::Devices::Bluetooth::IBluetoothDevice* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable, 0xdb56ce1c, 0x5e9f, 0x5138, 0x92,0x27, 0xb1,0xa6,0x6d,0x60,0xbc,0x1b)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_Release(__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable* This,__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_BluetoothDevice_IInspectable IID___FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable
#define ITypedEventHandler_BluetoothDevice_IInspectableVtbl __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectableVtbl
#define ITypedEventHandler_BluetoothDevice_IInspectable __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable
#define ITypedEventHandler_BluetoothDevice_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_QueryInterface
#define ITypedEventHandler_BluetoothDevice_IInspectable_AddRef __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_AddRef
#define ITypedEventHandler_BluetoothDevice_IInspectable_Release __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_Release
#define ITypedEventHandler_BluetoothDevice_IInspectable_Invoke __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothDevice_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice, 0xb2e8cdd1, 0x66aa, 0x5892, 0x85,0xa3, 0x8f,0x0b,0x16,0x5e,0x43,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("b2e8cdd1-66aa-5892-85a3-8f0b165e43fc")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::BluetoothDevice*, ABI::Windows::Devices::Bluetooth::IBluetoothDevice* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice, 0xb2e8cdd1, 0x66aa, 0x5892, 0x85,0xa3, 0x8f,0x0b,0x16,0x5e,0x43,0xfc)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDeviceVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_Release(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_BluetoothDevice IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice
#define IAsyncOperationCompletedHandler_BluetoothDeviceVtbl __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDeviceVtbl
#define IAsyncOperationCompletedHandler_BluetoothDevice __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice
#define IAsyncOperationCompletedHandler_BluetoothDevice_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_QueryInterface
#define IAsyncOperationCompletedHandler_BluetoothDevice_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_AddRef
#define IAsyncOperationCompletedHandler_BluetoothDevice_Release __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_Release
#define IAsyncOperationCompletedHandler_BluetoothDevice_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice, 0xb58d8d19, 0x44bd, 0x5ac0, 0xa0,0xd6, 0x1b,0x50,0x80,0x0f,0x31,0x81);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("b58d8d19-44bd-5ac0-a0d6-1b50800f3181")
            IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::BluetoothDevice*, ABI::Windows::Devices::Bluetooth::IBluetoothDevice* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice, 0xb58d8d19, 0x44bd, 0x5ac0, 0xa0,0xd6, 0x1b,0x50,0x80,0x0f,0x31,0x81)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDeviceVtbl;

interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice {
    CONST_VTBL __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_QueryInterface(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_AddRef(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_Release(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetIids(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetTrustLevel(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothDevice* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_put_Completed(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_get_Completed(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothDevice **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetResults(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice* This,__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothDevice **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_BluetoothDevice IID___FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice
#define IAsyncOperation_BluetoothDeviceVtbl __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDeviceVtbl
#define IAsyncOperation_BluetoothDevice __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice
#define IAsyncOperation_BluetoothDevice_QueryInterface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_QueryInterface
#define IAsyncOperation_BluetoothDevice_AddRef __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_AddRef
#define IAsyncOperation_BluetoothDevice_Release __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_Release
#define IAsyncOperation_BluetoothDevice_GetIids __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetIids
#define IAsyncOperation_BluetoothDevice_GetRuntimeClassName __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetRuntimeClassName
#define IAsyncOperation_BluetoothDevice_GetTrustLevel __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetTrustLevel
#define IAsyncOperation_BluetoothDevice_put_Completed __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_put_Completed
#define IAsyncOperation_BluetoothDevice_get_Completed __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_get_Completed
#define IAsyncOperation_BluetoothDevice_GetResults __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothDevice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice, 0x9156b79f, 0xc54a, 0x5277, 0x8f,0x8b, 0xd2,0xcc,0x43,0xc7,0xe0,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("9156b79f-c54a-5277-8f8b-d2cc43c7e004")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*, ABI::Windows::Devices::Bluetooth::IBluetoothLEDevice* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice, 0x9156b79f, 0xc54a, 0x5277, 0x8f,0x8b, 0xd2,0xcc,0x43,0xc7,0xe0,0x04)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDeviceVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_Release(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_BluetoothLEDevice IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice
#define IAsyncOperationCompletedHandler_BluetoothLEDeviceVtbl __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDeviceVtbl
#define IAsyncOperationCompletedHandler_BluetoothLEDevice __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice
#define IAsyncOperationCompletedHandler_BluetoothLEDevice_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_QueryInterface
#define IAsyncOperationCompletedHandler_BluetoothLEDevice_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_AddRef
#define IAsyncOperationCompletedHandler_BluetoothLEDevice_Release __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_Release
#define IAsyncOperationCompletedHandler_BluetoothLEDevice_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice, 0x375f9d67, 0x74a2, 0x5f91, 0xa1,0x1d, 0x16,0x90,0x93,0x71,0x8d,0x41);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("375f9d67-74a2-5f91-a11d-169093718d41")
            IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*, ABI::Windows::Devices::Bluetooth::IBluetoothLEDevice* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice, 0x375f9d67, 0x74a2, 0x5f91, 0xa1,0x1d, 0x16,0x90,0x93,0x71,0x8d,0x41)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDeviceVtbl;

interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice {
    CONST_VTBL __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_QueryInterface(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_AddRef(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_Release(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetIids(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetTrustLevel(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_put_Completed(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_get_Completed(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetResults(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice* This,__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_BluetoothLEDevice IID___FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice
#define IAsyncOperation_BluetoothLEDeviceVtbl __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDeviceVtbl
#define IAsyncOperation_BluetoothLEDevice __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice
#define IAsyncOperation_BluetoothLEDevice_QueryInterface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_QueryInterface
#define IAsyncOperation_BluetoothLEDevice_AddRef __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_AddRef
#define IAsyncOperation_BluetoothLEDevice_Release __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_Release
#define IAsyncOperation_BluetoothLEDevice_GetIids __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetIids
#define IAsyncOperation_BluetoothLEDevice_GetRuntimeClassName __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetRuntimeClassName
#define IAsyncOperation_BluetoothLEDevice_GetTrustLevel __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetTrustLevel
#define IAsyncOperation_BluetoothLEDevice_put_Completed __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_put_Completed
#define IAsyncOperation_BluetoothLEDevice_get_Completed __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_get_Completed
#define IAsyncOperation_BluetoothLEDevice_GetResults __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CBluetoothLEDevice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable, 0xa90661e2, 0x372e, 0x5d1e, 0xbb,0xbb, 0xb8,0xa2,0xce,0x0e,0x7c,0x4d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("a90661e2-372e-5d1e-bbbb-b8a2ce0e7c4d")
            ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*, ABI::Windows::Devices::Bluetooth::IBluetoothLEDevice* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable, 0xa90661e2, 0x372e, 0x5d1e, 0xbb,0xbb, 0xb8,0xa2,0xce,0x0e,0x7c,0x4d)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_Release(__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Devices::Bluetooth::BluetoothLEDevice*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable* This,__x_ABI_CWindows_CDevices_CBluetooth_CIBluetoothLEDevice *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_BluetoothLEDevice_IInspectable IID___FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable
#define ITypedEventHandler_BluetoothLEDevice_IInspectableVtbl __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectableVtbl
#define ITypedEventHandler_BluetoothLEDevice_IInspectable __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable
#define ITypedEventHandler_BluetoothLEDevice_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_QueryInterface
#define ITypedEventHandler_BluetoothLEDevice_IInspectable_AddRef __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_AddRef
#define ITypedEventHandler_BluetoothLEDevice_IInspectable_Release __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_Release
#define ITypedEventHandler_BluetoothLEDevice_IInspectable_Invoke __FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CDevices__CBluetooth__CBluetoothLEDevice_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Storage::Streams::IBuffer* > interface
 */
#ifndef ____FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CStorage__CStreams__CIBuffer, 0xfd944562, 0x11d6, 0x5eab, 0xbd,0x72, 0x70,0x19,0x93,0xb6,0x8f,0xac);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("fd944562-11d6-5eab-bd72-701993b68fac")
                IVectorView<ABI::Windows::Storage::Streams::IBuffer* > : IVectorView_impl<ABI::Windows::Storage::Streams::IBuffer* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer, 0xfd944562, 0x11d6, 0x5eab, 0xbd,0x72, 0x70,0x19,0x93,0xb6,0x8f,0xac)
#endif
#else
typedef struct __FIVectorView_1_Windows__CStorage__CStreams__CIBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Storage::Streams::IBuffer* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer *This,
        UINT32 index,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CStorage__CStreams__CIBufferVtbl;

interface __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer {
    CONST_VTBL __FIVectorView_1_Windows__CStorage__CStreams__CIBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Storage::Streams::IBuffer* > methods ***/
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_QueryInterface(__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_AddRef(__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_Release(__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetIids(__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName(__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel(__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Storage::Streams::IBuffer* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetAt(__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer* This,UINT32 index,__x_ABI_CWindows_CStorage_CStreams_CIBuffer **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_get_Size(__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_IndexOf(__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetMany(__FIVectorView_1_Windows__CStorage__CStreams__CIBuffer* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CStorage_CStreams_CIBuffer **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_IBuffer IID___FIVectorView_1_Windows__CStorage__CStreams__CIBuffer
#define IVectorView_IBufferVtbl __FIVectorView_1_Windows__CStorage__CStreams__CIBufferVtbl
#define IVectorView_IBuffer __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer
#define IVectorView_IBuffer_QueryInterface __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_QueryInterface
#define IVectorView_IBuffer_AddRef __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_AddRef
#define IVectorView_IBuffer_Release __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_Release
#define IVectorView_IBuffer_GetIids __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetIids
#define IVectorView_IBuffer_GetRuntimeClassName __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName
#define IVectorView_IBuffer_GetTrustLevel __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel
#define IVectorView_IBuffer_GetAt __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetAt
#define IVectorView_IBuffer_get_Size __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_get_Size
#define IVectorView_IBuffer_IndexOf __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_IndexOf
#define IVectorView_IBuffer_GetMany __FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > interface
 */
#ifndef ____FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService, 0x97df6b82, 0xd15c, 0x597e, 0xba,0x69, 0x49,0x22,0x07,0xa1,0xc1,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("97df6b82-d15c-597e-ba69-492207a1c108")
                IVectorView<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService*, ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService, 0x97df6b82, 0xd15c, 0x597e, 0xba,0x69, 0x49,0x22,0x07,0xa1,0xc1,0x08)
#endif
#else
typedef struct __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        UINT32 index,
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl;

interface __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService {
    CONST_VTBL __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > methods ***/
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_QueryInterface(__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_AddRef(__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Release(__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetIids(__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetRuntimeClassName(__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetTrustLevel(__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetAt(__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,UINT32 index,__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_get_Size(__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_IndexOf(__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetMany(__FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_RfcommDeviceService IID___FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService
#define IVectorView_RfcommDeviceServiceVtbl __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl
#define IVectorView_RfcommDeviceService __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService
#define IVectorView_RfcommDeviceService_QueryInterface __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_QueryInterface
#define IVectorView_RfcommDeviceService_AddRef __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_AddRef
#define IVectorView_RfcommDeviceService_Release __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Release
#define IVectorView_RfcommDeviceService_GetIids __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetIids
#define IVectorView_RfcommDeviceService_GetRuntimeClassName __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetRuntimeClassName
#define IVectorView_RfcommDeviceService_GetTrustLevel __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetTrustLevel
#define IVectorView_RfcommDeviceService_GetAt __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetAt
#define IVectorView_RfcommDeviceService_get_Size __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_get_Size
#define IVectorView_RfcommDeviceService_IndexOf __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_IndexOf
#define IVectorView_RfcommDeviceService_GetMany __FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Devices::Bluetooth::GenericAttributeProfile::GattDeviceService* > interface
 */
#ifndef ____FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService, 0x7c8e7fdd, 0xa1a1, 0x528a, 0x81,0xd1, 0x29,0x67,0x69,0x22,0x7a,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("7c8e7fdd-a1a1-528a-81d1-296769227a08")
                IVectorView<ABI::Windows::Devices::Bluetooth::GenericAttributeProfile::GattDeviceService* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::GenericAttributeProfile::GattDeviceService*, ABI::Windows::Devices::Bluetooth::GenericAttributeProfile::IGattDeviceService* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService, 0x7c8e7fdd, 0xa1a1, 0x528a, 0x81,0xd1, 0x29,0x67,0x69,0x22,0x7a,0x08)
#endif
#else
typedef struct __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Devices::Bluetooth::GenericAttributeProfile::GattDeviceService* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService *This,
        UINT32 index,
        __x_ABI_CWindows_CDevices_CBluetooth_CGenericAttributeProfile_CIGattDeviceService **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CGenericAttributeProfile_CIGattDeviceService *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CBluetooth_CGenericAttributeProfile_CIGattDeviceService **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceServiceVtbl;

interface __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService {
    CONST_VTBL __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Devices::Bluetooth::GenericAttributeProfile::GattDeviceService* > methods ***/
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_QueryInterface(__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_AddRef(__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_Release(__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetIids(__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetRuntimeClassName(__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetTrustLevel(__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Devices::Bluetooth::GenericAttributeProfile::GattDeviceService* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetAt(__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService* This,UINT32 index,__x_ABI_CWindows_CDevices_CBluetooth_CGenericAttributeProfile_CIGattDeviceService **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_get_Size(__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_IndexOf(__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService* This,__x_ABI_CWindows_CDevices_CBluetooth_CGenericAttributeProfile_CIGattDeviceService *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetMany(__FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CDevices_CBluetooth_CGenericAttributeProfile_CIGattDeviceService **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_GattDeviceService IID___FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService
#define IVectorView_GattDeviceServiceVtbl __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceServiceVtbl
#define IVectorView_GattDeviceService __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService
#define IVectorView_GattDeviceService_QueryInterface __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_QueryInterface
#define IVectorView_GattDeviceService_AddRef __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_AddRef
#define IVectorView_GattDeviceService_Release __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_Release
#define IVectorView_GattDeviceService_GetIids __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetIids
#define IVectorView_GattDeviceService_GetRuntimeClassName __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetRuntimeClassName
#define IVectorView_GattDeviceService_GetTrustLevel __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetTrustLevel
#define IVectorView_GattDeviceService_GetAt __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetAt
#define IVectorView_GattDeviceService_get_Size __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_get_Size
#define IVectorView_GattDeviceService_IndexOf __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_IndexOf
#define IVectorView_GattDeviceService_GetMany __FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CDevices__CBluetooth__CGenericAttributeProfile__CGattDeviceService_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_devices_bluetooth_h__ */
