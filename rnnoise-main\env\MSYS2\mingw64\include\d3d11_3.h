/*** Autogenerated by WIDL 10.12 from include/d3d11_3.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __d3d11_3_h__
#define __d3d11_3_h__

/* Forward declarations */

#ifndef __ID3D11Texture2D1_FWD_DEFINED__
#define __ID3D11Texture2D1_FWD_DEFINED__
typedef interface ID3D11Texture2D1 ID3D11Texture2D1;
#ifdef __cplusplus
interface ID3D11Texture2D1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Texture3D1_FWD_DEFINED__
#define __ID3D11Texture3D1_FWD_DEFINED__
typedef interface ID3D11Texture3D1 ID3D11Texture3D1;
#ifdef __cplusplus
interface ID3D11Texture3D1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11RasterizerState2_FWD_DEFINED__
#define __ID3D11RasterizerState2_FWD_DEFINED__
typedef interface ID3D11RasterizerState2 ID3D11RasterizerState2;
#ifdef __cplusplus
interface ID3D11RasterizerState2;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11ShaderResourceView1_FWD_DEFINED__
#define __ID3D11ShaderResourceView1_FWD_DEFINED__
typedef interface ID3D11ShaderResourceView1 ID3D11ShaderResourceView1;
#ifdef __cplusplus
interface ID3D11ShaderResourceView1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11RenderTargetView1_FWD_DEFINED__
#define __ID3D11RenderTargetView1_FWD_DEFINED__
typedef interface ID3D11RenderTargetView1 ID3D11RenderTargetView1;
#ifdef __cplusplus
interface ID3D11RenderTargetView1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11UnorderedAccessView1_FWD_DEFINED__
#define __ID3D11UnorderedAccessView1_FWD_DEFINED__
typedef interface ID3D11UnorderedAccessView1 ID3D11UnorderedAccessView1;
#ifdef __cplusplus
interface ID3D11UnorderedAccessView1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Query1_FWD_DEFINED__
#define __ID3D11Query1_FWD_DEFINED__
typedef interface ID3D11Query1 ID3D11Query1;
#ifdef __cplusplus
interface ID3D11Query1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11DeviceContext3_FWD_DEFINED__
#define __ID3D11DeviceContext3_FWD_DEFINED__
typedef interface ID3D11DeviceContext3 ID3D11DeviceContext3;
#ifdef __cplusplus
interface ID3D11DeviceContext3;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Fence_FWD_DEFINED__
#define __ID3D11Fence_FWD_DEFINED__
typedef interface ID3D11Fence ID3D11Fence;
#ifdef __cplusplus
interface ID3D11Fence;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11DeviceContext4_FWD_DEFINED__
#define __ID3D11DeviceContext4_FWD_DEFINED__
typedef interface ID3D11DeviceContext4 ID3D11DeviceContext4;
#ifdef __cplusplus
interface ID3D11DeviceContext4;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Device3_FWD_DEFINED__
#define __ID3D11Device3_FWD_DEFINED__
typedef interface ID3D11Device3 ID3D11Device3;
#ifdef __cplusplus
interface ID3D11Device3;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <dxgi1_3.h>
#include <d3dcommon.h>
#include <d3d11_2.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum D3D11_CONTEXT_TYPE {
    D3D11_CONTEXT_TYPE_ALL = 0x0,
    D3D11_CONTEXT_TYPE_3D = 0x1,
    D3D11_CONTEXT_TYPE_COMPUTE = 0x2,
    D3D11_CONTEXT_TYPE_COPY = 0x3,
    D3D11_CONTEXT_TYPE_VIDEO = 0x4
} D3D11_CONTEXT_TYPE;
typedef enum D3D11_TEXTURE_LAYOUT {
    D3D11_TEXTURE_LAYOUT_UNDEFINED = 0x0,
    D3D11_TEXTURE_LAYOUT_ROW_MAJOR = 0x1,
    D3D11_TEXTURE_LAYOUT_64K_STANDARD_SWIZZLE = 0x2
} D3D11_TEXTURE_LAYOUT;
typedef enum D3D11_CONSERVATIVE_RASTERIZATION_MODE {
    D3D11_CONSERVATIVE_RASTERIZATION_MODE_OFF = 0x0,
    D3D11_CONSERVATIVE_RASTERIZATION_MODE_ON = 0x1
} D3D11_CONSERVATIVE_RASTERIZATION_MODE;
typedef enum D3D11_FENCE_FLAG {
    D3D11_FENCE_FLAG_NONE = 0x1,
    D3D11_FENCE_FLAG_SHARED = 0x2,
    D3D11_FENCE_FLAG_SHARED_CROSS_ADAPTER = 0x4,
    D3D11_FENCE_FLAG_NON_MONITORED = 0x8
} D3D11_FENCE_FLAG;
typedef struct D3D11_TEXTURE2D_DESC1 {
    UINT Width;
    UINT Height;
    UINT MipLevels;
    UINT ArraySize;
    DXGI_FORMAT Format;
    DXGI_SAMPLE_DESC SampleDesc;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
    D3D11_TEXTURE_LAYOUT TextureLayout;
} D3D11_TEXTURE2D_DESC1;
typedef struct D3D11_TEXTURE3D_DESC1 {
    UINT Width;
    UINT Height;
    UINT Depth;
    UINT MipLevels;
    DXGI_FORMAT Format;
    D3D11_USAGE Usage;
    UINT BindFlags;
    UINT CPUAccessFlags;
    UINT MiscFlags;
    D3D11_TEXTURE_LAYOUT TextureLayout;
} D3D11_TEXTURE3D_DESC1;
typedef struct D3D11_RASTERIZER_DESC2 {
    D3D11_FILL_MODE FillMode;
    D3D11_CULL_MODE CullMode;
    WINBOOL FrontCounterClockwise;
    int DepthBias;
    float DepthBiasClamp;
    float SlopeScaledDepthBias;
    WINBOOL DepthClipEnable;
    WINBOOL ScissorEnable;
    WINBOOL MultisampleEnable;
    WINBOOL AntialiasedLineEnable;
    UINT ForcedSampleCount;
    D3D11_CONSERVATIVE_RASTERIZATION_MODE ConservativeRaster;
} D3D11_RASTERIZER_DESC2;
typedef struct D3D11_TEX2D_SRV1 {
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT PlaneSlice;
} D3D11_TEX2D_SRV1;
typedef struct D3D11_TEX2D_ARRAY_SRV1 {
    UINT MostDetailedMip;
    UINT MipLevels;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D11_TEX2D_ARRAY_SRV1;
typedef struct D3D11_SHADER_RESOURCE_VIEW_DESC1 {
    DXGI_FORMAT Format;
    D3D11_SRV_DIMENSION ViewDimension;
    __C89_NAMELESS union {
        D3D11_BUFFER_SRV Buffer;
        D3D11_TEX1D_SRV Texture1D;
        D3D11_TEX1D_ARRAY_SRV Texture1DArray;
        D3D11_TEX2D_SRV1 Texture2D;
        D3D11_TEX2D_ARRAY_SRV1 Texture2DArray;
        D3D11_TEX2DMS_SRV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_SRV Texture2DMSArray;
        D3D11_TEX3D_SRV Texture3D;
        D3D11_TEXCUBE_SRV TextureCube;
        D3D11_TEXCUBE_ARRAY_SRV TextureCubeArray;
        D3D11_BUFFEREX_SRV BufferEx;
    } __C89_NAMELESSUNIONNAME;
} D3D11_SHADER_RESOURCE_VIEW_DESC1;
typedef struct D3D11_TEX2D_RTV1 {
    UINT MipSlice;
    UINT PlaneSlice;
} D3D11_TEX2D_RTV1;
typedef struct D3D11_TEX2D_ARRAY_RTV1 {
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D11_TEX2D_ARRAY_RTV1;
typedef struct D3D11_RENDER_TARGET_VIEW_DESC1 {
    DXGI_FORMAT Format;
    D3D11_RTV_DIMENSION ViewDimension;
    __C89_NAMELESS union {
        D3D11_BUFFER_RTV Buffer;
        D3D11_TEX1D_RTV Texture1D;
        D3D11_TEX1D_ARRAY_RTV Texture1DArray;
        D3D11_TEX2D_RTV1 Texture2D;
        D3D11_TEX2D_ARRAY_RTV1 Texture2DArray;
        D3D11_TEX2DMS_RTV Texture2DMS;
        D3D11_TEX2DMS_ARRAY_RTV Texture2DMSArray;
        D3D11_TEX3D_RTV Texture3D;
    } __C89_NAMELESSUNIONNAME;
} D3D11_RENDER_TARGET_VIEW_DESC1;
typedef struct D3D11_TEX2D_UAV1 {
    UINT MipSlice;
    UINT PlaneSlice;
} D3D11_TEX2D_UAV1;
typedef struct D3D11_TEX2D_ARRAY_UAV1 {
    UINT MipSlice;
    UINT FirstArraySlice;
    UINT ArraySize;
    UINT PlaneSlice;
} D3D11_TEX2D_ARRAY_UAV1;
typedef struct D3D11_UNORDERED_ACCESS_VIEW_DESC1 {
    DXGI_FORMAT Format;
    D3D11_UAV_DIMENSION ViewDimension;
    __C89_NAMELESS union {
        D3D11_BUFFER_UAV Buffer;
        D3D11_TEX1D_UAV Texture1D;
        D3D11_TEX1D_ARRAY_UAV Texture1DArray;
        D3D11_TEX2D_UAV1 Texture2D;
        D3D11_TEX2D_ARRAY_UAV1 Texture2DArray;
        D3D11_TEX3D_UAV Texture3D;
    } __C89_NAMELESSUNIONNAME;
} D3D11_UNORDERED_ACCESS_VIEW_DESC1;
typedef struct D3D11_QUERY_DESC1 {
    D3D11_QUERY Query;
    UINT MiscFlags;
    D3D11_CONTEXT_TYPE ContextType;
} D3D11_QUERY_DESC1;
/*****************************************************************************
 * ID3D11Texture2D1 interface
 */
#ifndef __ID3D11Texture2D1_INTERFACE_DEFINED__
#define __ID3D11Texture2D1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Texture2D1, 0x51218251, 0x1e33, 0x4617, 0x9c,0xcb, 0x4d,0x3a,0x43,0x67,0xe7,0xbb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51218251-1e33-4617-9ccb-4d3a4367e7bb")
ID3D11Texture2D1 : public ID3D11Texture2D
{
    virtual void STDMETHODCALLTYPE GetDesc1(
        D3D11_TEXTURE2D_DESC1 *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Texture2D1, 0x51218251, 0x1e33, 0x4617, 0x9c,0xcb, 0x4d,0x3a,0x43,0x67,0xe7,0xbb)
#endif
#else
typedef struct ID3D11Texture2D1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Texture2D1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Texture2D1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Texture2D1 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Texture2D1 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Texture2D1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Texture2D1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Texture2D1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Resource methods ***/
    void (STDMETHODCALLTYPE *GetType)(
        ID3D11Texture2D1 *This,
        D3D11_RESOURCE_DIMENSION *pResourceDimension);

    void (STDMETHODCALLTYPE *SetEvictionPriority)(
        ID3D11Texture2D1 *This,
        UINT EvictionPriority);

    UINT (STDMETHODCALLTYPE *GetEvictionPriority)(
        ID3D11Texture2D1 *This);

    /*** ID3D11Texture2D methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11Texture2D1 *This,
        D3D11_TEXTURE2D_DESC *pDesc);

    /*** ID3D11Texture2D1 methods ***/
    void (STDMETHODCALLTYPE *GetDesc1)(
        ID3D11Texture2D1 *This,
        D3D11_TEXTURE2D_DESC1 *desc);

    END_INTERFACE
} ID3D11Texture2D1Vtbl;

interface ID3D11Texture2D1 {
    CONST_VTBL ID3D11Texture2D1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Texture2D1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Texture2D1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Texture2D1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Texture2D1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Texture2D1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Texture2D1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Texture2D1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Resource methods ***/
#define ID3D11Texture2D1_GetType(This,pResourceDimension) (This)->lpVtbl->GetType(This,pResourceDimension)
#define ID3D11Texture2D1_SetEvictionPriority(This,EvictionPriority) (This)->lpVtbl->SetEvictionPriority(This,EvictionPriority)
#define ID3D11Texture2D1_GetEvictionPriority(This) (This)->lpVtbl->GetEvictionPriority(This)
/*** ID3D11Texture2D methods ***/
#define ID3D11Texture2D1_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
/*** ID3D11Texture2D1 methods ***/
#define ID3D11Texture2D1_GetDesc1(This,desc) (This)->lpVtbl->GetDesc1(This,desc)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11Texture2D1_QueryInterface(ID3D11Texture2D1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11Texture2D1_AddRef(ID3D11Texture2D1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11Texture2D1_Release(ID3D11Texture2D1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11Texture2D1_GetDevice(ID3D11Texture2D1* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11Texture2D1_GetPrivateData(ID3D11Texture2D1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11Texture2D1_SetPrivateData(ID3D11Texture2D1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11Texture2D1_SetPrivateDataInterface(ID3D11Texture2D1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Resource methods ***/
static inline void ID3D11Texture2D1_GetType(ID3D11Texture2D1* This,D3D11_RESOURCE_DIMENSION *pResourceDimension) {
    This->lpVtbl->GetType(This,pResourceDimension);
}
static inline void ID3D11Texture2D1_SetEvictionPriority(ID3D11Texture2D1* This,UINT EvictionPriority) {
    This->lpVtbl->SetEvictionPriority(This,EvictionPriority);
}
static inline UINT ID3D11Texture2D1_GetEvictionPriority(ID3D11Texture2D1* This) {
    return This->lpVtbl->GetEvictionPriority(This);
}
/*** ID3D11Texture2D methods ***/
static inline void ID3D11Texture2D1_GetDesc(ID3D11Texture2D1* This,D3D11_TEXTURE2D_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
/*** ID3D11Texture2D1 methods ***/
static inline void ID3D11Texture2D1_GetDesc1(ID3D11Texture2D1* This,D3D11_TEXTURE2D_DESC1 *desc) {
    This->lpVtbl->GetDesc1(This,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D11Texture2D1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Texture3D1 interface
 */
#ifndef __ID3D11Texture3D1_INTERFACE_DEFINED__
#define __ID3D11Texture3D1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Texture3D1, 0x0c711683, 0x2853, 0x4846, 0x9b,0xb0, 0xf3,0xe6,0x06,0x39,0xe4,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0c711683-2853-4846-9bb0-f3e60639e46a")
ID3D11Texture3D1 : public ID3D11Texture3D
{
    virtual void STDMETHODCALLTYPE GetDesc1(
        D3D11_TEXTURE3D_DESC1 *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Texture3D1, 0x0c711683, 0x2853, 0x4846, 0x9b,0xb0, 0xf3,0xe6,0x06,0x39,0xe4,0x6a)
#endif
#else
typedef struct ID3D11Texture3D1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Texture3D1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Texture3D1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Texture3D1 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Texture3D1 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Texture3D1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Texture3D1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Texture3D1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Resource methods ***/
    void (STDMETHODCALLTYPE *GetType)(
        ID3D11Texture3D1 *This,
        D3D11_RESOURCE_DIMENSION *pResourceDimension);

    void (STDMETHODCALLTYPE *SetEvictionPriority)(
        ID3D11Texture3D1 *This,
        UINT EvictionPriority);

    UINT (STDMETHODCALLTYPE *GetEvictionPriority)(
        ID3D11Texture3D1 *This);

    /*** ID3D11Texture3D methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11Texture3D1 *This,
        D3D11_TEXTURE3D_DESC *pDesc);

    /*** ID3D11Texture3D1 methods ***/
    void (STDMETHODCALLTYPE *GetDesc1)(
        ID3D11Texture3D1 *This,
        D3D11_TEXTURE3D_DESC1 *desc);

    END_INTERFACE
} ID3D11Texture3D1Vtbl;

interface ID3D11Texture3D1 {
    CONST_VTBL ID3D11Texture3D1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Texture3D1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Texture3D1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Texture3D1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Texture3D1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Texture3D1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Texture3D1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Texture3D1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Resource methods ***/
#define ID3D11Texture3D1_GetType(This,pResourceDimension) (This)->lpVtbl->GetType(This,pResourceDimension)
#define ID3D11Texture3D1_SetEvictionPriority(This,EvictionPriority) (This)->lpVtbl->SetEvictionPriority(This,EvictionPriority)
#define ID3D11Texture3D1_GetEvictionPriority(This) (This)->lpVtbl->GetEvictionPriority(This)
/*** ID3D11Texture3D methods ***/
#define ID3D11Texture3D1_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
/*** ID3D11Texture3D1 methods ***/
#define ID3D11Texture3D1_GetDesc1(This,desc) (This)->lpVtbl->GetDesc1(This,desc)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11Texture3D1_QueryInterface(ID3D11Texture3D1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11Texture3D1_AddRef(ID3D11Texture3D1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11Texture3D1_Release(ID3D11Texture3D1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11Texture3D1_GetDevice(ID3D11Texture3D1* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11Texture3D1_GetPrivateData(ID3D11Texture3D1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11Texture3D1_SetPrivateData(ID3D11Texture3D1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11Texture3D1_SetPrivateDataInterface(ID3D11Texture3D1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Resource methods ***/
static inline void ID3D11Texture3D1_GetType(ID3D11Texture3D1* This,D3D11_RESOURCE_DIMENSION *pResourceDimension) {
    This->lpVtbl->GetType(This,pResourceDimension);
}
static inline void ID3D11Texture3D1_SetEvictionPriority(ID3D11Texture3D1* This,UINT EvictionPriority) {
    This->lpVtbl->SetEvictionPriority(This,EvictionPriority);
}
static inline UINT ID3D11Texture3D1_GetEvictionPriority(ID3D11Texture3D1* This) {
    return This->lpVtbl->GetEvictionPriority(This);
}
/*** ID3D11Texture3D methods ***/
static inline void ID3D11Texture3D1_GetDesc(ID3D11Texture3D1* This,D3D11_TEXTURE3D_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
/*** ID3D11Texture3D1 methods ***/
static inline void ID3D11Texture3D1_GetDesc1(ID3D11Texture3D1* This,D3D11_TEXTURE3D_DESC1 *desc) {
    This->lpVtbl->GetDesc1(This,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D11Texture3D1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11RasterizerState2 interface
 */
#ifndef __ID3D11RasterizerState2_INTERFACE_DEFINED__
#define __ID3D11RasterizerState2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11RasterizerState2, 0x6fbd02fb, 0x209f, 0x46c4, 0xb0,0x59, 0x2e,0xd1,0x55,0x86,0xa6,0xac);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6fbd02fb-209f-46c4-b059-2ed15586a6ac")
ID3D11RasterizerState2 : public ID3D11RasterizerState1
{
    virtual void STDMETHODCALLTYPE GetDesc2(
        D3D11_RASTERIZER_DESC2 *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11RasterizerState2, 0x6fbd02fb, 0x209f, 0x46c4, 0xb0,0x59, 0x2e,0xd1,0x55,0x86,0xa6,0xac)
#endif
#else
typedef struct ID3D11RasterizerState2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11RasterizerState2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11RasterizerState2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11RasterizerState2 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11RasterizerState2 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11RasterizerState2 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11RasterizerState2 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11RasterizerState2 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11RasterizerState methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11RasterizerState2 *This,
        D3D11_RASTERIZER_DESC *pDesc);

    /*** ID3D11RasterizerState1 methods ***/
    void (STDMETHODCALLTYPE *GetDesc1)(
        ID3D11RasterizerState2 *This,
        D3D11_RASTERIZER_DESC1 *pDesc);

    /*** ID3D11RasterizerState2 methods ***/
    void (STDMETHODCALLTYPE *GetDesc2)(
        ID3D11RasterizerState2 *This,
        D3D11_RASTERIZER_DESC2 *desc);

    END_INTERFACE
} ID3D11RasterizerState2Vtbl;

interface ID3D11RasterizerState2 {
    CONST_VTBL ID3D11RasterizerState2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11RasterizerState2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11RasterizerState2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11RasterizerState2_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11RasterizerState2_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11RasterizerState2_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11RasterizerState2_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11RasterizerState2_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11RasterizerState methods ***/
#define ID3D11RasterizerState2_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
/*** ID3D11RasterizerState1 methods ***/
#define ID3D11RasterizerState2_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
/*** ID3D11RasterizerState2 methods ***/
#define ID3D11RasterizerState2_GetDesc2(This,desc) (This)->lpVtbl->GetDesc2(This,desc)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11RasterizerState2_QueryInterface(ID3D11RasterizerState2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11RasterizerState2_AddRef(ID3D11RasterizerState2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11RasterizerState2_Release(ID3D11RasterizerState2* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11RasterizerState2_GetDevice(ID3D11RasterizerState2* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11RasterizerState2_GetPrivateData(ID3D11RasterizerState2* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11RasterizerState2_SetPrivateData(ID3D11RasterizerState2* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11RasterizerState2_SetPrivateDataInterface(ID3D11RasterizerState2* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11RasterizerState methods ***/
static inline void ID3D11RasterizerState2_GetDesc(ID3D11RasterizerState2* This,D3D11_RASTERIZER_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
/*** ID3D11RasterizerState1 methods ***/
static inline void ID3D11RasterizerState2_GetDesc1(ID3D11RasterizerState2* This,D3D11_RASTERIZER_DESC1 *pDesc) {
    This->lpVtbl->GetDesc1(This,pDesc);
}
/*** ID3D11RasterizerState2 methods ***/
static inline void ID3D11RasterizerState2_GetDesc2(ID3D11RasterizerState2* This,D3D11_RASTERIZER_DESC2 *desc) {
    This->lpVtbl->GetDesc2(This,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D11RasterizerState2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11ShaderResourceView1 interface
 */
#ifndef __ID3D11ShaderResourceView1_INTERFACE_DEFINED__
#define __ID3D11ShaderResourceView1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11ShaderResourceView1, 0x91308b87, 0x9040, 0x411d, 0x8c,0x67, 0xc3,0x92,0x53,0xce,0x38,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("91308b87-9040-411d-8c67-c39253ce3802")
ID3D11ShaderResourceView1 : public ID3D11ShaderResourceView
{
    virtual void STDMETHODCALLTYPE GetDesc1(
        D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11ShaderResourceView1, 0x91308b87, 0x9040, 0x411d, 0x8c,0x67, 0xc3,0x92,0x53,0xce,0x38,0x02)
#endif
#else
typedef struct ID3D11ShaderResourceView1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11ShaderResourceView1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11ShaderResourceView1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11ShaderResourceView1 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11ShaderResourceView1 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11ShaderResourceView1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11ShaderResourceView1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11ShaderResourceView1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D11ShaderResourceView1 *This,
        ID3D11Resource **ppResource);

    /*** ID3D11ShaderResourceView methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11ShaderResourceView1 *This,
        D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc);

    /*** ID3D11ShaderResourceView1 methods ***/
    void (STDMETHODCALLTYPE *GetDesc1)(
        ID3D11ShaderResourceView1 *This,
        D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc);

    END_INTERFACE
} ID3D11ShaderResourceView1Vtbl;

interface ID3D11ShaderResourceView1 {
    CONST_VTBL ID3D11ShaderResourceView1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11ShaderResourceView1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11ShaderResourceView1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11ShaderResourceView1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11ShaderResourceView1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11ShaderResourceView1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11ShaderResourceView1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11ShaderResourceView1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11View methods ***/
#define ID3D11ShaderResourceView1_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
/*** ID3D11ShaderResourceView methods ***/
#define ID3D11ShaderResourceView1_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
/*** ID3D11ShaderResourceView1 methods ***/
#define ID3D11ShaderResourceView1_GetDesc1(This,desc) (This)->lpVtbl->GetDesc1(This,desc)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11ShaderResourceView1_QueryInterface(ID3D11ShaderResourceView1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11ShaderResourceView1_AddRef(ID3D11ShaderResourceView1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11ShaderResourceView1_Release(ID3D11ShaderResourceView1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11ShaderResourceView1_GetDevice(ID3D11ShaderResourceView1* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11ShaderResourceView1_GetPrivateData(ID3D11ShaderResourceView1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11ShaderResourceView1_SetPrivateData(ID3D11ShaderResourceView1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11ShaderResourceView1_SetPrivateDataInterface(ID3D11ShaderResourceView1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11View methods ***/
static inline void ID3D11ShaderResourceView1_GetResource(ID3D11ShaderResourceView1* This,ID3D11Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
/*** ID3D11ShaderResourceView methods ***/
static inline void ID3D11ShaderResourceView1_GetDesc(ID3D11ShaderResourceView1* This,D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
/*** ID3D11ShaderResourceView1 methods ***/
static inline void ID3D11ShaderResourceView1_GetDesc1(ID3D11ShaderResourceView1* This,D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc) {
    This->lpVtbl->GetDesc1(This,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D11ShaderResourceView1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11RenderTargetView1 interface
 */
#ifndef __ID3D11RenderTargetView1_INTERFACE_DEFINED__
#define __ID3D11RenderTargetView1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11RenderTargetView1, 0xffbe2e23, 0xf011, 0x418a, 0xac,0x56, 0x5c,0xee,0xd7,0xc5,0xb9,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ffbe2e23-f011-418a-ac56-5ceed7c5b94b")
ID3D11RenderTargetView1 : public ID3D11RenderTargetView
{
    virtual void STDMETHODCALLTYPE GetDesc1(
        D3D11_RENDER_TARGET_VIEW_DESC1 *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11RenderTargetView1, 0xffbe2e23, 0xf011, 0x418a, 0xac,0x56, 0x5c,0xee,0xd7,0xc5,0xb9,0x4b)
#endif
#else
typedef struct ID3D11RenderTargetView1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11RenderTargetView1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11RenderTargetView1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11RenderTargetView1 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11RenderTargetView1 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11RenderTargetView1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11RenderTargetView1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11RenderTargetView1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D11RenderTargetView1 *This,
        ID3D11Resource **ppResource);

    /*** ID3D11RenderTargetView methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11RenderTargetView1 *This,
        D3D11_RENDER_TARGET_VIEW_DESC *pDesc);

    /*** ID3D11RenderTargetView1 methods ***/
    void (STDMETHODCALLTYPE *GetDesc1)(
        ID3D11RenderTargetView1 *This,
        D3D11_RENDER_TARGET_VIEW_DESC1 *desc);

    END_INTERFACE
} ID3D11RenderTargetView1Vtbl;

interface ID3D11RenderTargetView1 {
    CONST_VTBL ID3D11RenderTargetView1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11RenderTargetView1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11RenderTargetView1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11RenderTargetView1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11RenderTargetView1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11RenderTargetView1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11RenderTargetView1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11RenderTargetView1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11View methods ***/
#define ID3D11RenderTargetView1_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
/*** ID3D11RenderTargetView methods ***/
#define ID3D11RenderTargetView1_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
/*** ID3D11RenderTargetView1 methods ***/
#define ID3D11RenderTargetView1_GetDesc1(This,desc) (This)->lpVtbl->GetDesc1(This,desc)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11RenderTargetView1_QueryInterface(ID3D11RenderTargetView1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11RenderTargetView1_AddRef(ID3D11RenderTargetView1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11RenderTargetView1_Release(ID3D11RenderTargetView1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11RenderTargetView1_GetDevice(ID3D11RenderTargetView1* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11RenderTargetView1_GetPrivateData(ID3D11RenderTargetView1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11RenderTargetView1_SetPrivateData(ID3D11RenderTargetView1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11RenderTargetView1_SetPrivateDataInterface(ID3D11RenderTargetView1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11View methods ***/
static inline void ID3D11RenderTargetView1_GetResource(ID3D11RenderTargetView1* This,ID3D11Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
/*** ID3D11RenderTargetView methods ***/
static inline void ID3D11RenderTargetView1_GetDesc(ID3D11RenderTargetView1* This,D3D11_RENDER_TARGET_VIEW_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
/*** ID3D11RenderTargetView1 methods ***/
static inline void ID3D11RenderTargetView1_GetDesc1(ID3D11RenderTargetView1* This,D3D11_RENDER_TARGET_VIEW_DESC1 *desc) {
    This->lpVtbl->GetDesc1(This,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D11RenderTargetView1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11UnorderedAccessView1 interface
 */
#ifndef __ID3D11UnorderedAccessView1_INTERFACE_DEFINED__
#define __ID3D11UnorderedAccessView1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11UnorderedAccessView1, 0x7b3b6153, 0xa886, 0x4544, 0xab,0x37, 0x65,0x37,0xc8,0x50,0x04,0x03);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7b3b6153-a886-4544-ab37-6537c8500403")
ID3D11UnorderedAccessView1 : public ID3D11UnorderedAccessView
{
    virtual void STDMETHODCALLTYPE GetDesc1(
        D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11UnorderedAccessView1, 0x7b3b6153, 0xa886, 0x4544, 0xab,0x37, 0x65,0x37,0xc8,0x50,0x04,0x03)
#endif
#else
typedef struct ID3D11UnorderedAccessView1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11UnorderedAccessView1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11UnorderedAccessView1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11UnorderedAccessView1 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11UnorderedAccessView1 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11UnorderedAccessView1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11UnorderedAccessView1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11UnorderedAccessView1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11View methods ***/
    void (STDMETHODCALLTYPE *GetResource)(
        ID3D11UnorderedAccessView1 *This,
        ID3D11Resource **ppResource);

    /*** ID3D11UnorderedAccessView methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11UnorderedAccessView1 *This,
        D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc);

    /*** ID3D11UnorderedAccessView1 methods ***/
    void (STDMETHODCALLTYPE *GetDesc1)(
        ID3D11UnorderedAccessView1 *This,
        D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc);

    END_INTERFACE
} ID3D11UnorderedAccessView1Vtbl;

interface ID3D11UnorderedAccessView1 {
    CONST_VTBL ID3D11UnorderedAccessView1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11UnorderedAccessView1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11UnorderedAccessView1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11UnorderedAccessView1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11UnorderedAccessView1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11UnorderedAccessView1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11UnorderedAccessView1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11UnorderedAccessView1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11View methods ***/
#define ID3D11UnorderedAccessView1_GetResource(This,ppResource) (This)->lpVtbl->GetResource(This,ppResource)
/*** ID3D11UnorderedAccessView methods ***/
#define ID3D11UnorderedAccessView1_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
/*** ID3D11UnorderedAccessView1 methods ***/
#define ID3D11UnorderedAccessView1_GetDesc1(This,desc) (This)->lpVtbl->GetDesc1(This,desc)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11UnorderedAccessView1_QueryInterface(ID3D11UnorderedAccessView1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11UnorderedAccessView1_AddRef(ID3D11UnorderedAccessView1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11UnorderedAccessView1_Release(ID3D11UnorderedAccessView1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11UnorderedAccessView1_GetDevice(ID3D11UnorderedAccessView1* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11UnorderedAccessView1_GetPrivateData(ID3D11UnorderedAccessView1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11UnorderedAccessView1_SetPrivateData(ID3D11UnorderedAccessView1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11UnorderedAccessView1_SetPrivateDataInterface(ID3D11UnorderedAccessView1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11View methods ***/
static inline void ID3D11UnorderedAccessView1_GetResource(ID3D11UnorderedAccessView1* This,ID3D11Resource **ppResource) {
    This->lpVtbl->GetResource(This,ppResource);
}
/*** ID3D11UnorderedAccessView methods ***/
static inline void ID3D11UnorderedAccessView1_GetDesc(ID3D11UnorderedAccessView1* This,D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
/*** ID3D11UnorderedAccessView1 methods ***/
static inline void ID3D11UnorderedAccessView1_GetDesc1(ID3D11UnorderedAccessView1* This,D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc) {
    This->lpVtbl->GetDesc1(This,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D11UnorderedAccessView1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Query1 interface
 */
#ifndef __ID3D11Query1_INTERFACE_DEFINED__
#define __ID3D11Query1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Query1, 0x631b4766, 0x36dc, 0x461d, 0x8d,0xb6, 0xc4,0x7e,0x13,0xe6,0x09,0x16);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("631b4766-36dc-461d-8db6-c47e13e60916")
ID3D11Query1 : public ID3D11Query
{
    virtual void STDMETHODCALLTYPE GetDesc1(
        D3D11_QUERY_DESC1 *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Query1, 0x631b4766, 0x36dc, 0x461d, 0x8d,0xb6, 0xc4,0x7e,0x13,0xe6,0x09,0x16)
#endif
#else
typedef struct ID3D11Query1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Query1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Query1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Query1 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Query1 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Query1 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Query1 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Query1 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Asynchronous methods ***/
    UINT (STDMETHODCALLTYPE *GetDataSize)(
        ID3D11Query1 *This);

    /*** ID3D11Query methods ***/
    void (STDMETHODCALLTYPE *GetDesc)(
        ID3D11Query1 *This,
        D3D11_QUERY_DESC *pDesc);

    /*** ID3D11Query1 methods ***/
    void (STDMETHODCALLTYPE *GetDesc1)(
        ID3D11Query1 *This,
        D3D11_QUERY_DESC1 *desc);

    END_INTERFACE
} ID3D11Query1Vtbl;

interface ID3D11Query1 {
    CONST_VTBL ID3D11Query1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Query1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Query1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Query1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Query1_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Query1_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Query1_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Query1_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Asynchronous methods ***/
#define ID3D11Query1_GetDataSize(This) (This)->lpVtbl->GetDataSize(This)
/*** ID3D11Query methods ***/
#define ID3D11Query1_GetDesc(This,pDesc) (This)->lpVtbl->GetDesc(This,pDesc)
/*** ID3D11Query1 methods ***/
#define ID3D11Query1_GetDesc1(This,desc) (This)->lpVtbl->GetDesc1(This,desc)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11Query1_QueryInterface(ID3D11Query1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11Query1_AddRef(ID3D11Query1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11Query1_Release(ID3D11Query1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11Query1_GetDevice(ID3D11Query1* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11Query1_GetPrivateData(ID3D11Query1* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11Query1_SetPrivateData(ID3D11Query1* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11Query1_SetPrivateDataInterface(ID3D11Query1* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Asynchronous methods ***/
static inline UINT ID3D11Query1_GetDataSize(ID3D11Query1* This) {
    return This->lpVtbl->GetDataSize(This);
}
/*** ID3D11Query methods ***/
static inline void ID3D11Query1_GetDesc(ID3D11Query1* This,D3D11_QUERY_DESC *pDesc) {
    This->lpVtbl->GetDesc(This,pDesc);
}
/*** ID3D11Query1 methods ***/
static inline void ID3D11Query1_GetDesc1(ID3D11Query1* This,D3D11_QUERY_DESC1 *desc) {
    This->lpVtbl->GetDesc1(This,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D11Query1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11DeviceContext3 interface
 */
#ifndef __ID3D11DeviceContext3_INTERFACE_DEFINED__
#define __ID3D11DeviceContext3_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11DeviceContext3, 0xb4e3c01d, 0xe79e, 0x4637, 0x91,0xb2, 0x51,0x0e,0x9f,0x4c,0x9b,0x8f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b4e3c01d-e79e-4637-91b2-510e9f4c9b8f")
ID3D11DeviceContext3 : public ID3D11DeviceContext2
{
    virtual void STDMETHODCALLTYPE Flush1(
        D3D11_CONTEXT_TYPE type,
        HANDLE event) = 0;

    virtual void STDMETHODCALLTYPE SetHardwareProtectionState(
        WINBOOL enable) = 0;

    virtual void STDMETHODCALLTYPE GetHardwareProtectionState(
        WINBOOL *enable) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11DeviceContext3, 0xb4e3c01d, 0xe79e, 0x4637, 0x91,0xb2, 0x51,0x0e,0x9f,0x4c,0x9b,0x8f)
#endif
#else
typedef struct ID3D11DeviceContext3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11DeviceContext3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11DeviceContext3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11DeviceContext3 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11DeviceContext3 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11DeviceContext3 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11DeviceContext3 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11DeviceContext3 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11DeviceContext methods ***/
    void (STDMETHODCALLTYPE *VSSetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSSetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSSetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11PixelShader *pPixelShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *PSSetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *VSSetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11VertexShader *pVertexShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *DrawIndexed)(
        ID3D11DeviceContext3 *This,
        UINT IndexCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation);

    void (STDMETHODCALLTYPE *Draw)(
        ID3D11DeviceContext3 *This,
        UINT VertexCount,
        UINT StartVertexLocation);

    HRESULT (STDMETHODCALLTYPE *Map)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *pResource,
        UINT Subresource,
        D3D11_MAP MapType,
        UINT MapFlags,
        D3D11_MAPPED_SUBRESOURCE *pMappedResource);

    void (STDMETHODCALLTYPE *Unmap)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *pResource,
        UINT Subresource);

    void (STDMETHODCALLTYPE *PSSetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *IASetInputLayout)(
        ID3D11DeviceContext3 *This,
        ID3D11InputLayout *pInputLayout);

    void (STDMETHODCALLTYPE *IASetVertexBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppVertexBuffers,
        const UINT *pStrides,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *IASetIndexBuffer)(
        ID3D11DeviceContext3 *This,
        ID3D11Buffer *pIndexBuffer,
        DXGI_FORMAT Format,
        UINT Offset);

    void (STDMETHODCALLTYPE *DrawIndexedInstanced)(
        ID3D11DeviceContext3 *This,
        UINT IndexCountPerInstance,
        UINT InstanceCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *DrawInstanced)(
        ID3D11DeviceContext3 *This,
        UINT VertexCountPerInstance,
        UINT InstanceCount,
        UINT StartVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *GSSetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSSetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11GeometryShader *pShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *IASetPrimitiveTopology)(
        ID3D11DeviceContext3 *This,
        D3D11_PRIMITIVE_TOPOLOGY Topology);

    void (STDMETHODCALLTYPE *VSSetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSSetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *Begin)(
        ID3D11DeviceContext3 *This,
        ID3D11Asynchronous *pAsync);

    void (STDMETHODCALLTYPE *End)(
        ID3D11DeviceContext3 *This,
        ID3D11Asynchronous *pAsync);

    HRESULT (STDMETHODCALLTYPE *GetData)(
        ID3D11DeviceContext3 *This,
        ID3D11Asynchronous *pAsync,
        void *pData,
        UINT DataSize,
        UINT GetDataFlags);

    void (STDMETHODCALLTYPE *SetPredication)(
        ID3D11DeviceContext3 *This,
        ID3D11Predicate *pPredicate,
        WINBOOL PredicateValue);

    void (STDMETHODCALLTYPE *GSSetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSSetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *OMSetRenderTargets)(
        ID3D11DeviceContext3 *This,
        UINT NumViews,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView);

    void (STDMETHODCALLTYPE *OMSetRenderTargetsAndUnorderedAccessViews)(
        ID3D11DeviceContext3 *This,
        UINT NumRTVs,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts);

    void (STDMETHODCALLTYPE *OMSetBlendState)(
        ID3D11DeviceContext3 *This,
        ID3D11BlendState *pBlendState,
        const FLOAT BlendFactor[4],
        UINT SampleMask);

    void (STDMETHODCALLTYPE *OMSetDepthStencilState)(
        ID3D11DeviceContext3 *This,
        ID3D11DepthStencilState *pDepthStencilState,
        UINT StencilRef);

    void (STDMETHODCALLTYPE *SOSetTargets)(
        ID3D11DeviceContext3 *This,
        UINT NumBuffers,
        ID3D11Buffer *const *ppSOTargets,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *DrawAuto)(
        ID3D11DeviceContext3 *This);

    void (STDMETHODCALLTYPE *DrawIndexedInstancedIndirect)(
        ID3D11DeviceContext3 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *DrawInstancedIndirect)(
        ID3D11DeviceContext3 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *Dispatch)(
        ID3D11DeviceContext3 *This,
        UINT ThreadGroupCountX,
        UINT ThreadGroupCountY,
        UINT ThreadGroupCountZ);

    void (STDMETHODCALLTYPE *DispatchIndirect)(
        ID3D11DeviceContext3 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *RSSetState)(
        ID3D11DeviceContext3 *This,
        ID3D11RasterizerState *pRasterizerState);

    void (STDMETHODCALLTYPE *RSSetViewports)(
        ID3D11DeviceContext3 *This,
        UINT NumViewports,
        const D3D11_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSSetScissorRects)(
        ID3D11DeviceContext3 *This,
        UINT NumRects,
        const D3D11_RECT *pRects);

    void (STDMETHODCALLTYPE *CopySubresourceRegion)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D11_BOX *pSrcBox);

    void (STDMETHODCALLTYPE *CopyResource)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *pDstResource,
        ID3D11Resource *pSrcResource);

    void (STDMETHODCALLTYPE *UpdateSubresource)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        const D3D11_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch);

    void (STDMETHODCALLTYPE *CopyStructureCount)(
        ID3D11DeviceContext3 *This,
        ID3D11Buffer *pDstBuffer,
        UINT DstAlignedByteOffset,
        ID3D11UnorderedAccessView *pSrcView);

    void (STDMETHODCALLTYPE *ClearRenderTargetView)(
        ID3D11DeviceContext3 *This,
        ID3D11RenderTargetView *pRenderTargetView,
        const FLOAT ColorRGBA[4]);

    void (STDMETHODCALLTYPE *ClearUnorderedAccessViewUint)(
        ID3D11DeviceContext3 *This,
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const UINT Values[4]);

    void (STDMETHODCALLTYPE *ClearUnorderedAccessViewFloat)(
        ID3D11DeviceContext3 *This,
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const FLOAT Values[4]);

    void (STDMETHODCALLTYPE *ClearDepthStencilView)(
        ID3D11DeviceContext3 *This,
        ID3D11DepthStencilView *pDepthStencilView,
        UINT ClearFlags,
        FLOAT Depth,
        UINT8 Stencil);

    void (STDMETHODCALLTYPE *GenerateMips)(
        ID3D11DeviceContext3 *This,
        ID3D11ShaderResourceView *pShaderResourceView);

    void (STDMETHODCALLTYPE *SetResourceMinLOD)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *pResource,
        FLOAT MinLOD);

    FLOAT (STDMETHODCALLTYPE *GetResourceMinLOD)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *pResource);

    void (STDMETHODCALLTYPE *ResolveSubresource)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        DXGI_FORMAT Format);

    void (STDMETHODCALLTYPE *ExecuteCommandList)(
        ID3D11DeviceContext3 *This,
        ID3D11CommandList *pCommandList,
        WINBOOL RestoreContextState);

    void (STDMETHODCALLTYPE *HSSetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *HSSetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11HullShader *pHullShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *HSSetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *HSSetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *DSSetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *DSSetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11DomainShader *pDomainShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *DSSetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *DSSetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *CSSetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *CSSetUnorderedAccessViews)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts);

    void (STDMETHODCALLTYPE *CSSetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11ComputeShader *pComputeShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *CSSetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *CSSetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *VSGetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSGetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSGetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11PixelShader **ppPixelShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *PSGetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *VSGetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11VertexShader **ppVertexShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *PSGetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *IAGetInputLayout)(
        ID3D11DeviceContext3 *This,
        ID3D11InputLayout **ppInputLayout);

    void (STDMETHODCALLTYPE *IAGetVertexBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppVertexBuffers,
        UINT *pStrides,
        UINT *pOffsets);

    void (STDMETHODCALLTYPE *IAGetIndexBuffer)(
        ID3D11DeviceContext3 *This,
        ID3D11Buffer **pIndexBuffer,
        DXGI_FORMAT *Format,
        UINT *Offset);

    void (STDMETHODCALLTYPE *GSGetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSGetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11GeometryShader **ppGeometryShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *IAGetPrimitiveTopology)(
        ID3D11DeviceContext3 *This,
        D3D11_PRIMITIVE_TOPOLOGY *pTopology);

    void (STDMETHODCALLTYPE *VSGetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSGetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *GetPredication)(
        ID3D11DeviceContext3 *This,
        ID3D11Predicate **ppPredicate,
        WINBOOL *pPredicateValue);

    void (STDMETHODCALLTYPE *GSGetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSGetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *OMGetRenderTargets)(
        ID3D11DeviceContext3 *This,
        UINT NumViews,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView);

    void (STDMETHODCALLTYPE *OMGetRenderTargetsAndUnorderedAccessViews)(
        ID3D11DeviceContext3 *This,
        UINT NumRTVs,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews);

    void (STDMETHODCALLTYPE *OMGetBlendState)(
        ID3D11DeviceContext3 *This,
        ID3D11BlendState **ppBlendState,
        FLOAT BlendFactor[4],
        UINT *pSampleMask);

    void (STDMETHODCALLTYPE *OMGetDepthStencilState)(
        ID3D11DeviceContext3 *This,
        ID3D11DepthStencilState **ppDepthStencilState,
        UINT *pStencilRef);

    void (STDMETHODCALLTYPE *SOGetTargets)(
        ID3D11DeviceContext3 *This,
        UINT NumBuffers,
        ID3D11Buffer **ppSOTargets);

    void (STDMETHODCALLTYPE *RSGetState)(
        ID3D11DeviceContext3 *This,
        ID3D11RasterizerState **ppRasterizerState);

    void (STDMETHODCALLTYPE *RSGetViewports)(
        ID3D11DeviceContext3 *This,
        UINT *pNumViewports,
        D3D11_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSGetScissorRects)(
        ID3D11DeviceContext3 *This,
        UINT *pNumRects,
        D3D11_RECT *pRects);

    void (STDMETHODCALLTYPE *HSGetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *HSGetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11HullShader **ppHullShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *HSGetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *HSGetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *DSGetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *DSGetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11DomainShader **ppDomainShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *DSGetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *DSGetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *CSGetShaderResources)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *CSGetUnorderedAccessViews)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews);

    void (STDMETHODCALLTYPE *CSGetShader)(
        ID3D11DeviceContext3 *This,
        ID3D11ComputeShader **ppComputeShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *CSGetSamplers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *CSGetConstantBuffers)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *ClearState)(
        ID3D11DeviceContext3 *This);

    void (STDMETHODCALLTYPE *Flush)(
        ID3D11DeviceContext3 *This);

    D3D11_DEVICE_CONTEXT_TYPE (STDMETHODCALLTYPE *GetType)(
        ID3D11DeviceContext3 *This);

    UINT (STDMETHODCALLTYPE *GetContextFlags)(
        ID3D11DeviceContext3 *This);

    HRESULT (STDMETHODCALLTYPE *FinishCommandList)(
        ID3D11DeviceContext3 *This,
        WINBOOL RestoreDeferredContextState,
        ID3D11CommandList **ppCommandList);

    /*** ID3D11DeviceContext1 methods ***/
    void (STDMETHODCALLTYPE *CopySubresourceRegion1)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D11_BOX *pSrcBox,
        UINT CopyFlags);

    void (STDMETHODCALLTYPE *UpdateSubresource1)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        const D3D11_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch,
        UINT CopyFlags);

    void (STDMETHODCALLTYPE *DiscardResource)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *pResource);

    void (STDMETHODCALLTYPE *DiscardView)(
        ID3D11DeviceContext3 *This,
        ID3D11View *pResourceView);

    void (STDMETHODCALLTYPE *VSSetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *HSSetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *DSSetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *GSSetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *PSSetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *CSSetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *VSGetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *HSGetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *DSGetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *GSGetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *PSGetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *CSGetConstantBuffers1)(
        ID3D11DeviceContext3 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *SwapDeviceContextState)(
        ID3D11DeviceContext3 *This,
        ID3DDeviceContextState *pState,
        ID3DDeviceContextState **ppPreviousState);

    void (STDMETHODCALLTYPE *ClearView)(
        ID3D11DeviceContext3 *This,
        ID3D11View *pView,
        const FLOAT Color[4],
        const D3D11_RECT *pRect,
        UINT NumRects);

    void (STDMETHODCALLTYPE *DiscardView1)(
        ID3D11DeviceContext3 *This,
        ID3D11View *pResourceView,
        const D3D11_RECT *pRects,
        UINT NumRects);

    /*** ID3D11DeviceContext2 methods ***/
    HRESULT (STDMETHODCALLTYPE *UpdateTileMappings)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *resource,
        UINT region_count,
        const D3D11_TILED_RESOURCE_COORDINATE *region_start_coordinates,
        const D3D11_TILE_REGION_SIZE *region_sizes,
        ID3D11Buffer *pool,
        UINT range_count,
        const UINT *range_flags,
        const UINT *pool_start_offsets,
        const UINT *range_tile_counts,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *CopyTileMappings)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *dst_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,
        ID3D11Resource *src_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *src_start_coordinate,
        const D3D11_TILE_REGION_SIZE *region_size,
        UINT flags);

    void (STDMETHODCALLTYPE *CopyTiles)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *resource,
        const D3D11_TILED_RESOURCE_COORDINATE *start_coordinate,
        const D3D11_TILE_REGION_SIZE *size,
        ID3D11Buffer *buffer,
        UINT64 start_offset,
        UINT flags);

    void (STDMETHODCALLTYPE *UpdateTiles)(
        ID3D11DeviceContext3 *This,
        ID3D11Resource *dst_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,
        const D3D11_TILE_REGION_SIZE *dst_region_size,
        const void *src_data,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *ResizeTilePool)(
        ID3D11DeviceContext3 *This,
        ID3D11Buffer *pool,
        UINT64 size);

    void (STDMETHODCALLTYPE *TiledResourceBarrier)(
        ID3D11DeviceContext3 *This,
        ID3D11DeviceChild *before_barrier,
        ID3D11DeviceChild *after_barrier);

    WINBOOL (STDMETHODCALLTYPE *IsAnnotationEnabled)(
        ID3D11DeviceContext3 *This);

    void (STDMETHODCALLTYPE *SetMarkerInt)(
        ID3D11DeviceContext3 *This,
        const WCHAR *label,
        int data);

    void (STDMETHODCALLTYPE *BeginEventInt)(
        ID3D11DeviceContext3 *This,
        const WCHAR *label,
        int data);

    void (STDMETHODCALLTYPE *EndEvent)(
        ID3D11DeviceContext3 *This);

    /*** ID3D11DeviceContext3 methods ***/
    void (STDMETHODCALLTYPE *Flush1)(
        ID3D11DeviceContext3 *This,
        D3D11_CONTEXT_TYPE type,
        HANDLE event);

    void (STDMETHODCALLTYPE *SetHardwareProtectionState)(
        ID3D11DeviceContext3 *This,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *GetHardwareProtectionState)(
        ID3D11DeviceContext3 *This,
        WINBOOL *enable);

    END_INTERFACE
} ID3D11DeviceContext3Vtbl;

interface ID3D11DeviceContext3 {
    CONST_VTBL ID3D11DeviceContext3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11DeviceContext3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11DeviceContext3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11DeviceContext3_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11DeviceContext3_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11DeviceContext3_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11DeviceContext3_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11DeviceContext3_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11DeviceContext methods ***/
#define ID3D11DeviceContext3_VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext3_PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext3_DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation) (This)->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation)
#define ID3D11DeviceContext3_Draw(This,VertexCount,StartVertexLocation) (This)->lpVtbl->Draw(This,VertexCount,StartVertexLocation)
#define ID3D11DeviceContext3_Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource) (This)->lpVtbl->Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource)
#define ID3D11DeviceContext3_Unmap(This,pResource,Subresource) (This)->lpVtbl->Unmap(This,pResource,Subresource)
#define ID3D11DeviceContext3_PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_IASetInputLayout(This,pInputLayout) (This)->lpVtbl->IASetInputLayout(This,pInputLayout)
#define ID3D11DeviceContext3_IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D11DeviceContext3_IASetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D11DeviceContext3_DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation)
#define ID3D11DeviceContext3_DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation)
#define ID3D11DeviceContext3_GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_GSSetShader(This,pShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->GSSetShader(This,pShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext3_IASetPrimitiveTopology(This,Topology) (This)->lpVtbl->IASetPrimitiveTopology(This,Topology)
#define ID3D11DeviceContext3_VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_Begin(This,pAsync) (This)->lpVtbl->Begin(This,pAsync)
#define ID3D11DeviceContext3_End(This,pAsync) (This)->lpVtbl->End(This,pAsync)
#define ID3D11DeviceContext3_GetData(This,pAsync,pData,DataSize,GetDataFlags) (This)->lpVtbl->GetData(This,pAsync,pData,DataSize,GetDataFlags)
#define ID3D11DeviceContext3_SetPredication(This,pPredicate,PredicateValue) (This)->lpVtbl->SetPredication(This,pPredicate,PredicateValue)
#define ID3D11DeviceContext3_GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView) (This)->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView)
#define ID3D11DeviceContext3_OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts) (This)->lpVtbl->OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts)
#define ID3D11DeviceContext3_OMSetBlendState(This,pBlendState,BlendFactor,SampleMask) (This)->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask)
#define ID3D11DeviceContext3_OMSetDepthStencilState(This,pDepthStencilState,StencilRef) (This)->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef)
#define ID3D11DeviceContext3_SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets) (This)->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets)
#define ID3D11DeviceContext3_DrawAuto(This) (This)->lpVtbl->DrawAuto(This)
#define ID3D11DeviceContext3_DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext3_DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext3_Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ) (This)->lpVtbl->Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ)
#define ID3D11DeviceContext3_DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext3_RSSetState(This,pRasterizerState) (This)->lpVtbl->RSSetState(This,pRasterizerState)
#define ID3D11DeviceContext3_RSSetViewports(This,NumViewports,pViewports) (This)->lpVtbl->RSSetViewports(This,NumViewports,pViewports)
#define ID3D11DeviceContext3_RSSetScissorRects(This,NumRects,pRects) (This)->lpVtbl->RSSetScissorRects(This,NumRects,pRects)
#define ID3D11DeviceContext3_CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox) (This)->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox)
#define ID3D11DeviceContext3_CopyResource(This,pDstResource,pSrcResource) (This)->lpVtbl->CopyResource(This,pDstResource,pSrcResource)
#define ID3D11DeviceContext3_UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch) (This)->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch)
#define ID3D11DeviceContext3_CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView) (This)->lpVtbl->CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView)
#define ID3D11DeviceContext3_ClearRenderTargetView(This,pRenderTargetView,ColorRGBA) (This)->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA)
#define ID3D11DeviceContext3_ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values) (This)->lpVtbl->ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values)
#define ID3D11DeviceContext3_ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values) (This)->lpVtbl->ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values)
#define ID3D11DeviceContext3_ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil) (This)->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil)
#define ID3D11DeviceContext3_GenerateMips(This,pShaderResourceView) (This)->lpVtbl->GenerateMips(This,pShaderResourceView)
#define ID3D11DeviceContext3_SetResourceMinLOD(This,pResource,MinLOD) (This)->lpVtbl->SetResourceMinLOD(This,pResource,MinLOD)
#define ID3D11DeviceContext3_GetResourceMinLOD(This,pResource) (This)->lpVtbl->GetResourceMinLOD(This,pResource)
#define ID3D11DeviceContext3_ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format) (This)->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format)
#define ID3D11DeviceContext3_ExecuteCommandList(This,pCommandList,RestoreContextState) (This)->lpVtbl->ExecuteCommandList(This,pCommandList,RestoreContextState)
#define ID3D11DeviceContext3_HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext3_HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext3_DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts) (This)->lpVtbl->CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts)
#define ID3D11DeviceContext3_CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext3_CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext3_PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext3_PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_IAGetInputLayout(This,ppInputLayout) (This)->lpVtbl->IAGetInputLayout(This,ppInputLayout)
#define ID3D11DeviceContext3_IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D11DeviceContext3_IAGetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D11DeviceContext3_GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext3_IAGetPrimitiveTopology(This,pTopology) (This)->lpVtbl->IAGetPrimitiveTopology(This,pTopology)
#define ID3D11DeviceContext3_VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_GetPredication(This,ppPredicate,pPredicateValue) (This)->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue)
#define ID3D11DeviceContext3_GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView) (This)->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView)
#define ID3D11DeviceContext3_OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews) (This)->lpVtbl->OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews)
#define ID3D11DeviceContext3_OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask) (This)->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask)
#define ID3D11DeviceContext3_OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef) (This)->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef)
#define ID3D11DeviceContext3_SOGetTargets(This,NumBuffers,ppSOTargets) (This)->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets)
#define ID3D11DeviceContext3_RSGetState(This,ppRasterizerState) (This)->lpVtbl->RSGetState(This,ppRasterizerState)
#define ID3D11DeviceContext3_RSGetViewports(This,pNumViewports,pViewports) (This)->lpVtbl->RSGetViewports(This,pNumViewports,pViewports)
#define ID3D11DeviceContext3_RSGetScissorRects(This,pNumRects,pRects) (This)->lpVtbl->RSGetScissorRects(This,pNumRects,pRects)
#define ID3D11DeviceContext3_HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext3_HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext3_DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext3_CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews) (This)->lpVtbl->CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews)
#define ID3D11DeviceContext3_CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext3_CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext3_CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext3_ClearState(This) (This)->lpVtbl->ClearState(This)
#define ID3D11DeviceContext3_Flush(This) (This)->lpVtbl->Flush(This)
#define ID3D11DeviceContext3_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D11DeviceContext3_GetContextFlags(This) (This)->lpVtbl->GetContextFlags(This)
#define ID3D11DeviceContext3_FinishCommandList(This,RestoreDeferredContextState,ppCommandList) (This)->lpVtbl->FinishCommandList(This,RestoreDeferredContextState,ppCommandList)
/*** ID3D11DeviceContext1 methods ***/
#define ID3D11DeviceContext3_CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags) (This)->lpVtbl->CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags)
#define ID3D11DeviceContext3_UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags) (This)->lpVtbl->UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags)
#define ID3D11DeviceContext3_DiscardResource(This,pResource) (This)->lpVtbl->DiscardResource(This,pResource)
#define ID3D11DeviceContext3_DiscardView(This,pResourceView) (This)->lpVtbl->DiscardView(This,pResourceView)
#define ID3D11DeviceContext3_VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext3_SwapDeviceContextState(This,pState,ppPreviousState) (This)->lpVtbl->SwapDeviceContextState(This,pState,ppPreviousState)
#define ID3D11DeviceContext3_ClearView(This,pView,Color,pRect,NumRects) (This)->lpVtbl->ClearView(This,pView,Color,pRect,NumRects)
#define ID3D11DeviceContext3_DiscardView1(This,pResourceView,pRects,NumRects) (This)->lpVtbl->DiscardView1(This,pResourceView,pRects,NumRects)
/*** ID3D11DeviceContext2 methods ***/
#define ID3D11DeviceContext3_UpdateTileMappings(This,resource,region_count,region_start_coordinates,region_sizes,pool,range_count,range_flags,pool_start_offsets,range_tile_counts,flags) (This)->lpVtbl->UpdateTileMappings(This,resource,region_count,region_start_coordinates,region_sizes,pool,range_count,range_flags,pool_start_offsets,range_tile_counts,flags)
#define ID3D11DeviceContext3_CopyTileMappings(This,dst_resource,dst_start_coordinate,src_resource,src_start_coordinate,region_size,flags) (This)->lpVtbl->CopyTileMappings(This,dst_resource,dst_start_coordinate,src_resource,src_start_coordinate,region_size,flags)
#define ID3D11DeviceContext3_CopyTiles(This,resource,start_coordinate,size,buffer,start_offset,flags) (This)->lpVtbl->CopyTiles(This,resource,start_coordinate,size,buffer,start_offset,flags)
#define ID3D11DeviceContext3_UpdateTiles(This,dst_resource,dst_start_coordinate,dst_region_size,src_data,flags) (This)->lpVtbl->UpdateTiles(This,dst_resource,dst_start_coordinate,dst_region_size,src_data,flags)
#define ID3D11DeviceContext3_ResizeTilePool(This,pool,size) (This)->lpVtbl->ResizeTilePool(This,pool,size)
#define ID3D11DeviceContext3_TiledResourceBarrier(This,before_barrier,after_barrier) (This)->lpVtbl->TiledResourceBarrier(This,before_barrier,after_barrier)
#define ID3D11DeviceContext3_IsAnnotationEnabled(This) (This)->lpVtbl->IsAnnotationEnabled(This)
#define ID3D11DeviceContext3_SetMarkerInt(This,label,data) (This)->lpVtbl->SetMarkerInt(This,label,data)
#define ID3D11DeviceContext3_BeginEventInt(This,label,data) (This)->lpVtbl->BeginEventInt(This,label,data)
#define ID3D11DeviceContext3_EndEvent(This) (This)->lpVtbl->EndEvent(This)
/*** ID3D11DeviceContext3 methods ***/
#define ID3D11DeviceContext3_Flush1(This,type,event) (This)->lpVtbl->Flush1(This,type,event)
#define ID3D11DeviceContext3_SetHardwareProtectionState(This,enable) (This)->lpVtbl->SetHardwareProtectionState(This,enable)
#define ID3D11DeviceContext3_GetHardwareProtectionState(This,enable) (This)->lpVtbl->GetHardwareProtectionState(This,enable)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11DeviceContext3_QueryInterface(ID3D11DeviceContext3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11DeviceContext3_AddRef(ID3D11DeviceContext3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11DeviceContext3_Release(ID3D11DeviceContext3* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11DeviceContext3_GetDevice(ID3D11DeviceContext3* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11DeviceContext3_GetPrivateData(ID3D11DeviceContext3* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11DeviceContext3_SetPrivateData(ID3D11DeviceContext3* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11DeviceContext3_SetPrivateDataInterface(ID3D11DeviceContext3* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11DeviceContext methods ***/
static inline void ID3D11DeviceContext3_VSSetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_PSSetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_PSSetShader(ID3D11DeviceContext3* This,ID3D11PixelShader *pPixelShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext3_PSSetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_VSSetShader(ID3D11DeviceContext3* This,ID3D11VertexShader *pVertexShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext3_DrawIndexed(ID3D11DeviceContext3* This,UINT IndexCount,UINT StartIndexLocation,INT BaseVertexLocation) {
    This->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation);
}
static inline void ID3D11DeviceContext3_Draw(ID3D11DeviceContext3* This,UINT VertexCount,UINT StartVertexLocation) {
    This->lpVtbl->Draw(This,VertexCount,StartVertexLocation);
}
static inline HRESULT ID3D11DeviceContext3_Map(ID3D11DeviceContext3* This,ID3D11Resource *pResource,UINT Subresource,D3D11_MAP MapType,UINT MapFlags,D3D11_MAPPED_SUBRESOURCE *pMappedResource) {
    return This->lpVtbl->Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource);
}
static inline void ID3D11DeviceContext3_Unmap(ID3D11DeviceContext3* This,ID3D11Resource *pResource,UINT Subresource) {
    This->lpVtbl->Unmap(This,pResource,Subresource);
}
static inline void ID3D11DeviceContext3_PSSetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_IASetInputLayout(ID3D11DeviceContext3* This,ID3D11InputLayout *pInputLayout) {
    This->lpVtbl->IASetInputLayout(This,pInputLayout);
}
static inline void ID3D11DeviceContext3_IASetVertexBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppVertexBuffers,const UINT *pStrides,const UINT *pOffsets) {
    This->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static inline void ID3D11DeviceContext3_IASetIndexBuffer(ID3D11DeviceContext3* This,ID3D11Buffer *pIndexBuffer,DXGI_FORMAT Format,UINT Offset) {
    This->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static inline void ID3D11DeviceContext3_DrawIndexedInstanced(ID3D11DeviceContext3* This,UINT IndexCountPerInstance,UINT InstanceCount,UINT StartIndexLocation,INT BaseVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation);
}
static inline void ID3D11DeviceContext3_DrawInstanced(ID3D11DeviceContext3* This,UINT VertexCountPerInstance,UINT InstanceCount,UINT StartVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation);
}
static inline void ID3D11DeviceContext3_GSSetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_GSSetShader(ID3D11DeviceContext3* This,ID3D11GeometryShader *pShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->GSSetShader(This,pShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext3_IASetPrimitiveTopology(ID3D11DeviceContext3* This,D3D11_PRIMITIVE_TOPOLOGY Topology) {
    This->lpVtbl->IASetPrimitiveTopology(This,Topology);
}
static inline void ID3D11DeviceContext3_VSSetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_VSSetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_Begin(ID3D11DeviceContext3* This,ID3D11Asynchronous *pAsync) {
    This->lpVtbl->Begin(This,pAsync);
}
static inline void ID3D11DeviceContext3_End(ID3D11DeviceContext3* This,ID3D11Asynchronous *pAsync) {
    This->lpVtbl->End(This,pAsync);
}
static inline HRESULT ID3D11DeviceContext3_GetData(ID3D11DeviceContext3* This,ID3D11Asynchronous *pAsync,void *pData,UINT DataSize,UINT GetDataFlags) {
    return This->lpVtbl->GetData(This,pAsync,pData,DataSize,GetDataFlags);
}
static inline void ID3D11DeviceContext3_SetPredication(ID3D11DeviceContext3* This,ID3D11Predicate *pPredicate,WINBOOL PredicateValue) {
    This->lpVtbl->SetPredication(This,pPredicate,PredicateValue);
}
static inline void ID3D11DeviceContext3_GSSetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_GSSetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_OMSetRenderTargets(ID3D11DeviceContext3* This,UINT NumViews,ID3D11RenderTargetView *const *ppRenderTargetViews,ID3D11DepthStencilView *pDepthStencilView) {
    This->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView);
}
static inline void ID3D11DeviceContext3_OMSetRenderTargetsAndUnorderedAccessViews(ID3D11DeviceContext3* This,UINT NumRTVs,ID3D11RenderTargetView *const *ppRenderTargetViews,ID3D11DepthStencilView *pDepthStencilView,UINT UAVStartSlot,UINT NumUAVs,ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,const UINT *pUAVInitialCounts) {
    This->lpVtbl->OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts);
}
static inline void ID3D11DeviceContext3_OMSetBlendState(ID3D11DeviceContext3* This,ID3D11BlendState *pBlendState,const FLOAT BlendFactor[4],UINT SampleMask) {
    This->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask);
}
static inline void ID3D11DeviceContext3_OMSetDepthStencilState(ID3D11DeviceContext3* This,ID3D11DepthStencilState *pDepthStencilState,UINT StencilRef) {
    This->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef);
}
static inline void ID3D11DeviceContext3_SOSetTargets(ID3D11DeviceContext3* This,UINT NumBuffers,ID3D11Buffer *const *ppSOTargets,const UINT *pOffsets) {
    This->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets);
}
static inline void ID3D11DeviceContext3_DrawAuto(ID3D11DeviceContext3* This) {
    This->lpVtbl->DrawAuto(This);
}
static inline void ID3D11DeviceContext3_DrawIndexedInstancedIndirect(ID3D11DeviceContext3* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext3_DrawInstancedIndirect(ID3D11DeviceContext3* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext3_Dispatch(ID3D11DeviceContext3* This,UINT ThreadGroupCountX,UINT ThreadGroupCountY,UINT ThreadGroupCountZ) {
    This->lpVtbl->Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ);
}
static inline void ID3D11DeviceContext3_DispatchIndirect(ID3D11DeviceContext3* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext3_RSSetState(ID3D11DeviceContext3* This,ID3D11RasterizerState *pRasterizerState) {
    This->lpVtbl->RSSetState(This,pRasterizerState);
}
static inline void ID3D11DeviceContext3_RSSetViewports(ID3D11DeviceContext3* This,UINT NumViewports,const D3D11_VIEWPORT *pViewports) {
    This->lpVtbl->RSSetViewports(This,NumViewports,pViewports);
}
static inline void ID3D11DeviceContext3_RSSetScissorRects(ID3D11DeviceContext3* This,UINT NumRects,const D3D11_RECT *pRects) {
    This->lpVtbl->RSSetScissorRects(This,NumRects,pRects);
}
static inline void ID3D11DeviceContext3_CopySubresourceRegion(ID3D11DeviceContext3* This,ID3D11Resource *pDstResource,UINT DstSubresource,UINT DstX,UINT DstY,UINT DstZ,ID3D11Resource *pSrcResource,UINT SrcSubresource,const D3D11_BOX *pSrcBox) {
    This->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox);
}
static inline void ID3D11DeviceContext3_CopyResource(ID3D11DeviceContext3* This,ID3D11Resource *pDstResource,ID3D11Resource *pSrcResource) {
    This->lpVtbl->CopyResource(This,pDstResource,pSrcResource);
}
static inline void ID3D11DeviceContext3_UpdateSubresource(ID3D11DeviceContext3* This,ID3D11Resource *pDstResource,UINT DstSubresource,const D3D11_BOX *pDstBox,const void *pSrcData,UINT SrcRowPitch,UINT SrcDepthPitch) {
    This->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch);
}
static inline void ID3D11DeviceContext3_CopyStructureCount(ID3D11DeviceContext3* This,ID3D11Buffer *pDstBuffer,UINT DstAlignedByteOffset,ID3D11UnorderedAccessView *pSrcView) {
    This->lpVtbl->CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView);
}
static inline void ID3D11DeviceContext3_ClearRenderTargetView(ID3D11DeviceContext3* This,ID3D11RenderTargetView *pRenderTargetView,const FLOAT ColorRGBA[4]) {
    This->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA);
}
static inline void ID3D11DeviceContext3_ClearUnorderedAccessViewUint(ID3D11DeviceContext3* This,ID3D11UnorderedAccessView *pUnorderedAccessView,const UINT Values[4]) {
    This->lpVtbl->ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values);
}
static inline void ID3D11DeviceContext3_ClearUnorderedAccessViewFloat(ID3D11DeviceContext3* This,ID3D11UnorderedAccessView *pUnorderedAccessView,const FLOAT Values[4]) {
    This->lpVtbl->ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values);
}
static inline void ID3D11DeviceContext3_ClearDepthStencilView(ID3D11DeviceContext3* This,ID3D11DepthStencilView *pDepthStencilView,UINT ClearFlags,FLOAT Depth,UINT8 Stencil) {
    This->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil);
}
static inline void ID3D11DeviceContext3_GenerateMips(ID3D11DeviceContext3* This,ID3D11ShaderResourceView *pShaderResourceView) {
    This->lpVtbl->GenerateMips(This,pShaderResourceView);
}
static inline void ID3D11DeviceContext3_SetResourceMinLOD(ID3D11DeviceContext3* This,ID3D11Resource *pResource,FLOAT MinLOD) {
    This->lpVtbl->SetResourceMinLOD(This,pResource,MinLOD);
}
static inline FLOAT ID3D11DeviceContext3_GetResourceMinLOD(ID3D11DeviceContext3* This,ID3D11Resource *pResource) {
    return This->lpVtbl->GetResourceMinLOD(This,pResource);
}
static inline void ID3D11DeviceContext3_ResolveSubresource(ID3D11DeviceContext3* This,ID3D11Resource *pDstResource,UINT DstSubresource,ID3D11Resource *pSrcResource,UINT SrcSubresource,DXGI_FORMAT Format) {
    This->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format);
}
static inline void ID3D11DeviceContext3_ExecuteCommandList(ID3D11DeviceContext3* This,ID3D11CommandList *pCommandList,WINBOOL RestoreContextState) {
    This->lpVtbl->ExecuteCommandList(This,pCommandList,RestoreContextState);
}
static inline void ID3D11DeviceContext3_HSSetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_HSSetShader(ID3D11DeviceContext3* This,ID3D11HullShader *pHullShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext3_HSSetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_HSSetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_DSSetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_DSSetShader(ID3D11DeviceContext3* This,ID3D11DomainShader *pDomainShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext3_DSSetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_DSSetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_CSSetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_CSSetUnorderedAccessViews(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumUAVs,ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,const UINT *pUAVInitialCounts) {
    This->lpVtbl->CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts);
}
static inline void ID3D11DeviceContext3_CSSetShader(ID3D11DeviceContext3* This,ID3D11ComputeShader *pComputeShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext3_CSSetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_CSSetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_VSGetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_PSGetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_PSGetShader(ID3D11DeviceContext3* This,ID3D11PixelShader **ppPixelShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext3_PSGetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_VSGetShader(ID3D11DeviceContext3* This,ID3D11VertexShader **ppVertexShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext3_PSGetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_IAGetInputLayout(ID3D11DeviceContext3* This,ID3D11InputLayout **ppInputLayout) {
    This->lpVtbl->IAGetInputLayout(This,ppInputLayout);
}
static inline void ID3D11DeviceContext3_IAGetVertexBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppVertexBuffers,UINT *pStrides,UINT *pOffsets) {
    This->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static inline void ID3D11DeviceContext3_IAGetIndexBuffer(ID3D11DeviceContext3* This,ID3D11Buffer **pIndexBuffer,DXGI_FORMAT *Format,UINT *Offset) {
    This->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static inline void ID3D11DeviceContext3_GSGetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_GSGetShader(ID3D11DeviceContext3* This,ID3D11GeometryShader **ppGeometryShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext3_IAGetPrimitiveTopology(ID3D11DeviceContext3* This,D3D11_PRIMITIVE_TOPOLOGY *pTopology) {
    This->lpVtbl->IAGetPrimitiveTopology(This,pTopology);
}
static inline void ID3D11DeviceContext3_VSGetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_VSGetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_GetPredication(ID3D11DeviceContext3* This,ID3D11Predicate **ppPredicate,WINBOOL *pPredicateValue) {
    This->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue);
}
static inline void ID3D11DeviceContext3_GSGetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_GSGetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_OMGetRenderTargets(ID3D11DeviceContext3* This,UINT NumViews,ID3D11RenderTargetView **ppRenderTargetViews,ID3D11DepthStencilView **ppDepthStencilView) {
    This->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView);
}
static inline void ID3D11DeviceContext3_OMGetRenderTargetsAndUnorderedAccessViews(ID3D11DeviceContext3* This,UINT NumRTVs,ID3D11RenderTargetView **ppRenderTargetViews,ID3D11DepthStencilView **ppDepthStencilView,UINT UAVStartSlot,UINT NumUAVs,ID3D11UnorderedAccessView **ppUnorderedAccessViews) {
    This->lpVtbl->OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews);
}
static inline void ID3D11DeviceContext3_OMGetBlendState(ID3D11DeviceContext3* This,ID3D11BlendState **ppBlendState,FLOAT BlendFactor[4],UINT *pSampleMask) {
    This->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask);
}
static inline void ID3D11DeviceContext3_OMGetDepthStencilState(ID3D11DeviceContext3* This,ID3D11DepthStencilState **ppDepthStencilState,UINT *pStencilRef) {
    This->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef);
}
static inline void ID3D11DeviceContext3_SOGetTargets(ID3D11DeviceContext3* This,UINT NumBuffers,ID3D11Buffer **ppSOTargets) {
    This->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets);
}
static inline void ID3D11DeviceContext3_RSGetState(ID3D11DeviceContext3* This,ID3D11RasterizerState **ppRasterizerState) {
    This->lpVtbl->RSGetState(This,ppRasterizerState);
}
static inline void ID3D11DeviceContext3_RSGetViewports(ID3D11DeviceContext3* This,UINT *pNumViewports,D3D11_VIEWPORT *pViewports) {
    This->lpVtbl->RSGetViewports(This,pNumViewports,pViewports);
}
static inline void ID3D11DeviceContext3_RSGetScissorRects(ID3D11DeviceContext3* This,UINT *pNumRects,D3D11_RECT *pRects) {
    This->lpVtbl->RSGetScissorRects(This,pNumRects,pRects);
}
static inline void ID3D11DeviceContext3_HSGetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_HSGetShader(ID3D11DeviceContext3* This,ID3D11HullShader **ppHullShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext3_HSGetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_HSGetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_DSGetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_DSGetShader(ID3D11DeviceContext3* This,ID3D11DomainShader **ppDomainShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext3_DSGetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_DSGetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_CSGetShaderResources(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext3_CSGetUnorderedAccessViews(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumUAVs,ID3D11UnorderedAccessView **ppUnorderedAccessViews) {
    This->lpVtbl->CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews);
}
static inline void ID3D11DeviceContext3_CSGetShader(ID3D11DeviceContext3* This,ID3D11ComputeShader **ppComputeShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext3_CSGetSamplers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext3_CSGetConstantBuffers(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext3_ClearState(ID3D11DeviceContext3* This) {
    This->lpVtbl->ClearState(This);
}
static inline void ID3D11DeviceContext3_Flush(ID3D11DeviceContext3* This) {
    This->lpVtbl->Flush(This);
}
static inline D3D11_DEVICE_CONTEXT_TYPE ID3D11DeviceContext3_GetType(ID3D11DeviceContext3* This) {
    return This->lpVtbl->GetType(This);
}
static inline UINT ID3D11DeviceContext3_GetContextFlags(ID3D11DeviceContext3* This) {
    return This->lpVtbl->GetContextFlags(This);
}
static inline HRESULT ID3D11DeviceContext3_FinishCommandList(ID3D11DeviceContext3* This,WINBOOL RestoreDeferredContextState,ID3D11CommandList **ppCommandList) {
    return This->lpVtbl->FinishCommandList(This,RestoreDeferredContextState,ppCommandList);
}
/*** ID3D11DeviceContext1 methods ***/
static inline void ID3D11DeviceContext3_CopySubresourceRegion1(ID3D11DeviceContext3* This,ID3D11Resource *pDstResource,UINT DstSubresource,UINT DstX,UINT DstY,UINT DstZ,ID3D11Resource *pSrcResource,UINT SrcSubresource,const D3D11_BOX *pSrcBox,UINT CopyFlags) {
    This->lpVtbl->CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags);
}
static inline void ID3D11DeviceContext3_UpdateSubresource1(ID3D11DeviceContext3* This,ID3D11Resource *pDstResource,UINT DstSubresource,const D3D11_BOX *pDstBox,const void *pSrcData,UINT SrcRowPitch,UINT SrcDepthPitch,UINT CopyFlags) {
    This->lpVtbl->UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags);
}
static inline void ID3D11DeviceContext3_DiscardResource(ID3D11DeviceContext3* This,ID3D11Resource *pResource) {
    This->lpVtbl->DiscardResource(This,pResource);
}
static inline void ID3D11DeviceContext3_DiscardView(ID3D11DeviceContext3* This,ID3D11View *pResourceView) {
    This->lpVtbl->DiscardView(This,pResourceView);
}
static inline void ID3D11DeviceContext3_VSSetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_HSSetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_DSSetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_GSSetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_PSSetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_CSSetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_VSGetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_HSGetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_DSGetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_GSGetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_PSGetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_CSGetConstantBuffers1(ID3D11DeviceContext3* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext3_SwapDeviceContextState(ID3D11DeviceContext3* This,ID3DDeviceContextState *pState,ID3DDeviceContextState **ppPreviousState) {
    This->lpVtbl->SwapDeviceContextState(This,pState,ppPreviousState);
}
static inline void ID3D11DeviceContext3_ClearView(ID3D11DeviceContext3* This,ID3D11View *pView,const FLOAT Color[4],const D3D11_RECT *pRect,UINT NumRects) {
    This->lpVtbl->ClearView(This,pView,Color,pRect,NumRects);
}
static inline void ID3D11DeviceContext3_DiscardView1(ID3D11DeviceContext3* This,ID3D11View *pResourceView,const D3D11_RECT *pRects,UINT NumRects) {
    This->lpVtbl->DiscardView1(This,pResourceView,pRects,NumRects);
}
/*** ID3D11DeviceContext2 methods ***/
static inline HRESULT ID3D11DeviceContext3_UpdateTileMappings(ID3D11DeviceContext3* This,ID3D11Resource *resource,UINT region_count,const D3D11_TILED_RESOURCE_COORDINATE *region_start_coordinates,const D3D11_TILE_REGION_SIZE *region_sizes,ID3D11Buffer *pool,UINT range_count,const UINT *range_flags,const UINT *pool_start_offsets,const UINT *range_tile_counts,UINT flags) {
    return This->lpVtbl->UpdateTileMappings(This,resource,region_count,region_start_coordinates,region_sizes,pool,range_count,range_flags,pool_start_offsets,range_tile_counts,flags);
}
static inline HRESULT ID3D11DeviceContext3_CopyTileMappings(ID3D11DeviceContext3* This,ID3D11Resource *dst_resource,const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,ID3D11Resource *src_resource,const D3D11_TILED_RESOURCE_COORDINATE *src_start_coordinate,const D3D11_TILE_REGION_SIZE *region_size,UINT flags) {
    return This->lpVtbl->CopyTileMappings(This,dst_resource,dst_start_coordinate,src_resource,src_start_coordinate,region_size,flags);
}
static inline void ID3D11DeviceContext3_CopyTiles(ID3D11DeviceContext3* This,ID3D11Resource *resource,const D3D11_TILED_RESOURCE_COORDINATE *start_coordinate,const D3D11_TILE_REGION_SIZE *size,ID3D11Buffer *buffer,UINT64 start_offset,UINT flags) {
    This->lpVtbl->CopyTiles(This,resource,start_coordinate,size,buffer,start_offset,flags);
}
static inline void ID3D11DeviceContext3_UpdateTiles(ID3D11DeviceContext3* This,ID3D11Resource *dst_resource,const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,const D3D11_TILE_REGION_SIZE *dst_region_size,const void *src_data,UINT flags) {
    This->lpVtbl->UpdateTiles(This,dst_resource,dst_start_coordinate,dst_region_size,src_data,flags);
}
static inline HRESULT ID3D11DeviceContext3_ResizeTilePool(ID3D11DeviceContext3* This,ID3D11Buffer *pool,UINT64 size) {
    return This->lpVtbl->ResizeTilePool(This,pool,size);
}
static inline void ID3D11DeviceContext3_TiledResourceBarrier(ID3D11DeviceContext3* This,ID3D11DeviceChild *before_barrier,ID3D11DeviceChild *after_barrier) {
    This->lpVtbl->TiledResourceBarrier(This,before_barrier,after_barrier);
}
static inline WINBOOL ID3D11DeviceContext3_IsAnnotationEnabled(ID3D11DeviceContext3* This) {
    return This->lpVtbl->IsAnnotationEnabled(This);
}
static inline void ID3D11DeviceContext3_SetMarkerInt(ID3D11DeviceContext3* This,const WCHAR *label,int data) {
    This->lpVtbl->SetMarkerInt(This,label,data);
}
static inline void ID3D11DeviceContext3_BeginEventInt(ID3D11DeviceContext3* This,const WCHAR *label,int data) {
    This->lpVtbl->BeginEventInt(This,label,data);
}
static inline void ID3D11DeviceContext3_EndEvent(ID3D11DeviceContext3* This) {
    This->lpVtbl->EndEvent(This);
}
/*** ID3D11DeviceContext3 methods ***/
static inline void ID3D11DeviceContext3_Flush1(ID3D11DeviceContext3* This,D3D11_CONTEXT_TYPE type,HANDLE event) {
    This->lpVtbl->Flush1(This,type,event);
}
static inline void ID3D11DeviceContext3_SetHardwareProtectionState(ID3D11DeviceContext3* This,WINBOOL enable) {
    This->lpVtbl->SetHardwareProtectionState(This,enable);
}
static inline void ID3D11DeviceContext3_GetHardwareProtectionState(ID3D11DeviceContext3* This,WINBOOL *enable) {
    This->lpVtbl->GetHardwareProtectionState(This,enable);
}
#endif
#endif

#endif


#endif  /* __ID3D11DeviceContext3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Fence interface
 */
#ifndef __ID3D11Fence_INTERFACE_DEFINED__
#define __ID3D11Fence_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Fence, 0xaffde9d1, 0x1df7, 0x4bb7, 0x8a,0x34, 0x0f,0x46,0x25,0x1d,0xab,0x80);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("affde9d1-1df7-4bb7-8a34-0f46251dab80")
ID3D11Fence : public ID3D11DeviceChild
{
    virtual HRESULT STDMETHODCALLTYPE CreateSharedHandle(
        const SECURITY_ATTRIBUTES *attributes,
        DWORD access,
        const WCHAR *name,
        HANDLE *handle) = 0;

    virtual UINT64 STDMETHODCALLTYPE GetCompletedValue(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEventOnCompletion(
        UINT64 value,
        HANDLE event) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Fence, 0xaffde9d1, 0x1df7, 0x4bb7, 0x8a,0x34, 0x0f,0x46,0x25,0x1d,0xab,0x80)
#endif
#else
typedef struct ID3D11FenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Fence *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Fence *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Fence *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11Fence *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Fence *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Fence *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Fence *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11Fence methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateSharedHandle)(
        ID3D11Fence *This,
        const SECURITY_ATTRIBUTES *attributes,
        DWORD access,
        const WCHAR *name,
        HANDLE *handle);

    UINT64 (STDMETHODCALLTYPE *GetCompletedValue)(
        ID3D11Fence *This);

    HRESULT (STDMETHODCALLTYPE *SetEventOnCompletion)(
        ID3D11Fence *This,
        UINT64 value,
        HANDLE event);

    END_INTERFACE
} ID3D11FenceVtbl;

interface ID3D11Fence {
    CONST_VTBL ID3D11FenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Fence_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Fence_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Fence_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11Fence_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11Fence_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Fence_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Fence_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11Fence methods ***/
#define ID3D11Fence_CreateSharedHandle(This,attributes,access,name,handle) (This)->lpVtbl->CreateSharedHandle(This,attributes,access,name,handle)
#define ID3D11Fence_GetCompletedValue(This) (This)->lpVtbl->GetCompletedValue(This)
#define ID3D11Fence_SetEventOnCompletion(This,value,event) (This)->lpVtbl->SetEventOnCompletion(This,value,event)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11Fence_QueryInterface(ID3D11Fence* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11Fence_AddRef(ID3D11Fence* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11Fence_Release(ID3D11Fence* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11Fence_GetDevice(ID3D11Fence* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11Fence_GetPrivateData(ID3D11Fence* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11Fence_SetPrivateData(ID3D11Fence* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11Fence_SetPrivateDataInterface(ID3D11Fence* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11Fence methods ***/
static inline HRESULT ID3D11Fence_CreateSharedHandle(ID3D11Fence* This,const SECURITY_ATTRIBUTES *attributes,DWORD access,const WCHAR *name,HANDLE *handle) {
    return This->lpVtbl->CreateSharedHandle(This,attributes,access,name,handle);
}
static inline UINT64 ID3D11Fence_GetCompletedValue(ID3D11Fence* This) {
    return This->lpVtbl->GetCompletedValue(This);
}
static inline HRESULT ID3D11Fence_SetEventOnCompletion(ID3D11Fence* This,UINT64 value,HANDLE event) {
    return This->lpVtbl->SetEventOnCompletion(This,value,event);
}
#endif
#endif

#endif


#endif  /* __ID3D11Fence_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11DeviceContext4 interface
 */
#ifndef __ID3D11DeviceContext4_INTERFACE_DEFINED__
#define __ID3D11DeviceContext4_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11DeviceContext4, 0x917600da, 0xf58c, 0x4c33, 0x98,0xd8, 0x3e,0x15,0xb3,0x90,0xfa,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("917600da-f58c-4c33-98d8-3e15b390fa24")
ID3D11DeviceContext4 : public ID3D11DeviceContext3
{
    virtual HRESULT STDMETHODCALLTYPE Signal(
        ID3D11Fence *fence,
        UINT64 value) = 0;

    virtual HRESULT STDMETHODCALLTYPE Wait(
        ID3D11Fence *fence,
        UINT64 value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11DeviceContext4, 0x917600da, 0xf58c, 0x4c33, 0x98,0xd8, 0x3e,0x15,0xb3,0x90,0xfa,0x24)
#endif
#else
typedef struct ID3D11DeviceContext4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11DeviceContext4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11DeviceContext4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11DeviceContext4 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11DeviceContext4 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11DeviceContext4 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11DeviceContext4 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11DeviceContext4 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11DeviceContext methods ***/
    void (STDMETHODCALLTYPE *VSSetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSSetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSSetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11PixelShader *pPixelShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *PSSetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *VSSetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11VertexShader *pVertexShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *DrawIndexed)(
        ID3D11DeviceContext4 *This,
        UINT IndexCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation);

    void (STDMETHODCALLTYPE *Draw)(
        ID3D11DeviceContext4 *This,
        UINT VertexCount,
        UINT StartVertexLocation);

    HRESULT (STDMETHODCALLTYPE *Map)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *pResource,
        UINT Subresource,
        D3D11_MAP MapType,
        UINT MapFlags,
        D3D11_MAPPED_SUBRESOURCE *pMappedResource);

    void (STDMETHODCALLTYPE *Unmap)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *pResource,
        UINT Subresource);

    void (STDMETHODCALLTYPE *PSSetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *IASetInputLayout)(
        ID3D11DeviceContext4 *This,
        ID3D11InputLayout *pInputLayout);

    void (STDMETHODCALLTYPE *IASetVertexBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppVertexBuffers,
        const UINT *pStrides,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *IASetIndexBuffer)(
        ID3D11DeviceContext4 *This,
        ID3D11Buffer *pIndexBuffer,
        DXGI_FORMAT Format,
        UINT Offset);

    void (STDMETHODCALLTYPE *DrawIndexedInstanced)(
        ID3D11DeviceContext4 *This,
        UINT IndexCountPerInstance,
        UINT InstanceCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *DrawInstanced)(
        ID3D11DeviceContext4 *This,
        UINT VertexCountPerInstance,
        UINT InstanceCount,
        UINT StartVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *GSSetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSSetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11GeometryShader *pShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *IASetPrimitiveTopology)(
        ID3D11DeviceContext4 *This,
        D3D11_PRIMITIVE_TOPOLOGY Topology);

    void (STDMETHODCALLTYPE *VSSetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSSetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *Begin)(
        ID3D11DeviceContext4 *This,
        ID3D11Asynchronous *pAsync);

    void (STDMETHODCALLTYPE *End)(
        ID3D11DeviceContext4 *This,
        ID3D11Asynchronous *pAsync);

    HRESULT (STDMETHODCALLTYPE *GetData)(
        ID3D11DeviceContext4 *This,
        ID3D11Asynchronous *pAsync,
        void *pData,
        UINT DataSize,
        UINT GetDataFlags);

    void (STDMETHODCALLTYPE *SetPredication)(
        ID3D11DeviceContext4 *This,
        ID3D11Predicate *pPredicate,
        WINBOOL PredicateValue);

    void (STDMETHODCALLTYPE *GSSetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSSetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *OMSetRenderTargets)(
        ID3D11DeviceContext4 *This,
        UINT NumViews,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView);

    void (STDMETHODCALLTYPE *OMSetRenderTargetsAndUnorderedAccessViews)(
        ID3D11DeviceContext4 *This,
        UINT NumRTVs,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts);

    void (STDMETHODCALLTYPE *OMSetBlendState)(
        ID3D11DeviceContext4 *This,
        ID3D11BlendState *pBlendState,
        const FLOAT BlendFactor[4],
        UINT SampleMask);

    void (STDMETHODCALLTYPE *OMSetDepthStencilState)(
        ID3D11DeviceContext4 *This,
        ID3D11DepthStencilState *pDepthStencilState,
        UINT StencilRef);

    void (STDMETHODCALLTYPE *SOSetTargets)(
        ID3D11DeviceContext4 *This,
        UINT NumBuffers,
        ID3D11Buffer *const *ppSOTargets,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *DrawAuto)(
        ID3D11DeviceContext4 *This);

    void (STDMETHODCALLTYPE *DrawIndexedInstancedIndirect)(
        ID3D11DeviceContext4 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *DrawInstancedIndirect)(
        ID3D11DeviceContext4 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *Dispatch)(
        ID3D11DeviceContext4 *This,
        UINT ThreadGroupCountX,
        UINT ThreadGroupCountY,
        UINT ThreadGroupCountZ);

    void (STDMETHODCALLTYPE *DispatchIndirect)(
        ID3D11DeviceContext4 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *RSSetState)(
        ID3D11DeviceContext4 *This,
        ID3D11RasterizerState *pRasterizerState);

    void (STDMETHODCALLTYPE *RSSetViewports)(
        ID3D11DeviceContext4 *This,
        UINT NumViewports,
        const D3D11_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSSetScissorRects)(
        ID3D11DeviceContext4 *This,
        UINT NumRects,
        const D3D11_RECT *pRects);

    void (STDMETHODCALLTYPE *CopySubresourceRegion)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D11_BOX *pSrcBox);

    void (STDMETHODCALLTYPE *CopyResource)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *pDstResource,
        ID3D11Resource *pSrcResource);

    void (STDMETHODCALLTYPE *UpdateSubresource)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        const D3D11_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch);

    void (STDMETHODCALLTYPE *CopyStructureCount)(
        ID3D11DeviceContext4 *This,
        ID3D11Buffer *pDstBuffer,
        UINT DstAlignedByteOffset,
        ID3D11UnorderedAccessView *pSrcView);

    void (STDMETHODCALLTYPE *ClearRenderTargetView)(
        ID3D11DeviceContext4 *This,
        ID3D11RenderTargetView *pRenderTargetView,
        const FLOAT ColorRGBA[4]);

    void (STDMETHODCALLTYPE *ClearUnorderedAccessViewUint)(
        ID3D11DeviceContext4 *This,
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const UINT Values[4]);

    void (STDMETHODCALLTYPE *ClearUnorderedAccessViewFloat)(
        ID3D11DeviceContext4 *This,
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const FLOAT Values[4]);

    void (STDMETHODCALLTYPE *ClearDepthStencilView)(
        ID3D11DeviceContext4 *This,
        ID3D11DepthStencilView *pDepthStencilView,
        UINT ClearFlags,
        FLOAT Depth,
        UINT8 Stencil);

    void (STDMETHODCALLTYPE *GenerateMips)(
        ID3D11DeviceContext4 *This,
        ID3D11ShaderResourceView *pShaderResourceView);

    void (STDMETHODCALLTYPE *SetResourceMinLOD)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *pResource,
        FLOAT MinLOD);

    FLOAT (STDMETHODCALLTYPE *GetResourceMinLOD)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *pResource);

    void (STDMETHODCALLTYPE *ResolveSubresource)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        DXGI_FORMAT Format);

    void (STDMETHODCALLTYPE *ExecuteCommandList)(
        ID3D11DeviceContext4 *This,
        ID3D11CommandList *pCommandList,
        WINBOOL RestoreContextState);

    void (STDMETHODCALLTYPE *HSSetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *HSSetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11HullShader *pHullShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *HSSetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *HSSetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *DSSetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *DSSetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11DomainShader *pDomainShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *DSSetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *DSSetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *CSSetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *CSSetUnorderedAccessViews)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts);

    void (STDMETHODCALLTYPE *CSSetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11ComputeShader *pComputeShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *CSSetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *CSSetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *VSGetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSGetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSGetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11PixelShader **ppPixelShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *PSGetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *VSGetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11VertexShader **ppVertexShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *PSGetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *IAGetInputLayout)(
        ID3D11DeviceContext4 *This,
        ID3D11InputLayout **ppInputLayout);

    void (STDMETHODCALLTYPE *IAGetVertexBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppVertexBuffers,
        UINT *pStrides,
        UINT *pOffsets);

    void (STDMETHODCALLTYPE *IAGetIndexBuffer)(
        ID3D11DeviceContext4 *This,
        ID3D11Buffer **pIndexBuffer,
        DXGI_FORMAT *Format,
        UINT *Offset);

    void (STDMETHODCALLTYPE *GSGetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSGetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11GeometryShader **ppGeometryShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *IAGetPrimitiveTopology)(
        ID3D11DeviceContext4 *This,
        D3D11_PRIMITIVE_TOPOLOGY *pTopology);

    void (STDMETHODCALLTYPE *VSGetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSGetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *GetPredication)(
        ID3D11DeviceContext4 *This,
        ID3D11Predicate **ppPredicate,
        WINBOOL *pPredicateValue);

    void (STDMETHODCALLTYPE *GSGetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSGetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *OMGetRenderTargets)(
        ID3D11DeviceContext4 *This,
        UINT NumViews,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView);

    void (STDMETHODCALLTYPE *OMGetRenderTargetsAndUnorderedAccessViews)(
        ID3D11DeviceContext4 *This,
        UINT NumRTVs,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews);

    void (STDMETHODCALLTYPE *OMGetBlendState)(
        ID3D11DeviceContext4 *This,
        ID3D11BlendState **ppBlendState,
        FLOAT BlendFactor[4],
        UINT *pSampleMask);

    void (STDMETHODCALLTYPE *OMGetDepthStencilState)(
        ID3D11DeviceContext4 *This,
        ID3D11DepthStencilState **ppDepthStencilState,
        UINT *pStencilRef);

    void (STDMETHODCALLTYPE *SOGetTargets)(
        ID3D11DeviceContext4 *This,
        UINT NumBuffers,
        ID3D11Buffer **ppSOTargets);

    void (STDMETHODCALLTYPE *RSGetState)(
        ID3D11DeviceContext4 *This,
        ID3D11RasterizerState **ppRasterizerState);

    void (STDMETHODCALLTYPE *RSGetViewports)(
        ID3D11DeviceContext4 *This,
        UINT *pNumViewports,
        D3D11_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSGetScissorRects)(
        ID3D11DeviceContext4 *This,
        UINT *pNumRects,
        D3D11_RECT *pRects);

    void (STDMETHODCALLTYPE *HSGetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *HSGetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11HullShader **ppHullShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *HSGetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *HSGetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *DSGetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *DSGetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11DomainShader **ppDomainShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *DSGetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *DSGetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *CSGetShaderResources)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *CSGetUnorderedAccessViews)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews);

    void (STDMETHODCALLTYPE *CSGetShader)(
        ID3D11DeviceContext4 *This,
        ID3D11ComputeShader **ppComputeShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *CSGetSamplers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *CSGetConstantBuffers)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *ClearState)(
        ID3D11DeviceContext4 *This);

    void (STDMETHODCALLTYPE *Flush)(
        ID3D11DeviceContext4 *This);

    D3D11_DEVICE_CONTEXT_TYPE (STDMETHODCALLTYPE *GetType)(
        ID3D11DeviceContext4 *This);

    UINT (STDMETHODCALLTYPE *GetContextFlags)(
        ID3D11DeviceContext4 *This);

    HRESULT (STDMETHODCALLTYPE *FinishCommandList)(
        ID3D11DeviceContext4 *This,
        WINBOOL RestoreDeferredContextState,
        ID3D11CommandList **ppCommandList);

    /*** ID3D11DeviceContext1 methods ***/
    void (STDMETHODCALLTYPE *CopySubresourceRegion1)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D11_BOX *pSrcBox,
        UINT CopyFlags);

    void (STDMETHODCALLTYPE *UpdateSubresource1)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        const D3D11_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch,
        UINT CopyFlags);

    void (STDMETHODCALLTYPE *DiscardResource)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *pResource);

    void (STDMETHODCALLTYPE *DiscardView)(
        ID3D11DeviceContext4 *This,
        ID3D11View *pResourceView);

    void (STDMETHODCALLTYPE *VSSetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *HSSetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *DSSetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *GSSetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *PSSetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *CSSetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *VSGetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *HSGetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *DSGetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *GSGetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *PSGetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *CSGetConstantBuffers1)(
        ID3D11DeviceContext4 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *SwapDeviceContextState)(
        ID3D11DeviceContext4 *This,
        ID3DDeviceContextState *pState,
        ID3DDeviceContextState **ppPreviousState);

    void (STDMETHODCALLTYPE *ClearView)(
        ID3D11DeviceContext4 *This,
        ID3D11View *pView,
        const FLOAT Color[4],
        const D3D11_RECT *pRect,
        UINT NumRects);

    void (STDMETHODCALLTYPE *DiscardView1)(
        ID3D11DeviceContext4 *This,
        ID3D11View *pResourceView,
        const D3D11_RECT *pRects,
        UINT NumRects);

    /*** ID3D11DeviceContext2 methods ***/
    HRESULT (STDMETHODCALLTYPE *UpdateTileMappings)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *resource,
        UINT region_count,
        const D3D11_TILED_RESOURCE_COORDINATE *region_start_coordinates,
        const D3D11_TILE_REGION_SIZE *region_sizes,
        ID3D11Buffer *pool,
        UINT range_count,
        const UINT *range_flags,
        const UINT *pool_start_offsets,
        const UINT *range_tile_counts,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *CopyTileMappings)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *dst_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,
        ID3D11Resource *src_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *src_start_coordinate,
        const D3D11_TILE_REGION_SIZE *region_size,
        UINT flags);

    void (STDMETHODCALLTYPE *CopyTiles)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *resource,
        const D3D11_TILED_RESOURCE_COORDINATE *start_coordinate,
        const D3D11_TILE_REGION_SIZE *size,
        ID3D11Buffer *buffer,
        UINT64 start_offset,
        UINT flags);

    void (STDMETHODCALLTYPE *UpdateTiles)(
        ID3D11DeviceContext4 *This,
        ID3D11Resource *dst_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,
        const D3D11_TILE_REGION_SIZE *dst_region_size,
        const void *src_data,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *ResizeTilePool)(
        ID3D11DeviceContext4 *This,
        ID3D11Buffer *pool,
        UINT64 size);

    void (STDMETHODCALLTYPE *TiledResourceBarrier)(
        ID3D11DeviceContext4 *This,
        ID3D11DeviceChild *before_barrier,
        ID3D11DeviceChild *after_barrier);

    WINBOOL (STDMETHODCALLTYPE *IsAnnotationEnabled)(
        ID3D11DeviceContext4 *This);

    void (STDMETHODCALLTYPE *SetMarkerInt)(
        ID3D11DeviceContext4 *This,
        const WCHAR *label,
        int data);

    void (STDMETHODCALLTYPE *BeginEventInt)(
        ID3D11DeviceContext4 *This,
        const WCHAR *label,
        int data);

    void (STDMETHODCALLTYPE *EndEvent)(
        ID3D11DeviceContext4 *This);

    /*** ID3D11DeviceContext3 methods ***/
    void (STDMETHODCALLTYPE *Flush1)(
        ID3D11DeviceContext4 *This,
        D3D11_CONTEXT_TYPE type,
        HANDLE event);

    void (STDMETHODCALLTYPE *SetHardwareProtectionState)(
        ID3D11DeviceContext4 *This,
        WINBOOL enable);

    void (STDMETHODCALLTYPE *GetHardwareProtectionState)(
        ID3D11DeviceContext4 *This,
        WINBOOL *enable);

    /*** ID3D11DeviceContext4 methods ***/
    HRESULT (STDMETHODCALLTYPE *Signal)(
        ID3D11DeviceContext4 *This,
        ID3D11Fence *fence,
        UINT64 value);

    HRESULT (STDMETHODCALLTYPE *Wait)(
        ID3D11DeviceContext4 *This,
        ID3D11Fence *fence,
        UINT64 value);

    END_INTERFACE
} ID3D11DeviceContext4Vtbl;

interface ID3D11DeviceContext4 {
    CONST_VTBL ID3D11DeviceContext4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11DeviceContext4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11DeviceContext4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11DeviceContext4_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11DeviceContext4_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11DeviceContext4_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11DeviceContext4_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11DeviceContext4_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11DeviceContext methods ***/
#define ID3D11DeviceContext4_VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext4_PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext4_DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation) (This)->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation)
#define ID3D11DeviceContext4_Draw(This,VertexCount,StartVertexLocation) (This)->lpVtbl->Draw(This,VertexCount,StartVertexLocation)
#define ID3D11DeviceContext4_Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource) (This)->lpVtbl->Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource)
#define ID3D11DeviceContext4_Unmap(This,pResource,Subresource) (This)->lpVtbl->Unmap(This,pResource,Subresource)
#define ID3D11DeviceContext4_PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_IASetInputLayout(This,pInputLayout) (This)->lpVtbl->IASetInputLayout(This,pInputLayout)
#define ID3D11DeviceContext4_IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D11DeviceContext4_IASetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D11DeviceContext4_DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation)
#define ID3D11DeviceContext4_DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation)
#define ID3D11DeviceContext4_GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_GSSetShader(This,pShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->GSSetShader(This,pShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext4_IASetPrimitiveTopology(This,Topology) (This)->lpVtbl->IASetPrimitiveTopology(This,Topology)
#define ID3D11DeviceContext4_VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_Begin(This,pAsync) (This)->lpVtbl->Begin(This,pAsync)
#define ID3D11DeviceContext4_End(This,pAsync) (This)->lpVtbl->End(This,pAsync)
#define ID3D11DeviceContext4_GetData(This,pAsync,pData,DataSize,GetDataFlags) (This)->lpVtbl->GetData(This,pAsync,pData,DataSize,GetDataFlags)
#define ID3D11DeviceContext4_SetPredication(This,pPredicate,PredicateValue) (This)->lpVtbl->SetPredication(This,pPredicate,PredicateValue)
#define ID3D11DeviceContext4_GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView) (This)->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView)
#define ID3D11DeviceContext4_OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts) (This)->lpVtbl->OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts)
#define ID3D11DeviceContext4_OMSetBlendState(This,pBlendState,BlendFactor,SampleMask) (This)->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask)
#define ID3D11DeviceContext4_OMSetDepthStencilState(This,pDepthStencilState,StencilRef) (This)->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef)
#define ID3D11DeviceContext4_SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets) (This)->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets)
#define ID3D11DeviceContext4_DrawAuto(This) (This)->lpVtbl->DrawAuto(This)
#define ID3D11DeviceContext4_DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext4_DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext4_Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ) (This)->lpVtbl->Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ)
#define ID3D11DeviceContext4_DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext4_RSSetState(This,pRasterizerState) (This)->lpVtbl->RSSetState(This,pRasterizerState)
#define ID3D11DeviceContext4_RSSetViewports(This,NumViewports,pViewports) (This)->lpVtbl->RSSetViewports(This,NumViewports,pViewports)
#define ID3D11DeviceContext4_RSSetScissorRects(This,NumRects,pRects) (This)->lpVtbl->RSSetScissorRects(This,NumRects,pRects)
#define ID3D11DeviceContext4_CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox) (This)->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox)
#define ID3D11DeviceContext4_CopyResource(This,pDstResource,pSrcResource) (This)->lpVtbl->CopyResource(This,pDstResource,pSrcResource)
#define ID3D11DeviceContext4_UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch) (This)->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch)
#define ID3D11DeviceContext4_CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView) (This)->lpVtbl->CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView)
#define ID3D11DeviceContext4_ClearRenderTargetView(This,pRenderTargetView,ColorRGBA) (This)->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA)
#define ID3D11DeviceContext4_ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values) (This)->lpVtbl->ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values)
#define ID3D11DeviceContext4_ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values) (This)->lpVtbl->ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values)
#define ID3D11DeviceContext4_ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil) (This)->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil)
#define ID3D11DeviceContext4_GenerateMips(This,pShaderResourceView) (This)->lpVtbl->GenerateMips(This,pShaderResourceView)
#define ID3D11DeviceContext4_SetResourceMinLOD(This,pResource,MinLOD) (This)->lpVtbl->SetResourceMinLOD(This,pResource,MinLOD)
#define ID3D11DeviceContext4_GetResourceMinLOD(This,pResource) (This)->lpVtbl->GetResourceMinLOD(This,pResource)
#define ID3D11DeviceContext4_ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format) (This)->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format)
#define ID3D11DeviceContext4_ExecuteCommandList(This,pCommandList,RestoreContextState) (This)->lpVtbl->ExecuteCommandList(This,pCommandList,RestoreContextState)
#define ID3D11DeviceContext4_HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext4_HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext4_DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts) (This)->lpVtbl->CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts)
#define ID3D11DeviceContext4_CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext4_CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext4_PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext4_PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_IAGetInputLayout(This,ppInputLayout) (This)->lpVtbl->IAGetInputLayout(This,ppInputLayout)
#define ID3D11DeviceContext4_IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D11DeviceContext4_IAGetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D11DeviceContext4_GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext4_IAGetPrimitiveTopology(This,pTopology) (This)->lpVtbl->IAGetPrimitiveTopology(This,pTopology)
#define ID3D11DeviceContext4_VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_GetPredication(This,ppPredicate,pPredicateValue) (This)->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue)
#define ID3D11DeviceContext4_GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView) (This)->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView)
#define ID3D11DeviceContext4_OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews) (This)->lpVtbl->OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews)
#define ID3D11DeviceContext4_OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask) (This)->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask)
#define ID3D11DeviceContext4_OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef) (This)->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef)
#define ID3D11DeviceContext4_SOGetTargets(This,NumBuffers,ppSOTargets) (This)->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets)
#define ID3D11DeviceContext4_RSGetState(This,ppRasterizerState) (This)->lpVtbl->RSGetState(This,ppRasterizerState)
#define ID3D11DeviceContext4_RSGetViewports(This,pNumViewports,pViewports) (This)->lpVtbl->RSGetViewports(This,pNumViewports,pViewports)
#define ID3D11DeviceContext4_RSGetScissorRects(This,pNumRects,pRects) (This)->lpVtbl->RSGetScissorRects(This,pNumRects,pRects)
#define ID3D11DeviceContext4_HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext4_HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext4_DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext4_CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews) (This)->lpVtbl->CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews)
#define ID3D11DeviceContext4_CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext4_CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext4_CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext4_ClearState(This) (This)->lpVtbl->ClearState(This)
#define ID3D11DeviceContext4_Flush(This) (This)->lpVtbl->Flush(This)
#define ID3D11DeviceContext4_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D11DeviceContext4_GetContextFlags(This) (This)->lpVtbl->GetContextFlags(This)
#define ID3D11DeviceContext4_FinishCommandList(This,RestoreDeferredContextState,ppCommandList) (This)->lpVtbl->FinishCommandList(This,RestoreDeferredContextState,ppCommandList)
/*** ID3D11DeviceContext1 methods ***/
#define ID3D11DeviceContext4_CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags) (This)->lpVtbl->CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags)
#define ID3D11DeviceContext4_UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags) (This)->lpVtbl->UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags)
#define ID3D11DeviceContext4_DiscardResource(This,pResource) (This)->lpVtbl->DiscardResource(This,pResource)
#define ID3D11DeviceContext4_DiscardView(This,pResourceView) (This)->lpVtbl->DiscardView(This,pResourceView)
#define ID3D11DeviceContext4_VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext4_SwapDeviceContextState(This,pState,ppPreviousState) (This)->lpVtbl->SwapDeviceContextState(This,pState,ppPreviousState)
#define ID3D11DeviceContext4_ClearView(This,pView,Color,pRect,NumRects) (This)->lpVtbl->ClearView(This,pView,Color,pRect,NumRects)
#define ID3D11DeviceContext4_DiscardView1(This,pResourceView,pRects,NumRects) (This)->lpVtbl->DiscardView1(This,pResourceView,pRects,NumRects)
/*** ID3D11DeviceContext2 methods ***/
#define ID3D11DeviceContext4_UpdateTileMappings(This,resource,region_count,region_start_coordinates,region_sizes,pool,range_count,range_flags,pool_start_offsets,range_tile_counts,flags) (This)->lpVtbl->UpdateTileMappings(This,resource,region_count,region_start_coordinates,region_sizes,pool,range_count,range_flags,pool_start_offsets,range_tile_counts,flags)
#define ID3D11DeviceContext4_CopyTileMappings(This,dst_resource,dst_start_coordinate,src_resource,src_start_coordinate,region_size,flags) (This)->lpVtbl->CopyTileMappings(This,dst_resource,dst_start_coordinate,src_resource,src_start_coordinate,region_size,flags)
#define ID3D11DeviceContext4_CopyTiles(This,resource,start_coordinate,size,buffer,start_offset,flags) (This)->lpVtbl->CopyTiles(This,resource,start_coordinate,size,buffer,start_offset,flags)
#define ID3D11DeviceContext4_UpdateTiles(This,dst_resource,dst_start_coordinate,dst_region_size,src_data,flags) (This)->lpVtbl->UpdateTiles(This,dst_resource,dst_start_coordinate,dst_region_size,src_data,flags)
#define ID3D11DeviceContext4_ResizeTilePool(This,pool,size) (This)->lpVtbl->ResizeTilePool(This,pool,size)
#define ID3D11DeviceContext4_TiledResourceBarrier(This,before_barrier,after_barrier) (This)->lpVtbl->TiledResourceBarrier(This,before_barrier,after_barrier)
#define ID3D11DeviceContext4_IsAnnotationEnabled(This) (This)->lpVtbl->IsAnnotationEnabled(This)
#define ID3D11DeviceContext4_SetMarkerInt(This,label,data) (This)->lpVtbl->SetMarkerInt(This,label,data)
#define ID3D11DeviceContext4_BeginEventInt(This,label,data) (This)->lpVtbl->BeginEventInt(This,label,data)
#define ID3D11DeviceContext4_EndEvent(This) (This)->lpVtbl->EndEvent(This)
/*** ID3D11DeviceContext3 methods ***/
#define ID3D11DeviceContext4_Flush1(This,type,event) (This)->lpVtbl->Flush1(This,type,event)
#define ID3D11DeviceContext4_SetHardwareProtectionState(This,enable) (This)->lpVtbl->SetHardwareProtectionState(This,enable)
#define ID3D11DeviceContext4_GetHardwareProtectionState(This,enable) (This)->lpVtbl->GetHardwareProtectionState(This,enable)
/*** ID3D11DeviceContext4 methods ***/
#define ID3D11DeviceContext4_Signal(This,fence,value) (This)->lpVtbl->Signal(This,fence,value)
#define ID3D11DeviceContext4_Wait(This,fence,value) (This)->lpVtbl->Wait(This,fence,value)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11DeviceContext4_QueryInterface(ID3D11DeviceContext4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11DeviceContext4_AddRef(ID3D11DeviceContext4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11DeviceContext4_Release(ID3D11DeviceContext4* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11DeviceContext4_GetDevice(ID3D11DeviceContext4* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11DeviceContext4_GetPrivateData(ID3D11DeviceContext4* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11DeviceContext4_SetPrivateData(ID3D11DeviceContext4* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11DeviceContext4_SetPrivateDataInterface(ID3D11DeviceContext4* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11DeviceContext methods ***/
static inline void ID3D11DeviceContext4_VSSetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_PSSetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_PSSetShader(ID3D11DeviceContext4* This,ID3D11PixelShader *pPixelShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext4_PSSetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_VSSetShader(ID3D11DeviceContext4* This,ID3D11VertexShader *pVertexShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext4_DrawIndexed(ID3D11DeviceContext4* This,UINT IndexCount,UINT StartIndexLocation,INT BaseVertexLocation) {
    This->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation);
}
static inline void ID3D11DeviceContext4_Draw(ID3D11DeviceContext4* This,UINT VertexCount,UINT StartVertexLocation) {
    This->lpVtbl->Draw(This,VertexCount,StartVertexLocation);
}
static inline HRESULT ID3D11DeviceContext4_Map(ID3D11DeviceContext4* This,ID3D11Resource *pResource,UINT Subresource,D3D11_MAP MapType,UINT MapFlags,D3D11_MAPPED_SUBRESOURCE *pMappedResource) {
    return This->lpVtbl->Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource);
}
static inline void ID3D11DeviceContext4_Unmap(ID3D11DeviceContext4* This,ID3D11Resource *pResource,UINT Subresource) {
    This->lpVtbl->Unmap(This,pResource,Subresource);
}
static inline void ID3D11DeviceContext4_PSSetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_IASetInputLayout(ID3D11DeviceContext4* This,ID3D11InputLayout *pInputLayout) {
    This->lpVtbl->IASetInputLayout(This,pInputLayout);
}
static inline void ID3D11DeviceContext4_IASetVertexBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppVertexBuffers,const UINT *pStrides,const UINT *pOffsets) {
    This->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static inline void ID3D11DeviceContext4_IASetIndexBuffer(ID3D11DeviceContext4* This,ID3D11Buffer *pIndexBuffer,DXGI_FORMAT Format,UINT Offset) {
    This->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static inline void ID3D11DeviceContext4_DrawIndexedInstanced(ID3D11DeviceContext4* This,UINT IndexCountPerInstance,UINT InstanceCount,UINT StartIndexLocation,INT BaseVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation);
}
static inline void ID3D11DeviceContext4_DrawInstanced(ID3D11DeviceContext4* This,UINT VertexCountPerInstance,UINT InstanceCount,UINT StartVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation);
}
static inline void ID3D11DeviceContext4_GSSetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_GSSetShader(ID3D11DeviceContext4* This,ID3D11GeometryShader *pShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->GSSetShader(This,pShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext4_IASetPrimitiveTopology(ID3D11DeviceContext4* This,D3D11_PRIMITIVE_TOPOLOGY Topology) {
    This->lpVtbl->IASetPrimitiveTopology(This,Topology);
}
static inline void ID3D11DeviceContext4_VSSetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_VSSetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_Begin(ID3D11DeviceContext4* This,ID3D11Asynchronous *pAsync) {
    This->lpVtbl->Begin(This,pAsync);
}
static inline void ID3D11DeviceContext4_End(ID3D11DeviceContext4* This,ID3D11Asynchronous *pAsync) {
    This->lpVtbl->End(This,pAsync);
}
static inline HRESULT ID3D11DeviceContext4_GetData(ID3D11DeviceContext4* This,ID3D11Asynchronous *pAsync,void *pData,UINT DataSize,UINT GetDataFlags) {
    return This->lpVtbl->GetData(This,pAsync,pData,DataSize,GetDataFlags);
}
static inline void ID3D11DeviceContext4_SetPredication(ID3D11DeviceContext4* This,ID3D11Predicate *pPredicate,WINBOOL PredicateValue) {
    This->lpVtbl->SetPredication(This,pPredicate,PredicateValue);
}
static inline void ID3D11DeviceContext4_GSSetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_GSSetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_OMSetRenderTargets(ID3D11DeviceContext4* This,UINT NumViews,ID3D11RenderTargetView *const *ppRenderTargetViews,ID3D11DepthStencilView *pDepthStencilView) {
    This->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView);
}
static inline void ID3D11DeviceContext4_OMSetRenderTargetsAndUnorderedAccessViews(ID3D11DeviceContext4* This,UINT NumRTVs,ID3D11RenderTargetView *const *ppRenderTargetViews,ID3D11DepthStencilView *pDepthStencilView,UINT UAVStartSlot,UINT NumUAVs,ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,const UINT *pUAVInitialCounts) {
    This->lpVtbl->OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts);
}
static inline void ID3D11DeviceContext4_OMSetBlendState(ID3D11DeviceContext4* This,ID3D11BlendState *pBlendState,const FLOAT BlendFactor[4],UINT SampleMask) {
    This->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask);
}
static inline void ID3D11DeviceContext4_OMSetDepthStencilState(ID3D11DeviceContext4* This,ID3D11DepthStencilState *pDepthStencilState,UINT StencilRef) {
    This->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef);
}
static inline void ID3D11DeviceContext4_SOSetTargets(ID3D11DeviceContext4* This,UINT NumBuffers,ID3D11Buffer *const *ppSOTargets,const UINT *pOffsets) {
    This->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets);
}
static inline void ID3D11DeviceContext4_DrawAuto(ID3D11DeviceContext4* This) {
    This->lpVtbl->DrawAuto(This);
}
static inline void ID3D11DeviceContext4_DrawIndexedInstancedIndirect(ID3D11DeviceContext4* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext4_DrawInstancedIndirect(ID3D11DeviceContext4* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext4_Dispatch(ID3D11DeviceContext4* This,UINT ThreadGroupCountX,UINT ThreadGroupCountY,UINT ThreadGroupCountZ) {
    This->lpVtbl->Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ);
}
static inline void ID3D11DeviceContext4_DispatchIndirect(ID3D11DeviceContext4* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext4_RSSetState(ID3D11DeviceContext4* This,ID3D11RasterizerState *pRasterizerState) {
    This->lpVtbl->RSSetState(This,pRasterizerState);
}
static inline void ID3D11DeviceContext4_RSSetViewports(ID3D11DeviceContext4* This,UINT NumViewports,const D3D11_VIEWPORT *pViewports) {
    This->lpVtbl->RSSetViewports(This,NumViewports,pViewports);
}
static inline void ID3D11DeviceContext4_RSSetScissorRects(ID3D11DeviceContext4* This,UINT NumRects,const D3D11_RECT *pRects) {
    This->lpVtbl->RSSetScissorRects(This,NumRects,pRects);
}
static inline void ID3D11DeviceContext4_CopySubresourceRegion(ID3D11DeviceContext4* This,ID3D11Resource *pDstResource,UINT DstSubresource,UINT DstX,UINT DstY,UINT DstZ,ID3D11Resource *pSrcResource,UINT SrcSubresource,const D3D11_BOX *pSrcBox) {
    This->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox);
}
static inline void ID3D11DeviceContext4_CopyResource(ID3D11DeviceContext4* This,ID3D11Resource *pDstResource,ID3D11Resource *pSrcResource) {
    This->lpVtbl->CopyResource(This,pDstResource,pSrcResource);
}
static inline void ID3D11DeviceContext4_UpdateSubresource(ID3D11DeviceContext4* This,ID3D11Resource *pDstResource,UINT DstSubresource,const D3D11_BOX *pDstBox,const void *pSrcData,UINT SrcRowPitch,UINT SrcDepthPitch) {
    This->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch);
}
static inline void ID3D11DeviceContext4_CopyStructureCount(ID3D11DeviceContext4* This,ID3D11Buffer *pDstBuffer,UINT DstAlignedByteOffset,ID3D11UnorderedAccessView *pSrcView) {
    This->lpVtbl->CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView);
}
static inline void ID3D11DeviceContext4_ClearRenderTargetView(ID3D11DeviceContext4* This,ID3D11RenderTargetView *pRenderTargetView,const FLOAT ColorRGBA[4]) {
    This->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA);
}
static inline void ID3D11DeviceContext4_ClearUnorderedAccessViewUint(ID3D11DeviceContext4* This,ID3D11UnorderedAccessView *pUnorderedAccessView,const UINT Values[4]) {
    This->lpVtbl->ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values);
}
static inline void ID3D11DeviceContext4_ClearUnorderedAccessViewFloat(ID3D11DeviceContext4* This,ID3D11UnorderedAccessView *pUnorderedAccessView,const FLOAT Values[4]) {
    This->lpVtbl->ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values);
}
static inline void ID3D11DeviceContext4_ClearDepthStencilView(ID3D11DeviceContext4* This,ID3D11DepthStencilView *pDepthStencilView,UINT ClearFlags,FLOAT Depth,UINT8 Stencil) {
    This->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil);
}
static inline void ID3D11DeviceContext4_GenerateMips(ID3D11DeviceContext4* This,ID3D11ShaderResourceView *pShaderResourceView) {
    This->lpVtbl->GenerateMips(This,pShaderResourceView);
}
static inline void ID3D11DeviceContext4_SetResourceMinLOD(ID3D11DeviceContext4* This,ID3D11Resource *pResource,FLOAT MinLOD) {
    This->lpVtbl->SetResourceMinLOD(This,pResource,MinLOD);
}
static inline FLOAT ID3D11DeviceContext4_GetResourceMinLOD(ID3D11DeviceContext4* This,ID3D11Resource *pResource) {
    return This->lpVtbl->GetResourceMinLOD(This,pResource);
}
static inline void ID3D11DeviceContext4_ResolveSubresource(ID3D11DeviceContext4* This,ID3D11Resource *pDstResource,UINT DstSubresource,ID3D11Resource *pSrcResource,UINT SrcSubresource,DXGI_FORMAT Format) {
    This->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format);
}
static inline void ID3D11DeviceContext4_ExecuteCommandList(ID3D11DeviceContext4* This,ID3D11CommandList *pCommandList,WINBOOL RestoreContextState) {
    This->lpVtbl->ExecuteCommandList(This,pCommandList,RestoreContextState);
}
static inline void ID3D11DeviceContext4_HSSetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_HSSetShader(ID3D11DeviceContext4* This,ID3D11HullShader *pHullShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext4_HSSetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_HSSetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_DSSetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_DSSetShader(ID3D11DeviceContext4* This,ID3D11DomainShader *pDomainShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext4_DSSetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_DSSetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_CSSetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_CSSetUnorderedAccessViews(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumUAVs,ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,const UINT *pUAVInitialCounts) {
    This->lpVtbl->CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts);
}
static inline void ID3D11DeviceContext4_CSSetShader(ID3D11DeviceContext4* This,ID3D11ComputeShader *pComputeShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext4_CSSetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_CSSetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_VSGetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_PSGetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_PSGetShader(ID3D11DeviceContext4* This,ID3D11PixelShader **ppPixelShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext4_PSGetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_VSGetShader(ID3D11DeviceContext4* This,ID3D11VertexShader **ppVertexShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext4_PSGetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_IAGetInputLayout(ID3D11DeviceContext4* This,ID3D11InputLayout **ppInputLayout) {
    This->lpVtbl->IAGetInputLayout(This,ppInputLayout);
}
static inline void ID3D11DeviceContext4_IAGetVertexBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppVertexBuffers,UINT *pStrides,UINT *pOffsets) {
    This->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static inline void ID3D11DeviceContext4_IAGetIndexBuffer(ID3D11DeviceContext4* This,ID3D11Buffer **pIndexBuffer,DXGI_FORMAT *Format,UINT *Offset) {
    This->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static inline void ID3D11DeviceContext4_GSGetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_GSGetShader(ID3D11DeviceContext4* This,ID3D11GeometryShader **ppGeometryShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext4_IAGetPrimitiveTopology(ID3D11DeviceContext4* This,D3D11_PRIMITIVE_TOPOLOGY *pTopology) {
    This->lpVtbl->IAGetPrimitiveTopology(This,pTopology);
}
static inline void ID3D11DeviceContext4_VSGetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_VSGetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_GetPredication(ID3D11DeviceContext4* This,ID3D11Predicate **ppPredicate,WINBOOL *pPredicateValue) {
    This->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue);
}
static inline void ID3D11DeviceContext4_GSGetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_GSGetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_OMGetRenderTargets(ID3D11DeviceContext4* This,UINT NumViews,ID3D11RenderTargetView **ppRenderTargetViews,ID3D11DepthStencilView **ppDepthStencilView) {
    This->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView);
}
static inline void ID3D11DeviceContext4_OMGetRenderTargetsAndUnorderedAccessViews(ID3D11DeviceContext4* This,UINT NumRTVs,ID3D11RenderTargetView **ppRenderTargetViews,ID3D11DepthStencilView **ppDepthStencilView,UINT UAVStartSlot,UINT NumUAVs,ID3D11UnorderedAccessView **ppUnorderedAccessViews) {
    This->lpVtbl->OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews);
}
static inline void ID3D11DeviceContext4_OMGetBlendState(ID3D11DeviceContext4* This,ID3D11BlendState **ppBlendState,FLOAT BlendFactor[4],UINT *pSampleMask) {
    This->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask);
}
static inline void ID3D11DeviceContext4_OMGetDepthStencilState(ID3D11DeviceContext4* This,ID3D11DepthStencilState **ppDepthStencilState,UINT *pStencilRef) {
    This->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef);
}
static inline void ID3D11DeviceContext4_SOGetTargets(ID3D11DeviceContext4* This,UINT NumBuffers,ID3D11Buffer **ppSOTargets) {
    This->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets);
}
static inline void ID3D11DeviceContext4_RSGetState(ID3D11DeviceContext4* This,ID3D11RasterizerState **ppRasterizerState) {
    This->lpVtbl->RSGetState(This,ppRasterizerState);
}
static inline void ID3D11DeviceContext4_RSGetViewports(ID3D11DeviceContext4* This,UINT *pNumViewports,D3D11_VIEWPORT *pViewports) {
    This->lpVtbl->RSGetViewports(This,pNumViewports,pViewports);
}
static inline void ID3D11DeviceContext4_RSGetScissorRects(ID3D11DeviceContext4* This,UINT *pNumRects,D3D11_RECT *pRects) {
    This->lpVtbl->RSGetScissorRects(This,pNumRects,pRects);
}
static inline void ID3D11DeviceContext4_HSGetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_HSGetShader(ID3D11DeviceContext4* This,ID3D11HullShader **ppHullShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext4_HSGetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_HSGetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_DSGetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_DSGetShader(ID3D11DeviceContext4* This,ID3D11DomainShader **ppDomainShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext4_DSGetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_DSGetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_CSGetShaderResources(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext4_CSGetUnorderedAccessViews(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumUAVs,ID3D11UnorderedAccessView **ppUnorderedAccessViews) {
    This->lpVtbl->CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews);
}
static inline void ID3D11DeviceContext4_CSGetShader(ID3D11DeviceContext4* This,ID3D11ComputeShader **ppComputeShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext4_CSGetSamplers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext4_CSGetConstantBuffers(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext4_ClearState(ID3D11DeviceContext4* This) {
    This->lpVtbl->ClearState(This);
}
static inline void ID3D11DeviceContext4_Flush(ID3D11DeviceContext4* This) {
    This->lpVtbl->Flush(This);
}
static inline D3D11_DEVICE_CONTEXT_TYPE ID3D11DeviceContext4_GetType(ID3D11DeviceContext4* This) {
    return This->lpVtbl->GetType(This);
}
static inline UINT ID3D11DeviceContext4_GetContextFlags(ID3D11DeviceContext4* This) {
    return This->lpVtbl->GetContextFlags(This);
}
static inline HRESULT ID3D11DeviceContext4_FinishCommandList(ID3D11DeviceContext4* This,WINBOOL RestoreDeferredContextState,ID3D11CommandList **ppCommandList) {
    return This->lpVtbl->FinishCommandList(This,RestoreDeferredContextState,ppCommandList);
}
/*** ID3D11DeviceContext1 methods ***/
static inline void ID3D11DeviceContext4_CopySubresourceRegion1(ID3D11DeviceContext4* This,ID3D11Resource *pDstResource,UINT DstSubresource,UINT DstX,UINT DstY,UINT DstZ,ID3D11Resource *pSrcResource,UINT SrcSubresource,const D3D11_BOX *pSrcBox,UINT CopyFlags) {
    This->lpVtbl->CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags);
}
static inline void ID3D11DeviceContext4_UpdateSubresource1(ID3D11DeviceContext4* This,ID3D11Resource *pDstResource,UINT DstSubresource,const D3D11_BOX *pDstBox,const void *pSrcData,UINT SrcRowPitch,UINT SrcDepthPitch,UINT CopyFlags) {
    This->lpVtbl->UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags);
}
static inline void ID3D11DeviceContext4_DiscardResource(ID3D11DeviceContext4* This,ID3D11Resource *pResource) {
    This->lpVtbl->DiscardResource(This,pResource);
}
static inline void ID3D11DeviceContext4_DiscardView(ID3D11DeviceContext4* This,ID3D11View *pResourceView) {
    This->lpVtbl->DiscardView(This,pResourceView);
}
static inline void ID3D11DeviceContext4_VSSetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_HSSetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_DSSetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_GSSetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_PSSetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_CSSetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_VSGetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_HSGetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_DSGetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_GSGetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_PSGetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_CSGetConstantBuffers1(ID3D11DeviceContext4* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext4_SwapDeviceContextState(ID3D11DeviceContext4* This,ID3DDeviceContextState *pState,ID3DDeviceContextState **ppPreviousState) {
    This->lpVtbl->SwapDeviceContextState(This,pState,ppPreviousState);
}
static inline void ID3D11DeviceContext4_ClearView(ID3D11DeviceContext4* This,ID3D11View *pView,const FLOAT Color[4],const D3D11_RECT *pRect,UINT NumRects) {
    This->lpVtbl->ClearView(This,pView,Color,pRect,NumRects);
}
static inline void ID3D11DeviceContext4_DiscardView1(ID3D11DeviceContext4* This,ID3D11View *pResourceView,const D3D11_RECT *pRects,UINT NumRects) {
    This->lpVtbl->DiscardView1(This,pResourceView,pRects,NumRects);
}
/*** ID3D11DeviceContext2 methods ***/
static inline HRESULT ID3D11DeviceContext4_UpdateTileMappings(ID3D11DeviceContext4* This,ID3D11Resource *resource,UINT region_count,const D3D11_TILED_RESOURCE_COORDINATE *region_start_coordinates,const D3D11_TILE_REGION_SIZE *region_sizes,ID3D11Buffer *pool,UINT range_count,const UINT *range_flags,const UINT *pool_start_offsets,const UINT *range_tile_counts,UINT flags) {
    return This->lpVtbl->UpdateTileMappings(This,resource,region_count,region_start_coordinates,region_sizes,pool,range_count,range_flags,pool_start_offsets,range_tile_counts,flags);
}
static inline HRESULT ID3D11DeviceContext4_CopyTileMappings(ID3D11DeviceContext4* This,ID3D11Resource *dst_resource,const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,ID3D11Resource *src_resource,const D3D11_TILED_RESOURCE_COORDINATE *src_start_coordinate,const D3D11_TILE_REGION_SIZE *region_size,UINT flags) {
    return This->lpVtbl->CopyTileMappings(This,dst_resource,dst_start_coordinate,src_resource,src_start_coordinate,region_size,flags);
}
static inline void ID3D11DeviceContext4_CopyTiles(ID3D11DeviceContext4* This,ID3D11Resource *resource,const D3D11_TILED_RESOURCE_COORDINATE *start_coordinate,const D3D11_TILE_REGION_SIZE *size,ID3D11Buffer *buffer,UINT64 start_offset,UINT flags) {
    This->lpVtbl->CopyTiles(This,resource,start_coordinate,size,buffer,start_offset,flags);
}
static inline void ID3D11DeviceContext4_UpdateTiles(ID3D11DeviceContext4* This,ID3D11Resource *dst_resource,const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,const D3D11_TILE_REGION_SIZE *dst_region_size,const void *src_data,UINT flags) {
    This->lpVtbl->UpdateTiles(This,dst_resource,dst_start_coordinate,dst_region_size,src_data,flags);
}
static inline HRESULT ID3D11DeviceContext4_ResizeTilePool(ID3D11DeviceContext4* This,ID3D11Buffer *pool,UINT64 size) {
    return This->lpVtbl->ResizeTilePool(This,pool,size);
}
static inline void ID3D11DeviceContext4_TiledResourceBarrier(ID3D11DeviceContext4* This,ID3D11DeviceChild *before_barrier,ID3D11DeviceChild *after_barrier) {
    This->lpVtbl->TiledResourceBarrier(This,before_barrier,after_barrier);
}
static inline WINBOOL ID3D11DeviceContext4_IsAnnotationEnabled(ID3D11DeviceContext4* This) {
    return This->lpVtbl->IsAnnotationEnabled(This);
}
static inline void ID3D11DeviceContext4_SetMarkerInt(ID3D11DeviceContext4* This,const WCHAR *label,int data) {
    This->lpVtbl->SetMarkerInt(This,label,data);
}
static inline void ID3D11DeviceContext4_BeginEventInt(ID3D11DeviceContext4* This,const WCHAR *label,int data) {
    This->lpVtbl->BeginEventInt(This,label,data);
}
static inline void ID3D11DeviceContext4_EndEvent(ID3D11DeviceContext4* This) {
    This->lpVtbl->EndEvent(This);
}
/*** ID3D11DeviceContext3 methods ***/
static inline void ID3D11DeviceContext4_Flush1(ID3D11DeviceContext4* This,D3D11_CONTEXT_TYPE type,HANDLE event) {
    This->lpVtbl->Flush1(This,type,event);
}
static inline void ID3D11DeviceContext4_SetHardwareProtectionState(ID3D11DeviceContext4* This,WINBOOL enable) {
    This->lpVtbl->SetHardwareProtectionState(This,enable);
}
static inline void ID3D11DeviceContext4_GetHardwareProtectionState(ID3D11DeviceContext4* This,WINBOOL *enable) {
    This->lpVtbl->GetHardwareProtectionState(This,enable);
}
/*** ID3D11DeviceContext4 methods ***/
static inline HRESULT ID3D11DeviceContext4_Signal(ID3D11DeviceContext4* This,ID3D11Fence *fence,UINT64 value) {
    return This->lpVtbl->Signal(This,fence,value);
}
static inline HRESULT ID3D11DeviceContext4_Wait(ID3D11DeviceContext4* This,ID3D11Fence *fence,UINT64 value) {
    return This->lpVtbl->Wait(This,fence,value);
}
#endif
#endif

#endif


#endif  /* __ID3D11DeviceContext4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Device3 interface
 */
#ifndef __ID3D11Device3_INTERFACE_DEFINED__
#define __ID3D11Device3_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Device3, 0xa05c8c37, 0xd2c6, 0x4732, 0xb3,0xa0, 0x9c,0xe0,0xb0,0xdc,0x9a,0xe6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a05c8c37-d2c6-4732-b3a0-9ce0b0dc9ae6")
ID3D11Device3 : public ID3D11Device2
{
    virtual HRESULT STDMETHODCALLTYPE CreateTexture2D1(
        const D3D11_TEXTURE2D_DESC1 *desc,
        const D3D11_SUBRESOURCE_DATA *initial_data,
        ID3D11Texture2D1 **texture) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateTexture3D1(
        const D3D11_TEXTURE3D_DESC1 *desc,
        const D3D11_SUBRESOURCE_DATA *initial_data,
        ID3D11Texture3D1 **texture) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRasterizerState2(
        const D3D11_RASTERIZER_DESC2 *desc,
        ID3D11RasterizerState2 **state) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateShaderResourceView1(
        ID3D11Resource *resource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc,
        ID3D11ShaderResourceView1 **view) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateUnorderedAccessView1(
        ID3D11Resource *resource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc,
        ID3D11UnorderedAccessView1 **view) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRenderTargetView1(
        ID3D11Resource *resource,
        const D3D11_RENDER_TARGET_VIEW_DESC1 *desc,
        ID3D11RenderTargetView1 **view) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateQuery1(
        const D3D11_QUERY_DESC1 *desc,
        ID3D11Query1 **query) = 0;

    virtual void STDMETHODCALLTYPE GetImmediateContext3(
        ID3D11DeviceContext3 **context) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDeferredContext3(
        UINT flags,
        ID3D11DeviceContext3 **context) = 0;

    virtual void STDMETHODCALLTYPE WriteToSubresource(
        ID3D11Resource *dst_resource,
        UINT dst_subresource,
        const D3D11_BOX *dst_box,
        const void *src_data,
        UINT src_row_pitch,
        UINT src_depth_pitch) = 0;

    virtual void STDMETHODCALLTYPE ReadFromSubresource(
        void *dst_data,
        UINT dst_row_pitch,
        UINT dst_depth_pitch,
        ID3D11Resource *src_resource,
        UINT src_subresource,
        const D3D11_BOX *src_box) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Device3, 0xa05c8c37, 0xd2c6, 0x4732, 0xb3,0xa0, 0x9c,0xe0,0xb0,0xdc,0x9a,0xe6)
#endif
#else
typedef struct ID3D11Device3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Device3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Device3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Device3 *This);

    /*** ID3D11Device methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateBuffer)(
        ID3D11Device3 *This,
        const D3D11_BUFFER_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Buffer **ppBuffer);

    HRESULT (STDMETHODCALLTYPE *CreateTexture1D)(
        ID3D11Device3 *This,
        const D3D11_TEXTURE1D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture1D **ppTexture1D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture2D)(
        ID3D11Device3 *This,
        const D3D11_TEXTURE2D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture2D **ppTexture2D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture3D)(
        ID3D11Device3 *This,
        const D3D11_TEXTURE3D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture3D **ppTexture3D);

    HRESULT (STDMETHODCALLTYPE *CreateShaderResourceView)(
        ID3D11Device3 *This,
        ID3D11Resource *pResource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,
        ID3D11ShaderResourceView **ppSRView);

    HRESULT (STDMETHODCALLTYPE *CreateUnorderedAccessView)(
        ID3D11Device3 *This,
        ID3D11Resource *pResource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,
        ID3D11UnorderedAccessView **ppUAView);

    HRESULT (STDMETHODCALLTYPE *CreateRenderTargetView)(
        ID3D11Device3 *This,
        ID3D11Resource *pResource,
        const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,
        ID3D11RenderTargetView **ppRTView);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilView)(
        ID3D11Device3 *This,
        ID3D11Resource *pResource,
        const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,
        ID3D11DepthStencilView **ppDepthStencilView);

    HRESULT (STDMETHODCALLTYPE *CreateInputLayout)(
        ID3D11Device3 *This,
        const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,
        UINT NumElements,
        const void *pShaderBytecodeWithInputSignature,
        SIZE_T BytecodeLength,
        ID3D11InputLayout **ppInputLayout);

    HRESULT (STDMETHODCALLTYPE *CreateVertexShader)(
        ID3D11Device3 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11VertexShader **ppVertexShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShader)(
        ID3D11Device3 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShaderWithStreamOutput)(
        ID3D11Device3 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,
        UINT NumEntries,
        const UINT *pBufferStrides,
        UINT NumStrides,
        UINT RasterizedStream,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreatePixelShader)(
        ID3D11Device3 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11PixelShader **ppPixelShader);

    HRESULT (STDMETHODCALLTYPE *CreateHullShader)(
        ID3D11Device3 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11HullShader **ppHullShader);

    HRESULT (STDMETHODCALLTYPE *CreateDomainShader)(
        ID3D11Device3 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11DomainShader **ppDomainShader);

    HRESULT (STDMETHODCALLTYPE *CreateComputeShader)(
        ID3D11Device3 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11ComputeShader **ppComputeShader);

    HRESULT (STDMETHODCALLTYPE *CreateClassLinkage)(
        ID3D11Device3 *This,
        ID3D11ClassLinkage **ppLinkage);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState)(
        ID3D11Device3 *This,
        const D3D11_BLEND_DESC *pBlendStateDesc,
        ID3D11BlendState **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilState)(
        ID3D11Device3 *This,
        const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,
        ID3D11DepthStencilState **ppDepthStencilState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState)(
        ID3D11Device3 *This,
        const D3D11_RASTERIZER_DESC *pRasterizerDesc,
        ID3D11RasterizerState **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateSamplerState)(
        ID3D11Device3 *This,
        const D3D11_SAMPLER_DESC *pSamplerDesc,
        ID3D11SamplerState **ppSamplerState);

    HRESULT (STDMETHODCALLTYPE *CreateQuery)(
        ID3D11Device3 *This,
        const D3D11_QUERY_DESC *pQueryDesc,
        ID3D11Query **ppQuery);

    HRESULT (STDMETHODCALLTYPE *CreatePredicate)(
        ID3D11Device3 *This,
        const D3D11_QUERY_DESC *pPredicateDesc,
        ID3D11Predicate **ppPredicate);

    HRESULT (STDMETHODCALLTYPE *CreateCounter)(
        ID3D11Device3 *This,
        const D3D11_COUNTER_DESC *pCounterDesc,
        ID3D11Counter **ppCounter);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext)(
        ID3D11Device3 *This,
        UINT ContextFlags,
        ID3D11DeviceContext **ppDeferredContext);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource)(
        ID3D11Device3 *This,
        HANDLE hResource,
        REFIID ReturnedInterface,
        void **ppResource);

    HRESULT (STDMETHODCALLTYPE *CheckFormatSupport)(
        ID3D11Device3 *This,
        DXGI_FORMAT Format,
        UINT *pFormatSupport);

    HRESULT (STDMETHODCALLTYPE *CheckMultisampleQualityLevels)(
        ID3D11Device3 *This,
        DXGI_FORMAT Format,
        UINT SampleCount,
        UINT *pNumQualityLevels);

    void (STDMETHODCALLTYPE *CheckCounterInfo)(
        ID3D11Device3 *This,
        D3D11_COUNTER_INFO *pCounterInfo);

    HRESULT (STDMETHODCALLTYPE *CheckCounter)(
        ID3D11Device3 *This,
        const D3D11_COUNTER_DESC *pDesc,
        D3D11_COUNTER_TYPE *pType,
        UINT *pActiveCounters,
        LPSTR szName,
        UINT *pNameLength,
        LPSTR szUnits,
        UINT *pUnitsLength,
        LPSTR szDescription,
        UINT *pDescriptionLength);

    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        ID3D11Device3 *This,
        D3D11_FEATURE Feature,
        void *pFeatureSupportData,
        UINT FeatureSupportDataSize);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Device3 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Device3 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Device3 *This,
        REFGUID guid,
        const IUnknown *pData);

    D3D_FEATURE_LEVEL (STDMETHODCALLTYPE *GetFeatureLevel)(
        ID3D11Device3 *This);

    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        ID3D11Device3 *This);

    HRESULT (STDMETHODCALLTYPE *GetDeviceRemovedReason)(
        ID3D11Device3 *This);

    void (STDMETHODCALLTYPE *GetImmediateContext)(
        ID3D11Device3 *This,
        ID3D11DeviceContext **ppImmediateContext);

    HRESULT (STDMETHODCALLTYPE *SetExceptionMode)(
        ID3D11Device3 *This,
        UINT RaiseFlags);

    UINT (STDMETHODCALLTYPE *GetExceptionMode)(
        ID3D11Device3 *This);

    /*** ID3D11Device1 methods ***/
    void (STDMETHODCALLTYPE *GetImmediateContext1)(
        ID3D11Device3 *This,
        ID3D11DeviceContext1 **ppImmediateContext);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext1)(
        ID3D11Device3 *This,
        UINT ContextFlags,
        ID3D11DeviceContext1 **ppDeferredContext);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState1)(
        ID3D11Device3 *This,
        const D3D11_BLEND_DESC1 *pBlendStateDesc,
        ID3D11BlendState1 **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState1)(
        ID3D11Device3 *This,
        const D3D11_RASTERIZER_DESC1 *pRasterizerDesc,
        ID3D11RasterizerState1 **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateDeviceContextState)(
        ID3D11Device3 *This,
        UINT Flags,
        const D3D_FEATURE_LEVEL *pFeatureLevels,
        UINT FeatureLevels,
        UINT SDKVersion,
        REFIID EmulatedInterface,
        D3D_FEATURE_LEVEL *pChosenFeatureLevel,
        ID3DDeviceContextState **ppContextState);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource1)(
        ID3D11Device3 *This,
        HANDLE hResource,
        REFIID returnedInterface,
        void **ppResource);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResourceByName)(
        ID3D11Device3 *This,
        LPCWSTR lpName,
        DWORD dwDesiredAccess,
        REFIID returnedInterface,
        void **ppResource);

    /*** ID3D11Device2 methods ***/
    void (STDMETHODCALLTYPE *GetImmediateContext2)(
        ID3D11Device3 *This,
        ID3D11DeviceContext2 **context);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext2)(
        ID3D11Device3 *This,
        UINT flags,
        ID3D11DeviceContext2 **context);

    void (STDMETHODCALLTYPE *GetResourceTiling)(
        ID3D11Device3 *This,
        ID3D11Resource *resource,
        UINT *tile_count,
        D3D11_PACKED_MIP_DESC *mip_desc,
        D3D11_TILE_SHAPE *tile_shape,
        UINT *subresource_tiling_count,
        UINT first_subresource_tiling,
        D3D11_SUBRESOURCE_TILING *subresource_tiling);

    HRESULT (STDMETHODCALLTYPE *CheckMultisampleQualityLevels1)(
        ID3D11Device3 *This,
        DXGI_FORMAT format,
        UINT sample_count,
        UINT flags,
        UINT *quality_level_count);

    /*** ID3D11Device3 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateTexture2D1)(
        ID3D11Device3 *This,
        const D3D11_TEXTURE2D_DESC1 *desc,
        const D3D11_SUBRESOURCE_DATA *initial_data,
        ID3D11Texture2D1 **texture);

    HRESULT (STDMETHODCALLTYPE *CreateTexture3D1)(
        ID3D11Device3 *This,
        const D3D11_TEXTURE3D_DESC1 *desc,
        const D3D11_SUBRESOURCE_DATA *initial_data,
        ID3D11Texture3D1 **texture);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState2)(
        ID3D11Device3 *This,
        const D3D11_RASTERIZER_DESC2 *desc,
        ID3D11RasterizerState2 **state);

    HRESULT (STDMETHODCALLTYPE *CreateShaderResourceView1)(
        ID3D11Device3 *This,
        ID3D11Resource *resource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc,
        ID3D11ShaderResourceView1 **view);

    HRESULT (STDMETHODCALLTYPE *CreateUnorderedAccessView1)(
        ID3D11Device3 *This,
        ID3D11Resource *resource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc,
        ID3D11UnorderedAccessView1 **view);

    HRESULT (STDMETHODCALLTYPE *CreateRenderTargetView1)(
        ID3D11Device3 *This,
        ID3D11Resource *resource,
        const D3D11_RENDER_TARGET_VIEW_DESC1 *desc,
        ID3D11RenderTargetView1 **view);

    HRESULT (STDMETHODCALLTYPE *CreateQuery1)(
        ID3D11Device3 *This,
        const D3D11_QUERY_DESC1 *desc,
        ID3D11Query1 **query);

    void (STDMETHODCALLTYPE *GetImmediateContext3)(
        ID3D11Device3 *This,
        ID3D11DeviceContext3 **context);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext3)(
        ID3D11Device3 *This,
        UINT flags,
        ID3D11DeviceContext3 **context);

    void (STDMETHODCALLTYPE *WriteToSubresource)(
        ID3D11Device3 *This,
        ID3D11Resource *dst_resource,
        UINT dst_subresource,
        const D3D11_BOX *dst_box,
        const void *src_data,
        UINT src_row_pitch,
        UINT src_depth_pitch);

    void (STDMETHODCALLTYPE *ReadFromSubresource)(
        ID3D11Device3 *This,
        void *dst_data,
        UINT dst_row_pitch,
        UINT dst_depth_pitch,
        ID3D11Resource *src_resource,
        UINT src_subresource,
        const D3D11_BOX *src_box);

    END_INTERFACE
} ID3D11Device3Vtbl;

interface ID3D11Device3 {
    CONST_VTBL ID3D11Device3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Device3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Device3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Device3_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11Device methods ***/
#define ID3D11Device3_CreateBuffer(This,pDesc,pInitialData,ppBuffer) (This)->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer)
#define ID3D11Device3_CreateTexture1D(This,pDesc,pInitialData,ppTexture1D) (This)->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D)
#define ID3D11Device3_CreateTexture2D(This,pDesc,pInitialData,ppTexture2D) (This)->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D)
#define ID3D11Device3_CreateTexture3D(This,pDesc,pInitialData,ppTexture3D) (This)->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D)
#define ID3D11Device3_CreateShaderResourceView(This,pResource,pDesc,ppSRView) (This)->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView)
#define ID3D11Device3_CreateUnorderedAccessView(This,pResource,pDesc,ppUAView) (This)->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView)
#define ID3D11Device3_CreateRenderTargetView(This,pResource,pDesc,ppRTView) (This)->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView)
#define ID3D11Device3_CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView) (This)->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView)
#define ID3D11Device3_CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout) (This)->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout)
#define ID3D11Device3_CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader) (This)->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader)
#define ID3D11Device3_CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader)
#define ID3D11Device3_CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader)
#define ID3D11Device3_CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader) (This)->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader)
#define ID3D11Device3_CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader) (This)->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader)
#define ID3D11Device3_CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader) (This)->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader)
#define ID3D11Device3_CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader) (This)->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader)
#define ID3D11Device3_CreateClassLinkage(This,ppLinkage) (This)->lpVtbl->CreateClassLinkage(This,ppLinkage)
#define ID3D11Device3_CreateBlendState(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState)
#define ID3D11Device3_CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState) (This)->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState)
#define ID3D11Device3_CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState)
#define ID3D11Device3_CreateSamplerState(This,pSamplerDesc,ppSamplerState) (This)->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState)
#define ID3D11Device3_CreateQuery(This,pQueryDesc,ppQuery) (This)->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery)
#define ID3D11Device3_CreatePredicate(This,pPredicateDesc,ppPredicate) (This)->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate)
#define ID3D11Device3_CreateCounter(This,pCounterDesc,ppCounter) (This)->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter)
#define ID3D11Device3_CreateDeferredContext(This,ContextFlags,ppDeferredContext) (This)->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext)
#define ID3D11Device3_OpenSharedResource(This,hResource,ReturnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource)
#define ID3D11Device3_CheckFormatSupport(This,Format,pFormatSupport) (This)->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport)
#define ID3D11Device3_CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels) (This)->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels)
#define ID3D11Device3_CheckCounterInfo(This,pCounterInfo) (This)->lpVtbl->CheckCounterInfo(This,pCounterInfo)
#define ID3D11Device3_CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength) (This)->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength)
#define ID3D11Device3_CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize) (This)->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize)
#define ID3D11Device3_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Device3_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Device3_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#define ID3D11Device3_GetFeatureLevel(This) (This)->lpVtbl->GetFeatureLevel(This)
#define ID3D11Device3_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
#define ID3D11Device3_GetDeviceRemovedReason(This) (This)->lpVtbl->GetDeviceRemovedReason(This)
#define ID3D11Device3_GetImmediateContext(This,ppImmediateContext) (This)->lpVtbl->GetImmediateContext(This,ppImmediateContext)
#define ID3D11Device3_SetExceptionMode(This,RaiseFlags) (This)->lpVtbl->SetExceptionMode(This,RaiseFlags)
#define ID3D11Device3_GetExceptionMode(This) (This)->lpVtbl->GetExceptionMode(This)
/*** ID3D11Device1 methods ***/
#define ID3D11Device3_GetImmediateContext1(This,ppImmediateContext) (This)->lpVtbl->GetImmediateContext1(This,ppImmediateContext)
#define ID3D11Device3_CreateDeferredContext1(This,ContextFlags,ppDeferredContext) (This)->lpVtbl->CreateDeferredContext1(This,ContextFlags,ppDeferredContext)
#define ID3D11Device3_CreateBlendState1(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState)
#define ID3D11Device3_CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState)
#define ID3D11Device3_CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState) (This)->lpVtbl->CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState)
#define ID3D11Device3_OpenSharedResource1(This,hResource,returnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource1(This,hResource,returnedInterface,ppResource)
#define ID3D11Device3_OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource) (This)->lpVtbl->OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource)
/*** ID3D11Device2 methods ***/
#define ID3D11Device3_GetImmediateContext2(This,context) (This)->lpVtbl->GetImmediateContext2(This,context)
#define ID3D11Device3_CreateDeferredContext2(This,flags,context) (This)->lpVtbl->CreateDeferredContext2(This,flags,context)
#define ID3D11Device3_GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling) (This)->lpVtbl->GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling)
#define ID3D11Device3_CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count) (This)->lpVtbl->CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count)
/*** ID3D11Device3 methods ***/
#define ID3D11Device3_CreateTexture2D1(This,desc,initial_data,texture) (This)->lpVtbl->CreateTexture2D1(This,desc,initial_data,texture)
#define ID3D11Device3_CreateTexture3D1(This,desc,initial_data,texture) (This)->lpVtbl->CreateTexture3D1(This,desc,initial_data,texture)
#define ID3D11Device3_CreateRasterizerState2(This,desc,state) (This)->lpVtbl->CreateRasterizerState2(This,desc,state)
#define ID3D11Device3_CreateShaderResourceView1(This,resource,desc,view) (This)->lpVtbl->CreateShaderResourceView1(This,resource,desc,view)
#define ID3D11Device3_CreateUnorderedAccessView1(This,resource,desc,view) (This)->lpVtbl->CreateUnorderedAccessView1(This,resource,desc,view)
#define ID3D11Device3_CreateRenderTargetView1(This,resource,desc,view) (This)->lpVtbl->CreateRenderTargetView1(This,resource,desc,view)
#define ID3D11Device3_CreateQuery1(This,desc,query) (This)->lpVtbl->CreateQuery1(This,desc,query)
#define ID3D11Device3_GetImmediateContext3(This,context) (This)->lpVtbl->GetImmediateContext3(This,context)
#define ID3D11Device3_CreateDeferredContext3(This,flags,context) (This)->lpVtbl->CreateDeferredContext3(This,flags,context)
#define ID3D11Device3_WriteToSubresource(This,dst_resource,dst_subresource,dst_box,src_data,src_row_pitch,src_depth_pitch) (This)->lpVtbl->WriteToSubresource(This,dst_resource,dst_subresource,dst_box,src_data,src_row_pitch,src_depth_pitch)
#define ID3D11Device3_ReadFromSubresource(This,dst_data,dst_row_pitch,dst_depth_pitch,src_resource,src_subresource,src_box) (This)->lpVtbl->ReadFromSubresource(This,dst_data,dst_row_pitch,dst_depth_pitch,src_resource,src_subresource,src_box)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11Device3_QueryInterface(ID3D11Device3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11Device3_AddRef(ID3D11Device3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11Device3_Release(ID3D11Device3* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11Device methods ***/
static inline HRESULT ID3D11Device3_CreateBuffer(ID3D11Device3* This,const D3D11_BUFFER_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Buffer **ppBuffer) {
    return This->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer);
}
static inline HRESULT ID3D11Device3_CreateTexture1D(ID3D11Device3* This,const D3D11_TEXTURE1D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture1D **ppTexture1D) {
    return This->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D);
}
static inline HRESULT ID3D11Device3_CreateTexture2D(ID3D11Device3* This,const D3D11_TEXTURE2D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture2D **ppTexture2D) {
    return This->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D);
}
static inline HRESULT ID3D11Device3_CreateTexture3D(ID3D11Device3* This,const D3D11_TEXTURE3D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture3D **ppTexture3D) {
    return This->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D);
}
static inline HRESULT ID3D11Device3_CreateShaderResourceView(ID3D11Device3* This,ID3D11Resource *pResource,const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,ID3D11ShaderResourceView **ppSRView) {
    return This->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView);
}
static inline HRESULT ID3D11Device3_CreateUnorderedAccessView(ID3D11Device3* This,ID3D11Resource *pResource,const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,ID3D11UnorderedAccessView **ppUAView) {
    return This->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView);
}
static inline HRESULT ID3D11Device3_CreateRenderTargetView(ID3D11Device3* This,ID3D11Resource *pResource,const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,ID3D11RenderTargetView **ppRTView) {
    return This->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView);
}
static inline HRESULT ID3D11Device3_CreateDepthStencilView(ID3D11Device3* This,ID3D11Resource *pResource,const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,ID3D11DepthStencilView **ppDepthStencilView) {
    return This->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView);
}
static inline HRESULT ID3D11Device3_CreateInputLayout(ID3D11Device3* This,const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,UINT NumElements,const void *pShaderBytecodeWithInputSignature,SIZE_T BytecodeLength,ID3D11InputLayout **ppInputLayout) {
    return This->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout);
}
static inline HRESULT ID3D11Device3_CreateVertexShader(ID3D11Device3* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11VertexShader **ppVertexShader) {
    return This->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader);
}
static inline HRESULT ID3D11Device3_CreateGeometryShader(ID3D11Device3* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader);
}
static inline HRESULT ID3D11Device3_CreateGeometryShaderWithStreamOutput(ID3D11Device3* This,const void *pShaderBytecode,SIZE_T BytecodeLength,const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,UINT NumEntries,const UINT *pBufferStrides,UINT NumStrides,UINT RasterizedStream,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader);
}
static inline HRESULT ID3D11Device3_CreatePixelShader(ID3D11Device3* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11PixelShader **ppPixelShader) {
    return This->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader);
}
static inline HRESULT ID3D11Device3_CreateHullShader(ID3D11Device3* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11HullShader **ppHullShader) {
    return This->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader);
}
static inline HRESULT ID3D11Device3_CreateDomainShader(ID3D11Device3* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11DomainShader **ppDomainShader) {
    return This->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader);
}
static inline HRESULT ID3D11Device3_CreateComputeShader(ID3D11Device3* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11ComputeShader **ppComputeShader) {
    return This->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader);
}
static inline HRESULT ID3D11Device3_CreateClassLinkage(ID3D11Device3* This,ID3D11ClassLinkage **ppLinkage) {
    return This->lpVtbl->CreateClassLinkage(This,ppLinkage);
}
static inline HRESULT ID3D11Device3_CreateBlendState(ID3D11Device3* This,const D3D11_BLEND_DESC *pBlendStateDesc,ID3D11BlendState **ppBlendState) {
    return This->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState);
}
static inline HRESULT ID3D11Device3_CreateDepthStencilState(ID3D11Device3* This,const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,ID3D11DepthStencilState **ppDepthStencilState) {
    return This->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState);
}
static inline HRESULT ID3D11Device3_CreateRasterizerState(ID3D11Device3* This,const D3D11_RASTERIZER_DESC *pRasterizerDesc,ID3D11RasterizerState **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState);
}
static inline HRESULT ID3D11Device3_CreateSamplerState(ID3D11Device3* This,const D3D11_SAMPLER_DESC *pSamplerDesc,ID3D11SamplerState **ppSamplerState) {
    return This->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState);
}
static inline HRESULT ID3D11Device3_CreateQuery(ID3D11Device3* This,const D3D11_QUERY_DESC *pQueryDesc,ID3D11Query **ppQuery) {
    return This->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery);
}
static inline HRESULT ID3D11Device3_CreatePredicate(ID3D11Device3* This,const D3D11_QUERY_DESC *pPredicateDesc,ID3D11Predicate **ppPredicate) {
    return This->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate);
}
static inline HRESULT ID3D11Device3_CreateCounter(ID3D11Device3* This,const D3D11_COUNTER_DESC *pCounterDesc,ID3D11Counter **ppCounter) {
    return This->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter);
}
static inline HRESULT ID3D11Device3_CreateDeferredContext(ID3D11Device3* This,UINT ContextFlags,ID3D11DeviceContext **ppDeferredContext) {
    return This->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext);
}
static inline HRESULT ID3D11Device3_OpenSharedResource(ID3D11Device3* This,HANDLE hResource,REFIID ReturnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource);
}
static inline HRESULT ID3D11Device3_CheckFormatSupport(ID3D11Device3* This,DXGI_FORMAT Format,UINT *pFormatSupport) {
    return This->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport);
}
static inline HRESULT ID3D11Device3_CheckMultisampleQualityLevels(ID3D11Device3* This,DXGI_FORMAT Format,UINT SampleCount,UINT *pNumQualityLevels) {
    return This->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels);
}
static inline void ID3D11Device3_CheckCounterInfo(ID3D11Device3* This,D3D11_COUNTER_INFO *pCounterInfo) {
    This->lpVtbl->CheckCounterInfo(This,pCounterInfo);
}
static inline HRESULT ID3D11Device3_CheckCounter(ID3D11Device3* This,const D3D11_COUNTER_DESC *pDesc,D3D11_COUNTER_TYPE *pType,UINT *pActiveCounters,LPSTR szName,UINT *pNameLength,LPSTR szUnits,UINT *pUnitsLength,LPSTR szDescription,UINT *pDescriptionLength) {
    return This->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength);
}
static inline HRESULT ID3D11Device3_CheckFeatureSupport(ID3D11Device3* This,D3D11_FEATURE Feature,void *pFeatureSupportData,UINT FeatureSupportDataSize) {
    return This->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize);
}
static inline HRESULT ID3D11Device3_GetPrivateData(ID3D11Device3* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11Device3_SetPrivateData(ID3D11Device3* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11Device3_SetPrivateDataInterface(ID3D11Device3* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
static inline D3D_FEATURE_LEVEL ID3D11Device3_GetFeatureLevel(ID3D11Device3* This) {
    return This->lpVtbl->GetFeatureLevel(This);
}
static inline UINT ID3D11Device3_GetCreationFlags(ID3D11Device3* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
static inline HRESULT ID3D11Device3_GetDeviceRemovedReason(ID3D11Device3* This) {
    return This->lpVtbl->GetDeviceRemovedReason(This);
}
static inline void ID3D11Device3_GetImmediateContext(ID3D11Device3* This,ID3D11DeviceContext **ppImmediateContext) {
    This->lpVtbl->GetImmediateContext(This,ppImmediateContext);
}
static inline HRESULT ID3D11Device3_SetExceptionMode(ID3D11Device3* This,UINT RaiseFlags) {
    return This->lpVtbl->SetExceptionMode(This,RaiseFlags);
}
static inline UINT ID3D11Device3_GetExceptionMode(ID3D11Device3* This) {
    return This->lpVtbl->GetExceptionMode(This);
}
/*** ID3D11Device1 methods ***/
static inline void ID3D11Device3_GetImmediateContext1(ID3D11Device3* This,ID3D11DeviceContext1 **ppImmediateContext) {
    This->lpVtbl->GetImmediateContext1(This,ppImmediateContext);
}
static inline HRESULT ID3D11Device3_CreateDeferredContext1(ID3D11Device3* This,UINT ContextFlags,ID3D11DeviceContext1 **ppDeferredContext) {
    return This->lpVtbl->CreateDeferredContext1(This,ContextFlags,ppDeferredContext);
}
static inline HRESULT ID3D11Device3_CreateBlendState1(ID3D11Device3* This,const D3D11_BLEND_DESC1 *pBlendStateDesc,ID3D11BlendState1 **ppBlendState) {
    return This->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState);
}
static inline HRESULT ID3D11Device3_CreateRasterizerState1(ID3D11Device3* This,const D3D11_RASTERIZER_DESC1 *pRasterizerDesc,ID3D11RasterizerState1 **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState);
}
static inline HRESULT ID3D11Device3_CreateDeviceContextState(ID3D11Device3* This,UINT Flags,const D3D_FEATURE_LEVEL *pFeatureLevels,UINT FeatureLevels,UINT SDKVersion,REFIID EmulatedInterface,D3D_FEATURE_LEVEL *pChosenFeatureLevel,ID3DDeviceContextState **ppContextState) {
    return This->lpVtbl->CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState);
}
static inline HRESULT ID3D11Device3_OpenSharedResource1(ID3D11Device3* This,HANDLE hResource,REFIID returnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource1(This,hResource,returnedInterface,ppResource);
}
static inline HRESULT ID3D11Device3_OpenSharedResourceByName(ID3D11Device3* This,LPCWSTR lpName,DWORD dwDesiredAccess,REFIID returnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource);
}
/*** ID3D11Device2 methods ***/
static inline void ID3D11Device3_GetImmediateContext2(ID3D11Device3* This,ID3D11DeviceContext2 **context) {
    This->lpVtbl->GetImmediateContext2(This,context);
}
static inline HRESULT ID3D11Device3_CreateDeferredContext2(ID3D11Device3* This,UINT flags,ID3D11DeviceContext2 **context) {
    return This->lpVtbl->CreateDeferredContext2(This,flags,context);
}
static inline void ID3D11Device3_GetResourceTiling(ID3D11Device3* This,ID3D11Resource *resource,UINT *tile_count,D3D11_PACKED_MIP_DESC *mip_desc,D3D11_TILE_SHAPE *tile_shape,UINT *subresource_tiling_count,UINT first_subresource_tiling,D3D11_SUBRESOURCE_TILING *subresource_tiling) {
    This->lpVtbl->GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling);
}
static inline HRESULT ID3D11Device3_CheckMultisampleQualityLevels1(ID3D11Device3* This,DXGI_FORMAT format,UINT sample_count,UINT flags,UINT *quality_level_count) {
    return This->lpVtbl->CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count);
}
/*** ID3D11Device3 methods ***/
static inline HRESULT ID3D11Device3_CreateTexture2D1(ID3D11Device3* This,const D3D11_TEXTURE2D_DESC1 *desc,const D3D11_SUBRESOURCE_DATA *initial_data,ID3D11Texture2D1 **texture) {
    return This->lpVtbl->CreateTexture2D1(This,desc,initial_data,texture);
}
static inline HRESULT ID3D11Device3_CreateTexture3D1(ID3D11Device3* This,const D3D11_TEXTURE3D_DESC1 *desc,const D3D11_SUBRESOURCE_DATA *initial_data,ID3D11Texture3D1 **texture) {
    return This->lpVtbl->CreateTexture3D1(This,desc,initial_data,texture);
}
static inline HRESULT ID3D11Device3_CreateRasterizerState2(ID3D11Device3* This,const D3D11_RASTERIZER_DESC2 *desc,ID3D11RasterizerState2 **state) {
    return This->lpVtbl->CreateRasterizerState2(This,desc,state);
}
static inline HRESULT ID3D11Device3_CreateShaderResourceView1(ID3D11Device3* This,ID3D11Resource *resource,const D3D11_SHADER_RESOURCE_VIEW_DESC1 *desc,ID3D11ShaderResourceView1 **view) {
    return This->lpVtbl->CreateShaderResourceView1(This,resource,desc,view);
}
static inline HRESULT ID3D11Device3_CreateUnorderedAccessView1(ID3D11Device3* This,ID3D11Resource *resource,const D3D11_UNORDERED_ACCESS_VIEW_DESC1 *desc,ID3D11UnorderedAccessView1 **view) {
    return This->lpVtbl->CreateUnorderedAccessView1(This,resource,desc,view);
}
static inline HRESULT ID3D11Device3_CreateRenderTargetView1(ID3D11Device3* This,ID3D11Resource *resource,const D3D11_RENDER_TARGET_VIEW_DESC1 *desc,ID3D11RenderTargetView1 **view) {
    return This->lpVtbl->CreateRenderTargetView1(This,resource,desc,view);
}
static inline HRESULT ID3D11Device3_CreateQuery1(ID3D11Device3* This,const D3D11_QUERY_DESC1 *desc,ID3D11Query1 **query) {
    return This->lpVtbl->CreateQuery1(This,desc,query);
}
static inline void ID3D11Device3_GetImmediateContext3(ID3D11Device3* This,ID3D11DeviceContext3 **context) {
    This->lpVtbl->GetImmediateContext3(This,context);
}
static inline HRESULT ID3D11Device3_CreateDeferredContext3(ID3D11Device3* This,UINT flags,ID3D11DeviceContext3 **context) {
    return This->lpVtbl->CreateDeferredContext3(This,flags,context);
}
static inline void ID3D11Device3_WriteToSubresource(ID3D11Device3* This,ID3D11Resource *dst_resource,UINT dst_subresource,const D3D11_BOX *dst_box,const void *src_data,UINT src_row_pitch,UINT src_depth_pitch) {
    This->lpVtbl->WriteToSubresource(This,dst_resource,dst_subresource,dst_box,src_data,src_row_pitch,src_depth_pitch);
}
static inline void ID3D11Device3_ReadFromSubresource(ID3D11Device3* This,void *dst_data,UINT dst_row_pitch,UINT dst_depth_pitch,ID3D11Resource *src_resource,UINT src_subresource,const D3D11_BOX *src_box) {
    This->lpVtbl->ReadFromSubresource(This,dst_data,dst_row_pitch,dst_depth_pitch,src_resource,src_subresource,src_box);
}
#endif
#endif

#endif


#endif  /* __ID3D11Device3_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __d3d11_3_h__ */
