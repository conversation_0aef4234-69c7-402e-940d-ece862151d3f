/*
 * Copyright 2025 Vibhav Pant
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "windowscontracts.idl";

namespace Windows.Networking.Sockets {
    typedef enum SocketProtectionLevel SocketProtectionLevel;

    [contract(Windows.Foundation.UniversalApiContract, 1.0)]
    enum SocketProtectionLevel
    {
        PlainSocket = 0,
        Ssl = 1,
        SslAllowNullEncryption = 2,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        BluetoothEncryptionAllowNullAuthentication = 3,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        BluetoothEncryptionWithAuthentication = 4,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        Ssl3AllowWeakEncryption = 5,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        Tls10 = 6,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        Tls11 = 7,
        [contract(Windows.Foundation.UniversalApiContract, 1.0)]
        Tls12 = 8,
        [contract(Windows.Foundation.UniversalApiContract, 5.0)]
        Unspecified = 9,
        [contract(Windows.Foundation.UniversalApiContract, 16.0)]
        Tls13 = 10,
    };
}
