/*** Autogenerated by WIDL 10.12 from include/vsprov.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __vsprov_h__
#define __vsprov_h__

/* Forward declarations */

#ifndef __IVssSoftwareSnapshotProvider_FWD_DEFINED__
#define __IVssSoftwareSnapshotProvider_FWD_DEFINED__
typedef interface IVssSoftwareSnapshotProvider IVssSoftwareSnapshotProvider;
#ifdef __cplusplus
interface IVssSoftwareSnapshotProvider;
#endif /* __cplusplus */
#endif

#ifndef __IVssProviderCreateSnapshotSet_FWD_DEFINED__
#define __IVssProviderCreateSnapshotSet_FWD_DEFINED__
typedef interface IVssProviderCreateSnapshotSet IVssProviderCreateSnapshotSet;
#ifdef __cplusplus
interface IVssProviderCreateSnapshotSet;
#endif /* __cplusplus */
#endif

#ifndef __IVssProviderNotifications_FWD_DEFINED__
#define __IVssProviderNotifications_FWD_DEFINED__
typedef interface IVssProviderNotifications IVssProviderNotifications;
#ifdef __cplusplus
interface IVssProviderNotifications;
#endif /* __cplusplus */
#endif

#ifndef __IVssHardwareSnapshotProvider_FWD_DEFINED__
#define __IVssHardwareSnapshotProvider_FWD_DEFINED__
typedef interface IVssHardwareSnapshotProvider IVssHardwareSnapshotProvider;
#ifdef __cplusplus
interface IVssHardwareSnapshotProvider;
#endif /* __cplusplus */
#endif

#ifndef __IVssHardwareSnapshotProviderEx_FWD_DEFINED__
#define __IVssHardwareSnapshotProviderEx_FWD_DEFINED__
typedef interface IVssHardwareSnapshotProviderEx IVssHardwareSnapshotProviderEx;
#ifdef __cplusplus
interface IVssHardwareSnapshotProviderEx;
#endif /* __cplusplus */
#endif

#ifndef __IVssFileShareSnapshotProvider_FWD_DEFINED__
#define __IVssFileShareSnapshotProvider_FWD_DEFINED__
typedef interface IVssFileShareSnapshotProvider IVssFileShareSnapshotProvider;
#ifdef __cplusplus
interface IVssFileShareSnapshotProvider;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <vss.h>
#include <vdslun.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __IVssSnapshotProvider_FWD_DEFINED__
#define __IVssSnapshotProvider_FWD_DEFINED__
typedef interface IVssSnapshotProvider IVssSnapshotProvider;
#ifdef __cplusplus
interface IVssSnapshotProvider;
#endif /* __cplusplus */
#endif

#ifndef __IVssProviderNotifications_FWD_DEFINED__
#define __IVssProviderNotifications_FWD_DEFINED__
typedef interface IVssProviderNotifications IVssProviderNotifications;
#ifdef __cplusplus
interface IVssProviderNotifications;
#endif /* __cplusplus */
#endif

typedef VSS_PWSZ *PVSS_PWSZ;
/*****************************************************************************
 * IVssSoftwareSnapshotProvider interface
 */
#ifndef __IVssSoftwareSnapshotProvider_INTERFACE_DEFINED__
#define __IVssSoftwareSnapshotProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssSoftwareSnapshotProvider, 0x609e123e, 0x2c5a, 0x44d3, 0x8f,0x01, 0x0b,0x1d,0x9a,0x47,0xd1,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("609e123e-2c5a-44d3-8f01-0b1d9a47d1ff")
IVssSoftwareSnapshotProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetContext(
        LONG lContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSnapshotProperties(
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROP *pProp) = 0;

    virtual HRESULT STDMETHODCALLTYPE Query(
        VSS_ID QueriedObjectId,
        VSS_OBJECT_TYPE eQueriedObjectType,
        VSS_OBJECT_TYPE eReturnedObjectsType,
        IVssEnumObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteSnapshots(
        VSS_ID SourceObjectId,
        VSS_OBJECT_TYPE eSourceObjectType,
        WINBOOL bForceDelete,
        LONG *plDeletedSnapshots,
        VSS_ID *pNondeletedSnapshotID) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginPrepareSnapshot(
        VSS_ID SnapshotSetId,
        VSS_ID SnapshotId,
        VSS_PWSZ pwszVolumeName,
        LONG lNewContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsVolumeSupported(
        VSS_PWSZ pwszVolumeName,
        WINBOOL *pbSupportedByThisProvider) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsVolumeSnapshotted(
        VSS_PWSZ pwszVolumeName,
        WINBOOL *pbSnapshotsPresent,
        LONG *plSnapshotCompatibility) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSnapshotProperty(
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROPERTY_ID eSnapshotPropertyId,
        VARIANT vProperty) = 0;

    virtual HRESULT STDMETHODCALLTYPE RevertToSnapshot(
        VSS_ID SnapshotId) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryRevertStatus(
        VSS_PWSZ pwszVolume,
        IVssAsync **ppAsync) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssSoftwareSnapshotProvider, 0x609e123e, 0x2c5a, 0x44d3, 0x8f,0x01, 0x0b,0x1d,0x9a,0x47,0xd1,0xff)
#endif
#else
typedef struct IVssSoftwareSnapshotProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssSoftwareSnapshotProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssSoftwareSnapshotProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssSoftwareSnapshotProvider *This);

    /*** IVssSoftwareSnapshotProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *SetContext)(
        IVssSoftwareSnapshotProvider *This,
        LONG lContext);

    HRESULT (STDMETHODCALLTYPE *GetSnapshotProperties)(
        IVssSoftwareSnapshotProvider *This,
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROP *pProp);

    HRESULT (STDMETHODCALLTYPE *Query)(
        IVssSoftwareSnapshotProvider *This,
        VSS_ID QueriedObjectId,
        VSS_OBJECT_TYPE eQueriedObjectType,
        VSS_OBJECT_TYPE eReturnedObjectsType,
        IVssEnumObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *DeleteSnapshots)(
        IVssSoftwareSnapshotProvider *This,
        VSS_ID SourceObjectId,
        VSS_OBJECT_TYPE eSourceObjectType,
        WINBOOL bForceDelete,
        LONG *plDeletedSnapshots,
        VSS_ID *pNondeletedSnapshotID);

    HRESULT (STDMETHODCALLTYPE *BeginPrepareSnapshot)(
        IVssSoftwareSnapshotProvider *This,
        VSS_ID SnapshotSetId,
        VSS_ID SnapshotId,
        VSS_PWSZ pwszVolumeName,
        LONG lNewContext);

    HRESULT (STDMETHODCALLTYPE *IsVolumeSupported)(
        IVssSoftwareSnapshotProvider *This,
        VSS_PWSZ pwszVolumeName,
        WINBOOL *pbSupportedByThisProvider);

    HRESULT (STDMETHODCALLTYPE *IsVolumeSnapshotted)(
        IVssSoftwareSnapshotProvider *This,
        VSS_PWSZ pwszVolumeName,
        WINBOOL *pbSnapshotsPresent,
        LONG *plSnapshotCompatibility);

    HRESULT (STDMETHODCALLTYPE *SetSnapshotProperty)(
        IVssSoftwareSnapshotProvider *This,
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROPERTY_ID eSnapshotPropertyId,
        VARIANT vProperty);

    HRESULT (STDMETHODCALLTYPE *RevertToSnapshot)(
        IVssSoftwareSnapshotProvider *This,
        VSS_ID SnapshotId);

    HRESULT (STDMETHODCALLTYPE *QueryRevertStatus)(
        IVssSoftwareSnapshotProvider *This,
        VSS_PWSZ pwszVolume,
        IVssAsync **ppAsync);

    END_INTERFACE
} IVssSoftwareSnapshotProviderVtbl;

interface IVssSoftwareSnapshotProvider {
    CONST_VTBL IVssSoftwareSnapshotProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssSoftwareSnapshotProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssSoftwareSnapshotProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssSoftwareSnapshotProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IVssSoftwareSnapshotProvider methods ***/
#define IVssSoftwareSnapshotProvider_SetContext(This,lContext) (This)->lpVtbl->SetContext(This,lContext)
#define IVssSoftwareSnapshotProvider_GetSnapshotProperties(This,SnapshotId,pProp) (This)->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp)
#define IVssSoftwareSnapshotProvider_Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum) (This)->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum)
#define IVssSoftwareSnapshotProvider_DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID) (This)->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID)
#define IVssSoftwareSnapshotProvider_BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,pwszVolumeName,lNewContext) (This)->lpVtbl->BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,pwszVolumeName,lNewContext)
#define IVssSoftwareSnapshotProvider_IsVolumeSupported(This,pwszVolumeName,pbSupportedByThisProvider) (This)->lpVtbl->IsVolumeSupported(This,pwszVolumeName,pbSupportedByThisProvider)
#define IVssSoftwareSnapshotProvider_IsVolumeSnapshotted(This,pwszVolumeName,pbSnapshotsPresent,plSnapshotCompatibility) (This)->lpVtbl->IsVolumeSnapshotted(This,pwszVolumeName,pbSnapshotsPresent,plSnapshotCompatibility)
#define IVssSoftwareSnapshotProvider_SetSnapshotProperty(This,SnapshotId,eSnapshotPropertyId,vProperty) (This)->lpVtbl->SetSnapshotProperty(This,SnapshotId,eSnapshotPropertyId,vProperty)
#define IVssSoftwareSnapshotProvider_RevertToSnapshot(This,SnapshotId) (This)->lpVtbl->RevertToSnapshot(This,SnapshotId)
#define IVssSoftwareSnapshotProvider_QueryRevertStatus(This,pwszVolume,ppAsync) (This)->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssSoftwareSnapshotProvider_QueryInterface(IVssSoftwareSnapshotProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssSoftwareSnapshotProvider_AddRef(IVssSoftwareSnapshotProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssSoftwareSnapshotProvider_Release(IVssSoftwareSnapshotProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssSoftwareSnapshotProvider methods ***/
static inline HRESULT IVssSoftwareSnapshotProvider_SetContext(IVssSoftwareSnapshotProvider* This,LONG lContext) {
    return This->lpVtbl->SetContext(This,lContext);
}
static inline HRESULT IVssSoftwareSnapshotProvider_GetSnapshotProperties(IVssSoftwareSnapshotProvider* This,VSS_ID SnapshotId,VSS_SNAPSHOT_PROP *pProp) {
    return This->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp);
}
static inline HRESULT IVssSoftwareSnapshotProvider_Query(IVssSoftwareSnapshotProvider* This,VSS_ID QueriedObjectId,VSS_OBJECT_TYPE eQueriedObjectType,VSS_OBJECT_TYPE eReturnedObjectsType,IVssEnumObject **ppEnum) {
    return This->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum);
}
static inline HRESULT IVssSoftwareSnapshotProvider_DeleteSnapshots(IVssSoftwareSnapshotProvider* This,VSS_ID SourceObjectId,VSS_OBJECT_TYPE eSourceObjectType,WINBOOL bForceDelete,LONG *plDeletedSnapshots,VSS_ID *pNondeletedSnapshotID) {
    return This->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID);
}
static inline HRESULT IVssSoftwareSnapshotProvider_BeginPrepareSnapshot(IVssSoftwareSnapshotProvider* This,VSS_ID SnapshotSetId,VSS_ID SnapshotId,VSS_PWSZ pwszVolumeName,LONG lNewContext) {
    return This->lpVtbl->BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,pwszVolumeName,lNewContext);
}
static inline HRESULT IVssSoftwareSnapshotProvider_IsVolumeSupported(IVssSoftwareSnapshotProvider* This,VSS_PWSZ pwszVolumeName,WINBOOL *pbSupportedByThisProvider) {
    return This->lpVtbl->IsVolumeSupported(This,pwszVolumeName,pbSupportedByThisProvider);
}
static inline HRESULT IVssSoftwareSnapshotProvider_IsVolumeSnapshotted(IVssSoftwareSnapshotProvider* This,VSS_PWSZ pwszVolumeName,WINBOOL *pbSnapshotsPresent,LONG *plSnapshotCompatibility) {
    return This->lpVtbl->IsVolumeSnapshotted(This,pwszVolumeName,pbSnapshotsPresent,plSnapshotCompatibility);
}
static inline HRESULT IVssSoftwareSnapshotProvider_SetSnapshotProperty(IVssSoftwareSnapshotProvider* This,VSS_ID SnapshotId,VSS_SNAPSHOT_PROPERTY_ID eSnapshotPropertyId,VARIANT vProperty) {
    return This->lpVtbl->SetSnapshotProperty(This,SnapshotId,eSnapshotPropertyId,vProperty);
}
static inline HRESULT IVssSoftwareSnapshotProvider_RevertToSnapshot(IVssSoftwareSnapshotProvider* This,VSS_ID SnapshotId) {
    return This->lpVtbl->RevertToSnapshot(This,SnapshotId);
}
static inline HRESULT IVssSoftwareSnapshotProvider_QueryRevertStatus(IVssSoftwareSnapshotProvider* This,VSS_PWSZ pwszVolume,IVssAsync **ppAsync) {
    return This->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync);
}
#endif
#endif

#endif


#endif  /* __IVssSoftwareSnapshotProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssProviderCreateSnapshotSet interface
 */
#ifndef __IVssProviderCreateSnapshotSet_INTERFACE_DEFINED__
#define __IVssProviderCreateSnapshotSet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssProviderCreateSnapshotSet, 0x5f894e5b, 0x1e39, 0x4778, 0x8e,0x23, 0x9a,0xba,0xd9,0xf0,0xe0,0x8c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5f894e5b-1e39-4778-8e23-9abad9f0e08c")
IVssProviderCreateSnapshotSet : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE EndPrepareSnapshots(
        VSS_ID SnapshotSetId) = 0;

    virtual HRESULT STDMETHODCALLTYPE PreCommitSnapshots(
        VSS_ID SnapshotSetId) = 0;

    virtual HRESULT STDMETHODCALLTYPE CommitSnapshots(
        VSS_ID SnapshotSetId) = 0;

    virtual HRESULT STDMETHODCALLTYPE PostCommitSnapshots(
        VSS_ID SnapshotSetId,
        LONG lSnapshotsCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE PreFinalCommitSnapshots(
        VSS_ID SnapshotSetId) = 0;

    virtual HRESULT STDMETHODCALLTYPE PostFinalCommitSnapshots(
        VSS_ID SnapshotSetId) = 0;

    virtual HRESULT STDMETHODCALLTYPE AbortSnapshots(
        VSS_ID SnapshotSetId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssProviderCreateSnapshotSet, 0x5f894e5b, 0x1e39, 0x4778, 0x8e,0x23, 0x9a,0xba,0xd9,0xf0,0xe0,0x8c)
#endif
#else
typedef struct IVssProviderCreateSnapshotSetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssProviderCreateSnapshotSet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssProviderCreateSnapshotSet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssProviderCreateSnapshotSet *This);

    /*** IVssProviderCreateSnapshotSet methods ***/
    HRESULT (STDMETHODCALLTYPE *EndPrepareSnapshots)(
        IVssProviderCreateSnapshotSet *This,
        VSS_ID SnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *PreCommitSnapshots)(
        IVssProviderCreateSnapshotSet *This,
        VSS_ID SnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *CommitSnapshots)(
        IVssProviderCreateSnapshotSet *This,
        VSS_ID SnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *PostCommitSnapshots)(
        IVssProviderCreateSnapshotSet *This,
        VSS_ID SnapshotSetId,
        LONG lSnapshotsCount);

    HRESULT (STDMETHODCALLTYPE *PreFinalCommitSnapshots)(
        IVssProviderCreateSnapshotSet *This,
        VSS_ID SnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *PostFinalCommitSnapshots)(
        IVssProviderCreateSnapshotSet *This,
        VSS_ID SnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *AbortSnapshots)(
        IVssProviderCreateSnapshotSet *This,
        VSS_ID SnapshotSetId);

    END_INTERFACE
} IVssProviderCreateSnapshotSetVtbl;

interface IVssProviderCreateSnapshotSet {
    CONST_VTBL IVssProviderCreateSnapshotSetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssProviderCreateSnapshotSet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssProviderCreateSnapshotSet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssProviderCreateSnapshotSet_Release(This) (This)->lpVtbl->Release(This)
/*** IVssProviderCreateSnapshotSet methods ***/
#define IVssProviderCreateSnapshotSet_EndPrepareSnapshots(This,SnapshotSetId) (This)->lpVtbl->EndPrepareSnapshots(This,SnapshotSetId)
#define IVssProviderCreateSnapshotSet_PreCommitSnapshots(This,SnapshotSetId) (This)->lpVtbl->PreCommitSnapshots(This,SnapshotSetId)
#define IVssProviderCreateSnapshotSet_CommitSnapshots(This,SnapshotSetId) (This)->lpVtbl->CommitSnapshots(This,SnapshotSetId)
#define IVssProviderCreateSnapshotSet_PostCommitSnapshots(This,SnapshotSetId,lSnapshotsCount) (This)->lpVtbl->PostCommitSnapshots(This,SnapshotSetId,lSnapshotsCount)
#define IVssProviderCreateSnapshotSet_PreFinalCommitSnapshots(This,SnapshotSetId) (This)->lpVtbl->PreFinalCommitSnapshots(This,SnapshotSetId)
#define IVssProviderCreateSnapshotSet_PostFinalCommitSnapshots(This,SnapshotSetId) (This)->lpVtbl->PostFinalCommitSnapshots(This,SnapshotSetId)
#define IVssProviderCreateSnapshotSet_AbortSnapshots(This,SnapshotSetId) (This)->lpVtbl->AbortSnapshots(This,SnapshotSetId)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssProviderCreateSnapshotSet_QueryInterface(IVssProviderCreateSnapshotSet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssProviderCreateSnapshotSet_AddRef(IVssProviderCreateSnapshotSet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssProviderCreateSnapshotSet_Release(IVssProviderCreateSnapshotSet* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssProviderCreateSnapshotSet methods ***/
static inline HRESULT IVssProviderCreateSnapshotSet_EndPrepareSnapshots(IVssProviderCreateSnapshotSet* This,VSS_ID SnapshotSetId) {
    return This->lpVtbl->EndPrepareSnapshots(This,SnapshotSetId);
}
static inline HRESULT IVssProviderCreateSnapshotSet_PreCommitSnapshots(IVssProviderCreateSnapshotSet* This,VSS_ID SnapshotSetId) {
    return This->lpVtbl->PreCommitSnapshots(This,SnapshotSetId);
}
static inline HRESULT IVssProviderCreateSnapshotSet_CommitSnapshots(IVssProviderCreateSnapshotSet* This,VSS_ID SnapshotSetId) {
    return This->lpVtbl->CommitSnapshots(This,SnapshotSetId);
}
static inline HRESULT IVssProviderCreateSnapshotSet_PostCommitSnapshots(IVssProviderCreateSnapshotSet* This,VSS_ID SnapshotSetId,LONG lSnapshotsCount) {
    return This->lpVtbl->PostCommitSnapshots(This,SnapshotSetId,lSnapshotsCount);
}
static inline HRESULT IVssProviderCreateSnapshotSet_PreFinalCommitSnapshots(IVssProviderCreateSnapshotSet* This,VSS_ID SnapshotSetId) {
    return This->lpVtbl->PreFinalCommitSnapshots(This,SnapshotSetId);
}
static inline HRESULT IVssProviderCreateSnapshotSet_PostFinalCommitSnapshots(IVssProviderCreateSnapshotSet* This,VSS_ID SnapshotSetId) {
    return This->lpVtbl->PostFinalCommitSnapshots(This,SnapshotSetId);
}
static inline HRESULT IVssProviderCreateSnapshotSet_AbortSnapshots(IVssProviderCreateSnapshotSet* This,VSS_ID SnapshotSetId) {
    return This->lpVtbl->AbortSnapshots(This,SnapshotSetId);
}
#endif
#endif

#endif


#endif  /* __IVssProviderCreateSnapshotSet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssProviderNotifications interface
 */
#ifndef __IVssProviderNotifications_INTERFACE_DEFINED__
#define __IVssProviderNotifications_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssProviderNotifications, 0xe561901f, 0x03a5, 0x4afe, 0x86,0xd0, 0x72,0xba,0xee,0xce,0x70,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e561901f-03a5-4afe-86d0-72baeece7004")
IVssProviderNotifications : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnLoad(
        IUnknown *pCallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnUnload(
        WINBOOL bForceUnload) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssProviderNotifications, 0xe561901f, 0x03a5, 0x4afe, 0x86,0xd0, 0x72,0xba,0xee,0xce,0x70,0x04)
#endif
#else
typedef struct IVssProviderNotificationsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssProviderNotifications *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssProviderNotifications *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssProviderNotifications *This);

    /*** IVssProviderNotifications methods ***/
    HRESULT (STDMETHODCALLTYPE *OnLoad)(
        IVssProviderNotifications *This,
        IUnknown *pCallback);

    HRESULT (STDMETHODCALLTYPE *OnUnload)(
        IVssProviderNotifications *This,
        WINBOOL bForceUnload);

    END_INTERFACE
} IVssProviderNotificationsVtbl;

interface IVssProviderNotifications {
    CONST_VTBL IVssProviderNotificationsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssProviderNotifications_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssProviderNotifications_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssProviderNotifications_Release(This) (This)->lpVtbl->Release(This)
/*** IVssProviderNotifications methods ***/
#define IVssProviderNotifications_OnLoad(This,pCallback) (This)->lpVtbl->OnLoad(This,pCallback)
#define IVssProviderNotifications_OnUnload(This,bForceUnload) (This)->lpVtbl->OnUnload(This,bForceUnload)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssProviderNotifications_QueryInterface(IVssProviderNotifications* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssProviderNotifications_AddRef(IVssProviderNotifications* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssProviderNotifications_Release(IVssProviderNotifications* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssProviderNotifications methods ***/
static inline HRESULT IVssProviderNotifications_OnLoad(IVssProviderNotifications* This,IUnknown *pCallback) {
    return This->lpVtbl->OnLoad(This,pCallback);
}
static inline HRESULT IVssProviderNotifications_OnUnload(IVssProviderNotifications* This,WINBOOL bForceUnload) {
    return This->lpVtbl->OnUnload(This,bForceUnload);
}
#endif
#endif

#endif


#endif  /* __IVssProviderNotifications_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssHardwareSnapshotProvider interface
 */
#ifndef __IVssHardwareSnapshotProvider_INTERFACE_DEFINED__
#define __IVssHardwareSnapshotProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssHardwareSnapshotProvider, 0x9593a157, 0x44e9, 0x4344, 0xbb,0xeb, 0x44,0xfb,0xf9,0xb0,0x6b,0x10);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9593a157-44e9-4344-bbeb-44fbf9b06b10")
IVssHardwareSnapshotProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AreLunsSupported(
        LONG lLunCount,
        LONG lContext,
        VSS_PWSZ *rgwszDevices,
        VDS_LUN_INFORMATION *pLunInformation,
        WINBOOL *pbIsSupported) = 0;

    virtual HRESULT STDMETHODCALLTYPE FillInLunInfo(
        VSS_PWSZ wszDeviceName,
        VDS_LUN_INFORMATION *pLunInfo,
        WINBOOL *pbIsSupported) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginPrepareSnapshot(
        VSS_ID SnapshotSetId,
        VSS_ID SnapshotId,
        LONG lContext,
        LONG lLunCount,
        VSS_PWSZ *rgDeviceNames,
        VDS_LUN_INFORMATION *rgLunInformation) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTargetLuns(
        LONG lLunCount,
        VSS_PWSZ *rgDeviceNames,
        VDS_LUN_INFORMATION *rgSourceLuns,
        VDS_LUN_INFORMATION *rgDestinationLuns) = 0;

    virtual HRESULT STDMETHODCALLTYPE LocateLuns(
        LONG lLunCount,
        VDS_LUN_INFORMATION *rgSourceLuns) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnLunEmpty(
        VSS_PWSZ wszDeviceName,
        VDS_LUN_INFORMATION *pInformation) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssHardwareSnapshotProvider, 0x9593a157, 0x44e9, 0x4344, 0xbb,0xeb, 0x44,0xfb,0xf9,0xb0,0x6b,0x10)
#endif
#else
typedef struct IVssHardwareSnapshotProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssHardwareSnapshotProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssHardwareSnapshotProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssHardwareSnapshotProvider *This);

    /*** IVssHardwareSnapshotProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *AreLunsSupported)(
        IVssHardwareSnapshotProvider *This,
        LONG lLunCount,
        LONG lContext,
        VSS_PWSZ *rgwszDevices,
        VDS_LUN_INFORMATION *pLunInformation,
        WINBOOL *pbIsSupported);

    HRESULT (STDMETHODCALLTYPE *FillInLunInfo)(
        IVssHardwareSnapshotProvider *This,
        VSS_PWSZ wszDeviceName,
        VDS_LUN_INFORMATION *pLunInfo,
        WINBOOL *pbIsSupported);

    HRESULT (STDMETHODCALLTYPE *BeginPrepareSnapshot)(
        IVssHardwareSnapshotProvider *This,
        VSS_ID SnapshotSetId,
        VSS_ID SnapshotId,
        LONG lContext,
        LONG lLunCount,
        VSS_PWSZ *rgDeviceNames,
        VDS_LUN_INFORMATION *rgLunInformation);

    HRESULT (STDMETHODCALLTYPE *GetTargetLuns)(
        IVssHardwareSnapshotProvider *This,
        LONG lLunCount,
        VSS_PWSZ *rgDeviceNames,
        VDS_LUN_INFORMATION *rgSourceLuns,
        VDS_LUN_INFORMATION *rgDestinationLuns);

    HRESULT (STDMETHODCALLTYPE *LocateLuns)(
        IVssHardwareSnapshotProvider *This,
        LONG lLunCount,
        VDS_LUN_INFORMATION *rgSourceLuns);

    HRESULT (STDMETHODCALLTYPE *OnLunEmpty)(
        IVssHardwareSnapshotProvider *This,
        VSS_PWSZ wszDeviceName,
        VDS_LUN_INFORMATION *pInformation);

    END_INTERFACE
} IVssHardwareSnapshotProviderVtbl;

interface IVssHardwareSnapshotProvider {
    CONST_VTBL IVssHardwareSnapshotProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssHardwareSnapshotProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssHardwareSnapshotProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssHardwareSnapshotProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IVssHardwareSnapshotProvider methods ***/
#define IVssHardwareSnapshotProvider_AreLunsSupported(This,lLunCount,lContext,rgwszDevices,pLunInformation,pbIsSupported) (This)->lpVtbl->AreLunsSupported(This,lLunCount,lContext,rgwszDevices,pLunInformation,pbIsSupported)
#define IVssHardwareSnapshotProvider_FillInLunInfo(This,wszDeviceName,pLunInfo,pbIsSupported) (This)->lpVtbl->FillInLunInfo(This,wszDeviceName,pLunInfo,pbIsSupported)
#define IVssHardwareSnapshotProvider_BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,lContext,lLunCount,rgDeviceNames,rgLunInformation) (This)->lpVtbl->BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,lContext,lLunCount,rgDeviceNames,rgLunInformation)
#define IVssHardwareSnapshotProvider_GetTargetLuns(This,lLunCount,rgDeviceNames,rgSourceLuns,rgDestinationLuns) (This)->lpVtbl->GetTargetLuns(This,lLunCount,rgDeviceNames,rgSourceLuns,rgDestinationLuns)
#define IVssHardwareSnapshotProvider_LocateLuns(This,lLunCount,rgSourceLuns) (This)->lpVtbl->LocateLuns(This,lLunCount,rgSourceLuns)
#define IVssHardwareSnapshotProvider_OnLunEmpty(This,wszDeviceName,pInformation) (This)->lpVtbl->OnLunEmpty(This,wszDeviceName,pInformation)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssHardwareSnapshotProvider_QueryInterface(IVssHardwareSnapshotProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssHardwareSnapshotProvider_AddRef(IVssHardwareSnapshotProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssHardwareSnapshotProvider_Release(IVssHardwareSnapshotProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssHardwareSnapshotProvider methods ***/
static inline HRESULT IVssHardwareSnapshotProvider_AreLunsSupported(IVssHardwareSnapshotProvider* This,LONG lLunCount,LONG lContext,VSS_PWSZ *rgwszDevices,VDS_LUN_INFORMATION *pLunInformation,WINBOOL *pbIsSupported) {
    return This->lpVtbl->AreLunsSupported(This,lLunCount,lContext,rgwszDevices,pLunInformation,pbIsSupported);
}
static inline HRESULT IVssHardwareSnapshotProvider_FillInLunInfo(IVssHardwareSnapshotProvider* This,VSS_PWSZ wszDeviceName,VDS_LUN_INFORMATION *pLunInfo,WINBOOL *pbIsSupported) {
    return This->lpVtbl->FillInLunInfo(This,wszDeviceName,pLunInfo,pbIsSupported);
}
static inline HRESULT IVssHardwareSnapshotProvider_BeginPrepareSnapshot(IVssHardwareSnapshotProvider* This,VSS_ID SnapshotSetId,VSS_ID SnapshotId,LONG lContext,LONG lLunCount,VSS_PWSZ *rgDeviceNames,VDS_LUN_INFORMATION *rgLunInformation) {
    return This->lpVtbl->BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,lContext,lLunCount,rgDeviceNames,rgLunInformation);
}
static inline HRESULT IVssHardwareSnapshotProvider_GetTargetLuns(IVssHardwareSnapshotProvider* This,LONG lLunCount,VSS_PWSZ *rgDeviceNames,VDS_LUN_INFORMATION *rgSourceLuns,VDS_LUN_INFORMATION *rgDestinationLuns) {
    return This->lpVtbl->GetTargetLuns(This,lLunCount,rgDeviceNames,rgSourceLuns,rgDestinationLuns);
}
static inline HRESULT IVssHardwareSnapshotProvider_LocateLuns(IVssHardwareSnapshotProvider* This,LONG lLunCount,VDS_LUN_INFORMATION *rgSourceLuns) {
    return This->lpVtbl->LocateLuns(This,lLunCount,rgSourceLuns);
}
static inline HRESULT IVssHardwareSnapshotProvider_OnLunEmpty(IVssHardwareSnapshotProvider* This,VSS_PWSZ wszDeviceName,VDS_LUN_INFORMATION *pInformation) {
    return This->lpVtbl->OnLunEmpty(This,wszDeviceName,pInformation);
}
#endif
#endif

#endif


#endif  /* __IVssHardwareSnapshotProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssHardwareSnapshotProviderEx interface
 */
#ifndef __IVssHardwareSnapshotProviderEx_INTERFACE_DEFINED__
#define __IVssHardwareSnapshotProviderEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssHardwareSnapshotProviderEx, 0x7f5ba925, 0xcdb1, 0x4d11, 0xa7,0x1f, 0x33,0x9e,0xb7,0xe7,0x09,0xfd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7f5ba925-cdb1-4d11-a71f-339eb7e709fd")
IVssHardwareSnapshotProviderEx : public IVssHardwareSnapshotProvider
{
    virtual HRESULT STDMETHODCALLTYPE GetProviderCapabilities(
        ULONGLONG *pllOriginalCapabilityMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnLunStateChange(
        VDS_LUN_INFORMATION *pSnapshotLuns,
        VDS_LUN_INFORMATION *pOriginalLuns,
        DWORD dwCount,
        DWORD dwFlags) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResyncLuns(
        VDS_LUN_INFORMATION *pSourceLuns,
        VDS_LUN_INFORMATION *pTargetLuns,
        DWORD dwCount,
        IVssAsync **ppAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnReuseLuns(
        VDS_LUN_INFORMATION *pSnapshotLuns,
        VDS_LUN_INFORMATION *pOriginalLuns,
        DWORD dwCount) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssHardwareSnapshotProviderEx, 0x7f5ba925, 0xcdb1, 0x4d11, 0xa7,0x1f, 0x33,0x9e,0xb7,0xe7,0x09,0xfd)
#endif
#else
typedef struct IVssHardwareSnapshotProviderExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssHardwareSnapshotProviderEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssHardwareSnapshotProviderEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssHardwareSnapshotProviderEx *This);

    /*** IVssHardwareSnapshotProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *AreLunsSupported)(
        IVssHardwareSnapshotProviderEx *This,
        LONG lLunCount,
        LONG lContext,
        VSS_PWSZ *rgwszDevices,
        VDS_LUN_INFORMATION *pLunInformation,
        WINBOOL *pbIsSupported);

    HRESULT (STDMETHODCALLTYPE *FillInLunInfo)(
        IVssHardwareSnapshotProviderEx *This,
        VSS_PWSZ wszDeviceName,
        VDS_LUN_INFORMATION *pLunInfo,
        WINBOOL *pbIsSupported);

    HRESULT (STDMETHODCALLTYPE *BeginPrepareSnapshot)(
        IVssHardwareSnapshotProviderEx *This,
        VSS_ID SnapshotSetId,
        VSS_ID SnapshotId,
        LONG lContext,
        LONG lLunCount,
        VSS_PWSZ *rgDeviceNames,
        VDS_LUN_INFORMATION *rgLunInformation);

    HRESULT (STDMETHODCALLTYPE *GetTargetLuns)(
        IVssHardwareSnapshotProviderEx *This,
        LONG lLunCount,
        VSS_PWSZ *rgDeviceNames,
        VDS_LUN_INFORMATION *rgSourceLuns,
        VDS_LUN_INFORMATION *rgDestinationLuns);

    HRESULT (STDMETHODCALLTYPE *LocateLuns)(
        IVssHardwareSnapshotProviderEx *This,
        LONG lLunCount,
        VDS_LUN_INFORMATION *rgSourceLuns);

    HRESULT (STDMETHODCALLTYPE *OnLunEmpty)(
        IVssHardwareSnapshotProviderEx *This,
        VSS_PWSZ wszDeviceName,
        VDS_LUN_INFORMATION *pInformation);

    /*** IVssHardwareSnapshotProviderEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProviderCapabilities)(
        IVssHardwareSnapshotProviderEx *This,
        ULONGLONG *pllOriginalCapabilityMask);

    HRESULT (STDMETHODCALLTYPE *OnLunStateChange)(
        IVssHardwareSnapshotProviderEx *This,
        VDS_LUN_INFORMATION *pSnapshotLuns,
        VDS_LUN_INFORMATION *pOriginalLuns,
        DWORD dwCount,
        DWORD dwFlags);

    HRESULT (STDMETHODCALLTYPE *ResyncLuns)(
        IVssHardwareSnapshotProviderEx *This,
        VDS_LUN_INFORMATION *pSourceLuns,
        VDS_LUN_INFORMATION *pTargetLuns,
        DWORD dwCount,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *OnReuseLuns)(
        IVssHardwareSnapshotProviderEx *This,
        VDS_LUN_INFORMATION *pSnapshotLuns,
        VDS_LUN_INFORMATION *pOriginalLuns,
        DWORD dwCount);

    END_INTERFACE
} IVssHardwareSnapshotProviderExVtbl;

interface IVssHardwareSnapshotProviderEx {
    CONST_VTBL IVssHardwareSnapshotProviderExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssHardwareSnapshotProviderEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssHardwareSnapshotProviderEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssHardwareSnapshotProviderEx_Release(This) (This)->lpVtbl->Release(This)
/*** IVssHardwareSnapshotProvider methods ***/
#define IVssHardwareSnapshotProviderEx_AreLunsSupported(This,lLunCount,lContext,rgwszDevices,pLunInformation,pbIsSupported) (This)->lpVtbl->AreLunsSupported(This,lLunCount,lContext,rgwszDevices,pLunInformation,pbIsSupported)
#define IVssHardwareSnapshotProviderEx_FillInLunInfo(This,wszDeviceName,pLunInfo,pbIsSupported) (This)->lpVtbl->FillInLunInfo(This,wszDeviceName,pLunInfo,pbIsSupported)
#define IVssHardwareSnapshotProviderEx_BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,lContext,lLunCount,rgDeviceNames,rgLunInformation) (This)->lpVtbl->BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,lContext,lLunCount,rgDeviceNames,rgLunInformation)
#define IVssHardwareSnapshotProviderEx_GetTargetLuns(This,lLunCount,rgDeviceNames,rgSourceLuns,rgDestinationLuns) (This)->lpVtbl->GetTargetLuns(This,lLunCount,rgDeviceNames,rgSourceLuns,rgDestinationLuns)
#define IVssHardwareSnapshotProviderEx_LocateLuns(This,lLunCount,rgSourceLuns) (This)->lpVtbl->LocateLuns(This,lLunCount,rgSourceLuns)
#define IVssHardwareSnapshotProviderEx_OnLunEmpty(This,wszDeviceName,pInformation) (This)->lpVtbl->OnLunEmpty(This,wszDeviceName,pInformation)
/*** IVssHardwareSnapshotProviderEx methods ***/
#define IVssHardwareSnapshotProviderEx_GetProviderCapabilities(This,pllOriginalCapabilityMask) (This)->lpVtbl->GetProviderCapabilities(This,pllOriginalCapabilityMask)
#define IVssHardwareSnapshotProviderEx_OnLunStateChange(This,pSnapshotLuns,pOriginalLuns,dwCount,dwFlags) (This)->lpVtbl->OnLunStateChange(This,pSnapshotLuns,pOriginalLuns,dwCount,dwFlags)
#define IVssHardwareSnapshotProviderEx_ResyncLuns(This,pSourceLuns,pTargetLuns,dwCount,ppAsync) (This)->lpVtbl->ResyncLuns(This,pSourceLuns,pTargetLuns,dwCount,ppAsync)
#define IVssHardwareSnapshotProviderEx_OnReuseLuns(This,pSnapshotLuns,pOriginalLuns,dwCount) (This)->lpVtbl->OnReuseLuns(This,pSnapshotLuns,pOriginalLuns,dwCount)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssHardwareSnapshotProviderEx_QueryInterface(IVssHardwareSnapshotProviderEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssHardwareSnapshotProviderEx_AddRef(IVssHardwareSnapshotProviderEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssHardwareSnapshotProviderEx_Release(IVssHardwareSnapshotProviderEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssHardwareSnapshotProvider methods ***/
static inline HRESULT IVssHardwareSnapshotProviderEx_AreLunsSupported(IVssHardwareSnapshotProviderEx* This,LONG lLunCount,LONG lContext,VSS_PWSZ *rgwszDevices,VDS_LUN_INFORMATION *pLunInformation,WINBOOL *pbIsSupported) {
    return This->lpVtbl->AreLunsSupported(This,lLunCount,lContext,rgwszDevices,pLunInformation,pbIsSupported);
}
static inline HRESULT IVssHardwareSnapshotProviderEx_FillInLunInfo(IVssHardwareSnapshotProviderEx* This,VSS_PWSZ wszDeviceName,VDS_LUN_INFORMATION *pLunInfo,WINBOOL *pbIsSupported) {
    return This->lpVtbl->FillInLunInfo(This,wszDeviceName,pLunInfo,pbIsSupported);
}
static inline HRESULT IVssHardwareSnapshotProviderEx_BeginPrepareSnapshot(IVssHardwareSnapshotProviderEx* This,VSS_ID SnapshotSetId,VSS_ID SnapshotId,LONG lContext,LONG lLunCount,VSS_PWSZ *rgDeviceNames,VDS_LUN_INFORMATION *rgLunInformation) {
    return This->lpVtbl->BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,lContext,lLunCount,rgDeviceNames,rgLunInformation);
}
static inline HRESULT IVssHardwareSnapshotProviderEx_GetTargetLuns(IVssHardwareSnapshotProviderEx* This,LONG lLunCount,VSS_PWSZ *rgDeviceNames,VDS_LUN_INFORMATION *rgSourceLuns,VDS_LUN_INFORMATION *rgDestinationLuns) {
    return This->lpVtbl->GetTargetLuns(This,lLunCount,rgDeviceNames,rgSourceLuns,rgDestinationLuns);
}
static inline HRESULT IVssHardwareSnapshotProviderEx_LocateLuns(IVssHardwareSnapshotProviderEx* This,LONG lLunCount,VDS_LUN_INFORMATION *rgSourceLuns) {
    return This->lpVtbl->LocateLuns(This,lLunCount,rgSourceLuns);
}
static inline HRESULT IVssHardwareSnapshotProviderEx_OnLunEmpty(IVssHardwareSnapshotProviderEx* This,VSS_PWSZ wszDeviceName,VDS_LUN_INFORMATION *pInformation) {
    return This->lpVtbl->OnLunEmpty(This,wszDeviceName,pInformation);
}
/*** IVssHardwareSnapshotProviderEx methods ***/
static inline HRESULT IVssHardwareSnapshotProviderEx_GetProviderCapabilities(IVssHardwareSnapshotProviderEx* This,ULONGLONG *pllOriginalCapabilityMask) {
    return This->lpVtbl->GetProviderCapabilities(This,pllOriginalCapabilityMask);
}
static inline HRESULT IVssHardwareSnapshotProviderEx_OnLunStateChange(IVssHardwareSnapshotProviderEx* This,VDS_LUN_INFORMATION *pSnapshotLuns,VDS_LUN_INFORMATION *pOriginalLuns,DWORD dwCount,DWORD dwFlags) {
    return This->lpVtbl->OnLunStateChange(This,pSnapshotLuns,pOriginalLuns,dwCount,dwFlags);
}
static inline HRESULT IVssHardwareSnapshotProviderEx_ResyncLuns(IVssHardwareSnapshotProviderEx* This,VDS_LUN_INFORMATION *pSourceLuns,VDS_LUN_INFORMATION *pTargetLuns,DWORD dwCount,IVssAsync **ppAsync) {
    return This->lpVtbl->ResyncLuns(This,pSourceLuns,pTargetLuns,dwCount,ppAsync);
}
static inline HRESULT IVssHardwareSnapshotProviderEx_OnReuseLuns(IVssHardwareSnapshotProviderEx* This,VDS_LUN_INFORMATION *pSnapshotLuns,VDS_LUN_INFORMATION *pOriginalLuns,DWORD dwCount) {
    return This->lpVtbl->OnReuseLuns(This,pSnapshotLuns,pOriginalLuns,dwCount);
}
#endif
#endif

#endif


#endif  /* __IVssHardwareSnapshotProviderEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssFileShareSnapshotProvider interface
 */
#ifndef __IVssFileShareSnapshotProvider_INTERFACE_DEFINED__
#define __IVssFileShareSnapshotProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssFileShareSnapshotProvider, 0xc8636060, 0x7c2e, 0x11df, 0x8c,0x4a, 0x08,0x00,0x20,0x0c,0x9a,0x66);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c8636060-7c2e-11df-8c4a-0800200c9a66")
IVssFileShareSnapshotProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetContext(
        LONG lContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSnapshotProperties(
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROP *pProp) = 0;

    virtual HRESULT STDMETHODCALLTYPE Query(
        VSS_ID QueriedObjectId,
        VSS_OBJECT_TYPE eQueriedObjectType,
        VSS_OBJECT_TYPE eReturnedObjectsType,
        IVssEnumObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteSnapshots(
        VSS_ID SourceObjectId,
        VSS_OBJECT_TYPE eSourceObjectType,
        WINBOOL bForceDelete,
        LONG *plDeletedSnapshots,
        VSS_ID *pNondeletedSnapshotID) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginPrepareSnapshot(
        VSS_ID SnapshotSetId,
        VSS_ID SnapshotId,
        VSS_PWSZ pwszSharePath,
        LONG lNewContext,
        VSS_ID ProviderId) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsPathSupported(
        VSS_PWSZ pwszSharePath,
        WINBOOL *pbSupportedByThisProvider) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsPathSnapshotted(
        VSS_PWSZ pwszSharePath,
        WINBOOL *pbSnapshotsPresent,
        LONG *plSnapshotCompatibility) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSnapshotProperty(
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROPERTY_ID eSnapshotPropertyId,
        VARIANT vProperty) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssFileShareSnapshotProvider, 0xc8636060, 0x7c2e, 0x11df, 0x8c,0x4a, 0x08,0x00,0x20,0x0c,0x9a,0x66)
#endif
#else
typedef struct IVssFileShareSnapshotProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssFileShareSnapshotProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssFileShareSnapshotProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssFileShareSnapshotProvider *This);

    /*** IVssFileShareSnapshotProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *SetContext)(
        IVssFileShareSnapshotProvider *This,
        LONG lContext);

    HRESULT (STDMETHODCALLTYPE *GetSnapshotProperties)(
        IVssFileShareSnapshotProvider *This,
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROP *pProp);

    HRESULT (STDMETHODCALLTYPE *Query)(
        IVssFileShareSnapshotProvider *This,
        VSS_ID QueriedObjectId,
        VSS_OBJECT_TYPE eQueriedObjectType,
        VSS_OBJECT_TYPE eReturnedObjectsType,
        IVssEnumObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *DeleteSnapshots)(
        IVssFileShareSnapshotProvider *This,
        VSS_ID SourceObjectId,
        VSS_OBJECT_TYPE eSourceObjectType,
        WINBOOL bForceDelete,
        LONG *plDeletedSnapshots,
        VSS_ID *pNondeletedSnapshotID);

    HRESULT (STDMETHODCALLTYPE *BeginPrepareSnapshot)(
        IVssFileShareSnapshotProvider *This,
        VSS_ID SnapshotSetId,
        VSS_ID SnapshotId,
        VSS_PWSZ pwszSharePath,
        LONG lNewContext,
        VSS_ID ProviderId);

    HRESULT (STDMETHODCALLTYPE *IsPathSupported)(
        IVssFileShareSnapshotProvider *This,
        VSS_PWSZ pwszSharePath,
        WINBOOL *pbSupportedByThisProvider);

    HRESULT (STDMETHODCALLTYPE *IsPathSnapshotted)(
        IVssFileShareSnapshotProvider *This,
        VSS_PWSZ pwszSharePath,
        WINBOOL *pbSnapshotsPresent,
        LONG *plSnapshotCompatibility);

    HRESULT (STDMETHODCALLTYPE *SetSnapshotProperty)(
        IVssFileShareSnapshotProvider *This,
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROPERTY_ID eSnapshotPropertyId,
        VARIANT vProperty);

    END_INTERFACE
} IVssFileShareSnapshotProviderVtbl;

interface IVssFileShareSnapshotProvider {
    CONST_VTBL IVssFileShareSnapshotProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssFileShareSnapshotProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssFileShareSnapshotProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssFileShareSnapshotProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IVssFileShareSnapshotProvider methods ***/
#define IVssFileShareSnapshotProvider_SetContext(This,lContext) (This)->lpVtbl->SetContext(This,lContext)
#define IVssFileShareSnapshotProvider_GetSnapshotProperties(This,SnapshotId,pProp) (This)->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp)
#define IVssFileShareSnapshotProvider_Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum) (This)->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum)
#define IVssFileShareSnapshotProvider_DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID) (This)->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID)
#define IVssFileShareSnapshotProvider_BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,pwszSharePath,lNewContext,ProviderId) (This)->lpVtbl->BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,pwszSharePath,lNewContext,ProviderId)
#define IVssFileShareSnapshotProvider_IsPathSupported(This,pwszSharePath,pbSupportedByThisProvider) (This)->lpVtbl->IsPathSupported(This,pwszSharePath,pbSupportedByThisProvider)
#define IVssFileShareSnapshotProvider_IsPathSnapshotted(This,pwszSharePath,pbSnapshotsPresent,plSnapshotCompatibility) (This)->lpVtbl->IsPathSnapshotted(This,pwszSharePath,pbSnapshotsPresent,plSnapshotCompatibility)
#define IVssFileShareSnapshotProvider_SetSnapshotProperty(This,SnapshotId,eSnapshotPropertyId,vProperty) (This)->lpVtbl->SetSnapshotProperty(This,SnapshotId,eSnapshotPropertyId,vProperty)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssFileShareSnapshotProvider_QueryInterface(IVssFileShareSnapshotProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssFileShareSnapshotProvider_AddRef(IVssFileShareSnapshotProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssFileShareSnapshotProvider_Release(IVssFileShareSnapshotProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssFileShareSnapshotProvider methods ***/
static inline HRESULT IVssFileShareSnapshotProvider_SetContext(IVssFileShareSnapshotProvider* This,LONG lContext) {
    return This->lpVtbl->SetContext(This,lContext);
}
static inline HRESULT IVssFileShareSnapshotProvider_GetSnapshotProperties(IVssFileShareSnapshotProvider* This,VSS_ID SnapshotId,VSS_SNAPSHOT_PROP *pProp) {
    return This->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp);
}
static inline HRESULT IVssFileShareSnapshotProvider_Query(IVssFileShareSnapshotProvider* This,VSS_ID QueriedObjectId,VSS_OBJECT_TYPE eQueriedObjectType,VSS_OBJECT_TYPE eReturnedObjectsType,IVssEnumObject **ppEnum) {
    return This->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum);
}
static inline HRESULT IVssFileShareSnapshotProvider_DeleteSnapshots(IVssFileShareSnapshotProvider* This,VSS_ID SourceObjectId,VSS_OBJECT_TYPE eSourceObjectType,WINBOOL bForceDelete,LONG *plDeletedSnapshots,VSS_ID *pNondeletedSnapshotID) {
    return This->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID);
}
static inline HRESULT IVssFileShareSnapshotProvider_BeginPrepareSnapshot(IVssFileShareSnapshotProvider* This,VSS_ID SnapshotSetId,VSS_ID SnapshotId,VSS_PWSZ pwszSharePath,LONG lNewContext,VSS_ID ProviderId) {
    return This->lpVtbl->BeginPrepareSnapshot(This,SnapshotSetId,SnapshotId,pwszSharePath,lNewContext,ProviderId);
}
static inline HRESULT IVssFileShareSnapshotProvider_IsPathSupported(IVssFileShareSnapshotProvider* This,VSS_PWSZ pwszSharePath,WINBOOL *pbSupportedByThisProvider) {
    return This->lpVtbl->IsPathSupported(This,pwszSharePath,pbSupportedByThisProvider);
}
static inline HRESULT IVssFileShareSnapshotProvider_IsPathSnapshotted(IVssFileShareSnapshotProvider* This,VSS_PWSZ pwszSharePath,WINBOOL *pbSnapshotsPresent,LONG *plSnapshotCompatibility) {
    return This->lpVtbl->IsPathSnapshotted(This,pwszSharePath,pbSnapshotsPresent,plSnapshotCompatibility);
}
static inline HRESULT IVssFileShareSnapshotProvider_SetSnapshotProperty(IVssFileShareSnapshotProvider* This,VSS_ID SnapshotId,VSS_SNAPSHOT_PROPERTY_ID eSnapshotPropertyId,VARIANT vProperty) {
    return This->lpVtbl->SetSnapshotProperty(This,SnapshotId,eSnapshotPropertyId,vProperty);
}
#endif
#endif

#endif


#endif  /* __IVssFileShareSnapshotProvider_INTERFACE_DEFINED__ */

#ifndef __VSSProvider_LIBRARY_DEFINED__
#define __VSSProvider_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_VSSProvider, 0x73c8b4c1, 0x6e9d, 0x4fc2, 0xb3,0x04, 0x03,0x0e,0xc7,0x63,0xfe,0x81);

#endif /* __VSSProvider_LIBRARY_DEFINED__ */
#endif /* WINAPI_PARTITION_DESKTOP */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __vsprov_h__ */
