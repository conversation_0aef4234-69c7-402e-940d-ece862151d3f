/* Copyright (c) 2017-2018 Mozilla
   Copyright (c) 2023 Amazon */
/*
   Redistribution and use in source and binary forms, with or without
   modification, are permitted provided that the following conditions
   are met:

   - Redistributions of source code must retain the above copyright
   notice, this list of conditions and the following disclaimer.

   - Redistributions in binary form must reproduce the above copyright
   notice, this list of conditions and the following disclaimer in the
   documentation and/or other materials provided with the distribution.

   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
   ``AS IS'' AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
   LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTAB<PERSON>ITY AND FITNESS FOR
   A PARTICULAR PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE FOUNDATION OR
   CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
   EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
   PROCUREMENT OF SUBS<PERSON>TUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
   PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
   LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
   NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
   SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

#include <math.h>
#include <stdio.h>
#include "denoise.h"
#include "kiss_fft.h"

#define OVERLAP_SIZE FRAME_SIZE

int main(void) {
  int i;
  FILE *file;
  kiss_fft_state *kfft;
  float half_window[OVERLAP_SIZE];
  float dct_table[NB_BANDS*NB_BANDS];

  file=fopen("rnnoise_tables.c", "wb");
  fprintf(file, "/* The contents of this file was automatically generated by dump_rnnoise_tables.c*/\n\n");
  fprintf(file, "#ifdef HAVE_CONFIG_H\n");
  fprintf(file, "#include \"config.h\"\n");
  fprintf(file, "#endif\n");

  fprintf(file, "#include \"kiss_fft.h\"\n\n");

  kfft = rnn_fft_alloc_twiddles(WINDOW_SIZE, NULL, NULL, NULL, 0);

  fprintf(file, "static const arch_fft_state arch_fft = {0, NULL};\n\n");

  fprintf (file, "static const opus_int32 fft_bitrev[%d] = {\n", kfft->nfft);
  for (i=0;i<kfft->nfft;i++)
    fprintf (file, "%d,%c", kfft->bitrev[i],(i+16)%15==0?'\n':' ');
  fprintf (file, "};\n\n");

  fprintf (file, "static const kiss_twiddle_cpx fft_twiddles[%d] = {\n", kfft->nfft);
  for (i=0;i<kfft->nfft;i++)
    fprintf (file, "{%#0.9gf, %#0.9gf},%c", kfft->twiddles[i].r, kfft->twiddles[i].i,(i+3)%2==0?'\n':' ');
  fprintf (file, "};\n\n");


  fprintf(file, "const kiss_fft_state rnn_kfft = {\n");
  fprintf(file, "%d, /* nfft */\n", kfft->nfft);
  fprintf(file, "%#0.8gf, /* scale */\n", kfft->scale);
  fprintf(file, "%d, /* shift */\n", kfft->shift);
  fprintf(file, "{");
  for (i=0;i<2*MAXFACTORS;i++) {
    fprintf(file, "%d, ", kfft->factors[i]);
  }
  fprintf(file, "}, /* factors */\n");
  fprintf(file, "fft_bitrev, /* bitrev*/\n");
  fprintf(file, "fft_twiddles, /* twiddles*/\n");
  fprintf(file, "(arch_fft_state *)&arch_fft, /* arch_fft*/\n");

  fprintf(file, "};\n\n");

  for (i=0;i<OVERLAP_SIZE;i++)
    half_window[i] = sin(.5*M_PI*sin(.5*M_PI*(i+.5)/OVERLAP_SIZE) * sin(.5*M_PI*(i+.5)/OVERLAP_SIZE));
  fprintf(file, "const float rnn_half_window[] = {\n");
  for (i=0;i<OVERLAP_SIZE;i++)
    fprintf (file, "%#0.9gf,%c", half_window[i],(i+6)%5==0?'\n':' ');
  fprintf(file, "};\n\n");

  for (i=0;i<NB_BANDS;i++) {
    int j;
    for (j=0;j<NB_BANDS;j++) {
      dct_table[i*NB_BANDS + j] = cos((i+.5)*j*M_PI/NB_BANDS);
      if (j==0) dct_table[i*NB_BANDS + j] *= sqrt(.5);
    }
  }
  fprintf(file, "const float rnn_dct_table[] = {\n");
  for (i=0;i<NB_BANDS*NB_BANDS;i++)
    fprintf (file, "%#0.9gf,%c", dct_table[i],(i+6)%5==0?'\n':' ');
  fprintf(file, "};\n");

  fclose(file);
  return 0;
}
