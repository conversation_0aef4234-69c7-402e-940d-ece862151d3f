[2025-06-22T10:11:18+0000] [PACMAN] Running 'pacman -Syu --root /d/a/msys2-installer/msys2-installer/_build/newmsys/msys64'
[2025-06-22T10:11:18+0000] [PACMAN] synchronizing package lists
[2025-06-22T10:11:21+0000] [PACMAN] starting full system upgrade
[2025-06-22T10:11:21+0000] [PACMAN] Running 'pacman -S --noconfirm --root /d/a/msys2-installer/msys2-installer/_build/newmsys/msys64 filesystem msys2-runtime'
[2025-06-22T10:11:24+0000] [ALPM] transaction started
[2025-06-22T10:11:24+0000] [ALPM] installed filesystem (2025.05.08-2)
[2025-06-22T10:11:24+0000] [ALPM] installed msys2-runtime (3.6.3-3)
[2025-06-22T10:11:24+0000] [ALPM] transaction completed
[2025-06-22T10:11:25+0000] [PACMAN] Running 'pacman -S --noconfirm --root /d/a/msys2-installer/msys2-installer/_build/newmsys/msys64 base'
[2025-06-22T10:11:49+0000] [ALPM] transaction started
[2025-06-22T10:11:49+0000] [ALPM] installed bash (5.2.037-2)
[2025-06-22T10:11:51+0000] [ALPM] installed bash-completion (2.16.0-1)
[2025-06-22T10:11:51+0000] [ALPM] installed gcc-libs (13.4.0-2)
[2025-06-22T10:11:51+0000] [ALPM] installed libbz2 (1.0.8-4)
[2025-06-22T10:11:51+0000] [ALPM] installed libintl (0.22.5-1)
[2025-06-22T10:11:51+0000] [ALPM] installed libiconv (1.18-1)
[2025-06-22T10:11:51+0000] [ALPM] installed libexpat (2.7.1-1)
[2025-06-22T10:11:51+0000] [ALPM] installed liblzma (5.8.1-1)
[2025-06-22T10:11:51+0000] [ALPM] installed liblz4 (1.10.0-1)
[2025-06-22T10:11:51+0000] [ALPM] installed libopenssl (3.5.0-1)
[2025-06-22T10:11:51+0000] [ALPM] installed libzstd (1.5.7-1)
[2025-06-22T10:11:51+0000] [ALPM] installed zlib (1.3.1-1)
[2025-06-22T10:11:51+0000] [ALPM] installed bsdtar (3.8.1-1)
[2025-06-22T10:11:51+0000] [ALPM] installed bzip2 (1.0.8-4)
[2025-06-22T10:11:51+0000] [ALPM] installed gmp (6.3.0-1)
[2025-06-22T10:11:52+0000] [ALPM] installed coreutils (8.32-5)
[2025-06-22T10:11:52+0000] [ALPM] installed openssl (3.5.0-1)
[2025-06-22T10:11:52+0000] [ALPM] installed findutils (4.10.0-2)
[2025-06-22T10:11:52+0000] [ALPM] installed sed (4.9-1)
[2025-06-22T10:11:52+0000] [ALPM] installed libffi (3.5.0-1)
[2025-06-22T10:12:01+0000] [ALPM] installed ncurses (6.5.20240831-2)
[2025-06-22T10:12:01+0000] [ALPM] installed libpcre2_8 (10.45-1)
[2025-06-22T10:12:01+0000] [ALPM] installed less (679-1)
[2025-06-22T10:12:01+0000] [ALPM] installed gzip (1.14-1)
[2025-06-22T10:12:01+0000] [ALPM] installed libxcrypt (4.4.38-1)
[2025-06-22T10:12:01+0000] [ALPM] installed info (7.2-1)
[2025-06-22T10:12:01+0000] [ALPM] installed libtasn1 (4.20.0-1)
[2025-06-22T10:12:01+0000] [ALPM] installed libp11-kit (0.25.5-2)
[2025-06-22T10:12:01+0000] [ALPM] installed p11-kit (0.25.5-2)
[2025-06-22T10:12:01+0000] [ALPM] installed ca-certificates (20241223-1)
[2025-06-22T10:12:02+0000] [ALPM] installed brotli (1.1.0-2)
[2025-06-22T10:12:02+0000] [ALPM] installed libdb (6.2.32-5)
[2025-06-22T10:12:02+0000] [ALPM] installed libedit (20240808_3.1-1)
[2025-06-22T10:12:02+0000] [ALPM] installed libsqlite (3.50.1-1)
[2025-06-22T10:12:02+0000] [ALPM] installed heimdal-libs (7.8.0-5)
[2025-06-22T10:12:02+0000] [ALPM] installed libunistring (1.3-1)
[2025-06-22T10:12:02+0000] [ALPM] installed libidn2 (2.3.8-1)
[2025-06-22T10:12:02+0000] [ALPM] installed libnghttp2 (1.66.0-1)
[2025-06-22T10:12:02+0000] [ALPM] installed libpsl (0.21.5-2)
[2025-06-22T10:12:02+0000] [ALPM] installed libssh2 (1.11.1-1)
[2025-06-22T10:12:02+0000] [ALPM] installed libcurl (8.14.1-1)
[2025-06-22T10:12:03+0000] [ALPM] installed curl (8.14.1-1)
[2025-06-22T10:12:03+0000] [ALPM] installed libpcre (8.45-5)
[2025-06-22T10:12:03+0000] [ALPM] installed grep (1~3.0-7)
[2025-06-22T10:12:03+0000] [ALPM] installed dash (0.5.12-1)
[2025-06-22T10:12:03+0000] [ALPM] installed file (5.46-2)
[2025-06-22T10:12:03+0000] [ALPM] installed mpfr (4.2.2-1)
[2025-06-22T10:12:03+0000] [ALPM] installed libreadline (8.2.013-1)
[2025-06-22T10:12:03+0000] [ALPM] installed gawk (5.3.2-1)
[2025-06-22T10:12:03+0000] [ALPM] installed libargp (20241207-1)
[2025-06-22T10:12:03+0000] [ALPM] installed getent (2.18.90-5)
[2025-06-22T10:12:03+0000] [ALPM] installed inetutils (2.6-1)
[2025-06-22T10:12:04+0000] [ALPM] installed mintty (1~3.7.8-1)
[2025-06-22T10:12:04+0000] [ALPM] installed msys2-keyring (1~20250619-1)
[2025-06-22T10:12:04+0000] [ALPM] installed msys2-launcher (1.5-3)
[2025-06-22T10:12:04+0000] [ALPM] installed nano (8.5-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libgettextpo (0.22.5-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libasprintf (0.22.5-1)
[2025-06-22T10:12:04+0000] [ALPM] installed gettext (0.22.5-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libgpg-error (1.55-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libassuan (3.0.2-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libgcrypt (1.11.1-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libhogweed (3.10.1-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libnettle (3.10.1-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libgnutls (3.8.9-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libksba (1.6.7-1)
[2025-06-22T10:12:04+0000] [ALPM] installed libnpth (1.8-1)
[2025-06-22T10:12:04+0000] [ALPM] installed nettle (3.10.1-1)
[2025-06-22T10:12:04+0000] [ALPM] installed pinentry (1.3.1-2)
[2025-06-22T10:12:04+0000] [ALPM] installed gnupg (2.4.8-1)
[2025-06-22T10:12:04+0000] [ALPM] installed pacman-mirrors (20250607-1)
[2025-06-22T10:12:04+0000] [ALPM] installed which (2.23-4)
[2025-06-22T10:12:05+0000] [ALPM] installed xz (5.8.1-1)
[2025-06-22T10:12:05+0000] [ALPM] installed zstd (1.5.7-1)
[2025-06-22T10:12:06+0000] [ALPM] installed pacman (6.1.0-16)
[2025-06-22T10:12:06+0000] [ALPM] installed db (6.2.32-5)
[2025-06-22T10:12:06+0000] [ALPM] installed libgdbm (1.25-1)
[2025-06-22T10:12:06+0000] [ALPM] installed gdbm (1.25-1)
[2025-06-22T10:12:08+0000] [ALPM] installed perl (5.38.4-2)
[2025-06-22T10:12:09+0000] [ALPM] installed pacman-contrib (1.10.6-1)
[2025-06-22T10:12:09+0000] [ALPM] installed rebase (4.5.0-5)
[2025-06-22T10:12:09+0000] [ALPM] installed tar (1.35-2)
[2025-06-22T10:12:09+0000] [ALPM] installed time (1.9-3)
[2025-06-22T10:12:12+0000] [ALPM] installed tzcode (2025b-1)
[2025-06-22T10:12:12+0000] [ALPM] installed libutil-linux (2.40.2-2)
[2025-06-22T10:12:12+0000] [ALPM] installed util-linux (2.40.2-2)
[2025-06-22T10:12:12+0000] [ALPM] installed wget (1.25.0-1)
[2025-06-22T10:12:12+0000] [ALPM] installed base (2022.06-1)
[2025-06-22T10:12:12+0000] [ALPM] transaction completed
[2025-06-22T10:12:12+0000] [ALPM] running 'texinfo-install.hook'...
[2025-07-31T13:14:52+0800] [PACMAN] Running 'pacman -S mingw-w64-x86_64-gcc~'
[2025-07-31T13:15:54+0800] [PACMAN] Running 'pacman -S mingw-w64-x86_64-gcc'
[2025-07-31T13:53:48+0800] [ALPM] transaction started
[2025-07-31T13:53:48+0800] [ALPM] installed mingw-w64-x86_64-libwinpthread (13.0.0.r21.gf5469ff36-1)
[2025-07-31T13:53:48+0800] [ALPM] installed mingw-w64-x86_64-gcc-libs (15.1.0-5)
[2025-07-31T13:53:48+0800] [ALPM] installed mingw-w64-x86_64-libiconv (1.18-1)
[2025-07-31T13:53:48+0800] [ALPM] installed mingw-w64-x86_64-gettext-runtime (0.25-1)
[2025-07-31T13:53:48+0800] [ALPM] installed mingw-w64-x86_64-zlib (1.3.1-1)
[2025-07-31T13:53:48+0800] [ALPM] installed mingw-w64-x86_64-zstd (1.5.7-1)
[2025-07-31T13:53:49+0800] [ALPM] installed mingw-w64-x86_64-binutils (2.44-4)
[2025-07-31T13:53:52+0800] [ALPM] installed mingw-w64-x86_64-headers-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T13:53:53+0800] [ALPM] installed mingw-w64-x86_64-crt-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T13:53:53+0800] [ALPM] installed mingw-w64-x86_64-gmp (6.3.0-2)
[2025-07-31T13:53:53+0800] [ALPM] installed mingw-w64-x86_64-isl (0.27-1)
[2025-07-31T13:53:53+0800] [ALPM] installed mingw-w64-x86_64-mpfr (4.2.2-1)
[2025-07-31T13:53:54+0800] [ALPM] installed mingw-w64-x86_64-mpc (1.3.1-2)
[2025-07-31T13:53:54+0800] [ALPM] installed mingw-w64-x86_64-windows-default-manifest (6.4-4)
[2025-07-31T13:53:54+0800] [ALPM] installed mingw-w64-x86_64-winpthreads (13.0.0.r21.gf5469ff36-1)
[2025-07-31T13:53:57+0800] [ALPM] installed mingw-w64-x86_64-gcc (15.1.0-5)
[2025-07-31T13:53:57+0800] [ALPM] transaction completed
[2025-07-31T14:37:44+0800] [PACMAN] Running 'pacman -S mingw-w64-x86_64-toolchain mingw-w64-x86_64-cmake git'
[2025-07-31T14:38:24+0800] [PACMAN] Running 'pacman -S mingw-w64-x86_64-toolchain mingw-w64-x86_64-cmake git'
[2025-07-31T14:44:40+0800] [PACMAN] Running 'pacman -S mingw-w64-x86_64-toolchain mingw-w64-x86_64-cmake git'
[2025-07-31T14:44:55+0800] [PACMAN] Running 'pacman -S mingw-w64-x86_64-toolchain mingw-w64-x86_64-cmake git'
[2025-07-31T14:45:06+0800] [PACMAN] Running 'pacman -S mingw-w64-x86_64-toolchain mingw-w64-x86_64-cmake git'
[2025-07-31T14:45:49+0800] [ALPM] transaction started
[2025-07-31T14:45:49+0800] [ALPM] reinstalled mingw-w64-x86_64-libwinpthread (13.0.0.r21.gf5469ff36-1)
[2025-07-31T14:45:50+0800] [ALPM] reinstalled mingw-w64-x86_64-binutils (2.44-4)
[2025-07-31T14:45:53+0800] [ALPM] reinstalled mingw-w64-x86_64-headers-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T14:45:55+0800] [ALPM] reinstalled mingw-w64-x86_64-crt-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T14:45:55+0800] [ALPM] reinstalled mingw-w64-x86_64-winpthreads (13.0.0.r21.gf5469ff36-1)
[2025-07-31T14:45:59+0800] [ALPM] reinstalled mingw-w64-x86_64-gcc (15.1.0-5)
[2025-07-31T14:45:59+0800] [ALPM] installed mingw-w64-x86_64-expat (2.7.1-2)
[2025-07-31T14:46:00+0800] [ALPM] installed mingw-w64-x86_64-libtre (0.9.0-1)
[2025-07-31T14:46:00+0800] [ALPM] installed mingw-w64-x86_64-libsystre (1.0.2-1)
[2025-07-31T14:46:09+0800] [ALPM] installed mingw-w64-x86_64-ncurses (6.5.20241228-3)
[2025-07-31T14:46:09+0800] [ALPM] installed mingw-w64-x86_64-bzip2 (1.0.8-3)
[2025-07-31T14:46:09+0800] [ALPM] installed mingw-w64-x86_64-libffi (3.5.0-1)
[2025-07-31T14:46:09+0800] [ALPM] installed mingw-w64-x86_64-mpdecimal (4.0.1-1)
[2025-07-31T14:46:20+0800] [ALPM] installed mingw-w64-x86_64-openssl (3.5.0-1)
[2025-07-31T14:46:20+0800] [ALPM] installed mingw-w64-x86_64-termcap (1.3.1-7)
[2025-07-31T14:46:20+0800] [ALPM] installed mingw-w64-x86_64-readline (8.2.013-1)
[2025-07-31T14:46:21+0800] [ALPM] installed mingw-w64-x86_64-sqlite3 (3.50.1-1)
[2025-07-31T14:46:24+0800] [ALPM] installed mingw-w64-x86_64-tcl (8.6.16-1)
[2025-07-31T14:46:25+0800] [ALPM] installed mingw-w64-x86_64-tk (8.6.16-1)
[2025-07-31T14:46:25+0800] [ALPM] installed mingw-w64-x86_64-xz (5.8.1-2)
[2025-07-31T14:46:28+0800] [ALPM] installed mingw-w64-x86_64-tzdata (2025b-1)
[2025-07-31T14:46:42+0800] [ALPM] installed mingw-w64-x86_64-python (3.12.11-1)
[2025-07-31T14:46:42+0800] [ALPM] installed mingw-w64-x86_64-xxhash (0.8.3-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-gdb (16.3-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-gdb-multiarch (16.3-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-libmangle-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-make (4.4.1-3)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-pkgconf (1~2.5.0-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-tools-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-winstorecompat-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-cppdap (1.65-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-c-ares (1.34.5-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-brotli (1.1.0-5)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-libunistring (1.3-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-libidn2 (2.3.8-2)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-libpsl (0.21.5-3)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-libtasn1 (4.20.0-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-p11-kit (0.25.5-1)
[2025-07-31T14:46:43+0800] [ALPM] installed mingw-w64-x86_64-ca-certificates (20241223-1)
[2025-07-31T14:46:44+0800] [ALPM] installed mingw-w64-x86_64-libssh2 (1.11.1-1)
[2025-07-31T14:46:44+0800] [ALPM] installed mingw-w64-x86_64-nghttp2 (1.66.0-1)
[2025-07-31T14:46:45+0800] [ALPM] installed mingw-w64-x86_64-nettle (3.10.1-1)
[2025-07-31T14:46:47+0800] [ALPM] installed mingw-w64-x86_64-gnutls (3.8.9-4)
[2025-07-31T14:46:47+0800] [ALPM] installed mingw-w64-x86_64-ngtcp2 (1.13.0-2)
[2025-07-31T14:46:47+0800] [ALPM] installed mingw-w64-x86_64-nghttp3 (1.10.1-1)
[2025-07-31T14:46:48+0800] [ALPM] installed mingw-w64-x86_64-curl (8.14.1-1)
[2025-07-31T14:46:48+0800] [ALPM] installed mingw-w64-x86_64-jsoncpp (1.9.6-3)
[2025-07-31T14:46:48+0800] [ALPM] installed mingw-w64-x86_64-libb2 (0.98.1-2)
[2025-07-31T14:46:48+0800] [ALPM] installed mingw-w64-x86_64-lz4 (1.10.0-1)
[2025-07-31T14:46:48+0800] [ALPM] installed mingw-w64-x86_64-libarchive (3.8.1-2)
[2025-07-31T14:46:48+0800] [ALPM] installed mingw-w64-x86_64-libuv (1.51.0-1)
[2025-07-31T14:46:48+0800] [ALPM] installed mingw-w64-x86_64-ninja (1.12.1-2)
[2025-07-31T14:46:48+0800] [ALPM] installed mingw-w64-x86_64-rhash (1.4.5-1)
[2025-07-31T14:46:55+0800] [ALPM] installed mingw-w64-x86_64-cmake (4.0.2-1)
[2025-07-31T14:46:56+0800] [ALPM] installed heimdal (7.8.0-5)
[2025-07-31T14:46:56+0800] [ALPM] installed libcbor (0.12.0-1)
[2025-07-31T14:46:56+0800] [ALPM] installed libfido2 (1.16.0-1)
[2025-07-31T14:46:56+0800] [ALPM] installed openssh (10.0p1-2)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-Error (0.17030-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-Authen-SASL (2.1800-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-Encode-Locale (1.05-2)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-HTTP-Date (6.06-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-File-Listing (6.16-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-HTML-Tagset (3.24-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-Clone (0.47-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-IO-HTML (1.004-2)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-LWP-MediaTypes (6.04-2)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-URI (5.31-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-HTTP-Message (7.00-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-HTML-Parser (3.83-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-HTTP-Cookies (6.11-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-HTTP-Daemon (6.16-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-http-cookiejar (0.014-1)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-HTTP-Negotiate (6.01-3)
[2025-07-31T14:46:56+0800] [ALPM] installed perl-Net-HTTP (6.23-1)
[2025-07-31T14:46:57+0800] [ALPM] installed perl-WWW-RobotRules (6.02-3)
[2025-07-31T14:46:57+0800] [ALPM] installed perl-Try-Tiny (0.32-1)
[2025-07-31T14:46:57+0800] [ALPM] installed perl-libwww (6.78-1)
[2025-07-31T14:46:57+0800] [ALPM] installed perl-TimeDate (2.33-2)
[2025-07-31T14:46:57+0800] [ALPM] installed perl-MailTools (2.22-1)
[2025-07-31T14:46:57+0800] [ALPM] installed perl-IO-Stringy (2.113-2)
[2025-07-31T14:46:57+0800] [ALPM] installed perl-Convert-BinHex (1.125-2)
[2025-07-31T14:46:57+0800] [ALPM-SCRIPTLET] module test... pass.
[2025-07-31T14:46:57+0800] [ALPM] installed perl-MIME-tools (5.515-1)
[2025-07-31T14:46:57+0800] [ALPM] installed perl-Net-SSLeay (1.94-2)
[2025-07-31T14:46:57+0800] [ALPM] installed perl-IO-Socket-SSL (2.089-1)
[2025-07-31T14:46:57+0800] [ALPM] installed perl-Net-SMTP-SSL (1.04-2)
[2025-07-31T14:46:57+0800] [ALPM] installed perl-TermReadKey (2.38-6)
[2025-07-31T14:46:59+0800] [ALPM] installed git (2.50.0-1)
[2025-07-31T14:46:59+0800] [ALPM] transaction completed
[2025-07-31T14:59:47+0800] [PACMAN] Running 'pacman -S mingw-w64-x86_64-toolchain'
[2025-07-31T15:00:22+0800] [ALPM] transaction started
[2025-07-31T15:00:22+0800] [ALPM] reinstalled mingw-w64-x86_64-libwinpthread (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:23+0800] [ALPM] reinstalled mingw-w64-x86_64-binutils (2.44-4)
[2025-07-31T15:00:26+0800] [ALPM] reinstalled mingw-w64-x86_64-headers-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:28+0800] [ALPM] reinstalled mingw-w64-x86_64-crt-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:28+0800] [ALPM] reinstalled mingw-w64-x86_64-winpthreads (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:32+0800] [ALPM] reinstalled mingw-w64-x86_64-gcc (15.1.0-5)
[2025-07-31T15:00:32+0800] [ALPM] reinstalled mingw-w64-x86_64-gdb (16.3-1)
[2025-07-31T15:00:32+0800] [ALPM] reinstalled mingw-w64-x86_64-gdb-multiarch (16.3-1)
[2025-07-31T15:00:32+0800] [ALPM] reinstalled mingw-w64-x86_64-libmangle-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:32+0800] [ALPM] reinstalled mingw-w64-x86_64-make (4.4.1-3)
[2025-07-31T15:00:32+0800] [ALPM] reinstalled mingw-w64-x86_64-pkgconf (1~2.5.0-1)
[2025-07-31T15:00:32+0800] [ALPM] reinstalled mingw-w64-x86_64-tools-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:32+0800] [ALPM] reinstalled mingw-w64-x86_64-winstorecompat-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:32+0800] [ALPM] transaction completed
[2025-07-31T15:00:42+0800] [PACMAN] Running 'pacman -S mingw-w64-x86_64-toolchain'
[2025-07-31T15:00:48+0800] [ALPM] transaction started
[2025-07-31T15:00:48+0800] [ALPM] reinstalled mingw-w64-x86_64-libwinpthread (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:49+0800] [ALPM] reinstalled mingw-w64-x86_64-binutils (2.44-4)
[2025-07-31T15:00:52+0800] [ALPM] reinstalled mingw-w64-x86_64-headers-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:53+0800] [ALPM] reinstalled mingw-w64-x86_64-crt-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:53+0800] [ALPM] reinstalled mingw-w64-x86_64-winpthreads (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:57+0800] [ALPM] reinstalled mingw-w64-x86_64-gcc (15.1.0-5)
[2025-07-31T15:00:57+0800] [ALPM] reinstalled mingw-w64-x86_64-gdb (16.3-1)
[2025-07-31T15:00:57+0800] [ALPM] reinstalled mingw-w64-x86_64-gdb-multiarch (16.3-1)
[2025-07-31T15:00:57+0800] [ALPM] reinstalled mingw-w64-x86_64-libmangle-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:57+0800] [ALPM] reinstalled mingw-w64-x86_64-make (4.4.1-3)
[2025-07-31T15:00:58+0800] [ALPM] reinstalled mingw-w64-x86_64-pkgconf (1~2.5.0-1)
[2025-07-31T15:00:58+0800] [ALPM] reinstalled mingw-w64-x86_64-tools-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:58+0800] [ALPM] reinstalled mingw-w64-x86_64-winstorecompat-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:00:58+0800] [ALPM] transaction completed
[2025-07-31T15:02:30+0800] [PACMAN] Running 'pacman -S mingw-w64-x86_64-toolchain'
[2025-07-31T15:02:45+0800] [ALPM] transaction started
[2025-07-31T15:02:45+0800] [ALPM] reinstalled mingw-w64-x86_64-libwinpthread (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:02:46+0800] [ALPM] reinstalled mingw-w64-x86_64-binutils (2.44-4)
[2025-07-31T15:02:50+0800] [ALPM] reinstalled mingw-w64-x86_64-headers-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:02:51+0800] [ALPM] reinstalled mingw-w64-x86_64-crt-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:02:52+0800] [ALPM] reinstalled mingw-w64-x86_64-winpthreads (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:02:56+0800] [ALPM] reinstalled mingw-w64-x86_64-gcc (15.1.0-5)
[2025-07-31T15:02:56+0800] [ALPM] reinstalled mingw-w64-x86_64-gdb (16.3-1)
[2025-07-31T15:02:56+0800] [ALPM] reinstalled mingw-w64-x86_64-gdb-multiarch (16.3-1)
[2025-07-31T15:02:56+0800] [ALPM] reinstalled mingw-w64-x86_64-libmangle-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:02:56+0800] [ALPM] reinstalled mingw-w64-x86_64-make (4.4.1-3)
[2025-07-31T15:02:56+0800] [ALPM] reinstalled mingw-w64-x86_64-pkgconf (1~2.5.0-1)
[2025-07-31T15:02:56+0800] [ALPM] reinstalled mingw-w64-x86_64-tools-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:02:56+0800] [ALPM] reinstalled mingw-w64-x86_64-winstorecompat-git (13.0.0.r21.gf5469ff36-1)
[2025-07-31T15:02:56+0800] [ALPM] transaction completed
[2025-07-31T15:05:47+0800] [PACMAN] Running 'pacman -Rsc mingw-w64-x86_64-make'
[2025-07-31T15:05:50+0800] [ALPM] transaction started
[2025-07-31T15:05:50+0800] [ALPM] removed mingw-w64-x86_64-make (4.4.1-3)
[2025-07-31T15:05:50+0800] [ALPM] transaction completed
[2025-07-31T15:06:00+0800] [PACMAN] Running 'pacman -Scc'
[2025-07-31T15:06:26+0800] [PACMAN] Running 'pacman -Scc'
[2025-07-31T20:19:24+0800] [PACMAN] Running 'pacman -Syu'
[2025-07-31T20:19:24+0800] [PACMAN] synchronizing package lists
[2025-07-31T20:20:32+0800] [PACMAN] starting core system upgrade
[2025-07-31T20:28:59+0800] [PACMAN] Running 'pacman -S make'
[2025-07-31T20:29:22+0800] [ALPM] transaction started
[2025-07-31T20:29:22+0800] [ALPM] installed make (4.4.1-2)
[2025-07-31T20:29:22+0800] [ALPM] transaction completed
[2025-07-31T20:29:22+0800] [ALPM] running 'texinfo-install.hook'...
[2025-07-31T20:29:22+0800] [ALPM-SCRIPTLET] bash.exe: warning: could not find /tmp, please create!
[2025-07-31T20:30:38+0800] [PACMAN] Running 'pacman -S git'
[2025-07-31T20:30:45+0800] [ALPM] transaction started
[2025-07-31T20:30:46+0800] [ALPM] upgraded git (2.50.0-1 -> 2.50.1-1)
[2025-07-31T20:30:46+0800] [ALPM] transaction completed
[2025-07-31T21:20:51+0800] [PACMAN] Running 'pacman -S base-devel'
[2025-07-31T21:22:23+0800] [ALPM] transaction started
[2025-07-31T21:22:23+0800] [ALPM] installed binutils (2.45-1)
[2025-07-31T21:22:23+0800] [ALPM] installed m4 (1.4.19-2)
[2025-07-31T21:22:24+0800] [ALPM] installed bison (3.8.2-5)
[2025-07-31T21:22:24+0800] [ALPM] installed diffstat (1.68-1)
[2025-07-31T21:22:24+0800] [ALPM] installed diffutils (3.12-1)
[2025-07-31T21:22:24+0800] [ALPM] installed dos2unix (7.5.2-1)
[2025-07-31T21:22:24+0800] [ALPM] installed flex (2.6.4-4)
[2025-07-31T21:22:25+0800] [ALPM] installed texinfo (7.2-1)
[2025-07-31T21:22:25+0800] [ALPM] installed texinfo-tex (7.2-1)
[2025-07-31T21:22:25+0800] [ALPM] installed patch (2.7.6-3)
[2025-07-31T21:22:25+0800] [ALPM] installed base-devel (2024.11-1)
[2025-07-31T21:22:25+0800] [ALPM] transaction completed
[2025-07-31T21:22:25+0800] [ALPM] running 'texinfo-install.hook'...
[2025-07-31T21:22:25+0800] [ALPM-SCRIPTLET] bash.exe: warning: could not find /tmp, please create!
[2025-07-31T21:22:45+0800] [PACMAN] Running 'pacman -S base-devel'
[2025-07-31T21:22:48+0800] [ALPM] transaction started
[2025-07-31T21:22:48+0800] [ALPM] reinstalled base-devel (2024.11-1)
[2025-07-31T21:22:48+0800] [ALPM] transaction completed
[2025-07-31T21:23:00+0800] [PACMAN] Running 'pacman -S mingw-w64-x86_64-toolchain'
[2025-07-31T21:35:22+0800] [ALPM] transaction started
[2025-07-31T21:35:22+0800] [ALPM] upgraded mingw-w64-x86_64-libwinpthread (13.0.0.r21.gf5469ff36-1 -> 13.0.0.r79.g584661e80-1)
[2025-07-31T21:35:22+0800] [ALPM] upgraded mingw-w64-x86_64-gcc-libs (15.1.0-5 -> 15.1.0-8)
[2025-07-31T21:35:22+0800] [ALPM] upgraded mingw-w64-x86_64-binutils (2.44-4 -> 2.45-1)
[2025-07-31T21:35:26+0800] [ALPM] upgraded mingw-w64-x86_64-headers-git (13.0.0.r21.gf5469ff36-1 -> 13.0.0.r79.g584661e80-1)
[2025-07-31T21:35:28+0800] [ALPM] upgraded mingw-w64-x86_64-crt-git (13.0.0.r21.gf5469ff36-1 -> 13.0.0.r79.g584661e80-2)
[2025-07-31T21:35:28+0800] [ALPM] upgraded mingw-w64-x86_64-winpthreads (13.0.0.r21.gf5469ff36-1 -> 13.0.0.r79.g584661e80-1)
[2025-07-31T21:35:31+0800] [ALPM] upgraded mingw-w64-x86_64-gcc (15.1.0-5 -> 15.1.0-8)
[2025-07-31T21:35:32+0800] [ALPM] reinstalled mingw-w64-x86_64-gdb (16.3-1)
[2025-07-31T21:35:32+0800] [ALPM] reinstalled mingw-w64-x86_64-gdb-multiarch (16.3-1)
[2025-07-31T21:35:32+0800] [ALPM] upgraded mingw-w64-x86_64-libmangle-git (13.0.0.r21.gf5469ff36-1 -> 13.0.0.r79.g584661e80-1)
[2025-07-31T21:35:32+0800] [ALPM] installed mingw-w64-x86_64-make (4.4.1-3)
[2025-07-31T21:35:32+0800] [ALPM] upgraded mingw-w64-x86_64-pkgconf (1~2.5.0-1 -> 1~2.5.1-1)
[2025-07-31T21:35:32+0800] [ALPM] upgraded mingw-w64-x86_64-tools-git (13.0.0.r21.gf5469ff36-1 -> 13.0.0.r79.g584661e80-1)
[2025-07-31T21:35:32+0800] [ALPM] upgraded mingw-w64-x86_64-winstorecompat-git (13.0.0.r21.gf5469ff36-1 -> 13.0.0.r79.g584661e80-1)
[2025-07-31T21:35:32+0800] [ALPM] transaction completed
