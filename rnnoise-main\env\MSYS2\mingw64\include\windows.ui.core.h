/*** Autogenerated by WIDL 10.12 from include/windows.ui.core.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_ui_core_h__
#define __windows_ui_core_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler ABI::Windows::UI::Core::IDispatchedHandler
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IDispatchedHandler;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler ABI::Windows::UI::Core::IIdleDispatchedHandler
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IIdleDispatchedHandler;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs ABI::Windows::UI::Core::IAutomationProviderRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IAutomationProviderRequestedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs ABI::Windows::UI::Core::ICharacterReceivedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICharacterReceivedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs ABI::Windows::UI::Core::IClosestInteractiveBoundsRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IClosestInteractiveBoundsRequestedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreCursor_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreCursor_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreCursor __x_ABI_CWindows_CUI_CCore_CICoreCursor;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreCursor ABI::Windows::UI::Core::ICoreCursor
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreCursor;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory ABI::Windows::UI::Core::ICoreCursorFactory
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreCursorFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreDispatcher_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreDispatcher_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreDispatcher __x_ABI_CWindows_CUI_CCore_CICoreDispatcher;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher ABI::Windows::UI::Core::ICoreDispatcher
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreDispatcher;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindow __x_ABI_CWindows_CUI_CCore_CICoreWindow;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow ABI::Windows::UI::Core::ICoreWindow
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindow;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindow2 __x_ABI_CWindows_CUI_CCore_CICoreWindow2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow2 ABI::Windows::UI::Core::ICoreWindow2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindow2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow3_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindow3 __x_ABI_CWindows_CUI_CCore_CICoreWindow3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow3 ABI::Windows::UI::Core::ICoreWindow3
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindow3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow4_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindow4 __x_ABI_CWindows_CUI_CCore_CICoreWindow4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4 ABI::Windows::UI::Core::ICoreWindow4
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindow4;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow5_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow5_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindow5 __x_ABI_CWindows_CUI_CCore_CICoreWindow5;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow5 ABI::Windows::UI::Core::ICoreWindow5
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindow5;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs ABI::Windows::UI::Core::ICoreWindowEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindowEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic ABI::Windows::UI::Core::ICoreWindowStatic
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindowStatic;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs ABI::Windows::UI::Core::IIdleDispatchedHandlerArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IIdleDispatchedHandlerArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs ABI::Windows::UI::Core::IInputEnabledEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IInputEnabledEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs ABI::Windows::UI::Core::IKeyEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IKeyEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 ABI::Windows::UI::Core::IKeyEventArgs2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IKeyEventArgs2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs ABI::Windows::UI::Core::IPointerEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IPointerEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs ABI::Windows::UI::Core::ITouchHitTestingEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ITouchHitTestingEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs ABI::Windows::UI::Core::IVisibilityChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IVisibilityChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs ABI::Windows::UI::Core::IWindowActivatedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IWindowActivatedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs ABI::Windows::UI::Core::IWindowSizeChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IWindowSizeChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CAutomationProviderRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CAutomationProviderRequestedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class AutomationProviderRequestedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CAutomationProviderRequestedEventArgs __x_ABI_CWindows_CUI_CCore_CAutomationProviderRequestedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CAutomationProviderRequestedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CCharacterReceivedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCharacterReceivedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class CharacterReceivedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CCharacterReceivedEventArgs __x_ABI_CWindows_CUI_CCore_CCharacterReceivedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CCharacterReceivedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CClosestInteractiveBoundsRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CClosestInteractiveBoundsRequestedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class ClosestInteractiveBoundsRequestedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CClosestInteractiveBoundsRequestedEventArgs __x_ABI_CWindows_CUI_CCore_CClosestInteractiveBoundsRequestedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CClosestInteractiveBoundsRequestedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CCoreCursor_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCoreCursor_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class CoreCursor;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CCoreCursor __x_ABI_CWindows_CUI_CCore_CCoreCursor;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CCoreCursor_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CCoreDispatcher_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCoreDispatcher_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class CoreDispatcher;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CCoreDispatcher __x_ABI_CWindows_CUI_CCore_CCoreDispatcher;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CCoreDispatcher_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CCoreWindow_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCoreWindow_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class CoreWindow;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CCoreWindow __x_ABI_CWindows_CUI_CCore_CCoreWindow;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CCoreWindow_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CCoreWindowEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCoreWindowEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class CoreWindowEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CCoreWindowEventArgs __x_ABI_CWindows_CUI_CCore_CCoreWindowEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CCoreWindowEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CIdleDispatchedHandlerArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIdleDispatchedHandlerArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class IdleDispatchedHandlerArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIdleDispatchedHandlerArgs __x_ABI_CWindows_CUI_CCore_CIdleDispatchedHandlerArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CIdleDispatchedHandlerArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CInputEnabledEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CInputEnabledEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class InputEnabledEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CInputEnabledEventArgs __x_ABI_CWindows_CUI_CCore_CInputEnabledEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CInputEnabledEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CKeyEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CKeyEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class KeyEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CKeyEventArgs __x_ABI_CWindows_CUI_CCore_CKeyEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CKeyEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CPointerEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CPointerEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class PointerEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CPointerEventArgs __x_ABI_CWindows_CUI_CCore_CPointerEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CPointerEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CTouchHitTestingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CTouchHitTestingEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class TouchHitTestingEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CTouchHitTestingEventArgs __x_ABI_CWindows_CUI_CCore_CTouchHitTestingEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CTouchHitTestingEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CVisibilityChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CVisibilityChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class VisibilityChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CVisibilityChangedEventArgs __x_ABI_CWindows_CUI_CCore_CVisibilityChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CVisibilityChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CWindowActivatedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CWindowActivatedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class WindowActivatedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CWindowActivatedEventArgs __x_ABI_CWindows_CUI_CCore_CWindowActivatedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CWindowActivatedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CCore_CWindowSizeChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CWindowSizeChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                class WindowSizeChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CWindowSizeChangedEventArgs __x_ABI_CWindows_CUI_CCore_CWindowSizeChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CCore_CWindowSizeChangedEventArgs_FWD_DEFINED__ */

#ifndef ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CUI__CInput__CPointerPoint __FIVectorView_1_Windows__CUI__CInput__CPointerPoint;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::UI::Input::PointerPoint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
#define ____FIVector_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CUI__CInput__CPointerPoint __FIVector_1_Windows__CUI__CInput__CPointerPoint;
#ifdef __cplusplus
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint ABI::Windows::Foundation::Collections::IVector<ABI::Windows::UI::Input::PointerPoint* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::AutomationProviderRequestedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CharacterReceivedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::ClosestInteractiveBoundsRequestedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CoreWindowEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::InputEnabledEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::KeyEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::TouchHitTestingEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::VisibilityChangedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowActivatedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowSizeChangedEventArgs* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.system.h>
#include <windows.ui.h>
#include <windows.ui.input.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CUI__CInput__CPointerPoint __FIVectorView_1_Windows__CUI__CInput__CPointerPoint;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::UI::Input::PointerPoint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
#define ____FIVector_1_Windows__CUI__CInput__CPointerPoint_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CUI__CInput__CPointerPoint __FIVector_1_Windows__CUI__CInput__CPointerPoint;
#ifdef __cplusplus
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint ABI::Windows::Foundation::Collections::IVector<ABI::Windows::UI::Input::PointerPoint* >
#endif /* __cplusplus */
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CCoreCursorType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCoreCursorType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                enum CoreCursorType {
                    CoreCursorType_Arrow = 0,
                    CoreCursorType_Cross = 1,
                    CoreCursorType_Custom = 2,
                    CoreCursorType_Hand = 3,
                    CoreCursorType_Help = 4,
                    CoreCursorType_IBeam = 5,
                    CoreCursorType_SizeAll = 6,
                    CoreCursorType_SizeNortheastSouthwest = 7,
                    CoreCursorType_SizeNorthSouth = 8,
                    CoreCursorType_SizeNorthwestSoutheast = 9,
                    CoreCursorType_SizeWestEast = 10,
                    CoreCursorType_UniversalNo = 11,
                    CoreCursorType_UpArrow = 12,
                    CoreCursorType_Wait = 13,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
                    CoreCursorType_Pin = 14,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
                    CoreCursorType_Person = 15
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CCore_CCoreCursorType {
    CoreCursorType_Arrow = 0,
    CoreCursorType_Cross = 1,
    CoreCursorType_Custom = 2,
    CoreCursorType_Hand = 3,
    CoreCursorType_Help = 4,
    CoreCursorType_IBeam = 5,
    CoreCursorType_SizeAll = 6,
    CoreCursorType_SizeNortheastSouthwest = 7,
    CoreCursorType_SizeNorthSouth = 8,
    CoreCursorType_SizeNorthwestSoutheast = 9,
    CoreCursorType_SizeWestEast = 10,
    CoreCursorType_UniversalNo = 11,
    CoreCursorType_UpArrow = 12,
    CoreCursorType_Wait = 13,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
    CoreCursorType_Pin = 14,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
    CoreCursorType_Person = 15
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
};
#ifdef WIDL_using_Windows_UI_Core
#define CoreCursorType __x_ABI_CWindows_CUI_CCore_CCoreCursorType
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif /* ____x_ABI_CWindows_CUI_CCore_CCoreCursorType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CCore_CCoreCursorType __x_ABI_CWindows_CUI_CCore_CCoreCursorType;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CCoreDispatcherPriority_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCoreDispatcherPriority_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                enum CoreDispatcherPriority {
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    CoreDispatcherPriority_Idle = -2,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
                    CoreDispatcherPriority_Low = -1,
                    CoreDispatcherPriority_Normal = 0,
                    CoreDispatcherPriority_High = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CCore_CCoreDispatcherPriority {
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    CoreDispatcherPriority_Idle = -2,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
    CoreDispatcherPriority_Low = -1,
    CoreDispatcherPriority_Normal = 0,
    CoreDispatcherPriority_High = 1
};
#ifdef WIDL_using_Windows_UI_Core
#define CoreDispatcherPriority __x_ABI_CWindows_CUI_CCore_CCoreDispatcherPriority
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif /* ____x_ABI_CWindows_CUI_CCore_CCoreDispatcherPriority_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CCore_CCoreDispatcherPriority __x_ABI_CWindows_CUI_CCore_CCoreDispatcherPriority;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CCoreProcessEventsOption_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCoreProcessEventsOption_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                enum CoreProcessEventsOption {
                    CoreProcessEventsOption_ProcessOneAndAllPending = 0,
                    CoreProcessEventsOption_ProcessOneIfPresent = 1,
                    CoreProcessEventsOption_ProcessUntilQuit = 2,
                    CoreProcessEventsOption_ProcessAllIfPresent = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CCore_CCoreProcessEventsOption {
    CoreProcessEventsOption_ProcessOneAndAllPending = 0,
    CoreProcessEventsOption_ProcessOneIfPresent = 1,
    CoreProcessEventsOption_ProcessUntilQuit = 2,
    CoreProcessEventsOption_ProcessAllIfPresent = 3
};
#ifdef WIDL_using_Windows_UI_Core
#define CoreProcessEventsOption __x_ABI_CWindows_CUI_CCore_CCoreProcessEventsOption
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif /* ____x_ABI_CWindows_CUI_CCore_CCoreProcessEventsOption_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CCore_CCoreProcessEventsOption __x_ABI_CWindows_CUI_CCore_CCoreProcessEventsOption;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CCoreVirtualKeyStates_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCoreVirtualKeyStates_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                enum CoreVirtualKeyStates {
                    CoreVirtualKeyStates_None = 0x0,
                    CoreVirtualKeyStates_Down = 0x1,
                    CoreVirtualKeyStates_Locked = 0x2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CCore_CCoreVirtualKeyStates {
    CoreVirtualKeyStates_None = 0x0,
    CoreVirtualKeyStates_Down = 0x1,
    CoreVirtualKeyStates_Locked = 0x2
};
#ifdef WIDL_using_Windows_UI_Core
#define CoreVirtualKeyStates __x_ABI_CWindows_CUI_CCore_CCoreVirtualKeyStates
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif /* ____x_ABI_CWindows_CUI_CCore_CCoreVirtualKeyStates_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CCore_CCoreVirtualKeyStates __x_ABI_CWindows_CUI_CCore_CCoreVirtualKeyStates;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CUI_CCore_CCoreWindowActivationMode_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCoreWindowActivationMode_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                enum CoreWindowActivationMode {
                    CoreWindowActivationMode_None = 0,
                    CoreWindowActivationMode_Deactivated = 1,
                    CoreWindowActivationMode_ActivatedNotForeground = 2,
                    CoreWindowActivationMode_ActivatedInForeground = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CCore_CCoreWindowActivationMode {
    CoreWindowActivationMode_None = 0,
    CoreWindowActivationMode_Deactivated = 1,
    CoreWindowActivationMode_ActivatedNotForeground = 2,
    CoreWindowActivationMode_ActivatedInForeground = 3
};
#ifdef WIDL_using_Windows_UI_Core
#define CoreWindowActivationMode __x_ABI_CWindows_CUI_CCore_CCoreWindowActivationMode
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif /* ____x_ABI_CWindows_CUI_CCore_CCoreWindowActivationMode_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CCore_CCoreWindowActivationMode __x_ABI_CWindows_CUI_CCore_CCoreWindowActivationMode;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CCoreWindowActivationState_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCoreWindowActivationState_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                enum CoreWindowActivationState {
                    CoreWindowActivationState_CodeActivated = 0,
                    CoreWindowActivationState_Deactivated = 1,
                    CoreWindowActivationState_PointerActivated = 2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CCore_CCoreWindowActivationState {
    CoreWindowActivationState_CodeActivated = 0,
    CoreWindowActivationState_Deactivated = 1,
    CoreWindowActivationState_PointerActivated = 2
};
#ifdef WIDL_using_Windows_UI_Core
#define CoreWindowActivationState __x_ABI_CWindows_CUI_CCore_CCoreWindowActivationState
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif /* ____x_ABI_CWindows_CUI_CCore_CCoreWindowActivationState_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CCore_CCoreWindowActivationState __x_ABI_CWindows_CUI_CCore_CCoreWindowActivationState;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CCoreWindowFlowDirection_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CCoreWindowFlowDirection_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                enum CoreWindowFlowDirection {
                    CoreWindowFlowDirection_LeftToRight = 0,
                    CoreWindowFlowDirection_RightToLeft = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CCore_CCoreWindowFlowDirection {
    CoreWindowFlowDirection_LeftToRight = 0,
    CoreWindowFlowDirection_RightToLeft = 1
};
#ifdef WIDL_using_Windows_UI_Core
#define CoreWindowFlowDirection __x_ABI_CWindows_CUI_CCore_CCoreWindowFlowDirection
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif /* ____x_ABI_CWindows_CUI_CCore_CCoreWindowFlowDirection_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CCore_CCoreWindowFlowDirection __x_ABI_CWindows_CUI_CCore_CCoreWindowFlowDirection;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CUI_CCore_CCorePhysicalKeyStatus __x_ABI_CWindows_CUI_CCore_CCorePhysicalKeyStatus;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                typedef struct CorePhysicalKeyStatus CorePhysicalKeyStatus;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation __x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                typedef struct CoreProximityEvaluation CoreProximityEvaluation;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs ABI::Windows::UI::Core::IAutomationProviderRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IAutomationProviderRequestedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs ABI::Windows::UI::Core::ICharacterReceivedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICharacterReceivedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs ABI::Windows::UI::Core::IClosestInteractiveBoundsRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IClosestInteractiveBoundsRequestedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreAcceleratorKeys_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreAcceleratorKeys_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreAcceleratorKeys __x_ABI_CWindows_CUI_CCore_CICoreAcceleratorKeys;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreAcceleratorKeys ABI::Windows::UI::Core::ICoreAcceleratorKeys
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreAcceleratorKeys;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreCursor_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreCursor_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreCursor __x_ABI_CWindows_CUI_CCore_CICoreCursor;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreCursor ABI::Windows::UI::Core::ICoreCursor
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreCursor;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory ABI::Windows::UI::Core::ICoreCursorFactory
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreCursorFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreDispatcher_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreDispatcher_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreDispatcher __x_ABI_CWindows_CUI_CCore_CICoreDispatcher;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher ABI::Windows::UI::Core::ICoreDispatcher
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreDispatcher;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreDispatcher2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreDispatcher2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreDispatcher2 __x_ABI_CWindows_CUI_CCore_CICoreDispatcher2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher2 ABI::Windows::UI::Core::ICoreDispatcher2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreDispatcher2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreDispatcherWithTaskPriority_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreDispatcherWithTaskPriority_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreDispatcherWithTaskPriority __x_ABI_CWindows_CUI_CCore_CICoreDispatcherWithTaskPriority;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcherWithTaskPriority ABI::Windows::UI::Core::ICoreDispatcherWithTaskPriority
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreDispatcherWithTaskPriority;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindow __x_ABI_CWindows_CUI_CCore_CICoreWindow;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow ABI::Windows::UI::Core::ICoreWindow
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindow;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindow2 __x_ABI_CWindows_CUI_CCore_CICoreWindow2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow2 ABI::Windows::UI::Core::ICoreWindow2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindow2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow3_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindow3 __x_ABI_CWindows_CUI_CCore_CICoreWindow3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow3 ABI::Windows::UI::Core::ICoreWindow3
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindow3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow4_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindow4 __x_ABI_CWindows_CUI_CCore_CICoreWindow4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4 ABI::Windows::UI::Core::ICoreWindow4
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindow4;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow5_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow5_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindow5 __x_ABI_CWindows_CUI_CCore_CICoreWindow5;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow5 ABI::Windows::UI::Core::ICoreWindow5
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindow5;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs ABI::Windows::UI::Core::ICoreWindowEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindowEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic ABI::Windows::UI::Core::ICoreWindowStatic
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ICoreWindowStatic;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs ABI::Windows::UI::Core::IIdleDispatchedHandlerArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IIdleDispatchedHandlerArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs ABI::Windows::UI::Core::IInputEnabledEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IInputEnabledEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs ABI::Windows::UI::Core::IKeyEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IKeyEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 ABI::Windows::UI::Core::IKeyEventArgs2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IKeyEventArgs2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs ABI::Windows::UI::Core::IPointerEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IPointerEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs ABI::Windows::UI::Core::ITouchHitTestingEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface ITouchHitTestingEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs ABI::Windows::UI::Core::IVisibilityChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IVisibilityChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs ABI::Windows::UI::Core::IWindowActivatedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IWindowActivatedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs ABI::Windows::UI::Core::IWindowSizeChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                interface IWindowSizeChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                struct CorePhysicalKeyStatus {
                    UINT32 RepeatCount;
                    UINT32 ScanCode;
                    boolean IsExtendedKey;
                    boolean IsMenuKeyDown;
                    boolean WasKeyDown;
                    boolean IsKeyReleased;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CUI_CCore_CCorePhysicalKeyStatus {
    UINT32 RepeatCount;
    UINT32 ScanCode;
    boolean IsExtendedKey;
    boolean IsMenuKeyDown;
    boolean WasKeyDown;
    boolean IsKeyReleased;
};
#ifdef WIDL_using_Windows_UI_Core
#define CorePhysicalKeyStatus __x_ABI_CWindows_CUI_CCore_CCorePhysicalKeyStatus
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                struct CoreProximityEvaluation {
                    INT32 Score;
                    ABI::Windows::Foundation::Point AdjustedPoint;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation {
    INT32 Score;
    __x_ABI_CWindows_CFoundation_CPoint AdjustedPoint;
};
#ifdef WIDL_using_Windows_UI_Core
#define CoreProximityEvaluation __x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * IDispatchedHandler interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIDispatchedHandler, 0xd1f276c4, 0x98d8, 0x4636, 0xbf,0x49, 0xeb,0x79,0x50,0x75,0x48,0xe9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("d1f276c4-98d8-4636-bf49-eb79507548e9")
                IDispatchedHandler : public IUnknown
                {
                    virtual HRESULT STDMETHODCALLTYPE Invoke(
                        ) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIDispatchedHandler, 0xd1f276c4, 0x98d8, 0x4636, 0xbf,0x49, 0xeb,0x79,0x50,0x75,0x48,0xe9)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIDispatchedHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler *This);

    /*** IDispatchedHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler *This);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIDispatchedHandlerVtbl;

interface __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIDispatchedHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatchedHandler methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_Invoke(This) (This)->lpVtbl->Invoke(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIDispatchedHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_AddRef(__x_ABI_CWindows_CUI_CCore_CIDispatchedHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_Release(__x_ABI_CWindows_CUI_CCore_CIDispatchedHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatchedHandler methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_Invoke(__x_ABI_CWindows_CUI_CCore_CIDispatchedHandler* This) {
    return This->lpVtbl->Invoke(This);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IDispatchedHandler IID___x_ABI_CWindows_CUI_CCore_CIDispatchedHandler
#define IDispatchedHandlerVtbl __x_ABI_CWindows_CUI_CCore_CIDispatchedHandlerVtbl
#define IDispatchedHandler __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler
#define IDispatchedHandler_QueryInterface __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_QueryInterface
#define IDispatchedHandler_AddRef __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_AddRef
#define IDispatchedHandler_Release __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_Release
#define IDispatchedHandler_Invoke __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_Invoke
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIDispatchedHandler_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIdleDispatchedHandler interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler, 0xa42b0c24, 0x7f21, 0x4abc, 0x99,0xc1, 0x8f,0x01,0x00,0x7f,0x08,0x80);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("a42b0c24-7f21-4abc-99c1-8f01007f0880")
                IIdleDispatchedHandler : public IUnknown
                {
                    virtual HRESULT STDMETHODCALLTYPE Invoke(
                        ABI::Windows::UI::Core::IIdleDispatchedHandlerArgs *e) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler, 0xa42b0c24, 0x7f21, 0x4abc, 0x99,0xc1, 0x8f,0x01,0x00,0x7f,0x08,0x80)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler *This);

    /*** IIdleDispatchedHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler *This,
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs *e);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerVtbl;

interface __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_Release(This) (This)->lpVtbl->Release(This)
/*** IIdleDispatchedHandler methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_Invoke(This,e) (This)->lpVtbl->Invoke(This,e)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_AddRef(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_Release(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** IIdleDispatchedHandler methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_Invoke(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler* This,__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs *e) {
    return This->lpVtbl->Invoke(This,e);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IIdleDispatchedHandler IID___x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler
#define IIdleDispatchedHandlerVtbl __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerVtbl
#define IIdleDispatchedHandler __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler
#define IIdleDispatchedHandler_QueryInterface __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_QueryInterface
#define IIdleDispatchedHandler_AddRef __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_AddRef
#define IIdleDispatchedHandler_Release __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_Release
#define IIdleDispatchedHandler_Invoke __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_Invoke
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAutomationProviderRequestedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs, 0x961ff258, 0x21bf, 0x4b42, 0xa2,0x98, 0xfa,0x47,0x9d,0x4c,0x52,0xe2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("961ff258-21bf-4b42-a298-fa479d4c52e2")
                IAutomationProviderRequestedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AutomationProvider(
                        IInspectable **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_AutomationProvider(
                        IInspectable *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs, 0x961ff258, 0x21bf, 0x4b42, 0xa2,0x98, 0xfa,0x47,0x9d,0x4c,0x52,0xe2)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IAutomationProviderRequestedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AutomationProvider)(
        __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *put_AutomationProvider)(
        __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs *This,
        IInspectable *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAutomationProviderRequestedEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_get_AutomationProvider(This,value) (This)->lpVtbl->get_AutomationProvider(This,value)
#define __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_put_AutomationProvider(This,value) (This)->lpVtbl->put_AutomationProvider(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_Release(__x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAutomationProviderRequestedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_get_AutomationProvider(__x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs* This,IInspectable **value) {
    return This->lpVtbl->get_AutomationProvider(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_put_AutomationProvider(__x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs* This,IInspectable *value) {
    return This->lpVtbl->put_AutomationProvider(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IAutomationProviderRequestedEventArgs IID___x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs
#define IAutomationProviderRequestedEventArgsVtbl __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgsVtbl
#define IAutomationProviderRequestedEventArgs __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs
#define IAutomationProviderRequestedEventArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_QueryInterface
#define IAutomationProviderRequestedEventArgs_AddRef __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_AddRef
#define IAutomationProviderRequestedEventArgs_Release __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_Release
#define IAutomationProviderRequestedEventArgs_GetIids __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_GetIids
#define IAutomationProviderRequestedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_GetRuntimeClassName
#define IAutomationProviderRequestedEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_GetTrustLevel
#define IAutomationProviderRequestedEventArgs_get_AutomationProvider __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_get_AutomationProvider
#define IAutomationProviderRequestedEventArgs_put_AutomationProvider __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_put_AutomationProvider
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICharacterReceivedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs, 0xc584659f, 0x99b2, 0x4bcc, 0xbd,0x33, 0x04,0xe6,0x3f,0x42,0x90,0x2e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("c584659f-99b2-4bcc-bd33-04e63f42902e")
                ICharacterReceivedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_KeyCode(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_KeyStatus(
                        ABI::Windows::UI::Core::CorePhysicalKeyStatus *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs, 0xc584659f, 0x99b2, 0x4bcc, 0xbd,0x33, 0x04,0xe6,0x3f,0x42,0x90,0x2e)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs *This,
        TrustLevel *trustLevel);

    /*** ICharacterReceivedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_KeyCode)(
        __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_KeyStatus)(
        __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CCorePhysicalKeyStatus *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICharacterReceivedEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_get_KeyCode(This,value) (This)->lpVtbl->get_KeyCode(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_get_KeyStatus(This,value) (This)->lpVtbl->get_KeyStatus(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_Release(__x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICharacterReceivedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_get_KeyCode(__x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs* This,UINT32 *value) {
    return This->lpVtbl->get_KeyCode(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_get_KeyStatus(__x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs* This,__x_ABI_CWindows_CUI_CCore_CCorePhysicalKeyStatus *value) {
    return This->lpVtbl->get_KeyStatus(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ICharacterReceivedEventArgs IID___x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs
#define ICharacterReceivedEventArgsVtbl __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgsVtbl
#define ICharacterReceivedEventArgs __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs
#define ICharacterReceivedEventArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_QueryInterface
#define ICharacterReceivedEventArgs_AddRef __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_AddRef
#define ICharacterReceivedEventArgs_Release __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_Release
#define ICharacterReceivedEventArgs_GetIids __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_GetIids
#define ICharacterReceivedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_GetRuntimeClassName
#define ICharacterReceivedEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_GetTrustLevel
#define ICharacterReceivedEventArgs_get_KeyCode __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_get_KeyCode
#define ICharacterReceivedEventArgs_get_KeyStatus __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_get_KeyStatus
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IClosestInteractiveBoundsRequestedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs, 0x347c11d7, 0xf6f8, 0x40e3, 0xb2,0x9f, 0xae,0x50,0xd3,0xe8,0x64,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("347c11d7-f6f8-40e3-b29f-ae50d3e86486")
                IClosestInteractiveBoundsRequestedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_PointerPosition(
                        ABI::Windows::Foundation::Point *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SearchBounds(
                        ABI::Windows::Foundation::Rect *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ClosestInteractiveBounds(
                        ABI::Windows::Foundation::Rect *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_ClosestInteractiveBounds(
                        ABI::Windows::Foundation::Rect value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs, 0x347c11d7, 0xf6f8, 0x40e3, 0xb2,0x9f, 0xae,0x50,0xd3,0xe8,0x64,0x86)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IClosestInteractiveBoundsRequestedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_PointerPosition)(
        __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *This,
        __x_ABI_CWindows_CFoundation_CPoint *value);

    HRESULT (STDMETHODCALLTYPE *get_SearchBounds)(
        __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    HRESULT (STDMETHODCALLTYPE *get_ClosestInteractiveBounds)(
        __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    HRESULT (STDMETHODCALLTYPE *put_ClosestInteractiveBounds)(
        __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *This,
        __x_ABI_CWindows_CFoundation_CRect value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IClosestInteractiveBoundsRequestedEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_get_PointerPosition(This,value) (This)->lpVtbl->get_PointerPosition(This,value)
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_get_SearchBounds(This,value) (This)->lpVtbl->get_SearchBounds(This,value)
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_get_ClosestInteractiveBounds(This,value) (This)->lpVtbl->get_ClosestInteractiveBounds(This,value)
#define __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_put_ClosestInteractiveBounds(This,value) (This)->lpVtbl->put_ClosestInteractiveBounds(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_Release(__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IClosestInteractiveBoundsRequestedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_get_PointerPosition(__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs* This,__x_ABI_CWindows_CFoundation_CPoint *value) {
    return This->lpVtbl->get_PointerPosition(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_get_SearchBounds(__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_SearchBounds(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_get_ClosestInteractiveBounds(__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_ClosestInteractiveBounds(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_put_ClosestInteractiveBounds(__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs* This,__x_ABI_CWindows_CFoundation_CRect value) {
    return This->lpVtbl->put_ClosestInteractiveBounds(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IClosestInteractiveBoundsRequestedEventArgs IID___x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs
#define IClosestInteractiveBoundsRequestedEventArgsVtbl __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgsVtbl
#define IClosestInteractiveBoundsRequestedEventArgs __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs
#define IClosestInteractiveBoundsRequestedEventArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_QueryInterface
#define IClosestInteractiveBoundsRequestedEventArgs_AddRef __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_AddRef
#define IClosestInteractiveBoundsRequestedEventArgs_Release __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_Release
#define IClosestInteractiveBoundsRequestedEventArgs_GetIids __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_GetIids
#define IClosestInteractiveBoundsRequestedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_GetRuntimeClassName
#define IClosestInteractiveBoundsRequestedEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_GetTrustLevel
#define IClosestInteractiveBoundsRequestedEventArgs_get_PointerPosition __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_get_PointerPosition
#define IClosestInteractiveBoundsRequestedEventArgs_get_SearchBounds __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_get_SearchBounds
#define IClosestInteractiveBoundsRequestedEventArgs_get_ClosestInteractiveBounds __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_get_ClosestInteractiveBounds
#define IClosestInteractiveBoundsRequestedEventArgs_put_ClosestInteractiveBounds __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_put_ClosestInteractiveBounds
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * ICoreCursor interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreCursor_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreCursor_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CICoreCursor, 0x96893acf, 0x111d, 0x442c, 0x8a,0x77, 0xb8,0x79,0x92,0xf8,0xe2,0xd6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("96893acf-111d-442c-8a77-b87992f8e2d6")
                ICoreCursor : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Id(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Type(
                        ABI::Windows::UI::Core::CoreCursorType *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CICoreCursor, 0x96893acf, 0x111d, 0x442c, 0x8a,0x77, 0xb8,0x79,0x92,0xf8,0xe2,0xd6)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CICoreCursorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursor *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursor *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursor *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursor *This,
        TrustLevel *trustLevel);

    /*** ICoreCursor methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursor *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursor *This,
        __x_ABI_CWindows_CUI_CCore_CCoreCursorType *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CICoreCursorVtbl;

interface __x_ABI_CWindows_CUI_CCore_CICoreCursor {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CICoreCursorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreCursor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CICoreCursor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreCursor_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreCursor_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CICoreCursor_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CICoreCursor_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreCursor methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreCursor_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreCursor_get_Type(This,value) (This)->lpVtbl->get_Type(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreCursor_QueryInterface(__x_ABI_CWindows_CUI_CCore_CICoreCursor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreCursor_AddRef(__x_ABI_CWindows_CUI_CCore_CICoreCursor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreCursor_Release(__x_ABI_CWindows_CUI_CCore_CICoreCursor* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreCursor_GetIids(__x_ABI_CWindows_CUI_CCore_CICoreCursor* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreCursor_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CICoreCursor* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreCursor_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CICoreCursor* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreCursor methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreCursor_get_Id(__x_ABI_CWindows_CUI_CCore_CICoreCursor* This,UINT32 *value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreCursor_get_Type(__x_ABI_CWindows_CUI_CCore_CICoreCursor* This,__x_ABI_CWindows_CUI_CCore_CCoreCursorType *value) {
    return This->lpVtbl->get_Type(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ICoreCursor IID___x_ABI_CWindows_CUI_CCore_CICoreCursor
#define ICoreCursorVtbl __x_ABI_CWindows_CUI_CCore_CICoreCursorVtbl
#define ICoreCursor __x_ABI_CWindows_CUI_CCore_CICoreCursor
#define ICoreCursor_QueryInterface __x_ABI_CWindows_CUI_CCore_CICoreCursor_QueryInterface
#define ICoreCursor_AddRef __x_ABI_CWindows_CUI_CCore_CICoreCursor_AddRef
#define ICoreCursor_Release __x_ABI_CWindows_CUI_CCore_CICoreCursor_Release
#define ICoreCursor_GetIids __x_ABI_CWindows_CUI_CCore_CICoreCursor_GetIids
#define ICoreCursor_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CICoreCursor_GetRuntimeClassName
#define ICoreCursor_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CICoreCursor_GetTrustLevel
#define ICoreCursor_get_Id __x_ABI_CWindows_CUI_CCore_CICoreCursor_get_Id
#define ICoreCursor_get_Type __x_ABI_CWindows_CUI_CCore_CICoreCursor_get_Type
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CICoreCursor_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreCursorFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CICoreCursorFactory, 0xf6359621, 0xa79d, 0x4ed3, 0x8c,0x32, 0xa9,0xef,0x9d,0x6b,0x76,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("f6359621-a79d-4ed3-8c32-a9ef9d6b76a4")
                ICoreCursorFactory : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateCursor(
                        ABI::Windows::UI::Core::CoreCursorType type,
                        UINT32 id,
                        ABI::Windows::UI::Core::ICoreCursor **cursor) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CICoreCursorFactory, 0xf6359621, 0xa79d, 0x4ed3, 0x8c,0x32, 0xa9,0xef,0x9d,0x6b,0x76,0xa4)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CICoreCursorFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory *This,
        TrustLevel *trustLevel);

    /*** ICoreCursorFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateCursor)(
        __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory *This,
        __x_ABI_CWindows_CUI_CCore_CCoreCursorType type,
        UINT32 id,
        __x_ABI_CWindows_CUI_CCore_CICoreCursor **cursor);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CICoreCursorFactoryVtbl;

interface __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CICoreCursorFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreCursorFactory methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_CreateCursor(This,type,id,cursor) (This)->lpVtbl->CreateCursor(This,type,id,cursor)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_QueryInterface(__x_ABI_CWindows_CUI_CCore_CICoreCursorFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_AddRef(__x_ABI_CWindows_CUI_CCore_CICoreCursorFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_Release(__x_ABI_CWindows_CUI_CCore_CICoreCursorFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_GetIids(__x_ABI_CWindows_CUI_CCore_CICoreCursorFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CICoreCursorFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CICoreCursorFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreCursorFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_CreateCursor(__x_ABI_CWindows_CUI_CCore_CICoreCursorFactory* This,__x_ABI_CWindows_CUI_CCore_CCoreCursorType type,UINT32 id,__x_ABI_CWindows_CUI_CCore_CICoreCursor **cursor) {
    return This->lpVtbl->CreateCursor(This,type,id,cursor);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ICoreCursorFactory IID___x_ABI_CWindows_CUI_CCore_CICoreCursorFactory
#define ICoreCursorFactoryVtbl __x_ABI_CWindows_CUI_CCore_CICoreCursorFactoryVtbl
#define ICoreCursorFactory __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory
#define ICoreCursorFactory_QueryInterface __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_QueryInterface
#define ICoreCursorFactory_AddRef __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_AddRef
#define ICoreCursorFactory_Release __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_Release
#define ICoreCursorFactory_GetIids __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_GetIids
#define ICoreCursorFactory_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_GetRuntimeClassName
#define ICoreCursorFactory_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_GetTrustLevel
#define ICoreCursorFactory_CreateCursor __x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_CreateCursor
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CICoreCursorFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreDispatcher interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreDispatcher_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreDispatcher_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CICoreDispatcher, 0x60db2fa8, 0xb705, 0x4fde, 0xa7,0xd6, 0xeb,0xbb,0x18,0x91,0xd3,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("60db2fa8-b705-4fde-a7d6-ebbb1891d39e")
                ICoreDispatcher : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_HasThreadAccess(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE ProcessEvents(
                        ABI::Windows::UI::Core::CoreProcessEventsOption options) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RunAsync(
                        ABI::Windows::UI::Core::CoreDispatcherPriority priority,
                        ABI::Windows::UI::Core::IDispatchedHandler *callback,
                        ABI::Windows::Foundation::IAsyncAction **action) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RunIdleAsync(
                        ABI::Windows::UI::Core::IIdleDispatchedHandler *callback,
                        ABI::Windows::Foundation::IAsyncAction **action) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CICoreDispatcher, 0x60db2fa8, 0xb705, 0x4fde, 0xa7,0xd6, 0xeb,0xbb,0x18,0x91,0xd3,0x9e)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CICoreDispatcherVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher *This,
        TrustLevel *trustLevel);

    /*** ICoreDispatcher methods ***/
    HRESULT (STDMETHODCALLTYPE *get_HasThreadAccess)(
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *ProcessEvents)(
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher *This,
        __x_ABI_CWindows_CUI_CCore_CCoreProcessEventsOption options);

    HRESULT (STDMETHODCALLTYPE *RunAsync)(
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher *This,
        __x_ABI_CWindows_CUI_CCore_CCoreDispatcherPriority priority,
        __x_ABI_CWindows_CUI_CCore_CIDispatchedHandler *callback,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **action);

    HRESULT (STDMETHODCALLTYPE *RunIdleAsync)(
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher *This,
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler *callback,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **action);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CICoreDispatcherVtbl;

interface __x_ABI_CWindows_CUI_CCore_CICoreDispatcher {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CICoreDispatcherVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreDispatcher methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_get_HasThreadAccess(This,value) (This)->lpVtbl->get_HasThreadAccess(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_ProcessEvents(This,options) (This)->lpVtbl->ProcessEvents(This,options)
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_RunAsync(This,priority,callback,action) (This)->lpVtbl->RunAsync(This,priority,callback,action)
#define __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_RunIdleAsync(This,callback,action) (This)->lpVtbl->RunIdleAsync(This,callback,action)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_QueryInterface(__x_ABI_CWindows_CUI_CCore_CICoreDispatcher* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_AddRef(__x_ABI_CWindows_CUI_CCore_CICoreDispatcher* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_Release(__x_ABI_CWindows_CUI_CCore_CICoreDispatcher* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_GetIids(__x_ABI_CWindows_CUI_CCore_CICoreDispatcher* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CICoreDispatcher* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CICoreDispatcher* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreDispatcher methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_get_HasThreadAccess(__x_ABI_CWindows_CUI_CCore_CICoreDispatcher* This,boolean *value) {
    return This->lpVtbl->get_HasThreadAccess(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_ProcessEvents(__x_ABI_CWindows_CUI_CCore_CICoreDispatcher* This,__x_ABI_CWindows_CUI_CCore_CCoreProcessEventsOption options) {
    return This->lpVtbl->ProcessEvents(This,options);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_RunAsync(__x_ABI_CWindows_CUI_CCore_CICoreDispatcher* This,__x_ABI_CWindows_CUI_CCore_CCoreDispatcherPriority priority,__x_ABI_CWindows_CUI_CCore_CIDispatchedHandler *callback,__x_ABI_CWindows_CFoundation_CIAsyncAction **action) {
    return This->lpVtbl->RunAsync(This,priority,callback,action);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_RunIdleAsync(__x_ABI_CWindows_CUI_CCore_CICoreDispatcher* This,__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandler *callback,__x_ABI_CWindows_CFoundation_CIAsyncAction **action) {
    return This->lpVtbl->RunIdleAsync(This,callback,action);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ICoreDispatcher IID___x_ABI_CWindows_CUI_CCore_CICoreDispatcher
#define ICoreDispatcherVtbl __x_ABI_CWindows_CUI_CCore_CICoreDispatcherVtbl
#define ICoreDispatcher __x_ABI_CWindows_CUI_CCore_CICoreDispatcher
#define ICoreDispatcher_QueryInterface __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_QueryInterface
#define ICoreDispatcher_AddRef __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_AddRef
#define ICoreDispatcher_Release __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_Release
#define ICoreDispatcher_GetIids __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_GetIids
#define ICoreDispatcher_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_GetRuntimeClassName
#define ICoreDispatcher_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_GetTrustLevel
#define ICoreDispatcher_get_HasThreadAccess __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_get_HasThreadAccess
#define ICoreDispatcher_ProcessEvents __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_ProcessEvents
#define ICoreDispatcher_RunAsync __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_RunAsync
#define ICoreDispatcher_RunIdleAsync __x_ABI_CWindows_CUI_CCore_CICoreDispatcher_RunIdleAsync
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CICoreDispatcher_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreWindow interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CICoreWindow, 0x79b9d5f2, 0x879e, 0x4b89, 0xb7,0x98, 0x79,0xe4,0x75,0x98,0x03,0x0c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("79b9d5f2-879e-4b89-b798-79e47598030c")
                ICoreWindow : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AutomationHostProvider(
                        IInspectable **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Bounds(
                        ABI::Windows::Foundation::Rect *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_CustomProperties(
                        ABI::Windows::Foundation::Collections::IPropertySet **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Dispatcher(
                        ABI::Windows::UI::Core::ICoreDispatcher **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_FlowDirection(
                        ABI::Windows::UI::Core::CoreWindowFlowDirection *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_FlowDirection(
                        ABI::Windows::UI::Core::CoreWindowFlowDirection value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsInputEnabled(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_IsInputEnabled(
                        boolean value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PointerCursor(
                        ABI::Windows::UI::Core::ICoreCursor **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_PointerCursor(
                        ABI::Windows::UI::Core::ICoreCursor *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PointerPosition(
                        ABI::Windows::Foundation::Point *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Visible(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Activate(
                        ) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Close(
                        ) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetAsyncKeyState(
                        ABI::Windows::System::VirtualKey key,
                        ABI::Windows::UI::Core::CoreVirtualKeyStates *state) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetKeyState(
                        ABI::Windows::System::VirtualKey key,
                        ABI::Windows::UI::Core::CoreVirtualKeyStates *state) = 0;

                    virtual HRESULT STDMETHODCALLTYPE ReleasePointerCapture(
                        ) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SetPointerCapture(
                        ) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Activated(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowActivatedEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Activated(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_AutomationProviderRequested(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::AutomationProviderRequestedEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_AutomationProviderRequested(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_CharacterReceived(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CharacterReceivedEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_CharacterReceived(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Closed(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CoreWindowEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Closed(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_InputEnabled(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::InputEnabledEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_InputEnabled(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_KeyDown(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::KeyEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_KeyDown(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_KeyUp(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::KeyEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_KeyUp(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_PointerCaptureLost(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_PointerCaptureLost(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_PointerEntered(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_PointerEntered(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_PointerExited(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_PointerExited(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_PointerMoved(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_PointerMoved(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_PointerPressed(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_PointerPressed(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_PointerReleased(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_PointerReleased(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_TouchHitTesting(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::TouchHitTestingEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_TouchHitTesting(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_PointerWheelChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_PointerWheelChanged(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_SizeChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowSizeChangedEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_SizeChanged(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_VisibilityChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::VisibilityChangedEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_VisibilityChanged(
                        EventRegistrationToken cookie) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CICoreWindow, 0x79b9d5f2, 0x879e, 0x4b89, 0xb7,0x98, 0x79,0xe4,0x75,0x98,0x03,0x0c)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CICoreWindowVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        TrustLevel *trustLevel);

    /*** ICoreWindow methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AutomationHostProvider)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *get_Bounds)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    HRESULT (STDMETHODCALLTYPE *get_CustomProperties)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __x_ABI_CWindows_CFoundation_CCollections_CIPropertySet **value);

    HRESULT (STDMETHODCALLTYPE *get_Dispatcher)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __x_ABI_CWindows_CUI_CCore_CICoreDispatcher **value);

    HRESULT (STDMETHODCALLTYPE *get_FlowDirection)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __x_ABI_CWindows_CUI_CCore_CCoreWindowFlowDirection *value);

    HRESULT (STDMETHODCALLTYPE *put_FlowDirection)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __x_ABI_CWindows_CUI_CCore_CCoreWindowFlowDirection value);

    HRESULT (STDMETHODCALLTYPE *get_IsInputEnabled)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsInputEnabled)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_PointerCursor)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __x_ABI_CWindows_CUI_CCore_CICoreCursor **value);

    HRESULT (STDMETHODCALLTYPE *put_PointerCursor)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __x_ABI_CWindows_CUI_CCore_CICoreCursor *value);

    HRESULT (STDMETHODCALLTYPE *get_PointerPosition)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __x_ABI_CWindows_CFoundation_CPoint *value);

    HRESULT (STDMETHODCALLTYPE *get_Visible)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *Activate)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This);

    HRESULT (STDMETHODCALLTYPE *Close)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This);

    HRESULT (STDMETHODCALLTYPE *GetAsyncKeyState)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __x_ABI_CWindows_CSystem_CVirtualKey key,
        __x_ABI_CWindows_CUI_CCore_CCoreVirtualKeyStates *state);

    HRESULT (STDMETHODCALLTYPE *GetKeyState)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __x_ABI_CWindows_CSystem_CVirtualKey key,
        __x_ABI_CWindows_CUI_CCore_CCoreVirtualKeyStates *state);

    HRESULT (STDMETHODCALLTYPE *ReleasePointerCapture)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This);

    HRESULT (STDMETHODCALLTYPE *SetPointerCapture)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This);

    HRESULT (STDMETHODCALLTYPE *add_Activated)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_Activated)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_AutomationProviderRequested)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_AutomationProviderRequested)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_CharacterReceived)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_CharacterReceived)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_Closed)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_Closed)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_InputEnabled)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_InputEnabled)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_KeyDown)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_KeyDown)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_KeyUp)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_KeyUp)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_PointerCaptureLost)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_PointerCaptureLost)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_PointerEntered)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_PointerEntered)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_PointerExited)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_PointerExited)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_PointerMoved)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_PointerMoved)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_PointerPressed)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_PointerPressed)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_PointerReleased)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_PointerReleased)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_TouchHitTesting)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_TouchHitTesting)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_PointerWheelChanged)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_PointerWheelChanged)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_SizeChanged)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_SizeChanged)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_VisibilityChanged)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_VisibilityChanged)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *This,
        EventRegistrationToken cookie);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CICoreWindowVtbl;

interface __x_ABI_CWindows_CUI_CCore_CICoreWindow {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CICoreWindowVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreWindow methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_AutomationHostProvider(This,value) (This)->lpVtbl->get_AutomationHostProvider(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_Bounds(This,value) (This)->lpVtbl->get_Bounds(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_CustomProperties(This,value) (This)->lpVtbl->get_CustomProperties(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_Dispatcher(This,value) (This)->lpVtbl->get_Dispatcher(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_FlowDirection(This,value) (This)->lpVtbl->get_FlowDirection(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_put_FlowDirection(This,value) (This)->lpVtbl->put_FlowDirection(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_IsInputEnabled(This,value) (This)->lpVtbl->get_IsInputEnabled(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_put_IsInputEnabled(This,value) (This)->lpVtbl->put_IsInputEnabled(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_PointerCursor(This,value) (This)->lpVtbl->get_PointerCursor(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_put_PointerCursor(This,value) (This)->lpVtbl->put_PointerCursor(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_PointerPosition(This,value) (This)->lpVtbl->get_PointerPosition(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_Visible(This,value) (This)->lpVtbl->get_Visible(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_Activate(This) (This)->lpVtbl->Activate(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_Close(This) (This)->lpVtbl->Close(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetAsyncKeyState(This,key,state) (This)->lpVtbl->GetAsyncKeyState(This,key,state)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetKeyState(This,key,state) (This)->lpVtbl->GetKeyState(This,key,state)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_ReleasePointerCapture(This) (This)->lpVtbl->ReleasePointerCapture(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_SetPointerCapture(This) (This)->lpVtbl->SetPointerCapture(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_Activated(This,handler,cookie) (This)->lpVtbl->add_Activated(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_Activated(This,cookie) (This)->lpVtbl->remove_Activated(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_AutomationProviderRequested(This,handler,cookie) (This)->lpVtbl->add_AutomationProviderRequested(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_AutomationProviderRequested(This,cookie) (This)->lpVtbl->remove_AutomationProviderRequested(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_CharacterReceived(This,handler,cookie) (This)->lpVtbl->add_CharacterReceived(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_CharacterReceived(This,cookie) (This)->lpVtbl->remove_CharacterReceived(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_Closed(This,handler,cookie) (This)->lpVtbl->add_Closed(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_Closed(This,cookie) (This)->lpVtbl->remove_Closed(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_InputEnabled(This,handler,cookie) (This)->lpVtbl->add_InputEnabled(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_InputEnabled(This,cookie) (This)->lpVtbl->remove_InputEnabled(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_KeyDown(This,handler,cookie) (This)->lpVtbl->add_KeyDown(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_KeyDown(This,cookie) (This)->lpVtbl->remove_KeyDown(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_KeyUp(This,handler,cookie) (This)->lpVtbl->add_KeyUp(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_KeyUp(This,cookie) (This)->lpVtbl->remove_KeyUp(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerCaptureLost(This,handler,cookie) (This)->lpVtbl->add_PointerCaptureLost(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerCaptureLost(This,cookie) (This)->lpVtbl->remove_PointerCaptureLost(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerEntered(This,handler,cookie) (This)->lpVtbl->add_PointerEntered(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerEntered(This,cookie) (This)->lpVtbl->remove_PointerEntered(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerExited(This,handler,cookie) (This)->lpVtbl->add_PointerExited(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerExited(This,cookie) (This)->lpVtbl->remove_PointerExited(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerMoved(This,handler,cookie) (This)->lpVtbl->add_PointerMoved(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerMoved(This,cookie) (This)->lpVtbl->remove_PointerMoved(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerPressed(This,handler,cookie) (This)->lpVtbl->add_PointerPressed(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerPressed(This,cookie) (This)->lpVtbl->remove_PointerPressed(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerReleased(This,handler,cookie) (This)->lpVtbl->add_PointerReleased(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerReleased(This,cookie) (This)->lpVtbl->remove_PointerReleased(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_TouchHitTesting(This,handler,cookie) (This)->lpVtbl->add_TouchHitTesting(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_TouchHitTesting(This,cookie) (This)->lpVtbl->remove_TouchHitTesting(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerWheelChanged(This,handler,cookie) (This)->lpVtbl->add_PointerWheelChanged(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerWheelChanged(This,cookie) (This)->lpVtbl->remove_PointerWheelChanged(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_SizeChanged(This,handler,cookie) (This)->lpVtbl->add_SizeChanged(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_SizeChanged(This,cookie) (This)->lpVtbl->remove_SizeChanged(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_VisibilityChanged(This,handler,cookie) (This)->lpVtbl->add_VisibilityChanged(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_VisibilityChanged(This,cookie) (This)->lpVtbl->remove_VisibilityChanged(This,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_QueryInterface(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindow_AddRef(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindow_Release(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetIids(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreWindow methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_AutomationHostProvider(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,IInspectable **value) {
    return This->lpVtbl->get_AutomationHostProvider(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_Bounds(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_Bounds(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_CustomProperties(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__x_ABI_CWindows_CFoundation_CCollections_CIPropertySet **value) {
    return This->lpVtbl->get_CustomProperties(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_Dispatcher(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__x_ABI_CWindows_CUI_CCore_CICoreDispatcher **value) {
    return This->lpVtbl->get_Dispatcher(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_FlowDirection(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__x_ABI_CWindows_CUI_CCore_CCoreWindowFlowDirection *value) {
    return This->lpVtbl->get_FlowDirection(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_put_FlowDirection(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__x_ABI_CWindows_CUI_CCore_CCoreWindowFlowDirection value) {
    return This->lpVtbl->put_FlowDirection(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_IsInputEnabled(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,boolean *value) {
    return This->lpVtbl->get_IsInputEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_put_IsInputEnabled(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,boolean value) {
    return This->lpVtbl->put_IsInputEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_PointerCursor(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__x_ABI_CWindows_CUI_CCore_CICoreCursor **value) {
    return This->lpVtbl->get_PointerCursor(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_put_PointerCursor(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__x_ABI_CWindows_CUI_CCore_CICoreCursor *value) {
    return This->lpVtbl->put_PointerCursor(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_PointerPosition(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__x_ABI_CWindows_CFoundation_CPoint *value) {
    return This->lpVtbl->get_PointerPosition(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_Visible(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,boolean *value) {
    return This->lpVtbl->get_Visible(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_Activate(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This) {
    return This->lpVtbl->Activate(This);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_Close(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetAsyncKeyState(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__x_ABI_CWindows_CSystem_CVirtualKey key,__x_ABI_CWindows_CUI_CCore_CCoreVirtualKeyStates *state) {
    return This->lpVtbl->GetAsyncKeyState(This,key,state);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetKeyState(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__x_ABI_CWindows_CSystem_CVirtualKey key,__x_ABI_CWindows_CUI_CCore_CCoreVirtualKeyStates *state) {
    return This->lpVtbl->GetKeyState(This,key,state);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_ReleasePointerCapture(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This) {
    return This->lpVtbl->ReleasePointerCapture(This);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_SetPointerCapture(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This) {
    return This->lpVtbl->SetPointerCapture(This);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_Activated(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_Activated(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_Activated(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_Activated(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_AutomationProviderRequested(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_AutomationProviderRequested(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_AutomationProviderRequested(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_AutomationProviderRequested(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_CharacterReceived(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_CharacterReceived(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_CharacterReceived(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_CharacterReceived(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_Closed(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_Closed(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_Closed(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_Closed(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_InputEnabled(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_InputEnabled(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_InputEnabled(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_InputEnabled(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_KeyDown(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_KeyDown(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_KeyDown(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_KeyDown(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_KeyUp(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_KeyUp(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_KeyUp(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_KeyUp(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerCaptureLost(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_PointerCaptureLost(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerCaptureLost(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_PointerCaptureLost(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerEntered(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_PointerEntered(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerEntered(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_PointerEntered(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerExited(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_PointerExited(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerExited(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_PointerExited(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerMoved(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_PointerMoved(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerMoved(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_PointerMoved(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerPressed(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_PointerPressed(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerPressed(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_PointerPressed(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerReleased(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_PointerReleased(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerReleased(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_PointerReleased(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_TouchHitTesting(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_TouchHitTesting(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_TouchHitTesting(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_TouchHitTesting(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerWheelChanged(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_PointerWheelChanged(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerWheelChanged(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_PointerWheelChanged(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_SizeChanged(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_SizeChanged(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_SizeChanged(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_SizeChanged(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_VisibilityChanged(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_VisibilityChanged(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_VisibilityChanged(__x_ABI_CWindows_CUI_CCore_CICoreWindow* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_VisibilityChanged(This,cookie);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ICoreWindow IID___x_ABI_CWindows_CUI_CCore_CICoreWindow
#define ICoreWindowVtbl __x_ABI_CWindows_CUI_CCore_CICoreWindowVtbl
#define ICoreWindow __x_ABI_CWindows_CUI_CCore_CICoreWindow
#define ICoreWindow_QueryInterface __x_ABI_CWindows_CUI_CCore_CICoreWindow_QueryInterface
#define ICoreWindow_AddRef __x_ABI_CWindows_CUI_CCore_CICoreWindow_AddRef
#define ICoreWindow_Release __x_ABI_CWindows_CUI_CCore_CICoreWindow_Release
#define ICoreWindow_GetIids __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetIids
#define ICoreWindow_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetRuntimeClassName
#define ICoreWindow_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetTrustLevel
#define ICoreWindow_get_AutomationHostProvider __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_AutomationHostProvider
#define ICoreWindow_get_Bounds __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_Bounds
#define ICoreWindow_get_CustomProperties __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_CustomProperties
#define ICoreWindow_get_Dispatcher __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_Dispatcher
#define ICoreWindow_get_FlowDirection __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_FlowDirection
#define ICoreWindow_put_FlowDirection __x_ABI_CWindows_CUI_CCore_CICoreWindow_put_FlowDirection
#define ICoreWindow_get_IsInputEnabled __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_IsInputEnabled
#define ICoreWindow_put_IsInputEnabled __x_ABI_CWindows_CUI_CCore_CICoreWindow_put_IsInputEnabled
#define ICoreWindow_get_PointerCursor __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_PointerCursor
#define ICoreWindow_put_PointerCursor __x_ABI_CWindows_CUI_CCore_CICoreWindow_put_PointerCursor
#define ICoreWindow_get_PointerPosition __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_PointerPosition
#define ICoreWindow_get_Visible __x_ABI_CWindows_CUI_CCore_CICoreWindow_get_Visible
#define ICoreWindow_Activate __x_ABI_CWindows_CUI_CCore_CICoreWindow_Activate
#define ICoreWindow_Close __x_ABI_CWindows_CUI_CCore_CICoreWindow_Close
#define ICoreWindow_GetAsyncKeyState __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetAsyncKeyState
#define ICoreWindow_GetKeyState __x_ABI_CWindows_CUI_CCore_CICoreWindow_GetKeyState
#define ICoreWindow_ReleasePointerCapture __x_ABI_CWindows_CUI_CCore_CICoreWindow_ReleasePointerCapture
#define ICoreWindow_SetPointerCapture __x_ABI_CWindows_CUI_CCore_CICoreWindow_SetPointerCapture
#define ICoreWindow_add_Activated __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_Activated
#define ICoreWindow_remove_Activated __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_Activated
#define ICoreWindow_add_AutomationProviderRequested __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_AutomationProviderRequested
#define ICoreWindow_remove_AutomationProviderRequested __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_AutomationProviderRequested
#define ICoreWindow_add_CharacterReceived __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_CharacterReceived
#define ICoreWindow_remove_CharacterReceived __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_CharacterReceived
#define ICoreWindow_add_Closed __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_Closed
#define ICoreWindow_remove_Closed __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_Closed
#define ICoreWindow_add_InputEnabled __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_InputEnabled
#define ICoreWindow_remove_InputEnabled __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_InputEnabled
#define ICoreWindow_add_KeyDown __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_KeyDown
#define ICoreWindow_remove_KeyDown __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_KeyDown
#define ICoreWindow_add_KeyUp __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_KeyUp
#define ICoreWindow_remove_KeyUp __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_KeyUp
#define ICoreWindow_add_PointerCaptureLost __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerCaptureLost
#define ICoreWindow_remove_PointerCaptureLost __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerCaptureLost
#define ICoreWindow_add_PointerEntered __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerEntered
#define ICoreWindow_remove_PointerEntered __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerEntered
#define ICoreWindow_add_PointerExited __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerExited
#define ICoreWindow_remove_PointerExited __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerExited
#define ICoreWindow_add_PointerMoved __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerMoved
#define ICoreWindow_remove_PointerMoved __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerMoved
#define ICoreWindow_add_PointerPressed __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerPressed
#define ICoreWindow_remove_PointerPressed __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerPressed
#define ICoreWindow_add_PointerReleased __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerReleased
#define ICoreWindow_remove_PointerReleased __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerReleased
#define ICoreWindow_add_TouchHitTesting __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_TouchHitTesting
#define ICoreWindow_remove_TouchHitTesting __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_TouchHitTesting
#define ICoreWindow_add_PointerWheelChanged __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_PointerWheelChanged
#define ICoreWindow_remove_PointerWheelChanged __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_PointerWheelChanged
#define ICoreWindow_add_SizeChanged __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_SizeChanged
#define ICoreWindow_remove_SizeChanged __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_SizeChanged
#define ICoreWindow_add_VisibilityChanged __x_ABI_CWindows_CUI_CCore_CICoreWindow_add_VisibilityChanged
#define ICoreWindow_remove_VisibilityChanged __x_ABI_CWindows_CUI_CCore_CICoreWindow_remove_VisibilityChanged
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CICoreWindow_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreWindow2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CICoreWindow2, 0x7c2b1b85, 0x6917, 0x4361, 0x9c,0x02, 0x0d,0x9e,0x3a,0x42,0x0b,0x95);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("7c2b1b85-6917-4361-9c02-0d9e3a420b95")
                ICoreWindow2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE put_PointerPosition(
                        ABI::Windows::Foundation::Point value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CICoreWindow2, 0x7c2b1b85, 0x6917, 0x4361, 0x9c,0x02, 0x0d,0x9e,0x3a,0x42,0x0b,0x95)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CICoreWindow2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow2 *This,
        TrustLevel *trustLevel);

    /*** ICoreWindow2 methods ***/
    HRESULT (STDMETHODCALLTYPE *put_PointerPosition)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow2 *This,
        __x_ABI_CWindows_CFoundation_CPoint value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CICoreWindow2Vtbl;

interface __x_ABI_CWindows_CUI_CCore_CICoreWindow2 {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CICoreWindow2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreWindow2 methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow2_put_PointerPosition(This,value) (This)->lpVtbl->put_PointerPosition(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow2_QueryInterface(__x_ABI_CWindows_CUI_CCore_CICoreWindow2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindow2_AddRef(__x_ABI_CWindows_CUI_CCore_CICoreWindow2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindow2_Release(__x_ABI_CWindows_CUI_CCore_CICoreWindow2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow2_GetIids(__x_ABI_CWindows_CUI_CCore_CICoreWindow2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow2_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CICoreWindow2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow2_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CICoreWindow2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreWindow2 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow2_put_PointerPosition(__x_ABI_CWindows_CUI_CCore_CICoreWindow2* This,__x_ABI_CWindows_CFoundation_CPoint value) {
    return This->lpVtbl->put_PointerPosition(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ICoreWindow2 IID___x_ABI_CWindows_CUI_CCore_CICoreWindow2
#define ICoreWindow2Vtbl __x_ABI_CWindows_CUI_CCore_CICoreWindow2Vtbl
#define ICoreWindow2 __x_ABI_CWindows_CUI_CCore_CICoreWindow2
#define ICoreWindow2_QueryInterface __x_ABI_CWindows_CUI_CCore_CICoreWindow2_QueryInterface
#define ICoreWindow2_AddRef __x_ABI_CWindows_CUI_CCore_CICoreWindow2_AddRef
#define ICoreWindow2_Release __x_ABI_CWindows_CUI_CCore_CICoreWindow2_Release
#define ICoreWindow2_GetIids __x_ABI_CWindows_CUI_CCore_CICoreWindow2_GetIids
#define ICoreWindow2_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CICoreWindow2_GetRuntimeClassName
#define ICoreWindow2_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CICoreWindow2_GetTrustLevel
#define ICoreWindow2_put_PointerPosition __x_ABI_CWindows_CUI_CCore_CICoreWindow2_put_PointerPosition
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CICoreWindow2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreWindow3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CICoreWindow3, 0x32c20dd8, 0xfaef, 0x4375, 0xa2,0xab, 0x32,0x64,0x0e,0x48,0x15,0xc7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("32c20dd8-faef-4375-a2ab-32640e4815c7")
                ICoreWindow3 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_ClosestInteractiveBoundsRequested(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::ClosestInteractiveBoundsRequestedEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_ClosestInteractiveBoundsRequested(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetCurrentKeyEventDeviceId(
                        HSTRING *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CICoreWindow3, 0x32c20dd8, 0xfaef, 0x4375, 0xa2,0xab, 0x32,0x64,0x0e,0x48,0x15,0xc7)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CICoreWindow3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow3 *This,
        TrustLevel *trustLevel);

    /*** ICoreWindow3 methods ***/
    HRESULT (STDMETHODCALLTYPE *add_ClosestInteractiveBoundsRequested)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow3 *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_ClosestInteractiveBoundsRequested)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow3 *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *GetCurrentKeyEventDeviceId)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow3 *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CICoreWindow3Vtbl;

interface __x_ABI_CWindows_CUI_CCore_CICoreWindow3 {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CICoreWindow3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreWindow3 methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow3_add_ClosestInteractiveBoundsRequested(This,handler,cookie) (This)->lpVtbl->add_ClosestInteractiveBoundsRequested(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow3_remove_ClosestInteractiveBoundsRequested(This,cookie) (This)->lpVtbl->remove_ClosestInteractiveBoundsRequested(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetCurrentKeyEventDeviceId(This,value) (This)->lpVtbl->GetCurrentKeyEventDeviceId(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow3_QueryInterface(__x_ABI_CWindows_CUI_CCore_CICoreWindow3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindow3_AddRef(__x_ABI_CWindows_CUI_CCore_CICoreWindow3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindow3_Release(__x_ABI_CWindows_CUI_CCore_CICoreWindow3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetIids(__x_ABI_CWindows_CUI_CCore_CICoreWindow3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CICoreWindow3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CICoreWindow3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreWindow3 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow3_add_ClosestInteractiveBoundsRequested(__x_ABI_CWindows_CUI_CCore_CICoreWindow3* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_ClosestInteractiveBoundsRequested(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow3_remove_ClosestInteractiveBoundsRequested(__x_ABI_CWindows_CUI_CCore_CICoreWindow3* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_ClosestInteractiveBoundsRequested(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetCurrentKeyEventDeviceId(__x_ABI_CWindows_CUI_CCore_CICoreWindow3* This,HSTRING *value) {
    return This->lpVtbl->GetCurrentKeyEventDeviceId(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ICoreWindow3 IID___x_ABI_CWindows_CUI_CCore_CICoreWindow3
#define ICoreWindow3Vtbl __x_ABI_CWindows_CUI_CCore_CICoreWindow3Vtbl
#define ICoreWindow3 __x_ABI_CWindows_CUI_CCore_CICoreWindow3
#define ICoreWindow3_QueryInterface __x_ABI_CWindows_CUI_CCore_CICoreWindow3_QueryInterface
#define ICoreWindow3_AddRef __x_ABI_CWindows_CUI_CCore_CICoreWindow3_AddRef
#define ICoreWindow3_Release __x_ABI_CWindows_CUI_CCore_CICoreWindow3_Release
#define ICoreWindow3_GetIids __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetIids
#define ICoreWindow3_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetRuntimeClassName
#define ICoreWindow3_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetTrustLevel
#define ICoreWindow3_add_ClosestInteractiveBoundsRequested __x_ABI_CWindows_CUI_CCore_CICoreWindow3_add_ClosestInteractiveBoundsRequested
#define ICoreWindow3_remove_ClosestInteractiveBoundsRequested __x_ABI_CWindows_CUI_CCore_CICoreWindow3_remove_ClosestInteractiveBoundsRequested
#define ICoreWindow3_GetCurrentKeyEventDeviceId __x_ABI_CWindows_CUI_CCore_CICoreWindow3_GetCurrentKeyEventDeviceId
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CICoreWindow3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * ICoreWindow4 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow4_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow4_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CICoreWindow4, 0x35caf0d0, 0x47f0, 0x436c, 0xaf,0x97, 0x0d,0xd8,0x8f,0x6f,0x5f,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("35caf0d0-47f0-436c-af97-0dd88f6f5f02")
                ICoreWindow4 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_ResizeStarted(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,IInspectable* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_ResizeStarted(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_ResizeCompleted(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,IInspectable* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_ResizeCompleted(
                        EventRegistrationToken cookie) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CICoreWindow4, 0x35caf0d0, 0x47f0, 0x436c, 0xaf,0x97, 0x0d,0xd8,0x8f,0x6f,0x5f,0x02)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CICoreWindow4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow4 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow4 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow4 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow4 *This,
        TrustLevel *trustLevel);

    /*** ICoreWindow4 methods ***/
    HRESULT (STDMETHODCALLTYPE *add_ResizeStarted)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow4 *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_ResizeStarted)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow4 *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *add_ResizeCompleted)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow4 *This,
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_ResizeCompleted)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow4 *This,
        EventRegistrationToken cookie);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CICoreWindow4Vtbl;

interface __x_ABI_CWindows_CUI_CCore_CICoreWindow4 {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CICoreWindow4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreWindow4 methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4_add_ResizeStarted(This,handler,cookie) (This)->lpVtbl->add_ResizeStarted(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4_remove_ResizeStarted(This,cookie) (This)->lpVtbl->remove_ResizeStarted(This,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4_add_ResizeCompleted(This,handler,cookie) (This)->lpVtbl->add_ResizeCompleted(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow4_remove_ResizeCompleted(This,cookie) (This)->lpVtbl->remove_ResizeCompleted(This,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow4_QueryInterface(__x_ABI_CWindows_CUI_CCore_CICoreWindow4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindow4_AddRef(__x_ABI_CWindows_CUI_CCore_CICoreWindow4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindow4_Release(__x_ABI_CWindows_CUI_CCore_CICoreWindow4* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow4_GetIids(__x_ABI_CWindows_CUI_CCore_CICoreWindow4* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow4_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CICoreWindow4* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow4_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CICoreWindow4* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreWindow4 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow4_add_ResizeStarted(__x_ABI_CWindows_CUI_CCore_CICoreWindow4* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_ResizeStarted(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow4_remove_ResizeStarted(__x_ABI_CWindows_CUI_CCore_CICoreWindow4* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_ResizeStarted(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow4_add_ResizeCompleted(__x_ABI_CWindows_CUI_CCore_CICoreWindow4* This,__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_ResizeCompleted(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow4_remove_ResizeCompleted(__x_ABI_CWindows_CUI_CCore_CICoreWindow4* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_ResizeCompleted(This,cookie);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ICoreWindow4 IID___x_ABI_CWindows_CUI_CCore_CICoreWindow4
#define ICoreWindow4Vtbl __x_ABI_CWindows_CUI_CCore_CICoreWindow4Vtbl
#define ICoreWindow4 __x_ABI_CWindows_CUI_CCore_CICoreWindow4
#define ICoreWindow4_QueryInterface __x_ABI_CWindows_CUI_CCore_CICoreWindow4_QueryInterface
#define ICoreWindow4_AddRef __x_ABI_CWindows_CUI_CCore_CICoreWindow4_AddRef
#define ICoreWindow4_Release __x_ABI_CWindows_CUI_CCore_CICoreWindow4_Release
#define ICoreWindow4_GetIids __x_ABI_CWindows_CUI_CCore_CICoreWindow4_GetIids
#define ICoreWindow4_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CICoreWindow4_GetRuntimeClassName
#define ICoreWindow4_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CICoreWindow4_GetTrustLevel
#define ICoreWindow4_add_ResizeStarted __x_ABI_CWindows_CUI_CCore_CICoreWindow4_add_ResizeStarted
#define ICoreWindow4_remove_ResizeStarted __x_ABI_CWindows_CUI_CCore_CICoreWindow4_remove_ResizeStarted
#define ICoreWindow4_add_ResizeCompleted __x_ABI_CWindows_CUI_CCore_CICoreWindow4_add_ResizeCompleted
#define ICoreWindow4_remove_ResizeCompleted __x_ABI_CWindows_CUI_CCore_CICoreWindow4_remove_ResizeCompleted
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CICoreWindow4_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * ICoreWindow5 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindow5_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindow5_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CICoreWindow5, 0x4b4ae1e1, 0x2e6d, 0x4eaa, 0xbd,0xa1, 0x1c,0x5c,0xc1,0xbe,0xe1,0x41);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("4b4ae1e1-2e6d-4eaa-bda1-1c5cc1bee141")
                ICoreWindow5 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DispatcherQueue(
                        ABI::Windows::System::IDispatcherQueue **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ActivationMode(
                        ABI::Windows::UI::Core::CoreWindowActivationMode *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CICoreWindow5, 0x4b4ae1e1, 0x2e6d, 0x4eaa, 0xbd,0xa1, 0x1c,0x5c,0xc1,0xbe,0xe1,0x41)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CICoreWindow5Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow5 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow5 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow5 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow5 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow5 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow5 *This,
        TrustLevel *trustLevel);

    /*** ICoreWindow5 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DispatcherQueue)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow5 *This,
        __x_ABI_CWindows_CSystem_CIDispatcherQueue **value);

    HRESULT (STDMETHODCALLTYPE *get_ActivationMode)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindow5 *This,
        __x_ABI_CWindows_CUI_CCore_CCoreWindowActivationMode *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CICoreWindow5Vtbl;

interface __x_ABI_CWindows_CUI_CCore_CICoreWindow5 {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CICoreWindow5Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow5_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow5_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow5_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow5_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow5_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow5_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreWindow5 methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow5_get_DispatcherQueue(This,value) (This)->lpVtbl->get_DispatcherQueue(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindow5_get_ActivationMode(This,value) (This)->lpVtbl->get_ActivationMode(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow5_QueryInterface(__x_ABI_CWindows_CUI_CCore_CICoreWindow5* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindow5_AddRef(__x_ABI_CWindows_CUI_CCore_CICoreWindow5* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindow5_Release(__x_ABI_CWindows_CUI_CCore_CICoreWindow5* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow5_GetIids(__x_ABI_CWindows_CUI_CCore_CICoreWindow5* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow5_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CICoreWindow5* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow5_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CICoreWindow5* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreWindow5 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow5_get_DispatcherQueue(__x_ABI_CWindows_CUI_CCore_CICoreWindow5* This,__x_ABI_CWindows_CSystem_CIDispatcherQueue **value) {
    return This->lpVtbl->get_DispatcherQueue(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindow5_get_ActivationMode(__x_ABI_CWindows_CUI_CCore_CICoreWindow5* This,__x_ABI_CWindows_CUI_CCore_CCoreWindowActivationMode *value) {
    return This->lpVtbl->get_ActivationMode(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ICoreWindow5 IID___x_ABI_CWindows_CUI_CCore_CICoreWindow5
#define ICoreWindow5Vtbl __x_ABI_CWindows_CUI_CCore_CICoreWindow5Vtbl
#define ICoreWindow5 __x_ABI_CWindows_CUI_CCore_CICoreWindow5
#define ICoreWindow5_QueryInterface __x_ABI_CWindows_CUI_CCore_CICoreWindow5_QueryInterface
#define ICoreWindow5_AddRef __x_ABI_CWindows_CUI_CCore_CICoreWindow5_AddRef
#define ICoreWindow5_Release __x_ABI_CWindows_CUI_CCore_CICoreWindow5_Release
#define ICoreWindow5_GetIids __x_ABI_CWindows_CUI_CCore_CICoreWindow5_GetIids
#define ICoreWindow5_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CICoreWindow5_GetRuntimeClassName
#define ICoreWindow5_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CICoreWindow5_GetTrustLevel
#define ICoreWindow5_get_DispatcherQueue __x_ABI_CWindows_CUI_CCore_CICoreWindow5_get_DispatcherQueue
#define ICoreWindow5_get_ActivationMode __x_ABI_CWindows_CUI_CCore_CICoreWindow5_get_ActivationMode
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CICoreWindow5_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * ICoreWindowEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs, 0x272b1ef3, 0xc633, 0x4da5, 0xa2,0x6c, 0xc6,0xd0,0xf5,0x6b,0x29,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("272b1ef3-c633-4da5-a26c-c6d0f56b29da")
                ICoreWindowEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Handled(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_Handled(
                        boolean value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs, 0x272b1ef3, 0xc633, 0x4da5, 0xa2,0x6c, 0xc6,0xd0,0xf5,0x6b,0x29,0xda)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs *This,
        TrustLevel *trustLevel);

    /*** ICoreWindowEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Handled)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_Handled)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs *This,
        boolean value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreWindowEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_get_Handled(This,value) (This)->lpVtbl->get_Handled(This,value)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_put_Handled(This,value) (This)->lpVtbl->put_Handled(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_Release(__x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreWindowEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_get_Handled(__x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs* This,boolean *value) {
    return This->lpVtbl->get_Handled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_put_Handled(__x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs* This,boolean value) {
    return This->lpVtbl->put_Handled(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ICoreWindowEventArgs IID___x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs
#define ICoreWindowEventArgsVtbl __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgsVtbl
#define ICoreWindowEventArgs __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs
#define ICoreWindowEventArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_QueryInterface
#define ICoreWindowEventArgs_AddRef __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_AddRef
#define ICoreWindowEventArgs_Release __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_Release
#define ICoreWindowEventArgs_GetIids __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_GetIids
#define ICoreWindowEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_GetRuntimeClassName
#define ICoreWindowEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_GetTrustLevel
#define ICoreWindowEventArgs_get_Handled __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_get_Handled
#define ICoreWindowEventArgs_put_Handled __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_put_Handled
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ICoreWindowStatic interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CICoreWindowStatic, 0x4d239005, 0x3c2a, 0x41b1, 0x90,0x22, 0x53,0x6b,0xb9,0xcf,0x93,0xb1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("4d239005-3c2a-41b1-9022-536bb9cf93b1")
                ICoreWindowStatic : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetForCurrentThread(
                        ABI::Windows::UI::Core::ICoreWindow **windows) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CICoreWindowStatic, 0x4d239005, 0x3c2a, 0x41b1, 0x90,0x22, 0x53,0x6b,0xb9,0xcf,0x93,0xb1)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CICoreWindowStaticVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic *This,
        TrustLevel *trustLevel);

    /*** ICoreWindowStatic methods ***/
    HRESULT (STDMETHODCALLTYPE *GetForCurrentThread)(
        __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow **windows);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CICoreWindowStaticVtbl;

interface __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CICoreWindowStaticVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ICoreWindowStatic methods ***/
#define __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetForCurrentThread(This,windows) (This)->lpVtbl->GetForCurrentThread(This,windows)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_QueryInterface(__x_ABI_CWindows_CUI_CCore_CICoreWindowStatic* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_AddRef(__x_ABI_CWindows_CUI_CCore_CICoreWindowStatic* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_Release(__x_ABI_CWindows_CUI_CCore_CICoreWindowStatic* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetIids(__x_ABI_CWindows_CUI_CCore_CICoreWindowStatic* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CICoreWindowStatic* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CICoreWindowStatic* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ICoreWindowStatic methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetForCurrentThread(__x_ABI_CWindows_CUI_CCore_CICoreWindowStatic* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow **windows) {
    return This->lpVtbl->GetForCurrentThread(This,windows);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ICoreWindowStatic IID___x_ABI_CWindows_CUI_CCore_CICoreWindowStatic
#define ICoreWindowStaticVtbl __x_ABI_CWindows_CUI_CCore_CICoreWindowStaticVtbl
#define ICoreWindowStatic __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic
#define ICoreWindowStatic_QueryInterface __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_QueryInterface
#define ICoreWindowStatic_AddRef __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_AddRef
#define ICoreWindowStatic_Release __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_Release
#define ICoreWindowStatic_GetIids __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetIids
#define ICoreWindowStatic_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetRuntimeClassName
#define ICoreWindowStatic_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetTrustLevel
#define ICoreWindowStatic_GetForCurrentThread __x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_GetForCurrentThread
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CICoreWindowStatic_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIdleDispatchedHandlerArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs, 0x98bb6a24, 0xdc1c, 0x43cb, 0xb4,0xed, 0xd1,0xc0,0xeb,0x23,0x91,0xf3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("98bb6a24-dc1c-43cb-b4ed-d1c0eb2391f3")
                IIdleDispatchedHandlerArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_IsDispatcherIdle(
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs, 0x98bb6a24, 0xdc1c, 0x43cb, 0xb4,0xed, 0xd1,0xc0,0xeb,0x23,0x91,0xf3)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs *This,
        TrustLevel *trustLevel);

    /*** IIdleDispatchedHandlerArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IsDispatcherIdle)(
        __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIdleDispatchedHandlerArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_get_IsDispatcherIdle(This,value) (This)->lpVtbl->get_IsDispatcherIdle(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_Release(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIdleDispatchedHandlerArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_get_IsDispatcherIdle(__x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs* This,boolean *value) {
    return This->lpVtbl->get_IsDispatcherIdle(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IIdleDispatchedHandlerArgs IID___x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs
#define IIdleDispatchedHandlerArgsVtbl __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgsVtbl
#define IIdleDispatchedHandlerArgs __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs
#define IIdleDispatchedHandlerArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_QueryInterface
#define IIdleDispatchedHandlerArgs_AddRef __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_AddRef
#define IIdleDispatchedHandlerArgs_Release __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_Release
#define IIdleDispatchedHandlerArgs_GetIids __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_GetIids
#define IIdleDispatchedHandlerArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_GetRuntimeClassName
#define IIdleDispatchedHandlerArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_GetTrustLevel
#define IIdleDispatchedHandlerArgs_get_IsDispatcherIdle __x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_get_IsDispatcherIdle
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIIdleDispatchedHandlerArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IInputEnabledEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs, 0x80371d4f, 0x2fd8, 0x4c24, 0xaa,0x86, 0x31,0x63,0xa8,0x7b,0x4e,0x5a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("80371d4f-2fd8-4c24-aa86-3163a87b4e5a")
                IInputEnabledEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_InputEnabled(
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs, 0x80371d4f, 0x2fd8, 0x4c24, 0xaa,0x86, 0x31,0x63,0xa8,0x7b,0x4e,0x5a)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs *This,
        TrustLevel *trustLevel);

    /*** IInputEnabledEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_InputEnabled)(
        __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IInputEnabledEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_get_InputEnabled(This,value) (This)->lpVtbl->get_InputEnabled(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_Release(__x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IInputEnabledEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_get_InputEnabled(__x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs* This,boolean *value) {
    return This->lpVtbl->get_InputEnabled(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IInputEnabledEventArgs IID___x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs
#define IInputEnabledEventArgsVtbl __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgsVtbl
#define IInputEnabledEventArgs __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs
#define IInputEnabledEventArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_QueryInterface
#define IInputEnabledEventArgs_AddRef __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_AddRef
#define IInputEnabledEventArgs_Release __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_Release
#define IInputEnabledEventArgs_GetIids __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_GetIids
#define IInputEnabledEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_GetRuntimeClassName
#define IInputEnabledEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_GetTrustLevel
#define IInputEnabledEventArgs_get_InputEnabled __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_get_InputEnabled
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IKeyEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIKeyEventArgs, 0x5ff5e930, 0x2544, 0x4a17, 0xbd,0x78, 0x1f,0x2f,0xde,0xbb,0x10,0x6b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("5ff5e930-2544-4a17-bd78-1f2fdebb106b")
                IKeyEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_VirtualKey(
                        ABI::Windows::System::VirtualKey *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_KeyStatus(
                        ABI::Windows::UI::Core::CorePhysicalKeyStatus *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs, 0x5ff5e930, 0x2544, 0x4a17, 0xbd,0x78, 0x1f,0x2f,0xde,0xbb,0x10,0x6b)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIKeyEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs *This,
        TrustLevel *trustLevel);

    /*** IKeyEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_VirtualKey)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs *This,
        __x_ABI_CWindows_CSystem_CVirtualKey *value);

    HRESULT (STDMETHODCALLTYPE *get_KeyStatus)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CCorePhysicalKeyStatus *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIKeyEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIKeyEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKeyEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_get_VirtualKey(This,value) (This)->lpVtbl->get_VirtualKey(This,value)
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_get_KeyStatus(This,value) (This)->lpVtbl->get_KeyStatus(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_Release(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKeyEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_get_VirtualKey(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs* This,__x_ABI_CWindows_CSystem_CVirtualKey *value) {
    return This->lpVtbl->get_VirtualKey(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_get_KeyStatus(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs* This,__x_ABI_CWindows_CUI_CCore_CCorePhysicalKeyStatus *value) {
    return This->lpVtbl->get_KeyStatus(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IKeyEventArgs IID___x_ABI_CWindows_CUI_CCore_CIKeyEventArgs
#define IKeyEventArgsVtbl __x_ABI_CWindows_CUI_CCore_CIKeyEventArgsVtbl
#define IKeyEventArgs __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs
#define IKeyEventArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_QueryInterface
#define IKeyEventArgs_AddRef __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_AddRef
#define IKeyEventArgs_Release __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_Release
#define IKeyEventArgs_GetIids __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_GetIids
#define IKeyEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_GetRuntimeClassName
#define IKeyEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_GetTrustLevel
#define IKeyEventArgs_get_VirtualKey __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_get_VirtualKey
#define IKeyEventArgs_get_KeyStatus __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_get_KeyStatus
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IKeyEventArgs2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2, 0x583add98, 0x0790, 0x4571, 0x9b,0x12, 0x64,0x5e,0xf9,0xd7,0x9e,0x42);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("583add98-0790-4571-9b12-645ef9d79e42")
                IKeyEventArgs2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DeviceId(
                        HSTRING *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2, 0x583add98, 0x0790, 0x4571, 0x9b,0x12, 0x64,0x5e,0xf9,0xd7,0x9e,0x42)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 *This,
        TrustLevel *trustLevel);

    /*** IKeyEventArgs2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DeviceId)(
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2Vtbl;

interface __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2 {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKeyEventArgs2 methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_get_DeviceId(This,value) (This)->lpVtbl->get_DeviceId(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_AddRef(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_Release(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_GetIids(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKeyEventArgs2 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_get_DeviceId(__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2* This,HSTRING *value) {
    return This->lpVtbl->get_DeviceId(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IKeyEventArgs2 IID___x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2
#define IKeyEventArgs2Vtbl __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2Vtbl
#define IKeyEventArgs2 __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2
#define IKeyEventArgs2_QueryInterface __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_QueryInterface
#define IKeyEventArgs2_AddRef __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_AddRef
#define IKeyEventArgs2_Release __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_Release
#define IKeyEventArgs2_GetIids __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_GetIids
#define IKeyEventArgs2_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_GetRuntimeClassName
#define IKeyEventArgs2_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_GetTrustLevel
#define IKeyEventArgs2_get_DeviceId __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_get_DeviceId
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIKeyEventArgs2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IPointerEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIPointerEventArgs, 0x920d9cb1, 0xa5fc, 0x4a21, 0x8c,0x09, 0x49,0xdf,0xe6,0xff,0xe2,0x5f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("920d9cb1-a5fc-4a21-8c09-49dfe6ffe25f")
                IPointerEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_CurrentPoint(
                        ABI::Windows::UI::Input::IPointerPoint **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_KeyModifiers(
                        ABI::Windows::System::VirtualKeyModifiers *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetIntermediatePoints(
                        ABI::Windows::Foundation::Collections::IVector<ABI::Windows::UI::Input::PointerPoint* > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIPointerEventArgs, 0x920d9cb1, 0xa5fc, 0x4a21, 0x8c,0x09, 0x49,0xdf,0xe6,0xff,0xe2,0x5f)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIPointerEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs *This,
        TrustLevel *trustLevel);

    /*** IPointerEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CurrentPoint)(
        __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs *This,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **value);

    HRESULT (STDMETHODCALLTYPE *get_KeyModifiers)(
        __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs *This,
        __x_ABI_CWindows_CSystem_CVirtualKeyModifiers *value);

    HRESULT (STDMETHODCALLTYPE *GetIntermediatePoints)(
        __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs *This,
        __FIVector_1_Windows__CUI__CInput__CPointerPoint **value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIPointerEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIPointerEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPointerEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_get_CurrentPoint(This,value) (This)->lpVtbl->get_CurrentPoint(This,value)
#define __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_get_KeyModifiers(This,value) (This)->lpVtbl->get_KeyModifiers(This,value)
#define __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetIntermediatePoints(This,value) (This)->lpVtbl->GetIntermediatePoints(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIPointerEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CIPointerEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_Release(__x_ABI_CWindows_CUI_CCore_CIPointerEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CIPointerEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CIPointerEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CIPointerEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPointerEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_get_CurrentPoint(__x_ABI_CWindows_CUI_CCore_CIPointerEventArgs* This,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **value) {
    return This->lpVtbl->get_CurrentPoint(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_get_KeyModifiers(__x_ABI_CWindows_CUI_CCore_CIPointerEventArgs* This,__x_ABI_CWindows_CSystem_CVirtualKeyModifiers *value) {
    return This->lpVtbl->get_KeyModifiers(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetIntermediatePoints(__x_ABI_CWindows_CUI_CCore_CIPointerEventArgs* This,__FIVector_1_Windows__CUI__CInput__CPointerPoint **value) {
    return This->lpVtbl->GetIntermediatePoints(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IPointerEventArgs IID___x_ABI_CWindows_CUI_CCore_CIPointerEventArgs
#define IPointerEventArgsVtbl __x_ABI_CWindows_CUI_CCore_CIPointerEventArgsVtbl
#define IPointerEventArgs __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs
#define IPointerEventArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_QueryInterface
#define IPointerEventArgs_AddRef __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_AddRef
#define IPointerEventArgs_Release __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_Release
#define IPointerEventArgs_GetIids __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetIids
#define IPointerEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetRuntimeClassName
#define IPointerEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetTrustLevel
#define IPointerEventArgs_get_CurrentPoint __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_get_CurrentPoint
#define IPointerEventArgs_get_KeyModifiers __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_get_KeyModifiers
#define IPointerEventArgs_GetIntermediatePoints __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_GetIntermediatePoints
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIPointerEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ITouchHitTestingEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs, 0x22f3b823, 0x0b7c, 0x424e, 0x9d,0xf7, 0x33,0xd4,0xf9,0x62,0x93,0x1b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("22f3b823-0b7c-424e-9df7-33d4f962931b")
                ITouchHitTestingEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_ProximityEvaluation(
                        ABI::Windows::UI::Core::CoreProximityEvaluation *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_ProximityEvaluation(
                        ABI::Windows::UI::Core::CoreProximityEvaluation value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Point(
                        ABI::Windows::Foundation::Point *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_BoundingBox(
                        ABI::Windows::Foundation::Rect *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE EvaluateProximityToRect(
                        ABI::Windows::Foundation::Rect bounding_box,
                        ABI::Windows::UI::Core::CoreProximityEvaluation *evaluation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE EvaluateProximityToPolygon(
                        UINT32 vertice_size,
                        ABI::Windows::Foundation::Point *vertices,
                        ABI::Windows::UI::Core::CoreProximityEvaluation *evaluation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs, 0x22f3b823, 0x0b7c, 0x424e, 0x9d,0xf7, 0x33,0xd4,0xf9,0x62,0x93,0x1b)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This,
        TrustLevel *trustLevel);

    /*** ITouchHitTestingEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ProximityEvaluation)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation *value);

    HRESULT (STDMETHODCALLTYPE *put_ProximityEvaluation)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation value);

    HRESULT (STDMETHODCALLTYPE *get_Point)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This,
        __x_ABI_CWindows_CFoundation_CPoint *value);

    HRESULT (STDMETHODCALLTYPE *get_BoundingBox)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    HRESULT (STDMETHODCALLTYPE *EvaluateProximityToRect)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This,
        __x_ABI_CWindows_CFoundation_CRect bounding_box,
        __x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation *evaluation);

    HRESULT (STDMETHODCALLTYPE *EvaluateProximityToPolygon)(
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *This,
        UINT32 vertice_size,
        __x_ABI_CWindows_CFoundation_CPoint *vertices,
        __x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation *evaluation);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ITouchHitTestingEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_get_ProximityEvaluation(This,value) (This)->lpVtbl->get_ProximityEvaluation(This,value)
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_put_ProximityEvaluation(This,value) (This)->lpVtbl->put_ProximityEvaluation(This,value)
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_get_Point(This,value) (This)->lpVtbl->get_Point(This,value)
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_get_BoundingBox(This,value) (This)->lpVtbl->get_BoundingBox(This,value)
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_EvaluateProximityToRect(This,bounding_box,evaluation) (This)->lpVtbl->EvaluateProximityToRect(This,bounding_box,evaluation)
#define __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_EvaluateProximityToPolygon(This,vertice_size,vertices,evaluation) (This)->lpVtbl->EvaluateProximityToPolygon(This,vertice_size,vertices,evaluation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_Release(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ITouchHitTestingEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_get_ProximityEvaluation(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This,__x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation *value) {
    return This->lpVtbl->get_ProximityEvaluation(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_put_ProximityEvaluation(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This,__x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation value) {
    return This->lpVtbl->put_ProximityEvaluation(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_get_Point(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This,__x_ABI_CWindows_CFoundation_CPoint *value) {
    return This->lpVtbl->get_Point(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_get_BoundingBox(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_BoundingBox(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_EvaluateProximityToRect(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This,__x_ABI_CWindows_CFoundation_CRect bounding_box,__x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation *evaluation) {
    return This->lpVtbl->EvaluateProximityToRect(This,bounding_box,evaluation);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_EvaluateProximityToPolygon(__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs* This,UINT32 vertice_size,__x_ABI_CWindows_CFoundation_CPoint *vertices,__x_ABI_CWindows_CUI_CCore_CCoreProximityEvaluation *evaluation) {
    return This->lpVtbl->EvaluateProximityToPolygon(This,vertice_size,vertices,evaluation);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_ITouchHitTestingEventArgs IID___x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs
#define ITouchHitTestingEventArgsVtbl __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgsVtbl
#define ITouchHitTestingEventArgs __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs
#define ITouchHitTestingEventArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_QueryInterface
#define ITouchHitTestingEventArgs_AddRef __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_AddRef
#define ITouchHitTestingEventArgs_Release __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_Release
#define ITouchHitTestingEventArgs_GetIids __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_GetIids
#define ITouchHitTestingEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_GetRuntimeClassName
#define ITouchHitTestingEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_GetTrustLevel
#define ITouchHitTestingEventArgs_get_ProximityEvaluation __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_get_ProximityEvaluation
#define ITouchHitTestingEventArgs_put_ProximityEvaluation __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_put_ProximityEvaluation
#define ITouchHitTestingEventArgs_get_Point __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_get_Point
#define ITouchHitTestingEventArgs_get_BoundingBox __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_get_BoundingBox
#define ITouchHitTestingEventArgs_EvaluateProximityToRect __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_EvaluateProximityToRect
#define ITouchHitTestingEventArgs_EvaluateProximityToPolygon __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_EvaluateProximityToPolygon
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IVisibilityChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs, 0xbf9918ea, 0xd801, 0x4564, 0xa4,0x95, 0xb1,0xe8,0x4f,0x8a,0xd0,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("bf9918ea-d801-4564-a495-b1e84f8ad085")
                IVisibilityChangedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Visible(
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs, 0xbf9918ea, 0xd801, 0x4564, 0xa4,0x95, 0xb1,0xe8,0x4f,0x8a,0xd0,0x85)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IVisibilityChangedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Visible)(
        __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVisibilityChangedEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_get_Visible(This,value) (This)->lpVtbl->get_Visible(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_Release(__x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVisibilityChangedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_get_Visible(__x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs* This,boolean *value) {
    return This->lpVtbl->get_Visible(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IVisibilityChangedEventArgs IID___x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs
#define IVisibilityChangedEventArgsVtbl __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgsVtbl
#define IVisibilityChangedEventArgs __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs
#define IVisibilityChangedEventArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_QueryInterface
#define IVisibilityChangedEventArgs_AddRef __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_AddRef
#define IVisibilityChangedEventArgs_Release __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_Release
#define IVisibilityChangedEventArgs_GetIids __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_GetIids
#define IVisibilityChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_GetRuntimeClassName
#define IVisibilityChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_GetTrustLevel
#define IVisibilityChangedEventArgs_get_Visible __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_get_Visible
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IWindowActivatedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs, 0x179d65e7, 0x4658, 0x4cb6, 0xaa,0x13, 0x41,0xd0,0x94,0xea,0x25,0x5e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("179d65e7-4658-4cb6-aa13-41d094ea255e")
                IWindowActivatedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_WindowActivationState(
                        ABI::Windows::UI::Core::CoreWindowActivationState *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs, 0x179d65e7, 0x4658, 0x4cb6, 0xaa,0x13, 0x41,0xd0,0x94,0xea,0x25,0x5e)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IWindowActivatedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_WindowActivationState)(
        __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CCoreWindowActivationState *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IWindowActivatedEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_get_WindowActivationState(This,value) (This)->lpVtbl->get_WindowActivationState(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_Release(__x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IWindowActivatedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_get_WindowActivationState(__x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs* This,__x_ABI_CWindows_CUI_CCore_CCoreWindowActivationState *value) {
    return This->lpVtbl->get_WindowActivationState(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IWindowActivatedEventArgs IID___x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs
#define IWindowActivatedEventArgsVtbl __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgsVtbl
#define IWindowActivatedEventArgs __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs
#define IWindowActivatedEventArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_QueryInterface
#define IWindowActivatedEventArgs_AddRef __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_AddRef
#define IWindowActivatedEventArgs_Release __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_Release
#define IWindowActivatedEventArgs_GetIids __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_GetIids
#define IWindowActivatedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_GetRuntimeClassName
#define IWindowActivatedEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_GetTrustLevel
#define IWindowActivatedEventArgs_get_WindowActivationState __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_get_WindowActivationState
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IWindowSizeChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs, 0x5a200ec7, 0x0426, 0x47dc, 0xb8,0x6c, 0x6f,0x47,0x59,0x15,0xe4,0x51);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace Core {
                MIDL_INTERFACE("5a200ec7-0426-47dc-b86c-6f475915e451")
                IWindowSizeChangedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Size(
                        ABI::Windows::Foundation::Size *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs, 0x5a200ec7, 0x0426, 0x47dc, 0xb8,0x6c, 0x6f,0x47,0x59,0x15,0xe4,0x51)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IWindowSizeChangedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs *This,
        __x_ABI_CWindows_CFoundation_CSize *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IWindowSizeChangedEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_AddRef(__x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_Release(__x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_GetIids(__x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IWindowSizeChangedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_get_Size(__x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs* This,__x_ABI_CWindows_CFoundation_CSize *value) {
    return This->lpVtbl->get_Size(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_Core
#define IID_IWindowSizeChangedEventArgs IID___x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs
#define IWindowSizeChangedEventArgsVtbl __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgsVtbl
#define IWindowSizeChangedEventArgs __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs
#define IWindowSizeChangedEventArgs_QueryInterface __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_QueryInterface
#define IWindowSizeChangedEventArgs_AddRef __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_AddRef
#define IWindowSizeChangedEventArgs_Release __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_Release
#define IWindowSizeChangedEventArgs_GetIids __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_GetIids
#define IWindowSizeChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_GetRuntimeClassName
#define IWindowSizeChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_GetTrustLevel
#define IWindowSizeChangedEventArgs_get_Size __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_get_Size
#endif /* WIDL_using_Windows_UI_Core */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.AutomationProviderRequestedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_AutomationProviderRequestedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_AutomationProviderRequestedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_AutomationProviderRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','A','u','t','o','m','a','t','i','o','n','P','r','o','v','i','d','e','r','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_AutomationProviderRequestedEventArgs[] = L"Windows.UI.Core.AutomationProviderRequestedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_AutomationProviderRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','A','u','t','o','m','a','t','i','o','n','P','r','o','v','i','d','e','r','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_AutomationProviderRequestedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.CharacterReceivedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_CharacterReceivedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_CharacterReceivedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_CharacterReceivedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','h','a','r','a','c','t','e','r','R','e','c','e','i','v','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_CharacterReceivedEventArgs[] = L"Windows.UI.Core.CharacterReceivedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_CharacterReceivedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','h','a','r','a','c','t','e','r','R','e','c','e','i','v','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_CharacterReceivedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.ClosestInteractiveBoundsRequestedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef RUNTIMECLASS_Windows_UI_Core_ClosestInteractiveBoundsRequestedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_ClosestInteractiveBoundsRequestedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_ClosestInteractiveBoundsRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','l','o','s','e','s','t','I','n','t','e','r','a','c','t','i','v','e','B','o','u','n','d','s','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_ClosestInteractiveBoundsRequestedEventArgs[] = L"Windows.UI.Core.ClosestInteractiveBoundsRequestedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_ClosestInteractiveBoundsRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','l','o','s','e','s','t','I','n','t','e','r','a','c','t','i','v','e','B','o','u','n','d','s','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_ClosestInteractiveBoundsRequestedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*
 * Class Windows.UI.Core.CoreCursor
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_CoreCursor_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_CoreCursor_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_CoreCursor[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','o','r','e','C','u','r','s','o','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_CoreCursor[] = L"Windows.UI.Core.CoreCursor";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_CoreCursor[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','o','r','e','C','u','r','s','o','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_CoreCursor_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.CoreDispatcher
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_CoreDispatcher_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_CoreDispatcher_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_CoreDispatcher[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','o','r','e','D','i','s','p','a','t','c','h','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_CoreDispatcher[] = L"Windows.UI.Core.CoreDispatcher";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_CoreDispatcher[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','o','r','e','D','i','s','p','a','t','c','h','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_CoreDispatcher_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.CoreWindow
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_CoreWindow_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_CoreWindow_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_CoreWindow[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','o','r','e','W','i','n','d','o','w',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_CoreWindow[] = L"Windows.UI.Core.CoreWindow";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_CoreWindow[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','o','r','e','W','i','n','d','o','w',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_CoreWindow_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.CoreWindowEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_CoreWindowEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_CoreWindowEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_CoreWindowEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','o','r','e','W','i','n','d','o','w','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_CoreWindowEventArgs[] = L"Windows.UI.Core.CoreWindowEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_CoreWindowEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','C','o','r','e','W','i','n','d','o','w','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_CoreWindowEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.IdleDispatchedHandlerArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_IdleDispatchedHandlerArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_IdleDispatchedHandlerArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_IdleDispatchedHandlerArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','I','d','l','e','D','i','s','p','a','t','c','h','e','d','H','a','n','d','l','e','r','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_IdleDispatchedHandlerArgs[] = L"Windows.UI.Core.IdleDispatchedHandlerArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_IdleDispatchedHandlerArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','I','d','l','e','D','i','s','p','a','t','c','h','e','d','H','a','n','d','l','e','r','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_IdleDispatchedHandlerArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.InputEnabledEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_InputEnabledEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_InputEnabledEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_InputEnabledEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','I','n','p','u','t','E','n','a','b','l','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_InputEnabledEventArgs[] = L"Windows.UI.Core.InputEnabledEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_InputEnabledEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','I','n','p','u','t','E','n','a','b','l','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_InputEnabledEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.KeyEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_KeyEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_KeyEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_KeyEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','K','e','y','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_KeyEventArgs[] = L"Windows.UI.Core.KeyEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_KeyEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','K','e','y','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_KeyEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.PointerEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_PointerEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_PointerEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_PointerEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','P','o','i','n','t','e','r','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_PointerEventArgs[] = L"Windows.UI.Core.PointerEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_PointerEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','P','o','i','n','t','e','r','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_PointerEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.TouchHitTestingEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_TouchHitTestingEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_TouchHitTestingEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_TouchHitTestingEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','T','o','u','c','h','H','i','t','T','e','s','t','i','n','g','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_TouchHitTestingEventArgs[] = L"Windows.UI.Core.TouchHitTestingEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_TouchHitTestingEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','T','o','u','c','h','H','i','t','T','e','s','t','i','n','g','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_TouchHitTestingEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.VisibilityChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_VisibilityChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_VisibilityChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_VisibilityChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','V','i','s','i','b','i','l','i','t','y','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_VisibilityChangedEventArgs[] = L"Windows.UI.Core.VisibilityChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_VisibilityChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','V','i','s','i','b','i','l','i','t','y','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_VisibilityChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.WindowActivatedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_WindowActivatedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_WindowActivatedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_WindowActivatedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','W','i','n','d','o','w','A','c','t','i','v','a','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_WindowActivatedEventArgs[] = L"Windows.UI.Core.WindowActivatedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_WindowActivatedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','W','i','n','d','o','w','A','c','t','i','v','a','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_WindowActivatedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.Core.WindowSizeChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_Core_WindowSizeChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_Core_WindowSizeChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_Core_WindowSizeChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','W','i','n','d','o','w','S','i','z','e','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_WindowSizeChangedEventArgs[] = L"Windows.UI.Core.WindowSizeChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_Core_WindowSizeChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','C','o','r','e','.','W','i','n','d','o','w','S','i','z','e','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_Core_WindowSizeChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IVectorView<ABI::Windows::UI::Input::PointerPoint* > interface
 */
#ifndef ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CUI__CInput__CPointerPoint, 0xf0f57411, 0x7786, 0x5174, 0x87,0x52, 0x4c,0x5e,0x83,0x4b,0x6d,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("f0f57411-**************-4c5e834b6da2")
                IVectorView<ABI::Windows::UI::Input::PointerPoint* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Input::PointerPoint*, ABI::Windows::UI::Input::IPointerPoint* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint, 0xf0f57411, 0x7786, 0x5174, 0x87,0x52, 0x4c,0x5e,0x83,0x4b,0x6d,0xa2)
#endif
#else
typedef struct __FIVectorView_1_Windows__CUI__CInput__CPointerPointVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::UI::Input::PointerPoint* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 index,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CUI__CInput__CPointerPointVtbl;

interface __FIVectorView_1_Windows__CUI__CInput__CPointerPoint {
    CONST_VTBL __FIVectorView_1_Windows__CUI__CInput__CPointerPointVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::UI::Input::PointerPoint* > methods ***/
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_QueryInterface(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_AddRef(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_Release(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetIids(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::UI::Input::PointerPoint* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetAt(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 index,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_get_Size(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_IndexOf(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,__x_ABI_CWindows_CUI_CInput_CIPointerPoint *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetMany(__FIVectorView_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_PointerPoint IID___FIVectorView_1_Windows__CUI__CInput__CPointerPoint
#define IVectorView_PointerPointVtbl __FIVectorView_1_Windows__CUI__CInput__CPointerPointVtbl
#define IVectorView_PointerPoint __FIVectorView_1_Windows__CUI__CInput__CPointerPoint
#define IVectorView_PointerPoint_QueryInterface __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_QueryInterface
#define IVectorView_PointerPoint_AddRef __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_AddRef
#define IVectorView_PointerPoint_Release __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_Release
#define IVectorView_PointerPoint_GetIids __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetIids
#define IVectorView_PointerPoint_GetRuntimeClassName __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName
#define IVectorView_PointerPoint_GetTrustLevel __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel
#define IVectorView_PointerPoint_GetAt __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetAt
#define IVectorView_PointerPoint_get_Size __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_get_Size
#define IVectorView_PointerPoint_IndexOf __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_IndexOf
#define IVectorView_PointerPoint_GetMany __FIVectorView_1_Windows__CUI__CInput__CPointerPoint_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::UI::Input::PointerPoint* > interface
 */
#ifndef ____FIVector_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CUI__CInput__CPointerPoint, 0xdfa655cf, 0xfde7, 0x5048, 0xb4,0xbf, 0xc9,0x09,0x23,0x1b,0x7e,0xdb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("dfa655cf-fde7-5048-b4bf-c909231b7edb")
                IVector<ABI::Windows::UI::Input::PointerPoint* > : IVector_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Input::PointerPoint*, ABI::Windows::UI::Input::IPointerPoint* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CUI__CInput__CPointerPoint, 0xdfa655cf, 0xfde7, 0x5048, 0xb4,0xbf, 0xc9,0x09,0x23,0x1b,0x7e,0xdb)
#endif
#else
typedef struct __FIVector_1_Windows__CUI__CInput__CPointerPointVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::UI::Input::PointerPoint* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 index,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        __FIVectorView_1_Windows__CUI__CInput__CPointerPoint **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 index,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 index,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CUI__CInput__CPointerPoint *This,
        UINT32 count,
        __x_ABI_CWindows_CUI_CInput_CIPointerPoint **items);

    END_INTERFACE
} __FIVector_1_Windows__CUI__CInput__CPointerPointVtbl;

interface __FIVector_1_Windows__CUI__CInput__CPointerPoint {
    CONST_VTBL __FIVector_1_Windows__CUI__CInput__CPointerPointVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::UI::Input::PointerPoint* > methods ***/
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CUI__CInput__CPointerPoint_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_QueryInterface(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CUI__CInput__CPointerPoint_AddRef(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CUI__CInput__CPointerPoint_Release(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetIids(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::UI::Input::PointerPoint* > methods ***/
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetAt(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 index,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_get_Size(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetView(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,__FIVectorView_1_Windows__CUI__CInput__CPointerPoint **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_IndexOf(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,__x_ABI_CWindows_CUI_CInput_CIPointerPoint *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_SetAt(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 index,__x_ABI_CWindows_CUI_CInput_CIPointerPoint *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_InsertAt(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 index,__x_ABI_CWindows_CUI_CInput_CIPointerPoint *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAt(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_Append(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,__x_ABI_CWindows_CUI_CInput_CIPointerPoint *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAtEnd(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_Clear(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetMany(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CUI__CInput__CPointerPoint_ReplaceAll(__FIVector_1_Windows__CUI__CInput__CPointerPoint* This,UINT32 count,__x_ABI_CWindows_CUI_CInput_CIPointerPoint **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_PointerPoint IID___FIVector_1_Windows__CUI__CInput__CPointerPoint
#define IVector_PointerPointVtbl __FIVector_1_Windows__CUI__CInput__CPointerPointVtbl
#define IVector_PointerPoint __FIVector_1_Windows__CUI__CInput__CPointerPoint
#define IVector_PointerPoint_QueryInterface __FIVector_1_Windows__CUI__CInput__CPointerPoint_QueryInterface
#define IVector_PointerPoint_AddRef __FIVector_1_Windows__CUI__CInput__CPointerPoint_AddRef
#define IVector_PointerPoint_Release __FIVector_1_Windows__CUI__CInput__CPointerPoint_Release
#define IVector_PointerPoint_GetIids __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetIids
#define IVector_PointerPoint_GetRuntimeClassName __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetRuntimeClassName
#define IVector_PointerPoint_GetTrustLevel __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetTrustLevel
#define IVector_PointerPoint_GetAt __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetAt
#define IVector_PointerPoint_get_Size __FIVector_1_Windows__CUI__CInput__CPointerPoint_get_Size
#define IVector_PointerPoint_GetView __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetView
#define IVector_PointerPoint_IndexOf __FIVector_1_Windows__CUI__CInput__CPointerPoint_IndexOf
#define IVector_PointerPoint_SetAt __FIVector_1_Windows__CUI__CInput__CPointerPoint_SetAt
#define IVector_PointerPoint_InsertAt __FIVector_1_Windows__CUI__CInput__CPointerPoint_InsertAt
#define IVector_PointerPoint_RemoveAt __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAt
#define IVector_PointerPoint_Append __FIVector_1_Windows__CUI__CInput__CPointerPoint_Append
#define IVector_PointerPoint_RemoveAtEnd __FIVector_1_Windows__CUI__CInput__CPointerPoint_RemoveAtEnd
#define IVector_PointerPoint_Clear __FIVector_1_Windows__CUI__CInput__CPointerPoint_Clear
#define IVector_PointerPoint_GetMany __FIVector_1_Windows__CUI__CInput__CPointerPoint_GetMany
#define IVector_PointerPoint_ReplaceAll __FIVector_1_Windows__CUI__CInput__CPointerPoint_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CUI__CInput__CPointerPoint_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable, 0x6368ae3d, 0x52d4, 0x5290, 0xb9,0x36, 0x71,0x7a,0x9a,0xcf,0x5b,0xea);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("6368ae3d-52d4-5290-b936-717a9acf5bea")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable, 0x6368ae3d, 0x52d4, 0x5290, 0xb9,0x36, 0x71,0x7a,0x9a,0xcf,0x5b,0xea)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_IInspectable IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable
#define ITypedEventHandler_CoreWindow_IInspectableVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectableVtbl
#define ITypedEventHandler_CoreWindow_IInspectable __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable
#define ITypedEventHandler_CoreWindow_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_QueryInterface
#define ITypedEventHandler_CoreWindow_IInspectable_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_AddRef
#define ITypedEventHandler_CoreWindow_IInspectable_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_Release
#define ITypedEventHandler_CoreWindow_IInspectable_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::AutomationProviderRequestedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs, 0x54db5c04, 0x81f7, 0x5f46, 0x9f,0xb8, 0xe4,0x9b,0xee,0xc7,0x0a,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("54db5c04-81f7-5f46-9fb8-e49beec70a24")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::AutomationProviderRequestedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::AutomationProviderRequestedEventArgs*, ABI::Windows::UI::Core::IAutomationProviderRequestedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs, 0x54db5c04, 0x81f7, 0x5f46, 0x9f,0xb8, 0xe4,0x9b,0xee,0xc7,0x0a,0x24)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::AutomationProviderRequestedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        __x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::AutomationProviderRequestedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::AutomationProviderRequestedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,__x_ABI_CWindows_CUI_CCore_CIAutomationProviderRequestedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_AutomationProviderRequestedEventArgs IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs
#define ITypedEventHandler_CoreWindow_AutomationProviderRequestedEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgsVtbl
#define ITypedEventHandler_CoreWindow_AutomationProviderRequestedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs
#define ITypedEventHandler_CoreWindow_AutomationProviderRequestedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_QueryInterface
#define ITypedEventHandler_CoreWindow_AutomationProviderRequestedEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_AddRef
#define ITypedEventHandler_CoreWindow_AutomationProviderRequestedEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_Release
#define ITypedEventHandler_CoreWindow_AutomationProviderRequestedEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CAutomationProviderRequestedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CharacterReceivedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs, 0x73f846a8, 0x0af6, 0x5872, 0x8f,0xb8, 0xae,0x2f,0x56,0xd8,0x55,0x3e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("73f846a8-0af6-5872-8fb8-ae2f56d8553e")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CharacterReceivedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CharacterReceivedEventArgs*, ABI::Windows::UI::Core::ICharacterReceivedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs, 0x73f846a8, 0x0af6, 0x5872, 0x8f,0xb8, 0xae,0x2f,0x56,0xd8,0x55,0x3e)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CharacterReceivedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        __x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CharacterReceivedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CharacterReceivedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,__x_ABI_CWindows_CUI_CCore_CICharacterReceivedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_CharacterReceivedEventArgs IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs
#define ITypedEventHandler_CoreWindow_CharacterReceivedEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgsVtbl
#define ITypedEventHandler_CoreWindow_CharacterReceivedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs
#define ITypedEventHandler_CoreWindow_CharacterReceivedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_QueryInterface
#define ITypedEventHandler_CoreWindow_CharacterReceivedEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_AddRef
#define ITypedEventHandler_CoreWindow_CharacterReceivedEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_Release
#define ITypedEventHandler_CoreWindow_CharacterReceivedEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCharacterReceivedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::ClosestInteractiveBoundsRequestedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs, 0x21a652d2, 0xbfe2, 0x5b2e, 0xa2,0xab, 0xca,0x45,0x25,0x3b,0xe8,0xb0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("21a652d2-bfe2-5b2e-a2ab-ca45253be8b0")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::ClosestInteractiveBoundsRequestedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::ClosestInteractiveBoundsRequestedEventArgs*, ABI::Windows::UI::Core::IClosestInteractiveBoundsRequestedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs, 0x21a652d2, 0xbfe2, 0x5b2e, 0xa2,0xab, 0xca,0x45,0x25,0x3b,0xe8,0xb0)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::ClosestInteractiveBoundsRequestedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        __x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::ClosestInteractiveBoundsRequestedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::ClosestInteractiveBoundsRequestedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,__x_ABI_CWindows_CUI_CCore_CIClosestInteractiveBoundsRequestedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_ClosestInteractiveBoundsRequestedEventArgs IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs
#define ITypedEventHandler_CoreWindow_ClosestInteractiveBoundsRequestedEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgsVtbl
#define ITypedEventHandler_CoreWindow_ClosestInteractiveBoundsRequestedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs
#define ITypedEventHandler_CoreWindow_ClosestInteractiveBoundsRequestedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_QueryInterface
#define ITypedEventHandler_CoreWindow_ClosestInteractiveBoundsRequestedEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_AddRef
#define ITypedEventHandler_CoreWindow_ClosestInteractiveBoundsRequestedEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_Release
#define ITypedEventHandler_CoreWindow_ClosestInteractiveBoundsRequestedEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CClosestInteractiveBoundsRequestedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CoreWindowEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs, 0xd08e4f66, 0x3457, 0x57f2, 0xba,0x0c, 0xcb,0x34,0x71,0x33,0xbd,0x15);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d08e4f66-3457-57f2-ba0c-cb347133bd15")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CoreWindowEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindowEventArgs*, ABI::Windows::UI::Core::ICoreWindowEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs, 0xd08e4f66, 0x3457, 0x57f2, 0xba,0x0c, 0xcb,0x34,0x71,0x33,0xbd,0x15)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CoreWindowEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        __x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CoreWindowEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::CoreWindowEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,__x_ABI_CWindows_CUI_CCore_CICoreWindowEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_CoreWindowEventArgs IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs
#define ITypedEventHandler_CoreWindow_CoreWindowEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgsVtbl
#define ITypedEventHandler_CoreWindow_CoreWindowEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs
#define ITypedEventHandler_CoreWindow_CoreWindowEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_QueryInterface
#define ITypedEventHandler_CoreWindow_CoreWindowEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_AddRef
#define ITypedEventHandler_CoreWindow_CoreWindowEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_Release
#define ITypedEventHandler_CoreWindow_CoreWindowEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CCoreWindowEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::InputEnabledEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs, 0xe230a64a, 0x506a, 0x59c3, 0xbb,0x61, 0x55,0x59,0xff,0x99,0x56,0x63);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("e230a64a-506a-59c3-bb61-5559ff995663")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::InputEnabledEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::InputEnabledEventArgs*, ABI::Windows::UI::Core::IInputEnabledEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs, 0xe230a64a, 0x506a, 0x59c3, 0xbb,0x61, 0x55,0x59,0xff,0x99,0x56,0x63)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::InputEnabledEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        __x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::InputEnabledEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::InputEnabledEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,__x_ABI_CWindows_CUI_CCore_CIInputEnabledEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_InputEnabledEventArgs IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs
#define ITypedEventHandler_CoreWindow_InputEnabledEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgsVtbl
#define ITypedEventHandler_CoreWindow_InputEnabledEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs
#define ITypedEventHandler_CoreWindow_InputEnabledEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_QueryInterface
#define ITypedEventHandler_CoreWindow_InputEnabledEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_AddRef
#define ITypedEventHandler_CoreWindow_InputEnabledEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_Release
#define ITypedEventHandler_CoreWindow_InputEnabledEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CInputEnabledEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::KeyEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs, 0xa3ec0774, 0x55ac, 0x5d61, 0x82,0x32, 0xb3,0x5c,0x5d,0x35,0xc9,0x3c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("a3ec0774-55ac-5d61-8232-b35c5d35c93c")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::KeyEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::KeyEventArgs*, ABI::Windows::UI::Core::IKeyEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs, 0xa3ec0774, 0x55ac, 0x5d61, 0x82,0x32, 0xb3,0x5c,0x5d,0x35,0xc9,0x3c)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::KeyEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        __x_ABI_CWindows_CUI_CCore_CIKeyEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::KeyEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::KeyEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,__x_ABI_CWindows_CUI_CCore_CIKeyEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_KeyEventArgs IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs
#define ITypedEventHandler_CoreWindow_KeyEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgsVtbl
#define ITypedEventHandler_CoreWindow_KeyEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs
#define ITypedEventHandler_CoreWindow_KeyEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_QueryInterface
#define ITypedEventHandler_CoreWindow_KeyEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_AddRef
#define ITypedEventHandler_CoreWindow_KeyEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_Release
#define ITypedEventHandler_CoreWindow_KeyEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CKeyEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs, 0x420e1bb6, 0xe99d, 0x5e64, 0x8e,0x25, 0x07,0x46,0x7e,0x3c,0xae,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("420e1bb6-e99d-5e64-8e25-07467e3cae9e")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::PointerEventArgs*, ABI::Windows::UI::Core::IPointerEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs, 0x420e1bb6, 0xe99d, 0x5e64, 0x8e,0x25, 0x07,0x46,0x7e,0x3c,0xae,0x9e)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        __x_ABI_CWindows_CUI_CCore_CIPointerEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::PointerEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,__x_ABI_CWindows_CUI_CCore_CIPointerEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_PointerEventArgs IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs
#define ITypedEventHandler_CoreWindow_PointerEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgsVtbl
#define ITypedEventHandler_CoreWindow_PointerEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs
#define ITypedEventHandler_CoreWindow_PointerEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_QueryInterface
#define ITypedEventHandler_CoreWindow_PointerEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_AddRef
#define ITypedEventHandler_CoreWindow_PointerEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_Release
#define ITypedEventHandler_CoreWindow_PointerEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CPointerEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::TouchHitTestingEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs, 0x197654c9, 0x0c47, 0x502b, 0x9a,0xa1, 0x0d,0xeb,0x03,0xed,0x97,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("197654c9-0c47-502b-9aa1-0deb03ed9702")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::TouchHitTestingEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::TouchHitTestingEventArgs*, ABI::Windows::UI::Core::ITouchHitTestingEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs, 0x197654c9, 0x0c47, 0x502b, 0x9a,0xa1, 0x0d,0xeb,0x03,0xed,0x97,0x02)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::TouchHitTestingEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        __x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::TouchHitTestingEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::TouchHitTestingEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,__x_ABI_CWindows_CUI_CCore_CITouchHitTestingEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_TouchHitTestingEventArgs IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs
#define ITypedEventHandler_CoreWindow_TouchHitTestingEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgsVtbl
#define ITypedEventHandler_CoreWindow_TouchHitTestingEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs
#define ITypedEventHandler_CoreWindow_TouchHitTestingEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_QueryInterface
#define ITypedEventHandler_CoreWindow_TouchHitTestingEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_AddRef
#define ITypedEventHandler_CoreWindow_TouchHitTestingEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_Release
#define ITypedEventHandler_CoreWindow_TouchHitTestingEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CTouchHitTestingEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::VisibilityChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs, 0x14b7f692, 0x5583, 0x52a1, 0xaa,0x42, 0xfc,0x18,0x43,0xc0,0xf7,0x48);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("14b7f692-5583-52a1-aa42-fc1843c0f748")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::VisibilityChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::VisibilityChangedEventArgs*, ABI::Windows::UI::Core::IVisibilityChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs, 0x14b7f692, 0x5583, 0x52a1, 0xaa,0x42, 0xfc,0x18,0x43,0xc0,0xf7,0x48)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::VisibilityChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        __x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::VisibilityChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::VisibilityChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,__x_ABI_CWindows_CUI_CCore_CIVisibilityChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_VisibilityChangedEventArgs IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs
#define ITypedEventHandler_CoreWindow_VisibilityChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgsVtbl
#define ITypedEventHandler_CoreWindow_VisibilityChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs
#define ITypedEventHandler_CoreWindow_VisibilityChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_QueryInterface
#define ITypedEventHandler_CoreWindow_VisibilityChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_AddRef
#define ITypedEventHandler_CoreWindow_VisibilityChangedEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_Release
#define ITypedEventHandler_CoreWindow_VisibilityChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CVisibilityChangedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowActivatedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs, 0x26a73b35, 0xa7f9, 0x52db, 0x88,0xd6, 0x15,0x72,0x6d,0xeb,0x25,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("26a73b35-a7f9-52db-88d6-15726deb2523")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowActivatedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::WindowActivatedEventArgs*, ABI::Windows::UI::Core::IWindowActivatedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs, 0x26a73b35, 0xa7f9, 0x52db, 0x88,0xd6, 0x15,0x72,0x6d,0xeb,0x25,0x23)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowActivatedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        __x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowActivatedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowActivatedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,__x_ABI_CWindows_CUI_CCore_CIWindowActivatedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_WindowActivatedEventArgs IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs
#define ITypedEventHandler_CoreWindow_WindowActivatedEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgsVtbl
#define ITypedEventHandler_CoreWindow_WindowActivatedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs
#define ITypedEventHandler_CoreWindow_WindowActivatedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_QueryInterface
#define ITypedEventHandler_CoreWindow_WindowActivatedEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_AddRef
#define ITypedEventHandler_CoreWindow_WindowActivatedEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_Release
#define ITypedEventHandler_CoreWindow_WindowActivatedEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowActivatedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowSizeChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs, 0x318dbb67, 0x4089, 0x5690, 0x90,0x40, 0x1d,0x45,0x4f,0xb2,0xf6,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("318dbb67-**************-1d454fb2f686")
            ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowSizeChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::CoreWindow*, ABI::Windows::UI::Core::ICoreWindow* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::Core::WindowSizeChangedEventArgs*, ABI::Windows::UI::Core::IWindowSizeChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs, 0x318dbb67, 0x4089, 0x5690, 0x90,0x40, 0x1d,0x45,0x4f,0xb2,0xf6,0x86)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowSizeChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs *This,
        __x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,
        __x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowSizeChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::Core::CoreWindow*,ABI::Windows::UI::Core::WindowSizeChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs* This,__x_ABI_CWindows_CUI_CCore_CICoreWindow *sender,__x_ABI_CWindows_CUI_CCore_CIWindowSizeChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_CoreWindow_WindowSizeChangedEventArgs IID___FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs
#define ITypedEventHandler_CoreWindow_WindowSizeChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgsVtbl
#define ITypedEventHandler_CoreWindow_WindowSizeChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs
#define ITypedEventHandler_CoreWindow_WindowSizeChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_QueryInterface
#define ITypedEventHandler_CoreWindow_WindowSizeChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_AddRef
#define ITypedEventHandler_CoreWindow_WindowSizeChangedEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_Release
#define ITypedEventHandler_CoreWindow_WindowSizeChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CCore__CCoreWindow_Windows__CUI__CCore__CWindowSizeChangedEventArgs_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_ui_core_h__ */
