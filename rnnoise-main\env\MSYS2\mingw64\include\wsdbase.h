/*** Autogenerated by WIDL 10.12 from include/wsdbase.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wsdbase_h__
#define __wsdbase_h__

/* Forward declarations */

#ifndef __IWSDAddress_FWD_DEFINED__
#define __IWSDAddress_FWD_DEFINED__
typedef interface IWSDAddress IWSDAddress;
#ifdef __cplusplus
interface IWSDAddress;
#endif /* __cplusplus */
#endif

#ifndef __IWSDTransportAddress_FWD_DEFINED__
#define __IWSDTransportAddress_FWD_DEFINED__
typedef interface IWSDTransportAddress IWSDTransportAddress;
#ifdef __cplusplus
interface IWSDTransportAddress;
#endif /* __cplusplus */
#endif

#ifndef __IWSDMessageParameters_FWD_DEFINED__
#define __IWSDMessageParameters_FWD_DEFINED__
typedef interface IWSDMessageParameters IWSDMessageParameters;
#ifdef __cplusplus
interface IWSDMessageParameters;
#endif /* __cplusplus */
#endif

#ifndef __IWSDUdpMessageParameters_FWD_DEFINED__
#define __IWSDUdpMessageParameters_FWD_DEFINED__
typedef interface IWSDUdpMessageParameters IWSDUdpMessageParameters;
#ifdef __cplusplus
interface IWSDUdpMessageParameters;
#endif /* __cplusplus */
#endif

#ifndef __IWSDUdpAddress_FWD_DEFINED__
#define __IWSDUdpAddress_FWD_DEFINED__
typedef interface IWSDUdpAddress IWSDUdpAddress;
#ifdef __cplusplus
interface IWSDUdpAddress;
#endif /* __cplusplus */
#endif

#ifndef __IWSDHttpMessageParameters_FWD_DEFINED__
#define __IWSDHttpMessageParameters_FWD_DEFINED__
typedef interface IWSDHttpMessageParameters IWSDHttpMessageParameters;
#ifdef __cplusplus
interface IWSDHttpMessageParameters;
#endif /* __cplusplus */
#endif

#ifndef __IWSDHttpAddress_FWD_DEFINED__
#define __IWSDHttpAddress_FWD_DEFINED__
typedef interface IWSDHttpAddress IWSDHttpAddress;
#ifdef __cplusplus
interface IWSDHttpAddress;
#endif /* __cplusplus */
#endif

#ifndef __IWSDSSLClientCertificate_FWD_DEFINED__
#define __IWSDSSLClientCertificate_FWD_DEFINED__
typedef interface IWSDSSLClientCertificate IWSDSSLClientCertificate;
#ifdef __cplusplus
interface IWSDSSLClientCertificate;
#endif /* __cplusplus */
#endif

#ifndef __IWSDHttpAuthParameters_FWD_DEFINED__
#define __IWSDHttpAuthParameters_FWD_DEFINED__
typedef interface IWSDHttpAuthParameters IWSDHttpAuthParameters;
#ifdef __cplusplus
interface IWSDHttpAuthParameters;
#endif /* __cplusplus */
#endif

#ifndef __IWSDSignatureProperty_FWD_DEFINED__
#define __IWSDSignatureProperty_FWD_DEFINED__
typedef interface IWSDSignatureProperty IWSDSignatureProperty;
#ifdef __cplusplus
interface IWSDSignatureProperty;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <objidl.h>
#include <wincrypt.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

#ifndef __IWSDAddress_FWD_DEFINED__
#define __IWSDAddress_FWD_DEFINED__
typedef interface IWSDAddress IWSDAddress;
#ifdef __cplusplus
interface IWSDAddress;
#endif /* __cplusplus */
#endif

#ifndef __IWSDMessageParameters_FWD_DEFINED__
#define __IWSDMessageParameters_FWD_DEFINED__
typedef interface IWSDMessageParameters IWSDMessageParameters;
#ifdef __cplusplus
interface IWSDMessageParameters;
#endif /* __cplusplus */
#endif

#ifndef __IWSDUdpAddress_FWD_DEFINED__
#define __IWSDUdpAddress_FWD_DEFINED__
typedef interface IWSDUdpAddress IWSDUdpAddress;
#ifdef __cplusplus
interface IWSDUdpAddress;
#endif /* __cplusplus */
#endif

#ifndef __IWSDUdpMessageParameters_FWD_DEFINED__
#define __IWSDUdpMessageParameters_FWD_DEFINED__
typedef interface IWSDUdpMessageParameters IWSDUdpMessageParameters;
#ifdef __cplusplus
interface IWSDUdpMessageParameters;
#endif /* __cplusplus */
#endif

#ifndef __IWSDHttpAddress_FWD_DEFINED__
#define __IWSDHttpAddress_FWD_DEFINED__
typedef interface IWSDHttpAddress IWSDHttpAddress;
#ifdef __cplusplus
interface IWSDHttpAddress;
#endif /* __cplusplus */
#endif

#ifndef __IWSDHttpMessageParameters_FWD_DEFINED__
#define __IWSDHttpMessageParameters_FWD_DEFINED__
typedef interface IWSDHttpMessageParameters IWSDHttpMessageParameters;
#ifdef __cplusplus
interface IWSDHttpMessageParameters;
#endif /* __cplusplus */
#endif


#if WINVER >= 0x601
typedef enum __WIDL_wsdbase_generated_name_00000015 {
    WSD_CONFIG_MAX_INBOUND_MESSAGE_SIZE = 1,
    WSD_CONFIG_MAX_OUTBOUND_MESSAGE_SIZE = 2,
    WSD_SECURITY_SSL_CERT_FOR_CLIENT_AUTH = 3,
    WSD_SECURITY_SSL_SERVER_CERT_VALIDATION = 4,
    WSD_SECURITY_SSL_CLIENT_CERT_VALIDATION = 5,
    WSD_SECURITY_SSL_NEGOTIATE_CLIENT_CERT = 6,
    WSD_SECURITY_COMPACTSIG_SIGNING_CERT = 7,
    WSD_SECURITY_COMPACTSIG_VALIDATION = 8,
    WSD_CONFIG_HOSTING_ADDRESSES = 9,
    WSD_CONFIG_DEVICE_ADDRESSES = 10,
    WSD_SECURITY_REQUIRE_HTTP_CLIENT_AUTH = 11,
    WSD_SECURITY_REQUIRE_CLIENT_CERT_OR_HTTP_CLIENT_AUTH = 12,
    WSD_SECURITY_USE_HTTP_CLIENT_AUTH = 13
} WSD_CONFIG_PARAM_TYPE;
typedef struct _WSD_CONFIG_PARAM {
    WSD_CONFIG_PARAM_TYPE configParamType;
    PVOID pConfigData;
    DWORD dwConfigDataSize;
} WSD_CONFIG_PARAM;
typedef struct _WSD_CONFIG_PARAM *PWSD_CONFIG_PARAM;
typedef struct _WSD_SECURITY_CERT_VALIDATION_V1 {
    PCCERT_CONTEXT *certMatchArray;
    DWORD dwCertMatchArrayCount;
    HCERTSTORE hCertMatchStore;
    HCERTSTORE hCertIssuerStore;
    DWORD dwCertCheckOptions;
} WSD_SECURITY_CERT_VALIDATION_V1;
#if _WIN32_WINNT >= 0x602
typedef struct _WSD_SECURITY_CERT_VALIDATION {
    PCCERT_CONTEXT *certMatchArray;
    DWORD dwCertMatchArrayCount;
    HCERTSTORE hCertMatchStore;
    HCERTSTORE hCertIssuerStore;
    DWORD dwCertCheckOptions;
    LPCWSTR pszCNGHashAlgId;
    BYTE *pbCertHash;
    DWORD dwCertHashSize;
} WSD_SECURITY_CERT_VALIDATION;
#else
typedef WSD_SECURITY_CERT_VALIDATION_V1 WSD_SECURITY_CERT_VALIDATION;
#endif

typedef WSD_SECURITY_CERT_VALIDATION *PWSD_SECURITY_CERT_VALIDATION;
typedef struct _WSD_SECURITY_SIGNATURE_VALIDATION {
    PCCERT_CONTEXT *signingCertArray;
    DWORD dwSigningCertArrayCount;
    HCERTSTORE hSigningCertStore;
    DWORD dwFlags;
} WSD_SECURITY_SIGNATURE_VALIDATION;
typedef struct _WSD_SECURITY_SIGNATURE_VALIDATION *PWSD_SECURITY_SIGNATURE_VALIDATION;
typedef DWORD WSD_SECURITY_HTTP_AUTH_SCHEMES;
typedef DWORD *PWSD_SECURITY_HTTP_AUTH_SCHEMES;

#define WSDAPI_SSL_CERT_APPLY_DEFAULT_CHECKS 0x0
#define WSDAPI_SSL_CERT_IGNORE_REVOCATION 0x1
#define WSDAPI_SSL_CERT_IGNORE_EXPIRY 0x2
#define WSDAPI_SSL_CERT_IGNORE_WRONG_USAGE 0x4
#define WSDAPI_SSL_CERT_IGNORE_UNKNOWN_CA 0x8
#define WSDAPI_SSL_CERT_IGNORE_INVALID_CN 0x10

#define WSDAPI_COMPACTSIG_ACCEPT_ALL_MESSAGES 0x1

#define WSD_SECURITY_HTTP_AUTH_SCHEME_NEGOTIATE 0x1
#define WSD_SECURITY_HTTP_AUTH_SCHEME_NTLM 0x2

typedef struct _WSD_CONFIG_ADDRESSES {
    IWSDAddress **addresses;
    DWORD dwAddressCount;
} WSD_CONFIG_ADDRESSES;
typedef struct _WSD_CONFIG_ADDRESSES *PWSD_CONFIG_ADDRESSES;
#endif
#define WSDAPI_ADDRESSFAMILY_IPV4 1
#define WSDAPI_ADDRESSFAMILY_IPV6 2
/*****************************************************************************
 * IWSDAddress interface
 */
#ifndef __IWSDAddress_INTERFACE_DEFINED__
#define __IWSDAddress_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDAddress, 0xb9574c6c, 0x12a6, 0x4f74, 0x93,0xa1, 0x33,0x18,0xff,0x60,0x57,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("b9574c6c-12a6-4f74-93a1-3318ff605759")
IWSDAddress : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Serialize(
        LPWSTR pszBuffer,
        DWORD cchLength,
        WINBOOL fSafe) = 0;

    virtual HRESULT STDMETHODCALLTYPE Deserialize(
        LPCWSTR pszBuffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDAddress, 0xb9574c6c, 0x12a6, 0x4f74, 0x93,0xa1, 0x33,0x18,0xff,0x60,0x57,0x59)
#endif
#else
typedef struct IWSDAddressVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDAddress *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDAddress *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDAddress *This);

    /*** IWSDAddress methods ***/
    HRESULT (STDMETHODCALLTYPE *Serialize)(
        IWSDAddress *This,
        LPWSTR pszBuffer,
        DWORD cchLength,
        WINBOOL fSafe);

    HRESULT (STDMETHODCALLTYPE *Deserialize)(
        IWSDAddress *This,
        LPCWSTR pszBuffer);

    END_INTERFACE
} IWSDAddressVtbl;

interface IWSDAddress {
    CONST_VTBL IWSDAddressVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDAddress_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDAddress_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDAddress_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDAddress methods ***/
#define IWSDAddress_Serialize(This,pszBuffer,cchLength,fSafe) (This)->lpVtbl->Serialize(This,pszBuffer,cchLength,fSafe)
#define IWSDAddress_Deserialize(This,pszBuffer) (This)->lpVtbl->Deserialize(This,pszBuffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDAddress_QueryInterface(IWSDAddress* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDAddress_AddRef(IWSDAddress* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDAddress_Release(IWSDAddress* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDAddress methods ***/
static inline HRESULT IWSDAddress_Serialize(IWSDAddress* This,LPWSTR pszBuffer,DWORD cchLength,WINBOOL fSafe) {
    return This->lpVtbl->Serialize(This,pszBuffer,cchLength,fSafe);
}
static inline HRESULT IWSDAddress_Deserialize(IWSDAddress* This,LPCWSTR pszBuffer) {
    return This->lpVtbl->Deserialize(This,pszBuffer);
}
#endif
#endif

#endif


#endif  /* __IWSDAddress_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDTransportAddress interface
 */
#ifndef __IWSDTransportAddress_INTERFACE_DEFINED__
#define __IWSDTransportAddress_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDTransportAddress, 0x70d23498, 0x4ee6, 0x4340, 0xa3,0xdf, 0xd8,0x45,0xd2,0x23,0x54,0x67);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("70d23498-4ee6-4340-a3df-d845d2235467")
IWSDTransportAddress : public IWSDAddress
{
    virtual HRESULT STDMETHODCALLTYPE GetPort(
        WORD *pwPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPort(
        WORD wPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransportAddress(
        LPCWSTR *ppszAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTransportAddressEx(
        WINBOOL fSafe,
        LPCWSTR *ppszAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTransportAddress(
        LPCWSTR pszAddress) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDTransportAddress, 0x70d23498, 0x4ee6, 0x4340, 0xa3,0xdf, 0xd8,0x45,0xd2,0x23,0x54,0x67)
#endif
#else
typedef struct IWSDTransportAddressVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDTransportAddress *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDTransportAddress *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDTransportAddress *This);

    /*** IWSDAddress methods ***/
    HRESULT (STDMETHODCALLTYPE *Serialize)(
        IWSDTransportAddress *This,
        LPWSTR pszBuffer,
        DWORD cchLength,
        WINBOOL fSafe);

    HRESULT (STDMETHODCALLTYPE *Deserialize)(
        IWSDTransportAddress *This,
        LPCWSTR pszBuffer);

    /*** IWSDTransportAddress methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPort)(
        IWSDTransportAddress *This,
        WORD *pwPort);

    HRESULT (STDMETHODCALLTYPE *SetPort)(
        IWSDTransportAddress *This,
        WORD wPort);

    HRESULT (STDMETHODCALLTYPE *GetTransportAddress)(
        IWSDTransportAddress *This,
        LPCWSTR *ppszAddress);

    HRESULT (STDMETHODCALLTYPE *GetTransportAddressEx)(
        IWSDTransportAddress *This,
        WINBOOL fSafe,
        LPCWSTR *ppszAddress);

    HRESULT (STDMETHODCALLTYPE *SetTransportAddress)(
        IWSDTransportAddress *This,
        LPCWSTR pszAddress);

    END_INTERFACE
} IWSDTransportAddressVtbl;

interface IWSDTransportAddress {
    CONST_VTBL IWSDTransportAddressVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDTransportAddress_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDTransportAddress_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDTransportAddress_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDAddress methods ***/
#define IWSDTransportAddress_Serialize(This,pszBuffer,cchLength,fSafe) (This)->lpVtbl->Serialize(This,pszBuffer,cchLength,fSafe)
#define IWSDTransportAddress_Deserialize(This,pszBuffer) (This)->lpVtbl->Deserialize(This,pszBuffer)
/*** IWSDTransportAddress methods ***/
#define IWSDTransportAddress_GetPort(This,pwPort) (This)->lpVtbl->GetPort(This,pwPort)
#define IWSDTransportAddress_SetPort(This,wPort) (This)->lpVtbl->SetPort(This,wPort)
#define IWSDTransportAddress_GetTransportAddress(This,ppszAddress) (This)->lpVtbl->GetTransportAddress(This,ppszAddress)
#define IWSDTransportAddress_GetTransportAddressEx(This,fSafe,ppszAddress) (This)->lpVtbl->GetTransportAddressEx(This,fSafe,ppszAddress)
#define IWSDTransportAddress_SetTransportAddress(This,pszAddress) (This)->lpVtbl->SetTransportAddress(This,pszAddress)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDTransportAddress_QueryInterface(IWSDTransportAddress* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDTransportAddress_AddRef(IWSDTransportAddress* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDTransportAddress_Release(IWSDTransportAddress* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDAddress methods ***/
static inline HRESULT IWSDTransportAddress_Serialize(IWSDTransportAddress* This,LPWSTR pszBuffer,DWORD cchLength,WINBOOL fSafe) {
    return This->lpVtbl->Serialize(This,pszBuffer,cchLength,fSafe);
}
static inline HRESULT IWSDTransportAddress_Deserialize(IWSDTransportAddress* This,LPCWSTR pszBuffer) {
    return This->lpVtbl->Deserialize(This,pszBuffer);
}
/*** IWSDTransportAddress methods ***/
static inline HRESULT IWSDTransportAddress_GetPort(IWSDTransportAddress* This,WORD *pwPort) {
    return This->lpVtbl->GetPort(This,pwPort);
}
static inline HRESULT IWSDTransportAddress_SetPort(IWSDTransportAddress* This,WORD wPort) {
    return This->lpVtbl->SetPort(This,wPort);
}
static inline HRESULT IWSDTransportAddress_GetTransportAddress(IWSDTransportAddress* This,LPCWSTR *ppszAddress) {
    return This->lpVtbl->GetTransportAddress(This,ppszAddress);
}
static inline HRESULT IWSDTransportAddress_GetTransportAddressEx(IWSDTransportAddress* This,WINBOOL fSafe,LPCWSTR *ppszAddress) {
    return This->lpVtbl->GetTransportAddressEx(This,fSafe,ppszAddress);
}
static inline HRESULT IWSDTransportAddress_SetTransportAddress(IWSDTransportAddress* This,LPCWSTR pszAddress) {
    return This->lpVtbl->SetTransportAddress(This,pszAddress);
}
#endif
#endif

#endif


#endif  /* __IWSDTransportAddress_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDMessageParameters interface
 */
#ifndef __IWSDMessageParameters_INTERFACE_DEFINED__
#define __IWSDMessageParameters_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDMessageParameters, 0x1fafe8a2, 0xe6fc, 0x4b80, 0xb6,0xcf, 0xb7,0xd4,0x5c,0x41,0x6d,0x7c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1fafe8a2-e6fc-4b80-b6cf-b7d45c416d7c")
IWSDMessageParameters : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetLocalAddress(
        IWSDAddress **ppAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLocalAddress(
        IWSDAddress *pAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRemoteAddress(
        IWSDAddress **ppAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRemoteAddress(
        IWSDAddress *pAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLowerParameters(
        IWSDMessageParameters **ppTxParams) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDMessageParameters, 0x1fafe8a2, 0xe6fc, 0x4b80, 0xb6,0xcf, 0xb7,0xd4,0x5c,0x41,0x6d,0x7c)
#endif
#else
typedef struct IWSDMessageParametersVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDMessageParameters *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDMessageParameters *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDMessageParameters *This);

    /*** IWSDMessageParameters methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLocalAddress)(
        IWSDMessageParameters *This,
        IWSDAddress **ppAddress);

    HRESULT (STDMETHODCALLTYPE *SetLocalAddress)(
        IWSDMessageParameters *This,
        IWSDAddress *pAddress);

    HRESULT (STDMETHODCALLTYPE *GetRemoteAddress)(
        IWSDMessageParameters *This,
        IWSDAddress **ppAddress);

    HRESULT (STDMETHODCALLTYPE *SetRemoteAddress)(
        IWSDMessageParameters *This,
        IWSDAddress *pAddress);

    HRESULT (STDMETHODCALLTYPE *GetLowerParameters)(
        IWSDMessageParameters *This,
        IWSDMessageParameters **ppTxParams);

    END_INTERFACE
} IWSDMessageParametersVtbl;

interface IWSDMessageParameters {
    CONST_VTBL IWSDMessageParametersVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDMessageParameters_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDMessageParameters_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDMessageParameters_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDMessageParameters methods ***/
#define IWSDMessageParameters_GetLocalAddress(This,ppAddress) (This)->lpVtbl->GetLocalAddress(This,ppAddress)
#define IWSDMessageParameters_SetLocalAddress(This,pAddress) (This)->lpVtbl->SetLocalAddress(This,pAddress)
#define IWSDMessageParameters_GetRemoteAddress(This,ppAddress) (This)->lpVtbl->GetRemoteAddress(This,ppAddress)
#define IWSDMessageParameters_SetRemoteAddress(This,pAddress) (This)->lpVtbl->SetRemoteAddress(This,pAddress)
#define IWSDMessageParameters_GetLowerParameters(This,ppTxParams) (This)->lpVtbl->GetLowerParameters(This,ppTxParams)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDMessageParameters_QueryInterface(IWSDMessageParameters* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDMessageParameters_AddRef(IWSDMessageParameters* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDMessageParameters_Release(IWSDMessageParameters* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDMessageParameters methods ***/
static inline HRESULT IWSDMessageParameters_GetLocalAddress(IWSDMessageParameters* This,IWSDAddress **ppAddress) {
    return This->lpVtbl->GetLocalAddress(This,ppAddress);
}
static inline HRESULT IWSDMessageParameters_SetLocalAddress(IWSDMessageParameters* This,IWSDAddress *pAddress) {
    return This->lpVtbl->SetLocalAddress(This,pAddress);
}
static inline HRESULT IWSDMessageParameters_GetRemoteAddress(IWSDMessageParameters* This,IWSDAddress **ppAddress) {
    return This->lpVtbl->GetRemoteAddress(This,ppAddress);
}
static inline HRESULT IWSDMessageParameters_SetRemoteAddress(IWSDMessageParameters* This,IWSDAddress *pAddress) {
    return This->lpVtbl->SetRemoteAddress(This,pAddress);
}
static inline HRESULT IWSDMessageParameters_GetLowerParameters(IWSDMessageParameters* This,IWSDMessageParameters **ppTxParams) {
    return This->lpVtbl->GetLowerParameters(This,ppTxParams);
}
#endif
#endif

#endif


#endif  /* __IWSDMessageParameters_INTERFACE_DEFINED__ */

HRESULT WINAPI
WSDCreateUdpMessageParameters(
IWSDUdpMessageParameters** ppTxParams);
typedef struct _WSDUdpRetransmitParams {
    ULONG ulSendDelay;
    ULONG ulRepeat;
    ULONG ulRepeatMinDelay;
    ULONG ulRepeatMaxDelay;
    ULONG ulRepeatUpperDelay;
} WSDUdpRetransmitParams;
/*****************************************************************************
 * IWSDUdpMessageParameters interface
 */
#ifndef __IWSDUdpMessageParameters_INTERFACE_DEFINED__
#define __IWSDUdpMessageParameters_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDUdpMessageParameters, 0x9934149f, 0x8f0c, 0x447b, 0xaa,0x0b, 0x73,0x12,0x4b,0x0c,0xa7,0xf0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9934149f-8f0c-447b-aa0b-73124b0ca7f0")
IWSDUdpMessageParameters : public IWSDMessageParameters
{
    virtual HRESULT STDMETHODCALLTYPE SetRetransmitParams(
        const WSDUdpRetransmitParams *pParams) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRetransmitParams(
        WSDUdpRetransmitParams *pParams) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDUdpMessageParameters, 0x9934149f, 0x8f0c, 0x447b, 0xaa,0x0b, 0x73,0x12,0x4b,0x0c,0xa7,0xf0)
#endif
#else
typedef struct IWSDUdpMessageParametersVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDUdpMessageParameters *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDUdpMessageParameters *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDUdpMessageParameters *This);

    /*** IWSDMessageParameters methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLocalAddress)(
        IWSDUdpMessageParameters *This,
        IWSDAddress **ppAddress);

    HRESULT (STDMETHODCALLTYPE *SetLocalAddress)(
        IWSDUdpMessageParameters *This,
        IWSDAddress *pAddress);

    HRESULT (STDMETHODCALLTYPE *GetRemoteAddress)(
        IWSDUdpMessageParameters *This,
        IWSDAddress **ppAddress);

    HRESULT (STDMETHODCALLTYPE *SetRemoteAddress)(
        IWSDUdpMessageParameters *This,
        IWSDAddress *pAddress);

    HRESULT (STDMETHODCALLTYPE *GetLowerParameters)(
        IWSDUdpMessageParameters *This,
        IWSDMessageParameters **ppTxParams);

    /*** IWSDUdpMessageParameters methods ***/
    HRESULT (STDMETHODCALLTYPE *SetRetransmitParams)(
        IWSDUdpMessageParameters *This,
        const WSDUdpRetransmitParams *pParams);

    HRESULT (STDMETHODCALLTYPE *GetRetransmitParams)(
        IWSDUdpMessageParameters *This,
        WSDUdpRetransmitParams *pParams);

    END_INTERFACE
} IWSDUdpMessageParametersVtbl;

interface IWSDUdpMessageParameters {
    CONST_VTBL IWSDUdpMessageParametersVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDUdpMessageParameters_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDUdpMessageParameters_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDUdpMessageParameters_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDMessageParameters methods ***/
#define IWSDUdpMessageParameters_GetLocalAddress(This,ppAddress) (This)->lpVtbl->GetLocalAddress(This,ppAddress)
#define IWSDUdpMessageParameters_SetLocalAddress(This,pAddress) (This)->lpVtbl->SetLocalAddress(This,pAddress)
#define IWSDUdpMessageParameters_GetRemoteAddress(This,ppAddress) (This)->lpVtbl->GetRemoteAddress(This,ppAddress)
#define IWSDUdpMessageParameters_SetRemoteAddress(This,pAddress) (This)->lpVtbl->SetRemoteAddress(This,pAddress)
#define IWSDUdpMessageParameters_GetLowerParameters(This,ppTxParams) (This)->lpVtbl->GetLowerParameters(This,ppTxParams)
/*** IWSDUdpMessageParameters methods ***/
#define IWSDUdpMessageParameters_SetRetransmitParams(This,pParams) (This)->lpVtbl->SetRetransmitParams(This,pParams)
#define IWSDUdpMessageParameters_GetRetransmitParams(This,pParams) (This)->lpVtbl->GetRetransmitParams(This,pParams)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDUdpMessageParameters_QueryInterface(IWSDUdpMessageParameters* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDUdpMessageParameters_AddRef(IWSDUdpMessageParameters* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDUdpMessageParameters_Release(IWSDUdpMessageParameters* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDMessageParameters methods ***/
static inline HRESULT IWSDUdpMessageParameters_GetLocalAddress(IWSDUdpMessageParameters* This,IWSDAddress **ppAddress) {
    return This->lpVtbl->GetLocalAddress(This,ppAddress);
}
static inline HRESULT IWSDUdpMessageParameters_SetLocalAddress(IWSDUdpMessageParameters* This,IWSDAddress *pAddress) {
    return This->lpVtbl->SetLocalAddress(This,pAddress);
}
static inline HRESULT IWSDUdpMessageParameters_GetRemoteAddress(IWSDUdpMessageParameters* This,IWSDAddress **ppAddress) {
    return This->lpVtbl->GetRemoteAddress(This,ppAddress);
}
static inline HRESULT IWSDUdpMessageParameters_SetRemoteAddress(IWSDUdpMessageParameters* This,IWSDAddress *pAddress) {
    return This->lpVtbl->SetRemoteAddress(This,pAddress);
}
static inline HRESULT IWSDUdpMessageParameters_GetLowerParameters(IWSDUdpMessageParameters* This,IWSDMessageParameters **ppTxParams) {
    return This->lpVtbl->GetLowerParameters(This,ppTxParams);
}
/*** IWSDUdpMessageParameters methods ***/
static inline HRESULT IWSDUdpMessageParameters_SetRetransmitParams(IWSDUdpMessageParameters* This,const WSDUdpRetransmitParams *pParams) {
    return This->lpVtbl->SetRetransmitParams(This,pParams);
}
static inline HRESULT IWSDUdpMessageParameters_GetRetransmitParams(IWSDUdpMessageParameters* This,WSDUdpRetransmitParams *pParams) {
    return This->lpVtbl->GetRetransmitParams(This,pParams);
}
#endif
#endif

#endif


#endif  /* __IWSDUdpMessageParameters_INTERFACE_DEFINED__ */

HRESULT WINAPI
WSDCreateUdpAddress(
 IWSDUdpAddress** ppAddress);
#if 1

#ifndef __CSADDR_DEFINED__
struct SOCKADDR_STORAGE;
#endif
#else
typedef void SOCKADDR_STORAGE;
#endif
typedef enum _WSDUdpMessageType {
    ONE_WAY = 0,
    TWO_WAY = 1
} WSDUdpMessageType;
/*****************************************************************************
 * IWSDUdpAddress interface
 */
#ifndef __IWSDUdpAddress_INTERFACE_DEFINED__
#define __IWSDUdpAddress_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDUdpAddress, 0x74d6124a, 0xa441, 0x4f78, 0xa1,0xeb, 0x97,0xa8,0xd1,0x99,0x68,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("74d6124a-a441-4f78-a1eb-97a8d1996893")
IWSDUdpAddress : public IWSDTransportAddress
{
    virtual HRESULT STDMETHODCALLTYPE SetSockaddr(
        const SOCKADDR_STORAGE *pSockAddr) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSockaddr(
        SOCKADDR_STORAGE *pSockAddr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetExclusive(
        WINBOOL fExclusive) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExclusive(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMessageType(
        WSDUdpMessageType messageType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMessageType(
        WSDUdpMessageType *pMessageType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTTL(
        DWORD dwTTL) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTTL(
        DWORD *pdwTTL) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAlias(
        const GUID *pAlias) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAlias(
        GUID *pAlias) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDUdpAddress, 0x74d6124a, 0xa441, 0x4f78, 0xa1,0xeb, 0x97,0xa8,0xd1,0x99,0x68,0x93)
#endif
#else
typedef struct IWSDUdpAddressVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDUdpAddress *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDUdpAddress *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDUdpAddress *This);

    /*** IWSDAddress methods ***/
    HRESULT (STDMETHODCALLTYPE *Serialize)(
        IWSDUdpAddress *This,
        LPWSTR pszBuffer,
        DWORD cchLength,
        WINBOOL fSafe);

    HRESULT (STDMETHODCALLTYPE *Deserialize)(
        IWSDUdpAddress *This,
        LPCWSTR pszBuffer);

    /*** IWSDTransportAddress methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPort)(
        IWSDUdpAddress *This,
        WORD *pwPort);

    HRESULT (STDMETHODCALLTYPE *SetPort)(
        IWSDUdpAddress *This,
        WORD wPort);

    HRESULT (STDMETHODCALLTYPE *GetTransportAddress)(
        IWSDUdpAddress *This,
        LPCWSTR *ppszAddress);

    HRESULT (STDMETHODCALLTYPE *GetTransportAddressEx)(
        IWSDUdpAddress *This,
        WINBOOL fSafe,
        LPCWSTR *ppszAddress);

    HRESULT (STDMETHODCALLTYPE *SetTransportAddress)(
        IWSDUdpAddress *This,
        LPCWSTR pszAddress);

    /*** IWSDUdpAddress methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSockaddr)(
        IWSDUdpAddress *This,
        const SOCKADDR_STORAGE *pSockAddr);

    HRESULT (STDMETHODCALLTYPE *GetSockaddr)(
        IWSDUdpAddress *This,
        SOCKADDR_STORAGE *pSockAddr);

    HRESULT (STDMETHODCALLTYPE *SetExclusive)(
        IWSDUdpAddress *This,
        WINBOOL fExclusive);

    HRESULT (STDMETHODCALLTYPE *GetExclusive)(
        IWSDUdpAddress *This);

    HRESULT (STDMETHODCALLTYPE *SetMessageType)(
        IWSDUdpAddress *This,
        WSDUdpMessageType messageType);

    HRESULT (STDMETHODCALLTYPE *GetMessageType)(
        IWSDUdpAddress *This,
        WSDUdpMessageType *pMessageType);

    HRESULT (STDMETHODCALLTYPE *SetTTL)(
        IWSDUdpAddress *This,
        DWORD dwTTL);

    HRESULT (STDMETHODCALLTYPE *GetTTL)(
        IWSDUdpAddress *This,
        DWORD *pdwTTL);

    HRESULT (STDMETHODCALLTYPE *SetAlias)(
        IWSDUdpAddress *This,
        const GUID *pAlias);

    HRESULT (STDMETHODCALLTYPE *GetAlias)(
        IWSDUdpAddress *This,
        GUID *pAlias);

    END_INTERFACE
} IWSDUdpAddressVtbl;

interface IWSDUdpAddress {
    CONST_VTBL IWSDUdpAddressVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDUdpAddress_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDUdpAddress_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDUdpAddress_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDAddress methods ***/
#define IWSDUdpAddress_Serialize(This,pszBuffer,cchLength,fSafe) (This)->lpVtbl->Serialize(This,pszBuffer,cchLength,fSafe)
#define IWSDUdpAddress_Deserialize(This,pszBuffer) (This)->lpVtbl->Deserialize(This,pszBuffer)
/*** IWSDTransportAddress methods ***/
#define IWSDUdpAddress_GetPort(This,pwPort) (This)->lpVtbl->GetPort(This,pwPort)
#define IWSDUdpAddress_SetPort(This,wPort) (This)->lpVtbl->SetPort(This,wPort)
#define IWSDUdpAddress_GetTransportAddress(This,ppszAddress) (This)->lpVtbl->GetTransportAddress(This,ppszAddress)
#define IWSDUdpAddress_GetTransportAddressEx(This,fSafe,ppszAddress) (This)->lpVtbl->GetTransportAddressEx(This,fSafe,ppszAddress)
#define IWSDUdpAddress_SetTransportAddress(This,pszAddress) (This)->lpVtbl->SetTransportAddress(This,pszAddress)
/*** IWSDUdpAddress methods ***/
#define IWSDUdpAddress_SetSockaddr(This,pSockAddr) (This)->lpVtbl->SetSockaddr(This,pSockAddr)
#define IWSDUdpAddress_GetSockaddr(This,pSockAddr) (This)->lpVtbl->GetSockaddr(This,pSockAddr)
#define IWSDUdpAddress_SetExclusive(This,fExclusive) (This)->lpVtbl->SetExclusive(This,fExclusive)
#define IWSDUdpAddress_GetExclusive(This) (This)->lpVtbl->GetExclusive(This)
#define IWSDUdpAddress_SetMessageType(This,messageType) (This)->lpVtbl->SetMessageType(This,messageType)
#define IWSDUdpAddress_GetMessageType(This,pMessageType) (This)->lpVtbl->GetMessageType(This,pMessageType)
#define IWSDUdpAddress_SetTTL(This,dwTTL) (This)->lpVtbl->SetTTL(This,dwTTL)
#define IWSDUdpAddress_GetTTL(This,pdwTTL) (This)->lpVtbl->GetTTL(This,pdwTTL)
#define IWSDUdpAddress_SetAlias(This,pAlias) (This)->lpVtbl->SetAlias(This,pAlias)
#define IWSDUdpAddress_GetAlias(This,pAlias) (This)->lpVtbl->GetAlias(This,pAlias)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDUdpAddress_QueryInterface(IWSDUdpAddress* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDUdpAddress_AddRef(IWSDUdpAddress* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDUdpAddress_Release(IWSDUdpAddress* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDAddress methods ***/
static inline HRESULT IWSDUdpAddress_Serialize(IWSDUdpAddress* This,LPWSTR pszBuffer,DWORD cchLength,WINBOOL fSafe) {
    return This->lpVtbl->Serialize(This,pszBuffer,cchLength,fSafe);
}
static inline HRESULT IWSDUdpAddress_Deserialize(IWSDUdpAddress* This,LPCWSTR pszBuffer) {
    return This->lpVtbl->Deserialize(This,pszBuffer);
}
/*** IWSDTransportAddress methods ***/
static inline HRESULT IWSDUdpAddress_GetPort(IWSDUdpAddress* This,WORD *pwPort) {
    return This->lpVtbl->GetPort(This,pwPort);
}
static inline HRESULT IWSDUdpAddress_SetPort(IWSDUdpAddress* This,WORD wPort) {
    return This->lpVtbl->SetPort(This,wPort);
}
static inline HRESULT IWSDUdpAddress_GetTransportAddress(IWSDUdpAddress* This,LPCWSTR *ppszAddress) {
    return This->lpVtbl->GetTransportAddress(This,ppszAddress);
}
static inline HRESULT IWSDUdpAddress_GetTransportAddressEx(IWSDUdpAddress* This,WINBOOL fSafe,LPCWSTR *ppszAddress) {
    return This->lpVtbl->GetTransportAddressEx(This,fSafe,ppszAddress);
}
static inline HRESULT IWSDUdpAddress_SetTransportAddress(IWSDUdpAddress* This,LPCWSTR pszAddress) {
    return This->lpVtbl->SetTransportAddress(This,pszAddress);
}
/*** IWSDUdpAddress methods ***/
static inline HRESULT IWSDUdpAddress_SetSockaddr(IWSDUdpAddress* This,const SOCKADDR_STORAGE *pSockAddr) {
    return This->lpVtbl->SetSockaddr(This,pSockAddr);
}
static inline HRESULT IWSDUdpAddress_GetSockaddr(IWSDUdpAddress* This,SOCKADDR_STORAGE *pSockAddr) {
    return This->lpVtbl->GetSockaddr(This,pSockAddr);
}
static inline HRESULT IWSDUdpAddress_SetExclusive(IWSDUdpAddress* This,WINBOOL fExclusive) {
    return This->lpVtbl->SetExclusive(This,fExclusive);
}
static inline HRESULT IWSDUdpAddress_GetExclusive(IWSDUdpAddress* This) {
    return This->lpVtbl->GetExclusive(This);
}
static inline HRESULT IWSDUdpAddress_SetMessageType(IWSDUdpAddress* This,WSDUdpMessageType messageType) {
    return This->lpVtbl->SetMessageType(This,messageType);
}
static inline HRESULT IWSDUdpAddress_GetMessageType(IWSDUdpAddress* This,WSDUdpMessageType *pMessageType) {
    return This->lpVtbl->GetMessageType(This,pMessageType);
}
static inline HRESULT IWSDUdpAddress_SetTTL(IWSDUdpAddress* This,DWORD dwTTL) {
    return This->lpVtbl->SetTTL(This,dwTTL);
}
static inline HRESULT IWSDUdpAddress_GetTTL(IWSDUdpAddress* This,DWORD *pdwTTL) {
    return This->lpVtbl->GetTTL(This,pdwTTL);
}
static inline HRESULT IWSDUdpAddress_SetAlias(IWSDUdpAddress* This,const GUID *pAlias) {
    return This->lpVtbl->SetAlias(This,pAlias);
}
static inline HRESULT IWSDUdpAddress_GetAlias(IWSDUdpAddress* This,GUID *pAlias) {
    return This->lpVtbl->GetAlias(This,pAlias);
}
#endif
#endif

#endif


#endif  /* __IWSDUdpAddress_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDHttpMessageParameters interface
 */
#ifndef __IWSDHttpMessageParameters_INTERFACE_DEFINED__
#define __IWSDHttpMessageParameters_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDHttpMessageParameters, 0x540bd122, 0x5c83, 0x4dec, 0xb3,0x96, 0xea,0x62,0xa2,0x69,0x7f,0xdf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("540bd122-5c83-4dec-b396-ea62a2697fdf")
IWSDHttpMessageParameters : public IWSDMessageParameters
{
    virtual HRESULT STDMETHODCALLTYPE SetInboundHttpHeaders(
        LPCWSTR pszHeaders) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInboundHttpHeaders(
        LPCWSTR *ppszHeaders) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutboundHttpHeaders(
        LPCWSTR pszHeaders) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutboundHttpHeaders(
        LPCWSTR *ppszHeaders) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetID(
        LPCWSTR pszId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetID(
        LPCWSTR *ppszId) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContext(
        IUnknown *pContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetContext(
        IUnknown **ppContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDHttpMessageParameters, 0x540bd122, 0x5c83, 0x4dec, 0xb3,0x96, 0xea,0x62,0xa2,0x69,0x7f,0xdf)
#endif
#else
typedef struct IWSDHttpMessageParametersVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDHttpMessageParameters *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDHttpMessageParameters *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDHttpMessageParameters *This);

    /*** IWSDMessageParameters methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLocalAddress)(
        IWSDHttpMessageParameters *This,
        IWSDAddress **ppAddress);

    HRESULT (STDMETHODCALLTYPE *SetLocalAddress)(
        IWSDHttpMessageParameters *This,
        IWSDAddress *pAddress);

    HRESULT (STDMETHODCALLTYPE *GetRemoteAddress)(
        IWSDHttpMessageParameters *This,
        IWSDAddress **ppAddress);

    HRESULT (STDMETHODCALLTYPE *SetRemoteAddress)(
        IWSDHttpMessageParameters *This,
        IWSDAddress *pAddress);

    HRESULT (STDMETHODCALLTYPE *GetLowerParameters)(
        IWSDHttpMessageParameters *This,
        IWSDMessageParameters **ppTxParams);

    /*** IWSDHttpMessageParameters methods ***/
    HRESULT (STDMETHODCALLTYPE *SetInboundHttpHeaders)(
        IWSDHttpMessageParameters *This,
        LPCWSTR pszHeaders);

    HRESULT (STDMETHODCALLTYPE *GetInboundHttpHeaders)(
        IWSDHttpMessageParameters *This,
        LPCWSTR *ppszHeaders);

    HRESULT (STDMETHODCALLTYPE *SetOutboundHttpHeaders)(
        IWSDHttpMessageParameters *This,
        LPCWSTR pszHeaders);

    HRESULT (STDMETHODCALLTYPE *GetOutboundHttpHeaders)(
        IWSDHttpMessageParameters *This,
        LPCWSTR *ppszHeaders);

    HRESULT (STDMETHODCALLTYPE *SetID)(
        IWSDHttpMessageParameters *This,
        LPCWSTR pszId);

    HRESULT (STDMETHODCALLTYPE *GetID)(
        IWSDHttpMessageParameters *This,
        LPCWSTR *ppszId);

    HRESULT (STDMETHODCALLTYPE *SetContext)(
        IWSDHttpMessageParameters *This,
        IUnknown *pContext);

    HRESULT (STDMETHODCALLTYPE *GetContext)(
        IWSDHttpMessageParameters *This,
        IUnknown **ppContext);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IWSDHttpMessageParameters *This);

    END_INTERFACE
} IWSDHttpMessageParametersVtbl;

interface IWSDHttpMessageParameters {
    CONST_VTBL IWSDHttpMessageParametersVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDHttpMessageParameters_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDHttpMessageParameters_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDHttpMessageParameters_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDMessageParameters methods ***/
#define IWSDHttpMessageParameters_GetLocalAddress(This,ppAddress) (This)->lpVtbl->GetLocalAddress(This,ppAddress)
#define IWSDHttpMessageParameters_SetLocalAddress(This,pAddress) (This)->lpVtbl->SetLocalAddress(This,pAddress)
#define IWSDHttpMessageParameters_GetRemoteAddress(This,ppAddress) (This)->lpVtbl->GetRemoteAddress(This,ppAddress)
#define IWSDHttpMessageParameters_SetRemoteAddress(This,pAddress) (This)->lpVtbl->SetRemoteAddress(This,pAddress)
#define IWSDHttpMessageParameters_GetLowerParameters(This,ppTxParams) (This)->lpVtbl->GetLowerParameters(This,ppTxParams)
/*** IWSDHttpMessageParameters methods ***/
#define IWSDHttpMessageParameters_SetInboundHttpHeaders(This,pszHeaders) (This)->lpVtbl->SetInboundHttpHeaders(This,pszHeaders)
#define IWSDHttpMessageParameters_GetInboundHttpHeaders(This,ppszHeaders) (This)->lpVtbl->GetInboundHttpHeaders(This,ppszHeaders)
#define IWSDHttpMessageParameters_SetOutboundHttpHeaders(This,pszHeaders) (This)->lpVtbl->SetOutboundHttpHeaders(This,pszHeaders)
#define IWSDHttpMessageParameters_GetOutboundHttpHeaders(This,ppszHeaders) (This)->lpVtbl->GetOutboundHttpHeaders(This,ppszHeaders)
#define IWSDHttpMessageParameters_SetID(This,pszId) (This)->lpVtbl->SetID(This,pszId)
#define IWSDHttpMessageParameters_GetID(This,ppszId) (This)->lpVtbl->GetID(This,ppszId)
#define IWSDHttpMessageParameters_SetContext(This,pContext) (This)->lpVtbl->SetContext(This,pContext)
#define IWSDHttpMessageParameters_GetContext(This,ppContext) (This)->lpVtbl->GetContext(This,ppContext)
#define IWSDHttpMessageParameters_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDHttpMessageParameters_QueryInterface(IWSDHttpMessageParameters* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDHttpMessageParameters_AddRef(IWSDHttpMessageParameters* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDHttpMessageParameters_Release(IWSDHttpMessageParameters* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDMessageParameters methods ***/
static inline HRESULT IWSDHttpMessageParameters_GetLocalAddress(IWSDHttpMessageParameters* This,IWSDAddress **ppAddress) {
    return This->lpVtbl->GetLocalAddress(This,ppAddress);
}
static inline HRESULT IWSDHttpMessageParameters_SetLocalAddress(IWSDHttpMessageParameters* This,IWSDAddress *pAddress) {
    return This->lpVtbl->SetLocalAddress(This,pAddress);
}
static inline HRESULT IWSDHttpMessageParameters_GetRemoteAddress(IWSDHttpMessageParameters* This,IWSDAddress **ppAddress) {
    return This->lpVtbl->GetRemoteAddress(This,ppAddress);
}
static inline HRESULT IWSDHttpMessageParameters_SetRemoteAddress(IWSDHttpMessageParameters* This,IWSDAddress *pAddress) {
    return This->lpVtbl->SetRemoteAddress(This,pAddress);
}
static inline HRESULT IWSDHttpMessageParameters_GetLowerParameters(IWSDHttpMessageParameters* This,IWSDMessageParameters **ppTxParams) {
    return This->lpVtbl->GetLowerParameters(This,ppTxParams);
}
/*** IWSDHttpMessageParameters methods ***/
static inline HRESULT IWSDHttpMessageParameters_SetInboundHttpHeaders(IWSDHttpMessageParameters* This,LPCWSTR pszHeaders) {
    return This->lpVtbl->SetInboundHttpHeaders(This,pszHeaders);
}
static inline HRESULT IWSDHttpMessageParameters_GetInboundHttpHeaders(IWSDHttpMessageParameters* This,LPCWSTR *ppszHeaders) {
    return This->lpVtbl->GetInboundHttpHeaders(This,ppszHeaders);
}
static inline HRESULT IWSDHttpMessageParameters_SetOutboundHttpHeaders(IWSDHttpMessageParameters* This,LPCWSTR pszHeaders) {
    return This->lpVtbl->SetOutboundHttpHeaders(This,pszHeaders);
}
static inline HRESULT IWSDHttpMessageParameters_GetOutboundHttpHeaders(IWSDHttpMessageParameters* This,LPCWSTR *ppszHeaders) {
    return This->lpVtbl->GetOutboundHttpHeaders(This,ppszHeaders);
}
static inline HRESULT IWSDHttpMessageParameters_SetID(IWSDHttpMessageParameters* This,LPCWSTR pszId) {
    return This->lpVtbl->SetID(This,pszId);
}
static inline HRESULT IWSDHttpMessageParameters_GetID(IWSDHttpMessageParameters* This,LPCWSTR *ppszId) {
    return This->lpVtbl->GetID(This,ppszId);
}
static inline HRESULT IWSDHttpMessageParameters_SetContext(IWSDHttpMessageParameters* This,IUnknown *pContext) {
    return This->lpVtbl->SetContext(This,pContext);
}
static inline HRESULT IWSDHttpMessageParameters_GetContext(IWSDHttpMessageParameters* This,IUnknown **ppContext) {
    return This->lpVtbl->GetContext(This,ppContext);
}
static inline HRESULT IWSDHttpMessageParameters_Clear(IWSDHttpMessageParameters* This) {
    return This->lpVtbl->Clear(This);
}
#endif
#endif

#endif


#endif  /* __IWSDHttpMessageParameters_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDHttpAddress interface
 */
#ifndef __IWSDHttpAddress_INTERFACE_DEFINED__
#define __IWSDHttpAddress_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDHttpAddress, 0xd09ac7bd, 0x2a3e, 0x4b85, 0x86,0x05, 0x27,0x37,0xff,0x3e,0x4e,0xa0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d09ac7bd-2a3e-4b85-8605-2737ff3e4ea0")
IWSDHttpAddress : public IWSDTransportAddress
{
    virtual HRESULT STDMETHODCALLTYPE GetSecure(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSecure(
        WINBOOL fSecure) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPath(
        LPCWSTR *ppszPath) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPath(
        LPCWSTR pszPath) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDHttpAddress, 0xd09ac7bd, 0x2a3e, 0x4b85, 0x86,0x05, 0x27,0x37,0xff,0x3e,0x4e,0xa0)
#endif
#else
typedef struct IWSDHttpAddressVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDHttpAddress *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDHttpAddress *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDHttpAddress *This);

    /*** IWSDAddress methods ***/
    HRESULT (STDMETHODCALLTYPE *Serialize)(
        IWSDHttpAddress *This,
        LPWSTR pszBuffer,
        DWORD cchLength,
        WINBOOL fSafe);

    HRESULT (STDMETHODCALLTYPE *Deserialize)(
        IWSDHttpAddress *This,
        LPCWSTR pszBuffer);

    /*** IWSDTransportAddress methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPort)(
        IWSDHttpAddress *This,
        WORD *pwPort);

    HRESULT (STDMETHODCALLTYPE *SetPort)(
        IWSDHttpAddress *This,
        WORD wPort);

    HRESULT (STDMETHODCALLTYPE *GetTransportAddress)(
        IWSDHttpAddress *This,
        LPCWSTR *ppszAddress);

    HRESULT (STDMETHODCALLTYPE *GetTransportAddressEx)(
        IWSDHttpAddress *This,
        WINBOOL fSafe,
        LPCWSTR *ppszAddress);

    HRESULT (STDMETHODCALLTYPE *SetTransportAddress)(
        IWSDHttpAddress *This,
        LPCWSTR pszAddress);

    /*** IWSDHttpAddress methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSecure)(
        IWSDHttpAddress *This);

    HRESULT (STDMETHODCALLTYPE *SetSecure)(
        IWSDHttpAddress *This,
        WINBOOL fSecure);

    HRESULT (STDMETHODCALLTYPE *GetPath)(
        IWSDHttpAddress *This,
        LPCWSTR *ppszPath);

    HRESULT (STDMETHODCALLTYPE *SetPath)(
        IWSDHttpAddress *This,
        LPCWSTR pszPath);

    END_INTERFACE
} IWSDHttpAddressVtbl;

interface IWSDHttpAddress {
    CONST_VTBL IWSDHttpAddressVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDHttpAddress_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDHttpAddress_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDHttpAddress_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDAddress methods ***/
#define IWSDHttpAddress_Serialize(This,pszBuffer,cchLength,fSafe) (This)->lpVtbl->Serialize(This,pszBuffer,cchLength,fSafe)
#define IWSDHttpAddress_Deserialize(This,pszBuffer) (This)->lpVtbl->Deserialize(This,pszBuffer)
/*** IWSDTransportAddress methods ***/
#define IWSDHttpAddress_GetPort(This,pwPort) (This)->lpVtbl->GetPort(This,pwPort)
#define IWSDHttpAddress_SetPort(This,wPort) (This)->lpVtbl->SetPort(This,wPort)
#define IWSDHttpAddress_GetTransportAddress(This,ppszAddress) (This)->lpVtbl->GetTransportAddress(This,ppszAddress)
#define IWSDHttpAddress_GetTransportAddressEx(This,fSafe,ppszAddress) (This)->lpVtbl->GetTransportAddressEx(This,fSafe,ppszAddress)
#define IWSDHttpAddress_SetTransportAddress(This,pszAddress) (This)->lpVtbl->SetTransportAddress(This,pszAddress)
/*** IWSDHttpAddress methods ***/
#define IWSDHttpAddress_GetSecure(This) (This)->lpVtbl->GetSecure(This)
#define IWSDHttpAddress_SetSecure(This,fSecure) (This)->lpVtbl->SetSecure(This,fSecure)
#define IWSDHttpAddress_GetPath(This,ppszPath) (This)->lpVtbl->GetPath(This,ppszPath)
#define IWSDHttpAddress_SetPath(This,pszPath) (This)->lpVtbl->SetPath(This,pszPath)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDHttpAddress_QueryInterface(IWSDHttpAddress* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDHttpAddress_AddRef(IWSDHttpAddress* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDHttpAddress_Release(IWSDHttpAddress* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDAddress methods ***/
static inline HRESULT IWSDHttpAddress_Serialize(IWSDHttpAddress* This,LPWSTR pszBuffer,DWORD cchLength,WINBOOL fSafe) {
    return This->lpVtbl->Serialize(This,pszBuffer,cchLength,fSafe);
}
static inline HRESULT IWSDHttpAddress_Deserialize(IWSDHttpAddress* This,LPCWSTR pszBuffer) {
    return This->lpVtbl->Deserialize(This,pszBuffer);
}
/*** IWSDTransportAddress methods ***/
static inline HRESULT IWSDHttpAddress_GetPort(IWSDHttpAddress* This,WORD *pwPort) {
    return This->lpVtbl->GetPort(This,pwPort);
}
static inline HRESULT IWSDHttpAddress_SetPort(IWSDHttpAddress* This,WORD wPort) {
    return This->lpVtbl->SetPort(This,wPort);
}
static inline HRESULT IWSDHttpAddress_GetTransportAddress(IWSDHttpAddress* This,LPCWSTR *ppszAddress) {
    return This->lpVtbl->GetTransportAddress(This,ppszAddress);
}
static inline HRESULT IWSDHttpAddress_GetTransportAddressEx(IWSDHttpAddress* This,WINBOOL fSafe,LPCWSTR *ppszAddress) {
    return This->lpVtbl->GetTransportAddressEx(This,fSafe,ppszAddress);
}
static inline HRESULT IWSDHttpAddress_SetTransportAddress(IWSDHttpAddress* This,LPCWSTR pszAddress) {
    return This->lpVtbl->SetTransportAddress(This,pszAddress);
}
/*** IWSDHttpAddress methods ***/
static inline HRESULT IWSDHttpAddress_GetSecure(IWSDHttpAddress* This) {
    return This->lpVtbl->GetSecure(This);
}
static inline HRESULT IWSDHttpAddress_SetSecure(IWSDHttpAddress* This,WINBOOL fSecure) {
    return This->lpVtbl->SetSecure(This,fSecure);
}
static inline HRESULT IWSDHttpAddress_GetPath(IWSDHttpAddress* This,LPCWSTR *ppszPath) {
    return This->lpVtbl->GetPath(This,ppszPath);
}
static inline HRESULT IWSDHttpAddress_SetPath(IWSDHttpAddress* This,LPCWSTR pszPath) {
    return This->lpVtbl->SetPath(This,pszPath);
}
#endif
#endif

#endif


#endif  /* __IWSDHttpAddress_INTERFACE_DEFINED__ */

#if WINVER >= 0x601
/*****************************************************************************
 * IWSDSSLClientCertificate interface
 */
#ifndef __IWSDSSLClientCertificate_INTERFACE_DEFINED__
#define __IWSDSSLClientCertificate_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDSSLClientCertificate, 0xde105e87, 0xa0da, 0x418e, 0x98,0xad, 0x27,0xb9,0xee,0xd8,0x7b,0xdc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("de105e87-a0da-418e-98ad-27b9eed87bdc")
IWSDSSLClientCertificate : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetClientCertificate(
        PCCERT_CONTEXT *ppCertContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMappedAccessToken(
        HANDLE *phToken) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDSSLClientCertificate, 0xde105e87, 0xa0da, 0x418e, 0x98,0xad, 0x27,0xb9,0xee,0xd8,0x7b,0xdc)
#endif
#else
typedef struct IWSDSSLClientCertificateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDSSLClientCertificate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDSSLClientCertificate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDSSLClientCertificate *This);

    /*** IWSDSSLClientCertificate methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClientCertificate)(
        IWSDSSLClientCertificate *This,
        PCCERT_CONTEXT *ppCertContext);

    HRESULT (STDMETHODCALLTYPE *GetMappedAccessToken)(
        IWSDSSLClientCertificate *This,
        HANDLE *phToken);

    END_INTERFACE
} IWSDSSLClientCertificateVtbl;

interface IWSDSSLClientCertificate {
    CONST_VTBL IWSDSSLClientCertificateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDSSLClientCertificate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDSSLClientCertificate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDSSLClientCertificate_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDSSLClientCertificate methods ***/
#define IWSDSSLClientCertificate_GetClientCertificate(This,ppCertContext) (This)->lpVtbl->GetClientCertificate(This,ppCertContext)
#define IWSDSSLClientCertificate_GetMappedAccessToken(This,phToken) (This)->lpVtbl->GetMappedAccessToken(This,phToken)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDSSLClientCertificate_QueryInterface(IWSDSSLClientCertificate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDSSLClientCertificate_AddRef(IWSDSSLClientCertificate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDSSLClientCertificate_Release(IWSDSSLClientCertificate* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDSSLClientCertificate methods ***/
static inline HRESULT IWSDSSLClientCertificate_GetClientCertificate(IWSDSSLClientCertificate* This,PCCERT_CONTEXT *ppCertContext) {
    return This->lpVtbl->GetClientCertificate(This,ppCertContext);
}
static inline HRESULT IWSDSSLClientCertificate_GetMappedAccessToken(IWSDSSLClientCertificate* This,HANDLE *phToken) {
    return This->lpVtbl->GetMappedAccessToken(This,phToken);
}
#endif
#endif

#endif


#endif  /* __IWSDSSLClientCertificate_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDHttpAuthParameters interface
 */
#ifndef __IWSDHttpAuthParameters_INTERFACE_DEFINED__
#define __IWSDHttpAuthParameters_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDHttpAuthParameters, 0x0b476df0, 0x8dac, 0x480d, 0xb0,0x5c, 0x99,0x78,0x1a,0x58,0x84,0xaa);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0b476df0-8dac-480d-b05c-99781a5884aa")
IWSDHttpAuthParameters : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetClientAccessToken(
        HANDLE *phToken) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAuthType(
        PWSD_SECURITY_HTTP_AUTH_SCHEMES pAuthType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDHttpAuthParameters, 0x0b476df0, 0x8dac, 0x480d, 0xb0,0x5c, 0x99,0x78,0x1a,0x58,0x84,0xaa)
#endif
#else
typedef struct IWSDHttpAuthParametersVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDHttpAuthParameters *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDHttpAuthParameters *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDHttpAuthParameters *This);

    /*** IWSDHttpAuthParameters methods ***/
    HRESULT (STDMETHODCALLTYPE *GetClientAccessToken)(
        IWSDHttpAuthParameters *This,
        HANDLE *phToken);

    HRESULT (STDMETHODCALLTYPE *GetAuthType)(
        IWSDHttpAuthParameters *This,
        PWSD_SECURITY_HTTP_AUTH_SCHEMES pAuthType);

    END_INTERFACE
} IWSDHttpAuthParametersVtbl;

interface IWSDHttpAuthParameters {
    CONST_VTBL IWSDHttpAuthParametersVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDHttpAuthParameters_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDHttpAuthParameters_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDHttpAuthParameters_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDHttpAuthParameters methods ***/
#define IWSDHttpAuthParameters_GetClientAccessToken(This,phToken) (This)->lpVtbl->GetClientAccessToken(This,phToken)
#define IWSDHttpAuthParameters_GetAuthType(This,pAuthType) (This)->lpVtbl->GetAuthType(This,pAuthType)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDHttpAuthParameters_QueryInterface(IWSDHttpAuthParameters* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDHttpAuthParameters_AddRef(IWSDHttpAuthParameters* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDHttpAuthParameters_Release(IWSDHttpAuthParameters* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDHttpAuthParameters methods ***/
static inline HRESULT IWSDHttpAuthParameters_GetClientAccessToken(IWSDHttpAuthParameters* This,HANDLE *phToken) {
    return This->lpVtbl->GetClientAccessToken(This,phToken);
}
static inline HRESULT IWSDHttpAuthParameters_GetAuthType(IWSDHttpAuthParameters* This,PWSD_SECURITY_HTTP_AUTH_SCHEMES pAuthType) {
    return This->lpVtbl->GetAuthType(This,pAuthType);
}
#endif
#endif

#endif


#endif  /* __IWSDHttpAuthParameters_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDSignatureProperty interface
 */
#ifndef __IWSDSignatureProperty_INTERFACE_DEFINED__
#define __IWSDSignatureProperty_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDSignatureProperty, 0x03ce20aa, 0x71c4, 0x45e2, 0xb3,0x2e, 0x37,0x66,0xc6,0x1c,0x79,0x0f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("03ce20aa-71c4-45e2-b32e-3766c61c790f")
IWSDSignatureProperty : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE IsMessageSigned(
        WINBOOL *pbSigned) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsMessageSignatureTrusted(
        WINBOOL *pbSignatureTrusted) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetKeyInfo(
        BYTE *pbKeyInfo,
        DWORD *pdwKeyInfoSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignature(
        BYTE *pbSignature,
        DWORD *pdwSignatureSize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignedInfoHash(
        BYTE *pbSignedInfoHash,
        DWORD *pdwHashSize) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDSignatureProperty, 0x03ce20aa, 0x71c4, 0x45e2, 0xb3,0x2e, 0x37,0x66,0xc6,0x1c,0x79,0x0f)
#endif
#else
typedef struct IWSDSignaturePropertyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDSignatureProperty *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDSignatureProperty *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDSignatureProperty *This);

    /*** IWSDSignatureProperty methods ***/
    HRESULT (STDMETHODCALLTYPE *IsMessageSigned)(
        IWSDSignatureProperty *This,
        WINBOOL *pbSigned);

    HRESULT (STDMETHODCALLTYPE *IsMessageSignatureTrusted)(
        IWSDSignatureProperty *This,
        WINBOOL *pbSignatureTrusted);

    HRESULT (STDMETHODCALLTYPE *GetKeyInfo)(
        IWSDSignatureProperty *This,
        BYTE *pbKeyInfo,
        DWORD *pdwKeyInfoSize);

    HRESULT (STDMETHODCALLTYPE *GetSignature)(
        IWSDSignatureProperty *This,
        BYTE *pbSignature,
        DWORD *pdwSignatureSize);

    HRESULT (STDMETHODCALLTYPE *GetSignedInfoHash)(
        IWSDSignatureProperty *This,
        BYTE *pbSignedInfoHash,
        DWORD *pdwHashSize);

    END_INTERFACE
} IWSDSignaturePropertyVtbl;

interface IWSDSignatureProperty {
    CONST_VTBL IWSDSignaturePropertyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDSignatureProperty_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDSignatureProperty_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDSignatureProperty_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDSignatureProperty methods ***/
#define IWSDSignatureProperty_IsMessageSigned(This,pbSigned) (This)->lpVtbl->IsMessageSigned(This,pbSigned)
#define IWSDSignatureProperty_IsMessageSignatureTrusted(This,pbSignatureTrusted) (This)->lpVtbl->IsMessageSignatureTrusted(This,pbSignatureTrusted)
#define IWSDSignatureProperty_GetKeyInfo(This,pbKeyInfo,pdwKeyInfoSize) (This)->lpVtbl->GetKeyInfo(This,pbKeyInfo,pdwKeyInfoSize)
#define IWSDSignatureProperty_GetSignature(This,pbSignature,pdwSignatureSize) (This)->lpVtbl->GetSignature(This,pbSignature,pdwSignatureSize)
#define IWSDSignatureProperty_GetSignedInfoHash(This,pbSignedInfoHash,pdwHashSize) (This)->lpVtbl->GetSignedInfoHash(This,pbSignedInfoHash,pdwHashSize)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDSignatureProperty_QueryInterface(IWSDSignatureProperty* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDSignatureProperty_AddRef(IWSDSignatureProperty* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDSignatureProperty_Release(IWSDSignatureProperty* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDSignatureProperty methods ***/
static inline HRESULT IWSDSignatureProperty_IsMessageSigned(IWSDSignatureProperty* This,WINBOOL *pbSigned) {
    return This->lpVtbl->IsMessageSigned(This,pbSigned);
}
static inline HRESULT IWSDSignatureProperty_IsMessageSignatureTrusted(IWSDSignatureProperty* This,WINBOOL *pbSignatureTrusted) {
    return This->lpVtbl->IsMessageSignatureTrusted(This,pbSignatureTrusted);
}
static inline HRESULT IWSDSignatureProperty_GetKeyInfo(IWSDSignatureProperty* This,BYTE *pbKeyInfo,DWORD *pdwKeyInfoSize) {
    return This->lpVtbl->GetKeyInfo(This,pbKeyInfo,pdwKeyInfoSize);
}
static inline HRESULT IWSDSignatureProperty_GetSignature(IWSDSignatureProperty* This,BYTE *pbSignature,DWORD *pdwSignatureSize) {
    return This->lpVtbl->GetSignature(This,pbSignature,pdwSignatureSize);
}
static inline HRESULT IWSDSignatureProperty_GetSignedInfoHash(IWSDSignatureProperty* This,BYTE *pbSignedInfoHash,DWORD *pdwHashSize) {
    return This->lpVtbl->GetSignedInfoHash(This,pbSignedInfoHash,pdwHashSize);
}
#endif
#endif

#endif


#endif  /* __IWSDSignatureProperty_INTERFACE_DEFINED__ */

#endif
HRESULT WINAPI WSDCreateHttpAddress(IWSDHttpAddress **ppAdress);
HRESULT WINAPI WSDCreateHttpMessageParameters(IWSDHttpMessageParameters **ppTxParams);
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wsdbase_h__ */
