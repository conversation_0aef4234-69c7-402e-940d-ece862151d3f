/*** Autogenerated by WIDL 10.12 from include/dxgi1_3.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dxgi1_3_h__
#define __dxgi1_3_h__

/* Forward declarations */

#ifndef __IDXGIDevice3_FWD_DEFINED__
#define __IDXGIDevice3_FWD_DEFINED__
typedef interface IDXGIDevice3 IDXGIDevice3;
#ifdef __cplusplus
interface IDXGIDevice3;
#endif /* __cplusplus */
#endif

#ifndef __IDXGISwapChain2_FWD_DEFINED__
#define __IDXGISwap<PERSON>hain2_FWD_DEFINED__
typedef interface IDXGISwapChain2 IDXGISwapChain2;
#ifdef __cplusplus
interface IDXGISwapChain2;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIOutput2_FWD_DEFINED__
#define __IDXGIOutput2_FWD_DEFINED__
typedef interface IDXGIOutput2 IDXGIOutput2;
#ifdef __cplusplus
interface IDXGIOutput2;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIFactory3_FWD_DEFINED__
#define __IDXGIFactory3_FWD_DEFINED__
typedef interface IDXGIFactory3 IDXGIFactory3;
#ifdef __cplusplus
interface IDXGIFactory3;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIDecodeSwapChain_FWD_DEFINED__
#define __IDXGIDecodeSwapChain_FWD_DEFINED__
typedef interface IDXGIDecodeSwapChain IDXGIDecodeSwapChain;
#ifdef __cplusplus
interface IDXGIDecodeSwapChain;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIFactoryMedia_FWD_DEFINED__
#define __IDXGIFactoryMedia_FWD_DEFINED__
typedef interface IDXGIFactoryMedia IDXGIFactoryMedia;
#ifdef __cplusplus
interface IDXGIFactoryMedia;
#endif /* __cplusplus */
#endif

#ifndef __IDXGISwapChainMedia_FWD_DEFINED__
#define __IDXGISwapChainMedia_FWD_DEFINED__
typedef interface IDXGISwapChainMedia IDXGISwapChainMedia;
#ifdef __cplusplus
interface IDXGISwapChainMedia;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIOutput3_FWD_DEFINED__
#define __IDXGIOutput3_FWD_DEFINED__
typedef interface IDXGIOutput3 IDXGIOutput3;
#ifdef __cplusplus
interface IDXGIOutput3;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <dxgi1_2.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct DXGI_MATRIX_3X2_F {
    float _11;
    float _12;
    float _21;
    float _22;
    float _31;
    float _32;
} DXGI_MATRIX_3X2_F;
typedef struct DXGI_DECODE_SWAP_CHAIN_DESC {
    UINT Flags;
} DXGI_DECODE_SWAP_CHAIN_DESC;
typedef enum DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS {
    DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAG_NOMINAL_RANGE = 0x1,
    DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAG_BT709 = 0x2,
    DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAG_xvYCC = 0x4
} DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS;
typedef enum DXGI_FRAME_PRESENTATION_MODE {
    DXGI_FRAME_PRESENTATION_MODE_COMPOSED = 0,
    DXGI_FRAME_PRESENTATION_MODE_OVERLAY = 1,
    DXGI_FRAME_PRESENTATION_MODE_NONE = 2,
    DXGI_FRAME_PRESENTATION_MODE_COMPOSITION_FAILURE = 3
} DXGI_FRAME_PRESENTATION_MODE;
typedef struct DXGI_FRAME_STATISTICS_MEDIA {
    UINT PresentCount;
    UINT PresentRefreshCount;
    UINT SyncRefreshCount;
    LARGE_INTEGER SyncQPCTime;
    LARGE_INTEGER SyncGPUTime;
    DXGI_FRAME_PRESENTATION_MODE CompositionMode;
    UINT ApprovedPresentDuration;
} DXGI_FRAME_STATISTICS_MEDIA;
typedef enum DXGI_OVERLAY_SUPPORT_FLAG {
    DXGI_OVERLAY_SUPPORT_FLAG_DIRECT = 0x1,
    DXGI_OVERLAY_SUPPORT_FLAG_SCALING = 0x2
} DXGI_OVERLAY_SUPPORT_FLAG;
/*****************************************************************************
 * IDXGIDevice3 interface
 */
#ifndef __IDXGIDevice3_INTERFACE_DEFINED__
#define __IDXGIDevice3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIDevice3, 0x6007896c, 0x3244, 0x4afd, 0xbf,0x18, 0xa6,0xd3,0xbe,0xda,0x50,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6007896c-3244-4afd-bf18-a6d3beda5023")
IDXGIDevice3 : public IDXGIDevice2
{
    virtual void STDMETHODCALLTYPE Trim(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIDevice3, 0x6007896c, 0x3244, 0x4afd, 0xbf,0x18, 0xa6,0xd3,0xbe,0xda,0x50,0x23)
#endif
#else
typedef struct IDXGIDevice3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIDevice3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIDevice3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIDevice3 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIDevice3 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIDevice3 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIDevice3 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIDevice3 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAdapter)(
        IDXGIDevice3 *This,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *CreateSurface)(
        IDXGIDevice3 *This,
        const DXGI_SURFACE_DESC *desc,
        UINT surface_count,
        DXGI_USAGE usage,
        const DXGI_SHARED_RESOURCE *shared_resource,
        IDXGISurface **surface);

    HRESULT (STDMETHODCALLTYPE *QueryResourceResidency)(
        IDXGIDevice3 *This,
        IUnknown *const *resources,
        DXGI_RESIDENCY *residency,
        UINT resource_count);

    HRESULT (STDMETHODCALLTYPE *SetGPUThreadPriority)(
        IDXGIDevice3 *This,
        INT priority);

    HRESULT (STDMETHODCALLTYPE *GetGPUThreadPriority)(
        IDXGIDevice3 *This,
        INT *priority);

    /*** IDXGIDevice1 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMaximumFrameLatency)(
        IDXGIDevice3 *This,
        UINT MaxLatency);

    HRESULT (STDMETHODCALLTYPE *GetMaximumFrameLatency)(
        IDXGIDevice3 *This,
        UINT *pMaxLatency);

    /*** IDXGIDevice2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OfferResources)(
        IDXGIDevice3 *This,
        UINT NumResources,
        IDXGIResource *const *ppResources,
        DXGI_OFFER_RESOURCE_PRIORITY Priority);

    HRESULT (STDMETHODCALLTYPE *ReclaimResources)(
        IDXGIDevice3 *This,
        UINT NumResources,
        IDXGIResource *const *ppResources,
        WINBOOL *pDiscarded);

    HRESULT (STDMETHODCALLTYPE *EnqueueSetEvent)(
        IDXGIDevice3 *This,
        HANDLE hEvent);

    /*** IDXGIDevice3 methods ***/
    void (STDMETHODCALLTYPE *Trim)(
        IDXGIDevice3 *This);

    END_INTERFACE
} IDXGIDevice3Vtbl;

interface IDXGIDevice3 {
    CONST_VTBL IDXGIDevice3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIDevice3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIDevice3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIDevice3_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIDevice3_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIDevice3_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIDevice3_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIDevice3_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDevice methods ***/
#define IDXGIDevice3_GetAdapter(This,adapter) (This)->lpVtbl->GetAdapter(This,adapter)
#define IDXGIDevice3_CreateSurface(This,desc,surface_count,usage,shared_resource,surface) (This)->lpVtbl->CreateSurface(This,desc,surface_count,usage,shared_resource,surface)
#define IDXGIDevice3_QueryResourceResidency(This,resources,residency,resource_count) (This)->lpVtbl->QueryResourceResidency(This,resources,residency,resource_count)
#define IDXGIDevice3_SetGPUThreadPriority(This,priority) (This)->lpVtbl->SetGPUThreadPriority(This,priority)
#define IDXGIDevice3_GetGPUThreadPriority(This,priority) (This)->lpVtbl->GetGPUThreadPriority(This,priority)
/*** IDXGIDevice1 methods ***/
#define IDXGIDevice3_SetMaximumFrameLatency(This,MaxLatency) (This)->lpVtbl->SetMaximumFrameLatency(This,MaxLatency)
#define IDXGIDevice3_GetMaximumFrameLatency(This,pMaxLatency) (This)->lpVtbl->GetMaximumFrameLatency(This,pMaxLatency)
/*** IDXGIDevice2 methods ***/
#define IDXGIDevice3_OfferResources(This,NumResources,ppResources,Priority) (This)->lpVtbl->OfferResources(This,NumResources,ppResources,Priority)
#define IDXGIDevice3_ReclaimResources(This,NumResources,ppResources,pDiscarded) (This)->lpVtbl->ReclaimResources(This,NumResources,ppResources,pDiscarded)
#define IDXGIDevice3_EnqueueSetEvent(This,hEvent) (This)->lpVtbl->EnqueueSetEvent(This,hEvent)
/*** IDXGIDevice3 methods ***/
#define IDXGIDevice3_Trim(This) (This)->lpVtbl->Trim(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIDevice3_QueryInterface(IDXGIDevice3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIDevice3_AddRef(IDXGIDevice3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIDevice3_Release(IDXGIDevice3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIDevice3_SetPrivateData(IDXGIDevice3* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIDevice3_SetPrivateDataInterface(IDXGIDevice3* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIDevice3_GetPrivateData(IDXGIDevice3* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIDevice3_GetParent(IDXGIDevice3* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDevice methods ***/
static inline HRESULT IDXGIDevice3_GetAdapter(IDXGIDevice3* This,IDXGIAdapter **adapter) {
    return This->lpVtbl->GetAdapter(This,adapter);
}
static inline HRESULT IDXGIDevice3_CreateSurface(IDXGIDevice3* This,const DXGI_SURFACE_DESC *desc,UINT surface_count,DXGI_USAGE usage,const DXGI_SHARED_RESOURCE *shared_resource,IDXGISurface **surface) {
    return This->lpVtbl->CreateSurface(This,desc,surface_count,usage,shared_resource,surface);
}
static inline HRESULT IDXGIDevice3_QueryResourceResidency(IDXGIDevice3* This,IUnknown *const *resources,DXGI_RESIDENCY *residency,UINT resource_count) {
    return This->lpVtbl->QueryResourceResidency(This,resources,residency,resource_count);
}
static inline HRESULT IDXGIDevice3_SetGPUThreadPriority(IDXGIDevice3* This,INT priority) {
    return This->lpVtbl->SetGPUThreadPriority(This,priority);
}
static inline HRESULT IDXGIDevice3_GetGPUThreadPriority(IDXGIDevice3* This,INT *priority) {
    return This->lpVtbl->GetGPUThreadPriority(This,priority);
}
/*** IDXGIDevice1 methods ***/
static inline HRESULT IDXGIDevice3_SetMaximumFrameLatency(IDXGIDevice3* This,UINT MaxLatency) {
    return This->lpVtbl->SetMaximumFrameLatency(This,MaxLatency);
}
static inline HRESULT IDXGIDevice3_GetMaximumFrameLatency(IDXGIDevice3* This,UINT *pMaxLatency) {
    return This->lpVtbl->GetMaximumFrameLatency(This,pMaxLatency);
}
/*** IDXGIDevice2 methods ***/
static inline HRESULT IDXGIDevice3_OfferResources(IDXGIDevice3* This,UINT NumResources,IDXGIResource *const *ppResources,DXGI_OFFER_RESOURCE_PRIORITY Priority) {
    return This->lpVtbl->OfferResources(This,NumResources,ppResources,Priority);
}
static inline HRESULT IDXGIDevice3_ReclaimResources(IDXGIDevice3* This,UINT NumResources,IDXGIResource *const *ppResources,WINBOOL *pDiscarded) {
    return This->lpVtbl->ReclaimResources(This,NumResources,ppResources,pDiscarded);
}
static inline HRESULT IDXGIDevice3_EnqueueSetEvent(IDXGIDevice3* This,HANDLE hEvent) {
    return This->lpVtbl->EnqueueSetEvent(This,hEvent);
}
/*** IDXGIDevice3 methods ***/
static inline void IDXGIDevice3_Trim(IDXGIDevice3* This) {
    This->lpVtbl->Trim(This);
}
#endif
#endif

#endif


#endif  /* __IDXGIDevice3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGISwapChain2 interface
 */
#ifndef __IDXGISwapChain2_INTERFACE_DEFINED__
#define __IDXGISwapChain2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGISwapChain2, 0xa8be2ac4, 0x199f, 0x4946, 0xb3,0x31, 0x79,0x59,0x9f,0xb9,0x8d,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a8be2ac4-199f-4946-b331-79599fb98de7")
IDXGISwapChain2 : public IDXGISwapChain1
{
    virtual HRESULT STDMETHODCALLTYPE SetSourceSize(
        UINT width,
        UINT height) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSourceSize(
        UINT *width,
        UINT *height) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMaximumFrameLatency(
        UINT max_latency) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaximumFrameLatency(
        UINT *max_latency) = 0;

    virtual HANDLE STDMETHODCALLTYPE GetFrameLatencyWaitableObject(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMatrixTransform(
        const DXGI_MATRIX_3X2_F *matrix) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMatrixTransform(
        DXGI_MATRIX_3X2_F *matrix) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGISwapChain2, 0xa8be2ac4, 0x199f, 0x4946, 0xb3,0x31, 0x79,0x59,0x9f,0xb9,0x8d,0xe7)
#endif
#else
typedef struct IDXGISwapChain2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGISwapChain2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGISwapChain2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGISwapChain2 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGISwapChain2 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGISwapChain2 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGISwapChain2 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGISwapChain2 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGISwapChain2 *This,
        REFIID riid,
        void **device);

    /*** IDXGISwapChain methods ***/
    HRESULT (STDMETHODCALLTYPE *Present)(
        IDXGISwapChain2 *This,
        UINT sync_interval,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IDXGISwapChain2 *This,
        UINT buffer_idx,
        REFIID riid,
        void **surface);

    HRESULT (STDMETHODCALLTYPE *SetFullscreenState)(
        IDXGISwapChain2 *This,
        WINBOOL fullscreen,
        IDXGIOutput *target);

    HRESULT (STDMETHODCALLTYPE *GetFullscreenState)(
        IDXGISwapChain2 *This,
        WINBOOL *fullscreen,
        IDXGIOutput **target);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGISwapChain2 *This,
        DXGI_SWAP_CHAIN_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *ResizeBuffers)(
        IDXGISwapChain2 *This,
        UINT buffer_count,
        UINT width,
        UINT height,
        DXGI_FORMAT format,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *ResizeTarget)(
        IDXGISwapChain2 *This,
        const DXGI_MODE_DESC *target_mode_desc);

    HRESULT (STDMETHODCALLTYPE *GetContainingOutput)(
        IDXGISwapChain2 *This,
        IDXGIOutput **output);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGISwapChain2 *This,
        DXGI_FRAME_STATISTICS *stats);

    HRESULT (STDMETHODCALLTYPE *GetLastPresentCount)(
        IDXGISwapChain2 *This,
        UINT *last_present_count);

    /*** IDXGISwapChain1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc1)(
        IDXGISwapChain2 *This,
        DXGI_SWAP_CHAIN_DESC1 *pDesc);

    HRESULT (STDMETHODCALLTYPE *GetFullscreenDesc)(
        IDXGISwapChain2 *This,
        DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pDesc);

    HRESULT (STDMETHODCALLTYPE *GetHwnd)(
        IDXGISwapChain2 *This,
        HWND *pHwnd);

    HRESULT (STDMETHODCALLTYPE *GetCoreWindow)(
        IDXGISwapChain2 *This,
        REFIID refiid,
        void **ppUnk);

    HRESULT (STDMETHODCALLTYPE *Present1)(
        IDXGISwapChain2 *This,
        UINT SyncInterval,
        UINT PresentFlags,
        const DXGI_PRESENT_PARAMETERS *pPresentParameters);

    WINBOOL (STDMETHODCALLTYPE *IsTemporaryMonoSupported)(
        IDXGISwapChain2 *This);

    HRESULT (STDMETHODCALLTYPE *GetRestrictToOutput)(
        IDXGISwapChain2 *This,
        IDXGIOutput **ppRestrictToOutput);

    HRESULT (STDMETHODCALLTYPE *SetBackgroundColor)(
        IDXGISwapChain2 *This,
        const DXGI_RGBA *pColor);

    HRESULT (STDMETHODCALLTYPE *GetBackgroundColor)(
        IDXGISwapChain2 *This,
        DXGI_RGBA *pColor);

    HRESULT (STDMETHODCALLTYPE *SetRotation)(
        IDXGISwapChain2 *This,
        DXGI_MODE_ROTATION Rotation);

    HRESULT (STDMETHODCALLTYPE *GetRotation)(
        IDXGISwapChain2 *This,
        DXGI_MODE_ROTATION *pRotation);

    /*** IDXGISwapChain2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSourceSize)(
        IDXGISwapChain2 *This,
        UINT width,
        UINT height);

    HRESULT (STDMETHODCALLTYPE *GetSourceSize)(
        IDXGISwapChain2 *This,
        UINT *width,
        UINT *height);

    HRESULT (STDMETHODCALLTYPE *SetMaximumFrameLatency)(
        IDXGISwapChain2 *This,
        UINT max_latency);

    HRESULT (STDMETHODCALLTYPE *GetMaximumFrameLatency)(
        IDXGISwapChain2 *This,
        UINT *max_latency);

    HANDLE (STDMETHODCALLTYPE *GetFrameLatencyWaitableObject)(
        IDXGISwapChain2 *This);

    HRESULT (STDMETHODCALLTYPE *SetMatrixTransform)(
        IDXGISwapChain2 *This,
        const DXGI_MATRIX_3X2_F *matrix);

    HRESULT (STDMETHODCALLTYPE *GetMatrixTransform)(
        IDXGISwapChain2 *This,
        DXGI_MATRIX_3X2_F *matrix);

    END_INTERFACE
} IDXGISwapChain2Vtbl;

interface IDXGISwapChain2 {
    CONST_VTBL IDXGISwapChain2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGISwapChain2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGISwapChain2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGISwapChain2_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGISwapChain2_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGISwapChain2_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGISwapChain2_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGISwapChain2_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGISwapChain2_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** IDXGISwapChain methods ***/
#define IDXGISwapChain2_Present(This,sync_interval,flags) (This)->lpVtbl->Present(This,sync_interval,flags)
#define IDXGISwapChain2_GetBuffer(This,buffer_idx,riid,surface) (This)->lpVtbl->GetBuffer(This,buffer_idx,riid,surface)
#define IDXGISwapChain2_SetFullscreenState(This,fullscreen,target) (This)->lpVtbl->SetFullscreenState(This,fullscreen,target)
#define IDXGISwapChain2_GetFullscreenState(This,fullscreen,target) (This)->lpVtbl->GetFullscreenState(This,fullscreen,target)
#define IDXGISwapChain2_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGISwapChain2_ResizeBuffers(This,buffer_count,width,height,format,flags) (This)->lpVtbl->ResizeBuffers(This,buffer_count,width,height,format,flags)
#define IDXGISwapChain2_ResizeTarget(This,target_mode_desc) (This)->lpVtbl->ResizeTarget(This,target_mode_desc)
#define IDXGISwapChain2_GetContainingOutput(This,output) (This)->lpVtbl->GetContainingOutput(This,output)
#define IDXGISwapChain2_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
#define IDXGISwapChain2_GetLastPresentCount(This,last_present_count) (This)->lpVtbl->GetLastPresentCount(This,last_present_count)
/*** IDXGISwapChain1 methods ***/
#define IDXGISwapChain2_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
#define IDXGISwapChain2_GetFullscreenDesc(This,pDesc) (This)->lpVtbl->GetFullscreenDesc(This,pDesc)
#define IDXGISwapChain2_GetHwnd(This,pHwnd) (This)->lpVtbl->GetHwnd(This,pHwnd)
#define IDXGISwapChain2_GetCoreWindow(This,refiid,ppUnk) (This)->lpVtbl->GetCoreWindow(This,refiid,ppUnk)
#define IDXGISwapChain2_Present1(This,SyncInterval,PresentFlags,pPresentParameters) (This)->lpVtbl->Present1(This,SyncInterval,PresentFlags,pPresentParameters)
#define IDXGISwapChain2_IsTemporaryMonoSupported(This) (This)->lpVtbl->IsTemporaryMonoSupported(This)
#define IDXGISwapChain2_GetRestrictToOutput(This,ppRestrictToOutput) (This)->lpVtbl->GetRestrictToOutput(This,ppRestrictToOutput)
#define IDXGISwapChain2_SetBackgroundColor(This,pColor) (This)->lpVtbl->SetBackgroundColor(This,pColor)
#define IDXGISwapChain2_GetBackgroundColor(This,pColor) (This)->lpVtbl->GetBackgroundColor(This,pColor)
#define IDXGISwapChain2_SetRotation(This,Rotation) (This)->lpVtbl->SetRotation(This,Rotation)
#define IDXGISwapChain2_GetRotation(This,pRotation) (This)->lpVtbl->GetRotation(This,pRotation)
/*** IDXGISwapChain2 methods ***/
#define IDXGISwapChain2_SetSourceSize(This,width,height) (This)->lpVtbl->SetSourceSize(This,width,height)
#define IDXGISwapChain2_GetSourceSize(This,width,height) (This)->lpVtbl->GetSourceSize(This,width,height)
#define IDXGISwapChain2_SetMaximumFrameLatency(This,max_latency) (This)->lpVtbl->SetMaximumFrameLatency(This,max_latency)
#define IDXGISwapChain2_GetMaximumFrameLatency(This,max_latency) (This)->lpVtbl->GetMaximumFrameLatency(This,max_latency)
#define IDXGISwapChain2_GetFrameLatencyWaitableObject(This) (This)->lpVtbl->GetFrameLatencyWaitableObject(This)
#define IDXGISwapChain2_SetMatrixTransform(This,matrix) (This)->lpVtbl->SetMatrixTransform(This,matrix)
#define IDXGISwapChain2_GetMatrixTransform(This,matrix) (This)->lpVtbl->GetMatrixTransform(This,matrix)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGISwapChain2_QueryInterface(IDXGISwapChain2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGISwapChain2_AddRef(IDXGISwapChain2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGISwapChain2_Release(IDXGISwapChain2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGISwapChain2_SetPrivateData(IDXGISwapChain2* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISwapChain2_SetPrivateDataInterface(IDXGISwapChain2* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGISwapChain2_GetPrivateData(IDXGISwapChain2* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISwapChain2_GetParent(IDXGISwapChain2* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static inline HRESULT IDXGISwapChain2_GetDevice(IDXGISwapChain2* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** IDXGISwapChain methods ***/
static inline HRESULT IDXGISwapChain2_Present(IDXGISwapChain2* This,UINT sync_interval,UINT flags) {
    return This->lpVtbl->Present(This,sync_interval,flags);
}
static inline HRESULT IDXGISwapChain2_GetBuffer(IDXGISwapChain2* This,UINT buffer_idx,REFIID riid,void **surface) {
    return This->lpVtbl->GetBuffer(This,buffer_idx,riid,surface);
}
static inline HRESULT IDXGISwapChain2_SetFullscreenState(IDXGISwapChain2* This,WINBOOL fullscreen,IDXGIOutput *target) {
    return This->lpVtbl->SetFullscreenState(This,fullscreen,target);
}
static inline HRESULT IDXGISwapChain2_GetFullscreenState(IDXGISwapChain2* This,WINBOOL *fullscreen,IDXGIOutput **target) {
    return This->lpVtbl->GetFullscreenState(This,fullscreen,target);
}
static inline HRESULT IDXGISwapChain2_GetDesc(IDXGISwapChain2* This,DXGI_SWAP_CHAIN_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGISwapChain2_ResizeBuffers(IDXGISwapChain2* This,UINT buffer_count,UINT width,UINT height,DXGI_FORMAT format,UINT flags) {
    return This->lpVtbl->ResizeBuffers(This,buffer_count,width,height,format,flags);
}
static inline HRESULT IDXGISwapChain2_ResizeTarget(IDXGISwapChain2* This,const DXGI_MODE_DESC *target_mode_desc) {
    return This->lpVtbl->ResizeTarget(This,target_mode_desc);
}
static inline HRESULT IDXGISwapChain2_GetContainingOutput(IDXGISwapChain2* This,IDXGIOutput **output) {
    return This->lpVtbl->GetContainingOutput(This,output);
}
static inline HRESULT IDXGISwapChain2_GetFrameStatistics(IDXGISwapChain2* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
static inline HRESULT IDXGISwapChain2_GetLastPresentCount(IDXGISwapChain2* This,UINT *last_present_count) {
    return This->lpVtbl->GetLastPresentCount(This,last_present_count);
}
/*** IDXGISwapChain1 methods ***/
static inline HRESULT IDXGISwapChain2_GetDesc1(IDXGISwapChain2* This,DXGI_SWAP_CHAIN_DESC1 *pDesc) {
    return This->lpVtbl->GetDesc1(This,pDesc);
}
static inline HRESULT IDXGISwapChain2_GetFullscreenDesc(IDXGISwapChain2* This,DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pDesc) {
    return This->lpVtbl->GetFullscreenDesc(This,pDesc);
}
static inline HRESULT IDXGISwapChain2_GetHwnd(IDXGISwapChain2* This,HWND *pHwnd) {
    return This->lpVtbl->GetHwnd(This,pHwnd);
}
static inline HRESULT IDXGISwapChain2_GetCoreWindow(IDXGISwapChain2* This,REFIID refiid,void **ppUnk) {
    return This->lpVtbl->GetCoreWindow(This,refiid,ppUnk);
}
static inline HRESULT IDXGISwapChain2_Present1(IDXGISwapChain2* This,UINT SyncInterval,UINT PresentFlags,const DXGI_PRESENT_PARAMETERS *pPresentParameters) {
    return This->lpVtbl->Present1(This,SyncInterval,PresentFlags,pPresentParameters);
}
static inline WINBOOL IDXGISwapChain2_IsTemporaryMonoSupported(IDXGISwapChain2* This) {
    return This->lpVtbl->IsTemporaryMonoSupported(This);
}
static inline HRESULT IDXGISwapChain2_GetRestrictToOutput(IDXGISwapChain2* This,IDXGIOutput **ppRestrictToOutput) {
    return This->lpVtbl->GetRestrictToOutput(This,ppRestrictToOutput);
}
static inline HRESULT IDXGISwapChain2_SetBackgroundColor(IDXGISwapChain2* This,const DXGI_RGBA *pColor) {
    return This->lpVtbl->SetBackgroundColor(This,pColor);
}
static inline HRESULT IDXGISwapChain2_GetBackgroundColor(IDXGISwapChain2* This,DXGI_RGBA *pColor) {
    return This->lpVtbl->GetBackgroundColor(This,pColor);
}
static inline HRESULT IDXGISwapChain2_SetRotation(IDXGISwapChain2* This,DXGI_MODE_ROTATION Rotation) {
    return This->lpVtbl->SetRotation(This,Rotation);
}
static inline HRESULT IDXGISwapChain2_GetRotation(IDXGISwapChain2* This,DXGI_MODE_ROTATION *pRotation) {
    return This->lpVtbl->GetRotation(This,pRotation);
}
/*** IDXGISwapChain2 methods ***/
static inline HRESULT IDXGISwapChain2_SetSourceSize(IDXGISwapChain2* This,UINT width,UINT height) {
    return This->lpVtbl->SetSourceSize(This,width,height);
}
static inline HRESULT IDXGISwapChain2_GetSourceSize(IDXGISwapChain2* This,UINT *width,UINT *height) {
    return This->lpVtbl->GetSourceSize(This,width,height);
}
static inline HRESULT IDXGISwapChain2_SetMaximumFrameLatency(IDXGISwapChain2* This,UINT max_latency) {
    return This->lpVtbl->SetMaximumFrameLatency(This,max_latency);
}
static inline HRESULT IDXGISwapChain2_GetMaximumFrameLatency(IDXGISwapChain2* This,UINT *max_latency) {
    return This->lpVtbl->GetMaximumFrameLatency(This,max_latency);
}
static inline HANDLE IDXGISwapChain2_GetFrameLatencyWaitableObject(IDXGISwapChain2* This) {
    return This->lpVtbl->GetFrameLatencyWaitableObject(This);
}
static inline HRESULT IDXGISwapChain2_SetMatrixTransform(IDXGISwapChain2* This,const DXGI_MATRIX_3X2_F *matrix) {
    return This->lpVtbl->SetMatrixTransform(This,matrix);
}
static inline HRESULT IDXGISwapChain2_GetMatrixTransform(IDXGISwapChain2* This,DXGI_MATRIX_3X2_F *matrix) {
    return This->lpVtbl->GetMatrixTransform(This,matrix);
}
#endif
#endif

#endif


#endif  /* __IDXGISwapChain2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIOutput2 interface
 */
#ifndef __IDXGIOutput2_INTERFACE_DEFINED__
#define __IDXGIOutput2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIOutput2, 0x595e39d1, 0x2724, 0x4663, 0x99,0xb1, 0xda,0x96,0x9d,0xe2,0x83,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("595e39d1-2724-4663-99b1-da969de28364")
IDXGIOutput2 : public IDXGIOutput1
{
    virtual WINBOOL STDMETHODCALLTYPE SupportsOverlays(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIOutput2, 0x595e39d1, 0x2724, 0x4663, 0x99,0xb1, 0xda,0x96,0x9d,0xe2,0x83,0x64)
#endif
#else
typedef struct IDXGIOutput2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIOutput2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIOutput2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIOutput2 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIOutput2 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIOutput2 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIOutput2 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIOutput2 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIOutput methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIOutput2 *This,
        DXGI_OUTPUT_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList)(
        IDXGIOutput2 *This,
        DXGI_FORMAT format,
        UINT flags,
        UINT *mode_count,
        DXGI_MODE_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode)(
        IDXGIOutput2 *This,
        const DXGI_MODE_DESC *mode,
        DXGI_MODE_DESC *closest_match,
        IUnknown *device);

    HRESULT (STDMETHODCALLTYPE *WaitForVBlank)(
        IDXGIOutput2 *This);

    HRESULT (STDMETHODCALLTYPE *TakeOwnership)(
        IDXGIOutput2 *This,
        IUnknown *device,
        WINBOOL exclusive);

    void (STDMETHODCALLTYPE *ReleaseOwnership)(
        IDXGIOutput2 *This);

    HRESULT (STDMETHODCALLTYPE *GetGammaControlCapabilities)(
        IDXGIOutput2 *This,
        DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps);

    HRESULT (STDMETHODCALLTYPE *SetGammaControl)(
        IDXGIOutput2 *This,
        const DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *GetGammaControl)(
        IDXGIOutput2 *This,
        DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *SetDisplaySurface)(
        IDXGIOutput2 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData)(
        IDXGIOutput2 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGIOutput2 *This,
        DXGI_FRAME_STATISTICS *stats);

    /*** IDXGIOutput1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList1)(
        IDXGIOutput2 *This,
        DXGI_FORMAT enum_format,
        UINT flags,
        UINT *num_modes,
        DXGI_MODE_DESC1 *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode1)(
        IDXGIOutput2 *This,
        const DXGI_MODE_DESC1 *mode_to_match,
        DXGI_MODE_DESC1 *closest_match,
        IUnknown *concerned_device);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData1)(
        IDXGIOutput2 *This,
        IDXGIResource *destination);

    HRESULT (STDMETHODCALLTYPE *DuplicateOutput)(
        IDXGIOutput2 *This,
        IUnknown *device,
        IDXGIOutputDuplication **output_duplication);

    /*** IDXGIOutput2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *SupportsOverlays)(
        IDXGIOutput2 *This);

    END_INTERFACE
} IDXGIOutput2Vtbl;

interface IDXGIOutput2 {
    CONST_VTBL IDXGIOutput2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIOutput2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIOutput2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIOutput2_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIOutput2_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIOutput2_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIOutput2_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIOutput2_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIOutput methods ***/
#define IDXGIOutput2_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIOutput2_GetDisplayModeList(This,format,flags,mode_count,desc) (This)->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc)
#define IDXGIOutput2_FindClosestMatchingMode(This,mode,closest_match,device) (This)->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device)
#define IDXGIOutput2_WaitForVBlank(This) (This)->lpVtbl->WaitForVBlank(This)
#define IDXGIOutput2_TakeOwnership(This,device,exclusive) (This)->lpVtbl->TakeOwnership(This,device,exclusive)
#define IDXGIOutput2_ReleaseOwnership(This) (This)->lpVtbl->ReleaseOwnership(This)
#define IDXGIOutput2_GetGammaControlCapabilities(This,gamma_caps) (This)->lpVtbl->GetGammaControlCapabilities(This,gamma_caps)
#define IDXGIOutput2_SetGammaControl(This,gamma_control) (This)->lpVtbl->SetGammaControl(This,gamma_control)
#define IDXGIOutput2_GetGammaControl(This,gamma_control) (This)->lpVtbl->GetGammaControl(This,gamma_control)
#define IDXGIOutput2_SetDisplaySurface(This,surface) (This)->lpVtbl->SetDisplaySurface(This,surface)
#define IDXGIOutput2_GetDisplaySurfaceData(This,surface) (This)->lpVtbl->GetDisplaySurfaceData(This,surface)
#define IDXGIOutput2_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
/*** IDXGIOutput1 methods ***/
#define IDXGIOutput2_GetDisplayModeList1(This,enum_format,flags,num_modes,desc) (This)->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc)
#define IDXGIOutput2_FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device) (This)->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device)
#define IDXGIOutput2_GetDisplaySurfaceData1(This,destination) (This)->lpVtbl->GetDisplaySurfaceData1(This,destination)
#define IDXGIOutput2_DuplicateOutput(This,device,output_duplication) (This)->lpVtbl->DuplicateOutput(This,device,output_duplication)
/*** IDXGIOutput2 methods ***/
#define IDXGIOutput2_SupportsOverlays(This) (This)->lpVtbl->SupportsOverlays(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIOutput2_QueryInterface(IDXGIOutput2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIOutput2_AddRef(IDXGIOutput2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIOutput2_Release(IDXGIOutput2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIOutput2_SetPrivateData(IDXGIOutput2* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput2_SetPrivateDataInterface(IDXGIOutput2* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIOutput2_GetPrivateData(IDXGIOutput2* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput2_GetParent(IDXGIOutput2* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIOutput methods ***/
static inline HRESULT IDXGIOutput2_GetDesc(IDXGIOutput2* This,DXGI_OUTPUT_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGIOutput2_GetDisplayModeList(IDXGIOutput2* This,DXGI_FORMAT format,UINT flags,UINT *mode_count,DXGI_MODE_DESC *desc) {
    return This->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc);
}
static inline HRESULT IDXGIOutput2_FindClosestMatchingMode(IDXGIOutput2* This,const DXGI_MODE_DESC *mode,DXGI_MODE_DESC *closest_match,IUnknown *device) {
    return This->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device);
}
static inline HRESULT IDXGIOutput2_WaitForVBlank(IDXGIOutput2* This) {
    return This->lpVtbl->WaitForVBlank(This);
}
static inline HRESULT IDXGIOutput2_TakeOwnership(IDXGIOutput2* This,IUnknown *device,WINBOOL exclusive) {
    return This->lpVtbl->TakeOwnership(This,device,exclusive);
}
static inline void IDXGIOutput2_ReleaseOwnership(IDXGIOutput2* This) {
    This->lpVtbl->ReleaseOwnership(This);
}
static inline HRESULT IDXGIOutput2_GetGammaControlCapabilities(IDXGIOutput2* This,DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps) {
    return This->lpVtbl->GetGammaControlCapabilities(This,gamma_caps);
}
static inline HRESULT IDXGIOutput2_SetGammaControl(IDXGIOutput2* This,const DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->SetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput2_GetGammaControl(IDXGIOutput2* This,DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->GetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput2_SetDisplaySurface(IDXGIOutput2* This,IDXGISurface *surface) {
    return This->lpVtbl->SetDisplaySurface(This,surface);
}
static inline HRESULT IDXGIOutput2_GetDisplaySurfaceData(IDXGIOutput2* This,IDXGISurface *surface) {
    return This->lpVtbl->GetDisplaySurfaceData(This,surface);
}
static inline HRESULT IDXGIOutput2_GetFrameStatistics(IDXGIOutput2* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
/*** IDXGIOutput1 methods ***/
static inline HRESULT IDXGIOutput2_GetDisplayModeList1(IDXGIOutput2* This,DXGI_FORMAT enum_format,UINT flags,UINT *num_modes,DXGI_MODE_DESC1 *desc) {
    return This->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc);
}
static inline HRESULT IDXGIOutput2_FindClosestMatchingMode1(IDXGIOutput2* This,const DXGI_MODE_DESC1 *mode_to_match,DXGI_MODE_DESC1 *closest_match,IUnknown *concerned_device) {
    return This->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device);
}
static inline HRESULT IDXGIOutput2_GetDisplaySurfaceData1(IDXGIOutput2* This,IDXGIResource *destination) {
    return This->lpVtbl->GetDisplaySurfaceData1(This,destination);
}
static inline HRESULT IDXGIOutput2_DuplicateOutput(IDXGIOutput2* This,IUnknown *device,IDXGIOutputDuplication **output_duplication) {
    return This->lpVtbl->DuplicateOutput(This,device,output_duplication);
}
/*** IDXGIOutput2 methods ***/
static inline WINBOOL IDXGIOutput2_SupportsOverlays(IDXGIOutput2* This) {
    return This->lpVtbl->SupportsOverlays(This);
}
#endif
#endif

#endif


#endif  /* __IDXGIOutput2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIFactory3 interface
 */
#ifndef __IDXGIFactory3_INTERFACE_DEFINED__
#define __IDXGIFactory3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIFactory3, 0x25483823, 0xcd46, 0x4c7d, 0x86,0xca, 0x47,0xaa,0x95,0xb8,0x37,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("25483823-cd46-4c7d-86ca-47aa95b837bd")
IDXGIFactory3 : public IDXGIFactory2
{
    virtual UINT STDMETHODCALLTYPE GetCreationFlags(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIFactory3, 0x25483823, 0xcd46, 0x4c7d, 0x86,0xca, 0x47,0xaa,0x95,0xb8,0x37,0xbd)
#endif
#else
typedef struct IDXGIFactory3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIFactory3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIFactory3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIFactory3 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIFactory3 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIFactory3 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIFactory3 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIFactory3 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters)(
        IDXGIFactory3 *This,
        UINT adapter_idx,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *MakeWindowAssociation)(
        IDXGIFactory3 *This,
        HWND window,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetWindowAssociation)(
        IDXGIFactory3 *This,
        HWND *window);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChain)(
        IDXGIFactory3 *This,
        IUnknown *device,
        DXGI_SWAP_CHAIN_DESC *desc,
        IDXGISwapChain **swapchain);

    HRESULT (STDMETHODCALLTYPE *CreateSoftwareAdapter)(
        IDXGIFactory3 *This,
        HMODULE swrast,
        IDXGIAdapter **adapter);

    /*** IDXGIFactory1 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters1)(
        IDXGIFactory3 *This,
        UINT Adapter,
        IDXGIAdapter1 **ppAdapter);

    WINBOOL (STDMETHODCALLTYPE *IsCurrent)(
        IDXGIFactory3 *This);

    /*** IDXGIFactory2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsWindowedStereoEnabled)(
        IDXGIFactory3 *This);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForHwnd)(
        IDXGIFactory3 *This,
        IUnknown *pDevice,
        HWND hWnd,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForCoreWindow)(
        IDXGIFactory3 *This,
        IUnknown *pDevice,
        IUnknown *pWindow,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *GetSharedResourceAdapterLuid)(
        IDXGIFactory3 *This,
        HANDLE hResource,
        LUID *pLuid);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusWindow)(
        IDXGIFactory3 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusEvent)(
        IDXGIFactory3 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterStereoStatus)(
        IDXGIFactory3 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusWindow)(
        IDXGIFactory3 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusEvent)(
        IDXGIFactory3 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterOcclusionStatus)(
        IDXGIFactory3 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForComposition)(
        IDXGIFactory3 *This,
        IUnknown *pDevice,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    /*** IDXGIFactory3 methods ***/
    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        IDXGIFactory3 *This);

    END_INTERFACE
} IDXGIFactory3Vtbl;

interface IDXGIFactory3 {
    CONST_VTBL IDXGIFactory3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIFactory3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIFactory3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIFactory3_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIFactory3_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIFactory3_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIFactory3_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIFactory3_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIFactory methods ***/
#define IDXGIFactory3_EnumAdapters(This,adapter_idx,adapter) (This)->lpVtbl->EnumAdapters(This,adapter_idx,adapter)
#define IDXGIFactory3_MakeWindowAssociation(This,window,flags) (This)->lpVtbl->MakeWindowAssociation(This,window,flags)
#define IDXGIFactory3_GetWindowAssociation(This,window) (This)->lpVtbl->GetWindowAssociation(This,window)
#define IDXGIFactory3_CreateSwapChain(This,device,desc,swapchain) (This)->lpVtbl->CreateSwapChain(This,device,desc,swapchain)
#define IDXGIFactory3_CreateSoftwareAdapter(This,swrast,adapter) (This)->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter)
/*** IDXGIFactory1 methods ***/
#define IDXGIFactory3_EnumAdapters1(This,Adapter,ppAdapter) (This)->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter)
#define IDXGIFactory3_IsCurrent(This) (This)->lpVtbl->IsCurrent(This)
/*** IDXGIFactory2 methods ***/
#define IDXGIFactory3_IsWindowedStereoEnabled(This) (This)->lpVtbl->IsWindowedStereoEnabled(This)
#define IDXGIFactory3_CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory3_CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory3_GetSharedResourceAdapterLuid(This,hResource,pLuid) (This)->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid)
#define IDXGIFactory3_RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory3_RegisterStereoStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory3_UnregisterStereoStatus(This,dwCookie) (This)->lpVtbl->UnregisterStereoStatus(This,dwCookie)
#define IDXGIFactory3_RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory3_RegisterOcclusionStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory3_UnregisterOcclusionStatus(This,dwCookie) (This)->lpVtbl->UnregisterOcclusionStatus(This,dwCookie)
#define IDXGIFactory3_CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain)
/*** IDXGIFactory3 methods ***/
#define IDXGIFactory3_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIFactory3_QueryInterface(IDXGIFactory3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIFactory3_AddRef(IDXGIFactory3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIFactory3_Release(IDXGIFactory3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIFactory3_SetPrivateData(IDXGIFactory3* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory3_SetPrivateDataInterface(IDXGIFactory3* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIFactory3_GetPrivateData(IDXGIFactory3* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory3_GetParent(IDXGIFactory3* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIFactory methods ***/
static inline HRESULT IDXGIFactory3_EnumAdapters(IDXGIFactory3* This,UINT adapter_idx,IDXGIAdapter **adapter) {
    return This->lpVtbl->EnumAdapters(This,adapter_idx,adapter);
}
static inline HRESULT IDXGIFactory3_MakeWindowAssociation(IDXGIFactory3* This,HWND window,UINT flags) {
    return This->lpVtbl->MakeWindowAssociation(This,window,flags);
}
static inline HRESULT IDXGIFactory3_GetWindowAssociation(IDXGIFactory3* This,HWND *window) {
    return This->lpVtbl->GetWindowAssociation(This,window);
}
static inline HRESULT IDXGIFactory3_CreateSwapChain(IDXGIFactory3* This,IUnknown *device,DXGI_SWAP_CHAIN_DESC *desc,IDXGISwapChain **swapchain) {
    return This->lpVtbl->CreateSwapChain(This,device,desc,swapchain);
}
static inline HRESULT IDXGIFactory3_CreateSoftwareAdapter(IDXGIFactory3* This,HMODULE swrast,IDXGIAdapter **adapter) {
    return This->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter);
}
/*** IDXGIFactory1 methods ***/
static inline HRESULT IDXGIFactory3_EnumAdapters1(IDXGIFactory3* This,UINT Adapter,IDXGIAdapter1 **ppAdapter) {
    return This->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter);
}
static inline WINBOOL IDXGIFactory3_IsCurrent(IDXGIFactory3* This) {
    return This->lpVtbl->IsCurrent(This);
}
/*** IDXGIFactory2 methods ***/
static inline WINBOOL IDXGIFactory3_IsWindowedStereoEnabled(IDXGIFactory3* This) {
    return This->lpVtbl->IsWindowedStereoEnabled(This);
}
static inline HRESULT IDXGIFactory3_CreateSwapChainForHwnd(IDXGIFactory3* This,IUnknown *pDevice,HWND hWnd,const DXGI_SWAP_CHAIN_DESC1 *pDesc,const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain);
}
static inline HRESULT IDXGIFactory3_CreateSwapChainForCoreWindow(IDXGIFactory3* This,IUnknown *pDevice,IUnknown *pWindow,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain);
}
static inline HRESULT IDXGIFactory3_GetSharedResourceAdapterLuid(IDXGIFactory3* This,HANDLE hResource,LUID *pLuid) {
    return This->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid);
}
static inline HRESULT IDXGIFactory3_RegisterStereoStatusWindow(IDXGIFactory3* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static inline HRESULT IDXGIFactory3_RegisterStereoStatusEvent(IDXGIFactory3* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie);
}
static inline void IDXGIFactory3_UnregisterStereoStatus(IDXGIFactory3* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterStereoStatus(This,dwCookie);
}
static inline HRESULT IDXGIFactory3_RegisterOcclusionStatusWindow(IDXGIFactory3* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static inline HRESULT IDXGIFactory3_RegisterOcclusionStatusEvent(IDXGIFactory3* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie);
}
static inline void IDXGIFactory3_UnregisterOcclusionStatus(IDXGIFactory3* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterOcclusionStatus(This,dwCookie);
}
static inline HRESULT IDXGIFactory3_CreateSwapChainForComposition(IDXGIFactory3* This,IUnknown *pDevice,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain);
}
/*** IDXGIFactory3 methods ***/
static inline UINT IDXGIFactory3_GetCreationFlags(IDXGIFactory3* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
#endif
#endif

#endif


#endif  /* __IDXGIFactory3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIDecodeSwapChain interface
 */
#ifndef __IDXGIDecodeSwapChain_INTERFACE_DEFINED__
#define __IDXGIDecodeSwapChain_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIDecodeSwapChain, 0x2633066b, 0x4514, 0x4c7a, 0x8f,0xd8, 0x12,0xea,0x98,0x05,0x9d,0x18);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2633066b-4514-4c7a-8fd8-12ea98059d18")
IDXGIDecodeSwapChain : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE PresentBuffer(
        UINT buffer_to_present,
        UINT sync_interval,
        UINT flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSourceRect(
        const RECT *rect) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTargetRect(
        const RECT *rect) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDestSize(
        UINT width,
        UINT height) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSourceRect(
        RECT *rect) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTargetRect(
        RECT *rect) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDestSize(
        UINT *width,
        UINT *height) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetColorSpace(
        DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS colorspace) = 0;

    virtual DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS STDMETHODCALLTYPE GetColorSpace(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIDecodeSwapChain, 0x2633066b, 0x4514, 0x4c7a, 0x8f,0xd8, 0x12,0xea,0x98,0x05,0x9d,0x18)
#endif
#else
typedef struct IDXGIDecodeSwapChainVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIDecodeSwapChain *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIDecodeSwapChain *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIDecodeSwapChain *This);

    /*** IDXGIDecodeSwapChain methods ***/
    HRESULT (STDMETHODCALLTYPE *PresentBuffer)(
        IDXGIDecodeSwapChain *This,
        UINT buffer_to_present,
        UINT sync_interval,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *SetSourceRect)(
        IDXGIDecodeSwapChain *This,
        const RECT *rect);

    HRESULT (STDMETHODCALLTYPE *SetTargetRect)(
        IDXGIDecodeSwapChain *This,
        const RECT *rect);

    HRESULT (STDMETHODCALLTYPE *SetDestSize)(
        IDXGIDecodeSwapChain *This,
        UINT width,
        UINT height);

    HRESULT (STDMETHODCALLTYPE *GetSourceRect)(
        IDXGIDecodeSwapChain *This,
        RECT *rect);

    HRESULT (STDMETHODCALLTYPE *GetTargetRect)(
        IDXGIDecodeSwapChain *This,
        RECT *rect);

    HRESULT (STDMETHODCALLTYPE *GetDestSize)(
        IDXGIDecodeSwapChain *This,
        UINT *width,
        UINT *height);

    HRESULT (STDMETHODCALLTYPE *SetColorSpace)(
        IDXGIDecodeSwapChain *This,
        DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS colorspace);

    DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS (STDMETHODCALLTYPE *GetColorSpace)(
        IDXGIDecodeSwapChain *This);

    END_INTERFACE
} IDXGIDecodeSwapChainVtbl;

interface IDXGIDecodeSwapChain {
    CONST_VTBL IDXGIDecodeSwapChainVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIDecodeSwapChain_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIDecodeSwapChain_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIDecodeSwapChain_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIDecodeSwapChain methods ***/
#define IDXGIDecodeSwapChain_PresentBuffer(This,buffer_to_present,sync_interval,flags) (This)->lpVtbl->PresentBuffer(This,buffer_to_present,sync_interval,flags)
#define IDXGIDecodeSwapChain_SetSourceRect(This,rect) (This)->lpVtbl->SetSourceRect(This,rect)
#define IDXGIDecodeSwapChain_SetTargetRect(This,rect) (This)->lpVtbl->SetTargetRect(This,rect)
#define IDXGIDecodeSwapChain_SetDestSize(This,width,height) (This)->lpVtbl->SetDestSize(This,width,height)
#define IDXGIDecodeSwapChain_GetSourceRect(This,rect) (This)->lpVtbl->GetSourceRect(This,rect)
#define IDXGIDecodeSwapChain_GetTargetRect(This,rect) (This)->lpVtbl->GetTargetRect(This,rect)
#define IDXGIDecodeSwapChain_GetDestSize(This,width,height) (This)->lpVtbl->GetDestSize(This,width,height)
#define IDXGIDecodeSwapChain_SetColorSpace(This,colorspace) (This)->lpVtbl->SetColorSpace(This,colorspace)
#define IDXGIDecodeSwapChain_GetColorSpace(This) (This)->lpVtbl->GetColorSpace(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIDecodeSwapChain_QueryInterface(IDXGIDecodeSwapChain* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIDecodeSwapChain_AddRef(IDXGIDecodeSwapChain* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIDecodeSwapChain_Release(IDXGIDecodeSwapChain* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIDecodeSwapChain methods ***/
static inline HRESULT IDXGIDecodeSwapChain_PresentBuffer(IDXGIDecodeSwapChain* This,UINT buffer_to_present,UINT sync_interval,UINT flags) {
    return This->lpVtbl->PresentBuffer(This,buffer_to_present,sync_interval,flags);
}
static inline HRESULT IDXGIDecodeSwapChain_SetSourceRect(IDXGIDecodeSwapChain* This,const RECT *rect) {
    return This->lpVtbl->SetSourceRect(This,rect);
}
static inline HRESULT IDXGIDecodeSwapChain_SetTargetRect(IDXGIDecodeSwapChain* This,const RECT *rect) {
    return This->lpVtbl->SetTargetRect(This,rect);
}
static inline HRESULT IDXGIDecodeSwapChain_SetDestSize(IDXGIDecodeSwapChain* This,UINT width,UINT height) {
    return This->lpVtbl->SetDestSize(This,width,height);
}
static inline HRESULT IDXGIDecodeSwapChain_GetSourceRect(IDXGIDecodeSwapChain* This,RECT *rect) {
    return This->lpVtbl->GetSourceRect(This,rect);
}
static inline HRESULT IDXGIDecodeSwapChain_GetTargetRect(IDXGIDecodeSwapChain* This,RECT *rect) {
    return This->lpVtbl->GetTargetRect(This,rect);
}
static inline HRESULT IDXGIDecodeSwapChain_GetDestSize(IDXGIDecodeSwapChain* This,UINT *width,UINT *height) {
    return This->lpVtbl->GetDestSize(This,width,height);
}
static inline HRESULT IDXGIDecodeSwapChain_SetColorSpace(IDXGIDecodeSwapChain* This,DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS colorspace) {
    return This->lpVtbl->SetColorSpace(This,colorspace);
}
static inline DXGI_MULTIPLANE_OVERLAY_YCbCr_FLAGS IDXGIDecodeSwapChain_GetColorSpace(IDXGIDecodeSwapChain* This) {
    return This->lpVtbl->GetColorSpace(This);
}
#endif
#endif

#endif


#endif  /* __IDXGIDecodeSwapChain_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIFactoryMedia interface
 */
#ifndef __IDXGIFactoryMedia_INTERFACE_DEFINED__
#define __IDXGIFactoryMedia_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIFactoryMedia, 0x41e7d1f2, 0xa591, 0x4f7b, 0xa2,0xe5, 0xfa,0x9c,0x84,0x3e,0x1c,0x12);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("41e7d1f2-a591-4f7b-a2e5-fa9c843e1c12")
IDXGIFactoryMedia : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateSwapChainForCompositionSurfaceHandle(
        IUnknown *device,
        HANDLE surface,
        const DXGI_SWAP_CHAIN_DESC1 *desc,
        IDXGIOutput *restrict_to_output,
        IDXGISwapChain1 **swapchain) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDecodeSwapChainForCompositionSurfaceHandle(
        IUnknown *device,
        HANDLE surface,
        DXGI_DECODE_SWAP_CHAIN_DESC *desc,
        IDXGIResource *yuv_decode_buffers,
        IDXGIOutput *restrict_to_output,
        IDXGIDecodeSwapChain **swapchain) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIFactoryMedia, 0x41e7d1f2, 0xa591, 0x4f7b, 0xa2,0xe5, 0xfa,0x9c,0x84,0x3e,0x1c,0x12)
#endif
#else
typedef struct IDXGIFactoryMediaVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIFactoryMedia *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIFactoryMedia *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIFactoryMedia *This);

    /*** IDXGIFactoryMedia methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForCompositionSurfaceHandle)(
        IDXGIFactoryMedia *This,
        IUnknown *device,
        HANDLE surface,
        const DXGI_SWAP_CHAIN_DESC1 *desc,
        IDXGIOutput *restrict_to_output,
        IDXGISwapChain1 **swapchain);

    HRESULT (STDMETHODCALLTYPE *CreateDecodeSwapChainForCompositionSurfaceHandle)(
        IDXGIFactoryMedia *This,
        IUnknown *device,
        HANDLE surface,
        DXGI_DECODE_SWAP_CHAIN_DESC *desc,
        IDXGIResource *yuv_decode_buffers,
        IDXGIOutput *restrict_to_output,
        IDXGIDecodeSwapChain **swapchain);

    END_INTERFACE
} IDXGIFactoryMediaVtbl;

interface IDXGIFactoryMedia {
    CONST_VTBL IDXGIFactoryMediaVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIFactoryMedia_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIFactoryMedia_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIFactoryMedia_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIFactoryMedia methods ***/
#define IDXGIFactoryMedia_CreateSwapChainForCompositionSurfaceHandle(This,device,surface,desc,restrict_to_output,swapchain) (This)->lpVtbl->CreateSwapChainForCompositionSurfaceHandle(This,device,surface,desc,restrict_to_output,swapchain)
#define IDXGIFactoryMedia_CreateDecodeSwapChainForCompositionSurfaceHandle(This,device,surface,desc,yuv_decode_buffers,restrict_to_output,swapchain) (This)->lpVtbl->CreateDecodeSwapChainForCompositionSurfaceHandle(This,device,surface,desc,yuv_decode_buffers,restrict_to_output,swapchain)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIFactoryMedia_QueryInterface(IDXGIFactoryMedia* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIFactoryMedia_AddRef(IDXGIFactoryMedia* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIFactoryMedia_Release(IDXGIFactoryMedia* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIFactoryMedia methods ***/
static inline HRESULT IDXGIFactoryMedia_CreateSwapChainForCompositionSurfaceHandle(IDXGIFactoryMedia* This,IUnknown *device,HANDLE surface,const DXGI_SWAP_CHAIN_DESC1 *desc,IDXGIOutput *restrict_to_output,IDXGISwapChain1 **swapchain) {
    return This->lpVtbl->CreateSwapChainForCompositionSurfaceHandle(This,device,surface,desc,restrict_to_output,swapchain);
}
static inline HRESULT IDXGIFactoryMedia_CreateDecodeSwapChainForCompositionSurfaceHandle(IDXGIFactoryMedia* This,IUnknown *device,HANDLE surface,DXGI_DECODE_SWAP_CHAIN_DESC *desc,IDXGIResource *yuv_decode_buffers,IDXGIOutput *restrict_to_output,IDXGIDecodeSwapChain **swapchain) {
    return This->lpVtbl->CreateDecodeSwapChainForCompositionSurfaceHandle(This,device,surface,desc,yuv_decode_buffers,restrict_to_output,swapchain);
}
#endif
#endif

#endif


#endif  /* __IDXGIFactoryMedia_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGISwapChainMedia interface
 */
#ifndef __IDXGISwapChainMedia_INTERFACE_DEFINED__
#define __IDXGISwapChainMedia_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGISwapChainMedia, 0xdd95b90b, 0xf05f, 0x4f6a, 0xbd,0x65, 0x25,0xbf,0xb2,0x64,0xbd,0x84);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dd95b90b-f05f-4f6a-bd65-25bfb264bd84")
IDXGISwapChainMedia : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetFrameStatisticsMedia(
        DXGI_FRAME_STATISTICS_MEDIA *stats) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPresentDuration(
        UINT duration) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckPresentDurationSupport(
        UINT desired_present_duration,
        UINT *closest_smaller_present_duration,
        UINT *closest_larger_present_duration) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGISwapChainMedia, 0xdd95b90b, 0xf05f, 0x4f6a, 0xbd,0x65, 0x25,0xbf,0xb2,0x64,0xbd,0x84)
#endif
#else
typedef struct IDXGISwapChainMediaVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGISwapChainMedia *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGISwapChainMedia *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGISwapChainMedia *This);

    /*** IDXGISwapChainMedia methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFrameStatisticsMedia)(
        IDXGISwapChainMedia *This,
        DXGI_FRAME_STATISTICS_MEDIA *stats);

    HRESULT (STDMETHODCALLTYPE *SetPresentDuration)(
        IDXGISwapChainMedia *This,
        UINT duration);

    HRESULT (STDMETHODCALLTYPE *CheckPresentDurationSupport)(
        IDXGISwapChainMedia *This,
        UINT desired_present_duration,
        UINT *closest_smaller_present_duration,
        UINT *closest_larger_present_duration);

    END_INTERFACE
} IDXGISwapChainMediaVtbl;

interface IDXGISwapChainMedia {
    CONST_VTBL IDXGISwapChainMediaVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGISwapChainMedia_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGISwapChainMedia_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGISwapChainMedia_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGISwapChainMedia methods ***/
#define IDXGISwapChainMedia_GetFrameStatisticsMedia(This,stats) (This)->lpVtbl->GetFrameStatisticsMedia(This,stats)
#define IDXGISwapChainMedia_SetPresentDuration(This,duration) (This)->lpVtbl->SetPresentDuration(This,duration)
#define IDXGISwapChainMedia_CheckPresentDurationSupport(This,desired_present_duration,closest_smaller_present_duration,closest_larger_present_duration) (This)->lpVtbl->CheckPresentDurationSupport(This,desired_present_duration,closest_smaller_present_duration,closest_larger_present_duration)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGISwapChainMedia_QueryInterface(IDXGISwapChainMedia* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGISwapChainMedia_AddRef(IDXGISwapChainMedia* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGISwapChainMedia_Release(IDXGISwapChainMedia* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGISwapChainMedia methods ***/
static inline HRESULT IDXGISwapChainMedia_GetFrameStatisticsMedia(IDXGISwapChainMedia* This,DXGI_FRAME_STATISTICS_MEDIA *stats) {
    return This->lpVtbl->GetFrameStatisticsMedia(This,stats);
}
static inline HRESULT IDXGISwapChainMedia_SetPresentDuration(IDXGISwapChainMedia* This,UINT duration) {
    return This->lpVtbl->SetPresentDuration(This,duration);
}
static inline HRESULT IDXGISwapChainMedia_CheckPresentDurationSupport(IDXGISwapChainMedia* This,UINT desired_present_duration,UINT *closest_smaller_present_duration,UINT *closest_larger_present_duration) {
    return This->lpVtbl->CheckPresentDurationSupport(This,desired_present_duration,closest_smaller_present_duration,closest_larger_present_duration);
}
#endif
#endif

#endif


#endif  /* __IDXGISwapChainMedia_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIOutput3 interface
 */
#ifndef __IDXGIOutput3_INTERFACE_DEFINED__
#define __IDXGIOutput3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIOutput3, 0x8a6bb301, 0x7e7e, 0x41f4, 0xa8,0xe0, 0x5b,0x32,0xf7,0xf9,0x9b,0x18);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8a6bb301-7e7e-41f4-a8e0-5b32f7f99b18")
IDXGIOutput3 : public IDXGIOutput2
{
    virtual HRESULT STDMETHODCALLTYPE CheckOverlaySupport(
        DXGI_FORMAT enum_format,
        IUnknown *concerned_device,
        UINT *flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIOutput3, 0x8a6bb301, 0x7e7e, 0x41f4, 0xa8,0xe0, 0x5b,0x32,0xf7,0xf9,0x9b,0x18)
#endif
#else
typedef struct IDXGIOutput3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIOutput3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIOutput3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIOutput3 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIOutput3 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIOutput3 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIOutput3 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIOutput3 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIOutput methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIOutput3 *This,
        DXGI_OUTPUT_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList)(
        IDXGIOutput3 *This,
        DXGI_FORMAT format,
        UINT flags,
        UINT *mode_count,
        DXGI_MODE_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode)(
        IDXGIOutput3 *This,
        const DXGI_MODE_DESC *mode,
        DXGI_MODE_DESC *closest_match,
        IUnknown *device);

    HRESULT (STDMETHODCALLTYPE *WaitForVBlank)(
        IDXGIOutput3 *This);

    HRESULT (STDMETHODCALLTYPE *TakeOwnership)(
        IDXGIOutput3 *This,
        IUnknown *device,
        WINBOOL exclusive);

    void (STDMETHODCALLTYPE *ReleaseOwnership)(
        IDXGIOutput3 *This);

    HRESULT (STDMETHODCALLTYPE *GetGammaControlCapabilities)(
        IDXGIOutput3 *This,
        DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps);

    HRESULT (STDMETHODCALLTYPE *SetGammaControl)(
        IDXGIOutput3 *This,
        const DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *GetGammaControl)(
        IDXGIOutput3 *This,
        DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *SetDisplaySurface)(
        IDXGIOutput3 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData)(
        IDXGIOutput3 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGIOutput3 *This,
        DXGI_FRAME_STATISTICS *stats);

    /*** IDXGIOutput1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList1)(
        IDXGIOutput3 *This,
        DXGI_FORMAT enum_format,
        UINT flags,
        UINT *num_modes,
        DXGI_MODE_DESC1 *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode1)(
        IDXGIOutput3 *This,
        const DXGI_MODE_DESC1 *mode_to_match,
        DXGI_MODE_DESC1 *closest_match,
        IUnknown *concerned_device);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData1)(
        IDXGIOutput3 *This,
        IDXGIResource *destination);

    HRESULT (STDMETHODCALLTYPE *DuplicateOutput)(
        IDXGIOutput3 *This,
        IUnknown *device,
        IDXGIOutputDuplication **output_duplication);

    /*** IDXGIOutput2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *SupportsOverlays)(
        IDXGIOutput3 *This);

    /*** IDXGIOutput3 methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckOverlaySupport)(
        IDXGIOutput3 *This,
        DXGI_FORMAT enum_format,
        IUnknown *concerned_device,
        UINT *flags);

    END_INTERFACE
} IDXGIOutput3Vtbl;

interface IDXGIOutput3 {
    CONST_VTBL IDXGIOutput3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIOutput3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIOutput3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIOutput3_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIOutput3_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIOutput3_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIOutput3_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIOutput3_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIOutput methods ***/
#define IDXGIOutput3_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIOutput3_GetDisplayModeList(This,format,flags,mode_count,desc) (This)->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc)
#define IDXGIOutput3_FindClosestMatchingMode(This,mode,closest_match,device) (This)->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device)
#define IDXGIOutput3_WaitForVBlank(This) (This)->lpVtbl->WaitForVBlank(This)
#define IDXGIOutput3_TakeOwnership(This,device,exclusive) (This)->lpVtbl->TakeOwnership(This,device,exclusive)
#define IDXGIOutput3_ReleaseOwnership(This) (This)->lpVtbl->ReleaseOwnership(This)
#define IDXGIOutput3_GetGammaControlCapabilities(This,gamma_caps) (This)->lpVtbl->GetGammaControlCapabilities(This,gamma_caps)
#define IDXGIOutput3_SetGammaControl(This,gamma_control) (This)->lpVtbl->SetGammaControl(This,gamma_control)
#define IDXGIOutput3_GetGammaControl(This,gamma_control) (This)->lpVtbl->GetGammaControl(This,gamma_control)
#define IDXGIOutput3_SetDisplaySurface(This,surface) (This)->lpVtbl->SetDisplaySurface(This,surface)
#define IDXGIOutput3_GetDisplaySurfaceData(This,surface) (This)->lpVtbl->GetDisplaySurfaceData(This,surface)
#define IDXGIOutput3_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
/*** IDXGIOutput1 methods ***/
#define IDXGIOutput3_GetDisplayModeList1(This,enum_format,flags,num_modes,desc) (This)->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc)
#define IDXGIOutput3_FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device) (This)->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device)
#define IDXGIOutput3_GetDisplaySurfaceData1(This,destination) (This)->lpVtbl->GetDisplaySurfaceData1(This,destination)
#define IDXGIOutput3_DuplicateOutput(This,device,output_duplication) (This)->lpVtbl->DuplicateOutput(This,device,output_duplication)
/*** IDXGIOutput2 methods ***/
#define IDXGIOutput3_SupportsOverlays(This) (This)->lpVtbl->SupportsOverlays(This)
/*** IDXGIOutput3 methods ***/
#define IDXGIOutput3_CheckOverlaySupport(This,enum_format,concerned_device,flags) (This)->lpVtbl->CheckOverlaySupport(This,enum_format,concerned_device,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIOutput3_QueryInterface(IDXGIOutput3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIOutput3_AddRef(IDXGIOutput3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIOutput3_Release(IDXGIOutput3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIOutput3_SetPrivateData(IDXGIOutput3* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput3_SetPrivateDataInterface(IDXGIOutput3* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIOutput3_GetPrivateData(IDXGIOutput3* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput3_GetParent(IDXGIOutput3* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIOutput methods ***/
static inline HRESULT IDXGIOutput3_GetDesc(IDXGIOutput3* This,DXGI_OUTPUT_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGIOutput3_GetDisplayModeList(IDXGIOutput3* This,DXGI_FORMAT format,UINT flags,UINT *mode_count,DXGI_MODE_DESC *desc) {
    return This->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc);
}
static inline HRESULT IDXGIOutput3_FindClosestMatchingMode(IDXGIOutput3* This,const DXGI_MODE_DESC *mode,DXGI_MODE_DESC *closest_match,IUnknown *device) {
    return This->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device);
}
static inline HRESULT IDXGIOutput3_WaitForVBlank(IDXGIOutput3* This) {
    return This->lpVtbl->WaitForVBlank(This);
}
static inline HRESULT IDXGIOutput3_TakeOwnership(IDXGIOutput3* This,IUnknown *device,WINBOOL exclusive) {
    return This->lpVtbl->TakeOwnership(This,device,exclusive);
}
static inline void IDXGIOutput3_ReleaseOwnership(IDXGIOutput3* This) {
    This->lpVtbl->ReleaseOwnership(This);
}
static inline HRESULT IDXGIOutput3_GetGammaControlCapabilities(IDXGIOutput3* This,DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps) {
    return This->lpVtbl->GetGammaControlCapabilities(This,gamma_caps);
}
static inline HRESULT IDXGIOutput3_SetGammaControl(IDXGIOutput3* This,const DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->SetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput3_GetGammaControl(IDXGIOutput3* This,DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->GetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput3_SetDisplaySurface(IDXGIOutput3* This,IDXGISurface *surface) {
    return This->lpVtbl->SetDisplaySurface(This,surface);
}
static inline HRESULT IDXGIOutput3_GetDisplaySurfaceData(IDXGIOutput3* This,IDXGISurface *surface) {
    return This->lpVtbl->GetDisplaySurfaceData(This,surface);
}
static inline HRESULT IDXGIOutput3_GetFrameStatistics(IDXGIOutput3* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
/*** IDXGIOutput1 methods ***/
static inline HRESULT IDXGIOutput3_GetDisplayModeList1(IDXGIOutput3* This,DXGI_FORMAT enum_format,UINT flags,UINT *num_modes,DXGI_MODE_DESC1 *desc) {
    return This->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc);
}
static inline HRESULT IDXGIOutput3_FindClosestMatchingMode1(IDXGIOutput3* This,const DXGI_MODE_DESC1 *mode_to_match,DXGI_MODE_DESC1 *closest_match,IUnknown *concerned_device) {
    return This->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device);
}
static inline HRESULT IDXGIOutput3_GetDisplaySurfaceData1(IDXGIOutput3* This,IDXGIResource *destination) {
    return This->lpVtbl->GetDisplaySurfaceData1(This,destination);
}
static inline HRESULT IDXGIOutput3_DuplicateOutput(IDXGIOutput3* This,IUnknown *device,IDXGIOutputDuplication **output_duplication) {
    return This->lpVtbl->DuplicateOutput(This,device,output_duplication);
}
/*** IDXGIOutput2 methods ***/
static inline WINBOOL IDXGIOutput3_SupportsOverlays(IDXGIOutput3* This) {
    return This->lpVtbl->SupportsOverlays(This);
}
/*** IDXGIOutput3 methods ***/
static inline HRESULT IDXGIOutput3_CheckOverlaySupport(IDXGIOutput3* This,DXGI_FORMAT enum_format,IUnknown *concerned_device,UINT *flags) {
    return This->lpVtbl->CheckOverlaySupport(This,enum_format,concerned_device,flags);
}
#endif
#endif

#endif


#endif  /* __IDXGIOutput3_INTERFACE_DEFINED__ */

#define DXGI_CREATE_FACTORY_DEBUG (0x1)

HRESULT __stdcall  CreateDXGIFactory2(UINT flags,REFIID iid,void **factory);

HRESULT __stdcall  DXGIGetDebugInterface1(UINT flags,REFIID iid,void **debug);

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dxgi1_3_h__ */
