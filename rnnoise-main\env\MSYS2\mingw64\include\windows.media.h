/*** Autogenerated by WIDL 10.12 from include/windows.media.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_media_h__
#define __windows_media_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs ABI::Windows::Media::IAutoRepeatModeChangeRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IAutoRepeatModeChangeRequestedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIMediaMarker_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIMediaMarker_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIMediaMarker __x_ABI_CWindows_CMedia_CIMediaMarker;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIMediaMarker ABI::Windows::Media::IMediaMarker
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IMediaMarker;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIMusicDisplayProperties __x_ABI_CWindows_CMedia_CIMusicDisplayProperties;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties ABI::Windows::Media::IMusicDisplayProperties
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IMusicDisplayProperties;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 ABI::Windows::Media::IMusicDisplayProperties2
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IMusicDisplayProperties2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs ABI::Windows::Media::IPlaybackPositionChangeRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IPlaybackPositionChangeRequestedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs ABI::Windows::Media::IPlaybackRateChangeRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IPlaybackRateChangeRequestedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs ABI::Windows::Media::IShuffleEnabledChangeRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IShuffleEnabledChangeRequestedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControls __x_ABI_CWindows_CMedia_CISystemMediaTransportControls;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls ABI::Windows::Media::ISystemMediaTransportControls
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControls;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 ABI::Windows::Media::ISystemMediaTransportControls2
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControls2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater ABI::Windows::Media::ISystemMediaTransportControlsDisplayUpdater
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControlsDisplayUpdater;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs ABI::Windows::Media::ISystemMediaTransportControlsButtonPressedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControlsButtonPressedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs ABI::Windows::Media::ISystemMediaTransportControlsPropertyChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControlsPropertyChangedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties ABI::Windows::Media::ISystemMediaTransportControlsTimelineProperties
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControlsTimelineProperties;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CAutoRepeatModeChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CAutoRepeatModeChangeRequestedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class AutoRepeatModeChangeRequestedEventArgs;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CAutoRepeatModeChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CAutoRepeatModeChangeRequestedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CAutoRepeatModeChangeRequestedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CImageDisplayProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CImageDisplayProperties_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class ImageDisplayProperties;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CImageDisplayProperties __x_ABI_CWindows_CMedia_CImageDisplayProperties;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CImageDisplayProperties_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CMusicDisplayProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CMusicDisplayProperties_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class MusicDisplayProperties;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CMusicDisplayProperties __x_ABI_CWindows_CMedia_CMusicDisplayProperties;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CMusicDisplayProperties_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CPlaybackPositionChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CPlaybackPositionChangeRequestedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class PlaybackPositionChangeRequestedEventArgs;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CPlaybackPositionChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CPlaybackPositionChangeRequestedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CPlaybackPositionChangeRequestedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CPlaybackRateChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CPlaybackRateChangeRequestedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class PlaybackRateChangeRequestedEventArgs;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CPlaybackRateChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CPlaybackRateChangeRequestedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CPlaybackRateChangeRequestedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CShuffleEnabledChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CShuffleEnabledChangeRequestedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class ShuffleEnabledChangeRequestedEventArgs;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CShuffleEnabledChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CShuffleEnabledChangeRequestedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CShuffleEnabledChangeRequestedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSystemMediaTransportControls_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSystemMediaTransportControls_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class SystemMediaTransportControls;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSystemMediaTransportControls __x_ABI_CWindows_CMedia_CSystemMediaTransportControls;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSystemMediaTransportControls_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButtonPressedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButtonPressedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class SystemMediaTransportControlsButtonPressedEventArgs;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButtonPressedEventArgs __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButtonPressedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButtonPressedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsDisplayUpdater_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsDisplayUpdater_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class SystemMediaTransportControlsDisplayUpdater;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsDisplayUpdater __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsDisplayUpdater;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsDisplayUpdater_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsPropertyChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsPropertyChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class SystemMediaTransportControlsPropertyChangedEventArgs;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsPropertyChangedEventArgs __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsPropertyChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsPropertyChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsTimelineProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsTimelineProperties_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class SystemMediaTransportControlsTimelineProperties;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsTimelineProperties __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsTimelineProperties;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsTimelineProperties_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CVideoDisplayProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CVideoDisplayProperties_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            class VideoDisplayProperties;
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CVideoDisplayProperties __x_ABI_CWindows_CMedia_CVideoDisplayProperties;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CVideoDisplayProperties_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CIMediaMarker __FIIterable_1_Windows__CMedia__CIMediaMarker;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CIMediaMarker ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::IMediaMarker* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CIMediaMarker __FIIterator_1_Windows__CMedia__CIMediaMarker;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CIMediaMarker ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::IMediaMarker* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CIMediaMarker __FIVectorView_1_Windows__CMedia__CIMediaMarker;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::IMediaMarker* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
#define ____FIVector_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CMedia__CIMediaMarker __FIVector_1_Windows__CMedia__CIMediaMarker;
#ifdef __cplusplus
#define __FIVector_1_Windows__CMedia__CIMediaMarker ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::IMediaMarker* >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_MediaPlaybackAutoRepeatMode_FWD_DEFINED__
#define ____FIReference_1_MediaPlaybackAutoRepeatMode_FWD_DEFINED__
typedef interface __FIReference_1_MediaPlaybackAutoRepeatMode __FIReference_1_MediaPlaybackAutoRepeatMode;
#ifdef __cplusplus
#define __FIReference_1_MediaPlaybackAutoRepeatMode ABI::Windows::Foundation::IReference<ABI::Windows::Media::MediaPlaybackAutoRepeatMode >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::AutoRepeatModeChangeRequestedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackPositionChangeRequestedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackRateChangeRequestedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::ShuffleEnabledChangeRequestedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsButtonPressedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsPropertyChangedEventArgs* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <windows.foundation.h>
#include <windows.storage.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CAudioProcessing_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CAudioProcessing_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            enum AudioProcessing {
                AudioProcessing_Default = 0,
                AudioProcessing_Raw = 1
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CAudioProcessing {
    AudioProcessing_Default = 0,
    AudioProcessing_Raw = 1
};
#ifdef WIDL_using_Windows_Media
#define AudioProcessing __x_ABI_CWindows_CMedia_CAudioProcessing
#endif /* WIDL_using_Windows_Media */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CAudioProcessing_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CAudioProcessing __x_ABI_CWindows_CMedia_CAudioProcessing;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            enum MediaPlaybackAutoRepeatMode {
                MediaPlaybackAutoRepeatMode_None = 0,
                MediaPlaybackAutoRepeatMode_Track = 1,
                MediaPlaybackAutoRepeatMode_List = 2
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode {
    MediaPlaybackAutoRepeatMode_None = 0,
    MediaPlaybackAutoRepeatMode_Track = 1,
    MediaPlaybackAutoRepeatMode_List = 2
};
#ifdef WIDL_using_Windows_Media
#define MediaPlaybackAutoRepeatMode __x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode
#endif /* WIDL_using_Windows_Media */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode __x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CMediaPlaybackStatus_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CMediaPlaybackStatus_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            enum MediaPlaybackStatus {
                MediaPlaybackStatus_Closed = 0,
                MediaPlaybackStatus_Changing = 1,
                MediaPlaybackStatus_Stopped = 2,
                MediaPlaybackStatus_Playing = 3,
                MediaPlaybackStatus_Paused = 4
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CMediaPlaybackStatus {
    MediaPlaybackStatus_Closed = 0,
    MediaPlaybackStatus_Changing = 1,
    MediaPlaybackStatus_Stopped = 2,
    MediaPlaybackStatus_Playing = 3,
    MediaPlaybackStatus_Paused = 4
};
#ifdef WIDL_using_Windows_Media
#define MediaPlaybackStatus __x_ABI_CWindows_CMedia_CMediaPlaybackStatus
#endif /* WIDL_using_Windows_Media */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CMediaPlaybackStatus_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CMediaPlaybackStatus __x_ABI_CWindows_CMedia_CMediaPlaybackStatus;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CMediaPlaybackType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CMediaPlaybackType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            enum MediaPlaybackType {
                MediaPlaybackType_Unknown = 0,
                MediaPlaybackType_Music = 1,
                MediaPlaybackType_Video = 2,
                MediaPlaybackType_Image = 3
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CMediaPlaybackType {
    MediaPlaybackType_Unknown = 0,
    MediaPlaybackType_Music = 1,
    MediaPlaybackType_Video = 2,
    MediaPlaybackType_Image = 3
};
#ifdef WIDL_using_Windows_Media
#define MediaPlaybackType __x_ABI_CWindows_CMedia_CMediaPlaybackType
#endif /* WIDL_using_Windows_Media */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CMediaPlaybackType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CMediaPlaybackType __x_ABI_CWindows_CMedia_CMediaPlaybackType;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSoundLevel_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSoundLevel_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            enum SoundLevel {
                SoundLevel_Muted = 0,
                SoundLevel_Low = 1,
                SoundLevel_Full = 2
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSoundLevel {
    SoundLevel_Muted = 0,
    SoundLevel_Low = 1,
    SoundLevel_Full = 2
};
#ifdef WIDL_using_Windows_Media
#define SoundLevel __x_ABI_CWindows_CMedia_CSoundLevel
#endif /* WIDL_using_Windows_Media */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CSoundLevel_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSoundLevel __x_ABI_CWindows_CMedia_CSoundLevel;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButton_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButton_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            enum SystemMediaTransportControlsButton {
                SystemMediaTransportControlsButton_Play = 0,
                SystemMediaTransportControlsButton_Pause = 1,
                SystemMediaTransportControlsButton_Stop = 2,
                SystemMediaTransportControlsButton_Record = 3,
                SystemMediaTransportControlsButton_FastForward = 4,
                SystemMediaTransportControlsButton_Rewind = 5,
                SystemMediaTransportControlsButton_Next = 6,
                SystemMediaTransportControlsButton_Previous = 7,
                SystemMediaTransportControlsButton_ChannelUp = 8,
                SystemMediaTransportControlsButton_ChannelDown = 9
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButton {
    SystemMediaTransportControlsButton_Play = 0,
    SystemMediaTransportControlsButton_Pause = 1,
    SystemMediaTransportControlsButton_Stop = 2,
    SystemMediaTransportControlsButton_Record = 3,
    SystemMediaTransportControlsButton_FastForward = 4,
    SystemMediaTransportControlsButton_Rewind = 5,
    SystemMediaTransportControlsButton_Next = 6,
    SystemMediaTransportControlsButton_Previous = 7,
    SystemMediaTransportControlsButton_ChannelUp = 8,
    SystemMediaTransportControlsButton_ChannelDown = 9
};
#ifdef WIDL_using_Windows_Media
#define SystemMediaTransportControlsButton __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButton
#endif /* WIDL_using_Windows_Media */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButton_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButton __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButton;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsProperty_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsProperty_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            enum SystemMediaTransportControlsProperty {
                SystemMediaTransportControlsProperty_SoundLevel = 0
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsProperty {
    SystemMediaTransportControlsProperty_SoundLevel = 0
};
#ifdef WIDL_using_Windows_Media
#define SystemMediaTransportControlsProperty __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsProperty
#endif /* WIDL_using_Windows_Media */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CSystemMediaTransportControlsProperty_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsProperty __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsProperty;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs ABI::Windows::Media::IAutoRepeatModeChangeRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IAutoRepeatModeChangeRequestedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIImageDisplayProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIImageDisplayProperties_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIImageDisplayProperties __x_ABI_CWindows_CMedia_CIImageDisplayProperties;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIImageDisplayProperties ABI::Windows::Media::IImageDisplayProperties
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IImageDisplayProperties;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIMediaControl_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIMediaControl_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIMediaControl __x_ABI_CWindows_CMedia_CIMediaControl;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIMediaControl ABI::Windows::Media::IMediaControl
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IMediaControl;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIMediaMarker_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIMediaMarker_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIMediaMarker __x_ABI_CWindows_CMedia_CIMediaMarker;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIMediaMarker ABI::Windows::Media::IMediaMarker
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IMediaMarker;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIMusicDisplayProperties __x_ABI_CWindows_CMedia_CIMusicDisplayProperties;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties ABI::Windows::Media::IMusicDisplayProperties
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IMusicDisplayProperties;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 ABI::Windows::Media::IMusicDisplayProperties2
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IMusicDisplayProperties2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties3_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIMusicDisplayProperties3 __x_ABI_CWindows_CMedia_CIMusicDisplayProperties3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties3 ABI::Windows::Media::IMusicDisplayProperties3
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IMusicDisplayProperties3;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs ABI::Windows::Media::IPlaybackPositionChangeRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IPlaybackPositionChangeRequestedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs ABI::Windows::Media::IPlaybackRateChangeRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IPlaybackRateChangeRequestedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs ABI::Windows::Media::IShuffleEnabledChangeRequestedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IShuffleEnabledChangeRequestedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControls __x_ABI_CWindows_CMedia_CISystemMediaTransportControls;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls ABI::Windows::Media::ISystemMediaTransportControls
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControls;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 ABI::Windows::Media::ISystemMediaTransportControls2
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControls2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs ABI::Windows::Media::ISystemMediaTransportControlsButtonPressedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControlsButtonPressedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater ABI::Windows::Media::ISystemMediaTransportControlsDisplayUpdater
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControlsDisplayUpdater;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs ABI::Windows::Media::ISystemMediaTransportControlsPropertyChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControlsPropertyChangedEventArgs;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsStatics __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsStatics ABI::Windows::Media::ISystemMediaTransportControlsStatics
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControlsStatics;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties ABI::Windows::Media::ISystemMediaTransportControlsTimelineProperties
namespace ABI {
    namespace Windows {
        namespace Media {
            interface ISystemMediaTransportControlsTimelineProperties;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIVideoDisplayProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIVideoDisplayProperties_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIVideoDisplayProperties __x_ABI_CWindows_CMedia_CIVideoDisplayProperties;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIVideoDisplayProperties ABI::Windows::Media::IVideoDisplayProperties
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IVideoDisplayProperties;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CIVideoDisplayProperties2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIVideoDisplayProperties2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CIVideoDisplayProperties2 __x_ABI_CWindows_CMedia_CIVideoDisplayProperties2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CIVideoDisplayProperties2 ABI::Windows::Media::IVideoDisplayProperties2
namespace ABI {
    namespace Windows {
        namespace Media {
            interface IVideoDisplayProperties2;
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CIMediaMarker __FIIterable_1_Windows__CMedia__CIMediaMarker;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CIMediaMarker ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::IMediaMarker* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CIMediaMarker __FIIterator_1_Windows__CMedia__CIMediaMarker;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CIMediaMarker ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::IMediaMarker* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CIMediaMarker __FIVectorView_1_Windows__CMedia__CIMediaMarker;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::IMediaMarker* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
#define ____FIVector_1_Windows__CMedia__CIMediaMarker_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CMedia__CIMediaMarker __FIVector_1_Windows__CMedia__CIMediaMarker;
#ifdef __cplusplus
#define __FIVector_1_Windows__CMedia__CIMediaMarker ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::IMediaMarker* >
#endif /* __cplusplus */
#endif

#ifndef ____FIReference_1_MediaPlaybackAutoRepeatMode_FWD_DEFINED__
#define ____FIReference_1_MediaPlaybackAutoRepeatMode_FWD_DEFINED__
typedef interface __FIReference_1_MediaPlaybackAutoRepeatMode __FIReference_1_MediaPlaybackAutoRepeatMode;
#ifdef __cplusplus
#define __FIReference_1_MediaPlaybackAutoRepeatMode ABI::Windows::Foundation::IReference<ABI::Windows::Media::MediaPlaybackAutoRepeatMode >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IAutoRepeatModeChangeRequestedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs, 0xea137efa, 0xd852, 0x438e, 0x88,0x2b, 0xc9,0x90,0x10,0x9a,0x78,0xf4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("ea137efa-d852-438e-882b-c990109a78f4")
            IAutoRepeatModeChangeRequestedEventArgs : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_RequestedAutoRepeatMode(
                    ABI::Windows::Media::MediaPlaybackAutoRepeatMode *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs, 0xea137efa, 0xd852, 0x438e, 0x88,0x2b, 0xc9,0x90,0x10,0x9a,0x78,0xf4)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IAutoRepeatModeChangeRequestedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_RequestedAutoRepeatMode)(
        __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs *This,
        __x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAutoRepeatModeChangeRequestedEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_get_RequestedAutoRepeatMode(This,value) (This)->lpVtbl->get_RequestedAutoRepeatMode(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_AddRef(__x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_Release(__x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_GetIids(__x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAutoRepeatModeChangeRequestedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_get_RequestedAutoRepeatMode(__x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs* This,__x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode *value) {
    return This->lpVtbl->get_RequestedAutoRepeatMode(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_IAutoRepeatModeChangeRequestedEventArgs IID___x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs
#define IAutoRepeatModeChangeRequestedEventArgsVtbl __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgsVtbl
#define IAutoRepeatModeChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs
#define IAutoRepeatModeChangeRequestedEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_QueryInterface
#define IAutoRepeatModeChangeRequestedEventArgs_AddRef __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_AddRef
#define IAutoRepeatModeChangeRequestedEventArgs_Release __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_Release
#define IAutoRepeatModeChangeRequestedEventArgs_GetIids __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_GetIids
#define IAutoRepeatModeChangeRequestedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_GetRuntimeClassName
#define IAutoRepeatModeChangeRequestedEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_GetTrustLevel
#define IAutoRepeatModeChangeRequestedEventArgs_get_RequestedAutoRepeatMode __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_get_RequestedAutoRepeatMode
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IMediaMarker interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CIMediaMarker_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIMediaMarker_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CIMediaMarker, 0x1803def8, 0xdca5, 0x4b6f, 0x9c,0x20, 0xe3,0xd3,0xc0,0x64,0x36,0x25);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("1803def8-dca5-4b6f-9c20-e3d3c0643625")
            IMediaMarker : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Time(
                    ABI::Windows::Foundation::TimeSpan *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MediaMarkerType(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Text(
                    HSTRING *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CIMediaMarker, 0x1803def8, 0xdca5, 0x4b6f, 0x9c,0x20, 0xe3,0xd3,0xc0,0x64,0x36,0x25)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CIMediaMarkerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CIMediaMarker *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CIMediaMarker *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CIMediaMarker *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CIMediaMarker *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CIMediaMarker *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CIMediaMarker *This,
        TrustLevel *trustLevel);

    /*** IMediaMarker methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Time)(
        __x_ABI_CWindows_CMedia_CIMediaMarker *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *get_MediaMarkerType)(
        __x_ABI_CWindows_CMedia_CIMediaMarker *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Text)(
        __x_ABI_CWindows_CMedia_CIMediaMarker *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CIMediaMarkerVtbl;

interface __x_ABI_CWindows_CMedia_CIMediaMarker {
    CONST_VTBL __x_ABI_CWindows_CMedia_CIMediaMarkerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CIMediaMarker_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CIMediaMarker_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CIMediaMarker_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CIMediaMarker_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CIMediaMarker_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CIMediaMarker_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMediaMarker methods ***/
#define __x_ABI_CWindows_CMedia_CIMediaMarker_get_Time(This,value) (This)->lpVtbl->get_Time(This,value)
#define __x_ABI_CWindows_CMedia_CIMediaMarker_get_MediaMarkerType(This,value) (This)->lpVtbl->get_MediaMarkerType(This,value)
#define __x_ABI_CWindows_CMedia_CIMediaMarker_get_Text(This,value) (This)->lpVtbl->get_Text(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIMediaMarker_QueryInterface(__x_ABI_CWindows_CMedia_CIMediaMarker* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIMediaMarker_AddRef(__x_ABI_CWindows_CMedia_CIMediaMarker* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIMediaMarker_Release(__x_ABI_CWindows_CMedia_CIMediaMarker* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIMediaMarker_GetIids(__x_ABI_CWindows_CMedia_CIMediaMarker* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMediaMarker_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CIMediaMarker* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMediaMarker_GetTrustLevel(__x_ABI_CWindows_CMedia_CIMediaMarker* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMediaMarker methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIMediaMarker_get_Time(__x_ABI_CWindows_CMedia_CIMediaMarker* This,__x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_Time(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMediaMarker_get_MediaMarkerType(__x_ABI_CWindows_CMedia_CIMediaMarker* This,HSTRING *value) {
    return This->lpVtbl->get_MediaMarkerType(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMediaMarker_get_Text(__x_ABI_CWindows_CMedia_CIMediaMarker* This,HSTRING *value) {
    return This->lpVtbl->get_Text(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_IMediaMarker IID___x_ABI_CWindows_CMedia_CIMediaMarker
#define IMediaMarkerVtbl __x_ABI_CWindows_CMedia_CIMediaMarkerVtbl
#define IMediaMarker __x_ABI_CWindows_CMedia_CIMediaMarker
#define IMediaMarker_QueryInterface __x_ABI_CWindows_CMedia_CIMediaMarker_QueryInterface
#define IMediaMarker_AddRef __x_ABI_CWindows_CMedia_CIMediaMarker_AddRef
#define IMediaMarker_Release __x_ABI_CWindows_CMedia_CIMediaMarker_Release
#define IMediaMarker_GetIids __x_ABI_CWindows_CMedia_CIMediaMarker_GetIids
#define IMediaMarker_GetRuntimeClassName __x_ABI_CWindows_CMedia_CIMediaMarker_GetRuntimeClassName
#define IMediaMarker_GetTrustLevel __x_ABI_CWindows_CMedia_CIMediaMarker_GetTrustLevel
#define IMediaMarker_get_Time __x_ABI_CWindows_CMedia_CIMediaMarker_get_Time
#define IMediaMarker_get_MediaMarkerType __x_ABI_CWindows_CMedia_CIMediaMarker_get_MediaMarkerType
#define IMediaMarker_get_Text __x_ABI_CWindows_CMedia_CIMediaMarker_get_Text
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CIMediaMarker_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IMusicDisplayProperties interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CIMusicDisplayProperties, 0x6bbf0c59, 0xd0a0, 0x4d26, 0x92,0xa0, 0xf9,0x78,0xe1,0xd1,0x8e,0x7b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("6bbf0c59-d0a0-4d26-92a0-f978e1d18e7b")
            IMusicDisplayProperties : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Title(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Title(
                    HSTRING value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_AlbumArtist(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_AlbumArtist(
                    HSTRING value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Artist(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Artist(
                    HSTRING value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties, 0x6bbf0c59, 0xd0a0, 0x4d26, 0x92,0xa0, 0xf9,0x78,0xe1,0xd1,0x8e,0x7b)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CIMusicDisplayPropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This,
        TrustLevel *trustLevel);

    /*** IMusicDisplayProperties methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Title)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_Title)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *get_AlbumArtist)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_AlbumArtist)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *get_Artist)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_Artist)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties *This,
        HSTRING value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CIMusicDisplayPropertiesVtbl;

interface __x_ABI_CWindows_CMedia_CIMusicDisplayProperties {
    CONST_VTBL __x_ABI_CWindows_CMedia_CIMusicDisplayPropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMusicDisplayProperties methods ***/
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_get_Title(This,value) (This)->lpVtbl->get_Title(This,value)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_put_Title(This,value) (This)->lpVtbl->put_Title(This,value)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_get_AlbumArtist(This,value) (This)->lpVtbl->get_AlbumArtist(This,value)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_put_AlbumArtist(This,value) (This)->lpVtbl->put_AlbumArtist(This,value)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_get_Artist(This,value) (This)->lpVtbl->get_Artist(This,value)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_put_Artist(This,value) (This)->lpVtbl->put_Artist(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_QueryInterface(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_AddRef(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_Release(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_GetIids(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_GetTrustLevel(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMusicDisplayProperties methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_get_Title(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This,HSTRING *value) {
    return This->lpVtbl->get_Title(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_put_Title(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This,HSTRING value) {
    return This->lpVtbl->put_Title(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_get_AlbumArtist(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This,HSTRING *value) {
    return This->lpVtbl->get_AlbumArtist(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_put_AlbumArtist(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This,HSTRING value) {
    return This->lpVtbl->put_AlbumArtist(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_get_Artist(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This,HSTRING *value) {
    return This->lpVtbl->get_Artist(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_put_Artist(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties* This,HSTRING value) {
    return This->lpVtbl->put_Artist(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_IMusicDisplayProperties IID___x_ABI_CWindows_CMedia_CIMusicDisplayProperties
#define IMusicDisplayPropertiesVtbl __x_ABI_CWindows_CMedia_CIMusicDisplayPropertiesVtbl
#define IMusicDisplayProperties __x_ABI_CWindows_CMedia_CIMusicDisplayProperties
#define IMusicDisplayProperties_QueryInterface __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_QueryInterface
#define IMusicDisplayProperties_AddRef __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_AddRef
#define IMusicDisplayProperties_Release __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_Release
#define IMusicDisplayProperties_GetIids __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_GetIids
#define IMusicDisplayProperties_GetRuntimeClassName __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_GetRuntimeClassName
#define IMusicDisplayProperties_GetTrustLevel __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_GetTrustLevel
#define IMusicDisplayProperties_get_Title __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_get_Title
#define IMusicDisplayProperties_put_Title __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_put_Title
#define IMusicDisplayProperties_get_AlbumArtist __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_get_AlbumArtist
#define IMusicDisplayProperties_put_AlbumArtist __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_put_AlbumArtist
#define IMusicDisplayProperties_get_Artist __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_get_Artist
#define IMusicDisplayProperties_put_Artist __x_ABI_CWindows_CMedia_CIMusicDisplayProperties_put_Artist
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IMusicDisplayProperties2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CIMusicDisplayProperties2, 0x00368462, 0x97d3, 0x44b9, 0xb0,0x0f, 0x00,0x8a,0xfc,0xef,0xaf,0x18);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("00368462-97d3-44b9-b00f-008afcefaf18")
            IMusicDisplayProperties2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_AlbumTitle(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_AlbumTitle(
                    HSTRING value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_TrackNumber(
                    UINT32 *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_TrackNumber(
                    UINT32 value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Genres(
                    ABI::Windows::Foundation::Collections::IVector<HSTRING > **value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2, 0x00368462, 0x97d3, 0x44b9, 0xb0,0x0f, 0x00,0x8a,0xfc,0xef,0xaf,0x18)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 *This,
        TrustLevel *trustLevel);

    /*** IMusicDisplayProperties2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AlbumTitle)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_AlbumTitle)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *get_TrackNumber)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_TrackNumber)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 *This,
        UINT32 value);

    HRESULT (STDMETHODCALLTYPE *get_Genres)(
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 *This,
        __FIVector_1_HSTRING **value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2Vtbl;

interface __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2 {
    CONST_VTBL __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMusicDisplayProperties2 methods ***/
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_get_AlbumTitle(This,value) (This)->lpVtbl->get_AlbumTitle(This,value)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_put_AlbumTitle(This,value) (This)->lpVtbl->put_AlbumTitle(This,value)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_get_TrackNumber(This,value) (This)->lpVtbl->get_TrackNumber(This,value)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_put_TrackNumber(This,value) (This)->lpVtbl->put_TrackNumber(This,value)
#define __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_get_Genres(This,value) (This)->lpVtbl->get_Genres(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_QueryInterface(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_AddRef(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_Release(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_GetIids(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_GetTrustLevel(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMusicDisplayProperties2 methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_get_AlbumTitle(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2* This,HSTRING *value) {
    return This->lpVtbl->get_AlbumTitle(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_put_AlbumTitle(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2* This,HSTRING value) {
    return This->lpVtbl->put_AlbumTitle(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_get_TrackNumber(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2* This,UINT32 *value) {
    return This->lpVtbl->get_TrackNumber(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_put_TrackNumber(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2* This,UINT32 value) {
    return This->lpVtbl->put_TrackNumber(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_get_Genres(__x_ABI_CWindows_CMedia_CIMusicDisplayProperties2* This,__FIVector_1_HSTRING **value) {
    return This->lpVtbl->get_Genres(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_IMusicDisplayProperties2 IID___x_ABI_CWindows_CMedia_CIMusicDisplayProperties2
#define IMusicDisplayProperties2Vtbl __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2Vtbl
#define IMusicDisplayProperties2 __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2
#define IMusicDisplayProperties2_QueryInterface __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_QueryInterface
#define IMusicDisplayProperties2_AddRef __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_AddRef
#define IMusicDisplayProperties2_Release __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_Release
#define IMusicDisplayProperties2_GetIids __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_GetIids
#define IMusicDisplayProperties2_GetRuntimeClassName __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_GetRuntimeClassName
#define IMusicDisplayProperties2_GetTrustLevel __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_GetTrustLevel
#define IMusicDisplayProperties2_get_AlbumTitle __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_get_AlbumTitle
#define IMusicDisplayProperties2_put_AlbumTitle __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_put_AlbumTitle
#define IMusicDisplayProperties2_get_TrackNumber __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_get_TrackNumber
#define IMusicDisplayProperties2_put_TrackNumber __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_put_TrackNumber
#define IMusicDisplayProperties2_get_Genres __x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_get_Genres
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CIMusicDisplayProperties2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPlaybackPositionChangeRequestedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs, 0xb4493f88, 0xeb28, 0x4961, 0x9c,0x14, 0x33,0x5e,0x44,0xf3,0xe1,0x25);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("b4493f88-eb28-4961-9c14-335e44f3e125")
            IPlaybackPositionChangeRequestedEventArgs : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_RequestedPlaybackPosition(
                    ABI::Windows::Foundation::TimeSpan *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs, 0xb4493f88, 0xeb28, 0x4961, 0x9c,0x14, 0x33,0x5e,0x44,0xf3,0xe1,0x25)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IPlaybackPositionChangeRequestedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_RequestedPlaybackPosition)(
        __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPlaybackPositionChangeRequestedEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_get_RequestedPlaybackPosition(This,value) (This)->lpVtbl->get_RequestedPlaybackPosition(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_AddRef(__x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_Release(__x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_GetIids(__x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPlaybackPositionChangeRequestedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_get_RequestedPlaybackPosition(__x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs* This,__x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_RequestedPlaybackPosition(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_IPlaybackPositionChangeRequestedEventArgs IID___x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs
#define IPlaybackPositionChangeRequestedEventArgsVtbl __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgsVtbl
#define IPlaybackPositionChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs
#define IPlaybackPositionChangeRequestedEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_QueryInterface
#define IPlaybackPositionChangeRequestedEventArgs_AddRef __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_AddRef
#define IPlaybackPositionChangeRequestedEventArgs_Release __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_Release
#define IPlaybackPositionChangeRequestedEventArgs_GetIids __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_GetIids
#define IPlaybackPositionChangeRequestedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_GetRuntimeClassName
#define IPlaybackPositionChangeRequestedEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_GetTrustLevel
#define IPlaybackPositionChangeRequestedEventArgs_get_RequestedPlaybackPosition __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_get_RequestedPlaybackPosition
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IPlaybackRateChangeRequestedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs, 0x2ce2c41f, 0x3cd6, 0x4f77, 0x9b,0xa7, 0xeb,0x27,0xc2,0x6a,0x21,0x40);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("2ce2c41f-3cd6-4f77-9ba7-eb27c26a2140")
            IPlaybackRateChangeRequestedEventArgs : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_RequestedPlaybackRate(
                    DOUBLE *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs, 0x2ce2c41f, 0x3cd6, 0x4f77, 0x9b,0xa7, 0xeb,0x27,0xc2,0x6a,0x21,0x40)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IPlaybackRateChangeRequestedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_RequestedPlaybackRate)(
        __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs *This,
        DOUBLE *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IPlaybackRateChangeRequestedEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_get_RequestedPlaybackRate(This,value) (This)->lpVtbl->get_RequestedPlaybackRate(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_AddRef(__x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_Release(__x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_GetIids(__x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IPlaybackRateChangeRequestedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_get_RequestedPlaybackRate(__x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs* This,DOUBLE *value) {
    return This->lpVtbl->get_RequestedPlaybackRate(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_IPlaybackRateChangeRequestedEventArgs IID___x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs
#define IPlaybackRateChangeRequestedEventArgsVtbl __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgsVtbl
#define IPlaybackRateChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs
#define IPlaybackRateChangeRequestedEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_QueryInterface
#define IPlaybackRateChangeRequestedEventArgs_AddRef __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_AddRef
#define IPlaybackRateChangeRequestedEventArgs_Release __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_Release
#define IPlaybackRateChangeRequestedEventArgs_GetIids __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_GetIids
#define IPlaybackRateChangeRequestedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_GetRuntimeClassName
#define IPlaybackRateChangeRequestedEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_GetTrustLevel
#define IPlaybackRateChangeRequestedEventArgs_get_RequestedPlaybackRate __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_get_RequestedPlaybackRate
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IShuffleEnabledChangeRequestedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs, 0x49b593fe, 0x4fd0, 0x4666, 0xa3,0x14, 0xc0,0xe0,0x19,0x40,0xd3,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("49b593fe-4fd0-4666-a314-c0e01940d302")
            IShuffleEnabledChangeRequestedEventArgs : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_RequestedShuffleEnabled(
                    boolean *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs, 0x49b593fe, 0x4fd0, 0x4666, 0xa3,0x14, 0xc0,0xe0,0x19,0x40,0xd3,0x02)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IShuffleEnabledChangeRequestedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_RequestedShuffleEnabled)(
        __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IShuffleEnabledChangeRequestedEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_get_RequestedShuffleEnabled(This,value) (This)->lpVtbl->get_RequestedShuffleEnabled(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_AddRef(__x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_Release(__x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_GetIids(__x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IShuffleEnabledChangeRequestedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_get_RequestedShuffleEnabled(__x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs* This,boolean *value) {
    return This->lpVtbl->get_RequestedShuffleEnabled(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_IShuffleEnabledChangeRequestedEventArgs IID___x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs
#define IShuffleEnabledChangeRequestedEventArgsVtbl __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgsVtbl
#define IShuffleEnabledChangeRequestedEventArgs __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs
#define IShuffleEnabledChangeRequestedEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_QueryInterface
#define IShuffleEnabledChangeRequestedEventArgs_AddRef __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_AddRef
#define IShuffleEnabledChangeRequestedEventArgs_Release __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_Release
#define IShuffleEnabledChangeRequestedEventArgs_GetIids __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_GetIids
#define IShuffleEnabledChangeRequestedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_GetRuntimeClassName
#define IShuffleEnabledChangeRequestedEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_GetTrustLevel
#define IShuffleEnabledChangeRequestedEventArgs_get_RequestedShuffleEnabled __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_get_RequestedShuffleEnabled
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISystemMediaTransportControls interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControls, 0x99fa3ff4, 0x1742, 0x42a6, 0x90,0x2e, 0x08,0x7d,0x41,0xf9,0x65,0xec);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("99fa3ff4-1742-42a6-902e-087d41f965ec")
            ISystemMediaTransportControls : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_PlaybackStatus(
                    ABI::Windows::Media::MediaPlaybackStatus *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_PlaybackStatus(
                    ABI::Windows::Media::MediaPlaybackStatus value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_DisplayUpdater(
                    ABI::Windows::Media::ISystemMediaTransportControlsDisplayUpdater **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_SoundLevel(
                    ABI::Windows::Media::SoundLevel *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsPlayEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsPlayEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsStopEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsStopEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsPauseEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsPauseEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsRecordEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsRecordEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsFastForwardEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsFastForwardEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsRewindEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsRewindEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsPreviousEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsPreviousEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsNextEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsNextEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsChannelUpEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsChannelUpEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_IsChannelDownEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_IsChannelDownEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE add_ButtonPressed(
                    ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsButtonPressedEventArgs* > *handler,
                    EventRegistrationToken *token) = 0;

                virtual HRESULT STDMETHODCALLTYPE remove_ButtonPressed(
                    EventRegistrationToken token) = 0;

                virtual HRESULT STDMETHODCALLTYPE add_PropertyChanged(
                    ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsPropertyChangedEventArgs* > *handler,
                    EventRegistrationToken *token) = 0;

                virtual HRESULT STDMETHODCALLTYPE remove_PropertyChanged(
                    EventRegistrationToken token) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls, 0x99fa3ff4, 0x1742, 0x42a6, 0x90,0x2e, 0x08,0x7d,0x41,0xf9,0x65,0xec)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        TrustLevel *trustLevel);

    /*** ISystemMediaTransportControls methods ***/
    HRESULT (STDMETHODCALLTYPE *get_PlaybackStatus)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        __x_ABI_CWindows_CMedia_CMediaPlaybackStatus *value);

    HRESULT (STDMETHODCALLTYPE *put_PlaybackStatus)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        __x_ABI_CWindows_CMedia_CMediaPlaybackStatus value);

    HRESULT (STDMETHODCALLTYPE *get_DisplayUpdater)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater **value);

    HRESULT (STDMETHODCALLTYPE *get_SoundLevel)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        __x_ABI_CWindows_CMedia_CSoundLevel *value);

    HRESULT (STDMETHODCALLTYPE *get_IsEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_IsPlayEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsPlayEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_IsStopEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsStopEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_IsPauseEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsPauseEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_IsRecordEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsRecordEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_IsFastForwardEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsFastForwardEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_IsRewindEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsRewindEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_IsPreviousEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsPreviousEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_IsNextEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsNextEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_IsChannelUpEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsChannelUpEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_IsChannelDownEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsChannelDownEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *add_ButtonPressed)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_ButtonPressed)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_PropertyChanged)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_PropertyChanged)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsVtbl;

interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControls {
    CONST_VTBL __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISystemMediaTransportControls methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_PlaybackStatus(This,value) (This)->lpVtbl->get_PlaybackStatus(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_PlaybackStatus(This,value) (This)->lpVtbl->put_PlaybackStatus(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_DisplayUpdater(This,value) (This)->lpVtbl->get_DisplayUpdater(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_SoundLevel(This,value) (This)->lpVtbl->get_SoundLevel(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsEnabled(This,value) (This)->lpVtbl->get_IsEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsEnabled(This,value) (This)->lpVtbl->put_IsEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsPlayEnabled(This,value) (This)->lpVtbl->get_IsPlayEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsPlayEnabled(This,value) (This)->lpVtbl->put_IsPlayEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsStopEnabled(This,value) (This)->lpVtbl->get_IsStopEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsStopEnabled(This,value) (This)->lpVtbl->put_IsStopEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsPauseEnabled(This,value) (This)->lpVtbl->get_IsPauseEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsPauseEnabled(This,value) (This)->lpVtbl->put_IsPauseEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsRecordEnabled(This,value) (This)->lpVtbl->get_IsRecordEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsRecordEnabled(This,value) (This)->lpVtbl->put_IsRecordEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsFastForwardEnabled(This,value) (This)->lpVtbl->get_IsFastForwardEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsFastForwardEnabled(This,value) (This)->lpVtbl->put_IsFastForwardEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsRewindEnabled(This,value) (This)->lpVtbl->get_IsRewindEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsRewindEnabled(This,value) (This)->lpVtbl->put_IsRewindEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsPreviousEnabled(This,value) (This)->lpVtbl->get_IsPreviousEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsPreviousEnabled(This,value) (This)->lpVtbl->put_IsPreviousEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsNextEnabled(This,value) (This)->lpVtbl->get_IsNextEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsNextEnabled(This,value) (This)->lpVtbl->put_IsNextEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsChannelUpEnabled(This,value) (This)->lpVtbl->get_IsChannelUpEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsChannelUpEnabled(This,value) (This)->lpVtbl->put_IsChannelUpEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsChannelDownEnabled(This,value) (This)->lpVtbl->get_IsChannelDownEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsChannelDownEnabled(This,value) (This)->lpVtbl->put_IsChannelDownEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_add_ButtonPressed(This,handler,token) (This)->lpVtbl->add_ButtonPressed(This,handler,token)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_remove_ButtonPressed(This,token) (This)->lpVtbl->remove_ButtonPressed(This,token)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_add_PropertyChanged(This,handler,token) (This)->lpVtbl->add_PropertyChanged(This,handler,token)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_remove_PropertyChanged(This,token) (This)->lpVtbl->remove_PropertyChanged(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_QueryInterface(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_AddRef(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_Release(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_GetIids(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_GetTrustLevel(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISystemMediaTransportControls methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_PlaybackStatus(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,__x_ABI_CWindows_CMedia_CMediaPlaybackStatus *value) {
    return This->lpVtbl->get_PlaybackStatus(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_PlaybackStatus(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,__x_ABI_CWindows_CMedia_CMediaPlaybackStatus value) {
    return This->lpVtbl->put_PlaybackStatus(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_DisplayUpdater(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater **value) {
    return This->lpVtbl->get_DisplayUpdater(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_SoundLevel(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,__x_ABI_CWindows_CMedia_CSoundLevel *value) {
    return This->lpVtbl->get_SoundLevel(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean *value) {
    return This->lpVtbl->get_IsEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean value) {
    return This->lpVtbl->put_IsEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsPlayEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean *value) {
    return This->lpVtbl->get_IsPlayEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsPlayEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean value) {
    return This->lpVtbl->put_IsPlayEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsStopEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean *value) {
    return This->lpVtbl->get_IsStopEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsStopEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean value) {
    return This->lpVtbl->put_IsStopEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsPauseEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean *value) {
    return This->lpVtbl->get_IsPauseEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsPauseEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean value) {
    return This->lpVtbl->put_IsPauseEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsRecordEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean *value) {
    return This->lpVtbl->get_IsRecordEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsRecordEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean value) {
    return This->lpVtbl->put_IsRecordEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsFastForwardEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean *value) {
    return This->lpVtbl->get_IsFastForwardEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsFastForwardEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean value) {
    return This->lpVtbl->put_IsFastForwardEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsRewindEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean *value) {
    return This->lpVtbl->get_IsRewindEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsRewindEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean value) {
    return This->lpVtbl->put_IsRewindEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsPreviousEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean *value) {
    return This->lpVtbl->get_IsPreviousEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsPreviousEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean value) {
    return This->lpVtbl->put_IsPreviousEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsNextEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean *value) {
    return This->lpVtbl->get_IsNextEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsNextEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean value) {
    return This->lpVtbl->put_IsNextEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsChannelUpEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean *value) {
    return This->lpVtbl->get_IsChannelUpEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsChannelUpEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean value) {
    return This->lpVtbl->put_IsChannelUpEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsChannelDownEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean *value) {
    return This->lpVtbl->get_IsChannelDownEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsChannelDownEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,boolean value) {
    return This->lpVtbl->put_IsChannelDownEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_add_ButtonPressed(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_ButtonPressed(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_remove_ButtonPressed(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_ButtonPressed(This,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_add_PropertyChanged(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_PropertyChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_remove_PropertyChanged(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_PropertyChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_ISystemMediaTransportControls IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControls
#define ISystemMediaTransportControlsVtbl __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsVtbl
#define ISystemMediaTransportControls __x_ABI_CWindows_CMedia_CISystemMediaTransportControls
#define ISystemMediaTransportControls_QueryInterface __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_QueryInterface
#define ISystemMediaTransportControls_AddRef __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_AddRef
#define ISystemMediaTransportControls_Release __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_Release
#define ISystemMediaTransportControls_GetIids __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_GetIids
#define ISystemMediaTransportControls_GetRuntimeClassName __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_GetRuntimeClassName
#define ISystemMediaTransportControls_GetTrustLevel __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_GetTrustLevel
#define ISystemMediaTransportControls_get_PlaybackStatus __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_PlaybackStatus
#define ISystemMediaTransportControls_put_PlaybackStatus __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_PlaybackStatus
#define ISystemMediaTransportControls_get_DisplayUpdater __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_DisplayUpdater
#define ISystemMediaTransportControls_get_SoundLevel __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_SoundLevel
#define ISystemMediaTransportControls_get_IsEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsEnabled
#define ISystemMediaTransportControls_put_IsEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsEnabled
#define ISystemMediaTransportControls_get_IsPlayEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsPlayEnabled
#define ISystemMediaTransportControls_put_IsPlayEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsPlayEnabled
#define ISystemMediaTransportControls_get_IsStopEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsStopEnabled
#define ISystemMediaTransportControls_put_IsStopEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsStopEnabled
#define ISystemMediaTransportControls_get_IsPauseEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsPauseEnabled
#define ISystemMediaTransportControls_put_IsPauseEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsPauseEnabled
#define ISystemMediaTransportControls_get_IsRecordEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsRecordEnabled
#define ISystemMediaTransportControls_put_IsRecordEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsRecordEnabled
#define ISystemMediaTransportControls_get_IsFastForwardEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsFastForwardEnabled
#define ISystemMediaTransportControls_put_IsFastForwardEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsFastForwardEnabled
#define ISystemMediaTransportControls_get_IsRewindEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsRewindEnabled
#define ISystemMediaTransportControls_put_IsRewindEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsRewindEnabled
#define ISystemMediaTransportControls_get_IsPreviousEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsPreviousEnabled
#define ISystemMediaTransportControls_put_IsPreviousEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsPreviousEnabled
#define ISystemMediaTransportControls_get_IsNextEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsNextEnabled
#define ISystemMediaTransportControls_put_IsNextEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsNextEnabled
#define ISystemMediaTransportControls_get_IsChannelUpEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsChannelUpEnabled
#define ISystemMediaTransportControls_put_IsChannelUpEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsChannelUpEnabled
#define ISystemMediaTransportControls_get_IsChannelDownEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_get_IsChannelDownEnabled
#define ISystemMediaTransportControls_put_IsChannelDownEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_put_IsChannelDownEnabled
#define ISystemMediaTransportControls_add_ButtonPressed __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_add_ButtonPressed
#define ISystemMediaTransportControls_remove_ButtonPressed __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_remove_ButtonPressed
#define ISystemMediaTransportControls_add_PropertyChanged __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_add_PropertyChanged
#define ISystemMediaTransportControls_remove_PropertyChanged __x_ABI_CWindows_CMedia_CISystemMediaTransportControls_remove_PropertyChanged
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISystemMediaTransportControls2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControls2, 0xea98d2f6, 0x7f3c, 0x4af2, 0xa5,0x86, 0x72,0x88,0x98,0x08,0xef,0xb1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("ea98d2f6-7f3c-4af2-a586-72889808efb1")
            ISystemMediaTransportControls2 : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_AutoRepeatMode(
                    ABI::Windows::Media::MediaPlaybackAutoRepeatMode *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_AutoRepeatMode(
                    ABI::Windows::Media::MediaPlaybackAutoRepeatMode value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_ShuffleEnabled(
                    boolean *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_ShuffleEnabled(
                    boolean value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_PlaybackRate(
                    DOUBLE *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_PlaybackRate(
                    DOUBLE value) = 0;

                virtual HRESULT STDMETHODCALLTYPE UpdateTimelineProperties(
                    ABI::Windows::Media::ISystemMediaTransportControlsTimelineProperties *timeline_properties) = 0;

                virtual HRESULT STDMETHODCALLTYPE add_PlaybackPositionChangeRequested(
                    ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackPositionChangeRequestedEventArgs* > *handler,
                    EventRegistrationToken *token) = 0;

                virtual HRESULT STDMETHODCALLTYPE remove_PlaybackPositionChangeRequested(
                    EventRegistrationToken token) = 0;

                virtual HRESULT STDMETHODCALLTYPE add_PlaybackRateChangeRequested(
                    ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackRateChangeRequestedEventArgs* > *handler,
                    EventRegistrationToken *token) = 0;

                virtual HRESULT STDMETHODCALLTYPE remove_PlaybackRateChangeRequested(
                    EventRegistrationToken token) = 0;

                virtual HRESULT STDMETHODCALLTYPE add_ShuffleEnabledChangeRequested(
                    ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::ShuffleEnabledChangeRequestedEventArgs* > *handler,
                    EventRegistrationToken *token) = 0;

                virtual HRESULT STDMETHODCALLTYPE remove_ShuffleEnabledChangeRequested(
                    EventRegistrationToken token) = 0;

                virtual HRESULT STDMETHODCALLTYPE add_AutoRepeatModeChangeRequested(
                    ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::AutoRepeatModeChangeRequestedEventArgs* > *handler,
                    EventRegistrationToken *token) = 0;

                virtual HRESULT STDMETHODCALLTYPE remove_AutoRepeatModeChangeRequested(
                    EventRegistrationToken token) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2, 0xea98d2f6, 0x7f3c, 0x4af2, 0xa5,0x86, 0x72,0x88,0x98,0x08,0xef,0xb1)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        TrustLevel *trustLevel);

    /*** ISystemMediaTransportControls2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AutoRepeatMode)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        __x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode *value);

    HRESULT (STDMETHODCALLTYPE *put_AutoRepeatMode)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        __x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode value);

    HRESULT (STDMETHODCALLTYPE *get_ShuffleEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_ShuffleEnabled)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_PlaybackRate)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *put_PlaybackRate)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        DOUBLE value);

    HRESULT (STDMETHODCALLTYPE *UpdateTimelineProperties)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *timeline_properties);

    HRESULT (STDMETHODCALLTYPE *add_PlaybackPositionChangeRequested)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_PlaybackPositionChangeRequested)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_PlaybackRateChangeRequested)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_PlaybackRateChangeRequested)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_ShuffleEnabledChangeRequested)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_ShuffleEnabledChangeRequested)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_AutoRepeatModeChangeRequested)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_AutoRepeatModeChangeRequested)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2Vtbl;

interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2 {
    CONST_VTBL __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISystemMediaTransportControls2 methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_get_AutoRepeatMode(This,value) (This)->lpVtbl->get_AutoRepeatMode(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_put_AutoRepeatMode(This,value) (This)->lpVtbl->put_AutoRepeatMode(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_get_ShuffleEnabled(This,value) (This)->lpVtbl->get_ShuffleEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_put_ShuffleEnabled(This,value) (This)->lpVtbl->put_ShuffleEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_get_PlaybackRate(This,value) (This)->lpVtbl->get_PlaybackRate(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_put_PlaybackRate(This,value) (This)->lpVtbl->put_PlaybackRate(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_UpdateTimelineProperties(This,timeline_properties) (This)->lpVtbl->UpdateTimelineProperties(This,timeline_properties)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_PlaybackPositionChangeRequested(This,handler,token) (This)->lpVtbl->add_PlaybackPositionChangeRequested(This,handler,token)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_PlaybackPositionChangeRequested(This,token) (This)->lpVtbl->remove_PlaybackPositionChangeRequested(This,token)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_PlaybackRateChangeRequested(This,handler,token) (This)->lpVtbl->add_PlaybackRateChangeRequested(This,handler,token)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_PlaybackRateChangeRequested(This,token) (This)->lpVtbl->remove_PlaybackRateChangeRequested(This,token)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_ShuffleEnabledChangeRequested(This,handler,token) (This)->lpVtbl->add_ShuffleEnabledChangeRequested(This,handler,token)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_ShuffleEnabledChangeRequested(This,token) (This)->lpVtbl->remove_ShuffleEnabledChangeRequested(This,token)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_AutoRepeatModeChangeRequested(This,handler,token) (This)->lpVtbl->add_AutoRepeatModeChangeRequested(This,handler,token)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_AutoRepeatModeChangeRequested(This,token) (This)->lpVtbl->remove_AutoRepeatModeChangeRequested(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_QueryInterface(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_AddRef(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_Release(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_GetIids(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_GetTrustLevel(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISystemMediaTransportControls2 methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_get_AutoRepeatMode(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,__x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode *value) {
    return This->lpVtbl->get_AutoRepeatMode(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_put_AutoRepeatMode(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,__x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode value) {
    return This->lpVtbl->put_AutoRepeatMode(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_get_ShuffleEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,boolean *value) {
    return This->lpVtbl->get_ShuffleEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_put_ShuffleEnabled(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,boolean value) {
    return This->lpVtbl->put_ShuffleEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_get_PlaybackRate(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,DOUBLE *value) {
    return This->lpVtbl->get_PlaybackRate(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_put_PlaybackRate(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,DOUBLE value) {
    return This->lpVtbl->put_PlaybackRate(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_UpdateTimelineProperties(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *timeline_properties) {
    return This->lpVtbl->UpdateTimelineProperties(This,timeline_properties);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_PlaybackPositionChangeRequested(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_PlaybackPositionChangeRequested(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_PlaybackPositionChangeRequested(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_PlaybackPositionChangeRequested(This,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_PlaybackRateChangeRequested(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_PlaybackRateChangeRequested(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_PlaybackRateChangeRequested(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_PlaybackRateChangeRequested(This,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_ShuffleEnabledChangeRequested(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_ShuffleEnabledChangeRequested(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_ShuffleEnabledChangeRequested(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_ShuffleEnabledChangeRequested(This,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_AutoRepeatModeChangeRequested(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_AutoRepeatModeChangeRequested(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_AutoRepeatModeChangeRequested(__x_ABI_CWindows_CMedia_CISystemMediaTransportControls2* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_AutoRepeatModeChangeRequested(This,token);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_ISystemMediaTransportControls2 IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControls2
#define ISystemMediaTransportControls2Vtbl __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2Vtbl
#define ISystemMediaTransportControls2 __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2
#define ISystemMediaTransportControls2_QueryInterface __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_QueryInterface
#define ISystemMediaTransportControls2_AddRef __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_AddRef
#define ISystemMediaTransportControls2_Release __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_Release
#define ISystemMediaTransportControls2_GetIids __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_GetIids
#define ISystemMediaTransportControls2_GetRuntimeClassName __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_GetRuntimeClassName
#define ISystemMediaTransportControls2_GetTrustLevel __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_GetTrustLevel
#define ISystemMediaTransportControls2_get_AutoRepeatMode __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_get_AutoRepeatMode
#define ISystemMediaTransportControls2_put_AutoRepeatMode __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_put_AutoRepeatMode
#define ISystemMediaTransportControls2_get_ShuffleEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_get_ShuffleEnabled
#define ISystemMediaTransportControls2_put_ShuffleEnabled __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_put_ShuffleEnabled
#define ISystemMediaTransportControls2_get_PlaybackRate __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_get_PlaybackRate
#define ISystemMediaTransportControls2_put_PlaybackRate __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_put_PlaybackRate
#define ISystemMediaTransportControls2_UpdateTimelineProperties __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_UpdateTimelineProperties
#define ISystemMediaTransportControls2_add_PlaybackPositionChangeRequested __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_PlaybackPositionChangeRequested
#define ISystemMediaTransportControls2_remove_PlaybackPositionChangeRequested __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_PlaybackPositionChangeRequested
#define ISystemMediaTransportControls2_add_PlaybackRateChangeRequested __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_PlaybackRateChangeRequested
#define ISystemMediaTransportControls2_remove_PlaybackRateChangeRequested __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_PlaybackRateChangeRequested
#define ISystemMediaTransportControls2_add_ShuffleEnabledChangeRequested __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_ShuffleEnabledChangeRequested
#define ISystemMediaTransportControls2_remove_ShuffleEnabledChangeRequested __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_ShuffleEnabledChangeRequested
#define ISystemMediaTransportControls2_add_AutoRepeatModeChangeRequested __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_add_AutoRepeatModeChangeRequested
#define ISystemMediaTransportControls2_remove_AutoRepeatModeChangeRequested __x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_remove_AutoRepeatModeChangeRequested
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CISystemMediaTransportControls2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISystemMediaTransportControlsDisplayUpdater interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater, 0x8abbc53e, 0xfa55, 0x4ecf, 0xad,0x8e, 0xc9,0x84,0xe5,0xdd,0x15,0x50);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("8abbc53e-fa55-4ecf-ad8e-c984e5dd1550")
            ISystemMediaTransportControlsDisplayUpdater : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Type(
                    ABI::Windows::Media::MediaPlaybackType *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Type(
                    ABI::Windows::Media::MediaPlaybackType value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_AppMediaId(
                    HSTRING *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_AppMediaId(
                    HSTRING value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Thumbnail(
                    ABI::Windows::Storage::Streams::IRandomAccessStreamReference **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Thumbnail(
                    ABI::Windows::Storage::Streams::IRandomAccessStreamReference *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MusicProperties(
                    ABI::Windows::Media::IMusicDisplayProperties **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_VideoProperties(
                    ABI::Windows::Media::IVideoDisplayProperties **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_ImageProperties(
                    ABI::Windows::Media::IImageDisplayProperties **value) = 0;

                virtual HRESULT STDMETHODCALLTYPE CopyFromFileAsync(
                    ABI::Windows::Media::MediaPlaybackType type,
                    ABI::Windows::Storage::IStorageFile *source,
                    ABI::Windows::Foundation::IAsyncOperation<boolean > **operation) = 0;

                virtual HRESULT STDMETHODCALLTYPE ClearAll(
                    ) = 0;

                virtual HRESULT STDMETHODCALLTYPE Update(
                    ) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater, 0x8abbc53e, 0xfa55, 0x4ecf, 0xad,0x8e, 0xc9,0x84,0xe5,0xdd,0x15,0x50)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdaterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        TrustLevel *trustLevel);

    /*** ISystemMediaTransportControlsDisplayUpdater methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        __x_ABI_CWindows_CMedia_CMediaPlaybackType *value);

    HRESULT (STDMETHODCALLTYPE *put_Type)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        __x_ABI_CWindows_CMedia_CMediaPlaybackType value);

    HRESULT (STDMETHODCALLTYPE *get_AppMediaId)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_AppMediaId)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *get_Thumbnail)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **value);

    HRESULT (STDMETHODCALLTYPE *put_Thumbnail)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference *value);

    HRESULT (STDMETHODCALLTYPE *get_MusicProperties)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        __x_ABI_CWindows_CMedia_CIMusicDisplayProperties **value);

    HRESULT (STDMETHODCALLTYPE *get_VideoProperties)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        __x_ABI_CWindows_CMedia_CIVideoDisplayProperties **value);

    HRESULT (STDMETHODCALLTYPE *get_ImageProperties)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        __x_ABI_CWindows_CMedia_CIImageDisplayProperties **value);

    HRESULT (STDMETHODCALLTYPE *CopyFromFileAsync)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This,
        __x_ABI_CWindows_CMedia_CMediaPlaybackType type,
        __x_ABI_CWindows_CStorage_CIStorageFile *source,
        __FIAsyncOperation_1_boolean **operation);

    HRESULT (STDMETHODCALLTYPE *ClearAll)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This);

    HRESULT (STDMETHODCALLTYPE *Update)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater *This);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdaterVtbl;

interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater {
    CONST_VTBL __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdaterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISystemMediaTransportControlsDisplayUpdater methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_Type(This,value) (This)->lpVtbl->get_Type(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_put_Type(This,value) (This)->lpVtbl->put_Type(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_AppMediaId(This,value) (This)->lpVtbl->get_AppMediaId(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_put_AppMediaId(This,value) (This)->lpVtbl->put_AppMediaId(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_Thumbnail(This,value) (This)->lpVtbl->get_Thumbnail(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_put_Thumbnail(This,value) (This)->lpVtbl->put_Thumbnail(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_MusicProperties(This,value) (This)->lpVtbl->get_MusicProperties(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_VideoProperties(This,value) (This)->lpVtbl->get_VideoProperties(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_ImageProperties(This,value) (This)->lpVtbl->get_ImageProperties(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_CopyFromFileAsync(This,type,source,operation) (This)->lpVtbl->CopyFromFileAsync(This,type,source,operation)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_ClearAll(This) (This)->lpVtbl->ClearAll(This)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_Update(This) (This)->lpVtbl->Update(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_QueryInterface(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_AddRef(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_Release(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_GetIids(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_GetTrustLevel(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISystemMediaTransportControlsDisplayUpdater methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_Type(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,__x_ABI_CWindows_CMedia_CMediaPlaybackType *value) {
    return This->lpVtbl->get_Type(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_put_Type(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,__x_ABI_CWindows_CMedia_CMediaPlaybackType value) {
    return This->lpVtbl->put_Type(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_AppMediaId(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,HSTRING *value) {
    return This->lpVtbl->get_AppMediaId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_put_AppMediaId(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,HSTRING value) {
    return This->lpVtbl->put_AppMediaId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_Thumbnail(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **value) {
    return This->lpVtbl->get_Thumbnail(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_put_Thumbnail(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference *value) {
    return This->lpVtbl->put_Thumbnail(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_MusicProperties(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,__x_ABI_CWindows_CMedia_CIMusicDisplayProperties **value) {
    return This->lpVtbl->get_MusicProperties(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_VideoProperties(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,__x_ABI_CWindows_CMedia_CIVideoDisplayProperties **value) {
    return This->lpVtbl->get_VideoProperties(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_ImageProperties(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,__x_ABI_CWindows_CMedia_CIImageDisplayProperties **value) {
    return This->lpVtbl->get_ImageProperties(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_CopyFromFileAsync(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This,__x_ABI_CWindows_CMedia_CMediaPlaybackType type,__x_ABI_CWindows_CStorage_CIStorageFile *source,__FIAsyncOperation_1_boolean **operation) {
    return This->lpVtbl->CopyFromFileAsync(This,type,source,operation);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_ClearAll(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This) {
    return This->lpVtbl->ClearAll(This);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_Update(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater* This) {
    return This->lpVtbl->Update(This);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_ISystemMediaTransportControlsDisplayUpdater IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater
#define ISystemMediaTransportControlsDisplayUpdaterVtbl __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdaterVtbl
#define ISystemMediaTransportControlsDisplayUpdater __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater
#define ISystemMediaTransportControlsDisplayUpdater_QueryInterface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_QueryInterface
#define ISystemMediaTransportControlsDisplayUpdater_AddRef __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_AddRef
#define ISystemMediaTransportControlsDisplayUpdater_Release __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_Release
#define ISystemMediaTransportControlsDisplayUpdater_GetIids __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_GetIids
#define ISystemMediaTransportControlsDisplayUpdater_GetRuntimeClassName __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_GetRuntimeClassName
#define ISystemMediaTransportControlsDisplayUpdater_GetTrustLevel __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_GetTrustLevel
#define ISystemMediaTransportControlsDisplayUpdater_get_Type __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_Type
#define ISystemMediaTransportControlsDisplayUpdater_put_Type __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_put_Type
#define ISystemMediaTransportControlsDisplayUpdater_get_AppMediaId __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_AppMediaId
#define ISystemMediaTransportControlsDisplayUpdater_put_AppMediaId __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_put_AppMediaId
#define ISystemMediaTransportControlsDisplayUpdater_get_Thumbnail __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_Thumbnail
#define ISystemMediaTransportControlsDisplayUpdater_put_Thumbnail __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_put_Thumbnail
#define ISystemMediaTransportControlsDisplayUpdater_get_MusicProperties __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_MusicProperties
#define ISystemMediaTransportControlsDisplayUpdater_get_VideoProperties __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_VideoProperties
#define ISystemMediaTransportControlsDisplayUpdater_get_ImageProperties __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_get_ImageProperties
#define ISystemMediaTransportControlsDisplayUpdater_CopyFromFileAsync __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_CopyFromFileAsync
#define ISystemMediaTransportControlsDisplayUpdater_ClearAll __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_ClearAll
#define ISystemMediaTransportControlsDisplayUpdater_Update __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_Update
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsDisplayUpdater_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISystemMediaTransportControlsButtonPressedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs, 0xb7f47116, 0xa56f, 0x4dc8, 0x9e,0x11, 0x92,0x03,0x1f,0x4a,0x87,0xc2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("b7f47116-a56f-4dc8-9e11-92031f4a87c2")
            ISystemMediaTransportControlsButtonPressedEventArgs : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Button(
                    ABI::Windows::Media::SystemMediaTransportControlsButton *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs, 0xb7f47116, 0xa56f, 0x4dc8, 0x9e,0x11, 0x92,0x03,0x1f,0x4a,0x87,0xc2)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs *This,
        TrustLevel *trustLevel);

    /*** ISystemMediaTransportControlsButtonPressedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Button)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs *This,
        __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButton *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISystemMediaTransportControlsButtonPressedEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_get_Button(This,value) (This)->lpVtbl->get_Button(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_AddRef(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_Release(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_GetIids(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISystemMediaTransportControlsButtonPressedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_get_Button(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs* This,__x_ABI_CWindows_CMedia_CSystemMediaTransportControlsButton *value) {
    return This->lpVtbl->get_Button(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_ISystemMediaTransportControlsButtonPressedEventArgs IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs
#define ISystemMediaTransportControlsButtonPressedEventArgsVtbl __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgsVtbl
#define ISystemMediaTransportControlsButtonPressedEventArgs __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs
#define ISystemMediaTransportControlsButtonPressedEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_QueryInterface
#define ISystemMediaTransportControlsButtonPressedEventArgs_AddRef __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_AddRef
#define ISystemMediaTransportControlsButtonPressedEventArgs_Release __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_Release
#define ISystemMediaTransportControlsButtonPressedEventArgs_GetIids __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_GetIids
#define ISystemMediaTransportControlsButtonPressedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_GetRuntimeClassName
#define ISystemMediaTransportControlsButtonPressedEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_GetTrustLevel
#define ISystemMediaTransportControlsButtonPressedEventArgs_get_Button __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_get_Button
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISystemMediaTransportControlsPropertyChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs, 0xd0ca0936, 0x339b, 0x4cb3, 0x8e,0xeb, 0x73,0x76,0x07,0xf5,0x6e,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("d0ca0936-339b-4cb3-8eeb-737607f56e08")
            ISystemMediaTransportControlsPropertyChangedEventArgs : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_Property(
                    ABI::Windows::Media::SystemMediaTransportControlsProperty *value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs, 0xd0ca0936, 0x339b, 0x4cb3, 0x8e,0xeb, 0x73,0x76,0x07,0xf5,0x6e,0x08)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs *This,
        TrustLevel *trustLevel);

    /*** ISystemMediaTransportControlsPropertyChangedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Property)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs *This,
        __x_ABI_CWindows_CMedia_CSystemMediaTransportControlsProperty *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISystemMediaTransportControlsPropertyChangedEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_get_Property(This,value) (This)->lpVtbl->get_Property(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_AddRef(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_Release(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_GetIids(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISystemMediaTransportControlsPropertyChangedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_get_Property(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs* This,__x_ABI_CWindows_CMedia_CSystemMediaTransportControlsProperty *value) {
    return This->lpVtbl->get_Property(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_ISystemMediaTransportControlsPropertyChangedEventArgs IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs
#define ISystemMediaTransportControlsPropertyChangedEventArgsVtbl __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgsVtbl
#define ISystemMediaTransportControlsPropertyChangedEventArgs __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs
#define ISystemMediaTransportControlsPropertyChangedEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_QueryInterface
#define ISystemMediaTransportControlsPropertyChangedEventArgs_AddRef __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_AddRef
#define ISystemMediaTransportControlsPropertyChangedEventArgs_Release __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_Release
#define ISystemMediaTransportControlsPropertyChangedEventArgs_GetIids __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_GetIids
#define ISystemMediaTransportControlsPropertyChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_GetRuntimeClassName
#define ISystemMediaTransportControlsPropertyChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_GetTrustLevel
#define ISystemMediaTransportControlsPropertyChangedEventArgs_get_Property __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_get_Property
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISystemMediaTransportControlsTimelineProperties interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties, 0x5125316a, 0xc3a2, 0x475b, 0x85,0x07, 0x93,0x53,0x4d,0xc8,0x8f,0x15);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            MIDL_INTERFACE("5125316a-c3a2-475b-8507-93534dc88f15")
            ISystemMediaTransportControlsTimelineProperties : public IInspectable
            {
                virtual HRESULT STDMETHODCALLTYPE get_StartTime(
                    ABI::Windows::Foundation::TimeSpan *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_StartTime(
                    ABI::Windows::Foundation::TimeSpan value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_EndTime(
                    ABI::Windows::Foundation::TimeSpan *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_EndTime(
                    ABI::Windows::Foundation::TimeSpan value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MinSeekTime(
                    ABI::Windows::Foundation::TimeSpan *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_MinSeekTime(
                    ABI::Windows::Foundation::TimeSpan value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_MaxSeekTime(
                    ABI::Windows::Foundation::TimeSpan *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_MaxSeekTime(
                    ABI::Windows::Foundation::TimeSpan value) = 0;

                virtual HRESULT STDMETHODCALLTYPE get_Position(
                    ABI::Windows::Foundation::TimeSpan *value) = 0;

                virtual HRESULT STDMETHODCALLTYPE put_Position(
                    ABI::Windows::Foundation::TimeSpan value) = 0;

            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties, 0x5125316a, 0xc3a2, 0x475b, 0x85,0x07, 0x93,0x53,0x4d,0xc8,0x8f,0x15)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelinePropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        TrustLevel *trustLevel);

    /*** ISystemMediaTransportControlsTimelineProperties methods ***/
    HRESULT (STDMETHODCALLTYPE *get_StartTime)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *put_StartTime)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan value);

    HRESULT (STDMETHODCALLTYPE *get_EndTime)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *put_EndTime)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan value);

    HRESULT (STDMETHODCALLTYPE *get_MinSeekTime)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *put_MinSeekTime)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan value);

    HRESULT (STDMETHODCALLTYPE *get_MaxSeekTime)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *put_MaxSeekTime)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan value);

    HRESULT (STDMETHODCALLTYPE *get_Position)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *put_Position)(
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelinePropertiesVtbl;

interface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties {
    CONST_VTBL __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelinePropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISystemMediaTransportControlsTimelineProperties methods ***/
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_StartTime(This,value) (This)->lpVtbl->get_StartTime(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_StartTime(This,value) (This)->lpVtbl->put_StartTime(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_EndTime(This,value) (This)->lpVtbl->get_EndTime(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_EndTime(This,value) (This)->lpVtbl->put_EndTime(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_MinSeekTime(This,value) (This)->lpVtbl->get_MinSeekTime(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_MinSeekTime(This,value) (This)->lpVtbl->put_MinSeekTime(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_MaxSeekTime(This,value) (This)->lpVtbl->get_MaxSeekTime(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_MaxSeekTime(This,value) (This)->lpVtbl->put_MaxSeekTime(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_Position(This,value) (This)->lpVtbl->get_Position(This,value)
#define __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_Position(This,value) (This)->lpVtbl->put_Position(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_QueryInterface(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_AddRef(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_Release(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_GetIids(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_GetTrustLevel(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISystemMediaTransportControlsTimelineProperties methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_StartTime(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,__x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_StartTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_StartTime(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,__x_ABI_CWindows_CFoundation_CTimeSpan value) {
    return This->lpVtbl->put_StartTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_EndTime(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,__x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_EndTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_EndTime(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,__x_ABI_CWindows_CFoundation_CTimeSpan value) {
    return This->lpVtbl->put_EndTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_MinSeekTime(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,__x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_MinSeekTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_MinSeekTime(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,__x_ABI_CWindows_CFoundation_CTimeSpan value) {
    return This->lpVtbl->put_MinSeekTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_MaxSeekTime(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,__x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_MaxSeekTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_MaxSeekTime(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,__x_ABI_CWindows_CFoundation_CTimeSpan value) {
    return This->lpVtbl->put_MaxSeekTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_Position(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,__x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_Position(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_Position(__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties* This,__x_ABI_CWindows_CFoundation_CTimeSpan value) {
    return This->lpVtbl->put_Position(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media
#define IID_ISystemMediaTransportControlsTimelineProperties IID___x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties
#define ISystemMediaTransportControlsTimelinePropertiesVtbl __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelinePropertiesVtbl
#define ISystemMediaTransportControlsTimelineProperties __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties
#define ISystemMediaTransportControlsTimelineProperties_QueryInterface __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_QueryInterface
#define ISystemMediaTransportControlsTimelineProperties_AddRef __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_AddRef
#define ISystemMediaTransportControlsTimelineProperties_Release __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_Release
#define ISystemMediaTransportControlsTimelineProperties_GetIids __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_GetIids
#define ISystemMediaTransportControlsTimelineProperties_GetRuntimeClassName __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_GetRuntimeClassName
#define ISystemMediaTransportControlsTimelineProperties_GetTrustLevel __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_GetTrustLevel
#define ISystemMediaTransportControlsTimelineProperties_get_StartTime __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_StartTime
#define ISystemMediaTransportControlsTimelineProperties_put_StartTime __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_StartTime
#define ISystemMediaTransportControlsTimelineProperties_get_EndTime __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_EndTime
#define ISystemMediaTransportControlsTimelineProperties_put_EndTime __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_EndTime
#define ISystemMediaTransportControlsTimelineProperties_get_MinSeekTime __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_MinSeekTime
#define ISystemMediaTransportControlsTimelineProperties_put_MinSeekTime __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_MinSeekTime
#define ISystemMediaTransportControlsTimelineProperties_get_MaxSeekTime __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_MaxSeekTime
#define ISystemMediaTransportControlsTimelineProperties_put_MaxSeekTime __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_MaxSeekTime
#define ISystemMediaTransportControlsTimelineProperties_get_Position __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_get_Position
#define ISystemMediaTransportControlsTimelineProperties_put_Position __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_put_Position
#endif /* WIDL_using_Windows_Media */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CISystemMediaTransportControlsTimelineProperties_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.AutoRepeatModeChangeRequestedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_AutoRepeatModeChangeRequestedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_AutoRepeatModeChangeRequestedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_AutoRepeatModeChangeRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','A','u','t','o','R','e','p','e','a','t','M','o','d','e','C','h','a','n','g','e','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_AutoRepeatModeChangeRequestedEventArgs[] = L"Windows.Media.AutoRepeatModeChangeRequestedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_AutoRepeatModeChangeRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','A','u','t','o','R','e','p','e','a','t','M','o','d','e','C','h','a','n','g','e','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_AutoRepeatModeChangeRequestedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.ImageDisplayProperties
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_ImageDisplayProperties_DEFINED
#define RUNTIMECLASS_Windows_Media_ImageDisplayProperties_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_ImageDisplayProperties[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','I','m','a','g','e','D','i','s','p','l','a','y','P','r','o','p','e','r','t','i','e','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_ImageDisplayProperties[] = L"Windows.Media.ImageDisplayProperties";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_ImageDisplayProperties[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','I','m','a','g','e','D','i','s','p','l','a','y','P','r','o','p','e','r','t','i','e','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_ImageDisplayProperties_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.MusicDisplayProperties
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_MusicDisplayProperties_DEFINED
#define RUNTIMECLASS_Windows_Media_MusicDisplayProperties_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_MusicDisplayProperties[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','M','u','s','i','c','D','i','s','p','l','a','y','P','r','o','p','e','r','t','i','e','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_MusicDisplayProperties[] = L"Windows.Media.MusicDisplayProperties";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_MusicDisplayProperties[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','M','u','s','i','c','D','i','s','p','l','a','y','P','r','o','p','e','r','t','i','e','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_MusicDisplayProperties_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.PlaybackPositionChangeRequestedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_PlaybackPositionChangeRequestedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_PlaybackPositionChangeRequestedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_PlaybackPositionChangeRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','P','l','a','y','b','a','c','k','P','o','s','i','t','i','o','n','C','h','a','n','g','e','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_PlaybackPositionChangeRequestedEventArgs[] = L"Windows.Media.PlaybackPositionChangeRequestedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_PlaybackPositionChangeRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','P','l','a','y','b','a','c','k','P','o','s','i','t','i','o','n','C','h','a','n','g','e','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_PlaybackPositionChangeRequestedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.PlaybackRateChangeRequestedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_PlaybackRateChangeRequestedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_PlaybackRateChangeRequestedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_PlaybackRateChangeRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','P','l','a','y','b','a','c','k','R','a','t','e','C','h','a','n','g','e','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_PlaybackRateChangeRequestedEventArgs[] = L"Windows.Media.PlaybackRateChangeRequestedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_PlaybackRateChangeRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','P','l','a','y','b','a','c','k','R','a','t','e','C','h','a','n','g','e','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_PlaybackRateChangeRequestedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.ShuffleEnabledChangeRequestedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_ShuffleEnabledChangeRequestedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_ShuffleEnabledChangeRequestedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_ShuffleEnabledChangeRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','h','u','f','f','l','e','E','n','a','b','l','e','d','C','h','a','n','g','e','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_ShuffleEnabledChangeRequestedEventArgs[] = L"Windows.Media.ShuffleEnabledChangeRequestedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_ShuffleEnabledChangeRequestedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','h','u','f','f','l','e','E','n','a','b','l','e','d','C','h','a','n','g','e','R','e','q','u','e','s','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_ShuffleEnabledChangeRequestedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SystemMediaTransportControls
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SystemMediaTransportControls_DEFINED
#define RUNTIMECLASS_Windows_Media_SystemMediaTransportControls_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControls[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','y','s','t','e','m','M','e','d','i','a','T','r','a','n','s','p','o','r','t','C','o','n','t','r','o','l','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControls[] = L"Windows.Media.SystemMediaTransportControls";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControls[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','y','s','t','e','m','M','e','d','i','a','T','r','a','n','s','p','o','r','t','C','o','n','t','r','o','l','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SystemMediaTransportControls_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SystemMediaTransportControlsButtonPressedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsButtonPressedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsButtonPressedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsButtonPressedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','y','s','t','e','m','M','e','d','i','a','T','r','a','n','s','p','o','r','t','C','o','n','t','r','o','l','s','B','u','t','t','o','n','P','r','e','s','s','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsButtonPressedEventArgs[] = L"Windows.Media.SystemMediaTransportControlsButtonPressedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsButtonPressedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','y','s','t','e','m','M','e','d','i','a','T','r','a','n','s','p','o','r','t','C','o','n','t','r','o','l','s','B','u','t','t','o','n','P','r','e','s','s','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsButtonPressedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SystemMediaTransportControlsDisplayUpdater
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsDisplayUpdater_DEFINED
#define RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsDisplayUpdater_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsDisplayUpdater[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','y','s','t','e','m','M','e','d','i','a','T','r','a','n','s','p','o','r','t','C','o','n','t','r','o','l','s','D','i','s','p','l','a','y','U','p','d','a','t','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsDisplayUpdater[] = L"Windows.Media.SystemMediaTransportControlsDisplayUpdater";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsDisplayUpdater[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','y','s','t','e','m','M','e','d','i','a','T','r','a','n','s','p','o','r','t','C','o','n','t','r','o','l','s','D','i','s','p','l','a','y','U','p','d','a','t','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsDisplayUpdater_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SystemMediaTransportControlsPropertyChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsPropertyChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsPropertyChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsPropertyChangedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','y','s','t','e','m','M','e','d','i','a','T','r','a','n','s','p','o','r','t','C','o','n','t','r','o','l','s','P','r','o','p','e','r','t','y','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsPropertyChangedEventArgs[] = L"Windows.Media.SystemMediaTransportControlsPropertyChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsPropertyChangedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','y','s','t','e','m','M','e','d','i','a','T','r','a','n','s','p','o','r','t','C','o','n','t','r','o','l','s','P','r','o','p','e','r','t','y','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsPropertyChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SystemMediaTransportControlsTimelineProperties
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsTimelineProperties_DEFINED
#define RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsTimelineProperties_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsTimelineProperties[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','y','s','t','e','m','M','e','d','i','a','T','r','a','n','s','p','o','r','t','C','o','n','t','r','o','l','s','T','i','m','e','l','i','n','e','P','r','o','p','e','r','t','i','e','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsTimelineProperties[] = L"Windows.Media.SystemMediaTransportControlsTimelineProperties";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SystemMediaTransportControlsTimelineProperties[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','y','s','t','e','m','M','e','d','i','a','T','r','a','n','s','p','o','r','t','C','o','n','t','r','o','l','s','T','i','m','e','l','i','n','e','P','r','o','p','e','r','t','i','e','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SystemMediaTransportControlsTimelineProperties_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.VideoDisplayProperties
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_VideoDisplayProperties_DEFINED
#define RUNTIMECLASS_Windows_Media_VideoDisplayProperties_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_VideoDisplayProperties[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','V','i','d','e','o','D','i','s','p','l','a','y','P','r','o','p','e','r','t','i','e','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_VideoDisplayProperties[] = L"Windows.Media.VideoDisplayProperties";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_VideoDisplayProperties[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','V','i','d','e','o','D','i','s','p','l','a','y','P','r','o','p','e','r','t','i','e','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_VideoDisplayProperties_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Media::IMediaMarker* > interface
 */
#ifndef ____FIIterable_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CMedia__CIMediaMarker, 0xa1c0a397, 0x0364, 0x5e4c, 0x9d,0xca, 0x7c,0xd7,0x01,0x1b,0xd1,0x14);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("a1c0a397-0364-5e4c-9dca-7cd7011bd114")
                IIterable<ABI::Windows::Media::IMediaMarker* > : IIterable_impl<ABI::Windows::Media::IMediaMarker* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CMedia__CIMediaMarker, 0xa1c0a397, 0x0364, 0x5e4c, 0x9d,0xca, 0x7c,0xd7,0x01,0x1b,0xd1,0x14)
#endif
#else
typedef struct __FIIterable_1_Windows__CMedia__CIMediaMarkerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CMedia__CIMediaMarker *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CMedia__CIMediaMarker *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CMedia__CIMediaMarker *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CMedia__CIMediaMarker *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CMedia__CIMediaMarker *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CMedia__CIMediaMarker *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Media::IMediaMarker* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CMedia__CIMediaMarker *This,
        __FIIterator_1_Windows__CMedia__CIMediaMarker **value);

    END_INTERFACE
} __FIIterable_1_Windows__CMedia__CIMediaMarkerVtbl;

interface __FIIterable_1_Windows__CMedia__CIMediaMarker {
    CONST_VTBL __FIIterable_1_Windows__CMedia__CIMediaMarkerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CMedia__CIMediaMarker_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CMedia__CIMediaMarker_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CMedia__CIMediaMarker_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CMedia__CIMediaMarker_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CMedia__CIMediaMarker_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Media::IMediaMarker* > methods ***/
#define __FIIterable_1_Windows__CMedia__CIMediaMarker_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CIMediaMarker_QueryInterface(__FIIterable_1_Windows__CMedia__CIMediaMarker* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CMedia__CIMediaMarker_AddRef(__FIIterable_1_Windows__CMedia__CIMediaMarker* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CMedia__CIMediaMarker_Release(__FIIterable_1_Windows__CMedia__CIMediaMarker* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CIMediaMarker_GetIids(__FIIterable_1_Windows__CMedia__CIMediaMarker* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName(__FIIterable_1_Windows__CMedia__CIMediaMarker* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CMedia__CIMediaMarker_GetTrustLevel(__FIIterable_1_Windows__CMedia__CIMediaMarker* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Media::IMediaMarker* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CIMediaMarker_First(__FIIterable_1_Windows__CMedia__CIMediaMarker* This,__FIIterator_1_Windows__CMedia__CIMediaMarker **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_IMediaMarker IID___FIIterable_1_Windows__CMedia__CIMediaMarker
#define IIterable_IMediaMarkerVtbl __FIIterable_1_Windows__CMedia__CIMediaMarkerVtbl
#define IIterable_IMediaMarker __FIIterable_1_Windows__CMedia__CIMediaMarker
#define IIterable_IMediaMarker_QueryInterface __FIIterable_1_Windows__CMedia__CIMediaMarker_QueryInterface
#define IIterable_IMediaMarker_AddRef __FIIterable_1_Windows__CMedia__CIMediaMarker_AddRef
#define IIterable_IMediaMarker_Release __FIIterable_1_Windows__CMedia__CIMediaMarker_Release
#define IIterable_IMediaMarker_GetIids __FIIterable_1_Windows__CMedia__CIMediaMarker_GetIids
#define IIterable_IMediaMarker_GetRuntimeClassName __FIIterable_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName
#define IIterable_IMediaMarker_GetTrustLevel __FIIterable_1_Windows__CMedia__CIMediaMarker_GetTrustLevel
#define IIterable_IMediaMarker_First __FIIterable_1_Windows__CMedia__CIMediaMarker_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Media::IMediaMarker* > interface
 */
#ifndef ____FIIterator_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CMedia__CIMediaMarker, 0xf464661e, 0x88bc, 0x5cea, 0x93,0xcd, 0x0c,0x12,0x3f,0x17,0xd2,0x58);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("f464661e-88bc-5cea-93cd-0c123f17d258")
                IIterator<ABI::Windows::Media::IMediaMarker* > : IIterator_impl<ABI::Windows::Media::IMediaMarker* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CMedia__CIMediaMarker, 0xf464661e, 0x88bc, 0x5cea, 0x93,0xcd, 0x0c,0x12,0x3f,0x17,0xd2,0x58)
#endif
#else
typedef struct __FIIterator_1_Windows__CMedia__CIMediaMarkerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CMedia__CIMediaMarker *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CMedia__CIMediaMarker *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CMedia__CIMediaMarker *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CMedia__CIMediaMarker *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CMedia__CIMediaMarker *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CMedia__CIMediaMarker *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Media::IMediaMarker* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CMedia__CIMediaMarker *This,
        __x_ABI_CWindows_CMedia_CIMediaMarker **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CMedia__CIMediaMarker *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CMedia__CIMediaMarker *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CMedia__CIMediaMarker *This,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CIMediaMarker **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CMedia__CIMediaMarkerVtbl;

interface __FIIterator_1_Windows__CMedia__CIMediaMarker {
    CONST_VTBL __FIIterator_1_Windows__CMedia__CIMediaMarkerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CMedia__CIMediaMarker_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CMedia__CIMediaMarker_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CMedia__CIMediaMarker_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CMedia__CIMediaMarker_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CMedia__CIMediaMarker_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Media::IMediaMarker* > methods ***/
#define __FIIterator_1_Windows__CMedia__CIMediaMarker_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CMedia__CIMediaMarker_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CMedia__CIMediaMarker_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CMedia__CIMediaMarker_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CIMediaMarker_QueryInterface(__FIIterator_1_Windows__CMedia__CIMediaMarker* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CMedia__CIMediaMarker_AddRef(__FIIterator_1_Windows__CMedia__CIMediaMarker* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CMedia__CIMediaMarker_Release(__FIIterator_1_Windows__CMedia__CIMediaMarker* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CIMediaMarker_GetIids(__FIIterator_1_Windows__CMedia__CIMediaMarker* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName(__FIIterator_1_Windows__CMedia__CIMediaMarker* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CIMediaMarker_GetTrustLevel(__FIIterator_1_Windows__CMedia__CIMediaMarker* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Media::IMediaMarker* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CIMediaMarker_get_Current(__FIIterator_1_Windows__CMedia__CIMediaMarker* This,__x_ABI_CWindows_CMedia_CIMediaMarker **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CIMediaMarker_get_HasCurrent(__FIIterator_1_Windows__CMedia__CIMediaMarker* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CIMediaMarker_MoveNext(__FIIterator_1_Windows__CMedia__CIMediaMarker* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CIMediaMarker_GetMany(__FIIterator_1_Windows__CMedia__CIMediaMarker* This,UINT32 items_size,__x_ABI_CWindows_CMedia_CIMediaMarker **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_IMediaMarker IID___FIIterator_1_Windows__CMedia__CIMediaMarker
#define IIterator_IMediaMarkerVtbl __FIIterator_1_Windows__CMedia__CIMediaMarkerVtbl
#define IIterator_IMediaMarker __FIIterator_1_Windows__CMedia__CIMediaMarker
#define IIterator_IMediaMarker_QueryInterface __FIIterator_1_Windows__CMedia__CIMediaMarker_QueryInterface
#define IIterator_IMediaMarker_AddRef __FIIterator_1_Windows__CMedia__CIMediaMarker_AddRef
#define IIterator_IMediaMarker_Release __FIIterator_1_Windows__CMedia__CIMediaMarker_Release
#define IIterator_IMediaMarker_GetIids __FIIterator_1_Windows__CMedia__CIMediaMarker_GetIids
#define IIterator_IMediaMarker_GetRuntimeClassName __FIIterator_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName
#define IIterator_IMediaMarker_GetTrustLevel __FIIterator_1_Windows__CMedia__CIMediaMarker_GetTrustLevel
#define IIterator_IMediaMarker_get_Current __FIIterator_1_Windows__CMedia__CIMediaMarker_get_Current
#define IIterator_IMediaMarker_get_HasCurrent __FIIterator_1_Windows__CMedia__CIMediaMarker_get_HasCurrent
#define IIterator_IMediaMarker_MoveNext __FIIterator_1_Windows__CMedia__CIMediaMarker_MoveNext
#define IIterator_IMediaMarker_GetMany __FIIterator_1_Windows__CMedia__CIMediaMarker_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Media::IMediaMarker* > interface
 */
#ifndef ____FIVectorView_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CMedia__CIMediaMarker, 0xb543562c, 0x02b1, 0x5824, 0x80,0xa8, 0x98,0x54,0x13,0x0c,0xda,0xdd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("b543562c-02b1-5824-80a8-9854130cdadd")
                IVectorView<ABI::Windows::Media::IMediaMarker* > : IVectorView_impl<ABI::Windows::Media::IMediaMarker* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CMedia__CIMediaMarker, 0xb543562c, 0x02b1, 0x5824, 0x80,0xa8, 0x98,0x54,0x13,0x0c,0xda,0xdd)
#endif
#else
typedef struct __FIVectorView_1_Windows__CMedia__CIMediaMarkerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CMedia__CIMediaMarker *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CMedia__CIMediaMarker *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CMedia__CIMediaMarker *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CMedia__CIMediaMarker *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CMedia__CIMediaMarker *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CMedia__CIMediaMarker *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Media::IMediaMarker* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CMedia__CIMediaMarker *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CIMediaMarker **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CMedia__CIMediaMarker *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CMedia__CIMediaMarker *This,
        __x_ABI_CWindows_CMedia_CIMediaMarker *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CMedia__CIMediaMarker *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CIMediaMarker **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CMedia__CIMediaMarkerVtbl;

interface __FIVectorView_1_Windows__CMedia__CIMediaMarker {
    CONST_VTBL __FIVectorView_1_Windows__CMedia__CIMediaMarkerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Media::IMediaMarker* > methods ***/
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CIMediaMarker_QueryInterface(__FIVectorView_1_Windows__CMedia__CIMediaMarker* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CMedia__CIMediaMarker_AddRef(__FIVectorView_1_Windows__CMedia__CIMediaMarker* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CMedia__CIMediaMarker_Release(__FIVectorView_1_Windows__CMedia__CIMediaMarker* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetIids(__FIVectorView_1_Windows__CMedia__CIMediaMarker* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName(__FIVectorView_1_Windows__CMedia__CIMediaMarker* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetTrustLevel(__FIVectorView_1_Windows__CMedia__CIMediaMarker* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Media::IMediaMarker* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetAt(__FIVectorView_1_Windows__CMedia__CIMediaMarker* This,UINT32 index,__x_ABI_CWindows_CMedia_CIMediaMarker **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CIMediaMarker_get_Size(__FIVectorView_1_Windows__CMedia__CIMediaMarker* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CIMediaMarker_IndexOf(__FIVectorView_1_Windows__CMedia__CIMediaMarker* This,__x_ABI_CWindows_CMedia_CIMediaMarker *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetMany(__FIVectorView_1_Windows__CMedia__CIMediaMarker* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CMedia_CIMediaMarker **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_IMediaMarker IID___FIVectorView_1_Windows__CMedia__CIMediaMarker
#define IVectorView_IMediaMarkerVtbl __FIVectorView_1_Windows__CMedia__CIMediaMarkerVtbl
#define IVectorView_IMediaMarker __FIVectorView_1_Windows__CMedia__CIMediaMarker
#define IVectorView_IMediaMarker_QueryInterface __FIVectorView_1_Windows__CMedia__CIMediaMarker_QueryInterface
#define IVectorView_IMediaMarker_AddRef __FIVectorView_1_Windows__CMedia__CIMediaMarker_AddRef
#define IVectorView_IMediaMarker_Release __FIVectorView_1_Windows__CMedia__CIMediaMarker_Release
#define IVectorView_IMediaMarker_GetIids __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetIids
#define IVectorView_IMediaMarker_GetRuntimeClassName __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName
#define IVectorView_IMediaMarker_GetTrustLevel __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetTrustLevel
#define IVectorView_IMediaMarker_GetAt __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetAt
#define IVectorView_IMediaMarker_get_Size __FIVectorView_1_Windows__CMedia__CIMediaMarker_get_Size
#define IVectorView_IMediaMarker_IndexOf __FIVectorView_1_Windows__CMedia__CIMediaMarker_IndexOf
#define IVectorView_IMediaMarker_GetMany __FIVectorView_1_Windows__CMedia__CIMediaMarker_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Media::IMediaMarker* > interface
 */
#ifndef ____FIVector_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CMedia__CIMediaMarker, 0x057e6bc9, 0x7bb8, 0x5816, 0x98,0x8b, 0x8e,0x9b,0x3f,0xb1,0xbf,0xf4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("057e6bc9-7bb8-5816-988b-8e9b3fb1bff4")
                IVector<ABI::Windows::Media::IMediaMarker* > : IVector_impl<ABI::Windows::Media::IMediaMarker* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CMedia__CIMediaMarker, 0x057e6bc9, 0x7bb8, 0x5816, 0x98,0x8b, 0x8e,0x9b,0x3f,0xb1,0xbf,0xf4)
#endif
#else
typedef struct __FIVector_1_Windows__CMedia__CIMediaMarkerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Media::IMediaMarker* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CIMediaMarker **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        __FIVectorView_1_Windows__CMedia__CIMediaMarker **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        __x_ABI_CWindows_CMedia_CIMediaMarker *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CIMediaMarker *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CIMediaMarker *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        __x_ABI_CWindows_CMedia_CIMediaMarker *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CIMediaMarker **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CMedia__CIMediaMarker *This,
        UINT32 count,
        __x_ABI_CWindows_CMedia_CIMediaMarker **items);

    END_INTERFACE
} __FIVector_1_Windows__CMedia__CIMediaMarkerVtbl;

interface __FIVector_1_Windows__CMedia__CIMediaMarker {
    CONST_VTBL __FIVector_1_Windows__CMedia__CIMediaMarkerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CMedia__CIMediaMarker_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CMedia__CIMediaMarker_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Media::IMediaMarker* > methods ***/
#define __FIVector_1_Windows__CMedia__CIMediaMarker_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CMedia__CIMediaMarker_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_QueryInterface(__FIVector_1_Windows__CMedia__CIMediaMarker* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CMedia__CIMediaMarker_AddRef(__FIVector_1_Windows__CMedia__CIMediaMarker* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CMedia__CIMediaMarker_Release(__FIVector_1_Windows__CMedia__CIMediaMarker* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_GetIids(__FIVector_1_Windows__CMedia__CIMediaMarker* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName(__FIVector_1_Windows__CMedia__CIMediaMarker* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_GetTrustLevel(__FIVector_1_Windows__CMedia__CIMediaMarker* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Media::IMediaMarker* > methods ***/
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_GetAt(__FIVector_1_Windows__CMedia__CIMediaMarker* This,UINT32 index,__x_ABI_CWindows_CMedia_CIMediaMarker **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_get_Size(__FIVector_1_Windows__CMedia__CIMediaMarker* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_GetView(__FIVector_1_Windows__CMedia__CIMediaMarker* This,__FIVectorView_1_Windows__CMedia__CIMediaMarker **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_IndexOf(__FIVector_1_Windows__CMedia__CIMediaMarker* This,__x_ABI_CWindows_CMedia_CIMediaMarker *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_SetAt(__FIVector_1_Windows__CMedia__CIMediaMarker* This,UINT32 index,__x_ABI_CWindows_CMedia_CIMediaMarker *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_InsertAt(__FIVector_1_Windows__CMedia__CIMediaMarker* This,UINT32 index,__x_ABI_CWindows_CMedia_CIMediaMarker *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_RemoveAt(__FIVector_1_Windows__CMedia__CIMediaMarker* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_Append(__FIVector_1_Windows__CMedia__CIMediaMarker* This,__x_ABI_CWindows_CMedia_CIMediaMarker *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_RemoveAtEnd(__FIVector_1_Windows__CMedia__CIMediaMarker* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_Clear(__FIVector_1_Windows__CMedia__CIMediaMarker* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_GetMany(__FIVector_1_Windows__CMedia__CIMediaMarker* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CMedia_CIMediaMarker **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CMedia__CIMediaMarker_ReplaceAll(__FIVector_1_Windows__CMedia__CIMediaMarker* This,UINT32 count,__x_ABI_CWindows_CMedia_CIMediaMarker **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_IMediaMarker IID___FIVector_1_Windows__CMedia__CIMediaMarker
#define IVector_IMediaMarkerVtbl __FIVector_1_Windows__CMedia__CIMediaMarkerVtbl
#define IVector_IMediaMarker __FIVector_1_Windows__CMedia__CIMediaMarker
#define IVector_IMediaMarker_QueryInterface __FIVector_1_Windows__CMedia__CIMediaMarker_QueryInterface
#define IVector_IMediaMarker_AddRef __FIVector_1_Windows__CMedia__CIMediaMarker_AddRef
#define IVector_IMediaMarker_Release __FIVector_1_Windows__CMedia__CIMediaMarker_Release
#define IVector_IMediaMarker_GetIids __FIVector_1_Windows__CMedia__CIMediaMarker_GetIids
#define IVector_IMediaMarker_GetRuntimeClassName __FIVector_1_Windows__CMedia__CIMediaMarker_GetRuntimeClassName
#define IVector_IMediaMarker_GetTrustLevel __FIVector_1_Windows__CMedia__CIMediaMarker_GetTrustLevel
#define IVector_IMediaMarker_GetAt __FIVector_1_Windows__CMedia__CIMediaMarker_GetAt
#define IVector_IMediaMarker_get_Size __FIVector_1_Windows__CMedia__CIMediaMarker_get_Size
#define IVector_IMediaMarker_GetView __FIVector_1_Windows__CMedia__CIMediaMarker_GetView
#define IVector_IMediaMarker_IndexOf __FIVector_1_Windows__CMedia__CIMediaMarker_IndexOf
#define IVector_IMediaMarker_SetAt __FIVector_1_Windows__CMedia__CIMediaMarker_SetAt
#define IVector_IMediaMarker_InsertAt __FIVector_1_Windows__CMedia__CIMediaMarker_InsertAt
#define IVector_IMediaMarker_RemoveAt __FIVector_1_Windows__CMedia__CIMediaMarker_RemoveAt
#define IVector_IMediaMarker_Append __FIVector_1_Windows__CMedia__CIMediaMarker_Append
#define IVector_IMediaMarker_RemoveAtEnd __FIVector_1_Windows__CMedia__CIMediaMarker_RemoveAtEnd
#define IVector_IMediaMarker_Clear __FIVector_1_Windows__CMedia__CIMediaMarker_Clear
#define IVector_IMediaMarker_GetMany __FIVector_1_Windows__CMedia__CIMediaMarker_GetMany
#define IVector_IMediaMarker_ReplaceAll __FIVector_1_Windows__CMedia__CIMediaMarker_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CMedia__CIMediaMarker_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IReference<ABI::Windows::Media::MediaPlaybackAutoRepeatMode > interface
 */
#ifndef ____FIReference_1_MediaPlaybackAutoRepeatMode_INTERFACE_DEFINED__
#define ____FIReference_1_MediaPlaybackAutoRepeatMode_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIReference_1_MediaPlaybackAutoRepeatMode, 0x50a7f41f, 0x58d5, 0x5c4d, 0x94,0x75, 0x8d,0xd1,0xac,0xd6,0x58,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("50a7f41f-58d5-5c4d-9475-8dd1acd65836")
            IReference<ABI::Windows::Media::MediaPlaybackAutoRepeatMode > : IReference_impl<ABI::Windows::Media::MediaPlaybackAutoRepeatMode >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIReference_1_MediaPlaybackAutoRepeatMode, 0x50a7f41f, 0x58d5, 0x5c4d, 0x94,0x75, 0x8d,0xd1,0xac,0xd6,0x58,0x36)
#endif
#else
typedef struct __FIReference_1_MediaPlaybackAutoRepeatModeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIReference_1_MediaPlaybackAutoRepeatMode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIReference_1_MediaPlaybackAutoRepeatMode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIReference_1_MediaPlaybackAutoRepeatMode *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIReference_1_MediaPlaybackAutoRepeatMode *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIReference_1_MediaPlaybackAutoRepeatMode *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIReference_1_MediaPlaybackAutoRepeatMode *This,
        TrustLevel *trustLevel);

    /*** IReference<ABI::Windows::Media::MediaPlaybackAutoRepeatMode > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __FIReference_1_MediaPlaybackAutoRepeatMode *This,
        __x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode *value);

    END_INTERFACE
} __FIReference_1_MediaPlaybackAutoRepeatModeVtbl;

interface __FIReference_1_MediaPlaybackAutoRepeatMode {
    CONST_VTBL __FIReference_1_MediaPlaybackAutoRepeatModeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIReference_1_MediaPlaybackAutoRepeatMode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIReference_1_MediaPlaybackAutoRepeatMode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIReference_1_MediaPlaybackAutoRepeatMode_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIReference_1_MediaPlaybackAutoRepeatMode_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIReference_1_MediaPlaybackAutoRepeatMode_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIReference_1_MediaPlaybackAutoRepeatMode_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IReference<ABI::Windows::Media::MediaPlaybackAutoRepeatMode > methods ***/
#define __FIReference_1_MediaPlaybackAutoRepeatMode_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIReference_1_MediaPlaybackAutoRepeatMode_QueryInterface(__FIReference_1_MediaPlaybackAutoRepeatMode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIReference_1_MediaPlaybackAutoRepeatMode_AddRef(__FIReference_1_MediaPlaybackAutoRepeatMode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIReference_1_MediaPlaybackAutoRepeatMode_Release(__FIReference_1_MediaPlaybackAutoRepeatMode* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIReference_1_MediaPlaybackAutoRepeatMode_GetIids(__FIReference_1_MediaPlaybackAutoRepeatMode* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIReference_1_MediaPlaybackAutoRepeatMode_GetRuntimeClassName(__FIReference_1_MediaPlaybackAutoRepeatMode* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIReference_1_MediaPlaybackAutoRepeatMode_GetTrustLevel(__FIReference_1_MediaPlaybackAutoRepeatMode* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IReference<ABI::Windows::Media::MediaPlaybackAutoRepeatMode > methods ***/
static inline HRESULT __FIReference_1_MediaPlaybackAutoRepeatMode_get_Value(__FIReference_1_MediaPlaybackAutoRepeatMode* This,__x_ABI_CWindows_CMedia_CMediaPlaybackAutoRepeatMode *value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IReference_MediaPlaybackAutoRepeatMode IID___FIReference_1_MediaPlaybackAutoRepeatMode
#define IReference_MediaPlaybackAutoRepeatModeVtbl __FIReference_1_MediaPlaybackAutoRepeatModeVtbl
#define IReference_MediaPlaybackAutoRepeatMode __FIReference_1_MediaPlaybackAutoRepeatMode
#define IReference_MediaPlaybackAutoRepeatMode_QueryInterface __FIReference_1_MediaPlaybackAutoRepeatMode_QueryInterface
#define IReference_MediaPlaybackAutoRepeatMode_AddRef __FIReference_1_MediaPlaybackAutoRepeatMode_AddRef
#define IReference_MediaPlaybackAutoRepeatMode_Release __FIReference_1_MediaPlaybackAutoRepeatMode_Release
#define IReference_MediaPlaybackAutoRepeatMode_GetIids __FIReference_1_MediaPlaybackAutoRepeatMode_GetIids
#define IReference_MediaPlaybackAutoRepeatMode_GetRuntimeClassName __FIReference_1_MediaPlaybackAutoRepeatMode_GetRuntimeClassName
#define IReference_MediaPlaybackAutoRepeatMode_GetTrustLevel __FIReference_1_MediaPlaybackAutoRepeatMode_GetTrustLevel
#define IReference_MediaPlaybackAutoRepeatMode_get_Value __FIReference_1_MediaPlaybackAutoRepeatMode_get_Value
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIReference_1_MediaPlaybackAutoRepeatMode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::AutoRepeatModeChangeRequestedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs, 0xa6214bde, 0x02d5, 0x55b3, 0xab,0x0d, 0xc6,0x03,0x1b,0xe7,0x0d,0xa1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("a6214bde-02d5-55b3-ab0d-c6031be70da1")
            ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::AutoRepeatModeChangeRequestedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SystemMediaTransportControls*, ABI::Windows::Media::ISystemMediaTransportControls* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::AutoRepeatModeChangeRequestedEventArgs*, ABI::Windows::Media::IAutoRepeatModeChangeRequestedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs, 0xa6214bde, 0x02d5, 0x55b3, 0xab,0x0d, 0xc6,0x03,0x1b,0xe7,0x0d,0xa1)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::AutoRepeatModeChangeRequestedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs *This,
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,
        __x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::AutoRepeatModeChangeRequestedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_Release(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::AutoRepeatModeChangeRequestedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs* This,__x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,__x_ABI_CWindows_CMedia_CIAutoRepeatModeChangeRequestedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SystemMediaTransportControls_AutoRepeatModeChangeRequestedEventArgs IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_AutoRepeatModeChangeRequestedEventArgsVtbl __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgsVtbl
#define ITypedEventHandler_SystemMediaTransportControls_AutoRepeatModeChangeRequestedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_AutoRepeatModeChangeRequestedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_QueryInterface
#define ITypedEventHandler_SystemMediaTransportControls_AutoRepeatModeChangeRequestedEventArgs_AddRef __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_AddRef
#define ITypedEventHandler_SystemMediaTransportControls_AutoRepeatModeChangeRequestedEventArgs_Release __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_Release
#define ITypedEventHandler_SystemMediaTransportControls_AutoRepeatModeChangeRequestedEventArgs_Invoke __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CAutoRepeatModeChangeRequestedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackPositionChangeRequestedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs, 0x44e34f15, 0xbdc0, 0x50a7, 0xac,0xe4, 0x39,0xe9,0x1f,0xb7,0x53,0xf1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("44e34f15-bdc0-50a7-ace4-39e91fb753f1")
            ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackPositionChangeRequestedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SystemMediaTransportControls*, ABI::Windows::Media::ISystemMediaTransportControls* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::PlaybackPositionChangeRequestedEventArgs*, ABI::Windows::Media::IPlaybackPositionChangeRequestedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs, 0x44e34f15, 0xbdc0, 0x50a7, 0xac,0xe4, 0x39,0xe9,0x1f,0xb7,0x53,0xf1)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackPositionChangeRequestedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs *This,
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,
        __x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackPositionChangeRequestedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_Release(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackPositionChangeRequestedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs* This,__x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,__x_ABI_CWindows_CMedia_CIPlaybackPositionChangeRequestedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SystemMediaTransportControls_PlaybackPositionChangeRequestedEventArgs IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackPositionChangeRequestedEventArgsVtbl __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgsVtbl
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackPositionChangeRequestedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackPositionChangeRequestedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_QueryInterface
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackPositionChangeRequestedEventArgs_AddRef __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_AddRef
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackPositionChangeRequestedEventArgs_Release __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_Release
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackPositionChangeRequestedEventArgs_Invoke __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackPositionChangeRequestedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackRateChangeRequestedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs, 0x15eb0182, 0x6366, 0x5b9f, 0xbd,0x8c, 0x8a,0xb4,0xfa,0x9d,0x7c,0xd9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("15eb0182-6366-5b9f-bd8c-8ab4fa9d7cd9")
            ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackRateChangeRequestedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SystemMediaTransportControls*, ABI::Windows::Media::ISystemMediaTransportControls* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::PlaybackRateChangeRequestedEventArgs*, ABI::Windows::Media::IPlaybackRateChangeRequestedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs, 0x15eb0182, 0x6366, 0x5b9f, 0xbd,0x8c, 0x8a,0xb4,0xfa,0x9d,0x7c,0xd9)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackRateChangeRequestedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs *This,
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,
        __x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackRateChangeRequestedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_Release(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::PlaybackRateChangeRequestedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs* This,__x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,__x_ABI_CWindows_CMedia_CIPlaybackRateChangeRequestedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SystemMediaTransportControls_PlaybackRateChangeRequestedEventArgs IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackRateChangeRequestedEventArgsVtbl __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgsVtbl
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackRateChangeRequestedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackRateChangeRequestedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_QueryInterface
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackRateChangeRequestedEventArgs_AddRef __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_AddRef
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackRateChangeRequestedEventArgs_Release __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_Release
#define ITypedEventHandler_SystemMediaTransportControls_PlaybackRateChangeRequestedEventArgs_Invoke __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CPlaybackRateChangeRequestedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::ShuffleEnabledChangeRequestedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs, 0x17ecea80, 0x27e4, 0x5dae, 0xab,0xb4, 0xc8,0x58,0xad,0x1c,0x53,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("17ecea80-27e4-5dae-abb4-c858ad1c5307")
            ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::ShuffleEnabledChangeRequestedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SystemMediaTransportControls*, ABI::Windows::Media::ISystemMediaTransportControls* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::ShuffleEnabledChangeRequestedEventArgs*, ABI::Windows::Media::IShuffleEnabledChangeRequestedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs, 0x17ecea80, 0x27e4, 0x5dae, 0xab,0xb4, 0xc8,0x58,0xad,0x1c,0x53,0x07)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::ShuffleEnabledChangeRequestedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs *This,
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,
        __x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::ShuffleEnabledChangeRequestedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_Release(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::ShuffleEnabledChangeRequestedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs* This,__x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,__x_ABI_CWindows_CMedia_CIShuffleEnabledChangeRequestedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SystemMediaTransportControls_ShuffleEnabledChangeRequestedEventArgs IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_ShuffleEnabledChangeRequestedEventArgsVtbl __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgsVtbl
#define ITypedEventHandler_SystemMediaTransportControls_ShuffleEnabledChangeRequestedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_ShuffleEnabledChangeRequestedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_QueryInterface
#define ITypedEventHandler_SystemMediaTransportControls_ShuffleEnabledChangeRequestedEventArgs_AddRef __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_AddRef
#define ITypedEventHandler_SystemMediaTransportControls_ShuffleEnabledChangeRequestedEventArgs_Release __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_Release
#define ITypedEventHandler_SystemMediaTransportControls_ShuffleEnabledChangeRequestedEventArgs_Invoke __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CShuffleEnabledChangeRequestedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsButtonPressedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs, 0x0557e996, 0x7b23, 0x5bae, 0xaa,0x81, 0xea,0x0d,0x67,0x11,0x43,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("0557e996-7b23-5bae-aa81-ea0d671143a4")
            ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsButtonPressedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SystemMediaTransportControls*, ABI::Windows::Media::ISystemMediaTransportControls* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SystemMediaTransportControlsButtonPressedEventArgs*, ABI::Windows::Media::ISystemMediaTransportControlsButtonPressedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs, 0x0557e996, 0x7b23, 0x5bae, 0xaa,0x81, 0xea,0x0d,0x67,0x11,0x43,0xa4)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsButtonPressedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs *This,
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsButtonPressedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_Release(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsButtonPressedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs* This,__x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsButtonPressedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsButtonPressedEventArgs IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsButtonPressedEventArgsVtbl __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgsVtbl
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsButtonPressedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsButtonPressedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_QueryInterface
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsButtonPressedEventArgs_AddRef __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_AddRef
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsButtonPressedEventArgs_Release __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_Release
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsButtonPressedEventArgs_Invoke __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsButtonPressedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsPropertyChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs, 0x9fd61dad, 0x1746, 0x5fa1, 0xa9,0x08, 0xef,0x7c,0xb4,0x60,0x3c,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("9fd61dad-1746-5fa1-a908-ef7cb4603c85")
            ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsPropertyChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SystemMediaTransportControls*, ABI::Windows::Media::ISystemMediaTransportControls* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SystemMediaTransportControlsPropertyChangedEventArgs*, ABI::Windows::Media::ISystemMediaTransportControlsPropertyChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs, 0x9fd61dad, 0x1746, 0x5fa1, 0xa9,0x08, 0xef,0x7c,0xb4,0x60,0x3c,0x85)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsPropertyChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs *This,
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,
        __x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsPropertyChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::SystemMediaTransportControls*,ABI::Windows::Media::SystemMediaTransportControlsPropertyChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs* This,__x_ABI_CWindows_CMedia_CISystemMediaTransportControls *sender,__x_ABI_CWindows_CMedia_CISystemMediaTransportControlsPropertyChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsPropertyChangedEventArgs IID___FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsPropertyChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgsVtbl
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsPropertyChangedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsPropertyChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_QueryInterface
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsPropertyChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_AddRef
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsPropertyChangedEventArgs_Release __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_Release
#define ITypedEventHandler_SystemMediaTransportControls_SystemMediaTransportControlsPropertyChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CSystemMediaTransportControls_Windows__CMedia__CSystemMediaTransportControlsPropertyChangedEventArgs_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_media_h__ */
