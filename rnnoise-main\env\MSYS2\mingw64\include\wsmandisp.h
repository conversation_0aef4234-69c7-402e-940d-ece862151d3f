/*** Autogenerated by WIDL 10.12 from include/wsmandisp.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wsmandisp_h__
#define __wsmandisp_h__

/* Forward declarations */

#ifndef __WSMan_FWD_DEFINED__
#define __WSMan_FWD_DEFINED__
#ifdef __cplusplus
typedef class WSMan WSMan;
#else
typedef struct WSMan WSMan;
#endif /* defined __cplusplus */
#endif /* defined __WSMan_FWD_DEFINED__ */

#ifndef __WSManInternal_FWD_DEFINED__
#define __WSManInternal_FWD_DEFINED__
#ifdef __cplusplus
typedef class WSManInternal WSManInternal;
#else
typedef struct WSManInternal WSManInternal;
#endif /* defined __cplusplus */
#endif /* defined __WSManInternal_FWD_DEFINED__ */

#ifndef __IWSMan_FWD_DEFINED__
#define __IWSMan_FWD_DEFINED__
typedef interface IWSMan IWSMan;
#ifdef __cplusplus
interface IWSMan;
#endif /* __cplusplus */
#endif

#ifndef __IWSManEx_FWD_DEFINED__
#define __IWSManEx_FWD_DEFINED__
typedef interface IWSManEx IWSManEx;
#ifdef __cplusplus
interface IWSManEx;
#endif /* __cplusplus */
#endif

#ifndef __IWSManEx2_FWD_DEFINED__
#define __IWSManEx2_FWD_DEFINED__
typedef interface IWSManEx2 IWSManEx2;
#ifdef __cplusplus
interface IWSManEx2;
#endif /* __cplusplus */
#endif

#ifndef __IWSManEx3_FWD_DEFINED__
#define __IWSManEx3_FWD_DEFINED__
typedef interface IWSManEx3 IWSManEx3;
#ifdef __cplusplus
interface IWSManEx3;
#endif /* __cplusplus */
#endif

#ifndef __IWSManConnectionOptions_FWD_DEFINED__
#define __IWSManConnectionOptions_FWD_DEFINED__
typedef interface IWSManConnectionOptions IWSManConnectionOptions;
#ifdef __cplusplus
interface IWSManConnectionOptions;
#endif /* __cplusplus */
#endif

#ifndef __IWSManConnectionOptionsEx_FWD_DEFINED__
#define __IWSManConnectionOptionsEx_FWD_DEFINED__
typedef interface IWSManConnectionOptionsEx IWSManConnectionOptionsEx;
#ifdef __cplusplus
interface IWSManConnectionOptionsEx;
#endif /* __cplusplus */
#endif

#ifndef __IWSManConnectionOptionsEx2_FWD_DEFINED__
#define __IWSManConnectionOptionsEx2_FWD_DEFINED__
typedef interface IWSManConnectionOptionsEx2 IWSManConnectionOptionsEx2;
#ifdef __cplusplus
interface IWSManConnectionOptionsEx2;
#endif /* __cplusplus */
#endif

#ifndef __IWSManSession_FWD_DEFINED__
#define __IWSManSession_FWD_DEFINED__
typedef interface IWSManSession IWSManSession;
#ifdef __cplusplus
interface IWSManSession;
#endif /* __cplusplus */
#endif

#ifndef __IWSManEnumerator_FWD_DEFINED__
#define __IWSManEnumerator_FWD_DEFINED__
typedef interface IWSManEnumerator IWSManEnumerator;
#ifdef __cplusplus
interface IWSManEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IWSManResourceLocator_FWD_DEFINED__
#define __IWSManResourceLocator_FWD_DEFINED__
typedef interface IWSManResourceLocator IWSManResourceLocator;
#ifdef __cplusplus
interface IWSManResourceLocator;
#endif /* __cplusplus */
#endif

#ifndef __IWSManResourceLocatorInternal_FWD_DEFINED__
#define __IWSManResourceLocatorInternal_FWD_DEFINED__
typedef interface IWSManResourceLocatorInternal IWSManResourceLocatorInternal;
#ifdef __cplusplus
interface IWSManResourceLocatorInternal;
#endif /* __cplusplus */
#endif

#ifndef __IWSManInternal_FWD_DEFINED__
#define __IWSManInternal_FWD_DEFINED__
typedef interface IWSManInternal IWSManInternal;
#ifdef __cplusplus
interface IWSManInternal;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef __WSManAutomation_LIBRARY_DEFINED__
#define __WSManAutomation_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_WSManAutomation, 0xf010be25, 0x296d, 0x4036, 0x98,0x0f, 0x5a,0x06,0x69,0xa1,0x75,0x77);

typedef enum _WSManSessionFlags {
    WSManFlagUTF8 = 0x1,
    WSManFlagCredUsernamePassword = 0x1000,
    WSManFlagSkipCACheck = 0x2000,
    WSManFlagSkipCNCheck = 0x4000,
    WSManFlagUseNoAuthentication = 0x8000,
    WSManFlagUseDigest = 0x10000,
    WSManFlagUseNegotiate = 0x20000,
    WSManFlagUseBasic = 0x40000,
    WSManFlagUseKerberos = 0x80000,
    WSManFlagNoEncryption = 0x100000,
    WSManFlagUseClientCertificate = 0x200000,
    WSManFlagEnableSPNServerPort = 0x400000,
    WSManFlagUTF16 = 0x800000,
    WSManFlagUseCredSsp = 0x1000000,
    WSManFlagSkipRevocationCheck = 0x2000000,
    WSManFlagAllowNegotiateImplicitCredentials = 0x4000000,
    WSManFlagUseSsl = 0x8000000
} WSManSessionFlags;
typedef enum _WSManEnumFlags {
    WSManFlagReturnObject = 0x0,
    WSManFlagHierarchyDeep = 0x0,
    WSManFlagAssociatedInstance = 0x0,
    WSManFlagNonXmlText = 0x1,
    WSManFlagReturnEPR = 0x2,
    WSManFlagReturnObjectAndEPR = 0x4,
    WSManFlagHierarchyShallow = 0x20,
    WSManFlagHierarchyDeepBasePropsOnly = 0x40,
    WSManFlagAssociationInstance = 0x80
} WSManEnumFlags;
typedef enum _WSManProxyAccessTypeFlags {
    WSManProxyIEConfig = 0x1,
    WSManProxyWinHttpConfig = 0x2,
    WSManProxyAutoDetect = 0x4,
    WSManProxyNoProxyServer = 0x8
} WSManProxyAccessTypeFlags;
typedef enum _WSManProxyAuthenticationFlags {
    WSManFlagProxyAuthenticationUseNegotiate = 0x1,
    WSManFlagProxyAuthenticationUseBasic = 0x2,
    WSManFlagProxyAuthenticationUseDigest = 0x4
} WSManProxyAuthenticationFlags;
#ifndef __IWSMan_FWD_DEFINED__
#define __IWSMan_FWD_DEFINED__
typedef interface IWSMan IWSMan;
#ifdef __cplusplus
interface IWSMan;
#endif /* __cplusplus */
#endif

#ifndef __IWSManEx_FWD_DEFINED__
#define __IWSManEx_FWD_DEFINED__
typedef interface IWSManEx IWSManEx;
#ifdef __cplusplus
interface IWSManEx;
#endif /* __cplusplus */
#endif

#ifndef __IWSManEx2_FWD_DEFINED__
#define __IWSManEx2_FWD_DEFINED__
typedef interface IWSManEx2 IWSManEx2;
#ifdef __cplusplus
interface IWSManEx2;
#endif /* __cplusplus */
#endif

#ifndef __IWSManEx3_FWD_DEFINED__
#define __IWSManEx3_FWD_DEFINED__
typedef interface IWSManEx3 IWSManEx3;
#ifdef __cplusplus
interface IWSManEx3;
#endif /* __cplusplus */
#endif

#ifndef __IWSManConnectionOptions_FWD_DEFINED__
#define __IWSManConnectionOptions_FWD_DEFINED__
typedef interface IWSManConnectionOptions IWSManConnectionOptions;
#ifdef __cplusplus
interface IWSManConnectionOptions;
#endif /* __cplusplus */
#endif

#ifndef __IWSManConnectionOptionsEx_FWD_DEFINED__
#define __IWSManConnectionOptionsEx_FWD_DEFINED__
typedef interface IWSManConnectionOptionsEx IWSManConnectionOptionsEx;
#ifdef __cplusplus
interface IWSManConnectionOptionsEx;
#endif /* __cplusplus */
#endif

#ifndef __IWSManConnectionOptionsEx2_FWD_DEFINED__
#define __IWSManConnectionOptionsEx2_FWD_DEFINED__
typedef interface IWSManConnectionOptionsEx2 IWSManConnectionOptionsEx2;
#ifdef __cplusplus
interface IWSManConnectionOptionsEx2;
#endif /* __cplusplus */
#endif

#ifndef __IWSManSession_FWD_DEFINED__
#define __IWSManSession_FWD_DEFINED__
typedef interface IWSManSession IWSManSession;
#ifdef __cplusplus
interface IWSManSession;
#endif /* __cplusplus */
#endif

#ifndef __IWSManSessionEx_FWD_DEFINED__
#define __IWSManSessionEx_FWD_DEFINED__
typedef interface IWSManSessionEx IWSManSessionEx;
#ifdef __cplusplus
interface IWSManSessionEx;
#endif /* __cplusplus */
#endif

#ifndef __IWSManEnumerator_FWD_DEFINED__
#define __IWSManEnumerator_FWD_DEFINED__
typedef interface IWSManEnumerator IWSManEnumerator;
#ifdef __cplusplus
interface IWSManEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IWSManResourceLocator_FWD_DEFINED__
#define __IWSManResourceLocator_FWD_DEFINED__
typedef interface IWSManResourceLocator IWSManResourceLocator;
#ifdef __cplusplus
interface IWSManResourceLocator;
#endif /* __cplusplus */
#endif

#ifndef __IWSManResourceLocatorInternal_FWD_DEFINED__
#define __IWSManResourceLocatorInternal_FWD_DEFINED__
typedef interface IWSManResourceLocatorInternal IWSManResourceLocatorInternal;
#ifdef __cplusplus
interface IWSManResourceLocatorInternal;
#endif /* __cplusplus */
#endif

#ifndef __IWSManInternal_FWD_DEFINED__
#define __IWSManInternal_FWD_DEFINED__
typedef interface IWSManInternal IWSManInternal;
#ifdef __cplusplus
interface IWSManInternal;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * WSMan coclass
 */

DEFINE_GUID(CLSID_WSMan, 0xbced617b, 0xec03, 0x420b, 0x85,0x08, 0x97,0x7d,0xc7,0xa6,0x86,0xbd);

#ifdef __cplusplus
class DECLSPEC_UUID("bced617b-ec03-420b-8508-977dc7a686bd") WSMan;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WSMan, 0xbced617b, 0xec03, 0x420b, 0x85,0x08, 0x97,0x7d,0xc7,0xa6,0x86,0xbd)
#endif
#endif

/*****************************************************************************
 * WSManInternal coclass
 */

DEFINE_GUID(CLSID_WSManInternal, 0x7de087a5, 0x5dcb, 0x4df7, 0xbb,0x12, 0x09,0x24,0xad,0x8f,0xbd,0x9a);

#ifdef __cplusplus
class DECLSPEC_UUID("7de087a5-5dcb-4df7-bb12-0924ad8fbd9a") WSManInternal;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WSManInternal, 0x7de087a5, 0x5dcb, 0x4df7, 0xbb,0x12, 0x09,0x24,0xad,0x8f,0xbd,0x9a)
#endif
#endif

#endif /* __WSManAutomation_LIBRARY_DEFINED__ */
/*****************************************************************************
 * IWSMan interface
 */
#ifndef __IWSMan_INTERFACE_DEFINED__
#define __IWSMan_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSMan, 0x190d8637, 0x5cd3, 0x496d, 0xad,0x24, 0x69,0x63,0x6b,0xb5,0xa3,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("190d8637-5cd3-496d-ad24-69636bb5a3b5")
IWSMan : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE CreateSession(
        BSTR connection,
        LONG flags,
        IDispatch *connectionOptions,
        IDispatch **session) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateConnectionOptions(
        IDispatch **connectionOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CommandLine(
        BSTR *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Error(
        BSTR *value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSMan, 0x190d8637, 0x5cd3, 0x496d, 0xad,0x24, 0x69,0x63,0x6b,0xb5,0xa3,0xb5)
#endif
#else
typedef struct IWSManVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSMan *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSMan *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSMan *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSMan *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSMan *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSMan *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSMan *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSMan methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateSession)(
        IWSMan *This,
        BSTR connection,
        LONG flags,
        IDispatch *connectionOptions,
        IDispatch **session);

    HRESULT (STDMETHODCALLTYPE *CreateConnectionOptions)(
        IWSMan *This,
        IDispatch **connectionOptions);

    HRESULT (STDMETHODCALLTYPE *get_CommandLine)(
        IWSMan *This,
        BSTR *value);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWSMan *This,
        BSTR *value);

    END_INTERFACE
} IWSManVtbl;

interface IWSMan {
    CONST_VTBL IWSManVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSMan_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSMan_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSMan_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSMan_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSMan_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSMan_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSMan_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSMan methods ***/
#define IWSMan_CreateSession(This,connection,flags,connectionOptions,session) (This)->lpVtbl->CreateSession(This,connection,flags,connectionOptions,session)
#define IWSMan_CreateConnectionOptions(This,connectionOptions) (This)->lpVtbl->CreateConnectionOptions(This,connectionOptions)
#define IWSMan_get_CommandLine(This,value) (This)->lpVtbl->get_CommandLine(This,value)
#define IWSMan_get_Error(This,value) (This)->lpVtbl->get_Error(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSMan_QueryInterface(IWSMan* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSMan_AddRef(IWSMan* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSMan_Release(IWSMan* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSMan_GetTypeInfoCount(IWSMan* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSMan_GetTypeInfo(IWSMan* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSMan_GetIDsOfNames(IWSMan* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSMan_Invoke(IWSMan* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSMan methods ***/
static inline HRESULT IWSMan_CreateSession(IWSMan* This,BSTR connection,LONG flags,IDispatch *connectionOptions,IDispatch **session) {
    return This->lpVtbl->CreateSession(This,connection,flags,connectionOptions,session);
}
static inline HRESULT IWSMan_CreateConnectionOptions(IWSMan* This,IDispatch **connectionOptions) {
    return This->lpVtbl->CreateConnectionOptions(This,connectionOptions);
}
static inline HRESULT IWSMan_get_CommandLine(IWSMan* This,BSTR *value) {
    return This->lpVtbl->get_CommandLine(This,value);
}
static inline HRESULT IWSMan_get_Error(IWSMan* This,BSTR *value) {
    return This->lpVtbl->get_Error(This,value);
}
#endif
#endif

#endif


#endif  /* __IWSMan_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSManEx interface
 */
#ifndef __IWSManEx_INTERFACE_DEFINED__
#define __IWSManEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSManEx, 0x2d53bdaa, 0x798e, 0x49e6, 0xa1,0xaa, 0x74,0xd0,0x12,0x56,0xf4,0x11);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2d53bdaa-798e-49e6-a1aa-74d01256f411")
IWSManEx : public IWSMan
{
    virtual HRESULT STDMETHODCALLTYPE CreateResourceLocator(
        BSTR strResourceLocator,
        IDispatch **newResourceLocator) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagUTF8(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagCredUsernamePassword(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagSkipCACheck(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagSkipCNCheck(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagUseDigest(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagUseNegotiate(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagUseBasic(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagUseKerberos(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagNoEncryption(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagEnableSPNServerPort(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagUseNoAuthentication(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumerationFlagNonXmlText(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumerationFlagReturnEPR(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumerationFlagReturnObjectAndEPR(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetErrorMessage(
        DWORD errorNumber,
        BSTR *errorMessage) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumerationFlagHierarchyDeep(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumerationFlagHierarchyShallow(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumerationFlagHierarchyDeepBasePropsOnly(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumerationFlagReturnObject(
        LONG *flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSManEx, 0x2d53bdaa, 0x798e, 0x49e6, 0xa1,0xaa, 0x74,0xd0,0x12,0x56,0xf4,0x11)
#endif
#else
typedef struct IWSManExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSManEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSManEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSManEx *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSManEx *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSManEx *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSManEx *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSManEx *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSMan methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateSession)(
        IWSManEx *This,
        BSTR connection,
        LONG flags,
        IDispatch *connectionOptions,
        IDispatch **session);

    HRESULT (STDMETHODCALLTYPE *CreateConnectionOptions)(
        IWSManEx *This,
        IDispatch **connectionOptions);

    HRESULT (STDMETHODCALLTYPE *get_CommandLine)(
        IWSManEx *This,
        BSTR *value);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWSManEx *This,
        BSTR *value);

    /*** IWSManEx methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateResourceLocator)(
        IWSManEx *This,
        BSTR strResourceLocator,
        IDispatch **newResourceLocator);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUTF8)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagCredUsernamePassword)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagSkipCACheck)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagSkipCNCheck)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseDigest)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseNegotiate)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseBasic)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseKerberos)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagNoEncryption)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagEnableSPNServerPort)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseNoAuthentication)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagNonXmlText)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagReturnEPR)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagReturnObjectAndEPR)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *GetErrorMessage)(
        IWSManEx *This,
        DWORD errorNumber,
        BSTR *errorMessage);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagHierarchyDeep)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagHierarchyShallow)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagHierarchyDeepBasePropsOnly)(
        IWSManEx *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagReturnObject)(
        IWSManEx *This,
        LONG *flags);

    END_INTERFACE
} IWSManExVtbl;

interface IWSManEx {
    CONST_VTBL IWSManExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSManEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSManEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSManEx_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSManEx_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSManEx_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSManEx_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSManEx_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSMan methods ***/
#define IWSManEx_CreateSession(This,connection,flags,connectionOptions,session) (This)->lpVtbl->CreateSession(This,connection,flags,connectionOptions,session)
#define IWSManEx_CreateConnectionOptions(This,connectionOptions) (This)->lpVtbl->CreateConnectionOptions(This,connectionOptions)
#define IWSManEx_get_CommandLine(This,value) (This)->lpVtbl->get_CommandLine(This,value)
#define IWSManEx_get_Error(This,value) (This)->lpVtbl->get_Error(This,value)
/*** IWSManEx methods ***/
#define IWSManEx_CreateResourceLocator(This,strResourceLocator,newResourceLocator) (This)->lpVtbl->CreateResourceLocator(This,strResourceLocator,newResourceLocator)
#define IWSManEx_SessionFlagUTF8(This,flags) (This)->lpVtbl->SessionFlagUTF8(This,flags)
#define IWSManEx_SessionFlagCredUsernamePassword(This,flags) (This)->lpVtbl->SessionFlagCredUsernamePassword(This,flags)
#define IWSManEx_SessionFlagSkipCACheck(This,flags) (This)->lpVtbl->SessionFlagSkipCACheck(This,flags)
#define IWSManEx_SessionFlagSkipCNCheck(This,flags) (This)->lpVtbl->SessionFlagSkipCNCheck(This,flags)
#define IWSManEx_SessionFlagUseDigest(This,flags) (This)->lpVtbl->SessionFlagUseDigest(This,flags)
#define IWSManEx_SessionFlagUseNegotiate(This,flags) (This)->lpVtbl->SessionFlagUseNegotiate(This,flags)
#define IWSManEx_SessionFlagUseBasic(This,flags) (This)->lpVtbl->SessionFlagUseBasic(This,flags)
#define IWSManEx_SessionFlagUseKerberos(This,flags) (This)->lpVtbl->SessionFlagUseKerberos(This,flags)
#define IWSManEx_SessionFlagNoEncryption(This,flags) (This)->lpVtbl->SessionFlagNoEncryption(This,flags)
#define IWSManEx_SessionFlagEnableSPNServerPort(This,flags) (This)->lpVtbl->SessionFlagEnableSPNServerPort(This,flags)
#define IWSManEx_SessionFlagUseNoAuthentication(This,flags) (This)->lpVtbl->SessionFlagUseNoAuthentication(This,flags)
#define IWSManEx_EnumerationFlagNonXmlText(This,flags) (This)->lpVtbl->EnumerationFlagNonXmlText(This,flags)
#define IWSManEx_EnumerationFlagReturnEPR(This,flags) (This)->lpVtbl->EnumerationFlagReturnEPR(This,flags)
#define IWSManEx_EnumerationFlagReturnObjectAndEPR(This,flags) (This)->lpVtbl->EnumerationFlagReturnObjectAndEPR(This,flags)
#define IWSManEx_GetErrorMessage(This,errorNumber,errorMessage) (This)->lpVtbl->GetErrorMessage(This,errorNumber,errorMessage)
#define IWSManEx_EnumerationFlagHierarchyDeep(This,flags) (This)->lpVtbl->EnumerationFlagHierarchyDeep(This,flags)
#define IWSManEx_EnumerationFlagHierarchyShallow(This,flags) (This)->lpVtbl->EnumerationFlagHierarchyShallow(This,flags)
#define IWSManEx_EnumerationFlagHierarchyDeepBasePropsOnly(This,flags) (This)->lpVtbl->EnumerationFlagHierarchyDeepBasePropsOnly(This,flags)
#define IWSManEx_EnumerationFlagReturnObject(This,flags) (This)->lpVtbl->EnumerationFlagReturnObject(This,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSManEx_QueryInterface(IWSManEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSManEx_AddRef(IWSManEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSManEx_Release(IWSManEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSManEx_GetTypeInfoCount(IWSManEx* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSManEx_GetTypeInfo(IWSManEx* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSManEx_GetIDsOfNames(IWSManEx* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSManEx_Invoke(IWSManEx* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSMan methods ***/
static inline HRESULT IWSManEx_CreateSession(IWSManEx* This,BSTR connection,LONG flags,IDispatch *connectionOptions,IDispatch **session) {
    return This->lpVtbl->CreateSession(This,connection,flags,connectionOptions,session);
}
static inline HRESULT IWSManEx_CreateConnectionOptions(IWSManEx* This,IDispatch **connectionOptions) {
    return This->lpVtbl->CreateConnectionOptions(This,connectionOptions);
}
static inline HRESULT IWSManEx_get_CommandLine(IWSManEx* This,BSTR *value) {
    return This->lpVtbl->get_CommandLine(This,value);
}
static inline HRESULT IWSManEx_get_Error(IWSManEx* This,BSTR *value) {
    return This->lpVtbl->get_Error(This,value);
}
/*** IWSManEx methods ***/
static inline HRESULT IWSManEx_CreateResourceLocator(IWSManEx* This,BSTR strResourceLocator,IDispatch **newResourceLocator) {
    return This->lpVtbl->CreateResourceLocator(This,strResourceLocator,newResourceLocator);
}
static inline HRESULT IWSManEx_SessionFlagUTF8(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUTF8(This,flags);
}
static inline HRESULT IWSManEx_SessionFlagCredUsernamePassword(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->SessionFlagCredUsernamePassword(This,flags);
}
static inline HRESULT IWSManEx_SessionFlagSkipCACheck(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->SessionFlagSkipCACheck(This,flags);
}
static inline HRESULT IWSManEx_SessionFlagSkipCNCheck(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->SessionFlagSkipCNCheck(This,flags);
}
static inline HRESULT IWSManEx_SessionFlagUseDigest(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseDigest(This,flags);
}
static inline HRESULT IWSManEx_SessionFlagUseNegotiate(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseNegotiate(This,flags);
}
static inline HRESULT IWSManEx_SessionFlagUseBasic(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseBasic(This,flags);
}
static inline HRESULT IWSManEx_SessionFlagUseKerberos(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseKerberos(This,flags);
}
static inline HRESULT IWSManEx_SessionFlagNoEncryption(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->SessionFlagNoEncryption(This,flags);
}
static inline HRESULT IWSManEx_SessionFlagEnableSPNServerPort(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->SessionFlagEnableSPNServerPort(This,flags);
}
static inline HRESULT IWSManEx_SessionFlagUseNoAuthentication(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseNoAuthentication(This,flags);
}
static inline HRESULT IWSManEx_EnumerationFlagNonXmlText(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagNonXmlText(This,flags);
}
static inline HRESULT IWSManEx_EnumerationFlagReturnEPR(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagReturnEPR(This,flags);
}
static inline HRESULT IWSManEx_EnumerationFlagReturnObjectAndEPR(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagReturnObjectAndEPR(This,flags);
}
static inline HRESULT IWSManEx_GetErrorMessage(IWSManEx* This,DWORD errorNumber,BSTR *errorMessage) {
    return This->lpVtbl->GetErrorMessage(This,errorNumber,errorMessage);
}
static inline HRESULT IWSManEx_EnumerationFlagHierarchyDeep(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagHierarchyDeep(This,flags);
}
static inline HRESULT IWSManEx_EnumerationFlagHierarchyShallow(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagHierarchyShallow(This,flags);
}
static inline HRESULT IWSManEx_EnumerationFlagHierarchyDeepBasePropsOnly(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagHierarchyDeepBasePropsOnly(This,flags);
}
static inline HRESULT IWSManEx_EnumerationFlagReturnObject(IWSManEx* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagReturnObject(This,flags);
}
#endif
#endif

#endif


#endif  /* __IWSManEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSManEx2 interface
 */
#ifndef __IWSManEx2_INTERFACE_DEFINED__
#define __IWSManEx2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSManEx2, 0x1d1b5ae0, 0x42d9, 0x4021, 0x82,0x61, 0x39,0x87,0x61,0x95,0x12,0xe9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1d1b5ae0-42d9-4021-8261-3987619512e9")
IWSManEx2 : public IWSManEx
{
    virtual HRESULT STDMETHODCALLTYPE SessionFlagUseClientCertificate(
        LONG *flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSManEx2, 0x1d1b5ae0, 0x42d9, 0x4021, 0x82,0x61, 0x39,0x87,0x61,0x95,0x12,0xe9)
#endif
#else
typedef struct IWSManEx2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSManEx2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSManEx2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSManEx2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSManEx2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSManEx2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSManEx2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSManEx2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSMan methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateSession)(
        IWSManEx2 *This,
        BSTR connection,
        LONG flags,
        IDispatch *connectionOptions,
        IDispatch **session);

    HRESULT (STDMETHODCALLTYPE *CreateConnectionOptions)(
        IWSManEx2 *This,
        IDispatch **connectionOptions);

    HRESULT (STDMETHODCALLTYPE *get_CommandLine)(
        IWSManEx2 *This,
        BSTR *value);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWSManEx2 *This,
        BSTR *value);

    /*** IWSManEx methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateResourceLocator)(
        IWSManEx2 *This,
        BSTR strResourceLocator,
        IDispatch **newResourceLocator);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUTF8)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagCredUsernamePassword)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagSkipCACheck)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagSkipCNCheck)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseDigest)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseNegotiate)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseBasic)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseKerberos)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagNoEncryption)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagEnableSPNServerPort)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseNoAuthentication)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagNonXmlText)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagReturnEPR)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagReturnObjectAndEPR)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *GetErrorMessage)(
        IWSManEx2 *This,
        DWORD errorNumber,
        BSTR *errorMessage);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagHierarchyDeep)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagHierarchyShallow)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagHierarchyDeepBasePropsOnly)(
        IWSManEx2 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagReturnObject)(
        IWSManEx2 *This,
        LONG *flags);

    /*** IWSManEx2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SessionFlagUseClientCertificate)(
        IWSManEx2 *This,
        LONG *flags);

    END_INTERFACE
} IWSManEx2Vtbl;

interface IWSManEx2 {
    CONST_VTBL IWSManEx2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSManEx2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSManEx2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSManEx2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSManEx2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSManEx2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSManEx2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSManEx2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSMan methods ***/
#define IWSManEx2_CreateSession(This,connection,flags,connectionOptions,session) (This)->lpVtbl->CreateSession(This,connection,flags,connectionOptions,session)
#define IWSManEx2_CreateConnectionOptions(This,connectionOptions) (This)->lpVtbl->CreateConnectionOptions(This,connectionOptions)
#define IWSManEx2_get_CommandLine(This,value) (This)->lpVtbl->get_CommandLine(This,value)
#define IWSManEx2_get_Error(This,value) (This)->lpVtbl->get_Error(This,value)
/*** IWSManEx methods ***/
#define IWSManEx2_CreateResourceLocator(This,strResourceLocator,newResourceLocator) (This)->lpVtbl->CreateResourceLocator(This,strResourceLocator,newResourceLocator)
#define IWSManEx2_SessionFlagUTF8(This,flags) (This)->lpVtbl->SessionFlagUTF8(This,flags)
#define IWSManEx2_SessionFlagCredUsernamePassword(This,flags) (This)->lpVtbl->SessionFlagCredUsernamePassword(This,flags)
#define IWSManEx2_SessionFlagSkipCACheck(This,flags) (This)->lpVtbl->SessionFlagSkipCACheck(This,flags)
#define IWSManEx2_SessionFlagSkipCNCheck(This,flags) (This)->lpVtbl->SessionFlagSkipCNCheck(This,flags)
#define IWSManEx2_SessionFlagUseDigest(This,flags) (This)->lpVtbl->SessionFlagUseDigest(This,flags)
#define IWSManEx2_SessionFlagUseNegotiate(This,flags) (This)->lpVtbl->SessionFlagUseNegotiate(This,flags)
#define IWSManEx2_SessionFlagUseBasic(This,flags) (This)->lpVtbl->SessionFlagUseBasic(This,flags)
#define IWSManEx2_SessionFlagUseKerberos(This,flags) (This)->lpVtbl->SessionFlagUseKerberos(This,flags)
#define IWSManEx2_SessionFlagNoEncryption(This,flags) (This)->lpVtbl->SessionFlagNoEncryption(This,flags)
#define IWSManEx2_SessionFlagEnableSPNServerPort(This,flags) (This)->lpVtbl->SessionFlagEnableSPNServerPort(This,flags)
#define IWSManEx2_SessionFlagUseNoAuthentication(This,flags) (This)->lpVtbl->SessionFlagUseNoAuthentication(This,flags)
#define IWSManEx2_EnumerationFlagNonXmlText(This,flags) (This)->lpVtbl->EnumerationFlagNonXmlText(This,flags)
#define IWSManEx2_EnumerationFlagReturnEPR(This,flags) (This)->lpVtbl->EnumerationFlagReturnEPR(This,flags)
#define IWSManEx2_EnumerationFlagReturnObjectAndEPR(This,flags) (This)->lpVtbl->EnumerationFlagReturnObjectAndEPR(This,flags)
#define IWSManEx2_GetErrorMessage(This,errorNumber,errorMessage) (This)->lpVtbl->GetErrorMessage(This,errorNumber,errorMessage)
#define IWSManEx2_EnumerationFlagHierarchyDeep(This,flags) (This)->lpVtbl->EnumerationFlagHierarchyDeep(This,flags)
#define IWSManEx2_EnumerationFlagHierarchyShallow(This,flags) (This)->lpVtbl->EnumerationFlagHierarchyShallow(This,flags)
#define IWSManEx2_EnumerationFlagHierarchyDeepBasePropsOnly(This,flags) (This)->lpVtbl->EnumerationFlagHierarchyDeepBasePropsOnly(This,flags)
#define IWSManEx2_EnumerationFlagReturnObject(This,flags) (This)->lpVtbl->EnumerationFlagReturnObject(This,flags)
/*** IWSManEx2 methods ***/
#define IWSManEx2_SessionFlagUseClientCertificate(This,flags) (This)->lpVtbl->SessionFlagUseClientCertificate(This,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSManEx2_QueryInterface(IWSManEx2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSManEx2_AddRef(IWSManEx2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSManEx2_Release(IWSManEx2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSManEx2_GetTypeInfoCount(IWSManEx2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSManEx2_GetTypeInfo(IWSManEx2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSManEx2_GetIDsOfNames(IWSManEx2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSManEx2_Invoke(IWSManEx2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSMan methods ***/
static inline HRESULT IWSManEx2_CreateSession(IWSManEx2* This,BSTR connection,LONG flags,IDispatch *connectionOptions,IDispatch **session) {
    return This->lpVtbl->CreateSession(This,connection,flags,connectionOptions,session);
}
static inline HRESULT IWSManEx2_CreateConnectionOptions(IWSManEx2* This,IDispatch **connectionOptions) {
    return This->lpVtbl->CreateConnectionOptions(This,connectionOptions);
}
static inline HRESULT IWSManEx2_get_CommandLine(IWSManEx2* This,BSTR *value) {
    return This->lpVtbl->get_CommandLine(This,value);
}
static inline HRESULT IWSManEx2_get_Error(IWSManEx2* This,BSTR *value) {
    return This->lpVtbl->get_Error(This,value);
}
/*** IWSManEx methods ***/
static inline HRESULT IWSManEx2_CreateResourceLocator(IWSManEx2* This,BSTR strResourceLocator,IDispatch **newResourceLocator) {
    return This->lpVtbl->CreateResourceLocator(This,strResourceLocator,newResourceLocator);
}
static inline HRESULT IWSManEx2_SessionFlagUTF8(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUTF8(This,flags);
}
static inline HRESULT IWSManEx2_SessionFlagCredUsernamePassword(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagCredUsernamePassword(This,flags);
}
static inline HRESULT IWSManEx2_SessionFlagSkipCACheck(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagSkipCACheck(This,flags);
}
static inline HRESULT IWSManEx2_SessionFlagSkipCNCheck(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagSkipCNCheck(This,flags);
}
static inline HRESULT IWSManEx2_SessionFlagUseDigest(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseDigest(This,flags);
}
static inline HRESULT IWSManEx2_SessionFlagUseNegotiate(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseNegotiate(This,flags);
}
static inline HRESULT IWSManEx2_SessionFlagUseBasic(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseBasic(This,flags);
}
static inline HRESULT IWSManEx2_SessionFlagUseKerberos(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseKerberos(This,flags);
}
static inline HRESULT IWSManEx2_SessionFlagNoEncryption(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagNoEncryption(This,flags);
}
static inline HRESULT IWSManEx2_SessionFlagEnableSPNServerPort(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagEnableSPNServerPort(This,flags);
}
static inline HRESULT IWSManEx2_SessionFlagUseNoAuthentication(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseNoAuthentication(This,flags);
}
static inline HRESULT IWSManEx2_EnumerationFlagNonXmlText(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagNonXmlText(This,flags);
}
static inline HRESULT IWSManEx2_EnumerationFlagReturnEPR(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagReturnEPR(This,flags);
}
static inline HRESULT IWSManEx2_EnumerationFlagReturnObjectAndEPR(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagReturnObjectAndEPR(This,flags);
}
static inline HRESULT IWSManEx2_GetErrorMessage(IWSManEx2* This,DWORD errorNumber,BSTR *errorMessage) {
    return This->lpVtbl->GetErrorMessage(This,errorNumber,errorMessage);
}
static inline HRESULT IWSManEx2_EnumerationFlagHierarchyDeep(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagHierarchyDeep(This,flags);
}
static inline HRESULT IWSManEx2_EnumerationFlagHierarchyShallow(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagHierarchyShallow(This,flags);
}
static inline HRESULT IWSManEx2_EnumerationFlagHierarchyDeepBasePropsOnly(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagHierarchyDeepBasePropsOnly(This,flags);
}
static inline HRESULT IWSManEx2_EnumerationFlagReturnObject(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagReturnObject(This,flags);
}
/*** IWSManEx2 methods ***/
static inline HRESULT IWSManEx2_SessionFlagUseClientCertificate(IWSManEx2* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseClientCertificate(This,flags);
}
#endif
#endif

#endif


#endif  /* __IWSManEx2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSManEx3 interface
 */
#ifndef __IWSManEx3_INTERFACE_DEFINED__
#define __IWSManEx3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSManEx3, 0x6400e966, 0x011d, 0x4eac, 0x84,0x74, 0x04,0x9e,0x08,0x48,0xaf,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6400e966-011d-4eac-8474-049e0848afad")
IWSManEx3 : public IWSManEx2
{
    virtual HRESULT STDMETHODCALLTYPE SessionFlagUTF16(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagUseCredSsp(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumerationFlagAssociationInstance(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumerationFlagAssociatedInstance(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagSkipRevocationCheck(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagAllowNegotiateImplicitCredentials(
        LONG *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SessionFlagUseSsl(
        LONG *flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSManEx3, 0x6400e966, 0x011d, 0x4eac, 0x84,0x74, 0x04,0x9e,0x08,0x48,0xaf,0xad)
#endif
#else
typedef struct IWSManEx3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSManEx3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSManEx3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSManEx3 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSManEx3 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSManEx3 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSManEx3 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSManEx3 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSMan methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateSession)(
        IWSManEx3 *This,
        BSTR connection,
        LONG flags,
        IDispatch *connectionOptions,
        IDispatch **session);

    HRESULT (STDMETHODCALLTYPE *CreateConnectionOptions)(
        IWSManEx3 *This,
        IDispatch **connectionOptions);

    HRESULT (STDMETHODCALLTYPE *get_CommandLine)(
        IWSManEx3 *This,
        BSTR *value);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWSManEx3 *This,
        BSTR *value);

    /*** IWSManEx methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateResourceLocator)(
        IWSManEx3 *This,
        BSTR strResourceLocator,
        IDispatch **newResourceLocator);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUTF8)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagCredUsernamePassword)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagSkipCACheck)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagSkipCNCheck)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseDigest)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseNegotiate)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseBasic)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseKerberos)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagNoEncryption)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagEnableSPNServerPort)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseNoAuthentication)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagNonXmlText)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagReturnEPR)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagReturnObjectAndEPR)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *GetErrorMessage)(
        IWSManEx3 *This,
        DWORD errorNumber,
        BSTR *errorMessage);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagHierarchyDeep)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagHierarchyShallow)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagHierarchyDeepBasePropsOnly)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagReturnObject)(
        IWSManEx3 *This,
        LONG *flags);

    /*** IWSManEx2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SessionFlagUseClientCertificate)(
        IWSManEx3 *This,
        LONG *flags);

    /*** IWSManEx3 methods ***/
    HRESULT (STDMETHODCALLTYPE *SessionFlagUTF16)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseCredSsp)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagAssociationInstance)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *EnumerationFlagAssociatedInstance)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagSkipRevocationCheck)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagAllowNegotiateImplicitCredentials)(
        IWSManEx3 *This,
        LONG *flags);

    HRESULT (STDMETHODCALLTYPE *SessionFlagUseSsl)(
        IWSManEx3 *This,
        LONG *flags);

    END_INTERFACE
} IWSManEx3Vtbl;

interface IWSManEx3 {
    CONST_VTBL IWSManEx3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSManEx3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSManEx3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSManEx3_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSManEx3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSManEx3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSManEx3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSManEx3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSMan methods ***/
#define IWSManEx3_CreateSession(This,connection,flags,connectionOptions,session) (This)->lpVtbl->CreateSession(This,connection,flags,connectionOptions,session)
#define IWSManEx3_CreateConnectionOptions(This,connectionOptions) (This)->lpVtbl->CreateConnectionOptions(This,connectionOptions)
#define IWSManEx3_get_CommandLine(This,value) (This)->lpVtbl->get_CommandLine(This,value)
#define IWSManEx3_get_Error(This,value) (This)->lpVtbl->get_Error(This,value)
/*** IWSManEx methods ***/
#define IWSManEx3_CreateResourceLocator(This,strResourceLocator,newResourceLocator) (This)->lpVtbl->CreateResourceLocator(This,strResourceLocator,newResourceLocator)
#define IWSManEx3_SessionFlagUTF8(This,flags) (This)->lpVtbl->SessionFlagUTF8(This,flags)
#define IWSManEx3_SessionFlagCredUsernamePassword(This,flags) (This)->lpVtbl->SessionFlagCredUsernamePassword(This,flags)
#define IWSManEx3_SessionFlagSkipCACheck(This,flags) (This)->lpVtbl->SessionFlagSkipCACheck(This,flags)
#define IWSManEx3_SessionFlagSkipCNCheck(This,flags) (This)->lpVtbl->SessionFlagSkipCNCheck(This,flags)
#define IWSManEx3_SessionFlagUseDigest(This,flags) (This)->lpVtbl->SessionFlagUseDigest(This,flags)
#define IWSManEx3_SessionFlagUseNegotiate(This,flags) (This)->lpVtbl->SessionFlagUseNegotiate(This,flags)
#define IWSManEx3_SessionFlagUseBasic(This,flags) (This)->lpVtbl->SessionFlagUseBasic(This,flags)
#define IWSManEx3_SessionFlagUseKerberos(This,flags) (This)->lpVtbl->SessionFlagUseKerberos(This,flags)
#define IWSManEx3_SessionFlagNoEncryption(This,flags) (This)->lpVtbl->SessionFlagNoEncryption(This,flags)
#define IWSManEx3_SessionFlagEnableSPNServerPort(This,flags) (This)->lpVtbl->SessionFlagEnableSPNServerPort(This,flags)
#define IWSManEx3_SessionFlagUseNoAuthentication(This,flags) (This)->lpVtbl->SessionFlagUseNoAuthentication(This,flags)
#define IWSManEx3_EnumerationFlagNonXmlText(This,flags) (This)->lpVtbl->EnumerationFlagNonXmlText(This,flags)
#define IWSManEx3_EnumerationFlagReturnEPR(This,flags) (This)->lpVtbl->EnumerationFlagReturnEPR(This,flags)
#define IWSManEx3_EnumerationFlagReturnObjectAndEPR(This,flags) (This)->lpVtbl->EnumerationFlagReturnObjectAndEPR(This,flags)
#define IWSManEx3_GetErrorMessage(This,errorNumber,errorMessage) (This)->lpVtbl->GetErrorMessage(This,errorNumber,errorMessage)
#define IWSManEx3_EnumerationFlagHierarchyDeep(This,flags) (This)->lpVtbl->EnumerationFlagHierarchyDeep(This,flags)
#define IWSManEx3_EnumerationFlagHierarchyShallow(This,flags) (This)->lpVtbl->EnumerationFlagHierarchyShallow(This,flags)
#define IWSManEx3_EnumerationFlagHierarchyDeepBasePropsOnly(This,flags) (This)->lpVtbl->EnumerationFlagHierarchyDeepBasePropsOnly(This,flags)
#define IWSManEx3_EnumerationFlagReturnObject(This,flags) (This)->lpVtbl->EnumerationFlagReturnObject(This,flags)
/*** IWSManEx2 methods ***/
#define IWSManEx3_SessionFlagUseClientCertificate(This,flags) (This)->lpVtbl->SessionFlagUseClientCertificate(This,flags)
/*** IWSManEx3 methods ***/
#define IWSManEx3_SessionFlagUTF16(This,flags) (This)->lpVtbl->SessionFlagUTF16(This,flags)
#define IWSManEx3_SessionFlagUseCredSsp(This,flags) (This)->lpVtbl->SessionFlagUseCredSsp(This,flags)
#define IWSManEx3_EnumerationFlagAssociationInstance(This,flags) (This)->lpVtbl->EnumerationFlagAssociationInstance(This,flags)
#define IWSManEx3_EnumerationFlagAssociatedInstance(This,flags) (This)->lpVtbl->EnumerationFlagAssociatedInstance(This,flags)
#define IWSManEx3_SessionFlagSkipRevocationCheck(This,flags) (This)->lpVtbl->SessionFlagSkipRevocationCheck(This,flags)
#define IWSManEx3_SessionFlagAllowNegotiateImplicitCredentials(This,flags) (This)->lpVtbl->SessionFlagAllowNegotiateImplicitCredentials(This,flags)
#define IWSManEx3_SessionFlagUseSsl(This,flags) (This)->lpVtbl->SessionFlagUseSsl(This,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSManEx3_QueryInterface(IWSManEx3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSManEx3_AddRef(IWSManEx3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSManEx3_Release(IWSManEx3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSManEx3_GetTypeInfoCount(IWSManEx3* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSManEx3_GetTypeInfo(IWSManEx3* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSManEx3_GetIDsOfNames(IWSManEx3* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSManEx3_Invoke(IWSManEx3* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSMan methods ***/
static inline HRESULT IWSManEx3_CreateSession(IWSManEx3* This,BSTR connection,LONG flags,IDispatch *connectionOptions,IDispatch **session) {
    return This->lpVtbl->CreateSession(This,connection,flags,connectionOptions,session);
}
static inline HRESULT IWSManEx3_CreateConnectionOptions(IWSManEx3* This,IDispatch **connectionOptions) {
    return This->lpVtbl->CreateConnectionOptions(This,connectionOptions);
}
static inline HRESULT IWSManEx3_get_CommandLine(IWSManEx3* This,BSTR *value) {
    return This->lpVtbl->get_CommandLine(This,value);
}
static inline HRESULT IWSManEx3_get_Error(IWSManEx3* This,BSTR *value) {
    return This->lpVtbl->get_Error(This,value);
}
/*** IWSManEx methods ***/
static inline HRESULT IWSManEx3_CreateResourceLocator(IWSManEx3* This,BSTR strResourceLocator,IDispatch **newResourceLocator) {
    return This->lpVtbl->CreateResourceLocator(This,strResourceLocator,newResourceLocator);
}
static inline HRESULT IWSManEx3_SessionFlagUTF8(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUTF8(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagCredUsernamePassword(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagCredUsernamePassword(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagSkipCACheck(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagSkipCACheck(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagSkipCNCheck(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagSkipCNCheck(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagUseDigest(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseDigest(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagUseNegotiate(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseNegotiate(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagUseBasic(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseBasic(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagUseKerberos(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseKerberos(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagNoEncryption(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagNoEncryption(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagEnableSPNServerPort(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagEnableSPNServerPort(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagUseNoAuthentication(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseNoAuthentication(This,flags);
}
static inline HRESULT IWSManEx3_EnumerationFlagNonXmlText(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagNonXmlText(This,flags);
}
static inline HRESULT IWSManEx3_EnumerationFlagReturnEPR(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagReturnEPR(This,flags);
}
static inline HRESULT IWSManEx3_EnumerationFlagReturnObjectAndEPR(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagReturnObjectAndEPR(This,flags);
}
static inline HRESULT IWSManEx3_GetErrorMessage(IWSManEx3* This,DWORD errorNumber,BSTR *errorMessage) {
    return This->lpVtbl->GetErrorMessage(This,errorNumber,errorMessage);
}
static inline HRESULT IWSManEx3_EnumerationFlagHierarchyDeep(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagHierarchyDeep(This,flags);
}
static inline HRESULT IWSManEx3_EnumerationFlagHierarchyShallow(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagHierarchyShallow(This,flags);
}
static inline HRESULT IWSManEx3_EnumerationFlagHierarchyDeepBasePropsOnly(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagHierarchyDeepBasePropsOnly(This,flags);
}
static inline HRESULT IWSManEx3_EnumerationFlagReturnObject(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagReturnObject(This,flags);
}
/*** IWSManEx2 methods ***/
static inline HRESULT IWSManEx3_SessionFlagUseClientCertificate(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseClientCertificate(This,flags);
}
/*** IWSManEx3 methods ***/
static inline HRESULT IWSManEx3_SessionFlagUTF16(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUTF16(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagUseCredSsp(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseCredSsp(This,flags);
}
static inline HRESULT IWSManEx3_EnumerationFlagAssociationInstance(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagAssociationInstance(This,flags);
}
static inline HRESULT IWSManEx3_EnumerationFlagAssociatedInstance(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->EnumerationFlagAssociatedInstance(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagSkipRevocationCheck(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagSkipRevocationCheck(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagAllowNegotiateImplicitCredentials(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagAllowNegotiateImplicitCredentials(This,flags);
}
static inline HRESULT IWSManEx3_SessionFlagUseSsl(IWSManEx3* This,LONG *flags) {
    return This->lpVtbl->SessionFlagUseSsl(This,flags);
}
#endif
#endif

#endif


#endif  /* __IWSManEx3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSManConnectionOptions interface
 */
#ifndef __IWSManConnectionOptions_INTERFACE_DEFINED__
#define __IWSManConnectionOptions_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSManConnectionOptions, 0xf704e861, 0x9e52, 0x464f, 0xb7,0x86, 0xda,0x5e,0xb2,0x32,0x0f,0xdd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f704e861-9e52-464f-b786-da5eb2320fdd")
IWSManConnectionOptions : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_UserName(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_UserName(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Password(
        BSTR password) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSManConnectionOptions, 0xf704e861, 0x9e52, 0x464f, 0xb7,0x86, 0xda,0x5e,0xb2,0x32,0x0f,0xdd)
#endif
#else
typedef struct IWSManConnectionOptionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSManConnectionOptions *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSManConnectionOptions *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSManConnectionOptions *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSManConnectionOptions *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSManConnectionOptions *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSManConnectionOptions *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSManConnectionOptions *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSManConnectionOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *get_UserName)(
        IWSManConnectionOptions *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_UserName)(
        IWSManConnectionOptions *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *put_Password)(
        IWSManConnectionOptions *This,
        BSTR password);

    END_INTERFACE
} IWSManConnectionOptionsVtbl;

interface IWSManConnectionOptions {
    CONST_VTBL IWSManConnectionOptionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSManConnectionOptions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSManConnectionOptions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSManConnectionOptions_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSManConnectionOptions_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSManConnectionOptions_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSManConnectionOptions_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSManConnectionOptions_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSManConnectionOptions methods ***/
#define IWSManConnectionOptions_get_UserName(This,name) (This)->lpVtbl->get_UserName(This,name)
#define IWSManConnectionOptions_put_UserName(This,name) (This)->lpVtbl->put_UserName(This,name)
#define IWSManConnectionOptions_put_Password(This,password) (This)->lpVtbl->put_Password(This,password)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSManConnectionOptions_QueryInterface(IWSManConnectionOptions* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSManConnectionOptions_AddRef(IWSManConnectionOptions* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSManConnectionOptions_Release(IWSManConnectionOptions* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSManConnectionOptions_GetTypeInfoCount(IWSManConnectionOptions* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSManConnectionOptions_GetTypeInfo(IWSManConnectionOptions* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSManConnectionOptions_GetIDsOfNames(IWSManConnectionOptions* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSManConnectionOptions_Invoke(IWSManConnectionOptions* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSManConnectionOptions methods ***/
static inline HRESULT IWSManConnectionOptions_get_UserName(IWSManConnectionOptions* This,BSTR *name) {
    return This->lpVtbl->get_UserName(This,name);
}
static inline HRESULT IWSManConnectionOptions_put_UserName(IWSManConnectionOptions* This,BSTR name) {
    return This->lpVtbl->put_UserName(This,name);
}
static inline HRESULT IWSManConnectionOptions_put_Password(IWSManConnectionOptions* This,BSTR password) {
    return This->lpVtbl->put_Password(This,password);
}
#endif
#endif

#endif


#endif  /* __IWSManConnectionOptions_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSManConnectionOptionsEx interface
 */
#ifndef __IWSManConnectionOptionsEx_INTERFACE_DEFINED__
#define __IWSManConnectionOptionsEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSManConnectionOptionsEx, 0xef43edf7, 0x2a48, 0x4d93, 0x95,0x26, 0x8b,0xd6,0xab,0x6d,0x4a,0x6b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ef43edf7-2a48-4d93-9526-8bd6ab6d4a6b")
IWSManConnectionOptionsEx : public IWSManConnectionOptions
{
    virtual HRESULT STDMETHODCALLTYPE get_CertificateThumbprint(
        BSTR *thumbprint) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_CertificateThumbprint(
        BSTR thumbprint) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSManConnectionOptionsEx, 0xef43edf7, 0x2a48, 0x4d93, 0x95,0x26, 0x8b,0xd6,0xab,0x6d,0x4a,0x6b)
#endif
#else
typedef struct IWSManConnectionOptionsExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSManConnectionOptionsEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSManConnectionOptionsEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSManConnectionOptionsEx *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSManConnectionOptionsEx *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSManConnectionOptionsEx *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSManConnectionOptionsEx *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSManConnectionOptionsEx *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSManConnectionOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *get_UserName)(
        IWSManConnectionOptionsEx *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_UserName)(
        IWSManConnectionOptionsEx *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *put_Password)(
        IWSManConnectionOptionsEx *This,
        BSTR password);

    /*** IWSManConnectionOptionsEx methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CertificateThumbprint)(
        IWSManConnectionOptionsEx *This,
        BSTR *thumbprint);

    HRESULT (STDMETHODCALLTYPE *put_CertificateThumbprint)(
        IWSManConnectionOptionsEx *This,
        BSTR thumbprint);

    END_INTERFACE
} IWSManConnectionOptionsExVtbl;

interface IWSManConnectionOptionsEx {
    CONST_VTBL IWSManConnectionOptionsExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSManConnectionOptionsEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSManConnectionOptionsEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSManConnectionOptionsEx_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSManConnectionOptionsEx_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSManConnectionOptionsEx_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSManConnectionOptionsEx_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSManConnectionOptionsEx_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSManConnectionOptions methods ***/
#define IWSManConnectionOptionsEx_get_UserName(This,name) (This)->lpVtbl->get_UserName(This,name)
#define IWSManConnectionOptionsEx_put_UserName(This,name) (This)->lpVtbl->put_UserName(This,name)
#define IWSManConnectionOptionsEx_put_Password(This,password) (This)->lpVtbl->put_Password(This,password)
/*** IWSManConnectionOptionsEx methods ***/
#define IWSManConnectionOptionsEx_get_CertificateThumbprint(This,thumbprint) (This)->lpVtbl->get_CertificateThumbprint(This,thumbprint)
#define IWSManConnectionOptionsEx_put_CertificateThumbprint(This,thumbprint) (This)->lpVtbl->put_CertificateThumbprint(This,thumbprint)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSManConnectionOptionsEx_QueryInterface(IWSManConnectionOptionsEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSManConnectionOptionsEx_AddRef(IWSManConnectionOptionsEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSManConnectionOptionsEx_Release(IWSManConnectionOptionsEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSManConnectionOptionsEx_GetTypeInfoCount(IWSManConnectionOptionsEx* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSManConnectionOptionsEx_GetTypeInfo(IWSManConnectionOptionsEx* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSManConnectionOptionsEx_GetIDsOfNames(IWSManConnectionOptionsEx* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSManConnectionOptionsEx_Invoke(IWSManConnectionOptionsEx* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSManConnectionOptions methods ***/
static inline HRESULT IWSManConnectionOptionsEx_get_UserName(IWSManConnectionOptionsEx* This,BSTR *name) {
    return This->lpVtbl->get_UserName(This,name);
}
static inline HRESULT IWSManConnectionOptionsEx_put_UserName(IWSManConnectionOptionsEx* This,BSTR name) {
    return This->lpVtbl->put_UserName(This,name);
}
static inline HRESULT IWSManConnectionOptionsEx_put_Password(IWSManConnectionOptionsEx* This,BSTR password) {
    return This->lpVtbl->put_Password(This,password);
}
/*** IWSManConnectionOptionsEx methods ***/
static inline HRESULT IWSManConnectionOptionsEx_get_CertificateThumbprint(IWSManConnectionOptionsEx* This,BSTR *thumbprint) {
    return This->lpVtbl->get_CertificateThumbprint(This,thumbprint);
}
static inline HRESULT IWSManConnectionOptionsEx_put_CertificateThumbprint(IWSManConnectionOptionsEx* This,BSTR thumbprint) {
    return This->lpVtbl->put_CertificateThumbprint(This,thumbprint);
}
#endif
#endif

#endif


#endif  /* __IWSManConnectionOptionsEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSManConnectionOptionsEx2 interface
 */
#ifndef __IWSManConnectionOptionsEx2_INTERFACE_DEFINED__
#define __IWSManConnectionOptionsEx2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSManConnectionOptionsEx2, 0xf500c9ec, 0x24ee, 0x48ab, 0xb3,0x8d, 0xfc,0x9a,0x16,0x4c,0x65,0x8e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f500c9ec-24ee-48ab-b38d-fc9a164c658e")
IWSManConnectionOptionsEx2 : public IWSManConnectionOptionsEx
{
    virtual HRESULT STDMETHODCALLTYPE SetProxy(
        LONG accessType = 0,
        LONG authenticationMechanism = 0,
        BSTR userName = L"",
        BSTR password = L"") = 0;

    virtual HRESULT STDMETHODCALLTYPE ProxyIEConfig(
        LONG *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProxyWinHttpConfig(
        LONG *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProxyAutoDetect(
        LONG *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProxyNoProxyServer(
        LONG *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProxyAuthenticationUseNegotiate(
        LONG *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProxyAuthenticationUseBasic(
        LONG *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProxyAuthenticationUseDigest(
        LONG *value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSManConnectionOptionsEx2, 0xf500c9ec, 0x24ee, 0x48ab, 0xb3,0x8d, 0xfc,0x9a,0x16,0x4c,0x65,0x8e)
#endif
#else
typedef struct IWSManConnectionOptionsEx2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSManConnectionOptionsEx2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSManConnectionOptionsEx2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSManConnectionOptionsEx2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSManConnectionOptionsEx2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSManConnectionOptionsEx2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSManConnectionOptionsEx2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSManConnectionOptionsEx2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSManConnectionOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *get_UserName)(
        IWSManConnectionOptionsEx2 *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_UserName)(
        IWSManConnectionOptionsEx2 *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *put_Password)(
        IWSManConnectionOptionsEx2 *This,
        BSTR password);

    /*** IWSManConnectionOptionsEx methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CertificateThumbprint)(
        IWSManConnectionOptionsEx2 *This,
        BSTR *thumbprint);

    HRESULT (STDMETHODCALLTYPE *put_CertificateThumbprint)(
        IWSManConnectionOptionsEx2 *This,
        BSTR thumbprint);

    /*** IWSManConnectionOptionsEx2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetProxy)(
        IWSManConnectionOptionsEx2 *This,
        LONG accessType,
        LONG authenticationMechanism,
        BSTR userName,
        BSTR password);

    HRESULT (STDMETHODCALLTYPE *ProxyIEConfig)(
        IWSManConnectionOptionsEx2 *This,
        LONG *value);

    HRESULT (STDMETHODCALLTYPE *ProxyWinHttpConfig)(
        IWSManConnectionOptionsEx2 *This,
        LONG *value);

    HRESULT (STDMETHODCALLTYPE *ProxyAutoDetect)(
        IWSManConnectionOptionsEx2 *This,
        LONG *value);

    HRESULT (STDMETHODCALLTYPE *ProxyNoProxyServer)(
        IWSManConnectionOptionsEx2 *This,
        LONG *value);

    HRESULT (STDMETHODCALLTYPE *ProxyAuthenticationUseNegotiate)(
        IWSManConnectionOptionsEx2 *This,
        LONG *value);

    HRESULT (STDMETHODCALLTYPE *ProxyAuthenticationUseBasic)(
        IWSManConnectionOptionsEx2 *This,
        LONG *value);

    HRESULT (STDMETHODCALLTYPE *ProxyAuthenticationUseDigest)(
        IWSManConnectionOptionsEx2 *This,
        LONG *value);

    END_INTERFACE
} IWSManConnectionOptionsEx2Vtbl;

interface IWSManConnectionOptionsEx2 {
    CONST_VTBL IWSManConnectionOptionsEx2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSManConnectionOptionsEx2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSManConnectionOptionsEx2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSManConnectionOptionsEx2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSManConnectionOptionsEx2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSManConnectionOptionsEx2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSManConnectionOptionsEx2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSManConnectionOptionsEx2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSManConnectionOptions methods ***/
#define IWSManConnectionOptionsEx2_get_UserName(This,name) (This)->lpVtbl->get_UserName(This,name)
#define IWSManConnectionOptionsEx2_put_UserName(This,name) (This)->lpVtbl->put_UserName(This,name)
#define IWSManConnectionOptionsEx2_put_Password(This,password) (This)->lpVtbl->put_Password(This,password)
/*** IWSManConnectionOptionsEx methods ***/
#define IWSManConnectionOptionsEx2_get_CertificateThumbprint(This,thumbprint) (This)->lpVtbl->get_CertificateThumbprint(This,thumbprint)
#define IWSManConnectionOptionsEx2_put_CertificateThumbprint(This,thumbprint) (This)->lpVtbl->put_CertificateThumbprint(This,thumbprint)
/*** IWSManConnectionOptionsEx2 methods ***/
#define IWSManConnectionOptionsEx2_SetProxy(This,accessType,authenticationMechanism,userName,password) (This)->lpVtbl->SetProxy(This,accessType,authenticationMechanism,userName,password)
#define IWSManConnectionOptionsEx2_ProxyIEConfig(This,value) (This)->lpVtbl->ProxyIEConfig(This,value)
#define IWSManConnectionOptionsEx2_ProxyWinHttpConfig(This,value) (This)->lpVtbl->ProxyWinHttpConfig(This,value)
#define IWSManConnectionOptionsEx2_ProxyAutoDetect(This,value) (This)->lpVtbl->ProxyAutoDetect(This,value)
#define IWSManConnectionOptionsEx2_ProxyNoProxyServer(This,value) (This)->lpVtbl->ProxyNoProxyServer(This,value)
#define IWSManConnectionOptionsEx2_ProxyAuthenticationUseNegotiate(This,value) (This)->lpVtbl->ProxyAuthenticationUseNegotiate(This,value)
#define IWSManConnectionOptionsEx2_ProxyAuthenticationUseBasic(This,value) (This)->lpVtbl->ProxyAuthenticationUseBasic(This,value)
#define IWSManConnectionOptionsEx2_ProxyAuthenticationUseDigest(This,value) (This)->lpVtbl->ProxyAuthenticationUseDigest(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSManConnectionOptionsEx2_QueryInterface(IWSManConnectionOptionsEx2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSManConnectionOptionsEx2_AddRef(IWSManConnectionOptionsEx2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSManConnectionOptionsEx2_Release(IWSManConnectionOptionsEx2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSManConnectionOptionsEx2_GetTypeInfoCount(IWSManConnectionOptionsEx2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSManConnectionOptionsEx2_GetTypeInfo(IWSManConnectionOptionsEx2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSManConnectionOptionsEx2_GetIDsOfNames(IWSManConnectionOptionsEx2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSManConnectionOptionsEx2_Invoke(IWSManConnectionOptionsEx2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSManConnectionOptions methods ***/
static inline HRESULT IWSManConnectionOptionsEx2_get_UserName(IWSManConnectionOptionsEx2* This,BSTR *name) {
    return This->lpVtbl->get_UserName(This,name);
}
static inline HRESULT IWSManConnectionOptionsEx2_put_UserName(IWSManConnectionOptionsEx2* This,BSTR name) {
    return This->lpVtbl->put_UserName(This,name);
}
static inline HRESULT IWSManConnectionOptionsEx2_put_Password(IWSManConnectionOptionsEx2* This,BSTR password) {
    return This->lpVtbl->put_Password(This,password);
}
/*** IWSManConnectionOptionsEx methods ***/
static inline HRESULT IWSManConnectionOptionsEx2_get_CertificateThumbprint(IWSManConnectionOptionsEx2* This,BSTR *thumbprint) {
    return This->lpVtbl->get_CertificateThumbprint(This,thumbprint);
}
static inline HRESULT IWSManConnectionOptionsEx2_put_CertificateThumbprint(IWSManConnectionOptionsEx2* This,BSTR thumbprint) {
    return This->lpVtbl->put_CertificateThumbprint(This,thumbprint);
}
/*** IWSManConnectionOptionsEx2 methods ***/
static inline HRESULT IWSManConnectionOptionsEx2_SetProxy(IWSManConnectionOptionsEx2* This,LONG accessType,LONG authenticationMechanism,BSTR userName,BSTR password) {
    return This->lpVtbl->SetProxy(This,accessType,authenticationMechanism,userName,password);
}
static inline HRESULT IWSManConnectionOptionsEx2_ProxyIEConfig(IWSManConnectionOptionsEx2* This,LONG *value) {
    return This->lpVtbl->ProxyIEConfig(This,value);
}
static inline HRESULT IWSManConnectionOptionsEx2_ProxyWinHttpConfig(IWSManConnectionOptionsEx2* This,LONG *value) {
    return This->lpVtbl->ProxyWinHttpConfig(This,value);
}
static inline HRESULT IWSManConnectionOptionsEx2_ProxyAutoDetect(IWSManConnectionOptionsEx2* This,LONG *value) {
    return This->lpVtbl->ProxyAutoDetect(This,value);
}
static inline HRESULT IWSManConnectionOptionsEx2_ProxyNoProxyServer(IWSManConnectionOptionsEx2* This,LONG *value) {
    return This->lpVtbl->ProxyNoProxyServer(This,value);
}
static inline HRESULT IWSManConnectionOptionsEx2_ProxyAuthenticationUseNegotiate(IWSManConnectionOptionsEx2* This,LONG *value) {
    return This->lpVtbl->ProxyAuthenticationUseNegotiate(This,value);
}
static inline HRESULT IWSManConnectionOptionsEx2_ProxyAuthenticationUseBasic(IWSManConnectionOptionsEx2* This,LONG *value) {
    return This->lpVtbl->ProxyAuthenticationUseBasic(This,value);
}
static inline HRESULT IWSManConnectionOptionsEx2_ProxyAuthenticationUseDigest(IWSManConnectionOptionsEx2* This,LONG *value) {
    return This->lpVtbl->ProxyAuthenticationUseDigest(This,value);
}
#endif
#endif

#endif


#endif  /* __IWSManConnectionOptionsEx2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSManSession interface
 */
#ifndef __IWSManSession_INTERFACE_DEFINED__
#define __IWSManSession_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSManSession, 0xfc84dc58, 0x1286, 0x40c4, 0x9d,0xa0, 0xc8,0xef,0x6e,0xc2,0x41,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fc84dc58-1286-40c4-9da0-c8ef6ec241e0")
IWSManSession : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE Get(
        VARIANT resourceUri,
        LONG flags,
        BSTR *resource) = 0;

    virtual HRESULT STDMETHODCALLTYPE Put(
        VARIANT resourceUri,
        BSTR resource,
        LONG flags,
        BSTR *resultResource) = 0;

    virtual HRESULT STDMETHODCALLTYPE Create(
        VARIANT resourceUri,
        BSTR resource,
        LONG flags,
        BSTR *newUri) = 0;

    virtual HRESULT STDMETHODCALLTYPE Delete(
        VARIANT resourceUri,
        LONG flags = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE Invoke(
        BSTR actionUri,
        VARIANT resourceUri,
        BSTR parameters,
        LONG flags,
        BSTR *result) = 0;

    virtual HRESULT STDMETHODCALLTYPE Enumerate(
        VARIANT resourceUri,
        BSTR filter,
        BSTR dialect,
        LONG flags,
        IDispatch **resultSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE Identify(
        LONG flags,
        BSTR *result) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Error(
        BSTR *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_BatchItems(
        LONG *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_BatchItems(
        LONG value) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Timeout(
        LONG *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Timeout(
        LONG value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSManSession, 0xfc84dc58, 0x1286, 0x40c4, 0x9d,0xa0, 0xc8,0xef,0x6e,0xc2,0x41,0xe0)
#endif
#else
typedef struct IWSManSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSManSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSManSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSManSession *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSManSession *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSManSession *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSManSession *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSManSession *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSManSession methods ***/
    HRESULT (STDMETHODCALLTYPE *Get)(
        IWSManSession *This,
        VARIANT resourceUri,
        LONG flags,
        BSTR *resource);

    HRESULT (STDMETHODCALLTYPE *Put)(
        IWSManSession *This,
        VARIANT resourceUri,
        BSTR resource,
        LONG flags,
        BSTR *resultResource);

    HRESULT (STDMETHODCALLTYPE *Create)(
        IWSManSession *This,
        VARIANT resourceUri,
        BSTR resource,
        LONG flags,
        BSTR *newUri);

    HRESULT (STDMETHODCALLTYPE *Delete)(
        IWSManSession *This,
        VARIANT resourceUri,
        LONG flags);

    HRESULT (STDMETHODCALLTYPE *IWSManSession_Invoke)(
        IWSManSession *This,
        BSTR actionUri,
        VARIANT resourceUri,
        BSTR parameters,
        LONG flags,
        BSTR *result);

    HRESULT (STDMETHODCALLTYPE *Enumerate)(
        IWSManSession *This,
        VARIANT resourceUri,
        BSTR filter,
        BSTR dialect,
        LONG flags,
        IDispatch **resultSet);

    HRESULT (STDMETHODCALLTYPE *Identify)(
        IWSManSession *This,
        LONG flags,
        BSTR *result);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWSManSession *This,
        BSTR *value);

    HRESULT (STDMETHODCALLTYPE *get_BatchItems)(
        IWSManSession *This,
        LONG *value);

    HRESULT (STDMETHODCALLTYPE *put_BatchItems)(
        IWSManSession *This,
        LONG value);

    HRESULT (STDMETHODCALLTYPE *get_Timeout)(
        IWSManSession *This,
        LONG *value);

    HRESULT (STDMETHODCALLTYPE *put_Timeout)(
        IWSManSession *This,
        LONG value);

    END_INTERFACE
} IWSManSessionVtbl;

interface IWSManSession {
    CONST_VTBL IWSManSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSManSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSManSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSManSession_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSManSession_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSManSession_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSManSession_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
/*** IWSManSession methods ***/
#define IWSManSession_Get(This,resourceUri,flags,resource) (This)->lpVtbl->Get(This,resourceUri,flags,resource)
#define IWSManSession_Put(This,resourceUri,resource,flags,resultResource) (This)->lpVtbl->Put(This,resourceUri,resource,flags,resultResource)
#define IWSManSession_Create(This,resourceUri,resource,flags,newUri) (This)->lpVtbl->Create(This,resourceUri,resource,flags,newUri)
#define IWSManSession_Delete(This,resourceUri,flags) (This)->lpVtbl->Delete(This,resourceUri,flags)
#define IWSManSession_Invoke(This,actionUri,resourceUri,parameters,flags,result) (This)->lpVtbl->IWSManSession_Invoke(This,actionUri,resourceUri,parameters,flags,result)
#define IWSManSession_Enumerate(This,resourceUri,filter,dialect,flags,resultSet) (This)->lpVtbl->Enumerate(This,resourceUri,filter,dialect,flags,resultSet)
#define IWSManSession_Identify(This,flags,result) (This)->lpVtbl->Identify(This,flags,result)
#define IWSManSession_get_Error(This,value) (This)->lpVtbl->get_Error(This,value)
#define IWSManSession_get_BatchItems(This,value) (This)->lpVtbl->get_BatchItems(This,value)
#define IWSManSession_put_BatchItems(This,value) (This)->lpVtbl->put_BatchItems(This,value)
#define IWSManSession_get_Timeout(This,value) (This)->lpVtbl->get_Timeout(This,value)
#define IWSManSession_put_Timeout(This,value) (This)->lpVtbl->put_Timeout(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSManSession_QueryInterface(IWSManSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSManSession_AddRef(IWSManSession* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSManSession_Release(IWSManSession* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSManSession_GetTypeInfoCount(IWSManSession* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSManSession_GetTypeInfo(IWSManSession* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSManSession_GetIDsOfNames(IWSManSession* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
/*** IWSManSession methods ***/
static inline HRESULT IWSManSession_Get(IWSManSession* This,VARIANT resourceUri,LONG flags,BSTR *resource) {
    return This->lpVtbl->Get(This,resourceUri,flags,resource);
}
static inline HRESULT IWSManSession_Put(IWSManSession* This,VARIANT resourceUri,BSTR resource,LONG flags,BSTR *resultResource) {
    return This->lpVtbl->Put(This,resourceUri,resource,flags,resultResource);
}
static inline HRESULT IWSManSession_Create(IWSManSession* This,VARIANT resourceUri,BSTR resource,LONG flags,BSTR *newUri) {
    return This->lpVtbl->Create(This,resourceUri,resource,flags,newUri);
}
static inline HRESULT IWSManSession_Delete(IWSManSession* This,VARIANT resourceUri,LONG flags) {
    return This->lpVtbl->Delete(This,resourceUri,flags);
}
static inline HRESULT IWSManSession_Invoke(IWSManSession* This,BSTR actionUri,VARIANT resourceUri,BSTR parameters,LONG flags,BSTR *result) {
    return This->lpVtbl->IWSManSession_Invoke(This,actionUri,resourceUri,parameters,flags,result);
}
static inline HRESULT IWSManSession_Enumerate(IWSManSession* This,VARIANT resourceUri,BSTR filter,BSTR dialect,LONG flags,IDispatch **resultSet) {
    return This->lpVtbl->Enumerate(This,resourceUri,filter,dialect,flags,resultSet);
}
static inline HRESULT IWSManSession_Identify(IWSManSession* This,LONG flags,BSTR *result) {
    return This->lpVtbl->Identify(This,flags,result);
}
static inline HRESULT IWSManSession_get_Error(IWSManSession* This,BSTR *value) {
    return This->lpVtbl->get_Error(This,value);
}
static inline HRESULT IWSManSession_get_BatchItems(IWSManSession* This,LONG *value) {
    return This->lpVtbl->get_BatchItems(This,value);
}
static inline HRESULT IWSManSession_put_BatchItems(IWSManSession* This,LONG value) {
    return This->lpVtbl->put_BatchItems(This,value);
}
static inline HRESULT IWSManSession_get_Timeout(IWSManSession* This,LONG *value) {
    return This->lpVtbl->get_Timeout(This,value);
}
static inline HRESULT IWSManSession_put_Timeout(IWSManSession* This,LONG value) {
    return This->lpVtbl->put_Timeout(This,value);
}
#endif
#endif

#endif


#endif  /* __IWSManSession_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSManEnumerator interface
 */
#ifndef __IWSManEnumerator_INTERFACE_DEFINED__
#define __IWSManEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSManEnumerator, 0xf3457ca9, 0xabb9, 0x4fa5, 0xb8,0x50, 0x90,0xe8,0xca,0x30,0x0e,0x7f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f3457ca9-abb9-4fa5-b850-90e8ca300e7f")
IWSManEnumerator : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE ReadItem(
        BSTR *resource) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AtEndOfStream(
        VARIANT_BOOL *eos) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Error(
        BSTR *value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSManEnumerator, 0xf3457ca9, 0xabb9, 0x4fa5, 0xb8,0x50, 0x90,0xe8,0xca,0x30,0x0e,0x7f)
#endif
#else
typedef struct IWSManEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSManEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSManEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSManEnumerator *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSManEnumerator *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSManEnumerator *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSManEnumerator *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSManEnumerator *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSManEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *ReadItem)(
        IWSManEnumerator *This,
        BSTR *resource);

    HRESULT (STDMETHODCALLTYPE *get_AtEndOfStream)(
        IWSManEnumerator *This,
        VARIANT_BOOL *eos);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWSManEnumerator *This,
        BSTR *value);

    END_INTERFACE
} IWSManEnumeratorVtbl;

interface IWSManEnumerator {
    CONST_VTBL IWSManEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSManEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSManEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSManEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSManEnumerator_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSManEnumerator_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSManEnumerator_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSManEnumerator_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSManEnumerator methods ***/
#define IWSManEnumerator_ReadItem(This,resource) (This)->lpVtbl->ReadItem(This,resource)
#define IWSManEnumerator_get_AtEndOfStream(This,eos) (This)->lpVtbl->get_AtEndOfStream(This,eos)
#define IWSManEnumerator_get_Error(This,value) (This)->lpVtbl->get_Error(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSManEnumerator_QueryInterface(IWSManEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSManEnumerator_AddRef(IWSManEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSManEnumerator_Release(IWSManEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSManEnumerator_GetTypeInfoCount(IWSManEnumerator* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSManEnumerator_GetTypeInfo(IWSManEnumerator* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSManEnumerator_GetIDsOfNames(IWSManEnumerator* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSManEnumerator_Invoke(IWSManEnumerator* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSManEnumerator methods ***/
static inline HRESULT IWSManEnumerator_ReadItem(IWSManEnumerator* This,BSTR *resource) {
    return This->lpVtbl->ReadItem(This,resource);
}
static inline HRESULT IWSManEnumerator_get_AtEndOfStream(IWSManEnumerator* This,VARIANT_BOOL *eos) {
    return This->lpVtbl->get_AtEndOfStream(This,eos);
}
static inline HRESULT IWSManEnumerator_get_Error(IWSManEnumerator* This,BSTR *value) {
    return This->lpVtbl->get_Error(This,value);
}
#endif
#endif

#endif


#endif  /* __IWSManEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSManResourceLocator interface
 */
#ifndef __IWSManResourceLocator_INTERFACE_DEFINED__
#define __IWSManResourceLocator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSManResourceLocator, 0xa7a1ba28, 0xde41, 0x466a, 0xad,0x0a, 0xc4,0x05,0x9e,0xad,0x74,0x28);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a7a1ba28-de41-466a-ad0a-c4059ead7428")
IWSManResourceLocator : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE put_ResourceURI(
        BSTR uri) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ResourceURI(
        BSTR *uri) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddSelector(
        BSTR resourceSelName,
        VARIANT selValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearSelectors(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FragmentPath(
        BSTR *text) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FragmentPath(
        BSTR text) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FragmentDialect(
        BSTR *text) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_FragmentDialect(
        BSTR text) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddOption(
        BSTR OptionName,
        VARIANT OptionValue,
        WINBOOL mustComply = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_MustUnderstandOptions(
        WINBOOL mustUnderstand) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_MustUnderstandOptions(
        WINBOOL *mustUnderstand) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearOptions(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Error(
        BSTR *value) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSManResourceLocator, 0xa7a1ba28, 0xde41, 0x466a, 0xad,0x0a, 0xc4,0x05,0x9e,0xad,0x74,0x28)
#endif
#else
typedef struct IWSManResourceLocatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSManResourceLocator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSManResourceLocator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSManResourceLocator *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSManResourceLocator *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSManResourceLocator *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSManResourceLocator *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSManResourceLocator *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSManResourceLocator methods ***/
    HRESULT (STDMETHODCALLTYPE *put_ResourceURI)(
        IWSManResourceLocator *This,
        BSTR uri);

    HRESULT (STDMETHODCALLTYPE *get_ResourceURI)(
        IWSManResourceLocator *This,
        BSTR *uri);

    HRESULT (STDMETHODCALLTYPE *AddSelector)(
        IWSManResourceLocator *This,
        BSTR resourceSelName,
        VARIANT selValue);

    HRESULT (STDMETHODCALLTYPE *ClearSelectors)(
        IWSManResourceLocator *This);

    HRESULT (STDMETHODCALLTYPE *get_FragmentPath)(
        IWSManResourceLocator *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_FragmentPath)(
        IWSManResourceLocator *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *get_FragmentDialect)(
        IWSManResourceLocator *This,
        BSTR *text);

    HRESULT (STDMETHODCALLTYPE *put_FragmentDialect)(
        IWSManResourceLocator *This,
        BSTR text);

    HRESULT (STDMETHODCALLTYPE *AddOption)(
        IWSManResourceLocator *This,
        BSTR OptionName,
        VARIANT OptionValue,
        WINBOOL mustComply);

    HRESULT (STDMETHODCALLTYPE *put_MustUnderstandOptions)(
        IWSManResourceLocator *This,
        WINBOOL mustUnderstand);

    HRESULT (STDMETHODCALLTYPE *get_MustUnderstandOptions)(
        IWSManResourceLocator *This,
        WINBOOL *mustUnderstand);

    HRESULT (STDMETHODCALLTYPE *ClearOptions)(
        IWSManResourceLocator *This);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWSManResourceLocator *This,
        BSTR *value);

    END_INTERFACE
} IWSManResourceLocatorVtbl;

interface IWSManResourceLocator {
    CONST_VTBL IWSManResourceLocatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSManResourceLocator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSManResourceLocator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSManResourceLocator_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSManResourceLocator_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSManResourceLocator_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSManResourceLocator_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSManResourceLocator_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSManResourceLocator methods ***/
#define IWSManResourceLocator_put_ResourceURI(This,uri) (This)->lpVtbl->put_ResourceURI(This,uri)
#define IWSManResourceLocator_get_ResourceURI(This,uri) (This)->lpVtbl->get_ResourceURI(This,uri)
#define IWSManResourceLocator_AddSelector(This,resourceSelName,selValue) (This)->lpVtbl->AddSelector(This,resourceSelName,selValue)
#define IWSManResourceLocator_ClearSelectors(This) (This)->lpVtbl->ClearSelectors(This)
#define IWSManResourceLocator_get_FragmentPath(This,text) (This)->lpVtbl->get_FragmentPath(This,text)
#define IWSManResourceLocator_put_FragmentPath(This,text) (This)->lpVtbl->put_FragmentPath(This,text)
#define IWSManResourceLocator_get_FragmentDialect(This,text) (This)->lpVtbl->get_FragmentDialect(This,text)
#define IWSManResourceLocator_put_FragmentDialect(This,text) (This)->lpVtbl->put_FragmentDialect(This,text)
#define IWSManResourceLocator_AddOption(This,OptionName,OptionValue,mustComply) (This)->lpVtbl->AddOption(This,OptionName,OptionValue,mustComply)
#define IWSManResourceLocator_put_MustUnderstandOptions(This,mustUnderstand) (This)->lpVtbl->put_MustUnderstandOptions(This,mustUnderstand)
#define IWSManResourceLocator_get_MustUnderstandOptions(This,mustUnderstand) (This)->lpVtbl->get_MustUnderstandOptions(This,mustUnderstand)
#define IWSManResourceLocator_ClearOptions(This) (This)->lpVtbl->ClearOptions(This)
#define IWSManResourceLocator_get_Error(This,value) (This)->lpVtbl->get_Error(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSManResourceLocator_QueryInterface(IWSManResourceLocator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSManResourceLocator_AddRef(IWSManResourceLocator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSManResourceLocator_Release(IWSManResourceLocator* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSManResourceLocator_GetTypeInfoCount(IWSManResourceLocator* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSManResourceLocator_GetTypeInfo(IWSManResourceLocator* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSManResourceLocator_GetIDsOfNames(IWSManResourceLocator* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSManResourceLocator_Invoke(IWSManResourceLocator* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSManResourceLocator methods ***/
static inline HRESULT IWSManResourceLocator_put_ResourceURI(IWSManResourceLocator* This,BSTR uri) {
    return This->lpVtbl->put_ResourceURI(This,uri);
}
static inline HRESULT IWSManResourceLocator_get_ResourceURI(IWSManResourceLocator* This,BSTR *uri) {
    return This->lpVtbl->get_ResourceURI(This,uri);
}
static inline HRESULT IWSManResourceLocator_AddSelector(IWSManResourceLocator* This,BSTR resourceSelName,VARIANT selValue) {
    return This->lpVtbl->AddSelector(This,resourceSelName,selValue);
}
static inline HRESULT IWSManResourceLocator_ClearSelectors(IWSManResourceLocator* This) {
    return This->lpVtbl->ClearSelectors(This);
}
static inline HRESULT IWSManResourceLocator_get_FragmentPath(IWSManResourceLocator* This,BSTR *text) {
    return This->lpVtbl->get_FragmentPath(This,text);
}
static inline HRESULT IWSManResourceLocator_put_FragmentPath(IWSManResourceLocator* This,BSTR text) {
    return This->lpVtbl->put_FragmentPath(This,text);
}
static inline HRESULT IWSManResourceLocator_get_FragmentDialect(IWSManResourceLocator* This,BSTR *text) {
    return This->lpVtbl->get_FragmentDialect(This,text);
}
static inline HRESULT IWSManResourceLocator_put_FragmentDialect(IWSManResourceLocator* This,BSTR text) {
    return This->lpVtbl->put_FragmentDialect(This,text);
}
static inline HRESULT IWSManResourceLocator_AddOption(IWSManResourceLocator* This,BSTR OptionName,VARIANT OptionValue,WINBOOL mustComply) {
    return This->lpVtbl->AddOption(This,OptionName,OptionValue,mustComply);
}
static inline HRESULT IWSManResourceLocator_put_MustUnderstandOptions(IWSManResourceLocator* This,WINBOOL mustUnderstand) {
    return This->lpVtbl->put_MustUnderstandOptions(This,mustUnderstand);
}
static inline HRESULT IWSManResourceLocator_get_MustUnderstandOptions(IWSManResourceLocator* This,WINBOOL *mustUnderstand) {
    return This->lpVtbl->get_MustUnderstandOptions(This,mustUnderstand);
}
static inline HRESULT IWSManResourceLocator_ClearOptions(IWSManResourceLocator* This) {
    return This->lpVtbl->ClearOptions(This);
}
static inline HRESULT IWSManResourceLocator_get_Error(IWSManResourceLocator* This,BSTR *value) {
    return This->lpVtbl->get_Error(This,value);
}
#endif
#endif

#endif


#endif  /* __IWSManResourceLocator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSManResourceLocatorInternal interface
 */
#ifndef __IWSManResourceLocatorInternal_INTERFACE_DEFINED__
#define __IWSManResourceLocatorInternal_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSManResourceLocatorInternal, 0xeffaead7, 0x7ec8, 0x4716, 0xb9,0xbe, 0xf2,0xe7,0xe9,0xfb,0x4a,0xdb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("effaead7-7ec8-4716-b9be-f2e7e9fb4adb")
IWSManResourceLocatorInternal : public IUnknown
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSManResourceLocatorInternal, 0xeffaead7, 0x7ec8, 0x4716, 0xb9,0xbe, 0xf2,0xe7,0xe9,0xfb,0x4a,0xdb)
#endif
#else
typedef struct IWSManResourceLocatorInternalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSManResourceLocatorInternal *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSManResourceLocatorInternal *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSManResourceLocatorInternal *This);

    END_INTERFACE
} IWSManResourceLocatorInternalVtbl;

interface IWSManResourceLocatorInternal {
    CONST_VTBL IWSManResourceLocatorInternalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSManResourceLocatorInternal_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSManResourceLocatorInternal_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSManResourceLocatorInternal_Release(This) (This)->lpVtbl->Release(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSManResourceLocatorInternal_QueryInterface(IWSManResourceLocatorInternal* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSManResourceLocatorInternal_AddRef(IWSManResourceLocatorInternal* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSManResourceLocatorInternal_Release(IWSManResourceLocatorInternal* This) {
    return This->lpVtbl->Release(This);
}
#endif
#endif

#endif


#endif  /* __IWSManResourceLocatorInternal_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSManInternal interface
 */
#ifndef __IWSManInternal_INTERFACE_DEFINED__
#define __IWSManInternal_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSManInternal, 0x04ae2b1d, 0x9954, 0x4d99, 0x94,0xa9, 0xa9,0x61,0xe7,0x2c,0x3a,0x13);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("04ae2b1d-9954-4d99-94a9-a961e72c3a13")
IWSManInternal : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE ConfigSDDL(
        IDispatch *session,
        VARIANT resourceUri,
        LONG flags,
        BSTR *resource) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSManInternal, 0x04ae2b1d, 0x9954, 0x4d99, 0x94,0xa9, 0xa9,0x61,0xe7,0x2c,0x3a,0x13)
#endif
#else
typedef struct IWSManInternalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSManInternal *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSManInternal *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSManInternal *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSManInternal *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSManInternal *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSManInternal *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSManInternal *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSManInternal methods ***/
    HRESULT (STDMETHODCALLTYPE *ConfigSDDL)(
        IWSManInternal *This,
        IDispatch *session,
        VARIANT resourceUri,
        LONG flags,
        BSTR *resource);

    END_INTERFACE
} IWSManInternalVtbl;

interface IWSManInternal {
    CONST_VTBL IWSManInternalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSManInternal_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSManInternal_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSManInternal_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSManInternal_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSManInternal_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSManInternal_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSManInternal_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSManInternal methods ***/
#define IWSManInternal_ConfigSDDL(This,session,resourceUri,flags,resource) (This)->lpVtbl->ConfigSDDL(This,session,resourceUri,flags,resource)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSManInternal_QueryInterface(IWSManInternal* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSManInternal_AddRef(IWSManInternal* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSManInternal_Release(IWSManInternal* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSManInternal_GetTypeInfoCount(IWSManInternal* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSManInternal_GetTypeInfo(IWSManInternal* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSManInternal_GetIDsOfNames(IWSManInternal* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSManInternal_Invoke(IWSManInternal* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSManInternal methods ***/
static inline HRESULT IWSManInternal_ConfigSDDL(IWSManInternal* This,IDispatch *session,VARIANT resourceUri,LONG flags,BSTR *resource) {
    return This->lpVtbl->ConfigSDDL(This,session,resourceUri,flags,resource);
}
#endif
#endif

#endif


#endif  /* __IWSManInternal_INTERFACE_DEFINED__ */

#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wsmandisp_h__ */
