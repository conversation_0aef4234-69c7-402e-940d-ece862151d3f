/*** Autogenerated by WIDL 10.12 from include/wsddisco.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wsddisco_h__
#define __wsddisco_h__

/* Forward declarations */

#ifndef __IWSDScopeMatchingRule_FWD_DEFINED__
#define __IWSDScopeMatchingRule_FWD_DEFINED__
typedef interface IWSDScopeMatchingRule IWSDScopeMatchingRule;
#ifdef __cplusplus
interface IWSDScopeMatchingRule;
#endif /* __cplusplus */
#endif

#ifndef __IWSDiscoveryProviderNotify_FWD_DEFINED__
#define __IWSDiscoveryProviderNotify_FWD_DEFINED__
typedef interface IWSDiscoveryProviderNotify IWSDiscoveryProviderNotify;
#ifdef __cplusplus
interface IWSDiscoveryProviderNotify;
#endif /* __cplusplus */
#endif

#ifndef __IWSDiscoveryProvider_FWD_DEFINED__
#define __IWSDiscoveryProvider_FWD_DEFINED__
typedef interface IWSDiscoveryProvider IWSDiscoveryProvider;
#ifdef __cplusplus
interface IWSDiscoveryProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWSDiscoveredService_FWD_DEFINED__
#define __IWSDiscoveredService_FWD_DEFINED__
typedef interface IWSDiscoveredService IWSDiscoveredService;
#ifdef __cplusplus
interface IWSDiscoveredService;
#endif /* __cplusplus */
#endif

#ifndef __IWSDiscoveryPublisherNotify_FWD_DEFINED__
#define __IWSDiscoveryPublisherNotify_FWD_DEFINED__
typedef interface IWSDiscoveryPublisherNotify IWSDiscoveryPublisherNotify;
#ifdef __cplusplus
interface IWSDiscoveryPublisherNotify;
#endif /* __cplusplus */
#endif

#ifndef __IWSDiscoveryPublisher_FWD_DEFINED__
#define __IWSDiscoveryPublisher_FWD_DEFINED__
typedef interface IWSDiscoveryPublisher IWSDiscoveryPublisher;
#ifdef __cplusplus
interface IWSDiscoveryPublisher;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <objidl.h>
#include <wsdxmldom.h>
#include <wsdtypes.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

#ifndef __IWSDXMLContext_FWD_DEFINED__
#define __IWSDXMLContext_FWD_DEFINED__
typedef interface IWSDXMLContext IWSDXMLContext;
#ifdef __cplusplus
interface IWSDXMLContext;
#endif /* __cplusplus */
#endif

#ifndef __IWSDiscoveryProvider_FWD_DEFINED__
#define __IWSDiscoveryProvider_FWD_DEFINED__
typedef interface IWSDiscoveryProvider IWSDiscoveryProvider;
#ifdef __cplusplus
interface IWSDiscoveryProvider;
#endif /* __cplusplus */
#endif

#ifndef __IWSDiscoveryProviderNotify_FWD_DEFINED__
#define __IWSDiscoveryProviderNotify_FWD_DEFINED__
typedef interface IWSDiscoveryProviderNotify IWSDiscoveryProviderNotify;
#ifdef __cplusplus
interface IWSDiscoveryProviderNotify;
#endif /* __cplusplus */
#endif

#ifndef __IWSDiscoveredService_FWD_DEFINED__
#define __IWSDiscoveredService_FWD_DEFINED__
typedef interface IWSDiscoveredService IWSDiscoveredService;
#ifdef __cplusplus
interface IWSDiscoveredService;
#endif /* __cplusplus */
#endif

#ifndef __IWSDiscoveryPublisher_FWD_DEFINED__
#define __IWSDiscoveryPublisher_FWD_DEFINED__
typedef interface IWSDiscoveryPublisher IWSDiscoveryPublisher;
#ifdef __cplusplus
interface IWSDiscoveryPublisher;
#endif /* __cplusplus */
#endif

#ifndef __IWSDiscoveryPublisherNotify_FWD_DEFINED__
#define __IWSDiscoveryPublisherNotify_FWD_DEFINED__
typedef interface IWSDiscoveryPublisherNotify IWSDiscoveryPublisherNotify;
#ifdef __cplusplus
interface IWSDiscoveryPublisherNotify;
#endif /* __cplusplus */
#endif

#ifndef __IWSDScopeMatchingRule_FWD_DEFINED__
#define __IWSDScopeMatchingRule_FWD_DEFINED__
typedef interface IWSDScopeMatchingRule IWSDScopeMatchingRule;
#ifdef __cplusplus
interface IWSDScopeMatchingRule;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IWSDScopeMatchingRule interface
 */
#ifndef __IWSDScopeMatchingRule_INTERFACE_DEFINED__
#define __IWSDScopeMatchingRule_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDScopeMatchingRule, 0xfcafe424, 0xfef5, 0x481a, 0xbd,0x9f, 0x33,0xce,0x05,0x74,0x25,0x6f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fcafe424-fef5-481a-bd9f-33ce0574256f")
IWSDScopeMatchingRule : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetScopeRule(
        LPCWSTR *ppszScopeMatchingRule) = 0;

    virtual HRESULT STDMETHODCALLTYPE MatchScopes(
        LPCWSTR pszScope1,
        LPCWSTR pszScope2,
        WINBOOL *pfMatch) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDScopeMatchingRule, 0xfcafe424, 0xfef5, 0x481a, 0xbd,0x9f, 0x33,0xce,0x05,0x74,0x25,0x6f)
#endif
#else
typedef struct IWSDScopeMatchingRuleVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDScopeMatchingRule *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDScopeMatchingRule *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDScopeMatchingRule *This);

    /*** IWSDScopeMatchingRule methods ***/
    HRESULT (STDMETHODCALLTYPE *GetScopeRule)(
        IWSDScopeMatchingRule *This,
        LPCWSTR *ppszScopeMatchingRule);

    HRESULT (STDMETHODCALLTYPE *MatchScopes)(
        IWSDScopeMatchingRule *This,
        LPCWSTR pszScope1,
        LPCWSTR pszScope2,
        WINBOOL *pfMatch);

    END_INTERFACE
} IWSDScopeMatchingRuleVtbl;

interface IWSDScopeMatchingRule {
    CONST_VTBL IWSDScopeMatchingRuleVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDScopeMatchingRule_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDScopeMatchingRule_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDScopeMatchingRule_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDScopeMatchingRule methods ***/
#define IWSDScopeMatchingRule_GetScopeRule(This,ppszScopeMatchingRule) (This)->lpVtbl->GetScopeRule(This,ppszScopeMatchingRule)
#define IWSDScopeMatchingRule_MatchScopes(This,pszScope1,pszScope2,pfMatch) (This)->lpVtbl->MatchScopes(This,pszScope1,pszScope2,pfMatch)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDScopeMatchingRule_QueryInterface(IWSDScopeMatchingRule* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDScopeMatchingRule_AddRef(IWSDScopeMatchingRule* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDScopeMatchingRule_Release(IWSDScopeMatchingRule* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDScopeMatchingRule methods ***/
static inline HRESULT IWSDScopeMatchingRule_GetScopeRule(IWSDScopeMatchingRule* This,LPCWSTR *ppszScopeMatchingRule) {
    return This->lpVtbl->GetScopeRule(This,ppszScopeMatchingRule);
}
static inline HRESULT IWSDScopeMatchingRule_MatchScopes(IWSDScopeMatchingRule* This,LPCWSTR pszScope1,LPCWSTR pszScope2,WINBOOL *pfMatch) {
    return This->lpVtbl->MatchScopes(This,pszScope1,pszScope2,pfMatch);
}
#endif
#endif

#endif


#endif  /* __IWSDScopeMatchingRule_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDiscoveryProviderNotify interface
 */
#ifndef __IWSDiscoveryProviderNotify_INTERFACE_DEFINED__
#define __IWSDiscoveryProviderNotify_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDiscoveryProviderNotify, 0x73ee3ced, 0xb6e6, 0x4329, 0xa5,0x46, 0x3e,0x8a,0xd4,0x65,0x63,0xd2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("73ee3ced-b6e6-4329-a546-3e8ad46563d2")
IWSDiscoveryProviderNotify : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Add(
        IWSDiscoveredService *pService) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        IWSDiscoveredService *pService) = 0;

    virtual HRESULT STDMETHODCALLTYPE SearchFailed(
        HRESULT hr,
        LPCWSTR pszTag) = 0;

    virtual HRESULT STDMETHODCALLTYPE SearchComplete(
        LPCWSTR pszTag) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDiscoveryProviderNotify, 0x73ee3ced, 0xb6e6, 0x4329, 0xa5,0x46, 0x3e,0x8a,0xd4,0x65,0x63,0xd2)
#endif
#else
typedef struct IWSDiscoveryProviderNotifyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDiscoveryProviderNotify *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDiscoveryProviderNotify *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDiscoveryProviderNotify *This);

    /*** IWSDiscoveryProviderNotify methods ***/
    HRESULT (STDMETHODCALLTYPE *Add)(
        IWSDiscoveryProviderNotify *This,
        IWSDiscoveredService *pService);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        IWSDiscoveryProviderNotify *This,
        IWSDiscoveredService *pService);

    HRESULT (STDMETHODCALLTYPE *SearchFailed)(
        IWSDiscoveryProviderNotify *This,
        HRESULT hr,
        LPCWSTR pszTag);

    HRESULT (STDMETHODCALLTYPE *SearchComplete)(
        IWSDiscoveryProviderNotify *This,
        LPCWSTR pszTag);

    END_INTERFACE
} IWSDiscoveryProviderNotifyVtbl;

interface IWSDiscoveryProviderNotify {
    CONST_VTBL IWSDiscoveryProviderNotifyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDiscoveryProviderNotify_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDiscoveryProviderNotify_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDiscoveryProviderNotify_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDiscoveryProviderNotify methods ***/
#define IWSDiscoveryProviderNotify_Add(This,pService) (This)->lpVtbl->Add(This,pService)
#define IWSDiscoveryProviderNotify_Remove(This,pService) (This)->lpVtbl->Remove(This,pService)
#define IWSDiscoveryProviderNotify_SearchFailed(This,hr,pszTag) (This)->lpVtbl->SearchFailed(This,hr,pszTag)
#define IWSDiscoveryProviderNotify_SearchComplete(This,pszTag) (This)->lpVtbl->SearchComplete(This,pszTag)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDiscoveryProviderNotify_QueryInterface(IWSDiscoveryProviderNotify* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDiscoveryProviderNotify_AddRef(IWSDiscoveryProviderNotify* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDiscoveryProviderNotify_Release(IWSDiscoveryProviderNotify* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDiscoveryProviderNotify methods ***/
static inline HRESULT IWSDiscoveryProviderNotify_Add(IWSDiscoveryProviderNotify* This,IWSDiscoveredService *pService) {
    return This->lpVtbl->Add(This,pService);
}
static inline HRESULT IWSDiscoveryProviderNotify_Remove(IWSDiscoveryProviderNotify* This,IWSDiscoveredService *pService) {
    return This->lpVtbl->Remove(This,pService);
}
static inline HRESULT IWSDiscoveryProviderNotify_SearchFailed(IWSDiscoveryProviderNotify* This,HRESULT hr,LPCWSTR pszTag) {
    return This->lpVtbl->SearchFailed(This,hr,pszTag);
}
static inline HRESULT IWSDiscoveryProviderNotify_SearchComplete(IWSDiscoveryProviderNotify* This,LPCWSTR pszTag) {
    return This->lpVtbl->SearchComplete(This,pszTag);
}
#endif
#endif

#endif


#endif  /* __IWSDiscoveryProviderNotify_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDiscoveryProvider interface
 */
#ifndef __IWSDiscoveryProvider_INTERFACE_DEFINED__
#define __IWSDiscoveryProvider_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDiscoveryProvider, 0x8ffc8e55, 0xf0eb, 0x480f, 0x88,0xb7, 0xb4,0x35,0xdd,0x28,0x1d,0x45);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8ffc8e55-f0eb-480f-88b7-b435dd281d45")
IWSDiscoveryProvider : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetAddressFamily(
        DWORD dwAddressFamily) = 0;

    virtual HRESULT STDMETHODCALLTYPE Attach(
        IWSDiscoveryProviderNotify *pSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE Detach(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SearchById(
        LPCWSTR pszId,
        LPCWSTR pszTag) = 0;

    virtual HRESULT STDMETHODCALLTYPE SearchByAddress(
        LPCWSTR pszAddress,
        LPCWSTR pszTag) = 0;

    virtual HRESULT STDMETHODCALLTYPE SearchByType(
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        LPCWSTR pszMatchBy,
        LPCWSTR pszTag) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetXMLContext(
        IWSDXMLContext **ppContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDiscoveryProvider, 0x8ffc8e55, 0xf0eb, 0x480f, 0x88,0xb7, 0xb4,0x35,0xdd,0x28,0x1d,0x45)
#endif
#else
typedef struct IWSDiscoveryProviderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDiscoveryProvider *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDiscoveryProvider *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDiscoveryProvider *This);

    /*** IWSDiscoveryProvider methods ***/
    HRESULT (STDMETHODCALLTYPE *SetAddressFamily)(
        IWSDiscoveryProvider *This,
        DWORD dwAddressFamily);

    HRESULT (STDMETHODCALLTYPE *Attach)(
        IWSDiscoveryProvider *This,
        IWSDiscoveryProviderNotify *pSink);

    HRESULT (STDMETHODCALLTYPE *Detach)(
        IWSDiscoveryProvider *This);

    HRESULT (STDMETHODCALLTYPE *SearchById)(
        IWSDiscoveryProvider *This,
        LPCWSTR pszId,
        LPCWSTR pszTag);

    HRESULT (STDMETHODCALLTYPE *SearchByAddress)(
        IWSDiscoveryProvider *This,
        LPCWSTR pszAddress,
        LPCWSTR pszTag);

    HRESULT (STDMETHODCALLTYPE *SearchByType)(
        IWSDiscoveryProvider *This,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        LPCWSTR pszMatchBy,
        LPCWSTR pszTag);

    HRESULT (STDMETHODCALLTYPE *GetXMLContext)(
        IWSDiscoveryProvider *This,
        IWSDXMLContext **ppContext);

    END_INTERFACE
} IWSDiscoveryProviderVtbl;

interface IWSDiscoveryProvider {
    CONST_VTBL IWSDiscoveryProviderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDiscoveryProvider_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDiscoveryProvider_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDiscoveryProvider_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDiscoveryProvider methods ***/
#define IWSDiscoveryProvider_SetAddressFamily(This,dwAddressFamily) (This)->lpVtbl->SetAddressFamily(This,dwAddressFamily)
#define IWSDiscoveryProvider_Attach(This,pSink) (This)->lpVtbl->Attach(This,pSink)
#define IWSDiscoveryProvider_Detach(This) (This)->lpVtbl->Detach(This)
#define IWSDiscoveryProvider_SearchById(This,pszId,pszTag) (This)->lpVtbl->SearchById(This,pszId,pszTag)
#define IWSDiscoveryProvider_SearchByAddress(This,pszAddress,pszTag) (This)->lpVtbl->SearchByAddress(This,pszAddress,pszTag)
#define IWSDiscoveryProvider_SearchByType(This,pTypesList,pScopesList,pszMatchBy,pszTag) (This)->lpVtbl->SearchByType(This,pTypesList,pScopesList,pszMatchBy,pszTag)
#define IWSDiscoveryProvider_GetXMLContext(This,ppContext) (This)->lpVtbl->GetXMLContext(This,ppContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDiscoveryProvider_QueryInterface(IWSDiscoveryProvider* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDiscoveryProvider_AddRef(IWSDiscoveryProvider* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDiscoveryProvider_Release(IWSDiscoveryProvider* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDiscoveryProvider methods ***/
static inline HRESULT IWSDiscoveryProvider_SetAddressFamily(IWSDiscoveryProvider* This,DWORD dwAddressFamily) {
    return This->lpVtbl->SetAddressFamily(This,dwAddressFamily);
}
static inline HRESULT IWSDiscoveryProvider_Attach(IWSDiscoveryProvider* This,IWSDiscoveryProviderNotify *pSink) {
    return This->lpVtbl->Attach(This,pSink);
}
static inline HRESULT IWSDiscoveryProvider_Detach(IWSDiscoveryProvider* This) {
    return This->lpVtbl->Detach(This);
}
static inline HRESULT IWSDiscoveryProvider_SearchById(IWSDiscoveryProvider* This,LPCWSTR pszId,LPCWSTR pszTag) {
    return This->lpVtbl->SearchById(This,pszId,pszTag);
}
static inline HRESULT IWSDiscoveryProvider_SearchByAddress(IWSDiscoveryProvider* This,LPCWSTR pszAddress,LPCWSTR pszTag) {
    return This->lpVtbl->SearchByAddress(This,pszAddress,pszTag);
}
static inline HRESULT IWSDiscoveryProvider_SearchByType(IWSDiscoveryProvider* This,const WSD_NAME_LIST *pTypesList,const WSD_URI_LIST *pScopesList,LPCWSTR pszMatchBy,LPCWSTR pszTag) {
    return This->lpVtbl->SearchByType(This,pTypesList,pScopesList,pszMatchBy,pszTag);
}
static inline HRESULT IWSDiscoveryProvider_GetXMLContext(IWSDiscoveryProvider* This,IWSDXMLContext **ppContext) {
    return This->lpVtbl->GetXMLContext(This,ppContext);
}
#endif
#endif

#endif


#endif  /* __IWSDiscoveryProvider_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDiscoveredService interface
 */
#ifndef __IWSDiscoveredService_INTERFACE_DEFINED__
#define __IWSDiscoveredService_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDiscoveredService, 0x4bad8a3b, 0xb374, 0x4420, 0x96,0x32, 0xaa,0xc9,0x45,0xb3,0x74,0xaa);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4bad8a3b-b374-4420-9632-aac945b374aa")
IWSDiscoveredService : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetEndpointReference(
        WSD_ENDPOINT_REFERENCE **ppEndpointReference) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypes(
        WSD_NAME_LIST **ppTypesList) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScopes(
        WSD_URI_LIST **ppScopesList) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetXAddrs(
        WSD_URI_LIST **ppXAddrsList) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMetadataVersion(
        ULONGLONG *pullMetadataVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExtendedDiscoXML(
        WSDXML_ELEMENT **ppHeaderAny,
        WSDXML_ELEMENT **ppBodyAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProbeResolveTag(
        LPCWSTR *ppszTag) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRemoteTransportAddress(
        LPCWSTR *ppszRemoteTransportAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocalTransportAddress(
        LPCWSTR *ppszLocalTransportAddress) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocalInterfaceGUID(
        GUID *pGuid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInstanceId(
        ULONGLONG *pullInstanceId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDiscoveredService, 0x4bad8a3b, 0xb374, 0x4420, 0x96,0x32, 0xaa,0xc9,0x45,0xb3,0x74,0xaa)
#endif
#else
typedef struct IWSDiscoveredServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDiscoveredService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDiscoveredService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDiscoveredService *This);

    /*** IWSDiscoveredService methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEndpointReference)(
        IWSDiscoveredService *This,
        WSD_ENDPOINT_REFERENCE **ppEndpointReference);

    HRESULT (STDMETHODCALLTYPE *GetTypes)(
        IWSDiscoveredService *This,
        WSD_NAME_LIST **ppTypesList);

    HRESULT (STDMETHODCALLTYPE *GetScopes)(
        IWSDiscoveredService *This,
        WSD_URI_LIST **ppScopesList);

    HRESULT (STDMETHODCALLTYPE *GetXAddrs)(
        IWSDiscoveredService *This,
        WSD_URI_LIST **ppXAddrsList);

    HRESULT (STDMETHODCALLTYPE *GetMetadataVersion)(
        IWSDiscoveredService *This,
        ULONGLONG *pullMetadataVersion);

    HRESULT (STDMETHODCALLTYPE *GetExtendedDiscoXML)(
        IWSDiscoveredService *This,
        WSDXML_ELEMENT **ppHeaderAny,
        WSDXML_ELEMENT **ppBodyAny);

    HRESULT (STDMETHODCALLTYPE *GetProbeResolveTag)(
        IWSDiscoveredService *This,
        LPCWSTR *ppszTag);

    HRESULT (STDMETHODCALLTYPE *GetRemoteTransportAddress)(
        IWSDiscoveredService *This,
        LPCWSTR *ppszRemoteTransportAddress);

    HRESULT (STDMETHODCALLTYPE *GetLocalTransportAddress)(
        IWSDiscoveredService *This,
        LPCWSTR *ppszLocalTransportAddress);

    HRESULT (STDMETHODCALLTYPE *GetLocalInterfaceGUID)(
        IWSDiscoveredService *This,
        GUID *pGuid);

    HRESULT (STDMETHODCALLTYPE *GetInstanceId)(
        IWSDiscoveredService *This,
        ULONGLONG *pullInstanceId);

    END_INTERFACE
} IWSDiscoveredServiceVtbl;

interface IWSDiscoveredService {
    CONST_VTBL IWSDiscoveredServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDiscoveredService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDiscoveredService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDiscoveredService_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDiscoveredService methods ***/
#define IWSDiscoveredService_GetEndpointReference(This,ppEndpointReference) (This)->lpVtbl->GetEndpointReference(This,ppEndpointReference)
#define IWSDiscoveredService_GetTypes(This,ppTypesList) (This)->lpVtbl->GetTypes(This,ppTypesList)
#define IWSDiscoveredService_GetScopes(This,ppScopesList) (This)->lpVtbl->GetScopes(This,ppScopesList)
#define IWSDiscoveredService_GetXAddrs(This,ppXAddrsList) (This)->lpVtbl->GetXAddrs(This,ppXAddrsList)
#define IWSDiscoveredService_GetMetadataVersion(This,pullMetadataVersion) (This)->lpVtbl->GetMetadataVersion(This,pullMetadataVersion)
#define IWSDiscoveredService_GetExtendedDiscoXML(This,ppHeaderAny,ppBodyAny) (This)->lpVtbl->GetExtendedDiscoXML(This,ppHeaderAny,ppBodyAny)
#define IWSDiscoveredService_GetProbeResolveTag(This,ppszTag) (This)->lpVtbl->GetProbeResolveTag(This,ppszTag)
#define IWSDiscoveredService_GetRemoteTransportAddress(This,ppszRemoteTransportAddress) (This)->lpVtbl->GetRemoteTransportAddress(This,ppszRemoteTransportAddress)
#define IWSDiscoveredService_GetLocalTransportAddress(This,ppszLocalTransportAddress) (This)->lpVtbl->GetLocalTransportAddress(This,ppszLocalTransportAddress)
#define IWSDiscoveredService_GetLocalInterfaceGUID(This,pGuid) (This)->lpVtbl->GetLocalInterfaceGUID(This,pGuid)
#define IWSDiscoveredService_GetInstanceId(This,pullInstanceId) (This)->lpVtbl->GetInstanceId(This,pullInstanceId)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDiscoveredService_QueryInterface(IWSDiscoveredService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDiscoveredService_AddRef(IWSDiscoveredService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDiscoveredService_Release(IWSDiscoveredService* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDiscoveredService methods ***/
static inline HRESULT IWSDiscoveredService_GetEndpointReference(IWSDiscoveredService* This,WSD_ENDPOINT_REFERENCE **ppEndpointReference) {
    return This->lpVtbl->GetEndpointReference(This,ppEndpointReference);
}
static inline HRESULT IWSDiscoveredService_GetTypes(IWSDiscoveredService* This,WSD_NAME_LIST **ppTypesList) {
    return This->lpVtbl->GetTypes(This,ppTypesList);
}
static inline HRESULT IWSDiscoveredService_GetScopes(IWSDiscoveredService* This,WSD_URI_LIST **ppScopesList) {
    return This->lpVtbl->GetScopes(This,ppScopesList);
}
static inline HRESULT IWSDiscoveredService_GetXAddrs(IWSDiscoveredService* This,WSD_URI_LIST **ppXAddrsList) {
    return This->lpVtbl->GetXAddrs(This,ppXAddrsList);
}
static inline HRESULT IWSDiscoveredService_GetMetadataVersion(IWSDiscoveredService* This,ULONGLONG *pullMetadataVersion) {
    return This->lpVtbl->GetMetadataVersion(This,pullMetadataVersion);
}
static inline HRESULT IWSDiscoveredService_GetExtendedDiscoXML(IWSDiscoveredService* This,WSDXML_ELEMENT **ppHeaderAny,WSDXML_ELEMENT **ppBodyAny) {
    return This->lpVtbl->GetExtendedDiscoXML(This,ppHeaderAny,ppBodyAny);
}
static inline HRESULT IWSDiscoveredService_GetProbeResolveTag(IWSDiscoveredService* This,LPCWSTR *ppszTag) {
    return This->lpVtbl->GetProbeResolveTag(This,ppszTag);
}
static inline HRESULT IWSDiscoveredService_GetRemoteTransportAddress(IWSDiscoveredService* This,LPCWSTR *ppszRemoteTransportAddress) {
    return This->lpVtbl->GetRemoteTransportAddress(This,ppszRemoteTransportAddress);
}
static inline HRESULT IWSDiscoveredService_GetLocalTransportAddress(IWSDiscoveredService* This,LPCWSTR *ppszLocalTransportAddress) {
    return This->lpVtbl->GetLocalTransportAddress(This,ppszLocalTransportAddress);
}
static inline HRESULT IWSDiscoveredService_GetLocalInterfaceGUID(IWSDiscoveredService* This,GUID *pGuid) {
    return This->lpVtbl->GetLocalInterfaceGUID(This,pGuid);
}
static inline HRESULT IWSDiscoveredService_GetInstanceId(IWSDiscoveredService* This,ULONGLONG *pullInstanceId) {
    return This->lpVtbl->GetInstanceId(This,pullInstanceId);
}
#endif
#endif

#endif


#endif  /* __IWSDiscoveredService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDiscoveryPublisherNotify interface
 */
#ifndef __IWSDiscoveryPublisherNotify_INTERFACE_DEFINED__
#define __IWSDiscoveryPublisherNotify_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDiscoveryPublisherNotify, 0xe67651b0, 0x337a, 0x4b3c, 0x97,0x58, 0x73,0x33,0x88,0x56,0x82,0x51);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e67651b0-337a-4b3c-9758-************")
IWSDiscoveryPublisherNotify : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE ProbeHandler(
        const WSD_SOAP_MESSAGE *pSoap,
        IWSDMessageParameters *pMessageParameters) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResolveHandler(
        const WSD_SOAP_MESSAGE *pSoap,
        IWSDMessageParameters *pMessageParameters) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDiscoveryPublisherNotify, 0xe67651b0, 0x337a, 0x4b3c, 0x97,0x58, 0x73,0x33,0x88,0x56,0x82,0x51)
#endif
#else
typedef struct IWSDiscoveryPublisherNotifyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDiscoveryPublisherNotify *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDiscoveryPublisherNotify *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDiscoveryPublisherNotify *This);

    /*** IWSDiscoveryPublisherNotify methods ***/
    HRESULT (STDMETHODCALLTYPE *ProbeHandler)(
        IWSDiscoveryPublisherNotify *This,
        const WSD_SOAP_MESSAGE *pSoap,
        IWSDMessageParameters *pMessageParameters);

    HRESULT (STDMETHODCALLTYPE *ResolveHandler)(
        IWSDiscoveryPublisherNotify *This,
        const WSD_SOAP_MESSAGE *pSoap,
        IWSDMessageParameters *pMessageParameters);

    END_INTERFACE
} IWSDiscoveryPublisherNotifyVtbl;

interface IWSDiscoveryPublisherNotify {
    CONST_VTBL IWSDiscoveryPublisherNotifyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDiscoveryPublisherNotify_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDiscoveryPublisherNotify_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDiscoveryPublisherNotify_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDiscoveryPublisherNotify methods ***/
#define IWSDiscoveryPublisherNotify_ProbeHandler(This,pSoap,pMessageParameters) (This)->lpVtbl->ProbeHandler(This,pSoap,pMessageParameters)
#define IWSDiscoveryPublisherNotify_ResolveHandler(This,pSoap,pMessageParameters) (This)->lpVtbl->ResolveHandler(This,pSoap,pMessageParameters)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDiscoveryPublisherNotify_QueryInterface(IWSDiscoveryPublisherNotify* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDiscoveryPublisherNotify_AddRef(IWSDiscoveryPublisherNotify* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDiscoveryPublisherNotify_Release(IWSDiscoveryPublisherNotify* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDiscoveryPublisherNotify methods ***/
static inline HRESULT IWSDiscoveryPublisherNotify_ProbeHandler(IWSDiscoveryPublisherNotify* This,const WSD_SOAP_MESSAGE *pSoap,IWSDMessageParameters *pMessageParameters) {
    return This->lpVtbl->ProbeHandler(This,pSoap,pMessageParameters);
}
static inline HRESULT IWSDiscoveryPublisherNotify_ResolveHandler(IWSDiscoveryPublisherNotify* This,const WSD_SOAP_MESSAGE *pSoap,IWSDMessageParameters *pMessageParameters) {
    return This->lpVtbl->ResolveHandler(This,pSoap,pMessageParameters);
}
#endif
#endif

#endif


#endif  /* __IWSDiscoveryPublisherNotify_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDiscoveryPublisher interface
 */
#ifndef __IWSDiscoveryPublisher_INTERFACE_DEFINED__
#define __IWSDiscoveryPublisher_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDiscoveryPublisher, 0xae01e1a8, 0x3ff9, 0x4148, 0x81,0x16, 0x05,0x7c,0xc6,0x16,0xfe,0x13);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ae01e1a8-3ff9-4148-8116-057cc616fe13")
IWSDiscoveryPublisher : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetAddressFamily(
        DWORD dwAddressFamily) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterNotificationSink(
        IWSDiscoveryPublisherNotify *pSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnRegisterNotificationSink(
        IWSDiscoveryPublisherNotify *pSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE Publish(
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnPublish(
        LPCWSTR pszId,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSDXML_ELEMENT *pAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE MatchProbe(
        const WSD_SOAP_MESSAGE *pProbeMessage,
        IWSDMessageParameters *pMessageParameters,
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList) = 0;

    virtual HRESULT STDMETHODCALLTYPE MatchResolve(
        const WSD_SOAP_MESSAGE *pResolveMessage,
        IWSDMessageParameters *pMessageParameters,
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList) = 0;

    virtual HRESULT STDMETHODCALLTYPE PublishEx(
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList,
        const WSDXML_ELEMENT *pHeaderAny,
        const WSDXML_ELEMENT *pReferenceParameterAny,
        const WSDXML_ELEMENT *pPolicyAny,
        const WSDXML_ELEMENT *pEndpointReferenceAny,
        const WSDXML_ELEMENT *pAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE MatchProbeEx(
        const WSD_SOAP_MESSAGE *pProbeMessage,
        IWSDMessageParameters *pMessageParameters,
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList,
        const WSDXML_ELEMENT *pHeaderAny,
        const WSDXML_ELEMENT *pReferenceParameterAny,
        const WSDXML_ELEMENT *pPolicyAny,
        const WSDXML_ELEMENT *pEndpointReferenceAny,
        const WSDXML_ELEMENT *pAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE MatchResolveEx(
        const WSD_SOAP_MESSAGE *pResolveMessage,
        IWSDMessageParameters *pMessageParameters,
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList,
        const WSDXML_ELEMENT *pHeaderAny,
        const WSDXML_ELEMENT *pReferenceParameterAny,
        const WSDXML_ELEMENT *pPolicyAny,
        const WSDXML_ELEMENT *pEndpointReferenceAny,
        const WSDXML_ELEMENT *pAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterScopeMatchingRule(
        IWSDScopeMatchingRule *pScopeMatchingRule) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnRegisterScopeMatchingRule(
        IWSDScopeMatchingRule *pScopeMatchingRule) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetXMLContext(
        IWSDXMLContext **ppContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDiscoveryPublisher, 0xae01e1a8, 0x3ff9, 0x4148, 0x81,0x16, 0x05,0x7c,0xc6,0x16,0xfe,0x13)
#endif
#else
typedef struct IWSDiscoveryPublisherVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDiscoveryPublisher *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDiscoveryPublisher *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDiscoveryPublisher *This);

    /*** IWSDiscoveryPublisher methods ***/
    HRESULT (STDMETHODCALLTYPE *SetAddressFamily)(
        IWSDiscoveryPublisher *This,
        DWORD dwAddressFamily);

    HRESULT (STDMETHODCALLTYPE *RegisterNotificationSink)(
        IWSDiscoveryPublisher *This,
        IWSDiscoveryPublisherNotify *pSink);

    HRESULT (STDMETHODCALLTYPE *UnRegisterNotificationSink)(
        IWSDiscoveryPublisher *This,
        IWSDiscoveryPublisherNotify *pSink);

    HRESULT (STDMETHODCALLTYPE *Publish)(
        IWSDiscoveryPublisher *This,
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList);

    HRESULT (STDMETHODCALLTYPE *UnPublish)(
        IWSDiscoveryPublisher *This,
        LPCWSTR pszId,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSDXML_ELEMENT *pAny);

    HRESULT (STDMETHODCALLTYPE *MatchProbe)(
        IWSDiscoveryPublisher *This,
        const WSD_SOAP_MESSAGE *pProbeMessage,
        IWSDMessageParameters *pMessageParameters,
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList);

    HRESULT (STDMETHODCALLTYPE *MatchResolve)(
        IWSDiscoveryPublisher *This,
        const WSD_SOAP_MESSAGE *pResolveMessage,
        IWSDMessageParameters *pMessageParameters,
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList);

    HRESULT (STDMETHODCALLTYPE *PublishEx)(
        IWSDiscoveryPublisher *This,
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList,
        const WSDXML_ELEMENT *pHeaderAny,
        const WSDXML_ELEMENT *pReferenceParameterAny,
        const WSDXML_ELEMENT *pPolicyAny,
        const WSDXML_ELEMENT *pEndpointReferenceAny,
        const WSDXML_ELEMENT *pAny);

    HRESULT (STDMETHODCALLTYPE *MatchProbeEx)(
        IWSDiscoveryPublisher *This,
        const WSD_SOAP_MESSAGE *pProbeMessage,
        IWSDMessageParameters *pMessageParameters,
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList,
        const WSDXML_ELEMENT *pHeaderAny,
        const WSDXML_ELEMENT *pReferenceParameterAny,
        const WSDXML_ELEMENT *pPolicyAny,
        const WSDXML_ELEMENT *pEndpointReferenceAny,
        const WSDXML_ELEMENT *pAny);

    HRESULT (STDMETHODCALLTYPE *MatchResolveEx)(
        IWSDiscoveryPublisher *This,
        const WSD_SOAP_MESSAGE *pResolveMessage,
        IWSDMessageParameters *pMessageParameters,
        LPCWSTR pszId,
        ULONGLONG ullMetadataVersion,
        ULONGLONG ullInstanceId,
        ULONGLONG ullMessageNumber,
        LPCWSTR pszSessionId,
        const WSD_NAME_LIST *pTypesList,
        const WSD_URI_LIST *pScopesList,
        const WSD_URI_LIST *pXAddrsList,
        const WSDXML_ELEMENT *pHeaderAny,
        const WSDXML_ELEMENT *pReferenceParameterAny,
        const WSDXML_ELEMENT *pPolicyAny,
        const WSDXML_ELEMENT *pEndpointReferenceAny,
        const WSDXML_ELEMENT *pAny);

    HRESULT (STDMETHODCALLTYPE *RegisterScopeMatchingRule)(
        IWSDiscoveryPublisher *This,
        IWSDScopeMatchingRule *pScopeMatchingRule);

    HRESULT (STDMETHODCALLTYPE *UnRegisterScopeMatchingRule)(
        IWSDiscoveryPublisher *This,
        IWSDScopeMatchingRule *pScopeMatchingRule);

    HRESULT (STDMETHODCALLTYPE *GetXMLContext)(
        IWSDiscoveryPublisher *This,
        IWSDXMLContext **ppContext);

    END_INTERFACE
} IWSDiscoveryPublisherVtbl;

interface IWSDiscoveryPublisher {
    CONST_VTBL IWSDiscoveryPublisherVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDiscoveryPublisher_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDiscoveryPublisher_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDiscoveryPublisher_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDiscoveryPublisher methods ***/
#define IWSDiscoveryPublisher_SetAddressFamily(This,dwAddressFamily) (This)->lpVtbl->SetAddressFamily(This,dwAddressFamily)
#define IWSDiscoveryPublisher_RegisterNotificationSink(This,pSink) (This)->lpVtbl->RegisterNotificationSink(This,pSink)
#define IWSDiscoveryPublisher_UnRegisterNotificationSink(This,pSink) (This)->lpVtbl->UnRegisterNotificationSink(This,pSink)
#define IWSDiscoveryPublisher_Publish(This,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList) (This)->lpVtbl->Publish(This,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList)
#define IWSDiscoveryPublisher_UnPublish(This,pszId,ullInstanceId,ullMessageNumber,pszSessionId,pAny) (This)->lpVtbl->UnPublish(This,pszId,ullInstanceId,ullMessageNumber,pszSessionId,pAny)
#define IWSDiscoveryPublisher_MatchProbe(This,pProbeMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList) (This)->lpVtbl->MatchProbe(This,pProbeMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList)
#define IWSDiscoveryPublisher_MatchResolve(This,pResolveMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList) (This)->lpVtbl->MatchResolve(This,pResolveMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList)
#define IWSDiscoveryPublisher_PublishEx(This,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList,pHeaderAny,pReferenceParameterAny,pPolicyAny,pEndpointReferenceAny,pAny) (This)->lpVtbl->PublishEx(This,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList,pHeaderAny,pReferenceParameterAny,pPolicyAny,pEndpointReferenceAny,pAny)
#define IWSDiscoveryPublisher_MatchProbeEx(This,pProbeMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList,pHeaderAny,pReferenceParameterAny,pPolicyAny,pEndpointReferenceAny,pAny) (This)->lpVtbl->MatchProbeEx(This,pProbeMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList,pHeaderAny,pReferenceParameterAny,pPolicyAny,pEndpointReferenceAny,pAny)
#define IWSDiscoveryPublisher_MatchResolveEx(This,pResolveMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList,pHeaderAny,pReferenceParameterAny,pPolicyAny,pEndpointReferenceAny,pAny) (This)->lpVtbl->MatchResolveEx(This,pResolveMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList,pHeaderAny,pReferenceParameterAny,pPolicyAny,pEndpointReferenceAny,pAny)
#define IWSDiscoveryPublisher_RegisterScopeMatchingRule(This,pScopeMatchingRule) (This)->lpVtbl->RegisterScopeMatchingRule(This,pScopeMatchingRule)
#define IWSDiscoveryPublisher_UnRegisterScopeMatchingRule(This,pScopeMatchingRule) (This)->lpVtbl->UnRegisterScopeMatchingRule(This,pScopeMatchingRule)
#define IWSDiscoveryPublisher_GetXMLContext(This,ppContext) (This)->lpVtbl->GetXMLContext(This,ppContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDiscoveryPublisher_QueryInterface(IWSDiscoveryPublisher* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDiscoveryPublisher_AddRef(IWSDiscoveryPublisher* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDiscoveryPublisher_Release(IWSDiscoveryPublisher* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDiscoveryPublisher methods ***/
static inline HRESULT IWSDiscoveryPublisher_SetAddressFamily(IWSDiscoveryPublisher* This,DWORD dwAddressFamily) {
    return This->lpVtbl->SetAddressFamily(This,dwAddressFamily);
}
static inline HRESULT IWSDiscoveryPublisher_RegisterNotificationSink(IWSDiscoveryPublisher* This,IWSDiscoveryPublisherNotify *pSink) {
    return This->lpVtbl->RegisterNotificationSink(This,pSink);
}
static inline HRESULT IWSDiscoveryPublisher_UnRegisterNotificationSink(IWSDiscoveryPublisher* This,IWSDiscoveryPublisherNotify *pSink) {
    return This->lpVtbl->UnRegisterNotificationSink(This,pSink);
}
static inline HRESULT IWSDiscoveryPublisher_Publish(IWSDiscoveryPublisher* This,LPCWSTR pszId,ULONGLONG ullMetadataVersion,ULONGLONG ullInstanceId,ULONGLONG ullMessageNumber,LPCWSTR pszSessionId,const WSD_NAME_LIST *pTypesList,const WSD_URI_LIST *pScopesList,const WSD_URI_LIST *pXAddrsList) {
    return This->lpVtbl->Publish(This,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList);
}
static inline HRESULT IWSDiscoveryPublisher_UnPublish(IWSDiscoveryPublisher* This,LPCWSTR pszId,ULONGLONG ullInstanceId,ULONGLONG ullMessageNumber,LPCWSTR pszSessionId,const WSDXML_ELEMENT *pAny) {
    return This->lpVtbl->UnPublish(This,pszId,ullInstanceId,ullMessageNumber,pszSessionId,pAny);
}
static inline HRESULT IWSDiscoveryPublisher_MatchProbe(IWSDiscoveryPublisher* This,const WSD_SOAP_MESSAGE *pProbeMessage,IWSDMessageParameters *pMessageParameters,LPCWSTR pszId,ULONGLONG ullMetadataVersion,ULONGLONG ullInstanceId,ULONGLONG ullMessageNumber,LPCWSTR pszSessionId,const WSD_NAME_LIST *pTypesList,const WSD_URI_LIST *pScopesList,const WSD_URI_LIST *pXAddrsList) {
    return This->lpVtbl->MatchProbe(This,pProbeMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList);
}
static inline HRESULT IWSDiscoveryPublisher_MatchResolve(IWSDiscoveryPublisher* This,const WSD_SOAP_MESSAGE *pResolveMessage,IWSDMessageParameters *pMessageParameters,LPCWSTR pszId,ULONGLONG ullMetadataVersion,ULONGLONG ullInstanceId,ULONGLONG ullMessageNumber,LPCWSTR pszSessionId,const WSD_NAME_LIST *pTypesList,const WSD_URI_LIST *pScopesList,const WSD_URI_LIST *pXAddrsList) {
    return This->lpVtbl->MatchResolve(This,pResolveMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList);
}
static inline HRESULT IWSDiscoveryPublisher_PublishEx(IWSDiscoveryPublisher* This,LPCWSTR pszId,ULONGLONG ullMetadataVersion,ULONGLONG ullInstanceId,ULONGLONG ullMessageNumber,LPCWSTR pszSessionId,const WSD_NAME_LIST *pTypesList,const WSD_URI_LIST *pScopesList,const WSD_URI_LIST *pXAddrsList,const WSDXML_ELEMENT *pHeaderAny,const WSDXML_ELEMENT *pReferenceParameterAny,const WSDXML_ELEMENT *pPolicyAny,const WSDXML_ELEMENT *pEndpointReferenceAny,const WSDXML_ELEMENT *pAny) {
    return This->lpVtbl->PublishEx(This,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList,pHeaderAny,pReferenceParameterAny,pPolicyAny,pEndpointReferenceAny,pAny);
}
static inline HRESULT IWSDiscoveryPublisher_MatchProbeEx(IWSDiscoveryPublisher* This,const WSD_SOAP_MESSAGE *pProbeMessage,IWSDMessageParameters *pMessageParameters,LPCWSTR pszId,ULONGLONG ullMetadataVersion,ULONGLONG ullInstanceId,ULONGLONG ullMessageNumber,LPCWSTR pszSessionId,const WSD_NAME_LIST *pTypesList,const WSD_URI_LIST *pScopesList,const WSD_URI_LIST *pXAddrsList,const WSDXML_ELEMENT *pHeaderAny,const WSDXML_ELEMENT *pReferenceParameterAny,const WSDXML_ELEMENT *pPolicyAny,const WSDXML_ELEMENT *pEndpointReferenceAny,const WSDXML_ELEMENT *pAny) {
    return This->lpVtbl->MatchProbeEx(This,pProbeMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList,pHeaderAny,pReferenceParameterAny,pPolicyAny,pEndpointReferenceAny,pAny);
}
static inline HRESULT IWSDiscoveryPublisher_MatchResolveEx(IWSDiscoveryPublisher* This,const WSD_SOAP_MESSAGE *pResolveMessage,IWSDMessageParameters *pMessageParameters,LPCWSTR pszId,ULONGLONG ullMetadataVersion,ULONGLONG ullInstanceId,ULONGLONG ullMessageNumber,LPCWSTR pszSessionId,const WSD_NAME_LIST *pTypesList,const WSD_URI_LIST *pScopesList,const WSD_URI_LIST *pXAddrsList,const WSDXML_ELEMENT *pHeaderAny,const WSDXML_ELEMENT *pReferenceParameterAny,const WSDXML_ELEMENT *pPolicyAny,const WSDXML_ELEMENT *pEndpointReferenceAny,const WSDXML_ELEMENT *pAny) {
    return This->lpVtbl->MatchResolveEx(This,pResolveMessage,pMessageParameters,pszId,ullMetadataVersion,ullInstanceId,ullMessageNumber,pszSessionId,pTypesList,pScopesList,pXAddrsList,pHeaderAny,pReferenceParameterAny,pPolicyAny,pEndpointReferenceAny,pAny);
}
static inline HRESULT IWSDiscoveryPublisher_RegisterScopeMatchingRule(IWSDiscoveryPublisher* This,IWSDScopeMatchingRule *pScopeMatchingRule) {
    return This->lpVtbl->RegisterScopeMatchingRule(This,pScopeMatchingRule);
}
static inline HRESULT IWSDiscoveryPublisher_UnRegisterScopeMatchingRule(IWSDiscoveryPublisher* This,IWSDScopeMatchingRule *pScopeMatchingRule) {
    return This->lpVtbl->UnRegisterScopeMatchingRule(This,pScopeMatchingRule);
}
static inline HRESULT IWSDiscoveryPublisher_GetXMLContext(IWSDiscoveryPublisher* This,IWSDXMLContext **ppContext) {
    return This->lpVtbl->GetXMLContext(This,ppContext);
}
#endif
#endif

#endif


#endif  /* __IWSDiscoveryPublisher_INTERFACE_DEFINED__ */

HRESULT WINAPI WSDCreateDiscoveryProvider(IWSDXMLContext *pContext, IWSDiscoveryProvider **ppProvider);
HRESULT WINAPI WSDCreateDiscoveryPublisher(IWSDXMLContext* pContext, IWSDiscoveryPublisher **ppPublisher);
#if WINVER >= 0x601
HRESULT WINAPI WSDCreateDiscoveryProvider2(IWSDXMLContext *pContext, WSD_CONFIG_PARAM *pConfigParams, DWORD dwConfigParamCount, IWSDiscoveryProvider **ppProvider);
HRESULT WINAPI WSDCreateDiscoveryPublisher2(IWSDXMLContext *pContext, WSD_CONFIG_PARAM *pConfigParams, DWORD dwConfigParamCount, IWSDiscoveryPublisher **ppPublisher);
#endif
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wsddisco_h__ */
