/*** Autogenerated by WIDL 10.12 from include/xpsdigitalsignature.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __xpsdigitalsignature_h__
#define __xpsdigitalsignature_h__

/* Forward declarations */

#ifndef __IXpsSigningOptions_FWD_DEFINED__
#define __IXpsSigningOptions_FWD_DEFINED__
typedef interface IXpsSigningOptions IXpsSigningOptions;
#ifdef __cplusplus
interface IXpsSigningOptions;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignature_FWD_DEFINED__
#define __IXpsSignature_FWD_DEFINED__
typedef interface IXpsSignature IXpsSignature;
#ifdef __cplusplus
interface IXpsSignature;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignatureBlock_FWD_DEFINED__
#define __IXpsSignatureBlock_FWD_DEFINED__
typedef interface IXpsSignatureBlock IXpsSignatureBlock;
#ifdef __cplusplus
interface IXpsSignatureBlock;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignatureBlockCollection_FWD_DEFINED__
#define __IXpsSignatureBlockCollection_FWD_DEFINED__
typedef interface IXpsSignatureBlockCollection IXpsSignatureBlockCollection;
#ifdef __cplusplus
interface IXpsSignatureBlockCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignatureCollection_FWD_DEFINED__
#define __IXpsSignatureCollection_FWD_DEFINED__
typedef interface IXpsSignatureCollection IXpsSignatureCollection;
#ifdef __cplusplus
interface IXpsSignatureCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignatureManager_FWD_DEFINED__
#define __IXpsSignatureManager_FWD_DEFINED__
typedef interface IXpsSignatureManager IXpsSignatureManager;
#ifdef __cplusplus
interface IXpsSignatureManager;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignatureRequest_FWD_DEFINED__
#define __IXpsSignatureRequest_FWD_DEFINED__
typedef interface IXpsSignatureRequest IXpsSignatureRequest;
#ifdef __cplusplus
interface IXpsSignatureRequest;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignatureRequestCollection_FWD_DEFINED__
#define __IXpsSignatureRequestCollection_FWD_DEFINED__
typedef interface IXpsSignatureRequestCollection IXpsSignatureRequestCollection;
#ifdef __cplusplus
interface IXpsSignatureRequestCollection;
#endif /* __cplusplus */
#endif

#ifndef __XpsSignatureManager_FWD_DEFINED__
#define __XpsSignatureManager_FWD_DEFINED__
#ifdef __cplusplus
typedef class XpsSignatureManager XpsSignatureManager;
#else
typedef struct XpsSignatureManager XpsSignatureManager;
#endif /* defined __cplusplus */
#endif /* defined __XpsSignatureManager_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <wincrypt.h>
#include <msopc.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#if NTDDI_VERSION >= 0x06010000
#define XPS_E_SIGREQUESTID_DUP  MAKE_HRESULT(1, FACILITY_XPS, 901)
#define XPS_E_PACKAGE_NOT_OPENED MAKE_HRESULT(1, FACILITY_XPS, 902)
#define XPS_E_PACKAGE_ALREADY_OPENED MAKE_HRESULT(1, FACILITY_XPS, 903)
#define XPS_E_SIGNATUREID_DUP MAKE_HRESULT(1, FACILITY_XPS, 904)
#define XPS_E_MARKUP_COMPATIBILITY_ELEMENTS MAKE_HRESULT(1, FACILITY_XPS, 905)
#define XPS_E_OBJECT_DETACHED MAKE_HRESULT(1, FACILITY_XPS, 906)
#define XPS_E_INVALID_SIGNATUREBLOCK_MARKUP MAKE_HRESULT(1, FACILITY_XPS, 907)
#ifndef __MSXPSSIG_LIBRARY_DEFINED__
#define __MSXPSSIG_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_MSXPSSIG, 0x8223a7eb, 0xc4d5, 0x474d, 0x9b,0xcc, 0xff,0x67,0x18,0x5e,0x64,0xa0);

#ifndef __IXpsSigningOptions_FWD_DEFINED__
#define __IXpsSigningOptions_FWD_DEFINED__
typedef interface IXpsSigningOptions IXpsSigningOptions;
#ifdef __cplusplus
interface IXpsSigningOptions;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignatureCollection_FWD_DEFINED__
#define __IXpsSignatureCollection_FWD_DEFINED__
typedef interface IXpsSignatureCollection IXpsSignatureCollection;
#ifdef __cplusplus
interface IXpsSignatureCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignature_FWD_DEFINED__
#define __IXpsSignature_FWD_DEFINED__
typedef interface IXpsSignature IXpsSignature;
#ifdef __cplusplus
interface IXpsSignature;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignatureBlockCollection_FWD_DEFINED__
#define __IXpsSignatureBlockCollection_FWD_DEFINED__
typedef interface IXpsSignatureBlockCollection IXpsSignatureBlockCollection;
#ifdef __cplusplus
interface IXpsSignatureBlockCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignatureBlock_FWD_DEFINED__
#define __IXpsSignatureBlock_FWD_DEFINED__
typedef interface IXpsSignatureBlock IXpsSignatureBlock;
#ifdef __cplusplus
interface IXpsSignatureBlock;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignatureRequestCollection_FWD_DEFINED__
#define __IXpsSignatureRequestCollection_FWD_DEFINED__
typedef interface IXpsSignatureRequestCollection IXpsSignatureRequestCollection;
#ifdef __cplusplus
interface IXpsSignatureRequestCollection;
#endif /* __cplusplus */
#endif

#ifndef __IXpsSignatureRequest_FWD_DEFINED__
#define __IXpsSignatureRequest_FWD_DEFINED__
typedef interface IXpsSignatureRequest IXpsSignatureRequest;
#ifdef __cplusplus
interface IXpsSignatureRequest;
#endif /* __cplusplus */
#endif

typedef enum __WIDL_xpsdigitalsignature_generated_name_00000031 {
    XPS_SIGN_FLAGS_NONE = 0x0,
    XPS_SIGN_FLAGS_IGNORE_MARKUP_COMPATIBILITY = 0x1
} XPS_SIGN_FLAGS;
typedef enum __WIDL_xpsdigitalsignature_generated_name_00000032 {
    XPS_SIGN_POLICY_NONE = 0x0,
    XPS_SIGN_POLICY_CORE_PROPERTIES = 0x1,
    XPS_SIGN_POLICY_SIGNATURE_RELATIONSHIPS = 0x2,
    XPS_SIGN_POLICY_PRINT_TICKET = 0x4,
    XPS_SIGN_POLICY_DISCARD_CONTROL = 0x8,
    XPS_SIGN_POLICY_ALL = 0xf
} XPS_SIGN_POLICY;
typedef enum __WIDL_xpsdigitalsignature_generated_name_00000033 {
    XPS_SIGNATURE_STATUS_INCOMPLIANT = 1,
    XPS_SIGNATURE_STATUS_INCOMPLETE = 2,
    XPS_SIGNATURE_STATUS_BROKEN = 3,
    XPS_SIGNATURE_STATUS_QUESTIONABLE = 4,
    XPS_SIGNATURE_STATUS_VALID = 5
} XPS_SIGNATURE_STATUS;
DEFINE_ENUM_FLAG_OPERATORS(XPS_SIGN_FLAGS)
DEFINE_ENUM_FLAG_OPERATORS(XPS_SIGN_POLICY)
/*****************************************************************************
 * IXpsSigningOptions interface
 */
#ifndef __IXpsSigningOptions_INTERFACE_DEFINED__
#define __IXpsSigningOptions_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsSigningOptions, 0x7718eae4, 0x3215, 0x49be, 0xaf,0x5b, 0x59,0x4f,0xef,0x7f,0xcf,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7718eae4-3215-49be-af5b-594fef7fcfa6")
IXpsSigningOptions : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSignatureId(
        LPWSTR *signatureId) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignatureId(
        LPCWSTR signatureId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureMethod(
        LPWSTR *signatureMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignatureMethod(
        LPCWSTR signatureMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDigestMethod(
        LPWSTR *digestMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDigestMethod(
        LPCWSTR digestMethod) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignaturePartName(
        IOpcPartUri **signaturePartName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignaturePartName(
        IOpcPartUri *signaturePartName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPolicy(
        XPS_SIGN_POLICY *policy) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPolicy(
        XPS_SIGN_POLICY policy) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSigningTimeFormat(
        OPC_SIGNATURE_TIME_FORMAT *timeFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSigningTimeFormat(
        OPC_SIGNATURE_TIME_FORMAT timeFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCustomObjects(
        IOpcSignatureCustomObjectSet **customObjectSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCustomReferences(
        IOpcSignatureReferenceSet **customReferenceSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCertificateSet(
        IOpcCertificateSet **certificateSet) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFlags(
        XPS_SIGN_FLAGS *flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFlags(
        XPS_SIGN_FLAGS flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsSigningOptions, 0x7718eae4, 0x3215, 0x49be, 0xaf,0x5b, 0x59,0x4f,0xef,0x7f,0xcf,0xa6)
#endif
#else
typedef struct IXpsSigningOptionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsSigningOptions *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsSigningOptions *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsSigningOptions *This);

    /*** IXpsSigningOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSignatureId)(
        IXpsSigningOptions *This,
        LPWSTR *signatureId);

    HRESULT (STDMETHODCALLTYPE *SetSignatureId)(
        IXpsSigningOptions *This,
        LPCWSTR signatureId);

    HRESULT (STDMETHODCALLTYPE *GetSignatureMethod)(
        IXpsSigningOptions *This,
        LPWSTR *signatureMethod);

    HRESULT (STDMETHODCALLTYPE *SetSignatureMethod)(
        IXpsSigningOptions *This,
        LPCWSTR signatureMethod);

    HRESULT (STDMETHODCALLTYPE *GetDigestMethod)(
        IXpsSigningOptions *This,
        LPWSTR *digestMethod);

    HRESULT (STDMETHODCALLTYPE *SetDigestMethod)(
        IXpsSigningOptions *This,
        LPCWSTR digestMethod);

    HRESULT (STDMETHODCALLTYPE *GetSignaturePartName)(
        IXpsSigningOptions *This,
        IOpcPartUri **signaturePartName);

    HRESULT (STDMETHODCALLTYPE *SetSignaturePartName)(
        IXpsSigningOptions *This,
        IOpcPartUri *signaturePartName);

    HRESULT (STDMETHODCALLTYPE *GetPolicy)(
        IXpsSigningOptions *This,
        XPS_SIGN_POLICY *policy);

    HRESULT (STDMETHODCALLTYPE *SetPolicy)(
        IXpsSigningOptions *This,
        XPS_SIGN_POLICY policy);

    HRESULT (STDMETHODCALLTYPE *GetSigningTimeFormat)(
        IXpsSigningOptions *This,
        OPC_SIGNATURE_TIME_FORMAT *timeFormat);

    HRESULT (STDMETHODCALLTYPE *SetSigningTimeFormat)(
        IXpsSigningOptions *This,
        OPC_SIGNATURE_TIME_FORMAT timeFormat);

    HRESULT (STDMETHODCALLTYPE *GetCustomObjects)(
        IXpsSigningOptions *This,
        IOpcSignatureCustomObjectSet **customObjectSet);

    HRESULT (STDMETHODCALLTYPE *GetCustomReferences)(
        IXpsSigningOptions *This,
        IOpcSignatureReferenceSet **customReferenceSet);

    HRESULT (STDMETHODCALLTYPE *GetCertificateSet)(
        IXpsSigningOptions *This,
        IOpcCertificateSet **certificateSet);

    HRESULT (STDMETHODCALLTYPE *GetFlags)(
        IXpsSigningOptions *This,
        XPS_SIGN_FLAGS *flags);

    HRESULT (STDMETHODCALLTYPE *SetFlags)(
        IXpsSigningOptions *This,
        XPS_SIGN_FLAGS flags);

    END_INTERFACE
} IXpsSigningOptionsVtbl;

interface IXpsSigningOptions {
    CONST_VTBL IXpsSigningOptionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsSigningOptions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsSigningOptions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsSigningOptions_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsSigningOptions methods ***/
#define IXpsSigningOptions_GetSignatureId(This,signatureId) (This)->lpVtbl->GetSignatureId(This,signatureId)
#define IXpsSigningOptions_SetSignatureId(This,signatureId) (This)->lpVtbl->SetSignatureId(This,signatureId)
#define IXpsSigningOptions_GetSignatureMethod(This,signatureMethod) (This)->lpVtbl->GetSignatureMethod(This,signatureMethod)
#define IXpsSigningOptions_SetSignatureMethod(This,signatureMethod) (This)->lpVtbl->SetSignatureMethod(This,signatureMethod)
#define IXpsSigningOptions_GetDigestMethod(This,digestMethod) (This)->lpVtbl->GetDigestMethod(This,digestMethod)
#define IXpsSigningOptions_SetDigestMethod(This,digestMethod) (This)->lpVtbl->SetDigestMethod(This,digestMethod)
#define IXpsSigningOptions_GetSignaturePartName(This,signaturePartName) (This)->lpVtbl->GetSignaturePartName(This,signaturePartName)
#define IXpsSigningOptions_SetSignaturePartName(This,signaturePartName) (This)->lpVtbl->SetSignaturePartName(This,signaturePartName)
#define IXpsSigningOptions_GetPolicy(This,policy) (This)->lpVtbl->GetPolicy(This,policy)
#define IXpsSigningOptions_SetPolicy(This,policy) (This)->lpVtbl->SetPolicy(This,policy)
#define IXpsSigningOptions_GetSigningTimeFormat(This,timeFormat) (This)->lpVtbl->GetSigningTimeFormat(This,timeFormat)
#define IXpsSigningOptions_SetSigningTimeFormat(This,timeFormat) (This)->lpVtbl->SetSigningTimeFormat(This,timeFormat)
#define IXpsSigningOptions_GetCustomObjects(This,customObjectSet) (This)->lpVtbl->GetCustomObjects(This,customObjectSet)
#define IXpsSigningOptions_GetCustomReferences(This,customReferenceSet) (This)->lpVtbl->GetCustomReferences(This,customReferenceSet)
#define IXpsSigningOptions_GetCertificateSet(This,certificateSet) (This)->lpVtbl->GetCertificateSet(This,certificateSet)
#define IXpsSigningOptions_GetFlags(This,flags) (This)->lpVtbl->GetFlags(This,flags)
#define IXpsSigningOptions_SetFlags(This,flags) (This)->lpVtbl->SetFlags(This,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsSigningOptions_QueryInterface(IXpsSigningOptions* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsSigningOptions_AddRef(IXpsSigningOptions* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsSigningOptions_Release(IXpsSigningOptions* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsSigningOptions methods ***/
static inline HRESULT IXpsSigningOptions_GetSignatureId(IXpsSigningOptions* This,LPWSTR *signatureId) {
    return This->lpVtbl->GetSignatureId(This,signatureId);
}
static inline HRESULT IXpsSigningOptions_SetSignatureId(IXpsSigningOptions* This,LPCWSTR signatureId) {
    return This->lpVtbl->SetSignatureId(This,signatureId);
}
static inline HRESULT IXpsSigningOptions_GetSignatureMethod(IXpsSigningOptions* This,LPWSTR *signatureMethod) {
    return This->lpVtbl->GetSignatureMethod(This,signatureMethod);
}
static inline HRESULT IXpsSigningOptions_SetSignatureMethod(IXpsSigningOptions* This,LPCWSTR signatureMethod) {
    return This->lpVtbl->SetSignatureMethod(This,signatureMethod);
}
static inline HRESULT IXpsSigningOptions_GetDigestMethod(IXpsSigningOptions* This,LPWSTR *digestMethod) {
    return This->lpVtbl->GetDigestMethod(This,digestMethod);
}
static inline HRESULT IXpsSigningOptions_SetDigestMethod(IXpsSigningOptions* This,LPCWSTR digestMethod) {
    return This->lpVtbl->SetDigestMethod(This,digestMethod);
}
static inline HRESULT IXpsSigningOptions_GetSignaturePartName(IXpsSigningOptions* This,IOpcPartUri **signaturePartName) {
    return This->lpVtbl->GetSignaturePartName(This,signaturePartName);
}
static inline HRESULT IXpsSigningOptions_SetSignaturePartName(IXpsSigningOptions* This,IOpcPartUri *signaturePartName) {
    return This->lpVtbl->SetSignaturePartName(This,signaturePartName);
}
static inline HRESULT IXpsSigningOptions_GetPolicy(IXpsSigningOptions* This,XPS_SIGN_POLICY *policy) {
    return This->lpVtbl->GetPolicy(This,policy);
}
static inline HRESULT IXpsSigningOptions_SetPolicy(IXpsSigningOptions* This,XPS_SIGN_POLICY policy) {
    return This->lpVtbl->SetPolicy(This,policy);
}
static inline HRESULT IXpsSigningOptions_GetSigningTimeFormat(IXpsSigningOptions* This,OPC_SIGNATURE_TIME_FORMAT *timeFormat) {
    return This->lpVtbl->GetSigningTimeFormat(This,timeFormat);
}
static inline HRESULT IXpsSigningOptions_SetSigningTimeFormat(IXpsSigningOptions* This,OPC_SIGNATURE_TIME_FORMAT timeFormat) {
    return This->lpVtbl->SetSigningTimeFormat(This,timeFormat);
}
static inline HRESULT IXpsSigningOptions_GetCustomObjects(IXpsSigningOptions* This,IOpcSignatureCustomObjectSet **customObjectSet) {
    return This->lpVtbl->GetCustomObjects(This,customObjectSet);
}
static inline HRESULT IXpsSigningOptions_GetCustomReferences(IXpsSigningOptions* This,IOpcSignatureReferenceSet **customReferenceSet) {
    return This->lpVtbl->GetCustomReferences(This,customReferenceSet);
}
static inline HRESULT IXpsSigningOptions_GetCertificateSet(IXpsSigningOptions* This,IOpcCertificateSet **certificateSet) {
    return This->lpVtbl->GetCertificateSet(This,certificateSet);
}
static inline HRESULT IXpsSigningOptions_GetFlags(IXpsSigningOptions* This,XPS_SIGN_FLAGS *flags) {
    return This->lpVtbl->GetFlags(This,flags);
}
static inline HRESULT IXpsSigningOptions_SetFlags(IXpsSigningOptions* This,XPS_SIGN_FLAGS flags) {
    return This->lpVtbl->SetFlags(This,flags);
}
#endif
#endif

#endif


#endif  /* __IXpsSigningOptions_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsSignature interface
 */
#ifndef __IXpsSignature_INTERFACE_DEFINED__
#define __IXpsSignature_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsSignature, 0x6ae4c93e, 0x1ade, 0x42fb, 0x89,0x8b, 0x3a,0x56,0x58,0x28,0x48,0x57);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6ae4c93e-1ade-42fb-898b-3a5658284857")
IXpsSignature : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSignatureId(
        LPWSTR *sigId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureValue(
        UINT8 **signatureHashValue,
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCertificateEnumerator(
        IOpcCertificateEnumerator **certificateEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSigningTime(
        LPWSTR *sigDateTimeString) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSigningTimeFormat(
        OPC_SIGNATURE_TIME_FORMAT *timeFormat) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignaturePartName(
        IOpcPartUri **signaturePartName) = 0;

    virtual HRESULT STDMETHODCALLTYPE Verify(
        const CERT_CONTEXT *x509Certificate,
        XPS_SIGNATURE_STATUS *sigStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPolicy(
        XPS_SIGN_POLICY *policy) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCustomObjectEnumerator(
        IOpcSignatureCustomObjectEnumerator **customObjectEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCustomReferenceEnumerator(
        IOpcSignatureReferenceEnumerator **customReferenceEnumerator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureXml(
        UINT8 **signatureXml,
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignatureXml(
        const UINT8 *signatureXml,
        UINT32 count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsSignature, 0x6ae4c93e, 0x1ade, 0x42fb, 0x89,0x8b, 0x3a,0x56,0x58,0x28,0x48,0x57)
#endif
#else
typedef struct IXpsSignatureVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsSignature *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsSignature *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsSignature *This);

    /*** IXpsSignature methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSignatureId)(
        IXpsSignature *This,
        LPWSTR *sigId);

    HRESULT (STDMETHODCALLTYPE *GetSignatureValue)(
        IXpsSignature *This,
        UINT8 **signatureHashValue,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetCertificateEnumerator)(
        IXpsSignature *This,
        IOpcCertificateEnumerator **certificateEnumerator);

    HRESULT (STDMETHODCALLTYPE *GetSigningTime)(
        IXpsSignature *This,
        LPWSTR *sigDateTimeString);

    HRESULT (STDMETHODCALLTYPE *GetSigningTimeFormat)(
        IXpsSignature *This,
        OPC_SIGNATURE_TIME_FORMAT *timeFormat);

    HRESULT (STDMETHODCALLTYPE *GetSignaturePartName)(
        IXpsSignature *This,
        IOpcPartUri **signaturePartName);

    HRESULT (STDMETHODCALLTYPE *Verify)(
        IXpsSignature *This,
        const CERT_CONTEXT *x509Certificate,
        XPS_SIGNATURE_STATUS *sigStatus);

    HRESULT (STDMETHODCALLTYPE *GetPolicy)(
        IXpsSignature *This,
        XPS_SIGN_POLICY *policy);

    HRESULT (STDMETHODCALLTYPE *GetCustomObjectEnumerator)(
        IXpsSignature *This,
        IOpcSignatureCustomObjectEnumerator **customObjectEnumerator);

    HRESULT (STDMETHODCALLTYPE *GetCustomReferenceEnumerator)(
        IXpsSignature *This,
        IOpcSignatureReferenceEnumerator **customReferenceEnumerator);

    HRESULT (STDMETHODCALLTYPE *GetSignatureXml)(
        IXpsSignature *This,
        UINT8 **signatureXml,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *SetSignatureXml)(
        IXpsSignature *This,
        const UINT8 *signatureXml,
        UINT32 count);

    END_INTERFACE
} IXpsSignatureVtbl;

interface IXpsSignature {
    CONST_VTBL IXpsSignatureVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsSignature_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsSignature_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsSignature_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsSignature methods ***/
#define IXpsSignature_GetSignatureId(This,sigId) (This)->lpVtbl->GetSignatureId(This,sigId)
#define IXpsSignature_GetSignatureValue(This,signatureHashValue,count) (This)->lpVtbl->GetSignatureValue(This,signatureHashValue,count)
#define IXpsSignature_GetCertificateEnumerator(This,certificateEnumerator) (This)->lpVtbl->GetCertificateEnumerator(This,certificateEnumerator)
#define IXpsSignature_GetSigningTime(This,sigDateTimeString) (This)->lpVtbl->GetSigningTime(This,sigDateTimeString)
#define IXpsSignature_GetSigningTimeFormat(This,timeFormat) (This)->lpVtbl->GetSigningTimeFormat(This,timeFormat)
#define IXpsSignature_GetSignaturePartName(This,signaturePartName) (This)->lpVtbl->GetSignaturePartName(This,signaturePartName)
#define IXpsSignature_Verify(This,x509Certificate,sigStatus) (This)->lpVtbl->Verify(This,x509Certificate,sigStatus)
#define IXpsSignature_GetPolicy(This,policy) (This)->lpVtbl->GetPolicy(This,policy)
#define IXpsSignature_GetCustomObjectEnumerator(This,customObjectEnumerator) (This)->lpVtbl->GetCustomObjectEnumerator(This,customObjectEnumerator)
#define IXpsSignature_GetCustomReferenceEnumerator(This,customReferenceEnumerator) (This)->lpVtbl->GetCustomReferenceEnumerator(This,customReferenceEnumerator)
#define IXpsSignature_GetSignatureXml(This,signatureXml,count) (This)->lpVtbl->GetSignatureXml(This,signatureXml,count)
#define IXpsSignature_SetSignatureXml(This,signatureXml,count) (This)->lpVtbl->SetSignatureXml(This,signatureXml,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsSignature_QueryInterface(IXpsSignature* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsSignature_AddRef(IXpsSignature* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsSignature_Release(IXpsSignature* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsSignature methods ***/
static inline HRESULT IXpsSignature_GetSignatureId(IXpsSignature* This,LPWSTR *sigId) {
    return This->lpVtbl->GetSignatureId(This,sigId);
}
static inline HRESULT IXpsSignature_GetSignatureValue(IXpsSignature* This,UINT8 **signatureHashValue,UINT32 *count) {
    return This->lpVtbl->GetSignatureValue(This,signatureHashValue,count);
}
static inline HRESULT IXpsSignature_GetCertificateEnumerator(IXpsSignature* This,IOpcCertificateEnumerator **certificateEnumerator) {
    return This->lpVtbl->GetCertificateEnumerator(This,certificateEnumerator);
}
static inline HRESULT IXpsSignature_GetSigningTime(IXpsSignature* This,LPWSTR *sigDateTimeString) {
    return This->lpVtbl->GetSigningTime(This,sigDateTimeString);
}
static inline HRESULT IXpsSignature_GetSigningTimeFormat(IXpsSignature* This,OPC_SIGNATURE_TIME_FORMAT *timeFormat) {
    return This->lpVtbl->GetSigningTimeFormat(This,timeFormat);
}
static inline HRESULT IXpsSignature_GetSignaturePartName(IXpsSignature* This,IOpcPartUri **signaturePartName) {
    return This->lpVtbl->GetSignaturePartName(This,signaturePartName);
}
static inline HRESULT IXpsSignature_Verify(IXpsSignature* This,const CERT_CONTEXT *x509Certificate,XPS_SIGNATURE_STATUS *sigStatus) {
    return This->lpVtbl->Verify(This,x509Certificate,sigStatus);
}
static inline HRESULT IXpsSignature_GetPolicy(IXpsSignature* This,XPS_SIGN_POLICY *policy) {
    return This->lpVtbl->GetPolicy(This,policy);
}
static inline HRESULT IXpsSignature_GetCustomObjectEnumerator(IXpsSignature* This,IOpcSignatureCustomObjectEnumerator **customObjectEnumerator) {
    return This->lpVtbl->GetCustomObjectEnumerator(This,customObjectEnumerator);
}
static inline HRESULT IXpsSignature_GetCustomReferenceEnumerator(IXpsSignature* This,IOpcSignatureReferenceEnumerator **customReferenceEnumerator) {
    return This->lpVtbl->GetCustomReferenceEnumerator(This,customReferenceEnumerator);
}
static inline HRESULT IXpsSignature_GetSignatureXml(IXpsSignature* This,UINT8 **signatureXml,UINT32 *count) {
    return This->lpVtbl->GetSignatureXml(This,signatureXml,count);
}
static inline HRESULT IXpsSignature_SetSignatureXml(IXpsSignature* This,const UINT8 *signatureXml,UINT32 count) {
    return This->lpVtbl->SetSignatureXml(This,signatureXml,count);
}
#endif
#endif

#endif


#endif  /* __IXpsSignature_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsSignatureBlock interface
 */
#ifndef __IXpsSignatureBlock_INTERFACE_DEFINED__
#define __IXpsSignatureBlock_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsSignatureBlock, 0x151fac09, 0x0b97, 0x4ac6, 0xa3,0x23, 0x5e,0x42,0x97,0xd4,0x32,0x2b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("151fac09-0b97-4ac6-a323-5e4297d4322b")
IXpsSignatureBlock : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetRequests(
        IXpsSignatureRequestCollection **requests) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPartName(
        IOpcPartUri **partName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocumentIndex(
        UINT32 *fixedDocumentIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocumentName(
        IOpcPartUri **fixedDocumentName) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateRequest(
        LPCWSTR requestId,
        IXpsSignatureRequest **signatureRequest) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsSignatureBlock, 0x151fac09, 0x0b97, 0x4ac6, 0xa3,0x23, 0x5e,0x42,0x97,0xd4,0x32,0x2b)
#endif
#else
typedef struct IXpsSignatureBlockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsSignatureBlock *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsSignatureBlock *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsSignatureBlock *This);

    /*** IXpsSignatureBlock methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRequests)(
        IXpsSignatureBlock *This,
        IXpsSignatureRequestCollection **requests);

    HRESULT (STDMETHODCALLTYPE *GetPartName)(
        IXpsSignatureBlock *This,
        IOpcPartUri **partName);

    HRESULT (STDMETHODCALLTYPE *GetDocumentIndex)(
        IXpsSignatureBlock *This,
        UINT32 *fixedDocumentIndex);

    HRESULT (STDMETHODCALLTYPE *GetDocumentName)(
        IXpsSignatureBlock *This,
        IOpcPartUri **fixedDocumentName);

    HRESULT (STDMETHODCALLTYPE *CreateRequest)(
        IXpsSignatureBlock *This,
        LPCWSTR requestId,
        IXpsSignatureRequest **signatureRequest);

    END_INTERFACE
} IXpsSignatureBlockVtbl;

interface IXpsSignatureBlock {
    CONST_VTBL IXpsSignatureBlockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsSignatureBlock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsSignatureBlock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsSignatureBlock_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsSignatureBlock methods ***/
#define IXpsSignatureBlock_GetRequests(This,requests) (This)->lpVtbl->GetRequests(This,requests)
#define IXpsSignatureBlock_GetPartName(This,partName) (This)->lpVtbl->GetPartName(This,partName)
#define IXpsSignatureBlock_GetDocumentIndex(This,fixedDocumentIndex) (This)->lpVtbl->GetDocumentIndex(This,fixedDocumentIndex)
#define IXpsSignatureBlock_GetDocumentName(This,fixedDocumentName) (This)->lpVtbl->GetDocumentName(This,fixedDocumentName)
#define IXpsSignatureBlock_CreateRequest(This,requestId,signatureRequest) (This)->lpVtbl->CreateRequest(This,requestId,signatureRequest)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsSignatureBlock_QueryInterface(IXpsSignatureBlock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsSignatureBlock_AddRef(IXpsSignatureBlock* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsSignatureBlock_Release(IXpsSignatureBlock* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsSignatureBlock methods ***/
static inline HRESULT IXpsSignatureBlock_GetRequests(IXpsSignatureBlock* This,IXpsSignatureRequestCollection **requests) {
    return This->lpVtbl->GetRequests(This,requests);
}
static inline HRESULT IXpsSignatureBlock_GetPartName(IXpsSignatureBlock* This,IOpcPartUri **partName) {
    return This->lpVtbl->GetPartName(This,partName);
}
static inline HRESULT IXpsSignatureBlock_GetDocumentIndex(IXpsSignatureBlock* This,UINT32 *fixedDocumentIndex) {
    return This->lpVtbl->GetDocumentIndex(This,fixedDocumentIndex);
}
static inline HRESULT IXpsSignatureBlock_GetDocumentName(IXpsSignatureBlock* This,IOpcPartUri **fixedDocumentName) {
    return This->lpVtbl->GetDocumentName(This,fixedDocumentName);
}
static inline HRESULT IXpsSignatureBlock_CreateRequest(IXpsSignatureBlock* This,LPCWSTR requestId,IXpsSignatureRequest **signatureRequest) {
    return This->lpVtbl->CreateRequest(This,requestId,signatureRequest);
}
#endif
#endif

#endif


#endif  /* __IXpsSignatureBlock_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsSignatureBlockCollection interface
 */
#ifndef __IXpsSignatureBlockCollection_INTERFACE_DEFINED__
#define __IXpsSignatureBlockCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsSignatureBlockCollection, 0x23397050, 0xfe99, 0x467a, 0x8d,0xce, 0x92,0x37,0xf0,0x74,0xff,0xe4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("23397050-fe99-467a-8dce-9237f074ffe4")
IXpsSignatureBlockCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsSignatureBlock **signatureBlock) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsSignatureBlockCollection, 0x23397050, 0xfe99, 0x467a, 0x8d,0xce, 0x92,0x37,0xf0,0x74,0xff,0xe4)
#endif
#else
typedef struct IXpsSignatureBlockCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsSignatureBlockCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsSignatureBlockCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsSignatureBlockCollection *This);

    /*** IXpsSignatureBlockCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsSignatureBlockCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsSignatureBlockCollection *This,
        UINT32 index,
        IXpsSignatureBlock **signatureBlock);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsSignatureBlockCollection *This,
        UINT32 index);

    END_INTERFACE
} IXpsSignatureBlockCollectionVtbl;

interface IXpsSignatureBlockCollection {
    CONST_VTBL IXpsSignatureBlockCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsSignatureBlockCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsSignatureBlockCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsSignatureBlockCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsSignatureBlockCollection methods ***/
#define IXpsSignatureBlockCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsSignatureBlockCollection_GetAt(This,index,signatureBlock) (This)->lpVtbl->GetAt(This,index,signatureBlock)
#define IXpsSignatureBlockCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsSignatureBlockCollection_QueryInterface(IXpsSignatureBlockCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsSignatureBlockCollection_AddRef(IXpsSignatureBlockCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsSignatureBlockCollection_Release(IXpsSignatureBlockCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsSignatureBlockCollection methods ***/
static inline HRESULT IXpsSignatureBlockCollection_GetCount(IXpsSignatureBlockCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsSignatureBlockCollection_GetAt(IXpsSignatureBlockCollection* This,UINT32 index,IXpsSignatureBlock **signatureBlock) {
    return This->lpVtbl->GetAt(This,index,signatureBlock);
}
static inline HRESULT IXpsSignatureBlockCollection_RemoveAt(IXpsSignatureBlockCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
#endif
#endif

#endif


#endif  /* __IXpsSignatureBlockCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsSignatureCollection interface
 */
#ifndef __IXpsSignatureCollection_INTERFACE_DEFINED__
#define __IXpsSignatureCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsSignatureCollection, 0xa2d1d95d, 0xadd2, 0x4dff, 0xab,0x27, 0x6b,0x9c,0x64,0x5f,0xf3,0x22);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a2d1d95d-add2-4dff-ab27-6b9c645ff322")
IXpsSignatureCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsSignature **signature) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsSignatureCollection, 0xa2d1d95d, 0xadd2, 0x4dff, 0xab,0x27, 0x6b,0x9c,0x64,0x5f,0xf3,0x22)
#endif
#else
typedef struct IXpsSignatureCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsSignatureCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsSignatureCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsSignatureCollection *This);

    /*** IXpsSignatureCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsSignatureCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsSignatureCollection *This,
        UINT32 index,
        IXpsSignature **signature);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsSignatureCollection *This,
        UINT32 index);

    END_INTERFACE
} IXpsSignatureCollectionVtbl;

interface IXpsSignatureCollection {
    CONST_VTBL IXpsSignatureCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsSignatureCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsSignatureCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsSignatureCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsSignatureCollection methods ***/
#define IXpsSignatureCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsSignatureCollection_GetAt(This,index,signature) (This)->lpVtbl->GetAt(This,index,signature)
#define IXpsSignatureCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsSignatureCollection_QueryInterface(IXpsSignatureCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsSignatureCollection_AddRef(IXpsSignatureCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsSignatureCollection_Release(IXpsSignatureCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsSignatureCollection methods ***/
static inline HRESULT IXpsSignatureCollection_GetCount(IXpsSignatureCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsSignatureCollection_GetAt(IXpsSignatureCollection* This,UINT32 index,IXpsSignature **signature) {
    return This->lpVtbl->GetAt(This,index,signature);
}
static inline HRESULT IXpsSignatureCollection_RemoveAt(IXpsSignatureCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
#endif
#endif

#endif


#endif  /* __IXpsSignatureCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsSignatureManager interface
 */
#ifndef __IXpsSignatureManager_INTERFACE_DEFINED__
#define __IXpsSignatureManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsSignatureManager, 0xd3e8d338, 0xfdc4, 0x4afc, 0x80,0xb5, 0xd5,0x32,0xa1,0x78,0x2e,0xe1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d3e8d338-fdc4-4afc-80b5-d532a1782ee1")
IXpsSignatureManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE LoadPackageFile(
        LPCWSTR fileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE LoadPackageStream(
        IStream *stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE Sign(
        IXpsSigningOptions *signOptions,
        const CERT_CONTEXT *x509Certificate,
        IXpsSignature **signature) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureOriginPartName(
        IOpcPartUri **signatureOriginPartName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSignatureOriginPartName(
        IOpcPartUri *signatureOriginPartName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatures(
        IXpsSignatureCollection **signatures) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddSignatureBlock(
        IOpcPartUri *partName,
        UINT32 fixedDocumentIndex,
        IXpsSignatureBlock **signatureBlock) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignatureBlocks(
        IXpsSignatureBlockCollection **signatureBlocks) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateSigningOptions(
        IXpsSigningOptions **signingOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE SavePackageToFile(
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE SavePackageToStream(
        IStream *stream) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsSignatureManager, 0xd3e8d338, 0xfdc4, 0x4afc, 0x80,0xb5, 0xd5,0x32,0xa1,0x78,0x2e,0xe1)
#endif
#else
typedef struct IXpsSignatureManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsSignatureManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsSignatureManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsSignatureManager *This);

    /*** IXpsSignatureManager methods ***/
    HRESULT (STDMETHODCALLTYPE *LoadPackageFile)(
        IXpsSignatureManager *This,
        LPCWSTR fileName);

    HRESULT (STDMETHODCALLTYPE *LoadPackageStream)(
        IXpsSignatureManager *This,
        IStream *stream);

    HRESULT (STDMETHODCALLTYPE *Sign)(
        IXpsSignatureManager *This,
        IXpsSigningOptions *signOptions,
        const CERT_CONTEXT *x509Certificate,
        IXpsSignature **signature);

    HRESULT (STDMETHODCALLTYPE *GetSignatureOriginPartName)(
        IXpsSignatureManager *This,
        IOpcPartUri **signatureOriginPartName);

    HRESULT (STDMETHODCALLTYPE *SetSignatureOriginPartName)(
        IXpsSignatureManager *This,
        IOpcPartUri *signatureOriginPartName);

    HRESULT (STDMETHODCALLTYPE *GetSignatures)(
        IXpsSignatureManager *This,
        IXpsSignatureCollection **signatures);

    HRESULT (STDMETHODCALLTYPE *AddSignatureBlock)(
        IXpsSignatureManager *This,
        IOpcPartUri *partName,
        UINT32 fixedDocumentIndex,
        IXpsSignatureBlock **signatureBlock);

    HRESULT (STDMETHODCALLTYPE *GetSignatureBlocks)(
        IXpsSignatureManager *This,
        IXpsSignatureBlockCollection **signatureBlocks);

    HRESULT (STDMETHODCALLTYPE *CreateSigningOptions)(
        IXpsSignatureManager *This,
        IXpsSigningOptions **signingOptions);

    HRESULT (STDMETHODCALLTYPE *SavePackageToFile)(
        IXpsSignatureManager *This,
        LPCWSTR fileName,
        LPSECURITY_ATTRIBUTES securityAttributes,
        DWORD flagsAndAttributes);

    HRESULT (STDMETHODCALLTYPE *SavePackageToStream)(
        IXpsSignatureManager *This,
        IStream *stream);

    END_INTERFACE
} IXpsSignatureManagerVtbl;

interface IXpsSignatureManager {
    CONST_VTBL IXpsSignatureManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsSignatureManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsSignatureManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsSignatureManager_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsSignatureManager methods ***/
#define IXpsSignatureManager_LoadPackageFile(This,fileName) (This)->lpVtbl->LoadPackageFile(This,fileName)
#define IXpsSignatureManager_LoadPackageStream(This,stream) (This)->lpVtbl->LoadPackageStream(This,stream)
#define IXpsSignatureManager_Sign(This,signOptions,x509Certificate,signature) (This)->lpVtbl->Sign(This,signOptions,x509Certificate,signature)
#define IXpsSignatureManager_GetSignatureOriginPartName(This,signatureOriginPartName) (This)->lpVtbl->GetSignatureOriginPartName(This,signatureOriginPartName)
#define IXpsSignatureManager_SetSignatureOriginPartName(This,signatureOriginPartName) (This)->lpVtbl->SetSignatureOriginPartName(This,signatureOriginPartName)
#define IXpsSignatureManager_GetSignatures(This,signatures) (This)->lpVtbl->GetSignatures(This,signatures)
#define IXpsSignatureManager_AddSignatureBlock(This,partName,fixedDocumentIndex,signatureBlock) (This)->lpVtbl->AddSignatureBlock(This,partName,fixedDocumentIndex,signatureBlock)
#define IXpsSignatureManager_GetSignatureBlocks(This,signatureBlocks) (This)->lpVtbl->GetSignatureBlocks(This,signatureBlocks)
#define IXpsSignatureManager_CreateSigningOptions(This,signingOptions) (This)->lpVtbl->CreateSigningOptions(This,signingOptions)
#define IXpsSignatureManager_SavePackageToFile(This,fileName,securityAttributes,flagsAndAttributes) (This)->lpVtbl->SavePackageToFile(This,fileName,securityAttributes,flagsAndAttributes)
#define IXpsSignatureManager_SavePackageToStream(This,stream) (This)->lpVtbl->SavePackageToStream(This,stream)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsSignatureManager_QueryInterface(IXpsSignatureManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsSignatureManager_AddRef(IXpsSignatureManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsSignatureManager_Release(IXpsSignatureManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsSignatureManager methods ***/
static inline HRESULT IXpsSignatureManager_LoadPackageFile(IXpsSignatureManager* This,LPCWSTR fileName) {
    return This->lpVtbl->LoadPackageFile(This,fileName);
}
static inline HRESULT IXpsSignatureManager_LoadPackageStream(IXpsSignatureManager* This,IStream *stream) {
    return This->lpVtbl->LoadPackageStream(This,stream);
}
static inline HRESULT IXpsSignatureManager_Sign(IXpsSignatureManager* This,IXpsSigningOptions *signOptions,const CERT_CONTEXT *x509Certificate,IXpsSignature **signature) {
    return This->lpVtbl->Sign(This,signOptions,x509Certificate,signature);
}
static inline HRESULT IXpsSignatureManager_GetSignatureOriginPartName(IXpsSignatureManager* This,IOpcPartUri **signatureOriginPartName) {
    return This->lpVtbl->GetSignatureOriginPartName(This,signatureOriginPartName);
}
static inline HRESULT IXpsSignatureManager_SetSignatureOriginPartName(IXpsSignatureManager* This,IOpcPartUri *signatureOriginPartName) {
    return This->lpVtbl->SetSignatureOriginPartName(This,signatureOriginPartName);
}
static inline HRESULT IXpsSignatureManager_GetSignatures(IXpsSignatureManager* This,IXpsSignatureCollection **signatures) {
    return This->lpVtbl->GetSignatures(This,signatures);
}
static inline HRESULT IXpsSignatureManager_AddSignatureBlock(IXpsSignatureManager* This,IOpcPartUri *partName,UINT32 fixedDocumentIndex,IXpsSignatureBlock **signatureBlock) {
    return This->lpVtbl->AddSignatureBlock(This,partName,fixedDocumentIndex,signatureBlock);
}
static inline HRESULT IXpsSignatureManager_GetSignatureBlocks(IXpsSignatureManager* This,IXpsSignatureBlockCollection **signatureBlocks) {
    return This->lpVtbl->GetSignatureBlocks(This,signatureBlocks);
}
static inline HRESULT IXpsSignatureManager_CreateSigningOptions(IXpsSignatureManager* This,IXpsSigningOptions **signingOptions) {
    return This->lpVtbl->CreateSigningOptions(This,signingOptions);
}
static inline HRESULT IXpsSignatureManager_SavePackageToFile(IXpsSignatureManager* This,LPCWSTR fileName,LPSECURITY_ATTRIBUTES securityAttributes,DWORD flagsAndAttributes) {
    return This->lpVtbl->SavePackageToFile(This,fileName,securityAttributes,flagsAndAttributes);
}
static inline HRESULT IXpsSignatureManager_SavePackageToStream(IXpsSignatureManager* This,IStream *stream) {
    return This->lpVtbl->SavePackageToStream(This,stream);
}
#endif
#endif

#endif


#endif  /* __IXpsSignatureManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsSignatureRequest interface
 */
#ifndef __IXpsSignatureRequest_INTERFACE_DEFINED__
#define __IXpsSignatureRequest_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsSignatureRequest, 0xac58950b, 0x7208, 0x4b2d, 0xb2,0xc4, 0x95,0x10,0x83,0xd3,0xb8,0xeb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ac58950b-7208-4b2d-b2c4-951083d3b8eb")
IXpsSignatureRequest : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetIntent(
        LPWSTR *intent) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIntent(
        LPCWSTR intent) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRequestedSigner(
        LPWSTR *signerName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRequestedSigner(
        LPCWSTR signerName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRequestSignByDate(
        LPWSTR *dateString) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRequestSignByDate(
        LPCWSTR dateString) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSigningLocale(
        LPWSTR *place) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSigningLocale(
        LPCWSTR place) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSpotLocation(
        INT32 *pageIndex,
        IOpcPartUri **pagePartName,
        float *x,
        float *y) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSpotLocation(
        INT32 pageIndex,
        float x,
        float y) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRequestId(
        LPWSTR *requestId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSignature(
        IXpsSignature **signature) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsSignatureRequest, 0xac58950b, 0x7208, 0x4b2d, 0xb2,0xc4, 0x95,0x10,0x83,0xd3,0xb8,0xeb)
#endif
#else
typedef struct IXpsSignatureRequestVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsSignatureRequest *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsSignatureRequest *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsSignatureRequest *This);

    /*** IXpsSignatureRequest methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIntent)(
        IXpsSignatureRequest *This,
        LPWSTR *intent);

    HRESULT (STDMETHODCALLTYPE *SetIntent)(
        IXpsSignatureRequest *This,
        LPCWSTR intent);

    HRESULT (STDMETHODCALLTYPE *GetRequestedSigner)(
        IXpsSignatureRequest *This,
        LPWSTR *signerName);

    HRESULT (STDMETHODCALLTYPE *SetRequestedSigner)(
        IXpsSignatureRequest *This,
        LPCWSTR signerName);

    HRESULT (STDMETHODCALLTYPE *GetRequestSignByDate)(
        IXpsSignatureRequest *This,
        LPWSTR *dateString);

    HRESULT (STDMETHODCALLTYPE *SetRequestSignByDate)(
        IXpsSignatureRequest *This,
        LPCWSTR dateString);

    HRESULT (STDMETHODCALLTYPE *GetSigningLocale)(
        IXpsSignatureRequest *This,
        LPWSTR *place);

    HRESULT (STDMETHODCALLTYPE *SetSigningLocale)(
        IXpsSignatureRequest *This,
        LPCWSTR place);

    HRESULT (STDMETHODCALLTYPE *GetSpotLocation)(
        IXpsSignatureRequest *This,
        INT32 *pageIndex,
        IOpcPartUri **pagePartName,
        float *x,
        float *y);

    HRESULT (STDMETHODCALLTYPE *SetSpotLocation)(
        IXpsSignatureRequest *This,
        INT32 pageIndex,
        float x,
        float y);

    HRESULT (STDMETHODCALLTYPE *GetRequestId)(
        IXpsSignatureRequest *This,
        LPWSTR *requestId);

    HRESULT (STDMETHODCALLTYPE *GetSignature)(
        IXpsSignatureRequest *This,
        IXpsSignature **signature);

    END_INTERFACE
} IXpsSignatureRequestVtbl;

interface IXpsSignatureRequest {
    CONST_VTBL IXpsSignatureRequestVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsSignatureRequest_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsSignatureRequest_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsSignatureRequest_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsSignatureRequest methods ***/
#define IXpsSignatureRequest_GetIntent(This,intent) (This)->lpVtbl->GetIntent(This,intent)
#define IXpsSignatureRequest_SetIntent(This,intent) (This)->lpVtbl->SetIntent(This,intent)
#define IXpsSignatureRequest_GetRequestedSigner(This,signerName) (This)->lpVtbl->GetRequestedSigner(This,signerName)
#define IXpsSignatureRequest_SetRequestedSigner(This,signerName) (This)->lpVtbl->SetRequestedSigner(This,signerName)
#define IXpsSignatureRequest_GetRequestSignByDate(This,dateString) (This)->lpVtbl->GetRequestSignByDate(This,dateString)
#define IXpsSignatureRequest_SetRequestSignByDate(This,dateString) (This)->lpVtbl->SetRequestSignByDate(This,dateString)
#define IXpsSignatureRequest_GetSigningLocale(This,place) (This)->lpVtbl->GetSigningLocale(This,place)
#define IXpsSignatureRequest_SetSigningLocale(This,place) (This)->lpVtbl->SetSigningLocale(This,place)
#define IXpsSignatureRequest_GetSpotLocation(This,pageIndex,pagePartName,x,y) (This)->lpVtbl->GetSpotLocation(This,pageIndex,pagePartName,x,y)
#define IXpsSignatureRequest_SetSpotLocation(This,pageIndex,x,y) (This)->lpVtbl->SetSpotLocation(This,pageIndex,x,y)
#define IXpsSignatureRequest_GetRequestId(This,requestId) (This)->lpVtbl->GetRequestId(This,requestId)
#define IXpsSignatureRequest_GetSignature(This,signature) (This)->lpVtbl->GetSignature(This,signature)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsSignatureRequest_QueryInterface(IXpsSignatureRequest* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsSignatureRequest_AddRef(IXpsSignatureRequest* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsSignatureRequest_Release(IXpsSignatureRequest* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsSignatureRequest methods ***/
static inline HRESULT IXpsSignatureRequest_GetIntent(IXpsSignatureRequest* This,LPWSTR *intent) {
    return This->lpVtbl->GetIntent(This,intent);
}
static inline HRESULT IXpsSignatureRequest_SetIntent(IXpsSignatureRequest* This,LPCWSTR intent) {
    return This->lpVtbl->SetIntent(This,intent);
}
static inline HRESULT IXpsSignatureRequest_GetRequestedSigner(IXpsSignatureRequest* This,LPWSTR *signerName) {
    return This->lpVtbl->GetRequestedSigner(This,signerName);
}
static inline HRESULT IXpsSignatureRequest_SetRequestedSigner(IXpsSignatureRequest* This,LPCWSTR signerName) {
    return This->lpVtbl->SetRequestedSigner(This,signerName);
}
static inline HRESULT IXpsSignatureRequest_GetRequestSignByDate(IXpsSignatureRequest* This,LPWSTR *dateString) {
    return This->lpVtbl->GetRequestSignByDate(This,dateString);
}
static inline HRESULT IXpsSignatureRequest_SetRequestSignByDate(IXpsSignatureRequest* This,LPCWSTR dateString) {
    return This->lpVtbl->SetRequestSignByDate(This,dateString);
}
static inline HRESULT IXpsSignatureRequest_GetSigningLocale(IXpsSignatureRequest* This,LPWSTR *place) {
    return This->lpVtbl->GetSigningLocale(This,place);
}
static inline HRESULT IXpsSignatureRequest_SetSigningLocale(IXpsSignatureRequest* This,LPCWSTR place) {
    return This->lpVtbl->SetSigningLocale(This,place);
}
static inline HRESULT IXpsSignatureRequest_GetSpotLocation(IXpsSignatureRequest* This,INT32 *pageIndex,IOpcPartUri **pagePartName,float *x,float *y) {
    return This->lpVtbl->GetSpotLocation(This,pageIndex,pagePartName,x,y);
}
static inline HRESULT IXpsSignatureRequest_SetSpotLocation(IXpsSignatureRequest* This,INT32 pageIndex,float x,float y) {
    return This->lpVtbl->SetSpotLocation(This,pageIndex,x,y);
}
static inline HRESULT IXpsSignatureRequest_GetRequestId(IXpsSignatureRequest* This,LPWSTR *requestId) {
    return This->lpVtbl->GetRequestId(This,requestId);
}
static inline HRESULT IXpsSignatureRequest_GetSignature(IXpsSignatureRequest* This,IXpsSignature **signature) {
    return This->lpVtbl->GetSignature(This,signature);
}
#endif
#endif

#endif


#endif  /* __IXpsSignatureRequest_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IXpsSignatureRequestCollection interface
 */
#ifndef __IXpsSignatureRequestCollection_INTERFACE_DEFINED__
#define __IXpsSignatureRequestCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IXpsSignatureRequestCollection, 0xf0253e68, 0x9f19, 0x412e, 0x9b,0x4f, 0x54,0xd3,0xb0,0xac,0x6c,0xd9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f0253e68-9f19-412e-9b4f-54d3b0ac6cd9")
IXpsSignatureRequestCollection : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCount(
        UINT32 *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAt(
        UINT32 index,
        IXpsSignatureRequest **signatureRequest) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAt(
        UINT32 index) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IXpsSignatureRequestCollection, 0xf0253e68, 0x9f19, 0x412e, 0x9b,0x4f, 0x54,0xd3,0xb0,0xac,0x6c,0xd9)
#endif
#else
typedef struct IXpsSignatureRequestCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IXpsSignatureRequestCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IXpsSignatureRequestCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IXpsSignatureRequestCollection *This);

    /*** IXpsSignatureRequestCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCount)(
        IXpsSignatureRequestCollection *This,
        UINT32 *count);

    HRESULT (STDMETHODCALLTYPE *GetAt)(
        IXpsSignatureRequestCollection *This,
        UINT32 index,
        IXpsSignatureRequest **signatureRequest);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        IXpsSignatureRequestCollection *This,
        UINT32 index);

    END_INTERFACE
} IXpsSignatureRequestCollectionVtbl;

interface IXpsSignatureRequestCollection {
    CONST_VTBL IXpsSignatureRequestCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IXpsSignatureRequestCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IXpsSignatureRequestCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IXpsSignatureRequestCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IXpsSignatureRequestCollection methods ***/
#define IXpsSignatureRequestCollection_GetCount(This,count) (This)->lpVtbl->GetCount(This,count)
#define IXpsSignatureRequestCollection_GetAt(This,index,signatureRequest) (This)->lpVtbl->GetAt(This,index,signatureRequest)
#define IXpsSignatureRequestCollection_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#else
/*** IUnknown methods ***/
static inline HRESULT IXpsSignatureRequestCollection_QueryInterface(IXpsSignatureRequestCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IXpsSignatureRequestCollection_AddRef(IXpsSignatureRequestCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IXpsSignatureRequestCollection_Release(IXpsSignatureRequestCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IXpsSignatureRequestCollection methods ***/
static inline HRESULT IXpsSignatureRequestCollection_GetCount(IXpsSignatureRequestCollection* This,UINT32 *count) {
    return This->lpVtbl->GetCount(This,count);
}
static inline HRESULT IXpsSignatureRequestCollection_GetAt(IXpsSignatureRequestCollection* This,UINT32 index,IXpsSignatureRequest **signatureRequest) {
    return This->lpVtbl->GetAt(This,index,signatureRequest);
}
static inline HRESULT IXpsSignatureRequestCollection_RemoveAt(IXpsSignatureRequestCollection* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
#endif
#endif

#endif


#endif  /* __IXpsSignatureRequestCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * XpsSignatureManager coclass
 */

DEFINE_GUID(CLSID_XpsSignatureManager, 0xb0c43320, 0x2315, 0x44a2, 0xb7,0x0a, 0x09,0x43,0xa1,0x40,0xa8,0xee);

#ifdef __cplusplus
class DECLSPEC_UUID("b0c43320-2315-44a2-b70a-0943a140a8ee") XpsSignatureManager;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(XpsSignatureManager, 0xb0c43320, 0x2315, 0x44a2, 0xb7,0x0a, 0x09,0x43,0xa1,0x40,0xa8,0xee)
#endif
#endif

#endif /* __MSXPSSIG_LIBRARY_DEFINED__ */
#endif
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __xpsdigitalsignature_h__ */
