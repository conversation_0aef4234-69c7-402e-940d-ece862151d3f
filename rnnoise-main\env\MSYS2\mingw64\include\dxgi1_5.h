/*** Autogenerated by WIDL 10.12 from include/dxgi1_5.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dxgi1_5_h__
#define __dxgi1_5_h__

/* Forward declarations */

#ifndef __IDXGIOutput5_FWD_DEFINED__
#define __IDXGIOutput5_FWD_DEFINED__
typedef interface IDXGIOutput5 IDXGIOutput5;
#ifdef __cplusplus
interface IDXGIOutput5;
#endif /* __cplusplus */
#endif

#ifndef __IDXGISwapChain4_FWD_DEFINED__
#define __IDXGISwap<PERSON>hain4_FWD_DEFINED__
typedef interface IDXGISwapChain4 IDXGISwapChain4;
#ifdef __cplusplus
interface IDXGISwapChain4;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIDevice4_FWD_DEFINED__
#define __IDXGIDevice4_FWD_DEFINED__
typedef interface IDXGIDevice4 IDXGIDevice4;
#ifdef __cplusplus
interface IDXGIDevice4;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIFactory5_FWD_DEFINED__
#define __IDXGIFactory5_FWD_DEFINED__
typedef interface IDXGIFactory5 IDXGIFactory5;
#ifdef __cplusplus
interface IDXGIFactory5;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <dxgi1_4.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum DXGI_OUTDUPL_FLAG {
    DXGI_OUTDUPL_COMPOSITED_UI_CAPTURE_ONLY = 0x1
} DXGI_OUTDUPL_FLAG;
typedef enum DXGI_HDR_METADATA_TYPE {
    DXGI_HDR_METADATA_TYPE_NONE = 0x0,
    DXGI_HDR_METADATA_TYPE_HDR10 = 0x1,
    DXGI_HDR_METADATA_TYPE_HDR10PLUS = 0x2
} DXGI_HDR_METADATA_TYPE;
typedef enum _DXGI_OFFER_RESOURCE_FLAGS {
    DXGI_OFFER_RESOURCE_FLAG_ALLOW_DECOMMIT = 0x1
} DXGI_OFFER_RESOURCE_FLAGS;
typedef enum _DXGI_RECLAIM_RESOURCE_RESULTS {
    DXGI_RECLAIM_RESOURCE_RESULT_OK = 0x0,
    DXGI_RECLAIM_RESOURCE_RESULT_DISCARDED = 0x1,
    DXGI_RECLAIM_RESOURCE_RESULT_NOT_COMMITTED = 0x2
} DXGI_RECLAIM_RESOURCE_RESULTS;
typedef enum DXGI_FEATURE {
    DXGI_FEATURE_PRESENT_ALLOW_TEARING = 0x0
} DXGI_FEATURE;
typedef struct DXGI_HDR_METADATA_HDR10 {
    UINT16 RedPrimary[2];
    UINT16 GreenPrimary[2];
    UINT16 BluePrimary[2];
    UINT16 WhitePoint[2];
    UINT MaxMasteringLuminance;
    UINT MinMasteringLuminance;
    UINT16 MaxContentLightLevel;
    UINT16 MaxFrameAverageLightLevel;
} DXGI_HDR_METADATA_HDR10;
typedef struct DXGI_HDR_METADATA_HDR10PLUS {
    BYTE Data[72];
} DXGI_HDR_METADATA_HDR10PLUS;
/*****************************************************************************
 * IDXGIOutput5 interface
 */
#ifndef __IDXGIOutput5_INTERFACE_DEFINED__
#define __IDXGIOutput5_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIOutput5, 0x80a07424, 0xab52, 0x42eb, 0x83,0x3c, 0x0c,0x42,0xfd,0x28,0x2d,0x98);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("80a07424-ab52-42eb-833c-0c42fd282d98")
IDXGIOutput5 : public IDXGIOutput4
{
    virtual HRESULT STDMETHODCALLTYPE DuplicateOutput1(
        IUnknown *device,
        UINT flags,
        UINT format_count,
        const DXGI_FORMAT *formats,
        IDXGIOutputDuplication **duplication) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIOutput5, 0x80a07424, 0xab52, 0x42eb, 0x83,0x3c, 0x0c,0x42,0xfd,0x28,0x2d,0x98)
#endif
#else
typedef struct IDXGIOutput5Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIOutput5 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIOutput5 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIOutput5 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIOutput5 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIOutput5 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIOutput5 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIOutput5 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIOutput methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIOutput5 *This,
        DXGI_OUTPUT_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList)(
        IDXGIOutput5 *This,
        DXGI_FORMAT format,
        UINT flags,
        UINT *mode_count,
        DXGI_MODE_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode)(
        IDXGIOutput5 *This,
        const DXGI_MODE_DESC *mode,
        DXGI_MODE_DESC *closest_match,
        IUnknown *device);

    HRESULT (STDMETHODCALLTYPE *WaitForVBlank)(
        IDXGIOutput5 *This);

    HRESULT (STDMETHODCALLTYPE *TakeOwnership)(
        IDXGIOutput5 *This,
        IUnknown *device,
        WINBOOL exclusive);

    void (STDMETHODCALLTYPE *ReleaseOwnership)(
        IDXGIOutput5 *This);

    HRESULT (STDMETHODCALLTYPE *GetGammaControlCapabilities)(
        IDXGIOutput5 *This,
        DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps);

    HRESULT (STDMETHODCALLTYPE *SetGammaControl)(
        IDXGIOutput5 *This,
        const DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *GetGammaControl)(
        IDXGIOutput5 *This,
        DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *SetDisplaySurface)(
        IDXGIOutput5 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData)(
        IDXGIOutput5 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGIOutput5 *This,
        DXGI_FRAME_STATISTICS *stats);

    /*** IDXGIOutput1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList1)(
        IDXGIOutput5 *This,
        DXGI_FORMAT enum_format,
        UINT flags,
        UINT *num_modes,
        DXGI_MODE_DESC1 *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode1)(
        IDXGIOutput5 *This,
        const DXGI_MODE_DESC1 *mode_to_match,
        DXGI_MODE_DESC1 *closest_match,
        IUnknown *concerned_device);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData1)(
        IDXGIOutput5 *This,
        IDXGIResource *destination);

    HRESULT (STDMETHODCALLTYPE *DuplicateOutput)(
        IDXGIOutput5 *This,
        IUnknown *device,
        IDXGIOutputDuplication **output_duplication);

    /*** IDXGIOutput2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *SupportsOverlays)(
        IDXGIOutput5 *This);

    /*** IDXGIOutput3 methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckOverlaySupport)(
        IDXGIOutput5 *This,
        DXGI_FORMAT enum_format,
        IUnknown *concerned_device,
        UINT *flags);

    /*** IDXGIOutput4 methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckOverlayColorSpaceSupport)(
        IDXGIOutput5 *This,
        DXGI_FORMAT format,
        DXGI_COLOR_SPACE_TYPE colour_space,
        IUnknown *device,
        UINT *flags);

    /*** IDXGIOutput5 methods ***/
    HRESULT (STDMETHODCALLTYPE *DuplicateOutput1)(
        IDXGIOutput5 *This,
        IUnknown *device,
        UINT flags,
        UINT format_count,
        const DXGI_FORMAT *formats,
        IDXGIOutputDuplication **duplication);

    END_INTERFACE
} IDXGIOutput5Vtbl;

interface IDXGIOutput5 {
    CONST_VTBL IDXGIOutput5Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIOutput5_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIOutput5_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIOutput5_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIOutput5_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIOutput5_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIOutput5_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIOutput5_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIOutput methods ***/
#define IDXGIOutput5_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIOutput5_GetDisplayModeList(This,format,flags,mode_count,desc) (This)->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc)
#define IDXGIOutput5_FindClosestMatchingMode(This,mode,closest_match,device) (This)->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device)
#define IDXGIOutput5_WaitForVBlank(This) (This)->lpVtbl->WaitForVBlank(This)
#define IDXGIOutput5_TakeOwnership(This,device,exclusive) (This)->lpVtbl->TakeOwnership(This,device,exclusive)
#define IDXGIOutput5_ReleaseOwnership(This) (This)->lpVtbl->ReleaseOwnership(This)
#define IDXGIOutput5_GetGammaControlCapabilities(This,gamma_caps) (This)->lpVtbl->GetGammaControlCapabilities(This,gamma_caps)
#define IDXGIOutput5_SetGammaControl(This,gamma_control) (This)->lpVtbl->SetGammaControl(This,gamma_control)
#define IDXGIOutput5_GetGammaControl(This,gamma_control) (This)->lpVtbl->GetGammaControl(This,gamma_control)
#define IDXGIOutput5_SetDisplaySurface(This,surface) (This)->lpVtbl->SetDisplaySurface(This,surface)
#define IDXGIOutput5_GetDisplaySurfaceData(This,surface) (This)->lpVtbl->GetDisplaySurfaceData(This,surface)
#define IDXGIOutput5_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
/*** IDXGIOutput1 methods ***/
#define IDXGIOutput5_GetDisplayModeList1(This,enum_format,flags,num_modes,desc) (This)->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc)
#define IDXGIOutput5_FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device) (This)->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device)
#define IDXGIOutput5_GetDisplaySurfaceData1(This,destination) (This)->lpVtbl->GetDisplaySurfaceData1(This,destination)
#define IDXGIOutput5_DuplicateOutput(This,device,output_duplication) (This)->lpVtbl->DuplicateOutput(This,device,output_duplication)
/*** IDXGIOutput2 methods ***/
#define IDXGIOutput5_SupportsOverlays(This) (This)->lpVtbl->SupportsOverlays(This)
/*** IDXGIOutput3 methods ***/
#define IDXGIOutput5_CheckOverlaySupport(This,enum_format,concerned_device,flags) (This)->lpVtbl->CheckOverlaySupport(This,enum_format,concerned_device,flags)
/*** IDXGIOutput4 methods ***/
#define IDXGIOutput5_CheckOverlayColorSpaceSupport(This,format,colour_space,device,flags) (This)->lpVtbl->CheckOverlayColorSpaceSupport(This,format,colour_space,device,flags)
/*** IDXGIOutput5 methods ***/
#define IDXGIOutput5_DuplicateOutput1(This,device,flags,format_count,formats,duplication) (This)->lpVtbl->DuplicateOutput1(This,device,flags,format_count,formats,duplication)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIOutput5_QueryInterface(IDXGIOutput5* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIOutput5_AddRef(IDXGIOutput5* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIOutput5_Release(IDXGIOutput5* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIOutput5_SetPrivateData(IDXGIOutput5* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput5_SetPrivateDataInterface(IDXGIOutput5* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIOutput5_GetPrivateData(IDXGIOutput5* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput5_GetParent(IDXGIOutput5* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIOutput methods ***/
static inline HRESULT IDXGIOutput5_GetDesc(IDXGIOutput5* This,DXGI_OUTPUT_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGIOutput5_GetDisplayModeList(IDXGIOutput5* This,DXGI_FORMAT format,UINT flags,UINT *mode_count,DXGI_MODE_DESC *desc) {
    return This->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc);
}
static inline HRESULT IDXGIOutput5_FindClosestMatchingMode(IDXGIOutput5* This,const DXGI_MODE_DESC *mode,DXGI_MODE_DESC *closest_match,IUnknown *device) {
    return This->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device);
}
static inline HRESULT IDXGIOutput5_WaitForVBlank(IDXGIOutput5* This) {
    return This->lpVtbl->WaitForVBlank(This);
}
static inline HRESULT IDXGIOutput5_TakeOwnership(IDXGIOutput5* This,IUnknown *device,WINBOOL exclusive) {
    return This->lpVtbl->TakeOwnership(This,device,exclusive);
}
static inline void IDXGIOutput5_ReleaseOwnership(IDXGIOutput5* This) {
    This->lpVtbl->ReleaseOwnership(This);
}
static inline HRESULT IDXGIOutput5_GetGammaControlCapabilities(IDXGIOutput5* This,DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps) {
    return This->lpVtbl->GetGammaControlCapabilities(This,gamma_caps);
}
static inline HRESULT IDXGIOutput5_SetGammaControl(IDXGIOutput5* This,const DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->SetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput5_GetGammaControl(IDXGIOutput5* This,DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->GetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput5_SetDisplaySurface(IDXGIOutput5* This,IDXGISurface *surface) {
    return This->lpVtbl->SetDisplaySurface(This,surface);
}
static inline HRESULT IDXGIOutput5_GetDisplaySurfaceData(IDXGIOutput5* This,IDXGISurface *surface) {
    return This->lpVtbl->GetDisplaySurfaceData(This,surface);
}
static inline HRESULT IDXGIOutput5_GetFrameStatistics(IDXGIOutput5* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
/*** IDXGIOutput1 methods ***/
static inline HRESULT IDXGIOutput5_GetDisplayModeList1(IDXGIOutput5* This,DXGI_FORMAT enum_format,UINT flags,UINT *num_modes,DXGI_MODE_DESC1 *desc) {
    return This->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc);
}
static inline HRESULT IDXGIOutput5_FindClosestMatchingMode1(IDXGIOutput5* This,const DXGI_MODE_DESC1 *mode_to_match,DXGI_MODE_DESC1 *closest_match,IUnknown *concerned_device) {
    return This->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device);
}
static inline HRESULT IDXGIOutput5_GetDisplaySurfaceData1(IDXGIOutput5* This,IDXGIResource *destination) {
    return This->lpVtbl->GetDisplaySurfaceData1(This,destination);
}
static inline HRESULT IDXGIOutput5_DuplicateOutput(IDXGIOutput5* This,IUnknown *device,IDXGIOutputDuplication **output_duplication) {
    return This->lpVtbl->DuplicateOutput(This,device,output_duplication);
}
/*** IDXGIOutput2 methods ***/
static inline WINBOOL IDXGIOutput5_SupportsOverlays(IDXGIOutput5* This) {
    return This->lpVtbl->SupportsOverlays(This);
}
/*** IDXGIOutput3 methods ***/
static inline HRESULT IDXGIOutput5_CheckOverlaySupport(IDXGIOutput5* This,DXGI_FORMAT enum_format,IUnknown *concerned_device,UINT *flags) {
    return This->lpVtbl->CheckOverlaySupport(This,enum_format,concerned_device,flags);
}
/*** IDXGIOutput4 methods ***/
static inline HRESULT IDXGIOutput5_CheckOverlayColorSpaceSupport(IDXGIOutput5* This,DXGI_FORMAT format,DXGI_COLOR_SPACE_TYPE colour_space,IUnknown *device,UINT *flags) {
    return This->lpVtbl->CheckOverlayColorSpaceSupport(This,format,colour_space,device,flags);
}
/*** IDXGIOutput5 methods ***/
static inline HRESULT IDXGIOutput5_DuplicateOutput1(IDXGIOutput5* This,IUnknown *device,UINT flags,UINT format_count,const DXGI_FORMAT *formats,IDXGIOutputDuplication **duplication) {
    return This->lpVtbl->DuplicateOutput1(This,device,flags,format_count,formats,duplication);
}
#endif
#endif

#endif


#endif  /* __IDXGIOutput5_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGISwapChain4 interface
 */
#ifndef __IDXGISwapChain4_INTERFACE_DEFINED__
#define __IDXGISwapChain4_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGISwapChain4, 0x3d585d5a, 0xbd4a, 0x489e, 0xb1,0xf4, 0x3d,0xbc,0xb6,0x45,0x2f,0xfb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3d585d5a-bd4a-489e-b1f4-3dbcb6452ffb")
IDXGISwapChain4 : public IDXGISwapChain3
{
    virtual HRESULT STDMETHODCALLTYPE SetHDRMetaData(
        DXGI_HDR_METADATA_TYPE type,
        UINT size,
        void *metadata) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGISwapChain4, 0x3d585d5a, 0xbd4a, 0x489e, 0xb1,0xf4, 0x3d,0xbc,0xb6,0x45,0x2f,0xfb)
#endif
#else
typedef struct IDXGISwapChain4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGISwapChain4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGISwapChain4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGISwapChain4 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGISwapChain4 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGISwapChain4 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGISwapChain4 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGISwapChain4 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGISwapChain4 *This,
        REFIID riid,
        void **device);

    /*** IDXGISwapChain methods ***/
    HRESULT (STDMETHODCALLTYPE *Present)(
        IDXGISwapChain4 *This,
        UINT sync_interval,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IDXGISwapChain4 *This,
        UINT buffer_idx,
        REFIID riid,
        void **surface);

    HRESULT (STDMETHODCALLTYPE *SetFullscreenState)(
        IDXGISwapChain4 *This,
        WINBOOL fullscreen,
        IDXGIOutput *target);

    HRESULT (STDMETHODCALLTYPE *GetFullscreenState)(
        IDXGISwapChain4 *This,
        WINBOOL *fullscreen,
        IDXGIOutput **target);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGISwapChain4 *This,
        DXGI_SWAP_CHAIN_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *ResizeBuffers)(
        IDXGISwapChain4 *This,
        UINT buffer_count,
        UINT width,
        UINT height,
        DXGI_FORMAT format,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *ResizeTarget)(
        IDXGISwapChain4 *This,
        const DXGI_MODE_DESC *target_mode_desc);

    HRESULT (STDMETHODCALLTYPE *GetContainingOutput)(
        IDXGISwapChain4 *This,
        IDXGIOutput **output);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGISwapChain4 *This,
        DXGI_FRAME_STATISTICS *stats);

    HRESULT (STDMETHODCALLTYPE *GetLastPresentCount)(
        IDXGISwapChain4 *This,
        UINT *last_present_count);

    /*** IDXGISwapChain1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc1)(
        IDXGISwapChain4 *This,
        DXGI_SWAP_CHAIN_DESC1 *pDesc);

    HRESULT (STDMETHODCALLTYPE *GetFullscreenDesc)(
        IDXGISwapChain4 *This,
        DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pDesc);

    HRESULT (STDMETHODCALLTYPE *GetHwnd)(
        IDXGISwapChain4 *This,
        HWND *pHwnd);

    HRESULT (STDMETHODCALLTYPE *GetCoreWindow)(
        IDXGISwapChain4 *This,
        REFIID refiid,
        void **ppUnk);

    HRESULT (STDMETHODCALLTYPE *Present1)(
        IDXGISwapChain4 *This,
        UINT SyncInterval,
        UINT PresentFlags,
        const DXGI_PRESENT_PARAMETERS *pPresentParameters);

    WINBOOL (STDMETHODCALLTYPE *IsTemporaryMonoSupported)(
        IDXGISwapChain4 *This);

    HRESULT (STDMETHODCALLTYPE *GetRestrictToOutput)(
        IDXGISwapChain4 *This,
        IDXGIOutput **ppRestrictToOutput);

    HRESULT (STDMETHODCALLTYPE *SetBackgroundColor)(
        IDXGISwapChain4 *This,
        const DXGI_RGBA *pColor);

    HRESULT (STDMETHODCALLTYPE *GetBackgroundColor)(
        IDXGISwapChain4 *This,
        DXGI_RGBA *pColor);

    HRESULT (STDMETHODCALLTYPE *SetRotation)(
        IDXGISwapChain4 *This,
        DXGI_MODE_ROTATION Rotation);

    HRESULT (STDMETHODCALLTYPE *GetRotation)(
        IDXGISwapChain4 *This,
        DXGI_MODE_ROTATION *pRotation);

    /*** IDXGISwapChain2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSourceSize)(
        IDXGISwapChain4 *This,
        UINT width,
        UINT height);

    HRESULT (STDMETHODCALLTYPE *GetSourceSize)(
        IDXGISwapChain4 *This,
        UINT *width,
        UINT *height);

    HRESULT (STDMETHODCALLTYPE *SetMaximumFrameLatency)(
        IDXGISwapChain4 *This,
        UINT max_latency);

    HRESULT (STDMETHODCALLTYPE *GetMaximumFrameLatency)(
        IDXGISwapChain4 *This,
        UINT *max_latency);

    HANDLE (STDMETHODCALLTYPE *GetFrameLatencyWaitableObject)(
        IDXGISwapChain4 *This);

    HRESULT (STDMETHODCALLTYPE *SetMatrixTransform)(
        IDXGISwapChain4 *This,
        const DXGI_MATRIX_3X2_F *matrix);

    HRESULT (STDMETHODCALLTYPE *GetMatrixTransform)(
        IDXGISwapChain4 *This,
        DXGI_MATRIX_3X2_F *matrix);

    /*** IDXGISwapChain3 methods ***/
    UINT (STDMETHODCALLTYPE *GetCurrentBackBufferIndex)(
        IDXGISwapChain4 *This);

    HRESULT (STDMETHODCALLTYPE *CheckColorSpaceSupport)(
        IDXGISwapChain4 *This,
        DXGI_COLOR_SPACE_TYPE colour_space,
        UINT *colour_space_support);

    HRESULT (STDMETHODCALLTYPE *SetColorSpace1)(
        IDXGISwapChain4 *This,
        DXGI_COLOR_SPACE_TYPE colour_space);

    HRESULT (STDMETHODCALLTYPE *ResizeBuffers1)(
        IDXGISwapChain4 *This,
        UINT buffer_count,
        UINT width,
        UINT height,
        DXGI_FORMAT format,
        UINT flags,
        const UINT *node_mask,
        IUnknown *const *present_queue);

    /*** IDXGISwapChain4 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetHDRMetaData)(
        IDXGISwapChain4 *This,
        DXGI_HDR_METADATA_TYPE type,
        UINT size,
        void *metadata);

    END_INTERFACE
} IDXGISwapChain4Vtbl;

interface IDXGISwapChain4 {
    CONST_VTBL IDXGISwapChain4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGISwapChain4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGISwapChain4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGISwapChain4_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGISwapChain4_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGISwapChain4_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGISwapChain4_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGISwapChain4_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGISwapChain4_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** IDXGISwapChain methods ***/
#define IDXGISwapChain4_Present(This,sync_interval,flags) (This)->lpVtbl->Present(This,sync_interval,flags)
#define IDXGISwapChain4_GetBuffer(This,buffer_idx,riid,surface) (This)->lpVtbl->GetBuffer(This,buffer_idx,riid,surface)
#define IDXGISwapChain4_SetFullscreenState(This,fullscreen,target) (This)->lpVtbl->SetFullscreenState(This,fullscreen,target)
#define IDXGISwapChain4_GetFullscreenState(This,fullscreen,target) (This)->lpVtbl->GetFullscreenState(This,fullscreen,target)
#define IDXGISwapChain4_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGISwapChain4_ResizeBuffers(This,buffer_count,width,height,format,flags) (This)->lpVtbl->ResizeBuffers(This,buffer_count,width,height,format,flags)
#define IDXGISwapChain4_ResizeTarget(This,target_mode_desc) (This)->lpVtbl->ResizeTarget(This,target_mode_desc)
#define IDXGISwapChain4_GetContainingOutput(This,output) (This)->lpVtbl->GetContainingOutput(This,output)
#define IDXGISwapChain4_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
#define IDXGISwapChain4_GetLastPresentCount(This,last_present_count) (This)->lpVtbl->GetLastPresentCount(This,last_present_count)
/*** IDXGISwapChain1 methods ***/
#define IDXGISwapChain4_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
#define IDXGISwapChain4_GetFullscreenDesc(This,pDesc) (This)->lpVtbl->GetFullscreenDesc(This,pDesc)
#define IDXGISwapChain4_GetHwnd(This,pHwnd) (This)->lpVtbl->GetHwnd(This,pHwnd)
#define IDXGISwapChain4_GetCoreWindow(This,refiid,ppUnk) (This)->lpVtbl->GetCoreWindow(This,refiid,ppUnk)
#define IDXGISwapChain4_Present1(This,SyncInterval,PresentFlags,pPresentParameters) (This)->lpVtbl->Present1(This,SyncInterval,PresentFlags,pPresentParameters)
#define IDXGISwapChain4_IsTemporaryMonoSupported(This) (This)->lpVtbl->IsTemporaryMonoSupported(This)
#define IDXGISwapChain4_GetRestrictToOutput(This,ppRestrictToOutput) (This)->lpVtbl->GetRestrictToOutput(This,ppRestrictToOutput)
#define IDXGISwapChain4_SetBackgroundColor(This,pColor) (This)->lpVtbl->SetBackgroundColor(This,pColor)
#define IDXGISwapChain4_GetBackgroundColor(This,pColor) (This)->lpVtbl->GetBackgroundColor(This,pColor)
#define IDXGISwapChain4_SetRotation(This,Rotation) (This)->lpVtbl->SetRotation(This,Rotation)
#define IDXGISwapChain4_GetRotation(This,pRotation) (This)->lpVtbl->GetRotation(This,pRotation)
/*** IDXGISwapChain2 methods ***/
#define IDXGISwapChain4_SetSourceSize(This,width,height) (This)->lpVtbl->SetSourceSize(This,width,height)
#define IDXGISwapChain4_GetSourceSize(This,width,height) (This)->lpVtbl->GetSourceSize(This,width,height)
#define IDXGISwapChain4_SetMaximumFrameLatency(This,max_latency) (This)->lpVtbl->SetMaximumFrameLatency(This,max_latency)
#define IDXGISwapChain4_GetMaximumFrameLatency(This,max_latency) (This)->lpVtbl->GetMaximumFrameLatency(This,max_latency)
#define IDXGISwapChain4_GetFrameLatencyWaitableObject(This) (This)->lpVtbl->GetFrameLatencyWaitableObject(This)
#define IDXGISwapChain4_SetMatrixTransform(This,matrix) (This)->lpVtbl->SetMatrixTransform(This,matrix)
#define IDXGISwapChain4_GetMatrixTransform(This,matrix) (This)->lpVtbl->GetMatrixTransform(This,matrix)
/*** IDXGISwapChain3 methods ***/
#define IDXGISwapChain4_GetCurrentBackBufferIndex(This) (This)->lpVtbl->GetCurrentBackBufferIndex(This)
#define IDXGISwapChain4_CheckColorSpaceSupport(This,colour_space,colour_space_support) (This)->lpVtbl->CheckColorSpaceSupport(This,colour_space,colour_space_support)
#define IDXGISwapChain4_SetColorSpace1(This,colour_space) (This)->lpVtbl->SetColorSpace1(This,colour_space)
#define IDXGISwapChain4_ResizeBuffers1(This,buffer_count,width,height,format,flags,node_mask,present_queue) (This)->lpVtbl->ResizeBuffers1(This,buffer_count,width,height,format,flags,node_mask,present_queue)
/*** IDXGISwapChain4 methods ***/
#define IDXGISwapChain4_SetHDRMetaData(This,type,size,metadata) (This)->lpVtbl->SetHDRMetaData(This,type,size,metadata)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGISwapChain4_QueryInterface(IDXGISwapChain4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGISwapChain4_AddRef(IDXGISwapChain4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGISwapChain4_Release(IDXGISwapChain4* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGISwapChain4_SetPrivateData(IDXGISwapChain4* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISwapChain4_SetPrivateDataInterface(IDXGISwapChain4* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGISwapChain4_GetPrivateData(IDXGISwapChain4* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISwapChain4_GetParent(IDXGISwapChain4* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static inline HRESULT IDXGISwapChain4_GetDevice(IDXGISwapChain4* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** IDXGISwapChain methods ***/
static inline HRESULT IDXGISwapChain4_Present(IDXGISwapChain4* This,UINT sync_interval,UINT flags) {
    return This->lpVtbl->Present(This,sync_interval,flags);
}
static inline HRESULT IDXGISwapChain4_GetBuffer(IDXGISwapChain4* This,UINT buffer_idx,REFIID riid,void **surface) {
    return This->lpVtbl->GetBuffer(This,buffer_idx,riid,surface);
}
static inline HRESULT IDXGISwapChain4_SetFullscreenState(IDXGISwapChain4* This,WINBOOL fullscreen,IDXGIOutput *target) {
    return This->lpVtbl->SetFullscreenState(This,fullscreen,target);
}
static inline HRESULT IDXGISwapChain4_GetFullscreenState(IDXGISwapChain4* This,WINBOOL *fullscreen,IDXGIOutput **target) {
    return This->lpVtbl->GetFullscreenState(This,fullscreen,target);
}
static inline HRESULT IDXGISwapChain4_GetDesc(IDXGISwapChain4* This,DXGI_SWAP_CHAIN_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGISwapChain4_ResizeBuffers(IDXGISwapChain4* This,UINT buffer_count,UINT width,UINT height,DXGI_FORMAT format,UINT flags) {
    return This->lpVtbl->ResizeBuffers(This,buffer_count,width,height,format,flags);
}
static inline HRESULT IDXGISwapChain4_ResizeTarget(IDXGISwapChain4* This,const DXGI_MODE_DESC *target_mode_desc) {
    return This->lpVtbl->ResizeTarget(This,target_mode_desc);
}
static inline HRESULT IDXGISwapChain4_GetContainingOutput(IDXGISwapChain4* This,IDXGIOutput **output) {
    return This->lpVtbl->GetContainingOutput(This,output);
}
static inline HRESULT IDXGISwapChain4_GetFrameStatistics(IDXGISwapChain4* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
static inline HRESULT IDXGISwapChain4_GetLastPresentCount(IDXGISwapChain4* This,UINT *last_present_count) {
    return This->lpVtbl->GetLastPresentCount(This,last_present_count);
}
/*** IDXGISwapChain1 methods ***/
static inline HRESULT IDXGISwapChain4_GetDesc1(IDXGISwapChain4* This,DXGI_SWAP_CHAIN_DESC1 *pDesc) {
    return This->lpVtbl->GetDesc1(This,pDesc);
}
static inline HRESULT IDXGISwapChain4_GetFullscreenDesc(IDXGISwapChain4* This,DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pDesc) {
    return This->lpVtbl->GetFullscreenDesc(This,pDesc);
}
static inline HRESULT IDXGISwapChain4_GetHwnd(IDXGISwapChain4* This,HWND *pHwnd) {
    return This->lpVtbl->GetHwnd(This,pHwnd);
}
static inline HRESULT IDXGISwapChain4_GetCoreWindow(IDXGISwapChain4* This,REFIID refiid,void **ppUnk) {
    return This->lpVtbl->GetCoreWindow(This,refiid,ppUnk);
}
static inline HRESULT IDXGISwapChain4_Present1(IDXGISwapChain4* This,UINT SyncInterval,UINT PresentFlags,const DXGI_PRESENT_PARAMETERS *pPresentParameters) {
    return This->lpVtbl->Present1(This,SyncInterval,PresentFlags,pPresentParameters);
}
static inline WINBOOL IDXGISwapChain4_IsTemporaryMonoSupported(IDXGISwapChain4* This) {
    return This->lpVtbl->IsTemporaryMonoSupported(This);
}
static inline HRESULT IDXGISwapChain4_GetRestrictToOutput(IDXGISwapChain4* This,IDXGIOutput **ppRestrictToOutput) {
    return This->lpVtbl->GetRestrictToOutput(This,ppRestrictToOutput);
}
static inline HRESULT IDXGISwapChain4_SetBackgroundColor(IDXGISwapChain4* This,const DXGI_RGBA *pColor) {
    return This->lpVtbl->SetBackgroundColor(This,pColor);
}
static inline HRESULT IDXGISwapChain4_GetBackgroundColor(IDXGISwapChain4* This,DXGI_RGBA *pColor) {
    return This->lpVtbl->GetBackgroundColor(This,pColor);
}
static inline HRESULT IDXGISwapChain4_SetRotation(IDXGISwapChain4* This,DXGI_MODE_ROTATION Rotation) {
    return This->lpVtbl->SetRotation(This,Rotation);
}
static inline HRESULT IDXGISwapChain4_GetRotation(IDXGISwapChain4* This,DXGI_MODE_ROTATION *pRotation) {
    return This->lpVtbl->GetRotation(This,pRotation);
}
/*** IDXGISwapChain2 methods ***/
static inline HRESULT IDXGISwapChain4_SetSourceSize(IDXGISwapChain4* This,UINT width,UINT height) {
    return This->lpVtbl->SetSourceSize(This,width,height);
}
static inline HRESULT IDXGISwapChain4_GetSourceSize(IDXGISwapChain4* This,UINT *width,UINT *height) {
    return This->lpVtbl->GetSourceSize(This,width,height);
}
static inline HRESULT IDXGISwapChain4_SetMaximumFrameLatency(IDXGISwapChain4* This,UINT max_latency) {
    return This->lpVtbl->SetMaximumFrameLatency(This,max_latency);
}
static inline HRESULT IDXGISwapChain4_GetMaximumFrameLatency(IDXGISwapChain4* This,UINT *max_latency) {
    return This->lpVtbl->GetMaximumFrameLatency(This,max_latency);
}
static inline HANDLE IDXGISwapChain4_GetFrameLatencyWaitableObject(IDXGISwapChain4* This) {
    return This->lpVtbl->GetFrameLatencyWaitableObject(This);
}
static inline HRESULT IDXGISwapChain4_SetMatrixTransform(IDXGISwapChain4* This,const DXGI_MATRIX_3X2_F *matrix) {
    return This->lpVtbl->SetMatrixTransform(This,matrix);
}
static inline HRESULT IDXGISwapChain4_GetMatrixTransform(IDXGISwapChain4* This,DXGI_MATRIX_3X2_F *matrix) {
    return This->lpVtbl->GetMatrixTransform(This,matrix);
}
/*** IDXGISwapChain3 methods ***/
static inline UINT IDXGISwapChain4_GetCurrentBackBufferIndex(IDXGISwapChain4* This) {
    return This->lpVtbl->GetCurrentBackBufferIndex(This);
}
static inline HRESULT IDXGISwapChain4_CheckColorSpaceSupport(IDXGISwapChain4* This,DXGI_COLOR_SPACE_TYPE colour_space,UINT *colour_space_support) {
    return This->lpVtbl->CheckColorSpaceSupport(This,colour_space,colour_space_support);
}
static inline HRESULT IDXGISwapChain4_SetColorSpace1(IDXGISwapChain4* This,DXGI_COLOR_SPACE_TYPE colour_space) {
    return This->lpVtbl->SetColorSpace1(This,colour_space);
}
static inline HRESULT IDXGISwapChain4_ResizeBuffers1(IDXGISwapChain4* This,UINT buffer_count,UINT width,UINT height,DXGI_FORMAT format,UINT flags,const UINT *node_mask,IUnknown *const *present_queue) {
    return This->lpVtbl->ResizeBuffers1(This,buffer_count,width,height,format,flags,node_mask,present_queue);
}
/*** IDXGISwapChain4 methods ***/
static inline HRESULT IDXGISwapChain4_SetHDRMetaData(IDXGISwapChain4* This,DXGI_HDR_METADATA_TYPE type,UINT size,void *metadata) {
    return This->lpVtbl->SetHDRMetaData(This,type,size,metadata);
}
#endif
#endif

#endif


#endif  /* __IDXGISwapChain4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIDevice4 interface
 */
#ifndef __IDXGIDevice4_INTERFACE_DEFINED__
#define __IDXGIDevice4_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIDevice4, 0x95b4f95f, 0xd8da, 0x4ca4, 0x9e,0xe6, 0x3b,0x76,0xd5,0x96,0x8a,0x10);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("95b4f95f-d8da-4ca4-9ee6-3b76d5968a10")
IDXGIDevice4 : public IDXGIDevice3
{
    virtual HRESULT STDMETHODCALLTYPE OfferResources1(
        UINT resource_count,
        IDXGIResource *const *resources,
        DXGI_OFFER_RESOURCE_PRIORITY priority,
        UINT flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReclaimResources1(
        UINT resource_count,
        IDXGIResource *const *resources,
        DXGI_RECLAIM_RESOURCE_RESULTS *results) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIDevice4, 0x95b4f95f, 0xd8da, 0x4ca4, 0x9e,0xe6, 0x3b,0x76,0xd5,0x96,0x8a,0x10)
#endif
#else
typedef struct IDXGIDevice4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIDevice4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIDevice4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIDevice4 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIDevice4 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIDevice4 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIDevice4 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIDevice4 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAdapter)(
        IDXGIDevice4 *This,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *CreateSurface)(
        IDXGIDevice4 *This,
        const DXGI_SURFACE_DESC *desc,
        UINT surface_count,
        DXGI_USAGE usage,
        const DXGI_SHARED_RESOURCE *shared_resource,
        IDXGISurface **surface);

    HRESULT (STDMETHODCALLTYPE *QueryResourceResidency)(
        IDXGIDevice4 *This,
        IUnknown *const *resources,
        DXGI_RESIDENCY *residency,
        UINT resource_count);

    HRESULT (STDMETHODCALLTYPE *SetGPUThreadPriority)(
        IDXGIDevice4 *This,
        INT priority);

    HRESULT (STDMETHODCALLTYPE *GetGPUThreadPriority)(
        IDXGIDevice4 *This,
        INT *priority);

    /*** IDXGIDevice1 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMaximumFrameLatency)(
        IDXGIDevice4 *This,
        UINT MaxLatency);

    HRESULT (STDMETHODCALLTYPE *GetMaximumFrameLatency)(
        IDXGIDevice4 *This,
        UINT *pMaxLatency);

    /*** IDXGIDevice2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OfferResources)(
        IDXGIDevice4 *This,
        UINT NumResources,
        IDXGIResource *const *ppResources,
        DXGI_OFFER_RESOURCE_PRIORITY Priority);

    HRESULT (STDMETHODCALLTYPE *ReclaimResources)(
        IDXGIDevice4 *This,
        UINT NumResources,
        IDXGIResource *const *ppResources,
        WINBOOL *pDiscarded);

    HRESULT (STDMETHODCALLTYPE *EnqueueSetEvent)(
        IDXGIDevice4 *This,
        HANDLE hEvent);

    /*** IDXGIDevice3 methods ***/
    void (STDMETHODCALLTYPE *Trim)(
        IDXGIDevice4 *This);

    /*** IDXGIDevice4 methods ***/
    HRESULT (STDMETHODCALLTYPE *OfferResources1)(
        IDXGIDevice4 *This,
        UINT resource_count,
        IDXGIResource *const *resources,
        DXGI_OFFER_RESOURCE_PRIORITY priority,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *ReclaimResources1)(
        IDXGIDevice4 *This,
        UINT resource_count,
        IDXGIResource *const *resources,
        DXGI_RECLAIM_RESOURCE_RESULTS *results);

    END_INTERFACE
} IDXGIDevice4Vtbl;

interface IDXGIDevice4 {
    CONST_VTBL IDXGIDevice4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIDevice4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIDevice4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIDevice4_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIDevice4_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIDevice4_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIDevice4_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIDevice4_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDevice methods ***/
#define IDXGIDevice4_GetAdapter(This,adapter) (This)->lpVtbl->GetAdapter(This,adapter)
#define IDXGIDevice4_CreateSurface(This,desc,surface_count,usage,shared_resource,surface) (This)->lpVtbl->CreateSurface(This,desc,surface_count,usage,shared_resource,surface)
#define IDXGIDevice4_QueryResourceResidency(This,resources,residency,resource_count) (This)->lpVtbl->QueryResourceResidency(This,resources,residency,resource_count)
#define IDXGIDevice4_SetGPUThreadPriority(This,priority) (This)->lpVtbl->SetGPUThreadPriority(This,priority)
#define IDXGIDevice4_GetGPUThreadPriority(This,priority) (This)->lpVtbl->GetGPUThreadPriority(This,priority)
/*** IDXGIDevice1 methods ***/
#define IDXGIDevice4_SetMaximumFrameLatency(This,MaxLatency) (This)->lpVtbl->SetMaximumFrameLatency(This,MaxLatency)
#define IDXGIDevice4_GetMaximumFrameLatency(This,pMaxLatency) (This)->lpVtbl->GetMaximumFrameLatency(This,pMaxLatency)
/*** IDXGIDevice2 methods ***/
#define IDXGIDevice4_OfferResources(This,NumResources,ppResources,Priority) (This)->lpVtbl->OfferResources(This,NumResources,ppResources,Priority)
#define IDXGIDevice4_ReclaimResources(This,NumResources,ppResources,pDiscarded) (This)->lpVtbl->ReclaimResources(This,NumResources,ppResources,pDiscarded)
#define IDXGIDevice4_EnqueueSetEvent(This,hEvent) (This)->lpVtbl->EnqueueSetEvent(This,hEvent)
/*** IDXGIDevice3 methods ***/
#define IDXGIDevice4_Trim(This) (This)->lpVtbl->Trim(This)
/*** IDXGIDevice4 methods ***/
#define IDXGIDevice4_OfferResources1(This,resource_count,resources,priority,flags) (This)->lpVtbl->OfferResources1(This,resource_count,resources,priority,flags)
#define IDXGIDevice4_ReclaimResources1(This,resource_count,resources,results) (This)->lpVtbl->ReclaimResources1(This,resource_count,resources,results)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIDevice4_QueryInterface(IDXGIDevice4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIDevice4_AddRef(IDXGIDevice4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIDevice4_Release(IDXGIDevice4* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIDevice4_SetPrivateData(IDXGIDevice4* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIDevice4_SetPrivateDataInterface(IDXGIDevice4* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIDevice4_GetPrivateData(IDXGIDevice4* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIDevice4_GetParent(IDXGIDevice4* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDevice methods ***/
static inline HRESULT IDXGIDevice4_GetAdapter(IDXGIDevice4* This,IDXGIAdapter **adapter) {
    return This->lpVtbl->GetAdapter(This,adapter);
}
static inline HRESULT IDXGIDevice4_CreateSurface(IDXGIDevice4* This,const DXGI_SURFACE_DESC *desc,UINT surface_count,DXGI_USAGE usage,const DXGI_SHARED_RESOURCE *shared_resource,IDXGISurface **surface) {
    return This->lpVtbl->CreateSurface(This,desc,surface_count,usage,shared_resource,surface);
}
static inline HRESULT IDXGIDevice4_QueryResourceResidency(IDXGIDevice4* This,IUnknown *const *resources,DXGI_RESIDENCY *residency,UINT resource_count) {
    return This->lpVtbl->QueryResourceResidency(This,resources,residency,resource_count);
}
static inline HRESULT IDXGIDevice4_SetGPUThreadPriority(IDXGIDevice4* This,INT priority) {
    return This->lpVtbl->SetGPUThreadPriority(This,priority);
}
static inline HRESULT IDXGIDevice4_GetGPUThreadPriority(IDXGIDevice4* This,INT *priority) {
    return This->lpVtbl->GetGPUThreadPriority(This,priority);
}
/*** IDXGIDevice1 methods ***/
static inline HRESULT IDXGIDevice4_SetMaximumFrameLatency(IDXGIDevice4* This,UINT MaxLatency) {
    return This->lpVtbl->SetMaximumFrameLatency(This,MaxLatency);
}
static inline HRESULT IDXGIDevice4_GetMaximumFrameLatency(IDXGIDevice4* This,UINT *pMaxLatency) {
    return This->lpVtbl->GetMaximumFrameLatency(This,pMaxLatency);
}
/*** IDXGIDevice2 methods ***/
static inline HRESULT IDXGIDevice4_OfferResources(IDXGIDevice4* This,UINT NumResources,IDXGIResource *const *ppResources,DXGI_OFFER_RESOURCE_PRIORITY Priority) {
    return This->lpVtbl->OfferResources(This,NumResources,ppResources,Priority);
}
static inline HRESULT IDXGIDevice4_ReclaimResources(IDXGIDevice4* This,UINT NumResources,IDXGIResource *const *ppResources,WINBOOL *pDiscarded) {
    return This->lpVtbl->ReclaimResources(This,NumResources,ppResources,pDiscarded);
}
static inline HRESULT IDXGIDevice4_EnqueueSetEvent(IDXGIDevice4* This,HANDLE hEvent) {
    return This->lpVtbl->EnqueueSetEvent(This,hEvent);
}
/*** IDXGIDevice3 methods ***/
static inline void IDXGIDevice4_Trim(IDXGIDevice4* This) {
    This->lpVtbl->Trim(This);
}
/*** IDXGIDevice4 methods ***/
static inline HRESULT IDXGIDevice4_OfferResources1(IDXGIDevice4* This,UINT resource_count,IDXGIResource *const *resources,DXGI_OFFER_RESOURCE_PRIORITY priority,UINT flags) {
    return This->lpVtbl->OfferResources1(This,resource_count,resources,priority,flags);
}
static inline HRESULT IDXGIDevice4_ReclaimResources1(IDXGIDevice4* This,UINT resource_count,IDXGIResource *const *resources,DXGI_RECLAIM_RESOURCE_RESULTS *results) {
    return This->lpVtbl->ReclaimResources1(This,resource_count,resources,results);
}
#endif
#endif

#endif


#endif  /* __IDXGIDevice4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIFactory5 interface
 */
#ifndef __IDXGIFactory5_INTERFACE_DEFINED__
#define __IDXGIFactory5_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIFactory5, 0x7632e1f5, 0xee65, 0x4dca, 0x87,0xfd, 0x84,0xcd,0x75,0xf8,0x83,0x8d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7632e1f5-ee65-4dca-87fd-84cd75f8838d")
IDXGIFactory5 : public IDXGIFactory4
{
    virtual HRESULT STDMETHODCALLTYPE CheckFeatureSupport(
        DXGI_FEATURE feature,
        void *support_data,
        UINT support_data_size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIFactory5, 0x7632e1f5, 0xee65, 0x4dca, 0x87,0xfd, 0x84,0xcd,0x75,0xf8,0x83,0x8d)
#endif
#else
typedef struct IDXGIFactory5Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIFactory5 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIFactory5 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIFactory5 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIFactory5 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIFactory5 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIFactory5 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIFactory5 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters)(
        IDXGIFactory5 *This,
        UINT adapter_idx,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *MakeWindowAssociation)(
        IDXGIFactory5 *This,
        HWND window,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetWindowAssociation)(
        IDXGIFactory5 *This,
        HWND *window);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChain)(
        IDXGIFactory5 *This,
        IUnknown *device,
        DXGI_SWAP_CHAIN_DESC *desc,
        IDXGISwapChain **swapchain);

    HRESULT (STDMETHODCALLTYPE *CreateSoftwareAdapter)(
        IDXGIFactory5 *This,
        HMODULE swrast,
        IDXGIAdapter **adapter);

    /*** IDXGIFactory1 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters1)(
        IDXGIFactory5 *This,
        UINT Adapter,
        IDXGIAdapter1 **ppAdapter);

    WINBOOL (STDMETHODCALLTYPE *IsCurrent)(
        IDXGIFactory5 *This);

    /*** IDXGIFactory2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsWindowedStereoEnabled)(
        IDXGIFactory5 *This);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForHwnd)(
        IDXGIFactory5 *This,
        IUnknown *pDevice,
        HWND hWnd,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForCoreWindow)(
        IDXGIFactory5 *This,
        IUnknown *pDevice,
        IUnknown *pWindow,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *GetSharedResourceAdapterLuid)(
        IDXGIFactory5 *This,
        HANDLE hResource,
        LUID *pLuid);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusWindow)(
        IDXGIFactory5 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusEvent)(
        IDXGIFactory5 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterStereoStatus)(
        IDXGIFactory5 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusWindow)(
        IDXGIFactory5 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusEvent)(
        IDXGIFactory5 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterOcclusionStatus)(
        IDXGIFactory5 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForComposition)(
        IDXGIFactory5 *This,
        IUnknown *pDevice,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    /*** IDXGIFactory3 methods ***/
    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        IDXGIFactory5 *This);

    /*** IDXGIFactory4 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapterByLuid)(
        IDXGIFactory5 *This,
        LUID luid,
        REFIID iid,
        void **adapter);

    HRESULT (STDMETHODCALLTYPE *EnumWarpAdapter)(
        IDXGIFactory5 *This,
        REFIID iid,
        void **adapter);

    /*** IDXGIFactory5 methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        IDXGIFactory5 *This,
        DXGI_FEATURE feature,
        void *support_data,
        UINT support_data_size);

    END_INTERFACE
} IDXGIFactory5Vtbl;

interface IDXGIFactory5 {
    CONST_VTBL IDXGIFactory5Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIFactory5_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIFactory5_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIFactory5_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIFactory5_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIFactory5_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIFactory5_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIFactory5_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIFactory methods ***/
#define IDXGIFactory5_EnumAdapters(This,adapter_idx,adapter) (This)->lpVtbl->EnumAdapters(This,adapter_idx,adapter)
#define IDXGIFactory5_MakeWindowAssociation(This,window,flags) (This)->lpVtbl->MakeWindowAssociation(This,window,flags)
#define IDXGIFactory5_GetWindowAssociation(This,window) (This)->lpVtbl->GetWindowAssociation(This,window)
#define IDXGIFactory5_CreateSwapChain(This,device,desc,swapchain) (This)->lpVtbl->CreateSwapChain(This,device,desc,swapchain)
#define IDXGIFactory5_CreateSoftwareAdapter(This,swrast,adapter) (This)->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter)
/*** IDXGIFactory1 methods ***/
#define IDXGIFactory5_EnumAdapters1(This,Adapter,ppAdapter) (This)->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter)
#define IDXGIFactory5_IsCurrent(This) (This)->lpVtbl->IsCurrent(This)
/*** IDXGIFactory2 methods ***/
#define IDXGIFactory5_IsWindowedStereoEnabled(This) (This)->lpVtbl->IsWindowedStereoEnabled(This)
#define IDXGIFactory5_CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory5_CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory5_GetSharedResourceAdapterLuid(This,hResource,pLuid) (This)->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid)
#define IDXGIFactory5_RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory5_RegisterStereoStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory5_UnregisterStereoStatus(This,dwCookie) (This)->lpVtbl->UnregisterStereoStatus(This,dwCookie)
#define IDXGIFactory5_RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory5_RegisterOcclusionStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory5_UnregisterOcclusionStatus(This,dwCookie) (This)->lpVtbl->UnregisterOcclusionStatus(This,dwCookie)
#define IDXGIFactory5_CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain)
/*** IDXGIFactory3 methods ***/
#define IDXGIFactory5_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
/*** IDXGIFactory4 methods ***/
#define IDXGIFactory5_EnumAdapterByLuid(This,luid,iid,adapter) (This)->lpVtbl->EnumAdapterByLuid(This,luid,iid,adapter)
#define IDXGIFactory5_EnumWarpAdapter(This,iid,adapter) (This)->lpVtbl->EnumWarpAdapter(This,iid,adapter)
/*** IDXGIFactory5 methods ***/
#define IDXGIFactory5_CheckFeatureSupport(This,feature,support_data,support_data_size) (This)->lpVtbl->CheckFeatureSupport(This,feature,support_data,support_data_size)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIFactory5_QueryInterface(IDXGIFactory5* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIFactory5_AddRef(IDXGIFactory5* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIFactory5_Release(IDXGIFactory5* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIFactory5_SetPrivateData(IDXGIFactory5* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory5_SetPrivateDataInterface(IDXGIFactory5* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIFactory5_GetPrivateData(IDXGIFactory5* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory5_GetParent(IDXGIFactory5* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIFactory methods ***/
static inline HRESULT IDXGIFactory5_EnumAdapters(IDXGIFactory5* This,UINT adapter_idx,IDXGIAdapter **adapter) {
    return This->lpVtbl->EnumAdapters(This,adapter_idx,adapter);
}
static inline HRESULT IDXGIFactory5_MakeWindowAssociation(IDXGIFactory5* This,HWND window,UINT flags) {
    return This->lpVtbl->MakeWindowAssociation(This,window,flags);
}
static inline HRESULT IDXGIFactory5_GetWindowAssociation(IDXGIFactory5* This,HWND *window) {
    return This->lpVtbl->GetWindowAssociation(This,window);
}
static inline HRESULT IDXGIFactory5_CreateSwapChain(IDXGIFactory5* This,IUnknown *device,DXGI_SWAP_CHAIN_DESC *desc,IDXGISwapChain **swapchain) {
    return This->lpVtbl->CreateSwapChain(This,device,desc,swapchain);
}
static inline HRESULT IDXGIFactory5_CreateSoftwareAdapter(IDXGIFactory5* This,HMODULE swrast,IDXGIAdapter **adapter) {
    return This->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter);
}
/*** IDXGIFactory1 methods ***/
static inline HRESULT IDXGIFactory5_EnumAdapters1(IDXGIFactory5* This,UINT Adapter,IDXGIAdapter1 **ppAdapter) {
    return This->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter);
}
static inline WINBOOL IDXGIFactory5_IsCurrent(IDXGIFactory5* This) {
    return This->lpVtbl->IsCurrent(This);
}
/*** IDXGIFactory2 methods ***/
static inline WINBOOL IDXGIFactory5_IsWindowedStereoEnabled(IDXGIFactory5* This) {
    return This->lpVtbl->IsWindowedStereoEnabled(This);
}
static inline HRESULT IDXGIFactory5_CreateSwapChainForHwnd(IDXGIFactory5* This,IUnknown *pDevice,HWND hWnd,const DXGI_SWAP_CHAIN_DESC1 *pDesc,const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain);
}
static inline HRESULT IDXGIFactory5_CreateSwapChainForCoreWindow(IDXGIFactory5* This,IUnknown *pDevice,IUnknown *pWindow,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain);
}
static inline HRESULT IDXGIFactory5_GetSharedResourceAdapterLuid(IDXGIFactory5* This,HANDLE hResource,LUID *pLuid) {
    return This->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid);
}
static inline HRESULT IDXGIFactory5_RegisterStereoStatusWindow(IDXGIFactory5* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static inline HRESULT IDXGIFactory5_RegisterStereoStatusEvent(IDXGIFactory5* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie);
}
static inline void IDXGIFactory5_UnregisterStereoStatus(IDXGIFactory5* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterStereoStatus(This,dwCookie);
}
static inline HRESULT IDXGIFactory5_RegisterOcclusionStatusWindow(IDXGIFactory5* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static inline HRESULT IDXGIFactory5_RegisterOcclusionStatusEvent(IDXGIFactory5* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie);
}
static inline void IDXGIFactory5_UnregisterOcclusionStatus(IDXGIFactory5* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterOcclusionStatus(This,dwCookie);
}
static inline HRESULT IDXGIFactory5_CreateSwapChainForComposition(IDXGIFactory5* This,IUnknown *pDevice,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain);
}
/*** IDXGIFactory3 methods ***/
static inline UINT IDXGIFactory5_GetCreationFlags(IDXGIFactory5* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
/*** IDXGIFactory4 methods ***/
static inline HRESULT IDXGIFactory5_EnumAdapterByLuid(IDXGIFactory5* This,LUID luid,REFIID iid,void **adapter) {
    return This->lpVtbl->EnumAdapterByLuid(This,luid,iid,adapter);
}
static inline HRESULT IDXGIFactory5_EnumWarpAdapter(IDXGIFactory5* This,REFIID iid,void **adapter) {
    return This->lpVtbl->EnumWarpAdapter(This,iid,adapter);
}
/*** IDXGIFactory5 methods ***/
static inline HRESULT IDXGIFactory5_CheckFeatureSupport(IDXGIFactory5* This,DXGI_FEATURE feature,void *support_data,UINT support_data_size) {
    return This->lpVtbl->CheckFeatureSupport(This,feature,support_data,support_data_size);
}
#endif
#endif

#endif


#endif  /* __IDXGIFactory5_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dxgi1_5_h__ */
