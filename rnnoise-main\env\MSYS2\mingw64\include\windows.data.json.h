/*** Autogenerated by WIDL 10.12 from include/windows.data.json.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_data_json_h__
#define __windows_data_json_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonArray_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonArray_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonArray __x_ABI_CWindows_CData_CJson_CIJsonArray;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonArray ABI::Windows::Data::Json::IJsonArray
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonArray;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonObject_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonObject_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonObject __x_ABI_CWindows_CData_CJson_CIJsonObject;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonObject ABI::Windows::Data::Json::IJsonObject
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonObject;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonValue_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonValue_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonValue __x_ABI_CWindows_CData_CJson_CIJsonValue;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonValue ABI::Windows::Data::Json::IJsonValue
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonValue;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonValueStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonValueStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonValueStatics __x_ABI_CWindows_CData_CJson_CIJsonValueStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics ABI::Windows::Data::Json::IJsonValueStatics
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonValueStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CJson_CJsonArray_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CJsonArray_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                class JsonArray;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CJson_CJsonArray __x_ABI_CWindows_CData_CJson_CJsonArray;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CJson_CJsonArray_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CJson_CJsonObject_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CJsonObject_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                class JsonObject;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CJson_CJsonObject __x_ABI_CWindows_CData_CJson_CJsonObject;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CJson_CJsonObject_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CData_CJson_CJsonValue_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CJsonValue_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                class JsonValue;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CData_CJson_CJsonValue __x_ABI_CWindows_CData_CJson_CJsonValue;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CData_CJson_CJsonValue_FWD_DEFINED__ */

#ifndef ____FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIIterable_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CData__CJson__CIJsonValue __FIIterable_1_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIIterator_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CData__CJson__CIJsonValue __FIIterator_1_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IMap<HSTRING,ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CData__CJson__CIJsonValue __FIVectorView_1_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIVector_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CData__CJson__CIJsonValue __FIVector_1_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIVector_1_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CJson_CJsonValueType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CJsonValueType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                enum JsonValueType {
                    JsonValueType_Null = 0,
                    JsonValueType_Boolean = 1,
                    JsonValueType_Number = 2,
                    JsonValueType_String = 3,
                    JsonValueType_Array = 4,
                    JsonValueType_Object = 5
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CData_CJson_CJsonValueType {
    JsonValueType_Null = 0,
    JsonValueType_Boolean = 1,
    JsonValueType_Number = 2,
    JsonValueType_String = 3,
    JsonValueType_Array = 4,
    JsonValueType_Object = 5
};
#ifdef WIDL_using_Windows_Data_Json
#define JsonValueType __x_ABI_CWindows_CData_CJson_CJsonValueType
#endif /* WIDL_using_Windows_Data_Json */
#endif

#endif /* ____x_ABI_CWindows_CData_CJson_CJsonValueType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CData_CJson_CJsonValueType __x_ABI_CWindows_CData_CJson_CJsonValueType;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonArray_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonArray_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonArray __x_ABI_CWindows_CData_CJson_CIJsonArray;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonArray ABI::Windows::Data::Json::IJsonArray
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonArray;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonArrayStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonArrayStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonArrayStatics __x_ABI_CWindows_CData_CJson_CIJsonArrayStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonArrayStatics ABI::Windows::Data::Json::IJsonArrayStatics
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonArrayStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonObject_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonObject_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonObject __x_ABI_CWindows_CData_CJson_CIJsonObject;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonObject ABI::Windows::Data::Json::IJsonObject
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonObject;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonObjectStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonObjectStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonObjectStatics __x_ABI_CWindows_CData_CJson_CIJsonObjectStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonObjectStatics ABI::Windows::Data::Json::IJsonObjectStatics
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonObjectStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonObjectWithDefaultValues_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonObjectWithDefaultValues_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonObjectWithDefaultValues __x_ABI_CWindows_CData_CJson_CIJsonObjectWithDefaultValues;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonObjectWithDefaultValues ABI::Windows::Data::Json::IJsonObjectWithDefaultValues
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonObjectWithDefaultValues;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonValue_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonValue_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonValue __x_ABI_CWindows_CData_CJson_CIJsonValue;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonValue ABI::Windows::Data::Json::IJsonValue
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonValue;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonValueStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonValueStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonValueStatics __x_ABI_CWindows_CData_CJson_CIJsonValueStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics ABI::Windows::Data::Json::IJsonValueStatics
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonValueStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonValueStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonValueStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CData_CJson_CIJsonValueStatics2 __x_ABI_CWindows_CData_CJson_CIJsonValueStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics2 ABI::Windows::Data::Json::IJsonValueStatics2
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                interface IJsonValueStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIIterable_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CData__CJson__CIJsonValue __FIIterable_1_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIIterator_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CData__CJson__CIJsonValue __FIIterator_1_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IMap<HSTRING,ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CData__CJson__CIJsonValue __FIVectorView_1_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
#define ____FIVector_1_Windows__CData__CJson__CIJsonValue_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CData__CJson__CIJsonValue __FIVector_1_Windows__CData__CJson__CIJsonValue;
#ifdef __cplusplus
#define __FIVector_1_Windows__CData__CJson__CIJsonValue ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Data::Json::IJsonValue* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IJsonArray interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonArray_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonArray_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CJson_CIJsonArray, 0x08c1ddb6, 0x0cbd, 0x4a9a, 0xb5,0xd3, 0x2f,0x85,0x2d,0xc3,0x7e,0x81);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                MIDL_INTERFACE("08c1ddb6-0cbd-4a9a-b5d3-2f852dc37e81")
                IJsonArray : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetObjectAt(
                        UINT32 index,
                        ABI::Windows::Data::Json::IJsonObject **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetArrayAt(
                        UINT32 index,
                        ABI::Windows::Data::Json::IJsonArray **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetStringAt(
                        UINT32 index,
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNumberAt(
                        UINT32 index,
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetBooleanAt(
                        UINT32 index,
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CJson_CIJsonArray, 0x08c1ddb6, 0x0cbd, 0x4a9a, 0xb5,0xd3, 0x2f,0x85,0x2d,0xc3,0x7e,0x81)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CJson_CIJsonArrayVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CJson_CIJsonArray *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CJson_CIJsonArray *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CJson_CIJsonArray *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CJson_CIJsonArray *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CJson_CIJsonArray *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CJson_CIJsonArray *This,
        TrustLevel *trustLevel);

    /*** IJsonArray methods ***/
    HRESULT (STDMETHODCALLTYPE *GetObjectAt)(
        __x_ABI_CWindows_CData_CJson_CIJsonArray *This,
        UINT32 index,
        __x_ABI_CWindows_CData_CJson_CIJsonObject **value);

    HRESULT (STDMETHODCALLTYPE *GetArrayAt)(
        __x_ABI_CWindows_CData_CJson_CIJsonArray *This,
        UINT32 index,
        __x_ABI_CWindows_CData_CJson_CIJsonArray **value);

    HRESULT (STDMETHODCALLTYPE *GetStringAt)(
        __x_ABI_CWindows_CData_CJson_CIJsonArray *This,
        UINT32 index,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetNumberAt)(
        __x_ABI_CWindows_CData_CJson_CIJsonArray *This,
        UINT32 index,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *GetBooleanAt)(
        __x_ABI_CWindows_CData_CJson_CIJsonArray *This,
        UINT32 index,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CJson_CIJsonArrayVtbl;

interface __x_ABI_CWindows_CData_CJson_CIJsonArray {
    CONST_VTBL __x_ABI_CWindows_CData_CJson_CIJsonArrayVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonArray_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CJson_CIJsonArray_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CJson_CIJsonArray_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonArray_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CJson_CIJsonArray_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CJson_CIJsonArray_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IJsonArray methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonArray_GetObjectAt(This,index,value) (This)->lpVtbl->GetObjectAt(This,index,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonArray_GetArrayAt(This,index,value) (This)->lpVtbl->GetArrayAt(This,index,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonArray_GetStringAt(This,index,value) (This)->lpVtbl->GetStringAt(This,index,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonArray_GetNumberAt(This,index,value) (This)->lpVtbl->GetNumberAt(This,index,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonArray_GetBooleanAt(This,index,value) (This)->lpVtbl->GetBooleanAt(This,index,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonArray_QueryInterface(__x_ABI_CWindows_CData_CJson_CIJsonArray* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CJson_CIJsonArray_AddRef(__x_ABI_CWindows_CData_CJson_CIJsonArray* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CJson_CIJsonArray_Release(__x_ABI_CWindows_CData_CJson_CIJsonArray* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonArray_GetIids(__x_ABI_CWindows_CData_CJson_CIJsonArray* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonArray_GetRuntimeClassName(__x_ABI_CWindows_CData_CJson_CIJsonArray* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonArray_GetTrustLevel(__x_ABI_CWindows_CData_CJson_CIJsonArray* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IJsonArray methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonArray_GetObjectAt(__x_ABI_CWindows_CData_CJson_CIJsonArray* This,UINT32 index,__x_ABI_CWindows_CData_CJson_CIJsonObject **value) {
    return This->lpVtbl->GetObjectAt(This,index,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonArray_GetArrayAt(__x_ABI_CWindows_CData_CJson_CIJsonArray* This,UINT32 index,__x_ABI_CWindows_CData_CJson_CIJsonArray **value) {
    return This->lpVtbl->GetArrayAt(This,index,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonArray_GetStringAt(__x_ABI_CWindows_CData_CJson_CIJsonArray* This,UINT32 index,HSTRING *value) {
    return This->lpVtbl->GetStringAt(This,index,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonArray_GetNumberAt(__x_ABI_CWindows_CData_CJson_CIJsonArray* This,UINT32 index,DOUBLE *value) {
    return This->lpVtbl->GetNumberAt(This,index,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonArray_GetBooleanAt(__x_ABI_CWindows_CData_CJson_CIJsonArray* This,UINT32 index,boolean *value) {
    return This->lpVtbl->GetBooleanAt(This,index,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Json
#define IID_IJsonArray IID___x_ABI_CWindows_CData_CJson_CIJsonArray
#define IJsonArrayVtbl __x_ABI_CWindows_CData_CJson_CIJsonArrayVtbl
#define IJsonArray __x_ABI_CWindows_CData_CJson_CIJsonArray
#define IJsonArray_QueryInterface __x_ABI_CWindows_CData_CJson_CIJsonArray_QueryInterface
#define IJsonArray_AddRef __x_ABI_CWindows_CData_CJson_CIJsonArray_AddRef
#define IJsonArray_Release __x_ABI_CWindows_CData_CJson_CIJsonArray_Release
#define IJsonArray_GetIids __x_ABI_CWindows_CData_CJson_CIJsonArray_GetIids
#define IJsonArray_GetRuntimeClassName __x_ABI_CWindows_CData_CJson_CIJsonArray_GetRuntimeClassName
#define IJsonArray_GetTrustLevel __x_ABI_CWindows_CData_CJson_CIJsonArray_GetTrustLevel
#define IJsonArray_GetObjectAt __x_ABI_CWindows_CData_CJson_CIJsonArray_GetObjectAt
#define IJsonArray_GetArrayAt __x_ABI_CWindows_CData_CJson_CIJsonArray_GetArrayAt
#define IJsonArray_GetStringAt __x_ABI_CWindows_CData_CJson_CIJsonArray_GetStringAt
#define IJsonArray_GetNumberAt __x_ABI_CWindows_CData_CJson_CIJsonArray_GetNumberAt
#define IJsonArray_GetBooleanAt __x_ABI_CWindows_CData_CJson_CIJsonArray_GetBooleanAt
#endif /* WIDL_using_Windows_Data_Json */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CJson_CIJsonArray_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IJsonObject interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonObject_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonObject_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CJson_CIJsonObject, 0x064e24dd, 0x29c2, 0x4f83, 0x9a,0xc1, 0x9e,0xe1,0x15,0x78,0xbe,0xb3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                MIDL_INTERFACE("064e24dd-29c2-4f83-9ac1-9ee11578beb3")
                IJsonObject : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetNamedValue(
                        HSTRING name,
                        ABI::Windows::Data::Json::IJsonValue **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE SetNamedValue(
                        HSTRING name,
                        ABI::Windows::Data::Json::IJsonValue *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNamedObject(
                        HSTRING name,
                        ABI::Windows::Data::Json::IJsonObject **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNamedArray(
                        HSTRING name,
                        ABI::Windows::Data::Json::IJsonArray **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNamedString(
                        HSTRING name,
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNamedNumber(
                        HSTRING name,
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNamedBoolean(
                        HSTRING name,
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CJson_CIJsonObject, 0x064e24dd, 0x29c2, 0x4f83, 0x9a,0xc1, 0x9e,0xe1,0x15,0x78,0xbe,0xb3)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CJson_CIJsonObjectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This,
        TrustLevel *trustLevel);

    /*** IJsonObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetNamedValue)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This,
        HSTRING name,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **value);

    HRESULT (STDMETHODCALLTYPE *SetNamedValue)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This,
        HSTRING name,
        __x_ABI_CWindows_CData_CJson_CIJsonValue *value);

    HRESULT (STDMETHODCALLTYPE *GetNamedObject)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This,
        HSTRING name,
        __x_ABI_CWindows_CData_CJson_CIJsonObject **value);

    HRESULT (STDMETHODCALLTYPE *GetNamedArray)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This,
        HSTRING name,
        __x_ABI_CWindows_CData_CJson_CIJsonArray **value);

    HRESULT (STDMETHODCALLTYPE *GetNamedString)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This,
        HSTRING name,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetNamedNumber)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This,
        HSTRING name,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *GetNamedBoolean)(
        __x_ABI_CWindows_CData_CJson_CIJsonObject *This,
        HSTRING name,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CJson_CIJsonObjectVtbl;

interface __x_ABI_CWindows_CData_CJson_CIJsonObject {
    CONST_VTBL __x_ABI_CWindows_CData_CJson_CIJsonObjectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IJsonObject methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedValue(This,name,value) (This)->lpVtbl->GetNamedValue(This,name,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_SetNamedValue(This,name,value) (This)->lpVtbl->SetNamedValue(This,name,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedObject(This,name,value) (This)->lpVtbl->GetNamedObject(This,name,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedArray(This,name,value) (This)->lpVtbl->GetNamedArray(This,name,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedString(This,name,value) (This)->lpVtbl->GetNamedString(This,name,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedNumber(This,name,value) (This)->lpVtbl->GetNamedNumber(This,name,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedBoolean(This,name,value) (This)->lpVtbl->GetNamedBoolean(This,name,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonObject_QueryInterface(__x_ABI_CWindows_CData_CJson_CIJsonObject* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CJson_CIJsonObject_AddRef(__x_ABI_CWindows_CData_CJson_CIJsonObject* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CJson_CIJsonObject_Release(__x_ABI_CWindows_CData_CJson_CIJsonObject* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonObject_GetIids(__x_ABI_CWindows_CData_CJson_CIJsonObject* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonObject_GetRuntimeClassName(__x_ABI_CWindows_CData_CJson_CIJsonObject* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonObject_GetTrustLevel(__x_ABI_CWindows_CData_CJson_CIJsonObject* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IJsonObject methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedValue(__x_ABI_CWindows_CData_CJson_CIJsonObject* This,HSTRING name,__x_ABI_CWindows_CData_CJson_CIJsonValue **value) {
    return This->lpVtbl->GetNamedValue(This,name,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonObject_SetNamedValue(__x_ABI_CWindows_CData_CJson_CIJsonObject* This,HSTRING name,__x_ABI_CWindows_CData_CJson_CIJsonValue *value) {
    return This->lpVtbl->SetNamedValue(This,name,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedObject(__x_ABI_CWindows_CData_CJson_CIJsonObject* This,HSTRING name,__x_ABI_CWindows_CData_CJson_CIJsonObject **value) {
    return This->lpVtbl->GetNamedObject(This,name,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedArray(__x_ABI_CWindows_CData_CJson_CIJsonObject* This,HSTRING name,__x_ABI_CWindows_CData_CJson_CIJsonArray **value) {
    return This->lpVtbl->GetNamedArray(This,name,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedString(__x_ABI_CWindows_CData_CJson_CIJsonObject* This,HSTRING name,HSTRING *value) {
    return This->lpVtbl->GetNamedString(This,name,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedNumber(__x_ABI_CWindows_CData_CJson_CIJsonObject* This,HSTRING name,DOUBLE *value) {
    return This->lpVtbl->GetNamedNumber(This,name,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedBoolean(__x_ABI_CWindows_CData_CJson_CIJsonObject* This,HSTRING name,boolean *value) {
    return This->lpVtbl->GetNamedBoolean(This,name,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Json
#define IID_IJsonObject IID___x_ABI_CWindows_CData_CJson_CIJsonObject
#define IJsonObjectVtbl __x_ABI_CWindows_CData_CJson_CIJsonObjectVtbl
#define IJsonObject __x_ABI_CWindows_CData_CJson_CIJsonObject
#define IJsonObject_QueryInterface __x_ABI_CWindows_CData_CJson_CIJsonObject_QueryInterface
#define IJsonObject_AddRef __x_ABI_CWindows_CData_CJson_CIJsonObject_AddRef
#define IJsonObject_Release __x_ABI_CWindows_CData_CJson_CIJsonObject_Release
#define IJsonObject_GetIids __x_ABI_CWindows_CData_CJson_CIJsonObject_GetIids
#define IJsonObject_GetRuntimeClassName __x_ABI_CWindows_CData_CJson_CIJsonObject_GetRuntimeClassName
#define IJsonObject_GetTrustLevel __x_ABI_CWindows_CData_CJson_CIJsonObject_GetTrustLevel
#define IJsonObject_GetNamedValue __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedValue
#define IJsonObject_SetNamedValue __x_ABI_CWindows_CData_CJson_CIJsonObject_SetNamedValue
#define IJsonObject_GetNamedObject __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedObject
#define IJsonObject_GetNamedArray __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedArray
#define IJsonObject_GetNamedString __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedString
#define IJsonObject_GetNamedNumber __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedNumber
#define IJsonObject_GetNamedBoolean __x_ABI_CWindows_CData_CJson_CIJsonObject_GetNamedBoolean
#endif /* WIDL_using_Windows_Data_Json */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CJson_CIJsonObject_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IJsonValue interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonValue_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonValue_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CJson_CIJsonValue, 0xa3219ecb, 0xf0b3, 0x4dcd, 0xbe,0xee, 0x19,0xd4,0x8c,0xd3,0xed,0x1e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                MIDL_INTERFACE("a3219ecb-f0b3-4dcd-beee-19d48cd3ed1e")
                IJsonValue : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_ValueType(
                        ABI::Windows::Data::Json::JsonValueType *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Stringify(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetString(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNumber(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetBoolean(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetArray(
                        ABI::Windows::Data::Json::IJsonArray **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetObject(
                        ABI::Windows::Data::Json::IJsonObject **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CJson_CIJsonValue, 0xa3219ecb, 0xf0b3, 0x4dcd, 0xbe,0xee, 0x19,0xd4,0x8c,0xd3,0xed,0x1e)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CJson_CIJsonValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This,
        TrustLevel *trustLevel);

    /*** IJsonValue methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ValueType)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This,
        __x_ABI_CWindows_CData_CJson_CJsonValueType *value);

    HRESULT (STDMETHODCALLTYPE *Stringify)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetString)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetNumber)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *GetBoolean)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetArray)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This,
        __x_ABI_CWindows_CData_CJson_CIJsonArray **value);

    HRESULT (STDMETHODCALLTYPE *GetObject)(
        __x_ABI_CWindows_CData_CJson_CIJsonValue *This,
        __x_ABI_CWindows_CData_CJson_CIJsonObject **value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CJson_CIJsonValueVtbl;

interface __x_ABI_CWindows_CData_CJson_CIJsonValue {
    CONST_VTBL __x_ABI_CWindows_CData_CJson_CIJsonValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IJsonValue methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_get_ValueType(This,value) (This)->lpVtbl->get_ValueType(This,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_Stringify(This,value) (This)->lpVtbl->Stringify(This,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_GetString(This,value) (This)->lpVtbl->GetString(This,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_GetNumber(This,value) (This)->lpVtbl->GetNumber(This,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_GetBoolean(This,value) (This)->lpVtbl->GetBoolean(This,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_GetArray(This,value) (This)->lpVtbl->GetArray(This,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonValue_GetObject(This,value) (This)->lpVtbl->GetObject(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValue_QueryInterface(__x_ABI_CWindows_CData_CJson_CIJsonValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CJson_CIJsonValue_AddRef(__x_ABI_CWindows_CData_CJson_CIJsonValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CJson_CIJsonValue_Release(__x_ABI_CWindows_CData_CJson_CIJsonValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValue_GetIids(__x_ABI_CWindows_CData_CJson_CIJsonValue* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValue_GetRuntimeClassName(__x_ABI_CWindows_CData_CJson_CIJsonValue* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValue_GetTrustLevel(__x_ABI_CWindows_CData_CJson_CIJsonValue* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IJsonValue methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValue_get_ValueType(__x_ABI_CWindows_CData_CJson_CIJsonValue* This,__x_ABI_CWindows_CData_CJson_CJsonValueType *value) {
    return This->lpVtbl->get_ValueType(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValue_Stringify(__x_ABI_CWindows_CData_CJson_CIJsonValue* This,HSTRING *value) {
    return This->lpVtbl->Stringify(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValue_GetString(__x_ABI_CWindows_CData_CJson_CIJsonValue* This,HSTRING *value) {
    return This->lpVtbl->GetString(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValue_GetNumber(__x_ABI_CWindows_CData_CJson_CIJsonValue* This,DOUBLE *value) {
    return This->lpVtbl->GetNumber(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValue_GetBoolean(__x_ABI_CWindows_CData_CJson_CIJsonValue* This,boolean *value) {
    return This->lpVtbl->GetBoolean(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValue_GetArray(__x_ABI_CWindows_CData_CJson_CIJsonValue* This,__x_ABI_CWindows_CData_CJson_CIJsonArray **value) {
    return This->lpVtbl->GetArray(This,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValue_GetObject(__x_ABI_CWindows_CData_CJson_CIJsonValue* This,__x_ABI_CWindows_CData_CJson_CIJsonObject **value) {
    return This->lpVtbl->GetObject(This,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Json
#define IID_IJsonValue IID___x_ABI_CWindows_CData_CJson_CIJsonValue
#define IJsonValueVtbl __x_ABI_CWindows_CData_CJson_CIJsonValueVtbl
#define IJsonValue __x_ABI_CWindows_CData_CJson_CIJsonValue
#define IJsonValue_QueryInterface __x_ABI_CWindows_CData_CJson_CIJsonValue_QueryInterface
#define IJsonValue_AddRef __x_ABI_CWindows_CData_CJson_CIJsonValue_AddRef
#define IJsonValue_Release __x_ABI_CWindows_CData_CJson_CIJsonValue_Release
#define IJsonValue_GetIids __x_ABI_CWindows_CData_CJson_CIJsonValue_GetIids
#define IJsonValue_GetRuntimeClassName __x_ABI_CWindows_CData_CJson_CIJsonValue_GetRuntimeClassName
#define IJsonValue_GetTrustLevel __x_ABI_CWindows_CData_CJson_CIJsonValue_GetTrustLevel
#define IJsonValue_get_ValueType __x_ABI_CWindows_CData_CJson_CIJsonValue_get_ValueType
#define IJsonValue_Stringify __x_ABI_CWindows_CData_CJson_CIJsonValue_Stringify
#define IJsonValue_GetString __x_ABI_CWindows_CData_CJson_CIJsonValue_GetString
#define IJsonValue_GetNumber __x_ABI_CWindows_CData_CJson_CIJsonValue_GetNumber
#define IJsonValue_GetBoolean __x_ABI_CWindows_CData_CJson_CIJsonValue_GetBoolean
#define IJsonValue_GetArray __x_ABI_CWindows_CData_CJson_CIJsonValue_GetArray
#define IJsonValue_GetObject __x_ABI_CWindows_CData_CJson_CIJsonValue_GetObject
#endif /* WIDL_using_Windows_Data_Json */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CJson_CIJsonValue_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IJsonValueStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CData_CJson_CIJsonValueStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CData_CJson_CIJsonValueStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CData_CJson_CIJsonValueStatics, 0x5f6b544a, 0x2f53, 0x48e1, 0x91,0xa3, 0xf7,0x8b,0x50,0xa6,0x34,0x5c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Data {
            namespace Json {
                MIDL_INTERFACE("5f6b544a-2f53-48e1-91a3-f78b50a6345c")
                IJsonValueStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Parse(
                        HSTRING input,
                        ABI::Windows::Data::Json::IJsonValue **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE TryParse(
                        HSTRING input,
                        ABI::Windows::Data::Json::IJsonValue **result,
                        boolean *succeeded) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateBooleanValue(
                        boolean input,
                        ABI::Windows::Data::Json::IJsonValue **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateNumberValue(
                        DOUBLE input,
                        ABI::Windows::Data::Json::IJsonValue **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateStringValue(
                        HSTRING input,
                        ABI::Windows::Data::Json::IJsonValue **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics, 0x5f6b544a, 0x2f53, 0x48e1, 0x91,0xa3, 0xf7,0x8b,0x50,0xa6,0x34,0x5c)
#endif
#else
typedef struct __x_ABI_CWindows_CData_CJson_CIJsonValueStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CData_CJson_CIJsonValueStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CData_CJson_CIJsonValueStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CData_CJson_CIJsonValueStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CData_CJson_CIJsonValueStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CData_CJson_CIJsonValueStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CData_CJson_CIJsonValueStatics *This,
        TrustLevel *trustLevel);

    /*** IJsonValueStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *Parse)(
        __x_ABI_CWindows_CData_CJson_CIJsonValueStatics *This,
        HSTRING input,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **value);

    HRESULT (STDMETHODCALLTYPE *TryParse)(
        __x_ABI_CWindows_CData_CJson_CIJsonValueStatics *This,
        HSTRING input,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **result,
        boolean *succeeded);

    HRESULT (STDMETHODCALLTYPE *CreateBooleanValue)(
        __x_ABI_CWindows_CData_CJson_CIJsonValueStatics *This,
        boolean input,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **value);

    HRESULT (STDMETHODCALLTYPE *CreateNumberValue)(
        __x_ABI_CWindows_CData_CJson_CIJsonValueStatics *This,
        DOUBLE input,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **value);

    HRESULT (STDMETHODCALLTYPE *CreateStringValue)(
        __x_ABI_CWindows_CData_CJson_CIJsonValueStatics *This,
        HSTRING input,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **value);

    END_INTERFACE
} __x_ABI_CWindows_CData_CJson_CIJsonValueStaticsVtbl;

interface __x_ABI_CWindows_CData_CJson_CIJsonValueStatics {
    CONST_VTBL __x_ABI_CWindows_CData_CJson_CIJsonValueStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IJsonValueStatics methods ***/
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_Parse(This,input,value) (This)->lpVtbl->Parse(This,input,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_TryParse(This,input,result,succeeded) (This)->lpVtbl->TryParse(This,input,result,succeeded)
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_CreateBooleanValue(This,input,value) (This)->lpVtbl->CreateBooleanValue(This,input,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_CreateNumberValue(This,input,value) (This)->lpVtbl->CreateNumberValue(This,input,value)
#define __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_CreateStringValue(This,input,value) (This)->lpVtbl->CreateStringValue(This,input,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_QueryInterface(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_AddRef(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_Release(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_GetIids(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_GetRuntimeClassName(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_GetTrustLevel(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IJsonValueStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_Parse(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics* This,HSTRING input,__x_ABI_CWindows_CData_CJson_CIJsonValue **value) {
    return This->lpVtbl->Parse(This,input,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_TryParse(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics* This,HSTRING input,__x_ABI_CWindows_CData_CJson_CIJsonValue **result,boolean *succeeded) {
    return This->lpVtbl->TryParse(This,input,result,succeeded);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_CreateBooleanValue(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics* This,boolean input,__x_ABI_CWindows_CData_CJson_CIJsonValue **value) {
    return This->lpVtbl->CreateBooleanValue(This,input,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_CreateNumberValue(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics* This,DOUBLE input,__x_ABI_CWindows_CData_CJson_CIJsonValue **value) {
    return This->lpVtbl->CreateNumberValue(This,input,value);
}
static inline HRESULT __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_CreateStringValue(__x_ABI_CWindows_CData_CJson_CIJsonValueStatics* This,HSTRING input,__x_ABI_CWindows_CData_CJson_CIJsonValue **value) {
    return This->lpVtbl->CreateStringValue(This,input,value);
}
#endif
#ifdef WIDL_using_Windows_Data_Json
#define IID_IJsonValueStatics IID___x_ABI_CWindows_CData_CJson_CIJsonValueStatics
#define IJsonValueStaticsVtbl __x_ABI_CWindows_CData_CJson_CIJsonValueStaticsVtbl
#define IJsonValueStatics __x_ABI_CWindows_CData_CJson_CIJsonValueStatics
#define IJsonValueStatics_QueryInterface __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_QueryInterface
#define IJsonValueStatics_AddRef __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_AddRef
#define IJsonValueStatics_Release __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_Release
#define IJsonValueStatics_GetIids __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_GetIids
#define IJsonValueStatics_GetRuntimeClassName __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_GetRuntimeClassName
#define IJsonValueStatics_GetTrustLevel __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_GetTrustLevel
#define IJsonValueStatics_Parse __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_Parse
#define IJsonValueStatics_TryParse __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_TryParse
#define IJsonValueStatics_CreateBooleanValue __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_CreateBooleanValue
#define IJsonValueStatics_CreateNumberValue __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_CreateNumberValue
#define IJsonValueStatics_CreateStringValue __x_ABI_CWindows_CData_CJson_CIJsonValueStatics_CreateStringValue
#endif /* WIDL_using_Windows_Data_Json */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CData_CJson_CIJsonValueStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Json.JsonArray
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Json_JsonArray_DEFINED
#define RUNTIMECLASS_Windows_Data_Json_JsonArray_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Json_JsonArray[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','J','s','o','n','.','J','s','o','n','A','r','r','a','y',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Json_JsonArray[] = L"Windows.Data.Json.JsonArray";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Json_JsonArray[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','J','s','o','n','.','J','s','o','n','A','r','r','a','y',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Json_JsonArray_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Json.JsonObject
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Json_JsonObject_DEFINED
#define RUNTIMECLASS_Windows_Data_Json_JsonObject_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Json_JsonObject[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','J','s','o','n','.','J','s','o','n','O','b','j','e','c','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Json_JsonObject[] = L"Windows.Data.Json.JsonObject";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Json_JsonObject[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','J','s','o','n','.','J','s','o','n','O','b','j','e','c','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Json_JsonObject_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Data.Json.JsonValue
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Data_Json_JsonValue_DEFINED
#define RUNTIMECLASS_Windows_Data_Json_JsonValue_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Data_Json_JsonValue[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','J','s','o','n','.','J','s','o','n','V','a','l','u','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Json_JsonValue[] = L"Windows.Data.Json.JsonValue";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Data_Json_JsonValue[] = {'W','i','n','d','o','w','s','.','D','a','t','a','.','J','s','o','n','.','J','s','o','n','V','a','l','u','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Data_Json_JsonValue_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* > interface
 */
#ifndef ____FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__
#define ____FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue, 0x4deecc89, 0xb0b8, 0x5ee8, 0xa5,0x1d, 0x1c,0x25,0xad,0x9a,0x5b,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("4deecc89-b0b8-5ee8-a51d-1c25ad9a5b01")
                IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* > : IKeyValuePair_impl<HSTRING, ABI::Windows::Data::Json::IJsonValue* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue, 0x4deecc89, 0xb0b8, 0x5ee8, 0xa5,0x1d, 0x1c,0x25,0xad,0x9a,0x5b,0x01)
#endif
#else
typedef struct __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        TrustLevel *trustLevel);

    /*** IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Key)(
        __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING *key);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **value);

    END_INTERFACE
} __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl;

interface __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue {
    CONST_VTBL __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* > methods ***/
#define __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Key(This,key) (This)->lpVtbl->get_Key(This,key)
#define __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface(__FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef(__FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release(__FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids(__FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(__FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel(__FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* > methods ***/
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Key(__FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING *key) {
    return This->lpVtbl->get_Key(This,key);
}
static inline HRESULT __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Value(__FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,__x_ABI_CWindows_CData_CJson_CIJsonValue **value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IKeyValuePair_HSTRING_IJsonValue IID___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue
#define IKeyValuePair_HSTRING_IJsonValueVtbl __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl
#define IKeyValuePair_HSTRING_IJsonValue __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue
#define IKeyValuePair_HSTRING_IJsonValue_QueryInterface __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface
#define IKeyValuePair_HSTRING_IJsonValue_AddRef __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef
#define IKeyValuePair_HSTRING_IJsonValue_Release __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release
#define IKeyValuePair_HSTRING_IJsonValue_GetIids __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids
#define IKeyValuePair_HSTRING_IJsonValue_GetRuntimeClassName __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName
#define IKeyValuePair_HSTRING_IJsonValue_GetTrustLevel __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel
#define IKeyValuePair_HSTRING_IJsonValue_get_Key __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Key
#define IKeyValuePair_HSTRING_IJsonValue_get_Value __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Value
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Data::Json::IJsonValue* > interface
 */
#ifndef ____FIIterable_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CData__CJson__CIJsonValue, 0xcb0492b6, 0x4113, 0x55cf, 0xb2,0xc5, 0x99,0xeb,0x42,0x8b,0xa4,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("cb0492b6-4113-55cf-b2c5-99eb428ba493")
                IIterable<ABI::Windows::Data::Json::IJsonValue* > : IIterable_impl<ABI::Windows::Data::Json::IJsonValue* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CData__CJson__CIJsonValue, 0xcb0492b6, 0x4113, 0x55cf, 0xb2,0xc5, 0x99,0xeb,0x42,0x8b,0xa4,0x93)
#endif
#else
typedef struct __FIIterable_1_Windows__CData__CJson__CIJsonValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CData__CJson__CIJsonValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CData__CJson__CIJsonValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CData__CJson__CIJsonValue *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CData__CJson__CIJsonValue *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CData__CJson__CIJsonValue *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CData__CJson__CIJsonValue *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Data::Json::IJsonValue* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CData__CJson__CIJsonValue *This,
        __FIIterator_1_Windows__CData__CJson__CIJsonValue **value);

    END_INTERFACE
} __FIIterable_1_Windows__CData__CJson__CIJsonValueVtbl;

interface __FIIterable_1_Windows__CData__CJson__CIJsonValue {
    CONST_VTBL __FIIterable_1_Windows__CData__CJson__CIJsonValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CData__CJson__CIJsonValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CData__CJson__CIJsonValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CData__CJson__CIJsonValue_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CData__CJson__CIJsonValue_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Data::Json::IJsonValue* > methods ***/
#define __FIIterable_1_Windows__CData__CJson__CIJsonValue_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CData__CJson__CIJsonValue_QueryInterface(__FIIterable_1_Windows__CData__CJson__CIJsonValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CData__CJson__CIJsonValue_AddRef(__FIIterable_1_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CData__CJson__CIJsonValue_Release(__FIIterable_1_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CData__CJson__CIJsonValue_GetIids(__FIIterable_1_Windows__CData__CJson__CIJsonValue* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(__FIIterable_1_Windows__CData__CJson__CIJsonValue* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel(__FIIterable_1_Windows__CData__CJson__CIJsonValue* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Data::Json::IJsonValue* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CData__CJson__CIJsonValue_First(__FIIterable_1_Windows__CData__CJson__CIJsonValue* This,__FIIterator_1_Windows__CData__CJson__CIJsonValue **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_IJsonValue IID___FIIterable_1_Windows__CData__CJson__CIJsonValue
#define IIterable_IJsonValueVtbl __FIIterable_1_Windows__CData__CJson__CIJsonValueVtbl
#define IIterable_IJsonValue __FIIterable_1_Windows__CData__CJson__CIJsonValue
#define IIterable_IJsonValue_QueryInterface __FIIterable_1_Windows__CData__CJson__CIJsonValue_QueryInterface
#define IIterable_IJsonValue_AddRef __FIIterable_1_Windows__CData__CJson__CIJsonValue_AddRef
#define IIterable_IJsonValue_Release __FIIterable_1_Windows__CData__CJson__CIJsonValue_Release
#define IIterable_IJsonValue_GetIids __FIIterable_1_Windows__CData__CJson__CIJsonValue_GetIids
#define IIterable_IJsonValue_GetRuntimeClassName __FIIterable_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName
#define IIterable_IJsonValue_GetTrustLevel __FIIterable_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel
#define IIterable_IJsonValue_First __FIIterable_1_Windows__CData__CJson__CIJsonValue_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* > interface
 */
#ifndef ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__
#define ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue, 0xdfabb6e1, 0x0411, 0x5a8f, 0xaa,0x87, 0x35,0x4e,0x71,0x10,0xf0,0x99);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("dfabb6e1-0411-5a8f-aa87-354e7110f099")
                IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* > : IIterable_impl<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue, 0xdfabb6e1, 0x0411, 0x5a8f, 0xaa,0x87, 0x35,0x4e,0x71,0x10,0xf0,0x99)
#endif
#else
typedef struct __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue **value);

    END_INTERFACE
} __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl;

interface __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue {
    CONST_VTBL __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* > methods ***/
#define __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* > methods ***/
static inline HRESULT __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_First(__FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_IKeyValuePair_HSTRING_IJsonValue IID___FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue
#define IIterable_IKeyValuePair_HSTRING_IJsonValueVtbl __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl
#define IIterable_IKeyValuePair_HSTRING_IJsonValue __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue
#define IIterable_IKeyValuePair_HSTRING_IJsonValue_QueryInterface __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface
#define IIterable_IKeyValuePair_HSTRING_IJsonValue_AddRef __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef
#define IIterable_IKeyValuePair_HSTRING_IJsonValue_Release __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release
#define IIterable_IKeyValuePair_HSTRING_IJsonValue_GetIids __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids
#define IIterable_IKeyValuePair_HSTRING_IJsonValue_GetRuntimeClassName __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName
#define IIterable_IKeyValuePair_HSTRING_IJsonValue_GetTrustLevel __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel
#define IIterable_IKeyValuePair_HSTRING_IJsonValue_First __FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Data::Json::IJsonValue* > interface
 */
#ifndef ____FIIterator_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CData__CJson__CIJsonValue, 0x189eb512, 0x5a20, 0x5ec6, 0x98,0x66, 0x60,0xaf,0x96,0xf0,0xd2,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("189eb512-5a20-5ec6-9866-60af96f0d23b")
                IIterator<ABI::Windows::Data::Json::IJsonValue* > : IIterator_impl<ABI::Windows::Data::Json::IJsonValue* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CData__CJson__CIJsonValue, 0x189eb512, 0x5a20, 0x5ec6, 0x98,0x66, 0x60,0xaf,0x96,0xf0,0xd2,0x3b)
#endif
#else
typedef struct __FIIterator_1_Windows__CData__CJson__CIJsonValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CData__CJson__CIJsonValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CData__CJson__CIJsonValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CData__CJson__CIJsonValue *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CData__CJson__CIJsonValue *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CData__CJson__CIJsonValue *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CData__CJson__CIJsonValue *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Data::Json::IJsonValue* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CData__CJson__CIJsonValue *This,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CData__CJson__CIJsonValue *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CData__CJson__CIJsonValue *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CData__CJson__CIJsonValue *This,
        UINT32 items_size,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CData__CJson__CIJsonValueVtbl;

interface __FIIterator_1_Windows__CData__CJson__CIJsonValue {
    CONST_VTBL __FIIterator_1_Windows__CData__CJson__CIJsonValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Data::Json::IJsonValue* > methods ***/
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CData__CJson__CIJsonValue_QueryInterface(__FIIterator_1_Windows__CData__CJson__CIJsonValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CData__CJson__CIJsonValue_AddRef(__FIIterator_1_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CData__CJson__CIJsonValue_Release(__FIIterator_1_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetIids(__FIIterator_1_Windows__CData__CJson__CIJsonValue* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(__FIIterator_1_Windows__CData__CJson__CIJsonValue* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel(__FIIterator_1_Windows__CData__CJson__CIJsonValue* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Data::Json::IJsonValue* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CData__CJson__CIJsonValue_get_Current(__FIIterator_1_Windows__CData__CJson__CIJsonValue* This,__x_ABI_CWindows_CData_CJson_CIJsonValue **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CData__CJson__CIJsonValue_get_HasCurrent(__FIIterator_1_Windows__CData__CJson__CIJsonValue* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CData__CJson__CIJsonValue_MoveNext(__FIIterator_1_Windows__CData__CJson__CIJsonValue* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetMany(__FIIterator_1_Windows__CData__CJson__CIJsonValue* This,UINT32 items_size,__x_ABI_CWindows_CData_CJson_CIJsonValue **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_IJsonValue IID___FIIterator_1_Windows__CData__CJson__CIJsonValue
#define IIterator_IJsonValueVtbl __FIIterator_1_Windows__CData__CJson__CIJsonValueVtbl
#define IIterator_IJsonValue __FIIterator_1_Windows__CData__CJson__CIJsonValue
#define IIterator_IJsonValue_QueryInterface __FIIterator_1_Windows__CData__CJson__CIJsonValue_QueryInterface
#define IIterator_IJsonValue_AddRef __FIIterator_1_Windows__CData__CJson__CIJsonValue_AddRef
#define IIterator_IJsonValue_Release __FIIterator_1_Windows__CData__CJson__CIJsonValue_Release
#define IIterator_IJsonValue_GetIids __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetIids
#define IIterator_IJsonValue_GetRuntimeClassName __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName
#define IIterator_IJsonValue_GetTrustLevel __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel
#define IIterator_IJsonValue_get_Current __FIIterator_1_Windows__CData__CJson__CIJsonValue_get_Current
#define IIterator_IJsonValue_get_HasCurrent __FIIterator_1_Windows__CData__CJson__CIJsonValue_get_HasCurrent
#define IIterator_IJsonValue_MoveNext __FIIterator_1_Windows__CData__CJson__CIJsonValue_MoveNext
#define IIterator_IJsonValue_GetMany __FIIterator_1_Windows__CData__CJson__CIJsonValue_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* > interface
 */
#ifndef ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__
#define ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue, 0xf948eac5, 0x33eb, 0x50f5, 0xb5,0xaf, 0xe7,0xce,0xcf,0x0e,0x45,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("f948eac5-33eb-50f5-b5af-e7cecf0e4501")
                IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* > : IIterator_impl<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue, 0xf948eac5, 0x33eb, 0x50f5, 0xb5,0xaf, 0xe7,0xce,0xcf,0x0e,0x45,0x01)
#endif
#else
typedef struct __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        UINT32 items_size,
        __FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl;

interface __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue {
    CONST_VTBL __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* > methods ***/
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Foundation::Collections::IKeyValuePair<HSTRING,ABI::Windows::Data::Json::IJsonValue* >* > methods ***/
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Current(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,__FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_HasCurrent(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_MoveNext(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetMany(__FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,UINT32 items_size,__FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_IKeyValuePair_HSTRING_IJsonValue IID___FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue
#define IIterator_IKeyValuePair_HSTRING_IJsonValueVtbl __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl
#define IIterator_IKeyValuePair_HSTRING_IJsonValue __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue
#define IIterator_IKeyValuePair_HSTRING_IJsonValue_QueryInterface __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface
#define IIterator_IKeyValuePair_HSTRING_IJsonValue_AddRef __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef
#define IIterator_IKeyValuePair_HSTRING_IJsonValue_Release __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release
#define IIterator_IKeyValuePair_HSTRING_IJsonValue_GetIids __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids
#define IIterator_IKeyValuePair_HSTRING_IJsonValue_GetRuntimeClassName __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName
#define IIterator_IKeyValuePair_HSTRING_IJsonValue_GetTrustLevel __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel
#define IIterator_IKeyValuePair_HSTRING_IJsonValue_get_Current __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Current
#define IIterator_IKeyValuePair_HSTRING_IJsonValue_get_HasCurrent __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_HasCurrent
#define IIterator_IKeyValuePair_HSTRING_IJsonValue_MoveNext __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_MoveNext
#define IIterator_IKeyValuePair_HSTRING_IJsonValue_GetMany __FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1___FIKeyValuePair_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMapView<HSTRING,ABI::Windows::Data::Json::IJsonValue* > interface
 */
#ifndef ____FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue, 0xeecd690c, 0x1ff3, 0x529f, 0x92,0x3f, 0x9b,0x1c,0x31,0xfd,0x3d,0x0f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("eecd690c-1ff3-529f-923f-9b1c31fd3d0f")
                IMapView<HSTRING,ABI::Windows::Data::Json::IJsonValue* > : IMapView_impl<HSTRING, ABI::Windows::Data::Json::IJsonValue* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue, 0xeecd690c, 0x1ff3, 0x529f, 0x92,0x3f, 0x9b,0x1c,0x31,0xfd,0x3d,0x0f)
#endif
#else
typedef struct __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        TrustLevel *trustLevel);

    /*** IMapView<HSTRING,ABI::Windows::Data::Json::IJsonValue* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING key,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *Split)(
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue **first,
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue **second);

    END_INTERFACE
} __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl;

interface __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue {
    CONST_VTBL __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMapView<HSTRING,ABI::Windows::Data::Json::IJsonValue* > methods ***/
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_Split(This,first,second) (This)->lpVtbl->Split(This,first,second)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface(__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef(__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release(__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids(__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel(__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMapView<HSTRING,ABI::Windows::Data::Json::IJsonValue* > methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_Lookup(__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING key,__x_ABI_CWindows_CData_CJson_CIJsonValue **value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Size(__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_HasKey(__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_Split(__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue **first,__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue **second) {
    return This->lpVtbl->Split(This,first,second);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMapView_HSTRING_IJsonValue IID___FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue
#define IMapView_HSTRING_IJsonValueVtbl __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl
#define IMapView_HSTRING_IJsonValue __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue
#define IMapView_HSTRING_IJsonValue_QueryInterface __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface
#define IMapView_HSTRING_IJsonValue_AddRef __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef
#define IMapView_HSTRING_IJsonValue_Release __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release
#define IMapView_HSTRING_IJsonValue_GetIids __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids
#define IMapView_HSTRING_IJsonValue_GetRuntimeClassName __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName
#define IMapView_HSTRING_IJsonValue_GetTrustLevel __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel
#define IMapView_HSTRING_IJsonValue_Lookup __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_Lookup
#define IMapView_HSTRING_IJsonValue_get_Size __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Size
#define IMapView_HSTRING_IJsonValue_HasKey __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_HasKey
#define IMapView_HSTRING_IJsonValue_Split __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_Split
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMap<HSTRING,ABI::Windows::Data::Json::IJsonValue* > interface
 */
#ifndef ____FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__
#define ____FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue, 0xc9d9a725, 0x786b, 0x5113, 0xb4,0xb7, 0x9b,0x61,0x76,0x4c,0x22,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("c9d9a725-786b-5113-b4b7-9b61764c220b")
                IMap<HSTRING,ABI::Windows::Data::Json::IJsonValue* > : IMap_impl<HSTRING, ABI::Windows::Data::Json::IJsonValue* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue, 0xc9d9a725, 0x786b, 0x5113, 0xb4,0xb7, 0x9b,0x61,0x76,0x4c,0x22,0x0b)
#endif
#else
typedef struct __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        TrustLevel *trustLevel);

    /*** IMap<HSTRING,ABI::Windows::Data::Json::IJsonValue* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING key,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        __FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue **view);

    HRESULT (STDMETHODCALLTYPE *Insert)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING key,
        __x_ABI_CWindows_CData_CJson_CIJsonValue *value,
        boolean *replaced);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This,
        HSTRING key);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue *This);

    END_INTERFACE
} __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl;

interface __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue {
    CONST_VTBL __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMap<HSTRING,ABI::Windows::Data::Json::IJsonValue* > methods ***/
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetView(This,view) (This)->lpVtbl->GetView(This,view)
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Insert(This,key,value,replaced) (This)->lpVtbl->Insert(This,key,value,replaced)
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Remove(This,key) (This)->lpVtbl->Remove(This,key)
#define __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMap<HSTRING,ABI::Windows::Data::Json::IJsonValue* > methods ***/
static inline HRESULT __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Lookup(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING key,__x_ABI_CWindows_CData_CJson_CIJsonValue **value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Size(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_HasKey(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetView(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,__FIMapView_2_HSTRING_Windows__CData__CJson__CIJsonValue **view) {
    return This->lpVtbl->GetView(This,view);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Insert(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING key,__x_ABI_CWindows_CData_CJson_CIJsonValue *value,boolean *replaced) {
    return This->lpVtbl->Insert(This,key,value,replaced);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Remove(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This,HSTRING key) {
    return This->lpVtbl->Remove(This,key);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Clear(__FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->Clear(This);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMap_HSTRING_IJsonValue IID___FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue
#define IMap_HSTRING_IJsonValueVtbl __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValueVtbl
#define IMap_HSTRING_IJsonValue __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue
#define IMap_HSTRING_IJsonValue_QueryInterface __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_QueryInterface
#define IMap_HSTRING_IJsonValue_AddRef __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_AddRef
#define IMap_HSTRING_IJsonValue_Release __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Release
#define IMap_HSTRING_IJsonValue_GetIids __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetIids
#define IMap_HSTRING_IJsonValue_GetRuntimeClassName __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName
#define IMap_HSTRING_IJsonValue_GetTrustLevel __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetTrustLevel
#define IMap_HSTRING_IJsonValue_Lookup __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Lookup
#define IMap_HSTRING_IJsonValue_get_Size __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_get_Size
#define IMap_HSTRING_IJsonValue_HasKey __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_HasKey
#define IMap_HSTRING_IJsonValue_GetView __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_GetView
#define IMap_HSTRING_IJsonValue_Insert __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Insert
#define IMap_HSTRING_IJsonValue_Remove __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Remove
#define IMap_HSTRING_IJsonValue_Clear __FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_Clear
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMap_2_HSTRING_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Data::Json::IJsonValue* > interface
 */
#ifndef ____FIVectorView_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CData__CJson__CIJsonValue, 0xcffabb0f, 0x6bc4, 0x5ff6, 0x9b,0x9e, 0x7a,0x9d,0xf6,0xc6,0x87,0xc8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("cffabb0f-6bc4-5ff6-9b9e-7a9df6c687c8")
                IVectorView<ABI::Windows::Data::Json::IJsonValue* > : IVectorView_impl<ABI::Windows::Data::Json::IJsonValue* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CData__CJson__CIJsonValue, 0xcffabb0f, 0x6bc4, 0x5ff6, 0x9b,0x9e, 0x7a,0x9d,0xf6,0xc6,0x87,0xc8)
#endif
#else
typedef struct __FIVectorView_1_Windows__CData__CJson__CIJsonValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CData__CJson__CIJsonValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CData__CJson__CIJsonValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CData__CJson__CIJsonValue *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CData__CJson__CIJsonValue *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CData__CJson__CIJsonValue *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CData__CJson__CIJsonValue *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Data::Json::IJsonValue* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CData__CJson__CIJsonValue *This,
        UINT32 index,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CData__CJson__CIJsonValue *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CData__CJson__CIJsonValue *This,
        __x_ABI_CWindows_CData_CJson_CIJsonValue *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CData__CJson__CIJsonValue *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CData__CJson__CIJsonValueVtbl;

interface __FIVectorView_1_Windows__CData__CJson__CIJsonValue {
    CONST_VTBL __FIVectorView_1_Windows__CData__CJson__CIJsonValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Data::Json::IJsonValue* > methods ***/
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CData__CJson__CIJsonValue_QueryInterface(__FIVectorView_1_Windows__CData__CJson__CIJsonValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CData__CJson__CIJsonValue_AddRef(__FIVectorView_1_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CData__CJson__CIJsonValue_Release(__FIVectorView_1_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetIids(__FIVectorView_1_Windows__CData__CJson__CIJsonValue* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(__FIVectorView_1_Windows__CData__CJson__CIJsonValue* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel(__FIVectorView_1_Windows__CData__CJson__CIJsonValue* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Data::Json::IJsonValue* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetAt(__FIVectorView_1_Windows__CData__CJson__CIJsonValue* This,UINT32 index,__x_ABI_CWindows_CData_CJson_CIJsonValue **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CData__CJson__CIJsonValue_get_Size(__FIVectorView_1_Windows__CData__CJson__CIJsonValue* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CData__CJson__CIJsonValue_IndexOf(__FIVectorView_1_Windows__CData__CJson__CIJsonValue* This,__x_ABI_CWindows_CData_CJson_CIJsonValue *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetMany(__FIVectorView_1_Windows__CData__CJson__CIJsonValue* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CData_CJson_CIJsonValue **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_IJsonValue IID___FIVectorView_1_Windows__CData__CJson__CIJsonValue
#define IVectorView_IJsonValueVtbl __FIVectorView_1_Windows__CData__CJson__CIJsonValueVtbl
#define IVectorView_IJsonValue __FIVectorView_1_Windows__CData__CJson__CIJsonValue
#define IVectorView_IJsonValue_QueryInterface __FIVectorView_1_Windows__CData__CJson__CIJsonValue_QueryInterface
#define IVectorView_IJsonValue_AddRef __FIVectorView_1_Windows__CData__CJson__CIJsonValue_AddRef
#define IVectorView_IJsonValue_Release __FIVectorView_1_Windows__CData__CJson__CIJsonValue_Release
#define IVectorView_IJsonValue_GetIids __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetIids
#define IVectorView_IJsonValue_GetRuntimeClassName __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName
#define IVectorView_IJsonValue_GetTrustLevel __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel
#define IVectorView_IJsonValue_GetAt __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetAt
#define IVectorView_IJsonValue_get_Size __FIVectorView_1_Windows__CData__CJson__CIJsonValue_get_Size
#define IVectorView_IJsonValue_IndexOf __FIVectorView_1_Windows__CData__CJson__CIJsonValue_IndexOf
#define IVectorView_IJsonValue_GetMany __FIVectorView_1_Windows__CData__CJson__CIJsonValue_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Data::Json::IJsonValue* > interface
 */
#ifndef ____FIVector_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CData__CJson__CIJsonValue, 0xd44662bc, 0xdce3, 0x59a8, 0x92,0x72, 0x4b,0x21,0x0f,0x33,0x90,0x8b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("d44662bc-dce3-59a8-9272-4b210f33908b")
                IVector<ABI::Windows::Data::Json::IJsonValue* > : IVector_impl<ABI::Windows::Data::Json::IJsonValue* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CData__CJson__CIJsonValue, 0xd44662bc, 0xdce3, 0x59a8, 0x92,0x72, 0x4b,0x21,0x0f,0x33,0x90,0x8b)
#endif
#else
typedef struct __FIVector_1_Windows__CData__CJson__CIJsonValueVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Data::Json::IJsonValue* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        UINT32 index,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        __FIVectorView_1_Windows__CData__CJson__CIJsonValue **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        __x_ABI_CWindows_CData_CJson_CIJsonValue *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        UINT32 index,
        __x_ABI_CWindows_CData_CJson_CIJsonValue *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        UINT32 index,
        __x_ABI_CWindows_CData_CJson_CIJsonValue *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        __x_ABI_CWindows_CData_CJson_CIJsonValue *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CData__CJson__CIJsonValue *This,
        UINT32 count,
        __x_ABI_CWindows_CData_CJson_CIJsonValue **items);

    END_INTERFACE
} __FIVector_1_Windows__CData__CJson__CIJsonValueVtbl;

interface __FIVector_1_Windows__CData__CJson__CIJsonValue {
    CONST_VTBL __FIVector_1_Windows__CData__CJson__CIJsonValueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Data::Json::IJsonValue* > methods ***/
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CData__CJson__CIJsonValue_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_QueryInterface(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CData__CJson__CIJsonValue_AddRef(__FIVector_1_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CData__CJson__CIJsonValue_Release(__FIVector_1_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_GetIids(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Data::Json::IJsonValue* > methods ***/
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_GetAt(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,UINT32 index,__x_ABI_CWindows_CData_CJson_CIJsonValue **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_get_Size(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_GetView(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,__FIVectorView_1_Windows__CData__CJson__CIJsonValue **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_IndexOf(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,__x_ABI_CWindows_CData_CJson_CIJsonValue *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_SetAt(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,UINT32 index,__x_ABI_CWindows_CData_CJson_CIJsonValue *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_InsertAt(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,UINT32 index,__x_ABI_CWindows_CData_CJson_CIJsonValue *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_RemoveAt(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_Append(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,__x_ABI_CWindows_CData_CJson_CIJsonValue *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_RemoveAtEnd(__FIVector_1_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_Clear(__FIVector_1_Windows__CData__CJson__CIJsonValue* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_GetMany(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CData_CJson_CIJsonValue **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CData__CJson__CIJsonValue_ReplaceAll(__FIVector_1_Windows__CData__CJson__CIJsonValue* This,UINT32 count,__x_ABI_CWindows_CData_CJson_CIJsonValue **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_IJsonValue IID___FIVector_1_Windows__CData__CJson__CIJsonValue
#define IVector_IJsonValueVtbl __FIVector_1_Windows__CData__CJson__CIJsonValueVtbl
#define IVector_IJsonValue __FIVector_1_Windows__CData__CJson__CIJsonValue
#define IVector_IJsonValue_QueryInterface __FIVector_1_Windows__CData__CJson__CIJsonValue_QueryInterface
#define IVector_IJsonValue_AddRef __FIVector_1_Windows__CData__CJson__CIJsonValue_AddRef
#define IVector_IJsonValue_Release __FIVector_1_Windows__CData__CJson__CIJsonValue_Release
#define IVector_IJsonValue_GetIids __FIVector_1_Windows__CData__CJson__CIJsonValue_GetIids
#define IVector_IJsonValue_GetRuntimeClassName __FIVector_1_Windows__CData__CJson__CIJsonValue_GetRuntimeClassName
#define IVector_IJsonValue_GetTrustLevel __FIVector_1_Windows__CData__CJson__CIJsonValue_GetTrustLevel
#define IVector_IJsonValue_GetAt __FIVector_1_Windows__CData__CJson__CIJsonValue_GetAt
#define IVector_IJsonValue_get_Size __FIVector_1_Windows__CData__CJson__CIJsonValue_get_Size
#define IVector_IJsonValue_GetView __FIVector_1_Windows__CData__CJson__CIJsonValue_GetView
#define IVector_IJsonValue_IndexOf __FIVector_1_Windows__CData__CJson__CIJsonValue_IndexOf
#define IVector_IJsonValue_SetAt __FIVector_1_Windows__CData__CJson__CIJsonValue_SetAt
#define IVector_IJsonValue_InsertAt __FIVector_1_Windows__CData__CJson__CIJsonValue_InsertAt
#define IVector_IJsonValue_RemoveAt __FIVector_1_Windows__CData__CJson__CIJsonValue_RemoveAt
#define IVector_IJsonValue_Append __FIVector_1_Windows__CData__CJson__CIJsonValue_Append
#define IVector_IJsonValue_RemoveAtEnd __FIVector_1_Windows__CData__CJson__CIJsonValue_RemoveAtEnd
#define IVector_IJsonValue_Clear __FIVector_1_Windows__CData__CJson__CIJsonValue_Clear
#define IVector_IJsonValue_GetMany __FIVector_1_Windows__CData__CJson__CIJsonValue_GetMany
#define IVector_IJsonValue_ReplaceAll __FIVector_1_Windows__CData__CJson__CIJsonValue_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CData__CJson__CIJsonValue_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_data_json_h__ */
