/*** Autogenerated by WIDL 10.12 from include/iwscapi.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __iwscapi_h__
#define __iwscapi_h__

/* Forward declarations */

#ifndef __IWscProduct_FWD_DEFINED__
#define __IWscProduct_FWD_DEFINED__
typedef interface IWscProduct IWscProduct;
#ifdef __cplusplus
interface IWscProduct;
#endif /* __cplusplus */
#endif

#ifndef __IWscProduct2_FWD_DEFINED__
#define __IWscProduct2_FWD_DEFINED__
typedef interface IWscProduct2 IWscProduct2;
#ifdef __cplusplus
interface IWscProduct2;
#endif /* __cplusplus */
#endif

#ifndef __IWscProduct3_FWD_DEFINED__
#define __IWscProduct3_FWD_DEFINED__
typedef interface IWscProduct3 IWscProduct3;
#ifdef __cplusplus
interface IWscProduct3;
#endif /* __cplusplus */
#endif

#ifndef __IWSCProductList_FWD_DEFINED__
#define __IWSCProductList_FWD_DEFINED__
typedef interface IWSCProductList IWSCProductList;
#ifdef __cplusplus
interface IWSCProductList;
#endif /* __cplusplus */
#endif

#ifndef __IWSCDefaultProduct_FWD_DEFINED__
#define __IWSCDefaultProduct_FWD_DEFINED__
typedef interface IWSCDefaultProduct IWSCDefaultProduct;
#ifdef __cplusplus
interface IWSCDefaultProduct;
#endif /* __cplusplus */
#endif

#ifndef __WSCProductList_FWD_DEFINED__
#define __WSCProductList_FWD_DEFINED__
#ifdef __cplusplus
typedef class WSCProductList WSCProductList;
#else
typedef struct WSCProductList WSCProductList;
#endif /* defined __cplusplus */
#endif /* defined __WSCProductList_FWD_DEFINED__ */

#ifndef __WSCDefaultProduct_FWD_DEFINED__
#define __WSCDefaultProduct_FWD_DEFINED__
#ifdef __cplusplus
typedef class WSCDefaultProduct WSCDefaultProduct;
#else
typedef struct WSCDefaultProduct WSCDefaultProduct;
#endif /* defined __cplusplus */
#endif /* defined __WSCDefaultProduct_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum WSC_SECURITY_PRODUCT_SUBSTATUS {
    WSC_SECURITY_PRODUCT_SUBSTATUS_NOT_SET = 0,
    WSC_SECURITY_PRODUCT_SUBSTATUS_NO_ACTION = 1,
    WSC_SECURITY_PRODUCT_SUBSTATUS_ACTION_RECOMMENDED = 2,
    WSC_SECURITY_PRODUCT_SUBSTATUS_ACTION_NEEDED = 3
} WSC_SECURITY_PRODUCT_SUBSTATUS;
typedef enum WSC_SECURITY_PRODUCT_STATE {
    WSC_SECURITY_PRODUCT_STATE_ON = 0,
    WSC_SECURITY_PRODUCT_STATE_OFF = 1,
    WSC_SECURITY_PRODUCT_STATE_SNOOZED = 2,
    WSC_SECURITY_PRODUCT_STATE_EXPIRED = 3
} WSC_SECURITY_PRODUCT_STATE;
typedef enum _SECURITY_PRODUCT_TYPE {
    SECURITY_PRODUCT_TYPE_ANTIVIRUS = 0,
    SECURITY_PRODUCT_TYPE_FIREWALL = 1,
    SECURITY_PRODUCT_TYPE_ANTISPYWARE = 2
} SECURITY_PRODUCT_TYPE;
typedef enum _WSC_SECURITY_SIGNATURE_STATUS {
    WSC_SECURITY_PRODUCT_OUT_OF_DATE = 0,
    WSC_SECURITY_PRODUCT_UP_TO_DATE = 1
} WSC_SECURITY_SIGNATURE_STATUS;
/*****************************************************************************
 * IWscProduct interface
 */
#ifndef __IWscProduct_INTERFACE_DEFINED__
#define __IWscProduct_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWscProduct, 0x8c38232e, 0x3a45, 0x4a27, 0x92,0xb0, 0x1a,0x16,0xa9,0x75,0xf6,0x69);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8c38232e-3a45-4a27-92b0-1a16a975f669")
IWscProduct : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_ProductName(
        BSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ProductState(
        WSC_SECURITY_PRODUCT_STATE *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SignatureStatus(
        WSC_SECURITY_SIGNATURE_STATUS *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_RemediationPath(
        BSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ProductStateTimestamp(
        BSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ProductGuid(
        BSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ProductIsDefault(
        WINBOOL *val) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWscProduct, 0x8c38232e, 0x3a45, 0x4a27, 0x92,0xb0, 0x1a,0x16,0xa9,0x75,0xf6,0x69)
#endif
#else
typedef struct IWscProductVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWscProduct *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWscProduct *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWscProduct *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWscProduct *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWscProduct *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWscProduct *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWscProduct *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWscProduct methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ProductName)(
        IWscProduct *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductState)(
        IWscProduct *This,
        WSC_SECURITY_PRODUCT_STATE *val);

    HRESULT (STDMETHODCALLTYPE *get_SignatureStatus)(
        IWscProduct *This,
        WSC_SECURITY_SIGNATURE_STATUS *val);

    HRESULT (STDMETHODCALLTYPE *get_RemediationPath)(
        IWscProduct *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductStateTimestamp)(
        IWscProduct *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductGuid)(
        IWscProduct *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductIsDefault)(
        IWscProduct *This,
        WINBOOL *val);

    END_INTERFACE
} IWscProductVtbl;

interface IWscProduct {
    CONST_VTBL IWscProductVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWscProduct_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWscProduct_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWscProduct_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWscProduct_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWscProduct_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWscProduct_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWscProduct_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWscProduct methods ***/
#define IWscProduct_get_ProductName(This,val) (This)->lpVtbl->get_ProductName(This,val)
#define IWscProduct_get_ProductState(This,val) (This)->lpVtbl->get_ProductState(This,val)
#define IWscProduct_get_SignatureStatus(This,val) (This)->lpVtbl->get_SignatureStatus(This,val)
#define IWscProduct_get_RemediationPath(This,val) (This)->lpVtbl->get_RemediationPath(This,val)
#define IWscProduct_get_ProductStateTimestamp(This,val) (This)->lpVtbl->get_ProductStateTimestamp(This,val)
#define IWscProduct_get_ProductGuid(This,val) (This)->lpVtbl->get_ProductGuid(This,val)
#define IWscProduct_get_ProductIsDefault(This,val) (This)->lpVtbl->get_ProductIsDefault(This,val)
#else
/*** IUnknown methods ***/
static inline HRESULT IWscProduct_QueryInterface(IWscProduct* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWscProduct_AddRef(IWscProduct* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWscProduct_Release(IWscProduct* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWscProduct_GetTypeInfoCount(IWscProduct* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWscProduct_GetTypeInfo(IWscProduct* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWscProduct_GetIDsOfNames(IWscProduct* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWscProduct_Invoke(IWscProduct* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWscProduct methods ***/
static inline HRESULT IWscProduct_get_ProductName(IWscProduct* This,BSTR *val) {
    return This->lpVtbl->get_ProductName(This,val);
}
static inline HRESULT IWscProduct_get_ProductState(IWscProduct* This,WSC_SECURITY_PRODUCT_STATE *val) {
    return This->lpVtbl->get_ProductState(This,val);
}
static inline HRESULT IWscProduct_get_SignatureStatus(IWscProduct* This,WSC_SECURITY_SIGNATURE_STATUS *val) {
    return This->lpVtbl->get_SignatureStatus(This,val);
}
static inline HRESULT IWscProduct_get_RemediationPath(IWscProduct* This,BSTR *val) {
    return This->lpVtbl->get_RemediationPath(This,val);
}
static inline HRESULT IWscProduct_get_ProductStateTimestamp(IWscProduct* This,BSTR *val) {
    return This->lpVtbl->get_ProductStateTimestamp(This,val);
}
static inline HRESULT IWscProduct_get_ProductGuid(IWscProduct* This,BSTR *val) {
    return This->lpVtbl->get_ProductGuid(This,val);
}
static inline HRESULT IWscProduct_get_ProductIsDefault(IWscProduct* This,WINBOOL *val) {
    return This->lpVtbl->get_ProductIsDefault(This,val);
}
#endif
#endif

#endif


#endif  /* __IWscProduct_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWscProduct2 interface
 */
#ifndef __IWscProduct2_INTERFACE_DEFINED__
#define __IWscProduct2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWscProduct2, 0xf896ca54, 0xfe09, 0x4403, 0x86,0xd4, 0x23,0xcb,0x48,0x8d,0x81,0xd8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f896ca54-fe09-4403-86d4-23cb488d81d8")
IWscProduct2 : public IWscProduct
{
    virtual HRESULT STDMETHODCALLTYPE get_AntivirusScanSubstatus(
        WSC_SECURITY_PRODUCT_SUBSTATUS *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AntivirusSettingsSubstatus(
        WSC_SECURITY_PRODUCT_SUBSTATUS *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_AntivirusProtectionUpdateSubstatus(
        WSC_SECURITY_PRODUCT_SUBSTATUS *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FirewallDomainProfileSubstatus(
        WSC_SECURITY_PRODUCT_SUBSTATUS *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FirewallPrivateProfileSubstatus(
        WSC_SECURITY_PRODUCT_SUBSTATUS *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FirewallPublicProfileSubstatus(
        WSC_SECURITY_PRODUCT_SUBSTATUS *status) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWscProduct2, 0xf896ca54, 0xfe09, 0x4403, 0x86,0xd4, 0x23,0xcb,0x48,0x8d,0x81,0xd8)
#endif
#else
typedef struct IWscProduct2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWscProduct2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWscProduct2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWscProduct2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWscProduct2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWscProduct2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWscProduct2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWscProduct2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWscProduct methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ProductName)(
        IWscProduct2 *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductState)(
        IWscProduct2 *This,
        WSC_SECURITY_PRODUCT_STATE *val);

    HRESULT (STDMETHODCALLTYPE *get_SignatureStatus)(
        IWscProduct2 *This,
        WSC_SECURITY_SIGNATURE_STATUS *val);

    HRESULT (STDMETHODCALLTYPE *get_RemediationPath)(
        IWscProduct2 *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductStateTimestamp)(
        IWscProduct2 *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductGuid)(
        IWscProduct2 *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductIsDefault)(
        IWscProduct2 *This,
        WINBOOL *val);

    /*** IWscProduct2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AntivirusScanSubstatus)(
        IWscProduct2 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    HRESULT (STDMETHODCALLTYPE *get_AntivirusSettingsSubstatus)(
        IWscProduct2 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    HRESULT (STDMETHODCALLTYPE *get_AntivirusProtectionUpdateSubstatus)(
        IWscProduct2 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    HRESULT (STDMETHODCALLTYPE *get_FirewallDomainProfileSubstatus)(
        IWscProduct2 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    HRESULT (STDMETHODCALLTYPE *get_FirewallPrivateProfileSubstatus)(
        IWscProduct2 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    HRESULT (STDMETHODCALLTYPE *get_FirewallPublicProfileSubstatus)(
        IWscProduct2 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    END_INTERFACE
} IWscProduct2Vtbl;

interface IWscProduct2 {
    CONST_VTBL IWscProduct2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWscProduct2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWscProduct2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWscProduct2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWscProduct2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWscProduct2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWscProduct2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWscProduct2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWscProduct methods ***/
#define IWscProduct2_get_ProductName(This,val) (This)->lpVtbl->get_ProductName(This,val)
#define IWscProduct2_get_ProductState(This,val) (This)->lpVtbl->get_ProductState(This,val)
#define IWscProduct2_get_SignatureStatus(This,val) (This)->lpVtbl->get_SignatureStatus(This,val)
#define IWscProduct2_get_RemediationPath(This,val) (This)->lpVtbl->get_RemediationPath(This,val)
#define IWscProduct2_get_ProductStateTimestamp(This,val) (This)->lpVtbl->get_ProductStateTimestamp(This,val)
#define IWscProduct2_get_ProductGuid(This,val) (This)->lpVtbl->get_ProductGuid(This,val)
#define IWscProduct2_get_ProductIsDefault(This,val) (This)->lpVtbl->get_ProductIsDefault(This,val)
/*** IWscProduct2 methods ***/
#define IWscProduct2_get_AntivirusScanSubstatus(This,status) (This)->lpVtbl->get_AntivirusScanSubstatus(This,status)
#define IWscProduct2_get_AntivirusSettingsSubstatus(This,status) (This)->lpVtbl->get_AntivirusSettingsSubstatus(This,status)
#define IWscProduct2_get_AntivirusProtectionUpdateSubstatus(This,status) (This)->lpVtbl->get_AntivirusProtectionUpdateSubstatus(This,status)
#define IWscProduct2_get_FirewallDomainProfileSubstatus(This,status) (This)->lpVtbl->get_FirewallDomainProfileSubstatus(This,status)
#define IWscProduct2_get_FirewallPrivateProfileSubstatus(This,status) (This)->lpVtbl->get_FirewallPrivateProfileSubstatus(This,status)
#define IWscProduct2_get_FirewallPublicProfileSubstatus(This,status) (This)->lpVtbl->get_FirewallPublicProfileSubstatus(This,status)
#else
/*** IUnknown methods ***/
static inline HRESULT IWscProduct2_QueryInterface(IWscProduct2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWscProduct2_AddRef(IWscProduct2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWscProduct2_Release(IWscProduct2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWscProduct2_GetTypeInfoCount(IWscProduct2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWscProduct2_GetTypeInfo(IWscProduct2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWscProduct2_GetIDsOfNames(IWscProduct2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWscProduct2_Invoke(IWscProduct2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWscProduct methods ***/
static inline HRESULT IWscProduct2_get_ProductName(IWscProduct2* This,BSTR *val) {
    return This->lpVtbl->get_ProductName(This,val);
}
static inline HRESULT IWscProduct2_get_ProductState(IWscProduct2* This,WSC_SECURITY_PRODUCT_STATE *val) {
    return This->lpVtbl->get_ProductState(This,val);
}
static inline HRESULT IWscProduct2_get_SignatureStatus(IWscProduct2* This,WSC_SECURITY_SIGNATURE_STATUS *val) {
    return This->lpVtbl->get_SignatureStatus(This,val);
}
static inline HRESULT IWscProduct2_get_RemediationPath(IWscProduct2* This,BSTR *val) {
    return This->lpVtbl->get_RemediationPath(This,val);
}
static inline HRESULT IWscProduct2_get_ProductStateTimestamp(IWscProduct2* This,BSTR *val) {
    return This->lpVtbl->get_ProductStateTimestamp(This,val);
}
static inline HRESULT IWscProduct2_get_ProductGuid(IWscProduct2* This,BSTR *val) {
    return This->lpVtbl->get_ProductGuid(This,val);
}
static inline HRESULT IWscProduct2_get_ProductIsDefault(IWscProduct2* This,WINBOOL *val) {
    return This->lpVtbl->get_ProductIsDefault(This,val);
}
/*** IWscProduct2 methods ***/
static inline HRESULT IWscProduct2_get_AntivirusScanSubstatus(IWscProduct2* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_AntivirusScanSubstatus(This,status);
}
static inline HRESULT IWscProduct2_get_AntivirusSettingsSubstatus(IWscProduct2* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_AntivirusSettingsSubstatus(This,status);
}
static inline HRESULT IWscProduct2_get_AntivirusProtectionUpdateSubstatus(IWscProduct2* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_AntivirusProtectionUpdateSubstatus(This,status);
}
static inline HRESULT IWscProduct2_get_FirewallDomainProfileSubstatus(IWscProduct2* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_FirewallDomainProfileSubstatus(This,status);
}
static inline HRESULT IWscProduct2_get_FirewallPrivateProfileSubstatus(IWscProduct2* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_FirewallPrivateProfileSubstatus(This,status);
}
static inline HRESULT IWscProduct2_get_FirewallPublicProfileSubstatus(IWscProduct2* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_FirewallPublicProfileSubstatus(This,status);
}
#endif
#endif

#endif


#endif  /* __IWscProduct2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWscProduct3 interface
 */
#ifndef __IWscProduct3_INTERFACE_DEFINED__
#define __IWscProduct3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWscProduct3, 0x55536524, 0xd1d1, 0x4726, 0x8c,0x7c, 0x04,0x99,0x6a,0x19,0x04,0xe7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("55536524-d1d1-4726-8c7c-04996a1904e7")
IWscProduct3 : public IWscProduct2
{
    virtual HRESULT STDMETHODCALLTYPE get_AntivirusDaysUntilExpired(
        DWORD *days) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWscProduct3, 0x55536524, 0xd1d1, 0x4726, 0x8c,0x7c, 0x04,0x99,0x6a,0x19,0x04,0xe7)
#endif
#else
typedef struct IWscProduct3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWscProduct3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWscProduct3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWscProduct3 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWscProduct3 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWscProduct3 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWscProduct3 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWscProduct3 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWscProduct methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ProductName)(
        IWscProduct3 *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductState)(
        IWscProduct3 *This,
        WSC_SECURITY_PRODUCT_STATE *val);

    HRESULT (STDMETHODCALLTYPE *get_SignatureStatus)(
        IWscProduct3 *This,
        WSC_SECURITY_SIGNATURE_STATUS *val);

    HRESULT (STDMETHODCALLTYPE *get_RemediationPath)(
        IWscProduct3 *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductStateTimestamp)(
        IWscProduct3 *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductGuid)(
        IWscProduct3 *This,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_ProductIsDefault)(
        IWscProduct3 *This,
        WINBOOL *val);

    /*** IWscProduct2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AntivirusScanSubstatus)(
        IWscProduct3 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    HRESULT (STDMETHODCALLTYPE *get_AntivirusSettingsSubstatus)(
        IWscProduct3 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    HRESULT (STDMETHODCALLTYPE *get_AntivirusProtectionUpdateSubstatus)(
        IWscProduct3 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    HRESULT (STDMETHODCALLTYPE *get_FirewallDomainProfileSubstatus)(
        IWscProduct3 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    HRESULT (STDMETHODCALLTYPE *get_FirewallPrivateProfileSubstatus)(
        IWscProduct3 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    HRESULT (STDMETHODCALLTYPE *get_FirewallPublicProfileSubstatus)(
        IWscProduct3 *This,
        WSC_SECURITY_PRODUCT_SUBSTATUS *status);

    /*** IWscProduct3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AntivirusDaysUntilExpired)(
        IWscProduct3 *This,
        DWORD *days);

    END_INTERFACE
} IWscProduct3Vtbl;

interface IWscProduct3 {
    CONST_VTBL IWscProduct3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWscProduct3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWscProduct3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWscProduct3_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWscProduct3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWscProduct3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWscProduct3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWscProduct3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWscProduct methods ***/
#define IWscProduct3_get_ProductName(This,val) (This)->lpVtbl->get_ProductName(This,val)
#define IWscProduct3_get_ProductState(This,val) (This)->lpVtbl->get_ProductState(This,val)
#define IWscProduct3_get_SignatureStatus(This,val) (This)->lpVtbl->get_SignatureStatus(This,val)
#define IWscProduct3_get_RemediationPath(This,val) (This)->lpVtbl->get_RemediationPath(This,val)
#define IWscProduct3_get_ProductStateTimestamp(This,val) (This)->lpVtbl->get_ProductStateTimestamp(This,val)
#define IWscProduct3_get_ProductGuid(This,val) (This)->lpVtbl->get_ProductGuid(This,val)
#define IWscProduct3_get_ProductIsDefault(This,val) (This)->lpVtbl->get_ProductIsDefault(This,val)
/*** IWscProduct2 methods ***/
#define IWscProduct3_get_AntivirusScanSubstatus(This,status) (This)->lpVtbl->get_AntivirusScanSubstatus(This,status)
#define IWscProduct3_get_AntivirusSettingsSubstatus(This,status) (This)->lpVtbl->get_AntivirusSettingsSubstatus(This,status)
#define IWscProduct3_get_AntivirusProtectionUpdateSubstatus(This,status) (This)->lpVtbl->get_AntivirusProtectionUpdateSubstatus(This,status)
#define IWscProduct3_get_FirewallDomainProfileSubstatus(This,status) (This)->lpVtbl->get_FirewallDomainProfileSubstatus(This,status)
#define IWscProduct3_get_FirewallPrivateProfileSubstatus(This,status) (This)->lpVtbl->get_FirewallPrivateProfileSubstatus(This,status)
#define IWscProduct3_get_FirewallPublicProfileSubstatus(This,status) (This)->lpVtbl->get_FirewallPublicProfileSubstatus(This,status)
/*** IWscProduct3 methods ***/
#define IWscProduct3_get_AntivirusDaysUntilExpired(This,days) (This)->lpVtbl->get_AntivirusDaysUntilExpired(This,days)
#else
/*** IUnknown methods ***/
static inline HRESULT IWscProduct3_QueryInterface(IWscProduct3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWscProduct3_AddRef(IWscProduct3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWscProduct3_Release(IWscProduct3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWscProduct3_GetTypeInfoCount(IWscProduct3* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWscProduct3_GetTypeInfo(IWscProduct3* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWscProduct3_GetIDsOfNames(IWscProduct3* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWscProduct3_Invoke(IWscProduct3* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWscProduct methods ***/
static inline HRESULT IWscProduct3_get_ProductName(IWscProduct3* This,BSTR *val) {
    return This->lpVtbl->get_ProductName(This,val);
}
static inline HRESULT IWscProduct3_get_ProductState(IWscProduct3* This,WSC_SECURITY_PRODUCT_STATE *val) {
    return This->lpVtbl->get_ProductState(This,val);
}
static inline HRESULT IWscProduct3_get_SignatureStatus(IWscProduct3* This,WSC_SECURITY_SIGNATURE_STATUS *val) {
    return This->lpVtbl->get_SignatureStatus(This,val);
}
static inline HRESULT IWscProduct3_get_RemediationPath(IWscProduct3* This,BSTR *val) {
    return This->lpVtbl->get_RemediationPath(This,val);
}
static inline HRESULT IWscProduct3_get_ProductStateTimestamp(IWscProduct3* This,BSTR *val) {
    return This->lpVtbl->get_ProductStateTimestamp(This,val);
}
static inline HRESULT IWscProduct3_get_ProductGuid(IWscProduct3* This,BSTR *val) {
    return This->lpVtbl->get_ProductGuid(This,val);
}
static inline HRESULT IWscProduct3_get_ProductIsDefault(IWscProduct3* This,WINBOOL *val) {
    return This->lpVtbl->get_ProductIsDefault(This,val);
}
/*** IWscProduct2 methods ***/
static inline HRESULT IWscProduct3_get_AntivirusScanSubstatus(IWscProduct3* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_AntivirusScanSubstatus(This,status);
}
static inline HRESULT IWscProduct3_get_AntivirusSettingsSubstatus(IWscProduct3* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_AntivirusSettingsSubstatus(This,status);
}
static inline HRESULT IWscProduct3_get_AntivirusProtectionUpdateSubstatus(IWscProduct3* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_AntivirusProtectionUpdateSubstatus(This,status);
}
static inline HRESULT IWscProduct3_get_FirewallDomainProfileSubstatus(IWscProduct3* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_FirewallDomainProfileSubstatus(This,status);
}
static inline HRESULT IWscProduct3_get_FirewallPrivateProfileSubstatus(IWscProduct3* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_FirewallPrivateProfileSubstatus(This,status);
}
static inline HRESULT IWscProduct3_get_FirewallPublicProfileSubstatus(IWscProduct3* This,WSC_SECURITY_PRODUCT_SUBSTATUS *status) {
    return This->lpVtbl->get_FirewallPublicProfileSubstatus(This,status);
}
/*** IWscProduct3 methods ***/
static inline HRESULT IWscProduct3_get_AntivirusDaysUntilExpired(IWscProduct3* This,DWORD *days) {
    return This->lpVtbl->get_AntivirusDaysUntilExpired(This,days);
}
#endif
#endif

#endif


#endif  /* __IWscProduct3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSCProductList interface
 */
#ifndef __IWSCProductList_INTERFACE_DEFINED__
#define __IWSCProductList_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSCProductList, 0x722a338c, 0x6e8e, 0x4e72, 0xac,0x27, 0x14,0x17,0xfb,0x0c,0x81,0xc2);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("722a338c-6e8e-4e72-ac27-1417fb0c81c2")
IWSCProductList : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        ULONG provider) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Count(
        LONG *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        ULONG index,
        IWscProduct **val) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSCProductList, 0x722a338c, 0x6e8e, 0x4e72, 0xac,0x27, 0x14,0x17,0xfb,0x0c,0x81,0xc2)
#endif
#else
typedef struct IWSCProductListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSCProductList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSCProductList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSCProductList *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSCProductList *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSCProductList *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSCProductList *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSCProductList *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSCProductList methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IWSCProductList *This,
        ULONG provider);

    HRESULT (STDMETHODCALLTYPE *get_Count)(
        IWSCProductList *This,
        LONG *val);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IWSCProductList *This,
        ULONG index,
        IWscProduct **val);

    END_INTERFACE
} IWSCProductListVtbl;

interface IWSCProductList {
    CONST_VTBL IWSCProductListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSCProductList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSCProductList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSCProductList_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSCProductList_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSCProductList_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSCProductList_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSCProductList_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSCProductList methods ***/
#define IWSCProductList_Initialize(This,provider) (This)->lpVtbl->Initialize(This,provider)
#define IWSCProductList_get_Count(This,val) (This)->lpVtbl->get_Count(This,val)
#define IWSCProductList_get_Item(This,index,val) (This)->lpVtbl->get_Item(This,index,val)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSCProductList_QueryInterface(IWSCProductList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSCProductList_AddRef(IWSCProductList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSCProductList_Release(IWSCProductList* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSCProductList_GetTypeInfoCount(IWSCProductList* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSCProductList_GetTypeInfo(IWSCProductList* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSCProductList_GetIDsOfNames(IWSCProductList* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSCProductList_Invoke(IWSCProductList* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSCProductList methods ***/
static inline HRESULT IWSCProductList_Initialize(IWSCProductList* This,ULONG provider) {
    return This->lpVtbl->Initialize(This,provider);
}
static inline HRESULT IWSCProductList_get_Count(IWSCProductList* This,LONG *val) {
    return This->lpVtbl->get_Count(This,val);
}
static inline HRESULT IWSCProductList_get_Item(IWSCProductList* This,ULONG index,IWscProduct **val) {
    return This->lpVtbl->get_Item(This,index,val);
}
#endif
#endif

#endif


#endif  /* __IWSCProductList_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSCDefaultProduct interface
 */
#ifndef __IWSCDefaultProduct_INTERFACE_DEFINED__
#define __IWSCDefaultProduct_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSCDefaultProduct, 0x0476d69c, 0xf21a, 0x11e5, 0x9c,0xe9, 0x5e,0x55,0x17,0x50,0x7c,0x66);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0476d69c-f21a-11e5-9ce9-5e5517507c66")
IWSCDefaultProduct : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE SetDefaultProduct(
        SECURITY_PRODUCT_TYPE type,
        BSTR guid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSCDefaultProduct, 0x0476d69c, 0xf21a, 0x11e5, 0x9c,0xe9, 0x5e,0x55,0x17,0x50,0x7c,0x66)
#endif
#else
typedef struct IWSCDefaultProductVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSCDefaultProduct *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSCDefaultProduct *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSCDefaultProduct *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWSCDefaultProduct *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWSCDefaultProduct *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWSCDefaultProduct *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWSCDefaultProduct *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWSCDefaultProduct methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDefaultProduct)(
        IWSCDefaultProduct *This,
        SECURITY_PRODUCT_TYPE type,
        BSTR guid);

    END_INTERFACE
} IWSCDefaultProductVtbl;

interface IWSCDefaultProduct {
    CONST_VTBL IWSCDefaultProductVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSCDefaultProduct_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSCDefaultProduct_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSCDefaultProduct_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWSCDefaultProduct_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWSCDefaultProduct_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWSCDefaultProduct_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWSCDefaultProduct_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWSCDefaultProduct methods ***/
#define IWSCDefaultProduct_SetDefaultProduct(This,type,guid) (This)->lpVtbl->SetDefaultProduct(This,type,guid)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSCDefaultProduct_QueryInterface(IWSCDefaultProduct* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSCDefaultProduct_AddRef(IWSCDefaultProduct* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSCDefaultProduct_Release(IWSCDefaultProduct* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWSCDefaultProduct_GetTypeInfoCount(IWSCDefaultProduct* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWSCDefaultProduct_GetTypeInfo(IWSCDefaultProduct* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWSCDefaultProduct_GetIDsOfNames(IWSCDefaultProduct* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWSCDefaultProduct_Invoke(IWSCDefaultProduct* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWSCDefaultProduct methods ***/
static inline HRESULT IWSCDefaultProduct_SetDefaultProduct(IWSCDefaultProduct* This,SECURITY_PRODUCT_TYPE type,BSTR guid) {
    return This->lpVtbl->SetDefaultProduct(This,type,guid);
}
#endif
#endif

#endif


#endif  /* __IWSCDefaultProduct_INTERFACE_DEFINED__ */

#ifndef __wscAPILib_LIBRARY_DEFINED__
#define __wscAPILib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_wscAPILib, 0xb52a4496, 0x7753, 0x4f74, 0xbe,0x64, 0xc2,0x07,0x2e,0x30,0x81,0x22);

/*****************************************************************************
 * WSCProductList coclass
 */

DEFINE_GUID(CLSID_WSCProductList, 0x17072f7b, 0x9abe, 0x4a74, 0xa2,0x61, 0x1e,0xb7,0x6b,0x55,0x10,0x7a);

#ifdef __cplusplus
class DECLSPEC_UUID("17072f7b-9abe-4a74-a261-1eb76b55107a") WSCProductList;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WSCProductList, 0x17072f7b, 0x9abe, 0x4a74, 0xa2,0x61, 0x1e,0xb7,0x6b,0x55,0x10,0x7a)
#endif
#endif

/*****************************************************************************
 * WSCDefaultProduct coclass
 */

DEFINE_GUID(CLSID_WSCDefaultProduct, 0x2981a36e, 0xf22d, 0x11e5, 0x9c,0xe9, 0x5e,0x55,0x17,0x50,0x7c,0x66);

#ifdef __cplusplus
class DECLSPEC_UUID("2981a36e-f22d-11e5-9ce9-5e5517507c66") WSCDefaultProduct;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WSCDefaultProduct, 0x2981a36e, 0xf22d, 0x11e5, 0x9c,0xe9, 0x5e,0x55,0x17,0x50,0x7c,0x66)
#endif
#endif

#endif /* __wscAPILib_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __iwscapi_h__ */
