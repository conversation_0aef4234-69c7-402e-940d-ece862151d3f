/*** Autogenerated by WIDL 10.12 from include/dxgi1_4.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dxgi1_4_h__
#define __dxgi1_4_h__

/* Forward declarations */

#ifndef __IDXGISwapChain3_FWD_DEFINED__
#define __IDXGISwapChain3_FWD_DEFINED__
typedef interface IDXGISwapChain3 IDXGISwapChain3;
#ifdef __cplusplus
interface IDXGISwapChain3;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIOutput4_FWD_DEFINED__
#define __IDXGIOutput4_FWD_DEFINED__
typedef interface IDXGIOutput4 IDXGIOutput4;
#ifdef __cplusplus
interface IDXGIOutput4;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIFactory4_FWD_DEFINED__
#define __IDXGIFactory4_FWD_DEFINED__
typedef interface IDXGIFactory4 IDXGIFactory4;
#ifdef __cplusplus
interface IDXGIFactory4;
#endif /* __cplusplus */
#endif

#ifndef __IDXGIAdapter3_FWD_DEFINED__
#define __IDXGIAdapter3_FWD_DEFINED__
typedef interface IDXGIAdapter3 IDXGIAdapter3;
#ifdef __cplusplus
interface IDXGIAdapter3;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <dxgi1_3.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum DXGI_SWAP_CHAIN_COLOR_SPACE_SUPPORT_FLAG {
    DXGI_SWAP_CHAIN_COLOR_SPACE_SUPPORT_FLAG_PRESENT = 0x1,
    DXGI_SWAP_CHAIN_COLOR_SPACE_SUPPORT_FLAG_OVERLAY_PRESENT = 0x2
} DXGI_SWAP_CHAIN_COLOR_SPACE_SUPPORT_FLAG;
typedef enum DXGI_OVERLAY_COLOR_SPACE_SUPPORT_FLAG {
    DXGI_OVERLAY_COLOR_SPACE_SUPPORT_FLAG_PRESENT = 0x1
} DXGI_OVERLAY_COLOR_SPACE_SUPPORT_FLAG;
typedef enum DXGI_MEMORY_SEGMENT_GROUP {
    DXGI_MEMORY_SEGMENT_GROUP_LOCAL = 0x0,
    DXGI_MEMORY_SEGMENT_GROUP_NON_LOCAL = 0x1
} DXGI_MEMORY_SEGMENT_GROUP;
typedef struct DXGI_QUERY_VIDEO_MEMORY_INFO {
    UINT64 Budget;
    UINT64 CurrentUsage;
    UINT64 AvailableForReservation;
    UINT64 CurrentReservation;
} DXGI_QUERY_VIDEO_MEMORY_INFO;
/*****************************************************************************
 * IDXGISwapChain3 interface
 */
#ifndef __IDXGISwapChain3_INTERFACE_DEFINED__
#define __IDXGISwapChain3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGISwapChain3, 0x94d99bdb, 0xf1f8, 0x4ab0, 0xb2,0x36, 0x7d,0xa0,0x17,0x0e,0xda,0xb1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("94d99bdb-f1f8-4ab0-b236-7da0170edab1")
IDXGISwapChain3 : public IDXGISwapChain2
{
    virtual UINT STDMETHODCALLTYPE GetCurrentBackBufferIndex(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckColorSpaceSupport(
        DXGI_COLOR_SPACE_TYPE colour_space,
        UINT *colour_space_support) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetColorSpace1(
        DXGI_COLOR_SPACE_TYPE colour_space) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResizeBuffers1(
        UINT buffer_count,
        UINT width,
        UINT height,
        DXGI_FORMAT format,
        UINT flags,
        const UINT *node_mask,
        IUnknown *const *present_queue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGISwapChain3, 0x94d99bdb, 0xf1f8, 0x4ab0, 0xb2,0x36, 0x7d,0xa0,0x17,0x0e,0xda,0xb1)
#endif
#else
typedef struct IDXGISwapChain3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGISwapChain3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGISwapChain3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGISwapChain3 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGISwapChain3 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGISwapChain3 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGISwapChain3 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGISwapChain3 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIDeviceSubObject methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        IDXGISwapChain3 *This,
        REFIID riid,
        void **device);

    /*** IDXGISwapChain methods ***/
    HRESULT (STDMETHODCALLTYPE *Present)(
        IDXGISwapChain3 *This,
        UINT sync_interval,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetBuffer)(
        IDXGISwapChain3 *This,
        UINT buffer_idx,
        REFIID riid,
        void **surface);

    HRESULT (STDMETHODCALLTYPE *SetFullscreenState)(
        IDXGISwapChain3 *This,
        WINBOOL fullscreen,
        IDXGIOutput *target);

    HRESULT (STDMETHODCALLTYPE *GetFullscreenState)(
        IDXGISwapChain3 *This,
        WINBOOL *fullscreen,
        IDXGIOutput **target);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGISwapChain3 *This,
        DXGI_SWAP_CHAIN_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *ResizeBuffers)(
        IDXGISwapChain3 *This,
        UINT buffer_count,
        UINT width,
        UINT height,
        DXGI_FORMAT format,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *ResizeTarget)(
        IDXGISwapChain3 *This,
        const DXGI_MODE_DESC *target_mode_desc);

    HRESULT (STDMETHODCALLTYPE *GetContainingOutput)(
        IDXGISwapChain3 *This,
        IDXGIOutput **output);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGISwapChain3 *This,
        DXGI_FRAME_STATISTICS *stats);

    HRESULT (STDMETHODCALLTYPE *GetLastPresentCount)(
        IDXGISwapChain3 *This,
        UINT *last_present_count);

    /*** IDXGISwapChain1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc1)(
        IDXGISwapChain3 *This,
        DXGI_SWAP_CHAIN_DESC1 *pDesc);

    HRESULT (STDMETHODCALLTYPE *GetFullscreenDesc)(
        IDXGISwapChain3 *This,
        DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pDesc);

    HRESULT (STDMETHODCALLTYPE *GetHwnd)(
        IDXGISwapChain3 *This,
        HWND *pHwnd);

    HRESULT (STDMETHODCALLTYPE *GetCoreWindow)(
        IDXGISwapChain3 *This,
        REFIID refiid,
        void **ppUnk);

    HRESULT (STDMETHODCALLTYPE *Present1)(
        IDXGISwapChain3 *This,
        UINT SyncInterval,
        UINT PresentFlags,
        const DXGI_PRESENT_PARAMETERS *pPresentParameters);

    WINBOOL (STDMETHODCALLTYPE *IsTemporaryMonoSupported)(
        IDXGISwapChain3 *This);

    HRESULT (STDMETHODCALLTYPE *GetRestrictToOutput)(
        IDXGISwapChain3 *This,
        IDXGIOutput **ppRestrictToOutput);

    HRESULT (STDMETHODCALLTYPE *SetBackgroundColor)(
        IDXGISwapChain3 *This,
        const DXGI_RGBA *pColor);

    HRESULT (STDMETHODCALLTYPE *GetBackgroundColor)(
        IDXGISwapChain3 *This,
        DXGI_RGBA *pColor);

    HRESULT (STDMETHODCALLTYPE *SetRotation)(
        IDXGISwapChain3 *This,
        DXGI_MODE_ROTATION Rotation);

    HRESULT (STDMETHODCALLTYPE *GetRotation)(
        IDXGISwapChain3 *This,
        DXGI_MODE_ROTATION *pRotation);

    /*** IDXGISwapChain2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetSourceSize)(
        IDXGISwapChain3 *This,
        UINT width,
        UINT height);

    HRESULT (STDMETHODCALLTYPE *GetSourceSize)(
        IDXGISwapChain3 *This,
        UINT *width,
        UINT *height);

    HRESULT (STDMETHODCALLTYPE *SetMaximumFrameLatency)(
        IDXGISwapChain3 *This,
        UINT max_latency);

    HRESULT (STDMETHODCALLTYPE *GetMaximumFrameLatency)(
        IDXGISwapChain3 *This,
        UINT *max_latency);

    HANDLE (STDMETHODCALLTYPE *GetFrameLatencyWaitableObject)(
        IDXGISwapChain3 *This);

    HRESULT (STDMETHODCALLTYPE *SetMatrixTransform)(
        IDXGISwapChain3 *This,
        const DXGI_MATRIX_3X2_F *matrix);

    HRESULT (STDMETHODCALLTYPE *GetMatrixTransform)(
        IDXGISwapChain3 *This,
        DXGI_MATRIX_3X2_F *matrix);

    /*** IDXGISwapChain3 methods ***/
    UINT (STDMETHODCALLTYPE *GetCurrentBackBufferIndex)(
        IDXGISwapChain3 *This);

    HRESULT (STDMETHODCALLTYPE *CheckColorSpaceSupport)(
        IDXGISwapChain3 *This,
        DXGI_COLOR_SPACE_TYPE colour_space,
        UINT *colour_space_support);

    HRESULT (STDMETHODCALLTYPE *SetColorSpace1)(
        IDXGISwapChain3 *This,
        DXGI_COLOR_SPACE_TYPE colour_space);

    HRESULT (STDMETHODCALLTYPE *ResizeBuffers1)(
        IDXGISwapChain3 *This,
        UINT buffer_count,
        UINT width,
        UINT height,
        DXGI_FORMAT format,
        UINT flags,
        const UINT *node_mask,
        IUnknown *const *present_queue);

    END_INTERFACE
} IDXGISwapChain3Vtbl;

interface IDXGISwapChain3 {
    CONST_VTBL IDXGISwapChain3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGISwapChain3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGISwapChain3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGISwapChain3_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGISwapChain3_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGISwapChain3_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGISwapChain3_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGISwapChain3_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIDeviceSubObject methods ***/
#define IDXGISwapChain3_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** IDXGISwapChain methods ***/
#define IDXGISwapChain3_Present(This,sync_interval,flags) (This)->lpVtbl->Present(This,sync_interval,flags)
#define IDXGISwapChain3_GetBuffer(This,buffer_idx,riid,surface) (This)->lpVtbl->GetBuffer(This,buffer_idx,riid,surface)
#define IDXGISwapChain3_SetFullscreenState(This,fullscreen,target) (This)->lpVtbl->SetFullscreenState(This,fullscreen,target)
#define IDXGISwapChain3_GetFullscreenState(This,fullscreen,target) (This)->lpVtbl->GetFullscreenState(This,fullscreen,target)
#define IDXGISwapChain3_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGISwapChain3_ResizeBuffers(This,buffer_count,width,height,format,flags) (This)->lpVtbl->ResizeBuffers(This,buffer_count,width,height,format,flags)
#define IDXGISwapChain3_ResizeTarget(This,target_mode_desc) (This)->lpVtbl->ResizeTarget(This,target_mode_desc)
#define IDXGISwapChain3_GetContainingOutput(This,output) (This)->lpVtbl->GetContainingOutput(This,output)
#define IDXGISwapChain3_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
#define IDXGISwapChain3_GetLastPresentCount(This,last_present_count) (This)->lpVtbl->GetLastPresentCount(This,last_present_count)
/*** IDXGISwapChain1 methods ***/
#define IDXGISwapChain3_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
#define IDXGISwapChain3_GetFullscreenDesc(This,pDesc) (This)->lpVtbl->GetFullscreenDesc(This,pDesc)
#define IDXGISwapChain3_GetHwnd(This,pHwnd) (This)->lpVtbl->GetHwnd(This,pHwnd)
#define IDXGISwapChain3_GetCoreWindow(This,refiid,ppUnk) (This)->lpVtbl->GetCoreWindow(This,refiid,ppUnk)
#define IDXGISwapChain3_Present1(This,SyncInterval,PresentFlags,pPresentParameters) (This)->lpVtbl->Present1(This,SyncInterval,PresentFlags,pPresentParameters)
#define IDXGISwapChain3_IsTemporaryMonoSupported(This) (This)->lpVtbl->IsTemporaryMonoSupported(This)
#define IDXGISwapChain3_GetRestrictToOutput(This,ppRestrictToOutput) (This)->lpVtbl->GetRestrictToOutput(This,ppRestrictToOutput)
#define IDXGISwapChain3_SetBackgroundColor(This,pColor) (This)->lpVtbl->SetBackgroundColor(This,pColor)
#define IDXGISwapChain3_GetBackgroundColor(This,pColor) (This)->lpVtbl->GetBackgroundColor(This,pColor)
#define IDXGISwapChain3_SetRotation(This,Rotation) (This)->lpVtbl->SetRotation(This,Rotation)
#define IDXGISwapChain3_GetRotation(This,pRotation) (This)->lpVtbl->GetRotation(This,pRotation)
/*** IDXGISwapChain2 methods ***/
#define IDXGISwapChain3_SetSourceSize(This,width,height) (This)->lpVtbl->SetSourceSize(This,width,height)
#define IDXGISwapChain3_GetSourceSize(This,width,height) (This)->lpVtbl->GetSourceSize(This,width,height)
#define IDXGISwapChain3_SetMaximumFrameLatency(This,max_latency) (This)->lpVtbl->SetMaximumFrameLatency(This,max_latency)
#define IDXGISwapChain3_GetMaximumFrameLatency(This,max_latency) (This)->lpVtbl->GetMaximumFrameLatency(This,max_latency)
#define IDXGISwapChain3_GetFrameLatencyWaitableObject(This) (This)->lpVtbl->GetFrameLatencyWaitableObject(This)
#define IDXGISwapChain3_SetMatrixTransform(This,matrix) (This)->lpVtbl->SetMatrixTransform(This,matrix)
#define IDXGISwapChain3_GetMatrixTransform(This,matrix) (This)->lpVtbl->GetMatrixTransform(This,matrix)
/*** IDXGISwapChain3 methods ***/
#define IDXGISwapChain3_GetCurrentBackBufferIndex(This) (This)->lpVtbl->GetCurrentBackBufferIndex(This)
#define IDXGISwapChain3_CheckColorSpaceSupport(This,colour_space,colour_space_support) (This)->lpVtbl->CheckColorSpaceSupport(This,colour_space,colour_space_support)
#define IDXGISwapChain3_SetColorSpace1(This,colour_space) (This)->lpVtbl->SetColorSpace1(This,colour_space)
#define IDXGISwapChain3_ResizeBuffers1(This,buffer_count,width,height,format,flags,node_mask,present_queue) (This)->lpVtbl->ResizeBuffers1(This,buffer_count,width,height,format,flags,node_mask,present_queue)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGISwapChain3_QueryInterface(IDXGISwapChain3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGISwapChain3_AddRef(IDXGISwapChain3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGISwapChain3_Release(IDXGISwapChain3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGISwapChain3_SetPrivateData(IDXGISwapChain3* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISwapChain3_SetPrivateDataInterface(IDXGISwapChain3* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGISwapChain3_GetPrivateData(IDXGISwapChain3* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGISwapChain3_GetParent(IDXGISwapChain3* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIDeviceSubObject methods ***/
static inline HRESULT IDXGISwapChain3_GetDevice(IDXGISwapChain3* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** IDXGISwapChain methods ***/
static inline HRESULT IDXGISwapChain3_Present(IDXGISwapChain3* This,UINT sync_interval,UINT flags) {
    return This->lpVtbl->Present(This,sync_interval,flags);
}
static inline HRESULT IDXGISwapChain3_GetBuffer(IDXGISwapChain3* This,UINT buffer_idx,REFIID riid,void **surface) {
    return This->lpVtbl->GetBuffer(This,buffer_idx,riid,surface);
}
static inline HRESULT IDXGISwapChain3_SetFullscreenState(IDXGISwapChain3* This,WINBOOL fullscreen,IDXGIOutput *target) {
    return This->lpVtbl->SetFullscreenState(This,fullscreen,target);
}
static inline HRESULT IDXGISwapChain3_GetFullscreenState(IDXGISwapChain3* This,WINBOOL *fullscreen,IDXGIOutput **target) {
    return This->lpVtbl->GetFullscreenState(This,fullscreen,target);
}
static inline HRESULT IDXGISwapChain3_GetDesc(IDXGISwapChain3* This,DXGI_SWAP_CHAIN_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGISwapChain3_ResizeBuffers(IDXGISwapChain3* This,UINT buffer_count,UINT width,UINT height,DXGI_FORMAT format,UINT flags) {
    return This->lpVtbl->ResizeBuffers(This,buffer_count,width,height,format,flags);
}
static inline HRESULT IDXGISwapChain3_ResizeTarget(IDXGISwapChain3* This,const DXGI_MODE_DESC *target_mode_desc) {
    return This->lpVtbl->ResizeTarget(This,target_mode_desc);
}
static inline HRESULT IDXGISwapChain3_GetContainingOutput(IDXGISwapChain3* This,IDXGIOutput **output) {
    return This->lpVtbl->GetContainingOutput(This,output);
}
static inline HRESULT IDXGISwapChain3_GetFrameStatistics(IDXGISwapChain3* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
static inline HRESULT IDXGISwapChain3_GetLastPresentCount(IDXGISwapChain3* This,UINT *last_present_count) {
    return This->lpVtbl->GetLastPresentCount(This,last_present_count);
}
/*** IDXGISwapChain1 methods ***/
static inline HRESULT IDXGISwapChain3_GetDesc1(IDXGISwapChain3* This,DXGI_SWAP_CHAIN_DESC1 *pDesc) {
    return This->lpVtbl->GetDesc1(This,pDesc);
}
static inline HRESULT IDXGISwapChain3_GetFullscreenDesc(IDXGISwapChain3* This,DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pDesc) {
    return This->lpVtbl->GetFullscreenDesc(This,pDesc);
}
static inline HRESULT IDXGISwapChain3_GetHwnd(IDXGISwapChain3* This,HWND *pHwnd) {
    return This->lpVtbl->GetHwnd(This,pHwnd);
}
static inline HRESULT IDXGISwapChain3_GetCoreWindow(IDXGISwapChain3* This,REFIID refiid,void **ppUnk) {
    return This->lpVtbl->GetCoreWindow(This,refiid,ppUnk);
}
static inline HRESULT IDXGISwapChain3_Present1(IDXGISwapChain3* This,UINT SyncInterval,UINT PresentFlags,const DXGI_PRESENT_PARAMETERS *pPresentParameters) {
    return This->lpVtbl->Present1(This,SyncInterval,PresentFlags,pPresentParameters);
}
static inline WINBOOL IDXGISwapChain3_IsTemporaryMonoSupported(IDXGISwapChain3* This) {
    return This->lpVtbl->IsTemporaryMonoSupported(This);
}
static inline HRESULT IDXGISwapChain3_GetRestrictToOutput(IDXGISwapChain3* This,IDXGIOutput **ppRestrictToOutput) {
    return This->lpVtbl->GetRestrictToOutput(This,ppRestrictToOutput);
}
static inline HRESULT IDXGISwapChain3_SetBackgroundColor(IDXGISwapChain3* This,const DXGI_RGBA *pColor) {
    return This->lpVtbl->SetBackgroundColor(This,pColor);
}
static inline HRESULT IDXGISwapChain3_GetBackgroundColor(IDXGISwapChain3* This,DXGI_RGBA *pColor) {
    return This->lpVtbl->GetBackgroundColor(This,pColor);
}
static inline HRESULT IDXGISwapChain3_SetRotation(IDXGISwapChain3* This,DXGI_MODE_ROTATION Rotation) {
    return This->lpVtbl->SetRotation(This,Rotation);
}
static inline HRESULT IDXGISwapChain3_GetRotation(IDXGISwapChain3* This,DXGI_MODE_ROTATION *pRotation) {
    return This->lpVtbl->GetRotation(This,pRotation);
}
/*** IDXGISwapChain2 methods ***/
static inline HRESULT IDXGISwapChain3_SetSourceSize(IDXGISwapChain3* This,UINT width,UINT height) {
    return This->lpVtbl->SetSourceSize(This,width,height);
}
static inline HRESULT IDXGISwapChain3_GetSourceSize(IDXGISwapChain3* This,UINT *width,UINT *height) {
    return This->lpVtbl->GetSourceSize(This,width,height);
}
static inline HRESULT IDXGISwapChain3_SetMaximumFrameLatency(IDXGISwapChain3* This,UINT max_latency) {
    return This->lpVtbl->SetMaximumFrameLatency(This,max_latency);
}
static inline HRESULT IDXGISwapChain3_GetMaximumFrameLatency(IDXGISwapChain3* This,UINT *max_latency) {
    return This->lpVtbl->GetMaximumFrameLatency(This,max_latency);
}
static inline HANDLE IDXGISwapChain3_GetFrameLatencyWaitableObject(IDXGISwapChain3* This) {
    return This->lpVtbl->GetFrameLatencyWaitableObject(This);
}
static inline HRESULT IDXGISwapChain3_SetMatrixTransform(IDXGISwapChain3* This,const DXGI_MATRIX_3X2_F *matrix) {
    return This->lpVtbl->SetMatrixTransform(This,matrix);
}
static inline HRESULT IDXGISwapChain3_GetMatrixTransform(IDXGISwapChain3* This,DXGI_MATRIX_3X2_F *matrix) {
    return This->lpVtbl->GetMatrixTransform(This,matrix);
}
/*** IDXGISwapChain3 methods ***/
static inline UINT IDXGISwapChain3_GetCurrentBackBufferIndex(IDXGISwapChain3* This) {
    return This->lpVtbl->GetCurrentBackBufferIndex(This);
}
static inline HRESULT IDXGISwapChain3_CheckColorSpaceSupport(IDXGISwapChain3* This,DXGI_COLOR_SPACE_TYPE colour_space,UINT *colour_space_support) {
    return This->lpVtbl->CheckColorSpaceSupport(This,colour_space,colour_space_support);
}
static inline HRESULT IDXGISwapChain3_SetColorSpace1(IDXGISwapChain3* This,DXGI_COLOR_SPACE_TYPE colour_space) {
    return This->lpVtbl->SetColorSpace1(This,colour_space);
}
static inline HRESULT IDXGISwapChain3_ResizeBuffers1(IDXGISwapChain3* This,UINT buffer_count,UINT width,UINT height,DXGI_FORMAT format,UINT flags,const UINT *node_mask,IUnknown *const *present_queue) {
    return This->lpVtbl->ResizeBuffers1(This,buffer_count,width,height,format,flags,node_mask,present_queue);
}
#endif
#endif

#endif


#endif  /* __IDXGISwapChain3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIOutput4 interface
 */
#ifndef __IDXGIOutput4_INTERFACE_DEFINED__
#define __IDXGIOutput4_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIOutput4, 0xdc7dca35, 0x2196, 0x414d, 0x9f,0x53, 0x61,0x78,0x84,0x03,0x2a,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("dc7dca35-2196-414d-9f53-617884032a60")
IDXGIOutput4 : public IDXGIOutput3
{
    virtual HRESULT STDMETHODCALLTYPE CheckOverlayColorSpaceSupport(
        DXGI_FORMAT format,
        DXGI_COLOR_SPACE_TYPE colour_space,
        IUnknown *device,
        UINT *flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIOutput4, 0xdc7dca35, 0x2196, 0x414d, 0x9f,0x53, 0x61,0x78,0x84,0x03,0x2a,0x60)
#endif
#else
typedef struct IDXGIOutput4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIOutput4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIOutput4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIOutput4 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIOutput4 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIOutput4 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIOutput4 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIOutput4 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIOutput methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIOutput4 *This,
        DXGI_OUTPUT_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList)(
        IDXGIOutput4 *This,
        DXGI_FORMAT format,
        UINT flags,
        UINT *mode_count,
        DXGI_MODE_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode)(
        IDXGIOutput4 *This,
        const DXGI_MODE_DESC *mode,
        DXGI_MODE_DESC *closest_match,
        IUnknown *device);

    HRESULT (STDMETHODCALLTYPE *WaitForVBlank)(
        IDXGIOutput4 *This);

    HRESULT (STDMETHODCALLTYPE *TakeOwnership)(
        IDXGIOutput4 *This,
        IUnknown *device,
        WINBOOL exclusive);

    void (STDMETHODCALLTYPE *ReleaseOwnership)(
        IDXGIOutput4 *This);

    HRESULT (STDMETHODCALLTYPE *GetGammaControlCapabilities)(
        IDXGIOutput4 *This,
        DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps);

    HRESULT (STDMETHODCALLTYPE *SetGammaControl)(
        IDXGIOutput4 *This,
        const DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *GetGammaControl)(
        IDXGIOutput4 *This,
        DXGI_GAMMA_CONTROL *gamma_control);

    HRESULT (STDMETHODCALLTYPE *SetDisplaySurface)(
        IDXGIOutput4 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData)(
        IDXGIOutput4 *This,
        IDXGISurface *surface);

    HRESULT (STDMETHODCALLTYPE *GetFrameStatistics)(
        IDXGIOutput4 *This,
        DXGI_FRAME_STATISTICS *stats);

    /*** IDXGIOutput1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDisplayModeList1)(
        IDXGIOutput4 *This,
        DXGI_FORMAT enum_format,
        UINT flags,
        UINT *num_modes,
        DXGI_MODE_DESC1 *desc);

    HRESULT (STDMETHODCALLTYPE *FindClosestMatchingMode1)(
        IDXGIOutput4 *This,
        const DXGI_MODE_DESC1 *mode_to_match,
        DXGI_MODE_DESC1 *closest_match,
        IUnknown *concerned_device);

    HRESULT (STDMETHODCALLTYPE *GetDisplaySurfaceData1)(
        IDXGIOutput4 *This,
        IDXGIResource *destination);

    HRESULT (STDMETHODCALLTYPE *DuplicateOutput)(
        IDXGIOutput4 *This,
        IUnknown *device,
        IDXGIOutputDuplication **output_duplication);

    /*** IDXGIOutput2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *SupportsOverlays)(
        IDXGIOutput4 *This);

    /*** IDXGIOutput3 methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckOverlaySupport)(
        IDXGIOutput4 *This,
        DXGI_FORMAT enum_format,
        IUnknown *concerned_device,
        UINT *flags);

    /*** IDXGIOutput4 methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckOverlayColorSpaceSupport)(
        IDXGIOutput4 *This,
        DXGI_FORMAT format,
        DXGI_COLOR_SPACE_TYPE colour_space,
        IUnknown *device,
        UINT *flags);

    END_INTERFACE
} IDXGIOutput4Vtbl;

interface IDXGIOutput4 {
    CONST_VTBL IDXGIOutput4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIOutput4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIOutput4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIOutput4_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIOutput4_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIOutput4_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIOutput4_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIOutput4_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIOutput methods ***/
#define IDXGIOutput4_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIOutput4_GetDisplayModeList(This,format,flags,mode_count,desc) (This)->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc)
#define IDXGIOutput4_FindClosestMatchingMode(This,mode,closest_match,device) (This)->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device)
#define IDXGIOutput4_WaitForVBlank(This) (This)->lpVtbl->WaitForVBlank(This)
#define IDXGIOutput4_TakeOwnership(This,device,exclusive) (This)->lpVtbl->TakeOwnership(This,device,exclusive)
#define IDXGIOutput4_ReleaseOwnership(This) (This)->lpVtbl->ReleaseOwnership(This)
#define IDXGIOutput4_GetGammaControlCapabilities(This,gamma_caps) (This)->lpVtbl->GetGammaControlCapabilities(This,gamma_caps)
#define IDXGIOutput4_SetGammaControl(This,gamma_control) (This)->lpVtbl->SetGammaControl(This,gamma_control)
#define IDXGIOutput4_GetGammaControl(This,gamma_control) (This)->lpVtbl->GetGammaControl(This,gamma_control)
#define IDXGIOutput4_SetDisplaySurface(This,surface) (This)->lpVtbl->SetDisplaySurface(This,surface)
#define IDXGIOutput4_GetDisplaySurfaceData(This,surface) (This)->lpVtbl->GetDisplaySurfaceData(This,surface)
#define IDXGIOutput4_GetFrameStatistics(This,stats) (This)->lpVtbl->GetFrameStatistics(This,stats)
/*** IDXGIOutput1 methods ***/
#define IDXGIOutput4_GetDisplayModeList1(This,enum_format,flags,num_modes,desc) (This)->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc)
#define IDXGIOutput4_FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device) (This)->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device)
#define IDXGIOutput4_GetDisplaySurfaceData1(This,destination) (This)->lpVtbl->GetDisplaySurfaceData1(This,destination)
#define IDXGIOutput4_DuplicateOutput(This,device,output_duplication) (This)->lpVtbl->DuplicateOutput(This,device,output_duplication)
/*** IDXGIOutput2 methods ***/
#define IDXGIOutput4_SupportsOverlays(This) (This)->lpVtbl->SupportsOverlays(This)
/*** IDXGIOutput3 methods ***/
#define IDXGIOutput4_CheckOverlaySupport(This,enum_format,concerned_device,flags) (This)->lpVtbl->CheckOverlaySupport(This,enum_format,concerned_device,flags)
/*** IDXGIOutput4 methods ***/
#define IDXGIOutput4_CheckOverlayColorSpaceSupport(This,format,colour_space,device,flags) (This)->lpVtbl->CheckOverlayColorSpaceSupport(This,format,colour_space,device,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIOutput4_QueryInterface(IDXGIOutput4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIOutput4_AddRef(IDXGIOutput4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIOutput4_Release(IDXGIOutput4* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIOutput4_SetPrivateData(IDXGIOutput4* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput4_SetPrivateDataInterface(IDXGIOutput4* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIOutput4_GetPrivateData(IDXGIOutput4* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIOutput4_GetParent(IDXGIOutput4* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIOutput methods ***/
static inline HRESULT IDXGIOutput4_GetDesc(IDXGIOutput4* This,DXGI_OUTPUT_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGIOutput4_GetDisplayModeList(IDXGIOutput4* This,DXGI_FORMAT format,UINT flags,UINT *mode_count,DXGI_MODE_DESC *desc) {
    return This->lpVtbl->GetDisplayModeList(This,format,flags,mode_count,desc);
}
static inline HRESULT IDXGIOutput4_FindClosestMatchingMode(IDXGIOutput4* This,const DXGI_MODE_DESC *mode,DXGI_MODE_DESC *closest_match,IUnknown *device) {
    return This->lpVtbl->FindClosestMatchingMode(This,mode,closest_match,device);
}
static inline HRESULT IDXGIOutput4_WaitForVBlank(IDXGIOutput4* This) {
    return This->lpVtbl->WaitForVBlank(This);
}
static inline HRESULT IDXGIOutput4_TakeOwnership(IDXGIOutput4* This,IUnknown *device,WINBOOL exclusive) {
    return This->lpVtbl->TakeOwnership(This,device,exclusive);
}
static inline void IDXGIOutput4_ReleaseOwnership(IDXGIOutput4* This) {
    This->lpVtbl->ReleaseOwnership(This);
}
static inline HRESULT IDXGIOutput4_GetGammaControlCapabilities(IDXGIOutput4* This,DXGI_GAMMA_CONTROL_CAPABILITIES *gamma_caps) {
    return This->lpVtbl->GetGammaControlCapabilities(This,gamma_caps);
}
static inline HRESULT IDXGIOutput4_SetGammaControl(IDXGIOutput4* This,const DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->SetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput4_GetGammaControl(IDXGIOutput4* This,DXGI_GAMMA_CONTROL *gamma_control) {
    return This->lpVtbl->GetGammaControl(This,gamma_control);
}
static inline HRESULT IDXGIOutput4_SetDisplaySurface(IDXGIOutput4* This,IDXGISurface *surface) {
    return This->lpVtbl->SetDisplaySurface(This,surface);
}
static inline HRESULT IDXGIOutput4_GetDisplaySurfaceData(IDXGIOutput4* This,IDXGISurface *surface) {
    return This->lpVtbl->GetDisplaySurfaceData(This,surface);
}
static inline HRESULT IDXGIOutput4_GetFrameStatistics(IDXGIOutput4* This,DXGI_FRAME_STATISTICS *stats) {
    return This->lpVtbl->GetFrameStatistics(This,stats);
}
/*** IDXGIOutput1 methods ***/
static inline HRESULT IDXGIOutput4_GetDisplayModeList1(IDXGIOutput4* This,DXGI_FORMAT enum_format,UINT flags,UINT *num_modes,DXGI_MODE_DESC1 *desc) {
    return This->lpVtbl->GetDisplayModeList1(This,enum_format,flags,num_modes,desc);
}
static inline HRESULT IDXGIOutput4_FindClosestMatchingMode1(IDXGIOutput4* This,const DXGI_MODE_DESC1 *mode_to_match,DXGI_MODE_DESC1 *closest_match,IUnknown *concerned_device) {
    return This->lpVtbl->FindClosestMatchingMode1(This,mode_to_match,closest_match,concerned_device);
}
static inline HRESULT IDXGIOutput4_GetDisplaySurfaceData1(IDXGIOutput4* This,IDXGIResource *destination) {
    return This->lpVtbl->GetDisplaySurfaceData1(This,destination);
}
static inline HRESULT IDXGIOutput4_DuplicateOutput(IDXGIOutput4* This,IUnknown *device,IDXGIOutputDuplication **output_duplication) {
    return This->lpVtbl->DuplicateOutput(This,device,output_duplication);
}
/*** IDXGIOutput2 methods ***/
static inline WINBOOL IDXGIOutput4_SupportsOverlays(IDXGIOutput4* This) {
    return This->lpVtbl->SupportsOverlays(This);
}
/*** IDXGIOutput3 methods ***/
static inline HRESULT IDXGIOutput4_CheckOverlaySupport(IDXGIOutput4* This,DXGI_FORMAT enum_format,IUnknown *concerned_device,UINT *flags) {
    return This->lpVtbl->CheckOverlaySupport(This,enum_format,concerned_device,flags);
}
/*** IDXGIOutput4 methods ***/
static inline HRESULT IDXGIOutput4_CheckOverlayColorSpaceSupport(IDXGIOutput4* This,DXGI_FORMAT format,DXGI_COLOR_SPACE_TYPE colour_space,IUnknown *device,UINT *flags) {
    return This->lpVtbl->CheckOverlayColorSpaceSupport(This,format,colour_space,device,flags);
}
#endif
#endif

#endif


#endif  /* __IDXGIOutput4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIFactory4 interface
 */
#ifndef __IDXGIFactory4_INTERFACE_DEFINED__
#define __IDXGIFactory4_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIFactory4, 0x1bc6ea02, 0xef36, 0x464f, 0xbf,0x0c, 0x21,0xca,0x39,0xe5,0x16,0x8a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1bc6ea02-ef36-464f-bf0c-21ca39e5168a")
IDXGIFactory4 : public IDXGIFactory3
{
    virtual HRESULT STDMETHODCALLTYPE EnumAdapterByLuid(
        LUID luid,
        REFIID iid,
        void **adapter) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnumWarpAdapter(
        REFIID iid,
        void **adapter) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIFactory4, 0x1bc6ea02, 0xef36, 0x464f, 0xbf,0x0c, 0x21,0xca,0x39,0xe5,0x16,0x8a)
#endif
#else
typedef struct IDXGIFactory4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIFactory4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIFactory4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIFactory4 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIFactory4 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIFactory4 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIFactory4 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIFactory4 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters)(
        IDXGIFactory4 *This,
        UINT adapter_idx,
        IDXGIAdapter **adapter);

    HRESULT (STDMETHODCALLTYPE *MakeWindowAssociation)(
        IDXGIFactory4 *This,
        HWND window,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *GetWindowAssociation)(
        IDXGIFactory4 *This,
        HWND *window);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChain)(
        IDXGIFactory4 *This,
        IUnknown *device,
        DXGI_SWAP_CHAIN_DESC *desc,
        IDXGISwapChain **swapchain);

    HRESULT (STDMETHODCALLTYPE *CreateSoftwareAdapter)(
        IDXGIFactory4 *This,
        HMODULE swrast,
        IDXGIAdapter **adapter);

    /*** IDXGIFactory1 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapters1)(
        IDXGIFactory4 *This,
        UINT Adapter,
        IDXGIAdapter1 **ppAdapter);

    WINBOOL (STDMETHODCALLTYPE *IsCurrent)(
        IDXGIFactory4 *This);

    /*** IDXGIFactory2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsWindowedStereoEnabled)(
        IDXGIFactory4 *This);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForHwnd)(
        IDXGIFactory4 *This,
        IUnknown *pDevice,
        HWND hWnd,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForCoreWindow)(
        IDXGIFactory4 *This,
        IUnknown *pDevice,
        IUnknown *pWindow,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    HRESULT (STDMETHODCALLTYPE *GetSharedResourceAdapterLuid)(
        IDXGIFactory4 *This,
        HANDLE hResource,
        LUID *pLuid);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusWindow)(
        IDXGIFactory4 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterStereoStatusEvent)(
        IDXGIFactory4 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterStereoStatus)(
        IDXGIFactory4 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusWindow)(
        IDXGIFactory4 *This,
        HWND WindowHandle,
        UINT wMsg,
        DWORD *pdwCookie);

    HRESULT (STDMETHODCALLTYPE *RegisterOcclusionStatusEvent)(
        IDXGIFactory4 *This,
        HANDLE hEvent,
        DWORD *pdwCookie);

    void (STDMETHODCALLTYPE *UnregisterOcclusionStatus)(
        IDXGIFactory4 *This,
        DWORD dwCookie);

    HRESULT (STDMETHODCALLTYPE *CreateSwapChainForComposition)(
        IDXGIFactory4 *This,
        IUnknown *pDevice,
        const DXGI_SWAP_CHAIN_DESC1 *pDesc,
        IDXGIOutput *pRestrictToOutput,
        IDXGISwapChain1 **ppSwapChain);

    /*** IDXGIFactory3 methods ***/
    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        IDXGIFactory4 *This);

    /*** IDXGIFactory4 methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumAdapterByLuid)(
        IDXGIFactory4 *This,
        LUID luid,
        REFIID iid,
        void **adapter);

    HRESULT (STDMETHODCALLTYPE *EnumWarpAdapter)(
        IDXGIFactory4 *This,
        REFIID iid,
        void **adapter);

    END_INTERFACE
} IDXGIFactory4Vtbl;

interface IDXGIFactory4 {
    CONST_VTBL IDXGIFactory4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIFactory4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIFactory4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIFactory4_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIFactory4_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIFactory4_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIFactory4_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIFactory4_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIFactory methods ***/
#define IDXGIFactory4_EnumAdapters(This,adapter_idx,adapter) (This)->lpVtbl->EnumAdapters(This,adapter_idx,adapter)
#define IDXGIFactory4_MakeWindowAssociation(This,window,flags) (This)->lpVtbl->MakeWindowAssociation(This,window,flags)
#define IDXGIFactory4_GetWindowAssociation(This,window) (This)->lpVtbl->GetWindowAssociation(This,window)
#define IDXGIFactory4_CreateSwapChain(This,device,desc,swapchain) (This)->lpVtbl->CreateSwapChain(This,device,desc,swapchain)
#define IDXGIFactory4_CreateSoftwareAdapter(This,swrast,adapter) (This)->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter)
/*** IDXGIFactory1 methods ***/
#define IDXGIFactory4_EnumAdapters1(This,Adapter,ppAdapter) (This)->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter)
#define IDXGIFactory4_IsCurrent(This) (This)->lpVtbl->IsCurrent(This)
/*** IDXGIFactory2 methods ***/
#define IDXGIFactory4_IsWindowedStereoEnabled(This) (This)->lpVtbl->IsWindowedStereoEnabled(This)
#define IDXGIFactory4_CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory4_CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain)
#define IDXGIFactory4_GetSharedResourceAdapterLuid(This,hResource,pLuid) (This)->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid)
#define IDXGIFactory4_RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory4_RegisterStereoStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory4_UnregisterStereoStatus(This,dwCookie) (This)->lpVtbl->UnregisterStereoStatus(This,dwCookie)
#define IDXGIFactory4_RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie)
#define IDXGIFactory4_RegisterOcclusionStatusEvent(This,hEvent,pdwCookie) (This)->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie)
#define IDXGIFactory4_UnregisterOcclusionStatus(This,dwCookie) (This)->lpVtbl->UnregisterOcclusionStatus(This,dwCookie)
#define IDXGIFactory4_CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain) (This)->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain)
/*** IDXGIFactory3 methods ***/
#define IDXGIFactory4_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
/*** IDXGIFactory4 methods ***/
#define IDXGIFactory4_EnumAdapterByLuid(This,luid,iid,adapter) (This)->lpVtbl->EnumAdapterByLuid(This,luid,iid,adapter)
#define IDXGIFactory4_EnumWarpAdapter(This,iid,adapter) (This)->lpVtbl->EnumWarpAdapter(This,iid,adapter)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIFactory4_QueryInterface(IDXGIFactory4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIFactory4_AddRef(IDXGIFactory4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIFactory4_Release(IDXGIFactory4* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIFactory4_SetPrivateData(IDXGIFactory4* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory4_SetPrivateDataInterface(IDXGIFactory4* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIFactory4_GetPrivateData(IDXGIFactory4* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIFactory4_GetParent(IDXGIFactory4* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIFactory methods ***/
static inline HRESULT IDXGIFactory4_EnumAdapters(IDXGIFactory4* This,UINT adapter_idx,IDXGIAdapter **adapter) {
    return This->lpVtbl->EnumAdapters(This,adapter_idx,adapter);
}
static inline HRESULT IDXGIFactory4_MakeWindowAssociation(IDXGIFactory4* This,HWND window,UINT flags) {
    return This->lpVtbl->MakeWindowAssociation(This,window,flags);
}
static inline HRESULT IDXGIFactory4_GetWindowAssociation(IDXGIFactory4* This,HWND *window) {
    return This->lpVtbl->GetWindowAssociation(This,window);
}
static inline HRESULT IDXGIFactory4_CreateSwapChain(IDXGIFactory4* This,IUnknown *device,DXGI_SWAP_CHAIN_DESC *desc,IDXGISwapChain **swapchain) {
    return This->lpVtbl->CreateSwapChain(This,device,desc,swapchain);
}
static inline HRESULT IDXGIFactory4_CreateSoftwareAdapter(IDXGIFactory4* This,HMODULE swrast,IDXGIAdapter **adapter) {
    return This->lpVtbl->CreateSoftwareAdapter(This,swrast,adapter);
}
/*** IDXGIFactory1 methods ***/
static inline HRESULT IDXGIFactory4_EnumAdapters1(IDXGIFactory4* This,UINT Adapter,IDXGIAdapter1 **ppAdapter) {
    return This->lpVtbl->EnumAdapters1(This,Adapter,ppAdapter);
}
static inline WINBOOL IDXGIFactory4_IsCurrent(IDXGIFactory4* This) {
    return This->lpVtbl->IsCurrent(This);
}
/*** IDXGIFactory2 methods ***/
static inline WINBOOL IDXGIFactory4_IsWindowedStereoEnabled(IDXGIFactory4* This) {
    return This->lpVtbl->IsWindowedStereoEnabled(This);
}
static inline HRESULT IDXGIFactory4_CreateSwapChainForHwnd(IDXGIFactory4* This,IUnknown *pDevice,HWND hWnd,const DXGI_SWAP_CHAIN_DESC1 *pDesc,const DXGI_SWAP_CHAIN_FULLSCREEN_DESC *pFullscreenDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForHwnd(This,pDevice,hWnd,pDesc,pFullscreenDesc,pRestrictToOutput,ppSwapChain);
}
static inline HRESULT IDXGIFactory4_CreateSwapChainForCoreWindow(IDXGIFactory4* This,IUnknown *pDevice,IUnknown *pWindow,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForCoreWindow(This,pDevice,pWindow,pDesc,pRestrictToOutput,ppSwapChain);
}
static inline HRESULT IDXGIFactory4_GetSharedResourceAdapterLuid(IDXGIFactory4* This,HANDLE hResource,LUID *pLuid) {
    return This->lpVtbl->GetSharedResourceAdapterLuid(This,hResource,pLuid);
}
static inline HRESULT IDXGIFactory4_RegisterStereoStatusWindow(IDXGIFactory4* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static inline HRESULT IDXGIFactory4_RegisterStereoStatusEvent(IDXGIFactory4* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterStereoStatusEvent(This,hEvent,pdwCookie);
}
static inline void IDXGIFactory4_UnregisterStereoStatus(IDXGIFactory4* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterStereoStatus(This,dwCookie);
}
static inline HRESULT IDXGIFactory4_RegisterOcclusionStatusWindow(IDXGIFactory4* This,HWND WindowHandle,UINT wMsg,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusWindow(This,WindowHandle,wMsg,pdwCookie);
}
static inline HRESULT IDXGIFactory4_RegisterOcclusionStatusEvent(IDXGIFactory4* This,HANDLE hEvent,DWORD *pdwCookie) {
    return This->lpVtbl->RegisterOcclusionStatusEvent(This,hEvent,pdwCookie);
}
static inline void IDXGIFactory4_UnregisterOcclusionStatus(IDXGIFactory4* This,DWORD dwCookie) {
    This->lpVtbl->UnregisterOcclusionStatus(This,dwCookie);
}
static inline HRESULT IDXGIFactory4_CreateSwapChainForComposition(IDXGIFactory4* This,IUnknown *pDevice,const DXGI_SWAP_CHAIN_DESC1 *pDesc,IDXGIOutput *pRestrictToOutput,IDXGISwapChain1 **ppSwapChain) {
    return This->lpVtbl->CreateSwapChainForComposition(This,pDevice,pDesc,pRestrictToOutput,ppSwapChain);
}
/*** IDXGIFactory3 methods ***/
static inline UINT IDXGIFactory4_GetCreationFlags(IDXGIFactory4* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
/*** IDXGIFactory4 methods ***/
static inline HRESULT IDXGIFactory4_EnumAdapterByLuid(IDXGIFactory4* This,LUID luid,REFIID iid,void **adapter) {
    return This->lpVtbl->EnumAdapterByLuid(This,luid,iid,adapter);
}
static inline HRESULT IDXGIFactory4_EnumWarpAdapter(IDXGIFactory4* This,REFIID iid,void **adapter) {
    return This->lpVtbl->EnumWarpAdapter(This,iid,adapter);
}
#endif
#endif

#endif


#endif  /* __IDXGIFactory4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDXGIAdapter3 interface
 */
#ifndef __IDXGIAdapter3_INTERFACE_DEFINED__
#define __IDXGIAdapter3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDXGIAdapter3, 0x645967a4, 0x1392, 0x4310, 0xa7,0x98, 0x80,0x53,0xce,0x3e,0x93,0xfd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("645967a4-1392-4310-a798-8053ce3e93fd")
IDXGIAdapter3 : public IDXGIAdapter2
{
    virtual HRESULT STDMETHODCALLTYPE RegisterHardwareContentProtectionTeardownStatusEvent(
        HANDLE event,
        DWORD *cookie) = 0;

    virtual void STDMETHODCALLTYPE UnregisterHardwareContentProtectionTeardownStatus(
        DWORD cookie) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryVideoMemoryInfo(
        UINT node_index,
        DXGI_MEMORY_SEGMENT_GROUP segment_group,
        DXGI_QUERY_VIDEO_MEMORY_INFO *memory_info) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVideoMemoryReservation(
        UINT node_index,
        DXGI_MEMORY_SEGMENT_GROUP segment_group,
        UINT64 reservation) = 0;

    virtual HRESULT STDMETHODCALLTYPE RegisterVideoMemoryBudgetChangeNotificationEvent(
        HANDLE event,
        DWORD *cookie) = 0;

    virtual void STDMETHODCALLTYPE UnregisterVideoMemoryBudgetChangeNotification(
        DWORD cookie) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDXGIAdapter3, 0x645967a4, 0x1392, 0x4310, 0xa7,0x98, 0x80,0x53,0xce,0x3e,0x93,0xfd)
#endif
#else
typedef struct IDXGIAdapter3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDXGIAdapter3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDXGIAdapter3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDXGIAdapter3 *This);

    /*** IDXGIObject methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        IDXGIAdapter3 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        IDXGIAdapter3 *This,
        REFGUID guid,
        const IUnknown *object);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        IDXGIAdapter3 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *GetParent)(
        IDXGIAdapter3 *This,
        REFIID riid,
        void **parent);

    /*** IDXGIAdapter methods ***/
    HRESULT (STDMETHODCALLTYPE *EnumOutputs)(
        IDXGIAdapter3 *This,
        UINT output_idx,
        IDXGIOutput **output);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        IDXGIAdapter3 *This,
        DXGI_ADAPTER_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *CheckInterfaceSupport)(
        IDXGIAdapter3 *This,
        REFGUID guid,
        LARGE_INTEGER *umd_version);

    /*** IDXGIAdapter1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc1)(
        IDXGIAdapter3 *This,
        DXGI_ADAPTER_DESC1 *pDesc);

    /*** IDXGIAdapter2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDesc2)(
        IDXGIAdapter3 *This,
        DXGI_ADAPTER_DESC2 *pDesc);

    /*** IDXGIAdapter3 methods ***/
    HRESULT (STDMETHODCALLTYPE *RegisterHardwareContentProtectionTeardownStatusEvent)(
        IDXGIAdapter3 *This,
        HANDLE event,
        DWORD *cookie);

    void (STDMETHODCALLTYPE *UnregisterHardwareContentProtectionTeardownStatus)(
        IDXGIAdapter3 *This,
        DWORD cookie);

    HRESULT (STDMETHODCALLTYPE *QueryVideoMemoryInfo)(
        IDXGIAdapter3 *This,
        UINT node_index,
        DXGI_MEMORY_SEGMENT_GROUP segment_group,
        DXGI_QUERY_VIDEO_MEMORY_INFO *memory_info);

    HRESULT (STDMETHODCALLTYPE *SetVideoMemoryReservation)(
        IDXGIAdapter3 *This,
        UINT node_index,
        DXGI_MEMORY_SEGMENT_GROUP segment_group,
        UINT64 reservation);

    HRESULT (STDMETHODCALLTYPE *RegisterVideoMemoryBudgetChangeNotificationEvent)(
        IDXGIAdapter3 *This,
        HANDLE event,
        DWORD *cookie);

    void (STDMETHODCALLTYPE *UnregisterVideoMemoryBudgetChangeNotification)(
        IDXGIAdapter3 *This,
        DWORD cookie);

    END_INTERFACE
} IDXGIAdapter3Vtbl;

interface IDXGIAdapter3 {
    CONST_VTBL IDXGIAdapter3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDXGIAdapter3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDXGIAdapter3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDXGIAdapter3_Release(This) (This)->lpVtbl->Release(This)
/*** IDXGIObject methods ***/
#define IDXGIAdapter3_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define IDXGIAdapter3_SetPrivateDataInterface(This,guid,object) (This)->lpVtbl->SetPrivateDataInterface(This,guid,object)
#define IDXGIAdapter3_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define IDXGIAdapter3_GetParent(This,riid,parent) (This)->lpVtbl->GetParent(This,riid,parent)
/*** IDXGIAdapter methods ***/
#define IDXGIAdapter3_EnumOutputs(This,output_idx,output) (This)->lpVtbl->EnumOutputs(This,output_idx,output)
#define IDXGIAdapter3_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define IDXGIAdapter3_CheckInterfaceSupport(This,guid,umd_version) (This)->lpVtbl->CheckInterfaceSupport(This,guid,umd_version)
/*** IDXGIAdapter1 methods ***/
#define IDXGIAdapter3_GetDesc1(This,pDesc) (This)->lpVtbl->GetDesc1(This,pDesc)
/*** IDXGIAdapter2 methods ***/
#define IDXGIAdapter3_GetDesc2(This,pDesc) (This)->lpVtbl->GetDesc2(This,pDesc)
/*** IDXGIAdapter3 methods ***/
#define IDXGIAdapter3_RegisterHardwareContentProtectionTeardownStatusEvent(This,event,cookie) (This)->lpVtbl->RegisterHardwareContentProtectionTeardownStatusEvent(This,event,cookie)
#define IDXGIAdapter3_UnregisterHardwareContentProtectionTeardownStatus(This,cookie) (This)->lpVtbl->UnregisterHardwareContentProtectionTeardownStatus(This,cookie)
#define IDXGIAdapter3_QueryVideoMemoryInfo(This,node_index,segment_group,memory_info) (This)->lpVtbl->QueryVideoMemoryInfo(This,node_index,segment_group,memory_info)
#define IDXGIAdapter3_SetVideoMemoryReservation(This,node_index,segment_group,reservation) (This)->lpVtbl->SetVideoMemoryReservation(This,node_index,segment_group,reservation)
#define IDXGIAdapter3_RegisterVideoMemoryBudgetChangeNotificationEvent(This,event,cookie) (This)->lpVtbl->RegisterVideoMemoryBudgetChangeNotificationEvent(This,event,cookie)
#define IDXGIAdapter3_UnregisterVideoMemoryBudgetChangeNotification(This,cookie) (This)->lpVtbl->UnregisterVideoMemoryBudgetChangeNotification(This,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT IDXGIAdapter3_QueryInterface(IDXGIAdapter3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDXGIAdapter3_AddRef(IDXGIAdapter3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDXGIAdapter3_Release(IDXGIAdapter3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDXGIObject methods ***/
static inline HRESULT IDXGIAdapter3_SetPrivateData(IDXGIAdapter3* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIAdapter3_SetPrivateDataInterface(IDXGIAdapter3* This,REFGUID guid,const IUnknown *object) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,object);
}
static inline HRESULT IDXGIAdapter3_GetPrivateData(IDXGIAdapter3* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT IDXGIAdapter3_GetParent(IDXGIAdapter3* This,REFIID riid,void **parent) {
    return This->lpVtbl->GetParent(This,riid,parent);
}
/*** IDXGIAdapter methods ***/
static inline HRESULT IDXGIAdapter3_EnumOutputs(IDXGIAdapter3* This,UINT output_idx,IDXGIOutput **output) {
    return This->lpVtbl->EnumOutputs(This,output_idx,output);
}
static inline HRESULT IDXGIAdapter3_GetDesc(IDXGIAdapter3* This,DXGI_ADAPTER_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT IDXGIAdapter3_CheckInterfaceSupport(IDXGIAdapter3* This,REFGUID guid,LARGE_INTEGER *umd_version) {
    return This->lpVtbl->CheckInterfaceSupport(This,guid,umd_version);
}
/*** IDXGIAdapter1 methods ***/
static inline HRESULT IDXGIAdapter3_GetDesc1(IDXGIAdapter3* This,DXGI_ADAPTER_DESC1 *pDesc) {
    return This->lpVtbl->GetDesc1(This,pDesc);
}
/*** IDXGIAdapter2 methods ***/
static inline HRESULT IDXGIAdapter3_GetDesc2(IDXGIAdapter3* This,DXGI_ADAPTER_DESC2 *pDesc) {
    return This->lpVtbl->GetDesc2(This,pDesc);
}
/*** IDXGIAdapter3 methods ***/
static inline HRESULT IDXGIAdapter3_RegisterHardwareContentProtectionTeardownStatusEvent(IDXGIAdapter3* This,HANDLE event,DWORD *cookie) {
    return This->lpVtbl->RegisterHardwareContentProtectionTeardownStatusEvent(This,event,cookie);
}
static inline void IDXGIAdapter3_UnregisterHardwareContentProtectionTeardownStatus(IDXGIAdapter3* This,DWORD cookie) {
    This->lpVtbl->UnregisterHardwareContentProtectionTeardownStatus(This,cookie);
}
static inline HRESULT IDXGIAdapter3_QueryVideoMemoryInfo(IDXGIAdapter3* This,UINT node_index,DXGI_MEMORY_SEGMENT_GROUP segment_group,DXGI_QUERY_VIDEO_MEMORY_INFO *memory_info) {
    return This->lpVtbl->QueryVideoMemoryInfo(This,node_index,segment_group,memory_info);
}
static inline HRESULT IDXGIAdapter3_SetVideoMemoryReservation(IDXGIAdapter3* This,UINT node_index,DXGI_MEMORY_SEGMENT_GROUP segment_group,UINT64 reservation) {
    return This->lpVtbl->SetVideoMemoryReservation(This,node_index,segment_group,reservation);
}
static inline HRESULT IDXGIAdapter3_RegisterVideoMemoryBudgetChangeNotificationEvent(IDXGIAdapter3* This,HANDLE event,DWORD *cookie) {
    return This->lpVtbl->RegisterVideoMemoryBudgetChangeNotificationEvent(This,event,cookie);
}
static inline void IDXGIAdapter3_UnregisterVideoMemoryBudgetChangeNotification(IDXGIAdapter3* This,DWORD cookie) {
    This->lpVtbl->UnregisterVideoMemoryBudgetChangeNotification(This,cookie);
}
#endif
#endif

#endif


#endif  /* __IDXGIAdapter3_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dxgi1_4_h__ */
