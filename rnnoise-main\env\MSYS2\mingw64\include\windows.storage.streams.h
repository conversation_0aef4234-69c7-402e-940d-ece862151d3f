/*** Autogenerated by WIDL 10.12 from include/windows.storage.streams.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_storage_streams_h__
#define __windows_storage_streams_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIBuffer_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIBuffer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIBuffer __x_ABI_CWindows_CStorage_CStreams_CIBuffer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIBuffer ABI::Windows::Storage::Streams::IBuffer
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IBuffer;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory ABI::Windows::Storage::Streams::IBufferFactory
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IBufferFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics ABI::Windows::Storage::Streams::IBufferStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IBufferStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIDataWriter_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIDataWriter_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIDataWriter __x_ABI_CWindows_CStorage_CStreams_CIDataWriter;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter ABI::Windows::Storage::Streams::IDataWriter
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IDataWriter;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory ABI::Windows::Storage::Streams::IDataWriterFactory
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IDataWriterFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIOutputStream_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIOutputStream_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIOutputStream __x_ABI_CWindows_CStorage_CStreams_CIOutputStream;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIOutputStream ABI::Windows::Storage::Streams::IOutputStream
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IOutputStream;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream ABI::Windows::Storage::Streams::IRandomAccessStream
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IRandomAccessStream;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference ABI::Windows::Storage::Streams::IRandomAccessStreamReference
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IRandomAccessStreamReference;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics ABI::Windows::Storage::Streams::IRandomAccessStreamReferenceStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IRandomAccessStreamReferenceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IRandomAccessStreamWithContentType;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CBuffer_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CBuffer_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                class Buffer;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CBuffer __x_ABI_CWindows_CStorage_CStreams_CBuffer;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CStreams_CBuffer_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CDataWriter_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CDataWriter_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                class DataWriter;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CDataWriter __x_ABI_CWindows_CStorage_CStreams_CDataWriter;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CStreams_CDataWriter_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CDataWriterStoreOperation_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CDataWriterStoreOperation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                class DataWriterStoreOperation;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CDataWriterStoreOperation __x_ABI_CWindows_CStorage_CStreams_CDataWriterStoreOperation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CStreams_CDataWriterStoreOperation_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CInMemoryRandomAccessStream_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CInMemoryRandomAccessStream_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                class InMemoryRandomAccessStream;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CInMemoryRandomAccessStream __x_ABI_CWindows_CStorage_CStreams_CInMemoryRandomAccessStream;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CStreams_CInMemoryRandomAccessStream_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CRandomAccessStream_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CRandomAccessStream_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                class RandomAccessStream;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CRandomAccessStream __x_ABI_CWindows_CStorage_CStreams_CRandomAccessStream;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CStreams_CRandomAccessStream_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CRandomAccessStreamReference_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CRandomAccessStreamReference_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                class RandomAccessStreamReference;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CRandomAccessStreamReference __x_ABI_CWindows_CStorage_CStreams_CRandomAccessStreamReference;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CStreams_CRandomAccessStreamReference_FWD_DEFINED__ */

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IBuffer* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IOutputStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IBuffer* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IOutputStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
#define ____FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Storage::Streams::IRandomAccessStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
#define ____FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Storage::Streams::IRandomAccessStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
#define ____FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference ABI::Windows::Foundation::Collections::IMap<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::Streams::IRandomAccessStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
#define ____FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream;
#ifdef __cplusplus
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Storage::Streams::IRandomAccessStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::RandomAccessStreamReference* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::RandomAccessStreamReference* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <eventtoken.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CStorage_CIStorageFile_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CIStorageFile_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CIStorageFile __x_ABI_CWindows_CStorage_CIStorageFile;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CIStorageFile ABI::Windows::Storage::IStorageFile
namespace ABI {
    namespace Windows {
        namespace Storage {
            interface IStorageFile;
        }
    }
}
#endif /* __cplusplus */
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CByteOrder_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CByteOrder_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                enum ByteOrder {
                    ByteOrder_LittleEndian = 0,
                    ByteOrder_BigEndian = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CStreams_CByteOrder {
    ByteOrder_LittleEndian = 0,
    ByteOrder_BigEndian = 1
};
#ifdef WIDL_using_Windows_Storage_Streams
#define ByteOrder __x_ABI_CWindows_CStorage_CStreams_CByteOrder
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CStreams_CByteOrder_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CStreams_CByteOrder __x_ABI_CWindows_CStorage_CStreams_CByteOrder;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CUnicodeEncoding_ENUM_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CUnicodeEncoding_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                enum UnicodeEncoding {
                    UnicodeEncoding_Utf8 = 0,
                    UnicodeEncoding_Utf16LE = 1,
                    UnicodeEncoding_Utf16BE = 2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CStorage_CStreams_CUnicodeEncoding {
    UnicodeEncoding_Utf8 = 0,
    UnicodeEncoding_Utf16LE = 1,
    UnicodeEncoding_Utf16BE = 2
};
#ifdef WIDL_using_Windows_Storage_Streams
#define UnicodeEncoding __x_ABI_CWindows_CStorage_CStreams_CUnicodeEncoding
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif /* ____x_ABI_CWindows_CStorage_CStreams_CUnicodeEncoding_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CStorage_CStreams_CUnicodeEncoding __x_ABI_CWindows_CStorage_CStreams_CUnicodeEncoding;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIBuffer_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIBuffer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIBuffer __x_ABI_CWindows_CStorage_CStreams_CIBuffer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIBuffer ABI::Windows::Storage::Streams::IBuffer
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IBuffer;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory ABI::Windows::Storage::Streams::IBufferFactory
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IBufferFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics ABI::Windows::Storage::Streams::IBufferStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IBufferStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIContentTypeProvider_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIContentTypeProvider_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIContentTypeProvider __x_ABI_CWindows_CStorage_CStreams_CIContentTypeProvider;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIContentTypeProvider ABI::Windows::Storage::Streams::IContentTypeProvider
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IContentTypeProvider;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIDataWriter_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIDataWriter_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIDataWriter __x_ABI_CWindows_CStorage_CStreams_CIDataWriter;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter ABI::Windows::Storage::Streams::IDataWriter
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IDataWriter;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory ABI::Windows::Storage::Streams::IDataWriterFactory
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IDataWriterFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIInputStream_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIInputStream_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIInputStream __x_ABI_CWindows_CStorage_CStreams_CIInputStream;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIInputStream ABI::Windows::Storage::Streams::IInputStream
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IInputStream;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIInputStreamReference_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIInputStreamReference_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIInputStreamReference __x_ABI_CWindows_CStorage_CStreams_CIInputStreamReference;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIInputStreamReference ABI::Windows::Storage::Streams::IInputStreamReference
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IInputStreamReference;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIOutputStream_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIOutputStream_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIOutputStream __x_ABI_CWindows_CStorage_CStreams_CIOutputStream;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIOutputStream ABI::Windows::Storage::Streams::IOutputStream
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IOutputStream;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream ABI::Windows::Storage::Streams::IRandomAccessStream
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IRandomAccessStream;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference ABI::Windows::Storage::Streams::IRandomAccessStreamReference
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IRandomAccessStreamReference;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics ABI::Windows::Storage::Streams::IRandomAccessStreamReferenceStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IRandomAccessStreamReferenceStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamStatics __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamStatics ABI::Windows::Storage::Streams::IRandomAccessStreamStatics
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IRandomAccessStreamStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                interface IRandomAccessStreamWithContentType;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IBuffer* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IOutputStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
#define ____FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Storage::Streams::IRandomAccessStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
#define ____FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Storage::Streams::IRandomAccessStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
#define ____FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference ABI::Windows::Foundation::Collections::IMap<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Storage::Streams::IRandomAccessStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
#define ____FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream;
#ifdef __cplusplus
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Storage::Streams::IRandomAccessStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStream* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::RandomAccessStreamReference* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IBuffer interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIBuffer_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CStreams_CIBuffer, 0x905a0fe0, 0xbc53, 0x11df, 0x8c,0x49, 0x00,0x1e,0x4f,0xc6,0x86,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                MIDL_INTERFACE("905a0fe0-bc53-11df-8c49-001e4fc686da")
                IBuffer : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Capacity(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Length(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_Length(
                        UINT32 value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CStreams_CIBuffer, 0x905a0fe0, 0xbc53, 0x11df, 0x8c,0x49, 0x00,0x1e,0x4f,0xc6,0x86,0xda)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CIBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *This,
        TrustLevel *trustLevel);

    /*** IBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Capacity)(
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Length)(
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *put_Length)(
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *This,
        UINT32 value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CStreams_CIBufferVtbl;

interface __x_ABI_CWindows_CStorage_CStreams_CIBuffer {
    CONST_VTBL __x_ABI_CWindows_CStorage_CStreams_CIBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CStreams_CIBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CStreams_CIBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIBuffer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CStreams_CIBuffer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CStreams_CIBuffer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBuffer methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIBuffer_get_Capacity(This,value) (This)->lpVtbl->get_Capacity(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIBuffer_get_Length(This,value) (This)->lpVtbl->get_Length(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIBuffer_put_Length(This,value) (This)->lpVtbl->put_Length(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBuffer_QueryInterface(__x_ABI_CWindows_CStorage_CStreams_CIBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIBuffer_AddRef(__x_ABI_CWindows_CStorage_CStreams_CIBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIBuffer_Release(__x_ABI_CWindows_CStorage_CStreams_CIBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBuffer_GetIids(__x_ABI_CWindows_CStorage_CStreams_CIBuffer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBuffer_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CStreams_CIBuffer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBuffer_GetTrustLevel(__x_ABI_CWindows_CStorage_CStreams_CIBuffer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBuffer methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBuffer_get_Capacity(__x_ABI_CWindows_CStorage_CStreams_CIBuffer* This,UINT32 *value) {
    return This->lpVtbl->get_Capacity(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBuffer_get_Length(__x_ABI_CWindows_CStorage_CStreams_CIBuffer* This,UINT32 *value) {
    return This->lpVtbl->get_Length(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBuffer_put_Length(__x_ABI_CWindows_CStorage_CStreams_CIBuffer* This,UINT32 value) {
    return This->lpVtbl->put_Length(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage_Streams
#define IID_IBuffer IID___x_ABI_CWindows_CStorage_CStreams_CIBuffer
#define IBufferVtbl __x_ABI_CWindows_CStorage_CStreams_CIBufferVtbl
#define IBuffer __x_ABI_CWindows_CStorage_CStreams_CIBuffer
#define IBuffer_QueryInterface __x_ABI_CWindows_CStorage_CStreams_CIBuffer_QueryInterface
#define IBuffer_AddRef __x_ABI_CWindows_CStorage_CStreams_CIBuffer_AddRef
#define IBuffer_Release __x_ABI_CWindows_CStorage_CStreams_CIBuffer_Release
#define IBuffer_GetIids __x_ABI_CWindows_CStorage_CStreams_CIBuffer_GetIids
#define IBuffer_GetRuntimeClassName __x_ABI_CWindows_CStorage_CStreams_CIBuffer_GetRuntimeClassName
#define IBuffer_GetTrustLevel __x_ABI_CWindows_CStorage_CStreams_CIBuffer_GetTrustLevel
#define IBuffer_get_Capacity __x_ABI_CWindows_CStorage_CStreams_CIBuffer_get_Capacity
#define IBuffer_get_Length __x_ABI_CWindows_CStorage_CStreams_CIBuffer_get_Length
#define IBuffer_put_Length __x_ABI_CWindows_CStorage_CStreams_CIBuffer_put_Length
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CStreams_CIBuffer_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBufferFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CStreams_CIBufferFactory, 0x71af914d, 0xc10f, 0x484b, 0xbc,0x50, 0x14,0xbc,0x62,0x3b,0x3a,0x27);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                MIDL_INTERFACE("71af914d-c10f-484b-bc50-14bc623b3a27")
                IBufferFactory : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Create(
                        UINT32 capacity,
                        ABI::Windows::Storage::Streams::IBuffer **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CStreams_CIBufferFactory, 0x71af914d, 0xc10f, 0x484b, 0xbc,0x50, 0x14,0xbc,0x62,0x3b,0x3a,0x27)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CIBufferFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory *This,
        TrustLevel *trustLevel);

    /*** IBufferFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *Create)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory *This,
        UINT32 capacity,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer **value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CStreams_CIBufferFactoryVtbl;

interface __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory {
    CONST_VTBL __x_ABI_CWindows_CStorage_CStreams_CIBufferFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBufferFactory methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_Create(This,capacity,value) (This)->lpVtbl->Create(This,capacity,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_QueryInterface(__x_ABI_CWindows_CStorage_CStreams_CIBufferFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_AddRef(__x_ABI_CWindows_CStorage_CStreams_CIBufferFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_Release(__x_ABI_CWindows_CStorage_CStreams_CIBufferFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_GetIids(__x_ABI_CWindows_CStorage_CStreams_CIBufferFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CStreams_CIBufferFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_GetTrustLevel(__x_ABI_CWindows_CStorage_CStreams_CIBufferFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBufferFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_Create(__x_ABI_CWindows_CStorage_CStreams_CIBufferFactory* This,UINT32 capacity,__x_ABI_CWindows_CStorage_CStreams_CIBuffer **value) {
    return This->lpVtbl->Create(This,capacity,value);
}
#endif
#ifdef WIDL_using_Windows_Storage_Streams
#define IID_IBufferFactory IID___x_ABI_CWindows_CStorage_CStreams_CIBufferFactory
#define IBufferFactoryVtbl __x_ABI_CWindows_CStorage_CStreams_CIBufferFactoryVtbl
#define IBufferFactory __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory
#define IBufferFactory_QueryInterface __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_QueryInterface
#define IBufferFactory_AddRef __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_AddRef
#define IBufferFactory_Release __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_Release
#define IBufferFactory_GetIids __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_GetIids
#define IBufferFactory_GetRuntimeClassName __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_GetRuntimeClassName
#define IBufferFactory_GetTrustLevel __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_GetTrustLevel
#define IBufferFactory_Create __x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_Create
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CStreams_CIBufferFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IBufferStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CStreams_CIBufferStatics, 0xe901e65b, 0xd716, 0x475a, 0xa9,0x0a, 0xaf,0x72,0x29,0xb1,0xe7,0x41);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                MIDL_INTERFACE("e901e65b-d716-475a-a90a-af7229b1e741")
                IBufferStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateCopyFromMemoryBuffer(
                        ABI::Windows::Foundation::IMemoryBuffer *input,
                        ABI::Windows::Storage::Streams::IBuffer **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateMemoryBufferOverIBuffer(
                        ABI::Windows::Storage::Streams::IBuffer *input,
                        ABI::Windows::Foundation::IMemoryBuffer **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CStreams_CIBufferStatics, 0xe901e65b, 0xd716, 0x475a, 0xa9,0x0a, 0xaf,0x72,0x29,0xb1,0xe7,0x41)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CIBufferStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics *This,
        TrustLevel *trustLevel);

    /*** IBufferStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateCopyFromMemoryBuffer)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics *This,
        __x_ABI_CWindows_CFoundation_CIMemoryBuffer *input,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer **value);

    HRESULT (STDMETHODCALLTYPE *CreateMemoryBufferOverIBuffer)(
        __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *input,
        __x_ABI_CWindows_CFoundation_CIMemoryBuffer **value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CStreams_CIBufferStaticsVtbl;

interface __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics {
    CONST_VTBL __x_ABI_CWindows_CStorage_CStreams_CIBufferStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBufferStatics methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_CreateCopyFromMemoryBuffer(This,input,value) (This)->lpVtbl->CreateCopyFromMemoryBuffer(This,input,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_CreateMemoryBufferOverIBuffer(This,input,value) (This)->lpVtbl->CreateMemoryBufferOverIBuffer(This,input,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_QueryInterface(__x_ABI_CWindows_CStorage_CStreams_CIBufferStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_AddRef(__x_ABI_CWindows_CStorage_CStreams_CIBufferStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_Release(__x_ABI_CWindows_CStorage_CStreams_CIBufferStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_GetIids(__x_ABI_CWindows_CStorage_CStreams_CIBufferStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CStreams_CIBufferStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_GetTrustLevel(__x_ABI_CWindows_CStorage_CStreams_CIBufferStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBufferStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_CreateCopyFromMemoryBuffer(__x_ABI_CWindows_CStorage_CStreams_CIBufferStatics* This,__x_ABI_CWindows_CFoundation_CIMemoryBuffer *input,__x_ABI_CWindows_CStorage_CStreams_CIBuffer **value) {
    return This->lpVtbl->CreateCopyFromMemoryBuffer(This,input,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_CreateMemoryBufferOverIBuffer(__x_ABI_CWindows_CStorage_CStreams_CIBufferStatics* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *input,__x_ABI_CWindows_CFoundation_CIMemoryBuffer **value) {
    return This->lpVtbl->CreateMemoryBufferOverIBuffer(This,input,value);
}
#endif
#ifdef WIDL_using_Windows_Storage_Streams
#define IID_IBufferStatics IID___x_ABI_CWindows_CStorage_CStreams_CIBufferStatics
#define IBufferStaticsVtbl __x_ABI_CWindows_CStorage_CStreams_CIBufferStaticsVtbl
#define IBufferStatics __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics
#define IBufferStatics_QueryInterface __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_QueryInterface
#define IBufferStatics_AddRef __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_AddRef
#define IBufferStatics_Release __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_Release
#define IBufferStatics_GetIids __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_GetIids
#define IBufferStatics_GetRuntimeClassName __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_GetRuntimeClassName
#define IBufferStatics_GetTrustLevel __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_GetTrustLevel
#define IBufferStatics_CreateCopyFromMemoryBuffer __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_CreateCopyFromMemoryBuffer
#define IBufferStatics_CreateMemoryBufferOverIBuffer __x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_CreateMemoryBufferOverIBuffer
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CStreams_CIBufferStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDataWriter interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIDataWriter_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIDataWriter_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CStreams_CIDataWriter, 0x64b89265, 0xd341, 0x4922, 0xb3,0x8a, 0xdd,0x4a,0xf8,0x80,0x8c,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                MIDL_INTERFACE("64b89265-d341-4922-b38a-dd4af8808c4e")
                IDataWriter : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_UnstoredBufferLength(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_UnicodeEncoding(
                        ABI::Windows::Storage::Streams::UnicodeEncoding *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_UnicodeEncoding(
                        ABI::Windows::Storage::Streams::UnicodeEncoding value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ByteOrder(
                        ABI::Windows::Storage::Streams::ByteOrder *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_ByteOrder(
                        ABI::Windows::Storage::Streams::ByteOrder value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteByte(
                        BYTE value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteBytes(
                        UINT32 value_size,
                        BYTE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteBuffer(
                        ABI::Windows::Storage::Streams::IBuffer *buffer) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteBufferRange(
                        ABI::Windows::Storage::Streams::IBuffer *buffer,
                        UINT32 start,
                        UINT32 count) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteBoolean(
                        boolean value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteGuid(
                        GUID value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteInt16(
                        INT16 value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteInt32(
                        INT32 value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteInt64(
                        INT64 value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteUInt16(
                        UINT16 value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteUInt32(
                        UINT32 value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteUInt64(
                        UINT64 value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteSingle(
                        FLOAT value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteDouble(
                        DOUBLE value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteDateTime(
                        ABI::Windows::Foundation::DateTime value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteTimeSpan(
                        ABI::Windows::Foundation::TimeSpan value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE WriteString(
                        HSTRING value,
                        UINT32 *code_unit_count) = 0;

                    virtual HRESULT STDMETHODCALLTYPE MeasureString(
                        HSTRING value,
                        UINT32 *code_unit_count) = 0;

                    virtual HRESULT STDMETHODCALLTYPE StoreAsync(
                        ABI::Windows::Foundation::IAsyncOperation<UINT32 > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FlushAsync(
                        ABI::Windows::Foundation::IAsyncOperation<boolean > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE DetachBuffer(
                        ABI::Windows::Storage::Streams::IBuffer **buffer) = 0;

                    virtual HRESULT STDMETHODCALLTYPE DetachStream(
                        ABI::Windows::Storage::Streams::IOutputStream **output_stream) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter, 0x64b89265, 0xd341, 0x4922, 0xb3,0x8a, 0xdd,0x4a,0xf8,0x80,0x8c,0x4e)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CIDataWriterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        TrustLevel *trustLevel);

    /*** IDataWriter methods ***/
    HRESULT (STDMETHODCALLTYPE *get_UnstoredBufferLength)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_UnicodeEncoding)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __x_ABI_CWindows_CStorage_CStreams_CUnicodeEncoding *value);

    HRESULT (STDMETHODCALLTYPE *put_UnicodeEncoding)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __x_ABI_CWindows_CStorage_CStreams_CUnicodeEncoding value);

    HRESULT (STDMETHODCALLTYPE *get_ByteOrder)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __x_ABI_CWindows_CStorage_CStreams_CByteOrder *value);

    HRESULT (STDMETHODCALLTYPE *put_ByteOrder)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __x_ABI_CWindows_CStorage_CStreams_CByteOrder value);

    HRESULT (STDMETHODCALLTYPE *WriteByte)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        BYTE value);

    HRESULT (STDMETHODCALLTYPE *WriteBytes)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        UINT32 value_size,
        BYTE *value);

    HRESULT (STDMETHODCALLTYPE *WriteBuffer)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer);

    HRESULT (STDMETHODCALLTYPE *WriteBufferRange)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer,
        UINT32 start,
        UINT32 count);

    HRESULT (STDMETHODCALLTYPE *WriteBoolean)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *WriteGuid)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        GUID value);

    HRESULT (STDMETHODCALLTYPE *WriteInt16)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        INT16 value);

    HRESULT (STDMETHODCALLTYPE *WriteInt32)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        INT32 value);

    HRESULT (STDMETHODCALLTYPE *WriteInt64)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        INT64 value);

    HRESULT (STDMETHODCALLTYPE *WriteUInt16)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        UINT16 value);

    HRESULT (STDMETHODCALLTYPE *WriteUInt32)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        UINT32 value);

    HRESULT (STDMETHODCALLTYPE *WriteUInt64)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        UINT64 value);

    HRESULT (STDMETHODCALLTYPE *WriteSingle)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        FLOAT value);

    HRESULT (STDMETHODCALLTYPE *WriteDouble)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        DOUBLE value);

    HRESULT (STDMETHODCALLTYPE *WriteDateTime)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __x_ABI_CWindows_CFoundation_CDateTime value);

    HRESULT (STDMETHODCALLTYPE *WriteTimeSpan)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan value);

    HRESULT (STDMETHODCALLTYPE *WriteString)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        HSTRING value,
        UINT32 *code_unit_count);

    HRESULT (STDMETHODCALLTYPE *MeasureString)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        HSTRING value,
        UINT32 *code_unit_count);

    HRESULT (STDMETHODCALLTYPE *StoreAsync)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __FIAsyncOperation_1_UINT32 **operation);

    HRESULT (STDMETHODCALLTYPE *FlushAsync)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __FIAsyncOperation_1_boolean **operation);

    HRESULT (STDMETHODCALLTYPE *DetachBuffer)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer **buffer);

    HRESULT (STDMETHODCALLTYPE *DetachStream)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter *This,
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream **output_stream);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CStreams_CIDataWriterVtbl;

interface __x_ABI_CWindows_CStorage_CStreams_CIDataWriter {
    CONST_VTBL __x_ABI_CWindows_CStorage_CStreams_CIDataWriterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDataWriter methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_get_UnstoredBufferLength(This,value) (This)->lpVtbl->get_UnstoredBufferLength(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_get_UnicodeEncoding(This,value) (This)->lpVtbl->get_UnicodeEncoding(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_put_UnicodeEncoding(This,value) (This)->lpVtbl->put_UnicodeEncoding(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_get_ByteOrder(This,value) (This)->lpVtbl->get_ByteOrder(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_put_ByteOrder(This,value) (This)->lpVtbl->put_ByteOrder(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteByte(This,value) (This)->lpVtbl->WriteByte(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBytes(This,value_size,value) (This)->lpVtbl->WriteBytes(This,value_size,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBuffer(This,buffer) (This)->lpVtbl->WriteBuffer(This,buffer)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBufferRange(This,buffer,start,count) (This)->lpVtbl->WriteBufferRange(This,buffer,start,count)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBoolean(This,value) (This)->lpVtbl->WriteBoolean(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteGuid(This,value) (This)->lpVtbl->WriteGuid(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteInt16(This,value) (This)->lpVtbl->WriteInt16(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteInt32(This,value) (This)->lpVtbl->WriteInt32(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteInt64(This,value) (This)->lpVtbl->WriteInt64(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteUInt16(This,value) (This)->lpVtbl->WriteUInt16(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteUInt32(This,value) (This)->lpVtbl->WriteUInt32(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteUInt64(This,value) (This)->lpVtbl->WriteUInt64(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteSingle(This,value) (This)->lpVtbl->WriteSingle(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteDouble(This,value) (This)->lpVtbl->WriteDouble(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteDateTime(This,value) (This)->lpVtbl->WriteDateTime(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteTimeSpan(This,value) (This)->lpVtbl->WriteTimeSpan(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteString(This,value,code_unit_count) (This)->lpVtbl->WriteString(This,value,code_unit_count)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_MeasureString(This,value,code_unit_count) (This)->lpVtbl->MeasureString(This,value,code_unit_count)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_StoreAsync(This,operation) (This)->lpVtbl->StoreAsync(This,operation)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_FlushAsync(This,operation) (This)->lpVtbl->FlushAsync(This,operation)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_DetachBuffer(This,buffer) (This)->lpVtbl->DetachBuffer(This,buffer)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_DetachStream(This,output_stream) (This)->lpVtbl->DetachStream(This,output_stream)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_QueryInterface(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_AddRef(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_Release(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_GetIids(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_GetTrustLevel(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDataWriter methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_get_UnstoredBufferLength(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,UINT32 *value) {
    return This->lpVtbl->get_UnstoredBufferLength(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_get_UnicodeEncoding(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__x_ABI_CWindows_CStorage_CStreams_CUnicodeEncoding *value) {
    return This->lpVtbl->get_UnicodeEncoding(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_put_UnicodeEncoding(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__x_ABI_CWindows_CStorage_CStreams_CUnicodeEncoding value) {
    return This->lpVtbl->put_UnicodeEncoding(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_get_ByteOrder(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__x_ABI_CWindows_CStorage_CStreams_CByteOrder *value) {
    return This->lpVtbl->get_ByteOrder(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_put_ByteOrder(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__x_ABI_CWindows_CStorage_CStreams_CByteOrder value) {
    return This->lpVtbl->put_ByteOrder(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteByte(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,BYTE value) {
    return This->lpVtbl->WriteByte(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBytes(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,UINT32 value_size,BYTE *value) {
    return This->lpVtbl->WriteBytes(This,value_size,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBuffer(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer) {
    return This->lpVtbl->WriteBuffer(This,buffer);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBufferRange(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer,UINT32 start,UINT32 count) {
    return This->lpVtbl->WriteBufferRange(This,buffer,start,count);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBoolean(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,boolean value) {
    return This->lpVtbl->WriteBoolean(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteGuid(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,GUID value) {
    return This->lpVtbl->WriteGuid(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteInt16(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,INT16 value) {
    return This->lpVtbl->WriteInt16(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteInt32(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,INT32 value) {
    return This->lpVtbl->WriteInt32(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteInt64(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,INT64 value) {
    return This->lpVtbl->WriteInt64(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteUInt16(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,UINT16 value) {
    return This->lpVtbl->WriteUInt16(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteUInt32(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,UINT32 value) {
    return This->lpVtbl->WriteUInt32(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteUInt64(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,UINT64 value) {
    return This->lpVtbl->WriteUInt64(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteSingle(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,FLOAT value) {
    return This->lpVtbl->WriteSingle(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteDouble(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,DOUBLE value) {
    return This->lpVtbl->WriteDouble(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteDateTime(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__x_ABI_CWindows_CFoundation_CDateTime value) {
    return This->lpVtbl->WriteDateTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteTimeSpan(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__x_ABI_CWindows_CFoundation_CTimeSpan value) {
    return This->lpVtbl->WriteTimeSpan(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteString(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,HSTRING value,UINT32 *code_unit_count) {
    return This->lpVtbl->WriteString(This,value,code_unit_count);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_MeasureString(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,HSTRING value,UINT32 *code_unit_count) {
    return This->lpVtbl->MeasureString(This,value,code_unit_count);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_StoreAsync(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__FIAsyncOperation_1_UINT32 **operation) {
    return This->lpVtbl->StoreAsync(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_FlushAsync(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__FIAsyncOperation_1_boolean **operation) {
    return This->lpVtbl->FlushAsync(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_DetachBuffer(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer **buffer) {
    return This->lpVtbl->DetachBuffer(This,buffer);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_DetachStream(__x_ABI_CWindows_CStorage_CStreams_CIDataWriter* This,__x_ABI_CWindows_CStorage_CStreams_CIOutputStream **output_stream) {
    return This->lpVtbl->DetachStream(This,output_stream);
}
#endif
#ifdef WIDL_using_Windows_Storage_Streams
#define IID_IDataWriter IID___x_ABI_CWindows_CStorage_CStreams_CIDataWriter
#define IDataWriterVtbl __x_ABI_CWindows_CStorage_CStreams_CIDataWriterVtbl
#define IDataWriter __x_ABI_CWindows_CStorage_CStreams_CIDataWriter
#define IDataWriter_QueryInterface __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_QueryInterface
#define IDataWriter_AddRef __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_AddRef
#define IDataWriter_Release __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_Release
#define IDataWriter_GetIids __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_GetIids
#define IDataWriter_GetRuntimeClassName __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_GetRuntimeClassName
#define IDataWriter_GetTrustLevel __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_GetTrustLevel
#define IDataWriter_get_UnstoredBufferLength __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_get_UnstoredBufferLength
#define IDataWriter_get_UnicodeEncoding __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_get_UnicodeEncoding
#define IDataWriter_put_UnicodeEncoding __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_put_UnicodeEncoding
#define IDataWriter_get_ByteOrder __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_get_ByteOrder
#define IDataWriter_put_ByteOrder __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_put_ByteOrder
#define IDataWriter_WriteByte __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteByte
#define IDataWriter_WriteBytes __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBytes
#define IDataWriter_WriteBuffer __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBuffer
#define IDataWriter_WriteBufferRange __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBufferRange
#define IDataWriter_WriteBoolean __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteBoolean
#define IDataWriter_WriteGuid __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteGuid
#define IDataWriter_WriteInt16 __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteInt16
#define IDataWriter_WriteInt32 __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteInt32
#define IDataWriter_WriteInt64 __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteInt64
#define IDataWriter_WriteUInt16 __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteUInt16
#define IDataWriter_WriteUInt32 __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteUInt32
#define IDataWriter_WriteUInt64 __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteUInt64
#define IDataWriter_WriteSingle __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteSingle
#define IDataWriter_WriteDouble __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteDouble
#define IDataWriter_WriteDateTime __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteDateTime
#define IDataWriter_WriteTimeSpan __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteTimeSpan
#define IDataWriter_WriteString __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_WriteString
#define IDataWriter_MeasureString __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_MeasureString
#define IDataWriter_StoreAsync __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_StoreAsync
#define IDataWriter_FlushAsync __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_FlushAsync
#define IDataWriter_DetachBuffer __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_DetachBuffer
#define IDataWriter_DetachStream __x_ABI_CWindows_CStorage_CStreams_CIDataWriter_DetachStream
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CStreams_CIDataWriter_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDataWriterFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory, 0x338c67c2, 0x8b84, 0x4c2b, 0x9c,0x50, 0x7b,0x87,0x67,0x84,0x7a,0x1f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                MIDL_INTERFACE("338c67c2-8b84-4c2b-9c50-7b8767847a1f")
                IDataWriterFactory : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateDataWriter(
                        ABI::Windows::Storage::Streams::IOutputStream *output_stream,
                        ABI::Windows::Storage::Streams::IDataWriter **data_writer) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory, 0x338c67c2, 0x8b84, 0x4c2b, 0x9c,0x50, 0x7b,0x87,0x67,0x84,0x7a,0x1f)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory *This,
        TrustLevel *trustLevel);

    /*** IDataWriterFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateDataWriter)(
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory *This,
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream *output_stream,
        __x_ABI_CWindows_CStorage_CStreams_CIDataWriter **data_writer);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactoryVtbl;

interface __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory {
    CONST_VTBL __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDataWriterFactory methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_CreateDataWriter(This,output_stream,data_writer) (This)->lpVtbl->CreateDataWriter(This,output_stream,data_writer)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_QueryInterface(__x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_AddRef(__x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_Release(__x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_GetIids(__x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_GetTrustLevel(__x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDataWriterFactory methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_CreateDataWriter(__x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory* This,__x_ABI_CWindows_CStorage_CStreams_CIOutputStream *output_stream,__x_ABI_CWindows_CStorage_CStreams_CIDataWriter **data_writer) {
    return This->lpVtbl->CreateDataWriter(This,output_stream,data_writer);
}
#endif
#ifdef WIDL_using_Windows_Storage_Streams
#define IID_IDataWriterFactory IID___x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory
#define IDataWriterFactoryVtbl __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactoryVtbl
#define IDataWriterFactory __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory
#define IDataWriterFactory_QueryInterface __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_QueryInterface
#define IDataWriterFactory_AddRef __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_AddRef
#define IDataWriterFactory_Release __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_Release
#define IDataWriterFactory_GetIids __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_GetIids
#define IDataWriterFactory_GetRuntimeClassName __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_GetRuntimeClassName
#define IDataWriterFactory_GetTrustLevel __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_GetTrustLevel
#define IDataWriterFactory_CreateDataWriter __x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_CreateDataWriter
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CStreams_CIDataWriterFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IOutputStream interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIOutputStream_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIOutputStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CStreams_CIOutputStream, 0x905a0fe6, 0xbc53, 0x11df, 0x8c,0x49, 0x00,0x1e,0x4f,0xc6,0x86,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                MIDL_INTERFACE("905a0fe6-bc53-11df-8c49-001e4fc686da")
                IOutputStream : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE WriteAsync(
                        ABI::Windows::Storage::Streams::IBuffer *buffer,
                        ABI::Windows::Foundation::IAsyncOperationWithProgress<UINT32,UINT32 > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FlushAsync(
                        ABI::Windows::Foundation::IAsyncOperation<boolean > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CStreams_CIOutputStream, 0x905a0fe6, 0xbc53, 0x11df, 0x8c,0x49, 0x00,0x1e,0x4f,0xc6,0x86,0xda)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CIOutputStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream *This,
        TrustLevel *trustLevel);

    /*** IOutputStream methods ***/
    HRESULT (STDMETHODCALLTYPE *WriteAsync)(
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer,
        __FIAsyncOperationWithProgress_2_UINT32_UINT32 **operation);

    HRESULT (STDMETHODCALLTYPE *FlushAsync)(
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream *This,
        __FIAsyncOperation_1_boolean **operation);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CStreams_CIOutputStreamVtbl;

interface __x_ABI_CWindows_CStorage_CStreams_CIOutputStream {
    CONST_VTBL __x_ABI_CWindows_CStorage_CStreams_CIOutputStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IOutputStream methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_WriteAsync(This,buffer,operation) (This)->lpVtbl->WriteAsync(This,buffer,operation)
#define __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_FlushAsync(This,operation) (This)->lpVtbl->FlushAsync(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_QueryInterface(__x_ABI_CWindows_CStorage_CStreams_CIOutputStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_AddRef(__x_ABI_CWindows_CStorage_CStreams_CIOutputStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_Release(__x_ABI_CWindows_CStorage_CStreams_CIOutputStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_GetIids(__x_ABI_CWindows_CStorage_CStreams_CIOutputStream* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CStreams_CIOutputStream* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_GetTrustLevel(__x_ABI_CWindows_CStorage_CStreams_CIOutputStream* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IOutputStream methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_WriteAsync(__x_ABI_CWindows_CStorage_CStreams_CIOutputStream* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer *buffer,__FIAsyncOperationWithProgress_2_UINT32_UINT32 **operation) {
    return This->lpVtbl->WriteAsync(This,buffer,operation);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_FlushAsync(__x_ABI_CWindows_CStorage_CStreams_CIOutputStream* This,__FIAsyncOperation_1_boolean **operation) {
    return This->lpVtbl->FlushAsync(This,operation);
}
#endif
#ifdef WIDL_using_Windows_Storage_Streams
#define IID_IOutputStream IID___x_ABI_CWindows_CStorage_CStreams_CIOutputStream
#define IOutputStreamVtbl __x_ABI_CWindows_CStorage_CStreams_CIOutputStreamVtbl
#define IOutputStream __x_ABI_CWindows_CStorage_CStreams_CIOutputStream
#define IOutputStream_QueryInterface __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_QueryInterface
#define IOutputStream_AddRef __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_AddRef
#define IOutputStream_Release __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_Release
#define IOutputStream_GetIids __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_GetIids
#define IOutputStream_GetRuntimeClassName __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_GetRuntimeClassName
#define IOutputStream_GetTrustLevel __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_GetTrustLevel
#define IOutputStream_WriteAsync __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_WriteAsync
#define IOutputStream_FlushAsync __x_ABI_CWindows_CStorage_CStreams_CIOutputStream_FlushAsync
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CStreams_CIOutputStream_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IRandomAccessStream interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream, 0x905a0fe1, 0xbc53, 0x11df, 0x8c,0x49, 0x00,0x1e,0x4f,0xc6,0x86,0xda);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                MIDL_INTERFACE("905a0fe1-bc53-11df-8c49-001e4fc686da")
                IRandomAccessStream : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Size(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_Size(
                        UINT64 value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetInputStreamAt(
                        UINT64 position,
                        ABI::Windows::Storage::Streams::IInputStream **stream) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetOutputStreamAt(
                        UINT64 position,
                        ABI::Windows::Storage::Streams::IOutputStream **stream) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Position(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Seek(
                        UINT64 position) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CloneStream(
                        ABI::Windows::Storage::Streams::IRandomAccessStream **stream) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_CanRead(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_CanWrite(
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream, 0x905a0fe1, 0xbc53, 0x11df, 0x8c,0x49, 0x00,0x1e,0x4f,0xc6,0x86,0xda)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        TrustLevel *trustLevel);

    /*** IRandomAccessStream methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *put_Size)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        UINT64 value);

    HRESULT (STDMETHODCALLTYPE *GetInputStreamAt)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        UINT64 position,
        __x_ABI_CWindows_CStorage_CStreams_CIInputStream **stream);

    HRESULT (STDMETHODCALLTYPE *GetOutputStreamAt)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        UINT64 position,
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream **stream);

    HRESULT (STDMETHODCALLTYPE *get_Position)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *Seek)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        UINT64 position);

    HRESULT (STDMETHODCALLTYPE *CloneStream)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **stream);

    HRESULT (STDMETHODCALLTYPE *get_CanRead)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_CanWrite)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamVtbl;

interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream {
    CONST_VTBL __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRandomAccessStream methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_put_Size(This,value) (This)->lpVtbl->put_Size(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetInputStreamAt(This,position,stream) (This)->lpVtbl->GetInputStreamAt(This,position,stream)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetOutputStreamAt(This,position,stream) (This)->lpVtbl->GetOutputStreamAt(This,position,stream)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_Position(This,value) (This)->lpVtbl->get_Position(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_Seek(This,position) (This)->lpVtbl->Seek(This,position)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_CloneStream(This,stream) (This)->lpVtbl->CloneStream(This,stream)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_CanRead(This,value) (This)->lpVtbl->get_CanRead(This,value)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_CanWrite(This,value) (This)->lpVtbl->get_CanWrite(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_QueryInterface(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_AddRef(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_Release(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetIids(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetTrustLevel(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRandomAccessStream methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_Size(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,UINT64 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_put_Size(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,UINT64 value) {
    return This->lpVtbl->put_Size(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetInputStreamAt(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,UINT64 position,__x_ABI_CWindows_CStorage_CStreams_CIInputStream **stream) {
    return This->lpVtbl->GetInputStreamAt(This,position,stream);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetOutputStreamAt(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,UINT64 position,__x_ABI_CWindows_CStorage_CStreams_CIOutputStream **stream) {
    return This->lpVtbl->GetOutputStreamAt(This,position,stream);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_Position(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,UINT64 *value) {
    return This->lpVtbl->get_Position(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_Seek(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,UINT64 position) {
    return This->lpVtbl->Seek(This,position);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_CloneStream(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **stream) {
    return This->lpVtbl->CloneStream(This,stream);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_CanRead(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,boolean *value) {
    return This->lpVtbl->get_CanRead(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_CanWrite(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream* This,boolean *value) {
    return This->lpVtbl->get_CanWrite(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage_Streams
#define IID_IRandomAccessStream IID___x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream
#define IRandomAccessStreamVtbl __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamVtbl
#define IRandomAccessStream __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream
#define IRandomAccessStream_QueryInterface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_QueryInterface
#define IRandomAccessStream_AddRef __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_AddRef
#define IRandomAccessStream_Release __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_Release
#define IRandomAccessStream_GetIids __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetIids
#define IRandomAccessStream_GetRuntimeClassName __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetRuntimeClassName
#define IRandomAccessStream_GetTrustLevel __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetTrustLevel
#define IRandomAccessStream_get_Size __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_Size
#define IRandomAccessStream_put_Size __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_put_Size
#define IRandomAccessStream_GetInputStreamAt __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetInputStreamAt
#define IRandomAccessStream_GetOutputStreamAt __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_GetOutputStreamAt
#define IRandomAccessStream_get_Position __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_Position
#define IRandomAccessStream_Seek __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_Seek
#define IRandomAccessStream_CloneStream __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_CloneStream
#define IRandomAccessStream_get_CanRead __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_CanRead
#define IRandomAccessStream_get_CanWrite __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_get_CanWrite
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IRandomAccessStreamReference interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference, 0x33ee3134, 0x1dd6, 0x4e3a, 0x80,0x67, 0xd1,0xc1,0x62,0xe8,0x64,0x2b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                MIDL_INTERFACE("33ee3134-1dd6-4e3a-8067-d1c162e8642b")
                IRandomAccessStreamReference : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE OpenReadAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference, 0x33ee3134, 0x1dd6, 0x4e3a, 0x80,0x67, 0xd1,0xc1,0x62,0xe8,0x64,0x2b)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference *This,
        TrustLevel *trustLevel);

    /*** IRandomAccessStreamReference methods ***/
    HRESULT (STDMETHODCALLTYPE *OpenReadAsync)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference *This,
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType **operation);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceVtbl;

interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference {
    CONST_VTBL __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRandomAccessStreamReference methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_OpenReadAsync(This,operation) (This)->lpVtbl->OpenReadAsync(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_QueryInterface(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_AddRef(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_Release(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_GetIids(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_GetTrustLevel(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRandomAccessStreamReference methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_OpenReadAsync(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference* This,__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType **operation) {
    return This->lpVtbl->OpenReadAsync(This,operation);
}
#endif
#ifdef WIDL_using_Windows_Storage_Streams
#define IID_IRandomAccessStreamReference IID___x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference
#define IRandomAccessStreamReferenceVtbl __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceVtbl
#define IRandomAccessStreamReference __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference
#define IRandomAccessStreamReference_QueryInterface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_QueryInterface
#define IRandomAccessStreamReference_AddRef __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_AddRef
#define IRandomAccessStreamReference_Release __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_Release
#define IRandomAccessStreamReference_GetIids __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_GetIids
#define IRandomAccessStreamReference_GetRuntimeClassName __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_GetRuntimeClassName
#define IRandomAccessStreamReference_GetTrustLevel __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_GetTrustLevel
#define IRandomAccessStreamReference_OpenReadAsync __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_OpenReadAsync
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IRandomAccessStreamReferenceStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics, 0x857309dc, 0x3fbf, 0x4e7d, 0x98,0x6f, 0xef,0x3b,0x1a,0x07,0xa9,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                MIDL_INTERFACE("857309dc-3fbf-4e7d-986f-ef3b1a07a964")
                IRandomAccessStreamReferenceStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateFromFile(
                        ABI::Windows::Storage::IStorageFile *file,
                        ABI::Windows::Storage::Streams::IRandomAccessStreamReference **stream_reference) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateFromUri(
                        ABI::Windows::Foundation::IUriRuntimeClass *uri,
                        ABI::Windows::Storage::Streams::IRandomAccessStreamReference **stream_reference) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateFromStream(
                        ABI::Windows::Storage::Streams::IRandomAccessStream *stream,
                        ABI::Windows::Storage::Streams::IRandomAccessStreamReference **stream_reference) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics, 0x857309dc, 0x3fbf, 0x4e7d, 0x98,0x6f, 0xef,0x3b,0x1a,0x07,0xa9,0x64)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics *This,
        TrustLevel *trustLevel);

    /*** IRandomAccessStreamReferenceStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateFromFile)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics *This,
        __x_ABI_CWindows_CStorage_CIStorageFile *file,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **stream_reference);

    HRESULT (STDMETHODCALLTYPE *CreateFromUri)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics *This,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **stream_reference);

    HRESULT (STDMETHODCALLTYPE *CreateFromStream)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *stream,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **stream_reference);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStaticsVtbl;

interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics {
    CONST_VTBL __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRandomAccessStreamReferenceStatics methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_CreateFromFile(This,file,stream_reference) (This)->lpVtbl->CreateFromFile(This,file,stream_reference)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_CreateFromUri(This,uri,stream_reference) (This)->lpVtbl->CreateFromUri(This,uri,stream_reference)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_CreateFromStream(This,stream,stream_reference) (This)->lpVtbl->CreateFromStream(This,stream,stream_reference)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_QueryInterface(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_AddRef(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_Release(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_GetIids(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_GetTrustLevel(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRandomAccessStreamReferenceStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_CreateFromFile(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics* This,__x_ABI_CWindows_CStorage_CIStorageFile *file,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **stream_reference) {
    return This->lpVtbl->CreateFromFile(This,file,stream_reference);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_CreateFromUri(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics* This,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **stream_reference) {
    return This->lpVtbl->CreateFromUri(This,uri,stream_reference);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_CreateFromStream(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *stream,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **stream_reference) {
    return This->lpVtbl->CreateFromStream(This,stream,stream_reference);
}
#endif
#ifdef WIDL_using_Windows_Storage_Streams
#define IID_IRandomAccessStreamReferenceStatics IID___x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics
#define IRandomAccessStreamReferenceStaticsVtbl __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStaticsVtbl
#define IRandomAccessStreamReferenceStatics __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics
#define IRandomAccessStreamReferenceStatics_QueryInterface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_QueryInterface
#define IRandomAccessStreamReferenceStatics_AddRef __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_AddRef
#define IRandomAccessStreamReferenceStatics_Release __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_Release
#define IRandomAccessStreamReferenceStatics_GetIids __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_GetIids
#define IRandomAccessStreamReferenceStatics_GetRuntimeClassName __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_GetRuntimeClassName
#define IRandomAccessStreamReferenceStatics_GetTrustLevel __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_GetTrustLevel
#define IRandomAccessStreamReferenceStatics_CreateFromFile __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_CreateFromFile
#define IRandomAccessStreamReferenceStatics_CreateFromUri __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_CreateFromUri
#define IRandomAccessStreamReferenceStatics_CreateFromStream __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_CreateFromStream
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReferenceStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IRandomAccessStreamWithContentType interface
 */
#ifndef ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType, 0xcc254827, 0x4b3d, 0x438f, 0x92,0x32, 0x10,0xc7,0x6b,0xc7,0xe0,0x38);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace Streams {
                MIDL_INTERFACE("cc254827-4b3d-438f-9232-10c76bc7e038")
                IRandomAccessStreamWithContentType : public IInspectable
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType, 0xcc254827, 0x4b3d, 0x438f, 0x92,0x32, 0x10,0xc7,0x6b,0xc7,0xe0,0x38)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentTypeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentTypeVtbl;

interface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType {
    CONST_VTBL __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentTypeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_QueryInterface(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_AddRef(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_Release(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_GetIids(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_GetTrustLevel(__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_Storage_Streams
#define IID_IRandomAccessStreamWithContentType IID___x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType
#define IRandomAccessStreamWithContentTypeVtbl __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentTypeVtbl
#define IRandomAccessStreamWithContentType __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType
#define IRandomAccessStreamWithContentType_QueryInterface __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_QueryInterface
#define IRandomAccessStreamWithContentType_AddRef __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_AddRef
#define IRandomAccessStreamWithContentType_Release __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_Release
#define IRandomAccessStreamWithContentType_GetIids __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_GetIids
#define IRandomAccessStreamWithContentType_GetRuntimeClassName __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_GetRuntimeClassName
#define IRandomAccessStreamWithContentType_GetTrustLevel __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_GetTrustLevel
#endif /* WIDL_using_Windows_Storage_Streams */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType_INTERFACE_DEFINED__ */

/*
 * Class Windows.Storage.Streams.Buffer
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_Streams_Buffer_DEFINED
#define RUNTIMECLASS_Windows_Storage_Streams_Buffer_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_Streams_Buffer[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','B','u','f','f','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_Buffer[] = L"Windows.Storage.Streams.Buffer";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_Buffer[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','B','u','f','f','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_Streams_Buffer_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.Streams.DataWriter
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_Streams_DataWriter_DEFINED
#define RUNTIMECLASS_Windows_Storage_Streams_DataWriter_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_Streams_DataWriter[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','D','a','t','a','W','r','i','t','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_DataWriter[] = L"Windows.Storage.Streams.DataWriter";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_DataWriter[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','D','a','t','a','W','r','i','t','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_Streams_DataWriter_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.Streams.DataWriterStoreOperation
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_Streams_DataWriterStoreOperation_DEFINED
#define RUNTIMECLASS_Windows_Storage_Streams_DataWriterStoreOperation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_Streams_DataWriterStoreOperation[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','D','a','t','a','W','r','i','t','e','r','S','t','o','r','e','O','p','e','r','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_DataWriterStoreOperation[] = L"Windows.Storage.Streams.DataWriterStoreOperation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_DataWriterStoreOperation[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','D','a','t','a','W','r','i','t','e','r','S','t','o','r','e','O','p','e','r','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_Streams_DataWriterStoreOperation_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.Streams.InMemoryRandomAccessStream
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_Streams_InMemoryRandomAccessStream_DEFINED
#define RUNTIMECLASS_Windows_Storage_Streams_InMemoryRandomAccessStream_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_Streams_InMemoryRandomAccessStream[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','I','n','M','e','m','o','r','y','R','a','n','d','o','m','A','c','c','e','s','s','S','t','r','e','a','m',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_InMemoryRandomAccessStream[] = L"Windows.Storage.Streams.InMemoryRandomAccessStream";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_InMemoryRandomAccessStream[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','I','n','M','e','m','o','r','y','R','a','n','d','o','m','A','c','c','e','s','s','S','t','r','e','a','m',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_Streams_InMemoryRandomAccessStream_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.Streams.RandomAccessStream
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_Streams_RandomAccessStream_DEFINED
#define RUNTIMECLASS_Windows_Storage_Streams_RandomAccessStream_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_Streams_RandomAccessStream[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','R','a','n','d','o','m','A','c','c','e','s','s','S','t','r','e','a','m',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_RandomAccessStream[] = L"Windows.Storage.Streams.RandomAccessStream";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_RandomAccessStream[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','R','a','n','d','o','m','A','c','c','e','s','s','S','t','r','e','a','m',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_Streams_RandomAccessStream_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.Streams.RandomAccessStreamReference
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_Streams_RandomAccessStreamReference_DEFINED
#define RUNTIMECLASS_Windows_Storage_Streams_RandomAccessStreamReference_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_Streams_RandomAccessStreamReference[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','R','a','n','d','o','m','A','c','c','e','s','s','S','t','r','e','a','m','R','e','f','e','r','e','n','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_RandomAccessStreamReference[] = L"Windows.Storage.Streams.RandomAccessStreamReference";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_Streams_RandomAccessStreamReference[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','S','t','r','e','a','m','s','.','R','a','n','d','o','m','A','c','c','e','s','s','S','t','r','e','a','m','R','e','f','e','r','e','n','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_Streams_RandomAccessStreamReference_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IBuffer* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer, 0x51c3d2fd, 0xb8a1, 0x5620, 0xb7,0x46, 0x7e,0xe6,0xd5,0x33,0xac,0xa3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("51c3d2fd-b8a1-5620-b746-7ee6d533aca3")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IBuffer* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Storage::Streams::IBuffer* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer, 0x51c3d2fd, 0xb8a1, 0x5620, 0xb7,0x46, 0x7e,0xe6,0xd5,0x33,0xac,0xa3)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IBuffer* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer *This,
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBufferVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IBuffer* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IBuffer* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer* This,__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IBuffer IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer
#define IAsyncOperationCompletedHandler_IBufferVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBufferVtbl
#define IAsyncOperationCompletedHandler_IBuffer __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer
#define IAsyncOperationCompletedHandler_IBuffer_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_QueryInterface
#define IAsyncOperationCompletedHandler_IBuffer_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_AddRef
#define IAsyncOperationCompletedHandler_IBuffer_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_Release
#define IAsyncOperationCompletedHandler_IBuffer_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IOutputStream* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream, 0xbcb37f4f, 0x3af4, 0x561c, 0xa9,0xe3, 0xee,0xf1,0x73,0x84,0x94,0xd7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("bcb37f4f-3af4-561c-a9e3-eef1738494d7")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IOutputStream* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Storage::Streams::IOutputStream* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream, 0xbcb37f4f, 0x3af4, 0x561c, 0xa9,0xe3, 0xee,0xf1,0x73,0x84,0x94,0xd7)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IOutputStream* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream *This,
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStreamVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IOutputStream* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IOutputStream* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream* This,__FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IOutputStream IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream
#define IAsyncOperationCompletedHandler_IOutputStreamVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStreamVtbl
#define IAsyncOperationCompletedHandler_IOutputStream __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream
#define IAsyncOperationCompletedHandler_IOutputStream_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_QueryInterface
#define IAsyncOperationCompletedHandler_IOutputStream_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_AddRef
#define IAsyncOperationCompletedHandler_IOutputStream_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_Release
#define IAsyncOperationCompletedHandler_IOutputStream_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::Streams::IBuffer* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer, 0x3bee8834, 0xb9a7, 0x5a80, 0xa7,0x46, 0x5e,0xf0,0x97,0x22,0x78,0x78);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("3bee8834-b9a7-5a80-a746-5ef097227878")
            IAsyncOperation<ABI::Windows::Storage::Streams::IBuffer* > : IAsyncOperation_impl<ABI::Windows::Storage::Streams::IBuffer* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer, 0x3bee8834, 0xb9a7, 0x5a80, 0xa7,0x46, 0x5e,0xf0,0x97,0x22,0x78,0x78)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::Streams::IBuffer* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer *This,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBufferVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::Streams::IBuffer* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_AddRef(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_Release(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetIids(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::Streams::IBuffer* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIBuffer **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetResults(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer* This,__x_ABI_CWindows_CStorage_CStreams_CIBuffer **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IBuffer IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer
#define IAsyncOperation_IBufferVtbl __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBufferVtbl
#define IAsyncOperation_IBuffer __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer
#define IAsyncOperation_IBuffer_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_QueryInterface
#define IAsyncOperation_IBuffer_AddRef __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_AddRef
#define IAsyncOperation_IBuffer_Release __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_Release
#define IAsyncOperation_IBuffer_GetIids __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetIids
#define IAsyncOperation_IBuffer_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName
#define IAsyncOperation_IBuffer_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel
#define IAsyncOperation_IBuffer_put_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_put_Completed
#define IAsyncOperation_IBuffer_get_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_get_Completed
#define IAsyncOperation_IBuffer_GetResults __FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::Streams::IOutputStream* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream, 0xe8736833, 0xd013, 0x5361, 0x97,0x7d, 0xc5,0xe9,0x99,0x34,0x68,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("e8736833-d013-5361-977d-c5e99934680e")
            IAsyncOperation<ABI::Windows::Storage::Streams::IOutputStream* > : IAsyncOperation_impl<ABI::Windows::Storage::Streams::IOutputStream* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream, 0xe8736833, 0xd013, 0x5361, 0x97,0x7d, 0xc5,0xe9,0x99,0x34,0x68,0x0e)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::Streams::IOutputStream* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream *This,
        __x_ABI_CWindows_CStorage_CStreams_CIOutputStream **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStreamVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::Streams::IOutputStream* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_AddRef(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_Release(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetIids(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::Streams::IOutputStream* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIOutputStream **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetResults(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream* This,__x_ABI_CWindows_CStorage_CStreams_CIOutputStream **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IOutputStream IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream
#define IAsyncOperation_IOutputStreamVtbl __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStreamVtbl
#define IAsyncOperation_IOutputStream __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream
#define IAsyncOperation_IOutputStream_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_QueryInterface
#define IAsyncOperation_IOutputStream_AddRef __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_AddRef
#define IAsyncOperation_IOutputStream_Release __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_Release
#define IAsyncOperation_IOutputStream_GetIids __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetIids
#define IAsyncOperation_IOutputStream_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetRuntimeClassName
#define IAsyncOperation_IOutputStream_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetTrustLevel
#define IAsyncOperation_IOutputStream_put_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_put_Completed
#define IAsyncOperation_IOutputStream_get_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_get_Completed
#define IAsyncOperation_IOutputStream_GetResults __FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIOutputStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Storage::Streams::IRandomAccessStream* > interface
 */
#ifndef ____FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0xba666a00, 0x1555, 0x5df4, 0x81,0xa5, 0x07,0xd2,0x3f,0x7f,0xfc,0xeb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("ba666a00-1555-5df4-81a5-07d23f7ffceb")
                IIterable<ABI::Windows::Storage::Streams::IRandomAccessStream* > : IIterable_impl<ABI::Windows::Storage::Streams::IRandomAccessStream* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0xba666a00, 0x1555, 0x5df4, 0x81,0xa5, 0x07,0xd2,0x3f,0x7f,0xfc,0xeb)
#endif
#else
typedef struct __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream **value);

    END_INTERFACE
} __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl;

interface __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream {
    CONST_VTBL __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
#define __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(__FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(__FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(__FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids(__FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName(__FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel(__FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_First(__FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_IRandomAccessStream IID___FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IIterable_IRandomAccessStreamVtbl __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl
#define IIterable_IRandomAccessStream __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IIterable_IRandomAccessStream_QueryInterface __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface
#define IIterable_IRandomAccessStream_AddRef __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef
#define IIterable_IRandomAccessStream_Release __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release
#define IIterable_IRandomAccessStream_GetIids __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids
#define IIterable_IRandomAccessStream_GetRuntimeClassName __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName
#define IIterable_IRandomAccessStream_GetTrustLevel __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel
#define IIterable_IRandomAccessStream_First __FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Storage::Streams::IRandomAccessStream* > interface
 */
#ifndef ____FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0xc875446a, 0x587f, 0x58da, 0x89,0x7e, 0x3b,0xbe,0x5e,0xc7,0xc3,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("c875446a-587f-58da-897e-3bbe5ec7c30b")
                IIterator<ABI::Windows::Storage::Streams::IRandomAccessStream* > : IIterator_impl<ABI::Windows::Storage::Streams::IRandomAccessStream* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0xc875446a, 0x587f, 0x58da, 0x89,0x7e, 0x3b,0xbe,0x5e,0xc7,0xc3,0x0b)
#endif
#else
typedef struct __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        UINT32 items_size,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl;

interface __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream {
    CONST_VTBL __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids(__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName(__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel(__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Current(__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_HasCurrent(__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_MoveNext(__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetMany(__FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,UINT32 items_size,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_IRandomAccessStream IID___FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IIterator_IRandomAccessStreamVtbl __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl
#define IIterator_IRandomAccessStream __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IIterator_IRandomAccessStream_QueryInterface __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface
#define IIterator_IRandomAccessStream_AddRef __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef
#define IIterator_IRandomAccessStream_Release __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release
#define IIterator_IRandomAccessStream_GetIids __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids
#define IIterator_IRandomAccessStream_GetRuntimeClassName __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName
#define IIterator_IRandomAccessStream_GetTrustLevel __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel
#define IIterator_IRandomAccessStream_get_Current __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Current
#define IIterator_IRandomAccessStream_get_HasCurrent __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_HasCurrent
#define IIterator_IRandomAccessStream_MoveNext __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_MoveNext
#define IIterator_IRandomAccessStream_GetMany __FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* > interface
 */
#ifndef ____FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__
#define ____FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0x0a4ce7a5, 0xdfe0, 0x5796, 0xa4,0x38, 0xef,0xfd,0xfa,0xa3,0x1f,0x1b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("0a4ce7a5-dfe0-5796-a438-effdfaa31f1b")
                IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* > : IMapView_impl<HSTRING, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::Streams::RandomAccessStreamReference*, ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0x0a4ce7a5, 0xdfe0, 0x5796, 0xa4,0x38, 0xef,0xfd,0xfa,0xa3,0x1f,0x1b)
#endif
#else
typedef struct __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        TrustLevel *trustLevel);

    /*** IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        HSTRING key,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        HSTRING key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *Split)(
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference **first,
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference **second);

    END_INTERFACE
} __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl;

interface __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference {
    CONST_VTBL __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Split(This,first,second) (This)->lpVtbl->Split(This,first,second)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids(__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName(__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel(__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Lookup(__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,HSTRING key,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Size(__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_HasKey(__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,HSTRING key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Split(__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference **first,__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference **second) {
    return This->lpVtbl->Split(This,first,second);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMapView_HSTRING_RandomAccessStreamReference IID___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IMapView_HSTRING_RandomAccessStreamReferenceVtbl __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl
#define IMapView_HSTRING_RandomAccessStreamReference __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IMapView_HSTRING_RandomAccessStreamReference_QueryInterface __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface
#define IMapView_HSTRING_RandomAccessStreamReference_AddRef __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef
#define IMapView_HSTRING_RandomAccessStreamReference_Release __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release
#define IMapView_HSTRING_RandomAccessStreamReference_GetIids __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids
#define IMapView_HSTRING_RandomAccessStreamReference_GetRuntimeClassName __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName
#define IMapView_HSTRING_RandomAccessStreamReference_GetTrustLevel __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel
#define IMapView_HSTRING_RandomAccessStreamReference_Lookup __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Lookup
#define IMapView_HSTRING_RandomAccessStreamReference_get_Size __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Size
#define IMapView_HSTRING_RandomAccessStreamReference_HasKey __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_HasKey
#define IMapView_HSTRING_RandomAccessStreamReference_Split __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Split
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMap<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* > interface
 */
#ifndef ____FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__
#define ____FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0xe5d2ccfc, 0x825a, 0x5a8e, 0x82,0xaa, 0x09,0x5e,0xd5,0xdb,0xd5,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("e5d2ccfc-825a-5a8e-82aa-095ed5dbd5d1")
                IMap<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* > : IMap_impl<HSTRING, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::Streams::RandomAccessStreamReference*, ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0xe5d2ccfc, 0x825a, 0x5a8e, 0x82,0xaa, 0x09,0x5e,0xd5,0xdb,0xd5,0xd1)
#endif
#else
typedef struct __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        TrustLevel *trustLevel);

    /*** IMap<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        HSTRING key,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        HSTRING key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference **view);

    HRESULT (STDMETHODCALLTYPE *Insert)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        HSTRING key,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference *value,
        boolean *replaced);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        HSTRING key);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    END_INTERFACE
} __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl;

interface __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference {
    CONST_VTBL __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMap<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetView(This,view) (This)->lpVtbl->GetView(This,view)
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Insert(This,key,value,replaced) (This)->lpVtbl->Insert(This,key,value,replaced)
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Remove(This,key) (This)->lpVtbl->Remove(This,key)
#define __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMap<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
static inline HRESULT __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Lookup(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,HSTRING key,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Size(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_HasKey(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,HSTRING key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetView(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference **view) {
    return This->lpVtbl->GetView(This,view);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Insert(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,HSTRING key,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference *value,boolean *replaced) {
    return This->lpVtbl->Insert(This,key,value,replaced);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Remove(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,HSTRING key) {
    return This->lpVtbl->Remove(This,key);
}
static inline HRESULT __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Clear(__FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->Clear(This);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMap_HSTRING_RandomAccessStreamReference IID___FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IMap_HSTRING_RandomAccessStreamReferenceVtbl __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl
#define IMap_HSTRING_RandomAccessStreamReference __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IMap_HSTRING_RandomAccessStreamReference_QueryInterface __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface
#define IMap_HSTRING_RandomAccessStreamReference_AddRef __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef
#define IMap_HSTRING_RandomAccessStreamReference_Release __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release
#define IMap_HSTRING_RandomAccessStreamReference_GetIids __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids
#define IMap_HSTRING_RandomAccessStreamReference_GetRuntimeClassName __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName
#define IMap_HSTRING_RandomAccessStreamReference_GetTrustLevel __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel
#define IMap_HSTRING_RandomAccessStreamReference_Lookup __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Lookup
#define IMap_HSTRING_RandomAccessStreamReference_get_Size __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Size
#define IMap_HSTRING_RandomAccessStreamReference_HasKey __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_HasKey
#define IMap_HSTRING_RandomAccessStreamReference_GetView __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetView
#define IMap_HSTRING_RandomAccessStreamReference_Insert __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Insert
#define IMap_HSTRING_RandomAccessStreamReference_Remove __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Remove
#define IMap_HSTRING_RandomAccessStreamReference_Clear __FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Clear
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMap_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Storage::Streams::IRandomAccessStream* > interface
 */
#ifndef ____FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0x92cd0a46, 0x2266, 0x5cd6, 0x92,0x93, 0xe1,0x11,0x29,0x9f,0x27,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("92cd0a46-2266-5cd6-9293-e111299f2793")
                IVectorView<ABI::Windows::Storage::Streams::IRandomAccessStream* > : IVectorView_impl<ABI::Windows::Storage::Streams::IRandomAccessStream* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0x92cd0a46, 0x2266, 0x5cd6, 0x92,0x93, 0xe1,0x11,0x29,0x9f,0x27,0x93)
#endif
#else
typedef struct __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        UINT32 index,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl;

interface __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream {
    CONST_VTBL __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids(__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName(__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel(__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetAt(__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,UINT32 index,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Size(__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_IndexOf(__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetMany(__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_IRandomAccessStream IID___FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IVectorView_IRandomAccessStreamVtbl __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl
#define IVectorView_IRandomAccessStream __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IVectorView_IRandomAccessStream_QueryInterface __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface
#define IVectorView_IRandomAccessStream_AddRef __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef
#define IVectorView_IRandomAccessStream_Release __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release
#define IVectorView_IRandomAccessStream_GetIids __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids
#define IVectorView_IRandomAccessStream_GetRuntimeClassName __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName
#define IVectorView_IRandomAccessStream_GetTrustLevel __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel
#define IVectorView_IRandomAccessStream_GetAt __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetAt
#define IVectorView_IRandomAccessStream_get_Size __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Size
#define IVectorView_IRandomAccessStream_IndexOf __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_IndexOf
#define IVectorView_IRandomAccessStream_GetMany __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Storage::Streams::IRandomAccessStream* > interface
 */
#ifndef ____FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0x2736b66b, 0xdaa3, 0x5e0c, 0x98,0x42, 0x6a,0x0f,0x44,0xb5,0x44,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("2736b66b-daa3-5e0c-9842-6a0f44b5440b")
                IVector<ABI::Windows::Storage::Streams::IRandomAccessStream* > : IVector_impl<ABI::Windows::Storage::Streams::IRandomAccessStream* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0x2736b66b, 0xdaa3, 0x5e0c, 0x98,0x42, 0x6a,0x0f,0x44,0xb5,0x44,0x0b)
#endif
#else
typedef struct __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        UINT32 index,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        __FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        UINT32 index,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        UINT32 index,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        UINT32 count,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **items);

    END_INTERFACE
} __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl;

interface __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream {
    CONST_VTBL __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetAt(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,UINT32 index,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Size(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetView(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,__FIVectorView_1_Windows__CStorage__CStreams__CIRandomAccessStream **value) {
    return This->lpVtbl->GetView(This,value);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_IndexOf(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_SetAt(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,UINT32 index,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_InsertAt(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,UINT32 index,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_RemoveAt(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_Append(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream *value) {
    return This->lpVtbl->Append(This,value);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_RemoveAtEnd(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_Clear(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->Clear(This);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetMany(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static inline HRESULT __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_ReplaceAll(__FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,UINT32 count,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_IRandomAccessStream IID___FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IVector_IRandomAccessStreamVtbl __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl
#define IVector_IRandomAccessStream __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IVector_IRandomAccessStream_QueryInterface __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface
#define IVector_IRandomAccessStream_AddRef __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef
#define IVector_IRandomAccessStream_Release __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release
#define IVector_IRandomAccessStream_GetIids __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids
#define IVector_IRandomAccessStream_GetRuntimeClassName __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName
#define IVector_IRandomAccessStream_GetTrustLevel __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel
#define IVector_IRandomAccessStream_GetAt __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetAt
#define IVector_IRandomAccessStream_get_Size __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Size
#define IVector_IRandomAccessStream_GetView __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetView
#define IVector_IRandomAccessStream_IndexOf __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_IndexOf
#define IVector_IRandomAccessStream_SetAt __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_SetAt
#define IVector_IRandomAccessStream_InsertAt __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_InsertAt
#define IVector_IRandomAccessStream_RemoveAt __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_RemoveAt
#define IVector_IRandomAccessStream_Append __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_Append
#define IVector_IRandomAccessStream_RemoveAtEnd __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_RemoveAtEnd
#define IVector_IRandomAccessStream_Clear __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_Clear
#define IVector_IRandomAccessStream_GetMany __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetMany
#define IVector_IRandomAccessStream_ReplaceAll __FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0xd4cb6b80, 0x821a, 0x5a7b, 0x89,0x8d, 0xd5,0x89,0x17,0xb3,0x1a,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d4cb6b80-821a-5a7b-898d-d58917b31a36")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0xd4cb6b80, 0x821a, 0x5a7b, 0x89,0x8d, 0xd5,0x89,0x17,0xb3,0x1a,0x36)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Invoke(__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,__FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IMapView_HSTRING_RandomAccessStreamReference IID___FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_RandomAccessStreamReferenceVtbl __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_RandomAccessStreamReference __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_RandomAccessStreamReference_QueryInterface __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_RandomAccessStreamReference_AddRef __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_RandomAccessStreamReference_Release __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release
#define IAsyncOperationCompletedHandler_IMapView_HSTRING_RandomAccessStreamReference_Invoke __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStream* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0x398c4183, 0x793d, 0x5b00, 0x81,0x9b, 0x4a,0xef,0x92,0x48,0x5e,0x94);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("398c4183-793d-5b00-819b-4aef92485e94")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStream* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Storage::Streams::IRandomAccessStream* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0x398c4183, 0x793d, 0x5b00, 0x81,0x9b, 0x4a,0xef,0x92,0x48,0x5e,0x94)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IRandomAccessStream IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IAsyncOperationCompletedHandler_IRandomAccessStreamVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl
#define IAsyncOperationCompletedHandler_IRandomAccessStream __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IAsyncOperationCompletedHandler_IRandomAccessStream_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface
#define IAsyncOperationCompletedHandler_IRandomAccessStream_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef
#define IAsyncOperationCompletedHandler_IRandomAccessStream_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release
#define IAsyncOperationCompletedHandler_IRandomAccessStream_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference, 0x60847289, 0xea0b, 0x5df6, 0x89,0xdf, 0xf2,0xc6,0x2c,0xba,0x96,0x93);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("60847289-ea0b-5df6-89df-f2c62cba9693")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference, 0x60847289, 0xea0b, 0x5df6, 0x89,0xdf, 0xf2,0xc6,0x2c,0xba,0x96,0x93)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This,
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReferenceVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This,__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IRandomAccessStreamReference IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference
#define IAsyncOperationCompletedHandler_IRandomAccessStreamReferenceVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReferenceVtbl
#define IAsyncOperationCompletedHandler_IRandomAccessStreamReference __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference
#define IAsyncOperationCompletedHandler_IRandomAccessStreamReference_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_QueryInterface
#define IAsyncOperationCompletedHandler_IRandomAccessStreamReference_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_AddRef
#define IAsyncOperationCompletedHandler_IRandomAccessStreamReference_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_Release
#define IAsyncOperationCompletedHandler_IRandomAccessStreamReference_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType, 0x3dddecf4, 0x1d39, 0x58e8, 0x83,0xb1, 0xdb,0xed,0x54,0x1c,0x7f,0x35);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("3dddecf4-1d39-58e8-83b1-dbed541c7f35")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType, 0x3dddecf4, 0x1d39, 0x58e8, 0x83,0xb1, 0xdb,0xed,0x54,0x1c,0x7f,0x35)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentTypeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This,
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentTypeVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentTypeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This,__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IRandomAccessStreamWithContentType IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType
#define IAsyncOperationCompletedHandler_IRandomAccessStreamWithContentTypeVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentTypeVtbl
#define IAsyncOperationCompletedHandler_IRandomAccessStreamWithContentType __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType
#define IAsyncOperationCompletedHandler_IRandomAccessStreamWithContentType_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_QueryInterface
#define IAsyncOperationCompletedHandler_IRandomAccessStreamWithContentType_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_AddRef
#define IAsyncOperationCompletedHandler_IRandomAccessStreamWithContentType_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_Release
#define IAsyncOperationCompletedHandler_IRandomAccessStreamWithContentType_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::RandomAccessStreamReference* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0x3d203732, 0xded7, 0x5d32, 0x87,0xe6, 0xc1,0x79,0x78,0x1f,0x79,0x1f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("3d203732-ded7-5d32-87e6-c179781f791f")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::RandomAccessStreamReference* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::Streams::RandomAccessStreamReference*, ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0x3d203732, 0xded7, 0x5d32, 0x87,0xe6, 0xc1,0x79,0x78,0x1f,0x79,0x1f)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,__FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_RandomAccessStreamReference IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IAsyncOperationCompletedHandler_RandomAccessStreamReferenceVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl
#define IAsyncOperationCompletedHandler_RandomAccessStreamReference __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IAsyncOperationCompletedHandler_RandomAccessStreamReference_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface
#define IAsyncOperationCompletedHandler_RandomAccessStreamReference_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef
#define IAsyncOperationCompletedHandler_RandomAccessStreamReference_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release
#define IAsyncOperationCompletedHandler_RandomAccessStreamReference_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0xfc012d44, 0x2dcf, 0x5162, 0xbe,0x9a, 0x76,0x68,0x67,0x5a,0xa5,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("fc012d44-2dcf-5162-be9a-7668675aa590")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0xfc012d44, 0x2dcf, 0x5162, 0xbe,0x9a, 0x76,0x68,0x67,0x5a,0xa5,0x90)
#endif
#else
typedef struct __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        __FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl;

interface __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference {
    CONST_VTBL __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* > methods ***/
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(__FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(__FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(__FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids(__FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName(__FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel(__FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Storage::Streams::RandomAccessStreamReference* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_put_Completed(__FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Completed(__FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,__FIAsyncOperationCompletedHandler_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetResults(__FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,__FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReference IID___FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReferenceVtbl __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl
#define IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReference __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReference_QueryInterface __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface
#define IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReference_AddRef __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef
#define IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReference_Release __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release
#define IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReference_GetIids __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids
#define IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReference_GetRuntimeClassName __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName
#define IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReference_GetTrustLevel __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel
#define IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReference_put_Completed __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_put_Completed
#define IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReference_get_Completed __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Completed
#define IAsyncOperation_IMapView_HSTRING_RandomAccessStreamReference_GetResults __FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIMapView_2_HSTRING_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStream* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0x430ecece, 0x1418, 0x5d19, 0x81,0xb2, 0x5d,0xdb,0x38,0x16,0x03,0xcc);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("430ecece-1418-5d19-81b2-5ddb381603cc")
            IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStream* > : IAsyncOperation_impl<ABI::Windows::Storage::Streams::IRandomAccessStream* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream, 0x430ecece, 0x1418, 0x5d19, 0x81,0xb2, 0x5d,0xdb,0x38,0x16,0x03,0xcc)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStream* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStream **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetResults(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStream **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IRandomAccessStream IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IAsyncOperation_IRandomAccessStreamVtbl __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamVtbl
#define IAsyncOperation_IRandomAccessStream __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream
#define IAsyncOperation_IRandomAccessStream_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_QueryInterface
#define IAsyncOperation_IRandomAccessStream_AddRef __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_AddRef
#define IAsyncOperation_IRandomAccessStream_Release __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_Release
#define IAsyncOperation_IRandomAccessStream_GetIids __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetIids
#define IAsyncOperation_IRandomAccessStream_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetRuntimeClassName
#define IAsyncOperation_IRandomAccessStream_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetTrustLevel
#define IAsyncOperation_IRandomAccessStream_put_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_put_Completed
#define IAsyncOperation_IRandomAccessStream_get_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_get_Completed
#define IAsyncOperation_IRandomAccessStream_GetResults __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStream_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference, 0x65178d50, 0xe6a2, 0x5d16, 0xb2,0x44, 0x65,0xe9,0x72,0x5e,0x5a,0x0c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("65178d50-e6a2-5d16-b244-65e9725e5a0c")
            IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > : IAsyncOperation_impl<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference, 0x65178d50, 0xe6a2, 0x5d16, 0xb2,0x44, 0x65,0xe9,0x72,0x5e,0x5a,0x0c)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReferenceVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_AddRef(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_Release(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetIids(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetResults(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IRandomAccessStreamReference IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference
#define IAsyncOperation_IRandomAccessStreamReferenceVtbl __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReferenceVtbl
#define IAsyncOperation_IRandomAccessStreamReference __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference
#define IAsyncOperation_IRandomAccessStreamReference_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_QueryInterface
#define IAsyncOperation_IRandomAccessStreamReference_AddRef __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_AddRef
#define IAsyncOperation_IRandomAccessStreamReference_Release __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_Release
#define IAsyncOperation_IRandomAccessStreamReference_GetIids __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetIids
#define IAsyncOperation_IRandomAccessStreamReference_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetRuntimeClassName
#define IAsyncOperation_IRandomAccessStreamReference_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetTrustLevel
#define IAsyncOperation_IRandomAccessStreamReference_put_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_put_Completed
#define IAsyncOperation_IRandomAccessStreamReference_get_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_get_Completed
#define IAsyncOperation_IRandomAccessStreamReference_GetResults __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamReference_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType, 0xc4a57c5e, 0x32b0, 0x55b3, 0xad,0x13, 0xce,0x1c,0x23,0x04,0x1e,0xd6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("c4a57c5e-32b0-55b3-ad13-ce1c23041ed6")
            IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > : IAsyncOperation_impl<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType, 0xc4a57c5e, 0x32b0, 0x55b3, 0xad,0x13, 0xce,0x1c,0x23,0x04,0x1e,0xd6)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentTypeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentTypeVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentTypeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_AddRef(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_Release(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetIids(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetResults(__FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IRandomAccessStreamWithContentType IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType
#define IAsyncOperation_IRandomAccessStreamWithContentTypeVtbl __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentTypeVtbl
#define IAsyncOperation_IRandomAccessStreamWithContentType __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType
#define IAsyncOperation_IRandomAccessStreamWithContentType_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_QueryInterface
#define IAsyncOperation_IRandomAccessStreamWithContentType_AddRef __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_AddRef
#define IAsyncOperation_IRandomAccessStreamWithContentType_Release __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_Release
#define IAsyncOperation_IRandomAccessStreamWithContentType_GetIids __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetIids
#define IAsyncOperation_IRandomAccessStreamWithContentType_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetRuntimeClassName
#define IAsyncOperation_IRandomAccessStreamWithContentType_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetTrustLevel
#define IAsyncOperation_IRandomAccessStreamWithContentType_put_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_put_Completed
#define IAsyncOperation_IRandomAccessStreamWithContentType_get_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_get_Completed
#define IAsyncOperation_IRandomAccessStreamWithContentType_GetResults __FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CStreams__CIRandomAccessStreamWithContentType_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::Streams::RandomAccessStreamReference* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0xd90442ca, 0x543c, 0x504b, 0x9e,0xb9, 0x29,0x4b,0xca,0xd8,0xa2,0x83);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d90442ca-543c-504b-9eb9-294bcad8a283")
            IAsyncOperation<ABI::Windows::Storage::Streams::RandomAccessStreamReference* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::Streams::RandomAccessStreamReference*, ABI::Windows::Storage::Streams::IRandomAccessStreamReference* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference, 0xd90442ca, 0x543c, 0x504b, 0x9e,0xb9, 0x29,0x4b,0xca,0xd8,0xa2,0x83)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef(__FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release(__FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids(__FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::Streams::RandomAccessStreamReference* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CStreams__CRandomAccessStreamReference **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetResults(__FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamReference **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_RandomAccessStreamReference IID___FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IAsyncOperation_RandomAccessStreamReferenceVtbl __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReferenceVtbl
#define IAsyncOperation_RandomAccessStreamReference __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference
#define IAsyncOperation_RandomAccessStreamReference_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_QueryInterface
#define IAsyncOperation_RandomAccessStreamReference_AddRef __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_AddRef
#define IAsyncOperation_RandomAccessStreamReference_Release __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_Release
#define IAsyncOperation_RandomAccessStreamReference_GetIids __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetIids
#define IAsyncOperation_RandomAccessStreamReference_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetRuntimeClassName
#define IAsyncOperation_RandomAccessStreamReference_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetTrustLevel
#define IAsyncOperation_RandomAccessStreamReference_put_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_put_Completed
#define IAsyncOperation_RandomAccessStreamReference_get_Completed __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_get_Completed
#define IAsyncOperation_RandomAccessStreamReference_GetResults __FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CStreams__CRandomAccessStreamReference_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_storage_streams_h__ */
