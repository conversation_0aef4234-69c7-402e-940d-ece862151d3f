/*** Autogenerated by WIDL 10.12 from include/windows.media.effects.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_media_effects_h__
#define __windows_media_effects_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager ABI::Windows::Media::Effects::IAudioCaptureEffectsManager
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                interface IAudioCaptureEffectsManager;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect ABI::Windows::Media::Effects::IAudioEffect
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                interface IAudioEffect;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics ABI::Windows::Media::Effects::IAudioEffectsManagerStatics
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                interface IAudioEffectsManagerStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager ABI::Windows::Media::Effects::IAudioRenderEffectsManager
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                interface IAudioRenderEffectsManager;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 ABI::Windows::Media::Effects::IAudioRenderEffectsManager2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                interface IAudioRenderEffectsManager2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CAudioCaptureEffectsManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CAudioCaptureEffectsManager_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                class AudioCaptureEffectsManager;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CEffects_CAudioCaptureEffectsManager __x_ABI_CWindows_CMedia_CEffects_CAudioCaptureEffectsManager;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CEffects_CAudioCaptureEffectsManager_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CAudioEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CAudioEffect_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                class AudioEffect;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CEffects_CAudioEffect __x_ABI_CWindows_CMedia_CEffects_CAudioEffect;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CEffects_CAudioEffect_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CAudioEffectsManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CAudioEffectsManager_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                class AudioEffectsManager;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CEffects_CAudioEffectsManager __x_ABI_CWindows_CMedia_CEffects_CAudioEffectsManager;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CEffects_CAudioEffectsManager_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CAudioRenderEffectsManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CAudioRenderEffectsManager_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                class AudioRenderEffectsManager;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CEffects_CAudioRenderEffectsManager __x_ABI_CWindows_CMedia_CEffects_CAudioRenderEffectsManager;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CEffects_CAudioRenderEffectsManager_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::Effects::AudioEffect* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::Effects::AudioEffect* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::Effects::AudioEffect* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::Effects::AudioCaptureEffectsManager*,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::Effects::AudioRenderEffectsManager*,IInspectable* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <windows.foundation.h>
#include <windows.media.h>
#include <windows.media.capture.h>
#include <windows.media.render.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CEffects_CAudioEffectType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CAudioEffectType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                enum AudioEffectType {
                    AudioEffectType_Other = 0,
                    AudioEffectType_AcousticEchoCancellation = 1,
                    AudioEffectType_NoiseSuppression = 2,
                    AudioEffectType_AutomaticGainControl = 3,
                    AudioEffectType_BeamForming = 4,
                    AudioEffectType_ConstantToneRemoval = 5,
                    AudioEffectType_Equalizer = 6,
                    AudioEffectType_LoudnessEqualizer = 7,
                    AudioEffectType_BassBoost = 8,
                    AudioEffectType_VirtualSurround = 9,
                    AudioEffectType_VirtualHeadphones = 10,
                    AudioEffectType_SpeakerFill = 11,
                    AudioEffectType_RoomCorrection = 12,
                    AudioEffectType_BassManagement = 13,
                    AudioEffectType_EnvironmentalEffects = 14,
                    AudioEffectType_SpeakerProtection = 15,
                    AudioEffectType_SpeakerCompensation = 16,
                    AudioEffectType_DynamicRangeCompression = 17,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000
                    AudioEffectType_FarFieldBeamForming = 18,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xd0000
                    AudioEffectType_DeepNoiseSuppression = 19
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xd0000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CEffects_CAudioEffectType {
    AudioEffectType_Other = 0,
    AudioEffectType_AcousticEchoCancellation = 1,
    AudioEffectType_NoiseSuppression = 2,
    AudioEffectType_AutomaticGainControl = 3,
    AudioEffectType_BeamForming = 4,
    AudioEffectType_ConstantToneRemoval = 5,
    AudioEffectType_Equalizer = 6,
    AudioEffectType_LoudnessEqualizer = 7,
    AudioEffectType_BassBoost = 8,
    AudioEffectType_VirtualSurround = 9,
    AudioEffectType_VirtualHeadphones = 10,
    AudioEffectType_SpeakerFill = 11,
    AudioEffectType_RoomCorrection = 12,
    AudioEffectType_BassManagement = 13,
    AudioEffectType_EnvironmentalEffects = 14,
    AudioEffectType_SpeakerProtection = 15,
    AudioEffectType_SpeakerCompensation = 16,
    AudioEffectType_DynamicRangeCompression = 17,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000
    AudioEffectType_FarFieldBeamForming = 18,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xd0000
    AudioEffectType_DeepNoiseSuppression = 19
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xd0000 */
};
#ifdef WIDL_using_Windows_Media_Effects
#define AudioEffectType __x_ABI_CWindows_CMedia_CEffects_CAudioEffectType
#endif /* WIDL_using_Windows_Media_Effects */
#endif

#endif /* ____x_ABI_CWindows_CMedia_CEffects_CAudioEffectType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CEffects_CAudioEffectType __x_ABI_CWindows_CMedia_CEffects_CAudioEffectType;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager ABI::Windows::Media::Effects::IAudioCaptureEffectsManager
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                interface IAudioCaptureEffectsManager;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect ABI::Windows::Media::Effects::IAudioEffect
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                interface IAudioEffect;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics ABI::Windows::Media::Effects::IAudioEffectsManagerStatics
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                interface IAudioEffectsManagerStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager ABI::Windows::Media::Effects::IAudioRenderEffectsManager
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                interface IAudioRenderEffectsManager;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 ABI::Windows::Media::Effects::IAudioRenderEffectsManager2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                interface IAudioRenderEffectsManager2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::Effects::AudioEffect* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::Effects::AudioEffect* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::Effects::AudioEffect* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IAudioCaptureEffectsManager interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager, 0x8f85c271, 0x038d, 0x4393, 0x82,0x98, 0x54,0x01,0x10,0x60,0x8e,0xef);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                MIDL_INTERFACE("8f85c271-038d-4393-8298-540110608eef")
                IAudioCaptureEffectsManager : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_AudioCaptureEffectsChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::Effects::AudioCaptureEffectsManager*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_AudioCaptureEffectsChanged(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetAudioCaptureEffects(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::Effects::AudioEffect* > **effects) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager, 0x8f85c271, 0x038d, 0x4393, 0x82,0x98, 0x54,0x01,0x10,0x60,0x8e,0xef)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager *This,
        TrustLevel *trustLevel);

    /*** IAudioCaptureEffectsManager methods ***/
    HRESULT (STDMETHODCALLTYPE *add_AudioCaptureEffectsChanged)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager *This,
        __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_AudioCaptureEffectsChanged)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *GetAudioCaptureEffects)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager *This,
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect **effects);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManagerVtbl;

interface __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager {
    CONST_VTBL __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAudioCaptureEffectsManager methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_add_AudioCaptureEffectsChanged(This,handler,token) (This)->lpVtbl->add_AudioCaptureEffectsChanged(This,handler,token)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_remove_AudioCaptureEffectsChanged(This,token) (This)->lpVtbl->remove_AudioCaptureEffectsChanged(This,token)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetAudioCaptureEffects(This,effects) (This)->lpVtbl->GetAudioCaptureEffects(This,effects)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_QueryInterface(__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_AddRef(__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_Release(__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetIids(__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetTrustLevel(__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAudioCaptureEffectsManager methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_add_AudioCaptureEffectsChanged(__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager* This,__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_AudioCaptureEffectsChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_remove_AudioCaptureEffectsChanged(__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_AudioCaptureEffectsChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetAudioCaptureEffects(__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager* This,__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect **effects) {
    return This->lpVtbl->GetAudioCaptureEffects(This,effects);
}
#endif
#ifdef WIDL_using_Windows_Media_Effects
#define IID_IAudioCaptureEffectsManager IID___x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager
#define IAudioCaptureEffectsManagerVtbl __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManagerVtbl
#define IAudioCaptureEffectsManager __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager
#define IAudioCaptureEffectsManager_QueryInterface __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_QueryInterface
#define IAudioCaptureEffectsManager_AddRef __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_AddRef
#define IAudioCaptureEffectsManager_Release __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_Release
#define IAudioCaptureEffectsManager_GetIids __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetIids
#define IAudioCaptureEffectsManager_GetRuntimeClassName __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetRuntimeClassName
#define IAudioCaptureEffectsManager_GetTrustLevel __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetTrustLevel
#define IAudioCaptureEffectsManager_add_AudioCaptureEffectsChanged __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_add_AudioCaptureEffectsChanged
#define IAudioCaptureEffectsManager_remove_AudioCaptureEffectsChanged __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_remove_AudioCaptureEffectsChanged
#define IAudioCaptureEffectsManager_GetAudioCaptureEffects __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_GetAudioCaptureEffects
#endif /* WIDL_using_Windows_Media_Effects */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAudioEffect interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CEffects_CIAudioEffect, 0x34aafa51, 0x9207, 0x4055, 0xbe,0x93, 0x6e,0x57,0x34,0xa8,0x6a,0xe4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                MIDL_INTERFACE("34aafa51-9207-4055-be93-6e5734a86ae4")
                IAudioEffect : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AudioEffectType(
                        ABI::Windows::Media::Effects::AudioEffectType *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect, 0x34aafa51, 0x9207, 0x4055, 0xbe,0x93, 0x6e,0x57,0x34,0xa8,0x6a,0xe4)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect *This,
        TrustLevel *trustLevel);

    /*** IAudioEffect methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AudioEffectType)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect *This,
        __x_ABI_CWindows_CMedia_CEffects_CAudioEffectType *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectVtbl;

interface __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect {
    CONST_VTBL __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAudioEffect methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_get_AudioEffectType(This,value) (This)->lpVtbl->get_AudioEffectType(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_QueryInterface(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_AddRef(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_Release(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_GetIids(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_GetTrustLevel(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAudioEffect methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_get_AudioEffectType(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect* This,__x_ABI_CWindows_CMedia_CEffects_CAudioEffectType *value) {
    return This->lpVtbl->get_AudioEffectType(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_Effects
#define IID_IAudioEffect IID___x_ABI_CWindows_CMedia_CEffects_CIAudioEffect
#define IAudioEffectVtbl __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectVtbl
#define IAudioEffect __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect
#define IAudioEffect_QueryInterface __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_QueryInterface
#define IAudioEffect_AddRef __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_AddRef
#define IAudioEffect_Release __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_Release
#define IAudioEffect_GetIids __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_GetIids
#define IAudioEffect_GetRuntimeClassName __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_GetRuntimeClassName
#define IAudioEffect_GetTrustLevel __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_GetTrustLevel
#define IAudioEffect_get_AudioEffectType __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_get_AudioEffectType
#endif /* WIDL_using_Windows_Media_Effects */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffect_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAudioEffectsManagerStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics, 0x66406c04, 0x86fa, 0x47cc, 0xa3,0x15, 0xf4,0x89,0xd8,0xc3,0xfe,0x10);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                MIDL_INTERFACE("66406c04-86fa-47cc-a315-f489d8c3fe10")
                IAudioEffectsManagerStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateAudioRenderEffectsManager(
                        HSTRING device_id,
                        ABI::Windows::Media::Render::AudioRenderCategory category,
                        ABI::Windows::Media::Effects::IAudioRenderEffectsManager **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateAudioRenderEffectsManagerWithMode(
                        HSTRING device_id,
                        ABI::Windows::Media::Render::AudioRenderCategory category,
                        ABI::Windows::Media::AudioProcessing mode,
                        ABI::Windows::Media::Effects::IAudioRenderEffectsManager **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateAudioCaptureEffectsManager(
                        HSTRING device_id,
                        ABI::Windows::Media::Capture::MediaCategory category,
                        ABI::Windows::Media::Effects::IAudioCaptureEffectsManager **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateAudioCaptureEffectsManagerWithMode(
                        HSTRING device_id,
                        ABI::Windows::Media::Capture::MediaCategory category,
                        ABI::Windows::Media::AudioProcessing mode,
                        ABI::Windows::Media::Effects::IAudioCaptureEffectsManager **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics, 0x66406c04, 0x86fa, 0x47cc, 0xa3,0x15, 0xf4,0x89,0xd8,0xc3,0xfe,0x10)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics *This,
        TrustLevel *trustLevel);

    /*** IAudioEffectsManagerStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateAudioRenderEffectsManager)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics *This,
        HSTRING device_id,
        __x_ABI_CWindows_CMedia_CRender_CAudioRenderCategory category,
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager **value);

    HRESULT (STDMETHODCALLTYPE *CreateAudioRenderEffectsManagerWithMode)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics *This,
        HSTRING device_id,
        __x_ABI_CWindows_CMedia_CRender_CAudioRenderCategory category,
        __x_ABI_CWindows_CMedia_CAudioProcessing mode,
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager **value);

    HRESULT (STDMETHODCALLTYPE *CreateAudioCaptureEffectsManager)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics *This,
        HSTRING device_id,
        __x_ABI_CWindows_CMedia_CCapture_CMediaCategory category,
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager **value);

    HRESULT (STDMETHODCALLTYPE *CreateAudioCaptureEffectsManagerWithMode)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics *This,
        HSTRING device_id,
        __x_ABI_CWindows_CMedia_CCapture_CMediaCategory category,
        __x_ABI_CWindows_CMedia_CAudioProcessing mode,
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager **value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStaticsVtbl;

interface __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics {
    CONST_VTBL __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAudioEffectsManagerStatics methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioRenderEffectsManager(This,device_id,category,value) (This)->lpVtbl->CreateAudioRenderEffectsManager(This,device_id,category,value)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioRenderEffectsManagerWithMode(This,device_id,category,mode,value) (This)->lpVtbl->CreateAudioRenderEffectsManagerWithMode(This,device_id,category,mode,value)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioCaptureEffectsManager(This,device_id,category,value) (This)->lpVtbl->CreateAudioCaptureEffectsManager(This,device_id,category,value)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioCaptureEffectsManagerWithMode(This,device_id,category,mode,value) (This)->lpVtbl->CreateAudioCaptureEffectsManagerWithMode(This,device_id,category,mode,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_QueryInterface(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_AddRef(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_Release(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_GetIids(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_GetTrustLevel(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAudioEffectsManagerStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioRenderEffectsManager(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics* This,HSTRING device_id,__x_ABI_CWindows_CMedia_CRender_CAudioRenderCategory category,__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager **value) {
    return This->lpVtbl->CreateAudioRenderEffectsManager(This,device_id,category,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioRenderEffectsManagerWithMode(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics* This,HSTRING device_id,__x_ABI_CWindows_CMedia_CRender_CAudioRenderCategory category,__x_ABI_CWindows_CMedia_CAudioProcessing mode,__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager **value) {
    return This->lpVtbl->CreateAudioRenderEffectsManagerWithMode(This,device_id,category,mode,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioCaptureEffectsManager(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics* This,HSTRING device_id,__x_ABI_CWindows_CMedia_CCapture_CMediaCategory category,__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager **value) {
    return This->lpVtbl->CreateAudioCaptureEffectsManager(This,device_id,category,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioCaptureEffectsManagerWithMode(__x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics* This,HSTRING device_id,__x_ABI_CWindows_CMedia_CCapture_CMediaCategory category,__x_ABI_CWindows_CMedia_CAudioProcessing mode,__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager **value) {
    return This->lpVtbl->CreateAudioCaptureEffectsManagerWithMode(This,device_id,category,mode,value);
}
#endif
#ifdef WIDL_using_Windows_Media_Effects
#define IID_IAudioEffectsManagerStatics IID___x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics
#define IAudioEffectsManagerStaticsVtbl __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStaticsVtbl
#define IAudioEffectsManagerStatics __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics
#define IAudioEffectsManagerStatics_QueryInterface __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_QueryInterface
#define IAudioEffectsManagerStatics_AddRef __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_AddRef
#define IAudioEffectsManagerStatics_Release __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_Release
#define IAudioEffectsManagerStatics_GetIids __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_GetIids
#define IAudioEffectsManagerStatics_GetRuntimeClassName __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_GetRuntimeClassName
#define IAudioEffectsManagerStatics_GetTrustLevel __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_GetTrustLevel
#define IAudioEffectsManagerStatics_CreateAudioRenderEffectsManager __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioRenderEffectsManager
#define IAudioEffectsManagerStatics_CreateAudioRenderEffectsManagerWithMode __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioRenderEffectsManagerWithMode
#define IAudioEffectsManagerStatics_CreateAudioCaptureEffectsManager __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioCaptureEffectsManager
#define IAudioEffectsManagerStatics_CreateAudioCaptureEffectsManagerWithMode __x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_CreateAudioCaptureEffectsManagerWithMode
#endif /* WIDL_using_Windows_Media_Effects */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CEffects_CIAudioEffectsManagerStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAudioRenderEffectsManager interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager, 0x4dc98966, 0x8751, 0x42b2, 0xbf,0xcb, 0x39,0xca,0x78,0x64,0xbd,0x47);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                MIDL_INTERFACE("4dc98966-8751-42b2-bfcb-39ca7864bd47")
                IAudioRenderEffectsManager : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_AudioRenderEffectsChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::Effects::AudioRenderEffectsManager*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_AudioRenderEffectsChanged(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetAudioRenderEffects(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::Effects::AudioEffect* > **effects) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager, 0x4dc98966, 0x8751, 0x42b2, 0xbf,0xcb, 0x39,0xca,0x78,0x64,0xbd,0x47)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager *This,
        TrustLevel *trustLevel);

    /*** IAudioRenderEffectsManager methods ***/
    HRESULT (STDMETHODCALLTYPE *add_AudioRenderEffectsChanged)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager *This,
        __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_AudioRenderEffectsChanged)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *GetAudioRenderEffects)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager *This,
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect **effects);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManagerVtbl;

interface __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager {
    CONST_VTBL __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAudioRenderEffectsManager methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_add_AudioRenderEffectsChanged(This,handler,token) (This)->lpVtbl->add_AudioRenderEffectsChanged(This,handler,token)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_remove_AudioRenderEffectsChanged(This,token) (This)->lpVtbl->remove_AudioRenderEffectsChanged(This,token)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetAudioRenderEffects(This,effects) (This)->lpVtbl->GetAudioRenderEffects(This,effects)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_QueryInterface(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_AddRef(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_Release(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetIids(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetTrustLevel(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAudioRenderEffectsManager methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_add_AudioRenderEffectsChanged(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager* This,__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_AudioRenderEffectsChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_remove_AudioRenderEffectsChanged(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_AudioRenderEffectsChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetAudioRenderEffects(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager* This,__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect **effects) {
    return This->lpVtbl->GetAudioRenderEffects(This,effects);
}
#endif
#ifdef WIDL_using_Windows_Media_Effects
#define IID_IAudioRenderEffectsManager IID___x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager
#define IAudioRenderEffectsManagerVtbl __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManagerVtbl
#define IAudioRenderEffectsManager __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager
#define IAudioRenderEffectsManager_QueryInterface __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_QueryInterface
#define IAudioRenderEffectsManager_AddRef __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_AddRef
#define IAudioRenderEffectsManager_Release __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_Release
#define IAudioRenderEffectsManager_GetIids __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetIids
#define IAudioRenderEffectsManager_GetRuntimeClassName __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetRuntimeClassName
#define IAudioRenderEffectsManager_GetTrustLevel __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetTrustLevel
#define IAudioRenderEffectsManager_add_AudioRenderEffectsChanged __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_add_AudioRenderEffectsChanged
#define IAudioRenderEffectsManager_remove_AudioRenderEffectsChanged __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_remove_AudioRenderEffectsChanged
#define IAudioRenderEffectsManager_GetAudioRenderEffects __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_GetAudioRenderEffects
#endif /* WIDL_using_Windows_Media_Effects */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAudioRenderEffectsManager2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2, 0xa844cd09, 0x5ecc, 0x44b3, 0xbb,0x4e, 0x1d,0xb0,0x72,0x87,0x13,0x9c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace Effects {
                MIDL_INTERFACE("a844cd09-5ecc-44b3-bb4e-1db07287139c")
                IAudioRenderEffectsManager2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_EffectsProviderThumbnail(
                        ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_EffectsProviderSettingsLabel(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE ShowSettingsUI(
                        ) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2, 0xa844cd09, 0x5ecc, 0x44b3, 0xbb,0x4e, 0x1d,0xb0,0x72,0x87,0x13,0x9c)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 *This,
        TrustLevel *trustLevel);

    /*** IAudioRenderEffectsManager2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_EffectsProviderThumbnail)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType **value);

    HRESULT (STDMETHODCALLTYPE *get_EffectsProviderSettingsLabel)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *ShowSettingsUI)(
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 *This);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2Vtbl;

interface __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2 {
    CONST_VTBL __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAudioRenderEffectsManager2 methods ***/
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_get_EffectsProviderThumbnail(This,value) (This)->lpVtbl->get_EffectsProviderThumbnail(This,value)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_get_EffectsProviderSettingsLabel(This,value) (This)->lpVtbl->get_EffectsProviderSettingsLabel(This,value)
#define __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_ShowSettingsUI(This) (This)->lpVtbl->ShowSettingsUI(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_QueryInterface(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_AddRef(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_Release(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_GetIids(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_GetTrustLevel(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAudioRenderEffectsManager2 methods ***/
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_get_EffectsProviderThumbnail(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType **value) {
    return This->lpVtbl->get_EffectsProviderThumbnail(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_get_EffectsProviderSettingsLabel(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2* This,HSTRING *value) {
    return This->lpVtbl->get_EffectsProviderSettingsLabel(This,value);
}
static inline HRESULT __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_ShowSettingsUI(__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2* This) {
    return This->lpVtbl->ShowSettingsUI(This);
}
#endif
#ifdef WIDL_using_Windows_Media_Effects
#define IID_IAudioRenderEffectsManager2 IID___x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2
#define IAudioRenderEffectsManager2Vtbl __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2Vtbl
#define IAudioRenderEffectsManager2 __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2
#define IAudioRenderEffectsManager2_QueryInterface __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_QueryInterface
#define IAudioRenderEffectsManager2_AddRef __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_AddRef
#define IAudioRenderEffectsManager2_Release __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_Release
#define IAudioRenderEffectsManager2_GetIids __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_GetIids
#define IAudioRenderEffectsManager2_GetRuntimeClassName __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_GetRuntimeClassName
#define IAudioRenderEffectsManager2_GetTrustLevel __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_GetTrustLevel
#define IAudioRenderEffectsManager2_get_EffectsProviderThumbnail __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_get_EffectsProviderThumbnail
#define IAudioRenderEffectsManager2_get_EffectsProviderSettingsLabel __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_get_EffectsProviderSettingsLabel
#define IAudioRenderEffectsManager2_ShowSettingsUI __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_ShowSettingsUI
#endif /* WIDL_using_Windows_Media_Effects */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.Effects.AudioCaptureEffectsManager
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_Effects_AudioCaptureEffectsManager_DEFINED
#define RUNTIMECLASS_Windows_Media_Effects_AudioCaptureEffectsManager_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_Effects_AudioCaptureEffectsManager[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','E','f','f','e','c','t','s','.','A','u','d','i','o','C','a','p','t','u','r','e','E','f','f','e','c','t','s','M','a','n','a','g','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Effects_AudioCaptureEffectsManager[] = L"Windows.Media.Effects.AudioCaptureEffectsManager";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Effects_AudioCaptureEffectsManager[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','E','f','f','e','c','t','s','.','A','u','d','i','o','C','a','p','t','u','r','e','E','f','f','e','c','t','s','M','a','n','a','g','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_Effects_AudioCaptureEffectsManager_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.Effects.AudioEffect
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_Effects_AudioEffect_DEFINED
#define RUNTIMECLASS_Windows_Media_Effects_AudioEffect_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_Effects_AudioEffect[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','E','f','f','e','c','t','s','.','A','u','d','i','o','E','f','f','e','c','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Effects_AudioEffect[] = L"Windows.Media.Effects.AudioEffect";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Effects_AudioEffect[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','E','f','f','e','c','t','s','.','A','u','d','i','o','E','f','f','e','c','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_Effects_AudioEffect_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.Effects.AudioEffectsManager
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_Effects_AudioEffectsManager_DEFINED
#define RUNTIMECLASS_Windows_Media_Effects_AudioEffectsManager_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_Effects_AudioEffectsManager[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','E','f','f','e','c','t','s','.','A','u','d','i','o','E','f','f','e','c','t','s','M','a','n','a','g','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Effects_AudioEffectsManager[] = L"Windows.Media.Effects.AudioEffectsManager";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Effects_AudioEffectsManager[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','E','f','f','e','c','t','s','.','A','u','d','i','o','E','f','f','e','c','t','s','M','a','n','a','g','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_Effects_AudioEffectsManager_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.Effects.AudioRenderEffectsManager
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_Effects_AudioRenderEffectsManager_DEFINED
#define RUNTIMECLASS_Windows_Media_Effects_AudioRenderEffectsManager_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_Effects_AudioRenderEffectsManager[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','E','f','f','e','c','t','s','.','A','u','d','i','o','R','e','n','d','e','r','E','f','f','e','c','t','s','M','a','n','a','g','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Effects_AudioRenderEffectsManager[] = L"Windows.Media.Effects.AudioRenderEffectsManager";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_Effects_AudioRenderEffectsManager[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','E','f','f','e','c','t','s','.','A','u','d','i','o','R','e','n','d','e','r','E','f','f','e','c','t','s','M','a','n','a','g','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_Effects_AudioRenderEffectsManager_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Media::Effects::AudioEffect* > interface
 */
#ifndef ____FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CMedia__CEffects__CAudioEffect, 0x07af9afd, 0x25b8, 0x579d, 0xbe,0x7e, 0x8a,0xcc,0x03,0x41,0x8d,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("07af9afd-25b8-579d-be7e-8acc03418d0b")
                IIterable<ABI::Windows::Media::Effects::AudioEffect* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::Effects::AudioEffect*, ABI::Windows::Media::Effects::IAudioEffect* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CMedia__CEffects__CAudioEffect, 0x07af9afd, 0x25b8, 0x579d, 0xbe,0x7e, 0x8a,0xcc,0x03,0x41,0x8d,0x0b)
#endif
#else
typedef struct __FIIterable_1_Windows__CMedia__CEffects__CAudioEffectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Media::Effects::AudioEffect* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect *This,
        __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect **value);

    END_INTERFACE
} __FIIterable_1_Windows__CMedia__CEffects__CAudioEffectVtbl;

interface __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect {
    CONST_VTBL __FIIterable_1_Windows__CMedia__CEffects__CAudioEffectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Media::Effects::AudioEffect* > methods ***/
#define __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_QueryInterface(__FIIterable_1_Windows__CMedia__CEffects__CAudioEffect* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_AddRef(__FIIterable_1_Windows__CMedia__CEffects__CAudioEffect* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_Release(__FIIterable_1_Windows__CMedia__CEffects__CAudioEffect* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_GetIids(__FIIterable_1_Windows__CMedia__CEffects__CAudioEffect* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_GetRuntimeClassName(__FIIterable_1_Windows__CMedia__CEffects__CAudioEffect* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_GetTrustLevel(__FIIterable_1_Windows__CMedia__CEffects__CAudioEffect* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Media::Effects::AudioEffect* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_First(__FIIterable_1_Windows__CMedia__CEffects__CAudioEffect* This,__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_AudioEffect IID___FIIterable_1_Windows__CMedia__CEffects__CAudioEffect
#define IIterable_AudioEffectVtbl __FIIterable_1_Windows__CMedia__CEffects__CAudioEffectVtbl
#define IIterable_AudioEffect __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect
#define IIterable_AudioEffect_QueryInterface __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_QueryInterface
#define IIterable_AudioEffect_AddRef __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_AddRef
#define IIterable_AudioEffect_Release __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_Release
#define IIterable_AudioEffect_GetIids __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_GetIids
#define IIterable_AudioEffect_GetRuntimeClassName __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_GetRuntimeClassName
#define IIterable_AudioEffect_GetTrustLevel __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_GetTrustLevel
#define IIterable_AudioEffect_First __FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CMedia__CEffects__CAudioEffect_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Media::Effects::AudioEffect* > interface
 */
#ifndef ____FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CMedia__CEffects__CAudioEffect, 0x673ce717, 0xa3cf, 0x5d68, 0xa8,0x0b, 0x5e,0xd3,0xe7,0xb9,0x3f,0xed);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("673ce717-a3cf-5d68-a80b-5ed3e7b93fed")
                IIterator<ABI::Windows::Media::Effects::AudioEffect* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::Effects::AudioEffect*, ABI::Windows::Media::Effects::IAudioEffect* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect, 0x673ce717, 0xa3cf, 0x5d68, 0xa8,0x0b, 0x5e,0xd3,0xe7,0xb9,0x3f,0xed)
#endif
#else
typedef struct __FIIterator_1_Windows__CMedia__CEffects__CAudioEffectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Media::Effects::AudioEffect* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect *This,
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect *This,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CMedia__CEffects__CAudioEffectVtbl;

interface __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect {
    CONST_VTBL __FIIterator_1_Windows__CMedia__CEffects__CAudioEffectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Media::Effects::AudioEffect* > methods ***/
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_QueryInterface(__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_AddRef(__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_Release(__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetIids(__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetRuntimeClassName(__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetTrustLevel(__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Media::Effects::AudioEffect* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_get_Current(__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect* This,__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_get_HasCurrent(__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_MoveNext(__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetMany(__FIIterator_1_Windows__CMedia__CEffects__CAudioEffect* This,UINT32 items_size,__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_AudioEffect IID___FIIterator_1_Windows__CMedia__CEffects__CAudioEffect
#define IIterator_AudioEffectVtbl __FIIterator_1_Windows__CMedia__CEffects__CAudioEffectVtbl
#define IIterator_AudioEffect __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect
#define IIterator_AudioEffect_QueryInterface __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_QueryInterface
#define IIterator_AudioEffect_AddRef __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_AddRef
#define IIterator_AudioEffect_Release __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_Release
#define IIterator_AudioEffect_GetIids __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetIids
#define IIterator_AudioEffect_GetRuntimeClassName __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetRuntimeClassName
#define IIterator_AudioEffect_GetTrustLevel __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetTrustLevel
#define IIterator_AudioEffect_get_Current __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_get_Current
#define IIterator_AudioEffect_get_HasCurrent __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_get_HasCurrent
#define IIterator_AudioEffect_MoveNext __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_MoveNext
#define IIterator_AudioEffect_GetMany __FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CMedia__CEffects__CAudioEffect_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Media::Effects::AudioEffect* > interface
 */
#ifndef ____FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect, 0xcdcbc9e7, 0x53d8, 0x5e66, 0x9e,0x45, 0x31,0xd5,0xa2,0x3f,0xd0,0x1d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("cdcbc9e7-53d8-5e66-9e45-31d5a23fd01d")
                IVectorView<ABI::Windows::Media::Effects::AudioEffect* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::Effects::AudioEffect*, ABI::Windows::Media::Effects::IAudioEffect* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect, 0xcdcbc9e7, 0x53d8, 0x5e66, 0x9e,0x45, 0x31,0xd5,0xa2,0x3f,0xd0,0x1d)
#endif
#else
typedef struct __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Media::Effects::AudioEffect* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect *This,
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CEffects_CIAudioEffect **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffectVtbl;

interface __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect {
    CONST_VTBL __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Media::Effects::AudioEffect* > methods ***/
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_QueryInterface(__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_AddRef(__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_Release(__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetIids(__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetRuntimeClassName(__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetTrustLevel(__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Media::Effects::AudioEffect* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetAt(__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect* This,UINT32 index,__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_get_Size(__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_IndexOf(__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect* This,__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetMany(__FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CMedia_CEffects_CIAudioEffect **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_AudioEffect IID___FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect
#define IVectorView_AudioEffectVtbl __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffectVtbl
#define IVectorView_AudioEffect __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect
#define IVectorView_AudioEffect_QueryInterface __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_QueryInterface
#define IVectorView_AudioEffect_AddRef __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_AddRef
#define IVectorView_AudioEffect_Release __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_Release
#define IVectorView_AudioEffect_GetIids __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetIids
#define IVectorView_AudioEffect_GetRuntimeClassName __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetRuntimeClassName
#define IVectorView_AudioEffect_GetTrustLevel __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetTrustLevel
#define IVectorView_AudioEffect_GetAt __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetAt
#define IVectorView_AudioEffect_get_Size __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_get_Size
#define IVectorView_AudioEffect_IndexOf __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_IndexOf
#define IVectorView_AudioEffect_GetMany __FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CMedia__CEffects__CAudioEffect_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::Effects::AudioCaptureEffectsManager*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable, 0xa1c5e803, 0xa275, 0x5bb1, 0x9d,0x44, 0x2a,0xc8,0xae,0x9f,0xfb,0x89);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("a1c5e803-a275-5bb1-9d44-2ac8ae9ffb89")
            ITypedEventHandler<ABI::Windows::Media::Effects::AudioCaptureEffectsManager*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::Effects::AudioCaptureEffectsManager*, ABI::Windows::Media::Effects::IAudioCaptureEffectsManager* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable, 0xa1c5e803, 0xa275, 0x5bb1, 0x9d,0x44, 0x2a,0xc8,0xae,0x9f,0xfb,0x89)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::Media::Effects::AudioCaptureEffectsManager*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable *This,
        __x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::Effects::AudioCaptureEffectsManager*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_Release(__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::Effects::AudioCaptureEffectsManager*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable* This,__x_ABI_CWindows_CMedia_CEffects_CIAudioCaptureEffectsManager *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_AudioCaptureEffectsManager_IInspectable IID___FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable
#define ITypedEventHandler_AudioCaptureEffectsManager_IInspectableVtbl __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectableVtbl
#define ITypedEventHandler_AudioCaptureEffectsManager_IInspectable __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable
#define ITypedEventHandler_AudioCaptureEffectsManager_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_QueryInterface
#define ITypedEventHandler_AudioCaptureEffectsManager_IInspectable_AddRef __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_AddRef
#define ITypedEventHandler_AudioCaptureEffectsManager_IInspectable_Release __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_Release
#define ITypedEventHandler_AudioCaptureEffectsManager_IInspectable_Invoke __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioCaptureEffectsManager_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::Effects::AudioRenderEffectsManager*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable, 0x4be29c7e, 0x449c, 0x576e, 0xa7,0xb8, 0x3a,0x40,0xf2,0xf0,0x1d,0xc8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("4be29c7e-449c-576e-a7b8-3a40f2f01dc8")
            ITypedEventHandler<ABI::Windows::Media::Effects::AudioRenderEffectsManager*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::Effects::AudioRenderEffectsManager*, ABI::Windows::Media::Effects::IAudioRenderEffectsManager* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable, 0x4be29c7e, 0x449c, 0x576e, 0xa7,0xb8, 0x3a,0x40,0xf2,0xf0,0x1d,0xc8)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::Media::Effects::AudioRenderEffectsManager*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable *This,
        __x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::Effects::AudioRenderEffectsManager*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_Release(__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::Effects::AudioRenderEffectsManager*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable* This,__x_ABI_CWindows_CMedia_CEffects_CIAudioRenderEffectsManager *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_AudioRenderEffectsManager_IInspectable IID___FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable
#define ITypedEventHandler_AudioRenderEffectsManager_IInspectableVtbl __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectableVtbl
#define ITypedEventHandler_AudioRenderEffectsManager_IInspectable __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable
#define ITypedEventHandler_AudioRenderEffectsManager_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_QueryInterface
#define ITypedEventHandler_AudioRenderEffectsManager_IInspectable_AddRef __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_AddRef
#define ITypedEventHandler_AudioRenderEffectsManager_IInspectable_Release __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_Release
#define ITypedEventHandler_AudioRenderEffectsManager_IInspectable_Invoke __FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CEffects__CAudioRenderEffectsManager_IInspectable_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_media_effects_h__ */
