/*** Autogenerated by WIDL 10.12 from include/wmsdkidl.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wmsdkidl_h__
#define __wmsdkidl_h__

/* Forward declarations */

#ifndef __IWMStatusCallback_FWD_DEFINED__
#define __IWMStatusCallback_FWD_DEFINED__
typedef interface IWMStatusCallback IWMStatusCallback;
#ifdef __cplusplus
interface IWMStatusCallback;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderCallback_FWD_DEFINED__
#define __IWMReaderCallback_FWD_DEFINED__
typedef interface IWMReaderCallback IWMReaderCallback;
#ifdef __cplusplus
interface IWMReaderCallback;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderCallbackAdvanced_FWD_DEFINED__
#define __IWMReaderCallbackAdvanced_FWD_DEFINED__
typedef interface IWMReaderCallbackAdvanced IWMReaderCallbackAdvanced;
#ifdef __cplusplus
interface IWMReaderCallbackAdvanced;
#endif /* __cplusplus */
#endif

#ifndef __IWMStreamList_FWD_DEFINED__
#define __IWMStreamList_FWD_DEFINED__
typedef interface IWMStreamList IWMStreamList;
#ifdef __cplusplus
interface IWMStreamList;
#endif /* __cplusplus */
#endif

#ifndef __IWMMutualExclusion_FWD_DEFINED__
#define __IWMMutualExclusion_FWD_DEFINED__
typedef interface IWMMutualExclusion IWMMutualExclusion;
#ifdef __cplusplus
interface IWMMutualExclusion;
#endif /* __cplusplus */
#endif

#ifndef __IWMBandwidthSharing_FWD_DEFINED__
#define __IWMBandwidthSharing_FWD_DEFINED__
typedef interface IWMBandwidthSharing IWMBandwidthSharing;
#ifdef __cplusplus
interface IWMBandwidthSharing;
#endif /* __cplusplus */
#endif

#ifndef __IWMStreamPrioritization_FWD_DEFINED__
#define __IWMStreamPrioritization_FWD_DEFINED__
typedef interface IWMStreamPrioritization IWMStreamPrioritization;
#ifdef __cplusplus
interface IWMStreamPrioritization;
#endif /* __cplusplus */
#endif

#ifndef __IWMStreamConfig_FWD_DEFINED__
#define __IWMStreamConfig_FWD_DEFINED__
typedef interface IWMStreamConfig IWMStreamConfig;
#ifdef __cplusplus
interface IWMStreamConfig;
#endif /* __cplusplus */
#endif

#ifndef __IWMProfile_FWD_DEFINED__
#define __IWMProfile_FWD_DEFINED__
typedef interface IWMProfile IWMProfile;
#ifdef __cplusplus
interface IWMProfile;
#endif /* __cplusplus */
#endif

#ifndef __IWMProfile2_FWD_DEFINED__
#define __IWMProfile2_FWD_DEFINED__
typedef interface IWMProfile2 IWMProfile2;
#ifdef __cplusplus
interface IWMProfile2;
#endif /* __cplusplus */
#endif

#ifndef __IWMProfile3_FWD_DEFINED__
#define __IWMProfile3_FWD_DEFINED__
typedef interface IWMProfile3 IWMProfile3;
#ifdef __cplusplus
interface IWMProfile3;
#endif /* __cplusplus */
#endif

#ifndef __IWMProfileManager_FWD_DEFINED__
#define __IWMProfileManager_FWD_DEFINED__
typedef interface IWMProfileManager IWMProfileManager;
#ifdef __cplusplus
interface IWMProfileManager;
#endif /* __cplusplus */
#endif

#ifndef __IWMProfileManager2_FWD_DEFINED__
#define __IWMProfileManager2_FWD_DEFINED__
typedef interface IWMProfileManager2 IWMProfileManager2;
#ifdef __cplusplus
interface IWMProfileManager2;
#endif /* __cplusplus */
#endif

#ifndef __IWMCodecInfo_FWD_DEFINED__
#define __IWMCodecInfo_FWD_DEFINED__
typedef interface IWMCodecInfo IWMCodecInfo;
#ifdef __cplusplus
interface IWMCodecInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWMCodecInfo2_FWD_DEFINED__
#define __IWMCodecInfo2_FWD_DEFINED__
typedef interface IWMCodecInfo2 IWMCodecInfo2;
#ifdef __cplusplus
interface IWMCodecInfo2;
#endif /* __cplusplus */
#endif

#ifndef __IWMCodecInfo3_FWD_DEFINED__
#define __IWMCodecInfo3_FWD_DEFINED__
typedef interface IWMCodecInfo3 IWMCodecInfo3;
#ifdef __cplusplus
interface IWMCodecInfo3;
#endif /* __cplusplus */
#endif

#ifndef __IWMMediaProps_FWD_DEFINED__
#define __IWMMediaProps_FWD_DEFINED__
typedef interface IWMMediaProps IWMMediaProps;
#ifdef __cplusplus
interface IWMMediaProps;
#endif /* __cplusplus */
#endif

#ifndef __IWMOutputMediaProps_FWD_DEFINED__
#define __IWMOutputMediaProps_FWD_DEFINED__
typedef interface IWMOutputMediaProps IWMOutputMediaProps;
#ifdef __cplusplus
interface IWMOutputMediaProps;
#endif /* __cplusplus */
#endif

#ifndef __IWMMetadataEditor_FWD_DEFINED__
#define __IWMMetadataEditor_FWD_DEFINED__
typedef interface IWMMetadataEditor IWMMetadataEditor;
#ifdef __cplusplus
interface IWMMetadataEditor;
#endif /* __cplusplus */
#endif

#ifndef __IWMReader_FWD_DEFINED__
#define __IWMReader_FWD_DEFINED__
typedef interface IWMReader IWMReader;
#ifdef __cplusplus
interface IWMReader;
#endif /* __cplusplus */
#endif

#ifndef __IWMPlayerHook_FWD_DEFINED__
#define __IWMPlayerHook_FWD_DEFINED__
typedef interface IWMPlayerHook IWMPlayerHook;
#ifdef __cplusplus
interface IWMPlayerHook;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderAdvanced_FWD_DEFINED__
#define __IWMReaderAdvanced_FWD_DEFINED__
typedef interface IWMReaderAdvanced IWMReaderAdvanced;
#ifdef __cplusplus
interface IWMReaderAdvanced;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderAdvanced2_FWD_DEFINED__
#define __IWMReaderAdvanced2_FWD_DEFINED__
typedef interface IWMReaderAdvanced2 IWMReaderAdvanced2;
#ifdef __cplusplus
interface IWMReaderAdvanced2;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderAdvanced3_FWD_DEFINED__
#define __IWMReaderAdvanced3_FWD_DEFINED__
typedef interface IWMReaderAdvanced3 IWMReaderAdvanced3;
#ifdef __cplusplus
interface IWMReaderAdvanced3;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderAdvanced4_FWD_DEFINED__
#define __IWMReaderAdvanced4_FWD_DEFINED__
typedef interface IWMReaderAdvanced4 IWMReaderAdvanced4;
#ifdef __cplusplus
interface IWMReaderAdvanced4;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderAdvanced5_FWD_DEFINED__
#define __IWMReaderAdvanced5_FWD_DEFINED__
typedef interface IWMReaderAdvanced5 IWMReaderAdvanced5;
#ifdef __cplusplus
interface IWMReaderAdvanced5;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderAdvanced6_FWD_DEFINED__
#define __IWMReaderAdvanced6_FWD_DEFINED__
typedef interface IWMReaderAdvanced6 IWMReaderAdvanced6;
#ifdef __cplusplus
interface IWMReaderAdvanced6;
#endif /* __cplusplus */
#endif

#ifndef __IWMSyncReader_FWD_DEFINED__
#define __IWMSyncReader_FWD_DEFINED__
typedef interface IWMSyncReader IWMSyncReader;
#ifdef __cplusplus
interface IWMSyncReader;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderAllocatorEx_FWD_DEFINED__
#define __IWMReaderAllocatorEx_FWD_DEFINED__
typedef interface IWMReaderAllocatorEx IWMReaderAllocatorEx;
#ifdef __cplusplus
interface IWMReaderAllocatorEx;
#endif /* __cplusplus */
#endif

#ifndef __IWMSyncReader2_FWD_DEFINED__
#define __IWMSyncReader2_FWD_DEFINED__
typedef interface IWMSyncReader2 IWMSyncReader2;
#ifdef __cplusplus
interface IWMSyncReader2;
#endif /* __cplusplus */
#endif

#ifndef __IWMInputMediaProps_FWD_DEFINED__
#define __IWMInputMediaProps_FWD_DEFINED__
typedef interface IWMInputMediaProps IWMInputMediaProps;
#ifdef __cplusplus
interface IWMInputMediaProps;
#endif /* __cplusplus */
#endif

#ifndef __IWMWriterSink_FWD_DEFINED__
#define __IWMWriterSink_FWD_DEFINED__
typedef interface IWMWriterSink IWMWriterSink;
#ifdef __cplusplus
interface IWMWriterSink;
#endif /* __cplusplus */
#endif

#ifndef __IWMWriter_FWD_DEFINED__
#define __IWMWriter_FWD_DEFINED__
typedef interface IWMWriter IWMWriter;
#ifdef __cplusplus
interface IWMWriter;
#endif /* __cplusplus */
#endif

#ifndef __IWMWriterAdvanced_FWD_DEFINED__
#define __IWMWriterAdvanced_FWD_DEFINED__
typedef interface IWMWriterAdvanced IWMWriterAdvanced;
#ifdef __cplusplus
interface IWMWriterAdvanced;
#endif /* __cplusplus */
#endif

#ifndef __IWMWriterAdvanced2_FWD_DEFINED__
#define __IWMWriterAdvanced2_FWD_DEFINED__
typedef interface IWMWriterAdvanced2 IWMWriterAdvanced2;
#ifdef __cplusplus
interface IWMWriterAdvanced2;
#endif /* __cplusplus */
#endif

#ifndef __IWMWriterAdvanced3_FWD_DEFINED__
#define __IWMWriterAdvanced3_FWD_DEFINED__
typedef interface IWMWriterAdvanced3 IWMWriterAdvanced3;
#ifdef __cplusplus
interface IWMWriterAdvanced3;
#endif /* __cplusplus */
#endif

#ifndef __IWMHeaderInfo_FWD_DEFINED__
#define __IWMHeaderInfo_FWD_DEFINED__
typedef interface IWMHeaderInfo IWMHeaderInfo;
#ifdef __cplusplus
interface IWMHeaderInfo;
#endif /* __cplusplus */
#endif

#ifndef __IWMHeaderInfo2_FWD_DEFINED__
#define __IWMHeaderInfo2_FWD_DEFINED__
typedef interface IWMHeaderInfo2 IWMHeaderInfo2;
#ifdef __cplusplus
interface IWMHeaderInfo2;
#endif /* __cplusplus */
#endif

#ifndef __IWMHeaderInfo3_FWD_DEFINED__
#define __IWMHeaderInfo3_FWD_DEFINED__
typedef interface IWMHeaderInfo3 IWMHeaderInfo3;
#ifdef __cplusplus
interface IWMHeaderInfo3;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderNetworkConfig_FWD_DEFINED__
#define __IWMReaderNetworkConfig_FWD_DEFINED__
typedef interface IWMReaderNetworkConfig IWMReaderNetworkConfig;
#ifdef __cplusplus
interface IWMReaderNetworkConfig;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderNetworkConfig2_FWD_DEFINED__
#define __IWMReaderNetworkConfig2_FWD_DEFINED__
typedef interface IWMReaderNetworkConfig2 IWMReaderNetworkConfig2;
#ifdef __cplusplus
interface IWMReaderNetworkConfig2;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderStreamClock_FWD_DEFINED__
#define __IWMReaderStreamClock_FWD_DEFINED__
typedef interface IWMReaderStreamClock IWMReaderStreamClock;
#ifdef __cplusplus
interface IWMReaderStreamClock;
#endif /* __cplusplus */
#endif

#ifndef __IWMPacketSize_FWD_DEFINED__
#define __IWMPacketSize_FWD_DEFINED__
typedef interface IWMPacketSize IWMPacketSize;
#ifdef __cplusplus
interface IWMPacketSize;
#endif /* __cplusplus */
#endif

#ifndef __IWMPacketSize2_FWD_DEFINED__
#define __IWMPacketSize2_FWD_DEFINED__
typedef interface IWMPacketSize2 IWMPacketSize2;
#ifdef __cplusplus
interface IWMPacketSize2;
#endif /* __cplusplus */
#endif

#ifndef __IWMDRMReader_FWD_DEFINED__
#define __IWMDRMReader_FWD_DEFINED__
typedef interface IWMDRMReader IWMDRMReader;
#ifdef __cplusplus
interface IWMDRMReader;
#endif /* __cplusplus */
#endif

#ifndef __IWMDRMReader2_FWD_DEFINED__
#define __IWMDRMReader2_FWD_DEFINED__
typedef interface IWMDRMReader2 IWMDRMReader2;
#ifdef __cplusplus
interface IWMDRMReader2;
#endif /* __cplusplus */
#endif

#ifndef __IWMDRMReader3_FWD_DEFINED__
#define __IWMDRMReader3_FWD_DEFINED__
typedef interface IWMDRMReader3 IWMDRMReader3;
#ifdef __cplusplus
interface IWMDRMReader3;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderAccelerator_FWD_DEFINED__
#define __IWMReaderAccelerator_FWD_DEFINED__
typedef interface IWMReaderAccelerator IWMReaderAccelerator;
#ifdef __cplusplus
interface IWMReaderAccelerator;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderTimecode_FWD_DEFINED__
#define __IWMReaderTimecode_FWD_DEFINED__
typedef interface IWMReaderTimecode IWMReaderTimecode;
#ifdef __cplusplus
interface IWMReaderTimecode;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderTypeNegotiation_FWD_DEFINED__
#define __IWMReaderTypeNegotiation_FWD_DEFINED__
typedef interface IWMReaderTypeNegotiation IWMReaderTypeNegotiation;
#ifdef __cplusplus
interface IWMReaderTypeNegotiation;
#endif /* __cplusplus */
#endif

#ifndef __IWMLanguageList_FWD_DEFINED__
#define __IWMLanguageList_FWD_DEFINED__
typedef interface IWMLanguageList IWMLanguageList;
#ifdef __cplusplus
interface IWMLanguageList;
#endif /* __cplusplus */
#endif

#ifndef __IWMReaderPlaylistBurn_FWD_DEFINED__
#define __IWMReaderPlaylistBurn_FWD_DEFINED__
typedef interface IWMReaderPlaylistBurn IWMReaderPlaylistBurn;
#ifdef __cplusplus
interface IWMReaderPlaylistBurn;
#endif /* __cplusplus */
#endif

#ifndef __IWMPropertyVault_FWD_DEFINED__
#define __IWMPropertyVault_FWD_DEFINED__
typedef interface IWMPropertyVault IWMPropertyVault;
#ifdef __cplusplus
interface IWMPropertyVault;
#endif /* __cplusplus */
#endif

#ifndef __IWMWriterPreprocess_FWD_DEFINED__
#define __IWMWriterPreprocess_FWD_DEFINED__
typedef interface IWMWriterPreprocess IWMWriterPreprocess;
#ifdef __cplusplus
interface IWMWriterPreprocess;
#endif /* __cplusplus */
#endif

#ifndef __IWMLicenseBackup_FWD_DEFINED__
#define __IWMLicenseBackup_FWD_DEFINED__
typedef interface IWMLicenseBackup IWMLicenseBackup;
#ifdef __cplusplus
interface IWMLicenseBackup;
#endif /* __cplusplus */
#endif

#ifndef __IWMLicenseRestore_FWD_DEFINED__
#define __IWMLicenseRestore_FWD_DEFINED__
typedef interface IWMLicenseRestore IWMLicenseRestore;
#ifdef __cplusplus
interface IWMLicenseRestore;
#endif /* __cplusplus */
#endif

#ifndef __IWMIStreamProps_FWD_DEFINED__
#define __IWMIStreamProps_FWD_DEFINED__
typedef interface IWMIStreamProps IWMIStreamProps;
#ifdef __cplusplus
interface IWMIStreamProps;
#endif /* __cplusplus */
#endif

#ifndef __IWMCredentialCallback_FWD_DEFINED__
#define __IWMCredentialCallback_FWD_DEFINED__
typedef interface IWMCredentialCallback IWMCredentialCallback;
#ifdef __cplusplus
interface IWMCredentialCallback;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <wmsbuffer.h>
#include <drmexternals.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef struct _WMMediaType {
    GUID majortype;
    GUID subtype;
    WINBOOL bFixedSizeSamples;
    WINBOOL bTemporalCompression;
    ULONG lSampleSize;
    GUID formattype;
    IUnknown *pUnk;
    ULONG cbFormat;
    BYTE *pbFormat;
} WM_MEDIA_TYPE;
typedef struct _WMWriterStatistics {
    QWORD qwSampleCount;
    QWORD qwByteCount;
    QWORD qwDroppedSampleCount;
    QWORD qwDroppedByteCount;
    DWORD dwCurrentBitrate;
    DWORD dwAverageBitrate;
    DWORD dwExpectedBitrate;
    DWORD dwCurrentSampleRate;
    DWORD dwAverageSampleRate;
    DWORD dwExpectedSampleRate;
} WM_WRITER_STATISTICS;
typedef struct _WMWriterStatisticsEx {
    DWORD dwBitratePlusOverhead;
    DWORD dwCurrentSampleDropRateInQueue;
    DWORD dwCurrentSampleDropRateInCodec;
    DWORD dwCurrentSampleDropRateInMultiplexer;
    DWORD dwTotalSampleDropsInQueue;
    DWORD dwTotalSampleDropsInCodec;
    DWORD dwTotalSampleDropsInMultiplexer;
} WM_WRITER_STATISTICS_EX;
typedef struct _WMReaderStatistics {
    DWORD cbSize;
    DWORD dwBandwidth;
    DWORD cPacketsReceived;
    DWORD cPacketsRecovered;
    DWORD cPacketsLost;
    WORD wQuality;
} WM_READER_STATISTICS;
typedef struct _WMReaderClientInfo {
    DWORD cbSize;
    WCHAR *wszLang;
    WCHAR *wszBrowserUserAgent;
    WCHAR *wszBrowserWebPage;
    QWORD qwReserved;
    LPARAM *pReserved;
    WCHAR *wszHostExe;
    QWORD qwHostVersion;
    WCHAR *wszPlayerUserAgent;
} WM_READER_CLIENTINFO;
enum {
    WM_SF_CLEANPOINT = 0x1,
    WM_SF_DISCONTINUITY = 0x2,
    WM_SF_DATALOSS = 0x4
};
typedef enum WMT_ATTR_DATATYPE {
    WMT_TYPE_DWORD = 0,
    WMT_TYPE_STRING = 1,
    WMT_TYPE_BINARY = 2,
    WMT_TYPE_BOOL = 3,
    WMT_TYPE_QWORD = 4,
    WMT_TYPE_WORD = 5,
    WMT_TYPE_GUID = 6
} WMT_ATTR_DATATYPE;
typedef enum WMT_STATUS {
    WMT_ERROR = 0,
    WMT_OPENED = 1,
    WMT_BUFFERING_START = 2,
    WMT_BUFFERING_STOP = 3,
    WMT_EOF = 4,
    WMT_END_OF_FILE = 4,
    WMT_END_OF_SEGMENT = 5,
    WMT_END_OF_STREAMING = 6,
    WMT_LOCATING = 7,
    WMT_CONNECTING = 8,
    WMT_NO_RIGHTS = 9,
    WMT_MISSING_CODEC = 10,
    WMT_STARTED = 11,
    WMT_STOPPED = 12,
    WMT_CLOSED = 13,
    WMT_STRIDING = 14,
    WMT_TIMER = 15,
    WMT_INDEX_PROGRESS = 16,
    WMT_SAVEAS_START = 17,
    WMT_SAVEAS_STOP = 18,
    WMT_NEW_SOURCEFLAGS = 19,
    WMT_NEW_METADATA = 20,
    WMT_BACKUPRESTORE_BEGIN = 21,
    WMT_SOURCE_SWITCH = 22,
    WMT_ACQUIRE_LICENSE = 23,
    WMT_INDIVIDUALIZE = 24,
    WMT_NEEDS_INDIVIDUALIZATION = 25,
    WMT_NO_RIGHTS_EX = 26,
    WMT_BACKUPRESTORE_END = 27,
    WMT_BACKUPRESTORE_CONNECTING = 28,
    WMT_BACKUPRESTORE_DISCONNECTING = 29,
    WMT_ERROR_WITHURL = 30,
    WMT_RESTRICTED_LICENSE = 31,
    WMT_CLIENT_CONNECT = 32,
    WMT_CLIENT_DISCONNECT = 33,
    WMT_NATIVE_OUTPUT_PROPS_CHANGED = 34,
    WMT_RECONNECT_START = 35,
    WMT_RECONNECT_END = 36,
    WMT_CLIENT_CONNECT_EX = 37,
    WMT_CLIENT_DISCONNECT_EX = 38,
    WMT_SET_FEC_SPAN = 39,
    WMT_PREROLL_READY = 40,
    WMT_PREROLL_COMPLETE = 41,
    WMT_CLIENT_PROPERTIES = 42,
    WMT_LICENSEURL_SIGNATURE_STATE = 43,
    WMT_INIT_PLAYLIST_BURN = 44,
    WMT_TRANSCRYPTOR_INIT = 45,
    WMT_TRANSCRYPTOR_SEEKED = 46,
    WMT_TRANSCRYPTOR_READ = 47,
    WMT_TRANSCRYPTOR_CLOSED = 48,
    WMT_PROXIMITY_RESULT = 49,
    WMT_PROXIMITY_COMPLETED = 50,
    WMT_CONTENT_ENABLER = 51
} WMT_STATUS;
typedef enum WMT_STREAM_SELECTION {
    WMT_OFF = 0,
    WMT_CLEANPOINT_ONLY = 1,
    WMT_ON = 2
} WMT_STREAM_SELECTION;
typedef enum WMT_VERSION {
    WMT_VER_4_0 = 0x40000,
    WMT_VER_7_0 = 0x70000,
    WMT_VER_8_0 = 0x80000,
    WMT_VER_9_0 = 0x90000
} WMT_VERSION;
typedef enum WMT_PLAY_MODE {
    WMT_PLAY_MODE_AUTOSELECT = 0,
    WMT_PLAY_MODE_LOCAL = 1,
    WMT_PLAY_MODE_DOWNLOAD = 2,
    WMT_PLAY_MODE_STREAMING = 3
} WMT_PLAY_MODE;
typedef enum tagWMT_OFFSET_FORMAT {
    WMT_OFFSET_FORMAT_100NS = 0,
    WMT_OFFSET_FORMAT_FRAME_NUMBERS = 1,
    WMT_OFFSET_FORMAT_PLAYLIST_OFFSET = 2,
    WMT_OFFSET_FORMAT_TIMECODE = 3,
    WMT_OFFSET_FORMAT_100NS_APPROXIMATE = 4
} WMT_OFFSET_FORMAT;
typedef enum WMT_CODEC_INFO_TYPE {
    WMT_CODECINFO_AUDIO = 0,
    WMT_CODECINFO_VIDEO = 1,
    WMT_CODECINFO_UNKNOWN = 0xffffffff
} WMT_CODEC_INFO_TYPE;
typedef enum WMT_PROXY_SETTINGS {
    WMT_PROXY_SETTING_NONE = 0,
    WMT_PROXY_SETTING_MANUAL = 1,
    WMT_PROXY_SETTING_AUTO = 2,
    WMT_PROXY_SETTING_BROWSER = 3,
    WMT_PROXY_SETTING_MAX = 4
} WMT_PROXY_SETTINGS;
typedef enum tagWMT_STORAGE_FORMAT {
    WMT_Storage_Format_MP3 = 0,
    WMT_Storage_Format_V1 = 1
} WMT_STORAGE_FORMAT;
#pragma pack(push,2)
typedef struct _WMStreamPrioritizationRecord {
    WORD wStreamNumber;
    WINBOOL fMandatory;
} WM_STREAM_PRIORITY_RECORD;
typedef struct _WMT_TIMECODE_EXTENSION_DATA {
    WORD wRange;
    DWORD dwTimecode;
    DWORD dwUserbits;
    DWORD dwAmFlags;
} WMT_TIMECODE_EXTENSION_DATA;
#pragma pack(pop)
typedef struct _WM_PORT_NUMBER_RANGE {
    WORD wPortBegin;
    WORD wPortEnd;
} WM_PORT_NUMBER_RANGE;
typedef LPCWSTR LPCWSTR_WMSDK_TYPE_SAFE;
/*****************************************************************************
 * IWMStatusCallback interface
 */
#ifndef __IWMStatusCallback_INTERFACE_DEFINED__
#define __IWMStatusCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMStatusCallback, 0x6d7cdc70, 0x9888, 0x11d3, 0x8e,0xdc, 0x00,0xc0,0x4f,0x61,0x09,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6d7cdc70-9888-11d3-8edc-00c04f6109cf")
IWMStatusCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnStatus(
        WMT_STATUS Status,
        HRESULT hr,
        WMT_ATTR_DATATYPE dwType,
        BYTE *pValue,
        void *pvContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMStatusCallback, 0x6d7cdc70, 0x9888, 0x11d3, 0x8e,0xdc, 0x00,0xc0,0x4f,0x61,0x09,0xcf)
#endif
#else
typedef struct IWMStatusCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMStatusCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMStatusCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMStatusCallback *This);

    /*** IWMStatusCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnStatus)(
        IWMStatusCallback *This,
        WMT_STATUS Status,
        HRESULT hr,
        WMT_ATTR_DATATYPE dwType,
        BYTE *pValue,
        void *pvContext);

    END_INTERFACE
} IWMStatusCallbackVtbl;

interface IWMStatusCallback {
    CONST_VTBL IWMStatusCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMStatusCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMStatusCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMStatusCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IWMStatusCallback methods ***/
#define IWMStatusCallback_OnStatus(This,Status,hr,dwType,pValue,pvContext) (This)->lpVtbl->OnStatus(This,Status,hr,dwType,pValue,pvContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMStatusCallback_QueryInterface(IWMStatusCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMStatusCallback_AddRef(IWMStatusCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMStatusCallback_Release(IWMStatusCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMStatusCallback methods ***/
static inline HRESULT IWMStatusCallback_OnStatus(IWMStatusCallback* This,WMT_STATUS Status,HRESULT hr,WMT_ATTR_DATATYPE dwType,BYTE *pValue,void *pvContext) {
    return This->lpVtbl->OnStatus(This,Status,hr,dwType,pValue,pvContext);
}
#endif
#endif

#endif


#endif  /* __IWMStatusCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderCallback interface
 */
#ifndef __IWMReaderCallback_INTERFACE_DEFINED__
#define __IWMReaderCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderCallback, 0x96406bd8, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bd8-2b2b-11d3-b36b-00c04f6108ff")
IWMReaderCallback : public IWMStatusCallback
{
    virtual HRESULT STDMETHODCALLTYPE OnSample(
        DWORD dwOutputNum,
        QWORD cnsSampleTime,
        QWORD cnsSampleDuration,
        DWORD dwFlags,
        INSSBuffer *pSample,
        void *pvContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderCallback, 0x96406bd8, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMReaderCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderCallback *This);

    /*** IWMStatusCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnStatus)(
        IWMReaderCallback *This,
        WMT_STATUS Status,
        HRESULT hr,
        WMT_ATTR_DATATYPE dwType,
        BYTE *pValue,
        void *pvContext);

    /*** IWMReaderCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnSample)(
        IWMReaderCallback *This,
        DWORD dwOutputNum,
        QWORD cnsSampleTime,
        QWORD cnsSampleDuration,
        DWORD dwFlags,
        INSSBuffer *pSample,
        void *pvContext);

    END_INTERFACE
} IWMReaderCallbackVtbl;

interface IWMReaderCallback {
    CONST_VTBL IWMReaderCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IWMStatusCallback methods ***/
#define IWMReaderCallback_OnStatus(This,Status,hr,dwType,pValue,pvContext) (This)->lpVtbl->OnStatus(This,Status,hr,dwType,pValue,pvContext)
/*** IWMReaderCallback methods ***/
#define IWMReaderCallback_OnSample(This,dwOutputNum,cnsSampleTime,cnsSampleDuration,dwFlags,pSample,pvContext) (This)->lpVtbl->OnSample(This,dwOutputNum,cnsSampleTime,cnsSampleDuration,dwFlags,pSample,pvContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderCallback_QueryInterface(IWMReaderCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderCallback_AddRef(IWMReaderCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderCallback_Release(IWMReaderCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMStatusCallback methods ***/
static inline HRESULT IWMReaderCallback_OnStatus(IWMReaderCallback* This,WMT_STATUS Status,HRESULT hr,WMT_ATTR_DATATYPE dwType,BYTE *pValue,void *pvContext) {
    return This->lpVtbl->OnStatus(This,Status,hr,dwType,pValue,pvContext);
}
/*** IWMReaderCallback methods ***/
static inline HRESULT IWMReaderCallback_OnSample(IWMReaderCallback* This,DWORD dwOutputNum,QWORD cnsSampleTime,QWORD cnsSampleDuration,DWORD dwFlags,INSSBuffer *pSample,void *pvContext) {
    return This->lpVtbl->OnSample(This,dwOutputNum,cnsSampleTime,cnsSampleDuration,dwFlags,pSample,pvContext);
}
#endif
#endif

#endif


#endif  /* __IWMReaderCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderCallbackAdvanced interface
 */
#ifndef __IWMReaderCallbackAdvanced_INTERFACE_DEFINED__
#define __IWMReaderCallbackAdvanced_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderCallbackAdvanced, 0x96406beb, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406beb-2b2b-11d3-b36b-00c04f6108ff")
IWMReaderCallbackAdvanced : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnStreamSample(
        WORD stream_number,
        QWORD pts,
        QWORD duration,
        DWORD flags,
        INSSBuffer *sample,
        void *context) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnTime(
        QWORD time,
        void *context) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnStreamSelection(
        WORD count,
        WORD *stream_numbers,
        WMT_STREAM_SELECTION *selections,
        void *context) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnOutputPropsChanged(
        DWORD output_number,
        WM_MEDIA_TYPE *mt,
        void *context) = 0;

    virtual HRESULT STDMETHODCALLTYPE AllocateForStream(
        WORD stream_number,
        DWORD size,
        INSSBuffer **sample,
        void *context) = 0;

    virtual HRESULT STDMETHODCALLTYPE AllocateForOutput(
        DWORD output_number,
        DWORD size,
        INSSBuffer **sample,
        void *context) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderCallbackAdvanced, 0x96406beb, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMReaderCallbackAdvancedVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderCallbackAdvanced *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderCallbackAdvanced *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderCallbackAdvanced *This);

    /*** IWMReaderCallbackAdvanced methods ***/
    HRESULT (STDMETHODCALLTYPE *OnStreamSample)(
        IWMReaderCallbackAdvanced *This,
        WORD stream_number,
        QWORD pts,
        QWORD duration,
        DWORD flags,
        INSSBuffer *sample,
        void *context);

    HRESULT (STDMETHODCALLTYPE *OnTime)(
        IWMReaderCallbackAdvanced *This,
        QWORD time,
        void *context);

    HRESULT (STDMETHODCALLTYPE *OnStreamSelection)(
        IWMReaderCallbackAdvanced *This,
        WORD count,
        WORD *stream_numbers,
        WMT_STREAM_SELECTION *selections,
        void *context);

    HRESULT (STDMETHODCALLTYPE *OnOutputPropsChanged)(
        IWMReaderCallbackAdvanced *This,
        DWORD output_number,
        WM_MEDIA_TYPE *mt,
        void *context);

    HRESULT (STDMETHODCALLTYPE *AllocateForStream)(
        IWMReaderCallbackAdvanced *This,
        WORD stream_number,
        DWORD size,
        INSSBuffer **sample,
        void *context);

    HRESULT (STDMETHODCALLTYPE *AllocateForOutput)(
        IWMReaderCallbackAdvanced *This,
        DWORD output_number,
        DWORD size,
        INSSBuffer **sample,
        void *context);

    END_INTERFACE
} IWMReaderCallbackAdvancedVtbl;

interface IWMReaderCallbackAdvanced {
    CONST_VTBL IWMReaderCallbackAdvancedVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderCallbackAdvanced_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderCallbackAdvanced_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderCallbackAdvanced_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderCallbackAdvanced methods ***/
#define IWMReaderCallbackAdvanced_OnStreamSample(This,stream_number,pts,duration,flags,sample,context) (This)->lpVtbl->OnStreamSample(This,stream_number,pts,duration,flags,sample,context)
#define IWMReaderCallbackAdvanced_OnTime(This,time,context) (This)->lpVtbl->OnTime(This,time,context)
#define IWMReaderCallbackAdvanced_OnStreamSelection(This,count,stream_numbers,selections,context) (This)->lpVtbl->OnStreamSelection(This,count,stream_numbers,selections,context)
#define IWMReaderCallbackAdvanced_OnOutputPropsChanged(This,output_number,mt,context) (This)->lpVtbl->OnOutputPropsChanged(This,output_number,mt,context)
#define IWMReaderCallbackAdvanced_AllocateForStream(This,stream_number,size,sample,context) (This)->lpVtbl->AllocateForStream(This,stream_number,size,sample,context)
#define IWMReaderCallbackAdvanced_AllocateForOutput(This,output_number,size,sample,context) (This)->lpVtbl->AllocateForOutput(This,output_number,size,sample,context)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderCallbackAdvanced_QueryInterface(IWMReaderCallbackAdvanced* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderCallbackAdvanced_AddRef(IWMReaderCallbackAdvanced* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderCallbackAdvanced_Release(IWMReaderCallbackAdvanced* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderCallbackAdvanced methods ***/
static inline HRESULT IWMReaderCallbackAdvanced_OnStreamSample(IWMReaderCallbackAdvanced* This,WORD stream_number,QWORD pts,QWORD duration,DWORD flags,INSSBuffer *sample,void *context) {
    return This->lpVtbl->OnStreamSample(This,stream_number,pts,duration,flags,sample,context);
}
static inline HRESULT IWMReaderCallbackAdvanced_OnTime(IWMReaderCallbackAdvanced* This,QWORD time,void *context) {
    return This->lpVtbl->OnTime(This,time,context);
}
static inline HRESULT IWMReaderCallbackAdvanced_OnStreamSelection(IWMReaderCallbackAdvanced* This,WORD count,WORD *stream_numbers,WMT_STREAM_SELECTION *selections,void *context) {
    return This->lpVtbl->OnStreamSelection(This,count,stream_numbers,selections,context);
}
static inline HRESULT IWMReaderCallbackAdvanced_OnOutputPropsChanged(IWMReaderCallbackAdvanced* This,DWORD output_number,WM_MEDIA_TYPE *mt,void *context) {
    return This->lpVtbl->OnOutputPropsChanged(This,output_number,mt,context);
}
static inline HRESULT IWMReaderCallbackAdvanced_AllocateForStream(IWMReaderCallbackAdvanced* This,WORD stream_number,DWORD size,INSSBuffer **sample,void *context) {
    return This->lpVtbl->AllocateForStream(This,stream_number,size,sample,context);
}
static inline HRESULT IWMReaderCallbackAdvanced_AllocateForOutput(IWMReaderCallbackAdvanced* This,DWORD output_number,DWORD size,INSSBuffer **sample,void *context) {
    return This->lpVtbl->AllocateForOutput(This,output_number,size,sample,context);
}
#endif
#endif

#endif


#endif  /* __IWMReaderCallbackAdvanced_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMStreamList interface
 */
#ifndef __IWMStreamList_INTERFACE_DEFINED__
#define __IWMStreamList_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMStreamList, 0x96406bdd, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bdd-2b2b-11d3-b36b-00c04f6108ff")
IWMStreamList : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStreams(
        WORD *pwStreamNumArray,
        WORD *pcStreams) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStream(
        WORD wStreamNum) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveStream(
        WORD wStreamNum) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMStreamList, 0x96406bdd, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMStreamListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMStreamList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMStreamList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMStreamList *This);

    /*** IWMStreamList methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreams)(
        IWMStreamList *This,
        WORD *pwStreamNumArray,
        WORD *pcStreams);

    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IWMStreamList *This,
        WORD wStreamNum);

    HRESULT (STDMETHODCALLTYPE *RemoveStream)(
        IWMStreamList *This,
        WORD wStreamNum);

    END_INTERFACE
} IWMStreamListVtbl;

interface IWMStreamList {
    CONST_VTBL IWMStreamListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMStreamList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMStreamList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMStreamList_Release(This) (This)->lpVtbl->Release(This)
/*** IWMStreamList methods ***/
#define IWMStreamList_GetStreams(This,pwStreamNumArray,pcStreams) (This)->lpVtbl->GetStreams(This,pwStreamNumArray,pcStreams)
#define IWMStreamList_AddStream(This,wStreamNum) (This)->lpVtbl->AddStream(This,wStreamNum)
#define IWMStreamList_RemoveStream(This,wStreamNum) (This)->lpVtbl->RemoveStream(This,wStreamNum)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMStreamList_QueryInterface(IWMStreamList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMStreamList_AddRef(IWMStreamList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMStreamList_Release(IWMStreamList* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMStreamList methods ***/
static inline HRESULT IWMStreamList_GetStreams(IWMStreamList* This,WORD *pwStreamNumArray,WORD *pcStreams) {
    return This->lpVtbl->GetStreams(This,pwStreamNumArray,pcStreams);
}
static inline HRESULT IWMStreamList_AddStream(IWMStreamList* This,WORD wStreamNum) {
    return This->lpVtbl->AddStream(This,wStreamNum);
}
static inline HRESULT IWMStreamList_RemoveStream(IWMStreamList* This,WORD wStreamNum) {
    return This->lpVtbl->RemoveStream(This,wStreamNum);
}
#endif
#endif

#endif


#endif  /* __IWMStreamList_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMMutualExclusion interface
 */
#ifndef __IWMMutualExclusion_INTERFACE_DEFINED__
#define __IWMMutualExclusion_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMMutualExclusion, 0x96406bde, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bde-2b2b-11d3-b36b-00c04f6108ff")
IWMMutualExclusion : public IWMStreamList
{
    virtual HRESULT STDMETHODCALLTYPE GetType(
        GUID *pguidType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetType(
        REFGUID guidType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMMutualExclusion, 0x96406bde, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMMutualExclusionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMMutualExclusion *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMMutualExclusion *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMMutualExclusion *This);

    /*** IWMStreamList methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreams)(
        IWMMutualExclusion *This,
        WORD *pwStreamNumArray,
        WORD *pcStreams);

    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IWMMutualExclusion *This,
        WORD wStreamNum);

    HRESULT (STDMETHODCALLTYPE *RemoveStream)(
        IWMMutualExclusion *This,
        WORD wStreamNum);

    /*** IWMMutualExclusion methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        IWMMutualExclusion *This,
        GUID *pguidType);

    HRESULT (STDMETHODCALLTYPE *SetType)(
        IWMMutualExclusion *This,
        REFGUID guidType);

    END_INTERFACE
} IWMMutualExclusionVtbl;

interface IWMMutualExclusion {
    CONST_VTBL IWMMutualExclusionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMMutualExclusion_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMMutualExclusion_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMMutualExclusion_Release(This) (This)->lpVtbl->Release(This)
/*** IWMStreamList methods ***/
#define IWMMutualExclusion_GetStreams(This,pwStreamNumArray,pcStreams) (This)->lpVtbl->GetStreams(This,pwStreamNumArray,pcStreams)
#define IWMMutualExclusion_AddStream(This,wStreamNum) (This)->lpVtbl->AddStream(This,wStreamNum)
#define IWMMutualExclusion_RemoveStream(This,wStreamNum) (This)->lpVtbl->RemoveStream(This,wStreamNum)
/*** IWMMutualExclusion methods ***/
#define IWMMutualExclusion_GetType(This,pguidType) (This)->lpVtbl->GetType(This,pguidType)
#define IWMMutualExclusion_SetType(This,guidType) (This)->lpVtbl->SetType(This,guidType)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMMutualExclusion_QueryInterface(IWMMutualExclusion* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMMutualExclusion_AddRef(IWMMutualExclusion* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMMutualExclusion_Release(IWMMutualExclusion* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMStreamList methods ***/
static inline HRESULT IWMMutualExclusion_GetStreams(IWMMutualExclusion* This,WORD *pwStreamNumArray,WORD *pcStreams) {
    return This->lpVtbl->GetStreams(This,pwStreamNumArray,pcStreams);
}
static inline HRESULT IWMMutualExclusion_AddStream(IWMMutualExclusion* This,WORD wStreamNum) {
    return This->lpVtbl->AddStream(This,wStreamNum);
}
static inline HRESULT IWMMutualExclusion_RemoveStream(IWMMutualExclusion* This,WORD wStreamNum) {
    return This->lpVtbl->RemoveStream(This,wStreamNum);
}
/*** IWMMutualExclusion methods ***/
static inline HRESULT IWMMutualExclusion_GetType(IWMMutualExclusion* This,GUID *pguidType) {
    return This->lpVtbl->GetType(This,pguidType);
}
static inline HRESULT IWMMutualExclusion_SetType(IWMMutualExclusion* This,REFGUID guidType) {
    return This->lpVtbl->SetType(This,guidType);
}
#endif
#endif

#endif


#endif  /* __IWMMutualExclusion_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMBandwidthSharing interface
 */
#ifndef __IWMBandwidthSharing_INTERFACE_DEFINED__
#define __IWMBandwidthSharing_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMBandwidthSharing, 0xad694af1, 0xf8d9, 0x42f8, 0xbc,0x47, 0x70,0x31,0x1b,0x0c,0x4f,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ad694af1-f8d9-42f8-bc47-70311b0c4f9e")
IWMBandwidthSharing : public IWMStreamList
{
    virtual HRESULT STDMETHODCALLTYPE GetType(
        GUID *guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetType(
        REFGUID guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBandwidth(
        DWORD *bitrate,
        DWORD *buffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBandwidth(
        DWORD bitrate,
        DWORD buffer) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMBandwidthSharing, 0xad694af1, 0xf8d9, 0x42f8, 0xbc,0x47, 0x70,0x31,0x1b,0x0c,0x4f,0x9e)
#endif
#else
typedef struct IWMBandwidthSharingVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMBandwidthSharing *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMBandwidthSharing *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMBandwidthSharing *This);

    /*** IWMStreamList methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreams)(
        IWMBandwidthSharing *This,
        WORD *pwStreamNumArray,
        WORD *pcStreams);

    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IWMBandwidthSharing *This,
        WORD wStreamNum);

    HRESULT (STDMETHODCALLTYPE *RemoveStream)(
        IWMBandwidthSharing *This,
        WORD wStreamNum);

    /*** IWMBandwidthSharing methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        IWMBandwidthSharing *This,
        GUID *guid);

    HRESULT (STDMETHODCALLTYPE *SetType)(
        IWMBandwidthSharing *This,
        REFGUID guid);

    HRESULT (STDMETHODCALLTYPE *GetBandwidth)(
        IWMBandwidthSharing *This,
        DWORD *bitrate,
        DWORD *buffer);

    HRESULT (STDMETHODCALLTYPE *SetBandwidth)(
        IWMBandwidthSharing *This,
        DWORD bitrate,
        DWORD buffer);

    END_INTERFACE
} IWMBandwidthSharingVtbl;

interface IWMBandwidthSharing {
    CONST_VTBL IWMBandwidthSharingVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMBandwidthSharing_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMBandwidthSharing_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMBandwidthSharing_Release(This) (This)->lpVtbl->Release(This)
/*** IWMStreamList methods ***/
#define IWMBandwidthSharing_GetStreams(This,pwStreamNumArray,pcStreams) (This)->lpVtbl->GetStreams(This,pwStreamNumArray,pcStreams)
#define IWMBandwidthSharing_AddStream(This,wStreamNum) (This)->lpVtbl->AddStream(This,wStreamNum)
#define IWMBandwidthSharing_RemoveStream(This,wStreamNum) (This)->lpVtbl->RemoveStream(This,wStreamNum)
/*** IWMBandwidthSharing methods ***/
#define IWMBandwidthSharing_GetType(This,guid) (This)->lpVtbl->GetType(This,guid)
#define IWMBandwidthSharing_SetType(This,guid) (This)->lpVtbl->SetType(This,guid)
#define IWMBandwidthSharing_GetBandwidth(This,bitrate,buffer) (This)->lpVtbl->GetBandwidth(This,bitrate,buffer)
#define IWMBandwidthSharing_SetBandwidth(This,bitrate,buffer) (This)->lpVtbl->SetBandwidth(This,bitrate,buffer)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMBandwidthSharing_QueryInterface(IWMBandwidthSharing* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMBandwidthSharing_AddRef(IWMBandwidthSharing* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMBandwidthSharing_Release(IWMBandwidthSharing* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMStreamList methods ***/
static inline HRESULT IWMBandwidthSharing_GetStreams(IWMBandwidthSharing* This,WORD *pwStreamNumArray,WORD *pcStreams) {
    return This->lpVtbl->GetStreams(This,pwStreamNumArray,pcStreams);
}
static inline HRESULT IWMBandwidthSharing_AddStream(IWMBandwidthSharing* This,WORD wStreamNum) {
    return This->lpVtbl->AddStream(This,wStreamNum);
}
static inline HRESULT IWMBandwidthSharing_RemoveStream(IWMBandwidthSharing* This,WORD wStreamNum) {
    return This->lpVtbl->RemoveStream(This,wStreamNum);
}
/*** IWMBandwidthSharing methods ***/
static inline HRESULT IWMBandwidthSharing_GetType(IWMBandwidthSharing* This,GUID *guid) {
    return This->lpVtbl->GetType(This,guid);
}
static inline HRESULT IWMBandwidthSharing_SetType(IWMBandwidthSharing* This,REFGUID guid) {
    return This->lpVtbl->SetType(This,guid);
}
static inline HRESULT IWMBandwidthSharing_GetBandwidth(IWMBandwidthSharing* This,DWORD *bitrate,DWORD *buffer) {
    return This->lpVtbl->GetBandwidth(This,bitrate,buffer);
}
static inline HRESULT IWMBandwidthSharing_SetBandwidth(IWMBandwidthSharing* This,DWORD bitrate,DWORD buffer) {
    return This->lpVtbl->SetBandwidth(This,bitrate,buffer);
}
#endif
#endif

#endif


#endif  /* __IWMBandwidthSharing_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMStreamPrioritization interface
 */
#ifndef __IWMStreamPrioritization_INTERFACE_DEFINED__
#define __IWMStreamPrioritization_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMStreamPrioritization, 0x8c1c6090, 0xf9a8, 0x4748, 0x8e,0xc3, 0xdd,0x11,0x08,0xba,0x1e,0x77);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8c1c6090-f9a8-4748-8ec3-dd1108ba1e77")
IWMStreamPrioritization : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPriorityRecords(
        WM_STREAM_PRIORITY_RECORD *array,
        WORD *records) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPriorityRecords(
        WM_STREAM_PRIORITY_RECORD *array,
        WORD records) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMStreamPrioritization, 0x8c1c6090, 0xf9a8, 0x4748, 0x8e,0xc3, 0xdd,0x11,0x08,0xba,0x1e,0x77)
#endif
#else
typedef struct IWMStreamPrioritizationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMStreamPrioritization *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMStreamPrioritization *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMStreamPrioritization *This);

    /*** IWMStreamPrioritization methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPriorityRecords)(
        IWMStreamPrioritization *This,
        WM_STREAM_PRIORITY_RECORD *array,
        WORD *records);

    HRESULT (STDMETHODCALLTYPE *SetPriorityRecords)(
        IWMStreamPrioritization *This,
        WM_STREAM_PRIORITY_RECORD *array,
        WORD records);

    END_INTERFACE
} IWMStreamPrioritizationVtbl;

interface IWMStreamPrioritization {
    CONST_VTBL IWMStreamPrioritizationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMStreamPrioritization_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMStreamPrioritization_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMStreamPrioritization_Release(This) (This)->lpVtbl->Release(This)
/*** IWMStreamPrioritization methods ***/
#define IWMStreamPrioritization_GetPriorityRecords(This,array,records) (This)->lpVtbl->GetPriorityRecords(This,array,records)
#define IWMStreamPrioritization_SetPriorityRecords(This,array,records) (This)->lpVtbl->SetPriorityRecords(This,array,records)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMStreamPrioritization_QueryInterface(IWMStreamPrioritization* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMStreamPrioritization_AddRef(IWMStreamPrioritization* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMStreamPrioritization_Release(IWMStreamPrioritization* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMStreamPrioritization methods ***/
static inline HRESULT IWMStreamPrioritization_GetPriorityRecords(IWMStreamPrioritization* This,WM_STREAM_PRIORITY_RECORD *array,WORD *records) {
    return This->lpVtbl->GetPriorityRecords(This,array,records);
}
static inline HRESULT IWMStreamPrioritization_SetPriorityRecords(IWMStreamPrioritization* This,WM_STREAM_PRIORITY_RECORD *array,WORD records) {
    return This->lpVtbl->SetPriorityRecords(This,array,records);
}
#endif
#endif

#endif


#endif  /* __IWMStreamPrioritization_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMStreamConfig interface
 */
#ifndef __IWMStreamConfig_INTERFACE_DEFINED__
#define __IWMStreamConfig_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMStreamConfig, 0x96406bdc, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bdc-2b2b-11d3-b36b-00c04f6108ff")
IWMStreamConfig : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStreamType(
        GUID *pguidStreamType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamNumber(
        WORD *pwStreamNum) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamNumber(
        WORD wStreamNum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamName(
        WCHAR *pwszStreamName,
        WORD *pcchStreamName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamName(
        LPCWSTR_WMSDK_TYPE_SAFE pwszStreamName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectionName(
        WCHAR *pwszInputName,
        WORD *pcchInputName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetConnectionName(
        LPCWSTR_WMSDK_TYPE_SAFE pwszInputName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBitrate(
        DWORD *pdwBitrate) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBitrate(
        DWORD pdwBitrate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBufferWindow(
        DWORD *pmsBufferWindow) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBufferWindow(
        DWORD msBufferWindow) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMStreamConfig, 0x96406bdc, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMStreamConfigVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMStreamConfig *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMStreamConfig *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMStreamConfig *This);

    /*** IWMStreamConfig methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreamType)(
        IWMStreamConfig *This,
        GUID *pguidStreamType);

    HRESULT (STDMETHODCALLTYPE *GetStreamNumber)(
        IWMStreamConfig *This,
        WORD *pwStreamNum);

    HRESULT (STDMETHODCALLTYPE *SetStreamNumber)(
        IWMStreamConfig *This,
        WORD wStreamNum);

    HRESULT (STDMETHODCALLTYPE *GetStreamName)(
        IWMStreamConfig *This,
        WCHAR *pwszStreamName,
        WORD *pcchStreamName);

    HRESULT (STDMETHODCALLTYPE *SetStreamName)(
        IWMStreamConfig *This,
        LPCWSTR_WMSDK_TYPE_SAFE pwszStreamName);

    HRESULT (STDMETHODCALLTYPE *GetConnectionName)(
        IWMStreamConfig *This,
        WCHAR *pwszInputName,
        WORD *pcchInputName);

    HRESULT (STDMETHODCALLTYPE *SetConnectionName)(
        IWMStreamConfig *This,
        LPCWSTR_WMSDK_TYPE_SAFE pwszInputName);

    HRESULT (STDMETHODCALLTYPE *GetBitrate)(
        IWMStreamConfig *This,
        DWORD *pdwBitrate);

    HRESULT (STDMETHODCALLTYPE *SetBitrate)(
        IWMStreamConfig *This,
        DWORD pdwBitrate);

    HRESULT (STDMETHODCALLTYPE *GetBufferWindow)(
        IWMStreamConfig *This,
        DWORD *pmsBufferWindow);

    HRESULT (STDMETHODCALLTYPE *SetBufferWindow)(
        IWMStreamConfig *This,
        DWORD msBufferWindow);

    END_INTERFACE
} IWMStreamConfigVtbl;

interface IWMStreamConfig {
    CONST_VTBL IWMStreamConfigVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMStreamConfig_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMStreamConfig_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMStreamConfig_Release(This) (This)->lpVtbl->Release(This)
/*** IWMStreamConfig methods ***/
#define IWMStreamConfig_GetStreamType(This,pguidStreamType) (This)->lpVtbl->GetStreamType(This,pguidStreamType)
#define IWMStreamConfig_GetStreamNumber(This,pwStreamNum) (This)->lpVtbl->GetStreamNumber(This,pwStreamNum)
#define IWMStreamConfig_SetStreamNumber(This,wStreamNum) (This)->lpVtbl->SetStreamNumber(This,wStreamNum)
#define IWMStreamConfig_GetStreamName(This,pwszStreamName,pcchStreamName) (This)->lpVtbl->GetStreamName(This,pwszStreamName,pcchStreamName)
#define IWMStreamConfig_SetStreamName(This,pwszStreamName) (This)->lpVtbl->SetStreamName(This,pwszStreamName)
#define IWMStreamConfig_GetConnectionName(This,pwszInputName,pcchInputName) (This)->lpVtbl->GetConnectionName(This,pwszInputName,pcchInputName)
#define IWMStreamConfig_SetConnectionName(This,pwszInputName) (This)->lpVtbl->SetConnectionName(This,pwszInputName)
#define IWMStreamConfig_GetBitrate(This,pdwBitrate) (This)->lpVtbl->GetBitrate(This,pdwBitrate)
#define IWMStreamConfig_SetBitrate(This,pdwBitrate) (This)->lpVtbl->SetBitrate(This,pdwBitrate)
#define IWMStreamConfig_GetBufferWindow(This,pmsBufferWindow) (This)->lpVtbl->GetBufferWindow(This,pmsBufferWindow)
#define IWMStreamConfig_SetBufferWindow(This,msBufferWindow) (This)->lpVtbl->SetBufferWindow(This,msBufferWindow)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMStreamConfig_QueryInterface(IWMStreamConfig* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMStreamConfig_AddRef(IWMStreamConfig* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMStreamConfig_Release(IWMStreamConfig* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMStreamConfig methods ***/
static inline HRESULT IWMStreamConfig_GetStreamType(IWMStreamConfig* This,GUID *pguidStreamType) {
    return This->lpVtbl->GetStreamType(This,pguidStreamType);
}
static inline HRESULT IWMStreamConfig_GetStreamNumber(IWMStreamConfig* This,WORD *pwStreamNum) {
    return This->lpVtbl->GetStreamNumber(This,pwStreamNum);
}
static inline HRESULT IWMStreamConfig_SetStreamNumber(IWMStreamConfig* This,WORD wStreamNum) {
    return This->lpVtbl->SetStreamNumber(This,wStreamNum);
}
static inline HRESULT IWMStreamConfig_GetStreamName(IWMStreamConfig* This,WCHAR *pwszStreamName,WORD *pcchStreamName) {
    return This->lpVtbl->GetStreamName(This,pwszStreamName,pcchStreamName);
}
static inline HRESULT IWMStreamConfig_SetStreamName(IWMStreamConfig* This,LPCWSTR_WMSDK_TYPE_SAFE pwszStreamName) {
    return This->lpVtbl->SetStreamName(This,pwszStreamName);
}
static inline HRESULT IWMStreamConfig_GetConnectionName(IWMStreamConfig* This,WCHAR *pwszInputName,WORD *pcchInputName) {
    return This->lpVtbl->GetConnectionName(This,pwszInputName,pcchInputName);
}
static inline HRESULT IWMStreamConfig_SetConnectionName(IWMStreamConfig* This,LPCWSTR_WMSDK_TYPE_SAFE pwszInputName) {
    return This->lpVtbl->SetConnectionName(This,pwszInputName);
}
static inline HRESULT IWMStreamConfig_GetBitrate(IWMStreamConfig* This,DWORD *pdwBitrate) {
    return This->lpVtbl->GetBitrate(This,pdwBitrate);
}
static inline HRESULT IWMStreamConfig_SetBitrate(IWMStreamConfig* This,DWORD pdwBitrate) {
    return This->lpVtbl->SetBitrate(This,pdwBitrate);
}
static inline HRESULT IWMStreamConfig_GetBufferWindow(IWMStreamConfig* This,DWORD *pmsBufferWindow) {
    return This->lpVtbl->GetBufferWindow(This,pmsBufferWindow);
}
static inline HRESULT IWMStreamConfig_SetBufferWindow(IWMStreamConfig* This,DWORD msBufferWindow) {
    return This->lpVtbl->SetBufferWindow(This,msBufferWindow);
}
#endif
#endif

#endif


#endif  /* __IWMStreamConfig_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMProfile interface
 */
#ifndef __IWMProfile_INTERFACE_DEFINED__
#define __IWMProfile_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMProfile, 0x96406bdb, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bdb-2b2b-11d3-b36b-00c04f6108ff")
IWMProfile : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetVersion(
        WMT_VERSION *pdwVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetName(
        WCHAR *pwszName,
        DWORD *pcchName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetName(
        const WCHAR *pwszName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDescription(
        WCHAR *pwszDescription,
        DWORD *pcchDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDescription(
        const WCHAR *pwszDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamCount(
        DWORD *pcStreams) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStream(
        DWORD dwStreamIndex,
        IWMStreamConfig **ppConfig) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamByNumber(
        WORD wStreamNum,
        IWMStreamConfig **ppConfig) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveStream(
        IWMStreamConfig *pConfig) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveStreamByNumber(
        WORD wStreamNum) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStream(
        IWMStreamConfig *pConfig) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReconfigStream(
        IWMStreamConfig *pConfig) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateNewStream(
        REFGUID guidStreamType,
        IWMStreamConfig **ppConfig) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMutualExclusionCount(
        DWORD *pcME) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMutualExclusion(
        DWORD dwMEIndex,
        IWMMutualExclusion **ppME) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveMutualExclusion(
        IWMMutualExclusion *pME) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddMutualExclusion(
        IWMMutualExclusion *pME) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateNewMutualExclusion(
        IWMMutualExclusion **ppME) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMProfile, 0x96406bdb, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMProfileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMProfile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMProfile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMProfile *This);

    /*** IWMProfile methods ***/
    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWMProfile *This,
        WMT_VERSION *pdwVersion);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IWMProfile *This,
        WCHAR *pwszName,
        DWORD *pcchName);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        IWMProfile *This,
        const WCHAR *pwszName);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        IWMProfile *This,
        WCHAR *pwszDescription,
        DWORD *pcchDescription);

    HRESULT (STDMETHODCALLTYPE *SetDescription)(
        IWMProfile *This,
        const WCHAR *pwszDescription);

    HRESULT (STDMETHODCALLTYPE *GetStreamCount)(
        IWMProfile *This,
        DWORD *pcStreams);

    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IWMProfile *This,
        DWORD dwStreamIndex,
        IWMStreamConfig **ppConfig);

    HRESULT (STDMETHODCALLTYPE *GetStreamByNumber)(
        IWMProfile *This,
        WORD wStreamNum,
        IWMStreamConfig **ppConfig);

    HRESULT (STDMETHODCALLTYPE *RemoveStream)(
        IWMProfile *This,
        IWMStreamConfig *pConfig);

    HRESULT (STDMETHODCALLTYPE *RemoveStreamByNumber)(
        IWMProfile *This,
        WORD wStreamNum);

    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IWMProfile *This,
        IWMStreamConfig *pConfig);

    HRESULT (STDMETHODCALLTYPE *ReconfigStream)(
        IWMProfile *This,
        IWMStreamConfig *pConfig);

    HRESULT (STDMETHODCALLTYPE *CreateNewStream)(
        IWMProfile *This,
        REFGUID guidStreamType,
        IWMStreamConfig **ppConfig);

    HRESULT (STDMETHODCALLTYPE *GetMutualExclusionCount)(
        IWMProfile *This,
        DWORD *pcME);

    HRESULT (STDMETHODCALLTYPE *GetMutualExclusion)(
        IWMProfile *This,
        DWORD dwMEIndex,
        IWMMutualExclusion **ppME);

    HRESULT (STDMETHODCALLTYPE *RemoveMutualExclusion)(
        IWMProfile *This,
        IWMMutualExclusion *pME);

    HRESULT (STDMETHODCALLTYPE *AddMutualExclusion)(
        IWMProfile *This,
        IWMMutualExclusion *pME);

    HRESULT (STDMETHODCALLTYPE *CreateNewMutualExclusion)(
        IWMProfile *This,
        IWMMutualExclusion **ppME);

    END_INTERFACE
} IWMProfileVtbl;

interface IWMProfile {
    CONST_VTBL IWMProfileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMProfile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMProfile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMProfile_Release(This) (This)->lpVtbl->Release(This)
/*** IWMProfile methods ***/
#define IWMProfile_GetVersion(This,pdwVersion) (This)->lpVtbl->GetVersion(This,pdwVersion)
#define IWMProfile_GetName(This,pwszName,pcchName) (This)->lpVtbl->GetName(This,pwszName,pcchName)
#define IWMProfile_SetName(This,pwszName) (This)->lpVtbl->SetName(This,pwszName)
#define IWMProfile_GetDescription(This,pwszDescription,pcchDescription) (This)->lpVtbl->GetDescription(This,pwszDescription,pcchDescription)
#define IWMProfile_SetDescription(This,pwszDescription) (This)->lpVtbl->SetDescription(This,pwszDescription)
#define IWMProfile_GetStreamCount(This,pcStreams) (This)->lpVtbl->GetStreamCount(This,pcStreams)
#define IWMProfile_GetStream(This,dwStreamIndex,ppConfig) (This)->lpVtbl->GetStream(This,dwStreamIndex,ppConfig)
#define IWMProfile_GetStreamByNumber(This,wStreamNum,ppConfig) (This)->lpVtbl->GetStreamByNumber(This,wStreamNum,ppConfig)
#define IWMProfile_RemoveStream(This,pConfig) (This)->lpVtbl->RemoveStream(This,pConfig)
#define IWMProfile_RemoveStreamByNumber(This,wStreamNum) (This)->lpVtbl->RemoveStreamByNumber(This,wStreamNum)
#define IWMProfile_AddStream(This,pConfig) (This)->lpVtbl->AddStream(This,pConfig)
#define IWMProfile_ReconfigStream(This,pConfig) (This)->lpVtbl->ReconfigStream(This,pConfig)
#define IWMProfile_CreateNewStream(This,guidStreamType,ppConfig) (This)->lpVtbl->CreateNewStream(This,guidStreamType,ppConfig)
#define IWMProfile_GetMutualExclusionCount(This,pcME) (This)->lpVtbl->GetMutualExclusionCount(This,pcME)
#define IWMProfile_GetMutualExclusion(This,dwMEIndex,ppME) (This)->lpVtbl->GetMutualExclusion(This,dwMEIndex,ppME)
#define IWMProfile_RemoveMutualExclusion(This,pME) (This)->lpVtbl->RemoveMutualExclusion(This,pME)
#define IWMProfile_AddMutualExclusion(This,pME) (This)->lpVtbl->AddMutualExclusion(This,pME)
#define IWMProfile_CreateNewMutualExclusion(This,ppME) (This)->lpVtbl->CreateNewMutualExclusion(This,ppME)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMProfile_QueryInterface(IWMProfile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMProfile_AddRef(IWMProfile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMProfile_Release(IWMProfile* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMProfile methods ***/
static inline HRESULT IWMProfile_GetVersion(IWMProfile* This,WMT_VERSION *pdwVersion) {
    return This->lpVtbl->GetVersion(This,pdwVersion);
}
static inline HRESULT IWMProfile_GetName(IWMProfile* This,WCHAR *pwszName,DWORD *pcchName) {
    return This->lpVtbl->GetName(This,pwszName,pcchName);
}
static inline HRESULT IWMProfile_SetName(IWMProfile* This,const WCHAR *pwszName) {
    return This->lpVtbl->SetName(This,pwszName);
}
static inline HRESULT IWMProfile_GetDescription(IWMProfile* This,WCHAR *pwszDescription,DWORD *pcchDescription) {
    return This->lpVtbl->GetDescription(This,pwszDescription,pcchDescription);
}
static inline HRESULT IWMProfile_SetDescription(IWMProfile* This,const WCHAR *pwszDescription) {
    return This->lpVtbl->SetDescription(This,pwszDescription);
}
static inline HRESULT IWMProfile_GetStreamCount(IWMProfile* This,DWORD *pcStreams) {
    return This->lpVtbl->GetStreamCount(This,pcStreams);
}
static inline HRESULT IWMProfile_GetStream(IWMProfile* This,DWORD dwStreamIndex,IWMStreamConfig **ppConfig) {
    return This->lpVtbl->GetStream(This,dwStreamIndex,ppConfig);
}
static inline HRESULT IWMProfile_GetStreamByNumber(IWMProfile* This,WORD wStreamNum,IWMStreamConfig **ppConfig) {
    return This->lpVtbl->GetStreamByNumber(This,wStreamNum,ppConfig);
}
static inline HRESULT IWMProfile_RemoveStream(IWMProfile* This,IWMStreamConfig *pConfig) {
    return This->lpVtbl->RemoveStream(This,pConfig);
}
static inline HRESULT IWMProfile_RemoveStreamByNumber(IWMProfile* This,WORD wStreamNum) {
    return This->lpVtbl->RemoveStreamByNumber(This,wStreamNum);
}
static inline HRESULT IWMProfile_AddStream(IWMProfile* This,IWMStreamConfig *pConfig) {
    return This->lpVtbl->AddStream(This,pConfig);
}
static inline HRESULT IWMProfile_ReconfigStream(IWMProfile* This,IWMStreamConfig *pConfig) {
    return This->lpVtbl->ReconfigStream(This,pConfig);
}
static inline HRESULT IWMProfile_CreateNewStream(IWMProfile* This,REFGUID guidStreamType,IWMStreamConfig **ppConfig) {
    return This->lpVtbl->CreateNewStream(This,guidStreamType,ppConfig);
}
static inline HRESULT IWMProfile_GetMutualExclusionCount(IWMProfile* This,DWORD *pcME) {
    return This->lpVtbl->GetMutualExclusionCount(This,pcME);
}
static inline HRESULT IWMProfile_GetMutualExclusion(IWMProfile* This,DWORD dwMEIndex,IWMMutualExclusion **ppME) {
    return This->lpVtbl->GetMutualExclusion(This,dwMEIndex,ppME);
}
static inline HRESULT IWMProfile_RemoveMutualExclusion(IWMProfile* This,IWMMutualExclusion *pME) {
    return This->lpVtbl->RemoveMutualExclusion(This,pME);
}
static inline HRESULT IWMProfile_AddMutualExclusion(IWMProfile* This,IWMMutualExclusion *pME) {
    return This->lpVtbl->AddMutualExclusion(This,pME);
}
static inline HRESULT IWMProfile_CreateNewMutualExclusion(IWMProfile* This,IWMMutualExclusion **ppME) {
    return This->lpVtbl->CreateNewMutualExclusion(This,ppME);
}
#endif
#endif

#endif


#endif  /* __IWMProfile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMProfile2 interface
 */
#ifndef __IWMProfile2_INTERFACE_DEFINED__
#define __IWMProfile2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMProfile2, 0x07e72d33, 0xd94e, 0x4be7, 0x88,0x43, 0x60,0xae,0x5f,0xf7,0xe5,0xf5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("07e72d33-d94e-4be7-8843-60ae5ff7e5f5")
IWMProfile2 : public IWMProfile
{
    virtual HRESULT STDMETHODCALLTYPE GetProfileID(
        GUID *guid) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMProfile2, 0x07e72d33, 0xd94e, 0x4be7, 0x88,0x43, 0x60,0xae,0x5f,0xf7,0xe5,0xf5)
#endif
#else
typedef struct IWMProfile2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMProfile2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMProfile2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMProfile2 *This);

    /*** IWMProfile methods ***/
    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWMProfile2 *This,
        WMT_VERSION *pdwVersion);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IWMProfile2 *This,
        WCHAR *pwszName,
        DWORD *pcchName);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        IWMProfile2 *This,
        const WCHAR *pwszName);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        IWMProfile2 *This,
        WCHAR *pwszDescription,
        DWORD *pcchDescription);

    HRESULT (STDMETHODCALLTYPE *SetDescription)(
        IWMProfile2 *This,
        const WCHAR *pwszDescription);

    HRESULT (STDMETHODCALLTYPE *GetStreamCount)(
        IWMProfile2 *This,
        DWORD *pcStreams);

    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IWMProfile2 *This,
        DWORD dwStreamIndex,
        IWMStreamConfig **ppConfig);

    HRESULT (STDMETHODCALLTYPE *GetStreamByNumber)(
        IWMProfile2 *This,
        WORD wStreamNum,
        IWMStreamConfig **ppConfig);

    HRESULT (STDMETHODCALLTYPE *RemoveStream)(
        IWMProfile2 *This,
        IWMStreamConfig *pConfig);

    HRESULT (STDMETHODCALLTYPE *RemoveStreamByNumber)(
        IWMProfile2 *This,
        WORD wStreamNum);

    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IWMProfile2 *This,
        IWMStreamConfig *pConfig);

    HRESULT (STDMETHODCALLTYPE *ReconfigStream)(
        IWMProfile2 *This,
        IWMStreamConfig *pConfig);

    HRESULT (STDMETHODCALLTYPE *CreateNewStream)(
        IWMProfile2 *This,
        REFGUID guidStreamType,
        IWMStreamConfig **ppConfig);

    HRESULT (STDMETHODCALLTYPE *GetMutualExclusionCount)(
        IWMProfile2 *This,
        DWORD *pcME);

    HRESULT (STDMETHODCALLTYPE *GetMutualExclusion)(
        IWMProfile2 *This,
        DWORD dwMEIndex,
        IWMMutualExclusion **ppME);

    HRESULT (STDMETHODCALLTYPE *RemoveMutualExclusion)(
        IWMProfile2 *This,
        IWMMutualExclusion *pME);

    HRESULT (STDMETHODCALLTYPE *AddMutualExclusion)(
        IWMProfile2 *This,
        IWMMutualExclusion *pME);

    HRESULT (STDMETHODCALLTYPE *CreateNewMutualExclusion)(
        IWMProfile2 *This,
        IWMMutualExclusion **ppME);

    /*** IWMProfile2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProfileID)(
        IWMProfile2 *This,
        GUID *guid);

    END_INTERFACE
} IWMProfile2Vtbl;

interface IWMProfile2 {
    CONST_VTBL IWMProfile2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMProfile2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMProfile2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMProfile2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMProfile methods ***/
#define IWMProfile2_GetVersion(This,pdwVersion) (This)->lpVtbl->GetVersion(This,pdwVersion)
#define IWMProfile2_GetName(This,pwszName,pcchName) (This)->lpVtbl->GetName(This,pwszName,pcchName)
#define IWMProfile2_SetName(This,pwszName) (This)->lpVtbl->SetName(This,pwszName)
#define IWMProfile2_GetDescription(This,pwszDescription,pcchDescription) (This)->lpVtbl->GetDescription(This,pwszDescription,pcchDescription)
#define IWMProfile2_SetDescription(This,pwszDescription) (This)->lpVtbl->SetDescription(This,pwszDescription)
#define IWMProfile2_GetStreamCount(This,pcStreams) (This)->lpVtbl->GetStreamCount(This,pcStreams)
#define IWMProfile2_GetStream(This,dwStreamIndex,ppConfig) (This)->lpVtbl->GetStream(This,dwStreamIndex,ppConfig)
#define IWMProfile2_GetStreamByNumber(This,wStreamNum,ppConfig) (This)->lpVtbl->GetStreamByNumber(This,wStreamNum,ppConfig)
#define IWMProfile2_RemoveStream(This,pConfig) (This)->lpVtbl->RemoveStream(This,pConfig)
#define IWMProfile2_RemoveStreamByNumber(This,wStreamNum) (This)->lpVtbl->RemoveStreamByNumber(This,wStreamNum)
#define IWMProfile2_AddStream(This,pConfig) (This)->lpVtbl->AddStream(This,pConfig)
#define IWMProfile2_ReconfigStream(This,pConfig) (This)->lpVtbl->ReconfigStream(This,pConfig)
#define IWMProfile2_CreateNewStream(This,guidStreamType,ppConfig) (This)->lpVtbl->CreateNewStream(This,guidStreamType,ppConfig)
#define IWMProfile2_GetMutualExclusionCount(This,pcME) (This)->lpVtbl->GetMutualExclusionCount(This,pcME)
#define IWMProfile2_GetMutualExclusion(This,dwMEIndex,ppME) (This)->lpVtbl->GetMutualExclusion(This,dwMEIndex,ppME)
#define IWMProfile2_RemoveMutualExclusion(This,pME) (This)->lpVtbl->RemoveMutualExclusion(This,pME)
#define IWMProfile2_AddMutualExclusion(This,pME) (This)->lpVtbl->AddMutualExclusion(This,pME)
#define IWMProfile2_CreateNewMutualExclusion(This,ppME) (This)->lpVtbl->CreateNewMutualExclusion(This,ppME)
/*** IWMProfile2 methods ***/
#define IWMProfile2_GetProfileID(This,guid) (This)->lpVtbl->GetProfileID(This,guid)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMProfile2_QueryInterface(IWMProfile2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMProfile2_AddRef(IWMProfile2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMProfile2_Release(IWMProfile2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMProfile methods ***/
static inline HRESULT IWMProfile2_GetVersion(IWMProfile2* This,WMT_VERSION *pdwVersion) {
    return This->lpVtbl->GetVersion(This,pdwVersion);
}
static inline HRESULT IWMProfile2_GetName(IWMProfile2* This,WCHAR *pwszName,DWORD *pcchName) {
    return This->lpVtbl->GetName(This,pwszName,pcchName);
}
static inline HRESULT IWMProfile2_SetName(IWMProfile2* This,const WCHAR *pwszName) {
    return This->lpVtbl->SetName(This,pwszName);
}
static inline HRESULT IWMProfile2_GetDescription(IWMProfile2* This,WCHAR *pwszDescription,DWORD *pcchDescription) {
    return This->lpVtbl->GetDescription(This,pwszDescription,pcchDescription);
}
static inline HRESULT IWMProfile2_SetDescription(IWMProfile2* This,const WCHAR *pwszDescription) {
    return This->lpVtbl->SetDescription(This,pwszDescription);
}
static inline HRESULT IWMProfile2_GetStreamCount(IWMProfile2* This,DWORD *pcStreams) {
    return This->lpVtbl->GetStreamCount(This,pcStreams);
}
static inline HRESULT IWMProfile2_GetStream(IWMProfile2* This,DWORD dwStreamIndex,IWMStreamConfig **ppConfig) {
    return This->lpVtbl->GetStream(This,dwStreamIndex,ppConfig);
}
static inline HRESULT IWMProfile2_GetStreamByNumber(IWMProfile2* This,WORD wStreamNum,IWMStreamConfig **ppConfig) {
    return This->lpVtbl->GetStreamByNumber(This,wStreamNum,ppConfig);
}
static inline HRESULT IWMProfile2_RemoveStream(IWMProfile2* This,IWMStreamConfig *pConfig) {
    return This->lpVtbl->RemoveStream(This,pConfig);
}
static inline HRESULT IWMProfile2_RemoveStreamByNumber(IWMProfile2* This,WORD wStreamNum) {
    return This->lpVtbl->RemoveStreamByNumber(This,wStreamNum);
}
static inline HRESULT IWMProfile2_AddStream(IWMProfile2* This,IWMStreamConfig *pConfig) {
    return This->lpVtbl->AddStream(This,pConfig);
}
static inline HRESULT IWMProfile2_ReconfigStream(IWMProfile2* This,IWMStreamConfig *pConfig) {
    return This->lpVtbl->ReconfigStream(This,pConfig);
}
static inline HRESULT IWMProfile2_CreateNewStream(IWMProfile2* This,REFGUID guidStreamType,IWMStreamConfig **ppConfig) {
    return This->lpVtbl->CreateNewStream(This,guidStreamType,ppConfig);
}
static inline HRESULT IWMProfile2_GetMutualExclusionCount(IWMProfile2* This,DWORD *pcME) {
    return This->lpVtbl->GetMutualExclusionCount(This,pcME);
}
static inline HRESULT IWMProfile2_GetMutualExclusion(IWMProfile2* This,DWORD dwMEIndex,IWMMutualExclusion **ppME) {
    return This->lpVtbl->GetMutualExclusion(This,dwMEIndex,ppME);
}
static inline HRESULT IWMProfile2_RemoveMutualExclusion(IWMProfile2* This,IWMMutualExclusion *pME) {
    return This->lpVtbl->RemoveMutualExclusion(This,pME);
}
static inline HRESULT IWMProfile2_AddMutualExclusion(IWMProfile2* This,IWMMutualExclusion *pME) {
    return This->lpVtbl->AddMutualExclusion(This,pME);
}
static inline HRESULT IWMProfile2_CreateNewMutualExclusion(IWMProfile2* This,IWMMutualExclusion **ppME) {
    return This->lpVtbl->CreateNewMutualExclusion(This,ppME);
}
/*** IWMProfile2 methods ***/
static inline HRESULT IWMProfile2_GetProfileID(IWMProfile2* This,GUID *guid) {
    return This->lpVtbl->GetProfileID(This,guid);
}
#endif
#endif

#endif


#endif  /* __IWMProfile2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMProfile3 interface
 */
#ifndef __IWMProfile3_INTERFACE_DEFINED__
#define __IWMProfile3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMProfile3, 0x00ef96cc, 0xa461, 0x4546, 0x8b,0xcd, 0xc9,0xa2,0x8f,0x0e,0x06,0xf5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00ef96cc-a461-4546-8bcd-c9a28f0e06f5")
IWMProfile3 : public IWMProfile2
{
    virtual HRESULT STDMETHODCALLTYPE GetStorageFormat(
        WMT_STORAGE_FORMAT *storage) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStorageFormat(
        WMT_STORAGE_FORMAT storage) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBandwidthSharingCount(
        DWORD *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBandwidthSharing(
        DWORD index,
        IWMBandwidthSharing **bandwidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveBandwidthSharing(
        IWMBandwidthSharing *bandwidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddBandwidthSharing(
        IWMBandwidthSharing *bandwidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateNewBandwidthSharing(
        IWMBandwidthSharing **bandwidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamPrioritization(
        IWMStreamPrioritization **stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamPrioritization(
        IWMStreamPrioritization *stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveStreamPrioritization(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateNewStreamPrioritization(
        IWMStreamPrioritization **stream) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExpectedPacketCount(
        QWORD duration,
        QWORD *packets) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMProfile3, 0x00ef96cc, 0xa461, 0x4546, 0x8b,0xcd, 0xc9,0xa2,0x8f,0x0e,0x06,0xf5)
#endif
#else
typedef struct IWMProfile3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMProfile3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMProfile3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMProfile3 *This);

    /*** IWMProfile methods ***/
    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IWMProfile3 *This,
        WMT_VERSION *pdwVersion);

    HRESULT (STDMETHODCALLTYPE *GetName)(
        IWMProfile3 *This,
        WCHAR *pwszName,
        DWORD *pcchName);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        IWMProfile3 *This,
        const WCHAR *pwszName);

    HRESULT (STDMETHODCALLTYPE *GetDescription)(
        IWMProfile3 *This,
        WCHAR *pwszDescription,
        DWORD *pcchDescription);

    HRESULT (STDMETHODCALLTYPE *SetDescription)(
        IWMProfile3 *This,
        const WCHAR *pwszDescription);

    HRESULT (STDMETHODCALLTYPE *GetStreamCount)(
        IWMProfile3 *This,
        DWORD *pcStreams);

    HRESULT (STDMETHODCALLTYPE *GetStream)(
        IWMProfile3 *This,
        DWORD dwStreamIndex,
        IWMStreamConfig **ppConfig);

    HRESULT (STDMETHODCALLTYPE *GetStreamByNumber)(
        IWMProfile3 *This,
        WORD wStreamNum,
        IWMStreamConfig **ppConfig);

    HRESULT (STDMETHODCALLTYPE *RemoveStream)(
        IWMProfile3 *This,
        IWMStreamConfig *pConfig);

    HRESULT (STDMETHODCALLTYPE *RemoveStreamByNumber)(
        IWMProfile3 *This,
        WORD wStreamNum);

    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IWMProfile3 *This,
        IWMStreamConfig *pConfig);

    HRESULT (STDMETHODCALLTYPE *ReconfigStream)(
        IWMProfile3 *This,
        IWMStreamConfig *pConfig);

    HRESULT (STDMETHODCALLTYPE *CreateNewStream)(
        IWMProfile3 *This,
        REFGUID guidStreamType,
        IWMStreamConfig **ppConfig);

    HRESULT (STDMETHODCALLTYPE *GetMutualExclusionCount)(
        IWMProfile3 *This,
        DWORD *pcME);

    HRESULT (STDMETHODCALLTYPE *GetMutualExclusion)(
        IWMProfile3 *This,
        DWORD dwMEIndex,
        IWMMutualExclusion **ppME);

    HRESULT (STDMETHODCALLTYPE *RemoveMutualExclusion)(
        IWMProfile3 *This,
        IWMMutualExclusion *pME);

    HRESULT (STDMETHODCALLTYPE *AddMutualExclusion)(
        IWMProfile3 *This,
        IWMMutualExclusion *pME);

    HRESULT (STDMETHODCALLTYPE *CreateNewMutualExclusion)(
        IWMProfile3 *This,
        IWMMutualExclusion **ppME);

    /*** IWMProfile2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProfileID)(
        IWMProfile3 *This,
        GUID *guid);

    /*** IWMProfile3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStorageFormat)(
        IWMProfile3 *This,
        WMT_STORAGE_FORMAT *storage);

    HRESULT (STDMETHODCALLTYPE *SetStorageFormat)(
        IWMProfile3 *This,
        WMT_STORAGE_FORMAT storage);

    HRESULT (STDMETHODCALLTYPE *GetBandwidthSharingCount)(
        IWMProfile3 *This,
        DWORD *count);

    HRESULT (STDMETHODCALLTYPE *GetBandwidthSharing)(
        IWMProfile3 *This,
        DWORD index,
        IWMBandwidthSharing **bandwidth);

    HRESULT (STDMETHODCALLTYPE *RemoveBandwidthSharing)(
        IWMProfile3 *This,
        IWMBandwidthSharing *bandwidth);

    HRESULT (STDMETHODCALLTYPE *AddBandwidthSharing)(
        IWMProfile3 *This,
        IWMBandwidthSharing *bandwidth);

    HRESULT (STDMETHODCALLTYPE *CreateNewBandwidthSharing)(
        IWMProfile3 *This,
        IWMBandwidthSharing **bandwidth);

    HRESULT (STDMETHODCALLTYPE *GetStreamPrioritization)(
        IWMProfile3 *This,
        IWMStreamPrioritization **stream);

    HRESULT (STDMETHODCALLTYPE *SetStreamPrioritization)(
        IWMProfile3 *This,
        IWMStreamPrioritization *stream);

    HRESULT (STDMETHODCALLTYPE *RemoveStreamPrioritization)(
        IWMProfile3 *This);

    HRESULT (STDMETHODCALLTYPE *CreateNewStreamPrioritization)(
        IWMProfile3 *This,
        IWMStreamPrioritization **stream);

    HRESULT (STDMETHODCALLTYPE *GetExpectedPacketCount)(
        IWMProfile3 *This,
        QWORD duration,
        QWORD *packets);

    END_INTERFACE
} IWMProfile3Vtbl;

interface IWMProfile3 {
    CONST_VTBL IWMProfile3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMProfile3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMProfile3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMProfile3_Release(This) (This)->lpVtbl->Release(This)
/*** IWMProfile methods ***/
#define IWMProfile3_GetVersion(This,pdwVersion) (This)->lpVtbl->GetVersion(This,pdwVersion)
#define IWMProfile3_GetName(This,pwszName,pcchName) (This)->lpVtbl->GetName(This,pwszName,pcchName)
#define IWMProfile3_SetName(This,pwszName) (This)->lpVtbl->SetName(This,pwszName)
#define IWMProfile3_GetDescription(This,pwszDescription,pcchDescription) (This)->lpVtbl->GetDescription(This,pwszDescription,pcchDescription)
#define IWMProfile3_SetDescription(This,pwszDescription) (This)->lpVtbl->SetDescription(This,pwszDescription)
#define IWMProfile3_GetStreamCount(This,pcStreams) (This)->lpVtbl->GetStreamCount(This,pcStreams)
#define IWMProfile3_GetStream(This,dwStreamIndex,ppConfig) (This)->lpVtbl->GetStream(This,dwStreamIndex,ppConfig)
#define IWMProfile3_GetStreamByNumber(This,wStreamNum,ppConfig) (This)->lpVtbl->GetStreamByNumber(This,wStreamNum,ppConfig)
#define IWMProfile3_RemoveStream(This,pConfig) (This)->lpVtbl->RemoveStream(This,pConfig)
#define IWMProfile3_RemoveStreamByNumber(This,wStreamNum) (This)->lpVtbl->RemoveStreamByNumber(This,wStreamNum)
#define IWMProfile3_AddStream(This,pConfig) (This)->lpVtbl->AddStream(This,pConfig)
#define IWMProfile3_ReconfigStream(This,pConfig) (This)->lpVtbl->ReconfigStream(This,pConfig)
#define IWMProfile3_CreateNewStream(This,guidStreamType,ppConfig) (This)->lpVtbl->CreateNewStream(This,guidStreamType,ppConfig)
#define IWMProfile3_GetMutualExclusionCount(This,pcME) (This)->lpVtbl->GetMutualExclusionCount(This,pcME)
#define IWMProfile3_GetMutualExclusion(This,dwMEIndex,ppME) (This)->lpVtbl->GetMutualExclusion(This,dwMEIndex,ppME)
#define IWMProfile3_RemoveMutualExclusion(This,pME) (This)->lpVtbl->RemoveMutualExclusion(This,pME)
#define IWMProfile3_AddMutualExclusion(This,pME) (This)->lpVtbl->AddMutualExclusion(This,pME)
#define IWMProfile3_CreateNewMutualExclusion(This,ppME) (This)->lpVtbl->CreateNewMutualExclusion(This,ppME)
/*** IWMProfile2 methods ***/
#define IWMProfile3_GetProfileID(This,guid) (This)->lpVtbl->GetProfileID(This,guid)
/*** IWMProfile3 methods ***/
#define IWMProfile3_GetStorageFormat(This,storage) (This)->lpVtbl->GetStorageFormat(This,storage)
#define IWMProfile3_SetStorageFormat(This,storage) (This)->lpVtbl->SetStorageFormat(This,storage)
#define IWMProfile3_GetBandwidthSharingCount(This,count) (This)->lpVtbl->GetBandwidthSharingCount(This,count)
#define IWMProfile3_GetBandwidthSharing(This,index,bandwidth) (This)->lpVtbl->GetBandwidthSharing(This,index,bandwidth)
#define IWMProfile3_RemoveBandwidthSharing(This,bandwidth) (This)->lpVtbl->RemoveBandwidthSharing(This,bandwidth)
#define IWMProfile3_AddBandwidthSharing(This,bandwidth) (This)->lpVtbl->AddBandwidthSharing(This,bandwidth)
#define IWMProfile3_CreateNewBandwidthSharing(This,bandwidth) (This)->lpVtbl->CreateNewBandwidthSharing(This,bandwidth)
#define IWMProfile3_GetStreamPrioritization(This,stream) (This)->lpVtbl->GetStreamPrioritization(This,stream)
#define IWMProfile3_SetStreamPrioritization(This,stream) (This)->lpVtbl->SetStreamPrioritization(This,stream)
#define IWMProfile3_RemoveStreamPrioritization(This) (This)->lpVtbl->RemoveStreamPrioritization(This)
#define IWMProfile3_CreateNewStreamPrioritization(This,stream) (This)->lpVtbl->CreateNewStreamPrioritization(This,stream)
#define IWMProfile3_GetExpectedPacketCount(This,duration,packets) (This)->lpVtbl->GetExpectedPacketCount(This,duration,packets)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMProfile3_QueryInterface(IWMProfile3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMProfile3_AddRef(IWMProfile3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMProfile3_Release(IWMProfile3* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMProfile methods ***/
static inline HRESULT IWMProfile3_GetVersion(IWMProfile3* This,WMT_VERSION *pdwVersion) {
    return This->lpVtbl->GetVersion(This,pdwVersion);
}
static inline HRESULT IWMProfile3_GetName(IWMProfile3* This,WCHAR *pwszName,DWORD *pcchName) {
    return This->lpVtbl->GetName(This,pwszName,pcchName);
}
static inline HRESULT IWMProfile3_SetName(IWMProfile3* This,const WCHAR *pwszName) {
    return This->lpVtbl->SetName(This,pwszName);
}
static inline HRESULT IWMProfile3_GetDescription(IWMProfile3* This,WCHAR *pwszDescription,DWORD *pcchDescription) {
    return This->lpVtbl->GetDescription(This,pwszDescription,pcchDescription);
}
static inline HRESULT IWMProfile3_SetDescription(IWMProfile3* This,const WCHAR *pwszDescription) {
    return This->lpVtbl->SetDescription(This,pwszDescription);
}
static inline HRESULT IWMProfile3_GetStreamCount(IWMProfile3* This,DWORD *pcStreams) {
    return This->lpVtbl->GetStreamCount(This,pcStreams);
}
static inline HRESULT IWMProfile3_GetStream(IWMProfile3* This,DWORD dwStreamIndex,IWMStreamConfig **ppConfig) {
    return This->lpVtbl->GetStream(This,dwStreamIndex,ppConfig);
}
static inline HRESULT IWMProfile3_GetStreamByNumber(IWMProfile3* This,WORD wStreamNum,IWMStreamConfig **ppConfig) {
    return This->lpVtbl->GetStreamByNumber(This,wStreamNum,ppConfig);
}
static inline HRESULT IWMProfile3_RemoveStream(IWMProfile3* This,IWMStreamConfig *pConfig) {
    return This->lpVtbl->RemoveStream(This,pConfig);
}
static inline HRESULT IWMProfile3_RemoveStreamByNumber(IWMProfile3* This,WORD wStreamNum) {
    return This->lpVtbl->RemoveStreamByNumber(This,wStreamNum);
}
static inline HRESULT IWMProfile3_AddStream(IWMProfile3* This,IWMStreamConfig *pConfig) {
    return This->lpVtbl->AddStream(This,pConfig);
}
static inline HRESULT IWMProfile3_ReconfigStream(IWMProfile3* This,IWMStreamConfig *pConfig) {
    return This->lpVtbl->ReconfigStream(This,pConfig);
}
static inline HRESULT IWMProfile3_CreateNewStream(IWMProfile3* This,REFGUID guidStreamType,IWMStreamConfig **ppConfig) {
    return This->lpVtbl->CreateNewStream(This,guidStreamType,ppConfig);
}
static inline HRESULT IWMProfile3_GetMutualExclusionCount(IWMProfile3* This,DWORD *pcME) {
    return This->lpVtbl->GetMutualExclusionCount(This,pcME);
}
static inline HRESULT IWMProfile3_GetMutualExclusion(IWMProfile3* This,DWORD dwMEIndex,IWMMutualExclusion **ppME) {
    return This->lpVtbl->GetMutualExclusion(This,dwMEIndex,ppME);
}
static inline HRESULT IWMProfile3_RemoveMutualExclusion(IWMProfile3* This,IWMMutualExclusion *pME) {
    return This->lpVtbl->RemoveMutualExclusion(This,pME);
}
static inline HRESULT IWMProfile3_AddMutualExclusion(IWMProfile3* This,IWMMutualExclusion *pME) {
    return This->lpVtbl->AddMutualExclusion(This,pME);
}
static inline HRESULT IWMProfile3_CreateNewMutualExclusion(IWMProfile3* This,IWMMutualExclusion **ppME) {
    return This->lpVtbl->CreateNewMutualExclusion(This,ppME);
}
/*** IWMProfile2 methods ***/
static inline HRESULT IWMProfile3_GetProfileID(IWMProfile3* This,GUID *guid) {
    return This->lpVtbl->GetProfileID(This,guid);
}
/*** IWMProfile3 methods ***/
static inline HRESULT IWMProfile3_GetStorageFormat(IWMProfile3* This,WMT_STORAGE_FORMAT *storage) {
    return This->lpVtbl->GetStorageFormat(This,storage);
}
static inline HRESULT IWMProfile3_SetStorageFormat(IWMProfile3* This,WMT_STORAGE_FORMAT storage) {
    return This->lpVtbl->SetStorageFormat(This,storage);
}
static inline HRESULT IWMProfile3_GetBandwidthSharingCount(IWMProfile3* This,DWORD *count) {
    return This->lpVtbl->GetBandwidthSharingCount(This,count);
}
static inline HRESULT IWMProfile3_GetBandwidthSharing(IWMProfile3* This,DWORD index,IWMBandwidthSharing **bandwidth) {
    return This->lpVtbl->GetBandwidthSharing(This,index,bandwidth);
}
static inline HRESULT IWMProfile3_RemoveBandwidthSharing(IWMProfile3* This,IWMBandwidthSharing *bandwidth) {
    return This->lpVtbl->RemoveBandwidthSharing(This,bandwidth);
}
static inline HRESULT IWMProfile3_AddBandwidthSharing(IWMProfile3* This,IWMBandwidthSharing *bandwidth) {
    return This->lpVtbl->AddBandwidthSharing(This,bandwidth);
}
static inline HRESULT IWMProfile3_CreateNewBandwidthSharing(IWMProfile3* This,IWMBandwidthSharing **bandwidth) {
    return This->lpVtbl->CreateNewBandwidthSharing(This,bandwidth);
}
static inline HRESULT IWMProfile3_GetStreamPrioritization(IWMProfile3* This,IWMStreamPrioritization **stream) {
    return This->lpVtbl->GetStreamPrioritization(This,stream);
}
static inline HRESULT IWMProfile3_SetStreamPrioritization(IWMProfile3* This,IWMStreamPrioritization *stream) {
    return This->lpVtbl->SetStreamPrioritization(This,stream);
}
static inline HRESULT IWMProfile3_RemoveStreamPrioritization(IWMProfile3* This) {
    return This->lpVtbl->RemoveStreamPrioritization(This);
}
static inline HRESULT IWMProfile3_CreateNewStreamPrioritization(IWMProfile3* This,IWMStreamPrioritization **stream) {
    return This->lpVtbl->CreateNewStreamPrioritization(This,stream);
}
static inline HRESULT IWMProfile3_GetExpectedPacketCount(IWMProfile3* This,QWORD duration,QWORD *packets) {
    return This->lpVtbl->GetExpectedPacketCount(This,duration,packets);
}
#endif
#endif

#endif


#endif  /* __IWMProfile3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMProfileManager interface
 */
#ifndef __IWMProfileManager_INTERFACE_DEFINED__
#define __IWMProfileManager_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMProfileManager, 0xd16679f2, 0x6ca0, 0x472d, 0x8d,0x31, 0x2f,0x5d,0x55,0xae,0xe1,0x55);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d16679f2-6ca0-472d-8d31-2f5d55aee155")
IWMProfileManager : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateEmptyProfile(
        WMT_VERSION dwVersion,
        IWMProfile **ppProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE LoadProfileByID(
        REFGUID guidProfile,
        IWMProfile **ppProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE LoadProfileByData(
        const WCHAR *pwszProfile,
        IWMProfile **ppProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveProfile(
        IWMProfile *pIWMProfile,
        WCHAR *pwszProfile,
        DWORD *pdwLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSystemProfileCount(
        DWORD *pcProfiles) = 0;

    virtual HRESULT STDMETHODCALLTYPE LoadSystemProfile(
        DWORD dwProfileIndex,
        IWMProfile **ppProfile) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMProfileManager, 0xd16679f2, 0x6ca0, 0x472d, 0x8d,0x31, 0x2f,0x5d,0x55,0xae,0xe1,0x55)
#endif
#else
typedef struct IWMProfileManagerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMProfileManager *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMProfileManager *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMProfileManager *This);

    /*** IWMProfileManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateEmptyProfile)(
        IWMProfileManager *This,
        WMT_VERSION dwVersion,
        IWMProfile **ppProfile);

    HRESULT (STDMETHODCALLTYPE *LoadProfileByID)(
        IWMProfileManager *This,
        REFGUID guidProfile,
        IWMProfile **ppProfile);

    HRESULT (STDMETHODCALLTYPE *LoadProfileByData)(
        IWMProfileManager *This,
        const WCHAR *pwszProfile,
        IWMProfile **ppProfile);

    HRESULT (STDMETHODCALLTYPE *SaveProfile)(
        IWMProfileManager *This,
        IWMProfile *pIWMProfile,
        WCHAR *pwszProfile,
        DWORD *pdwLength);

    HRESULT (STDMETHODCALLTYPE *GetSystemProfileCount)(
        IWMProfileManager *This,
        DWORD *pcProfiles);

    HRESULT (STDMETHODCALLTYPE *LoadSystemProfile)(
        IWMProfileManager *This,
        DWORD dwProfileIndex,
        IWMProfile **ppProfile);

    END_INTERFACE
} IWMProfileManagerVtbl;

interface IWMProfileManager {
    CONST_VTBL IWMProfileManagerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMProfileManager_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMProfileManager_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMProfileManager_Release(This) (This)->lpVtbl->Release(This)
/*** IWMProfileManager methods ***/
#define IWMProfileManager_CreateEmptyProfile(This,dwVersion,ppProfile) (This)->lpVtbl->CreateEmptyProfile(This,dwVersion,ppProfile)
#define IWMProfileManager_LoadProfileByID(This,guidProfile,ppProfile) (This)->lpVtbl->LoadProfileByID(This,guidProfile,ppProfile)
#define IWMProfileManager_LoadProfileByData(This,pwszProfile,ppProfile) (This)->lpVtbl->LoadProfileByData(This,pwszProfile,ppProfile)
#define IWMProfileManager_SaveProfile(This,pIWMProfile,pwszProfile,pdwLength) (This)->lpVtbl->SaveProfile(This,pIWMProfile,pwszProfile,pdwLength)
#define IWMProfileManager_GetSystemProfileCount(This,pcProfiles) (This)->lpVtbl->GetSystemProfileCount(This,pcProfiles)
#define IWMProfileManager_LoadSystemProfile(This,dwProfileIndex,ppProfile) (This)->lpVtbl->LoadSystemProfile(This,dwProfileIndex,ppProfile)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMProfileManager_QueryInterface(IWMProfileManager* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMProfileManager_AddRef(IWMProfileManager* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMProfileManager_Release(IWMProfileManager* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMProfileManager methods ***/
static inline HRESULT IWMProfileManager_CreateEmptyProfile(IWMProfileManager* This,WMT_VERSION dwVersion,IWMProfile **ppProfile) {
    return This->lpVtbl->CreateEmptyProfile(This,dwVersion,ppProfile);
}
static inline HRESULT IWMProfileManager_LoadProfileByID(IWMProfileManager* This,REFGUID guidProfile,IWMProfile **ppProfile) {
    return This->lpVtbl->LoadProfileByID(This,guidProfile,ppProfile);
}
static inline HRESULT IWMProfileManager_LoadProfileByData(IWMProfileManager* This,const WCHAR *pwszProfile,IWMProfile **ppProfile) {
    return This->lpVtbl->LoadProfileByData(This,pwszProfile,ppProfile);
}
static inline HRESULT IWMProfileManager_SaveProfile(IWMProfileManager* This,IWMProfile *pIWMProfile,WCHAR *pwszProfile,DWORD *pdwLength) {
    return This->lpVtbl->SaveProfile(This,pIWMProfile,pwszProfile,pdwLength);
}
static inline HRESULT IWMProfileManager_GetSystemProfileCount(IWMProfileManager* This,DWORD *pcProfiles) {
    return This->lpVtbl->GetSystemProfileCount(This,pcProfiles);
}
static inline HRESULT IWMProfileManager_LoadSystemProfile(IWMProfileManager* This,DWORD dwProfileIndex,IWMProfile **ppProfile) {
    return This->lpVtbl->LoadSystemProfile(This,dwProfileIndex,ppProfile);
}
#endif
#endif

#endif


#endif  /* __IWMProfileManager_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMProfileManager2 interface
 */
#ifndef __IWMProfileManager2_INTERFACE_DEFINED__
#define __IWMProfileManager2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMProfileManager2, 0x7a924e51, 0x73c1, 0x494d, 0x80,0x19, 0x23,0xd3,0x7e,0xd9,0xb8,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7a924e51-73c1-494d-8019-23d37ed9b89a")
IWMProfileManager2 : public IWMProfileManager
{
    virtual HRESULT STDMETHODCALLTYPE GetSystemProfileVersion(
        WMT_VERSION *version) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSystemProfileVersion(
        WMT_VERSION version) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMProfileManager2, 0x7a924e51, 0x73c1, 0x494d, 0x80,0x19, 0x23,0xd3,0x7e,0xd9,0xb8,0x9a)
#endif
#else
typedef struct IWMProfileManager2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMProfileManager2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMProfileManager2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMProfileManager2 *This);

    /*** IWMProfileManager methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateEmptyProfile)(
        IWMProfileManager2 *This,
        WMT_VERSION dwVersion,
        IWMProfile **ppProfile);

    HRESULT (STDMETHODCALLTYPE *LoadProfileByID)(
        IWMProfileManager2 *This,
        REFGUID guidProfile,
        IWMProfile **ppProfile);

    HRESULT (STDMETHODCALLTYPE *LoadProfileByData)(
        IWMProfileManager2 *This,
        const WCHAR *pwszProfile,
        IWMProfile **ppProfile);

    HRESULT (STDMETHODCALLTYPE *SaveProfile)(
        IWMProfileManager2 *This,
        IWMProfile *pIWMProfile,
        WCHAR *pwszProfile,
        DWORD *pdwLength);

    HRESULT (STDMETHODCALLTYPE *GetSystemProfileCount)(
        IWMProfileManager2 *This,
        DWORD *pcProfiles);

    HRESULT (STDMETHODCALLTYPE *LoadSystemProfile)(
        IWMProfileManager2 *This,
        DWORD dwProfileIndex,
        IWMProfile **ppProfile);

    /*** IWMProfileManager2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSystemProfileVersion)(
        IWMProfileManager2 *This,
        WMT_VERSION *version);

    HRESULT (STDMETHODCALLTYPE *SetSystemProfileVersion)(
        IWMProfileManager2 *This,
        WMT_VERSION version);

    END_INTERFACE
} IWMProfileManager2Vtbl;

interface IWMProfileManager2 {
    CONST_VTBL IWMProfileManager2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMProfileManager2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMProfileManager2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMProfileManager2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMProfileManager methods ***/
#define IWMProfileManager2_CreateEmptyProfile(This,dwVersion,ppProfile) (This)->lpVtbl->CreateEmptyProfile(This,dwVersion,ppProfile)
#define IWMProfileManager2_LoadProfileByID(This,guidProfile,ppProfile) (This)->lpVtbl->LoadProfileByID(This,guidProfile,ppProfile)
#define IWMProfileManager2_LoadProfileByData(This,pwszProfile,ppProfile) (This)->lpVtbl->LoadProfileByData(This,pwszProfile,ppProfile)
#define IWMProfileManager2_SaveProfile(This,pIWMProfile,pwszProfile,pdwLength) (This)->lpVtbl->SaveProfile(This,pIWMProfile,pwszProfile,pdwLength)
#define IWMProfileManager2_GetSystemProfileCount(This,pcProfiles) (This)->lpVtbl->GetSystemProfileCount(This,pcProfiles)
#define IWMProfileManager2_LoadSystemProfile(This,dwProfileIndex,ppProfile) (This)->lpVtbl->LoadSystemProfile(This,dwProfileIndex,ppProfile)
/*** IWMProfileManager2 methods ***/
#define IWMProfileManager2_GetSystemProfileVersion(This,version) (This)->lpVtbl->GetSystemProfileVersion(This,version)
#define IWMProfileManager2_SetSystemProfileVersion(This,version) (This)->lpVtbl->SetSystemProfileVersion(This,version)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMProfileManager2_QueryInterface(IWMProfileManager2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMProfileManager2_AddRef(IWMProfileManager2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMProfileManager2_Release(IWMProfileManager2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMProfileManager methods ***/
static inline HRESULT IWMProfileManager2_CreateEmptyProfile(IWMProfileManager2* This,WMT_VERSION dwVersion,IWMProfile **ppProfile) {
    return This->lpVtbl->CreateEmptyProfile(This,dwVersion,ppProfile);
}
static inline HRESULT IWMProfileManager2_LoadProfileByID(IWMProfileManager2* This,REFGUID guidProfile,IWMProfile **ppProfile) {
    return This->lpVtbl->LoadProfileByID(This,guidProfile,ppProfile);
}
static inline HRESULT IWMProfileManager2_LoadProfileByData(IWMProfileManager2* This,const WCHAR *pwszProfile,IWMProfile **ppProfile) {
    return This->lpVtbl->LoadProfileByData(This,pwszProfile,ppProfile);
}
static inline HRESULT IWMProfileManager2_SaveProfile(IWMProfileManager2* This,IWMProfile *pIWMProfile,WCHAR *pwszProfile,DWORD *pdwLength) {
    return This->lpVtbl->SaveProfile(This,pIWMProfile,pwszProfile,pdwLength);
}
static inline HRESULT IWMProfileManager2_GetSystemProfileCount(IWMProfileManager2* This,DWORD *pcProfiles) {
    return This->lpVtbl->GetSystemProfileCount(This,pcProfiles);
}
static inline HRESULT IWMProfileManager2_LoadSystemProfile(IWMProfileManager2* This,DWORD dwProfileIndex,IWMProfile **ppProfile) {
    return This->lpVtbl->LoadSystemProfile(This,dwProfileIndex,ppProfile);
}
/*** IWMProfileManager2 methods ***/
static inline HRESULT IWMProfileManager2_GetSystemProfileVersion(IWMProfileManager2* This,WMT_VERSION *version) {
    return This->lpVtbl->GetSystemProfileVersion(This,version);
}
static inline HRESULT IWMProfileManager2_SetSystemProfileVersion(IWMProfileManager2* This,WMT_VERSION version) {
    return This->lpVtbl->SetSystemProfileVersion(This,version);
}
#endif
#endif

#endif


#endif  /* __IWMProfileManager2_INTERFACE_DEFINED__ */

HRESULT WINAPI WMCreateProfileManager(IWMProfileManager**);
/*****************************************************************************
 * IWMCodecInfo interface
 */
#ifndef __IWMCodecInfo_INTERFACE_DEFINED__
#define __IWMCodecInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMCodecInfo, 0xa970f41e, 0x34de, 0x4a98, 0xb3,0xba, 0xe4,0xb3,0xca,0x75,0x28,0xf0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a970f41e-34de-4a98-b3ba-e4b3ca7528f0")
IWMCodecInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCodecInfoCount(
        REFGUID guid,
        DWORD *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodecFormatCount(
        REFGUID guid,
        DWORD codecindex,
        DWORD *formatcount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodecFormat(
        REFGUID guid,
        DWORD codecindex,
        DWORD formatindex,
        IWMStreamConfig **streamconfig) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMCodecInfo, 0xa970f41e, 0x34de, 0x4a98, 0xb3,0xba, 0xe4,0xb3,0xca,0x75,0x28,0xf0)
#endif
#else
typedef struct IWMCodecInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMCodecInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMCodecInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMCodecInfo *This);

    /*** IWMCodecInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCodecInfoCount)(
        IWMCodecInfo *This,
        REFGUID guid,
        DWORD *count);

    HRESULT (STDMETHODCALLTYPE *GetCodecFormatCount)(
        IWMCodecInfo *This,
        REFGUID guid,
        DWORD codecindex,
        DWORD *formatcount);

    HRESULT (STDMETHODCALLTYPE *GetCodecFormat)(
        IWMCodecInfo *This,
        REFGUID guid,
        DWORD codecindex,
        DWORD formatindex,
        IWMStreamConfig **streamconfig);

    END_INTERFACE
} IWMCodecInfoVtbl;

interface IWMCodecInfo {
    CONST_VTBL IWMCodecInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMCodecInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMCodecInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMCodecInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWMCodecInfo methods ***/
#define IWMCodecInfo_GetCodecInfoCount(This,guid,count) (This)->lpVtbl->GetCodecInfoCount(This,guid,count)
#define IWMCodecInfo_GetCodecFormatCount(This,guid,codecindex,formatcount) (This)->lpVtbl->GetCodecFormatCount(This,guid,codecindex,formatcount)
#define IWMCodecInfo_GetCodecFormat(This,guid,codecindex,formatindex,streamconfig) (This)->lpVtbl->GetCodecFormat(This,guid,codecindex,formatindex,streamconfig)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMCodecInfo_QueryInterface(IWMCodecInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMCodecInfo_AddRef(IWMCodecInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMCodecInfo_Release(IWMCodecInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMCodecInfo methods ***/
static inline HRESULT IWMCodecInfo_GetCodecInfoCount(IWMCodecInfo* This,REFGUID guid,DWORD *count) {
    return This->lpVtbl->GetCodecInfoCount(This,guid,count);
}
static inline HRESULT IWMCodecInfo_GetCodecFormatCount(IWMCodecInfo* This,REFGUID guid,DWORD codecindex,DWORD *formatcount) {
    return This->lpVtbl->GetCodecFormatCount(This,guid,codecindex,formatcount);
}
static inline HRESULT IWMCodecInfo_GetCodecFormat(IWMCodecInfo* This,REFGUID guid,DWORD codecindex,DWORD formatindex,IWMStreamConfig **streamconfig) {
    return This->lpVtbl->GetCodecFormat(This,guid,codecindex,formatindex,streamconfig);
}
#endif
#endif

#endif


#endif  /* __IWMCodecInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMCodecInfo2 interface
 */
#ifndef __IWMCodecInfo2_INTERFACE_DEFINED__
#define __IWMCodecInfo2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMCodecInfo2, 0xaa65e273, 0xb686, 0x4056, 0x91,0xec, 0xdd,0x76,0x8d,0x4d,0xf7,0x10);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aa65e273-b686-4056-91ec-dd768d4df710")
IWMCodecInfo2 : public IWMCodecInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetCodecName(
        REFGUID guid,
        DWORD codecindex,
        WCHAR *name,
        DWORD *namesize) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodecFormatDesc(
        REFGUID guid,
        DWORD codecindex,
        DWORD formatindex,
        IWMStreamConfig **streamconfig,
        WCHAR *description,
        DWORD *descrsize) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMCodecInfo2, 0xaa65e273, 0xb686, 0x4056, 0x91,0xec, 0xdd,0x76,0x8d,0x4d,0xf7,0x10)
#endif
#else
typedef struct IWMCodecInfo2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMCodecInfo2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMCodecInfo2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMCodecInfo2 *This);

    /*** IWMCodecInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCodecInfoCount)(
        IWMCodecInfo2 *This,
        REFGUID guid,
        DWORD *count);

    HRESULT (STDMETHODCALLTYPE *GetCodecFormatCount)(
        IWMCodecInfo2 *This,
        REFGUID guid,
        DWORD codecindex,
        DWORD *formatcount);

    HRESULT (STDMETHODCALLTYPE *GetCodecFormat)(
        IWMCodecInfo2 *This,
        REFGUID guid,
        DWORD codecindex,
        DWORD formatindex,
        IWMStreamConfig **streamconfig);

    /*** IWMCodecInfo2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCodecName)(
        IWMCodecInfo2 *This,
        REFGUID guid,
        DWORD codecindex,
        WCHAR *name,
        DWORD *namesize);

    HRESULT (STDMETHODCALLTYPE *GetCodecFormatDesc)(
        IWMCodecInfo2 *This,
        REFGUID guid,
        DWORD codecindex,
        DWORD formatindex,
        IWMStreamConfig **streamconfig,
        WCHAR *description,
        DWORD *descrsize);

    END_INTERFACE
} IWMCodecInfo2Vtbl;

interface IWMCodecInfo2 {
    CONST_VTBL IWMCodecInfo2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMCodecInfo2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMCodecInfo2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMCodecInfo2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMCodecInfo methods ***/
#define IWMCodecInfo2_GetCodecInfoCount(This,guid,count) (This)->lpVtbl->GetCodecInfoCount(This,guid,count)
#define IWMCodecInfo2_GetCodecFormatCount(This,guid,codecindex,formatcount) (This)->lpVtbl->GetCodecFormatCount(This,guid,codecindex,formatcount)
#define IWMCodecInfo2_GetCodecFormat(This,guid,codecindex,formatindex,streamconfig) (This)->lpVtbl->GetCodecFormat(This,guid,codecindex,formatindex,streamconfig)
/*** IWMCodecInfo2 methods ***/
#define IWMCodecInfo2_GetCodecName(This,guid,codecindex,name,namesize) (This)->lpVtbl->GetCodecName(This,guid,codecindex,name,namesize)
#define IWMCodecInfo2_GetCodecFormatDesc(This,guid,codecindex,formatindex,streamconfig,description,descrsize) (This)->lpVtbl->GetCodecFormatDesc(This,guid,codecindex,formatindex,streamconfig,description,descrsize)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMCodecInfo2_QueryInterface(IWMCodecInfo2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMCodecInfo2_AddRef(IWMCodecInfo2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMCodecInfo2_Release(IWMCodecInfo2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMCodecInfo methods ***/
static inline HRESULT IWMCodecInfo2_GetCodecInfoCount(IWMCodecInfo2* This,REFGUID guid,DWORD *count) {
    return This->lpVtbl->GetCodecInfoCount(This,guid,count);
}
static inline HRESULT IWMCodecInfo2_GetCodecFormatCount(IWMCodecInfo2* This,REFGUID guid,DWORD codecindex,DWORD *formatcount) {
    return This->lpVtbl->GetCodecFormatCount(This,guid,codecindex,formatcount);
}
static inline HRESULT IWMCodecInfo2_GetCodecFormat(IWMCodecInfo2* This,REFGUID guid,DWORD codecindex,DWORD formatindex,IWMStreamConfig **streamconfig) {
    return This->lpVtbl->GetCodecFormat(This,guid,codecindex,formatindex,streamconfig);
}
/*** IWMCodecInfo2 methods ***/
static inline HRESULT IWMCodecInfo2_GetCodecName(IWMCodecInfo2* This,REFGUID guid,DWORD codecindex,WCHAR *name,DWORD *namesize) {
    return This->lpVtbl->GetCodecName(This,guid,codecindex,name,namesize);
}
static inline HRESULT IWMCodecInfo2_GetCodecFormatDesc(IWMCodecInfo2* This,REFGUID guid,DWORD codecindex,DWORD formatindex,IWMStreamConfig **streamconfig,WCHAR *description,DWORD *descrsize) {
    return This->lpVtbl->GetCodecFormatDesc(This,guid,codecindex,formatindex,streamconfig,description,descrsize);
}
#endif
#endif

#endif


#endif  /* __IWMCodecInfo2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMCodecInfo3 interface
 */
#ifndef __IWMCodecInfo3_INTERFACE_DEFINED__
#define __IWMCodecInfo3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMCodecInfo3, 0x7e51f487, 0x4d93, 0x4f98, 0x8a,0xb4, 0x27,0xd0,0x56,0x5a,0xdc,0x51);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7e51f487-4d93-4f98-8ab4-27d0565adc51")
IWMCodecInfo3 : public IWMCodecInfo2
{
    virtual HRESULT STDMETHODCALLTYPE GetCodecFormatProp(
        REFGUID guid,
        DWORD codecindex,
        DWORD formatindex,
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodecProp(
        REFGUID guid,
        DWORD codecindex,
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCodecEnumerationSetting(
        REFGUID guid,
        DWORD codecindex,
        const WCHAR *name,
        WMT_ATTR_DATATYPE type,
        const BYTE *value,
        DWORD size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodecEnumerationSetting(
        REFGUID guid,
        DWORD codecindex,
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMCodecInfo3, 0x7e51f487, 0x4d93, 0x4f98, 0x8a,0xb4, 0x27,0xd0,0x56,0x5a,0xdc,0x51)
#endif
#else
typedef struct IWMCodecInfo3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMCodecInfo3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMCodecInfo3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMCodecInfo3 *This);

    /*** IWMCodecInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCodecInfoCount)(
        IWMCodecInfo3 *This,
        REFGUID guid,
        DWORD *count);

    HRESULT (STDMETHODCALLTYPE *GetCodecFormatCount)(
        IWMCodecInfo3 *This,
        REFGUID guid,
        DWORD codecindex,
        DWORD *formatcount);

    HRESULT (STDMETHODCALLTYPE *GetCodecFormat)(
        IWMCodecInfo3 *This,
        REFGUID guid,
        DWORD codecindex,
        DWORD formatindex,
        IWMStreamConfig **streamconfig);

    /*** IWMCodecInfo2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCodecName)(
        IWMCodecInfo3 *This,
        REFGUID guid,
        DWORD codecindex,
        WCHAR *name,
        DWORD *namesize);

    HRESULT (STDMETHODCALLTYPE *GetCodecFormatDesc)(
        IWMCodecInfo3 *This,
        REFGUID guid,
        DWORD codecindex,
        DWORD formatindex,
        IWMStreamConfig **streamconfig,
        WCHAR *description,
        DWORD *descrsize);

    /*** IWMCodecInfo3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCodecFormatProp)(
        IWMCodecInfo3 *This,
        REFGUID guid,
        DWORD codecindex,
        DWORD formatindex,
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *GetCodecProp)(
        IWMCodecInfo3 *This,
        REFGUID guid,
        DWORD codecindex,
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *SetCodecEnumerationSetting)(
        IWMCodecInfo3 *This,
        REFGUID guid,
        DWORD codecindex,
        const WCHAR *name,
        WMT_ATTR_DATATYPE type,
        const BYTE *value,
        DWORD size);

    HRESULT (STDMETHODCALLTYPE *GetCodecEnumerationSetting)(
        IWMCodecInfo3 *This,
        REFGUID guid,
        DWORD codecindex,
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size);

    END_INTERFACE
} IWMCodecInfo3Vtbl;

interface IWMCodecInfo3 {
    CONST_VTBL IWMCodecInfo3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMCodecInfo3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMCodecInfo3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMCodecInfo3_Release(This) (This)->lpVtbl->Release(This)
/*** IWMCodecInfo methods ***/
#define IWMCodecInfo3_GetCodecInfoCount(This,guid,count) (This)->lpVtbl->GetCodecInfoCount(This,guid,count)
#define IWMCodecInfo3_GetCodecFormatCount(This,guid,codecindex,formatcount) (This)->lpVtbl->GetCodecFormatCount(This,guid,codecindex,formatcount)
#define IWMCodecInfo3_GetCodecFormat(This,guid,codecindex,formatindex,streamconfig) (This)->lpVtbl->GetCodecFormat(This,guid,codecindex,formatindex,streamconfig)
/*** IWMCodecInfo2 methods ***/
#define IWMCodecInfo3_GetCodecName(This,guid,codecindex,name,namesize) (This)->lpVtbl->GetCodecName(This,guid,codecindex,name,namesize)
#define IWMCodecInfo3_GetCodecFormatDesc(This,guid,codecindex,formatindex,streamconfig,description,descrsize) (This)->lpVtbl->GetCodecFormatDesc(This,guid,codecindex,formatindex,streamconfig,description,descrsize)
/*** IWMCodecInfo3 methods ***/
#define IWMCodecInfo3_GetCodecFormatProp(This,guid,codecindex,formatindex,name,type,value,size) (This)->lpVtbl->GetCodecFormatProp(This,guid,codecindex,formatindex,name,type,value,size)
#define IWMCodecInfo3_GetCodecProp(This,guid,codecindex,name,type,value,size) (This)->lpVtbl->GetCodecProp(This,guid,codecindex,name,type,value,size)
#define IWMCodecInfo3_SetCodecEnumerationSetting(This,guid,codecindex,name,type,value,size) (This)->lpVtbl->SetCodecEnumerationSetting(This,guid,codecindex,name,type,value,size)
#define IWMCodecInfo3_GetCodecEnumerationSetting(This,guid,codecindex,name,type,value,size) (This)->lpVtbl->GetCodecEnumerationSetting(This,guid,codecindex,name,type,value,size)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMCodecInfo3_QueryInterface(IWMCodecInfo3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMCodecInfo3_AddRef(IWMCodecInfo3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMCodecInfo3_Release(IWMCodecInfo3* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMCodecInfo methods ***/
static inline HRESULT IWMCodecInfo3_GetCodecInfoCount(IWMCodecInfo3* This,REFGUID guid,DWORD *count) {
    return This->lpVtbl->GetCodecInfoCount(This,guid,count);
}
static inline HRESULT IWMCodecInfo3_GetCodecFormatCount(IWMCodecInfo3* This,REFGUID guid,DWORD codecindex,DWORD *formatcount) {
    return This->lpVtbl->GetCodecFormatCount(This,guid,codecindex,formatcount);
}
static inline HRESULT IWMCodecInfo3_GetCodecFormat(IWMCodecInfo3* This,REFGUID guid,DWORD codecindex,DWORD formatindex,IWMStreamConfig **streamconfig) {
    return This->lpVtbl->GetCodecFormat(This,guid,codecindex,formatindex,streamconfig);
}
/*** IWMCodecInfo2 methods ***/
static inline HRESULT IWMCodecInfo3_GetCodecName(IWMCodecInfo3* This,REFGUID guid,DWORD codecindex,WCHAR *name,DWORD *namesize) {
    return This->lpVtbl->GetCodecName(This,guid,codecindex,name,namesize);
}
static inline HRESULT IWMCodecInfo3_GetCodecFormatDesc(IWMCodecInfo3* This,REFGUID guid,DWORD codecindex,DWORD formatindex,IWMStreamConfig **streamconfig,WCHAR *description,DWORD *descrsize) {
    return This->lpVtbl->GetCodecFormatDesc(This,guid,codecindex,formatindex,streamconfig,description,descrsize);
}
/*** IWMCodecInfo3 methods ***/
static inline HRESULT IWMCodecInfo3_GetCodecFormatProp(IWMCodecInfo3* This,REFGUID guid,DWORD codecindex,DWORD formatindex,const WCHAR *name,WMT_ATTR_DATATYPE *type,BYTE *value,DWORD *size) {
    return This->lpVtbl->GetCodecFormatProp(This,guid,codecindex,formatindex,name,type,value,size);
}
static inline HRESULT IWMCodecInfo3_GetCodecProp(IWMCodecInfo3* This,REFGUID guid,DWORD codecindex,const WCHAR *name,WMT_ATTR_DATATYPE *type,BYTE *value,DWORD *size) {
    return This->lpVtbl->GetCodecProp(This,guid,codecindex,name,type,value,size);
}
static inline HRESULT IWMCodecInfo3_SetCodecEnumerationSetting(IWMCodecInfo3* This,REFGUID guid,DWORD codecindex,const WCHAR *name,WMT_ATTR_DATATYPE type,const BYTE *value,DWORD size) {
    return This->lpVtbl->SetCodecEnumerationSetting(This,guid,codecindex,name,type,value,size);
}
static inline HRESULT IWMCodecInfo3_GetCodecEnumerationSetting(IWMCodecInfo3* This,REFGUID guid,DWORD codecindex,const WCHAR *name,WMT_ATTR_DATATYPE *type,BYTE *value,DWORD *size) {
    return This->lpVtbl->GetCodecEnumerationSetting(This,guid,codecindex,name,type,value,size);
}
#endif
#endif

#endif


#endif  /* __IWMCodecInfo3_INTERFACE_DEFINED__ */

static const WCHAR g_wszNumPasses[] = {'_','P','A','S','S','E','S','U','S','E','D',0};
static const WCHAR g_wszVBREnabled[] = {'_','V','B','R','E','N','A','B','L','E','D',0};
/*****************************************************************************
 * IWMMediaProps interface
 */
#ifndef __IWMMediaProps_INTERFACE_DEFINED__
#define __IWMMediaProps_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMMediaProps, 0x96406bce, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bce-2b2b-11d3-b36b-00c04f6108ff")
IWMMediaProps : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetType(
        GUID *pguidType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaType(
        WM_MEDIA_TYPE *pType,
        DWORD *pcbType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaType(
        WM_MEDIA_TYPE *pType) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMMediaProps, 0x96406bce, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMMediaPropsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMMediaProps *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMMediaProps *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMMediaProps *This);

    /*** IWMMediaProps methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        IWMMediaProps *This,
        GUID *pguidType);

    HRESULT (STDMETHODCALLTYPE *GetMediaType)(
        IWMMediaProps *This,
        WM_MEDIA_TYPE *pType,
        DWORD *pcbType);

    HRESULT (STDMETHODCALLTYPE *SetMediaType)(
        IWMMediaProps *This,
        WM_MEDIA_TYPE *pType);

    END_INTERFACE
} IWMMediaPropsVtbl;

interface IWMMediaProps {
    CONST_VTBL IWMMediaPropsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMMediaProps_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMMediaProps_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMMediaProps_Release(This) (This)->lpVtbl->Release(This)
/*** IWMMediaProps methods ***/
#define IWMMediaProps_GetType(This,pguidType) (This)->lpVtbl->GetType(This,pguidType)
#define IWMMediaProps_GetMediaType(This,pType,pcbType) (This)->lpVtbl->GetMediaType(This,pType,pcbType)
#define IWMMediaProps_SetMediaType(This,pType) (This)->lpVtbl->SetMediaType(This,pType)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMMediaProps_QueryInterface(IWMMediaProps* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMMediaProps_AddRef(IWMMediaProps* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMMediaProps_Release(IWMMediaProps* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMMediaProps methods ***/
static inline HRESULT IWMMediaProps_GetType(IWMMediaProps* This,GUID *pguidType) {
    return This->lpVtbl->GetType(This,pguidType);
}
static inline HRESULT IWMMediaProps_GetMediaType(IWMMediaProps* This,WM_MEDIA_TYPE *pType,DWORD *pcbType) {
    return This->lpVtbl->GetMediaType(This,pType,pcbType);
}
static inline HRESULT IWMMediaProps_SetMediaType(IWMMediaProps* This,WM_MEDIA_TYPE *pType) {
    return This->lpVtbl->SetMediaType(This,pType);
}
#endif
#endif

#endif


#endif  /* __IWMMediaProps_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMOutputMediaProps interface
 */
#ifndef __IWMOutputMediaProps_INTERFACE_DEFINED__
#define __IWMOutputMediaProps_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMOutputMediaProps, 0x96406bd7, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bd7-2b2b-11d3-b36b-00c04f6108ff")
IWMOutputMediaProps : public IWMMediaProps
{
    virtual HRESULT STDMETHODCALLTYPE GetStreamGroupName(
        WCHAR *pwszName,
        WORD *pcchName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectionName(
        WCHAR *pwszName,
        WORD *pcchName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMOutputMediaProps, 0x96406bd7, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMOutputMediaPropsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMOutputMediaProps *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMOutputMediaProps *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMOutputMediaProps *This);

    /*** IWMMediaProps methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        IWMOutputMediaProps *This,
        GUID *pguidType);

    HRESULT (STDMETHODCALLTYPE *GetMediaType)(
        IWMOutputMediaProps *This,
        WM_MEDIA_TYPE *pType,
        DWORD *pcbType);

    HRESULT (STDMETHODCALLTYPE *SetMediaType)(
        IWMOutputMediaProps *This,
        WM_MEDIA_TYPE *pType);

    /*** IWMOutputMediaProps methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStreamGroupName)(
        IWMOutputMediaProps *This,
        WCHAR *pwszName,
        WORD *pcchName);

    HRESULT (STDMETHODCALLTYPE *GetConnectionName)(
        IWMOutputMediaProps *This,
        WCHAR *pwszName,
        WORD *pcchName);

    END_INTERFACE
} IWMOutputMediaPropsVtbl;

interface IWMOutputMediaProps {
    CONST_VTBL IWMOutputMediaPropsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMOutputMediaProps_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMOutputMediaProps_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMOutputMediaProps_Release(This) (This)->lpVtbl->Release(This)
/*** IWMMediaProps methods ***/
#define IWMOutputMediaProps_GetType(This,pguidType) (This)->lpVtbl->GetType(This,pguidType)
#define IWMOutputMediaProps_GetMediaType(This,pType,pcbType) (This)->lpVtbl->GetMediaType(This,pType,pcbType)
#define IWMOutputMediaProps_SetMediaType(This,pType) (This)->lpVtbl->SetMediaType(This,pType)
/*** IWMOutputMediaProps methods ***/
#define IWMOutputMediaProps_GetStreamGroupName(This,pwszName,pcchName) (This)->lpVtbl->GetStreamGroupName(This,pwszName,pcchName)
#define IWMOutputMediaProps_GetConnectionName(This,pwszName,pcchName) (This)->lpVtbl->GetConnectionName(This,pwszName,pcchName)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMOutputMediaProps_QueryInterface(IWMOutputMediaProps* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMOutputMediaProps_AddRef(IWMOutputMediaProps* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMOutputMediaProps_Release(IWMOutputMediaProps* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMMediaProps methods ***/
static inline HRESULT IWMOutputMediaProps_GetType(IWMOutputMediaProps* This,GUID *pguidType) {
    return This->lpVtbl->GetType(This,pguidType);
}
static inline HRESULT IWMOutputMediaProps_GetMediaType(IWMOutputMediaProps* This,WM_MEDIA_TYPE *pType,DWORD *pcbType) {
    return This->lpVtbl->GetMediaType(This,pType,pcbType);
}
static inline HRESULT IWMOutputMediaProps_SetMediaType(IWMOutputMediaProps* This,WM_MEDIA_TYPE *pType) {
    return This->lpVtbl->SetMediaType(This,pType);
}
/*** IWMOutputMediaProps methods ***/
static inline HRESULT IWMOutputMediaProps_GetStreamGroupName(IWMOutputMediaProps* This,WCHAR *pwszName,WORD *pcchName) {
    return This->lpVtbl->GetStreamGroupName(This,pwszName,pcchName);
}
static inline HRESULT IWMOutputMediaProps_GetConnectionName(IWMOutputMediaProps* This,WCHAR *pwszName,WORD *pcchName) {
    return This->lpVtbl->GetConnectionName(This,pwszName,pcchName);
}
#endif
#endif

#endif


#endif  /* __IWMOutputMediaProps_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMMetadataEditor interface
 */
#ifndef __IWMMetadataEditor_INTERFACE_DEFINED__
#define __IWMMetadataEditor_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMMetadataEditor, 0x96406bd9, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bd9-2b2b-11d3-b36b-00c04f6108ff")
IWMMetadataEditor : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Open(
        const WCHAR *pwszFilename) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Flush(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMMetadataEditor, 0x96406bd9, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMMetadataEditorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMMetadataEditor *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMMetadataEditor *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMMetadataEditor *This);

    /*** IWMMetadataEditor methods ***/
    HRESULT (STDMETHODCALLTYPE *Open)(
        IWMMetadataEditor *This,
        const WCHAR *pwszFilename);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IWMMetadataEditor *This);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        IWMMetadataEditor *This);

    END_INTERFACE
} IWMMetadataEditorVtbl;

interface IWMMetadataEditor {
    CONST_VTBL IWMMetadataEditorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMMetadataEditor_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMMetadataEditor_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMMetadataEditor_Release(This) (This)->lpVtbl->Release(This)
/*** IWMMetadataEditor methods ***/
#define IWMMetadataEditor_Open(This,pwszFilename) (This)->lpVtbl->Open(This,pwszFilename)
#define IWMMetadataEditor_Close(This) (This)->lpVtbl->Close(This)
#define IWMMetadataEditor_Flush(This) (This)->lpVtbl->Flush(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMMetadataEditor_QueryInterface(IWMMetadataEditor* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMMetadataEditor_AddRef(IWMMetadataEditor* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMMetadataEditor_Release(IWMMetadataEditor* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMMetadataEditor methods ***/
static inline HRESULT IWMMetadataEditor_Open(IWMMetadataEditor* This,const WCHAR *pwszFilename) {
    return This->lpVtbl->Open(This,pwszFilename);
}
static inline HRESULT IWMMetadataEditor_Close(IWMMetadataEditor* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IWMMetadataEditor_Flush(IWMMetadataEditor* This) {
    return This->lpVtbl->Flush(This);
}
#endif
#endif

#endif


#endif  /* __IWMMetadataEditor_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReader interface
 */
#ifndef __IWMReader_INTERFACE_DEFINED__
#define __IWMReader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReader, 0x96406bd6, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bd6-2b2b-11d3-b36b-00c04f6108ff")
IWMReader : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Open(
        const WCHAR *pwszURL,
        IWMReaderCallback *pCallback,
        void *pvContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputCount(
        DWORD *pcOutputs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputProps(
        DWORD dwOutputNum,
        IWMOutputMediaProps **ppOutput) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputProps(
        DWORD dwOutputNum,
        IWMOutputMediaProps *pOutput) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputFormatCount(
        DWORD dwOutputNumber,
        DWORD *pcFormats) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputFormat(
        DWORD dwOutputNumber,
        DWORD dwFormatNumber,
        IWMOutputMediaProps **ppProps) = 0;

    virtual HRESULT STDMETHODCALLTYPE Start(
        QWORD cnsStart,
        QWORD cnsDuration,
        float fRate,
        void *pvContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE Stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Resume(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReader, 0x96406bd6, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMReaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReader *This);

    /*** IWMReader methods ***/
    HRESULT (STDMETHODCALLTYPE *Open)(
        IWMReader *This,
        const WCHAR *pwszURL,
        IWMReaderCallback *pCallback,
        void *pvContext);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IWMReader *This);

    HRESULT (STDMETHODCALLTYPE *GetOutputCount)(
        IWMReader *This,
        DWORD *pcOutputs);

    HRESULT (STDMETHODCALLTYPE *GetOutputProps)(
        IWMReader *This,
        DWORD dwOutputNum,
        IWMOutputMediaProps **ppOutput);

    HRESULT (STDMETHODCALLTYPE *SetOutputProps)(
        IWMReader *This,
        DWORD dwOutputNum,
        IWMOutputMediaProps *pOutput);

    HRESULT (STDMETHODCALLTYPE *GetOutputFormatCount)(
        IWMReader *This,
        DWORD dwOutputNumber,
        DWORD *pcFormats);

    HRESULT (STDMETHODCALLTYPE *GetOutputFormat)(
        IWMReader *This,
        DWORD dwOutputNumber,
        DWORD dwFormatNumber,
        IWMOutputMediaProps **ppProps);

    HRESULT (STDMETHODCALLTYPE *Start)(
        IWMReader *This,
        QWORD cnsStart,
        QWORD cnsDuration,
        float fRate,
        void *pvContext);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        IWMReader *This);

    HRESULT (STDMETHODCALLTYPE *Pause)(
        IWMReader *This);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        IWMReader *This);

    END_INTERFACE
} IWMReaderVtbl;

interface IWMReader {
    CONST_VTBL IWMReaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReader_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReader methods ***/
#define IWMReader_Open(This,pwszURL,pCallback,pvContext) (This)->lpVtbl->Open(This,pwszURL,pCallback,pvContext)
#define IWMReader_Close(This) (This)->lpVtbl->Close(This)
#define IWMReader_GetOutputCount(This,pcOutputs) (This)->lpVtbl->GetOutputCount(This,pcOutputs)
#define IWMReader_GetOutputProps(This,dwOutputNum,ppOutput) (This)->lpVtbl->GetOutputProps(This,dwOutputNum,ppOutput)
#define IWMReader_SetOutputProps(This,dwOutputNum,pOutput) (This)->lpVtbl->SetOutputProps(This,dwOutputNum,pOutput)
#define IWMReader_GetOutputFormatCount(This,dwOutputNumber,pcFormats) (This)->lpVtbl->GetOutputFormatCount(This,dwOutputNumber,pcFormats)
#define IWMReader_GetOutputFormat(This,dwOutputNumber,dwFormatNumber,ppProps) (This)->lpVtbl->GetOutputFormat(This,dwOutputNumber,dwFormatNumber,ppProps)
#define IWMReader_Start(This,cnsStart,cnsDuration,fRate,pvContext) (This)->lpVtbl->Start(This,cnsStart,cnsDuration,fRate,pvContext)
#define IWMReader_Stop(This) (This)->lpVtbl->Stop(This)
#define IWMReader_Pause(This) (This)->lpVtbl->Pause(This)
#define IWMReader_Resume(This) (This)->lpVtbl->Resume(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReader_QueryInterface(IWMReader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReader_AddRef(IWMReader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReader_Release(IWMReader* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReader methods ***/
static inline HRESULT IWMReader_Open(IWMReader* This,const WCHAR *pwszURL,IWMReaderCallback *pCallback,void *pvContext) {
    return This->lpVtbl->Open(This,pwszURL,pCallback,pvContext);
}
static inline HRESULT IWMReader_Close(IWMReader* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IWMReader_GetOutputCount(IWMReader* This,DWORD *pcOutputs) {
    return This->lpVtbl->GetOutputCount(This,pcOutputs);
}
static inline HRESULT IWMReader_GetOutputProps(IWMReader* This,DWORD dwOutputNum,IWMOutputMediaProps **ppOutput) {
    return This->lpVtbl->GetOutputProps(This,dwOutputNum,ppOutput);
}
static inline HRESULT IWMReader_SetOutputProps(IWMReader* This,DWORD dwOutputNum,IWMOutputMediaProps *pOutput) {
    return This->lpVtbl->SetOutputProps(This,dwOutputNum,pOutput);
}
static inline HRESULT IWMReader_GetOutputFormatCount(IWMReader* This,DWORD dwOutputNumber,DWORD *pcFormats) {
    return This->lpVtbl->GetOutputFormatCount(This,dwOutputNumber,pcFormats);
}
static inline HRESULT IWMReader_GetOutputFormat(IWMReader* This,DWORD dwOutputNumber,DWORD dwFormatNumber,IWMOutputMediaProps **ppProps) {
    return This->lpVtbl->GetOutputFormat(This,dwOutputNumber,dwFormatNumber,ppProps);
}
static inline HRESULT IWMReader_Start(IWMReader* This,QWORD cnsStart,QWORD cnsDuration,float fRate,void *pvContext) {
    return This->lpVtbl->Start(This,cnsStart,cnsDuration,fRate,pvContext);
}
static inline HRESULT IWMReader_Stop(IWMReader* This) {
    return This->lpVtbl->Stop(This);
}
static inline HRESULT IWMReader_Pause(IWMReader* This) {
    return This->lpVtbl->Pause(This);
}
static inline HRESULT IWMReader_Resume(IWMReader* This) {
    return This->lpVtbl->Resume(This);
}
#endif
#endif

#endif


#endif  /* __IWMReader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPlayerHook interface
 */
#ifndef __IWMPlayerHook_INTERFACE_DEFINED__
#define __IWMPlayerHook_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPlayerHook, 0xe5b7ca9a, 0x0f1c, 0x4f66, 0x90,0x02, 0x74,0xec,0x50,0xd8,0xb3,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e5b7ca9a-0f1c-4f66-9002-74ec50d8b304")
IWMPlayerHook : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE PreDecode(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPlayerHook, 0xe5b7ca9a, 0x0f1c, 0x4f66, 0x90,0x02, 0x74,0xec,0x50,0xd8,0xb3,0x04)
#endif
#else
typedef struct IWMPlayerHookVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPlayerHook *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPlayerHook *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPlayerHook *This);

    /*** IWMPlayerHook methods ***/
    HRESULT (STDMETHODCALLTYPE *PreDecode)(
        IWMPlayerHook *This);

    END_INTERFACE
} IWMPlayerHookVtbl;

interface IWMPlayerHook {
    CONST_VTBL IWMPlayerHookVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPlayerHook_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPlayerHook_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPlayerHook_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPlayerHook methods ***/
#define IWMPlayerHook_PreDecode(This) (This)->lpVtbl->PreDecode(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPlayerHook_QueryInterface(IWMPlayerHook* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPlayerHook_AddRef(IWMPlayerHook* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPlayerHook_Release(IWMPlayerHook* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPlayerHook methods ***/
static inline HRESULT IWMPlayerHook_PreDecode(IWMPlayerHook* This) {
    return This->lpVtbl->PreDecode(This);
}
#endif
#endif

#endif


#endif  /* __IWMPlayerHook_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderAdvanced interface
 */
#ifndef __IWMReaderAdvanced_INTERFACE_DEFINED__
#define __IWMReaderAdvanced_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderAdvanced, 0x96406bea, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bea-2b2b-11d3-b36b-00c04f6108ff")
IWMReaderAdvanced : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetUserProvidedClock(
        WINBOOL fUserClock) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserProvidedClock(
        WINBOOL *pfUserClock) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeliverTime(
        QWORD cnsTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetManualStreamSelection(
        WINBOOL fSelection) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetManualStreamSelection(
        WINBOOL *pfSelection) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamsSelected(
        WORD cStreamCount,
        WORD *pwStreamNumbers,
        WMT_STREAM_SELECTION *pSelections) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamSelected(
        WORD wStreamNum,
        WMT_STREAM_SELECTION *pSelection) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetReceiveSelectionCallbacks(
        WINBOOL fGetCallbacks) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetReceiveSelectionCallbacks(
        WINBOOL *pfGetCallbacks) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetReceiveStreamSamples(
        WORD wStreamNum,
        WINBOOL fReceiveStreamSamples) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetReceiveStreamSamples(
        WORD wStreamNum,
        WINBOOL *pfReceiveStreamSamples) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAllocateForOutput(
        DWORD dwOutputNum,
        WINBOOL fAllocate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllocateForOutput(
        DWORD dwOutputNum,
        WINBOOL *pfAllocate) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAllocateForStream(
        WORD wStreamNum,
        WINBOOL fAllocate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllocateForStream(
        WORD dwStreamNum,
        WINBOOL *pfAllocate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatistics(
        WM_READER_STATISTICS *pStatistics) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetClientInfo(
        WM_READER_CLIENTINFO *pClientInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaxOutputSampleSize(
        DWORD dwOutput,
        DWORD *pcbMax) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaxStreamSampleSize(
        WORD wStream,
        DWORD *pcbMax) = 0;

    virtual HRESULT STDMETHODCALLTYPE NotifyLateDelivery(
        QWORD cnsLateness) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderAdvanced, 0x96406bea, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMReaderAdvancedVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderAdvanced *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderAdvanced *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderAdvanced *This);

    /*** IWMReaderAdvanced methods ***/
    HRESULT (STDMETHODCALLTYPE *SetUserProvidedClock)(
        IWMReaderAdvanced *This,
        WINBOOL fUserClock);

    HRESULT (STDMETHODCALLTYPE *GetUserProvidedClock)(
        IWMReaderAdvanced *This,
        WINBOOL *pfUserClock);

    HRESULT (STDMETHODCALLTYPE *DeliverTime)(
        IWMReaderAdvanced *This,
        QWORD cnsTime);

    HRESULT (STDMETHODCALLTYPE *SetManualStreamSelection)(
        IWMReaderAdvanced *This,
        WINBOOL fSelection);

    HRESULT (STDMETHODCALLTYPE *GetManualStreamSelection)(
        IWMReaderAdvanced *This,
        WINBOOL *pfSelection);

    HRESULT (STDMETHODCALLTYPE *SetStreamsSelected)(
        IWMReaderAdvanced *This,
        WORD cStreamCount,
        WORD *pwStreamNumbers,
        WMT_STREAM_SELECTION *pSelections);

    HRESULT (STDMETHODCALLTYPE *GetStreamSelected)(
        IWMReaderAdvanced *This,
        WORD wStreamNum,
        WMT_STREAM_SELECTION *pSelection);

    HRESULT (STDMETHODCALLTYPE *SetReceiveSelectionCallbacks)(
        IWMReaderAdvanced *This,
        WINBOOL fGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *GetReceiveSelectionCallbacks)(
        IWMReaderAdvanced *This,
        WINBOOL *pfGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *SetReceiveStreamSamples)(
        IWMReaderAdvanced *This,
        WORD wStreamNum,
        WINBOOL fReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *GetReceiveStreamSamples)(
        IWMReaderAdvanced *This,
        WORD wStreamNum,
        WINBOOL *pfReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForOutput)(
        IWMReaderAdvanced *This,
        DWORD dwOutputNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForOutput)(
        IWMReaderAdvanced *This,
        DWORD dwOutputNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForStream)(
        IWMReaderAdvanced *This,
        WORD wStreamNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForStream)(
        IWMReaderAdvanced *This,
        WORD dwStreamNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IWMReaderAdvanced *This,
        WM_READER_STATISTICS *pStatistics);

    HRESULT (STDMETHODCALLTYPE *SetClientInfo)(
        IWMReaderAdvanced *This,
        WM_READER_CLIENTINFO *pClientInfo);

    HRESULT (STDMETHODCALLTYPE *GetMaxOutputSampleSize)(
        IWMReaderAdvanced *This,
        DWORD dwOutput,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *GetMaxStreamSampleSize)(
        IWMReaderAdvanced *This,
        WORD wStream,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *NotifyLateDelivery)(
        IWMReaderAdvanced *This,
        QWORD cnsLateness);

    END_INTERFACE
} IWMReaderAdvancedVtbl;

interface IWMReaderAdvanced {
    CONST_VTBL IWMReaderAdvancedVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderAdvanced_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderAdvanced_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderAdvanced_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderAdvanced methods ***/
#define IWMReaderAdvanced_SetUserProvidedClock(This,fUserClock) (This)->lpVtbl->SetUserProvidedClock(This,fUserClock)
#define IWMReaderAdvanced_GetUserProvidedClock(This,pfUserClock) (This)->lpVtbl->GetUserProvidedClock(This,pfUserClock)
#define IWMReaderAdvanced_DeliverTime(This,cnsTime) (This)->lpVtbl->DeliverTime(This,cnsTime)
#define IWMReaderAdvanced_SetManualStreamSelection(This,fSelection) (This)->lpVtbl->SetManualStreamSelection(This,fSelection)
#define IWMReaderAdvanced_GetManualStreamSelection(This,pfSelection) (This)->lpVtbl->GetManualStreamSelection(This,pfSelection)
#define IWMReaderAdvanced_SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections) (This)->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections)
#define IWMReaderAdvanced_GetStreamSelected(This,wStreamNum,pSelection) (This)->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection)
#define IWMReaderAdvanced_SetReceiveSelectionCallbacks(This,fGetCallbacks) (This)->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks)
#define IWMReaderAdvanced_GetReceiveSelectionCallbacks(This,pfGetCallbacks) (This)->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks)
#define IWMReaderAdvanced_SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples) (This)->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples)
#define IWMReaderAdvanced_GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples) (This)->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples)
#define IWMReaderAdvanced_SetAllocateForOutput(This,dwOutputNum,fAllocate) (This)->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate)
#define IWMReaderAdvanced_GetAllocateForOutput(This,dwOutputNum,pfAllocate) (This)->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate)
#define IWMReaderAdvanced_SetAllocateForStream(This,wStreamNum,fAllocate) (This)->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate)
#define IWMReaderAdvanced_GetAllocateForStream(This,dwStreamNum,pfAllocate) (This)->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate)
#define IWMReaderAdvanced_GetStatistics(This,pStatistics) (This)->lpVtbl->GetStatistics(This,pStatistics)
#define IWMReaderAdvanced_SetClientInfo(This,pClientInfo) (This)->lpVtbl->SetClientInfo(This,pClientInfo)
#define IWMReaderAdvanced_GetMaxOutputSampleSize(This,dwOutput,pcbMax) (This)->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax)
#define IWMReaderAdvanced_GetMaxStreamSampleSize(This,wStream,pcbMax) (This)->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax)
#define IWMReaderAdvanced_NotifyLateDelivery(This,cnsLateness) (This)->lpVtbl->NotifyLateDelivery(This,cnsLateness)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderAdvanced_QueryInterface(IWMReaderAdvanced* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderAdvanced_AddRef(IWMReaderAdvanced* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderAdvanced_Release(IWMReaderAdvanced* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderAdvanced methods ***/
static inline HRESULT IWMReaderAdvanced_SetUserProvidedClock(IWMReaderAdvanced* This,WINBOOL fUserClock) {
    return This->lpVtbl->SetUserProvidedClock(This,fUserClock);
}
static inline HRESULT IWMReaderAdvanced_GetUserProvidedClock(IWMReaderAdvanced* This,WINBOOL *pfUserClock) {
    return This->lpVtbl->GetUserProvidedClock(This,pfUserClock);
}
static inline HRESULT IWMReaderAdvanced_DeliverTime(IWMReaderAdvanced* This,QWORD cnsTime) {
    return This->lpVtbl->DeliverTime(This,cnsTime);
}
static inline HRESULT IWMReaderAdvanced_SetManualStreamSelection(IWMReaderAdvanced* This,WINBOOL fSelection) {
    return This->lpVtbl->SetManualStreamSelection(This,fSelection);
}
static inline HRESULT IWMReaderAdvanced_GetManualStreamSelection(IWMReaderAdvanced* This,WINBOOL *pfSelection) {
    return This->lpVtbl->GetManualStreamSelection(This,pfSelection);
}
static inline HRESULT IWMReaderAdvanced_SetStreamsSelected(IWMReaderAdvanced* This,WORD cStreamCount,WORD *pwStreamNumbers,WMT_STREAM_SELECTION *pSelections) {
    return This->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections);
}
static inline HRESULT IWMReaderAdvanced_GetStreamSelected(IWMReaderAdvanced* This,WORD wStreamNum,WMT_STREAM_SELECTION *pSelection) {
    return This->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection);
}
static inline HRESULT IWMReaderAdvanced_SetReceiveSelectionCallbacks(IWMReaderAdvanced* This,WINBOOL fGetCallbacks) {
    return This->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced_GetReceiveSelectionCallbacks(IWMReaderAdvanced* This,WINBOOL *pfGetCallbacks) {
    return This->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced_SetReceiveStreamSamples(IWMReaderAdvanced* This,WORD wStreamNum,WINBOOL fReceiveStreamSamples) {
    return This->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced_GetReceiveStreamSamples(IWMReaderAdvanced* This,WORD wStreamNum,WINBOOL *pfReceiveStreamSamples) {
    return This->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced_SetAllocateForOutput(IWMReaderAdvanced* This,DWORD dwOutputNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced_GetAllocateForOutput(IWMReaderAdvanced* This,DWORD dwOutputNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced_SetAllocateForStream(IWMReaderAdvanced* This,WORD wStreamNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced_GetAllocateForStream(IWMReaderAdvanced* This,WORD dwStreamNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced_GetStatistics(IWMReaderAdvanced* This,WM_READER_STATISTICS *pStatistics) {
    return This->lpVtbl->GetStatistics(This,pStatistics);
}
static inline HRESULT IWMReaderAdvanced_SetClientInfo(IWMReaderAdvanced* This,WM_READER_CLIENTINFO *pClientInfo) {
    return This->lpVtbl->SetClientInfo(This,pClientInfo);
}
static inline HRESULT IWMReaderAdvanced_GetMaxOutputSampleSize(IWMReaderAdvanced* This,DWORD dwOutput,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax);
}
static inline HRESULT IWMReaderAdvanced_GetMaxStreamSampleSize(IWMReaderAdvanced* This,WORD wStream,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax);
}
static inline HRESULT IWMReaderAdvanced_NotifyLateDelivery(IWMReaderAdvanced* This,QWORD cnsLateness) {
    return This->lpVtbl->NotifyLateDelivery(This,cnsLateness);
}
#endif
#endif

#endif


#endif  /* __IWMReaderAdvanced_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderAdvanced2 interface
 */
#ifndef __IWMReaderAdvanced2_INTERFACE_DEFINED__
#define __IWMReaderAdvanced2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderAdvanced2, 0xae14a945, 0xb90c, 0x4d0d, 0x91,0x27, 0x80,0xd6,0x65,0xf7,0xd7,0x3e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ae14a945-b90c-4d0d-9127-80d665f7d73e")
IWMReaderAdvanced2 : public IWMReaderAdvanced
{
    virtual HRESULT STDMETHODCALLTYPE SetPlayMode(
        WMT_PLAY_MODE Mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPlayMode(
        WMT_PLAY_MODE *pMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBufferProgress(
        DWORD *pdwPercent,
        QWORD *pcnsBuffering) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDownloadProgress(
        DWORD *pdwPercent,
        QWORD *pqwBytesDownloaded,
        QWORD *pcnsDownload) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSaveAsProgress(
        DWORD *pdwPercent) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveFileAs(
        const WCHAR *pwszFilename) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProtocolName(
        WCHAR *pwszProtocol,
        DWORD *pcchProtocol) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartAtMarker(
        WORD wMarkerIndex,
        QWORD cnsDuration,
        float fRate,
        void *pvContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputSetting(
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputSetting(
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE Preroll(
        QWORD cnsStart,
        QWORD cnsDuration,
        float fRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLogClientID(
        WINBOOL fLogClientID) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLogClientID(
        WINBOOL *pfLogClientID) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopBuffering(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenStream(
        IStream *pStream,
        IWMReaderCallback *pCallback,
        void *pvContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderAdvanced2, 0xae14a945, 0xb90c, 0x4d0d, 0x91,0x27, 0x80,0xd6,0x65,0xf7,0xd7,0x3e)
#endif
#else
typedef struct IWMReaderAdvanced2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderAdvanced2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderAdvanced2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderAdvanced2 *This);

    /*** IWMReaderAdvanced methods ***/
    HRESULT (STDMETHODCALLTYPE *SetUserProvidedClock)(
        IWMReaderAdvanced2 *This,
        WINBOOL fUserClock);

    HRESULT (STDMETHODCALLTYPE *GetUserProvidedClock)(
        IWMReaderAdvanced2 *This,
        WINBOOL *pfUserClock);

    HRESULT (STDMETHODCALLTYPE *DeliverTime)(
        IWMReaderAdvanced2 *This,
        QWORD cnsTime);

    HRESULT (STDMETHODCALLTYPE *SetManualStreamSelection)(
        IWMReaderAdvanced2 *This,
        WINBOOL fSelection);

    HRESULT (STDMETHODCALLTYPE *GetManualStreamSelection)(
        IWMReaderAdvanced2 *This,
        WINBOOL *pfSelection);

    HRESULT (STDMETHODCALLTYPE *SetStreamsSelected)(
        IWMReaderAdvanced2 *This,
        WORD cStreamCount,
        WORD *pwStreamNumbers,
        WMT_STREAM_SELECTION *pSelections);

    HRESULT (STDMETHODCALLTYPE *GetStreamSelected)(
        IWMReaderAdvanced2 *This,
        WORD wStreamNum,
        WMT_STREAM_SELECTION *pSelection);

    HRESULT (STDMETHODCALLTYPE *SetReceiveSelectionCallbacks)(
        IWMReaderAdvanced2 *This,
        WINBOOL fGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *GetReceiveSelectionCallbacks)(
        IWMReaderAdvanced2 *This,
        WINBOOL *pfGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *SetReceiveStreamSamples)(
        IWMReaderAdvanced2 *This,
        WORD wStreamNum,
        WINBOOL fReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *GetReceiveStreamSamples)(
        IWMReaderAdvanced2 *This,
        WORD wStreamNum,
        WINBOOL *pfReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForOutput)(
        IWMReaderAdvanced2 *This,
        DWORD dwOutputNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForOutput)(
        IWMReaderAdvanced2 *This,
        DWORD dwOutputNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForStream)(
        IWMReaderAdvanced2 *This,
        WORD wStreamNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForStream)(
        IWMReaderAdvanced2 *This,
        WORD dwStreamNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IWMReaderAdvanced2 *This,
        WM_READER_STATISTICS *pStatistics);

    HRESULT (STDMETHODCALLTYPE *SetClientInfo)(
        IWMReaderAdvanced2 *This,
        WM_READER_CLIENTINFO *pClientInfo);

    HRESULT (STDMETHODCALLTYPE *GetMaxOutputSampleSize)(
        IWMReaderAdvanced2 *This,
        DWORD dwOutput,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *GetMaxStreamSampleSize)(
        IWMReaderAdvanced2 *This,
        WORD wStream,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *NotifyLateDelivery)(
        IWMReaderAdvanced2 *This,
        QWORD cnsLateness);

    /*** IWMReaderAdvanced2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPlayMode)(
        IWMReaderAdvanced2 *This,
        WMT_PLAY_MODE Mode);

    HRESULT (STDMETHODCALLTYPE *GetPlayMode)(
        IWMReaderAdvanced2 *This,
        WMT_PLAY_MODE *pMode);

    HRESULT (STDMETHODCALLTYPE *GetBufferProgress)(
        IWMReaderAdvanced2 *This,
        DWORD *pdwPercent,
        QWORD *pcnsBuffering);

    HRESULT (STDMETHODCALLTYPE *GetDownloadProgress)(
        IWMReaderAdvanced2 *This,
        DWORD *pdwPercent,
        QWORD *pqwBytesDownloaded,
        QWORD *pcnsDownload);

    HRESULT (STDMETHODCALLTYPE *GetSaveAsProgress)(
        IWMReaderAdvanced2 *This,
        DWORD *pdwPercent);

    HRESULT (STDMETHODCALLTYPE *SaveFileAs)(
        IWMReaderAdvanced2 *This,
        const WCHAR *pwszFilename);

    HRESULT (STDMETHODCALLTYPE *GetProtocolName)(
        IWMReaderAdvanced2 *This,
        WCHAR *pwszProtocol,
        DWORD *pcchProtocol);

    HRESULT (STDMETHODCALLTYPE *StartAtMarker)(
        IWMReaderAdvanced2 *This,
        WORD wMarkerIndex,
        QWORD cnsDuration,
        float fRate,
        void *pvContext);

    HRESULT (STDMETHODCALLTYPE *GetOutputSetting)(
        IWMReaderAdvanced2 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength);

    HRESULT (STDMETHODCALLTYPE *SetOutputSetting)(
        IWMReaderAdvanced2 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength);

    HRESULT (STDMETHODCALLTYPE *Preroll)(
        IWMReaderAdvanced2 *This,
        QWORD cnsStart,
        QWORD cnsDuration,
        float fRate);

    HRESULT (STDMETHODCALLTYPE *SetLogClientID)(
        IWMReaderAdvanced2 *This,
        WINBOOL fLogClientID);

    HRESULT (STDMETHODCALLTYPE *GetLogClientID)(
        IWMReaderAdvanced2 *This,
        WINBOOL *pfLogClientID);

    HRESULT (STDMETHODCALLTYPE *StopBuffering)(
        IWMReaderAdvanced2 *This);

    HRESULT (STDMETHODCALLTYPE *OpenStream)(
        IWMReaderAdvanced2 *This,
        IStream *pStream,
        IWMReaderCallback *pCallback,
        void *pvContext);

    END_INTERFACE
} IWMReaderAdvanced2Vtbl;

interface IWMReaderAdvanced2 {
    CONST_VTBL IWMReaderAdvanced2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderAdvanced2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderAdvanced2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderAdvanced2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderAdvanced methods ***/
#define IWMReaderAdvanced2_SetUserProvidedClock(This,fUserClock) (This)->lpVtbl->SetUserProvidedClock(This,fUserClock)
#define IWMReaderAdvanced2_GetUserProvidedClock(This,pfUserClock) (This)->lpVtbl->GetUserProvidedClock(This,pfUserClock)
#define IWMReaderAdvanced2_DeliverTime(This,cnsTime) (This)->lpVtbl->DeliverTime(This,cnsTime)
#define IWMReaderAdvanced2_SetManualStreamSelection(This,fSelection) (This)->lpVtbl->SetManualStreamSelection(This,fSelection)
#define IWMReaderAdvanced2_GetManualStreamSelection(This,pfSelection) (This)->lpVtbl->GetManualStreamSelection(This,pfSelection)
#define IWMReaderAdvanced2_SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections) (This)->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections)
#define IWMReaderAdvanced2_GetStreamSelected(This,wStreamNum,pSelection) (This)->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection)
#define IWMReaderAdvanced2_SetReceiveSelectionCallbacks(This,fGetCallbacks) (This)->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks)
#define IWMReaderAdvanced2_GetReceiveSelectionCallbacks(This,pfGetCallbacks) (This)->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks)
#define IWMReaderAdvanced2_SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples) (This)->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples)
#define IWMReaderAdvanced2_GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples) (This)->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples)
#define IWMReaderAdvanced2_SetAllocateForOutput(This,dwOutputNum,fAllocate) (This)->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate)
#define IWMReaderAdvanced2_GetAllocateForOutput(This,dwOutputNum,pfAllocate) (This)->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate)
#define IWMReaderAdvanced2_SetAllocateForStream(This,wStreamNum,fAllocate) (This)->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate)
#define IWMReaderAdvanced2_GetAllocateForStream(This,dwStreamNum,pfAllocate) (This)->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate)
#define IWMReaderAdvanced2_GetStatistics(This,pStatistics) (This)->lpVtbl->GetStatistics(This,pStatistics)
#define IWMReaderAdvanced2_SetClientInfo(This,pClientInfo) (This)->lpVtbl->SetClientInfo(This,pClientInfo)
#define IWMReaderAdvanced2_GetMaxOutputSampleSize(This,dwOutput,pcbMax) (This)->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax)
#define IWMReaderAdvanced2_GetMaxStreamSampleSize(This,wStream,pcbMax) (This)->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax)
#define IWMReaderAdvanced2_NotifyLateDelivery(This,cnsLateness) (This)->lpVtbl->NotifyLateDelivery(This,cnsLateness)
/*** IWMReaderAdvanced2 methods ***/
#define IWMReaderAdvanced2_SetPlayMode(This,Mode) (This)->lpVtbl->SetPlayMode(This,Mode)
#define IWMReaderAdvanced2_GetPlayMode(This,pMode) (This)->lpVtbl->GetPlayMode(This,pMode)
#define IWMReaderAdvanced2_GetBufferProgress(This,pdwPercent,pcnsBuffering) (This)->lpVtbl->GetBufferProgress(This,pdwPercent,pcnsBuffering)
#define IWMReaderAdvanced2_GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload) (This)->lpVtbl->GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload)
#define IWMReaderAdvanced2_GetSaveAsProgress(This,pdwPercent) (This)->lpVtbl->GetSaveAsProgress(This,pdwPercent)
#define IWMReaderAdvanced2_SaveFileAs(This,pwszFilename) (This)->lpVtbl->SaveFileAs(This,pwszFilename)
#define IWMReaderAdvanced2_GetProtocolName(This,pwszProtocol,pcchProtocol) (This)->lpVtbl->GetProtocolName(This,pwszProtocol,pcchProtocol)
#define IWMReaderAdvanced2_StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext) (This)->lpVtbl->StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext)
#define IWMReaderAdvanced2_GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength) (This)->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength)
#define IWMReaderAdvanced2_SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength) (This)->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength)
#define IWMReaderAdvanced2_Preroll(This,cnsStart,cnsDuration,fRate) (This)->lpVtbl->Preroll(This,cnsStart,cnsDuration,fRate)
#define IWMReaderAdvanced2_SetLogClientID(This,fLogClientID) (This)->lpVtbl->SetLogClientID(This,fLogClientID)
#define IWMReaderAdvanced2_GetLogClientID(This,pfLogClientID) (This)->lpVtbl->GetLogClientID(This,pfLogClientID)
#define IWMReaderAdvanced2_StopBuffering(This) (This)->lpVtbl->StopBuffering(This)
#define IWMReaderAdvanced2_OpenStream(This,pStream,pCallback,pvContext) (This)->lpVtbl->OpenStream(This,pStream,pCallback,pvContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderAdvanced2_QueryInterface(IWMReaderAdvanced2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderAdvanced2_AddRef(IWMReaderAdvanced2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderAdvanced2_Release(IWMReaderAdvanced2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderAdvanced methods ***/
static inline HRESULT IWMReaderAdvanced2_SetUserProvidedClock(IWMReaderAdvanced2* This,WINBOOL fUserClock) {
    return This->lpVtbl->SetUserProvidedClock(This,fUserClock);
}
static inline HRESULT IWMReaderAdvanced2_GetUserProvidedClock(IWMReaderAdvanced2* This,WINBOOL *pfUserClock) {
    return This->lpVtbl->GetUserProvidedClock(This,pfUserClock);
}
static inline HRESULT IWMReaderAdvanced2_DeliverTime(IWMReaderAdvanced2* This,QWORD cnsTime) {
    return This->lpVtbl->DeliverTime(This,cnsTime);
}
static inline HRESULT IWMReaderAdvanced2_SetManualStreamSelection(IWMReaderAdvanced2* This,WINBOOL fSelection) {
    return This->lpVtbl->SetManualStreamSelection(This,fSelection);
}
static inline HRESULT IWMReaderAdvanced2_GetManualStreamSelection(IWMReaderAdvanced2* This,WINBOOL *pfSelection) {
    return This->lpVtbl->GetManualStreamSelection(This,pfSelection);
}
static inline HRESULT IWMReaderAdvanced2_SetStreamsSelected(IWMReaderAdvanced2* This,WORD cStreamCount,WORD *pwStreamNumbers,WMT_STREAM_SELECTION *pSelections) {
    return This->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections);
}
static inline HRESULT IWMReaderAdvanced2_GetStreamSelected(IWMReaderAdvanced2* This,WORD wStreamNum,WMT_STREAM_SELECTION *pSelection) {
    return This->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection);
}
static inline HRESULT IWMReaderAdvanced2_SetReceiveSelectionCallbacks(IWMReaderAdvanced2* This,WINBOOL fGetCallbacks) {
    return This->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced2_GetReceiveSelectionCallbacks(IWMReaderAdvanced2* This,WINBOOL *pfGetCallbacks) {
    return This->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced2_SetReceiveStreamSamples(IWMReaderAdvanced2* This,WORD wStreamNum,WINBOOL fReceiveStreamSamples) {
    return This->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced2_GetReceiveStreamSamples(IWMReaderAdvanced2* This,WORD wStreamNum,WINBOOL *pfReceiveStreamSamples) {
    return This->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced2_SetAllocateForOutput(IWMReaderAdvanced2* This,DWORD dwOutputNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced2_GetAllocateForOutput(IWMReaderAdvanced2* This,DWORD dwOutputNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced2_SetAllocateForStream(IWMReaderAdvanced2* This,WORD wStreamNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced2_GetAllocateForStream(IWMReaderAdvanced2* This,WORD dwStreamNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced2_GetStatistics(IWMReaderAdvanced2* This,WM_READER_STATISTICS *pStatistics) {
    return This->lpVtbl->GetStatistics(This,pStatistics);
}
static inline HRESULT IWMReaderAdvanced2_SetClientInfo(IWMReaderAdvanced2* This,WM_READER_CLIENTINFO *pClientInfo) {
    return This->lpVtbl->SetClientInfo(This,pClientInfo);
}
static inline HRESULT IWMReaderAdvanced2_GetMaxOutputSampleSize(IWMReaderAdvanced2* This,DWORD dwOutput,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax);
}
static inline HRESULT IWMReaderAdvanced2_GetMaxStreamSampleSize(IWMReaderAdvanced2* This,WORD wStream,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax);
}
static inline HRESULT IWMReaderAdvanced2_NotifyLateDelivery(IWMReaderAdvanced2* This,QWORD cnsLateness) {
    return This->lpVtbl->NotifyLateDelivery(This,cnsLateness);
}
/*** IWMReaderAdvanced2 methods ***/
static inline HRESULT IWMReaderAdvanced2_SetPlayMode(IWMReaderAdvanced2* This,WMT_PLAY_MODE Mode) {
    return This->lpVtbl->SetPlayMode(This,Mode);
}
static inline HRESULT IWMReaderAdvanced2_GetPlayMode(IWMReaderAdvanced2* This,WMT_PLAY_MODE *pMode) {
    return This->lpVtbl->GetPlayMode(This,pMode);
}
static inline HRESULT IWMReaderAdvanced2_GetBufferProgress(IWMReaderAdvanced2* This,DWORD *pdwPercent,QWORD *pcnsBuffering) {
    return This->lpVtbl->GetBufferProgress(This,pdwPercent,pcnsBuffering);
}
static inline HRESULT IWMReaderAdvanced2_GetDownloadProgress(IWMReaderAdvanced2* This,DWORD *pdwPercent,QWORD *pqwBytesDownloaded,QWORD *pcnsDownload) {
    return This->lpVtbl->GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload);
}
static inline HRESULT IWMReaderAdvanced2_GetSaveAsProgress(IWMReaderAdvanced2* This,DWORD *pdwPercent) {
    return This->lpVtbl->GetSaveAsProgress(This,pdwPercent);
}
static inline HRESULT IWMReaderAdvanced2_SaveFileAs(IWMReaderAdvanced2* This,const WCHAR *pwszFilename) {
    return This->lpVtbl->SaveFileAs(This,pwszFilename);
}
static inline HRESULT IWMReaderAdvanced2_GetProtocolName(IWMReaderAdvanced2* This,WCHAR *pwszProtocol,DWORD *pcchProtocol) {
    return This->lpVtbl->GetProtocolName(This,pwszProtocol,pcchProtocol);
}
static inline HRESULT IWMReaderAdvanced2_StartAtMarker(IWMReaderAdvanced2* This,WORD wMarkerIndex,QWORD cnsDuration,float fRate,void *pvContext) {
    return This->lpVtbl->StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext);
}
static inline HRESULT IWMReaderAdvanced2_GetOutputSetting(IWMReaderAdvanced2* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE *pType,BYTE *pValue,WORD *pcbLength) {
    return This->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength);
}
static inline HRESULT IWMReaderAdvanced2_SetOutputSetting(IWMReaderAdvanced2* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE Type,const BYTE *pValue,WORD cbLength) {
    return This->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength);
}
static inline HRESULT IWMReaderAdvanced2_Preroll(IWMReaderAdvanced2* This,QWORD cnsStart,QWORD cnsDuration,float fRate) {
    return This->lpVtbl->Preroll(This,cnsStart,cnsDuration,fRate);
}
static inline HRESULT IWMReaderAdvanced2_SetLogClientID(IWMReaderAdvanced2* This,WINBOOL fLogClientID) {
    return This->lpVtbl->SetLogClientID(This,fLogClientID);
}
static inline HRESULT IWMReaderAdvanced2_GetLogClientID(IWMReaderAdvanced2* This,WINBOOL *pfLogClientID) {
    return This->lpVtbl->GetLogClientID(This,pfLogClientID);
}
static inline HRESULT IWMReaderAdvanced2_StopBuffering(IWMReaderAdvanced2* This) {
    return This->lpVtbl->StopBuffering(This);
}
static inline HRESULT IWMReaderAdvanced2_OpenStream(IWMReaderAdvanced2* This,IStream *pStream,IWMReaderCallback *pCallback,void *pvContext) {
    return This->lpVtbl->OpenStream(This,pStream,pCallback,pvContext);
}
#endif
#endif

#endif


#endif  /* __IWMReaderAdvanced2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderAdvanced3 interface
 */
#ifndef __IWMReaderAdvanced3_INTERFACE_DEFINED__
#define __IWMReaderAdvanced3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderAdvanced3, 0x5dc0674b, 0xf04b, 0x4a4e, 0x9f,0x2a, 0xb1,0xaf,0xde,0x2c,0x81,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5dc0674b-f04b-4a4e-9f2a-b1afde2c8100")
IWMReaderAdvanced3 : public IWMReaderAdvanced2
{
    virtual HRESULT STDMETHODCALLTYPE StopNetStreaming(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartAtPosition(
        WORD wStreamNum,
        void *pvOffsetStart,
        void *pvDuration,
        WMT_OFFSET_FORMAT dwOffsetFormat,
        float fRate,
        void *pvContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderAdvanced3, 0x5dc0674b, 0xf04b, 0x4a4e, 0x9f,0x2a, 0xb1,0xaf,0xde,0x2c,0x81,0x00)
#endif
#else
typedef struct IWMReaderAdvanced3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderAdvanced3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderAdvanced3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderAdvanced3 *This);

    /*** IWMReaderAdvanced methods ***/
    HRESULT (STDMETHODCALLTYPE *SetUserProvidedClock)(
        IWMReaderAdvanced3 *This,
        WINBOOL fUserClock);

    HRESULT (STDMETHODCALLTYPE *GetUserProvidedClock)(
        IWMReaderAdvanced3 *This,
        WINBOOL *pfUserClock);

    HRESULT (STDMETHODCALLTYPE *DeliverTime)(
        IWMReaderAdvanced3 *This,
        QWORD cnsTime);

    HRESULT (STDMETHODCALLTYPE *SetManualStreamSelection)(
        IWMReaderAdvanced3 *This,
        WINBOOL fSelection);

    HRESULT (STDMETHODCALLTYPE *GetManualStreamSelection)(
        IWMReaderAdvanced3 *This,
        WINBOOL *pfSelection);

    HRESULT (STDMETHODCALLTYPE *SetStreamsSelected)(
        IWMReaderAdvanced3 *This,
        WORD cStreamCount,
        WORD *pwStreamNumbers,
        WMT_STREAM_SELECTION *pSelections);

    HRESULT (STDMETHODCALLTYPE *GetStreamSelected)(
        IWMReaderAdvanced3 *This,
        WORD wStreamNum,
        WMT_STREAM_SELECTION *pSelection);

    HRESULT (STDMETHODCALLTYPE *SetReceiveSelectionCallbacks)(
        IWMReaderAdvanced3 *This,
        WINBOOL fGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *GetReceiveSelectionCallbacks)(
        IWMReaderAdvanced3 *This,
        WINBOOL *pfGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *SetReceiveStreamSamples)(
        IWMReaderAdvanced3 *This,
        WORD wStreamNum,
        WINBOOL fReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *GetReceiveStreamSamples)(
        IWMReaderAdvanced3 *This,
        WORD wStreamNum,
        WINBOOL *pfReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForOutput)(
        IWMReaderAdvanced3 *This,
        DWORD dwOutputNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForOutput)(
        IWMReaderAdvanced3 *This,
        DWORD dwOutputNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForStream)(
        IWMReaderAdvanced3 *This,
        WORD wStreamNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForStream)(
        IWMReaderAdvanced3 *This,
        WORD dwStreamNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IWMReaderAdvanced3 *This,
        WM_READER_STATISTICS *pStatistics);

    HRESULT (STDMETHODCALLTYPE *SetClientInfo)(
        IWMReaderAdvanced3 *This,
        WM_READER_CLIENTINFO *pClientInfo);

    HRESULT (STDMETHODCALLTYPE *GetMaxOutputSampleSize)(
        IWMReaderAdvanced3 *This,
        DWORD dwOutput,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *GetMaxStreamSampleSize)(
        IWMReaderAdvanced3 *This,
        WORD wStream,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *NotifyLateDelivery)(
        IWMReaderAdvanced3 *This,
        QWORD cnsLateness);

    /*** IWMReaderAdvanced2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPlayMode)(
        IWMReaderAdvanced3 *This,
        WMT_PLAY_MODE Mode);

    HRESULT (STDMETHODCALLTYPE *GetPlayMode)(
        IWMReaderAdvanced3 *This,
        WMT_PLAY_MODE *pMode);

    HRESULT (STDMETHODCALLTYPE *GetBufferProgress)(
        IWMReaderAdvanced3 *This,
        DWORD *pdwPercent,
        QWORD *pcnsBuffering);

    HRESULT (STDMETHODCALLTYPE *GetDownloadProgress)(
        IWMReaderAdvanced3 *This,
        DWORD *pdwPercent,
        QWORD *pqwBytesDownloaded,
        QWORD *pcnsDownload);

    HRESULT (STDMETHODCALLTYPE *GetSaveAsProgress)(
        IWMReaderAdvanced3 *This,
        DWORD *pdwPercent);

    HRESULT (STDMETHODCALLTYPE *SaveFileAs)(
        IWMReaderAdvanced3 *This,
        const WCHAR *pwszFilename);

    HRESULT (STDMETHODCALLTYPE *GetProtocolName)(
        IWMReaderAdvanced3 *This,
        WCHAR *pwszProtocol,
        DWORD *pcchProtocol);

    HRESULT (STDMETHODCALLTYPE *StartAtMarker)(
        IWMReaderAdvanced3 *This,
        WORD wMarkerIndex,
        QWORD cnsDuration,
        float fRate,
        void *pvContext);

    HRESULT (STDMETHODCALLTYPE *GetOutputSetting)(
        IWMReaderAdvanced3 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength);

    HRESULT (STDMETHODCALLTYPE *SetOutputSetting)(
        IWMReaderAdvanced3 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength);

    HRESULT (STDMETHODCALLTYPE *Preroll)(
        IWMReaderAdvanced3 *This,
        QWORD cnsStart,
        QWORD cnsDuration,
        float fRate);

    HRESULT (STDMETHODCALLTYPE *SetLogClientID)(
        IWMReaderAdvanced3 *This,
        WINBOOL fLogClientID);

    HRESULT (STDMETHODCALLTYPE *GetLogClientID)(
        IWMReaderAdvanced3 *This,
        WINBOOL *pfLogClientID);

    HRESULT (STDMETHODCALLTYPE *StopBuffering)(
        IWMReaderAdvanced3 *This);

    HRESULT (STDMETHODCALLTYPE *OpenStream)(
        IWMReaderAdvanced3 *This,
        IStream *pStream,
        IWMReaderCallback *pCallback,
        void *pvContext);

    /*** IWMReaderAdvanced3 methods ***/
    HRESULT (STDMETHODCALLTYPE *StopNetStreaming)(
        IWMReaderAdvanced3 *This);

    HRESULT (STDMETHODCALLTYPE *StartAtPosition)(
        IWMReaderAdvanced3 *This,
        WORD wStreamNum,
        void *pvOffsetStart,
        void *pvDuration,
        WMT_OFFSET_FORMAT dwOffsetFormat,
        float fRate,
        void *pvContext);

    END_INTERFACE
} IWMReaderAdvanced3Vtbl;

interface IWMReaderAdvanced3 {
    CONST_VTBL IWMReaderAdvanced3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderAdvanced3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderAdvanced3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderAdvanced3_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderAdvanced methods ***/
#define IWMReaderAdvanced3_SetUserProvidedClock(This,fUserClock) (This)->lpVtbl->SetUserProvidedClock(This,fUserClock)
#define IWMReaderAdvanced3_GetUserProvidedClock(This,pfUserClock) (This)->lpVtbl->GetUserProvidedClock(This,pfUserClock)
#define IWMReaderAdvanced3_DeliverTime(This,cnsTime) (This)->lpVtbl->DeliverTime(This,cnsTime)
#define IWMReaderAdvanced3_SetManualStreamSelection(This,fSelection) (This)->lpVtbl->SetManualStreamSelection(This,fSelection)
#define IWMReaderAdvanced3_GetManualStreamSelection(This,pfSelection) (This)->lpVtbl->GetManualStreamSelection(This,pfSelection)
#define IWMReaderAdvanced3_SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections) (This)->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections)
#define IWMReaderAdvanced3_GetStreamSelected(This,wStreamNum,pSelection) (This)->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection)
#define IWMReaderAdvanced3_SetReceiveSelectionCallbacks(This,fGetCallbacks) (This)->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks)
#define IWMReaderAdvanced3_GetReceiveSelectionCallbacks(This,pfGetCallbacks) (This)->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks)
#define IWMReaderAdvanced3_SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples) (This)->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples)
#define IWMReaderAdvanced3_GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples) (This)->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples)
#define IWMReaderAdvanced3_SetAllocateForOutput(This,dwOutputNum,fAllocate) (This)->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate)
#define IWMReaderAdvanced3_GetAllocateForOutput(This,dwOutputNum,pfAllocate) (This)->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate)
#define IWMReaderAdvanced3_SetAllocateForStream(This,wStreamNum,fAllocate) (This)->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate)
#define IWMReaderAdvanced3_GetAllocateForStream(This,dwStreamNum,pfAllocate) (This)->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate)
#define IWMReaderAdvanced3_GetStatistics(This,pStatistics) (This)->lpVtbl->GetStatistics(This,pStatistics)
#define IWMReaderAdvanced3_SetClientInfo(This,pClientInfo) (This)->lpVtbl->SetClientInfo(This,pClientInfo)
#define IWMReaderAdvanced3_GetMaxOutputSampleSize(This,dwOutput,pcbMax) (This)->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax)
#define IWMReaderAdvanced3_GetMaxStreamSampleSize(This,wStream,pcbMax) (This)->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax)
#define IWMReaderAdvanced3_NotifyLateDelivery(This,cnsLateness) (This)->lpVtbl->NotifyLateDelivery(This,cnsLateness)
/*** IWMReaderAdvanced2 methods ***/
#define IWMReaderAdvanced3_SetPlayMode(This,Mode) (This)->lpVtbl->SetPlayMode(This,Mode)
#define IWMReaderAdvanced3_GetPlayMode(This,pMode) (This)->lpVtbl->GetPlayMode(This,pMode)
#define IWMReaderAdvanced3_GetBufferProgress(This,pdwPercent,pcnsBuffering) (This)->lpVtbl->GetBufferProgress(This,pdwPercent,pcnsBuffering)
#define IWMReaderAdvanced3_GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload) (This)->lpVtbl->GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload)
#define IWMReaderAdvanced3_GetSaveAsProgress(This,pdwPercent) (This)->lpVtbl->GetSaveAsProgress(This,pdwPercent)
#define IWMReaderAdvanced3_SaveFileAs(This,pwszFilename) (This)->lpVtbl->SaveFileAs(This,pwszFilename)
#define IWMReaderAdvanced3_GetProtocolName(This,pwszProtocol,pcchProtocol) (This)->lpVtbl->GetProtocolName(This,pwszProtocol,pcchProtocol)
#define IWMReaderAdvanced3_StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext) (This)->lpVtbl->StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext)
#define IWMReaderAdvanced3_GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength) (This)->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength)
#define IWMReaderAdvanced3_SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength) (This)->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength)
#define IWMReaderAdvanced3_Preroll(This,cnsStart,cnsDuration,fRate) (This)->lpVtbl->Preroll(This,cnsStart,cnsDuration,fRate)
#define IWMReaderAdvanced3_SetLogClientID(This,fLogClientID) (This)->lpVtbl->SetLogClientID(This,fLogClientID)
#define IWMReaderAdvanced3_GetLogClientID(This,pfLogClientID) (This)->lpVtbl->GetLogClientID(This,pfLogClientID)
#define IWMReaderAdvanced3_StopBuffering(This) (This)->lpVtbl->StopBuffering(This)
#define IWMReaderAdvanced3_OpenStream(This,pStream,pCallback,pvContext) (This)->lpVtbl->OpenStream(This,pStream,pCallback,pvContext)
/*** IWMReaderAdvanced3 methods ***/
#define IWMReaderAdvanced3_StopNetStreaming(This) (This)->lpVtbl->StopNetStreaming(This)
#define IWMReaderAdvanced3_StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext) (This)->lpVtbl->StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderAdvanced3_QueryInterface(IWMReaderAdvanced3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderAdvanced3_AddRef(IWMReaderAdvanced3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderAdvanced3_Release(IWMReaderAdvanced3* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderAdvanced methods ***/
static inline HRESULT IWMReaderAdvanced3_SetUserProvidedClock(IWMReaderAdvanced3* This,WINBOOL fUserClock) {
    return This->lpVtbl->SetUserProvidedClock(This,fUserClock);
}
static inline HRESULT IWMReaderAdvanced3_GetUserProvidedClock(IWMReaderAdvanced3* This,WINBOOL *pfUserClock) {
    return This->lpVtbl->GetUserProvidedClock(This,pfUserClock);
}
static inline HRESULT IWMReaderAdvanced3_DeliverTime(IWMReaderAdvanced3* This,QWORD cnsTime) {
    return This->lpVtbl->DeliverTime(This,cnsTime);
}
static inline HRESULT IWMReaderAdvanced3_SetManualStreamSelection(IWMReaderAdvanced3* This,WINBOOL fSelection) {
    return This->lpVtbl->SetManualStreamSelection(This,fSelection);
}
static inline HRESULT IWMReaderAdvanced3_GetManualStreamSelection(IWMReaderAdvanced3* This,WINBOOL *pfSelection) {
    return This->lpVtbl->GetManualStreamSelection(This,pfSelection);
}
static inline HRESULT IWMReaderAdvanced3_SetStreamsSelected(IWMReaderAdvanced3* This,WORD cStreamCount,WORD *pwStreamNumbers,WMT_STREAM_SELECTION *pSelections) {
    return This->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections);
}
static inline HRESULT IWMReaderAdvanced3_GetStreamSelected(IWMReaderAdvanced3* This,WORD wStreamNum,WMT_STREAM_SELECTION *pSelection) {
    return This->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection);
}
static inline HRESULT IWMReaderAdvanced3_SetReceiveSelectionCallbacks(IWMReaderAdvanced3* This,WINBOOL fGetCallbacks) {
    return This->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced3_GetReceiveSelectionCallbacks(IWMReaderAdvanced3* This,WINBOOL *pfGetCallbacks) {
    return This->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced3_SetReceiveStreamSamples(IWMReaderAdvanced3* This,WORD wStreamNum,WINBOOL fReceiveStreamSamples) {
    return This->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced3_GetReceiveStreamSamples(IWMReaderAdvanced3* This,WORD wStreamNum,WINBOOL *pfReceiveStreamSamples) {
    return This->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced3_SetAllocateForOutput(IWMReaderAdvanced3* This,DWORD dwOutputNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced3_GetAllocateForOutput(IWMReaderAdvanced3* This,DWORD dwOutputNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced3_SetAllocateForStream(IWMReaderAdvanced3* This,WORD wStreamNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced3_GetAllocateForStream(IWMReaderAdvanced3* This,WORD dwStreamNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced3_GetStatistics(IWMReaderAdvanced3* This,WM_READER_STATISTICS *pStatistics) {
    return This->lpVtbl->GetStatistics(This,pStatistics);
}
static inline HRESULT IWMReaderAdvanced3_SetClientInfo(IWMReaderAdvanced3* This,WM_READER_CLIENTINFO *pClientInfo) {
    return This->lpVtbl->SetClientInfo(This,pClientInfo);
}
static inline HRESULT IWMReaderAdvanced3_GetMaxOutputSampleSize(IWMReaderAdvanced3* This,DWORD dwOutput,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax);
}
static inline HRESULT IWMReaderAdvanced3_GetMaxStreamSampleSize(IWMReaderAdvanced3* This,WORD wStream,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax);
}
static inline HRESULT IWMReaderAdvanced3_NotifyLateDelivery(IWMReaderAdvanced3* This,QWORD cnsLateness) {
    return This->lpVtbl->NotifyLateDelivery(This,cnsLateness);
}
/*** IWMReaderAdvanced2 methods ***/
static inline HRESULT IWMReaderAdvanced3_SetPlayMode(IWMReaderAdvanced3* This,WMT_PLAY_MODE Mode) {
    return This->lpVtbl->SetPlayMode(This,Mode);
}
static inline HRESULT IWMReaderAdvanced3_GetPlayMode(IWMReaderAdvanced3* This,WMT_PLAY_MODE *pMode) {
    return This->lpVtbl->GetPlayMode(This,pMode);
}
static inline HRESULT IWMReaderAdvanced3_GetBufferProgress(IWMReaderAdvanced3* This,DWORD *pdwPercent,QWORD *pcnsBuffering) {
    return This->lpVtbl->GetBufferProgress(This,pdwPercent,pcnsBuffering);
}
static inline HRESULT IWMReaderAdvanced3_GetDownloadProgress(IWMReaderAdvanced3* This,DWORD *pdwPercent,QWORD *pqwBytesDownloaded,QWORD *pcnsDownload) {
    return This->lpVtbl->GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload);
}
static inline HRESULT IWMReaderAdvanced3_GetSaveAsProgress(IWMReaderAdvanced3* This,DWORD *pdwPercent) {
    return This->lpVtbl->GetSaveAsProgress(This,pdwPercent);
}
static inline HRESULT IWMReaderAdvanced3_SaveFileAs(IWMReaderAdvanced3* This,const WCHAR *pwszFilename) {
    return This->lpVtbl->SaveFileAs(This,pwszFilename);
}
static inline HRESULT IWMReaderAdvanced3_GetProtocolName(IWMReaderAdvanced3* This,WCHAR *pwszProtocol,DWORD *pcchProtocol) {
    return This->lpVtbl->GetProtocolName(This,pwszProtocol,pcchProtocol);
}
static inline HRESULT IWMReaderAdvanced3_StartAtMarker(IWMReaderAdvanced3* This,WORD wMarkerIndex,QWORD cnsDuration,float fRate,void *pvContext) {
    return This->lpVtbl->StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext);
}
static inline HRESULT IWMReaderAdvanced3_GetOutputSetting(IWMReaderAdvanced3* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE *pType,BYTE *pValue,WORD *pcbLength) {
    return This->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength);
}
static inline HRESULT IWMReaderAdvanced3_SetOutputSetting(IWMReaderAdvanced3* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE Type,const BYTE *pValue,WORD cbLength) {
    return This->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength);
}
static inline HRESULT IWMReaderAdvanced3_Preroll(IWMReaderAdvanced3* This,QWORD cnsStart,QWORD cnsDuration,float fRate) {
    return This->lpVtbl->Preroll(This,cnsStart,cnsDuration,fRate);
}
static inline HRESULT IWMReaderAdvanced3_SetLogClientID(IWMReaderAdvanced3* This,WINBOOL fLogClientID) {
    return This->lpVtbl->SetLogClientID(This,fLogClientID);
}
static inline HRESULT IWMReaderAdvanced3_GetLogClientID(IWMReaderAdvanced3* This,WINBOOL *pfLogClientID) {
    return This->lpVtbl->GetLogClientID(This,pfLogClientID);
}
static inline HRESULT IWMReaderAdvanced3_StopBuffering(IWMReaderAdvanced3* This) {
    return This->lpVtbl->StopBuffering(This);
}
static inline HRESULT IWMReaderAdvanced3_OpenStream(IWMReaderAdvanced3* This,IStream *pStream,IWMReaderCallback *pCallback,void *pvContext) {
    return This->lpVtbl->OpenStream(This,pStream,pCallback,pvContext);
}
/*** IWMReaderAdvanced3 methods ***/
static inline HRESULT IWMReaderAdvanced3_StopNetStreaming(IWMReaderAdvanced3* This) {
    return This->lpVtbl->StopNetStreaming(This);
}
static inline HRESULT IWMReaderAdvanced3_StartAtPosition(IWMReaderAdvanced3* This,WORD wStreamNum,void *pvOffsetStart,void *pvDuration,WMT_OFFSET_FORMAT dwOffsetFormat,float fRate,void *pvContext) {
    return This->lpVtbl->StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext);
}
#endif
#endif

#endif


#endif  /* __IWMReaderAdvanced3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderAdvanced4 interface
 */
#ifndef __IWMReaderAdvanced4_INTERFACE_DEFINED__
#define __IWMReaderAdvanced4_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderAdvanced4, 0x945a76a2, 0x12ae, 0x4d48, 0xbd,0x3c, 0xcd,0x1d,0x90,0x39,0x9b,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("945a76a2-12ae-4d48-bd3c-cd1d90399b85")
IWMReaderAdvanced4 : public IWMReaderAdvanced3
{
    virtual HRESULT STDMETHODCALLTYPE GetLanguageCount(
        DWORD dwOutputNum,
        WORD *pwLanguageCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguage(
        DWORD dwOutputNum,
        WORD wLanguage,
        WCHAR *pwszLanguageString,
        WORD *pcchLanguageStringLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaxSpeedFactor(
        double *pdblFactor) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsUsingFastCache(
        WINBOOL *pfUsingFastCache) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddLogParam(
        LPCWSTR wszNameSpace,
        LPCWSTR wszName,
        LPCWSTR wszValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendLogParams(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CanSaveFileAs(
        WINBOOL *pfCanSave) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelSaveFileAs(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetURL(
        WCHAR *pwszURL,
        DWORD *pcchURL) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderAdvanced4, 0x945a76a2, 0x12ae, 0x4d48, 0xbd,0x3c, 0xcd,0x1d,0x90,0x39,0x9b,0x85)
#endif
#else
typedef struct IWMReaderAdvanced4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderAdvanced4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderAdvanced4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderAdvanced4 *This);

    /*** IWMReaderAdvanced methods ***/
    HRESULT (STDMETHODCALLTYPE *SetUserProvidedClock)(
        IWMReaderAdvanced4 *This,
        WINBOOL fUserClock);

    HRESULT (STDMETHODCALLTYPE *GetUserProvidedClock)(
        IWMReaderAdvanced4 *This,
        WINBOOL *pfUserClock);

    HRESULT (STDMETHODCALLTYPE *DeliverTime)(
        IWMReaderAdvanced4 *This,
        QWORD cnsTime);

    HRESULT (STDMETHODCALLTYPE *SetManualStreamSelection)(
        IWMReaderAdvanced4 *This,
        WINBOOL fSelection);

    HRESULT (STDMETHODCALLTYPE *GetManualStreamSelection)(
        IWMReaderAdvanced4 *This,
        WINBOOL *pfSelection);

    HRESULT (STDMETHODCALLTYPE *SetStreamsSelected)(
        IWMReaderAdvanced4 *This,
        WORD cStreamCount,
        WORD *pwStreamNumbers,
        WMT_STREAM_SELECTION *pSelections);

    HRESULT (STDMETHODCALLTYPE *GetStreamSelected)(
        IWMReaderAdvanced4 *This,
        WORD wStreamNum,
        WMT_STREAM_SELECTION *pSelection);

    HRESULT (STDMETHODCALLTYPE *SetReceiveSelectionCallbacks)(
        IWMReaderAdvanced4 *This,
        WINBOOL fGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *GetReceiveSelectionCallbacks)(
        IWMReaderAdvanced4 *This,
        WINBOOL *pfGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *SetReceiveStreamSamples)(
        IWMReaderAdvanced4 *This,
        WORD wStreamNum,
        WINBOOL fReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *GetReceiveStreamSamples)(
        IWMReaderAdvanced4 *This,
        WORD wStreamNum,
        WINBOOL *pfReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForOutput)(
        IWMReaderAdvanced4 *This,
        DWORD dwOutputNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForOutput)(
        IWMReaderAdvanced4 *This,
        DWORD dwOutputNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForStream)(
        IWMReaderAdvanced4 *This,
        WORD wStreamNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForStream)(
        IWMReaderAdvanced4 *This,
        WORD dwStreamNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IWMReaderAdvanced4 *This,
        WM_READER_STATISTICS *pStatistics);

    HRESULT (STDMETHODCALLTYPE *SetClientInfo)(
        IWMReaderAdvanced4 *This,
        WM_READER_CLIENTINFO *pClientInfo);

    HRESULT (STDMETHODCALLTYPE *GetMaxOutputSampleSize)(
        IWMReaderAdvanced4 *This,
        DWORD dwOutput,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *GetMaxStreamSampleSize)(
        IWMReaderAdvanced4 *This,
        WORD wStream,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *NotifyLateDelivery)(
        IWMReaderAdvanced4 *This,
        QWORD cnsLateness);

    /*** IWMReaderAdvanced2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPlayMode)(
        IWMReaderAdvanced4 *This,
        WMT_PLAY_MODE Mode);

    HRESULT (STDMETHODCALLTYPE *GetPlayMode)(
        IWMReaderAdvanced4 *This,
        WMT_PLAY_MODE *pMode);

    HRESULT (STDMETHODCALLTYPE *GetBufferProgress)(
        IWMReaderAdvanced4 *This,
        DWORD *pdwPercent,
        QWORD *pcnsBuffering);

    HRESULT (STDMETHODCALLTYPE *GetDownloadProgress)(
        IWMReaderAdvanced4 *This,
        DWORD *pdwPercent,
        QWORD *pqwBytesDownloaded,
        QWORD *pcnsDownload);

    HRESULT (STDMETHODCALLTYPE *GetSaveAsProgress)(
        IWMReaderAdvanced4 *This,
        DWORD *pdwPercent);

    HRESULT (STDMETHODCALLTYPE *SaveFileAs)(
        IWMReaderAdvanced4 *This,
        const WCHAR *pwszFilename);

    HRESULT (STDMETHODCALLTYPE *GetProtocolName)(
        IWMReaderAdvanced4 *This,
        WCHAR *pwszProtocol,
        DWORD *pcchProtocol);

    HRESULT (STDMETHODCALLTYPE *StartAtMarker)(
        IWMReaderAdvanced4 *This,
        WORD wMarkerIndex,
        QWORD cnsDuration,
        float fRate,
        void *pvContext);

    HRESULT (STDMETHODCALLTYPE *GetOutputSetting)(
        IWMReaderAdvanced4 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength);

    HRESULT (STDMETHODCALLTYPE *SetOutputSetting)(
        IWMReaderAdvanced4 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength);

    HRESULT (STDMETHODCALLTYPE *Preroll)(
        IWMReaderAdvanced4 *This,
        QWORD cnsStart,
        QWORD cnsDuration,
        float fRate);

    HRESULT (STDMETHODCALLTYPE *SetLogClientID)(
        IWMReaderAdvanced4 *This,
        WINBOOL fLogClientID);

    HRESULT (STDMETHODCALLTYPE *GetLogClientID)(
        IWMReaderAdvanced4 *This,
        WINBOOL *pfLogClientID);

    HRESULT (STDMETHODCALLTYPE *StopBuffering)(
        IWMReaderAdvanced4 *This);

    HRESULT (STDMETHODCALLTYPE *OpenStream)(
        IWMReaderAdvanced4 *This,
        IStream *pStream,
        IWMReaderCallback *pCallback,
        void *pvContext);

    /*** IWMReaderAdvanced3 methods ***/
    HRESULT (STDMETHODCALLTYPE *StopNetStreaming)(
        IWMReaderAdvanced4 *This);

    HRESULT (STDMETHODCALLTYPE *StartAtPosition)(
        IWMReaderAdvanced4 *This,
        WORD wStreamNum,
        void *pvOffsetStart,
        void *pvDuration,
        WMT_OFFSET_FORMAT dwOffsetFormat,
        float fRate,
        void *pvContext);

    /*** IWMReaderAdvanced4 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLanguageCount)(
        IWMReaderAdvanced4 *This,
        DWORD dwOutputNum,
        WORD *pwLanguageCount);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IWMReaderAdvanced4 *This,
        DWORD dwOutputNum,
        WORD wLanguage,
        WCHAR *pwszLanguageString,
        WORD *pcchLanguageStringLength);

    HRESULT (STDMETHODCALLTYPE *GetMaxSpeedFactor)(
        IWMReaderAdvanced4 *This,
        double *pdblFactor);

    HRESULT (STDMETHODCALLTYPE *IsUsingFastCache)(
        IWMReaderAdvanced4 *This,
        WINBOOL *pfUsingFastCache);

    HRESULT (STDMETHODCALLTYPE *AddLogParam)(
        IWMReaderAdvanced4 *This,
        LPCWSTR wszNameSpace,
        LPCWSTR wszName,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SendLogParams)(
        IWMReaderAdvanced4 *This);

    HRESULT (STDMETHODCALLTYPE *CanSaveFileAs)(
        IWMReaderAdvanced4 *This,
        WINBOOL *pfCanSave);

    HRESULT (STDMETHODCALLTYPE *CancelSaveFileAs)(
        IWMReaderAdvanced4 *This);

    HRESULT (STDMETHODCALLTYPE *GetURL)(
        IWMReaderAdvanced4 *This,
        WCHAR *pwszURL,
        DWORD *pcchURL);

    END_INTERFACE
} IWMReaderAdvanced4Vtbl;

interface IWMReaderAdvanced4 {
    CONST_VTBL IWMReaderAdvanced4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderAdvanced4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderAdvanced4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderAdvanced4_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderAdvanced methods ***/
#define IWMReaderAdvanced4_SetUserProvidedClock(This,fUserClock) (This)->lpVtbl->SetUserProvidedClock(This,fUserClock)
#define IWMReaderAdvanced4_GetUserProvidedClock(This,pfUserClock) (This)->lpVtbl->GetUserProvidedClock(This,pfUserClock)
#define IWMReaderAdvanced4_DeliverTime(This,cnsTime) (This)->lpVtbl->DeliverTime(This,cnsTime)
#define IWMReaderAdvanced4_SetManualStreamSelection(This,fSelection) (This)->lpVtbl->SetManualStreamSelection(This,fSelection)
#define IWMReaderAdvanced4_GetManualStreamSelection(This,pfSelection) (This)->lpVtbl->GetManualStreamSelection(This,pfSelection)
#define IWMReaderAdvanced4_SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections) (This)->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections)
#define IWMReaderAdvanced4_GetStreamSelected(This,wStreamNum,pSelection) (This)->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection)
#define IWMReaderAdvanced4_SetReceiveSelectionCallbacks(This,fGetCallbacks) (This)->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks)
#define IWMReaderAdvanced4_GetReceiveSelectionCallbacks(This,pfGetCallbacks) (This)->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks)
#define IWMReaderAdvanced4_SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples) (This)->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples)
#define IWMReaderAdvanced4_GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples) (This)->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples)
#define IWMReaderAdvanced4_SetAllocateForOutput(This,dwOutputNum,fAllocate) (This)->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate)
#define IWMReaderAdvanced4_GetAllocateForOutput(This,dwOutputNum,pfAllocate) (This)->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate)
#define IWMReaderAdvanced4_SetAllocateForStream(This,wStreamNum,fAllocate) (This)->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate)
#define IWMReaderAdvanced4_GetAllocateForStream(This,dwStreamNum,pfAllocate) (This)->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate)
#define IWMReaderAdvanced4_GetStatistics(This,pStatistics) (This)->lpVtbl->GetStatistics(This,pStatistics)
#define IWMReaderAdvanced4_SetClientInfo(This,pClientInfo) (This)->lpVtbl->SetClientInfo(This,pClientInfo)
#define IWMReaderAdvanced4_GetMaxOutputSampleSize(This,dwOutput,pcbMax) (This)->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax)
#define IWMReaderAdvanced4_GetMaxStreamSampleSize(This,wStream,pcbMax) (This)->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax)
#define IWMReaderAdvanced4_NotifyLateDelivery(This,cnsLateness) (This)->lpVtbl->NotifyLateDelivery(This,cnsLateness)
/*** IWMReaderAdvanced2 methods ***/
#define IWMReaderAdvanced4_SetPlayMode(This,Mode) (This)->lpVtbl->SetPlayMode(This,Mode)
#define IWMReaderAdvanced4_GetPlayMode(This,pMode) (This)->lpVtbl->GetPlayMode(This,pMode)
#define IWMReaderAdvanced4_GetBufferProgress(This,pdwPercent,pcnsBuffering) (This)->lpVtbl->GetBufferProgress(This,pdwPercent,pcnsBuffering)
#define IWMReaderAdvanced4_GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload) (This)->lpVtbl->GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload)
#define IWMReaderAdvanced4_GetSaveAsProgress(This,pdwPercent) (This)->lpVtbl->GetSaveAsProgress(This,pdwPercent)
#define IWMReaderAdvanced4_SaveFileAs(This,pwszFilename) (This)->lpVtbl->SaveFileAs(This,pwszFilename)
#define IWMReaderAdvanced4_GetProtocolName(This,pwszProtocol,pcchProtocol) (This)->lpVtbl->GetProtocolName(This,pwszProtocol,pcchProtocol)
#define IWMReaderAdvanced4_StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext) (This)->lpVtbl->StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext)
#define IWMReaderAdvanced4_GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength) (This)->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength)
#define IWMReaderAdvanced4_SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength) (This)->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength)
#define IWMReaderAdvanced4_Preroll(This,cnsStart,cnsDuration,fRate) (This)->lpVtbl->Preroll(This,cnsStart,cnsDuration,fRate)
#define IWMReaderAdvanced4_SetLogClientID(This,fLogClientID) (This)->lpVtbl->SetLogClientID(This,fLogClientID)
#define IWMReaderAdvanced4_GetLogClientID(This,pfLogClientID) (This)->lpVtbl->GetLogClientID(This,pfLogClientID)
#define IWMReaderAdvanced4_StopBuffering(This) (This)->lpVtbl->StopBuffering(This)
#define IWMReaderAdvanced4_OpenStream(This,pStream,pCallback,pvContext) (This)->lpVtbl->OpenStream(This,pStream,pCallback,pvContext)
/*** IWMReaderAdvanced3 methods ***/
#define IWMReaderAdvanced4_StopNetStreaming(This) (This)->lpVtbl->StopNetStreaming(This)
#define IWMReaderAdvanced4_StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext) (This)->lpVtbl->StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext)
/*** IWMReaderAdvanced4 methods ***/
#define IWMReaderAdvanced4_GetLanguageCount(This,dwOutputNum,pwLanguageCount) (This)->lpVtbl->GetLanguageCount(This,dwOutputNum,pwLanguageCount)
#define IWMReaderAdvanced4_GetLanguage(This,dwOutputNum,wLanguage,pwszLanguageString,pcchLanguageStringLength) (This)->lpVtbl->GetLanguage(This,dwOutputNum,wLanguage,pwszLanguageString,pcchLanguageStringLength)
#define IWMReaderAdvanced4_GetMaxSpeedFactor(This,pdblFactor) (This)->lpVtbl->GetMaxSpeedFactor(This,pdblFactor)
#define IWMReaderAdvanced4_IsUsingFastCache(This,pfUsingFastCache) (This)->lpVtbl->IsUsingFastCache(This,pfUsingFastCache)
#define IWMReaderAdvanced4_AddLogParam(This,wszNameSpace,wszName,wszValue) (This)->lpVtbl->AddLogParam(This,wszNameSpace,wszName,wszValue)
#define IWMReaderAdvanced4_SendLogParams(This) (This)->lpVtbl->SendLogParams(This)
#define IWMReaderAdvanced4_CanSaveFileAs(This,pfCanSave) (This)->lpVtbl->CanSaveFileAs(This,pfCanSave)
#define IWMReaderAdvanced4_CancelSaveFileAs(This) (This)->lpVtbl->CancelSaveFileAs(This)
#define IWMReaderAdvanced4_GetURL(This,pwszURL,pcchURL) (This)->lpVtbl->GetURL(This,pwszURL,pcchURL)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderAdvanced4_QueryInterface(IWMReaderAdvanced4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderAdvanced4_AddRef(IWMReaderAdvanced4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderAdvanced4_Release(IWMReaderAdvanced4* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderAdvanced methods ***/
static inline HRESULT IWMReaderAdvanced4_SetUserProvidedClock(IWMReaderAdvanced4* This,WINBOOL fUserClock) {
    return This->lpVtbl->SetUserProvidedClock(This,fUserClock);
}
static inline HRESULT IWMReaderAdvanced4_GetUserProvidedClock(IWMReaderAdvanced4* This,WINBOOL *pfUserClock) {
    return This->lpVtbl->GetUserProvidedClock(This,pfUserClock);
}
static inline HRESULT IWMReaderAdvanced4_DeliverTime(IWMReaderAdvanced4* This,QWORD cnsTime) {
    return This->lpVtbl->DeliverTime(This,cnsTime);
}
static inline HRESULT IWMReaderAdvanced4_SetManualStreamSelection(IWMReaderAdvanced4* This,WINBOOL fSelection) {
    return This->lpVtbl->SetManualStreamSelection(This,fSelection);
}
static inline HRESULT IWMReaderAdvanced4_GetManualStreamSelection(IWMReaderAdvanced4* This,WINBOOL *pfSelection) {
    return This->lpVtbl->GetManualStreamSelection(This,pfSelection);
}
static inline HRESULT IWMReaderAdvanced4_SetStreamsSelected(IWMReaderAdvanced4* This,WORD cStreamCount,WORD *pwStreamNumbers,WMT_STREAM_SELECTION *pSelections) {
    return This->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections);
}
static inline HRESULT IWMReaderAdvanced4_GetStreamSelected(IWMReaderAdvanced4* This,WORD wStreamNum,WMT_STREAM_SELECTION *pSelection) {
    return This->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection);
}
static inline HRESULT IWMReaderAdvanced4_SetReceiveSelectionCallbacks(IWMReaderAdvanced4* This,WINBOOL fGetCallbacks) {
    return This->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced4_GetReceiveSelectionCallbacks(IWMReaderAdvanced4* This,WINBOOL *pfGetCallbacks) {
    return This->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced4_SetReceiveStreamSamples(IWMReaderAdvanced4* This,WORD wStreamNum,WINBOOL fReceiveStreamSamples) {
    return This->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced4_GetReceiveStreamSamples(IWMReaderAdvanced4* This,WORD wStreamNum,WINBOOL *pfReceiveStreamSamples) {
    return This->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced4_SetAllocateForOutput(IWMReaderAdvanced4* This,DWORD dwOutputNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced4_GetAllocateForOutput(IWMReaderAdvanced4* This,DWORD dwOutputNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced4_SetAllocateForStream(IWMReaderAdvanced4* This,WORD wStreamNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced4_GetAllocateForStream(IWMReaderAdvanced4* This,WORD dwStreamNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced4_GetStatistics(IWMReaderAdvanced4* This,WM_READER_STATISTICS *pStatistics) {
    return This->lpVtbl->GetStatistics(This,pStatistics);
}
static inline HRESULT IWMReaderAdvanced4_SetClientInfo(IWMReaderAdvanced4* This,WM_READER_CLIENTINFO *pClientInfo) {
    return This->lpVtbl->SetClientInfo(This,pClientInfo);
}
static inline HRESULT IWMReaderAdvanced4_GetMaxOutputSampleSize(IWMReaderAdvanced4* This,DWORD dwOutput,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax);
}
static inline HRESULT IWMReaderAdvanced4_GetMaxStreamSampleSize(IWMReaderAdvanced4* This,WORD wStream,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax);
}
static inline HRESULT IWMReaderAdvanced4_NotifyLateDelivery(IWMReaderAdvanced4* This,QWORD cnsLateness) {
    return This->lpVtbl->NotifyLateDelivery(This,cnsLateness);
}
/*** IWMReaderAdvanced2 methods ***/
static inline HRESULT IWMReaderAdvanced4_SetPlayMode(IWMReaderAdvanced4* This,WMT_PLAY_MODE Mode) {
    return This->lpVtbl->SetPlayMode(This,Mode);
}
static inline HRESULT IWMReaderAdvanced4_GetPlayMode(IWMReaderAdvanced4* This,WMT_PLAY_MODE *pMode) {
    return This->lpVtbl->GetPlayMode(This,pMode);
}
static inline HRESULT IWMReaderAdvanced4_GetBufferProgress(IWMReaderAdvanced4* This,DWORD *pdwPercent,QWORD *pcnsBuffering) {
    return This->lpVtbl->GetBufferProgress(This,pdwPercent,pcnsBuffering);
}
static inline HRESULT IWMReaderAdvanced4_GetDownloadProgress(IWMReaderAdvanced4* This,DWORD *pdwPercent,QWORD *pqwBytesDownloaded,QWORD *pcnsDownload) {
    return This->lpVtbl->GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload);
}
static inline HRESULT IWMReaderAdvanced4_GetSaveAsProgress(IWMReaderAdvanced4* This,DWORD *pdwPercent) {
    return This->lpVtbl->GetSaveAsProgress(This,pdwPercent);
}
static inline HRESULT IWMReaderAdvanced4_SaveFileAs(IWMReaderAdvanced4* This,const WCHAR *pwszFilename) {
    return This->lpVtbl->SaveFileAs(This,pwszFilename);
}
static inline HRESULT IWMReaderAdvanced4_GetProtocolName(IWMReaderAdvanced4* This,WCHAR *pwszProtocol,DWORD *pcchProtocol) {
    return This->lpVtbl->GetProtocolName(This,pwszProtocol,pcchProtocol);
}
static inline HRESULT IWMReaderAdvanced4_StartAtMarker(IWMReaderAdvanced4* This,WORD wMarkerIndex,QWORD cnsDuration,float fRate,void *pvContext) {
    return This->lpVtbl->StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext);
}
static inline HRESULT IWMReaderAdvanced4_GetOutputSetting(IWMReaderAdvanced4* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE *pType,BYTE *pValue,WORD *pcbLength) {
    return This->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength);
}
static inline HRESULT IWMReaderAdvanced4_SetOutputSetting(IWMReaderAdvanced4* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE Type,const BYTE *pValue,WORD cbLength) {
    return This->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength);
}
static inline HRESULT IWMReaderAdvanced4_Preroll(IWMReaderAdvanced4* This,QWORD cnsStart,QWORD cnsDuration,float fRate) {
    return This->lpVtbl->Preroll(This,cnsStart,cnsDuration,fRate);
}
static inline HRESULT IWMReaderAdvanced4_SetLogClientID(IWMReaderAdvanced4* This,WINBOOL fLogClientID) {
    return This->lpVtbl->SetLogClientID(This,fLogClientID);
}
static inline HRESULT IWMReaderAdvanced4_GetLogClientID(IWMReaderAdvanced4* This,WINBOOL *pfLogClientID) {
    return This->lpVtbl->GetLogClientID(This,pfLogClientID);
}
static inline HRESULT IWMReaderAdvanced4_StopBuffering(IWMReaderAdvanced4* This) {
    return This->lpVtbl->StopBuffering(This);
}
static inline HRESULT IWMReaderAdvanced4_OpenStream(IWMReaderAdvanced4* This,IStream *pStream,IWMReaderCallback *pCallback,void *pvContext) {
    return This->lpVtbl->OpenStream(This,pStream,pCallback,pvContext);
}
/*** IWMReaderAdvanced3 methods ***/
static inline HRESULT IWMReaderAdvanced4_StopNetStreaming(IWMReaderAdvanced4* This) {
    return This->lpVtbl->StopNetStreaming(This);
}
static inline HRESULT IWMReaderAdvanced4_StartAtPosition(IWMReaderAdvanced4* This,WORD wStreamNum,void *pvOffsetStart,void *pvDuration,WMT_OFFSET_FORMAT dwOffsetFormat,float fRate,void *pvContext) {
    return This->lpVtbl->StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext);
}
/*** IWMReaderAdvanced4 methods ***/
static inline HRESULT IWMReaderAdvanced4_GetLanguageCount(IWMReaderAdvanced4* This,DWORD dwOutputNum,WORD *pwLanguageCount) {
    return This->lpVtbl->GetLanguageCount(This,dwOutputNum,pwLanguageCount);
}
static inline HRESULT IWMReaderAdvanced4_GetLanguage(IWMReaderAdvanced4* This,DWORD dwOutputNum,WORD wLanguage,WCHAR *pwszLanguageString,WORD *pcchLanguageStringLength) {
    return This->lpVtbl->GetLanguage(This,dwOutputNum,wLanguage,pwszLanguageString,pcchLanguageStringLength);
}
static inline HRESULT IWMReaderAdvanced4_GetMaxSpeedFactor(IWMReaderAdvanced4* This,double *pdblFactor) {
    return This->lpVtbl->GetMaxSpeedFactor(This,pdblFactor);
}
static inline HRESULT IWMReaderAdvanced4_IsUsingFastCache(IWMReaderAdvanced4* This,WINBOOL *pfUsingFastCache) {
    return This->lpVtbl->IsUsingFastCache(This,pfUsingFastCache);
}
static inline HRESULT IWMReaderAdvanced4_AddLogParam(IWMReaderAdvanced4* This,LPCWSTR wszNameSpace,LPCWSTR wszName,LPCWSTR wszValue) {
    return This->lpVtbl->AddLogParam(This,wszNameSpace,wszName,wszValue);
}
static inline HRESULT IWMReaderAdvanced4_SendLogParams(IWMReaderAdvanced4* This) {
    return This->lpVtbl->SendLogParams(This);
}
static inline HRESULT IWMReaderAdvanced4_CanSaveFileAs(IWMReaderAdvanced4* This,WINBOOL *pfCanSave) {
    return This->lpVtbl->CanSaveFileAs(This,pfCanSave);
}
static inline HRESULT IWMReaderAdvanced4_CancelSaveFileAs(IWMReaderAdvanced4* This) {
    return This->lpVtbl->CancelSaveFileAs(This);
}
static inline HRESULT IWMReaderAdvanced4_GetURL(IWMReaderAdvanced4* This,WCHAR *pwszURL,DWORD *pcchURL) {
    return This->lpVtbl->GetURL(This,pwszURL,pcchURL);
}
#endif
#endif

#endif


#endif  /* __IWMReaderAdvanced4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderAdvanced5 interface
 */
#ifndef __IWMReaderAdvanced5_INTERFACE_DEFINED__
#define __IWMReaderAdvanced5_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderAdvanced5, 0x24c44db0, 0x55d1, 0x49ae, 0xa5,0xcc, 0xf1,0x38,0x15,0xe3,0x63,0x63);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("24c44db0-55d1-49ae-a5cc-f13815e36363")
IWMReaderAdvanced5 : public IWMReaderAdvanced4
{
    virtual HRESULT STDMETHODCALLTYPE SetPlayerHook(
        DWORD dwOutputNum,
        IWMPlayerHook *pHook) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderAdvanced5, 0x24c44db0, 0x55d1, 0x49ae, 0xa5,0xcc, 0xf1,0x38,0x15,0xe3,0x63,0x63)
#endif
#else
typedef struct IWMReaderAdvanced5Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderAdvanced5 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderAdvanced5 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderAdvanced5 *This);

    /*** IWMReaderAdvanced methods ***/
    HRESULT (STDMETHODCALLTYPE *SetUserProvidedClock)(
        IWMReaderAdvanced5 *This,
        WINBOOL fUserClock);

    HRESULT (STDMETHODCALLTYPE *GetUserProvidedClock)(
        IWMReaderAdvanced5 *This,
        WINBOOL *pfUserClock);

    HRESULT (STDMETHODCALLTYPE *DeliverTime)(
        IWMReaderAdvanced5 *This,
        QWORD cnsTime);

    HRESULT (STDMETHODCALLTYPE *SetManualStreamSelection)(
        IWMReaderAdvanced5 *This,
        WINBOOL fSelection);

    HRESULT (STDMETHODCALLTYPE *GetManualStreamSelection)(
        IWMReaderAdvanced5 *This,
        WINBOOL *pfSelection);

    HRESULT (STDMETHODCALLTYPE *SetStreamsSelected)(
        IWMReaderAdvanced5 *This,
        WORD cStreamCount,
        WORD *pwStreamNumbers,
        WMT_STREAM_SELECTION *pSelections);

    HRESULT (STDMETHODCALLTYPE *GetStreamSelected)(
        IWMReaderAdvanced5 *This,
        WORD wStreamNum,
        WMT_STREAM_SELECTION *pSelection);

    HRESULT (STDMETHODCALLTYPE *SetReceiveSelectionCallbacks)(
        IWMReaderAdvanced5 *This,
        WINBOOL fGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *GetReceiveSelectionCallbacks)(
        IWMReaderAdvanced5 *This,
        WINBOOL *pfGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *SetReceiveStreamSamples)(
        IWMReaderAdvanced5 *This,
        WORD wStreamNum,
        WINBOOL fReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *GetReceiveStreamSamples)(
        IWMReaderAdvanced5 *This,
        WORD wStreamNum,
        WINBOOL *pfReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForOutput)(
        IWMReaderAdvanced5 *This,
        DWORD dwOutputNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForOutput)(
        IWMReaderAdvanced5 *This,
        DWORD dwOutputNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForStream)(
        IWMReaderAdvanced5 *This,
        WORD wStreamNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForStream)(
        IWMReaderAdvanced5 *This,
        WORD dwStreamNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IWMReaderAdvanced5 *This,
        WM_READER_STATISTICS *pStatistics);

    HRESULT (STDMETHODCALLTYPE *SetClientInfo)(
        IWMReaderAdvanced5 *This,
        WM_READER_CLIENTINFO *pClientInfo);

    HRESULT (STDMETHODCALLTYPE *GetMaxOutputSampleSize)(
        IWMReaderAdvanced5 *This,
        DWORD dwOutput,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *GetMaxStreamSampleSize)(
        IWMReaderAdvanced5 *This,
        WORD wStream,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *NotifyLateDelivery)(
        IWMReaderAdvanced5 *This,
        QWORD cnsLateness);

    /*** IWMReaderAdvanced2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPlayMode)(
        IWMReaderAdvanced5 *This,
        WMT_PLAY_MODE Mode);

    HRESULT (STDMETHODCALLTYPE *GetPlayMode)(
        IWMReaderAdvanced5 *This,
        WMT_PLAY_MODE *pMode);

    HRESULT (STDMETHODCALLTYPE *GetBufferProgress)(
        IWMReaderAdvanced5 *This,
        DWORD *pdwPercent,
        QWORD *pcnsBuffering);

    HRESULT (STDMETHODCALLTYPE *GetDownloadProgress)(
        IWMReaderAdvanced5 *This,
        DWORD *pdwPercent,
        QWORD *pqwBytesDownloaded,
        QWORD *pcnsDownload);

    HRESULT (STDMETHODCALLTYPE *GetSaveAsProgress)(
        IWMReaderAdvanced5 *This,
        DWORD *pdwPercent);

    HRESULT (STDMETHODCALLTYPE *SaveFileAs)(
        IWMReaderAdvanced5 *This,
        const WCHAR *pwszFilename);

    HRESULT (STDMETHODCALLTYPE *GetProtocolName)(
        IWMReaderAdvanced5 *This,
        WCHAR *pwszProtocol,
        DWORD *pcchProtocol);

    HRESULT (STDMETHODCALLTYPE *StartAtMarker)(
        IWMReaderAdvanced5 *This,
        WORD wMarkerIndex,
        QWORD cnsDuration,
        float fRate,
        void *pvContext);

    HRESULT (STDMETHODCALLTYPE *GetOutputSetting)(
        IWMReaderAdvanced5 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength);

    HRESULT (STDMETHODCALLTYPE *SetOutputSetting)(
        IWMReaderAdvanced5 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength);

    HRESULT (STDMETHODCALLTYPE *Preroll)(
        IWMReaderAdvanced5 *This,
        QWORD cnsStart,
        QWORD cnsDuration,
        float fRate);

    HRESULT (STDMETHODCALLTYPE *SetLogClientID)(
        IWMReaderAdvanced5 *This,
        WINBOOL fLogClientID);

    HRESULT (STDMETHODCALLTYPE *GetLogClientID)(
        IWMReaderAdvanced5 *This,
        WINBOOL *pfLogClientID);

    HRESULT (STDMETHODCALLTYPE *StopBuffering)(
        IWMReaderAdvanced5 *This);

    HRESULT (STDMETHODCALLTYPE *OpenStream)(
        IWMReaderAdvanced5 *This,
        IStream *pStream,
        IWMReaderCallback *pCallback,
        void *pvContext);

    /*** IWMReaderAdvanced3 methods ***/
    HRESULT (STDMETHODCALLTYPE *StopNetStreaming)(
        IWMReaderAdvanced5 *This);

    HRESULT (STDMETHODCALLTYPE *StartAtPosition)(
        IWMReaderAdvanced5 *This,
        WORD wStreamNum,
        void *pvOffsetStart,
        void *pvDuration,
        WMT_OFFSET_FORMAT dwOffsetFormat,
        float fRate,
        void *pvContext);

    /*** IWMReaderAdvanced4 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLanguageCount)(
        IWMReaderAdvanced5 *This,
        DWORD dwOutputNum,
        WORD *pwLanguageCount);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IWMReaderAdvanced5 *This,
        DWORD dwOutputNum,
        WORD wLanguage,
        WCHAR *pwszLanguageString,
        WORD *pcchLanguageStringLength);

    HRESULT (STDMETHODCALLTYPE *GetMaxSpeedFactor)(
        IWMReaderAdvanced5 *This,
        double *pdblFactor);

    HRESULT (STDMETHODCALLTYPE *IsUsingFastCache)(
        IWMReaderAdvanced5 *This,
        WINBOOL *pfUsingFastCache);

    HRESULT (STDMETHODCALLTYPE *AddLogParam)(
        IWMReaderAdvanced5 *This,
        LPCWSTR wszNameSpace,
        LPCWSTR wszName,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SendLogParams)(
        IWMReaderAdvanced5 *This);

    HRESULT (STDMETHODCALLTYPE *CanSaveFileAs)(
        IWMReaderAdvanced5 *This,
        WINBOOL *pfCanSave);

    HRESULT (STDMETHODCALLTYPE *CancelSaveFileAs)(
        IWMReaderAdvanced5 *This);

    HRESULT (STDMETHODCALLTYPE *GetURL)(
        IWMReaderAdvanced5 *This,
        WCHAR *pwszURL,
        DWORD *pcchURL);

    /*** IWMReaderAdvanced5 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPlayerHook)(
        IWMReaderAdvanced5 *This,
        DWORD dwOutputNum,
        IWMPlayerHook *pHook);

    END_INTERFACE
} IWMReaderAdvanced5Vtbl;

interface IWMReaderAdvanced5 {
    CONST_VTBL IWMReaderAdvanced5Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderAdvanced5_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderAdvanced5_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderAdvanced5_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderAdvanced methods ***/
#define IWMReaderAdvanced5_SetUserProvidedClock(This,fUserClock) (This)->lpVtbl->SetUserProvidedClock(This,fUserClock)
#define IWMReaderAdvanced5_GetUserProvidedClock(This,pfUserClock) (This)->lpVtbl->GetUserProvidedClock(This,pfUserClock)
#define IWMReaderAdvanced5_DeliverTime(This,cnsTime) (This)->lpVtbl->DeliverTime(This,cnsTime)
#define IWMReaderAdvanced5_SetManualStreamSelection(This,fSelection) (This)->lpVtbl->SetManualStreamSelection(This,fSelection)
#define IWMReaderAdvanced5_GetManualStreamSelection(This,pfSelection) (This)->lpVtbl->GetManualStreamSelection(This,pfSelection)
#define IWMReaderAdvanced5_SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections) (This)->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections)
#define IWMReaderAdvanced5_GetStreamSelected(This,wStreamNum,pSelection) (This)->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection)
#define IWMReaderAdvanced5_SetReceiveSelectionCallbacks(This,fGetCallbacks) (This)->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks)
#define IWMReaderAdvanced5_GetReceiveSelectionCallbacks(This,pfGetCallbacks) (This)->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks)
#define IWMReaderAdvanced5_SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples) (This)->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples)
#define IWMReaderAdvanced5_GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples) (This)->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples)
#define IWMReaderAdvanced5_SetAllocateForOutput(This,dwOutputNum,fAllocate) (This)->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate)
#define IWMReaderAdvanced5_GetAllocateForOutput(This,dwOutputNum,pfAllocate) (This)->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate)
#define IWMReaderAdvanced5_SetAllocateForStream(This,wStreamNum,fAllocate) (This)->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate)
#define IWMReaderAdvanced5_GetAllocateForStream(This,dwStreamNum,pfAllocate) (This)->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate)
#define IWMReaderAdvanced5_GetStatistics(This,pStatistics) (This)->lpVtbl->GetStatistics(This,pStatistics)
#define IWMReaderAdvanced5_SetClientInfo(This,pClientInfo) (This)->lpVtbl->SetClientInfo(This,pClientInfo)
#define IWMReaderAdvanced5_GetMaxOutputSampleSize(This,dwOutput,pcbMax) (This)->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax)
#define IWMReaderAdvanced5_GetMaxStreamSampleSize(This,wStream,pcbMax) (This)->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax)
#define IWMReaderAdvanced5_NotifyLateDelivery(This,cnsLateness) (This)->lpVtbl->NotifyLateDelivery(This,cnsLateness)
/*** IWMReaderAdvanced2 methods ***/
#define IWMReaderAdvanced5_SetPlayMode(This,Mode) (This)->lpVtbl->SetPlayMode(This,Mode)
#define IWMReaderAdvanced5_GetPlayMode(This,pMode) (This)->lpVtbl->GetPlayMode(This,pMode)
#define IWMReaderAdvanced5_GetBufferProgress(This,pdwPercent,pcnsBuffering) (This)->lpVtbl->GetBufferProgress(This,pdwPercent,pcnsBuffering)
#define IWMReaderAdvanced5_GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload) (This)->lpVtbl->GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload)
#define IWMReaderAdvanced5_GetSaveAsProgress(This,pdwPercent) (This)->lpVtbl->GetSaveAsProgress(This,pdwPercent)
#define IWMReaderAdvanced5_SaveFileAs(This,pwszFilename) (This)->lpVtbl->SaveFileAs(This,pwszFilename)
#define IWMReaderAdvanced5_GetProtocolName(This,pwszProtocol,pcchProtocol) (This)->lpVtbl->GetProtocolName(This,pwszProtocol,pcchProtocol)
#define IWMReaderAdvanced5_StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext) (This)->lpVtbl->StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext)
#define IWMReaderAdvanced5_GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength) (This)->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength)
#define IWMReaderAdvanced5_SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength) (This)->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength)
#define IWMReaderAdvanced5_Preroll(This,cnsStart,cnsDuration,fRate) (This)->lpVtbl->Preroll(This,cnsStart,cnsDuration,fRate)
#define IWMReaderAdvanced5_SetLogClientID(This,fLogClientID) (This)->lpVtbl->SetLogClientID(This,fLogClientID)
#define IWMReaderAdvanced5_GetLogClientID(This,pfLogClientID) (This)->lpVtbl->GetLogClientID(This,pfLogClientID)
#define IWMReaderAdvanced5_StopBuffering(This) (This)->lpVtbl->StopBuffering(This)
#define IWMReaderAdvanced5_OpenStream(This,pStream,pCallback,pvContext) (This)->lpVtbl->OpenStream(This,pStream,pCallback,pvContext)
/*** IWMReaderAdvanced3 methods ***/
#define IWMReaderAdvanced5_StopNetStreaming(This) (This)->lpVtbl->StopNetStreaming(This)
#define IWMReaderAdvanced5_StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext) (This)->lpVtbl->StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext)
/*** IWMReaderAdvanced4 methods ***/
#define IWMReaderAdvanced5_GetLanguageCount(This,dwOutputNum,pwLanguageCount) (This)->lpVtbl->GetLanguageCount(This,dwOutputNum,pwLanguageCount)
#define IWMReaderAdvanced5_GetLanguage(This,dwOutputNum,wLanguage,pwszLanguageString,pcchLanguageStringLength) (This)->lpVtbl->GetLanguage(This,dwOutputNum,wLanguage,pwszLanguageString,pcchLanguageStringLength)
#define IWMReaderAdvanced5_GetMaxSpeedFactor(This,pdblFactor) (This)->lpVtbl->GetMaxSpeedFactor(This,pdblFactor)
#define IWMReaderAdvanced5_IsUsingFastCache(This,pfUsingFastCache) (This)->lpVtbl->IsUsingFastCache(This,pfUsingFastCache)
#define IWMReaderAdvanced5_AddLogParam(This,wszNameSpace,wszName,wszValue) (This)->lpVtbl->AddLogParam(This,wszNameSpace,wszName,wszValue)
#define IWMReaderAdvanced5_SendLogParams(This) (This)->lpVtbl->SendLogParams(This)
#define IWMReaderAdvanced5_CanSaveFileAs(This,pfCanSave) (This)->lpVtbl->CanSaveFileAs(This,pfCanSave)
#define IWMReaderAdvanced5_CancelSaveFileAs(This) (This)->lpVtbl->CancelSaveFileAs(This)
#define IWMReaderAdvanced5_GetURL(This,pwszURL,pcchURL) (This)->lpVtbl->GetURL(This,pwszURL,pcchURL)
/*** IWMReaderAdvanced5 methods ***/
#define IWMReaderAdvanced5_SetPlayerHook(This,dwOutputNum,pHook) (This)->lpVtbl->SetPlayerHook(This,dwOutputNum,pHook)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderAdvanced5_QueryInterface(IWMReaderAdvanced5* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderAdvanced5_AddRef(IWMReaderAdvanced5* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderAdvanced5_Release(IWMReaderAdvanced5* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderAdvanced methods ***/
static inline HRESULT IWMReaderAdvanced5_SetUserProvidedClock(IWMReaderAdvanced5* This,WINBOOL fUserClock) {
    return This->lpVtbl->SetUserProvidedClock(This,fUserClock);
}
static inline HRESULT IWMReaderAdvanced5_GetUserProvidedClock(IWMReaderAdvanced5* This,WINBOOL *pfUserClock) {
    return This->lpVtbl->GetUserProvidedClock(This,pfUserClock);
}
static inline HRESULT IWMReaderAdvanced5_DeliverTime(IWMReaderAdvanced5* This,QWORD cnsTime) {
    return This->lpVtbl->DeliverTime(This,cnsTime);
}
static inline HRESULT IWMReaderAdvanced5_SetManualStreamSelection(IWMReaderAdvanced5* This,WINBOOL fSelection) {
    return This->lpVtbl->SetManualStreamSelection(This,fSelection);
}
static inline HRESULT IWMReaderAdvanced5_GetManualStreamSelection(IWMReaderAdvanced5* This,WINBOOL *pfSelection) {
    return This->lpVtbl->GetManualStreamSelection(This,pfSelection);
}
static inline HRESULT IWMReaderAdvanced5_SetStreamsSelected(IWMReaderAdvanced5* This,WORD cStreamCount,WORD *pwStreamNumbers,WMT_STREAM_SELECTION *pSelections) {
    return This->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections);
}
static inline HRESULT IWMReaderAdvanced5_GetStreamSelected(IWMReaderAdvanced5* This,WORD wStreamNum,WMT_STREAM_SELECTION *pSelection) {
    return This->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection);
}
static inline HRESULT IWMReaderAdvanced5_SetReceiveSelectionCallbacks(IWMReaderAdvanced5* This,WINBOOL fGetCallbacks) {
    return This->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced5_GetReceiveSelectionCallbacks(IWMReaderAdvanced5* This,WINBOOL *pfGetCallbacks) {
    return This->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced5_SetReceiveStreamSamples(IWMReaderAdvanced5* This,WORD wStreamNum,WINBOOL fReceiveStreamSamples) {
    return This->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced5_GetReceiveStreamSamples(IWMReaderAdvanced5* This,WORD wStreamNum,WINBOOL *pfReceiveStreamSamples) {
    return This->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced5_SetAllocateForOutput(IWMReaderAdvanced5* This,DWORD dwOutputNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced5_GetAllocateForOutput(IWMReaderAdvanced5* This,DWORD dwOutputNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced5_SetAllocateForStream(IWMReaderAdvanced5* This,WORD wStreamNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced5_GetAllocateForStream(IWMReaderAdvanced5* This,WORD dwStreamNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced5_GetStatistics(IWMReaderAdvanced5* This,WM_READER_STATISTICS *pStatistics) {
    return This->lpVtbl->GetStatistics(This,pStatistics);
}
static inline HRESULT IWMReaderAdvanced5_SetClientInfo(IWMReaderAdvanced5* This,WM_READER_CLIENTINFO *pClientInfo) {
    return This->lpVtbl->SetClientInfo(This,pClientInfo);
}
static inline HRESULT IWMReaderAdvanced5_GetMaxOutputSampleSize(IWMReaderAdvanced5* This,DWORD dwOutput,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax);
}
static inline HRESULT IWMReaderAdvanced5_GetMaxStreamSampleSize(IWMReaderAdvanced5* This,WORD wStream,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax);
}
static inline HRESULT IWMReaderAdvanced5_NotifyLateDelivery(IWMReaderAdvanced5* This,QWORD cnsLateness) {
    return This->lpVtbl->NotifyLateDelivery(This,cnsLateness);
}
/*** IWMReaderAdvanced2 methods ***/
static inline HRESULT IWMReaderAdvanced5_SetPlayMode(IWMReaderAdvanced5* This,WMT_PLAY_MODE Mode) {
    return This->lpVtbl->SetPlayMode(This,Mode);
}
static inline HRESULT IWMReaderAdvanced5_GetPlayMode(IWMReaderAdvanced5* This,WMT_PLAY_MODE *pMode) {
    return This->lpVtbl->GetPlayMode(This,pMode);
}
static inline HRESULT IWMReaderAdvanced5_GetBufferProgress(IWMReaderAdvanced5* This,DWORD *pdwPercent,QWORD *pcnsBuffering) {
    return This->lpVtbl->GetBufferProgress(This,pdwPercent,pcnsBuffering);
}
static inline HRESULT IWMReaderAdvanced5_GetDownloadProgress(IWMReaderAdvanced5* This,DWORD *pdwPercent,QWORD *pqwBytesDownloaded,QWORD *pcnsDownload) {
    return This->lpVtbl->GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload);
}
static inline HRESULT IWMReaderAdvanced5_GetSaveAsProgress(IWMReaderAdvanced5* This,DWORD *pdwPercent) {
    return This->lpVtbl->GetSaveAsProgress(This,pdwPercent);
}
static inline HRESULT IWMReaderAdvanced5_SaveFileAs(IWMReaderAdvanced5* This,const WCHAR *pwszFilename) {
    return This->lpVtbl->SaveFileAs(This,pwszFilename);
}
static inline HRESULT IWMReaderAdvanced5_GetProtocolName(IWMReaderAdvanced5* This,WCHAR *pwszProtocol,DWORD *pcchProtocol) {
    return This->lpVtbl->GetProtocolName(This,pwszProtocol,pcchProtocol);
}
static inline HRESULT IWMReaderAdvanced5_StartAtMarker(IWMReaderAdvanced5* This,WORD wMarkerIndex,QWORD cnsDuration,float fRate,void *pvContext) {
    return This->lpVtbl->StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext);
}
static inline HRESULT IWMReaderAdvanced5_GetOutputSetting(IWMReaderAdvanced5* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE *pType,BYTE *pValue,WORD *pcbLength) {
    return This->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength);
}
static inline HRESULT IWMReaderAdvanced5_SetOutputSetting(IWMReaderAdvanced5* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE Type,const BYTE *pValue,WORD cbLength) {
    return This->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength);
}
static inline HRESULT IWMReaderAdvanced5_Preroll(IWMReaderAdvanced5* This,QWORD cnsStart,QWORD cnsDuration,float fRate) {
    return This->lpVtbl->Preroll(This,cnsStart,cnsDuration,fRate);
}
static inline HRESULT IWMReaderAdvanced5_SetLogClientID(IWMReaderAdvanced5* This,WINBOOL fLogClientID) {
    return This->lpVtbl->SetLogClientID(This,fLogClientID);
}
static inline HRESULT IWMReaderAdvanced5_GetLogClientID(IWMReaderAdvanced5* This,WINBOOL *pfLogClientID) {
    return This->lpVtbl->GetLogClientID(This,pfLogClientID);
}
static inline HRESULT IWMReaderAdvanced5_StopBuffering(IWMReaderAdvanced5* This) {
    return This->lpVtbl->StopBuffering(This);
}
static inline HRESULT IWMReaderAdvanced5_OpenStream(IWMReaderAdvanced5* This,IStream *pStream,IWMReaderCallback *pCallback,void *pvContext) {
    return This->lpVtbl->OpenStream(This,pStream,pCallback,pvContext);
}
/*** IWMReaderAdvanced3 methods ***/
static inline HRESULT IWMReaderAdvanced5_StopNetStreaming(IWMReaderAdvanced5* This) {
    return This->lpVtbl->StopNetStreaming(This);
}
static inline HRESULT IWMReaderAdvanced5_StartAtPosition(IWMReaderAdvanced5* This,WORD wStreamNum,void *pvOffsetStart,void *pvDuration,WMT_OFFSET_FORMAT dwOffsetFormat,float fRate,void *pvContext) {
    return This->lpVtbl->StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext);
}
/*** IWMReaderAdvanced4 methods ***/
static inline HRESULT IWMReaderAdvanced5_GetLanguageCount(IWMReaderAdvanced5* This,DWORD dwOutputNum,WORD *pwLanguageCount) {
    return This->lpVtbl->GetLanguageCount(This,dwOutputNum,pwLanguageCount);
}
static inline HRESULT IWMReaderAdvanced5_GetLanguage(IWMReaderAdvanced5* This,DWORD dwOutputNum,WORD wLanguage,WCHAR *pwszLanguageString,WORD *pcchLanguageStringLength) {
    return This->lpVtbl->GetLanguage(This,dwOutputNum,wLanguage,pwszLanguageString,pcchLanguageStringLength);
}
static inline HRESULT IWMReaderAdvanced5_GetMaxSpeedFactor(IWMReaderAdvanced5* This,double *pdblFactor) {
    return This->lpVtbl->GetMaxSpeedFactor(This,pdblFactor);
}
static inline HRESULT IWMReaderAdvanced5_IsUsingFastCache(IWMReaderAdvanced5* This,WINBOOL *pfUsingFastCache) {
    return This->lpVtbl->IsUsingFastCache(This,pfUsingFastCache);
}
static inline HRESULT IWMReaderAdvanced5_AddLogParam(IWMReaderAdvanced5* This,LPCWSTR wszNameSpace,LPCWSTR wszName,LPCWSTR wszValue) {
    return This->lpVtbl->AddLogParam(This,wszNameSpace,wszName,wszValue);
}
static inline HRESULT IWMReaderAdvanced5_SendLogParams(IWMReaderAdvanced5* This) {
    return This->lpVtbl->SendLogParams(This);
}
static inline HRESULT IWMReaderAdvanced5_CanSaveFileAs(IWMReaderAdvanced5* This,WINBOOL *pfCanSave) {
    return This->lpVtbl->CanSaveFileAs(This,pfCanSave);
}
static inline HRESULT IWMReaderAdvanced5_CancelSaveFileAs(IWMReaderAdvanced5* This) {
    return This->lpVtbl->CancelSaveFileAs(This);
}
static inline HRESULT IWMReaderAdvanced5_GetURL(IWMReaderAdvanced5* This,WCHAR *pwszURL,DWORD *pcchURL) {
    return This->lpVtbl->GetURL(This,pwszURL,pcchURL);
}
/*** IWMReaderAdvanced5 methods ***/
static inline HRESULT IWMReaderAdvanced5_SetPlayerHook(IWMReaderAdvanced5* This,DWORD dwOutputNum,IWMPlayerHook *pHook) {
    return This->lpVtbl->SetPlayerHook(This,dwOutputNum,pHook);
}
#endif
#endif

#endif


#endif  /* __IWMReaderAdvanced5_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderAdvanced6 interface
 */
#ifndef __IWMReaderAdvanced6_INTERFACE_DEFINED__
#define __IWMReaderAdvanced6_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderAdvanced6, 0x18a2e7f8, 0x428f, 0x4acd, 0x8a,0x00, 0xe6,0x46,0x39,0xbc,0x93,0xde);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("18a2e7f8-428f-4acd-8a00-e64639bc93de")
IWMReaderAdvanced6 : public IWMReaderAdvanced5
{
    virtual HRESULT STDMETHODCALLTYPE SetProtectStreamSamples(
        BYTE *pbCertificate,
        DWORD cbCertificate,
        DWORD dwCertificateType,
        DWORD dwFlags,
        BYTE *pbInitializationVector,
        DWORD *pcbInitializationVector) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderAdvanced6, 0x18a2e7f8, 0x428f, 0x4acd, 0x8a,0x00, 0xe6,0x46,0x39,0xbc,0x93,0xde)
#endif
#else
typedef struct IWMReaderAdvanced6Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderAdvanced6 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderAdvanced6 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderAdvanced6 *This);

    /*** IWMReaderAdvanced methods ***/
    HRESULT (STDMETHODCALLTYPE *SetUserProvidedClock)(
        IWMReaderAdvanced6 *This,
        WINBOOL fUserClock);

    HRESULT (STDMETHODCALLTYPE *GetUserProvidedClock)(
        IWMReaderAdvanced6 *This,
        WINBOOL *pfUserClock);

    HRESULT (STDMETHODCALLTYPE *DeliverTime)(
        IWMReaderAdvanced6 *This,
        QWORD cnsTime);

    HRESULT (STDMETHODCALLTYPE *SetManualStreamSelection)(
        IWMReaderAdvanced6 *This,
        WINBOOL fSelection);

    HRESULT (STDMETHODCALLTYPE *GetManualStreamSelection)(
        IWMReaderAdvanced6 *This,
        WINBOOL *pfSelection);

    HRESULT (STDMETHODCALLTYPE *SetStreamsSelected)(
        IWMReaderAdvanced6 *This,
        WORD cStreamCount,
        WORD *pwStreamNumbers,
        WMT_STREAM_SELECTION *pSelections);

    HRESULT (STDMETHODCALLTYPE *GetStreamSelected)(
        IWMReaderAdvanced6 *This,
        WORD wStreamNum,
        WMT_STREAM_SELECTION *pSelection);

    HRESULT (STDMETHODCALLTYPE *SetReceiveSelectionCallbacks)(
        IWMReaderAdvanced6 *This,
        WINBOOL fGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *GetReceiveSelectionCallbacks)(
        IWMReaderAdvanced6 *This,
        WINBOOL *pfGetCallbacks);

    HRESULT (STDMETHODCALLTYPE *SetReceiveStreamSamples)(
        IWMReaderAdvanced6 *This,
        WORD wStreamNum,
        WINBOOL fReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *GetReceiveStreamSamples)(
        IWMReaderAdvanced6 *This,
        WORD wStreamNum,
        WINBOOL *pfReceiveStreamSamples);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForOutput)(
        IWMReaderAdvanced6 *This,
        DWORD dwOutputNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForOutput)(
        IWMReaderAdvanced6 *This,
        DWORD dwOutputNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForStream)(
        IWMReaderAdvanced6 *This,
        WORD wStreamNum,
        WINBOOL fAllocate);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForStream)(
        IWMReaderAdvanced6 *This,
        WORD dwStreamNum,
        WINBOOL *pfAllocate);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IWMReaderAdvanced6 *This,
        WM_READER_STATISTICS *pStatistics);

    HRESULT (STDMETHODCALLTYPE *SetClientInfo)(
        IWMReaderAdvanced6 *This,
        WM_READER_CLIENTINFO *pClientInfo);

    HRESULT (STDMETHODCALLTYPE *GetMaxOutputSampleSize)(
        IWMReaderAdvanced6 *This,
        DWORD dwOutput,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *GetMaxStreamSampleSize)(
        IWMReaderAdvanced6 *This,
        WORD wStream,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *NotifyLateDelivery)(
        IWMReaderAdvanced6 *This,
        QWORD cnsLateness);

    /*** IWMReaderAdvanced2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPlayMode)(
        IWMReaderAdvanced6 *This,
        WMT_PLAY_MODE Mode);

    HRESULT (STDMETHODCALLTYPE *GetPlayMode)(
        IWMReaderAdvanced6 *This,
        WMT_PLAY_MODE *pMode);

    HRESULT (STDMETHODCALLTYPE *GetBufferProgress)(
        IWMReaderAdvanced6 *This,
        DWORD *pdwPercent,
        QWORD *pcnsBuffering);

    HRESULT (STDMETHODCALLTYPE *GetDownloadProgress)(
        IWMReaderAdvanced6 *This,
        DWORD *pdwPercent,
        QWORD *pqwBytesDownloaded,
        QWORD *pcnsDownload);

    HRESULT (STDMETHODCALLTYPE *GetSaveAsProgress)(
        IWMReaderAdvanced6 *This,
        DWORD *pdwPercent);

    HRESULT (STDMETHODCALLTYPE *SaveFileAs)(
        IWMReaderAdvanced6 *This,
        const WCHAR *pwszFilename);

    HRESULT (STDMETHODCALLTYPE *GetProtocolName)(
        IWMReaderAdvanced6 *This,
        WCHAR *pwszProtocol,
        DWORD *pcchProtocol);

    HRESULT (STDMETHODCALLTYPE *StartAtMarker)(
        IWMReaderAdvanced6 *This,
        WORD wMarkerIndex,
        QWORD cnsDuration,
        float fRate,
        void *pvContext);

    HRESULT (STDMETHODCALLTYPE *GetOutputSetting)(
        IWMReaderAdvanced6 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength);

    HRESULT (STDMETHODCALLTYPE *SetOutputSetting)(
        IWMReaderAdvanced6 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength);

    HRESULT (STDMETHODCALLTYPE *Preroll)(
        IWMReaderAdvanced6 *This,
        QWORD cnsStart,
        QWORD cnsDuration,
        float fRate);

    HRESULT (STDMETHODCALLTYPE *SetLogClientID)(
        IWMReaderAdvanced6 *This,
        WINBOOL fLogClientID);

    HRESULT (STDMETHODCALLTYPE *GetLogClientID)(
        IWMReaderAdvanced6 *This,
        WINBOOL *pfLogClientID);

    HRESULT (STDMETHODCALLTYPE *StopBuffering)(
        IWMReaderAdvanced6 *This);

    HRESULT (STDMETHODCALLTYPE *OpenStream)(
        IWMReaderAdvanced6 *This,
        IStream *pStream,
        IWMReaderCallback *pCallback,
        void *pvContext);

    /*** IWMReaderAdvanced3 methods ***/
    HRESULT (STDMETHODCALLTYPE *StopNetStreaming)(
        IWMReaderAdvanced6 *This);

    HRESULT (STDMETHODCALLTYPE *StartAtPosition)(
        IWMReaderAdvanced6 *This,
        WORD wStreamNum,
        void *pvOffsetStart,
        void *pvDuration,
        WMT_OFFSET_FORMAT dwOffsetFormat,
        float fRate,
        void *pvContext);

    /*** IWMReaderAdvanced4 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLanguageCount)(
        IWMReaderAdvanced6 *This,
        DWORD dwOutputNum,
        WORD *pwLanguageCount);

    HRESULT (STDMETHODCALLTYPE *GetLanguage)(
        IWMReaderAdvanced6 *This,
        DWORD dwOutputNum,
        WORD wLanguage,
        WCHAR *pwszLanguageString,
        WORD *pcchLanguageStringLength);

    HRESULT (STDMETHODCALLTYPE *GetMaxSpeedFactor)(
        IWMReaderAdvanced6 *This,
        double *pdblFactor);

    HRESULT (STDMETHODCALLTYPE *IsUsingFastCache)(
        IWMReaderAdvanced6 *This,
        WINBOOL *pfUsingFastCache);

    HRESULT (STDMETHODCALLTYPE *AddLogParam)(
        IWMReaderAdvanced6 *This,
        LPCWSTR wszNameSpace,
        LPCWSTR wszName,
        LPCWSTR wszValue);

    HRESULT (STDMETHODCALLTYPE *SendLogParams)(
        IWMReaderAdvanced6 *This);

    HRESULT (STDMETHODCALLTYPE *CanSaveFileAs)(
        IWMReaderAdvanced6 *This,
        WINBOOL *pfCanSave);

    HRESULT (STDMETHODCALLTYPE *CancelSaveFileAs)(
        IWMReaderAdvanced6 *This);

    HRESULT (STDMETHODCALLTYPE *GetURL)(
        IWMReaderAdvanced6 *This,
        WCHAR *pwszURL,
        DWORD *pcchURL);

    /*** IWMReaderAdvanced5 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPlayerHook)(
        IWMReaderAdvanced6 *This,
        DWORD dwOutputNum,
        IWMPlayerHook *pHook);

    /*** IWMReaderAdvanced6 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetProtectStreamSamples)(
        IWMReaderAdvanced6 *This,
        BYTE *pbCertificate,
        DWORD cbCertificate,
        DWORD dwCertificateType,
        DWORD dwFlags,
        BYTE *pbInitializationVector,
        DWORD *pcbInitializationVector);

    END_INTERFACE
} IWMReaderAdvanced6Vtbl;

interface IWMReaderAdvanced6 {
    CONST_VTBL IWMReaderAdvanced6Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderAdvanced6_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderAdvanced6_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderAdvanced6_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderAdvanced methods ***/
#define IWMReaderAdvanced6_SetUserProvidedClock(This,fUserClock) (This)->lpVtbl->SetUserProvidedClock(This,fUserClock)
#define IWMReaderAdvanced6_GetUserProvidedClock(This,pfUserClock) (This)->lpVtbl->GetUserProvidedClock(This,pfUserClock)
#define IWMReaderAdvanced6_DeliverTime(This,cnsTime) (This)->lpVtbl->DeliverTime(This,cnsTime)
#define IWMReaderAdvanced6_SetManualStreamSelection(This,fSelection) (This)->lpVtbl->SetManualStreamSelection(This,fSelection)
#define IWMReaderAdvanced6_GetManualStreamSelection(This,pfSelection) (This)->lpVtbl->GetManualStreamSelection(This,pfSelection)
#define IWMReaderAdvanced6_SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections) (This)->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections)
#define IWMReaderAdvanced6_GetStreamSelected(This,wStreamNum,pSelection) (This)->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection)
#define IWMReaderAdvanced6_SetReceiveSelectionCallbacks(This,fGetCallbacks) (This)->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks)
#define IWMReaderAdvanced6_GetReceiveSelectionCallbacks(This,pfGetCallbacks) (This)->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks)
#define IWMReaderAdvanced6_SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples) (This)->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples)
#define IWMReaderAdvanced6_GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples) (This)->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples)
#define IWMReaderAdvanced6_SetAllocateForOutput(This,dwOutputNum,fAllocate) (This)->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate)
#define IWMReaderAdvanced6_GetAllocateForOutput(This,dwOutputNum,pfAllocate) (This)->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate)
#define IWMReaderAdvanced6_SetAllocateForStream(This,wStreamNum,fAllocate) (This)->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate)
#define IWMReaderAdvanced6_GetAllocateForStream(This,dwStreamNum,pfAllocate) (This)->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate)
#define IWMReaderAdvanced6_GetStatistics(This,pStatistics) (This)->lpVtbl->GetStatistics(This,pStatistics)
#define IWMReaderAdvanced6_SetClientInfo(This,pClientInfo) (This)->lpVtbl->SetClientInfo(This,pClientInfo)
#define IWMReaderAdvanced6_GetMaxOutputSampleSize(This,dwOutput,pcbMax) (This)->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax)
#define IWMReaderAdvanced6_GetMaxStreamSampleSize(This,wStream,pcbMax) (This)->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax)
#define IWMReaderAdvanced6_NotifyLateDelivery(This,cnsLateness) (This)->lpVtbl->NotifyLateDelivery(This,cnsLateness)
/*** IWMReaderAdvanced2 methods ***/
#define IWMReaderAdvanced6_SetPlayMode(This,Mode) (This)->lpVtbl->SetPlayMode(This,Mode)
#define IWMReaderAdvanced6_GetPlayMode(This,pMode) (This)->lpVtbl->GetPlayMode(This,pMode)
#define IWMReaderAdvanced6_GetBufferProgress(This,pdwPercent,pcnsBuffering) (This)->lpVtbl->GetBufferProgress(This,pdwPercent,pcnsBuffering)
#define IWMReaderAdvanced6_GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload) (This)->lpVtbl->GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload)
#define IWMReaderAdvanced6_GetSaveAsProgress(This,pdwPercent) (This)->lpVtbl->GetSaveAsProgress(This,pdwPercent)
#define IWMReaderAdvanced6_SaveFileAs(This,pwszFilename) (This)->lpVtbl->SaveFileAs(This,pwszFilename)
#define IWMReaderAdvanced6_GetProtocolName(This,pwszProtocol,pcchProtocol) (This)->lpVtbl->GetProtocolName(This,pwszProtocol,pcchProtocol)
#define IWMReaderAdvanced6_StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext) (This)->lpVtbl->StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext)
#define IWMReaderAdvanced6_GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength) (This)->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength)
#define IWMReaderAdvanced6_SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength) (This)->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength)
#define IWMReaderAdvanced6_Preroll(This,cnsStart,cnsDuration,fRate) (This)->lpVtbl->Preroll(This,cnsStart,cnsDuration,fRate)
#define IWMReaderAdvanced6_SetLogClientID(This,fLogClientID) (This)->lpVtbl->SetLogClientID(This,fLogClientID)
#define IWMReaderAdvanced6_GetLogClientID(This,pfLogClientID) (This)->lpVtbl->GetLogClientID(This,pfLogClientID)
#define IWMReaderAdvanced6_StopBuffering(This) (This)->lpVtbl->StopBuffering(This)
#define IWMReaderAdvanced6_OpenStream(This,pStream,pCallback,pvContext) (This)->lpVtbl->OpenStream(This,pStream,pCallback,pvContext)
/*** IWMReaderAdvanced3 methods ***/
#define IWMReaderAdvanced6_StopNetStreaming(This) (This)->lpVtbl->StopNetStreaming(This)
#define IWMReaderAdvanced6_StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext) (This)->lpVtbl->StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext)
/*** IWMReaderAdvanced4 methods ***/
#define IWMReaderAdvanced6_GetLanguageCount(This,dwOutputNum,pwLanguageCount) (This)->lpVtbl->GetLanguageCount(This,dwOutputNum,pwLanguageCount)
#define IWMReaderAdvanced6_GetLanguage(This,dwOutputNum,wLanguage,pwszLanguageString,pcchLanguageStringLength) (This)->lpVtbl->GetLanguage(This,dwOutputNum,wLanguage,pwszLanguageString,pcchLanguageStringLength)
#define IWMReaderAdvanced6_GetMaxSpeedFactor(This,pdblFactor) (This)->lpVtbl->GetMaxSpeedFactor(This,pdblFactor)
#define IWMReaderAdvanced6_IsUsingFastCache(This,pfUsingFastCache) (This)->lpVtbl->IsUsingFastCache(This,pfUsingFastCache)
#define IWMReaderAdvanced6_AddLogParam(This,wszNameSpace,wszName,wszValue) (This)->lpVtbl->AddLogParam(This,wszNameSpace,wszName,wszValue)
#define IWMReaderAdvanced6_SendLogParams(This) (This)->lpVtbl->SendLogParams(This)
#define IWMReaderAdvanced6_CanSaveFileAs(This,pfCanSave) (This)->lpVtbl->CanSaveFileAs(This,pfCanSave)
#define IWMReaderAdvanced6_CancelSaveFileAs(This) (This)->lpVtbl->CancelSaveFileAs(This)
#define IWMReaderAdvanced6_GetURL(This,pwszURL,pcchURL) (This)->lpVtbl->GetURL(This,pwszURL,pcchURL)
/*** IWMReaderAdvanced5 methods ***/
#define IWMReaderAdvanced6_SetPlayerHook(This,dwOutputNum,pHook) (This)->lpVtbl->SetPlayerHook(This,dwOutputNum,pHook)
/*** IWMReaderAdvanced6 methods ***/
#define IWMReaderAdvanced6_SetProtectStreamSamples(This,pbCertificate,cbCertificate,dwCertificateType,dwFlags,pbInitializationVector,pcbInitializationVector) (This)->lpVtbl->SetProtectStreamSamples(This,pbCertificate,cbCertificate,dwCertificateType,dwFlags,pbInitializationVector,pcbInitializationVector)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderAdvanced6_QueryInterface(IWMReaderAdvanced6* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderAdvanced6_AddRef(IWMReaderAdvanced6* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderAdvanced6_Release(IWMReaderAdvanced6* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderAdvanced methods ***/
static inline HRESULT IWMReaderAdvanced6_SetUserProvidedClock(IWMReaderAdvanced6* This,WINBOOL fUserClock) {
    return This->lpVtbl->SetUserProvidedClock(This,fUserClock);
}
static inline HRESULT IWMReaderAdvanced6_GetUserProvidedClock(IWMReaderAdvanced6* This,WINBOOL *pfUserClock) {
    return This->lpVtbl->GetUserProvidedClock(This,pfUserClock);
}
static inline HRESULT IWMReaderAdvanced6_DeliverTime(IWMReaderAdvanced6* This,QWORD cnsTime) {
    return This->lpVtbl->DeliverTime(This,cnsTime);
}
static inline HRESULT IWMReaderAdvanced6_SetManualStreamSelection(IWMReaderAdvanced6* This,WINBOOL fSelection) {
    return This->lpVtbl->SetManualStreamSelection(This,fSelection);
}
static inline HRESULT IWMReaderAdvanced6_GetManualStreamSelection(IWMReaderAdvanced6* This,WINBOOL *pfSelection) {
    return This->lpVtbl->GetManualStreamSelection(This,pfSelection);
}
static inline HRESULT IWMReaderAdvanced6_SetStreamsSelected(IWMReaderAdvanced6* This,WORD cStreamCount,WORD *pwStreamNumbers,WMT_STREAM_SELECTION *pSelections) {
    return This->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections);
}
static inline HRESULT IWMReaderAdvanced6_GetStreamSelected(IWMReaderAdvanced6* This,WORD wStreamNum,WMT_STREAM_SELECTION *pSelection) {
    return This->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection);
}
static inline HRESULT IWMReaderAdvanced6_SetReceiveSelectionCallbacks(IWMReaderAdvanced6* This,WINBOOL fGetCallbacks) {
    return This->lpVtbl->SetReceiveSelectionCallbacks(This,fGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced6_GetReceiveSelectionCallbacks(IWMReaderAdvanced6* This,WINBOOL *pfGetCallbacks) {
    return This->lpVtbl->GetReceiveSelectionCallbacks(This,pfGetCallbacks);
}
static inline HRESULT IWMReaderAdvanced6_SetReceiveStreamSamples(IWMReaderAdvanced6* This,WORD wStreamNum,WINBOOL fReceiveStreamSamples) {
    return This->lpVtbl->SetReceiveStreamSamples(This,wStreamNum,fReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced6_GetReceiveStreamSamples(IWMReaderAdvanced6* This,WORD wStreamNum,WINBOOL *pfReceiveStreamSamples) {
    return This->lpVtbl->GetReceiveStreamSamples(This,wStreamNum,pfReceiveStreamSamples);
}
static inline HRESULT IWMReaderAdvanced6_SetAllocateForOutput(IWMReaderAdvanced6* This,DWORD dwOutputNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForOutput(This,dwOutputNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced6_GetAllocateForOutput(IWMReaderAdvanced6* This,DWORD dwOutputNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForOutput(This,dwOutputNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced6_SetAllocateForStream(IWMReaderAdvanced6* This,WORD wStreamNum,WINBOOL fAllocate) {
    return This->lpVtbl->SetAllocateForStream(This,wStreamNum,fAllocate);
}
static inline HRESULT IWMReaderAdvanced6_GetAllocateForStream(IWMReaderAdvanced6* This,WORD dwStreamNum,WINBOOL *pfAllocate) {
    return This->lpVtbl->GetAllocateForStream(This,dwStreamNum,pfAllocate);
}
static inline HRESULT IWMReaderAdvanced6_GetStatistics(IWMReaderAdvanced6* This,WM_READER_STATISTICS *pStatistics) {
    return This->lpVtbl->GetStatistics(This,pStatistics);
}
static inline HRESULT IWMReaderAdvanced6_SetClientInfo(IWMReaderAdvanced6* This,WM_READER_CLIENTINFO *pClientInfo) {
    return This->lpVtbl->SetClientInfo(This,pClientInfo);
}
static inline HRESULT IWMReaderAdvanced6_GetMaxOutputSampleSize(IWMReaderAdvanced6* This,DWORD dwOutput,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax);
}
static inline HRESULT IWMReaderAdvanced6_GetMaxStreamSampleSize(IWMReaderAdvanced6* This,WORD wStream,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax);
}
static inline HRESULT IWMReaderAdvanced6_NotifyLateDelivery(IWMReaderAdvanced6* This,QWORD cnsLateness) {
    return This->lpVtbl->NotifyLateDelivery(This,cnsLateness);
}
/*** IWMReaderAdvanced2 methods ***/
static inline HRESULT IWMReaderAdvanced6_SetPlayMode(IWMReaderAdvanced6* This,WMT_PLAY_MODE Mode) {
    return This->lpVtbl->SetPlayMode(This,Mode);
}
static inline HRESULT IWMReaderAdvanced6_GetPlayMode(IWMReaderAdvanced6* This,WMT_PLAY_MODE *pMode) {
    return This->lpVtbl->GetPlayMode(This,pMode);
}
static inline HRESULT IWMReaderAdvanced6_GetBufferProgress(IWMReaderAdvanced6* This,DWORD *pdwPercent,QWORD *pcnsBuffering) {
    return This->lpVtbl->GetBufferProgress(This,pdwPercent,pcnsBuffering);
}
static inline HRESULT IWMReaderAdvanced6_GetDownloadProgress(IWMReaderAdvanced6* This,DWORD *pdwPercent,QWORD *pqwBytesDownloaded,QWORD *pcnsDownload) {
    return This->lpVtbl->GetDownloadProgress(This,pdwPercent,pqwBytesDownloaded,pcnsDownload);
}
static inline HRESULT IWMReaderAdvanced6_GetSaveAsProgress(IWMReaderAdvanced6* This,DWORD *pdwPercent) {
    return This->lpVtbl->GetSaveAsProgress(This,pdwPercent);
}
static inline HRESULT IWMReaderAdvanced6_SaveFileAs(IWMReaderAdvanced6* This,const WCHAR *pwszFilename) {
    return This->lpVtbl->SaveFileAs(This,pwszFilename);
}
static inline HRESULT IWMReaderAdvanced6_GetProtocolName(IWMReaderAdvanced6* This,WCHAR *pwszProtocol,DWORD *pcchProtocol) {
    return This->lpVtbl->GetProtocolName(This,pwszProtocol,pcchProtocol);
}
static inline HRESULT IWMReaderAdvanced6_StartAtMarker(IWMReaderAdvanced6* This,WORD wMarkerIndex,QWORD cnsDuration,float fRate,void *pvContext) {
    return This->lpVtbl->StartAtMarker(This,wMarkerIndex,cnsDuration,fRate,pvContext);
}
static inline HRESULT IWMReaderAdvanced6_GetOutputSetting(IWMReaderAdvanced6* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE *pType,BYTE *pValue,WORD *pcbLength) {
    return This->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength);
}
static inline HRESULT IWMReaderAdvanced6_SetOutputSetting(IWMReaderAdvanced6* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE Type,const BYTE *pValue,WORD cbLength) {
    return This->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength);
}
static inline HRESULT IWMReaderAdvanced6_Preroll(IWMReaderAdvanced6* This,QWORD cnsStart,QWORD cnsDuration,float fRate) {
    return This->lpVtbl->Preroll(This,cnsStart,cnsDuration,fRate);
}
static inline HRESULT IWMReaderAdvanced6_SetLogClientID(IWMReaderAdvanced6* This,WINBOOL fLogClientID) {
    return This->lpVtbl->SetLogClientID(This,fLogClientID);
}
static inline HRESULT IWMReaderAdvanced6_GetLogClientID(IWMReaderAdvanced6* This,WINBOOL *pfLogClientID) {
    return This->lpVtbl->GetLogClientID(This,pfLogClientID);
}
static inline HRESULT IWMReaderAdvanced6_StopBuffering(IWMReaderAdvanced6* This) {
    return This->lpVtbl->StopBuffering(This);
}
static inline HRESULT IWMReaderAdvanced6_OpenStream(IWMReaderAdvanced6* This,IStream *pStream,IWMReaderCallback *pCallback,void *pvContext) {
    return This->lpVtbl->OpenStream(This,pStream,pCallback,pvContext);
}
/*** IWMReaderAdvanced3 methods ***/
static inline HRESULT IWMReaderAdvanced6_StopNetStreaming(IWMReaderAdvanced6* This) {
    return This->lpVtbl->StopNetStreaming(This);
}
static inline HRESULT IWMReaderAdvanced6_StartAtPosition(IWMReaderAdvanced6* This,WORD wStreamNum,void *pvOffsetStart,void *pvDuration,WMT_OFFSET_FORMAT dwOffsetFormat,float fRate,void *pvContext) {
    return This->lpVtbl->StartAtPosition(This,wStreamNum,pvOffsetStart,pvDuration,dwOffsetFormat,fRate,pvContext);
}
/*** IWMReaderAdvanced4 methods ***/
static inline HRESULT IWMReaderAdvanced6_GetLanguageCount(IWMReaderAdvanced6* This,DWORD dwOutputNum,WORD *pwLanguageCount) {
    return This->lpVtbl->GetLanguageCount(This,dwOutputNum,pwLanguageCount);
}
static inline HRESULT IWMReaderAdvanced6_GetLanguage(IWMReaderAdvanced6* This,DWORD dwOutputNum,WORD wLanguage,WCHAR *pwszLanguageString,WORD *pcchLanguageStringLength) {
    return This->lpVtbl->GetLanguage(This,dwOutputNum,wLanguage,pwszLanguageString,pcchLanguageStringLength);
}
static inline HRESULT IWMReaderAdvanced6_GetMaxSpeedFactor(IWMReaderAdvanced6* This,double *pdblFactor) {
    return This->lpVtbl->GetMaxSpeedFactor(This,pdblFactor);
}
static inline HRESULT IWMReaderAdvanced6_IsUsingFastCache(IWMReaderAdvanced6* This,WINBOOL *pfUsingFastCache) {
    return This->lpVtbl->IsUsingFastCache(This,pfUsingFastCache);
}
static inline HRESULT IWMReaderAdvanced6_AddLogParam(IWMReaderAdvanced6* This,LPCWSTR wszNameSpace,LPCWSTR wszName,LPCWSTR wszValue) {
    return This->lpVtbl->AddLogParam(This,wszNameSpace,wszName,wszValue);
}
static inline HRESULT IWMReaderAdvanced6_SendLogParams(IWMReaderAdvanced6* This) {
    return This->lpVtbl->SendLogParams(This);
}
static inline HRESULT IWMReaderAdvanced6_CanSaveFileAs(IWMReaderAdvanced6* This,WINBOOL *pfCanSave) {
    return This->lpVtbl->CanSaveFileAs(This,pfCanSave);
}
static inline HRESULT IWMReaderAdvanced6_CancelSaveFileAs(IWMReaderAdvanced6* This) {
    return This->lpVtbl->CancelSaveFileAs(This);
}
static inline HRESULT IWMReaderAdvanced6_GetURL(IWMReaderAdvanced6* This,WCHAR *pwszURL,DWORD *pcchURL) {
    return This->lpVtbl->GetURL(This,pwszURL,pcchURL);
}
/*** IWMReaderAdvanced5 methods ***/
static inline HRESULT IWMReaderAdvanced6_SetPlayerHook(IWMReaderAdvanced6* This,DWORD dwOutputNum,IWMPlayerHook *pHook) {
    return This->lpVtbl->SetPlayerHook(This,dwOutputNum,pHook);
}
/*** IWMReaderAdvanced6 methods ***/
static inline HRESULT IWMReaderAdvanced6_SetProtectStreamSamples(IWMReaderAdvanced6* This,BYTE *pbCertificate,DWORD cbCertificate,DWORD dwCertificateType,DWORD dwFlags,BYTE *pbInitializationVector,DWORD *pcbInitializationVector) {
    return This->lpVtbl->SetProtectStreamSamples(This,pbCertificate,cbCertificate,dwCertificateType,dwFlags,pbInitializationVector,pcbInitializationVector);
}
#endif
#endif

#endif


#endif  /* __IWMReaderAdvanced6_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMSyncReader interface
 */
#ifndef __IWMSyncReader_INTERFACE_DEFINED__
#define __IWMSyncReader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMSyncReader, 0x9397f121, 0x7705, 0x4dc9, 0xb0,0x49, 0x98,0xb6,0x98,0x18,0x84,0x14);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9397f121-7705-4dc9-b049-98b698188414")
IWMSyncReader : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Open(
        const WCHAR *pwszFilename) = 0;

    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRange(
        QWORD cnsStartTime,
        LONGLONG cnsDuration) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRangeByFrame(
        WORD wStreamNum,
        QWORD qwFrameNumber,
        LONGLONG cFramesToRead) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNextSample(
        WORD wStreamNum,
        INSSBuffer **ppSample,
        QWORD *pcnsSampleTime,
        QWORD *pcnsDuration,
        DWORD *pdwFlags,
        DWORD *pdwOutputNum,
        WORD *pwStreamNum) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamsSelected(
        WORD cStreamCount,
        WORD *pwStreamNumbers,
        WMT_STREAM_SELECTION *pSelections) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamSelected(
        WORD wStreamNum,
        WMT_STREAM_SELECTION *pSelection) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetReadStreamSamples(
        WORD wStreamNum,
        WINBOOL fCompressed) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetReadStreamSamples(
        WORD wStreamNum,
        WINBOOL *pfCompressed) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputSetting(
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputSetting(
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputCount(
        DWORD *pcOutputs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputProps(
        DWORD dwOutputNum,
        IWMOutputMediaProps **ppOutput) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputProps(
        DWORD dwOutputNum,
        IWMOutputMediaProps *pOutput) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputFormatCount(
        DWORD dwOutputNum,
        DWORD *pcFormats) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputFormat(
        DWORD dwOutputNum,
        DWORD dwFormatNum,
        IWMOutputMediaProps **ppProps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputNumberForStream(
        WORD wStreamNum,
        DWORD *pdwOutputNum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamNumberForOutput(
        DWORD dwOutputNum,
        WORD *pwStreamNum) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaxOutputSampleSize(
        DWORD dwOutput,
        DWORD *pcbMax) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaxStreamSampleSize(
        WORD wStream,
        DWORD *pcbMax) = 0;

    virtual HRESULT STDMETHODCALLTYPE OpenStream(
        IStream *pStream) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMSyncReader, 0x9397f121, 0x7705, 0x4dc9, 0xb0,0x49, 0x98,0xb6,0x98,0x18,0x84,0x14)
#endif
#else
typedef struct IWMSyncReaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMSyncReader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMSyncReader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMSyncReader *This);

    /*** IWMSyncReader methods ***/
    HRESULT (STDMETHODCALLTYPE *Open)(
        IWMSyncReader *This,
        const WCHAR *pwszFilename);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IWMSyncReader *This);

    HRESULT (STDMETHODCALLTYPE *SetRange)(
        IWMSyncReader *This,
        QWORD cnsStartTime,
        LONGLONG cnsDuration);

    HRESULT (STDMETHODCALLTYPE *SetRangeByFrame)(
        IWMSyncReader *This,
        WORD wStreamNum,
        QWORD qwFrameNumber,
        LONGLONG cFramesToRead);

    HRESULT (STDMETHODCALLTYPE *GetNextSample)(
        IWMSyncReader *This,
        WORD wStreamNum,
        INSSBuffer **ppSample,
        QWORD *pcnsSampleTime,
        QWORD *pcnsDuration,
        DWORD *pdwFlags,
        DWORD *pdwOutputNum,
        WORD *pwStreamNum);

    HRESULT (STDMETHODCALLTYPE *SetStreamsSelected)(
        IWMSyncReader *This,
        WORD cStreamCount,
        WORD *pwStreamNumbers,
        WMT_STREAM_SELECTION *pSelections);

    HRESULT (STDMETHODCALLTYPE *GetStreamSelected)(
        IWMSyncReader *This,
        WORD wStreamNum,
        WMT_STREAM_SELECTION *pSelection);

    HRESULT (STDMETHODCALLTYPE *SetReadStreamSamples)(
        IWMSyncReader *This,
        WORD wStreamNum,
        WINBOOL fCompressed);

    HRESULT (STDMETHODCALLTYPE *GetReadStreamSamples)(
        IWMSyncReader *This,
        WORD wStreamNum,
        WINBOOL *pfCompressed);

    HRESULT (STDMETHODCALLTYPE *GetOutputSetting)(
        IWMSyncReader *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength);

    HRESULT (STDMETHODCALLTYPE *SetOutputSetting)(
        IWMSyncReader *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength);

    HRESULT (STDMETHODCALLTYPE *GetOutputCount)(
        IWMSyncReader *This,
        DWORD *pcOutputs);

    HRESULT (STDMETHODCALLTYPE *GetOutputProps)(
        IWMSyncReader *This,
        DWORD dwOutputNum,
        IWMOutputMediaProps **ppOutput);

    HRESULT (STDMETHODCALLTYPE *SetOutputProps)(
        IWMSyncReader *This,
        DWORD dwOutputNum,
        IWMOutputMediaProps *pOutput);

    HRESULT (STDMETHODCALLTYPE *GetOutputFormatCount)(
        IWMSyncReader *This,
        DWORD dwOutputNum,
        DWORD *pcFormats);

    HRESULT (STDMETHODCALLTYPE *GetOutputFormat)(
        IWMSyncReader *This,
        DWORD dwOutputNum,
        DWORD dwFormatNum,
        IWMOutputMediaProps **ppProps);

    HRESULT (STDMETHODCALLTYPE *GetOutputNumberForStream)(
        IWMSyncReader *This,
        WORD wStreamNum,
        DWORD *pdwOutputNum);

    HRESULT (STDMETHODCALLTYPE *GetStreamNumberForOutput)(
        IWMSyncReader *This,
        DWORD dwOutputNum,
        WORD *pwStreamNum);

    HRESULT (STDMETHODCALLTYPE *GetMaxOutputSampleSize)(
        IWMSyncReader *This,
        DWORD dwOutput,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *GetMaxStreamSampleSize)(
        IWMSyncReader *This,
        WORD wStream,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *OpenStream)(
        IWMSyncReader *This,
        IStream *pStream);

    END_INTERFACE
} IWMSyncReaderVtbl;

interface IWMSyncReader {
    CONST_VTBL IWMSyncReaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMSyncReader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMSyncReader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMSyncReader_Release(This) (This)->lpVtbl->Release(This)
/*** IWMSyncReader methods ***/
#define IWMSyncReader_Open(This,pwszFilename) (This)->lpVtbl->Open(This,pwszFilename)
#define IWMSyncReader_Close(This) (This)->lpVtbl->Close(This)
#define IWMSyncReader_SetRange(This,cnsStartTime,cnsDuration) (This)->lpVtbl->SetRange(This,cnsStartTime,cnsDuration)
#define IWMSyncReader_SetRangeByFrame(This,wStreamNum,qwFrameNumber,cFramesToRead) (This)->lpVtbl->SetRangeByFrame(This,wStreamNum,qwFrameNumber,cFramesToRead)
#define IWMSyncReader_GetNextSample(This,wStreamNum,ppSample,pcnsSampleTime,pcnsDuration,pdwFlags,pdwOutputNum,pwStreamNum) (This)->lpVtbl->GetNextSample(This,wStreamNum,ppSample,pcnsSampleTime,pcnsDuration,pdwFlags,pdwOutputNum,pwStreamNum)
#define IWMSyncReader_SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections) (This)->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections)
#define IWMSyncReader_GetStreamSelected(This,wStreamNum,pSelection) (This)->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection)
#define IWMSyncReader_SetReadStreamSamples(This,wStreamNum,fCompressed) (This)->lpVtbl->SetReadStreamSamples(This,wStreamNum,fCompressed)
#define IWMSyncReader_GetReadStreamSamples(This,wStreamNum,pfCompressed) (This)->lpVtbl->GetReadStreamSamples(This,wStreamNum,pfCompressed)
#define IWMSyncReader_GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength) (This)->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength)
#define IWMSyncReader_SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength) (This)->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength)
#define IWMSyncReader_GetOutputCount(This,pcOutputs) (This)->lpVtbl->GetOutputCount(This,pcOutputs)
#define IWMSyncReader_GetOutputProps(This,dwOutputNum,ppOutput) (This)->lpVtbl->GetOutputProps(This,dwOutputNum,ppOutput)
#define IWMSyncReader_SetOutputProps(This,dwOutputNum,pOutput) (This)->lpVtbl->SetOutputProps(This,dwOutputNum,pOutput)
#define IWMSyncReader_GetOutputFormatCount(This,dwOutputNum,pcFormats) (This)->lpVtbl->GetOutputFormatCount(This,dwOutputNum,pcFormats)
#define IWMSyncReader_GetOutputFormat(This,dwOutputNum,dwFormatNum,ppProps) (This)->lpVtbl->GetOutputFormat(This,dwOutputNum,dwFormatNum,ppProps)
#define IWMSyncReader_GetOutputNumberForStream(This,wStreamNum,pdwOutputNum) (This)->lpVtbl->GetOutputNumberForStream(This,wStreamNum,pdwOutputNum)
#define IWMSyncReader_GetStreamNumberForOutput(This,dwOutputNum,pwStreamNum) (This)->lpVtbl->GetStreamNumberForOutput(This,dwOutputNum,pwStreamNum)
#define IWMSyncReader_GetMaxOutputSampleSize(This,dwOutput,pcbMax) (This)->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax)
#define IWMSyncReader_GetMaxStreamSampleSize(This,wStream,pcbMax) (This)->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax)
#define IWMSyncReader_OpenStream(This,pStream) (This)->lpVtbl->OpenStream(This,pStream)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMSyncReader_QueryInterface(IWMSyncReader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMSyncReader_AddRef(IWMSyncReader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMSyncReader_Release(IWMSyncReader* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMSyncReader methods ***/
static inline HRESULT IWMSyncReader_Open(IWMSyncReader* This,const WCHAR *pwszFilename) {
    return This->lpVtbl->Open(This,pwszFilename);
}
static inline HRESULT IWMSyncReader_Close(IWMSyncReader* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IWMSyncReader_SetRange(IWMSyncReader* This,QWORD cnsStartTime,LONGLONG cnsDuration) {
    return This->lpVtbl->SetRange(This,cnsStartTime,cnsDuration);
}
static inline HRESULT IWMSyncReader_SetRangeByFrame(IWMSyncReader* This,WORD wStreamNum,QWORD qwFrameNumber,LONGLONG cFramesToRead) {
    return This->lpVtbl->SetRangeByFrame(This,wStreamNum,qwFrameNumber,cFramesToRead);
}
static inline HRESULT IWMSyncReader_GetNextSample(IWMSyncReader* This,WORD wStreamNum,INSSBuffer **ppSample,QWORD *pcnsSampleTime,QWORD *pcnsDuration,DWORD *pdwFlags,DWORD *pdwOutputNum,WORD *pwStreamNum) {
    return This->lpVtbl->GetNextSample(This,wStreamNum,ppSample,pcnsSampleTime,pcnsDuration,pdwFlags,pdwOutputNum,pwStreamNum);
}
static inline HRESULT IWMSyncReader_SetStreamsSelected(IWMSyncReader* This,WORD cStreamCount,WORD *pwStreamNumbers,WMT_STREAM_SELECTION *pSelections) {
    return This->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections);
}
static inline HRESULT IWMSyncReader_GetStreamSelected(IWMSyncReader* This,WORD wStreamNum,WMT_STREAM_SELECTION *pSelection) {
    return This->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection);
}
static inline HRESULT IWMSyncReader_SetReadStreamSamples(IWMSyncReader* This,WORD wStreamNum,WINBOOL fCompressed) {
    return This->lpVtbl->SetReadStreamSamples(This,wStreamNum,fCompressed);
}
static inline HRESULT IWMSyncReader_GetReadStreamSamples(IWMSyncReader* This,WORD wStreamNum,WINBOOL *pfCompressed) {
    return This->lpVtbl->GetReadStreamSamples(This,wStreamNum,pfCompressed);
}
static inline HRESULT IWMSyncReader_GetOutputSetting(IWMSyncReader* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE *pType,BYTE *pValue,WORD *pcbLength) {
    return This->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength);
}
static inline HRESULT IWMSyncReader_SetOutputSetting(IWMSyncReader* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE Type,const BYTE *pValue,WORD cbLength) {
    return This->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength);
}
static inline HRESULT IWMSyncReader_GetOutputCount(IWMSyncReader* This,DWORD *pcOutputs) {
    return This->lpVtbl->GetOutputCount(This,pcOutputs);
}
static inline HRESULT IWMSyncReader_GetOutputProps(IWMSyncReader* This,DWORD dwOutputNum,IWMOutputMediaProps **ppOutput) {
    return This->lpVtbl->GetOutputProps(This,dwOutputNum,ppOutput);
}
static inline HRESULT IWMSyncReader_SetOutputProps(IWMSyncReader* This,DWORD dwOutputNum,IWMOutputMediaProps *pOutput) {
    return This->lpVtbl->SetOutputProps(This,dwOutputNum,pOutput);
}
static inline HRESULT IWMSyncReader_GetOutputFormatCount(IWMSyncReader* This,DWORD dwOutputNum,DWORD *pcFormats) {
    return This->lpVtbl->GetOutputFormatCount(This,dwOutputNum,pcFormats);
}
static inline HRESULT IWMSyncReader_GetOutputFormat(IWMSyncReader* This,DWORD dwOutputNum,DWORD dwFormatNum,IWMOutputMediaProps **ppProps) {
    return This->lpVtbl->GetOutputFormat(This,dwOutputNum,dwFormatNum,ppProps);
}
static inline HRESULT IWMSyncReader_GetOutputNumberForStream(IWMSyncReader* This,WORD wStreamNum,DWORD *pdwOutputNum) {
    return This->lpVtbl->GetOutputNumberForStream(This,wStreamNum,pdwOutputNum);
}
static inline HRESULT IWMSyncReader_GetStreamNumberForOutput(IWMSyncReader* This,DWORD dwOutputNum,WORD *pwStreamNum) {
    return This->lpVtbl->GetStreamNumberForOutput(This,dwOutputNum,pwStreamNum);
}
static inline HRESULT IWMSyncReader_GetMaxOutputSampleSize(IWMSyncReader* This,DWORD dwOutput,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax);
}
static inline HRESULT IWMSyncReader_GetMaxStreamSampleSize(IWMSyncReader* This,WORD wStream,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax);
}
static inline HRESULT IWMSyncReader_OpenStream(IWMSyncReader* This,IStream *pStream) {
    return This->lpVtbl->OpenStream(This,pStream);
}
#endif
#endif

#endif


#endif  /* __IWMSyncReader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderAllocatorEx interface
 */
#ifndef __IWMReaderAllocatorEx_INTERFACE_DEFINED__
#define __IWMReaderAllocatorEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderAllocatorEx, 0x9f762fa7, 0xa22e, 0x428d, 0x93,0xc9, 0xac,0x82,0xf3,0xaa,0xfe,0x5a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9f762fa7-a22e-428d-93c9-ac82f3aafe5a")
IWMReaderAllocatorEx : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AllocateForStreamEx(
        WORD wStreamNum,
        DWORD cbBuffer,
        INSSBuffer **ppBuffer,
        DWORD dwFlags,
        QWORD cnsSampleTime,
        QWORD cnsSampleDuration,
        void *pvContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE AllocateForOutputEx(
        DWORD output,
        DWORD cbBuffer,
        INSSBuffer **ppBuffer,
        DWORD dwFlags,
        QWORD cnsSampleTime,
        QWORD cnsSampleDuration,
        void *pvContext) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderAllocatorEx, 0x9f762fa7, 0xa22e, 0x428d, 0x93,0xc9, 0xac,0x82,0xf3,0xaa,0xfe,0x5a)
#endif
#else
typedef struct IWMReaderAllocatorExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderAllocatorEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderAllocatorEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderAllocatorEx *This);

    /*** IWMReaderAllocatorEx methods ***/
    HRESULT (STDMETHODCALLTYPE *AllocateForStreamEx)(
        IWMReaderAllocatorEx *This,
        WORD wStreamNum,
        DWORD cbBuffer,
        INSSBuffer **ppBuffer,
        DWORD dwFlags,
        QWORD cnsSampleTime,
        QWORD cnsSampleDuration,
        void *pvContext);

    HRESULT (STDMETHODCALLTYPE *AllocateForOutputEx)(
        IWMReaderAllocatorEx *This,
        DWORD output,
        DWORD cbBuffer,
        INSSBuffer **ppBuffer,
        DWORD dwFlags,
        QWORD cnsSampleTime,
        QWORD cnsSampleDuration,
        void *pvContext);

    END_INTERFACE
} IWMReaderAllocatorExVtbl;

interface IWMReaderAllocatorEx {
    CONST_VTBL IWMReaderAllocatorExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderAllocatorEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderAllocatorEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderAllocatorEx_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderAllocatorEx methods ***/
#define IWMReaderAllocatorEx_AllocateForStreamEx(This,wStreamNum,cbBuffer,ppBuffer,dwFlags,cnsSampleTime,cnsSampleDuration,pvContext) (This)->lpVtbl->AllocateForStreamEx(This,wStreamNum,cbBuffer,ppBuffer,dwFlags,cnsSampleTime,cnsSampleDuration,pvContext)
#define IWMReaderAllocatorEx_AllocateForOutputEx(This,output,cbBuffer,ppBuffer,dwFlags,cnsSampleTime,cnsSampleDuration,pvContext) (This)->lpVtbl->AllocateForOutputEx(This,output,cbBuffer,ppBuffer,dwFlags,cnsSampleTime,cnsSampleDuration,pvContext)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderAllocatorEx_QueryInterface(IWMReaderAllocatorEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderAllocatorEx_AddRef(IWMReaderAllocatorEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderAllocatorEx_Release(IWMReaderAllocatorEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderAllocatorEx methods ***/
static inline HRESULT IWMReaderAllocatorEx_AllocateForStreamEx(IWMReaderAllocatorEx* This,WORD wStreamNum,DWORD cbBuffer,INSSBuffer **ppBuffer,DWORD dwFlags,QWORD cnsSampleTime,QWORD cnsSampleDuration,void *pvContext) {
    return This->lpVtbl->AllocateForStreamEx(This,wStreamNum,cbBuffer,ppBuffer,dwFlags,cnsSampleTime,cnsSampleDuration,pvContext);
}
static inline HRESULT IWMReaderAllocatorEx_AllocateForOutputEx(IWMReaderAllocatorEx* This,DWORD output,DWORD cbBuffer,INSSBuffer **ppBuffer,DWORD dwFlags,QWORD cnsSampleTime,QWORD cnsSampleDuration,void *pvContext) {
    return This->lpVtbl->AllocateForOutputEx(This,output,cbBuffer,ppBuffer,dwFlags,cnsSampleTime,cnsSampleDuration,pvContext);
}
#endif
#endif

#endif


#endif  /* __IWMReaderAllocatorEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMSyncReader2 interface
 */
#ifndef __IWMSyncReader2_INTERFACE_DEFINED__
#define __IWMSyncReader2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMSyncReader2, 0xfaed3d21, 0x1b6b, 0x4af7, 0x8c,0xb6, 0x3e,0x18,0x9b,0xbc,0x18,0x7b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("faed3d21-1b6b-4af7-8cb6-3e189bbc187b")
IWMSyncReader2 : public IWMSyncReader
{
    virtual HRESULT STDMETHODCALLTYPE SetRangeByTimecode(
        WORD wStreamNum,
        WMT_TIMECODE_EXTENSION_DATA *pStart,
        WMT_TIMECODE_EXTENSION_DATA *pEnd) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRangeByFrameEx(
        WORD wStreamNum,
        QWORD qwFrameNumber,
        LONGLONG cFramesToRead,
        QWORD *pcnsStartTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAllocateForOutput(
        DWORD dwOutputNum,
        IWMReaderAllocatorEx *pAllocator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllocateForOutput(
        DWORD dwOutputNum,
        IWMReaderAllocatorEx **ppAllocator) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAllocateForStream(
        DWORD dwStreamNum,
        IWMReaderAllocatorEx *pAllocator) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllocateForStream(
        DWORD dwStreamNum,
        IWMReaderAllocatorEx **ppAllocator) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMSyncReader2, 0xfaed3d21, 0x1b6b, 0x4af7, 0x8c,0xb6, 0x3e,0x18,0x9b,0xbc,0x18,0x7b)
#endif
#else
typedef struct IWMSyncReader2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMSyncReader2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMSyncReader2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMSyncReader2 *This);

    /*** IWMSyncReader methods ***/
    HRESULT (STDMETHODCALLTYPE *Open)(
        IWMSyncReader2 *This,
        const WCHAR *pwszFilename);

    HRESULT (STDMETHODCALLTYPE *Close)(
        IWMSyncReader2 *This);

    HRESULT (STDMETHODCALLTYPE *SetRange)(
        IWMSyncReader2 *This,
        QWORD cnsStartTime,
        LONGLONG cnsDuration);

    HRESULT (STDMETHODCALLTYPE *SetRangeByFrame)(
        IWMSyncReader2 *This,
        WORD wStreamNum,
        QWORD qwFrameNumber,
        LONGLONG cFramesToRead);

    HRESULT (STDMETHODCALLTYPE *GetNextSample)(
        IWMSyncReader2 *This,
        WORD wStreamNum,
        INSSBuffer **ppSample,
        QWORD *pcnsSampleTime,
        QWORD *pcnsDuration,
        DWORD *pdwFlags,
        DWORD *pdwOutputNum,
        WORD *pwStreamNum);

    HRESULT (STDMETHODCALLTYPE *SetStreamsSelected)(
        IWMSyncReader2 *This,
        WORD cStreamCount,
        WORD *pwStreamNumbers,
        WMT_STREAM_SELECTION *pSelections);

    HRESULT (STDMETHODCALLTYPE *GetStreamSelected)(
        IWMSyncReader2 *This,
        WORD wStreamNum,
        WMT_STREAM_SELECTION *pSelection);

    HRESULT (STDMETHODCALLTYPE *SetReadStreamSamples)(
        IWMSyncReader2 *This,
        WORD wStreamNum,
        WINBOOL fCompressed);

    HRESULT (STDMETHODCALLTYPE *GetReadStreamSamples)(
        IWMSyncReader2 *This,
        WORD wStreamNum,
        WINBOOL *pfCompressed);

    HRESULT (STDMETHODCALLTYPE *GetOutputSetting)(
        IWMSyncReader2 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength);

    HRESULT (STDMETHODCALLTYPE *SetOutputSetting)(
        IWMSyncReader2 *This,
        DWORD dwOutputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength);

    HRESULT (STDMETHODCALLTYPE *GetOutputCount)(
        IWMSyncReader2 *This,
        DWORD *pcOutputs);

    HRESULT (STDMETHODCALLTYPE *GetOutputProps)(
        IWMSyncReader2 *This,
        DWORD dwOutputNum,
        IWMOutputMediaProps **ppOutput);

    HRESULT (STDMETHODCALLTYPE *SetOutputProps)(
        IWMSyncReader2 *This,
        DWORD dwOutputNum,
        IWMOutputMediaProps *pOutput);

    HRESULT (STDMETHODCALLTYPE *GetOutputFormatCount)(
        IWMSyncReader2 *This,
        DWORD dwOutputNum,
        DWORD *pcFormats);

    HRESULT (STDMETHODCALLTYPE *GetOutputFormat)(
        IWMSyncReader2 *This,
        DWORD dwOutputNum,
        DWORD dwFormatNum,
        IWMOutputMediaProps **ppProps);

    HRESULT (STDMETHODCALLTYPE *GetOutputNumberForStream)(
        IWMSyncReader2 *This,
        WORD wStreamNum,
        DWORD *pdwOutputNum);

    HRESULT (STDMETHODCALLTYPE *GetStreamNumberForOutput)(
        IWMSyncReader2 *This,
        DWORD dwOutputNum,
        WORD *pwStreamNum);

    HRESULT (STDMETHODCALLTYPE *GetMaxOutputSampleSize)(
        IWMSyncReader2 *This,
        DWORD dwOutput,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *GetMaxStreamSampleSize)(
        IWMSyncReader2 *This,
        WORD wStream,
        DWORD *pcbMax);

    HRESULT (STDMETHODCALLTYPE *OpenStream)(
        IWMSyncReader2 *This,
        IStream *pStream);

    /*** IWMSyncReader2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetRangeByTimecode)(
        IWMSyncReader2 *This,
        WORD wStreamNum,
        WMT_TIMECODE_EXTENSION_DATA *pStart,
        WMT_TIMECODE_EXTENSION_DATA *pEnd);

    HRESULT (STDMETHODCALLTYPE *SetRangeByFrameEx)(
        IWMSyncReader2 *This,
        WORD wStreamNum,
        QWORD qwFrameNumber,
        LONGLONG cFramesToRead,
        QWORD *pcnsStartTime);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForOutput)(
        IWMSyncReader2 *This,
        DWORD dwOutputNum,
        IWMReaderAllocatorEx *pAllocator);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForOutput)(
        IWMSyncReader2 *This,
        DWORD dwOutputNum,
        IWMReaderAllocatorEx **ppAllocator);

    HRESULT (STDMETHODCALLTYPE *SetAllocateForStream)(
        IWMSyncReader2 *This,
        DWORD dwStreamNum,
        IWMReaderAllocatorEx *pAllocator);

    HRESULT (STDMETHODCALLTYPE *GetAllocateForStream)(
        IWMSyncReader2 *This,
        DWORD dwStreamNum,
        IWMReaderAllocatorEx **ppAllocator);

    END_INTERFACE
} IWMSyncReader2Vtbl;

interface IWMSyncReader2 {
    CONST_VTBL IWMSyncReader2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMSyncReader2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMSyncReader2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMSyncReader2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMSyncReader methods ***/
#define IWMSyncReader2_Open(This,pwszFilename) (This)->lpVtbl->Open(This,pwszFilename)
#define IWMSyncReader2_Close(This) (This)->lpVtbl->Close(This)
#define IWMSyncReader2_SetRange(This,cnsStartTime,cnsDuration) (This)->lpVtbl->SetRange(This,cnsStartTime,cnsDuration)
#define IWMSyncReader2_SetRangeByFrame(This,wStreamNum,qwFrameNumber,cFramesToRead) (This)->lpVtbl->SetRangeByFrame(This,wStreamNum,qwFrameNumber,cFramesToRead)
#define IWMSyncReader2_GetNextSample(This,wStreamNum,ppSample,pcnsSampleTime,pcnsDuration,pdwFlags,pdwOutputNum,pwStreamNum) (This)->lpVtbl->GetNextSample(This,wStreamNum,ppSample,pcnsSampleTime,pcnsDuration,pdwFlags,pdwOutputNum,pwStreamNum)
#define IWMSyncReader2_SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections) (This)->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections)
#define IWMSyncReader2_GetStreamSelected(This,wStreamNum,pSelection) (This)->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection)
#define IWMSyncReader2_SetReadStreamSamples(This,wStreamNum,fCompressed) (This)->lpVtbl->SetReadStreamSamples(This,wStreamNum,fCompressed)
#define IWMSyncReader2_GetReadStreamSamples(This,wStreamNum,pfCompressed) (This)->lpVtbl->GetReadStreamSamples(This,wStreamNum,pfCompressed)
#define IWMSyncReader2_GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength) (This)->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength)
#define IWMSyncReader2_SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength) (This)->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength)
#define IWMSyncReader2_GetOutputCount(This,pcOutputs) (This)->lpVtbl->GetOutputCount(This,pcOutputs)
#define IWMSyncReader2_GetOutputProps(This,dwOutputNum,ppOutput) (This)->lpVtbl->GetOutputProps(This,dwOutputNum,ppOutput)
#define IWMSyncReader2_SetOutputProps(This,dwOutputNum,pOutput) (This)->lpVtbl->SetOutputProps(This,dwOutputNum,pOutput)
#define IWMSyncReader2_GetOutputFormatCount(This,dwOutputNum,pcFormats) (This)->lpVtbl->GetOutputFormatCount(This,dwOutputNum,pcFormats)
#define IWMSyncReader2_GetOutputFormat(This,dwOutputNum,dwFormatNum,ppProps) (This)->lpVtbl->GetOutputFormat(This,dwOutputNum,dwFormatNum,ppProps)
#define IWMSyncReader2_GetOutputNumberForStream(This,wStreamNum,pdwOutputNum) (This)->lpVtbl->GetOutputNumberForStream(This,wStreamNum,pdwOutputNum)
#define IWMSyncReader2_GetStreamNumberForOutput(This,dwOutputNum,pwStreamNum) (This)->lpVtbl->GetStreamNumberForOutput(This,dwOutputNum,pwStreamNum)
#define IWMSyncReader2_GetMaxOutputSampleSize(This,dwOutput,pcbMax) (This)->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax)
#define IWMSyncReader2_GetMaxStreamSampleSize(This,wStream,pcbMax) (This)->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax)
#define IWMSyncReader2_OpenStream(This,pStream) (This)->lpVtbl->OpenStream(This,pStream)
/*** IWMSyncReader2 methods ***/
#define IWMSyncReader2_SetRangeByTimecode(This,wStreamNum,pStart,pEnd) (This)->lpVtbl->SetRangeByTimecode(This,wStreamNum,pStart,pEnd)
#define IWMSyncReader2_SetRangeByFrameEx(This,wStreamNum,qwFrameNumber,cFramesToRead,pcnsStartTime) (This)->lpVtbl->SetRangeByFrameEx(This,wStreamNum,qwFrameNumber,cFramesToRead,pcnsStartTime)
#define IWMSyncReader2_SetAllocateForOutput(This,dwOutputNum,pAllocator) (This)->lpVtbl->SetAllocateForOutput(This,dwOutputNum,pAllocator)
#define IWMSyncReader2_GetAllocateForOutput(This,dwOutputNum,ppAllocator) (This)->lpVtbl->GetAllocateForOutput(This,dwOutputNum,ppAllocator)
#define IWMSyncReader2_SetAllocateForStream(This,dwStreamNum,pAllocator) (This)->lpVtbl->SetAllocateForStream(This,dwStreamNum,pAllocator)
#define IWMSyncReader2_GetAllocateForStream(This,dwStreamNum,ppAllocator) (This)->lpVtbl->GetAllocateForStream(This,dwStreamNum,ppAllocator)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMSyncReader2_QueryInterface(IWMSyncReader2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMSyncReader2_AddRef(IWMSyncReader2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMSyncReader2_Release(IWMSyncReader2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMSyncReader methods ***/
static inline HRESULT IWMSyncReader2_Open(IWMSyncReader2* This,const WCHAR *pwszFilename) {
    return This->lpVtbl->Open(This,pwszFilename);
}
static inline HRESULT IWMSyncReader2_Close(IWMSyncReader2* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT IWMSyncReader2_SetRange(IWMSyncReader2* This,QWORD cnsStartTime,LONGLONG cnsDuration) {
    return This->lpVtbl->SetRange(This,cnsStartTime,cnsDuration);
}
static inline HRESULT IWMSyncReader2_SetRangeByFrame(IWMSyncReader2* This,WORD wStreamNum,QWORD qwFrameNumber,LONGLONG cFramesToRead) {
    return This->lpVtbl->SetRangeByFrame(This,wStreamNum,qwFrameNumber,cFramesToRead);
}
static inline HRESULT IWMSyncReader2_GetNextSample(IWMSyncReader2* This,WORD wStreamNum,INSSBuffer **ppSample,QWORD *pcnsSampleTime,QWORD *pcnsDuration,DWORD *pdwFlags,DWORD *pdwOutputNum,WORD *pwStreamNum) {
    return This->lpVtbl->GetNextSample(This,wStreamNum,ppSample,pcnsSampleTime,pcnsDuration,pdwFlags,pdwOutputNum,pwStreamNum);
}
static inline HRESULT IWMSyncReader2_SetStreamsSelected(IWMSyncReader2* This,WORD cStreamCount,WORD *pwStreamNumbers,WMT_STREAM_SELECTION *pSelections) {
    return This->lpVtbl->SetStreamsSelected(This,cStreamCount,pwStreamNumbers,pSelections);
}
static inline HRESULT IWMSyncReader2_GetStreamSelected(IWMSyncReader2* This,WORD wStreamNum,WMT_STREAM_SELECTION *pSelection) {
    return This->lpVtbl->GetStreamSelected(This,wStreamNum,pSelection);
}
static inline HRESULT IWMSyncReader2_SetReadStreamSamples(IWMSyncReader2* This,WORD wStreamNum,WINBOOL fCompressed) {
    return This->lpVtbl->SetReadStreamSamples(This,wStreamNum,fCompressed);
}
static inline HRESULT IWMSyncReader2_GetReadStreamSamples(IWMSyncReader2* This,WORD wStreamNum,WINBOOL *pfCompressed) {
    return This->lpVtbl->GetReadStreamSamples(This,wStreamNum,pfCompressed);
}
static inline HRESULT IWMSyncReader2_GetOutputSetting(IWMSyncReader2* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE *pType,BYTE *pValue,WORD *pcbLength) {
    return This->lpVtbl->GetOutputSetting(This,dwOutputNum,pszName,pType,pValue,pcbLength);
}
static inline HRESULT IWMSyncReader2_SetOutputSetting(IWMSyncReader2* This,DWORD dwOutputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE Type,const BYTE *pValue,WORD cbLength) {
    return This->lpVtbl->SetOutputSetting(This,dwOutputNum,pszName,Type,pValue,cbLength);
}
static inline HRESULT IWMSyncReader2_GetOutputCount(IWMSyncReader2* This,DWORD *pcOutputs) {
    return This->lpVtbl->GetOutputCount(This,pcOutputs);
}
static inline HRESULT IWMSyncReader2_GetOutputProps(IWMSyncReader2* This,DWORD dwOutputNum,IWMOutputMediaProps **ppOutput) {
    return This->lpVtbl->GetOutputProps(This,dwOutputNum,ppOutput);
}
static inline HRESULT IWMSyncReader2_SetOutputProps(IWMSyncReader2* This,DWORD dwOutputNum,IWMOutputMediaProps *pOutput) {
    return This->lpVtbl->SetOutputProps(This,dwOutputNum,pOutput);
}
static inline HRESULT IWMSyncReader2_GetOutputFormatCount(IWMSyncReader2* This,DWORD dwOutputNum,DWORD *pcFormats) {
    return This->lpVtbl->GetOutputFormatCount(This,dwOutputNum,pcFormats);
}
static inline HRESULT IWMSyncReader2_GetOutputFormat(IWMSyncReader2* This,DWORD dwOutputNum,DWORD dwFormatNum,IWMOutputMediaProps **ppProps) {
    return This->lpVtbl->GetOutputFormat(This,dwOutputNum,dwFormatNum,ppProps);
}
static inline HRESULT IWMSyncReader2_GetOutputNumberForStream(IWMSyncReader2* This,WORD wStreamNum,DWORD *pdwOutputNum) {
    return This->lpVtbl->GetOutputNumberForStream(This,wStreamNum,pdwOutputNum);
}
static inline HRESULT IWMSyncReader2_GetStreamNumberForOutput(IWMSyncReader2* This,DWORD dwOutputNum,WORD *pwStreamNum) {
    return This->lpVtbl->GetStreamNumberForOutput(This,dwOutputNum,pwStreamNum);
}
static inline HRESULT IWMSyncReader2_GetMaxOutputSampleSize(IWMSyncReader2* This,DWORD dwOutput,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxOutputSampleSize(This,dwOutput,pcbMax);
}
static inline HRESULT IWMSyncReader2_GetMaxStreamSampleSize(IWMSyncReader2* This,WORD wStream,DWORD *pcbMax) {
    return This->lpVtbl->GetMaxStreamSampleSize(This,wStream,pcbMax);
}
static inline HRESULT IWMSyncReader2_OpenStream(IWMSyncReader2* This,IStream *pStream) {
    return This->lpVtbl->OpenStream(This,pStream);
}
/*** IWMSyncReader2 methods ***/
static inline HRESULT IWMSyncReader2_SetRangeByTimecode(IWMSyncReader2* This,WORD wStreamNum,WMT_TIMECODE_EXTENSION_DATA *pStart,WMT_TIMECODE_EXTENSION_DATA *pEnd) {
    return This->lpVtbl->SetRangeByTimecode(This,wStreamNum,pStart,pEnd);
}
static inline HRESULT IWMSyncReader2_SetRangeByFrameEx(IWMSyncReader2* This,WORD wStreamNum,QWORD qwFrameNumber,LONGLONG cFramesToRead,QWORD *pcnsStartTime) {
    return This->lpVtbl->SetRangeByFrameEx(This,wStreamNum,qwFrameNumber,cFramesToRead,pcnsStartTime);
}
static inline HRESULT IWMSyncReader2_SetAllocateForOutput(IWMSyncReader2* This,DWORD dwOutputNum,IWMReaderAllocatorEx *pAllocator) {
    return This->lpVtbl->SetAllocateForOutput(This,dwOutputNum,pAllocator);
}
static inline HRESULT IWMSyncReader2_GetAllocateForOutput(IWMSyncReader2* This,DWORD dwOutputNum,IWMReaderAllocatorEx **ppAllocator) {
    return This->lpVtbl->GetAllocateForOutput(This,dwOutputNum,ppAllocator);
}
static inline HRESULT IWMSyncReader2_SetAllocateForStream(IWMSyncReader2* This,DWORD dwStreamNum,IWMReaderAllocatorEx *pAllocator) {
    return This->lpVtbl->SetAllocateForStream(This,dwStreamNum,pAllocator);
}
static inline HRESULT IWMSyncReader2_GetAllocateForStream(IWMSyncReader2* This,DWORD dwStreamNum,IWMReaderAllocatorEx **ppAllocator) {
    return This->lpVtbl->GetAllocateForStream(This,dwStreamNum,ppAllocator);
}
#endif
#endif

#endif


#endif  /* __IWMSyncReader2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMInputMediaProps interface
 */
#ifndef __IWMInputMediaProps_INTERFACE_DEFINED__
#define __IWMInputMediaProps_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMInputMediaProps, 0x96406bd5, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bd5-2b2b-11d3-b36b-00c04f6108ff")
IWMInputMediaProps : public IWMMediaProps
{
    virtual HRESULT STDMETHODCALLTYPE GetConnectionName(
        WCHAR *pwszName,
        WORD *pcchName) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGroupName(
        WCHAR *pwszName,
        WORD *pcchName) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMInputMediaProps, 0x96406bd5, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMInputMediaPropsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMInputMediaProps *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMInputMediaProps *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMInputMediaProps *This);

    /*** IWMMediaProps methods ***/
    HRESULT (STDMETHODCALLTYPE *GetType)(
        IWMInputMediaProps *This,
        GUID *pguidType);

    HRESULT (STDMETHODCALLTYPE *GetMediaType)(
        IWMInputMediaProps *This,
        WM_MEDIA_TYPE *pType,
        DWORD *pcbType);

    HRESULT (STDMETHODCALLTYPE *SetMediaType)(
        IWMInputMediaProps *This,
        WM_MEDIA_TYPE *pType);

    /*** IWMInputMediaProps methods ***/
    HRESULT (STDMETHODCALLTYPE *GetConnectionName)(
        IWMInputMediaProps *This,
        WCHAR *pwszName,
        WORD *pcchName);

    HRESULT (STDMETHODCALLTYPE *GetGroupName)(
        IWMInputMediaProps *This,
        WCHAR *pwszName,
        WORD *pcchName);

    END_INTERFACE
} IWMInputMediaPropsVtbl;

interface IWMInputMediaProps {
    CONST_VTBL IWMInputMediaPropsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMInputMediaProps_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMInputMediaProps_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMInputMediaProps_Release(This) (This)->lpVtbl->Release(This)
/*** IWMMediaProps methods ***/
#define IWMInputMediaProps_GetType(This,pguidType) (This)->lpVtbl->GetType(This,pguidType)
#define IWMInputMediaProps_GetMediaType(This,pType,pcbType) (This)->lpVtbl->GetMediaType(This,pType,pcbType)
#define IWMInputMediaProps_SetMediaType(This,pType) (This)->lpVtbl->SetMediaType(This,pType)
/*** IWMInputMediaProps methods ***/
#define IWMInputMediaProps_GetConnectionName(This,pwszName,pcchName) (This)->lpVtbl->GetConnectionName(This,pwszName,pcchName)
#define IWMInputMediaProps_GetGroupName(This,pwszName,pcchName) (This)->lpVtbl->GetGroupName(This,pwszName,pcchName)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMInputMediaProps_QueryInterface(IWMInputMediaProps* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMInputMediaProps_AddRef(IWMInputMediaProps* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMInputMediaProps_Release(IWMInputMediaProps* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMMediaProps methods ***/
static inline HRESULT IWMInputMediaProps_GetType(IWMInputMediaProps* This,GUID *pguidType) {
    return This->lpVtbl->GetType(This,pguidType);
}
static inline HRESULT IWMInputMediaProps_GetMediaType(IWMInputMediaProps* This,WM_MEDIA_TYPE *pType,DWORD *pcbType) {
    return This->lpVtbl->GetMediaType(This,pType,pcbType);
}
static inline HRESULT IWMInputMediaProps_SetMediaType(IWMInputMediaProps* This,WM_MEDIA_TYPE *pType) {
    return This->lpVtbl->SetMediaType(This,pType);
}
/*** IWMInputMediaProps methods ***/
static inline HRESULT IWMInputMediaProps_GetConnectionName(IWMInputMediaProps* This,WCHAR *pwszName,WORD *pcchName) {
    return This->lpVtbl->GetConnectionName(This,pwszName,pcchName);
}
static inline HRESULT IWMInputMediaProps_GetGroupName(IWMInputMediaProps* This,WCHAR *pwszName,WORD *pcchName) {
    return This->lpVtbl->GetGroupName(This,pwszName,pcchName);
}
#endif
#endif

#endif


#endif  /* __IWMInputMediaProps_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMWriterSink interface
 */
#ifndef __IWMWriterSink_INTERFACE_DEFINED__
#define __IWMWriterSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMWriterSink, 0x96406be4, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406be4-2b2b-11d3-b36b-00c04f6108ff")
IWMWriterSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnHeader(
        INSSBuffer *pHeader) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsRealTime(
        WINBOOL *pfRealTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE AllocateDataUnit(
        DWORD cbDataUnit,
        INSSBuffer **ppDataUnit) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnDataUnit(
        INSSBuffer *pDataUnit) = 0;

    virtual HRESULT STDMETHODCALLTYPE OnEndWriting(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMWriterSink, 0x96406be4, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMWriterSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMWriterSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMWriterSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMWriterSink *This);

    /*** IWMWriterSink methods ***/
    HRESULT (STDMETHODCALLTYPE *OnHeader)(
        IWMWriterSink *This,
        INSSBuffer *pHeader);

    HRESULT (STDMETHODCALLTYPE *IsRealTime)(
        IWMWriterSink *This,
        WINBOOL *pfRealTime);

    HRESULT (STDMETHODCALLTYPE *AllocateDataUnit)(
        IWMWriterSink *This,
        DWORD cbDataUnit,
        INSSBuffer **ppDataUnit);

    HRESULT (STDMETHODCALLTYPE *OnDataUnit)(
        IWMWriterSink *This,
        INSSBuffer *pDataUnit);

    HRESULT (STDMETHODCALLTYPE *OnEndWriting)(
        IWMWriterSink *This);

    END_INTERFACE
} IWMWriterSinkVtbl;

interface IWMWriterSink {
    CONST_VTBL IWMWriterSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMWriterSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMWriterSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMWriterSink_Release(This) (This)->lpVtbl->Release(This)
/*** IWMWriterSink methods ***/
#define IWMWriterSink_OnHeader(This,pHeader) (This)->lpVtbl->OnHeader(This,pHeader)
#define IWMWriterSink_IsRealTime(This,pfRealTime) (This)->lpVtbl->IsRealTime(This,pfRealTime)
#define IWMWriterSink_AllocateDataUnit(This,cbDataUnit,ppDataUnit) (This)->lpVtbl->AllocateDataUnit(This,cbDataUnit,ppDataUnit)
#define IWMWriterSink_OnDataUnit(This,pDataUnit) (This)->lpVtbl->OnDataUnit(This,pDataUnit)
#define IWMWriterSink_OnEndWriting(This) (This)->lpVtbl->OnEndWriting(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMWriterSink_QueryInterface(IWMWriterSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMWriterSink_AddRef(IWMWriterSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMWriterSink_Release(IWMWriterSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMWriterSink methods ***/
static inline HRESULT IWMWriterSink_OnHeader(IWMWriterSink* This,INSSBuffer *pHeader) {
    return This->lpVtbl->OnHeader(This,pHeader);
}
static inline HRESULT IWMWriterSink_IsRealTime(IWMWriterSink* This,WINBOOL *pfRealTime) {
    return This->lpVtbl->IsRealTime(This,pfRealTime);
}
static inline HRESULT IWMWriterSink_AllocateDataUnit(IWMWriterSink* This,DWORD cbDataUnit,INSSBuffer **ppDataUnit) {
    return This->lpVtbl->AllocateDataUnit(This,cbDataUnit,ppDataUnit);
}
static inline HRESULT IWMWriterSink_OnDataUnit(IWMWriterSink* This,INSSBuffer *pDataUnit) {
    return This->lpVtbl->OnDataUnit(This,pDataUnit);
}
static inline HRESULT IWMWriterSink_OnEndWriting(IWMWriterSink* This) {
    return This->lpVtbl->OnEndWriting(This);
}
#endif
#endif

#endif


#endif  /* __IWMWriterSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMWriter interface
 */
#ifndef __IWMWriter_INTERFACE_DEFINED__
#define __IWMWriter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMWriter, 0x96406bd4, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bd4-2b2b-11d3-b36b-00c04f6108ff")
IWMWriter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetProfileByID(
        REFGUID guidProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProfile(
        IWMProfile *pProfile) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputFilename(
        const WCHAR *pwszFilename) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInputCount(
        DWORD *pcInputs) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInputProps(
        DWORD dwInputNum,
        IWMInputMediaProps **ppInput) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInputProps(
        DWORD dwInputNum,
        IWMInputMediaProps *pInput) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInputFormatCount(
        DWORD dwInputNumber,
        DWORD *pcFormats) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInputFormat(
        DWORD dwInputNumber,
        DWORD dwFormatNumber,
        IWMInputMediaProps **pProps) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginWriting(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndWriting(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AllocateSample(
        DWORD dwSampleSize,
        INSSBuffer **ppSample) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteSample(
        DWORD dwInputNum,
        QWORD cnsSampleTime,
        DWORD dwFlags,
        INSSBuffer *pSample) = 0;

    virtual HRESULT STDMETHODCALLTYPE Flush(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMWriter, 0x96406bd4, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMWriterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMWriter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMWriter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMWriter *This);

    /*** IWMWriter methods ***/
    HRESULT (STDMETHODCALLTYPE *SetProfileByID)(
        IWMWriter *This,
        REFGUID guidProfile);

    HRESULT (STDMETHODCALLTYPE *SetProfile)(
        IWMWriter *This,
        IWMProfile *pProfile);

    HRESULT (STDMETHODCALLTYPE *SetOutputFilename)(
        IWMWriter *This,
        const WCHAR *pwszFilename);

    HRESULT (STDMETHODCALLTYPE *GetInputCount)(
        IWMWriter *This,
        DWORD *pcInputs);

    HRESULT (STDMETHODCALLTYPE *GetInputProps)(
        IWMWriter *This,
        DWORD dwInputNum,
        IWMInputMediaProps **ppInput);

    HRESULT (STDMETHODCALLTYPE *SetInputProps)(
        IWMWriter *This,
        DWORD dwInputNum,
        IWMInputMediaProps *pInput);

    HRESULT (STDMETHODCALLTYPE *GetInputFormatCount)(
        IWMWriter *This,
        DWORD dwInputNumber,
        DWORD *pcFormats);

    HRESULT (STDMETHODCALLTYPE *GetInputFormat)(
        IWMWriter *This,
        DWORD dwInputNumber,
        DWORD dwFormatNumber,
        IWMInputMediaProps **pProps);

    HRESULT (STDMETHODCALLTYPE *BeginWriting)(
        IWMWriter *This);

    HRESULT (STDMETHODCALLTYPE *EndWriting)(
        IWMWriter *This);

    HRESULT (STDMETHODCALLTYPE *AllocateSample)(
        IWMWriter *This,
        DWORD dwSampleSize,
        INSSBuffer **ppSample);

    HRESULT (STDMETHODCALLTYPE *WriteSample)(
        IWMWriter *This,
        DWORD dwInputNum,
        QWORD cnsSampleTime,
        DWORD dwFlags,
        INSSBuffer *pSample);

    HRESULT (STDMETHODCALLTYPE *Flush)(
        IWMWriter *This);

    END_INTERFACE
} IWMWriterVtbl;

interface IWMWriter {
    CONST_VTBL IWMWriterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMWriter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMWriter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMWriter_Release(This) (This)->lpVtbl->Release(This)
/*** IWMWriter methods ***/
#define IWMWriter_SetProfileByID(This,guidProfile) (This)->lpVtbl->SetProfileByID(This,guidProfile)
#define IWMWriter_SetProfile(This,pProfile) (This)->lpVtbl->SetProfile(This,pProfile)
#define IWMWriter_SetOutputFilename(This,pwszFilename) (This)->lpVtbl->SetOutputFilename(This,pwszFilename)
#define IWMWriter_GetInputCount(This,pcInputs) (This)->lpVtbl->GetInputCount(This,pcInputs)
#define IWMWriter_GetInputProps(This,dwInputNum,ppInput) (This)->lpVtbl->GetInputProps(This,dwInputNum,ppInput)
#define IWMWriter_SetInputProps(This,dwInputNum,pInput) (This)->lpVtbl->SetInputProps(This,dwInputNum,pInput)
#define IWMWriter_GetInputFormatCount(This,dwInputNumber,pcFormats) (This)->lpVtbl->GetInputFormatCount(This,dwInputNumber,pcFormats)
#define IWMWriter_GetInputFormat(This,dwInputNumber,dwFormatNumber,pProps) (This)->lpVtbl->GetInputFormat(This,dwInputNumber,dwFormatNumber,pProps)
#define IWMWriter_BeginWriting(This) (This)->lpVtbl->BeginWriting(This)
#define IWMWriter_EndWriting(This) (This)->lpVtbl->EndWriting(This)
#define IWMWriter_AllocateSample(This,dwSampleSize,ppSample) (This)->lpVtbl->AllocateSample(This,dwSampleSize,ppSample)
#define IWMWriter_WriteSample(This,dwInputNum,cnsSampleTime,dwFlags,pSample) (This)->lpVtbl->WriteSample(This,dwInputNum,cnsSampleTime,dwFlags,pSample)
#define IWMWriter_Flush(This) (This)->lpVtbl->Flush(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMWriter_QueryInterface(IWMWriter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMWriter_AddRef(IWMWriter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMWriter_Release(IWMWriter* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMWriter methods ***/
static inline HRESULT IWMWriter_SetProfileByID(IWMWriter* This,REFGUID guidProfile) {
    return This->lpVtbl->SetProfileByID(This,guidProfile);
}
static inline HRESULT IWMWriter_SetProfile(IWMWriter* This,IWMProfile *pProfile) {
    return This->lpVtbl->SetProfile(This,pProfile);
}
static inline HRESULT IWMWriter_SetOutputFilename(IWMWriter* This,const WCHAR *pwszFilename) {
    return This->lpVtbl->SetOutputFilename(This,pwszFilename);
}
static inline HRESULT IWMWriter_GetInputCount(IWMWriter* This,DWORD *pcInputs) {
    return This->lpVtbl->GetInputCount(This,pcInputs);
}
static inline HRESULT IWMWriter_GetInputProps(IWMWriter* This,DWORD dwInputNum,IWMInputMediaProps **ppInput) {
    return This->lpVtbl->GetInputProps(This,dwInputNum,ppInput);
}
static inline HRESULT IWMWriter_SetInputProps(IWMWriter* This,DWORD dwInputNum,IWMInputMediaProps *pInput) {
    return This->lpVtbl->SetInputProps(This,dwInputNum,pInput);
}
static inline HRESULT IWMWriter_GetInputFormatCount(IWMWriter* This,DWORD dwInputNumber,DWORD *pcFormats) {
    return This->lpVtbl->GetInputFormatCount(This,dwInputNumber,pcFormats);
}
static inline HRESULT IWMWriter_GetInputFormat(IWMWriter* This,DWORD dwInputNumber,DWORD dwFormatNumber,IWMInputMediaProps **pProps) {
    return This->lpVtbl->GetInputFormat(This,dwInputNumber,dwFormatNumber,pProps);
}
static inline HRESULT IWMWriter_BeginWriting(IWMWriter* This) {
    return This->lpVtbl->BeginWriting(This);
}
static inline HRESULT IWMWriter_EndWriting(IWMWriter* This) {
    return This->lpVtbl->EndWriting(This);
}
static inline HRESULT IWMWriter_AllocateSample(IWMWriter* This,DWORD dwSampleSize,INSSBuffer **ppSample) {
    return This->lpVtbl->AllocateSample(This,dwSampleSize,ppSample);
}
static inline HRESULT IWMWriter_WriteSample(IWMWriter* This,DWORD dwInputNum,QWORD cnsSampleTime,DWORD dwFlags,INSSBuffer *pSample) {
    return This->lpVtbl->WriteSample(This,dwInputNum,cnsSampleTime,dwFlags,pSample);
}
static inline HRESULT IWMWriter_Flush(IWMWriter* This) {
    return This->lpVtbl->Flush(This);
}
#endif
#endif

#endif


#endif  /* __IWMWriter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMWriterAdvanced interface
 */
#ifndef __IWMWriterAdvanced_INTERFACE_DEFINED__
#define __IWMWriterAdvanced_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMWriterAdvanced, 0x96406be3, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406be3-2b2b-11d3-b36b-00c04f6108ff")
IWMWriterAdvanced : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetSinkCount(
        DWORD *pcSinks) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSink(
        DWORD dwSinkNum,
        IWMWriterSink **ppSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddSink(
        IWMWriterSink *pSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveSink(
        IWMWriterSink *pSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteStreamSample(
        WORD wStreamNum,
        QWORD cnsSampleTime,
        DWORD msSampleSendTime,
        QWORD cnsSampleDuration,
        DWORD dwFlags,
        INSSBuffer *pSample) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLiveSource(
        WINBOOL fIsLiveSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsRealTime(
        WINBOOL *pfRealTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWriterTime(
        QWORD *pCurrentTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatistics(
        WORD wStreamNum,
        WM_WRITER_STATISTICS *pStats) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSyncTolerance(
        DWORD msWindow) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSyncTolerance(
        DWORD *pmsWindow) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMWriterAdvanced, 0x96406be3, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMWriterAdvancedVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMWriterAdvanced *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMWriterAdvanced *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMWriterAdvanced *This);

    /*** IWMWriterAdvanced methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSinkCount)(
        IWMWriterAdvanced *This,
        DWORD *pcSinks);

    HRESULT (STDMETHODCALLTYPE *GetSink)(
        IWMWriterAdvanced *This,
        DWORD dwSinkNum,
        IWMWriterSink **ppSink);

    HRESULT (STDMETHODCALLTYPE *AddSink)(
        IWMWriterAdvanced *This,
        IWMWriterSink *pSink);

    HRESULT (STDMETHODCALLTYPE *RemoveSink)(
        IWMWriterAdvanced *This,
        IWMWriterSink *pSink);

    HRESULT (STDMETHODCALLTYPE *WriteStreamSample)(
        IWMWriterAdvanced *This,
        WORD wStreamNum,
        QWORD cnsSampleTime,
        DWORD msSampleSendTime,
        QWORD cnsSampleDuration,
        DWORD dwFlags,
        INSSBuffer *pSample);

    HRESULT (STDMETHODCALLTYPE *SetLiveSource)(
        IWMWriterAdvanced *This,
        WINBOOL fIsLiveSource);

    HRESULT (STDMETHODCALLTYPE *IsRealTime)(
        IWMWriterAdvanced *This,
        WINBOOL *pfRealTime);

    HRESULT (STDMETHODCALLTYPE *GetWriterTime)(
        IWMWriterAdvanced *This,
        QWORD *pCurrentTime);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IWMWriterAdvanced *This,
        WORD wStreamNum,
        WM_WRITER_STATISTICS *pStats);

    HRESULT (STDMETHODCALLTYPE *SetSyncTolerance)(
        IWMWriterAdvanced *This,
        DWORD msWindow);

    HRESULT (STDMETHODCALLTYPE *GetSyncTolerance)(
        IWMWriterAdvanced *This,
        DWORD *pmsWindow);

    END_INTERFACE
} IWMWriterAdvancedVtbl;

interface IWMWriterAdvanced {
    CONST_VTBL IWMWriterAdvancedVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMWriterAdvanced_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMWriterAdvanced_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMWriterAdvanced_Release(This) (This)->lpVtbl->Release(This)
/*** IWMWriterAdvanced methods ***/
#define IWMWriterAdvanced_GetSinkCount(This,pcSinks) (This)->lpVtbl->GetSinkCount(This,pcSinks)
#define IWMWriterAdvanced_GetSink(This,dwSinkNum,ppSink) (This)->lpVtbl->GetSink(This,dwSinkNum,ppSink)
#define IWMWriterAdvanced_AddSink(This,pSink) (This)->lpVtbl->AddSink(This,pSink)
#define IWMWriterAdvanced_RemoveSink(This,pSink) (This)->lpVtbl->RemoveSink(This,pSink)
#define IWMWriterAdvanced_WriteStreamSample(This,wStreamNum,cnsSampleTime,msSampleSendTime,cnsSampleDuration,dwFlags,pSample) (This)->lpVtbl->WriteStreamSample(This,wStreamNum,cnsSampleTime,msSampleSendTime,cnsSampleDuration,dwFlags,pSample)
#define IWMWriterAdvanced_SetLiveSource(This,fIsLiveSource) (This)->lpVtbl->SetLiveSource(This,fIsLiveSource)
#define IWMWriterAdvanced_IsRealTime(This,pfRealTime) (This)->lpVtbl->IsRealTime(This,pfRealTime)
#define IWMWriterAdvanced_GetWriterTime(This,pCurrentTime) (This)->lpVtbl->GetWriterTime(This,pCurrentTime)
#define IWMWriterAdvanced_GetStatistics(This,wStreamNum,pStats) (This)->lpVtbl->GetStatistics(This,wStreamNum,pStats)
#define IWMWriterAdvanced_SetSyncTolerance(This,msWindow) (This)->lpVtbl->SetSyncTolerance(This,msWindow)
#define IWMWriterAdvanced_GetSyncTolerance(This,pmsWindow) (This)->lpVtbl->GetSyncTolerance(This,pmsWindow)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMWriterAdvanced_QueryInterface(IWMWriterAdvanced* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMWriterAdvanced_AddRef(IWMWriterAdvanced* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMWriterAdvanced_Release(IWMWriterAdvanced* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMWriterAdvanced methods ***/
static inline HRESULT IWMWriterAdvanced_GetSinkCount(IWMWriterAdvanced* This,DWORD *pcSinks) {
    return This->lpVtbl->GetSinkCount(This,pcSinks);
}
static inline HRESULT IWMWriterAdvanced_GetSink(IWMWriterAdvanced* This,DWORD dwSinkNum,IWMWriterSink **ppSink) {
    return This->lpVtbl->GetSink(This,dwSinkNum,ppSink);
}
static inline HRESULT IWMWriterAdvanced_AddSink(IWMWriterAdvanced* This,IWMWriterSink *pSink) {
    return This->lpVtbl->AddSink(This,pSink);
}
static inline HRESULT IWMWriterAdvanced_RemoveSink(IWMWriterAdvanced* This,IWMWriterSink *pSink) {
    return This->lpVtbl->RemoveSink(This,pSink);
}
static inline HRESULT IWMWriterAdvanced_WriteStreamSample(IWMWriterAdvanced* This,WORD wStreamNum,QWORD cnsSampleTime,DWORD msSampleSendTime,QWORD cnsSampleDuration,DWORD dwFlags,INSSBuffer *pSample) {
    return This->lpVtbl->WriteStreamSample(This,wStreamNum,cnsSampleTime,msSampleSendTime,cnsSampleDuration,dwFlags,pSample);
}
static inline HRESULT IWMWriterAdvanced_SetLiveSource(IWMWriterAdvanced* This,WINBOOL fIsLiveSource) {
    return This->lpVtbl->SetLiveSource(This,fIsLiveSource);
}
static inline HRESULT IWMWriterAdvanced_IsRealTime(IWMWriterAdvanced* This,WINBOOL *pfRealTime) {
    return This->lpVtbl->IsRealTime(This,pfRealTime);
}
static inline HRESULT IWMWriterAdvanced_GetWriterTime(IWMWriterAdvanced* This,QWORD *pCurrentTime) {
    return This->lpVtbl->GetWriterTime(This,pCurrentTime);
}
static inline HRESULT IWMWriterAdvanced_GetStatistics(IWMWriterAdvanced* This,WORD wStreamNum,WM_WRITER_STATISTICS *pStats) {
    return This->lpVtbl->GetStatistics(This,wStreamNum,pStats);
}
static inline HRESULT IWMWriterAdvanced_SetSyncTolerance(IWMWriterAdvanced* This,DWORD msWindow) {
    return This->lpVtbl->SetSyncTolerance(This,msWindow);
}
static inline HRESULT IWMWriterAdvanced_GetSyncTolerance(IWMWriterAdvanced* This,DWORD *pmsWindow) {
    return This->lpVtbl->GetSyncTolerance(This,pmsWindow);
}
#endif
#endif

#endif


#endif  /* __IWMWriterAdvanced_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMWriterAdvanced2 interface
 */
#ifndef __IWMWriterAdvanced2_INTERFACE_DEFINED__
#define __IWMWriterAdvanced2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMWriterAdvanced2, 0x962dc1ec, 0xc046, 0x4db8, 0x9c,0xc7, 0x26,0xce,0xae,0x50,0x08,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("962dc1ec-c046-4db8-9cc7-26ceae500817")
IWMWriterAdvanced2 : public IWMWriterAdvanced
{
    virtual HRESULT STDMETHODCALLTYPE GetInputSetting(
        DWORD dwInputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInputSetting(
        DWORD dwInputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMWriterAdvanced2, 0x962dc1ec, 0xc046, 0x4db8, 0x9c,0xc7, 0x26,0xce,0xae,0x50,0x08,0x17)
#endif
#else
typedef struct IWMWriterAdvanced2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMWriterAdvanced2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMWriterAdvanced2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMWriterAdvanced2 *This);

    /*** IWMWriterAdvanced methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSinkCount)(
        IWMWriterAdvanced2 *This,
        DWORD *pcSinks);

    HRESULT (STDMETHODCALLTYPE *GetSink)(
        IWMWriterAdvanced2 *This,
        DWORD dwSinkNum,
        IWMWriterSink **ppSink);

    HRESULT (STDMETHODCALLTYPE *AddSink)(
        IWMWriterAdvanced2 *This,
        IWMWriterSink *pSink);

    HRESULT (STDMETHODCALLTYPE *RemoveSink)(
        IWMWriterAdvanced2 *This,
        IWMWriterSink *pSink);

    HRESULT (STDMETHODCALLTYPE *WriteStreamSample)(
        IWMWriterAdvanced2 *This,
        WORD wStreamNum,
        QWORD cnsSampleTime,
        DWORD msSampleSendTime,
        QWORD cnsSampleDuration,
        DWORD dwFlags,
        INSSBuffer *pSample);

    HRESULT (STDMETHODCALLTYPE *SetLiveSource)(
        IWMWriterAdvanced2 *This,
        WINBOOL fIsLiveSource);

    HRESULT (STDMETHODCALLTYPE *IsRealTime)(
        IWMWriterAdvanced2 *This,
        WINBOOL *pfRealTime);

    HRESULT (STDMETHODCALLTYPE *GetWriterTime)(
        IWMWriterAdvanced2 *This,
        QWORD *pCurrentTime);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IWMWriterAdvanced2 *This,
        WORD wStreamNum,
        WM_WRITER_STATISTICS *pStats);

    HRESULT (STDMETHODCALLTYPE *SetSyncTolerance)(
        IWMWriterAdvanced2 *This,
        DWORD msWindow);

    HRESULT (STDMETHODCALLTYPE *GetSyncTolerance)(
        IWMWriterAdvanced2 *This,
        DWORD *pmsWindow);

    /*** IWMWriterAdvanced2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetInputSetting)(
        IWMWriterAdvanced2 *This,
        DWORD dwInputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength);

    HRESULT (STDMETHODCALLTYPE *SetInputSetting)(
        IWMWriterAdvanced2 *This,
        DWORD dwInputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength);

    END_INTERFACE
} IWMWriterAdvanced2Vtbl;

interface IWMWriterAdvanced2 {
    CONST_VTBL IWMWriterAdvanced2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMWriterAdvanced2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMWriterAdvanced2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMWriterAdvanced2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMWriterAdvanced methods ***/
#define IWMWriterAdvanced2_GetSinkCount(This,pcSinks) (This)->lpVtbl->GetSinkCount(This,pcSinks)
#define IWMWriterAdvanced2_GetSink(This,dwSinkNum,ppSink) (This)->lpVtbl->GetSink(This,dwSinkNum,ppSink)
#define IWMWriterAdvanced2_AddSink(This,pSink) (This)->lpVtbl->AddSink(This,pSink)
#define IWMWriterAdvanced2_RemoveSink(This,pSink) (This)->lpVtbl->RemoveSink(This,pSink)
#define IWMWriterAdvanced2_WriteStreamSample(This,wStreamNum,cnsSampleTime,msSampleSendTime,cnsSampleDuration,dwFlags,pSample) (This)->lpVtbl->WriteStreamSample(This,wStreamNum,cnsSampleTime,msSampleSendTime,cnsSampleDuration,dwFlags,pSample)
#define IWMWriterAdvanced2_SetLiveSource(This,fIsLiveSource) (This)->lpVtbl->SetLiveSource(This,fIsLiveSource)
#define IWMWriterAdvanced2_IsRealTime(This,pfRealTime) (This)->lpVtbl->IsRealTime(This,pfRealTime)
#define IWMWriterAdvanced2_GetWriterTime(This,pCurrentTime) (This)->lpVtbl->GetWriterTime(This,pCurrentTime)
#define IWMWriterAdvanced2_GetStatistics(This,wStreamNum,pStats) (This)->lpVtbl->GetStatistics(This,wStreamNum,pStats)
#define IWMWriterAdvanced2_SetSyncTolerance(This,msWindow) (This)->lpVtbl->SetSyncTolerance(This,msWindow)
#define IWMWriterAdvanced2_GetSyncTolerance(This,pmsWindow) (This)->lpVtbl->GetSyncTolerance(This,pmsWindow)
/*** IWMWriterAdvanced2 methods ***/
#define IWMWriterAdvanced2_GetInputSetting(This,dwInputNum,pszName,pType,pValue,pcbLength) (This)->lpVtbl->GetInputSetting(This,dwInputNum,pszName,pType,pValue,pcbLength)
#define IWMWriterAdvanced2_SetInputSetting(This,dwInputNum,pszName,Type,pValue,cbLength) (This)->lpVtbl->SetInputSetting(This,dwInputNum,pszName,Type,pValue,cbLength)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMWriterAdvanced2_QueryInterface(IWMWriterAdvanced2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMWriterAdvanced2_AddRef(IWMWriterAdvanced2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMWriterAdvanced2_Release(IWMWriterAdvanced2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMWriterAdvanced methods ***/
static inline HRESULT IWMWriterAdvanced2_GetSinkCount(IWMWriterAdvanced2* This,DWORD *pcSinks) {
    return This->lpVtbl->GetSinkCount(This,pcSinks);
}
static inline HRESULT IWMWriterAdvanced2_GetSink(IWMWriterAdvanced2* This,DWORD dwSinkNum,IWMWriterSink **ppSink) {
    return This->lpVtbl->GetSink(This,dwSinkNum,ppSink);
}
static inline HRESULT IWMWriterAdvanced2_AddSink(IWMWriterAdvanced2* This,IWMWriterSink *pSink) {
    return This->lpVtbl->AddSink(This,pSink);
}
static inline HRESULT IWMWriterAdvanced2_RemoveSink(IWMWriterAdvanced2* This,IWMWriterSink *pSink) {
    return This->lpVtbl->RemoveSink(This,pSink);
}
static inline HRESULT IWMWriterAdvanced2_WriteStreamSample(IWMWriterAdvanced2* This,WORD wStreamNum,QWORD cnsSampleTime,DWORD msSampleSendTime,QWORD cnsSampleDuration,DWORD dwFlags,INSSBuffer *pSample) {
    return This->lpVtbl->WriteStreamSample(This,wStreamNum,cnsSampleTime,msSampleSendTime,cnsSampleDuration,dwFlags,pSample);
}
static inline HRESULT IWMWriterAdvanced2_SetLiveSource(IWMWriterAdvanced2* This,WINBOOL fIsLiveSource) {
    return This->lpVtbl->SetLiveSource(This,fIsLiveSource);
}
static inline HRESULT IWMWriterAdvanced2_IsRealTime(IWMWriterAdvanced2* This,WINBOOL *pfRealTime) {
    return This->lpVtbl->IsRealTime(This,pfRealTime);
}
static inline HRESULT IWMWriterAdvanced2_GetWriterTime(IWMWriterAdvanced2* This,QWORD *pCurrentTime) {
    return This->lpVtbl->GetWriterTime(This,pCurrentTime);
}
static inline HRESULT IWMWriterAdvanced2_GetStatistics(IWMWriterAdvanced2* This,WORD wStreamNum,WM_WRITER_STATISTICS *pStats) {
    return This->lpVtbl->GetStatistics(This,wStreamNum,pStats);
}
static inline HRESULT IWMWriterAdvanced2_SetSyncTolerance(IWMWriterAdvanced2* This,DWORD msWindow) {
    return This->lpVtbl->SetSyncTolerance(This,msWindow);
}
static inline HRESULT IWMWriterAdvanced2_GetSyncTolerance(IWMWriterAdvanced2* This,DWORD *pmsWindow) {
    return This->lpVtbl->GetSyncTolerance(This,pmsWindow);
}
/*** IWMWriterAdvanced2 methods ***/
static inline HRESULT IWMWriterAdvanced2_GetInputSetting(IWMWriterAdvanced2* This,DWORD dwInputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE *pType,BYTE *pValue,WORD *pcbLength) {
    return This->lpVtbl->GetInputSetting(This,dwInputNum,pszName,pType,pValue,pcbLength);
}
static inline HRESULT IWMWriterAdvanced2_SetInputSetting(IWMWriterAdvanced2* This,DWORD dwInputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE Type,const BYTE *pValue,WORD cbLength) {
    return This->lpVtbl->SetInputSetting(This,dwInputNum,pszName,Type,pValue,cbLength);
}
#endif
#endif

#endif


#endif  /* __IWMWriterAdvanced2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMWriterAdvanced3 interface
 */
#ifndef __IWMWriterAdvanced3_INTERFACE_DEFINED__
#define __IWMWriterAdvanced3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMWriterAdvanced3, 0x2cd6492d, 0x7c37, 0x4e76, 0x9d,0x3b, 0x59,0x26,0x11,0x83,0xa2,0x2e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2cd6492d-7c37-4e76-9d3b-59261183a22e")
IWMWriterAdvanced3 : public IWMWriterAdvanced2
{
    virtual HRESULT STDMETHODCALLTYPE GetStatisticsEx(
        WORD wStreamNum,
        WM_WRITER_STATISTICS_EX *pStats) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetNonBlocking(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMWriterAdvanced3, 0x2cd6492d, 0x7c37, 0x4e76, 0x9d,0x3b, 0x59,0x26,0x11,0x83,0xa2,0x2e)
#endif
#else
typedef struct IWMWriterAdvanced3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMWriterAdvanced3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMWriterAdvanced3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMWriterAdvanced3 *This);

    /*** IWMWriterAdvanced methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSinkCount)(
        IWMWriterAdvanced3 *This,
        DWORD *pcSinks);

    HRESULT (STDMETHODCALLTYPE *GetSink)(
        IWMWriterAdvanced3 *This,
        DWORD dwSinkNum,
        IWMWriterSink **ppSink);

    HRESULT (STDMETHODCALLTYPE *AddSink)(
        IWMWriterAdvanced3 *This,
        IWMWriterSink *pSink);

    HRESULT (STDMETHODCALLTYPE *RemoveSink)(
        IWMWriterAdvanced3 *This,
        IWMWriterSink *pSink);

    HRESULT (STDMETHODCALLTYPE *WriteStreamSample)(
        IWMWriterAdvanced3 *This,
        WORD wStreamNum,
        QWORD cnsSampleTime,
        DWORD msSampleSendTime,
        QWORD cnsSampleDuration,
        DWORD dwFlags,
        INSSBuffer *pSample);

    HRESULT (STDMETHODCALLTYPE *SetLiveSource)(
        IWMWriterAdvanced3 *This,
        WINBOOL fIsLiveSource);

    HRESULT (STDMETHODCALLTYPE *IsRealTime)(
        IWMWriterAdvanced3 *This,
        WINBOOL *pfRealTime);

    HRESULT (STDMETHODCALLTYPE *GetWriterTime)(
        IWMWriterAdvanced3 *This,
        QWORD *pCurrentTime);

    HRESULT (STDMETHODCALLTYPE *GetStatistics)(
        IWMWriterAdvanced3 *This,
        WORD wStreamNum,
        WM_WRITER_STATISTICS *pStats);

    HRESULT (STDMETHODCALLTYPE *SetSyncTolerance)(
        IWMWriterAdvanced3 *This,
        DWORD msWindow);

    HRESULT (STDMETHODCALLTYPE *GetSyncTolerance)(
        IWMWriterAdvanced3 *This,
        DWORD *pmsWindow);

    /*** IWMWriterAdvanced2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetInputSetting)(
        IWMWriterAdvanced3 *This,
        DWORD dwInputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE *pType,
        BYTE *pValue,
        WORD *pcbLength);

    HRESULT (STDMETHODCALLTYPE *SetInputSetting)(
        IWMWriterAdvanced3 *This,
        DWORD dwInputNum,
        LPCWSTR pszName,
        WMT_ATTR_DATATYPE Type,
        const BYTE *pValue,
        WORD cbLength);

    /*** IWMWriterAdvanced3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStatisticsEx)(
        IWMWriterAdvanced3 *This,
        WORD wStreamNum,
        WM_WRITER_STATISTICS_EX *pStats);

    HRESULT (STDMETHODCALLTYPE *SetNonBlocking)(
        IWMWriterAdvanced3 *This);

    END_INTERFACE
} IWMWriterAdvanced3Vtbl;

interface IWMWriterAdvanced3 {
    CONST_VTBL IWMWriterAdvanced3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMWriterAdvanced3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMWriterAdvanced3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMWriterAdvanced3_Release(This) (This)->lpVtbl->Release(This)
/*** IWMWriterAdvanced methods ***/
#define IWMWriterAdvanced3_GetSinkCount(This,pcSinks) (This)->lpVtbl->GetSinkCount(This,pcSinks)
#define IWMWriterAdvanced3_GetSink(This,dwSinkNum,ppSink) (This)->lpVtbl->GetSink(This,dwSinkNum,ppSink)
#define IWMWriterAdvanced3_AddSink(This,pSink) (This)->lpVtbl->AddSink(This,pSink)
#define IWMWriterAdvanced3_RemoveSink(This,pSink) (This)->lpVtbl->RemoveSink(This,pSink)
#define IWMWriterAdvanced3_WriteStreamSample(This,wStreamNum,cnsSampleTime,msSampleSendTime,cnsSampleDuration,dwFlags,pSample) (This)->lpVtbl->WriteStreamSample(This,wStreamNum,cnsSampleTime,msSampleSendTime,cnsSampleDuration,dwFlags,pSample)
#define IWMWriterAdvanced3_SetLiveSource(This,fIsLiveSource) (This)->lpVtbl->SetLiveSource(This,fIsLiveSource)
#define IWMWriterAdvanced3_IsRealTime(This,pfRealTime) (This)->lpVtbl->IsRealTime(This,pfRealTime)
#define IWMWriterAdvanced3_GetWriterTime(This,pCurrentTime) (This)->lpVtbl->GetWriterTime(This,pCurrentTime)
#define IWMWriterAdvanced3_GetStatistics(This,wStreamNum,pStats) (This)->lpVtbl->GetStatistics(This,wStreamNum,pStats)
#define IWMWriterAdvanced3_SetSyncTolerance(This,msWindow) (This)->lpVtbl->SetSyncTolerance(This,msWindow)
#define IWMWriterAdvanced3_GetSyncTolerance(This,pmsWindow) (This)->lpVtbl->GetSyncTolerance(This,pmsWindow)
/*** IWMWriterAdvanced2 methods ***/
#define IWMWriterAdvanced3_GetInputSetting(This,dwInputNum,pszName,pType,pValue,pcbLength) (This)->lpVtbl->GetInputSetting(This,dwInputNum,pszName,pType,pValue,pcbLength)
#define IWMWriterAdvanced3_SetInputSetting(This,dwInputNum,pszName,Type,pValue,cbLength) (This)->lpVtbl->SetInputSetting(This,dwInputNum,pszName,Type,pValue,cbLength)
/*** IWMWriterAdvanced3 methods ***/
#define IWMWriterAdvanced3_GetStatisticsEx(This,wStreamNum,pStats) (This)->lpVtbl->GetStatisticsEx(This,wStreamNum,pStats)
#define IWMWriterAdvanced3_SetNonBlocking(This) (This)->lpVtbl->SetNonBlocking(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMWriterAdvanced3_QueryInterface(IWMWriterAdvanced3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMWriterAdvanced3_AddRef(IWMWriterAdvanced3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMWriterAdvanced3_Release(IWMWriterAdvanced3* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMWriterAdvanced methods ***/
static inline HRESULT IWMWriterAdvanced3_GetSinkCount(IWMWriterAdvanced3* This,DWORD *pcSinks) {
    return This->lpVtbl->GetSinkCount(This,pcSinks);
}
static inline HRESULT IWMWriterAdvanced3_GetSink(IWMWriterAdvanced3* This,DWORD dwSinkNum,IWMWriterSink **ppSink) {
    return This->lpVtbl->GetSink(This,dwSinkNum,ppSink);
}
static inline HRESULT IWMWriterAdvanced3_AddSink(IWMWriterAdvanced3* This,IWMWriterSink *pSink) {
    return This->lpVtbl->AddSink(This,pSink);
}
static inline HRESULT IWMWriterAdvanced3_RemoveSink(IWMWriterAdvanced3* This,IWMWriterSink *pSink) {
    return This->lpVtbl->RemoveSink(This,pSink);
}
static inline HRESULT IWMWriterAdvanced3_WriteStreamSample(IWMWriterAdvanced3* This,WORD wStreamNum,QWORD cnsSampleTime,DWORD msSampleSendTime,QWORD cnsSampleDuration,DWORD dwFlags,INSSBuffer *pSample) {
    return This->lpVtbl->WriteStreamSample(This,wStreamNum,cnsSampleTime,msSampleSendTime,cnsSampleDuration,dwFlags,pSample);
}
static inline HRESULT IWMWriterAdvanced3_SetLiveSource(IWMWriterAdvanced3* This,WINBOOL fIsLiveSource) {
    return This->lpVtbl->SetLiveSource(This,fIsLiveSource);
}
static inline HRESULT IWMWriterAdvanced3_IsRealTime(IWMWriterAdvanced3* This,WINBOOL *pfRealTime) {
    return This->lpVtbl->IsRealTime(This,pfRealTime);
}
static inline HRESULT IWMWriterAdvanced3_GetWriterTime(IWMWriterAdvanced3* This,QWORD *pCurrentTime) {
    return This->lpVtbl->GetWriterTime(This,pCurrentTime);
}
static inline HRESULT IWMWriterAdvanced3_GetStatistics(IWMWriterAdvanced3* This,WORD wStreamNum,WM_WRITER_STATISTICS *pStats) {
    return This->lpVtbl->GetStatistics(This,wStreamNum,pStats);
}
static inline HRESULT IWMWriterAdvanced3_SetSyncTolerance(IWMWriterAdvanced3* This,DWORD msWindow) {
    return This->lpVtbl->SetSyncTolerance(This,msWindow);
}
static inline HRESULT IWMWriterAdvanced3_GetSyncTolerance(IWMWriterAdvanced3* This,DWORD *pmsWindow) {
    return This->lpVtbl->GetSyncTolerance(This,pmsWindow);
}
/*** IWMWriterAdvanced2 methods ***/
static inline HRESULT IWMWriterAdvanced3_GetInputSetting(IWMWriterAdvanced3* This,DWORD dwInputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE *pType,BYTE *pValue,WORD *pcbLength) {
    return This->lpVtbl->GetInputSetting(This,dwInputNum,pszName,pType,pValue,pcbLength);
}
static inline HRESULT IWMWriterAdvanced3_SetInputSetting(IWMWriterAdvanced3* This,DWORD dwInputNum,LPCWSTR pszName,WMT_ATTR_DATATYPE Type,const BYTE *pValue,WORD cbLength) {
    return This->lpVtbl->SetInputSetting(This,dwInputNum,pszName,Type,pValue,cbLength);
}
/*** IWMWriterAdvanced3 methods ***/
static inline HRESULT IWMWriterAdvanced3_GetStatisticsEx(IWMWriterAdvanced3* This,WORD wStreamNum,WM_WRITER_STATISTICS_EX *pStats) {
    return This->lpVtbl->GetStatisticsEx(This,wStreamNum,pStats);
}
static inline HRESULT IWMWriterAdvanced3_SetNonBlocking(IWMWriterAdvanced3* This) {
    return This->lpVtbl->SetNonBlocking(This);
}
#endif
#endif

#endif


#endif  /* __IWMWriterAdvanced3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMHeaderInfo interface
 */
#ifndef __IWMHeaderInfo_INTERFACE_DEFINED__
#define __IWMHeaderInfo_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMHeaderInfo, 0x96406bda, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bda-2b2b-11d3-b36b-00c04f6108ff")
IWMHeaderInfo : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetAttributeCount(
        WORD stream_num,
        WORD *attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAttributeByIndex(
        WORD index,
        WORD *stream_num,
        WCHAR *name,
        WORD *name_len,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAttributeByName(
        WORD *stream_num,
        LPCWSTR name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAttribute(
        WORD stream_num,
        LPCWSTR name,
        WMT_ATTR_DATATYPE type,
        const BYTE *value,
        WORD length) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMarkerCount(
        WORD *markers) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMarker(
        WORD index,
        WCHAR *marker_name,
        WORD *marker_len,
        QWORD *marker_time) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddMarker(
        LPCWSTR_WMSDK_TYPE_SAFE marker_name,
        QWORD marker_time) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveMarker(
        WORD index) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptCount(
        WORD *scripts) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScript(
        WORD index,
        WCHAR *type,
        WORD *type_len,
        WCHAR *command,
        WORD *command_len,
        QWORD *script_time) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddScript(
        LPCWSTR_WMSDK_TYPE_SAFE type,
        LPCWSTR_WMSDK_TYPE_SAFE command,
        QWORD script_time) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveScript(
        WORD index) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMHeaderInfo, 0x96406bda, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMHeaderInfoVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMHeaderInfo *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMHeaderInfo *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMHeaderInfo *This);

    /*** IWMHeaderInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAttributeCount)(
        IWMHeaderInfo *This,
        WORD stream_num,
        WORD *attributes);

    HRESULT (STDMETHODCALLTYPE *GetAttributeByIndex)(
        IWMHeaderInfo *This,
        WORD index,
        WORD *stream_num,
        WCHAR *name,
        WORD *name_len,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length);

    HRESULT (STDMETHODCALLTYPE *GetAttributeByName)(
        IWMHeaderInfo *This,
        WORD *stream_num,
        LPCWSTR name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length);

    HRESULT (STDMETHODCALLTYPE *SetAttribute)(
        IWMHeaderInfo *This,
        WORD stream_num,
        LPCWSTR name,
        WMT_ATTR_DATATYPE type,
        const BYTE *value,
        WORD length);

    HRESULT (STDMETHODCALLTYPE *GetMarkerCount)(
        IWMHeaderInfo *This,
        WORD *markers);

    HRESULT (STDMETHODCALLTYPE *GetMarker)(
        IWMHeaderInfo *This,
        WORD index,
        WCHAR *marker_name,
        WORD *marker_len,
        QWORD *marker_time);

    HRESULT (STDMETHODCALLTYPE *AddMarker)(
        IWMHeaderInfo *This,
        LPCWSTR_WMSDK_TYPE_SAFE marker_name,
        QWORD marker_time);

    HRESULT (STDMETHODCALLTYPE *RemoveMarker)(
        IWMHeaderInfo *This,
        WORD index);

    HRESULT (STDMETHODCALLTYPE *GetScriptCount)(
        IWMHeaderInfo *This,
        WORD *scripts);

    HRESULT (STDMETHODCALLTYPE *GetScript)(
        IWMHeaderInfo *This,
        WORD index,
        WCHAR *type,
        WORD *type_len,
        WCHAR *command,
        WORD *command_len,
        QWORD *script_time);

    HRESULT (STDMETHODCALLTYPE *AddScript)(
        IWMHeaderInfo *This,
        LPCWSTR_WMSDK_TYPE_SAFE type,
        LPCWSTR_WMSDK_TYPE_SAFE command,
        QWORD script_time);

    HRESULT (STDMETHODCALLTYPE *RemoveScript)(
        IWMHeaderInfo *This,
        WORD index);

    END_INTERFACE
} IWMHeaderInfoVtbl;

interface IWMHeaderInfo {
    CONST_VTBL IWMHeaderInfoVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMHeaderInfo_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMHeaderInfo_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMHeaderInfo_Release(This) (This)->lpVtbl->Release(This)
/*** IWMHeaderInfo methods ***/
#define IWMHeaderInfo_GetAttributeCount(This,stream_num,attributes) (This)->lpVtbl->GetAttributeCount(This,stream_num,attributes)
#define IWMHeaderInfo_GetAttributeByIndex(This,index,stream_num,name,name_len,type,value,length) (This)->lpVtbl->GetAttributeByIndex(This,index,stream_num,name,name_len,type,value,length)
#define IWMHeaderInfo_GetAttributeByName(This,stream_num,name,type,value,length) (This)->lpVtbl->GetAttributeByName(This,stream_num,name,type,value,length)
#define IWMHeaderInfo_SetAttribute(This,stream_num,name,type,value,length) (This)->lpVtbl->SetAttribute(This,stream_num,name,type,value,length)
#define IWMHeaderInfo_GetMarkerCount(This,markers) (This)->lpVtbl->GetMarkerCount(This,markers)
#define IWMHeaderInfo_GetMarker(This,index,marker_name,marker_len,marker_time) (This)->lpVtbl->GetMarker(This,index,marker_name,marker_len,marker_time)
#define IWMHeaderInfo_AddMarker(This,marker_name,marker_time) (This)->lpVtbl->AddMarker(This,marker_name,marker_time)
#define IWMHeaderInfo_RemoveMarker(This,index) (This)->lpVtbl->RemoveMarker(This,index)
#define IWMHeaderInfo_GetScriptCount(This,scripts) (This)->lpVtbl->GetScriptCount(This,scripts)
#define IWMHeaderInfo_GetScript(This,index,type,type_len,command,command_len,script_time) (This)->lpVtbl->GetScript(This,index,type,type_len,command,command_len,script_time)
#define IWMHeaderInfo_AddScript(This,type,command,script_time) (This)->lpVtbl->AddScript(This,type,command,script_time)
#define IWMHeaderInfo_RemoveScript(This,index) (This)->lpVtbl->RemoveScript(This,index)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMHeaderInfo_QueryInterface(IWMHeaderInfo* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMHeaderInfo_AddRef(IWMHeaderInfo* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMHeaderInfo_Release(IWMHeaderInfo* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMHeaderInfo methods ***/
static inline HRESULT IWMHeaderInfo_GetAttributeCount(IWMHeaderInfo* This,WORD stream_num,WORD *attributes) {
    return This->lpVtbl->GetAttributeCount(This,stream_num,attributes);
}
static inline HRESULT IWMHeaderInfo_GetAttributeByIndex(IWMHeaderInfo* This,WORD index,WORD *stream_num,WCHAR *name,WORD *name_len,WMT_ATTR_DATATYPE *type,BYTE *value,WORD *length) {
    return This->lpVtbl->GetAttributeByIndex(This,index,stream_num,name,name_len,type,value,length);
}
static inline HRESULT IWMHeaderInfo_GetAttributeByName(IWMHeaderInfo* This,WORD *stream_num,LPCWSTR name,WMT_ATTR_DATATYPE *type,BYTE *value,WORD *length) {
    return This->lpVtbl->GetAttributeByName(This,stream_num,name,type,value,length);
}
static inline HRESULT IWMHeaderInfo_SetAttribute(IWMHeaderInfo* This,WORD stream_num,LPCWSTR name,WMT_ATTR_DATATYPE type,const BYTE *value,WORD length) {
    return This->lpVtbl->SetAttribute(This,stream_num,name,type,value,length);
}
static inline HRESULT IWMHeaderInfo_GetMarkerCount(IWMHeaderInfo* This,WORD *markers) {
    return This->lpVtbl->GetMarkerCount(This,markers);
}
static inline HRESULT IWMHeaderInfo_GetMarker(IWMHeaderInfo* This,WORD index,WCHAR *marker_name,WORD *marker_len,QWORD *marker_time) {
    return This->lpVtbl->GetMarker(This,index,marker_name,marker_len,marker_time);
}
static inline HRESULT IWMHeaderInfo_AddMarker(IWMHeaderInfo* This,LPCWSTR_WMSDK_TYPE_SAFE marker_name,QWORD marker_time) {
    return This->lpVtbl->AddMarker(This,marker_name,marker_time);
}
static inline HRESULT IWMHeaderInfo_RemoveMarker(IWMHeaderInfo* This,WORD index) {
    return This->lpVtbl->RemoveMarker(This,index);
}
static inline HRESULT IWMHeaderInfo_GetScriptCount(IWMHeaderInfo* This,WORD *scripts) {
    return This->lpVtbl->GetScriptCount(This,scripts);
}
static inline HRESULT IWMHeaderInfo_GetScript(IWMHeaderInfo* This,WORD index,WCHAR *type,WORD *type_len,WCHAR *command,WORD *command_len,QWORD *script_time) {
    return This->lpVtbl->GetScript(This,index,type,type_len,command,command_len,script_time);
}
static inline HRESULT IWMHeaderInfo_AddScript(IWMHeaderInfo* This,LPCWSTR_WMSDK_TYPE_SAFE type,LPCWSTR_WMSDK_TYPE_SAFE command,QWORD script_time) {
    return This->lpVtbl->AddScript(This,type,command,script_time);
}
static inline HRESULT IWMHeaderInfo_RemoveScript(IWMHeaderInfo* This,WORD index) {
    return This->lpVtbl->RemoveScript(This,index);
}
#endif
#endif

#endif


#endif  /* __IWMHeaderInfo_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMHeaderInfo2 interface
 */
#ifndef __IWMHeaderInfo2_INTERFACE_DEFINED__
#define __IWMHeaderInfo2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMHeaderInfo2, 0x15cf9781, 0x454e, 0x482e, 0xb3,0x93, 0x85,0xfa,0xe4,0x87,0xa8,0x10);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("15cf9781-454e-482e-b393-85fae487a810")
IWMHeaderInfo2 : public IWMHeaderInfo
{
    virtual HRESULT STDMETHODCALLTYPE GetCodecInfoCount(
        DWORD *codec_infos) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodecInfo(
        DWORD index,
        WORD *name_len,
        WCHAR *name,
        WORD *description_len,
        WCHAR *description,
        WMT_CODEC_INFO_TYPE *codec_type,
        WORD *codec_info_cnt,
        BYTE *codec_info) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMHeaderInfo2, 0x15cf9781, 0x454e, 0x482e, 0xb3,0x93, 0x85,0xfa,0xe4,0x87,0xa8,0x10)
#endif
#else
typedef struct IWMHeaderInfo2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMHeaderInfo2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMHeaderInfo2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMHeaderInfo2 *This);

    /*** IWMHeaderInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAttributeCount)(
        IWMHeaderInfo2 *This,
        WORD stream_num,
        WORD *attributes);

    HRESULT (STDMETHODCALLTYPE *GetAttributeByIndex)(
        IWMHeaderInfo2 *This,
        WORD index,
        WORD *stream_num,
        WCHAR *name,
        WORD *name_len,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length);

    HRESULT (STDMETHODCALLTYPE *GetAttributeByName)(
        IWMHeaderInfo2 *This,
        WORD *stream_num,
        LPCWSTR name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length);

    HRESULT (STDMETHODCALLTYPE *SetAttribute)(
        IWMHeaderInfo2 *This,
        WORD stream_num,
        LPCWSTR name,
        WMT_ATTR_DATATYPE type,
        const BYTE *value,
        WORD length);

    HRESULT (STDMETHODCALLTYPE *GetMarkerCount)(
        IWMHeaderInfo2 *This,
        WORD *markers);

    HRESULT (STDMETHODCALLTYPE *GetMarker)(
        IWMHeaderInfo2 *This,
        WORD index,
        WCHAR *marker_name,
        WORD *marker_len,
        QWORD *marker_time);

    HRESULT (STDMETHODCALLTYPE *AddMarker)(
        IWMHeaderInfo2 *This,
        LPCWSTR_WMSDK_TYPE_SAFE marker_name,
        QWORD marker_time);

    HRESULT (STDMETHODCALLTYPE *RemoveMarker)(
        IWMHeaderInfo2 *This,
        WORD index);

    HRESULT (STDMETHODCALLTYPE *GetScriptCount)(
        IWMHeaderInfo2 *This,
        WORD *scripts);

    HRESULT (STDMETHODCALLTYPE *GetScript)(
        IWMHeaderInfo2 *This,
        WORD index,
        WCHAR *type,
        WORD *type_len,
        WCHAR *command,
        WORD *command_len,
        QWORD *script_time);

    HRESULT (STDMETHODCALLTYPE *AddScript)(
        IWMHeaderInfo2 *This,
        LPCWSTR_WMSDK_TYPE_SAFE type,
        LPCWSTR_WMSDK_TYPE_SAFE command,
        QWORD script_time);

    HRESULT (STDMETHODCALLTYPE *RemoveScript)(
        IWMHeaderInfo2 *This,
        WORD index);

    /*** IWMHeaderInfo2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCodecInfoCount)(
        IWMHeaderInfo2 *This,
        DWORD *codec_infos);

    HRESULT (STDMETHODCALLTYPE *GetCodecInfo)(
        IWMHeaderInfo2 *This,
        DWORD index,
        WORD *name_len,
        WCHAR *name,
        WORD *description_len,
        WCHAR *description,
        WMT_CODEC_INFO_TYPE *codec_type,
        WORD *codec_info_cnt,
        BYTE *codec_info);

    END_INTERFACE
} IWMHeaderInfo2Vtbl;

interface IWMHeaderInfo2 {
    CONST_VTBL IWMHeaderInfo2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMHeaderInfo2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMHeaderInfo2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMHeaderInfo2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMHeaderInfo methods ***/
#define IWMHeaderInfo2_GetAttributeCount(This,stream_num,attributes) (This)->lpVtbl->GetAttributeCount(This,stream_num,attributes)
#define IWMHeaderInfo2_GetAttributeByIndex(This,index,stream_num,name,name_len,type,value,length) (This)->lpVtbl->GetAttributeByIndex(This,index,stream_num,name,name_len,type,value,length)
#define IWMHeaderInfo2_GetAttributeByName(This,stream_num,name,type,value,length) (This)->lpVtbl->GetAttributeByName(This,stream_num,name,type,value,length)
#define IWMHeaderInfo2_SetAttribute(This,stream_num,name,type,value,length) (This)->lpVtbl->SetAttribute(This,stream_num,name,type,value,length)
#define IWMHeaderInfo2_GetMarkerCount(This,markers) (This)->lpVtbl->GetMarkerCount(This,markers)
#define IWMHeaderInfo2_GetMarker(This,index,marker_name,marker_len,marker_time) (This)->lpVtbl->GetMarker(This,index,marker_name,marker_len,marker_time)
#define IWMHeaderInfo2_AddMarker(This,marker_name,marker_time) (This)->lpVtbl->AddMarker(This,marker_name,marker_time)
#define IWMHeaderInfo2_RemoveMarker(This,index) (This)->lpVtbl->RemoveMarker(This,index)
#define IWMHeaderInfo2_GetScriptCount(This,scripts) (This)->lpVtbl->GetScriptCount(This,scripts)
#define IWMHeaderInfo2_GetScript(This,index,type,type_len,command,command_len,script_time) (This)->lpVtbl->GetScript(This,index,type,type_len,command,command_len,script_time)
#define IWMHeaderInfo2_AddScript(This,type,command,script_time) (This)->lpVtbl->AddScript(This,type,command,script_time)
#define IWMHeaderInfo2_RemoveScript(This,index) (This)->lpVtbl->RemoveScript(This,index)
/*** IWMHeaderInfo2 methods ***/
#define IWMHeaderInfo2_GetCodecInfoCount(This,codec_infos) (This)->lpVtbl->GetCodecInfoCount(This,codec_infos)
#define IWMHeaderInfo2_GetCodecInfo(This,index,name_len,name,description_len,description,codec_type,codec_info_cnt,codec_info) (This)->lpVtbl->GetCodecInfo(This,index,name_len,name,description_len,description,codec_type,codec_info_cnt,codec_info)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMHeaderInfo2_QueryInterface(IWMHeaderInfo2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMHeaderInfo2_AddRef(IWMHeaderInfo2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMHeaderInfo2_Release(IWMHeaderInfo2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMHeaderInfo methods ***/
static inline HRESULT IWMHeaderInfo2_GetAttributeCount(IWMHeaderInfo2* This,WORD stream_num,WORD *attributes) {
    return This->lpVtbl->GetAttributeCount(This,stream_num,attributes);
}
static inline HRESULT IWMHeaderInfo2_GetAttributeByIndex(IWMHeaderInfo2* This,WORD index,WORD *stream_num,WCHAR *name,WORD *name_len,WMT_ATTR_DATATYPE *type,BYTE *value,WORD *length) {
    return This->lpVtbl->GetAttributeByIndex(This,index,stream_num,name,name_len,type,value,length);
}
static inline HRESULT IWMHeaderInfo2_GetAttributeByName(IWMHeaderInfo2* This,WORD *stream_num,LPCWSTR name,WMT_ATTR_DATATYPE *type,BYTE *value,WORD *length) {
    return This->lpVtbl->GetAttributeByName(This,stream_num,name,type,value,length);
}
static inline HRESULT IWMHeaderInfo2_SetAttribute(IWMHeaderInfo2* This,WORD stream_num,LPCWSTR name,WMT_ATTR_DATATYPE type,const BYTE *value,WORD length) {
    return This->lpVtbl->SetAttribute(This,stream_num,name,type,value,length);
}
static inline HRESULT IWMHeaderInfo2_GetMarkerCount(IWMHeaderInfo2* This,WORD *markers) {
    return This->lpVtbl->GetMarkerCount(This,markers);
}
static inline HRESULT IWMHeaderInfo2_GetMarker(IWMHeaderInfo2* This,WORD index,WCHAR *marker_name,WORD *marker_len,QWORD *marker_time) {
    return This->lpVtbl->GetMarker(This,index,marker_name,marker_len,marker_time);
}
static inline HRESULT IWMHeaderInfo2_AddMarker(IWMHeaderInfo2* This,LPCWSTR_WMSDK_TYPE_SAFE marker_name,QWORD marker_time) {
    return This->lpVtbl->AddMarker(This,marker_name,marker_time);
}
static inline HRESULT IWMHeaderInfo2_RemoveMarker(IWMHeaderInfo2* This,WORD index) {
    return This->lpVtbl->RemoveMarker(This,index);
}
static inline HRESULT IWMHeaderInfo2_GetScriptCount(IWMHeaderInfo2* This,WORD *scripts) {
    return This->lpVtbl->GetScriptCount(This,scripts);
}
static inline HRESULT IWMHeaderInfo2_GetScript(IWMHeaderInfo2* This,WORD index,WCHAR *type,WORD *type_len,WCHAR *command,WORD *command_len,QWORD *script_time) {
    return This->lpVtbl->GetScript(This,index,type,type_len,command,command_len,script_time);
}
static inline HRESULT IWMHeaderInfo2_AddScript(IWMHeaderInfo2* This,LPCWSTR_WMSDK_TYPE_SAFE type,LPCWSTR_WMSDK_TYPE_SAFE command,QWORD script_time) {
    return This->lpVtbl->AddScript(This,type,command,script_time);
}
static inline HRESULT IWMHeaderInfo2_RemoveScript(IWMHeaderInfo2* This,WORD index) {
    return This->lpVtbl->RemoveScript(This,index);
}
/*** IWMHeaderInfo2 methods ***/
static inline HRESULT IWMHeaderInfo2_GetCodecInfoCount(IWMHeaderInfo2* This,DWORD *codec_infos) {
    return This->lpVtbl->GetCodecInfoCount(This,codec_infos);
}
static inline HRESULT IWMHeaderInfo2_GetCodecInfo(IWMHeaderInfo2* This,DWORD index,WORD *name_len,WCHAR *name,WORD *description_len,WCHAR *description,WMT_CODEC_INFO_TYPE *codec_type,WORD *codec_info_cnt,BYTE *codec_info) {
    return This->lpVtbl->GetCodecInfo(This,index,name_len,name,description_len,description,codec_type,codec_info_cnt,codec_info);
}
#endif
#endif

#endif


#endif  /* __IWMHeaderInfo2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMHeaderInfo3 interface
 */
#ifndef __IWMHeaderInfo3_INTERFACE_DEFINED__
#define __IWMHeaderInfo3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMHeaderInfo3, 0x15cc68e3, 0x27cc, 0x4ecd, 0xb2,0x22, 0x3f,0x5d,0x02,0xd8,0x0b,0xd5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("15cc68e3-27cc-4ecd-b222-3f5d02d80bd5")
IWMHeaderInfo3 : public IWMHeaderInfo2
{
    virtual HRESULT STDMETHODCALLTYPE GetAttributeCountEx(
        WORD stream_num,
        WORD *attributes) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAttributeIndices(
        WORD stream_num,
        LPCWSTR name,
        WORD *lang_index,
        WORD *indices,
        WORD *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAttributeByIndexEx(
        WORD stream_num,
        WORD index,
        LPWSTR name,
        WORD *name_len,
        WMT_ATTR_DATATYPE *type,
        WORD *lang_index,
        BYTE *value,
        DWORD *data_len) = 0;

    virtual HRESULT STDMETHODCALLTYPE ModifyAttribute(
        WORD stream_num,
        WORD index,
        WMT_ATTR_DATATYPE type,
        WORD lang_index,
        const BYTE *value,
        DWORD length) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddAttribute(
        WORD stream_num,
        LPCWSTR name,
        WORD *index,
        WMT_ATTR_DATATYPE type,
        WORD lang_index,
        const BYTE *value,
        DWORD length) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteAttribute(
        WORD stream_num,
        WORD index) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddCodecInfo(
        LPCWSTR_WMSDK_TYPE_SAFE name,
        LPCWSTR_WMSDK_TYPE_SAFE description,
        WMT_CODEC_INFO_TYPE codec_type,
        WORD codec_info_cnt,
        BYTE *codec_info) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMHeaderInfo3, 0x15cc68e3, 0x27cc, 0x4ecd, 0xb2,0x22, 0x3f,0x5d,0x02,0xd8,0x0b,0xd5)
#endif
#else
typedef struct IWMHeaderInfo3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMHeaderInfo3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMHeaderInfo3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMHeaderInfo3 *This);

    /*** IWMHeaderInfo methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAttributeCount)(
        IWMHeaderInfo3 *This,
        WORD stream_num,
        WORD *attributes);

    HRESULT (STDMETHODCALLTYPE *GetAttributeByIndex)(
        IWMHeaderInfo3 *This,
        WORD index,
        WORD *stream_num,
        WCHAR *name,
        WORD *name_len,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length);

    HRESULT (STDMETHODCALLTYPE *GetAttributeByName)(
        IWMHeaderInfo3 *This,
        WORD *stream_num,
        LPCWSTR name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length);

    HRESULT (STDMETHODCALLTYPE *SetAttribute)(
        IWMHeaderInfo3 *This,
        WORD stream_num,
        LPCWSTR name,
        WMT_ATTR_DATATYPE type,
        const BYTE *value,
        WORD length);

    HRESULT (STDMETHODCALLTYPE *GetMarkerCount)(
        IWMHeaderInfo3 *This,
        WORD *markers);

    HRESULT (STDMETHODCALLTYPE *GetMarker)(
        IWMHeaderInfo3 *This,
        WORD index,
        WCHAR *marker_name,
        WORD *marker_len,
        QWORD *marker_time);

    HRESULT (STDMETHODCALLTYPE *AddMarker)(
        IWMHeaderInfo3 *This,
        LPCWSTR_WMSDK_TYPE_SAFE marker_name,
        QWORD marker_time);

    HRESULT (STDMETHODCALLTYPE *RemoveMarker)(
        IWMHeaderInfo3 *This,
        WORD index);

    HRESULT (STDMETHODCALLTYPE *GetScriptCount)(
        IWMHeaderInfo3 *This,
        WORD *scripts);

    HRESULT (STDMETHODCALLTYPE *GetScript)(
        IWMHeaderInfo3 *This,
        WORD index,
        WCHAR *type,
        WORD *type_len,
        WCHAR *command,
        WORD *command_len,
        QWORD *script_time);

    HRESULT (STDMETHODCALLTYPE *AddScript)(
        IWMHeaderInfo3 *This,
        LPCWSTR_WMSDK_TYPE_SAFE type,
        LPCWSTR_WMSDK_TYPE_SAFE command,
        QWORD script_time);

    HRESULT (STDMETHODCALLTYPE *RemoveScript)(
        IWMHeaderInfo3 *This,
        WORD index);

    /*** IWMHeaderInfo2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCodecInfoCount)(
        IWMHeaderInfo3 *This,
        DWORD *codec_infos);

    HRESULT (STDMETHODCALLTYPE *GetCodecInfo)(
        IWMHeaderInfo3 *This,
        DWORD index,
        WORD *name_len,
        WCHAR *name,
        WORD *description_len,
        WCHAR *description,
        WMT_CODEC_INFO_TYPE *codec_type,
        WORD *codec_info_cnt,
        BYTE *codec_info);

    /*** IWMHeaderInfo3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAttributeCountEx)(
        IWMHeaderInfo3 *This,
        WORD stream_num,
        WORD *attributes);

    HRESULT (STDMETHODCALLTYPE *GetAttributeIndices)(
        IWMHeaderInfo3 *This,
        WORD stream_num,
        LPCWSTR name,
        WORD *lang_index,
        WORD *indices,
        WORD *count);

    HRESULT (STDMETHODCALLTYPE *GetAttributeByIndexEx)(
        IWMHeaderInfo3 *This,
        WORD stream_num,
        WORD index,
        LPWSTR name,
        WORD *name_len,
        WMT_ATTR_DATATYPE *type,
        WORD *lang_index,
        BYTE *value,
        DWORD *data_len);

    HRESULT (STDMETHODCALLTYPE *ModifyAttribute)(
        IWMHeaderInfo3 *This,
        WORD stream_num,
        WORD index,
        WMT_ATTR_DATATYPE type,
        WORD lang_index,
        const BYTE *value,
        DWORD length);

    HRESULT (STDMETHODCALLTYPE *AddAttribute)(
        IWMHeaderInfo3 *This,
        WORD stream_num,
        LPCWSTR name,
        WORD *index,
        WMT_ATTR_DATATYPE type,
        WORD lang_index,
        const BYTE *value,
        DWORD length);

    HRESULT (STDMETHODCALLTYPE *DeleteAttribute)(
        IWMHeaderInfo3 *This,
        WORD stream_num,
        WORD index);

    HRESULT (STDMETHODCALLTYPE *AddCodecInfo)(
        IWMHeaderInfo3 *This,
        LPCWSTR_WMSDK_TYPE_SAFE name,
        LPCWSTR_WMSDK_TYPE_SAFE description,
        WMT_CODEC_INFO_TYPE codec_type,
        WORD codec_info_cnt,
        BYTE *codec_info);

    END_INTERFACE
} IWMHeaderInfo3Vtbl;

interface IWMHeaderInfo3 {
    CONST_VTBL IWMHeaderInfo3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMHeaderInfo3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMHeaderInfo3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMHeaderInfo3_Release(This) (This)->lpVtbl->Release(This)
/*** IWMHeaderInfo methods ***/
#define IWMHeaderInfo3_GetAttributeCount(This,stream_num,attributes) (This)->lpVtbl->GetAttributeCount(This,stream_num,attributes)
#define IWMHeaderInfo3_GetAttributeByIndex(This,index,stream_num,name,name_len,type,value,length) (This)->lpVtbl->GetAttributeByIndex(This,index,stream_num,name,name_len,type,value,length)
#define IWMHeaderInfo3_GetAttributeByName(This,stream_num,name,type,value,length) (This)->lpVtbl->GetAttributeByName(This,stream_num,name,type,value,length)
#define IWMHeaderInfo3_SetAttribute(This,stream_num,name,type,value,length) (This)->lpVtbl->SetAttribute(This,stream_num,name,type,value,length)
#define IWMHeaderInfo3_GetMarkerCount(This,markers) (This)->lpVtbl->GetMarkerCount(This,markers)
#define IWMHeaderInfo3_GetMarker(This,index,marker_name,marker_len,marker_time) (This)->lpVtbl->GetMarker(This,index,marker_name,marker_len,marker_time)
#define IWMHeaderInfo3_AddMarker(This,marker_name,marker_time) (This)->lpVtbl->AddMarker(This,marker_name,marker_time)
#define IWMHeaderInfo3_RemoveMarker(This,index) (This)->lpVtbl->RemoveMarker(This,index)
#define IWMHeaderInfo3_GetScriptCount(This,scripts) (This)->lpVtbl->GetScriptCount(This,scripts)
#define IWMHeaderInfo3_GetScript(This,index,type,type_len,command,command_len,script_time) (This)->lpVtbl->GetScript(This,index,type,type_len,command,command_len,script_time)
#define IWMHeaderInfo3_AddScript(This,type,command,script_time) (This)->lpVtbl->AddScript(This,type,command,script_time)
#define IWMHeaderInfo3_RemoveScript(This,index) (This)->lpVtbl->RemoveScript(This,index)
/*** IWMHeaderInfo2 methods ***/
#define IWMHeaderInfo3_GetCodecInfoCount(This,codec_infos) (This)->lpVtbl->GetCodecInfoCount(This,codec_infos)
#define IWMHeaderInfo3_GetCodecInfo(This,index,name_len,name,description_len,description,codec_type,codec_info_cnt,codec_info) (This)->lpVtbl->GetCodecInfo(This,index,name_len,name,description_len,description,codec_type,codec_info_cnt,codec_info)
/*** IWMHeaderInfo3 methods ***/
#define IWMHeaderInfo3_GetAttributeCountEx(This,stream_num,attributes) (This)->lpVtbl->GetAttributeCountEx(This,stream_num,attributes)
#define IWMHeaderInfo3_GetAttributeIndices(This,stream_num,name,lang_index,indices,count) (This)->lpVtbl->GetAttributeIndices(This,stream_num,name,lang_index,indices,count)
#define IWMHeaderInfo3_GetAttributeByIndexEx(This,stream_num,index,name,name_len,type,lang_index,value,data_len) (This)->lpVtbl->GetAttributeByIndexEx(This,stream_num,index,name,name_len,type,lang_index,value,data_len)
#define IWMHeaderInfo3_ModifyAttribute(This,stream_num,index,type,lang_index,value,length) (This)->lpVtbl->ModifyAttribute(This,stream_num,index,type,lang_index,value,length)
#define IWMHeaderInfo3_AddAttribute(This,stream_num,name,index,type,lang_index,value,length) (This)->lpVtbl->AddAttribute(This,stream_num,name,index,type,lang_index,value,length)
#define IWMHeaderInfo3_DeleteAttribute(This,stream_num,index) (This)->lpVtbl->DeleteAttribute(This,stream_num,index)
#define IWMHeaderInfo3_AddCodecInfo(This,name,description,codec_type,codec_info_cnt,codec_info) (This)->lpVtbl->AddCodecInfo(This,name,description,codec_type,codec_info_cnt,codec_info)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMHeaderInfo3_QueryInterface(IWMHeaderInfo3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMHeaderInfo3_AddRef(IWMHeaderInfo3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMHeaderInfo3_Release(IWMHeaderInfo3* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMHeaderInfo methods ***/
static inline HRESULT IWMHeaderInfo3_GetAttributeCount(IWMHeaderInfo3* This,WORD stream_num,WORD *attributes) {
    return This->lpVtbl->GetAttributeCount(This,stream_num,attributes);
}
static inline HRESULT IWMHeaderInfo3_GetAttributeByIndex(IWMHeaderInfo3* This,WORD index,WORD *stream_num,WCHAR *name,WORD *name_len,WMT_ATTR_DATATYPE *type,BYTE *value,WORD *length) {
    return This->lpVtbl->GetAttributeByIndex(This,index,stream_num,name,name_len,type,value,length);
}
static inline HRESULT IWMHeaderInfo3_GetAttributeByName(IWMHeaderInfo3* This,WORD *stream_num,LPCWSTR name,WMT_ATTR_DATATYPE *type,BYTE *value,WORD *length) {
    return This->lpVtbl->GetAttributeByName(This,stream_num,name,type,value,length);
}
static inline HRESULT IWMHeaderInfo3_SetAttribute(IWMHeaderInfo3* This,WORD stream_num,LPCWSTR name,WMT_ATTR_DATATYPE type,const BYTE *value,WORD length) {
    return This->lpVtbl->SetAttribute(This,stream_num,name,type,value,length);
}
static inline HRESULT IWMHeaderInfo3_GetMarkerCount(IWMHeaderInfo3* This,WORD *markers) {
    return This->lpVtbl->GetMarkerCount(This,markers);
}
static inline HRESULT IWMHeaderInfo3_GetMarker(IWMHeaderInfo3* This,WORD index,WCHAR *marker_name,WORD *marker_len,QWORD *marker_time) {
    return This->lpVtbl->GetMarker(This,index,marker_name,marker_len,marker_time);
}
static inline HRESULT IWMHeaderInfo3_AddMarker(IWMHeaderInfo3* This,LPCWSTR_WMSDK_TYPE_SAFE marker_name,QWORD marker_time) {
    return This->lpVtbl->AddMarker(This,marker_name,marker_time);
}
static inline HRESULT IWMHeaderInfo3_RemoveMarker(IWMHeaderInfo3* This,WORD index) {
    return This->lpVtbl->RemoveMarker(This,index);
}
static inline HRESULT IWMHeaderInfo3_GetScriptCount(IWMHeaderInfo3* This,WORD *scripts) {
    return This->lpVtbl->GetScriptCount(This,scripts);
}
static inline HRESULT IWMHeaderInfo3_GetScript(IWMHeaderInfo3* This,WORD index,WCHAR *type,WORD *type_len,WCHAR *command,WORD *command_len,QWORD *script_time) {
    return This->lpVtbl->GetScript(This,index,type,type_len,command,command_len,script_time);
}
static inline HRESULT IWMHeaderInfo3_AddScript(IWMHeaderInfo3* This,LPCWSTR_WMSDK_TYPE_SAFE type,LPCWSTR_WMSDK_TYPE_SAFE command,QWORD script_time) {
    return This->lpVtbl->AddScript(This,type,command,script_time);
}
static inline HRESULT IWMHeaderInfo3_RemoveScript(IWMHeaderInfo3* This,WORD index) {
    return This->lpVtbl->RemoveScript(This,index);
}
/*** IWMHeaderInfo2 methods ***/
static inline HRESULT IWMHeaderInfo3_GetCodecInfoCount(IWMHeaderInfo3* This,DWORD *codec_infos) {
    return This->lpVtbl->GetCodecInfoCount(This,codec_infos);
}
static inline HRESULT IWMHeaderInfo3_GetCodecInfo(IWMHeaderInfo3* This,DWORD index,WORD *name_len,WCHAR *name,WORD *description_len,WCHAR *description,WMT_CODEC_INFO_TYPE *codec_type,WORD *codec_info_cnt,BYTE *codec_info) {
    return This->lpVtbl->GetCodecInfo(This,index,name_len,name,description_len,description,codec_type,codec_info_cnt,codec_info);
}
/*** IWMHeaderInfo3 methods ***/
static inline HRESULT IWMHeaderInfo3_GetAttributeCountEx(IWMHeaderInfo3* This,WORD stream_num,WORD *attributes) {
    return This->lpVtbl->GetAttributeCountEx(This,stream_num,attributes);
}
static inline HRESULT IWMHeaderInfo3_GetAttributeIndices(IWMHeaderInfo3* This,WORD stream_num,LPCWSTR name,WORD *lang_index,WORD *indices,WORD *count) {
    return This->lpVtbl->GetAttributeIndices(This,stream_num,name,lang_index,indices,count);
}
static inline HRESULT IWMHeaderInfo3_GetAttributeByIndexEx(IWMHeaderInfo3* This,WORD stream_num,WORD index,LPWSTR name,WORD *name_len,WMT_ATTR_DATATYPE *type,WORD *lang_index,BYTE *value,DWORD *data_len) {
    return This->lpVtbl->GetAttributeByIndexEx(This,stream_num,index,name,name_len,type,lang_index,value,data_len);
}
static inline HRESULT IWMHeaderInfo3_ModifyAttribute(IWMHeaderInfo3* This,WORD stream_num,WORD index,WMT_ATTR_DATATYPE type,WORD lang_index,const BYTE *value,DWORD length) {
    return This->lpVtbl->ModifyAttribute(This,stream_num,index,type,lang_index,value,length);
}
static inline HRESULT IWMHeaderInfo3_AddAttribute(IWMHeaderInfo3* This,WORD stream_num,LPCWSTR name,WORD *index,WMT_ATTR_DATATYPE type,WORD lang_index,const BYTE *value,DWORD length) {
    return This->lpVtbl->AddAttribute(This,stream_num,name,index,type,lang_index,value,length);
}
static inline HRESULT IWMHeaderInfo3_DeleteAttribute(IWMHeaderInfo3* This,WORD stream_num,WORD index) {
    return This->lpVtbl->DeleteAttribute(This,stream_num,index);
}
static inline HRESULT IWMHeaderInfo3_AddCodecInfo(IWMHeaderInfo3* This,LPCWSTR_WMSDK_TYPE_SAFE name,LPCWSTR_WMSDK_TYPE_SAFE description,WMT_CODEC_INFO_TYPE codec_type,WORD codec_info_cnt,BYTE *codec_info) {
    return This->lpVtbl->AddCodecInfo(This,name,description,codec_type,codec_info_cnt,codec_info);
}
#endif
#endif

#endif


#endif  /* __IWMHeaderInfo3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderNetworkConfig interface
 */
#ifndef __IWMReaderNetworkConfig_INTERFACE_DEFINED__
#define __IWMReaderNetworkConfig_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderNetworkConfig, 0x96406bec, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bec-2b2b-11d3-b36b-00c04f6108ff")
IWMReaderNetworkConfig : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetBufferingTime(
        QWORD *buffering_time) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBufferingTime(
        QWORD buffering_time) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUDPPortRanges(
        WM_PORT_NUMBER_RANGE *array,
        DWORD *ranges) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUDPPortRanges(
        WM_PORT_NUMBER_RANGE *array,
        DWORD ranges) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProxySettings(
        const WCHAR *protocol,
        WMT_PROXY_SETTINGS *proxy) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProxySettings(
        LPCWSTR protocol,
        WMT_PROXY_SETTINGS proxy) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProxyHostName(
        const WCHAR *protocol,
        WCHAR *hostname,
        DWORD *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProxyHostName(
        const WCHAR *protocol,
        const WCHAR *hostname) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProxyPort(
        const WCHAR *protocol,
        DWORD *port) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProxyPort(
        const WCHAR *protocol,
        DWORD port) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProxyExceptionList(
        const WCHAR *protocol,
        WCHAR *exceptions,
        DWORD *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProxyExceptionList(
        const WCHAR *protocol,
        const WCHAR *exceptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProxyBypassForLocal(
        const WCHAR *protocol,
        WINBOOL *bypass) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProxyBypassForLocal(
        const WCHAR *protocol,
        WINBOOL bypass) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetForceRerunAutoProxyDetection(
        WINBOOL *detection) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetForceRerunAutoProxyDetection(
        WINBOOL detection) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnableMulticast(
        WINBOOL *multicast) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnableMulticast(
        WINBOOL multicast) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnableHTTP(
        WINBOOL *enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnableHTTP(
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnableUDP(
        WINBOOL *enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnableUDP(
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnableTCP(
        WINBOOL *enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnableTCP(
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResetProtocolRollover(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectionBandwidth(
        DWORD *bandwidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetConnectionBandwidth(
        DWORD bandwidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetNumProtocolsSupported(
        DWORD *protocols) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSupportedProtocolName(
        DWORD protocol_num,
        WCHAR *protocol,
        DWORD *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddLoggingUrl(
        const WCHAR *url) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLoggingUrl(
        DWORD index,
        WCHAR *url,
        DWORD *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLoggingUrlCount(
        DWORD *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResetLoggingUrlList(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderNetworkConfig, 0x96406bec, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMReaderNetworkConfigVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderNetworkConfig *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderNetworkConfig *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderNetworkConfig *This);

    /*** IWMReaderNetworkConfig methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBufferingTime)(
        IWMReaderNetworkConfig *This,
        QWORD *buffering_time);

    HRESULT (STDMETHODCALLTYPE *SetBufferingTime)(
        IWMReaderNetworkConfig *This,
        QWORD buffering_time);

    HRESULT (STDMETHODCALLTYPE *GetUDPPortRanges)(
        IWMReaderNetworkConfig *This,
        WM_PORT_NUMBER_RANGE *array,
        DWORD *ranges);

    HRESULT (STDMETHODCALLTYPE *SetUDPPortRanges)(
        IWMReaderNetworkConfig *This,
        WM_PORT_NUMBER_RANGE *array,
        DWORD ranges);

    HRESULT (STDMETHODCALLTYPE *GetProxySettings)(
        IWMReaderNetworkConfig *This,
        const WCHAR *protocol,
        WMT_PROXY_SETTINGS *proxy);

    HRESULT (STDMETHODCALLTYPE *SetProxySettings)(
        IWMReaderNetworkConfig *This,
        LPCWSTR protocol,
        WMT_PROXY_SETTINGS proxy);

    HRESULT (STDMETHODCALLTYPE *GetProxyHostName)(
        IWMReaderNetworkConfig *This,
        const WCHAR *protocol,
        WCHAR *hostname,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *SetProxyHostName)(
        IWMReaderNetworkConfig *This,
        const WCHAR *protocol,
        const WCHAR *hostname);

    HRESULT (STDMETHODCALLTYPE *GetProxyPort)(
        IWMReaderNetworkConfig *This,
        const WCHAR *protocol,
        DWORD *port);

    HRESULT (STDMETHODCALLTYPE *SetProxyPort)(
        IWMReaderNetworkConfig *This,
        const WCHAR *protocol,
        DWORD port);

    HRESULT (STDMETHODCALLTYPE *GetProxyExceptionList)(
        IWMReaderNetworkConfig *This,
        const WCHAR *protocol,
        WCHAR *exceptions,
        DWORD *count);

    HRESULT (STDMETHODCALLTYPE *SetProxyExceptionList)(
        IWMReaderNetworkConfig *This,
        const WCHAR *protocol,
        const WCHAR *exceptions);

    HRESULT (STDMETHODCALLTYPE *GetProxyBypassForLocal)(
        IWMReaderNetworkConfig *This,
        const WCHAR *protocol,
        WINBOOL *bypass);

    HRESULT (STDMETHODCALLTYPE *SetProxyBypassForLocal)(
        IWMReaderNetworkConfig *This,
        const WCHAR *protocol,
        WINBOOL bypass);

    HRESULT (STDMETHODCALLTYPE *GetForceRerunAutoProxyDetection)(
        IWMReaderNetworkConfig *This,
        WINBOOL *detection);

    HRESULT (STDMETHODCALLTYPE *SetForceRerunAutoProxyDetection)(
        IWMReaderNetworkConfig *This,
        WINBOOL detection);

    HRESULT (STDMETHODCALLTYPE *GetEnableMulticast)(
        IWMReaderNetworkConfig *This,
        WINBOOL *multicast);

    HRESULT (STDMETHODCALLTYPE *SetEnableMulticast)(
        IWMReaderNetworkConfig *This,
        WINBOOL multicast);

    HRESULT (STDMETHODCALLTYPE *GetEnableHTTP)(
        IWMReaderNetworkConfig *This,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *SetEnableHTTP)(
        IWMReaderNetworkConfig *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *GetEnableUDP)(
        IWMReaderNetworkConfig *This,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *SetEnableUDP)(
        IWMReaderNetworkConfig *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *GetEnableTCP)(
        IWMReaderNetworkConfig *This,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *SetEnableTCP)(
        IWMReaderNetworkConfig *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *ResetProtocolRollover)(
        IWMReaderNetworkConfig *This);

    HRESULT (STDMETHODCALLTYPE *GetConnectionBandwidth)(
        IWMReaderNetworkConfig *This,
        DWORD *bandwidth);

    HRESULT (STDMETHODCALLTYPE *SetConnectionBandwidth)(
        IWMReaderNetworkConfig *This,
        DWORD bandwidth);

    HRESULT (STDMETHODCALLTYPE *GetNumProtocolsSupported)(
        IWMReaderNetworkConfig *This,
        DWORD *protocols);

    HRESULT (STDMETHODCALLTYPE *GetSupportedProtocolName)(
        IWMReaderNetworkConfig *This,
        DWORD protocol_num,
        WCHAR *protocol,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *AddLoggingUrl)(
        IWMReaderNetworkConfig *This,
        const WCHAR *url);

    HRESULT (STDMETHODCALLTYPE *GetLoggingUrl)(
        IWMReaderNetworkConfig *This,
        DWORD index,
        WCHAR *url,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *GetLoggingUrlCount)(
        IWMReaderNetworkConfig *This,
        DWORD *count);

    HRESULT (STDMETHODCALLTYPE *ResetLoggingUrlList)(
        IWMReaderNetworkConfig *This);

    END_INTERFACE
} IWMReaderNetworkConfigVtbl;

interface IWMReaderNetworkConfig {
    CONST_VTBL IWMReaderNetworkConfigVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderNetworkConfig_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderNetworkConfig_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderNetworkConfig_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderNetworkConfig methods ***/
#define IWMReaderNetworkConfig_GetBufferingTime(This,buffering_time) (This)->lpVtbl->GetBufferingTime(This,buffering_time)
#define IWMReaderNetworkConfig_SetBufferingTime(This,buffering_time) (This)->lpVtbl->SetBufferingTime(This,buffering_time)
#define IWMReaderNetworkConfig_GetUDPPortRanges(This,array,ranges) (This)->lpVtbl->GetUDPPortRanges(This,array,ranges)
#define IWMReaderNetworkConfig_SetUDPPortRanges(This,array,ranges) (This)->lpVtbl->SetUDPPortRanges(This,array,ranges)
#define IWMReaderNetworkConfig_GetProxySettings(This,protocol,proxy) (This)->lpVtbl->GetProxySettings(This,protocol,proxy)
#define IWMReaderNetworkConfig_SetProxySettings(This,protocol,proxy) (This)->lpVtbl->SetProxySettings(This,protocol,proxy)
#define IWMReaderNetworkConfig_GetProxyHostName(This,protocol,hostname,size) (This)->lpVtbl->GetProxyHostName(This,protocol,hostname,size)
#define IWMReaderNetworkConfig_SetProxyHostName(This,protocol,hostname) (This)->lpVtbl->SetProxyHostName(This,protocol,hostname)
#define IWMReaderNetworkConfig_GetProxyPort(This,protocol,port) (This)->lpVtbl->GetProxyPort(This,protocol,port)
#define IWMReaderNetworkConfig_SetProxyPort(This,protocol,port) (This)->lpVtbl->SetProxyPort(This,protocol,port)
#define IWMReaderNetworkConfig_GetProxyExceptionList(This,protocol,exceptions,count) (This)->lpVtbl->GetProxyExceptionList(This,protocol,exceptions,count)
#define IWMReaderNetworkConfig_SetProxyExceptionList(This,protocol,exceptions) (This)->lpVtbl->SetProxyExceptionList(This,protocol,exceptions)
#define IWMReaderNetworkConfig_GetProxyBypassForLocal(This,protocol,bypass) (This)->lpVtbl->GetProxyBypassForLocal(This,protocol,bypass)
#define IWMReaderNetworkConfig_SetProxyBypassForLocal(This,protocol,bypass) (This)->lpVtbl->SetProxyBypassForLocal(This,protocol,bypass)
#define IWMReaderNetworkConfig_GetForceRerunAutoProxyDetection(This,detection) (This)->lpVtbl->GetForceRerunAutoProxyDetection(This,detection)
#define IWMReaderNetworkConfig_SetForceRerunAutoProxyDetection(This,detection) (This)->lpVtbl->SetForceRerunAutoProxyDetection(This,detection)
#define IWMReaderNetworkConfig_GetEnableMulticast(This,multicast) (This)->lpVtbl->GetEnableMulticast(This,multicast)
#define IWMReaderNetworkConfig_SetEnableMulticast(This,multicast) (This)->lpVtbl->SetEnableMulticast(This,multicast)
#define IWMReaderNetworkConfig_GetEnableHTTP(This,enable) (This)->lpVtbl->GetEnableHTTP(This,enable)
#define IWMReaderNetworkConfig_SetEnableHTTP(This,enable) (This)->lpVtbl->SetEnableHTTP(This,enable)
#define IWMReaderNetworkConfig_GetEnableUDP(This,enable) (This)->lpVtbl->GetEnableUDP(This,enable)
#define IWMReaderNetworkConfig_SetEnableUDP(This,enable) (This)->lpVtbl->SetEnableUDP(This,enable)
#define IWMReaderNetworkConfig_GetEnableTCP(This,enable) (This)->lpVtbl->GetEnableTCP(This,enable)
#define IWMReaderNetworkConfig_SetEnableTCP(This,enable) (This)->lpVtbl->SetEnableTCP(This,enable)
#define IWMReaderNetworkConfig_ResetProtocolRollover(This) (This)->lpVtbl->ResetProtocolRollover(This)
#define IWMReaderNetworkConfig_GetConnectionBandwidth(This,bandwidth) (This)->lpVtbl->GetConnectionBandwidth(This,bandwidth)
#define IWMReaderNetworkConfig_SetConnectionBandwidth(This,bandwidth) (This)->lpVtbl->SetConnectionBandwidth(This,bandwidth)
#define IWMReaderNetworkConfig_GetNumProtocolsSupported(This,protocols) (This)->lpVtbl->GetNumProtocolsSupported(This,protocols)
#define IWMReaderNetworkConfig_GetSupportedProtocolName(This,protocol_num,protocol,size) (This)->lpVtbl->GetSupportedProtocolName(This,protocol_num,protocol,size)
#define IWMReaderNetworkConfig_AddLoggingUrl(This,url) (This)->lpVtbl->AddLoggingUrl(This,url)
#define IWMReaderNetworkConfig_GetLoggingUrl(This,index,url,size) (This)->lpVtbl->GetLoggingUrl(This,index,url,size)
#define IWMReaderNetworkConfig_GetLoggingUrlCount(This,count) (This)->lpVtbl->GetLoggingUrlCount(This,count)
#define IWMReaderNetworkConfig_ResetLoggingUrlList(This) (This)->lpVtbl->ResetLoggingUrlList(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderNetworkConfig_QueryInterface(IWMReaderNetworkConfig* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderNetworkConfig_AddRef(IWMReaderNetworkConfig* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderNetworkConfig_Release(IWMReaderNetworkConfig* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderNetworkConfig methods ***/
static inline HRESULT IWMReaderNetworkConfig_GetBufferingTime(IWMReaderNetworkConfig* This,QWORD *buffering_time) {
    return This->lpVtbl->GetBufferingTime(This,buffering_time);
}
static inline HRESULT IWMReaderNetworkConfig_SetBufferingTime(IWMReaderNetworkConfig* This,QWORD buffering_time) {
    return This->lpVtbl->SetBufferingTime(This,buffering_time);
}
static inline HRESULT IWMReaderNetworkConfig_GetUDPPortRanges(IWMReaderNetworkConfig* This,WM_PORT_NUMBER_RANGE *array,DWORD *ranges) {
    return This->lpVtbl->GetUDPPortRanges(This,array,ranges);
}
static inline HRESULT IWMReaderNetworkConfig_SetUDPPortRanges(IWMReaderNetworkConfig* This,WM_PORT_NUMBER_RANGE *array,DWORD ranges) {
    return This->lpVtbl->SetUDPPortRanges(This,array,ranges);
}
static inline HRESULT IWMReaderNetworkConfig_GetProxySettings(IWMReaderNetworkConfig* This,const WCHAR *protocol,WMT_PROXY_SETTINGS *proxy) {
    return This->lpVtbl->GetProxySettings(This,protocol,proxy);
}
static inline HRESULT IWMReaderNetworkConfig_SetProxySettings(IWMReaderNetworkConfig* This,LPCWSTR protocol,WMT_PROXY_SETTINGS proxy) {
    return This->lpVtbl->SetProxySettings(This,protocol,proxy);
}
static inline HRESULT IWMReaderNetworkConfig_GetProxyHostName(IWMReaderNetworkConfig* This,const WCHAR *protocol,WCHAR *hostname,DWORD *size) {
    return This->lpVtbl->GetProxyHostName(This,protocol,hostname,size);
}
static inline HRESULT IWMReaderNetworkConfig_SetProxyHostName(IWMReaderNetworkConfig* This,const WCHAR *protocol,const WCHAR *hostname) {
    return This->lpVtbl->SetProxyHostName(This,protocol,hostname);
}
static inline HRESULT IWMReaderNetworkConfig_GetProxyPort(IWMReaderNetworkConfig* This,const WCHAR *protocol,DWORD *port) {
    return This->lpVtbl->GetProxyPort(This,protocol,port);
}
static inline HRESULT IWMReaderNetworkConfig_SetProxyPort(IWMReaderNetworkConfig* This,const WCHAR *protocol,DWORD port) {
    return This->lpVtbl->SetProxyPort(This,protocol,port);
}
static inline HRESULT IWMReaderNetworkConfig_GetProxyExceptionList(IWMReaderNetworkConfig* This,const WCHAR *protocol,WCHAR *exceptions,DWORD *count) {
    return This->lpVtbl->GetProxyExceptionList(This,protocol,exceptions,count);
}
static inline HRESULT IWMReaderNetworkConfig_SetProxyExceptionList(IWMReaderNetworkConfig* This,const WCHAR *protocol,const WCHAR *exceptions) {
    return This->lpVtbl->SetProxyExceptionList(This,protocol,exceptions);
}
static inline HRESULT IWMReaderNetworkConfig_GetProxyBypassForLocal(IWMReaderNetworkConfig* This,const WCHAR *protocol,WINBOOL *bypass) {
    return This->lpVtbl->GetProxyBypassForLocal(This,protocol,bypass);
}
static inline HRESULT IWMReaderNetworkConfig_SetProxyBypassForLocal(IWMReaderNetworkConfig* This,const WCHAR *protocol,WINBOOL bypass) {
    return This->lpVtbl->SetProxyBypassForLocal(This,protocol,bypass);
}
static inline HRESULT IWMReaderNetworkConfig_GetForceRerunAutoProxyDetection(IWMReaderNetworkConfig* This,WINBOOL *detection) {
    return This->lpVtbl->GetForceRerunAutoProxyDetection(This,detection);
}
static inline HRESULT IWMReaderNetworkConfig_SetForceRerunAutoProxyDetection(IWMReaderNetworkConfig* This,WINBOOL detection) {
    return This->lpVtbl->SetForceRerunAutoProxyDetection(This,detection);
}
static inline HRESULT IWMReaderNetworkConfig_GetEnableMulticast(IWMReaderNetworkConfig* This,WINBOOL *multicast) {
    return This->lpVtbl->GetEnableMulticast(This,multicast);
}
static inline HRESULT IWMReaderNetworkConfig_SetEnableMulticast(IWMReaderNetworkConfig* This,WINBOOL multicast) {
    return This->lpVtbl->SetEnableMulticast(This,multicast);
}
static inline HRESULT IWMReaderNetworkConfig_GetEnableHTTP(IWMReaderNetworkConfig* This,WINBOOL *enable) {
    return This->lpVtbl->GetEnableHTTP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig_SetEnableHTTP(IWMReaderNetworkConfig* This,WINBOOL enable) {
    return This->lpVtbl->SetEnableHTTP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig_GetEnableUDP(IWMReaderNetworkConfig* This,WINBOOL *enable) {
    return This->lpVtbl->GetEnableUDP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig_SetEnableUDP(IWMReaderNetworkConfig* This,WINBOOL enable) {
    return This->lpVtbl->SetEnableUDP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig_GetEnableTCP(IWMReaderNetworkConfig* This,WINBOOL *enable) {
    return This->lpVtbl->GetEnableTCP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig_SetEnableTCP(IWMReaderNetworkConfig* This,WINBOOL enable) {
    return This->lpVtbl->SetEnableTCP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig_ResetProtocolRollover(IWMReaderNetworkConfig* This) {
    return This->lpVtbl->ResetProtocolRollover(This);
}
static inline HRESULT IWMReaderNetworkConfig_GetConnectionBandwidth(IWMReaderNetworkConfig* This,DWORD *bandwidth) {
    return This->lpVtbl->GetConnectionBandwidth(This,bandwidth);
}
static inline HRESULT IWMReaderNetworkConfig_SetConnectionBandwidth(IWMReaderNetworkConfig* This,DWORD bandwidth) {
    return This->lpVtbl->SetConnectionBandwidth(This,bandwidth);
}
static inline HRESULT IWMReaderNetworkConfig_GetNumProtocolsSupported(IWMReaderNetworkConfig* This,DWORD *protocols) {
    return This->lpVtbl->GetNumProtocolsSupported(This,protocols);
}
static inline HRESULT IWMReaderNetworkConfig_GetSupportedProtocolName(IWMReaderNetworkConfig* This,DWORD protocol_num,WCHAR *protocol,DWORD *size) {
    return This->lpVtbl->GetSupportedProtocolName(This,protocol_num,protocol,size);
}
static inline HRESULT IWMReaderNetworkConfig_AddLoggingUrl(IWMReaderNetworkConfig* This,const WCHAR *url) {
    return This->lpVtbl->AddLoggingUrl(This,url);
}
static inline HRESULT IWMReaderNetworkConfig_GetLoggingUrl(IWMReaderNetworkConfig* This,DWORD index,WCHAR *url,DWORD *size) {
    return This->lpVtbl->GetLoggingUrl(This,index,url,size);
}
static inline HRESULT IWMReaderNetworkConfig_GetLoggingUrlCount(IWMReaderNetworkConfig* This,DWORD *count) {
    return This->lpVtbl->GetLoggingUrlCount(This,count);
}
static inline HRESULT IWMReaderNetworkConfig_ResetLoggingUrlList(IWMReaderNetworkConfig* This) {
    return This->lpVtbl->ResetLoggingUrlList(This);
}
#endif
#endif

#endif


#endif  /* __IWMReaderNetworkConfig_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderNetworkConfig2 interface
 */
#ifndef __IWMReaderNetworkConfig2_INTERFACE_DEFINED__
#define __IWMReaderNetworkConfig2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderNetworkConfig2, 0xd979a853, 0x042b, 0x4050, 0x83,0x87, 0xc9,0x39,0xdb,0x22,0x01,0x3f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d979a853-042b-4050-8387-c939db22013f")
IWMReaderNetworkConfig2 : public IWMReaderNetworkConfig
{
    virtual HRESULT STDMETHODCALLTYPE GetEnableContentCaching(
        WINBOOL *enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnableContentCaching(
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnableFastCache(
        WINBOOL *enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnableFastCache(
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAcceleratedStreamingDuration(
        QWORD *duration) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAcceleratedStreamingDuration(
        QWORD duration) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAutoReconnectLimit(
        DWORD *limit) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAutoReconnectLimit(
        DWORD limit) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnableResends(
        WINBOOL *enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnableResends(
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEnableThinning(
        WINBOOL *enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEnableThinning(
        WINBOOL enable) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMaxNetPacketSize(
        DWORD *packet_size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderNetworkConfig2, 0xd979a853, 0x042b, 0x4050, 0x83,0x87, 0xc9,0x39,0xdb,0x22,0x01,0x3f)
#endif
#else
typedef struct IWMReaderNetworkConfig2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderNetworkConfig2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderNetworkConfig2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderNetworkConfig2 *This);

    /*** IWMReaderNetworkConfig methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBufferingTime)(
        IWMReaderNetworkConfig2 *This,
        QWORD *buffering_time);

    HRESULT (STDMETHODCALLTYPE *SetBufferingTime)(
        IWMReaderNetworkConfig2 *This,
        QWORD buffering_time);

    HRESULT (STDMETHODCALLTYPE *GetUDPPortRanges)(
        IWMReaderNetworkConfig2 *This,
        WM_PORT_NUMBER_RANGE *array,
        DWORD *ranges);

    HRESULT (STDMETHODCALLTYPE *SetUDPPortRanges)(
        IWMReaderNetworkConfig2 *This,
        WM_PORT_NUMBER_RANGE *array,
        DWORD ranges);

    HRESULT (STDMETHODCALLTYPE *GetProxySettings)(
        IWMReaderNetworkConfig2 *This,
        const WCHAR *protocol,
        WMT_PROXY_SETTINGS *proxy);

    HRESULT (STDMETHODCALLTYPE *SetProxySettings)(
        IWMReaderNetworkConfig2 *This,
        LPCWSTR protocol,
        WMT_PROXY_SETTINGS proxy);

    HRESULT (STDMETHODCALLTYPE *GetProxyHostName)(
        IWMReaderNetworkConfig2 *This,
        const WCHAR *protocol,
        WCHAR *hostname,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *SetProxyHostName)(
        IWMReaderNetworkConfig2 *This,
        const WCHAR *protocol,
        const WCHAR *hostname);

    HRESULT (STDMETHODCALLTYPE *GetProxyPort)(
        IWMReaderNetworkConfig2 *This,
        const WCHAR *protocol,
        DWORD *port);

    HRESULT (STDMETHODCALLTYPE *SetProxyPort)(
        IWMReaderNetworkConfig2 *This,
        const WCHAR *protocol,
        DWORD port);

    HRESULT (STDMETHODCALLTYPE *GetProxyExceptionList)(
        IWMReaderNetworkConfig2 *This,
        const WCHAR *protocol,
        WCHAR *exceptions,
        DWORD *count);

    HRESULT (STDMETHODCALLTYPE *SetProxyExceptionList)(
        IWMReaderNetworkConfig2 *This,
        const WCHAR *protocol,
        const WCHAR *exceptions);

    HRESULT (STDMETHODCALLTYPE *GetProxyBypassForLocal)(
        IWMReaderNetworkConfig2 *This,
        const WCHAR *protocol,
        WINBOOL *bypass);

    HRESULT (STDMETHODCALLTYPE *SetProxyBypassForLocal)(
        IWMReaderNetworkConfig2 *This,
        const WCHAR *protocol,
        WINBOOL bypass);

    HRESULT (STDMETHODCALLTYPE *GetForceRerunAutoProxyDetection)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL *detection);

    HRESULT (STDMETHODCALLTYPE *SetForceRerunAutoProxyDetection)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL detection);

    HRESULT (STDMETHODCALLTYPE *GetEnableMulticast)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL *multicast);

    HRESULT (STDMETHODCALLTYPE *SetEnableMulticast)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL multicast);

    HRESULT (STDMETHODCALLTYPE *GetEnableHTTP)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *SetEnableHTTP)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *GetEnableUDP)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *SetEnableUDP)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *GetEnableTCP)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *SetEnableTCP)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *ResetProtocolRollover)(
        IWMReaderNetworkConfig2 *This);

    HRESULT (STDMETHODCALLTYPE *GetConnectionBandwidth)(
        IWMReaderNetworkConfig2 *This,
        DWORD *bandwidth);

    HRESULT (STDMETHODCALLTYPE *SetConnectionBandwidth)(
        IWMReaderNetworkConfig2 *This,
        DWORD bandwidth);

    HRESULT (STDMETHODCALLTYPE *GetNumProtocolsSupported)(
        IWMReaderNetworkConfig2 *This,
        DWORD *protocols);

    HRESULT (STDMETHODCALLTYPE *GetSupportedProtocolName)(
        IWMReaderNetworkConfig2 *This,
        DWORD protocol_num,
        WCHAR *protocol,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *AddLoggingUrl)(
        IWMReaderNetworkConfig2 *This,
        const WCHAR *url);

    HRESULT (STDMETHODCALLTYPE *GetLoggingUrl)(
        IWMReaderNetworkConfig2 *This,
        DWORD index,
        WCHAR *url,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *GetLoggingUrlCount)(
        IWMReaderNetworkConfig2 *This,
        DWORD *count);

    HRESULT (STDMETHODCALLTYPE *ResetLoggingUrlList)(
        IWMReaderNetworkConfig2 *This);

    /*** IWMReaderNetworkConfig2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEnableContentCaching)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *SetEnableContentCaching)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *GetEnableFastCache)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *SetEnableFastCache)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *GetAcceleratedStreamingDuration)(
        IWMReaderNetworkConfig2 *This,
        QWORD *duration);

    HRESULT (STDMETHODCALLTYPE *SetAcceleratedStreamingDuration)(
        IWMReaderNetworkConfig2 *This,
        QWORD duration);

    HRESULT (STDMETHODCALLTYPE *GetAutoReconnectLimit)(
        IWMReaderNetworkConfig2 *This,
        DWORD *limit);

    HRESULT (STDMETHODCALLTYPE *SetAutoReconnectLimit)(
        IWMReaderNetworkConfig2 *This,
        DWORD limit);

    HRESULT (STDMETHODCALLTYPE *GetEnableResends)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *SetEnableResends)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *GetEnableThinning)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL *enable);

    HRESULT (STDMETHODCALLTYPE *SetEnableThinning)(
        IWMReaderNetworkConfig2 *This,
        WINBOOL enable);

    HRESULT (STDMETHODCALLTYPE *GetMaxNetPacketSize)(
        IWMReaderNetworkConfig2 *This,
        DWORD *packet_size);

    END_INTERFACE
} IWMReaderNetworkConfig2Vtbl;

interface IWMReaderNetworkConfig2 {
    CONST_VTBL IWMReaderNetworkConfig2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderNetworkConfig2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderNetworkConfig2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderNetworkConfig2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderNetworkConfig methods ***/
#define IWMReaderNetworkConfig2_GetBufferingTime(This,buffering_time) (This)->lpVtbl->GetBufferingTime(This,buffering_time)
#define IWMReaderNetworkConfig2_SetBufferingTime(This,buffering_time) (This)->lpVtbl->SetBufferingTime(This,buffering_time)
#define IWMReaderNetworkConfig2_GetUDPPortRanges(This,array,ranges) (This)->lpVtbl->GetUDPPortRanges(This,array,ranges)
#define IWMReaderNetworkConfig2_SetUDPPortRanges(This,array,ranges) (This)->lpVtbl->SetUDPPortRanges(This,array,ranges)
#define IWMReaderNetworkConfig2_GetProxySettings(This,protocol,proxy) (This)->lpVtbl->GetProxySettings(This,protocol,proxy)
#define IWMReaderNetworkConfig2_SetProxySettings(This,protocol,proxy) (This)->lpVtbl->SetProxySettings(This,protocol,proxy)
#define IWMReaderNetworkConfig2_GetProxyHostName(This,protocol,hostname,size) (This)->lpVtbl->GetProxyHostName(This,protocol,hostname,size)
#define IWMReaderNetworkConfig2_SetProxyHostName(This,protocol,hostname) (This)->lpVtbl->SetProxyHostName(This,protocol,hostname)
#define IWMReaderNetworkConfig2_GetProxyPort(This,protocol,port) (This)->lpVtbl->GetProxyPort(This,protocol,port)
#define IWMReaderNetworkConfig2_SetProxyPort(This,protocol,port) (This)->lpVtbl->SetProxyPort(This,protocol,port)
#define IWMReaderNetworkConfig2_GetProxyExceptionList(This,protocol,exceptions,count) (This)->lpVtbl->GetProxyExceptionList(This,protocol,exceptions,count)
#define IWMReaderNetworkConfig2_SetProxyExceptionList(This,protocol,exceptions) (This)->lpVtbl->SetProxyExceptionList(This,protocol,exceptions)
#define IWMReaderNetworkConfig2_GetProxyBypassForLocal(This,protocol,bypass) (This)->lpVtbl->GetProxyBypassForLocal(This,protocol,bypass)
#define IWMReaderNetworkConfig2_SetProxyBypassForLocal(This,protocol,bypass) (This)->lpVtbl->SetProxyBypassForLocal(This,protocol,bypass)
#define IWMReaderNetworkConfig2_GetForceRerunAutoProxyDetection(This,detection) (This)->lpVtbl->GetForceRerunAutoProxyDetection(This,detection)
#define IWMReaderNetworkConfig2_SetForceRerunAutoProxyDetection(This,detection) (This)->lpVtbl->SetForceRerunAutoProxyDetection(This,detection)
#define IWMReaderNetworkConfig2_GetEnableMulticast(This,multicast) (This)->lpVtbl->GetEnableMulticast(This,multicast)
#define IWMReaderNetworkConfig2_SetEnableMulticast(This,multicast) (This)->lpVtbl->SetEnableMulticast(This,multicast)
#define IWMReaderNetworkConfig2_GetEnableHTTP(This,enable) (This)->lpVtbl->GetEnableHTTP(This,enable)
#define IWMReaderNetworkConfig2_SetEnableHTTP(This,enable) (This)->lpVtbl->SetEnableHTTP(This,enable)
#define IWMReaderNetworkConfig2_GetEnableUDP(This,enable) (This)->lpVtbl->GetEnableUDP(This,enable)
#define IWMReaderNetworkConfig2_SetEnableUDP(This,enable) (This)->lpVtbl->SetEnableUDP(This,enable)
#define IWMReaderNetworkConfig2_GetEnableTCP(This,enable) (This)->lpVtbl->GetEnableTCP(This,enable)
#define IWMReaderNetworkConfig2_SetEnableTCP(This,enable) (This)->lpVtbl->SetEnableTCP(This,enable)
#define IWMReaderNetworkConfig2_ResetProtocolRollover(This) (This)->lpVtbl->ResetProtocolRollover(This)
#define IWMReaderNetworkConfig2_GetConnectionBandwidth(This,bandwidth) (This)->lpVtbl->GetConnectionBandwidth(This,bandwidth)
#define IWMReaderNetworkConfig2_SetConnectionBandwidth(This,bandwidth) (This)->lpVtbl->SetConnectionBandwidth(This,bandwidth)
#define IWMReaderNetworkConfig2_GetNumProtocolsSupported(This,protocols) (This)->lpVtbl->GetNumProtocolsSupported(This,protocols)
#define IWMReaderNetworkConfig2_GetSupportedProtocolName(This,protocol_num,protocol,size) (This)->lpVtbl->GetSupportedProtocolName(This,protocol_num,protocol,size)
#define IWMReaderNetworkConfig2_AddLoggingUrl(This,url) (This)->lpVtbl->AddLoggingUrl(This,url)
#define IWMReaderNetworkConfig2_GetLoggingUrl(This,index,url,size) (This)->lpVtbl->GetLoggingUrl(This,index,url,size)
#define IWMReaderNetworkConfig2_GetLoggingUrlCount(This,count) (This)->lpVtbl->GetLoggingUrlCount(This,count)
#define IWMReaderNetworkConfig2_ResetLoggingUrlList(This) (This)->lpVtbl->ResetLoggingUrlList(This)
/*** IWMReaderNetworkConfig2 methods ***/
#define IWMReaderNetworkConfig2_GetEnableContentCaching(This,enable) (This)->lpVtbl->GetEnableContentCaching(This,enable)
#define IWMReaderNetworkConfig2_SetEnableContentCaching(This,enable) (This)->lpVtbl->SetEnableContentCaching(This,enable)
#define IWMReaderNetworkConfig2_GetEnableFastCache(This,enable) (This)->lpVtbl->GetEnableFastCache(This,enable)
#define IWMReaderNetworkConfig2_SetEnableFastCache(This,enable) (This)->lpVtbl->SetEnableFastCache(This,enable)
#define IWMReaderNetworkConfig2_GetAcceleratedStreamingDuration(This,duration) (This)->lpVtbl->GetAcceleratedStreamingDuration(This,duration)
#define IWMReaderNetworkConfig2_SetAcceleratedStreamingDuration(This,duration) (This)->lpVtbl->SetAcceleratedStreamingDuration(This,duration)
#define IWMReaderNetworkConfig2_GetAutoReconnectLimit(This,limit) (This)->lpVtbl->GetAutoReconnectLimit(This,limit)
#define IWMReaderNetworkConfig2_SetAutoReconnectLimit(This,limit) (This)->lpVtbl->SetAutoReconnectLimit(This,limit)
#define IWMReaderNetworkConfig2_GetEnableResends(This,enable) (This)->lpVtbl->GetEnableResends(This,enable)
#define IWMReaderNetworkConfig2_SetEnableResends(This,enable) (This)->lpVtbl->SetEnableResends(This,enable)
#define IWMReaderNetworkConfig2_GetEnableThinning(This,enable) (This)->lpVtbl->GetEnableThinning(This,enable)
#define IWMReaderNetworkConfig2_SetEnableThinning(This,enable) (This)->lpVtbl->SetEnableThinning(This,enable)
#define IWMReaderNetworkConfig2_GetMaxNetPacketSize(This,packet_size) (This)->lpVtbl->GetMaxNetPacketSize(This,packet_size)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderNetworkConfig2_QueryInterface(IWMReaderNetworkConfig2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderNetworkConfig2_AddRef(IWMReaderNetworkConfig2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderNetworkConfig2_Release(IWMReaderNetworkConfig2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderNetworkConfig methods ***/
static inline HRESULT IWMReaderNetworkConfig2_GetBufferingTime(IWMReaderNetworkConfig2* This,QWORD *buffering_time) {
    return This->lpVtbl->GetBufferingTime(This,buffering_time);
}
static inline HRESULT IWMReaderNetworkConfig2_SetBufferingTime(IWMReaderNetworkConfig2* This,QWORD buffering_time) {
    return This->lpVtbl->SetBufferingTime(This,buffering_time);
}
static inline HRESULT IWMReaderNetworkConfig2_GetUDPPortRanges(IWMReaderNetworkConfig2* This,WM_PORT_NUMBER_RANGE *array,DWORD *ranges) {
    return This->lpVtbl->GetUDPPortRanges(This,array,ranges);
}
static inline HRESULT IWMReaderNetworkConfig2_SetUDPPortRanges(IWMReaderNetworkConfig2* This,WM_PORT_NUMBER_RANGE *array,DWORD ranges) {
    return This->lpVtbl->SetUDPPortRanges(This,array,ranges);
}
static inline HRESULT IWMReaderNetworkConfig2_GetProxySettings(IWMReaderNetworkConfig2* This,const WCHAR *protocol,WMT_PROXY_SETTINGS *proxy) {
    return This->lpVtbl->GetProxySettings(This,protocol,proxy);
}
static inline HRESULT IWMReaderNetworkConfig2_SetProxySettings(IWMReaderNetworkConfig2* This,LPCWSTR protocol,WMT_PROXY_SETTINGS proxy) {
    return This->lpVtbl->SetProxySettings(This,protocol,proxy);
}
static inline HRESULT IWMReaderNetworkConfig2_GetProxyHostName(IWMReaderNetworkConfig2* This,const WCHAR *protocol,WCHAR *hostname,DWORD *size) {
    return This->lpVtbl->GetProxyHostName(This,protocol,hostname,size);
}
static inline HRESULT IWMReaderNetworkConfig2_SetProxyHostName(IWMReaderNetworkConfig2* This,const WCHAR *protocol,const WCHAR *hostname) {
    return This->lpVtbl->SetProxyHostName(This,protocol,hostname);
}
static inline HRESULT IWMReaderNetworkConfig2_GetProxyPort(IWMReaderNetworkConfig2* This,const WCHAR *protocol,DWORD *port) {
    return This->lpVtbl->GetProxyPort(This,protocol,port);
}
static inline HRESULT IWMReaderNetworkConfig2_SetProxyPort(IWMReaderNetworkConfig2* This,const WCHAR *protocol,DWORD port) {
    return This->lpVtbl->SetProxyPort(This,protocol,port);
}
static inline HRESULT IWMReaderNetworkConfig2_GetProxyExceptionList(IWMReaderNetworkConfig2* This,const WCHAR *protocol,WCHAR *exceptions,DWORD *count) {
    return This->lpVtbl->GetProxyExceptionList(This,protocol,exceptions,count);
}
static inline HRESULT IWMReaderNetworkConfig2_SetProxyExceptionList(IWMReaderNetworkConfig2* This,const WCHAR *protocol,const WCHAR *exceptions) {
    return This->lpVtbl->SetProxyExceptionList(This,protocol,exceptions);
}
static inline HRESULT IWMReaderNetworkConfig2_GetProxyBypassForLocal(IWMReaderNetworkConfig2* This,const WCHAR *protocol,WINBOOL *bypass) {
    return This->lpVtbl->GetProxyBypassForLocal(This,protocol,bypass);
}
static inline HRESULT IWMReaderNetworkConfig2_SetProxyBypassForLocal(IWMReaderNetworkConfig2* This,const WCHAR *protocol,WINBOOL bypass) {
    return This->lpVtbl->SetProxyBypassForLocal(This,protocol,bypass);
}
static inline HRESULT IWMReaderNetworkConfig2_GetForceRerunAutoProxyDetection(IWMReaderNetworkConfig2* This,WINBOOL *detection) {
    return This->lpVtbl->GetForceRerunAutoProxyDetection(This,detection);
}
static inline HRESULT IWMReaderNetworkConfig2_SetForceRerunAutoProxyDetection(IWMReaderNetworkConfig2* This,WINBOOL detection) {
    return This->lpVtbl->SetForceRerunAutoProxyDetection(This,detection);
}
static inline HRESULT IWMReaderNetworkConfig2_GetEnableMulticast(IWMReaderNetworkConfig2* This,WINBOOL *multicast) {
    return This->lpVtbl->GetEnableMulticast(This,multicast);
}
static inline HRESULT IWMReaderNetworkConfig2_SetEnableMulticast(IWMReaderNetworkConfig2* This,WINBOOL multicast) {
    return This->lpVtbl->SetEnableMulticast(This,multicast);
}
static inline HRESULT IWMReaderNetworkConfig2_GetEnableHTTP(IWMReaderNetworkConfig2* This,WINBOOL *enable) {
    return This->lpVtbl->GetEnableHTTP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_SetEnableHTTP(IWMReaderNetworkConfig2* This,WINBOOL enable) {
    return This->lpVtbl->SetEnableHTTP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_GetEnableUDP(IWMReaderNetworkConfig2* This,WINBOOL *enable) {
    return This->lpVtbl->GetEnableUDP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_SetEnableUDP(IWMReaderNetworkConfig2* This,WINBOOL enable) {
    return This->lpVtbl->SetEnableUDP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_GetEnableTCP(IWMReaderNetworkConfig2* This,WINBOOL *enable) {
    return This->lpVtbl->GetEnableTCP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_SetEnableTCP(IWMReaderNetworkConfig2* This,WINBOOL enable) {
    return This->lpVtbl->SetEnableTCP(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_ResetProtocolRollover(IWMReaderNetworkConfig2* This) {
    return This->lpVtbl->ResetProtocolRollover(This);
}
static inline HRESULT IWMReaderNetworkConfig2_GetConnectionBandwidth(IWMReaderNetworkConfig2* This,DWORD *bandwidth) {
    return This->lpVtbl->GetConnectionBandwidth(This,bandwidth);
}
static inline HRESULT IWMReaderNetworkConfig2_SetConnectionBandwidth(IWMReaderNetworkConfig2* This,DWORD bandwidth) {
    return This->lpVtbl->SetConnectionBandwidth(This,bandwidth);
}
static inline HRESULT IWMReaderNetworkConfig2_GetNumProtocolsSupported(IWMReaderNetworkConfig2* This,DWORD *protocols) {
    return This->lpVtbl->GetNumProtocolsSupported(This,protocols);
}
static inline HRESULT IWMReaderNetworkConfig2_GetSupportedProtocolName(IWMReaderNetworkConfig2* This,DWORD protocol_num,WCHAR *protocol,DWORD *size) {
    return This->lpVtbl->GetSupportedProtocolName(This,protocol_num,protocol,size);
}
static inline HRESULT IWMReaderNetworkConfig2_AddLoggingUrl(IWMReaderNetworkConfig2* This,const WCHAR *url) {
    return This->lpVtbl->AddLoggingUrl(This,url);
}
static inline HRESULT IWMReaderNetworkConfig2_GetLoggingUrl(IWMReaderNetworkConfig2* This,DWORD index,WCHAR *url,DWORD *size) {
    return This->lpVtbl->GetLoggingUrl(This,index,url,size);
}
static inline HRESULT IWMReaderNetworkConfig2_GetLoggingUrlCount(IWMReaderNetworkConfig2* This,DWORD *count) {
    return This->lpVtbl->GetLoggingUrlCount(This,count);
}
static inline HRESULT IWMReaderNetworkConfig2_ResetLoggingUrlList(IWMReaderNetworkConfig2* This) {
    return This->lpVtbl->ResetLoggingUrlList(This);
}
/*** IWMReaderNetworkConfig2 methods ***/
static inline HRESULT IWMReaderNetworkConfig2_GetEnableContentCaching(IWMReaderNetworkConfig2* This,WINBOOL *enable) {
    return This->lpVtbl->GetEnableContentCaching(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_SetEnableContentCaching(IWMReaderNetworkConfig2* This,WINBOOL enable) {
    return This->lpVtbl->SetEnableContentCaching(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_GetEnableFastCache(IWMReaderNetworkConfig2* This,WINBOOL *enable) {
    return This->lpVtbl->GetEnableFastCache(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_SetEnableFastCache(IWMReaderNetworkConfig2* This,WINBOOL enable) {
    return This->lpVtbl->SetEnableFastCache(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_GetAcceleratedStreamingDuration(IWMReaderNetworkConfig2* This,QWORD *duration) {
    return This->lpVtbl->GetAcceleratedStreamingDuration(This,duration);
}
static inline HRESULT IWMReaderNetworkConfig2_SetAcceleratedStreamingDuration(IWMReaderNetworkConfig2* This,QWORD duration) {
    return This->lpVtbl->SetAcceleratedStreamingDuration(This,duration);
}
static inline HRESULT IWMReaderNetworkConfig2_GetAutoReconnectLimit(IWMReaderNetworkConfig2* This,DWORD *limit) {
    return This->lpVtbl->GetAutoReconnectLimit(This,limit);
}
static inline HRESULT IWMReaderNetworkConfig2_SetAutoReconnectLimit(IWMReaderNetworkConfig2* This,DWORD limit) {
    return This->lpVtbl->SetAutoReconnectLimit(This,limit);
}
static inline HRESULT IWMReaderNetworkConfig2_GetEnableResends(IWMReaderNetworkConfig2* This,WINBOOL *enable) {
    return This->lpVtbl->GetEnableResends(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_SetEnableResends(IWMReaderNetworkConfig2* This,WINBOOL enable) {
    return This->lpVtbl->SetEnableResends(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_GetEnableThinning(IWMReaderNetworkConfig2* This,WINBOOL *enable) {
    return This->lpVtbl->GetEnableThinning(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_SetEnableThinning(IWMReaderNetworkConfig2* This,WINBOOL enable) {
    return This->lpVtbl->SetEnableThinning(This,enable);
}
static inline HRESULT IWMReaderNetworkConfig2_GetMaxNetPacketSize(IWMReaderNetworkConfig2* This,DWORD *packet_size) {
    return This->lpVtbl->GetMaxNetPacketSize(This,packet_size);
}
#endif
#endif

#endif


#endif  /* __IWMReaderNetworkConfig2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderStreamClock interface
 */
#ifndef __IWMReaderStreamClock_INTERFACE_DEFINED__
#define __IWMReaderStreamClock_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderStreamClock, 0x96406bed, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("96406bed-2b2b-11d3-b36b-00c04f6108ff")
IWMReaderStreamClock : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetTime(
        QWORD *now) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTimer(
        QWORD when,
        void *param,
        DWORD *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE KillTimer(
        DWORD id) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderStreamClock, 0x96406bed, 0x2b2b, 0x11d3, 0xb3,0x6b, 0x00,0xc0,0x4f,0x61,0x08,0xff)
#endif
#else
typedef struct IWMReaderStreamClockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderStreamClock *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderStreamClock *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderStreamClock *This);

    /*** IWMReaderStreamClock methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTime)(
        IWMReaderStreamClock *This,
        QWORD *now);

    HRESULT (STDMETHODCALLTYPE *SetTimer)(
        IWMReaderStreamClock *This,
        QWORD when,
        void *param,
        DWORD *id);

    HRESULT (STDMETHODCALLTYPE *KillTimer)(
        IWMReaderStreamClock *This,
        DWORD id);

    END_INTERFACE
} IWMReaderStreamClockVtbl;

interface IWMReaderStreamClock {
    CONST_VTBL IWMReaderStreamClockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderStreamClock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderStreamClock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderStreamClock_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderStreamClock methods ***/
#define IWMReaderStreamClock_GetTime(This,now) (This)->lpVtbl->GetTime(This,now)
#define IWMReaderStreamClock_SetTimer(This,when,param,id) (This)->lpVtbl->SetTimer(This,when,param,id)
#define IWMReaderStreamClock_KillTimer(This,id) (This)->lpVtbl->KillTimer(This,id)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderStreamClock_QueryInterface(IWMReaderStreamClock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderStreamClock_AddRef(IWMReaderStreamClock* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderStreamClock_Release(IWMReaderStreamClock* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderStreamClock methods ***/
static inline HRESULT IWMReaderStreamClock_GetTime(IWMReaderStreamClock* This,QWORD *now) {
    return This->lpVtbl->GetTime(This,now);
}
static inline HRESULT IWMReaderStreamClock_SetTimer(IWMReaderStreamClock* This,QWORD when,void *param,DWORD *id) {
    return This->lpVtbl->SetTimer(This,when,param,id);
}
static inline HRESULT IWMReaderStreamClock_KillTimer(IWMReaderStreamClock* This,DWORD id) {
    return This->lpVtbl->KillTimer(This,id);
}
#endif
#endif

#endif


#endif  /* __IWMReaderStreamClock_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPacketSize interface
 */
#ifndef __IWMPacketSize_INTERFACE_DEFINED__
#define __IWMPacketSize_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPacketSize, 0xcdfb97ab, 0x188f, 0x40b3, 0xb6,0x43, 0x5b,0x79,0x03,0x97,0x5c,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cdfb97ab-188f-40b3-b643-5b7903975c59")
IWMPacketSize : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMaxPacketSize(
        DWORD *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMaxPacketSize(
        DWORD size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPacketSize, 0xcdfb97ab, 0x188f, 0x40b3, 0xb6,0x43, 0x5b,0x79,0x03,0x97,0x5c,0x59)
#endif
#else
typedef struct IWMPacketSizeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPacketSize *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPacketSize *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPacketSize *This);

    /*** IWMPacketSize methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMaxPacketSize)(
        IWMPacketSize *This,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *SetMaxPacketSize)(
        IWMPacketSize *This,
        DWORD size);

    END_INTERFACE
} IWMPacketSizeVtbl;

interface IWMPacketSize {
    CONST_VTBL IWMPacketSizeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPacketSize_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPacketSize_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPacketSize_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPacketSize methods ***/
#define IWMPacketSize_GetMaxPacketSize(This,size) (This)->lpVtbl->GetMaxPacketSize(This,size)
#define IWMPacketSize_SetMaxPacketSize(This,size) (This)->lpVtbl->SetMaxPacketSize(This,size)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPacketSize_QueryInterface(IWMPacketSize* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPacketSize_AddRef(IWMPacketSize* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPacketSize_Release(IWMPacketSize* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPacketSize methods ***/
static inline HRESULT IWMPacketSize_GetMaxPacketSize(IWMPacketSize* This,DWORD *size) {
    return This->lpVtbl->GetMaxPacketSize(This,size);
}
static inline HRESULT IWMPacketSize_SetMaxPacketSize(IWMPacketSize* This,DWORD size) {
    return This->lpVtbl->SetMaxPacketSize(This,size);
}
#endif
#endif

#endif


#endif  /* __IWMPacketSize_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPacketSize2 interface
 */
#ifndef __IWMPacketSize2_INTERFACE_DEFINED__
#define __IWMPacketSize2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPacketSize2, 0x8bfc2b9e, 0xb646, 0x4233, 0xa8,0x77, 0x1c,0x6a,0x07,0x96,0x69,0xdc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8bfc2b9e-b646-4233-a877-1c6a079669dc")
IWMPacketSize2 : public IWMPacketSize
{
    virtual HRESULT STDMETHODCALLTYPE GetMinPacketSize(
        DWORD *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMinPacketSize(
        DWORD size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPacketSize2, 0x8bfc2b9e, 0xb646, 0x4233, 0xa8,0x77, 0x1c,0x6a,0x07,0x96,0x69,0xdc)
#endif
#else
typedef struct IWMPacketSize2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPacketSize2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPacketSize2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPacketSize2 *This);

    /*** IWMPacketSize methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMaxPacketSize)(
        IWMPacketSize2 *This,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *SetMaxPacketSize)(
        IWMPacketSize2 *This,
        DWORD size);

    /*** IWMPacketSize2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMinPacketSize)(
        IWMPacketSize2 *This,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *SetMinPacketSize)(
        IWMPacketSize2 *This,
        DWORD size);

    END_INTERFACE
} IWMPacketSize2Vtbl;

interface IWMPacketSize2 {
    CONST_VTBL IWMPacketSize2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPacketSize2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPacketSize2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPacketSize2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPacketSize methods ***/
#define IWMPacketSize2_GetMaxPacketSize(This,size) (This)->lpVtbl->GetMaxPacketSize(This,size)
#define IWMPacketSize2_SetMaxPacketSize(This,size) (This)->lpVtbl->SetMaxPacketSize(This,size)
/*** IWMPacketSize2 methods ***/
#define IWMPacketSize2_GetMinPacketSize(This,size) (This)->lpVtbl->GetMinPacketSize(This,size)
#define IWMPacketSize2_SetMinPacketSize(This,size) (This)->lpVtbl->SetMinPacketSize(This,size)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPacketSize2_QueryInterface(IWMPacketSize2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPacketSize2_AddRef(IWMPacketSize2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPacketSize2_Release(IWMPacketSize2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPacketSize methods ***/
static inline HRESULT IWMPacketSize2_GetMaxPacketSize(IWMPacketSize2* This,DWORD *size) {
    return This->lpVtbl->GetMaxPacketSize(This,size);
}
static inline HRESULT IWMPacketSize2_SetMaxPacketSize(IWMPacketSize2* This,DWORD size) {
    return This->lpVtbl->SetMaxPacketSize(This,size);
}
/*** IWMPacketSize2 methods ***/
static inline HRESULT IWMPacketSize2_GetMinPacketSize(IWMPacketSize2* This,DWORD *size) {
    return This->lpVtbl->GetMinPacketSize(This,size);
}
static inline HRESULT IWMPacketSize2_SetMinPacketSize(IWMPacketSize2* This,DWORD size) {
    return This->lpVtbl->SetMinPacketSize(This,size);
}
#endif
#endif

#endif


#endif  /* __IWMPacketSize2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMDRMReader interface
 */
#ifndef __IWMDRMReader_INTERFACE_DEFINED__
#define __IWMDRMReader_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMDRMReader, 0xd2827540, 0x3ee7, 0x432c, 0xb1,0x4c, 0xdc,0x17,0xf0,0x85,0xd3,0xb3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d2827540-3ee7-432c-b14c-dc17f085d3b3")
IWMDRMReader : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AcquireLicense(
        DWORD flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelLicenseAcquisition(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Individualize(
        DWORD flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelIndividualization(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE MonitorLicenseAcquisition(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelMonitorLicenseAcquisition(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDRMProperty(
        const WCHAR *name,
        WMT_ATTR_DATATYPE type,
        const BYTE *value,
        WORD length) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDRMProperty(
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMDRMReader, 0xd2827540, 0x3ee7, 0x432c, 0xb1,0x4c, 0xdc,0x17,0xf0,0x85,0xd3,0xb3)
#endif
#else
typedef struct IWMDRMReaderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMDRMReader *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMDRMReader *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMDRMReader *This);

    /*** IWMDRMReader methods ***/
    HRESULT (STDMETHODCALLTYPE *AcquireLicense)(
        IWMDRMReader *This,
        DWORD flags);

    HRESULT (STDMETHODCALLTYPE *CancelLicenseAcquisition)(
        IWMDRMReader *This);

    HRESULT (STDMETHODCALLTYPE *Individualize)(
        IWMDRMReader *This,
        DWORD flags);

    HRESULT (STDMETHODCALLTYPE *CancelIndividualization)(
        IWMDRMReader *This);

    HRESULT (STDMETHODCALLTYPE *MonitorLicenseAcquisition)(
        IWMDRMReader *This);

    HRESULT (STDMETHODCALLTYPE *CancelMonitorLicenseAcquisition)(
        IWMDRMReader *This);

    HRESULT (STDMETHODCALLTYPE *SetDRMProperty)(
        IWMDRMReader *This,
        const WCHAR *name,
        WMT_ATTR_DATATYPE type,
        const BYTE *value,
        WORD length);

    HRESULT (STDMETHODCALLTYPE *GetDRMProperty)(
        IWMDRMReader *This,
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length);

    END_INTERFACE
} IWMDRMReaderVtbl;

interface IWMDRMReader {
    CONST_VTBL IWMDRMReaderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMDRMReader_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMDRMReader_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMDRMReader_Release(This) (This)->lpVtbl->Release(This)
/*** IWMDRMReader methods ***/
#define IWMDRMReader_AcquireLicense(This,flags) (This)->lpVtbl->AcquireLicense(This,flags)
#define IWMDRMReader_CancelLicenseAcquisition(This) (This)->lpVtbl->CancelLicenseAcquisition(This)
#define IWMDRMReader_Individualize(This,flags) (This)->lpVtbl->Individualize(This,flags)
#define IWMDRMReader_CancelIndividualization(This) (This)->lpVtbl->CancelIndividualization(This)
#define IWMDRMReader_MonitorLicenseAcquisition(This) (This)->lpVtbl->MonitorLicenseAcquisition(This)
#define IWMDRMReader_CancelMonitorLicenseAcquisition(This) (This)->lpVtbl->CancelMonitorLicenseAcquisition(This)
#define IWMDRMReader_SetDRMProperty(This,name,type,value,length) (This)->lpVtbl->SetDRMProperty(This,name,type,value,length)
#define IWMDRMReader_GetDRMProperty(This,name,type,value,length) (This)->lpVtbl->GetDRMProperty(This,name,type,value,length)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMDRMReader_QueryInterface(IWMDRMReader* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMDRMReader_AddRef(IWMDRMReader* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMDRMReader_Release(IWMDRMReader* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMDRMReader methods ***/
static inline HRESULT IWMDRMReader_AcquireLicense(IWMDRMReader* This,DWORD flags) {
    return This->lpVtbl->AcquireLicense(This,flags);
}
static inline HRESULT IWMDRMReader_CancelLicenseAcquisition(IWMDRMReader* This) {
    return This->lpVtbl->CancelLicenseAcquisition(This);
}
static inline HRESULT IWMDRMReader_Individualize(IWMDRMReader* This,DWORD flags) {
    return This->lpVtbl->Individualize(This,flags);
}
static inline HRESULT IWMDRMReader_CancelIndividualization(IWMDRMReader* This) {
    return This->lpVtbl->CancelIndividualization(This);
}
static inline HRESULT IWMDRMReader_MonitorLicenseAcquisition(IWMDRMReader* This) {
    return This->lpVtbl->MonitorLicenseAcquisition(This);
}
static inline HRESULT IWMDRMReader_CancelMonitorLicenseAcquisition(IWMDRMReader* This) {
    return This->lpVtbl->CancelMonitorLicenseAcquisition(This);
}
static inline HRESULT IWMDRMReader_SetDRMProperty(IWMDRMReader* This,const WCHAR *name,WMT_ATTR_DATATYPE type,const BYTE *value,WORD length) {
    return This->lpVtbl->SetDRMProperty(This,name,type,value,length);
}
static inline HRESULT IWMDRMReader_GetDRMProperty(IWMDRMReader* This,const WCHAR *name,WMT_ATTR_DATATYPE *type,BYTE *value,WORD *length) {
    return This->lpVtbl->GetDRMProperty(This,name,type,value,length);
}
#endif
#endif

#endif


#endif  /* __IWMDRMReader_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMDRMReader2 interface
 */
#ifndef __IWMDRMReader2_INTERFACE_DEFINED__
#define __IWMDRMReader2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMDRMReader2, 0xbefe7a75, 0x9f1d, 0x4075, 0xb9,0xd9, 0xa3,0xc3,0x7b,0xda,0x49,0xa0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("befe7a75-9f1d-4075-b9d9-a3c37bda49a0")
IWMDRMReader2 : public IWMDRMReader
{
    virtual HRESULT STDMETHODCALLTYPE SetEvaluateOutputLevelLicenses(
        WINBOOL evaluate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPlayOutputLevels(
        DRM_PLAY_OPL *play,
        DWORD *length,
        DWORD *level) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCopyOutputLevels(
        DRM_COPY_OPL *copy,
        DWORD *length,
        DWORD *level) = 0;

    virtual HRESULT STDMETHODCALLTYPE TryNextLicense(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMDRMReader2, 0xbefe7a75, 0x9f1d, 0x4075, 0xb9,0xd9, 0xa3,0xc3,0x7b,0xda,0x49,0xa0)
#endif
#else
typedef struct IWMDRMReader2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMDRMReader2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMDRMReader2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMDRMReader2 *This);

    /*** IWMDRMReader methods ***/
    HRESULT (STDMETHODCALLTYPE *AcquireLicense)(
        IWMDRMReader2 *This,
        DWORD flags);

    HRESULT (STDMETHODCALLTYPE *CancelLicenseAcquisition)(
        IWMDRMReader2 *This);

    HRESULT (STDMETHODCALLTYPE *Individualize)(
        IWMDRMReader2 *This,
        DWORD flags);

    HRESULT (STDMETHODCALLTYPE *CancelIndividualization)(
        IWMDRMReader2 *This);

    HRESULT (STDMETHODCALLTYPE *MonitorLicenseAcquisition)(
        IWMDRMReader2 *This);

    HRESULT (STDMETHODCALLTYPE *CancelMonitorLicenseAcquisition)(
        IWMDRMReader2 *This);

    HRESULT (STDMETHODCALLTYPE *SetDRMProperty)(
        IWMDRMReader2 *This,
        const WCHAR *name,
        WMT_ATTR_DATATYPE type,
        const BYTE *value,
        WORD length);

    HRESULT (STDMETHODCALLTYPE *GetDRMProperty)(
        IWMDRMReader2 *This,
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length);

    /*** IWMDRMReader2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetEvaluateOutputLevelLicenses)(
        IWMDRMReader2 *This,
        WINBOOL evaluate);

    HRESULT (STDMETHODCALLTYPE *GetPlayOutputLevels)(
        IWMDRMReader2 *This,
        DRM_PLAY_OPL *play,
        DWORD *length,
        DWORD *level);

    HRESULT (STDMETHODCALLTYPE *GetCopyOutputLevels)(
        IWMDRMReader2 *This,
        DRM_COPY_OPL *copy,
        DWORD *length,
        DWORD *level);

    HRESULT (STDMETHODCALLTYPE *TryNextLicense)(
        IWMDRMReader2 *This);

    END_INTERFACE
} IWMDRMReader2Vtbl;

interface IWMDRMReader2 {
    CONST_VTBL IWMDRMReader2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMDRMReader2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMDRMReader2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMDRMReader2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMDRMReader methods ***/
#define IWMDRMReader2_AcquireLicense(This,flags) (This)->lpVtbl->AcquireLicense(This,flags)
#define IWMDRMReader2_CancelLicenseAcquisition(This) (This)->lpVtbl->CancelLicenseAcquisition(This)
#define IWMDRMReader2_Individualize(This,flags) (This)->lpVtbl->Individualize(This,flags)
#define IWMDRMReader2_CancelIndividualization(This) (This)->lpVtbl->CancelIndividualization(This)
#define IWMDRMReader2_MonitorLicenseAcquisition(This) (This)->lpVtbl->MonitorLicenseAcquisition(This)
#define IWMDRMReader2_CancelMonitorLicenseAcquisition(This) (This)->lpVtbl->CancelMonitorLicenseAcquisition(This)
#define IWMDRMReader2_SetDRMProperty(This,name,type,value,length) (This)->lpVtbl->SetDRMProperty(This,name,type,value,length)
#define IWMDRMReader2_GetDRMProperty(This,name,type,value,length) (This)->lpVtbl->GetDRMProperty(This,name,type,value,length)
/*** IWMDRMReader2 methods ***/
#define IWMDRMReader2_SetEvaluateOutputLevelLicenses(This,evaluate) (This)->lpVtbl->SetEvaluateOutputLevelLicenses(This,evaluate)
#define IWMDRMReader2_GetPlayOutputLevels(This,play,length,level) (This)->lpVtbl->GetPlayOutputLevels(This,play,length,level)
#define IWMDRMReader2_GetCopyOutputLevels(This,copy,length,level) (This)->lpVtbl->GetCopyOutputLevels(This,copy,length,level)
#define IWMDRMReader2_TryNextLicense(This) (This)->lpVtbl->TryNextLicense(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMDRMReader2_QueryInterface(IWMDRMReader2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMDRMReader2_AddRef(IWMDRMReader2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMDRMReader2_Release(IWMDRMReader2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMDRMReader methods ***/
static inline HRESULT IWMDRMReader2_AcquireLicense(IWMDRMReader2* This,DWORD flags) {
    return This->lpVtbl->AcquireLicense(This,flags);
}
static inline HRESULT IWMDRMReader2_CancelLicenseAcquisition(IWMDRMReader2* This) {
    return This->lpVtbl->CancelLicenseAcquisition(This);
}
static inline HRESULT IWMDRMReader2_Individualize(IWMDRMReader2* This,DWORD flags) {
    return This->lpVtbl->Individualize(This,flags);
}
static inline HRESULT IWMDRMReader2_CancelIndividualization(IWMDRMReader2* This) {
    return This->lpVtbl->CancelIndividualization(This);
}
static inline HRESULT IWMDRMReader2_MonitorLicenseAcquisition(IWMDRMReader2* This) {
    return This->lpVtbl->MonitorLicenseAcquisition(This);
}
static inline HRESULT IWMDRMReader2_CancelMonitorLicenseAcquisition(IWMDRMReader2* This) {
    return This->lpVtbl->CancelMonitorLicenseAcquisition(This);
}
static inline HRESULT IWMDRMReader2_SetDRMProperty(IWMDRMReader2* This,const WCHAR *name,WMT_ATTR_DATATYPE type,const BYTE *value,WORD length) {
    return This->lpVtbl->SetDRMProperty(This,name,type,value,length);
}
static inline HRESULT IWMDRMReader2_GetDRMProperty(IWMDRMReader2* This,const WCHAR *name,WMT_ATTR_DATATYPE *type,BYTE *value,WORD *length) {
    return This->lpVtbl->GetDRMProperty(This,name,type,value,length);
}
/*** IWMDRMReader2 methods ***/
static inline HRESULT IWMDRMReader2_SetEvaluateOutputLevelLicenses(IWMDRMReader2* This,WINBOOL evaluate) {
    return This->lpVtbl->SetEvaluateOutputLevelLicenses(This,evaluate);
}
static inline HRESULT IWMDRMReader2_GetPlayOutputLevels(IWMDRMReader2* This,DRM_PLAY_OPL *play,DWORD *length,DWORD *level) {
    return This->lpVtbl->GetPlayOutputLevels(This,play,length,level);
}
static inline HRESULT IWMDRMReader2_GetCopyOutputLevels(IWMDRMReader2* This,DRM_COPY_OPL *copy,DWORD *length,DWORD *level) {
    return This->lpVtbl->GetCopyOutputLevels(This,copy,length,level);
}
static inline HRESULT IWMDRMReader2_TryNextLicense(IWMDRMReader2* This) {
    return This->lpVtbl->TryNextLicense(This);
}
#endif
#endif

#endif


#endif  /* __IWMDRMReader2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMDRMReader3 interface
 */
#ifndef __IWMDRMReader3_INTERFACE_DEFINED__
#define __IWMDRMReader3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMDRMReader3, 0xe08672de, 0xf1e7, 0x4ff4, 0xa0,0xa3, 0xfc,0x4b,0x08,0xe4,0xca,0xf8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e08672de-f1e7-4ff4-a0a3-fc4b08e4caf8")
IWMDRMReader3 : public IWMDRMReader2
{
    virtual HRESULT STDMETHODCALLTYPE GetInclusionList(
        GUID **guids,
        DWORD *count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMDRMReader3, 0xe08672de, 0xf1e7, 0x4ff4, 0xa0,0xa3, 0xfc,0x4b,0x08,0xe4,0xca,0xf8)
#endif
#else
typedef struct IWMDRMReader3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMDRMReader3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMDRMReader3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMDRMReader3 *This);

    /*** IWMDRMReader methods ***/
    HRESULT (STDMETHODCALLTYPE *AcquireLicense)(
        IWMDRMReader3 *This,
        DWORD flags);

    HRESULT (STDMETHODCALLTYPE *CancelLicenseAcquisition)(
        IWMDRMReader3 *This);

    HRESULT (STDMETHODCALLTYPE *Individualize)(
        IWMDRMReader3 *This,
        DWORD flags);

    HRESULT (STDMETHODCALLTYPE *CancelIndividualization)(
        IWMDRMReader3 *This);

    HRESULT (STDMETHODCALLTYPE *MonitorLicenseAcquisition)(
        IWMDRMReader3 *This);

    HRESULT (STDMETHODCALLTYPE *CancelMonitorLicenseAcquisition)(
        IWMDRMReader3 *This);

    HRESULT (STDMETHODCALLTYPE *SetDRMProperty)(
        IWMDRMReader3 *This,
        const WCHAR *name,
        WMT_ATTR_DATATYPE type,
        const BYTE *value,
        WORD length);

    HRESULT (STDMETHODCALLTYPE *GetDRMProperty)(
        IWMDRMReader3 *This,
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        WORD *length);

    /*** IWMDRMReader2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetEvaluateOutputLevelLicenses)(
        IWMDRMReader3 *This,
        WINBOOL evaluate);

    HRESULT (STDMETHODCALLTYPE *GetPlayOutputLevels)(
        IWMDRMReader3 *This,
        DRM_PLAY_OPL *play,
        DWORD *length,
        DWORD *level);

    HRESULT (STDMETHODCALLTYPE *GetCopyOutputLevels)(
        IWMDRMReader3 *This,
        DRM_COPY_OPL *copy,
        DWORD *length,
        DWORD *level);

    HRESULT (STDMETHODCALLTYPE *TryNextLicense)(
        IWMDRMReader3 *This);

    /*** IWMDRMReader3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetInclusionList)(
        IWMDRMReader3 *This,
        GUID **guids,
        DWORD *count);

    END_INTERFACE
} IWMDRMReader3Vtbl;

interface IWMDRMReader3 {
    CONST_VTBL IWMDRMReader3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMDRMReader3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMDRMReader3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMDRMReader3_Release(This) (This)->lpVtbl->Release(This)
/*** IWMDRMReader methods ***/
#define IWMDRMReader3_AcquireLicense(This,flags) (This)->lpVtbl->AcquireLicense(This,flags)
#define IWMDRMReader3_CancelLicenseAcquisition(This) (This)->lpVtbl->CancelLicenseAcquisition(This)
#define IWMDRMReader3_Individualize(This,flags) (This)->lpVtbl->Individualize(This,flags)
#define IWMDRMReader3_CancelIndividualization(This) (This)->lpVtbl->CancelIndividualization(This)
#define IWMDRMReader3_MonitorLicenseAcquisition(This) (This)->lpVtbl->MonitorLicenseAcquisition(This)
#define IWMDRMReader3_CancelMonitorLicenseAcquisition(This) (This)->lpVtbl->CancelMonitorLicenseAcquisition(This)
#define IWMDRMReader3_SetDRMProperty(This,name,type,value,length) (This)->lpVtbl->SetDRMProperty(This,name,type,value,length)
#define IWMDRMReader3_GetDRMProperty(This,name,type,value,length) (This)->lpVtbl->GetDRMProperty(This,name,type,value,length)
/*** IWMDRMReader2 methods ***/
#define IWMDRMReader3_SetEvaluateOutputLevelLicenses(This,evaluate) (This)->lpVtbl->SetEvaluateOutputLevelLicenses(This,evaluate)
#define IWMDRMReader3_GetPlayOutputLevels(This,play,length,level) (This)->lpVtbl->GetPlayOutputLevels(This,play,length,level)
#define IWMDRMReader3_GetCopyOutputLevels(This,copy,length,level) (This)->lpVtbl->GetCopyOutputLevels(This,copy,length,level)
#define IWMDRMReader3_TryNextLicense(This) (This)->lpVtbl->TryNextLicense(This)
/*** IWMDRMReader3 methods ***/
#define IWMDRMReader3_GetInclusionList(This,guids,count) (This)->lpVtbl->GetInclusionList(This,guids,count)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMDRMReader3_QueryInterface(IWMDRMReader3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMDRMReader3_AddRef(IWMDRMReader3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMDRMReader3_Release(IWMDRMReader3* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMDRMReader methods ***/
static inline HRESULT IWMDRMReader3_AcquireLicense(IWMDRMReader3* This,DWORD flags) {
    return This->lpVtbl->AcquireLicense(This,flags);
}
static inline HRESULT IWMDRMReader3_CancelLicenseAcquisition(IWMDRMReader3* This) {
    return This->lpVtbl->CancelLicenseAcquisition(This);
}
static inline HRESULT IWMDRMReader3_Individualize(IWMDRMReader3* This,DWORD flags) {
    return This->lpVtbl->Individualize(This,flags);
}
static inline HRESULT IWMDRMReader3_CancelIndividualization(IWMDRMReader3* This) {
    return This->lpVtbl->CancelIndividualization(This);
}
static inline HRESULT IWMDRMReader3_MonitorLicenseAcquisition(IWMDRMReader3* This) {
    return This->lpVtbl->MonitorLicenseAcquisition(This);
}
static inline HRESULT IWMDRMReader3_CancelMonitorLicenseAcquisition(IWMDRMReader3* This) {
    return This->lpVtbl->CancelMonitorLicenseAcquisition(This);
}
static inline HRESULT IWMDRMReader3_SetDRMProperty(IWMDRMReader3* This,const WCHAR *name,WMT_ATTR_DATATYPE type,const BYTE *value,WORD length) {
    return This->lpVtbl->SetDRMProperty(This,name,type,value,length);
}
static inline HRESULT IWMDRMReader3_GetDRMProperty(IWMDRMReader3* This,const WCHAR *name,WMT_ATTR_DATATYPE *type,BYTE *value,WORD *length) {
    return This->lpVtbl->GetDRMProperty(This,name,type,value,length);
}
/*** IWMDRMReader2 methods ***/
static inline HRESULT IWMDRMReader3_SetEvaluateOutputLevelLicenses(IWMDRMReader3* This,WINBOOL evaluate) {
    return This->lpVtbl->SetEvaluateOutputLevelLicenses(This,evaluate);
}
static inline HRESULT IWMDRMReader3_GetPlayOutputLevels(IWMDRMReader3* This,DRM_PLAY_OPL *play,DWORD *length,DWORD *level) {
    return This->lpVtbl->GetPlayOutputLevels(This,play,length,level);
}
static inline HRESULT IWMDRMReader3_GetCopyOutputLevels(IWMDRMReader3* This,DRM_COPY_OPL *copy,DWORD *length,DWORD *level) {
    return This->lpVtbl->GetCopyOutputLevels(This,copy,length,level);
}
static inline HRESULT IWMDRMReader3_TryNextLicense(IWMDRMReader3* This) {
    return This->lpVtbl->TryNextLicense(This);
}
/*** IWMDRMReader3 methods ***/
static inline HRESULT IWMDRMReader3_GetInclusionList(IWMDRMReader3* This,GUID **guids,DWORD *count) {
    return This->lpVtbl->GetInclusionList(This,guids,count);
}
#endif
#endif

#endif


#endif  /* __IWMDRMReader3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderAccelerator interface
 */
#ifndef __IWMReaderAccelerator_INTERFACE_DEFINED__
#define __IWMReaderAccelerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderAccelerator, 0xbddc4d08, 0x944d, 0x4d52, 0xa6,0x12, 0x46,0xc3,0xfd,0xa0,0x7d,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bddc4d08-944d-4d52-a612-46c3fda07dd4")
IWMReaderAccelerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCodecInterface(
        DWORD output,
        REFIID riid,
        void **codec) = 0;

    virtual HRESULT STDMETHODCALLTYPE Notify(
        DWORD output,
        WM_MEDIA_TYPE *subtype) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderAccelerator, 0xbddc4d08, 0x944d, 0x4d52, 0xa6,0x12, 0x46,0xc3,0xfd,0xa0,0x7d,0xd4)
#endif
#else
typedef struct IWMReaderAcceleratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderAccelerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderAccelerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderAccelerator *This);

    /*** IWMReaderAccelerator methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCodecInterface)(
        IWMReaderAccelerator *This,
        DWORD output,
        REFIID riid,
        void **codec);

    HRESULT (STDMETHODCALLTYPE *Notify)(
        IWMReaderAccelerator *This,
        DWORD output,
        WM_MEDIA_TYPE *subtype);

    END_INTERFACE
} IWMReaderAcceleratorVtbl;

interface IWMReaderAccelerator {
    CONST_VTBL IWMReaderAcceleratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderAccelerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderAccelerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderAccelerator_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderAccelerator methods ***/
#define IWMReaderAccelerator_GetCodecInterface(This,output,riid,codec) (This)->lpVtbl->GetCodecInterface(This,output,riid,codec)
#define IWMReaderAccelerator_Notify(This,output,subtype) (This)->lpVtbl->Notify(This,output,subtype)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderAccelerator_QueryInterface(IWMReaderAccelerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderAccelerator_AddRef(IWMReaderAccelerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderAccelerator_Release(IWMReaderAccelerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderAccelerator methods ***/
static inline HRESULT IWMReaderAccelerator_GetCodecInterface(IWMReaderAccelerator* This,DWORD output,REFIID riid,void **codec) {
    return This->lpVtbl->GetCodecInterface(This,output,riid,codec);
}
static inline HRESULT IWMReaderAccelerator_Notify(IWMReaderAccelerator* This,DWORD output,WM_MEDIA_TYPE *subtype) {
    return This->lpVtbl->Notify(This,output,subtype);
}
#endif
#endif

#endif


#endif  /* __IWMReaderAccelerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderTimecode interface
 */
#ifndef __IWMReaderTimecode_INTERFACE_DEFINED__
#define __IWMReaderTimecode_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderTimecode, 0xf369e2f0, 0xe081, 0x4fe6, 0x84,0x50, 0xb8,0x10,0xb2,0xf4,0x10,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f369e2f0-e081-4fe6-8450-b810b2f410d1")
IWMReaderTimecode : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetTimecodeRangeCount(
        WORD num,
        WORD *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimecodeRangeBounds(
        WORD stream,
        WORD range,
        DWORD *start_timecode,
        DWORD *end_timecode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderTimecode, 0xf369e2f0, 0xe081, 0x4fe6, 0x84,0x50, 0xb8,0x10,0xb2,0xf4,0x10,0xd1)
#endif
#else
typedef struct IWMReaderTimecodeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderTimecode *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderTimecode *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderTimecode *This);

    /*** IWMReaderTimecode methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTimecodeRangeCount)(
        IWMReaderTimecode *This,
        WORD num,
        WORD *count);

    HRESULT (STDMETHODCALLTYPE *GetTimecodeRangeBounds)(
        IWMReaderTimecode *This,
        WORD stream,
        WORD range,
        DWORD *start_timecode,
        DWORD *end_timecode);

    END_INTERFACE
} IWMReaderTimecodeVtbl;

interface IWMReaderTimecode {
    CONST_VTBL IWMReaderTimecodeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderTimecode_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderTimecode_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderTimecode_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderTimecode methods ***/
#define IWMReaderTimecode_GetTimecodeRangeCount(This,num,count) (This)->lpVtbl->GetTimecodeRangeCount(This,num,count)
#define IWMReaderTimecode_GetTimecodeRangeBounds(This,stream,range,start_timecode,end_timecode) (This)->lpVtbl->GetTimecodeRangeBounds(This,stream,range,start_timecode,end_timecode)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderTimecode_QueryInterface(IWMReaderTimecode* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderTimecode_AddRef(IWMReaderTimecode* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderTimecode_Release(IWMReaderTimecode* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderTimecode methods ***/
static inline HRESULT IWMReaderTimecode_GetTimecodeRangeCount(IWMReaderTimecode* This,WORD num,WORD *count) {
    return This->lpVtbl->GetTimecodeRangeCount(This,num,count);
}
static inline HRESULT IWMReaderTimecode_GetTimecodeRangeBounds(IWMReaderTimecode* This,WORD stream,WORD range,DWORD *start_timecode,DWORD *end_timecode) {
    return This->lpVtbl->GetTimecodeRangeBounds(This,stream,range,start_timecode,end_timecode);
}
#endif
#endif

#endif


#endif  /* __IWMReaderTimecode_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderTypeNegotiation interface
 */
#ifndef __IWMReaderTypeNegotiation_INTERFACE_DEFINED__
#define __IWMReaderTypeNegotiation_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderTypeNegotiation, 0xfdbe5592, 0x81a1, 0x41ea, 0x93,0xbd, 0x73,0x5c,0xad,0x1a,0xdc,0x05);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fdbe5592-81a1-41ea-93bd-735cad1adc05")
IWMReaderTypeNegotiation : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE TryOutputProps(
        DWORD output,
        IWMOutputMediaProps *props) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderTypeNegotiation, 0xfdbe5592, 0x81a1, 0x41ea, 0x93,0xbd, 0x73,0x5c,0xad,0x1a,0xdc,0x05)
#endif
#else
typedef struct IWMReaderTypeNegotiationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderTypeNegotiation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderTypeNegotiation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderTypeNegotiation *This);

    /*** IWMReaderTypeNegotiation methods ***/
    HRESULT (STDMETHODCALLTYPE *TryOutputProps)(
        IWMReaderTypeNegotiation *This,
        DWORD output,
        IWMOutputMediaProps *props);

    END_INTERFACE
} IWMReaderTypeNegotiationVtbl;

interface IWMReaderTypeNegotiation {
    CONST_VTBL IWMReaderTypeNegotiationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderTypeNegotiation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderTypeNegotiation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderTypeNegotiation_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderTypeNegotiation methods ***/
#define IWMReaderTypeNegotiation_TryOutputProps(This,output,props) (This)->lpVtbl->TryOutputProps(This,output,props)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderTypeNegotiation_QueryInterface(IWMReaderTypeNegotiation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderTypeNegotiation_AddRef(IWMReaderTypeNegotiation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderTypeNegotiation_Release(IWMReaderTypeNegotiation* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderTypeNegotiation methods ***/
static inline HRESULT IWMReaderTypeNegotiation_TryOutputProps(IWMReaderTypeNegotiation* This,DWORD output,IWMOutputMediaProps *props) {
    return This->lpVtbl->TryOutputProps(This,output,props);
}
#endif
#endif

#endif


#endif  /* __IWMReaderTypeNegotiation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMLanguageList interface
 */
#ifndef __IWMLanguageList_INTERFACE_DEFINED__
#define __IWMLanguageList_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMLanguageList, 0xdf683f00, 0x2d49, 0x4d8e, 0x92,0xb7, 0xfb,0x19,0xf6,0xa0,0xdc,0x57);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("df683f00-2d49-4d8e-92b7-fb19f6a0dc57")
IWMLanguageList : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetLanguageCount(
        WORD *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLanguageDetails(
        WORD index,
        WCHAR *language,
        WORD *length) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddLanguageByRFC1766String(
        LPCWSTR_WMSDK_TYPE_SAFE language,
        WORD *index) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMLanguageList, 0xdf683f00, 0x2d49, 0x4d8e, 0x92,0xb7, 0xfb,0x19,0xf6,0xa0,0xdc,0x57)
#endif
#else
typedef struct IWMLanguageListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMLanguageList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMLanguageList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMLanguageList *This);

    /*** IWMLanguageList methods ***/
    HRESULT (STDMETHODCALLTYPE *GetLanguageCount)(
        IWMLanguageList *This,
        WORD *count);

    HRESULT (STDMETHODCALLTYPE *GetLanguageDetails)(
        IWMLanguageList *This,
        WORD index,
        WCHAR *language,
        WORD *length);

    HRESULT (STDMETHODCALLTYPE *AddLanguageByRFC1766String)(
        IWMLanguageList *This,
        LPCWSTR_WMSDK_TYPE_SAFE language,
        WORD *index);

    END_INTERFACE
} IWMLanguageListVtbl;

interface IWMLanguageList {
    CONST_VTBL IWMLanguageListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMLanguageList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMLanguageList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMLanguageList_Release(This) (This)->lpVtbl->Release(This)
/*** IWMLanguageList methods ***/
#define IWMLanguageList_GetLanguageCount(This,count) (This)->lpVtbl->GetLanguageCount(This,count)
#define IWMLanguageList_GetLanguageDetails(This,index,language,length) (This)->lpVtbl->GetLanguageDetails(This,index,language,length)
#define IWMLanguageList_AddLanguageByRFC1766String(This,language,index) (This)->lpVtbl->AddLanguageByRFC1766String(This,language,index)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMLanguageList_QueryInterface(IWMLanguageList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMLanguageList_AddRef(IWMLanguageList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMLanguageList_Release(IWMLanguageList* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMLanguageList methods ***/
static inline HRESULT IWMLanguageList_GetLanguageCount(IWMLanguageList* This,WORD *count) {
    return This->lpVtbl->GetLanguageCount(This,count);
}
static inline HRESULT IWMLanguageList_GetLanguageDetails(IWMLanguageList* This,WORD index,WCHAR *language,WORD *length) {
    return This->lpVtbl->GetLanguageDetails(This,index,language,length);
}
static inline HRESULT IWMLanguageList_AddLanguageByRFC1766String(IWMLanguageList* This,LPCWSTR_WMSDK_TYPE_SAFE language,WORD *index) {
    return This->lpVtbl->AddLanguageByRFC1766String(This,language,index);
}
#endif
#endif

#endif


#endif  /* __IWMLanguageList_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMReaderPlaylistBurn interface
 */
#ifndef __IWMReaderPlaylistBurn_INTERFACE_DEFINED__
#define __IWMReaderPlaylistBurn_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMReaderPlaylistBurn, 0xf28c0300, 0x9baa, 0x4477, 0xa8,0x46, 0x17,0x44,0xd9,0xcb,0xf5,0x33);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f28c0300-9baa-4477-a846-1744d9cbf533")
IWMReaderPlaylistBurn : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE InitPlaylistBurn(
        DWORD count,
        LPCWSTR_WMSDK_TYPE_SAFE *filenames,
        IWMStatusCallback *callback,
        void *context) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInitResults(
        DWORD count,
        HRESULT *stat) = 0;

    virtual HRESULT STDMETHODCALLTYPE Cancel(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndPlaylistBurn(
        HRESULT result) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMReaderPlaylistBurn, 0xf28c0300, 0x9baa, 0x4477, 0xa8,0x46, 0x17,0x44,0xd9,0xcb,0xf5,0x33)
#endif
#else
typedef struct IWMReaderPlaylistBurnVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMReaderPlaylistBurn *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMReaderPlaylistBurn *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMReaderPlaylistBurn *This);

    /*** IWMReaderPlaylistBurn methods ***/
    HRESULT (STDMETHODCALLTYPE *InitPlaylistBurn)(
        IWMReaderPlaylistBurn *This,
        DWORD count,
        LPCWSTR_WMSDK_TYPE_SAFE *filenames,
        IWMStatusCallback *callback,
        void *context);

    HRESULT (STDMETHODCALLTYPE *GetInitResults)(
        IWMReaderPlaylistBurn *This,
        DWORD count,
        HRESULT *stat);

    HRESULT (STDMETHODCALLTYPE *Cancel)(
        IWMReaderPlaylistBurn *This);

    HRESULT (STDMETHODCALLTYPE *EndPlaylistBurn)(
        IWMReaderPlaylistBurn *This,
        HRESULT result);

    END_INTERFACE
} IWMReaderPlaylistBurnVtbl;

interface IWMReaderPlaylistBurn {
    CONST_VTBL IWMReaderPlaylistBurnVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMReaderPlaylistBurn_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMReaderPlaylistBurn_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMReaderPlaylistBurn_Release(This) (This)->lpVtbl->Release(This)
/*** IWMReaderPlaylistBurn methods ***/
#define IWMReaderPlaylistBurn_InitPlaylistBurn(This,count,filenames,callback,context) (This)->lpVtbl->InitPlaylistBurn(This,count,filenames,callback,context)
#define IWMReaderPlaylistBurn_GetInitResults(This,count,stat) (This)->lpVtbl->GetInitResults(This,count,stat)
#define IWMReaderPlaylistBurn_Cancel(This) (This)->lpVtbl->Cancel(This)
#define IWMReaderPlaylistBurn_EndPlaylistBurn(This,result) (This)->lpVtbl->EndPlaylistBurn(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMReaderPlaylistBurn_QueryInterface(IWMReaderPlaylistBurn* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMReaderPlaylistBurn_AddRef(IWMReaderPlaylistBurn* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMReaderPlaylistBurn_Release(IWMReaderPlaylistBurn* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMReaderPlaylistBurn methods ***/
static inline HRESULT IWMReaderPlaylistBurn_InitPlaylistBurn(IWMReaderPlaylistBurn* This,DWORD count,LPCWSTR_WMSDK_TYPE_SAFE *filenames,IWMStatusCallback *callback,void *context) {
    return This->lpVtbl->InitPlaylistBurn(This,count,filenames,callback,context);
}
static inline HRESULT IWMReaderPlaylistBurn_GetInitResults(IWMReaderPlaylistBurn* This,DWORD count,HRESULT *stat) {
    return This->lpVtbl->GetInitResults(This,count,stat);
}
static inline HRESULT IWMReaderPlaylistBurn_Cancel(IWMReaderPlaylistBurn* This) {
    return This->lpVtbl->Cancel(This);
}
static inline HRESULT IWMReaderPlaylistBurn_EndPlaylistBurn(IWMReaderPlaylistBurn* This,HRESULT result) {
    return This->lpVtbl->EndPlaylistBurn(This,result);
}
#endif
#endif

#endif


#endif  /* __IWMReaderPlaylistBurn_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPropertyVault interface
 */
#ifndef __IWMPropertyVault_INTERFACE_DEFINED__
#define __IWMPropertyVault_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPropertyVault, 0x72995a79, 0x5090, 0x42a4, 0x9c,0x8c, 0xd9,0xd0,0xb6,0xd3,0x4b,0xe5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("72995a79-5090-42a4-9c8c-d9d0b6d34be5")
IWMPropertyVault : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetPropertyCount(
        DWORD *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyByName(
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProperty(
        const WCHAR *name,
        WMT_ATTR_DATATYPE type,
        BYTE *value,
        DWORD size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertyByIndex(
        DWORD index,
        WCHAR *name,
        DWORD *length,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyPropertiesFrom(
        IWMPropertyVault *vault) = 0;

    virtual HRESULT STDMETHODCALLTYPE Clear(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPropertyVault, 0x72995a79, 0x5090, 0x42a4, 0x9c,0x8c, 0xd9,0xd0,0xb6,0xd3,0x4b,0xe5)
#endif
#else
typedef struct IWMPropertyVaultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPropertyVault *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPropertyVault *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPropertyVault *This);

    /*** IWMPropertyVault methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPropertyCount)(
        IWMPropertyVault *This,
        DWORD *count);

    HRESULT (STDMETHODCALLTYPE *GetPropertyByName)(
        IWMPropertyVault *This,
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *SetProperty)(
        IWMPropertyVault *This,
        const WCHAR *name,
        WMT_ATTR_DATATYPE type,
        BYTE *value,
        DWORD size);

    HRESULT (STDMETHODCALLTYPE *GetPropertyByIndex)(
        IWMPropertyVault *This,
        DWORD index,
        WCHAR *name,
        DWORD *length,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size);

    HRESULT (STDMETHODCALLTYPE *CopyPropertiesFrom)(
        IWMPropertyVault *This,
        IWMPropertyVault *vault);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        IWMPropertyVault *This);

    END_INTERFACE
} IWMPropertyVaultVtbl;

interface IWMPropertyVault {
    CONST_VTBL IWMPropertyVaultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPropertyVault_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPropertyVault_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPropertyVault_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPropertyVault methods ***/
#define IWMPropertyVault_GetPropertyCount(This,count) (This)->lpVtbl->GetPropertyCount(This,count)
#define IWMPropertyVault_GetPropertyByName(This,name,type,value,size) (This)->lpVtbl->GetPropertyByName(This,name,type,value,size)
#define IWMPropertyVault_SetProperty(This,name,type,value,size) (This)->lpVtbl->SetProperty(This,name,type,value,size)
#define IWMPropertyVault_GetPropertyByIndex(This,index,name,length,type,value,size) (This)->lpVtbl->GetPropertyByIndex(This,index,name,length,type,value,size)
#define IWMPropertyVault_CopyPropertiesFrom(This,vault) (This)->lpVtbl->CopyPropertiesFrom(This,vault)
#define IWMPropertyVault_Clear(This) (This)->lpVtbl->Clear(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPropertyVault_QueryInterface(IWMPropertyVault* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPropertyVault_AddRef(IWMPropertyVault* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPropertyVault_Release(IWMPropertyVault* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPropertyVault methods ***/
static inline HRESULT IWMPropertyVault_GetPropertyCount(IWMPropertyVault* This,DWORD *count) {
    return This->lpVtbl->GetPropertyCount(This,count);
}
static inline HRESULT IWMPropertyVault_GetPropertyByName(IWMPropertyVault* This,const WCHAR *name,WMT_ATTR_DATATYPE *type,BYTE *value,DWORD *size) {
    return This->lpVtbl->GetPropertyByName(This,name,type,value,size);
}
static inline HRESULT IWMPropertyVault_SetProperty(IWMPropertyVault* This,const WCHAR *name,WMT_ATTR_DATATYPE type,BYTE *value,DWORD size) {
    return This->lpVtbl->SetProperty(This,name,type,value,size);
}
static inline HRESULT IWMPropertyVault_GetPropertyByIndex(IWMPropertyVault* This,DWORD index,WCHAR *name,DWORD *length,WMT_ATTR_DATATYPE *type,BYTE *value,DWORD *size) {
    return This->lpVtbl->GetPropertyByIndex(This,index,name,length,type,value,size);
}
static inline HRESULT IWMPropertyVault_CopyPropertiesFrom(IWMPropertyVault* This,IWMPropertyVault *vault) {
    return This->lpVtbl->CopyPropertiesFrom(This,vault);
}
static inline HRESULT IWMPropertyVault_Clear(IWMPropertyVault* This) {
    return This->lpVtbl->Clear(This);
}
#endif
#endif

#endif


#endif  /* __IWMPropertyVault_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMWriterPreprocess interface
 */
#ifndef __IWMWriterPreprocess_INTERFACE_DEFINED__
#define __IWMWriterPreprocess_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMWriterPreprocess, 0xfc54a285, 0x38c4, 0x45b5, 0xaa,0x23, 0x85,0xb9,0xf7,0xcb,0x42,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fc54a285-38c4-45b5-aa23-85b9f7cb424b")
IWMWriterPreprocess : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMaxPreprocessingPasses(
        DWORD input,
        DWORD flags,
        DWORD *passes) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetNumPreprocessingPasses(
        DWORD input,
        DWORD flags,
        DWORD passes) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginPreprocessingPass(
        DWORD input,
        DWORD flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE PreprocessSample(
        DWORD input,
        QWORD sample_time,
        DWORD flags,
        INSSBuffer *sample) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndPreprocessingPass(
        DWORD input,
        DWORD flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMWriterPreprocess, 0xfc54a285, 0x38c4, 0x45b5, 0xaa,0x23, 0x85,0xb9,0xf7,0xcb,0x42,0x4b)
#endif
#else
typedef struct IWMWriterPreprocessVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMWriterPreprocess *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMWriterPreprocess *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMWriterPreprocess *This);

    /*** IWMWriterPreprocess methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMaxPreprocessingPasses)(
        IWMWriterPreprocess *This,
        DWORD input,
        DWORD flags,
        DWORD *passes);

    HRESULT (STDMETHODCALLTYPE *SetNumPreprocessingPasses)(
        IWMWriterPreprocess *This,
        DWORD input,
        DWORD flags,
        DWORD passes);

    HRESULT (STDMETHODCALLTYPE *BeginPreprocessingPass)(
        IWMWriterPreprocess *This,
        DWORD input,
        DWORD flags);

    HRESULT (STDMETHODCALLTYPE *PreprocessSample)(
        IWMWriterPreprocess *This,
        DWORD input,
        QWORD sample_time,
        DWORD flags,
        INSSBuffer *sample);

    HRESULT (STDMETHODCALLTYPE *EndPreprocessingPass)(
        IWMWriterPreprocess *This,
        DWORD input,
        DWORD flags);

    END_INTERFACE
} IWMWriterPreprocessVtbl;

interface IWMWriterPreprocess {
    CONST_VTBL IWMWriterPreprocessVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMWriterPreprocess_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMWriterPreprocess_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMWriterPreprocess_Release(This) (This)->lpVtbl->Release(This)
/*** IWMWriterPreprocess methods ***/
#define IWMWriterPreprocess_GetMaxPreprocessingPasses(This,input,flags,passes) (This)->lpVtbl->GetMaxPreprocessingPasses(This,input,flags,passes)
#define IWMWriterPreprocess_SetNumPreprocessingPasses(This,input,flags,passes) (This)->lpVtbl->SetNumPreprocessingPasses(This,input,flags,passes)
#define IWMWriterPreprocess_BeginPreprocessingPass(This,input,flags) (This)->lpVtbl->BeginPreprocessingPass(This,input,flags)
#define IWMWriterPreprocess_PreprocessSample(This,input,sample_time,flags,sample) (This)->lpVtbl->PreprocessSample(This,input,sample_time,flags,sample)
#define IWMWriterPreprocess_EndPreprocessingPass(This,input,flags) (This)->lpVtbl->EndPreprocessingPass(This,input,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMWriterPreprocess_QueryInterface(IWMWriterPreprocess* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMWriterPreprocess_AddRef(IWMWriterPreprocess* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMWriterPreprocess_Release(IWMWriterPreprocess* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMWriterPreprocess methods ***/
static inline HRESULT IWMWriterPreprocess_GetMaxPreprocessingPasses(IWMWriterPreprocess* This,DWORD input,DWORD flags,DWORD *passes) {
    return This->lpVtbl->GetMaxPreprocessingPasses(This,input,flags,passes);
}
static inline HRESULT IWMWriterPreprocess_SetNumPreprocessingPasses(IWMWriterPreprocess* This,DWORD input,DWORD flags,DWORD passes) {
    return This->lpVtbl->SetNumPreprocessingPasses(This,input,flags,passes);
}
static inline HRESULT IWMWriterPreprocess_BeginPreprocessingPass(IWMWriterPreprocess* This,DWORD input,DWORD flags) {
    return This->lpVtbl->BeginPreprocessingPass(This,input,flags);
}
static inline HRESULT IWMWriterPreprocess_PreprocessSample(IWMWriterPreprocess* This,DWORD input,QWORD sample_time,DWORD flags,INSSBuffer *sample) {
    return This->lpVtbl->PreprocessSample(This,input,sample_time,flags,sample);
}
static inline HRESULT IWMWriterPreprocess_EndPreprocessingPass(IWMWriterPreprocess* This,DWORD input,DWORD flags) {
    return This->lpVtbl->EndPreprocessingPass(This,input,flags);
}
#endif
#endif

#endif


#endif  /* __IWMWriterPreprocess_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMLicenseBackup interface
 */
#ifndef __IWMLicenseBackup_INTERFACE_DEFINED__
#define __IWMLicenseBackup_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMLicenseBackup, 0x05e5ac9f, 0x3fb6, 0x4508, 0xbb,0x43, 0xa4,0x06,0x7b,0xa1,0xeb,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("05e5ac9f-3fb6-4508-bb43-a4067ba1ebe8")
IWMLicenseBackup : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE BackupLicenses(
        DWORD dwFlags,
        IWMStatusCallback *pCallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelLicenseBackup(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMLicenseBackup, 0x05e5ac9f, 0x3fb6, 0x4508, 0xbb,0x43, 0xa4,0x06,0x7b,0xa1,0xeb,0xe8)
#endif
#else
typedef struct IWMLicenseBackupVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMLicenseBackup *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMLicenseBackup *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMLicenseBackup *This);

    /*** IWMLicenseBackup methods ***/
    HRESULT (STDMETHODCALLTYPE *BackupLicenses)(
        IWMLicenseBackup *This,
        DWORD dwFlags,
        IWMStatusCallback *pCallback);

    HRESULT (STDMETHODCALLTYPE *CancelLicenseBackup)(
        IWMLicenseBackup *This);

    END_INTERFACE
} IWMLicenseBackupVtbl;

interface IWMLicenseBackup {
    CONST_VTBL IWMLicenseBackupVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMLicenseBackup_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMLicenseBackup_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMLicenseBackup_Release(This) (This)->lpVtbl->Release(This)
/*** IWMLicenseBackup methods ***/
#define IWMLicenseBackup_BackupLicenses(This,dwFlags,pCallback) (This)->lpVtbl->BackupLicenses(This,dwFlags,pCallback)
#define IWMLicenseBackup_CancelLicenseBackup(This) (This)->lpVtbl->CancelLicenseBackup(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMLicenseBackup_QueryInterface(IWMLicenseBackup* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMLicenseBackup_AddRef(IWMLicenseBackup* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMLicenseBackup_Release(IWMLicenseBackup* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMLicenseBackup methods ***/
static inline HRESULT IWMLicenseBackup_BackupLicenses(IWMLicenseBackup* This,DWORD dwFlags,IWMStatusCallback *pCallback) {
    return This->lpVtbl->BackupLicenses(This,dwFlags,pCallback);
}
static inline HRESULT IWMLicenseBackup_CancelLicenseBackup(IWMLicenseBackup* This) {
    return This->lpVtbl->CancelLicenseBackup(This);
}
#endif
#endif

#endif


#endif  /* __IWMLicenseBackup_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMLicenseRestore interface
 */
#ifndef __IWMLicenseRestore_INTERFACE_DEFINED__
#define __IWMLicenseRestore_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMLicenseRestore, 0xc70b6334, 0xa22e, 0x4efb, 0xa2,0x45, 0x15,0xe6,0x5a,0x00,0x4a,0x13);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c70b6334-a22e-4efb-a245-15e65a004a13")
IWMLicenseRestore : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE RestoreLicenses(
        DWORD dwFlags,
        IWMStatusCallback *pCallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE CancelLicenseRestore(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMLicenseRestore, 0xc70b6334, 0xa22e, 0x4efb, 0xa2,0x45, 0x15,0xe6,0x5a,0x00,0x4a,0x13)
#endif
#else
typedef struct IWMLicenseRestoreVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMLicenseRestore *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMLicenseRestore *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMLicenseRestore *This);

    /*** IWMLicenseRestore methods ***/
    HRESULT (STDMETHODCALLTYPE *RestoreLicenses)(
        IWMLicenseRestore *This,
        DWORD dwFlags,
        IWMStatusCallback *pCallback);

    HRESULT (STDMETHODCALLTYPE *CancelLicenseRestore)(
        IWMLicenseRestore *This);

    END_INTERFACE
} IWMLicenseRestoreVtbl;

interface IWMLicenseRestore {
    CONST_VTBL IWMLicenseRestoreVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMLicenseRestore_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMLicenseRestore_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMLicenseRestore_Release(This) (This)->lpVtbl->Release(This)
/*** IWMLicenseRestore methods ***/
#define IWMLicenseRestore_RestoreLicenses(This,dwFlags,pCallback) (This)->lpVtbl->RestoreLicenses(This,dwFlags,pCallback)
#define IWMLicenseRestore_CancelLicenseRestore(This) (This)->lpVtbl->CancelLicenseRestore(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMLicenseRestore_QueryInterface(IWMLicenseRestore* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMLicenseRestore_AddRef(IWMLicenseRestore* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMLicenseRestore_Release(IWMLicenseRestore* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMLicenseRestore methods ***/
static inline HRESULT IWMLicenseRestore_RestoreLicenses(IWMLicenseRestore* This,DWORD dwFlags,IWMStatusCallback *pCallback) {
    return This->lpVtbl->RestoreLicenses(This,dwFlags,pCallback);
}
static inline HRESULT IWMLicenseRestore_CancelLicenseRestore(IWMLicenseRestore* This) {
    return This->lpVtbl->CancelLicenseRestore(This);
}
#endif
#endif

#endif


#endif  /* __IWMLicenseRestore_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMIStreamProps interface
 */
#ifndef __IWMIStreamProps_INTERFACE_DEFINED__
#define __IWMIStreamProps_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMIStreamProps, 0x6816dad3, 0x2b4b, 0x4c8e, 0x81,0x49, 0x87,0x4c,0x34,0x83,0xa7,0x53);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6816dad3-2b4b-4c8e-8149-874c3483a753")
IWMIStreamProps : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetProperty(
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMIStreamProps, 0x6816dad3, 0x2b4b, 0x4c8e, 0x81,0x49, 0x87,0x4c,0x34,0x83,0xa7,0x53)
#endif
#else
typedef struct IWMIStreamPropsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMIStreamProps *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMIStreamProps *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMIStreamProps *This);

    /*** IWMIStreamProps methods ***/
    HRESULT (STDMETHODCALLTYPE *GetProperty)(
        IWMIStreamProps *This,
        const WCHAR *name,
        WMT_ATTR_DATATYPE *type,
        BYTE *value,
        DWORD *size);

    END_INTERFACE
} IWMIStreamPropsVtbl;

interface IWMIStreamProps {
    CONST_VTBL IWMIStreamPropsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMIStreamProps_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMIStreamProps_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMIStreamProps_Release(This) (This)->lpVtbl->Release(This)
/*** IWMIStreamProps methods ***/
#define IWMIStreamProps_GetProperty(This,name,type,value,size) (This)->lpVtbl->GetProperty(This,name,type,value,size)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMIStreamProps_QueryInterface(IWMIStreamProps* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMIStreamProps_AddRef(IWMIStreamProps* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMIStreamProps_Release(IWMIStreamProps* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMIStreamProps methods ***/
static inline HRESULT IWMIStreamProps_GetProperty(IWMIStreamProps* This,const WCHAR *name,WMT_ATTR_DATATYPE *type,BYTE *value,DWORD *size) {
    return This->lpVtbl->GetProperty(This,name,type,value,size);
}
#endif
#endif

#endif


#endif  /* __IWMIStreamProps_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMCredentialCallback interface
 */
#ifndef __IWMCredentialCallback_INTERFACE_DEFINED__
#define __IWMCredentialCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMCredentialCallback, 0x342e0eb7, 0xe651, 0x450c, 0x97,0x5b, 0x2a,0xce,0x2c,0x90,0xc4,0x8e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("342e0eb7-e651-450c-975b-2ace2c90c48e")
IWMCredentialCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AcquireCredentials(
        WCHAR *realm,
        WCHAR *site,
        WCHAR *username,
        DWORD username_size,
        WCHAR *password,
        DWORD password_size,
        HRESULT hr,
        DWORD *flags) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMCredentialCallback, 0x342e0eb7, 0xe651, 0x450c, 0x97,0x5b, 0x2a,0xce,0x2c,0x90,0xc4,0x8e)
#endif
#else
typedef struct IWMCredentialCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMCredentialCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMCredentialCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMCredentialCallback *This);

    /*** IWMCredentialCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *AcquireCredentials)(
        IWMCredentialCallback *This,
        WCHAR *realm,
        WCHAR *site,
        WCHAR *username,
        DWORD username_size,
        WCHAR *password,
        DWORD password_size,
        HRESULT hr,
        DWORD *flags);

    END_INTERFACE
} IWMCredentialCallbackVtbl;

interface IWMCredentialCallback {
    CONST_VTBL IWMCredentialCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMCredentialCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMCredentialCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMCredentialCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IWMCredentialCallback methods ***/
#define IWMCredentialCallback_AcquireCredentials(This,realm,site,username,username_size,password,password_size,hr,flags) (This)->lpVtbl->AcquireCredentials(This,realm,site,username,username_size,password,password_size,hr,flags)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMCredentialCallback_QueryInterface(IWMCredentialCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMCredentialCallback_AddRef(IWMCredentialCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMCredentialCallback_Release(IWMCredentialCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMCredentialCallback methods ***/
static inline HRESULT IWMCredentialCallback_AcquireCredentials(IWMCredentialCallback* This,WCHAR *realm,WCHAR *site,WCHAR *username,DWORD username_size,WCHAR *password,DWORD password_size,HRESULT hr,DWORD *flags) {
    return This->lpVtbl->AcquireCredentials(This,realm,site,username,username_size,password,password_size,hr,flags);
}
#endif
#endif

#endif


#endif  /* __IWMCredentialCallback_INTERFACE_DEFINED__ */

HRESULT WINAPI WMCheckURLExtension(LPCWSTR);
HRESULT WINAPI WMCheckURLScheme(LPCWSTR);
HRESULT WINAPI WMCreateWriter(IUnknown*,IWMWriter**);
HRESULT WINAPI WMCreateReader(IUnknown*,DWORD,IWMReader**);
HRESULT WINAPI WMCreateSyncReader(IUnknown*,DWORD,IWMSyncReader**);
HRESULT WINAPI WMCreateEditor(IWMMetadataEditor**);
HRESULT WINAPI WMCreateBackupRestorer(IUnknown*,IWMLicenseBackup**);
HRESULT WINAPI WMCreateProfileManager(IWMProfileManager**);
HRESULT WINAPI WMIsContentProtected(const WCHAR*, WINBOOL*);
EXTERN_GUID(WMMEDIASUBTYPE_Base,   0x00000000,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIATYPE_Video,     0x73646976,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_RGB1,   0xe436eb78,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);
EXTERN_GUID(WMMEDIASUBTYPE_RGB4,   0xe436eb79,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);
EXTERN_GUID(WMMEDIASUBTYPE_RGB8,   0xe436eb7a,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);
EXTERN_GUID(WMMEDIASUBTYPE_RGB565, 0xe436eb7b,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);
EXTERN_GUID(WMMEDIASUBTYPE_RGB555, 0xe436eb7c,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);
EXTERN_GUID(WMMEDIASUBTYPE_RGB24,  0xe436eb7d,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);
EXTERN_GUID(WMMEDIASUBTYPE_RGB32,  0xe436eb7e,0x524f,0x11ce,0x9f,0x53,0x00,0x20,0xaf,0x0b,0xa7,0x70);
EXTERN_GUID(WMMEDIASUBTYPE_I420,   0x30323449,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_IYUV,   0x56555949,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_YV12,   0x32315659,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_YUY2,   0x32595559,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_P422,   0x32323450,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_UYVY,   0x59565955,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_YVYU,   0x55595659,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_YVU9,   0x39555659,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_VIDEOIMAGE, 0x1d4a45f2,0xe5f6,0x4b44,0x83,0x88,0xf0,0xae,0x5c,0x0e,0x0c,0x37);
EXTERN_GUID(WMMEDIASUBTYPE_MP43,   0x3334504d,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_MP4S,   0x5334504d,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_M4S2,   0x3253344d,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_WMV1,   0x31564d57,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_WMV2,   0x32564d57,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_MSS1,   0x3153534d,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_MPEG2_VIDEO, 0xe06d8026,0xdb46,0x11cf,0xb4,0xd1,0x00,0x80,0x5f,0x6c,0xbb,0xea);
EXTERN_GUID(WMMEDIATYPE_Audio,     0x73647561,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_PCM,    0x00000001,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_DRM,    0x00000009,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_WMAudioV9,        0x00000162,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_WMAudio_Lossless, 0x00000163,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_MSS2,   0x3253534d,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_WMSP1,  0x0000000a,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_WMSP2,  0x0000000b,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_WMV3,   0x33564d57,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_WMVP,   0x50564d57,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_WVP2,   0x32505657,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_WMVA,   0x41564d57,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
EXTERN_GUID(WMMEDIASUBTYPE_WVC1,   0x31435657,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wmsdkidl_h__ */
