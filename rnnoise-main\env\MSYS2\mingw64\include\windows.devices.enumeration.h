/*** Autogenerated by WIDL 10.12 from include/windows.devices.enumeration.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_devices_enumeration_h__
#define __windows_devices_enumeration_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation ABI::Windows::Devices::Enumeration::IDeviceInformation
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceInformation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate ABI::Windows::Devices::Enumeration::IDeviceInformationUpdate
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceInformationUpdate;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher ABI::Windows::Devices::Enumeration::IDeviceWatcher
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceWatcher;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation ABI::Windows::Devices::Enumeration::IEnclosureLocation
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IEnclosureLocation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics ABI::Windows::Devices::Enumeration::IDeviceInformationStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceInformationStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 ABI::Windows::Devices::Enumeration::IDeviceInformationStatics2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceInformationStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs ABI::Windows::Devices::Enumeration::IDeviceAccessChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceAccessChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2 __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2 ABI::Windows::Devices::Enumeration::IDeviceAccessChangedEventArgs2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceAccessChangedEventArgs2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation ABI::Windows::Devices::Enumeration::IDeviceAccessInformation
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceAccessInformation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics ABI::Windows::Devices::Enumeration::IDeviceAccessInformationStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceAccessInformationStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                class DeviceInformation;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformation __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformation_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationCollection_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationCollection_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                class DeviceInformationCollection;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationCollection __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationCollection;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationCollection_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationUpdate_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationUpdate_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                class DeviceInformationUpdate;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationUpdate __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationUpdate;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationUpdate_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceThumbnail_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceThumbnail_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                class DeviceThumbnail;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CDeviceThumbnail __x_ABI_CWindows_CDevices_CEnumeration_CDeviceThumbnail;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceThumbnail_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcher_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcher_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                class DeviceWatcher;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcher __x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcher;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcher_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CEnclosureLocation_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CEnclosureLocation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                class EnclosureLocation;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CEnclosureLocation __x_ABI_CWindows_CDevices_CEnumeration_CEnclosureLocation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CEnumeration_CEnclosureLocation_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                class DeviceAccessChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessChangedEventArgs __x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessInformation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                class DeviceAccessInformation;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessInformation __x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessInformation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessInformation_FWD_DEFINED__ */

#ifndef ____FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Enumeration::DeviceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
#define ____FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Devices::Enumeration::DeviceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
#define ____FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Devices::Enumeration::DeviceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceThumbnail* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_IInspectable_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_IInspectable_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_IInspectable __FIMapView_2_HSTRING_IInspectable;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_IInspectable ABI::Windows::Foundation::Collections::IMapView<HSTRING,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceThumbnail* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformationUpdate* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceAccessInformation*,ABI::Windows::Devices::Enumeration::DeviceAccessChangedEventArgs* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.storage.streams.h>
#include <windows.foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                enum DeviceClass {
                    DeviceClass_All = 0,
                    DeviceClass_AudioCapture = 1,
                    DeviceClass_AudioRender = 2,
                    DeviceClass_PortableStorageDevice = 3,
                    DeviceClass_VideoCapture = 4,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    DeviceClass_ImageScanner = 5,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    DeviceClass_Location = 6
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass {
    DeviceClass_All = 0,
    DeviceClass_AudioCapture = 1,
    DeviceClass_AudioRender = 2,
    DeviceClass_PortableStorageDevice = 3,
    DeviceClass_VideoCapture = 4,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    DeviceClass_ImageScanner = 5,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    DeviceClass_Location = 6
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
};
#ifdef WIDL_using_Windows_Devices_Enumeration
#define DeviceClass __x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass __x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                enum DeviceInformationKind {
                    DeviceInformationKind_Unknown = 0,
                    DeviceInformationKind_DeviceInterface = 1,
                    DeviceInformationKind_DeviceContainer = 2,
                    DeviceInformationKind_Device = 3,
                    DeviceInformationKind_DeviceInterfaceClass = 4,
                    DeviceInformationKind_AssociationEndpoint = 5,
                    DeviceInformationKind_AssociationEndpointContainer = 6,
                    DeviceInformationKind_AssociationEndpointService = 7,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000
                    DeviceInformationKind_DevicePanel = 8
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind {
    DeviceInformationKind_Unknown = 0,
    DeviceInformationKind_DeviceInterface = 1,
    DeviceInformationKind_DeviceContainer = 2,
    DeviceInformationKind_Device = 3,
    DeviceInformationKind_DeviceInterfaceClass = 4,
    DeviceInformationKind_AssociationEndpoint = 5,
    DeviceInformationKind_AssociationEndpointContainer = 6,
    DeviceInformationKind_AssociationEndpointService = 7,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000
    DeviceInformationKind_DevicePanel = 8
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000 */
};
#ifdef WIDL_using_Windows_Devices_Enumeration
#define DeviceInformationKind __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcherStatus_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcherStatus_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                enum DeviceWatcherStatus {
                    DeviceWatcherStatus_Created = 0,
                    DeviceWatcherStatus_Started = 1,
                    DeviceWatcherStatus_EnumerationCompleted = 2,
                    DeviceWatcherStatus_Stopping = 3,
                    DeviceWatcherStatus_Stopped = 4,
                    DeviceWatcherStatus_Aborted = 5
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcherStatus {
    DeviceWatcherStatus_Created = 0,
    DeviceWatcherStatus_Started = 1,
    DeviceWatcherStatus_EnumerationCompleted = 2,
    DeviceWatcherStatus_Stopping = 3,
    DeviceWatcherStatus_Stopped = 4,
    DeviceWatcherStatus_Aborted = 5
};
#ifdef WIDL_using_Windows_Devices_Enumeration
#define DeviceWatcherStatus __x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcherStatus
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcherStatus_ENUM_DEFINED__ */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcherStatus __x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcherStatus;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CPanel_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CPanel_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                enum Panel {
                    Panel_Unknown = 0,
                    Panel_Front = 1,
                    Panel_Back = 2,
                    Panel_Top = 3,
                    Panel_Bottom = 4,
                    Panel_Left = 5,
                    Panel_Right = 6
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CEnumeration_CPanel {
    Panel_Unknown = 0,
    Panel_Front = 1,
    Panel_Back = 2,
    Panel_Top = 3,
    Panel_Bottom = 4,
    Panel_Left = 5,
    Panel_Right = 6
};
#ifdef WIDL_using_Windows_Devices_Enumeration
#define Panel __x_ABI_CWindows_CDevices_CEnumeration_CPanel
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CEnumeration_CPanel_ENUM_DEFINED__ */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CEnumeration_CPanel __x_ABI_CWindows_CDevices_CEnumeration_CPanel;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessStatus_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessStatus_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                enum DeviceAccessStatus {
                    DeviceAccessStatus_Unspecified = 0,
                    DeviceAccessStatus_Allowed = 1,
                    DeviceAccessStatus_DeniedByUser = 2,
                    DeviceAccessStatus_DeniedBySystem = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessStatus {
    DeviceAccessStatus_Unspecified = 0,
    DeviceAccessStatus_Allowed = 1,
    DeviceAccessStatus_DeniedByUser = 2,
    DeviceAccessStatus_DeniedBySystem = 3
};
#ifdef WIDL_using_Windows_Devices_Enumeration
#define DeviceAccessStatus __x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessStatus
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessStatus_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessStatus __x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessStatus;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation ABI::Windows::Devices::Enumeration::IDeviceInformation
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceInformation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics ABI::Windows::Devices::Enumeration::IDeviceInformationStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceInformationStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 ABI::Windows::Devices::Enumeration::IDeviceInformationStatics2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceInformationStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate ABI::Windows::Devices::Enumeration::IDeviceInformationUpdate
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IDeviceInformationUpdate;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation ABI::Windows::Devices::Enumeration::IEnclosureLocation
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                interface IEnclosureLocation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Enumeration::DeviceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
#define ____FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Devices::Enumeration::DeviceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
#define ____FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Devices::Enumeration::DeviceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_HSTRING_IInspectable_FWD_DEFINED__
#define ____FIMapView_2_HSTRING_IInspectable_FWD_DEFINED__
typedef interface __FIMapView_2_HSTRING_IInspectable __FIMapView_2_HSTRING_IInspectable;
#ifdef __cplusplus
#define __FIMapView_2_HSTRING_IInspectable ABI::Windows::Foundation::Collections::IMapView<HSTRING,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformation* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceThumbnail* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IDeviceInformation interface
 */
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation, 0xaba0fb95, 0x4398, 0x489d, 0x8e,0x44, 0xe6,0x13,0x09,0x27,0x01,0x1f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                MIDL_INTERFACE("aba0fb95-4398-489d-8e44-e6130927011f")
                IDeviceInformation : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Id(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Name(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsEnabled(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsDefault(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_EnclosureLocation(
                        ABI::Windows::Devices::Enumeration::IEnclosureLocation **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Properties(
                        ABI::Windows::Foundation::Collections::IMapView<HSTRING,IInspectable* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Update(
                        ABI::Windows::Devices::Enumeration::IDeviceInformationUpdate *info) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetThumbnailAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetGlyphThumbnailAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation, 0xaba0fb95, 0x4398, 0x489d, 0x8e,0x44, 0xe6,0x13,0x09,0x27,0x01,0x1f)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        TrustLevel *trustLevel);

    /*** IDeviceInformation methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Name)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_IsEnabled)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsDefault)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_EnclosureLocation)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation **value);

    HRESULT (STDMETHODCALLTYPE *get_Properties)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        __FIMapView_2_HSTRING_IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *Update)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *info);

    HRESULT (STDMETHODCALLTYPE *GetThumbnailAsync)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail **operation);

    HRESULT (STDMETHODCALLTYPE *GetGlyphThumbnailAsync)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *This,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail **operation);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationVtbl;

interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation {
    CONST_VTBL __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDeviceInformation methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_Name(This,value) (This)->lpVtbl->get_Name(This,value)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_IsEnabled(This,value) (This)->lpVtbl->get_IsEnabled(This,value)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_IsDefault(This,value) (This)->lpVtbl->get_IsDefault(This,value)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_EnclosureLocation(This,value) (This)->lpVtbl->get_EnclosureLocation(This,value)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_Properties(This,value) (This)->lpVtbl->get_Properties(This,value)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_Update(This,info) (This)->lpVtbl->Update(This,info)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetThumbnailAsync(This,operation) (This)->lpVtbl->GetThumbnailAsync(This,operation)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetGlyphThumbnailAsync(This,operation) (This)->lpVtbl->GetGlyphThumbnailAsync(This,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_QueryInterface(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_AddRef(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_Release(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetIids(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetTrustLevel(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDeviceInformation methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_Id(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,HSTRING *value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_Name(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,HSTRING *value) {
    return This->lpVtbl->get_Name(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_IsEnabled(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,boolean *value) {
    return This->lpVtbl->get_IsEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_IsDefault(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,boolean *value) {
    return This->lpVtbl->get_IsDefault(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_EnclosureLocation(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,__x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation **value) {
    return This->lpVtbl->get_EnclosureLocation(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_Properties(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,__FIMapView_2_HSTRING_IInspectable **value) {
    return This->lpVtbl->get_Properties(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_Update(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *info) {
    return This->lpVtbl->Update(This,info);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetThumbnailAsync(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail **operation) {
    return This->lpVtbl->GetThumbnailAsync(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetGlyphThumbnailAsync(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation* This,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail **operation) {
    return This->lpVtbl->GetGlyphThumbnailAsync(This,operation);
}
#endif
#ifdef WIDL_using_Windows_Devices_Enumeration
#define IID_IDeviceInformation IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation
#define IDeviceInformationVtbl __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationVtbl
#define IDeviceInformation __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation
#define IDeviceInformation_QueryInterface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_QueryInterface
#define IDeviceInformation_AddRef __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_AddRef
#define IDeviceInformation_Release __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_Release
#define IDeviceInformation_GetIids __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetIids
#define IDeviceInformation_GetRuntimeClassName __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetRuntimeClassName
#define IDeviceInformation_GetTrustLevel __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetTrustLevel
#define IDeviceInformation_get_Id __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_Id
#define IDeviceInformation_get_Name __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_Name
#define IDeviceInformation_get_IsEnabled __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_IsEnabled
#define IDeviceInformation_get_IsDefault __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_IsDefault
#define IDeviceInformation_get_EnclosureLocation __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_EnclosureLocation
#define IDeviceInformation_get_Properties __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_get_Properties
#define IDeviceInformation_Update __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_Update
#define IDeviceInformation_GetThumbnailAsync __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetThumbnailAsync
#define IDeviceInformation_GetGlyphThumbnailAsync __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_GetGlyphThumbnailAsync
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDeviceInformationUpdate interface
 */
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate, 0x8f315305, 0xd972, 0x44b7, 0xa3,0x7e, 0x9e,0x82,0x2c,0x78,0x21,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                MIDL_INTERFACE("8f315305-d972-44b7-a37e-9e822c78213b")
                IDeviceInformationUpdate : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Id(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Properties(
                        ABI::Windows::Foundation::Collections::IMapView<HSTRING,IInspectable* > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate, 0x8f315305, 0xd972, 0x44b7, 0xa3,0x7e, 0x9e,0x82,0x2c,0x78,0x21,0x3b)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *This,
        TrustLevel *trustLevel);

    /*** IDeviceInformationUpdate methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Properties)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *This,
        __FIMapView_2_HSTRING_IInspectable **value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdateVtbl;

interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate {
    CONST_VTBL __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDeviceInformationUpdate methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_get_Properties(This,value) (This)->lpVtbl->get_Properties(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_QueryInterface(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_AddRef(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_Release(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_GetIids(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_GetTrustLevel(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDeviceInformationUpdate methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_get_Id(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate* This,HSTRING *value) {
    return This->lpVtbl->get_Id(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_get_Properties(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate* This,__FIMapView_2_HSTRING_IInspectable **value) {
    return This->lpVtbl->get_Properties(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Enumeration
#define IID_IDeviceInformationUpdate IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate
#define IDeviceInformationUpdateVtbl __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdateVtbl
#define IDeviceInformationUpdate __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate
#define IDeviceInformationUpdate_QueryInterface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_QueryInterface
#define IDeviceInformationUpdate_AddRef __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_AddRef
#define IDeviceInformationUpdate_Release __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_Release
#define IDeviceInformationUpdate_GetIids __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_GetIids
#define IDeviceInformationUpdate_GetRuntimeClassName __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_GetRuntimeClassName
#define IDeviceInformationUpdate_GetTrustLevel __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_GetTrustLevel
#define IDeviceInformationUpdate_get_Id __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_get_Id
#define IDeviceInformationUpdate_get_Properties __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_get_Properties
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDeviceWatcher interface
 */
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher, 0xc9eab97d, 0x8f6b, 0x4f96, 0xa9,0xf4, 0xab,0xc8,0x14,0xe2,0x22,0x71);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                MIDL_INTERFACE("c9eab97d-8f6b-4f96-a9f4-abc814e22271")
                IDeviceWatcher : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_Added(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformation* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Added(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Updated(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformationUpdate* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Updated(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Removed(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformationUpdate* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Removed(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_EnumerationCompleted(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_EnumerationCompleted(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Stopped(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Stopped(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Status(
                        ABI::Windows::Devices::Enumeration::DeviceWatcherStatus *status) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Start(
                        ) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Stop(
                        ) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher, 0xc9eab97d, 0x8f6b, 0x4f96, 0xa9,0xf4, 0xab,0xc8,0x14,0xe2,0x22,0x71)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcherVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        TrustLevel *trustLevel);

    /*** IDeviceWatcher methods ***/
    HRESULT (STDMETHODCALLTYPE *add_Added)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Added)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_Updated)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Updated)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_Removed)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Removed)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_EnumerationCompleted)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_EnumerationCompleted)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_Stopped)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Stopped)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_Status)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcherStatus *status);

    HRESULT (STDMETHODCALLTYPE *Start)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This);

    HRESULT (STDMETHODCALLTYPE *Stop)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *This);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcherVtbl;

interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher {
    CONST_VTBL __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcherVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDeviceWatcher methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Added(This,handler,token) (This)->lpVtbl->add_Added(This,handler,token)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Added(This,token) (This)->lpVtbl->remove_Added(This,token)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Updated(This,handler,token) (This)->lpVtbl->add_Updated(This,handler,token)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Updated(This,token) (This)->lpVtbl->remove_Updated(This,token)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Removed(This,handler,token) (This)->lpVtbl->add_Removed(This,handler,token)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Removed(This,token) (This)->lpVtbl->remove_Removed(This,token)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_EnumerationCompleted(This,handler,token) (This)->lpVtbl->add_EnumerationCompleted(This,handler,token)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_EnumerationCompleted(This,token) (This)->lpVtbl->remove_EnumerationCompleted(This,token)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Stopped(This,handler,token) (This)->lpVtbl->add_Stopped(This,handler,token)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Stopped(This,token) (This)->lpVtbl->remove_Stopped(This,token)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_get_Status(This,status) (This)->lpVtbl->get_Status(This,status)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_Start(This) (This)->lpVtbl->Start(This)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_Stop(This) (This)->lpVtbl->Stop(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_QueryInterface(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_AddRef(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_Release(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_GetIids(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_GetTrustLevel(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDeviceWatcher methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Added(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Added(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Added(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Added(This,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Updated(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Updated(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Updated(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Updated(This,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Removed(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Removed(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Removed(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Removed(This,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_EnumerationCompleted(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_EnumerationCompleted(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_EnumerationCompleted(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_EnumerationCompleted(This,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Stopped(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Stopped(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Stopped(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Stopped(This,token);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_get_Status(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This,__x_ABI_CWindows_CDevices_CEnumeration_CDeviceWatcherStatus *status) {
    return This->lpVtbl->get_Status(This,status);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_Start(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This) {
    return This->lpVtbl->Start(This);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_Stop(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher* This) {
    return This->lpVtbl->Stop(This);
}
#endif
#ifdef WIDL_using_Windows_Devices_Enumeration
#define IID_IDeviceWatcher IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher
#define IDeviceWatcherVtbl __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcherVtbl
#define IDeviceWatcher __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher
#define IDeviceWatcher_QueryInterface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_QueryInterface
#define IDeviceWatcher_AddRef __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_AddRef
#define IDeviceWatcher_Release __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_Release
#define IDeviceWatcher_GetIids __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_GetIids
#define IDeviceWatcher_GetRuntimeClassName __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_GetRuntimeClassName
#define IDeviceWatcher_GetTrustLevel __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_GetTrustLevel
#define IDeviceWatcher_add_Added __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Added
#define IDeviceWatcher_remove_Added __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Added
#define IDeviceWatcher_add_Updated __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Updated
#define IDeviceWatcher_remove_Updated __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Updated
#define IDeviceWatcher_add_Removed __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Removed
#define IDeviceWatcher_remove_Removed __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Removed
#define IDeviceWatcher_add_EnumerationCompleted __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_EnumerationCompleted
#define IDeviceWatcher_remove_EnumerationCompleted __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_EnumerationCompleted
#define IDeviceWatcher_add_Stopped __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_add_Stopped
#define IDeviceWatcher_remove_Stopped __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_remove_Stopped
#define IDeviceWatcher_get_Status __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_get_Status
#define IDeviceWatcher_Start __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_Start
#define IDeviceWatcher_Stop __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_Stop
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IEnclosureLocation interface
 */
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation, 0x42340a27, 0x5810, 0x459c, 0xaa,0xbb, 0xc6,0x5e,0x1f,0x81,0x3e,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                MIDL_INTERFACE("42340a27-5810-459c-aabb-c65e1f813ecf")
                IEnclosureLocation : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_InDock(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_InLid(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Panel(
                        ABI::Windows::Devices::Enumeration::Panel *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation, 0x42340a27, 0x5810, 0x459c, 0xaa,0xbb, 0xc6,0x5e,0x1f,0x81,0x3e,0xcf)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation *This,
        TrustLevel *trustLevel);

    /*** IEnclosureLocation methods ***/
    HRESULT (STDMETHODCALLTYPE *get_InDock)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_InLid)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_Panel)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CPanel *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocationVtbl;

interface __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation {
    CONST_VTBL __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IEnclosureLocation methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_get_InDock(This,value) (This)->lpVtbl->get_InDock(This,value)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_get_InLid(This,value) (This)->lpVtbl->get_InLid(This,value)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_get_Panel(This,value) (This)->lpVtbl->get_Panel(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_QueryInterface(__x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_AddRef(__x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_Release(__x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_GetIids(__x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_GetTrustLevel(__x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IEnclosureLocation methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_get_InDock(__x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation* This,boolean *value) {
    return This->lpVtbl->get_InDock(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_get_InLid(__x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation* This,boolean *value) {
    return This->lpVtbl->get_InLid(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_get_Panel(__x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation* This,__x_ABI_CWindows_CDevices_CEnumeration_CPanel *value) {
    return This->lpVtbl->get_Panel(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Enumeration
#define IID_IEnclosureLocation IID___x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation
#define IEnclosureLocationVtbl __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocationVtbl
#define IEnclosureLocation __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation
#define IEnclosureLocation_QueryInterface __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_QueryInterface
#define IEnclosureLocation_AddRef __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_AddRef
#define IEnclosureLocation_Release __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_Release
#define IEnclosureLocation_GetIids __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_GetIids
#define IEnclosureLocation_GetRuntimeClassName __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_GetRuntimeClassName
#define IEnclosureLocation_GetTrustLevel __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_GetTrustLevel
#define IEnclosureLocation_get_InDock __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_get_InDock
#define IEnclosureLocation_get_InLid __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_get_InLid
#define IEnclosureLocation_get_Panel __x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_get_Panel
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CEnumeration_CIEnclosureLocation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDeviceInformationStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics, 0xc17f100e, 0x3a46, 0x4a78, 0x80,0x13, 0x76,0x9d,0xc9,0xb9,0x73,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                MIDL_INTERFACE("c17f100e-3a46-4a78-8013-769dc9b97390")
                IDeviceInformationStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateFromIdAsync(
                        HSTRING id,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformation* > **op) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateFromIdAsyncAdditionalProperties(
                        HSTRING id,
                        ABI::Windows::Foundation::Collections::IIterable<HSTRING > *additional_properties,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformation* > **op) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindAllAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > **op) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindAllAsyncDeviceClass(
                        ABI::Windows::Devices::Enumeration::DeviceClass device_class,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > **op) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindAllAsyncAqsFilter(
                        HSTRING filter,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > **op) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindAllAsyncAqsFilterAndAdditionalProperties(
                        HSTRING filter,
                        ABI::Windows::Foundation::Collections::IIterable<HSTRING > *additional_properties,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > **op) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateWatcher(
                        ABI::Windows::Devices::Enumeration::IDeviceWatcher **watcher) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateWatcherDeviceClass(
                        ABI::Windows::Devices::Enumeration::DeviceClass device_class,
                        ABI::Windows::Devices::Enumeration::IDeviceWatcher **watcher) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateWatcherAqsFilter(
                        HSTRING filter,
                        ABI::Windows::Devices::Enumeration::IDeviceWatcher **watcher) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateWatcherAqsFilterAndAdditionalProperties(
                        HSTRING filter,
                        ABI::Windows::Foundation::Collections::IIterable<HSTRING > *additional_properties,
                        ABI::Windows::Devices::Enumeration::IDeviceWatcher **watcher) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics, 0xc17f100e, 0x3a46, 0x4a78, 0x80,0x13, 0x76,0x9d,0xc9,0xb9,0x73,0x90)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        TrustLevel *trustLevel);

    /*** IDeviceInformationStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateFromIdAsync)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        HSTRING id,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation **op);

    HRESULT (STDMETHODCALLTYPE *CreateFromIdAsyncAdditionalProperties)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        HSTRING id,
        __FIIterable_1_HSTRING *additional_properties,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation **op);

    HRESULT (STDMETHODCALLTYPE *FindAllAsync)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **op);

    HRESULT (STDMETHODCALLTYPE *FindAllAsyncDeviceClass)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass device_class,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **op);

    HRESULT (STDMETHODCALLTYPE *FindAllAsyncAqsFilter)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        HSTRING filter,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **op);

    HRESULT (STDMETHODCALLTYPE *FindAllAsyncAqsFilterAndAdditionalProperties)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        HSTRING filter,
        __FIIterable_1_HSTRING *additional_properties,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **op);

    HRESULT (STDMETHODCALLTYPE *CreateWatcher)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher **watcher);

    HRESULT (STDMETHODCALLTYPE *CreateWatcherDeviceClass)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass device_class,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher **watcher);

    HRESULT (STDMETHODCALLTYPE *CreateWatcherAqsFilter)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        HSTRING filter,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher **watcher);

    HRESULT (STDMETHODCALLTYPE *CreateWatcherAqsFilterAndAdditionalProperties)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics *This,
        HSTRING filter,
        __FIIterable_1_HSTRING *additional_properties,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher **watcher);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDeviceInformationStatics methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateFromIdAsync(This,id,op) (This)->lpVtbl->CreateFromIdAsync(This,id,op)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateFromIdAsyncAdditionalProperties(This,id,additional_properties,op) (This)->lpVtbl->CreateFromIdAsyncAdditionalProperties(This,id,additional_properties,op)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsync(This,op) (This)->lpVtbl->FindAllAsync(This,op)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsyncDeviceClass(This,device_class,op) (This)->lpVtbl->FindAllAsyncDeviceClass(This,device_class,op)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsyncAqsFilter(This,filter,op) (This)->lpVtbl->FindAllAsyncAqsFilter(This,filter,op)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsyncAqsFilterAndAdditionalProperties(This,filter,additional_properties,op) (This)->lpVtbl->FindAllAsyncAqsFilterAndAdditionalProperties(This,filter,additional_properties,op)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcher(This,watcher) (This)->lpVtbl->CreateWatcher(This,watcher)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcherDeviceClass(This,device_class,watcher) (This)->lpVtbl->CreateWatcherDeviceClass(This,device_class,watcher)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcherAqsFilter(This,filter,watcher) (This)->lpVtbl->CreateWatcherAqsFilter(This,filter,watcher)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcherAqsFilterAndAdditionalProperties(This,filter,additional_properties,watcher) (This)->lpVtbl->CreateWatcherAqsFilterAndAdditionalProperties(This,filter,additional_properties,watcher)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_QueryInterface(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_AddRef(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_Release(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_GetIids(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDeviceInformationStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateFromIdAsync(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,HSTRING id,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation **op) {
    return This->lpVtbl->CreateFromIdAsync(This,id,op);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateFromIdAsyncAdditionalProperties(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,HSTRING id,__FIIterable_1_HSTRING *additional_properties,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation **op) {
    return This->lpVtbl->CreateFromIdAsyncAdditionalProperties(This,id,additional_properties,op);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsync(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **op) {
    return This->lpVtbl->FindAllAsync(This,op);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsyncDeviceClass(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,__x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass device_class,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **op) {
    return This->lpVtbl->FindAllAsyncDeviceClass(This,device_class,op);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsyncAqsFilter(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,HSTRING filter,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **op) {
    return This->lpVtbl->FindAllAsyncAqsFilter(This,filter,op);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsyncAqsFilterAndAdditionalProperties(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,HSTRING filter,__FIIterable_1_HSTRING *additional_properties,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **op) {
    return This->lpVtbl->FindAllAsyncAqsFilterAndAdditionalProperties(This,filter,additional_properties,op);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcher(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher **watcher) {
    return This->lpVtbl->CreateWatcher(This,watcher);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcherDeviceClass(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,__x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass device_class,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher **watcher) {
    return This->lpVtbl->CreateWatcherDeviceClass(This,device_class,watcher);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcherAqsFilter(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,HSTRING filter,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher **watcher) {
    return This->lpVtbl->CreateWatcherAqsFilter(This,filter,watcher);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcherAqsFilterAndAdditionalProperties(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics* This,HSTRING filter,__FIIterable_1_HSTRING *additional_properties,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher **watcher) {
    return This->lpVtbl->CreateWatcherAqsFilterAndAdditionalProperties(This,filter,additional_properties,watcher);
}
#endif
#ifdef WIDL_using_Windows_Devices_Enumeration
#define IID_IDeviceInformationStatics IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics
#define IDeviceInformationStaticsVtbl __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStaticsVtbl
#define IDeviceInformationStatics __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics
#define IDeviceInformationStatics_QueryInterface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_QueryInterface
#define IDeviceInformationStatics_AddRef __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_AddRef
#define IDeviceInformationStatics_Release __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_Release
#define IDeviceInformationStatics_GetIids __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_GetIids
#define IDeviceInformationStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_GetRuntimeClassName
#define IDeviceInformationStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_GetTrustLevel
#define IDeviceInformationStatics_CreateFromIdAsync __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateFromIdAsync
#define IDeviceInformationStatics_CreateFromIdAsyncAdditionalProperties __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateFromIdAsyncAdditionalProperties
#define IDeviceInformationStatics_FindAllAsync __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsync
#define IDeviceInformationStatics_FindAllAsyncDeviceClass __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsyncDeviceClass
#define IDeviceInformationStatics_FindAllAsyncAqsFilter __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsyncAqsFilter
#define IDeviceInformationStatics_FindAllAsyncAqsFilterAndAdditionalProperties __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_FindAllAsyncAqsFilterAndAdditionalProperties
#define IDeviceInformationStatics_CreateWatcher __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcher
#define IDeviceInformationStatics_CreateWatcherDeviceClass __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcherDeviceClass
#define IDeviceInformationStatics_CreateWatcherAqsFilter __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcherAqsFilter
#define IDeviceInformationStatics_CreateWatcherAqsFilterAndAdditionalProperties __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_CreateWatcherAqsFilterAndAdditionalProperties
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDeviceInformationStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2, 0x493b4f34, 0xa84f, 0x45fd, 0x91,0x67, 0x15,0xd1,0xcb,0x1b,0xd1,0xf9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                MIDL_INTERFACE("493b4f34-a84f-45fd-9167-15d1cb1bd1f9")
                IDeviceInformationStatics2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetAqsFilterFromDeviceClass(
                        ABI::Windows::Devices::Enumeration::DeviceClass device_class,
                        HSTRING *filter) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateFromIdAsyncWithKindAndAdditionalProperties(
                        HSTRING device_id,
                        ABI::Windows::Foundation::Collections::IIterable<HSTRING > *additional_properties,
                        ABI::Windows::Devices::Enumeration::DeviceInformationKind kind,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformation* > **async_operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE FindAllAsyncWithKindAqsFilterAndAdditionalProperties(
                        HSTRING filter,
                        ABI::Windows::Foundation::Collections::IIterable<HSTRING > *additional_properties,
                        ABI::Windows::Devices::Enumeration::DeviceInformationKind kind,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > **async_operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateWatcherWithKindAqsFilterAndAdditionalProperties(
                        HSTRING filter,
                        ABI::Windows::Foundation::Collections::IIterable<HSTRING > *additional_properties,
                        ABI::Windows::Devices::Enumeration::DeviceInformationKind kind,
                        ABI::Windows::Devices::Enumeration::IDeviceWatcher **watcher) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2, 0x493b4f34, 0xa84f, 0x45fd, 0x91,0x67, 0x15,0xd1,0xcb,0x1b,0xd1,0xf9)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 *This,
        TrustLevel *trustLevel);

    /*** IDeviceInformationStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAqsFilterFromDeviceClass)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass device_class,
        HSTRING *filter);

    HRESULT (STDMETHODCALLTYPE *CreateFromIdAsyncWithKindAndAdditionalProperties)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 *This,
        HSTRING device_id,
        __FIIterable_1_HSTRING *additional_properties,
        __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind kind,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation **async_operation);

    HRESULT (STDMETHODCALLTYPE *FindAllAsyncWithKindAqsFilterAndAdditionalProperties)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 *This,
        HSTRING filter,
        __FIIterable_1_HSTRING *additional_properties,
        __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind kind,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **async_operation);

    HRESULT (STDMETHODCALLTYPE *CreateWatcherWithKindAqsFilterAndAdditionalProperties)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 *This,
        HSTRING filter,
        __FIIterable_1_HSTRING *additional_properties,
        __x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind kind,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher **watcher);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2Vtbl;

interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2 {
    CONST_VTBL __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDeviceInformationStatics2 methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetAqsFilterFromDeviceClass(This,device_class,filter) (This)->lpVtbl->GetAqsFilterFromDeviceClass(This,device_class,filter)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_CreateFromIdAsyncWithKindAndAdditionalProperties(This,device_id,additional_properties,kind,async_operation) (This)->lpVtbl->CreateFromIdAsyncWithKindAndAdditionalProperties(This,device_id,additional_properties,kind,async_operation)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_FindAllAsyncWithKindAqsFilterAndAdditionalProperties(This,filter,additional_properties,kind,async_operation) (This)->lpVtbl->FindAllAsyncWithKindAqsFilterAndAdditionalProperties(This,filter,additional_properties,kind,async_operation)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_CreateWatcherWithKindAqsFilterAndAdditionalProperties(This,filter,additional_properties,kind,watcher) (This)->lpVtbl->CreateWatcherWithKindAqsFilterAndAdditionalProperties(This,filter,additional_properties,kind,watcher)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_QueryInterface(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_AddRef(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_Release(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetIids(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetTrustLevel(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDeviceInformationStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetAqsFilterFromDeviceClass(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2* This,__x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass device_class,HSTRING *filter) {
    return This->lpVtbl->GetAqsFilterFromDeviceClass(This,device_class,filter);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_CreateFromIdAsyncWithKindAndAdditionalProperties(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2* This,HSTRING device_id,__FIIterable_1_HSTRING *additional_properties,__x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind kind,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation **async_operation) {
    return This->lpVtbl->CreateFromIdAsyncWithKindAndAdditionalProperties(This,device_id,additional_properties,kind,async_operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_FindAllAsyncWithKindAqsFilterAndAdditionalProperties(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2* This,HSTRING filter,__FIIterable_1_HSTRING *additional_properties,__x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind kind,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **async_operation) {
    return This->lpVtbl->FindAllAsyncWithKindAqsFilterAndAdditionalProperties(This,filter,additional_properties,kind,async_operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_CreateWatcherWithKindAqsFilterAndAdditionalProperties(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2* This,HSTRING filter,__FIIterable_1_HSTRING *additional_properties,__x_ABI_CWindows_CDevices_CEnumeration_CDeviceInformationKind kind,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher **watcher) {
    return This->lpVtbl->CreateWatcherWithKindAqsFilterAndAdditionalProperties(This,filter,additional_properties,kind,watcher);
}
#endif
#ifdef WIDL_using_Windows_Devices_Enumeration
#define IID_IDeviceInformationStatics2 IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2
#define IDeviceInformationStatics2Vtbl __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2Vtbl
#define IDeviceInformationStatics2 __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2
#define IDeviceInformationStatics2_QueryInterface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_QueryInterface
#define IDeviceInformationStatics2_AddRef __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_AddRef
#define IDeviceInformationStatics2_Release __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_Release
#define IDeviceInformationStatics2_GetIids __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetIids
#define IDeviceInformationStatics2_GetRuntimeClassName __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetRuntimeClassName
#define IDeviceInformationStatics2_GetTrustLevel __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetTrustLevel
#define IDeviceInformationStatics2_GetAqsFilterFromDeviceClass __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_GetAqsFilterFromDeviceClass
#define IDeviceInformationStatics2_CreateFromIdAsyncWithKindAndAdditionalProperties __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_CreateFromIdAsyncWithKindAndAdditionalProperties
#define IDeviceInformationStatics2_FindAllAsyncWithKindAqsFilterAndAdditionalProperties __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_FindAllAsyncWithKindAqsFilterAndAdditionalProperties
#define IDeviceInformationStatics2_CreateWatcherWithKindAqsFilterAndAdditionalProperties __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_CreateWatcherWithKindAqsFilterAndAdditionalProperties
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDeviceAccessChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs, 0xdeda0bcc, 0x4f9d, 0x4f58, 0x9d,0xba, 0xa9,0xbc,0x80,0x04,0x08,0xd5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                MIDL_INTERFACE("deda0bcc-4f9d-4f58-9dba-a9bc800408d5")
                IDeviceAccessChangedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Status(
                        ABI::Windows::Devices::Enumeration::DeviceAccessStatus *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs, 0xdeda0bcc, 0x4f9d, 0x4f58, 0x9d,0xba, 0xa9,0xbc,0x80,0x04,0x08,0xd5)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs *This,
        TrustLevel *trustLevel);

    /*** IDeviceAccessChangedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Status)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessStatus *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgsVtbl;

interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDeviceAccessChangedEventArgs methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_get_Status(This,value) (This)->lpVtbl->get_Status(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_QueryInterface(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_AddRef(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_Release(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_GetIids(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDeviceAccessChangedEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_get_Status(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs* This,__x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessStatus *value) {
    return This->lpVtbl->get_Status(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Enumeration
#define IID_IDeviceAccessChangedEventArgs IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs
#define IDeviceAccessChangedEventArgsVtbl __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgsVtbl
#define IDeviceAccessChangedEventArgs __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs
#define IDeviceAccessChangedEventArgs_QueryInterface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_QueryInterface
#define IDeviceAccessChangedEventArgs_AddRef __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_AddRef
#define IDeviceAccessChangedEventArgs_Release __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_Release
#define IDeviceAccessChangedEventArgs_GetIids __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_GetIids
#define IDeviceAccessChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_GetRuntimeClassName
#define IDeviceAccessChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_GetTrustLevel
#define IDeviceAccessChangedEventArgs_get_Status __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_get_Status
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDeviceAccessChangedEventArgs2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2, 0x82523262, 0x934b, 0x4b30, 0xa1,0x78, 0xad,0xc3,0x9f,0x2f,0x2b,0xe3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                MIDL_INTERFACE("82523262-934b-4b30-a178-adc39f2f2be3")
                IDeviceAccessChangedEventArgs2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Id(
                        HSTRING *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2, 0x82523262, 0x934b, 0x4b30, 0xa1,0x78, 0xad,0xc3,0x9f,0x2f,0x2b,0xe3)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2 *This,
        TrustLevel *trustLevel);

    /*** IDeviceAccessChangedEventArgs2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Id)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2 *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2Vtbl;

interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2 {
    CONST_VTBL __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDeviceAccessChangedEventArgs2 methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_get_Id(This,value) (This)->lpVtbl->get_Id(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_QueryInterface(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_AddRef(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_Release(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_GetIids(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_GetTrustLevel(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDeviceAccessChangedEventArgs2 methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_get_Id(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2* This,HSTRING *value) {
    return This->lpVtbl->get_Id(This,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Enumeration
#define IID_IDeviceAccessChangedEventArgs2 IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2
#define IDeviceAccessChangedEventArgs2Vtbl __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2Vtbl
#define IDeviceAccessChangedEventArgs2 __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2
#define IDeviceAccessChangedEventArgs2_QueryInterface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_QueryInterface
#define IDeviceAccessChangedEventArgs2_AddRef __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_AddRef
#define IDeviceAccessChangedEventArgs2_Release __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_Release
#define IDeviceAccessChangedEventArgs2_GetIids __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_GetIids
#define IDeviceAccessChangedEventArgs2_GetRuntimeClassName __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_GetRuntimeClassName
#define IDeviceAccessChangedEventArgs2_GetTrustLevel __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_GetTrustLevel
#define IDeviceAccessChangedEventArgs2_get_Id __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_get_Id
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x30000 */

/*****************************************************************************
 * IDeviceAccessInformation interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation, 0x0baa9a73, 0x6de5, 0x4915, 0x8d,0xdd, 0x9a,0x05,0x54,0xa6,0xf5,0x45);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                MIDL_INTERFACE("0baa9a73-6de5-4915-8ddd-9a0554a6f545")
                IDeviceAccessInformation : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_AccessChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceAccessInformation*,ABI::Windows::Devices::Enumeration::DeviceAccessChangedEventArgs* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_AccessChanged(
                        EventRegistrationToken cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_CurrentStatus(
                        ABI::Windows::Devices::Enumeration::DeviceAccessStatus *status) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation, 0x0baa9a73, 0x6de5, 0x4915, 0x8d,0xdd, 0x9a,0x05,0x54,0xa6,0xf5,0x45)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation *This,
        TrustLevel *trustLevel);

    /*** IDeviceAccessInformation methods ***/
    HRESULT (STDMETHODCALLTYPE *add_AccessChanged)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation *This,
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_AccessChanged)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation *This,
        EventRegistrationToken cookie);

    HRESULT (STDMETHODCALLTYPE *get_CurrentStatus)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessStatus *status);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationVtbl;

interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation {
    CONST_VTBL __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDeviceAccessInformation methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_add_AccessChanged(This,handler,cookie) (This)->lpVtbl->add_AccessChanged(This,handler,cookie)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_remove_AccessChanged(This,cookie) (This)->lpVtbl->remove_AccessChanged(This,cookie)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_get_CurrentStatus(This,status) (This)->lpVtbl->get_CurrentStatus(This,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_QueryInterface(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_AddRef(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_Release(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_GetIids(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_GetTrustLevel(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDeviceAccessInformation methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_add_AccessChanged(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation* This,__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_AccessChanged(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_remove_AccessChanged(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_AccessChanged(This,cookie);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_get_CurrentStatus(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation* This,__x_ABI_CWindows_CDevices_CEnumeration_CDeviceAccessStatus *status) {
    return This->lpVtbl->get_CurrentStatus(This,status);
}
#endif
#ifdef WIDL_using_Windows_Devices_Enumeration
#define IID_IDeviceAccessInformation IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation
#define IDeviceAccessInformationVtbl __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationVtbl
#define IDeviceAccessInformation __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation
#define IDeviceAccessInformation_QueryInterface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_QueryInterface
#define IDeviceAccessInformation_AddRef __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_AddRef
#define IDeviceAccessInformation_Release __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_Release
#define IDeviceAccessInformation_GetIids __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_GetIids
#define IDeviceAccessInformation_GetRuntimeClassName __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_GetRuntimeClassName
#define IDeviceAccessInformation_GetTrustLevel __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_GetTrustLevel
#define IDeviceAccessInformation_add_AccessChanged __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_add_AccessChanged
#define IDeviceAccessInformation_remove_AccessChanged __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_remove_AccessChanged
#define IDeviceAccessInformation_get_CurrentStatus __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_get_CurrentStatus
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDeviceAccessInformationStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics, 0x574bd3d3, 0x5f30, 0x45cd, 0x8a,0x94, 0x72,0x4f,0xe5,0x97,0x30,0x84);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Enumeration {
                MIDL_INTERFACE("574bd3d3-5f30-45cd-8a94-724fe5973084")
                IDeviceAccessInformationStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateFromId(
                        HSTRING device_id,
                        ABI::Windows::Devices::Enumeration::IDeviceAccessInformation **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateFromDeviceClassId(
                        GUID device_class_id,
                        ABI::Windows::Devices::Enumeration::IDeviceAccessInformation **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateFromDeviceClass(
                        ABI::Windows::Devices::Enumeration::DeviceClass device_class,
                        ABI::Windows::Devices::Enumeration::IDeviceAccessInformation **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics, 0x574bd3d3, 0x5f30, 0x45cd, 0x8a,0x94, 0x72,0x4f,0xe5,0x97,0x30,0x84)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics *This,
        TrustLevel *trustLevel);

    /*** IDeviceAccessInformationStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateFromId)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics *This,
        HSTRING device_id,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation **value);

    HRESULT (STDMETHODCALLTYPE *CreateFromDeviceClassId)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics *This,
        GUID device_class_id,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation **value);

    HRESULT (STDMETHODCALLTYPE *CreateFromDeviceClass)(
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass device_class,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation **value);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDeviceAccessInformationStatics methods ***/
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_CreateFromId(This,device_id,value) (This)->lpVtbl->CreateFromId(This,device_id,value)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_CreateFromDeviceClassId(This,device_class_id,value) (This)->lpVtbl->CreateFromDeviceClassId(This,device_class_id,value)
#define __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_CreateFromDeviceClass(This,device_class,value) (This)->lpVtbl->CreateFromDeviceClass(This,device_class,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_QueryInterface(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_AddRef(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_Release(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_GetIids(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDeviceAccessInformationStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_CreateFromId(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics* This,HSTRING device_id,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation **value) {
    return This->lpVtbl->CreateFromId(This,device_id,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_CreateFromDeviceClassId(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics* This,GUID device_class_id,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation **value) {
    return This->lpVtbl->CreateFromDeviceClassId(This,device_class_id,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_CreateFromDeviceClass(__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics* This,__x_ABI_CWindows_CDevices_CEnumeration_CDeviceClass device_class,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation **value) {
    return This->lpVtbl->CreateFromDeviceClass(This,device_class,value);
}
#endif
#ifdef WIDL_using_Windows_Devices_Enumeration
#define IID_IDeviceAccessInformationStatics IID___x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics
#define IDeviceAccessInformationStaticsVtbl __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStaticsVtbl
#define IDeviceAccessInformationStatics __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics
#define IDeviceAccessInformationStatics_QueryInterface __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_QueryInterface
#define IDeviceAccessInformationStatics_AddRef __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_AddRef
#define IDeviceAccessInformationStatics_Release __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_Release
#define IDeviceAccessInformationStatics_GetIids __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_GetIids
#define IDeviceAccessInformationStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_GetRuntimeClassName
#define IDeviceAccessInformationStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_GetTrustLevel
#define IDeviceAccessInformationStatics_CreateFromId __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_CreateFromId
#define IDeviceAccessInformationStatics_CreateFromDeviceClassId __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_CreateFromDeviceClassId
#define IDeviceAccessInformationStatics_CreateFromDeviceClass __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_CreateFromDeviceClass
#endif /* WIDL_using_Windows_Devices_Enumeration */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformationStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Enumeration.DeviceInformation
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Enumeration_DeviceInformation_DEFINED
#define RUNTIMECLASS_Windows_Devices_Enumeration_DeviceInformation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceInformation[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','I','n','f','o','r','m','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceInformation[] = L"Windows.Devices.Enumeration.DeviceInformation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceInformation[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','I','n','f','o','r','m','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Enumeration_DeviceInformation_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Enumeration.DeviceInformationCollection
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Enumeration_DeviceInformationCollection_DEFINED
#define RUNTIMECLASS_Windows_Devices_Enumeration_DeviceInformationCollection_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceInformationCollection[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','I','n','f','o','r','m','a','t','i','o','n','C','o','l','l','e','c','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceInformationCollection[] = L"Windows.Devices.Enumeration.DeviceInformationCollection";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceInformationCollection[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','I','n','f','o','r','m','a','t','i','o','n','C','o','l','l','e','c','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Enumeration_DeviceInformationCollection_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Enumeration.DeviceInformationUpdate
 */
#ifndef RUNTIMECLASS_Windows_Devices_Enumeration_DeviceInformationUpdate_DEFINED
#define RUNTIMECLASS_Windows_Devices_Enumeration_DeviceInformationUpdate_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceInformationUpdate[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','I','n','f','o','r','m','a','t','i','o','n','U','p','d','a','t','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceInformationUpdate[] = L"Windows.Devices.Enumeration.DeviceInformationUpdate";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceInformationUpdate[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','I','n','f','o','r','m','a','t','i','o','n','U','p','d','a','t','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Enumeration_DeviceInformationUpdate_DEFINED */

/*
 * Class Windows.Devices.Enumeration.DeviceThumbnail
 */
#ifndef RUNTIMECLASS_Windows_Devices_Enumeration_DeviceThumbnail_DEFINED
#define RUNTIMECLASS_Windows_Devices_Enumeration_DeviceThumbnail_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceThumbnail[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','T','h','u','m','b','n','a','i','l',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceThumbnail[] = L"Windows.Devices.Enumeration.DeviceThumbnail";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceThumbnail[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','T','h','u','m','b','n','a','i','l',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Enumeration_DeviceThumbnail_DEFINED */

/*
 * Class Windows.Devices.Enumeration.DeviceWatcher
 */
#ifndef RUNTIMECLASS_Windows_Devices_Enumeration_DeviceWatcher_DEFINED
#define RUNTIMECLASS_Windows_Devices_Enumeration_DeviceWatcher_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceWatcher[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','W','a','t','c','h','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceWatcher[] = L"Windows.Devices.Enumeration.DeviceWatcher";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceWatcher[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','W','a','t','c','h','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Enumeration_DeviceWatcher_DEFINED */

/*
 * Class Windows.Devices.Enumeration.EnclosureLocation
 */
#ifndef RUNTIMECLASS_Windows_Devices_Enumeration_EnclosureLocation_DEFINED
#define RUNTIMECLASS_Windows_Devices_Enumeration_EnclosureLocation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Enumeration_EnclosureLocation[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','E','n','c','l','o','s','u','r','e','L','o','c','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_EnclosureLocation[] = L"Windows.Devices.Enumeration.EnclosureLocation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_EnclosureLocation[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','E','n','c','l','o','s','u','r','e','L','o','c','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Enumeration_EnclosureLocation_DEFINED */

/*
 * Class Windows.Devices.Enumeration.DeviceAccessChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Enumeration_DeviceAccessChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Devices_Enumeration_DeviceAccessChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceAccessChangedEventArgs[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','A','c','c','e','s','s','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceAccessChangedEventArgs[] = L"Windows.Devices.Enumeration.DeviceAccessChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceAccessChangedEventArgs[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','A','c','c','e','s','s','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Enumeration_DeviceAccessChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Enumeration.DeviceAccessInformation
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Enumeration_DeviceAccessInformation_DEFINED
#define RUNTIMECLASS_Windows_Devices_Enumeration_DeviceAccessInformation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceAccessInformation[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','A','c','c','e','s','s','I','n','f','o','r','m','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceAccessInformation[] = L"Windows.Devices.Enumeration.DeviceAccessInformation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Enumeration_DeviceAccessInformation[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','E','n','u','m','e','r','a','t','i','o','n','.','D','e','v','i','c','e','A','c','c','e','s','s','I','n','f','o','r','m','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Enumeration_DeviceAccessInformation_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IVectorView<ABI::Windows::Devices::Enumeration::DeviceInformation* > interface
 */
#ifndef ____FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation, 0xe170688f, 0x3495, 0x5bf6, 0xaa,0xb5, 0x9c,0xac,0x17,0xe0,0xf1,0x0f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("e170688f-3495-5bf6-aab5-9cac17e0f10f")
                IVectorView<ABI::Windows::Devices::Enumeration::DeviceInformation* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceInformation*, ABI::Windows::Devices::Enumeration::IDeviceInformation* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation, 0xe170688f, 0x3495, 0x5bf6, 0xaa,0xb5, 0x9c,0xac,0x17,0xe0,0xf1,0x0f)
#endif
#else
typedef struct __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        UINT32 index,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl;

interface __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation {
    CONST_VTBL __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release(__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids(__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName(__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel(__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetAt(__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,UINT32 index,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_Size(__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_IndexOf(__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetMany(__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_DeviceInformation IID___FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation
#define IVectorView_DeviceInformationVtbl __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl
#define IVectorView_DeviceInformation __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation
#define IVectorView_DeviceInformation_QueryInterface __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface
#define IVectorView_DeviceInformation_AddRef __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef
#define IVectorView_DeviceInformation_Release __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release
#define IVectorView_DeviceInformation_GetIids __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids
#define IVectorView_DeviceInformation_GetRuntimeClassName __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName
#define IVectorView_DeviceInformation_GetTrustLevel __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel
#define IVectorView_DeviceInformation_GetAt __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetAt
#define IVectorView_DeviceInformation_get_Size __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_Size
#define IVectorView_DeviceInformation_IndexOf __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_IndexOf
#define IVectorView_DeviceInformation_GetMany __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Devices::Enumeration::DeviceInformation* > interface
 */
#ifndef ____FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation, 0xdd9f8a5d, 0xec98, 0x5f4b, 0xa3,0xea, 0x9c,0x8b,0x5a,0xd5,0x3c,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("dd9f8a5d-ec98-5f4b-a3ea-9c8b5ad53c4b")
                IIterable<ABI::Windows::Devices::Enumeration::DeviceInformation* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceInformation*, ABI::Windows::Devices::Enumeration::IDeviceInformation* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation, 0xdd9f8a5d, 0xec98, 0x5f4b, 0xa3,0xea, 0x9c,0x8b,0x5a,0xd5,0x3c,0x4b)
#endif
#else
typedef struct __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation **value);

    END_INTERFACE
} __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl;

interface __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation {
    CONST_VTBL __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
#define __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(__FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(__FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release(__FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids(__FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName(__FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel(__FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_First(__FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_DeviceInformation IID___FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation
#define IIterable_DeviceInformationVtbl __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl
#define IIterable_DeviceInformation __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation
#define IIterable_DeviceInformation_QueryInterface __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface
#define IIterable_DeviceInformation_AddRef __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef
#define IIterable_DeviceInformation_Release __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release
#define IIterable_DeviceInformation_GetIids __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids
#define IIterable_DeviceInformation_GetRuntimeClassName __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName
#define IIterable_DeviceInformation_GetTrustLevel __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel
#define IIterable_DeviceInformation_First __FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Devices::Enumeration::DeviceInformation* > interface
 */
#ifndef ____FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation, 0x6f85d843, 0xe8ab, 0x5b46, 0x85,0xd7, 0x32,0x7c,0x58,0xd1,0x87,0x12);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("6f85d843-e8ab-5b46-85d7-327c58d18712")
                IIterator<ABI::Windows::Devices::Enumeration::DeviceInformation* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceInformation*, ABI::Windows::Devices::Enumeration::IDeviceInformation* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation, 0x6f85d843, 0xe8ab, 0x5b46, 0x85,0xd7, 0x32,0x7c,0x58,0xd1,0x87,0x12)
#endif
#else
typedef struct __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        UINT32 items_size,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl;

interface __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation {
    CONST_VTBL __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release(__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids(__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName(__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel(__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
static inline HRESULT __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_Current(__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation **value) {
    return This->lpVtbl->get_Current(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_HasCurrent(__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,boolean *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_MoveNext(__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,boolean *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static inline HRESULT __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetMany(__FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,UINT32 items_size,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_DeviceInformation IID___FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation
#define IIterator_DeviceInformationVtbl __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl
#define IIterator_DeviceInformation __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation
#define IIterator_DeviceInformation_QueryInterface __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface
#define IIterator_DeviceInformation_AddRef __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef
#define IIterator_DeviceInformation_Release __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release
#define IIterator_DeviceInformation_GetIids __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids
#define IIterator_DeviceInformation_GetRuntimeClassName __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName
#define IIterator_DeviceInformation_GetTrustLevel __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel
#define IIterator_DeviceInformation_get_Current __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_Current
#define IIterator_DeviceInformation_get_HasCurrent __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_HasCurrent
#define IIterator_DeviceInformation_MoveNext __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_MoveNext
#define IIterator_DeviceInformation_GetMany __FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformation* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation, 0xbb483df2, 0x7bb6, 0x5923, 0xa2,0x8d, 0x83,0x42,0xec,0x30,0x04,0x6b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("bb483df2-7bb6-5923-a28d-8342ec30046b")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformation* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceInformation*, ABI::Windows::Devices::Enumeration::IDeviceInformation* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation, 0xbb483df2, 0x7bb6, 0x5923, 0xa2,0x8d, 0x83,0x42,0xec,0x30,0x04,0x6b)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_DeviceInformation IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation
#define IAsyncOperationCompletedHandler_DeviceInformationVtbl __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl
#define IAsyncOperationCompletedHandler_DeviceInformation __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation
#define IAsyncOperationCompletedHandler_DeviceInformation_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface
#define IAsyncOperationCompletedHandler_DeviceInformation_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef
#define IAsyncOperationCompletedHandler_DeviceInformation_Release __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release
#define IAsyncOperationCompletedHandler_DeviceInformation_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection, 0x4a458732, 0x527e, 0x5c73, 0x9a,0x68, 0xa7,0x3d,0xa3,0x70,0xf7,0x82);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("4a458732-527e-5c73-9a68-a73da370f782")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceInformationCollection*, ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Enumeration::DeviceInformation* >* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection, 0x4a458732, 0x527e, 0x5c73, 0x9a,0x68, 0xa7,0x3d,0xa3,0x70,0xf7,0x82)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollectionVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_Release(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_DeviceInformationCollection IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection
#define IAsyncOperationCompletedHandler_DeviceInformationCollectionVtbl __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollectionVtbl
#define IAsyncOperationCompletedHandler_DeviceInformationCollection __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection
#define IAsyncOperationCompletedHandler_DeviceInformationCollection_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_QueryInterface
#define IAsyncOperationCompletedHandler_DeviceInformationCollection_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_AddRef
#define IAsyncOperationCompletedHandler_DeviceInformationCollection_Release __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_Release
#define IAsyncOperationCompletedHandler_DeviceInformationCollection_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail, 0x86d455b2, 0xd795, 0x554c, 0x9c,0x31, 0xbf,0x65,0x39,0x34,0x9c,0x19);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("86d455b2-d795-554c-9c31-bf6539349c19")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceThumbnail*, ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail, 0x86d455b2, 0xd795, 0x554c, 0x9c,0x31, 0xbf,0x65,0x39,0x34,0x9c,0x19)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnailVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This,
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnailVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnailVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_Release(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This,__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_DeviceThumbnail IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail
#define IAsyncOperationCompletedHandler_DeviceThumbnailVtbl __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnailVtbl
#define IAsyncOperationCompletedHandler_DeviceThumbnail __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail
#define IAsyncOperationCompletedHandler_DeviceThumbnail_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_QueryInterface
#define IAsyncOperationCompletedHandler_DeviceThumbnail_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_AddRef
#define IAsyncOperationCompletedHandler_DeviceThumbnail_Release __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_Release
#define IAsyncOperationCompletedHandler_DeviceThumbnail_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMapView<HSTRING,IInspectable* > interface
 */
#ifndef ____FIMapView_2_HSTRING_IInspectable_INTERFACE_DEFINED__
#define ____FIMapView_2_HSTRING_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMapView_2_HSTRING_IInspectable, 0xbb78502a, 0xf79d, 0x54fa, 0x92,0xc9, 0x90,0xc5,0x03,0x9f,0xdf,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("bb78502a-f79d-54fa-92c9-90c5039fdf7e")
                IMapView<HSTRING,IInspectable* > : IMapView_impl<HSTRING, IInspectable* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMapView_2_HSTRING_IInspectable, 0xbb78502a, 0xf79d, 0x54fa, 0x92,0xc9, 0x90,0xc5,0x03,0x9f,0xdf,0x7e)
#endif
#else
typedef struct __FIMapView_2_HSTRING_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMapView_2_HSTRING_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMapView_2_HSTRING_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMapView_2_HSTRING_IInspectable *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMapView_2_HSTRING_IInspectable *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMapView_2_HSTRING_IInspectable *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMapView_2_HSTRING_IInspectable *This,
        TrustLevel *trustLevel);

    /*** IMapView<HSTRING,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMapView_2_HSTRING_IInspectable *This,
        HSTRING key,
        IInspectable **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMapView_2_HSTRING_IInspectable *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMapView_2_HSTRING_IInspectable *This,
        HSTRING key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *Split)(
        __FIMapView_2_HSTRING_IInspectable *This,
        __FIMapView_2_HSTRING_IInspectable **first,
        __FIMapView_2_HSTRING_IInspectable **second);

    END_INTERFACE
} __FIMapView_2_HSTRING_IInspectableVtbl;

interface __FIMapView_2_HSTRING_IInspectable {
    CONST_VTBL __FIMapView_2_HSTRING_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMapView_2_HSTRING_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMapView_2_HSTRING_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMapView_2_HSTRING_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMapView_2_HSTRING_IInspectable_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMapView_2_HSTRING_IInspectable_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMapView_2_HSTRING_IInspectable_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMapView<HSTRING,IInspectable* > methods ***/
#define __FIMapView_2_HSTRING_IInspectable_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMapView_2_HSTRING_IInspectable_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMapView_2_HSTRING_IInspectable_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMapView_2_HSTRING_IInspectable_Split(This,first,second) (This)->lpVtbl->Split(This,first,second)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMapView_2_HSTRING_IInspectable_QueryInterface(__FIMapView_2_HSTRING_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMapView_2_HSTRING_IInspectable_AddRef(__FIMapView_2_HSTRING_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMapView_2_HSTRING_IInspectable_Release(__FIMapView_2_HSTRING_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMapView_2_HSTRING_IInspectable_GetIids(__FIMapView_2_HSTRING_IInspectable* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMapView_2_HSTRING_IInspectable_GetRuntimeClassName(__FIMapView_2_HSTRING_IInspectable* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMapView_2_HSTRING_IInspectable_GetTrustLevel(__FIMapView_2_HSTRING_IInspectable* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMapView<HSTRING,IInspectable* > methods ***/
static inline HRESULT __FIMapView_2_HSTRING_IInspectable_Lookup(__FIMapView_2_HSTRING_IInspectable* This,HSTRING key,IInspectable **value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMapView_2_HSTRING_IInspectable_get_Size(__FIMapView_2_HSTRING_IInspectable* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMapView_2_HSTRING_IInspectable_HasKey(__FIMapView_2_HSTRING_IInspectable* This,HSTRING key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMapView_2_HSTRING_IInspectable_Split(__FIMapView_2_HSTRING_IInspectable* This,__FIMapView_2_HSTRING_IInspectable **first,__FIMapView_2_HSTRING_IInspectable **second) {
    return This->lpVtbl->Split(This,first,second);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMapView_HSTRING_IInspectable IID___FIMapView_2_HSTRING_IInspectable
#define IMapView_HSTRING_IInspectableVtbl __FIMapView_2_HSTRING_IInspectableVtbl
#define IMapView_HSTRING_IInspectable __FIMapView_2_HSTRING_IInspectable
#define IMapView_HSTRING_IInspectable_QueryInterface __FIMapView_2_HSTRING_IInspectable_QueryInterface
#define IMapView_HSTRING_IInspectable_AddRef __FIMapView_2_HSTRING_IInspectable_AddRef
#define IMapView_HSTRING_IInspectable_Release __FIMapView_2_HSTRING_IInspectable_Release
#define IMapView_HSTRING_IInspectable_GetIids __FIMapView_2_HSTRING_IInspectable_GetIids
#define IMapView_HSTRING_IInspectable_GetRuntimeClassName __FIMapView_2_HSTRING_IInspectable_GetRuntimeClassName
#define IMapView_HSTRING_IInspectable_GetTrustLevel __FIMapView_2_HSTRING_IInspectable_GetTrustLevel
#define IMapView_HSTRING_IInspectable_Lookup __FIMapView_2_HSTRING_IInspectable_Lookup
#define IMapView_HSTRING_IInspectable_get_Size __FIMapView_2_HSTRING_IInspectable_get_Size
#define IMapView_HSTRING_IInspectable_HasKey __FIMapView_2_HSTRING_IInspectable_HasKey
#define IMapView_HSTRING_IInspectable_Split __FIMapView_2_HSTRING_IInspectable_Split
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMapView_2_HSTRING_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformation* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation, 0x07faa053, 0xeb2f, 0x5cba, 0xb2,0x5b, 0xd9,0xd5,0x7b,0xe6,0x71,0x5f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("07faa053-eb2f-5cba-b25b-d9d57be6715f")
            IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformation* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceInformation*, ABI::Windows::Devices::Enumeration::IDeviceInformation* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation, 0x07faa053, 0xeb2f, 0x5cba, 0xb2,0x5b, 0xd9,0xd5,0x7b,0xe6,0x71,0x5f)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl;

interface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation {
    CONST_VTBL __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_put_Completed(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_Completed(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformation **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetResults(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation* This,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_DeviceInformation IID___FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation
#define IAsyncOperation_DeviceInformationVtbl __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationVtbl
#define IAsyncOperation_DeviceInformation __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation
#define IAsyncOperation_DeviceInformation_QueryInterface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface
#define IAsyncOperation_DeviceInformation_AddRef __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef
#define IAsyncOperation_DeviceInformation_Release __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_Release
#define IAsyncOperation_DeviceInformation_GetIids __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetIids
#define IAsyncOperation_DeviceInformation_GetRuntimeClassName __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetRuntimeClassName
#define IAsyncOperation_DeviceInformation_GetTrustLevel __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetTrustLevel
#define IAsyncOperation_DeviceInformation_put_Completed __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_put_Completed
#define IAsyncOperation_DeviceInformation_get_Completed __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_get_Completed
#define IAsyncOperation_DeviceInformation_GetResults __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection, 0x45180254, 0x082e, 0x5274, 0xb2,0xe7, 0xac,0x05,0x17,0xf4,0x4d,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("45180254-082e-5274-b2e7-ac0517f44d07")
            IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceInformationCollection*, ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Devices::Enumeration::DeviceInformation* >* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection, 0x45180254, 0x082e, 0x5274, 0xb2,0xe7, 0xac,0x05,0x17,0xf4,0x4d,0x07)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *This,
        __FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollectionVtbl;

interface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection {
    CONST_VTBL __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_QueryInterface(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_AddRef(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_Release(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetIids(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetTrustLevel(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceInformationCollection* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_put_Completed(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_get_Completed(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetResults(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection* This,__FIVectorView_1_Windows__CDevices__CEnumeration__CDeviceInformation **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_DeviceInformationCollection IID___FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection
#define IAsyncOperation_DeviceInformationCollectionVtbl __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollectionVtbl
#define IAsyncOperation_DeviceInformationCollection __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection
#define IAsyncOperation_DeviceInformationCollection_QueryInterface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_QueryInterface
#define IAsyncOperation_DeviceInformationCollection_AddRef __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_AddRef
#define IAsyncOperation_DeviceInformationCollection_Release __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_Release
#define IAsyncOperation_DeviceInformationCollection_GetIids __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetIids
#define IAsyncOperation_DeviceInformationCollection_GetRuntimeClassName __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetRuntimeClassName
#define IAsyncOperation_DeviceInformationCollection_GetTrustLevel __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetTrustLevel
#define IAsyncOperation_DeviceInformationCollection_put_Completed __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_put_Completed
#define IAsyncOperation_DeviceInformationCollection_get_Completed __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_get_Completed
#define IAsyncOperation_DeviceInformationCollection_GetResults __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceInformationCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail, 0xbac083a3, 0x3a19, 0x5072, 0x9d,0x90, 0x13,0x33,0x23,0xa0,0x49,0xba);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("bac083a3-3a19-5072-9d90-133323a049ba")
            IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceThumbnail*, ABI::Windows::Storage::Streams::IRandomAccessStreamWithContentType* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail, 0xbac083a3, 0x3a19, 0x5072, 0x9d,0x90, 0x13,0x33,0x23,0xa0,0x49,0xba)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnailVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *This,
        __x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnailVtbl;

interface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail {
    CONST_VTBL __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnailVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_QueryInterface(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_AddRef(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_Release(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetIids(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetTrustLevel(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Enumeration::DeviceThumbnail* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_put_Completed(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_get_Completed(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CEnumeration__CDeviceThumbnail **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetResults(__FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail* This,__x_ABI_CWindows_CStorage_CStreams_CIRandomAccessStreamWithContentType **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_DeviceThumbnail IID___FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail
#define IAsyncOperation_DeviceThumbnailVtbl __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnailVtbl
#define IAsyncOperation_DeviceThumbnail __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail
#define IAsyncOperation_DeviceThumbnail_QueryInterface __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_QueryInterface
#define IAsyncOperation_DeviceThumbnail_AddRef __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_AddRef
#define IAsyncOperation_DeviceThumbnail_Release __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_Release
#define IAsyncOperation_DeviceThumbnail_GetIids __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetIids
#define IAsyncOperation_DeviceThumbnail_GetRuntimeClassName __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetRuntimeClassName
#define IAsyncOperation_DeviceThumbnail_GetTrustLevel __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetTrustLevel
#define IAsyncOperation_DeviceThumbnail_put_Completed __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_put_Completed
#define IAsyncOperation_DeviceThumbnail_get_Completed __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_get_Completed
#define IAsyncOperation_DeviceThumbnail_GetResults __FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CDevices__CEnumeration__CDeviceThumbnail_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable, 0x9234630f, 0x1ff4, 0x54f6, 0x9e,0x3f, 0xac,0x20,0x36,0x9b,0x77,0x25);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("9234630f-1ff4-54f6-9e3f-ac20369b7725")
            ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceWatcher*, ABI::Windows::Devices::Enumeration::IDeviceWatcher* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable, 0x9234630f, 0x1ff4, 0x54f6, 0x9e,0x3f, 0xac,0x20,0x36,0x9b,0x77,0x25)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_Release(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable* This,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_DeviceWatcher_IInspectable IID___FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable
#define ITypedEventHandler_DeviceWatcher_IInspectableVtbl __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectableVtbl
#define ITypedEventHandler_DeviceWatcher_IInspectable __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable
#define ITypedEventHandler_DeviceWatcher_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_QueryInterface
#define ITypedEventHandler_DeviceWatcher_IInspectable_AddRef __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_AddRef
#define ITypedEventHandler_DeviceWatcher_IInspectable_Release __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_Release
#define ITypedEventHandler_DeviceWatcher_IInspectable_Invoke __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformation* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation, 0x03c5a07b, 0x990c, 0x5d09, 0xb0,0xb8, 0x57,0x34,0xea,0xa3,0x82,0x22);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("03c5a07b-990c-5d09-b0b8-5734eaa38222")
            ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformation* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceWatcher*, ABI::Windows::Devices::Enumeration::IDeviceWatcher* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceInformation*, ABI::Windows::Devices::Enumeration::IDeviceInformation* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation, 0x03c5a07b, 0x990c, 0x5d09, 0xb0,0xb8, 0x57,0x34,0xea,0xa3,0x82,0x22)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation *This);

    /*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *sender,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationVtbl;

interface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation {
    CONST_VTBL __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_Release(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformation* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_Invoke(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation* This,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *sender,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformation *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_DeviceWatcher_DeviceInformation IID___FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation
#define ITypedEventHandler_DeviceWatcher_DeviceInformationVtbl __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationVtbl
#define ITypedEventHandler_DeviceWatcher_DeviceInformation __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation
#define ITypedEventHandler_DeviceWatcher_DeviceInformation_QueryInterface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_QueryInterface
#define ITypedEventHandler_DeviceWatcher_DeviceInformation_AddRef __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_AddRef
#define ITypedEventHandler_DeviceWatcher_DeviceInformation_Release __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_Release
#define ITypedEventHandler_DeviceWatcher_DeviceInformation_Invoke __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformation_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformationUpdate* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate, 0x906f1254, 0x79ad, 0x54fc, 0x93,0xc4, 0xcd,0xb9,0x9b,0x43,0x78,0x99);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("906f1254-79ad-54fc-93c4-cdb99b437899")
            ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformationUpdate* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceWatcher*, ABI::Windows::Devices::Enumeration::IDeviceWatcher* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceInformationUpdate*, ABI::Windows::Devices::Enumeration::IDeviceInformationUpdate* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate, 0x906f1254, 0x79ad, 0x54fc, 0x93,0xc4, 0xcd,0xb9,0x9b,0x43,0x78,0x99)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate *This);

    /*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformationUpdate* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *sender,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdateVtbl;

interface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate {
    CONST_VTBL __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformationUpdate* > methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_QueryInterface(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_AddRef(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_Release(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceWatcher*,ABI::Windows::Devices::Enumeration::DeviceInformationUpdate* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_Invoke(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate* This,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceWatcher *sender,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceInformationUpdate *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_DeviceWatcher_DeviceInformationUpdate IID___FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate
#define ITypedEventHandler_DeviceWatcher_DeviceInformationUpdateVtbl __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdateVtbl
#define ITypedEventHandler_DeviceWatcher_DeviceInformationUpdate __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate
#define ITypedEventHandler_DeviceWatcher_DeviceInformationUpdate_QueryInterface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_QueryInterface
#define ITypedEventHandler_DeviceWatcher_DeviceInformationUpdate_AddRef __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_AddRef
#define ITypedEventHandler_DeviceWatcher_DeviceInformationUpdate_Release __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_Release
#define ITypedEventHandler_DeviceWatcher_DeviceInformationUpdate_Invoke __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceWatcher_Windows__CDevices__CEnumeration__CDeviceInformationUpdate_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceAccessInformation*,ABI::Windows::Devices::Enumeration::DeviceAccessChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs, 0x4c71d028, 0xb793, 0x5bce, 0xae,0x59, 0xfa,0x77,0xf4,0x5a,0x40,0xd8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("4c71d028-b793-5bce-ae59-fa77f45a40d8")
            ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceAccessInformation*,ABI::Windows::Devices::Enumeration::DeviceAccessChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceAccessInformation*, ABI::Windows::Devices::Enumeration::IDeviceAccessInformation* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Enumeration::DeviceAccessChangedEventArgs*, ABI::Windows::Devices::Enumeration::IDeviceAccessChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs, 0x4c71d028, 0xb793, 0x5bce, 0xae,0x59, 0xfa,0x77,0xf4,0x5a,0x40,0xd8)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceAccessInformation*,ABI::Windows::Devices::Enumeration::DeviceAccessChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs *This,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation *sender,
        __x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceAccessInformation*,ABI::Windows::Devices::Enumeration::DeviceAccessChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Devices::Enumeration::DeviceAccessInformation*,ABI::Windows::Devices::Enumeration::DeviceAccessChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs* This,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessInformation *sender,__x_ABI_CWindows_CDevices_CEnumeration_CIDeviceAccessChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_DeviceAccessInformation_DeviceAccessChangedEventArgs IID___FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs
#define ITypedEventHandler_DeviceAccessInformation_DeviceAccessChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgsVtbl
#define ITypedEventHandler_DeviceAccessInformation_DeviceAccessChangedEventArgs __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs
#define ITypedEventHandler_DeviceAccessInformation_DeviceAccessChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_QueryInterface
#define ITypedEventHandler_DeviceAccessInformation_DeviceAccessChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_AddRef
#define ITypedEventHandler_DeviceAccessInformation_DeviceAccessChangedEventArgs_Release __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_Release
#define ITypedEventHandler_DeviceAccessInformation_DeviceAccessChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CDevices__CEnumeration__CDeviceAccessInformation_Windows__CDevices__CEnumeration__CDeviceAccessChangedEventArgs_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_devices_enumeration_h__ */
