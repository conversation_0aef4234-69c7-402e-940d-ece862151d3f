/*** Autogenerated by WIDL 10.12 from include/d3d10effect.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __d3d10effect_h__
#define __d3d10effect_h__

/* Forward declarations */

#ifndef __ID3D10EffectType_FWD_DEFINED__
#define __ID3D10EffectType_FWD_DEFINED__
typedef interface ID3D10EffectType ID3D10EffectType;
#ifdef __cplusplus
interface ID3D10EffectType;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectVariable_FWD_DEFINED__
#define __ID3D10EffectVariable_FWD_DEFINED__
typedef interface ID3D10EffectVariable ID3D10EffectVariable;
#ifdef __cplusplus
interface ID3D10EffectVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectConstantBuffer_FWD_DEFINED__
#define __ID3D10EffectConstantBuffer_FWD_DEFINED__
typedef interface ID3D10EffectConstantBuffer ID3D10EffectConstantBuffer;
#ifdef __cplusplus
interface ID3D10EffectConstantBuffer;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectScalarVariable_FWD_DEFINED__
#define __ID3D10EffectScalarVariable_FWD_DEFINED__
typedef interface ID3D10EffectScalarVariable ID3D10EffectScalarVariable;
#ifdef __cplusplus
interface ID3D10EffectScalarVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectVectorVariable_FWD_DEFINED__
#define __ID3D10EffectVectorVariable_FWD_DEFINED__
typedef interface ID3D10EffectVectorVariable ID3D10EffectVectorVariable;
#ifdef __cplusplus
interface ID3D10EffectVectorVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectMatrixVariable_FWD_DEFINED__
#define __ID3D10EffectMatrixVariable_FWD_DEFINED__
typedef interface ID3D10EffectMatrixVariable ID3D10EffectMatrixVariable;
#ifdef __cplusplus
interface ID3D10EffectMatrixVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectStringVariable_FWD_DEFINED__
#define __ID3D10EffectStringVariable_FWD_DEFINED__
typedef interface ID3D10EffectStringVariable ID3D10EffectStringVariable;
#ifdef __cplusplus
interface ID3D10EffectStringVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectShaderResourceVariable_FWD_DEFINED__
#define __ID3D10EffectShaderResourceVariable_FWD_DEFINED__
typedef interface ID3D10EffectShaderResourceVariable ID3D10EffectShaderResourceVariable;
#ifdef __cplusplus
interface ID3D10EffectShaderResourceVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectRenderTargetViewVariable_FWD_DEFINED__
#define __ID3D10EffectRenderTargetViewVariable_FWD_DEFINED__
typedef interface ID3D10EffectRenderTargetViewVariable ID3D10EffectRenderTargetViewVariable;
#ifdef __cplusplus
interface ID3D10EffectRenderTargetViewVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectDepthStencilViewVariable_FWD_DEFINED__
#define __ID3D10EffectDepthStencilViewVariable_FWD_DEFINED__
typedef interface ID3D10EffectDepthStencilViewVariable ID3D10EffectDepthStencilViewVariable;
#ifdef __cplusplus
interface ID3D10EffectDepthStencilViewVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectShaderVariable_FWD_DEFINED__
#define __ID3D10EffectShaderVariable_FWD_DEFINED__
typedef interface ID3D10EffectShaderVariable ID3D10EffectShaderVariable;
#ifdef __cplusplus
interface ID3D10EffectShaderVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectBlendVariable_FWD_DEFINED__
#define __ID3D10EffectBlendVariable_FWD_DEFINED__
typedef interface ID3D10EffectBlendVariable ID3D10EffectBlendVariable;
#ifdef __cplusplus
interface ID3D10EffectBlendVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectDepthStencilVariable_FWD_DEFINED__
#define __ID3D10EffectDepthStencilVariable_FWD_DEFINED__
typedef interface ID3D10EffectDepthStencilVariable ID3D10EffectDepthStencilVariable;
#ifdef __cplusplus
interface ID3D10EffectDepthStencilVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectRasterizerVariable_FWD_DEFINED__
#define __ID3D10EffectRasterizerVariable_FWD_DEFINED__
typedef interface ID3D10EffectRasterizerVariable ID3D10EffectRasterizerVariable;
#ifdef __cplusplus
interface ID3D10EffectRasterizerVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectSamplerVariable_FWD_DEFINED__
#define __ID3D10EffectSamplerVariable_FWD_DEFINED__
typedef interface ID3D10EffectSamplerVariable ID3D10EffectSamplerVariable;
#ifdef __cplusplus
interface ID3D10EffectSamplerVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectTechnique_FWD_DEFINED__
#define __ID3D10EffectTechnique_FWD_DEFINED__
typedef interface ID3D10EffectTechnique ID3D10EffectTechnique;
#ifdef __cplusplus
interface ID3D10EffectTechnique;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10Effect_FWD_DEFINED__
#define __ID3D10Effect_FWD_DEFINED__
typedef interface ID3D10Effect ID3D10Effect;
#ifdef __cplusplus
interface ID3D10Effect;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectPool_FWD_DEFINED__
#define __ID3D10EffectPool_FWD_DEFINED__
typedef interface ID3D10EffectPool ID3D10EffectPool;
#ifdef __cplusplus
interface ID3D10EffectPool;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectPass_FWD_DEFINED__
#define __ID3D10EffectPass_FWD_DEFINED__
typedef interface ID3D10EffectPass ID3D10EffectPass;
#ifdef __cplusplus
interface ID3D10EffectPass;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10StateBlock_FWD_DEFINED__
#define __ID3D10StateBlock_FWD_DEFINED__
typedef interface ID3D10StateBlock ID3D10StateBlock;
#ifdef __cplusplus
interface ID3D10StateBlock;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <d3d10.h>
#include <d3d10shader.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __ID3D10EffectConstantBuffer_FWD_DEFINED__
#define __ID3D10EffectConstantBuffer_FWD_DEFINED__
typedef interface ID3D10EffectConstantBuffer ID3D10EffectConstantBuffer;
#ifdef __cplusplus
interface ID3D10EffectConstantBuffer;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectBlendVariable_FWD_DEFINED__
#define __ID3D10EffectBlendVariable_FWD_DEFINED__
typedef interface ID3D10EffectBlendVariable ID3D10EffectBlendVariable;
#ifdef __cplusplus
interface ID3D10EffectBlendVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectDepthStencilVariable_FWD_DEFINED__
#define __ID3D10EffectDepthStencilVariable_FWD_DEFINED__
typedef interface ID3D10EffectDepthStencilVariable ID3D10EffectDepthStencilVariable;
#ifdef __cplusplus
interface ID3D10EffectDepthStencilVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectDepthStencilViewVariable_FWD_DEFINED__
#define __ID3D10EffectDepthStencilViewVariable_FWD_DEFINED__
typedef interface ID3D10EffectDepthStencilViewVariable ID3D10EffectDepthStencilViewVariable;
#ifdef __cplusplus
interface ID3D10EffectDepthStencilViewVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectMatrixVariable_FWD_DEFINED__
#define __ID3D10EffectMatrixVariable_FWD_DEFINED__
typedef interface ID3D10EffectMatrixVariable ID3D10EffectMatrixVariable;
#ifdef __cplusplus
interface ID3D10EffectMatrixVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectPass_FWD_DEFINED__
#define __ID3D10EffectPass_FWD_DEFINED__
typedef interface ID3D10EffectPass ID3D10EffectPass;
#ifdef __cplusplus
interface ID3D10EffectPass;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectRasterizerVariable_FWD_DEFINED__
#define __ID3D10EffectRasterizerVariable_FWD_DEFINED__
typedef interface ID3D10EffectRasterizerVariable ID3D10EffectRasterizerVariable;
#ifdef __cplusplus
interface ID3D10EffectRasterizerVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectRenderTargetViewVariable_FWD_DEFINED__
#define __ID3D10EffectRenderTargetViewVariable_FWD_DEFINED__
typedef interface ID3D10EffectRenderTargetViewVariable ID3D10EffectRenderTargetViewVariable;
#ifdef __cplusplus
interface ID3D10EffectRenderTargetViewVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectSamplerVariable_FWD_DEFINED__
#define __ID3D10EffectSamplerVariable_FWD_DEFINED__
typedef interface ID3D10EffectSamplerVariable ID3D10EffectSamplerVariable;
#ifdef __cplusplus
interface ID3D10EffectSamplerVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectScalarVariable_FWD_DEFINED__
#define __ID3D10EffectScalarVariable_FWD_DEFINED__
typedef interface ID3D10EffectScalarVariable ID3D10EffectScalarVariable;
#ifdef __cplusplus
interface ID3D10EffectScalarVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectShaderVariable_FWD_DEFINED__
#define __ID3D10EffectShaderVariable_FWD_DEFINED__
typedef interface ID3D10EffectShaderVariable ID3D10EffectShaderVariable;
#ifdef __cplusplus
interface ID3D10EffectShaderVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectShaderResourceVariable_FWD_DEFINED__
#define __ID3D10EffectShaderResourceVariable_FWD_DEFINED__
typedef interface ID3D10EffectShaderResourceVariable ID3D10EffectShaderResourceVariable;
#ifdef __cplusplus
interface ID3D10EffectShaderResourceVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectStringVariable_FWD_DEFINED__
#define __ID3D10EffectStringVariable_FWD_DEFINED__
typedef interface ID3D10EffectStringVariable ID3D10EffectStringVariable;
#ifdef __cplusplus
interface ID3D10EffectStringVariable;
#endif /* __cplusplus */
#endif

#ifndef __ID3D10EffectVectorVariable_FWD_DEFINED__
#define __ID3D10EffectVectorVariable_FWD_DEFINED__
typedef interface ID3D10EffectVectorVariable ID3D10EffectVectorVariable;
#ifdef __cplusplus
interface ID3D10EffectVectorVariable;
#endif /* __cplusplus */
#endif

#ifndef D3D10_BYTES_FROM_BITS
#define D3D10_BYTES_FROM_BITS(x) (((x) + 7) >> 3)
#endif
#define D3D10_EFFECT_VARIABLE_POOLED (0x1)

#define D3D10_EFFECT_VARIABLE_ANNOTATION (0x2)

#define D3D10_EFFECT_VARIABLE_EXPLICIT_BIND_POINT (0x4)

typedef enum _D3D10_DEVICE_STATE_TYPES {
    D3D10_DST_SO_BUFFERS = 1,
    D3D10_DST_OM_RENDER_TARGETS = 2,
    D3D10_DST_OM_DEPTH_STENCIL_STATE = 3,
    D3D10_DST_OM_BLEND_STATE = 4,
    D3D10_DST_VS = 5,
    D3D10_DST_VS_SAMPLERS = 6,
    D3D10_DST_VS_SHADER_RESOURCES = 7,
    D3D10_DST_VS_CONSTANT_BUFFERS = 8,
    D3D10_DST_GS = 9,
    D3D10_DST_GS_SAMPLERS = 10,
    D3D10_DST_GS_SHADER_RESOURCES = 11,
    D3D10_DST_GS_CONSTANT_BUFFERS = 12,
    D3D10_DST_PS = 13,
    D3D10_DST_PS_SAMPLERS = 14,
    D3D10_DST_PS_SHADER_RESOURCES = 15,
    D3D10_DST_PS_CONSTANT_BUFFERS = 16,
    D3D10_DST_IA_VERTEX_BUFFERS = 17,
    D3D10_DST_IA_INDEX_BUFFER = 18,
    D3D10_DST_IA_INPUT_LAYOUT = 19,
    D3D10_DST_IA_PRIMITIVE_TOPOLOGY = 20,
    D3D10_DST_RS_VIEWPORTS = 21,
    D3D10_DST_RS_SCISSOR_RECTS = 22,
    D3D10_DST_RS_RASTERIZER_STATE = 23,
    D3D10_DST_PREDICATION = 24
} D3D10_DEVICE_STATE_TYPES;
typedef struct _D3D10_EFFECT_TYPE_DESC {
    const char *TypeName;
    D3D10_SHADER_VARIABLE_CLASS Class;
    D3D10_SHADER_VARIABLE_TYPE Type;
    UINT Elements;
    UINT Members;
    UINT Rows;
    UINT Columns;
    UINT PackedSize;
    UINT UnpackedSize;
    UINT Stride;
} D3D10_EFFECT_TYPE_DESC;
typedef struct _D3D10_EFFECT_VARIABLE_DESC {
    const char *Name;
    const char *Semantic;
    UINT Flags;
    UINT Annotations;
    UINT BufferOffset;
    UINT ExplicitBindPoint;
} D3D10_EFFECT_VARIABLE_DESC;
typedef struct _D3D10_TECHNIQUE_DESC {
    const char *Name;
    UINT Passes;
    UINT Annotations;
} D3D10_TECHNIQUE_DESC;
typedef struct _D3D10_STATE_BLOCK_MASK {
    BYTE VS;
    BYTE VSSamplers[2];
    BYTE VSShaderResources[16];
    BYTE VSConstantBuffers[2];
    BYTE GS;
    BYTE GSSamplers[2];
    BYTE GSShaderResources[16];
    BYTE GSConstantBuffers[2];
    BYTE PS;
    BYTE PSSamplers[2];
    BYTE PSShaderResources[16];
    BYTE PSConstantBuffers[2];
    BYTE IAVertexBuffers[2];
    BYTE IAIndexBuffer;
    BYTE IAInputLayout;
    BYTE IAPrimitiveTopology;
    BYTE OMRenderTargets;
    BYTE OMDepthStencilState;
    BYTE OMBlendState;
    BYTE RSViewports;
    BYTE RSScissorRects;
    BYTE RSRasterizerState;
    BYTE SOBuffers;
    BYTE Predication;
} D3D10_STATE_BLOCK_MASK;
typedef struct _D3D10_EFFECT_DESC {
    WINBOOL IsChildEffect;
    UINT ConstantBuffers;
    UINT SharedConstantBuffers;
    UINT GlobalVariables;
    UINT SharedGlobalVariables;
    UINT Techniques;
} D3D10_EFFECT_DESC;
typedef struct _D3D10_EFFECT_SHADER_DESC {
    const BYTE *pInputSignature;
    WINBOOL IsInline;
    const BYTE *pBytecode;
    UINT BytecodeLength;
    const char *SODecl;
    UINT NumInputSignatureEntries;
    UINT NumOutputSignatureEntries;
} D3D10_EFFECT_SHADER_DESC;
typedef struct _D3D10_PASS_DESC {
    const char *Name;
    UINT Annotations;
    BYTE *pIAInputSignature;
    SIZE_T IAInputSignatureSize;
    UINT StencilRef;
    UINT SampleMask;
    FLOAT BlendFactor[4];
} D3D10_PASS_DESC;
typedef struct _D3D10_PASS_SHADER_DESC {
    ID3D10EffectShaderVariable *pShaderVariable;
    UINT ShaderIndex;
} D3D10_PASS_SHADER_DESC;
#define D3D10_EFFECT_COMPILE_CHILD_EFFECT (0x1)

#define D3D10_EFFECT_COMPILE_ALLOW_SLOW_OPS (0x2)

#define D3D10_EFFECT_SINGLE_THREADED (0x8)

/*****************************************************************************
 * ID3D10EffectType interface
 */
#ifndef __ID3D10EffectType_INTERFACE_DEFINED__
#define __ID3D10EffectType_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectType, 0x4e9e1ddc, 0xcd9d, 0x4772, 0xa8,0x37, 0x00,0x18,0x0b,0x9b,0x88,0xfd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4e9e1ddc-cd9d-4772-a837-00180b9b88fd")
ID3D10EffectType
{

    BEGIN_INTERFACE

    virtual WINBOOL STDMETHODCALLTYPE IsValid(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D10_EFFECT_TYPE_DESC *desc) = 0;

    virtual ID3D10EffectType * STDMETHODCALLTYPE GetMemberTypeByIndex(
        UINT index) = 0;

    virtual ID3D10EffectType * STDMETHODCALLTYPE GetMemberTypeByName(
        const char *name) = 0;

    virtual ID3D10EffectType * STDMETHODCALLTYPE GetMemberTypeBySemantic(
        const char *semantic) = 0;

    virtual const char * STDMETHODCALLTYPE GetMemberName(
        UINT index) = 0;

    virtual const char * STDMETHODCALLTYPE GetMemberSemantic(
        UINT index) = 0;

    END_INTERFACE

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectType, 0x4e9e1ddc, 0xcd9d, 0x4772, 0xa8,0x37, 0x00,0x18,0x0b,0x9b,0x88,0xfd)
#endif
#else
typedef struct ID3D10EffectTypeVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectType methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectType *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectType *This,
        D3D10_EFFECT_TYPE_DESC *desc);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetMemberTypeByIndex)(
        ID3D10EffectType *This,
        UINT index);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetMemberTypeByName)(
        ID3D10EffectType *This,
        const char *name);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetMemberTypeBySemantic)(
        ID3D10EffectType *This,
        const char *semantic);

    const char * (STDMETHODCALLTYPE *GetMemberName)(
        ID3D10EffectType *This,
        UINT index);

    const char * (STDMETHODCALLTYPE *GetMemberSemantic)(
        ID3D10EffectType *This,
        UINT index);

    END_INTERFACE
} ID3D10EffectTypeVtbl;

interface ID3D10EffectType {
    CONST_VTBL ID3D10EffectTypeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectType methods ***/
#define ID3D10EffectType_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectType_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectType_GetMemberTypeByIndex(This,index) (This)->lpVtbl->GetMemberTypeByIndex(This,index)
#define ID3D10EffectType_GetMemberTypeByName(This,name) (This)->lpVtbl->GetMemberTypeByName(This,name)
#define ID3D10EffectType_GetMemberTypeBySemantic(This,semantic) (This)->lpVtbl->GetMemberTypeBySemantic(This,semantic)
#define ID3D10EffectType_GetMemberName(This,index) (This)->lpVtbl->GetMemberName(This,index)
#define ID3D10EffectType_GetMemberSemantic(This,index) (This)->lpVtbl->GetMemberSemantic(This,index)
#else
/*** ID3D10EffectType methods ***/
static inline WINBOOL ID3D10EffectType_IsValid(ID3D10EffectType* This) {
    return This->lpVtbl->IsValid(This);
}
static inline HRESULT ID3D10EffectType_GetDesc(ID3D10EffectType* This,D3D10_EFFECT_TYPE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectType * ID3D10EffectType_GetMemberTypeByIndex(ID3D10EffectType* This,UINT index) {
    return This->lpVtbl->GetMemberTypeByIndex(This,index);
}
static inline ID3D10EffectType * ID3D10EffectType_GetMemberTypeByName(ID3D10EffectType* This,const char *name) {
    return This->lpVtbl->GetMemberTypeByName(This,name);
}
static inline ID3D10EffectType * ID3D10EffectType_GetMemberTypeBySemantic(ID3D10EffectType* This,const char *semantic) {
    return This->lpVtbl->GetMemberTypeBySemantic(This,semantic);
}
static inline const char * ID3D10EffectType_GetMemberName(ID3D10EffectType* This,UINT index) {
    return This->lpVtbl->GetMemberName(This,index);
}
static inline const char * ID3D10EffectType_GetMemberSemantic(ID3D10EffectType* This,UINT index) {
    return This->lpVtbl->GetMemberSemantic(This,index);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectType_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectVariable interface
 */
#ifndef __ID3D10EffectVariable_INTERFACE_DEFINED__
#define __ID3D10EffectVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectVariable, 0xae897105, 0x00e6, 0x45bf, 0xbb,0x8e, 0x28,0x1d,0xd6,0xdb,0x8e,0x1b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ae897105-00e6-45bf-bb8e-281dd6db8e1b")
ID3D10EffectVariable
{

    BEGIN_INTERFACE

    virtual WINBOOL STDMETHODCALLTYPE IsValid(
        ) = 0;

    virtual ID3D10EffectType * STDMETHODCALLTYPE GetType(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D10_EFFECT_VARIABLE_DESC *desc) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetAnnotationByIndex(
        UINT index) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetAnnotationByName(
        const char *name) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetMemberByIndex(
        UINT index) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetMemberByName(
        const char *name) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetMemberBySemantic(
        const char *semantic) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetElement(
        UINT index) = 0;

    virtual ID3D10EffectConstantBuffer * STDMETHODCALLTYPE GetParentConstantBuffer(
        ) = 0;

    virtual ID3D10EffectScalarVariable * STDMETHODCALLTYPE AsScalar(
        ) = 0;

    virtual ID3D10EffectVectorVariable * STDMETHODCALLTYPE AsVector(
        ) = 0;

    virtual ID3D10EffectMatrixVariable * STDMETHODCALLTYPE AsMatrix(
        ) = 0;

    virtual ID3D10EffectStringVariable * STDMETHODCALLTYPE AsString(
        ) = 0;

    virtual ID3D10EffectShaderResourceVariable * STDMETHODCALLTYPE AsShaderResource(
        ) = 0;

    virtual ID3D10EffectRenderTargetViewVariable * STDMETHODCALLTYPE AsRenderTargetView(
        ) = 0;

    virtual ID3D10EffectDepthStencilViewVariable * STDMETHODCALLTYPE AsDepthStencilView(
        ) = 0;

    virtual ID3D10EffectConstantBuffer * STDMETHODCALLTYPE AsConstantBuffer(
        ) = 0;

    virtual ID3D10EffectShaderVariable * STDMETHODCALLTYPE AsShader(
        ) = 0;

    virtual ID3D10EffectBlendVariable * STDMETHODCALLTYPE AsBlend(
        ) = 0;

    virtual ID3D10EffectDepthStencilVariable * STDMETHODCALLTYPE AsDepthStencil(
        ) = 0;

    virtual ID3D10EffectRasterizerVariable * STDMETHODCALLTYPE AsRasterizer(
        ) = 0;

    virtual ID3D10EffectSamplerVariable * STDMETHODCALLTYPE AsSampler(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRawValue(
        void *data,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRawValue(
        void *data,
        UINT offset,
        UINT count) = 0;

    END_INTERFACE

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectVariable, 0xae897105, 0x00e6, 0x45bf, 0xbb,0x8e, 0x28,0x1d,0xd6,0xdb,0x8e,0x1b)
#endif
#else
typedef struct ID3D10EffectVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectVariable *This,
        void *data,
        UINT offset,
        UINT count);

    END_INTERFACE
} ID3D10EffectVariableVtbl;

interface ID3D10EffectVariable {
    CONST_VTBL ID3D10EffectVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectVariable_IsValid(ID3D10EffectVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectVariable_GetType(ID3D10EffectVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectVariable_GetDesc(ID3D10EffectVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectVariable_GetAnnotationByIndex(ID3D10EffectVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectVariable_GetAnnotationByName(ID3D10EffectVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectVariable_GetMemberByIndex(ID3D10EffectVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectVariable_GetMemberByName(ID3D10EffectVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectVariable_GetMemberBySemantic(ID3D10EffectVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectVariable_GetElement(ID3D10EffectVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectVariable_GetParentConstantBuffer(ID3D10EffectVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectVariable_AsScalar(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectVariable_AsVector(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectVariable_AsMatrix(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectVariable_AsString(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectVariable_AsShaderResource(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectVariable_AsRenderTargetView(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectVariable_AsDepthStencilView(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectVariable_AsConstantBuffer(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectVariable_AsShader(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectVariable_AsBlend(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectVariable_AsDepthStencil(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectVariable_AsRasterizer(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectVariable_AsSampler(ID3D10EffectVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectVariable_SetRawValue(ID3D10EffectVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectVariable_GetRawValue(ID3D10EffectVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectConstantBuffer interface
 */
#ifndef __ID3D10EffectConstantBuffer_INTERFACE_DEFINED__
#define __ID3D10EffectConstantBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectConstantBuffer, 0x56648f4d, 0xcc8b, 0x4444, 0xa5,0xad, 0xb5,0xa3,0xd7,0x6e,0x91,0xb3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56648f4d-cc8b-4444-a5ad-b5a3d76e91b3")
ID3D10EffectConstantBuffer : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE SetConstantBuffer(
        ID3D10Buffer *buffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConstantBuffer(
        ID3D10Buffer **buffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTextureBuffer(
        ID3D10ShaderResourceView *view) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTextureBuffer(
        ID3D10ShaderResourceView **view) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectConstantBuffer, 0x56648f4d, 0xcc8b, 0x4444, 0xa5,0xad, 0xb5,0xa3,0xd7,0x6e,0x91,0xb3)
#endif
#else
typedef struct ID3D10EffectConstantBufferVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectConstantBuffer *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectConstantBuffer *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectConstantBuffer *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectConstantBuffer *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectConstantBuffer *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectConstantBuffer *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectConstantBuffer *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectConstantBuffer *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectConstantBuffer *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectConstantBuffer *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectConstantBuffer *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectConstantBuffer *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectConstantBuffer methods ***/
    HRESULT (STDMETHODCALLTYPE *SetConstantBuffer)(
        ID3D10EffectConstantBuffer *This,
        ID3D10Buffer *buffer);

    HRESULT (STDMETHODCALLTYPE *GetConstantBuffer)(
        ID3D10EffectConstantBuffer *This,
        ID3D10Buffer **buffer);

    HRESULT (STDMETHODCALLTYPE *SetTextureBuffer)(
        ID3D10EffectConstantBuffer *This,
        ID3D10ShaderResourceView *view);

    HRESULT (STDMETHODCALLTYPE *GetTextureBuffer)(
        ID3D10EffectConstantBuffer *This,
        ID3D10ShaderResourceView **view);

    END_INTERFACE
} ID3D10EffectConstantBufferVtbl;

interface ID3D10EffectConstantBuffer {
    CONST_VTBL ID3D10EffectConstantBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectConstantBuffer_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectConstantBuffer_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectConstantBuffer_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectConstantBuffer_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectConstantBuffer_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectConstantBuffer_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectConstantBuffer_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectConstantBuffer_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectConstantBuffer_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectConstantBuffer_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectConstantBuffer_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectConstantBuffer_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectConstantBuffer_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectConstantBuffer_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectConstantBuffer_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectConstantBuffer_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectConstantBuffer_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectConstantBuffer_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectConstantBuffer_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectConstantBuffer_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectConstantBuffer_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectConstantBuffer_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectConstantBuffer_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectConstantBuffer_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectConstantBuffer_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectConstantBuffer methods ***/
#define ID3D10EffectConstantBuffer_SetConstantBuffer(This,buffer) (This)->lpVtbl->SetConstantBuffer(This,buffer)
#define ID3D10EffectConstantBuffer_GetConstantBuffer(This,buffer) (This)->lpVtbl->GetConstantBuffer(This,buffer)
#define ID3D10EffectConstantBuffer_SetTextureBuffer(This,view) (This)->lpVtbl->SetTextureBuffer(This,view)
#define ID3D10EffectConstantBuffer_GetTextureBuffer(This,view) (This)->lpVtbl->GetTextureBuffer(This,view)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectConstantBuffer_IsValid(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectConstantBuffer_GetType(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectConstantBuffer_GetDesc(ID3D10EffectConstantBuffer* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectConstantBuffer_GetAnnotationByIndex(ID3D10EffectConstantBuffer* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectConstantBuffer_GetAnnotationByName(ID3D10EffectConstantBuffer* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectConstantBuffer_GetMemberByIndex(ID3D10EffectConstantBuffer* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectConstantBuffer_GetMemberByName(ID3D10EffectConstantBuffer* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectConstantBuffer_GetMemberBySemantic(ID3D10EffectConstantBuffer* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectConstantBuffer_GetElement(ID3D10EffectConstantBuffer* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectConstantBuffer_GetParentConstantBuffer(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectConstantBuffer_AsScalar(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectConstantBuffer_AsVector(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectConstantBuffer_AsMatrix(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectConstantBuffer_AsString(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectConstantBuffer_AsShaderResource(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectConstantBuffer_AsRenderTargetView(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectConstantBuffer_AsDepthStencilView(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectConstantBuffer_AsConstantBuffer(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectConstantBuffer_AsShader(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectConstantBuffer_AsBlend(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectConstantBuffer_AsDepthStencil(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectConstantBuffer_AsRasterizer(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectConstantBuffer_AsSampler(ID3D10EffectConstantBuffer* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectConstantBuffer_SetRawValue(ID3D10EffectConstantBuffer* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectConstantBuffer_GetRawValue(ID3D10EffectConstantBuffer* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectConstantBuffer methods ***/
static inline HRESULT ID3D10EffectConstantBuffer_SetConstantBuffer(ID3D10EffectConstantBuffer* This,ID3D10Buffer *buffer) {
    return This->lpVtbl->SetConstantBuffer(This,buffer);
}
static inline HRESULT ID3D10EffectConstantBuffer_GetConstantBuffer(ID3D10EffectConstantBuffer* This,ID3D10Buffer **buffer) {
    return This->lpVtbl->GetConstantBuffer(This,buffer);
}
static inline HRESULT ID3D10EffectConstantBuffer_SetTextureBuffer(ID3D10EffectConstantBuffer* This,ID3D10ShaderResourceView *view) {
    return This->lpVtbl->SetTextureBuffer(This,view);
}
static inline HRESULT ID3D10EffectConstantBuffer_GetTextureBuffer(ID3D10EffectConstantBuffer* This,ID3D10ShaderResourceView **view) {
    return This->lpVtbl->GetTextureBuffer(This,view);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectConstantBuffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectScalarVariable interface
 */
#ifndef __ID3D10EffectScalarVariable_INTERFACE_DEFINED__
#define __ID3D10EffectScalarVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectScalarVariable, 0x00e48f7b, 0xd2c8, 0x49e8, 0xa8,0x6c, 0x02,0x2d,0xee,0x53,0x43,0x1f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00e48f7b-d2c8-49e8-a86c-022dee53431f")
ID3D10EffectScalarVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE SetFloat(
        float value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFloat(
        float *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFloatArray(
        float *values,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFloatArray(
        float *values,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInt(
        int value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInt(
        int *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIntArray(
        int *values,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIntArray(
        int *values,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBool(
        WINBOOL value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBool(
        WINBOOL *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBoolArray(
        WINBOOL *values,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBoolArray(
        WINBOOL *values,
        UINT offset,
        UINT count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectScalarVariable, 0x00e48f7b, 0xd2c8, 0x49e8, 0xa8,0x6c, 0x02,0x2d,0xee,0x53,0x43,0x1f)
#endif
#else
typedef struct ID3D10EffectScalarVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectScalarVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectScalarVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectScalarVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectScalarVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectScalarVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectScalarVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectScalarVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectScalarVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectScalarVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectScalarVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectScalarVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectScalarVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectScalarVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *SetFloat)(
        ID3D10EffectScalarVariable *This,
        float value);

    HRESULT (STDMETHODCALLTYPE *GetFloat)(
        ID3D10EffectScalarVariable *This,
        float *value);

    HRESULT (STDMETHODCALLTYPE *SetFloatArray)(
        ID3D10EffectScalarVariable *This,
        float *values,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetFloatArray)(
        ID3D10EffectScalarVariable *This,
        float *values,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *SetInt)(
        ID3D10EffectScalarVariable *This,
        int value);

    HRESULT (STDMETHODCALLTYPE *GetInt)(
        ID3D10EffectScalarVariable *This,
        int *value);

    HRESULT (STDMETHODCALLTYPE *SetIntArray)(
        ID3D10EffectScalarVariable *This,
        int *values,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetIntArray)(
        ID3D10EffectScalarVariable *This,
        int *values,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *SetBool)(
        ID3D10EffectScalarVariable *This,
        WINBOOL value);

    HRESULT (STDMETHODCALLTYPE *GetBool)(
        ID3D10EffectScalarVariable *This,
        WINBOOL *value);

    HRESULT (STDMETHODCALLTYPE *SetBoolArray)(
        ID3D10EffectScalarVariable *This,
        WINBOOL *values,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetBoolArray)(
        ID3D10EffectScalarVariable *This,
        WINBOOL *values,
        UINT offset,
        UINT count);

    END_INTERFACE
} ID3D10EffectScalarVariableVtbl;

interface ID3D10EffectScalarVariable {
    CONST_VTBL ID3D10EffectScalarVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectScalarVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectScalarVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectScalarVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectScalarVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectScalarVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectScalarVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectScalarVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectScalarVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectScalarVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectScalarVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectScalarVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectScalarVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectScalarVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectScalarVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectScalarVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectScalarVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectScalarVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectScalarVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectScalarVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectScalarVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectScalarVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectScalarVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectScalarVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectScalarVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectScalarVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectScalarVariable methods ***/
#define ID3D10EffectScalarVariable_SetFloat(This,value) (This)->lpVtbl->SetFloat(This,value)
#define ID3D10EffectScalarVariable_GetFloat(This,value) (This)->lpVtbl->GetFloat(This,value)
#define ID3D10EffectScalarVariable_SetFloatArray(This,values,offset,count) (This)->lpVtbl->SetFloatArray(This,values,offset,count)
#define ID3D10EffectScalarVariable_GetFloatArray(This,values,offset,count) (This)->lpVtbl->GetFloatArray(This,values,offset,count)
#define ID3D10EffectScalarVariable_SetInt(This,value) (This)->lpVtbl->SetInt(This,value)
#define ID3D10EffectScalarVariable_GetInt(This,value) (This)->lpVtbl->GetInt(This,value)
#define ID3D10EffectScalarVariable_SetIntArray(This,values,offset,count) (This)->lpVtbl->SetIntArray(This,values,offset,count)
#define ID3D10EffectScalarVariable_GetIntArray(This,values,offset,count) (This)->lpVtbl->GetIntArray(This,values,offset,count)
#define ID3D10EffectScalarVariable_SetBool(This,value) (This)->lpVtbl->SetBool(This,value)
#define ID3D10EffectScalarVariable_GetBool(This,value) (This)->lpVtbl->GetBool(This,value)
#define ID3D10EffectScalarVariable_SetBoolArray(This,values,offset,count) (This)->lpVtbl->SetBoolArray(This,values,offset,count)
#define ID3D10EffectScalarVariable_GetBoolArray(This,values,offset,count) (This)->lpVtbl->GetBoolArray(This,values,offset,count)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectScalarVariable_IsValid(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectScalarVariable_GetType(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectScalarVariable_GetDesc(ID3D10EffectScalarVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectScalarVariable_GetAnnotationByIndex(ID3D10EffectScalarVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectScalarVariable_GetAnnotationByName(ID3D10EffectScalarVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectScalarVariable_GetMemberByIndex(ID3D10EffectScalarVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectScalarVariable_GetMemberByName(ID3D10EffectScalarVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectScalarVariable_GetMemberBySemantic(ID3D10EffectScalarVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectScalarVariable_GetElement(ID3D10EffectScalarVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectScalarVariable_GetParentConstantBuffer(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectScalarVariable_AsScalar(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectScalarVariable_AsVector(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectScalarVariable_AsMatrix(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectScalarVariable_AsString(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectScalarVariable_AsShaderResource(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectScalarVariable_AsRenderTargetView(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectScalarVariable_AsDepthStencilView(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectScalarVariable_AsConstantBuffer(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectScalarVariable_AsShader(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectScalarVariable_AsBlend(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectScalarVariable_AsDepthStencil(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectScalarVariable_AsRasterizer(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectScalarVariable_AsSampler(ID3D10EffectScalarVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectScalarVariable_SetRawValue(ID3D10EffectScalarVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectScalarVariable_GetRawValue(ID3D10EffectScalarVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectScalarVariable methods ***/
static inline HRESULT ID3D10EffectScalarVariable_SetFloat(ID3D10EffectScalarVariable* This,float value) {
    return This->lpVtbl->SetFloat(This,value);
}
static inline HRESULT ID3D10EffectScalarVariable_GetFloat(ID3D10EffectScalarVariable* This,float *value) {
    return This->lpVtbl->GetFloat(This,value);
}
static inline HRESULT ID3D10EffectScalarVariable_SetFloatArray(ID3D10EffectScalarVariable* This,float *values,UINT offset,UINT count) {
    return This->lpVtbl->SetFloatArray(This,values,offset,count);
}
static inline HRESULT ID3D10EffectScalarVariable_GetFloatArray(ID3D10EffectScalarVariable* This,float *values,UINT offset,UINT count) {
    return This->lpVtbl->GetFloatArray(This,values,offset,count);
}
static inline HRESULT ID3D10EffectScalarVariable_SetInt(ID3D10EffectScalarVariable* This,int value) {
    return This->lpVtbl->SetInt(This,value);
}
static inline HRESULT ID3D10EffectScalarVariable_GetInt(ID3D10EffectScalarVariable* This,int *value) {
    return This->lpVtbl->GetInt(This,value);
}
static inline HRESULT ID3D10EffectScalarVariable_SetIntArray(ID3D10EffectScalarVariable* This,int *values,UINT offset,UINT count) {
    return This->lpVtbl->SetIntArray(This,values,offset,count);
}
static inline HRESULT ID3D10EffectScalarVariable_GetIntArray(ID3D10EffectScalarVariable* This,int *values,UINT offset,UINT count) {
    return This->lpVtbl->GetIntArray(This,values,offset,count);
}
static inline HRESULT ID3D10EffectScalarVariable_SetBool(ID3D10EffectScalarVariable* This,WINBOOL value) {
    return This->lpVtbl->SetBool(This,value);
}
static inline HRESULT ID3D10EffectScalarVariable_GetBool(ID3D10EffectScalarVariable* This,WINBOOL *value) {
    return This->lpVtbl->GetBool(This,value);
}
static inline HRESULT ID3D10EffectScalarVariable_SetBoolArray(ID3D10EffectScalarVariable* This,WINBOOL *values,UINT offset,UINT count) {
    return This->lpVtbl->SetBoolArray(This,values,offset,count);
}
static inline HRESULT ID3D10EffectScalarVariable_GetBoolArray(ID3D10EffectScalarVariable* This,WINBOOL *values,UINT offset,UINT count) {
    return This->lpVtbl->GetBoolArray(This,values,offset,count);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectScalarVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectVectorVariable interface
 */
#ifndef __ID3D10EffectVectorVariable_INTERFACE_DEFINED__
#define __ID3D10EffectVectorVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectVectorVariable, 0x62b98c44, 0x1f82, 0x4c67, 0xbc,0xd0, 0x72,0xcf,0x8f,0x21,0x7e,0x81);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("62b98c44-1f82-4c67-bcd0-72cf8f217e81")
ID3D10EffectVectorVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE SetBoolVector(
        WINBOOL *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIntVector(
        int *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFloatVector(
        float *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBoolVector(
        WINBOOL *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIntVector(
        int *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFloatVector(
        float *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBoolVectorArray(
        WINBOOL *values,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetIntVectorArray(
        int *values,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFloatVectorArray(
        float *values,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBoolVectorArray(
        WINBOOL *values,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIntVectorArray(
        int *values,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFloatVectorArray(
        float *values,
        UINT offset,
        UINT count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectVectorVariable, 0x62b98c44, 0x1f82, 0x4c67, 0xbc,0xd0, 0x72,0xcf,0x8f,0x21,0x7e,0x81)
#endif
#else
typedef struct ID3D10EffectVectorVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectVectorVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectVectorVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectVectorVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectVectorVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectVectorVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectVectorVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectVectorVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectVectorVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectVectorVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectVectorVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectVectorVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectVectorVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectVectorVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *SetBoolVector)(
        ID3D10EffectVectorVariable *This,
        WINBOOL *value);

    HRESULT (STDMETHODCALLTYPE *SetIntVector)(
        ID3D10EffectVectorVariable *This,
        int *value);

    HRESULT (STDMETHODCALLTYPE *SetFloatVector)(
        ID3D10EffectVectorVariable *This,
        float *value);

    HRESULT (STDMETHODCALLTYPE *GetBoolVector)(
        ID3D10EffectVectorVariable *This,
        WINBOOL *value);

    HRESULT (STDMETHODCALLTYPE *GetIntVector)(
        ID3D10EffectVectorVariable *This,
        int *value);

    HRESULT (STDMETHODCALLTYPE *GetFloatVector)(
        ID3D10EffectVectorVariable *This,
        float *value);

    HRESULT (STDMETHODCALLTYPE *SetBoolVectorArray)(
        ID3D10EffectVectorVariable *This,
        WINBOOL *values,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *SetIntVectorArray)(
        ID3D10EffectVectorVariable *This,
        int *values,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *SetFloatVectorArray)(
        ID3D10EffectVectorVariable *This,
        float *values,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetBoolVectorArray)(
        ID3D10EffectVectorVariable *This,
        WINBOOL *values,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetIntVectorArray)(
        ID3D10EffectVectorVariable *This,
        int *values,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetFloatVectorArray)(
        ID3D10EffectVectorVariable *This,
        float *values,
        UINT offset,
        UINT count);

    END_INTERFACE
} ID3D10EffectVectorVariableVtbl;

interface ID3D10EffectVectorVariable {
    CONST_VTBL ID3D10EffectVectorVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectVectorVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectVectorVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectVectorVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectVectorVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectVectorVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectVectorVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectVectorVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectVectorVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectVectorVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectVectorVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectVectorVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectVectorVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectVectorVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectVectorVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectVectorVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectVectorVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectVectorVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectVectorVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectVectorVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectVectorVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectVectorVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectVectorVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectVectorVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectVectorVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectVectorVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectVectorVariable methods ***/
#define ID3D10EffectVectorVariable_SetBoolVector(This,value) (This)->lpVtbl->SetBoolVector(This,value)
#define ID3D10EffectVectorVariable_SetIntVector(This,value) (This)->lpVtbl->SetIntVector(This,value)
#define ID3D10EffectVectorVariable_SetFloatVector(This,value) (This)->lpVtbl->SetFloatVector(This,value)
#define ID3D10EffectVectorVariable_GetBoolVector(This,value) (This)->lpVtbl->GetBoolVector(This,value)
#define ID3D10EffectVectorVariable_GetIntVector(This,value) (This)->lpVtbl->GetIntVector(This,value)
#define ID3D10EffectVectorVariable_GetFloatVector(This,value) (This)->lpVtbl->GetFloatVector(This,value)
#define ID3D10EffectVectorVariable_SetBoolVectorArray(This,values,offset,count) (This)->lpVtbl->SetBoolVectorArray(This,values,offset,count)
#define ID3D10EffectVectorVariable_SetIntVectorArray(This,values,offset,count) (This)->lpVtbl->SetIntVectorArray(This,values,offset,count)
#define ID3D10EffectVectorVariable_SetFloatVectorArray(This,values,offset,count) (This)->lpVtbl->SetFloatVectorArray(This,values,offset,count)
#define ID3D10EffectVectorVariable_GetBoolVectorArray(This,values,offset,count) (This)->lpVtbl->GetBoolVectorArray(This,values,offset,count)
#define ID3D10EffectVectorVariable_GetIntVectorArray(This,values,offset,count) (This)->lpVtbl->GetIntVectorArray(This,values,offset,count)
#define ID3D10EffectVectorVariable_GetFloatVectorArray(This,values,offset,count) (This)->lpVtbl->GetFloatVectorArray(This,values,offset,count)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectVectorVariable_IsValid(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectVectorVariable_GetType(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectVectorVariable_GetDesc(ID3D10EffectVectorVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectVectorVariable_GetAnnotationByIndex(ID3D10EffectVectorVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectVectorVariable_GetAnnotationByName(ID3D10EffectVectorVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectVectorVariable_GetMemberByIndex(ID3D10EffectVectorVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectVectorVariable_GetMemberByName(ID3D10EffectVectorVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectVectorVariable_GetMemberBySemantic(ID3D10EffectVectorVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectVectorVariable_GetElement(ID3D10EffectVectorVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectVectorVariable_GetParentConstantBuffer(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectVectorVariable_AsScalar(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectVectorVariable_AsVector(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectVectorVariable_AsMatrix(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectVectorVariable_AsString(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectVectorVariable_AsShaderResource(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectVectorVariable_AsRenderTargetView(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectVectorVariable_AsDepthStencilView(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectVectorVariable_AsConstantBuffer(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectVectorVariable_AsShader(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectVectorVariable_AsBlend(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectVectorVariable_AsDepthStencil(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectVectorVariable_AsRasterizer(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectVectorVariable_AsSampler(ID3D10EffectVectorVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectVectorVariable_SetRawValue(ID3D10EffectVectorVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectVectorVariable_GetRawValue(ID3D10EffectVectorVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectVectorVariable methods ***/
static inline HRESULT ID3D10EffectVectorVariable_SetBoolVector(ID3D10EffectVectorVariable* This,WINBOOL *value) {
    return This->lpVtbl->SetBoolVector(This,value);
}
static inline HRESULT ID3D10EffectVectorVariable_SetIntVector(ID3D10EffectVectorVariable* This,int *value) {
    return This->lpVtbl->SetIntVector(This,value);
}
static inline HRESULT ID3D10EffectVectorVariable_SetFloatVector(ID3D10EffectVectorVariable* This,float *value) {
    return This->lpVtbl->SetFloatVector(This,value);
}
static inline HRESULT ID3D10EffectVectorVariable_GetBoolVector(ID3D10EffectVectorVariable* This,WINBOOL *value) {
    return This->lpVtbl->GetBoolVector(This,value);
}
static inline HRESULT ID3D10EffectVectorVariable_GetIntVector(ID3D10EffectVectorVariable* This,int *value) {
    return This->lpVtbl->GetIntVector(This,value);
}
static inline HRESULT ID3D10EffectVectorVariable_GetFloatVector(ID3D10EffectVectorVariable* This,float *value) {
    return This->lpVtbl->GetFloatVector(This,value);
}
static inline HRESULT ID3D10EffectVectorVariable_SetBoolVectorArray(ID3D10EffectVectorVariable* This,WINBOOL *values,UINT offset,UINT count) {
    return This->lpVtbl->SetBoolVectorArray(This,values,offset,count);
}
static inline HRESULT ID3D10EffectVectorVariable_SetIntVectorArray(ID3D10EffectVectorVariable* This,int *values,UINT offset,UINT count) {
    return This->lpVtbl->SetIntVectorArray(This,values,offset,count);
}
static inline HRESULT ID3D10EffectVectorVariable_SetFloatVectorArray(ID3D10EffectVectorVariable* This,float *values,UINT offset,UINT count) {
    return This->lpVtbl->SetFloatVectorArray(This,values,offset,count);
}
static inline HRESULT ID3D10EffectVectorVariable_GetBoolVectorArray(ID3D10EffectVectorVariable* This,WINBOOL *values,UINT offset,UINT count) {
    return This->lpVtbl->GetBoolVectorArray(This,values,offset,count);
}
static inline HRESULT ID3D10EffectVectorVariable_GetIntVectorArray(ID3D10EffectVectorVariable* This,int *values,UINT offset,UINT count) {
    return This->lpVtbl->GetIntVectorArray(This,values,offset,count);
}
static inline HRESULT ID3D10EffectVectorVariable_GetFloatVectorArray(ID3D10EffectVectorVariable* This,float *values,UINT offset,UINT count) {
    return This->lpVtbl->GetFloatVectorArray(This,values,offset,count);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectVectorVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectMatrixVariable interface
 */
#ifndef __ID3D10EffectMatrixVariable_INTERFACE_DEFINED__
#define __ID3D10EffectMatrixVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectMatrixVariable, 0x50666c24, 0xb82f, 0x4eed, 0xa1,0x72, 0x5b,0x6e,0x7e,0x85,0x22,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("50666c24-b82f-4eed-a172-5b6e7e8522e0")
ID3D10EffectMatrixVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE SetMatrix(
        float *data) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMatrix(
        float *data) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMatrixArray(
        float *data,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMatrixArray(
        float *data,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMatrixTranspose(
        float *data) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMatrixTranspose(
        float *data) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMatrixTransposeArray(
        float *data,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMatrixTransposeArray(
        float *data,
        UINT offset,
        UINT count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectMatrixVariable, 0x50666c24, 0xb82f, 0x4eed, 0xa1,0x72, 0x5b,0x6e,0x7e,0x85,0x22,0xe0)
#endif
#else
typedef struct ID3D10EffectMatrixVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectMatrixVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectMatrixVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectMatrixVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectMatrixVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectMatrixVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectMatrixVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectMatrixVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectMatrixVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectMatrixVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectMatrixVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectMatrixVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectMatrixVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectMatrixVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMatrix)(
        ID3D10EffectMatrixVariable *This,
        float *data);

    HRESULT (STDMETHODCALLTYPE *GetMatrix)(
        ID3D10EffectMatrixVariable *This,
        float *data);

    HRESULT (STDMETHODCALLTYPE *SetMatrixArray)(
        ID3D10EffectMatrixVariable *This,
        float *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetMatrixArray)(
        ID3D10EffectMatrixVariable *This,
        float *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *SetMatrixTranspose)(
        ID3D10EffectMatrixVariable *This,
        float *data);

    HRESULT (STDMETHODCALLTYPE *GetMatrixTranspose)(
        ID3D10EffectMatrixVariable *This,
        float *data);

    HRESULT (STDMETHODCALLTYPE *SetMatrixTransposeArray)(
        ID3D10EffectMatrixVariable *This,
        float *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetMatrixTransposeArray)(
        ID3D10EffectMatrixVariable *This,
        float *data,
        UINT offset,
        UINT count);

    END_INTERFACE
} ID3D10EffectMatrixVariableVtbl;

interface ID3D10EffectMatrixVariable {
    CONST_VTBL ID3D10EffectMatrixVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectMatrixVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectMatrixVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectMatrixVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectMatrixVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectMatrixVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectMatrixVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectMatrixVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectMatrixVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectMatrixVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectMatrixVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectMatrixVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectMatrixVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectMatrixVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectMatrixVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectMatrixVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectMatrixVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectMatrixVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectMatrixVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectMatrixVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectMatrixVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectMatrixVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectMatrixVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectMatrixVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectMatrixVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectMatrixVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectMatrixVariable methods ***/
#define ID3D10EffectMatrixVariable_SetMatrix(This,data) (This)->lpVtbl->SetMatrix(This,data)
#define ID3D10EffectMatrixVariable_GetMatrix(This,data) (This)->lpVtbl->GetMatrix(This,data)
#define ID3D10EffectMatrixVariable_SetMatrixArray(This,data,offset,count) (This)->lpVtbl->SetMatrixArray(This,data,offset,count)
#define ID3D10EffectMatrixVariable_GetMatrixArray(This,data,offset,count) (This)->lpVtbl->GetMatrixArray(This,data,offset,count)
#define ID3D10EffectMatrixVariable_SetMatrixTranspose(This,data) (This)->lpVtbl->SetMatrixTranspose(This,data)
#define ID3D10EffectMatrixVariable_GetMatrixTranspose(This,data) (This)->lpVtbl->GetMatrixTranspose(This,data)
#define ID3D10EffectMatrixVariable_SetMatrixTransposeArray(This,data,offset,count) (This)->lpVtbl->SetMatrixTransposeArray(This,data,offset,count)
#define ID3D10EffectMatrixVariable_GetMatrixTransposeArray(This,data,offset,count) (This)->lpVtbl->GetMatrixTransposeArray(This,data,offset,count)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectMatrixVariable_IsValid(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectMatrixVariable_GetType(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectMatrixVariable_GetDesc(ID3D10EffectMatrixVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectMatrixVariable_GetAnnotationByIndex(ID3D10EffectMatrixVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectMatrixVariable_GetAnnotationByName(ID3D10EffectMatrixVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectMatrixVariable_GetMemberByIndex(ID3D10EffectMatrixVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectMatrixVariable_GetMemberByName(ID3D10EffectMatrixVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectMatrixVariable_GetMemberBySemantic(ID3D10EffectMatrixVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectMatrixVariable_GetElement(ID3D10EffectMatrixVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectMatrixVariable_GetParentConstantBuffer(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectMatrixVariable_AsScalar(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectMatrixVariable_AsVector(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectMatrixVariable_AsMatrix(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectMatrixVariable_AsString(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectMatrixVariable_AsShaderResource(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectMatrixVariable_AsRenderTargetView(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectMatrixVariable_AsDepthStencilView(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectMatrixVariable_AsConstantBuffer(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectMatrixVariable_AsShader(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectMatrixVariable_AsBlend(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectMatrixVariable_AsDepthStencil(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectMatrixVariable_AsRasterizer(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectMatrixVariable_AsSampler(ID3D10EffectMatrixVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectMatrixVariable_SetRawValue(ID3D10EffectMatrixVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectMatrixVariable_GetRawValue(ID3D10EffectMatrixVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectMatrixVariable methods ***/
static inline HRESULT ID3D10EffectMatrixVariable_SetMatrix(ID3D10EffectMatrixVariable* This,float *data) {
    return This->lpVtbl->SetMatrix(This,data);
}
static inline HRESULT ID3D10EffectMatrixVariable_GetMatrix(ID3D10EffectMatrixVariable* This,float *data) {
    return This->lpVtbl->GetMatrix(This,data);
}
static inline HRESULT ID3D10EffectMatrixVariable_SetMatrixArray(ID3D10EffectMatrixVariable* This,float *data,UINT offset,UINT count) {
    return This->lpVtbl->SetMatrixArray(This,data,offset,count);
}
static inline HRESULT ID3D10EffectMatrixVariable_GetMatrixArray(ID3D10EffectMatrixVariable* This,float *data,UINT offset,UINT count) {
    return This->lpVtbl->GetMatrixArray(This,data,offset,count);
}
static inline HRESULT ID3D10EffectMatrixVariable_SetMatrixTranspose(ID3D10EffectMatrixVariable* This,float *data) {
    return This->lpVtbl->SetMatrixTranspose(This,data);
}
static inline HRESULT ID3D10EffectMatrixVariable_GetMatrixTranspose(ID3D10EffectMatrixVariable* This,float *data) {
    return This->lpVtbl->GetMatrixTranspose(This,data);
}
static inline HRESULT ID3D10EffectMatrixVariable_SetMatrixTransposeArray(ID3D10EffectMatrixVariable* This,float *data,UINT offset,UINT count) {
    return This->lpVtbl->SetMatrixTransposeArray(This,data,offset,count);
}
static inline HRESULT ID3D10EffectMatrixVariable_GetMatrixTransposeArray(ID3D10EffectMatrixVariable* This,float *data,UINT offset,UINT count) {
    return This->lpVtbl->GetMatrixTransposeArray(This,data,offset,count);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectMatrixVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectStringVariable interface
 */
#ifndef __ID3D10EffectStringVariable_INTERFACE_DEFINED__
#define __ID3D10EffectStringVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectStringVariable, 0x71417501, 0x8df9, 0x4e0a, 0xa7,0x8a, 0x25,0x5f,0x97,0x56,0xba,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("71417501-8df9-4e0a-a78a-255f9756baff")
ID3D10EffectStringVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE GetString(
        const char **str) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStringArray(
        const char **strs,
        UINT offset,
        UINT count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectStringVariable, 0x71417501, 0x8df9, 0x4e0a, 0xa7,0x8a, 0x25,0x5f,0x97,0x56,0xba,0xff)
#endif
#else
typedef struct ID3D10EffectStringVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectStringVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectStringVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectStringVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectStringVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectStringVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectStringVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectStringVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectStringVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectStringVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectStringVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectStringVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectStringVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectStringVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetString)(
        ID3D10EffectStringVariable *This,
        const char **str);

    HRESULT (STDMETHODCALLTYPE *GetStringArray)(
        ID3D10EffectStringVariable *This,
        const char **strs,
        UINT offset,
        UINT count);

    END_INTERFACE
} ID3D10EffectStringVariableVtbl;

interface ID3D10EffectStringVariable {
    CONST_VTBL ID3D10EffectStringVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectStringVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectStringVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectStringVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectStringVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectStringVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectStringVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectStringVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectStringVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectStringVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectStringVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectStringVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectStringVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectStringVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectStringVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectStringVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectStringVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectStringVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectStringVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectStringVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectStringVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectStringVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectStringVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectStringVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectStringVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectStringVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectStringVariable methods ***/
#define ID3D10EffectStringVariable_GetString(This,str) (This)->lpVtbl->GetString(This,str)
#define ID3D10EffectStringVariable_GetStringArray(This,strs,offset,count) (This)->lpVtbl->GetStringArray(This,strs,offset,count)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectStringVariable_IsValid(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectStringVariable_GetType(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectStringVariable_GetDesc(ID3D10EffectStringVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectStringVariable_GetAnnotationByIndex(ID3D10EffectStringVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectStringVariable_GetAnnotationByName(ID3D10EffectStringVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectStringVariable_GetMemberByIndex(ID3D10EffectStringVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectStringVariable_GetMemberByName(ID3D10EffectStringVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectStringVariable_GetMemberBySemantic(ID3D10EffectStringVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectStringVariable_GetElement(ID3D10EffectStringVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectStringVariable_GetParentConstantBuffer(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectStringVariable_AsScalar(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectStringVariable_AsVector(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectStringVariable_AsMatrix(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectStringVariable_AsString(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectStringVariable_AsShaderResource(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectStringVariable_AsRenderTargetView(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectStringVariable_AsDepthStencilView(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectStringVariable_AsConstantBuffer(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectStringVariable_AsShader(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectStringVariable_AsBlend(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectStringVariable_AsDepthStencil(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectStringVariable_AsRasterizer(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectStringVariable_AsSampler(ID3D10EffectStringVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectStringVariable_SetRawValue(ID3D10EffectStringVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectStringVariable_GetRawValue(ID3D10EffectStringVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectStringVariable methods ***/
static inline HRESULT ID3D10EffectStringVariable_GetString(ID3D10EffectStringVariable* This,const char **str) {
    return This->lpVtbl->GetString(This,str);
}
static inline HRESULT ID3D10EffectStringVariable_GetStringArray(ID3D10EffectStringVariable* This,const char **strs,UINT offset,UINT count) {
    return This->lpVtbl->GetStringArray(This,strs,offset,count);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectStringVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectShaderResourceVariable interface
 */
#ifndef __ID3D10EffectShaderResourceVariable_INTERFACE_DEFINED__
#define __ID3D10EffectShaderResourceVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectShaderResourceVariable, 0xc0a7157b, 0xd872, 0x4b1d, 0x80,0x73, 0xef,0xc2,0xac,0xd4,0xb1,0xfc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c0a7157b-d872-4b1d-8073-efc2acd4b1fc")
ID3D10EffectShaderResourceVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE SetResource(
        ID3D10ShaderResourceView *resource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResource(
        ID3D10ShaderResourceView **resource) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetResourceArray(
        ID3D10ShaderResourceView **resources,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResourceArray(
        ID3D10ShaderResourceView **resources,
        UINT offset,
        UINT count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectShaderResourceVariable, 0xc0a7157b, 0xd872, 0x4b1d, 0x80,0x73, 0xef,0xc2,0xac,0xd4,0xb1,0xfc)
#endif
#else
typedef struct ID3D10EffectShaderResourceVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectShaderResourceVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectShaderResourceVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectShaderResourceVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectShaderResourceVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectShaderResourceVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectShaderResourceVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectShaderResourceVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectShaderResourceVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectShaderResourceVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectShaderResourceVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectShaderResourceVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectShaderResourceVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectShaderResourceVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *SetResource)(
        ID3D10EffectShaderResourceVariable *This,
        ID3D10ShaderResourceView *resource);

    HRESULT (STDMETHODCALLTYPE *GetResource)(
        ID3D10EffectShaderResourceVariable *This,
        ID3D10ShaderResourceView **resource);

    HRESULT (STDMETHODCALLTYPE *SetResourceArray)(
        ID3D10EffectShaderResourceVariable *This,
        ID3D10ShaderResourceView **resources,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetResourceArray)(
        ID3D10EffectShaderResourceVariable *This,
        ID3D10ShaderResourceView **resources,
        UINT offset,
        UINT count);

    END_INTERFACE
} ID3D10EffectShaderResourceVariableVtbl;

interface ID3D10EffectShaderResourceVariable {
    CONST_VTBL ID3D10EffectShaderResourceVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectShaderResourceVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectShaderResourceVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectShaderResourceVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectShaderResourceVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectShaderResourceVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectShaderResourceVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectShaderResourceVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectShaderResourceVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectShaderResourceVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectShaderResourceVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectShaderResourceVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectShaderResourceVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectShaderResourceVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectShaderResourceVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectShaderResourceVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectShaderResourceVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectShaderResourceVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectShaderResourceVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectShaderResourceVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectShaderResourceVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectShaderResourceVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectShaderResourceVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectShaderResourceVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectShaderResourceVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectShaderResourceVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectShaderResourceVariable methods ***/
#define ID3D10EffectShaderResourceVariable_SetResource(This,resource) (This)->lpVtbl->SetResource(This,resource)
#define ID3D10EffectShaderResourceVariable_GetResource(This,resource) (This)->lpVtbl->GetResource(This,resource)
#define ID3D10EffectShaderResourceVariable_SetResourceArray(This,resources,offset,count) (This)->lpVtbl->SetResourceArray(This,resources,offset,count)
#define ID3D10EffectShaderResourceVariable_GetResourceArray(This,resources,offset,count) (This)->lpVtbl->GetResourceArray(This,resources,offset,count)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectShaderResourceVariable_IsValid(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectShaderResourceVariable_GetType(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectShaderResourceVariable_GetDesc(ID3D10EffectShaderResourceVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderResourceVariable_GetAnnotationByIndex(ID3D10EffectShaderResourceVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderResourceVariable_GetAnnotationByName(ID3D10EffectShaderResourceVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderResourceVariable_GetMemberByIndex(ID3D10EffectShaderResourceVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderResourceVariable_GetMemberByName(ID3D10EffectShaderResourceVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderResourceVariable_GetMemberBySemantic(ID3D10EffectShaderResourceVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderResourceVariable_GetElement(ID3D10EffectShaderResourceVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectShaderResourceVariable_GetParentConstantBuffer(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectShaderResourceVariable_AsScalar(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectShaderResourceVariable_AsVector(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectShaderResourceVariable_AsMatrix(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectShaderResourceVariable_AsString(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectShaderResourceVariable_AsShaderResource(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectShaderResourceVariable_AsRenderTargetView(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectShaderResourceVariable_AsDepthStencilView(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectShaderResourceVariable_AsConstantBuffer(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectShaderResourceVariable_AsShader(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectShaderResourceVariable_AsBlend(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectShaderResourceVariable_AsDepthStencil(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectShaderResourceVariable_AsRasterizer(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectShaderResourceVariable_AsSampler(ID3D10EffectShaderResourceVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectShaderResourceVariable_SetRawValue(ID3D10EffectShaderResourceVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectShaderResourceVariable_GetRawValue(ID3D10EffectShaderResourceVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectShaderResourceVariable methods ***/
static inline HRESULT ID3D10EffectShaderResourceVariable_SetResource(ID3D10EffectShaderResourceVariable* This,ID3D10ShaderResourceView *resource) {
    return This->lpVtbl->SetResource(This,resource);
}
static inline HRESULT ID3D10EffectShaderResourceVariable_GetResource(ID3D10EffectShaderResourceVariable* This,ID3D10ShaderResourceView **resource) {
    return This->lpVtbl->GetResource(This,resource);
}
static inline HRESULT ID3D10EffectShaderResourceVariable_SetResourceArray(ID3D10EffectShaderResourceVariable* This,ID3D10ShaderResourceView **resources,UINT offset,UINT count) {
    return This->lpVtbl->SetResourceArray(This,resources,offset,count);
}
static inline HRESULT ID3D10EffectShaderResourceVariable_GetResourceArray(ID3D10EffectShaderResourceVariable* This,ID3D10ShaderResourceView **resources,UINT offset,UINT count) {
    return This->lpVtbl->GetResourceArray(This,resources,offset,count);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectShaderResourceVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectRenderTargetViewVariable interface
 */
#ifndef __ID3D10EffectRenderTargetViewVariable_INTERFACE_DEFINED__
#define __ID3D10EffectRenderTargetViewVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectRenderTargetViewVariable, 0x28ca0cc3, 0xc2c9, 0x40bb, 0xb5,0x7f, 0x67,0xb7,0x37,0x12,0x2b,0x17);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("28ca0cc3-c2c9-40bb-b57f-67b737122b17")
ID3D10EffectRenderTargetViewVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE SetRenderTarget(
        ID3D10RenderTargetView *view) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRenderTarget(
        ID3D10RenderTargetView **view) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRenderTargetArray(
        ID3D10RenderTargetView **views,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRenderTargetArray(
        ID3D10RenderTargetView **views,
        UINT offset,
        UINT count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectRenderTargetViewVariable, 0x28ca0cc3, 0xc2c9, 0x40bb, 0xb5,0x7f, 0x67,0xb7,0x37,0x12,0x2b,0x17)
#endif
#else
typedef struct ID3D10EffectRenderTargetViewVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectRenderTargetViewVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectRenderTargetViewVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectRenderTargetViewVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectRenderTargetViewVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectRenderTargetViewVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectRenderTargetViewVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectRenderTargetViewVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectRenderTargetViewVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectRenderTargetViewVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectRenderTargetViewVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectRenderTargetViewVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectRenderTargetViewVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectRenderTargetViewVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *SetRenderTarget)(
        ID3D10EffectRenderTargetViewVariable *This,
        ID3D10RenderTargetView *view);

    HRESULT (STDMETHODCALLTYPE *GetRenderTarget)(
        ID3D10EffectRenderTargetViewVariable *This,
        ID3D10RenderTargetView **view);

    HRESULT (STDMETHODCALLTYPE *SetRenderTargetArray)(
        ID3D10EffectRenderTargetViewVariable *This,
        ID3D10RenderTargetView **views,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRenderTargetArray)(
        ID3D10EffectRenderTargetViewVariable *This,
        ID3D10RenderTargetView **views,
        UINT offset,
        UINT count);

    END_INTERFACE
} ID3D10EffectRenderTargetViewVariableVtbl;

interface ID3D10EffectRenderTargetViewVariable {
    CONST_VTBL ID3D10EffectRenderTargetViewVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectRenderTargetViewVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectRenderTargetViewVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectRenderTargetViewVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectRenderTargetViewVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectRenderTargetViewVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectRenderTargetViewVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectRenderTargetViewVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectRenderTargetViewVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectRenderTargetViewVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectRenderTargetViewVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectRenderTargetViewVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectRenderTargetViewVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectRenderTargetViewVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectRenderTargetViewVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectRenderTargetViewVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectRenderTargetViewVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectRenderTargetViewVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectRenderTargetViewVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectRenderTargetViewVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectRenderTargetViewVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectRenderTargetViewVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectRenderTargetViewVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectRenderTargetViewVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectRenderTargetViewVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectRenderTargetViewVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectRenderTargetViewVariable methods ***/
#define ID3D10EffectRenderTargetViewVariable_SetRenderTarget(This,view) (This)->lpVtbl->SetRenderTarget(This,view)
#define ID3D10EffectRenderTargetViewVariable_GetRenderTarget(This,view) (This)->lpVtbl->GetRenderTarget(This,view)
#define ID3D10EffectRenderTargetViewVariable_SetRenderTargetArray(This,views,offset,count) (This)->lpVtbl->SetRenderTargetArray(This,views,offset,count)
#define ID3D10EffectRenderTargetViewVariable_GetRenderTargetArray(This,views,offset,count) (This)->lpVtbl->GetRenderTargetArray(This,views,offset,count)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectRenderTargetViewVariable_IsValid(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectRenderTargetViewVariable_GetType(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectRenderTargetViewVariable_GetDesc(ID3D10EffectRenderTargetViewVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectRenderTargetViewVariable_GetAnnotationByIndex(ID3D10EffectRenderTargetViewVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectRenderTargetViewVariable_GetAnnotationByName(ID3D10EffectRenderTargetViewVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectRenderTargetViewVariable_GetMemberByIndex(ID3D10EffectRenderTargetViewVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectRenderTargetViewVariable_GetMemberByName(ID3D10EffectRenderTargetViewVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectRenderTargetViewVariable_GetMemberBySemantic(ID3D10EffectRenderTargetViewVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectRenderTargetViewVariable_GetElement(ID3D10EffectRenderTargetViewVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectRenderTargetViewVariable_GetParentConstantBuffer(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectRenderTargetViewVariable_AsScalar(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectRenderTargetViewVariable_AsVector(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectRenderTargetViewVariable_AsMatrix(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectRenderTargetViewVariable_AsString(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectRenderTargetViewVariable_AsShaderResource(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectRenderTargetViewVariable_AsRenderTargetView(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectRenderTargetViewVariable_AsDepthStencilView(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectRenderTargetViewVariable_AsConstantBuffer(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectRenderTargetViewVariable_AsShader(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectRenderTargetViewVariable_AsBlend(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectRenderTargetViewVariable_AsDepthStencil(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectRenderTargetViewVariable_AsRasterizer(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectRenderTargetViewVariable_AsSampler(ID3D10EffectRenderTargetViewVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectRenderTargetViewVariable_SetRawValue(ID3D10EffectRenderTargetViewVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectRenderTargetViewVariable_GetRawValue(ID3D10EffectRenderTargetViewVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectRenderTargetViewVariable methods ***/
static inline HRESULT ID3D10EffectRenderTargetViewVariable_SetRenderTarget(ID3D10EffectRenderTargetViewVariable* This,ID3D10RenderTargetView *view) {
    return This->lpVtbl->SetRenderTarget(This,view);
}
static inline HRESULT ID3D10EffectRenderTargetViewVariable_GetRenderTarget(ID3D10EffectRenderTargetViewVariable* This,ID3D10RenderTargetView **view) {
    return This->lpVtbl->GetRenderTarget(This,view);
}
static inline HRESULT ID3D10EffectRenderTargetViewVariable_SetRenderTargetArray(ID3D10EffectRenderTargetViewVariable* This,ID3D10RenderTargetView **views,UINT offset,UINT count) {
    return This->lpVtbl->SetRenderTargetArray(This,views,offset,count);
}
static inline HRESULT ID3D10EffectRenderTargetViewVariable_GetRenderTargetArray(ID3D10EffectRenderTargetViewVariable* This,ID3D10RenderTargetView **views,UINT offset,UINT count) {
    return This->lpVtbl->GetRenderTargetArray(This,views,offset,count);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectRenderTargetViewVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectDepthStencilViewVariable interface
 */
#ifndef __ID3D10EffectDepthStencilViewVariable_INTERFACE_DEFINED__
#define __ID3D10EffectDepthStencilViewVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectDepthStencilViewVariable, 0x3e02c918, 0xcc79, 0x4985, 0xb6,0x22, 0x2d,0x92,0xad,0x70,0x16,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3e02c918-cc79-4985-b622-2d92ad701623")
ID3D10EffectDepthStencilViewVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE SetDepthStencil(
        ID3D10DepthStencilView *view) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDepthStencil(
        ID3D10DepthStencilView **view) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDepthStencilArray(
        ID3D10DepthStencilView **views,
        UINT offset,
        UINT count) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDepthStencilArray(
        ID3D10DepthStencilView **views,
        UINT offset,
        UINT count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectDepthStencilViewVariable, 0x3e02c918, 0xcc79, 0x4985, 0xb6,0x22, 0x2d,0x92,0xad,0x70,0x16,0x23)
#endif
#else
typedef struct ID3D10EffectDepthStencilViewVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectDepthStencilViewVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectDepthStencilViewVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectDepthStencilViewVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectDepthStencilViewVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectDepthStencilViewVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectDepthStencilViewVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectDepthStencilViewVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectDepthStencilViewVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectDepthStencilViewVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectDepthStencilViewVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectDepthStencilViewVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectDepthStencilViewVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectDepthStencilViewVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *SetDepthStencil)(
        ID3D10EffectDepthStencilViewVariable *This,
        ID3D10DepthStencilView *view);

    HRESULT (STDMETHODCALLTYPE *GetDepthStencil)(
        ID3D10EffectDepthStencilViewVariable *This,
        ID3D10DepthStencilView **view);

    HRESULT (STDMETHODCALLTYPE *SetDepthStencilArray)(
        ID3D10EffectDepthStencilViewVariable *This,
        ID3D10DepthStencilView **views,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetDepthStencilArray)(
        ID3D10EffectDepthStencilViewVariable *This,
        ID3D10DepthStencilView **views,
        UINT offset,
        UINT count);

    END_INTERFACE
} ID3D10EffectDepthStencilViewVariableVtbl;

interface ID3D10EffectDepthStencilViewVariable {
    CONST_VTBL ID3D10EffectDepthStencilViewVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectDepthStencilViewVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectDepthStencilViewVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectDepthStencilViewVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectDepthStencilViewVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectDepthStencilViewVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectDepthStencilViewVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectDepthStencilViewVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectDepthStencilViewVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectDepthStencilViewVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectDepthStencilViewVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectDepthStencilViewVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectDepthStencilViewVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectDepthStencilViewVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectDepthStencilViewVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectDepthStencilViewVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectDepthStencilViewVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectDepthStencilViewVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectDepthStencilViewVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectDepthStencilViewVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectDepthStencilViewVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectDepthStencilViewVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectDepthStencilViewVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectDepthStencilViewVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectDepthStencilViewVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectDepthStencilViewVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectDepthStencilViewVariable methods ***/
#define ID3D10EffectDepthStencilViewVariable_SetDepthStencil(This,view) (This)->lpVtbl->SetDepthStencil(This,view)
#define ID3D10EffectDepthStencilViewVariable_GetDepthStencil(This,view) (This)->lpVtbl->GetDepthStencil(This,view)
#define ID3D10EffectDepthStencilViewVariable_SetDepthStencilArray(This,views,offset,count) (This)->lpVtbl->SetDepthStencilArray(This,views,offset,count)
#define ID3D10EffectDepthStencilViewVariable_GetDepthStencilArray(This,views,offset,count) (This)->lpVtbl->GetDepthStencilArray(This,views,offset,count)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectDepthStencilViewVariable_IsValid(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectDepthStencilViewVariable_GetType(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectDepthStencilViewVariable_GetDesc(ID3D10EffectDepthStencilViewVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilViewVariable_GetAnnotationByIndex(ID3D10EffectDepthStencilViewVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilViewVariable_GetAnnotationByName(ID3D10EffectDepthStencilViewVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilViewVariable_GetMemberByIndex(ID3D10EffectDepthStencilViewVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilViewVariable_GetMemberByName(ID3D10EffectDepthStencilViewVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilViewVariable_GetMemberBySemantic(ID3D10EffectDepthStencilViewVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilViewVariable_GetElement(ID3D10EffectDepthStencilViewVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectDepthStencilViewVariable_GetParentConstantBuffer(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectDepthStencilViewVariable_AsScalar(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectDepthStencilViewVariable_AsVector(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectDepthStencilViewVariable_AsMatrix(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectDepthStencilViewVariable_AsString(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectDepthStencilViewVariable_AsShaderResource(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectDepthStencilViewVariable_AsRenderTargetView(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectDepthStencilViewVariable_AsDepthStencilView(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectDepthStencilViewVariable_AsConstantBuffer(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectDepthStencilViewVariable_AsShader(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectDepthStencilViewVariable_AsBlend(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectDepthStencilViewVariable_AsDepthStencil(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectDepthStencilViewVariable_AsRasterizer(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectDepthStencilViewVariable_AsSampler(ID3D10EffectDepthStencilViewVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectDepthStencilViewVariable_SetRawValue(ID3D10EffectDepthStencilViewVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectDepthStencilViewVariable_GetRawValue(ID3D10EffectDepthStencilViewVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectDepthStencilViewVariable methods ***/
static inline HRESULT ID3D10EffectDepthStencilViewVariable_SetDepthStencil(ID3D10EffectDepthStencilViewVariable* This,ID3D10DepthStencilView *view) {
    return This->lpVtbl->SetDepthStencil(This,view);
}
static inline HRESULT ID3D10EffectDepthStencilViewVariable_GetDepthStencil(ID3D10EffectDepthStencilViewVariable* This,ID3D10DepthStencilView **view) {
    return This->lpVtbl->GetDepthStencil(This,view);
}
static inline HRESULT ID3D10EffectDepthStencilViewVariable_SetDepthStencilArray(ID3D10EffectDepthStencilViewVariable* This,ID3D10DepthStencilView **views,UINT offset,UINT count) {
    return This->lpVtbl->SetDepthStencilArray(This,views,offset,count);
}
static inline HRESULT ID3D10EffectDepthStencilViewVariable_GetDepthStencilArray(ID3D10EffectDepthStencilViewVariable* This,ID3D10DepthStencilView **views,UINT offset,UINT count) {
    return This->lpVtbl->GetDepthStencilArray(This,views,offset,count);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectDepthStencilViewVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectShaderVariable interface
 */
#ifndef __ID3D10EffectShaderVariable_INTERFACE_DEFINED__
#define __ID3D10EffectShaderVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectShaderVariable, 0x80849279, 0xc799, 0x4797, 0x8c,0x33, 0x04,0x07,0xa0,0x7d,0x9e,0x06);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("80849279-c799-4797-8c33-0407a07d9e06")
ID3D10EffectShaderVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE GetShaderDesc(
        UINT index,
        D3D10_EFFECT_SHADER_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVertexShader(
        UINT index,
        ID3D10VertexShader **shader) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGeometryShader(
        UINT index,
        ID3D10GeometryShader **shader) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPixelShader(
        UINT index,
        ID3D10PixelShader **shader) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInputSignatureElementDesc(
        UINT shader_index,
        UINT element_index,
        D3D10_SIGNATURE_PARAMETER_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputSignatureElementDesc(
        UINT shader_index,
        UINT element_index,
        D3D10_SIGNATURE_PARAMETER_DESC *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectShaderVariable, 0x80849279, 0xc799, 0x4797, 0x8c,0x33, 0x04,0x07,0xa0,0x7d,0x9e,0x06)
#endif
#else
typedef struct ID3D10EffectShaderVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectShaderVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectShaderVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectShaderVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectShaderVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectShaderVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectShaderVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectShaderVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectShaderVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectShaderVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectShaderVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectShaderVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectShaderVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectShaderVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetShaderDesc)(
        ID3D10EffectShaderVariable *This,
        UINT index,
        D3D10_EFFECT_SHADER_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetVertexShader)(
        ID3D10EffectShaderVariable *This,
        UINT index,
        ID3D10VertexShader **shader);

    HRESULT (STDMETHODCALLTYPE *GetGeometryShader)(
        ID3D10EffectShaderVariable *This,
        UINT index,
        ID3D10GeometryShader **shader);

    HRESULT (STDMETHODCALLTYPE *GetPixelShader)(
        ID3D10EffectShaderVariable *This,
        UINT index,
        ID3D10PixelShader **shader);

    HRESULT (STDMETHODCALLTYPE *GetInputSignatureElementDesc)(
        ID3D10EffectShaderVariable *This,
        UINT shader_index,
        UINT element_index,
        D3D10_SIGNATURE_PARAMETER_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetOutputSignatureElementDesc)(
        ID3D10EffectShaderVariable *This,
        UINT shader_index,
        UINT element_index,
        D3D10_SIGNATURE_PARAMETER_DESC *desc);

    END_INTERFACE
} ID3D10EffectShaderVariableVtbl;

interface ID3D10EffectShaderVariable {
    CONST_VTBL ID3D10EffectShaderVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectShaderVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectShaderVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectShaderVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectShaderVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectShaderVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectShaderVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectShaderVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectShaderVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectShaderVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectShaderVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectShaderVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectShaderVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectShaderVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectShaderVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectShaderVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectShaderVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectShaderVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectShaderVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectShaderVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectShaderVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectShaderVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectShaderVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectShaderVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectShaderVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectShaderVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectShaderVariable methods ***/
#define ID3D10EffectShaderVariable_GetShaderDesc(This,index,desc) (This)->lpVtbl->GetShaderDesc(This,index,desc)
#define ID3D10EffectShaderVariable_GetVertexShader(This,index,shader) (This)->lpVtbl->GetVertexShader(This,index,shader)
#define ID3D10EffectShaderVariable_GetGeometryShader(This,index,shader) (This)->lpVtbl->GetGeometryShader(This,index,shader)
#define ID3D10EffectShaderVariable_GetPixelShader(This,index,shader) (This)->lpVtbl->GetPixelShader(This,index,shader)
#define ID3D10EffectShaderVariable_GetInputSignatureElementDesc(This,shader_index,element_index,desc) (This)->lpVtbl->GetInputSignatureElementDesc(This,shader_index,element_index,desc)
#define ID3D10EffectShaderVariable_GetOutputSignatureElementDesc(This,shader_index,element_index,desc) (This)->lpVtbl->GetOutputSignatureElementDesc(This,shader_index,element_index,desc)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectShaderVariable_IsValid(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectShaderVariable_GetType(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectShaderVariable_GetDesc(ID3D10EffectShaderVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderVariable_GetAnnotationByIndex(ID3D10EffectShaderVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderVariable_GetAnnotationByName(ID3D10EffectShaderVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderVariable_GetMemberByIndex(ID3D10EffectShaderVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderVariable_GetMemberByName(ID3D10EffectShaderVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderVariable_GetMemberBySemantic(ID3D10EffectShaderVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectShaderVariable_GetElement(ID3D10EffectShaderVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectShaderVariable_GetParentConstantBuffer(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectShaderVariable_AsScalar(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectShaderVariable_AsVector(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectShaderVariable_AsMatrix(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectShaderVariable_AsString(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectShaderVariable_AsShaderResource(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectShaderVariable_AsRenderTargetView(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectShaderVariable_AsDepthStencilView(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectShaderVariable_AsConstantBuffer(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectShaderVariable_AsShader(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectShaderVariable_AsBlend(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectShaderVariable_AsDepthStencil(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectShaderVariable_AsRasterizer(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectShaderVariable_AsSampler(ID3D10EffectShaderVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectShaderVariable_SetRawValue(ID3D10EffectShaderVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectShaderVariable_GetRawValue(ID3D10EffectShaderVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectShaderVariable methods ***/
static inline HRESULT ID3D10EffectShaderVariable_GetShaderDesc(ID3D10EffectShaderVariable* This,UINT index,D3D10_EFFECT_SHADER_DESC *desc) {
    return This->lpVtbl->GetShaderDesc(This,index,desc);
}
static inline HRESULT ID3D10EffectShaderVariable_GetVertexShader(ID3D10EffectShaderVariable* This,UINT index,ID3D10VertexShader **shader) {
    return This->lpVtbl->GetVertexShader(This,index,shader);
}
static inline HRESULT ID3D10EffectShaderVariable_GetGeometryShader(ID3D10EffectShaderVariable* This,UINT index,ID3D10GeometryShader **shader) {
    return This->lpVtbl->GetGeometryShader(This,index,shader);
}
static inline HRESULT ID3D10EffectShaderVariable_GetPixelShader(ID3D10EffectShaderVariable* This,UINT index,ID3D10PixelShader **shader) {
    return This->lpVtbl->GetPixelShader(This,index,shader);
}
static inline HRESULT ID3D10EffectShaderVariable_GetInputSignatureElementDesc(ID3D10EffectShaderVariable* This,UINT shader_index,UINT element_index,D3D10_SIGNATURE_PARAMETER_DESC *desc) {
    return This->lpVtbl->GetInputSignatureElementDesc(This,shader_index,element_index,desc);
}
static inline HRESULT ID3D10EffectShaderVariable_GetOutputSignatureElementDesc(ID3D10EffectShaderVariable* This,UINT shader_index,UINT element_index,D3D10_SIGNATURE_PARAMETER_DESC *desc) {
    return This->lpVtbl->GetOutputSignatureElementDesc(This,shader_index,element_index,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectShaderVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectBlendVariable interface
 */
#ifndef __ID3D10EffectBlendVariable_INTERFACE_DEFINED__
#define __ID3D10EffectBlendVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectBlendVariable, 0x1fcd2294, 0xdf6d, 0x4eae, 0x86,0xb3, 0x0e,0x91,0x60,0xcf,0xb0,0x7b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1fcd2294-df6d-4eae-86b3-0e9160cfb07b")
ID3D10EffectBlendVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE GetBlendState(
        UINT index,
        ID3D10BlendState **blend_state) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackingStore(
        UINT index,
        D3D10_BLEND_DESC *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectBlendVariable, 0x1fcd2294, 0xdf6d, 0x4eae, 0x86,0xb3, 0x0e,0x91,0x60,0xcf,0xb0,0x7b)
#endif
#else
typedef struct ID3D10EffectBlendVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectBlendVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectBlendVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectBlendVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectBlendVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectBlendVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectBlendVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectBlendVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectBlendVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectBlendVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectBlendVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectBlendVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectBlendVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectBlendVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetBlendState)(
        ID3D10EffectBlendVariable *This,
        UINT index,
        ID3D10BlendState **blend_state);

    HRESULT (STDMETHODCALLTYPE *GetBackingStore)(
        ID3D10EffectBlendVariable *This,
        UINT index,
        D3D10_BLEND_DESC *desc);

    END_INTERFACE
} ID3D10EffectBlendVariableVtbl;

interface ID3D10EffectBlendVariable {
    CONST_VTBL ID3D10EffectBlendVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectBlendVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectBlendVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectBlendVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectBlendVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectBlendVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectBlendVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectBlendVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectBlendVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectBlendVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectBlendVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectBlendVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectBlendVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectBlendVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectBlendVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectBlendVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectBlendVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectBlendVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectBlendVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectBlendVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectBlendVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectBlendVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectBlendVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectBlendVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectBlendVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectBlendVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectBlendVariable methods ***/
#define ID3D10EffectBlendVariable_GetBlendState(This,index,blend_state) (This)->lpVtbl->GetBlendState(This,index,blend_state)
#define ID3D10EffectBlendVariable_GetBackingStore(This,index,desc) (This)->lpVtbl->GetBackingStore(This,index,desc)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectBlendVariable_IsValid(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectBlendVariable_GetType(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectBlendVariable_GetDesc(ID3D10EffectBlendVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectBlendVariable_GetAnnotationByIndex(ID3D10EffectBlendVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectBlendVariable_GetAnnotationByName(ID3D10EffectBlendVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectBlendVariable_GetMemberByIndex(ID3D10EffectBlendVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectBlendVariable_GetMemberByName(ID3D10EffectBlendVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectBlendVariable_GetMemberBySemantic(ID3D10EffectBlendVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectBlendVariable_GetElement(ID3D10EffectBlendVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectBlendVariable_GetParentConstantBuffer(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectBlendVariable_AsScalar(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectBlendVariable_AsVector(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectBlendVariable_AsMatrix(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectBlendVariable_AsString(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectBlendVariable_AsShaderResource(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectBlendVariable_AsRenderTargetView(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectBlendVariable_AsDepthStencilView(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectBlendVariable_AsConstantBuffer(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectBlendVariable_AsShader(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectBlendVariable_AsBlend(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectBlendVariable_AsDepthStencil(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectBlendVariable_AsRasterizer(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectBlendVariable_AsSampler(ID3D10EffectBlendVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectBlendVariable_SetRawValue(ID3D10EffectBlendVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectBlendVariable_GetRawValue(ID3D10EffectBlendVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectBlendVariable methods ***/
static inline HRESULT ID3D10EffectBlendVariable_GetBlendState(ID3D10EffectBlendVariable* This,UINT index,ID3D10BlendState **blend_state) {
    return This->lpVtbl->GetBlendState(This,index,blend_state);
}
static inline HRESULT ID3D10EffectBlendVariable_GetBackingStore(ID3D10EffectBlendVariable* This,UINT index,D3D10_BLEND_DESC *desc) {
    return This->lpVtbl->GetBackingStore(This,index,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectBlendVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectDepthStencilVariable interface
 */
#ifndef __ID3D10EffectDepthStencilVariable_INTERFACE_DEFINED__
#define __ID3D10EffectDepthStencilVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectDepthStencilVariable, 0xaf482368, 0x330a, 0x46a5, 0x9a,0x5c, 0x01,0xc7,0x1a,0xf2,0x4c,0x8d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("af482368-330a-46a5-9a5c-01c71af24c8d")
ID3D10EffectDepthStencilVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE GetDepthStencilState(
        UINT index,
        ID3D10DepthStencilState **depth_stencil_state) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackingStore(
        UINT index,
        D3D10_DEPTH_STENCIL_DESC *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectDepthStencilVariable, 0xaf482368, 0x330a, 0x46a5, 0x9a,0x5c, 0x01,0xc7,0x1a,0xf2,0x4c,0x8d)
#endif
#else
typedef struct ID3D10EffectDepthStencilVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectDepthStencilVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectDepthStencilVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectDepthStencilVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectDepthStencilVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectDepthStencilVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectDepthStencilVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectDepthStencilVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectDepthStencilVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectDepthStencilVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectDepthStencilVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectDepthStencilVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectDepthStencilVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectDepthStencilVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDepthStencilState)(
        ID3D10EffectDepthStencilVariable *This,
        UINT index,
        ID3D10DepthStencilState **depth_stencil_state);

    HRESULT (STDMETHODCALLTYPE *GetBackingStore)(
        ID3D10EffectDepthStencilVariable *This,
        UINT index,
        D3D10_DEPTH_STENCIL_DESC *desc);

    END_INTERFACE
} ID3D10EffectDepthStencilVariableVtbl;

interface ID3D10EffectDepthStencilVariable {
    CONST_VTBL ID3D10EffectDepthStencilVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectDepthStencilVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectDepthStencilVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectDepthStencilVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectDepthStencilVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectDepthStencilVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectDepthStencilVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectDepthStencilVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectDepthStencilVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectDepthStencilVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectDepthStencilVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectDepthStencilVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectDepthStencilVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectDepthStencilVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectDepthStencilVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectDepthStencilVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectDepthStencilVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectDepthStencilVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectDepthStencilVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectDepthStencilVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectDepthStencilVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectDepthStencilVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectDepthStencilVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectDepthStencilVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectDepthStencilVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectDepthStencilVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectDepthStencilVariable methods ***/
#define ID3D10EffectDepthStencilVariable_GetDepthStencilState(This,index,depth_stencil_state) (This)->lpVtbl->GetDepthStencilState(This,index,depth_stencil_state)
#define ID3D10EffectDepthStencilVariable_GetBackingStore(This,index,desc) (This)->lpVtbl->GetBackingStore(This,index,desc)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectDepthStencilVariable_IsValid(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectDepthStencilVariable_GetType(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectDepthStencilVariable_GetDesc(ID3D10EffectDepthStencilVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilVariable_GetAnnotationByIndex(ID3D10EffectDepthStencilVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilVariable_GetAnnotationByName(ID3D10EffectDepthStencilVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilVariable_GetMemberByIndex(ID3D10EffectDepthStencilVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilVariable_GetMemberByName(ID3D10EffectDepthStencilVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilVariable_GetMemberBySemantic(ID3D10EffectDepthStencilVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectDepthStencilVariable_GetElement(ID3D10EffectDepthStencilVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectDepthStencilVariable_GetParentConstantBuffer(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectDepthStencilVariable_AsScalar(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectDepthStencilVariable_AsVector(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectDepthStencilVariable_AsMatrix(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectDepthStencilVariable_AsString(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectDepthStencilVariable_AsShaderResource(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectDepthStencilVariable_AsRenderTargetView(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectDepthStencilVariable_AsDepthStencilView(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectDepthStencilVariable_AsConstantBuffer(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectDepthStencilVariable_AsShader(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectDepthStencilVariable_AsBlend(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectDepthStencilVariable_AsDepthStencil(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectDepthStencilVariable_AsRasterizer(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectDepthStencilVariable_AsSampler(ID3D10EffectDepthStencilVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectDepthStencilVariable_SetRawValue(ID3D10EffectDepthStencilVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectDepthStencilVariable_GetRawValue(ID3D10EffectDepthStencilVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectDepthStencilVariable methods ***/
static inline HRESULT ID3D10EffectDepthStencilVariable_GetDepthStencilState(ID3D10EffectDepthStencilVariable* This,UINT index,ID3D10DepthStencilState **depth_stencil_state) {
    return This->lpVtbl->GetDepthStencilState(This,index,depth_stencil_state);
}
static inline HRESULT ID3D10EffectDepthStencilVariable_GetBackingStore(ID3D10EffectDepthStencilVariable* This,UINT index,D3D10_DEPTH_STENCIL_DESC *desc) {
    return This->lpVtbl->GetBackingStore(This,index,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectDepthStencilVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectRasterizerVariable interface
 */
#ifndef __ID3D10EffectRasterizerVariable_INTERFACE_DEFINED__
#define __ID3D10EffectRasterizerVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectRasterizerVariable, 0x21af9f0e, 0x4d94, 0x4ea9, 0x97,0x85, 0x2c,0xb7,0x6b,0x8c,0x0b,0x34);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("21af9f0e-4d94-4ea9-9785-2cb76b8c0b34")
ID3D10EffectRasterizerVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE GetRasterizerState(
        UINT index,
        ID3D10RasterizerState **rasterizer_state) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackingStore(
        UINT index,
        D3D10_RASTERIZER_DESC *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectRasterizerVariable, 0x21af9f0e, 0x4d94, 0x4ea9, 0x97,0x85, 0x2c,0xb7,0x6b,0x8c,0x0b,0x34)
#endif
#else
typedef struct ID3D10EffectRasterizerVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectRasterizerVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectRasterizerVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectRasterizerVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectRasterizerVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectRasterizerVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectRasterizerVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectRasterizerVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectRasterizerVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectRasterizerVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectRasterizerVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectRasterizerVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectRasterizerVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectRasterizerVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRasterizerState)(
        ID3D10EffectRasterizerVariable *This,
        UINT index,
        ID3D10RasterizerState **rasterizer_state);

    HRESULT (STDMETHODCALLTYPE *GetBackingStore)(
        ID3D10EffectRasterizerVariable *This,
        UINT index,
        D3D10_RASTERIZER_DESC *desc);

    END_INTERFACE
} ID3D10EffectRasterizerVariableVtbl;

interface ID3D10EffectRasterizerVariable {
    CONST_VTBL ID3D10EffectRasterizerVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectRasterizerVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectRasterizerVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectRasterizerVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectRasterizerVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectRasterizerVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectRasterizerVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectRasterizerVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectRasterizerVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectRasterizerVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectRasterizerVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectRasterizerVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectRasterizerVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectRasterizerVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectRasterizerVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectRasterizerVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectRasterizerVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectRasterizerVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectRasterizerVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectRasterizerVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectRasterizerVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectRasterizerVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectRasterizerVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectRasterizerVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectRasterizerVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectRasterizerVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectRasterizerVariable methods ***/
#define ID3D10EffectRasterizerVariable_GetRasterizerState(This,index,rasterizer_state) (This)->lpVtbl->GetRasterizerState(This,index,rasterizer_state)
#define ID3D10EffectRasterizerVariable_GetBackingStore(This,index,desc) (This)->lpVtbl->GetBackingStore(This,index,desc)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectRasterizerVariable_IsValid(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectRasterizerVariable_GetType(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectRasterizerVariable_GetDesc(ID3D10EffectRasterizerVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectRasterizerVariable_GetAnnotationByIndex(ID3D10EffectRasterizerVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectRasterizerVariable_GetAnnotationByName(ID3D10EffectRasterizerVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectRasterizerVariable_GetMemberByIndex(ID3D10EffectRasterizerVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectRasterizerVariable_GetMemberByName(ID3D10EffectRasterizerVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectRasterizerVariable_GetMemberBySemantic(ID3D10EffectRasterizerVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectRasterizerVariable_GetElement(ID3D10EffectRasterizerVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectRasterizerVariable_GetParentConstantBuffer(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectRasterizerVariable_AsScalar(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectRasterizerVariable_AsVector(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectRasterizerVariable_AsMatrix(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectRasterizerVariable_AsString(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectRasterizerVariable_AsShaderResource(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectRasterizerVariable_AsRenderTargetView(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectRasterizerVariable_AsDepthStencilView(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectRasterizerVariable_AsConstantBuffer(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectRasterizerVariable_AsShader(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectRasterizerVariable_AsBlend(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectRasterizerVariable_AsDepthStencil(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectRasterizerVariable_AsRasterizer(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectRasterizerVariable_AsSampler(ID3D10EffectRasterizerVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectRasterizerVariable_SetRawValue(ID3D10EffectRasterizerVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectRasterizerVariable_GetRawValue(ID3D10EffectRasterizerVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectRasterizerVariable methods ***/
static inline HRESULT ID3D10EffectRasterizerVariable_GetRasterizerState(ID3D10EffectRasterizerVariable* This,UINT index,ID3D10RasterizerState **rasterizer_state) {
    return This->lpVtbl->GetRasterizerState(This,index,rasterizer_state);
}
static inline HRESULT ID3D10EffectRasterizerVariable_GetBackingStore(ID3D10EffectRasterizerVariable* This,UINT index,D3D10_RASTERIZER_DESC *desc) {
    return This->lpVtbl->GetBackingStore(This,index,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectRasterizerVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectSamplerVariable interface
 */
#ifndef __ID3D10EffectSamplerVariable_INTERFACE_DEFINED__
#define __ID3D10EffectSamplerVariable_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectSamplerVariable, 0x6530d5c7, 0x07e9, 0x4271, 0xa4,0x18, 0xe7,0xce,0x4b,0xd1,0xe4,0x80);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6530d5c7-07e9-4271-a418-e7ce4bd1e480")
ID3D10EffectSamplerVariable : public ID3D10EffectVariable
{
    virtual HRESULT STDMETHODCALLTYPE GetSampler(
        UINT index,
        ID3D10SamplerState **sampler) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackingStore(
        UINT index,
        D3D10_SAMPLER_DESC *desc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectSamplerVariable, 0x6530d5c7, 0x07e9, 0x4271, 0xa4,0x18, 0xe7,0xce,0x4b,0xd1,0xe4,0x80)
#endif
#else
typedef struct ID3D10EffectSamplerVariableVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectVariable methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectType * (STDMETHODCALLTYPE *GetType)(
        ID3D10EffectSamplerVariable *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectSamplerVariable *This,
        D3D10_EFFECT_VARIABLE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectSamplerVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectSamplerVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByIndex)(
        ID3D10EffectSamplerVariable *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberByName)(
        ID3D10EffectSamplerVariable *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetMemberBySemantic)(
        ID3D10EffectSamplerVariable *This,
        const char *semantic);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetElement)(
        ID3D10EffectSamplerVariable *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetParentConstantBuffer)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectScalarVariable * (STDMETHODCALLTYPE *AsScalar)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectVectorVariable * (STDMETHODCALLTYPE *AsVector)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectMatrixVariable * (STDMETHODCALLTYPE *AsMatrix)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectStringVariable * (STDMETHODCALLTYPE *AsString)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectShaderResourceVariable * (STDMETHODCALLTYPE *AsShaderResource)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectRenderTargetViewVariable * (STDMETHODCALLTYPE *AsRenderTargetView)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectDepthStencilViewVariable * (STDMETHODCALLTYPE *AsDepthStencilView)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *AsConstantBuffer)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectShaderVariable * (STDMETHODCALLTYPE *AsShader)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectBlendVariable * (STDMETHODCALLTYPE *AsBlend)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectDepthStencilVariable * (STDMETHODCALLTYPE *AsDepthStencil)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectRasterizerVariable * (STDMETHODCALLTYPE *AsRasterizer)(
        ID3D10EffectSamplerVariable *This);

    ID3D10EffectSamplerVariable * (STDMETHODCALLTYPE *AsSampler)(
        ID3D10EffectSamplerVariable *This);

    HRESULT (STDMETHODCALLTYPE *SetRawValue)(
        ID3D10EffectSamplerVariable *This,
        void *data,
        UINT offset,
        UINT count);

    HRESULT (STDMETHODCALLTYPE *GetRawValue)(
        ID3D10EffectSamplerVariable *This,
        void *data,
        UINT offset,
        UINT count);

    /*** ID3D10EffectSamplerVariable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSampler)(
        ID3D10EffectSamplerVariable *This,
        UINT index,
        ID3D10SamplerState **sampler);

    HRESULT (STDMETHODCALLTYPE *GetBackingStore)(
        ID3D10EffectSamplerVariable *This,
        UINT index,
        D3D10_SAMPLER_DESC *desc);

    END_INTERFACE
} ID3D10EffectSamplerVariableVtbl;

interface ID3D10EffectSamplerVariable {
    CONST_VTBL ID3D10EffectSamplerVariableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectVariable methods ***/
#define ID3D10EffectSamplerVariable_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectSamplerVariable_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D10EffectSamplerVariable_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectSamplerVariable_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectSamplerVariable_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectSamplerVariable_GetMemberByIndex(This,index) (This)->lpVtbl->GetMemberByIndex(This,index)
#define ID3D10EffectSamplerVariable_GetMemberByName(This,name) (This)->lpVtbl->GetMemberByName(This,name)
#define ID3D10EffectSamplerVariable_GetMemberBySemantic(This,semantic) (This)->lpVtbl->GetMemberBySemantic(This,semantic)
#define ID3D10EffectSamplerVariable_GetElement(This,index) (This)->lpVtbl->GetElement(This,index)
#define ID3D10EffectSamplerVariable_GetParentConstantBuffer(This) (This)->lpVtbl->GetParentConstantBuffer(This)
#define ID3D10EffectSamplerVariable_AsScalar(This) (This)->lpVtbl->AsScalar(This)
#define ID3D10EffectSamplerVariable_AsVector(This) (This)->lpVtbl->AsVector(This)
#define ID3D10EffectSamplerVariable_AsMatrix(This) (This)->lpVtbl->AsMatrix(This)
#define ID3D10EffectSamplerVariable_AsString(This) (This)->lpVtbl->AsString(This)
#define ID3D10EffectSamplerVariable_AsShaderResource(This) (This)->lpVtbl->AsShaderResource(This)
#define ID3D10EffectSamplerVariable_AsRenderTargetView(This) (This)->lpVtbl->AsRenderTargetView(This)
#define ID3D10EffectSamplerVariable_AsDepthStencilView(This) (This)->lpVtbl->AsDepthStencilView(This)
#define ID3D10EffectSamplerVariable_AsConstantBuffer(This) (This)->lpVtbl->AsConstantBuffer(This)
#define ID3D10EffectSamplerVariable_AsShader(This) (This)->lpVtbl->AsShader(This)
#define ID3D10EffectSamplerVariable_AsBlend(This) (This)->lpVtbl->AsBlend(This)
#define ID3D10EffectSamplerVariable_AsDepthStencil(This) (This)->lpVtbl->AsDepthStencil(This)
#define ID3D10EffectSamplerVariable_AsRasterizer(This) (This)->lpVtbl->AsRasterizer(This)
#define ID3D10EffectSamplerVariable_AsSampler(This) (This)->lpVtbl->AsSampler(This)
#define ID3D10EffectSamplerVariable_SetRawValue(This,data,offset,count) (This)->lpVtbl->SetRawValue(This,data,offset,count)
#define ID3D10EffectSamplerVariable_GetRawValue(This,data,offset,count) (This)->lpVtbl->GetRawValue(This,data,offset,count)
/*** ID3D10EffectSamplerVariable methods ***/
#define ID3D10EffectSamplerVariable_GetSampler(This,index,sampler) (This)->lpVtbl->GetSampler(This,index,sampler)
#define ID3D10EffectSamplerVariable_GetBackingStore(This,index,desc) (This)->lpVtbl->GetBackingStore(This,index,desc)
#else
/*** ID3D10EffectVariable methods ***/
static inline WINBOOL ID3D10EffectSamplerVariable_IsValid(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->IsValid(This);
}
static inline ID3D10EffectType * ID3D10EffectSamplerVariable_GetType(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT ID3D10EffectSamplerVariable_GetDesc(ID3D10EffectSamplerVariable* This,D3D10_EFFECT_VARIABLE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectSamplerVariable_GetAnnotationByIndex(ID3D10EffectSamplerVariable* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectSamplerVariable_GetAnnotationByName(ID3D10EffectSamplerVariable* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectSamplerVariable_GetMemberByIndex(ID3D10EffectSamplerVariable* This,UINT index) {
    return This->lpVtbl->GetMemberByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectSamplerVariable_GetMemberByName(ID3D10EffectSamplerVariable* This,const char *name) {
    return This->lpVtbl->GetMemberByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10EffectSamplerVariable_GetMemberBySemantic(ID3D10EffectSamplerVariable* This,const char *semantic) {
    return This->lpVtbl->GetMemberBySemantic(This,semantic);
}
static inline ID3D10EffectVariable * ID3D10EffectSamplerVariable_GetElement(ID3D10EffectSamplerVariable* This,UINT index) {
    return This->lpVtbl->GetElement(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectSamplerVariable_GetParentConstantBuffer(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->GetParentConstantBuffer(This);
}
static inline ID3D10EffectScalarVariable * ID3D10EffectSamplerVariable_AsScalar(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsScalar(This);
}
static inline ID3D10EffectVectorVariable * ID3D10EffectSamplerVariable_AsVector(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsVector(This);
}
static inline ID3D10EffectMatrixVariable * ID3D10EffectSamplerVariable_AsMatrix(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsMatrix(This);
}
static inline ID3D10EffectStringVariable * ID3D10EffectSamplerVariable_AsString(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsString(This);
}
static inline ID3D10EffectShaderResourceVariable * ID3D10EffectSamplerVariable_AsShaderResource(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsShaderResource(This);
}
static inline ID3D10EffectRenderTargetViewVariable * ID3D10EffectSamplerVariable_AsRenderTargetView(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsRenderTargetView(This);
}
static inline ID3D10EffectDepthStencilViewVariable * ID3D10EffectSamplerVariable_AsDepthStencilView(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsDepthStencilView(This);
}
static inline ID3D10EffectConstantBuffer * ID3D10EffectSamplerVariable_AsConstantBuffer(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsConstantBuffer(This);
}
static inline ID3D10EffectShaderVariable * ID3D10EffectSamplerVariable_AsShader(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsShader(This);
}
static inline ID3D10EffectBlendVariable * ID3D10EffectSamplerVariable_AsBlend(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsBlend(This);
}
static inline ID3D10EffectDepthStencilVariable * ID3D10EffectSamplerVariable_AsDepthStencil(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsDepthStencil(This);
}
static inline ID3D10EffectRasterizerVariable * ID3D10EffectSamplerVariable_AsRasterizer(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsRasterizer(This);
}
static inline ID3D10EffectSamplerVariable * ID3D10EffectSamplerVariable_AsSampler(ID3D10EffectSamplerVariable* This) {
    return This->lpVtbl->AsSampler(This);
}
static inline HRESULT ID3D10EffectSamplerVariable_SetRawValue(ID3D10EffectSamplerVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->SetRawValue(This,data,offset,count);
}
static inline HRESULT ID3D10EffectSamplerVariable_GetRawValue(ID3D10EffectSamplerVariable* This,void *data,UINT offset,UINT count) {
    return This->lpVtbl->GetRawValue(This,data,offset,count);
}
/*** ID3D10EffectSamplerVariable methods ***/
static inline HRESULT ID3D10EffectSamplerVariable_GetSampler(ID3D10EffectSamplerVariable* This,UINT index,ID3D10SamplerState **sampler) {
    return This->lpVtbl->GetSampler(This,index,sampler);
}
static inline HRESULT ID3D10EffectSamplerVariable_GetBackingStore(ID3D10EffectSamplerVariable* This,UINT index,D3D10_SAMPLER_DESC *desc) {
    return This->lpVtbl->GetBackingStore(This,index,desc);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectSamplerVariable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectTechnique interface
 */
#ifndef __ID3D10EffectTechnique_INTERFACE_DEFINED__
#define __ID3D10EffectTechnique_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectTechnique, 0xdb122ce8, 0xd1c9, 0x4292, 0xb2,0x37, 0x24,0xed,0x3d,0xe8,0xb1,0x75);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("db122ce8-d1c9-4292-b237-24ed3de8b175")
ID3D10EffectTechnique
{

    BEGIN_INTERFACE

    virtual WINBOOL STDMETHODCALLTYPE IsValid(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D10_TECHNIQUE_DESC *desc) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetAnnotationByIndex(
        UINT index) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetAnnotationByName(
        const char *name) = 0;

    virtual ID3D10EffectPass * STDMETHODCALLTYPE GetPassByIndex(
        UINT index) = 0;

    virtual ID3D10EffectPass * STDMETHODCALLTYPE GetPassByName(
        const char *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE ComputeStateBlockMask(
        D3D10_STATE_BLOCK_MASK *mask) = 0;

    END_INTERFACE

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectTechnique, 0xdb122ce8, 0xd1c9, 0x4292, 0xb2,0x37, 0x24,0xed,0x3d,0xe8,0xb1,0x75)
#endif
#else
typedef struct ID3D10EffectTechniqueVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectTechnique methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectTechnique *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectTechnique *This,
        D3D10_TECHNIQUE_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectTechnique *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectTechnique *This,
        const char *name);

    ID3D10EffectPass * (STDMETHODCALLTYPE *GetPassByIndex)(
        ID3D10EffectTechnique *This,
        UINT index);

    ID3D10EffectPass * (STDMETHODCALLTYPE *GetPassByName)(
        ID3D10EffectTechnique *This,
        const char *name);

    HRESULT (STDMETHODCALLTYPE *ComputeStateBlockMask)(
        ID3D10EffectTechnique *This,
        D3D10_STATE_BLOCK_MASK *mask);

    END_INTERFACE
} ID3D10EffectTechniqueVtbl;

interface ID3D10EffectTechnique {
    CONST_VTBL ID3D10EffectTechniqueVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectTechnique methods ***/
#define ID3D10EffectTechnique_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectTechnique_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectTechnique_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectTechnique_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectTechnique_GetPassByIndex(This,index) (This)->lpVtbl->GetPassByIndex(This,index)
#define ID3D10EffectTechnique_GetPassByName(This,name) (This)->lpVtbl->GetPassByName(This,name)
#define ID3D10EffectTechnique_ComputeStateBlockMask(This,mask) (This)->lpVtbl->ComputeStateBlockMask(This,mask)
#else
/*** ID3D10EffectTechnique methods ***/
static inline WINBOOL ID3D10EffectTechnique_IsValid(ID3D10EffectTechnique* This) {
    return This->lpVtbl->IsValid(This);
}
static inline HRESULT ID3D10EffectTechnique_GetDesc(ID3D10EffectTechnique* This,D3D10_TECHNIQUE_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectTechnique_GetAnnotationByIndex(ID3D10EffectTechnique* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectTechnique_GetAnnotationByName(ID3D10EffectTechnique* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline ID3D10EffectPass * ID3D10EffectTechnique_GetPassByIndex(ID3D10EffectTechnique* This,UINT index) {
    return This->lpVtbl->GetPassByIndex(This,index);
}
static inline ID3D10EffectPass * ID3D10EffectTechnique_GetPassByName(ID3D10EffectTechnique* This,const char *name) {
    return This->lpVtbl->GetPassByName(This,name);
}
static inline HRESULT ID3D10EffectTechnique_ComputeStateBlockMask(ID3D10EffectTechnique* This,D3D10_STATE_BLOCK_MASK *mask) {
    return This->lpVtbl->ComputeStateBlockMask(This,mask);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectTechnique_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10Effect interface
 */
#ifndef __ID3D10Effect_INTERFACE_DEFINED__
#define __ID3D10Effect_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10Effect, 0x51b0ca8b, 0xec0b, 0x4519, 0x87,0x0d, 0x8e,0xe1,0xcb,0x50,0x17,0xc7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("51b0ca8b-ec0b-4519-870d-8ee1cb5017c7")
ID3D10Effect : public IUnknown
{
    virtual WINBOOL STDMETHODCALLTYPE IsValid(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsPool(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDevice(
        ID3D10Device **device) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D10_EFFECT_DESC *desc) = 0;

    virtual ID3D10EffectConstantBuffer * STDMETHODCALLTYPE GetConstantBufferByIndex(
        UINT index) = 0;

    virtual ID3D10EffectConstantBuffer * STDMETHODCALLTYPE GetConstantBufferByName(
        const char *name) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetVariableByIndex(
        UINT index) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetVariableByName(
        const char *name) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetVariableBySemantic(
        const char *semantic) = 0;

    virtual ID3D10EffectTechnique * STDMETHODCALLTYPE GetTechniqueByIndex(
        UINT index) = 0;

    virtual ID3D10EffectTechnique * STDMETHODCALLTYPE GetTechniqueByName(
        const char *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE Optimize(
        ) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsOptimized(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10Effect, 0x51b0ca8b, 0xec0b, 0x4519, 0x87,0x0d, 0x8e,0xe1,0xcb,0x50,0x17,0xc7)
#endif
#else
typedef struct ID3D10EffectVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D10Effect *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D10Effect *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D10Effect *This);

    /*** ID3D10Effect methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10Effect *This);

    WINBOOL (STDMETHODCALLTYPE *IsPool)(
        ID3D10Effect *This);

    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D10Effect *This,
        ID3D10Device **device);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10Effect *This,
        D3D10_EFFECT_DESC *desc);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetConstantBufferByIndex)(
        ID3D10Effect *This,
        UINT index);

    ID3D10EffectConstantBuffer * (STDMETHODCALLTYPE *GetConstantBufferByName)(
        ID3D10Effect *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetVariableByIndex)(
        ID3D10Effect *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetVariableByName)(
        ID3D10Effect *This,
        const char *name);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetVariableBySemantic)(
        ID3D10Effect *This,
        const char *semantic);

    ID3D10EffectTechnique * (STDMETHODCALLTYPE *GetTechniqueByIndex)(
        ID3D10Effect *This,
        UINT index);

    ID3D10EffectTechnique * (STDMETHODCALLTYPE *GetTechniqueByName)(
        ID3D10Effect *This,
        const char *name);

    HRESULT (STDMETHODCALLTYPE *Optimize)(
        ID3D10Effect *This);

    WINBOOL (STDMETHODCALLTYPE *IsOptimized)(
        ID3D10Effect *This);

    END_INTERFACE
} ID3D10EffectVtbl;

interface ID3D10Effect {
    CONST_VTBL ID3D10EffectVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D10Effect_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D10Effect_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D10Effect_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D10Effect methods ***/
#define ID3D10Effect_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10Effect_IsPool(This) (This)->lpVtbl->IsPool(This)
#define ID3D10Effect_GetDevice(This,device) (This)->lpVtbl->GetDevice(This,device)
#define ID3D10Effect_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10Effect_GetConstantBufferByIndex(This,index) (This)->lpVtbl->GetConstantBufferByIndex(This,index)
#define ID3D10Effect_GetConstantBufferByName(This,name) (This)->lpVtbl->GetConstantBufferByName(This,name)
#define ID3D10Effect_GetVariableByIndex(This,index) (This)->lpVtbl->GetVariableByIndex(This,index)
#define ID3D10Effect_GetVariableByName(This,name) (This)->lpVtbl->GetVariableByName(This,name)
#define ID3D10Effect_GetVariableBySemantic(This,semantic) (This)->lpVtbl->GetVariableBySemantic(This,semantic)
#define ID3D10Effect_GetTechniqueByIndex(This,index) (This)->lpVtbl->GetTechniqueByIndex(This,index)
#define ID3D10Effect_GetTechniqueByName(This,name) (This)->lpVtbl->GetTechniqueByName(This,name)
#define ID3D10Effect_Optimize(This) (This)->lpVtbl->Optimize(This)
#define ID3D10Effect_IsOptimized(This) (This)->lpVtbl->IsOptimized(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D10Effect_QueryInterface(ID3D10Effect* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D10Effect_AddRef(ID3D10Effect* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D10Effect_Release(ID3D10Effect* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D10Effect methods ***/
static inline WINBOOL ID3D10Effect_IsValid(ID3D10Effect* This) {
    return This->lpVtbl->IsValid(This);
}
static inline WINBOOL ID3D10Effect_IsPool(ID3D10Effect* This) {
    return This->lpVtbl->IsPool(This);
}
static inline HRESULT ID3D10Effect_GetDevice(ID3D10Effect* This,ID3D10Device **device) {
    return This->lpVtbl->GetDevice(This,device);
}
static inline HRESULT ID3D10Effect_GetDesc(ID3D10Effect* This,D3D10_EFFECT_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline ID3D10EffectConstantBuffer * ID3D10Effect_GetConstantBufferByIndex(ID3D10Effect* This,UINT index) {
    return This->lpVtbl->GetConstantBufferByIndex(This,index);
}
static inline ID3D10EffectConstantBuffer * ID3D10Effect_GetConstantBufferByName(ID3D10Effect* This,const char *name) {
    return This->lpVtbl->GetConstantBufferByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10Effect_GetVariableByIndex(ID3D10Effect* This,UINT index) {
    return This->lpVtbl->GetVariableByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10Effect_GetVariableByName(ID3D10Effect* This,const char *name) {
    return This->lpVtbl->GetVariableByName(This,name);
}
static inline ID3D10EffectVariable * ID3D10Effect_GetVariableBySemantic(ID3D10Effect* This,const char *semantic) {
    return This->lpVtbl->GetVariableBySemantic(This,semantic);
}
static inline ID3D10EffectTechnique * ID3D10Effect_GetTechniqueByIndex(ID3D10Effect* This,UINT index) {
    return This->lpVtbl->GetTechniqueByIndex(This,index);
}
static inline ID3D10EffectTechnique * ID3D10Effect_GetTechniqueByName(ID3D10Effect* This,const char *name) {
    return This->lpVtbl->GetTechniqueByName(This,name);
}
static inline HRESULT ID3D10Effect_Optimize(ID3D10Effect* This) {
    return This->lpVtbl->Optimize(This);
}
static inline WINBOOL ID3D10Effect_IsOptimized(ID3D10Effect* This) {
    return This->lpVtbl->IsOptimized(This);
}
#endif
#endif

#endif


#endif  /* __ID3D10Effect_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectPool interface
 */
#ifndef __ID3D10EffectPool_INTERFACE_DEFINED__
#define __ID3D10EffectPool_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectPool, 0x9537ab04, 0x3250, 0x412e, 0x82,0x13, 0xfc,0xd2,0xf8,0x67,0x79,0x33);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9537ab04-3250-412e-8213-fcd2f8677933")
ID3D10EffectPool : public IUnknown
{
    virtual ID3D10Effect * STDMETHODCALLTYPE AsEffect(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectPool, 0x9537ab04, 0x3250, 0x412e, 0x82,0x13, 0xfc,0xd2,0xf8,0x67,0x79,0x33)
#endif
#else
typedef struct ID3D10EffectPoolVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D10EffectPool *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D10EffectPool *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D10EffectPool *This);

    /*** ID3D10EffectPool methods ***/
    ID3D10Effect * (STDMETHODCALLTYPE *AsEffect)(
        ID3D10EffectPool *This);

    END_INTERFACE
} ID3D10EffectPoolVtbl;

interface ID3D10EffectPool {
    CONST_VTBL ID3D10EffectPoolVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D10EffectPool_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D10EffectPool_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D10EffectPool_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D10EffectPool methods ***/
#define ID3D10EffectPool_AsEffect(This) (This)->lpVtbl->AsEffect(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D10EffectPool_QueryInterface(ID3D10EffectPool* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D10EffectPool_AddRef(ID3D10EffectPool* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D10EffectPool_Release(ID3D10EffectPool* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D10EffectPool methods ***/
static inline ID3D10Effect * ID3D10EffectPool_AsEffect(ID3D10EffectPool* This) {
    return This->lpVtbl->AsEffect(This);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectPool_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10EffectPass interface
 */
#ifndef __ID3D10EffectPass_INTERFACE_DEFINED__
#define __ID3D10EffectPass_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10EffectPass, 0x5cfbeb89, 0x1a06, 0x46e0, 0xb2,0x82, 0xe3,0xf9,0xbf,0xa3,0x6a,0x54);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5cfbeb89-1a06-46e0-b282-e3f9bfa36a54")
ID3D10EffectPass
{

    BEGIN_INTERFACE

    virtual WINBOOL STDMETHODCALLTYPE IsValid(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDesc(
        D3D10_PASS_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetVertexShaderDesc(
        D3D10_PASS_SHADER_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGeometryShaderDesc(
        D3D10_PASS_SHADER_DESC *desc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPixelShaderDesc(
        D3D10_PASS_SHADER_DESC *desc) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetAnnotationByIndex(
        UINT index) = 0;

    virtual ID3D10EffectVariable * STDMETHODCALLTYPE GetAnnotationByName(
        const char *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE Apply(
        UINT flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE ComputeStateBlockMask(
        D3D10_STATE_BLOCK_MASK *mask) = 0;

    END_INTERFACE

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10EffectPass, 0x5cfbeb89, 0x1a06, 0x46e0, 0xb2,0x82, 0xe3,0xf9,0xbf,0xa3,0x6a,0x54)
#endif
#else
typedef struct ID3D10EffectPassVtbl {
    BEGIN_INTERFACE

    /*** ID3D10EffectPass methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsValid)(
        ID3D10EffectPass *This);

    HRESULT (STDMETHODCALLTYPE *GetDesc)(
        ID3D10EffectPass *This,
        D3D10_PASS_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetVertexShaderDesc)(
        ID3D10EffectPass *This,
        D3D10_PASS_SHADER_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetGeometryShaderDesc)(
        ID3D10EffectPass *This,
        D3D10_PASS_SHADER_DESC *desc);

    HRESULT (STDMETHODCALLTYPE *GetPixelShaderDesc)(
        ID3D10EffectPass *This,
        D3D10_PASS_SHADER_DESC *desc);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByIndex)(
        ID3D10EffectPass *This,
        UINT index);

    ID3D10EffectVariable * (STDMETHODCALLTYPE *GetAnnotationByName)(
        ID3D10EffectPass *This,
        const char *name);

    HRESULT (STDMETHODCALLTYPE *Apply)(
        ID3D10EffectPass *This,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *ComputeStateBlockMask)(
        ID3D10EffectPass *This,
        D3D10_STATE_BLOCK_MASK *mask);

    END_INTERFACE
} ID3D10EffectPassVtbl;

interface ID3D10EffectPass {
    CONST_VTBL ID3D10EffectPassVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** ID3D10EffectPass methods ***/
#define ID3D10EffectPass_IsValid(This) (This)->lpVtbl->IsValid(This)
#define ID3D10EffectPass_GetDesc(This,desc) (This)->lpVtbl->GetDesc(This,desc)
#define ID3D10EffectPass_GetVertexShaderDesc(This,desc) (This)->lpVtbl->GetVertexShaderDesc(This,desc)
#define ID3D10EffectPass_GetGeometryShaderDesc(This,desc) (This)->lpVtbl->GetGeometryShaderDesc(This,desc)
#define ID3D10EffectPass_GetPixelShaderDesc(This,desc) (This)->lpVtbl->GetPixelShaderDesc(This,desc)
#define ID3D10EffectPass_GetAnnotationByIndex(This,index) (This)->lpVtbl->GetAnnotationByIndex(This,index)
#define ID3D10EffectPass_GetAnnotationByName(This,name) (This)->lpVtbl->GetAnnotationByName(This,name)
#define ID3D10EffectPass_Apply(This,flags) (This)->lpVtbl->Apply(This,flags)
#define ID3D10EffectPass_ComputeStateBlockMask(This,mask) (This)->lpVtbl->ComputeStateBlockMask(This,mask)
#else
/*** ID3D10EffectPass methods ***/
static inline WINBOOL ID3D10EffectPass_IsValid(ID3D10EffectPass* This) {
    return This->lpVtbl->IsValid(This);
}
static inline HRESULT ID3D10EffectPass_GetDesc(ID3D10EffectPass* This,D3D10_PASS_DESC *desc) {
    return This->lpVtbl->GetDesc(This,desc);
}
static inline HRESULT ID3D10EffectPass_GetVertexShaderDesc(ID3D10EffectPass* This,D3D10_PASS_SHADER_DESC *desc) {
    return This->lpVtbl->GetVertexShaderDesc(This,desc);
}
static inline HRESULT ID3D10EffectPass_GetGeometryShaderDesc(ID3D10EffectPass* This,D3D10_PASS_SHADER_DESC *desc) {
    return This->lpVtbl->GetGeometryShaderDesc(This,desc);
}
static inline HRESULT ID3D10EffectPass_GetPixelShaderDesc(ID3D10EffectPass* This,D3D10_PASS_SHADER_DESC *desc) {
    return This->lpVtbl->GetPixelShaderDesc(This,desc);
}
static inline ID3D10EffectVariable * ID3D10EffectPass_GetAnnotationByIndex(ID3D10EffectPass* This,UINT index) {
    return This->lpVtbl->GetAnnotationByIndex(This,index);
}
static inline ID3D10EffectVariable * ID3D10EffectPass_GetAnnotationByName(ID3D10EffectPass* This,const char *name) {
    return This->lpVtbl->GetAnnotationByName(This,name);
}
static inline HRESULT ID3D10EffectPass_Apply(ID3D10EffectPass* This,UINT flags) {
    return This->lpVtbl->Apply(This,flags);
}
static inline HRESULT ID3D10EffectPass_ComputeStateBlockMask(ID3D10EffectPass* This,D3D10_STATE_BLOCK_MASK *mask) {
    return This->lpVtbl->ComputeStateBlockMask(This,mask);
}
#endif
#endif

#endif


#endif  /* __ID3D10EffectPass_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D10StateBlock interface
 */
#ifndef __ID3D10StateBlock_INTERFACE_DEFINED__
#define __ID3D10StateBlock_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D10StateBlock, 0x0803425a, 0x57f5, 0x4dd6, 0x94,0x65, 0xa8,0x75,0x70,0x83,0x4a,0x08);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0803425a-57f5-4dd6-9465-a87570834a08")
ID3D10StateBlock : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Capture(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Apply(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE ReleaseAllDeviceObjects(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDevice(
        ID3D10Device **device) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D10StateBlock, 0x0803425a, 0x57f5, 0x4dd6, 0x94,0x65, 0xa8,0x75,0x70,0x83,0x4a,0x08)
#endif
#else
typedef struct ID3D10StateBlockVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D10StateBlock *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D10StateBlock *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D10StateBlock *This);

    /*** ID3D10StateBlock methods ***/
    HRESULT (STDMETHODCALLTYPE *Capture)(
        ID3D10StateBlock *This);

    HRESULT (STDMETHODCALLTYPE *Apply)(
        ID3D10StateBlock *This);

    HRESULT (STDMETHODCALLTYPE *ReleaseAllDeviceObjects)(
        ID3D10StateBlock *This);

    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D10StateBlock *This,
        ID3D10Device **device);

    END_INTERFACE
} ID3D10StateBlockVtbl;

interface ID3D10StateBlock {
    CONST_VTBL ID3D10StateBlockVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D10StateBlock_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D10StateBlock_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D10StateBlock_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D10StateBlock methods ***/
#define ID3D10StateBlock_Capture(This) (This)->lpVtbl->Capture(This)
#define ID3D10StateBlock_Apply(This) (This)->lpVtbl->Apply(This)
#define ID3D10StateBlock_ReleaseAllDeviceObjects(This) (This)->lpVtbl->ReleaseAllDeviceObjects(This)
#define ID3D10StateBlock_GetDevice(This,device) (This)->lpVtbl->GetDevice(This,device)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D10StateBlock_QueryInterface(ID3D10StateBlock* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D10StateBlock_AddRef(ID3D10StateBlock* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D10StateBlock_Release(ID3D10StateBlock* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D10StateBlock methods ***/
static inline HRESULT ID3D10StateBlock_Capture(ID3D10StateBlock* This) {
    return This->lpVtbl->Capture(This);
}
static inline HRESULT ID3D10StateBlock_Apply(ID3D10StateBlock* This) {
    return This->lpVtbl->Apply(This);
}
static inline HRESULT ID3D10StateBlock_ReleaseAllDeviceObjects(ID3D10StateBlock* This) {
    return This->lpVtbl->ReleaseAllDeviceObjects(This);
}
static inline HRESULT ID3D10StateBlock_GetDevice(ID3D10StateBlock* This,ID3D10Device **device) {
    return This->lpVtbl->GetDevice(This,device);
}
#endif
#endif

#endif


#endif  /* __ID3D10StateBlock_INTERFACE_DEFINED__ */

HRESULT __stdcall  D3D10CompileEffectFromMemory(void *data,SIZE_T data_size,const char *filename,const D3D10_SHADER_MACRO *defines,ID3D10Include *include,UINT hlsl_flags,UINT fx_flags,ID3D10Blob **effect,ID3D10Blob **errors);

HRESULT __stdcall  D3D10CreateEffectFromMemory(void *data,SIZE_T data_size,UINT flags,ID3D10Device *device,ID3D10EffectPool *effect_pool,ID3D10Effect **effect);

HRESULT __stdcall  D3D10CreateEffectPoolFromMemory(void *data,SIZE_T data_size,UINT fx_flags,ID3D10Device *device,ID3D10EffectPool **effect_pool);

HRESULT __stdcall  D3D10CreateStateBlock(ID3D10Device *device,D3D10_STATE_BLOCK_MASK *mask,ID3D10StateBlock **stateblock);

HRESULT __stdcall  D3D10StateBlockMaskDifference(D3D10_STATE_BLOCK_MASK *mask_x,D3D10_STATE_BLOCK_MASK *mask_y,D3D10_STATE_BLOCK_MASK *result);

HRESULT __stdcall  D3D10StateBlockMaskDisableAll(D3D10_STATE_BLOCK_MASK *mask);

HRESULT __stdcall  D3D10StateBlockMaskDisableCapture(D3D10_STATE_BLOCK_MASK *mask,D3D10_DEVICE_STATE_TYPES state_type,UINT start_idx,UINT count);

HRESULT __stdcall  D3D10StateBlockMaskEnableAll(D3D10_STATE_BLOCK_MASK *mask);

HRESULT __stdcall  D3D10StateBlockMaskEnableCapture(D3D10_STATE_BLOCK_MASK *mask,D3D10_DEVICE_STATE_TYPES state_type,UINT start_idx,UINT count);

WINBOOL __stdcall  D3D10StateBlockMaskGetSetting(D3D10_STATE_BLOCK_MASK *mask,D3D10_DEVICE_STATE_TYPES state_type,UINT idx);

HRESULT __stdcall  D3D10StateBlockMaskIntersect(D3D10_STATE_BLOCK_MASK *mask_x,D3D10_STATE_BLOCK_MASK *mask_y,D3D10_STATE_BLOCK_MASK *result);

HRESULT __stdcall  D3D10StateBlockMaskUnion(D3D10_STATE_BLOCK_MASK *mask_x,D3D10_STATE_BLOCK_MASK *mask_y,D3D10_STATE_BLOCK_MASK *result);

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __d3d10effect_h__ */
