/*** Autogenerated by WIDL 10.12 from include/wmp.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wmp_h__
#define __wmp_h__

/* Forward declarations */

#ifndef __IWMPPlaylist_FWD_DEFINED__
#define __IWMPPlaylist_FWD_DEFINED__
typedef interface IWMPPlaylist IWMPPlaylist;
#ifdef __cplusplus
interface IWMPPlaylist;
#endif /* __cplusplus */
#endif

#ifndef __IWMPMedia_FWD_DEFINED__
#define __IWMPMedia_FWD_DEFINED__
typedef interface IWMPMedia IWMPMedia;
#ifdef __cplusplus
interface IWMPMedia;
#endif /* __cplusplus */
#endif

#ifndef __IWMPControls_FWD_DEFINED__
#define __IWMPControls_FWD_DEFINED__
typedef interface IWMPControls IWMPControls;
#ifdef __cplusplus
interface IWMPControls;
#endif /* __cplusplus */
#endif

#ifndef __IWMPControls2_FWD_DEFINED__
#define __IWMPControls2_FWD_DEFINED__
typedef interface IWMPControls2 IWMPControls2;
#ifdef __cplusplus
interface IWMPControls2;
#endif /* __cplusplus */
#endif

#ifndef __IWMPRemoteMediaServices_FWD_DEFINED__
#define __IWMPRemoteMediaServices_FWD_DEFINED__
typedef interface IWMPRemoteMediaServices IWMPRemoteMediaServices;
#ifdef __cplusplus
interface IWMPRemoteMediaServices;
#endif /* __cplusplus */
#endif

#ifndef __IWMPSettings_FWD_DEFINED__
#define __IWMPSettings_FWD_DEFINED__
typedef interface IWMPSettings IWMPSettings;
#ifdef __cplusplus
interface IWMPSettings;
#endif /* __cplusplus */
#endif

#ifndef __IWMPStringCollection_FWD_DEFINED__
#define __IWMPStringCollection_FWD_DEFINED__
typedef interface IWMPStringCollection IWMPStringCollection;
#ifdef __cplusplus
interface IWMPStringCollection;
#endif /* __cplusplus */
#endif

#ifndef __IWMPMediaCollection_FWD_DEFINED__
#define __IWMPMediaCollection_FWD_DEFINED__
typedef interface IWMPMediaCollection IWMPMediaCollection;
#ifdef __cplusplus
interface IWMPMediaCollection;
#endif /* __cplusplus */
#endif

#ifndef __IWMPPlaylistArray_FWD_DEFINED__
#define __IWMPPlaylistArray_FWD_DEFINED__
typedef interface IWMPPlaylistArray IWMPPlaylistArray;
#ifdef __cplusplus
interface IWMPPlaylistArray;
#endif /* __cplusplus */
#endif

#ifndef __IWMPPlaylistCollection_FWD_DEFINED__
#define __IWMPPlaylistCollection_FWD_DEFINED__
typedef interface IWMPPlaylistCollection IWMPPlaylistCollection;
#ifdef __cplusplus
interface IWMPPlaylistCollection;
#endif /* __cplusplus */
#endif

#ifndef __IWMPNetwork_FWD_DEFINED__
#define __IWMPNetwork_FWD_DEFINED__
typedef interface IWMPNetwork IWMPNetwork;
#ifdef __cplusplus
interface IWMPNetwork;
#endif /* __cplusplus */
#endif

#ifndef __IWMPCdrom_FWD_DEFINED__
#define __IWMPCdrom_FWD_DEFINED__
typedef interface IWMPCdrom IWMPCdrom;
#ifdef __cplusplus
interface IWMPCdrom;
#endif /* __cplusplus */
#endif

#ifndef __IWMPCdromCollection_FWD_DEFINED__
#define __IWMPCdromCollection_FWD_DEFINED__
typedef interface IWMPCdromCollection IWMPCdromCollection;
#ifdef __cplusplus
interface IWMPCdromCollection;
#endif /* __cplusplus */
#endif

#ifndef __IWMPDVD_FWD_DEFINED__
#define __IWMPDVD_FWD_DEFINED__
typedef interface IWMPDVD IWMPDVD;
#ifdef __cplusplus
interface IWMPDVD;
#endif /* __cplusplus */
#endif

#ifndef __IWMPClosedCaption_FWD_DEFINED__
#define __IWMPClosedCaption_FWD_DEFINED__
typedef interface IWMPClosedCaption IWMPClosedCaption;
#ifdef __cplusplus
interface IWMPClosedCaption;
#endif /* __cplusplus */
#endif

#ifndef __IWMPErrorItem_FWD_DEFINED__
#define __IWMPErrorItem_FWD_DEFINED__
typedef interface IWMPErrorItem IWMPErrorItem;
#ifdef __cplusplus
interface IWMPErrorItem;
#endif /* __cplusplus */
#endif

#ifndef __IWMPMedia2_FWD_DEFINED__
#define __IWMPMedia2_FWD_DEFINED__
typedef interface IWMPMedia2 IWMPMedia2;
#ifdef __cplusplus
interface IWMPMedia2;
#endif /* __cplusplus */
#endif

#ifndef __IWMPMedia3_FWD_DEFINED__
#define __IWMPMedia3_FWD_DEFINED__
typedef interface IWMPMedia3 IWMPMedia3;
#ifdef __cplusplus
interface IWMPMedia3;
#endif /* __cplusplus */
#endif

#ifndef __IWMPError_FWD_DEFINED__
#define __IWMPError_FWD_DEFINED__
typedef interface IWMPError IWMPError;
#ifdef __cplusplus
interface IWMPError;
#endif /* __cplusplus */
#endif

#ifndef __IWMPPlayerApplication_FWD_DEFINED__
#define __IWMPPlayerApplication_FWD_DEFINED__
typedef interface IWMPPlayerApplication IWMPPlayerApplication;
#ifdef __cplusplus
interface IWMPPlayerApplication;
#endif /* __cplusplus */
#endif

#ifndef __IWMPCore_FWD_DEFINED__
#define __IWMPCore_FWD_DEFINED__
typedef interface IWMPCore IWMPCore;
#ifdef __cplusplus
interface IWMPCore;
#endif /* __cplusplus */
#endif

#ifndef __IWMPCore2_FWD_DEFINED__
#define __IWMPCore2_FWD_DEFINED__
typedef interface IWMPCore2 IWMPCore2;
#ifdef __cplusplus
interface IWMPCore2;
#endif /* __cplusplus */
#endif

#ifndef __IWMPCore3_FWD_DEFINED__
#define __IWMPCore3_FWD_DEFINED__
typedef interface IWMPCore3 IWMPCore3;
#ifdef __cplusplus
interface IWMPCore3;
#endif /* __cplusplus */
#endif

#ifndef __IWMPPlayer4_FWD_DEFINED__
#define __IWMPPlayer4_FWD_DEFINED__
typedef interface IWMPPlayer4 IWMPPlayer4;
#ifdef __cplusplus
interface IWMPPlayer4;
#endif /* __cplusplus */
#endif

#ifndef __IWMPPlayer_FWD_DEFINED__
#define __IWMPPlayer_FWD_DEFINED__
typedef interface IWMPPlayer IWMPPlayer;
#ifdef __cplusplus
interface IWMPPlayer;
#endif /* __cplusplus */
#endif

#ifndef __IWMPSyncDevice_FWD_DEFINED__
#define __IWMPSyncDevice_FWD_DEFINED__
typedef interface IWMPSyncDevice IWMPSyncDevice;
#ifdef __cplusplus
interface IWMPSyncDevice;
#endif /* __cplusplus */
#endif

#ifndef __IWMPSyncDevice2_FWD_DEFINED__
#define __IWMPSyncDevice2_FWD_DEFINED__
typedef interface IWMPSyncDevice2 IWMPSyncDevice2;
#ifdef __cplusplus
interface IWMPSyncDevice2;
#endif /* __cplusplus */
#endif

#ifndef __IWMPCdromRip_FWD_DEFINED__
#define __IWMPCdromRip_FWD_DEFINED__
typedef interface IWMPCdromRip IWMPCdromRip;
#ifdef __cplusplus
interface IWMPCdromRip;
#endif /* __cplusplus */
#endif

#ifndef __IWMPCdromBurn_FWD_DEFINED__
#define __IWMPCdromBurn_FWD_DEFINED__
typedef interface IWMPCdromBurn IWMPCdromBurn;
#ifdef __cplusplus
interface IWMPCdromBurn;
#endif /* __cplusplus */
#endif

#ifndef __IWMPLibrary_FWD_DEFINED__
#define __IWMPLibrary_FWD_DEFINED__
typedef interface IWMPLibrary IWMPLibrary;
#ifdef __cplusplus
interface IWMPLibrary;
#endif /* __cplusplus */
#endif

#ifndef __IWMPEvents_FWD_DEFINED__
#define __IWMPEvents_FWD_DEFINED__
typedef interface IWMPEvents IWMPEvents;
#ifdef __cplusplus
interface IWMPEvents;
#endif /* __cplusplus */
#endif

#ifndef __IWMPEvents2_FWD_DEFINED__
#define __IWMPEvents2_FWD_DEFINED__
typedef interface IWMPEvents2 IWMPEvents2;
#ifdef __cplusplus
interface IWMPEvents2;
#endif /* __cplusplus */
#endif

#ifndef __IWMPEvents3_FWD_DEFINED__
#define __IWMPEvents3_FWD_DEFINED__
typedef interface IWMPEvents3 IWMPEvents3;
#ifdef __cplusplus
interface IWMPEvents3;
#endif /* __cplusplus */
#endif

#ifndef __IWMPEvents4_FWD_DEFINED__
#define __IWMPEvents4_FWD_DEFINED__
typedef interface IWMPEvents4 IWMPEvents4;
#ifdef __cplusplus
interface IWMPEvents4;
#endif /* __cplusplus */
#endif

#ifndef ___WMPOCXEvents_FWD_DEFINED__
#define ___WMPOCXEvents_FWD_DEFINED__
typedef interface _WMPOCXEvents _WMPOCXEvents;
#ifdef __cplusplus
interface _WMPOCXEvents;
#endif /* __cplusplus */
#endif

#ifndef __WindowsMediaPlayer_FWD_DEFINED__
#define __WindowsMediaPlayer_FWD_DEFINED__
#ifdef __cplusplus
typedef class WindowsMediaPlayer WindowsMediaPlayer;
#else
typedef struct WindowsMediaPlayer WindowsMediaPlayer;
#endif /* defined __cplusplus */
#endif /* defined __WindowsMediaPlayer_FWD_DEFINED__ */

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __WMPLib_LIBRARY_DEFINED__
#define __WMPLib_LIBRARY_DEFINED__

DEFINE_GUID(LIBID_WMPLib, 0x6bf52a50, 0x394a, 0x11d3, 0xb1,0x53, 0x00,0xc0,0x4f,0x79,0xfa,0xa6);

typedef enum __WIDL_wmp_generated_name_00000020 {
    wmposUndefined = 0,
    wmposPlaylistChanging = 1,
    wmposPlaylistLocating = 2,
    wmposPlaylistConnecting = 3,
    wmposPlaylistLoading = 4,
    wmposPlaylistOpening = 5,
    wmposPlaylistOpenNoMedia = 6,
    wmposPlaylistChanged = 7,
    wmposMediaChanging = 8,
    wmposMediaLocating = 9,
    wmposMediaConnecting = 10,
    wmposMediaLoading = 11,
    wmposMediaOpening = 12,
    wmposMediaOpen = 13,
    wmposBeginCodecAcquisition = 14,
    wmposEndCodecAcquisition = 15,
    wmposBeginLicenseAcquisition = 16,
    wmposEndLicenseAcquisition = 17,
    wmposBeginIndividualization = 18,
    wmposEndIndividualization = 19,
    wmposMediaWaiting = 20,
    wmposOpeningUnknownURL = 21
} WMPOpenState;
typedef enum __WIDL_wmp_generated_name_00000021 {
    wmppsUndefined = 0,
    wmppsStopped = 1,
    wmppsPaused = 2,
    wmppsPlaying = 3,
    wmppsScanForward = 4,
    wmppsScanReverse = 5,
    wmppsBuffering = 6,
    wmppsWaiting = 7,
    wmppsMediaEnded = 8,
    wmppsTransitioning = 9,
    wmppsReady = 10,
    wmppsReconnecting = 11,
    wmppsLast = 12
} WMPPlayState;
typedef enum __WIDL_wmp_generated_name_00000022 {
    wmplcUnknown = 0,
    wmplcClear = 1,
    wmplcInfoChange = 2,
    wmplcMove = 3,
    wmplcDelete = 4,
    wmplcInsert = 5,
    wmplcAppend = 6,
    wmplcPrivate = 7,
    wmplcNameChange = 8,
    wmplcMorph = 9,
    wmplcSort = 10,
    wmplcLast = 11
} WMPPlaylistChangeEventType;
typedef enum __WIDL_wmp_generated_name_00000023 {
    wmpdsUnknown = 0,
    wmpdsPartnershipExists = 1,
    wmpdsPartnershipDeclined = 2,
    wmpdsPartnershipAnother = 3,
    wmpdsManualDevice = 4,
    wmpdsNewDevice = 5,
    wmpdsLast = 6
} WMPDeviceStatus;
typedef enum __WIDL_wmp_generated_name_00000024 {
    wmpssUnknown = 0,
    wmpssSynchronizing = 1,
    wmpssStopped = 2,
    wmpssEstimating = 3,
    wmpssLast = 4
} WMPSyncState;
typedef enum __WIDL_wmp_generated_name_00000025 {
    wmprsUnknown = 0,
    wmprsRipping = 1,
    wmprsStopped = 2
} WMPRipState;
typedef enum __WIDL_wmp_generated_name_00000026 {
    wmpbfAudioCD = 0,
    wmpbfDataCD = 1
} WMPBurnFormat;
typedef enum __WIDL_wmp_generated_name_00000027 {
    wmpbsUnknown = 0,
    wmpbsBusy = 1,
    wmpbsReady = 2,
    wmpbsWaitingForDisc = 3,
    wmpbsRefreshStatusPending = 4,
    wmpbsPreparingToBurn = 5,
    wmpbsBurning = 6,
    wmpbsStopped = 7,
    wmpbsErasing = 8,
    wmpbsDownloading = 9
} WMPBurnState;
typedef enum __WIDL_wmp_generated_name_00000028 {
    wmpltUnknown = 0,
    wmpltAll = 1,
    wmpltLocal = 2,
    wmpltRemote = 3,
    wmpltDisc = 4,
    wmpltPortableDevice = 5
} WMPLibraryType;
typedef enum __WIDL_wmp_generated_name_00000029 {
    wmpfssUnknown = 0,
    wmpfssScanning = 1,
    wmpfssUpdating = 2,
    wmpfssStopped = 3
} WMPFolderScanState;
typedef enum __WIDL_wmp_generated_name_0000002A {
    wmpsccetUnknown = 0,
    wmpsccetInsert = 1,
    wmpsccetChange = 2,
    wmpsccetDelete = 3,
    wmpsccetClear = 4,
    wmpsccetBeginUpdates = 5,
    wmpsccetEndUpdates = 6
} WMPStringCollectionChangeEventType;
#ifndef __IWMPMedia_FWD_DEFINED__
#define __IWMPMedia_FWD_DEFINED__
typedef interface IWMPMedia IWMPMedia;
#ifdef __cplusplus
interface IWMPMedia;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IWMPPlaylist interface
 */
#ifndef __IWMPPlaylist_INTERFACE_DEFINED__
#define __IWMPPlaylist_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPPlaylist, 0xd5f0f4f1, 0x130c, 0x11d3, 0xb1,0x4e, 0x00,0xc0,0x4f,0x79,0xfa,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d5f0f4f1-130c-11d3-b14e-00c04f79faa6")
IWMPPlaylist : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_count(
        LONG *plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_name(
        BSTR *pbstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_name(
        BSTR pbstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_attributeCount(
        LONG *plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_attributeName(
        LONG lIndex,
        BSTR *pbstrAttributeName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG lIndex,
        IWMPMedia **ppIWMPMedia) = 0;

    virtual HRESULT STDMETHODCALLTYPE getItemInfo(
        BSTR bstrName,
        BSTR *pbstrVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE setItemInfo(
        BSTR bstrName,
        BSTR bstrValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_isIdentical(
        IWMPPlaylist *pIWMPPlaylist,
        VARIANT_BOOL *pvbool) = 0;

    virtual HRESULT STDMETHODCALLTYPE clear(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE insertItem(
        LONG lIndex,
        IWMPMedia *pIWMPMedia) = 0;

    virtual HRESULT STDMETHODCALLTYPE appendItem(
        IWMPMedia *pIWMPMedia) = 0;

    virtual HRESULT STDMETHODCALLTYPE removeItem(
        IWMPMedia *pIWMPMedia) = 0;

    virtual HRESULT STDMETHODCALLTYPE moveItem(
        LONG lIndexOld,
        LONG lIndexNew) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPPlaylist, 0xd5f0f4f1, 0x130c, 0x11d3, 0xb1,0x4e, 0x00,0xc0,0x4f,0x79,0xfa,0xa6)
#endif
#else
typedef struct IWMPPlaylistVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPPlaylist *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPPlaylist *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPPlaylist *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPPlaylist *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPPlaylist *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPPlaylist *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPPlaylist *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPPlaylist methods ***/
    HRESULT (STDMETHODCALLTYPE *get_count)(
        IWMPPlaylist *This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *get_name)(
        IWMPPlaylist *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *put_name)(
        IWMPPlaylist *This,
        BSTR pbstrName);

    HRESULT (STDMETHODCALLTYPE *get_attributeCount)(
        IWMPPlaylist *This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *get_attributeName)(
        IWMPPlaylist *This,
        LONG lIndex,
        BSTR *pbstrAttributeName);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IWMPPlaylist *This,
        LONG lIndex,
        IWMPMedia **ppIWMPMedia);

    HRESULT (STDMETHODCALLTYPE *getItemInfo)(
        IWMPPlaylist *This,
        BSTR bstrName,
        BSTR *pbstrVal);

    HRESULT (STDMETHODCALLTYPE *setItemInfo)(
        IWMPPlaylist *This,
        BSTR bstrName,
        BSTR bstrValue);

    HRESULT (STDMETHODCALLTYPE *get_isIdentical)(
        IWMPPlaylist *This,
        IWMPPlaylist *pIWMPPlaylist,
        VARIANT_BOOL *pvbool);

    HRESULT (STDMETHODCALLTYPE *clear)(
        IWMPPlaylist *This);

    HRESULT (STDMETHODCALLTYPE *insertItem)(
        IWMPPlaylist *This,
        LONG lIndex,
        IWMPMedia *pIWMPMedia);

    HRESULT (STDMETHODCALLTYPE *appendItem)(
        IWMPPlaylist *This,
        IWMPMedia *pIWMPMedia);

    HRESULT (STDMETHODCALLTYPE *removeItem)(
        IWMPPlaylist *This,
        IWMPMedia *pIWMPMedia);

    HRESULT (STDMETHODCALLTYPE *moveItem)(
        IWMPPlaylist *This,
        LONG lIndexOld,
        LONG lIndexNew);

    END_INTERFACE
} IWMPPlaylistVtbl;

interface IWMPPlaylist {
    CONST_VTBL IWMPPlaylistVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPPlaylist_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPPlaylist_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPPlaylist_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPPlaylist_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPPlaylist_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPPlaylist_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPPlaylist_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPPlaylist methods ***/
#define IWMPPlaylist_get_count(This,plCount) (This)->lpVtbl->get_count(This,plCount)
#define IWMPPlaylist_get_name(This,pbstrName) (This)->lpVtbl->get_name(This,pbstrName)
#define IWMPPlaylist_put_name(This,pbstrName) (This)->lpVtbl->put_name(This,pbstrName)
#define IWMPPlaylist_get_attributeCount(This,plCount) (This)->lpVtbl->get_attributeCount(This,plCount)
#define IWMPPlaylist_get_attributeName(This,lIndex,pbstrAttributeName) (This)->lpVtbl->get_attributeName(This,lIndex,pbstrAttributeName)
#define IWMPPlaylist_get_Item(This,lIndex,ppIWMPMedia) (This)->lpVtbl->get_Item(This,lIndex,ppIWMPMedia)
#define IWMPPlaylist_getItemInfo(This,bstrName,pbstrVal) (This)->lpVtbl->getItemInfo(This,bstrName,pbstrVal)
#define IWMPPlaylist_setItemInfo(This,bstrName,bstrValue) (This)->lpVtbl->setItemInfo(This,bstrName,bstrValue)
#define IWMPPlaylist_get_isIdentical(This,pIWMPPlaylist,pvbool) (This)->lpVtbl->get_isIdentical(This,pIWMPPlaylist,pvbool)
#define IWMPPlaylist_clear(This) (This)->lpVtbl->clear(This)
#define IWMPPlaylist_insertItem(This,lIndex,pIWMPMedia) (This)->lpVtbl->insertItem(This,lIndex,pIWMPMedia)
#define IWMPPlaylist_appendItem(This,pIWMPMedia) (This)->lpVtbl->appendItem(This,pIWMPMedia)
#define IWMPPlaylist_removeItem(This,pIWMPMedia) (This)->lpVtbl->removeItem(This,pIWMPMedia)
#define IWMPPlaylist_moveItem(This,lIndexOld,lIndexNew) (This)->lpVtbl->moveItem(This,lIndexOld,lIndexNew)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPPlaylist_QueryInterface(IWMPPlaylist* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPPlaylist_AddRef(IWMPPlaylist* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPPlaylist_Release(IWMPPlaylist* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPPlaylist_GetTypeInfoCount(IWMPPlaylist* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPPlaylist_GetTypeInfo(IWMPPlaylist* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPPlaylist_GetIDsOfNames(IWMPPlaylist* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPPlaylist_Invoke(IWMPPlaylist* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPPlaylist methods ***/
static inline HRESULT IWMPPlaylist_get_count(IWMPPlaylist* This,LONG *plCount) {
    return This->lpVtbl->get_count(This,plCount);
}
static inline HRESULT IWMPPlaylist_get_name(IWMPPlaylist* This,BSTR *pbstrName) {
    return This->lpVtbl->get_name(This,pbstrName);
}
static inline HRESULT IWMPPlaylist_put_name(IWMPPlaylist* This,BSTR pbstrName) {
    return This->lpVtbl->put_name(This,pbstrName);
}
static inline HRESULT IWMPPlaylist_get_attributeCount(IWMPPlaylist* This,LONG *plCount) {
    return This->lpVtbl->get_attributeCount(This,plCount);
}
static inline HRESULT IWMPPlaylist_get_attributeName(IWMPPlaylist* This,LONG lIndex,BSTR *pbstrAttributeName) {
    return This->lpVtbl->get_attributeName(This,lIndex,pbstrAttributeName);
}
static inline HRESULT IWMPPlaylist_get_Item(IWMPPlaylist* This,LONG lIndex,IWMPMedia **ppIWMPMedia) {
    return This->lpVtbl->get_Item(This,lIndex,ppIWMPMedia);
}
static inline HRESULT IWMPPlaylist_getItemInfo(IWMPPlaylist* This,BSTR bstrName,BSTR *pbstrVal) {
    return This->lpVtbl->getItemInfo(This,bstrName,pbstrVal);
}
static inline HRESULT IWMPPlaylist_setItemInfo(IWMPPlaylist* This,BSTR bstrName,BSTR bstrValue) {
    return This->lpVtbl->setItemInfo(This,bstrName,bstrValue);
}
static inline HRESULT IWMPPlaylist_get_isIdentical(IWMPPlaylist* This,IWMPPlaylist *pIWMPPlaylist,VARIANT_BOOL *pvbool) {
    return This->lpVtbl->get_isIdentical(This,pIWMPPlaylist,pvbool);
}
static inline HRESULT IWMPPlaylist_clear(IWMPPlaylist* This) {
    return This->lpVtbl->clear(This);
}
static inline HRESULT IWMPPlaylist_insertItem(IWMPPlaylist* This,LONG lIndex,IWMPMedia *pIWMPMedia) {
    return This->lpVtbl->insertItem(This,lIndex,pIWMPMedia);
}
static inline HRESULT IWMPPlaylist_appendItem(IWMPPlaylist* This,IWMPMedia *pIWMPMedia) {
    return This->lpVtbl->appendItem(This,pIWMPMedia);
}
static inline HRESULT IWMPPlaylist_removeItem(IWMPPlaylist* This,IWMPMedia *pIWMPMedia) {
    return This->lpVtbl->removeItem(This,pIWMPMedia);
}
static inline HRESULT IWMPPlaylist_moveItem(IWMPPlaylist* This,LONG lIndexOld,LONG lIndexNew) {
    return This->lpVtbl->moveItem(This,lIndexOld,lIndexNew);
}
#endif
#endif

#endif


#endif  /* __IWMPPlaylist_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPMedia interface
 */
#ifndef __IWMPMedia_INTERFACE_DEFINED__
#define __IWMPMedia_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPMedia, 0x94d55e95, 0x3fac, 0x11d3, 0xb1,0x55, 0x00,0xc0,0x4f,0x79,0xfa,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("94d55e95-3fac-11d3-b155-00c04f79faa6")
IWMPMedia : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_isIdentical(
        IWMPMedia *pIWMPMedia,
        VARIANT_BOOL *pvbool) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_sourceURL(
        BSTR *pbstrSourceURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_name(
        BSTR *pbstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_name(
        BSTR pbstrName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_imageSourceWidth(
        LONG *pWidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_imageSourceHeight(
        LONG *pHeight) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_markerCount(
        LONG *pMarkerCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE getMarkerTime(
        LONG MarkerNum,
        double *pMarkerTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE getMarkerName(
        LONG MarkerNum,
        BSTR *pbstrMarkerName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_duration(
        double *pDuration) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_durationString(
        BSTR *pbstrDuration) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_attributeCount(
        LONG *plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE getAttributeName(
        LONG lIndex,
        BSTR *pbstrItemName) = 0;

    virtual HRESULT STDMETHODCALLTYPE getItemInfo(
        BSTR bstrItemName,
        BSTR *pbstrVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE setItemInfo(
        BSTR bstrItemName,
        BSTR bstrVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE getItemInfoByAtom(
        LONG lAtom,
        BSTR *pbstrVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE isMemberOf(
        IWMPPlaylist *pPlaylist,
        VARIANT_BOOL *pvarfIsMemberOf) = 0;

    virtual HRESULT STDMETHODCALLTYPE isReadOnlyItem(
        BSTR bstrItemName,
        VARIANT_BOOL *pvarfIsReadOnly) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPMedia, 0x94d55e95, 0x3fac, 0x11d3, 0xb1,0x55, 0x00,0xc0,0x4f,0x79,0xfa,0xa6)
#endif
#else
typedef struct IWMPMediaVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPMedia *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPMedia *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPMedia *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPMedia *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPMedia *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPMedia *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPMedia *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPMedia methods ***/
    HRESULT (STDMETHODCALLTYPE *get_isIdentical)(
        IWMPMedia *This,
        IWMPMedia *pIWMPMedia,
        VARIANT_BOOL *pvbool);

    HRESULT (STDMETHODCALLTYPE *get_sourceURL)(
        IWMPMedia *This,
        BSTR *pbstrSourceURL);

    HRESULT (STDMETHODCALLTYPE *get_name)(
        IWMPMedia *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *put_name)(
        IWMPMedia *This,
        BSTR pbstrName);

    HRESULT (STDMETHODCALLTYPE *get_imageSourceWidth)(
        IWMPMedia *This,
        LONG *pWidth);

    HRESULT (STDMETHODCALLTYPE *get_imageSourceHeight)(
        IWMPMedia *This,
        LONG *pHeight);

    HRESULT (STDMETHODCALLTYPE *get_markerCount)(
        IWMPMedia *This,
        LONG *pMarkerCount);

    HRESULT (STDMETHODCALLTYPE *getMarkerTime)(
        IWMPMedia *This,
        LONG MarkerNum,
        double *pMarkerTime);

    HRESULT (STDMETHODCALLTYPE *getMarkerName)(
        IWMPMedia *This,
        LONG MarkerNum,
        BSTR *pbstrMarkerName);

    HRESULT (STDMETHODCALLTYPE *get_duration)(
        IWMPMedia *This,
        double *pDuration);

    HRESULT (STDMETHODCALLTYPE *get_durationString)(
        IWMPMedia *This,
        BSTR *pbstrDuration);

    HRESULT (STDMETHODCALLTYPE *get_attributeCount)(
        IWMPMedia *This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *getAttributeName)(
        IWMPMedia *This,
        LONG lIndex,
        BSTR *pbstrItemName);

    HRESULT (STDMETHODCALLTYPE *getItemInfo)(
        IWMPMedia *This,
        BSTR bstrItemName,
        BSTR *pbstrVal);

    HRESULT (STDMETHODCALLTYPE *setItemInfo)(
        IWMPMedia *This,
        BSTR bstrItemName,
        BSTR bstrVal);

    HRESULT (STDMETHODCALLTYPE *getItemInfoByAtom)(
        IWMPMedia *This,
        LONG lAtom,
        BSTR *pbstrVal);

    HRESULT (STDMETHODCALLTYPE *isMemberOf)(
        IWMPMedia *This,
        IWMPPlaylist *pPlaylist,
        VARIANT_BOOL *pvarfIsMemberOf);

    HRESULT (STDMETHODCALLTYPE *isReadOnlyItem)(
        IWMPMedia *This,
        BSTR bstrItemName,
        VARIANT_BOOL *pvarfIsReadOnly);

    END_INTERFACE
} IWMPMediaVtbl;

interface IWMPMedia {
    CONST_VTBL IWMPMediaVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPMedia_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPMedia_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPMedia_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPMedia_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPMedia_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPMedia_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPMedia_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPMedia methods ***/
#define IWMPMedia_get_isIdentical(This,pIWMPMedia,pvbool) (This)->lpVtbl->get_isIdentical(This,pIWMPMedia,pvbool)
#define IWMPMedia_get_sourceURL(This,pbstrSourceURL) (This)->lpVtbl->get_sourceURL(This,pbstrSourceURL)
#define IWMPMedia_get_name(This,pbstrName) (This)->lpVtbl->get_name(This,pbstrName)
#define IWMPMedia_put_name(This,pbstrName) (This)->lpVtbl->put_name(This,pbstrName)
#define IWMPMedia_get_imageSourceWidth(This,pWidth) (This)->lpVtbl->get_imageSourceWidth(This,pWidth)
#define IWMPMedia_get_imageSourceHeight(This,pHeight) (This)->lpVtbl->get_imageSourceHeight(This,pHeight)
#define IWMPMedia_get_markerCount(This,pMarkerCount) (This)->lpVtbl->get_markerCount(This,pMarkerCount)
#define IWMPMedia_getMarkerTime(This,MarkerNum,pMarkerTime) (This)->lpVtbl->getMarkerTime(This,MarkerNum,pMarkerTime)
#define IWMPMedia_getMarkerName(This,MarkerNum,pbstrMarkerName) (This)->lpVtbl->getMarkerName(This,MarkerNum,pbstrMarkerName)
#define IWMPMedia_get_duration(This,pDuration) (This)->lpVtbl->get_duration(This,pDuration)
#define IWMPMedia_get_durationString(This,pbstrDuration) (This)->lpVtbl->get_durationString(This,pbstrDuration)
#define IWMPMedia_get_attributeCount(This,plCount) (This)->lpVtbl->get_attributeCount(This,plCount)
#define IWMPMedia_getAttributeName(This,lIndex,pbstrItemName) (This)->lpVtbl->getAttributeName(This,lIndex,pbstrItemName)
#define IWMPMedia_getItemInfo(This,bstrItemName,pbstrVal) (This)->lpVtbl->getItemInfo(This,bstrItemName,pbstrVal)
#define IWMPMedia_setItemInfo(This,bstrItemName,bstrVal) (This)->lpVtbl->setItemInfo(This,bstrItemName,bstrVal)
#define IWMPMedia_getItemInfoByAtom(This,lAtom,pbstrVal) (This)->lpVtbl->getItemInfoByAtom(This,lAtom,pbstrVal)
#define IWMPMedia_isMemberOf(This,pPlaylist,pvarfIsMemberOf) (This)->lpVtbl->isMemberOf(This,pPlaylist,pvarfIsMemberOf)
#define IWMPMedia_isReadOnlyItem(This,bstrItemName,pvarfIsReadOnly) (This)->lpVtbl->isReadOnlyItem(This,bstrItemName,pvarfIsReadOnly)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPMedia_QueryInterface(IWMPMedia* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPMedia_AddRef(IWMPMedia* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPMedia_Release(IWMPMedia* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPMedia_GetTypeInfoCount(IWMPMedia* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPMedia_GetTypeInfo(IWMPMedia* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPMedia_GetIDsOfNames(IWMPMedia* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPMedia_Invoke(IWMPMedia* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPMedia methods ***/
static inline HRESULT IWMPMedia_get_isIdentical(IWMPMedia* This,IWMPMedia *pIWMPMedia,VARIANT_BOOL *pvbool) {
    return This->lpVtbl->get_isIdentical(This,pIWMPMedia,pvbool);
}
static inline HRESULT IWMPMedia_get_sourceURL(IWMPMedia* This,BSTR *pbstrSourceURL) {
    return This->lpVtbl->get_sourceURL(This,pbstrSourceURL);
}
static inline HRESULT IWMPMedia_get_name(IWMPMedia* This,BSTR *pbstrName) {
    return This->lpVtbl->get_name(This,pbstrName);
}
static inline HRESULT IWMPMedia_put_name(IWMPMedia* This,BSTR pbstrName) {
    return This->lpVtbl->put_name(This,pbstrName);
}
static inline HRESULT IWMPMedia_get_imageSourceWidth(IWMPMedia* This,LONG *pWidth) {
    return This->lpVtbl->get_imageSourceWidth(This,pWidth);
}
static inline HRESULT IWMPMedia_get_imageSourceHeight(IWMPMedia* This,LONG *pHeight) {
    return This->lpVtbl->get_imageSourceHeight(This,pHeight);
}
static inline HRESULT IWMPMedia_get_markerCount(IWMPMedia* This,LONG *pMarkerCount) {
    return This->lpVtbl->get_markerCount(This,pMarkerCount);
}
static inline HRESULT IWMPMedia_getMarkerTime(IWMPMedia* This,LONG MarkerNum,double *pMarkerTime) {
    return This->lpVtbl->getMarkerTime(This,MarkerNum,pMarkerTime);
}
static inline HRESULT IWMPMedia_getMarkerName(IWMPMedia* This,LONG MarkerNum,BSTR *pbstrMarkerName) {
    return This->lpVtbl->getMarkerName(This,MarkerNum,pbstrMarkerName);
}
static inline HRESULT IWMPMedia_get_duration(IWMPMedia* This,double *pDuration) {
    return This->lpVtbl->get_duration(This,pDuration);
}
static inline HRESULT IWMPMedia_get_durationString(IWMPMedia* This,BSTR *pbstrDuration) {
    return This->lpVtbl->get_durationString(This,pbstrDuration);
}
static inline HRESULT IWMPMedia_get_attributeCount(IWMPMedia* This,LONG *plCount) {
    return This->lpVtbl->get_attributeCount(This,plCount);
}
static inline HRESULT IWMPMedia_getAttributeName(IWMPMedia* This,LONG lIndex,BSTR *pbstrItemName) {
    return This->lpVtbl->getAttributeName(This,lIndex,pbstrItemName);
}
static inline HRESULT IWMPMedia_getItemInfo(IWMPMedia* This,BSTR bstrItemName,BSTR *pbstrVal) {
    return This->lpVtbl->getItemInfo(This,bstrItemName,pbstrVal);
}
static inline HRESULT IWMPMedia_setItemInfo(IWMPMedia* This,BSTR bstrItemName,BSTR bstrVal) {
    return This->lpVtbl->setItemInfo(This,bstrItemName,bstrVal);
}
static inline HRESULT IWMPMedia_getItemInfoByAtom(IWMPMedia* This,LONG lAtom,BSTR *pbstrVal) {
    return This->lpVtbl->getItemInfoByAtom(This,lAtom,pbstrVal);
}
static inline HRESULT IWMPMedia_isMemberOf(IWMPMedia* This,IWMPPlaylist *pPlaylist,VARIANT_BOOL *pvarfIsMemberOf) {
    return This->lpVtbl->isMemberOf(This,pPlaylist,pvarfIsMemberOf);
}
static inline HRESULT IWMPMedia_isReadOnlyItem(IWMPMedia* This,BSTR bstrItemName,VARIANT_BOOL *pvarfIsReadOnly) {
    return This->lpVtbl->isReadOnlyItem(This,bstrItemName,pvarfIsReadOnly);
}
#endif
#endif

#endif


#endif  /* __IWMPMedia_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPControls interface
 */
#ifndef __IWMPControls_INTERFACE_DEFINED__
#define __IWMPControls_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPControls, 0x74c09e02, 0xf828, 0x11d2, 0xa7,0x4b, 0x00,0xa0,0xc9,0x05,0xf3,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("74c09e02-f828-11d2-a74b-00a0c905f36e")
IWMPControls : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_isAvailable(
        BSTR bstrItem,
        VARIANT_BOOL *pIsAvailable) = 0;

    virtual HRESULT STDMETHODCALLTYPE play(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE pause(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE fastForward(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE fastReverse(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_currentPosition(
        double *pdCurrentPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_currentPosition(
        double pdCurrentPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_currentPositionString(
        BSTR *pbstrCurrentPosition) = 0;

    virtual HRESULT STDMETHODCALLTYPE next(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE previous(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE currentItem(
        IWMPMedia **ppIWMPMedia) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_currentItem(
        IWMPMedia *ppIWMPMedia) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_currentMarker(
        LONG *plMarker) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_currentMarker(
        LONG plMarker) = 0;

    virtual HRESULT STDMETHODCALLTYPE playItem(
        IWMPMedia *pIWMPMedia) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPControls, 0x74c09e02, 0xf828, 0x11d2, 0xa7,0x4b, 0x00,0xa0,0xc9,0x05,0xf3,0x6e)
#endif
#else
typedef struct IWMPControlsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPControls *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPControls *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPControls *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPControls *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPControls *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPControls *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPControls *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPControls methods ***/
    HRESULT (STDMETHODCALLTYPE *get_isAvailable)(
        IWMPControls *This,
        BSTR bstrItem,
        VARIANT_BOOL *pIsAvailable);

    HRESULT (STDMETHODCALLTYPE *play)(
        IWMPControls *This);

    HRESULT (STDMETHODCALLTYPE *stop)(
        IWMPControls *This);

    HRESULT (STDMETHODCALLTYPE *pause)(
        IWMPControls *This);

    HRESULT (STDMETHODCALLTYPE *fastForward)(
        IWMPControls *This);

    HRESULT (STDMETHODCALLTYPE *fastReverse)(
        IWMPControls *This);

    HRESULT (STDMETHODCALLTYPE *get_currentPosition)(
        IWMPControls *This,
        double *pdCurrentPosition);

    HRESULT (STDMETHODCALLTYPE *put_currentPosition)(
        IWMPControls *This,
        double pdCurrentPosition);

    HRESULT (STDMETHODCALLTYPE *get_currentPositionString)(
        IWMPControls *This,
        BSTR *pbstrCurrentPosition);

    HRESULT (STDMETHODCALLTYPE *next)(
        IWMPControls *This);

    HRESULT (STDMETHODCALLTYPE *previous)(
        IWMPControls *This);

    HRESULT (STDMETHODCALLTYPE *currentItem)(
        IWMPControls *This,
        IWMPMedia **ppIWMPMedia);

    HRESULT (STDMETHODCALLTYPE *put_currentItem)(
        IWMPControls *This,
        IWMPMedia *ppIWMPMedia);

    HRESULT (STDMETHODCALLTYPE *get_currentMarker)(
        IWMPControls *This,
        LONG *plMarker);

    HRESULT (STDMETHODCALLTYPE *put_currentMarker)(
        IWMPControls *This,
        LONG plMarker);

    HRESULT (STDMETHODCALLTYPE *playItem)(
        IWMPControls *This,
        IWMPMedia *pIWMPMedia);

    END_INTERFACE
} IWMPControlsVtbl;

interface IWMPControls {
    CONST_VTBL IWMPControlsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPControls_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPControls_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPControls_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPControls_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPControls_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPControls_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPControls_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPControls methods ***/
#define IWMPControls_get_isAvailable(This,bstrItem,pIsAvailable) (This)->lpVtbl->get_isAvailable(This,bstrItem,pIsAvailable)
#define IWMPControls_play(This) (This)->lpVtbl->play(This)
#define IWMPControls_stop(This) (This)->lpVtbl->stop(This)
#define IWMPControls_pause(This) (This)->lpVtbl->pause(This)
#define IWMPControls_fastForward(This) (This)->lpVtbl->fastForward(This)
#define IWMPControls_fastReverse(This) (This)->lpVtbl->fastReverse(This)
#define IWMPControls_get_currentPosition(This,pdCurrentPosition) (This)->lpVtbl->get_currentPosition(This,pdCurrentPosition)
#define IWMPControls_put_currentPosition(This,pdCurrentPosition) (This)->lpVtbl->put_currentPosition(This,pdCurrentPosition)
#define IWMPControls_get_currentPositionString(This,pbstrCurrentPosition) (This)->lpVtbl->get_currentPositionString(This,pbstrCurrentPosition)
#define IWMPControls_next(This) (This)->lpVtbl->next(This)
#define IWMPControls_previous(This) (This)->lpVtbl->previous(This)
#define IWMPControls_currentItem(This,ppIWMPMedia) (This)->lpVtbl->currentItem(This,ppIWMPMedia)
#define IWMPControls_put_currentItem(This,ppIWMPMedia) (This)->lpVtbl->put_currentItem(This,ppIWMPMedia)
#define IWMPControls_get_currentMarker(This,plMarker) (This)->lpVtbl->get_currentMarker(This,plMarker)
#define IWMPControls_put_currentMarker(This,plMarker) (This)->lpVtbl->put_currentMarker(This,plMarker)
#define IWMPControls_playItem(This,pIWMPMedia) (This)->lpVtbl->playItem(This,pIWMPMedia)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPControls_QueryInterface(IWMPControls* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPControls_AddRef(IWMPControls* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPControls_Release(IWMPControls* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPControls_GetTypeInfoCount(IWMPControls* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPControls_GetTypeInfo(IWMPControls* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPControls_GetIDsOfNames(IWMPControls* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPControls_Invoke(IWMPControls* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPControls methods ***/
static inline HRESULT IWMPControls_get_isAvailable(IWMPControls* This,BSTR bstrItem,VARIANT_BOOL *pIsAvailable) {
    return This->lpVtbl->get_isAvailable(This,bstrItem,pIsAvailable);
}
static inline HRESULT IWMPControls_play(IWMPControls* This) {
    return This->lpVtbl->play(This);
}
static inline HRESULT IWMPControls_stop(IWMPControls* This) {
    return This->lpVtbl->stop(This);
}
static inline HRESULT IWMPControls_pause(IWMPControls* This) {
    return This->lpVtbl->pause(This);
}
static inline HRESULT IWMPControls_fastForward(IWMPControls* This) {
    return This->lpVtbl->fastForward(This);
}
static inline HRESULT IWMPControls_fastReverse(IWMPControls* This) {
    return This->lpVtbl->fastReverse(This);
}
static inline HRESULT IWMPControls_get_currentPosition(IWMPControls* This,double *pdCurrentPosition) {
    return This->lpVtbl->get_currentPosition(This,pdCurrentPosition);
}
static inline HRESULT IWMPControls_put_currentPosition(IWMPControls* This,double pdCurrentPosition) {
    return This->lpVtbl->put_currentPosition(This,pdCurrentPosition);
}
static inline HRESULT IWMPControls_get_currentPositionString(IWMPControls* This,BSTR *pbstrCurrentPosition) {
    return This->lpVtbl->get_currentPositionString(This,pbstrCurrentPosition);
}
static inline HRESULT IWMPControls_next(IWMPControls* This) {
    return This->lpVtbl->next(This);
}
static inline HRESULT IWMPControls_previous(IWMPControls* This) {
    return This->lpVtbl->previous(This);
}
static inline HRESULT IWMPControls_currentItem(IWMPControls* This,IWMPMedia **ppIWMPMedia) {
    return This->lpVtbl->currentItem(This,ppIWMPMedia);
}
static inline HRESULT IWMPControls_put_currentItem(IWMPControls* This,IWMPMedia *ppIWMPMedia) {
    return This->lpVtbl->put_currentItem(This,ppIWMPMedia);
}
static inline HRESULT IWMPControls_get_currentMarker(IWMPControls* This,LONG *plMarker) {
    return This->lpVtbl->get_currentMarker(This,plMarker);
}
static inline HRESULT IWMPControls_put_currentMarker(IWMPControls* This,LONG plMarker) {
    return This->lpVtbl->put_currentMarker(This,plMarker);
}
static inline HRESULT IWMPControls_playItem(IWMPControls* This,IWMPMedia *pIWMPMedia) {
    return This->lpVtbl->playItem(This,pIWMPMedia);
}
#endif
#endif

#endif


#endif  /* __IWMPControls_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPControls2 interface
 */
#ifndef __IWMPControls2_INTERFACE_DEFINED__
#define __IWMPControls2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPControls2, 0x6f030d25, 0x0890, 0x480f, 0x97,0x75, 0x1f,0x7e,0x40,0xab,0x5b,0x8e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6f030d25-0890-480f-9775-1f7e40ab5b8e")
IWMPControls2 : public IWMPControls
{
    virtual HRESULT STDMETHODCALLTYPE step(
        LONG lStep) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPControls2, 0x6f030d25, 0x0890, 0x480f, 0x97,0x75, 0x1f,0x7e,0x40,0xab,0x5b,0x8e)
#endif
#else
typedef struct IWMPControls2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPControls2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPControls2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPControls2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPControls2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPControls2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPControls2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPControls2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPControls methods ***/
    HRESULT (STDMETHODCALLTYPE *get_isAvailable)(
        IWMPControls2 *This,
        BSTR bstrItem,
        VARIANT_BOOL *pIsAvailable);

    HRESULT (STDMETHODCALLTYPE *play)(
        IWMPControls2 *This);

    HRESULT (STDMETHODCALLTYPE *stop)(
        IWMPControls2 *This);

    HRESULT (STDMETHODCALLTYPE *pause)(
        IWMPControls2 *This);

    HRESULT (STDMETHODCALLTYPE *fastForward)(
        IWMPControls2 *This);

    HRESULT (STDMETHODCALLTYPE *fastReverse)(
        IWMPControls2 *This);

    HRESULT (STDMETHODCALLTYPE *get_currentPosition)(
        IWMPControls2 *This,
        double *pdCurrentPosition);

    HRESULT (STDMETHODCALLTYPE *put_currentPosition)(
        IWMPControls2 *This,
        double pdCurrentPosition);

    HRESULT (STDMETHODCALLTYPE *get_currentPositionString)(
        IWMPControls2 *This,
        BSTR *pbstrCurrentPosition);

    HRESULT (STDMETHODCALLTYPE *next)(
        IWMPControls2 *This);

    HRESULT (STDMETHODCALLTYPE *previous)(
        IWMPControls2 *This);

    HRESULT (STDMETHODCALLTYPE *currentItem)(
        IWMPControls2 *This,
        IWMPMedia **ppIWMPMedia);

    HRESULT (STDMETHODCALLTYPE *put_currentItem)(
        IWMPControls2 *This,
        IWMPMedia *ppIWMPMedia);

    HRESULT (STDMETHODCALLTYPE *get_currentMarker)(
        IWMPControls2 *This,
        LONG *plMarker);

    HRESULT (STDMETHODCALLTYPE *put_currentMarker)(
        IWMPControls2 *This,
        LONG plMarker);

    HRESULT (STDMETHODCALLTYPE *playItem)(
        IWMPControls2 *This,
        IWMPMedia *pIWMPMedia);

    /*** IWMPControls2 methods ***/
    HRESULT (STDMETHODCALLTYPE *step)(
        IWMPControls2 *This,
        LONG lStep);

    END_INTERFACE
} IWMPControls2Vtbl;

interface IWMPControls2 {
    CONST_VTBL IWMPControls2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPControls2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPControls2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPControls2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPControls2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPControls2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPControls2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPControls2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPControls methods ***/
#define IWMPControls2_get_isAvailable(This,bstrItem,pIsAvailable) (This)->lpVtbl->get_isAvailable(This,bstrItem,pIsAvailable)
#define IWMPControls2_play(This) (This)->lpVtbl->play(This)
#define IWMPControls2_stop(This) (This)->lpVtbl->stop(This)
#define IWMPControls2_pause(This) (This)->lpVtbl->pause(This)
#define IWMPControls2_fastForward(This) (This)->lpVtbl->fastForward(This)
#define IWMPControls2_fastReverse(This) (This)->lpVtbl->fastReverse(This)
#define IWMPControls2_get_currentPosition(This,pdCurrentPosition) (This)->lpVtbl->get_currentPosition(This,pdCurrentPosition)
#define IWMPControls2_put_currentPosition(This,pdCurrentPosition) (This)->lpVtbl->put_currentPosition(This,pdCurrentPosition)
#define IWMPControls2_get_currentPositionString(This,pbstrCurrentPosition) (This)->lpVtbl->get_currentPositionString(This,pbstrCurrentPosition)
#define IWMPControls2_next(This) (This)->lpVtbl->next(This)
#define IWMPControls2_previous(This) (This)->lpVtbl->previous(This)
#define IWMPControls2_currentItem(This,ppIWMPMedia) (This)->lpVtbl->currentItem(This,ppIWMPMedia)
#define IWMPControls2_put_currentItem(This,ppIWMPMedia) (This)->lpVtbl->put_currentItem(This,ppIWMPMedia)
#define IWMPControls2_get_currentMarker(This,plMarker) (This)->lpVtbl->get_currentMarker(This,plMarker)
#define IWMPControls2_put_currentMarker(This,plMarker) (This)->lpVtbl->put_currentMarker(This,plMarker)
#define IWMPControls2_playItem(This,pIWMPMedia) (This)->lpVtbl->playItem(This,pIWMPMedia)
/*** IWMPControls2 methods ***/
#define IWMPControls2_step(This,lStep) (This)->lpVtbl->step(This,lStep)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPControls2_QueryInterface(IWMPControls2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPControls2_AddRef(IWMPControls2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPControls2_Release(IWMPControls2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPControls2_GetTypeInfoCount(IWMPControls2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPControls2_GetTypeInfo(IWMPControls2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPControls2_GetIDsOfNames(IWMPControls2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPControls2_Invoke(IWMPControls2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPControls methods ***/
static inline HRESULT IWMPControls2_get_isAvailable(IWMPControls2* This,BSTR bstrItem,VARIANT_BOOL *pIsAvailable) {
    return This->lpVtbl->get_isAvailable(This,bstrItem,pIsAvailable);
}
static inline HRESULT IWMPControls2_play(IWMPControls2* This) {
    return This->lpVtbl->play(This);
}
static inline HRESULT IWMPControls2_stop(IWMPControls2* This) {
    return This->lpVtbl->stop(This);
}
static inline HRESULT IWMPControls2_pause(IWMPControls2* This) {
    return This->lpVtbl->pause(This);
}
static inline HRESULT IWMPControls2_fastForward(IWMPControls2* This) {
    return This->lpVtbl->fastForward(This);
}
static inline HRESULT IWMPControls2_fastReverse(IWMPControls2* This) {
    return This->lpVtbl->fastReverse(This);
}
static inline HRESULT IWMPControls2_get_currentPosition(IWMPControls2* This,double *pdCurrentPosition) {
    return This->lpVtbl->get_currentPosition(This,pdCurrentPosition);
}
static inline HRESULT IWMPControls2_put_currentPosition(IWMPControls2* This,double pdCurrentPosition) {
    return This->lpVtbl->put_currentPosition(This,pdCurrentPosition);
}
static inline HRESULT IWMPControls2_get_currentPositionString(IWMPControls2* This,BSTR *pbstrCurrentPosition) {
    return This->lpVtbl->get_currentPositionString(This,pbstrCurrentPosition);
}
static inline HRESULT IWMPControls2_next(IWMPControls2* This) {
    return This->lpVtbl->next(This);
}
static inline HRESULT IWMPControls2_previous(IWMPControls2* This) {
    return This->lpVtbl->previous(This);
}
static inline HRESULT IWMPControls2_currentItem(IWMPControls2* This,IWMPMedia **ppIWMPMedia) {
    return This->lpVtbl->currentItem(This,ppIWMPMedia);
}
static inline HRESULT IWMPControls2_put_currentItem(IWMPControls2* This,IWMPMedia *ppIWMPMedia) {
    return This->lpVtbl->put_currentItem(This,ppIWMPMedia);
}
static inline HRESULT IWMPControls2_get_currentMarker(IWMPControls2* This,LONG *plMarker) {
    return This->lpVtbl->get_currentMarker(This,plMarker);
}
static inline HRESULT IWMPControls2_put_currentMarker(IWMPControls2* This,LONG plMarker) {
    return This->lpVtbl->put_currentMarker(This,plMarker);
}
static inline HRESULT IWMPControls2_playItem(IWMPControls2* This,IWMPMedia *pIWMPMedia) {
    return This->lpVtbl->playItem(This,pIWMPMedia);
}
/*** IWMPControls2 methods ***/
static inline HRESULT IWMPControls2_step(IWMPControls2* This,LONG lStep) {
    return This->lpVtbl->step(This,lStep);
}
#endif
#endif

#endif


#endif  /* __IWMPControls2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPRemoteMediaServices interface
 */
#ifndef __IWMPRemoteMediaServices_INTERFACE_DEFINED__
#define __IWMPRemoteMediaServices_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPRemoteMediaServices, 0xcbb92747, 0x741f, 0x44fe, 0xab,0x5b, 0xf1,0xa4,0x8f,0x3b,0x2a,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cbb92747-741f-44fe-ab5b-f1a48f3b2a59")
IWMPRemoteMediaServices : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetServiceType(
        BSTR *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetApplicationName(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetScriptableObject(
        BSTR *name,
        IDispatch **out) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCustomUIMode(
        BSTR *file) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPRemoteMediaServices, 0xcbb92747, 0x741f, 0x44fe, 0xab,0x5b, 0xf1,0xa4,0x8f,0x3b,0x2a,0x59)
#endif
#else
typedef struct IWMPRemoteMediaServicesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPRemoteMediaServices *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPRemoteMediaServices *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPRemoteMediaServices *This);

    /*** IWMPRemoteMediaServices methods ***/
    HRESULT (STDMETHODCALLTYPE *GetServiceType)(
        IWMPRemoteMediaServices *This,
        BSTR *type);

    HRESULT (STDMETHODCALLTYPE *GetApplicationName)(
        IWMPRemoteMediaServices *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *GetScriptableObject)(
        IWMPRemoteMediaServices *This,
        BSTR *name,
        IDispatch **out);

    HRESULT (STDMETHODCALLTYPE *GetCustomUIMode)(
        IWMPRemoteMediaServices *This,
        BSTR *file);

    END_INTERFACE
} IWMPRemoteMediaServicesVtbl;

interface IWMPRemoteMediaServices {
    CONST_VTBL IWMPRemoteMediaServicesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPRemoteMediaServices_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPRemoteMediaServices_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPRemoteMediaServices_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPRemoteMediaServices methods ***/
#define IWMPRemoteMediaServices_GetServiceType(This,type) (This)->lpVtbl->GetServiceType(This,type)
#define IWMPRemoteMediaServices_GetApplicationName(This,name) (This)->lpVtbl->GetApplicationName(This,name)
#define IWMPRemoteMediaServices_GetScriptableObject(This,name,out) (This)->lpVtbl->GetScriptableObject(This,name,out)
#define IWMPRemoteMediaServices_GetCustomUIMode(This,file) (This)->lpVtbl->GetCustomUIMode(This,file)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPRemoteMediaServices_QueryInterface(IWMPRemoteMediaServices* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPRemoteMediaServices_AddRef(IWMPRemoteMediaServices* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPRemoteMediaServices_Release(IWMPRemoteMediaServices* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPRemoteMediaServices methods ***/
static inline HRESULT IWMPRemoteMediaServices_GetServiceType(IWMPRemoteMediaServices* This,BSTR *type) {
    return This->lpVtbl->GetServiceType(This,type);
}
static inline HRESULT IWMPRemoteMediaServices_GetApplicationName(IWMPRemoteMediaServices* This,BSTR *name) {
    return This->lpVtbl->GetApplicationName(This,name);
}
static inline HRESULT IWMPRemoteMediaServices_GetScriptableObject(IWMPRemoteMediaServices* This,BSTR *name,IDispatch **out) {
    return This->lpVtbl->GetScriptableObject(This,name,out);
}
static inline HRESULT IWMPRemoteMediaServices_GetCustomUIMode(IWMPRemoteMediaServices* This,BSTR *file) {
    return This->lpVtbl->GetCustomUIMode(This,file);
}
#endif
#endif

#endif


#endif  /* __IWMPRemoteMediaServices_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPSettings interface
 */
#ifndef __IWMPSettings_INTERFACE_DEFINED__
#define __IWMPSettings_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPSettings, 0x9104d1ab, 0x80c9, 0x4fed, 0xab,0xf0, 0x2e,0x64,0x17,0xa6,0xdf,0x14);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9104d1ab-80c9-4fed-abf0-2e6417a6df14")
IWMPSettings : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_isAvailable(
        BSTR bstrItem,
        VARIANT_BOOL *pIsAvailable) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_autoStart(
        VARIANT_BOOL *pfAutoStart) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_autoStart(
        VARIANT_BOOL pfAutoStart) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_baseURL(
        BSTR *pbstrBaseURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_baseURL(
        BSTR pbstrBaseURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_defaultFrame(
        BSTR *pbstrDefaultFrame) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_defaultFrame(
        BSTR pbstrDefaultFrame) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_invokeURLs(
        VARIANT_BOOL *pfInvokeURLs) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_invokeURLs(
        VARIANT_BOOL pfInvokeURLs) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_mute(
        VARIANT_BOOL *pfMute) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_mute(
        VARIANT_BOOL pfMute) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_playCount(
        LONG *plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_playCount(
        LONG plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_rate(
        double *pdRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_rate(
        double pdRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_balance(
        LONG *plBalance) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_balance(
        LONG plBalance) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_volume(
        LONG *plVolume) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_volume(
        LONG plVolume) = 0;

    virtual HRESULT STDMETHODCALLTYPE getMode(
        BSTR bstrMode,
        VARIANT_BOOL *pvarfMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE setMode(
        BSTR bstrMode,
        VARIANT_BOOL varfMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_enableErrorDialogs(
        VARIANT_BOOL *pfEnableErrorDialogs) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_enableErrorDialogs(
        VARIANT_BOOL pfEnableErrorDialogs) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPSettings, 0x9104d1ab, 0x80c9, 0x4fed, 0xab,0xf0, 0x2e,0x64,0x17,0xa6,0xdf,0x14)
#endif
#else
typedef struct IWMPSettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPSettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPSettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPSettings *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPSettings *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPSettings *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPSettings *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPSettings *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPSettings methods ***/
    HRESULT (STDMETHODCALLTYPE *get_isAvailable)(
        IWMPSettings *This,
        BSTR bstrItem,
        VARIANT_BOOL *pIsAvailable);

    HRESULT (STDMETHODCALLTYPE *get_autoStart)(
        IWMPSettings *This,
        VARIANT_BOOL *pfAutoStart);

    HRESULT (STDMETHODCALLTYPE *put_autoStart)(
        IWMPSettings *This,
        VARIANT_BOOL pfAutoStart);

    HRESULT (STDMETHODCALLTYPE *get_baseURL)(
        IWMPSettings *This,
        BSTR *pbstrBaseURL);

    HRESULT (STDMETHODCALLTYPE *put_baseURL)(
        IWMPSettings *This,
        BSTR pbstrBaseURL);

    HRESULT (STDMETHODCALLTYPE *get_defaultFrame)(
        IWMPSettings *This,
        BSTR *pbstrDefaultFrame);

    HRESULT (STDMETHODCALLTYPE *put_defaultFrame)(
        IWMPSettings *This,
        BSTR pbstrDefaultFrame);

    HRESULT (STDMETHODCALLTYPE *get_invokeURLs)(
        IWMPSettings *This,
        VARIANT_BOOL *pfInvokeURLs);

    HRESULT (STDMETHODCALLTYPE *put_invokeURLs)(
        IWMPSettings *This,
        VARIANT_BOOL pfInvokeURLs);

    HRESULT (STDMETHODCALLTYPE *get_mute)(
        IWMPSettings *This,
        VARIANT_BOOL *pfMute);

    HRESULT (STDMETHODCALLTYPE *put_mute)(
        IWMPSettings *This,
        VARIANT_BOOL pfMute);

    HRESULT (STDMETHODCALLTYPE *get_playCount)(
        IWMPSettings *This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *put_playCount)(
        IWMPSettings *This,
        LONG plCount);

    HRESULT (STDMETHODCALLTYPE *get_rate)(
        IWMPSettings *This,
        double *pdRate);

    HRESULT (STDMETHODCALLTYPE *put_rate)(
        IWMPSettings *This,
        double pdRate);

    HRESULT (STDMETHODCALLTYPE *get_balance)(
        IWMPSettings *This,
        LONG *plBalance);

    HRESULT (STDMETHODCALLTYPE *put_balance)(
        IWMPSettings *This,
        LONG plBalance);

    HRESULT (STDMETHODCALLTYPE *get_volume)(
        IWMPSettings *This,
        LONG *plVolume);

    HRESULT (STDMETHODCALLTYPE *put_volume)(
        IWMPSettings *This,
        LONG plVolume);

    HRESULT (STDMETHODCALLTYPE *getMode)(
        IWMPSettings *This,
        BSTR bstrMode,
        VARIANT_BOOL *pvarfMode);

    HRESULT (STDMETHODCALLTYPE *setMode)(
        IWMPSettings *This,
        BSTR bstrMode,
        VARIANT_BOOL varfMode);

    HRESULT (STDMETHODCALLTYPE *get_enableErrorDialogs)(
        IWMPSettings *This,
        VARIANT_BOOL *pfEnableErrorDialogs);

    HRESULT (STDMETHODCALLTYPE *put_enableErrorDialogs)(
        IWMPSettings *This,
        VARIANT_BOOL pfEnableErrorDialogs);

    END_INTERFACE
} IWMPSettingsVtbl;

interface IWMPSettings {
    CONST_VTBL IWMPSettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPSettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPSettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPSettings_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPSettings_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPSettings_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPSettings_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPSettings_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPSettings methods ***/
#define IWMPSettings_get_isAvailable(This,bstrItem,pIsAvailable) (This)->lpVtbl->get_isAvailable(This,bstrItem,pIsAvailable)
#define IWMPSettings_get_autoStart(This,pfAutoStart) (This)->lpVtbl->get_autoStart(This,pfAutoStart)
#define IWMPSettings_put_autoStart(This,pfAutoStart) (This)->lpVtbl->put_autoStart(This,pfAutoStart)
#define IWMPSettings_get_baseURL(This,pbstrBaseURL) (This)->lpVtbl->get_baseURL(This,pbstrBaseURL)
#define IWMPSettings_put_baseURL(This,pbstrBaseURL) (This)->lpVtbl->put_baseURL(This,pbstrBaseURL)
#define IWMPSettings_get_defaultFrame(This,pbstrDefaultFrame) (This)->lpVtbl->get_defaultFrame(This,pbstrDefaultFrame)
#define IWMPSettings_put_defaultFrame(This,pbstrDefaultFrame) (This)->lpVtbl->put_defaultFrame(This,pbstrDefaultFrame)
#define IWMPSettings_get_invokeURLs(This,pfInvokeURLs) (This)->lpVtbl->get_invokeURLs(This,pfInvokeURLs)
#define IWMPSettings_put_invokeURLs(This,pfInvokeURLs) (This)->lpVtbl->put_invokeURLs(This,pfInvokeURLs)
#define IWMPSettings_get_mute(This,pfMute) (This)->lpVtbl->get_mute(This,pfMute)
#define IWMPSettings_put_mute(This,pfMute) (This)->lpVtbl->put_mute(This,pfMute)
#define IWMPSettings_get_playCount(This,plCount) (This)->lpVtbl->get_playCount(This,plCount)
#define IWMPSettings_put_playCount(This,plCount) (This)->lpVtbl->put_playCount(This,plCount)
#define IWMPSettings_get_rate(This,pdRate) (This)->lpVtbl->get_rate(This,pdRate)
#define IWMPSettings_put_rate(This,pdRate) (This)->lpVtbl->put_rate(This,pdRate)
#define IWMPSettings_get_balance(This,plBalance) (This)->lpVtbl->get_balance(This,plBalance)
#define IWMPSettings_put_balance(This,plBalance) (This)->lpVtbl->put_balance(This,plBalance)
#define IWMPSettings_get_volume(This,plVolume) (This)->lpVtbl->get_volume(This,plVolume)
#define IWMPSettings_put_volume(This,plVolume) (This)->lpVtbl->put_volume(This,plVolume)
#define IWMPSettings_getMode(This,bstrMode,pvarfMode) (This)->lpVtbl->getMode(This,bstrMode,pvarfMode)
#define IWMPSettings_setMode(This,bstrMode,varfMode) (This)->lpVtbl->setMode(This,bstrMode,varfMode)
#define IWMPSettings_get_enableErrorDialogs(This,pfEnableErrorDialogs) (This)->lpVtbl->get_enableErrorDialogs(This,pfEnableErrorDialogs)
#define IWMPSettings_put_enableErrorDialogs(This,pfEnableErrorDialogs) (This)->lpVtbl->put_enableErrorDialogs(This,pfEnableErrorDialogs)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPSettings_QueryInterface(IWMPSettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPSettings_AddRef(IWMPSettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPSettings_Release(IWMPSettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPSettings_GetTypeInfoCount(IWMPSettings* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPSettings_GetTypeInfo(IWMPSettings* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPSettings_GetIDsOfNames(IWMPSettings* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPSettings_Invoke(IWMPSettings* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPSettings methods ***/
static inline HRESULT IWMPSettings_get_isAvailable(IWMPSettings* This,BSTR bstrItem,VARIANT_BOOL *pIsAvailable) {
    return This->lpVtbl->get_isAvailable(This,bstrItem,pIsAvailable);
}
static inline HRESULT IWMPSettings_get_autoStart(IWMPSettings* This,VARIANT_BOOL *pfAutoStart) {
    return This->lpVtbl->get_autoStart(This,pfAutoStart);
}
static inline HRESULT IWMPSettings_put_autoStart(IWMPSettings* This,VARIANT_BOOL pfAutoStart) {
    return This->lpVtbl->put_autoStart(This,pfAutoStart);
}
static inline HRESULT IWMPSettings_get_baseURL(IWMPSettings* This,BSTR *pbstrBaseURL) {
    return This->lpVtbl->get_baseURL(This,pbstrBaseURL);
}
static inline HRESULT IWMPSettings_put_baseURL(IWMPSettings* This,BSTR pbstrBaseURL) {
    return This->lpVtbl->put_baseURL(This,pbstrBaseURL);
}
static inline HRESULT IWMPSettings_get_defaultFrame(IWMPSettings* This,BSTR *pbstrDefaultFrame) {
    return This->lpVtbl->get_defaultFrame(This,pbstrDefaultFrame);
}
static inline HRESULT IWMPSettings_put_defaultFrame(IWMPSettings* This,BSTR pbstrDefaultFrame) {
    return This->lpVtbl->put_defaultFrame(This,pbstrDefaultFrame);
}
static inline HRESULT IWMPSettings_get_invokeURLs(IWMPSettings* This,VARIANT_BOOL *pfInvokeURLs) {
    return This->lpVtbl->get_invokeURLs(This,pfInvokeURLs);
}
static inline HRESULT IWMPSettings_put_invokeURLs(IWMPSettings* This,VARIANT_BOOL pfInvokeURLs) {
    return This->lpVtbl->put_invokeURLs(This,pfInvokeURLs);
}
static inline HRESULT IWMPSettings_get_mute(IWMPSettings* This,VARIANT_BOOL *pfMute) {
    return This->lpVtbl->get_mute(This,pfMute);
}
static inline HRESULT IWMPSettings_put_mute(IWMPSettings* This,VARIANT_BOOL pfMute) {
    return This->lpVtbl->put_mute(This,pfMute);
}
static inline HRESULT IWMPSettings_get_playCount(IWMPSettings* This,LONG *plCount) {
    return This->lpVtbl->get_playCount(This,plCount);
}
static inline HRESULT IWMPSettings_put_playCount(IWMPSettings* This,LONG plCount) {
    return This->lpVtbl->put_playCount(This,plCount);
}
static inline HRESULT IWMPSettings_get_rate(IWMPSettings* This,double *pdRate) {
    return This->lpVtbl->get_rate(This,pdRate);
}
static inline HRESULT IWMPSettings_put_rate(IWMPSettings* This,double pdRate) {
    return This->lpVtbl->put_rate(This,pdRate);
}
static inline HRESULT IWMPSettings_get_balance(IWMPSettings* This,LONG *plBalance) {
    return This->lpVtbl->get_balance(This,plBalance);
}
static inline HRESULT IWMPSettings_put_balance(IWMPSettings* This,LONG plBalance) {
    return This->lpVtbl->put_balance(This,plBalance);
}
static inline HRESULT IWMPSettings_get_volume(IWMPSettings* This,LONG *plVolume) {
    return This->lpVtbl->get_volume(This,plVolume);
}
static inline HRESULT IWMPSettings_put_volume(IWMPSettings* This,LONG plVolume) {
    return This->lpVtbl->put_volume(This,plVolume);
}
static inline HRESULT IWMPSettings_getMode(IWMPSettings* This,BSTR bstrMode,VARIANT_BOOL *pvarfMode) {
    return This->lpVtbl->getMode(This,bstrMode,pvarfMode);
}
static inline HRESULT IWMPSettings_setMode(IWMPSettings* This,BSTR bstrMode,VARIANT_BOOL varfMode) {
    return This->lpVtbl->setMode(This,bstrMode,varfMode);
}
static inline HRESULT IWMPSettings_get_enableErrorDialogs(IWMPSettings* This,VARIANT_BOOL *pfEnableErrorDialogs) {
    return This->lpVtbl->get_enableErrorDialogs(This,pfEnableErrorDialogs);
}
static inline HRESULT IWMPSettings_put_enableErrorDialogs(IWMPSettings* This,VARIANT_BOOL pfEnableErrorDialogs) {
    return This->lpVtbl->put_enableErrorDialogs(This,pfEnableErrorDialogs);
}
#endif
#endif

#endif


#endif  /* __IWMPSettings_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPStringCollection interface
 */
#ifndef __IWMPStringCollection_INTERFACE_DEFINED__
#define __IWMPStringCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPStringCollection, 0x4a976298, 0x8c0d, 0x11d3, 0xb3,0x89, 0x00,0xc0,0x4f,0x68,0x57,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4a976298-8c0d-11d3-b389-00c04f68574b")
IWMPStringCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_count(
        LONG *plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        LONG lIndex,
        BSTR *pbstrString) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPStringCollection, 0x4a976298, 0x8c0d, 0x11d3, 0xb3,0x89, 0x00,0xc0,0x4f,0x68,0x57,0x4b)
#endif
#else
typedef struct IWMPStringCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPStringCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPStringCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPStringCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPStringCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPStringCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPStringCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPStringCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPStringCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_count)(
        IWMPStringCollection *This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *Item)(
        IWMPStringCollection *This,
        LONG lIndex,
        BSTR *pbstrString);

    END_INTERFACE
} IWMPStringCollectionVtbl;

interface IWMPStringCollection {
    CONST_VTBL IWMPStringCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPStringCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPStringCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPStringCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPStringCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPStringCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPStringCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPStringCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPStringCollection methods ***/
#define IWMPStringCollection_get_count(This,plCount) (This)->lpVtbl->get_count(This,plCount)
#define IWMPStringCollection_Item(This,lIndex,pbstrString) (This)->lpVtbl->Item(This,lIndex,pbstrString)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPStringCollection_QueryInterface(IWMPStringCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPStringCollection_AddRef(IWMPStringCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPStringCollection_Release(IWMPStringCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPStringCollection_GetTypeInfoCount(IWMPStringCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPStringCollection_GetTypeInfo(IWMPStringCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPStringCollection_GetIDsOfNames(IWMPStringCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPStringCollection_Invoke(IWMPStringCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPStringCollection methods ***/
static inline HRESULT IWMPStringCollection_get_count(IWMPStringCollection* This,LONG *plCount) {
    return This->lpVtbl->get_count(This,plCount);
}
static inline HRESULT IWMPStringCollection_Item(IWMPStringCollection* This,LONG lIndex,BSTR *pbstrString) {
    return This->lpVtbl->Item(This,lIndex,pbstrString);
}
#endif
#endif

#endif


#endif  /* __IWMPStringCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPMediaCollection interface
 */
#ifndef __IWMPMediaCollection_INTERFACE_DEFINED__
#define __IWMPMediaCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPMediaCollection, 0x8363bc22, 0xb4b4, 0x4b19, 0x98,0x9d, 0x1c,0xd7,0x65,0x74,0x9d,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8363bc22-b4b4-4b19-989d-1cd765749dd1")
IWMPMediaCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE add(
        BSTR bstrURL,
        IWMPMedia **ppItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE getAll(
        IWMPPlaylist **ppMediaItems) = 0;

    virtual HRESULT STDMETHODCALLTYPE getByName(
        BSTR bstrName,
        IWMPPlaylist **ppMediaItems) = 0;

    virtual HRESULT STDMETHODCALLTYPE getByGenre(
        BSTR bstrGenre,
        IWMPPlaylist **ppMediaItems) = 0;

    virtual HRESULT STDMETHODCALLTYPE getByAuthor(
        BSTR bstrAuthor,
        IWMPPlaylist **ppMediaItems) = 0;

    virtual HRESULT STDMETHODCALLTYPE getByAlbum(
        BSTR bstrAlbum,
        IWMPPlaylist **ppMediaItems) = 0;

    virtual HRESULT STDMETHODCALLTYPE getByAttribute(
        BSTR bstrAttribute,
        BSTR bstrValue,
        IWMPPlaylist **ppMediaItems) = 0;

    virtual HRESULT STDMETHODCALLTYPE remove(
        IWMPMedia *pItem,
        VARIANT_BOOL varfDeleteFile) = 0;

    virtual HRESULT STDMETHODCALLTYPE getAttributeStringCollection(
        BSTR bstrAttribute,
        BSTR bstrMediaType,
        IWMPStringCollection **ppStringCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE getMediaAtom(
        BSTR bstrItemName,
        LONG *plAtom) = 0;

    virtual HRESULT STDMETHODCALLTYPE setDeleted(
        IWMPMedia *pItem,
        VARIANT_BOOL varfIsDeleted) = 0;

    virtual HRESULT STDMETHODCALLTYPE isDeleted(
        IWMPMedia *pItem,
        VARIANT_BOOL *pvarfIsDeleted) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPMediaCollection, 0x8363bc22, 0xb4b4, 0x4b19, 0x98,0x9d, 0x1c,0xd7,0x65,0x74,0x9d,0xd1)
#endif
#else
typedef struct IWMPMediaCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPMediaCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPMediaCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPMediaCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPMediaCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPMediaCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPMediaCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPMediaCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPMediaCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *add)(
        IWMPMediaCollection *This,
        BSTR bstrURL,
        IWMPMedia **ppItem);

    HRESULT (STDMETHODCALLTYPE *getAll)(
        IWMPMediaCollection *This,
        IWMPPlaylist **ppMediaItems);

    HRESULT (STDMETHODCALLTYPE *getByName)(
        IWMPMediaCollection *This,
        BSTR bstrName,
        IWMPPlaylist **ppMediaItems);

    HRESULT (STDMETHODCALLTYPE *getByGenre)(
        IWMPMediaCollection *This,
        BSTR bstrGenre,
        IWMPPlaylist **ppMediaItems);

    HRESULT (STDMETHODCALLTYPE *getByAuthor)(
        IWMPMediaCollection *This,
        BSTR bstrAuthor,
        IWMPPlaylist **ppMediaItems);

    HRESULT (STDMETHODCALLTYPE *getByAlbum)(
        IWMPMediaCollection *This,
        BSTR bstrAlbum,
        IWMPPlaylist **ppMediaItems);

    HRESULT (STDMETHODCALLTYPE *getByAttribute)(
        IWMPMediaCollection *This,
        BSTR bstrAttribute,
        BSTR bstrValue,
        IWMPPlaylist **ppMediaItems);

    HRESULT (STDMETHODCALLTYPE *remove)(
        IWMPMediaCollection *This,
        IWMPMedia *pItem,
        VARIANT_BOOL varfDeleteFile);

    HRESULT (STDMETHODCALLTYPE *getAttributeStringCollection)(
        IWMPMediaCollection *This,
        BSTR bstrAttribute,
        BSTR bstrMediaType,
        IWMPStringCollection **ppStringCollection);

    HRESULT (STDMETHODCALLTYPE *getMediaAtom)(
        IWMPMediaCollection *This,
        BSTR bstrItemName,
        LONG *plAtom);

    HRESULT (STDMETHODCALLTYPE *setDeleted)(
        IWMPMediaCollection *This,
        IWMPMedia *pItem,
        VARIANT_BOOL varfIsDeleted);

    HRESULT (STDMETHODCALLTYPE *isDeleted)(
        IWMPMediaCollection *This,
        IWMPMedia *pItem,
        VARIANT_BOOL *pvarfIsDeleted);

    END_INTERFACE
} IWMPMediaCollectionVtbl;

interface IWMPMediaCollection {
    CONST_VTBL IWMPMediaCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPMediaCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPMediaCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPMediaCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPMediaCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPMediaCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPMediaCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPMediaCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPMediaCollection methods ***/
#define IWMPMediaCollection_add(This,bstrURL,ppItem) (This)->lpVtbl->add(This,bstrURL,ppItem)
#define IWMPMediaCollection_getAll(This,ppMediaItems) (This)->lpVtbl->getAll(This,ppMediaItems)
#define IWMPMediaCollection_getByName(This,bstrName,ppMediaItems) (This)->lpVtbl->getByName(This,bstrName,ppMediaItems)
#define IWMPMediaCollection_getByGenre(This,bstrGenre,ppMediaItems) (This)->lpVtbl->getByGenre(This,bstrGenre,ppMediaItems)
#define IWMPMediaCollection_getByAuthor(This,bstrAuthor,ppMediaItems) (This)->lpVtbl->getByAuthor(This,bstrAuthor,ppMediaItems)
#define IWMPMediaCollection_getByAlbum(This,bstrAlbum,ppMediaItems) (This)->lpVtbl->getByAlbum(This,bstrAlbum,ppMediaItems)
#define IWMPMediaCollection_getByAttribute(This,bstrAttribute,bstrValue,ppMediaItems) (This)->lpVtbl->getByAttribute(This,bstrAttribute,bstrValue,ppMediaItems)
#define IWMPMediaCollection_remove(This,pItem,varfDeleteFile) (This)->lpVtbl->remove(This,pItem,varfDeleteFile)
#define IWMPMediaCollection_getAttributeStringCollection(This,bstrAttribute,bstrMediaType,ppStringCollection) (This)->lpVtbl->getAttributeStringCollection(This,bstrAttribute,bstrMediaType,ppStringCollection)
#define IWMPMediaCollection_getMediaAtom(This,bstrItemName,plAtom) (This)->lpVtbl->getMediaAtom(This,bstrItemName,plAtom)
#define IWMPMediaCollection_setDeleted(This,pItem,varfIsDeleted) (This)->lpVtbl->setDeleted(This,pItem,varfIsDeleted)
#define IWMPMediaCollection_isDeleted(This,pItem,pvarfIsDeleted) (This)->lpVtbl->isDeleted(This,pItem,pvarfIsDeleted)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPMediaCollection_QueryInterface(IWMPMediaCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPMediaCollection_AddRef(IWMPMediaCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPMediaCollection_Release(IWMPMediaCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPMediaCollection_GetTypeInfoCount(IWMPMediaCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPMediaCollection_GetTypeInfo(IWMPMediaCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPMediaCollection_GetIDsOfNames(IWMPMediaCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPMediaCollection_Invoke(IWMPMediaCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPMediaCollection methods ***/
static inline HRESULT IWMPMediaCollection_add(IWMPMediaCollection* This,BSTR bstrURL,IWMPMedia **ppItem) {
    return This->lpVtbl->add(This,bstrURL,ppItem);
}
static inline HRESULT IWMPMediaCollection_getAll(IWMPMediaCollection* This,IWMPPlaylist **ppMediaItems) {
    return This->lpVtbl->getAll(This,ppMediaItems);
}
static inline HRESULT IWMPMediaCollection_getByName(IWMPMediaCollection* This,BSTR bstrName,IWMPPlaylist **ppMediaItems) {
    return This->lpVtbl->getByName(This,bstrName,ppMediaItems);
}
static inline HRESULT IWMPMediaCollection_getByGenre(IWMPMediaCollection* This,BSTR bstrGenre,IWMPPlaylist **ppMediaItems) {
    return This->lpVtbl->getByGenre(This,bstrGenre,ppMediaItems);
}
static inline HRESULT IWMPMediaCollection_getByAuthor(IWMPMediaCollection* This,BSTR bstrAuthor,IWMPPlaylist **ppMediaItems) {
    return This->lpVtbl->getByAuthor(This,bstrAuthor,ppMediaItems);
}
static inline HRESULT IWMPMediaCollection_getByAlbum(IWMPMediaCollection* This,BSTR bstrAlbum,IWMPPlaylist **ppMediaItems) {
    return This->lpVtbl->getByAlbum(This,bstrAlbum,ppMediaItems);
}
static inline HRESULT IWMPMediaCollection_getByAttribute(IWMPMediaCollection* This,BSTR bstrAttribute,BSTR bstrValue,IWMPPlaylist **ppMediaItems) {
    return This->lpVtbl->getByAttribute(This,bstrAttribute,bstrValue,ppMediaItems);
}
static inline HRESULT IWMPMediaCollection_remove(IWMPMediaCollection* This,IWMPMedia *pItem,VARIANT_BOOL varfDeleteFile) {
    return This->lpVtbl->remove(This,pItem,varfDeleteFile);
}
static inline HRESULT IWMPMediaCollection_getAttributeStringCollection(IWMPMediaCollection* This,BSTR bstrAttribute,BSTR bstrMediaType,IWMPStringCollection **ppStringCollection) {
    return This->lpVtbl->getAttributeStringCollection(This,bstrAttribute,bstrMediaType,ppStringCollection);
}
static inline HRESULT IWMPMediaCollection_getMediaAtom(IWMPMediaCollection* This,BSTR bstrItemName,LONG *plAtom) {
    return This->lpVtbl->getMediaAtom(This,bstrItemName,plAtom);
}
static inline HRESULT IWMPMediaCollection_setDeleted(IWMPMediaCollection* This,IWMPMedia *pItem,VARIANT_BOOL varfIsDeleted) {
    return This->lpVtbl->setDeleted(This,pItem,varfIsDeleted);
}
static inline HRESULT IWMPMediaCollection_isDeleted(IWMPMediaCollection* This,IWMPMedia *pItem,VARIANT_BOOL *pvarfIsDeleted) {
    return This->lpVtbl->isDeleted(This,pItem,pvarfIsDeleted);
}
#endif
#endif

#endif


#endif  /* __IWMPMediaCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPPlaylistArray interface
 */
#ifndef __IWMPPlaylistArray_INTERFACE_DEFINED__
#define __IWMPPlaylistArray_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPPlaylistArray, 0x679409c0, 0x99f7, 0x11d3, 0x9f,0xb7, 0x00,0x10,0x5a,0xa6,0x20,0xbb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("679409c0-99f7-11d3-9fb7-00105aa620bb")
IWMPPlaylistArray : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_count(
        LONG *plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        LONG lIndex,
        IWMPPlaylist **ppItem) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPPlaylistArray, 0x679409c0, 0x99f7, 0x11d3, 0x9f,0xb7, 0x00,0x10,0x5a,0xa6,0x20,0xbb)
#endif
#else
typedef struct IWMPPlaylistArrayVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPPlaylistArray *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPPlaylistArray *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPPlaylistArray *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPPlaylistArray *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPPlaylistArray *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPPlaylistArray *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPPlaylistArray *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPPlaylistArray methods ***/
    HRESULT (STDMETHODCALLTYPE *get_count)(
        IWMPPlaylistArray *This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *Item)(
        IWMPPlaylistArray *This,
        LONG lIndex,
        IWMPPlaylist **ppItem);

    END_INTERFACE
} IWMPPlaylistArrayVtbl;

interface IWMPPlaylistArray {
    CONST_VTBL IWMPPlaylistArrayVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPPlaylistArray_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPPlaylistArray_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPPlaylistArray_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPPlaylistArray_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPPlaylistArray_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPPlaylistArray_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPPlaylistArray_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPPlaylistArray methods ***/
#define IWMPPlaylistArray_get_count(This,plCount) (This)->lpVtbl->get_count(This,plCount)
#define IWMPPlaylistArray_Item(This,lIndex,ppItem) (This)->lpVtbl->Item(This,lIndex,ppItem)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPPlaylistArray_QueryInterface(IWMPPlaylistArray* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPPlaylistArray_AddRef(IWMPPlaylistArray* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPPlaylistArray_Release(IWMPPlaylistArray* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPPlaylistArray_GetTypeInfoCount(IWMPPlaylistArray* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPPlaylistArray_GetTypeInfo(IWMPPlaylistArray* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPPlaylistArray_GetIDsOfNames(IWMPPlaylistArray* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPPlaylistArray_Invoke(IWMPPlaylistArray* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPPlaylistArray methods ***/
static inline HRESULT IWMPPlaylistArray_get_count(IWMPPlaylistArray* This,LONG *plCount) {
    return This->lpVtbl->get_count(This,plCount);
}
static inline HRESULT IWMPPlaylistArray_Item(IWMPPlaylistArray* This,LONG lIndex,IWMPPlaylist **ppItem) {
    return This->lpVtbl->Item(This,lIndex,ppItem);
}
#endif
#endif

#endif


#endif  /* __IWMPPlaylistArray_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPPlaylistCollection interface
 */
#ifndef __IWMPPlaylistCollection_INTERFACE_DEFINED__
#define __IWMPPlaylistCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPPlaylistCollection, 0x10a13217, 0x23a7, 0x439b, 0xb1,0xc0, 0xd8,0x47,0xc7,0x9b,0x77,0x74);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("10a13217-23a7-439b-b1c0-d847c79b7774")
IWMPPlaylistCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE newPlaylist(
        BSTR bstrName,
        IWMPPlaylist **ppItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE getAll(
        IWMPPlaylistArray **ppPlaylistArray) = 0;

    virtual HRESULT STDMETHODCALLTYPE getByName(
        BSTR bstrName,
        IWMPPlaylistArray **ppPlaylistArray) = 0;

    virtual HRESULT STDMETHODCALLTYPE remove(
        IWMPPlaylist *pItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE setDeleted(
        IWMPPlaylist *pItem,
        VARIANT_BOOL varfIsDeleted) = 0;

    virtual HRESULT STDMETHODCALLTYPE isDeleted(
        IWMPPlaylist *pItem,
        VARIANT_BOOL *pvarfIsDeleted) = 0;

    virtual HRESULT STDMETHODCALLTYPE importPlaylist(
        IWMPPlaylist *pItem,
        IWMPPlaylist **ppImportedItem) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPPlaylistCollection, 0x10a13217, 0x23a7, 0x439b, 0xb1,0xc0, 0xd8,0x47,0xc7,0x9b,0x77,0x74)
#endif
#else
typedef struct IWMPPlaylistCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPPlaylistCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPPlaylistCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPPlaylistCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPPlaylistCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPPlaylistCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPPlaylistCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPPlaylistCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPPlaylistCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *newPlaylist)(
        IWMPPlaylistCollection *This,
        BSTR bstrName,
        IWMPPlaylist **ppItem);

    HRESULT (STDMETHODCALLTYPE *getAll)(
        IWMPPlaylistCollection *This,
        IWMPPlaylistArray **ppPlaylistArray);

    HRESULT (STDMETHODCALLTYPE *getByName)(
        IWMPPlaylistCollection *This,
        BSTR bstrName,
        IWMPPlaylistArray **ppPlaylistArray);

    HRESULT (STDMETHODCALLTYPE *remove)(
        IWMPPlaylistCollection *This,
        IWMPPlaylist *pItem);

    HRESULT (STDMETHODCALLTYPE *setDeleted)(
        IWMPPlaylistCollection *This,
        IWMPPlaylist *pItem,
        VARIANT_BOOL varfIsDeleted);

    HRESULT (STDMETHODCALLTYPE *isDeleted)(
        IWMPPlaylistCollection *This,
        IWMPPlaylist *pItem,
        VARIANT_BOOL *pvarfIsDeleted);

    HRESULT (STDMETHODCALLTYPE *importPlaylist)(
        IWMPPlaylistCollection *This,
        IWMPPlaylist *pItem,
        IWMPPlaylist **ppImportedItem);

    END_INTERFACE
} IWMPPlaylistCollectionVtbl;

interface IWMPPlaylistCollection {
    CONST_VTBL IWMPPlaylistCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPPlaylistCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPPlaylistCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPPlaylistCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPPlaylistCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPPlaylistCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPPlaylistCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPPlaylistCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPPlaylistCollection methods ***/
#define IWMPPlaylistCollection_newPlaylist(This,bstrName,ppItem) (This)->lpVtbl->newPlaylist(This,bstrName,ppItem)
#define IWMPPlaylistCollection_getAll(This,ppPlaylistArray) (This)->lpVtbl->getAll(This,ppPlaylistArray)
#define IWMPPlaylistCollection_getByName(This,bstrName,ppPlaylistArray) (This)->lpVtbl->getByName(This,bstrName,ppPlaylistArray)
#define IWMPPlaylistCollection_remove(This,pItem) (This)->lpVtbl->remove(This,pItem)
#define IWMPPlaylistCollection_setDeleted(This,pItem,varfIsDeleted) (This)->lpVtbl->setDeleted(This,pItem,varfIsDeleted)
#define IWMPPlaylistCollection_isDeleted(This,pItem,pvarfIsDeleted) (This)->lpVtbl->isDeleted(This,pItem,pvarfIsDeleted)
#define IWMPPlaylistCollection_importPlaylist(This,pItem,ppImportedItem) (This)->lpVtbl->importPlaylist(This,pItem,ppImportedItem)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPPlaylistCollection_QueryInterface(IWMPPlaylistCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPPlaylistCollection_AddRef(IWMPPlaylistCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPPlaylistCollection_Release(IWMPPlaylistCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPPlaylistCollection_GetTypeInfoCount(IWMPPlaylistCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPPlaylistCollection_GetTypeInfo(IWMPPlaylistCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPPlaylistCollection_GetIDsOfNames(IWMPPlaylistCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPPlaylistCollection_Invoke(IWMPPlaylistCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPPlaylistCollection methods ***/
static inline HRESULT IWMPPlaylistCollection_newPlaylist(IWMPPlaylistCollection* This,BSTR bstrName,IWMPPlaylist **ppItem) {
    return This->lpVtbl->newPlaylist(This,bstrName,ppItem);
}
static inline HRESULT IWMPPlaylistCollection_getAll(IWMPPlaylistCollection* This,IWMPPlaylistArray **ppPlaylistArray) {
    return This->lpVtbl->getAll(This,ppPlaylistArray);
}
static inline HRESULT IWMPPlaylistCollection_getByName(IWMPPlaylistCollection* This,BSTR bstrName,IWMPPlaylistArray **ppPlaylistArray) {
    return This->lpVtbl->getByName(This,bstrName,ppPlaylistArray);
}
static inline HRESULT IWMPPlaylistCollection_remove(IWMPPlaylistCollection* This,IWMPPlaylist *pItem) {
    return This->lpVtbl->remove(This,pItem);
}
static inline HRESULT IWMPPlaylistCollection_setDeleted(IWMPPlaylistCollection* This,IWMPPlaylist *pItem,VARIANT_BOOL varfIsDeleted) {
    return This->lpVtbl->setDeleted(This,pItem,varfIsDeleted);
}
static inline HRESULT IWMPPlaylistCollection_isDeleted(IWMPPlaylistCollection* This,IWMPPlaylist *pItem,VARIANT_BOOL *pvarfIsDeleted) {
    return This->lpVtbl->isDeleted(This,pItem,pvarfIsDeleted);
}
static inline HRESULT IWMPPlaylistCollection_importPlaylist(IWMPPlaylistCollection* This,IWMPPlaylist *pItem,IWMPPlaylist **ppImportedItem) {
    return This->lpVtbl->importPlaylist(This,pItem,ppImportedItem);
}
#endif
#endif

#endif


#endif  /* __IWMPPlaylistCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPNetwork interface
 */
#ifndef __IWMPNetwork_INTERFACE_DEFINED__
#define __IWMPNetwork_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPNetwork, 0xec21b779, 0xedef, 0x462d, 0xbb,0xa4, 0xad,0x9d,0xde,0x2b,0x29,0xa7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ec21b779-edef-462d-bba4-ad9dde2b29a7")
IWMPNetwork : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_bandWidth(
        LONG *plBandwidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_recoveredPackets(
        LONG *plRecoveredPackets) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_sourceProtocol(
        BSTR *pbstrSourceProtocol) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_receivedPackets(
        LONG *plReceivedPackets) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_lostPackets(
        LONG *plLostPackets) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_receptionQuality(
        LONG *plReceptionQuality) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_bufferingCount(
        LONG *plBufferingCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_bufferingProgress(
        LONG *plBufferingProgress) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_bufferingTime(
        LONG *plBufferingTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_bufferingTime(
        LONG plBufferingTime) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_frameRate(
        LONG *plFrameRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_maxBitRate(
        LONG *plBitRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_bitRate(
        LONG *plBitRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE getProxySettings(
        BSTR bstrProtocol,
        LONG *plProxySetting) = 0;

    virtual HRESULT STDMETHODCALLTYPE setProxySettings(
        BSTR bstrProtocol,
        LONG lProxySetting) = 0;

    virtual HRESULT STDMETHODCALLTYPE getProxyName(
        BSTR bstrProtocol,
        BSTR *pbstrProxyName) = 0;

    virtual HRESULT STDMETHODCALLTYPE setProxyName(
        BSTR bstrProtocol,
        BSTR bstrProxyName) = 0;

    virtual HRESULT STDMETHODCALLTYPE getProxyPort(
        BSTR bstrProtocol,
        LONG *lProxyPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE setProxyPort(
        BSTR bstrProtocol,
        LONG lProxyPort) = 0;

    virtual HRESULT STDMETHODCALLTYPE getProxyExceptionList(
        BSTR bstrProtocol,
        BSTR *pbstrExceptionList) = 0;

    virtual HRESULT STDMETHODCALLTYPE setProxyExceptionList(
        BSTR bstrProtocol,
        BSTR pbstrExceptionList) = 0;

    virtual HRESULT STDMETHODCALLTYPE getProxyBypassForLocal(
        BSTR bstrProtocol,
        VARIANT_BOOL *pfBypassForLocal) = 0;

    virtual HRESULT STDMETHODCALLTYPE setProxyBypassForLocal(
        BSTR bstrProtocol,
        VARIANT_BOOL fBypassForLocal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_maxBandwidth(
        LONG *lMaxBandwidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_maxBandwidth(
        LONG lMaxBandwidth) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_downloadProgress(
        LONG *plDownloadProgress) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_encodedFrameRate(
        LONG *plFrameRate) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_framesSkipped(
        LONG *plFrames) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPNetwork, 0xec21b779, 0xedef, 0x462d, 0xbb,0xa4, 0xad,0x9d,0xde,0x2b,0x29,0xa7)
#endif
#else
typedef struct IWMPNetworkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPNetwork *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPNetwork *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPNetwork *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPNetwork *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPNetwork *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPNetwork *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPNetwork *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPNetwork methods ***/
    HRESULT (STDMETHODCALLTYPE *get_bandWidth)(
        IWMPNetwork *This,
        LONG *plBandwidth);

    HRESULT (STDMETHODCALLTYPE *get_recoveredPackets)(
        IWMPNetwork *This,
        LONG *plRecoveredPackets);

    HRESULT (STDMETHODCALLTYPE *get_sourceProtocol)(
        IWMPNetwork *This,
        BSTR *pbstrSourceProtocol);

    HRESULT (STDMETHODCALLTYPE *get_receivedPackets)(
        IWMPNetwork *This,
        LONG *plReceivedPackets);

    HRESULT (STDMETHODCALLTYPE *get_lostPackets)(
        IWMPNetwork *This,
        LONG *plLostPackets);

    HRESULT (STDMETHODCALLTYPE *get_receptionQuality)(
        IWMPNetwork *This,
        LONG *plReceptionQuality);

    HRESULT (STDMETHODCALLTYPE *get_bufferingCount)(
        IWMPNetwork *This,
        LONG *plBufferingCount);

    HRESULT (STDMETHODCALLTYPE *get_bufferingProgress)(
        IWMPNetwork *This,
        LONG *plBufferingProgress);

    HRESULT (STDMETHODCALLTYPE *get_bufferingTime)(
        IWMPNetwork *This,
        LONG *plBufferingTime);

    HRESULT (STDMETHODCALLTYPE *put_bufferingTime)(
        IWMPNetwork *This,
        LONG plBufferingTime);

    HRESULT (STDMETHODCALLTYPE *get_frameRate)(
        IWMPNetwork *This,
        LONG *plFrameRate);

    HRESULT (STDMETHODCALLTYPE *get_maxBitRate)(
        IWMPNetwork *This,
        LONG *plBitRate);

    HRESULT (STDMETHODCALLTYPE *get_bitRate)(
        IWMPNetwork *This,
        LONG *plBitRate);

    HRESULT (STDMETHODCALLTYPE *getProxySettings)(
        IWMPNetwork *This,
        BSTR bstrProtocol,
        LONG *plProxySetting);

    HRESULT (STDMETHODCALLTYPE *setProxySettings)(
        IWMPNetwork *This,
        BSTR bstrProtocol,
        LONG lProxySetting);

    HRESULT (STDMETHODCALLTYPE *getProxyName)(
        IWMPNetwork *This,
        BSTR bstrProtocol,
        BSTR *pbstrProxyName);

    HRESULT (STDMETHODCALLTYPE *setProxyName)(
        IWMPNetwork *This,
        BSTR bstrProtocol,
        BSTR bstrProxyName);

    HRESULT (STDMETHODCALLTYPE *getProxyPort)(
        IWMPNetwork *This,
        BSTR bstrProtocol,
        LONG *lProxyPort);

    HRESULT (STDMETHODCALLTYPE *setProxyPort)(
        IWMPNetwork *This,
        BSTR bstrProtocol,
        LONG lProxyPort);

    HRESULT (STDMETHODCALLTYPE *getProxyExceptionList)(
        IWMPNetwork *This,
        BSTR bstrProtocol,
        BSTR *pbstrExceptionList);

    HRESULT (STDMETHODCALLTYPE *setProxyExceptionList)(
        IWMPNetwork *This,
        BSTR bstrProtocol,
        BSTR pbstrExceptionList);

    HRESULT (STDMETHODCALLTYPE *getProxyBypassForLocal)(
        IWMPNetwork *This,
        BSTR bstrProtocol,
        VARIANT_BOOL *pfBypassForLocal);

    HRESULT (STDMETHODCALLTYPE *setProxyBypassForLocal)(
        IWMPNetwork *This,
        BSTR bstrProtocol,
        VARIANT_BOOL fBypassForLocal);

    HRESULT (STDMETHODCALLTYPE *get_maxBandwidth)(
        IWMPNetwork *This,
        LONG *lMaxBandwidth);

    HRESULT (STDMETHODCALLTYPE *put_maxBandwidth)(
        IWMPNetwork *This,
        LONG lMaxBandwidth);

    HRESULT (STDMETHODCALLTYPE *get_downloadProgress)(
        IWMPNetwork *This,
        LONG *plDownloadProgress);

    HRESULT (STDMETHODCALLTYPE *get_encodedFrameRate)(
        IWMPNetwork *This,
        LONG *plFrameRate);

    HRESULT (STDMETHODCALLTYPE *get_framesSkipped)(
        IWMPNetwork *This,
        LONG *plFrames);

    END_INTERFACE
} IWMPNetworkVtbl;

interface IWMPNetwork {
    CONST_VTBL IWMPNetworkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPNetwork_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPNetwork_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPNetwork_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPNetwork_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPNetwork_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPNetwork_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPNetwork_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPNetwork methods ***/
#define IWMPNetwork_get_bandWidth(This,plBandwidth) (This)->lpVtbl->get_bandWidth(This,plBandwidth)
#define IWMPNetwork_get_recoveredPackets(This,plRecoveredPackets) (This)->lpVtbl->get_recoveredPackets(This,plRecoveredPackets)
#define IWMPNetwork_get_sourceProtocol(This,pbstrSourceProtocol) (This)->lpVtbl->get_sourceProtocol(This,pbstrSourceProtocol)
#define IWMPNetwork_get_receivedPackets(This,plReceivedPackets) (This)->lpVtbl->get_receivedPackets(This,plReceivedPackets)
#define IWMPNetwork_get_lostPackets(This,plLostPackets) (This)->lpVtbl->get_lostPackets(This,plLostPackets)
#define IWMPNetwork_get_receptionQuality(This,plReceptionQuality) (This)->lpVtbl->get_receptionQuality(This,plReceptionQuality)
#define IWMPNetwork_get_bufferingCount(This,plBufferingCount) (This)->lpVtbl->get_bufferingCount(This,plBufferingCount)
#define IWMPNetwork_get_bufferingProgress(This,plBufferingProgress) (This)->lpVtbl->get_bufferingProgress(This,plBufferingProgress)
#define IWMPNetwork_get_bufferingTime(This,plBufferingTime) (This)->lpVtbl->get_bufferingTime(This,plBufferingTime)
#define IWMPNetwork_put_bufferingTime(This,plBufferingTime) (This)->lpVtbl->put_bufferingTime(This,plBufferingTime)
#define IWMPNetwork_get_frameRate(This,plFrameRate) (This)->lpVtbl->get_frameRate(This,plFrameRate)
#define IWMPNetwork_get_maxBitRate(This,plBitRate) (This)->lpVtbl->get_maxBitRate(This,plBitRate)
#define IWMPNetwork_get_bitRate(This,plBitRate) (This)->lpVtbl->get_bitRate(This,plBitRate)
#define IWMPNetwork_getProxySettings(This,bstrProtocol,plProxySetting) (This)->lpVtbl->getProxySettings(This,bstrProtocol,plProxySetting)
#define IWMPNetwork_setProxySettings(This,bstrProtocol,lProxySetting) (This)->lpVtbl->setProxySettings(This,bstrProtocol,lProxySetting)
#define IWMPNetwork_getProxyName(This,bstrProtocol,pbstrProxyName) (This)->lpVtbl->getProxyName(This,bstrProtocol,pbstrProxyName)
#define IWMPNetwork_setProxyName(This,bstrProtocol,bstrProxyName) (This)->lpVtbl->setProxyName(This,bstrProtocol,bstrProxyName)
#define IWMPNetwork_getProxyPort(This,bstrProtocol,lProxyPort) (This)->lpVtbl->getProxyPort(This,bstrProtocol,lProxyPort)
#define IWMPNetwork_setProxyPort(This,bstrProtocol,lProxyPort) (This)->lpVtbl->setProxyPort(This,bstrProtocol,lProxyPort)
#define IWMPNetwork_getProxyExceptionList(This,bstrProtocol,pbstrExceptionList) (This)->lpVtbl->getProxyExceptionList(This,bstrProtocol,pbstrExceptionList)
#define IWMPNetwork_setProxyExceptionList(This,bstrProtocol,pbstrExceptionList) (This)->lpVtbl->setProxyExceptionList(This,bstrProtocol,pbstrExceptionList)
#define IWMPNetwork_getProxyBypassForLocal(This,bstrProtocol,pfBypassForLocal) (This)->lpVtbl->getProxyBypassForLocal(This,bstrProtocol,pfBypassForLocal)
#define IWMPNetwork_setProxyBypassForLocal(This,bstrProtocol,fBypassForLocal) (This)->lpVtbl->setProxyBypassForLocal(This,bstrProtocol,fBypassForLocal)
#define IWMPNetwork_get_maxBandwidth(This,lMaxBandwidth) (This)->lpVtbl->get_maxBandwidth(This,lMaxBandwidth)
#define IWMPNetwork_put_maxBandwidth(This,lMaxBandwidth) (This)->lpVtbl->put_maxBandwidth(This,lMaxBandwidth)
#define IWMPNetwork_get_downloadProgress(This,plDownloadProgress) (This)->lpVtbl->get_downloadProgress(This,plDownloadProgress)
#define IWMPNetwork_get_encodedFrameRate(This,plFrameRate) (This)->lpVtbl->get_encodedFrameRate(This,plFrameRate)
#define IWMPNetwork_get_framesSkipped(This,plFrames) (This)->lpVtbl->get_framesSkipped(This,plFrames)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPNetwork_QueryInterface(IWMPNetwork* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPNetwork_AddRef(IWMPNetwork* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPNetwork_Release(IWMPNetwork* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPNetwork_GetTypeInfoCount(IWMPNetwork* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPNetwork_GetTypeInfo(IWMPNetwork* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPNetwork_GetIDsOfNames(IWMPNetwork* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPNetwork_Invoke(IWMPNetwork* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPNetwork methods ***/
static inline HRESULT IWMPNetwork_get_bandWidth(IWMPNetwork* This,LONG *plBandwidth) {
    return This->lpVtbl->get_bandWidth(This,plBandwidth);
}
static inline HRESULT IWMPNetwork_get_recoveredPackets(IWMPNetwork* This,LONG *plRecoveredPackets) {
    return This->lpVtbl->get_recoveredPackets(This,plRecoveredPackets);
}
static inline HRESULT IWMPNetwork_get_sourceProtocol(IWMPNetwork* This,BSTR *pbstrSourceProtocol) {
    return This->lpVtbl->get_sourceProtocol(This,pbstrSourceProtocol);
}
static inline HRESULT IWMPNetwork_get_receivedPackets(IWMPNetwork* This,LONG *plReceivedPackets) {
    return This->lpVtbl->get_receivedPackets(This,plReceivedPackets);
}
static inline HRESULT IWMPNetwork_get_lostPackets(IWMPNetwork* This,LONG *plLostPackets) {
    return This->lpVtbl->get_lostPackets(This,plLostPackets);
}
static inline HRESULT IWMPNetwork_get_receptionQuality(IWMPNetwork* This,LONG *plReceptionQuality) {
    return This->lpVtbl->get_receptionQuality(This,plReceptionQuality);
}
static inline HRESULT IWMPNetwork_get_bufferingCount(IWMPNetwork* This,LONG *plBufferingCount) {
    return This->lpVtbl->get_bufferingCount(This,plBufferingCount);
}
static inline HRESULT IWMPNetwork_get_bufferingProgress(IWMPNetwork* This,LONG *plBufferingProgress) {
    return This->lpVtbl->get_bufferingProgress(This,plBufferingProgress);
}
static inline HRESULT IWMPNetwork_get_bufferingTime(IWMPNetwork* This,LONG *plBufferingTime) {
    return This->lpVtbl->get_bufferingTime(This,plBufferingTime);
}
static inline HRESULT IWMPNetwork_put_bufferingTime(IWMPNetwork* This,LONG plBufferingTime) {
    return This->lpVtbl->put_bufferingTime(This,plBufferingTime);
}
static inline HRESULT IWMPNetwork_get_frameRate(IWMPNetwork* This,LONG *plFrameRate) {
    return This->lpVtbl->get_frameRate(This,plFrameRate);
}
static inline HRESULT IWMPNetwork_get_maxBitRate(IWMPNetwork* This,LONG *plBitRate) {
    return This->lpVtbl->get_maxBitRate(This,plBitRate);
}
static inline HRESULT IWMPNetwork_get_bitRate(IWMPNetwork* This,LONG *plBitRate) {
    return This->lpVtbl->get_bitRate(This,plBitRate);
}
static inline HRESULT IWMPNetwork_getProxySettings(IWMPNetwork* This,BSTR bstrProtocol,LONG *plProxySetting) {
    return This->lpVtbl->getProxySettings(This,bstrProtocol,plProxySetting);
}
static inline HRESULT IWMPNetwork_setProxySettings(IWMPNetwork* This,BSTR bstrProtocol,LONG lProxySetting) {
    return This->lpVtbl->setProxySettings(This,bstrProtocol,lProxySetting);
}
static inline HRESULT IWMPNetwork_getProxyName(IWMPNetwork* This,BSTR bstrProtocol,BSTR *pbstrProxyName) {
    return This->lpVtbl->getProxyName(This,bstrProtocol,pbstrProxyName);
}
static inline HRESULT IWMPNetwork_setProxyName(IWMPNetwork* This,BSTR bstrProtocol,BSTR bstrProxyName) {
    return This->lpVtbl->setProxyName(This,bstrProtocol,bstrProxyName);
}
static inline HRESULT IWMPNetwork_getProxyPort(IWMPNetwork* This,BSTR bstrProtocol,LONG *lProxyPort) {
    return This->lpVtbl->getProxyPort(This,bstrProtocol,lProxyPort);
}
static inline HRESULT IWMPNetwork_setProxyPort(IWMPNetwork* This,BSTR bstrProtocol,LONG lProxyPort) {
    return This->lpVtbl->setProxyPort(This,bstrProtocol,lProxyPort);
}
static inline HRESULT IWMPNetwork_getProxyExceptionList(IWMPNetwork* This,BSTR bstrProtocol,BSTR *pbstrExceptionList) {
    return This->lpVtbl->getProxyExceptionList(This,bstrProtocol,pbstrExceptionList);
}
static inline HRESULT IWMPNetwork_setProxyExceptionList(IWMPNetwork* This,BSTR bstrProtocol,BSTR pbstrExceptionList) {
    return This->lpVtbl->setProxyExceptionList(This,bstrProtocol,pbstrExceptionList);
}
static inline HRESULT IWMPNetwork_getProxyBypassForLocal(IWMPNetwork* This,BSTR bstrProtocol,VARIANT_BOOL *pfBypassForLocal) {
    return This->lpVtbl->getProxyBypassForLocal(This,bstrProtocol,pfBypassForLocal);
}
static inline HRESULT IWMPNetwork_setProxyBypassForLocal(IWMPNetwork* This,BSTR bstrProtocol,VARIANT_BOOL fBypassForLocal) {
    return This->lpVtbl->setProxyBypassForLocal(This,bstrProtocol,fBypassForLocal);
}
static inline HRESULT IWMPNetwork_get_maxBandwidth(IWMPNetwork* This,LONG *lMaxBandwidth) {
    return This->lpVtbl->get_maxBandwidth(This,lMaxBandwidth);
}
static inline HRESULT IWMPNetwork_put_maxBandwidth(IWMPNetwork* This,LONG lMaxBandwidth) {
    return This->lpVtbl->put_maxBandwidth(This,lMaxBandwidth);
}
static inline HRESULT IWMPNetwork_get_downloadProgress(IWMPNetwork* This,LONG *plDownloadProgress) {
    return This->lpVtbl->get_downloadProgress(This,plDownloadProgress);
}
static inline HRESULT IWMPNetwork_get_encodedFrameRate(IWMPNetwork* This,LONG *plFrameRate) {
    return This->lpVtbl->get_encodedFrameRate(This,plFrameRate);
}
static inline HRESULT IWMPNetwork_get_framesSkipped(IWMPNetwork* This,LONG *plFrames) {
    return This->lpVtbl->get_framesSkipped(This,plFrames);
}
#endif
#endif

#endif


#endif  /* __IWMPNetwork_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPCdrom interface
 */
#ifndef __IWMPCdrom_INTERFACE_DEFINED__
#define __IWMPCdrom_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPCdrom, 0xcfab6e98, 0x8730, 0x11d3, 0xb3,0x88, 0x00,0xc0,0x4f,0x68,0x57,0x4b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cfab6e98-8730-11d3-b388-00c04f68574b")
IWMPCdrom : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_driveSpecifier(
        BSTR *pbstrDrive) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Playlist(
        IWMPPlaylist **ppPlaylist) = 0;

    virtual HRESULT STDMETHODCALLTYPE eject(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPCdrom, 0xcfab6e98, 0x8730, 0x11d3, 0xb3,0x88, 0x00,0xc0,0x4f,0x68,0x57,0x4b)
#endif
#else
typedef struct IWMPCdromVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPCdrom *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPCdrom *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPCdrom *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPCdrom *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPCdrom *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPCdrom *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPCdrom *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPCdrom methods ***/
    HRESULT (STDMETHODCALLTYPE *get_driveSpecifier)(
        IWMPCdrom *This,
        BSTR *pbstrDrive);

    HRESULT (STDMETHODCALLTYPE *get_Playlist)(
        IWMPCdrom *This,
        IWMPPlaylist **ppPlaylist);

    HRESULT (STDMETHODCALLTYPE *eject)(
        IWMPCdrom *This);

    END_INTERFACE
} IWMPCdromVtbl;

interface IWMPCdrom {
    CONST_VTBL IWMPCdromVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPCdrom_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPCdrom_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPCdrom_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPCdrom_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPCdrom_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPCdrom_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPCdrom_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPCdrom methods ***/
#define IWMPCdrom_get_driveSpecifier(This,pbstrDrive) (This)->lpVtbl->get_driveSpecifier(This,pbstrDrive)
#define IWMPCdrom_get_Playlist(This,ppPlaylist) (This)->lpVtbl->get_Playlist(This,ppPlaylist)
#define IWMPCdrom_eject(This) (This)->lpVtbl->eject(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPCdrom_QueryInterface(IWMPCdrom* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPCdrom_AddRef(IWMPCdrom* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPCdrom_Release(IWMPCdrom* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPCdrom_GetTypeInfoCount(IWMPCdrom* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPCdrom_GetTypeInfo(IWMPCdrom* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPCdrom_GetIDsOfNames(IWMPCdrom* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPCdrom_Invoke(IWMPCdrom* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPCdrom methods ***/
static inline HRESULT IWMPCdrom_get_driveSpecifier(IWMPCdrom* This,BSTR *pbstrDrive) {
    return This->lpVtbl->get_driveSpecifier(This,pbstrDrive);
}
static inline HRESULT IWMPCdrom_get_Playlist(IWMPCdrom* This,IWMPPlaylist **ppPlaylist) {
    return This->lpVtbl->get_Playlist(This,ppPlaylist);
}
static inline HRESULT IWMPCdrom_eject(IWMPCdrom* This) {
    return This->lpVtbl->eject(This);
}
#endif
#endif

#endif


#endif  /* __IWMPCdrom_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPCdromCollection interface
 */
#ifndef __IWMPCdromCollection_INTERFACE_DEFINED__
#define __IWMPCdromCollection_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPCdromCollection, 0xee4c8fe2, 0x34b2, 0x11d3, 0xa3,0xbf, 0x00,0x60,0x97,0xc9,0xb3,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ee4c8fe2-34b2-11d3-a3bf-006097c9b344")
IWMPCdromCollection : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_count(
        LONG *plCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE Item(
        LONG lIndex,
        IWMPCdrom **ppItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE getByDriveSpecifier(
        BSTR bstrDriveSpecifier,
        IWMPCdrom **ppCdrom) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPCdromCollection, 0xee4c8fe2, 0x34b2, 0x11d3, 0xa3,0xbf, 0x00,0x60,0x97,0xc9,0xb3,0x44)
#endif
#else
typedef struct IWMPCdromCollectionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPCdromCollection *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPCdromCollection *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPCdromCollection *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPCdromCollection *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPCdromCollection *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPCdromCollection *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPCdromCollection *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPCdromCollection methods ***/
    HRESULT (STDMETHODCALLTYPE *get_count)(
        IWMPCdromCollection *This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *Item)(
        IWMPCdromCollection *This,
        LONG lIndex,
        IWMPCdrom **ppItem);

    HRESULT (STDMETHODCALLTYPE *getByDriveSpecifier)(
        IWMPCdromCollection *This,
        BSTR bstrDriveSpecifier,
        IWMPCdrom **ppCdrom);

    END_INTERFACE
} IWMPCdromCollectionVtbl;

interface IWMPCdromCollection {
    CONST_VTBL IWMPCdromCollectionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPCdromCollection_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPCdromCollection_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPCdromCollection_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPCdromCollection_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPCdromCollection_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPCdromCollection_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPCdromCollection_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPCdromCollection methods ***/
#define IWMPCdromCollection_get_count(This,plCount) (This)->lpVtbl->get_count(This,plCount)
#define IWMPCdromCollection_Item(This,lIndex,ppItem) (This)->lpVtbl->Item(This,lIndex,ppItem)
#define IWMPCdromCollection_getByDriveSpecifier(This,bstrDriveSpecifier,ppCdrom) (This)->lpVtbl->getByDriveSpecifier(This,bstrDriveSpecifier,ppCdrom)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPCdromCollection_QueryInterface(IWMPCdromCollection* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPCdromCollection_AddRef(IWMPCdromCollection* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPCdromCollection_Release(IWMPCdromCollection* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPCdromCollection_GetTypeInfoCount(IWMPCdromCollection* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPCdromCollection_GetTypeInfo(IWMPCdromCollection* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPCdromCollection_GetIDsOfNames(IWMPCdromCollection* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPCdromCollection_Invoke(IWMPCdromCollection* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPCdromCollection methods ***/
static inline HRESULT IWMPCdromCollection_get_count(IWMPCdromCollection* This,LONG *plCount) {
    return This->lpVtbl->get_count(This,plCount);
}
static inline HRESULT IWMPCdromCollection_Item(IWMPCdromCollection* This,LONG lIndex,IWMPCdrom **ppItem) {
    return This->lpVtbl->Item(This,lIndex,ppItem);
}
static inline HRESULT IWMPCdromCollection_getByDriveSpecifier(IWMPCdromCollection* This,BSTR bstrDriveSpecifier,IWMPCdrom **ppCdrom) {
    return This->lpVtbl->getByDriveSpecifier(This,bstrDriveSpecifier,ppCdrom);
}
#endif
#endif

#endif


#endif  /* __IWMPCdromCollection_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPDVD interface
 */
#ifndef __IWMPDVD_INTERFACE_DEFINED__
#define __IWMPDVD_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPDVD, 0x8da61686, 0x4668, 0x4a5c, 0xae,0x5d, 0x80,0x31,0x93,0x29,0x3d,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8da61686-4668-4a5c-ae5d-803193293dbe")
IWMPDVD : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_isAvailable(
        BSTR bstrItem,
        VARIANT_BOOL *pIsAvailable) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_domain(
        BSTR *strDomain) = 0;

    virtual HRESULT STDMETHODCALLTYPE topMenu(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE titleMenu(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE back(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE resume(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPDVD, 0x8da61686, 0x4668, 0x4a5c, 0xae,0x5d, 0x80,0x31,0x93,0x29,0x3d,0xbe)
#endif
#else
typedef struct IWMPDVDVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPDVD *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPDVD *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPDVD *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPDVD *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPDVD *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPDVD *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPDVD *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPDVD methods ***/
    HRESULT (STDMETHODCALLTYPE *get_isAvailable)(
        IWMPDVD *This,
        BSTR bstrItem,
        VARIANT_BOOL *pIsAvailable);

    HRESULT (STDMETHODCALLTYPE *get_domain)(
        IWMPDVD *This,
        BSTR *strDomain);

    HRESULT (STDMETHODCALLTYPE *topMenu)(
        IWMPDVD *This);

    HRESULT (STDMETHODCALLTYPE *titleMenu)(
        IWMPDVD *This);

    HRESULT (STDMETHODCALLTYPE *back)(
        IWMPDVD *This);

    HRESULT (STDMETHODCALLTYPE *resume)(
        IWMPDVD *This);

    END_INTERFACE
} IWMPDVDVtbl;

interface IWMPDVD {
    CONST_VTBL IWMPDVDVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPDVD_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPDVD_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPDVD_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPDVD_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPDVD_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPDVD_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPDVD_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPDVD methods ***/
#define IWMPDVD_get_isAvailable(This,bstrItem,pIsAvailable) (This)->lpVtbl->get_isAvailable(This,bstrItem,pIsAvailable)
#define IWMPDVD_get_domain(This,strDomain) (This)->lpVtbl->get_domain(This,strDomain)
#define IWMPDVD_topMenu(This) (This)->lpVtbl->topMenu(This)
#define IWMPDVD_titleMenu(This) (This)->lpVtbl->titleMenu(This)
#define IWMPDVD_back(This) (This)->lpVtbl->back(This)
#define IWMPDVD_resume(This) (This)->lpVtbl->resume(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPDVD_QueryInterface(IWMPDVD* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPDVD_AddRef(IWMPDVD* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPDVD_Release(IWMPDVD* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPDVD_GetTypeInfoCount(IWMPDVD* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPDVD_GetTypeInfo(IWMPDVD* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPDVD_GetIDsOfNames(IWMPDVD* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPDVD_Invoke(IWMPDVD* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPDVD methods ***/
static inline HRESULT IWMPDVD_get_isAvailable(IWMPDVD* This,BSTR bstrItem,VARIANT_BOOL *pIsAvailable) {
    return This->lpVtbl->get_isAvailable(This,bstrItem,pIsAvailable);
}
static inline HRESULT IWMPDVD_get_domain(IWMPDVD* This,BSTR *strDomain) {
    return This->lpVtbl->get_domain(This,strDomain);
}
static inline HRESULT IWMPDVD_topMenu(IWMPDVD* This) {
    return This->lpVtbl->topMenu(This);
}
static inline HRESULT IWMPDVD_titleMenu(IWMPDVD* This) {
    return This->lpVtbl->titleMenu(This);
}
static inline HRESULT IWMPDVD_back(IWMPDVD* This) {
    return This->lpVtbl->back(This);
}
static inline HRESULT IWMPDVD_resume(IWMPDVD* This) {
    return This->lpVtbl->resume(This);
}
#endif
#endif

#endif


#endif  /* __IWMPDVD_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPClosedCaption interface
 */
#ifndef __IWMPClosedCaption_INTERFACE_DEFINED__
#define __IWMPClosedCaption_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPClosedCaption, 0x4f2df574, 0xc588, 0x11d3, 0x9e,0xd0, 0x00,0xc0,0x4f,0xb6,0xe9,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4f2df574-c588-11d3-9ed0-00c04fb6e937")
IWMPClosedCaption : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_SAMIStyle(
        BSTR *pbstrSAMIStyle) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SAMIStyle(
        BSTR pbstrSAMIStyle) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SAMILang(
        BSTR *pbstrSAMILang) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SAMILang(
        BSTR pbstrSAMILang) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_SAMIFileName(
        BSTR *pbstrSAMIFileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_SAMIFileName(
        BSTR pbstrSAMIFileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_captioningId(
        BSTR *pbstrCaptioningID) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_captioningId(
        BSTR pbstrCaptioningID) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPClosedCaption, 0x4f2df574, 0xc588, 0x11d3, 0x9e,0xd0, 0x00,0xc0,0x4f,0xb6,0xe9,0x37)
#endif
#else
typedef struct IWMPClosedCaptionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPClosedCaption *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPClosedCaption *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPClosedCaption *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPClosedCaption *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPClosedCaption *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPClosedCaption *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPClosedCaption *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPClosedCaption methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SAMIStyle)(
        IWMPClosedCaption *This,
        BSTR *pbstrSAMIStyle);

    HRESULT (STDMETHODCALLTYPE *put_SAMIStyle)(
        IWMPClosedCaption *This,
        BSTR pbstrSAMIStyle);

    HRESULT (STDMETHODCALLTYPE *get_SAMILang)(
        IWMPClosedCaption *This,
        BSTR *pbstrSAMILang);

    HRESULT (STDMETHODCALLTYPE *put_SAMILang)(
        IWMPClosedCaption *This,
        BSTR pbstrSAMILang);

    HRESULT (STDMETHODCALLTYPE *get_SAMIFileName)(
        IWMPClosedCaption *This,
        BSTR *pbstrSAMIFileName);

    HRESULT (STDMETHODCALLTYPE *put_SAMIFileName)(
        IWMPClosedCaption *This,
        BSTR pbstrSAMIFileName);

    HRESULT (STDMETHODCALLTYPE *get_captioningId)(
        IWMPClosedCaption *This,
        BSTR *pbstrCaptioningID);

    HRESULT (STDMETHODCALLTYPE *put_captioningId)(
        IWMPClosedCaption *This,
        BSTR pbstrCaptioningID);

    END_INTERFACE
} IWMPClosedCaptionVtbl;

interface IWMPClosedCaption {
    CONST_VTBL IWMPClosedCaptionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPClosedCaption_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPClosedCaption_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPClosedCaption_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPClosedCaption_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPClosedCaption_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPClosedCaption_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPClosedCaption_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPClosedCaption methods ***/
#define IWMPClosedCaption_get_SAMIStyle(This,pbstrSAMIStyle) (This)->lpVtbl->get_SAMIStyle(This,pbstrSAMIStyle)
#define IWMPClosedCaption_put_SAMIStyle(This,pbstrSAMIStyle) (This)->lpVtbl->put_SAMIStyle(This,pbstrSAMIStyle)
#define IWMPClosedCaption_get_SAMILang(This,pbstrSAMILang) (This)->lpVtbl->get_SAMILang(This,pbstrSAMILang)
#define IWMPClosedCaption_put_SAMILang(This,pbstrSAMILang) (This)->lpVtbl->put_SAMILang(This,pbstrSAMILang)
#define IWMPClosedCaption_get_SAMIFileName(This,pbstrSAMIFileName) (This)->lpVtbl->get_SAMIFileName(This,pbstrSAMIFileName)
#define IWMPClosedCaption_put_SAMIFileName(This,pbstrSAMIFileName) (This)->lpVtbl->put_SAMIFileName(This,pbstrSAMIFileName)
#define IWMPClosedCaption_get_captioningId(This,pbstrCaptioningID) (This)->lpVtbl->get_captioningId(This,pbstrCaptioningID)
#define IWMPClosedCaption_put_captioningId(This,pbstrCaptioningID) (This)->lpVtbl->put_captioningId(This,pbstrCaptioningID)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPClosedCaption_QueryInterface(IWMPClosedCaption* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPClosedCaption_AddRef(IWMPClosedCaption* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPClosedCaption_Release(IWMPClosedCaption* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPClosedCaption_GetTypeInfoCount(IWMPClosedCaption* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPClosedCaption_GetTypeInfo(IWMPClosedCaption* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPClosedCaption_GetIDsOfNames(IWMPClosedCaption* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPClosedCaption_Invoke(IWMPClosedCaption* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPClosedCaption methods ***/
static inline HRESULT IWMPClosedCaption_get_SAMIStyle(IWMPClosedCaption* This,BSTR *pbstrSAMIStyle) {
    return This->lpVtbl->get_SAMIStyle(This,pbstrSAMIStyle);
}
static inline HRESULT IWMPClosedCaption_put_SAMIStyle(IWMPClosedCaption* This,BSTR pbstrSAMIStyle) {
    return This->lpVtbl->put_SAMIStyle(This,pbstrSAMIStyle);
}
static inline HRESULT IWMPClosedCaption_get_SAMILang(IWMPClosedCaption* This,BSTR *pbstrSAMILang) {
    return This->lpVtbl->get_SAMILang(This,pbstrSAMILang);
}
static inline HRESULT IWMPClosedCaption_put_SAMILang(IWMPClosedCaption* This,BSTR pbstrSAMILang) {
    return This->lpVtbl->put_SAMILang(This,pbstrSAMILang);
}
static inline HRESULT IWMPClosedCaption_get_SAMIFileName(IWMPClosedCaption* This,BSTR *pbstrSAMIFileName) {
    return This->lpVtbl->get_SAMIFileName(This,pbstrSAMIFileName);
}
static inline HRESULT IWMPClosedCaption_put_SAMIFileName(IWMPClosedCaption* This,BSTR pbstrSAMIFileName) {
    return This->lpVtbl->put_SAMIFileName(This,pbstrSAMIFileName);
}
static inline HRESULT IWMPClosedCaption_get_captioningId(IWMPClosedCaption* This,BSTR *pbstrCaptioningID) {
    return This->lpVtbl->get_captioningId(This,pbstrCaptioningID);
}
static inline HRESULT IWMPClosedCaption_put_captioningId(IWMPClosedCaption* This,BSTR pbstrCaptioningID) {
    return This->lpVtbl->put_captioningId(This,pbstrCaptioningID);
}
#endif
#endif

#endif


#endif  /* __IWMPClosedCaption_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPErrorItem interface
 */
#ifndef __IWMPErrorItem_INTERFACE_DEFINED__
#define __IWMPErrorItem_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPErrorItem, 0x3614c646, 0x3b3b, 0x4de7, 0xa8,0x1e, 0x93,0x0e,0x3f,0x21,0x27,0xb3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3614c646-3b3b-4de7-a81e-930e3f2127b3")
IWMPErrorItem : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE get_errorCode(
        LONG *phr) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_errorDescription(
        BSTR *pbstrDescription) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_errorContext(
        VARIANT *pvarContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_remedy(
        LONG *plRemedy) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_customUrl(
        BSTR *pbstrCustomUrl) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPErrorItem, 0x3614c646, 0x3b3b, 0x4de7, 0xa8,0x1e, 0x93,0x0e,0x3f,0x21,0x27,0xb3)
#endif
#else
typedef struct IWMPErrorItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPErrorItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPErrorItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPErrorItem *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPErrorItem *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPErrorItem *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPErrorItem *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPErrorItem *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPErrorItem methods ***/
    HRESULT (STDMETHODCALLTYPE *get_errorCode)(
        IWMPErrorItem *This,
        LONG *phr);

    HRESULT (STDMETHODCALLTYPE *get_errorDescription)(
        IWMPErrorItem *This,
        BSTR *pbstrDescription);

    HRESULT (STDMETHODCALLTYPE *get_errorContext)(
        IWMPErrorItem *This,
        VARIANT *pvarContext);

    HRESULT (STDMETHODCALLTYPE *get_remedy)(
        IWMPErrorItem *This,
        LONG *plRemedy);

    HRESULT (STDMETHODCALLTYPE *get_customUrl)(
        IWMPErrorItem *This,
        BSTR *pbstrCustomUrl);

    END_INTERFACE
} IWMPErrorItemVtbl;

interface IWMPErrorItem {
    CONST_VTBL IWMPErrorItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPErrorItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPErrorItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPErrorItem_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPErrorItem_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPErrorItem_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPErrorItem_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPErrorItem_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPErrorItem methods ***/
#define IWMPErrorItem_get_errorCode(This,phr) (This)->lpVtbl->get_errorCode(This,phr)
#define IWMPErrorItem_get_errorDescription(This,pbstrDescription) (This)->lpVtbl->get_errorDescription(This,pbstrDescription)
#define IWMPErrorItem_get_errorContext(This,pvarContext) (This)->lpVtbl->get_errorContext(This,pvarContext)
#define IWMPErrorItem_get_remedy(This,plRemedy) (This)->lpVtbl->get_remedy(This,plRemedy)
#define IWMPErrorItem_get_customUrl(This,pbstrCustomUrl) (This)->lpVtbl->get_customUrl(This,pbstrCustomUrl)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPErrorItem_QueryInterface(IWMPErrorItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPErrorItem_AddRef(IWMPErrorItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPErrorItem_Release(IWMPErrorItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPErrorItem_GetTypeInfoCount(IWMPErrorItem* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPErrorItem_GetTypeInfo(IWMPErrorItem* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPErrorItem_GetIDsOfNames(IWMPErrorItem* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPErrorItem_Invoke(IWMPErrorItem* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPErrorItem methods ***/
static inline HRESULT IWMPErrorItem_get_errorCode(IWMPErrorItem* This,LONG *phr) {
    return This->lpVtbl->get_errorCode(This,phr);
}
static inline HRESULT IWMPErrorItem_get_errorDescription(IWMPErrorItem* This,BSTR *pbstrDescription) {
    return This->lpVtbl->get_errorDescription(This,pbstrDescription);
}
static inline HRESULT IWMPErrorItem_get_errorContext(IWMPErrorItem* This,VARIANT *pvarContext) {
    return This->lpVtbl->get_errorContext(This,pvarContext);
}
static inline HRESULT IWMPErrorItem_get_remedy(IWMPErrorItem* This,LONG *plRemedy) {
    return This->lpVtbl->get_remedy(This,plRemedy);
}
static inline HRESULT IWMPErrorItem_get_customUrl(IWMPErrorItem* This,BSTR *pbstrCustomUrl) {
    return This->lpVtbl->get_customUrl(This,pbstrCustomUrl);
}
#endif
#endif

#endif


#endif  /* __IWMPErrorItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPMedia2 interface
 */
#ifndef __IWMPMedia2_INTERFACE_DEFINED__
#define __IWMPMedia2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPMedia2, 0xab7c88bb, 0x143e, 0x4ea4, 0xac,0xc3, 0xe4,0x35,0x0b,0x21,0x06,0xc3);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ab7c88bb-143e-4ea4-acc3-e4350b2106c3")
IWMPMedia2 : public IWMPMedia
{
    virtual HRESULT STDMETHODCALLTYPE get_error(
        IWMPErrorItem **out) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPMedia2, 0xab7c88bb, 0x143e, 0x4ea4, 0xac,0xc3, 0xe4,0x35,0x0b,0x21,0x06,0xc3)
#endif
#else
typedef struct IWMPMedia2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPMedia2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPMedia2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPMedia2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPMedia2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPMedia2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPMedia2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPMedia2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPMedia methods ***/
    HRESULT (STDMETHODCALLTYPE *get_isIdentical)(
        IWMPMedia2 *This,
        IWMPMedia *pIWMPMedia,
        VARIANT_BOOL *pvbool);

    HRESULT (STDMETHODCALLTYPE *get_sourceURL)(
        IWMPMedia2 *This,
        BSTR *pbstrSourceURL);

    HRESULT (STDMETHODCALLTYPE *get_name)(
        IWMPMedia2 *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *put_name)(
        IWMPMedia2 *This,
        BSTR pbstrName);

    HRESULT (STDMETHODCALLTYPE *get_imageSourceWidth)(
        IWMPMedia2 *This,
        LONG *pWidth);

    HRESULT (STDMETHODCALLTYPE *get_imageSourceHeight)(
        IWMPMedia2 *This,
        LONG *pHeight);

    HRESULT (STDMETHODCALLTYPE *get_markerCount)(
        IWMPMedia2 *This,
        LONG *pMarkerCount);

    HRESULT (STDMETHODCALLTYPE *getMarkerTime)(
        IWMPMedia2 *This,
        LONG MarkerNum,
        double *pMarkerTime);

    HRESULT (STDMETHODCALLTYPE *getMarkerName)(
        IWMPMedia2 *This,
        LONG MarkerNum,
        BSTR *pbstrMarkerName);

    HRESULT (STDMETHODCALLTYPE *get_duration)(
        IWMPMedia2 *This,
        double *pDuration);

    HRESULT (STDMETHODCALLTYPE *get_durationString)(
        IWMPMedia2 *This,
        BSTR *pbstrDuration);

    HRESULT (STDMETHODCALLTYPE *get_attributeCount)(
        IWMPMedia2 *This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *getAttributeName)(
        IWMPMedia2 *This,
        LONG lIndex,
        BSTR *pbstrItemName);

    HRESULT (STDMETHODCALLTYPE *getItemInfo)(
        IWMPMedia2 *This,
        BSTR bstrItemName,
        BSTR *pbstrVal);

    HRESULT (STDMETHODCALLTYPE *setItemInfo)(
        IWMPMedia2 *This,
        BSTR bstrItemName,
        BSTR bstrVal);

    HRESULT (STDMETHODCALLTYPE *getItemInfoByAtom)(
        IWMPMedia2 *This,
        LONG lAtom,
        BSTR *pbstrVal);

    HRESULT (STDMETHODCALLTYPE *isMemberOf)(
        IWMPMedia2 *This,
        IWMPPlaylist *pPlaylist,
        VARIANT_BOOL *pvarfIsMemberOf);

    HRESULT (STDMETHODCALLTYPE *isReadOnlyItem)(
        IWMPMedia2 *This,
        BSTR bstrItemName,
        VARIANT_BOOL *pvarfIsReadOnly);

    /*** IWMPMedia2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_error)(
        IWMPMedia2 *This,
        IWMPErrorItem **out);

    END_INTERFACE
} IWMPMedia2Vtbl;

interface IWMPMedia2 {
    CONST_VTBL IWMPMedia2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPMedia2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPMedia2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPMedia2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPMedia2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPMedia2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPMedia2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPMedia2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPMedia methods ***/
#define IWMPMedia2_get_isIdentical(This,pIWMPMedia,pvbool) (This)->lpVtbl->get_isIdentical(This,pIWMPMedia,pvbool)
#define IWMPMedia2_get_sourceURL(This,pbstrSourceURL) (This)->lpVtbl->get_sourceURL(This,pbstrSourceURL)
#define IWMPMedia2_get_name(This,pbstrName) (This)->lpVtbl->get_name(This,pbstrName)
#define IWMPMedia2_put_name(This,pbstrName) (This)->lpVtbl->put_name(This,pbstrName)
#define IWMPMedia2_get_imageSourceWidth(This,pWidth) (This)->lpVtbl->get_imageSourceWidth(This,pWidth)
#define IWMPMedia2_get_imageSourceHeight(This,pHeight) (This)->lpVtbl->get_imageSourceHeight(This,pHeight)
#define IWMPMedia2_get_markerCount(This,pMarkerCount) (This)->lpVtbl->get_markerCount(This,pMarkerCount)
#define IWMPMedia2_getMarkerTime(This,MarkerNum,pMarkerTime) (This)->lpVtbl->getMarkerTime(This,MarkerNum,pMarkerTime)
#define IWMPMedia2_getMarkerName(This,MarkerNum,pbstrMarkerName) (This)->lpVtbl->getMarkerName(This,MarkerNum,pbstrMarkerName)
#define IWMPMedia2_get_duration(This,pDuration) (This)->lpVtbl->get_duration(This,pDuration)
#define IWMPMedia2_get_durationString(This,pbstrDuration) (This)->lpVtbl->get_durationString(This,pbstrDuration)
#define IWMPMedia2_get_attributeCount(This,plCount) (This)->lpVtbl->get_attributeCount(This,plCount)
#define IWMPMedia2_getAttributeName(This,lIndex,pbstrItemName) (This)->lpVtbl->getAttributeName(This,lIndex,pbstrItemName)
#define IWMPMedia2_getItemInfo(This,bstrItemName,pbstrVal) (This)->lpVtbl->getItemInfo(This,bstrItemName,pbstrVal)
#define IWMPMedia2_setItemInfo(This,bstrItemName,bstrVal) (This)->lpVtbl->setItemInfo(This,bstrItemName,bstrVal)
#define IWMPMedia2_getItemInfoByAtom(This,lAtom,pbstrVal) (This)->lpVtbl->getItemInfoByAtom(This,lAtom,pbstrVal)
#define IWMPMedia2_isMemberOf(This,pPlaylist,pvarfIsMemberOf) (This)->lpVtbl->isMemberOf(This,pPlaylist,pvarfIsMemberOf)
#define IWMPMedia2_isReadOnlyItem(This,bstrItemName,pvarfIsReadOnly) (This)->lpVtbl->isReadOnlyItem(This,bstrItemName,pvarfIsReadOnly)
/*** IWMPMedia2 methods ***/
#define IWMPMedia2_get_error(This,out) (This)->lpVtbl->get_error(This,out)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPMedia2_QueryInterface(IWMPMedia2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPMedia2_AddRef(IWMPMedia2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPMedia2_Release(IWMPMedia2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPMedia2_GetTypeInfoCount(IWMPMedia2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPMedia2_GetTypeInfo(IWMPMedia2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPMedia2_GetIDsOfNames(IWMPMedia2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPMedia2_Invoke(IWMPMedia2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPMedia methods ***/
static inline HRESULT IWMPMedia2_get_isIdentical(IWMPMedia2* This,IWMPMedia *pIWMPMedia,VARIANT_BOOL *pvbool) {
    return This->lpVtbl->get_isIdentical(This,pIWMPMedia,pvbool);
}
static inline HRESULT IWMPMedia2_get_sourceURL(IWMPMedia2* This,BSTR *pbstrSourceURL) {
    return This->lpVtbl->get_sourceURL(This,pbstrSourceURL);
}
static inline HRESULT IWMPMedia2_get_name(IWMPMedia2* This,BSTR *pbstrName) {
    return This->lpVtbl->get_name(This,pbstrName);
}
static inline HRESULT IWMPMedia2_put_name(IWMPMedia2* This,BSTR pbstrName) {
    return This->lpVtbl->put_name(This,pbstrName);
}
static inline HRESULT IWMPMedia2_get_imageSourceWidth(IWMPMedia2* This,LONG *pWidth) {
    return This->lpVtbl->get_imageSourceWidth(This,pWidth);
}
static inline HRESULT IWMPMedia2_get_imageSourceHeight(IWMPMedia2* This,LONG *pHeight) {
    return This->lpVtbl->get_imageSourceHeight(This,pHeight);
}
static inline HRESULT IWMPMedia2_get_markerCount(IWMPMedia2* This,LONG *pMarkerCount) {
    return This->lpVtbl->get_markerCount(This,pMarkerCount);
}
static inline HRESULT IWMPMedia2_getMarkerTime(IWMPMedia2* This,LONG MarkerNum,double *pMarkerTime) {
    return This->lpVtbl->getMarkerTime(This,MarkerNum,pMarkerTime);
}
static inline HRESULT IWMPMedia2_getMarkerName(IWMPMedia2* This,LONG MarkerNum,BSTR *pbstrMarkerName) {
    return This->lpVtbl->getMarkerName(This,MarkerNum,pbstrMarkerName);
}
static inline HRESULT IWMPMedia2_get_duration(IWMPMedia2* This,double *pDuration) {
    return This->lpVtbl->get_duration(This,pDuration);
}
static inline HRESULT IWMPMedia2_get_durationString(IWMPMedia2* This,BSTR *pbstrDuration) {
    return This->lpVtbl->get_durationString(This,pbstrDuration);
}
static inline HRESULT IWMPMedia2_get_attributeCount(IWMPMedia2* This,LONG *plCount) {
    return This->lpVtbl->get_attributeCount(This,plCount);
}
static inline HRESULT IWMPMedia2_getAttributeName(IWMPMedia2* This,LONG lIndex,BSTR *pbstrItemName) {
    return This->lpVtbl->getAttributeName(This,lIndex,pbstrItemName);
}
static inline HRESULT IWMPMedia2_getItemInfo(IWMPMedia2* This,BSTR bstrItemName,BSTR *pbstrVal) {
    return This->lpVtbl->getItemInfo(This,bstrItemName,pbstrVal);
}
static inline HRESULT IWMPMedia2_setItemInfo(IWMPMedia2* This,BSTR bstrItemName,BSTR bstrVal) {
    return This->lpVtbl->setItemInfo(This,bstrItemName,bstrVal);
}
static inline HRESULT IWMPMedia2_getItemInfoByAtom(IWMPMedia2* This,LONG lAtom,BSTR *pbstrVal) {
    return This->lpVtbl->getItemInfoByAtom(This,lAtom,pbstrVal);
}
static inline HRESULT IWMPMedia2_isMemberOf(IWMPMedia2* This,IWMPPlaylist *pPlaylist,VARIANT_BOOL *pvarfIsMemberOf) {
    return This->lpVtbl->isMemberOf(This,pPlaylist,pvarfIsMemberOf);
}
static inline HRESULT IWMPMedia2_isReadOnlyItem(IWMPMedia2* This,BSTR bstrItemName,VARIANT_BOOL *pvarfIsReadOnly) {
    return This->lpVtbl->isReadOnlyItem(This,bstrItemName,pvarfIsReadOnly);
}
/*** IWMPMedia2 methods ***/
static inline HRESULT IWMPMedia2_get_error(IWMPMedia2* This,IWMPErrorItem **out) {
    return This->lpVtbl->get_error(This,out);
}
#endif
#endif

#endif


#endif  /* __IWMPMedia2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPMedia3 interface
 */
#ifndef __IWMPMedia3_INTERFACE_DEFINED__
#define __IWMPMedia3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPMedia3, 0xf118efc7, 0xf03a, 0x4fb4, 0x99,0xc9, 0x1c,0x02,0xa5,0xc1,0x06,0x5b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f118efc7-f03a-4fb4-99c9-1c02a5c1065b")
IWMPMedia3 : public IWMPMedia2
{
    virtual HRESULT STDMETHODCALLTYPE getAttributeCountByType(
        BSTR type,
        BSTR language,
        LONG *out) = 0;

    virtual HRESULT STDMETHODCALLTYPE getItemInfoByType(
        BSTR type,
        BSTR language,
        LONG index,
        VARIANT *out) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPMedia3, 0xf118efc7, 0xf03a, 0x4fb4, 0x99,0xc9, 0x1c,0x02,0xa5,0xc1,0x06,0x5b)
#endif
#else
typedef struct IWMPMedia3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPMedia3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPMedia3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPMedia3 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPMedia3 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPMedia3 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPMedia3 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPMedia3 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPMedia methods ***/
    HRESULT (STDMETHODCALLTYPE *get_isIdentical)(
        IWMPMedia3 *This,
        IWMPMedia *pIWMPMedia,
        VARIANT_BOOL *pvbool);

    HRESULT (STDMETHODCALLTYPE *get_sourceURL)(
        IWMPMedia3 *This,
        BSTR *pbstrSourceURL);

    HRESULT (STDMETHODCALLTYPE *get_name)(
        IWMPMedia3 *This,
        BSTR *pbstrName);

    HRESULT (STDMETHODCALLTYPE *put_name)(
        IWMPMedia3 *This,
        BSTR pbstrName);

    HRESULT (STDMETHODCALLTYPE *get_imageSourceWidth)(
        IWMPMedia3 *This,
        LONG *pWidth);

    HRESULT (STDMETHODCALLTYPE *get_imageSourceHeight)(
        IWMPMedia3 *This,
        LONG *pHeight);

    HRESULT (STDMETHODCALLTYPE *get_markerCount)(
        IWMPMedia3 *This,
        LONG *pMarkerCount);

    HRESULT (STDMETHODCALLTYPE *getMarkerTime)(
        IWMPMedia3 *This,
        LONG MarkerNum,
        double *pMarkerTime);

    HRESULT (STDMETHODCALLTYPE *getMarkerName)(
        IWMPMedia3 *This,
        LONG MarkerNum,
        BSTR *pbstrMarkerName);

    HRESULT (STDMETHODCALLTYPE *get_duration)(
        IWMPMedia3 *This,
        double *pDuration);

    HRESULT (STDMETHODCALLTYPE *get_durationString)(
        IWMPMedia3 *This,
        BSTR *pbstrDuration);

    HRESULT (STDMETHODCALLTYPE *get_attributeCount)(
        IWMPMedia3 *This,
        LONG *plCount);

    HRESULT (STDMETHODCALLTYPE *getAttributeName)(
        IWMPMedia3 *This,
        LONG lIndex,
        BSTR *pbstrItemName);

    HRESULT (STDMETHODCALLTYPE *getItemInfo)(
        IWMPMedia3 *This,
        BSTR bstrItemName,
        BSTR *pbstrVal);

    HRESULT (STDMETHODCALLTYPE *setItemInfo)(
        IWMPMedia3 *This,
        BSTR bstrItemName,
        BSTR bstrVal);

    HRESULT (STDMETHODCALLTYPE *getItemInfoByAtom)(
        IWMPMedia3 *This,
        LONG lAtom,
        BSTR *pbstrVal);

    HRESULT (STDMETHODCALLTYPE *isMemberOf)(
        IWMPMedia3 *This,
        IWMPPlaylist *pPlaylist,
        VARIANT_BOOL *pvarfIsMemberOf);

    HRESULT (STDMETHODCALLTYPE *isReadOnlyItem)(
        IWMPMedia3 *This,
        BSTR bstrItemName,
        VARIANT_BOOL *pvarfIsReadOnly);

    /*** IWMPMedia2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_error)(
        IWMPMedia3 *This,
        IWMPErrorItem **out);

    /*** IWMPMedia3 methods ***/
    HRESULT (STDMETHODCALLTYPE *getAttributeCountByType)(
        IWMPMedia3 *This,
        BSTR type,
        BSTR language,
        LONG *out);

    HRESULT (STDMETHODCALLTYPE *getItemInfoByType)(
        IWMPMedia3 *This,
        BSTR type,
        BSTR language,
        LONG index,
        VARIANT *out);

    END_INTERFACE
} IWMPMedia3Vtbl;

interface IWMPMedia3 {
    CONST_VTBL IWMPMedia3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPMedia3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPMedia3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPMedia3_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPMedia3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPMedia3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPMedia3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPMedia3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPMedia methods ***/
#define IWMPMedia3_get_isIdentical(This,pIWMPMedia,pvbool) (This)->lpVtbl->get_isIdentical(This,pIWMPMedia,pvbool)
#define IWMPMedia3_get_sourceURL(This,pbstrSourceURL) (This)->lpVtbl->get_sourceURL(This,pbstrSourceURL)
#define IWMPMedia3_get_name(This,pbstrName) (This)->lpVtbl->get_name(This,pbstrName)
#define IWMPMedia3_put_name(This,pbstrName) (This)->lpVtbl->put_name(This,pbstrName)
#define IWMPMedia3_get_imageSourceWidth(This,pWidth) (This)->lpVtbl->get_imageSourceWidth(This,pWidth)
#define IWMPMedia3_get_imageSourceHeight(This,pHeight) (This)->lpVtbl->get_imageSourceHeight(This,pHeight)
#define IWMPMedia3_get_markerCount(This,pMarkerCount) (This)->lpVtbl->get_markerCount(This,pMarkerCount)
#define IWMPMedia3_getMarkerTime(This,MarkerNum,pMarkerTime) (This)->lpVtbl->getMarkerTime(This,MarkerNum,pMarkerTime)
#define IWMPMedia3_getMarkerName(This,MarkerNum,pbstrMarkerName) (This)->lpVtbl->getMarkerName(This,MarkerNum,pbstrMarkerName)
#define IWMPMedia3_get_duration(This,pDuration) (This)->lpVtbl->get_duration(This,pDuration)
#define IWMPMedia3_get_durationString(This,pbstrDuration) (This)->lpVtbl->get_durationString(This,pbstrDuration)
#define IWMPMedia3_get_attributeCount(This,plCount) (This)->lpVtbl->get_attributeCount(This,plCount)
#define IWMPMedia3_getAttributeName(This,lIndex,pbstrItemName) (This)->lpVtbl->getAttributeName(This,lIndex,pbstrItemName)
#define IWMPMedia3_getItemInfo(This,bstrItemName,pbstrVal) (This)->lpVtbl->getItemInfo(This,bstrItemName,pbstrVal)
#define IWMPMedia3_setItemInfo(This,bstrItemName,bstrVal) (This)->lpVtbl->setItemInfo(This,bstrItemName,bstrVal)
#define IWMPMedia3_getItemInfoByAtom(This,lAtom,pbstrVal) (This)->lpVtbl->getItemInfoByAtom(This,lAtom,pbstrVal)
#define IWMPMedia3_isMemberOf(This,pPlaylist,pvarfIsMemberOf) (This)->lpVtbl->isMemberOf(This,pPlaylist,pvarfIsMemberOf)
#define IWMPMedia3_isReadOnlyItem(This,bstrItemName,pvarfIsReadOnly) (This)->lpVtbl->isReadOnlyItem(This,bstrItemName,pvarfIsReadOnly)
/*** IWMPMedia2 methods ***/
#define IWMPMedia3_get_error(This,out) (This)->lpVtbl->get_error(This,out)
/*** IWMPMedia3 methods ***/
#define IWMPMedia3_getAttributeCountByType(This,type,language,out) (This)->lpVtbl->getAttributeCountByType(This,type,language,out)
#define IWMPMedia3_getItemInfoByType(This,type,language,index,out) (This)->lpVtbl->getItemInfoByType(This,type,language,index,out)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPMedia3_QueryInterface(IWMPMedia3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPMedia3_AddRef(IWMPMedia3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPMedia3_Release(IWMPMedia3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPMedia3_GetTypeInfoCount(IWMPMedia3* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPMedia3_GetTypeInfo(IWMPMedia3* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPMedia3_GetIDsOfNames(IWMPMedia3* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPMedia3_Invoke(IWMPMedia3* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPMedia methods ***/
static inline HRESULT IWMPMedia3_get_isIdentical(IWMPMedia3* This,IWMPMedia *pIWMPMedia,VARIANT_BOOL *pvbool) {
    return This->lpVtbl->get_isIdentical(This,pIWMPMedia,pvbool);
}
static inline HRESULT IWMPMedia3_get_sourceURL(IWMPMedia3* This,BSTR *pbstrSourceURL) {
    return This->lpVtbl->get_sourceURL(This,pbstrSourceURL);
}
static inline HRESULT IWMPMedia3_get_name(IWMPMedia3* This,BSTR *pbstrName) {
    return This->lpVtbl->get_name(This,pbstrName);
}
static inline HRESULT IWMPMedia3_put_name(IWMPMedia3* This,BSTR pbstrName) {
    return This->lpVtbl->put_name(This,pbstrName);
}
static inline HRESULT IWMPMedia3_get_imageSourceWidth(IWMPMedia3* This,LONG *pWidth) {
    return This->lpVtbl->get_imageSourceWidth(This,pWidth);
}
static inline HRESULT IWMPMedia3_get_imageSourceHeight(IWMPMedia3* This,LONG *pHeight) {
    return This->lpVtbl->get_imageSourceHeight(This,pHeight);
}
static inline HRESULT IWMPMedia3_get_markerCount(IWMPMedia3* This,LONG *pMarkerCount) {
    return This->lpVtbl->get_markerCount(This,pMarkerCount);
}
static inline HRESULT IWMPMedia3_getMarkerTime(IWMPMedia3* This,LONG MarkerNum,double *pMarkerTime) {
    return This->lpVtbl->getMarkerTime(This,MarkerNum,pMarkerTime);
}
static inline HRESULT IWMPMedia3_getMarkerName(IWMPMedia3* This,LONG MarkerNum,BSTR *pbstrMarkerName) {
    return This->lpVtbl->getMarkerName(This,MarkerNum,pbstrMarkerName);
}
static inline HRESULT IWMPMedia3_get_duration(IWMPMedia3* This,double *pDuration) {
    return This->lpVtbl->get_duration(This,pDuration);
}
static inline HRESULT IWMPMedia3_get_durationString(IWMPMedia3* This,BSTR *pbstrDuration) {
    return This->lpVtbl->get_durationString(This,pbstrDuration);
}
static inline HRESULT IWMPMedia3_get_attributeCount(IWMPMedia3* This,LONG *plCount) {
    return This->lpVtbl->get_attributeCount(This,plCount);
}
static inline HRESULT IWMPMedia3_getAttributeName(IWMPMedia3* This,LONG lIndex,BSTR *pbstrItemName) {
    return This->lpVtbl->getAttributeName(This,lIndex,pbstrItemName);
}
static inline HRESULT IWMPMedia3_getItemInfo(IWMPMedia3* This,BSTR bstrItemName,BSTR *pbstrVal) {
    return This->lpVtbl->getItemInfo(This,bstrItemName,pbstrVal);
}
static inline HRESULT IWMPMedia3_setItemInfo(IWMPMedia3* This,BSTR bstrItemName,BSTR bstrVal) {
    return This->lpVtbl->setItemInfo(This,bstrItemName,bstrVal);
}
static inline HRESULT IWMPMedia3_getItemInfoByAtom(IWMPMedia3* This,LONG lAtom,BSTR *pbstrVal) {
    return This->lpVtbl->getItemInfoByAtom(This,lAtom,pbstrVal);
}
static inline HRESULT IWMPMedia3_isMemberOf(IWMPMedia3* This,IWMPPlaylist *pPlaylist,VARIANT_BOOL *pvarfIsMemberOf) {
    return This->lpVtbl->isMemberOf(This,pPlaylist,pvarfIsMemberOf);
}
static inline HRESULT IWMPMedia3_isReadOnlyItem(IWMPMedia3* This,BSTR bstrItemName,VARIANT_BOOL *pvarfIsReadOnly) {
    return This->lpVtbl->isReadOnlyItem(This,bstrItemName,pvarfIsReadOnly);
}
/*** IWMPMedia2 methods ***/
static inline HRESULT IWMPMedia3_get_error(IWMPMedia3* This,IWMPErrorItem **out) {
    return This->lpVtbl->get_error(This,out);
}
/*** IWMPMedia3 methods ***/
static inline HRESULT IWMPMedia3_getAttributeCountByType(IWMPMedia3* This,BSTR type,BSTR language,LONG *out) {
    return This->lpVtbl->getAttributeCountByType(This,type,language,out);
}
static inline HRESULT IWMPMedia3_getItemInfoByType(IWMPMedia3* This,BSTR type,BSTR language,LONG index,VARIANT *out) {
    return This->lpVtbl->getItemInfoByType(This,type,language,index,out);
}
#endif
#endif

#endif


#endif  /* __IWMPMedia3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPError interface
 */
#ifndef __IWMPError_INTERFACE_DEFINED__
#define __IWMPError_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPError, 0xa12dcf7d, 0x14ab, 0x4c1b, 0xa8,0xcd, 0x63,0x90,0x9f,0x06,0x02,0x5b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a12dcf7d-14ab-4c1b-a8cd-63909f06025b")
IWMPError : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE clearErrorQueue(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_errorCount(
        LONG *plNumErrors) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Item(
        LONG dwIndex,
        IWMPErrorItem **ppErrorItem) = 0;

    virtual HRESULT STDMETHODCALLTYPE webHelp(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPError, 0xa12dcf7d, 0x14ab, 0x4c1b, 0xa8,0xcd, 0x63,0x90,0x9f,0x06,0x02,0x5b)
#endif
#else
typedef struct IWMPErrorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPError *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPError *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPError *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPError *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPError *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPError *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPError *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPError methods ***/
    HRESULT (STDMETHODCALLTYPE *clearErrorQueue)(
        IWMPError *This);

    HRESULT (STDMETHODCALLTYPE *get_errorCount)(
        IWMPError *This,
        LONG *plNumErrors);

    HRESULT (STDMETHODCALLTYPE *get_Item)(
        IWMPError *This,
        LONG dwIndex,
        IWMPErrorItem **ppErrorItem);

    HRESULT (STDMETHODCALLTYPE *webHelp)(
        IWMPError *This);

    END_INTERFACE
} IWMPErrorVtbl;

interface IWMPError {
    CONST_VTBL IWMPErrorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPError_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPError_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPError_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPError_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPError_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPError_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPError_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPError methods ***/
#define IWMPError_clearErrorQueue(This) (This)->lpVtbl->clearErrorQueue(This)
#define IWMPError_get_errorCount(This,plNumErrors) (This)->lpVtbl->get_errorCount(This,plNumErrors)
#define IWMPError_get_Item(This,dwIndex,ppErrorItem) (This)->lpVtbl->get_Item(This,dwIndex,ppErrorItem)
#define IWMPError_webHelp(This) (This)->lpVtbl->webHelp(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPError_QueryInterface(IWMPError* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPError_AddRef(IWMPError* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPError_Release(IWMPError* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPError_GetTypeInfoCount(IWMPError* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPError_GetTypeInfo(IWMPError* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPError_GetIDsOfNames(IWMPError* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPError_Invoke(IWMPError* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPError methods ***/
static inline HRESULT IWMPError_clearErrorQueue(IWMPError* This) {
    return This->lpVtbl->clearErrorQueue(This);
}
static inline HRESULT IWMPError_get_errorCount(IWMPError* This,LONG *plNumErrors) {
    return This->lpVtbl->get_errorCount(This,plNumErrors);
}
static inline HRESULT IWMPError_get_Item(IWMPError* This,LONG dwIndex,IWMPErrorItem **ppErrorItem) {
    return This->lpVtbl->get_Item(This,dwIndex,ppErrorItem);
}
static inline HRESULT IWMPError_webHelp(IWMPError* This) {
    return This->lpVtbl->webHelp(This);
}
#endif
#endif

#endif


#endif  /* __IWMPError_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPPlayerApplication interface
 */
#ifndef __IWMPPlayerApplication_INTERFACE_DEFINED__
#define __IWMPPlayerApplication_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPPlayerApplication, 0x40897764, 0xceab, 0x47be, 0xad,0x4a, 0x8e,0x28,0x53,0x7f,0x9b,0xbf);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("40897764-ceab-47be-ad4a-8e28537f9bbf")
IWMPPlayerApplication : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE switchToPlayerApplication(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE switchToControl(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_playerDocked(
        VARIANT_BOOL *pbPlayerDocked) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_hasDisplay(
        VARIANT_BOOL *pbHasDisplay) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPPlayerApplication, 0x40897764, 0xceab, 0x47be, 0xad,0x4a, 0x8e,0x28,0x53,0x7f,0x9b,0xbf)
#endif
#else
typedef struct IWMPPlayerApplicationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPPlayerApplication *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPPlayerApplication *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPPlayerApplication *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPPlayerApplication *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPPlayerApplication *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPPlayerApplication *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPPlayerApplication *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPPlayerApplication methods ***/
    HRESULT (STDMETHODCALLTYPE *switchToPlayerApplication)(
        IWMPPlayerApplication *This);

    HRESULT (STDMETHODCALLTYPE *switchToControl)(
        IWMPPlayerApplication *This);

    HRESULT (STDMETHODCALLTYPE *get_playerDocked)(
        IWMPPlayerApplication *This,
        VARIANT_BOOL *pbPlayerDocked);

    HRESULT (STDMETHODCALLTYPE *get_hasDisplay)(
        IWMPPlayerApplication *This,
        VARIANT_BOOL *pbHasDisplay);

    END_INTERFACE
} IWMPPlayerApplicationVtbl;

interface IWMPPlayerApplication {
    CONST_VTBL IWMPPlayerApplicationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPPlayerApplication_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPPlayerApplication_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPPlayerApplication_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPPlayerApplication_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPPlayerApplication_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPPlayerApplication_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPPlayerApplication_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPPlayerApplication methods ***/
#define IWMPPlayerApplication_switchToPlayerApplication(This) (This)->lpVtbl->switchToPlayerApplication(This)
#define IWMPPlayerApplication_switchToControl(This) (This)->lpVtbl->switchToControl(This)
#define IWMPPlayerApplication_get_playerDocked(This,pbPlayerDocked) (This)->lpVtbl->get_playerDocked(This,pbPlayerDocked)
#define IWMPPlayerApplication_get_hasDisplay(This,pbHasDisplay) (This)->lpVtbl->get_hasDisplay(This,pbHasDisplay)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPPlayerApplication_QueryInterface(IWMPPlayerApplication* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPPlayerApplication_AddRef(IWMPPlayerApplication* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPPlayerApplication_Release(IWMPPlayerApplication* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPPlayerApplication_GetTypeInfoCount(IWMPPlayerApplication* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPPlayerApplication_GetTypeInfo(IWMPPlayerApplication* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPPlayerApplication_GetIDsOfNames(IWMPPlayerApplication* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPPlayerApplication_Invoke(IWMPPlayerApplication* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPPlayerApplication methods ***/
static inline HRESULT IWMPPlayerApplication_switchToPlayerApplication(IWMPPlayerApplication* This) {
    return This->lpVtbl->switchToPlayerApplication(This);
}
static inline HRESULT IWMPPlayerApplication_switchToControl(IWMPPlayerApplication* This) {
    return This->lpVtbl->switchToControl(This);
}
static inline HRESULT IWMPPlayerApplication_get_playerDocked(IWMPPlayerApplication* This,VARIANT_BOOL *pbPlayerDocked) {
    return This->lpVtbl->get_playerDocked(This,pbPlayerDocked);
}
static inline HRESULT IWMPPlayerApplication_get_hasDisplay(IWMPPlayerApplication* This,VARIANT_BOOL *pbHasDisplay) {
    return This->lpVtbl->get_hasDisplay(This,pbHasDisplay);
}
#endif
#endif

#endif


#endif  /* __IWMPPlayerApplication_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPCore interface
 */
#ifndef __IWMPCore_INTERFACE_DEFINED__
#define __IWMPCore_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPCore, 0xd84cca99, 0xcce2, 0x11d2, 0x9e,0xcc, 0x00,0x00,0xf8,0x08,0x59,0x81);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d84cca99-cce2-11d2-9ecc-0000f8085981")
IWMPCore : public IDispatch
{
    virtual HRESULT STDMETHODCALLTYPE close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_URL(
        BSTR *pbstrURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_URL(
        BSTR pbstrURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_openState(
        WMPOpenState *pwmpos) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_playState(
        WMPPlayState *pwmpps) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_controls(
        IWMPControls **ppControl) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_settings(
        IWMPSettings **ppSettings) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_currentMedia(
        IWMPMedia **ppMedia) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_currentMedia(
        IWMPMedia *ppMedia) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_mediaCollection(
        IWMPMediaCollection **ppMediaCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_playlistCollection(
        IWMPPlaylistCollection **ppPlaylistCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_versionInfo(
        BSTR *pbstrVersionInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE launchURL(
        BSTR bstrURL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_network(
        IWMPNetwork **ppQNI) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_currentPlaylist(
        IWMPPlaylist **ppPL) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_currentPlaylist(
        IWMPPlaylist *ppPL) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_cdromCollection(
        IWMPCdromCollection **ppCdromCollection) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_closedCaption(
        IWMPClosedCaption **ppClosedCaption) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_isOnline(
        VARIANT_BOOL *pfOnline) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Error(
        IWMPError **ppError) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_status(
        BSTR *pbstrStatus) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPCore, 0xd84cca99, 0xcce2, 0x11d2, 0x9e,0xcc, 0x00,0x00,0xf8,0x08,0x59,0x81)
#endif
#else
typedef struct IWMPCoreVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPCore *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPCore *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPCore *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPCore *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPCore *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPCore *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPCore *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPCore methods ***/
    HRESULT (STDMETHODCALLTYPE *close)(
        IWMPCore *This);

    HRESULT (STDMETHODCALLTYPE *get_URL)(
        IWMPCore *This,
        BSTR *pbstrURL);

    HRESULT (STDMETHODCALLTYPE *put_URL)(
        IWMPCore *This,
        BSTR pbstrURL);

    HRESULT (STDMETHODCALLTYPE *get_openState)(
        IWMPCore *This,
        WMPOpenState *pwmpos);

    HRESULT (STDMETHODCALLTYPE *get_playState)(
        IWMPCore *This,
        WMPPlayState *pwmpps);

    HRESULT (STDMETHODCALLTYPE *get_controls)(
        IWMPCore *This,
        IWMPControls **ppControl);

    HRESULT (STDMETHODCALLTYPE *get_settings)(
        IWMPCore *This,
        IWMPSettings **ppSettings);

    HRESULT (STDMETHODCALLTYPE *get_currentMedia)(
        IWMPCore *This,
        IWMPMedia **ppMedia);

    HRESULT (STDMETHODCALLTYPE *put_currentMedia)(
        IWMPCore *This,
        IWMPMedia *ppMedia);

    HRESULT (STDMETHODCALLTYPE *get_mediaCollection)(
        IWMPCore *This,
        IWMPMediaCollection **ppMediaCollection);

    HRESULT (STDMETHODCALLTYPE *get_playlistCollection)(
        IWMPCore *This,
        IWMPPlaylistCollection **ppPlaylistCollection);

    HRESULT (STDMETHODCALLTYPE *get_versionInfo)(
        IWMPCore *This,
        BSTR *pbstrVersionInfo);

    HRESULT (STDMETHODCALLTYPE *launchURL)(
        IWMPCore *This,
        BSTR bstrURL);

    HRESULT (STDMETHODCALLTYPE *get_network)(
        IWMPCore *This,
        IWMPNetwork **ppQNI);

    HRESULT (STDMETHODCALLTYPE *get_currentPlaylist)(
        IWMPCore *This,
        IWMPPlaylist **ppPL);

    HRESULT (STDMETHODCALLTYPE *put_currentPlaylist)(
        IWMPCore *This,
        IWMPPlaylist *ppPL);

    HRESULT (STDMETHODCALLTYPE *get_cdromCollection)(
        IWMPCore *This,
        IWMPCdromCollection **ppCdromCollection);

    HRESULT (STDMETHODCALLTYPE *get_closedCaption)(
        IWMPCore *This,
        IWMPClosedCaption **ppClosedCaption);

    HRESULT (STDMETHODCALLTYPE *get_isOnline)(
        IWMPCore *This,
        VARIANT_BOOL *pfOnline);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWMPCore *This,
        IWMPError **ppError);

    HRESULT (STDMETHODCALLTYPE *get_status)(
        IWMPCore *This,
        BSTR *pbstrStatus);

    END_INTERFACE
} IWMPCoreVtbl;

interface IWMPCore {
    CONST_VTBL IWMPCoreVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPCore_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPCore_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPCore_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPCore_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPCore_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPCore_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPCore_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPCore methods ***/
#define IWMPCore_close(This) (This)->lpVtbl->close(This)
#define IWMPCore_get_URL(This,pbstrURL) (This)->lpVtbl->get_URL(This,pbstrURL)
#define IWMPCore_put_URL(This,pbstrURL) (This)->lpVtbl->put_URL(This,pbstrURL)
#define IWMPCore_get_openState(This,pwmpos) (This)->lpVtbl->get_openState(This,pwmpos)
#define IWMPCore_get_playState(This,pwmpps) (This)->lpVtbl->get_playState(This,pwmpps)
#define IWMPCore_get_controls(This,ppControl) (This)->lpVtbl->get_controls(This,ppControl)
#define IWMPCore_get_settings(This,ppSettings) (This)->lpVtbl->get_settings(This,ppSettings)
#define IWMPCore_get_currentMedia(This,ppMedia) (This)->lpVtbl->get_currentMedia(This,ppMedia)
#define IWMPCore_put_currentMedia(This,ppMedia) (This)->lpVtbl->put_currentMedia(This,ppMedia)
#define IWMPCore_get_mediaCollection(This,ppMediaCollection) (This)->lpVtbl->get_mediaCollection(This,ppMediaCollection)
#define IWMPCore_get_playlistCollection(This,ppPlaylistCollection) (This)->lpVtbl->get_playlistCollection(This,ppPlaylistCollection)
#define IWMPCore_get_versionInfo(This,pbstrVersionInfo) (This)->lpVtbl->get_versionInfo(This,pbstrVersionInfo)
#define IWMPCore_launchURL(This,bstrURL) (This)->lpVtbl->launchURL(This,bstrURL)
#define IWMPCore_get_network(This,ppQNI) (This)->lpVtbl->get_network(This,ppQNI)
#define IWMPCore_get_currentPlaylist(This,ppPL) (This)->lpVtbl->get_currentPlaylist(This,ppPL)
#define IWMPCore_put_currentPlaylist(This,ppPL) (This)->lpVtbl->put_currentPlaylist(This,ppPL)
#define IWMPCore_get_cdromCollection(This,ppCdromCollection) (This)->lpVtbl->get_cdromCollection(This,ppCdromCollection)
#define IWMPCore_get_closedCaption(This,ppClosedCaption) (This)->lpVtbl->get_closedCaption(This,ppClosedCaption)
#define IWMPCore_get_isOnline(This,pfOnline) (This)->lpVtbl->get_isOnline(This,pfOnline)
#define IWMPCore_get_Error(This,ppError) (This)->lpVtbl->get_Error(This,ppError)
#define IWMPCore_get_status(This,pbstrStatus) (This)->lpVtbl->get_status(This,pbstrStatus)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPCore_QueryInterface(IWMPCore* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPCore_AddRef(IWMPCore* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPCore_Release(IWMPCore* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPCore_GetTypeInfoCount(IWMPCore* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPCore_GetTypeInfo(IWMPCore* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPCore_GetIDsOfNames(IWMPCore* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPCore_Invoke(IWMPCore* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPCore methods ***/
static inline HRESULT IWMPCore_close(IWMPCore* This) {
    return This->lpVtbl->close(This);
}
static inline HRESULT IWMPCore_get_URL(IWMPCore* This,BSTR *pbstrURL) {
    return This->lpVtbl->get_URL(This,pbstrURL);
}
static inline HRESULT IWMPCore_put_URL(IWMPCore* This,BSTR pbstrURL) {
    return This->lpVtbl->put_URL(This,pbstrURL);
}
static inline HRESULT IWMPCore_get_openState(IWMPCore* This,WMPOpenState *pwmpos) {
    return This->lpVtbl->get_openState(This,pwmpos);
}
static inline HRESULT IWMPCore_get_playState(IWMPCore* This,WMPPlayState *pwmpps) {
    return This->lpVtbl->get_playState(This,pwmpps);
}
static inline HRESULT IWMPCore_get_controls(IWMPCore* This,IWMPControls **ppControl) {
    return This->lpVtbl->get_controls(This,ppControl);
}
static inline HRESULT IWMPCore_get_settings(IWMPCore* This,IWMPSettings **ppSettings) {
    return This->lpVtbl->get_settings(This,ppSettings);
}
static inline HRESULT IWMPCore_get_currentMedia(IWMPCore* This,IWMPMedia **ppMedia) {
    return This->lpVtbl->get_currentMedia(This,ppMedia);
}
static inline HRESULT IWMPCore_put_currentMedia(IWMPCore* This,IWMPMedia *ppMedia) {
    return This->lpVtbl->put_currentMedia(This,ppMedia);
}
static inline HRESULT IWMPCore_get_mediaCollection(IWMPCore* This,IWMPMediaCollection **ppMediaCollection) {
    return This->lpVtbl->get_mediaCollection(This,ppMediaCollection);
}
static inline HRESULT IWMPCore_get_playlistCollection(IWMPCore* This,IWMPPlaylistCollection **ppPlaylistCollection) {
    return This->lpVtbl->get_playlistCollection(This,ppPlaylistCollection);
}
static inline HRESULT IWMPCore_get_versionInfo(IWMPCore* This,BSTR *pbstrVersionInfo) {
    return This->lpVtbl->get_versionInfo(This,pbstrVersionInfo);
}
static inline HRESULT IWMPCore_launchURL(IWMPCore* This,BSTR bstrURL) {
    return This->lpVtbl->launchURL(This,bstrURL);
}
static inline HRESULT IWMPCore_get_network(IWMPCore* This,IWMPNetwork **ppQNI) {
    return This->lpVtbl->get_network(This,ppQNI);
}
static inline HRESULT IWMPCore_get_currentPlaylist(IWMPCore* This,IWMPPlaylist **ppPL) {
    return This->lpVtbl->get_currentPlaylist(This,ppPL);
}
static inline HRESULT IWMPCore_put_currentPlaylist(IWMPCore* This,IWMPPlaylist *ppPL) {
    return This->lpVtbl->put_currentPlaylist(This,ppPL);
}
static inline HRESULT IWMPCore_get_cdromCollection(IWMPCore* This,IWMPCdromCollection **ppCdromCollection) {
    return This->lpVtbl->get_cdromCollection(This,ppCdromCollection);
}
static inline HRESULT IWMPCore_get_closedCaption(IWMPCore* This,IWMPClosedCaption **ppClosedCaption) {
    return This->lpVtbl->get_closedCaption(This,ppClosedCaption);
}
static inline HRESULT IWMPCore_get_isOnline(IWMPCore* This,VARIANT_BOOL *pfOnline) {
    return This->lpVtbl->get_isOnline(This,pfOnline);
}
static inline HRESULT IWMPCore_get_Error(IWMPCore* This,IWMPError **ppError) {
    return This->lpVtbl->get_Error(This,ppError);
}
static inline HRESULT IWMPCore_get_status(IWMPCore* This,BSTR *pbstrStatus) {
    return This->lpVtbl->get_status(This,pbstrStatus);
}
#endif
#endif

#endif


#endif  /* __IWMPCore_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPCore2 interface
 */
#ifndef __IWMPCore2_INTERFACE_DEFINED__
#define __IWMPCore2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPCore2, 0xbc17e5b7, 0x7561, 0x4c18, 0xbb,0x90, 0x17,0xd4,0x85,0x77,0x56,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bc17e5b7-7561-4c18-bb90-17d485775659")
IWMPCore2 : public IWMPCore
{
    virtual HRESULT STDMETHODCALLTYPE get_dvd(
        IWMPDVD **ppDVD) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPCore2, 0xbc17e5b7, 0x7561, 0x4c18, 0xbb,0x90, 0x17,0xd4,0x85,0x77,0x56,0x59)
#endif
#else
typedef struct IWMPCore2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPCore2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPCore2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPCore2 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPCore2 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPCore2 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPCore2 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPCore2 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPCore methods ***/
    HRESULT (STDMETHODCALLTYPE *close)(
        IWMPCore2 *This);

    HRESULT (STDMETHODCALLTYPE *get_URL)(
        IWMPCore2 *This,
        BSTR *pbstrURL);

    HRESULT (STDMETHODCALLTYPE *put_URL)(
        IWMPCore2 *This,
        BSTR pbstrURL);

    HRESULT (STDMETHODCALLTYPE *get_openState)(
        IWMPCore2 *This,
        WMPOpenState *pwmpos);

    HRESULT (STDMETHODCALLTYPE *get_playState)(
        IWMPCore2 *This,
        WMPPlayState *pwmpps);

    HRESULT (STDMETHODCALLTYPE *get_controls)(
        IWMPCore2 *This,
        IWMPControls **ppControl);

    HRESULT (STDMETHODCALLTYPE *get_settings)(
        IWMPCore2 *This,
        IWMPSettings **ppSettings);

    HRESULT (STDMETHODCALLTYPE *get_currentMedia)(
        IWMPCore2 *This,
        IWMPMedia **ppMedia);

    HRESULT (STDMETHODCALLTYPE *put_currentMedia)(
        IWMPCore2 *This,
        IWMPMedia *ppMedia);

    HRESULT (STDMETHODCALLTYPE *get_mediaCollection)(
        IWMPCore2 *This,
        IWMPMediaCollection **ppMediaCollection);

    HRESULT (STDMETHODCALLTYPE *get_playlistCollection)(
        IWMPCore2 *This,
        IWMPPlaylistCollection **ppPlaylistCollection);

    HRESULT (STDMETHODCALLTYPE *get_versionInfo)(
        IWMPCore2 *This,
        BSTR *pbstrVersionInfo);

    HRESULT (STDMETHODCALLTYPE *launchURL)(
        IWMPCore2 *This,
        BSTR bstrURL);

    HRESULT (STDMETHODCALLTYPE *get_network)(
        IWMPCore2 *This,
        IWMPNetwork **ppQNI);

    HRESULT (STDMETHODCALLTYPE *get_currentPlaylist)(
        IWMPCore2 *This,
        IWMPPlaylist **ppPL);

    HRESULT (STDMETHODCALLTYPE *put_currentPlaylist)(
        IWMPCore2 *This,
        IWMPPlaylist *ppPL);

    HRESULT (STDMETHODCALLTYPE *get_cdromCollection)(
        IWMPCore2 *This,
        IWMPCdromCollection **ppCdromCollection);

    HRESULT (STDMETHODCALLTYPE *get_closedCaption)(
        IWMPCore2 *This,
        IWMPClosedCaption **ppClosedCaption);

    HRESULT (STDMETHODCALLTYPE *get_isOnline)(
        IWMPCore2 *This,
        VARIANT_BOOL *pfOnline);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWMPCore2 *This,
        IWMPError **ppError);

    HRESULT (STDMETHODCALLTYPE *get_status)(
        IWMPCore2 *This,
        BSTR *pbstrStatus);

    /*** IWMPCore2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_dvd)(
        IWMPCore2 *This,
        IWMPDVD **ppDVD);

    END_INTERFACE
} IWMPCore2Vtbl;

interface IWMPCore2 {
    CONST_VTBL IWMPCore2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPCore2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPCore2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPCore2_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPCore2_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPCore2_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPCore2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPCore2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPCore methods ***/
#define IWMPCore2_close(This) (This)->lpVtbl->close(This)
#define IWMPCore2_get_URL(This,pbstrURL) (This)->lpVtbl->get_URL(This,pbstrURL)
#define IWMPCore2_put_URL(This,pbstrURL) (This)->lpVtbl->put_URL(This,pbstrURL)
#define IWMPCore2_get_openState(This,pwmpos) (This)->lpVtbl->get_openState(This,pwmpos)
#define IWMPCore2_get_playState(This,pwmpps) (This)->lpVtbl->get_playState(This,pwmpps)
#define IWMPCore2_get_controls(This,ppControl) (This)->lpVtbl->get_controls(This,ppControl)
#define IWMPCore2_get_settings(This,ppSettings) (This)->lpVtbl->get_settings(This,ppSettings)
#define IWMPCore2_get_currentMedia(This,ppMedia) (This)->lpVtbl->get_currentMedia(This,ppMedia)
#define IWMPCore2_put_currentMedia(This,ppMedia) (This)->lpVtbl->put_currentMedia(This,ppMedia)
#define IWMPCore2_get_mediaCollection(This,ppMediaCollection) (This)->lpVtbl->get_mediaCollection(This,ppMediaCollection)
#define IWMPCore2_get_playlistCollection(This,ppPlaylistCollection) (This)->lpVtbl->get_playlistCollection(This,ppPlaylistCollection)
#define IWMPCore2_get_versionInfo(This,pbstrVersionInfo) (This)->lpVtbl->get_versionInfo(This,pbstrVersionInfo)
#define IWMPCore2_launchURL(This,bstrURL) (This)->lpVtbl->launchURL(This,bstrURL)
#define IWMPCore2_get_network(This,ppQNI) (This)->lpVtbl->get_network(This,ppQNI)
#define IWMPCore2_get_currentPlaylist(This,ppPL) (This)->lpVtbl->get_currentPlaylist(This,ppPL)
#define IWMPCore2_put_currentPlaylist(This,ppPL) (This)->lpVtbl->put_currentPlaylist(This,ppPL)
#define IWMPCore2_get_cdromCollection(This,ppCdromCollection) (This)->lpVtbl->get_cdromCollection(This,ppCdromCollection)
#define IWMPCore2_get_closedCaption(This,ppClosedCaption) (This)->lpVtbl->get_closedCaption(This,ppClosedCaption)
#define IWMPCore2_get_isOnline(This,pfOnline) (This)->lpVtbl->get_isOnline(This,pfOnline)
#define IWMPCore2_get_Error(This,ppError) (This)->lpVtbl->get_Error(This,ppError)
#define IWMPCore2_get_status(This,pbstrStatus) (This)->lpVtbl->get_status(This,pbstrStatus)
/*** IWMPCore2 methods ***/
#define IWMPCore2_get_dvd(This,ppDVD) (This)->lpVtbl->get_dvd(This,ppDVD)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPCore2_QueryInterface(IWMPCore2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPCore2_AddRef(IWMPCore2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPCore2_Release(IWMPCore2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPCore2_GetTypeInfoCount(IWMPCore2* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPCore2_GetTypeInfo(IWMPCore2* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPCore2_GetIDsOfNames(IWMPCore2* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPCore2_Invoke(IWMPCore2* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPCore methods ***/
static inline HRESULT IWMPCore2_close(IWMPCore2* This) {
    return This->lpVtbl->close(This);
}
static inline HRESULT IWMPCore2_get_URL(IWMPCore2* This,BSTR *pbstrURL) {
    return This->lpVtbl->get_URL(This,pbstrURL);
}
static inline HRESULT IWMPCore2_put_URL(IWMPCore2* This,BSTR pbstrURL) {
    return This->lpVtbl->put_URL(This,pbstrURL);
}
static inline HRESULT IWMPCore2_get_openState(IWMPCore2* This,WMPOpenState *pwmpos) {
    return This->lpVtbl->get_openState(This,pwmpos);
}
static inline HRESULT IWMPCore2_get_playState(IWMPCore2* This,WMPPlayState *pwmpps) {
    return This->lpVtbl->get_playState(This,pwmpps);
}
static inline HRESULT IWMPCore2_get_controls(IWMPCore2* This,IWMPControls **ppControl) {
    return This->lpVtbl->get_controls(This,ppControl);
}
static inline HRESULT IWMPCore2_get_settings(IWMPCore2* This,IWMPSettings **ppSettings) {
    return This->lpVtbl->get_settings(This,ppSettings);
}
static inline HRESULT IWMPCore2_get_currentMedia(IWMPCore2* This,IWMPMedia **ppMedia) {
    return This->lpVtbl->get_currentMedia(This,ppMedia);
}
static inline HRESULT IWMPCore2_put_currentMedia(IWMPCore2* This,IWMPMedia *ppMedia) {
    return This->lpVtbl->put_currentMedia(This,ppMedia);
}
static inline HRESULT IWMPCore2_get_mediaCollection(IWMPCore2* This,IWMPMediaCollection **ppMediaCollection) {
    return This->lpVtbl->get_mediaCollection(This,ppMediaCollection);
}
static inline HRESULT IWMPCore2_get_playlistCollection(IWMPCore2* This,IWMPPlaylistCollection **ppPlaylistCollection) {
    return This->lpVtbl->get_playlistCollection(This,ppPlaylistCollection);
}
static inline HRESULT IWMPCore2_get_versionInfo(IWMPCore2* This,BSTR *pbstrVersionInfo) {
    return This->lpVtbl->get_versionInfo(This,pbstrVersionInfo);
}
static inline HRESULT IWMPCore2_launchURL(IWMPCore2* This,BSTR bstrURL) {
    return This->lpVtbl->launchURL(This,bstrURL);
}
static inline HRESULT IWMPCore2_get_network(IWMPCore2* This,IWMPNetwork **ppQNI) {
    return This->lpVtbl->get_network(This,ppQNI);
}
static inline HRESULT IWMPCore2_get_currentPlaylist(IWMPCore2* This,IWMPPlaylist **ppPL) {
    return This->lpVtbl->get_currentPlaylist(This,ppPL);
}
static inline HRESULT IWMPCore2_put_currentPlaylist(IWMPCore2* This,IWMPPlaylist *ppPL) {
    return This->lpVtbl->put_currentPlaylist(This,ppPL);
}
static inline HRESULT IWMPCore2_get_cdromCollection(IWMPCore2* This,IWMPCdromCollection **ppCdromCollection) {
    return This->lpVtbl->get_cdromCollection(This,ppCdromCollection);
}
static inline HRESULT IWMPCore2_get_closedCaption(IWMPCore2* This,IWMPClosedCaption **ppClosedCaption) {
    return This->lpVtbl->get_closedCaption(This,ppClosedCaption);
}
static inline HRESULT IWMPCore2_get_isOnline(IWMPCore2* This,VARIANT_BOOL *pfOnline) {
    return This->lpVtbl->get_isOnline(This,pfOnline);
}
static inline HRESULT IWMPCore2_get_Error(IWMPCore2* This,IWMPError **ppError) {
    return This->lpVtbl->get_Error(This,ppError);
}
static inline HRESULT IWMPCore2_get_status(IWMPCore2* This,BSTR *pbstrStatus) {
    return This->lpVtbl->get_status(This,pbstrStatus);
}
/*** IWMPCore2 methods ***/
static inline HRESULT IWMPCore2_get_dvd(IWMPCore2* This,IWMPDVD **ppDVD) {
    return This->lpVtbl->get_dvd(This,ppDVD);
}
#endif
#endif

#endif


#endif  /* __IWMPCore2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPCore3 interface
 */
#ifndef __IWMPCore3_INTERFACE_DEFINED__
#define __IWMPCore3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPCore3, 0x7587c667, 0x628f, 0x499f, 0x88,0xe7, 0x6a,0x6f,0x4e,0x88,0x84,0x64);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("7587c667-628f-499f-88e7-6a6f4e888464")
IWMPCore3 : public IWMPCore2
{
    virtual HRESULT STDMETHODCALLTYPE newPlaylist(
        BSTR bstrName,
        BSTR bstrURL,
        IWMPPlaylist **ppPlaylist) = 0;

    virtual HRESULT STDMETHODCALLTYPE newMedia(
        BSTR bstrURL,
        IWMPMedia **ppMedia) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPCore3, 0x7587c667, 0x628f, 0x499f, 0x88,0xe7, 0x6a,0x6f,0x4e,0x88,0x84,0x64)
#endif
#else
typedef struct IWMPCore3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPCore3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPCore3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPCore3 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPCore3 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPCore3 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPCore3 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPCore3 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPCore methods ***/
    HRESULT (STDMETHODCALLTYPE *close)(
        IWMPCore3 *This);

    HRESULT (STDMETHODCALLTYPE *get_URL)(
        IWMPCore3 *This,
        BSTR *pbstrURL);

    HRESULT (STDMETHODCALLTYPE *put_URL)(
        IWMPCore3 *This,
        BSTR pbstrURL);

    HRESULT (STDMETHODCALLTYPE *get_openState)(
        IWMPCore3 *This,
        WMPOpenState *pwmpos);

    HRESULT (STDMETHODCALLTYPE *get_playState)(
        IWMPCore3 *This,
        WMPPlayState *pwmpps);

    HRESULT (STDMETHODCALLTYPE *get_controls)(
        IWMPCore3 *This,
        IWMPControls **ppControl);

    HRESULT (STDMETHODCALLTYPE *get_settings)(
        IWMPCore3 *This,
        IWMPSettings **ppSettings);

    HRESULT (STDMETHODCALLTYPE *get_currentMedia)(
        IWMPCore3 *This,
        IWMPMedia **ppMedia);

    HRESULT (STDMETHODCALLTYPE *put_currentMedia)(
        IWMPCore3 *This,
        IWMPMedia *ppMedia);

    HRESULT (STDMETHODCALLTYPE *get_mediaCollection)(
        IWMPCore3 *This,
        IWMPMediaCollection **ppMediaCollection);

    HRESULT (STDMETHODCALLTYPE *get_playlistCollection)(
        IWMPCore3 *This,
        IWMPPlaylistCollection **ppPlaylistCollection);

    HRESULT (STDMETHODCALLTYPE *get_versionInfo)(
        IWMPCore3 *This,
        BSTR *pbstrVersionInfo);

    HRESULT (STDMETHODCALLTYPE *launchURL)(
        IWMPCore3 *This,
        BSTR bstrURL);

    HRESULT (STDMETHODCALLTYPE *get_network)(
        IWMPCore3 *This,
        IWMPNetwork **ppQNI);

    HRESULT (STDMETHODCALLTYPE *get_currentPlaylist)(
        IWMPCore3 *This,
        IWMPPlaylist **ppPL);

    HRESULT (STDMETHODCALLTYPE *put_currentPlaylist)(
        IWMPCore3 *This,
        IWMPPlaylist *ppPL);

    HRESULT (STDMETHODCALLTYPE *get_cdromCollection)(
        IWMPCore3 *This,
        IWMPCdromCollection **ppCdromCollection);

    HRESULT (STDMETHODCALLTYPE *get_closedCaption)(
        IWMPCore3 *This,
        IWMPClosedCaption **ppClosedCaption);

    HRESULT (STDMETHODCALLTYPE *get_isOnline)(
        IWMPCore3 *This,
        VARIANT_BOOL *pfOnline);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWMPCore3 *This,
        IWMPError **ppError);

    HRESULT (STDMETHODCALLTYPE *get_status)(
        IWMPCore3 *This,
        BSTR *pbstrStatus);

    /*** IWMPCore2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_dvd)(
        IWMPCore3 *This,
        IWMPDVD **ppDVD);

    /*** IWMPCore3 methods ***/
    HRESULT (STDMETHODCALLTYPE *newPlaylist)(
        IWMPCore3 *This,
        BSTR bstrName,
        BSTR bstrURL,
        IWMPPlaylist **ppPlaylist);

    HRESULT (STDMETHODCALLTYPE *newMedia)(
        IWMPCore3 *This,
        BSTR bstrURL,
        IWMPMedia **ppMedia);

    END_INTERFACE
} IWMPCore3Vtbl;

interface IWMPCore3 {
    CONST_VTBL IWMPCore3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPCore3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPCore3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPCore3_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPCore3_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPCore3_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPCore3_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPCore3_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPCore methods ***/
#define IWMPCore3_close(This) (This)->lpVtbl->close(This)
#define IWMPCore3_get_URL(This,pbstrURL) (This)->lpVtbl->get_URL(This,pbstrURL)
#define IWMPCore3_put_URL(This,pbstrURL) (This)->lpVtbl->put_URL(This,pbstrURL)
#define IWMPCore3_get_openState(This,pwmpos) (This)->lpVtbl->get_openState(This,pwmpos)
#define IWMPCore3_get_playState(This,pwmpps) (This)->lpVtbl->get_playState(This,pwmpps)
#define IWMPCore3_get_controls(This,ppControl) (This)->lpVtbl->get_controls(This,ppControl)
#define IWMPCore3_get_settings(This,ppSettings) (This)->lpVtbl->get_settings(This,ppSettings)
#define IWMPCore3_get_currentMedia(This,ppMedia) (This)->lpVtbl->get_currentMedia(This,ppMedia)
#define IWMPCore3_put_currentMedia(This,ppMedia) (This)->lpVtbl->put_currentMedia(This,ppMedia)
#define IWMPCore3_get_mediaCollection(This,ppMediaCollection) (This)->lpVtbl->get_mediaCollection(This,ppMediaCollection)
#define IWMPCore3_get_playlistCollection(This,ppPlaylistCollection) (This)->lpVtbl->get_playlistCollection(This,ppPlaylistCollection)
#define IWMPCore3_get_versionInfo(This,pbstrVersionInfo) (This)->lpVtbl->get_versionInfo(This,pbstrVersionInfo)
#define IWMPCore3_launchURL(This,bstrURL) (This)->lpVtbl->launchURL(This,bstrURL)
#define IWMPCore3_get_network(This,ppQNI) (This)->lpVtbl->get_network(This,ppQNI)
#define IWMPCore3_get_currentPlaylist(This,ppPL) (This)->lpVtbl->get_currentPlaylist(This,ppPL)
#define IWMPCore3_put_currentPlaylist(This,ppPL) (This)->lpVtbl->put_currentPlaylist(This,ppPL)
#define IWMPCore3_get_cdromCollection(This,ppCdromCollection) (This)->lpVtbl->get_cdromCollection(This,ppCdromCollection)
#define IWMPCore3_get_closedCaption(This,ppClosedCaption) (This)->lpVtbl->get_closedCaption(This,ppClosedCaption)
#define IWMPCore3_get_isOnline(This,pfOnline) (This)->lpVtbl->get_isOnline(This,pfOnline)
#define IWMPCore3_get_Error(This,ppError) (This)->lpVtbl->get_Error(This,ppError)
#define IWMPCore3_get_status(This,pbstrStatus) (This)->lpVtbl->get_status(This,pbstrStatus)
/*** IWMPCore2 methods ***/
#define IWMPCore3_get_dvd(This,ppDVD) (This)->lpVtbl->get_dvd(This,ppDVD)
/*** IWMPCore3 methods ***/
#define IWMPCore3_newPlaylist(This,bstrName,bstrURL,ppPlaylist) (This)->lpVtbl->newPlaylist(This,bstrName,bstrURL,ppPlaylist)
#define IWMPCore3_newMedia(This,bstrURL,ppMedia) (This)->lpVtbl->newMedia(This,bstrURL,ppMedia)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPCore3_QueryInterface(IWMPCore3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPCore3_AddRef(IWMPCore3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPCore3_Release(IWMPCore3* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPCore3_GetTypeInfoCount(IWMPCore3* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPCore3_GetTypeInfo(IWMPCore3* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPCore3_GetIDsOfNames(IWMPCore3* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPCore3_Invoke(IWMPCore3* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPCore methods ***/
static inline HRESULT IWMPCore3_close(IWMPCore3* This) {
    return This->lpVtbl->close(This);
}
static inline HRESULT IWMPCore3_get_URL(IWMPCore3* This,BSTR *pbstrURL) {
    return This->lpVtbl->get_URL(This,pbstrURL);
}
static inline HRESULT IWMPCore3_put_URL(IWMPCore3* This,BSTR pbstrURL) {
    return This->lpVtbl->put_URL(This,pbstrURL);
}
static inline HRESULT IWMPCore3_get_openState(IWMPCore3* This,WMPOpenState *pwmpos) {
    return This->lpVtbl->get_openState(This,pwmpos);
}
static inline HRESULT IWMPCore3_get_playState(IWMPCore3* This,WMPPlayState *pwmpps) {
    return This->lpVtbl->get_playState(This,pwmpps);
}
static inline HRESULT IWMPCore3_get_controls(IWMPCore3* This,IWMPControls **ppControl) {
    return This->lpVtbl->get_controls(This,ppControl);
}
static inline HRESULT IWMPCore3_get_settings(IWMPCore3* This,IWMPSettings **ppSettings) {
    return This->lpVtbl->get_settings(This,ppSettings);
}
static inline HRESULT IWMPCore3_get_currentMedia(IWMPCore3* This,IWMPMedia **ppMedia) {
    return This->lpVtbl->get_currentMedia(This,ppMedia);
}
static inline HRESULT IWMPCore3_put_currentMedia(IWMPCore3* This,IWMPMedia *ppMedia) {
    return This->lpVtbl->put_currentMedia(This,ppMedia);
}
static inline HRESULT IWMPCore3_get_mediaCollection(IWMPCore3* This,IWMPMediaCollection **ppMediaCollection) {
    return This->lpVtbl->get_mediaCollection(This,ppMediaCollection);
}
static inline HRESULT IWMPCore3_get_playlistCollection(IWMPCore3* This,IWMPPlaylistCollection **ppPlaylistCollection) {
    return This->lpVtbl->get_playlistCollection(This,ppPlaylistCollection);
}
static inline HRESULT IWMPCore3_get_versionInfo(IWMPCore3* This,BSTR *pbstrVersionInfo) {
    return This->lpVtbl->get_versionInfo(This,pbstrVersionInfo);
}
static inline HRESULT IWMPCore3_launchURL(IWMPCore3* This,BSTR bstrURL) {
    return This->lpVtbl->launchURL(This,bstrURL);
}
static inline HRESULT IWMPCore3_get_network(IWMPCore3* This,IWMPNetwork **ppQNI) {
    return This->lpVtbl->get_network(This,ppQNI);
}
static inline HRESULT IWMPCore3_get_currentPlaylist(IWMPCore3* This,IWMPPlaylist **ppPL) {
    return This->lpVtbl->get_currentPlaylist(This,ppPL);
}
static inline HRESULT IWMPCore3_put_currentPlaylist(IWMPCore3* This,IWMPPlaylist *ppPL) {
    return This->lpVtbl->put_currentPlaylist(This,ppPL);
}
static inline HRESULT IWMPCore3_get_cdromCollection(IWMPCore3* This,IWMPCdromCollection **ppCdromCollection) {
    return This->lpVtbl->get_cdromCollection(This,ppCdromCollection);
}
static inline HRESULT IWMPCore3_get_closedCaption(IWMPCore3* This,IWMPClosedCaption **ppClosedCaption) {
    return This->lpVtbl->get_closedCaption(This,ppClosedCaption);
}
static inline HRESULT IWMPCore3_get_isOnline(IWMPCore3* This,VARIANT_BOOL *pfOnline) {
    return This->lpVtbl->get_isOnline(This,pfOnline);
}
static inline HRESULT IWMPCore3_get_Error(IWMPCore3* This,IWMPError **ppError) {
    return This->lpVtbl->get_Error(This,ppError);
}
static inline HRESULT IWMPCore3_get_status(IWMPCore3* This,BSTR *pbstrStatus) {
    return This->lpVtbl->get_status(This,pbstrStatus);
}
/*** IWMPCore2 methods ***/
static inline HRESULT IWMPCore3_get_dvd(IWMPCore3* This,IWMPDVD **ppDVD) {
    return This->lpVtbl->get_dvd(This,ppDVD);
}
/*** IWMPCore3 methods ***/
static inline HRESULT IWMPCore3_newPlaylist(IWMPCore3* This,BSTR bstrName,BSTR bstrURL,IWMPPlaylist **ppPlaylist) {
    return This->lpVtbl->newPlaylist(This,bstrName,bstrURL,ppPlaylist);
}
static inline HRESULT IWMPCore3_newMedia(IWMPCore3* This,BSTR bstrURL,IWMPMedia **ppMedia) {
    return This->lpVtbl->newMedia(This,bstrURL,ppMedia);
}
#endif
#endif

#endif


#endif  /* __IWMPCore3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPPlayer4 interface
 */
#ifndef __IWMPPlayer4_INTERFACE_DEFINED__
#define __IWMPPlayer4_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPPlayer4, 0x6c497d62, 0x8919, 0x413c, 0x82,0xdb, 0xe9,0x35,0xfb,0x3e,0xc5,0x84);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6c497d62-8919-413c-82db-e935fb3ec584")
IWMPPlayer4 : public IWMPCore3
{
    virtual HRESULT STDMETHODCALLTYPE get_enabled(
        VARIANT_BOOL *pbEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_enabled(
        VARIANT_BOOL pbEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_fullScreen(
        VARIANT_BOOL *pbFullScreen) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_fullScreen(
        VARIANT_BOOL pbFullScreen) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_enableContextMenu(
        VARIANT_BOOL *pbEnableContextMenu) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_enableContextMenu(
        VARIANT_BOOL pbEnableContextMenu) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_uiMode(
        BSTR pbstrMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_uiMode(
        BSTR *pbstrMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_stretchToFit(
        VARIANT_BOOL *pbEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_stretchToFit(
        VARIANT_BOOL pbEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_windowlessVideo(
        VARIANT_BOOL *pbEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_windowlessVideo(
        VARIANT_BOOL pbEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_isRemote(
        VARIANT_BOOL *pvarfIsRemote) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_playerApplication(
        IWMPPlayerApplication **ppIWMPPlayerApplication) = 0;

    virtual HRESULT STDMETHODCALLTYPE openPlayer(
        BSTR bstrURL) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPPlayer4, 0x6c497d62, 0x8919, 0x413c, 0x82,0xdb, 0xe9,0x35,0xfb,0x3e,0xc5,0x84)
#endif
#else
typedef struct IWMPPlayer4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPPlayer4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPPlayer4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPPlayer4 *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPPlayer4 *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPPlayer4 *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPPlayer4 *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPPlayer4 *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPCore methods ***/
    HRESULT (STDMETHODCALLTYPE *close)(
        IWMPPlayer4 *This);

    HRESULT (STDMETHODCALLTYPE *get_URL)(
        IWMPPlayer4 *This,
        BSTR *pbstrURL);

    HRESULT (STDMETHODCALLTYPE *put_URL)(
        IWMPPlayer4 *This,
        BSTR pbstrURL);

    HRESULT (STDMETHODCALLTYPE *get_openState)(
        IWMPPlayer4 *This,
        WMPOpenState *pwmpos);

    HRESULT (STDMETHODCALLTYPE *get_playState)(
        IWMPPlayer4 *This,
        WMPPlayState *pwmpps);

    HRESULT (STDMETHODCALLTYPE *get_controls)(
        IWMPPlayer4 *This,
        IWMPControls **ppControl);

    HRESULT (STDMETHODCALLTYPE *get_settings)(
        IWMPPlayer4 *This,
        IWMPSettings **ppSettings);

    HRESULT (STDMETHODCALLTYPE *get_currentMedia)(
        IWMPPlayer4 *This,
        IWMPMedia **ppMedia);

    HRESULT (STDMETHODCALLTYPE *put_currentMedia)(
        IWMPPlayer4 *This,
        IWMPMedia *ppMedia);

    HRESULT (STDMETHODCALLTYPE *get_mediaCollection)(
        IWMPPlayer4 *This,
        IWMPMediaCollection **ppMediaCollection);

    HRESULT (STDMETHODCALLTYPE *get_playlistCollection)(
        IWMPPlayer4 *This,
        IWMPPlaylistCollection **ppPlaylistCollection);

    HRESULT (STDMETHODCALLTYPE *get_versionInfo)(
        IWMPPlayer4 *This,
        BSTR *pbstrVersionInfo);

    HRESULT (STDMETHODCALLTYPE *launchURL)(
        IWMPPlayer4 *This,
        BSTR bstrURL);

    HRESULT (STDMETHODCALLTYPE *get_network)(
        IWMPPlayer4 *This,
        IWMPNetwork **ppQNI);

    HRESULT (STDMETHODCALLTYPE *get_currentPlaylist)(
        IWMPPlayer4 *This,
        IWMPPlaylist **ppPL);

    HRESULT (STDMETHODCALLTYPE *put_currentPlaylist)(
        IWMPPlayer4 *This,
        IWMPPlaylist *ppPL);

    HRESULT (STDMETHODCALLTYPE *get_cdromCollection)(
        IWMPPlayer4 *This,
        IWMPCdromCollection **ppCdromCollection);

    HRESULT (STDMETHODCALLTYPE *get_closedCaption)(
        IWMPPlayer4 *This,
        IWMPClosedCaption **ppClosedCaption);

    HRESULT (STDMETHODCALLTYPE *get_isOnline)(
        IWMPPlayer4 *This,
        VARIANT_BOOL *pfOnline);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWMPPlayer4 *This,
        IWMPError **ppError);

    HRESULT (STDMETHODCALLTYPE *get_status)(
        IWMPPlayer4 *This,
        BSTR *pbstrStatus);

    /*** IWMPCore2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_dvd)(
        IWMPPlayer4 *This,
        IWMPDVD **ppDVD);

    /*** IWMPCore3 methods ***/
    HRESULT (STDMETHODCALLTYPE *newPlaylist)(
        IWMPPlayer4 *This,
        BSTR bstrName,
        BSTR bstrURL,
        IWMPPlaylist **ppPlaylist);

    HRESULT (STDMETHODCALLTYPE *newMedia)(
        IWMPPlayer4 *This,
        BSTR bstrURL,
        IWMPMedia **ppMedia);

    /*** IWMPPlayer4 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_enabled)(
        IWMPPlayer4 *This,
        VARIANT_BOOL *pbEnabled);

    HRESULT (STDMETHODCALLTYPE *put_enabled)(
        IWMPPlayer4 *This,
        VARIANT_BOOL pbEnabled);

    HRESULT (STDMETHODCALLTYPE *get_fullScreen)(
        IWMPPlayer4 *This,
        VARIANT_BOOL *pbFullScreen);

    HRESULT (STDMETHODCALLTYPE *put_fullScreen)(
        IWMPPlayer4 *This,
        VARIANT_BOOL pbFullScreen);

    HRESULT (STDMETHODCALLTYPE *get_enableContextMenu)(
        IWMPPlayer4 *This,
        VARIANT_BOOL *pbEnableContextMenu);

    HRESULT (STDMETHODCALLTYPE *put_enableContextMenu)(
        IWMPPlayer4 *This,
        VARIANT_BOOL pbEnableContextMenu);

    HRESULT (STDMETHODCALLTYPE *put_uiMode)(
        IWMPPlayer4 *This,
        BSTR pbstrMode);

    HRESULT (STDMETHODCALLTYPE *get_uiMode)(
        IWMPPlayer4 *This,
        BSTR *pbstrMode);

    HRESULT (STDMETHODCALLTYPE *get_stretchToFit)(
        IWMPPlayer4 *This,
        VARIANT_BOOL *pbEnabled);

    HRESULT (STDMETHODCALLTYPE *put_stretchToFit)(
        IWMPPlayer4 *This,
        VARIANT_BOOL pbEnabled);

    HRESULT (STDMETHODCALLTYPE *get_windowlessVideo)(
        IWMPPlayer4 *This,
        VARIANT_BOOL *pbEnabled);

    HRESULT (STDMETHODCALLTYPE *put_windowlessVideo)(
        IWMPPlayer4 *This,
        VARIANT_BOOL pbEnabled);

    HRESULT (STDMETHODCALLTYPE *get_isRemote)(
        IWMPPlayer4 *This,
        VARIANT_BOOL *pvarfIsRemote);

    HRESULT (STDMETHODCALLTYPE *get_playerApplication)(
        IWMPPlayer4 *This,
        IWMPPlayerApplication **ppIWMPPlayerApplication);

    HRESULT (STDMETHODCALLTYPE *openPlayer)(
        IWMPPlayer4 *This,
        BSTR bstrURL);

    END_INTERFACE
} IWMPPlayer4Vtbl;

interface IWMPPlayer4 {
    CONST_VTBL IWMPPlayer4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPPlayer4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPPlayer4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPPlayer4_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPPlayer4_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPPlayer4_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPPlayer4_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPPlayer4_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPCore methods ***/
#define IWMPPlayer4_close(This) (This)->lpVtbl->close(This)
#define IWMPPlayer4_get_URL(This,pbstrURL) (This)->lpVtbl->get_URL(This,pbstrURL)
#define IWMPPlayer4_put_URL(This,pbstrURL) (This)->lpVtbl->put_URL(This,pbstrURL)
#define IWMPPlayer4_get_openState(This,pwmpos) (This)->lpVtbl->get_openState(This,pwmpos)
#define IWMPPlayer4_get_playState(This,pwmpps) (This)->lpVtbl->get_playState(This,pwmpps)
#define IWMPPlayer4_get_controls(This,ppControl) (This)->lpVtbl->get_controls(This,ppControl)
#define IWMPPlayer4_get_settings(This,ppSettings) (This)->lpVtbl->get_settings(This,ppSettings)
#define IWMPPlayer4_get_currentMedia(This,ppMedia) (This)->lpVtbl->get_currentMedia(This,ppMedia)
#define IWMPPlayer4_put_currentMedia(This,ppMedia) (This)->lpVtbl->put_currentMedia(This,ppMedia)
#define IWMPPlayer4_get_mediaCollection(This,ppMediaCollection) (This)->lpVtbl->get_mediaCollection(This,ppMediaCollection)
#define IWMPPlayer4_get_playlistCollection(This,ppPlaylistCollection) (This)->lpVtbl->get_playlistCollection(This,ppPlaylistCollection)
#define IWMPPlayer4_get_versionInfo(This,pbstrVersionInfo) (This)->lpVtbl->get_versionInfo(This,pbstrVersionInfo)
#define IWMPPlayer4_launchURL(This,bstrURL) (This)->lpVtbl->launchURL(This,bstrURL)
#define IWMPPlayer4_get_network(This,ppQNI) (This)->lpVtbl->get_network(This,ppQNI)
#define IWMPPlayer4_get_currentPlaylist(This,ppPL) (This)->lpVtbl->get_currentPlaylist(This,ppPL)
#define IWMPPlayer4_put_currentPlaylist(This,ppPL) (This)->lpVtbl->put_currentPlaylist(This,ppPL)
#define IWMPPlayer4_get_cdromCollection(This,ppCdromCollection) (This)->lpVtbl->get_cdromCollection(This,ppCdromCollection)
#define IWMPPlayer4_get_closedCaption(This,ppClosedCaption) (This)->lpVtbl->get_closedCaption(This,ppClosedCaption)
#define IWMPPlayer4_get_isOnline(This,pfOnline) (This)->lpVtbl->get_isOnline(This,pfOnline)
#define IWMPPlayer4_get_Error(This,ppError) (This)->lpVtbl->get_Error(This,ppError)
#define IWMPPlayer4_get_status(This,pbstrStatus) (This)->lpVtbl->get_status(This,pbstrStatus)
/*** IWMPCore2 methods ***/
#define IWMPPlayer4_get_dvd(This,ppDVD) (This)->lpVtbl->get_dvd(This,ppDVD)
/*** IWMPCore3 methods ***/
#define IWMPPlayer4_newPlaylist(This,bstrName,bstrURL,ppPlaylist) (This)->lpVtbl->newPlaylist(This,bstrName,bstrURL,ppPlaylist)
#define IWMPPlayer4_newMedia(This,bstrURL,ppMedia) (This)->lpVtbl->newMedia(This,bstrURL,ppMedia)
/*** IWMPPlayer4 methods ***/
#define IWMPPlayer4_get_enabled(This,pbEnabled) (This)->lpVtbl->get_enabled(This,pbEnabled)
#define IWMPPlayer4_put_enabled(This,pbEnabled) (This)->lpVtbl->put_enabled(This,pbEnabled)
#define IWMPPlayer4_get_fullScreen(This,pbFullScreen) (This)->lpVtbl->get_fullScreen(This,pbFullScreen)
#define IWMPPlayer4_put_fullScreen(This,pbFullScreen) (This)->lpVtbl->put_fullScreen(This,pbFullScreen)
#define IWMPPlayer4_get_enableContextMenu(This,pbEnableContextMenu) (This)->lpVtbl->get_enableContextMenu(This,pbEnableContextMenu)
#define IWMPPlayer4_put_enableContextMenu(This,pbEnableContextMenu) (This)->lpVtbl->put_enableContextMenu(This,pbEnableContextMenu)
#define IWMPPlayer4_put_uiMode(This,pbstrMode) (This)->lpVtbl->put_uiMode(This,pbstrMode)
#define IWMPPlayer4_get_uiMode(This,pbstrMode) (This)->lpVtbl->get_uiMode(This,pbstrMode)
#define IWMPPlayer4_get_stretchToFit(This,pbEnabled) (This)->lpVtbl->get_stretchToFit(This,pbEnabled)
#define IWMPPlayer4_put_stretchToFit(This,pbEnabled) (This)->lpVtbl->put_stretchToFit(This,pbEnabled)
#define IWMPPlayer4_get_windowlessVideo(This,pbEnabled) (This)->lpVtbl->get_windowlessVideo(This,pbEnabled)
#define IWMPPlayer4_put_windowlessVideo(This,pbEnabled) (This)->lpVtbl->put_windowlessVideo(This,pbEnabled)
#define IWMPPlayer4_get_isRemote(This,pvarfIsRemote) (This)->lpVtbl->get_isRemote(This,pvarfIsRemote)
#define IWMPPlayer4_get_playerApplication(This,ppIWMPPlayerApplication) (This)->lpVtbl->get_playerApplication(This,ppIWMPPlayerApplication)
#define IWMPPlayer4_openPlayer(This,bstrURL) (This)->lpVtbl->openPlayer(This,bstrURL)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPPlayer4_QueryInterface(IWMPPlayer4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPPlayer4_AddRef(IWMPPlayer4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPPlayer4_Release(IWMPPlayer4* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPPlayer4_GetTypeInfoCount(IWMPPlayer4* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPPlayer4_GetTypeInfo(IWMPPlayer4* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPPlayer4_GetIDsOfNames(IWMPPlayer4* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPPlayer4_Invoke(IWMPPlayer4* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPCore methods ***/
static inline HRESULT IWMPPlayer4_close(IWMPPlayer4* This) {
    return This->lpVtbl->close(This);
}
static inline HRESULT IWMPPlayer4_get_URL(IWMPPlayer4* This,BSTR *pbstrURL) {
    return This->lpVtbl->get_URL(This,pbstrURL);
}
static inline HRESULT IWMPPlayer4_put_URL(IWMPPlayer4* This,BSTR pbstrURL) {
    return This->lpVtbl->put_URL(This,pbstrURL);
}
static inline HRESULT IWMPPlayer4_get_openState(IWMPPlayer4* This,WMPOpenState *pwmpos) {
    return This->lpVtbl->get_openState(This,pwmpos);
}
static inline HRESULT IWMPPlayer4_get_playState(IWMPPlayer4* This,WMPPlayState *pwmpps) {
    return This->lpVtbl->get_playState(This,pwmpps);
}
static inline HRESULT IWMPPlayer4_get_controls(IWMPPlayer4* This,IWMPControls **ppControl) {
    return This->lpVtbl->get_controls(This,ppControl);
}
static inline HRESULT IWMPPlayer4_get_settings(IWMPPlayer4* This,IWMPSettings **ppSettings) {
    return This->lpVtbl->get_settings(This,ppSettings);
}
static inline HRESULT IWMPPlayer4_get_currentMedia(IWMPPlayer4* This,IWMPMedia **ppMedia) {
    return This->lpVtbl->get_currentMedia(This,ppMedia);
}
static inline HRESULT IWMPPlayer4_put_currentMedia(IWMPPlayer4* This,IWMPMedia *ppMedia) {
    return This->lpVtbl->put_currentMedia(This,ppMedia);
}
static inline HRESULT IWMPPlayer4_get_mediaCollection(IWMPPlayer4* This,IWMPMediaCollection **ppMediaCollection) {
    return This->lpVtbl->get_mediaCollection(This,ppMediaCollection);
}
static inline HRESULT IWMPPlayer4_get_playlistCollection(IWMPPlayer4* This,IWMPPlaylistCollection **ppPlaylistCollection) {
    return This->lpVtbl->get_playlistCollection(This,ppPlaylistCollection);
}
static inline HRESULT IWMPPlayer4_get_versionInfo(IWMPPlayer4* This,BSTR *pbstrVersionInfo) {
    return This->lpVtbl->get_versionInfo(This,pbstrVersionInfo);
}
static inline HRESULT IWMPPlayer4_launchURL(IWMPPlayer4* This,BSTR bstrURL) {
    return This->lpVtbl->launchURL(This,bstrURL);
}
static inline HRESULT IWMPPlayer4_get_network(IWMPPlayer4* This,IWMPNetwork **ppQNI) {
    return This->lpVtbl->get_network(This,ppQNI);
}
static inline HRESULT IWMPPlayer4_get_currentPlaylist(IWMPPlayer4* This,IWMPPlaylist **ppPL) {
    return This->lpVtbl->get_currentPlaylist(This,ppPL);
}
static inline HRESULT IWMPPlayer4_put_currentPlaylist(IWMPPlayer4* This,IWMPPlaylist *ppPL) {
    return This->lpVtbl->put_currentPlaylist(This,ppPL);
}
static inline HRESULT IWMPPlayer4_get_cdromCollection(IWMPPlayer4* This,IWMPCdromCollection **ppCdromCollection) {
    return This->lpVtbl->get_cdromCollection(This,ppCdromCollection);
}
static inline HRESULT IWMPPlayer4_get_closedCaption(IWMPPlayer4* This,IWMPClosedCaption **ppClosedCaption) {
    return This->lpVtbl->get_closedCaption(This,ppClosedCaption);
}
static inline HRESULT IWMPPlayer4_get_isOnline(IWMPPlayer4* This,VARIANT_BOOL *pfOnline) {
    return This->lpVtbl->get_isOnline(This,pfOnline);
}
static inline HRESULT IWMPPlayer4_get_Error(IWMPPlayer4* This,IWMPError **ppError) {
    return This->lpVtbl->get_Error(This,ppError);
}
static inline HRESULT IWMPPlayer4_get_status(IWMPPlayer4* This,BSTR *pbstrStatus) {
    return This->lpVtbl->get_status(This,pbstrStatus);
}
/*** IWMPCore2 methods ***/
static inline HRESULT IWMPPlayer4_get_dvd(IWMPPlayer4* This,IWMPDVD **ppDVD) {
    return This->lpVtbl->get_dvd(This,ppDVD);
}
/*** IWMPCore3 methods ***/
static inline HRESULT IWMPPlayer4_newPlaylist(IWMPPlayer4* This,BSTR bstrName,BSTR bstrURL,IWMPPlaylist **ppPlaylist) {
    return This->lpVtbl->newPlaylist(This,bstrName,bstrURL,ppPlaylist);
}
static inline HRESULT IWMPPlayer4_newMedia(IWMPPlayer4* This,BSTR bstrURL,IWMPMedia **ppMedia) {
    return This->lpVtbl->newMedia(This,bstrURL,ppMedia);
}
/*** IWMPPlayer4 methods ***/
static inline HRESULT IWMPPlayer4_get_enabled(IWMPPlayer4* This,VARIANT_BOOL *pbEnabled) {
    return This->lpVtbl->get_enabled(This,pbEnabled);
}
static inline HRESULT IWMPPlayer4_put_enabled(IWMPPlayer4* This,VARIANT_BOOL pbEnabled) {
    return This->lpVtbl->put_enabled(This,pbEnabled);
}
static inline HRESULT IWMPPlayer4_get_fullScreen(IWMPPlayer4* This,VARIANT_BOOL *pbFullScreen) {
    return This->lpVtbl->get_fullScreen(This,pbFullScreen);
}
static inline HRESULT IWMPPlayer4_put_fullScreen(IWMPPlayer4* This,VARIANT_BOOL pbFullScreen) {
    return This->lpVtbl->put_fullScreen(This,pbFullScreen);
}
static inline HRESULT IWMPPlayer4_get_enableContextMenu(IWMPPlayer4* This,VARIANT_BOOL *pbEnableContextMenu) {
    return This->lpVtbl->get_enableContextMenu(This,pbEnableContextMenu);
}
static inline HRESULT IWMPPlayer4_put_enableContextMenu(IWMPPlayer4* This,VARIANT_BOOL pbEnableContextMenu) {
    return This->lpVtbl->put_enableContextMenu(This,pbEnableContextMenu);
}
static inline HRESULT IWMPPlayer4_put_uiMode(IWMPPlayer4* This,BSTR pbstrMode) {
    return This->lpVtbl->put_uiMode(This,pbstrMode);
}
static inline HRESULT IWMPPlayer4_get_uiMode(IWMPPlayer4* This,BSTR *pbstrMode) {
    return This->lpVtbl->get_uiMode(This,pbstrMode);
}
static inline HRESULT IWMPPlayer4_get_stretchToFit(IWMPPlayer4* This,VARIANT_BOOL *pbEnabled) {
    return This->lpVtbl->get_stretchToFit(This,pbEnabled);
}
static inline HRESULT IWMPPlayer4_put_stretchToFit(IWMPPlayer4* This,VARIANT_BOOL pbEnabled) {
    return This->lpVtbl->put_stretchToFit(This,pbEnabled);
}
static inline HRESULT IWMPPlayer4_get_windowlessVideo(IWMPPlayer4* This,VARIANT_BOOL *pbEnabled) {
    return This->lpVtbl->get_windowlessVideo(This,pbEnabled);
}
static inline HRESULT IWMPPlayer4_put_windowlessVideo(IWMPPlayer4* This,VARIANT_BOOL pbEnabled) {
    return This->lpVtbl->put_windowlessVideo(This,pbEnabled);
}
static inline HRESULT IWMPPlayer4_get_isRemote(IWMPPlayer4* This,VARIANT_BOOL *pvarfIsRemote) {
    return This->lpVtbl->get_isRemote(This,pvarfIsRemote);
}
static inline HRESULT IWMPPlayer4_get_playerApplication(IWMPPlayer4* This,IWMPPlayerApplication **ppIWMPPlayerApplication) {
    return This->lpVtbl->get_playerApplication(This,ppIWMPPlayerApplication);
}
static inline HRESULT IWMPPlayer4_openPlayer(IWMPPlayer4* This,BSTR bstrURL) {
    return This->lpVtbl->openPlayer(This,bstrURL);
}
#endif
#endif

#endif


#endif  /* __IWMPPlayer4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPPlayer interface
 */
#ifndef __IWMPPlayer_INTERFACE_DEFINED__
#define __IWMPPlayer_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPPlayer, 0x6bf52a4f, 0x394a, 0x11d3, 0xb1,0x53, 0x00,0xc0,0x4f,0x79,0xfa,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6bf52a4f-394a-11d3-b153-00c04f79faa6")
IWMPPlayer : public IWMPCore
{
    virtual HRESULT STDMETHODCALLTYPE get_enabled(
        VARIANT_BOOL *pbEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_enabled(
        VARIANT_BOOL pbEnabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_fullScreen(
        VARIANT_BOOL *pbFullScreen) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_fullScreen(
        VARIANT_BOOL pbFullScreen) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_enableContextMenu(
        VARIANT_BOOL *pbEnableContextMenu) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_enableContextMenu(
        VARIANT_BOOL pbEnableContextMenu) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_uiMode(
        BSTR pbstrMode) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_uiMode(
        BSTR *pbstrMode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPPlayer, 0x6bf52a4f, 0x394a, 0x11d3, 0xb1,0x53, 0x00,0xc0,0x4f,0x79,0xfa,0xa6)
#endif
#else
typedef struct IWMPPlayerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPPlayer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPPlayer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPPlayer *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        IWMPPlayer *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        IWMPPlayer *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        IWMPPlayer *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        IWMPPlayer *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    /*** IWMPCore methods ***/
    HRESULT (STDMETHODCALLTYPE *close)(
        IWMPPlayer *This);

    HRESULT (STDMETHODCALLTYPE *get_URL)(
        IWMPPlayer *This,
        BSTR *pbstrURL);

    HRESULT (STDMETHODCALLTYPE *put_URL)(
        IWMPPlayer *This,
        BSTR pbstrURL);

    HRESULT (STDMETHODCALLTYPE *get_openState)(
        IWMPPlayer *This,
        WMPOpenState *pwmpos);

    HRESULT (STDMETHODCALLTYPE *get_playState)(
        IWMPPlayer *This,
        WMPPlayState *pwmpps);

    HRESULT (STDMETHODCALLTYPE *get_controls)(
        IWMPPlayer *This,
        IWMPControls **ppControl);

    HRESULT (STDMETHODCALLTYPE *get_settings)(
        IWMPPlayer *This,
        IWMPSettings **ppSettings);

    HRESULT (STDMETHODCALLTYPE *get_currentMedia)(
        IWMPPlayer *This,
        IWMPMedia **ppMedia);

    HRESULT (STDMETHODCALLTYPE *put_currentMedia)(
        IWMPPlayer *This,
        IWMPMedia *ppMedia);

    HRESULT (STDMETHODCALLTYPE *get_mediaCollection)(
        IWMPPlayer *This,
        IWMPMediaCollection **ppMediaCollection);

    HRESULT (STDMETHODCALLTYPE *get_playlistCollection)(
        IWMPPlayer *This,
        IWMPPlaylistCollection **ppPlaylistCollection);

    HRESULT (STDMETHODCALLTYPE *get_versionInfo)(
        IWMPPlayer *This,
        BSTR *pbstrVersionInfo);

    HRESULT (STDMETHODCALLTYPE *launchURL)(
        IWMPPlayer *This,
        BSTR bstrURL);

    HRESULT (STDMETHODCALLTYPE *get_network)(
        IWMPPlayer *This,
        IWMPNetwork **ppQNI);

    HRESULT (STDMETHODCALLTYPE *get_currentPlaylist)(
        IWMPPlayer *This,
        IWMPPlaylist **ppPL);

    HRESULT (STDMETHODCALLTYPE *put_currentPlaylist)(
        IWMPPlayer *This,
        IWMPPlaylist *ppPL);

    HRESULT (STDMETHODCALLTYPE *get_cdromCollection)(
        IWMPPlayer *This,
        IWMPCdromCollection **ppCdromCollection);

    HRESULT (STDMETHODCALLTYPE *get_closedCaption)(
        IWMPPlayer *This,
        IWMPClosedCaption **ppClosedCaption);

    HRESULT (STDMETHODCALLTYPE *get_isOnline)(
        IWMPPlayer *This,
        VARIANT_BOOL *pfOnline);

    HRESULT (STDMETHODCALLTYPE *get_Error)(
        IWMPPlayer *This,
        IWMPError **ppError);

    HRESULT (STDMETHODCALLTYPE *get_status)(
        IWMPPlayer *This,
        BSTR *pbstrStatus);

    /*** IWMPPlayer methods ***/
    HRESULT (STDMETHODCALLTYPE *get_enabled)(
        IWMPPlayer *This,
        VARIANT_BOOL *pbEnabled);

    HRESULT (STDMETHODCALLTYPE *put_enabled)(
        IWMPPlayer *This,
        VARIANT_BOOL pbEnabled);

    HRESULT (STDMETHODCALLTYPE *get_fullScreen)(
        IWMPPlayer *This,
        VARIANT_BOOL *pbFullScreen);

    HRESULT (STDMETHODCALLTYPE *put_fullScreen)(
        IWMPPlayer *This,
        VARIANT_BOOL pbFullScreen);

    HRESULT (STDMETHODCALLTYPE *get_enableContextMenu)(
        IWMPPlayer *This,
        VARIANT_BOOL *pbEnableContextMenu);

    HRESULT (STDMETHODCALLTYPE *put_enableContextMenu)(
        IWMPPlayer *This,
        VARIANT_BOOL pbEnableContextMenu);

    HRESULT (STDMETHODCALLTYPE *put_uiMode)(
        IWMPPlayer *This,
        BSTR pbstrMode);

    HRESULT (STDMETHODCALLTYPE *get_uiMode)(
        IWMPPlayer *This,
        BSTR *pbstrMode);

    END_INTERFACE
} IWMPPlayerVtbl;

interface IWMPPlayer {
    CONST_VTBL IWMPPlayerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPPlayer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPPlayer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPPlayer_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define IWMPPlayer_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define IWMPPlayer_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define IWMPPlayer_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define IWMPPlayer_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
/*** IWMPCore methods ***/
#define IWMPPlayer_close(This) (This)->lpVtbl->close(This)
#define IWMPPlayer_get_URL(This,pbstrURL) (This)->lpVtbl->get_URL(This,pbstrURL)
#define IWMPPlayer_put_URL(This,pbstrURL) (This)->lpVtbl->put_URL(This,pbstrURL)
#define IWMPPlayer_get_openState(This,pwmpos) (This)->lpVtbl->get_openState(This,pwmpos)
#define IWMPPlayer_get_playState(This,pwmpps) (This)->lpVtbl->get_playState(This,pwmpps)
#define IWMPPlayer_get_controls(This,ppControl) (This)->lpVtbl->get_controls(This,ppControl)
#define IWMPPlayer_get_settings(This,ppSettings) (This)->lpVtbl->get_settings(This,ppSettings)
#define IWMPPlayer_get_currentMedia(This,ppMedia) (This)->lpVtbl->get_currentMedia(This,ppMedia)
#define IWMPPlayer_put_currentMedia(This,ppMedia) (This)->lpVtbl->put_currentMedia(This,ppMedia)
#define IWMPPlayer_get_mediaCollection(This,ppMediaCollection) (This)->lpVtbl->get_mediaCollection(This,ppMediaCollection)
#define IWMPPlayer_get_playlistCollection(This,ppPlaylistCollection) (This)->lpVtbl->get_playlistCollection(This,ppPlaylistCollection)
#define IWMPPlayer_get_versionInfo(This,pbstrVersionInfo) (This)->lpVtbl->get_versionInfo(This,pbstrVersionInfo)
#define IWMPPlayer_launchURL(This,bstrURL) (This)->lpVtbl->launchURL(This,bstrURL)
#define IWMPPlayer_get_network(This,ppQNI) (This)->lpVtbl->get_network(This,ppQNI)
#define IWMPPlayer_get_currentPlaylist(This,ppPL) (This)->lpVtbl->get_currentPlaylist(This,ppPL)
#define IWMPPlayer_put_currentPlaylist(This,ppPL) (This)->lpVtbl->put_currentPlaylist(This,ppPL)
#define IWMPPlayer_get_cdromCollection(This,ppCdromCollection) (This)->lpVtbl->get_cdromCollection(This,ppCdromCollection)
#define IWMPPlayer_get_closedCaption(This,ppClosedCaption) (This)->lpVtbl->get_closedCaption(This,ppClosedCaption)
#define IWMPPlayer_get_isOnline(This,pfOnline) (This)->lpVtbl->get_isOnline(This,pfOnline)
#define IWMPPlayer_get_Error(This,ppError) (This)->lpVtbl->get_Error(This,ppError)
#define IWMPPlayer_get_status(This,pbstrStatus) (This)->lpVtbl->get_status(This,pbstrStatus)
/*** IWMPPlayer methods ***/
#define IWMPPlayer_get_enabled(This,pbEnabled) (This)->lpVtbl->get_enabled(This,pbEnabled)
#define IWMPPlayer_put_enabled(This,pbEnabled) (This)->lpVtbl->put_enabled(This,pbEnabled)
#define IWMPPlayer_get_fullScreen(This,pbFullScreen) (This)->lpVtbl->get_fullScreen(This,pbFullScreen)
#define IWMPPlayer_put_fullScreen(This,pbFullScreen) (This)->lpVtbl->put_fullScreen(This,pbFullScreen)
#define IWMPPlayer_get_enableContextMenu(This,pbEnableContextMenu) (This)->lpVtbl->get_enableContextMenu(This,pbEnableContextMenu)
#define IWMPPlayer_put_enableContextMenu(This,pbEnableContextMenu) (This)->lpVtbl->put_enableContextMenu(This,pbEnableContextMenu)
#define IWMPPlayer_put_uiMode(This,pbstrMode) (This)->lpVtbl->put_uiMode(This,pbstrMode)
#define IWMPPlayer_get_uiMode(This,pbstrMode) (This)->lpVtbl->get_uiMode(This,pbstrMode)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPPlayer_QueryInterface(IWMPPlayer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPPlayer_AddRef(IWMPPlayer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPPlayer_Release(IWMPPlayer* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT IWMPPlayer_GetTypeInfoCount(IWMPPlayer* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT IWMPPlayer_GetTypeInfo(IWMPPlayer* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT IWMPPlayer_GetIDsOfNames(IWMPPlayer* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT IWMPPlayer_Invoke(IWMPPlayer* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
/*** IWMPCore methods ***/
static inline HRESULT IWMPPlayer_close(IWMPPlayer* This) {
    return This->lpVtbl->close(This);
}
static inline HRESULT IWMPPlayer_get_URL(IWMPPlayer* This,BSTR *pbstrURL) {
    return This->lpVtbl->get_URL(This,pbstrURL);
}
static inline HRESULT IWMPPlayer_put_URL(IWMPPlayer* This,BSTR pbstrURL) {
    return This->lpVtbl->put_URL(This,pbstrURL);
}
static inline HRESULT IWMPPlayer_get_openState(IWMPPlayer* This,WMPOpenState *pwmpos) {
    return This->lpVtbl->get_openState(This,pwmpos);
}
static inline HRESULT IWMPPlayer_get_playState(IWMPPlayer* This,WMPPlayState *pwmpps) {
    return This->lpVtbl->get_playState(This,pwmpps);
}
static inline HRESULT IWMPPlayer_get_controls(IWMPPlayer* This,IWMPControls **ppControl) {
    return This->lpVtbl->get_controls(This,ppControl);
}
static inline HRESULT IWMPPlayer_get_settings(IWMPPlayer* This,IWMPSettings **ppSettings) {
    return This->lpVtbl->get_settings(This,ppSettings);
}
static inline HRESULT IWMPPlayer_get_currentMedia(IWMPPlayer* This,IWMPMedia **ppMedia) {
    return This->lpVtbl->get_currentMedia(This,ppMedia);
}
static inline HRESULT IWMPPlayer_put_currentMedia(IWMPPlayer* This,IWMPMedia *ppMedia) {
    return This->lpVtbl->put_currentMedia(This,ppMedia);
}
static inline HRESULT IWMPPlayer_get_mediaCollection(IWMPPlayer* This,IWMPMediaCollection **ppMediaCollection) {
    return This->lpVtbl->get_mediaCollection(This,ppMediaCollection);
}
static inline HRESULT IWMPPlayer_get_playlistCollection(IWMPPlayer* This,IWMPPlaylistCollection **ppPlaylistCollection) {
    return This->lpVtbl->get_playlistCollection(This,ppPlaylistCollection);
}
static inline HRESULT IWMPPlayer_get_versionInfo(IWMPPlayer* This,BSTR *pbstrVersionInfo) {
    return This->lpVtbl->get_versionInfo(This,pbstrVersionInfo);
}
static inline HRESULT IWMPPlayer_launchURL(IWMPPlayer* This,BSTR bstrURL) {
    return This->lpVtbl->launchURL(This,bstrURL);
}
static inline HRESULT IWMPPlayer_get_network(IWMPPlayer* This,IWMPNetwork **ppQNI) {
    return This->lpVtbl->get_network(This,ppQNI);
}
static inline HRESULT IWMPPlayer_get_currentPlaylist(IWMPPlayer* This,IWMPPlaylist **ppPL) {
    return This->lpVtbl->get_currentPlaylist(This,ppPL);
}
static inline HRESULT IWMPPlayer_put_currentPlaylist(IWMPPlayer* This,IWMPPlaylist *ppPL) {
    return This->lpVtbl->put_currentPlaylist(This,ppPL);
}
static inline HRESULT IWMPPlayer_get_cdromCollection(IWMPPlayer* This,IWMPCdromCollection **ppCdromCollection) {
    return This->lpVtbl->get_cdromCollection(This,ppCdromCollection);
}
static inline HRESULT IWMPPlayer_get_closedCaption(IWMPPlayer* This,IWMPClosedCaption **ppClosedCaption) {
    return This->lpVtbl->get_closedCaption(This,ppClosedCaption);
}
static inline HRESULT IWMPPlayer_get_isOnline(IWMPPlayer* This,VARIANT_BOOL *pfOnline) {
    return This->lpVtbl->get_isOnline(This,pfOnline);
}
static inline HRESULT IWMPPlayer_get_Error(IWMPPlayer* This,IWMPError **ppError) {
    return This->lpVtbl->get_Error(This,ppError);
}
static inline HRESULT IWMPPlayer_get_status(IWMPPlayer* This,BSTR *pbstrStatus) {
    return This->lpVtbl->get_status(This,pbstrStatus);
}
/*** IWMPPlayer methods ***/
static inline HRESULT IWMPPlayer_get_enabled(IWMPPlayer* This,VARIANT_BOOL *pbEnabled) {
    return This->lpVtbl->get_enabled(This,pbEnabled);
}
static inline HRESULT IWMPPlayer_put_enabled(IWMPPlayer* This,VARIANT_BOOL pbEnabled) {
    return This->lpVtbl->put_enabled(This,pbEnabled);
}
static inline HRESULT IWMPPlayer_get_fullScreen(IWMPPlayer* This,VARIANT_BOOL *pbFullScreen) {
    return This->lpVtbl->get_fullScreen(This,pbFullScreen);
}
static inline HRESULT IWMPPlayer_put_fullScreen(IWMPPlayer* This,VARIANT_BOOL pbFullScreen) {
    return This->lpVtbl->put_fullScreen(This,pbFullScreen);
}
static inline HRESULT IWMPPlayer_get_enableContextMenu(IWMPPlayer* This,VARIANT_BOOL *pbEnableContextMenu) {
    return This->lpVtbl->get_enableContextMenu(This,pbEnableContextMenu);
}
static inline HRESULT IWMPPlayer_put_enableContextMenu(IWMPPlayer* This,VARIANT_BOOL pbEnableContextMenu) {
    return This->lpVtbl->put_enableContextMenu(This,pbEnableContextMenu);
}
static inline HRESULT IWMPPlayer_put_uiMode(IWMPPlayer* This,BSTR pbstrMode) {
    return This->lpVtbl->put_uiMode(This,pbstrMode);
}
static inline HRESULT IWMPPlayer_get_uiMode(IWMPPlayer* This,BSTR *pbstrMode) {
    return This->lpVtbl->get_uiMode(This,pbstrMode);
}
#endif
#endif

#endif


#endif  /* __IWMPPlayer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPSyncDevice interface
 */
#ifndef __IWMPSyncDevice_INTERFACE_DEFINED__
#define __IWMPSyncDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPSyncDevice, 0x82a2986c, 0x0293, 0x4fd0, 0xb2,0x79, 0xb2,0x1b,0x86,0xc0,0x58,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("82a2986c-0293-4fd0-b279-b21b86c058be")
IWMPSyncDevice : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_friendlyName(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_friendlyName(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_deviceName(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_deviceId(
        BSTR *device) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_partnershipIndex(
        LONG *index) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_connected(
        VARIANT_BOOL *connected) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_status(
        WMPDeviceStatus *status) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_syncState(
        WMPSyncState *state) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_progress(
        LONG *progress) = 0;

    virtual HRESULT STDMETHODCALLTYPE getItemInfo(
        BSTR name,
        BSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE createPartnership(
        VARIANT_BOOL showui) = 0;

    virtual HRESULT STDMETHODCALLTYPE deletePartnership(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE start(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE stop(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE showSettings(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE isIdentical(
        IWMPSyncDevice *device,
        VARIANT_BOOL *ret) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPSyncDevice, 0x82a2986c, 0x0293, 0x4fd0, 0xb2,0x79, 0xb2,0x1b,0x86,0xc0,0x58,0xbe)
#endif
#else
typedef struct IWMPSyncDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPSyncDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPSyncDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPSyncDevice *This);

    /*** IWMPSyncDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *get_friendlyName)(
        IWMPSyncDevice *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_friendlyName)(
        IWMPSyncDevice *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_deviceName)(
        IWMPSyncDevice *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_deviceId)(
        IWMPSyncDevice *This,
        BSTR *device);

    HRESULT (STDMETHODCALLTYPE *get_partnershipIndex)(
        IWMPSyncDevice *This,
        LONG *index);

    HRESULT (STDMETHODCALLTYPE *get_connected)(
        IWMPSyncDevice *This,
        VARIANT_BOOL *connected);

    HRESULT (STDMETHODCALLTYPE *get_status)(
        IWMPSyncDevice *This,
        WMPDeviceStatus *status);

    HRESULT (STDMETHODCALLTYPE *get_syncState)(
        IWMPSyncDevice *This,
        WMPSyncState *state);

    HRESULT (STDMETHODCALLTYPE *get_progress)(
        IWMPSyncDevice *This,
        LONG *progress);

    HRESULT (STDMETHODCALLTYPE *getItemInfo)(
        IWMPSyncDevice *This,
        BSTR name,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *createPartnership)(
        IWMPSyncDevice *This,
        VARIANT_BOOL showui);

    HRESULT (STDMETHODCALLTYPE *deletePartnership)(
        IWMPSyncDevice *This);

    HRESULT (STDMETHODCALLTYPE *start)(
        IWMPSyncDevice *This);

    HRESULT (STDMETHODCALLTYPE *stop)(
        IWMPSyncDevice *This);

    HRESULT (STDMETHODCALLTYPE *showSettings)(
        IWMPSyncDevice *This);

    HRESULT (STDMETHODCALLTYPE *isIdentical)(
        IWMPSyncDevice *This,
        IWMPSyncDevice *device,
        VARIANT_BOOL *ret);

    END_INTERFACE
} IWMPSyncDeviceVtbl;

interface IWMPSyncDevice {
    CONST_VTBL IWMPSyncDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPSyncDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPSyncDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPSyncDevice_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPSyncDevice methods ***/
#define IWMPSyncDevice_get_friendlyName(This,name) (This)->lpVtbl->get_friendlyName(This,name)
#define IWMPSyncDevice_put_friendlyName(This,name) (This)->lpVtbl->put_friendlyName(This,name)
#define IWMPSyncDevice_get_deviceName(This,name) (This)->lpVtbl->get_deviceName(This,name)
#define IWMPSyncDevice_get_deviceId(This,device) (This)->lpVtbl->get_deviceId(This,device)
#define IWMPSyncDevice_get_partnershipIndex(This,index) (This)->lpVtbl->get_partnershipIndex(This,index)
#define IWMPSyncDevice_get_connected(This,connected) (This)->lpVtbl->get_connected(This,connected)
#define IWMPSyncDevice_get_status(This,status) (This)->lpVtbl->get_status(This,status)
#define IWMPSyncDevice_get_syncState(This,state) (This)->lpVtbl->get_syncState(This,state)
#define IWMPSyncDevice_get_progress(This,progress) (This)->lpVtbl->get_progress(This,progress)
#define IWMPSyncDevice_getItemInfo(This,name,val) (This)->lpVtbl->getItemInfo(This,name,val)
#define IWMPSyncDevice_createPartnership(This,showui) (This)->lpVtbl->createPartnership(This,showui)
#define IWMPSyncDevice_deletePartnership(This) (This)->lpVtbl->deletePartnership(This)
#define IWMPSyncDevice_start(This) (This)->lpVtbl->start(This)
#define IWMPSyncDevice_stop(This) (This)->lpVtbl->stop(This)
#define IWMPSyncDevice_showSettings(This) (This)->lpVtbl->showSettings(This)
#define IWMPSyncDevice_isIdentical(This,device,ret) (This)->lpVtbl->isIdentical(This,device,ret)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPSyncDevice_QueryInterface(IWMPSyncDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPSyncDevice_AddRef(IWMPSyncDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPSyncDevice_Release(IWMPSyncDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPSyncDevice methods ***/
static inline HRESULT IWMPSyncDevice_get_friendlyName(IWMPSyncDevice* This,BSTR *name) {
    return This->lpVtbl->get_friendlyName(This,name);
}
static inline HRESULT IWMPSyncDevice_put_friendlyName(IWMPSyncDevice* This,BSTR name) {
    return This->lpVtbl->put_friendlyName(This,name);
}
static inline HRESULT IWMPSyncDevice_get_deviceName(IWMPSyncDevice* This,BSTR *name) {
    return This->lpVtbl->get_deviceName(This,name);
}
static inline HRESULT IWMPSyncDevice_get_deviceId(IWMPSyncDevice* This,BSTR *device) {
    return This->lpVtbl->get_deviceId(This,device);
}
static inline HRESULT IWMPSyncDevice_get_partnershipIndex(IWMPSyncDevice* This,LONG *index) {
    return This->lpVtbl->get_partnershipIndex(This,index);
}
static inline HRESULT IWMPSyncDevice_get_connected(IWMPSyncDevice* This,VARIANT_BOOL *connected) {
    return This->lpVtbl->get_connected(This,connected);
}
static inline HRESULT IWMPSyncDevice_get_status(IWMPSyncDevice* This,WMPDeviceStatus *status) {
    return This->lpVtbl->get_status(This,status);
}
static inline HRESULT IWMPSyncDevice_get_syncState(IWMPSyncDevice* This,WMPSyncState *state) {
    return This->lpVtbl->get_syncState(This,state);
}
static inline HRESULT IWMPSyncDevice_get_progress(IWMPSyncDevice* This,LONG *progress) {
    return This->lpVtbl->get_progress(This,progress);
}
static inline HRESULT IWMPSyncDevice_getItemInfo(IWMPSyncDevice* This,BSTR name,BSTR *val) {
    return This->lpVtbl->getItemInfo(This,name,val);
}
static inline HRESULT IWMPSyncDevice_createPartnership(IWMPSyncDevice* This,VARIANT_BOOL showui) {
    return This->lpVtbl->createPartnership(This,showui);
}
static inline HRESULT IWMPSyncDevice_deletePartnership(IWMPSyncDevice* This) {
    return This->lpVtbl->deletePartnership(This);
}
static inline HRESULT IWMPSyncDevice_start(IWMPSyncDevice* This) {
    return This->lpVtbl->start(This);
}
static inline HRESULT IWMPSyncDevice_stop(IWMPSyncDevice* This) {
    return This->lpVtbl->stop(This);
}
static inline HRESULT IWMPSyncDevice_showSettings(IWMPSyncDevice* This) {
    return This->lpVtbl->showSettings(This);
}
static inline HRESULT IWMPSyncDevice_isIdentical(IWMPSyncDevice* This,IWMPSyncDevice *device,VARIANT_BOOL *ret) {
    return This->lpVtbl->isIdentical(This,device,ret);
}
#endif
#endif

#endif


#endif  /* __IWMPSyncDevice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPSyncDevice2 interface
 */
#ifndef __IWMPSyncDevice2_INTERFACE_DEFINED__
#define __IWMPSyncDevice2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPSyncDevice2, 0x88afb4b2, 0x140a, 0x44d2, 0x91,0xe6, 0x45,0x43,0xda,0x46,0x7c,0xd1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("88afb4b2-140a-44d2-91e6-4543da467cd1")
IWMPSyncDevice2 : public IWMPSyncDevice
{
    virtual HRESULT STDMETHODCALLTYPE setItemInfo(
        BSTR name,
        BSTR val) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPSyncDevice2, 0x88afb4b2, 0x140a, 0x44d2, 0x91,0xe6, 0x45,0x43,0xda,0x46,0x7c,0xd1)
#endif
#else
typedef struct IWMPSyncDevice2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPSyncDevice2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPSyncDevice2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPSyncDevice2 *This);

    /*** IWMPSyncDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *get_friendlyName)(
        IWMPSyncDevice2 *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *put_friendlyName)(
        IWMPSyncDevice2 *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *get_deviceName)(
        IWMPSyncDevice2 *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_deviceId)(
        IWMPSyncDevice2 *This,
        BSTR *device);

    HRESULT (STDMETHODCALLTYPE *get_partnershipIndex)(
        IWMPSyncDevice2 *This,
        LONG *index);

    HRESULT (STDMETHODCALLTYPE *get_connected)(
        IWMPSyncDevice2 *This,
        VARIANT_BOOL *connected);

    HRESULT (STDMETHODCALLTYPE *get_status)(
        IWMPSyncDevice2 *This,
        WMPDeviceStatus *status);

    HRESULT (STDMETHODCALLTYPE *get_syncState)(
        IWMPSyncDevice2 *This,
        WMPSyncState *state);

    HRESULT (STDMETHODCALLTYPE *get_progress)(
        IWMPSyncDevice2 *This,
        LONG *progress);

    HRESULT (STDMETHODCALLTYPE *getItemInfo)(
        IWMPSyncDevice2 *This,
        BSTR name,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *createPartnership)(
        IWMPSyncDevice2 *This,
        VARIANT_BOOL showui);

    HRESULT (STDMETHODCALLTYPE *deletePartnership)(
        IWMPSyncDevice2 *This);

    HRESULT (STDMETHODCALLTYPE *start)(
        IWMPSyncDevice2 *This);

    HRESULT (STDMETHODCALLTYPE *stop)(
        IWMPSyncDevice2 *This);

    HRESULT (STDMETHODCALLTYPE *showSettings)(
        IWMPSyncDevice2 *This);

    HRESULT (STDMETHODCALLTYPE *isIdentical)(
        IWMPSyncDevice2 *This,
        IWMPSyncDevice *device,
        VARIANT_BOOL *ret);

    /*** IWMPSyncDevice2 methods ***/
    HRESULT (STDMETHODCALLTYPE *setItemInfo)(
        IWMPSyncDevice2 *This,
        BSTR name,
        BSTR val);

    END_INTERFACE
} IWMPSyncDevice2Vtbl;

interface IWMPSyncDevice2 {
    CONST_VTBL IWMPSyncDevice2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPSyncDevice2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPSyncDevice2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPSyncDevice2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPSyncDevice methods ***/
#define IWMPSyncDevice2_get_friendlyName(This,name) (This)->lpVtbl->get_friendlyName(This,name)
#define IWMPSyncDevice2_put_friendlyName(This,name) (This)->lpVtbl->put_friendlyName(This,name)
#define IWMPSyncDevice2_get_deviceName(This,name) (This)->lpVtbl->get_deviceName(This,name)
#define IWMPSyncDevice2_get_deviceId(This,device) (This)->lpVtbl->get_deviceId(This,device)
#define IWMPSyncDevice2_get_partnershipIndex(This,index) (This)->lpVtbl->get_partnershipIndex(This,index)
#define IWMPSyncDevice2_get_connected(This,connected) (This)->lpVtbl->get_connected(This,connected)
#define IWMPSyncDevice2_get_status(This,status) (This)->lpVtbl->get_status(This,status)
#define IWMPSyncDevice2_get_syncState(This,state) (This)->lpVtbl->get_syncState(This,state)
#define IWMPSyncDevice2_get_progress(This,progress) (This)->lpVtbl->get_progress(This,progress)
#define IWMPSyncDevice2_getItemInfo(This,name,val) (This)->lpVtbl->getItemInfo(This,name,val)
#define IWMPSyncDevice2_createPartnership(This,showui) (This)->lpVtbl->createPartnership(This,showui)
#define IWMPSyncDevice2_deletePartnership(This) (This)->lpVtbl->deletePartnership(This)
#define IWMPSyncDevice2_start(This) (This)->lpVtbl->start(This)
#define IWMPSyncDevice2_stop(This) (This)->lpVtbl->stop(This)
#define IWMPSyncDevice2_showSettings(This) (This)->lpVtbl->showSettings(This)
#define IWMPSyncDevice2_isIdentical(This,device,ret) (This)->lpVtbl->isIdentical(This,device,ret)
/*** IWMPSyncDevice2 methods ***/
#define IWMPSyncDevice2_setItemInfo(This,name,val) (This)->lpVtbl->setItemInfo(This,name,val)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPSyncDevice2_QueryInterface(IWMPSyncDevice2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPSyncDevice2_AddRef(IWMPSyncDevice2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPSyncDevice2_Release(IWMPSyncDevice2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPSyncDevice methods ***/
static inline HRESULT IWMPSyncDevice2_get_friendlyName(IWMPSyncDevice2* This,BSTR *name) {
    return This->lpVtbl->get_friendlyName(This,name);
}
static inline HRESULT IWMPSyncDevice2_put_friendlyName(IWMPSyncDevice2* This,BSTR name) {
    return This->lpVtbl->put_friendlyName(This,name);
}
static inline HRESULT IWMPSyncDevice2_get_deviceName(IWMPSyncDevice2* This,BSTR *name) {
    return This->lpVtbl->get_deviceName(This,name);
}
static inline HRESULT IWMPSyncDevice2_get_deviceId(IWMPSyncDevice2* This,BSTR *device) {
    return This->lpVtbl->get_deviceId(This,device);
}
static inline HRESULT IWMPSyncDevice2_get_partnershipIndex(IWMPSyncDevice2* This,LONG *index) {
    return This->lpVtbl->get_partnershipIndex(This,index);
}
static inline HRESULT IWMPSyncDevice2_get_connected(IWMPSyncDevice2* This,VARIANT_BOOL *connected) {
    return This->lpVtbl->get_connected(This,connected);
}
static inline HRESULT IWMPSyncDevice2_get_status(IWMPSyncDevice2* This,WMPDeviceStatus *status) {
    return This->lpVtbl->get_status(This,status);
}
static inline HRESULT IWMPSyncDevice2_get_syncState(IWMPSyncDevice2* This,WMPSyncState *state) {
    return This->lpVtbl->get_syncState(This,state);
}
static inline HRESULT IWMPSyncDevice2_get_progress(IWMPSyncDevice2* This,LONG *progress) {
    return This->lpVtbl->get_progress(This,progress);
}
static inline HRESULT IWMPSyncDevice2_getItemInfo(IWMPSyncDevice2* This,BSTR name,BSTR *val) {
    return This->lpVtbl->getItemInfo(This,name,val);
}
static inline HRESULT IWMPSyncDevice2_createPartnership(IWMPSyncDevice2* This,VARIANT_BOOL showui) {
    return This->lpVtbl->createPartnership(This,showui);
}
static inline HRESULT IWMPSyncDevice2_deletePartnership(IWMPSyncDevice2* This) {
    return This->lpVtbl->deletePartnership(This);
}
static inline HRESULT IWMPSyncDevice2_start(IWMPSyncDevice2* This) {
    return This->lpVtbl->start(This);
}
static inline HRESULT IWMPSyncDevice2_stop(IWMPSyncDevice2* This) {
    return This->lpVtbl->stop(This);
}
static inline HRESULT IWMPSyncDevice2_showSettings(IWMPSyncDevice2* This) {
    return This->lpVtbl->showSettings(This);
}
static inline HRESULT IWMPSyncDevice2_isIdentical(IWMPSyncDevice2* This,IWMPSyncDevice *device,VARIANT_BOOL *ret) {
    return This->lpVtbl->isIdentical(This,device,ret);
}
/*** IWMPSyncDevice2 methods ***/
static inline HRESULT IWMPSyncDevice2_setItemInfo(IWMPSyncDevice2* This,BSTR name,BSTR val) {
    return This->lpVtbl->setItemInfo(This,name,val);
}
#endif
#endif

#endif


#endif  /* __IWMPSyncDevice2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPCdromRip interface
 */
#ifndef __IWMPCdromRip_INTERFACE_DEFINED__
#define __IWMPCdromRip_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPCdromRip, 0x56e2294f, 0x69ed, 0x4629, 0xa8,0x69, 0xae,0xa7,0x2c,0x0d,0xcc,0x2c);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("56e2294f-69ed-4629-a869-aea72c0dcc2c")
IWMPCdromRip : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_ripState(
        WMPRipState *state) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_ripProgress(
        LONG *progress) = 0;

    virtual HRESULT STDMETHODCALLTYPE startRip(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE stopRip(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPCdromRip, 0x56e2294f, 0x69ed, 0x4629, 0xa8,0x69, 0xae,0xa7,0x2c,0x0d,0xcc,0x2c)
#endif
#else
typedef struct IWMPCdromRipVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPCdromRip *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPCdromRip *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPCdromRip *This);

    /*** IWMPCdromRip methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ripState)(
        IWMPCdromRip *This,
        WMPRipState *state);

    HRESULT (STDMETHODCALLTYPE *get_ripProgress)(
        IWMPCdromRip *This,
        LONG *progress);

    HRESULT (STDMETHODCALLTYPE *startRip)(
        IWMPCdromRip *This);

    HRESULT (STDMETHODCALLTYPE *stopRip)(
        IWMPCdromRip *This);

    END_INTERFACE
} IWMPCdromRipVtbl;

interface IWMPCdromRip {
    CONST_VTBL IWMPCdromRipVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPCdromRip_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPCdromRip_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPCdromRip_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPCdromRip methods ***/
#define IWMPCdromRip_get_ripState(This,state) (This)->lpVtbl->get_ripState(This,state)
#define IWMPCdromRip_get_ripProgress(This,progress) (This)->lpVtbl->get_ripProgress(This,progress)
#define IWMPCdromRip_startRip(This) (This)->lpVtbl->startRip(This)
#define IWMPCdromRip_stopRip(This) (This)->lpVtbl->stopRip(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPCdromRip_QueryInterface(IWMPCdromRip* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPCdromRip_AddRef(IWMPCdromRip* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPCdromRip_Release(IWMPCdromRip* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPCdromRip methods ***/
static inline HRESULT IWMPCdromRip_get_ripState(IWMPCdromRip* This,WMPRipState *state) {
    return This->lpVtbl->get_ripState(This,state);
}
static inline HRESULT IWMPCdromRip_get_ripProgress(IWMPCdromRip* This,LONG *progress) {
    return This->lpVtbl->get_ripProgress(This,progress);
}
static inline HRESULT IWMPCdromRip_startRip(IWMPCdromRip* This) {
    return This->lpVtbl->startRip(This);
}
static inline HRESULT IWMPCdromRip_stopRip(IWMPCdromRip* This) {
    return This->lpVtbl->stopRip(This);
}
#endif
#endif

#endif


#endif  /* __IWMPCdromRip_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPCdromBurn interface
 */
#ifndef __IWMPCdromBurn_INTERFACE_DEFINED__
#define __IWMPCdromBurn_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPCdromBurn, 0xbd94dbeb, 0x417f, 0x4928, 0xaa,0x06, 0x08,0x7d,0x56,0xed,0x9b,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("bd94dbeb-417f-4928-aa06-087d56ed9b59")
IWMPCdromBurn : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE isAvailable(
        BSTR item,
        VARIANT_BOOL *available) = 0;

    virtual HRESULT STDMETHODCALLTYPE getItemInfo(
        BSTR item,
        BSTR *val) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_label(
        BSTR *label) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_label(
        BSTR label) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_burnFormat(
        WMPBurnFormat *format) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_burnFormat(
        WMPBurnFormat format) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_burnPlaylist(
        IWMPPlaylist **playlist) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_burnPlaylist(
        IWMPPlaylist *playlist) = 0;

    virtual HRESULT STDMETHODCALLTYPE refreshStatus(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_burnState(
        WMPBurnState *state) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_burnProgress(
        LONG *progress) = 0;

    virtual HRESULT STDMETHODCALLTYPE startBurn(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE stopBurn(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE erase(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPCdromBurn, 0xbd94dbeb, 0x417f, 0x4928, 0xaa,0x06, 0x08,0x7d,0x56,0xed,0x9b,0x59)
#endif
#else
typedef struct IWMPCdromBurnVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPCdromBurn *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPCdromBurn *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPCdromBurn *This);

    /*** IWMPCdromBurn methods ***/
    HRESULT (STDMETHODCALLTYPE *isAvailable)(
        IWMPCdromBurn *This,
        BSTR item,
        VARIANT_BOOL *available);

    HRESULT (STDMETHODCALLTYPE *getItemInfo)(
        IWMPCdromBurn *This,
        BSTR item,
        BSTR *val);

    HRESULT (STDMETHODCALLTYPE *get_label)(
        IWMPCdromBurn *This,
        BSTR *label);

    HRESULT (STDMETHODCALLTYPE *put_label)(
        IWMPCdromBurn *This,
        BSTR label);

    HRESULT (STDMETHODCALLTYPE *get_burnFormat)(
        IWMPCdromBurn *This,
        WMPBurnFormat *format);

    HRESULT (STDMETHODCALLTYPE *put_burnFormat)(
        IWMPCdromBurn *This,
        WMPBurnFormat format);

    HRESULT (STDMETHODCALLTYPE *get_burnPlaylist)(
        IWMPCdromBurn *This,
        IWMPPlaylist **playlist);

    HRESULT (STDMETHODCALLTYPE *put_burnPlaylist)(
        IWMPCdromBurn *This,
        IWMPPlaylist *playlist);

    HRESULT (STDMETHODCALLTYPE *refreshStatus)(
        IWMPCdromBurn *This);

    HRESULT (STDMETHODCALLTYPE *get_burnState)(
        IWMPCdromBurn *This,
        WMPBurnState *state);

    HRESULT (STDMETHODCALLTYPE *get_burnProgress)(
        IWMPCdromBurn *This,
        LONG *progress);

    HRESULT (STDMETHODCALLTYPE *startBurn)(
        IWMPCdromBurn *This);

    HRESULT (STDMETHODCALLTYPE *stopBurn)(
        IWMPCdromBurn *This);

    HRESULT (STDMETHODCALLTYPE *erase)(
        IWMPCdromBurn *This);

    END_INTERFACE
} IWMPCdromBurnVtbl;

interface IWMPCdromBurn {
    CONST_VTBL IWMPCdromBurnVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPCdromBurn_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPCdromBurn_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPCdromBurn_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPCdromBurn methods ***/
#define IWMPCdromBurn_isAvailable(This,item,available) (This)->lpVtbl->isAvailable(This,item,available)
#define IWMPCdromBurn_getItemInfo(This,item,val) (This)->lpVtbl->getItemInfo(This,item,val)
#define IWMPCdromBurn_get_label(This,label) (This)->lpVtbl->get_label(This,label)
#define IWMPCdromBurn_put_label(This,label) (This)->lpVtbl->put_label(This,label)
#define IWMPCdromBurn_get_burnFormat(This,format) (This)->lpVtbl->get_burnFormat(This,format)
#define IWMPCdromBurn_put_burnFormat(This,format) (This)->lpVtbl->put_burnFormat(This,format)
#define IWMPCdromBurn_get_burnPlaylist(This,playlist) (This)->lpVtbl->get_burnPlaylist(This,playlist)
#define IWMPCdromBurn_put_burnPlaylist(This,playlist) (This)->lpVtbl->put_burnPlaylist(This,playlist)
#define IWMPCdromBurn_refreshStatus(This) (This)->lpVtbl->refreshStatus(This)
#define IWMPCdromBurn_get_burnState(This,state) (This)->lpVtbl->get_burnState(This,state)
#define IWMPCdromBurn_get_burnProgress(This,progress) (This)->lpVtbl->get_burnProgress(This,progress)
#define IWMPCdromBurn_startBurn(This) (This)->lpVtbl->startBurn(This)
#define IWMPCdromBurn_stopBurn(This) (This)->lpVtbl->stopBurn(This)
#define IWMPCdromBurn_erase(This) (This)->lpVtbl->erase(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPCdromBurn_QueryInterface(IWMPCdromBurn* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPCdromBurn_AddRef(IWMPCdromBurn* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPCdromBurn_Release(IWMPCdromBurn* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPCdromBurn methods ***/
static inline HRESULT IWMPCdromBurn_isAvailable(IWMPCdromBurn* This,BSTR item,VARIANT_BOOL *available) {
    return This->lpVtbl->isAvailable(This,item,available);
}
static inline HRESULT IWMPCdromBurn_getItemInfo(IWMPCdromBurn* This,BSTR item,BSTR *val) {
    return This->lpVtbl->getItemInfo(This,item,val);
}
static inline HRESULT IWMPCdromBurn_get_label(IWMPCdromBurn* This,BSTR *label) {
    return This->lpVtbl->get_label(This,label);
}
static inline HRESULT IWMPCdromBurn_put_label(IWMPCdromBurn* This,BSTR label) {
    return This->lpVtbl->put_label(This,label);
}
static inline HRESULT IWMPCdromBurn_get_burnFormat(IWMPCdromBurn* This,WMPBurnFormat *format) {
    return This->lpVtbl->get_burnFormat(This,format);
}
static inline HRESULT IWMPCdromBurn_put_burnFormat(IWMPCdromBurn* This,WMPBurnFormat format) {
    return This->lpVtbl->put_burnFormat(This,format);
}
static inline HRESULT IWMPCdromBurn_get_burnPlaylist(IWMPCdromBurn* This,IWMPPlaylist **playlist) {
    return This->lpVtbl->get_burnPlaylist(This,playlist);
}
static inline HRESULT IWMPCdromBurn_put_burnPlaylist(IWMPCdromBurn* This,IWMPPlaylist *playlist) {
    return This->lpVtbl->put_burnPlaylist(This,playlist);
}
static inline HRESULT IWMPCdromBurn_refreshStatus(IWMPCdromBurn* This) {
    return This->lpVtbl->refreshStatus(This);
}
static inline HRESULT IWMPCdromBurn_get_burnState(IWMPCdromBurn* This,WMPBurnState *state) {
    return This->lpVtbl->get_burnState(This,state);
}
static inline HRESULT IWMPCdromBurn_get_burnProgress(IWMPCdromBurn* This,LONG *progress) {
    return This->lpVtbl->get_burnProgress(This,progress);
}
static inline HRESULT IWMPCdromBurn_startBurn(IWMPCdromBurn* This) {
    return This->lpVtbl->startBurn(This);
}
static inline HRESULT IWMPCdromBurn_stopBurn(IWMPCdromBurn* This) {
    return This->lpVtbl->stopBurn(This);
}
static inline HRESULT IWMPCdromBurn_erase(IWMPCdromBurn* This) {
    return This->lpVtbl->erase(This);
}
#endif
#endif

#endif


#endif  /* __IWMPCdromBurn_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPLibrary interface
 */
#ifndef __IWMPLibrary_INTERFACE_DEFINED__
#define __IWMPLibrary_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPLibrary, 0x3df47861, 0x7df1, 0x4c1f, 0xa8,0x1b, 0x4c,0x26,0xf0,0xf7,0xa7,0xc6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3df47861-7df1-4c1f-a81b-4c26f0f7a7c6")
IWMPLibrary : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_name(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_type(
        WMPLibraryType *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_mediaCollection(
        IWMPMediaCollection **collection) = 0;

    virtual HRESULT STDMETHODCALLTYPE isIdentical(
        IWMPLibrary *wmplibrary,
        VARIANT_BOOL *ret) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPLibrary, 0x3df47861, 0x7df1, 0x4c1f, 0xa8,0x1b, 0x4c,0x26,0xf0,0xf7,0xa7,0xc6)
#endif
#else
typedef struct IWMPLibraryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPLibrary *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPLibrary *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPLibrary *This);

    /*** IWMPLibrary methods ***/
    HRESULT (STDMETHODCALLTYPE *get_name)(
        IWMPLibrary *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *get_type)(
        IWMPLibrary *This,
        WMPLibraryType *type);

    HRESULT (STDMETHODCALLTYPE *get_mediaCollection)(
        IWMPLibrary *This,
        IWMPMediaCollection **collection);

    HRESULT (STDMETHODCALLTYPE *isIdentical)(
        IWMPLibrary *This,
        IWMPLibrary *wmplibrary,
        VARIANT_BOOL *ret);

    END_INTERFACE
} IWMPLibraryVtbl;

interface IWMPLibrary {
    CONST_VTBL IWMPLibraryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPLibrary_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPLibrary_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPLibrary_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPLibrary methods ***/
#define IWMPLibrary_get_name(This,name) (This)->lpVtbl->get_name(This,name)
#define IWMPLibrary_get_type(This,type) (This)->lpVtbl->get_type(This,type)
#define IWMPLibrary_get_mediaCollection(This,collection) (This)->lpVtbl->get_mediaCollection(This,collection)
#define IWMPLibrary_isIdentical(This,wmplibrary,ret) (This)->lpVtbl->isIdentical(This,wmplibrary,ret)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPLibrary_QueryInterface(IWMPLibrary* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPLibrary_AddRef(IWMPLibrary* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPLibrary_Release(IWMPLibrary* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPLibrary methods ***/
static inline HRESULT IWMPLibrary_get_name(IWMPLibrary* This,BSTR *name) {
    return This->lpVtbl->get_name(This,name);
}
static inline HRESULT IWMPLibrary_get_type(IWMPLibrary* This,WMPLibraryType *type) {
    return This->lpVtbl->get_type(This,type);
}
static inline HRESULT IWMPLibrary_get_mediaCollection(IWMPLibrary* This,IWMPMediaCollection **collection) {
    return This->lpVtbl->get_mediaCollection(This,collection);
}
static inline HRESULT IWMPLibrary_isIdentical(IWMPLibrary* This,IWMPLibrary *wmplibrary,VARIANT_BOOL *ret) {
    return This->lpVtbl->isIdentical(This,wmplibrary,ret);
}
#endif
#endif

#endif


#endif  /* __IWMPLibrary_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPEvents interface
 */
#ifndef __IWMPEvents_INTERFACE_DEFINED__
#define __IWMPEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPEvents, 0x19a6627b, 0xda9e, 0x47c1, 0xbb,0x23, 0x00,0xb5,0xe6,0x68,0x23,0x6a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("19a6627b-da9e-47c1-bb23-00b5e668236a")
IWMPEvents : public IUnknown
{
    virtual void STDMETHODCALLTYPE OpenStateChange(
        LONG state) = 0;

    virtual void STDMETHODCALLTYPE PlayStateChange(
        LONG state) = 0;

    virtual void STDMETHODCALLTYPE AudioLanguageChange(
        LONG lang) = 0;

    virtual void STDMETHODCALLTYPE StatusChange(
        ) = 0;

    virtual void STDMETHODCALLTYPE ScriptCommand(
        BSTR type,
        BSTR param) = 0;

    virtual void STDMETHODCALLTYPE NewStream(
        ) = 0;

    virtual void STDMETHODCALLTYPE Disconnect(
        LONG result) = 0;

    virtual void STDMETHODCALLTYPE Buffering(
        VARIANT_BOOL start) = 0;

    virtual void STDMETHODCALLTYPE Error(
        ) = 0;

    virtual void STDMETHODCALLTYPE Warning(
        LONG warning,
        LONG param,
        BSTR description) = 0;

    virtual void STDMETHODCALLTYPE EndOfStream(
        LONG result) = 0;

    virtual void STDMETHODCALLTYPE PositionChange(
        double old_position,
        double new_position) = 0;

    virtual void STDMETHODCALLTYPE MarkerHit(
        LONG marker) = 0;

    virtual void STDMETHODCALLTYPE DurationUnitChange(
        LONG duration) = 0;

    virtual void STDMETHODCALLTYPE CdromMediaChange(
        LONG num) = 0;

    virtual void STDMETHODCALLTYPE PlaylistChange(
        IDispatch *playlist,
        WMPPlaylistChangeEventType change) = 0;

    virtual void STDMETHODCALLTYPE CurrentPlaylistChange(
        WMPPlaylistChangeEventType change) = 0;

    virtual void STDMETHODCALLTYPE CurrentPlaylistItemAvailable(
        BSTR item) = 0;

    virtual void STDMETHODCALLTYPE MediaChange(
        IDispatch *item) = 0;

    virtual void STDMETHODCALLTYPE CurrentMediaItemAvailable(
        BSTR name) = 0;

    virtual void STDMETHODCALLTYPE CurrentItemChange(
        IDispatch *media) = 0;

    virtual void STDMETHODCALLTYPE MediaCollectionChange(
        ) = 0;

    virtual void STDMETHODCALLTYPE MediaCollectionAttributeStringAdded(
        BSTR name,
        BSTR val) = 0;

    virtual void STDMETHODCALLTYPE MediaCollectionAttributeStringRemoved(
        BSTR name,
        BSTR val) = 0;

    virtual void STDMETHODCALLTYPE MediaCollectionAttributeStringChanged(
        BSTR name,
        BSTR old_val,
        BSTR new_val) = 0;

    virtual void STDMETHODCALLTYPE PlaylistCollectionChange(
        ) = 0;

    virtual void STDMETHODCALLTYPE PlaylistCollectionPlaylistAdded(
        BSTR name) = 0;

    virtual void STDMETHODCALLTYPE PlaylistCollectionPlaylistRemoved(
        BSTR name) = 0;

    virtual void STDMETHODCALLTYPE PlaylistCollectionPlaylistSetAsDeleted(
        BSTR name,
        VARIANT_BOOL deleted) = 0;

    virtual void STDMETHODCALLTYPE ModeChange(
        BSTR ModeName,
        VARIANT_BOOL value) = 0;

    virtual void STDMETHODCALLTYPE MediaError(
        IDispatch *media) = 0;

    virtual void STDMETHODCALLTYPE OpenPlaylistSwitch(
        IDispatch *item) = 0;

    virtual void STDMETHODCALLTYPE DomainChange(
        BSTR domain) = 0;

    virtual void STDMETHODCALLTYPE SwitchedToPlayerApplication(
        ) = 0;

    virtual void STDMETHODCALLTYPE SwitchedToControl(
        ) = 0;

    virtual void STDMETHODCALLTYPE PlayerDockedStateChange(
        ) = 0;

    virtual void STDMETHODCALLTYPE PlayerReconnect(
        ) = 0;

    virtual void STDMETHODCALLTYPE Click(
        short button,
        short shift_state,
        LONG x,
        LONG y) = 0;

    virtual void STDMETHODCALLTYPE DoubleClick(
        short button,
        short shift_state,
        LONG fX,
        LONG fY) = 0;

    virtual void STDMETHODCALLTYPE KeyDown(
        short keycode,
        short shift_state) = 0;

    virtual void STDMETHODCALLTYPE KeyPress(
        short ascii) = 0;

    virtual void STDMETHODCALLTYPE KeyUp(
        short keycode,
        short shift_state) = 0;

    virtual void STDMETHODCALLTYPE MouseDown(
        short button,
        short nShiftState,
        LONG x,
        LONG y) = 0;

    virtual void STDMETHODCALLTYPE MouseMove(
        short button,
        short shift_state,
        LONG x,
        LONG y) = 0;

    virtual void STDMETHODCALLTYPE MouseUp(
        short button,
        short shift_state,
        LONG x,
        LONG y) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPEvents, 0x19a6627b, 0xda9e, 0x47c1, 0xbb,0x23, 0x00,0xb5,0xe6,0x68,0x23,0x6a)
#endif
#else
typedef struct IWMPEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPEvents *This);

    /*** IWMPEvents methods ***/
    void (STDMETHODCALLTYPE *OpenStateChange)(
        IWMPEvents *This,
        LONG state);

    void (STDMETHODCALLTYPE *PlayStateChange)(
        IWMPEvents *This,
        LONG state);

    void (STDMETHODCALLTYPE *AudioLanguageChange)(
        IWMPEvents *This,
        LONG lang);

    void (STDMETHODCALLTYPE *StatusChange)(
        IWMPEvents *This);

    void (STDMETHODCALLTYPE *ScriptCommand)(
        IWMPEvents *This,
        BSTR type,
        BSTR param);

    void (STDMETHODCALLTYPE *NewStream)(
        IWMPEvents *This);

    void (STDMETHODCALLTYPE *Disconnect)(
        IWMPEvents *This,
        LONG result);

    void (STDMETHODCALLTYPE *Buffering)(
        IWMPEvents *This,
        VARIANT_BOOL start);

    void (STDMETHODCALLTYPE *Error)(
        IWMPEvents *This);

    void (STDMETHODCALLTYPE *Warning)(
        IWMPEvents *This,
        LONG warning,
        LONG param,
        BSTR description);

    void (STDMETHODCALLTYPE *EndOfStream)(
        IWMPEvents *This,
        LONG result);

    void (STDMETHODCALLTYPE *PositionChange)(
        IWMPEvents *This,
        double old_position,
        double new_position);

    void (STDMETHODCALLTYPE *MarkerHit)(
        IWMPEvents *This,
        LONG marker);

    void (STDMETHODCALLTYPE *DurationUnitChange)(
        IWMPEvents *This,
        LONG duration);

    void (STDMETHODCALLTYPE *CdromMediaChange)(
        IWMPEvents *This,
        LONG num);

    void (STDMETHODCALLTYPE *PlaylistChange)(
        IWMPEvents *This,
        IDispatch *playlist,
        WMPPlaylistChangeEventType change);

    void (STDMETHODCALLTYPE *CurrentPlaylistChange)(
        IWMPEvents *This,
        WMPPlaylistChangeEventType change);

    void (STDMETHODCALLTYPE *CurrentPlaylistItemAvailable)(
        IWMPEvents *This,
        BSTR item);

    void (STDMETHODCALLTYPE *MediaChange)(
        IWMPEvents *This,
        IDispatch *item);

    void (STDMETHODCALLTYPE *CurrentMediaItemAvailable)(
        IWMPEvents *This,
        BSTR name);

    void (STDMETHODCALLTYPE *CurrentItemChange)(
        IWMPEvents *This,
        IDispatch *media);

    void (STDMETHODCALLTYPE *MediaCollectionChange)(
        IWMPEvents *This);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringAdded)(
        IWMPEvents *This,
        BSTR name,
        BSTR val);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringRemoved)(
        IWMPEvents *This,
        BSTR name,
        BSTR val);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringChanged)(
        IWMPEvents *This,
        BSTR name,
        BSTR old_val,
        BSTR new_val);

    void (STDMETHODCALLTYPE *PlaylistCollectionChange)(
        IWMPEvents *This);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistAdded)(
        IWMPEvents *This,
        BSTR name);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistRemoved)(
        IWMPEvents *This,
        BSTR name);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistSetAsDeleted)(
        IWMPEvents *This,
        BSTR name,
        VARIANT_BOOL deleted);

    void (STDMETHODCALLTYPE *ModeChange)(
        IWMPEvents *This,
        BSTR ModeName,
        VARIANT_BOOL value);

    void (STDMETHODCALLTYPE *MediaError)(
        IWMPEvents *This,
        IDispatch *media);

    void (STDMETHODCALLTYPE *OpenPlaylistSwitch)(
        IWMPEvents *This,
        IDispatch *item);

    void (STDMETHODCALLTYPE *DomainChange)(
        IWMPEvents *This,
        BSTR domain);

    void (STDMETHODCALLTYPE *SwitchedToPlayerApplication)(
        IWMPEvents *This);

    void (STDMETHODCALLTYPE *SwitchedToControl)(
        IWMPEvents *This);

    void (STDMETHODCALLTYPE *PlayerDockedStateChange)(
        IWMPEvents *This);

    void (STDMETHODCALLTYPE *PlayerReconnect)(
        IWMPEvents *This);

    void (STDMETHODCALLTYPE *Click)(
        IWMPEvents *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *DoubleClick)(
        IWMPEvents *This,
        short button,
        short shift_state,
        LONG fX,
        LONG fY);

    void (STDMETHODCALLTYPE *KeyDown)(
        IWMPEvents *This,
        short keycode,
        short shift_state);

    void (STDMETHODCALLTYPE *KeyPress)(
        IWMPEvents *This,
        short ascii);

    void (STDMETHODCALLTYPE *KeyUp)(
        IWMPEvents *This,
        short keycode,
        short shift_state);

    void (STDMETHODCALLTYPE *MouseDown)(
        IWMPEvents *This,
        short button,
        short nShiftState,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *MouseMove)(
        IWMPEvents *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *MouseUp)(
        IWMPEvents *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    END_INTERFACE
} IWMPEventsVtbl;

interface IWMPEvents {
    CONST_VTBL IWMPEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPEvents methods ***/
#define IWMPEvents_OpenStateChange(This,state) (This)->lpVtbl->OpenStateChange(This,state)
#define IWMPEvents_PlayStateChange(This,state) (This)->lpVtbl->PlayStateChange(This,state)
#define IWMPEvents_AudioLanguageChange(This,lang) (This)->lpVtbl->AudioLanguageChange(This,lang)
#define IWMPEvents_StatusChange(This) (This)->lpVtbl->StatusChange(This)
#define IWMPEvents_ScriptCommand(This,type,param) (This)->lpVtbl->ScriptCommand(This,type,param)
#define IWMPEvents_NewStream(This) (This)->lpVtbl->NewStream(This)
#define IWMPEvents_Disconnect(This,result) (This)->lpVtbl->Disconnect(This,result)
#define IWMPEvents_Buffering(This,start) (This)->lpVtbl->Buffering(This,start)
#define IWMPEvents_Error(This) (This)->lpVtbl->Error(This)
#define IWMPEvents_Warning(This,warning,param,description) (This)->lpVtbl->Warning(This,warning,param,description)
#define IWMPEvents_EndOfStream(This,result) (This)->lpVtbl->EndOfStream(This,result)
#define IWMPEvents_PositionChange(This,old_position,new_position) (This)->lpVtbl->PositionChange(This,old_position,new_position)
#define IWMPEvents_MarkerHit(This,marker) (This)->lpVtbl->MarkerHit(This,marker)
#define IWMPEvents_DurationUnitChange(This,duration) (This)->lpVtbl->DurationUnitChange(This,duration)
#define IWMPEvents_CdromMediaChange(This,num) (This)->lpVtbl->CdromMediaChange(This,num)
#define IWMPEvents_PlaylistChange(This,playlist,change) (This)->lpVtbl->PlaylistChange(This,playlist,change)
#define IWMPEvents_CurrentPlaylistChange(This,change) (This)->lpVtbl->CurrentPlaylistChange(This,change)
#define IWMPEvents_CurrentPlaylistItemAvailable(This,item) (This)->lpVtbl->CurrentPlaylistItemAvailable(This,item)
#define IWMPEvents_MediaChange(This,item) (This)->lpVtbl->MediaChange(This,item)
#define IWMPEvents_CurrentMediaItemAvailable(This,name) (This)->lpVtbl->CurrentMediaItemAvailable(This,name)
#define IWMPEvents_CurrentItemChange(This,media) (This)->lpVtbl->CurrentItemChange(This,media)
#define IWMPEvents_MediaCollectionChange(This) (This)->lpVtbl->MediaCollectionChange(This)
#define IWMPEvents_MediaCollectionAttributeStringAdded(This,name,val) (This)->lpVtbl->MediaCollectionAttributeStringAdded(This,name,val)
#define IWMPEvents_MediaCollectionAttributeStringRemoved(This,name,val) (This)->lpVtbl->MediaCollectionAttributeStringRemoved(This,name,val)
#define IWMPEvents_MediaCollectionAttributeStringChanged(This,name,old_val,new_val) (This)->lpVtbl->MediaCollectionAttributeStringChanged(This,name,old_val,new_val)
#define IWMPEvents_PlaylistCollectionChange(This) (This)->lpVtbl->PlaylistCollectionChange(This)
#define IWMPEvents_PlaylistCollectionPlaylistAdded(This,name) (This)->lpVtbl->PlaylistCollectionPlaylistAdded(This,name)
#define IWMPEvents_PlaylistCollectionPlaylistRemoved(This,name) (This)->lpVtbl->PlaylistCollectionPlaylistRemoved(This,name)
#define IWMPEvents_PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted) (This)->lpVtbl->PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted)
#define IWMPEvents_ModeChange(This,ModeName,value) (This)->lpVtbl->ModeChange(This,ModeName,value)
#define IWMPEvents_MediaError(This,media) (This)->lpVtbl->MediaError(This,media)
#define IWMPEvents_OpenPlaylistSwitch(This,item) (This)->lpVtbl->OpenPlaylistSwitch(This,item)
#define IWMPEvents_DomainChange(This,domain) (This)->lpVtbl->DomainChange(This,domain)
#define IWMPEvents_SwitchedToPlayerApplication(This) (This)->lpVtbl->SwitchedToPlayerApplication(This)
#define IWMPEvents_SwitchedToControl(This) (This)->lpVtbl->SwitchedToControl(This)
#define IWMPEvents_PlayerDockedStateChange(This) (This)->lpVtbl->PlayerDockedStateChange(This)
#define IWMPEvents_PlayerReconnect(This) (This)->lpVtbl->PlayerReconnect(This)
#define IWMPEvents_Click(This,button,shift_state,x,y) (This)->lpVtbl->Click(This,button,shift_state,x,y)
#define IWMPEvents_DoubleClick(This,button,shift_state,fX,fY) (This)->lpVtbl->DoubleClick(This,button,shift_state,fX,fY)
#define IWMPEvents_KeyDown(This,keycode,shift_state) (This)->lpVtbl->KeyDown(This,keycode,shift_state)
#define IWMPEvents_KeyPress(This,ascii) (This)->lpVtbl->KeyPress(This,ascii)
#define IWMPEvents_KeyUp(This,keycode,shift_state) (This)->lpVtbl->KeyUp(This,keycode,shift_state)
#define IWMPEvents_MouseDown(This,button,nShiftState,x,y) (This)->lpVtbl->MouseDown(This,button,nShiftState,x,y)
#define IWMPEvents_MouseMove(This,button,shift_state,x,y) (This)->lpVtbl->MouseMove(This,button,shift_state,x,y)
#define IWMPEvents_MouseUp(This,button,shift_state,x,y) (This)->lpVtbl->MouseUp(This,button,shift_state,x,y)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPEvents_QueryInterface(IWMPEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPEvents_AddRef(IWMPEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPEvents_Release(IWMPEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPEvents methods ***/
static inline void IWMPEvents_OpenStateChange(IWMPEvents* This,LONG state) {
    This->lpVtbl->OpenStateChange(This,state);
}
static inline void IWMPEvents_PlayStateChange(IWMPEvents* This,LONG state) {
    This->lpVtbl->PlayStateChange(This,state);
}
static inline void IWMPEvents_AudioLanguageChange(IWMPEvents* This,LONG lang) {
    This->lpVtbl->AudioLanguageChange(This,lang);
}
static inline void IWMPEvents_StatusChange(IWMPEvents* This) {
    This->lpVtbl->StatusChange(This);
}
static inline void IWMPEvents_ScriptCommand(IWMPEvents* This,BSTR type,BSTR param) {
    This->lpVtbl->ScriptCommand(This,type,param);
}
static inline void IWMPEvents_NewStream(IWMPEvents* This) {
    This->lpVtbl->NewStream(This);
}
static inline void IWMPEvents_Disconnect(IWMPEvents* This,LONG result) {
    This->lpVtbl->Disconnect(This,result);
}
static inline void IWMPEvents_Buffering(IWMPEvents* This,VARIANT_BOOL start) {
    This->lpVtbl->Buffering(This,start);
}
static inline void IWMPEvents_Error(IWMPEvents* This) {
    This->lpVtbl->Error(This);
}
static inline void IWMPEvents_Warning(IWMPEvents* This,LONG warning,LONG param,BSTR description) {
    This->lpVtbl->Warning(This,warning,param,description);
}
static inline void IWMPEvents_EndOfStream(IWMPEvents* This,LONG result) {
    This->lpVtbl->EndOfStream(This,result);
}
static inline void IWMPEvents_PositionChange(IWMPEvents* This,double old_position,double new_position) {
    This->lpVtbl->PositionChange(This,old_position,new_position);
}
static inline void IWMPEvents_MarkerHit(IWMPEvents* This,LONG marker) {
    This->lpVtbl->MarkerHit(This,marker);
}
static inline void IWMPEvents_DurationUnitChange(IWMPEvents* This,LONG duration) {
    This->lpVtbl->DurationUnitChange(This,duration);
}
static inline void IWMPEvents_CdromMediaChange(IWMPEvents* This,LONG num) {
    This->lpVtbl->CdromMediaChange(This,num);
}
static inline void IWMPEvents_PlaylistChange(IWMPEvents* This,IDispatch *playlist,WMPPlaylistChangeEventType change) {
    This->lpVtbl->PlaylistChange(This,playlist,change);
}
static inline void IWMPEvents_CurrentPlaylistChange(IWMPEvents* This,WMPPlaylistChangeEventType change) {
    This->lpVtbl->CurrentPlaylistChange(This,change);
}
static inline void IWMPEvents_CurrentPlaylistItemAvailable(IWMPEvents* This,BSTR item) {
    This->lpVtbl->CurrentPlaylistItemAvailable(This,item);
}
static inline void IWMPEvents_MediaChange(IWMPEvents* This,IDispatch *item) {
    This->lpVtbl->MediaChange(This,item);
}
static inline void IWMPEvents_CurrentMediaItemAvailable(IWMPEvents* This,BSTR name) {
    This->lpVtbl->CurrentMediaItemAvailable(This,name);
}
static inline void IWMPEvents_CurrentItemChange(IWMPEvents* This,IDispatch *media) {
    This->lpVtbl->CurrentItemChange(This,media);
}
static inline void IWMPEvents_MediaCollectionChange(IWMPEvents* This) {
    This->lpVtbl->MediaCollectionChange(This);
}
static inline void IWMPEvents_MediaCollectionAttributeStringAdded(IWMPEvents* This,BSTR name,BSTR val) {
    This->lpVtbl->MediaCollectionAttributeStringAdded(This,name,val);
}
static inline void IWMPEvents_MediaCollectionAttributeStringRemoved(IWMPEvents* This,BSTR name,BSTR val) {
    This->lpVtbl->MediaCollectionAttributeStringRemoved(This,name,val);
}
static inline void IWMPEvents_MediaCollectionAttributeStringChanged(IWMPEvents* This,BSTR name,BSTR old_val,BSTR new_val) {
    This->lpVtbl->MediaCollectionAttributeStringChanged(This,name,old_val,new_val);
}
static inline void IWMPEvents_PlaylistCollectionChange(IWMPEvents* This) {
    This->lpVtbl->PlaylistCollectionChange(This);
}
static inline void IWMPEvents_PlaylistCollectionPlaylistAdded(IWMPEvents* This,BSTR name) {
    This->lpVtbl->PlaylistCollectionPlaylistAdded(This,name);
}
static inline void IWMPEvents_PlaylistCollectionPlaylistRemoved(IWMPEvents* This,BSTR name) {
    This->lpVtbl->PlaylistCollectionPlaylistRemoved(This,name);
}
static inline void IWMPEvents_PlaylistCollectionPlaylistSetAsDeleted(IWMPEvents* This,BSTR name,VARIANT_BOOL deleted) {
    This->lpVtbl->PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted);
}
static inline void IWMPEvents_ModeChange(IWMPEvents* This,BSTR ModeName,VARIANT_BOOL value) {
    This->lpVtbl->ModeChange(This,ModeName,value);
}
static inline void IWMPEvents_MediaError(IWMPEvents* This,IDispatch *media) {
    This->lpVtbl->MediaError(This,media);
}
static inline void IWMPEvents_OpenPlaylistSwitch(IWMPEvents* This,IDispatch *item) {
    This->lpVtbl->OpenPlaylistSwitch(This,item);
}
static inline void IWMPEvents_DomainChange(IWMPEvents* This,BSTR domain) {
    This->lpVtbl->DomainChange(This,domain);
}
static inline void IWMPEvents_SwitchedToPlayerApplication(IWMPEvents* This) {
    This->lpVtbl->SwitchedToPlayerApplication(This);
}
static inline void IWMPEvents_SwitchedToControl(IWMPEvents* This) {
    This->lpVtbl->SwitchedToControl(This);
}
static inline void IWMPEvents_PlayerDockedStateChange(IWMPEvents* This) {
    This->lpVtbl->PlayerDockedStateChange(This);
}
static inline void IWMPEvents_PlayerReconnect(IWMPEvents* This) {
    This->lpVtbl->PlayerReconnect(This);
}
static inline void IWMPEvents_Click(IWMPEvents* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->Click(This,button,shift_state,x,y);
}
static inline void IWMPEvents_DoubleClick(IWMPEvents* This,short button,short shift_state,LONG fX,LONG fY) {
    This->lpVtbl->DoubleClick(This,button,shift_state,fX,fY);
}
static inline void IWMPEvents_KeyDown(IWMPEvents* This,short keycode,short shift_state) {
    This->lpVtbl->KeyDown(This,keycode,shift_state);
}
static inline void IWMPEvents_KeyPress(IWMPEvents* This,short ascii) {
    This->lpVtbl->KeyPress(This,ascii);
}
static inline void IWMPEvents_KeyUp(IWMPEvents* This,short keycode,short shift_state) {
    This->lpVtbl->KeyUp(This,keycode,shift_state);
}
static inline void IWMPEvents_MouseDown(IWMPEvents* This,short button,short nShiftState,LONG x,LONG y) {
    This->lpVtbl->MouseDown(This,button,nShiftState,x,y);
}
static inline void IWMPEvents_MouseMove(IWMPEvents* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->MouseMove(This,button,shift_state,x,y);
}
static inline void IWMPEvents_MouseUp(IWMPEvents* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->MouseUp(This,button,shift_state,x,y);
}
#endif
#endif

#endif


#endif  /* __IWMPEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPEvents2 interface
 */
#ifndef __IWMPEvents2_INTERFACE_DEFINED__
#define __IWMPEvents2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPEvents2, 0x1e7601fa, 0x47ea, 0x4107, 0x9e,0xa9, 0x90,0x04,0xed,0x96,0x84,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1e7601fa-47ea-4107-9ea9-9004ed9684ff")
IWMPEvents2 : public IWMPEvents
{
    virtual void STDMETHODCALLTYPE DeviceConnect(
        IWMPSyncDevice *device) = 0;

    virtual void STDMETHODCALLTYPE DeviceDisconnect(
        IWMPSyncDevice *device) = 0;

    virtual void STDMETHODCALLTYPE DeviceStatusChange(
        IWMPSyncDevice *device,
        WMPDeviceStatus status) = 0;

    virtual void STDMETHODCALLTYPE DeviceSyncStateChange(
        IWMPSyncDevice *device,
        WMPSyncState state) = 0;

    virtual void STDMETHODCALLTYPE DeviceSyncError(
        IWMPSyncDevice *device,
        IDispatch *media) = 0;

    virtual void STDMETHODCALLTYPE CreatePartnershipComplete(
        IWMPSyncDevice *device,
        HRESULT result) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPEvents2, 0x1e7601fa, 0x47ea, 0x4107, 0x9e,0xa9, 0x90,0x04,0xed,0x96,0x84,0xff)
#endif
#else
typedef struct IWMPEvents2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPEvents2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPEvents2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPEvents2 *This);

    /*** IWMPEvents methods ***/
    void (STDMETHODCALLTYPE *OpenStateChange)(
        IWMPEvents2 *This,
        LONG state);

    void (STDMETHODCALLTYPE *PlayStateChange)(
        IWMPEvents2 *This,
        LONG state);

    void (STDMETHODCALLTYPE *AudioLanguageChange)(
        IWMPEvents2 *This,
        LONG lang);

    void (STDMETHODCALLTYPE *StatusChange)(
        IWMPEvents2 *This);

    void (STDMETHODCALLTYPE *ScriptCommand)(
        IWMPEvents2 *This,
        BSTR type,
        BSTR param);

    void (STDMETHODCALLTYPE *NewStream)(
        IWMPEvents2 *This);

    void (STDMETHODCALLTYPE *Disconnect)(
        IWMPEvents2 *This,
        LONG result);

    void (STDMETHODCALLTYPE *Buffering)(
        IWMPEvents2 *This,
        VARIANT_BOOL start);

    void (STDMETHODCALLTYPE *Error)(
        IWMPEvents2 *This);

    void (STDMETHODCALLTYPE *Warning)(
        IWMPEvents2 *This,
        LONG warning,
        LONG param,
        BSTR description);

    void (STDMETHODCALLTYPE *EndOfStream)(
        IWMPEvents2 *This,
        LONG result);

    void (STDMETHODCALLTYPE *PositionChange)(
        IWMPEvents2 *This,
        double old_position,
        double new_position);

    void (STDMETHODCALLTYPE *MarkerHit)(
        IWMPEvents2 *This,
        LONG marker);

    void (STDMETHODCALLTYPE *DurationUnitChange)(
        IWMPEvents2 *This,
        LONG duration);

    void (STDMETHODCALLTYPE *CdromMediaChange)(
        IWMPEvents2 *This,
        LONG num);

    void (STDMETHODCALLTYPE *PlaylistChange)(
        IWMPEvents2 *This,
        IDispatch *playlist,
        WMPPlaylistChangeEventType change);

    void (STDMETHODCALLTYPE *CurrentPlaylistChange)(
        IWMPEvents2 *This,
        WMPPlaylistChangeEventType change);

    void (STDMETHODCALLTYPE *CurrentPlaylistItemAvailable)(
        IWMPEvents2 *This,
        BSTR item);

    void (STDMETHODCALLTYPE *MediaChange)(
        IWMPEvents2 *This,
        IDispatch *item);

    void (STDMETHODCALLTYPE *CurrentMediaItemAvailable)(
        IWMPEvents2 *This,
        BSTR name);

    void (STDMETHODCALLTYPE *CurrentItemChange)(
        IWMPEvents2 *This,
        IDispatch *media);

    void (STDMETHODCALLTYPE *MediaCollectionChange)(
        IWMPEvents2 *This);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringAdded)(
        IWMPEvents2 *This,
        BSTR name,
        BSTR val);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringRemoved)(
        IWMPEvents2 *This,
        BSTR name,
        BSTR val);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringChanged)(
        IWMPEvents2 *This,
        BSTR name,
        BSTR old_val,
        BSTR new_val);

    void (STDMETHODCALLTYPE *PlaylistCollectionChange)(
        IWMPEvents2 *This);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistAdded)(
        IWMPEvents2 *This,
        BSTR name);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistRemoved)(
        IWMPEvents2 *This,
        BSTR name);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistSetAsDeleted)(
        IWMPEvents2 *This,
        BSTR name,
        VARIANT_BOOL deleted);

    void (STDMETHODCALLTYPE *ModeChange)(
        IWMPEvents2 *This,
        BSTR ModeName,
        VARIANT_BOOL value);

    void (STDMETHODCALLTYPE *MediaError)(
        IWMPEvents2 *This,
        IDispatch *media);

    void (STDMETHODCALLTYPE *OpenPlaylistSwitch)(
        IWMPEvents2 *This,
        IDispatch *item);

    void (STDMETHODCALLTYPE *DomainChange)(
        IWMPEvents2 *This,
        BSTR domain);

    void (STDMETHODCALLTYPE *SwitchedToPlayerApplication)(
        IWMPEvents2 *This);

    void (STDMETHODCALLTYPE *SwitchedToControl)(
        IWMPEvents2 *This);

    void (STDMETHODCALLTYPE *PlayerDockedStateChange)(
        IWMPEvents2 *This);

    void (STDMETHODCALLTYPE *PlayerReconnect)(
        IWMPEvents2 *This);

    void (STDMETHODCALLTYPE *Click)(
        IWMPEvents2 *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *DoubleClick)(
        IWMPEvents2 *This,
        short button,
        short shift_state,
        LONG fX,
        LONG fY);

    void (STDMETHODCALLTYPE *KeyDown)(
        IWMPEvents2 *This,
        short keycode,
        short shift_state);

    void (STDMETHODCALLTYPE *KeyPress)(
        IWMPEvents2 *This,
        short ascii);

    void (STDMETHODCALLTYPE *KeyUp)(
        IWMPEvents2 *This,
        short keycode,
        short shift_state);

    void (STDMETHODCALLTYPE *MouseDown)(
        IWMPEvents2 *This,
        short button,
        short nShiftState,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *MouseMove)(
        IWMPEvents2 *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *MouseUp)(
        IWMPEvents2 *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    /*** IWMPEvents2 methods ***/
    void (STDMETHODCALLTYPE *DeviceConnect)(
        IWMPEvents2 *This,
        IWMPSyncDevice *device);

    void (STDMETHODCALLTYPE *DeviceDisconnect)(
        IWMPEvents2 *This,
        IWMPSyncDevice *device);

    void (STDMETHODCALLTYPE *DeviceStatusChange)(
        IWMPEvents2 *This,
        IWMPSyncDevice *device,
        WMPDeviceStatus status);

    void (STDMETHODCALLTYPE *DeviceSyncStateChange)(
        IWMPEvents2 *This,
        IWMPSyncDevice *device,
        WMPSyncState state);

    void (STDMETHODCALLTYPE *DeviceSyncError)(
        IWMPEvents2 *This,
        IWMPSyncDevice *device,
        IDispatch *media);

    void (STDMETHODCALLTYPE *CreatePartnershipComplete)(
        IWMPEvents2 *This,
        IWMPSyncDevice *device,
        HRESULT result);

    END_INTERFACE
} IWMPEvents2Vtbl;

interface IWMPEvents2 {
    CONST_VTBL IWMPEvents2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPEvents2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPEvents2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPEvents2_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPEvents methods ***/
#define IWMPEvents2_OpenStateChange(This,state) (This)->lpVtbl->OpenStateChange(This,state)
#define IWMPEvents2_PlayStateChange(This,state) (This)->lpVtbl->PlayStateChange(This,state)
#define IWMPEvents2_AudioLanguageChange(This,lang) (This)->lpVtbl->AudioLanguageChange(This,lang)
#define IWMPEvents2_StatusChange(This) (This)->lpVtbl->StatusChange(This)
#define IWMPEvents2_ScriptCommand(This,type,param) (This)->lpVtbl->ScriptCommand(This,type,param)
#define IWMPEvents2_NewStream(This) (This)->lpVtbl->NewStream(This)
#define IWMPEvents2_Disconnect(This,result) (This)->lpVtbl->Disconnect(This,result)
#define IWMPEvents2_Buffering(This,start) (This)->lpVtbl->Buffering(This,start)
#define IWMPEvents2_Error(This) (This)->lpVtbl->Error(This)
#define IWMPEvents2_Warning(This,warning,param,description) (This)->lpVtbl->Warning(This,warning,param,description)
#define IWMPEvents2_EndOfStream(This,result) (This)->lpVtbl->EndOfStream(This,result)
#define IWMPEvents2_PositionChange(This,old_position,new_position) (This)->lpVtbl->PositionChange(This,old_position,new_position)
#define IWMPEvents2_MarkerHit(This,marker) (This)->lpVtbl->MarkerHit(This,marker)
#define IWMPEvents2_DurationUnitChange(This,duration) (This)->lpVtbl->DurationUnitChange(This,duration)
#define IWMPEvents2_CdromMediaChange(This,num) (This)->lpVtbl->CdromMediaChange(This,num)
#define IWMPEvents2_PlaylistChange(This,playlist,change) (This)->lpVtbl->PlaylistChange(This,playlist,change)
#define IWMPEvents2_CurrentPlaylistChange(This,change) (This)->lpVtbl->CurrentPlaylistChange(This,change)
#define IWMPEvents2_CurrentPlaylistItemAvailable(This,item) (This)->lpVtbl->CurrentPlaylistItemAvailable(This,item)
#define IWMPEvents2_MediaChange(This,item) (This)->lpVtbl->MediaChange(This,item)
#define IWMPEvents2_CurrentMediaItemAvailable(This,name) (This)->lpVtbl->CurrentMediaItemAvailable(This,name)
#define IWMPEvents2_CurrentItemChange(This,media) (This)->lpVtbl->CurrentItemChange(This,media)
#define IWMPEvents2_MediaCollectionChange(This) (This)->lpVtbl->MediaCollectionChange(This)
#define IWMPEvents2_MediaCollectionAttributeStringAdded(This,name,val) (This)->lpVtbl->MediaCollectionAttributeStringAdded(This,name,val)
#define IWMPEvents2_MediaCollectionAttributeStringRemoved(This,name,val) (This)->lpVtbl->MediaCollectionAttributeStringRemoved(This,name,val)
#define IWMPEvents2_MediaCollectionAttributeStringChanged(This,name,old_val,new_val) (This)->lpVtbl->MediaCollectionAttributeStringChanged(This,name,old_val,new_val)
#define IWMPEvents2_PlaylistCollectionChange(This) (This)->lpVtbl->PlaylistCollectionChange(This)
#define IWMPEvents2_PlaylistCollectionPlaylistAdded(This,name) (This)->lpVtbl->PlaylistCollectionPlaylistAdded(This,name)
#define IWMPEvents2_PlaylistCollectionPlaylistRemoved(This,name) (This)->lpVtbl->PlaylistCollectionPlaylistRemoved(This,name)
#define IWMPEvents2_PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted) (This)->lpVtbl->PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted)
#define IWMPEvents2_ModeChange(This,ModeName,value) (This)->lpVtbl->ModeChange(This,ModeName,value)
#define IWMPEvents2_MediaError(This,media) (This)->lpVtbl->MediaError(This,media)
#define IWMPEvents2_OpenPlaylistSwitch(This,item) (This)->lpVtbl->OpenPlaylistSwitch(This,item)
#define IWMPEvents2_DomainChange(This,domain) (This)->lpVtbl->DomainChange(This,domain)
#define IWMPEvents2_SwitchedToPlayerApplication(This) (This)->lpVtbl->SwitchedToPlayerApplication(This)
#define IWMPEvents2_SwitchedToControl(This) (This)->lpVtbl->SwitchedToControl(This)
#define IWMPEvents2_PlayerDockedStateChange(This) (This)->lpVtbl->PlayerDockedStateChange(This)
#define IWMPEvents2_PlayerReconnect(This) (This)->lpVtbl->PlayerReconnect(This)
#define IWMPEvents2_Click(This,button,shift_state,x,y) (This)->lpVtbl->Click(This,button,shift_state,x,y)
#define IWMPEvents2_DoubleClick(This,button,shift_state,fX,fY) (This)->lpVtbl->DoubleClick(This,button,shift_state,fX,fY)
#define IWMPEvents2_KeyDown(This,keycode,shift_state) (This)->lpVtbl->KeyDown(This,keycode,shift_state)
#define IWMPEvents2_KeyPress(This,ascii) (This)->lpVtbl->KeyPress(This,ascii)
#define IWMPEvents2_KeyUp(This,keycode,shift_state) (This)->lpVtbl->KeyUp(This,keycode,shift_state)
#define IWMPEvents2_MouseDown(This,button,nShiftState,x,y) (This)->lpVtbl->MouseDown(This,button,nShiftState,x,y)
#define IWMPEvents2_MouseMove(This,button,shift_state,x,y) (This)->lpVtbl->MouseMove(This,button,shift_state,x,y)
#define IWMPEvents2_MouseUp(This,button,shift_state,x,y) (This)->lpVtbl->MouseUp(This,button,shift_state,x,y)
/*** IWMPEvents2 methods ***/
#define IWMPEvents2_DeviceConnect(This,device) (This)->lpVtbl->DeviceConnect(This,device)
#define IWMPEvents2_DeviceDisconnect(This,device) (This)->lpVtbl->DeviceDisconnect(This,device)
#define IWMPEvents2_DeviceStatusChange(This,device,status) (This)->lpVtbl->DeviceStatusChange(This,device,status)
#define IWMPEvents2_DeviceSyncStateChange(This,device,state) (This)->lpVtbl->DeviceSyncStateChange(This,device,state)
#define IWMPEvents2_DeviceSyncError(This,device,media) (This)->lpVtbl->DeviceSyncError(This,device,media)
#define IWMPEvents2_CreatePartnershipComplete(This,device,result) (This)->lpVtbl->CreatePartnershipComplete(This,device,result)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPEvents2_QueryInterface(IWMPEvents2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPEvents2_AddRef(IWMPEvents2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPEvents2_Release(IWMPEvents2* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPEvents methods ***/
static inline void IWMPEvents2_OpenStateChange(IWMPEvents2* This,LONG state) {
    This->lpVtbl->OpenStateChange(This,state);
}
static inline void IWMPEvents2_PlayStateChange(IWMPEvents2* This,LONG state) {
    This->lpVtbl->PlayStateChange(This,state);
}
static inline void IWMPEvents2_AudioLanguageChange(IWMPEvents2* This,LONG lang) {
    This->lpVtbl->AudioLanguageChange(This,lang);
}
static inline void IWMPEvents2_StatusChange(IWMPEvents2* This) {
    This->lpVtbl->StatusChange(This);
}
static inline void IWMPEvents2_ScriptCommand(IWMPEvents2* This,BSTR type,BSTR param) {
    This->lpVtbl->ScriptCommand(This,type,param);
}
static inline void IWMPEvents2_NewStream(IWMPEvents2* This) {
    This->lpVtbl->NewStream(This);
}
static inline void IWMPEvents2_Disconnect(IWMPEvents2* This,LONG result) {
    This->lpVtbl->Disconnect(This,result);
}
static inline void IWMPEvents2_Buffering(IWMPEvents2* This,VARIANT_BOOL start) {
    This->lpVtbl->Buffering(This,start);
}
static inline void IWMPEvents2_Error(IWMPEvents2* This) {
    This->lpVtbl->Error(This);
}
static inline void IWMPEvents2_Warning(IWMPEvents2* This,LONG warning,LONG param,BSTR description) {
    This->lpVtbl->Warning(This,warning,param,description);
}
static inline void IWMPEvents2_EndOfStream(IWMPEvents2* This,LONG result) {
    This->lpVtbl->EndOfStream(This,result);
}
static inline void IWMPEvents2_PositionChange(IWMPEvents2* This,double old_position,double new_position) {
    This->lpVtbl->PositionChange(This,old_position,new_position);
}
static inline void IWMPEvents2_MarkerHit(IWMPEvents2* This,LONG marker) {
    This->lpVtbl->MarkerHit(This,marker);
}
static inline void IWMPEvents2_DurationUnitChange(IWMPEvents2* This,LONG duration) {
    This->lpVtbl->DurationUnitChange(This,duration);
}
static inline void IWMPEvents2_CdromMediaChange(IWMPEvents2* This,LONG num) {
    This->lpVtbl->CdromMediaChange(This,num);
}
static inline void IWMPEvents2_PlaylistChange(IWMPEvents2* This,IDispatch *playlist,WMPPlaylistChangeEventType change) {
    This->lpVtbl->PlaylistChange(This,playlist,change);
}
static inline void IWMPEvents2_CurrentPlaylistChange(IWMPEvents2* This,WMPPlaylistChangeEventType change) {
    This->lpVtbl->CurrentPlaylistChange(This,change);
}
static inline void IWMPEvents2_CurrentPlaylistItemAvailable(IWMPEvents2* This,BSTR item) {
    This->lpVtbl->CurrentPlaylistItemAvailable(This,item);
}
static inline void IWMPEvents2_MediaChange(IWMPEvents2* This,IDispatch *item) {
    This->lpVtbl->MediaChange(This,item);
}
static inline void IWMPEvents2_CurrentMediaItemAvailable(IWMPEvents2* This,BSTR name) {
    This->lpVtbl->CurrentMediaItemAvailable(This,name);
}
static inline void IWMPEvents2_CurrentItemChange(IWMPEvents2* This,IDispatch *media) {
    This->lpVtbl->CurrentItemChange(This,media);
}
static inline void IWMPEvents2_MediaCollectionChange(IWMPEvents2* This) {
    This->lpVtbl->MediaCollectionChange(This);
}
static inline void IWMPEvents2_MediaCollectionAttributeStringAdded(IWMPEvents2* This,BSTR name,BSTR val) {
    This->lpVtbl->MediaCollectionAttributeStringAdded(This,name,val);
}
static inline void IWMPEvents2_MediaCollectionAttributeStringRemoved(IWMPEvents2* This,BSTR name,BSTR val) {
    This->lpVtbl->MediaCollectionAttributeStringRemoved(This,name,val);
}
static inline void IWMPEvents2_MediaCollectionAttributeStringChanged(IWMPEvents2* This,BSTR name,BSTR old_val,BSTR new_val) {
    This->lpVtbl->MediaCollectionAttributeStringChanged(This,name,old_val,new_val);
}
static inline void IWMPEvents2_PlaylistCollectionChange(IWMPEvents2* This) {
    This->lpVtbl->PlaylistCollectionChange(This);
}
static inline void IWMPEvents2_PlaylistCollectionPlaylistAdded(IWMPEvents2* This,BSTR name) {
    This->lpVtbl->PlaylistCollectionPlaylistAdded(This,name);
}
static inline void IWMPEvents2_PlaylistCollectionPlaylistRemoved(IWMPEvents2* This,BSTR name) {
    This->lpVtbl->PlaylistCollectionPlaylistRemoved(This,name);
}
static inline void IWMPEvents2_PlaylistCollectionPlaylistSetAsDeleted(IWMPEvents2* This,BSTR name,VARIANT_BOOL deleted) {
    This->lpVtbl->PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted);
}
static inline void IWMPEvents2_ModeChange(IWMPEvents2* This,BSTR ModeName,VARIANT_BOOL value) {
    This->lpVtbl->ModeChange(This,ModeName,value);
}
static inline void IWMPEvents2_MediaError(IWMPEvents2* This,IDispatch *media) {
    This->lpVtbl->MediaError(This,media);
}
static inline void IWMPEvents2_OpenPlaylistSwitch(IWMPEvents2* This,IDispatch *item) {
    This->lpVtbl->OpenPlaylistSwitch(This,item);
}
static inline void IWMPEvents2_DomainChange(IWMPEvents2* This,BSTR domain) {
    This->lpVtbl->DomainChange(This,domain);
}
static inline void IWMPEvents2_SwitchedToPlayerApplication(IWMPEvents2* This) {
    This->lpVtbl->SwitchedToPlayerApplication(This);
}
static inline void IWMPEvents2_SwitchedToControl(IWMPEvents2* This) {
    This->lpVtbl->SwitchedToControl(This);
}
static inline void IWMPEvents2_PlayerDockedStateChange(IWMPEvents2* This) {
    This->lpVtbl->PlayerDockedStateChange(This);
}
static inline void IWMPEvents2_PlayerReconnect(IWMPEvents2* This) {
    This->lpVtbl->PlayerReconnect(This);
}
static inline void IWMPEvents2_Click(IWMPEvents2* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->Click(This,button,shift_state,x,y);
}
static inline void IWMPEvents2_DoubleClick(IWMPEvents2* This,short button,short shift_state,LONG fX,LONG fY) {
    This->lpVtbl->DoubleClick(This,button,shift_state,fX,fY);
}
static inline void IWMPEvents2_KeyDown(IWMPEvents2* This,short keycode,short shift_state) {
    This->lpVtbl->KeyDown(This,keycode,shift_state);
}
static inline void IWMPEvents2_KeyPress(IWMPEvents2* This,short ascii) {
    This->lpVtbl->KeyPress(This,ascii);
}
static inline void IWMPEvents2_KeyUp(IWMPEvents2* This,short keycode,short shift_state) {
    This->lpVtbl->KeyUp(This,keycode,shift_state);
}
static inline void IWMPEvents2_MouseDown(IWMPEvents2* This,short button,short nShiftState,LONG x,LONG y) {
    This->lpVtbl->MouseDown(This,button,nShiftState,x,y);
}
static inline void IWMPEvents2_MouseMove(IWMPEvents2* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->MouseMove(This,button,shift_state,x,y);
}
static inline void IWMPEvents2_MouseUp(IWMPEvents2* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->MouseUp(This,button,shift_state,x,y);
}
/*** IWMPEvents2 methods ***/
static inline void IWMPEvents2_DeviceConnect(IWMPEvents2* This,IWMPSyncDevice *device) {
    This->lpVtbl->DeviceConnect(This,device);
}
static inline void IWMPEvents2_DeviceDisconnect(IWMPEvents2* This,IWMPSyncDevice *device) {
    This->lpVtbl->DeviceDisconnect(This,device);
}
static inline void IWMPEvents2_DeviceStatusChange(IWMPEvents2* This,IWMPSyncDevice *device,WMPDeviceStatus status) {
    This->lpVtbl->DeviceStatusChange(This,device,status);
}
static inline void IWMPEvents2_DeviceSyncStateChange(IWMPEvents2* This,IWMPSyncDevice *device,WMPSyncState state) {
    This->lpVtbl->DeviceSyncStateChange(This,device,state);
}
static inline void IWMPEvents2_DeviceSyncError(IWMPEvents2* This,IWMPSyncDevice *device,IDispatch *media) {
    This->lpVtbl->DeviceSyncError(This,device,media);
}
static inline void IWMPEvents2_CreatePartnershipComplete(IWMPEvents2* This,IWMPSyncDevice *device,HRESULT result) {
    This->lpVtbl->CreatePartnershipComplete(This,device,result);
}
#endif
#endif

#endif


#endif  /* __IWMPEvents2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPEvents3 interface
 */
#ifndef __IWMPEvents3_INTERFACE_DEFINED__
#define __IWMPEvents3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPEvents3, 0x1f504270, 0xa66b, 0x4223, 0x8e,0x96, 0x26,0xa0,0x6c,0x63,0xd6,0x9f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1f504270-a66b-4223-8e96-26a06c63d69f")
IWMPEvents3 : public IWMPEvents2
{
    virtual void STDMETHODCALLTYPE CdromRipStateChange(
        IWMPCdromRip *rip,
        WMPRipState state) = 0;

    virtual void STDMETHODCALLTYPE CdromRipMediaError(
        IWMPCdromRip *rip,
        IDispatch *media) = 0;

    virtual void STDMETHODCALLTYPE CdromBurnStateChange(
        IWMPCdromBurn *burn,
        WMPBurnState state) = 0;

    virtual void STDMETHODCALLTYPE CdromBurnMediaError(
        IWMPCdromBurn *burn,
        IDispatch *media) = 0;

    virtual void STDMETHODCALLTYPE CdromBurnError(
        IWMPCdromBurn *burn,
        HRESULT error) = 0;

    virtual void STDMETHODCALLTYPE LibraryConnect(
        IWMPLibrary *wmplibrary) = 0;

    virtual void STDMETHODCALLTYPE LibraryDisconnect(
        IWMPLibrary *wmplibrary) = 0;

    virtual void STDMETHODCALLTYPE FolderScanStateChange(
        WMPFolderScanState state) = 0;

    virtual void STDMETHODCALLTYPE StringCollectionChange(
        IDispatch *collection,
        WMPStringCollectionChangeEventType change,
        LONG index) = 0;

    virtual void STDMETHODCALLTYPE MediaCollectionMediaAdded(
        IDispatch *media) = 0;

    virtual void STDMETHODCALLTYPE MediaCollectionMediaRemoved(
        IDispatch *media) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPEvents3, 0x1f504270, 0xa66b, 0x4223, 0x8e,0x96, 0x26,0xa0,0x6c,0x63,0xd6,0x9f)
#endif
#else
typedef struct IWMPEvents3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPEvents3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPEvents3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPEvents3 *This);

    /*** IWMPEvents methods ***/
    void (STDMETHODCALLTYPE *OpenStateChange)(
        IWMPEvents3 *This,
        LONG state);

    void (STDMETHODCALLTYPE *PlayStateChange)(
        IWMPEvents3 *This,
        LONG state);

    void (STDMETHODCALLTYPE *AudioLanguageChange)(
        IWMPEvents3 *This,
        LONG lang);

    void (STDMETHODCALLTYPE *StatusChange)(
        IWMPEvents3 *This);

    void (STDMETHODCALLTYPE *ScriptCommand)(
        IWMPEvents3 *This,
        BSTR type,
        BSTR param);

    void (STDMETHODCALLTYPE *NewStream)(
        IWMPEvents3 *This);

    void (STDMETHODCALLTYPE *Disconnect)(
        IWMPEvents3 *This,
        LONG result);

    void (STDMETHODCALLTYPE *Buffering)(
        IWMPEvents3 *This,
        VARIANT_BOOL start);

    void (STDMETHODCALLTYPE *Error)(
        IWMPEvents3 *This);

    void (STDMETHODCALLTYPE *Warning)(
        IWMPEvents3 *This,
        LONG warning,
        LONG param,
        BSTR description);

    void (STDMETHODCALLTYPE *EndOfStream)(
        IWMPEvents3 *This,
        LONG result);

    void (STDMETHODCALLTYPE *PositionChange)(
        IWMPEvents3 *This,
        double old_position,
        double new_position);

    void (STDMETHODCALLTYPE *MarkerHit)(
        IWMPEvents3 *This,
        LONG marker);

    void (STDMETHODCALLTYPE *DurationUnitChange)(
        IWMPEvents3 *This,
        LONG duration);

    void (STDMETHODCALLTYPE *CdromMediaChange)(
        IWMPEvents3 *This,
        LONG num);

    void (STDMETHODCALLTYPE *PlaylistChange)(
        IWMPEvents3 *This,
        IDispatch *playlist,
        WMPPlaylistChangeEventType change);

    void (STDMETHODCALLTYPE *CurrentPlaylistChange)(
        IWMPEvents3 *This,
        WMPPlaylistChangeEventType change);

    void (STDMETHODCALLTYPE *CurrentPlaylistItemAvailable)(
        IWMPEvents3 *This,
        BSTR item);

    void (STDMETHODCALLTYPE *MediaChange)(
        IWMPEvents3 *This,
        IDispatch *item);

    void (STDMETHODCALLTYPE *CurrentMediaItemAvailable)(
        IWMPEvents3 *This,
        BSTR name);

    void (STDMETHODCALLTYPE *CurrentItemChange)(
        IWMPEvents3 *This,
        IDispatch *media);

    void (STDMETHODCALLTYPE *MediaCollectionChange)(
        IWMPEvents3 *This);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringAdded)(
        IWMPEvents3 *This,
        BSTR name,
        BSTR val);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringRemoved)(
        IWMPEvents3 *This,
        BSTR name,
        BSTR val);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringChanged)(
        IWMPEvents3 *This,
        BSTR name,
        BSTR old_val,
        BSTR new_val);

    void (STDMETHODCALLTYPE *PlaylistCollectionChange)(
        IWMPEvents3 *This);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistAdded)(
        IWMPEvents3 *This,
        BSTR name);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistRemoved)(
        IWMPEvents3 *This,
        BSTR name);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistSetAsDeleted)(
        IWMPEvents3 *This,
        BSTR name,
        VARIANT_BOOL deleted);

    void (STDMETHODCALLTYPE *ModeChange)(
        IWMPEvents3 *This,
        BSTR ModeName,
        VARIANT_BOOL value);

    void (STDMETHODCALLTYPE *MediaError)(
        IWMPEvents3 *This,
        IDispatch *media);

    void (STDMETHODCALLTYPE *OpenPlaylistSwitch)(
        IWMPEvents3 *This,
        IDispatch *item);

    void (STDMETHODCALLTYPE *DomainChange)(
        IWMPEvents3 *This,
        BSTR domain);

    void (STDMETHODCALLTYPE *SwitchedToPlayerApplication)(
        IWMPEvents3 *This);

    void (STDMETHODCALLTYPE *SwitchedToControl)(
        IWMPEvents3 *This);

    void (STDMETHODCALLTYPE *PlayerDockedStateChange)(
        IWMPEvents3 *This);

    void (STDMETHODCALLTYPE *PlayerReconnect)(
        IWMPEvents3 *This);

    void (STDMETHODCALLTYPE *Click)(
        IWMPEvents3 *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *DoubleClick)(
        IWMPEvents3 *This,
        short button,
        short shift_state,
        LONG fX,
        LONG fY);

    void (STDMETHODCALLTYPE *KeyDown)(
        IWMPEvents3 *This,
        short keycode,
        short shift_state);

    void (STDMETHODCALLTYPE *KeyPress)(
        IWMPEvents3 *This,
        short ascii);

    void (STDMETHODCALLTYPE *KeyUp)(
        IWMPEvents3 *This,
        short keycode,
        short shift_state);

    void (STDMETHODCALLTYPE *MouseDown)(
        IWMPEvents3 *This,
        short button,
        short nShiftState,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *MouseMove)(
        IWMPEvents3 *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *MouseUp)(
        IWMPEvents3 *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    /*** IWMPEvents2 methods ***/
    void (STDMETHODCALLTYPE *DeviceConnect)(
        IWMPEvents3 *This,
        IWMPSyncDevice *device);

    void (STDMETHODCALLTYPE *DeviceDisconnect)(
        IWMPEvents3 *This,
        IWMPSyncDevice *device);

    void (STDMETHODCALLTYPE *DeviceStatusChange)(
        IWMPEvents3 *This,
        IWMPSyncDevice *device,
        WMPDeviceStatus status);

    void (STDMETHODCALLTYPE *DeviceSyncStateChange)(
        IWMPEvents3 *This,
        IWMPSyncDevice *device,
        WMPSyncState state);

    void (STDMETHODCALLTYPE *DeviceSyncError)(
        IWMPEvents3 *This,
        IWMPSyncDevice *device,
        IDispatch *media);

    void (STDMETHODCALLTYPE *CreatePartnershipComplete)(
        IWMPEvents3 *This,
        IWMPSyncDevice *device,
        HRESULT result);

    /*** IWMPEvents3 methods ***/
    void (STDMETHODCALLTYPE *CdromRipStateChange)(
        IWMPEvents3 *This,
        IWMPCdromRip *rip,
        WMPRipState state);

    void (STDMETHODCALLTYPE *CdromRipMediaError)(
        IWMPEvents3 *This,
        IWMPCdromRip *rip,
        IDispatch *media);

    void (STDMETHODCALLTYPE *CdromBurnStateChange)(
        IWMPEvents3 *This,
        IWMPCdromBurn *burn,
        WMPBurnState state);

    void (STDMETHODCALLTYPE *CdromBurnMediaError)(
        IWMPEvents3 *This,
        IWMPCdromBurn *burn,
        IDispatch *media);

    void (STDMETHODCALLTYPE *CdromBurnError)(
        IWMPEvents3 *This,
        IWMPCdromBurn *burn,
        HRESULT error);

    void (STDMETHODCALLTYPE *LibraryConnect)(
        IWMPEvents3 *This,
        IWMPLibrary *wmplibrary);

    void (STDMETHODCALLTYPE *LibraryDisconnect)(
        IWMPEvents3 *This,
        IWMPLibrary *wmplibrary);

    void (STDMETHODCALLTYPE *FolderScanStateChange)(
        IWMPEvents3 *This,
        WMPFolderScanState state);

    void (STDMETHODCALLTYPE *StringCollectionChange)(
        IWMPEvents3 *This,
        IDispatch *collection,
        WMPStringCollectionChangeEventType change,
        LONG index);

    void (STDMETHODCALLTYPE *MediaCollectionMediaAdded)(
        IWMPEvents3 *This,
        IDispatch *media);

    void (STDMETHODCALLTYPE *MediaCollectionMediaRemoved)(
        IWMPEvents3 *This,
        IDispatch *media);

    END_INTERFACE
} IWMPEvents3Vtbl;

interface IWMPEvents3 {
    CONST_VTBL IWMPEvents3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPEvents3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPEvents3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPEvents3_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPEvents methods ***/
#define IWMPEvents3_OpenStateChange(This,state) (This)->lpVtbl->OpenStateChange(This,state)
#define IWMPEvents3_PlayStateChange(This,state) (This)->lpVtbl->PlayStateChange(This,state)
#define IWMPEvents3_AudioLanguageChange(This,lang) (This)->lpVtbl->AudioLanguageChange(This,lang)
#define IWMPEvents3_StatusChange(This) (This)->lpVtbl->StatusChange(This)
#define IWMPEvents3_ScriptCommand(This,type,param) (This)->lpVtbl->ScriptCommand(This,type,param)
#define IWMPEvents3_NewStream(This) (This)->lpVtbl->NewStream(This)
#define IWMPEvents3_Disconnect(This,result) (This)->lpVtbl->Disconnect(This,result)
#define IWMPEvents3_Buffering(This,start) (This)->lpVtbl->Buffering(This,start)
#define IWMPEvents3_Error(This) (This)->lpVtbl->Error(This)
#define IWMPEvents3_Warning(This,warning,param,description) (This)->lpVtbl->Warning(This,warning,param,description)
#define IWMPEvents3_EndOfStream(This,result) (This)->lpVtbl->EndOfStream(This,result)
#define IWMPEvents3_PositionChange(This,old_position,new_position) (This)->lpVtbl->PositionChange(This,old_position,new_position)
#define IWMPEvents3_MarkerHit(This,marker) (This)->lpVtbl->MarkerHit(This,marker)
#define IWMPEvents3_DurationUnitChange(This,duration) (This)->lpVtbl->DurationUnitChange(This,duration)
#define IWMPEvents3_CdromMediaChange(This,num) (This)->lpVtbl->CdromMediaChange(This,num)
#define IWMPEvents3_PlaylistChange(This,playlist,change) (This)->lpVtbl->PlaylistChange(This,playlist,change)
#define IWMPEvents3_CurrentPlaylistChange(This,change) (This)->lpVtbl->CurrentPlaylistChange(This,change)
#define IWMPEvents3_CurrentPlaylistItemAvailable(This,item) (This)->lpVtbl->CurrentPlaylistItemAvailable(This,item)
#define IWMPEvents3_MediaChange(This,item) (This)->lpVtbl->MediaChange(This,item)
#define IWMPEvents3_CurrentMediaItemAvailable(This,name) (This)->lpVtbl->CurrentMediaItemAvailable(This,name)
#define IWMPEvents3_CurrentItemChange(This,media) (This)->lpVtbl->CurrentItemChange(This,media)
#define IWMPEvents3_MediaCollectionChange(This) (This)->lpVtbl->MediaCollectionChange(This)
#define IWMPEvents3_MediaCollectionAttributeStringAdded(This,name,val) (This)->lpVtbl->MediaCollectionAttributeStringAdded(This,name,val)
#define IWMPEvents3_MediaCollectionAttributeStringRemoved(This,name,val) (This)->lpVtbl->MediaCollectionAttributeStringRemoved(This,name,val)
#define IWMPEvents3_MediaCollectionAttributeStringChanged(This,name,old_val,new_val) (This)->lpVtbl->MediaCollectionAttributeStringChanged(This,name,old_val,new_val)
#define IWMPEvents3_PlaylistCollectionChange(This) (This)->lpVtbl->PlaylistCollectionChange(This)
#define IWMPEvents3_PlaylistCollectionPlaylistAdded(This,name) (This)->lpVtbl->PlaylistCollectionPlaylistAdded(This,name)
#define IWMPEvents3_PlaylistCollectionPlaylistRemoved(This,name) (This)->lpVtbl->PlaylistCollectionPlaylistRemoved(This,name)
#define IWMPEvents3_PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted) (This)->lpVtbl->PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted)
#define IWMPEvents3_ModeChange(This,ModeName,value) (This)->lpVtbl->ModeChange(This,ModeName,value)
#define IWMPEvents3_MediaError(This,media) (This)->lpVtbl->MediaError(This,media)
#define IWMPEvents3_OpenPlaylistSwitch(This,item) (This)->lpVtbl->OpenPlaylistSwitch(This,item)
#define IWMPEvents3_DomainChange(This,domain) (This)->lpVtbl->DomainChange(This,domain)
#define IWMPEvents3_SwitchedToPlayerApplication(This) (This)->lpVtbl->SwitchedToPlayerApplication(This)
#define IWMPEvents3_SwitchedToControl(This) (This)->lpVtbl->SwitchedToControl(This)
#define IWMPEvents3_PlayerDockedStateChange(This) (This)->lpVtbl->PlayerDockedStateChange(This)
#define IWMPEvents3_PlayerReconnect(This) (This)->lpVtbl->PlayerReconnect(This)
#define IWMPEvents3_Click(This,button,shift_state,x,y) (This)->lpVtbl->Click(This,button,shift_state,x,y)
#define IWMPEvents3_DoubleClick(This,button,shift_state,fX,fY) (This)->lpVtbl->DoubleClick(This,button,shift_state,fX,fY)
#define IWMPEvents3_KeyDown(This,keycode,shift_state) (This)->lpVtbl->KeyDown(This,keycode,shift_state)
#define IWMPEvents3_KeyPress(This,ascii) (This)->lpVtbl->KeyPress(This,ascii)
#define IWMPEvents3_KeyUp(This,keycode,shift_state) (This)->lpVtbl->KeyUp(This,keycode,shift_state)
#define IWMPEvents3_MouseDown(This,button,nShiftState,x,y) (This)->lpVtbl->MouseDown(This,button,nShiftState,x,y)
#define IWMPEvents3_MouseMove(This,button,shift_state,x,y) (This)->lpVtbl->MouseMove(This,button,shift_state,x,y)
#define IWMPEvents3_MouseUp(This,button,shift_state,x,y) (This)->lpVtbl->MouseUp(This,button,shift_state,x,y)
/*** IWMPEvents2 methods ***/
#define IWMPEvents3_DeviceConnect(This,device) (This)->lpVtbl->DeviceConnect(This,device)
#define IWMPEvents3_DeviceDisconnect(This,device) (This)->lpVtbl->DeviceDisconnect(This,device)
#define IWMPEvents3_DeviceStatusChange(This,device,status) (This)->lpVtbl->DeviceStatusChange(This,device,status)
#define IWMPEvents3_DeviceSyncStateChange(This,device,state) (This)->lpVtbl->DeviceSyncStateChange(This,device,state)
#define IWMPEvents3_DeviceSyncError(This,device,media) (This)->lpVtbl->DeviceSyncError(This,device,media)
#define IWMPEvents3_CreatePartnershipComplete(This,device,result) (This)->lpVtbl->CreatePartnershipComplete(This,device,result)
/*** IWMPEvents3 methods ***/
#define IWMPEvents3_CdromRipStateChange(This,rip,state) (This)->lpVtbl->CdromRipStateChange(This,rip,state)
#define IWMPEvents3_CdromRipMediaError(This,rip,media) (This)->lpVtbl->CdromRipMediaError(This,rip,media)
#define IWMPEvents3_CdromBurnStateChange(This,burn,state) (This)->lpVtbl->CdromBurnStateChange(This,burn,state)
#define IWMPEvents3_CdromBurnMediaError(This,burn,media) (This)->lpVtbl->CdromBurnMediaError(This,burn,media)
#define IWMPEvents3_CdromBurnError(This,burn,error) (This)->lpVtbl->CdromBurnError(This,burn,error)
#define IWMPEvents3_LibraryConnect(This,wmplibrary) (This)->lpVtbl->LibraryConnect(This,wmplibrary)
#define IWMPEvents3_LibraryDisconnect(This,wmplibrary) (This)->lpVtbl->LibraryDisconnect(This,wmplibrary)
#define IWMPEvents3_FolderScanStateChange(This,state) (This)->lpVtbl->FolderScanStateChange(This,state)
#define IWMPEvents3_StringCollectionChange(This,collection,change,index) (This)->lpVtbl->StringCollectionChange(This,collection,change,index)
#define IWMPEvents3_MediaCollectionMediaAdded(This,media) (This)->lpVtbl->MediaCollectionMediaAdded(This,media)
#define IWMPEvents3_MediaCollectionMediaRemoved(This,media) (This)->lpVtbl->MediaCollectionMediaRemoved(This,media)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPEvents3_QueryInterface(IWMPEvents3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPEvents3_AddRef(IWMPEvents3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPEvents3_Release(IWMPEvents3* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPEvents methods ***/
static inline void IWMPEvents3_OpenStateChange(IWMPEvents3* This,LONG state) {
    This->lpVtbl->OpenStateChange(This,state);
}
static inline void IWMPEvents3_PlayStateChange(IWMPEvents3* This,LONG state) {
    This->lpVtbl->PlayStateChange(This,state);
}
static inline void IWMPEvents3_AudioLanguageChange(IWMPEvents3* This,LONG lang) {
    This->lpVtbl->AudioLanguageChange(This,lang);
}
static inline void IWMPEvents3_StatusChange(IWMPEvents3* This) {
    This->lpVtbl->StatusChange(This);
}
static inline void IWMPEvents3_ScriptCommand(IWMPEvents3* This,BSTR type,BSTR param) {
    This->lpVtbl->ScriptCommand(This,type,param);
}
static inline void IWMPEvents3_NewStream(IWMPEvents3* This) {
    This->lpVtbl->NewStream(This);
}
static inline void IWMPEvents3_Disconnect(IWMPEvents3* This,LONG result) {
    This->lpVtbl->Disconnect(This,result);
}
static inline void IWMPEvents3_Buffering(IWMPEvents3* This,VARIANT_BOOL start) {
    This->lpVtbl->Buffering(This,start);
}
static inline void IWMPEvents3_Error(IWMPEvents3* This) {
    This->lpVtbl->Error(This);
}
static inline void IWMPEvents3_Warning(IWMPEvents3* This,LONG warning,LONG param,BSTR description) {
    This->lpVtbl->Warning(This,warning,param,description);
}
static inline void IWMPEvents3_EndOfStream(IWMPEvents3* This,LONG result) {
    This->lpVtbl->EndOfStream(This,result);
}
static inline void IWMPEvents3_PositionChange(IWMPEvents3* This,double old_position,double new_position) {
    This->lpVtbl->PositionChange(This,old_position,new_position);
}
static inline void IWMPEvents3_MarkerHit(IWMPEvents3* This,LONG marker) {
    This->lpVtbl->MarkerHit(This,marker);
}
static inline void IWMPEvents3_DurationUnitChange(IWMPEvents3* This,LONG duration) {
    This->lpVtbl->DurationUnitChange(This,duration);
}
static inline void IWMPEvents3_CdromMediaChange(IWMPEvents3* This,LONG num) {
    This->lpVtbl->CdromMediaChange(This,num);
}
static inline void IWMPEvents3_PlaylistChange(IWMPEvents3* This,IDispatch *playlist,WMPPlaylistChangeEventType change) {
    This->lpVtbl->PlaylistChange(This,playlist,change);
}
static inline void IWMPEvents3_CurrentPlaylistChange(IWMPEvents3* This,WMPPlaylistChangeEventType change) {
    This->lpVtbl->CurrentPlaylistChange(This,change);
}
static inline void IWMPEvents3_CurrentPlaylistItemAvailable(IWMPEvents3* This,BSTR item) {
    This->lpVtbl->CurrentPlaylistItemAvailable(This,item);
}
static inline void IWMPEvents3_MediaChange(IWMPEvents3* This,IDispatch *item) {
    This->lpVtbl->MediaChange(This,item);
}
static inline void IWMPEvents3_CurrentMediaItemAvailable(IWMPEvents3* This,BSTR name) {
    This->lpVtbl->CurrentMediaItemAvailable(This,name);
}
static inline void IWMPEvents3_CurrentItemChange(IWMPEvents3* This,IDispatch *media) {
    This->lpVtbl->CurrentItemChange(This,media);
}
static inline void IWMPEvents3_MediaCollectionChange(IWMPEvents3* This) {
    This->lpVtbl->MediaCollectionChange(This);
}
static inline void IWMPEvents3_MediaCollectionAttributeStringAdded(IWMPEvents3* This,BSTR name,BSTR val) {
    This->lpVtbl->MediaCollectionAttributeStringAdded(This,name,val);
}
static inline void IWMPEvents3_MediaCollectionAttributeStringRemoved(IWMPEvents3* This,BSTR name,BSTR val) {
    This->lpVtbl->MediaCollectionAttributeStringRemoved(This,name,val);
}
static inline void IWMPEvents3_MediaCollectionAttributeStringChanged(IWMPEvents3* This,BSTR name,BSTR old_val,BSTR new_val) {
    This->lpVtbl->MediaCollectionAttributeStringChanged(This,name,old_val,new_val);
}
static inline void IWMPEvents3_PlaylistCollectionChange(IWMPEvents3* This) {
    This->lpVtbl->PlaylistCollectionChange(This);
}
static inline void IWMPEvents3_PlaylistCollectionPlaylistAdded(IWMPEvents3* This,BSTR name) {
    This->lpVtbl->PlaylistCollectionPlaylistAdded(This,name);
}
static inline void IWMPEvents3_PlaylistCollectionPlaylistRemoved(IWMPEvents3* This,BSTR name) {
    This->lpVtbl->PlaylistCollectionPlaylistRemoved(This,name);
}
static inline void IWMPEvents3_PlaylistCollectionPlaylistSetAsDeleted(IWMPEvents3* This,BSTR name,VARIANT_BOOL deleted) {
    This->lpVtbl->PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted);
}
static inline void IWMPEvents3_ModeChange(IWMPEvents3* This,BSTR ModeName,VARIANT_BOOL value) {
    This->lpVtbl->ModeChange(This,ModeName,value);
}
static inline void IWMPEvents3_MediaError(IWMPEvents3* This,IDispatch *media) {
    This->lpVtbl->MediaError(This,media);
}
static inline void IWMPEvents3_OpenPlaylistSwitch(IWMPEvents3* This,IDispatch *item) {
    This->lpVtbl->OpenPlaylistSwitch(This,item);
}
static inline void IWMPEvents3_DomainChange(IWMPEvents3* This,BSTR domain) {
    This->lpVtbl->DomainChange(This,domain);
}
static inline void IWMPEvents3_SwitchedToPlayerApplication(IWMPEvents3* This) {
    This->lpVtbl->SwitchedToPlayerApplication(This);
}
static inline void IWMPEvents3_SwitchedToControl(IWMPEvents3* This) {
    This->lpVtbl->SwitchedToControl(This);
}
static inline void IWMPEvents3_PlayerDockedStateChange(IWMPEvents3* This) {
    This->lpVtbl->PlayerDockedStateChange(This);
}
static inline void IWMPEvents3_PlayerReconnect(IWMPEvents3* This) {
    This->lpVtbl->PlayerReconnect(This);
}
static inline void IWMPEvents3_Click(IWMPEvents3* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->Click(This,button,shift_state,x,y);
}
static inline void IWMPEvents3_DoubleClick(IWMPEvents3* This,short button,short shift_state,LONG fX,LONG fY) {
    This->lpVtbl->DoubleClick(This,button,shift_state,fX,fY);
}
static inline void IWMPEvents3_KeyDown(IWMPEvents3* This,short keycode,short shift_state) {
    This->lpVtbl->KeyDown(This,keycode,shift_state);
}
static inline void IWMPEvents3_KeyPress(IWMPEvents3* This,short ascii) {
    This->lpVtbl->KeyPress(This,ascii);
}
static inline void IWMPEvents3_KeyUp(IWMPEvents3* This,short keycode,short shift_state) {
    This->lpVtbl->KeyUp(This,keycode,shift_state);
}
static inline void IWMPEvents3_MouseDown(IWMPEvents3* This,short button,short nShiftState,LONG x,LONG y) {
    This->lpVtbl->MouseDown(This,button,nShiftState,x,y);
}
static inline void IWMPEvents3_MouseMove(IWMPEvents3* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->MouseMove(This,button,shift_state,x,y);
}
static inline void IWMPEvents3_MouseUp(IWMPEvents3* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->MouseUp(This,button,shift_state,x,y);
}
/*** IWMPEvents2 methods ***/
static inline void IWMPEvents3_DeviceConnect(IWMPEvents3* This,IWMPSyncDevice *device) {
    This->lpVtbl->DeviceConnect(This,device);
}
static inline void IWMPEvents3_DeviceDisconnect(IWMPEvents3* This,IWMPSyncDevice *device) {
    This->lpVtbl->DeviceDisconnect(This,device);
}
static inline void IWMPEvents3_DeviceStatusChange(IWMPEvents3* This,IWMPSyncDevice *device,WMPDeviceStatus status) {
    This->lpVtbl->DeviceStatusChange(This,device,status);
}
static inline void IWMPEvents3_DeviceSyncStateChange(IWMPEvents3* This,IWMPSyncDevice *device,WMPSyncState state) {
    This->lpVtbl->DeviceSyncStateChange(This,device,state);
}
static inline void IWMPEvents3_DeviceSyncError(IWMPEvents3* This,IWMPSyncDevice *device,IDispatch *media) {
    This->lpVtbl->DeviceSyncError(This,device,media);
}
static inline void IWMPEvents3_CreatePartnershipComplete(IWMPEvents3* This,IWMPSyncDevice *device,HRESULT result) {
    This->lpVtbl->CreatePartnershipComplete(This,device,result);
}
/*** IWMPEvents3 methods ***/
static inline void IWMPEvents3_CdromRipStateChange(IWMPEvents3* This,IWMPCdromRip *rip,WMPRipState state) {
    This->lpVtbl->CdromRipStateChange(This,rip,state);
}
static inline void IWMPEvents3_CdromRipMediaError(IWMPEvents3* This,IWMPCdromRip *rip,IDispatch *media) {
    This->lpVtbl->CdromRipMediaError(This,rip,media);
}
static inline void IWMPEvents3_CdromBurnStateChange(IWMPEvents3* This,IWMPCdromBurn *burn,WMPBurnState state) {
    This->lpVtbl->CdromBurnStateChange(This,burn,state);
}
static inline void IWMPEvents3_CdromBurnMediaError(IWMPEvents3* This,IWMPCdromBurn *burn,IDispatch *media) {
    This->lpVtbl->CdromBurnMediaError(This,burn,media);
}
static inline void IWMPEvents3_CdromBurnError(IWMPEvents3* This,IWMPCdromBurn *burn,HRESULT error) {
    This->lpVtbl->CdromBurnError(This,burn,error);
}
static inline void IWMPEvents3_LibraryConnect(IWMPEvents3* This,IWMPLibrary *wmplibrary) {
    This->lpVtbl->LibraryConnect(This,wmplibrary);
}
static inline void IWMPEvents3_LibraryDisconnect(IWMPEvents3* This,IWMPLibrary *wmplibrary) {
    This->lpVtbl->LibraryDisconnect(This,wmplibrary);
}
static inline void IWMPEvents3_FolderScanStateChange(IWMPEvents3* This,WMPFolderScanState state) {
    This->lpVtbl->FolderScanStateChange(This,state);
}
static inline void IWMPEvents3_StringCollectionChange(IWMPEvents3* This,IDispatch *collection,WMPStringCollectionChangeEventType change,LONG index) {
    This->lpVtbl->StringCollectionChange(This,collection,change,index);
}
static inline void IWMPEvents3_MediaCollectionMediaAdded(IWMPEvents3* This,IDispatch *media) {
    This->lpVtbl->MediaCollectionMediaAdded(This,media);
}
static inline void IWMPEvents3_MediaCollectionMediaRemoved(IWMPEvents3* This,IDispatch *media) {
    This->lpVtbl->MediaCollectionMediaRemoved(This,media);
}
#endif
#endif

#endif


#endif  /* __IWMPEvents3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMPEvents4 interface
 */
#ifndef __IWMPEvents4_INTERFACE_DEFINED__
#define __IWMPEvents4_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMPEvents4, 0x26dabcfa, 0x306b, 0x404d, 0x9a,0x6f, 0x63,0x0a,0x84,0x05,0x04,0x8d);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("26dabcfa-306b-404d-9a6f-630a8405048d")
IWMPEvents4 : public IWMPEvents3
{
    virtual void STDMETHODCALLTYPE DeviceEstimation(
        IWMPSyncDevice *device,
        HRESULT result,
        LONGLONG used_space,
        LONGLONG estimated_space) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMPEvents4, 0x26dabcfa, 0x306b, 0x404d, 0x9a,0x6f, 0x63,0x0a,0x84,0x05,0x04,0x8d)
#endif
#else
typedef struct IWMPEvents4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMPEvents4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMPEvents4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMPEvents4 *This);

    /*** IWMPEvents methods ***/
    void (STDMETHODCALLTYPE *OpenStateChange)(
        IWMPEvents4 *This,
        LONG state);

    void (STDMETHODCALLTYPE *PlayStateChange)(
        IWMPEvents4 *This,
        LONG state);

    void (STDMETHODCALLTYPE *AudioLanguageChange)(
        IWMPEvents4 *This,
        LONG lang);

    void (STDMETHODCALLTYPE *StatusChange)(
        IWMPEvents4 *This);

    void (STDMETHODCALLTYPE *ScriptCommand)(
        IWMPEvents4 *This,
        BSTR type,
        BSTR param);

    void (STDMETHODCALLTYPE *NewStream)(
        IWMPEvents4 *This);

    void (STDMETHODCALLTYPE *Disconnect)(
        IWMPEvents4 *This,
        LONG result);

    void (STDMETHODCALLTYPE *Buffering)(
        IWMPEvents4 *This,
        VARIANT_BOOL start);

    void (STDMETHODCALLTYPE *Error)(
        IWMPEvents4 *This);

    void (STDMETHODCALLTYPE *Warning)(
        IWMPEvents4 *This,
        LONG warning,
        LONG param,
        BSTR description);

    void (STDMETHODCALLTYPE *EndOfStream)(
        IWMPEvents4 *This,
        LONG result);

    void (STDMETHODCALLTYPE *PositionChange)(
        IWMPEvents4 *This,
        double old_position,
        double new_position);

    void (STDMETHODCALLTYPE *MarkerHit)(
        IWMPEvents4 *This,
        LONG marker);

    void (STDMETHODCALLTYPE *DurationUnitChange)(
        IWMPEvents4 *This,
        LONG duration);

    void (STDMETHODCALLTYPE *CdromMediaChange)(
        IWMPEvents4 *This,
        LONG num);

    void (STDMETHODCALLTYPE *PlaylistChange)(
        IWMPEvents4 *This,
        IDispatch *playlist,
        WMPPlaylistChangeEventType change);

    void (STDMETHODCALLTYPE *CurrentPlaylistChange)(
        IWMPEvents4 *This,
        WMPPlaylistChangeEventType change);

    void (STDMETHODCALLTYPE *CurrentPlaylistItemAvailable)(
        IWMPEvents4 *This,
        BSTR item);

    void (STDMETHODCALLTYPE *MediaChange)(
        IWMPEvents4 *This,
        IDispatch *item);

    void (STDMETHODCALLTYPE *CurrentMediaItemAvailable)(
        IWMPEvents4 *This,
        BSTR name);

    void (STDMETHODCALLTYPE *CurrentItemChange)(
        IWMPEvents4 *This,
        IDispatch *media);

    void (STDMETHODCALLTYPE *MediaCollectionChange)(
        IWMPEvents4 *This);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringAdded)(
        IWMPEvents4 *This,
        BSTR name,
        BSTR val);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringRemoved)(
        IWMPEvents4 *This,
        BSTR name,
        BSTR val);

    void (STDMETHODCALLTYPE *MediaCollectionAttributeStringChanged)(
        IWMPEvents4 *This,
        BSTR name,
        BSTR old_val,
        BSTR new_val);

    void (STDMETHODCALLTYPE *PlaylistCollectionChange)(
        IWMPEvents4 *This);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistAdded)(
        IWMPEvents4 *This,
        BSTR name);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistRemoved)(
        IWMPEvents4 *This,
        BSTR name);

    void (STDMETHODCALLTYPE *PlaylistCollectionPlaylistSetAsDeleted)(
        IWMPEvents4 *This,
        BSTR name,
        VARIANT_BOOL deleted);

    void (STDMETHODCALLTYPE *ModeChange)(
        IWMPEvents4 *This,
        BSTR ModeName,
        VARIANT_BOOL value);

    void (STDMETHODCALLTYPE *MediaError)(
        IWMPEvents4 *This,
        IDispatch *media);

    void (STDMETHODCALLTYPE *OpenPlaylistSwitch)(
        IWMPEvents4 *This,
        IDispatch *item);

    void (STDMETHODCALLTYPE *DomainChange)(
        IWMPEvents4 *This,
        BSTR domain);

    void (STDMETHODCALLTYPE *SwitchedToPlayerApplication)(
        IWMPEvents4 *This);

    void (STDMETHODCALLTYPE *SwitchedToControl)(
        IWMPEvents4 *This);

    void (STDMETHODCALLTYPE *PlayerDockedStateChange)(
        IWMPEvents4 *This);

    void (STDMETHODCALLTYPE *PlayerReconnect)(
        IWMPEvents4 *This);

    void (STDMETHODCALLTYPE *Click)(
        IWMPEvents4 *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *DoubleClick)(
        IWMPEvents4 *This,
        short button,
        short shift_state,
        LONG fX,
        LONG fY);

    void (STDMETHODCALLTYPE *KeyDown)(
        IWMPEvents4 *This,
        short keycode,
        short shift_state);

    void (STDMETHODCALLTYPE *KeyPress)(
        IWMPEvents4 *This,
        short ascii);

    void (STDMETHODCALLTYPE *KeyUp)(
        IWMPEvents4 *This,
        short keycode,
        short shift_state);

    void (STDMETHODCALLTYPE *MouseDown)(
        IWMPEvents4 *This,
        short button,
        short nShiftState,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *MouseMove)(
        IWMPEvents4 *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    void (STDMETHODCALLTYPE *MouseUp)(
        IWMPEvents4 *This,
        short button,
        short shift_state,
        LONG x,
        LONG y);

    /*** IWMPEvents2 methods ***/
    void (STDMETHODCALLTYPE *DeviceConnect)(
        IWMPEvents4 *This,
        IWMPSyncDevice *device);

    void (STDMETHODCALLTYPE *DeviceDisconnect)(
        IWMPEvents4 *This,
        IWMPSyncDevice *device);

    void (STDMETHODCALLTYPE *DeviceStatusChange)(
        IWMPEvents4 *This,
        IWMPSyncDevice *device,
        WMPDeviceStatus status);

    void (STDMETHODCALLTYPE *DeviceSyncStateChange)(
        IWMPEvents4 *This,
        IWMPSyncDevice *device,
        WMPSyncState state);

    void (STDMETHODCALLTYPE *DeviceSyncError)(
        IWMPEvents4 *This,
        IWMPSyncDevice *device,
        IDispatch *media);

    void (STDMETHODCALLTYPE *CreatePartnershipComplete)(
        IWMPEvents4 *This,
        IWMPSyncDevice *device,
        HRESULT result);

    /*** IWMPEvents3 methods ***/
    void (STDMETHODCALLTYPE *CdromRipStateChange)(
        IWMPEvents4 *This,
        IWMPCdromRip *rip,
        WMPRipState state);

    void (STDMETHODCALLTYPE *CdromRipMediaError)(
        IWMPEvents4 *This,
        IWMPCdromRip *rip,
        IDispatch *media);

    void (STDMETHODCALLTYPE *CdromBurnStateChange)(
        IWMPEvents4 *This,
        IWMPCdromBurn *burn,
        WMPBurnState state);

    void (STDMETHODCALLTYPE *CdromBurnMediaError)(
        IWMPEvents4 *This,
        IWMPCdromBurn *burn,
        IDispatch *media);

    void (STDMETHODCALLTYPE *CdromBurnError)(
        IWMPEvents4 *This,
        IWMPCdromBurn *burn,
        HRESULT error);

    void (STDMETHODCALLTYPE *LibraryConnect)(
        IWMPEvents4 *This,
        IWMPLibrary *wmplibrary);

    void (STDMETHODCALLTYPE *LibraryDisconnect)(
        IWMPEvents4 *This,
        IWMPLibrary *wmplibrary);

    void (STDMETHODCALLTYPE *FolderScanStateChange)(
        IWMPEvents4 *This,
        WMPFolderScanState state);

    void (STDMETHODCALLTYPE *StringCollectionChange)(
        IWMPEvents4 *This,
        IDispatch *collection,
        WMPStringCollectionChangeEventType change,
        LONG index);

    void (STDMETHODCALLTYPE *MediaCollectionMediaAdded)(
        IWMPEvents4 *This,
        IDispatch *media);

    void (STDMETHODCALLTYPE *MediaCollectionMediaRemoved)(
        IWMPEvents4 *This,
        IDispatch *media);

    /*** IWMPEvents4 methods ***/
    void (STDMETHODCALLTYPE *DeviceEstimation)(
        IWMPEvents4 *This,
        IWMPSyncDevice *device,
        HRESULT result,
        LONGLONG used_space,
        LONGLONG estimated_space);

    END_INTERFACE
} IWMPEvents4Vtbl;

interface IWMPEvents4 {
    CONST_VTBL IWMPEvents4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMPEvents4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMPEvents4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMPEvents4_Release(This) (This)->lpVtbl->Release(This)
/*** IWMPEvents methods ***/
#define IWMPEvents4_OpenStateChange(This,state) (This)->lpVtbl->OpenStateChange(This,state)
#define IWMPEvents4_PlayStateChange(This,state) (This)->lpVtbl->PlayStateChange(This,state)
#define IWMPEvents4_AudioLanguageChange(This,lang) (This)->lpVtbl->AudioLanguageChange(This,lang)
#define IWMPEvents4_StatusChange(This) (This)->lpVtbl->StatusChange(This)
#define IWMPEvents4_ScriptCommand(This,type,param) (This)->lpVtbl->ScriptCommand(This,type,param)
#define IWMPEvents4_NewStream(This) (This)->lpVtbl->NewStream(This)
#define IWMPEvents4_Disconnect(This,result) (This)->lpVtbl->Disconnect(This,result)
#define IWMPEvents4_Buffering(This,start) (This)->lpVtbl->Buffering(This,start)
#define IWMPEvents4_Error(This) (This)->lpVtbl->Error(This)
#define IWMPEvents4_Warning(This,warning,param,description) (This)->lpVtbl->Warning(This,warning,param,description)
#define IWMPEvents4_EndOfStream(This,result) (This)->lpVtbl->EndOfStream(This,result)
#define IWMPEvents4_PositionChange(This,old_position,new_position) (This)->lpVtbl->PositionChange(This,old_position,new_position)
#define IWMPEvents4_MarkerHit(This,marker) (This)->lpVtbl->MarkerHit(This,marker)
#define IWMPEvents4_DurationUnitChange(This,duration) (This)->lpVtbl->DurationUnitChange(This,duration)
#define IWMPEvents4_CdromMediaChange(This,num) (This)->lpVtbl->CdromMediaChange(This,num)
#define IWMPEvents4_PlaylistChange(This,playlist,change) (This)->lpVtbl->PlaylistChange(This,playlist,change)
#define IWMPEvents4_CurrentPlaylistChange(This,change) (This)->lpVtbl->CurrentPlaylistChange(This,change)
#define IWMPEvents4_CurrentPlaylistItemAvailable(This,item) (This)->lpVtbl->CurrentPlaylistItemAvailable(This,item)
#define IWMPEvents4_MediaChange(This,item) (This)->lpVtbl->MediaChange(This,item)
#define IWMPEvents4_CurrentMediaItemAvailable(This,name) (This)->lpVtbl->CurrentMediaItemAvailable(This,name)
#define IWMPEvents4_CurrentItemChange(This,media) (This)->lpVtbl->CurrentItemChange(This,media)
#define IWMPEvents4_MediaCollectionChange(This) (This)->lpVtbl->MediaCollectionChange(This)
#define IWMPEvents4_MediaCollectionAttributeStringAdded(This,name,val) (This)->lpVtbl->MediaCollectionAttributeStringAdded(This,name,val)
#define IWMPEvents4_MediaCollectionAttributeStringRemoved(This,name,val) (This)->lpVtbl->MediaCollectionAttributeStringRemoved(This,name,val)
#define IWMPEvents4_MediaCollectionAttributeStringChanged(This,name,old_val,new_val) (This)->lpVtbl->MediaCollectionAttributeStringChanged(This,name,old_val,new_val)
#define IWMPEvents4_PlaylistCollectionChange(This) (This)->lpVtbl->PlaylistCollectionChange(This)
#define IWMPEvents4_PlaylistCollectionPlaylistAdded(This,name) (This)->lpVtbl->PlaylistCollectionPlaylistAdded(This,name)
#define IWMPEvents4_PlaylistCollectionPlaylistRemoved(This,name) (This)->lpVtbl->PlaylistCollectionPlaylistRemoved(This,name)
#define IWMPEvents4_PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted) (This)->lpVtbl->PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted)
#define IWMPEvents4_ModeChange(This,ModeName,value) (This)->lpVtbl->ModeChange(This,ModeName,value)
#define IWMPEvents4_MediaError(This,media) (This)->lpVtbl->MediaError(This,media)
#define IWMPEvents4_OpenPlaylistSwitch(This,item) (This)->lpVtbl->OpenPlaylistSwitch(This,item)
#define IWMPEvents4_DomainChange(This,domain) (This)->lpVtbl->DomainChange(This,domain)
#define IWMPEvents4_SwitchedToPlayerApplication(This) (This)->lpVtbl->SwitchedToPlayerApplication(This)
#define IWMPEvents4_SwitchedToControl(This) (This)->lpVtbl->SwitchedToControl(This)
#define IWMPEvents4_PlayerDockedStateChange(This) (This)->lpVtbl->PlayerDockedStateChange(This)
#define IWMPEvents4_PlayerReconnect(This) (This)->lpVtbl->PlayerReconnect(This)
#define IWMPEvents4_Click(This,button,shift_state,x,y) (This)->lpVtbl->Click(This,button,shift_state,x,y)
#define IWMPEvents4_DoubleClick(This,button,shift_state,fX,fY) (This)->lpVtbl->DoubleClick(This,button,shift_state,fX,fY)
#define IWMPEvents4_KeyDown(This,keycode,shift_state) (This)->lpVtbl->KeyDown(This,keycode,shift_state)
#define IWMPEvents4_KeyPress(This,ascii) (This)->lpVtbl->KeyPress(This,ascii)
#define IWMPEvents4_KeyUp(This,keycode,shift_state) (This)->lpVtbl->KeyUp(This,keycode,shift_state)
#define IWMPEvents4_MouseDown(This,button,nShiftState,x,y) (This)->lpVtbl->MouseDown(This,button,nShiftState,x,y)
#define IWMPEvents4_MouseMove(This,button,shift_state,x,y) (This)->lpVtbl->MouseMove(This,button,shift_state,x,y)
#define IWMPEvents4_MouseUp(This,button,shift_state,x,y) (This)->lpVtbl->MouseUp(This,button,shift_state,x,y)
/*** IWMPEvents2 methods ***/
#define IWMPEvents4_DeviceConnect(This,device) (This)->lpVtbl->DeviceConnect(This,device)
#define IWMPEvents4_DeviceDisconnect(This,device) (This)->lpVtbl->DeviceDisconnect(This,device)
#define IWMPEvents4_DeviceStatusChange(This,device,status) (This)->lpVtbl->DeviceStatusChange(This,device,status)
#define IWMPEvents4_DeviceSyncStateChange(This,device,state) (This)->lpVtbl->DeviceSyncStateChange(This,device,state)
#define IWMPEvents4_DeviceSyncError(This,device,media) (This)->lpVtbl->DeviceSyncError(This,device,media)
#define IWMPEvents4_CreatePartnershipComplete(This,device,result) (This)->lpVtbl->CreatePartnershipComplete(This,device,result)
/*** IWMPEvents3 methods ***/
#define IWMPEvents4_CdromRipStateChange(This,rip,state) (This)->lpVtbl->CdromRipStateChange(This,rip,state)
#define IWMPEvents4_CdromRipMediaError(This,rip,media) (This)->lpVtbl->CdromRipMediaError(This,rip,media)
#define IWMPEvents4_CdromBurnStateChange(This,burn,state) (This)->lpVtbl->CdromBurnStateChange(This,burn,state)
#define IWMPEvents4_CdromBurnMediaError(This,burn,media) (This)->lpVtbl->CdromBurnMediaError(This,burn,media)
#define IWMPEvents4_CdromBurnError(This,burn,error) (This)->lpVtbl->CdromBurnError(This,burn,error)
#define IWMPEvents4_LibraryConnect(This,wmplibrary) (This)->lpVtbl->LibraryConnect(This,wmplibrary)
#define IWMPEvents4_LibraryDisconnect(This,wmplibrary) (This)->lpVtbl->LibraryDisconnect(This,wmplibrary)
#define IWMPEvents4_FolderScanStateChange(This,state) (This)->lpVtbl->FolderScanStateChange(This,state)
#define IWMPEvents4_StringCollectionChange(This,collection,change,index) (This)->lpVtbl->StringCollectionChange(This,collection,change,index)
#define IWMPEvents4_MediaCollectionMediaAdded(This,media) (This)->lpVtbl->MediaCollectionMediaAdded(This,media)
#define IWMPEvents4_MediaCollectionMediaRemoved(This,media) (This)->lpVtbl->MediaCollectionMediaRemoved(This,media)
/*** IWMPEvents4 methods ***/
#define IWMPEvents4_DeviceEstimation(This,device,result,used_space,estimated_space) (This)->lpVtbl->DeviceEstimation(This,device,result,used_space,estimated_space)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMPEvents4_QueryInterface(IWMPEvents4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMPEvents4_AddRef(IWMPEvents4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMPEvents4_Release(IWMPEvents4* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMPEvents methods ***/
static inline void IWMPEvents4_OpenStateChange(IWMPEvents4* This,LONG state) {
    This->lpVtbl->OpenStateChange(This,state);
}
static inline void IWMPEvents4_PlayStateChange(IWMPEvents4* This,LONG state) {
    This->lpVtbl->PlayStateChange(This,state);
}
static inline void IWMPEvents4_AudioLanguageChange(IWMPEvents4* This,LONG lang) {
    This->lpVtbl->AudioLanguageChange(This,lang);
}
static inline void IWMPEvents4_StatusChange(IWMPEvents4* This) {
    This->lpVtbl->StatusChange(This);
}
static inline void IWMPEvents4_ScriptCommand(IWMPEvents4* This,BSTR type,BSTR param) {
    This->lpVtbl->ScriptCommand(This,type,param);
}
static inline void IWMPEvents4_NewStream(IWMPEvents4* This) {
    This->lpVtbl->NewStream(This);
}
static inline void IWMPEvents4_Disconnect(IWMPEvents4* This,LONG result) {
    This->lpVtbl->Disconnect(This,result);
}
static inline void IWMPEvents4_Buffering(IWMPEvents4* This,VARIANT_BOOL start) {
    This->lpVtbl->Buffering(This,start);
}
static inline void IWMPEvents4_Error(IWMPEvents4* This) {
    This->lpVtbl->Error(This);
}
static inline void IWMPEvents4_Warning(IWMPEvents4* This,LONG warning,LONG param,BSTR description) {
    This->lpVtbl->Warning(This,warning,param,description);
}
static inline void IWMPEvents4_EndOfStream(IWMPEvents4* This,LONG result) {
    This->lpVtbl->EndOfStream(This,result);
}
static inline void IWMPEvents4_PositionChange(IWMPEvents4* This,double old_position,double new_position) {
    This->lpVtbl->PositionChange(This,old_position,new_position);
}
static inline void IWMPEvents4_MarkerHit(IWMPEvents4* This,LONG marker) {
    This->lpVtbl->MarkerHit(This,marker);
}
static inline void IWMPEvents4_DurationUnitChange(IWMPEvents4* This,LONG duration) {
    This->lpVtbl->DurationUnitChange(This,duration);
}
static inline void IWMPEvents4_CdromMediaChange(IWMPEvents4* This,LONG num) {
    This->lpVtbl->CdromMediaChange(This,num);
}
static inline void IWMPEvents4_PlaylistChange(IWMPEvents4* This,IDispatch *playlist,WMPPlaylistChangeEventType change) {
    This->lpVtbl->PlaylistChange(This,playlist,change);
}
static inline void IWMPEvents4_CurrentPlaylistChange(IWMPEvents4* This,WMPPlaylistChangeEventType change) {
    This->lpVtbl->CurrentPlaylistChange(This,change);
}
static inline void IWMPEvents4_CurrentPlaylistItemAvailable(IWMPEvents4* This,BSTR item) {
    This->lpVtbl->CurrentPlaylistItemAvailable(This,item);
}
static inline void IWMPEvents4_MediaChange(IWMPEvents4* This,IDispatch *item) {
    This->lpVtbl->MediaChange(This,item);
}
static inline void IWMPEvents4_CurrentMediaItemAvailable(IWMPEvents4* This,BSTR name) {
    This->lpVtbl->CurrentMediaItemAvailable(This,name);
}
static inline void IWMPEvents4_CurrentItemChange(IWMPEvents4* This,IDispatch *media) {
    This->lpVtbl->CurrentItemChange(This,media);
}
static inline void IWMPEvents4_MediaCollectionChange(IWMPEvents4* This) {
    This->lpVtbl->MediaCollectionChange(This);
}
static inline void IWMPEvents4_MediaCollectionAttributeStringAdded(IWMPEvents4* This,BSTR name,BSTR val) {
    This->lpVtbl->MediaCollectionAttributeStringAdded(This,name,val);
}
static inline void IWMPEvents4_MediaCollectionAttributeStringRemoved(IWMPEvents4* This,BSTR name,BSTR val) {
    This->lpVtbl->MediaCollectionAttributeStringRemoved(This,name,val);
}
static inline void IWMPEvents4_MediaCollectionAttributeStringChanged(IWMPEvents4* This,BSTR name,BSTR old_val,BSTR new_val) {
    This->lpVtbl->MediaCollectionAttributeStringChanged(This,name,old_val,new_val);
}
static inline void IWMPEvents4_PlaylistCollectionChange(IWMPEvents4* This) {
    This->lpVtbl->PlaylistCollectionChange(This);
}
static inline void IWMPEvents4_PlaylistCollectionPlaylistAdded(IWMPEvents4* This,BSTR name) {
    This->lpVtbl->PlaylistCollectionPlaylistAdded(This,name);
}
static inline void IWMPEvents4_PlaylistCollectionPlaylistRemoved(IWMPEvents4* This,BSTR name) {
    This->lpVtbl->PlaylistCollectionPlaylistRemoved(This,name);
}
static inline void IWMPEvents4_PlaylistCollectionPlaylistSetAsDeleted(IWMPEvents4* This,BSTR name,VARIANT_BOOL deleted) {
    This->lpVtbl->PlaylistCollectionPlaylistSetAsDeleted(This,name,deleted);
}
static inline void IWMPEvents4_ModeChange(IWMPEvents4* This,BSTR ModeName,VARIANT_BOOL value) {
    This->lpVtbl->ModeChange(This,ModeName,value);
}
static inline void IWMPEvents4_MediaError(IWMPEvents4* This,IDispatch *media) {
    This->lpVtbl->MediaError(This,media);
}
static inline void IWMPEvents4_OpenPlaylistSwitch(IWMPEvents4* This,IDispatch *item) {
    This->lpVtbl->OpenPlaylistSwitch(This,item);
}
static inline void IWMPEvents4_DomainChange(IWMPEvents4* This,BSTR domain) {
    This->lpVtbl->DomainChange(This,domain);
}
static inline void IWMPEvents4_SwitchedToPlayerApplication(IWMPEvents4* This) {
    This->lpVtbl->SwitchedToPlayerApplication(This);
}
static inline void IWMPEvents4_SwitchedToControl(IWMPEvents4* This) {
    This->lpVtbl->SwitchedToControl(This);
}
static inline void IWMPEvents4_PlayerDockedStateChange(IWMPEvents4* This) {
    This->lpVtbl->PlayerDockedStateChange(This);
}
static inline void IWMPEvents4_PlayerReconnect(IWMPEvents4* This) {
    This->lpVtbl->PlayerReconnect(This);
}
static inline void IWMPEvents4_Click(IWMPEvents4* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->Click(This,button,shift_state,x,y);
}
static inline void IWMPEvents4_DoubleClick(IWMPEvents4* This,short button,short shift_state,LONG fX,LONG fY) {
    This->lpVtbl->DoubleClick(This,button,shift_state,fX,fY);
}
static inline void IWMPEvents4_KeyDown(IWMPEvents4* This,short keycode,short shift_state) {
    This->lpVtbl->KeyDown(This,keycode,shift_state);
}
static inline void IWMPEvents4_KeyPress(IWMPEvents4* This,short ascii) {
    This->lpVtbl->KeyPress(This,ascii);
}
static inline void IWMPEvents4_KeyUp(IWMPEvents4* This,short keycode,short shift_state) {
    This->lpVtbl->KeyUp(This,keycode,shift_state);
}
static inline void IWMPEvents4_MouseDown(IWMPEvents4* This,short button,short nShiftState,LONG x,LONG y) {
    This->lpVtbl->MouseDown(This,button,nShiftState,x,y);
}
static inline void IWMPEvents4_MouseMove(IWMPEvents4* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->MouseMove(This,button,shift_state,x,y);
}
static inline void IWMPEvents4_MouseUp(IWMPEvents4* This,short button,short shift_state,LONG x,LONG y) {
    This->lpVtbl->MouseUp(This,button,shift_state,x,y);
}
/*** IWMPEvents2 methods ***/
static inline void IWMPEvents4_DeviceConnect(IWMPEvents4* This,IWMPSyncDevice *device) {
    This->lpVtbl->DeviceConnect(This,device);
}
static inline void IWMPEvents4_DeviceDisconnect(IWMPEvents4* This,IWMPSyncDevice *device) {
    This->lpVtbl->DeviceDisconnect(This,device);
}
static inline void IWMPEvents4_DeviceStatusChange(IWMPEvents4* This,IWMPSyncDevice *device,WMPDeviceStatus status) {
    This->lpVtbl->DeviceStatusChange(This,device,status);
}
static inline void IWMPEvents4_DeviceSyncStateChange(IWMPEvents4* This,IWMPSyncDevice *device,WMPSyncState state) {
    This->lpVtbl->DeviceSyncStateChange(This,device,state);
}
static inline void IWMPEvents4_DeviceSyncError(IWMPEvents4* This,IWMPSyncDevice *device,IDispatch *media) {
    This->lpVtbl->DeviceSyncError(This,device,media);
}
static inline void IWMPEvents4_CreatePartnershipComplete(IWMPEvents4* This,IWMPSyncDevice *device,HRESULT result) {
    This->lpVtbl->CreatePartnershipComplete(This,device,result);
}
/*** IWMPEvents3 methods ***/
static inline void IWMPEvents4_CdromRipStateChange(IWMPEvents4* This,IWMPCdromRip *rip,WMPRipState state) {
    This->lpVtbl->CdromRipStateChange(This,rip,state);
}
static inline void IWMPEvents4_CdromRipMediaError(IWMPEvents4* This,IWMPCdromRip *rip,IDispatch *media) {
    This->lpVtbl->CdromRipMediaError(This,rip,media);
}
static inline void IWMPEvents4_CdromBurnStateChange(IWMPEvents4* This,IWMPCdromBurn *burn,WMPBurnState state) {
    This->lpVtbl->CdromBurnStateChange(This,burn,state);
}
static inline void IWMPEvents4_CdromBurnMediaError(IWMPEvents4* This,IWMPCdromBurn *burn,IDispatch *media) {
    This->lpVtbl->CdromBurnMediaError(This,burn,media);
}
static inline void IWMPEvents4_CdromBurnError(IWMPEvents4* This,IWMPCdromBurn *burn,HRESULT error) {
    This->lpVtbl->CdromBurnError(This,burn,error);
}
static inline void IWMPEvents4_LibraryConnect(IWMPEvents4* This,IWMPLibrary *wmplibrary) {
    This->lpVtbl->LibraryConnect(This,wmplibrary);
}
static inline void IWMPEvents4_LibraryDisconnect(IWMPEvents4* This,IWMPLibrary *wmplibrary) {
    This->lpVtbl->LibraryDisconnect(This,wmplibrary);
}
static inline void IWMPEvents4_FolderScanStateChange(IWMPEvents4* This,WMPFolderScanState state) {
    This->lpVtbl->FolderScanStateChange(This,state);
}
static inline void IWMPEvents4_StringCollectionChange(IWMPEvents4* This,IDispatch *collection,WMPStringCollectionChangeEventType change,LONG index) {
    This->lpVtbl->StringCollectionChange(This,collection,change,index);
}
static inline void IWMPEvents4_MediaCollectionMediaAdded(IWMPEvents4* This,IDispatch *media) {
    This->lpVtbl->MediaCollectionMediaAdded(This,media);
}
static inline void IWMPEvents4_MediaCollectionMediaRemoved(IWMPEvents4* This,IDispatch *media) {
    This->lpVtbl->MediaCollectionMediaRemoved(This,media);
}
/*** IWMPEvents4 methods ***/
static inline void IWMPEvents4_DeviceEstimation(IWMPEvents4* This,IWMPSyncDevice *device,HRESULT result,LONGLONG used_space,LONGLONG estimated_space) {
    This->lpVtbl->DeviceEstimation(This,device,result,used_space,estimated_space);
}
#endif
#endif

#endif


#endif  /* __IWMPEvents4_INTERFACE_DEFINED__ */

/*****************************************************************************
 * _WMPOCXEvents interface
 */
#ifndef ___WMPOCXEvents_INTERFACE_DEFINED__
#define ___WMPOCXEvents_INTERFACE_DEFINED__

DEFINE_GUID(IID__WMPOCXEvents, 0x6bf52a51, 0x394a, 0x11d3, 0xb1,0x53, 0x00,0xc0,0x4f,0x79,0xfa,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6bf52a51-394a-11d3-b153-00c04f79faa6")
_WMPOCXEvents : public IDispatch
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(_WMPOCXEvents, 0x6bf52a51, 0x394a, 0x11d3, 0xb1,0x53, 0x00,0xc0,0x4f,0x79,0xfa,0xa6)
#endif
#else
typedef struct _WMPOCXEventsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        _WMPOCXEvents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        _WMPOCXEvents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        _WMPOCXEvents *This);

    /*** IDispatch methods ***/
    HRESULT (STDMETHODCALLTYPE *GetTypeInfoCount)(
        _WMPOCXEvents *This,
        UINT *pctinfo);

    HRESULT (STDMETHODCALLTYPE *GetTypeInfo)(
        _WMPOCXEvents *This,
        UINT iTInfo,
        LCID lcid,
        ITypeInfo **ppTInfo);

    HRESULT (STDMETHODCALLTYPE *GetIDsOfNames)(
        _WMPOCXEvents *This,
        REFIID riid,
        LPOLESTR *rgszNames,
        UINT cNames,
        LCID lcid,
        DISPID *rgDispId);

    HRESULT (STDMETHODCALLTYPE *Invoke)(
        _WMPOCXEvents *This,
        DISPID dispIdMember,
        REFIID riid,
        LCID lcid,
        WORD wFlags,
        DISPPARAMS *pDispParams,
        VARIANT *pVarResult,
        EXCEPINFO *pExcepInfo,
        UINT *puArgErr);

    END_INTERFACE
} _WMPOCXEventsVtbl;

interface _WMPOCXEvents {
    CONST_VTBL _WMPOCXEventsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define _WMPOCXEvents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define _WMPOCXEvents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define _WMPOCXEvents_Release(This) (This)->lpVtbl->Release(This)
/*** IDispatch methods ***/
#define _WMPOCXEvents_GetTypeInfoCount(This,pctinfo) (This)->lpVtbl->GetTypeInfoCount(This,pctinfo)
#define _WMPOCXEvents_GetTypeInfo(This,iTInfo,lcid,ppTInfo) (This)->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo)
#define _WMPOCXEvents_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) (This)->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)
#define _WMPOCXEvents_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) (This)->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)
#else
/*** IUnknown methods ***/
static inline HRESULT _WMPOCXEvents_QueryInterface(_WMPOCXEvents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG _WMPOCXEvents_AddRef(_WMPOCXEvents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG _WMPOCXEvents_Release(_WMPOCXEvents* This) {
    return This->lpVtbl->Release(This);
}
/*** IDispatch methods ***/
static inline HRESULT _WMPOCXEvents_GetTypeInfoCount(_WMPOCXEvents* This,UINT *pctinfo) {
    return This->lpVtbl->GetTypeInfoCount(This,pctinfo);
}
static inline HRESULT _WMPOCXEvents_GetTypeInfo(_WMPOCXEvents* This,UINT iTInfo,LCID lcid,ITypeInfo **ppTInfo) {
    return This->lpVtbl->GetTypeInfo(This,iTInfo,lcid,ppTInfo);
}
static inline HRESULT _WMPOCXEvents_GetIDsOfNames(_WMPOCXEvents* This,REFIID riid,LPOLESTR *rgszNames,UINT cNames,LCID lcid,DISPID *rgDispId) {
    return This->lpVtbl->GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId);
}
static inline HRESULT _WMPOCXEvents_Invoke(_WMPOCXEvents* This,DISPID dispIdMember,REFIID riid,LCID lcid,WORD wFlags,DISPPARAMS *pDispParams,VARIANT *pVarResult,EXCEPINFO *pExcepInfo,UINT *puArgErr) {
    return This->lpVtbl->Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr);
}
#endif
#endif

#endif


#endif  /* ___WMPOCXEvents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * WindowsMediaPlayer coclass
 */

DEFINE_GUID(CLSID_WindowsMediaPlayer, 0x6bf52a52, 0x394a, 0x11d3, 0xb1,0x53, 0x00,0xc0,0x4f,0x79,0xfa,0xa6);

#ifdef __cplusplus
class DECLSPEC_UUID("6bf52a52-394a-11d3-b153-00c04f79faa6") WindowsMediaPlayer;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(WindowsMediaPlayer, 0x6bf52a52, 0x394a, 0x11d3, 0xb1,0x53, 0x00,0xc0,0x4f,0x79,0xfa,0xa6)
#endif
#endif

#endif /* __WMPLib_LIBRARY_DEFINED__ */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wmp_h__ */
