/*** Autogenerated by WIDL 10.12 from include/mfcaptureengine.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __mfcaptureengine_h__
#define __mfcaptureengine_h__

/* Forward declarations */

#ifndef __IMFCaptureEngineOnEventCallback_FWD_DEFINED__
#define __IMFCaptureEngineOnEventCallback_FWD_DEFINED__
typedef interface IMFCaptureEngineOnEventCallback IMFCaptureEngineOnEventCallback;
#ifdef __cplusplus
interface IMFCaptureEngineOnEventCallback;
#endif /* __cplusplus */
#endif

#ifndef __IMFCaptureEngineOnSampleCallback_FWD_DEFINED__
#define __IMFCaptureEngineOnSampleCallback_FWD_DEFINED__
typedef interface IMFCaptureEngineOnSampleCallback IMFCaptureEngineOnSampleCallback;
#ifdef __cplusplus
interface IMFCaptureEngineOnSampleCallback;
#endif /* __cplusplus */
#endif

#ifndef __IMFCaptureSink_FWD_DEFINED__
#define __IMFCaptureSink_FWD_DEFINED__
typedef interface IMFCaptureSink IMFCaptureSink;
#ifdef __cplusplus
interface IMFCaptureSink;
#endif /* __cplusplus */
#endif

#ifndef __IMFCaptureRecordSink_FWD_DEFINED__
#define __IMFCaptureRecordSink_FWD_DEFINED__
typedef interface IMFCaptureRecordSink IMFCaptureRecordSink;
#ifdef __cplusplus
interface IMFCaptureRecordSink;
#endif /* __cplusplus */
#endif

#ifndef __IMFCapturePreviewSink_FWD_DEFINED__
#define __IMFCapturePreviewSink_FWD_DEFINED__
typedef interface IMFCapturePreviewSink IMFCapturePreviewSink;
#ifdef __cplusplus
interface IMFCapturePreviewSink;
#endif /* __cplusplus */
#endif

#ifndef __IMFCapturePhotoSink_FWD_DEFINED__
#define __IMFCapturePhotoSink_FWD_DEFINED__
typedef interface IMFCapturePhotoSink IMFCapturePhotoSink;
#ifdef __cplusplus
interface IMFCapturePhotoSink;
#endif /* __cplusplus */
#endif

#ifndef __IMFCaptureSource_FWD_DEFINED__
#define __IMFCaptureSource_FWD_DEFINED__
typedef interface IMFCaptureSource IMFCaptureSource;
#ifdef __cplusplus
interface IMFCaptureSource;
#endif /* __cplusplus */
#endif

#ifndef __IMFCaptureEngine_FWD_DEFINED__
#define __IMFCaptureEngine_FWD_DEFINED__
typedef interface IMFCaptureEngine IMFCaptureEngine;
#ifdef __cplusplus
interface IMFCaptureEngine;
#endif /* __cplusplus */
#endif

#ifndef __IMFCaptureEngineClassFactory_FWD_DEFINED__
#define __IMFCaptureEngineClassFactory_FWD_DEFINED__
typedef interface IMFCaptureEngineClassFactory IMFCaptureEngineClassFactory;
#ifdef __cplusplus
interface IMFCaptureEngineClassFactory;
#endif /* __cplusplus */
#endif

#ifndef __IMFCaptureEngineOnSampleCallback2_FWD_DEFINED__
#define __IMFCaptureEngineOnSampleCallback2_FWD_DEFINED__
typedef interface IMFCaptureEngineOnSampleCallback2 IMFCaptureEngineOnSampleCallback2;
#ifdef __cplusplus
interface IMFCaptureEngineOnSampleCallback2;
#endif /* __cplusplus */
#endif

#ifndef __IMFCaptureSink2_FWD_DEFINED__
#define __IMFCaptureSink2_FWD_DEFINED__
typedef interface IMFCaptureSink2 IMFCaptureSink2;
#ifdef __cplusplus
interface IMFCaptureSink2;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <mfobjects.h>
#include <mfidl.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>
#if WINVER >= _WIN32_WINNT_WIN7
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
#ifndef _MFVideoNormalizedRect_
#define _MFVideoNormalizedRect_
typedef struct MFVideoNormalizedRect {
    float left;
    float top;
    float right;
    float bottom;
} MFVideoNormalizedRect;
#endif
typedef enum MF_CAPTURE_ENGINE_DEVICE_TYPE {
    MF_CAPTURE_ENGINE_DEVICE_TYPE_AUDIO = 0x0,
    MF_CAPTURE_ENGINE_DEVICE_TYPE_VIDEO = 0x1
} MF_CAPTURE_ENGINE_DEVICE_TYPE;
typedef enum MF_CAPTURE_ENGINE_SINK_TYPE {
    MF_CAPTURE_ENGINE_SINK_TYPE_RECORD = 0x0,
    MF_CAPTURE_ENGINE_SINK_TYPE_PREVIEW = 0x1,
    MF_CAPTURE_ENGINE_SINK_TYPE_PHOTO = 0x2
} MF_CAPTURE_ENGINE_SINK_TYPE;
enum {
    MF_CAPTURE_ENGINE_PREFERRED_SOURCE_STREAM_FOR_VIDEO_PREVIEW = 0xfffffffa,
    MF_CAPTURE_ENGINE_PREFERRED_SOURCE_STREAM_FOR_VIDEO_RECORD = 0xfffffff9,
    MF_CAPTURE_ENGINE_PREFERRED_SOURCE_STREAM_FOR_PHOTO = 0xfffffff8,
    MF_CAPTURE_ENGINE_PREFERRED_SOURCE_STREAM_FOR_AUDIO = 0xfffffff7,
    MF_CAPTURE_ENGINE_PREFERRED_SOURCE_STREAM_FOR_METADATA = 0xfffffff6,
    MF_CAPTURE_ENGINE_MEDIASOURCE = 0xffffffff
};
typedef enum MF_CAPTURE_ENGINE_STREAM_CATEGORY {
    MF_CAPTURE_ENGINE_STREAM_CATEGORY_VIDEO_PREVIEW = 0x0,
    MF_CAPTURE_ENGINE_STREAM_CATEGORY_VIDEO_CAPTURE = 0x1,
    MF_CAPTURE_ENGINE_STREAM_CATEGORY_PHOTO_INDEPENDENT = 0x2,
    MF_CAPTURE_ENGINE_STREAM_CATEGORY_PHOTO_DEPENDENT = 0x3,
    MF_CAPTURE_ENGINE_STREAM_CATEGORY_AUDIO = 0x4,
    MF_CAPTURE_ENGINE_STREAM_CATEGORY_UNSUPPORTED = 0x5,
    MF_CAPTURE_ENGINE_STREAM_CATEGORY_METADATA = 0x6
} MF_CAPTURE_ENGINE_STREAM_CATEGORY;
typedef enum MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE {
    MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_OTHER = 0,
    MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_COMMUNICATIONS = 1,
    MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_MEDIA = 2,
    MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_GAMECHAT = 3,
    MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_SPEECH = 4,
    MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_FARFIELDSPEECH = 5,
    MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_UNIFORMSPEECH = 6,
    MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE_VOICETYPING = 7
} MF_CAPTURE_ENGINE_MEDIA_CATEGORY_TYPE;
typedef enum MF_CAPTURE_ENGINE_AUDIO_PROCESSING_MODE {
    MF_CAPTURE_ENGINE_AUDIO_PROCESSING_DEFAULT = 0,
    MF_CAPTURE_ENGINE_AUDIO_PROCESSING_RAW = 1
} MF_CAPTURE_ENGINE_AUDIO_PROCESSING_MODE;
EXTERN_GUID(MF_CAPTURE_ENGINE_INITIALIZED, 0x219992bc, 0xcf92, 0x4531, 0xa1, 0xae, 0x96, 0xe1, 0xe8, 0x86, 0xc8, 0xf1);
EXTERN_GUID(MF_CAPTURE_ENGINE_PREVIEW_STARTED, 0xa416df21, 0xf9d3, 0x4a74, 0x99, 0x1b, 0xb8, 0x17, 0x29, 0x89, 0x52, 0xc4);
EXTERN_GUID(MF_CAPTURE_ENGINE_PREVIEW_STOPPED, 0x13d5143c, 0x1edd, 0x4e50,0xa2, 0xef, 0x35, 0x0a, 0x47, 0x67, 0x80, 0x60);
EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_STARTED, 0xac2b027b, 0xddf9, 0x48a0,0x89, 0xbe, 0x38, 0xab, 0x35, 0xef, 0x45, 0xc0);
EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_STOPPED, 0x55e5200a, 0xf98f, 0x4c0d, 0xa9, 0xec, 0x9e, 0xb2, 0x5e, 0xd3, 0xd7, 0x73);
EXTERN_GUID(MF_CAPTURE_ENGINE_PHOTO_TAKEN, 0x3c50c445, 0x7304, 0x48eb,0x86, 0x5d, 0xbb, 0xa1, 0x9b, 0xa3, 0xaf, 0x5c);
EXTERN_GUID(MF_CAPTURE_SOURCE_CURRENT_DEVICE_MEDIA_TYPE_SET, 0xe7e75e4c, 0x039c, 0x4410, 0x81, 0x5b, 0x87, 0x41, 0x30, 0x7b, 0x63, 0xaa);
EXTERN_GUID(MF_CAPTURE_ENGINE_ERROR, 0x46b89fc6, 0x33cc, 0x4399,0x9d, 0xad, 0x78, 0x4d, 0xe7, 0x7d, 0x58, 0x7c);
EXTERN_GUID(MF_CAPTURE_ENGINE_EFFECT_ADDED, 0xaa8dc7b5, 0xa048, 0x4e13, 0x8e, 0xbe, 0xf2, 0x3c, 0x46, 0xc8, 0x30, 0xc1);
EXTERN_GUID(MF_CAPTURE_ENGINE_EFFECT_REMOVED, 0xc6e8db07, 0xfb09, 0x4a48, 0x89, 0xc6, 0xbf, 0x92, 0xa0, 0x42, 0x22, 0xc9);
EXTERN_GUID(MF_CAPTURE_ENGINE_ALL_EFFECTS_REMOVED, 0xfded7521, 0x8ed8, 0x431a, 0xa9, 0x6b, 0xf3, 0xe2, 0x56, 0x5e, 0x98, 0x1c);
EXTERN_GUID(MF_CAPTURE_SINK_PREPARED, 0x7bfce257, 0x12b1, 0x4409, 0x8c, 0x34, 0xd4, 0x45, 0xda, 0xab, 0x75, 0x78);
EXTERN_GUID(MF_CAPTURE_ENGINE_OUTPUT_MEDIA_TYPE_SET, 0xcaaad994, 0x83ec, 0x45e9,0xa3, 0x0a, 0x1f, 0x20, 0xaa, 0xdb, 0x98, 0x31);
EXTERN_GUID(MF_CAPTURE_ENGINE_CAMERA_STREAM_BLOCKED, 0xa4209417, 0x8d39, 0x46f3, 0xb7, 0x59, 0x59, 0x12, 0x52, 0x8f, 0x42, 0x07);
EXTERN_GUID(MF_CAPTURE_ENGINE_CAMERA_STREAM_UNBLOCKED, 0x9be9eef0, 0xcdaf, 0x4717, 0x85, 0x64, 0x83, 0x4a, 0xae, 0x66, 0x41, 0x5c);
EXTERN_GUID(MF_CAPTURE_ENGINE_D3D_MANAGER, 0x76e25e7b, 0xd595, 0x4283, 0x96, 0x2c, 0xc5, 0x94, 0xaf, 0xd7, 0x8d, 0xdf);
EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_SINK_VIDEO_MAX_UNPROCESSED_SAMPLES, 0xb467f705, 0x7913, 0x4894, 0x9d, 0x42, 0xa2, 0x15, 0xfe, 0xa2, 0x3d, 0xa9);
EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_SINK_AUDIO_MAX_UNPROCESSED_SAMPLES, 0x1cddb141, 0xa7f4, 0x4d58, 0x98, 0x96, 0x4d, 0x15, 0xa5, 0x3c, 0x4e, 0xfe);
EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_SINK_VIDEO_MAX_PROCESSED_SAMPLES, 0xe7b4a49e, 0x382c, 0x4aef, 0xa9, 0x46, 0xae, 0xd5, 0x49, 0xb, 0x71, 0x11);
EXTERN_GUID(MF_CAPTURE_ENGINE_RECORD_SINK_AUDIO_MAX_PROCESSED_SAMPLES, 0x9896e12a, 0xf707, 0x4500, 0xb6, 0xbd, 0xdb, 0x8e, 0xb8, 0x10, 0xb5, 0xf);
EXTERN_GUID(MF_CAPTURE_ENGINE_USE_AUDIO_DEVICE_ONLY, 0x1c8077da, 0x8466, 0x4dc4, 0x8b, 0x8e, 0x27, 0x6b, 0x3f, 0x85, 0x92, 0x3b);
EXTERN_GUID(MF_CAPTURE_ENGINE_USE_VIDEO_DEVICE_ONLY, 0x7e025171, 0xcf32, 0x4f2e, 0x8f, 0x19, 0x41, 0x5, 0x77, 0xb7, 0x3a, 0x66);
EXTERN_GUID(MF_CAPTURE_ENGINE_DISABLE_HARDWARE_TRANSFORMS, 0xb7c42a6b, 0x3207,  0x4495, 0xb4, 0xe7, 0x81, 0xf9, 0xc3, 0x5d, 0x59, 0x91);
EXTERN_GUID(MF_CAPTURE_ENGINE_DISABLE_DXVA, 0xf9818862, 0x179d, 0x433f, 0xa3, 0x2f, 0x74, 0xcb, 0xcf, 0x74, 0x46, 0x6d);
EXTERN_GUID(MF_CAPTURE_ENGINE_MEDIASOURCE_CONFIG, 0xbc6989d2, 0x0fc1, 0x46e1, 0xa7, 0x4f, 0xef, 0xd3, 0x6b, 0xc7, 0x88, 0xde);
EXTERN_GUID(MF_CAPTURE_ENGINE_DECODER_MFT_FIELDOFUSE_UNLOCK_Attribute, 0x2b8ad2e8, 0x7acb, 0x4321, 0xa6, 0x06, 0x32, 0x5c, 0x42, 0x49, 0xf4, 0xfc);
EXTERN_GUID(MF_CAPTURE_ENGINE_ENCODER_MFT_FIELDOFUSE_UNLOCK_Attribute, 0x54c63a00, 0x78d5, 0x422f, 0xaa, 0x3e, 0x5e, 0x99, 0xac, 0x64, 0x92, 0x69);
EXTERN_GUID(MF_CAPTURE_ENGINE_ENABLE_CAMERA_STREAMSTATE_NOTIFICATION, 0x4c808e9d, 0xaaed, 0x4713, 0x90, 0xfb, 0xcb, 0x24, 0x06, 0x4a, 0xb8, 0xda);
EXTERN_GUID(MF_CAPTURE_ENGINE_MEDIA_CATEGORY, 0x8e3f5bd5, 0xdbbf, 0x42f0, 0x85, 0x42, 0xd0, 0x7a, 0x39, 0x71, 0x76, 0x2a);
EXTERN_GUID(MF_CAPTURE_ENGINE_AUDIO_PROCESSING, 0x10f1be5e, 0x7e11, 0x410b, 0x97, 0x3d, 0xf4, 0xb6, 0x10, 0x90, 0x0, 0xfe);
EXTERN_GUID(MF_CAPTURE_ENGINE_EVENT_GENERATOR_GUID, 0xabfa8ad5, 0xfc6d, 0x4911, 0x87, 0xe0, 0x96, 0x19, 0x45, 0xf8, 0xf7, 0xce);
EXTERN_GUID(MF_CAPTURE_ENGINE_EVENT_STREAM_INDEX, 0x82697f44, 0xb1cf, 0x42eb, 0x97, 0x53, 0xf8, 0x6d, 0x64, 0x9c, 0x88, 0x65);
EXTERN_GUID(MF_CAPTURE_ENGINE_SELECTEDCAMERAPROFILE, 0x03160b7e, 0x1c6f, 0x4db2, 0xad, 0x56, 0xa7, 0xc4, 0x30, 0xf8, 0x23, 0x92);
EXTERN_GUID(MF_CAPTURE_ENGINE_SELECTEDCAMERAPROFILE_INDEX, 0x3ce88613, 0x2214, 0x46c3, 0xb4, 0x17, 0x82, 0xf8, 0xa3, 0x13, 0xc9, 0xc3);
/*****************************************************************************
 * IMFCaptureEngineOnEventCallback interface
 */
#ifndef __IMFCaptureEngineOnEventCallback_INTERFACE_DEFINED__
#define __IMFCaptureEngineOnEventCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCaptureEngineOnEventCallback, 0xaeda51c0, 0x9025, 0x4983, 0x90,0x12, 0xde,0x59,0x7b,0x88,0xb0,0x89);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("aeda51c0-**************-de597b88b089")
IMFCaptureEngineOnEventCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnEvent(
        IMFMediaEvent *pEvent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCaptureEngineOnEventCallback, 0xaeda51c0, 0x9025, 0x4983, 0x90,0x12, 0xde,0x59,0x7b,0x88,0xb0,0x89)
#endif
#else
typedef struct IMFCaptureEngineOnEventCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCaptureEngineOnEventCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCaptureEngineOnEventCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCaptureEngineOnEventCallback *This);

    /*** IMFCaptureEngineOnEventCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnEvent)(
        IMFCaptureEngineOnEventCallback *This,
        IMFMediaEvent *pEvent);

    END_INTERFACE
} IMFCaptureEngineOnEventCallbackVtbl;

interface IMFCaptureEngineOnEventCallback {
    CONST_VTBL IMFCaptureEngineOnEventCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCaptureEngineOnEventCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCaptureEngineOnEventCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCaptureEngineOnEventCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCaptureEngineOnEventCallback methods ***/
#define IMFCaptureEngineOnEventCallback_OnEvent(This,pEvent) (This)->lpVtbl->OnEvent(This,pEvent)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCaptureEngineOnEventCallback_QueryInterface(IMFCaptureEngineOnEventCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCaptureEngineOnEventCallback_AddRef(IMFCaptureEngineOnEventCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCaptureEngineOnEventCallback_Release(IMFCaptureEngineOnEventCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCaptureEngineOnEventCallback methods ***/
static inline HRESULT IMFCaptureEngineOnEventCallback_OnEvent(IMFCaptureEngineOnEventCallback* This,IMFMediaEvent *pEvent) {
    return This->lpVtbl->OnEvent(This,pEvent);
}
#endif
#endif

#endif


#endif  /* __IMFCaptureEngineOnEventCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFCaptureEngineOnSampleCallback interface
 */
#ifndef __IMFCaptureEngineOnSampleCallback_INTERFACE_DEFINED__
#define __IMFCaptureEngineOnSampleCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCaptureEngineOnSampleCallback, 0x52150b82, 0xab39, 0x4467, 0x98,0x0f, 0xe4,0x8b,0xf0,0x82,0x2e,0xcd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("52150b82-ab39-4467-980f-e48bf0822ecd")
IMFCaptureEngineOnSampleCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE OnSample(
        IMFSample *pSample) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCaptureEngineOnSampleCallback, 0x52150b82, 0xab39, 0x4467, 0x98,0x0f, 0xe4,0x8b,0xf0,0x82,0x2e,0xcd)
#endif
#else
typedef struct IMFCaptureEngineOnSampleCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCaptureEngineOnSampleCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCaptureEngineOnSampleCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCaptureEngineOnSampleCallback *This);

    /*** IMFCaptureEngineOnSampleCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnSample)(
        IMFCaptureEngineOnSampleCallback *This,
        IMFSample *pSample);

    END_INTERFACE
} IMFCaptureEngineOnSampleCallbackVtbl;

interface IMFCaptureEngineOnSampleCallback {
    CONST_VTBL IMFCaptureEngineOnSampleCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCaptureEngineOnSampleCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCaptureEngineOnSampleCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCaptureEngineOnSampleCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCaptureEngineOnSampleCallback methods ***/
#define IMFCaptureEngineOnSampleCallback_OnSample(This,pSample) (This)->lpVtbl->OnSample(This,pSample)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCaptureEngineOnSampleCallback_QueryInterface(IMFCaptureEngineOnSampleCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCaptureEngineOnSampleCallback_AddRef(IMFCaptureEngineOnSampleCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCaptureEngineOnSampleCallback_Release(IMFCaptureEngineOnSampleCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCaptureEngineOnSampleCallback methods ***/
static inline HRESULT IMFCaptureEngineOnSampleCallback_OnSample(IMFCaptureEngineOnSampleCallback* This,IMFSample *pSample) {
    return This->lpVtbl->OnSample(This,pSample);
}
#endif
#endif

#endif


#endif  /* __IMFCaptureEngineOnSampleCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFCaptureSink interface
 */
#ifndef __IMFCaptureSink_INTERFACE_DEFINED__
#define __IMFCaptureSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCaptureSink, 0x72d6135b, 0x35e9, 0x412c, 0xb9,0x26, 0xfd,0x52,0x65,0xf2,0xa8,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("72d6135b-35e9-412c-b926-fd5265f2a885")
IMFCaptureSink : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetOutputMediaType(
        DWORD dwSinkStreamIndex,
        IMFMediaType **ppMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetService(
        DWORD dwSinkStreamIndex,
        REFGUID rguidService,
        REFIID riid,
        IUnknown **ppUnknown) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddStream(
        DWORD dwSourceStreamIndex,
        IMFMediaType *pMediaType,
        IMFAttributes *pAttributes,
        DWORD *pdwSinkStreamIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE Prepare(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAllStreams(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCaptureSink, 0x72d6135b, 0x35e9, 0x412c, 0xb9,0x26, 0xfd,0x52,0x65,0xf2,0xa8,0x85)
#endif
#else
typedef struct IMFCaptureSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCaptureSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCaptureSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCaptureSink *This);

    /*** IMFCaptureSink methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOutputMediaType)(
        IMFCaptureSink *This,
        DWORD dwSinkStreamIndex,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *GetService)(
        IMFCaptureSink *This,
        DWORD dwSinkStreamIndex,
        REFGUID rguidService,
        REFIID riid,
        IUnknown **ppUnknown);

    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IMFCaptureSink *This,
        DWORD dwSourceStreamIndex,
        IMFMediaType *pMediaType,
        IMFAttributes *pAttributes,
        DWORD *pdwSinkStreamIndex);

    HRESULT (STDMETHODCALLTYPE *Prepare)(
        IMFCaptureSink *This);

    HRESULT (STDMETHODCALLTYPE *RemoveAllStreams)(
        IMFCaptureSink *This);

    END_INTERFACE
} IMFCaptureSinkVtbl;

interface IMFCaptureSink {
    CONST_VTBL IMFCaptureSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCaptureSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCaptureSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCaptureSink_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCaptureSink methods ***/
#define IMFCaptureSink_GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType) (This)->lpVtbl->GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType)
#define IMFCaptureSink_GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown) (This)->lpVtbl->GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown)
#define IMFCaptureSink_AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex) (This)->lpVtbl->AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex)
#define IMFCaptureSink_Prepare(This) (This)->lpVtbl->Prepare(This)
#define IMFCaptureSink_RemoveAllStreams(This) (This)->lpVtbl->RemoveAllStreams(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCaptureSink_QueryInterface(IMFCaptureSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCaptureSink_AddRef(IMFCaptureSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCaptureSink_Release(IMFCaptureSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCaptureSink methods ***/
static inline HRESULT IMFCaptureSink_GetOutputMediaType(IMFCaptureSink* This,DWORD dwSinkStreamIndex,IMFMediaType **ppMediaType) {
    return This->lpVtbl->GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType);
}
static inline HRESULT IMFCaptureSink_GetService(IMFCaptureSink* This,DWORD dwSinkStreamIndex,REFGUID rguidService,REFIID riid,IUnknown **ppUnknown) {
    return This->lpVtbl->GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown);
}
static inline HRESULT IMFCaptureSink_AddStream(IMFCaptureSink* This,DWORD dwSourceStreamIndex,IMFMediaType *pMediaType,IMFAttributes *pAttributes,DWORD *pdwSinkStreamIndex) {
    return This->lpVtbl->AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex);
}
static inline HRESULT IMFCaptureSink_Prepare(IMFCaptureSink* This) {
    return This->lpVtbl->Prepare(This);
}
static inline HRESULT IMFCaptureSink_RemoveAllStreams(IMFCaptureSink* This) {
    return This->lpVtbl->RemoveAllStreams(This);
}
#endif
#endif

#endif


#endif  /* __IMFCaptureSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFCaptureRecordSink interface
 */
#ifndef __IMFCaptureRecordSink_INTERFACE_DEFINED__
#define __IMFCaptureRecordSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCaptureRecordSink, 0x3323b55a, 0xf92a, 0x4fe2, 0x8e,0xdc, 0xe9,0xbf,0xc0,0x63,0x4d,0x77);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3323b55a-f92a-4fe2-8edc-e9bfc0634d77")
IMFCaptureRecordSink : public IMFCaptureSink
{
    virtual HRESULT STDMETHODCALLTYPE SetOutputByteStream(
        IMFByteStream *pByteStream,
        REFGUID guidContainerType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputFileName(
        LPCWSTR fileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSampleCallback(
        DWORD dwStreamSinkIndex,
        IMFCaptureEngineOnSampleCallback *pCallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCustomSink(
        IMFMediaSink *pMediaSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRotation(
        DWORD dwStreamIndex,
        DWORD *pdwRotationValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRotation(
        DWORD dwStreamIndex,
        DWORD dwRotationValue) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCaptureRecordSink, 0x3323b55a, 0xf92a, 0x4fe2, 0x8e,0xdc, 0xe9,0xbf,0xc0,0x63,0x4d,0x77)
#endif
#else
typedef struct IMFCaptureRecordSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCaptureRecordSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCaptureRecordSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCaptureRecordSink *This);

    /*** IMFCaptureSink methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOutputMediaType)(
        IMFCaptureRecordSink *This,
        DWORD dwSinkStreamIndex,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *GetService)(
        IMFCaptureRecordSink *This,
        DWORD dwSinkStreamIndex,
        REFGUID rguidService,
        REFIID riid,
        IUnknown **ppUnknown);

    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IMFCaptureRecordSink *This,
        DWORD dwSourceStreamIndex,
        IMFMediaType *pMediaType,
        IMFAttributes *pAttributes,
        DWORD *pdwSinkStreamIndex);

    HRESULT (STDMETHODCALLTYPE *Prepare)(
        IMFCaptureRecordSink *This);

    HRESULT (STDMETHODCALLTYPE *RemoveAllStreams)(
        IMFCaptureRecordSink *This);

    /*** IMFCaptureRecordSink methods ***/
    HRESULT (STDMETHODCALLTYPE *SetOutputByteStream)(
        IMFCaptureRecordSink *This,
        IMFByteStream *pByteStream,
        REFGUID guidContainerType);

    HRESULT (STDMETHODCALLTYPE *SetOutputFileName)(
        IMFCaptureRecordSink *This,
        LPCWSTR fileName);

    HRESULT (STDMETHODCALLTYPE *SetSampleCallback)(
        IMFCaptureRecordSink *This,
        DWORD dwStreamSinkIndex,
        IMFCaptureEngineOnSampleCallback *pCallback);

    HRESULT (STDMETHODCALLTYPE *SetCustomSink)(
        IMFCaptureRecordSink *This,
        IMFMediaSink *pMediaSink);

    HRESULT (STDMETHODCALLTYPE *GetRotation)(
        IMFCaptureRecordSink *This,
        DWORD dwStreamIndex,
        DWORD *pdwRotationValue);

    HRESULT (STDMETHODCALLTYPE *SetRotation)(
        IMFCaptureRecordSink *This,
        DWORD dwStreamIndex,
        DWORD dwRotationValue);

    END_INTERFACE
} IMFCaptureRecordSinkVtbl;

interface IMFCaptureRecordSink {
    CONST_VTBL IMFCaptureRecordSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCaptureRecordSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCaptureRecordSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCaptureRecordSink_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCaptureSink methods ***/
#define IMFCaptureRecordSink_GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType) (This)->lpVtbl->GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType)
#define IMFCaptureRecordSink_GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown) (This)->lpVtbl->GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown)
#define IMFCaptureRecordSink_AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex) (This)->lpVtbl->AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex)
#define IMFCaptureRecordSink_Prepare(This) (This)->lpVtbl->Prepare(This)
#define IMFCaptureRecordSink_RemoveAllStreams(This) (This)->lpVtbl->RemoveAllStreams(This)
/*** IMFCaptureRecordSink methods ***/
#define IMFCaptureRecordSink_SetOutputByteStream(This,pByteStream,guidContainerType) (This)->lpVtbl->SetOutputByteStream(This,pByteStream,guidContainerType)
#define IMFCaptureRecordSink_SetOutputFileName(This,fileName) (This)->lpVtbl->SetOutputFileName(This,fileName)
#define IMFCaptureRecordSink_SetSampleCallback(This,dwStreamSinkIndex,pCallback) (This)->lpVtbl->SetSampleCallback(This,dwStreamSinkIndex,pCallback)
#define IMFCaptureRecordSink_SetCustomSink(This,pMediaSink) (This)->lpVtbl->SetCustomSink(This,pMediaSink)
#define IMFCaptureRecordSink_GetRotation(This,dwStreamIndex,pdwRotationValue) (This)->lpVtbl->GetRotation(This,dwStreamIndex,pdwRotationValue)
#define IMFCaptureRecordSink_SetRotation(This,dwStreamIndex,dwRotationValue) (This)->lpVtbl->SetRotation(This,dwStreamIndex,dwRotationValue)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCaptureRecordSink_QueryInterface(IMFCaptureRecordSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCaptureRecordSink_AddRef(IMFCaptureRecordSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCaptureRecordSink_Release(IMFCaptureRecordSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCaptureSink methods ***/
static inline HRESULT IMFCaptureRecordSink_GetOutputMediaType(IMFCaptureRecordSink* This,DWORD dwSinkStreamIndex,IMFMediaType **ppMediaType) {
    return This->lpVtbl->GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType);
}
static inline HRESULT IMFCaptureRecordSink_GetService(IMFCaptureRecordSink* This,DWORD dwSinkStreamIndex,REFGUID rguidService,REFIID riid,IUnknown **ppUnknown) {
    return This->lpVtbl->GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown);
}
static inline HRESULT IMFCaptureRecordSink_AddStream(IMFCaptureRecordSink* This,DWORD dwSourceStreamIndex,IMFMediaType *pMediaType,IMFAttributes *pAttributes,DWORD *pdwSinkStreamIndex) {
    return This->lpVtbl->AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex);
}
static inline HRESULT IMFCaptureRecordSink_Prepare(IMFCaptureRecordSink* This) {
    return This->lpVtbl->Prepare(This);
}
static inline HRESULT IMFCaptureRecordSink_RemoveAllStreams(IMFCaptureRecordSink* This) {
    return This->lpVtbl->RemoveAllStreams(This);
}
/*** IMFCaptureRecordSink methods ***/
static inline HRESULT IMFCaptureRecordSink_SetOutputByteStream(IMFCaptureRecordSink* This,IMFByteStream *pByteStream,REFGUID guidContainerType) {
    return This->lpVtbl->SetOutputByteStream(This,pByteStream,guidContainerType);
}
static inline HRESULT IMFCaptureRecordSink_SetOutputFileName(IMFCaptureRecordSink* This,LPCWSTR fileName) {
    return This->lpVtbl->SetOutputFileName(This,fileName);
}
static inline HRESULT IMFCaptureRecordSink_SetSampleCallback(IMFCaptureRecordSink* This,DWORD dwStreamSinkIndex,IMFCaptureEngineOnSampleCallback *pCallback) {
    return This->lpVtbl->SetSampleCallback(This,dwStreamSinkIndex,pCallback);
}
static inline HRESULT IMFCaptureRecordSink_SetCustomSink(IMFCaptureRecordSink* This,IMFMediaSink *pMediaSink) {
    return This->lpVtbl->SetCustomSink(This,pMediaSink);
}
static inline HRESULT IMFCaptureRecordSink_GetRotation(IMFCaptureRecordSink* This,DWORD dwStreamIndex,DWORD *pdwRotationValue) {
    return This->lpVtbl->GetRotation(This,dwStreamIndex,pdwRotationValue);
}
static inline HRESULT IMFCaptureRecordSink_SetRotation(IMFCaptureRecordSink* This,DWORD dwStreamIndex,DWORD dwRotationValue) {
    return This->lpVtbl->SetRotation(This,dwStreamIndex,dwRotationValue);
}
#endif
#endif

#endif


#endif  /* __IMFCaptureRecordSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFCapturePreviewSink interface
 */
#ifndef __IMFCapturePreviewSink_INTERFACE_DEFINED__
#define __IMFCapturePreviewSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCapturePreviewSink, 0x77346cfd, 0x5b49, 0x4d73, 0xac,0xe0, 0x5b,0x52,0xa8,0x59,0xf2,0xe0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("77346cfd-5b49-4d73-ace0-5b52a859f2e0")
IMFCapturePreviewSink : public IMFCaptureSink
{
    virtual HRESULT STDMETHODCALLTYPE SetRenderHandle(
        HANDLE handle) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRenderSurface(
        IUnknown *pSurface) = 0;

    virtual HRESULT STDMETHODCALLTYPE UpdateVideo(
        const MFVideoNormalizedRect *pSrc,
        const RECT *pDst,
        const COLORREF *pBorderClr) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSampleCallback(
        DWORD dwStreamSinkIndex,
        IMFCaptureEngineOnSampleCallback *pCallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMirrorState(
        WINBOOL *pfMirrorState) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMirrorState(
        WINBOOL fMirrorState) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRotation(
        DWORD dwStreamIndex,
        DWORD *pdwRotationValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRotation(
        DWORD dwStreamIndex,
        DWORD dwRotationValue) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCustomSink(
        IMFMediaSink *pMediaSink) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCapturePreviewSink, 0x77346cfd, 0x5b49, 0x4d73, 0xac,0xe0, 0x5b,0x52,0xa8,0x59,0xf2,0xe0)
#endif
#else
typedef struct IMFCapturePreviewSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCapturePreviewSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCapturePreviewSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCapturePreviewSink *This);

    /*** IMFCaptureSink methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOutputMediaType)(
        IMFCapturePreviewSink *This,
        DWORD dwSinkStreamIndex,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *GetService)(
        IMFCapturePreviewSink *This,
        DWORD dwSinkStreamIndex,
        REFGUID rguidService,
        REFIID riid,
        IUnknown **ppUnknown);

    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IMFCapturePreviewSink *This,
        DWORD dwSourceStreamIndex,
        IMFMediaType *pMediaType,
        IMFAttributes *pAttributes,
        DWORD *pdwSinkStreamIndex);

    HRESULT (STDMETHODCALLTYPE *Prepare)(
        IMFCapturePreviewSink *This);

    HRESULT (STDMETHODCALLTYPE *RemoveAllStreams)(
        IMFCapturePreviewSink *This);

    /*** IMFCapturePreviewSink methods ***/
    HRESULT (STDMETHODCALLTYPE *SetRenderHandle)(
        IMFCapturePreviewSink *This,
        HANDLE handle);

    HRESULT (STDMETHODCALLTYPE *SetRenderSurface)(
        IMFCapturePreviewSink *This,
        IUnknown *pSurface);

    HRESULT (STDMETHODCALLTYPE *UpdateVideo)(
        IMFCapturePreviewSink *This,
        const MFVideoNormalizedRect *pSrc,
        const RECT *pDst,
        const COLORREF *pBorderClr);

    HRESULT (STDMETHODCALLTYPE *SetSampleCallback)(
        IMFCapturePreviewSink *This,
        DWORD dwStreamSinkIndex,
        IMFCaptureEngineOnSampleCallback *pCallback);

    HRESULT (STDMETHODCALLTYPE *GetMirrorState)(
        IMFCapturePreviewSink *This,
        WINBOOL *pfMirrorState);

    HRESULT (STDMETHODCALLTYPE *SetMirrorState)(
        IMFCapturePreviewSink *This,
        WINBOOL fMirrorState);

    HRESULT (STDMETHODCALLTYPE *GetRotation)(
        IMFCapturePreviewSink *This,
        DWORD dwStreamIndex,
        DWORD *pdwRotationValue);

    HRESULT (STDMETHODCALLTYPE *SetRotation)(
        IMFCapturePreviewSink *This,
        DWORD dwStreamIndex,
        DWORD dwRotationValue);

    HRESULT (STDMETHODCALLTYPE *SetCustomSink)(
        IMFCapturePreviewSink *This,
        IMFMediaSink *pMediaSink);

    END_INTERFACE
} IMFCapturePreviewSinkVtbl;

interface IMFCapturePreviewSink {
    CONST_VTBL IMFCapturePreviewSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCapturePreviewSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCapturePreviewSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCapturePreviewSink_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCaptureSink methods ***/
#define IMFCapturePreviewSink_GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType) (This)->lpVtbl->GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType)
#define IMFCapturePreviewSink_GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown) (This)->lpVtbl->GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown)
#define IMFCapturePreviewSink_AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex) (This)->lpVtbl->AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex)
#define IMFCapturePreviewSink_Prepare(This) (This)->lpVtbl->Prepare(This)
#define IMFCapturePreviewSink_RemoveAllStreams(This) (This)->lpVtbl->RemoveAllStreams(This)
/*** IMFCapturePreviewSink methods ***/
#define IMFCapturePreviewSink_SetRenderHandle(This,handle) (This)->lpVtbl->SetRenderHandle(This,handle)
#define IMFCapturePreviewSink_SetRenderSurface(This,pSurface) (This)->lpVtbl->SetRenderSurface(This,pSurface)
#define IMFCapturePreviewSink_UpdateVideo(This,pSrc,pDst,pBorderClr) (This)->lpVtbl->UpdateVideo(This,pSrc,pDst,pBorderClr)
#define IMFCapturePreviewSink_SetSampleCallback(This,dwStreamSinkIndex,pCallback) (This)->lpVtbl->SetSampleCallback(This,dwStreamSinkIndex,pCallback)
#define IMFCapturePreviewSink_GetMirrorState(This,pfMirrorState) (This)->lpVtbl->GetMirrorState(This,pfMirrorState)
#define IMFCapturePreviewSink_SetMirrorState(This,fMirrorState) (This)->lpVtbl->SetMirrorState(This,fMirrorState)
#define IMFCapturePreviewSink_GetRotation(This,dwStreamIndex,pdwRotationValue) (This)->lpVtbl->GetRotation(This,dwStreamIndex,pdwRotationValue)
#define IMFCapturePreviewSink_SetRotation(This,dwStreamIndex,dwRotationValue) (This)->lpVtbl->SetRotation(This,dwStreamIndex,dwRotationValue)
#define IMFCapturePreviewSink_SetCustomSink(This,pMediaSink) (This)->lpVtbl->SetCustomSink(This,pMediaSink)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCapturePreviewSink_QueryInterface(IMFCapturePreviewSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCapturePreviewSink_AddRef(IMFCapturePreviewSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCapturePreviewSink_Release(IMFCapturePreviewSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCaptureSink methods ***/
static inline HRESULT IMFCapturePreviewSink_GetOutputMediaType(IMFCapturePreviewSink* This,DWORD dwSinkStreamIndex,IMFMediaType **ppMediaType) {
    return This->lpVtbl->GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType);
}
static inline HRESULT IMFCapturePreviewSink_GetService(IMFCapturePreviewSink* This,DWORD dwSinkStreamIndex,REFGUID rguidService,REFIID riid,IUnknown **ppUnknown) {
    return This->lpVtbl->GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown);
}
static inline HRESULT IMFCapturePreviewSink_AddStream(IMFCapturePreviewSink* This,DWORD dwSourceStreamIndex,IMFMediaType *pMediaType,IMFAttributes *pAttributes,DWORD *pdwSinkStreamIndex) {
    return This->lpVtbl->AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex);
}
static inline HRESULT IMFCapturePreviewSink_Prepare(IMFCapturePreviewSink* This) {
    return This->lpVtbl->Prepare(This);
}
static inline HRESULT IMFCapturePreviewSink_RemoveAllStreams(IMFCapturePreviewSink* This) {
    return This->lpVtbl->RemoveAllStreams(This);
}
/*** IMFCapturePreviewSink methods ***/
static inline HRESULT IMFCapturePreviewSink_SetRenderHandle(IMFCapturePreviewSink* This,HANDLE handle) {
    return This->lpVtbl->SetRenderHandle(This,handle);
}
static inline HRESULT IMFCapturePreviewSink_SetRenderSurface(IMFCapturePreviewSink* This,IUnknown *pSurface) {
    return This->lpVtbl->SetRenderSurface(This,pSurface);
}
static inline HRESULT IMFCapturePreviewSink_UpdateVideo(IMFCapturePreviewSink* This,const MFVideoNormalizedRect *pSrc,const RECT *pDst,const COLORREF *pBorderClr) {
    return This->lpVtbl->UpdateVideo(This,pSrc,pDst,pBorderClr);
}
static inline HRESULT IMFCapturePreviewSink_SetSampleCallback(IMFCapturePreviewSink* This,DWORD dwStreamSinkIndex,IMFCaptureEngineOnSampleCallback *pCallback) {
    return This->lpVtbl->SetSampleCallback(This,dwStreamSinkIndex,pCallback);
}
static inline HRESULT IMFCapturePreviewSink_GetMirrorState(IMFCapturePreviewSink* This,WINBOOL *pfMirrorState) {
    return This->lpVtbl->GetMirrorState(This,pfMirrorState);
}
static inline HRESULT IMFCapturePreviewSink_SetMirrorState(IMFCapturePreviewSink* This,WINBOOL fMirrorState) {
    return This->lpVtbl->SetMirrorState(This,fMirrorState);
}
static inline HRESULT IMFCapturePreviewSink_GetRotation(IMFCapturePreviewSink* This,DWORD dwStreamIndex,DWORD *pdwRotationValue) {
    return This->lpVtbl->GetRotation(This,dwStreamIndex,pdwRotationValue);
}
static inline HRESULT IMFCapturePreviewSink_SetRotation(IMFCapturePreviewSink* This,DWORD dwStreamIndex,DWORD dwRotationValue) {
    return This->lpVtbl->SetRotation(This,dwStreamIndex,dwRotationValue);
}
static inline HRESULT IMFCapturePreviewSink_SetCustomSink(IMFCapturePreviewSink* This,IMFMediaSink *pMediaSink) {
    return This->lpVtbl->SetCustomSink(This,pMediaSink);
}
#endif
#endif

#endif


#endif  /* __IMFCapturePreviewSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFCapturePhotoSink interface
 */
#ifndef __IMFCapturePhotoSink_INTERFACE_DEFINED__
#define __IMFCapturePhotoSink_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCapturePhotoSink, 0xd2d43cc8, 0x48bb, 0x4aa7, 0x95,0xdb, 0x10,0xc0,0x69,0x77,0xe7,0x77);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d2d43cc8-48bb-4aa7-95db-10c06977e777")
IMFCapturePhotoSink : public IMFCaptureSink
{
    virtual HRESULT STDMETHODCALLTYPE SetOutputFileName(
        LPCWSTR fileName) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSampleCallback(
        IMFCaptureEngineOnSampleCallback *pCallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputByteStream(
        IMFByteStream *pByteStream) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCapturePhotoSink, 0xd2d43cc8, 0x48bb, 0x4aa7, 0x95,0xdb, 0x10,0xc0,0x69,0x77,0xe7,0x77)
#endif
#else
typedef struct IMFCapturePhotoSinkVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCapturePhotoSink *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCapturePhotoSink *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCapturePhotoSink *This);

    /*** IMFCaptureSink methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOutputMediaType)(
        IMFCapturePhotoSink *This,
        DWORD dwSinkStreamIndex,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *GetService)(
        IMFCapturePhotoSink *This,
        DWORD dwSinkStreamIndex,
        REFGUID rguidService,
        REFIID riid,
        IUnknown **ppUnknown);

    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IMFCapturePhotoSink *This,
        DWORD dwSourceStreamIndex,
        IMFMediaType *pMediaType,
        IMFAttributes *pAttributes,
        DWORD *pdwSinkStreamIndex);

    HRESULT (STDMETHODCALLTYPE *Prepare)(
        IMFCapturePhotoSink *This);

    HRESULT (STDMETHODCALLTYPE *RemoveAllStreams)(
        IMFCapturePhotoSink *This);

    /*** IMFCapturePhotoSink methods ***/
    HRESULT (STDMETHODCALLTYPE *SetOutputFileName)(
        IMFCapturePhotoSink *This,
        LPCWSTR fileName);

    HRESULT (STDMETHODCALLTYPE *SetSampleCallback)(
        IMFCapturePhotoSink *This,
        IMFCaptureEngineOnSampleCallback *pCallback);

    HRESULT (STDMETHODCALLTYPE *SetOutputByteStream)(
        IMFCapturePhotoSink *This,
        IMFByteStream *pByteStream);

    END_INTERFACE
} IMFCapturePhotoSinkVtbl;

interface IMFCapturePhotoSink {
    CONST_VTBL IMFCapturePhotoSinkVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCapturePhotoSink_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCapturePhotoSink_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCapturePhotoSink_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCaptureSink methods ***/
#define IMFCapturePhotoSink_GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType) (This)->lpVtbl->GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType)
#define IMFCapturePhotoSink_GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown) (This)->lpVtbl->GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown)
#define IMFCapturePhotoSink_AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex) (This)->lpVtbl->AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex)
#define IMFCapturePhotoSink_Prepare(This) (This)->lpVtbl->Prepare(This)
#define IMFCapturePhotoSink_RemoveAllStreams(This) (This)->lpVtbl->RemoveAllStreams(This)
/*** IMFCapturePhotoSink methods ***/
#define IMFCapturePhotoSink_SetOutputFileName(This,fileName) (This)->lpVtbl->SetOutputFileName(This,fileName)
#define IMFCapturePhotoSink_SetSampleCallback(This,pCallback) (This)->lpVtbl->SetSampleCallback(This,pCallback)
#define IMFCapturePhotoSink_SetOutputByteStream(This,pByteStream) (This)->lpVtbl->SetOutputByteStream(This,pByteStream)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCapturePhotoSink_QueryInterface(IMFCapturePhotoSink* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCapturePhotoSink_AddRef(IMFCapturePhotoSink* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCapturePhotoSink_Release(IMFCapturePhotoSink* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCaptureSink methods ***/
static inline HRESULT IMFCapturePhotoSink_GetOutputMediaType(IMFCapturePhotoSink* This,DWORD dwSinkStreamIndex,IMFMediaType **ppMediaType) {
    return This->lpVtbl->GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType);
}
static inline HRESULT IMFCapturePhotoSink_GetService(IMFCapturePhotoSink* This,DWORD dwSinkStreamIndex,REFGUID rguidService,REFIID riid,IUnknown **ppUnknown) {
    return This->lpVtbl->GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown);
}
static inline HRESULT IMFCapturePhotoSink_AddStream(IMFCapturePhotoSink* This,DWORD dwSourceStreamIndex,IMFMediaType *pMediaType,IMFAttributes *pAttributes,DWORD *pdwSinkStreamIndex) {
    return This->lpVtbl->AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex);
}
static inline HRESULT IMFCapturePhotoSink_Prepare(IMFCapturePhotoSink* This) {
    return This->lpVtbl->Prepare(This);
}
static inline HRESULT IMFCapturePhotoSink_RemoveAllStreams(IMFCapturePhotoSink* This) {
    return This->lpVtbl->RemoveAllStreams(This);
}
/*** IMFCapturePhotoSink methods ***/
static inline HRESULT IMFCapturePhotoSink_SetOutputFileName(IMFCapturePhotoSink* This,LPCWSTR fileName) {
    return This->lpVtbl->SetOutputFileName(This,fileName);
}
static inline HRESULT IMFCapturePhotoSink_SetSampleCallback(IMFCapturePhotoSink* This,IMFCaptureEngineOnSampleCallback *pCallback) {
    return This->lpVtbl->SetSampleCallback(This,pCallback);
}
static inline HRESULT IMFCapturePhotoSink_SetOutputByteStream(IMFCapturePhotoSink* This,IMFByteStream *pByteStream) {
    return This->lpVtbl->SetOutputByteStream(This,pByteStream);
}
#endif
#endif

#endif


#endif  /* __IMFCapturePhotoSink_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFCaptureSource interface
 */
#ifndef __IMFCaptureSource_INTERFACE_DEFINED__
#define __IMFCaptureSource_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCaptureSource, 0x439a42a8, 0x0d2c, 0x4505, 0xbe,0x83, 0xf7,0x9b,0x2a,0x05,0xd5,0xc4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("439a42a8-0d2c-4505-be83-f79b2a05d5c4")
IMFCaptureSource : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetCaptureDeviceSource(
        MF_CAPTURE_ENGINE_DEVICE_TYPE mfCaptureEngineDeviceType,
        IMFMediaSource **ppMediaSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCaptureDeviceActivate(
        MF_CAPTURE_ENGINE_DEVICE_TYPE mfCaptureEngineDeviceType,
        IMFActivate **ppActivate) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetService(
        REFIID rguidService,
        REFIID riid,
        IUnknown **ppUnknown) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddEffect(
        DWORD dwSourceStreamIndex,
        IUnknown *pUnknown) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveEffect(
        DWORD dwSourceStreamIndex,
        IUnknown *pUnknown) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAllEffects(
        DWORD dwSourceStreamIndex) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAvailableDeviceMediaType(
        DWORD dwSourceStreamIndex,
        DWORD dwMediaTypeIndex,
        IMFMediaType **ppMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCurrentDeviceMediaType(
        DWORD dwSourceStreamIndex,
        IMFMediaType *pMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentDeviceMediaType(
        DWORD dwSourceStreamIndex,
        IMFMediaType **ppMediaType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceStreamCount(
        DWORD *pdwStreamCount) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDeviceStreamCategory(
        DWORD dwSourceStreamIndex,
        MF_CAPTURE_ENGINE_STREAM_CATEGORY *pStreamCategory) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMirrorState(
        DWORD dwStreamIndex,
        WINBOOL *pfMirrorState) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMirrorState(
        DWORD dwStreamIndex,
        WINBOOL fMirrorState) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamIndexFromFriendlyName(
        UINT32 uifriendlyName,
        DWORD *pdwActualStreamIndex) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCaptureSource, 0x439a42a8, 0x0d2c, 0x4505, 0xbe,0x83, 0xf7,0x9b,0x2a,0x05,0xd5,0xc4)
#endif
#else
typedef struct IMFCaptureSourceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCaptureSource *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCaptureSource *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCaptureSource *This);

    /*** IMFCaptureSource methods ***/
    HRESULT (STDMETHODCALLTYPE *GetCaptureDeviceSource)(
        IMFCaptureSource *This,
        MF_CAPTURE_ENGINE_DEVICE_TYPE mfCaptureEngineDeviceType,
        IMFMediaSource **ppMediaSource);

    HRESULT (STDMETHODCALLTYPE *GetCaptureDeviceActivate)(
        IMFCaptureSource *This,
        MF_CAPTURE_ENGINE_DEVICE_TYPE mfCaptureEngineDeviceType,
        IMFActivate **ppActivate);

    HRESULT (STDMETHODCALLTYPE *GetService)(
        IMFCaptureSource *This,
        REFIID rguidService,
        REFIID riid,
        IUnknown **ppUnknown);

    HRESULT (STDMETHODCALLTYPE *AddEffect)(
        IMFCaptureSource *This,
        DWORD dwSourceStreamIndex,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *RemoveEffect)(
        IMFCaptureSource *This,
        DWORD dwSourceStreamIndex,
        IUnknown *pUnknown);

    HRESULT (STDMETHODCALLTYPE *RemoveAllEffects)(
        IMFCaptureSource *This,
        DWORD dwSourceStreamIndex);

    HRESULT (STDMETHODCALLTYPE *GetAvailableDeviceMediaType)(
        IMFCaptureSource *This,
        DWORD dwSourceStreamIndex,
        DWORD dwMediaTypeIndex,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *SetCurrentDeviceMediaType)(
        IMFCaptureSource *This,
        DWORD dwSourceStreamIndex,
        IMFMediaType *pMediaType);

    HRESULT (STDMETHODCALLTYPE *GetCurrentDeviceMediaType)(
        IMFCaptureSource *This,
        DWORD dwSourceStreamIndex,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *GetDeviceStreamCount)(
        IMFCaptureSource *This,
        DWORD *pdwStreamCount);

    HRESULT (STDMETHODCALLTYPE *GetDeviceStreamCategory)(
        IMFCaptureSource *This,
        DWORD dwSourceStreamIndex,
        MF_CAPTURE_ENGINE_STREAM_CATEGORY *pStreamCategory);

    HRESULT (STDMETHODCALLTYPE *GetMirrorState)(
        IMFCaptureSource *This,
        DWORD dwStreamIndex,
        WINBOOL *pfMirrorState);

    HRESULT (STDMETHODCALLTYPE *SetMirrorState)(
        IMFCaptureSource *This,
        DWORD dwStreamIndex,
        WINBOOL fMirrorState);

    HRESULT (STDMETHODCALLTYPE *GetStreamIndexFromFriendlyName)(
        IMFCaptureSource *This,
        UINT32 uifriendlyName,
        DWORD *pdwActualStreamIndex);

    END_INTERFACE
} IMFCaptureSourceVtbl;

interface IMFCaptureSource {
    CONST_VTBL IMFCaptureSourceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCaptureSource_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCaptureSource_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCaptureSource_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCaptureSource methods ***/
#define IMFCaptureSource_GetCaptureDeviceSource(This,mfCaptureEngineDeviceType,ppMediaSource) (This)->lpVtbl->GetCaptureDeviceSource(This,mfCaptureEngineDeviceType,ppMediaSource)
#define IMFCaptureSource_GetCaptureDeviceActivate(This,mfCaptureEngineDeviceType,ppActivate) (This)->lpVtbl->GetCaptureDeviceActivate(This,mfCaptureEngineDeviceType,ppActivate)
#define IMFCaptureSource_GetService(This,rguidService,riid,ppUnknown) (This)->lpVtbl->GetService(This,rguidService,riid,ppUnknown)
#define IMFCaptureSource_AddEffect(This,dwSourceStreamIndex,pUnknown) (This)->lpVtbl->AddEffect(This,dwSourceStreamIndex,pUnknown)
#define IMFCaptureSource_RemoveEffect(This,dwSourceStreamIndex,pUnknown) (This)->lpVtbl->RemoveEffect(This,dwSourceStreamIndex,pUnknown)
#define IMFCaptureSource_RemoveAllEffects(This,dwSourceStreamIndex) (This)->lpVtbl->RemoveAllEffects(This,dwSourceStreamIndex)
#define IMFCaptureSource_GetAvailableDeviceMediaType(This,dwSourceStreamIndex,dwMediaTypeIndex,ppMediaType) (This)->lpVtbl->GetAvailableDeviceMediaType(This,dwSourceStreamIndex,dwMediaTypeIndex,ppMediaType)
#define IMFCaptureSource_SetCurrentDeviceMediaType(This,dwSourceStreamIndex,pMediaType) (This)->lpVtbl->SetCurrentDeviceMediaType(This,dwSourceStreamIndex,pMediaType)
#define IMFCaptureSource_GetCurrentDeviceMediaType(This,dwSourceStreamIndex,ppMediaType) (This)->lpVtbl->GetCurrentDeviceMediaType(This,dwSourceStreamIndex,ppMediaType)
#define IMFCaptureSource_GetDeviceStreamCount(This,pdwStreamCount) (This)->lpVtbl->GetDeviceStreamCount(This,pdwStreamCount)
#define IMFCaptureSource_GetDeviceStreamCategory(This,dwSourceStreamIndex,pStreamCategory) (This)->lpVtbl->GetDeviceStreamCategory(This,dwSourceStreamIndex,pStreamCategory)
#define IMFCaptureSource_GetMirrorState(This,dwStreamIndex,pfMirrorState) (This)->lpVtbl->GetMirrorState(This,dwStreamIndex,pfMirrorState)
#define IMFCaptureSource_SetMirrorState(This,dwStreamIndex,fMirrorState) (This)->lpVtbl->SetMirrorState(This,dwStreamIndex,fMirrorState)
#define IMFCaptureSource_GetStreamIndexFromFriendlyName(This,uifriendlyName,pdwActualStreamIndex) (This)->lpVtbl->GetStreamIndexFromFriendlyName(This,uifriendlyName,pdwActualStreamIndex)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCaptureSource_QueryInterface(IMFCaptureSource* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCaptureSource_AddRef(IMFCaptureSource* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCaptureSource_Release(IMFCaptureSource* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCaptureSource methods ***/
static inline HRESULT IMFCaptureSource_GetCaptureDeviceSource(IMFCaptureSource* This,MF_CAPTURE_ENGINE_DEVICE_TYPE mfCaptureEngineDeviceType,IMFMediaSource **ppMediaSource) {
    return This->lpVtbl->GetCaptureDeviceSource(This,mfCaptureEngineDeviceType,ppMediaSource);
}
static inline HRESULT IMFCaptureSource_GetCaptureDeviceActivate(IMFCaptureSource* This,MF_CAPTURE_ENGINE_DEVICE_TYPE mfCaptureEngineDeviceType,IMFActivate **ppActivate) {
    return This->lpVtbl->GetCaptureDeviceActivate(This,mfCaptureEngineDeviceType,ppActivate);
}
static inline HRESULT IMFCaptureSource_GetService(IMFCaptureSource* This,REFIID rguidService,REFIID riid,IUnknown **ppUnknown) {
    return This->lpVtbl->GetService(This,rguidService,riid,ppUnknown);
}
static inline HRESULT IMFCaptureSource_AddEffect(IMFCaptureSource* This,DWORD dwSourceStreamIndex,IUnknown *pUnknown) {
    return This->lpVtbl->AddEffect(This,dwSourceStreamIndex,pUnknown);
}
static inline HRESULT IMFCaptureSource_RemoveEffect(IMFCaptureSource* This,DWORD dwSourceStreamIndex,IUnknown *pUnknown) {
    return This->lpVtbl->RemoveEffect(This,dwSourceStreamIndex,pUnknown);
}
static inline HRESULT IMFCaptureSource_RemoveAllEffects(IMFCaptureSource* This,DWORD dwSourceStreamIndex) {
    return This->lpVtbl->RemoveAllEffects(This,dwSourceStreamIndex);
}
static inline HRESULT IMFCaptureSource_GetAvailableDeviceMediaType(IMFCaptureSource* This,DWORD dwSourceStreamIndex,DWORD dwMediaTypeIndex,IMFMediaType **ppMediaType) {
    return This->lpVtbl->GetAvailableDeviceMediaType(This,dwSourceStreamIndex,dwMediaTypeIndex,ppMediaType);
}
static inline HRESULT IMFCaptureSource_SetCurrentDeviceMediaType(IMFCaptureSource* This,DWORD dwSourceStreamIndex,IMFMediaType *pMediaType) {
    return This->lpVtbl->SetCurrentDeviceMediaType(This,dwSourceStreamIndex,pMediaType);
}
static inline HRESULT IMFCaptureSource_GetCurrentDeviceMediaType(IMFCaptureSource* This,DWORD dwSourceStreamIndex,IMFMediaType **ppMediaType) {
    return This->lpVtbl->GetCurrentDeviceMediaType(This,dwSourceStreamIndex,ppMediaType);
}
static inline HRESULT IMFCaptureSource_GetDeviceStreamCount(IMFCaptureSource* This,DWORD *pdwStreamCount) {
    return This->lpVtbl->GetDeviceStreamCount(This,pdwStreamCount);
}
static inline HRESULT IMFCaptureSource_GetDeviceStreamCategory(IMFCaptureSource* This,DWORD dwSourceStreamIndex,MF_CAPTURE_ENGINE_STREAM_CATEGORY *pStreamCategory) {
    return This->lpVtbl->GetDeviceStreamCategory(This,dwSourceStreamIndex,pStreamCategory);
}
static inline HRESULT IMFCaptureSource_GetMirrorState(IMFCaptureSource* This,DWORD dwStreamIndex,WINBOOL *pfMirrorState) {
    return This->lpVtbl->GetMirrorState(This,dwStreamIndex,pfMirrorState);
}
static inline HRESULT IMFCaptureSource_SetMirrorState(IMFCaptureSource* This,DWORD dwStreamIndex,WINBOOL fMirrorState) {
    return This->lpVtbl->SetMirrorState(This,dwStreamIndex,fMirrorState);
}
static inline HRESULT IMFCaptureSource_GetStreamIndexFromFriendlyName(IMFCaptureSource* This,UINT32 uifriendlyName,DWORD *pdwActualStreamIndex) {
    return This->lpVtbl->GetStreamIndexFromFriendlyName(This,uifriendlyName,pdwActualStreamIndex);
}
#endif
#endif

#endif


#endif  /* __IMFCaptureSource_INTERFACE_DEFINED__ */

EXTERN_GUID(CLSID_MFCaptureEngine, 0xefce38d3, 0x8914, 0x4674,0xa7, 0xdf, 0xae, 0x1b, 0x3d, 0x65, 0x4b, 0x8a);
/*****************************************************************************
 * IMFCaptureEngine interface
 */
#ifndef __IMFCaptureEngine_INTERFACE_DEFINED__
#define __IMFCaptureEngine_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCaptureEngine, 0xa6bba433, 0x176b, 0x48b2, 0xb3,0x75, 0x53,0xaa,0x03,0x47,0x32,0x07);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a6bba433-176b-48b2-b375-53aa03473207")
IMFCaptureEngine : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Initialize(
        IMFCaptureEngineOnEventCallback *pEventCallback,
        IMFAttributes *pAttributes,
        IUnknown *pAudioSource,
        IUnknown *pVideoSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartPreview(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopPreview(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartRecord(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE StopRecord(
        WINBOOL bFinalize,
        WINBOOL bFlushUnprocessedSamples) = 0;

    virtual HRESULT STDMETHODCALLTYPE TakePhoto(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSink(
        MF_CAPTURE_ENGINE_SINK_TYPE mfCaptureEngineSinkType,
        IMFCaptureSink **ppSink) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSource(
        IMFCaptureSource **ppSource) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCaptureEngine, 0xa6bba433, 0x176b, 0x48b2, 0xb3,0x75, 0x53,0xaa,0x03,0x47,0x32,0x07)
#endif
#else
typedef struct IMFCaptureEngineVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCaptureEngine *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCaptureEngine *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCaptureEngine *This);

    /*** IMFCaptureEngine methods ***/
    HRESULT (STDMETHODCALLTYPE *Initialize)(
        IMFCaptureEngine *This,
        IMFCaptureEngineOnEventCallback *pEventCallback,
        IMFAttributes *pAttributes,
        IUnknown *pAudioSource,
        IUnknown *pVideoSource);

    HRESULT (STDMETHODCALLTYPE *StartPreview)(
        IMFCaptureEngine *This);

    HRESULT (STDMETHODCALLTYPE *StopPreview)(
        IMFCaptureEngine *This);

    HRESULT (STDMETHODCALLTYPE *StartRecord)(
        IMFCaptureEngine *This);

    HRESULT (STDMETHODCALLTYPE *StopRecord)(
        IMFCaptureEngine *This,
        WINBOOL bFinalize,
        WINBOOL bFlushUnprocessedSamples);

    HRESULT (STDMETHODCALLTYPE *TakePhoto)(
        IMFCaptureEngine *This);

    HRESULT (STDMETHODCALLTYPE *GetSink)(
        IMFCaptureEngine *This,
        MF_CAPTURE_ENGINE_SINK_TYPE mfCaptureEngineSinkType,
        IMFCaptureSink **ppSink);

    HRESULT (STDMETHODCALLTYPE *GetSource)(
        IMFCaptureEngine *This,
        IMFCaptureSource **ppSource);

    END_INTERFACE
} IMFCaptureEngineVtbl;

interface IMFCaptureEngine {
    CONST_VTBL IMFCaptureEngineVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCaptureEngine_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCaptureEngine_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCaptureEngine_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCaptureEngine methods ***/
#define IMFCaptureEngine_Initialize(This,pEventCallback,pAttributes,pAudioSource,pVideoSource) (This)->lpVtbl->Initialize(This,pEventCallback,pAttributes,pAudioSource,pVideoSource)
#define IMFCaptureEngine_StartPreview(This) (This)->lpVtbl->StartPreview(This)
#define IMFCaptureEngine_StopPreview(This) (This)->lpVtbl->StopPreview(This)
#define IMFCaptureEngine_StartRecord(This) (This)->lpVtbl->StartRecord(This)
#define IMFCaptureEngine_StopRecord(This,bFinalize,bFlushUnprocessedSamples) (This)->lpVtbl->StopRecord(This,bFinalize,bFlushUnprocessedSamples)
#define IMFCaptureEngine_TakePhoto(This) (This)->lpVtbl->TakePhoto(This)
#define IMFCaptureEngine_GetSink(This,mfCaptureEngineSinkType,ppSink) (This)->lpVtbl->GetSink(This,mfCaptureEngineSinkType,ppSink)
#define IMFCaptureEngine_GetSource(This,ppSource) (This)->lpVtbl->GetSource(This,ppSource)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCaptureEngine_QueryInterface(IMFCaptureEngine* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCaptureEngine_AddRef(IMFCaptureEngine* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCaptureEngine_Release(IMFCaptureEngine* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCaptureEngine methods ***/
static inline HRESULT IMFCaptureEngine_Initialize(IMFCaptureEngine* This,IMFCaptureEngineOnEventCallback *pEventCallback,IMFAttributes *pAttributes,IUnknown *pAudioSource,IUnknown *pVideoSource) {
    return This->lpVtbl->Initialize(This,pEventCallback,pAttributes,pAudioSource,pVideoSource);
}
static inline HRESULT IMFCaptureEngine_StartPreview(IMFCaptureEngine* This) {
    return This->lpVtbl->StartPreview(This);
}
static inline HRESULT IMFCaptureEngine_StopPreview(IMFCaptureEngine* This) {
    return This->lpVtbl->StopPreview(This);
}
static inline HRESULT IMFCaptureEngine_StartRecord(IMFCaptureEngine* This) {
    return This->lpVtbl->StartRecord(This);
}
static inline HRESULT IMFCaptureEngine_StopRecord(IMFCaptureEngine* This,WINBOOL bFinalize,WINBOOL bFlushUnprocessedSamples) {
    return This->lpVtbl->StopRecord(This,bFinalize,bFlushUnprocessedSamples);
}
static inline HRESULT IMFCaptureEngine_TakePhoto(IMFCaptureEngine* This) {
    return This->lpVtbl->TakePhoto(This);
}
static inline HRESULT IMFCaptureEngine_GetSink(IMFCaptureEngine* This,MF_CAPTURE_ENGINE_SINK_TYPE mfCaptureEngineSinkType,IMFCaptureSink **ppSink) {
    return This->lpVtbl->GetSink(This,mfCaptureEngineSinkType,ppSink);
}
static inline HRESULT IMFCaptureEngine_GetSource(IMFCaptureEngine* This,IMFCaptureSource **ppSource) {
    return This->lpVtbl->GetSource(This,ppSource);
}
#endif
#endif

#endif


#endif  /* __IMFCaptureEngine_INTERFACE_DEFINED__ */

EXTERN_GUID(CLSID_MFCaptureEngineClassFactory, 0xefce38d3, 0x8914, 0x4674,0xa7, 0xdf, 0xae, 0x1b, 0x3d, 0x65, 0x4b, 0x8a);
/*****************************************************************************
 * IMFCaptureEngineClassFactory interface
 */
#ifndef __IMFCaptureEngineClassFactory_INTERFACE_DEFINED__
#define __IMFCaptureEngineClassFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCaptureEngineClassFactory, 0x8f02d140, 0x56fc, 0x4302, 0xa7,0x05, 0x3a,0x97,0xc7,0x8b,0xe7,0x79);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8f02d140-56fc-4302-a705-3a97c78be779")
IMFCaptureEngineClassFactory : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateInstance(
        REFCLSID clsid,
        REFIID riid,
        LPVOID *ppvObject) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCaptureEngineClassFactory, 0x8f02d140, 0x56fc, 0x4302, 0xa7,0x05, 0x3a,0x97,0xc7,0x8b,0xe7,0x79)
#endif
#else
typedef struct IMFCaptureEngineClassFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCaptureEngineClassFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCaptureEngineClassFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCaptureEngineClassFactory *This);

    /*** IMFCaptureEngineClassFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateInstance)(
        IMFCaptureEngineClassFactory *This,
        REFCLSID clsid,
        REFIID riid,
        LPVOID *ppvObject);

    END_INTERFACE
} IMFCaptureEngineClassFactoryVtbl;

interface IMFCaptureEngineClassFactory {
    CONST_VTBL IMFCaptureEngineClassFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCaptureEngineClassFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCaptureEngineClassFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCaptureEngineClassFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCaptureEngineClassFactory methods ***/
#define IMFCaptureEngineClassFactory_CreateInstance(This,clsid,riid,ppvObject) (This)->lpVtbl->CreateInstance(This,clsid,riid,ppvObject)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCaptureEngineClassFactory_QueryInterface(IMFCaptureEngineClassFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCaptureEngineClassFactory_AddRef(IMFCaptureEngineClassFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCaptureEngineClassFactory_Release(IMFCaptureEngineClassFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCaptureEngineClassFactory methods ***/
static inline HRESULT IMFCaptureEngineClassFactory_CreateInstance(IMFCaptureEngineClassFactory* This,REFCLSID clsid,REFIID riid,LPVOID *ppvObject) {
    return This->lpVtbl->CreateInstance(This,clsid,riid,ppvObject);
}
#endif
#endif

#endif


#endif  /* __IMFCaptureEngineClassFactory_INTERFACE_DEFINED__ */

EXTERN_GUID(MFSampleExtension_DeviceReferenceSystemTime, 0x6523775a, 0xba2d, 0x405f,0xb2, 0xc5, 0x01, 0xff, 0x88, 0xe2, 0xe8, 0xf6);
/*****************************************************************************
 * IMFCaptureEngineOnSampleCallback2 interface
 */
#ifndef __IMFCaptureEngineOnSampleCallback2_INTERFACE_DEFINED__
#define __IMFCaptureEngineOnSampleCallback2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCaptureEngineOnSampleCallback2, 0xe37ceed7, 0x340f, 0x4514, 0x9f,0x4d, 0x9c,0x2a,0xe0,0x26,0x10,0x0b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e37ceed7-340f-4514-9f4d-9c2ae026100b")
IMFCaptureEngineOnSampleCallback2 : public IMFCaptureEngineOnSampleCallback
{
    virtual HRESULT STDMETHODCALLTYPE OnSynchronizedEvent(
        IMFMediaEvent *pEvent) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCaptureEngineOnSampleCallback2, 0xe37ceed7, 0x340f, 0x4514, 0x9f,0x4d, 0x9c,0x2a,0xe0,0x26,0x10,0x0b)
#endif
#else
typedef struct IMFCaptureEngineOnSampleCallback2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCaptureEngineOnSampleCallback2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCaptureEngineOnSampleCallback2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCaptureEngineOnSampleCallback2 *This);

    /*** IMFCaptureEngineOnSampleCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *OnSample)(
        IMFCaptureEngineOnSampleCallback2 *This,
        IMFSample *pSample);

    /*** IMFCaptureEngineOnSampleCallback2 methods ***/
    HRESULT (STDMETHODCALLTYPE *OnSynchronizedEvent)(
        IMFCaptureEngineOnSampleCallback2 *This,
        IMFMediaEvent *pEvent);

    END_INTERFACE
} IMFCaptureEngineOnSampleCallback2Vtbl;

interface IMFCaptureEngineOnSampleCallback2 {
    CONST_VTBL IMFCaptureEngineOnSampleCallback2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCaptureEngineOnSampleCallback2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCaptureEngineOnSampleCallback2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCaptureEngineOnSampleCallback2_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCaptureEngineOnSampleCallback methods ***/
#define IMFCaptureEngineOnSampleCallback2_OnSample(This,pSample) (This)->lpVtbl->OnSample(This,pSample)
/*** IMFCaptureEngineOnSampleCallback2 methods ***/
#define IMFCaptureEngineOnSampleCallback2_OnSynchronizedEvent(This,pEvent) (This)->lpVtbl->OnSynchronizedEvent(This,pEvent)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCaptureEngineOnSampleCallback2_QueryInterface(IMFCaptureEngineOnSampleCallback2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCaptureEngineOnSampleCallback2_AddRef(IMFCaptureEngineOnSampleCallback2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCaptureEngineOnSampleCallback2_Release(IMFCaptureEngineOnSampleCallback2* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCaptureEngineOnSampleCallback methods ***/
static inline HRESULT IMFCaptureEngineOnSampleCallback2_OnSample(IMFCaptureEngineOnSampleCallback2* This,IMFSample *pSample) {
    return This->lpVtbl->OnSample(This,pSample);
}
/*** IMFCaptureEngineOnSampleCallback2 methods ***/
static inline HRESULT IMFCaptureEngineOnSampleCallback2_OnSynchronizedEvent(IMFCaptureEngineOnSampleCallback2* This,IMFMediaEvent *pEvent) {
    return This->lpVtbl->OnSynchronizedEvent(This,pEvent);
}
#endif
#endif

#endif


#endif  /* __IMFCaptureEngineOnSampleCallback2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IMFCaptureSink2 interface
 */
#ifndef __IMFCaptureSink2_INTERFACE_DEFINED__
#define __IMFCaptureSink2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMFCaptureSink2, 0xf9e4219e, 0x6197, 0x4b5e, 0xb8,0x88, 0xbe,0xe3,0x10,0xab,0x2c,0x59);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f9e4219e-6197-4b5e-b888-bee310ab2c59")
IMFCaptureSink2 : public IMFCaptureSink
{
    virtual HRESULT STDMETHODCALLTYPE SetOutputMediaType(
        DWORD dwStreamIndex,
        IMFMediaType *pMediaType,
        IMFAttributes *pEncodingAttributes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMFCaptureSink2, 0xf9e4219e, 0x6197, 0x4b5e, 0xb8,0x88, 0xbe,0xe3,0x10,0xab,0x2c,0x59)
#endif
#else
typedef struct IMFCaptureSink2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMFCaptureSink2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMFCaptureSink2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMFCaptureSink2 *This);

    /*** IMFCaptureSink methods ***/
    HRESULT (STDMETHODCALLTYPE *GetOutputMediaType)(
        IMFCaptureSink2 *This,
        DWORD dwSinkStreamIndex,
        IMFMediaType **ppMediaType);

    HRESULT (STDMETHODCALLTYPE *GetService)(
        IMFCaptureSink2 *This,
        DWORD dwSinkStreamIndex,
        REFGUID rguidService,
        REFIID riid,
        IUnknown **ppUnknown);

    HRESULT (STDMETHODCALLTYPE *AddStream)(
        IMFCaptureSink2 *This,
        DWORD dwSourceStreamIndex,
        IMFMediaType *pMediaType,
        IMFAttributes *pAttributes,
        DWORD *pdwSinkStreamIndex);

    HRESULT (STDMETHODCALLTYPE *Prepare)(
        IMFCaptureSink2 *This);

    HRESULT (STDMETHODCALLTYPE *RemoveAllStreams)(
        IMFCaptureSink2 *This);

    /*** IMFCaptureSink2 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetOutputMediaType)(
        IMFCaptureSink2 *This,
        DWORD dwStreamIndex,
        IMFMediaType *pMediaType,
        IMFAttributes *pEncodingAttributes);

    END_INTERFACE
} IMFCaptureSink2Vtbl;

interface IMFCaptureSink2 {
    CONST_VTBL IMFCaptureSink2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMFCaptureSink2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMFCaptureSink2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMFCaptureSink2_Release(This) (This)->lpVtbl->Release(This)
/*** IMFCaptureSink methods ***/
#define IMFCaptureSink2_GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType) (This)->lpVtbl->GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType)
#define IMFCaptureSink2_GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown) (This)->lpVtbl->GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown)
#define IMFCaptureSink2_AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex) (This)->lpVtbl->AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex)
#define IMFCaptureSink2_Prepare(This) (This)->lpVtbl->Prepare(This)
#define IMFCaptureSink2_RemoveAllStreams(This) (This)->lpVtbl->RemoveAllStreams(This)
/*** IMFCaptureSink2 methods ***/
#define IMFCaptureSink2_SetOutputMediaType(This,dwStreamIndex,pMediaType,pEncodingAttributes) (This)->lpVtbl->SetOutputMediaType(This,dwStreamIndex,pMediaType,pEncodingAttributes)
#else
/*** IUnknown methods ***/
static inline HRESULT IMFCaptureSink2_QueryInterface(IMFCaptureSink2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMFCaptureSink2_AddRef(IMFCaptureSink2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMFCaptureSink2_Release(IMFCaptureSink2* This) {
    return This->lpVtbl->Release(This);
}
/*** IMFCaptureSink methods ***/
static inline HRESULT IMFCaptureSink2_GetOutputMediaType(IMFCaptureSink2* This,DWORD dwSinkStreamIndex,IMFMediaType **ppMediaType) {
    return This->lpVtbl->GetOutputMediaType(This,dwSinkStreamIndex,ppMediaType);
}
static inline HRESULT IMFCaptureSink2_GetService(IMFCaptureSink2* This,DWORD dwSinkStreamIndex,REFGUID rguidService,REFIID riid,IUnknown **ppUnknown) {
    return This->lpVtbl->GetService(This,dwSinkStreamIndex,rguidService,riid,ppUnknown);
}
static inline HRESULT IMFCaptureSink2_AddStream(IMFCaptureSink2* This,DWORD dwSourceStreamIndex,IMFMediaType *pMediaType,IMFAttributes *pAttributes,DWORD *pdwSinkStreamIndex) {
    return This->lpVtbl->AddStream(This,dwSourceStreamIndex,pMediaType,pAttributes,pdwSinkStreamIndex);
}
static inline HRESULT IMFCaptureSink2_Prepare(IMFCaptureSink2* This) {
    return This->lpVtbl->Prepare(This);
}
static inline HRESULT IMFCaptureSink2_RemoveAllStreams(IMFCaptureSink2* This) {
    return This->lpVtbl->RemoveAllStreams(This);
}
/*** IMFCaptureSink2 methods ***/
static inline HRESULT IMFCaptureSink2_SetOutputMediaType(IMFCaptureSink2* This,DWORD dwStreamIndex,IMFMediaType *pMediaType,IMFAttributes *pEncodingAttributes) {
    return This->lpVtbl->SetOutputMediaType(This,dwStreamIndex,pMediaType,pEncodingAttributes);
}
#endif
#endif

#endif


#endif  /* __IMFCaptureSink2_INTERFACE_DEFINED__ */

#endif /* WINAPI_PARTITION_DESKTOP */
#endif /* WINVER >= _WIN32_WINNT_WIN7 */
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __mfcaptureengine_h__ */
