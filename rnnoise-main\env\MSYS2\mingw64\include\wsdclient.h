/*** Autogenerated by WIDL 10.12 from include/wsdclient.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wsdclient_h__
#define __wsdclient_h__

/* Forward declarations */

#ifndef __IWSDAsyncResult_FWD_DEFINED__
#define __IWSDAsyncResult_FWD_DEFINED__
typedef interface IWSDAsyncResult IWSDAsyncResult;
#ifdef __cplusplus
interface IWSDAsyncResult;
#endif /* __cplusplus */
#endif

#ifndef __IWSDAsyncCallback_FWD_DEFINED__
#define __IWSDAsyncCallback_FWD_DEFINED__
typedef interface IWSDAsyncCallback IWSDAsyncCallback;
#ifdef __cplusplus
interface IWSDAsyncCallback;
#endif /* __cplusplus */
#endif

#ifndef __IWSDMetadataExchange_FWD_DEFINED__
#define __IWSDMetadataExchange_FWD_DEFINED__
typedef interface IWSDMetadataExchange IWSDMetadataExchange;
#ifdef __cplusplus
interface IWSDMetadataExchange;
#endif /* __cplusplus */
#endif

#ifndef __IWSDEventingStatus_FWD_DEFINED__
#define __IWSDEventingStatus_FWD_DEFINED__
typedef interface IWSDEventingStatus IWSDEventingStatus;
#ifdef __cplusplus
interface IWSDEventingStatus;
#endif /* __cplusplus */
#endif

#ifndef __IWSDEndpointProxy_FWD_DEFINED__
#define __IWSDEndpointProxy_FWD_DEFINED__
typedef interface IWSDEndpointProxy IWSDEndpointProxy;
#ifdef __cplusplus
interface IWSDEndpointProxy;
#endif /* __cplusplus */
#endif

#ifndef __IWSDDeviceProxy_FWD_DEFINED__
#define __IWSDDeviceProxy_FWD_DEFINED__
typedef interface IWSDDeviceProxy IWSDDeviceProxy;
#ifdef __cplusplus
interface IWSDDeviceProxy;
#endif /* __cplusplus */
#endif

#ifndef __IWSDServiceProxy_FWD_DEFINED__
#define __IWSDServiceProxy_FWD_DEFINED__
typedef interface IWSDServiceProxy IWSDServiceProxy;
#ifdef __cplusplus
interface IWSDServiceProxy;
#endif /* __cplusplus */
#endif

#ifndef __IWSDServiceProxyEventing_FWD_DEFINED__
#define __IWSDServiceProxyEventing_FWD_DEFINED__
typedef interface IWSDServiceProxyEventing IWSDServiceProxyEventing;
#ifdef __cplusplus
interface IWSDServiceProxyEventing;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <wsdxmldom.h>
#include <wsdtypes.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>

#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)

#ifndef __IWSDAddress_FWD_DEFINED__
#define __IWSDAddress_FWD_DEFINED__
typedef interface IWSDAddress IWSDAddress;
#ifdef __cplusplus
interface IWSDAddress;
#endif /* __cplusplus */
#endif

#ifndef __IWSDXMLContext_FWD_DEFINED__
#define __IWSDXMLContext_FWD_DEFINED__
typedef interface IWSDXMLContext IWSDXMLContext;
#ifdef __cplusplus
interface IWSDXMLContext;
#endif /* __cplusplus */
#endif

#ifndef __IWSDiscoveredService_FWD_DEFINED__
#define __IWSDiscoveredService_FWD_DEFINED__
typedef interface IWSDiscoveredService IWSDiscoveredService;
#ifdef __cplusplus
interface IWSDiscoveredService;
#endif /* __cplusplus */
#endif

#ifndef __IWSDDeviceProxy_FWD_DEFINED__
#define __IWSDDeviceProxy_FWD_DEFINED__
typedef interface IWSDDeviceProxy IWSDDeviceProxy;
#ifdef __cplusplus
interface IWSDDeviceProxy;
#endif /* __cplusplus */
#endif

#ifndef __IWSDServiceProxy_FWD_DEFINED__
#define __IWSDServiceProxy_FWD_DEFINED__
typedef interface IWSDServiceProxy IWSDServiceProxy;
#ifdef __cplusplus
interface IWSDServiceProxy;
#endif /* __cplusplus */
#endif

#ifndef __IWSDEndpointProxy_FWD_DEFINED__
#define __IWSDEndpointProxy_FWD_DEFINED__
typedef interface IWSDEndpointProxy IWSDEndpointProxy;
#ifdef __cplusplus
interface IWSDEndpointProxy;
#endif /* __cplusplus */
#endif

#ifndef __IWSDAsyncResult_FWD_DEFINED__
#define __IWSDAsyncResult_FWD_DEFINED__
typedef interface IWSDAsyncResult IWSDAsyncResult;
#ifdef __cplusplus
interface IWSDAsyncResult;
#endif /* __cplusplus */
#endif

#ifndef __IWSDAsyncCallback_FWD_DEFINED__
#define __IWSDAsyncCallback_FWD_DEFINED__
typedef interface IWSDAsyncCallback IWSDAsyncCallback;
#ifdef __cplusplus
interface IWSDAsyncCallback;
#endif /* __cplusplus */
#endif

#ifndef __IWSDMetadataExchange_FWD_DEFINED__
#define __IWSDMetadataExchange_FWD_DEFINED__
typedef interface IWSDMetadataExchange IWSDMetadataExchange;
#ifdef __cplusplus
interface IWSDMetadataExchange;
#endif /* __cplusplus */
#endif

#ifndef __IWSDEventingStatus_FWD_DEFINED__
#define __IWSDEventingStatus_FWD_DEFINED__
typedef interface IWSDEventingStatus IWSDEventingStatus;
#ifdef __cplusplus
interface IWSDEventingStatus;
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IWSDAsyncResult interface
 */
#ifndef __IWSDAsyncResult_INTERFACE_DEFINED__
#define __IWSDAsyncResult_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDAsyncResult, 0x11a9852a, 0x8dd8, 0x423e, 0xb5,0x37, 0x93,0x56,0xdb,0x4f,0xbf,0xb8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("11a9852a-8dd8-423e-b537-9356db4fbfb8")
IWSDAsyncResult : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetCallback(
        IWSDAsyncCallback *pCallback,
        IUnknown *pAsyncState) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetWaitHandle(
        HANDLE hWaitHandle) = 0;

    virtual HRESULT STDMETHODCALLTYPE HasCompleted(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAsyncState(
        IUnknown **ppAsyncState) = 0;

    virtual HRESULT STDMETHODCALLTYPE Abort(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEvent(
        WSD_EVENT *pEvent) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEndpointProxy(
        IWSDEndpointProxy **ppEndpoint) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDAsyncResult, 0x11a9852a, 0x8dd8, 0x423e, 0xb5,0x37, 0x93,0x56,0xdb,0x4f,0xbf,0xb8)
#endif
#else
typedef struct IWSDAsyncResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDAsyncResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDAsyncResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDAsyncResult *This);

    /*** IWSDAsyncResult methods ***/
    HRESULT (STDMETHODCALLTYPE *SetCallback)(
        IWSDAsyncResult *This,
        IWSDAsyncCallback *pCallback,
        IUnknown *pAsyncState);

    HRESULT (STDMETHODCALLTYPE *SetWaitHandle)(
        IWSDAsyncResult *This,
        HANDLE hWaitHandle);

    HRESULT (STDMETHODCALLTYPE *HasCompleted)(
        IWSDAsyncResult *This);

    HRESULT (STDMETHODCALLTYPE *GetAsyncState)(
        IWSDAsyncResult *This,
        IUnknown **ppAsyncState);

    HRESULT (STDMETHODCALLTYPE *Abort)(
        IWSDAsyncResult *This);

    HRESULT (STDMETHODCALLTYPE *GetEvent)(
        IWSDAsyncResult *This,
        WSD_EVENT *pEvent);

    HRESULT (STDMETHODCALLTYPE *GetEndpointProxy)(
        IWSDAsyncResult *This,
        IWSDEndpointProxy **ppEndpoint);

    END_INTERFACE
} IWSDAsyncResultVtbl;

interface IWSDAsyncResult {
    CONST_VTBL IWSDAsyncResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDAsyncResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDAsyncResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDAsyncResult_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDAsyncResult methods ***/
#define IWSDAsyncResult_SetCallback(This,pCallback,pAsyncState) (This)->lpVtbl->SetCallback(This,pCallback,pAsyncState)
#define IWSDAsyncResult_SetWaitHandle(This,hWaitHandle) (This)->lpVtbl->SetWaitHandle(This,hWaitHandle)
#define IWSDAsyncResult_HasCompleted(This) (This)->lpVtbl->HasCompleted(This)
#define IWSDAsyncResult_GetAsyncState(This,ppAsyncState) (This)->lpVtbl->GetAsyncState(This,ppAsyncState)
#define IWSDAsyncResult_Abort(This) (This)->lpVtbl->Abort(This)
#define IWSDAsyncResult_GetEvent(This,pEvent) (This)->lpVtbl->GetEvent(This,pEvent)
#define IWSDAsyncResult_GetEndpointProxy(This,ppEndpoint) (This)->lpVtbl->GetEndpointProxy(This,ppEndpoint)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDAsyncResult_QueryInterface(IWSDAsyncResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDAsyncResult_AddRef(IWSDAsyncResult* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDAsyncResult_Release(IWSDAsyncResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDAsyncResult methods ***/
static inline HRESULT IWSDAsyncResult_SetCallback(IWSDAsyncResult* This,IWSDAsyncCallback *pCallback,IUnknown *pAsyncState) {
    return This->lpVtbl->SetCallback(This,pCallback,pAsyncState);
}
static inline HRESULT IWSDAsyncResult_SetWaitHandle(IWSDAsyncResult* This,HANDLE hWaitHandle) {
    return This->lpVtbl->SetWaitHandle(This,hWaitHandle);
}
static inline HRESULT IWSDAsyncResult_HasCompleted(IWSDAsyncResult* This) {
    return This->lpVtbl->HasCompleted(This);
}
static inline HRESULT IWSDAsyncResult_GetAsyncState(IWSDAsyncResult* This,IUnknown **ppAsyncState) {
    return This->lpVtbl->GetAsyncState(This,ppAsyncState);
}
static inline HRESULT IWSDAsyncResult_Abort(IWSDAsyncResult* This) {
    return This->lpVtbl->Abort(This);
}
static inline HRESULT IWSDAsyncResult_GetEvent(IWSDAsyncResult* This,WSD_EVENT *pEvent) {
    return This->lpVtbl->GetEvent(This,pEvent);
}
static inline HRESULT IWSDAsyncResult_GetEndpointProxy(IWSDAsyncResult* This,IWSDEndpointProxy **ppEndpoint) {
    return This->lpVtbl->GetEndpointProxy(This,ppEndpoint);
}
#endif
#endif

#endif


#endif  /* __IWSDAsyncResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDAsyncCallback interface
 */
#ifndef __IWSDAsyncCallback_INTERFACE_DEFINED__
#define __IWSDAsyncCallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDAsyncCallback, 0xa63e109d, 0xce72, 0x49e2, 0xba,0x98, 0xe8,0x45,0xf5,0xee,0x16,0x66);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("a63e109d-ce72-49e2-ba98-e845f5ee1666")
IWSDAsyncCallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AsyncOperationComplete(
        IWSDAsyncResult *pAsyncResult,
        IUnknown *pAsyncState) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDAsyncCallback, 0xa63e109d, 0xce72, 0x49e2, 0xba,0x98, 0xe8,0x45,0xf5,0xee,0x16,0x66)
#endif
#else
typedef struct IWSDAsyncCallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDAsyncCallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDAsyncCallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDAsyncCallback *This);

    /*** IWSDAsyncCallback methods ***/
    HRESULT (STDMETHODCALLTYPE *AsyncOperationComplete)(
        IWSDAsyncCallback *This,
        IWSDAsyncResult *pAsyncResult,
        IUnknown *pAsyncState);

    END_INTERFACE
} IWSDAsyncCallbackVtbl;

interface IWSDAsyncCallback {
    CONST_VTBL IWSDAsyncCallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDAsyncCallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDAsyncCallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDAsyncCallback_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDAsyncCallback methods ***/
#define IWSDAsyncCallback_AsyncOperationComplete(This,pAsyncResult,pAsyncState) (This)->lpVtbl->AsyncOperationComplete(This,pAsyncResult,pAsyncState)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDAsyncCallback_QueryInterface(IWSDAsyncCallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDAsyncCallback_AddRef(IWSDAsyncCallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDAsyncCallback_Release(IWSDAsyncCallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDAsyncCallback methods ***/
static inline HRESULT IWSDAsyncCallback_AsyncOperationComplete(IWSDAsyncCallback* This,IWSDAsyncResult *pAsyncResult,IUnknown *pAsyncState) {
    return This->lpVtbl->AsyncOperationComplete(This,pAsyncResult,pAsyncState);
}
#endif
#endif

#endif


#endif  /* __IWSDAsyncCallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDMetadataExchange interface
 */
#ifndef __IWSDMetadataExchange_INTERFACE_DEFINED__
#define __IWSDMetadataExchange_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDMetadataExchange, 0x06996d57, 0x1d67, 0x4928, 0x93,0x07, 0x3d,0x78,0x33,0xfd,0xb8,0x46);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("06996d57-1d67-4928-9307-3d7833fdb846")
IWSDMetadataExchange : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMetadata(
        WSD_METADATA_SECTION_LIST **MetadataOut) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDMetadataExchange, 0x06996d57, 0x1d67, 0x4928, 0x93,0x07, 0x3d,0x78,0x33,0xfd,0xb8,0x46)
#endif
#else
typedef struct IWSDMetadataExchangeVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDMetadataExchange *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDMetadataExchange *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDMetadataExchange *This);

    /*** IWSDMetadataExchange methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMetadata)(
        IWSDMetadataExchange *This,
        WSD_METADATA_SECTION_LIST **MetadataOut);

    END_INTERFACE
} IWSDMetadataExchangeVtbl;

interface IWSDMetadataExchange {
    CONST_VTBL IWSDMetadataExchangeVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDMetadataExchange_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDMetadataExchange_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDMetadataExchange_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDMetadataExchange methods ***/
#define IWSDMetadataExchange_GetMetadata(This,MetadataOut) (This)->lpVtbl->GetMetadata(This,MetadataOut)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDMetadataExchange_QueryInterface(IWSDMetadataExchange* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDMetadataExchange_AddRef(IWSDMetadataExchange* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDMetadataExchange_Release(IWSDMetadataExchange* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDMetadataExchange methods ***/
static inline HRESULT IWSDMetadataExchange_GetMetadata(IWSDMetadataExchange* This,WSD_METADATA_SECTION_LIST **MetadataOut) {
    return This->lpVtbl->GetMetadata(This,MetadataOut);
}
#endif
#endif

#endif


#endif  /* __IWSDMetadataExchange_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDEventingStatus interface
 */
#ifndef __IWSDEventingStatus_INTERFACE_DEFINED__
#define __IWSDEventingStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDEventingStatus, 0x49b17f52, 0x637a, 0x407a, 0xae,0x99, 0xfb,0xe8,0x2a,0x4d,0x38,0xc0);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("49b17f52-637a-407a-ae99-fbe82a4d38c0")
IWSDEventingStatus : public IUnknown
{
    virtual void STDMETHODCALLTYPE SubscriptionRenewed(
        LPCWSTR pszSubscriptionAction) = 0;

    virtual void STDMETHODCALLTYPE SubscriptionRenewalFailed(
        LPCWSTR pszSubscriptionAction,
        HRESULT hr) = 0;

    virtual void STDMETHODCALLTYPE SubscriptionEnded(
        LPCWSTR pszSubscriptionAction) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDEventingStatus, 0x49b17f52, 0x637a, 0x407a, 0xae,0x99, 0xfb,0xe8,0x2a,0x4d,0x38,0xc0)
#endif
#else
typedef struct IWSDEventingStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDEventingStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDEventingStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDEventingStatus *This);

    /*** IWSDEventingStatus methods ***/
    void (STDMETHODCALLTYPE *SubscriptionRenewed)(
        IWSDEventingStatus *This,
        LPCWSTR pszSubscriptionAction);

    void (STDMETHODCALLTYPE *SubscriptionRenewalFailed)(
        IWSDEventingStatus *This,
        LPCWSTR pszSubscriptionAction,
        HRESULT hr);

    void (STDMETHODCALLTYPE *SubscriptionEnded)(
        IWSDEventingStatus *This,
        LPCWSTR pszSubscriptionAction);

    END_INTERFACE
} IWSDEventingStatusVtbl;

interface IWSDEventingStatus {
    CONST_VTBL IWSDEventingStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDEventingStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDEventingStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDEventingStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDEventingStatus methods ***/
#define IWSDEventingStatus_SubscriptionRenewed(This,pszSubscriptionAction) (This)->lpVtbl->SubscriptionRenewed(This,pszSubscriptionAction)
#define IWSDEventingStatus_SubscriptionRenewalFailed(This,pszSubscriptionAction,hr) (This)->lpVtbl->SubscriptionRenewalFailed(This,pszSubscriptionAction,hr)
#define IWSDEventingStatus_SubscriptionEnded(This,pszSubscriptionAction) (This)->lpVtbl->SubscriptionEnded(This,pszSubscriptionAction)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDEventingStatus_QueryInterface(IWSDEventingStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDEventingStatus_AddRef(IWSDEventingStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDEventingStatus_Release(IWSDEventingStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDEventingStatus methods ***/
static inline void IWSDEventingStatus_SubscriptionRenewed(IWSDEventingStatus* This,LPCWSTR pszSubscriptionAction) {
    This->lpVtbl->SubscriptionRenewed(This,pszSubscriptionAction);
}
static inline void IWSDEventingStatus_SubscriptionRenewalFailed(IWSDEventingStatus* This,LPCWSTR pszSubscriptionAction,HRESULT hr) {
    This->lpVtbl->SubscriptionRenewalFailed(This,pszSubscriptionAction,hr);
}
static inline void IWSDEventingStatus_SubscriptionEnded(IWSDEventingStatus* This,LPCWSTR pszSubscriptionAction) {
    This->lpVtbl->SubscriptionEnded(This,pszSubscriptionAction);
}
#endif
#endif

#endif


#endif  /* __IWSDEventingStatus_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDEndpointProxy interface
 */
#ifndef __IWSDEndpointProxy_INTERFACE_DEFINED__
#define __IWSDEndpointProxy_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDEndpointProxy, 0x1860d430, 0xb24c, 0x4975, 0x9f,0x90, 0xdb,0xb3,0x9b,0xaa,0x24,0xec);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1860d430-b24c-4975-9f90-dbb39baa24ec")
IWSDEndpointProxy : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SendOneWayRequest(
        const void *pBody,
        const WSD_OPERATION *pOperation) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendTwoWayRequest(
        const void *pBody,
        const WSD_OPERATION *pOperation,
        const WSD_SYNCHRONOUS_RESPONSE_CONTEXT *pResponseContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE SendTwoWayRequestAsync(
        const void *pBody,
        const WSD_OPERATION *pOperation,
        IUnknown *pAsyncState,
        IWSDAsyncCallback *pCallback,
        IWSDAsyncResult **pResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE AbortAsyncOperation(
        IWSDAsyncResult *pAsyncResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE ProcessFault(
        const WSD_SOAP_FAULT *pFault) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetErrorInfo(
        LPCWSTR *ppszErrorInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFaultInfo(
        WSD_SOAP_FAULT **ppFault) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDEndpointProxy, 0x1860d430, 0xb24c, 0x4975, 0x9f,0x90, 0xdb,0xb3,0x9b,0xaa,0x24,0xec)
#endif
#else
typedef struct IWSDEndpointProxyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDEndpointProxy *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDEndpointProxy *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDEndpointProxy *This);

    /*** IWSDEndpointProxy methods ***/
    HRESULT (STDMETHODCALLTYPE *SendOneWayRequest)(
        IWSDEndpointProxy *This,
        const void *pBody,
        const WSD_OPERATION *pOperation);

    HRESULT (STDMETHODCALLTYPE *SendTwoWayRequest)(
        IWSDEndpointProxy *This,
        const void *pBody,
        const WSD_OPERATION *pOperation,
        const WSD_SYNCHRONOUS_RESPONSE_CONTEXT *pResponseContext);

    HRESULT (STDMETHODCALLTYPE *SendTwoWayRequestAsync)(
        IWSDEndpointProxy *This,
        const void *pBody,
        const WSD_OPERATION *pOperation,
        IUnknown *pAsyncState,
        IWSDAsyncCallback *pCallback,
        IWSDAsyncResult **pResult);

    HRESULT (STDMETHODCALLTYPE *AbortAsyncOperation)(
        IWSDEndpointProxy *This,
        IWSDAsyncResult *pAsyncResult);

    HRESULT (STDMETHODCALLTYPE *ProcessFault)(
        IWSDEndpointProxy *This,
        const WSD_SOAP_FAULT *pFault);

    HRESULT (STDMETHODCALLTYPE *GetErrorInfo)(
        IWSDEndpointProxy *This,
        LPCWSTR *ppszErrorInfo);

    HRESULT (STDMETHODCALLTYPE *GetFaultInfo)(
        IWSDEndpointProxy *This,
        WSD_SOAP_FAULT **ppFault);

    END_INTERFACE
} IWSDEndpointProxyVtbl;

interface IWSDEndpointProxy {
    CONST_VTBL IWSDEndpointProxyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDEndpointProxy_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDEndpointProxy_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDEndpointProxy_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDEndpointProxy methods ***/
#define IWSDEndpointProxy_SendOneWayRequest(This,pBody,pOperation) (This)->lpVtbl->SendOneWayRequest(This,pBody,pOperation)
#define IWSDEndpointProxy_SendTwoWayRequest(This,pBody,pOperation,pResponseContext) (This)->lpVtbl->SendTwoWayRequest(This,pBody,pOperation,pResponseContext)
#define IWSDEndpointProxy_SendTwoWayRequestAsync(This,pBody,pOperation,pAsyncState,pCallback,pResult) (This)->lpVtbl->SendTwoWayRequestAsync(This,pBody,pOperation,pAsyncState,pCallback,pResult)
#define IWSDEndpointProxy_AbortAsyncOperation(This,pAsyncResult) (This)->lpVtbl->AbortAsyncOperation(This,pAsyncResult)
#define IWSDEndpointProxy_ProcessFault(This,pFault) (This)->lpVtbl->ProcessFault(This,pFault)
#define IWSDEndpointProxy_GetErrorInfo(This,ppszErrorInfo) (This)->lpVtbl->GetErrorInfo(This,ppszErrorInfo)
#define IWSDEndpointProxy_GetFaultInfo(This,ppFault) (This)->lpVtbl->GetFaultInfo(This,ppFault)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDEndpointProxy_QueryInterface(IWSDEndpointProxy* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDEndpointProxy_AddRef(IWSDEndpointProxy* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDEndpointProxy_Release(IWSDEndpointProxy* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDEndpointProxy methods ***/
static inline HRESULT IWSDEndpointProxy_SendOneWayRequest(IWSDEndpointProxy* This,const void *pBody,const WSD_OPERATION *pOperation) {
    return This->lpVtbl->SendOneWayRequest(This,pBody,pOperation);
}
static inline HRESULT IWSDEndpointProxy_SendTwoWayRequest(IWSDEndpointProxy* This,const void *pBody,const WSD_OPERATION *pOperation,const WSD_SYNCHRONOUS_RESPONSE_CONTEXT *pResponseContext) {
    return This->lpVtbl->SendTwoWayRequest(This,pBody,pOperation,pResponseContext);
}
static inline HRESULT IWSDEndpointProxy_SendTwoWayRequestAsync(IWSDEndpointProxy* This,const void *pBody,const WSD_OPERATION *pOperation,IUnknown *pAsyncState,IWSDAsyncCallback *pCallback,IWSDAsyncResult **pResult) {
    return This->lpVtbl->SendTwoWayRequestAsync(This,pBody,pOperation,pAsyncState,pCallback,pResult);
}
static inline HRESULT IWSDEndpointProxy_AbortAsyncOperation(IWSDEndpointProxy* This,IWSDAsyncResult *pAsyncResult) {
    return This->lpVtbl->AbortAsyncOperation(This,pAsyncResult);
}
static inline HRESULT IWSDEndpointProxy_ProcessFault(IWSDEndpointProxy* This,const WSD_SOAP_FAULT *pFault) {
    return This->lpVtbl->ProcessFault(This,pFault);
}
static inline HRESULT IWSDEndpointProxy_GetErrorInfo(IWSDEndpointProxy* This,LPCWSTR *ppszErrorInfo) {
    return This->lpVtbl->GetErrorInfo(This,ppszErrorInfo);
}
static inline HRESULT IWSDEndpointProxy_GetFaultInfo(IWSDEndpointProxy* This,WSD_SOAP_FAULT **ppFault) {
    return This->lpVtbl->GetFaultInfo(This,ppFault);
}
#endif
#endif

#endif


#endif  /* __IWSDEndpointProxy_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDDeviceProxy interface
 */
#ifndef __IWSDDeviceProxy_INTERFACE_DEFINED__
#define __IWSDDeviceProxy_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDDeviceProxy, 0xeee0c031, 0xc578, 0x4c0e, 0x9a,0x3b, 0x97,0x3c,0x35,0xf4,0x09,0xdb);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("eee0c031-c578-4c0e-9a3b-973c35f409db")
IWSDDeviceProxy : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE Init(
        LPCWSTR pszDeviceId,
        IWSDAddress *pDeviceAddress,
        LPCWSTR pszLocalId,
        IWSDXMLContext *pContext,
        IWSDDeviceProxy *pSponsor) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginGetMetadata(
        IWSDAsyncResult **ppResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndGetMetadata(
        IWSDAsyncResult *pResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetHostMetadata(
        WSD_HOST_METADATA **ppHostMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetThisModelMetadata(
        WSD_THIS_MODEL_METADATA **ppManufacturerMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetThisDeviceMetadata(
        WSD_THIS_DEVICE_METADATA **ppThisDeviceMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAllMetadata(
        WSD_METADATA_SECTION_LIST **ppMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetServiceProxyById(
        LPCWSTR pszServiceId,
        IWSDServiceProxy **ppServiceProxy) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetServiceProxyByType(
        const WSDXML_NAME *pType,
        IWSDServiceProxy **ppServiceProxy) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEndpointProxy(
        IWSDEndpointProxy **ppProxy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDDeviceProxy, 0xeee0c031, 0xc578, 0x4c0e, 0x9a,0x3b, 0x97,0x3c,0x35,0xf4,0x09,0xdb)
#endif
#else
typedef struct IWSDDeviceProxyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDDeviceProxy *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDDeviceProxy *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDDeviceProxy *This);

    /*** IWSDDeviceProxy methods ***/
    HRESULT (STDMETHODCALLTYPE *Init)(
        IWSDDeviceProxy *This,
        LPCWSTR pszDeviceId,
        IWSDAddress *pDeviceAddress,
        LPCWSTR pszLocalId,
        IWSDXMLContext *pContext,
        IWSDDeviceProxy *pSponsor);

    HRESULT (STDMETHODCALLTYPE *BeginGetMetadata)(
        IWSDDeviceProxy *This,
        IWSDAsyncResult **ppResult);

    HRESULT (STDMETHODCALLTYPE *EndGetMetadata)(
        IWSDDeviceProxy *This,
        IWSDAsyncResult *pResult);

    HRESULT (STDMETHODCALLTYPE *GetHostMetadata)(
        IWSDDeviceProxy *This,
        WSD_HOST_METADATA **ppHostMetadata);

    HRESULT (STDMETHODCALLTYPE *GetThisModelMetadata)(
        IWSDDeviceProxy *This,
        WSD_THIS_MODEL_METADATA **ppManufacturerMetadata);

    HRESULT (STDMETHODCALLTYPE *GetThisDeviceMetadata)(
        IWSDDeviceProxy *This,
        WSD_THIS_DEVICE_METADATA **ppThisDeviceMetadata);

    HRESULT (STDMETHODCALLTYPE *GetAllMetadata)(
        IWSDDeviceProxy *This,
        WSD_METADATA_SECTION_LIST **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *GetServiceProxyById)(
        IWSDDeviceProxy *This,
        LPCWSTR pszServiceId,
        IWSDServiceProxy **ppServiceProxy);

    HRESULT (STDMETHODCALLTYPE *GetServiceProxyByType)(
        IWSDDeviceProxy *This,
        const WSDXML_NAME *pType,
        IWSDServiceProxy **ppServiceProxy);

    HRESULT (STDMETHODCALLTYPE *GetEndpointProxy)(
        IWSDDeviceProxy *This,
        IWSDEndpointProxy **ppProxy);

    END_INTERFACE
} IWSDDeviceProxyVtbl;

interface IWSDDeviceProxy {
    CONST_VTBL IWSDDeviceProxyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDDeviceProxy_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDDeviceProxy_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDDeviceProxy_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDDeviceProxy methods ***/
#define IWSDDeviceProxy_Init(This,pszDeviceId,pDeviceAddress,pszLocalId,pContext,pSponsor) (This)->lpVtbl->Init(This,pszDeviceId,pDeviceAddress,pszLocalId,pContext,pSponsor)
#define IWSDDeviceProxy_BeginGetMetadata(This,ppResult) (This)->lpVtbl->BeginGetMetadata(This,ppResult)
#define IWSDDeviceProxy_EndGetMetadata(This,pResult) (This)->lpVtbl->EndGetMetadata(This,pResult)
#define IWSDDeviceProxy_GetHostMetadata(This,ppHostMetadata) (This)->lpVtbl->GetHostMetadata(This,ppHostMetadata)
#define IWSDDeviceProxy_GetThisModelMetadata(This,ppManufacturerMetadata) (This)->lpVtbl->GetThisModelMetadata(This,ppManufacturerMetadata)
#define IWSDDeviceProxy_GetThisDeviceMetadata(This,ppThisDeviceMetadata) (This)->lpVtbl->GetThisDeviceMetadata(This,ppThisDeviceMetadata)
#define IWSDDeviceProxy_GetAllMetadata(This,ppMetadata) (This)->lpVtbl->GetAllMetadata(This,ppMetadata)
#define IWSDDeviceProxy_GetServiceProxyById(This,pszServiceId,ppServiceProxy) (This)->lpVtbl->GetServiceProxyById(This,pszServiceId,ppServiceProxy)
#define IWSDDeviceProxy_GetServiceProxyByType(This,pType,ppServiceProxy) (This)->lpVtbl->GetServiceProxyByType(This,pType,ppServiceProxy)
#define IWSDDeviceProxy_GetEndpointProxy(This,ppProxy) (This)->lpVtbl->GetEndpointProxy(This,ppProxy)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDDeviceProxy_QueryInterface(IWSDDeviceProxy* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDDeviceProxy_AddRef(IWSDDeviceProxy* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDDeviceProxy_Release(IWSDDeviceProxy* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDDeviceProxy methods ***/
static inline HRESULT IWSDDeviceProxy_Init(IWSDDeviceProxy* This,LPCWSTR pszDeviceId,IWSDAddress *pDeviceAddress,LPCWSTR pszLocalId,IWSDXMLContext *pContext,IWSDDeviceProxy *pSponsor) {
    return This->lpVtbl->Init(This,pszDeviceId,pDeviceAddress,pszLocalId,pContext,pSponsor);
}
static inline HRESULT IWSDDeviceProxy_BeginGetMetadata(IWSDDeviceProxy* This,IWSDAsyncResult **ppResult) {
    return This->lpVtbl->BeginGetMetadata(This,ppResult);
}
static inline HRESULT IWSDDeviceProxy_EndGetMetadata(IWSDDeviceProxy* This,IWSDAsyncResult *pResult) {
    return This->lpVtbl->EndGetMetadata(This,pResult);
}
static inline HRESULT IWSDDeviceProxy_GetHostMetadata(IWSDDeviceProxy* This,WSD_HOST_METADATA **ppHostMetadata) {
    return This->lpVtbl->GetHostMetadata(This,ppHostMetadata);
}
static inline HRESULT IWSDDeviceProxy_GetThisModelMetadata(IWSDDeviceProxy* This,WSD_THIS_MODEL_METADATA **ppManufacturerMetadata) {
    return This->lpVtbl->GetThisModelMetadata(This,ppManufacturerMetadata);
}
static inline HRESULT IWSDDeviceProxy_GetThisDeviceMetadata(IWSDDeviceProxy* This,WSD_THIS_DEVICE_METADATA **ppThisDeviceMetadata) {
    return This->lpVtbl->GetThisDeviceMetadata(This,ppThisDeviceMetadata);
}
static inline HRESULT IWSDDeviceProxy_GetAllMetadata(IWSDDeviceProxy* This,WSD_METADATA_SECTION_LIST **ppMetadata) {
    return This->lpVtbl->GetAllMetadata(This,ppMetadata);
}
static inline HRESULT IWSDDeviceProxy_GetServiceProxyById(IWSDDeviceProxy* This,LPCWSTR pszServiceId,IWSDServiceProxy **ppServiceProxy) {
    return This->lpVtbl->GetServiceProxyById(This,pszServiceId,ppServiceProxy);
}
static inline HRESULT IWSDDeviceProxy_GetServiceProxyByType(IWSDDeviceProxy* This,const WSDXML_NAME *pType,IWSDServiceProxy **ppServiceProxy) {
    return This->lpVtbl->GetServiceProxyByType(This,pType,ppServiceProxy);
}
static inline HRESULT IWSDDeviceProxy_GetEndpointProxy(IWSDDeviceProxy* This,IWSDEndpointProxy **ppProxy) {
    return This->lpVtbl->GetEndpointProxy(This,ppProxy);
}
#endif
#endif

#endif


#endif  /* __IWSDDeviceProxy_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWSDServiceProxy interface
 */
#ifndef __IWSDServiceProxy_INTERFACE_DEFINED__
#define __IWSDServiceProxy_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDServiceProxy, 0xd4c7fb9c, 0x03ab, 0x4175, 0x9d,0x67, 0x09,0x4f,0xaf,0xeb,0xf4,0x87);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d4c7fb9c-03ab-4175-9d67-094fafebf487")
IWSDServiceProxy : public IWSDMetadataExchange
{
    virtual HRESULT STDMETHODCALLTYPE BeginGetMetadata(
        IWSDAsyncResult **ppResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndGetMetadata(
        IWSDAsyncResult *pResult,
        WSD_METADATA_SECTION_LIST **ppMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetServiceMetadata(
        WSD_SERVICE_METADATA **ppServiceMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE SubscribeToOperation(
        const WSD_OPERATION *pOperation,
        IUnknown *pUnknown,
        const WSDXML_ELEMENT *pAny,
        WSDXML_ELEMENT **ppAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnsubscribeToOperation(
        const WSD_OPERATION *pOperation) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetEventingStatusCallback(
        IWSDEventingStatus *pStatus) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEndpointProxy(
        IWSDEndpointProxy **ppProxy) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDServiceProxy, 0xd4c7fb9c, 0x03ab, 0x4175, 0x9d,0x67, 0x09,0x4f,0xaf,0xeb,0xf4,0x87)
#endif
#else
typedef struct IWSDServiceProxyVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDServiceProxy *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDServiceProxy *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDServiceProxy *This);

    /*** IWSDMetadataExchange methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMetadata)(
        IWSDServiceProxy *This,
        WSD_METADATA_SECTION_LIST **MetadataOut);

    /*** IWSDServiceProxy methods ***/
    HRESULT (STDMETHODCALLTYPE *BeginGetMetadata)(
        IWSDServiceProxy *This,
        IWSDAsyncResult **ppResult);

    HRESULT (STDMETHODCALLTYPE *EndGetMetadata)(
        IWSDServiceProxy *This,
        IWSDAsyncResult *pResult,
        WSD_METADATA_SECTION_LIST **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *GetServiceMetadata)(
        IWSDServiceProxy *This,
        WSD_SERVICE_METADATA **ppServiceMetadata);

    HRESULT (STDMETHODCALLTYPE *SubscribeToOperation)(
        IWSDServiceProxy *This,
        const WSD_OPERATION *pOperation,
        IUnknown *pUnknown,
        const WSDXML_ELEMENT *pAny,
        WSDXML_ELEMENT **ppAny);

    HRESULT (STDMETHODCALLTYPE *UnsubscribeToOperation)(
        IWSDServiceProxy *This,
        const WSD_OPERATION *pOperation);

    HRESULT (STDMETHODCALLTYPE *SetEventingStatusCallback)(
        IWSDServiceProxy *This,
        IWSDEventingStatus *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetEndpointProxy)(
        IWSDServiceProxy *This,
        IWSDEndpointProxy **ppProxy);

    END_INTERFACE
} IWSDServiceProxyVtbl;

interface IWSDServiceProxy {
    CONST_VTBL IWSDServiceProxyVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDServiceProxy_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDServiceProxy_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDServiceProxy_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDMetadataExchange methods ***/
#define IWSDServiceProxy_GetMetadata(This,MetadataOut) (This)->lpVtbl->GetMetadata(This,MetadataOut)
/*** IWSDServiceProxy methods ***/
#define IWSDServiceProxy_BeginGetMetadata(This,ppResult) (This)->lpVtbl->BeginGetMetadata(This,ppResult)
#define IWSDServiceProxy_EndGetMetadata(This,pResult,ppMetadata) (This)->lpVtbl->EndGetMetadata(This,pResult,ppMetadata)
#define IWSDServiceProxy_GetServiceMetadata(This,ppServiceMetadata) (This)->lpVtbl->GetServiceMetadata(This,ppServiceMetadata)
#define IWSDServiceProxy_SubscribeToOperation(This,pOperation,pUnknown,pAny,ppAny) (This)->lpVtbl->SubscribeToOperation(This,pOperation,pUnknown,pAny,ppAny)
#define IWSDServiceProxy_UnsubscribeToOperation(This,pOperation) (This)->lpVtbl->UnsubscribeToOperation(This,pOperation)
#define IWSDServiceProxy_SetEventingStatusCallback(This,pStatus) (This)->lpVtbl->SetEventingStatusCallback(This,pStatus)
#define IWSDServiceProxy_GetEndpointProxy(This,ppProxy) (This)->lpVtbl->GetEndpointProxy(This,ppProxy)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDServiceProxy_QueryInterface(IWSDServiceProxy* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDServiceProxy_AddRef(IWSDServiceProxy* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDServiceProxy_Release(IWSDServiceProxy* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDMetadataExchange methods ***/
static inline HRESULT IWSDServiceProxy_GetMetadata(IWSDServiceProxy* This,WSD_METADATA_SECTION_LIST **MetadataOut) {
    return This->lpVtbl->GetMetadata(This,MetadataOut);
}
/*** IWSDServiceProxy methods ***/
static inline HRESULT IWSDServiceProxy_BeginGetMetadata(IWSDServiceProxy* This,IWSDAsyncResult **ppResult) {
    return This->lpVtbl->BeginGetMetadata(This,ppResult);
}
static inline HRESULT IWSDServiceProxy_EndGetMetadata(IWSDServiceProxy* This,IWSDAsyncResult *pResult,WSD_METADATA_SECTION_LIST **ppMetadata) {
    return This->lpVtbl->EndGetMetadata(This,pResult,ppMetadata);
}
static inline HRESULT IWSDServiceProxy_GetServiceMetadata(IWSDServiceProxy* This,WSD_SERVICE_METADATA **ppServiceMetadata) {
    return This->lpVtbl->GetServiceMetadata(This,ppServiceMetadata);
}
static inline HRESULT IWSDServiceProxy_SubscribeToOperation(IWSDServiceProxy* This,const WSD_OPERATION *pOperation,IUnknown *pUnknown,const WSDXML_ELEMENT *pAny,WSDXML_ELEMENT **ppAny) {
    return This->lpVtbl->SubscribeToOperation(This,pOperation,pUnknown,pAny,ppAny);
}
static inline HRESULT IWSDServiceProxy_UnsubscribeToOperation(IWSDServiceProxy* This,const WSD_OPERATION *pOperation) {
    return This->lpVtbl->UnsubscribeToOperation(This,pOperation);
}
static inline HRESULT IWSDServiceProxy_SetEventingStatusCallback(IWSDServiceProxy* This,IWSDEventingStatus *pStatus) {
    return This->lpVtbl->SetEventingStatusCallback(This,pStatus);
}
static inline HRESULT IWSDServiceProxy_GetEndpointProxy(IWSDServiceProxy* This,IWSDEndpointProxy **ppProxy) {
    return This->lpVtbl->GetEndpointProxy(This,ppProxy);
}
#endif
#endif

#endif


#endif  /* __IWSDServiceProxy_INTERFACE_DEFINED__ */

#if WINVER >= 0x601
/*****************************************************************************
 * IWSDServiceProxyEventing interface
 */
#ifndef __IWSDServiceProxyEventing_INTERFACE_DEFINED__
#define __IWSDServiceProxyEventing_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWSDServiceProxyEventing, 0xf9279d6d, 0x1012, 0x4a94, 0xb8,0xcc, 0xfd,0x35,0xd2,0x20,0x2b,0xfe);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f9279d6d-1012-4a94-b8cc-fd35d2202bfe")
IWSDServiceProxyEventing : public IWSDServiceProxy
{
    virtual HRESULT STDMETHODCALLTYPE SubscribeToMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IUnknown *pUnknown,
        const WSD_EVENTING_EXPIRES *pExpires,
        const WSDXML_ELEMENT *pAny,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginSubscribeToMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IUnknown *pUnknown,
        const WSD_EVENTING_EXPIRES *pExpires,
        const WSDXML_ELEMENT *pAny,
        IUnknown *pAsyncState,
        IWSDAsyncCallback *pAsyncCallback,
        IWSDAsyncResult **ppResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndSubscribeToMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IWSDAsyncResult *pResult,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE UnsubscribeToMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSDXML_ELEMENT *pAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginUnsubscribeToMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSDXML_ELEMENT *pAny,
        IUnknown *pAsyncState,
        IWSDAsyncCallback *pAsyncCallback,
        IWSDAsyncResult **ppResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndUnsubscribeToMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IWSDAsyncResult *pResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE RenewMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSD_EVENTING_EXPIRES *pExpires,
        const WSDXML_ELEMENT *pAny,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginRenewMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSD_EVENTING_EXPIRES *pExpires,
        const WSDXML_ELEMENT *pAny,
        IUnknown *pAsyncState,
        IWSDAsyncCallback *pAsyncCallback,
        IWSDAsyncResult **ppResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndRenewMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IWSDAsyncResult *pResult,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStatusForMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSDXML_ELEMENT *pAny,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny) = 0;

    virtual HRESULT STDMETHODCALLTYPE BeginGetStatusForMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSDXML_ELEMENT *pAny,
        IUnknown *pAsyncState,
        IWSDAsyncCallback *pAsyncCallback,
        IWSDAsyncResult **ppResult) = 0;

    virtual HRESULT STDMETHODCALLTYPE EndGetStatusForMultipleOperations(
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IWSDAsyncResult *pResult,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWSDServiceProxyEventing, 0xf9279d6d, 0x1012, 0x4a94, 0xb8,0xcc, 0xfd,0x35,0xd2,0x20,0x2b,0xfe)
#endif
#else
typedef struct IWSDServiceProxyEventingVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWSDServiceProxyEventing *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWSDServiceProxyEventing *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWSDServiceProxyEventing *This);

    /*** IWSDMetadataExchange methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMetadata)(
        IWSDServiceProxyEventing *This,
        WSD_METADATA_SECTION_LIST **MetadataOut);

    /*** IWSDServiceProxy methods ***/
    HRESULT (STDMETHODCALLTYPE *BeginGetMetadata)(
        IWSDServiceProxyEventing *This,
        IWSDAsyncResult **ppResult);

    HRESULT (STDMETHODCALLTYPE *EndGetMetadata)(
        IWSDServiceProxyEventing *This,
        IWSDAsyncResult *pResult,
        WSD_METADATA_SECTION_LIST **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *GetServiceMetadata)(
        IWSDServiceProxyEventing *This,
        WSD_SERVICE_METADATA **ppServiceMetadata);

    HRESULT (STDMETHODCALLTYPE *SubscribeToOperation)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperation,
        IUnknown *pUnknown,
        const WSDXML_ELEMENT *pAny,
        WSDXML_ELEMENT **ppAny);

    HRESULT (STDMETHODCALLTYPE *UnsubscribeToOperation)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperation);

    HRESULT (STDMETHODCALLTYPE *SetEventingStatusCallback)(
        IWSDServiceProxyEventing *This,
        IWSDEventingStatus *pStatus);

    HRESULT (STDMETHODCALLTYPE *GetEndpointProxy)(
        IWSDServiceProxyEventing *This,
        IWSDEndpointProxy **ppProxy);

    /*** IWSDServiceProxyEventing methods ***/
    HRESULT (STDMETHODCALLTYPE *SubscribeToMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IUnknown *pUnknown,
        const WSD_EVENTING_EXPIRES *pExpires,
        const WSDXML_ELEMENT *pAny,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny);

    HRESULT (STDMETHODCALLTYPE *BeginSubscribeToMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IUnknown *pUnknown,
        const WSD_EVENTING_EXPIRES *pExpires,
        const WSDXML_ELEMENT *pAny,
        IUnknown *pAsyncState,
        IWSDAsyncCallback *pAsyncCallback,
        IWSDAsyncResult **ppResult);

    HRESULT (STDMETHODCALLTYPE *EndSubscribeToMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IWSDAsyncResult *pResult,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny);

    HRESULT (STDMETHODCALLTYPE *UnsubscribeToMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSDXML_ELEMENT *pAny);

    HRESULT (STDMETHODCALLTYPE *BeginUnsubscribeToMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSDXML_ELEMENT *pAny,
        IUnknown *pAsyncState,
        IWSDAsyncCallback *pAsyncCallback,
        IWSDAsyncResult **ppResult);

    HRESULT (STDMETHODCALLTYPE *EndUnsubscribeToMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IWSDAsyncResult *pResult);

    HRESULT (STDMETHODCALLTYPE *RenewMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSD_EVENTING_EXPIRES *pExpires,
        const WSDXML_ELEMENT *pAny,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny);

    HRESULT (STDMETHODCALLTYPE *BeginRenewMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSD_EVENTING_EXPIRES *pExpires,
        const WSDXML_ELEMENT *pAny,
        IUnknown *pAsyncState,
        IWSDAsyncCallback *pAsyncCallback,
        IWSDAsyncResult **ppResult);

    HRESULT (STDMETHODCALLTYPE *EndRenewMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IWSDAsyncResult *pResult,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny);

    HRESULT (STDMETHODCALLTYPE *GetStatusForMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSDXML_ELEMENT *pAny,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny);

    HRESULT (STDMETHODCALLTYPE *BeginGetStatusForMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        const WSDXML_ELEMENT *pAny,
        IUnknown *pAsyncState,
        IWSDAsyncCallback *pAsyncCallback,
        IWSDAsyncResult **ppResult);

    HRESULT (STDMETHODCALLTYPE *EndGetStatusForMultipleOperations)(
        IWSDServiceProxyEventing *This,
        const WSD_OPERATION *pOperations,
        DWORD dwOperationCount,
        IWSDAsyncResult *pResult,
        WSD_EVENTING_EXPIRES **ppExpires,
        WSDXML_ELEMENT **ppAny);

    END_INTERFACE
} IWSDServiceProxyEventingVtbl;

interface IWSDServiceProxyEventing {
    CONST_VTBL IWSDServiceProxyEventingVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWSDServiceProxyEventing_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWSDServiceProxyEventing_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWSDServiceProxyEventing_Release(This) (This)->lpVtbl->Release(This)
/*** IWSDMetadataExchange methods ***/
#define IWSDServiceProxyEventing_GetMetadata(This,MetadataOut) (This)->lpVtbl->GetMetadata(This,MetadataOut)
/*** IWSDServiceProxy methods ***/
#define IWSDServiceProxyEventing_BeginGetMetadata(This,ppResult) (This)->lpVtbl->BeginGetMetadata(This,ppResult)
#define IWSDServiceProxyEventing_EndGetMetadata(This,pResult,ppMetadata) (This)->lpVtbl->EndGetMetadata(This,pResult,ppMetadata)
#define IWSDServiceProxyEventing_GetServiceMetadata(This,ppServiceMetadata) (This)->lpVtbl->GetServiceMetadata(This,ppServiceMetadata)
#define IWSDServiceProxyEventing_SubscribeToOperation(This,pOperation,pUnknown,pAny,ppAny) (This)->lpVtbl->SubscribeToOperation(This,pOperation,pUnknown,pAny,ppAny)
#define IWSDServiceProxyEventing_UnsubscribeToOperation(This,pOperation) (This)->lpVtbl->UnsubscribeToOperation(This,pOperation)
#define IWSDServiceProxyEventing_SetEventingStatusCallback(This,pStatus) (This)->lpVtbl->SetEventingStatusCallback(This,pStatus)
#define IWSDServiceProxyEventing_GetEndpointProxy(This,ppProxy) (This)->lpVtbl->GetEndpointProxy(This,ppProxy)
/*** IWSDServiceProxyEventing methods ***/
#define IWSDServiceProxyEventing_SubscribeToMultipleOperations(This,pOperations,dwOperationCount,pUnknown,pExpires,pAny,ppExpires,ppAny) (This)->lpVtbl->SubscribeToMultipleOperations(This,pOperations,dwOperationCount,pUnknown,pExpires,pAny,ppExpires,ppAny)
#define IWSDServiceProxyEventing_BeginSubscribeToMultipleOperations(This,pOperations,dwOperationCount,pUnknown,pExpires,pAny,pAsyncState,pAsyncCallback,ppResult) (This)->lpVtbl->BeginSubscribeToMultipleOperations(This,pOperations,dwOperationCount,pUnknown,pExpires,pAny,pAsyncState,pAsyncCallback,ppResult)
#define IWSDServiceProxyEventing_EndSubscribeToMultipleOperations(This,pOperations,dwOperationCount,pResult,ppExpires,ppAny) (This)->lpVtbl->EndSubscribeToMultipleOperations(This,pOperations,dwOperationCount,pResult,ppExpires,ppAny)
#define IWSDServiceProxyEventing_UnsubscribeToMultipleOperations(This,pOperations,dwOperationCount,pAny) (This)->lpVtbl->UnsubscribeToMultipleOperations(This,pOperations,dwOperationCount,pAny)
#define IWSDServiceProxyEventing_BeginUnsubscribeToMultipleOperations(This,pOperations,dwOperationCount,pAny,pAsyncState,pAsyncCallback,ppResult) (This)->lpVtbl->BeginUnsubscribeToMultipleOperations(This,pOperations,dwOperationCount,pAny,pAsyncState,pAsyncCallback,ppResult)
#define IWSDServiceProxyEventing_EndUnsubscribeToMultipleOperations(This,pOperations,dwOperationCount,pResult) (This)->lpVtbl->EndUnsubscribeToMultipleOperations(This,pOperations,dwOperationCount,pResult)
#define IWSDServiceProxyEventing_RenewMultipleOperations(This,pOperations,dwOperationCount,pExpires,pAny,ppExpires,ppAny) (This)->lpVtbl->RenewMultipleOperations(This,pOperations,dwOperationCount,pExpires,pAny,ppExpires,ppAny)
#define IWSDServiceProxyEventing_BeginRenewMultipleOperations(This,pOperations,dwOperationCount,pExpires,pAny,pAsyncState,pAsyncCallback,ppResult) (This)->lpVtbl->BeginRenewMultipleOperations(This,pOperations,dwOperationCount,pExpires,pAny,pAsyncState,pAsyncCallback,ppResult)
#define IWSDServiceProxyEventing_EndRenewMultipleOperations(This,pOperations,dwOperationCount,pResult,ppExpires,ppAny) (This)->lpVtbl->EndRenewMultipleOperations(This,pOperations,dwOperationCount,pResult,ppExpires,ppAny)
#define IWSDServiceProxyEventing_GetStatusForMultipleOperations(This,pOperations,dwOperationCount,pAny,ppExpires,ppAny) (This)->lpVtbl->GetStatusForMultipleOperations(This,pOperations,dwOperationCount,pAny,ppExpires,ppAny)
#define IWSDServiceProxyEventing_BeginGetStatusForMultipleOperations(This,pOperations,dwOperationCount,pAny,pAsyncState,pAsyncCallback,ppResult) (This)->lpVtbl->BeginGetStatusForMultipleOperations(This,pOperations,dwOperationCount,pAny,pAsyncState,pAsyncCallback,ppResult)
#define IWSDServiceProxyEventing_EndGetStatusForMultipleOperations(This,pOperations,dwOperationCount,pResult,ppExpires,ppAny) (This)->lpVtbl->EndGetStatusForMultipleOperations(This,pOperations,dwOperationCount,pResult,ppExpires,ppAny)
#else
/*** IUnknown methods ***/
static inline HRESULT IWSDServiceProxyEventing_QueryInterface(IWSDServiceProxyEventing* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWSDServiceProxyEventing_AddRef(IWSDServiceProxyEventing* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWSDServiceProxyEventing_Release(IWSDServiceProxyEventing* This) {
    return This->lpVtbl->Release(This);
}
/*** IWSDMetadataExchange methods ***/
static inline HRESULT IWSDServiceProxyEventing_GetMetadata(IWSDServiceProxyEventing* This,WSD_METADATA_SECTION_LIST **MetadataOut) {
    return This->lpVtbl->GetMetadata(This,MetadataOut);
}
/*** IWSDServiceProxy methods ***/
static inline HRESULT IWSDServiceProxyEventing_BeginGetMetadata(IWSDServiceProxyEventing* This,IWSDAsyncResult **ppResult) {
    return This->lpVtbl->BeginGetMetadata(This,ppResult);
}
static inline HRESULT IWSDServiceProxyEventing_EndGetMetadata(IWSDServiceProxyEventing* This,IWSDAsyncResult *pResult,WSD_METADATA_SECTION_LIST **ppMetadata) {
    return This->lpVtbl->EndGetMetadata(This,pResult,ppMetadata);
}
static inline HRESULT IWSDServiceProxyEventing_GetServiceMetadata(IWSDServiceProxyEventing* This,WSD_SERVICE_METADATA **ppServiceMetadata) {
    return This->lpVtbl->GetServiceMetadata(This,ppServiceMetadata);
}
static inline HRESULT IWSDServiceProxyEventing_SubscribeToOperation(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperation,IUnknown *pUnknown,const WSDXML_ELEMENT *pAny,WSDXML_ELEMENT **ppAny) {
    return This->lpVtbl->SubscribeToOperation(This,pOperation,pUnknown,pAny,ppAny);
}
static inline HRESULT IWSDServiceProxyEventing_UnsubscribeToOperation(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperation) {
    return This->lpVtbl->UnsubscribeToOperation(This,pOperation);
}
static inline HRESULT IWSDServiceProxyEventing_SetEventingStatusCallback(IWSDServiceProxyEventing* This,IWSDEventingStatus *pStatus) {
    return This->lpVtbl->SetEventingStatusCallback(This,pStatus);
}
static inline HRESULT IWSDServiceProxyEventing_GetEndpointProxy(IWSDServiceProxyEventing* This,IWSDEndpointProxy **ppProxy) {
    return This->lpVtbl->GetEndpointProxy(This,ppProxy);
}
/*** IWSDServiceProxyEventing methods ***/
static inline HRESULT IWSDServiceProxyEventing_SubscribeToMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,IUnknown *pUnknown,const WSD_EVENTING_EXPIRES *pExpires,const WSDXML_ELEMENT *pAny,WSD_EVENTING_EXPIRES **ppExpires,WSDXML_ELEMENT **ppAny) {
    return This->lpVtbl->SubscribeToMultipleOperations(This,pOperations,dwOperationCount,pUnknown,pExpires,pAny,ppExpires,ppAny);
}
static inline HRESULT IWSDServiceProxyEventing_BeginSubscribeToMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,IUnknown *pUnknown,const WSD_EVENTING_EXPIRES *pExpires,const WSDXML_ELEMENT *pAny,IUnknown *pAsyncState,IWSDAsyncCallback *pAsyncCallback,IWSDAsyncResult **ppResult) {
    return This->lpVtbl->BeginSubscribeToMultipleOperations(This,pOperations,dwOperationCount,pUnknown,pExpires,pAny,pAsyncState,pAsyncCallback,ppResult);
}
static inline HRESULT IWSDServiceProxyEventing_EndSubscribeToMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,IWSDAsyncResult *pResult,WSD_EVENTING_EXPIRES **ppExpires,WSDXML_ELEMENT **ppAny) {
    return This->lpVtbl->EndSubscribeToMultipleOperations(This,pOperations,dwOperationCount,pResult,ppExpires,ppAny);
}
static inline HRESULT IWSDServiceProxyEventing_UnsubscribeToMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,const WSDXML_ELEMENT *pAny) {
    return This->lpVtbl->UnsubscribeToMultipleOperations(This,pOperations,dwOperationCount,pAny);
}
static inline HRESULT IWSDServiceProxyEventing_BeginUnsubscribeToMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,const WSDXML_ELEMENT *pAny,IUnknown *pAsyncState,IWSDAsyncCallback *pAsyncCallback,IWSDAsyncResult **ppResult) {
    return This->lpVtbl->BeginUnsubscribeToMultipleOperations(This,pOperations,dwOperationCount,pAny,pAsyncState,pAsyncCallback,ppResult);
}
static inline HRESULT IWSDServiceProxyEventing_EndUnsubscribeToMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,IWSDAsyncResult *pResult) {
    return This->lpVtbl->EndUnsubscribeToMultipleOperations(This,pOperations,dwOperationCount,pResult);
}
static inline HRESULT IWSDServiceProxyEventing_RenewMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,const WSD_EVENTING_EXPIRES *pExpires,const WSDXML_ELEMENT *pAny,WSD_EVENTING_EXPIRES **ppExpires,WSDXML_ELEMENT **ppAny) {
    return This->lpVtbl->RenewMultipleOperations(This,pOperations,dwOperationCount,pExpires,pAny,ppExpires,ppAny);
}
static inline HRESULT IWSDServiceProxyEventing_BeginRenewMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,const WSD_EVENTING_EXPIRES *pExpires,const WSDXML_ELEMENT *pAny,IUnknown *pAsyncState,IWSDAsyncCallback *pAsyncCallback,IWSDAsyncResult **ppResult) {
    return This->lpVtbl->BeginRenewMultipleOperations(This,pOperations,dwOperationCount,pExpires,pAny,pAsyncState,pAsyncCallback,ppResult);
}
static inline HRESULT IWSDServiceProxyEventing_EndRenewMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,IWSDAsyncResult *pResult,WSD_EVENTING_EXPIRES **ppExpires,WSDXML_ELEMENT **ppAny) {
    return This->lpVtbl->EndRenewMultipleOperations(This,pOperations,dwOperationCount,pResult,ppExpires,ppAny);
}
static inline HRESULT IWSDServiceProxyEventing_GetStatusForMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,const WSDXML_ELEMENT *pAny,WSD_EVENTING_EXPIRES **ppExpires,WSDXML_ELEMENT **ppAny) {
    return This->lpVtbl->GetStatusForMultipleOperations(This,pOperations,dwOperationCount,pAny,ppExpires,ppAny);
}
static inline HRESULT IWSDServiceProxyEventing_BeginGetStatusForMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,const WSDXML_ELEMENT *pAny,IUnknown *pAsyncState,IWSDAsyncCallback *pAsyncCallback,IWSDAsyncResult **ppResult) {
    return This->lpVtbl->BeginGetStatusForMultipleOperations(This,pOperations,dwOperationCount,pAny,pAsyncState,pAsyncCallback,ppResult);
}
static inline HRESULT IWSDServiceProxyEventing_EndGetStatusForMultipleOperations(IWSDServiceProxyEventing* This,const WSD_OPERATION *pOperations,DWORD dwOperationCount,IWSDAsyncResult *pResult,WSD_EVENTING_EXPIRES **ppExpires,WSDXML_ELEMENT **ppAny) {
    return This->lpVtbl->EndGetStatusForMultipleOperations(This,pOperations,dwOperationCount,pResult,ppExpires,ppAny);
}
#endif
#endif

#endif


#endif  /* __IWSDServiceProxyEventing_INTERFACE_DEFINED__ */

#endif
HRESULT WINAPI WSDCreateDeviceProxy(LPCWSTR pszDeviceId, LPCWSTR pszLocalId, IWSDXMLContext *pContext, IWSDDeviceProxy **ppDeviceProxy);
HRESULT WINAPI WSDCreateDeviceProxyAdvanced(LPCWSTR pszDeviceId, IWSDAddress* pDeviceAddress, LPCWSTR pszLocalId, IWSDXMLContext *pContext, IWSDDeviceProxy **ppDeviceProxy);
#if WINVER >= 0x601
HRESULT WINAPI WSDCreateDeviceProxy2(LPCWSTR pszDeviceId, LPCWSTR pszLocalId, IWSDXMLContext *pContext, WSD_CONFIG_PARAM *pConfigParams, DWORD dwConfigParamCount, IWSDDeviceProxy **ppDeviceProxy);
#endif
#endif
/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wsdclient_h__ */
