/*** Autogenerated by WIDL 10.12 from include/d3d11_2.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __d3d11_2_h__
#define __d3d11_2_h__

/* Forward declarations */

#ifndef __ID3D11DeviceContext2_FWD_DEFINED__
#define __ID3D11DeviceContext2_FWD_DEFINED__
typedef interface ID3D11DeviceContext2 ID3D11DeviceContext2;
#ifdef __cplusplus
interface ID3D11DeviceContext2;
#endif /* __cplusplus */
#endif

#ifndef __ID3D11Device2_FWD_DEFINED__
#define __ID3D11Device2_FWD_DEFINED__
typedef interface ID3D11Device2 ID3D11Device2;
#ifdef __cplusplus
interface ID3D11Device2;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <dxgi1_3.h>
#include <d3dcommon.h>
#include <d3d11_1.h>

#ifdef __cplusplus
extern "C" {
#endif

#define D3D11_PACKED_TILE (0xffffffff)

typedef enum D3D11_TILE_MAPPING_FLAG {
    D3D11_TILE_MAPPING_NO_OVERWRITE = 0x1
} D3D11_TILE_MAPPING_FLAG;
typedef enum D3D11_TILE_RANGE_FLAG {
    D3D11_TILE_RANGE_NULL = 0x1,
    D3D11_TILE_RANGE_SKIP = 0x2,
    D3D11_TILE_RANGE_REUSE_SINGLE_TILE = 0x4
} D3D11_TILE_RANGE_FLAG;
typedef enum D3D11_CHECK_MULTISAMPLE_QUALITY_LEVELS_FLAG {
    D3D11_CHECK_MULTISAMPLE_QUALITY_LEVELS_TILED_RESOURCE = 0x1
} D3D11_CHECK_MULTISAMPLE_QUALITY_LEVELS_FLAG;
typedef enum D3D11_TILE_COPY_FLAG {
    D3D11_TILE_COPY_NO_OVERWRITE = 0x1,
    D3D11_TILE_COPY_LINEAR_BUFFER_TO_SWIZZLED_TILED_RESOURCE = 0x2,
    D3D11_TILE_COPY_SWIZZLED_TILED_RESOURCE_TO_LINEAR_BUFFER = 0x4
} D3D11_TILE_COPY_FLAG;
typedef struct D3D11_TILED_RESOURCE_COORDINATE {
    UINT X;
    UINT Y;
    UINT Z;
    UINT Subresource;
} D3D11_TILED_RESOURCE_COORDINATE;
typedef struct D3D11_TILE_REGION_SIZE {
    UINT NumTiles;
    WINBOOL bUseBox;
    UINT Width;
    UINT16 Height;
    UINT16 Depth;
} D3D11_TILE_REGION_SIZE;
typedef struct D3D11_SUBRESOURCE_TILING {
    UINT WidthInTiles;
    UINT16 HeightInTiles;
    UINT16 DepthInTiles;
    UINT StartTileIndexInOverallResource;
} D3D11_SUBRESOURCE_TILING;
typedef struct D3D11_TILE_SHAPE {
    UINT WidthInTexels;
    UINT HeightInTexels;
    UINT DepthInTexels;
} D3D11_TILE_SHAPE;
typedef struct D3D11_PACKED_MIP_DESC {
    UINT8 NumStandardMips;
    UINT8 NumPackedMips;
    UINT NumTilesForPackedMips;
    UINT StartTileIndexInOverallResource;
} D3D11_PACKED_MIP_DESC;
/*****************************************************************************
 * ID3D11DeviceContext2 interface
 */
#ifndef __ID3D11DeviceContext2_INTERFACE_DEFINED__
#define __ID3D11DeviceContext2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11DeviceContext2, 0x420d5b32, 0xb90c, 0x4da4, 0xbe,0xf0, 0x35,0x9f,0x6a,0x24,0xa8,0x3a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("420d5b32-b90c-4da4-bef0-359f6a24a83a")
ID3D11DeviceContext2 : public ID3D11DeviceContext1
{
    virtual HRESULT STDMETHODCALLTYPE UpdateTileMappings(
        ID3D11Resource *resource,
        UINT region_count,
        const D3D11_TILED_RESOURCE_COORDINATE *region_start_coordinates,
        const D3D11_TILE_REGION_SIZE *region_sizes,
        ID3D11Buffer *pool,
        UINT range_count,
        const UINT *range_flags,
        const UINT *pool_start_offsets,
        const UINT *range_tile_counts,
        UINT flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE CopyTileMappings(
        ID3D11Resource *dst_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,
        ID3D11Resource *src_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *src_start_coordinate,
        const D3D11_TILE_REGION_SIZE *region_size,
        UINT flags) = 0;

    virtual void STDMETHODCALLTYPE CopyTiles(
        ID3D11Resource *resource,
        const D3D11_TILED_RESOURCE_COORDINATE *start_coordinate,
        const D3D11_TILE_REGION_SIZE *size,
        ID3D11Buffer *buffer,
        UINT64 start_offset,
        UINT flags) = 0;

    virtual void STDMETHODCALLTYPE UpdateTiles(
        ID3D11Resource *dst_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,
        const D3D11_TILE_REGION_SIZE *dst_region_size,
        const void *src_data,
        UINT flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE ResizeTilePool(
        ID3D11Buffer *pool,
        UINT64 size) = 0;

    virtual void STDMETHODCALLTYPE TiledResourceBarrier(
        ID3D11DeviceChild *before_barrier,
        ID3D11DeviceChild *after_barrier) = 0;

    virtual WINBOOL STDMETHODCALLTYPE IsAnnotationEnabled(
        ) = 0;

    virtual void STDMETHODCALLTYPE SetMarkerInt(
        const WCHAR *label,
        int data) = 0;

    virtual void STDMETHODCALLTYPE BeginEventInt(
        const WCHAR *label,
        int data) = 0;

    virtual void STDMETHODCALLTYPE EndEvent(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11DeviceContext2, 0x420d5b32, 0xb90c, 0x4da4, 0xbe,0xf0, 0x35,0x9f,0x6a,0x24,0xa8,0x3a)
#endif
#else
typedef struct ID3D11DeviceContext2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11DeviceContext2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11DeviceContext2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11DeviceContext2 *This);

    /*** ID3D11DeviceChild methods ***/
    void (STDMETHODCALLTYPE *GetDevice)(
        ID3D11DeviceContext2 *This,
        ID3D11Device **ppDevice);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11DeviceContext2 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11DeviceContext2 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11DeviceContext2 *This,
        REFGUID guid,
        const IUnknown *pData);

    /*** ID3D11DeviceContext methods ***/
    void (STDMETHODCALLTYPE *VSSetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSSetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSSetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11PixelShader *pPixelShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *PSSetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *VSSetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11VertexShader *pVertexShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *DrawIndexed)(
        ID3D11DeviceContext2 *This,
        UINT IndexCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation);

    void (STDMETHODCALLTYPE *Draw)(
        ID3D11DeviceContext2 *This,
        UINT VertexCount,
        UINT StartVertexLocation);

    HRESULT (STDMETHODCALLTYPE *Map)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *pResource,
        UINT Subresource,
        D3D11_MAP MapType,
        UINT MapFlags,
        D3D11_MAPPED_SUBRESOURCE *pMappedResource);

    void (STDMETHODCALLTYPE *Unmap)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *pResource,
        UINT Subresource);

    void (STDMETHODCALLTYPE *PSSetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *IASetInputLayout)(
        ID3D11DeviceContext2 *This,
        ID3D11InputLayout *pInputLayout);

    void (STDMETHODCALLTYPE *IASetVertexBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppVertexBuffers,
        const UINT *pStrides,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *IASetIndexBuffer)(
        ID3D11DeviceContext2 *This,
        ID3D11Buffer *pIndexBuffer,
        DXGI_FORMAT Format,
        UINT Offset);

    void (STDMETHODCALLTYPE *DrawIndexedInstanced)(
        ID3D11DeviceContext2 *This,
        UINT IndexCountPerInstance,
        UINT InstanceCount,
        UINT StartIndexLocation,
        INT BaseVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *DrawInstanced)(
        ID3D11DeviceContext2 *This,
        UINT VertexCountPerInstance,
        UINT InstanceCount,
        UINT StartVertexLocation,
        UINT StartInstanceLocation);

    void (STDMETHODCALLTYPE *GSSetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSSetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11GeometryShader *pShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *IASetPrimitiveTopology)(
        ID3D11DeviceContext2 *This,
        D3D11_PRIMITIVE_TOPOLOGY Topology);

    void (STDMETHODCALLTYPE *VSSetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSSetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *Begin)(
        ID3D11DeviceContext2 *This,
        ID3D11Asynchronous *pAsync);

    void (STDMETHODCALLTYPE *End)(
        ID3D11DeviceContext2 *This,
        ID3D11Asynchronous *pAsync);

    HRESULT (STDMETHODCALLTYPE *GetData)(
        ID3D11DeviceContext2 *This,
        ID3D11Asynchronous *pAsync,
        void *pData,
        UINT DataSize,
        UINT GetDataFlags);

    void (STDMETHODCALLTYPE *SetPredication)(
        ID3D11DeviceContext2 *This,
        ID3D11Predicate *pPredicate,
        WINBOOL PredicateValue);

    void (STDMETHODCALLTYPE *GSSetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSSetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *OMSetRenderTargets)(
        ID3D11DeviceContext2 *This,
        UINT NumViews,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView);

    void (STDMETHODCALLTYPE *OMSetRenderTargetsAndUnorderedAccessViews)(
        ID3D11DeviceContext2 *This,
        UINT NumRTVs,
        ID3D11RenderTargetView *const *ppRenderTargetViews,
        ID3D11DepthStencilView *pDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts);

    void (STDMETHODCALLTYPE *OMSetBlendState)(
        ID3D11DeviceContext2 *This,
        ID3D11BlendState *pBlendState,
        const FLOAT BlendFactor[4],
        UINT SampleMask);

    void (STDMETHODCALLTYPE *OMSetDepthStencilState)(
        ID3D11DeviceContext2 *This,
        ID3D11DepthStencilState *pDepthStencilState,
        UINT StencilRef);

    void (STDMETHODCALLTYPE *SOSetTargets)(
        ID3D11DeviceContext2 *This,
        UINT NumBuffers,
        ID3D11Buffer *const *ppSOTargets,
        const UINT *pOffsets);

    void (STDMETHODCALLTYPE *DrawAuto)(
        ID3D11DeviceContext2 *This);

    void (STDMETHODCALLTYPE *DrawIndexedInstancedIndirect)(
        ID3D11DeviceContext2 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *DrawInstancedIndirect)(
        ID3D11DeviceContext2 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *Dispatch)(
        ID3D11DeviceContext2 *This,
        UINT ThreadGroupCountX,
        UINT ThreadGroupCountY,
        UINT ThreadGroupCountZ);

    void (STDMETHODCALLTYPE *DispatchIndirect)(
        ID3D11DeviceContext2 *This,
        ID3D11Buffer *pBufferForArgs,
        UINT AlignedByteOffsetForArgs);

    void (STDMETHODCALLTYPE *RSSetState)(
        ID3D11DeviceContext2 *This,
        ID3D11RasterizerState *pRasterizerState);

    void (STDMETHODCALLTYPE *RSSetViewports)(
        ID3D11DeviceContext2 *This,
        UINT NumViewports,
        const D3D11_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSSetScissorRects)(
        ID3D11DeviceContext2 *This,
        UINT NumRects,
        const D3D11_RECT *pRects);

    void (STDMETHODCALLTYPE *CopySubresourceRegion)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D11_BOX *pSrcBox);

    void (STDMETHODCALLTYPE *CopyResource)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *pDstResource,
        ID3D11Resource *pSrcResource);

    void (STDMETHODCALLTYPE *UpdateSubresource)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        const D3D11_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch);

    void (STDMETHODCALLTYPE *CopyStructureCount)(
        ID3D11DeviceContext2 *This,
        ID3D11Buffer *pDstBuffer,
        UINT DstAlignedByteOffset,
        ID3D11UnorderedAccessView *pSrcView);

    void (STDMETHODCALLTYPE *ClearRenderTargetView)(
        ID3D11DeviceContext2 *This,
        ID3D11RenderTargetView *pRenderTargetView,
        const FLOAT ColorRGBA[4]);

    void (STDMETHODCALLTYPE *ClearUnorderedAccessViewUint)(
        ID3D11DeviceContext2 *This,
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const UINT Values[4]);

    void (STDMETHODCALLTYPE *ClearUnorderedAccessViewFloat)(
        ID3D11DeviceContext2 *This,
        ID3D11UnorderedAccessView *pUnorderedAccessView,
        const FLOAT Values[4]);

    void (STDMETHODCALLTYPE *ClearDepthStencilView)(
        ID3D11DeviceContext2 *This,
        ID3D11DepthStencilView *pDepthStencilView,
        UINT ClearFlags,
        FLOAT Depth,
        UINT8 Stencil);

    void (STDMETHODCALLTYPE *GenerateMips)(
        ID3D11DeviceContext2 *This,
        ID3D11ShaderResourceView *pShaderResourceView);

    void (STDMETHODCALLTYPE *SetResourceMinLOD)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *pResource,
        FLOAT MinLOD);

    FLOAT (STDMETHODCALLTYPE *GetResourceMinLOD)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *pResource);

    void (STDMETHODCALLTYPE *ResolveSubresource)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        DXGI_FORMAT Format);

    void (STDMETHODCALLTYPE *ExecuteCommandList)(
        ID3D11DeviceContext2 *This,
        ID3D11CommandList *pCommandList,
        WINBOOL RestoreContextState);

    void (STDMETHODCALLTYPE *HSSetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *HSSetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11HullShader *pHullShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *HSSetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *HSSetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *DSSetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *DSSetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11DomainShader *pDomainShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *DSSetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *DSSetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *CSSetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView *const *ppShaderResourceViews);

    void (STDMETHODCALLTYPE *CSSetUnorderedAccessViews)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,
        const UINT *pUAVInitialCounts);

    void (STDMETHODCALLTYPE *CSSetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11ComputeShader *pComputeShader,
        ID3D11ClassInstance *const *ppClassInstances,
        UINT NumClassInstances);

    void (STDMETHODCALLTYPE *CSSetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState *const *ppSamplers);

    void (STDMETHODCALLTYPE *CSSetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers);

    void (STDMETHODCALLTYPE *VSGetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *PSGetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *PSGetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11PixelShader **ppPixelShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *PSGetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *VSGetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11VertexShader **ppVertexShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *PSGetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *IAGetInputLayout)(
        ID3D11DeviceContext2 *This,
        ID3D11InputLayout **ppInputLayout);

    void (STDMETHODCALLTYPE *IAGetVertexBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppVertexBuffers,
        UINT *pStrides,
        UINT *pOffsets);

    void (STDMETHODCALLTYPE *IAGetIndexBuffer)(
        ID3D11DeviceContext2 *This,
        ID3D11Buffer **pIndexBuffer,
        DXGI_FORMAT *Format,
        UINT *Offset);

    void (STDMETHODCALLTYPE *GSGetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *GSGetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11GeometryShader **ppGeometryShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *IAGetPrimitiveTopology)(
        ID3D11DeviceContext2 *This,
        D3D11_PRIMITIVE_TOPOLOGY *pTopology);

    void (STDMETHODCALLTYPE *VSGetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *VSGetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *GetPredication)(
        ID3D11DeviceContext2 *This,
        ID3D11Predicate **ppPredicate,
        WINBOOL *pPredicateValue);

    void (STDMETHODCALLTYPE *GSGetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *GSGetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *OMGetRenderTargets)(
        ID3D11DeviceContext2 *This,
        UINT NumViews,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView);

    void (STDMETHODCALLTYPE *OMGetRenderTargetsAndUnorderedAccessViews)(
        ID3D11DeviceContext2 *This,
        UINT NumRTVs,
        ID3D11RenderTargetView **ppRenderTargetViews,
        ID3D11DepthStencilView **ppDepthStencilView,
        UINT UAVStartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews);

    void (STDMETHODCALLTYPE *OMGetBlendState)(
        ID3D11DeviceContext2 *This,
        ID3D11BlendState **ppBlendState,
        FLOAT BlendFactor[4],
        UINT *pSampleMask);

    void (STDMETHODCALLTYPE *OMGetDepthStencilState)(
        ID3D11DeviceContext2 *This,
        ID3D11DepthStencilState **ppDepthStencilState,
        UINT *pStencilRef);

    void (STDMETHODCALLTYPE *SOGetTargets)(
        ID3D11DeviceContext2 *This,
        UINT NumBuffers,
        ID3D11Buffer **ppSOTargets);

    void (STDMETHODCALLTYPE *RSGetState)(
        ID3D11DeviceContext2 *This,
        ID3D11RasterizerState **ppRasterizerState);

    void (STDMETHODCALLTYPE *RSGetViewports)(
        ID3D11DeviceContext2 *This,
        UINT *pNumViewports,
        D3D11_VIEWPORT *pViewports);

    void (STDMETHODCALLTYPE *RSGetScissorRects)(
        ID3D11DeviceContext2 *This,
        UINT *pNumRects,
        D3D11_RECT *pRects);

    void (STDMETHODCALLTYPE *HSGetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *HSGetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11HullShader **ppHullShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *HSGetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *HSGetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *DSGetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *DSGetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11DomainShader **ppDomainShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *DSGetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *DSGetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *CSGetShaderResources)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumViews,
        ID3D11ShaderResourceView **ppShaderResourceViews);

    void (STDMETHODCALLTYPE *CSGetUnorderedAccessViews)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumUAVs,
        ID3D11UnorderedAccessView **ppUnorderedAccessViews);

    void (STDMETHODCALLTYPE *CSGetShader)(
        ID3D11DeviceContext2 *This,
        ID3D11ComputeShader **ppComputeShader,
        ID3D11ClassInstance **ppClassInstances,
        UINT *pNumClassInstances);

    void (STDMETHODCALLTYPE *CSGetSamplers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumSamplers,
        ID3D11SamplerState **ppSamplers);

    void (STDMETHODCALLTYPE *CSGetConstantBuffers)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers);

    void (STDMETHODCALLTYPE *ClearState)(
        ID3D11DeviceContext2 *This);

    void (STDMETHODCALLTYPE *Flush)(
        ID3D11DeviceContext2 *This);

    D3D11_DEVICE_CONTEXT_TYPE (STDMETHODCALLTYPE *GetType)(
        ID3D11DeviceContext2 *This);

    UINT (STDMETHODCALLTYPE *GetContextFlags)(
        ID3D11DeviceContext2 *This);

    HRESULT (STDMETHODCALLTYPE *FinishCommandList)(
        ID3D11DeviceContext2 *This,
        WINBOOL RestoreDeferredContextState,
        ID3D11CommandList **ppCommandList);

    /*** ID3D11DeviceContext1 methods ***/
    void (STDMETHODCALLTYPE *CopySubresourceRegion1)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        UINT DstX,
        UINT DstY,
        UINT DstZ,
        ID3D11Resource *pSrcResource,
        UINT SrcSubresource,
        const D3D11_BOX *pSrcBox,
        UINT CopyFlags);

    void (STDMETHODCALLTYPE *UpdateSubresource1)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *pDstResource,
        UINT DstSubresource,
        const D3D11_BOX *pDstBox,
        const void *pSrcData,
        UINT SrcRowPitch,
        UINT SrcDepthPitch,
        UINT CopyFlags);

    void (STDMETHODCALLTYPE *DiscardResource)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *pResource);

    void (STDMETHODCALLTYPE *DiscardView)(
        ID3D11DeviceContext2 *This,
        ID3D11View *pResourceView);

    void (STDMETHODCALLTYPE *VSSetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *HSSetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *DSSetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *GSSetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *PSSetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *CSSetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer *const *ppConstantBuffers,
        const UINT *pFirstConstant,
        const UINT *pNumConstants);

    void (STDMETHODCALLTYPE *VSGetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *HSGetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *DSGetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *GSGetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *PSGetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *CSGetConstantBuffers1)(
        ID3D11DeviceContext2 *This,
        UINT StartSlot,
        UINT NumBuffers,
        ID3D11Buffer **ppConstantBuffers,
        UINT *pFirstConstant,
        UINT *pNumConstants);

    void (STDMETHODCALLTYPE *SwapDeviceContextState)(
        ID3D11DeviceContext2 *This,
        ID3DDeviceContextState *pState,
        ID3DDeviceContextState **ppPreviousState);

    void (STDMETHODCALLTYPE *ClearView)(
        ID3D11DeviceContext2 *This,
        ID3D11View *pView,
        const FLOAT Color[4],
        const D3D11_RECT *pRect,
        UINT NumRects);

    void (STDMETHODCALLTYPE *DiscardView1)(
        ID3D11DeviceContext2 *This,
        ID3D11View *pResourceView,
        const D3D11_RECT *pRects,
        UINT NumRects);

    /*** ID3D11DeviceContext2 methods ***/
    HRESULT (STDMETHODCALLTYPE *UpdateTileMappings)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *resource,
        UINT region_count,
        const D3D11_TILED_RESOURCE_COORDINATE *region_start_coordinates,
        const D3D11_TILE_REGION_SIZE *region_sizes,
        ID3D11Buffer *pool,
        UINT range_count,
        const UINT *range_flags,
        const UINT *pool_start_offsets,
        const UINT *range_tile_counts,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *CopyTileMappings)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *dst_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,
        ID3D11Resource *src_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *src_start_coordinate,
        const D3D11_TILE_REGION_SIZE *region_size,
        UINT flags);

    void (STDMETHODCALLTYPE *CopyTiles)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *resource,
        const D3D11_TILED_RESOURCE_COORDINATE *start_coordinate,
        const D3D11_TILE_REGION_SIZE *size,
        ID3D11Buffer *buffer,
        UINT64 start_offset,
        UINT flags);

    void (STDMETHODCALLTYPE *UpdateTiles)(
        ID3D11DeviceContext2 *This,
        ID3D11Resource *dst_resource,
        const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,
        const D3D11_TILE_REGION_SIZE *dst_region_size,
        const void *src_data,
        UINT flags);

    HRESULT (STDMETHODCALLTYPE *ResizeTilePool)(
        ID3D11DeviceContext2 *This,
        ID3D11Buffer *pool,
        UINT64 size);

    void (STDMETHODCALLTYPE *TiledResourceBarrier)(
        ID3D11DeviceContext2 *This,
        ID3D11DeviceChild *before_barrier,
        ID3D11DeviceChild *after_barrier);

    WINBOOL (STDMETHODCALLTYPE *IsAnnotationEnabled)(
        ID3D11DeviceContext2 *This);

    void (STDMETHODCALLTYPE *SetMarkerInt)(
        ID3D11DeviceContext2 *This,
        const WCHAR *label,
        int data);

    void (STDMETHODCALLTYPE *BeginEventInt)(
        ID3D11DeviceContext2 *This,
        const WCHAR *label,
        int data);

    void (STDMETHODCALLTYPE *EndEvent)(
        ID3D11DeviceContext2 *This);

    END_INTERFACE
} ID3D11DeviceContext2Vtbl;

interface ID3D11DeviceContext2 {
    CONST_VTBL ID3D11DeviceContext2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11DeviceContext2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11DeviceContext2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11DeviceContext2_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11DeviceChild methods ***/
#define ID3D11DeviceContext2_GetDevice(This,ppDevice) (This)->lpVtbl->GetDevice(This,ppDevice)
#define ID3D11DeviceContext2_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11DeviceContext2_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11DeviceContext2_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
/*** ID3D11DeviceContext methods ***/
#define ID3D11DeviceContext2_VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext2_PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext2_DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation) (This)->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation)
#define ID3D11DeviceContext2_Draw(This,VertexCount,StartVertexLocation) (This)->lpVtbl->Draw(This,VertexCount,StartVertexLocation)
#define ID3D11DeviceContext2_Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource) (This)->lpVtbl->Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource)
#define ID3D11DeviceContext2_Unmap(This,pResource,Subresource) (This)->lpVtbl->Unmap(This,pResource,Subresource)
#define ID3D11DeviceContext2_PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_IASetInputLayout(This,pInputLayout) (This)->lpVtbl->IASetInputLayout(This,pInputLayout)
#define ID3D11DeviceContext2_IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D11DeviceContext2_IASetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D11DeviceContext2_DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation)
#define ID3D11DeviceContext2_DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation) (This)->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation)
#define ID3D11DeviceContext2_GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_GSSetShader(This,pShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->GSSetShader(This,pShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext2_IASetPrimitiveTopology(This,Topology) (This)->lpVtbl->IASetPrimitiveTopology(This,Topology)
#define ID3D11DeviceContext2_VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_Begin(This,pAsync) (This)->lpVtbl->Begin(This,pAsync)
#define ID3D11DeviceContext2_End(This,pAsync) (This)->lpVtbl->End(This,pAsync)
#define ID3D11DeviceContext2_GetData(This,pAsync,pData,DataSize,GetDataFlags) (This)->lpVtbl->GetData(This,pAsync,pData,DataSize,GetDataFlags)
#define ID3D11DeviceContext2_SetPredication(This,pPredicate,PredicateValue) (This)->lpVtbl->SetPredication(This,pPredicate,PredicateValue)
#define ID3D11DeviceContext2_GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView) (This)->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView)
#define ID3D11DeviceContext2_OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts) (This)->lpVtbl->OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts)
#define ID3D11DeviceContext2_OMSetBlendState(This,pBlendState,BlendFactor,SampleMask) (This)->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask)
#define ID3D11DeviceContext2_OMSetDepthStencilState(This,pDepthStencilState,StencilRef) (This)->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef)
#define ID3D11DeviceContext2_SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets) (This)->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets)
#define ID3D11DeviceContext2_DrawAuto(This) (This)->lpVtbl->DrawAuto(This)
#define ID3D11DeviceContext2_DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext2_DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext2_Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ) (This)->lpVtbl->Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ)
#define ID3D11DeviceContext2_DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs) (This)->lpVtbl->DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs)
#define ID3D11DeviceContext2_RSSetState(This,pRasterizerState) (This)->lpVtbl->RSSetState(This,pRasterizerState)
#define ID3D11DeviceContext2_RSSetViewports(This,NumViewports,pViewports) (This)->lpVtbl->RSSetViewports(This,NumViewports,pViewports)
#define ID3D11DeviceContext2_RSSetScissorRects(This,NumRects,pRects) (This)->lpVtbl->RSSetScissorRects(This,NumRects,pRects)
#define ID3D11DeviceContext2_CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox) (This)->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox)
#define ID3D11DeviceContext2_CopyResource(This,pDstResource,pSrcResource) (This)->lpVtbl->CopyResource(This,pDstResource,pSrcResource)
#define ID3D11DeviceContext2_UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch) (This)->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch)
#define ID3D11DeviceContext2_CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView) (This)->lpVtbl->CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView)
#define ID3D11DeviceContext2_ClearRenderTargetView(This,pRenderTargetView,ColorRGBA) (This)->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA)
#define ID3D11DeviceContext2_ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values) (This)->lpVtbl->ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values)
#define ID3D11DeviceContext2_ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values) (This)->lpVtbl->ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values)
#define ID3D11DeviceContext2_ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil) (This)->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil)
#define ID3D11DeviceContext2_GenerateMips(This,pShaderResourceView) (This)->lpVtbl->GenerateMips(This,pShaderResourceView)
#define ID3D11DeviceContext2_SetResourceMinLOD(This,pResource,MinLOD) (This)->lpVtbl->SetResourceMinLOD(This,pResource,MinLOD)
#define ID3D11DeviceContext2_GetResourceMinLOD(This,pResource) (This)->lpVtbl->GetResourceMinLOD(This,pResource)
#define ID3D11DeviceContext2_ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format) (This)->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format)
#define ID3D11DeviceContext2_ExecuteCommandList(This,pCommandList,RestoreContextState) (This)->lpVtbl->ExecuteCommandList(This,pCommandList,RestoreContextState)
#define ID3D11DeviceContext2_HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext2_HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext2_DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts) (This)->lpVtbl->CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts)
#define ID3D11DeviceContext2_CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances) (This)->lpVtbl->CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances)
#define ID3D11DeviceContext2_CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext2_PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext2_PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_IAGetInputLayout(This,ppInputLayout) (This)->lpVtbl->IAGetInputLayout(This,ppInputLayout)
#define ID3D11DeviceContext2_IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets) (This)->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets)
#define ID3D11DeviceContext2_IAGetIndexBuffer(This,pIndexBuffer,Format,Offset) (This)->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset)
#define ID3D11DeviceContext2_GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext2_IAGetPrimitiveTopology(This,pTopology) (This)->lpVtbl->IAGetPrimitiveTopology(This,pTopology)
#define ID3D11DeviceContext2_VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_GetPredication(This,ppPredicate,pPredicateValue) (This)->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue)
#define ID3D11DeviceContext2_GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView) (This)->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView)
#define ID3D11DeviceContext2_OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews) (This)->lpVtbl->OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews)
#define ID3D11DeviceContext2_OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask) (This)->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask)
#define ID3D11DeviceContext2_OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef) (This)->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef)
#define ID3D11DeviceContext2_SOGetTargets(This,NumBuffers,ppSOTargets) (This)->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets)
#define ID3D11DeviceContext2_RSGetState(This,ppRasterizerState) (This)->lpVtbl->RSGetState(This,ppRasterizerState)
#define ID3D11DeviceContext2_RSGetViewports(This,pNumViewports,pViewports) (This)->lpVtbl->RSGetViewports(This,pNumViewports,pViewports)
#define ID3D11DeviceContext2_RSGetScissorRects(This,pNumRects,pRects) (This)->lpVtbl->RSGetScissorRects(This,pNumRects,pRects)
#define ID3D11DeviceContext2_HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext2_HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext2_DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews) (This)->lpVtbl->CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews)
#define ID3D11DeviceContext2_CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews) (This)->lpVtbl->CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews)
#define ID3D11DeviceContext2_CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances) (This)->lpVtbl->CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances)
#define ID3D11DeviceContext2_CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers) (This)->lpVtbl->CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers)
#define ID3D11DeviceContext2_CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers) (This)->lpVtbl->CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers)
#define ID3D11DeviceContext2_ClearState(This) (This)->lpVtbl->ClearState(This)
#define ID3D11DeviceContext2_Flush(This) (This)->lpVtbl->Flush(This)
#define ID3D11DeviceContext2_GetType(This) (This)->lpVtbl->GetType(This)
#define ID3D11DeviceContext2_GetContextFlags(This) (This)->lpVtbl->GetContextFlags(This)
#define ID3D11DeviceContext2_FinishCommandList(This,RestoreDeferredContextState,ppCommandList) (This)->lpVtbl->FinishCommandList(This,RestoreDeferredContextState,ppCommandList)
/*** ID3D11DeviceContext1 methods ***/
#define ID3D11DeviceContext2_CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags) (This)->lpVtbl->CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags)
#define ID3D11DeviceContext2_UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags) (This)->lpVtbl->UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags)
#define ID3D11DeviceContext2_DiscardResource(This,pResource) (This)->lpVtbl->DiscardResource(This,pResource)
#define ID3D11DeviceContext2_DiscardView(This,pResourceView) (This)->lpVtbl->DiscardView(This,pResourceView)
#define ID3D11DeviceContext2_VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants) (This)->lpVtbl->CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants)
#define ID3D11DeviceContext2_SwapDeviceContextState(This,pState,ppPreviousState) (This)->lpVtbl->SwapDeviceContextState(This,pState,ppPreviousState)
#define ID3D11DeviceContext2_ClearView(This,pView,Color,pRect,NumRects) (This)->lpVtbl->ClearView(This,pView,Color,pRect,NumRects)
#define ID3D11DeviceContext2_DiscardView1(This,pResourceView,pRects,NumRects) (This)->lpVtbl->DiscardView1(This,pResourceView,pRects,NumRects)
/*** ID3D11DeviceContext2 methods ***/
#define ID3D11DeviceContext2_UpdateTileMappings(This,resource,region_count,region_start_coordinates,region_sizes,pool,range_count,range_flags,pool_start_offsets,range_tile_counts,flags) (This)->lpVtbl->UpdateTileMappings(This,resource,region_count,region_start_coordinates,region_sizes,pool,range_count,range_flags,pool_start_offsets,range_tile_counts,flags)
#define ID3D11DeviceContext2_CopyTileMappings(This,dst_resource,dst_start_coordinate,src_resource,src_start_coordinate,region_size,flags) (This)->lpVtbl->CopyTileMappings(This,dst_resource,dst_start_coordinate,src_resource,src_start_coordinate,region_size,flags)
#define ID3D11DeviceContext2_CopyTiles(This,resource,start_coordinate,size,buffer,start_offset,flags) (This)->lpVtbl->CopyTiles(This,resource,start_coordinate,size,buffer,start_offset,flags)
#define ID3D11DeviceContext2_UpdateTiles(This,dst_resource,dst_start_coordinate,dst_region_size,src_data,flags) (This)->lpVtbl->UpdateTiles(This,dst_resource,dst_start_coordinate,dst_region_size,src_data,flags)
#define ID3D11DeviceContext2_ResizeTilePool(This,pool,size) (This)->lpVtbl->ResizeTilePool(This,pool,size)
#define ID3D11DeviceContext2_TiledResourceBarrier(This,before_barrier,after_barrier) (This)->lpVtbl->TiledResourceBarrier(This,before_barrier,after_barrier)
#define ID3D11DeviceContext2_IsAnnotationEnabled(This) (This)->lpVtbl->IsAnnotationEnabled(This)
#define ID3D11DeviceContext2_SetMarkerInt(This,label,data) (This)->lpVtbl->SetMarkerInt(This,label,data)
#define ID3D11DeviceContext2_BeginEventInt(This,label,data) (This)->lpVtbl->BeginEventInt(This,label,data)
#define ID3D11DeviceContext2_EndEvent(This) (This)->lpVtbl->EndEvent(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11DeviceContext2_QueryInterface(ID3D11DeviceContext2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11DeviceContext2_AddRef(ID3D11DeviceContext2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11DeviceContext2_Release(ID3D11DeviceContext2* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11DeviceChild methods ***/
static inline void ID3D11DeviceContext2_GetDevice(ID3D11DeviceContext2* This,ID3D11Device **ppDevice) {
    This->lpVtbl->GetDevice(This,ppDevice);
}
static inline HRESULT ID3D11DeviceContext2_GetPrivateData(ID3D11DeviceContext2* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11DeviceContext2_SetPrivateData(ID3D11DeviceContext2* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11DeviceContext2_SetPrivateDataInterface(ID3D11DeviceContext2* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
/*** ID3D11DeviceContext methods ***/
static inline void ID3D11DeviceContext2_VSSetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->VSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_PSSetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->PSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_PSSetShader(ID3D11DeviceContext2* This,ID3D11PixelShader *pPixelShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->PSSetShader(This,pPixelShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext2_PSSetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->PSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_VSSetShader(ID3D11DeviceContext2* This,ID3D11VertexShader *pVertexShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->VSSetShader(This,pVertexShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext2_DrawIndexed(ID3D11DeviceContext2* This,UINT IndexCount,UINT StartIndexLocation,INT BaseVertexLocation) {
    This->lpVtbl->DrawIndexed(This,IndexCount,StartIndexLocation,BaseVertexLocation);
}
static inline void ID3D11DeviceContext2_Draw(ID3D11DeviceContext2* This,UINT VertexCount,UINT StartVertexLocation) {
    This->lpVtbl->Draw(This,VertexCount,StartVertexLocation);
}
static inline HRESULT ID3D11DeviceContext2_Map(ID3D11DeviceContext2* This,ID3D11Resource *pResource,UINT Subresource,D3D11_MAP MapType,UINT MapFlags,D3D11_MAPPED_SUBRESOURCE *pMappedResource) {
    return This->lpVtbl->Map(This,pResource,Subresource,MapType,MapFlags,pMappedResource);
}
static inline void ID3D11DeviceContext2_Unmap(ID3D11DeviceContext2* This,ID3D11Resource *pResource,UINT Subresource) {
    This->lpVtbl->Unmap(This,pResource,Subresource);
}
static inline void ID3D11DeviceContext2_PSSetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->PSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_IASetInputLayout(ID3D11DeviceContext2* This,ID3D11InputLayout *pInputLayout) {
    This->lpVtbl->IASetInputLayout(This,pInputLayout);
}
static inline void ID3D11DeviceContext2_IASetVertexBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppVertexBuffers,const UINT *pStrides,const UINT *pOffsets) {
    This->lpVtbl->IASetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static inline void ID3D11DeviceContext2_IASetIndexBuffer(ID3D11DeviceContext2* This,ID3D11Buffer *pIndexBuffer,DXGI_FORMAT Format,UINT Offset) {
    This->lpVtbl->IASetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static inline void ID3D11DeviceContext2_DrawIndexedInstanced(ID3D11DeviceContext2* This,UINT IndexCountPerInstance,UINT InstanceCount,UINT StartIndexLocation,INT BaseVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawIndexedInstanced(This,IndexCountPerInstance,InstanceCount,StartIndexLocation,BaseVertexLocation,StartInstanceLocation);
}
static inline void ID3D11DeviceContext2_DrawInstanced(ID3D11DeviceContext2* This,UINT VertexCountPerInstance,UINT InstanceCount,UINT StartVertexLocation,UINT StartInstanceLocation) {
    This->lpVtbl->DrawInstanced(This,VertexCountPerInstance,InstanceCount,StartVertexLocation,StartInstanceLocation);
}
static inline void ID3D11DeviceContext2_GSSetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->GSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_GSSetShader(ID3D11DeviceContext2* This,ID3D11GeometryShader *pShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->GSSetShader(This,pShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext2_IASetPrimitiveTopology(ID3D11DeviceContext2* This,D3D11_PRIMITIVE_TOPOLOGY Topology) {
    This->lpVtbl->IASetPrimitiveTopology(This,Topology);
}
static inline void ID3D11DeviceContext2_VSSetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->VSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_VSSetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->VSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_Begin(ID3D11DeviceContext2* This,ID3D11Asynchronous *pAsync) {
    This->lpVtbl->Begin(This,pAsync);
}
static inline void ID3D11DeviceContext2_End(ID3D11DeviceContext2* This,ID3D11Asynchronous *pAsync) {
    This->lpVtbl->End(This,pAsync);
}
static inline HRESULT ID3D11DeviceContext2_GetData(ID3D11DeviceContext2* This,ID3D11Asynchronous *pAsync,void *pData,UINT DataSize,UINT GetDataFlags) {
    return This->lpVtbl->GetData(This,pAsync,pData,DataSize,GetDataFlags);
}
static inline void ID3D11DeviceContext2_SetPredication(ID3D11DeviceContext2* This,ID3D11Predicate *pPredicate,WINBOOL PredicateValue) {
    This->lpVtbl->SetPredication(This,pPredicate,PredicateValue);
}
static inline void ID3D11DeviceContext2_GSSetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->GSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_GSSetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->GSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_OMSetRenderTargets(ID3D11DeviceContext2* This,UINT NumViews,ID3D11RenderTargetView *const *ppRenderTargetViews,ID3D11DepthStencilView *pDepthStencilView) {
    This->lpVtbl->OMSetRenderTargets(This,NumViews,ppRenderTargetViews,pDepthStencilView);
}
static inline void ID3D11DeviceContext2_OMSetRenderTargetsAndUnorderedAccessViews(ID3D11DeviceContext2* This,UINT NumRTVs,ID3D11RenderTargetView *const *ppRenderTargetViews,ID3D11DepthStencilView *pDepthStencilView,UINT UAVStartSlot,UINT NumUAVs,ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,const UINT *pUAVInitialCounts) {
    This->lpVtbl->OMSetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,pDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts);
}
static inline void ID3D11DeviceContext2_OMSetBlendState(ID3D11DeviceContext2* This,ID3D11BlendState *pBlendState,const FLOAT BlendFactor[4],UINT SampleMask) {
    This->lpVtbl->OMSetBlendState(This,pBlendState,BlendFactor,SampleMask);
}
static inline void ID3D11DeviceContext2_OMSetDepthStencilState(ID3D11DeviceContext2* This,ID3D11DepthStencilState *pDepthStencilState,UINT StencilRef) {
    This->lpVtbl->OMSetDepthStencilState(This,pDepthStencilState,StencilRef);
}
static inline void ID3D11DeviceContext2_SOSetTargets(ID3D11DeviceContext2* This,UINT NumBuffers,ID3D11Buffer *const *ppSOTargets,const UINT *pOffsets) {
    This->lpVtbl->SOSetTargets(This,NumBuffers,ppSOTargets,pOffsets);
}
static inline void ID3D11DeviceContext2_DrawAuto(ID3D11DeviceContext2* This) {
    This->lpVtbl->DrawAuto(This);
}
static inline void ID3D11DeviceContext2_DrawIndexedInstancedIndirect(ID3D11DeviceContext2* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DrawIndexedInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext2_DrawInstancedIndirect(ID3D11DeviceContext2* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DrawInstancedIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext2_Dispatch(ID3D11DeviceContext2* This,UINT ThreadGroupCountX,UINT ThreadGroupCountY,UINT ThreadGroupCountZ) {
    This->lpVtbl->Dispatch(This,ThreadGroupCountX,ThreadGroupCountY,ThreadGroupCountZ);
}
static inline void ID3D11DeviceContext2_DispatchIndirect(ID3D11DeviceContext2* This,ID3D11Buffer *pBufferForArgs,UINT AlignedByteOffsetForArgs) {
    This->lpVtbl->DispatchIndirect(This,pBufferForArgs,AlignedByteOffsetForArgs);
}
static inline void ID3D11DeviceContext2_RSSetState(ID3D11DeviceContext2* This,ID3D11RasterizerState *pRasterizerState) {
    This->lpVtbl->RSSetState(This,pRasterizerState);
}
static inline void ID3D11DeviceContext2_RSSetViewports(ID3D11DeviceContext2* This,UINT NumViewports,const D3D11_VIEWPORT *pViewports) {
    This->lpVtbl->RSSetViewports(This,NumViewports,pViewports);
}
static inline void ID3D11DeviceContext2_RSSetScissorRects(ID3D11DeviceContext2* This,UINT NumRects,const D3D11_RECT *pRects) {
    This->lpVtbl->RSSetScissorRects(This,NumRects,pRects);
}
static inline void ID3D11DeviceContext2_CopySubresourceRegion(ID3D11DeviceContext2* This,ID3D11Resource *pDstResource,UINT DstSubresource,UINT DstX,UINT DstY,UINT DstZ,ID3D11Resource *pSrcResource,UINT SrcSubresource,const D3D11_BOX *pSrcBox) {
    This->lpVtbl->CopySubresourceRegion(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox);
}
static inline void ID3D11DeviceContext2_CopyResource(ID3D11DeviceContext2* This,ID3D11Resource *pDstResource,ID3D11Resource *pSrcResource) {
    This->lpVtbl->CopyResource(This,pDstResource,pSrcResource);
}
static inline void ID3D11DeviceContext2_UpdateSubresource(ID3D11DeviceContext2* This,ID3D11Resource *pDstResource,UINT DstSubresource,const D3D11_BOX *pDstBox,const void *pSrcData,UINT SrcRowPitch,UINT SrcDepthPitch) {
    This->lpVtbl->UpdateSubresource(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch);
}
static inline void ID3D11DeviceContext2_CopyStructureCount(ID3D11DeviceContext2* This,ID3D11Buffer *pDstBuffer,UINT DstAlignedByteOffset,ID3D11UnorderedAccessView *pSrcView) {
    This->lpVtbl->CopyStructureCount(This,pDstBuffer,DstAlignedByteOffset,pSrcView);
}
static inline void ID3D11DeviceContext2_ClearRenderTargetView(ID3D11DeviceContext2* This,ID3D11RenderTargetView *pRenderTargetView,const FLOAT ColorRGBA[4]) {
    This->lpVtbl->ClearRenderTargetView(This,pRenderTargetView,ColorRGBA);
}
static inline void ID3D11DeviceContext2_ClearUnorderedAccessViewUint(ID3D11DeviceContext2* This,ID3D11UnorderedAccessView *pUnorderedAccessView,const UINT Values[4]) {
    This->lpVtbl->ClearUnorderedAccessViewUint(This,pUnorderedAccessView,Values);
}
static inline void ID3D11DeviceContext2_ClearUnorderedAccessViewFloat(ID3D11DeviceContext2* This,ID3D11UnorderedAccessView *pUnorderedAccessView,const FLOAT Values[4]) {
    This->lpVtbl->ClearUnorderedAccessViewFloat(This,pUnorderedAccessView,Values);
}
static inline void ID3D11DeviceContext2_ClearDepthStencilView(ID3D11DeviceContext2* This,ID3D11DepthStencilView *pDepthStencilView,UINT ClearFlags,FLOAT Depth,UINT8 Stencil) {
    This->lpVtbl->ClearDepthStencilView(This,pDepthStencilView,ClearFlags,Depth,Stencil);
}
static inline void ID3D11DeviceContext2_GenerateMips(ID3D11DeviceContext2* This,ID3D11ShaderResourceView *pShaderResourceView) {
    This->lpVtbl->GenerateMips(This,pShaderResourceView);
}
static inline void ID3D11DeviceContext2_SetResourceMinLOD(ID3D11DeviceContext2* This,ID3D11Resource *pResource,FLOAT MinLOD) {
    This->lpVtbl->SetResourceMinLOD(This,pResource,MinLOD);
}
static inline FLOAT ID3D11DeviceContext2_GetResourceMinLOD(ID3D11DeviceContext2* This,ID3D11Resource *pResource) {
    return This->lpVtbl->GetResourceMinLOD(This,pResource);
}
static inline void ID3D11DeviceContext2_ResolveSubresource(ID3D11DeviceContext2* This,ID3D11Resource *pDstResource,UINT DstSubresource,ID3D11Resource *pSrcResource,UINT SrcSubresource,DXGI_FORMAT Format) {
    This->lpVtbl->ResolveSubresource(This,pDstResource,DstSubresource,pSrcResource,SrcSubresource,Format);
}
static inline void ID3D11DeviceContext2_ExecuteCommandList(ID3D11DeviceContext2* This,ID3D11CommandList *pCommandList,WINBOOL RestoreContextState) {
    This->lpVtbl->ExecuteCommandList(This,pCommandList,RestoreContextState);
}
static inline void ID3D11DeviceContext2_HSSetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->HSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_HSSetShader(ID3D11DeviceContext2* This,ID3D11HullShader *pHullShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->HSSetShader(This,pHullShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext2_HSSetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->HSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_HSSetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->HSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_DSSetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->DSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_DSSetShader(ID3D11DeviceContext2* This,ID3D11DomainShader *pDomainShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->DSSetShader(This,pDomainShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext2_DSSetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->DSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_DSSetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->DSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_CSSetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView *const *ppShaderResourceViews) {
    This->lpVtbl->CSSetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_CSSetUnorderedAccessViews(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumUAVs,ID3D11UnorderedAccessView *const *ppUnorderedAccessViews,const UINT *pUAVInitialCounts) {
    This->lpVtbl->CSSetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews,pUAVInitialCounts);
}
static inline void ID3D11DeviceContext2_CSSetShader(ID3D11DeviceContext2* This,ID3D11ComputeShader *pComputeShader,ID3D11ClassInstance *const *ppClassInstances,UINT NumClassInstances) {
    This->lpVtbl->CSSetShader(This,pComputeShader,ppClassInstances,NumClassInstances);
}
static inline void ID3D11DeviceContext2_CSSetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState *const *ppSamplers) {
    This->lpVtbl->CSSetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_CSSetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers) {
    This->lpVtbl->CSSetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_VSGetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->VSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_PSGetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->PSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_PSGetShader(ID3D11DeviceContext2* This,ID3D11PixelShader **ppPixelShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->PSGetShader(This,ppPixelShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext2_PSGetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->PSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_VSGetShader(ID3D11DeviceContext2* This,ID3D11VertexShader **ppVertexShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->VSGetShader(This,ppVertexShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext2_PSGetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->PSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_IAGetInputLayout(ID3D11DeviceContext2* This,ID3D11InputLayout **ppInputLayout) {
    This->lpVtbl->IAGetInputLayout(This,ppInputLayout);
}
static inline void ID3D11DeviceContext2_IAGetVertexBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppVertexBuffers,UINT *pStrides,UINT *pOffsets) {
    This->lpVtbl->IAGetVertexBuffers(This,StartSlot,NumBuffers,ppVertexBuffers,pStrides,pOffsets);
}
static inline void ID3D11DeviceContext2_IAGetIndexBuffer(ID3D11DeviceContext2* This,ID3D11Buffer **pIndexBuffer,DXGI_FORMAT *Format,UINT *Offset) {
    This->lpVtbl->IAGetIndexBuffer(This,pIndexBuffer,Format,Offset);
}
static inline void ID3D11DeviceContext2_GSGetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->GSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_GSGetShader(ID3D11DeviceContext2* This,ID3D11GeometryShader **ppGeometryShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->GSGetShader(This,ppGeometryShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext2_IAGetPrimitiveTopology(ID3D11DeviceContext2* This,D3D11_PRIMITIVE_TOPOLOGY *pTopology) {
    This->lpVtbl->IAGetPrimitiveTopology(This,pTopology);
}
static inline void ID3D11DeviceContext2_VSGetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->VSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_VSGetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->VSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_GetPredication(ID3D11DeviceContext2* This,ID3D11Predicate **ppPredicate,WINBOOL *pPredicateValue) {
    This->lpVtbl->GetPredication(This,ppPredicate,pPredicateValue);
}
static inline void ID3D11DeviceContext2_GSGetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->GSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_GSGetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->GSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_OMGetRenderTargets(ID3D11DeviceContext2* This,UINT NumViews,ID3D11RenderTargetView **ppRenderTargetViews,ID3D11DepthStencilView **ppDepthStencilView) {
    This->lpVtbl->OMGetRenderTargets(This,NumViews,ppRenderTargetViews,ppDepthStencilView);
}
static inline void ID3D11DeviceContext2_OMGetRenderTargetsAndUnorderedAccessViews(ID3D11DeviceContext2* This,UINT NumRTVs,ID3D11RenderTargetView **ppRenderTargetViews,ID3D11DepthStencilView **ppDepthStencilView,UINT UAVStartSlot,UINT NumUAVs,ID3D11UnorderedAccessView **ppUnorderedAccessViews) {
    This->lpVtbl->OMGetRenderTargetsAndUnorderedAccessViews(This,NumRTVs,ppRenderTargetViews,ppDepthStencilView,UAVStartSlot,NumUAVs,ppUnorderedAccessViews);
}
static inline void ID3D11DeviceContext2_OMGetBlendState(ID3D11DeviceContext2* This,ID3D11BlendState **ppBlendState,FLOAT BlendFactor[4],UINT *pSampleMask) {
    This->lpVtbl->OMGetBlendState(This,ppBlendState,BlendFactor,pSampleMask);
}
static inline void ID3D11DeviceContext2_OMGetDepthStencilState(ID3D11DeviceContext2* This,ID3D11DepthStencilState **ppDepthStencilState,UINT *pStencilRef) {
    This->lpVtbl->OMGetDepthStencilState(This,ppDepthStencilState,pStencilRef);
}
static inline void ID3D11DeviceContext2_SOGetTargets(ID3D11DeviceContext2* This,UINT NumBuffers,ID3D11Buffer **ppSOTargets) {
    This->lpVtbl->SOGetTargets(This,NumBuffers,ppSOTargets);
}
static inline void ID3D11DeviceContext2_RSGetState(ID3D11DeviceContext2* This,ID3D11RasterizerState **ppRasterizerState) {
    This->lpVtbl->RSGetState(This,ppRasterizerState);
}
static inline void ID3D11DeviceContext2_RSGetViewports(ID3D11DeviceContext2* This,UINT *pNumViewports,D3D11_VIEWPORT *pViewports) {
    This->lpVtbl->RSGetViewports(This,pNumViewports,pViewports);
}
static inline void ID3D11DeviceContext2_RSGetScissorRects(ID3D11DeviceContext2* This,UINT *pNumRects,D3D11_RECT *pRects) {
    This->lpVtbl->RSGetScissorRects(This,pNumRects,pRects);
}
static inline void ID3D11DeviceContext2_HSGetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->HSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_HSGetShader(ID3D11DeviceContext2* This,ID3D11HullShader **ppHullShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->HSGetShader(This,ppHullShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext2_HSGetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->HSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_HSGetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->HSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_DSGetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->DSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_DSGetShader(ID3D11DeviceContext2* This,ID3D11DomainShader **ppDomainShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->DSGetShader(This,ppDomainShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext2_DSGetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->DSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_DSGetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->DSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_CSGetShaderResources(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumViews,ID3D11ShaderResourceView **ppShaderResourceViews) {
    This->lpVtbl->CSGetShaderResources(This,StartSlot,NumViews,ppShaderResourceViews);
}
static inline void ID3D11DeviceContext2_CSGetUnorderedAccessViews(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumUAVs,ID3D11UnorderedAccessView **ppUnorderedAccessViews) {
    This->lpVtbl->CSGetUnorderedAccessViews(This,StartSlot,NumUAVs,ppUnorderedAccessViews);
}
static inline void ID3D11DeviceContext2_CSGetShader(ID3D11DeviceContext2* This,ID3D11ComputeShader **ppComputeShader,ID3D11ClassInstance **ppClassInstances,UINT *pNumClassInstances) {
    This->lpVtbl->CSGetShader(This,ppComputeShader,ppClassInstances,pNumClassInstances);
}
static inline void ID3D11DeviceContext2_CSGetSamplers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumSamplers,ID3D11SamplerState **ppSamplers) {
    This->lpVtbl->CSGetSamplers(This,StartSlot,NumSamplers,ppSamplers);
}
static inline void ID3D11DeviceContext2_CSGetConstantBuffers(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers) {
    This->lpVtbl->CSGetConstantBuffers(This,StartSlot,NumBuffers,ppConstantBuffers);
}
static inline void ID3D11DeviceContext2_ClearState(ID3D11DeviceContext2* This) {
    This->lpVtbl->ClearState(This);
}
static inline void ID3D11DeviceContext2_Flush(ID3D11DeviceContext2* This) {
    This->lpVtbl->Flush(This);
}
static inline D3D11_DEVICE_CONTEXT_TYPE ID3D11DeviceContext2_GetType(ID3D11DeviceContext2* This) {
    return This->lpVtbl->GetType(This);
}
static inline UINT ID3D11DeviceContext2_GetContextFlags(ID3D11DeviceContext2* This) {
    return This->lpVtbl->GetContextFlags(This);
}
static inline HRESULT ID3D11DeviceContext2_FinishCommandList(ID3D11DeviceContext2* This,WINBOOL RestoreDeferredContextState,ID3D11CommandList **ppCommandList) {
    return This->lpVtbl->FinishCommandList(This,RestoreDeferredContextState,ppCommandList);
}
/*** ID3D11DeviceContext1 methods ***/
static inline void ID3D11DeviceContext2_CopySubresourceRegion1(ID3D11DeviceContext2* This,ID3D11Resource *pDstResource,UINT DstSubresource,UINT DstX,UINT DstY,UINT DstZ,ID3D11Resource *pSrcResource,UINT SrcSubresource,const D3D11_BOX *pSrcBox,UINT CopyFlags) {
    This->lpVtbl->CopySubresourceRegion1(This,pDstResource,DstSubresource,DstX,DstY,DstZ,pSrcResource,SrcSubresource,pSrcBox,CopyFlags);
}
static inline void ID3D11DeviceContext2_UpdateSubresource1(ID3D11DeviceContext2* This,ID3D11Resource *pDstResource,UINT DstSubresource,const D3D11_BOX *pDstBox,const void *pSrcData,UINT SrcRowPitch,UINT SrcDepthPitch,UINT CopyFlags) {
    This->lpVtbl->UpdateSubresource1(This,pDstResource,DstSubresource,pDstBox,pSrcData,SrcRowPitch,SrcDepthPitch,CopyFlags);
}
static inline void ID3D11DeviceContext2_DiscardResource(ID3D11DeviceContext2* This,ID3D11Resource *pResource) {
    This->lpVtbl->DiscardResource(This,pResource);
}
static inline void ID3D11DeviceContext2_DiscardView(ID3D11DeviceContext2* This,ID3D11View *pResourceView) {
    This->lpVtbl->DiscardView(This,pResourceView);
}
static inline void ID3D11DeviceContext2_VSSetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->VSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_HSSetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->HSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_DSSetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->DSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_GSSetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->GSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_PSSetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->PSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_CSSetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer *const *ppConstantBuffers,const UINT *pFirstConstant,const UINT *pNumConstants) {
    This->lpVtbl->CSSetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_VSGetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->VSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_HSGetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->HSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_DSGetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->DSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_GSGetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->GSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_PSGetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->PSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_CSGetConstantBuffers1(ID3D11DeviceContext2* This,UINT StartSlot,UINT NumBuffers,ID3D11Buffer **ppConstantBuffers,UINT *pFirstConstant,UINT *pNumConstants) {
    This->lpVtbl->CSGetConstantBuffers1(This,StartSlot,NumBuffers,ppConstantBuffers,pFirstConstant,pNumConstants);
}
static inline void ID3D11DeviceContext2_SwapDeviceContextState(ID3D11DeviceContext2* This,ID3DDeviceContextState *pState,ID3DDeviceContextState **ppPreviousState) {
    This->lpVtbl->SwapDeviceContextState(This,pState,ppPreviousState);
}
static inline void ID3D11DeviceContext2_ClearView(ID3D11DeviceContext2* This,ID3D11View *pView,const FLOAT Color[4],const D3D11_RECT *pRect,UINT NumRects) {
    This->lpVtbl->ClearView(This,pView,Color,pRect,NumRects);
}
static inline void ID3D11DeviceContext2_DiscardView1(ID3D11DeviceContext2* This,ID3D11View *pResourceView,const D3D11_RECT *pRects,UINT NumRects) {
    This->lpVtbl->DiscardView1(This,pResourceView,pRects,NumRects);
}
/*** ID3D11DeviceContext2 methods ***/
static inline HRESULT ID3D11DeviceContext2_UpdateTileMappings(ID3D11DeviceContext2* This,ID3D11Resource *resource,UINT region_count,const D3D11_TILED_RESOURCE_COORDINATE *region_start_coordinates,const D3D11_TILE_REGION_SIZE *region_sizes,ID3D11Buffer *pool,UINT range_count,const UINT *range_flags,const UINT *pool_start_offsets,const UINT *range_tile_counts,UINT flags) {
    return This->lpVtbl->UpdateTileMappings(This,resource,region_count,region_start_coordinates,region_sizes,pool,range_count,range_flags,pool_start_offsets,range_tile_counts,flags);
}
static inline HRESULT ID3D11DeviceContext2_CopyTileMappings(ID3D11DeviceContext2* This,ID3D11Resource *dst_resource,const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,ID3D11Resource *src_resource,const D3D11_TILED_RESOURCE_COORDINATE *src_start_coordinate,const D3D11_TILE_REGION_SIZE *region_size,UINT flags) {
    return This->lpVtbl->CopyTileMappings(This,dst_resource,dst_start_coordinate,src_resource,src_start_coordinate,region_size,flags);
}
static inline void ID3D11DeviceContext2_CopyTiles(ID3D11DeviceContext2* This,ID3D11Resource *resource,const D3D11_TILED_RESOURCE_COORDINATE *start_coordinate,const D3D11_TILE_REGION_SIZE *size,ID3D11Buffer *buffer,UINT64 start_offset,UINT flags) {
    This->lpVtbl->CopyTiles(This,resource,start_coordinate,size,buffer,start_offset,flags);
}
static inline void ID3D11DeviceContext2_UpdateTiles(ID3D11DeviceContext2* This,ID3D11Resource *dst_resource,const D3D11_TILED_RESOURCE_COORDINATE *dst_start_coordinate,const D3D11_TILE_REGION_SIZE *dst_region_size,const void *src_data,UINT flags) {
    This->lpVtbl->UpdateTiles(This,dst_resource,dst_start_coordinate,dst_region_size,src_data,flags);
}
static inline HRESULT ID3D11DeviceContext2_ResizeTilePool(ID3D11DeviceContext2* This,ID3D11Buffer *pool,UINT64 size) {
    return This->lpVtbl->ResizeTilePool(This,pool,size);
}
static inline void ID3D11DeviceContext2_TiledResourceBarrier(ID3D11DeviceContext2* This,ID3D11DeviceChild *before_barrier,ID3D11DeviceChild *after_barrier) {
    This->lpVtbl->TiledResourceBarrier(This,before_barrier,after_barrier);
}
static inline WINBOOL ID3D11DeviceContext2_IsAnnotationEnabled(ID3D11DeviceContext2* This) {
    return This->lpVtbl->IsAnnotationEnabled(This);
}
static inline void ID3D11DeviceContext2_SetMarkerInt(ID3D11DeviceContext2* This,const WCHAR *label,int data) {
    This->lpVtbl->SetMarkerInt(This,label,data);
}
static inline void ID3D11DeviceContext2_BeginEventInt(ID3D11DeviceContext2* This,const WCHAR *label,int data) {
    This->lpVtbl->BeginEventInt(This,label,data);
}
static inline void ID3D11DeviceContext2_EndEvent(ID3D11DeviceContext2* This) {
    This->lpVtbl->EndEvent(This);
}
#endif
#endif

#endif


#endif  /* __ID3D11DeviceContext2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D11Device2 interface
 */
#ifndef __ID3D11Device2_INTERFACE_DEFINED__
#define __ID3D11Device2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D11Device2, 0x9d06dffa, 0xd1e5, 0x4d07, 0x83,0xa8, 0x1b,0xb1,0x23,0xf2,0xf8,0x41);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9d06dffa-d1e5-4d07-83a8-1bb123f2f841")
ID3D11Device2 : public ID3D11Device1
{
    virtual void STDMETHODCALLTYPE GetImmediateContext2(
        ID3D11DeviceContext2 **context) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateDeferredContext2(
        UINT flags,
        ID3D11DeviceContext2 **context) = 0;

    virtual void STDMETHODCALLTYPE GetResourceTiling(
        ID3D11Resource *resource,
        UINT *tile_count,
        D3D11_PACKED_MIP_DESC *mip_desc,
        D3D11_TILE_SHAPE *tile_shape,
        UINT *subresource_tiling_count,
        UINT first_subresource_tiling,
        D3D11_SUBRESOURCE_TILING *subresource_tiling) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckMultisampleQualityLevels1(
        DXGI_FORMAT format,
        UINT sample_count,
        UINT flags,
        UINT *quality_level_count) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D11Device2, 0x9d06dffa, 0xd1e5, 0x4d07, 0x83,0xa8, 0x1b,0xb1,0x23,0xf2,0xf8,0x41)
#endif
#else
typedef struct ID3D11Device2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D11Device2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D11Device2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D11Device2 *This);

    /*** ID3D11Device methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateBuffer)(
        ID3D11Device2 *This,
        const D3D11_BUFFER_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Buffer **ppBuffer);

    HRESULT (STDMETHODCALLTYPE *CreateTexture1D)(
        ID3D11Device2 *This,
        const D3D11_TEXTURE1D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture1D **ppTexture1D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture2D)(
        ID3D11Device2 *This,
        const D3D11_TEXTURE2D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture2D **ppTexture2D);

    HRESULT (STDMETHODCALLTYPE *CreateTexture3D)(
        ID3D11Device2 *This,
        const D3D11_TEXTURE3D_DESC *pDesc,
        const D3D11_SUBRESOURCE_DATA *pInitialData,
        ID3D11Texture3D **ppTexture3D);

    HRESULT (STDMETHODCALLTYPE *CreateShaderResourceView)(
        ID3D11Device2 *This,
        ID3D11Resource *pResource,
        const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,
        ID3D11ShaderResourceView **ppSRView);

    HRESULT (STDMETHODCALLTYPE *CreateUnorderedAccessView)(
        ID3D11Device2 *This,
        ID3D11Resource *pResource,
        const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,
        ID3D11UnorderedAccessView **ppUAView);

    HRESULT (STDMETHODCALLTYPE *CreateRenderTargetView)(
        ID3D11Device2 *This,
        ID3D11Resource *pResource,
        const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,
        ID3D11RenderTargetView **ppRTView);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilView)(
        ID3D11Device2 *This,
        ID3D11Resource *pResource,
        const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,
        ID3D11DepthStencilView **ppDepthStencilView);

    HRESULT (STDMETHODCALLTYPE *CreateInputLayout)(
        ID3D11Device2 *This,
        const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,
        UINT NumElements,
        const void *pShaderBytecodeWithInputSignature,
        SIZE_T BytecodeLength,
        ID3D11InputLayout **ppInputLayout);

    HRESULT (STDMETHODCALLTYPE *CreateVertexShader)(
        ID3D11Device2 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11VertexShader **ppVertexShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShader)(
        ID3D11Device2 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreateGeometryShaderWithStreamOutput)(
        ID3D11Device2 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,
        UINT NumEntries,
        const UINT *pBufferStrides,
        UINT NumStrides,
        UINT RasterizedStream,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11GeometryShader **ppGeometryShader);

    HRESULT (STDMETHODCALLTYPE *CreatePixelShader)(
        ID3D11Device2 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11PixelShader **ppPixelShader);

    HRESULT (STDMETHODCALLTYPE *CreateHullShader)(
        ID3D11Device2 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11HullShader **ppHullShader);

    HRESULT (STDMETHODCALLTYPE *CreateDomainShader)(
        ID3D11Device2 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11DomainShader **ppDomainShader);

    HRESULT (STDMETHODCALLTYPE *CreateComputeShader)(
        ID3D11Device2 *This,
        const void *pShaderBytecode,
        SIZE_T BytecodeLength,
        ID3D11ClassLinkage *pClassLinkage,
        ID3D11ComputeShader **ppComputeShader);

    HRESULT (STDMETHODCALLTYPE *CreateClassLinkage)(
        ID3D11Device2 *This,
        ID3D11ClassLinkage **ppLinkage);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState)(
        ID3D11Device2 *This,
        const D3D11_BLEND_DESC *pBlendStateDesc,
        ID3D11BlendState **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateDepthStencilState)(
        ID3D11Device2 *This,
        const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,
        ID3D11DepthStencilState **ppDepthStencilState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState)(
        ID3D11Device2 *This,
        const D3D11_RASTERIZER_DESC *pRasterizerDesc,
        ID3D11RasterizerState **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateSamplerState)(
        ID3D11Device2 *This,
        const D3D11_SAMPLER_DESC *pSamplerDesc,
        ID3D11SamplerState **ppSamplerState);

    HRESULT (STDMETHODCALLTYPE *CreateQuery)(
        ID3D11Device2 *This,
        const D3D11_QUERY_DESC *pQueryDesc,
        ID3D11Query **ppQuery);

    HRESULT (STDMETHODCALLTYPE *CreatePredicate)(
        ID3D11Device2 *This,
        const D3D11_QUERY_DESC *pPredicateDesc,
        ID3D11Predicate **ppPredicate);

    HRESULT (STDMETHODCALLTYPE *CreateCounter)(
        ID3D11Device2 *This,
        const D3D11_COUNTER_DESC *pCounterDesc,
        ID3D11Counter **ppCounter);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext)(
        ID3D11Device2 *This,
        UINT ContextFlags,
        ID3D11DeviceContext **ppDeferredContext);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource)(
        ID3D11Device2 *This,
        HANDLE hResource,
        REFIID ReturnedInterface,
        void **ppResource);

    HRESULT (STDMETHODCALLTYPE *CheckFormatSupport)(
        ID3D11Device2 *This,
        DXGI_FORMAT Format,
        UINT *pFormatSupport);

    HRESULT (STDMETHODCALLTYPE *CheckMultisampleQualityLevels)(
        ID3D11Device2 *This,
        DXGI_FORMAT Format,
        UINT SampleCount,
        UINT *pNumQualityLevels);

    void (STDMETHODCALLTYPE *CheckCounterInfo)(
        ID3D11Device2 *This,
        D3D11_COUNTER_INFO *pCounterInfo);

    HRESULT (STDMETHODCALLTYPE *CheckCounter)(
        ID3D11Device2 *This,
        const D3D11_COUNTER_DESC *pDesc,
        D3D11_COUNTER_TYPE *pType,
        UINT *pActiveCounters,
        LPSTR szName,
        UINT *pNameLength,
        LPSTR szUnits,
        UINT *pUnitsLength,
        LPSTR szDescription,
        UINT *pDescriptionLength);

    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        ID3D11Device2 *This,
        D3D11_FEATURE Feature,
        void *pFeatureSupportData,
        UINT FeatureSupportDataSize);

    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D11Device2 *This,
        REFGUID guid,
        UINT *pDataSize,
        void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D11Device2 *This,
        REFGUID guid,
        UINT DataSize,
        const void *pData);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D11Device2 *This,
        REFGUID guid,
        const IUnknown *pData);

    D3D_FEATURE_LEVEL (STDMETHODCALLTYPE *GetFeatureLevel)(
        ID3D11Device2 *This);

    UINT (STDMETHODCALLTYPE *GetCreationFlags)(
        ID3D11Device2 *This);

    HRESULT (STDMETHODCALLTYPE *GetDeviceRemovedReason)(
        ID3D11Device2 *This);

    void (STDMETHODCALLTYPE *GetImmediateContext)(
        ID3D11Device2 *This,
        ID3D11DeviceContext **ppImmediateContext);

    HRESULT (STDMETHODCALLTYPE *SetExceptionMode)(
        ID3D11Device2 *This,
        UINT RaiseFlags);

    UINT (STDMETHODCALLTYPE *GetExceptionMode)(
        ID3D11Device2 *This);

    /*** ID3D11Device1 methods ***/
    void (STDMETHODCALLTYPE *GetImmediateContext1)(
        ID3D11Device2 *This,
        ID3D11DeviceContext1 **ppImmediateContext);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext1)(
        ID3D11Device2 *This,
        UINT ContextFlags,
        ID3D11DeviceContext1 **ppDeferredContext);

    HRESULT (STDMETHODCALLTYPE *CreateBlendState1)(
        ID3D11Device2 *This,
        const D3D11_BLEND_DESC1 *pBlendStateDesc,
        ID3D11BlendState1 **ppBlendState);

    HRESULT (STDMETHODCALLTYPE *CreateRasterizerState1)(
        ID3D11Device2 *This,
        const D3D11_RASTERIZER_DESC1 *pRasterizerDesc,
        ID3D11RasterizerState1 **ppRasterizerState);

    HRESULT (STDMETHODCALLTYPE *CreateDeviceContextState)(
        ID3D11Device2 *This,
        UINT Flags,
        const D3D_FEATURE_LEVEL *pFeatureLevels,
        UINT FeatureLevels,
        UINT SDKVersion,
        REFIID EmulatedInterface,
        D3D_FEATURE_LEVEL *pChosenFeatureLevel,
        ID3DDeviceContextState **ppContextState);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResource1)(
        ID3D11Device2 *This,
        HANDLE hResource,
        REFIID returnedInterface,
        void **ppResource);

    HRESULT (STDMETHODCALLTYPE *OpenSharedResourceByName)(
        ID3D11Device2 *This,
        LPCWSTR lpName,
        DWORD dwDesiredAccess,
        REFIID returnedInterface,
        void **ppResource);

    /*** ID3D11Device2 methods ***/
    void (STDMETHODCALLTYPE *GetImmediateContext2)(
        ID3D11Device2 *This,
        ID3D11DeviceContext2 **context);

    HRESULT (STDMETHODCALLTYPE *CreateDeferredContext2)(
        ID3D11Device2 *This,
        UINT flags,
        ID3D11DeviceContext2 **context);

    void (STDMETHODCALLTYPE *GetResourceTiling)(
        ID3D11Device2 *This,
        ID3D11Resource *resource,
        UINT *tile_count,
        D3D11_PACKED_MIP_DESC *mip_desc,
        D3D11_TILE_SHAPE *tile_shape,
        UINT *subresource_tiling_count,
        UINT first_subresource_tiling,
        D3D11_SUBRESOURCE_TILING *subresource_tiling);

    HRESULT (STDMETHODCALLTYPE *CheckMultisampleQualityLevels1)(
        ID3D11Device2 *This,
        DXGI_FORMAT format,
        UINT sample_count,
        UINT flags,
        UINT *quality_level_count);

    END_INTERFACE
} ID3D11Device2Vtbl;

interface ID3D11Device2 {
    CONST_VTBL ID3D11Device2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D11Device2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D11Device2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D11Device2_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D11Device methods ***/
#define ID3D11Device2_CreateBuffer(This,pDesc,pInitialData,ppBuffer) (This)->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer)
#define ID3D11Device2_CreateTexture1D(This,pDesc,pInitialData,ppTexture1D) (This)->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D)
#define ID3D11Device2_CreateTexture2D(This,pDesc,pInitialData,ppTexture2D) (This)->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D)
#define ID3D11Device2_CreateTexture3D(This,pDesc,pInitialData,ppTexture3D) (This)->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D)
#define ID3D11Device2_CreateShaderResourceView(This,pResource,pDesc,ppSRView) (This)->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView)
#define ID3D11Device2_CreateUnorderedAccessView(This,pResource,pDesc,ppUAView) (This)->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView)
#define ID3D11Device2_CreateRenderTargetView(This,pResource,pDesc,ppRTView) (This)->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView)
#define ID3D11Device2_CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView) (This)->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView)
#define ID3D11Device2_CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout) (This)->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout)
#define ID3D11Device2_CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader) (This)->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader)
#define ID3D11Device2_CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader)
#define ID3D11Device2_CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader) (This)->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader)
#define ID3D11Device2_CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader) (This)->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader)
#define ID3D11Device2_CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader) (This)->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader)
#define ID3D11Device2_CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader) (This)->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader)
#define ID3D11Device2_CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader) (This)->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader)
#define ID3D11Device2_CreateClassLinkage(This,ppLinkage) (This)->lpVtbl->CreateClassLinkage(This,ppLinkage)
#define ID3D11Device2_CreateBlendState(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState)
#define ID3D11Device2_CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState) (This)->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState)
#define ID3D11Device2_CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState)
#define ID3D11Device2_CreateSamplerState(This,pSamplerDesc,ppSamplerState) (This)->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState)
#define ID3D11Device2_CreateQuery(This,pQueryDesc,ppQuery) (This)->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery)
#define ID3D11Device2_CreatePredicate(This,pPredicateDesc,ppPredicate) (This)->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate)
#define ID3D11Device2_CreateCounter(This,pCounterDesc,ppCounter) (This)->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter)
#define ID3D11Device2_CreateDeferredContext(This,ContextFlags,ppDeferredContext) (This)->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext)
#define ID3D11Device2_OpenSharedResource(This,hResource,ReturnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource)
#define ID3D11Device2_CheckFormatSupport(This,Format,pFormatSupport) (This)->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport)
#define ID3D11Device2_CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels) (This)->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels)
#define ID3D11Device2_CheckCounterInfo(This,pCounterInfo) (This)->lpVtbl->CheckCounterInfo(This,pCounterInfo)
#define ID3D11Device2_CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength) (This)->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength)
#define ID3D11Device2_CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize) (This)->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize)
#define ID3D11Device2_GetPrivateData(This,guid,pDataSize,pData) (This)->lpVtbl->GetPrivateData(This,guid,pDataSize,pData)
#define ID3D11Device2_SetPrivateData(This,guid,DataSize,pData) (This)->lpVtbl->SetPrivateData(This,guid,DataSize,pData)
#define ID3D11Device2_SetPrivateDataInterface(This,guid,pData) (This)->lpVtbl->SetPrivateDataInterface(This,guid,pData)
#define ID3D11Device2_GetFeatureLevel(This) (This)->lpVtbl->GetFeatureLevel(This)
#define ID3D11Device2_GetCreationFlags(This) (This)->lpVtbl->GetCreationFlags(This)
#define ID3D11Device2_GetDeviceRemovedReason(This) (This)->lpVtbl->GetDeviceRemovedReason(This)
#define ID3D11Device2_GetImmediateContext(This,ppImmediateContext) (This)->lpVtbl->GetImmediateContext(This,ppImmediateContext)
#define ID3D11Device2_SetExceptionMode(This,RaiseFlags) (This)->lpVtbl->SetExceptionMode(This,RaiseFlags)
#define ID3D11Device2_GetExceptionMode(This) (This)->lpVtbl->GetExceptionMode(This)
/*** ID3D11Device1 methods ***/
#define ID3D11Device2_GetImmediateContext1(This,ppImmediateContext) (This)->lpVtbl->GetImmediateContext1(This,ppImmediateContext)
#define ID3D11Device2_CreateDeferredContext1(This,ContextFlags,ppDeferredContext) (This)->lpVtbl->CreateDeferredContext1(This,ContextFlags,ppDeferredContext)
#define ID3D11Device2_CreateBlendState1(This,pBlendStateDesc,ppBlendState) (This)->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState)
#define ID3D11Device2_CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState) (This)->lpVtbl->CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState)
#define ID3D11Device2_CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState) (This)->lpVtbl->CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState)
#define ID3D11Device2_OpenSharedResource1(This,hResource,returnedInterface,ppResource) (This)->lpVtbl->OpenSharedResource1(This,hResource,returnedInterface,ppResource)
#define ID3D11Device2_OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource) (This)->lpVtbl->OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource)
/*** ID3D11Device2 methods ***/
#define ID3D11Device2_GetImmediateContext2(This,context) (This)->lpVtbl->GetImmediateContext2(This,context)
#define ID3D11Device2_CreateDeferredContext2(This,flags,context) (This)->lpVtbl->CreateDeferredContext2(This,flags,context)
#define ID3D11Device2_GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling) (This)->lpVtbl->GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling)
#define ID3D11Device2_CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count) (This)->lpVtbl->CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D11Device2_QueryInterface(ID3D11Device2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D11Device2_AddRef(ID3D11Device2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D11Device2_Release(ID3D11Device2* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D11Device methods ***/
static inline HRESULT ID3D11Device2_CreateBuffer(ID3D11Device2* This,const D3D11_BUFFER_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Buffer **ppBuffer) {
    return This->lpVtbl->CreateBuffer(This,pDesc,pInitialData,ppBuffer);
}
static inline HRESULT ID3D11Device2_CreateTexture1D(ID3D11Device2* This,const D3D11_TEXTURE1D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture1D **ppTexture1D) {
    return This->lpVtbl->CreateTexture1D(This,pDesc,pInitialData,ppTexture1D);
}
static inline HRESULT ID3D11Device2_CreateTexture2D(ID3D11Device2* This,const D3D11_TEXTURE2D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture2D **ppTexture2D) {
    return This->lpVtbl->CreateTexture2D(This,pDesc,pInitialData,ppTexture2D);
}
static inline HRESULT ID3D11Device2_CreateTexture3D(ID3D11Device2* This,const D3D11_TEXTURE3D_DESC *pDesc,const D3D11_SUBRESOURCE_DATA *pInitialData,ID3D11Texture3D **ppTexture3D) {
    return This->lpVtbl->CreateTexture3D(This,pDesc,pInitialData,ppTexture3D);
}
static inline HRESULT ID3D11Device2_CreateShaderResourceView(ID3D11Device2* This,ID3D11Resource *pResource,const D3D11_SHADER_RESOURCE_VIEW_DESC *pDesc,ID3D11ShaderResourceView **ppSRView) {
    return This->lpVtbl->CreateShaderResourceView(This,pResource,pDesc,ppSRView);
}
static inline HRESULT ID3D11Device2_CreateUnorderedAccessView(ID3D11Device2* This,ID3D11Resource *pResource,const D3D11_UNORDERED_ACCESS_VIEW_DESC *pDesc,ID3D11UnorderedAccessView **ppUAView) {
    return This->lpVtbl->CreateUnorderedAccessView(This,pResource,pDesc,ppUAView);
}
static inline HRESULT ID3D11Device2_CreateRenderTargetView(ID3D11Device2* This,ID3D11Resource *pResource,const D3D11_RENDER_TARGET_VIEW_DESC *pDesc,ID3D11RenderTargetView **ppRTView) {
    return This->lpVtbl->CreateRenderTargetView(This,pResource,pDesc,ppRTView);
}
static inline HRESULT ID3D11Device2_CreateDepthStencilView(ID3D11Device2* This,ID3D11Resource *pResource,const D3D11_DEPTH_STENCIL_VIEW_DESC *pDesc,ID3D11DepthStencilView **ppDepthStencilView) {
    return This->lpVtbl->CreateDepthStencilView(This,pResource,pDesc,ppDepthStencilView);
}
static inline HRESULT ID3D11Device2_CreateInputLayout(ID3D11Device2* This,const D3D11_INPUT_ELEMENT_DESC *pInputElementDescs,UINT NumElements,const void *pShaderBytecodeWithInputSignature,SIZE_T BytecodeLength,ID3D11InputLayout **ppInputLayout) {
    return This->lpVtbl->CreateInputLayout(This,pInputElementDescs,NumElements,pShaderBytecodeWithInputSignature,BytecodeLength,ppInputLayout);
}
static inline HRESULT ID3D11Device2_CreateVertexShader(ID3D11Device2* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11VertexShader **ppVertexShader) {
    return This->lpVtbl->CreateVertexShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppVertexShader);
}
static inline HRESULT ID3D11Device2_CreateGeometryShader(ID3D11Device2* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppGeometryShader);
}
static inline HRESULT ID3D11Device2_CreateGeometryShaderWithStreamOutput(ID3D11Device2* This,const void *pShaderBytecode,SIZE_T BytecodeLength,const D3D11_SO_DECLARATION_ENTRY *pSODeclaration,UINT NumEntries,const UINT *pBufferStrides,UINT NumStrides,UINT RasterizedStream,ID3D11ClassLinkage *pClassLinkage,ID3D11GeometryShader **ppGeometryShader) {
    return This->lpVtbl->CreateGeometryShaderWithStreamOutput(This,pShaderBytecode,BytecodeLength,pSODeclaration,NumEntries,pBufferStrides,NumStrides,RasterizedStream,pClassLinkage,ppGeometryShader);
}
static inline HRESULT ID3D11Device2_CreatePixelShader(ID3D11Device2* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11PixelShader **ppPixelShader) {
    return This->lpVtbl->CreatePixelShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppPixelShader);
}
static inline HRESULT ID3D11Device2_CreateHullShader(ID3D11Device2* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11HullShader **ppHullShader) {
    return This->lpVtbl->CreateHullShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppHullShader);
}
static inline HRESULT ID3D11Device2_CreateDomainShader(ID3D11Device2* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11DomainShader **ppDomainShader) {
    return This->lpVtbl->CreateDomainShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppDomainShader);
}
static inline HRESULT ID3D11Device2_CreateComputeShader(ID3D11Device2* This,const void *pShaderBytecode,SIZE_T BytecodeLength,ID3D11ClassLinkage *pClassLinkage,ID3D11ComputeShader **ppComputeShader) {
    return This->lpVtbl->CreateComputeShader(This,pShaderBytecode,BytecodeLength,pClassLinkage,ppComputeShader);
}
static inline HRESULT ID3D11Device2_CreateClassLinkage(ID3D11Device2* This,ID3D11ClassLinkage **ppLinkage) {
    return This->lpVtbl->CreateClassLinkage(This,ppLinkage);
}
static inline HRESULT ID3D11Device2_CreateBlendState(ID3D11Device2* This,const D3D11_BLEND_DESC *pBlendStateDesc,ID3D11BlendState **ppBlendState) {
    return This->lpVtbl->CreateBlendState(This,pBlendStateDesc,ppBlendState);
}
static inline HRESULT ID3D11Device2_CreateDepthStencilState(ID3D11Device2* This,const D3D11_DEPTH_STENCIL_DESC *pDepthStencilDesc,ID3D11DepthStencilState **ppDepthStencilState) {
    return This->lpVtbl->CreateDepthStencilState(This,pDepthStencilDesc,ppDepthStencilState);
}
static inline HRESULT ID3D11Device2_CreateRasterizerState(ID3D11Device2* This,const D3D11_RASTERIZER_DESC *pRasterizerDesc,ID3D11RasterizerState **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState(This,pRasterizerDesc,ppRasterizerState);
}
static inline HRESULT ID3D11Device2_CreateSamplerState(ID3D11Device2* This,const D3D11_SAMPLER_DESC *pSamplerDesc,ID3D11SamplerState **ppSamplerState) {
    return This->lpVtbl->CreateSamplerState(This,pSamplerDesc,ppSamplerState);
}
static inline HRESULT ID3D11Device2_CreateQuery(ID3D11Device2* This,const D3D11_QUERY_DESC *pQueryDesc,ID3D11Query **ppQuery) {
    return This->lpVtbl->CreateQuery(This,pQueryDesc,ppQuery);
}
static inline HRESULT ID3D11Device2_CreatePredicate(ID3D11Device2* This,const D3D11_QUERY_DESC *pPredicateDesc,ID3D11Predicate **ppPredicate) {
    return This->lpVtbl->CreatePredicate(This,pPredicateDesc,ppPredicate);
}
static inline HRESULT ID3D11Device2_CreateCounter(ID3D11Device2* This,const D3D11_COUNTER_DESC *pCounterDesc,ID3D11Counter **ppCounter) {
    return This->lpVtbl->CreateCounter(This,pCounterDesc,ppCounter);
}
static inline HRESULT ID3D11Device2_CreateDeferredContext(ID3D11Device2* This,UINT ContextFlags,ID3D11DeviceContext **ppDeferredContext) {
    return This->lpVtbl->CreateDeferredContext(This,ContextFlags,ppDeferredContext);
}
static inline HRESULT ID3D11Device2_OpenSharedResource(ID3D11Device2* This,HANDLE hResource,REFIID ReturnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource(This,hResource,ReturnedInterface,ppResource);
}
static inline HRESULT ID3D11Device2_CheckFormatSupport(ID3D11Device2* This,DXGI_FORMAT Format,UINT *pFormatSupport) {
    return This->lpVtbl->CheckFormatSupport(This,Format,pFormatSupport);
}
static inline HRESULT ID3D11Device2_CheckMultisampleQualityLevels(ID3D11Device2* This,DXGI_FORMAT Format,UINT SampleCount,UINT *pNumQualityLevels) {
    return This->lpVtbl->CheckMultisampleQualityLevels(This,Format,SampleCount,pNumQualityLevels);
}
static inline void ID3D11Device2_CheckCounterInfo(ID3D11Device2* This,D3D11_COUNTER_INFO *pCounterInfo) {
    This->lpVtbl->CheckCounterInfo(This,pCounterInfo);
}
static inline HRESULT ID3D11Device2_CheckCounter(ID3D11Device2* This,const D3D11_COUNTER_DESC *pDesc,D3D11_COUNTER_TYPE *pType,UINT *pActiveCounters,LPSTR szName,UINT *pNameLength,LPSTR szUnits,UINT *pUnitsLength,LPSTR szDescription,UINT *pDescriptionLength) {
    return This->lpVtbl->CheckCounter(This,pDesc,pType,pActiveCounters,szName,pNameLength,szUnits,pUnitsLength,szDescription,pDescriptionLength);
}
static inline HRESULT ID3D11Device2_CheckFeatureSupport(ID3D11Device2* This,D3D11_FEATURE Feature,void *pFeatureSupportData,UINT FeatureSupportDataSize) {
    return This->lpVtbl->CheckFeatureSupport(This,Feature,pFeatureSupportData,FeatureSupportDataSize);
}
static inline HRESULT ID3D11Device2_GetPrivateData(ID3D11Device2* This,REFGUID guid,UINT *pDataSize,void *pData) {
    return This->lpVtbl->GetPrivateData(This,guid,pDataSize,pData);
}
static inline HRESULT ID3D11Device2_SetPrivateData(ID3D11Device2* This,REFGUID guid,UINT DataSize,const void *pData) {
    return This->lpVtbl->SetPrivateData(This,guid,DataSize,pData);
}
static inline HRESULT ID3D11Device2_SetPrivateDataInterface(ID3D11Device2* This,REFGUID guid,const IUnknown *pData) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,pData);
}
static inline D3D_FEATURE_LEVEL ID3D11Device2_GetFeatureLevel(ID3D11Device2* This) {
    return This->lpVtbl->GetFeatureLevel(This);
}
static inline UINT ID3D11Device2_GetCreationFlags(ID3D11Device2* This) {
    return This->lpVtbl->GetCreationFlags(This);
}
static inline HRESULT ID3D11Device2_GetDeviceRemovedReason(ID3D11Device2* This) {
    return This->lpVtbl->GetDeviceRemovedReason(This);
}
static inline void ID3D11Device2_GetImmediateContext(ID3D11Device2* This,ID3D11DeviceContext **ppImmediateContext) {
    This->lpVtbl->GetImmediateContext(This,ppImmediateContext);
}
static inline HRESULT ID3D11Device2_SetExceptionMode(ID3D11Device2* This,UINT RaiseFlags) {
    return This->lpVtbl->SetExceptionMode(This,RaiseFlags);
}
static inline UINT ID3D11Device2_GetExceptionMode(ID3D11Device2* This) {
    return This->lpVtbl->GetExceptionMode(This);
}
/*** ID3D11Device1 methods ***/
static inline void ID3D11Device2_GetImmediateContext1(ID3D11Device2* This,ID3D11DeviceContext1 **ppImmediateContext) {
    This->lpVtbl->GetImmediateContext1(This,ppImmediateContext);
}
static inline HRESULT ID3D11Device2_CreateDeferredContext1(ID3D11Device2* This,UINT ContextFlags,ID3D11DeviceContext1 **ppDeferredContext) {
    return This->lpVtbl->CreateDeferredContext1(This,ContextFlags,ppDeferredContext);
}
static inline HRESULT ID3D11Device2_CreateBlendState1(ID3D11Device2* This,const D3D11_BLEND_DESC1 *pBlendStateDesc,ID3D11BlendState1 **ppBlendState) {
    return This->lpVtbl->CreateBlendState1(This,pBlendStateDesc,ppBlendState);
}
static inline HRESULT ID3D11Device2_CreateRasterizerState1(ID3D11Device2* This,const D3D11_RASTERIZER_DESC1 *pRasterizerDesc,ID3D11RasterizerState1 **ppRasterizerState) {
    return This->lpVtbl->CreateRasterizerState1(This,pRasterizerDesc,ppRasterizerState);
}
static inline HRESULT ID3D11Device2_CreateDeviceContextState(ID3D11Device2* This,UINT Flags,const D3D_FEATURE_LEVEL *pFeatureLevels,UINT FeatureLevels,UINT SDKVersion,REFIID EmulatedInterface,D3D_FEATURE_LEVEL *pChosenFeatureLevel,ID3DDeviceContextState **ppContextState) {
    return This->lpVtbl->CreateDeviceContextState(This,Flags,pFeatureLevels,FeatureLevels,SDKVersion,EmulatedInterface,pChosenFeatureLevel,ppContextState);
}
static inline HRESULT ID3D11Device2_OpenSharedResource1(ID3D11Device2* This,HANDLE hResource,REFIID returnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResource1(This,hResource,returnedInterface,ppResource);
}
static inline HRESULT ID3D11Device2_OpenSharedResourceByName(ID3D11Device2* This,LPCWSTR lpName,DWORD dwDesiredAccess,REFIID returnedInterface,void **ppResource) {
    return This->lpVtbl->OpenSharedResourceByName(This,lpName,dwDesiredAccess,returnedInterface,ppResource);
}
/*** ID3D11Device2 methods ***/
static inline void ID3D11Device2_GetImmediateContext2(ID3D11Device2* This,ID3D11DeviceContext2 **context) {
    This->lpVtbl->GetImmediateContext2(This,context);
}
static inline HRESULT ID3D11Device2_CreateDeferredContext2(ID3D11Device2* This,UINT flags,ID3D11DeviceContext2 **context) {
    return This->lpVtbl->CreateDeferredContext2(This,flags,context);
}
static inline void ID3D11Device2_GetResourceTiling(ID3D11Device2* This,ID3D11Resource *resource,UINT *tile_count,D3D11_PACKED_MIP_DESC *mip_desc,D3D11_TILE_SHAPE *tile_shape,UINT *subresource_tiling_count,UINT first_subresource_tiling,D3D11_SUBRESOURCE_TILING *subresource_tiling) {
    This->lpVtbl->GetResourceTiling(This,resource,tile_count,mip_desc,tile_shape,subresource_tiling_count,first_subresource_tiling,subresource_tiling);
}
static inline HRESULT ID3D11Device2_CheckMultisampleQualityLevels1(ID3D11Device2* This,DXGI_FORMAT format,UINT sample_count,UINT flags,UINT *quality_level_count) {
    return This->lpVtbl->CheckMultisampleQualityLevels1(This,format,sample_count,flags,quality_level_count);
}
#endif
#endif

#endif


#endif  /* __ID3D11Device2_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __d3d11_2_h__ */
