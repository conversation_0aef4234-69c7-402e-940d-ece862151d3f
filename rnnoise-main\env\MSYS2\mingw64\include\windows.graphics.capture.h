/*** Autogenerated by WIDL 10.12 from include/windows.graphics.capture.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_graphics_capture_h__
#define __windows_graphics_capture_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame ABI::Windows::Graphics::Capture::IDirect3D11CaptureFrame
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IDirect3D11CaptureFrame;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 ABI::Windows::Graphics::Capture::IDirect3D11CaptureFrame2
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IDirect3D11CaptureFrame2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool ABI::Windows::Graphics::Capture::IDirect3D11CaptureFramePool
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IDirect3D11CaptureFramePool;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics ABI::Windows::Graphics::Capture::IDirect3D11CaptureFramePoolStatics
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IDirect3D11CaptureFramePoolStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 ABI::Windows::Graphics::Capture::IDirect3D11CaptureFramePoolStatics2
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IDirect3D11CaptureFramePoolStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem ABI::Windows::Graphics::Capture::IGraphicsCaptureItem
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureItem;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics ABI::Windows::Graphics::Capture::IGraphicsCaptureItemStatics
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureItemStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 ABI::Windows::Graphics::Capture::IGraphicsCaptureItemStatics2
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureItemStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession ABI::Windows::Graphics::Capture::IGraphicsCaptureSession
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureSession;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 ABI::Windows::Graphics::Capture::IGraphicsCaptureSession2
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureSession2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 ABI::Windows::Graphics::Capture::IGraphicsCaptureSession3
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureSession3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics ABI::Windows::Graphics::Capture::IGraphicsCaptureSessionStatics
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureSessionStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CDirect3D11CaptureFrame_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CDirect3D11CaptureFrame_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                class Direct3D11CaptureFrame;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CDirect3D11CaptureFrame __x_ABI_CWindows_CGraphics_CCapture_CDirect3D11CaptureFrame;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGraphics_CCapture_CDirect3D11CaptureFrame_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CDirect3D11CaptureFramePool_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CDirect3D11CaptureFramePool_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                class Direct3D11CaptureFramePool;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CDirect3D11CaptureFramePool __x_ABI_CWindows_CGraphics_CCapture_CDirect3D11CaptureFramePool;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGraphics_CCapture_CDirect3D11CaptureFramePool_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureItem_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureItem_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                class GraphicsCaptureItem;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureItem __x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureItem;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureItem_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureSession_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureSession_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                class GraphicsCaptureSession;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureSession __x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureSession;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureSession_FWD_DEFINED__ */

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Graphics::Capture::Direct3D11CaptureFramePool*,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem*,IInspectable* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.graphics.h>
#include <windows.graphics.directx.h>
#include <windows.graphics.directx.direct3d11.h>
#include <windows.system.h>
#include <windows.ui.h>
#include <windows.ui.composition.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x130000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureDirtyRegionMode_ENUM_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureDirtyRegionMode_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                enum GraphicsCaptureDirtyRegionMode {
                    GraphicsCaptureDirtyRegionMode_ReportOnly = 0,
                    GraphicsCaptureDirtyRegionMode_ReportAndRender = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureDirtyRegionMode {
    GraphicsCaptureDirtyRegionMode_ReportOnly = 0,
    GraphicsCaptureDirtyRegionMode_ReportAndRender = 1
};
#ifdef WIDL_using_Windows_Graphics_Capture
#define GraphicsCaptureDirtyRegionMode __x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureDirtyRegionMode
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif /* ____x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureDirtyRegionMode_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x130000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureDirtyRegionMode __x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureDirtyRegionMode;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame ABI::Windows::Graphics::Capture::IDirect3D11CaptureFrame
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IDirect3D11CaptureFrame;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 ABI::Windows::Graphics::Capture::IDirect3D11CaptureFrame2
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IDirect3D11CaptureFrame2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool ABI::Windows::Graphics::Capture::IDirect3D11CaptureFramePool
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IDirect3D11CaptureFramePool;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics ABI::Windows::Graphics::Capture::IDirect3D11CaptureFramePoolStatics
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IDirect3D11CaptureFramePoolStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 ABI::Windows::Graphics::Capture::IDirect3D11CaptureFramePoolStatics2
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IDirect3D11CaptureFramePoolStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem ABI::Windows::Graphics::Capture::IGraphicsCaptureItem
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureItem;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics ABI::Windows::Graphics::Capture::IGraphicsCaptureItemStatics
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureItemStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 ABI::Windows::Graphics::Capture::IGraphicsCaptureItemStatics2
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureItemStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession ABI::Windows::Graphics::Capture::IGraphicsCaptureSession
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureSession;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 ABI::Windows::Graphics::Capture::IGraphicsCaptureSession2
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureSession2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 ABI::Windows::Graphics::Capture::IGraphicsCaptureSession3
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureSession3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics ABI::Windows::Graphics::Capture::IGraphicsCaptureSessionStatics
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                interface IGraphicsCaptureSessionStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IDirect3D11CaptureFrame interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame, 0xfa50c623, 0x38da, 0x4b32, 0xac,0xf3, 0xfa,0x97,0x34,0xad,0x80,0x0e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("fa50c623-38da-4b32-acf3-fa9734ad800e")
                IDirect3D11CaptureFrame : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Surface(
                        ABI::Windows::Graphics::DirectX::Direct3D11::IDirect3DSurface **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SystemRelativeTime(
                        ABI::Windows::Foundation::TimeSpan *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ContentSize(
                        ABI::Windows::Graphics::SizeInt32 *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame, 0xfa50c623, 0x38da, 0x4b32, 0xac,0xf3, 0xfa,0x97,0x34,0xad,0x80,0x0e)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrameVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame *This,
        TrustLevel *trustLevel);

    /*** IDirect3D11CaptureFrame methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Surface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DSurface **value);

    HRESULT (STDMETHODCALLTYPE *get_SystemRelativeTime)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *get_ContentSize)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame *This,
        __x_ABI_CWindows_CGraphics_CSizeInt32 *value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrameVtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrameVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDirect3D11CaptureFrame methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_get_Surface(This,value) (This)->lpVtbl->get_Surface(This,value)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_get_SystemRelativeTime(This,value) (This)->lpVtbl->get_SystemRelativeTime(This,value)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_get_ContentSize(This,value) (This)->lpVtbl->get_ContentSize(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_Release(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDirect3D11CaptureFrame methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_get_Surface(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DSurface **value) {
    return This->lpVtbl->get_Surface(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_get_SystemRelativeTime(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame* This,__x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_SystemRelativeTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_get_ContentSize(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame* This,__x_ABI_CWindows_CGraphics_CSizeInt32 *value) {
    return This->lpVtbl->get_ContentSize(This,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IDirect3D11CaptureFrame IID___x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame
#define IDirect3D11CaptureFrameVtbl __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrameVtbl
#define IDirect3D11CaptureFrame __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame
#define IDirect3D11CaptureFrame_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_QueryInterface
#define IDirect3D11CaptureFrame_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_AddRef
#define IDirect3D11CaptureFrame_Release __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_Release
#define IDirect3D11CaptureFrame_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_GetIids
#define IDirect3D11CaptureFrame_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_GetRuntimeClassName
#define IDirect3D11CaptureFrame_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_GetTrustLevel
#define IDirect3D11CaptureFrame_get_Surface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_get_Surface
#define IDirect3D11CaptureFrame_get_SystemRelativeTime __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_get_SystemRelativeTime
#define IDirect3D11CaptureFrame_get_ContentSize __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_get_ContentSize
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * IDirect3D11CaptureFrame2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x130000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2, 0x37869cfa, 0x2b48, 0x5ebf, 0x9a,0xfb, 0xdf,0xfd,0x80,0x5d,0xef,0xdb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("37869cfa-2b48-5ebf-9afb-dffd805defdb")
                IDirect3D11CaptureFrame2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DirtyRegions(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Graphics::RectInt32 > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_DirtyRegionMode(
                        ABI::Windows::Graphics::Capture::GraphicsCaptureDirtyRegionMode *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2, 0x37869cfa, 0x2b48, 0x5ebf, 0x9a,0xfb, 0xdf,0xfd,0x80,0x5d,0xef,0xdb)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 *This,
        TrustLevel *trustLevel);

    /*** IDirect3D11CaptureFrame2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DirtyRegions)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 *This,
        __FIVectorView_1_RectInt32 **value);

    HRESULT (STDMETHODCALLTYPE *get_DirtyRegionMode)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 *This,
        __x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureDirtyRegionMode *value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2Vtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2 {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDirect3D11CaptureFrame2 methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_get_DirtyRegions(This,value) (This)->lpVtbl->get_DirtyRegions(This,value)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_get_DirtyRegionMode(This,value) (This)->lpVtbl->get_DirtyRegionMode(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_Release(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDirect3D11CaptureFrame2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_get_DirtyRegions(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2* This,__FIVectorView_1_RectInt32 **value) {
    return This->lpVtbl->get_DirtyRegions(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_get_DirtyRegionMode(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2* This,__x_ABI_CWindows_CGraphics_CCapture_CGraphicsCaptureDirtyRegionMode *value) {
    return This->lpVtbl->get_DirtyRegionMode(This,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IDirect3D11CaptureFrame2 IID___x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2
#define IDirect3D11CaptureFrame2Vtbl __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2Vtbl
#define IDirect3D11CaptureFrame2 __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2
#define IDirect3D11CaptureFrame2_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_QueryInterface
#define IDirect3D11CaptureFrame2_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_AddRef
#define IDirect3D11CaptureFrame2_Release __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_Release
#define IDirect3D11CaptureFrame2_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_GetIids
#define IDirect3D11CaptureFrame2_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_GetRuntimeClassName
#define IDirect3D11CaptureFrame2_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_GetTrustLevel
#define IDirect3D11CaptureFrame2_get_DirtyRegions __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_get_DirtyRegions
#define IDirect3D11CaptureFrame2_get_DirtyRegionMode __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_get_DirtyRegionMode
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x130000 */

/*****************************************************************************
 * IDirect3D11CaptureFramePool interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool, 0x24eb6d22, 0x1975, 0x422e, 0x82,0xe7, 0x78,0x0d,0xbd,0x8d,0xdf,0x24);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("24eb6d22-1975-422e-82e7-780dbd8ddf24")
                IDirect3D11CaptureFramePool : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Recreate(
                        ABI::Windows::Graphics::DirectX::Direct3D11::IDirect3DDevice *device,
                        ABI::Windows::Graphics::DirectX::DirectXPixelFormat pixel_format,
                        INT32 number_of_buffers,
                        ABI::Windows::Graphics::SizeInt32 size) = 0;

                    virtual HRESULT STDMETHODCALLTYPE TryGetNextFrame(
                        ABI::Windows::Graphics::Capture::IDirect3D11CaptureFrame **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_FrameArrived(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Graphics::Capture::Direct3D11CaptureFramePool*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_FrameArrived(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateCaptureSession(
                        ABI::Windows::Graphics::Capture::IGraphicsCaptureItem *item,
                        ABI::Windows::Graphics::Capture::IGraphicsCaptureSession **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_DispatcherQueue(
                        ABI::Windows::System::IDispatcherQueue **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool, 0x24eb6d22, 0x1975, 0x422e, 0x82,0xe7, 0x78,0x0d,0xbd,0x8d,0xdf,0x24)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This,
        TrustLevel *trustLevel);

    /*** IDirect3D11CaptureFramePool methods ***/
    HRESULT (STDMETHODCALLTYPE *Recreate)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DDevice *device,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat pixel_format,
        INT32 number_of_buffers,
        __x_ABI_CWindows_CGraphics_CSizeInt32 size);

    HRESULT (STDMETHODCALLTYPE *TryGetNextFrame)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This,
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame **result);

    HRESULT (STDMETHODCALLTYPE *add_FrameArrived)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This,
        __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_FrameArrived)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *CreateCaptureSession)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This,
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *item,
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession **result);

    HRESULT (STDMETHODCALLTYPE *get_DispatcherQueue)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *This,
        __x_ABI_CWindows_CSystem_CIDispatcherQueue **value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolVtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDirect3D11CaptureFramePool methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_Recreate(This,device,pixel_format,number_of_buffers,size) (This)->lpVtbl->Recreate(This,device,pixel_format,number_of_buffers,size)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_TryGetNextFrame(This,result) (This)->lpVtbl->TryGetNextFrame(This,result)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_add_FrameArrived(This,handler,token) (This)->lpVtbl->add_FrameArrived(This,handler,token)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_remove_FrameArrived(This,token) (This)->lpVtbl->remove_FrameArrived(This,token)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_CreateCaptureSession(This,item,result) (This)->lpVtbl->CreateCaptureSession(This,item,result)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_get_DispatcherQueue(This,value) (This)->lpVtbl->get_DispatcherQueue(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_Release(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDirect3D11CaptureFramePool methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_Recreate(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DDevice *device,__x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat pixel_format,INT32 number_of_buffers,__x_ABI_CWindows_CGraphics_CSizeInt32 size) {
    return This->lpVtbl->Recreate(This,device,pixel_format,number_of_buffers,size);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_TryGetNextFrame(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This,__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFrame **result) {
    return This->lpVtbl->TryGetNextFrame(This,result);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_add_FrameArrived(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This,__FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_FrameArrived(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_remove_FrameArrived(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_FrameArrived(This,token);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_CreateCaptureSession(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This,__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *item,__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession **result) {
    return This->lpVtbl->CreateCaptureSession(This,item,result);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_get_DispatcherQueue(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool* This,__x_ABI_CWindows_CSystem_CIDispatcherQueue **value) {
    return This->lpVtbl->get_DispatcherQueue(This,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IDirect3D11CaptureFramePool IID___x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool
#define IDirect3D11CaptureFramePoolVtbl __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolVtbl
#define IDirect3D11CaptureFramePool __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool
#define IDirect3D11CaptureFramePool_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_QueryInterface
#define IDirect3D11CaptureFramePool_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_AddRef
#define IDirect3D11CaptureFramePool_Release __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_Release
#define IDirect3D11CaptureFramePool_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_GetIids
#define IDirect3D11CaptureFramePool_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_GetRuntimeClassName
#define IDirect3D11CaptureFramePool_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_GetTrustLevel
#define IDirect3D11CaptureFramePool_Recreate __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_Recreate
#define IDirect3D11CaptureFramePool_TryGetNextFrame __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_TryGetNextFrame
#define IDirect3D11CaptureFramePool_add_FrameArrived __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_add_FrameArrived
#define IDirect3D11CaptureFramePool_remove_FrameArrived __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_remove_FrameArrived
#define IDirect3D11CaptureFramePool_CreateCaptureSession __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_CreateCaptureSession
#define IDirect3D11CaptureFramePool_get_DispatcherQueue __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_get_DispatcherQueue
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * IDirect3D11CaptureFramePoolStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics, 0x7784056a, 0x67aa, 0x4d53, 0xae,0x54, 0x10,0x88,0xd5,0xa8,0xca,0x21);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("7784056a-67aa-4d53-ae54-1088d5a8ca21")
                IDirect3D11CaptureFramePoolStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Create(
                        ABI::Windows::Graphics::DirectX::Direct3D11::IDirect3DDevice *device,
                        ABI::Windows::Graphics::DirectX::DirectXPixelFormat pixel_format,
                        INT32 number_of_buffers,
                        ABI::Windows::Graphics::SizeInt32 size,
                        ABI::Windows::Graphics::Capture::IDirect3D11CaptureFramePool **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics, 0x7784056a, 0x67aa, 0x4d53, 0xae,0x54, 0x10,0x88,0xd5,0xa8,0xca,0x21)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics *This,
        TrustLevel *trustLevel);

    /*** IDirect3D11CaptureFramePoolStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *Create)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DDevice *device,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat pixel_format,
        INT32 number_of_buffers,
        __x_ABI_CWindows_CGraphics_CSizeInt32 size,
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool **result);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStaticsVtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDirect3D11CaptureFramePoolStatics methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_Create(This,device,pixel_format,number_of_buffers,size,result) (This)->lpVtbl->Create(This,device,pixel_format,number_of_buffers,size,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_Release(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDirect3D11CaptureFramePoolStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_Create(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DDevice *device,__x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat pixel_format,INT32 number_of_buffers,__x_ABI_CWindows_CGraphics_CSizeInt32 size,__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool **result) {
    return This->lpVtbl->Create(This,device,pixel_format,number_of_buffers,size,result);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IDirect3D11CaptureFramePoolStatics IID___x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics
#define IDirect3D11CaptureFramePoolStaticsVtbl __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStaticsVtbl
#define IDirect3D11CaptureFramePoolStatics __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics
#define IDirect3D11CaptureFramePoolStatics_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_QueryInterface
#define IDirect3D11CaptureFramePoolStatics_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_AddRef
#define IDirect3D11CaptureFramePoolStatics_Release __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_Release
#define IDirect3D11CaptureFramePoolStatics_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_GetIids
#define IDirect3D11CaptureFramePoolStatics_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_GetRuntimeClassName
#define IDirect3D11CaptureFramePoolStatics_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_GetTrustLevel
#define IDirect3D11CaptureFramePoolStatics_Create __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_Create
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * IDirect3D11CaptureFramePoolStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2, 0x589b103f, 0x6bbc, 0x5df5, 0xa9,0x91, 0x02,0xe2,0x8b,0x3b,0x66,0xd5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("589b103f-6bbc-5df5-a991-02e28b3b66d5")
                IDirect3D11CaptureFramePoolStatics2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateFreeThreaded(
                        ABI::Windows::Graphics::DirectX::Direct3D11::IDirect3DDevice *device,
                        ABI::Windows::Graphics::DirectX::DirectXPixelFormat pixel_format,
                        INT32 number_of_buffers,
                        ABI::Windows::Graphics::SizeInt32 size,
                        ABI::Windows::Graphics::Capture::IDirect3D11CaptureFramePool **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2, 0x589b103f, 0x6bbc, 0x5df5, 0xa9,0x91, 0x02,0xe2,0x8b,0x3b,0x66,0xd5)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 *This,
        TrustLevel *trustLevel);

    /*** IDirect3D11CaptureFramePoolStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateFreeThreaded)(
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 *This,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DDevice *device,
        __x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat pixel_format,
        INT32 number_of_buffers,
        __x_ABI_CWindows_CGraphics_CSizeInt32 size,
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool **result);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2Vtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2 {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDirect3D11CaptureFramePoolStatics2 methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_CreateFreeThreaded(This,device,pixel_format,number_of_buffers,size,result) (This)->lpVtbl->CreateFreeThreaded(This,device,pixel_format,number_of_buffers,size,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_Release(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDirect3D11CaptureFramePoolStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_CreateFreeThreaded(__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2* This,__x_ABI_CWindows_CGraphics_CDirectX_CDirect3D11_CIDirect3DDevice *device,__x_ABI_CWindows_CGraphics_CDirectX_CDirectXPixelFormat pixel_format,INT32 number_of_buffers,__x_ABI_CWindows_CGraphics_CSizeInt32 size,__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool **result) {
    return This->lpVtbl->CreateFreeThreaded(This,device,pixel_format,number_of_buffers,size,result);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IDirect3D11CaptureFramePoolStatics2 IID___x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2
#define IDirect3D11CaptureFramePoolStatics2Vtbl __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2Vtbl
#define IDirect3D11CaptureFramePoolStatics2 __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2
#define IDirect3D11CaptureFramePoolStatics2_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_QueryInterface
#define IDirect3D11CaptureFramePoolStatics2_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_AddRef
#define IDirect3D11CaptureFramePoolStatics2_Release __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_Release
#define IDirect3D11CaptureFramePoolStatics2_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_GetIids
#define IDirect3D11CaptureFramePoolStatics2_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_GetRuntimeClassName
#define IDirect3D11CaptureFramePoolStatics2_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_GetTrustLevel
#define IDirect3D11CaptureFramePoolStatics2_CreateFreeThreaded __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_CreateFreeThreaded
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePoolStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000 */

/*****************************************************************************
 * IGraphicsCaptureItem interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem, 0x79c3f95b, 0x31f7, 0x4ec2, 0xa4,0x64, 0x63,0x2e,0xf5,0xd3,0x07,0x60);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("79c3f95b-31f7-4ec2-a464-632ef5d30760")
                IGraphicsCaptureItem : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DisplayName(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Size(
                        ABI::Windows::Graphics::SizeInt32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Closed(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem*,IInspectable* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Closed(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem, 0x79c3f95b, 0x31f7, 0x4ec2, 0xa4,0x64, 0x63,0x2e,0xf5,0xd3,0x07,0x60)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *This,
        TrustLevel *trustLevel);

    /*** IGraphicsCaptureItem methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DisplayName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *This,
        __x_ABI_CWindows_CGraphics_CSizeInt32 *value);

    HRESULT (STDMETHODCALLTYPE *add_Closed)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *This,
        __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Closed)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemVtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGraphicsCaptureItem methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_get_DisplayName(This,value) (This)->lpVtbl->get_DisplayName(This,value)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_add_Closed(This,handler,token) (This)->lpVtbl->add_Closed(This,handler,token)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_remove_Closed(This,token) (This)->lpVtbl->remove_Closed(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_Release(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGraphicsCaptureItem methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_get_DisplayName(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem* This,HSTRING *value) {
    return This->lpVtbl->get_DisplayName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_get_Size(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem* This,__x_ABI_CWindows_CGraphics_CSizeInt32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_add_Closed(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem* This,__FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Closed(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_remove_Closed(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Closed(This,token);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IGraphicsCaptureItem IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem
#define IGraphicsCaptureItemVtbl __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemVtbl
#define IGraphicsCaptureItem __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem
#define IGraphicsCaptureItem_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_QueryInterface
#define IGraphicsCaptureItem_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_AddRef
#define IGraphicsCaptureItem_Release __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_Release
#define IGraphicsCaptureItem_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_GetIids
#define IGraphicsCaptureItem_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_GetRuntimeClassName
#define IGraphicsCaptureItem_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_GetTrustLevel
#define IGraphicsCaptureItem_get_DisplayName __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_get_DisplayName
#define IGraphicsCaptureItem_get_Size __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_get_Size
#define IGraphicsCaptureItem_add_Closed __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_add_Closed
#define IGraphicsCaptureItem_remove_Closed __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_remove_Closed
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * IGraphicsCaptureItemStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics, 0xa87ebea5, 0x457c, 0x5788, 0xab,0x47, 0x0c,0xf1,0xd3,0x63,0x7e,0x74);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("a87ebea5-457c-5788-ab47-0cf1d3637e74")
                IGraphicsCaptureItemStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE CreateFromVisual(
                        ABI::Windows::UI::Composition::IVisual *visual,
                        ABI::Windows::Graphics::Capture::IGraphicsCaptureItem **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics, 0xa87ebea5, 0x457c, 0x5788, 0xab,0x47, 0x0c,0xf1,0xd3,0x63,0x7e,0x74)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics *This,
        TrustLevel *trustLevel);

    /*** IGraphicsCaptureItemStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateFromVisual)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics *This,
        __x_ABI_CWindows_CUI_CComposition_CIVisual *visual,
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem **result);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStaticsVtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGraphicsCaptureItemStatics methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_CreateFromVisual(This,visual,result) (This)->lpVtbl->CreateFromVisual(This,visual,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_Release(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGraphicsCaptureItemStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_CreateFromVisual(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics* This,__x_ABI_CWindows_CUI_CComposition_CIVisual *visual,__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem **result) {
    return This->lpVtbl->CreateFromVisual(This,visual,result);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IGraphicsCaptureItemStatics IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics
#define IGraphicsCaptureItemStaticsVtbl __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStaticsVtbl
#define IGraphicsCaptureItemStatics __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics
#define IGraphicsCaptureItemStatics_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_QueryInterface
#define IGraphicsCaptureItemStatics_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_AddRef
#define IGraphicsCaptureItemStatics_Release __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_Release
#define IGraphicsCaptureItemStatics_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_GetIids
#define IGraphicsCaptureItemStatics_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_GetRuntimeClassName
#define IGraphicsCaptureItemStatics_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_GetTrustLevel
#define IGraphicsCaptureItemStatics_CreateFromVisual __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_CreateFromVisual
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x70000 */

/*****************************************************************************
 * IGraphicsCaptureItemStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2, 0x3b92acc9, 0xe584, 0x5862, 0xbf,0x5c, 0x9c,0x31,0x6c,0x6d,0x2d,0xbb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("3b92acc9-e584-5862-bf5c-9c316c6d2dbb")
                IGraphicsCaptureItemStatics2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE TryCreateFromWindowId(
                        ABI::Windows::UI::WindowId window_id,
                        ABI::Windows::Graphics::Capture::IGraphicsCaptureItem **result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE TryCreateFromDisplayId(
                        ABI::Windows::Graphics::DisplayId display_id,
                        ABI::Windows::Graphics::Capture::IGraphicsCaptureItem **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2, 0x3b92acc9, 0xe584, 0x5862, 0xbf,0x5c, 0x9c,0x31,0x6c,0x6d,0x2d,0xbb)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 *This,
        TrustLevel *trustLevel);

    /*** IGraphicsCaptureItemStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *TryCreateFromWindowId)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 *This,
        __x_ABI_CWindows_CUI_CWindowId window_id,
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem **result);

    HRESULT (STDMETHODCALLTYPE *TryCreateFromDisplayId)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 *This,
        __x_ABI_CWindows_CGraphics_CDisplayId display_id,
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem **result);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2Vtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2 {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGraphicsCaptureItemStatics2 methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_TryCreateFromWindowId(This,window_id,result) (This)->lpVtbl->TryCreateFromWindowId(This,window_id,result)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_TryCreateFromDisplayId(This,display_id,result) (This)->lpVtbl->TryCreateFromDisplayId(This,display_id,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_Release(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGraphicsCaptureItemStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_TryCreateFromWindowId(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2* This,__x_ABI_CWindows_CUI_CWindowId window_id,__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem **result) {
    return This->lpVtbl->TryCreateFromWindowId(This,window_id,result);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_TryCreateFromDisplayId(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2* This,__x_ABI_CWindows_CGraphics_CDisplayId display_id,__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem **result) {
    return This->lpVtbl->TryCreateFromDisplayId(This,display_id,result);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IGraphicsCaptureItemStatics2 IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2
#define IGraphicsCaptureItemStatics2Vtbl __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2Vtbl
#define IGraphicsCaptureItemStatics2 __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2
#define IGraphicsCaptureItemStatics2_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_QueryInterface
#define IGraphicsCaptureItemStatics2_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_AddRef
#define IGraphicsCaptureItemStatics2_Release __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_Release
#define IGraphicsCaptureItemStatics2_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_GetIids
#define IGraphicsCaptureItemStatics2_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_GetRuntimeClassName
#define IGraphicsCaptureItemStatics2_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_GetTrustLevel
#define IGraphicsCaptureItemStatics2_TryCreateFromWindowId __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_TryCreateFromWindowId
#define IGraphicsCaptureItemStatics2_TryCreateFromDisplayId __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_TryCreateFromDisplayId
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItemStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000 */

/*****************************************************************************
 * IGraphicsCaptureSession interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession, 0x814e42a9, 0xf70f, 0x4ad7, 0x93,0x9b, 0xfd,0xdc,0xc6,0xeb,0x88,0x0d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("814e42a9-f70f-4ad7-939b-fddcc6eb880d")
                IGraphicsCaptureSession : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE StartCapture(
                        ) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession, 0x814e42a9, 0xf70f, 0x4ad7, 0x93,0x9b, 0xfd,0xdc,0xc6,0xeb,0x88,0x0d)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession *This,
        TrustLevel *trustLevel);

    /*** IGraphicsCaptureSession methods ***/
    HRESULT (STDMETHODCALLTYPE *StartCapture)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession *This);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionVtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGraphicsCaptureSession methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_StartCapture(This) (This)->lpVtbl->StartCapture(This)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_Release(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGraphicsCaptureSession methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_StartCapture(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession* This) {
    return This->lpVtbl->StartCapture(This);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IGraphicsCaptureSession IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession
#define IGraphicsCaptureSessionVtbl __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionVtbl
#define IGraphicsCaptureSession __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession
#define IGraphicsCaptureSession_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_QueryInterface
#define IGraphicsCaptureSession_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_AddRef
#define IGraphicsCaptureSession_Release __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_Release
#define IGraphicsCaptureSession_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_GetIids
#define IGraphicsCaptureSession_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_GetRuntimeClassName
#define IGraphicsCaptureSession_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_GetTrustLevel
#define IGraphicsCaptureSession_StartCapture __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_StartCapture
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * IGraphicsCaptureSession2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2, 0x2c39ae40, 0x7d2e, 0x5044, 0x80,0x4e, 0x8b,0x67,0x99,0xd4,0xcf,0x9e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("2c39ae40-7d2e-5044-804e-8b6799d4cf9e")
                IGraphicsCaptureSession2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_IsCursorCaptureEnabled(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_IsCursorCaptureEnabled(
                        boolean value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2, 0x2c39ae40, 0x7d2e, 0x5044, 0x80,0x4e, 0x8b,0x67,0x99,0xd4,0xcf,0x9e)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 *This,
        TrustLevel *trustLevel);

    /*** IGraphicsCaptureSession2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IsCursorCaptureEnabled)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsCursorCaptureEnabled)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 *This,
        boolean value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2Vtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2 {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGraphicsCaptureSession2 methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_get_IsCursorCaptureEnabled(This,value) (This)->lpVtbl->get_IsCursorCaptureEnabled(This,value)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_put_IsCursorCaptureEnabled(This,value) (This)->lpVtbl->put_IsCursorCaptureEnabled(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_Release(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGraphicsCaptureSession2 methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_get_IsCursorCaptureEnabled(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2* This,boolean *value) {
    return This->lpVtbl->get_IsCursorCaptureEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_put_IsCursorCaptureEnabled(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2* This,boolean value) {
    return This->lpVtbl->put_IsCursorCaptureEnabled(This,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IGraphicsCaptureSession2 IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2
#define IGraphicsCaptureSession2Vtbl __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2Vtbl
#define IGraphicsCaptureSession2 __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2
#define IGraphicsCaptureSession2_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_QueryInterface
#define IGraphicsCaptureSession2_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_AddRef
#define IGraphicsCaptureSession2_Release __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_Release
#define IGraphicsCaptureSession2_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_GetIids
#define IGraphicsCaptureSession2_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_GetRuntimeClassName
#define IGraphicsCaptureSession2_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_GetTrustLevel
#define IGraphicsCaptureSession2_get_IsCursorCaptureEnabled __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_get_IsCursorCaptureEnabled
#define IGraphicsCaptureSession2_put_IsCursorCaptureEnabled __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_put_IsCursorCaptureEnabled
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * IGraphicsCaptureSession3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3, 0xf2cdd966, 0x22ae, 0x5ea1, 0x95,0x96, 0x3a,0x28,0x93,0x44,0xc3,0xbe);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("f2cdd966-22ae-5ea1-9596-3a289344c3be")
                IGraphicsCaptureSession3 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_IsBorderRequired(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_IsBorderRequired(
                        boolean value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3, 0xf2cdd966, 0x22ae, 0x5ea1, 0x95,0x96, 0x3a,0x28,0x93,0x44,0xc3,0xbe)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 *This,
        TrustLevel *trustLevel);

    /*** IGraphicsCaptureSession3 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IsBorderRequired)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsBorderRequired)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 *This,
        boolean value);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3Vtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3 {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGraphicsCaptureSession3 methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_get_IsBorderRequired(This,value) (This)->lpVtbl->get_IsBorderRequired(This,value)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_put_IsBorderRequired(This,value) (This)->lpVtbl->put_IsBorderRequired(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_Release(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGraphicsCaptureSession3 methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_get_IsBorderRequired(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3* This,boolean *value) {
    return This->lpVtbl->get_IsBorderRequired(This,value);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_put_IsBorderRequired(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3* This,boolean value) {
    return This->lpVtbl->put_IsBorderRequired(This,value);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IGraphicsCaptureSession3 IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3
#define IGraphicsCaptureSession3Vtbl __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3Vtbl
#define IGraphicsCaptureSession3 __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3
#define IGraphicsCaptureSession3_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_QueryInterface
#define IGraphicsCaptureSession3_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_AddRef
#define IGraphicsCaptureSession3_Release __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_Release
#define IGraphicsCaptureSession3_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_GetIids
#define IGraphicsCaptureSession3_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_GetRuntimeClassName
#define IGraphicsCaptureSession3_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_GetTrustLevel
#define IGraphicsCaptureSession3_get_IsBorderRequired __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_get_IsBorderRequired
#define IGraphicsCaptureSession3_put_IsBorderRequired __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_put_IsBorderRequired
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSession3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xc0000 */

/*****************************************************************************
 * IGraphicsCaptureSessionStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics, 0x2224a540, 0x5974, 0x49aa, 0xb2,0x32, 0x08,0x82,0x53,0x6f,0x4c,0xb5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Graphics {
            namespace Capture {
                MIDL_INTERFACE("2224a540-5974-49aa-b232-0882536f4cb5")
                IGraphicsCaptureSessionStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE IsSupported(
                        boolean *result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics, 0x2224a540, 0x5974, 0x49aa, 0xb2,0x32, 0x08,0x82,0x53,0x6f,0x4c,0xb5)
#endif
#else
typedef struct __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics *This,
        TrustLevel *trustLevel);

    /*** IGraphicsCaptureSessionStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *IsSupported)(
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics *This,
        boolean *result);

    END_INTERFACE
} __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStaticsVtbl;

interface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics {
    CONST_VTBL __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IGraphicsCaptureSessionStatics methods ***/
#define __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_IsSupported(This,result) (This)->lpVtbl->IsSupported(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_QueryInterface(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_AddRef(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_Release(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_GetIids(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_GetRuntimeClassName(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_GetTrustLevel(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IGraphicsCaptureSessionStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_IsSupported(__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics* This,boolean *result) {
    return This->lpVtbl->IsSupported(This,result);
}
#endif
#ifdef WIDL_using_Windows_Graphics_Capture
#define IID_IGraphicsCaptureSessionStatics IID___x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics
#define IGraphicsCaptureSessionStaticsVtbl __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStaticsVtbl
#define IGraphicsCaptureSessionStatics __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics
#define IGraphicsCaptureSessionStatics_QueryInterface __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_QueryInterface
#define IGraphicsCaptureSessionStatics_AddRef __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_AddRef
#define IGraphicsCaptureSessionStatics_Release __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_Release
#define IGraphicsCaptureSessionStatics_GetIids __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_GetIids
#define IGraphicsCaptureSessionStatics_GetRuntimeClassName __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_GetRuntimeClassName
#define IGraphicsCaptureSessionStatics_GetTrustLevel __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_GetTrustLevel
#define IGraphicsCaptureSessionStatics_IsSupported __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_IsSupported
#endif /* WIDL_using_Windows_Graphics_Capture */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureSessionStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*
 * Class Windows.Graphics.Capture.Direct3D11CaptureFrame
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef RUNTIMECLASS_Windows_Graphics_Capture_Direct3D11CaptureFrame_DEFINED
#define RUNTIMECLASS_Windows_Graphics_Capture_Direct3D11CaptureFrame_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Graphics_Capture_Direct3D11CaptureFrame[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','C','a','p','t','u','r','e','.','D','i','r','e','c','t','3','D','1','1','C','a','p','t','u','r','e','F','r','a','m','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Capture_Direct3D11CaptureFrame[] = L"Windows.Graphics.Capture.Direct3D11CaptureFrame";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Capture_Direct3D11CaptureFrame[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','C','a','p','t','u','r','e','.','D','i','r','e','c','t','3','D','1','1','C','a','p','t','u','r','e','F','r','a','m','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Graphics_Capture_Direct3D11CaptureFrame_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*
 * Class Windows.Graphics.Capture.Direct3D11CaptureFramePool
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef RUNTIMECLASS_Windows_Graphics_Capture_Direct3D11CaptureFramePool_DEFINED
#define RUNTIMECLASS_Windows_Graphics_Capture_Direct3D11CaptureFramePool_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Graphics_Capture_Direct3D11CaptureFramePool[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','C','a','p','t','u','r','e','.','D','i','r','e','c','t','3','D','1','1','C','a','p','t','u','r','e','F','r','a','m','e','P','o','o','l',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Capture_Direct3D11CaptureFramePool[] = L"Windows.Graphics.Capture.Direct3D11CaptureFramePool";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Capture_Direct3D11CaptureFramePool[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','C','a','p','t','u','r','e','.','D','i','r','e','c','t','3','D','1','1','C','a','p','t','u','r','e','F','r','a','m','e','P','o','o','l',0};
#endif
#endif /* RUNTIMECLASS_Windows_Graphics_Capture_Direct3D11CaptureFramePool_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*
 * Class Windows.Graphics.Capture.GraphicsCaptureItem
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef RUNTIMECLASS_Windows_Graphics_Capture_GraphicsCaptureItem_DEFINED
#define RUNTIMECLASS_Windows_Graphics_Capture_GraphicsCaptureItem_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Graphics_Capture_GraphicsCaptureItem[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','C','a','p','t','u','r','e','.','G','r','a','p','h','i','c','s','C','a','p','t','u','r','e','I','t','e','m',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Capture_GraphicsCaptureItem[] = L"Windows.Graphics.Capture.GraphicsCaptureItem";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Capture_GraphicsCaptureItem[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','C','a','p','t','u','r','e','.','G','r','a','p','h','i','c','s','C','a','p','t','u','r','e','I','t','e','m',0};
#endif
#endif /* RUNTIMECLASS_Windows_Graphics_Capture_GraphicsCaptureItem_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*
 * Class Windows.Graphics.Capture.GraphicsCaptureSession
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000
#ifndef RUNTIMECLASS_Windows_Graphics_Capture_GraphicsCaptureSession_DEFINED
#define RUNTIMECLASS_Windows_Graphics_Capture_GraphicsCaptureSession_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Graphics_Capture_GraphicsCaptureSession[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','C','a','p','t','u','r','e','.','G','r','a','p','h','i','c','s','C','a','p','t','u','r','e','S','e','s','s','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Capture_GraphicsCaptureSession[] = L"Windows.Graphics.Capture.GraphicsCaptureSession";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Graphics_Capture_GraphicsCaptureSession[] = {'W','i','n','d','o','w','s','.','G','r','a','p','h','i','c','s','.','C','a','p','t','u','r','e','.','G','r','a','p','h','i','c','s','C','a','p','t','u','r','e','S','e','s','s','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Graphics_Capture_GraphicsCaptureSession_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x60000 */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem, 0x43004a3c, 0xffe7, 0x5352, 0x85,0xa6, 0x7b,0xc4,0x1b,0x78,0x2f,0xca);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("43004a3c-ffe7-5352-85a6-7bc41b782fca")
            IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Capture::GraphicsCaptureItem*, ABI::Windows::Graphics::Capture::IGraphicsCaptureItem* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem, 0x43004a3c, 0xffe7, 0x5352, 0x85,0xa6, 0x7b,0xc4,0x1b,0x78,0x2f,0xca)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This,
        __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItemVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_Release(__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This,__FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_GraphicsCaptureItem IID___FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem
#define IAsyncOperationCompletedHandler_GraphicsCaptureItemVtbl __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItemVtbl
#define IAsyncOperationCompletedHandler_GraphicsCaptureItem __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem
#define IAsyncOperationCompletedHandler_GraphicsCaptureItem_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_QueryInterface
#define IAsyncOperationCompletedHandler_GraphicsCaptureItem_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_AddRef
#define IAsyncOperationCompletedHandler_GraphicsCaptureItem_Release __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_Release
#define IAsyncOperationCompletedHandler_GraphicsCaptureItem_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem, 0x01ccf2ae, 0x1059, 0x5d57, 0xa8,0x05, 0x0a,0x1d,0xfc,0x54,0xca,0xb9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("01ccf2ae-1059-5d57-a805-0a1dfc54cab9")
            IAsyncOperation<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Capture::GraphicsCaptureItem*, ABI::Windows::Graphics::Capture::IGraphicsCaptureItem* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem, 0x01ccf2ae, 0x1059, 0x5d57, 0xa8,0x05, 0x0a,0x1d,0xfc,0x54,0xca,0xb9)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *This,
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItemVtbl;

interface __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem {
    CONST_VTBL __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* > methods ***/
#define __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_QueryInterface(__FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_AddRef(__FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_Release(__FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetIids(__FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetTrustLevel(__FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Graphics::Capture::GraphicsCaptureItem* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_put_Completed(__FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This,__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_get_Completed(__FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This,__FIAsyncOperationCompletedHandler_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetResults(__FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem* This,__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_GraphicsCaptureItem IID___FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem
#define IAsyncOperation_GraphicsCaptureItemVtbl __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItemVtbl
#define IAsyncOperation_GraphicsCaptureItem __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem
#define IAsyncOperation_GraphicsCaptureItem_QueryInterface __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_QueryInterface
#define IAsyncOperation_GraphicsCaptureItem_AddRef __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_AddRef
#define IAsyncOperation_GraphicsCaptureItem_Release __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_Release
#define IAsyncOperation_GraphicsCaptureItem_GetIids __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetIids
#define IAsyncOperation_GraphicsCaptureItem_GetRuntimeClassName __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetRuntimeClassName
#define IAsyncOperation_GraphicsCaptureItem_GetTrustLevel __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetTrustLevel
#define IAsyncOperation_GraphicsCaptureItem_put_Completed __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_put_Completed
#define IAsyncOperation_GraphicsCaptureItem_get_Completed __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_get_Completed
#define IAsyncOperation_GraphicsCaptureItem_GetResults __FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CGraphics__CCapture__CGraphicsCaptureItem_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Graphics::Capture::Direct3D11CaptureFramePool*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable, 0x51a947f7, 0x79cf, 0x5a3e, 0xa3,0xa5, 0x12,0x89,0xcf,0xa6,0xdf,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("51a947f7-79cf-5a3e-a3a5-1289cfa6dfe8")
            ITypedEventHandler<ABI::Windows::Graphics::Capture::Direct3D11CaptureFramePool*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Capture::Direct3D11CaptureFramePool*, ABI::Windows::Graphics::Capture::IDirect3D11CaptureFramePool* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable, 0x51a947f7, 0x79cf, 0x5a3e, 0xa3,0xa5, 0x12,0x89,0xcf,0xa6,0xdf,0xe8)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::Graphics::Capture::Direct3D11CaptureFramePool*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable *This,
        __x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Graphics::Capture::Direct3D11CaptureFramePool*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_Release(__FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Graphics::Capture::Direct3D11CaptureFramePool*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable* This,__x_ABI_CWindows_CGraphics_CCapture_CIDirect3D11CaptureFramePool *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_Direct3D11CaptureFramePool_IInspectable IID___FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable
#define ITypedEventHandler_Direct3D11CaptureFramePool_IInspectableVtbl __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectableVtbl
#define ITypedEventHandler_Direct3D11CaptureFramePool_IInspectable __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable
#define ITypedEventHandler_Direct3D11CaptureFramePool_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_QueryInterface
#define ITypedEventHandler_Direct3D11CaptureFramePool_IInspectable_AddRef __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_AddRef
#define ITypedEventHandler_Direct3D11CaptureFramePool_IInspectable_Release __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_Release
#define ITypedEventHandler_Direct3D11CaptureFramePool_IInspectable_Invoke __FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CGraphics__CCapture__CDirect3D11CaptureFramePool_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable, 0xe9c610c0, 0xa68c, 0x5bd9, 0x80,0x21, 0x85,0x89,0x34,0x6e,0xee,0xe2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("e9c610c0-a68c-5bd9-8021-8589346eeee2")
            ITypedEventHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Graphics::Capture::GraphicsCaptureItem*, ABI::Windows::Graphics::Capture::IGraphicsCaptureItem* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable, 0xe9c610c0, 0xa68c, 0x5bd9, 0x80,0x21, 0x85,0x89,0x34,0x6e,0xee,0xe2)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable *This,
        __x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_Release(__FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Graphics::Capture::GraphicsCaptureItem*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable* This,__x_ABI_CWindows_CGraphics_CCapture_CIGraphicsCaptureItem *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_GraphicsCaptureItem_IInspectable IID___FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable
#define ITypedEventHandler_GraphicsCaptureItem_IInspectableVtbl __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectableVtbl
#define ITypedEventHandler_GraphicsCaptureItem_IInspectable __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable
#define ITypedEventHandler_GraphicsCaptureItem_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_QueryInterface
#define ITypedEventHandler_GraphicsCaptureItem_IInspectable_AddRef __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_AddRef
#define ITypedEventHandler_GraphicsCaptureItem_IInspectable_Release __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_Release
#define ITypedEventHandler_GraphicsCaptureItem_IInspectable_Invoke __FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CGraphics__CCapture__CGraphicsCaptureItem_IInspectable_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_graphics_capture_h__ */
