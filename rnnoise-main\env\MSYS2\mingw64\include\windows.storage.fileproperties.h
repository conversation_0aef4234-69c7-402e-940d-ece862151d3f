/*** Autogenerated by WIDL 10.12 from include/windows.storage.fileproperties.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_storage_fileproperties_h__
#define __windows_storage_fileproperties_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties ABI::Windows::Storage::FileProperties::IBasicProperties
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace FileProperties {
                interface IBasicProperties;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CStorage_CFileProperties_CBasicProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CFileProperties_CBasicProperties_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace FileProperties {
                class BasicProperties;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CStorage_CFileProperties_CBasicProperties __x_ABI_CWindows_CStorage_CFileProperties_CBasicProperties;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CStorage_CFileProperties_CBasicProperties_FWD_DEFINED__ */

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Storage::FileProperties::BasicProperties* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::FileProperties::BasicProperties* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.devices.geolocation.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_FWD_DEFINED__
#define ____x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties;
#ifdef __cplusplus
#define __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties ABI::Windows::Storage::FileProperties::IBasicProperties
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace FileProperties {
                interface IBasicProperties;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Storage::FileProperties::BasicProperties* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IBasicProperties interface
 */
#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties, 0xd05d55db, 0x785e, 0x4a66, 0xbe,0x02, 0x9b,0xee,0xc5,0x8a,0xea,0x81);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Storage {
            namespace FileProperties {
                MIDL_INTERFACE("d05d55db-785e-4a66-be02-9beec58aea81")
                IBasicProperties : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Size(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_DateModified(
                        ABI::Windows::Foundation::DateTime *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ItemDate(
                        ABI::Windows::Foundation::DateTime *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties, 0xd05d55db, 0x785e, 0x4a66, 0xbe,0x02, 0x9b,0xee,0xc5,0x8a,0xea,0x81)
#endif
#else
typedef struct __x_ABI_CWindows_CStorage_CFileProperties_CIBasicPropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties *This,
        TrustLevel *trustLevel);

    /*** IBasicProperties methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *get_DateModified)(
        __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties *This,
        __x_ABI_CWindows_CFoundation_CDateTime *value);

    HRESULT (STDMETHODCALLTYPE *get_ItemDate)(
        __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties *This,
        __x_ABI_CWindows_CFoundation_CDateTime *value);

    END_INTERFACE
} __x_ABI_CWindows_CStorage_CFileProperties_CIBasicPropertiesVtbl;

interface __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties {
    CONST_VTBL __x_ABI_CWindows_CStorage_CFileProperties_CIBasicPropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IBasicProperties methods ***/
#define __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_get_DateModified(This,value) (This)->lpVtbl->get_DateModified(This,value)
#define __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_get_ItemDate(This,value) (This)->lpVtbl->get_ItemDate(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_QueryInterface(__x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_AddRef(__x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_Release(__x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_GetIids(__x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_GetRuntimeClassName(__x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_GetTrustLevel(__x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IBasicProperties methods ***/
static inline HRESULT __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_get_Size(__x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties* This,UINT64 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_get_DateModified(__x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties* This,__x_ABI_CWindows_CFoundation_CDateTime *value) {
    return This->lpVtbl->get_DateModified(This,value);
}
static inline HRESULT __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_get_ItemDate(__x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties* This,__x_ABI_CWindows_CFoundation_CDateTime *value) {
    return This->lpVtbl->get_ItemDate(This,value);
}
#endif
#ifdef WIDL_using_Windows_Storage_FileProperties
#define IID_IBasicProperties IID___x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties
#define IBasicPropertiesVtbl __x_ABI_CWindows_CStorage_CFileProperties_CIBasicPropertiesVtbl
#define IBasicProperties __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties
#define IBasicProperties_QueryInterface __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_QueryInterface
#define IBasicProperties_AddRef __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_AddRef
#define IBasicProperties_Release __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_Release
#define IBasicProperties_GetIids __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_GetIids
#define IBasicProperties_GetRuntimeClassName __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_GetRuntimeClassName
#define IBasicProperties_GetTrustLevel __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_GetTrustLevel
#define IBasicProperties_get_Size __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_get_Size
#define IBasicProperties_get_DateModified __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_get_DateModified
#define IBasicProperties_get_ItemDate __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_get_ItemDate
#endif /* WIDL_using_Windows_Storage_FileProperties */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Storage.FileProperties.BasicProperties
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Storage_FileProperties_BasicProperties_DEFINED
#define RUNTIMECLASS_Windows_Storage_FileProperties_BasicProperties_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Storage_FileProperties_BasicProperties[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','F','i','l','e','P','r','o','p','e','r','t','i','e','s','.','B','a','s','i','c','P','r','o','p','e','r','t','i','e','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_FileProperties_BasicProperties[] = L"Windows.Storage.FileProperties.BasicProperties";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Storage_FileProperties_BasicProperties[] = {'W','i','n','d','o','w','s','.','S','t','o','r','a','g','e','.','F','i','l','e','P','r','o','p','e','r','t','i','e','s','.','B','a','s','i','c','P','r','o','p','e','r','t','i','e','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Storage_FileProperties_BasicProperties_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Storage::FileProperties::BasicProperties* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties, 0xc8659aae, 0x4926, 0x52ad, 0x8f,0x60, 0xd8,0x9f,0xe5,0xa8,0xdf,0x5f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("c8659aae-4926-52ad-8f60-d89fe5a8df5f")
            IAsyncOperationCompletedHandler<ABI::Windows::Storage::FileProperties::BasicProperties* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::FileProperties::BasicProperties*, ABI::Windows::Storage::FileProperties::IBasicProperties* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties, 0xc8659aae, 0x4926, 0x52ad, 0x8f,0x60, 0xd8,0x9f,0xe5,0xa8,0xdf,0x5f)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicPropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::FileProperties::BasicProperties* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties *This,
        __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicPropertiesVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicPropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::FileProperties::BasicProperties* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_Release(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Storage::FileProperties::BasicProperties* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties* This,__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_BasicProperties IID___FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties
#define IAsyncOperationCompletedHandler_BasicPropertiesVtbl __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicPropertiesVtbl
#define IAsyncOperationCompletedHandler_BasicProperties __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties
#define IAsyncOperationCompletedHandler_BasicProperties_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_QueryInterface
#define IAsyncOperationCompletedHandler_BasicProperties_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_AddRef
#define IAsyncOperationCompletedHandler_BasicProperties_Release __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_Release
#define IAsyncOperationCompletedHandler_BasicProperties_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Storage::FileProperties::BasicProperties* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties, 0x5186131a, 0x4467, 0x504b, 0x97,0x7a, 0x07,0x85,0xa8,0x23,0x04,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("5186131a-4467-504b-977a-0785a8230485")
            IAsyncOperation<ABI::Windows::Storage::FileProperties::BasicProperties* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Storage::FileProperties::BasicProperties*, ABI::Windows::Storage::FileProperties::IBasicProperties* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties, 0x5186131a, 0x4467, 0x504b, 0x97,0x7a, 0x07,0x85,0xa8,0x23,0x04,0x85)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicPropertiesVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Storage::FileProperties::BasicProperties* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties *This,
        __x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicPropertiesVtbl;

interface __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties {
    CONST_VTBL __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicPropertiesVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Storage::FileProperties::BasicProperties* > methods ***/
#define __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_QueryInterface(__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_AddRef(__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_Release(__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetIids(__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetTrustLevel(__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Storage::FileProperties::BasicProperties* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_put_Completed(__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_get_Completed(__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties* This,__FIAsyncOperationCompletedHandler_1_Windows__CStorage__CFileProperties__CBasicProperties **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetResults(__FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties* This,__x_ABI_CWindows_CStorage_CFileProperties_CIBasicProperties **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_BasicProperties IID___FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties
#define IAsyncOperation_BasicPropertiesVtbl __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicPropertiesVtbl
#define IAsyncOperation_BasicProperties __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties
#define IAsyncOperation_BasicProperties_QueryInterface __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_QueryInterface
#define IAsyncOperation_BasicProperties_AddRef __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_AddRef
#define IAsyncOperation_BasicProperties_Release __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_Release
#define IAsyncOperation_BasicProperties_GetIids __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetIids
#define IAsyncOperation_BasicProperties_GetRuntimeClassName __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetRuntimeClassName
#define IAsyncOperation_BasicProperties_GetTrustLevel __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetTrustLevel
#define IAsyncOperation_BasicProperties_put_Completed __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_put_Completed
#define IAsyncOperation_BasicProperties_get_Completed __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_get_Completed
#define IAsyncOperation_BasicProperties_GetResults __FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CStorage__CFileProperties__CBasicProperties_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_storage_fileproperties_h__ */
