/*** Autogenerated by WIDL 10.12 from include/vsbackup.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __vsbackup_h__
#define __vsbackup_h__

/* Forward declarations */

#ifndef __IVssWMComponent_FWD_DEFINED__
#define __IVssWMComponent_FWD_DEFINED__
typedef interface IVssWMComponent IVssWMComponent;
#ifdef __cplusplus
interface IVssWMComponent;
#endif /* __cplusplus */
#endif

#ifndef __IVssExamineWriterMetadata_FWD_DEFINED__
#define __IVssExamineWriterMetadata_FWD_DEFINED__
typedef interface IVssExamineWriterMetadata IVssExamineWriterMetadata;
#ifdef __cplusplus
interface IVssExamineWriterMetadata;
#endif /* __cplusplus */
#endif

#ifndef __IVssExamineWriterMetadataEx_FWD_DEFINED__
#define __IVssExamineWriterMetadataEx_FWD_DEFINED__
typedef interface IVssExamineWriterMetadataEx IVssExamineWriterMetadataEx;
#ifdef __cplusplus
interface IVssExamineWriterMetadataEx;
#endif /* __cplusplus */
#endif

#ifndef __IVssExamineWriterMetadataEx2_FWD_DEFINED__
#define __IVssExamineWriterMetadataEx2_FWD_DEFINED__
typedef interface IVssExamineWriterMetadataEx2 IVssExamineWriterMetadataEx2;
#ifdef __cplusplus
interface IVssExamineWriterMetadataEx2;
#endif /* __cplusplus */
#endif

#ifndef __IVssWriterComponentsExt_FWD_DEFINED__
#define __IVssWriterComponentsExt_FWD_DEFINED__
typedef interface IVssWriterComponentsExt IVssWriterComponentsExt;
#ifdef __cplusplus
interface IVssWriterComponentsExt;
#endif /* __cplusplus */
#endif

#ifndef __IVssBackupComponents_FWD_DEFINED__
#define __IVssBackupComponents_FWD_DEFINED__
typedef interface IVssBackupComponents IVssBackupComponents;
#ifdef __cplusplus
interface IVssBackupComponents;
#endif /* __cplusplus */
#endif

#ifndef __IVssBackupComponentsEx_FWD_DEFINED__
#define __IVssBackupComponentsEx_FWD_DEFINED__
typedef interface IVssBackupComponentsEx IVssBackupComponentsEx;
#ifdef __cplusplus
interface IVssBackupComponentsEx;
#endif /* __cplusplus */
#endif

#ifndef __IVssBackupComponentsEx2_FWD_DEFINED__
#define __IVssBackupComponentsEx2_FWD_DEFINED__
typedef interface IVssBackupComponentsEx2 IVssBackupComponentsEx2;
#ifdef __cplusplus
interface IVssBackupComponentsEx2;
#endif /* __cplusplus */
#endif

#ifndef __IVssBackupComponentsEx3_FWD_DEFINED__
#define __IVssBackupComponentsEx3_FWD_DEFINED__
typedef interface IVssBackupComponentsEx3 IVssBackupComponentsEx3;
#ifdef __cplusplus
interface IVssBackupComponentsEx3;
#endif /* __cplusplus */
#endif

#ifndef __IVssBackupComponentsEx4_FWD_DEFINED__
#define __IVssBackupComponentsEx4_FWD_DEFINED__
typedef interface IVssBackupComponentsEx4 IVssBackupComponentsEx4;
#ifdef __cplusplus
interface IVssBackupComponentsEx4;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <vss.h>
#include <vswriter.h>

#ifdef __cplusplus
extern "C" {
#endif

#include <winapifamily.h>
#if WINAPI_FAMILY_PARTITION(WINAPI_PARTITION_DESKTOP)
typedef struct _VSS_COMPONENTINFO {
    VSS_COMPONENT_TYPE type;
    BSTR bstrLogicalPath;
    BSTR bstrComponentName;
    BSTR bstrCaption;
    BYTE *pbIcon;
    UINT cbIcon;
    boolean bRestoreMetadata;
    boolean bNotifyOnBackupComplete;
    boolean bSelectable;
    boolean bSelectableForRestore;
    DWORD dwComponentFlags;
    UINT cFileCount;
    UINT cDatabases;
    UINT cLogFiles;
    UINT cDependencies;
} VSS_COMPONENTINFO;
typedef const VSS_COMPONENTINFO *PVSSCOMPONENTINFO;
/*****************************************************************************
 * IVssWMComponent interface
 */
#ifndef __IVssWMComponent_INTERFACE_DEFINED__
#define __IVssWMComponent_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssWMComponent, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000000-0000-0000-0000-000000000000")
IVssWMComponent : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetComponentInfo(
        PVSSCOMPONENTINFO *ppInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeComponentInfo(
        PVSSCOMPONENTINFO pInfo) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFile(
        UINT iFile,
        IVssWMFiledesc **ppFiledesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDatabaseFile(
        UINT iDBFile,
        IVssWMFiledesc **ppFiledesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDatabaseLogFile(
        UINT iDbLogFile,
        IVssWMFiledesc **ppFiledesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDependency(
        UINT iDependency,
        IVssWMDependency **ppDependency) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssWMComponent, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00)
#endif
#else
typedef struct IVssWMComponentVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssWMComponent *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssWMComponent *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssWMComponent *This);

    /*** IVssWMComponent methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentInfo)(
        IVssWMComponent *This,
        PVSSCOMPONENTINFO *ppInfo);

    HRESULT (STDMETHODCALLTYPE *FreeComponentInfo)(
        IVssWMComponent *This,
        PVSSCOMPONENTINFO pInfo);

    HRESULT (STDMETHODCALLTYPE *GetFile)(
        IVssWMComponent *This,
        UINT iFile,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetDatabaseFile)(
        IVssWMComponent *This,
        UINT iDBFile,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetDatabaseLogFile)(
        IVssWMComponent *This,
        UINT iDbLogFile,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetDependency)(
        IVssWMComponent *This,
        UINT iDependency,
        IVssWMDependency **ppDependency);

    END_INTERFACE
} IVssWMComponentVtbl;

interface IVssWMComponent {
    CONST_VTBL IVssWMComponentVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssWMComponent_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssWMComponent_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssWMComponent_Release(This) (This)->lpVtbl->Release(This)
/*** IVssWMComponent methods ***/
#define IVssWMComponent_GetComponentInfo(This,ppInfo) (This)->lpVtbl->GetComponentInfo(This,ppInfo)
#define IVssWMComponent_FreeComponentInfo(This,pInfo) (This)->lpVtbl->FreeComponentInfo(This,pInfo)
#define IVssWMComponent_GetFile(This,iFile,ppFiledesc) (This)->lpVtbl->GetFile(This,iFile,ppFiledesc)
#define IVssWMComponent_GetDatabaseFile(This,iDBFile,ppFiledesc) (This)->lpVtbl->GetDatabaseFile(This,iDBFile,ppFiledesc)
#define IVssWMComponent_GetDatabaseLogFile(This,iDbLogFile,ppFiledesc) (This)->lpVtbl->GetDatabaseLogFile(This,iDbLogFile,ppFiledesc)
#define IVssWMComponent_GetDependency(This,iDependency,ppDependency) (This)->lpVtbl->GetDependency(This,iDependency,ppDependency)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssWMComponent_QueryInterface(IVssWMComponent* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssWMComponent_AddRef(IVssWMComponent* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssWMComponent_Release(IVssWMComponent* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssWMComponent methods ***/
static inline HRESULT IVssWMComponent_GetComponentInfo(IVssWMComponent* This,PVSSCOMPONENTINFO *ppInfo) {
    return This->lpVtbl->GetComponentInfo(This,ppInfo);
}
static inline HRESULT IVssWMComponent_FreeComponentInfo(IVssWMComponent* This,PVSSCOMPONENTINFO pInfo) {
    return This->lpVtbl->FreeComponentInfo(This,pInfo);
}
static inline HRESULT IVssWMComponent_GetFile(IVssWMComponent* This,UINT iFile,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetFile(This,iFile,ppFiledesc);
}
static inline HRESULT IVssWMComponent_GetDatabaseFile(IVssWMComponent* This,UINT iDBFile,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetDatabaseFile(This,iDBFile,ppFiledesc);
}
static inline HRESULT IVssWMComponent_GetDatabaseLogFile(IVssWMComponent* This,UINT iDbLogFile,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetDatabaseLogFile(This,iDbLogFile,ppFiledesc);
}
static inline HRESULT IVssWMComponent_GetDependency(IVssWMComponent* This,UINT iDependency,IVssWMDependency **ppDependency) {
    return This->lpVtbl->GetDependency(This,iDependency,ppDependency);
}
#endif
#endif

#endif


#endif  /* __IVssWMComponent_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssExamineWriterMetadata interface
 */
#ifndef __IVssExamineWriterMetadata_INTERFACE_DEFINED__
#define __IVssExamineWriterMetadata_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssExamineWriterMetadata, 0x902fcf7f, 0xb7fd, 0x42f8, 0x81,0xf1, 0xb2,0xe4,0x00,0xb1,0xe5,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("902fcf7f-b7fd-42f8-81f1-b2e400b1e5bd")
IVssExamineWriterMetadata : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetIdentity(
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriterName,
        VSS_USAGE_TYPE *pUsage,
        VSS_SOURCE_TYPE *pSource) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFileCounts(
        UINT *pcIncludeFiles,
        UINT *pcExcludeFiles,
        UINT *pcComponents) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetIncludeFile(
        UINT iFile,
        IVssWMFiledesc **ppFiledesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExcludeFile(
        UINT iFile,
        IVssWMFiledesc **ppFiledesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetComponent(
        UINT iComponent,
        IVssWMComponent **ppComponent) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRestoreMethod(
        VSS_RESTOREMETHOD_ENUM *pMethod,
        BSTR *pbstrService,
        BSTR *pbstrUserProcedure,
        VSS_WRITERRESTORE_ENUM *pwriterRestore,
        boolean *pbRebootRequired,
        UINT *pcMappings) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetAlternateLocationMapping(
        UINT iMapping,
        IVssWMFiledesc **ppFiledesc) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBackupSchema(
        DWORD *pdwSchemaMask) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDocument(
        IXMLDOMDocument **pDoc) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveAsXML(
        BSTR *pbstrXML) = 0;

    virtual HRESULT STDMETHODCALLTYPE LoadFromXML(
        BSTR bstrXML) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssExamineWriterMetadata, 0x902fcf7f, 0xb7fd, 0x42f8, 0x81,0xf1, 0xb2,0xe4,0x00,0xb1,0xe5,0xbd)
#endif
#else
typedef struct IVssExamineWriterMetadataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssExamineWriterMetadata *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssExamineWriterMetadata *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssExamineWriterMetadata *This);

    /*** IVssExamineWriterMetadata methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIdentity)(
        IVssExamineWriterMetadata *This,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriterName,
        VSS_USAGE_TYPE *pUsage,
        VSS_SOURCE_TYPE *pSource);

    HRESULT (STDMETHODCALLTYPE *GetFileCounts)(
        IVssExamineWriterMetadata *This,
        UINT *pcIncludeFiles,
        UINT *pcExcludeFiles,
        UINT *pcComponents);

    HRESULT (STDMETHODCALLTYPE *GetIncludeFile)(
        IVssExamineWriterMetadata *This,
        UINT iFile,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetExcludeFile)(
        IVssExamineWriterMetadata *This,
        UINT iFile,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetComponent)(
        IVssExamineWriterMetadata *This,
        UINT iComponent,
        IVssWMComponent **ppComponent);

    HRESULT (STDMETHODCALLTYPE *GetRestoreMethod)(
        IVssExamineWriterMetadata *This,
        VSS_RESTOREMETHOD_ENUM *pMethod,
        BSTR *pbstrService,
        BSTR *pbstrUserProcedure,
        VSS_WRITERRESTORE_ENUM *pwriterRestore,
        boolean *pbRebootRequired,
        UINT *pcMappings);

    HRESULT (STDMETHODCALLTYPE *GetAlternateLocationMapping)(
        IVssExamineWriterMetadata *This,
        UINT iMapping,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetBackupSchema)(
        IVssExamineWriterMetadata *This,
        DWORD *pdwSchemaMask);

    HRESULT (STDMETHODCALLTYPE *GetDocument)(
        IVssExamineWriterMetadata *This,
        IXMLDOMDocument **pDoc);

    HRESULT (STDMETHODCALLTYPE *SaveAsXML)(
        IVssExamineWriterMetadata *This,
        BSTR *pbstrXML);

    HRESULT (STDMETHODCALLTYPE *LoadFromXML)(
        IVssExamineWriterMetadata *This,
        BSTR bstrXML);

    END_INTERFACE
} IVssExamineWriterMetadataVtbl;

interface IVssExamineWriterMetadata {
    CONST_VTBL IVssExamineWriterMetadataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssExamineWriterMetadata_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssExamineWriterMetadata_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssExamineWriterMetadata_Release(This) (This)->lpVtbl->Release(This)
/*** IVssExamineWriterMetadata methods ***/
#define IVssExamineWriterMetadata_GetIdentity(This,pidInstance,pidWriter,pbstrWriterName,pUsage,pSource) (This)->lpVtbl->GetIdentity(This,pidInstance,pidWriter,pbstrWriterName,pUsage,pSource)
#define IVssExamineWriterMetadata_GetFileCounts(This,pcIncludeFiles,pcExcludeFiles,pcComponents) (This)->lpVtbl->GetFileCounts(This,pcIncludeFiles,pcExcludeFiles,pcComponents)
#define IVssExamineWriterMetadata_GetIncludeFile(This,iFile,ppFiledesc) (This)->lpVtbl->GetIncludeFile(This,iFile,ppFiledesc)
#define IVssExamineWriterMetadata_GetExcludeFile(This,iFile,ppFiledesc) (This)->lpVtbl->GetExcludeFile(This,iFile,ppFiledesc)
#define IVssExamineWriterMetadata_GetComponent(This,iComponent,ppComponent) (This)->lpVtbl->GetComponent(This,iComponent,ppComponent)
#define IVssExamineWriterMetadata_GetRestoreMethod(This,pMethod,pbstrService,pbstrUserProcedure,pwriterRestore,pbRebootRequired,pcMappings) (This)->lpVtbl->GetRestoreMethod(This,pMethod,pbstrService,pbstrUserProcedure,pwriterRestore,pbRebootRequired,pcMappings)
#define IVssExamineWriterMetadata_GetAlternateLocationMapping(This,iMapping,ppFiledesc) (This)->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc)
#define IVssExamineWriterMetadata_GetBackupSchema(This,pdwSchemaMask) (This)->lpVtbl->GetBackupSchema(This,pdwSchemaMask)
#define IVssExamineWriterMetadata_GetDocument(This,pDoc) (This)->lpVtbl->GetDocument(This,pDoc)
#define IVssExamineWriterMetadata_SaveAsXML(This,pbstrXML) (This)->lpVtbl->SaveAsXML(This,pbstrXML)
#define IVssExamineWriterMetadata_LoadFromXML(This,bstrXML) (This)->lpVtbl->LoadFromXML(This,bstrXML)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssExamineWriterMetadata_QueryInterface(IVssExamineWriterMetadata* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssExamineWriterMetadata_AddRef(IVssExamineWriterMetadata* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssExamineWriterMetadata_Release(IVssExamineWriterMetadata* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssExamineWriterMetadata methods ***/
static inline HRESULT IVssExamineWriterMetadata_GetIdentity(IVssExamineWriterMetadata* This,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriterName,VSS_USAGE_TYPE *pUsage,VSS_SOURCE_TYPE *pSource) {
    return This->lpVtbl->GetIdentity(This,pidInstance,pidWriter,pbstrWriterName,pUsage,pSource);
}
static inline HRESULT IVssExamineWriterMetadata_GetFileCounts(IVssExamineWriterMetadata* This,UINT *pcIncludeFiles,UINT *pcExcludeFiles,UINT *pcComponents) {
    return This->lpVtbl->GetFileCounts(This,pcIncludeFiles,pcExcludeFiles,pcComponents);
}
static inline HRESULT IVssExamineWriterMetadata_GetIncludeFile(IVssExamineWriterMetadata* This,UINT iFile,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetIncludeFile(This,iFile,ppFiledesc);
}
static inline HRESULT IVssExamineWriterMetadata_GetExcludeFile(IVssExamineWriterMetadata* This,UINT iFile,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetExcludeFile(This,iFile,ppFiledesc);
}
static inline HRESULT IVssExamineWriterMetadata_GetComponent(IVssExamineWriterMetadata* This,UINT iComponent,IVssWMComponent **ppComponent) {
    return This->lpVtbl->GetComponent(This,iComponent,ppComponent);
}
static inline HRESULT IVssExamineWriterMetadata_GetRestoreMethod(IVssExamineWriterMetadata* This,VSS_RESTOREMETHOD_ENUM *pMethod,BSTR *pbstrService,BSTR *pbstrUserProcedure,VSS_WRITERRESTORE_ENUM *pwriterRestore,boolean *pbRebootRequired,UINT *pcMappings) {
    return This->lpVtbl->GetRestoreMethod(This,pMethod,pbstrService,pbstrUserProcedure,pwriterRestore,pbRebootRequired,pcMappings);
}
static inline HRESULT IVssExamineWriterMetadata_GetAlternateLocationMapping(IVssExamineWriterMetadata* This,UINT iMapping,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc);
}
static inline HRESULT IVssExamineWriterMetadata_GetBackupSchema(IVssExamineWriterMetadata* This,DWORD *pdwSchemaMask) {
    return This->lpVtbl->GetBackupSchema(This,pdwSchemaMask);
}
static inline HRESULT IVssExamineWriterMetadata_GetDocument(IVssExamineWriterMetadata* This,IXMLDOMDocument **pDoc) {
    return This->lpVtbl->GetDocument(This,pDoc);
}
static inline HRESULT IVssExamineWriterMetadata_SaveAsXML(IVssExamineWriterMetadata* This,BSTR *pbstrXML) {
    return This->lpVtbl->SaveAsXML(This,pbstrXML);
}
static inline HRESULT IVssExamineWriterMetadata_LoadFromXML(IVssExamineWriterMetadata* This,BSTR bstrXML) {
    return This->lpVtbl->LoadFromXML(This,bstrXML);
}
#endif
#endif

#endif


#endif  /* __IVssExamineWriterMetadata_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssExamineWriterMetadataEx interface
 */
#ifndef __IVssExamineWriterMetadataEx_INTERFACE_DEFINED__
#define __IVssExamineWriterMetadataEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssExamineWriterMetadataEx, 0x0c0e5ec0, 0xca44, 0x472b, 0xb7,0x02, 0xe6,0x52,0xdb,0x1c,0x04,0x51);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0c0e5ec0-ca44-472b-b702-e652db1c0451")
IVssExamineWriterMetadataEx : public IVssExamineWriterMetadata
{
    virtual HRESULT STDMETHODCALLTYPE GetIdentityEx(
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriterName,
        BSTR *pbstrInstanceName,
        VSS_USAGE_TYPE *pUsage,
        VSS_SOURCE_TYPE *pSource) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssExamineWriterMetadataEx, 0x0c0e5ec0, 0xca44, 0x472b, 0xb7,0x02, 0xe6,0x52,0xdb,0x1c,0x04,0x51)
#endif
#else
typedef struct IVssExamineWriterMetadataExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssExamineWriterMetadataEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssExamineWriterMetadataEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssExamineWriterMetadataEx *This);

    /*** IVssExamineWriterMetadata methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIdentity)(
        IVssExamineWriterMetadataEx *This,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriterName,
        VSS_USAGE_TYPE *pUsage,
        VSS_SOURCE_TYPE *pSource);

    HRESULT (STDMETHODCALLTYPE *GetFileCounts)(
        IVssExamineWriterMetadataEx *This,
        UINT *pcIncludeFiles,
        UINT *pcExcludeFiles,
        UINT *pcComponents);

    HRESULT (STDMETHODCALLTYPE *GetIncludeFile)(
        IVssExamineWriterMetadataEx *This,
        UINT iFile,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetExcludeFile)(
        IVssExamineWriterMetadataEx *This,
        UINT iFile,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetComponent)(
        IVssExamineWriterMetadataEx *This,
        UINT iComponent,
        IVssWMComponent **ppComponent);

    HRESULT (STDMETHODCALLTYPE *GetRestoreMethod)(
        IVssExamineWriterMetadataEx *This,
        VSS_RESTOREMETHOD_ENUM *pMethod,
        BSTR *pbstrService,
        BSTR *pbstrUserProcedure,
        VSS_WRITERRESTORE_ENUM *pwriterRestore,
        boolean *pbRebootRequired,
        UINT *pcMappings);

    HRESULT (STDMETHODCALLTYPE *GetAlternateLocationMapping)(
        IVssExamineWriterMetadataEx *This,
        UINT iMapping,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetBackupSchema)(
        IVssExamineWriterMetadataEx *This,
        DWORD *pdwSchemaMask);

    HRESULT (STDMETHODCALLTYPE *GetDocument)(
        IVssExamineWriterMetadataEx *This,
        IXMLDOMDocument **pDoc);

    HRESULT (STDMETHODCALLTYPE *SaveAsXML)(
        IVssExamineWriterMetadataEx *This,
        BSTR *pbstrXML);

    HRESULT (STDMETHODCALLTYPE *LoadFromXML)(
        IVssExamineWriterMetadataEx *This,
        BSTR bstrXML);

    /*** IVssExamineWriterMetadataEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIdentityEx)(
        IVssExamineWriterMetadataEx *This,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriterName,
        BSTR *pbstrInstanceName,
        VSS_USAGE_TYPE *pUsage,
        VSS_SOURCE_TYPE *pSource);

    END_INTERFACE
} IVssExamineWriterMetadataExVtbl;

interface IVssExamineWriterMetadataEx {
    CONST_VTBL IVssExamineWriterMetadataExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssExamineWriterMetadataEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssExamineWriterMetadataEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssExamineWriterMetadataEx_Release(This) (This)->lpVtbl->Release(This)
/*** IVssExamineWriterMetadata methods ***/
#define IVssExamineWriterMetadataEx_GetIdentity(This,pidInstance,pidWriter,pbstrWriterName,pUsage,pSource) (This)->lpVtbl->GetIdentity(This,pidInstance,pidWriter,pbstrWriterName,pUsage,pSource)
#define IVssExamineWriterMetadataEx_GetFileCounts(This,pcIncludeFiles,pcExcludeFiles,pcComponents) (This)->lpVtbl->GetFileCounts(This,pcIncludeFiles,pcExcludeFiles,pcComponents)
#define IVssExamineWriterMetadataEx_GetIncludeFile(This,iFile,ppFiledesc) (This)->lpVtbl->GetIncludeFile(This,iFile,ppFiledesc)
#define IVssExamineWriterMetadataEx_GetExcludeFile(This,iFile,ppFiledesc) (This)->lpVtbl->GetExcludeFile(This,iFile,ppFiledesc)
#define IVssExamineWriterMetadataEx_GetComponent(This,iComponent,ppComponent) (This)->lpVtbl->GetComponent(This,iComponent,ppComponent)
#define IVssExamineWriterMetadataEx_GetRestoreMethod(This,pMethod,pbstrService,pbstrUserProcedure,pwriterRestore,pbRebootRequired,pcMappings) (This)->lpVtbl->GetRestoreMethod(This,pMethod,pbstrService,pbstrUserProcedure,pwriterRestore,pbRebootRequired,pcMappings)
#define IVssExamineWriterMetadataEx_GetAlternateLocationMapping(This,iMapping,ppFiledesc) (This)->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc)
#define IVssExamineWriterMetadataEx_GetBackupSchema(This,pdwSchemaMask) (This)->lpVtbl->GetBackupSchema(This,pdwSchemaMask)
#define IVssExamineWriterMetadataEx_GetDocument(This,pDoc) (This)->lpVtbl->GetDocument(This,pDoc)
#define IVssExamineWriterMetadataEx_SaveAsXML(This,pbstrXML) (This)->lpVtbl->SaveAsXML(This,pbstrXML)
#define IVssExamineWriterMetadataEx_LoadFromXML(This,bstrXML) (This)->lpVtbl->LoadFromXML(This,bstrXML)
/*** IVssExamineWriterMetadataEx methods ***/
#define IVssExamineWriterMetadataEx_GetIdentityEx(This,pidInstance,pidWriter,pbstrWriterName,pbstrInstanceName,pUsage,pSource) (This)->lpVtbl->GetIdentityEx(This,pidInstance,pidWriter,pbstrWriterName,pbstrInstanceName,pUsage,pSource)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssExamineWriterMetadataEx_QueryInterface(IVssExamineWriterMetadataEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssExamineWriterMetadataEx_AddRef(IVssExamineWriterMetadataEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssExamineWriterMetadataEx_Release(IVssExamineWriterMetadataEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssExamineWriterMetadata methods ***/
static inline HRESULT IVssExamineWriterMetadataEx_GetIdentity(IVssExamineWriterMetadataEx* This,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriterName,VSS_USAGE_TYPE *pUsage,VSS_SOURCE_TYPE *pSource) {
    return This->lpVtbl->GetIdentity(This,pidInstance,pidWriter,pbstrWriterName,pUsage,pSource);
}
static inline HRESULT IVssExamineWriterMetadataEx_GetFileCounts(IVssExamineWriterMetadataEx* This,UINT *pcIncludeFiles,UINT *pcExcludeFiles,UINT *pcComponents) {
    return This->lpVtbl->GetFileCounts(This,pcIncludeFiles,pcExcludeFiles,pcComponents);
}
static inline HRESULT IVssExamineWriterMetadataEx_GetIncludeFile(IVssExamineWriterMetadataEx* This,UINT iFile,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetIncludeFile(This,iFile,ppFiledesc);
}
static inline HRESULT IVssExamineWriterMetadataEx_GetExcludeFile(IVssExamineWriterMetadataEx* This,UINT iFile,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetExcludeFile(This,iFile,ppFiledesc);
}
static inline HRESULT IVssExamineWriterMetadataEx_GetComponent(IVssExamineWriterMetadataEx* This,UINT iComponent,IVssWMComponent **ppComponent) {
    return This->lpVtbl->GetComponent(This,iComponent,ppComponent);
}
static inline HRESULT IVssExamineWriterMetadataEx_GetRestoreMethod(IVssExamineWriterMetadataEx* This,VSS_RESTOREMETHOD_ENUM *pMethod,BSTR *pbstrService,BSTR *pbstrUserProcedure,VSS_WRITERRESTORE_ENUM *pwriterRestore,boolean *pbRebootRequired,UINT *pcMappings) {
    return This->lpVtbl->GetRestoreMethod(This,pMethod,pbstrService,pbstrUserProcedure,pwriterRestore,pbRebootRequired,pcMappings);
}
static inline HRESULT IVssExamineWriterMetadataEx_GetAlternateLocationMapping(IVssExamineWriterMetadataEx* This,UINT iMapping,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc);
}
static inline HRESULT IVssExamineWriterMetadataEx_GetBackupSchema(IVssExamineWriterMetadataEx* This,DWORD *pdwSchemaMask) {
    return This->lpVtbl->GetBackupSchema(This,pdwSchemaMask);
}
static inline HRESULT IVssExamineWriterMetadataEx_GetDocument(IVssExamineWriterMetadataEx* This,IXMLDOMDocument **pDoc) {
    return This->lpVtbl->GetDocument(This,pDoc);
}
static inline HRESULT IVssExamineWriterMetadataEx_SaveAsXML(IVssExamineWriterMetadataEx* This,BSTR *pbstrXML) {
    return This->lpVtbl->SaveAsXML(This,pbstrXML);
}
static inline HRESULT IVssExamineWriterMetadataEx_LoadFromXML(IVssExamineWriterMetadataEx* This,BSTR bstrXML) {
    return This->lpVtbl->LoadFromXML(This,bstrXML);
}
/*** IVssExamineWriterMetadataEx methods ***/
static inline HRESULT IVssExamineWriterMetadataEx_GetIdentityEx(IVssExamineWriterMetadataEx* This,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriterName,BSTR *pbstrInstanceName,VSS_USAGE_TYPE *pUsage,VSS_SOURCE_TYPE *pSource) {
    return This->lpVtbl->GetIdentityEx(This,pidInstance,pidWriter,pbstrWriterName,pbstrInstanceName,pUsage,pSource);
}
#endif
#endif

#endif


#endif  /* __IVssExamineWriterMetadataEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssExamineWriterMetadataEx2 interface
 */
#ifndef __IVssExamineWriterMetadataEx2_INTERFACE_DEFINED__
#define __IVssExamineWriterMetadataEx2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssExamineWriterMetadataEx2, 0xce115780, 0xa611, 0x431b, 0xb5,0x7f, 0xc3,0x83,0x03,0xab,0x6a,0xee);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ce115780-a611-431b-b57f-c38303ab6aee")
IVssExamineWriterMetadataEx2 : public IVssExamineWriterMetadataEx
{
    virtual HRESULT STDMETHODCALLTYPE GetVersion(
        DWORD *pdwMajorVersion,
        DWORD *pdwMinorVersion) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExcludeFromSnapshotCount(
        UINT *pcExcludedFromSnapshot) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetExcludeFromSnapshotFile(
        UINT iFile,
        IVssWMFiledesc **ppFiledesc) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssExamineWriterMetadataEx2, 0xce115780, 0xa611, 0x431b, 0xb5,0x7f, 0xc3,0x83,0x03,0xab,0x6a,0xee)
#endif
#else
typedef struct IVssExamineWriterMetadataEx2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssExamineWriterMetadataEx2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssExamineWriterMetadataEx2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssExamineWriterMetadataEx2 *This);

    /*** IVssExamineWriterMetadata methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIdentity)(
        IVssExamineWriterMetadataEx2 *This,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriterName,
        VSS_USAGE_TYPE *pUsage,
        VSS_SOURCE_TYPE *pSource);

    HRESULT (STDMETHODCALLTYPE *GetFileCounts)(
        IVssExamineWriterMetadataEx2 *This,
        UINT *pcIncludeFiles,
        UINT *pcExcludeFiles,
        UINT *pcComponents);

    HRESULT (STDMETHODCALLTYPE *GetIncludeFile)(
        IVssExamineWriterMetadataEx2 *This,
        UINT iFile,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetExcludeFile)(
        IVssExamineWriterMetadataEx2 *This,
        UINT iFile,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetComponent)(
        IVssExamineWriterMetadataEx2 *This,
        UINT iComponent,
        IVssWMComponent **ppComponent);

    HRESULT (STDMETHODCALLTYPE *GetRestoreMethod)(
        IVssExamineWriterMetadataEx2 *This,
        VSS_RESTOREMETHOD_ENUM *pMethod,
        BSTR *pbstrService,
        BSTR *pbstrUserProcedure,
        VSS_WRITERRESTORE_ENUM *pwriterRestore,
        boolean *pbRebootRequired,
        UINT *pcMappings);

    HRESULT (STDMETHODCALLTYPE *GetAlternateLocationMapping)(
        IVssExamineWriterMetadataEx2 *This,
        UINT iMapping,
        IVssWMFiledesc **ppFiledesc);

    HRESULT (STDMETHODCALLTYPE *GetBackupSchema)(
        IVssExamineWriterMetadataEx2 *This,
        DWORD *pdwSchemaMask);

    HRESULT (STDMETHODCALLTYPE *GetDocument)(
        IVssExamineWriterMetadataEx2 *This,
        IXMLDOMDocument **pDoc);

    HRESULT (STDMETHODCALLTYPE *SaveAsXML)(
        IVssExamineWriterMetadataEx2 *This,
        BSTR *pbstrXML);

    HRESULT (STDMETHODCALLTYPE *LoadFromXML)(
        IVssExamineWriterMetadataEx2 *This,
        BSTR bstrXML);

    /*** IVssExamineWriterMetadataEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIdentityEx)(
        IVssExamineWriterMetadataEx2 *This,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriterName,
        BSTR *pbstrInstanceName,
        VSS_USAGE_TYPE *pUsage,
        VSS_SOURCE_TYPE *pSource);

    /*** IVssExamineWriterMetadataEx2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetVersion)(
        IVssExamineWriterMetadataEx2 *This,
        DWORD *pdwMajorVersion,
        DWORD *pdwMinorVersion);

    HRESULT (STDMETHODCALLTYPE *GetExcludeFromSnapshotCount)(
        IVssExamineWriterMetadataEx2 *This,
        UINT *pcExcludedFromSnapshot);

    HRESULT (STDMETHODCALLTYPE *GetExcludeFromSnapshotFile)(
        IVssExamineWriterMetadataEx2 *This,
        UINT iFile,
        IVssWMFiledesc **ppFiledesc);

    END_INTERFACE
} IVssExamineWriterMetadataEx2Vtbl;

interface IVssExamineWriterMetadataEx2 {
    CONST_VTBL IVssExamineWriterMetadataEx2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssExamineWriterMetadataEx2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssExamineWriterMetadataEx2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssExamineWriterMetadataEx2_Release(This) (This)->lpVtbl->Release(This)
/*** IVssExamineWriterMetadata methods ***/
#define IVssExamineWriterMetadataEx2_GetIdentity(This,pidInstance,pidWriter,pbstrWriterName,pUsage,pSource) (This)->lpVtbl->GetIdentity(This,pidInstance,pidWriter,pbstrWriterName,pUsage,pSource)
#define IVssExamineWriterMetadataEx2_GetFileCounts(This,pcIncludeFiles,pcExcludeFiles,pcComponents) (This)->lpVtbl->GetFileCounts(This,pcIncludeFiles,pcExcludeFiles,pcComponents)
#define IVssExamineWriterMetadataEx2_GetIncludeFile(This,iFile,ppFiledesc) (This)->lpVtbl->GetIncludeFile(This,iFile,ppFiledesc)
#define IVssExamineWriterMetadataEx2_GetExcludeFile(This,iFile,ppFiledesc) (This)->lpVtbl->GetExcludeFile(This,iFile,ppFiledesc)
#define IVssExamineWriterMetadataEx2_GetComponent(This,iComponent,ppComponent) (This)->lpVtbl->GetComponent(This,iComponent,ppComponent)
#define IVssExamineWriterMetadataEx2_GetRestoreMethod(This,pMethod,pbstrService,pbstrUserProcedure,pwriterRestore,pbRebootRequired,pcMappings) (This)->lpVtbl->GetRestoreMethod(This,pMethod,pbstrService,pbstrUserProcedure,pwriterRestore,pbRebootRequired,pcMappings)
#define IVssExamineWriterMetadataEx2_GetAlternateLocationMapping(This,iMapping,ppFiledesc) (This)->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc)
#define IVssExamineWriterMetadataEx2_GetBackupSchema(This,pdwSchemaMask) (This)->lpVtbl->GetBackupSchema(This,pdwSchemaMask)
#define IVssExamineWriterMetadataEx2_GetDocument(This,pDoc) (This)->lpVtbl->GetDocument(This,pDoc)
#define IVssExamineWriterMetadataEx2_SaveAsXML(This,pbstrXML) (This)->lpVtbl->SaveAsXML(This,pbstrXML)
#define IVssExamineWriterMetadataEx2_LoadFromXML(This,bstrXML) (This)->lpVtbl->LoadFromXML(This,bstrXML)
/*** IVssExamineWriterMetadataEx methods ***/
#define IVssExamineWriterMetadataEx2_GetIdentityEx(This,pidInstance,pidWriter,pbstrWriterName,pbstrInstanceName,pUsage,pSource) (This)->lpVtbl->GetIdentityEx(This,pidInstance,pidWriter,pbstrWriterName,pbstrInstanceName,pUsage,pSource)
/*** IVssExamineWriterMetadataEx2 methods ***/
#define IVssExamineWriterMetadataEx2_GetVersion(This,pdwMajorVersion,pdwMinorVersion) (This)->lpVtbl->GetVersion(This,pdwMajorVersion,pdwMinorVersion)
#define IVssExamineWriterMetadataEx2_GetExcludeFromSnapshotCount(This,pcExcludedFromSnapshot) (This)->lpVtbl->GetExcludeFromSnapshotCount(This,pcExcludedFromSnapshot)
#define IVssExamineWriterMetadataEx2_GetExcludeFromSnapshotFile(This,iFile,ppFiledesc) (This)->lpVtbl->GetExcludeFromSnapshotFile(This,iFile,ppFiledesc)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssExamineWriterMetadataEx2_QueryInterface(IVssExamineWriterMetadataEx2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssExamineWriterMetadataEx2_AddRef(IVssExamineWriterMetadataEx2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssExamineWriterMetadataEx2_Release(IVssExamineWriterMetadataEx2* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssExamineWriterMetadata methods ***/
static inline HRESULT IVssExamineWriterMetadataEx2_GetIdentity(IVssExamineWriterMetadataEx2* This,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriterName,VSS_USAGE_TYPE *pUsage,VSS_SOURCE_TYPE *pSource) {
    return This->lpVtbl->GetIdentity(This,pidInstance,pidWriter,pbstrWriterName,pUsage,pSource);
}
static inline HRESULT IVssExamineWriterMetadataEx2_GetFileCounts(IVssExamineWriterMetadataEx2* This,UINT *pcIncludeFiles,UINT *pcExcludeFiles,UINT *pcComponents) {
    return This->lpVtbl->GetFileCounts(This,pcIncludeFiles,pcExcludeFiles,pcComponents);
}
static inline HRESULT IVssExamineWriterMetadataEx2_GetIncludeFile(IVssExamineWriterMetadataEx2* This,UINT iFile,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetIncludeFile(This,iFile,ppFiledesc);
}
static inline HRESULT IVssExamineWriterMetadataEx2_GetExcludeFile(IVssExamineWriterMetadataEx2* This,UINT iFile,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetExcludeFile(This,iFile,ppFiledesc);
}
static inline HRESULT IVssExamineWriterMetadataEx2_GetComponent(IVssExamineWriterMetadataEx2* This,UINT iComponent,IVssWMComponent **ppComponent) {
    return This->lpVtbl->GetComponent(This,iComponent,ppComponent);
}
static inline HRESULT IVssExamineWriterMetadataEx2_GetRestoreMethod(IVssExamineWriterMetadataEx2* This,VSS_RESTOREMETHOD_ENUM *pMethod,BSTR *pbstrService,BSTR *pbstrUserProcedure,VSS_WRITERRESTORE_ENUM *pwriterRestore,boolean *pbRebootRequired,UINT *pcMappings) {
    return This->lpVtbl->GetRestoreMethod(This,pMethod,pbstrService,pbstrUserProcedure,pwriterRestore,pbRebootRequired,pcMappings);
}
static inline HRESULT IVssExamineWriterMetadataEx2_GetAlternateLocationMapping(IVssExamineWriterMetadataEx2* This,UINT iMapping,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetAlternateLocationMapping(This,iMapping,ppFiledesc);
}
static inline HRESULT IVssExamineWriterMetadataEx2_GetBackupSchema(IVssExamineWriterMetadataEx2* This,DWORD *pdwSchemaMask) {
    return This->lpVtbl->GetBackupSchema(This,pdwSchemaMask);
}
static inline HRESULT IVssExamineWriterMetadataEx2_GetDocument(IVssExamineWriterMetadataEx2* This,IXMLDOMDocument **pDoc) {
    return This->lpVtbl->GetDocument(This,pDoc);
}
static inline HRESULT IVssExamineWriterMetadataEx2_SaveAsXML(IVssExamineWriterMetadataEx2* This,BSTR *pbstrXML) {
    return This->lpVtbl->SaveAsXML(This,pbstrXML);
}
static inline HRESULT IVssExamineWriterMetadataEx2_LoadFromXML(IVssExamineWriterMetadataEx2* This,BSTR bstrXML) {
    return This->lpVtbl->LoadFromXML(This,bstrXML);
}
/*** IVssExamineWriterMetadataEx methods ***/
static inline HRESULT IVssExamineWriterMetadataEx2_GetIdentityEx(IVssExamineWriterMetadataEx2* This,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriterName,BSTR *pbstrInstanceName,VSS_USAGE_TYPE *pUsage,VSS_SOURCE_TYPE *pSource) {
    return This->lpVtbl->GetIdentityEx(This,pidInstance,pidWriter,pbstrWriterName,pbstrInstanceName,pUsage,pSource);
}
/*** IVssExamineWriterMetadataEx2 methods ***/
static inline HRESULT IVssExamineWriterMetadataEx2_GetVersion(IVssExamineWriterMetadataEx2* This,DWORD *pdwMajorVersion,DWORD *pdwMinorVersion) {
    return This->lpVtbl->GetVersion(This,pdwMajorVersion,pdwMinorVersion);
}
static inline HRESULT IVssExamineWriterMetadataEx2_GetExcludeFromSnapshotCount(IVssExamineWriterMetadataEx2* This,UINT *pcExcludedFromSnapshot) {
    return This->lpVtbl->GetExcludeFromSnapshotCount(This,pcExcludedFromSnapshot);
}
static inline HRESULT IVssExamineWriterMetadataEx2_GetExcludeFromSnapshotFile(IVssExamineWriterMetadataEx2* This,UINT iFile,IVssWMFiledesc **ppFiledesc) {
    return This->lpVtbl->GetExcludeFromSnapshotFile(This,iFile,ppFiledesc);
}
#endif
#endif

#endif


#endif  /* __IVssExamineWriterMetadataEx2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssWriterComponentsExt interface
 */
#ifndef __IVssWriterComponentsExt_INTERFACE_DEFINED__
#define __IVssWriterComponentsExt_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssWriterComponentsExt, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("00000000-0000-0000-0000-000000000000")
IVssWriterComponentsExt : public IVssWriterComponents
{
};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssWriterComponentsExt, 0x00000000, 0x0000, 0x0000, 0x00,0x00, 0x00,0x00,0x00,0x00,0x00,0x00)
#endif
#else
typedef struct IVssWriterComponentsExtVtbl {
    BEGIN_INTERFACE

    /*** IVssWriterComponents methods ***/
    HRESULT (STDMETHODCALLTYPE *GetComponentCount)(
        IVssWriterComponentsExt *This,
        UINT *pcComponents);

    HRESULT (STDMETHODCALLTYPE *GetWriterInfo)(
        IVssWriterComponentsExt *This,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter);

    HRESULT (STDMETHODCALLTYPE *GetComponent)(
        IVssWriterComponentsExt *This,
        UINT iComponent,
        IVssComponent **ppComponent);

    END_INTERFACE
} IVssWriterComponentsExtVtbl;

interface IVssWriterComponentsExt {
    CONST_VTBL IVssWriterComponentsExtVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IVssWriterComponents methods ***/
#define IVssWriterComponentsExt_GetComponentCount(This,pcComponents) (This)->lpVtbl->GetComponentCount(This,pcComponents)
#define IVssWriterComponentsExt_GetWriterInfo(This,pidInstance,pidWriter) (This)->lpVtbl->GetWriterInfo(This,pidInstance,pidWriter)
#define IVssWriterComponentsExt_GetComponent(This,iComponent,ppComponent) (This)->lpVtbl->GetComponent(This,iComponent,ppComponent)
#else
/*** IVssWriterComponents methods ***/
static inline HRESULT IVssWriterComponentsExt_GetComponentCount(IVssWriterComponentsExt* This,UINT *pcComponents) {
    return This->lpVtbl->GetComponentCount(This,pcComponents);
}
static inline HRESULT IVssWriterComponentsExt_GetWriterInfo(IVssWriterComponentsExt* This,VSS_ID *pidInstance,VSS_ID *pidWriter) {
    return This->lpVtbl->GetWriterInfo(This,pidInstance,pidWriter);
}
static inline HRESULT IVssWriterComponentsExt_GetComponent(IVssWriterComponentsExt* This,UINT iComponent,IVssComponent **ppComponent) {
    return This->lpVtbl->GetComponent(This,iComponent,ppComponent);
}
#endif
#endif

#endif


#endif  /* __IVssWriterComponentsExt_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssBackupComponents interface
 */
#ifndef __IVssBackupComponents_INTERFACE_DEFINED__
#define __IVssBackupComponents_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssBackupComponents, 0x665c1d5f, 0xc218, 0x414d, 0xa0,0x5d, 0x7f,0xef,0x5f,0x9d,0x5c,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("665c1d5f-c218-414d-a05d-7fef5f9d5c86")
IVssBackupComponents : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetWriterComponentsCount(
        UINT *pcComponents) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWriterComponents(
        UINT iWriter,
        IVssWriterComponentsExt **ppWriter) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeForBackup(
        BSTR bstrXML = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBackupState(
        boolean bSelectComponents,
        boolean bBackupBootableSystemState,
        VSS_BACKUP_TYPE backupType,
        boolean bPartialFileSupport = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE InitializeForRestore(
        BSTR bstrXML) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRestoreState(
        VSS_RESTORE_TYPE restoreType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GatherWriterMetadata(
        IVssAsync **pAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWriterMetadataCount(
        UINT *pcWriters) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWriterMetadata(
        UINT iWriter,
        VSS_ID *pidInstance,
        IVssExamineWriterMetadata **ppMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeWriterMetadata(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddComponent(
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName) = 0;

    virtual HRESULT STDMETHODCALLTYPE PrepareForBackup(
        IVssAsync **ppAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE AbortBackup(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GatherWriterStatus(
        IVssAsync **pAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWriterStatusCount(
        UINT *pcWriters) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeWriterStatus(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetWriterStatus(
        UINT iWriter,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriter,
        VSS_WRITER_STATE *pnStatus,
        HRESULT *phResultFailure) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBackupSucceeded(
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSucceded) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBackupOptions(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszBackupOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSelectedForRestore(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSelectedForRestore) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRestoreOptions(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszRestoreOptions) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAdditionalRestores(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bAdditionalRestores) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPreviousBackupStamp(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPreviousBackupStamp) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveAsXML(
        BSTR *pbstrXML) = 0;

    virtual HRESULT STDMETHODCALLTYPE BackupComplete(
        IVssAsync **ppAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddAlternativeLocationMapping(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszDestination) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddRestoreSubcomponent(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszSubComponentLogicalPath,
        LPCWSTR wszSubComponentName,
        boolean bRepair) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFileRestoreStatus(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        VSS_FILE_RESTORE_STATUS status) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddNewTarget(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFileName,
        boolean bRecursive,
        LPCWSTR wszAlternatePath) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRangesFilePath(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        UINT iPartialFile,
        LPCWSTR wszRangesFile) = 0;

    virtual HRESULT STDMETHODCALLTYPE PreRestore(
        IVssAsync **ppAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE PostRestore(
        IVssAsync **ppAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetContext(
        LONG lContext) = 0;

    virtual HRESULT STDMETHODCALLTYPE StartSnapshotSet(
        VSS_ID *pSnapshotSetId) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddToSnapshotSet(
        VSS_PWSZ pwszVolumeName,
        VSS_ID ProviderId,
        VSS_ID *pidSnapshot) = 0;

    virtual HRESULT STDMETHODCALLTYPE DoSnapshotSet(
        IVssAsync **ppAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE DeleteSnapshots(
        VSS_ID SourceObjectId,
        VSS_OBJECT_TYPE eSourceObjectType,
        WINBOOL bForceDelete,
        LONG *plDeletedSnapshots,
        VSS_ID *pNondeletedSnapshotID) = 0;

    virtual HRESULT STDMETHODCALLTYPE ImportSnapshots(
        IVssAsync **ppAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE BreakSnapshotSet(
        VSS_ID SnapshotSetId) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSnapshotProperties(
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROP *pProp) = 0;

    virtual HRESULT STDMETHODCALLTYPE Query(
        VSS_ID QueriedObjectId,
        VSS_OBJECT_TYPE eQueriedObjectType,
        VSS_OBJECT_TYPE eReturnedObjectsType,
        IVssEnumObject **ppEnum) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsVolumeSupported(
        VSS_ID ProviderId,
        VSS_PWSZ pwszVolumeName,
        WINBOOL *pbSupportedByThisProvider) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisableWriterClasses(
        const VSS_ID *rgWriterClassId,
        UINT cClassId) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableWriterClasses(
        const VSS_ID *rgWriterClassId,
        UINT cClassId) = 0;

    virtual HRESULT STDMETHODCALLTYPE DisableWriterInstances(
        const VSS_ID *rgWriterInstanceId,
        UINT cInstanceId) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExposeSnapshot(
        VSS_ID SnapshotId,
        VSS_PWSZ wszPathFromRoot,
        LONG lAttributes,
        VSS_PWSZ wszExpose,
        VSS_PWSZ *pwszExposed) = 0;

    virtual HRESULT STDMETHODCALLTYPE RevertToSnapshot(
        VSS_ID SnapshotId,
        WINBOOL bForceDismount) = 0;

    virtual HRESULT STDMETHODCALLTYPE QueryRevertStatus(
        VSS_PWSZ pwszVolume,
        IVssAsync **ppAsync) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssBackupComponents, 0x665c1d5f, 0xc218, 0x414d, 0xa0,0x5d, 0x7f,0xef,0x5f,0x9d,0x5c,0x86)
#endif
#else
typedef struct IVssBackupComponentsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssBackupComponents *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssBackupComponents *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssBackupComponents *This);

    /*** IVssBackupComponents methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterComponentsCount)(
        IVssBackupComponents *This,
        UINT *pcComponents);

    HRESULT (STDMETHODCALLTYPE *GetWriterComponents)(
        IVssBackupComponents *This,
        UINT iWriter,
        IVssWriterComponentsExt **ppWriter);

    HRESULT (STDMETHODCALLTYPE *InitializeForBackup)(
        IVssBackupComponents *This,
        BSTR bstrXML);

    HRESULT (STDMETHODCALLTYPE *SetBackupState)(
        IVssBackupComponents *This,
        boolean bSelectComponents,
        boolean bBackupBootableSystemState,
        VSS_BACKUP_TYPE backupType,
        boolean bPartialFileSupport);

    HRESULT (STDMETHODCALLTYPE *InitializeForRestore)(
        IVssBackupComponents *This,
        BSTR bstrXML);

    HRESULT (STDMETHODCALLTYPE *SetRestoreState)(
        IVssBackupComponents *This,
        VSS_RESTORE_TYPE restoreType);

    HRESULT (STDMETHODCALLTYPE *GatherWriterMetadata)(
        IVssBackupComponents *This,
        IVssAsync **pAsync);

    HRESULT (STDMETHODCALLTYPE *GetWriterMetadataCount)(
        IVssBackupComponents *This,
        UINT *pcWriters);

    HRESULT (STDMETHODCALLTYPE *GetWriterMetadata)(
        IVssBackupComponents *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        IVssExamineWriterMetadata **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *FreeWriterMetadata)(
        IVssBackupComponents *This);

    HRESULT (STDMETHODCALLTYPE *AddComponent)(
        IVssBackupComponents *This,
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName);

    HRESULT (STDMETHODCALLTYPE *PrepareForBackup)(
        IVssBackupComponents *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *AbortBackup)(
        IVssBackupComponents *This);

    HRESULT (STDMETHODCALLTYPE *GatherWriterStatus)(
        IVssBackupComponents *This,
        IVssAsync **pAsync);

    HRESULT (STDMETHODCALLTYPE *GetWriterStatusCount)(
        IVssBackupComponents *This,
        UINT *pcWriters);

    HRESULT (STDMETHODCALLTYPE *FreeWriterStatus)(
        IVssBackupComponents *This);

    HRESULT (STDMETHODCALLTYPE *GetWriterStatus)(
        IVssBackupComponents *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriter,
        VSS_WRITER_STATE *pnStatus,
        HRESULT *phResultFailure);

    HRESULT (STDMETHODCALLTYPE *SetBackupSucceeded)(
        IVssBackupComponents *This,
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSucceded);

    HRESULT (STDMETHODCALLTYPE *SetBackupOptions)(
        IVssBackupComponents *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszBackupOptions);

    HRESULT (STDMETHODCALLTYPE *SetSelectedForRestore)(
        IVssBackupComponents *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSelectedForRestore);

    HRESULT (STDMETHODCALLTYPE *SetRestoreOptions)(
        IVssBackupComponents *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszRestoreOptions);

    HRESULT (STDMETHODCALLTYPE *SetAdditionalRestores)(
        IVssBackupComponents *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bAdditionalRestores);

    HRESULT (STDMETHODCALLTYPE *SetPreviousBackupStamp)(
        IVssBackupComponents *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPreviousBackupStamp);

    HRESULT (STDMETHODCALLTYPE *SaveAsXML)(
        IVssBackupComponents *This,
        BSTR *pbstrXML);

    HRESULT (STDMETHODCALLTYPE *BackupComplete)(
        IVssBackupComponents *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *AddAlternativeLocationMapping)(
        IVssBackupComponents *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszDestination);

    HRESULT (STDMETHODCALLTYPE *AddRestoreSubcomponent)(
        IVssBackupComponents *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszSubComponentLogicalPath,
        LPCWSTR wszSubComponentName,
        boolean bRepair);

    HRESULT (STDMETHODCALLTYPE *SetFileRestoreStatus)(
        IVssBackupComponents *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        VSS_FILE_RESTORE_STATUS status);

    HRESULT (STDMETHODCALLTYPE *AddNewTarget)(
        IVssBackupComponents *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFileName,
        boolean bRecursive,
        LPCWSTR wszAlternatePath);

    HRESULT (STDMETHODCALLTYPE *SetRangesFilePath)(
        IVssBackupComponents *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        UINT iPartialFile,
        LPCWSTR wszRangesFile);

    HRESULT (STDMETHODCALLTYPE *PreRestore)(
        IVssBackupComponents *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *PostRestore)(
        IVssBackupComponents *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *SetContext)(
        IVssBackupComponents *This,
        LONG lContext);

    HRESULT (STDMETHODCALLTYPE *StartSnapshotSet)(
        IVssBackupComponents *This,
        VSS_ID *pSnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *AddToSnapshotSet)(
        IVssBackupComponents *This,
        VSS_PWSZ pwszVolumeName,
        VSS_ID ProviderId,
        VSS_ID *pidSnapshot);

    HRESULT (STDMETHODCALLTYPE *DoSnapshotSet)(
        IVssBackupComponents *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *DeleteSnapshots)(
        IVssBackupComponents *This,
        VSS_ID SourceObjectId,
        VSS_OBJECT_TYPE eSourceObjectType,
        WINBOOL bForceDelete,
        LONG *plDeletedSnapshots,
        VSS_ID *pNondeletedSnapshotID);

    HRESULT (STDMETHODCALLTYPE *ImportSnapshots)(
        IVssBackupComponents *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *BreakSnapshotSet)(
        IVssBackupComponents *This,
        VSS_ID SnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *GetSnapshotProperties)(
        IVssBackupComponents *This,
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROP *pProp);

    HRESULT (STDMETHODCALLTYPE *Query)(
        IVssBackupComponents *This,
        VSS_ID QueriedObjectId,
        VSS_OBJECT_TYPE eQueriedObjectType,
        VSS_OBJECT_TYPE eReturnedObjectsType,
        IVssEnumObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *IsVolumeSupported)(
        IVssBackupComponents *This,
        VSS_ID ProviderId,
        VSS_PWSZ pwszVolumeName,
        WINBOOL *pbSupportedByThisProvider);

    HRESULT (STDMETHODCALLTYPE *DisableWriterClasses)(
        IVssBackupComponents *This,
        const VSS_ID *rgWriterClassId,
        UINT cClassId);

    HRESULT (STDMETHODCALLTYPE *EnableWriterClasses)(
        IVssBackupComponents *This,
        const VSS_ID *rgWriterClassId,
        UINT cClassId);

    HRESULT (STDMETHODCALLTYPE *DisableWriterInstances)(
        IVssBackupComponents *This,
        const VSS_ID *rgWriterInstanceId,
        UINT cInstanceId);

    HRESULT (STDMETHODCALLTYPE *ExposeSnapshot)(
        IVssBackupComponents *This,
        VSS_ID SnapshotId,
        VSS_PWSZ wszPathFromRoot,
        LONG lAttributes,
        VSS_PWSZ wszExpose,
        VSS_PWSZ *pwszExposed);

    HRESULT (STDMETHODCALLTYPE *RevertToSnapshot)(
        IVssBackupComponents *This,
        VSS_ID SnapshotId,
        WINBOOL bForceDismount);

    HRESULT (STDMETHODCALLTYPE *QueryRevertStatus)(
        IVssBackupComponents *This,
        VSS_PWSZ pwszVolume,
        IVssAsync **ppAsync);

    END_INTERFACE
} IVssBackupComponentsVtbl;

interface IVssBackupComponents {
    CONST_VTBL IVssBackupComponentsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssBackupComponents_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssBackupComponents_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssBackupComponents_Release(This) (This)->lpVtbl->Release(This)
/*** IVssBackupComponents methods ***/
#define IVssBackupComponents_GetWriterComponentsCount(This,pcComponents) (This)->lpVtbl->GetWriterComponentsCount(This,pcComponents)
#define IVssBackupComponents_GetWriterComponents(This,iWriter,ppWriter) (This)->lpVtbl->GetWriterComponents(This,iWriter,ppWriter)
#define IVssBackupComponents_InitializeForBackup(This,bstrXML) (This)->lpVtbl->InitializeForBackup(This,bstrXML)
#define IVssBackupComponents_SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport) (This)->lpVtbl->SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport)
#define IVssBackupComponents_InitializeForRestore(This,bstrXML) (This)->lpVtbl->InitializeForRestore(This,bstrXML)
#define IVssBackupComponents_SetRestoreState(This,restoreType) (This)->lpVtbl->SetRestoreState(This,restoreType)
#define IVssBackupComponents_GatherWriterMetadata(This,pAsync) (This)->lpVtbl->GatherWriterMetadata(This,pAsync)
#define IVssBackupComponents_GetWriterMetadataCount(This,pcWriters) (This)->lpVtbl->GetWriterMetadataCount(This,pcWriters)
#define IVssBackupComponents_GetWriterMetadata(This,iWriter,pidInstance,ppMetadata) (This)->lpVtbl->GetWriterMetadata(This,iWriter,pidInstance,ppMetadata)
#define IVssBackupComponents_FreeWriterMetadata(This) (This)->lpVtbl->FreeWriterMetadata(This)
#define IVssBackupComponents_AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName) (This)->lpVtbl->AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName)
#define IVssBackupComponents_PrepareForBackup(This,ppAsync) (This)->lpVtbl->PrepareForBackup(This,ppAsync)
#define IVssBackupComponents_AbortBackup(This) (This)->lpVtbl->AbortBackup(This)
#define IVssBackupComponents_GatherWriterStatus(This,pAsync) (This)->lpVtbl->GatherWriterStatus(This,pAsync)
#define IVssBackupComponents_GetWriterStatusCount(This,pcWriters) (This)->lpVtbl->GetWriterStatusCount(This,pcWriters)
#define IVssBackupComponents_FreeWriterStatus(This) (This)->lpVtbl->FreeWriterStatus(This)
#define IVssBackupComponents_GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure) (This)->lpVtbl->GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure)
#define IVssBackupComponents_SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded) (This)->lpVtbl->SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded)
#define IVssBackupComponents_SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions) (This)->lpVtbl->SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions)
#define IVssBackupComponents_SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore) (This)->lpVtbl->SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore)
#define IVssBackupComponents_SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions) (This)->lpVtbl->SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions)
#define IVssBackupComponents_SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores) (This)->lpVtbl->SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores)
#define IVssBackupComponents_SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp) (This)->lpVtbl->SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp)
#define IVssBackupComponents_SaveAsXML(This,pbstrXML) (This)->lpVtbl->SaveAsXML(This,pbstrXML)
#define IVssBackupComponents_BackupComplete(This,ppAsync) (This)->lpVtbl->BackupComplete(This,ppAsync)
#define IVssBackupComponents_AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination) (This)->lpVtbl->AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination)
#define IVssBackupComponents_AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair) (This)->lpVtbl->AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair)
#define IVssBackupComponents_SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status) (This)->lpVtbl->SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status)
#define IVssBackupComponents_AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath) (This)->lpVtbl->AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath)
#define IVssBackupComponents_SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile) (This)->lpVtbl->SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile)
#define IVssBackupComponents_PreRestore(This,ppAsync) (This)->lpVtbl->PreRestore(This,ppAsync)
#define IVssBackupComponents_PostRestore(This,ppAsync) (This)->lpVtbl->PostRestore(This,ppAsync)
#define IVssBackupComponents_SetContext(This,lContext) (This)->lpVtbl->SetContext(This,lContext)
#define IVssBackupComponents_StartSnapshotSet(This,pSnapshotSetId) (This)->lpVtbl->StartSnapshotSet(This,pSnapshotSetId)
#define IVssBackupComponents_AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot) (This)->lpVtbl->AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot)
#define IVssBackupComponents_DoSnapshotSet(This,ppAsync) (This)->lpVtbl->DoSnapshotSet(This,ppAsync)
#define IVssBackupComponents_DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID) (This)->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID)
#define IVssBackupComponents_ImportSnapshots(This,ppAsync) (This)->lpVtbl->ImportSnapshots(This,ppAsync)
#define IVssBackupComponents_BreakSnapshotSet(This,SnapshotSetId) (This)->lpVtbl->BreakSnapshotSet(This,SnapshotSetId)
#define IVssBackupComponents_GetSnapshotProperties(This,SnapshotId,pProp) (This)->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp)
#define IVssBackupComponents_Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum) (This)->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum)
#define IVssBackupComponents_IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider) (This)->lpVtbl->IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider)
#define IVssBackupComponents_DisableWriterClasses(This,rgWriterClassId,cClassId) (This)->lpVtbl->DisableWriterClasses(This,rgWriterClassId,cClassId)
#define IVssBackupComponents_EnableWriterClasses(This,rgWriterClassId,cClassId) (This)->lpVtbl->EnableWriterClasses(This,rgWriterClassId,cClassId)
#define IVssBackupComponents_DisableWriterInstances(This,rgWriterInstanceId,cInstanceId) (This)->lpVtbl->DisableWriterInstances(This,rgWriterInstanceId,cInstanceId)
#define IVssBackupComponents_ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed) (This)->lpVtbl->ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed)
#define IVssBackupComponents_RevertToSnapshot(This,SnapshotId,bForceDismount) (This)->lpVtbl->RevertToSnapshot(This,SnapshotId,bForceDismount)
#define IVssBackupComponents_QueryRevertStatus(This,pwszVolume,ppAsync) (This)->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssBackupComponents_QueryInterface(IVssBackupComponents* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssBackupComponents_AddRef(IVssBackupComponents* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssBackupComponents_Release(IVssBackupComponents* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssBackupComponents methods ***/
static inline HRESULT IVssBackupComponents_GetWriterComponentsCount(IVssBackupComponents* This,UINT *pcComponents) {
    return This->lpVtbl->GetWriterComponentsCount(This,pcComponents);
}
static inline HRESULT IVssBackupComponents_GetWriterComponents(IVssBackupComponents* This,UINT iWriter,IVssWriterComponentsExt **ppWriter) {
    return This->lpVtbl->GetWriterComponents(This,iWriter,ppWriter);
}
static inline HRESULT IVssBackupComponents_InitializeForBackup(IVssBackupComponents* This,BSTR bstrXML) {
    return This->lpVtbl->InitializeForBackup(This,bstrXML);
}
static inline HRESULT IVssBackupComponents_SetBackupState(IVssBackupComponents* This,boolean bSelectComponents,boolean bBackupBootableSystemState,VSS_BACKUP_TYPE backupType,boolean bPartialFileSupport) {
    return This->lpVtbl->SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport);
}
static inline HRESULT IVssBackupComponents_InitializeForRestore(IVssBackupComponents* This,BSTR bstrXML) {
    return This->lpVtbl->InitializeForRestore(This,bstrXML);
}
static inline HRESULT IVssBackupComponents_SetRestoreState(IVssBackupComponents* This,VSS_RESTORE_TYPE restoreType) {
    return This->lpVtbl->SetRestoreState(This,restoreType);
}
static inline HRESULT IVssBackupComponents_GatherWriterMetadata(IVssBackupComponents* This,IVssAsync **pAsync) {
    return This->lpVtbl->GatherWriterMetadata(This,pAsync);
}
static inline HRESULT IVssBackupComponents_GetWriterMetadataCount(IVssBackupComponents* This,UINT *pcWriters) {
    return This->lpVtbl->GetWriterMetadataCount(This,pcWriters);
}
static inline HRESULT IVssBackupComponents_GetWriterMetadata(IVssBackupComponents* This,UINT iWriter,VSS_ID *pidInstance,IVssExamineWriterMetadata **ppMetadata) {
    return This->lpVtbl->GetWriterMetadata(This,iWriter,pidInstance,ppMetadata);
}
static inline HRESULT IVssBackupComponents_FreeWriterMetadata(IVssBackupComponents* This) {
    return This->lpVtbl->FreeWriterMetadata(This);
}
static inline HRESULT IVssBackupComponents_AddComponent(IVssBackupComponents* This,VSS_ID instanceId,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName) {
    return This->lpVtbl->AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName);
}
static inline HRESULT IVssBackupComponents_PrepareForBackup(IVssBackupComponents* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PrepareForBackup(This,ppAsync);
}
static inline HRESULT IVssBackupComponents_AbortBackup(IVssBackupComponents* This) {
    return This->lpVtbl->AbortBackup(This);
}
static inline HRESULT IVssBackupComponents_GatherWriterStatus(IVssBackupComponents* This,IVssAsync **pAsync) {
    return This->lpVtbl->GatherWriterStatus(This,pAsync);
}
static inline HRESULT IVssBackupComponents_GetWriterStatusCount(IVssBackupComponents* This,UINT *pcWriters) {
    return This->lpVtbl->GetWriterStatusCount(This,pcWriters);
}
static inline HRESULT IVssBackupComponents_FreeWriterStatus(IVssBackupComponents* This) {
    return This->lpVtbl->FreeWriterStatus(This);
}
static inline HRESULT IVssBackupComponents_GetWriterStatus(IVssBackupComponents* This,UINT iWriter,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriter,VSS_WRITER_STATE *pnStatus,HRESULT *phResultFailure) {
    return This->lpVtbl->GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure);
}
static inline HRESULT IVssBackupComponents_SetBackupSucceeded(IVssBackupComponents* This,VSS_ID instanceId,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSucceded) {
    return This->lpVtbl->SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded);
}
static inline HRESULT IVssBackupComponents_SetBackupOptions(IVssBackupComponents* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszBackupOptions) {
    return This->lpVtbl->SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions);
}
static inline HRESULT IVssBackupComponents_SetSelectedForRestore(IVssBackupComponents* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSelectedForRestore) {
    return This->lpVtbl->SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore);
}
static inline HRESULT IVssBackupComponents_SetRestoreOptions(IVssBackupComponents* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszRestoreOptions) {
    return This->lpVtbl->SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions);
}
static inline HRESULT IVssBackupComponents_SetAdditionalRestores(IVssBackupComponents* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bAdditionalRestores) {
    return This->lpVtbl->SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores);
}
static inline HRESULT IVssBackupComponents_SetPreviousBackupStamp(IVssBackupComponents* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPreviousBackupStamp) {
    return This->lpVtbl->SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp);
}
static inline HRESULT IVssBackupComponents_SaveAsXML(IVssBackupComponents* This,BSTR *pbstrXML) {
    return This->lpVtbl->SaveAsXML(This,pbstrXML);
}
static inline HRESULT IVssBackupComponents_BackupComplete(IVssBackupComponents* This,IVssAsync **ppAsync) {
    return This->lpVtbl->BackupComplete(This,ppAsync);
}
static inline HRESULT IVssBackupComponents_AddAlternativeLocationMapping(IVssBackupComponents* This,VSS_ID writerId,VSS_COMPONENT_TYPE componentType,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive,LPCWSTR wszDestination) {
    return This->lpVtbl->AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination);
}
static inline HRESULT IVssBackupComponents_AddRestoreSubcomponent(IVssBackupComponents* This,VSS_ID writerId,VSS_COMPONENT_TYPE componentType,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszSubComponentLogicalPath,LPCWSTR wszSubComponentName,boolean bRepair) {
    return This->lpVtbl->AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair);
}
static inline HRESULT IVssBackupComponents_SetFileRestoreStatus(IVssBackupComponents* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,VSS_FILE_RESTORE_STATUS status) {
    return This->lpVtbl->SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status);
}
static inline HRESULT IVssBackupComponents_AddNewTarget(IVssBackupComponents* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPath,LPCWSTR wszFileName,boolean bRecursive,LPCWSTR wszAlternatePath) {
    return This->lpVtbl->AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath);
}
static inline HRESULT IVssBackupComponents_SetRangesFilePath(IVssBackupComponents* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,UINT iPartialFile,LPCWSTR wszRangesFile) {
    return This->lpVtbl->SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile);
}
static inline HRESULT IVssBackupComponents_PreRestore(IVssBackupComponents* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PreRestore(This,ppAsync);
}
static inline HRESULT IVssBackupComponents_PostRestore(IVssBackupComponents* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PostRestore(This,ppAsync);
}
static inline HRESULT IVssBackupComponents_SetContext(IVssBackupComponents* This,LONG lContext) {
    return This->lpVtbl->SetContext(This,lContext);
}
static inline HRESULT IVssBackupComponents_StartSnapshotSet(IVssBackupComponents* This,VSS_ID *pSnapshotSetId) {
    return This->lpVtbl->StartSnapshotSet(This,pSnapshotSetId);
}
static inline HRESULT IVssBackupComponents_AddToSnapshotSet(IVssBackupComponents* This,VSS_PWSZ pwszVolumeName,VSS_ID ProviderId,VSS_ID *pidSnapshot) {
    return This->lpVtbl->AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot);
}
static inline HRESULT IVssBackupComponents_DoSnapshotSet(IVssBackupComponents* This,IVssAsync **ppAsync) {
    return This->lpVtbl->DoSnapshotSet(This,ppAsync);
}
static inline HRESULT IVssBackupComponents_DeleteSnapshots(IVssBackupComponents* This,VSS_ID SourceObjectId,VSS_OBJECT_TYPE eSourceObjectType,WINBOOL bForceDelete,LONG *plDeletedSnapshots,VSS_ID *pNondeletedSnapshotID) {
    return This->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID);
}
static inline HRESULT IVssBackupComponents_ImportSnapshots(IVssBackupComponents* This,IVssAsync **ppAsync) {
    return This->lpVtbl->ImportSnapshots(This,ppAsync);
}
static inline HRESULT IVssBackupComponents_BreakSnapshotSet(IVssBackupComponents* This,VSS_ID SnapshotSetId) {
    return This->lpVtbl->BreakSnapshotSet(This,SnapshotSetId);
}
static inline HRESULT IVssBackupComponents_GetSnapshotProperties(IVssBackupComponents* This,VSS_ID SnapshotId,VSS_SNAPSHOT_PROP *pProp) {
    return This->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp);
}
static inline HRESULT IVssBackupComponents_Query(IVssBackupComponents* This,VSS_ID QueriedObjectId,VSS_OBJECT_TYPE eQueriedObjectType,VSS_OBJECT_TYPE eReturnedObjectsType,IVssEnumObject **ppEnum) {
    return This->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum);
}
static inline HRESULT IVssBackupComponents_IsVolumeSupported(IVssBackupComponents* This,VSS_ID ProviderId,VSS_PWSZ pwszVolumeName,WINBOOL *pbSupportedByThisProvider) {
    return This->lpVtbl->IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider);
}
static inline HRESULT IVssBackupComponents_DisableWriterClasses(IVssBackupComponents* This,const VSS_ID *rgWriterClassId,UINT cClassId) {
    return This->lpVtbl->DisableWriterClasses(This,rgWriterClassId,cClassId);
}
static inline HRESULT IVssBackupComponents_EnableWriterClasses(IVssBackupComponents* This,const VSS_ID *rgWriterClassId,UINT cClassId) {
    return This->lpVtbl->EnableWriterClasses(This,rgWriterClassId,cClassId);
}
static inline HRESULT IVssBackupComponents_DisableWriterInstances(IVssBackupComponents* This,const VSS_ID *rgWriterInstanceId,UINT cInstanceId) {
    return This->lpVtbl->DisableWriterInstances(This,rgWriterInstanceId,cInstanceId);
}
static inline HRESULT IVssBackupComponents_ExposeSnapshot(IVssBackupComponents* This,VSS_ID SnapshotId,VSS_PWSZ wszPathFromRoot,LONG lAttributes,VSS_PWSZ wszExpose,VSS_PWSZ *pwszExposed) {
    return This->lpVtbl->ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed);
}
static inline HRESULT IVssBackupComponents_RevertToSnapshot(IVssBackupComponents* This,VSS_ID SnapshotId,WINBOOL bForceDismount) {
    return This->lpVtbl->RevertToSnapshot(This,SnapshotId,bForceDismount);
}
static inline HRESULT IVssBackupComponents_QueryRevertStatus(IVssBackupComponents* This,VSS_PWSZ pwszVolume,IVssAsync **ppAsync) {
    return This->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync);
}
#endif
#endif

#endif


#endif  /* __IVssBackupComponents_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssBackupComponentsEx interface
 */
#ifndef __IVssBackupComponentsEx_INTERFACE_DEFINED__
#define __IVssBackupComponentsEx_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssBackupComponentsEx, 0x963f03ad, 0x9e4c, 0x4a34, 0xac,0x15, 0xe4,0xb6,0x17,0x4e,0x50,0x36);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("963f03ad-9e4c-4a34-ac15-e4b6174e5036")
IVssBackupComponentsEx : public IVssBackupComponents
{
    virtual HRESULT STDMETHODCALLTYPE GetWriterMetadataEx(
        UINT iWriter,
        VSS_ID *pidInstance,
        IVssExamineWriterMetadataEx **ppMetadata) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSelectedForRestoreEx(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSelectedForRestore,
        VSS_ID instanceId) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssBackupComponentsEx, 0x963f03ad, 0x9e4c, 0x4a34, 0xac,0x15, 0xe4,0xb6,0x17,0x4e,0x50,0x36)
#endif
#else
typedef struct IVssBackupComponentsExVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssBackupComponentsEx *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssBackupComponentsEx *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssBackupComponentsEx *This);

    /*** IVssBackupComponents methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterComponentsCount)(
        IVssBackupComponentsEx *This,
        UINT *pcComponents);

    HRESULT (STDMETHODCALLTYPE *GetWriterComponents)(
        IVssBackupComponentsEx *This,
        UINT iWriter,
        IVssWriterComponentsExt **ppWriter);

    HRESULT (STDMETHODCALLTYPE *InitializeForBackup)(
        IVssBackupComponentsEx *This,
        BSTR bstrXML);

    HRESULT (STDMETHODCALLTYPE *SetBackupState)(
        IVssBackupComponentsEx *This,
        boolean bSelectComponents,
        boolean bBackupBootableSystemState,
        VSS_BACKUP_TYPE backupType,
        boolean bPartialFileSupport);

    HRESULT (STDMETHODCALLTYPE *InitializeForRestore)(
        IVssBackupComponentsEx *This,
        BSTR bstrXML);

    HRESULT (STDMETHODCALLTYPE *SetRestoreState)(
        IVssBackupComponentsEx *This,
        VSS_RESTORE_TYPE restoreType);

    HRESULT (STDMETHODCALLTYPE *GatherWriterMetadata)(
        IVssBackupComponentsEx *This,
        IVssAsync **pAsync);

    HRESULT (STDMETHODCALLTYPE *GetWriterMetadataCount)(
        IVssBackupComponentsEx *This,
        UINT *pcWriters);

    HRESULT (STDMETHODCALLTYPE *GetWriterMetadata)(
        IVssBackupComponentsEx *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        IVssExamineWriterMetadata **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *FreeWriterMetadata)(
        IVssBackupComponentsEx *This);

    HRESULT (STDMETHODCALLTYPE *AddComponent)(
        IVssBackupComponentsEx *This,
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName);

    HRESULT (STDMETHODCALLTYPE *PrepareForBackup)(
        IVssBackupComponentsEx *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *AbortBackup)(
        IVssBackupComponentsEx *This);

    HRESULT (STDMETHODCALLTYPE *GatherWriterStatus)(
        IVssBackupComponentsEx *This,
        IVssAsync **pAsync);

    HRESULT (STDMETHODCALLTYPE *GetWriterStatusCount)(
        IVssBackupComponentsEx *This,
        UINT *pcWriters);

    HRESULT (STDMETHODCALLTYPE *FreeWriterStatus)(
        IVssBackupComponentsEx *This);

    HRESULT (STDMETHODCALLTYPE *GetWriterStatus)(
        IVssBackupComponentsEx *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriter,
        VSS_WRITER_STATE *pnStatus,
        HRESULT *phResultFailure);

    HRESULT (STDMETHODCALLTYPE *SetBackupSucceeded)(
        IVssBackupComponentsEx *This,
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSucceded);

    HRESULT (STDMETHODCALLTYPE *SetBackupOptions)(
        IVssBackupComponentsEx *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszBackupOptions);

    HRESULT (STDMETHODCALLTYPE *SetSelectedForRestore)(
        IVssBackupComponentsEx *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSelectedForRestore);

    HRESULT (STDMETHODCALLTYPE *SetRestoreOptions)(
        IVssBackupComponentsEx *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszRestoreOptions);

    HRESULT (STDMETHODCALLTYPE *SetAdditionalRestores)(
        IVssBackupComponentsEx *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bAdditionalRestores);

    HRESULT (STDMETHODCALLTYPE *SetPreviousBackupStamp)(
        IVssBackupComponentsEx *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPreviousBackupStamp);

    HRESULT (STDMETHODCALLTYPE *SaveAsXML)(
        IVssBackupComponentsEx *This,
        BSTR *pbstrXML);

    HRESULT (STDMETHODCALLTYPE *BackupComplete)(
        IVssBackupComponentsEx *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *AddAlternativeLocationMapping)(
        IVssBackupComponentsEx *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszDestination);

    HRESULT (STDMETHODCALLTYPE *AddRestoreSubcomponent)(
        IVssBackupComponentsEx *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszSubComponentLogicalPath,
        LPCWSTR wszSubComponentName,
        boolean bRepair);

    HRESULT (STDMETHODCALLTYPE *SetFileRestoreStatus)(
        IVssBackupComponentsEx *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        VSS_FILE_RESTORE_STATUS status);

    HRESULT (STDMETHODCALLTYPE *AddNewTarget)(
        IVssBackupComponentsEx *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFileName,
        boolean bRecursive,
        LPCWSTR wszAlternatePath);

    HRESULT (STDMETHODCALLTYPE *SetRangesFilePath)(
        IVssBackupComponentsEx *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        UINT iPartialFile,
        LPCWSTR wszRangesFile);

    HRESULT (STDMETHODCALLTYPE *PreRestore)(
        IVssBackupComponentsEx *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *PostRestore)(
        IVssBackupComponentsEx *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *SetContext)(
        IVssBackupComponentsEx *This,
        LONG lContext);

    HRESULT (STDMETHODCALLTYPE *StartSnapshotSet)(
        IVssBackupComponentsEx *This,
        VSS_ID *pSnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *AddToSnapshotSet)(
        IVssBackupComponentsEx *This,
        VSS_PWSZ pwszVolumeName,
        VSS_ID ProviderId,
        VSS_ID *pidSnapshot);

    HRESULT (STDMETHODCALLTYPE *DoSnapshotSet)(
        IVssBackupComponentsEx *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *DeleteSnapshots)(
        IVssBackupComponentsEx *This,
        VSS_ID SourceObjectId,
        VSS_OBJECT_TYPE eSourceObjectType,
        WINBOOL bForceDelete,
        LONG *plDeletedSnapshots,
        VSS_ID *pNondeletedSnapshotID);

    HRESULT (STDMETHODCALLTYPE *ImportSnapshots)(
        IVssBackupComponentsEx *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *BreakSnapshotSet)(
        IVssBackupComponentsEx *This,
        VSS_ID SnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *GetSnapshotProperties)(
        IVssBackupComponentsEx *This,
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROP *pProp);

    HRESULT (STDMETHODCALLTYPE *Query)(
        IVssBackupComponentsEx *This,
        VSS_ID QueriedObjectId,
        VSS_OBJECT_TYPE eQueriedObjectType,
        VSS_OBJECT_TYPE eReturnedObjectsType,
        IVssEnumObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *IsVolumeSupported)(
        IVssBackupComponentsEx *This,
        VSS_ID ProviderId,
        VSS_PWSZ pwszVolumeName,
        WINBOOL *pbSupportedByThisProvider);

    HRESULT (STDMETHODCALLTYPE *DisableWriterClasses)(
        IVssBackupComponentsEx *This,
        const VSS_ID *rgWriterClassId,
        UINT cClassId);

    HRESULT (STDMETHODCALLTYPE *EnableWriterClasses)(
        IVssBackupComponentsEx *This,
        const VSS_ID *rgWriterClassId,
        UINT cClassId);

    HRESULT (STDMETHODCALLTYPE *DisableWriterInstances)(
        IVssBackupComponentsEx *This,
        const VSS_ID *rgWriterInstanceId,
        UINT cInstanceId);

    HRESULT (STDMETHODCALLTYPE *ExposeSnapshot)(
        IVssBackupComponentsEx *This,
        VSS_ID SnapshotId,
        VSS_PWSZ wszPathFromRoot,
        LONG lAttributes,
        VSS_PWSZ wszExpose,
        VSS_PWSZ *pwszExposed);

    HRESULT (STDMETHODCALLTYPE *RevertToSnapshot)(
        IVssBackupComponentsEx *This,
        VSS_ID SnapshotId,
        WINBOOL bForceDismount);

    HRESULT (STDMETHODCALLTYPE *QueryRevertStatus)(
        IVssBackupComponentsEx *This,
        VSS_PWSZ pwszVolume,
        IVssAsync **ppAsync);

    /*** IVssBackupComponentsEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterMetadataEx)(
        IVssBackupComponentsEx *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        IVssExamineWriterMetadataEx **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *SetSelectedForRestoreEx)(
        IVssBackupComponentsEx *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSelectedForRestore,
        VSS_ID instanceId);

    END_INTERFACE
} IVssBackupComponentsExVtbl;

interface IVssBackupComponentsEx {
    CONST_VTBL IVssBackupComponentsExVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssBackupComponentsEx_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssBackupComponentsEx_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssBackupComponentsEx_Release(This) (This)->lpVtbl->Release(This)
/*** IVssBackupComponents methods ***/
#define IVssBackupComponentsEx_GetWriterComponentsCount(This,pcComponents) (This)->lpVtbl->GetWriterComponentsCount(This,pcComponents)
#define IVssBackupComponentsEx_GetWriterComponents(This,iWriter,ppWriter) (This)->lpVtbl->GetWriterComponents(This,iWriter,ppWriter)
#define IVssBackupComponentsEx_InitializeForBackup(This,bstrXML) (This)->lpVtbl->InitializeForBackup(This,bstrXML)
#define IVssBackupComponentsEx_SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport) (This)->lpVtbl->SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport)
#define IVssBackupComponentsEx_InitializeForRestore(This,bstrXML) (This)->lpVtbl->InitializeForRestore(This,bstrXML)
#define IVssBackupComponentsEx_SetRestoreState(This,restoreType) (This)->lpVtbl->SetRestoreState(This,restoreType)
#define IVssBackupComponentsEx_GatherWriterMetadata(This,pAsync) (This)->lpVtbl->GatherWriterMetadata(This,pAsync)
#define IVssBackupComponentsEx_GetWriterMetadataCount(This,pcWriters) (This)->lpVtbl->GetWriterMetadataCount(This,pcWriters)
#define IVssBackupComponentsEx_GetWriterMetadata(This,iWriter,pidInstance,ppMetadata) (This)->lpVtbl->GetWriterMetadata(This,iWriter,pidInstance,ppMetadata)
#define IVssBackupComponentsEx_FreeWriterMetadata(This) (This)->lpVtbl->FreeWriterMetadata(This)
#define IVssBackupComponentsEx_AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName) (This)->lpVtbl->AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName)
#define IVssBackupComponentsEx_PrepareForBackup(This,ppAsync) (This)->lpVtbl->PrepareForBackup(This,ppAsync)
#define IVssBackupComponentsEx_AbortBackup(This) (This)->lpVtbl->AbortBackup(This)
#define IVssBackupComponentsEx_GatherWriterStatus(This,pAsync) (This)->lpVtbl->GatherWriterStatus(This,pAsync)
#define IVssBackupComponentsEx_GetWriterStatusCount(This,pcWriters) (This)->lpVtbl->GetWriterStatusCount(This,pcWriters)
#define IVssBackupComponentsEx_FreeWriterStatus(This) (This)->lpVtbl->FreeWriterStatus(This)
#define IVssBackupComponentsEx_GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure) (This)->lpVtbl->GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure)
#define IVssBackupComponentsEx_SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded) (This)->lpVtbl->SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded)
#define IVssBackupComponentsEx_SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions) (This)->lpVtbl->SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions)
#define IVssBackupComponentsEx_SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore) (This)->lpVtbl->SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore)
#define IVssBackupComponentsEx_SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions) (This)->lpVtbl->SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions)
#define IVssBackupComponentsEx_SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores) (This)->lpVtbl->SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores)
#define IVssBackupComponentsEx_SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp) (This)->lpVtbl->SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp)
#define IVssBackupComponentsEx_SaveAsXML(This,pbstrXML) (This)->lpVtbl->SaveAsXML(This,pbstrXML)
#define IVssBackupComponentsEx_BackupComplete(This,ppAsync) (This)->lpVtbl->BackupComplete(This,ppAsync)
#define IVssBackupComponentsEx_AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination) (This)->lpVtbl->AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination)
#define IVssBackupComponentsEx_AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair) (This)->lpVtbl->AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair)
#define IVssBackupComponentsEx_SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status) (This)->lpVtbl->SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status)
#define IVssBackupComponentsEx_AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath) (This)->lpVtbl->AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath)
#define IVssBackupComponentsEx_SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile) (This)->lpVtbl->SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile)
#define IVssBackupComponentsEx_PreRestore(This,ppAsync) (This)->lpVtbl->PreRestore(This,ppAsync)
#define IVssBackupComponentsEx_PostRestore(This,ppAsync) (This)->lpVtbl->PostRestore(This,ppAsync)
#define IVssBackupComponentsEx_SetContext(This,lContext) (This)->lpVtbl->SetContext(This,lContext)
#define IVssBackupComponentsEx_StartSnapshotSet(This,pSnapshotSetId) (This)->lpVtbl->StartSnapshotSet(This,pSnapshotSetId)
#define IVssBackupComponentsEx_AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot) (This)->lpVtbl->AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot)
#define IVssBackupComponentsEx_DoSnapshotSet(This,ppAsync) (This)->lpVtbl->DoSnapshotSet(This,ppAsync)
#define IVssBackupComponentsEx_DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID) (This)->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID)
#define IVssBackupComponentsEx_ImportSnapshots(This,ppAsync) (This)->lpVtbl->ImportSnapshots(This,ppAsync)
#define IVssBackupComponentsEx_BreakSnapshotSet(This,SnapshotSetId) (This)->lpVtbl->BreakSnapshotSet(This,SnapshotSetId)
#define IVssBackupComponentsEx_GetSnapshotProperties(This,SnapshotId,pProp) (This)->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp)
#define IVssBackupComponentsEx_Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum) (This)->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum)
#define IVssBackupComponentsEx_IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider) (This)->lpVtbl->IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider)
#define IVssBackupComponentsEx_DisableWriterClasses(This,rgWriterClassId,cClassId) (This)->lpVtbl->DisableWriterClasses(This,rgWriterClassId,cClassId)
#define IVssBackupComponentsEx_EnableWriterClasses(This,rgWriterClassId,cClassId) (This)->lpVtbl->EnableWriterClasses(This,rgWriterClassId,cClassId)
#define IVssBackupComponentsEx_DisableWriterInstances(This,rgWriterInstanceId,cInstanceId) (This)->lpVtbl->DisableWriterInstances(This,rgWriterInstanceId,cInstanceId)
#define IVssBackupComponentsEx_ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed) (This)->lpVtbl->ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed)
#define IVssBackupComponentsEx_RevertToSnapshot(This,SnapshotId,bForceDismount) (This)->lpVtbl->RevertToSnapshot(This,SnapshotId,bForceDismount)
#define IVssBackupComponentsEx_QueryRevertStatus(This,pwszVolume,ppAsync) (This)->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync)
/*** IVssBackupComponentsEx methods ***/
#define IVssBackupComponentsEx_GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata) (This)->lpVtbl->GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata)
#define IVssBackupComponentsEx_SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId) (This)->lpVtbl->SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssBackupComponentsEx_QueryInterface(IVssBackupComponentsEx* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssBackupComponentsEx_AddRef(IVssBackupComponentsEx* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssBackupComponentsEx_Release(IVssBackupComponentsEx* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssBackupComponents methods ***/
static inline HRESULT IVssBackupComponentsEx_GetWriterComponentsCount(IVssBackupComponentsEx* This,UINT *pcComponents) {
    return This->lpVtbl->GetWriterComponentsCount(This,pcComponents);
}
static inline HRESULT IVssBackupComponentsEx_GetWriterComponents(IVssBackupComponentsEx* This,UINT iWriter,IVssWriterComponentsExt **ppWriter) {
    return This->lpVtbl->GetWriterComponents(This,iWriter,ppWriter);
}
static inline HRESULT IVssBackupComponentsEx_InitializeForBackup(IVssBackupComponentsEx* This,BSTR bstrXML) {
    return This->lpVtbl->InitializeForBackup(This,bstrXML);
}
static inline HRESULT IVssBackupComponentsEx_SetBackupState(IVssBackupComponentsEx* This,boolean bSelectComponents,boolean bBackupBootableSystemState,VSS_BACKUP_TYPE backupType,boolean bPartialFileSupport) {
    return This->lpVtbl->SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport);
}
static inline HRESULT IVssBackupComponentsEx_InitializeForRestore(IVssBackupComponentsEx* This,BSTR bstrXML) {
    return This->lpVtbl->InitializeForRestore(This,bstrXML);
}
static inline HRESULT IVssBackupComponentsEx_SetRestoreState(IVssBackupComponentsEx* This,VSS_RESTORE_TYPE restoreType) {
    return This->lpVtbl->SetRestoreState(This,restoreType);
}
static inline HRESULT IVssBackupComponentsEx_GatherWriterMetadata(IVssBackupComponentsEx* This,IVssAsync **pAsync) {
    return This->lpVtbl->GatherWriterMetadata(This,pAsync);
}
static inline HRESULT IVssBackupComponentsEx_GetWriterMetadataCount(IVssBackupComponentsEx* This,UINT *pcWriters) {
    return This->lpVtbl->GetWriterMetadataCount(This,pcWriters);
}
static inline HRESULT IVssBackupComponentsEx_GetWriterMetadata(IVssBackupComponentsEx* This,UINT iWriter,VSS_ID *pidInstance,IVssExamineWriterMetadata **ppMetadata) {
    return This->lpVtbl->GetWriterMetadata(This,iWriter,pidInstance,ppMetadata);
}
static inline HRESULT IVssBackupComponentsEx_FreeWriterMetadata(IVssBackupComponentsEx* This) {
    return This->lpVtbl->FreeWriterMetadata(This);
}
static inline HRESULT IVssBackupComponentsEx_AddComponent(IVssBackupComponentsEx* This,VSS_ID instanceId,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName) {
    return This->lpVtbl->AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName);
}
static inline HRESULT IVssBackupComponentsEx_PrepareForBackup(IVssBackupComponentsEx* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PrepareForBackup(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx_AbortBackup(IVssBackupComponentsEx* This) {
    return This->lpVtbl->AbortBackup(This);
}
static inline HRESULT IVssBackupComponentsEx_GatherWriterStatus(IVssBackupComponentsEx* This,IVssAsync **pAsync) {
    return This->lpVtbl->GatherWriterStatus(This,pAsync);
}
static inline HRESULT IVssBackupComponentsEx_GetWriterStatusCount(IVssBackupComponentsEx* This,UINT *pcWriters) {
    return This->lpVtbl->GetWriterStatusCount(This,pcWriters);
}
static inline HRESULT IVssBackupComponentsEx_FreeWriterStatus(IVssBackupComponentsEx* This) {
    return This->lpVtbl->FreeWriterStatus(This);
}
static inline HRESULT IVssBackupComponentsEx_GetWriterStatus(IVssBackupComponentsEx* This,UINT iWriter,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriter,VSS_WRITER_STATE *pnStatus,HRESULT *phResultFailure) {
    return This->lpVtbl->GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure);
}
static inline HRESULT IVssBackupComponentsEx_SetBackupSucceeded(IVssBackupComponentsEx* This,VSS_ID instanceId,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSucceded) {
    return This->lpVtbl->SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded);
}
static inline HRESULT IVssBackupComponentsEx_SetBackupOptions(IVssBackupComponentsEx* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszBackupOptions) {
    return This->lpVtbl->SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions);
}
static inline HRESULT IVssBackupComponentsEx_SetSelectedForRestore(IVssBackupComponentsEx* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSelectedForRestore) {
    return This->lpVtbl->SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore);
}
static inline HRESULT IVssBackupComponentsEx_SetRestoreOptions(IVssBackupComponentsEx* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszRestoreOptions) {
    return This->lpVtbl->SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions);
}
static inline HRESULT IVssBackupComponentsEx_SetAdditionalRestores(IVssBackupComponentsEx* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bAdditionalRestores) {
    return This->lpVtbl->SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores);
}
static inline HRESULT IVssBackupComponentsEx_SetPreviousBackupStamp(IVssBackupComponentsEx* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPreviousBackupStamp) {
    return This->lpVtbl->SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp);
}
static inline HRESULT IVssBackupComponentsEx_SaveAsXML(IVssBackupComponentsEx* This,BSTR *pbstrXML) {
    return This->lpVtbl->SaveAsXML(This,pbstrXML);
}
static inline HRESULT IVssBackupComponentsEx_BackupComplete(IVssBackupComponentsEx* This,IVssAsync **ppAsync) {
    return This->lpVtbl->BackupComplete(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx_AddAlternativeLocationMapping(IVssBackupComponentsEx* This,VSS_ID writerId,VSS_COMPONENT_TYPE componentType,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive,LPCWSTR wszDestination) {
    return This->lpVtbl->AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination);
}
static inline HRESULT IVssBackupComponentsEx_AddRestoreSubcomponent(IVssBackupComponentsEx* This,VSS_ID writerId,VSS_COMPONENT_TYPE componentType,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszSubComponentLogicalPath,LPCWSTR wszSubComponentName,boolean bRepair) {
    return This->lpVtbl->AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair);
}
static inline HRESULT IVssBackupComponentsEx_SetFileRestoreStatus(IVssBackupComponentsEx* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,VSS_FILE_RESTORE_STATUS status) {
    return This->lpVtbl->SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status);
}
static inline HRESULT IVssBackupComponentsEx_AddNewTarget(IVssBackupComponentsEx* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPath,LPCWSTR wszFileName,boolean bRecursive,LPCWSTR wszAlternatePath) {
    return This->lpVtbl->AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath);
}
static inline HRESULT IVssBackupComponentsEx_SetRangesFilePath(IVssBackupComponentsEx* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,UINT iPartialFile,LPCWSTR wszRangesFile) {
    return This->lpVtbl->SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile);
}
static inline HRESULT IVssBackupComponentsEx_PreRestore(IVssBackupComponentsEx* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PreRestore(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx_PostRestore(IVssBackupComponentsEx* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PostRestore(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx_SetContext(IVssBackupComponentsEx* This,LONG lContext) {
    return This->lpVtbl->SetContext(This,lContext);
}
static inline HRESULT IVssBackupComponentsEx_StartSnapshotSet(IVssBackupComponentsEx* This,VSS_ID *pSnapshotSetId) {
    return This->lpVtbl->StartSnapshotSet(This,pSnapshotSetId);
}
static inline HRESULT IVssBackupComponentsEx_AddToSnapshotSet(IVssBackupComponentsEx* This,VSS_PWSZ pwszVolumeName,VSS_ID ProviderId,VSS_ID *pidSnapshot) {
    return This->lpVtbl->AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot);
}
static inline HRESULT IVssBackupComponentsEx_DoSnapshotSet(IVssBackupComponentsEx* This,IVssAsync **ppAsync) {
    return This->lpVtbl->DoSnapshotSet(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx_DeleteSnapshots(IVssBackupComponentsEx* This,VSS_ID SourceObjectId,VSS_OBJECT_TYPE eSourceObjectType,WINBOOL bForceDelete,LONG *plDeletedSnapshots,VSS_ID *pNondeletedSnapshotID) {
    return This->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID);
}
static inline HRESULT IVssBackupComponentsEx_ImportSnapshots(IVssBackupComponentsEx* This,IVssAsync **ppAsync) {
    return This->lpVtbl->ImportSnapshots(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx_BreakSnapshotSet(IVssBackupComponentsEx* This,VSS_ID SnapshotSetId) {
    return This->lpVtbl->BreakSnapshotSet(This,SnapshotSetId);
}
static inline HRESULT IVssBackupComponentsEx_GetSnapshotProperties(IVssBackupComponentsEx* This,VSS_ID SnapshotId,VSS_SNAPSHOT_PROP *pProp) {
    return This->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp);
}
static inline HRESULT IVssBackupComponentsEx_Query(IVssBackupComponentsEx* This,VSS_ID QueriedObjectId,VSS_OBJECT_TYPE eQueriedObjectType,VSS_OBJECT_TYPE eReturnedObjectsType,IVssEnumObject **ppEnum) {
    return This->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum);
}
static inline HRESULT IVssBackupComponentsEx_IsVolumeSupported(IVssBackupComponentsEx* This,VSS_ID ProviderId,VSS_PWSZ pwszVolumeName,WINBOOL *pbSupportedByThisProvider) {
    return This->lpVtbl->IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider);
}
static inline HRESULT IVssBackupComponentsEx_DisableWriterClasses(IVssBackupComponentsEx* This,const VSS_ID *rgWriterClassId,UINT cClassId) {
    return This->lpVtbl->DisableWriterClasses(This,rgWriterClassId,cClassId);
}
static inline HRESULT IVssBackupComponentsEx_EnableWriterClasses(IVssBackupComponentsEx* This,const VSS_ID *rgWriterClassId,UINT cClassId) {
    return This->lpVtbl->EnableWriterClasses(This,rgWriterClassId,cClassId);
}
static inline HRESULT IVssBackupComponentsEx_DisableWriterInstances(IVssBackupComponentsEx* This,const VSS_ID *rgWriterInstanceId,UINT cInstanceId) {
    return This->lpVtbl->DisableWriterInstances(This,rgWriterInstanceId,cInstanceId);
}
static inline HRESULT IVssBackupComponentsEx_ExposeSnapshot(IVssBackupComponentsEx* This,VSS_ID SnapshotId,VSS_PWSZ wszPathFromRoot,LONG lAttributes,VSS_PWSZ wszExpose,VSS_PWSZ *pwszExposed) {
    return This->lpVtbl->ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed);
}
static inline HRESULT IVssBackupComponentsEx_RevertToSnapshot(IVssBackupComponentsEx* This,VSS_ID SnapshotId,WINBOOL bForceDismount) {
    return This->lpVtbl->RevertToSnapshot(This,SnapshotId,bForceDismount);
}
static inline HRESULT IVssBackupComponentsEx_QueryRevertStatus(IVssBackupComponentsEx* This,VSS_PWSZ pwszVolume,IVssAsync **ppAsync) {
    return This->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync);
}
/*** IVssBackupComponentsEx methods ***/
static inline HRESULT IVssBackupComponentsEx_GetWriterMetadataEx(IVssBackupComponentsEx* This,UINT iWriter,VSS_ID *pidInstance,IVssExamineWriterMetadataEx **ppMetadata) {
    return This->lpVtbl->GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata);
}
static inline HRESULT IVssBackupComponentsEx_SetSelectedForRestoreEx(IVssBackupComponentsEx* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSelectedForRestore,VSS_ID instanceId) {
    return This->lpVtbl->SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId);
}
#endif
#endif

#endif


#endif  /* __IVssBackupComponentsEx_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssBackupComponentsEx2 interface
 */
#ifndef __IVssBackupComponentsEx2_INTERFACE_DEFINED__
#define __IVssBackupComponentsEx2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssBackupComponentsEx2, 0xacfe2b3a, 0x22c9, 0x4ef8, 0xbd,0x03, 0x2f,0x9c,0xa2,0x30,0x08,0x4e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("acfe2b3a-22c9-4ef8-bd03-2f9ca230084e")
IVssBackupComponentsEx2 : public IVssBackupComponentsEx
{
    virtual HRESULT STDMETHODCALLTYPE UnexposeSnapshot(
        VSS_ID snapshotId) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetAuthoritativeRestore(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bAuth) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRollForward(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        VSS_ROLLFORWARD_TYPE rollType,
        LPCWSTR wszRollForwardPoint) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRestoreName(
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszRestoreName) = 0;

    virtual HRESULT STDMETHODCALLTYPE BreakSnapshotSetEx(
        VSS_ID SnapshotSetID,
        DWORD dwBreakFlags,
        IVssAsync **ppAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE PreFastRecovery(
        VSS_ID SnapshotSetID,
        DWORD dwPreFastRecoveryFlags,
        IVssAsync **ppAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE FastRecovery(
        VSS_ID SnapshotSetID,
        DWORD dwFastRecoveryFlags,
        IVssAsync **ppAsync) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssBackupComponentsEx2, 0xacfe2b3a, 0x22c9, 0x4ef8, 0xbd,0x03, 0x2f,0x9c,0xa2,0x30,0x08,0x4e)
#endif
#else
typedef struct IVssBackupComponentsEx2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssBackupComponentsEx2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssBackupComponentsEx2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssBackupComponentsEx2 *This);

    /*** IVssBackupComponents methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterComponentsCount)(
        IVssBackupComponentsEx2 *This,
        UINT *pcComponents);

    HRESULT (STDMETHODCALLTYPE *GetWriterComponents)(
        IVssBackupComponentsEx2 *This,
        UINT iWriter,
        IVssWriterComponentsExt **ppWriter);

    HRESULT (STDMETHODCALLTYPE *InitializeForBackup)(
        IVssBackupComponentsEx2 *This,
        BSTR bstrXML);

    HRESULT (STDMETHODCALLTYPE *SetBackupState)(
        IVssBackupComponentsEx2 *This,
        boolean bSelectComponents,
        boolean bBackupBootableSystemState,
        VSS_BACKUP_TYPE backupType,
        boolean bPartialFileSupport);

    HRESULT (STDMETHODCALLTYPE *InitializeForRestore)(
        IVssBackupComponentsEx2 *This,
        BSTR bstrXML);

    HRESULT (STDMETHODCALLTYPE *SetRestoreState)(
        IVssBackupComponentsEx2 *This,
        VSS_RESTORE_TYPE restoreType);

    HRESULT (STDMETHODCALLTYPE *GatherWriterMetadata)(
        IVssBackupComponentsEx2 *This,
        IVssAsync **pAsync);

    HRESULT (STDMETHODCALLTYPE *GetWriterMetadataCount)(
        IVssBackupComponentsEx2 *This,
        UINT *pcWriters);

    HRESULT (STDMETHODCALLTYPE *GetWriterMetadata)(
        IVssBackupComponentsEx2 *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        IVssExamineWriterMetadata **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *FreeWriterMetadata)(
        IVssBackupComponentsEx2 *This);

    HRESULT (STDMETHODCALLTYPE *AddComponent)(
        IVssBackupComponentsEx2 *This,
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName);

    HRESULT (STDMETHODCALLTYPE *PrepareForBackup)(
        IVssBackupComponentsEx2 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *AbortBackup)(
        IVssBackupComponentsEx2 *This);

    HRESULT (STDMETHODCALLTYPE *GatherWriterStatus)(
        IVssBackupComponentsEx2 *This,
        IVssAsync **pAsync);

    HRESULT (STDMETHODCALLTYPE *GetWriterStatusCount)(
        IVssBackupComponentsEx2 *This,
        UINT *pcWriters);

    HRESULT (STDMETHODCALLTYPE *FreeWriterStatus)(
        IVssBackupComponentsEx2 *This);

    HRESULT (STDMETHODCALLTYPE *GetWriterStatus)(
        IVssBackupComponentsEx2 *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriter,
        VSS_WRITER_STATE *pnStatus,
        HRESULT *phResultFailure);

    HRESULT (STDMETHODCALLTYPE *SetBackupSucceeded)(
        IVssBackupComponentsEx2 *This,
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSucceded);

    HRESULT (STDMETHODCALLTYPE *SetBackupOptions)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszBackupOptions);

    HRESULT (STDMETHODCALLTYPE *SetSelectedForRestore)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSelectedForRestore);

    HRESULT (STDMETHODCALLTYPE *SetRestoreOptions)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszRestoreOptions);

    HRESULT (STDMETHODCALLTYPE *SetAdditionalRestores)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bAdditionalRestores);

    HRESULT (STDMETHODCALLTYPE *SetPreviousBackupStamp)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPreviousBackupStamp);

    HRESULT (STDMETHODCALLTYPE *SaveAsXML)(
        IVssBackupComponentsEx2 *This,
        BSTR *pbstrXML);

    HRESULT (STDMETHODCALLTYPE *BackupComplete)(
        IVssBackupComponentsEx2 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *AddAlternativeLocationMapping)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszDestination);

    HRESULT (STDMETHODCALLTYPE *AddRestoreSubcomponent)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszSubComponentLogicalPath,
        LPCWSTR wszSubComponentName,
        boolean bRepair);

    HRESULT (STDMETHODCALLTYPE *SetFileRestoreStatus)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        VSS_FILE_RESTORE_STATUS status);

    HRESULT (STDMETHODCALLTYPE *AddNewTarget)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFileName,
        boolean bRecursive,
        LPCWSTR wszAlternatePath);

    HRESULT (STDMETHODCALLTYPE *SetRangesFilePath)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        UINT iPartialFile,
        LPCWSTR wszRangesFile);

    HRESULT (STDMETHODCALLTYPE *PreRestore)(
        IVssBackupComponentsEx2 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *PostRestore)(
        IVssBackupComponentsEx2 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *SetContext)(
        IVssBackupComponentsEx2 *This,
        LONG lContext);

    HRESULT (STDMETHODCALLTYPE *StartSnapshotSet)(
        IVssBackupComponentsEx2 *This,
        VSS_ID *pSnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *AddToSnapshotSet)(
        IVssBackupComponentsEx2 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_ID ProviderId,
        VSS_ID *pidSnapshot);

    HRESULT (STDMETHODCALLTYPE *DoSnapshotSet)(
        IVssBackupComponentsEx2 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *DeleteSnapshots)(
        IVssBackupComponentsEx2 *This,
        VSS_ID SourceObjectId,
        VSS_OBJECT_TYPE eSourceObjectType,
        WINBOOL bForceDelete,
        LONG *plDeletedSnapshots,
        VSS_ID *pNondeletedSnapshotID);

    HRESULT (STDMETHODCALLTYPE *ImportSnapshots)(
        IVssBackupComponentsEx2 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *BreakSnapshotSet)(
        IVssBackupComponentsEx2 *This,
        VSS_ID SnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *GetSnapshotProperties)(
        IVssBackupComponentsEx2 *This,
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROP *pProp);

    HRESULT (STDMETHODCALLTYPE *Query)(
        IVssBackupComponentsEx2 *This,
        VSS_ID QueriedObjectId,
        VSS_OBJECT_TYPE eQueriedObjectType,
        VSS_OBJECT_TYPE eReturnedObjectsType,
        IVssEnumObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *IsVolumeSupported)(
        IVssBackupComponentsEx2 *This,
        VSS_ID ProviderId,
        VSS_PWSZ pwszVolumeName,
        WINBOOL *pbSupportedByThisProvider);

    HRESULT (STDMETHODCALLTYPE *DisableWriterClasses)(
        IVssBackupComponentsEx2 *This,
        const VSS_ID *rgWriterClassId,
        UINT cClassId);

    HRESULT (STDMETHODCALLTYPE *EnableWriterClasses)(
        IVssBackupComponentsEx2 *This,
        const VSS_ID *rgWriterClassId,
        UINT cClassId);

    HRESULT (STDMETHODCALLTYPE *DisableWriterInstances)(
        IVssBackupComponentsEx2 *This,
        const VSS_ID *rgWriterInstanceId,
        UINT cInstanceId);

    HRESULT (STDMETHODCALLTYPE *ExposeSnapshot)(
        IVssBackupComponentsEx2 *This,
        VSS_ID SnapshotId,
        VSS_PWSZ wszPathFromRoot,
        LONG lAttributes,
        VSS_PWSZ wszExpose,
        VSS_PWSZ *pwszExposed);

    HRESULT (STDMETHODCALLTYPE *RevertToSnapshot)(
        IVssBackupComponentsEx2 *This,
        VSS_ID SnapshotId,
        WINBOOL bForceDismount);

    HRESULT (STDMETHODCALLTYPE *QueryRevertStatus)(
        IVssBackupComponentsEx2 *This,
        VSS_PWSZ pwszVolume,
        IVssAsync **ppAsync);

    /*** IVssBackupComponentsEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterMetadataEx)(
        IVssBackupComponentsEx2 *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        IVssExamineWriterMetadataEx **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *SetSelectedForRestoreEx)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSelectedForRestore,
        VSS_ID instanceId);

    /*** IVssBackupComponentsEx2 methods ***/
    HRESULT (STDMETHODCALLTYPE *UnexposeSnapshot)(
        IVssBackupComponentsEx2 *This,
        VSS_ID snapshotId);

    HRESULT (STDMETHODCALLTYPE *SetAuthoritativeRestore)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bAuth);

    HRESULT (STDMETHODCALLTYPE *SetRollForward)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        VSS_ROLLFORWARD_TYPE rollType,
        LPCWSTR wszRollForwardPoint);

    HRESULT (STDMETHODCALLTYPE *SetRestoreName)(
        IVssBackupComponentsEx2 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszRestoreName);

    HRESULT (STDMETHODCALLTYPE *BreakSnapshotSetEx)(
        IVssBackupComponentsEx2 *This,
        VSS_ID SnapshotSetID,
        DWORD dwBreakFlags,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *PreFastRecovery)(
        IVssBackupComponentsEx2 *This,
        VSS_ID SnapshotSetID,
        DWORD dwPreFastRecoveryFlags,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *FastRecovery)(
        IVssBackupComponentsEx2 *This,
        VSS_ID SnapshotSetID,
        DWORD dwFastRecoveryFlags,
        IVssAsync **ppAsync);

    END_INTERFACE
} IVssBackupComponentsEx2Vtbl;

interface IVssBackupComponentsEx2 {
    CONST_VTBL IVssBackupComponentsEx2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssBackupComponentsEx2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssBackupComponentsEx2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssBackupComponentsEx2_Release(This) (This)->lpVtbl->Release(This)
/*** IVssBackupComponents methods ***/
#define IVssBackupComponentsEx2_GetWriterComponentsCount(This,pcComponents) (This)->lpVtbl->GetWriterComponentsCount(This,pcComponents)
#define IVssBackupComponentsEx2_GetWriterComponents(This,iWriter,ppWriter) (This)->lpVtbl->GetWriterComponents(This,iWriter,ppWriter)
#define IVssBackupComponentsEx2_InitializeForBackup(This,bstrXML) (This)->lpVtbl->InitializeForBackup(This,bstrXML)
#define IVssBackupComponentsEx2_SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport) (This)->lpVtbl->SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport)
#define IVssBackupComponentsEx2_InitializeForRestore(This,bstrXML) (This)->lpVtbl->InitializeForRestore(This,bstrXML)
#define IVssBackupComponentsEx2_SetRestoreState(This,restoreType) (This)->lpVtbl->SetRestoreState(This,restoreType)
#define IVssBackupComponentsEx2_GatherWriterMetadata(This,pAsync) (This)->lpVtbl->GatherWriterMetadata(This,pAsync)
#define IVssBackupComponentsEx2_GetWriterMetadataCount(This,pcWriters) (This)->lpVtbl->GetWriterMetadataCount(This,pcWriters)
#define IVssBackupComponentsEx2_GetWriterMetadata(This,iWriter,pidInstance,ppMetadata) (This)->lpVtbl->GetWriterMetadata(This,iWriter,pidInstance,ppMetadata)
#define IVssBackupComponentsEx2_FreeWriterMetadata(This) (This)->lpVtbl->FreeWriterMetadata(This)
#define IVssBackupComponentsEx2_AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName) (This)->lpVtbl->AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName)
#define IVssBackupComponentsEx2_PrepareForBackup(This,ppAsync) (This)->lpVtbl->PrepareForBackup(This,ppAsync)
#define IVssBackupComponentsEx2_AbortBackup(This) (This)->lpVtbl->AbortBackup(This)
#define IVssBackupComponentsEx2_GatherWriterStatus(This,pAsync) (This)->lpVtbl->GatherWriterStatus(This,pAsync)
#define IVssBackupComponentsEx2_GetWriterStatusCount(This,pcWriters) (This)->lpVtbl->GetWriterStatusCount(This,pcWriters)
#define IVssBackupComponentsEx2_FreeWriterStatus(This) (This)->lpVtbl->FreeWriterStatus(This)
#define IVssBackupComponentsEx2_GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure) (This)->lpVtbl->GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure)
#define IVssBackupComponentsEx2_SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded) (This)->lpVtbl->SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded)
#define IVssBackupComponentsEx2_SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions) (This)->lpVtbl->SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions)
#define IVssBackupComponentsEx2_SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore) (This)->lpVtbl->SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore)
#define IVssBackupComponentsEx2_SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions) (This)->lpVtbl->SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions)
#define IVssBackupComponentsEx2_SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores) (This)->lpVtbl->SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores)
#define IVssBackupComponentsEx2_SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp) (This)->lpVtbl->SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp)
#define IVssBackupComponentsEx2_SaveAsXML(This,pbstrXML) (This)->lpVtbl->SaveAsXML(This,pbstrXML)
#define IVssBackupComponentsEx2_BackupComplete(This,ppAsync) (This)->lpVtbl->BackupComplete(This,ppAsync)
#define IVssBackupComponentsEx2_AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination) (This)->lpVtbl->AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination)
#define IVssBackupComponentsEx2_AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair) (This)->lpVtbl->AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair)
#define IVssBackupComponentsEx2_SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status) (This)->lpVtbl->SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status)
#define IVssBackupComponentsEx2_AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath) (This)->lpVtbl->AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath)
#define IVssBackupComponentsEx2_SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile) (This)->lpVtbl->SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile)
#define IVssBackupComponentsEx2_PreRestore(This,ppAsync) (This)->lpVtbl->PreRestore(This,ppAsync)
#define IVssBackupComponentsEx2_PostRestore(This,ppAsync) (This)->lpVtbl->PostRestore(This,ppAsync)
#define IVssBackupComponentsEx2_SetContext(This,lContext) (This)->lpVtbl->SetContext(This,lContext)
#define IVssBackupComponentsEx2_StartSnapshotSet(This,pSnapshotSetId) (This)->lpVtbl->StartSnapshotSet(This,pSnapshotSetId)
#define IVssBackupComponentsEx2_AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot) (This)->lpVtbl->AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot)
#define IVssBackupComponentsEx2_DoSnapshotSet(This,ppAsync) (This)->lpVtbl->DoSnapshotSet(This,ppAsync)
#define IVssBackupComponentsEx2_DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID) (This)->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID)
#define IVssBackupComponentsEx2_ImportSnapshots(This,ppAsync) (This)->lpVtbl->ImportSnapshots(This,ppAsync)
#define IVssBackupComponentsEx2_BreakSnapshotSet(This,SnapshotSetId) (This)->lpVtbl->BreakSnapshotSet(This,SnapshotSetId)
#define IVssBackupComponentsEx2_GetSnapshotProperties(This,SnapshotId,pProp) (This)->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp)
#define IVssBackupComponentsEx2_Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum) (This)->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum)
#define IVssBackupComponentsEx2_IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider) (This)->lpVtbl->IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider)
#define IVssBackupComponentsEx2_DisableWriterClasses(This,rgWriterClassId,cClassId) (This)->lpVtbl->DisableWriterClasses(This,rgWriterClassId,cClassId)
#define IVssBackupComponentsEx2_EnableWriterClasses(This,rgWriterClassId,cClassId) (This)->lpVtbl->EnableWriterClasses(This,rgWriterClassId,cClassId)
#define IVssBackupComponentsEx2_DisableWriterInstances(This,rgWriterInstanceId,cInstanceId) (This)->lpVtbl->DisableWriterInstances(This,rgWriterInstanceId,cInstanceId)
#define IVssBackupComponentsEx2_ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed) (This)->lpVtbl->ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed)
#define IVssBackupComponentsEx2_RevertToSnapshot(This,SnapshotId,bForceDismount) (This)->lpVtbl->RevertToSnapshot(This,SnapshotId,bForceDismount)
#define IVssBackupComponentsEx2_QueryRevertStatus(This,pwszVolume,ppAsync) (This)->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync)
/*** IVssBackupComponentsEx methods ***/
#define IVssBackupComponentsEx2_GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata) (This)->lpVtbl->GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata)
#define IVssBackupComponentsEx2_SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId) (This)->lpVtbl->SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId)
/*** IVssBackupComponentsEx2 methods ***/
#define IVssBackupComponentsEx2_UnexposeSnapshot(This,snapshotId) (This)->lpVtbl->UnexposeSnapshot(This,snapshotId)
#define IVssBackupComponentsEx2_SetAuthoritativeRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bAuth) (This)->lpVtbl->SetAuthoritativeRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bAuth)
#define IVssBackupComponentsEx2_SetRollForward(This,writerId,ct,wszLogicalPath,wszComponentName,rollType,wszRollForwardPoint) (This)->lpVtbl->SetRollForward(This,writerId,ct,wszLogicalPath,wszComponentName,rollType,wszRollForwardPoint)
#define IVssBackupComponentsEx2_SetRestoreName(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreName) (This)->lpVtbl->SetRestoreName(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreName)
#define IVssBackupComponentsEx2_BreakSnapshotSetEx(This,SnapshotSetID,dwBreakFlags,ppAsync) (This)->lpVtbl->BreakSnapshotSetEx(This,SnapshotSetID,dwBreakFlags,ppAsync)
#define IVssBackupComponentsEx2_PreFastRecovery(This,SnapshotSetID,dwPreFastRecoveryFlags,ppAsync) (This)->lpVtbl->PreFastRecovery(This,SnapshotSetID,dwPreFastRecoveryFlags,ppAsync)
#define IVssBackupComponentsEx2_FastRecovery(This,SnapshotSetID,dwFastRecoveryFlags,ppAsync) (This)->lpVtbl->FastRecovery(This,SnapshotSetID,dwFastRecoveryFlags,ppAsync)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssBackupComponentsEx2_QueryInterface(IVssBackupComponentsEx2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssBackupComponentsEx2_AddRef(IVssBackupComponentsEx2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssBackupComponentsEx2_Release(IVssBackupComponentsEx2* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssBackupComponents methods ***/
static inline HRESULT IVssBackupComponentsEx2_GetWriterComponentsCount(IVssBackupComponentsEx2* This,UINT *pcComponents) {
    return This->lpVtbl->GetWriterComponentsCount(This,pcComponents);
}
static inline HRESULT IVssBackupComponentsEx2_GetWriterComponents(IVssBackupComponentsEx2* This,UINT iWriter,IVssWriterComponentsExt **ppWriter) {
    return This->lpVtbl->GetWriterComponents(This,iWriter,ppWriter);
}
static inline HRESULT IVssBackupComponentsEx2_InitializeForBackup(IVssBackupComponentsEx2* This,BSTR bstrXML) {
    return This->lpVtbl->InitializeForBackup(This,bstrXML);
}
static inline HRESULT IVssBackupComponentsEx2_SetBackupState(IVssBackupComponentsEx2* This,boolean bSelectComponents,boolean bBackupBootableSystemState,VSS_BACKUP_TYPE backupType,boolean bPartialFileSupport) {
    return This->lpVtbl->SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport);
}
static inline HRESULT IVssBackupComponentsEx2_InitializeForRestore(IVssBackupComponentsEx2* This,BSTR bstrXML) {
    return This->lpVtbl->InitializeForRestore(This,bstrXML);
}
static inline HRESULT IVssBackupComponentsEx2_SetRestoreState(IVssBackupComponentsEx2* This,VSS_RESTORE_TYPE restoreType) {
    return This->lpVtbl->SetRestoreState(This,restoreType);
}
static inline HRESULT IVssBackupComponentsEx2_GatherWriterMetadata(IVssBackupComponentsEx2* This,IVssAsync **pAsync) {
    return This->lpVtbl->GatherWriterMetadata(This,pAsync);
}
static inline HRESULT IVssBackupComponentsEx2_GetWriterMetadataCount(IVssBackupComponentsEx2* This,UINT *pcWriters) {
    return This->lpVtbl->GetWriterMetadataCount(This,pcWriters);
}
static inline HRESULT IVssBackupComponentsEx2_GetWriterMetadata(IVssBackupComponentsEx2* This,UINT iWriter,VSS_ID *pidInstance,IVssExamineWriterMetadata **ppMetadata) {
    return This->lpVtbl->GetWriterMetadata(This,iWriter,pidInstance,ppMetadata);
}
static inline HRESULT IVssBackupComponentsEx2_FreeWriterMetadata(IVssBackupComponentsEx2* This) {
    return This->lpVtbl->FreeWriterMetadata(This);
}
static inline HRESULT IVssBackupComponentsEx2_AddComponent(IVssBackupComponentsEx2* This,VSS_ID instanceId,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName) {
    return This->lpVtbl->AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName);
}
static inline HRESULT IVssBackupComponentsEx2_PrepareForBackup(IVssBackupComponentsEx2* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PrepareForBackup(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx2_AbortBackup(IVssBackupComponentsEx2* This) {
    return This->lpVtbl->AbortBackup(This);
}
static inline HRESULT IVssBackupComponentsEx2_GatherWriterStatus(IVssBackupComponentsEx2* This,IVssAsync **pAsync) {
    return This->lpVtbl->GatherWriterStatus(This,pAsync);
}
static inline HRESULT IVssBackupComponentsEx2_GetWriterStatusCount(IVssBackupComponentsEx2* This,UINT *pcWriters) {
    return This->lpVtbl->GetWriterStatusCount(This,pcWriters);
}
static inline HRESULT IVssBackupComponentsEx2_FreeWriterStatus(IVssBackupComponentsEx2* This) {
    return This->lpVtbl->FreeWriterStatus(This);
}
static inline HRESULT IVssBackupComponentsEx2_GetWriterStatus(IVssBackupComponentsEx2* This,UINT iWriter,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriter,VSS_WRITER_STATE *pnStatus,HRESULT *phResultFailure) {
    return This->lpVtbl->GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure);
}
static inline HRESULT IVssBackupComponentsEx2_SetBackupSucceeded(IVssBackupComponentsEx2* This,VSS_ID instanceId,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSucceded) {
    return This->lpVtbl->SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded);
}
static inline HRESULT IVssBackupComponentsEx2_SetBackupOptions(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszBackupOptions) {
    return This->lpVtbl->SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions);
}
static inline HRESULT IVssBackupComponentsEx2_SetSelectedForRestore(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSelectedForRestore) {
    return This->lpVtbl->SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore);
}
static inline HRESULT IVssBackupComponentsEx2_SetRestoreOptions(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszRestoreOptions) {
    return This->lpVtbl->SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions);
}
static inline HRESULT IVssBackupComponentsEx2_SetAdditionalRestores(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bAdditionalRestores) {
    return This->lpVtbl->SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores);
}
static inline HRESULT IVssBackupComponentsEx2_SetPreviousBackupStamp(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPreviousBackupStamp) {
    return This->lpVtbl->SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp);
}
static inline HRESULT IVssBackupComponentsEx2_SaveAsXML(IVssBackupComponentsEx2* This,BSTR *pbstrXML) {
    return This->lpVtbl->SaveAsXML(This,pbstrXML);
}
static inline HRESULT IVssBackupComponentsEx2_BackupComplete(IVssBackupComponentsEx2* This,IVssAsync **ppAsync) {
    return This->lpVtbl->BackupComplete(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx2_AddAlternativeLocationMapping(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE componentType,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive,LPCWSTR wszDestination) {
    return This->lpVtbl->AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination);
}
static inline HRESULT IVssBackupComponentsEx2_AddRestoreSubcomponent(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE componentType,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszSubComponentLogicalPath,LPCWSTR wszSubComponentName,boolean bRepair) {
    return This->lpVtbl->AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair);
}
static inline HRESULT IVssBackupComponentsEx2_SetFileRestoreStatus(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,VSS_FILE_RESTORE_STATUS status) {
    return This->lpVtbl->SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status);
}
static inline HRESULT IVssBackupComponentsEx2_AddNewTarget(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPath,LPCWSTR wszFileName,boolean bRecursive,LPCWSTR wszAlternatePath) {
    return This->lpVtbl->AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath);
}
static inline HRESULT IVssBackupComponentsEx2_SetRangesFilePath(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,UINT iPartialFile,LPCWSTR wszRangesFile) {
    return This->lpVtbl->SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile);
}
static inline HRESULT IVssBackupComponentsEx2_PreRestore(IVssBackupComponentsEx2* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PreRestore(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx2_PostRestore(IVssBackupComponentsEx2* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PostRestore(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx2_SetContext(IVssBackupComponentsEx2* This,LONG lContext) {
    return This->lpVtbl->SetContext(This,lContext);
}
static inline HRESULT IVssBackupComponentsEx2_StartSnapshotSet(IVssBackupComponentsEx2* This,VSS_ID *pSnapshotSetId) {
    return This->lpVtbl->StartSnapshotSet(This,pSnapshotSetId);
}
static inline HRESULT IVssBackupComponentsEx2_AddToSnapshotSet(IVssBackupComponentsEx2* This,VSS_PWSZ pwszVolumeName,VSS_ID ProviderId,VSS_ID *pidSnapshot) {
    return This->lpVtbl->AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot);
}
static inline HRESULT IVssBackupComponentsEx2_DoSnapshotSet(IVssBackupComponentsEx2* This,IVssAsync **ppAsync) {
    return This->lpVtbl->DoSnapshotSet(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx2_DeleteSnapshots(IVssBackupComponentsEx2* This,VSS_ID SourceObjectId,VSS_OBJECT_TYPE eSourceObjectType,WINBOOL bForceDelete,LONG *plDeletedSnapshots,VSS_ID *pNondeletedSnapshotID) {
    return This->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID);
}
static inline HRESULT IVssBackupComponentsEx2_ImportSnapshots(IVssBackupComponentsEx2* This,IVssAsync **ppAsync) {
    return This->lpVtbl->ImportSnapshots(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx2_BreakSnapshotSet(IVssBackupComponentsEx2* This,VSS_ID SnapshotSetId) {
    return This->lpVtbl->BreakSnapshotSet(This,SnapshotSetId);
}
static inline HRESULT IVssBackupComponentsEx2_GetSnapshotProperties(IVssBackupComponentsEx2* This,VSS_ID SnapshotId,VSS_SNAPSHOT_PROP *pProp) {
    return This->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp);
}
static inline HRESULT IVssBackupComponentsEx2_Query(IVssBackupComponentsEx2* This,VSS_ID QueriedObjectId,VSS_OBJECT_TYPE eQueriedObjectType,VSS_OBJECT_TYPE eReturnedObjectsType,IVssEnumObject **ppEnum) {
    return This->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum);
}
static inline HRESULT IVssBackupComponentsEx2_IsVolumeSupported(IVssBackupComponentsEx2* This,VSS_ID ProviderId,VSS_PWSZ pwszVolumeName,WINBOOL *pbSupportedByThisProvider) {
    return This->lpVtbl->IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider);
}
static inline HRESULT IVssBackupComponentsEx2_DisableWriterClasses(IVssBackupComponentsEx2* This,const VSS_ID *rgWriterClassId,UINT cClassId) {
    return This->lpVtbl->DisableWriterClasses(This,rgWriterClassId,cClassId);
}
static inline HRESULT IVssBackupComponentsEx2_EnableWriterClasses(IVssBackupComponentsEx2* This,const VSS_ID *rgWriterClassId,UINT cClassId) {
    return This->lpVtbl->EnableWriterClasses(This,rgWriterClassId,cClassId);
}
static inline HRESULT IVssBackupComponentsEx2_DisableWriterInstances(IVssBackupComponentsEx2* This,const VSS_ID *rgWriterInstanceId,UINT cInstanceId) {
    return This->lpVtbl->DisableWriterInstances(This,rgWriterInstanceId,cInstanceId);
}
static inline HRESULT IVssBackupComponentsEx2_ExposeSnapshot(IVssBackupComponentsEx2* This,VSS_ID SnapshotId,VSS_PWSZ wszPathFromRoot,LONG lAttributes,VSS_PWSZ wszExpose,VSS_PWSZ *pwszExposed) {
    return This->lpVtbl->ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed);
}
static inline HRESULT IVssBackupComponentsEx2_RevertToSnapshot(IVssBackupComponentsEx2* This,VSS_ID SnapshotId,WINBOOL bForceDismount) {
    return This->lpVtbl->RevertToSnapshot(This,SnapshotId,bForceDismount);
}
static inline HRESULT IVssBackupComponentsEx2_QueryRevertStatus(IVssBackupComponentsEx2* This,VSS_PWSZ pwszVolume,IVssAsync **ppAsync) {
    return This->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync);
}
/*** IVssBackupComponentsEx methods ***/
static inline HRESULT IVssBackupComponentsEx2_GetWriterMetadataEx(IVssBackupComponentsEx2* This,UINT iWriter,VSS_ID *pidInstance,IVssExamineWriterMetadataEx **ppMetadata) {
    return This->lpVtbl->GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata);
}
static inline HRESULT IVssBackupComponentsEx2_SetSelectedForRestoreEx(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSelectedForRestore,VSS_ID instanceId) {
    return This->lpVtbl->SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId);
}
/*** IVssBackupComponentsEx2 methods ***/
static inline HRESULT IVssBackupComponentsEx2_UnexposeSnapshot(IVssBackupComponentsEx2* This,VSS_ID snapshotId) {
    return This->lpVtbl->UnexposeSnapshot(This,snapshotId);
}
static inline HRESULT IVssBackupComponentsEx2_SetAuthoritativeRestore(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bAuth) {
    return This->lpVtbl->SetAuthoritativeRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bAuth);
}
static inline HRESULT IVssBackupComponentsEx2_SetRollForward(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,VSS_ROLLFORWARD_TYPE rollType,LPCWSTR wszRollForwardPoint) {
    return This->lpVtbl->SetRollForward(This,writerId,ct,wszLogicalPath,wszComponentName,rollType,wszRollForwardPoint);
}
static inline HRESULT IVssBackupComponentsEx2_SetRestoreName(IVssBackupComponentsEx2* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszRestoreName) {
    return This->lpVtbl->SetRestoreName(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreName);
}
static inline HRESULT IVssBackupComponentsEx2_BreakSnapshotSetEx(IVssBackupComponentsEx2* This,VSS_ID SnapshotSetID,DWORD dwBreakFlags,IVssAsync **ppAsync) {
    return This->lpVtbl->BreakSnapshotSetEx(This,SnapshotSetID,dwBreakFlags,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx2_PreFastRecovery(IVssBackupComponentsEx2* This,VSS_ID SnapshotSetID,DWORD dwPreFastRecoveryFlags,IVssAsync **ppAsync) {
    return This->lpVtbl->PreFastRecovery(This,SnapshotSetID,dwPreFastRecoveryFlags,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx2_FastRecovery(IVssBackupComponentsEx2* This,VSS_ID SnapshotSetID,DWORD dwFastRecoveryFlags,IVssAsync **ppAsync) {
    return This->lpVtbl->FastRecovery(This,SnapshotSetID,dwFastRecoveryFlags,ppAsync);
}
#endif
#endif

#endif


#endif  /* __IVssBackupComponentsEx2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssBackupComponentsEx3 interface
 */
#ifndef __IVssBackupComponentsEx3_INTERFACE_DEFINED__
#define __IVssBackupComponentsEx3_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssBackupComponentsEx3, 0xc191bfbc, 0xb602, 0x4675, 0x8b,0xd1, 0x67,0xd6,0x42,0xf5,0x29,0xd5);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c191bfbc-b602-4675-8bd1-67d642f529d5")
IVssBackupComponentsEx3 : public IVssBackupComponentsEx2
{
    virtual HRESULT STDMETHODCALLTYPE GetWriterStatusEx(
        UINT iWriter,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriter,
        VSS_WRITER_STATE *pnStatus,
        HRESULT *phrFailureWriter,
        HRESULT *phrApplication = 0,
        BSTR *pbstrApplicationMessage = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddSnapshotToRecoverySet(
        VSS_ID snapshotId,
        DWORD dwFlags,
        VSS_PWSZ pwszDestinationVolume = 0) = 0;

    virtual HRESULT STDMETHODCALLTYPE RecoverSet(
        DWORD dwFlags,
        IVssAsync **ppAsync) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSessionId(
        VSS_ID *idSession) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssBackupComponentsEx3, 0xc191bfbc, 0xb602, 0x4675, 0x8b,0xd1, 0x67,0xd6,0x42,0xf5,0x29,0xd5)
#endif
#else
typedef struct IVssBackupComponentsEx3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssBackupComponentsEx3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssBackupComponentsEx3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssBackupComponentsEx3 *This);

    /*** IVssBackupComponents methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterComponentsCount)(
        IVssBackupComponentsEx3 *This,
        UINT *pcComponents);

    HRESULT (STDMETHODCALLTYPE *GetWriterComponents)(
        IVssBackupComponentsEx3 *This,
        UINT iWriter,
        IVssWriterComponentsExt **ppWriter);

    HRESULT (STDMETHODCALLTYPE *InitializeForBackup)(
        IVssBackupComponentsEx3 *This,
        BSTR bstrXML);

    HRESULT (STDMETHODCALLTYPE *SetBackupState)(
        IVssBackupComponentsEx3 *This,
        boolean bSelectComponents,
        boolean bBackupBootableSystemState,
        VSS_BACKUP_TYPE backupType,
        boolean bPartialFileSupport);

    HRESULT (STDMETHODCALLTYPE *InitializeForRestore)(
        IVssBackupComponentsEx3 *This,
        BSTR bstrXML);

    HRESULT (STDMETHODCALLTYPE *SetRestoreState)(
        IVssBackupComponentsEx3 *This,
        VSS_RESTORE_TYPE restoreType);

    HRESULT (STDMETHODCALLTYPE *GatherWriterMetadata)(
        IVssBackupComponentsEx3 *This,
        IVssAsync **pAsync);

    HRESULT (STDMETHODCALLTYPE *GetWriterMetadataCount)(
        IVssBackupComponentsEx3 *This,
        UINT *pcWriters);

    HRESULT (STDMETHODCALLTYPE *GetWriterMetadata)(
        IVssBackupComponentsEx3 *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        IVssExamineWriterMetadata **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *FreeWriterMetadata)(
        IVssBackupComponentsEx3 *This);

    HRESULT (STDMETHODCALLTYPE *AddComponent)(
        IVssBackupComponentsEx3 *This,
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName);

    HRESULT (STDMETHODCALLTYPE *PrepareForBackup)(
        IVssBackupComponentsEx3 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *AbortBackup)(
        IVssBackupComponentsEx3 *This);

    HRESULT (STDMETHODCALLTYPE *GatherWriterStatus)(
        IVssBackupComponentsEx3 *This,
        IVssAsync **pAsync);

    HRESULT (STDMETHODCALLTYPE *GetWriterStatusCount)(
        IVssBackupComponentsEx3 *This,
        UINT *pcWriters);

    HRESULT (STDMETHODCALLTYPE *FreeWriterStatus)(
        IVssBackupComponentsEx3 *This);

    HRESULT (STDMETHODCALLTYPE *GetWriterStatus)(
        IVssBackupComponentsEx3 *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriter,
        VSS_WRITER_STATE *pnStatus,
        HRESULT *phResultFailure);

    HRESULT (STDMETHODCALLTYPE *SetBackupSucceeded)(
        IVssBackupComponentsEx3 *This,
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSucceded);

    HRESULT (STDMETHODCALLTYPE *SetBackupOptions)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszBackupOptions);

    HRESULT (STDMETHODCALLTYPE *SetSelectedForRestore)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSelectedForRestore);

    HRESULT (STDMETHODCALLTYPE *SetRestoreOptions)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszRestoreOptions);

    HRESULT (STDMETHODCALLTYPE *SetAdditionalRestores)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bAdditionalRestores);

    HRESULT (STDMETHODCALLTYPE *SetPreviousBackupStamp)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPreviousBackupStamp);

    HRESULT (STDMETHODCALLTYPE *SaveAsXML)(
        IVssBackupComponentsEx3 *This,
        BSTR *pbstrXML);

    HRESULT (STDMETHODCALLTYPE *BackupComplete)(
        IVssBackupComponentsEx3 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *AddAlternativeLocationMapping)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszDestination);

    HRESULT (STDMETHODCALLTYPE *AddRestoreSubcomponent)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszSubComponentLogicalPath,
        LPCWSTR wszSubComponentName,
        boolean bRepair);

    HRESULT (STDMETHODCALLTYPE *SetFileRestoreStatus)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        VSS_FILE_RESTORE_STATUS status);

    HRESULT (STDMETHODCALLTYPE *AddNewTarget)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFileName,
        boolean bRecursive,
        LPCWSTR wszAlternatePath);

    HRESULT (STDMETHODCALLTYPE *SetRangesFilePath)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        UINT iPartialFile,
        LPCWSTR wszRangesFile);

    HRESULT (STDMETHODCALLTYPE *PreRestore)(
        IVssBackupComponentsEx3 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *PostRestore)(
        IVssBackupComponentsEx3 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *SetContext)(
        IVssBackupComponentsEx3 *This,
        LONG lContext);

    HRESULT (STDMETHODCALLTYPE *StartSnapshotSet)(
        IVssBackupComponentsEx3 *This,
        VSS_ID *pSnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *AddToSnapshotSet)(
        IVssBackupComponentsEx3 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_ID ProviderId,
        VSS_ID *pidSnapshot);

    HRESULT (STDMETHODCALLTYPE *DoSnapshotSet)(
        IVssBackupComponentsEx3 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *DeleteSnapshots)(
        IVssBackupComponentsEx3 *This,
        VSS_ID SourceObjectId,
        VSS_OBJECT_TYPE eSourceObjectType,
        WINBOOL bForceDelete,
        LONG *plDeletedSnapshots,
        VSS_ID *pNondeletedSnapshotID);

    HRESULT (STDMETHODCALLTYPE *ImportSnapshots)(
        IVssBackupComponentsEx3 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *BreakSnapshotSet)(
        IVssBackupComponentsEx3 *This,
        VSS_ID SnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *GetSnapshotProperties)(
        IVssBackupComponentsEx3 *This,
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROP *pProp);

    HRESULT (STDMETHODCALLTYPE *Query)(
        IVssBackupComponentsEx3 *This,
        VSS_ID QueriedObjectId,
        VSS_OBJECT_TYPE eQueriedObjectType,
        VSS_OBJECT_TYPE eReturnedObjectsType,
        IVssEnumObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *IsVolumeSupported)(
        IVssBackupComponentsEx3 *This,
        VSS_ID ProviderId,
        VSS_PWSZ pwszVolumeName,
        WINBOOL *pbSupportedByThisProvider);

    HRESULT (STDMETHODCALLTYPE *DisableWriterClasses)(
        IVssBackupComponentsEx3 *This,
        const VSS_ID *rgWriterClassId,
        UINT cClassId);

    HRESULT (STDMETHODCALLTYPE *EnableWriterClasses)(
        IVssBackupComponentsEx3 *This,
        const VSS_ID *rgWriterClassId,
        UINT cClassId);

    HRESULT (STDMETHODCALLTYPE *DisableWriterInstances)(
        IVssBackupComponentsEx3 *This,
        const VSS_ID *rgWriterInstanceId,
        UINT cInstanceId);

    HRESULT (STDMETHODCALLTYPE *ExposeSnapshot)(
        IVssBackupComponentsEx3 *This,
        VSS_ID SnapshotId,
        VSS_PWSZ wszPathFromRoot,
        LONG lAttributes,
        VSS_PWSZ wszExpose,
        VSS_PWSZ *pwszExposed);

    HRESULT (STDMETHODCALLTYPE *RevertToSnapshot)(
        IVssBackupComponentsEx3 *This,
        VSS_ID SnapshotId,
        WINBOOL bForceDismount);

    HRESULT (STDMETHODCALLTYPE *QueryRevertStatus)(
        IVssBackupComponentsEx3 *This,
        VSS_PWSZ pwszVolume,
        IVssAsync **ppAsync);

    /*** IVssBackupComponentsEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterMetadataEx)(
        IVssBackupComponentsEx3 *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        IVssExamineWriterMetadataEx **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *SetSelectedForRestoreEx)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSelectedForRestore,
        VSS_ID instanceId);

    /*** IVssBackupComponentsEx2 methods ***/
    HRESULT (STDMETHODCALLTYPE *UnexposeSnapshot)(
        IVssBackupComponentsEx3 *This,
        VSS_ID snapshotId);

    HRESULT (STDMETHODCALLTYPE *SetAuthoritativeRestore)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bAuth);

    HRESULT (STDMETHODCALLTYPE *SetRollForward)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        VSS_ROLLFORWARD_TYPE rollType,
        LPCWSTR wszRollForwardPoint);

    HRESULT (STDMETHODCALLTYPE *SetRestoreName)(
        IVssBackupComponentsEx3 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszRestoreName);

    HRESULT (STDMETHODCALLTYPE *BreakSnapshotSetEx)(
        IVssBackupComponentsEx3 *This,
        VSS_ID SnapshotSetID,
        DWORD dwBreakFlags,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *PreFastRecovery)(
        IVssBackupComponentsEx3 *This,
        VSS_ID SnapshotSetID,
        DWORD dwPreFastRecoveryFlags,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *FastRecovery)(
        IVssBackupComponentsEx3 *This,
        VSS_ID SnapshotSetID,
        DWORD dwFastRecoveryFlags,
        IVssAsync **ppAsync);

    /*** IVssBackupComponentsEx3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterStatusEx)(
        IVssBackupComponentsEx3 *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriter,
        VSS_WRITER_STATE *pnStatus,
        HRESULT *phrFailureWriter,
        HRESULT *phrApplication,
        BSTR *pbstrApplicationMessage);

    HRESULT (STDMETHODCALLTYPE *AddSnapshotToRecoverySet)(
        IVssBackupComponentsEx3 *This,
        VSS_ID snapshotId,
        DWORD dwFlags,
        VSS_PWSZ pwszDestinationVolume);

    HRESULT (STDMETHODCALLTYPE *RecoverSet)(
        IVssBackupComponentsEx3 *This,
        DWORD dwFlags,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *GetSessionId)(
        IVssBackupComponentsEx3 *This,
        VSS_ID *idSession);

    END_INTERFACE
} IVssBackupComponentsEx3Vtbl;

interface IVssBackupComponentsEx3 {
    CONST_VTBL IVssBackupComponentsEx3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssBackupComponentsEx3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssBackupComponentsEx3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssBackupComponentsEx3_Release(This) (This)->lpVtbl->Release(This)
/*** IVssBackupComponents methods ***/
#define IVssBackupComponentsEx3_GetWriterComponentsCount(This,pcComponents) (This)->lpVtbl->GetWriterComponentsCount(This,pcComponents)
#define IVssBackupComponentsEx3_GetWriterComponents(This,iWriter,ppWriter) (This)->lpVtbl->GetWriterComponents(This,iWriter,ppWriter)
#define IVssBackupComponentsEx3_InitializeForBackup(This,bstrXML) (This)->lpVtbl->InitializeForBackup(This,bstrXML)
#define IVssBackupComponentsEx3_SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport) (This)->lpVtbl->SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport)
#define IVssBackupComponentsEx3_InitializeForRestore(This,bstrXML) (This)->lpVtbl->InitializeForRestore(This,bstrXML)
#define IVssBackupComponentsEx3_SetRestoreState(This,restoreType) (This)->lpVtbl->SetRestoreState(This,restoreType)
#define IVssBackupComponentsEx3_GatherWriterMetadata(This,pAsync) (This)->lpVtbl->GatherWriterMetadata(This,pAsync)
#define IVssBackupComponentsEx3_GetWriterMetadataCount(This,pcWriters) (This)->lpVtbl->GetWriterMetadataCount(This,pcWriters)
#define IVssBackupComponentsEx3_GetWriterMetadata(This,iWriter,pidInstance,ppMetadata) (This)->lpVtbl->GetWriterMetadata(This,iWriter,pidInstance,ppMetadata)
#define IVssBackupComponentsEx3_FreeWriterMetadata(This) (This)->lpVtbl->FreeWriterMetadata(This)
#define IVssBackupComponentsEx3_AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName) (This)->lpVtbl->AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName)
#define IVssBackupComponentsEx3_PrepareForBackup(This,ppAsync) (This)->lpVtbl->PrepareForBackup(This,ppAsync)
#define IVssBackupComponentsEx3_AbortBackup(This) (This)->lpVtbl->AbortBackup(This)
#define IVssBackupComponentsEx3_GatherWriterStatus(This,pAsync) (This)->lpVtbl->GatherWriterStatus(This,pAsync)
#define IVssBackupComponentsEx3_GetWriterStatusCount(This,pcWriters) (This)->lpVtbl->GetWriterStatusCount(This,pcWriters)
#define IVssBackupComponentsEx3_FreeWriterStatus(This) (This)->lpVtbl->FreeWriterStatus(This)
#define IVssBackupComponentsEx3_GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure) (This)->lpVtbl->GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure)
#define IVssBackupComponentsEx3_SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded) (This)->lpVtbl->SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded)
#define IVssBackupComponentsEx3_SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions) (This)->lpVtbl->SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions)
#define IVssBackupComponentsEx3_SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore) (This)->lpVtbl->SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore)
#define IVssBackupComponentsEx3_SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions) (This)->lpVtbl->SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions)
#define IVssBackupComponentsEx3_SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores) (This)->lpVtbl->SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores)
#define IVssBackupComponentsEx3_SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp) (This)->lpVtbl->SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp)
#define IVssBackupComponentsEx3_SaveAsXML(This,pbstrXML) (This)->lpVtbl->SaveAsXML(This,pbstrXML)
#define IVssBackupComponentsEx3_BackupComplete(This,ppAsync) (This)->lpVtbl->BackupComplete(This,ppAsync)
#define IVssBackupComponentsEx3_AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination) (This)->lpVtbl->AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination)
#define IVssBackupComponentsEx3_AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair) (This)->lpVtbl->AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair)
#define IVssBackupComponentsEx3_SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status) (This)->lpVtbl->SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status)
#define IVssBackupComponentsEx3_AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath) (This)->lpVtbl->AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath)
#define IVssBackupComponentsEx3_SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile) (This)->lpVtbl->SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile)
#define IVssBackupComponentsEx3_PreRestore(This,ppAsync) (This)->lpVtbl->PreRestore(This,ppAsync)
#define IVssBackupComponentsEx3_PostRestore(This,ppAsync) (This)->lpVtbl->PostRestore(This,ppAsync)
#define IVssBackupComponentsEx3_SetContext(This,lContext) (This)->lpVtbl->SetContext(This,lContext)
#define IVssBackupComponentsEx3_StartSnapshotSet(This,pSnapshotSetId) (This)->lpVtbl->StartSnapshotSet(This,pSnapshotSetId)
#define IVssBackupComponentsEx3_AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot) (This)->lpVtbl->AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot)
#define IVssBackupComponentsEx3_DoSnapshotSet(This,ppAsync) (This)->lpVtbl->DoSnapshotSet(This,ppAsync)
#define IVssBackupComponentsEx3_DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID) (This)->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID)
#define IVssBackupComponentsEx3_ImportSnapshots(This,ppAsync) (This)->lpVtbl->ImportSnapshots(This,ppAsync)
#define IVssBackupComponentsEx3_BreakSnapshotSet(This,SnapshotSetId) (This)->lpVtbl->BreakSnapshotSet(This,SnapshotSetId)
#define IVssBackupComponentsEx3_GetSnapshotProperties(This,SnapshotId,pProp) (This)->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp)
#define IVssBackupComponentsEx3_Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum) (This)->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum)
#define IVssBackupComponentsEx3_IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider) (This)->lpVtbl->IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider)
#define IVssBackupComponentsEx3_DisableWriterClasses(This,rgWriterClassId,cClassId) (This)->lpVtbl->DisableWriterClasses(This,rgWriterClassId,cClassId)
#define IVssBackupComponentsEx3_EnableWriterClasses(This,rgWriterClassId,cClassId) (This)->lpVtbl->EnableWriterClasses(This,rgWriterClassId,cClassId)
#define IVssBackupComponentsEx3_DisableWriterInstances(This,rgWriterInstanceId,cInstanceId) (This)->lpVtbl->DisableWriterInstances(This,rgWriterInstanceId,cInstanceId)
#define IVssBackupComponentsEx3_ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed) (This)->lpVtbl->ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed)
#define IVssBackupComponentsEx3_RevertToSnapshot(This,SnapshotId,bForceDismount) (This)->lpVtbl->RevertToSnapshot(This,SnapshotId,bForceDismount)
#define IVssBackupComponentsEx3_QueryRevertStatus(This,pwszVolume,ppAsync) (This)->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync)
/*** IVssBackupComponentsEx methods ***/
#define IVssBackupComponentsEx3_GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata) (This)->lpVtbl->GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata)
#define IVssBackupComponentsEx3_SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId) (This)->lpVtbl->SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId)
/*** IVssBackupComponentsEx2 methods ***/
#define IVssBackupComponentsEx3_UnexposeSnapshot(This,snapshotId) (This)->lpVtbl->UnexposeSnapshot(This,snapshotId)
#define IVssBackupComponentsEx3_SetAuthoritativeRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bAuth) (This)->lpVtbl->SetAuthoritativeRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bAuth)
#define IVssBackupComponentsEx3_SetRollForward(This,writerId,ct,wszLogicalPath,wszComponentName,rollType,wszRollForwardPoint) (This)->lpVtbl->SetRollForward(This,writerId,ct,wszLogicalPath,wszComponentName,rollType,wszRollForwardPoint)
#define IVssBackupComponentsEx3_SetRestoreName(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreName) (This)->lpVtbl->SetRestoreName(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreName)
#define IVssBackupComponentsEx3_BreakSnapshotSetEx(This,SnapshotSetID,dwBreakFlags,ppAsync) (This)->lpVtbl->BreakSnapshotSetEx(This,SnapshotSetID,dwBreakFlags,ppAsync)
#define IVssBackupComponentsEx3_PreFastRecovery(This,SnapshotSetID,dwPreFastRecoveryFlags,ppAsync) (This)->lpVtbl->PreFastRecovery(This,SnapshotSetID,dwPreFastRecoveryFlags,ppAsync)
#define IVssBackupComponentsEx3_FastRecovery(This,SnapshotSetID,dwFastRecoveryFlags,ppAsync) (This)->lpVtbl->FastRecovery(This,SnapshotSetID,dwFastRecoveryFlags,ppAsync)
/*** IVssBackupComponentsEx3 methods ***/
#define IVssBackupComponentsEx3_GetWriterStatusEx(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phrFailureWriter,phrApplication,pbstrApplicationMessage) (This)->lpVtbl->GetWriterStatusEx(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phrFailureWriter,phrApplication,pbstrApplicationMessage)
#define IVssBackupComponentsEx3_AddSnapshotToRecoverySet(This,snapshotId,dwFlags,pwszDestinationVolume) (This)->lpVtbl->AddSnapshotToRecoverySet(This,snapshotId,dwFlags,pwszDestinationVolume)
#define IVssBackupComponentsEx3_RecoverSet(This,dwFlags,ppAsync) (This)->lpVtbl->RecoverSet(This,dwFlags,ppAsync)
#define IVssBackupComponentsEx3_GetSessionId(This,idSession) (This)->lpVtbl->GetSessionId(This,idSession)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssBackupComponentsEx3_QueryInterface(IVssBackupComponentsEx3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssBackupComponentsEx3_AddRef(IVssBackupComponentsEx3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssBackupComponentsEx3_Release(IVssBackupComponentsEx3* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssBackupComponents methods ***/
static inline HRESULT IVssBackupComponentsEx3_GetWriterComponentsCount(IVssBackupComponentsEx3* This,UINT *pcComponents) {
    return This->lpVtbl->GetWriterComponentsCount(This,pcComponents);
}
static inline HRESULT IVssBackupComponentsEx3_GetWriterComponents(IVssBackupComponentsEx3* This,UINT iWriter,IVssWriterComponentsExt **ppWriter) {
    return This->lpVtbl->GetWriterComponents(This,iWriter,ppWriter);
}
static inline HRESULT IVssBackupComponentsEx3_InitializeForBackup(IVssBackupComponentsEx3* This,BSTR bstrXML) {
    return This->lpVtbl->InitializeForBackup(This,bstrXML);
}
static inline HRESULT IVssBackupComponentsEx3_SetBackupState(IVssBackupComponentsEx3* This,boolean bSelectComponents,boolean bBackupBootableSystemState,VSS_BACKUP_TYPE backupType,boolean bPartialFileSupport) {
    return This->lpVtbl->SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport);
}
static inline HRESULT IVssBackupComponentsEx3_InitializeForRestore(IVssBackupComponentsEx3* This,BSTR bstrXML) {
    return This->lpVtbl->InitializeForRestore(This,bstrXML);
}
static inline HRESULT IVssBackupComponentsEx3_SetRestoreState(IVssBackupComponentsEx3* This,VSS_RESTORE_TYPE restoreType) {
    return This->lpVtbl->SetRestoreState(This,restoreType);
}
static inline HRESULT IVssBackupComponentsEx3_GatherWriterMetadata(IVssBackupComponentsEx3* This,IVssAsync **pAsync) {
    return This->lpVtbl->GatherWriterMetadata(This,pAsync);
}
static inline HRESULT IVssBackupComponentsEx3_GetWriterMetadataCount(IVssBackupComponentsEx3* This,UINT *pcWriters) {
    return This->lpVtbl->GetWriterMetadataCount(This,pcWriters);
}
static inline HRESULT IVssBackupComponentsEx3_GetWriterMetadata(IVssBackupComponentsEx3* This,UINT iWriter,VSS_ID *pidInstance,IVssExamineWriterMetadata **ppMetadata) {
    return This->lpVtbl->GetWriterMetadata(This,iWriter,pidInstance,ppMetadata);
}
static inline HRESULT IVssBackupComponentsEx3_FreeWriterMetadata(IVssBackupComponentsEx3* This) {
    return This->lpVtbl->FreeWriterMetadata(This);
}
static inline HRESULT IVssBackupComponentsEx3_AddComponent(IVssBackupComponentsEx3* This,VSS_ID instanceId,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName) {
    return This->lpVtbl->AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName);
}
static inline HRESULT IVssBackupComponentsEx3_PrepareForBackup(IVssBackupComponentsEx3* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PrepareForBackup(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx3_AbortBackup(IVssBackupComponentsEx3* This) {
    return This->lpVtbl->AbortBackup(This);
}
static inline HRESULT IVssBackupComponentsEx3_GatherWriterStatus(IVssBackupComponentsEx3* This,IVssAsync **pAsync) {
    return This->lpVtbl->GatherWriterStatus(This,pAsync);
}
static inline HRESULT IVssBackupComponentsEx3_GetWriterStatusCount(IVssBackupComponentsEx3* This,UINT *pcWriters) {
    return This->lpVtbl->GetWriterStatusCount(This,pcWriters);
}
static inline HRESULT IVssBackupComponentsEx3_FreeWriterStatus(IVssBackupComponentsEx3* This) {
    return This->lpVtbl->FreeWriterStatus(This);
}
static inline HRESULT IVssBackupComponentsEx3_GetWriterStatus(IVssBackupComponentsEx3* This,UINT iWriter,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriter,VSS_WRITER_STATE *pnStatus,HRESULT *phResultFailure) {
    return This->lpVtbl->GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure);
}
static inline HRESULT IVssBackupComponentsEx3_SetBackupSucceeded(IVssBackupComponentsEx3* This,VSS_ID instanceId,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSucceded) {
    return This->lpVtbl->SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded);
}
static inline HRESULT IVssBackupComponentsEx3_SetBackupOptions(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszBackupOptions) {
    return This->lpVtbl->SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions);
}
static inline HRESULT IVssBackupComponentsEx3_SetSelectedForRestore(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSelectedForRestore) {
    return This->lpVtbl->SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore);
}
static inline HRESULT IVssBackupComponentsEx3_SetRestoreOptions(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszRestoreOptions) {
    return This->lpVtbl->SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions);
}
static inline HRESULT IVssBackupComponentsEx3_SetAdditionalRestores(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bAdditionalRestores) {
    return This->lpVtbl->SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores);
}
static inline HRESULT IVssBackupComponentsEx3_SetPreviousBackupStamp(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPreviousBackupStamp) {
    return This->lpVtbl->SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp);
}
static inline HRESULT IVssBackupComponentsEx3_SaveAsXML(IVssBackupComponentsEx3* This,BSTR *pbstrXML) {
    return This->lpVtbl->SaveAsXML(This,pbstrXML);
}
static inline HRESULT IVssBackupComponentsEx3_BackupComplete(IVssBackupComponentsEx3* This,IVssAsync **ppAsync) {
    return This->lpVtbl->BackupComplete(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx3_AddAlternativeLocationMapping(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE componentType,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive,LPCWSTR wszDestination) {
    return This->lpVtbl->AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination);
}
static inline HRESULT IVssBackupComponentsEx3_AddRestoreSubcomponent(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE componentType,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszSubComponentLogicalPath,LPCWSTR wszSubComponentName,boolean bRepair) {
    return This->lpVtbl->AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair);
}
static inline HRESULT IVssBackupComponentsEx3_SetFileRestoreStatus(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,VSS_FILE_RESTORE_STATUS status) {
    return This->lpVtbl->SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status);
}
static inline HRESULT IVssBackupComponentsEx3_AddNewTarget(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPath,LPCWSTR wszFileName,boolean bRecursive,LPCWSTR wszAlternatePath) {
    return This->lpVtbl->AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath);
}
static inline HRESULT IVssBackupComponentsEx3_SetRangesFilePath(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,UINT iPartialFile,LPCWSTR wszRangesFile) {
    return This->lpVtbl->SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile);
}
static inline HRESULT IVssBackupComponentsEx3_PreRestore(IVssBackupComponentsEx3* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PreRestore(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx3_PostRestore(IVssBackupComponentsEx3* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PostRestore(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx3_SetContext(IVssBackupComponentsEx3* This,LONG lContext) {
    return This->lpVtbl->SetContext(This,lContext);
}
static inline HRESULT IVssBackupComponentsEx3_StartSnapshotSet(IVssBackupComponentsEx3* This,VSS_ID *pSnapshotSetId) {
    return This->lpVtbl->StartSnapshotSet(This,pSnapshotSetId);
}
static inline HRESULT IVssBackupComponentsEx3_AddToSnapshotSet(IVssBackupComponentsEx3* This,VSS_PWSZ pwszVolumeName,VSS_ID ProviderId,VSS_ID *pidSnapshot) {
    return This->lpVtbl->AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot);
}
static inline HRESULT IVssBackupComponentsEx3_DoSnapshotSet(IVssBackupComponentsEx3* This,IVssAsync **ppAsync) {
    return This->lpVtbl->DoSnapshotSet(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx3_DeleteSnapshots(IVssBackupComponentsEx3* This,VSS_ID SourceObjectId,VSS_OBJECT_TYPE eSourceObjectType,WINBOOL bForceDelete,LONG *plDeletedSnapshots,VSS_ID *pNondeletedSnapshotID) {
    return This->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID);
}
static inline HRESULT IVssBackupComponentsEx3_ImportSnapshots(IVssBackupComponentsEx3* This,IVssAsync **ppAsync) {
    return This->lpVtbl->ImportSnapshots(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx3_BreakSnapshotSet(IVssBackupComponentsEx3* This,VSS_ID SnapshotSetId) {
    return This->lpVtbl->BreakSnapshotSet(This,SnapshotSetId);
}
static inline HRESULT IVssBackupComponentsEx3_GetSnapshotProperties(IVssBackupComponentsEx3* This,VSS_ID SnapshotId,VSS_SNAPSHOT_PROP *pProp) {
    return This->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp);
}
static inline HRESULT IVssBackupComponentsEx3_Query(IVssBackupComponentsEx3* This,VSS_ID QueriedObjectId,VSS_OBJECT_TYPE eQueriedObjectType,VSS_OBJECT_TYPE eReturnedObjectsType,IVssEnumObject **ppEnum) {
    return This->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum);
}
static inline HRESULT IVssBackupComponentsEx3_IsVolumeSupported(IVssBackupComponentsEx3* This,VSS_ID ProviderId,VSS_PWSZ pwszVolumeName,WINBOOL *pbSupportedByThisProvider) {
    return This->lpVtbl->IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider);
}
static inline HRESULT IVssBackupComponentsEx3_DisableWriterClasses(IVssBackupComponentsEx3* This,const VSS_ID *rgWriterClassId,UINT cClassId) {
    return This->lpVtbl->DisableWriterClasses(This,rgWriterClassId,cClassId);
}
static inline HRESULT IVssBackupComponentsEx3_EnableWriterClasses(IVssBackupComponentsEx3* This,const VSS_ID *rgWriterClassId,UINT cClassId) {
    return This->lpVtbl->EnableWriterClasses(This,rgWriterClassId,cClassId);
}
static inline HRESULT IVssBackupComponentsEx3_DisableWriterInstances(IVssBackupComponentsEx3* This,const VSS_ID *rgWriterInstanceId,UINT cInstanceId) {
    return This->lpVtbl->DisableWriterInstances(This,rgWriterInstanceId,cInstanceId);
}
static inline HRESULT IVssBackupComponentsEx3_ExposeSnapshot(IVssBackupComponentsEx3* This,VSS_ID SnapshotId,VSS_PWSZ wszPathFromRoot,LONG lAttributes,VSS_PWSZ wszExpose,VSS_PWSZ *pwszExposed) {
    return This->lpVtbl->ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed);
}
static inline HRESULT IVssBackupComponentsEx3_RevertToSnapshot(IVssBackupComponentsEx3* This,VSS_ID SnapshotId,WINBOOL bForceDismount) {
    return This->lpVtbl->RevertToSnapshot(This,SnapshotId,bForceDismount);
}
static inline HRESULT IVssBackupComponentsEx3_QueryRevertStatus(IVssBackupComponentsEx3* This,VSS_PWSZ pwszVolume,IVssAsync **ppAsync) {
    return This->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync);
}
/*** IVssBackupComponentsEx methods ***/
static inline HRESULT IVssBackupComponentsEx3_GetWriterMetadataEx(IVssBackupComponentsEx3* This,UINT iWriter,VSS_ID *pidInstance,IVssExamineWriterMetadataEx **ppMetadata) {
    return This->lpVtbl->GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata);
}
static inline HRESULT IVssBackupComponentsEx3_SetSelectedForRestoreEx(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSelectedForRestore,VSS_ID instanceId) {
    return This->lpVtbl->SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId);
}
/*** IVssBackupComponentsEx2 methods ***/
static inline HRESULT IVssBackupComponentsEx3_UnexposeSnapshot(IVssBackupComponentsEx3* This,VSS_ID snapshotId) {
    return This->lpVtbl->UnexposeSnapshot(This,snapshotId);
}
static inline HRESULT IVssBackupComponentsEx3_SetAuthoritativeRestore(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bAuth) {
    return This->lpVtbl->SetAuthoritativeRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bAuth);
}
static inline HRESULT IVssBackupComponentsEx3_SetRollForward(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,VSS_ROLLFORWARD_TYPE rollType,LPCWSTR wszRollForwardPoint) {
    return This->lpVtbl->SetRollForward(This,writerId,ct,wszLogicalPath,wszComponentName,rollType,wszRollForwardPoint);
}
static inline HRESULT IVssBackupComponentsEx3_SetRestoreName(IVssBackupComponentsEx3* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszRestoreName) {
    return This->lpVtbl->SetRestoreName(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreName);
}
static inline HRESULT IVssBackupComponentsEx3_BreakSnapshotSetEx(IVssBackupComponentsEx3* This,VSS_ID SnapshotSetID,DWORD dwBreakFlags,IVssAsync **ppAsync) {
    return This->lpVtbl->BreakSnapshotSetEx(This,SnapshotSetID,dwBreakFlags,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx3_PreFastRecovery(IVssBackupComponentsEx3* This,VSS_ID SnapshotSetID,DWORD dwPreFastRecoveryFlags,IVssAsync **ppAsync) {
    return This->lpVtbl->PreFastRecovery(This,SnapshotSetID,dwPreFastRecoveryFlags,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx3_FastRecovery(IVssBackupComponentsEx3* This,VSS_ID SnapshotSetID,DWORD dwFastRecoveryFlags,IVssAsync **ppAsync) {
    return This->lpVtbl->FastRecovery(This,SnapshotSetID,dwFastRecoveryFlags,ppAsync);
}
/*** IVssBackupComponentsEx3 methods ***/
static inline HRESULT IVssBackupComponentsEx3_GetWriterStatusEx(IVssBackupComponentsEx3* This,UINT iWriter,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriter,VSS_WRITER_STATE *pnStatus,HRESULT *phrFailureWriter,HRESULT *phrApplication,BSTR *pbstrApplicationMessage) {
    return This->lpVtbl->GetWriterStatusEx(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phrFailureWriter,phrApplication,pbstrApplicationMessage);
}
static inline HRESULT IVssBackupComponentsEx3_AddSnapshotToRecoverySet(IVssBackupComponentsEx3* This,VSS_ID snapshotId,DWORD dwFlags,VSS_PWSZ pwszDestinationVolume) {
    return This->lpVtbl->AddSnapshotToRecoverySet(This,snapshotId,dwFlags,pwszDestinationVolume);
}
static inline HRESULT IVssBackupComponentsEx3_RecoverSet(IVssBackupComponentsEx3* This,DWORD dwFlags,IVssAsync **ppAsync) {
    return This->lpVtbl->RecoverSet(This,dwFlags,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx3_GetSessionId(IVssBackupComponentsEx3* This,VSS_ID *idSession) {
    return This->lpVtbl->GetSessionId(This,idSession);
}
#endif
#endif

#endif


#endif  /* __IVssBackupComponentsEx3_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVssBackupComponentsEx4 interface
 */
#ifndef __IVssBackupComponentsEx4_INTERFACE_DEFINED__
#define __IVssBackupComponentsEx4_INTERFACE_DEFINED__

DEFINE_GUID(IID_IVssBackupComponentsEx4, 0xf434c2fd, 0xb553, 0x4961, 0xa9,0xf9, 0xa8,0xe9,0x0b,0x67,0x3e,0x53);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f434c2fd-b553-4961-a9f9-a8e90b673e53")
IVssBackupComponentsEx4 : public IVssBackupComponentsEx3
{
    virtual HRESULT STDMETHODCALLTYPE GetRootAndLogicalPrefixPaths(
        VSS_PWSZ pwszFilePath,
        VSS_PWSZ *ppwszRootPath,
        VSS_PWSZ *ppwszLogicalPrefix,
        WINBOOL bNormalizeFQDNforRootPath = FALSE) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IVssBackupComponentsEx4, 0xf434c2fd, 0xb553, 0x4961, 0xa9,0xf9, 0xa8,0xe9,0x0b,0x67,0x3e,0x53)
#endif
#else
typedef struct IVssBackupComponentsEx4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IVssBackupComponentsEx4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IVssBackupComponentsEx4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IVssBackupComponentsEx4 *This);

    /*** IVssBackupComponents methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterComponentsCount)(
        IVssBackupComponentsEx4 *This,
        UINT *pcComponents);

    HRESULT (STDMETHODCALLTYPE *GetWriterComponents)(
        IVssBackupComponentsEx4 *This,
        UINT iWriter,
        IVssWriterComponentsExt **ppWriter);

    HRESULT (STDMETHODCALLTYPE *InitializeForBackup)(
        IVssBackupComponentsEx4 *This,
        BSTR bstrXML);

    HRESULT (STDMETHODCALLTYPE *SetBackupState)(
        IVssBackupComponentsEx4 *This,
        boolean bSelectComponents,
        boolean bBackupBootableSystemState,
        VSS_BACKUP_TYPE backupType,
        boolean bPartialFileSupport);

    HRESULT (STDMETHODCALLTYPE *InitializeForRestore)(
        IVssBackupComponentsEx4 *This,
        BSTR bstrXML);

    HRESULT (STDMETHODCALLTYPE *SetRestoreState)(
        IVssBackupComponentsEx4 *This,
        VSS_RESTORE_TYPE restoreType);

    HRESULT (STDMETHODCALLTYPE *GatherWriterMetadata)(
        IVssBackupComponentsEx4 *This,
        IVssAsync **pAsync);

    HRESULT (STDMETHODCALLTYPE *GetWriterMetadataCount)(
        IVssBackupComponentsEx4 *This,
        UINT *pcWriters);

    HRESULT (STDMETHODCALLTYPE *GetWriterMetadata)(
        IVssBackupComponentsEx4 *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        IVssExamineWriterMetadata **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *FreeWriterMetadata)(
        IVssBackupComponentsEx4 *This);

    HRESULT (STDMETHODCALLTYPE *AddComponent)(
        IVssBackupComponentsEx4 *This,
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName);

    HRESULT (STDMETHODCALLTYPE *PrepareForBackup)(
        IVssBackupComponentsEx4 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *AbortBackup)(
        IVssBackupComponentsEx4 *This);

    HRESULT (STDMETHODCALLTYPE *GatherWriterStatus)(
        IVssBackupComponentsEx4 *This,
        IVssAsync **pAsync);

    HRESULT (STDMETHODCALLTYPE *GetWriterStatusCount)(
        IVssBackupComponentsEx4 *This,
        UINT *pcWriters);

    HRESULT (STDMETHODCALLTYPE *FreeWriterStatus)(
        IVssBackupComponentsEx4 *This);

    HRESULT (STDMETHODCALLTYPE *GetWriterStatus)(
        IVssBackupComponentsEx4 *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriter,
        VSS_WRITER_STATE *pnStatus,
        HRESULT *phResultFailure);

    HRESULT (STDMETHODCALLTYPE *SetBackupSucceeded)(
        IVssBackupComponentsEx4 *This,
        VSS_ID instanceId,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSucceded);

    HRESULT (STDMETHODCALLTYPE *SetBackupOptions)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszBackupOptions);

    HRESULT (STDMETHODCALLTYPE *SetSelectedForRestore)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSelectedForRestore);

    HRESULT (STDMETHODCALLTYPE *SetRestoreOptions)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszRestoreOptions);

    HRESULT (STDMETHODCALLTYPE *SetAdditionalRestores)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bAdditionalRestores);

    HRESULT (STDMETHODCALLTYPE *SetPreviousBackupStamp)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPreviousBackupStamp);

    HRESULT (STDMETHODCALLTYPE *SaveAsXML)(
        IVssBackupComponentsEx4 *This,
        BSTR *pbstrXML);

    HRESULT (STDMETHODCALLTYPE *BackupComplete)(
        IVssBackupComponentsEx4 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *AddAlternativeLocationMapping)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFilespec,
        boolean bRecursive,
        LPCWSTR wszDestination);

    HRESULT (STDMETHODCALLTYPE *AddRestoreSubcomponent)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE componentType,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszSubComponentLogicalPath,
        LPCWSTR wszSubComponentName,
        boolean bRepair);

    HRESULT (STDMETHODCALLTYPE *SetFileRestoreStatus)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        VSS_FILE_RESTORE_STATUS status);

    HRESULT (STDMETHODCALLTYPE *AddNewTarget)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszPath,
        LPCWSTR wszFileName,
        boolean bRecursive,
        LPCWSTR wszAlternatePath);

    HRESULT (STDMETHODCALLTYPE *SetRangesFilePath)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        UINT iPartialFile,
        LPCWSTR wszRangesFile);

    HRESULT (STDMETHODCALLTYPE *PreRestore)(
        IVssBackupComponentsEx4 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *PostRestore)(
        IVssBackupComponentsEx4 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *SetContext)(
        IVssBackupComponentsEx4 *This,
        LONG lContext);

    HRESULT (STDMETHODCALLTYPE *StartSnapshotSet)(
        IVssBackupComponentsEx4 *This,
        VSS_ID *pSnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *AddToSnapshotSet)(
        IVssBackupComponentsEx4 *This,
        VSS_PWSZ pwszVolumeName,
        VSS_ID ProviderId,
        VSS_ID *pidSnapshot);

    HRESULT (STDMETHODCALLTYPE *DoSnapshotSet)(
        IVssBackupComponentsEx4 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *DeleteSnapshots)(
        IVssBackupComponentsEx4 *This,
        VSS_ID SourceObjectId,
        VSS_OBJECT_TYPE eSourceObjectType,
        WINBOOL bForceDelete,
        LONG *plDeletedSnapshots,
        VSS_ID *pNondeletedSnapshotID);

    HRESULT (STDMETHODCALLTYPE *ImportSnapshots)(
        IVssBackupComponentsEx4 *This,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *BreakSnapshotSet)(
        IVssBackupComponentsEx4 *This,
        VSS_ID SnapshotSetId);

    HRESULT (STDMETHODCALLTYPE *GetSnapshotProperties)(
        IVssBackupComponentsEx4 *This,
        VSS_ID SnapshotId,
        VSS_SNAPSHOT_PROP *pProp);

    HRESULT (STDMETHODCALLTYPE *Query)(
        IVssBackupComponentsEx4 *This,
        VSS_ID QueriedObjectId,
        VSS_OBJECT_TYPE eQueriedObjectType,
        VSS_OBJECT_TYPE eReturnedObjectsType,
        IVssEnumObject **ppEnum);

    HRESULT (STDMETHODCALLTYPE *IsVolumeSupported)(
        IVssBackupComponentsEx4 *This,
        VSS_ID ProviderId,
        VSS_PWSZ pwszVolumeName,
        WINBOOL *pbSupportedByThisProvider);

    HRESULT (STDMETHODCALLTYPE *DisableWriterClasses)(
        IVssBackupComponentsEx4 *This,
        const VSS_ID *rgWriterClassId,
        UINT cClassId);

    HRESULT (STDMETHODCALLTYPE *EnableWriterClasses)(
        IVssBackupComponentsEx4 *This,
        const VSS_ID *rgWriterClassId,
        UINT cClassId);

    HRESULT (STDMETHODCALLTYPE *DisableWriterInstances)(
        IVssBackupComponentsEx4 *This,
        const VSS_ID *rgWriterInstanceId,
        UINT cInstanceId);

    HRESULT (STDMETHODCALLTYPE *ExposeSnapshot)(
        IVssBackupComponentsEx4 *This,
        VSS_ID SnapshotId,
        VSS_PWSZ wszPathFromRoot,
        LONG lAttributes,
        VSS_PWSZ wszExpose,
        VSS_PWSZ *pwszExposed);

    HRESULT (STDMETHODCALLTYPE *RevertToSnapshot)(
        IVssBackupComponentsEx4 *This,
        VSS_ID SnapshotId,
        WINBOOL bForceDismount);

    HRESULT (STDMETHODCALLTYPE *QueryRevertStatus)(
        IVssBackupComponentsEx4 *This,
        VSS_PWSZ pwszVolume,
        IVssAsync **ppAsync);

    /*** IVssBackupComponentsEx methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterMetadataEx)(
        IVssBackupComponentsEx4 *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        IVssExamineWriterMetadataEx **ppMetadata);

    HRESULT (STDMETHODCALLTYPE *SetSelectedForRestoreEx)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bSelectedForRestore,
        VSS_ID instanceId);

    /*** IVssBackupComponentsEx2 methods ***/
    HRESULT (STDMETHODCALLTYPE *UnexposeSnapshot)(
        IVssBackupComponentsEx4 *This,
        VSS_ID snapshotId);

    HRESULT (STDMETHODCALLTYPE *SetAuthoritativeRestore)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        boolean bAuth);

    HRESULT (STDMETHODCALLTYPE *SetRollForward)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        VSS_ROLLFORWARD_TYPE rollType,
        LPCWSTR wszRollForwardPoint);

    HRESULT (STDMETHODCALLTYPE *SetRestoreName)(
        IVssBackupComponentsEx4 *This,
        VSS_ID writerId,
        VSS_COMPONENT_TYPE ct,
        LPCWSTR wszLogicalPath,
        LPCWSTR wszComponentName,
        LPCWSTR wszRestoreName);

    HRESULT (STDMETHODCALLTYPE *BreakSnapshotSetEx)(
        IVssBackupComponentsEx4 *This,
        VSS_ID SnapshotSetID,
        DWORD dwBreakFlags,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *PreFastRecovery)(
        IVssBackupComponentsEx4 *This,
        VSS_ID SnapshotSetID,
        DWORD dwPreFastRecoveryFlags,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *FastRecovery)(
        IVssBackupComponentsEx4 *This,
        VSS_ID SnapshotSetID,
        DWORD dwFastRecoveryFlags,
        IVssAsync **ppAsync);

    /*** IVssBackupComponentsEx3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetWriterStatusEx)(
        IVssBackupComponentsEx4 *This,
        UINT iWriter,
        VSS_ID *pidInstance,
        VSS_ID *pidWriter,
        BSTR *pbstrWriter,
        VSS_WRITER_STATE *pnStatus,
        HRESULT *phrFailureWriter,
        HRESULT *phrApplication,
        BSTR *pbstrApplicationMessage);

    HRESULT (STDMETHODCALLTYPE *AddSnapshotToRecoverySet)(
        IVssBackupComponentsEx4 *This,
        VSS_ID snapshotId,
        DWORD dwFlags,
        VSS_PWSZ pwszDestinationVolume);

    HRESULT (STDMETHODCALLTYPE *RecoverSet)(
        IVssBackupComponentsEx4 *This,
        DWORD dwFlags,
        IVssAsync **ppAsync);

    HRESULT (STDMETHODCALLTYPE *GetSessionId)(
        IVssBackupComponentsEx4 *This,
        VSS_ID *idSession);

    /*** IVssBackupComponentsEx4 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetRootAndLogicalPrefixPaths)(
        IVssBackupComponentsEx4 *This,
        VSS_PWSZ pwszFilePath,
        VSS_PWSZ *ppwszRootPath,
        VSS_PWSZ *ppwszLogicalPrefix,
        WINBOOL bNormalizeFQDNforRootPath);

    END_INTERFACE
} IVssBackupComponentsEx4Vtbl;

interface IVssBackupComponentsEx4 {
    CONST_VTBL IVssBackupComponentsEx4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IVssBackupComponentsEx4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IVssBackupComponentsEx4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IVssBackupComponentsEx4_Release(This) (This)->lpVtbl->Release(This)
/*** IVssBackupComponents methods ***/
#define IVssBackupComponentsEx4_GetWriterComponentsCount(This,pcComponents) (This)->lpVtbl->GetWriterComponentsCount(This,pcComponents)
#define IVssBackupComponentsEx4_GetWriterComponents(This,iWriter,ppWriter) (This)->lpVtbl->GetWriterComponents(This,iWriter,ppWriter)
#define IVssBackupComponentsEx4_InitializeForBackup(This,bstrXML) (This)->lpVtbl->InitializeForBackup(This,bstrXML)
#define IVssBackupComponentsEx4_SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport) (This)->lpVtbl->SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport)
#define IVssBackupComponentsEx4_InitializeForRestore(This,bstrXML) (This)->lpVtbl->InitializeForRestore(This,bstrXML)
#define IVssBackupComponentsEx4_SetRestoreState(This,restoreType) (This)->lpVtbl->SetRestoreState(This,restoreType)
#define IVssBackupComponentsEx4_GatherWriterMetadata(This,pAsync) (This)->lpVtbl->GatherWriterMetadata(This,pAsync)
#define IVssBackupComponentsEx4_GetWriterMetadataCount(This,pcWriters) (This)->lpVtbl->GetWriterMetadataCount(This,pcWriters)
#define IVssBackupComponentsEx4_GetWriterMetadata(This,iWriter,pidInstance,ppMetadata) (This)->lpVtbl->GetWriterMetadata(This,iWriter,pidInstance,ppMetadata)
#define IVssBackupComponentsEx4_FreeWriterMetadata(This) (This)->lpVtbl->FreeWriterMetadata(This)
#define IVssBackupComponentsEx4_AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName) (This)->lpVtbl->AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName)
#define IVssBackupComponentsEx4_PrepareForBackup(This,ppAsync) (This)->lpVtbl->PrepareForBackup(This,ppAsync)
#define IVssBackupComponentsEx4_AbortBackup(This) (This)->lpVtbl->AbortBackup(This)
#define IVssBackupComponentsEx4_GatherWriterStatus(This,pAsync) (This)->lpVtbl->GatherWriterStatus(This,pAsync)
#define IVssBackupComponentsEx4_GetWriterStatusCount(This,pcWriters) (This)->lpVtbl->GetWriterStatusCount(This,pcWriters)
#define IVssBackupComponentsEx4_FreeWriterStatus(This) (This)->lpVtbl->FreeWriterStatus(This)
#define IVssBackupComponentsEx4_GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure) (This)->lpVtbl->GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure)
#define IVssBackupComponentsEx4_SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded) (This)->lpVtbl->SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded)
#define IVssBackupComponentsEx4_SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions) (This)->lpVtbl->SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions)
#define IVssBackupComponentsEx4_SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore) (This)->lpVtbl->SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore)
#define IVssBackupComponentsEx4_SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions) (This)->lpVtbl->SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions)
#define IVssBackupComponentsEx4_SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores) (This)->lpVtbl->SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores)
#define IVssBackupComponentsEx4_SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp) (This)->lpVtbl->SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp)
#define IVssBackupComponentsEx4_SaveAsXML(This,pbstrXML) (This)->lpVtbl->SaveAsXML(This,pbstrXML)
#define IVssBackupComponentsEx4_BackupComplete(This,ppAsync) (This)->lpVtbl->BackupComplete(This,ppAsync)
#define IVssBackupComponentsEx4_AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination) (This)->lpVtbl->AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination)
#define IVssBackupComponentsEx4_AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair) (This)->lpVtbl->AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair)
#define IVssBackupComponentsEx4_SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status) (This)->lpVtbl->SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status)
#define IVssBackupComponentsEx4_AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath) (This)->lpVtbl->AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath)
#define IVssBackupComponentsEx4_SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile) (This)->lpVtbl->SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile)
#define IVssBackupComponentsEx4_PreRestore(This,ppAsync) (This)->lpVtbl->PreRestore(This,ppAsync)
#define IVssBackupComponentsEx4_PostRestore(This,ppAsync) (This)->lpVtbl->PostRestore(This,ppAsync)
#define IVssBackupComponentsEx4_SetContext(This,lContext) (This)->lpVtbl->SetContext(This,lContext)
#define IVssBackupComponentsEx4_StartSnapshotSet(This,pSnapshotSetId) (This)->lpVtbl->StartSnapshotSet(This,pSnapshotSetId)
#define IVssBackupComponentsEx4_AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot) (This)->lpVtbl->AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot)
#define IVssBackupComponentsEx4_DoSnapshotSet(This,ppAsync) (This)->lpVtbl->DoSnapshotSet(This,ppAsync)
#define IVssBackupComponentsEx4_DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID) (This)->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID)
#define IVssBackupComponentsEx4_ImportSnapshots(This,ppAsync) (This)->lpVtbl->ImportSnapshots(This,ppAsync)
#define IVssBackupComponentsEx4_BreakSnapshotSet(This,SnapshotSetId) (This)->lpVtbl->BreakSnapshotSet(This,SnapshotSetId)
#define IVssBackupComponentsEx4_GetSnapshotProperties(This,SnapshotId,pProp) (This)->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp)
#define IVssBackupComponentsEx4_Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum) (This)->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum)
#define IVssBackupComponentsEx4_IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider) (This)->lpVtbl->IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider)
#define IVssBackupComponentsEx4_DisableWriterClasses(This,rgWriterClassId,cClassId) (This)->lpVtbl->DisableWriterClasses(This,rgWriterClassId,cClassId)
#define IVssBackupComponentsEx4_EnableWriterClasses(This,rgWriterClassId,cClassId) (This)->lpVtbl->EnableWriterClasses(This,rgWriterClassId,cClassId)
#define IVssBackupComponentsEx4_DisableWriterInstances(This,rgWriterInstanceId,cInstanceId) (This)->lpVtbl->DisableWriterInstances(This,rgWriterInstanceId,cInstanceId)
#define IVssBackupComponentsEx4_ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed) (This)->lpVtbl->ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed)
#define IVssBackupComponentsEx4_RevertToSnapshot(This,SnapshotId,bForceDismount) (This)->lpVtbl->RevertToSnapshot(This,SnapshotId,bForceDismount)
#define IVssBackupComponentsEx4_QueryRevertStatus(This,pwszVolume,ppAsync) (This)->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync)
/*** IVssBackupComponentsEx methods ***/
#define IVssBackupComponentsEx4_GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata) (This)->lpVtbl->GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata)
#define IVssBackupComponentsEx4_SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId) (This)->lpVtbl->SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId)
/*** IVssBackupComponentsEx2 methods ***/
#define IVssBackupComponentsEx4_UnexposeSnapshot(This,snapshotId) (This)->lpVtbl->UnexposeSnapshot(This,snapshotId)
#define IVssBackupComponentsEx4_SetAuthoritativeRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bAuth) (This)->lpVtbl->SetAuthoritativeRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bAuth)
#define IVssBackupComponentsEx4_SetRollForward(This,writerId,ct,wszLogicalPath,wszComponentName,rollType,wszRollForwardPoint) (This)->lpVtbl->SetRollForward(This,writerId,ct,wszLogicalPath,wszComponentName,rollType,wszRollForwardPoint)
#define IVssBackupComponentsEx4_SetRestoreName(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreName) (This)->lpVtbl->SetRestoreName(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreName)
#define IVssBackupComponentsEx4_BreakSnapshotSetEx(This,SnapshotSetID,dwBreakFlags,ppAsync) (This)->lpVtbl->BreakSnapshotSetEx(This,SnapshotSetID,dwBreakFlags,ppAsync)
#define IVssBackupComponentsEx4_PreFastRecovery(This,SnapshotSetID,dwPreFastRecoveryFlags,ppAsync) (This)->lpVtbl->PreFastRecovery(This,SnapshotSetID,dwPreFastRecoveryFlags,ppAsync)
#define IVssBackupComponentsEx4_FastRecovery(This,SnapshotSetID,dwFastRecoveryFlags,ppAsync) (This)->lpVtbl->FastRecovery(This,SnapshotSetID,dwFastRecoveryFlags,ppAsync)
/*** IVssBackupComponentsEx3 methods ***/
#define IVssBackupComponentsEx4_GetWriterStatusEx(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phrFailureWriter,phrApplication,pbstrApplicationMessage) (This)->lpVtbl->GetWriterStatusEx(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phrFailureWriter,phrApplication,pbstrApplicationMessage)
#define IVssBackupComponentsEx4_AddSnapshotToRecoverySet(This,snapshotId,dwFlags,pwszDestinationVolume) (This)->lpVtbl->AddSnapshotToRecoverySet(This,snapshotId,dwFlags,pwszDestinationVolume)
#define IVssBackupComponentsEx4_RecoverSet(This,dwFlags,ppAsync) (This)->lpVtbl->RecoverSet(This,dwFlags,ppAsync)
#define IVssBackupComponentsEx4_GetSessionId(This,idSession) (This)->lpVtbl->GetSessionId(This,idSession)
/*** IVssBackupComponentsEx4 methods ***/
#define IVssBackupComponentsEx4_GetRootAndLogicalPrefixPaths(This,pwszFilePath,ppwszRootPath,ppwszLogicalPrefix,bNormalizeFQDNforRootPath) (This)->lpVtbl->GetRootAndLogicalPrefixPaths(This,pwszFilePath,ppwszRootPath,ppwszLogicalPrefix,bNormalizeFQDNforRootPath)
#else
/*** IUnknown methods ***/
static inline HRESULT IVssBackupComponentsEx4_QueryInterface(IVssBackupComponentsEx4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IVssBackupComponentsEx4_AddRef(IVssBackupComponentsEx4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IVssBackupComponentsEx4_Release(IVssBackupComponentsEx4* This) {
    return This->lpVtbl->Release(This);
}
/*** IVssBackupComponents methods ***/
static inline HRESULT IVssBackupComponentsEx4_GetWriterComponentsCount(IVssBackupComponentsEx4* This,UINT *pcComponents) {
    return This->lpVtbl->GetWriterComponentsCount(This,pcComponents);
}
static inline HRESULT IVssBackupComponentsEx4_GetWriterComponents(IVssBackupComponentsEx4* This,UINT iWriter,IVssWriterComponentsExt **ppWriter) {
    return This->lpVtbl->GetWriterComponents(This,iWriter,ppWriter);
}
static inline HRESULT IVssBackupComponentsEx4_InitializeForBackup(IVssBackupComponentsEx4* This,BSTR bstrXML) {
    return This->lpVtbl->InitializeForBackup(This,bstrXML);
}
static inline HRESULT IVssBackupComponentsEx4_SetBackupState(IVssBackupComponentsEx4* This,boolean bSelectComponents,boolean bBackupBootableSystemState,VSS_BACKUP_TYPE backupType,boolean bPartialFileSupport) {
    return This->lpVtbl->SetBackupState(This,bSelectComponents,bBackupBootableSystemState,backupType,bPartialFileSupport);
}
static inline HRESULT IVssBackupComponentsEx4_InitializeForRestore(IVssBackupComponentsEx4* This,BSTR bstrXML) {
    return This->lpVtbl->InitializeForRestore(This,bstrXML);
}
static inline HRESULT IVssBackupComponentsEx4_SetRestoreState(IVssBackupComponentsEx4* This,VSS_RESTORE_TYPE restoreType) {
    return This->lpVtbl->SetRestoreState(This,restoreType);
}
static inline HRESULT IVssBackupComponentsEx4_GatherWriterMetadata(IVssBackupComponentsEx4* This,IVssAsync **pAsync) {
    return This->lpVtbl->GatherWriterMetadata(This,pAsync);
}
static inline HRESULT IVssBackupComponentsEx4_GetWriterMetadataCount(IVssBackupComponentsEx4* This,UINT *pcWriters) {
    return This->lpVtbl->GetWriterMetadataCount(This,pcWriters);
}
static inline HRESULT IVssBackupComponentsEx4_GetWriterMetadata(IVssBackupComponentsEx4* This,UINT iWriter,VSS_ID *pidInstance,IVssExamineWriterMetadata **ppMetadata) {
    return This->lpVtbl->GetWriterMetadata(This,iWriter,pidInstance,ppMetadata);
}
static inline HRESULT IVssBackupComponentsEx4_FreeWriterMetadata(IVssBackupComponentsEx4* This) {
    return This->lpVtbl->FreeWriterMetadata(This);
}
static inline HRESULT IVssBackupComponentsEx4_AddComponent(IVssBackupComponentsEx4* This,VSS_ID instanceId,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName) {
    return This->lpVtbl->AddComponent(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName);
}
static inline HRESULT IVssBackupComponentsEx4_PrepareForBackup(IVssBackupComponentsEx4* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PrepareForBackup(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx4_AbortBackup(IVssBackupComponentsEx4* This) {
    return This->lpVtbl->AbortBackup(This);
}
static inline HRESULT IVssBackupComponentsEx4_GatherWriterStatus(IVssBackupComponentsEx4* This,IVssAsync **pAsync) {
    return This->lpVtbl->GatherWriterStatus(This,pAsync);
}
static inline HRESULT IVssBackupComponentsEx4_GetWriterStatusCount(IVssBackupComponentsEx4* This,UINT *pcWriters) {
    return This->lpVtbl->GetWriterStatusCount(This,pcWriters);
}
static inline HRESULT IVssBackupComponentsEx4_FreeWriterStatus(IVssBackupComponentsEx4* This) {
    return This->lpVtbl->FreeWriterStatus(This);
}
static inline HRESULT IVssBackupComponentsEx4_GetWriterStatus(IVssBackupComponentsEx4* This,UINT iWriter,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriter,VSS_WRITER_STATE *pnStatus,HRESULT *phResultFailure) {
    return This->lpVtbl->GetWriterStatus(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phResultFailure);
}
static inline HRESULT IVssBackupComponentsEx4_SetBackupSucceeded(IVssBackupComponentsEx4* This,VSS_ID instanceId,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSucceded) {
    return This->lpVtbl->SetBackupSucceeded(This,instanceId,writerId,ct,wszLogicalPath,wszComponentName,bSucceded);
}
static inline HRESULT IVssBackupComponentsEx4_SetBackupOptions(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszBackupOptions) {
    return This->lpVtbl->SetBackupOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszBackupOptions);
}
static inline HRESULT IVssBackupComponentsEx4_SetSelectedForRestore(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSelectedForRestore) {
    return This->lpVtbl->SetSelectedForRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore);
}
static inline HRESULT IVssBackupComponentsEx4_SetRestoreOptions(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszRestoreOptions) {
    return This->lpVtbl->SetRestoreOptions(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreOptions);
}
static inline HRESULT IVssBackupComponentsEx4_SetAdditionalRestores(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bAdditionalRestores) {
    return This->lpVtbl->SetAdditionalRestores(This,writerId,ct,wszLogicalPath,wszComponentName,bAdditionalRestores);
}
static inline HRESULT IVssBackupComponentsEx4_SetPreviousBackupStamp(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPreviousBackupStamp) {
    return This->lpVtbl->SetPreviousBackupStamp(This,writerId,ct,wszLogicalPath,wszComponentName,wszPreviousBackupStamp);
}
static inline HRESULT IVssBackupComponentsEx4_SaveAsXML(IVssBackupComponentsEx4* This,BSTR *pbstrXML) {
    return This->lpVtbl->SaveAsXML(This,pbstrXML);
}
static inline HRESULT IVssBackupComponentsEx4_BackupComplete(IVssBackupComponentsEx4* This,IVssAsync **ppAsync) {
    return This->lpVtbl->BackupComplete(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx4_AddAlternativeLocationMapping(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE componentType,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPath,LPCWSTR wszFilespec,boolean bRecursive,LPCWSTR wszDestination) {
    return This->lpVtbl->AddAlternativeLocationMapping(This,writerId,componentType,wszLogicalPath,wszComponentName,wszPath,wszFilespec,bRecursive,wszDestination);
}
static inline HRESULT IVssBackupComponentsEx4_AddRestoreSubcomponent(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE componentType,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszSubComponentLogicalPath,LPCWSTR wszSubComponentName,boolean bRepair) {
    return This->lpVtbl->AddRestoreSubcomponent(This,writerId,componentType,wszLogicalPath,wszComponentName,wszSubComponentLogicalPath,wszSubComponentName,bRepair);
}
static inline HRESULT IVssBackupComponentsEx4_SetFileRestoreStatus(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,VSS_FILE_RESTORE_STATUS status) {
    return This->lpVtbl->SetFileRestoreStatus(This,writerId,ct,wszLogicalPath,wszComponentName,status);
}
static inline HRESULT IVssBackupComponentsEx4_AddNewTarget(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszPath,LPCWSTR wszFileName,boolean bRecursive,LPCWSTR wszAlternatePath) {
    return This->lpVtbl->AddNewTarget(This,writerId,ct,wszLogicalPath,wszComponentName,wszPath,wszFileName,bRecursive,wszAlternatePath);
}
static inline HRESULT IVssBackupComponentsEx4_SetRangesFilePath(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,UINT iPartialFile,LPCWSTR wszRangesFile) {
    return This->lpVtbl->SetRangesFilePath(This,writerId,ct,wszLogicalPath,wszComponentName,iPartialFile,wszRangesFile);
}
static inline HRESULT IVssBackupComponentsEx4_PreRestore(IVssBackupComponentsEx4* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PreRestore(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx4_PostRestore(IVssBackupComponentsEx4* This,IVssAsync **ppAsync) {
    return This->lpVtbl->PostRestore(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx4_SetContext(IVssBackupComponentsEx4* This,LONG lContext) {
    return This->lpVtbl->SetContext(This,lContext);
}
static inline HRESULT IVssBackupComponentsEx4_StartSnapshotSet(IVssBackupComponentsEx4* This,VSS_ID *pSnapshotSetId) {
    return This->lpVtbl->StartSnapshotSet(This,pSnapshotSetId);
}
static inline HRESULT IVssBackupComponentsEx4_AddToSnapshotSet(IVssBackupComponentsEx4* This,VSS_PWSZ pwszVolumeName,VSS_ID ProviderId,VSS_ID *pidSnapshot) {
    return This->lpVtbl->AddToSnapshotSet(This,pwszVolumeName,ProviderId,pidSnapshot);
}
static inline HRESULT IVssBackupComponentsEx4_DoSnapshotSet(IVssBackupComponentsEx4* This,IVssAsync **ppAsync) {
    return This->lpVtbl->DoSnapshotSet(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx4_DeleteSnapshots(IVssBackupComponentsEx4* This,VSS_ID SourceObjectId,VSS_OBJECT_TYPE eSourceObjectType,WINBOOL bForceDelete,LONG *plDeletedSnapshots,VSS_ID *pNondeletedSnapshotID) {
    return This->lpVtbl->DeleteSnapshots(This,SourceObjectId,eSourceObjectType,bForceDelete,plDeletedSnapshots,pNondeletedSnapshotID);
}
static inline HRESULT IVssBackupComponentsEx4_ImportSnapshots(IVssBackupComponentsEx4* This,IVssAsync **ppAsync) {
    return This->lpVtbl->ImportSnapshots(This,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx4_BreakSnapshotSet(IVssBackupComponentsEx4* This,VSS_ID SnapshotSetId) {
    return This->lpVtbl->BreakSnapshotSet(This,SnapshotSetId);
}
static inline HRESULT IVssBackupComponentsEx4_GetSnapshotProperties(IVssBackupComponentsEx4* This,VSS_ID SnapshotId,VSS_SNAPSHOT_PROP *pProp) {
    return This->lpVtbl->GetSnapshotProperties(This,SnapshotId,pProp);
}
static inline HRESULT IVssBackupComponentsEx4_Query(IVssBackupComponentsEx4* This,VSS_ID QueriedObjectId,VSS_OBJECT_TYPE eQueriedObjectType,VSS_OBJECT_TYPE eReturnedObjectsType,IVssEnumObject **ppEnum) {
    return This->lpVtbl->Query(This,QueriedObjectId,eQueriedObjectType,eReturnedObjectsType,ppEnum);
}
static inline HRESULT IVssBackupComponentsEx4_IsVolumeSupported(IVssBackupComponentsEx4* This,VSS_ID ProviderId,VSS_PWSZ pwszVolumeName,WINBOOL *pbSupportedByThisProvider) {
    return This->lpVtbl->IsVolumeSupported(This,ProviderId,pwszVolumeName,pbSupportedByThisProvider);
}
static inline HRESULT IVssBackupComponentsEx4_DisableWriterClasses(IVssBackupComponentsEx4* This,const VSS_ID *rgWriterClassId,UINT cClassId) {
    return This->lpVtbl->DisableWriterClasses(This,rgWriterClassId,cClassId);
}
static inline HRESULT IVssBackupComponentsEx4_EnableWriterClasses(IVssBackupComponentsEx4* This,const VSS_ID *rgWriterClassId,UINT cClassId) {
    return This->lpVtbl->EnableWriterClasses(This,rgWriterClassId,cClassId);
}
static inline HRESULT IVssBackupComponentsEx4_DisableWriterInstances(IVssBackupComponentsEx4* This,const VSS_ID *rgWriterInstanceId,UINT cInstanceId) {
    return This->lpVtbl->DisableWriterInstances(This,rgWriterInstanceId,cInstanceId);
}
static inline HRESULT IVssBackupComponentsEx4_ExposeSnapshot(IVssBackupComponentsEx4* This,VSS_ID SnapshotId,VSS_PWSZ wszPathFromRoot,LONG lAttributes,VSS_PWSZ wszExpose,VSS_PWSZ *pwszExposed) {
    return This->lpVtbl->ExposeSnapshot(This,SnapshotId,wszPathFromRoot,lAttributes,wszExpose,pwszExposed);
}
static inline HRESULT IVssBackupComponentsEx4_RevertToSnapshot(IVssBackupComponentsEx4* This,VSS_ID SnapshotId,WINBOOL bForceDismount) {
    return This->lpVtbl->RevertToSnapshot(This,SnapshotId,bForceDismount);
}
static inline HRESULT IVssBackupComponentsEx4_QueryRevertStatus(IVssBackupComponentsEx4* This,VSS_PWSZ pwszVolume,IVssAsync **ppAsync) {
    return This->lpVtbl->QueryRevertStatus(This,pwszVolume,ppAsync);
}
/*** IVssBackupComponentsEx methods ***/
static inline HRESULT IVssBackupComponentsEx4_GetWriterMetadataEx(IVssBackupComponentsEx4* This,UINT iWriter,VSS_ID *pidInstance,IVssExamineWriterMetadataEx **ppMetadata) {
    return This->lpVtbl->GetWriterMetadataEx(This,iWriter,pidInstance,ppMetadata);
}
static inline HRESULT IVssBackupComponentsEx4_SetSelectedForRestoreEx(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bSelectedForRestore,VSS_ID instanceId) {
    return This->lpVtbl->SetSelectedForRestoreEx(This,writerId,ct,wszLogicalPath,wszComponentName,bSelectedForRestore,instanceId);
}
/*** IVssBackupComponentsEx2 methods ***/
static inline HRESULT IVssBackupComponentsEx4_UnexposeSnapshot(IVssBackupComponentsEx4* This,VSS_ID snapshotId) {
    return This->lpVtbl->UnexposeSnapshot(This,snapshotId);
}
static inline HRESULT IVssBackupComponentsEx4_SetAuthoritativeRestore(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,boolean bAuth) {
    return This->lpVtbl->SetAuthoritativeRestore(This,writerId,ct,wszLogicalPath,wszComponentName,bAuth);
}
static inline HRESULT IVssBackupComponentsEx4_SetRollForward(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,VSS_ROLLFORWARD_TYPE rollType,LPCWSTR wszRollForwardPoint) {
    return This->lpVtbl->SetRollForward(This,writerId,ct,wszLogicalPath,wszComponentName,rollType,wszRollForwardPoint);
}
static inline HRESULT IVssBackupComponentsEx4_SetRestoreName(IVssBackupComponentsEx4* This,VSS_ID writerId,VSS_COMPONENT_TYPE ct,LPCWSTR wszLogicalPath,LPCWSTR wszComponentName,LPCWSTR wszRestoreName) {
    return This->lpVtbl->SetRestoreName(This,writerId,ct,wszLogicalPath,wszComponentName,wszRestoreName);
}
static inline HRESULT IVssBackupComponentsEx4_BreakSnapshotSetEx(IVssBackupComponentsEx4* This,VSS_ID SnapshotSetID,DWORD dwBreakFlags,IVssAsync **ppAsync) {
    return This->lpVtbl->BreakSnapshotSetEx(This,SnapshotSetID,dwBreakFlags,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx4_PreFastRecovery(IVssBackupComponentsEx4* This,VSS_ID SnapshotSetID,DWORD dwPreFastRecoveryFlags,IVssAsync **ppAsync) {
    return This->lpVtbl->PreFastRecovery(This,SnapshotSetID,dwPreFastRecoveryFlags,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx4_FastRecovery(IVssBackupComponentsEx4* This,VSS_ID SnapshotSetID,DWORD dwFastRecoveryFlags,IVssAsync **ppAsync) {
    return This->lpVtbl->FastRecovery(This,SnapshotSetID,dwFastRecoveryFlags,ppAsync);
}
/*** IVssBackupComponentsEx3 methods ***/
static inline HRESULT IVssBackupComponentsEx4_GetWriterStatusEx(IVssBackupComponentsEx4* This,UINT iWriter,VSS_ID *pidInstance,VSS_ID *pidWriter,BSTR *pbstrWriter,VSS_WRITER_STATE *pnStatus,HRESULT *phrFailureWriter,HRESULT *phrApplication,BSTR *pbstrApplicationMessage) {
    return This->lpVtbl->GetWriterStatusEx(This,iWriter,pidInstance,pidWriter,pbstrWriter,pnStatus,phrFailureWriter,phrApplication,pbstrApplicationMessage);
}
static inline HRESULT IVssBackupComponentsEx4_AddSnapshotToRecoverySet(IVssBackupComponentsEx4* This,VSS_ID snapshotId,DWORD dwFlags,VSS_PWSZ pwszDestinationVolume) {
    return This->lpVtbl->AddSnapshotToRecoverySet(This,snapshotId,dwFlags,pwszDestinationVolume);
}
static inline HRESULT IVssBackupComponentsEx4_RecoverSet(IVssBackupComponentsEx4* This,DWORD dwFlags,IVssAsync **ppAsync) {
    return This->lpVtbl->RecoverSet(This,dwFlags,ppAsync);
}
static inline HRESULT IVssBackupComponentsEx4_GetSessionId(IVssBackupComponentsEx4* This,VSS_ID *idSession) {
    return This->lpVtbl->GetSessionId(This,idSession);
}
/*** IVssBackupComponentsEx4 methods ***/
static inline HRESULT IVssBackupComponentsEx4_GetRootAndLogicalPrefixPaths(IVssBackupComponentsEx4* This,VSS_PWSZ pwszFilePath,VSS_PWSZ *ppwszRootPath,VSS_PWSZ *ppwszLogicalPrefix,WINBOOL bNormalizeFQDNforRootPath) {
    return This->lpVtbl->GetRootAndLogicalPrefixPaths(This,pwszFilePath,ppwszRootPath,ppwszLogicalPrefix,bNormalizeFQDNforRootPath);
}
#endif
#endif

#endif


#endif  /* __IVssBackupComponentsEx4_INTERFACE_DEFINED__ */

#define VSS_SW_BOOTABLE_STATE (1 << 0)
HRESULT STDAPICALLTYPE CreateVssBackupComponentsInternal(IVssBackupComponents **ppBackup);
HRESULT STDAPICALLTYPE CreateVssExamineWriterMetadataInternal(BSTR bstrXML, IVssExamineWriterMetadata **ppMetadata);
HRESULT APIENTRY IsVolumeSnapshottedInternal(VSS_PWSZ pwszVolumeName, WINBOOL *pbSnapshotsPresent, LONG *plSnapshotCapability);
void APIENTRY VssFreeSnapshotPropertiesInternal(VSS_SNAPSHOT_PROP *pProp);
HRESULT APIENTRY GetProviderMgmtInterfaceInternal(VSS_ID ProviderId, IID InterfaceId, IUnknown **ppItf);
HRESULT APIENTRY ShouldBlockRevertInternal(LPCWSTR wszVolumeName, boolean *pbBlock);
static __inline HRESULT CreateVssBackupComponents(IVssBackupComponents **ppBackup) { return CreateVssBackupComponentsInternal(ppBackup); }
static __inline HRESULT CreateVssExamineWriterMetadata(BSTR bstrXML, IVssExamineWriterMetadata **ppMetadata) { return CreateVssExamineWriterMetadataInternal(bstrXML, ppMetadata); }
static __inline HRESULT IsVolumeSnapshotted(VSS_PWSZ pwszVolumeName, WINBOOL *pbSnapshotsPresent, LONG *plSnapshotCapability) { return IsVolumeSnapshottedInternal(pwszVolumeName, pbSnapshotsPresent, plSnapshotCapability); }
static __inline void VssFreeSnapshotProperties(VSS_SNAPSHOT_PROP *pProp) { return VssFreeSnapshotPropertiesInternal(pProp); }
static __inline HRESULT GetProviderMgmtInterface(VSS_ID ProviderId, IID InterfaceId, IUnknown **ppItf) { return GetProviderMgmtInterfaceInternal(ProviderId, InterfaceId, ppItf); }
static __inline HRESULT ShouldBlockRevert(LPCWSTR wszVolumeName, boolean *pbBlock) { return ShouldBlockRevertInternal(wszVolumeName, pbBlock); }
#endif /* WINAPI_PARTITION_DESKTOP */
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __vsbackup_h__ */
