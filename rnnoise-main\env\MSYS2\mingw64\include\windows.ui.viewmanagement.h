/*** Autogenerated by WIDL 10.12 from include/windows.ui.viewmanagement.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_ui_viewmanagement_h__
#define __windows_ui_viewmanagement_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings __x_ABI_CWindows_CUI_CViewManagement_CIUISettings;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings ABI::Windows::UI::ViewManagement::IUISettings
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 ABI::Windows::UI::ViewManagement::IUISettings2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 ABI::Windows::UI::ViewManagement::IUISettings3
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 ABI::Windows::UI::ViewManagement::IUISettings4
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings4;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 ABI::Windows::UI::ViewManagement::IUISettings5
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings5;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 ABI::Windows::UI::ViewManagement::IUISettings6
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings6;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs ABI::Windows::UI::ViewManagement::IUISettingsAnimationsEnabledChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettingsAnimationsEnabledChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs ABI::Windows::UI::ViewManagement::IUISettingsAutoHideScrollBarsChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettingsAutoHideScrollBarsChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs ABI::Windows::UI::ViewManagement::IUISettingsMessageDurationChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettingsMessageDurationChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings ABI::Windows::UI::ViewManagement::IUIViewSettings
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUIViewSettings;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics ABI::Windows::UI::ViewManagement::IUIViewSettingsStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUIViewSettingsStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPane __x_ABI_CWindows_CUI_CViewManagement_CIInputPane;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane ABI::Windows::UI::ViewManagement::IInputPane
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPane;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 ABI::Windows::UI::ViewManagement::IInputPane2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPane2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl ABI::Windows::UI::ViewManagement::IInputPaneControl
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPaneControl;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs ABI::Windows::UI::ViewManagement::IInputPaneVisibilityEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPaneVisibilityEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics ABI::Windows::UI::ViewManagement::IInputPaneStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPaneStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 ABI::Windows::UI::ViewManagement::IInputPaneStatics2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPaneStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CUISettings_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CUISettings_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                class UISettings;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CUISettings __x_ABI_CWindows_CUI_CViewManagement_CUISettings;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CUISettings_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CUISettingsAnimationsEnabledChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CUISettingsAnimationsEnabledChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                class UISettingsAnimationsEnabledChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CUISettingsAnimationsEnabledChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CUISettingsAnimationsEnabledChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CUISettingsAnimationsEnabledChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CUISettingsAutoHideScrollBarsChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CUISettingsAutoHideScrollBarsChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                class UISettingsAutoHideScrollBarsChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CUISettingsAutoHideScrollBarsChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CUISettingsAutoHideScrollBarsChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CUISettingsAutoHideScrollBarsChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CUISettingsMessageDurationChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CUISettingsMessageDurationChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                class UISettingsMessageDurationChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CUISettingsMessageDurationChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CUISettingsMessageDurationChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CUISettingsMessageDurationChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CUIViewSettings_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CUIViewSettings_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                class UIViewSettings;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CUIViewSettings __x_ABI_CWindows_CUI_CViewManagement_CUIViewSettings;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CUIViewSettings_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CInputPane_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CInputPane_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                class InputPane;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CInputPane __x_ABI_CWindows_CUI_CViewManagement_CInputPane;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CInputPane_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CInputPaneVisibilityEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CInputPaneVisibilityEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                class InputPaneVisibilityEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CInputPaneVisibilityEventArgs __x_ABI_CWindows_CUI_CViewManagement_CInputPaneVisibilityEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CUI_CViewManagement_CInputPaneVisibilityEventArgs_FWD_DEFINED__ */

#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,IInspectable* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAnimationsEnabledChangedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAutoHideScrollBarsChangedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsMessageDurationChangedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::InputPane*,ABI::Windows::UI::ViewManagement::InputPaneVisibilityEventArgs* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.devices.enumeration.h>
#include <windows.ui.h>
#include <windows.ui.core.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CHandPreference_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CHandPreference_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                enum HandPreference {
                    HandPreference_LeftHanded = 0,
                    HandPreference_RightHanded = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CViewManagement_CHandPreference {
    HandPreference_LeftHanded = 0,
    HandPreference_RightHanded = 1
};
#ifdef WIDL_using_Windows_UI_ViewManagement
#define HandPreference __x_ABI_CWindows_CUI_CViewManagement_CHandPreference
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif /* ____x_ABI_CWindows_CUI_CViewManagement_CHandPreference_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CViewManagement_CHandPreference __x_ABI_CWindows_CUI_CViewManagement_CHandPreference;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CUIColorType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CUIColorType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                enum UIColorType {
                    UIColorType_Background = 0,
                    UIColorType_Foreground = 1,
                    UIColorType_AccentDark3 = 2,
                    UIColorType_AccentDark2 = 3,
                    UIColorType_AccentDark1 = 4,
                    UIColorType_Accent = 5,
                    UIColorType_AccentLight1 = 6,
                    UIColorType_AccentLight2 = 7,
                    UIColorType_AccentLight3 = 8,
                    UIColorType_Complement = 9
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CViewManagement_CUIColorType {
    UIColorType_Background = 0,
    UIColorType_Foreground = 1,
    UIColorType_AccentDark3 = 2,
    UIColorType_AccentDark2 = 3,
    UIColorType_AccentDark1 = 4,
    UIColorType_Accent = 5,
    UIColorType_AccentLight1 = 6,
    UIColorType_AccentLight2 = 7,
    UIColorType_AccentLight3 = 8,
    UIColorType_Complement = 9
};
#ifdef WIDL_using_Windows_UI_ViewManagement
#define UIColorType __x_ABI_CWindows_CUI_CViewManagement_CUIColorType
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif /* ____x_ABI_CWindows_CUI_CViewManagement_CUIColorType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CViewManagement_CUIColorType __x_ABI_CWindows_CUI_CViewManagement_CUIColorType;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CUIElementType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CUIElementType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                enum UIElementType {
                    UIElementType_ActiveCaption = 0,
                    UIElementType_Background = 1,
                    UIElementType_ButtonFace = 2,
                    UIElementType_ButtonText = 3,
                    UIElementType_CaptionText = 4,
                    UIElementType_GrayText = 5,
                    UIElementType_Highlight = 6,
                    UIElementType_HighlightText = 7,
                    UIElementType_Hotlight = 8,
                    UIElementType_InactiveCaption = 9,
                    UIElementType_InactiveCaptionText = 10,
                    UIElementType_Window = 11,
                    UIElementType_WindowText = 12,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_AccentColor = 1000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_TextHigh = 1001,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_TextMedium = 1002,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_TextLow = 1003,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_TextContrastWithHigh = 1004,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_NonTextHigh = 1005,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_NonTextMediumHigh = 1006,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_NonTextMedium = 1007,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_NonTextMediumLow = 1008,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_NonTextLow = 1009,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_PageBackground = 1010,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_PopupBackground = 1011,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    UIElementType_OverlayOutsidePopup = 1012
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CViewManagement_CUIElementType {
    UIElementType_ActiveCaption = 0,
    UIElementType_Background = 1,
    UIElementType_ButtonFace = 2,
    UIElementType_ButtonText = 3,
    UIElementType_CaptionText = 4,
    UIElementType_GrayText = 5,
    UIElementType_Highlight = 6,
    UIElementType_HighlightText = 7,
    UIElementType_Hotlight = 8,
    UIElementType_InactiveCaption = 9,
    UIElementType_InactiveCaptionText = 10,
    UIElementType_Window = 11,
    UIElementType_WindowText = 12,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_AccentColor = 1000,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_TextHigh = 1001,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_TextMedium = 1002,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_TextLow = 1003,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_TextContrastWithHigh = 1004,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_NonTextHigh = 1005,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_NonTextMediumHigh = 1006,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_NonTextMedium = 1007,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_NonTextMediumLow = 1008,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_NonTextLow = 1009,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_PageBackground = 1010,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_PopupBackground = 1011,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    UIElementType_OverlayOutsidePopup = 1012
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
};
#ifdef WIDL_using_Windows_UI_ViewManagement
#define UIElementType __x_ABI_CWindows_CUI_CViewManagement_CUIElementType
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif /* ____x_ABI_CWindows_CUI_CViewManagement_CUIElementType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CViewManagement_CUIElementType __x_ABI_CWindows_CUI_CViewManagement_CUIElementType;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CUserInteractionMode_ENUM_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CUserInteractionMode_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                enum UserInteractionMode {
                    UserInteractionMode_Mouse = 0,
                    UserInteractionMode_Touch = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CUI_CViewManagement_CUserInteractionMode {
    UserInteractionMode_Mouse = 0,
    UserInteractionMode_Touch = 1
};
#ifdef WIDL_using_Windows_UI_ViewManagement
#define UserInteractionMode __x_ABI_CWindows_CUI_CViewManagement_CUserInteractionMode
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif /* ____x_ABI_CWindows_CUI_CViewManagement_CUserInteractionMode_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CUI_CViewManagement_CUserInteractionMode __x_ABI_CWindows_CUI_CViewManagement_CUserInteractionMode;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings __x_ABI_CWindows_CUI_CViewManagement_CIUISettings;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings ABI::Windows::UI::ViewManagement::IUISettings
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 ABI::Windows::UI::ViewManagement::IUISettings2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 ABI::Windows::UI::ViewManagement::IUISettings3
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings3;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 ABI::Windows::UI::ViewManagement::IUISettings4
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings4;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 ABI::Windows::UI::ViewManagement::IUISettings5
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings5;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 ABI::Windows::UI::ViewManagement::IUISettings6
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettings6;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs ABI::Windows::UI::ViewManagement::IUISettingsAnimationsEnabledChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettingsAnimationsEnabledChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs ABI::Windows::UI::ViewManagement::IUISettingsAutoHideScrollBarsChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettingsAutoHideScrollBarsChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs ABI::Windows::UI::ViewManagement::IUISettingsMessageDurationChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUISettingsMessageDurationChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings ABI::Windows::UI::ViewManagement::IUIViewSettings
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUIViewSettings;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics ABI::Windows::UI::ViewManagement::IUIViewSettingsStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IUIViewSettingsStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPane __x_ABI_CWindows_CUI_CViewManagement_CIInputPane;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane ABI::Windows::UI::ViewManagement::IInputPane
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPane;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 ABI::Windows::UI::ViewManagement::IInputPane2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPane2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl ABI::Windows::UI::ViewManagement::IInputPaneControl
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPaneControl;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs ABI::Windows::UI::ViewManagement::IInputPaneVisibilityEventArgs
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPaneVisibilityEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics ABI::Windows::UI::ViewManagement::IInputPaneStatics
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPaneStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 ABI::Windows::UI::ViewManagement::IInputPaneStatics2
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                interface IInputPaneStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IUISettings interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings, 0x85361600, 0x1c63, 0x4627, 0xbc,0xb1, 0x3a,0x89,0xe0,0xbc,0x9c,0x55);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("85361600-1c63-4627-bcb1-3a89e0bc9c55")
                IUISettings : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_HandPreference(
                        ABI::Windows::UI::ViewManagement::HandPreference *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_CursorSize(
                        ABI::Windows::Foundation::Size *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ScrollBarSize(
                        ABI::Windows::Foundation::Size *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ScrollBarArrowSize(
                        ABI::Windows::Foundation::Size *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ScrollBarThumbBoxSize(
                        ABI::Windows::Foundation::Size *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MessageDuration(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_AnimationsEnabled(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_CaretBrowsingEnabled(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_CaretBlinkRate(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_CaretWidth(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_DoubleClickTime(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MouseHoverTime(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE UIElementColor(
                        ABI::Windows::UI::ViewManagement::UIElementType element,
                        ABI::Windows::UI::Color *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings, 0x85361600, 0x1c63, 0x4627, 0xbc,0xb1, 0x3a,0x89,0xe0,0xbc,0x9c,0x55)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        TrustLevel *trustLevel);

    /*** IUISettings methods ***/
    HRESULT (STDMETHODCALLTYPE *get_HandPreference)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        __x_ABI_CWindows_CUI_CViewManagement_CHandPreference *value);

    HRESULT (STDMETHODCALLTYPE *get_CursorSize)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        __x_ABI_CWindows_CFoundation_CSize *value);

    HRESULT (STDMETHODCALLTYPE *get_ScrollBarSize)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        __x_ABI_CWindows_CFoundation_CSize *value);

    HRESULT (STDMETHODCALLTYPE *get_ScrollBarArrowSize)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        __x_ABI_CWindows_CFoundation_CSize *value);

    HRESULT (STDMETHODCALLTYPE *get_ScrollBarThumbBoxSize)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        __x_ABI_CWindows_CFoundation_CSize *value);

    HRESULT (STDMETHODCALLTYPE *get_MessageDuration)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_AnimationsEnabled)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_CaretBrowsingEnabled)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_CaretBlinkRate)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_CaretWidth)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_DoubleClickTime)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_MouseHoverTime)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *UIElementColor)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *This,
        __x_ABI_CWindows_CUI_CViewManagement_CUIElementType element,
        __x_ABI_CWindows_CUI_CColor *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUISettings methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_HandPreference(This,value) (This)->lpVtbl->get_HandPreference(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CursorSize(This,value) (This)->lpVtbl->get_CursorSize(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_ScrollBarSize(This,value) (This)->lpVtbl->get_ScrollBarSize(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_ScrollBarArrowSize(This,value) (This)->lpVtbl->get_ScrollBarArrowSize(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_ScrollBarThumbBoxSize(This,value) (This)->lpVtbl->get_ScrollBarThumbBoxSize(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_MessageDuration(This,value) (This)->lpVtbl->get_MessageDuration(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_AnimationsEnabled(This,value) (This)->lpVtbl->get_AnimationsEnabled(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CaretBrowsingEnabled(This,value) (This)->lpVtbl->get_CaretBrowsingEnabled(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CaretBlinkRate(This,value) (This)->lpVtbl->get_CaretBlinkRate(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CaretWidth(This,value) (This)->lpVtbl->get_CaretWidth(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_DoubleClickTime(This,value) (This)->lpVtbl->get_DoubleClickTime(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_MouseHoverTime(This,value) (This)->lpVtbl->get_MouseHoverTime(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_UIElementColor(This,element,value) (This)->lpVtbl->UIElementColor(This,element,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_Release(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUISettings methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_HandPreference(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,__x_ABI_CWindows_CUI_CViewManagement_CHandPreference *value) {
    return This->lpVtbl->get_HandPreference(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CursorSize(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,__x_ABI_CWindows_CFoundation_CSize *value) {
    return This->lpVtbl->get_CursorSize(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_ScrollBarSize(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,__x_ABI_CWindows_CFoundation_CSize *value) {
    return This->lpVtbl->get_ScrollBarSize(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_ScrollBarArrowSize(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,__x_ABI_CWindows_CFoundation_CSize *value) {
    return This->lpVtbl->get_ScrollBarArrowSize(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_ScrollBarThumbBoxSize(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,__x_ABI_CWindows_CFoundation_CSize *value) {
    return This->lpVtbl->get_ScrollBarThumbBoxSize(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_MessageDuration(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,UINT32 *value) {
    return This->lpVtbl->get_MessageDuration(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_AnimationsEnabled(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,boolean *value) {
    return This->lpVtbl->get_AnimationsEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CaretBrowsingEnabled(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,boolean *value) {
    return This->lpVtbl->get_CaretBrowsingEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CaretBlinkRate(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,UINT32 *value) {
    return This->lpVtbl->get_CaretBlinkRate(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CaretWidth(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,UINT32 *value) {
    return This->lpVtbl->get_CaretWidth(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_DoubleClickTime(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,UINT32 *value) {
    return This->lpVtbl->get_DoubleClickTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_MouseHoverTime(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,UINT32 *value) {
    return This->lpVtbl->get_MouseHoverTime(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_UIElementColor(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings* This,__x_ABI_CWindows_CUI_CViewManagement_CUIElementType element,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->UIElementColor(This,element,value);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IUISettings IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings
#define IUISettingsVtbl __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsVtbl
#define IUISettings __x_ABI_CWindows_CUI_CViewManagement_CIUISettings
#define IUISettings_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_QueryInterface
#define IUISettings_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_AddRef
#define IUISettings_Release __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_Release
#define IUISettings_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_GetIids
#define IUISettings_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_GetRuntimeClassName
#define IUISettings_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_GetTrustLevel
#define IUISettings_get_HandPreference __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_HandPreference
#define IUISettings_get_CursorSize __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CursorSize
#define IUISettings_get_ScrollBarSize __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_ScrollBarSize
#define IUISettings_get_ScrollBarArrowSize __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_ScrollBarArrowSize
#define IUISettings_get_ScrollBarThumbBoxSize __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_ScrollBarThumbBoxSize
#define IUISettings_get_MessageDuration __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_MessageDuration
#define IUISettings_get_AnimationsEnabled __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_AnimationsEnabled
#define IUISettings_get_CaretBrowsingEnabled __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CaretBrowsingEnabled
#define IUISettings_get_CaretBlinkRate __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CaretBlinkRate
#define IUISettings_get_CaretWidth __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_CaretWidth
#define IUISettings_get_DoubleClickTime __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_DoubleClickTime
#define IUISettings_get_MouseHoverTime __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_get_MouseHoverTime
#define IUISettings_UIElementColor __x_ABI_CWindows_CUI_CViewManagement_CIUISettings_UIElementColor
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IUISettings2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings2, 0xbad82401, 0x2721, 0x44f9, 0xbb,0x91, 0x2b,0xb2,0x28,0xbe,0x44,0x2f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("bad82401-2721-44f9-bb91-2bb228be442f")
                IUISettings2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_TextScaleFactor(
                        DOUBLE *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_TextScaleFactorChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,IInspectable* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_TextScaleFactorChanged(
                        EventRegistrationToken cookie) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings2, 0xbad82401, 0x2721, 0x44f9, 0xbb,0x91, 0x2b,0xb2,0x28,0xbe,0x44,0x2f)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 *This,
        TrustLevel *trustLevel);

    /*** IUISettings2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_TextScaleFactor)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 *This,
        DOUBLE *value);

    HRESULT (STDMETHODCALLTYPE *add_TextScaleFactorChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_TextScaleFactorChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 *This,
        EventRegistrationToken cookie);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2Vtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2 {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUISettings2 methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_get_TextScaleFactor(This,value) (This)->lpVtbl->get_TextScaleFactor(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_add_TextScaleFactorChanged(This,handler,cookie) (This)->lpVtbl->add_TextScaleFactorChanged(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_remove_TextScaleFactorChanged(This,cookie) (This)->lpVtbl->remove_TextScaleFactorChanged(This,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_Release(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUISettings2 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_get_TextScaleFactor(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings2* This,DOUBLE *value) {
    return This->lpVtbl->get_TextScaleFactor(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_add_TextScaleFactorChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings2* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_TextScaleFactorChanged(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_remove_TextScaleFactorChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings2* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_TextScaleFactorChanged(This,cookie);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IUISettings2 IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings2
#define IUISettings2Vtbl __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2Vtbl
#define IUISettings2 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2
#define IUISettings2_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_QueryInterface
#define IUISettings2_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_AddRef
#define IUISettings2_Release __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_Release
#define IUISettings2_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_GetIids
#define IUISettings2_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_GetRuntimeClassName
#define IUISettings2_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_GetTrustLevel
#define IUISettings2_get_TextScaleFactor __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_get_TextScaleFactor
#define IUISettings2_add_TextScaleFactorChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_add_TextScaleFactorChanged
#define IUISettings2_remove_TextScaleFactorChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_remove_TextScaleFactorChanged
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IUISettings3 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings3, 0x03021be4, 0x5254, 0x4781, 0x81,0x94, 0x51,0x68,0xf7,0xd0,0x6d,0x7b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("03021be4-**************-5168f7d06d7b")
                IUISettings3 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetColorValue(
                        ABI::Windows::UI::ViewManagement::UIColorType color,
                        ABI::Windows::UI::Color *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_ColorValuesChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,IInspectable* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_ColorValuesChanged(
                        EventRegistrationToken cookie) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings3, 0x03021be4, 0x5254, 0x4781, 0x81,0x94, 0x51,0x68,0xf7,0xd0,0x6d,0x7b)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 *This,
        TrustLevel *trustLevel);

    /*** IUISettings3 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetColorValue)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 *This,
        __x_ABI_CWindows_CUI_CViewManagement_CUIColorType color,
        __x_ABI_CWindows_CUI_CColor *value);

    HRESULT (STDMETHODCALLTYPE *add_ColorValuesChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_ColorValuesChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 *This,
        EventRegistrationToken cookie);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3Vtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3 {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUISettings3 methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetColorValue(This,color,value) (This)->lpVtbl->GetColorValue(This,color,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_add_ColorValuesChanged(This,handler,cookie) (This)->lpVtbl->add_ColorValuesChanged(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_remove_ColorValuesChanged(This,cookie) (This)->lpVtbl->remove_ColorValuesChanged(This,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_Release(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings3* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings3* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings3* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings3* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUISettings3 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetColorValue(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings3* This,__x_ABI_CWindows_CUI_CViewManagement_CUIColorType color,__x_ABI_CWindows_CUI_CColor *value) {
    return This->lpVtbl->GetColorValue(This,color,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_add_ColorValuesChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings3* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_ColorValuesChanged(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_remove_ColorValuesChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings3* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_ColorValuesChanged(This,cookie);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IUISettings3 IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings3
#define IUISettings3Vtbl __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3Vtbl
#define IUISettings3 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3
#define IUISettings3_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_QueryInterface
#define IUISettings3_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_AddRef
#define IUISettings3_Release __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_Release
#define IUISettings3_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetIids
#define IUISettings3_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetRuntimeClassName
#define IUISettings3_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetTrustLevel
#define IUISettings3_GetColorValue __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_GetColorValue
#define IUISettings3_add_ColorValuesChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_add_ColorValuesChanged
#define IUISettings3_remove_ColorValuesChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_remove_ColorValuesChanged
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings3_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IUISettings4 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings4, 0x52bb3002, 0x919b, 0x4d6b, 0x9b,0x78, 0x8d,0xd6,0x6f,0xf4,0xb9,0x3b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("52bb3002-919b-4d6b-9b78-8dd66ff4b93b")
                IUISettings4 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AdvancedEffectsEnabled(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_AdvancedEffectsEnabledChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,IInspectable* > *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_AdvancedEffectsEnabledChanged(
                        EventRegistrationToken cookie) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings4, 0x52bb3002, 0x919b, 0x4d6b, 0x9b,0x78, 0x8d,0xd6,0x6f,0xf4,0xb9,0x3b)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 *This,
        TrustLevel *trustLevel);

    /*** IUISettings4 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AdvancedEffectsEnabled)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *add_AdvancedEffectsEnabledChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_AdvancedEffectsEnabledChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 *This,
        EventRegistrationToken cookie);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4Vtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4 {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUISettings4 methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_get_AdvancedEffectsEnabled(This,value) (This)->lpVtbl->get_AdvancedEffectsEnabled(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_add_AdvancedEffectsEnabledChanged(This,handler,cookie) (This)->lpVtbl->add_AdvancedEffectsEnabledChanged(This,handler,cookie)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_remove_AdvancedEffectsEnabledChanged(This,cookie) (This)->lpVtbl->remove_AdvancedEffectsEnabledChanged(This,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings4* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings4* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_Release(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings4* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings4* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings4* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings4* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUISettings4 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_get_AdvancedEffectsEnabled(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings4* This,boolean *value) {
    return This->lpVtbl->get_AdvancedEffectsEnabled(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_add_AdvancedEffectsEnabledChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings4* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_AdvancedEffectsEnabledChanged(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_remove_AdvancedEffectsEnabledChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings4* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_AdvancedEffectsEnabledChanged(This,cookie);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IUISettings4 IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings4
#define IUISettings4Vtbl __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4Vtbl
#define IUISettings4 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4
#define IUISettings4_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_QueryInterface
#define IUISettings4_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_AddRef
#define IUISettings4_Release __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_Release
#define IUISettings4_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_GetIids
#define IUISettings4_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_GetRuntimeClassName
#define IUISettings4_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_GetTrustLevel
#define IUISettings4_get_AdvancedEffectsEnabled __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_get_AdvancedEffectsEnabled
#define IUISettings4_add_AdvancedEffectsEnabledChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_add_AdvancedEffectsEnabledChanged
#define IUISettings4_remove_AdvancedEffectsEnabledChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_remove_AdvancedEffectsEnabledChanged
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings4_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x40000 */

/*****************************************************************************
 * IUISettings5 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings5, 0x5349d588, 0x0cb5, 0x5f05, 0xbd,0x34, 0x70,0x6b,0x32,0x31,0xf0,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("5349d588-0cb5-5f05-bd34-706b3231f0bd")
                IUISettings5 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AutoHideScrollBars(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_AutoHideScrollBarsChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAutoHideScrollBarsChangedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_AutoHideScrollBarsChanged(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings5, 0x5349d588, 0x0cb5, 0x5f05, 0xbd,0x34, 0x70,0x6b,0x32,0x31,0xf0,0xbd)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 *This,
        TrustLevel *trustLevel);

    /*** IUISettings5 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AutoHideScrollBars)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *add_AutoHideScrollBarsChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_AutoHideScrollBarsChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5Vtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5 {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUISettings5 methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_get_AutoHideScrollBars(This,value) (This)->lpVtbl->get_AutoHideScrollBars(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_add_AutoHideScrollBarsChanged(This,handler,token) (This)->lpVtbl->add_AutoHideScrollBarsChanged(This,handler,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_remove_AutoHideScrollBarsChanged(This,token) (This)->lpVtbl->remove_AutoHideScrollBarsChanged(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings5* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings5* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_Release(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings5* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings5* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings5* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings5* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUISettings5 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_get_AutoHideScrollBars(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings5* This,boolean *value) {
    return This->lpVtbl->get_AutoHideScrollBars(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_add_AutoHideScrollBarsChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings5* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_AutoHideScrollBarsChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_remove_AutoHideScrollBarsChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings5* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_AutoHideScrollBarsChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IUISettings5 IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings5
#define IUISettings5Vtbl __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5Vtbl
#define IUISettings5 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5
#define IUISettings5_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_QueryInterface
#define IUISettings5_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_AddRef
#define IUISettings5_Release __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_Release
#define IUISettings5_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_GetIids
#define IUISettings5_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_GetRuntimeClassName
#define IUISettings5_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_GetTrustLevel
#define IUISettings5_get_AutoHideScrollBars __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_get_AutoHideScrollBars
#define IUISettings5_add_AutoHideScrollBarsChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_add_AutoHideScrollBarsChanged
#define IUISettings5_remove_AutoHideScrollBarsChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_remove_AutoHideScrollBarsChanged
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings5_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*****************************************************************************
 * IUISettings6 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings6, 0xaef19bd7, 0xfe31, 0x5a04, 0xad,0xa4, 0x46,0x9a,0xae,0xc6,0xdf,0xa9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("aef19bd7-fe31-5a04-ada4-469aaec6dfa9")
                IUISettings6 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_AnimationsEnabledChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAnimationsEnabledChangedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_AnimationsEnabledChanged(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_MessageDurationChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsMessageDurationChangedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_MessageDurationChanged(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings6, 0xaef19bd7, 0xfe31, 0x5a04, 0xad,0xa4, 0x46,0x9a,0xae,0xc6,0xdf,0xa9)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 *This,
        TrustLevel *trustLevel);

    /*** IUISettings6 methods ***/
    HRESULT (STDMETHODCALLTYPE *add_AnimationsEnabledChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_AnimationsEnabledChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_MessageDurationChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_MessageDurationChanged)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6Vtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6 {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUISettings6 methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_add_AnimationsEnabledChanged(This,handler,token) (This)->lpVtbl->add_AnimationsEnabledChanged(This,handler,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_remove_AnimationsEnabledChanged(This,token) (This)->lpVtbl->remove_AnimationsEnabledChanged(This,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_add_MessageDurationChanged(This,handler,token) (This)->lpVtbl->add_MessageDurationChanged(This,handler,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_remove_MessageDurationChanged(This,token) (This)->lpVtbl->remove_MessageDurationChanged(This,token)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings6* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings6* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_Release(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings6* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings6* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings6* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings6* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUISettings6 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_add_AnimationsEnabledChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings6* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_AnimationsEnabledChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_remove_AnimationsEnabledChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings6* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_AnimationsEnabledChanged(This,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_add_MessageDurationChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings6* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_MessageDurationChanged(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_remove_MessageDurationChanged(__x_ABI_CWindows_CUI_CViewManagement_CIUISettings6* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_MessageDurationChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IUISettings6 IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettings6
#define IUISettings6Vtbl __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6Vtbl
#define IUISettings6 __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6
#define IUISettings6_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_QueryInterface
#define IUISettings6_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_AddRef
#define IUISettings6_Release __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_Release
#define IUISettings6_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_GetIids
#define IUISettings6_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_GetRuntimeClassName
#define IUISettings6_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_GetTrustLevel
#define IUISettings6_add_AnimationsEnabledChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_add_AnimationsEnabledChanged
#define IUISettings6_remove_AnimationsEnabledChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_remove_AnimationsEnabledChanged
#define IUISettings6_add_MessageDurationChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_add_MessageDurationChanged
#define IUISettings6_remove_MessageDurationChanged __x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_remove_MessageDurationChanged
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIUISettings6_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * IUISettingsAnimationsEnabledChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs, 0x0c7b4b3d, 0x2ea1, 0x533e, 0x89,0x4d, 0x41,0x5b,0xc5,0x24,0x3c,0x29);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("0c7b4b3d-2ea1-533e-894d-415bc5243c29")
                IUISettingsAnimationsEnabledChangedEventArgs : public IInspectable
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs, 0x0c7b4b3d, 0x2ea1, 0x533e, 0x89,0x4d, 0x41,0x5b,0xc5,0x24,0x3c,0x29)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_Release(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IUISettingsAnimationsEnabledChangedEventArgs IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs
#define IUISettingsAnimationsEnabledChangedEventArgsVtbl __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgsVtbl
#define IUISettingsAnimationsEnabledChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs
#define IUISettingsAnimationsEnabledChangedEventArgs_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_QueryInterface
#define IUISettingsAnimationsEnabledChangedEventArgs_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_AddRef
#define IUISettingsAnimationsEnabledChangedEventArgs_Release __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_Release
#define IUISettingsAnimationsEnabledChangedEventArgs_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_GetIids
#define IUISettingsAnimationsEnabledChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_GetRuntimeClassName
#define IUISettingsAnimationsEnabledChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_GetTrustLevel
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * IUISettingsAutoHideScrollBarsChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs, 0x87afd4b2, 0x9146, 0x5f02, 0x8f,0x6b, 0x06,0xd4,0x54,0x17,0x4c,0x0f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("87afd4b2-9146-5f02-8f6b-06d454174c0f")
                IUISettingsAutoHideScrollBarsChangedEventArgs : public IInspectable
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs, 0x87afd4b2, 0x9146, 0x5f02, 0x8f,0x6b, 0x06,0xd4,0x54,0x17,0x4c,0x0f)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_Release(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IUISettingsAutoHideScrollBarsChangedEventArgs IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs
#define IUISettingsAutoHideScrollBarsChangedEventArgsVtbl __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgsVtbl
#define IUISettingsAutoHideScrollBarsChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs
#define IUISettingsAutoHideScrollBarsChangedEventArgs_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_QueryInterface
#define IUISettingsAutoHideScrollBarsChangedEventArgs_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_AddRef
#define IUISettingsAutoHideScrollBarsChangedEventArgs_Release __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_Release
#define IUISettingsAutoHideScrollBarsChangedEventArgs_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_GetIids
#define IUISettingsAutoHideScrollBarsChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_GetRuntimeClassName
#define IUISettingsAutoHideScrollBarsChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_GetTrustLevel
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*****************************************************************************
 * IUISettingsMessageDurationChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs, 0x338aad52, 0x4a5d, 0x5b59, 0x80,0x02, 0xd9,0x30,0xf6,0x08,0xfd,0x6e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("338aad52-4a5d-5b59-8002-d930f608fd6e")
                IUISettingsMessageDurationChangedEventArgs : public IInspectable
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs, 0x338aad52, 0x4a5d, 0x5b59, 0x80,0x02, 0xd9,0x30,0xf6,0x08,0xfd,0x6e)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs *This,
        TrustLevel *trustLevel);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_Release(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IUISettingsMessageDurationChangedEventArgs IID___x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs
#define IUISettingsMessageDurationChangedEventArgsVtbl __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgsVtbl
#define IUISettingsMessageDurationChangedEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs
#define IUISettingsMessageDurationChangedEventArgs_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_QueryInterface
#define IUISettingsMessageDurationChangedEventArgs_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_AddRef
#define IUISettingsMessageDurationChangedEventArgs_Release __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_Release
#define IUISettingsMessageDurationChangedEventArgs_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_GetIids
#define IUISettingsMessageDurationChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_GetRuntimeClassName
#define IUISettingsMessageDurationChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_GetTrustLevel
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*****************************************************************************
 * IUIViewSettings interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings, 0xc63657f6, 0x8850, 0x470d, 0x88,0xf8, 0x45,0x5e,0x16,0xea,0x2c,0x26);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("c63657f6-8850-470d-88f8-455e16ea2c26")
                IUIViewSettings : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_UserInteractionMode(
                        ABI::Windows::UI::ViewManagement::UserInteractionMode *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings, 0xc63657f6, 0x8850, 0x470d, 0x88,0xf8, 0x45,0x5e,0x16,0xea,0x2c,0x26)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings *This,
        TrustLevel *trustLevel);

    /*** IUIViewSettings methods ***/
    HRESULT (STDMETHODCALLTYPE *get_UserInteractionMode)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings *This,
        __x_ABI_CWindows_CUI_CViewManagement_CUserInteractionMode *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUIViewSettings methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_get_UserInteractionMode(This,value) (This)->lpVtbl->get_UserInteractionMode(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_Release(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUIViewSettings methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_get_UserInteractionMode(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings* This,__x_ABI_CWindows_CUI_CViewManagement_CUserInteractionMode *value) {
    return This->lpVtbl->get_UserInteractionMode(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IUIViewSettings IID___x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings
#define IUIViewSettingsVtbl __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsVtbl
#define IUIViewSettings __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings
#define IUIViewSettings_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_QueryInterface
#define IUIViewSettings_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_AddRef
#define IUIViewSettings_Release __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_Release
#define IUIViewSettings_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_GetIids
#define IUIViewSettings_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_GetRuntimeClassName
#define IUIViewSettings_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_GetTrustLevel
#define IUIViewSettings_get_UserInteractionMode __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_get_UserInteractionMode
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IUIViewSettingsStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics, 0x595c97a5, 0xf8f6, 0x41cf, 0xb0,0xfb, 0xaa,0xcd,0xb8,0x1f,0xd5,0xf6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("595c97a5-f8f6-41cf-b0fb-aacdb81fd5f6")
                IUIViewSettingsStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetForCurrentView(
                        ABI::Windows::UI::ViewManagement::IUIViewSettings **current) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics, 0x595c97a5, 0xf8f6, 0x41cf, 0xb0,0xfb, 0xaa,0xcd,0xb8,0x1f,0xd5,0xf6)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics *This,
        TrustLevel *trustLevel);

    /*** IUIViewSettingsStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetForCurrentView)(
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics *This,
        __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings **current);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStaticsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IUIViewSettingsStatics methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetForCurrentView(This,current) (This)->lpVtbl->GetForCurrentView(This,current)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_Release(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IUIViewSettingsStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetForCurrentView(__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics* This,__x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettings **current) {
    return This->lpVtbl->GetForCurrentView(This,current);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IUIViewSettingsStatics IID___x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics
#define IUIViewSettingsStaticsVtbl __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStaticsVtbl
#define IUIViewSettingsStatics __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics
#define IUIViewSettingsStatics_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_QueryInterface
#define IUIViewSettingsStatics_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_AddRef
#define IUIViewSettingsStatics_Release __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_Release
#define IUIViewSettingsStatics_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetIids
#define IUIViewSettingsStatics_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetRuntimeClassName
#define IUIViewSettingsStatics_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetTrustLevel
#define IUIViewSettingsStatics_GetForCurrentView __x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_GetForCurrentView
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIUIViewSettingsStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IInputPane interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPane, 0x640ada70, 0x06f3, 0x4c87, 0xa6,0x78, 0x98,0x29,0xc9,0x12,0x7c,0x28);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("640ada70-06f3-4c87-a678-9829c9127c28")
                IInputPane : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE add_Showing(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::InputPane*,ABI::Windows::UI::ViewManagement::InputPaneVisibilityEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Showing(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Hiding(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::UI::ViewManagement::InputPane*,ABI::Windows::UI::ViewManagement::InputPaneVisibilityEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Hiding(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_OccludedRect(
                        ABI::Windows::Foundation::Rect *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane, 0x640ada70, 0x06f3, 0x4c87, 0xa6,0x78, 0x98,0x29,0xc9,0x12,0x7c,0x28)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *This,
        TrustLevel *trustLevel);

    /*** IInputPane methods ***/
    HRESULT (STDMETHODCALLTYPE *add_Showing)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Showing)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_Hiding)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *This,
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Hiding)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *get_OccludedRect)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPane {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IInputPane methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_add_Showing(This,handler,token) (This)->lpVtbl->add_Showing(This,handler,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_remove_Showing(This,token) (This)->lpVtbl->remove_Showing(This,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_add_Hiding(This,handler,token) (This)->lpVtbl->add_Hiding(This,handler,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_remove_Hiding(This,token) (This)->lpVtbl->remove_Hiding(This,token)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_get_OccludedRect(This,value) (This)->lpVtbl->get_OccludedRect(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_Release(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IInputPane methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_add_Showing(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Showing(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_remove_Showing(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Showing(This,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_add_Hiding(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane* This,__FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_Hiding(This,handler,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_remove_Hiding(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Hiding(This,token);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_get_OccludedRect(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_OccludedRect(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IInputPane IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPane
#define IInputPaneVtbl __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVtbl
#define IInputPane __x_ABI_CWindows_CUI_CViewManagement_CIInputPane
#define IInputPane_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_QueryInterface
#define IInputPane_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_AddRef
#define IInputPane_Release __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_Release
#define IInputPane_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_GetIids
#define IInputPane_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_GetRuntimeClassName
#define IInputPane_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_GetTrustLevel
#define IInputPane_add_Showing __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_add_Showing
#define IInputPane_remove_Showing __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_remove_Showing
#define IInputPane_add_Hiding __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_add_Hiding
#define IInputPane_remove_Hiding __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_remove_Hiding
#define IInputPane_get_OccludedRect __x_ABI_CWindows_CUI_CViewManagement_CIInputPane_get_OccludedRect
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IInputPane2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPane2, 0x8a6b3f26, 0x7090, 0x4793, 0x94,0x4c, 0xc3,0xf2,0xcd,0xe2,0x62,0x76);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("8a6b3f26-7090-4793-944c-c3f2cde26276")
                IInputPane2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE TryShow(
                        boolean *result) = 0;

                    virtual HRESULT STDMETHODCALLTYPE TryHide(
                        boolean *result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane2, 0x8a6b3f26, 0x7090, 0x4793, 0x94,0x4c, 0xc3,0xf2,0xcd,0xe2,0x62,0x76)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 *This,
        TrustLevel *trustLevel);

    /*** IInputPane2 methods ***/
    HRESULT (STDMETHODCALLTYPE *TryShow)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 *This,
        boolean *result);

    HRESULT (STDMETHODCALLTYPE *TryHide)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 *This,
        boolean *result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2Vtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2 {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IInputPane2 methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_TryShow(This,result) (This)->lpVtbl->TryShow(This,result)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_TryHide(This,result) (This)->lpVtbl->TryHide(This,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_Release(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IInputPane2 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_TryShow(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane2* This,boolean *result) {
    return This->lpVtbl->TryShow(This,result);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_TryHide(__x_ABI_CWindows_CUI_CViewManagement_CIInputPane2* This,boolean *result) {
    return This->lpVtbl->TryHide(This,result);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IInputPane2 IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPane2
#define IInputPane2Vtbl __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2Vtbl
#define IInputPane2 __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2
#define IInputPane2_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_QueryInterface
#define IInputPane2_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_AddRef
#define IInputPane2_Release __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_Release
#define IInputPane2_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_GetIids
#define IInputPane2_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_GetRuntimeClassName
#define IInputPane2_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_GetTrustLevel
#define IInputPane2_TryShow __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_TryShow
#define IInputPane2_TryHide __x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_TryHide
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIInputPane2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IInputPaneControl interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl, 0x088bb24f, 0x962f, 0x489d, 0xaa,0x6e, 0xc6,0xbe,0x1a,0x0a,0x6e,0x52);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("088bb24f-962f-489d-aa6e-c6be1a0a6e52")
                IInputPaneControl : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Visible(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_Visible(
                        boolean value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl, 0x088bb24f, 0x962f, 0x489d, 0xaa,0x6e, 0xc6,0xbe,0x1a,0x0a,0x6e,0x52)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControlVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl *This,
        TrustLevel *trustLevel);

    /*** IInputPaneControl methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Visible)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_Visible)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl *This,
        boolean value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControlVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControlVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IInputPaneControl methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_get_Visible(This,value) (This)->lpVtbl->get_Visible(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_put_Visible(This,value) (This)->lpVtbl->put_Visible(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_Release(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IInputPaneControl methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_get_Visible(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl* This,boolean *value) {
    return This->lpVtbl->get_Visible(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_put_Visible(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl* This,boolean value) {
    return This->lpVtbl->put_Visible(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IInputPaneControl IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl
#define IInputPaneControlVtbl __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControlVtbl
#define IInputPaneControl __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl
#define IInputPaneControl_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_QueryInterface
#define IInputPaneControl_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_AddRef
#define IInputPaneControl_Release __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_Release
#define IInputPaneControl_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_GetIids
#define IInputPaneControl_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_GetRuntimeClassName
#define IInputPaneControl_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_GetTrustLevel
#define IInputPaneControl_get_Visible __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_get_Visible
#define IInputPaneControl_put_Visible __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_put_Visible
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneControl_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IInputPaneVisibilityEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs, 0xd243e016, 0xd907, 0x4fcc, 0xbb,0x8d, 0xf7,0x7b,0xaa,0x50,0x28,0xf1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("d243e016-d907-4fcc-bb8d-f77baa5028f1")
                IInputPaneVisibilityEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_OccludedRect(
                        ABI::Windows::Foundation::Rect *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_EnsuredFocusedElementInView(
                        boolean value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_EnsuredFocusedElementInView(
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs, 0xd243e016, 0xd907, 0x4fcc, 0xbb,0x8d, 0xf7,0x7b,0xaa,0x50,0x28,0xf1)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs *This,
        TrustLevel *trustLevel);

    /*** IInputPaneVisibilityEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_OccludedRect)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs *This,
        __x_ABI_CWindows_CFoundation_CRect *value);

    HRESULT (STDMETHODCALLTYPE *put_EnsuredFocusedElementInView)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_EnsuredFocusedElementInView)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IInputPaneVisibilityEventArgs methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_get_OccludedRect(This,value) (This)->lpVtbl->get_OccludedRect(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_put_EnsuredFocusedElementInView(This,value) (This)->lpVtbl->put_EnsuredFocusedElementInView(This,value)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_get_EnsuredFocusedElementInView(This,value) (This)->lpVtbl->get_EnsuredFocusedElementInView(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_Release(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IInputPaneVisibilityEventArgs methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_get_OccludedRect(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs* This,__x_ABI_CWindows_CFoundation_CRect *value) {
    return This->lpVtbl->get_OccludedRect(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_put_EnsuredFocusedElementInView(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs* This,boolean value) {
    return This->lpVtbl->put_EnsuredFocusedElementInView(This,value);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_get_EnsuredFocusedElementInView(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs* This,boolean *value) {
    return This->lpVtbl->get_EnsuredFocusedElementInView(This,value);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IInputPaneVisibilityEventArgs IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs
#define IInputPaneVisibilityEventArgsVtbl __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgsVtbl
#define IInputPaneVisibilityEventArgs __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs
#define IInputPaneVisibilityEventArgs_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_QueryInterface
#define IInputPaneVisibilityEventArgs_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_AddRef
#define IInputPaneVisibilityEventArgs_Release __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_Release
#define IInputPaneVisibilityEventArgs_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_GetIids
#define IInputPaneVisibilityEventArgs_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_GetRuntimeClassName
#define IInputPaneVisibilityEventArgs_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_GetTrustLevel
#define IInputPaneVisibilityEventArgs_get_OccludedRect __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_get_OccludedRect
#define IInputPaneVisibilityEventArgs_put_EnsuredFocusedElementInView __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_put_EnsuredFocusedElementInView
#define IInputPaneVisibilityEventArgs_get_EnsuredFocusedElementInView __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_get_EnsuredFocusedElementInView
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IInputPaneStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics, 0x95f4af3a, 0xef47, 0x424a, 0x97,0x41, 0xfd,0x28,0x15,0xeb,0xa2,0xbd);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("95f4af3a-ef47-424a-9741-fd2815eba2bd")
                IInputPaneStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetForCurrentView(
                        ABI::Windows::UI::ViewManagement::IInputPane **input_pane) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics, 0x95f4af3a, 0xef47, 0x424a, 0x97,0x41, 0xfd,0x28,0x15,0xeb,0xa2,0xbd)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics *This,
        TrustLevel *trustLevel);

    /*** IInputPaneStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetForCurrentView)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics *This,
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane **input_pane);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStaticsVtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IInputPaneStatics methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetForCurrentView(This,input_pane) (This)->lpVtbl->GetForCurrentView(This,input_pane)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_Release(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IInputPaneStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetForCurrentView(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics* This,__x_ABI_CWindows_CUI_CViewManagement_CIInputPane **input_pane) {
    return This->lpVtbl->GetForCurrentView(This,input_pane);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IInputPaneStatics IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics
#define IInputPaneStaticsVtbl __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStaticsVtbl
#define IInputPaneStatics __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics
#define IInputPaneStatics_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_QueryInterface
#define IInputPaneStatics_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_AddRef
#define IInputPaneStatics_Release __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_Release
#define IInputPaneStatics_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetIids
#define IInputPaneStatics_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetRuntimeClassName
#define IInputPaneStatics_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetTrustLevel
#define IInputPaneStatics_GetForCurrentView __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_GetForCurrentView
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IInputPaneStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2, 0x1b63529b, 0xd9ec, 0x4531, 0x84,0x45, 0x71,0xba,0xb9,0xfb,0x82,0x8e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace UI {
            namespace ViewManagement {
                MIDL_INTERFACE("1b63529b-d9ec-4531-8445-71bab9fb828e")
                IInputPaneStatics2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetForUIContext(
                        ABI::Windows::UI::IUIContext *context,
                        ABI::Windows::UI::ViewManagement::IInputPane **result) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2, 0x1b63529b, 0xd9ec, 0x4531, 0x84,0x45, 0x71,0xba,0xb9,0xfb,0x82,0x8e)
#endif
#else
typedef struct __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 *This,
        TrustLevel *trustLevel);

    /*** IInputPaneStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetForUIContext)(
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 *This,
        __x_ABI_CWindows_CUI_CIUIContext *context,
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane **result);

    END_INTERFACE
} __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2Vtbl;

interface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2 {
    CONST_VTBL __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IInputPaneStatics2 methods ***/
#define __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetForUIContext(This,context,result) (This)->lpVtbl->GetForUIContext(This,context,result)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_QueryInterface(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_AddRef(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_Release(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetIids(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetRuntimeClassName(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetTrustLevel(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IInputPaneStatics2 methods ***/
static inline HRESULT __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetForUIContext(__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2* This,__x_ABI_CWindows_CUI_CIUIContext *context,__x_ABI_CWindows_CUI_CViewManagement_CIInputPane **result) {
    return This->lpVtbl->GetForUIContext(This,context,result);
}
#endif
#ifdef WIDL_using_Windows_UI_ViewManagement
#define IID_IInputPaneStatics2 IID___x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2
#define IInputPaneStatics2Vtbl __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2Vtbl
#define IInputPaneStatics2 __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2
#define IInputPaneStatics2_QueryInterface __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_QueryInterface
#define IInputPaneStatics2_AddRef __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_AddRef
#define IInputPaneStatics2_Release __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_Release
#define IInputPaneStatics2_GetIids __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetIids
#define IInputPaneStatics2_GetRuntimeClassName __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetRuntimeClassName
#define IInputPaneStatics2_GetTrustLevel __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetTrustLevel
#define IInputPaneStatics2_GetForUIContext __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_GetForUIContext
#endif /* WIDL_using_Windows_UI_ViewManagement */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CUI_CViewManagement_CIInputPaneStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*
 * Class Windows.UI.ViewManagement.UISettings
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_UISettings_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_UISettings_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettings[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','U','I','S','e','t','t','i','n','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettings[] = L"Windows.UI.ViewManagement.UISettings";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettings[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','U','I','S','e','t','t','i','n','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_UISettings_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.ViewManagement.UISettingsAnimationsEnabledChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_UISettingsAnimationsEnabledChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_UISettingsAnimationsEnabledChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettingsAnimationsEnabledChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','U','I','S','e','t','t','i','n','g','s','A','n','i','m','a','t','i','o','n','s','E','n','a','b','l','e','d','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettingsAnimationsEnabledChangedEventArgs[] = L"Windows.UI.ViewManagement.UISettingsAnimationsEnabledChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettingsAnimationsEnabledChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','U','I','S','e','t','t','i','n','g','s','A','n','i','m','a','t','i','o','n','s','E','n','a','b','l','e','d','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_UISettingsAnimationsEnabledChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*
 * Class Windows.UI.ViewManagement.UISettingsAutoHideScrollBarsChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_UISettingsAutoHideScrollBarsChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_UISettingsAutoHideScrollBarsChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettingsAutoHideScrollBarsChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','U','I','S','e','t','t','i','n','g','s','A','u','t','o','H','i','d','e','S','c','r','o','l','l','B','a','r','s','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettingsAutoHideScrollBarsChangedEventArgs[] = L"Windows.UI.ViewManagement.UISettingsAutoHideScrollBarsChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettingsAutoHideScrollBarsChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','U','I','S','e','t','t','i','n','g','s','A','u','t','o','H','i','d','e','S','c','r','o','l','l','B','a','r','s','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_UISettingsAutoHideScrollBarsChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x80000 */

/*
 * Class Windows.UI.ViewManagement.UISettingsMessageDurationChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_UISettingsMessageDurationChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_UISettingsMessageDurationChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettingsMessageDurationChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','U','I','S','e','t','t','i','n','g','s','M','e','s','s','a','g','e','D','u','r','a','t','i','o','n','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettingsMessageDurationChangedEventArgs[] = L"Windows.UI.ViewManagement.UISettingsMessageDurationChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_UISettingsMessageDurationChangedEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','U','I','S','e','t','t','i','n','g','s','M','e','s','s','a','g','e','D','u','r','a','t','i','o','n','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_UISettingsMessageDurationChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0xa0000 */

/*
 * Class Windows.UI.ViewManagement.UIViewSettings
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_UIViewSettings_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_UIViewSettings_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_UIViewSettings[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','U','I','V','i','e','w','S','e','t','t','i','n','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_UIViewSettings[] = L"Windows.UI.ViewManagement.UIViewSettings";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_UIViewSettings[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','U','I','V','i','e','w','S','e','t','t','i','n','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_UIViewSettings_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.ViewManagement.InputPane
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_InputPane_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_InputPane_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_InputPane[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','I','n','p','u','t','P','a','n','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_InputPane[] = L"Windows.UI.ViewManagement.InputPane";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_InputPane[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','I','n','p','u','t','P','a','n','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_InputPane_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.UI.ViewManagement.InputPaneVisibilityEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_UI_ViewManagement_InputPaneVisibilityEventArgs_DEFINED
#define RUNTIMECLASS_Windows_UI_ViewManagement_InputPaneVisibilityEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_UI_ViewManagement_InputPaneVisibilityEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','I','n','p','u','t','P','a','n','e','V','i','s','i','b','i','l','i','t','y','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_InputPaneVisibilityEventArgs[] = L"Windows.UI.ViewManagement.InputPaneVisibilityEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_UI_ViewManagement_InputPaneVisibilityEventArgs[] = {'W','i','n','d','o','w','s','.','U','I','.','V','i','e','w','M','a','n','a','g','e','m','e','n','t','.','I','n','p','u','t','P','a','n','e','V','i','s','i','b','i','l','i','t','y','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_UI_ViewManagement_InputPaneVisibilityEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,IInspectable* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable, 0x2dbdba9d, 0x20da, 0x519d, 0x90,0x78, 0x09,0xf8,0x35,0xbc,0x5b,0xc7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("2dbdba9d-20da-519d-9078-09f835bc5bc7")
            ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,IInspectable* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::UISettings*, ABI::Windows::UI::ViewManagement::IUISettings* >, IInspectable* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable, 0x2dbdba9d, 0x20da, 0x519d, 0x90,0x78, 0x09,0xf8,0x35,0xbc,0x5b,0xc7)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectableVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable *This);

    /*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,IInspectable* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable *This,
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *sender,
        IInspectable *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectableVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectableVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,IInspectable* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_AddRef(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_Release(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,IInspectable* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_Invoke(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable* This,__x_ABI_CWindows_CUI_CViewManagement_CIUISettings *sender,IInspectable *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_UISettings_IInspectable IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable
#define ITypedEventHandler_UISettings_IInspectableVtbl __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectableVtbl
#define ITypedEventHandler_UISettings_IInspectable __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable
#define ITypedEventHandler_UISettings_IInspectable_QueryInterface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_QueryInterface
#define ITypedEventHandler_UISettings_IInspectable_AddRef __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_AddRef
#define ITypedEventHandler_UISettings_IInspectable_Release __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_Release
#define ITypedEventHandler_UISettings_IInspectable_Invoke __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_IInspectable_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAnimationsEnabledChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs, 0xdeff0f90, 0x59e1, 0x5859, 0xa2,0x45, 0x3d,0x27,0x50,0x81,0xc2,0xad);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("deff0f90-59e1-5859-a245-3d275081c2ad")
            ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAnimationsEnabledChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::UISettings*, ABI::Windows::UI::ViewManagement::IUISettings* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::UISettingsAnimationsEnabledChangedEventArgs*, ABI::Windows::UI::ViewManagement::IUISettingsAnimationsEnabledChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs, 0xdeff0f90, 0x59e1, 0x5859, 0xa2,0x45, 0x3d,0x27,0x50,0x81,0xc2,0xad)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAnimationsEnabledChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs *This,
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *sender,
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAnimationsEnabledChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAnimationsEnabledChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs* This,__x_ABI_CWindows_CUI_CViewManagement_CIUISettings *sender,__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAnimationsEnabledChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_UISettings_UISettingsAnimationsEnabledChangedEventArgs IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs
#define ITypedEventHandler_UISettings_UISettingsAnimationsEnabledChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgsVtbl
#define ITypedEventHandler_UISettings_UISettingsAnimationsEnabledChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs
#define ITypedEventHandler_UISettings_UISettingsAnimationsEnabledChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_QueryInterface
#define ITypedEventHandler_UISettings_UISettingsAnimationsEnabledChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_AddRef
#define ITypedEventHandler_UISettings_UISettingsAnimationsEnabledChangedEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_Release
#define ITypedEventHandler_UISettings_UISettingsAnimationsEnabledChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAnimationsEnabledChangedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAutoHideScrollBarsChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs, 0x808aef30, 0x2660, 0x51b0, 0x9c,0x11, 0xf7,0x5d,0xd4,0x20,0x06,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("808aef30-2660-51b0-9c11-f75dd42006b4")
            ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAutoHideScrollBarsChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::UISettings*, ABI::Windows::UI::ViewManagement::IUISettings* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::UISettingsAutoHideScrollBarsChangedEventArgs*, ABI::Windows::UI::ViewManagement::IUISettingsAutoHideScrollBarsChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs, 0x808aef30, 0x2660, 0x51b0, 0x9c,0x11, 0xf7,0x5d,0xd4,0x20,0x06,0xb4)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAutoHideScrollBarsChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs *This,
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *sender,
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAutoHideScrollBarsChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsAutoHideScrollBarsChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs* This,__x_ABI_CWindows_CUI_CViewManagement_CIUISettings *sender,__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsAutoHideScrollBarsChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_UISettings_UISettingsAutoHideScrollBarsChangedEventArgs IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs
#define ITypedEventHandler_UISettings_UISettingsAutoHideScrollBarsChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgsVtbl
#define ITypedEventHandler_UISettings_UISettingsAutoHideScrollBarsChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs
#define ITypedEventHandler_UISettings_UISettingsAutoHideScrollBarsChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_QueryInterface
#define ITypedEventHandler_UISettings_UISettingsAutoHideScrollBarsChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_AddRef
#define ITypedEventHandler_UISettings_UISettingsAutoHideScrollBarsChangedEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_Release
#define ITypedEventHandler_UISettings_UISettingsAutoHideScrollBarsChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsAutoHideScrollBarsChangedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsMessageDurationChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs, 0xec807b08, 0x1ac6, 0x5b37, 0x9a,0xf7, 0x1a,0xaf,0x1c,0x93,0x57,0x7e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("ec807b08-1ac6-5b37-9af7-1aaf1c93577e")
            ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsMessageDurationChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::UISettings*, ABI::Windows::UI::ViewManagement::IUISettings* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::UISettingsMessageDurationChangedEventArgs*, ABI::Windows::UI::ViewManagement::IUISettingsMessageDurationChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs, 0xec807b08, 0x1ac6, 0x5b37, 0x9a,0xf7, 0x1a,0xaf,0x1c,0x93,0x57,0x7e)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsMessageDurationChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs *This,
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettings *sender,
        __x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsMessageDurationChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::UISettings*,ABI::Windows::UI::ViewManagement::UISettingsMessageDurationChangedEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs* This,__x_ABI_CWindows_CUI_CViewManagement_CIUISettings *sender,__x_ABI_CWindows_CUI_CViewManagement_CIUISettingsMessageDurationChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_UISettings_UISettingsMessageDurationChangedEventArgs IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs
#define ITypedEventHandler_UISettings_UISettingsMessageDurationChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgsVtbl
#define ITypedEventHandler_UISettings_UISettingsMessageDurationChangedEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs
#define ITypedEventHandler_UISettings_UISettingsMessageDurationChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_QueryInterface
#define ITypedEventHandler_UISettings_UISettingsMessageDurationChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_AddRef
#define ITypedEventHandler_UISettings_UISettingsMessageDurationChangedEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_Release
#define ITypedEventHandler_UISettings_UISettingsMessageDurationChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CUISettings_Windows__CUI__CViewManagement__CUISettingsMessageDurationChangedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::UI::ViewManagement::InputPane*,ABI::Windows::UI::ViewManagement::InputPaneVisibilityEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs, 0xb813d684, 0xd953, 0x5a8a, 0x9b,0x30, 0x78,0xb7,0x9f,0xb9,0x14,0x7b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("b813d684-d953-5a8a-9b30-78b79fb9147b")
            ITypedEventHandler<ABI::Windows::UI::ViewManagement::InputPane*,ABI::Windows::UI::ViewManagement::InputPaneVisibilityEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::InputPane*, ABI::Windows::UI::ViewManagement::IInputPane* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::UI::ViewManagement::InputPaneVisibilityEventArgs*, ABI::Windows::UI::ViewManagement::IInputPaneVisibilityEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs, 0xb813d684, 0xd953, 0x5a8a, 0x9b,0x30, 0x78,0xb7,0x9f,0xb9,0x14,0x7b)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::InputPane*,ABI::Windows::UI::ViewManagement::InputPaneVisibilityEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs *This,
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPane *sender,
        __x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::InputPane*,ABI::Windows::UI::ViewManagement::InputPaneVisibilityEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_AddRef(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_Release(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::UI::ViewManagement::InputPane*,ABI::Windows::UI::ViewManagement::InputPaneVisibilityEventArgs* > methods ***/
static inline HRESULT __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_Invoke(__FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs* This,__x_ABI_CWindows_CUI_CViewManagement_CIInputPane *sender,__x_ABI_CWindows_CUI_CViewManagement_CIInputPaneVisibilityEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_InputPane_InputPaneVisibilityEventArgs IID___FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs
#define ITypedEventHandler_InputPane_InputPaneVisibilityEventArgsVtbl __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgsVtbl
#define ITypedEventHandler_InputPane_InputPaneVisibilityEventArgs __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs
#define ITypedEventHandler_InputPane_InputPaneVisibilityEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_QueryInterface
#define ITypedEventHandler_InputPane_InputPaneVisibilityEventArgs_AddRef __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_AddRef
#define ITypedEventHandler_InputPane_InputPaneVisibilityEventArgs_Release __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_Release
#define ITypedEventHandler_InputPane_InputPaneVisibilityEventArgs_Invoke __FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CUI__CViewManagement__CInputPane_Windows__CUI__CViewManagement__CInputPaneVisibilityEventArgs_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_ui_viewmanagement_h__ */
