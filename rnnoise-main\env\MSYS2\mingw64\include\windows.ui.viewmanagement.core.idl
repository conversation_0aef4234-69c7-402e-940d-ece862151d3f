/*
 * Copyright 2025 Z<PERSON><PERSON> for CodeWeavers
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

#ifndef DO_NO_IMPORTS
import "inspectable.idl";
import "asyncinfo.idl";
import "eventtoken.idl";
import "windowscontracts.idl";
import "windows.foundation.idl";
import "windows.ui.idl";
#endif

namespace Windows.UI.ViewManagement.Core
{
    typedef enum CoreInputViewKind CoreInputViewKind;
    typedef enum CoreInputViewOcclusionKind CoreInputViewOcclusionKind;
    typedef enum CoreInputViewXYFocusTransferDirection CoreInputViewXYFocusTransferDirection;

    interface ICoreInputView;
    interface ICoreInputView2;
    interface ICoreInputView3;
    interface ICoreInputView4;
    interface ICoreInputViewHidingEventArgs;
    interface ICoreInputViewOcclusion;
    interface ICoreInputViewOcclusionsChangedEventArgs;
    interface ICoreInputViewShowingEventArgs;
    interface ICoreInputViewStatics;
    interface ICoreInputViewStatics2;
    interface ICoreInputViewTransferringXYFocusEventArgs;

    runtimeclass CoreInputView;
    runtimeclass CoreInputViewHidingEventArgs;
    runtimeclass CoreInputViewOcclusion;
    runtimeclass CoreInputViewOcclusionsChangedEventArgs;
    runtimeclass CoreInputViewShowingEventArgs;
    runtimeclass CoreInputViewTransferringXYFocusEventArgs;

    declare
    {
        interface Windows.Foundation.Collections.IIterable<Windows.UI.ViewManagement.Core.CoreInputViewOcclusion *>;
        interface Windows.Foundation.Collections.IIterator<Windows.UI.ViewManagement.Core.CoreInputViewOcclusion *>;
        interface Windows.Foundation.Collections.IVectorView<Windows.UI.ViewManagement.Core.CoreInputViewOcclusion *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.Core.CoreInputView *, IInspectable *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.Core.CoreInputView *, Windows.UI.ViewManagement.Core.CoreInputViewHidingEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.Core.CoreInputView *, Windows.UI.ViewManagement.Core.CoreInputViewOcclusionsChangedEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.Core.CoreInputView *, Windows.UI.ViewManagement.Core.CoreInputViewShowingEventArgs *>;
        interface Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.Core.CoreInputView *, Windows.UI.ViewManagement.Core.CoreInputViewTransferringXYFocusEventArgs *>;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 7.0)
    ]
    enum CoreInputViewKind
    {
        Default = 0,
        Keyboard = 1,
        Handwriting = 2,
        Emoji = 3,
        [contract(Windows.Foundation.UniversalApiContract, 10.0)] Symbols = 4
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0)
    ]
    enum CoreInputViewOcclusionKind
    {
        Docked = 0,
        Floating = 1,
        Overlay = 2
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 6.0)
    ]
    enum CoreInputViewXYFocusTransferDirection
    {
        Up = 0,
        Right = 1,
        Down = 2,
        Left = 3
    };

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0),
        exclusiveto(Windows.UI.ViewManagement.Core.CoreInputView),
        uuid(c770cd7a-7001-4c32-bf94-25c1f554cbf1)
    ]
    interface ICoreInputView : IInspectable
    {
        [eventadd] HRESULT OcclusionsChanged([in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.Core.CoreInputView *, Windows.UI.ViewManagement.Core.CoreInputViewOcclusionsChangedEventArgs *> *handler, [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT OcclusionsChanged([in] EventRegistrationToken token);
        HRESULT GetCoreInputViewOcclusions([out, retval] Windows.Foundation.Collections.IVectorView<Windows.UI.ViewManagement.Core.CoreInputViewOcclusion *> **result);
        HRESULT TryShowPrimaryView([out, retval] boolean *result);
        HRESULT TryHidePrimaryView([out, retval] boolean *result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 6.0),
        exclusiveto(Windows.UI.ViewManagement.Core.CoreInputView),
        uuid(0ed726c1-e09a-4ae8-aedf-dfa4857d1a01)
    ]
    interface ICoreInputView2 : IInspectable
    {
        [eventadd] HRESULT XYFocusTransferringFromPrimaryView([in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.Core.CoreInputView *, Windows.UI.ViewManagement.Core.CoreInputViewTransferringXYFocusEventArgs *> *handler, [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT XYFocusTransferringFromPrimaryView([in] EventRegistrationToken token);
        [eventadd] HRESULT XYFocusTransferredToPrimaryView([in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.Core.CoreInputView *, IInspectable *> *handler, [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT XYFocusTransferredToPrimaryView([in] EventRegistrationToken token);
        HRESULT TryTransferXYFocusToPrimaryView([in] Windows.Foundation.Rect origin, [in] Windows.UI.ViewManagement.Core.CoreInputViewXYFocusTransferDirection direction, [out, retval] boolean *result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 7.0),
        exclusiveto(Windows.UI.ViewManagement.Core.CoreInputView),
        uuid(bc941653-3ab9-4849-8f58-46e7f0353cfc)
    ]
    interface ICoreInputView3 : IInspectable
    {
        [overload("TryShow")] HRESULT TryShow([out, retval] boolean *result);
        [overload("TryShow")] HRESULT TryShowWithKind([in] Windows.UI.ViewManagement.Core.CoreInputViewKind type, [out, retval] boolean *result);
        HRESULT TryHide([out, retval] boolean *result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 10.0),
        exclusiveto(Windows.UI.ViewManagement.Core.CoreInputView),
        uuid(002863d6-d9ef-57eb-8cef-77f6ce1b7ee7)
    ]
    interface ICoreInputView4 : IInspectable
    {
        [eventadd] HRESULT PrimaryViewShowing([in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.Core.CoreInputView *, Windows.UI.ViewManagement.Core.CoreInputViewShowingEventArgs *> *handler, [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT PrimaryViewShowing([in] EventRegistrationToken token);
        [eventadd] HRESULT PrimaryViewHiding([in] Windows.Foundation.TypedEventHandler<Windows.UI.ViewManagement.Core.CoreInputView *, Windows.UI.ViewManagement.Core.CoreInputViewHidingEventArgs *> *handler, [out, retval] EventRegistrationToken *token);
        [eventremove] HRESULT PrimaryViewHiding([in] EventRegistrationToken token);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 10.0),
        exclusiveto(Windows.UI.ViewManagement.Core.CoreInputViewHidingEventArgs),
        uuid(eada47bd-bac5-5336-848d-41083584daad)
    ]
    interface ICoreInputViewHidingEventArgs : IInspectable
    {
        HRESULT TryCancel([out, retval] boolean *result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0),
        exclusiveto(Windows.UI.ViewManagement.Core.CoreInputViewOcclusion),
        uuid(cc36ce06-3865-4177-b5f5-8b65e0b9ce84)
    ]
    interface ICoreInputViewOcclusion : IInspectable
    {
        [propget] HRESULT OccludingRect([out, retval] Windows.Foundation.Rect *rect);
        [propget] HRESULT OcclusionKind([out, retval] Windows.UI.ViewManagement.Core.CoreInputViewOcclusionKind *kind);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0),
        exclusiveto(Windows.UI.ViewManagement.Core.CoreInputViewOcclusionsChangedEventArgs),
        uuid(be1027e8-b3ee-4df7-9554-89cdc66082c2)
    ]
    interface ICoreInputViewOcclusionsChangedEventArgs : IInspectable
    {
        [propget] HRESULT Occlusions([out, retval] Windows.Foundation.Collections.IVectorView<Windows.UI.ViewManagement.Core.CoreInputViewOcclusion *> **value);
        [propget] HRESULT Handled([out, retval] boolean *value);
        [propput] HRESULT Handled([in] boolean value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 10.0),
        exclusiveto(Windows.UI.ViewManagement.Core.CoreInputViewShowingEventArgs),
        uuid(ca52261b-fb9e-5daf-a98c-262b8b76af50)
    ]
    interface ICoreInputViewShowingEventArgs : IInspectable
    {
        HRESULT TryCancel([out, retval] boolean *result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0),
        exclusiveto(Windows.UI.ViewManagement.Core.CoreInputView),
        uuid(7d9b97cd-edbe-49cf-a54f-337de052907f)
    ]
    interface ICoreInputViewStatics : IInspectable
    {
        HRESULT GetForCurrentView([out, retval] Windows.UI.ViewManagement.Core.CoreInputView **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 8.0),
        exclusiveto(Windows.UI.ViewManagement.Core.CoreInputView),
        uuid(7ebc0862-d049-4e52-87b0-1e90e98c49ed)
    ]
    interface ICoreInputViewStatics2 : IInspectable
    {
        HRESULT GetForUIContext([in] Windows.UI.UIContext *context, [out, retval] Windows.UI.ViewManagement.Core.CoreInputView **result);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 6.0),
        exclusiveto(Windows.UI.ViewManagement.Core.CoreInputViewTransferringXYFocusEventArgs),
        uuid(04de169f-ba02-4850-8b55-d82d03ba6d7f)
    ]
    interface ICoreInputViewTransferringXYFocusEventArgs : IInspectable
    {
        [propget] HRESULT Origin([out, retval] Windows.Foundation.Rect *value);
        [propget] HRESULT Direction([out, retval] Windows.UI.ViewManagement.Core.CoreInputViewXYFocusTransferDirection *value);
        [propput] HRESULT TransferHandled([in] boolean value);
        [propget] HRESULT TransferHandled([out, retval] boolean *value);
        [propput] HRESULT KeepPrimaryViewVisible([in] boolean value);
        [propget] HRESULT KeepPrimaryViewVisible([out, retval] boolean *value);
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0),
        marshaling_behavior(agile),
        static(Windows.UI.ViewManagement.Core.ICoreInputViewStatics, Windows.Foundation.UniversalApiContract, 5.0),
        static(Windows.UI.ViewManagement.Core.ICoreInputViewStatics2, Windows.Foundation.UniversalApiContract, 8.0),
        threading(both)
    ]
    runtimeclass CoreInputView
    {
        [default] interface Windows.UI.ViewManagement.Core.ICoreInputView;
        [contract(Windows.Foundation.UniversalApiContract, 6.0)] interface Windows.UI.ViewManagement.Core.ICoreInputView2;
        [contract(Windows.Foundation.UniversalApiContract, 7.0)] interface Windows.UI.ViewManagement.Core.ICoreInputView3;
        [contract(Windows.Foundation.UniversalApiContract, 10.0)] interface Windows.UI.ViewManagement.Core.ICoreInputView4;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 10.0),
        marshaling_behavior(agile)
    ]
    runtimeclass CoreInputViewHidingEventArgs
    {
        [default] interface Windows.UI.ViewManagement.Core.ICoreInputViewHidingEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0),
        marshaling_behavior(agile)
    ]
    runtimeclass CoreInputViewOcclusion
    {
        [default] interface Windows.UI.ViewManagement.Core.ICoreInputViewOcclusion;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 5.0),
        marshaling_behavior(agile)
    ]
    runtimeclass CoreInputViewOcclusionsChangedEventArgs
    {
        [default] interface Windows.UI.ViewManagement.Core.ICoreInputViewOcclusionsChangedEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 10.0),
        marshaling_behavior(agile)
    ]
    runtimeclass CoreInputViewShowingEventArgs
    {
        [default] interface Windows.UI.ViewManagement.Core.ICoreInputViewShowingEventArgs;
    }

    [
        contract(Windows.Foundation.UniversalApiContract, 6.0),
        marshaling_behavior(agile)
    ]
    runtimeclass CoreInputViewTransferringXYFocusEventArgs
    {
        [default] interface Windows.UI.ViewManagement.Core.ICoreInputViewTransferringXYFocusEventArgs;
    }
}
