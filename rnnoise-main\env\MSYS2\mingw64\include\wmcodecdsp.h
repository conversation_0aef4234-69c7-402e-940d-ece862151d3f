/*** Autogenerated by WIDL 10.12 from include/wmcodecdsp.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __wmcodecdsp_h__
#define __wmcodecdsp_h__

/* Forward declarations */

#ifndef __IWMColorConvProps_FWD_DEFINED__
#define __IWMColorConvProps_FWD_DEFINED__
typedef interface IWMColorConvProps IWMColorConvProps;
#ifdef __cplusplus
interface IWMColorConvProps;
#endif /* __cplusplus */
#endif

#ifndef __IWMValidate_FWD_DEFINED__
#define __IWMValidate_FWD_DEFINED__
typedef interface IWMValidate IWMValidate;
#ifdef __cplusplus
interface IWMValidate;
#endif /* __cplusplus */
#endif

#ifndef __CMpeg4DecMediaObject_FWD_DEFINED__
#define __CMpeg4DecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMpeg4DecMediaObject CMpeg4DecMediaObject;
#else
typedef struct CMpeg4DecMediaObject CMpeg4DecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CMpeg4DecMediaObject_FWD_DEFINED__ */

#ifndef __CMpeg43DecMediaObject_FWD_DEFINED__
#define __CMpeg43DecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMpeg43DecMediaObject CMpeg43DecMediaObject;
#else
typedef struct CMpeg43DecMediaObject CMpeg43DecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CMpeg43DecMediaObject_FWD_DEFINED__ */

#ifndef __CMpeg4sDecMediaObject_FWD_DEFINED__
#define __CMpeg4sDecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMpeg4sDecMediaObject CMpeg4sDecMediaObject;
#else
typedef struct CMpeg4sDecMediaObject CMpeg4sDecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CMpeg4sDecMediaObject_FWD_DEFINED__ */

#ifndef __CMpeg4sDecMFT_FWD_DEFINED__
#define __CMpeg4sDecMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMpeg4sDecMFT CMpeg4sDecMFT;
#else
typedef struct CMpeg4sDecMFT CMpeg4sDecMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMpeg4sDecMFT_FWD_DEFINED__ */

#ifndef __CZuneM4S2DecMediaObject_FWD_DEFINED__
#define __CZuneM4S2DecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CZuneM4S2DecMediaObject CZuneM4S2DecMediaObject;
#else
typedef struct CZuneM4S2DecMediaObject CZuneM4S2DecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CZuneM4S2DecMediaObject_FWD_DEFINED__ */

#ifndef __CMpeg4EncMediaObject_FWD_DEFINED__
#define __CMpeg4EncMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMpeg4EncMediaObject CMpeg4EncMediaObject;
#else
typedef struct CMpeg4EncMediaObject CMpeg4EncMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CMpeg4EncMediaObject_FWD_DEFINED__ */

#ifndef __CMpeg4sEncMediaObject_FWD_DEFINED__
#define __CMpeg4sEncMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMpeg4sEncMediaObject CMpeg4sEncMediaObject;
#else
typedef struct CMpeg4sEncMediaObject CMpeg4sEncMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CMpeg4sEncMediaObject_FWD_DEFINED__ */

#ifndef __CMSSCDecMediaObject_FWD_DEFINED__
#define __CMSSCDecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSSCDecMediaObject CMSSCDecMediaObject;
#else
typedef struct CMSSCDecMediaObject CMSSCDecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CMSSCDecMediaObject_FWD_DEFINED__ */

#ifndef __CMSSCEncMediaObject_FWD_DEFINED__
#define __CMSSCEncMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSSCEncMediaObject CMSSCEncMediaObject;
#else
typedef struct CMSSCEncMediaObject CMSSCEncMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CMSSCEncMediaObject_FWD_DEFINED__ */

#ifndef __CMSSCEncMediaObject2_FWD_DEFINED__
#define __CMSSCEncMediaObject2_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSSCEncMediaObject2 CMSSCEncMediaObject2;
#else
typedef struct CMSSCEncMediaObject2 CMSSCEncMediaObject2;
#endif /* defined __cplusplus */
#endif /* defined __CMSSCEncMediaObject2_FWD_DEFINED__ */

#ifndef __CWMADecMediaObject_FWD_DEFINED__
#define __CWMADecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMADecMediaObject CWMADecMediaObject;
#else
typedef struct CWMADecMediaObject CWMADecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWMADecMediaObject_FWD_DEFINED__ */

#ifndef __CWMAEncMediaObject_FWD_DEFINED__
#define __CWMAEncMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMAEncMediaObject CWMAEncMediaObject;
#else
typedef struct CWMAEncMediaObject CWMAEncMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWMAEncMediaObject_FWD_DEFINED__ */

#ifndef __CWMATransMediaObject_FWD_DEFINED__
#define __CWMATransMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMATransMediaObject CWMATransMediaObject;
#else
typedef struct CWMATransMediaObject CWMATransMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWMATransMediaObject_FWD_DEFINED__ */

#ifndef __CWMSPDecMediaObject_FWD_DEFINED__
#define __CWMSPDecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMSPDecMediaObject CWMSPDecMediaObject;
#else
typedef struct CWMSPDecMediaObject CWMSPDecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWMSPDecMediaObject_FWD_DEFINED__ */

#ifndef __CWMSPEncMediaObject_FWD_DEFINED__
#define __CWMSPEncMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMSPEncMediaObject CWMSPEncMediaObject;
#else
typedef struct CWMSPEncMediaObject CWMSPEncMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWMSPEncMediaObject_FWD_DEFINED__ */

#ifndef __CWMSPEncMediaObject2_FWD_DEFINED__
#define __CWMSPEncMediaObject2_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMSPEncMediaObject2 CWMSPEncMediaObject2;
#else
typedef struct CWMSPEncMediaObject2 CWMSPEncMediaObject2;
#endif /* defined __cplusplus */
#endif /* defined __CWMSPEncMediaObject2_FWD_DEFINED__ */

#ifndef __CWMTDecMediaObject_FWD_DEFINED__
#define __CWMTDecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMTDecMediaObject CWMTDecMediaObject;
#else
typedef struct CWMTDecMediaObject CWMTDecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWMTDecMediaObject_FWD_DEFINED__ */

#ifndef __CWMTEncMediaObject_FWD_DEFINED__
#define __CWMTEncMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMTEncMediaObject CWMTEncMediaObject;
#else
typedef struct CWMTEncMediaObject CWMTEncMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWMTEncMediaObject_FWD_DEFINED__ */

#ifndef __CWMVDecMediaObject_FWD_DEFINED__
#define __CWMVDecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMVDecMediaObject CWMVDecMediaObject;
#else
typedef struct CWMVDecMediaObject CWMVDecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWMVDecMediaObject_FWD_DEFINED__ */

#ifndef __CWMVEncMediaObject2_FWD_DEFINED__
#define __CWMVEncMediaObject2_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMVEncMediaObject2 CWMVEncMediaObject2;
#else
typedef struct CWMVEncMediaObject2 CWMVEncMediaObject2;
#endif /* defined __cplusplus */
#endif /* defined __CWMVEncMediaObject2_FWD_DEFINED__ */

#ifndef __CWMVXEncMediaObject_FWD_DEFINED__
#define __CWMVXEncMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMVXEncMediaObject CWMVXEncMediaObject;
#else
typedef struct CWMVXEncMediaObject CWMVXEncMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWMVXEncMediaObject_FWD_DEFINED__ */

#ifndef __CWMV9EncMediaObject_FWD_DEFINED__
#define __CWMV9EncMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMV9EncMediaObject CWMV9EncMediaObject;
#else
typedef struct CWMV9EncMediaObject CWMV9EncMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWMV9EncMediaObject_FWD_DEFINED__ */

#ifndef __CWVC1DecMediaObject_FWD_DEFINED__
#define __CWVC1DecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWVC1DecMediaObject CWVC1DecMediaObject;
#else
typedef struct CWVC1DecMediaObject CWVC1DecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWVC1DecMediaObject_FWD_DEFINED__ */

#ifndef __CWVC1EncMediaObject_FWD_DEFINED__
#define __CWVC1EncMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWVC1EncMediaObject CWVC1EncMediaObject;
#else
typedef struct CWVC1EncMediaObject CWVC1EncMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CWVC1EncMediaObject_FWD_DEFINED__ */

#ifndef __CDeColorConvMediaObject_FWD_DEFINED__
#define __CDeColorConvMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CDeColorConvMediaObject CDeColorConvMediaObject;
#else
typedef struct CDeColorConvMediaObject CDeColorConvMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CDeColorConvMediaObject_FWD_DEFINED__ */

#ifndef __CDVDecoderMediaObject_FWD_DEFINED__
#define __CDVDecoderMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CDVDecoderMediaObject CDVDecoderMediaObject;
#else
typedef struct CDVDecoderMediaObject CDVDecoderMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CDVDecoderMediaObject_FWD_DEFINED__ */

#ifndef __CDVEncoderMediaObject_FWD_DEFINED__
#define __CDVEncoderMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CDVEncoderMediaObject CDVEncoderMediaObject;
#else
typedef struct CDVEncoderMediaObject CDVEncoderMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CDVEncoderMediaObject_FWD_DEFINED__ */

#ifndef __CMpeg2DecMediaObject_FWD_DEFINED__
#define __CMpeg2DecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMpeg2DecMediaObject CMpeg2DecMediaObject;
#else
typedef struct CMpeg2DecMediaObject CMpeg2DecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CMpeg2DecMediaObject_FWD_DEFINED__ */

#ifndef __CPK_DS_MPEG2Decoder_FWD_DEFINED__
#define __CPK_DS_MPEG2Decoder_FWD_DEFINED__
#ifdef __cplusplus
typedef class CPK_DS_MPEG2Decoder CPK_DS_MPEG2Decoder;
#else
typedef struct CPK_DS_MPEG2Decoder CPK_DS_MPEG2Decoder;
#endif /* defined __cplusplus */
#endif /* defined __CPK_DS_MPEG2Decoder_FWD_DEFINED__ */

#ifndef __CAC3DecMediaObject_FWD_DEFINED__
#define __CAC3DecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CAC3DecMediaObject CAC3DecMediaObject;
#else
typedef struct CAC3DecMediaObject CAC3DecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CAC3DecMediaObject_FWD_DEFINED__ */

#ifndef __CPK_DS_AC3Decoder_FWD_DEFINED__
#define __CPK_DS_AC3Decoder_FWD_DEFINED__
#ifdef __cplusplus
typedef class CPK_DS_AC3Decoder CPK_DS_AC3Decoder;
#else
typedef struct CPK_DS_AC3Decoder CPK_DS_AC3Decoder;
#endif /* defined __cplusplus */
#endif /* defined __CPK_DS_AC3Decoder_FWD_DEFINED__ */

#ifndef __CMP3DecMediaObject_FWD_DEFINED__
#define __CMP3DecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMP3DecMediaObject CMP3DecMediaObject;
#else
typedef struct CMP3DecMediaObject CMP3DecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CMP3DecMediaObject_FWD_DEFINED__ */

#ifndef __CResamplerMediaObject_FWD_DEFINED__
#define __CResamplerMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CResamplerMediaObject CResamplerMediaObject;
#else
typedef struct CResamplerMediaObject CResamplerMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CResamplerMediaObject_FWD_DEFINED__ */

#ifndef __CResizerMediaObject_FWD_DEFINED__
#define __CResizerMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CResizerMediaObject CResizerMediaObject;
#else
typedef struct CResizerMediaObject CResizerMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CResizerMediaObject_FWD_DEFINED__ */

#ifndef __CInterlaceMediaObject_FWD_DEFINED__
#define __CInterlaceMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CInterlaceMediaObject CInterlaceMediaObject;
#else
typedef struct CInterlaceMediaObject CInterlaceMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CInterlaceMediaObject_FWD_DEFINED__ */

#ifndef __CWMAudioLFXAPO_FWD_DEFINED__
#define __CWMAudioLFXAPO_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMAudioLFXAPO CWMAudioLFXAPO;
#else
typedef struct CWMAudioLFXAPO CWMAudioLFXAPO;
#endif /* defined __cplusplus */
#endif /* defined __CWMAudioLFXAPO_FWD_DEFINED__ */

#ifndef __CWMAudioGFXAPO_FWD_DEFINED__
#define __CWMAudioGFXAPO_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMAudioGFXAPO CWMAudioGFXAPO;
#else
typedef struct CWMAudioGFXAPO CWMAudioGFXAPO;
#endif /* defined __cplusplus */
#endif /* defined __CWMAudioGFXAPO_FWD_DEFINED__ */

#ifndef __CWMAudioSpdTxDMO_FWD_DEFINED__
#define __CWMAudioSpdTxDMO_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMAudioSpdTxDMO CWMAudioSpdTxDMO;
#else
typedef struct CWMAudioSpdTxDMO CWMAudioSpdTxDMO;
#endif /* defined __cplusplus */
#endif /* defined __CWMAudioSpdTxDMO_FWD_DEFINED__ */

#ifndef __CWMAudioAEC_FWD_DEFINED__
#define __CWMAudioAEC_FWD_DEFINED__
#ifdef __cplusplus
typedef class CWMAudioAEC CWMAudioAEC;
#else
typedef struct CWMAudioAEC CWMAudioAEC;
#endif /* defined __cplusplus */
#endif /* defined __CWMAudioAEC_FWD_DEFINED__ */

#ifndef __CClusterDetectorDmo_FWD_DEFINED__
#define __CClusterDetectorDmo_FWD_DEFINED__
#ifdef __cplusplus
typedef class CClusterDetectorDmo CClusterDetectorDmo;
#else
typedef struct CClusterDetectorDmo CClusterDetectorDmo;
#endif /* defined __cplusplus */
#endif /* defined __CClusterDetectorDmo_FWD_DEFINED__ */

#ifndef __CColorControlDmo_FWD_DEFINED__
#define __CColorControlDmo_FWD_DEFINED__
#ifdef __cplusplus
typedef class CColorControlDmo CColorControlDmo;
#else
typedef struct CColorControlDmo CColorControlDmo;
#endif /* defined __cplusplus */
#endif /* defined __CColorControlDmo_FWD_DEFINED__ */

#ifndef __CColorConvertDMO_FWD_DEFINED__
#define __CColorConvertDMO_FWD_DEFINED__
#ifdef __cplusplus
typedef class CColorConvertDMO CColorConvertDMO;
#else
typedef struct CColorConvertDMO CColorConvertDMO;
#endif /* defined __cplusplus */
#endif /* defined __CColorConvertDMO_FWD_DEFINED__ */

#ifndef __CColorLegalizerDmo_FWD_DEFINED__
#define __CColorLegalizerDmo_FWD_DEFINED__
#ifdef __cplusplus
typedef class CColorLegalizerDmo CColorLegalizerDmo;
#else
typedef struct CColorLegalizerDmo CColorLegalizerDmo;
#endif /* defined __cplusplus */
#endif /* defined __CColorLegalizerDmo_FWD_DEFINED__ */

#ifndef __CFrameInterpDMO_FWD_DEFINED__
#define __CFrameInterpDMO_FWD_DEFINED__
#ifdef __cplusplus
typedef class CFrameInterpDMO CFrameInterpDMO;
#else
typedef struct CFrameInterpDMO CFrameInterpDMO;
#endif /* defined __cplusplus */
#endif /* defined __CFrameInterpDMO_FWD_DEFINED__ */

#ifndef __CFrameRateConvertDmo_FWD_DEFINED__
#define __CFrameRateConvertDmo_FWD_DEFINED__
#ifdef __cplusplus
typedef class CFrameRateConvertDmo CFrameRateConvertDmo;
#else
typedef struct CFrameRateConvertDmo CFrameRateConvertDmo;
#endif /* defined __cplusplus */
#endif /* defined __CFrameRateConvertDmo_FWD_DEFINED__ */

#ifndef __CResizerDMO_FWD_DEFINED__
#define __CResizerDMO_FWD_DEFINED__
#ifdef __cplusplus
typedef class CResizerDMO CResizerDMO;
#else
typedef struct CResizerDMO CResizerDMO;
#endif /* defined __cplusplus */
#endif /* defined __CResizerDMO_FWD_DEFINED__ */

#ifndef __CShotDetectorDmo_FWD_DEFINED__
#define __CShotDetectorDmo_FWD_DEFINED__
#ifdef __cplusplus
typedef class CShotDetectorDmo CShotDetectorDmo;
#else
typedef struct CShotDetectorDmo CShotDetectorDmo;
#endif /* defined __cplusplus */
#endif /* defined __CShotDetectorDmo_FWD_DEFINED__ */

#ifndef __CSmpteTransformsDmo_FWD_DEFINED__
#define __CSmpteTransformsDmo_FWD_DEFINED__
#ifdef __cplusplus
typedef class CSmpteTransformsDmo CSmpteTransformsDmo;
#else
typedef struct CSmpteTransformsDmo CSmpteTransformsDmo;
#endif /* defined __cplusplus */
#endif /* defined __CSmpteTransformsDmo_FWD_DEFINED__ */

#ifndef __CThumbnailGeneratorDmo_FWD_DEFINED__
#define __CThumbnailGeneratorDmo_FWD_DEFINED__
#ifdef __cplusplus
typedef class CThumbnailGeneratorDmo CThumbnailGeneratorDmo;
#else
typedef struct CThumbnailGeneratorDmo CThumbnailGeneratorDmo;
#endif /* defined __cplusplus */
#endif /* defined __CThumbnailGeneratorDmo_FWD_DEFINED__ */

#ifndef __CTocGeneratorDmo_FWD_DEFINED__
#define __CTocGeneratorDmo_FWD_DEFINED__
#ifdef __cplusplus
typedef class CTocGeneratorDmo CTocGeneratorDmo;
#else
typedef struct CTocGeneratorDmo CTocGeneratorDmo;
#endif /* defined __cplusplus */
#endif /* defined __CTocGeneratorDmo_FWD_DEFINED__ */

#ifndef __CMPEGAACDecMediaObject_FWD_DEFINED__
#define __CMPEGAACDecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMPEGAACDecMediaObject CMPEGAACDecMediaObject;
#else
typedef struct CMPEGAACDecMediaObject CMPEGAACDecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CMPEGAACDecMediaObject_FWD_DEFINED__ */

#ifndef __CNokiaAACDecMediaObject_FWD_DEFINED__
#define __CNokiaAACDecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CNokiaAACDecMediaObject CNokiaAACDecMediaObject;
#else
typedef struct CNokiaAACDecMediaObject CNokiaAACDecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CNokiaAACDecMediaObject_FWD_DEFINED__ */

#ifndef __CVodafoneAACDecMediaObject_FWD_DEFINED__
#define __CVodafoneAACDecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CVodafoneAACDecMediaObject CVodafoneAACDecMediaObject;
#else
typedef struct CVodafoneAACDecMediaObject CVodafoneAACDecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CVodafoneAACDecMediaObject_FWD_DEFINED__ */

#ifndef __CZuneAACCCDecMediaObject_FWD_DEFINED__
#define __CZuneAACCCDecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CZuneAACCCDecMediaObject CZuneAACCCDecMediaObject;
#else
typedef struct CZuneAACCCDecMediaObject CZuneAACCCDecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CZuneAACCCDecMediaObject_FWD_DEFINED__ */

#ifndef __CNokiaAACCCDecMediaObject_FWD_DEFINED__
#define __CNokiaAACCCDecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CNokiaAACCCDecMediaObject CNokiaAACCCDecMediaObject;
#else
typedef struct CNokiaAACCCDecMediaObject CNokiaAACCCDecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CNokiaAACCCDecMediaObject_FWD_DEFINED__ */

#ifndef __CVodafoneAACCCDecMediaObject_FWD_DEFINED__
#define __CVodafoneAACCCDecMediaObject_FWD_DEFINED__
#ifdef __cplusplus
typedef class CVodafoneAACCCDecMediaObject CVodafoneAACCCDecMediaObject;
#else
typedef struct CVodafoneAACCCDecMediaObject CVodafoneAACCCDecMediaObject;
#endif /* defined __cplusplus */
#endif /* defined __CVodafoneAACCCDecMediaObject_FWD_DEFINED__ */

#ifndef __CMPEG2EncoderDS_FWD_DEFINED__
#define __CMPEG2EncoderDS_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMPEG2EncoderDS CMPEG2EncoderDS;
#else
typedef struct CMPEG2EncoderDS CMPEG2EncoderDS;
#endif /* defined __cplusplus */
#endif /* defined __CMPEG2EncoderDS_FWD_DEFINED__ */

#ifndef __CMPEG2EncoderVideoDS_FWD_DEFINED__
#define __CMPEG2EncoderVideoDS_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMPEG2EncoderVideoDS CMPEG2EncoderVideoDS;
#else
typedef struct CMPEG2EncoderVideoDS CMPEG2EncoderVideoDS;
#endif /* defined __cplusplus */
#endif /* defined __CMPEG2EncoderVideoDS_FWD_DEFINED__ */

#ifndef __CMPEG2EncoderAudioDS_FWD_DEFINED__
#define __CMPEG2EncoderAudioDS_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMPEG2EncoderAudioDS CMPEG2EncoderAudioDS;
#else
typedef struct CMPEG2EncoderAudioDS CMPEG2EncoderAudioDS;
#endif /* defined __cplusplus */
#endif /* defined __CMPEG2EncoderAudioDS_FWD_DEFINED__ */

#ifndef __CMPEG2AudDecoderDS_FWD_DEFINED__
#define __CMPEG2AudDecoderDS_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMPEG2AudDecoderDS CMPEG2AudDecoderDS;
#else
typedef struct CMPEG2AudDecoderDS CMPEG2AudDecoderDS;
#endif /* defined __cplusplus */
#endif /* defined __CMPEG2AudDecoderDS_FWD_DEFINED__ */

#ifndef __CMPEG2VidDecoderDS_FWD_DEFINED__
#define __CMPEG2VidDecoderDS_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMPEG2VidDecoderDS CMPEG2VidDecoderDS;
#else
typedef struct CMPEG2VidDecoderDS CMPEG2VidDecoderDS;
#endif /* defined __cplusplus */
#endif /* defined __CMPEG2VidDecoderDS_FWD_DEFINED__ */

#ifndef __CDTVAudDecoderDS_FWD_DEFINED__
#define __CDTVAudDecoderDS_FWD_DEFINED__
#ifdef __cplusplus
typedef class CDTVAudDecoderDS CDTVAudDecoderDS;
#else
typedef struct CDTVAudDecoderDS CDTVAudDecoderDS;
#endif /* defined __cplusplus */
#endif /* defined __CDTVAudDecoderDS_FWD_DEFINED__ */

#ifndef __CDTVVidDecoderDS_FWD_DEFINED__
#define __CDTVVidDecoderDS_FWD_DEFINED__
#ifdef __cplusplus
typedef class CDTVVidDecoderDS CDTVVidDecoderDS;
#else
typedef struct CDTVVidDecoderDS CDTVVidDecoderDS;
#endif /* defined __cplusplus */
#endif /* defined __CDTVVidDecoderDS_FWD_DEFINED__ */

#ifndef __CMSAC3Enc_FWD_DEFINED__
#define __CMSAC3Enc_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSAC3Enc CMSAC3Enc;
#else
typedef struct CMSAC3Enc CMSAC3Enc;
#endif /* defined __cplusplus */
#endif /* defined __CMSAC3Enc_FWD_DEFINED__ */

#ifndef __CMSH264DecoderMFT_FWD_DEFINED__
#define __CMSH264DecoderMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSH264DecoderMFT CMSH264DecoderMFT;
#else
typedef struct CMSH264DecoderMFT CMSH264DecoderMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMSH264DecoderMFT_FWD_DEFINED__ */

#ifndef __CMSH264EncoderMFT_FWD_DEFINED__
#define __CMSH264EncoderMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSH264EncoderMFT CMSH264EncoderMFT;
#else
typedef struct CMSH264EncoderMFT CMSH264EncoderMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMSH264EncoderMFT_FWD_DEFINED__ */

#ifndef __CMSH264RemuxMFT_FWD_DEFINED__
#define __CMSH264RemuxMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSH264RemuxMFT CMSH264RemuxMFT;
#else
typedef struct CMSH264RemuxMFT CMSH264RemuxMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMSH264RemuxMFT_FWD_DEFINED__ */

#ifndef __CMSAACDecMFT_FWD_DEFINED__
#define __CMSAACDecMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSAACDecMFT CMSAACDecMFT;
#else
typedef struct CMSAACDecMFT CMSAACDecMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMSAACDecMFT_FWD_DEFINED__ */

#ifndef __AACMFTEncoder_FWD_DEFINED__
#define __AACMFTEncoder_FWD_DEFINED__
#ifdef __cplusplus
typedef class AACMFTEncoder AACMFTEncoder;
#else
typedef struct AACMFTEncoder AACMFTEncoder;
#endif /* defined __cplusplus */
#endif /* defined __AACMFTEncoder_FWD_DEFINED__ */

#ifndef __CMSDDPlusDecMFT_FWD_DEFINED__
#define __CMSDDPlusDecMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSDDPlusDecMFT CMSDDPlusDecMFT;
#else
typedef struct CMSDDPlusDecMFT CMSDDPlusDecMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMSDDPlusDecMFT_FWD_DEFINED__ */

#ifndef __CMPEG2VideoEncoderMFT_FWD_DEFINED__
#define __CMPEG2VideoEncoderMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMPEG2VideoEncoderMFT CMPEG2VideoEncoderMFT;
#else
typedef struct CMPEG2VideoEncoderMFT CMPEG2VideoEncoderMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMPEG2VideoEncoderMFT_FWD_DEFINED__ */

#ifndef __CMPEG2AudioEncoderMFT_FWD_DEFINED__
#define __CMPEG2AudioEncoderMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMPEG2AudioEncoderMFT CMPEG2AudioEncoderMFT;
#else
typedef struct CMPEG2AudioEncoderMFT CMPEG2AudioEncoderMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMPEG2AudioEncoderMFT_FWD_DEFINED__ */

#ifndef __CMSMPEGDecoderMFT_FWD_DEFINED__
#define __CMSMPEGDecoderMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSMPEGDecoderMFT CMSMPEGDecoderMFT;
#else
typedef struct CMSMPEGDecoderMFT CMSMPEGDecoderMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMSMPEGDecoderMFT_FWD_DEFINED__ */

#ifndef __CMSMPEGAudDecMFT_FWD_DEFINED__
#define __CMSMPEGAudDecMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSMPEGAudDecMFT CMSMPEGAudDecMFT;
#else
typedef struct CMSMPEGAudDecMFT CMSMPEGAudDecMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMSMPEGAudDecMFT_FWD_DEFINED__ */

#ifndef __CMSDolbyDigitalEncMFT_FWD_DEFINED__
#define __CMSDolbyDigitalEncMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSDolbyDigitalEncMFT CMSDolbyDigitalEncMFT;
#else
typedef struct CMSDolbyDigitalEncMFT CMSDolbyDigitalEncMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMSDolbyDigitalEncMFT_FWD_DEFINED__ */

#ifndef __MP3ACMCodecWrapper_FWD_DEFINED__
#define __MP3ACMCodecWrapper_FWD_DEFINED__
#ifdef __cplusplus
typedef class MP3ACMCodecWrapper MP3ACMCodecWrapper;
#else
typedef struct MP3ACMCodecWrapper MP3ACMCodecWrapper;
#endif /* defined __cplusplus */
#endif /* defined __MP3ACMCodecWrapper_FWD_DEFINED__ */

#ifndef __CMSVideoDSPMFT_FWD_DEFINED__
#define __CMSVideoDSPMFT_FWD_DEFINED__
#ifdef __cplusplus
typedef class CMSVideoDSPMFT CMSVideoDSPMFT;
#else
typedef struct CMSVideoDSPMFT CMSVideoDSPMFT;
#endif /* defined __cplusplus */
#endif /* defined __CMSVideoDSPMFT_FWD_DEFINED__ */

/* Headers for imported files */

#include <mediaobj.h>
#include <strmif.h>

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************************
 * IWMColorConvProps interface
 */
#ifndef __IWMColorConvProps_INTERFACE_DEFINED__
#define __IWMColorConvProps_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMColorConvProps, 0xe6a49e22, 0xc099, 0x421d, 0xaa,0xd3, 0xc0,0x61,0xfb,0x4a,0xe8,0x5b);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e6a49e22-c099-421d-aad3-c061fb4ae85b")
IWMColorConvProps : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetMode(
        LONG mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFullCroppingParam(
        LONG src_left,
        LONG src_top,
        LONG dst_left,
        LONG dst_top,
        LONG width,
        LONG height) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMColorConvProps, 0xe6a49e22, 0xc099, 0x421d, 0xaa,0xd3, 0xc0,0x61,0xfb,0x4a,0xe8,0x5b)
#endif
#else
typedef struct IWMColorConvPropsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMColorConvProps *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMColorConvProps *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMColorConvProps *This);

    /*** IWMColorConvProps methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMode)(
        IWMColorConvProps *This,
        LONG mode);

    HRESULT (STDMETHODCALLTYPE *SetFullCroppingParam)(
        IWMColorConvProps *This,
        LONG src_left,
        LONG src_top,
        LONG dst_left,
        LONG dst_top,
        LONG width,
        LONG height);

    END_INTERFACE
} IWMColorConvPropsVtbl;

interface IWMColorConvProps {
    CONST_VTBL IWMColorConvPropsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMColorConvProps_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMColorConvProps_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMColorConvProps_Release(This) (This)->lpVtbl->Release(This)
/*** IWMColorConvProps methods ***/
#define IWMColorConvProps_SetMode(This,mode) (This)->lpVtbl->SetMode(This,mode)
#define IWMColorConvProps_SetFullCroppingParam(This,src_left,src_top,dst_left,dst_top,width,height) (This)->lpVtbl->SetFullCroppingParam(This,src_left,src_top,dst_left,dst_top,width,height)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMColorConvProps_QueryInterface(IWMColorConvProps* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMColorConvProps_AddRef(IWMColorConvProps* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMColorConvProps_Release(IWMColorConvProps* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMColorConvProps methods ***/
static inline HRESULT IWMColorConvProps_SetMode(IWMColorConvProps* This,LONG mode) {
    return This->lpVtbl->SetMode(This,mode);
}
static inline HRESULT IWMColorConvProps_SetFullCroppingParam(IWMColorConvProps* This,LONG src_left,LONG src_top,LONG dst_left,LONG dst_top,LONG width,LONG height) {
    return This->lpVtbl->SetFullCroppingParam(This,src_left,src_top,dst_left,dst_top,width,height);
}
#endif
#endif

#endif


#endif  /* __IWMColorConvProps_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IWMValidate interface
 */
#ifndef __IWMValidate_INTERFACE_DEFINED__
#define __IWMValidate_INTERFACE_DEFINED__

DEFINE_GUID(IID_IWMValidate, 0xcee3def2, 0x3808, 0x414d, 0xbe,0x66, 0xfa,0xfd,0x47,0x22,0x10,0xbc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("cee3def2-3808-414d-be66-fafd472210bc")
IWMValidate : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetIdentifier(
        GUID guidValidationID) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IWMValidate, 0xcee3def2, 0x3808, 0x414d, 0xbe,0x66, 0xfa,0xfd,0x47,0x22,0x10,0xbc)
#endif
#else
typedef struct IWMValidateVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IWMValidate *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IWMValidate *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IWMValidate *This);

    /*** IWMValidate methods ***/
    HRESULT (STDMETHODCALLTYPE *SetIdentifier)(
        IWMValidate *This,
        GUID guidValidationID);

    END_INTERFACE
} IWMValidateVtbl;

interface IWMValidate {
    CONST_VTBL IWMValidateVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IWMValidate_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IWMValidate_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IWMValidate_Release(This) (This)->lpVtbl->Release(This)
/*** IWMValidate methods ***/
#define IWMValidate_SetIdentifier(This,guidValidationID) (This)->lpVtbl->SetIdentifier(This,guidValidationID)
#else
/*** IUnknown methods ***/
static inline HRESULT IWMValidate_QueryInterface(IWMValidate* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IWMValidate_AddRef(IWMValidate* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IWMValidate_Release(IWMValidate* This) {
    return This->lpVtbl->Release(This);
}
/*** IWMValidate methods ***/
static inline HRESULT IWMValidate_SetIdentifier(IWMValidate* This,GUID guidValidationID) {
    return This->lpVtbl->SetIdentifier(This,guidValidationID);
}
#endif
#endif

#endif


#endif  /* __IWMValidate_INTERFACE_DEFINED__ */

DEFINE_GUID(MEDIASUBTYPE_I420,0x30323449,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
DEFINE_GUID(MEDIASUBTYPE_RAW_AAC1,0x000000ff,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
DEFINE_GUID(MEDIASUBTYPE_DVM,0x00002000,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
DEFINE_GUID(MEDIASUBTYPE_H264,0x34363248,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
DEFINE_GUID(MEDIASUBTYPE_h264,0x34363268,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
DEFINE_GUID(MEDIASUBTYPE_AVC1,0x31435641,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
DEFINE_GUID(MEDIASUBTYPE_X264,0x34363258,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
DEFINE_GUID(MEDIASUBTYPE_x264,0x34363278,0x0000,0x0010,0x80,0x00,0x00,0xaa,0x00,0x38,0x9b,0x71);
/*****************************************************************************
 * CMpeg4DecMediaObject coclass
 */

DEFINE_GUID(CLSID_CMpeg4DecMediaObject, 0xf371728a, 0x6052, 0x4d47, 0x82,0x7c, 0xd0,0x39,0x33,0x5d,0xfe,0x0a);

#ifdef __cplusplus
class DECLSPEC_UUID("f371728a-6052-4d47-827c-d039335dfe0a") CMpeg4DecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMpeg4DecMediaObject, 0xf371728a, 0x6052, 0x4d47, 0x82,0x7c, 0xd0,0x39,0x33,0x5d,0xfe,0x0a)
#endif
#endif

/*****************************************************************************
 * CMpeg43DecMediaObject coclass
 */

DEFINE_GUID(CLSID_CMpeg43DecMediaObject, 0xcba9e78b, 0x49a3, 0x49ea, 0x93,0xd4, 0x6b,0xcb,0xa8,0xc4,0xde,0x07);

#ifdef __cplusplus
class DECLSPEC_UUID("cba9e78b-49a3-49ea-93d4-6bcba8c4de07") CMpeg43DecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMpeg43DecMediaObject, 0xcba9e78b, 0x49a3, 0x49ea, 0x93,0xd4, 0x6b,0xcb,0xa8,0xc4,0xde,0x07)
#endif
#endif

/*****************************************************************************
 * CMpeg4sDecMediaObject coclass
 */

DEFINE_GUID(CLSID_CMpeg4sDecMediaObject, 0x2a11bae2, 0xfe6e, 0x4249, 0x86,0x4b, 0x9e,0x9e,0xd6,0xe8,0xdb,0xc2);

#ifdef __cplusplus
class DECLSPEC_UUID("2a11bae2-fe6e-4249-864b-9e9ed6e8dbc2") CMpeg4sDecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMpeg4sDecMediaObject, 0x2a11bae2, 0xfe6e, 0x4249, 0x86,0x4b, 0x9e,0x9e,0xd6,0xe8,0xdb,0xc2)
#endif
#endif

/*****************************************************************************
 * CMpeg4sDecMFT coclass
 */

DEFINE_GUID(CLSID_CMpeg4sDecMFT, 0x5686a0d9, 0xfe39, 0x409f, 0x9d,0xff, 0x3f,0xdb,0xc8,0x49,0xf9,0xf5);

#ifdef __cplusplus
class DECLSPEC_UUID("5686a0d9-fe39-409f-9dff-3fdbc849f9f5") CMpeg4sDecMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMpeg4sDecMFT, 0x5686a0d9, 0xfe39, 0x409f, 0x9d,0xff, 0x3f,0xdb,0xc8,0x49,0xf9,0xf5)
#endif
#endif

/*****************************************************************************
 * CZuneM4S2DecMediaObject coclass
 */

DEFINE_GUID(CLSID_CZuneM4S2DecMediaObject, 0xc56fc25c, 0x0fc6, 0x404a, 0x95,0x03, 0xb1,0x0b,0xf5,0x1a,0x8a,0xb9);

#ifdef __cplusplus
class DECLSPEC_UUID("c56fc25c-0fc6-404a-9503-b10bf51a8ab9") CZuneM4S2DecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CZuneM4S2DecMediaObject, 0xc56fc25c, 0x0fc6, 0x404a, 0x95,0x03, 0xb1,0x0b,0xf5,0x1a,0x8a,0xb9)
#endif
#endif

/*****************************************************************************
 * CMpeg4EncMediaObject coclass
 */

DEFINE_GUID(CLSID_CMpeg4EncMediaObject, 0x24f258d8, 0xc651, 0x4042, 0x93,0xe4, 0xca,0x65,0x4a,0xbb,0x68,0x2c);

#ifdef __cplusplus
class DECLSPEC_UUID("24f258d8-c651-4042-93e4-ca654abb682c") CMpeg4EncMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMpeg4EncMediaObject, 0x24f258d8, 0xc651, 0x4042, 0x93,0xe4, 0xca,0x65,0x4a,0xbb,0x68,0x2c)
#endif
#endif

/*****************************************************************************
 * CMpeg4sEncMediaObject coclass
 */

DEFINE_GUID(CLSID_CMpeg4sEncMediaObject, 0x6ec5a7be, 0xd81e, 0x4f9e, 0xad,0xa3, 0xcd,0x1b,0xf2,0x62,0xb6,0xd8);

#ifdef __cplusplus
class DECLSPEC_UUID("6ec5a7be-d81e-4f9e-ada3-cd1bf262b6d8") CMpeg4sEncMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMpeg4sEncMediaObject, 0x6ec5a7be, 0xd81e, 0x4f9e, 0xad,0xa3, 0xcd,0x1b,0xf2,0x62,0xb6,0xd8)
#endif
#endif

/*****************************************************************************
 * CMSSCDecMediaObject coclass
 */

DEFINE_GUID(CLSID_CMSSCDecMediaObject, 0x7bafb3b1, 0xd8f4, 0x4279, 0x92,0x53, 0x27,0xda,0x42,0x31,0x08,0xde);

#ifdef __cplusplus
class DECLSPEC_UUID("7bafb3b1-d8f4-4279-9253-27da423108de") CMSSCDecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSSCDecMediaObject, 0x7bafb3b1, 0xd8f4, 0x4279, 0x92,0x53, 0x27,0xda,0x42,0x31,0x08,0xde)
#endif
#endif

/*****************************************************************************
 * CMSSCEncMediaObject coclass
 */

DEFINE_GUID(CLSID_CMSSCEncMediaObject, 0x8cb9cc06, 0xd139, 0x4ae6, 0x8b,0xb4, 0x41,0xe6,0x12,0xe1,0x41,0xd5);

#ifdef __cplusplus
class DECLSPEC_UUID("8cb9cc06-d139-4ae6-8bb4-41e612e141d5") CMSSCEncMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSSCEncMediaObject, 0x8cb9cc06, 0xd139, 0x4ae6, 0x8b,0xb4, 0x41,0xe6,0x12,0xe1,0x41,0xd5)
#endif
#endif

/*****************************************************************************
 * CMSSCEncMediaObject2 coclass
 */

DEFINE_GUID(CLSID_CMSSCEncMediaObject2, 0xf7ffe0a0, 0xa4f5, 0x44b5, 0x94,0x9e, 0x15,0xed,0x2b,0xc6,0x6f,0x9d);

#ifdef __cplusplus
class DECLSPEC_UUID("f7ffe0a0-a4f5-44b5-949e-15ed2bc66f9d") CMSSCEncMediaObject2;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSSCEncMediaObject2, 0xf7ffe0a0, 0xa4f5, 0x44b5, 0x94,0x9e, 0x15,0xed,0x2b,0xc6,0x6f,0x9d)
#endif
#endif

/*****************************************************************************
 * CWMADecMediaObject coclass
 */

DEFINE_GUID(CLSID_CWMADecMediaObject, 0x2eeb4adf, 0x4578, 0x4d10, 0xbc,0xa7, 0xbb,0x95,0x5f,0x56,0x32,0x0a);

#ifdef __cplusplus
class DECLSPEC_UUID("2eeb4adf-4578-4d10-bca7-bb955f56320a") CWMADecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMADecMediaObject, 0x2eeb4adf, 0x4578, 0x4d10, 0xbc,0xa7, 0xbb,0x95,0x5f,0x56,0x32,0x0a)
#endif
#endif

/*****************************************************************************
 * CWMAEncMediaObject coclass
 */

DEFINE_GUID(CLSID_CWMAEncMediaObject, 0x70f598e9, 0xf4ab, 0x495a, 0x99,0xe2, 0xa7,0xc4,0xd3,0xd8,0x9a,0xbf);

#ifdef __cplusplus
class DECLSPEC_UUID("70f598e9-f4ab-495a-99e2-a7c4d3d89abf") CWMAEncMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMAEncMediaObject, 0x70f598e9, 0xf4ab, 0x495a, 0x99,0xe2, 0xa7,0xc4,0xd3,0xd8,0x9a,0xbf)
#endif
#endif

/*****************************************************************************
 * CWMATransMediaObject coclass
 */

DEFINE_GUID(CLSID_CWMATransMediaObject, 0xedcad9cb, 0x3127, 0x40df, 0xb5,0x27, 0x01,0x52,0xcc,0xb3,0xf6,0xf5);

#ifdef __cplusplus
class DECLSPEC_UUID("edcad9cb-3127-40df-b527-0152ccb3f6f5") CWMATransMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMATransMediaObject, 0xedcad9cb, 0x3127, 0x40df, 0xb5,0x27, 0x01,0x52,0xcc,0xb3,0xf6,0xf5)
#endif
#endif

/*****************************************************************************
 * CWMSPDecMediaObject coclass
 */

DEFINE_GUID(CLSID_CWMSPDecMediaObject, 0x874131cb, 0x4ecc, 0x443b, 0x89,0x48, 0x74,0x6b,0x89,0x59,0x5d,0x20);

#ifdef __cplusplus
class DECLSPEC_UUID("874131cb-4ecc-443b-8948-746b89595d20") CWMSPDecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMSPDecMediaObject, 0x874131cb, 0x4ecc, 0x443b, 0x89,0x48, 0x74,0x6b,0x89,0x59,0x5d,0x20)
#endif
#endif

/*****************************************************************************
 * CWMSPEncMediaObject coclass
 */

DEFINE_GUID(CLSID_CWMSPEncMediaObject, 0x67841b03, 0xc689, 0x4188, 0xad,0x3f, 0x4c,0x9e,0xbe,0xec,0x71,0x0b);

#ifdef __cplusplus
class DECLSPEC_UUID("67841b03-c689-4188-ad3f-4c9ebeec710b") CWMSPEncMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMSPEncMediaObject, 0x67841b03, 0xc689, 0x4188, 0xad,0x3f, 0x4c,0x9e,0xbe,0xec,0x71,0x0b)
#endif
#endif

/*****************************************************************************
 * CWMSPEncMediaObject2 coclass
 */

DEFINE_GUID(CLSID_CWMSPEncMediaObject2, 0x1f1f4e1a, 0x2252, 0x4063, 0x84,0xbb, 0xee,0xe7,0x5f,0x88,0x56,0xd5);

#ifdef __cplusplus
class DECLSPEC_UUID("1f1f4e1a-2252-4063-84bb-eee75f8856d5") CWMSPEncMediaObject2;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMSPEncMediaObject2, 0x1f1f4e1a, 0x2252, 0x4063, 0x84,0xbb, 0xee,0xe7,0x5f,0x88,0x56,0xd5)
#endif
#endif

/*****************************************************************************
 * CWMTDecMediaObject coclass
 */

DEFINE_GUID(CLSID_CWMTDecMediaObject, 0xf9dbc64e, 0x2dd0, 0x45dd, 0x9b,0x52, 0x66,0x64,0x2e,0xf9,0x44,0x31);

#ifdef __cplusplus
class DECLSPEC_UUID("f9dbc64e-2dd0-45dd-9b52-66642ef94431") CWMTDecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMTDecMediaObject, 0xf9dbc64e, 0x2dd0, 0x45dd, 0x9b,0x52, 0x66,0x64,0x2e,0xf9,0x44,0x31)
#endif
#endif

/*****************************************************************************
 * CWMTEncMediaObject coclass
 */

DEFINE_GUID(CLSID_CWMTEncMediaObject, 0x60b67652, 0xe46b, 0x4e44, 0x86,0x09, 0xf7,0x4b,0xff,0xdc,0x08,0x3c);

#ifdef __cplusplus
class DECLSPEC_UUID("60b67652-e46b-4e44-8609-f74bffdc083c") CWMTEncMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMTEncMediaObject, 0x60b67652, 0xe46b, 0x4e44, 0x86,0x09, 0xf7,0x4b,0xff,0xdc,0x08,0x3c)
#endif
#endif

/*****************************************************************************
 * CWMVDecMediaObject coclass
 */

DEFINE_GUID(CLSID_CWMVDecMediaObject, 0x82d353df, 0x90bd, 0x4382, 0x8b,0xc2, 0x3f,0x61,0x92,0xb7,0x6e,0x34);

#ifdef __cplusplus
class DECLSPEC_UUID("82d353df-90bd-4382-8bc2-3f6192b76e34") CWMVDecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMVDecMediaObject, 0x82d353df, 0x90bd, 0x4382, 0x8b,0xc2, 0x3f,0x61,0x92,0xb7,0x6e,0x34)
#endif
#endif

/*****************************************************************************
 * CWMVEncMediaObject2 coclass
 */

DEFINE_GUID(CLSID_CWMVEncMediaObject2, 0x96b57cdd, 0x8966, 0x410c, 0xbb,0x1f, 0xc9,0x7e,0xea,0x76,0x5c,0x04);

#ifdef __cplusplus
class DECLSPEC_UUID("96b57cdd-8966-410c-bb1f-c97eea765c04") CWMVEncMediaObject2;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMVEncMediaObject2, 0x96b57cdd, 0x8966, 0x410c, 0xbb,0x1f, 0xc9,0x7e,0xea,0x76,0x5c,0x04)
#endif
#endif

/*****************************************************************************
 * CWMVXEncMediaObject coclass
 */

DEFINE_GUID(CLSID_CWMVXEncMediaObject, 0x7e320092, 0x596a, 0x41b2, 0xbb,0xeb, 0x17,0x5d,0x10,0x50,0x4e,0xb6);

#ifdef __cplusplus
class DECLSPEC_UUID("7e320092-596a-41b2-bbeb-175d10504eb6") CWMVXEncMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMVXEncMediaObject, 0x7e320092, 0x596a, 0x41b2, 0xbb,0xeb, 0x17,0x5d,0x10,0x50,0x4e,0xb6)
#endif
#endif

/*****************************************************************************
 * CWMV9EncMediaObject coclass
 */

DEFINE_GUID(CLSID_CWMV9EncMediaObject, 0xd23b90d0, 0x144f, 0x46bd, 0x84,0x1d, 0x59,0xe4,0xeb,0x19,0xdc,0x59);

#ifdef __cplusplus
class DECLSPEC_UUID("d23b90d0-144f-46bd-841d-59e4eb19dc59") CWMV9EncMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMV9EncMediaObject, 0xd23b90d0, 0x144f, 0x46bd, 0x84,0x1d, 0x59,0xe4,0xeb,0x19,0xdc,0x59)
#endif
#endif

/*****************************************************************************
 * CWVC1DecMediaObject coclass
 */

DEFINE_GUID(CLSID_CWVC1DecMediaObject, 0xc9bfbccf, 0xe60e, 0x4588, 0xa3,0xdf, 0x5a,0x03,0xb1,0xfd,0x95,0x85);

#ifdef __cplusplus
class DECLSPEC_UUID("c9bfbccf-e60e-4588-a3df-5a03b1fd9585") CWVC1DecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWVC1DecMediaObject, 0xc9bfbccf, 0xe60e, 0x4588, 0xa3,0xdf, 0x5a,0x03,0xb1,0xfd,0x95,0x85)
#endif
#endif

/*****************************************************************************
 * CWVC1EncMediaObject coclass
 */

DEFINE_GUID(CLSID_CWVC1EncMediaObject, 0x44653d0d, 0x8cca, 0x41e7, 0xba,0xca, 0x88,0x43,0x37,0xb7,0x47,0xac);

#ifdef __cplusplus
class DECLSPEC_UUID("44653d0d-8cca-41e7-baca-884337b747ac") CWVC1EncMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWVC1EncMediaObject, 0x44653d0d, 0x8cca, 0x41e7, 0xba,0xca, 0x88,0x43,0x37,0xb7,0x47,0xac)
#endif
#endif

/*****************************************************************************
 * CDeColorConvMediaObject coclass
 */

DEFINE_GUID(CLSID_CDeColorConvMediaObject, 0x49034c05, 0xf43c, 0x400f, 0x84,0xc1, 0x90,0xa6,0x83,0x19,0x5a,0x3a);

#ifdef __cplusplus
class DECLSPEC_UUID("49034c05-f43c-400f-84c1-90a683195a3a") CDeColorConvMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CDeColorConvMediaObject, 0x49034c05, 0xf43c, 0x400f, 0x84,0xc1, 0x90,0xa6,0x83,0x19,0x5a,0x3a)
#endif
#endif

/*****************************************************************************
 * CDVDecoderMediaObject coclass
 */

DEFINE_GUID(CLSID_CDVDecoderMediaObject, 0xe54709c5, 0x1e17, 0x4c8d, 0x94,0xe7, 0x47,0x89,0x40,0x43,0x35,0x84);

#ifdef __cplusplus
class DECLSPEC_UUID("e54709c5-1e17-4c8d-94e7-************") CDVDecoderMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CDVDecoderMediaObject, 0xe54709c5, 0x1e17, 0x4c8d, 0x94,0xe7, 0x47,0x89,0x40,0x43,0x35,0x84)
#endif
#endif

/*****************************************************************************
 * CDVEncoderMediaObject coclass
 */

DEFINE_GUID(CLSID_CDVEncoderMediaObject, 0xc82ae729, 0xc327, 0x4cce, 0x91,0x4d, 0x81,0x71,0xfe,0xfe,0xbe,0xfb);

#ifdef __cplusplus
class DECLSPEC_UUID("c82ae729-c327-4cce-914d-8171fefebefb") CDVEncoderMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CDVEncoderMediaObject, 0xc82ae729, 0xc327, 0x4cce, 0x91,0x4d, 0x81,0x71,0xfe,0xfe,0xbe,0xfb)
#endif
#endif

/*****************************************************************************
 * CMpeg2DecMediaObject coclass
 */

DEFINE_GUID(CLSID_CMpeg2DecMediaObject, 0x863d66cd, 0xcdce, 0x4617, 0xb4,0x7f, 0xc8,0x92,0x9c,0xfc,0x28,0xa6);

#ifdef __cplusplus
class DECLSPEC_UUID("863d66cd-cdce-4617-b47f-c8929cfc28a6") CMpeg2DecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMpeg2DecMediaObject, 0x863d66cd, 0xcdce, 0x4617, 0xb4,0x7f, 0xc8,0x92,0x9c,0xfc,0x28,0xa6)
#endif
#endif

/*****************************************************************************
 * CPK_DS_MPEG2Decoder coclass
 */

DEFINE_GUID(CLSID_CPK_DS_MPEG2Decoder, 0x9910c5cd, 0x95c9, 0x4e06, 0x86,0x5a, 0xef,0xa1,0xc8,0x01,0x6b,0xf4);

#ifdef __cplusplus
class DECLSPEC_UUID("9910c5cd-95c9-4e06-865a-efa1c8016bf4") CPK_DS_MPEG2Decoder;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CPK_DS_MPEG2Decoder, 0x9910c5cd, 0x95c9, 0x4e06, 0x86,0x5a, 0xef,0xa1,0xc8,0x01,0x6b,0xf4)
#endif
#endif

/*****************************************************************************
 * CAC3DecMediaObject coclass
 */

DEFINE_GUID(CLSID_CAC3DecMediaObject, 0x03d7c802, 0xecfa, 0x47d9, 0xb2,0x68, 0x5f,0xb3,0xe3,0x10,0xde,0xe4);

#ifdef __cplusplus
class DECLSPEC_UUID("03d7c802-ecfa-47d9-b268-5fb3e310dee4") CAC3DecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CAC3DecMediaObject, 0x03d7c802, 0xecfa, 0x47d9, 0xb2,0x68, 0x5f,0xb3,0xe3,0x10,0xde,0xe4)
#endif
#endif

/*****************************************************************************
 * CPK_DS_AC3Decoder coclass
 */

DEFINE_GUID(CLSID_CPK_DS_AC3Decoder, 0x6c9c69d6, 0x0ffc, 0x4481, 0xaf,0xdb, 0xcd,0xf1,0xc7,0x9c,0x6f,0x3e);

#ifdef __cplusplus
class DECLSPEC_UUID("6c9c69d6-0ffc-4481-afdb-cdf1c79c6f3e") CPK_DS_AC3Decoder;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CPK_DS_AC3Decoder, 0x6c9c69d6, 0x0ffc, 0x4481, 0xaf,0xdb, 0xcd,0xf1,0xc7,0x9c,0x6f,0x3e)
#endif
#endif

/*****************************************************************************
 * CMP3DecMediaObject coclass
 */

DEFINE_GUID(CLSID_CMP3DecMediaObject, 0xbbeea841, 0x0a63, 0x4f52, 0xa7,0xab, 0xa9,0xb3,0xa8,0x4e,0xd3,0x8a);

#ifdef __cplusplus
class DECLSPEC_UUID("bbeea841-0a63-4f52-a7ab-a9b3a84ed38a") CMP3DecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMP3DecMediaObject, 0xbbeea841, 0x0a63, 0x4f52, 0xa7,0xab, 0xa9,0xb3,0xa8,0x4e,0xd3,0x8a)
#endif
#endif

/*****************************************************************************
 * CResamplerMediaObject coclass
 */

DEFINE_GUID(CLSID_CResamplerMediaObject, 0xf447b69e, 0x1884, 0x4a7e, 0x80,0x55, 0x34,0x6f,0x74,0xd6,0xed,0xb3);

#ifdef __cplusplus
class DECLSPEC_UUID("f447b69e-1884-4a7e-8055-346f74d6edb3") CResamplerMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CResamplerMediaObject, 0xf447b69e, 0x1884, 0x4a7e, 0x80,0x55, 0x34,0x6f,0x74,0xd6,0xed,0xb3)
#endif
#endif

/*****************************************************************************
 * CResizerMediaObject coclass
 */

DEFINE_GUID(CLSID_CResizerMediaObject, 0xd3ec8b8b, 0x7728, 0x4fd8, 0x9f,0xe0, 0x7b,0x67,0xd1,0x9f,0x73,0xa3);

#ifdef __cplusplus
class DECLSPEC_UUID("d3ec8b8b-7728-4fd8-9fe0-7b67d19f73a3") CResizerMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CResizerMediaObject, 0xd3ec8b8b, 0x7728, 0x4fd8, 0x9f,0xe0, 0x7b,0x67,0xd1,0x9f,0x73,0xa3)
#endif
#endif

/*****************************************************************************
 * CInterlaceMediaObject coclass
 */

DEFINE_GUID(CLSID_CInterlaceMediaObject, 0xb5a89c80, 0x4901, 0x407b, 0x9a,0xbc, 0x90,0xd9,0xa6,0x44,0xbb,0x46);

#ifdef __cplusplus
class DECLSPEC_UUID("b5a89c80-4901-407b-9abc-90d9a644bb46") CInterlaceMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CInterlaceMediaObject, 0xb5a89c80, 0x4901, 0x407b, 0x9a,0xbc, 0x90,0xd9,0xa6,0x44,0xbb,0x46)
#endif
#endif

/*****************************************************************************
 * CWMAudioLFXAPO coclass
 */

DEFINE_GUID(CLSID_CWMAudioLFXAPO, 0x62dc1a93, 0xae24, 0x464c, 0xa4,0x3e, 0x45,0x2f,0x82,0x4c,0x42,0x50);

#ifdef __cplusplus
class DECLSPEC_UUID("62dc1a93-ae24-464c-a43e-452f824c4250") CWMAudioLFXAPO;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMAudioLFXAPO, 0x62dc1a93, 0xae24, 0x464c, 0xa4,0x3e, 0x45,0x2f,0x82,0x4c,0x42,0x50)
#endif
#endif

/*****************************************************************************
 * CWMAudioGFXAPO coclass
 */

DEFINE_GUID(CLSID_CWMAudioGFXAPO, 0x637c490d, 0xeee3, 0x4c0a, 0x97,0x3f, 0x37,0x19,0x58,0x80,0x2d,0xa2);

#ifdef __cplusplus
class DECLSPEC_UUID("637c490d-eee3-4c0a-973f-371958802da2") CWMAudioGFXAPO;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMAudioGFXAPO, 0x637c490d, 0xeee3, 0x4c0a, 0x97,0x3f, 0x37,0x19,0x58,0x80,0x2d,0xa2)
#endif
#endif

/*****************************************************************************
 * CWMAudioSpdTxDMO coclass
 */

DEFINE_GUID(CLSID_CWMAudioSpdTxDMO, 0x5210f8e4, 0xb0bb, 0x47c3, 0xa8,0xd9, 0x7b,0x22,0x82,0xcc,0x79,0xed);

#ifdef __cplusplus
class DECLSPEC_UUID("5210f8e4-b0bb-47c3-a8d9-7b2282cc79ed") CWMAudioSpdTxDMO;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMAudioSpdTxDMO, 0x5210f8e4, 0xb0bb, 0x47c3, 0xa8,0xd9, 0x7b,0x22,0x82,0xcc,0x79,0xed)
#endif
#endif

/*****************************************************************************
 * CWMAudioAEC coclass
 */

DEFINE_GUID(CLSID_CWMAudioAEC, 0x745057c7, 0xf353, 0x4f2d, 0xa7,0xee, 0x58,0x43,0x44,0x77,0x73,0x0e);

#ifdef __cplusplus
class DECLSPEC_UUID("745057c7-f353-4f2d-a7ee-58434477730e") CWMAudioAEC;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CWMAudioAEC, 0x745057c7, 0xf353, 0x4f2d, 0xa7,0xee, 0x58,0x43,0x44,0x77,0x73,0x0e)
#endif
#endif

/*****************************************************************************
 * CClusterDetectorDmo coclass
 */

DEFINE_GUID(CLSID_CClusterDetectorDmo, 0x36e820c4, 0x165a, 0x4521, 0x86,0x3c, 0x61,0x9e,0x11,0x60,0xd4,0xd4);

#ifdef __cplusplus
class DECLSPEC_UUID("36e820c4-165a-4521-863c-619e1160d4d4") CClusterDetectorDmo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CClusterDetectorDmo, 0x36e820c4, 0x165a, 0x4521, 0x86,0x3c, 0x61,0x9e,0x11,0x60,0xd4,0xd4)
#endif
#endif

/*****************************************************************************
 * CColorControlDmo coclass
 */

DEFINE_GUID(CLSID_CColorControlDmo, 0x798059f0, 0x89ca, 0x4160, 0xb3,0x25, 0xae,0xb4,0x8e,0xfe,0x4f,0x9a);

#ifdef __cplusplus
class DECLSPEC_UUID("798059f0-89ca-4160-b325-aeb48efe4f9a") CColorControlDmo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CColorControlDmo, 0x798059f0, 0x89ca, 0x4160, 0xb3,0x25, 0xae,0xb4,0x8e,0xfe,0x4f,0x9a)
#endif
#endif

/*****************************************************************************
 * CColorConvertDMO coclass
 */

DEFINE_GUID(CLSID_CColorConvertDMO, 0x98230571, 0x0087, 0x4204, 0xb0,0x20, 0x32,0x82,0x53,0x8e,0x57,0xd3);

#ifdef __cplusplus
class DECLSPEC_UUID("*************-4204-b020-3282538e57d3") CColorConvertDMO;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CColorConvertDMO, 0x98230571, 0x0087, 0x4204, 0xb0,0x20, 0x32,0x82,0x53,0x8e,0x57,0xd3)
#endif
#endif

/*****************************************************************************
 * CColorLegalizerDmo coclass
 */

DEFINE_GUID(CLSID_CColorLegalizerDmo, 0xfdfaa753, 0xe48e, 0x4e33, 0x9c,0x74, 0x98,0xa2,0x7f,0xc6,0x72,0x6a);

#ifdef __cplusplus
class DECLSPEC_UUID("fdfaa753-e48e-4e33-9c74-98a27fc6726a") CColorLegalizerDmo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CColorLegalizerDmo, 0xfdfaa753, 0xe48e, 0x4e33, 0x9c,0x74, 0x98,0xa2,0x7f,0xc6,0x72,0x6a)
#endif
#endif

/*****************************************************************************
 * CFrameInterpDMO coclass
 */

DEFINE_GUID(CLSID_CFrameInterpDMO, 0x0a7cfe1b, 0x6ab5, 0x4334, 0x9e,0xd8, 0x3f,0x97,0xcb,0x37,0xda,0xa1);

#ifdef __cplusplus
class DECLSPEC_UUID("0a7cfe1b-6ab5-4334-9ed8-3f97cb37daa1") CFrameInterpDMO;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CFrameInterpDMO, 0x0a7cfe1b, 0x6ab5, 0x4334, 0x9e,0xd8, 0x3f,0x97,0xcb,0x37,0xda,0xa1)
#endif
#endif

/*****************************************************************************
 * CFrameRateConvertDmo coclass
 */

DEFINE_GUID(CLSID_CFrameRateConvertDmo, 0x01f36ce2, 0x0907, 0x4d8b, 0x97,0x9d, 0xf1,0x51,0xbe,0x91,0xc8,0x83);

#ifdef __cplusplus
class DECLSPEC_UUID("01f36ce2-0907-4d8b-979d-f151be91c883") CFrameRateConvertDmo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CFrameRateConvertDmo, 0x01f36ce2, 0x0907, 0x4d8b, 0x97,0x9d, 0xf1,0x51,0xbe,0x91,0xc8,0x83)
#endif
#endif

/*****************************************************************************
 * CResizerDMO coclass
 */

DEFINE_GUID(CLSID_CResizerDMO, 0x1ea1ea14, 0x48f4, 0x4054, 0xad,0x1a, 0xe8,0xae,0xe1,0x0a,0xc8,0x05);

#ifdef __cplusplus
class DECLSPEC_UUID("1ea1ea14-48f4-4054-ad1a-e8aee10ac805") CResizerDMO;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CResizerDMO, 0x1ea1ea14, 0x48f4, 0x4054, 0xad,0x1a, 0xe8,0xae,0xe1,0x0a,0xc8,0x05)
#endif
#endif

/*****************************************************************************
 * CShotDetectorDmo coclass
 */

DEFINE_GUID(CLSID_CShotDetectorDmo, 0x56aefacd, 0x110c, 0x4397, 0x92,0x92, 0xb0,0xa0,0xc6,0x1b,0x67,0x50);

#ifdef __cplusplus
class DECLSPEC_UUID("56aefacd-110c-4397-9292-b0a0c61b6750") CShotDetectorDmo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CShotDetectorDmo, 0x56aefacd, 0x110c, 0x4397, 0x92,0x92, 0xb0,0xa0,0xc6,0x1b,0x67,0x50)
#endif
#endif

/*****************************************************************************
 * CSmpteTransformsDmo coclass
 */

DEFINE_GUID(CLSID_CSmpteTransformsDmo, 0xbde6388b, 0xda25, 0x485d, 0xba,0x7f, 0xfa,0xbc,0x28,0xb2,0x03,0x18);

#ifdef __cplusplus
class DECLSPEC_UUID("bde6388b-da25-485d-ba7f-fabc28b20318") CSmpteTransformsDmo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CSmpteTransformsDmo, 0xbde6388b, 0xda25, 0x485d, 0xba,0x7f, 0xfa,0xbc,0x28,0xb2,0x03,0x18)
#endif
#endif

/*****************************************************************************
 * CThumbnailGeneratorDmo coclass
 */

DEFINE_GUID(CLSID_CThumbnailGeneratorDmo, 0x559c6bad, 0x1ea8, 0x4963, 0xa0,0x87, 0x8a,0x68,0x10,0xf9,0x21,0x8b);

#ifdef __cplusplus
class DECLSPEC_UUID("559c6bad-1ea8-4963-a087-8a6810f9218b") CThumbnailGeneratorDmo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CThumbnailGeneratorDmo, 0x559c6bad, 0x1ea8, 0x4963, 0xa0,0x87, 0x8a,0x68,0x10,0xf9,0x21,0x8b)
#endif
#endif

/*****************************************************************************
 * CTocGeneratorDmo coclass
 */

DEFINE_GUID(CLSID_CTocGeneratorDmo, 0x4dda1941, 0x77a0, 0x4fb1, 0xa5,0x18, 0xe2,0x18,0x50,0x41,0xd7,0x0c);

#ifdef __cplusplus
class DECLSPEC_UUID("4dda1941-77a0-4fb1-a518-e2185041d70c") CTocGeneratorDmo;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CTocGeneratorDmo, 0x4dda1941, 0x77a0, 0x4fb1, 0xa5,0x18, 0xe2,0x18,0x50,0x41,0xd7,0x0c)
#endif
#endif

/*****************************************************************************
 * CMPEGAACDecMediaObject coclass
 */

DEFINE_GUID(CLSID_CMPEGAACDecMediaObject, 0x8dde1772, 0xedad, 0x41c3, 0xb4,0xbe, 0x1f,0x30,0xfb,0x4e,0xe0,0xd6);

#ifdef __cplusplus
class DECLSPEC_UUID("8dde1772-edad-41c3-b4be-1f30fb4ee0d6") CMPEGAACDecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMPEGAACDecMediaObject, 0x8dde1772, 0xedad, 0x41c3, 0xb4,0xbe, 0x1f,0x30,0xfb,0x4e,0xe0,0xd6)
#endif
#endif

/*****************************************************************************
 * CNokiaAACDecMediaObject coclass
 */

DEFINE_GUID(CLSID_CNokiaAACDecMediaObject, 0x3cb2bde4, 0x4e29, 0x4c44, 0xa7,0x3e, 0x2d,0x7c,0x2c,0x46,0xd6,0xec);

#ifdef __cplusplus
class DECLSPEC_UUID("3cb2bde4-4e29-4c44-a73e-2d7c2c46d6ec") CNokiaAACDecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CNokiaAACDecMediaObject, 0x3cb2bde4, 0x4e29, 0x4c44, 0xa7,0x3e, 0x2d,0x7c,0x2c,0x46,0xd6,0xec)
#endif
#endif

/*****************************************************************************
 * CVodafoneAACDecMediaObject coclass
 */

DEFINE_GUID(CLSID_CVodafoneAACDecMediaObject, 0x7f36f942, 0xdcf3, 0x4d82, 0x92,0x89, 0x5b,0x18,0x20,0x27,0x8f,0x7c);

#ifdef __cplusplus
class DECLSPEC_UUID("7f36f942-dcf3-4d82-9289-5b1820278f7c") CVodafoneAACDecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CVodafoneAACDecMediaObject, 0x7f36f942, 0xdcf3, 0x4d82, 0x92,0x89, 0x5b,0x18,0x20,0x27,0x8f,0x7c)
#endif
#endif

/*****************************************************************************
 * CZuneAACCCDecMediaObject coclass
 */

DEFINE_GUID(CLSID_CZuneAACCCDecMediaObject, 0xa74e98f2, 0x52d6, 0x4b4e, 0x88,0x5b, 0xe0,0xa6,0xca,0x4f,0x18,0x7a);

#ifdef __cplusplus
class DECLSPEC_UUID("a74e98f2-52d6-4b4e-885b-e0a6ca4f187a") CZuneAACCCDecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CZuneAACCCDecMediaObject, 0xa74e98f2, 0x52d6, 0x4b4e, 0x88,0x5b, 0xe0,0xa6,0xca,0x4f,0x18,0x7a)
#endif
#endif

/*****************************************************************************
 * CNokiaAACCCDecMediaObject coclass
 */

DEFINE_GUID(CLSID_CNokiaAACCCDecMediaObject, 0xeabf7a6f, 0xccba, 0x4d60, 0x86,0x20, 0xb1,0x52,0xcc,0x97,0x72,0x63);

#ifdef __cplusplus
class DECLSPEC_UUID("eabf7a6f-ccba-4d60-8620-b152cc977263") CNokiaAACCCDecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CNokiaAACCCDecMediaObject, 0xeabf7a6f, 0xccba, 0x4d60, 0x86,0x20, 0xb1,0x52,0xcc,0x97,0x72,0x63)
#endif
#endif

/*****************************************************************************
 * CVodafoneAACCCDecMediaObject coclass
 */

DEFINE_GUID(CLSID_CVodafoneAACCCDecMediaObject, 0x7e76bf7f, 0xc993, 0x4e26, 0x8f,0xab, 0x47,0x0a,0x70,0xc0,0xd5,0x9c);

#ifdef __cplusplus
class DECLSPEC_UUID("7e76bf7f-c993-4e26-8fab-470a70c0d59c") CVodafoneAACCCDecMediaObject;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CVodafoneAACCCDecMediaObject, 0x7e76bf7f, 0xc993, 0x4e26, 0x8f,0xab, 0x47,0x0a,0x70,0xc0,0xd5,0x9c)
#endif
#endif

/*****************************************************************************
 * CMPEG2EncoderDS coclass
 */

DEFINE_GUID(CLSID_CMPEG2EncoderDS, 0x5f5aff4a, 0x2f7f, 0x4279, 0x88,0xc2, 0xcd,0x88,0xeb,0x39,0xd1,0x44);

#ifdef __cplusplus
class DECLSPEC_UUID("5f5aff4a-2f7f-4279-88c2-cd88eb39d144") CMPEG2EncoderDS;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMPEG2EncoderDS, 0x5f5aff4a, 0x2f7f, 0x4279, 0x88,0xc2, 0xcd,0x88,0xeb,0x39,0xd1,0x44)
#endif
#endif

/*****************************************************************************
 * CMPEG2EncoderVideoDS coclass
 */

DEFINE_GUID(CLSID_CMPEG2EncoderVideoDS, 0x42150cd9, 0xca9a, 0x4ea5, 0x99,0x39, 0x30,0xee,0x03,0x7f,0x6e,0x74);

#ifdef __cplusplus
class DECLSPEC_UUID("42150cd9-ca9a-4ea5-9939-30ee037f6e74") CMPEG2EncoderVideoDS;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMPEG2EncoderVideoDS, 0x42150cd9, 0xca9a, 0x4ea5, 0x99,0x39, 0x30,0xee,0x03,0x7f,0x6e,0x74)
#endif
#endif

/*****************************************************************************
 * CMPEG2EncoderAudioDS coclass
 */

DEFINE_GUID(CLSID_CMPEG2EncoderAudioDS, 0xacd453bc, 0xc58a, 0x44d1, 0xbb,0xf5, 0xbf,0xb3,0x25,0xbe,0x2d,0x78);

#ifdef __cplusplus
class DECLSPEC_UUID("acd453bc-c58a-44d1-bbf5-bfb325be2d78") CMPEG2EncoderAudioDS;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMPEG2EncoderAudioDS, 0xacd453bc, 0xc58a, 0x44d1, 0xbb,0xf5, 0xbf,0xb3,0x25,0xbe,0x2d,0x78)
#endif
#endif

/*****************************************************************************
 * CMPEG2AudDecoderDS coclass
 */

DEFINE_GUID(CLSID_CMPEG2AudDecoderDS, 0xe1f1a0b8, 0xbeee, 0x490d, 0xba,0x7c, 0x06,0x6c,0x40,0xb5,0xe2,0xb9);

#ifdef __cplusplus
class DECLSPEC_UUID("e1f1a0b8-beee-490d-ba7c-066c40b5e2b9") CMPEG2AudDecoderDS;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMPEG2AudDecoderDS, 0xe1f1a0b8, 0xbeee, 0x490d, 0xba,0x7c, 0x06,0x6c,0x40,0xb5,0xe2,0xb9)
#endif
#endif

/*****************************************************************************
 * CMPEG2VidDecoderDS coclass
 */

DEFINE_GUID(CLSID_CMPEG2VidDecoderDS, 0x212690fb, 0x83e5, 0x4526, 0x8f,0xd7, 0x74,0x47,0x8b,0x79,0x39,0xcd);

#ifdef __cplusplus
class DECLSPEC_UUID("212690fb-83e5-4526-8fd7-74478b7939cd") CMPEG2VidDecoderDS;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMPEG2VidDecoderDS, 0x212690fb, 0x83e5, 0x4526, 0x8f,0xd7, 0x74,0x47,0x8b,0x79,0x39,0xcd)
#endif
#endif

/*****************************************************************************
 * CDTVAudDecoderDS coclass
 */

DEFINE_GUID(CLSID_CDTVAudDecoderDS, 0x8e269032, 0xfe03, 0x4753, 0x9b,0x17, 0x18,0x25,0x3c,0x21,0x72,0x2e);

#ifdef __cplusplus
class DECLSPEC_UUID("8e269032-fe03-4753-9b17-18253c21722e") CDTVAudDecoderDS;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CDTVAudDecoderDS, 0x8e269032, 0xfe03, 0x4753, 0x9b,0x17, 0x18,0x25,0x3c,0x21,0x72,0x2e)
#endif
#endif

/*****************************************************************************
 * CDTVVidDecoderDS coclass
 */

DEFINE_GUID(CLSID_CDTVVidDecoderDS, 0x64777dc8, 0x4e24, 0x4beb, 0x9d,0x19, 0x60,0xa3,0x5b,0xe1,0xda,0xaf);

#ifdef __cplusplus
class DECLSPEC_UUID("64777dc8-4e24-4beb-9d19-60a35be1daaf") CDTVVidDecoderDS;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CDTVVidDecoderDS, 0x64777dc8, 0x4e24, 0x4beb, 0x9d,0x19, 0x60,0xa3,0x5b,0xe1,0xda,0xaf)
#endif
#endif

/*****************************************************************************
 * CMSAC3Enc coclass
 */

DEFINE_GUID(CLSID_CMSAC3Enc, 0xc6b400e2, 0x20a7, 0x4e58, 0xa2,0xfe, 0x24,0x61,0x96,0x82,0xce,0x6c);

#ifdef __cplusplus
class DECLSPEC_UUID("c6b400e2-20a7-4e58-a2fe-24619682ce6c") CMSAC3Enc;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSAC3Enc, 0xc6b400e2, 0x20a7, 0x4e58, 0xa2,0xfe, 0x24,0x61,0x96,0x82,0xce,0x6c)
#endif
#endif

/*****************************************************************************
 * CMSH264DecoderMFT coclass
 */

DEFINE_GUID(CLSID_CMSH264DecoderMFT, 0x62ce7e72, 0x4c71, 0x4d20, 0xb1,0x5d, 0x45,0x28,0x31,0xa8,0x7d,0x9d);

#ifdef __cplusplus
class DECLSPEC_UUID("62ce7e72-4c71-4d20-b15d-452831a87d9d") CMSH264DecoderMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSH264DecoderMFT, 0x62ce7e72, 0x4c71, 0x4d20, 0xb1,0x5d, 0x45,0x28,0x31,0xa8,0x7d,0x9d)
#endif
#endif

/*****************************************************************************
 * CMSH264EncoderMFT coclass
 */

DEFINE_GUID(CLSID_CMSH264EncoderMFT, 0x6ca50344, 0x051a, 0x4ded, 0x97,0x79, 0xa4,0x33,0x05,0x16,0x5e,0x35);

#ifdef __cplusplus
class DECLSPEC_UUID("6ca50344-051a-4ded-9779-a43305165e35") CMSH264EncoderMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSH264EncoderMFT, 0x6ca50344, 0x051a, 0x4ded, 0x97,0x79, 0xa4,0x33,0x05,0x16,0x5e,0x35)
#endif
#endif

/*****************************************************************************
 * CMSH264RemuxMFT coclass
 */

DEFINE_GUID(CLSID_CMSH264RemuxMFT, 0x05a47ebb, 0x8bf0, 0x4cbf, 0xad,0x2f, 0x3b,0x71,0xd7,0x58,0x66,0xf5);

#ifdef __cplusplus
class DECLSPEC_UUID("05a47ebb-8bf0-4cbf-ad2f-3b71d75866f5") CMSH264RemuxMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSH264RemuxMFT, 0x05a47ebb, 0x8bf0, 0x4cbf, 0xad,0x2f, 0x3b,0x71,0xd7,0x58,0x66,0xf5)
#endif
#endif

/*****************************************************************************
 * CMSAACDecMFT coclass
 */

DEFINE_GUID(CLSID_CMSAACDecMFT, 0x32d186a7, 0x218f, 0x4c75, 0x88,0x76, 0xdd,0x77,0x27,0x3a,0x89,0x99);

#ifdef __cplusplus
class DECLSPEC_UUID("32d186a7-218f-4c75-8876-dd77273a8999") CMSAACDecMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSAACDecMFT, 0x32d186a7, 0x218f, 0x4c75, 0x88,0x76, 0xdd,0x77,0x27,0x3a,0x89,0x99)
#endif
#endif

/*****************************************************************************
 * AACMFTEncoder coclass
 */

DEFINE_GUID(CLSID_AACMFTEncoder, 0x93af0c51, 0x2275, 0x45d2, 0xa3,0x5b, 0xf2,0xba,0x21,0xca,0xed,0x00);

#ifdef __cplusplus
class DECLSPEC_UUID("93af0c51-2275-45d2-a35b-f2ba21caed00") AACMFTEncoder;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(AACMFTEncoder, 0x93af0c51, 0x2275, 0x45d2, 0xa3,0x5b, 0xf2,0xba,0x21,0xca,0xed,0x00)
#endif
#endif

/*****************************************************************************
 * CMSDDPlusDecMFT coclass
 */

DEFINE_GUID(CLSID_CMSDDPlusDecMFT, 0x177c0afe, 0x900b, 0x48d4, 0x9e,0x4c, 0x57,0xad,0xd2,0x50,0xb3,0xd4);

#ifdef __cplusplus
class DECLSPEC_UUID("177c0afe-900b-48d4-9e4c-57add250b3d4") CMSDDPlusDecMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSDDPlusDecMFT, 0x177c0afe, 0x900b, 0x48d4, 0x9e,0x4c, 0x57,0xad,0xd2,0x50,0xb3,0xd4)
#endif
#endif

/*****************************************************************************
 * CMPEG2VideoEncoderMFT coclass
 */

DEFINE_GUID(CLSID_CMPEG2VideoEncoderMFT, 0xe6335f02, 0x80b7, 0x4dc4, 0xad,0xfa, 0xdf,0xe7,0x21,0x0d,0x20,0xd5);

#ifdef __cplusplus
class DECLSPEC_UUID("e6335f02-80b7-4dc4-adfa-dfe7210d20d5") CMPEG2VideoEncoderMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMPEG2VideoEncoderMFT, 0xe6335f02, 0x80b7, 0x4dc4, 0xad,0xfa, 0xdf,0xe7,0x21,0x0d,0x20,0xd5)
#endif
#endif

/*****************************************************************************
 * CMPEG2AudioEncoderMFT coclass
 */

DEFINE_GUID(CLSID_CMPEG2AudioEncoderMFT, 0x46a4dd5c, 0x73f8, 0x4304, 0x94,0xdf, 0x30,0x8f,0x76,0x09,0x74,0xf4);

#ifdef __cplusplus
class DECLSPEC_UUID("46a4dd5c-73f8-4304-94df-308f760974f4") CMPEG2AudioEncoderMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMPEG2AudioEncoderMFT, 0x46a4dd5c, 0x73f8, 0x4304, 0x94,0xdf, 0x30,0x8f,0x76,0x09,0x74,0xf4)
#endif
#endif

/*****************************************************************************
 * CMSMPEGDecoderMFT coclass
 */

DEFINE_GUID(CLSID_CMSMPEGDecoderMFT, 0x2d709e52, 0x123f, 0x49b5, 0x9c,0xbc, 0x9a,0xf5,0xcd,0xe2,0x8f,0xb9);

#ifdef __cplusplus
class DECLSPEC_UUID("2d709e52-123f-49b5-9cbc-9af5cde28fb9") CMSMPEGDecoderMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSMPEGDecoderMFT, 0x2d709e52, 0x123f, 0x49b5, 0x9c,0xbc, 0x9a,0xf5,0xcd,0xe2,0x8f,0xb9)
#endif
#endif

/*****************************************************************************
 * CMSMPEGAudDecMFT coclass
 */

DEFINE_GUID(CLSID_CMSMPEGAudDecMFT, 0x70707b39, 0xb2ca, 0x4015, 0xab,0xea, 0xf8,0x44,0x7d,0x22,0xd8,0x8b);

#ifdef __cplusplus
class DECLSPEC_UUID("70707b39-b2ca-4015-abea-f8447d22d88b") CMSMPEGAudDecMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSMPEGAudDecMFT, 0x70707b39, 0xb2ca, 0x4015, 0xab,0xea, 0xf8,0x44,0x7d,0x22,0xd8,0x8b)
#endif
#endif

/*****************************************************************************
 * CMSDolbyDigitalEncMFT coclass
 */

DEFINE_GUID(CLSID_CMSDolbyDigitalEncMFT, 0xac3315c9, 0xf481, 0x45d7, 0x82,0x6c, 0x0b,0x40,0x6c,0x1f,0x64,0xb8);

#ifdef __cplusplus
class DECLSPEC_UUID("ac3315c9-f481-45d7-826c-0b406c1f64b8") CMSDolbyDigitalEncMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSDolbyDigitalEncMFT, 0xac3315c9, 0xf481, 0x45d7, 0x82,0x6c, 0x0b,0x40,0x6c,0x1f,0x64,0xb8)
#endif
#endif

/*****************************************************************************
 * MP3ACMCodecWrapper coclass
 */

DEFINE_GUID(CLSID_MP3ACMCodecWrapper, 0x11103421, 0x354c, 0x4cca, 0xa7,0xa3, 0x1a,0xff,0x9a,0x5b,0x67,0x01);

#ifdef __cplusplus
class DECLSPEC_UUID("11103421-354c-4cca-a7a3-1aff9a5b6701") MP3ACMCodecWrapper;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(MP3ACMCodecWrapper, 0x11103421, 0x354c, 0x4cca, 0xa7,0xa3, 0x1a,0xff,0x9a,0x5b,0x67,0x01)
#endif
#endif

/*****************************************************************************
 * CMSVideoDSPMFT coclass
 */

DEFINE_GUID(CLSID_CMSVideoDSPMFT, 0x51571744, 0x7fe4, 0x4ff2, 0xa4,0x98, 0x2d,0xc3,0x4f,0xf7,0x4f,0x1b);

#ifdef __cplusplus
class DECLSPEC_UUID("51571744-7fe4-4ff2-a498-2dc34ff74f1b") CMSVideoDSPMFT;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(CMSVideoDSPMFT, 0x51571744, 0x7fe4, 0x4ff2, 0xa4,0x98, 0x2d,0xc3,0x4f,0xf7,0x4f,0x1b)
#endif
#endif

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __wmcodecdsp_h__ */
