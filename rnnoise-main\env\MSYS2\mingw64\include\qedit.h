/*** Autogenerated by WIDL 10.12 from include/qedit.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __qedit_h__
#define __qedit_h__

/* Forward declarations */

#ifndef __ISampleGrabberCB_FWD_DEFINED__
#define __ISampleGrabberCB_FWD_DEFINED__
typedef interface ISampleGrabberCB ISampleGrabberCB;
#ifdef __cplusplus
interface ISampleGrabberCB;
#endif /* __cplusplus */
#endif

#ifndef __ISampleGrabber_FWD_DEFINED__
#define __ISampleGrabber_FWD_DEFINED__
typedef interface ISampleGrabber ISampleGrabber;
#ifdef __cplusplus
interface ISampleGrabber;
#endif /* __cplusplus */
#endif

#ifndef __SampleGrabber_FWD_DEFINED__
#define __SampleGrabber_FWD_DEFINED__
#ifdef __cplusplus
typedef class SampleGrabber SampleGrabber;
#else
typedef struct SampleGrabber SampleGrabber;
#endif /* defined __cplusplus */
#endif /* defined __SampleGrabber_FWD_DEFINED__ */

#ifndef __IMediaDet_FWD_DEFINED__
#define __IMediaDet_FWD_DEFINED__
typedef interface IMediaDet IMediaDet;
#ifdef __cplusplus
interface IMediaDet;
#endif /* __cplusplus */
#endif

#ifndef __MediaDet_FWD_DEFINED__
#define __MediaDet_FWD_DEFINED__
#ifdef __cplusplus
typedef class MediaDet MediaDet;
#else
typedef struct MediaDet MediaDet;
#endif /* defined __cplusplus */
#endif /* defined __MediaDet_FWD_DEFINED__ */

#ifndef __IMediaLocator_FWD_DEFINED__
#define __IMediaLocator_FWD_DEFINED__
typedef interface IMediaLocator IMediaLocator;
#ifdef __cplusplus
interface IMediaLocator;
#endif /* __cplusplus */
#endif

#ifndef __IPropertySetter_FWD_DEFINED__
#define __IPropertySetter_FWD_DEFINED__
typedef interface IPropertySetter IPropertySetter;
#ifdef __cplusplus
interface IPropertySetter;
#endif /* __cplusplus */
#endif

#ifndef __IAMErrorLog_FWD_DEFINED__
#define __IAMErrorLog_FWD_DEFINED__
typedef interface IAMErrorLog IAMErrorLog;
#ifdef __cplusplus
interface IAMErrorLog;
#endif /* __cplusplus */
#endif

#ifndef __IAMSetErrorLog_FWD_DEFINED__
#define __IAMSetErrorLog_FWD_DEFINED__
typedef interface IAMSetErrorLog IAMSetErrorLog;
#ifdef __cplusplus
interface IAMSetErrorLog;
#endif /* __cplusplus */
#endif

#ifndef __IAMTimeline_FWD_DEFINED__
#define __IAMTimeline_FWD_DEFINED__
typedef interface IAMTimeline IAMTimeline;
#ifdef __cplusplus
interface IAMTimeline;
#endif /* __cplusplus */
#endif

#ifndef __AMTimeline_FWD_DEFINED__
#define __AMTimeline_FWD_DEFINED__
#ifdef __cplusplus
typedef class AMTimeline AMTimeline;
#else
typedef struct AMTimeline AMTimeline;
#endif /* defined __cplusplus */
#endif /* defined __AMTimeline_FWD_DEFINED__ */

#ifndef __IAMTimelineGroup_FWD_DEFINED__
#define __IAMTimelineGroup_FWD_DEFINED__
typedef interface IAMTimelineGroup IAMTimelineGroup;
#ifdef __cplusplus
interface IAMTimelineGroup;
#endif /* __cplusplus */
#endif

#ifndef __IAMTimelineObj_FWD_DEFINED__
#define __IAMTimelineObj_FWD_DEFINED__
typedef interface IAMTimelineObj IAMTimelineObj;
#ifdef __cplusplus
interface IAMTimelineObj;
#endif /* __cplusplus */
#endif

#ifndef __IAMTimelineSrc_FWD_DEFINED__
#define __IAMTimelineSrc_FWD_DEFINED__
typedef interface IAMTimelineSrc IAMTimelineSrc;
#ifdef __cplusplus
interface IAMTimelineSrc;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <amstream.h>
#include <msxml.h>

#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************************
 * ISampleGrabberCB interface
 */
#ifndef __ISampleGrabberCB_INTERFACE_DEFINED__
#define __ISampleGrabberCB_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISampleGrabberCB, 0x0579154a, 0x2b53, 0x4994, 0xb0,0xd0, 0xe7,0x73,0x14,0x8e,0xff,0x85);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0579154a-2b53-4994-b0d0-e773148eff85")
ISampleGrabberCB : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SampleCB(
        double SampleTime,
        IMediaSample *pSample) = 0;

    virtual HRESULT STDMETHODCALLTYPE BufferCB(
        double SampleTime,
        BYTE *pBuffer,
        LONG BufferLen) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISampleGrabberCB, 0x0579154a, 0x2b53, 0x4994, 0xb0,0xd0, 0xe7,0x73,0x14,0x8e,0xff,0x85)
#endif
#else
typedef struct ISampleGrabberCBVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISampleGrabberCB *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISampleGrabberCB *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISampleGrabberCB *This);

    /*** ISampleGrabberCB methods ***/
    HRESULT (STDMETHODCALLTYPE *SampleCB)(
        ISampleGrabberCB *This,
        double SampleTime,
        IMediaSample *pSample);

    HRESULT (STDMETHODCALLTYPE *BufferCB)(
        ISampleGrabberCB *This,
        double SampleTime,
        BYTE *pBuffer,
        LONG BufferLen);

    END_INTERFACE
} ISampleGrabberCBVtbl;

interface ISampleGrabberCB {
    CONST_VTBL ISampleGrabberCBVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISampleGrabberCB_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISampleGrabberCB_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISampleGrabberCB_Release(This) (This)->lpVtbl->Release(This)
/*** ISampleGrabberCB methods ***/
#define ISampleGrabberCB_SampleCB(This,SampleTime,pSample) (This)->lpVtbl->SampleCB(This,SampleTime,pSample)
#define ISampleGrabberCB_BufferCB(This,SampleTime,pBuffer,BufferLen) (This)->lpVtbl->BufferCB(This,SampleTime,pBuffer,BufferLen)
#else
/*** IUnknown methods ***/
static inline HRESULT ISampleGrabberCB_QueryInterface(ISampleGrabberCB* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISampleGrabberCB_AddRef(ISampleGrabberCB* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISampleGrabberCB_Release(ISampleGrabberCB* This) {
    return This->lpVtbl->Release(This);
}
/*** ISampleGrabberCB methods ***/
static inline HRESULT ISampleGrabberCB_SampleCB(ISampleGrabberCB* This,double SampleTime,IMediaSample *pSample) {
    return This->lpVtbl->SampleCB(This,SampleTime,pSample);
}
static inline HRESULT ISampleGrabberCB_BufferCB(ISampleGrabberCB* This,double SampleTime,BYTE *pBuffer,LONG BufferLen) {
    return This->lpVtbl->BufferCB(This,SampleTime,pBuffer,BufferLen);
}
#endif
#endif

#endif


#endif  /* __ISampleGrabberCB_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ISampleGrabber interface
 */
#ifndef __ISampleGrabber_INTERFACE_DEFINED__
#define __ISampleGrabber_INTERFACE_DEFINED__

DEFINE_GUID(IID_ISampleGrabber, 0x6b652fff, 0x11fe, 0x4fce, 0x92,0xad, 0x02,0x66,0xb5,0xd7,0xc7,0x8f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("6b652fff-11fe-4fce-92ad-0266b5d7c78f")
ISampleGrabber : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetOneShot(
        WINBOOL OneShot) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaType(
        const AM_MEDIA_TYPE *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetConnectedMediaType(
        AM_MEDIA_TYPE *pType) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetBufferSamples(
        WINBOOL BufferThem) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentBuffer(
        LONG *pBufferSize,
        LONG *pBuffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentSample(
        IMediaSample **ppSample) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetCallback(
        ISampleGrabberCB *pCallback,
        LONG WhichMethodToCallback) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ISampleGrabber, 0x6b652fff, 0x11fe, 0x4fce, 0x92,0xad, 0x02,0x66,0xb5,0xd7,0xc7,0x8f)
#endif
#else
typedef struct ISampleGrabberVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ISampleGrabber *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ISampleGrabber *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ISampleGrabber *This);

    /*** ISampleGrabber methods ***/
    HRESULT (STDMETHODCALLTYPE *SetOneShot)(
        ISampleGrabber *This,
        WINBOOL OneShot);

    HRESULT (STDMETHODCALLTYPE *SetMediaType)(
        ISampleGrabber *This,
        const AM_MEDIA_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *GetConnectedMediaType)(
        ISampleGrabber *This,
        AM_MEDIA_TYPE *pType);

    HRESULT (STDMETHODCALLTYPE *SetBufferSamples)(
        ISampleGrabber *This,
        WINBOOL BufferThem);

    HRESULT (STDMETHODCALLTYPE *GetCurrentBuffer)(
        ISampleGrabber *This,
        LONG *pBufferSize,
        LONG *pBuffer);

    HRESULT (STDMETHODCALLTYPE *GetCurrentSample)(
        ISampleGrabber *This,
        IMediaSample **ppSample);

    HRESULT (STDMETHODCALLTYPE *SetCallback)(
        ISampleGrabber *This,
        ISampleGrabberCB *pCallback,
        LONG WhichMethodToCallback);

    END_INTERFACE
} ISampleGrabberVtbl;

interface ISampleGrabber {
    CONST_VTBL ISampleGrabberVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ISampleGrabber_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ISampleGrabber_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ISampleGrabber_Release(This) (This)->lpVtbl->Release(This)
/*** ISampleGrabber methods ***/
#define ISampleGrabber_SetOneShot(This,OneShot) (This)->lpVtbl->SetOneShot(This,OneShot)
#define ISampleGrabber_SetMediaType(This,pType) (This)->lpVtbl->SetMediaType(This,pType)
#define ISampleGrabber_GetConnectedMediaType(This,pType) (This)->lpVtbl->GetConnectedMediaType(This,pType)
#define ISampleGrabber_SetBufferSamples(This,BufferThem) (This)->lpVtbl->SetBufferSamples(This,BufferThem)
#define ISampleGrabber_GetCurrentBuffer(This,pBufferSize,pBuffer) (This)->lpVtbl->GetCurrentBuffer(This,pBufferSize,pBuffer)
#define ISampleGrabber_GetCurrentSample(This,ppSample) (This)->lpVtbl->GetCurrentSample(This,ppSample)
#define ISampleGrabber_SetCallback(This,pCallback,WhichMethodToCallback) (This)->lpVtbl->SetCallback(This,pCallback,WhichMethodToCallback)
#else
/*** IUnknown methods ***/
static inline HRESULT ISampleGrabber_QueryInterface(ISampleGrabber* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ISampleGrabber_AddRef(ISampleGrabber* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ISampleGrabber_Release(ISampleGrabber* This) {
    return This->lpVtbl->Release(This);
}
/*** ISampleGrabber methods ***/
static inline HRESULT ISampleGrabber_SetOneShot(ISampleGrabber* This,WINBOOL OneShot) {
    return This->lpVtbl->SetOneShot(This,OneShot);
}
static inline HRESULT ISampleGrabber_SetMediaType(ISampleGrabber* This,const AM_MEDIA_TYPE *pType) {
    return This->lpVtbl->SetMediaType(This,pType);
}
static inline HRESULT ISampleGrabber_GetConnectedMediaType(ISampleGrabber* This,AM_MEDIA_TYPE *pType) {
    return This->lpVtbl->GetConnectedMediaType(This,pType);
}
static inline HRESULT ISampleGrabber_SetBufferSamples(ISampleGrabber* This,WINBOOL BufferThem) {
    return This->lpVtbl->SetBufferSamples(This,BufferThem);
}
static inline HRESULT ISampleGrabber_GetCurrentBuffer(ISampleGrabber* This,LONG *pBufferSize,LONG *pBuffer) {
    return This->lpVtbl->GetCurrentBuffer(This,pBufferSize,pBuffer);
}
static inline HRESULT ISampleGrabber_GetCurrentSample(ISampleGrabber* This,IMediaSample **ppSample) {
    return This->lpVtbl->GetCurrentSample(This,ppSample);
}
static inline HRESULT ISampleGrabber_SetCallback(ISampleGrabber* This,ISampleGrabberCB *pCallback,LONG WhichMethodToCallback) {
    return This->lpVtbl->SetCallback(This,pCallback,WhichMethodToCallback);
}
#endif
#endif

#endif


#endif  /* __ISampleGrabber_INTERFACE_DEFINED__ */

/*****************************************************************************
 * SampleGrabber coclass
 */

DEFINE_GUID(CLSID_SampleGrabber, 0xc1f400a0, 0x3f08, 0x11d3, 0x9f,0x0b, 0x00,0x60,0x08,0x03,0x9e,0x37);

#ifdef __cplusplus
class DECLSPEC_UUID("c1f400a0-3f08-11d3-9f0b-006008039e37") SampleGrabber;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(SampleGrabber, 0xc1f400a0, 0x3f08, 0x11d3, 0x9f,0x0b, 0x00,0x60,0x08,0x03,0x9e,0x37)
#endif
#endif

/*****************************************************************************
 * IMediaDet interface
 */
#ifndef __IMediaDet_INTERFACE_DEFINED__
#define __IMediaDet_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMediaDet, 0x65bd0710, 0x24d2, 0x4ff7, 0x93,0x24, 0xed,0x2e,0x5d,0x3a,0xba,0xfa);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("65bd0710-24d2-4ff7-9324-ed2e5d3abafa")
IMediaDet : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_Filter(
        IUnknown **pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Filter(
        IUnknown *newVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_OutputStreams(
        LONG *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_CurrentStream(
        LONG *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_CurrentStream(
        LONG newVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StreamType(
        GUID *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StreamTypeB(
        BSTR *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StreamLength(
        double *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_Filename(
        BSTR *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_Filename(
        BSTR newVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetBitmapBits(
        double StreamTime,
        LONG *pBufferSize,
        char *pBuffer,
        LONG Width,
        LONG Height) = 0;

    virtual HRESULT STDMETHODCALLTYPE WriteBitmapBits(
        double StreamTime,
        LONG Width,
        LONG Height,
        BSTR Filename) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_StreamMediaType(
        AM_MEDIA_TYPE *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSampleGrabber(
        ISampleGrabber **ppVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE get_FrameRate(
        double *pVal) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnterBitmapGrabMode(
        double SeekTime) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMediaDet, 0x65bd0710, 0x24d2, 0x4ff7, 0x93,0x24, 0xed,0x2e,0x5d,0x3a,0xba,0xfa)
#endif
#else
typedef struct IMediaDetVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMediaDet *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMediaDet *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMediaDet *This);

    /*** IMediaDet methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Filter)(
        IMediaDet *This,
        IUnknown **pVal);

    HRESULT (STDMETHODCALLTYPE *put_Filter)(
        IMediaDet *This,
        IUnknown *newVal);

    HRESULT (STDMETHODCALLTYPE *get_OutputStreams)(
        IMediaDet *This,
        LONG *pVal);

    HRESULT (STDMETHODCALLTYPE *get_CurrentStream)(
        IMediaDet *This,
        LONG *pVal);

    HRESULT (STDMETHODCALLTYPE *put_CurrentStream)(
        IMediaDet *This,
        LONG newVal);

    HRESULT (STDMETHODCALLTYPE *get_StreamType)(
        IMediaDet *This,
        GUID *pVal);

    HRESULT (STDMETHODCALLTYPE *get_StreamTypeB)(
        IMediaDet *This,
        BSTR *pVal);

    HRESULT (STDMETHODCALLTYPE *get_StreamLength)(
        IMediaDet *This,
        double *pVal);

    HRESULT (STDMETHODCALLTYPE *get_Filename)(
        IMediaDet *This,
        BSTR *pVal);

    HRESULT (STDMETHODCALLTYPE *put_Filename)(
        IMediaDet *This,
        BSTR newVal);

    HRESULT (STDMETHODCALLTYPE *GetBitmapBits)(
        IMediaDet *This,
        double StreamTime,
        LONG *pBufferSize,
        char *pBuffer,
        LONG Width,
        LONG Height);

    HRESULT (STDMETHODCALLTYPE *WriteBitmapBits)(
        IMediaDet *This,
        double StreamTime,
        LONG Width,
        LONG Height,
        BSTR Filename);

    HRESULT (STDMETHODCALLTYPE *get_StreamMediaType)(
        IMediaDet *This,
        AM_MEDIA_TYPE *pVal);

    HRESULT (STDMETHODCALLTYPE *GetSampleGrabber)(
        IMediaDet *This,
        ISampleGrabber **ppVal);

    HRESULT (STDMETHODCALLTYPE *get_FrameRate)(
        IMediaDet *This,
        double *pVal);

    HRESULT (STDMETHODCALLTYPE *EnterBitmapGrabMode)(
        IMediaDet *This,
        double SeekTime);

    END_INTERFACE
} IMediaDetVtbl;

interface IMediaDet {
    CONST_VTBL IMediaDetVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMediaDet_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMediaDet_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMediaDet_Release(This) (This)->lpVtbl->Release(This)
/*** IMediaDet methods ***/
#define IMediaDet_get_Filter(This,pVal) (This)->lpVtbl->get_Filter(This,pVal)
#define IMediaDet_put_Filter(This,newVal) (This)->lpVtbl->put_Filter(This,newVal)
#define IMediaDet_get_OutputStreams(This,pVal) (This)->lpVtbl->get_OutputStreams(This,pVal)
#define IMediaDet_get_CurrentStream(This,pVal) (This)->lpVtbl->get_CurrentStream(This,pVal)
#define IMediaDet_put_CurrentStream(This,newVal) (This)->lpVtbl->put_CurrentStream(This,newVal)
#define IMediaDet_get_StreamType(This,pVal) (This)->lpVtbl->get_StreamType(This,pVal)
#define IMediaDet_get_StreamTypeB(This,pVal) (This)->lpVtbl->get_StreamTypeB(This,pVal)
#define IMediaDet_get_StreamLength(This,pVal) (This)->lpVtbl->get_StreamLength(This,pVal)
#define IMediaDet_get_Filename(This,pVal) (This)->lpVtbl->get_Filename(This,pVal)
#define IMediaDet_put_Filename(This,newVal) (This)->lpVtbl->put_Filename(This,newVal)
#define IMediaDet_GetBitmapBits(This,StreamTime,pBufferSize,pBuffer,Width,Height) (This)->lpVtbl->GetBitmapBits(This,StreamTime,pBufferSize,pBuffer,Width,Height)
#define IMediaDet_WriteBitmapBits(This,StreamTime,Width,Height,Filename) (This)->lpVtbl->WriteBitmapBits(This,StreamTime,Width,Height,Filename)
#define IMediaDet_get_StreamMediaType(This,pVal) (This)->lpVtbl->get_StreamMediaType(This,pVal)
#define IMediaDet_GetSampleGrabber(This,ppVal) (This)->lpVtbl->GetSampleGrabber(This,ppVal)
#define IMediaDet_get_FrameRate(This,pVal) (This)->lpVtbl->get_FrameRate(This,pVal)
#define IMediaDet_EnterBitmapGrabMode(This,SeekTime) (This)->lpVtbl->EnterBitmapGrabMode(This,SeekTime)
#else
/*** IUnknown methods ***/
static inline HRESULT IMediaDet_QueryInterface(IMediaDet* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMediaDet_AddRef(IMediaDet* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMediaDet_Release(IMediaDet* This) {
    return This->lpVtbl->Release(This);
}
/*** IMediaDet methods ***/
static inline HRESULT IMediaDet_get_Filter(IMediaDet* This,IUnknown **pVal) {
    return This->lpVtbl->get_Filter(This,pVal);
}
static inline HRESULT IMediaDet_put_Filter(IMediaDet* This,IUnknown *newVal) {
    return This->lpVtbl->put_Filter(This,newVal);
}
static inline HRESULT IMediaDet_get_OutputStreams(IMediaDet* This,LONG *pVal) {
    return This->lpVtbl->get_OutputStreams(This,pVal);
}
static inline HRESULT IMediaDet_get_CurrentStream(IMediaDet* This,LONG *pVal) {
    return This->lpVtbl->get_CurrentStream(This,pVal);
}
static inline HRESULT IMediaDet_put_CurrentStream(IMediaDet* This,LONG newVal) {
    return This->lpVtbl->put_CurrentStream(This,newVal);
}
static inline HRESULT IMediaDet_get_StreamType(IMediaDet* This,GUID *pVal) {
    return This->lpVtbl->get_StreamType(This,pVal);
}
static inline HRESULT IMediaDet_get_StreamTypeB(IMediaDet* This,BSTR *pVal) {
    return This->lpVtbl->get_StreamTypeB(This,pVal);
}
static inline HRESULT IMediaDet_get_StreamLength(IMediaDet* This,double *pVal) {
    return This->lpVtbl->get_StreamLength(This,pVal);
}
static inline HRESULT IMediaDet_get_Filename(IMediaDet* This,BSTR *pVal) {
    return This->lpVtbl->get_Filename(This,pVal);
}
static inline HRESULT IMediaDet_put_Filename(IMediaDet* This,BSTR newVal) {
    return This->lpVtbl->put_Filename(This,newVal);
}
static inline HRESULT IMediaDet_GetBitmapBits(IMediaDet* This,double StreamTime,LONG *pBufferSize,char *pBuffer,LONG Width,LONG Height) {
    return This->lpVtbl->GetBitmapBits(This,StreamTime,pBufferSize,pBuffer,Width,Height);
}
static inline HRESULT IMediaDet_WriteBitmapBits(IMediaDet* This,double StreamTime,LONG Width,LONG Height,BSTR Filename) {
    return This->lpVtbl->WriteBitmapBits(This,StreamTime,Width,Height,Filename);
}
static inline HRESULT IMediaDet_get_StreamMediaType(IMediaDet* This,AM_MEDIA_TYPE *pVal) {
    return This->lpVtbl->get_StreamMediaType(This,pVal);
}
static inline HRESULT IMediaDet_GetSampleGrabber(IMediaDet* This,ISampleGrabber **ppVal) {
    return This->lpVtbl->GetSampleGrabber(This,ppVal);
}
static inline HRESULT IMediaDet_get_FrameRate(IMediaDet* This,double *pVal) {
    return This->lpVtbl->get_FrameRate(This,pVal);
}
static inline HRESULT IMediaDet_EnterBitmapGrabMode(IMediaDet* This,double SeekTime) {
    return This->lpVtbl->EnterBitmapGrabMode(This,SeekTime);
}
#endif
#endif

#endif


#endif  /* __IMediaDet_INTERFACE_DEFINED__ */

/*****************************************************************************
 * MediaDet coclass
 */

DEFINE_GUID(CLSID_MediaDet, 0x65bd0711, 0x24d2, 0x4ff7, 0x93,0x24, 0xed,0x2e,0x5d,0x3a,0xba,0xfa);

#ifdef __cplusplus
class DECLSPEC_UUID("65bd0711-24d2-4ff7-9324-ed2e5d3abafa") MediaDet;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(MediaDet, 0x65bd0711, 0x24d2, 0x4ff7, 0x93,0x24, 0xed,0x2e,0x5d,0x3a,0xba,0xfa)
#endif
#endif

/*****************************************************************************
 * IMediaLocator interface
 */
#ifndef __IMediaLocator_INTERFACE_DEFINED__
#define __IMediaLocator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IMediaLocator, 0x288581e0, 0x66ce, 0x11d2, 0x91,0x8f, 0x00,0xc0,0xdf,0x10,0xd4,0x34);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("288581e0-66ce-11d2-918f-00c0df10d434")
IMediaLocator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE FindMediaFile(
        BSTR input,
        BSTR filter,
        BSTR *output,
        LONG flags) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddFoundLocation(
        BSTR dir) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IMediaLocator, 0x288581e0, 0x66ce, 0x11d2, 0x91,0x8f, 0x00,0xc0,0xdf,0x10,0xd4,0x34)
#endif
#else
typedef struct IMediaLocatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IMediaLocator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IMediaLocator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IMediaLocator *This);

    /*** IMediaLocator methods ***/
    HRESULT (STDMETHODCALLTYPE *FindMediaFile)(
        IMediaLocator *This,
        BSTR input,
        BSTR filter,
        BSTR *output,
        LONG flags);

    HRESULT (STDMETHODCALLTYPE *AddFoundLocation)(
        IMediaLocator *This,
        BSTR dir);

    END_INTERFACE
} IMediaLocatorVtbl;

interface IMediaLocator {
    CONST_VTBL IMediaLocatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IMediaLocator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IMediaLocator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IMediaLocator_Release(This) (This)->lpVtbl->Release(This)
/*** IMediaLocator methods ***/
#define IMediaLocator_FindMediaFile(This,input,filter,output,flags) (This)->lpVtbl->FindMediaFile(This,input,filter,output,flags)
#define IMediaLocator_AddFoundLocation(This,dir) (This)->lpVtbl->AddFoundLocation(This,dir)
#else
/*** IUnknown methods ***/
static inline HRESULT IMediaLocator_QueryInterface(IMediaLocator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IMediaLocator_AddRef(IMediaLocator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IMediaLocator_Release(IMediaLocator* This) {
    return This->lpVtbl->Release(This);
}
/*** IMediaLocator methods ***/
static inline HRESULT IMediaLocator_FindMediaFile(IMediaLocator* This,BSTR input,BSTR filter,BSTR *output,LONG flags) {
    return This->lpVtbl->FindMediaFile(This,input,filter,output,flags);
}
static inline HRESULT IMediaLocator_AddFoundLocation(IMediaLocator* This,BSTR dir) {
    return This->lpVtbl->AddFoundLocation(This,dir);
}
#endif
#endif

#endif


#endif  /* __IMediaLocator_INTERFACE_DEFINED__ */

typedef struct __WIDL_qedit_generated_name_0000002B {
    BSTR name;
    DISPID dispID;
    LONG nValues;
} DEXTER_PARAM;
typedef struct __WIDL_qedit_generated_name_0000002C {
    VARIANT v;
    REFERENCE_TIME rt;
    DWORD dwInterp;
} DEXTER_VALUE;
/*****************************************************************************
 * IPropertySetter interface
 */
#ifndef __IPropertySetter_INTERFACE_DEFINED__
#define __IPropertySetter_INTERFACE_DEFINED__

DEFINE_GUID(IID_IPropertySetter, 0xae9472bd, 0xb0c3, 0x11d2, 0x8d,0x24, 0x00,0xa0,0xc9,0x44,0x1e,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("ae9472bd-b0c3-11d2-8d24-00a0c9441e20")
IPropertySetter : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE LoadXML(
        IUnknown *pxml) = 0;

    virtual HRESULT STDMETHODCALLTYPE PrintXML(
        char *xml,
        int size,
        int *printed,
        int indent) = 0;

    virtual HRESULT STDMETHODCALLTYPE CloneProps(
        IPropertySetter **setter,
        REFERENCE_TIME start,
        REFERENCE_TIME stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddProp(
        DEXTER_PARAM param,
        DEXTER_VALUE *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetProps(
        LONG *params,
        DEXTER_PARAM **param,
        DEXTER_VALUE **value) = 0;

    virtual HRESULT STDMETHODCALLTYPE FreeProps(
        LONG params,
        DEXTER_PARAM *param,
        DEXTER_VALUE *value) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearProps(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SaveToBlob(
        LONG *size,
        BYTE **blob) = 0;

    virtual HRESULT STDMETHODCALLTYPE LoadFromBlob(
        LONG size,
        BYTE *blob) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetProps(
        IUnknown *target,
        REFERENCE_TIME now) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IPropertySetter, 0xae9472bd, 0xb0c3, 0x11d2, 0x8d,0x24, 0x00,0xa0,0xc9,0x44,0x1e,0x20)
#endif
#else
typedef struct IPropertySetterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IPropertySetter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IPropertySetter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IPropertySetter *This);

    /*** IPropertySetter methods ***/
    HRESULT (STDMETHODCALLTYPE *LoadXML)(
        IPropertySetter *This,
        IUnknown *pxml);

    HRESULT (STDMETHODCALLTYPE *PrintXML)(
        IPropertySetter *This,
        char *xml,
        int size,
        int *printed,
        int indent);

    HRESULT (STDMETHODCALLTYPE *CloneProps)(
        IPropertySetter *This,
        IPropertySetter **setter,
        REFERENCE_TIME start,
        REFERENCE_TIME stop);

    HRESULT (STDMETHODCALLTYPE *AddProp)(
        IPropertySetter *This,
        DEXTER_PARAM param,
        DEXTER_VALUE *value);

    HRESULT (STDMETHODCALLTYPE *GetProps)(
        IPropertySetter *This,
        LONG *params,
        DEXTER_PARAM **param,
        DEXTER_VALUE **value);

    HRESULT (STDMETHODCALLTYPE *FreeProps)(
        IPropertySetter *This,
        LONG params,
        DEXTER_PARAM *param,
        DEXTER_VALUE *value);

    HRESULT (STDMETHODCALLTYPE *ClearProps)(
        IPropertySetter *This);

    HRESULT (STDMETHODCALLTYPE *SaveToBlob)(
        IPropertySetter *This,
        LONG *size,
        BYTE **blob);

    HRESULT (STDMETHODCALLTYPE *LoadFromBlob)(
        IPropertySetter *This,
        LONG size,
        BYTE *blob);

    HRESULT (STDMETHODCALLTYPE *SetProps)(
        IPropertySetter *This,
        IUnknown *target,
        REFERENCE_TIME now);

    END_INTERFACE
} IPropertySetterVtbl;

interface IPropertySetter {
    CONST_VTBL IPropertySetterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IPropertySetter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IPropertySetter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IPropertySetter_Release(This) (This)->lpVtbl->Release(This)
/*** IPropertySetter methods ***/
#define IPropertySetter_LoadXML(This,pxml) (This)->lpVtbl->LoadXML(This,pxml)
#define IPropertySetter_PrintXML(This,xml,size,printed,indent) (This)->lpVtbl->PrintXML(This,xml,size,printed,indent)
#define IPropertySetter_CloneProps(This,setter,start,stop) (This)->lpVtbl->CloneProps(This,setter,start,stop)
#define IPropertySetter_AddProp(This,param,value) (This)->lpVtbl->AddProp(This,param,value)
#define IPropertySetter_GetProps(This,params,param,value) (This)->lpVtbl->GetProps(This,params,param,value)
#define IPropertySetter_FreeProps(This,params,param,value) (This)->lpVtbl->FreeProps(This,params,param,value)
#define IPropertySetter_ClearProps(This) (This)->lpVtbl->ClearProps(This)
#define IPropertySetter_SaveToBlob(This,size,blob) (This)->lpVtbl->SaveToBlob(This,size,blob)
#define IPropertySetter_LoadFromBlob(This,size,blob) (This)->lpVtbl->LoadFromBlob(This,size,blob)
#define IPropertySetter_SetProps(This,target,now) (This)->lpVtbl->SetProps(This,target,now)
#else
/*** IUnknown methods ***/
static inline HRESULT IPropertySetter_QueryInterface(IPropertySetter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IPropertySetter_AddRef(IPropertySetter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IPropertySetter_Release(IPropertySetter* This) {
    return This->lpVtbl->Release(This);
}
/*** IPropertySetter methods ***/
static inline HRESULT IPropertySetter_LoadXML(IPropertySetter* This,IUnknown *pxml) {
    return This->lpVtbl->LoadXML(This,pxml);
}
static inline HRESULT IPropertySetter_PrintXML(IPropertySetter* This,char *xml,int size,int *printed,int indent) {
    return This->lpVtbl->PrintXML(This,xml,size,printed,indent);
}
static inline HRESULT IPropertySetter_CloneProps(IPropertySetter* This,IPropertySetter **setter,REFERENCE_TIME start,REFERENCE_TIME stop) {
    return This->lpVtbl->CloneProps(This,setter,start,stop);
}
static inline HRESULT IPropertySetter_AddProp(IPropertySetter* This,DEXTER_PARAM param,DEXTER_VALUE *value) {
    return This->lpVtbl->AddProp(This,param,value);
}
static inline HRESULT IPropertySetter_GetProps(IPropertySetter* This,LONG *params,DEXTER_PARAM **param,DEXTER_VALUE **value) {
    return This->lpVtbl->GetProps(This,params,param,value);
}
static inline HRESULT IPropertySetter_FreeProps(IPropertySetter* This,LONG params,DEXTER_PARAM *param,DEXTER_VALUE *value) {
    return This->lpVtbl->FreeProps(This,params,param,value);
}
static inline HRESULT IPropertySetter_ClearProps(IPropertySetter* This) {
    return This->lpVtbl->ClearProps(This);
}
static inline HRESULT IPropertySetter_SaveToBlob(IPropertySetter* This,LONG *size,BYTE **blob) {
    return This->lpVtbl->SaveToBlob(This,size,blob);
}
static inline HRESULT IPropertySetter_LoadFromBlob(IPropertySetter* This,LONG size,BYTE *blob) {
    return This->lpVtbl->LoadFromBlob(This,size,blob);
}
static inline HRESULT IPropertySetter_SetProps(IPropertySetter* This,IUnknown *target,REFERENCE_TIME now) {
    return This->lpVtbl->SetProps(This,target,now);
}
#endif
#endif

#endif


#endif  /* __IPropertySetter_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAMErrorLog interface
 */
#ifndef __IAMErrorLog_INTERFACE_DEFINED__
#define __IAMErrorLog_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMErrorLog, 0xe43e73a2, 0x0efa, 0x11d3, 0x96,0x01, 0x00,0xa0,0xc9,0x44,0x1e,0x20);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("e43e73a2-0efa-11d3-9601-00a0c9441e20")
IAMErrorLog : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE LogError(
        LONG severity,
        BSTR error_str,
        LONG error_code,
        LONG hresult,
        VARIANT *extra) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMErrorLog, 0xe43e73a2, 0x0efa, 0x11d3, 0x96,0x01, 0x00,0xa0,0xc9,0x44,0x1e,0x20)
#endif
#else
typedef struct IAMErrorLogVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMErrorLog *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMErrorLog *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMErrorLog *This);

    /*** IAMErrorLog methods ***/
    HRESULT (STDMETHODCALLTYPE *LogError)(
        IAMErrorLog *This,
        LONG severity,
        BSTR error_str,
        LONG error_code,
        LONG hresult,
        VARIANT *extra);

    END_INTERFACE
} IAMErrorLogVtbl;

interface IAMErrorLog {
    CONST_VTBL IAMErrorLogVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMErrorLog_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMErrorLog_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMErrorLog_Release(This) (This)->lpVtbl->Release(This)
/*** IAMErrorLog methods ***/
#define IAMErrorLog_LogError(This,severity,error_str,error_code,hresult,extra) (This)->lpVtbl->LogError(This,severity,error_str,error_code,hresult,extra)
#else
/*** IUnknown methods ***/
static inline HRESULT IAMErrorLog_QueryInterface(IAMErrorLog* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAMErrorLog_AddRef(IAMErrorLog* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAMErrorLog_Release(IAMErrorLog* This) {
    return This->lpVtbl->Release(This);
}
/*** IAMErrorLog methods ***/
static inline HRESULT IAMErrorLog_LogError(IAMErrorLog* This,LONG severity,BSTR error_str,LONG error_code,LONG hresult,VARIANT *extra) {
    return This->lpVtbl->LogError(This,severity,error_str,error_code,hresult,extra);
}
#endif
#endif

#endif


#endif  /* __IAMErrorLog_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAMSetErrorLog interface
 */
#ifndef __IAMSetErrorLog_INTERFACE_DEFINED__
#define __IAMSetErrorLog_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMSetErrorLog, 0x963566da, 0xbe21, 0x4eaf, 0x88,0xe9, 0x35,0x70,0x4f,0x8f,0x52,0xa1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("963566da-be21-4eaf-88e9-35704f8f52a1")
IAMSetErrorLog : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE get_ErrorLog(
        IAMErrorLog **log) = 0;

    virtual HRESULT STDMETHODCALLTYPE put_ErrorLog(
        IAMErrorLog *log) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMSetErrorLog, 0x963566da, 0xbe21, 0x4eaf, 0x88,0xe9, 0x35,0x70,0x4f,0x8f,0x52,0xa1)
#endif
#else
typedef struct IAMSetErrorLogVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMSetErrorLog *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMSetErrorLog *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMSetErrorLog *This);

    /*** IAMSetErrorLog methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ErrorLog)(
        IAMSetErrorLog *This,
        IAMErrorLog **log);

    HRESULT (STDMETHODCALLTYPE *put_ErrorLog)(
        IAMSetErrorLog *This,
        IAMErrorLog *log);

    END_INTERFACE
} IAMSetErrorLogVtbl;

interface IAMSetErrorLog {
    CONST_VTBL IAMSetErrorLogVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMSetErrorLog_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMSetErrorLog_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMSetErrorLog_Release(This) (This)->lpVtbl->Release(This)
/*** IAMSetErrorLog methods ***/
#define IAMSetErrorLog_get_ErrorLog(This,log) (This)->lpVtbl->get_ErrorLog(This,log)
#define IAMSetErrorLog_put_ErrorLog(This,log) (This)->lpVtbl->put_ErrorLog(This,log)
#else
/*** IUnknown methods ***/
static inline HRESULT IAMSetErrorLog_QueryInterface(IAMSetErrorLog* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAMSetErrorLog_AddRef(IAMSetErrorLog* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAMSetErrorLog_Release(IAMSetErrorLog* This) {
    return This->lpVtbl->Release(This);
}
/*** IAMSetErrorLog methods ***/
static inline HRESULT IAMSetErrorLog_get_ErrorLog(IAMSetErrorLog* This,IAMErrorLog **log) {
    return This->lpVtbl->get_ErrorLog(This,log);
}
static inline HRESULT IAMSetErrorLog_put_ErrorLog(IAMSetErrorLog* This,IAMErrorLog *log) {
    return This->lpVtbl->put_ErrorLog(This,log);
}
#endif
#endif

#endif


#endif  /* __IAMSetErrorLog_INTERFACE_DEFINED__ */

#ifndef __IAMTimeline_FWD_DEFINED__
#define __IAMTimeline_FWD_DEFINED__
typedef interface IAMTimeline IAMTimeline;
#ifdef __cplusplus
interface IAMTimeline;
#endif /* __cplusplus */
#endif

#ifndef __IAMTimelineGroup_FWD_DEFINED__
#define __IAMTimelineGroup_FWD_DEFINED__
typedef interface IAMTimelineGroup IAMTimelineGroup;
#ifdef __cplusplus
interface IAMTimelineGroup;
#endif /* __cplusplus */
#endif

#ifndef __IAMTimelineObj_FWD_DEFINED__
#define __IAMTimelineObj_FWD_DEFINED__
typedef interface IAMTimelineObj IAMTimelineObj;
#ifdef __cplusplus
interface IAMTimelineObj;
#endif /* __cplusplus */
#endif

#ifndef __IAMTimelineSrc_FWD_DEFINED__
#define __IAMTimelineSrc_FWD_DEFINED__
typedef interface IAMTimelineSrc IAMTimelineSrc;
#ifdef __cplusplus
interface IAMTimelineSrc;
#endif /* __cplusplus */
#endif

typedef enum __WIDL_qedit_generated_name_0000002D {
    TIMELINE_MAJOR_TYPE_COMPOSITE = 1,
    TIMELINE_MAJOR_TYPE_TRACK = 2,
    TIMELINE_MAJOR_TYPE_SOURCE = 4,
    TIMELINE_MAJOR_TYPE_TRANSITION = 8,
    TIMELINE_MAJOR_TYPE_EFFECT = 16,
    TIMELINE_MAJOR_TYPE_GROUP = 128
} TIMELINE_MAJOR_TYPE;
/*****************************************************************************
 * IAMTimeline interface
 */
#ifndef __IAMTimeline_INTERFACE_DEFINED__
#define __IAMTimeline_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMTimeline, 0x78530b74, 0x61f9, 0x11d2, 0x8c,0xad, 0x00,0xa0,0x24,0x58,0x09,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("78530b74-61f9-11d2-8cad-00a024580902")
IAMTimeline : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CreateEmptyNode(
        IAMTimelineObj **obj,
        TIMELINE_MAJOR_TYPE type) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddGroup(
        IAMTimelineObj *group) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemGroupFromList(
        IAMTimelineObj *group) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGroup(
        IAMTimelineObj **group,
        LONG index) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGroupCount(
        LONG *count) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearAllGroups(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetInsertMode(
        LONG *mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInsertMode(
        LONG mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableTransitions(
        WINBOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE TransitionsEnabled(
        WINBOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE EnableEffects(
        WINBOOL enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE EffectsEnabled(
        WINBOOL *enabled) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetInterestRange(
        REFERENCE_TIME start,
        REFERENCE_TIME stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDuration(
        REFERENCE_TIME *duration) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDuration2(
        double *duration) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultFPS(
        double fps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultFPS(
        double *fps) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsDirty(
        WINBOOL *dirty) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDirtyRange(
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCountOfType(
        LONG group,
        LONG *value,
        LONG *value_with_comps,
        TIMELINE_MAJOR_TYPE type) = 0;

    virtual HRESULT STDMETHODCALLTYPE ValidateSourceNames(
        LONG flags,
        IMediaLocator *override,
        LONG_PTR notify_event) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultTransition(
        GUID *guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultTransition(
        GUID *guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultEffect(
        GUID *guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultEffect(
        GUID *guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultTransitionB(
        BSTR guidb) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultTransitionB(
        BSTR *guidb) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultEffectB(
        BSTR guidb) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultEffectB(
        BSTR *guidb) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMTimeline, 0x78530b74, 0x61f9, 0x11d2, 0x8c,0xad, 0x00,0xa0,0x24,0x58,0x09,0x02)
#endif
#else
typedef struct IAMTimelineVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMTimeline *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMTimeline *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMTimeline *This);

    /*** IAMTimeline methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateEmptyNode)(
        IAMTimeline *This,
        IAMTimelineObj **obj,
        TIMELINE_MAJOR_TYPE type);

    HRESULT (STDMETHODCALLTYPE *AddGroup)(
        IAMTimeline *This,
        IAMTimelineObj *group);

    HRESULT (STDMETHODCALLTYPE *RemGroupFromList)(
        IAMTimeline *This,
        IAMTimelineObj *group);

    HRESULT (STDMETHODCALLTYPE *GetGroup)(
        IAMTimeline *This,
        IAMTimelineObj **group,
        LONG index);

    HRESULT (STDMETHODCALLTYPE *GetGroupCount)(
        IAMTimeline *This,
        LONG *count);

    HRESULT (STDMETHODCALLTYPE *ClearAllGroups)(
        IAMTimeline *This);

    HRESULT (STDMETHODCALLTYPE *GetInsertMode)(
        IAMTimeline *This,
        LONG *mode);

    HRESULT (STDMETHODCALLTYPE *SetInsertMode)(
        IAMTimeline *This,
        LONG mode);

    HRESULT (STDMETHODCALLTYPE *EnableTransitions)(
        IAMTimeline *This,
        WINBOOL enabled);

    HRESULT (STDMETHODCALLTYPE *TransitionsEnabled)(
        IAMTimeline *This,
        WINBOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *EnableEffects)(
        IAMTimeline *This,
        WINBOOL enabled);

    HRESULT (STDMETHODCALLTYPE *EffectsEnabled)(
        IAMTimeline *This,
        WINBOOL *enabled);

    HRESULT (STDMETHODCALLTYPE *SetInterestRange)(
        IAMTimeline *This,
        REFERENCE_TIME start,
        REFERENCE_TIME stop);

    HRESULT (STDMETHODCALLTYPE *GetDuration)(
        IAMTimeline *This,
        REFERENCE_TIME *duration);

    HRESULT (STDMETHODCALLTYPE *GetDuration2)(
        IAMTimeline *This,
        double *duration);

    HRESULT (STDMETHODCALLTYPE *SetDefaultFPS)(
        IAMTimeline *This,
        double fps);

    HRESULT (STDMETHODCALLTYPE *GetDefaultFPS)(
        IAMTimeline *This,
        double *fps);

    HRESULT (STDMETHODCALLTYPE *IsDirty)(
        IAMTimeline *This,
        WINBOOL *dirty);

    HRESULT (STDMETHODCALLTYPE *GetDirtyRange)(
        IAMTimeline *This,
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop);

    HRESULT (STDMETHODCALLTYPE *GetCountOfType)(
        IAMTimeline *This,
        LONG group,
        LONG *value,
        LONG *value_with_comps,
        TIMELINE_MAJOR_TYPE type);

    HRESULT (STDMETHODCALLTYPE *ValidateSourceNames)(
        IAMTimeline *This,
        LONG flags,
        IMediaLocator *override,
        LONG_PTR notify_event);

    HRESULT (STDMETHODCALLTYPE *SetDefaultTransition)(
        IAMTimeline *This,
        GUID *guid);

    HRESULT (STDMETHODCALLTYPE *GetDefaultTransition)(
        IAMTimeline *This,
        GUID *guid);

    HRESULT (STDMETHODCALLTYPE *SetDefaultEffect)(
        IAMTimeline *This,
        GUID *guid);

    HRESULT (STDMETHODCALLTYPE *GetDefaultEffect)(
        IAMTimeline *This,
        GUID *guid);

    HRESULT (STDMETHODCALLTYPE *SetDefaultTransitionB)(
        IAMTimeline *This,
        BSTR guidb);

    HRESULT (STDMETHODCALLTYPE *GetDefaultTransitionB)(
        IAMTimeline *This,
        BSTR *guidb);

    HRESULT (STDMETHODCALLTYPE *SetDefaultEffectB)(
        IAMTimeline *This,
        BSTR guidb);

    HRESULT (STDMETHODCALLTYPE *GetDefaultEffectB)(
        IAMTimeline *This,
        BSTR *guidb);

    END_INTERFACE
} IAMTimelineVtbl;

interface IAMTimeline {
    CONST_VTBL IAMTimelineVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMTimeline_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMTimeline_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMTimeline_Release(This) (This)->lpVtbl->Release(This)
/*** IAMTimeline methods ***/
#define IAMTimeline_CreateEmptyNode(This,obj,type) (This)->lpVtbl->CreateEmptyNode(This,obj,type)
#define IAMTimeline_AddGroup(This,group) (This)->lpVtbl->AddGroup(This,group)
#define IAMTimeline_RemGroupFromList(This,group) (This)->lpVtbl->RemGroupFromList(This,group)
#define IAMTimeline_GetGroup(This,group,index) (This)->lpVtbl->GetGroup(This,group,index)
#define IAMTimeline_GetGroupCount(This,count) (This)->lpVtbl->GetGroupCount(This,count)
#define IAMTimeline_ClearAllGroups(This) (This)->lpVtbl->ClearAllGroups(This)
#define IAMTimeline_GetInsertMode(This,mode) (This)->lpVtbl->GetInsertMode(This,mode)
#define IAMTimeline_SetInsertMode(This,mode) (This)->lpVtbl->SetInsertMode(This,mode)
#define IAMTimeline_EnableTransitions(This,enabled) (This)->lpVtbl->EnableTransitions(This,enabled)
#define IAMTimeline_TransitionsEnabled(This,enabled) (This)->lpVtbl->TransitionsEnabled(This,enabled)
#define IAMTimeline_EnableEffects(This,enabled) (This)->lpVtbl->EnableEffects(This,enabled)
#define IAMTimeline_EffectsEnabled(This,enabled) (This)->lpVtbl->EffectsEnabled(This,enabled)
#define IAMTimeline_SetInterestRange(This,start,stop) (This)->lpVtbl->SetInterestRange(This,start,stop)
#define IAMTimeline_GetDuration(This,duration) (This)->lpVtbl->GetDuration(This,duration)
#define IAMTimeline_GetDuration2(This,duration) (This)->lpVtbl->GetDuration2(This,duration)
#define IAMTimeline_SetDefaultFPS(This,fps) (This)->lpVtbl->SetDefaultFPS(This,fps)
#define IAMTimeline_GetDefaultFPS(This,fps) (This)->lpVtbl->GetDefaultFPS(This,fps)
#define IAMTimeline_IsDirty(This,dirty) (This)->lpVtbl->IsDirty(This,dirty)
#define IAMTimeline_GetDirtyRange(This,start,stop) (This)->lpVtbl->GetDirtyRange(This,start,stop)
#define IAMTimeline_GetCountOfType(This,group,value,value_with_comps,type) (This)->lpVtbl->GetCountOfType(This,group,value,value_with_comps,type)
#define IAMTimeline_ValidateSourceNames(This,flags,override,notify_event) (This)->lpVtbl->ValidateSourceNames(This,flags,override,notify_event)
#define IAMTimeline_SetDefaultTransition(This,guid) (This)->lpVtbl->SetDefaultTransition(This,guid)
#define IAMTimeline_GetDefaultTransition(This,guid) (This)->lpVtbl->GetDefaultTransition(This,guid)
#define IAMTimeline_SetDefaultEffect(This,guid) (This)->lpVtbl->SetDefaultEffect(This,guid)
#define IAMTimeline_GetDefaultEffect(This,guid) (This)->lpVtbl->GetDefaultEffect(This,guid)
#define IAMTimeline_SetDefaultTransitionB(This,guidb) (This)->lpVtbl->SetDefaultTransitionB(This,guidb)
#define IAMTimeline_GetDefaultTransitionB(This,guidb) (This)->lpVtbl->GetDefaultTransitionB(This,guidb)
#define IAMTimeline_SetDefaultEffectB(This,guidb) (This)->lpVtbl->SetDefaultEffectB(This,guidb)
#define IAMTimeline_GetDefaultEffectB(This,guidb) (This)->lpVtbl->GetDefaultEffectB(This,guidb)
#else
/*** IUnknown methods ***/
static inline HRESULT IAMTimeline_QueryInterface(IAMTimeline* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAMTimeline_AddRef(IAMTimeline* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAMTimeline_Release(IAMTimeline* This) {
    return This->lpVtbl->Release(This);
}
/*** IAMTimeline methods ***/
static inline HRESULT IAMTimeline_CreateEmptyNode(IAMTimeline* This,IAMTimelineObj **obj,TIMELINE_MAJOR_TYPE type) {
    return This->lpVtbl->CreateEmptyNode(This,obj,type);
}
static inline HRESULT IAMTimeline_AddGroup(IAMTimeline* This,IAMTimelineObj *group) {
    return This->lpVtbl->AddGroup(This,group);
}
static inline HRESULT IAMTimeline_RemGroupFromList(IAMTimeline* This,IAMTimelineObj *group) {
    return This->lpVtbl->RemGroupFromList(This,group);
}
static inline HRESULT IAMTimeline_GetGroup(IAMTimeline* This,IAMTimelineObj **group,LONG index) {
    return This->lpVtbl->GetGroup(This,group,index);
}
static inline HRESULT IAMTimeline_GetGroupCount(IAMTimeline* This,LONG *count) {
    return This->lpVtbl->GetGroupCount(This,count);
}
static inline HRESULT IAMTimeline_ClearAllGroups(IAMTimeline* This) {
    return This->lpVtbl->ClearAllGroups(This);
}
static inline HRESULT IAMTimeline_GetInsertMode(IAMTimeline* This,LONG *mode) {
    return This->lpVtbl->GetInsertMode(This,mode);
}
static inline HRESULT IAMTimeline_SetInsertMode(IAMTimeline* This,LONG mode) {
    return This->lpVtbl->SetInsertMode(This,mode);
}
static inline HRESULT IAMTimeline_EnableTransitions(IAMTimeline* This,WINBOOL enabled) {
    return This->lpVtbl->EnableTransitions(This,enabled);
}
static inline HRESULT IAMTimeline_TransitionsEnabled(IAMTimeline* This,WINBOOL *enabled) {
    return This->lpVtbl->TransitionsEnabled(This,enabled);
}
static inline HRESULT IAMTimeline_EnableEffects(IAMTimeline* This,WINBOOL enabled) {
    return This->lpVtbl->EnableEffects(This,enabled);
}
static inline HRESULT IAMTimeline_EffectsEnabled(IAMTimeline* This,WINBOOL *enabled) {
    return This->lpVtbl->EffectsEnabled(This,enabled);
}
static inline HRESULT IAMTimeline_SetInterestRange(IAMTimeline* This,REFERENCE_TIME start,REFERENCE_TIME stop) {
    return This->lpVtbl->SetInterestRange(This,start,stop);
}
static inline HRESULT IAMTimeline_GetDuration(IAMTimeline* This,REFERENCE_TIME *duration) {
    return This->lpVtbl->GetDuration(This,duration);
}
static inline HRESULT IAMTimeline_GetDuration2(IAMTimeline* This,double *duration) {
    return This->lpVtbl->GetDuration2(This,duration);
}
static inline HRESULT IAMTimeline_SetDefaultFPS(IAMTimeline* This,double fps) {
    return This->lpVtbl->SetDefaultFPS(This,fps);
}
static inline HRESULT IAMTimeline_GetDefaultFPS(IAMTimeline* This,double *fps) {
    return This->lpVtbl->GetDefaultFPS(This,fps);
}
static inline HRESULT IAMTimeline_IsDirty(IAMTimeline* This,WINBOOL *dirty) {
    return This->lpVtbl->IsDirty(This,dirty);
}
static inline HRESULT IAMTimeline_GetDirtyRange(IAMTimeline* This,REFERENCE_TIME *start,REFERENCE_TIME *stop) {
    return This->lpVtbl->GetDirtyRange(This,start,stop);
}
static inline HRESULT IAMTimeline_GetCountOfType(IAMTimeline* This,LONG group,LONG *value,LONG *value_with_comps,TIMELINE_MAJOR_TYPE type) {
    return This->lpVtbl->GetCountOfType(This,group,value,value_with_comps,type);
}
static inline HRESULT IAMTimeline_ValidateSourceNames(IAMTimeline* This,LONG flags,IMediaLocator *override,LONG_PTR notify_event) {
    return This->lpVtbl->ValidateSourceNames(This,flags,override,notify_event);
}
static inline HRESULT IAMTimeline_SetDefaultTransition(IAMTimeline* This,GUID *guid) {
    return This->lpVtbl->SetDefaultTransition(This,guid);
}
static inline HRESULT IAMTimeline_GetDefaultTransition(IAMTimeline* This,GUID *guid) {
    return This->lpVtbl->GetDefaultTransition(This,guid);
}
static inline HRESULT IAMTimeline_SetDefaultEffect(IAMTimeline* This,GUID *guid) {
    return This->lpVtbl->SetDefaultEffect(This,guid);
}
static inline HRESULT IAMTimeline_GetDefaultEffect(IAMTimeline* This,GUID *guid) {
    return This->lpVtbl->GetDefaultEffect(This,guid);
}
static inline HRESULT IAMTimeline_SetDefaultTransitionB(IAMTimeline* This,BSTR guidb) {
    return This->lpVtbl->SetDefaultTransitionB(This,guidb);
}
static inline HRESULT IAMTimeline_GetDefaultTransitionB(IAMTimeline* This,BSTR *guidb) {
    return This->lpVtbl->GetDefaultTransitionB(This,guidb);
}
static inline HRESULT IAMTimeline_SetDefaultEffectB(IAMTimeline* This,BSTR guidb) {
    return This->lpVtbl->SetDefaultEffectB(This,guidb);
}
static inline HRESULT IAMTimeline_GetDefaultEffectB(IAMTimeline* This,BSTR *guidb) {
    return This->lpVtbl->GetDefaultEffectB(This,guidb);
}
#endif
#endif

#endif


#endif  /* __IAMTimeline_INTERFACE_DEFINED__ */

/*****************************************************************************
 * AMTimeline coclass
 */

DEFINE_GUID(CLSID_AMTimeline, 0x78530b75, 0x61f9, 0x11d2, 0x8c,0xad, 0x00,0xa0,0x24,0x58,0x09,0x02);

#ifdef __cplusplus
class DECLSPEC_UUID("78530b75-61f9-11d2-8cad-00a024580902") AMTimeline;
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(AMTimeline, 0x78530b75, 0x61f9, 0x11d2, 0x8c,0xad, 0x00,0xa0,0x24,0x58,0x09,0x02)
#endif
#endif

/*****************************************************************************
 * IAMTimelineGroup interface
 */
#ifndef __IAMTimelineGroup_INTERFACE_DEFINED__
#define __IAMTimelineGroup_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMTimelineGroup, 0x9eed4f00, 0xb8a6, 0x11d2, 0x80,0x23, 0x00,0xc0,0xdf,0x10,0xd4,0x34);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("9eed4f00-b8a6-11d2-8023-00c0df10d434")
IAMTimelineGroup : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE SetTimeline(
        IAMTimeline *timeline) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimeline(
        IAMTimeline **timeline) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPriority(
        LONG *priority) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaType(
        AM_MEDIA_TYPE *a) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaType(
        AM_MEDIA_TYPE *a) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputFPS(
        double fps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputFPS(
        double *fps) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetGroupName(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGroupName(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPreviewMode(
        WINBOOL preview) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPreviewMode(
        WINBOOL *preview) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaTypeForVB(
        LONG type) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetOutputBuffering(
        int *buffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOutputBuffering(
        int buffer) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSmartRecompressFormat(
        LONG *format) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSmartRecompressFormat(
        LONG **format) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsSmartRecompressFormatSet(
        WINBOOL *set) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsRecompressFormatDirty(
        WINBOOL *dirty) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearRecompressFormatDirty(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetRecompFormatFromSource(
        IAMTimelineSrc *source) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMTimelineGroup, 0x9eed4f00, 0xb8a6, 0x11d2, 0x80,0x23, 0x00,0xc0,0xdf,0x10,0xd4,0x34)
#endif
#else
typedef struct IAMTimelineGroupVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMTimelineGroup *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMTimelineGroup *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMTimelineGroup *This);

    /*** IAMTimelineGroup methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTimeline)(
        IAMTimelineGroup *This,
        IAMTimeline *timeline);

    HRESULT (STDMETHODCALLTYPE *GetTimeline)(
        IAMTimelineGroup *This,
        IAMTimeline **timeline);

    HRESULT (STDMETHODCALLTYPE *GetPriority)(
        IAMTimelineGroup *This,
        LONG *priority);

    HRESULT (STDMETHODCALLTYPE *GetMediaType)(
        IAMTimelineGroup *This,
        AM_MEDIA_TYPE *a);

    HRESULT (STDMETHODCALLTYPE *SetMediaType)(
        IAMTimelineGroup *This,
        AM_MEDIA_TYPE *a);

    HRESULT (STDMETHODCALLTYPE *SetOutputFPS)(
        IAMTimelineGroup *This,
        double fps);

    HRESULT (STDMETHODCALLTYPE *GetOutputFPS)(
        IAMTimelineGroup *This,
        double *fps);

    HRESULT (STDMETHODCALLTYPE *SetGroupName)(
        IAMTimelineGroup *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *GetGroupName)(
        IAMTimelineGroup *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *SetPreviewMode)(
        IAMTimelineGroup *This,
        WINBOOL preview);

    HRESULT (STDMETHODCALLTYPE *GetPreviewMode)(
        IAMTimelineGroup *This,
        WINBOOL *preview);

    HRESULT (STDMETHODCALLTYPE *SetMediaTypeForVB)(
        IAMTimelineGroup *This,
        LONG type);

    HRESULT (STDMETHODCALLTYPE *GetOutputBuffering)(
        IAMTimelineGroup *This,
        int *buffer);

    HRESULT (STDMETHODCALLTYPE *SetOutputBuffering)(
        IAMTimelineGroup *This,
        int buffer);

    HRESULT (STDMETHODCALLTYPE *SetSmartRecompressFormat)(
        IAMTimelineGroup *This,
        LONG *format);

    HRESULT (STDMETHODCALLTYPE *GetSmartRecompressFormat)(
        IAMTimelineGroup *This,
        LONG **format);

    HRESULT (STDMETHODCALLTYPE *IsSmartRecompressFormatSet)(
        IAMTimelineGroup *This,
        WINBOOL *set);

    HRESULT (STDMETHODCALLTYPE *IsRecompressFormatDirty)(
        IAMTimelineGroup *This,
        WINBOOL *dirty);

    HRESULT (STDMETHODCALLTYPE *ClearRecompressFormatDirty)(
        IAMTimelineGroup *This);

    HRESULT (STDMETHODCALLTYPE *SetRecompFormatFromSource)(
        IAMTimelineGroup *This,
        IAMTimelineSrc *source);

    END_INTERFACE
} IAMTimelineGroupVtbl;

interface IAMTimelineGroup {
    CONST_VTBL IAMTimelineGroupVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMTimelineGroup_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMTimelineGroup_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMTimelineGroup_Release(This) (This)->lpVtbl->Release(This)
/*** IAMTimelineGroup methods ***/
#define IAMTimelineGroup_SetTimeline(This,timeline) (This)->lpVtbl->SetTimeline(This,timeline)
#define IAMTimelineGroup_GetTimeline(This,timeline) (This)->lpVtbl->GetTimeline(This,timeline)
#define IAMTimelineGroup_GetPriority(This,priority) (This)->lpVtbl->GetPriority(This,priority)
#define IAMTimelineGroup_GetMediaType(This,a) (This)->lpVtbl->GetMediaType(This,a)
#define IAMTimelineGroup_SetMediaType(This,a) (This)->lpVtbl->SetMediaType(This,a)
#define IAMTimelineGroup_SetOutputFPS(This,fps) (This)->lpVtbl->SetOutputFPS(This,fps)
#define IAMTimelineGroup_GetOutputFPS(This,fps) (This)->lpVtbl->GetOutputFPS(This,fps)
#define IAMTimelineGroup_SetGroupName(This,name) (This)->lpVtbl->SetGroupName(This,name)
#define IAMTimelineGroup_GetGroupName(This,name) (This)->lpVtbl->GetGroupName(This,name)
#define IAMTimelineGroup_SetPreviewMode(This,preview) (This)->lpVtbl->SetPreviewMode(This,preview)
#define IAMTimelineGroup_GetPreviewMode(This,preview) (This)->lpVtbl->GetPreviewMode(This,preview)
#define IAMTimelineGroup_SetMediaTypeForVB(This,type) (This)->lpVtbl->SetMediaTypeForVB(This,type)
#define IAMTimelineGroup_GetOutputBuffering(This,buffer) (This)->lpVtbl->GetOutputBuffering(This,buffer)
#define IAMTimelineGroup_SetOutputBuffering(This,buffer) (This)->lpVtbl->SetOutputBuffering(This,buffer)
#define IAMTimelineGroup_SetSmartRecompressFormat(This,format) (This)->lpVtbl->SetSmartRecompressFormat(This,format)
#define IAMTimelineGroup_GetSmartRecompressFormat(This,format) (This)->lpVtbl->GetSmartRecompressFormat(This,format)
#define IAMTimelineGroup_IsSmartRecompressFormatSet(This,set) (This)->lpVtbl->IsSmartRecompressFormatSet(This,set)
#define IAMTimelineGroup_IsRecompressFormatDirty(This,dirty) (This)->lpVtbl->IsRecompressFormatDirty(This,dirty)
#define IAMTimelineGroup_ClearRecompressFormatDirty(This) (This)->lpVtbl->ClearRecompressFormatDirty(This)
#define IAMTimelineGroup_SetRecompFormatFromSource(This,source) (This)->lpVtbl->SetRecompFormatFromSource(This,source)
#else
/*** IUnknown methods ***/
static inline HRESULT IAMTimelineGroup_QueryInterface(IAMTimelineGroup* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAMTimelineGroup_AddRef(IAMTimelineGroup* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAMTimelineGroup_Release(IAMTimelineGroup* This) {
    return This->lpVtbl->Release(This);
}
/*** IAMTimelineGroup methods ***/
static inline HRESULT IAMTimelineGroup_SetTimeline(IAMTimelineGroup* This,IAMTimeline *timeline) {
    return This->lpVtbl->SetTimeline(This,timeline);
}
static inline HRESULT IAMTimelineGroup_GetTimeline(IAMTimelineGroup* This,IAMTimeline **timeline) {
    return This->lpVtbl->GetTimeline(This,timeline);
}
static inline HRESULT IAMTimelineGroup_GetPriority(IAMTimelineGroup* This,LONG *priority) {
    return This->lpVtbl->GetPriority(This,priority);
}
static inline HRESULT IAMTimelineGroup_GetMediaType(IAMTimelineGroup* This,AM_MEDIA_TYPE *a) {
    return This->lpVtbl->GetMediaType(This,a);
}
static inline HRESULT IAMTimelineGroup_SetMediaType(IAMTimelineGroup* This,AM_MEDIA_TYPE *a) {
    return This->lpVtbl->SetMediaType(This,a);
}
static inline HRESULT IAMTimelineGroup_SetOutputFPS(IAMTimelineGroup* This,double fps) {
    return This->lpVtbl->SetOutputFPS(This,fps);
}
static inline HRESULT IAMTimelineGroup_GetOutputFPS(IAMTimelineGroup* This,double *fps) {
    return This->lpVtbl->GetOutputFPS(This,fps);
}
static inline HRESULT IAMTimelineGroup_SetGroupName(IAMTimelineGroup* This,BSTR name) {
    return This->lpVtbl->SetGroupName(This,name);
}
static inline HRESULT IAMTimelineGroup_GetGroupName(IAMTimelineGroup* This,BSTR *name) {
    return This->lpVtbl->GetGroupName(This,name);
}
static inline HRESULT IAMTimelineGroup_SetPreviewMode(IAMTimelineGroup* This,WINBOOL preview) {
    return This->lpVtbl->SetPreviewMode(This,preview);
}
static inline HRESULT IAMTimelineGroup_GetPreviewMode(IAMTimelineGroup* This,WINBOOL *preview) {
    return This->lpVtbl->GetPreviewMode(This,preview);
}
static inline HRESULT IAMTimelineGroup_SetMediaTypeForVB(IAMTimelineGroup* This,LONG type) {
    return This->lpVtbl->SetMediaTypeForVB(This,type);
}
static inline HRESULT IAMTimelineGroup_GetOutputBuffering(IAMTimelineGroup* This,int *buffer) {
    return This->lpVtbl->GetOutputBuffering(This,buffer);
}
static inline HRESULT IAMTimelineGroup_SetOutputBuffering(IAMTimelineGroup* This,int buffer) {
    return This->lpVtbl->SetOutputBuffering(This,buffer);
}
static inline HRESULT IAMTimelineGroup_SetSmartRecompressFormat(IAMTimelineGroup* This,LONG *format) {
    return This->lpVtbl->SetSmartRecompressFormat(This,format);
}
static inline HRESULT IAMTimelineGroup_GetSmartRecompressFormat(IAMTimelineGroup* This,LONG **format) {
    return This->lpVtbl->GetSmartRecompressFormat(This,format);
}
static inline HRESULT IAMTimelineGroup_IsSmartRecompressFormatSet(IAMTimelineGroup* This,WINBOOL *set) {
    return This->lpVtbl->IsSmartRecompressFormatSet(This,set);
}
static inline HRESULT IAMTimelineGroup_IsRecompressFormatDirty(IAMTimelineGroup* This,WINBOOL *dirty) {
    return This->lpVtbl->IsRecompressFormatDirty(This,dirty);
}
static inline HRESULT IAMTimelineGroup_ClearRecompressFormatDirty(IAMTimelineGroup* This) {
    return This->lpVtbl->ClearRecompressFormatDirty(This);
}
static inline HRESULT IAMTimelineGroup_SetRecompFormatFromSource(IAMTimelineGroup* This,IAMTimelineSrc *source) {
    return This->lpVtbl->SetRecompFormatFromSource(This,source);
}
#endif
#endif

#endif


#endif  /* __IAMTimelineGroup_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAMTimelineObj interface
 */
#ifndef __IAMTimelineObj_INTERFACE_DEFINED__
#define __IAMTimelineObj_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMTimelineObj, 0x78530b77, 0x61f9, 0x11d2, 0x8c,0xad, 0x00,0xa0,0x24,0x58,0x09,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("78530b77-61f9-11d2-8cad-00a024580902")
IAMTimelineObj : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetStartStop(
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStartStop2(
        REFTIME *start,
        REFTIME *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE FixTimes(
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE FixTimes2(
        REFTIME *start,
        REFTIME *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStartStop(
        REFERENCE_TIME start,
        REFERENCE_TIME stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStartStop2(
        REFTIME start,
        REFTIME stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPropertySetter(
        IPropertySetter **setter) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetPropertySetter(
        IPropertySetter *setter) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubObject(
        IUnknown **obj) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSubObject(
        IUnknown *obj) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSubObjectGUID(
        GUID guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetSubObjectGUIDB(
        BSTR guidb) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubObjectGUID(
        GUID *guid) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubObjectGUIDB(
        BSTR *guidb) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetSubObjectLoaded(
        WINBOOL *loaded) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimelineType(
        TIMELINE_MAJOR_TYPE *type) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetTimelineType(
        TIMELINE_MAJOR_TYPE type) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserID(
        LONG *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUserID(
        LONG id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGenID(
        LONG *id) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserName(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUserName(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetUserData(
        BYTE *data,
        LONG *size) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetUserData(
        BYTE *data,
        LONG size) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMuted(
        WINBOOL *muted) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMuted(
        WINBOOL muted) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetLocked(
        WINBOOL *locked) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLocked(
        WINBOOL locked) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDirtyRange(
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDirtyRange2(
        REFTIME *start,
        REFTIME *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDirtyRange(
        REFERENCE_TIME start,
        REFERENCE_TIME stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDirtyRange2(
        REFTIME start,
        REFTIME stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE ClearDirty(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Remove(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE RemoveAll(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTimelineNoRef(
        IAMTimeline **timeline) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetGroupIBelongTo(
        IAMTimelineGroup **group) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetEmbedDepth(
        LONG *depth) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMTimelineObj, 0x78530b77, 0x61f9, 0x11d2, 0x8c,0xad, 0x00,0xa0,0x24,0x58,0x09,0x02)
#endif
#else
typedef struct IAMTimelineObjVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMTimelineObj *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMTimelineObj *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMTimelineObj *This);

    /*** IAMTimelineObj methods ***/
    HRESULT (STDMETHODCALLTYPE *GetStartStop)(
        IAMTimelineObj *This,
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop);

    HRESULT (STDMETHODCALLTYPE *GetStartStop2)(
        IAMTimelineObj *This,
        REFTIME *start,
        REFTIME *stop);

    HRESULT (STDMETHODCALLTYPE *FixTimes)(
        IAMTimelineObj *This,
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop);

    HRESULT (STDMETHODCALLTYPE *FixTimes2)(
        IAMTimelineObj *This,
        REFTIME *start,
        REFTIME *stop);

    HRESULT (STDMETHODCALLTYPE *SetStartStop)(
        IAMTimelineObj *This,
        REFERENCE_TIME start,
        REFERENCE_TIME stop);

    HRESULT (STDMETHODCALLTYPE *SetStartStop2)(
        IAMTimelineObj *This,
        REFTIME start,
        REFTIME stop);

    HRESULT (STDMETHODCALLTYPE *GetPropertySetter)(
        IAMTimelineObj *This,
        IPropertySetter **setter);

    HRESULT (STDMETHODCALLTYPE *SetPropertySetter)(
        IAMTimelineObj *This,
        IPropertySetter *setter);

    HRESULT (STDMETHODCALLTYPE *GetSubObject)(
        IAMTimelineObj *This,
        IUnknown **obj);

    HRESULT (STDMETHODCALLTYPE *SetSubObject)(
        IAMTimelineObj *This,
        IUnknown *obj);

    HRESULT (STDMETHODCALLTYPE *SetSubObjectGUID)(
        IAMTimelineObj *This,
        GUID guid);

    HRESULT (STDMETHODCALLTYPE *SetSubObjectGUIDB)(
        IAMTimelineObj *This,
        BSTR guidb);

    HRESULT (STDMETHODCALLTYPE *GetSubObjectGUID)(
        IAMTimelineObj *This,
        GUID *guid);

    HRESULT (STDMETHODCALLTYPE *GetSubObjectGUIDB)(
        IAMTimelineObj *This,
        BSTR *guidb);

    HRESULT (STDMETHODCALLTYPE *GetSubObjectLoaded)(
        IAMTimelineObj *This,
        WINBOOL *loaded);

    HRESULT (STDMETHODCALLTYPE *GetTimelineType)(
        IAMTimelineObj *This,
        TIMELINE_MAJOR_TYPE *type);

    HRESULT (STDMETHODCALLTYPE *SetTimelineType)(
        IAMTimelineObj *This,
        TIMELINE_MAJOR_TYPE type);

    HRESULT (STDMETHODCALLTYPE *GetUserID)(
        IAMTimelineObj *This,
        LONG *id);

    HRESULT (STDMETHODCALLTYPE *SetUserID)(
        IAMTimelineObj *This,
        LONG id);

    HRESULT (STDMETHODCALLTYPE *GetGenID)(
        IAMTimelineObj *This,
        LONG *id);

    HRESULT (STDMETHODCALLTYPE *GetUserName)(
        IAMTimelineObj *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *SetUserName)(
        IAMTimelineObj *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *GetUserData)(
        IAMTimelineObj *This,
        BYTE *data,
        LONG *size);

    HRESULT (STDMETHODCALLTYPE *SetUserData)(
        IAMTimelineObj *This,
        BYTE *data,
        LONG size);

    HRESULT (STDMETHODCALLTYPE *GetMuted)(
        IAMTimelineObj *This,
        WINBOOL *muted);

    HRESULT (STDMETHODCALLTYPE *SetMuted)(
        IAMTimelineObj *This,
        WINBOOL muted);

    HRESULT (STDMETHODCALLTYPE *GetLocked)(
        IAMTimelineObj *This,
        WINBOOL *locked);

    HRESULT (STDMETHODCALLTYPE *SetLocked)(
        IAMTimelineObj *This,
        WINBOOL locked);

    HRESULT (STDMETHODCALLTYPE *GetDirtyRange)(
        IAMTimelineObj *This,
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop);

    HRESULT (STDMETHODCALLTYPE *GetDirtyRange2)(
        IAMTimelineObj *This,
        REFTIME *start,
        REFTIME *stop);

    HRESULT (STDMETHODCALLTYPE *SetDirtyRange)(
        IAMTimelineObj *This,
        REFERENCE_TIME start,
        REFERENCE_TIME stop);

    HRESULT (STDMETHODCALLTYPE *SetDirtyRange2)(
        IAMTimelineObj *This,
        REFTIME start,
        REFTIME stop);

    HRESULT (STDMETHODCALLTYPE *ClearDirty)(
        IAMTimelineObj *This);

    HRESULT (STDMETHODCALLTYPE *Remove)(
        IAMTimelineObj *This);

    HRESULT (STDMETHODCALLTYPE *RemoveAll)(
        IAMTimelineObj *This);

    HRESULT (STDMETHODCALLTYPE *GetTimelineNoRef)(
        IAMTimelineObj *This,
        IAMTimeline **timeline);

    HRESULT (STDMETHODCALLTYPE *GetGroupIBelongTo)(
        IAMTimelineObj *This,
        IAMTimelineGroup **group);

    HRESULT (STDMETHODCALLTYPE *GetEmbedDepth)(
        IAMTimelineObj *This,
        LONG *depth);

    END_INTERFACE
} IAMTimelineObjVtbl;

interface IAMTimelineObj {
    CONST_VTBL IAMTimelineObjVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMTimelineObj_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMTimelineObj_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMTimelineObj_Release(This) (This)->lpVtbl->Release(This)
/*** IAMTimelineObj methods ***/
#define IAMTimelineObj_GetStartStop(This,start,stop) (This)->lpVtbl->GetStartStop(This,start,stop)
#define IAMTimelineObj_GetStartStop2(This,start,stop) (This)->lpVtbl->GetStartStop2(This,start,stop)
#define IAMTimelineObj_FixTimes(This,start,stop) (This)->lpVtbl->FixTimes(This,start,stop)
#define IAMTimelineObj_FixTimes2(This,start,stop) (This)->lpVtbl->FixTimes2(This,start,stop)
#define IAMTimelineObj_SetStartStop(This,start,stop) (This)->lpVtbl->SetStartStop(This,start,stop)
#define IAMTimelineObj_SetStartStop2(This,start,stop) (This)->lpVtbl->SetStartStop2(This,start,stop)
#define IAMTimelineObj_GetPropertySetter(This,setter) (This)->lpVtbl->GetPropertySetter(This,setter)
#define IAMTimelineObj_SetPropertySetter(This,setter) (This)->lpVtbl->SetPropertySetter(This,setter)
#define IAMTimelineObj_GetSubObject(This,obj) (This)->lpVtbl->GetSubObject(This,obj)
#define IAMTimelineObj_SetSubObject(This,obj) (This)->lpVtbl->SetSubObject(This,obj)
#define IAMTimelineObj_SetSubObjectGUID(This,guid) (This)->lpVtbl->SetSubObjectGUID(This,guid)
#define IAMTimelineObj_SetSubObjectGUIDB(This,guidb) (This)->lpVtbl->SetSubObjectGUIDB(This,guidb)
#define IAMTimelineObj_GetSubObjectGUID(This,guid) (This)->lpVtbl->GetSubObjectGUID(This,guid)
#define IAMTimelineObj_GetSubObjectGUIDB(This,guidb) (This)->lpVtbl->GetSubObjectGUIDB(This,guidb)
#define IAMTimelineObj_GetSubObjectLoaded(This,loaded) (This)->lpVtbl->GetSubObjectLoaded(This,loaded)
#define IAMTimelineObj_GetTimelineType(This,type) (This)->lpVtbl->GetTimelineType(This,type)
#define IAMTimelineObj_SetTimelineType(This,type) (This)->lpVtbl->SetTimelineType(This,type)
#define IAMTimelineObj_GetUserID(This,id) (This)->lpVtbl->GetUserID(This,id)
#define IAMTimelineObj_SetUserID(This,id) (This)->lpVtbl->SetUserID(This,id)
#define IAMTimelineObj_GetGenID(This,id) (This)->lpVtbl->GetGenID(This,id)
#define IAMTimelineObj_GetUserName(This,name) (This)->lpVtbl->GetUserName(This,name)
#define IAMTimelineObj_SetUserName(This,name) (This)->lpVtbl->SetUserName(This,name)
#define IAMTimelineObj_GetUserData(This,data,size) (This)->lpVtbl->GetUserData(This,data,size)
#define IAMTimelineObj_SetUserData(This,data,size) (This)->lpVtbl->SetUserData(This,data,size)
#define IAMTimelineObj_GetMuted(This,muted) (This)->lpVtbl->GetMuted(This,muted)
#define IAMTimelineObj_SetMuted(This,muted) (This)->lpVtbl->SetMuted(This,muted)
#define IAMTimelineObj_GetLocked(This,locked) (This)->lpVtbl->GetLocked(This,locked)
#define IAMTimelineObj_SetLocked(This,locked) (This)->lpVtbl->SetLocked(This,locked)
#define IAMTimelineObj_GetDirtyRange(This,start,stop) (This)->lpVtbl->GetDirtyRange(This,start,stop)
#define IAMTimelineObj_GetDirtyRange2(This,start,stop) (This)->lpVtbl->GetDirtyRange2(This,start,stop)
#define IAMTimelineObj_SetDirtyRange(This,start,stop) (This)->lpVtbl->SetDirtyRange(This,start,stop)
#define IAMTimelineObj_SetDirtyRange2(This,start,stop) (This)->lpVtbl->SetDirtyRange2(This,start,stop)
#define IAMTimelineObj_ClearDirty(This) (This)->lpVtbl->ClearDirty(This)
#define IAMTimelineObj_Remove(This) (This)->lpVtbl->Remove(This)
#define IAMTimelineObj_RemoveAll(This) (This)->lpVtbl->RemoveAll(This)
#define IAMTimelineObj_GetTimelineNoRef(This,timeline) (This)->lpVtbl->GetTimelineNoRef(This,timeline)
#define IAMTimelineObj_GetGroupIBelongTo(This,group) (This)->lpVtbl->GetGroupIBelongTo(This,group)
#define IAMTimelineObj_GetEmbedDepth(This,depth) (This)->lpVtbl->GetEmbedDepth(This,depth)
#else
/*** IUnknown methods ***/
static inline HRESULT IAMTimelineObj_QueryInterface(IAMTimelineObj* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAMTimelineObj_AddRef(IAMTimelineObj* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAMTimelineObj_Release(IAMTimelineObj* This) {
    return This->lpVtbl->Release(This);
}
/*** IAMTimelineObj methods ***/
static inline HRESULT IAMTimelineObj_GetStartStop(IAMTimelineObj* This,REFERENCE_TIME *start,REFERENCE_TIME *stop) {
    return This->lpVtbl->GetStartStop(This,start,stop);
}
static inline HRESULT IAMTimelineObj_GetStartStop2(IAMTimelineObj* This,REFTIME *start,REFTIME *stop) {
    return This->lpVtbl->GetStartStop2(This,start,stop);
}
static inline HRESULT IAMTimelineObj_FixTimes(IAMTimelineObj* This,REFERENCE_TIME *start,REFERENCE_TIME *stop) {
    return This->lpVtbl->FixTimes(This,start,stop);
}
static inline HRESULT IAMTimelineObj_FixTimes2(IAMTimelineObj* This,REFTIME *start,REFTIME *stop) {
    return This->lpVtbl->FixTimes2(This,start,stop);
}
static inline HRESULT IAMTimelineObj_SetStartStop(IAMTimelineObj* This,REFERENCE_TIME start,REFERENCE_TIME stop) {
    return This->lpVtbl->SetStartStop(This,start,stop);
}
static inline HRESULT IAMTimelineObj_SetStartStop2(IAMTimelineObj* This,REFTIME start,REFTIME stop) {
    return This->lpVtbl->SetStartStop2(This,start,stop);
}
static inline HRESULT IAMTimelineObj_GetPropertySetter(IAMTimelineObj* This,IPropertySetter **setter) {
    return This->lpVtbl->GetPropertySetter(This,setter);
}
static inline HRESULT IAMTimelineObj_SetPropertySetter(IAMTimelineObj* This,IPropertySetter *setter) {
    return This->lpVtbl->SetPropertySetter(This,setter);
}
static inline HRESULT IAMTimelineObj_GetSubObject(IAMTimelineObj* This,IUnknown **obj) {
    return This->lpVtbl->GetSubObject(This,obj);
}
static inline HRESULT IAMTimelineObj_SetSubObject(IAMTimelineObj* This,IUnknown *obj) {
    return This->lpVtbl->SetSubObject(This,obj);
}
static inline HRESULT IAMTimelineObj_SetSubObjectGUID(IAMTimelineObj* This,GUID guid) {
    return This->lpVtbl->SetSubObjectGUID(This,guid);
}
static inline HRESULT IAMTimelineObj_SetSubObjectGUIDB(IAMTimelineObj* This,BSTR guidb) {
    return This->lpVtbl->SetSubObjectGUIDB(This,guidb);
}
static inline HRESULT IAMTimelineObj_GetSubObjectGUID(IAMTimelineObj* This,GUID *guid) {
    return This->lpVtbl->GetSubObjectGUID(This,guid);
}
static inline HRESULT IAMTimelineObj_GetSubObjectGUIDB(IAMTimelineObj* This,BSTR *guidb) {
    return This->lpVtbl->GetSubObjectGUIDB(This,guidb);
}
static inline HRESULT IAMTimelineObj_GetSubObjectLoaded(IAMTimelineObj* This,WINBOOL *loaded) {
    return This->lpVtbl->GetSubObjectLoaded(This,loaded);
}
static inline HRESULT IAMTimelineObj_GetTimelineType(IAMTimelineObj* This,TIMELINE_MAJOR_TYPE *type) {
    return This->lpVtbl->GetTimelineType(This,type);
}
static inline HRESULT IAMTimelineObj_SetTimelineType(IAMTimelineObj* This,TIMELINE_MAJOR_TYPE type) {
    return This->lpVtbl->SetTimelineType(This,type);
}
static inline HRESULT IAMTimelineObj_GetUserID(IAMTimelineObj* This,LONG *id) {
    return This->lpVtbl->GetUserID(This,id);
}
static inline HRESULT IAMTimelineObj_SetUserID(IAMTimelineObj* This,LONG id) {
    return This->lpVtbl->SetUserID(This,id);
}
static inline HRESULT IAMTimelineObj_GetGenID(IAMTimelineObj* This,LONG *id) {
    return This->lpVtbl->GetGenID(This,id);
}
static inline HRESULT IAMTimelineObj_GetUserName(IAMTimelineObj* This,BSTR *name) {
    return This->lpVtbl->GetUserName(This,name);
}
static inline HRESULT IAMTimelineObj_SetUserName(IAMTimelineObj* This,BSTR name) {
    return This->lpVtbl->SetUserName(This,name);
}
static inline HRESULT IAMTimelineObj_GetUserData(IAMTimelineObj* This,BYTE *data,LONG *size) {
    return This->lpVtbl->GetUserData(This,data,size);
}
static inline HRESULT IAMTimelineObj_SetUserData(IAMTimelineObj* This,BYTE *data,LONG size) {
    return This->lpVtbl->SetUserData(This,data,size);
}
static inline HRESULT IAMTimelineObj_GetMuted(IAMTimelineObj* This,WINBOOL *muted) {
    return This->lpVtbl->GetMuted(This,muted);
}
static inline HRESULT IAMTimelineObj_SetMuted(IAMTimelineObj* This,WINBOOL muted) {
    return This->lpVtbl->SetMuted(This,muted);
}
static inline HRESULT IAMTimelineObj_GetLocked(IAMTimelineObj* This,WINBOOL *locked) {
    return This->lpVtbl->GetLocked(This,locked);
}
static inline HRESULT IAMTimelineObj_SetLocked(IAMTimelineObj* This,WINBOOL locked) {
    return This->lpVtbl->SetLocked(This,locked);
}
static inline HRESULT IAMTimelineObj_GetDirtyRange(IAMTimelineObj* This,REFERENCE_TIME *start,REFERENCE_TIME *stop) {
    return This->lpVtbl->GetDirtyRange(This,start,stop);
}
static inline HRESULT IAMTimelineObj_GetDirtyRange2(IAMTimelineObj* This,REFTIME *start,REFTIME *stop) {
    return This->lpVtbl->GetDirtyRange2(This,start,stop);
}
static inline HRESULT IAMTimelineObj_SetDirtyRange(IAMTimelineObj* This,REFERENCE_TIME start,REFERENCE_TIME stop) {
    return This->lpVtbl->SetDirtyRange(This,start,stop);
}
static inline HRESULT IAMTimelineObj_SetDirtyRange2(IAMTimelineObj* This,REFTIME start,REFTIME stop) {
    return This->lpVtbl->SetDirtyRange2(This,start,stop);
}
static inline HRESULT IAMTimelineObj_ClearDirty(IAMTimelineObj* This) {
    return This->lpVtbl->ClearDirty(This);
}
static inline HRESULT IAMTimelineObj_Remove(IAMTimelineObj* This) {
    return This->lpVtbl->Remove(This);
}
static inline HRESULT IAMTimelineObj_RemoveAll(IAMTimelineObj* This) {
    return This->lpVtbl->RemoveAll(This);
}
static inline HRESULT IAMTimelineObj_GetTimelineNoRef(IAMTimelineObj* This,IAMTimeline **timeline) {
    return This->lpVtbl->GetTimelineNoRef(This,timeline);
}
static inline HRESULT IAMTimelineObj_GetGroupIBelongTo(IAMTimelineObj* This,IAMTimelineGroup **group) {
    return This->lpVtbl->GetGroupIBelongTo(This,group);
}
static inline HRESULT IAMTimelineObj_GetEmbedDepth(IAMTimelineObj* This,LONG *depth) {
    return This->lpVtbl->GetEmbedDepth(This,depth);
}
#endif
#endif

#endif


#endif  /* __IAMTimelineObj_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAMTimelineSrc interface
 */
#ifndef __IAMTimelineSrc_INTERFACE_DEFINED__
#define __IAMTimelineSrc_INTERFACE_DEFINED__

DEFINE_GUID(IID_IAMTimelineSrc, 0x78530b79, 0x61f9, 0x11d2, 0x8c,0xad, 0x00,0xa0,0x24,0x58,0x09,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("78530b79-61f9-11d2-8cad-00a024580902")
IAMTimelineSrc : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE GetMediaTimes(
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaTimes2(
        REFTIME *start,
        REFTIME *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE ModifyStopTime(
        REFERENCE_TIME stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE ModifyStopTime2(
        REFTIME stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE FixMediaTimes(
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE FixMediaTimes2(
        REFTIME *start,
        REFTIME *stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaTimes(
        REFERENCE_TIME Start,
        REFERENCE_TIME Stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaTimes2(
        REFTIME Start,
        REFTIME Stop) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaLength(
        REFERENCE_TIME length) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaLength2(
        REFTIME length) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaLength(
        REFERENCE_TIME *length) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaLength2(
        REFTIME *length) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetMediaName(
        BSTR *name) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetMediaName(
        BSTR name) = 0;

    virtual HRESULT STDMETHODCALLTYPE SpliceWithNext(
        IAMTimelineObj *next) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStreamNumber(
        LONG *num) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStreamNumber(
        LONG num) = 0;

    virtual HRESULT STDMETHODCALLTYPE IsNormalRate(
        WINBOOL *normal) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetDefaultFPS(
        double *fps) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetDefaultFPS(
        double fps) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetStretchMode(
        int *mode) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetStretchMode(
        int mode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IAMTimelineSrc, 0x78530b79, 0x61f9, 0x11d2, 0x8c,0xad, 0x00,0xa0,0x24,0x58,0x09,0x02)
#endif
#else
typedef struct IAMTimelineSrcVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IAMTimelineSrc *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IAMTimelineSrc *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IAMTimelineSrc *This);

    /*** IAMTimelineSrc methods ***/
    HRESULT (STDMETHODCALLTYPE *GetMediaTimes)(
        IAMTimelineSrc *This,
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop);

    HRESULT (STDMETHODCALLTYPE *GetMediaTimes2)(
        IAMTimelineSrc *This,
        REFTIME *start,
        REFTIME *stop);

    HRESULT (STDMETHODCALLTYPE *ModifyStopTime)(
        IAMTimelineSrc *This,
        REFERENCE_TIME stop);

    HRESULT (STDMETHODCALLTYPE *ModifyStopTime2)(
        IAMTimelineSrc *This,
        REFTIME stop);

    HRESULT (STDMETHODCALLTYPE *FixMediaTimes)(
        IAMTimelineSrc *This,
        REFERENCE_TIME *start,
        REFERENCE_TIME *stop);

    HRESULT (STDMETHODCALLTYPE *FixMediaTimes2)(
        IAMTimelineSrc *This,
        REFTIME *start,
        REFTIME *stop);

    HRESULT (STDMETHODCALLTYPE *SetMediaTimes)(
        IAMTimelineSrc *This,
        REFERENCE_TIME Start,
        REFERENCE_TIME Stop);

    HRESULT (STDMETHODCALLTYPE *SetMediaTimes2)(
        IAMTimelineSrc *This,
        REFTIME Start,
        REFTIME Stop);

    HRESULT (STDMETHODCALLTYPE *SetMediaLength)(
        IAMTimelineSrc *This,
        REFERENCE_TIME length);

    HRESULT (STDMETHODCALLTYPE *SetMediaLength2)(
        IAMTimelineSrc *This,
        REFTIME length);

    HRESULT (STDMETHODCALLTYPE *GetMediaLength)(
        IAMTimelineSrc *This,
        REFERENCE_TIME *length);

    HRESULT (STDMETHODCALLTYPE *GetMediaLength2)(
        IAMTimelineSrc *This,
        REFTIME *length);

    HRESULT (STDMETHODCALLTYPE *GetMediaName)(
        IAMTimelineSrc *This,
        BSTR *name);

    HRESULT (STDMETHODCALLTYPE *SetMediaName)(
        IAMTimelineSrc *This,
        BSTR name);

    HRESULT (STDMETHODCALLTYPE *SpliceWithNext)(
        IAMTimelineSrc *This,
        IAMTimelineObj *next);

    HRESULT (STDMETHODCALLTYPE *GetStreamNumber)(
        IAMTimelineSrc *This,
        LONG *num);

    HRESULT (STDMETHODCALLTYPE *SetStreamNumber)(
        IAMTimelineSrc *This,
        LONG num);

    HRESULT (STDMETHODCALLTYPE *IsNormalRate)(
        IAMTimelineSrc *This,
        WINBOOL *normal);

    HRESULT (STDMETHODCALLTYPE *GetDefaultFPS)(
        IAMTimelineSrc *This,
        double *fps);

    HRESULT (STDMETHODCALLTYPE *SetDefaultFPS)(
        IAMTimelineSrc *This,
        double fps);

    HRESULT (STDMETHODCALLTYPE *GetStretchMode)(
        IAMTimelineSrc *This,
        int *mode);

    HRESULT (STDMETHODCALLTYPE *SetStretchMode)(
        IAMTimelineSrc *This,
        int mode);

    END_INTERFACE
} IAMTimelineSrcVtbl;

interface IAMTimelineSrc {
    CONST_VTBL IAMTimelineSrcVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IAMTimelineSrc_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IAMTimelineSrc_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IAMTimelineSrc_Release(This) (This)->lpVtbl->Release(This)
/*** IAMTimelineSrc methods ***/
#define IAMTimelineSrc_GetMediaTimes(This,start,stop) (This)->lpVtbl->GetMediaTimes(This,start,stop)
#define IAMTimelineSrc_GetMediaTimes2(This,start,stop) (This)->lpVtbl->GetMediaTimes2(This,start,stop)
#define IAMTimelineSrc_ModifyStopTime(This,stop) (This)->lpVtbl->ModifyStopTime(This,stop)
#define IAMTimelineSrc_ModifyStopTime2(This,stop) (This)->lpVtbl->ModifyStopTime2(This,stop)
#define IAMTimelineSrc_FixMediaTimes(This,start,stop) (This)->lpVtbl->FixMediaTimes(This,start,stop)
#define IAMTimelineSrc_FixMediaTimes2(This,start,stop) (This)->lpVtbl->FixMediaTimes2(This,start,stop)
#define IAMTimelineSrc_SetMediaTimes(This,Start,Stop) (This)->lpVtbl->SetMediaTimes(This,Start,Stop)
#define IAMTimelineSrc_SetMediaTimes2(This,Start,Stop) (This)->lpVtbl->SetMediaTimes2(This,Start,Stop)
#define IAMTimelineSrc_SetMediaLength(This,length) (This)->lpVtbl->SetMediaLength(This,length)
#define IAMTimelineSrc_SetMediaLength2(This,length) (This)->lpVtbl->SetMediaLength2(This,length)
#define IAMTimelineSrc_GetMediaLength(This,length) (This)->lpVtbl->GetMediaLength(This,length)
#define IAMTimelineSrc_GetMediaLength2(This,length) (This)->lpVtbl->GetMediaLength2(This,length)
#define IAMTimelineSrc_GetMediaName(This,name) (This)->lpVtbl->GetMediaName(This,name)
#define IAMTimelineSrc_SetMediaName(This,name) (This)->lpVtbl->SetMediaName(This,name)
#define IAMTimelineSrc_SpliceWithNext(This,next) (This)->lpVtbl->SpliceWithNext(This,next)
#define IAMTimelineSrc_GetStreamNumber(This,num) (This)->lpVtbl->GetStreamNumber(This,num)
#define IAMTimelineSrc_SetStreamNumber(This,num) (This)->lpVtbl->SetStreamNumber(This,num)
#define IAMTimelineSrc_IsNormalRate(This,normal) (This)->lpVtbl->IsNormalRate(This,normal)
#define IAMTimelineSrc_GetDefaultFPS(This,fps) (This)->lpVtbl->GetDefaultFPS(This,fps)
#define IAMTimelineSrc_SetDefaultFPS(This,fps) (This)->lpVtbl->SetDefaultFPS(This,fps)
#define IAMTimelineSrc_GetStretchMode(This,mode) (This)->lpVtbl->GetStretchMode(This,mode)
#define IAMTimelineSrc_SetStretchMode(This,mode) (This)->lpVtbl->SetStretchMode(This,mode)
#else
/*** IUnknown methods ***/
static inline HRESULT IAMTimelineSrc_QueryInterface(IAMTimelineSrc* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IAMTimelineSrc_AddRef(IAMTimelineSrc* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IAMTimelineSrc_Release(IAMTimelineSrc* This) {
    return This->lpVtbl->Release(This);
}
/*** IAMTimelineSrc methods ***/
static inline HRESULT IAMTimelineSrc_GetMediaTimes(IAMTimelineSrc* This,REFERENCE_TIME *start,REFERENCE_TIME *stop) {
    return This->lpVtbl->GetMediaTimes(This,start,stop);
}
static inline HRESULT IAMTimelineSrc_GetMediaTimes2(IAMTimelineSrc* This,REFTIME *start,REFTIME *stop) {
    return This->lpVtbl->GetMediaTimes2(This,start,stop);
}
static inline HRESULT IAMTimelineSrc_ModifyStopTime(IAMTimelineSrc* This,REFERENCE_TIME stop) {
    return This->lpVtbl->ModifyStopTime(This,stop);
}
static inline HRESULT IAMTimelineSrc_ModifyStopTime2(IAMTimelineSrc* This,REFTIME stop) {
    return This->lpVtbl->ModifyStopTime2(This,stop);
}
static inline HRESULT IAMTimelineSrc_FixMediaTimes(IAMTimelineSrc* This,REFERENCE_TIME *start,REFERENCE_TIME *stop) {
    return This->lpVtbl->FixMediaTimes(This,start,stop);
}
static inline HRESULT IAMTimelineSrc_FixMediaTimes2(IAMTimelineSrc* This,REFTIME *start,REFTIME *stop) {
    return This->lpVtbl->FixMediaTimes2(This,start,stop);
}
static inline HRESULT IAMTimelineSrc_SetMediaTimes(IAMTimelineSrc* This,REFERENCE_TIME Start,REFERENCE_TIME Stop) {
    return This->lpVtbl->SetMediaTimes(This,Start,Stop);
}
static inline HRESULT IAMTimelineSrc_SetMediaTimes2(IAMTimelineSrc* This,REFTIME Start,REFTIME Stop) {
    return This->lpVtbl->SetMediaTimes2(This,Start,Stop);
}
static inline HRESULT IAMTimelineSrc_SetMediaLength(IAMTimelineSrc* This,REFERENCE_TIME length) {
    return This->lpVtbl->SetMediaLength(This,length);
}
static inline HRESULT IAMTimelineSrc_SetMediaLength2(IAMTimelineSrc* This,REFTIME length) {
    return This->lpVtbl->SetMediaLength2(This,length);
}
static inline HRESULT IAMTimelineSrc_GetMediaLength(IAMTimelineSrc* This,REFERENCE_TIME *length) {
    return This->lpVtbl->GetMediaLength(This,length);
}
static inline HRESULT IAMTimelineSrc_GetMediaLength2(IAMTimelineSrc* This,REFTIME *length) {
    return This->lpVtbl->GetMediaLength2(This,length);
}
static inline HRESULT IAMTimelineSrc_GetMediaName(IAMTimelineSrc* This,BSTR *name) {
    return This->lpVtbl->GetMediaName(This,name);
}
static inline HRESULT IAMTimelineSrc_SetMediaName(IAMTimelineSrc* This,BSTR name) {
    return This->lpVtbl->SetMediaName(This,name);
}
static inline HRESULT IAMTimelineSrc_SpliceWithNext(IAMTimelineSrc* This,IAMTimelineObj *next) {
    return This->lpVtbl->SpliceWithNext(This,next);
}
static inline HRESULT IAMTimelineSrc_GetStreamNumber(IAMTimelineSrc* This,LONG *num) {
    return This->lpVtbl->GetStreamNumber(This,num);
}
static inline HRESULT IAMTimelineSrc_SetStreamNumber(IAMTimelineSrc* This,LONG num) {
    return This->lpVtbl->SetStreamNumber(This,num);
}
static inline HRESULT IAMTimelineSrc_IsNormalRate(IAMTimelineSrc* This,WINBOOL *normal) {
    return This->lpVtbl->IsNormalRate(This,normal);
}
static inline HRESULT IAMTimelineSrc_GetDefaultFPS(IAMTimelineSrc* This,double *fps) {
    return This->lpVtbl->GetDefaultFPS(This,fps);
}
static inline HRESULT IAMTimelineSrc_SetDefaultFPS(IAMTimelineSrc* This,double fps) {
    return This->lpVtbl->SetDefaultFPS(This,fps);
}
static inline HRESULT IAMTimelineSrc_GetStretchMode(IAMTimelineSrc* This,int *mode) {
    return This->lpVtbl->GetStretchMode(This,mode);
}
static inline HRESULT IAMTimelineSrc_SetStretchMode(IAMTimelineSrc* This,int mode) {
    return This->lpVtbl->SetStretchMode(This,mode);
}
#endif
#endif

#endif


#endif  /* __IAMTimelineSrc_INTERFACE_DEFINED__ */

enum {
    E_NOTINTREE = 0x80040400,
    E_RENDER_ENGINE_IS_BROKEN = 0x80040401,
    E_MUST_INIT_RENDERER = 0x80040402,
    E_NOTDETERMINED = 0x80040403,
    E_NO_TIMELINE = 0x80040404,
    S_WARN_OUTPUTRESET = 40404
};
/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER BSTR_UserSize     (ULONG *, ULONG, BSTR *);
unsigned char * __RPC_USER BSTR_UserMarshal  (ULONG *, unsigned char *, BSTR *);
unsigned char * __RPC_USER BSTR_UserUnmarshal(ULONG *, unsigned char *, BSTR *);
void            __RPC_USER BSTR_UserFree     (ULONG *, BSTR *);
ULONG           __RPC_USER VARIANT_UserSize     (ULONG *, ULONG, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserMarshal  (ULONG *, unsigned char *, VARIANT *);
unsigned char * __RPC_USER VARIANT_UserUnmarshal(ULONG *, unsigned char *, VARIANT *);
void            __RPC_USER VARIANT_UserFree     (ULONG *, VARIANT *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __qedit_h__ */
