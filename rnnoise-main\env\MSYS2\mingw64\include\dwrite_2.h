/*** Autogenerated by WIDL 10.12 from include/dwrite_2.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __dwrite_2_h__
#define __dwrite_2_h__

/* Forward declarations */

#ifndef __IDWriteTextRenderer1_FWD_DEFINED__
#define __IDWriteTextRenderer1_FWD_DEFINED__
typedef interface IDWriteTextRenderer1 IDWriteTextRenderer1;
#ifdef __cplusplus
interface IDWriteTextRenderer1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontFallback_FWD_DEFINED__
#define __IDWriteFontFallback_FWD_DEFINED__
typedef interface IDWriteFontFallback IDWriteFontFallback;
#ifdef __cplusplus
interface IDWriteFontFallback;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextFormat1_FWD_DEFINED__
#define __IDWriteTextFormat1_FWD_DEFINED__
typedef interface IDWriteTextFormat1 IDWriteTextFormat1;
#ifdef __cplusplus
interface IDWriteTextFormat1;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextLayout2_FWD_DEFINED__
#define __IDWriteTextLayout2_FWD_DEFINED__
typedef interface IDWriteTextLayout2 IDWriteTextLayout2;
#ifdef __cplusplus
interface IDWriteTextLayout2;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteTextAnalyzer2_FWD_DEFINED__
#define __IDWriteTextAnalyzer2_FWD_DEFINED__
typedef interface IDWriteTextAnalyzer2 IDWriteTextAnalyzer2;
#ifdef __cplusplus
interface IDWriteTextAnalyzer2;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontFallbackBuilder_FWD_DEFINED__
#define __IDWriteFontFallbackBuilder_FWD_DEFINED__
typedef interface IDWriteFontFallbackBuilder IDWriteFontFallbackBuilder;
#ifdef __cplusplus
interface IDWriteFontFallbackBuilder;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFont2_FWD_DEFINED__
#define __IDWriteFont2_FWD_DEFINED__
typedef interface IDWriteFont2 IDWriteFont2;
#ifdef __cplusplus
interface IDWriteFont2;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFontFace2_FWD_DEFINED__
#define __IDWriteFontFace2_FWD_DEFINED__
typedef interface IDWriteFontFace2 IDWriteFontFace2;
#ifdef __cplusplus
interface IDWriteFontFace2;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteColorGlyphRunEnumerator_FWD_DEFINED__
#define __IDWriteColorGlyphRunEnumerator_FWD_DEFINED__
typedef interface IDWriteColorGlyphRunEnumerator IDWriteColorGlyphRunEnumerator;
#ifdef __cplusplus
interface IDWriteColorGlyphRunEnumerator;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteRenderingParams2_FWD_DEFINED__
#define __IDWriteRenderingParams2_FWD_DEFINED__
typedef interface IDWriteRenderingParams2 IDWriteRenderingParams2;
#ifdef __cplusplus
interface IDWriteRenderingParams2;
#endif /* __cplusplus */
#endif

#ifndef __IDWriteFactory2_FWD_DEFINED__
#define __IDWriteFactory2_FWD_DEFINED__
typedef interface IDWriteFactory2 IDWriteFactory2;
#ifdef __cplusplus
interface IDWriteFactory2;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <dwrite_1.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum DWRITE_OPTICAL_ALIGNMENT {
    DWRITE_OPTICAL_ALIGNMENT_NONE = 0,
    DWRITE_OPTICAL_ALIGNMENT_NO_SIDE_BEARINGS = 1
} DWRITE_OPTICAL_ALIGNMENT;
typedef enum DWRITE_GRID_FIT_MODE {
    DWRITE_GRID_FIT_MODE_DEFAULT = 0,
    DWRITE_GRID_FIT_MODE_DISABLED = 1,
    DWRITE_GRID_FIT_MODE_ENABLED = 2
} DWRITE_GRID_FIT_MODE;
typedef struct DWRITE_TEXT_METRICS1 {
    FLOAT left;
    FLOAT top;
    FLOAT width;
    FLOAT widthIncludingTrailingWhitespace;
    FLOAT height;
    FLOAT layoutWidth;
    FLOAT layoutHeight;
    UINT32 maxBidiReorderingDepth;
    UINT32 lineCount;
    FLOAT heightIncludingTrailingWhitespace;
} DWRITE_TEXT_METRICS1;
#ifndef D3DCOLORVALUE_DEFINED
typedef struct _D3DCOLORVALUE {
    __C89_NAMELESS union {
        FLOAT r;
        FLOAT dvR;
    } __C89_NAMELESSUNIONNAME1;
    __C89_NAMELESS union {
        FLOAT g;
        FLOAT dvG;
    } __C89_NAMELESSUNIONNAME2;
    __C89_NAMELESS union {
        FLOAT b;
        FLOAT dvB;
    } __C89_NAMELESSUNIONNAME3;
    __C89_NAMELESS union {
        FLOAT a;
        FLOAT dvA;
    } __C89_NAMELESSUNIONNAME4;
} D3DCOLORVALUE;
#define D3DCOLORVALUE_DEFINED
#endif
typedef D3DCOLORVALUE DWRITE_COLOR_F;
typedef struct DWRITE_COLOR_GLYPH_RUN {
    DWRITE_GLYPH_RUN glyphRun;
    DWRITE_GLYPH_RUN_DESCRIPTION *glyphRunDescription;
    FLOAT baselineOriginX;
    FLOAT baselineOriginY;
    DWRITE_COLOR_F runColor;
    UINT16 paletteIndex;
} DWRITE_COLOR_GLYPH_RUN;
/*****************************************************************************
 * IDWriteTextRenderer1 interface
 */
#ifndef __IDWriteTextRenderer1_INTERFACE_DEFINED__
#define __IDWriteTextRenderer1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextRenderer1, 0xd3e0e934, 0x22a0, 0x427e, 0xaa,0xe4, 0x7d,0x95,0x74,0xb5,0x9d,0xb1);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d3e0e934-22a0-427e-aae4-7d9574b59db1")
IDWriteTextRenderer1 : public IDWriteTextRenderer
{
    virtual HRESULT STDMETHODCALLTYPE DrawGlyphRun(
        void *context,
        FLOAT originX,
        FLOAT originY,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        DWRITE_MEASURING_MODE mode,
        const DWRITE_GLYPH_RUN *run,
        const DWRITE_GLYPH_RUN_DESCRIPTION *rundescr,
        IUnknown *effect) = 0;

    virtual HRESULT STDMETHODCALLTYPE DrawUnderline(
        void *context,
        FLOAT originX,
        FLOAT originY,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        const DWRITE_UNDERLINE *underline,
        IUnknown *effect) = 0;

    virtual HRESULT STDMETHODCALLTYPE DrawStrikethrough(
        void *context,
        FLOAT originX,
        FLOAT originY,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        const DWRITE_STRIKETHROUGH *strikethrough,
        IUnknown *effect) = 0;

    virtual HRESULT STDMETHODCALLTYPE DrawInlineObject(
        void *context,
        FLOAT originX,
        FLOAT originY,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        IDWriteInlineObject *inlineObject,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        IUnknown *effect) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextRenderer1, 0xd3e0e934, 0x22a0, 0x427e, 0xaa,0xe4, 0x7d,0x95,0x74,0xb5,0x9d,0xb1)
#endif
#else
typedef struct IDWriteTextRenderer1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextRenderer1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextRenderer1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextRenderer1 *This);

    /*** IDWritePixelSnapping methods ***/
    HRESULT (STDMETHODCALLTYPE *IsPixelSnappingDisabled)(
        IDWriteTextRenderer1 *This,
        void *client_drawingcontext,
        WINBOOL *disabled);

    HRESULT (STDMETHODCALLTYPE *GetCurrentTransform)(
        IDWriteTextRenderer1 *This,
        void *client_drawingcontext,
        DWRITE_MATRIX *transform);

    HRESULT (STDMETHODCALLTYPE *GetPixelsPerDip)(
        IDWriteTextRenderer1 *This,
        void *client_drawingcontext,
        FLOAT *pixels_per_dip);

    /*** IDWriteTextRenderer methods ***/
    HRESULT (STDMETHODCALLTYPE *DrawGlyphRun)(
        IDWriteTextRenderer1 *This,
        void *client_drawingcontext,
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        DWRITE_MEASURING_MODE mode,
        const DWRITE_GLYPH_RUN *glyph_run,
        const DWRITE_GLYPH_RUN_DESCRIPTION *run_descr,
        IUnknown *drawing_effect);

    HRESULT (STDMETHODCALLTYPE *DrawUnderline)(
        IDWriteTextRenderer1 *This,
        void *client_drawingcontext,
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        const DWRITE_UNDERLINE *underline,
        IUnknown *drawing_effect);

    HRESULT (STDMETHODCALLTYPE *DrawStrikethrough)(
        IDWriteTextRenderer1 *This,
        void *client_drawingcontext,
        FLOAT baselineOriginX,
        FLOAT baselineOriginY,
        const DWRITE_STRIKETHROUGH *strikethrough,
        IUnknown *drawing_effect);

    HRESULT (STDMETHODCALLTYPE *DrawInlineObject)(
        IDWriteTextRenderer1 *This,
        void *client_drawingcontext,
        FLOAT originX,
        FLOAT originY,
        IDWriteInlineObject *object,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        IUnknown *drawing_effect);

    /*** IDWriteTextRenderer1 methods ***/
    HRESULT (STDMETHODCALLTYPE *IDWriteTextRenderer1_DrawGlyphRun)(
        IDWriteTextRenderer1 *This,
        void *context,
        FLOAT originX,
        FLOAT originY,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        DWRITE_MEASURING_MODE mode,
        const DWRITE_GLYPH_RUN *run,
        const DWRITE_GLYPH_RUN_DESCRIPTION *rundescr,
        IUnknown *effect);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextRenderer1_DrawUnderline)(
        IDWriteTextRenderer1 *This,
        void *context,
        FLOAT originX,
        FLOAT originY,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        const DWRITE_UNDERLINE *underline,
        IUnknown *effect);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextRenderer1_DrawStrikethrough)(
        IDWriteTextRenderer1 *This,
        void *context,
        FLOAT originX,
        FLOAT originY,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        const DWRITE_STRIKETHROUGH *strikethrough,
        IUnknown *effect);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextRenderer1_DrawInlineObject)(
        IDWriteTextRenderer1 *This,
        void *context,
        FLOAT originX,
        FLOAT originY,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        IDWriteInlineObject *inlineObject,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        IUnknown *effect);

    END_INTERFACE
} IDWriteTextRenderer1Vtbl;

interface IDWriteTextRenderer1 {
    CONST_VTBL IDWriteTextRenderer1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextRenderer1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextRenderer1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextRenderer1_Release(This) (This)->lpVtbl->Release(This)
/*** IDWritePixelSnapping methods ***/
#define IDWriteTextRenderer1_IsPixelSnappingDisabled(This,client_drawingcontext,disabled) (This)->lpVtbl->IsPixelSnappingDisabled(This,client_drawingcontext,disabled)
#define IDWriteTextRenderer1_GetCurrentTransform(This,client_drawingcontext,transform) (This)->lpVtbl->GetCurrentTransform(This,client_drawingcontext,transform)
#define IDWriteTextRenderer1_GetPixelsPerDip(This,client_drawingcontext,pixels_per_dip) (This)->lpVtbl->GetPixelsPerDip(This,client_drawingcontext,pixels_per_dip)
/*** IDWriteTextRenderer methods ***/
/*** IDWriteTextRenderer1 methods ***/
#define IDWriteTextRenderer1_DrawGlyphRun(This,context,originX,originY,angle,mode,run,rundescr,effect) (This)->lpVtbl->IDWriteTextRenderer1_DrawGlyphRun(This,context,originX,originY,angle,mode,run,rundescr,effect)
#define IDWriteTextRenderer1_DrawUnderline(This,context,originX,originY,angle,underline,effect) (This)->lpVtbl->IDWriteTextRenderer1_DrawUnderline(This,context,originX,originY,angle,underline,effect)
#define IDWriteTextRenderer1_DrawStrikethrough(This,context,originX,originY,angle,strikethrough,effect) (This)->lpVtbl->IDWriteTextRenderer1_DrawStrikethrough(This,context,originX,originY,angle,strikethrough,effect)
#define IDWriteTextRenderer1_DrawInlineObject(This,context,originX,originY,angle,inlineObject,is_sideways,is_rtl,effect) (This)->lpVtbl->IDWriteTextRenderer1_DrawInlineObject(This,context,originX,originY,angle,inlineObject,is_sideways,is_rtl,effect)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextRenderer1_QueryInterface(IDWriteTextRenderer1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextRenderer1_AddRef(IDWriteTextRenderer1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextRenderer1_Release(IDWriteTextRenderer1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWritePixelSnapping methods ***/
static inline HRESULT IDWriteTextRenderer1_IsPixelSnappingDisabled(IDWriteTextRenderer1* This,void *client_drawingcontext,WINBOOL *disabled) {
    return This->lpVtbl->IsPixelSnappingDisabled(This,client_drawingcontext,disabled);
}
static inline HRESULT IDWriteTextRenderer1_GetCurrentTransform(IDWriteTextRenderer1* This,void *client_drawingcontext,DWRITE_MATRIX *transform) {
    return This->lpVtbl->GetCurrentTransform(This,client_drawingcontext,transform);
}
static inline HRESULT IDWriteTextRenderer1_GetPixelsPerDip(IDWriteTextRenderer1* This,void *client_drawingcontext,FLOAT *pixels_per_dip) {
    return This->lpVtbl->GetPixelsPerDip(This,client_drawingcontext,pixels_per_dip);
}
/*** IDWriteTextRenderer methods ***/
/*** IDWriteTextRenderer1 methods ***/
static inline HRESULT IDWriteTextRenderer1_DrawGlyphRun(IDWriteTextRenderer1* This,void *context,FLOAT originX,FLOAT originY,DWRITE_GLYPH_ORIENTATION_ANGLE angle,DWRITE_MEASURING_MODE mode,const DWRITE_GLYPH_RUN *run,const DWRITE_GLYPH_RUN_DESCRIPTION *rundescr,IUnknown *effect) {
    return This->lpVtbl->IDWriteTextRenderer1_DrawGlyphRun(This,context,originX,originY,angle,mode,run,rundescr,effect);
}
static inline HRESULT IDWriteTextRenderer1_DrawUnderline(IDWriteTextRenderer1* This,void *context,FLOAT originX,FLOAT originY,DWRITE_GLYPH_ORIENTATION_ANGLE angle,const DWRITE_UNDERLINE *underline,IUnknown *effect) {
    return This->lpVtbl->IDWriteTextRenderer1_DrawUnderline(This,context,originX,originY,angle,underline,effect);
}
static inline HRESULT IDWriteTextRenderer1_DrawStrikethrough(IDWriteTextRenderer1* This,void *context,FLOAT originX,FLOAT originY,DWRITE_GLYPH_ORIENTATION_ANGLE angle,const DWRITE_STRIKETHROUGH *strikethrough,IUnknown *effect) {
    return This->lpVtbl->IDWriteTextRenderer1_DrawStrikethrough(This,context,originX,originY,angle,strikethrough,effect);
}
static inline HRESULT IDWriteTextRenderer1_DrawInlineObject(IDWriteTextRenderer1* This,void *context,FLOAT originX,FLOAT originY,DWRITE_GLYPH_ORIENTATION_ANGLE angle,IDWriteInlineObject *inlineObject,WINBOOL is_sideways,WINBOOL is_rtl,IUnknown *effect) {
    return This->lpVtbl->IDWriteTextRenderer1_DrawInlineObject(This,context,originX,originY,angle,inlineObject,is_sideways,is_rtl,effect);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextRenderer1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontFallback interface
 */
#ifndef __IDWriteFontFallback_INTERFACE_DEFINED__
#define __IDWriteFontFallback_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontFallback, 0xefa008f9, 0xf7a1, 0x48bf, 0xb0,0x5c, 0xf2,0x24,0x71,0x3c,0xc0,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("efa008f9-f7a1-48bf-b05c-f224713cc0ff")
IDWriteFontFallback : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MapCharacters(
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteFontCollection *basecollection,
        const WCHAR *baseFamilyName,
        DWRITE_FONT_WEIGHT baseWeight,
        DWRITE_FONT_STYLE baseStyle,
        DWRITE_FONT_STRETCH baseStretch,
        UINT32 *mappedLength,
        IDWriteFont **mappedFont,
        FLOAT *scale) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontFallback, 0xefa008f9, 0xf7a1, 0x48bf, 0xb0,0x5c, 0xf2,0x24,0x71,0x3c,0xc0,0xff)
#endif
#else
typedef struct IDWriteFontFallbackVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontFallback *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontFallback *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontFallback *This);

    /*** IDWriteFontFallback methods ***/
    HRESULT (STDMETHODCALLTYPE *MapCharacters)(
        IDWriteFontFallback *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteFontCollection *basecollection,
        const WCHAR *baseFamilyName,
        DWRITE_FONT_WEIGHT baseWeight,
        DWRITE_FONT_STYLE baseStyle,
        DWRITE_FONT_STRETCH baseStretch,
        UINT32 *mappedLength,
        IDWriteFont **mappedFont,
        FLOAT *scale);

    END_INTERFACE
} IDWriteFontFallbackVtbl;

interface IDWriteFontFallback {
    CONST_VTBL IDWriteFontFallbackVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontFallback_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontFallback_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontFallback_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontFallback methods ***/
#define IDWriteFontFallback_MapCharacters(This,source,position,length,basecollection,baseFamilyName,baseWeight,baseStyle,baseStretch,mappedLength,mappedFont,scale) (This)->lpVtbl->MapCharacters(This,source,position,length,basecollection,baseFamilyName,baseWeight,baseStyle,baseStretch,mappedLength,mappedFont,scale)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontFallback_QueryInterface(IDWriteFontFallback* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontFallback_AddRef(IDWriteFontFallback* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontFallback_Release(IDWriteFontFallback* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontFallback methods ***/
static inline HRESULT IDWriteFontFallback_MapCharacters(IDWriteFontFallback* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteFontCollection *basecollection,const WCHAR *baseFamilyName,DWRITE_FONT_WEIGHT baseWeight,DWRITE_FONT_STYLE baseStyle,DWRITE_FONT_STRETCH baseStretch,UINT32 *mappedLength,IDWriteFont **mappedFont,FLOAT *scale) {
    return This->lpVtbl->MapCharacters(This,source,position,length,basecollection,baseFamilyName,baseWeight,baseStyle,baseStretch,mappedLength,mappedFont,scale);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontFallback_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextFormat1 interface
 */
#ifndef __IDWriteTextFormat1_INTERFACE_DEFINED__
#define __IDWriteTextFormat1_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextFormat1, 0x5f174b49, 0x0d8b, 0x4cfb, 0x8b,0xca, 0xf1,0xcc,0xe9,0xd0,0x6c,0x67);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5f174b49-0d8b-4cfb-8bca-f1cce9d06c67")
IDWriteTextFormat1 : public IDWriteTextFormat
{
    virtual HRESULT STDMETHODCALLTYPE SetVerticalGlyphOrientation(
        DWRITE_VERTICAL_GLYPH_ORIENTATION orientation) = 0;

    virtual DWRITE_VERTICAL_GLYPH_ORIENTATION STDMETHODCALLTYPE GetVerticalGlyphOrientation(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLastLineWrapping(
        WINBOOL lastline_wrapping_enabled) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetLastLineWrapping(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOpticalAlignment(
        DWRITE_OPTICAL_ALIGNMENT alignment) = 0;

    virtual DWRITE_OPTICAL_ALIGNMENT STDMETHODCALLTYPE GetOpticalAlignment(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFontFallback(
        IDWriteFontFallback *fallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontFallback(
        IDWriteFontFallback **fallback) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextFormat1, 0x5f174b49, 0x0d8b, 0x4cfb, 0x8b,0xca, 0xf1,0xcc,0xe9,0xd0,0x6c,0x67)
#endif
#else
typedef struct IDWriteTextFormat1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextFormat1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextFormat1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextFormat1 *This);

    /*** IDWriteTextFormat methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTextAlignment)(
        IDWriteTextFormat1 *This,
        DWRITE_TEXT_ALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *SetParagraphAlignment)(
        IDWriteTextFormat1 *This,
        DWRITE_PARAGRAPH_ALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *SetWordWrapping)(
        IDWriteTextFormat1 *This,
        DWRITE_WORD_WRAPPING wrapping);

    HRESULT (STDMETHODCALLTYPE *SetReadingDirection)(
        IDWriteTextFormat1 *This,
        DWRITE_READING_DIRECTION direction);

    HRESULT (STDMETHODCALLTYPE *SetFlowDirection)(
        IDWriteTextFormat1 *This,
        DWRITE_FLOW_DIRECTION direction);

    HRESULT (STDMETHODCALLTYPE *SetIncrementalTabStop)(
        IDWriteTextFormat1 *This,
        FLOAT tabstop);

    HRESULT (STDMETHODCALLTYPE *SetTrimming)(
        IDWriteTextFormat1 *This,
        const DWRITE_TRIMMING *trimming,
        IDWriteInlineObject *trimming_sign);

    HRESULT (STDMETHODCALLTYPE *SetLineSpacing)(
        IDWriteTextFormat1 *This,
        DWRITE_LINE_SPACING_METHOD spacing,
        FLOAT line_spacing,
        FLOAT baseline);

    DWRITE_TEXT_ALIGNMENT (STDMETHODCALLTYPE *GetTextAlignment)(
        IDWriteTextFormat1 *This);

    DWRITE_PARAGRAPH_ALIGNMENT (STDMETHODCALLTYPE *GetParagraphAlignment)(
        IDWriteTextFormat1 *This);

    DWRITE_WORD_WRAPPING (STDMETHODCALLTYPE *GetWordWrapping)(
        IDWriteTextFormat1 *This);

    DWRITE_READING_DIRECTION (STDMETHODCALLTYPE *GetReadingDirection)(
        IDWriteTextFormat1 *This);

    DWRITE_FLOW_DIRECTION (STDMETHODCALLTYPE *GetFlowDirection)(
        IDWriteTextFormat1 *This);

    FLOAT (STDMETHODCALLTYPE *GetIncrementalTabStop)(
        IDWriteTextFormat1 *This);

    HRESULT (STDMETHODCALLTYPE *GetTrimming)(
        IDWriteTextFormat1 *This,
        DWRITE_TRIMMING *options,
        IDWriteInlineObject **trimming_sign);

    HRESULT (STDMETHODCALLTYPE *GetLineSpacing)(
        IDWriteTextFormat1 *This,
        DWRITE_LINE_SPACING_METHOD *method,
        FLOAT *spacing,
        FLOAT *baseline);

    HRESULT (STDMETHODCALLTYPE *GetFontCollection)(
        IDWriteTextFormat1 *This,
        IDWriteFontCollection **collection);

    UINT32 (STDMETHODCALLTYPE *GetFontFamilyNameLength)(
        IDWriteTextFormat1 *This);

    HRESULT (STDMETHODCALLTYPE *GetFontFamilyName)(
        IDWriteTextFormat1 *This,
        WCHAR *name,
        UINT32 size);

    DWRITE_FONT_WEIGHT (STDMETHODCALLTYPE *GetFontWeight)(
        IDWriteTextFormat1 *This);

    DWRITE_FONT_STYLE (STDMETHODCALLTYPE *GetFontStyle)(
        IDWriteTextFormat1 *This);

    DWRITE_FONT_STRETCH (STDMETHODCALLTYPE *GetFontStretch)(
        IDWriteTextFormat1 *This);

    FLOAT (STDMETHODCALLTYPE *GetFontSize)(
        IDWriteTextFormat1 *This);

    UINT32 (STDMETHODCALLTYPE *GetLocaleNameLength)(
        IDWriteTextFormat1 *This);

    HRESULT (STDMETHODCALLTYPE *GetLocaleName)(
        IDWriteTextFormat1 *This,
        WCHAR *name,
        UINT32 size);

    /*** IDWriteTextFormat1 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetVerticalGlyphOrientation)(
        IDWriteTextFormat1 *This,
        DWRITE_VERTICAL_GLYPH_ORIENTATION orientation);

    DWRITE_VERTICAL_GLYPH_ORIENTATION (STDMETHODCALLTYPE *GetVerticalGlyphOrientation)(
        IDWriteTextFormat1 *This);

    HRESULT (STDMETHODCALLTYPE *SetLastLineWrapping)(
        IDWriteTextFormat1 *This,
        WINBOOL lastline_wrapping_enabled);

    WINBOOL (STDMETHODCALLTYPE *GetLastLineWrapping)(
        IDWriteTextFormat1 *This);

    HRESULT (STDMETHODCALLTYPE *SetOpticalAlignment)(
        IDWriteTextFormat1 *This,
        DWRITE_OPTICAL_ALIGNMENT alignment);

    DWRITE_OPTICAL_ALIGNMENT (STDMETHODCALLTYPE *GetOpticalAlignment)(
        IDWriteTextFormat1 *This);

    HRESULT (STDMETHODCALLTYPE *SetFontFallback)(
        IDWriteTextFormat1 *This,
        IDWriteFontFallback *fallback);

    HRESULT (STDMETHODCALLTYPE *GetFontFallback)(
        IDWriteTextFormat1 *This,
        IDWriteFontFallback **fallback);

    END_INTERFACE
} IDWriteTextFormat1Vtbl;

interface IDWriteTextFormat1 {
    CONST_VTBL IDWriteTextFormat1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextFormat1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextFormat1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextFormat1_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextFormat methods ***/
#define IDWriteTextFormat1_SetTextAlignment(This,alignment) (This)->lpVtbl->SetTextAlignment(This,alignment)
#define IDWriteTextFormat1_SetParagraphAlignment(This,alignment) (This)->lpVtbl->SetParagraphAlignment(This,alignment)
#define IDWriteTextFormat1_SetWordWrapping(This,wrapping) (This)->lpVtbl->SetWordWrapping(This,wrapping)
#define IDWriteTextFormat1_SetReadingDirection(This,direction) (This)->lpVtbl->SetReadingDirection(This,direction)
#define IDWriteTextFormat1_SetFlowDirection(This,direction) (This)->lpVtbl->SetFlowDirection(This,direction)
#define IDWriteTextFormat1_SetIncrementalTabStop(This,tabstop) (This)->lpVtbl->SetIncrementalTabStop(This,tabstop)
#define IDWriteTextFormat1_SetTrimming(This,trimming,trimming_sign) (This)->lpVtbl->SetTrimming(This,trimming,trimming_sign)
#define IDWriteTextFormat1_SetLineSpacing(This,spacing,line_spacing,baseline) (This)->lpVtbl->SetLineSpacing(This,spacing,line_spacing,baseline)
#define IDWriteTextFormat1_GetTextAlignment(This) (This)->lpVtbl->GetTextAlignment(This)
#define IDWriteTextFormat1_GetParagraphAlignment(This) (This)->lpVtbl->GetParagraphAlignment(This)
#define IDWriteTextFormat1_GetWordWrapping(This) (This)->lpVtbl->GetWordWrapping(This)
#define IDWriteTextFormat1_GetReadingDirection(This) (This)->lpVtbl->GetReadingDirection(This)
#define IDWriteTextFormat1_GetFlowDirection(This) (This)->lpVtbl->GetFlowDirection(This)
#define IDWriteTextFormat1_GetIncrementalTabStop(This) (This)->lpVtbl->GetIncrementalTabStop(This)
#define IDWriteTextFormat1_GetTrimming(This,options,trimming_sign) (This)->lpVtbl->GetTrimming(This,options,trimming_sign)
#define IDWriteTextFormat1_GetLineSpacing(This,method,spacing,baseline) (This)->lpVtbl->GetLineSpacing(This,method,spacing,baseline)
#define IDWriteTextFormat1_GetFontCollection(This,collection) (This)->lpVtbl->GetFontCollection(This,collection)
#define IDWriteTextFormat1_GetFontFamilyNameLength(This) (This)->lpVtbl->GetFontFamilyNameLength(This)
#define IDWriteTextFormat1_GetFontFamilyName(This,name,size) (This)->lpVtbl->GetFontFamilyName(This,name,size)
#define IDWriteTextFormat1_GetFontWeight(This) (This)->lpVtbl->GetFontWeight(This)
#define IDWriteTextFormat1_GetFontStyle(This) (This)->lpVtbl->GetFontStyle(This)
#define IDWriteTextFormat1_GetFontStretch(This) (This)->lpVtbl->GetFontStretch(This)
#define IDWriteTextFormat1_GetFontSize(This) (This)->lpVtbl->GetFontSize(This)
#define IDWriteTextFormat1_GetLocaleNameLength(This) (This)->lpVtbl->GetLocaleNameLength(This)
#define IDWriteTextFormat1_GetLocaleName(This,name,size) (This)->lpVtbl->GetLocaleName(This,name,size)
/*** IDWriteTextFormat1 methods ***/
#define IDWriteTextFormat1_SetVerticalGlyphOrientation(This,orientation) (This)->lpVtbl->SetVerticalGlyphOrientation(This,orientation)
#define IDWriteTextFormat1_GetVerticalGlyphOrientation(This) (This)->lpVtbl->GetVerticalGlyphOrientation(This)
#define IDWriteTextFormat1_SetLastLineWrapping(This,lastline_wrapping_enabled) (This)->lpVtbl->SetLastLineWrapping(This,lastline_wrapping_enabled)
#define IDWriteTextFormat1_GetLastLineWrapping(This) (This)->lpVtbl->GetLastLineWrapping(This)
#define IDWriteTextFormat1_SetOpticalAlignment(This,alignment) (This)->lpVtbl->SetOpticalAlignment(This,alignment)
#define IDWriteTextFormat1_GetOpticalAlignment(This) (This)->lpVtbl->GetOpticalAlignment(This)
#define IDWriteTextFormat1_SetFontFallback(This,fallback) (This)->lpVtbl->SetFontFallback(This,fallback)
#define IDWriteTextFormat1_GetFontFallback(This,fallback) (This)->lpVtbl->GetFontFallback(This,fallback)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextFormat1_QueryInterface(IDWriteTextFormat1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextFormat1_AddRef(IDWriteTextFormat1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextFormat1_Release(IDWriteTextFormat1* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextFormat methods ***/
static inline HRESULT IDWriteTextFormat1_SetTextAlignment(IDWriteTextFormat1* This,DWRITE_TEXT_ALIGNMENT alignment) {
    return This->lpVtbl->SetTextAlignment(This,alignment);
}
static inline HRESULT IDWriteTextFormat1_SetParagraphAlignment(IDWriteTextFormat1* This,DWRITE_PARAGRAPH_ALIGNMENT alignment) {
    return This->lpVtbl->SetParagraphAlignment(This,alignment);
}
static inline HRESULT IDWriteTextFormat1_SetWordWrapping(IDWriteTextFormat1* This,DWRITE_WORD_WRAPPING wrapping) {
    return This->lpVtbl->SetWordWrapping(This,wrapping);
}
static inline HRESULT IDWriteTextFormat1_SetReadingDirection(IDWriteTextFormat1* This,DWRITE_READING_DIRECTION direction) {
    return This->lpVtbl->SetReadingDirection(This,direction);
}
static inline HRESULT IDWriteTextFormat1_SetFlowDirection(IDWriteTextFormat1* This,DWRITE_FLOW_DIRECTION direction) {
    return This->lpVtbl->SetFlowDirection(This,direction);
}
static inline HRESULT IDWriteTextFormat1_SetIncrementalTabStop(IDWriteTextFormat1* This,FLOAT tabstop) {
    return This->lpVtbl->SetIncrementalTabStop(This,tabstop);
}
static inline HRESULT IDWriteTextFormat1_SetTrimming(IDWriteTextFormat1* This,const DWRITE_TRIMMING *trimming,IDWriteInlineObject *trimming_sign) {
    return This->lpVtbl->SetTrimming(This,trimming,trimming_sign);
}
static inline HRESULT IDWriteTextFormat1_SetLineSpacing(IDWriteTextFormat1* This,DWRITE_LINE_SPACING_METHOD spacing,FLOAT line_spacing,FLOAT baseline) {
    return This->lpVtbl->SetLineSpacing(This,spacing,line_spacing,baseline);
}
static inline DWRITE_TEXT_ALIGNMENT IDWriteTextFormat1_GetTextAlignment(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetTextAlignment(This);
}
static inline DWRITE_PARAGRAPH_ALIGNMENT IDWriteTextFormat1_GetParagraphAlignment(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetParagraphAlignment(This);
}
static inline DWRITE_WORD_WRAPPING IDWriteTextFormat1_GetWordWrapping(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetWordWrapping(This);
}
static inline DWRITE_READING_DIRECTION IDWriteTextFormat1_GetReadingDirection(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetReadingDirection(This);
}
static inline DWRITE_FLOW_DIRECTION IDWriteTextFormat1_GetFlowDirection(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetFlowDirection(This);
}
static inline FLOAT IDWriteTextFormat1_GetIncrementalTabStop(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetIncrementalTabStop(This);
}
static inline HRESULT IDWriteTextFormat1_GetTrimming(IDWriteTextFormat1* This,DWRITE_TRIMMING *options,IDWriteInlineObject **trimming_sign) {
    return This->lpVtbl->GetTrimming(This,options,trimming_sign);
}
static inline HRESULT IDWriteTextFormat1_GetLineSpacing(IDWriteTextFormat1* This,DWRITE_LINE_SPACING_METHOD *method,FLOAT *spacing,FLOAT *baseline) {
    return This->lpVtbl->GetLineSpacing(This,method,spacing,baseline);
}
static inline HRESULT IDWriteTextFormat1_GetFontCollection(IDWriteTextFormat1* This,IDWriteFontCollection **collection) {
    return This->lpVtbl->GetFontCollection(This,collection);
}
static inline UINT32 IDWriteTextFormat1_GetFontFamilyNameLength(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetFontFamilyNameLength(This);
}
static inline HRESULT IDWriteTextFormat1_GetFontFamilyName(IDWriteTextFormat1* This,WCHAR *name,UINT32 size) {
    return This->lpVtbl->GetFontFamilyName(This,name,size);
}
static inline DWRITE_FONT_WEIGHT IDWriteTextFormat1_GetFontWeight(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetFontWeight(This);
}
static inline DWRITE_FONT_STYLE IDWriteTextFormat1_GetFontStyle(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetFontStyle(This);
}
static inline DWRITE_FONT_STRETCH IDWriteTextFormat1_GetFontStretch(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetFontStretch(This);
}
static inline FLOAT IDWriteTextFormat1_GetFontSize(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetFontSize(This);
}
static inline UINT32 IDWriteTextFormat1_GetLocaleNameLength(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetLocaleNameLength(This);
}
static inline HRESULT IDWriteTextFormat1_GetLocaleName(IDWriteTextFormat1* This,WCHAR *name,UINT32 size) {
    return This->lpVtbl->GetLocaleName(This,name,size);
}
/*** IDWriteTextFormat1 methods ***/
static inline HRESULT IDWriteTextFormat1_SetVerticalGlyphOrientation(IDWriteTextFormat1* This,DWRITE_VERTICAL_GLYPH_ORIENTATION orientation) {
    return This->lpVtbl->SetVerticalGlyphOrientation(This,orientation);
}
static inline DWRITE_VERTICAL_GLYPH_ORIENTATION IDWriteTextFormat1_GetVerticalGlyphOrientation(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetVerticalGlyphOrientation(This);
}
static inline HRESULT IDWriteTextFormat1_SetLastLineWrapping(IDWriteTextFormat1* This,WINBOOL lastline_wrapping_enabled) {
    return This->lpVtbl->SetLastLineWrapping(This,lastline_wrapping_enabled);
}
static inline WINBOOL IDWriteTextFormat1_GetLastLineWrapping(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetLastLineWrapping(This);
}
static inline HRESULT IDWriteTextFormat1_SetOpticalAlignment(IDWriteTextFormat1* This,DWRITE_OPTICAL_ALIGNMENT alignment) {
    return This->lpVtbl->SetOpticalAlignment(This,alignment);
}
static inline DWRITE_OPTICAL_ALIGNMENT IDWriteTextFormat1_GetOpticalAlignment(IDWriteTextFormat1* This) {
    return This->lpVtbl->GetOpticalAlignment(This);
}
static inline HRESULT IDWriteTextFormat1_SetFontFallback(IDWriteTextFormat1* This,IDWriteFontFallback *fallback) {
    return This->lpVtbl->SetFontFallback(This,fallback);
}
static inline HRESULT IDWriteTextFormat1_GetFontFallback(IDWriteTextFormat1* This,IDWriteFontFallback **fallback) {
    return This->lpVtbl->GetFontFallback(This,fallback);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextFormat1_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextLayout2 interface
 */
#ifndef __IDWriteTextLayout2_INTERFACE_DEFINED__
#define __IDWriteTextLayout2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextLayout2, 0x1093c18f, 0x8d5e, 0x43f0, 0xb0,0x64, 0x09,0x17,0x31,0x1b,0x52,0x5e);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1093c18f-8d5e-43f0-b064-0917311b525e")
IDWriteTextLayout2 : public IDWriteTextLayout1
{
    virtual HRESULT STDMETHODCALLTYPE GetMetrics(
        DWRITE_TEXT_METRICS1 *metrics) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetVerticalGlyphOrientation(
        DWRITE_VERTICAL_GLYPH_ORIENTATION orientation) = 0;

    virtual DWRITE_VERTICAL_GLYPH_ORIENTATION STDMETHODCALLTYPE GetVerticalGlyphOrientation(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetLastLineWrapping(
        WINBOOL lastline_wrapping_enabled) = 0;

    virtual WINBOOL STDMETHODCALLTYPE GetLastLineWrapping(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetOpticalAlignment(
        DWRITE_OPTICAL_ALIGNMENT alignment) = 0;

    virtual DWRITE_OPTICAL_ALIGNMENT STDMETHODCALLTYPE GetOpticalAlignment(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE SetFontFallback(
        IDWriteFontFallback *fallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetFontFallback(
        IDWriteFontFallback **fallback) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextLayout2, 0x1093c18f, 0x8d5e, 0x43f0, 0xb0,0x64, 0x09,0x17,0x31,0x1b,0x52,0x5e)
#endif
#else
typedef struct IDWriteTextLayout2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextLayout2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextLayout2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextLayout2 *This);

    /*** IDWriteTextFormat methods ***/
    HRESULT (STDMETHODCALLTYPE *SetTextAlignment)(
        IDWriteTextLayout2 *This,
        DWRITE_TEXT_ALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *SetParagraphAlignment)(
        IDWriteTextLayout2 *This,
        DWRITE_PARAGRAPH_ALIGNMENT alignment);

    HRESULT (STDMETHODCALLTYPE *SetWordWrapping)(
        IDWriteTextLayout2 *This,
        DWRITE_WORD_WRAPPING wrapping);

    HRESULT (STDMETHODCALLTYPE *SetReadingDirection)(
        IDWriteTextLayout2 *This,
        DWRITE_READING_DIRECTION direction);

    HRESULT (STDMETHODCALLTYPE *SetFlowDirection)(
        IDWriteTextLayout2 *This,
        DWRITE_FLOW_DIRECTION direction);

    HRESULT (STDMETHODCALLTYPE *SetIncrementalTabStop)(
        IDWriteTextLayout2 *This,
        FLOAT tabstop);

    HRESULT (STDMETHODCALLTYPE *SetTrimming)(
        IDWriteTextLayout2 *This,
        const DWRITE_TRIMMING *trimming,
        IDWriteInlineObject *trimming_sign);

    HRESULT (STDMETHODCALLTYPE *SetLineSpacing)(
        IDWriteTextLayout2 *This,
        DWRITE_LINE_SPACING_METHOD spacing,
        FLOAT line_spacing,
        FLOAT baseline);

    DWRITE_TEXT_ALIGNMENT (STDMETHODCALLTYPE *GetTextAlignment)(
        IDWriteTextLayout2 *This);

    DWRITE_PARAGRAPH_ALIGNMENT (STDMETHODCALLTYPE *GetParagraphAlignment)(
        IDWriteTextLayout2 *This);

    DWRITE_WORD_WRAPPING (STDMETHODCALLTYPE *GetWordWrapping)(
        IDWriteTextLayout2 *This);

    DWRITE_READING_DIRECTION (STDMETHODCALLTYPE *GetReadingDirection)(
        IDWriteTextLayout2 *This);

    DWRITE_FLOW_DIRECTION (STDMETHODCALLTYPE *GetFlowDirection)(
        IDWriteTextLayout2 *This);

    FLOAT (STDMETHODCALLTYPE *GetIncrementalTabStop)(
        IDWriteTextLayout2 *This);

    HRESULT (STDMETHODCALLTYPE *GetTrimming)(
        IDWriteTextLayout2 *This,
        DWRITE_TRIMMING *options,
        IDWriteInlineObject **trimming_sign);

    HRESULT (STDMETHODCALLTYPE *GetLineSpacing)(
        IDWriteTextLayout2 *This,
        DWRITE_LINE_SPACING_METHOD *method,
        FLOAT *spacing,
        FLOAT *baseline);

    HRESULT (STDMETHODCALLTYPE *GetFontCollection)(
        IDWriteTextLayout2 *This,
        IDWriteFontCollection **collection);

    UINT32 (STDMETHODCALLTYPE *GetFontFamilyNameLength)(
        IDWriteTextLayout2 *This);

    HRESULT (STDMETHODCALLTYPE *GetFontFamilyName)(
        IDWriteTextLayout2 *This,
        WCHAR *name,
        UINT32 size);

    DWRITE_FONT_WEIGHT (STDMETHODCALLTYPE *GetFontWeight)(
        IDWriteTextLayout2 *This);

    DWRITE_FONT_STYLE (STDMETHODCALLTYPE *GetFontStyle)(
        IDWriteTextLayout2 *This);

    DWRITE_FONT_STRETCH (STDMETHODCALLTYPE *GetFontStretch)(
        IDWriteTextLayout2 *This);

    FLOAT (STDMETHODCALLTYPE *GetFontSize)(
        IDWriteTextLayout2 *This);

    UINT32 (STDMETHODCALLTYPE *GetLocaleNameLength)(
        IDWriteTextLayout2 *This);

    HRESULT (STDMETHODCALLTYPE *GetLocaleName)(
        IDWriteTextLayout2 *This,
        WCHAR *name,
        UINT32 size);

    /*** IDWriteTextLayout methods ***/
    HRESULT (STDMETHODCALLTYPE *SetMaxWidth)(
        IDWriteTextLayout2 *This,
        FLOAT maxWidth);

    HRESULT (STDMETHODCALLTYPE *SetMaxHeight)(
        IDWriteTextLayout2 *This,
        FLOAT maxHeight);

    HRESULT (STDMETHODCALLTYPE *SetFontCollection)(
        IDWriteTextLayout2 *This,
        IDWriteFontCollection *collection,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontFamilyName)(
        IDWriteTextLayout2 *This,
        const WCHAR *name,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontWeight)(
        IDWriteTextLayout2 *This,
        DWRITE_FONT_WEIGHT weight,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontStyle)(
        IDWriteTextLayout2 *This,
        DWRITE_FONT_STYLE style,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontStretch)(
        IDWriteTextLayout2 *This,
        DWRITE_FONT_STRETCH stretch,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetFontSize)(
        IDWriteTextLayout2 *This,
        FLOAT size,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetUnderline)(
        IDWriteTextLayout2 *This,
        WINBOOL underline,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetStrikethrough)(
        IDWriteTextLayout2 *This,
        WINBOOL strikethrough,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetDrawingEffect)(
        IDWriteTextLayout2 *This,
        IUnknown *effect,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetInlineObject)(
        IDWriteTextLayout2 *This,
        IDWriteInlineObject *object,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetTypography)(
        IDWriteTextLayout2 *This,
        IDWriteTypography *typography,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *SetLocaleName)(
        IDWriteTextLayout2 *This,
        const WCHAR *locale,
        DWRITE_TEXT_RANGE range);

    FLOAT (STDMETHODCALLTYPE *GetMaxWidth)(
        IDWriteTextLayout2 *This);

    FLOAT (STDMETHODCALLTYPE *GetMaxHeight)(
        IDWriteTextLayout2 *This);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontCollection)(
        IDWriteTextLayout2 *This,
        UINT32 pos,
        IDWriteFontCollection **collection,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontFamilyNameLength)(
        IDWriteTextLayout2 *This,
        UINT32 pos,
        UINT32 *len,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontFamilyName)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        WCHAR *name,
        UINT32 name_size,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontWeight)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        DWRITE_FONT_WEIGHT *weight,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontStyle)(
        IDWriteTextLayout2 *This,
        UINT32 currentPosition,
        DWRITE_FONT_STYLE *style,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontStretch)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        DWRITE_FONT_STRETCH *stretch,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetFontSize)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        FLOAT *size,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetUnderline)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        WINBOOL *has_underline,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetStrikethrough)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        WINBOOL *has_strikethrough,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetDrawingEffect)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        IUnknown **effect,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetInlineObject)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        IDWriteInlineObject **object,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *GetTypography)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        IDWriteTypography **typography,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetLocaleNameLength)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        UINT32 *length,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout_GetLocaleName)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        WCHAR *name,
        UINT32 name_size,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *Draw)(
        IDWriteTextLayout2 *This,
        void *context,
        IDWriteTextRenderer *renderer,
        FLOAT originX,
        FLOAT originY);

    HRESULT (STDMETHODCALLTYPE *GetLineMetrics)(
        IDWriteTextLayout2 *This,
        DWRITE_LINE_METRICS *metrics,
        UINT32 max_count,
        UINT32 *actual_count);

    HRESULT (STDMETHODCALLTYPE *GetMetrics)(
        IDWriteTextLayout2 *This,
        DWRITE_TEXT_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *GetOverhangMetrics)(
        IDWriteTextLayout2 *This,
        DWRITE_OVERHANG_METRICS *overhangs);

    HRESULT (STDMETHODCALLTYPE *GetClusterMetrics)(
        IDWriteTextLayout2 *This,
        DWRITE_CLUSTER_METRICS *metrics,
        UINT32 max_count,
        UINT32 *act_count);

    HRESULT (STDMETHODCALLTYPE *DetermineMinWidth)(
        IDWriteTextLayout2 *This,
        FLOAT *min_width);

    HRESULT (STDMETHODCALLTYPE *HitTestPoint)(
        IDWriteTextLayout2 *This,
        FLOAT pointX,
        FLOAT pointY,
        WINBOOL *is_trailinghit,
        WINBOOL *is_inside,
        DWRITE_HIT_TEST_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *HitTestTextPosition)(
        IDWriteTextLayout2 *This,
        UINT32 textPosition,
        WINBOOL is_trailinghit,
        FLOAT *pointX,
        FLOAT *pointY,
        DWRITE_HIT_TEST_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *HitTestTextRange)(
        IDWriteTextLayout2 *This,
        UINT32 textPosition,
        UINT32 textLength,
        FLOAT originX,
        FLOAT originY,
        DWRITE_HIT_TEST_METRICS *metrics,
        UINT32 max_metricscount,
        UINT32 *actual_metricscount);

    /*** IDWriteTextLayout1 methods ***/
    HRESULT (STDMETHODCALLTYPE *SetPairKerning)(
        IDWriteTextLayout2 *This,
        WINBOOL is_pairkerning_enabled,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *GetPairKerning)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        WINBOOL *is_pairkerning_enabled,
        DWRITE_TEXT_RANGE *range);

    HRESULT (STDMETHODCALLTYPE *SetCharacterSpacing)(
        IDWriteTextLayout2 *This,
        FLOAT leading_spacing,
        FLOAT trailing_spacing,
        FLOAT minimum_advance_width,
        DWRITE_TEXT_RANGE range);

    HRESULT (STDMETHODCALLTYPE *GetCharacterSpacing)(
        IDWriteTextLayout2 *This,
        UINT32 position,
        FLOAT *leading_spacing,
        FLOAT *trailing_spacing,
        FLOAT *minimum_advance_width,
        DWRITE_TEXT_RANGE *range);

    /*** IDWriteTextLayout2 methods ***/
    HRESULT (STDMETHODCALLTYPE *IDWriteTextLayout2_GetMetrics)(
        IDWriteTextLayout2 *This,
        DWRITE_TEXT_METRICS1 *metrics);

    HRESULT (STDMETHODCALLTYPE *SetVerticalGlyphOrientation)(
        IDWriteTextLayout2 *This,
        DWRITE_VERTICAL_GLYPH_ORIENTATION orientation);

    DWRITE_VERTICAL_GLYPH_ORIENTATION (STDMETHODCALLTYPE *GetVerticalGlyphOrientation)(
        IDWriteTextLayout2 *This);

    HRESULT (STDMETHODCALLTYPE *SetLastLineWrapping)(
        IDWriteTextLayout2 *This,
        WINBOOL lastline_wrapping_enabled);

    WINBOOL (STDMETHODCALLTYPE *GetLastLineWrapping)(
        IDWriteTextLayout2 *This);

    HRESULT (STDMETHODCALLTYPE *SetOpticalAlignment)(
        IDWriteTextLayout2 *This,
        DWRITE_OPTICAL_ALIGNMENT alignment);

    DWRITE_OPTICAL_ALIGNMENT (STDMETHODCALLTYPE *GetOpticalAlignment)(
        IDWriteTextLayout2 *This);

    HRESULT (STDMETHODCALLTYPE *SetFontFallback)(
        IDWriteTextLayout2 *This,
        IDWriteFontFallback *fallback);

    HRESULT (STDMETHODCALLTYPE *GetFontFallback)(
        IDWriteTextLayout2 *This,
        IDWriteFontFallback **fallback);

    END_INTERFACE
} IDWriteTextLayout2Vtbl;

interface IDWriteTextLayout2 {
    CONST_VTBL IDWriteTextLayout2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextLayout2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextLayout2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextLayout2_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextFormat methods ***/
#define IDWriteTextLayout2_SetTextAlignment(This,alignment) (This)->lpVtbl->SetTextAlignment(This,alignment)
#define IDWriteTextLayout2_SetParagraphAlignment(This,alignment) (This)->lpVtbl->SetParagraphAlignment(This,alignment)
#define IDWriteTextLayout2_SetWordWrapping(This,wrapping) (This)->lpVtbl->SetWordWrapping(This,wrapping)
#define IDWriteTextLayout2_SetReadingDirection(This,direction) (This)->lpVtbl->SetReadingDirection(This,direction)
#define IDWriteTextLayout2_SetFlowDirection(This,direction) (This)->lpVtbl->SetFlowDirection(This,direction)
#define IDWriteTextLayout2_SetIncrementalTabStop(This,tabstop) (This)->lpVtbl->SetIncrementalTabStop(This,tabstop)
#define IDWriteTextLayout2_SetTrimming(This,trimming,trimming_sign) (This)->lpVtbl->SetTrimming(This,trimming,trimming_sign)
#define IDWriteTextLayout2_SetLineSpacing(This,spacing,line_spacing,baseline) (This)->lpVtbl->SetLineSpacing(This,spacing,line_spacing,baseline)
#define IDWriteTextLayout2_GetTextAlignment(This) (This)->lpVtbl->GetTextAlignment(This)
#define IDWriteTextLayout2_GetParagraphAlignment(This) (This)->lpVtbl->GetParagraphAlignment(This)
#define IDWriteTextLayout2_GetWordWrapping(This) (This)->lpVtbl->GetWordWrapping(This)
#define IDWriteTextLayout2_GetReadingDirection(This) (This)->lpVtbl->GetReadingDirection(This)
#define IDWriteTextLayout2_GetFlowDirection(This) (This)->lpVtbl->GetFlowDirection(This)
#define IDWriteTextLayout2_GetIncrementalTabStop(This) (This)->lpVtbl->GetIncrementalTabStop(This)
#define IDWriteTextLayout2_GetTrimming(This,options,trimming_sign) (This)->lpVtbl->GetTrimming(This,options,trimming_sign)
#define IDWriteTextLayout2_GetLineSpacing(This,method,spacing,baseline) (This)->lpVtbl->GetLineSpacing(This,method,spacing,baseline)
/*** IDWriteTextLayout methods ***/
#define IDWriteTextLayout2_SetMaxWidth(This,maxWidth) (This)->lpVtbl->SetMaxWidth(This,maxWidth)
#define IDWriteTextLayout2_SetMaxHeight(This,maxHeight) (This)->lpVtbl->SetMaxHeight(This,maxHeight)
#define IDWriteTextLayout2_SetFontCollection(This,collection,range) (This)->lpVtbl->SetFontCollection(This,collection,range)
#define IDWriteTextLayout2_SetFontFamilyName(This,name,range) (This)->lpVtbl->SetFontFamilyName(This,name,range)
#define IDWriteTextLayout2_SetFontWeight(This,weight,range) (This)->lpVtbl->SetFontWeight(This,weight,range)
#define IDWriteTextLayout2_SetFontStyle(This,style,range) (This)->lpVtbl->SetFontStyle(This,style,range)
#define IDWriteTextLayout2_SetFontStretch(This,stretch,range) (This)->lpVtbl->SetFontStretch(This,stretch,range)
#define IDWriteTextLayout2_SetFontSize(This,size,range) (This)->lpVtbl->SetFontSize(This,size,range)
#define IDWriteTextLayout2_SetUnderline(This,underline,range) (This)->lpVtbl->SetUnderline(This,underline,range)
#define IDWriteTextLayout2_SetStrikethrough(This,strikethrough,range) (This)->lpVtbl->SetStrikethrough(This,strikethrough,range)
#define IDWriteTextLayout2_SetDrawingEffect(This,effect,range) (This)->lpVtbl->SetDrawingEffect(This,effect,range)
#define IDWriteTextLayout2_SetInlineObject(This,object,range) (This)->lpVtbl->SetInlineObject(This,object,range)
#define IDWriteTextLayout2_SetTypography(This,typography,range) (This)->lpVtbl->SetTypography(This,typography,range)
#define IDWriteTextLayout2_SetLocaleName(This,locale,range) (This)->lpVtbl->SetLocaleName(This,locale,range)
#define IDWriteTextLayout2_GetMaxWidth(This) (This)->lpVtbl->GetMaxWidth(This)
#define IDWriteTextLayout2_GetMaxHeight(This) (This)->lpVtbl->GetMaxHeight(This)
#define IDWriteTextLayout2_GetFontCollection(This,pos,collection,range) (This)->lpVtbl->IDWriteTextLayout_GetFontCollection(This,pos,collection,range)
#define IDWriteTextLayout2_GetFontFamilyNameLength(This,pos,len,range) (This)->lpVtbl->IDWriteTextLayout_GetFontFamilyNameLength(This,pos,len,range)
#define IDWriteTextLayout2_GetFontFamilyName(This,position,name,name_size,range) (This)->lpVtbl->IDWriteTextLayout_GetFontFamilyName(This,position,name,name_size,range)
#define IDWriteTextLayout2_GetFontWeight(This,position,weight,range) (This)->lpVtbl->IDWriteTextLayout_GetFontWeight(This,position,weight,range)
#define IDWriteTextLayout2_GetFontStyle(This,currentPosition,style,range) (This)->lpVtbl->IDWriteTextLayout_GetFontStyle(This,currentPosition,style,range)
#define IDWriteTextLayout2_GetFontStretch(This,position,stretch,range) (This)->lpVtbl->IDWriteTextLayout_GetFontStretch(This,position,stretch,range)
#define IDWriteTextLayout2_GetFontSize(This,position,size,range) (This)->lpVtbl->IDWriteTextLayout_GetFontSize(This,position,size,range)
#define IDWriteTextLayout2_GetUnderline(This,position,has_underline,range) (This)->lpVtbl->GetUnderline(This,position,has_underline,range)
#define IDWriteTextLayout2_GetStrikethrough(This,position,has_strikethrough,range) (This)->lpVtbl->GetStrikethrough(This,position,has_strikethrough,range)
#define IDWriteTextLayout2_GetDrawingEffect(This,position,effect,range) (This)->lpVtbl->GetDrawingEffect(This,position,effect,range)
#define IDWriteTextLayout2_GetInlineObject(This,position,object,range) (This)->lpVtbl->GetInlineObject(This,position,object,range)
#define IDWriteTextLayout2_GetTypography(This,position,typography,range) (This)->lpVtbl->GetTypography(This,position,typography,range)
#define IDWriteTextLayout2_GetLocaleNameLength(This,position,length,range) (This)->lpVtbl->IDWriteTextLayout_GetLocaleNameLength(This,position,length,range)
#define IDWriteTextLayout2_GetLocaleName(This,position,name,name_size,range) (This)->lpVtbl->IDWriteTextLayout_GetLocaleName(This,position,name,name_size,range)
#define IDWriteTextLayout2_Draw(This,context,renderer,originX,originY) (This)->lpVtbl->Draw(This,context,renderer,originX,originY)
#define IDWriteTextLayout2_GetLineMetrics(This,metrics,max_count,actual_count) (This)->lpVtbl->GetLineMetrics(This,metrics,max_count,actual_count)
#define IDWriteTextLayout2_GetOverhangMetrics(This,overhangs) (This)->lpVtbl->GetOverhangMetrics(This,overhangs)
#define IDWriteTextLayout2_GetClusterMetrics(This,metrics,max_count,act_count) (This)->lpVtbl->GetClusterMetrics(This,metrics,max_count,act_count)
#define IDWriteTextLayout2_DetermineMinWidth(This,min_width) (This)->lpVtbl->DetermineMinWidth(This,min_width)
#define IDWriteTextLayout2_HitTestPoint(This,pointX,pointY,is_trailinghit,is_inside,metrics) (This)->lpVtbl->HitTestPoint(This,pointX,pointY,is_trailinghit,is_inside,metrics)
#define IDWriteTextLayout2_HitTestTextPosition(This,textPosition,is_trailinghit,pointX,pointY,metrics) (This)->lpVtbl->HitTestTextPosition(This,textPosition,is_trailinghit,pointX,pointY,metrics)
#define IDWriteTextLayout2_HitTestTextRange(This,textPosition,textLength,originX,originY,metrics,max_metricscount,actual_metricscount) (This)->lpVtbl->HitTestTextRange(This,textPosition,textLength,originX,originY,metrics,max_metricscount,actual_metricscount)
/*** IDWriteTextLayout1 methods ***/
#define IDWriteTextLayout2_SetPairKerning(This,is_pairkerning_enabled,range) (This)->lpVtbl->SetPairKerning(This,is_pairkerning_enabled,range)
#define IDWriteTextLayout2_GetPairKerning(This,position,is_pairkerning_enabled,range) (This)->lpVtbl->GetPairKerning(This,position,is_pairkerning_enabled,range)
#define IDWriteTextLayout2_SetCharacterSpacing(This,leading_spacing,trailing_spacing,minimum_advance_width,range) (This)->lpVtbl->SetCharacterSpacing(This,leading_spacing,trailing_spacing,minimum_advance_width,range)
#define IDWriteTextLayout2_GetCharacterSpacing(This,position,leading_spacing,trailing_spacing,minimum_advance_width,range) (This)->lpVtbl->GetCharacterSpacing(This,position,leading_spacing,trailing_spacing,minimum_advance_width,range)
/*** IDWriteTextLayout2 methods ***/
#define IDWriteTextLayout2_GetMetrics(This,metrics) (This)->lpVtbl->IDWriteTextLayout2_GetMetrics(This,metrics)
#define IDWriteTextLayout2_SetVerticalGlyphOrientation(This,orientation) (This)->lpVtbl->SetVerticalGlyphOrientation(This,orientation)
#define IDWriteTextLayout2_GetVerticalGlyphOrientation(This) (This)->lpVtbl->GetVerticalGlyphOrientation(This)
#define IDWriteTextLayout2_SetLastLineWrapping(This,lastline_wrapping_enabled) (This)->lpVtbl->SetLastLineWrapping(This,lastline_wrapping_enabled)
#define IDWriteTextLayout2_GetLastLineWrapping(This) (This)->lpVtbl->GetLastLineWrapping(This)
#define IDWriteTextLayout2_SetOpticalAlignment(This,alignment) (This)->lpVtbl->SetOpticalAlignment(This,alignment)
#define IDWriteTextLayout2_GetOpticalAlignment(This) (This)->lpVtbl->GetOpticalAlignment(This)
#define IDWriteTextLayout2_SetFontFallback(This,fallback) (This)->lpVtbl->SetFontFallback(This,fallback)
#define IDWriteTextLayout2_GetFontFallback(This,fallback) (This)->lpVtbl->GetFontFallback(This,fallback)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextLayout2_QueryInterface(IDWriteTextLayout2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextLayout2_AddRef(IDWriteTextLayout2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextLayout2_Release(IDWriteTextLayout2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextFormat methods ***/
static inline HRESULT IDWriteTextLayout2_SetTextAlignment(IDWriteTextLayout2* This,DWRITE_TEXT_ALIGNMENT alignment) {
    return This->lpVtbl->SetTextAlignment(This,alignment);
}
static inline HRESULT IDWriteTextLayout2_SetParagraphAlignment(IDWriteTextLayout2* This,DWRITE_PARAGRAPH_ALIGNMENT alignment) {
    return This->lpVtbl->SetParagraphAlignment(This,alignment);
}
static inline HRESULT IDWriteTextLayout2_SetWordWrapping(IDWriteTextLayout2* This,DWRITE_WORD_WRAPPING wrapping) {
    return This->lpVtbl->SetWordWrapping(This,wrapping);
}
static inline HRESULT IDWriteTextLayout2_SetReadingDirection(IDWriteTextLayout2* This,DWRITE_READING_DIRECTION direction) {
    return This->lpVtbl->SetReadingDirection(This,direction);
}
static inline HRESULT IDWriteTextLayout2_SetFlowDirection(IDWriteTextLayout2* This,DWRITE_FLOW_DIRECTION direction) {
    return This->lpVtbl->SetFlowDirection(This,direction);
}
static inline HRESULT IDWriteTextLayout2_SetIncrementalTabStop(IDWriteTextLayout2* This,FLOAT tabstop) {
    return This->lpVtbl->SetIncrementalTabStop(This,tabstop);
}
static inline HRESULT IDWriteTextLayout2_SetTrimming(IDWriteTextLayout2* This,const DWRITE_TRIMMING *trimming,IDWriteInlineObject *trimming_sign) {
    return This->lpVtbl->SetTrimming(This,trimming,trimming_sign);
}
static inline HRESULT IDWriteTextLayout2_SetLineSpacing(IDWriteTextLayout2* This,DWRITE_LINE_SPACING_METHOD spacing,FLOAT line_spacing,FLOAT baseline) {
    return This->lpVtbl->SetLineSpacing(This,spacing,line_spacing,baseline);
}
static inline DWRITE_TEXT_ALIGNMENT IDWriteTextLayout2_GetTextAlignment(IDWriteTextLayout2* This) {
    return This->lpVtbl->GetTextAlignment(This);
}
static inline DWRITE_PARAGRAPH_ALIGNMENT IDWriteTextLayout2_GetParagraphAlignment(IDWriteTextLayout2* This) {
    return This->lpVtbl->GetParagraphAlignment(This);
}
static inline DWRITE_WORD_WRAPPING IDWriteTextLayout2_GetWordWrapping(IDWriteTextLayout2* This) {
    return This->lpVtbl->GetWordWrapping(This);
}
static inline DWRITE_READING_DIRECTION IDWriteTextLayout2_GetReadingDirection(IDWriteTextLayout2* This) {
    return This->lpVtbl->GetReadingDirection(This);
}
static inline DWRITE_FLOW_DIRECTION IDWriteTextLayout2_GetFlowDirection(IDWriteTextLayout2* This) {
    return This->lpVtbl->GetFlowDirection(This);
}
static inline FLOAT IDWriteTextLayout2_GetIncrementalTabStop(IDWriteTextLayout2* This) {
    return This->lpVtbl->GetIncrementalTabStop(This);
}
static inline HRESULT IDWriteTextLayout2_GetTrimming(IDWriteTextLayout2* This,DWRITE_TRIMMING *options,IDWriteInlineObject **trimming_sign) {
    return This->lpVtbl->GetTrimming(This,options,trimming_sign);
}
static inline HRESULT IDWriteTextLayout2_GetLineSpacing(IDWriteTextLayout2* This,DWRITE_LINE_SPACING_METHOD *method,FLOAT *spacing,FLOAT *baseline) {
    return This->lpVtbl->GetLineSpacing(This,method,spacing,baseline);
}
/*** IDWriteTextLayout methods ***/
static inline HRESULT IDWriteTextLayout2_SetMaxWidth(IDWriteTextLayout2* This,FLOAT maxWidth) {
    return This->lpVtbl->SetMaxWidth(This,maxWidth);
}
static inline HRESULT IDWriteTextLayout2_SetMaxHeight(IDWriteTextLayout2* This,FLOAT maxHeight) {
    return This->lpVtbl->SetMaxHeight(This,maxHeight);
}
static inline HRESULT IDWriteTextLayout2_SetFontCollection(IDWriteTextLayout2* This,IDWriteFontCollection *collection,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontCollection(This,collection,range);
}
static inline HRESULT IDWriteTextLayout2_SetFontFamilyName(IDWriteTextLayout2* This,const WCHAR *name,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontFamilyName(This,name,range);
}
static inline HRESULT IDWriteTextLayout2_SetFontWeight(IDWriteTextLayout2* This,DWRITE_FONT_WEIGHT weight,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontWeight(This,weight,range);
}
static inline HRESULT IDWriteTextLayout2_SetFontStyle(IDWriteTextLayout2* This,DWRITE_FONT_STYLE style,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontStyle(This,style,range);
}
static inline HRESULT IDWriteTextLayout2_SetFontStretch(IDWriteTextLayout2* This,DWRITE_FONT_STRETCH stretch,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontStretch(This,stretch,range);
}
static inline HRESULT IDWriteTextLayout2_SetFontSize(IDWriteTextLayout2* This,FLOAT size,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetFontSize(This,size,range);
}
static inline HRESULT IDWriteTextLayout2_SetUnderline(IDWriteTextLayout2* This,WINBOOL underline,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetUnderline(This,underline,range);
}
static inline HRESULT IDWriteTextLayout2_SetStrikethrough(IDWriteTextLayout2* This,WINBOOL strikethrough,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetStrikethrough(This,strikethrough,range);
}
static inline HRESULT IDWriteTextLayout2_SetDrawingEffect(IDWriteTextLayout2* This,IUnknown *effect,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetDrawingEffect(This,effect,range);
}
static inline HRESULT IDWriteTextLayout2_SetInlineObject(IDWriteTextLayout2* This,IDWriteInlineObject *object,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetInlineObject(This,object,range);
}
static inline HRESULT IDWriteTextLayout2_SetTypography(IDWriteTextLayout2* This,IDWriteTypography *typography,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetTypography(This,typography,range);
}
static inline HRESULT IDWriteTextLayout2_SetLocaleName(IDWriteTextLayout2* This,const WCHAR *locale,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetLocaleName(This,locale,range);
}
static inline FLOAT IDWriteTextLayout2_GetMaxWidth(IDWriteTextLayout2* This) {
    return This->lpVtbl->GetMaxWidth(This);
}
static inline FLOAT IDWriteTextLayout2_GetMaxHeight(IDWriteTextLayout2* This) {
    return This->lpVtbl->GetMaxHeight(This);
}
static inline HRESULT IDWriteTextLayout2_GetFontCollection(IDWriteTextLayout2* This,UINT32 pos,IDWriteFontCollection **collection,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontCollection(This,pos,collection,range);
}
static inline HRESULT IDWriteTextLayout2_GetFontFamilyNameLength(IDWriteTextLayout2* This,UINT32 pos,UINT32 *len,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontFamilyNameLength(This,pos,len,range);
}
static inline HRESULT IDWriteTextLayout2_GetFontFamilyName(IDWriteTextLayout2* This,UINT32 position,WCHAR *name,UINT32 name_size,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontFamilyName(This,position,name,name_size,range);
}
static inline HRESULT IDWriteTextLayout2_GetFontWeight(IDWriteTextLayout2* This,UINT32 position,DWRITE_FONT_WEIGHT *weight,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontWeight(This,position,weight,range);
}
static inline HRESULT IDWriteTextLayout2_GetFontStyle(IDWriteTextLayout2* This,UINT32 currentPosition,DWRITE_FONT_STYLE *style,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontStyle(This,currentPosition,style,range);
}
static inline HRESULT IDWriteTextLayout2_GetFontStretch(IDWriteTextLayout2* This,UINT32 position,DWRITE_FONT_STRETCH *stretch,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontStretch(This,position,stretch,range);
}
static inline HRESULT IDWriteTextLayout2_GetFontSize(IDWriteTextLayout2* This,UINT32 position,FLOAT *size,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetFontSize(This,position,size,range);
}
static inline HRESULT IDWriteTextLayout2_GetUnderline(IDWriteTextLayout2* This,UINT32 position,WINBOOL *has_underline,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetUnderline(This,position,has_underline,range);
}
static inline HRESULT IDWriteTextLayout2_GetStrikethrough(IDWriteTextLayout2* This,UINT32 position,WINBOOL *has_strikethrough,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetStrikethrough(This,position,has_strikethrough,range);
}
static inline HRESULT IDWriteTextLayout2_GetDrawingEffect(IDWriteTextLayout2* This,UINT32 position,IUnknown **effect,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetDrawingEffect(This,position,effect,range);
}
static inline HRESULT IDWriteTextLayout2_GetInlineObject(IDWriteTextLayout2* This,UINT32 position,IDWriteInlineObject **object,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetInlineObject(This,position,object,range);
}
static inline HRESULT IDWriteTextLayout2_GetTypography(IDWriteTextLayout2* This,UINT32 position,IDWriteTypography **typography,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetTypography(This,position,typography,range);
}
static inline HRESULT IDWriteTextLayout2_GetLocaleNameLength(IDWriteTextLayout2* This,UINT32 position,UINT32 *length,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetLocaleNameLength(This,position,length,range);
}
static inline HRESULT IDWriteTextLayout2_GetLocaleName(IDWriteTextLayout2* This,UINT32 position,WCHAR *name,UINT32 name_size,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->IDWriteTextLayout_GetLocaleName(This,position,name,name_size,range);
}
static inline HRESULT IDWriteTextLayout2_Draw(IDWriteTextLayout2* This,void *context,IDWriteTextRenderer *renderer,FLOAT originX,FLOAT originY) {
    return This->lpVtbl->Draw(This,context,renderer,originX,originY);
}
static inline HRESULT IDWriteTextLayout2_GetLineMetrics(IDWriteTextLayout2* This,DWRITE_LINE_METRICS *metrics,UINT32 max_count,UINT32 *actual_count) {
    return This->lpVtbl->GetLineMetrics(This,metrics,max_count,actual_count);
}
static inline HRESULT IDWriteTextLayout2_GetOverhangMetrics(IDWriteTextLayout2* This,DWRITE_OVERHANG_METRICS *overhangs) {
    return This->lpVtbl->GetOverhangMetrics(This,overhangs);
}
static inline HRESULT IDWriteTextLayout2_GetClusterMetrics(IDWriteTextLayout2* This,DWRITE_CLUSTER_METRICS *metrics,UINT32 max_count,UINT32 *act_count) {
    return This->lpVtbl->GetClusterMetrics(This,metrics,max_count,act_count);
}
static inline HRESULT IDWriteTextLayout2_DetermineMinWidth(IDWriteTextLayout2* This,FLOAT *min_width) {
    return This->lpVtbl->DetermineMinWidth(This,min_width);
}
static inline HRESULT IDWriteTextLayout2_HitTestPoint(IDWriteTextLayout2* This,FLOAT pointX,FLOAT pointY,WINBOOL *is_trailinghit,WINBOOL *is_inside,DWRITE_HIT_TEST_METRICS *metrics) {
    return This->lpVtbl->HitTestPoint(This,pointX,pointY,is_trailinghit,is_inside,metrics);
}
static inline HRESULT IDWriteTextLayout2_HitTestTextPosition(IDWriteTextLayout2* This,UINT32 textPosition,WINBOOL is_trailinghit,FLOAT *pointX,FLOAT *pointY,DWRITE_HIT_TEST_METRICS *metrics) {
    return This->lpVtbl->HitTestTextPosition(This,textPosition,is_trailinghit,pointX,pointY,metrics);
}
static inline HRESULT IDWriteTextLayout2_HitTestTextRange(IDWriteTextLayout2* This,UINT32 textPosition,UINT32 textLength,FLOAT originX,FLOAT originY,DWRITE_HIT_TEST_METRICS *metrics,UINT32 max_metricscount,UINT32 *actual_metricscount) {
    return This->lpVtbl->HitTestTextRange(This,textPosition,textLength,originX,originY,metrics,max_metricscount,actual_metricscount);
}
/*** IDWriteTextLayout1 methods ***/
static inline HRESULT IDWriteTextLayout2_SetPairKerning(IDWriteTextLayout2* This,WINBOOL is_pairkerning_enabled,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetPairKerning(This,is_pairkerning_enabled,range);
}
static inline HRESULT IDWriteTextLayout2_GetPairKerning(IDWriteTextLayout2* This,UINT32 position,WINBOOL *is_pairkerning_enabled,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetPairKerning(This,position,is_pairkerning_enabled,range);
}
static inline HRESULT IDWriteTextLayout2_SetCharacterSpacing(IDWriteTextLayout2* This,FLOAT leading_spacing,FLOAT trailing_spacing,FLOAT minimum_advance_width,DWRITE_TEXT_RANGE range) {
    return This->lpVtbl->SetCharacterSpacing(This,leading_spacing,trailing_spacing,minimum_advance_width,range);
}
static inline HRESULT IDWriteTextLayout2_GetCharacterSpacing(IDWriteTextLayout2* This,UINT32 position,FLOAT *leading_spacing,FLOAT *trailing_spacing,FLOAT *minimum_advance_width,DWRITE_TEXT_RANGE *range) {
    return This->lpVtbl->GetCharacterSpacing(This,position,leading_spacing,trailing_spacing,minimum_advance_width,range);
}
/*** IDWriteTextLayout2 methods ***/
static inline HRESULT IDWriteTextLayout2_GetMetrics(IDWriteTextLayout2* This,DWRITE_TEXT_METRICS1 *metrics) {
    return This->lpVtbl->IDWriteTextLayout2_GetMetrics(This,metrics);
}
static inline HRESULT IDWriteTextLayout2_SetVerticalGlyphOrientation(IDWriteTextLayout2* This,DWRITE_VERTICAL_GLYPH_ORIENTATION orientation) {
    return This->lpVtbl->SetVerticalGlyphOrientation(This,orientation);
}
static inline DWRITE_VERTICAL_GLYPH_ORIENTATION IDWriteTextLayout2_GetVerticalGlyphOrientation(IDWriteTextLayout2* This) {
    return This->lpVtbl->GetVerticalGlyphOrientation(This);
}
static inline HRESULT IDWriteTextLayout2_SetLastLineWrapping(IDWriteTextLayout2* This,WINBOOL lastline_wrapping_enabled) {
    return This->lpVtbl->SetLastLineWrapping(This,lastline_wrapping_enabled);
}
static inline WINBOOL IDWriteTextLayout2_GetLastLineWrapping(IDWriteTextLayout2* This) {
    return This->lpVtbl->GetLastLineWrapping(This);
}
static inline HRESULT IDWriteTextLayout2_SetOpticalAlignment(IDWriteTextLayout2* This,DWRITE_OPTICAL_ALIGNMENT alignment) {
    return This->lpVtbl->SetOpticalAlignment(This,alignment);
}
static inline DWRITE_OPTICAL_ALIGNMENT IDWriteTextLayout2_GetOpticalAlignment(IDWriteTextLayout2* This) {
    return This->lpVtbl->GetOpticalAlignment(This);
}
static inline HRESULT IDWriteTextLayout2_SetFontFallback(IDWriteTextLayout2* This,IDWriteFontFallback *fallback) {
    return This->lpVtbl->SetFontFallback(This,fallback);
}
static inline HRESULT IDWriteTextLayout2_GetFontFallback(IDWriteTextLayout2* This,IDWriteFontFallback **fallback) {
    return This->lpVtbl->GetFontFallback(This,fallback);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextLayout2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteTextAnalyzer2 interface
 */
#ifndef __IDWriteTextAnalyzer2_INTERFACE_DEFINED__
#define __IDWriteTextAnalyzer2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteTextAnalyzer2, 0x553a9ff3, 0x5693, 0x4df7, 0xb5,0x2b, 0x74,0x80,0x6f,0x7f,0x2e,0xb9);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("553a9ff3-5693-4df7-b52b-74806f7f2eb9")
IDWriteTextAnalyzer2 : public IDWriteTextAnalyzer1
{
    virtual HRESULT STDMETHODCALLTYPE GetGlyphOrientationTransform(
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        WINBOOL is_sideways,
        FLOAT originX,
        FLOAT originY,
        DWRITE_MATRIX *transform) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetTypographicFeatures(
        IDWriteFontFace *fontface,
        DWRITE_SCRIPT_ANALYSIS analysis,
        const WCHAR *localeName,
        UINT32 max_tagcount,
        UINT32 *actual_tagcount,
        DWRITE_FONT_FEATURE_TAG *tags) = 0;

    virtual HRESULT STDMETHODCALLTYPE CheckTypographicFeature(
        IDWriteFontFace *fontface,
        DWRITE_SCRIPT_ANALYSIS analysis,
        const WCHAR *localeName,
        DWRITE_FONT_FEATURE_TAG feature,
        UINT32 glyph_count,
        const UINT16 *indices,
        UINT8 *feature_applies) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteTextAnalyzer2, 0x553a9ff3, 0x5693, 0x4df7, 0xb5,0x2b, 0x74,0x80,0x6f,0x7f,0x2e,0xb9)
#endif
#else
typedef struct IDWriteTextAnalyzer2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteTextAnalyzer2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteTextAnalyzer2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteTextAnalyzer2 *This);

    /*** IDWriteTextAnalyzer methods ***/
    HRESULT (STDMETHODCALLTYPE *AnalyzeScript)(
        IDWriteTextAnalyzer2 *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *AnalyzeBidi)(
        IDWriteTextAnalyzer2 *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *AnalyzeNumberSubstitution)(
        IDWriteTextAnalyzer2 *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *AnalyzeLineBreakpoints)(
        IDWriteTextAnalyzer2 *This,
        IDWriteTextAnalysisSource *source,
        UINT32 position,
        UINT32 length,
        IDWriteTextAnalysisSink *sink);

    HRESULT (STDMETHODCALLTYPE *GetGlyphs)(
        IDWriteTextAnalyzer2 *This,
        const WCHAR *text,
        UINT32 length,
        IDWriteFontFace *font_face,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        IDWriteNumberSubstitution *substitution,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_len,
        UINT32 feature_ranges,
        UINT32 max_glyph_count,
        UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *text_props,
        UINT16 *glyph_indices,
        DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 *actual_glyph_count);

    HRESULT (STDMETHODCALLTYPE *GetGlyphPlacements)(
        IDWriteTextAnalyzer2 *This,
        const WCHAR *text,
        const UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *props,
        UINT32 text_len,
        const UINT16 *glyph_indices,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 glyph_count,
        IDWriteFontFace *font_face,
        FLOAT fontEmSize,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_len,
        UINT32 feature_ranges,
        FLOAT *glyph_advances,
        DWRITE_GLYPH_OFFSET *glyph_offsets);

    HRESULT (STDMETHODCALLTYPE *GetGdiCompatibleGlyphPlacements)(
        IDWriteTextAnalyzer2 *This,
        const WCHAR *text,
        const UINT16 *clustermap,
        DWRITE_SHAPING_TEXT_PROPERTIES *props,
        UINT32 text_len,
        const UINT16 *glyph_indices,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,
        UINT32 glyph_count,
        IDWriteFontFace *font_face,
        FLOAT fontEmSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        const DWRITE_SCRIPT_ANALYSIS *analysis,
        const WCHAR *locale,
        const DWRITE_TYPOGRAPHIC_FEATURES **features,
        const UINT32 *feature_range_lengths,
        UINT32 feature_ranges,
        FLOAT *glyph_advances,
        DWRITE_GLYPH_OFFSET *glyph_offsets);

    /*** IDWriteTextAnalyzer1 methods ***/
    HRESULT (STDMETHODCALLTYPE *ApplyCharacterSpacing)(
        IDWriteTextAnalyzer2 *This,
        FLOAT leading_spacing,
        FLOAT trailing_spacing,
        FLOAT min_advance_width,
        UINT32 len,
        UINT32 glyph_count,
        const UINT16 *clustermap,
        const FLOAT *advances,
        const DWRITE_GLYPH_OFFSET *offsets,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *props,
        FLOAT *modified_advances,
        DWRITE_GLYPH_OFFSET *modified_offsets);

    HRESULT (STDMETHODCALLTYPE *GetBaseline)(
        IDWriteTextAnalyzer2 *This,
        IDWriteFontFace *face,
        DWRITE_BASELINE baseline,
        WINBOOL vertical,
        WINBOOL is_simulation_allowed,
        DWRITE_SCRIPT_ANALYSIS sa,
        const WCHAR *localeName,
        INT32 *baseline_coord,
        WINBOOL *exists);

    HRESULT (STDMETHODCALLTYPE *AnalyzeVerticalGlyphOrientation)(
        IDWriteTextAnalyzer2 *This,
        IDWriteTextAnalysisSource1 *source,
        UINT32 text_pos,
        UINT32 len,
        IDWriteTextAnalysisSink1 *sink);

    HRESULT (STDMETHODCALLTYPE *GetGlyphOrientationTransform)(
        IDWriteTextAnalyzer2 *This,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        WINBOOL is_sideways,
        DWRITE_MATRIX *transform);

    HRESULT (STDMETHODCALLTYPE *GetScriptProperties)(
        IDWriteTextAnalyzer2 *This,
        DWRITE_SCRIPT_ANALYSIS sa,
        DWRITE_SCRIPT_PROPERTIES *props);

    HRESULT (STDMETHODCALLTYPE *GetTextComplexity)(
        IDWriteTextAnalyzer2 *This,
        const WCHAR *text,
        UINT32 len,
        IDWriteFontFace *face,
        WINBOOL *is_simple,
        UINT32 *len_read,
        UINT16 *indices);

    HRESULT (STDMETHODCALLTYPE *GetJustificationOpportunities)(
        IDWriteTextAnalyzer2 *This,
        IDWriteFontFace *face,
        FLOAT font_em_size,
        DWRITE_SCRIPT_ANALYSIS sa,
        UINT32 length,
        UINT32 glyph_count,
        const WCHAR *text,
        const UINT16 *clustermap,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,
        DWRITE_JUSTIFICATION_OPPORTUNITY *jo);

    HRESULT (STDMETHODCALLTYPE *JustifyGlyphAdvances)(
        IDWriteTextAnalyzer2 *This,
        FLOAT width,
        UINT32 glyph_count,
        const DWRITE_JUSTIFICATION_OPPORTUNITY *jo,
        const FLOAT *advances,
        const DWRITE_GLYPH_OFFSET *offsets,
        FLOAT *justifiedadvances,
        DWRITE_GLYPH_OFFSET *justifiedoffsets);

    HRESULT (STDMETHODCALLTYPE *GetJustifiedGlyphs)(
        IDWriteTextAnalyzer2 *This,
        IDWriteFontFace *face,
        FLOAT font_em_size,
        DWRITE_SCRIPT_ANALYSIS sa,
        UINT32 length,
        UINT32 glyph_count,
        UINT32 max_glyphcount,
        const UINT16 *clustermap,
        const UINT16 *indices,
        const FLOAT *advances,
        const FLOAT *justifiedadvances,
        const DWRITE_GLYPH_OFFSET *justifiedoffsets,
        const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,
        UINT32 *actual_count,
        UINT16 *modified_clustermap,
        UINT16 *modified_indices,
        FLOAT *modified_advances,
        DWRITE_GLYPH_OFFSET *modified_offsets);

    /*** IDWriteTextAnalyzer2 methods ***/
    HRESULT (STDMETHODCALLTYPE *IDWriteTextAnalyzer2_GetGlyphOrientationTransform)(
        IDWriteTextAnalyzer2 *This,
        DWRITE_GLYPH_ORIENTATION_ANGLE angle,
        WINBOOL is_sideways,
        FLOAT originX,
        FLOAT originY,
        DWRITE_MATRIX *transform);

    HRESULT (STDMETHODCALLTYPE *GetTypographicFeatures)(
        IDWriteTextAnalyzer2 *This,
        IDWriteFontFace *fontface,
        DWRITE_SCRIPT_ANALYSIS analysis,
        const WCHAR *localeName,
        UINT32 max_tagcount,
        UINT32 *actual_tagcount,
        DWRITE_FONT_FEATURE_TAG *tags);

    HRESULT (STDMETHODCALLTYPE *CheckTypographicFeature)(
        IDWriteTextAnalyzer2 *This,
        IDWriteFontFace *fontface,
        DWRITE_SCRIPT_ANALYSIS analysis,
        const WCHAR *localeName,
        DWRITE_FONT_FEATURE_TAG feature,
        UINT32 glyph_count,
        const UINT16 *indices,
        UINT8 *feature_applies);

    END_INTERFACE
} IDWriteTextAnalyzer2Vtbl;

interface IDWriteTextAnalyzer2 {
    CONST_VTBL IDWriteTextAnalyzer2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteTextAnalyzer2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteTextAnalyzer2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteTextAnalyzer2_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteTextAnalyzer methods ***/
#define IDWriteTextAnalyzer2_AnalyzeScript(This,source,position,length,sink) (This)->lpVtbl->AnalyzeScript(This,source,position,length,sink)
#define IDWriteTextAnalyzer2_AnalyzeBidi(This,source,position,length,sink) (This)->lpVtbl->AnalyzeBidi(This,source,position,length,sink)
#define IDWriteTextAnalyzer2_AnalyzeNumberSubstitution(This,source,position,length,sink) (This)->lpVtbl->AnalyzeNumberSubstitution(This,source,position,length,sink)
#define IDWriteTextAnalyzer2_AnalyzeLineBreakpoints(This,source,position,length,sink) (This)->lpVtbl->AnalyzeLineBreakpoints(This,source,position,length,sink)
#define IDWriteTextAnalyzer2_GetGlyphs(This,text,length,font_face,is_sideways,is_rtl,analysis,locale,substitution,features,feature_range_len,feature_ranges,max_glyph_count,clustermap,text_props,glyph_indices,glyph_props,actual_glyph_count) (This)->lpVtbl->GetGlyphs(This,text,length,font_face,is_sideways,is_rtl,analysis,locale,substitution,features,feature_range_len,feature_ranges,max_glyph_count,clustermap,text_props,glyph_indices,glyph_props,actual_glyph_count)
#define IDWriteTextAnalyzer2_GetGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,is_sideways,is_rtl,analysis,locale,features,feature_range_len,feature_ranges,glyph_advances,glyph_offsets) (This)->lpVtbl->GetGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,is_sideways,is_rtl,analysis,locale,features,feature_range_len,feature_ranges,glyph_advances,glyph_offsets)
#define IDWriteTextAnalyzer2_GetGdiCompatibleGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,pixels_per_dip,transform,use_gdi_natural,is_sideways,is_rtl,analysis,locale,features,feature_range_lengths,feature_ranges,glyph_advances,glyph_offsets) (This)->lpVtbl->GetGdiCompatibleGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,pixels_per_dip,transform,use_gdi_natural,is_sideways,is_rtl,analysis,locale,features,feature_range_lengths,feature_ranges,glyph_advances,glyph_offsets)
/*** IDWriteTextAnalyzer1 methods ***/
#define IDWriteTextAnalyzer2_ApplyCharacterSpacing(This,leading_spacing,trailing_spacing,min_advance_width,len,glyph_count,clustermap,advances,offsets,props,modified_advances,modified_offsets) (This)->lpVtbl->ApplyCharacterSpacing(This,leading_spacing,trailing_spacing,min_advance_width,len,glyph_count,clustermap,advances,offsets,props,modified_advances,modified_offsets)
#define IDWriteTextAnalyzer2_GetBaseline(This,face,baseline,vertical,is_simulation_allowed,sa,localeName,baseline_coord,exists) (This)->lpVtbl->GetBaseline(This,face,baseline,vertical,is_simulation_allowed,sa,localeName,baseline_coord,exists)
#define IDWriteTextAnalyzer2_AnalyzeVerticalGlyphOrientation(This,source,text_pos,len,sink) (This)->lpVtbl->AnalyzeVerticalGlyphOrientation(This,source,text_pos,len,sink)
#define IDWriteTextAnalyzer2_GetScriptProperties(This,sa,props) (This)->lpVtbl->GetScriptProperties(This,sa,props)
#define IDWriteTextAnalyzer2_GetTextComplexity(This,text,len,face,is_simple,len_read,indices) (This)->lpVtbl->GetTextComplexity(This,text,len,face,is_simple,len_read,indices)
#define IDWriteTextAnalyzer2_GetJustificationOpportunities(This,face,font_em_size,sa,length,glyph_count,text,clustermap,prop,jo) (This)->lpVtbl->GetJustificationOpportunities(This,face,font_em_size,sa,length,glyph_count,text,clustermap,prop,jo)
#define IDWriteTextAnalyzer2_JustifyGlyphAdvances(This,width,glyph_count,jo,advances,offsets,justifiedadvances,justifiedoffsets) (This)->lpVtbl->JustifyGlyphAdvances(This,width,glyph_count,jo,advances,offsets,justifiedadvances,justifiedoffsets)
#define IDWriteTextAnalyzer2_GetJustifiedGlyphs(This,face,font_em_size,sa,length,glyph_count,max_glyphcount,clustermap,indices,advances,justifiedadvances,justifiedoffsets,prop,actual_count,modified_clustermap,modified_indices,modified_advances,modified_offsets) (This)->lpVtbl->GetJustifiedGlyphs(This,face,font_em_size,sa,length,glyph_count,max_glyphcount,clustermap,indices,advances,justifiedadvances,justifiedoffsets,prop,actual_count,modified_clustermap,modified_indices,modified_advances,modified_offsets)
/*** IDWriteTextAnalyzer2 methods ***/
#define IDWriteTextAnalyzer2_GetGlyphOrientationTransform(This,angle,is_sideways,originX,originY,transform) (This)->lpVtbl->IDWriteTextAnalyzer2_GetGlyphOrientationTransform(This,angle,is_sideways,originX,originY,transform)
#define IDWriteTextAnalyzer2_GetTypographicFeatures(This,fontface,analysis,localeName,max_tagcount,actual_tagcount,tags) (This)->lpVtbl->GetTypographicFeatures(This,fontface,analysis,localeName,max_tagcount,actual_tagcount,tags)
#define IDWriteTextAnalyzer2_CheckTypographicFeature(This,fontface,analysis,localeName,feature,glyph_count,indices,feature_applies) (This)->lpVtbl->CheckTypographicFeature(This,fontface,analysis,localeName,feature,glyph_count,indices,feature_applies)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteTextAnalyzer2_QueryInterface(IDWriteTextAnalyzer2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteTextAnalyzer2_AddRef(IDWriteTextAnalyzer2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteTextAnalyzer2_Release(IDWriteTextAnalyzer2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteTextAnalyzer methods ***/
static inline HRESULT IDWriteTextAnalyzer2_AnalyzeScript(IDWriteTextAnalyzer2* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeScript(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer2_AnalyzeBidi(IDWriteTextAnalyzer2* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeBidi(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer2_AnalyzeNumberSubstitution(IDWriteTextAnalyzer2* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeNumberSubstitution(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer2_AnalyzeLineBreakpoints(IDWriteTextAnalyzer2* This,IDWriteTextAnalysisSource *source,UINT32 position,UINT32 length,IDWriteTextAnalysisSink *sink) {
    return This->lpVtbl->AnalyzeLineBreakpoints(This,source,position,length,sink);
}
static inline HRESULT IDWriteTextAnalyzer2_GetGlyphs(IDWriteTextAnalyzer2* This,const WCHAR *text,UINT32 length,IDWriteFontFace *font_face,WINBOOL is_sideways,WINBOOL is_rtl,const DWRITE_SCRIPT_ANALYSIS *analysis,const WCHAR *locale,IDWriteNumberSubstitution *substitution,const DWRITE_TYPOGRAPHIC_FEATURES **features,const UINT32 *feature_range_len,UINT32 feature_ranges,UINT32 max_glyph_count,UINT16 *clustermap,DWRITE_SHAPING_TEXT_PROPERTIES *text_props,UINT16 *glyph_indices,DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,UINT32 *actual_glyph_count) {
    return This->lpVtbl->GetGlyphs(This,text,length,font_face,is_sideways,is_rtl,analysis,locale,substitution,features,feature_range_len,feature_ranges,max_glyph_count,clustermap,text_props,glyph_indices,glyph_props,actual_glyph_count);
}
static inline HRESULT IDWriteTextAnalyzer2_GetGlyphPlacements(IDWriteTextAnalyzer2* This,const WCHAR *text,const UINT16 *clustermap,DWRITE_SHAPING_TEXT_PROPERTIES *props,UINT32 text_len,const UINT16 *glyph_indices,const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,UINT32 glyph_count,IDWriteFontFace *font_face,FLOAT fontEmSize,WINBOOL is_sideways,WINBOOL is_rtl,const DWRITE_SCRIPT_ANALYSIS *analysis,const WCHAR *locale,const DWRITE_TYPOGRAPHIC_FEATURES **features,const UINT32 *feature_range_len,UINT32 feature_ranges,FLOAT *glyph_advances,DWRITE_GLYPH_OFFSET *glyph_offsets) {
    return This->lpVtbl->GetGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,is_sideways,is_rtl,analysis,locale,features,feature_range_len,feature_ranges,glyph_advances,glyph_offsets);
}
static inline HRESULT IDWriteTextAnalyzer2_GetGdiCompatibleGlyphPlacements(IDWriteTextAnalyzer2* This,const WCHAR *text,const UINT16 *clustermap,DWRITE_SHAPING_TEXT_PROPERTIES *props,UINT32 text_len,const UINT16 *glyph_indices,const DWRITE_SHAPING_GLYPH_PROPERTIES *glyph_props,UINT32 glyph_count,IDWriteFontFace *font_face,FLOAT fontEmSize,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,WINBOOL use_gdi_natural,WINBOOL is_sideways,WINBOOL is_rtl,const DWRITE_SCRIPT_ANALYSIS *analysis,const WCHAR *locale,const DWRITE_TYPOGRAPHIC_FEATURES **features,const UINT32 *feature_range_lengths,UINT32 feature_ranges,FLOAT *glyph_advances,DWRITE_GLYPH_OFFSET *glyph_offsets) {
    return This->lpVtbl->GetGdiCompatibleGlyphPlacements(This,text,clustermap,props,text_len,glyph_indices,glyph_props,glyph_count,font_face,fontEmSize,pixels_per_dip,transform,use_gdi_natural,is_sideways,is_rtl,analysis,locale,features,feature_range_lengths,feature_ranges,glyph_advances,glyph_offsets);
}
/*** IDWriteTextAnalyzer1 methods ***/
static inline HRESULT IDWriteTextAnalyzer2_ApplyCharacterSpacing(IDWriteTextAnalyzer2* This,FLOAT leading_spacing,FLOAT trailing_spacing,FLOAT min_advance_width,UINT32 len,UINT32 glyph_count,const UINT16 *clustermap,const FLOAT *advances,const DWRITE_GLYPH_OFFSET *offsets,const DWRITE_SHAPING_GLYPH_PROPERTIES *props,FLOAT *modified_advances,DWRITE_GLYPH_OFFSET *modified_offsets) {
    return This->lpVtbl->ApplyCharacterSpacing(This,leading_spacing,trailing_spacing,min_advance_width,len,glyph_count,clustermap,advances,offsets,props,modified_advances,modified_offsets);
}
static inline HRESULT IDWriteTextAnalyzer2_GetBaseline(IDWriteTextAnalyzer2* This,IDWriteFontFace *face,DWRITE_BASELINE baseline,WINBOOL vertical,WINBOOL is_simulation_allowed,DWRITE_SCRIPT_ANALYSIS sa,const WCHAR *localeName,INT32 *baseline_coord,WINBOOL *exists) {
    return This->lpVtbl->GetBaseline(This,face,baseline,vertical,is_simulation_allowed,sa,localeName,baseline_coord,exists);
}
static inline HRESULT IDWriteTextAnalyzer2_AnalyzeVerticalGlyphOrientation(IDWriteTextAnalyzer2* This,IDWriteTextAnalysisSource1 *source,UINT32 text_pos,UINT32 len,IDWriteTextAnalysisSink1 *sink) {
    return This->lpVtbl->AnalyzeVerticalGlyphOrientation(This,source,text_pos,len,sink);
}
static inline HRESULT IDWriteTextAnalyzer2_GetScriptProperties(IDWriteTextAnalyzer2* This,DWRITE_SCRIPT_ANALYSIS sa,DWRITE_SCRIPT_PROPERTIES *props) {
    return This->lpVtbl->GetScriptProperties(This,sa,props);
}
static inline HRESULT IDWriteTextAnalyzer2_GetTextComplexity(IDWriteTextAnalyzer2* This,const WCHAR *text,UINT32 len,IDWriteFontFace *face,WINBOOL *is_simple,UINT32 *len_read,UINT16 *indices) {
    return This->lpVtbl->GetTextComplexity(This,text,len,face,is_simple,len_read,indices);
}
static inline HRESULT IDWriteTextAnalyzer2_GetJustificationOpportunities(IDWriteTextAnalyzer2* This,IDWriteFontFace *face,FLOAT font_em_size,DWRITE_SCRIPT_ANALYSIS sa,UINT32 length,UINT32 glyph_count,const WCHAR *text,const UINT16 *clustermap,const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,DWRITE_JUSTIFICATION_OPPORTUNITY *jo) {
    return This->lpVtbl->GetJustificationOpportunities(This,face,font_em_size,sa,length,glyph_count,text,clustermap,prop,jo);
}
static inline HRESULT IDWriteTextAnalyzer2_JustifyGlyphAdvances(IDWriteTextAnalyzer2* This,FLOAT width,UINT32 glyph_count,const DWRITE_JUSTIFICATION_OPPORTUNITY *jo,const FLOAT *advances,const DWRITE_GLYPH_OFFSET *offsets,FLOAT *justifiedadvances,DWRITE_GLYPH_OFFSET *justifiedoffsets) {
    return This->lpVtbl->JustifyGlyphAdvances(This,width,glyph_count,jo,advances,offsets,justifiedadvances,justifiedoffsets);
}
static inline HRESULT IDWriteTextAnalyzer2_GetJustifiedGlyphs(IDWriteTextAnalyzer2* This,IDWriteFontFace *face,FLOAT font_em_size,DWRITE_SCRIPT_ANALYSIS sa,UINT32 length,UINT32 glyph_count,UINT32 max_glyphcount,const UINT16 *clustermap,const UINT16 *indices,const FLOAT *advances,const FLOAT *justifiedadvances,const DWRITE_GLYPH_OFFSET *justifiedoffsets,const DWRITE_SHAPING_GLYPH_PROPERTIES *prop,UINT32 *actual_count,UINT16 *modified_clustermap,UINT16 *modified_indices,FLOAT *modified_advances,DWRITE_GLYPH_OFFSET *modified_offsets) {
    return This->lpVtbl->GetJustifiedGlyphs(This,face,font_em_size,sa,length,glyph_count,max_glyphcount,clustermap,indices,advances,justifiedadvances,justifiedoffsets,prop,actual_count,modified_clustermap,modified_indices,modified_advances,modified_offsets);
}
/*** IDWriteTextAnalyzer2 methods ***/
static inline HRESULT IDWriteTextAnalyzer2_GetGlyphOrientationTransform(IDWriteTextAnalyzer2* This,DWRITE_GLYPH_ORIENTATION_ANGLE angle,WINBOOL is_sideways,FLOAT originX,FLOAT originY,DWRITE_MATRIX *transform) {
    return This->lpVtbl->IDWriteTextAnalyzer2_GetGlyphOrientationTransform(This,angle,is_sideways,originX,originY,transform);
}
static inline HRESULT IDWriteTextAnalyzer2_GetTypographicFeatures(IDWriteTextAnalyzer2* This,IDWriteFontFace *fontface,DWRITE_SCRIPT_ANALYSIS analysis,const WCHAR *localeName,UINT32 max_tagcount,UINT32 *actual_tagcount,DWRITE_FONT_FEATURE_TAG *tags) {
    return This->lpVtbl->GetTypographicFeatures(This,fontface,analysis,localeName,max_tagcount,actual_tagcount,tags);
}
static inline HRESULT IDWriteTextAnalyzer2_CheckTypographicFeature(IDWriteTextAnalyzer2* This,IDWriteFontFace *fontface,DWRITE_SCRIPT_ANALYSIS analysis,const WCHAR *localeName,DWRITE_FONT_FEATURE_TAG feature,UINT32 glyph_count,const UINT16 *indices,UINT8 *feature_applies) {
    return This->lpVtbl->CheckTypographicFeature(This,fontface,analysis,localeName,feature,glyph_count,indices,feature_applies);
}
#endif
#endif

#endif


#endif  /* __IDWriteTextAnalyzer2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontFallbackBuilder interface
 */
#ifndef __IDWriteFontFallbackBuilder_INTERFACE_DEFINED__
#define __IDWriteFontFallbackBuilder_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontFallbackBuilder, 0xfd882d06, 0x8aba, 0x4fb8, 0xb8,0x49, 0x8b,0xe8,0xb7,0x3e,0x14,0xde);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("fd882d06-8aba-4fb8-b849-8be8b73e14de")
IDWriteFontFallbackBuilder : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE AddMapping(
        const DWRITE_UNICODE_RANGE *ranges,
        UINT32 rangesCount,
        const WCHAR **targetFamilyNames,
        UINT32 targetFamilyNamesCount,
        IDWriteFontCollection *collection = 0,
        const WCHAR *localeName = 0,
        const WCHAR *baseFamilyName = 0,
        FLOAT scale = 1) = 0;

    virtual HRESULT STDMETHODCALLTYPE AddMappings(
        IDWriteFontFallback *fallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFontFallback(
        IDWriteFontFallback **fallback) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontFallbackBuilder, 0xfd882d06, 0x8aba, 0x4fb8, 0xb8,0x49, 0x8b,0xe8,0xb7,0x3e,0x14,0xde)
#endif
#else
typedef struct IDWriteFontFallbackBuilderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontFallbackBuilder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontFallbackBuilder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontFallbackBuilder *This);

    /*** IDWriteFontFallbackBuilder methods ***/
    HRESULT (STDMETHODCALLTYPE *AddMapping)(
        IDWriteFontFallbackBuilder *This,
        const DWRITE_UNICODE_RANGE *ranges,
        UINT32 rangesCount,
        const WCHAR **targetFamilyNames,
        UINT32 targetFamilyNamesCount,
        IDWriteFontCollection *collection,
        const WCHAR *localeName,
        const WCHAR *baseFamilyName,
        FLOAT scale);

    HRESULT (STDMETHODCALLTYPE *AddMappings)(
        IDWriteFontFallbackBuilder *This,
        IDWriteFontFallback *fallback);

    HRESULT (STDMETHODCALLTYPE *CreateFontFallback)(
        IDWriteFontFallbackBuilder *This,
        IDWriteFontFallback **fallback);

    END_INTERFACE
} IDWriteFontFallbackBuilderVtbl;

interface IDWriteFontFallbackBuilder {
    CONST_VTBL IDWriteFontFallbackBuilderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontFallbackBuilder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontFallbackBuilder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontFallbackBuilder_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontFallbackBuilder methods ***/
#define IDWriteFontFallbackBuilder_AddMapping(This,ranges,rangesCount,targetFamilyNames,targetFamilyNamesCount,collection,localeName,baseFamilyName,scale) (This)->lpVtbl->AddMapping(This,ranges,rangesCount,targetFamilyNames,targetFamilyNamesCount,collection,localeName,baseFamilyName,scale)
#define IDWriteFontFallbackBuilder_AddMappings(This,fallback) (This)->lpVtbl->AddMappings(This,fallback)
#define IDWriteFontFallbackBuilder_CreateFontFallback(This,fallback) (This)->lpVtbl->CreateFontFallback(This,fallback)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontFallbackBuilder_QueryInterface(IDWriteFontFallbackBuilder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontFallbackBuilder_AddRef(IDWriteFontFallbackBuilder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontFallbackBuilder_Release(IDWriteFontFallbackBuilder* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontFallbackBuilder methods ***/
static inline HRESULT IDWriteFontFallbackBuilder_AddMapping(IDWriteFontFallbackBuilder* This,const DWRITE_UNICODE_RANGE *ranges,UINT32 rangesCount,const WCHAR **targetFamilyNames,UINT32 targetFamilyNamesCount,IDWriteFontCollection *collection,const WCHAR *localeName,const WCHAR *baseFamilyName,FLOAT scale) {
    return This->lpVtbl->AddMapping(This,ranges,rangesCount,targetFamilyNames,targetFamilyNamesCount,collection,localeName,baseFamilyName,scale);
}
static inline HRESULT IDWriteFontFallbackBuilder_AddMappings(IDWriteFontFallbackBuilder* This,IDWriteFontFallback *fallback) {
    return This->lpVtbl->AddMappings(This,fallback);
}
static inline HRESULT IDWriteFontFallbackBuilder_CreateFontFallback(IDWriteFontFallbackBuilder* This,IDWriteFontFallback **fallback) {
    return This->lpVtbl->CreateFontFallback(This,fallback);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontFallbackBuilder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFont2 interface
 */
#ifndef __IDWriteFont2_INTERFACE_DEFINED__
#define __IDWriteFont2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFont2, 0x29748ed6, 0x8c9c, 0x4a6a, 0xbe,0x0b, 0xd9,0x12,0xe8,0x53,0x89,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("29748ed6-8c9c-4a6a-be0b-d912e8538944")
IDWriteFont2 : public IDWriteFont1
{
    virtual WINBOOL STDMETHODCALLTYPE IsColorFont(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFont2, 0x29748ed6, 0x8c9c, 0x4a6a, 0xbe,0x0b, 0xd9,0x12,0xe8,0x53,0x89,0x44)
#endif
#else
typedef struct IDWriteFont2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFont2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFont2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFont2 *This);

    /*** IDWriteFont methods ***/
    HRESULT (STDMETHODCALLTYPE *GetFontFamily)(
        IDWriteFont2 *This,
        IDWriteFontFamily **family);

    DWRITE_FONT_WEIGHT (STDMETHODCALLTYPE *GetWeight)(
        IDWriteFont2 *This);

    DWRITE_FONT_STRETCH (STDMETHODCALLTYPE *GetStretch)(
        IDWriteFont2 *This);

    DWRITE_FONT_STYLE (STDMETHODCALLTYPE *GetStyle)(
        IDWriteFont2 *This);

    WINBOOL (STDMETHODCALLTYPE *IsSymbolFont)(
        IDWriteFont2 *This);

    HRESULT (STDMETHODCALLTYPE *GetFaceNames)(
        IDWriteFont2 *This,
        IDWriteLocalizedStrings **names);

    HRESULT (STDMETHODCALLTYPE *GetInformationalStrings)(
        IDWriteFont2 *This,
        DWRITE_INFORMATIONAL_STRING_ID stringid,
        IDWriteLocalizedStrings **strings,
        WINBOOL *exists);

    DWRITE_FONT_SIMULATIONS (STDMETHODCALLTYPE *GetSimulations)(
        IDWriteFont2 *This);

    void (STDMETHODCALLTYPE *GetMetrics)(
        IDWriteFont2 *This,
        DWRITE_FONT_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *HasCharacter)(
        IDWriteFont2 *This,
        UINT32 value,
        WINBOOL *exists);

    HRESULT (STDMETHODCALLTYPE *CreateFontFace)(
        IDWriteFont2 *This,
        IDWriteFontFace **face);

    /*** IDWriteFont1 methods ***/
    void (STDMETHODCALLTYPE *IDWriteFont1_GetMetrics)(
        IDWriteFont2 *This,
        DWRITE_FONT_METRICS1 *metrics);

    void (STDMETHODCALLTYPE *GetPanose)(
        IDWriteFont2 *This,
        DWRITE_PANOSE *panose);

    HRESULT (STDMETHODCALLTYPE *GetUnicodeRanges)(
        IDWriteFont2 *This,
        UINT32 max_count,
        DWRITE_UNICODE_RANGE *ranges,
        UINT32 *count);

    WINBOOL (STDMETHODCALLTYPE *IsMonospacedFont)(
        IDWriteFont2 *This);

    /*** IDWriteFont2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsColorFont)(
        IDWriteFont2 *This);

    END_INTERFACE
} IDWriteFont2Vtbl;

interface IDWriteFont2 {
    CONST_VTBL IDWriteFont2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFont2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFont2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFont2_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFont methods ***/
#define IDWriteFont2_GetFontFamily(This,family) (This)->lpVtbl->GetFontFamily(This,family)
#define IDWriteFont2_GetWeight(This) (This)->lpVtbl->GetWeight(This)
#define IDWriteFont2_GetStretch(This) (This)->lpVtbl->GetStretch(This)
#define IDWriteFont2_GetStyle(This) (This)->lpVtbl->GetStyle(This)
#define IDWriteFont2_IsSymbolFont(This) (This)->lpVtbl->IsSymbolFont(This)
#define IDWriteFont2_GetFaceNames(This,names) (This)->lpVtbl->GetFaceNames(This,names)
#define IDWriteFont2_GetInformationalStrings(This,stringid,strings,exists) (This)->lpVtbl->GetInformationalStrings(This,stringid,strings,exists)
#define IDWriteFont2_GetSimulations(This) (This)->lpVtbl->GetSimulations(This)
#define IDWriteFont2_HasCharacter(This,value,exists) (This)->lpVtbl->HasCharacter(This,value,exists)
#define IDWriteFont2_CreateFontFace(This,face) (This)->lpVtbl->CreateFontFace(This,face)
/*** IDWriteFont1 methods ***/
#define IDWriteFont2_GetMetrics(This,metrics) (This)->lpVtbl->IDWriteFont1_GetMetrics(This,metrics)
#define IDWriteFont2_GetPanose(This,panose) (This)->lpVtbl->GetPanose(This,panose)
#define IDWriteFont2_GetUnicodeRanges(This,max_count,ranges,count) (This)->lpVtbl->GetUnicodeRanges(This,max_count,ranges,count)
#define IDWriteFont2_IsMonospacedFont(This) (This)->lpVtbl->IsMonospacedFont(This)
/*** IDWriteFont2 methods ***/
#define IDWriteFont2_IsColorFont(This) (This)->lpVtbl->IsColorFont(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFont2_QueryInterface(IDWriteFont2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFont2_AddRef(IDWriteFont2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFont2_Release(IDWriteFont2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFont methods ***/
static inline HRESULT IDWriteFont2_GetFontFamily(IDWriteFont2* This,IDWriteFontFamily **family) {
    return This->lpVtbl->GetFontFamily(This,family);
}
static inline DWRITE_FONT_WEIGHT IDWriteFont2_GetWeight(IDWriteFont2* This) {
    return This->lpVtbl->GetWeight(This);
}
static inline DWRITE_FONT_STRETCH IDWriteFont2_GetStretch(IDWriteFont2* This) {
    return This->lpVtbl->GetStretch(This);
}
static inline DWRITE_FONT_STYLE IDWriteFont2_GetStyle(IDWriteFont2* This) {
    return This->lpVtbl->GetStyle(This);
}
static inline WINBOOL IDWriteFont2_IsSymbolFont(IDWriteFont2* This) {
    return This->lpVtbl->IsSymbolFont(This);
}
static inline HRESULT IDWriteFont2_GetFaceNames(IDWriteFont2* This,IDWriteLocalizedStrings **names) {
    return This->lpVtbl->GetFaceNames(This,names);
}
static inline HRESULT IDWriteFont2_GetInformationalStrings(IDWriteFont2* This,DWRITE_INFORMATIONAL_STRING_ID stringid,IDWriteLocalizedStrings **strings,WINBOOL *exists) {
    return This->lpVtbl->GetInformationalStrings(This,stringid,strings,exists);
}
static inline DWRITE_FONT_SIMULATIONS IDWriteFont2_GetSimulations(IDWriteFont2* This) {
    return This->lpVtbl->GetSimulations(This);
}
static inline HRESULT IDWriteFont2_HasCharacter(IDWriteFont2* This,UINT32 value,WINBOOL *exists) {
    return This->lpVtbl->HasCharacter(This,value,exists);
}
static inline HRESULT IDWriteFont2_CreateFontFace(IDWriteFont2* This,IDWriteFontFace **face) {
    return This->lpVtbl->CreateFontFace(This,face);
}
/*** IDWriteFont1 methods ***/
static inline void IDWriteFont2_GetMetrics(IDWriteFont2* This,DWRITE_FONT_METRICS1 *metrics) {
    This->lpVtbl->IDWriteFont1_GetMetrics(This,metrics);
}
static inline void IDWriteFont2_GetPanose(IDWriteFont2* This,DWRITE_PANOSE *panose) {
    This->lpVtbl->GetPanose(This,panose);
}
static inline HRESULT IDWriteFont2_GetUnicodeRanges(IDWriteFont2* This,UINT32 max_count,DWRITE_UNICODE_RANGE *ranges,UINT32 *count) {
    return This->lpVtbl->GetUnicodeRanges(This,max_count,ranges,count);
}
static inline WINBOOL IDWriteFont2_IsMonospacedFont(IDWriteFont2* This) {
    return This->lpVtbl->IsMonospacedFont(This);
}
/*** IDWriteFont2 methods ***/
static inline WINBOOL IDWriteFont2_IsColorFont(IDWriteFont2* This) {
    return This->lpVtbl->IsColorFont(This);
}
#endif
#endif

#endif


#endif  /* __IDWriteFont2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFontFace2 interface
 */
#ifndef __IDWriteFontFace2_INTERFACE_DEFINED__
#define __IDWriteFontFace2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFontFace2, 0xd8b768ff, 0x64bc, 0x4e66, 0x98,0x2b, 0xec,0x8e,0x87,0xf6,0x93,0xf7);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d8b768ff-64bc-4e66-982b-ec8e87f693f7")
IDWriteFontFace2 : public IDWriteFontFace1
{
    virtual WINBOOL STDMETHODCALLTYPE IsColorFont(
        ) = 0;

    virtual UINT32 STDMETHODCALLTYPE GetColorPaletteCount(
        ) = 0;

    virtual UINT32 STDMETHODCALLTYPE GetPaletteEntryCount(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetPaletteEntries(
        UINT32 palette_index,
        UINT32 first_entry_index,
        UINT32 entry_count,
        DWRITE_COLOR_F *entries) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetRecommendedRenderingMode(
        FLOAT fontEmSize,
        FLOAT dpiX,
        FLOAT dpiY,
        const DWRITE_MATRIX *transform,
        WINBOOL is_sideways,
        DWRITE_OUTLINE_THRESHOLD threshold,
        DWRITE_MEASURING_MODE measuringmode,
        IDWriteRenderingParams *params,
        DWRITE_RENDERING_MODE *renderingmode,
        DWRITE_GRID_FIT_MODE *gridfitmode) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFontFace2, 0xd8b768ff, 0x64bc, 0x4e66, 0x98,0x2b, 0xec,0x8e,0x87,0xf6,0x93,0xf7)
#endif
#else
typedef struct IDWriteFontFace2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFontFace2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFontFace2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFontFace2 *This);

    /*** IDWriteFontFace methods ***/
    DWRITE_FONT_FACE_TYPE (STDMETHODCALLTYPE *GetType)(
        IDWriteFontFace2 *This);

    HRESULT (STDMETHODCALLTYPE *GetFiles)(
        IDWriteFontFace2 *This,
        UINT32 *number_of_files,
        IDWriteFontFile **fontfiles);

    UINT32 (STDMETHODCALLTYPE *GetIndex)(
        IDWriteFontFace2 *This);

    DWRITE_FONT_SIMULATIONS (STDMETHODCALLTYPE *GetSimulations)(
        IDWriteFontFace2 *This);

    WINBOOL (STDMETHODCALLTYPE *IsSymbolFont)(
        IDWriteFontFace2 *This);

    void (STDMETHODCALLTYPE *GetMetrics)(
        IDWriteFontFace2 *This,
        DWRITE_FONT_METRICS *metrics);

    UINT16 (STDMETHODCALLTYPE *GetGlyphCount)(
        IDWriteFontFace2 *This);

    HRESULT (STDMETHODCALLTYPE *GetDesignGlyphMetrics)(
        IDWriteFontFace2 *This,
        const UINT16 *glyph_indices,
        UINT32 glyph_count,
        DWRITE_GLYPH_METRICS *metrics,
        WINBOOL is_sideways);

    HRESULT (STDMETHODCALLTYPE *GetGlyphIndices)(
        IDWriteFontFace2 *This,
        const UINT32 *codepoints,
        UINT32 count,
        UINT16 *glyph_indices);

    HRESULT (STDMETHODCALLTYPE *TryGetFontTable)(
        IDWriteFontFace2 *This,
        UINT32 table_tag,
        const void **table_data,
        UINT32 *table_size,
        void **context,
        WINBOOL *exists);

    void (STDMETHODCALLTYPE *ReleaseFontTable)(
        IDWriteFontFace2 *This,
        void *table_context);

    HRESULT (STDMETHODCALLTYPE *GetGlyphRunOutline)(
        IDWriteFontFace2 *This,
        FLOAT emSize,
        const UINT16 *glyph_indices,
        const FLOAT *glyph_advances,
        const DWRITE_GLYPH_OFFSET *glyph_offsets,
        UINT32 glyph_count,
        WINBOOL is_sideways,
        WINBOOL is_rtl,
        IDWriteGeometrySink *geometrysink);

    HRESULT (STDMETHODCALLTYPE *GetRecommendedRenderingMode)(
        IDWriteFontFace2 *This,
        FLOAT emSize,
        FLOAT pixels_per_dip,
        DWRITE_MEASURING_MODE mode,
        IDWriteRenderingParams *params,
        DWRITE_RENDERING_MODE *rendering_mode);

    HRESULT (STDMETHODCALLTYPE *GetGdiCompatibleMetrics)(
        IDWriteFontFace2 *This,
        FLOAT emSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        DWRITE_FONT_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *GetGdiCompatibleGlyphMetrics)(
        IDWriteFontFace2 *This,
        FLOAT emSize,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        const UINT16 *glyph_indices,
        UINT32 glyph_count,
        DWRITE_GLYPH_METRICS *metrics,
        WINBOOL is_sideways);

    /*** IDWriteFontFace1 methods ***/
    void (STDMETHODCALLTYPE *IDWriteFontFace1_GetMetrics)(
        IDWriteFontFace2 *This,
        DWRITE_FONT_METRICS1 *metrics);

    HRESULT (STDMETHODCALLTYPE *IDWriteFontFace1_GetGdiCompatibleMetrics)(
        IDWriteFontFace2 *This,
        FLOAT em_size,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        DWRITE_FONT_METRICS1 *metrics);

    void (STDMETHODCALLTYPE *GetCaretMetrics)(
        IDWriteFontFace2 *This,
        DWRITE_CARET_METRICS *metrics);

    HRESULT (STDMETHODCALLTYPE *GetUnicodeRanges)(
        IDWriteFontFace2 *This,
        UINT32 max_count,
        DWRITE_UNICODE_RANGE *ranges,
        UINT32 *count);

    WINBOOL (STDMETHODCALLTYPE *IsMonospacedFont)(
        IDWriteFontFace2 *This);

    HRESULT (STDMETHODCALLTYPE *GetDesignGlyphAdvances)(
        IDWriteFontFace2 *This,
        UINT32 glyph_count,
        const UINT16 *indices,
        INT32 *advances,
        WINBOOL is_sideways);

    HRESULT (STDMETHODCALLTYPE *GetGdiCompatibleGlyphAdvances)(
        IDWriteFontFace2 *This,
        FLOAT em_size,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        WINBOOL is_sideways,
        UINT32 glyph_count,
        const UINT16 *indices,
        INT32 *advances);

    HRESULT (STDMETHODCALLTYPE *GetKerningPairAdjustments)(
        IDWriteFontFace2 *This,
        UINT32 glyph_count,
        const UINT16 *indices,
        INT32 *adjustments);

    WINBOOL (STDMETHODCALLTYPE *HasKerningPairs)(
        IDWriteFontFace2 *This);

    HRESULT (STDMETHODCALLTYPE *IDWriteFontFace1_GetRecommendedRenderingMode)(
        IDWriteFontFace2 *This,
        FLOAT font_emsize,
        FLOAT dpiX,
        FLOAT dpiY,
        const DWRITE_MATRIX *transform,
        WINBOOL is_sideways,
        DWRITE_OUTLINE_THRESHOLD threshold,
        DWRITE_MEASURING_MODE measuring_mode,
        DWRITE_RENDERING_MODE *rendering_mode);

    HRESULT (STDMETHODCALLTYPE *GetVerticalGlyphVariants)(
        IDWriteFontFace2 *This,
        UINT32 glyph_count,
        const UINT16 *nominal_indices,
        UINT16 *vertical_indices);

    WINBOOL (STDMETHODCALLTYPE *HasVerticalGlyphVariants)(
        IDWriteFontFace2 *This);

    /*** IDWriteFontFace2 methods ***/
    WINBOOL (STDMETHODCALLTYPE *IsColorFont)(
        IDWriteFontFace2 *This);

    UINT32 (STDMETHODCALLTYPE *GetColorPaletteCount)(
        IDWriteFontFace2 *This);

    UINT32 (STDMETHODCALLTYPE *GetPaletteEntryCount)(
        IDWriteFontFace2 *This);

    HRESULT (STDMETHODCALLTYPE *GetPaletteEntries)(
        IDWriteFontFace2 *This,
        UINT32 palette_index,
        UINT32 first_entry_index,
        UINT32 entry_count,
        DWRITE_COLOR_F *entries);

    HRESULT (STDMETHODCALLTYPE *IDWriteFontFace2_GetRecommendedRenderingMode)(
        IDWriteFontFace2 *This,
        FLOAT fontEmSize,
        FLOAT dpiX,
        FLOAT dpiY,
        const DWRITE_MATRIX *transform,
        WINBOOL is_sideways,
        DWRITE_OUTLINE_THRESHOLD threshold,
        DWRITE_MEASURING_MODE measuringmode,
        IDWriteRenderingParams *params,
        DWRITE_RENDERING_MODE *renderingmode,
        DWRITE_GRID_FIT_MODE *gridfitmode);

    END_INTERFACE
} IDWriteFontFace2Vtbl;

interface IDWriteFontFace2 {
    CONST_VTBL IDWriteFontFace2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFontFace2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFontFace2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFontFace2_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFontFace methods ***/
#define IDWriteFontFace2_GetType(This) (This)->lpVtbl->GetType(This)
#define IDWriteFontFace2_GetFiles(This,number_of_files,fontfiles) (This)->lpVtbl->GetFiles(This,number_of_files,fontfiles)
#define IDWriteFontFace2_GetIndex(This) (This)->lpVtbl->GetIndex(This)
#define IDWriteFontFace2_GetSimulations(This) (This)->lpVtbl->GetSimulations(This)
#define IDWriteFontFace2_IsSymbolFont(This) (This)->lpVtbl->IsSymbolFont(This)
#define IDWriteFontFace2_GetGlyphCount(This) (This)->lpVtbl->GetGlyphCount(This)
#define IDWriteFontFace2_GetDesignGlyphMetrics(This,glyph_indices,glyph_count,metrics,is_sideways) (This)->lpVtbl->GetDesignGlyphMetrics(This,glyph_indices,glyph_count,metrics,is_sideways)
#define IDWriteFontFace2_GetGlyphIndices(This,codepoints,count,glyph_indices) (This)->lpVtbl->GetGlyphIndices(This,codepoints,count,glyph_indices)
#define IDWriteFontFace2_TryGetFontTable(This,table_tag,table_data,table_size,context,exists) (This)->lpVtbl->TryGetFontTable(This,table_tag,table_data,table_size,context,exists)
#define IDWriteFontFace2_ReleaseFontTable(This,table_context) (This)->lpVtbl->ReleaseFontTable(This,table_context)
#define IDWriteFontFace2_GetGlyphRunOutline(This,emSize,glyph_indices,glyph_advances,glyph_offsets,glyph_count,is_sideways,is_rtl,geometrysink) (This)->lpVtbl->GetGlyphRunOutline(This,emSize,glyph_indices,glyph_advances,glyph_offsets,glyph_count,is_sideways,is_rtl,geometrysink)
#define IDWriteFontFace2_GetGdiCompatibleGlyphMetrics(This,emSize,pixels_per_dip,transform,use_gdi_natural,glyph_indices,glyph_count,metrics,is_sideways) (This)->lpVtbl->GetGdiCompatibleGlyphMetrics(This,emSize,pixels_per_dip,transform,use_gdi_natural,glyph_indices,glyph_count,metrics,is_sideways)
/*** IDWriteFontFace1 methods ***/
#define IDWriteFontFace2_GetMetrics(This,metrics) (This)->lpVtbl->IDWriteFontFace1_GetMetrics(This,metrics)
#define IDWriteFontFace2_GetGdiCompatibleMetrics(This,em_size,pixels_per_dip,transform,metrics) (This)->lpVtbl->IDWriteFontFace1_GetGdiCompatibleMetrics(This,em_size,pixels_per_dip,transform,metrics)
#define IDWriteFontFace2_GetCaretMetrics(This,metrics) (This)->lpVtbl->GetCaretMetrics(This,metrics)
#define IDWriteFontFace2_GetUnicodeRanges(This,max_count,ranges,count) (This)->lpVtbl->GetUnicodeRanges(This,max_count,ranges,count)
#define IDWriteFontFace2_IsMonospacedFont(This) (This)->lpVtbl->IsMonospacedFont(This)
#define IDWriteFontFace2_GetDesignGlyphAdvances(This,glyph_count,indices,advances,is_sideways) (This)->lpVtbl->GetDesignGlyphAdvances(This,glyph_count,indices,advances,is_sideways)
#define IDWriteFontFace2_GetGdiCompatibleGlyphAdvances(This,em_size,pixels_per_dip,transform,use_gdi_natural,is_sideways,glyph_count,indices,advances) (This)->lpVtbl->GetGdiCompatibleGlyphAdvances(This,em_size,pixels_per_dip,transform,use_gdi_natural,is_sideways,glyph_count,indices,advances)
#define IDWriteFontFace2_GetKerningPairAdjustments(This,glyph_count,indices,adjustments) (This)->lpVtbl->GetKerningPairAdjustments(This,glyph_count,indices,adjustments)
#define IDWriteFontFace2_HasKerningPairs(This) (This)->lpVtbl->HasKerningPairs(This)
#define IDWriteFontFace2_GetVerticalGlyphVariants(This,glyph_count,nominal_indices,vertical_indices) (This)->lpVtbl->GetVerticalGlyphVariants(This,glyph_count,nominal_indices,vertical_indices)
#define IDWriteFontFace2_HasVerticalGlyphVariants(This) (This)->lpVtbl->HasVerticalGlyphVariants(This)
/*** IDWriteFontFace2 methods ***/
#define IDWriteFontFace2_IsColorFont(This) (This)->lpVtbl->IsColorFont(This)
#define IDWriteFontFace2_GetColorPaletteCount(This) (This)->lpVtbl->GetColorPaletteCount(This)
#define IDWriteFontFace2_GetPaletteEntryCount(This) (This)->lpVtbl->GetPaletteEntryCount(This)
#define IDWriteFontFace2_GetPaletteEntries(This,palette_index,first_entry_index,entry_count,entries) (This)->lpVtbl->GetPaletteEntries(This,palette_index,first_entry_index,entry_count,entries)
#define IDWriteFontFace2_GetRecommendedRenderingMode(This,fontEmSize,dpiX,dpiY,transform,is_sideways,threshold,measuringmode,params,renderingmode,gridfitmode) (This)->lpVtbl->IDWriteFontFace2_GetRecommendedRenderingMode(This,fontEmSize,dpiX,dpiY,transform,is_sideways,threshold,measuringmode,params,renderingmode,gridfitmode)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFontFace2_QueryInterface(IDWriteFontFace2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFontFace2_AddRef(IDWriteFontFace2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFontFace2_Release(IDWriteFontFace2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFontFace methods ***/
static inline DWRITE_FONT_FACE_TYPE IDWriteFontFace2_GetType(IDWriteFontFace2* This) {
    return This->lpVtbl->GetType(This);
}
static inline HRESULT IDWriteFontFace2_GetFiles(IDWriteFontFace2* This,UINT32 *number_of_files,IDWriteFontFile **fontfiles) {
    return This->lpVtbl->GetFiles(This,number_of_files,fontfiles);
}
static inline UINT32 IDWriteFontFace2_GetIndex(IDWriteFontFace2* This) {
    return This->lpVtbl->GetIndex(This);
}
static inline DWRITE_FONT_SIMULATIONS IDWriteFontFace2_GetSimulations(IDWriteFontFace2* This) {
    return This->lpVtbl->GetSimulations(This);
}
static inline WINBOOL IDWriteFontFace2_IsSymbolFont(IDWriteFontFace2* This) {
    return This->lpVtbl->IsSymbolFont(This);
}
static inline UINT16 IDWriteFontFace2_GetGlyphCount(IDWriteFontFace2* This) {
    return This->lpVtbl->GetGlyphCount(This);
}
static inline HRESULT IDWriteFontFace2_GetDesignGlyphMetrics(IDWriteFontFace2* This,const UINT16 *glyph_indices,UINT32 glyph_count,DWRITE_GLYPH_METRICS *metrics,WINBOOL is_sideways) {
    return This->lpVtbl->GetDesignGlyphMetrics(This,glyph_indices,glyph_count,metrics,is_sideways);
}
static inline HRESULT IDWriteFontFace2_GetGlyphIndices(IDWriteFontFace2* This,const UINT32 *codepoints,UINT32 count,UINT16 *glyph_indices) {
    return This->lpVtbl->GetGlyphIndices(This,codepoints,count,glyph_indices);
}
static inline HRESULT IDWriteFontFace2_TryGetFontTable(IDWriteFontFace2* This,UINT32 table_tag,const void **table_data,UINT32 *table_size,void **context,WINBOOL *exists) {
    return This->lpVtbl->TryGetFontTable(This,table_tag,table_data,table_size,context,exists);
}
static inline void IDWriteFontFace2_ReleaseFontTable(IDWriteFontFace2* This,void *table_context) {
    This->lpVtbl->ReleaseFontTable(This,table_context);
}
static inline HRESULT IDWriteFontFace2_GetGlyphRunOutline(IDWriteFontFace2* This,FLOAT emSize,const UINT16 *glyph_indices,const FLOAT *glyph_advances,const DWRITE_GLYPH_OFFSET *glyph_offsets,UINT32 glyph_count,WINBOOL is_sideways,WINBOOL is_rtl,IDWriteGeometrySink *geometrysink) {
    return This->lpVtbl->GetGlyphRunOutline(This,emSize,glyph_indices,glyph_advances,glyph_offsets,glyph_count,is_sideways,is_rtl,geometrysink);
}
static inline HRESULT IDWriteFontFace2_GetGdiCompatibleGlyphMetrics(IDWriteFontFace2* This,FLOAT emSize,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,WINBOOL use_gdi_natural,const UINT16 *glyph_indices,UINT32 glyph_count,DWRITE_GLYPH_METRICS *metrics,WINBOOL is_sideways) {
    return This->lpVtbl->GetGdiCompatibleGlyphMetrics(This,emSize,pixels_per_dip,transform,use_gdi_natural,glyph_indices,glyph_count,metrics,is_sideways);
}
/*** IDWriteFontFace1 methods ***/
static inline void IDWriteFontFace2_GetMetrics(IDWriteFontFace2* This,DWRITE_FONT_METRICS1 *metrics) {
    This->lpVtbl->IDWriteFontFace1_GetMetrics(This,metrics);
}
static inline HRESULT IDWriteFontFace2_GetGdiCompatibleMetrics(IDWriteFontFace2* This,FLOAT em_size,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,DWRITE_FONT_METRICS1 *metrics) {
    return This->lpVtbl->IDWriteFontFace1_GetGdiCompatibleMetrics(This,em_size,pixels_per_dip,transform,metrics);
}
static inline void IDWriteFontFace2_GetCaretMetrics(IDWriteFontFace2* This,DWRITE_CARET_METRICS *metrics) {
    This->lpVtbl->GetCaretMetrics(This,metrics);
}
static inline HRESULT IDWriteFontFace2_GetUnicodeRanges(IDWriteFontFace2* This,UINT32 max_count,DWRITE_UNICODE_RANGE *ranges,UINT32 *count) {
    return This->lpVtbl->GetUnicodeRanges(This,max_count,ranges,count);
}
static inline WINBOOL IDWriteFontFace2_IsMonospacedFont(IDWriteFontFace2* This) {
    return This->lpVtbl->IsMonospacedFont(This);
}
static inline HRESULT IDWriteFontFace2_GetDesignGlyphAdvances(IDWriteFontFace2* This,UINT32 glyph_count,const UINT16 *indices,INT32 *advances,WINBOOL is_sideways) {
    return This->lpVtbl->GetDesignGlyphAdvances(This,glyph_count,indices,advances,is_sideways);
}
static inline HRESULT IDWriteFontFace2_GetGdiCompatibleGlyphAdvances(IDWriteFontFace2* This,FLOAT em_size,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,WINBOOL use_gdi_natural,WINBOOL is_sideways,UINT32 glyph_count,const UINT16 *indices,INT32 *advances) {
    return This->lpVtbl->GetGdiCompatibleGlyphAdvances(This,em_size,pixels_per_dip,transform,use_gdi_natural,is_sideways,glyph_count,indices,advances);
}
static inline HRESULT IDWriteFontFace2_GetKerningPairAdjustments(IDWriteFontFace2* This,UINT32 glyph_count,const UINT16 *indices,INT32 *adjustments) {
    return This->lpVtbl->GetKerningPairAdjustments(This,glyph_count,indices,adjustments);
}
static inline WINBOOL IDWriteFontFace2_HasKerningPairs(IDWriteFontFace2* This) {
    return This->lpVtbl->HasKerningPairs(This);
}
static inline HRESULT IDWriteFontFace2_GetVerticalGlyphVariants(IDWriteFontFace2* This,UINT32 glyph_count,const UINT16 *nominal_indices,UINT16 *vertical_indices) {
    return This->lpVtbl->GetVerticalGlyphVariants(This,glyph_count,nominal_indices,vertical_indices);
}
static inline WINBOOL IDWriteFontFace2_HasVerticalGlyphVariants(IDWriteFontFace2* This) {
    return This->lpVtbl->HasVerticalGlyphVariants(This);
}
/*** IDWriteFontFace2 methods ***/
static inline WINBOOL IDWriteFontFace2_IsColorFont(IDWriteFontFace2* This) {
    return This->lpVtbl->IsColorFont(This);
}
static inline UINT32 IDWriteFontFace2_GetColorPaletteCount(IDWriteFontFace2* This) {
    return This->lpVtbl->GetColorPaletteCount(This);
}
static inline UINT32 IDWriteFontFace2_GetPaletteEntryCount(IDWriteFontFace2* This) {
    return This->lpVtbl->GetPaletteEntryCount(This);
}
static inline HRESULT IDWriteFontFace2_GetPaletteEntries(IDWriteFontFace2* This,UINT32 palette_index,UINT32 first_entry_index,UINT32 entry_count,DWRITE_COLOR_F *entries) {
    return This->lpVtbl->GetPaletteEntries(This,palette_index,first_entry_index,entry_count,entries);
}
static inline HRESULT IDWriteFontFace2_GetRecommendedRenderingMode(IDWriteFontFace2* This,FLOAT fontEmSize,FLOAT dpiX,FLOAT dpiY,const DWRITE_MATRIX *transform,WINBOOL is_sideways,DWRITE_OUTLINE_THRESHOLD threshold,DWRITE_MEASURING_MODE measuringmode,IDWriteRenderingParams *params,DWRITE_RENDERING_MODE *renderingmode,DWRITE_GRID_FIT_MODE *gridfitmode) {
    return This->lpVtbl->IDWriteFontFace2_GetRecommendedRenderingMode(This,fontEmSize,dpiX,dpiY,transform,is_sideways,threshold,measuringmode,params,renderingmode,gridfitmode);
}
#endif
#endif

#endif


#endif  /* __IDWriteFontFace2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteColorGlyphRunEnumerator interface
 */
#ifndef __IDWriteColorGlyphRunEnumerator_INTERFACE_DEFINED__
#define __IDWriteColorGlyphRunEnumerator_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteColorGlyphRunEnumerator, 0xd31fbe17, 0xf157, 0x41a2, 0x8d,0x24, 0xcb,0x77,0x9e,0x05,0x60,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d31fbe17-f157-41a2-8d24-cb779e0560e8")
IDWriteColorGlyphRunEnumerator : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE MoveNext(
        WINBOOL *hasRun) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCurrentRun(
        const DWRITE_COLOR_GLYPH_RUN **run) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteColorGlyphRunEnumerator, 0xd31fbe17, 0xf157, 0x41a2, 0x8d,0x24, 0xcb,0x77,0x9e,0x05,0x60,0xe8)
#endif
#else
typedef struct IDWriteColorGlyphRunEnumeratorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteColorGlyphRunEnumerator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteColorGlyphRunEnumerator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteColorGlyphRunEnumerator *This);

    /*** IDWriteColorGlyphRunEnumerator methods ***/
    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        IDWriteColorGlyphRunEnumerator *This,
        WINBOOL *hasRun);

    HRESULT (STDMETHODCALLTYPE *GetCurrentRun)(
        IDWriteColorGlyphRunEnumerator *This,
        const DWRITE_COLOR_GLYPH_RUN **run);

    END_INTERFACE
} IDWriteColorGlyphRunEnumeratorVtbl;

interface IDWriteColorGlyphRunEnumerator {
    CONST_VTBL IDWriteColorGlyphRunEnumeratorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteColorGlyphRunEnumerator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteColorGlyphRunEnumerator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteColorGlyphRunEnumerator_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteColorGlyphRunEnumerator methods ***/
#define IDWriteColorGlyphRunEnumerator_MoveNext(This,hasRun) (This)->lpVtbl->MoveNext(This,hasRun)
#define IDWriteColorGlyphRunEnumerator_GetCurrentRun(This,run) (This)->lpVtbl->GetCurrentRun(This,run)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteColorGlyphRunEnumerator_QueryInterface(IDWriteColorGlyphRunEnumerator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteColorGlyphRunEnumerator_AddRef(IDWriteColorGlyphRunEnumerator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteColorGlyphRunEnumerator_Release(IDWriteColorGlyphRunEnumerator* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteColorGlyphRunEnumerator methods ***/
static inline HRESULT IDWriteColorGlyphRunEnumerator_MoveNext(IDWriteColorGlyphRunEnumerator* This,WINBOOL *hasRun) {
    return This->lpVtbl->MoveNext(This,hasRun);
}
static inline HRESULT IDWriteColorGlyphRunEnumerator_GetCurrentRun(IDWriteColorGlyphRunEnumerator* This,const DWRITE_COLOR_GLYPH_RUN **run) {
    return This->lpVtbl->GetCurrentRun(This,run);
}
#endif
#endif

#endif


#endif  /* __IDWriteColorGlyphRunEnumerator_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteRenderingParams2 interface
 */
#ifndef __IDWriteRenderingParams2_INTERFACE_DEFINED__
#define __IDWriteRenderingParams2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteRenderingParams2, 0xf9d711c3, 0x9777, 0x40ae, 0x87,0xe8, 0x3e,0x5a,0xf9,0xbf,0x09,0x48);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f9d711c3-9777-40ae-87e8-3e5af9bf0948")
IDWriteRenderingParams2 : public IDWriteRenderingParams1
{
    virtual DWRITE_GRID_FIT_MODE STDMETHODCALLTYPE GetGridFitMode(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteRenderingParams2, 0xf9d711c3, 0x9777, 0x40ae, 0x87,0xe8, 0x3e,0x5a,0xf9,0xbf,0x09,0x48)
#endif
#else
typedef struct IDWriteRenderingParams2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteRenderingParams2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteRenderingParams2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteRenderingParams2 *This);

    /*** IDWriteRenderingParams methods ***/
    FLOAT (STDMETHODCALLTYPE *GetGamma)(
        IDWriteRenderingParams2 *This);

    FLOAT (STDMETHODCALLTYPE *GetEnhancedContrast)(
        IDWriteRenderingParams2 *This);

    FLOAT (STDMETHODCALLTYPE *GetClearTypeLevel)(
        IDWriteRenderingParams2 *This);

    DWRITE_PIXEL_GEOMETRY (STDMETHODCALLTYPE *GetPixelGeometry)(
        IDWriteRenderingParams2 *This);

    DWRITE_RENDERING_MODE (STDMETHODCALLTYPE *GetRenderingMode)(
        IDWriteRenderingParams2 *This);

    /*** IDWriteRenderingParams1 methods ***/
    FLOAT (STDMETHODCALLTYPE *GetGrayscaleEnhancedContrast)(
        IDWriteRenderingParams2 *This);

    /*** IDWriteRenderingParams2 methods ***/
    DWRITE_GRID_FIT_MODE (STDMETHODCALLTYPE *GetGridFitMode)(
        IDWriteRenderingParams2 *This);

    END_INTERFACE
} IDWriteRenderingParams2Vtbl;

interface IDWriteRenderingParams2 {
    CONST_VTBL IDWriteRenderingParams2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteRenderingParams2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteRenderingParams2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteRenderingParams2_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteRenderingParams methods ***/
#define IDWriteRenderingParams2_GetGamma(This) (This)->lpVtbl->GetGamma(This)
#define IDWriteRenderingParams2_GetEnhancedContrast(This) (This)->lpVtbl->GetEnhancedContrast(This)
#define IDWriteRenderingParams2_GetClearTypeLevel(This) (This)->lpVtbl->GetClearTypeLevel(This)
#define IDWriteRenderingParams2_GetPixelGeometry(This) (This)->lpVtbl->GetPixelGeometry(This)
#define IDWriteRenderingParams2_GetRenderingMode(This) (This)->lpVtbl->GetRenderingMode(This)
/*** IDWriteRenderingParams1 methods ***/
#define IDWriteRenderingParams2_GetGrayscaleEnhancedContrast(This) (This)->lpVtbl->GetGrayscaleEnhancedContrast(This)
/*** IDWriteRenderingParams2 methods ***/
#define IDWriteRenderingParams2_GetGridFitMode(This) (This)->lpVtbl->GetGridFitMode(This)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteRenderingParams2_QueryInterface(IDWriteRenderingParams2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteRenderingParams2_AddRef(IDWriteRenderingParams2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteRenderingParams2_Release(IDWriteRenderingParams2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteRenderingParams methods ***/
static inline FLOAT IDWriteRenderingParams2_GetGamma(IDWriteRenderingParams2* This) {
    return This->lpVtbl->GetGamma(This);
}
static inline FLOAT IDWriteRenderingParams2_GetEnhancedContrast(IDWriteRenderingParams2* This) {
    return This->lpVtbl->GetEnhancedContrast(This);
}
static inline FLOAT IDWriteRenderingParams2_GetClearTypeLevel(IDWriteRenderingParams2* This) {
    return This->lpVtbl->GetClearTypeLevel(This);
}
static inline DWRITE_PIXEL_GEOMETRY IDWriteRenderingParams2_GetPixelGeometry(IDWriteRenderingParams2* This) {
    return This->lpVtbl->GetPixelGeometry(This);
}
static inline DWRITE_RENDERING_MODE IDWriteRenderingParams2_GetRenderingMode(IDWriteRenderingParams2* This) {
    return This->lpVtbl->GetRenderingMode(This);
}
/*** IDWriteRenderingParams1 methods ***/
static inline FLOAT IDWriteRenderingParams2_GetGrayscaleEnhancedContrast(IDWriteRenderingParams2* This) {
    return This->lpVtbl->GetGrayscaleEnhancedContrast(This);
}
/*** IDWriteRenderingParams2 methods ***/
static inline DWRITE_GRID_FIT_MODE IDWriteRenderingParams2_GetGridFitMode(IDWriteRenderingParams2* This) {
    return This->lpVtbl->GetGridFitMode(This);
}
#endif
#endif

#endif


#endif  /* __IDWriteRenderingParams2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IDWriteFactory2 interface
 */
#ifndef __IDWriteFactory2_INTERFACE_DEFINED__
#define __IDWriteFactory2_INTERFACE_DEFINED__

DEFINE_GUID(IID_IDWriteFactory2, 0x0439fc60, 0xca44, 0x4994, 0x8d,0xee, 0x3a,0x9a,0xf7,0xb7,0x32,0xec);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0439fc60-ca44-4994-8dee-3a9af7b732ec")
IDWriteFactory2 : public IDWriteFactory1
{
    virtual HRESULT STDMETHODCALLTYPE GetSystemFontFallback(
        IDWriteFontFallback **fallback) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateFontFallbackBuilder(
        IDWriteFontFallbackBuilder **fallbackbuilder) = 0;

    virtual HRESULT STDMETHODCALLTYPE TranslateColorGlyphRun(
        FLOAT originX,
        FLOAT originY,
        const DWRITE_GLYPH_RUN *run,
        const DWRITE_GLYPH_RUN_DESCRIPTION *rundescr,
        DWRITE_MEASURING_MODE mode,
        const DWRITE_MATRIX *transform,
        UINT32 palette_index,
        IDWriteColorGlyphRunEnumerator **colorlayers) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateCustomRenderingParams(
        FLOAT gamma,
        FLOAT contrast,
        FLOAT grayscalecontrast,
        FLOAT cleartypeLevel,
        DWRITE_PIXEL_GEOMETRY pixelGeometry,
        DWRITE_RENDERING_MODE renderingMode,
        DWRITE_GRID_FIT_MODE gridFitMode,
        IDWriteRenderingParams2 **params) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateGlyphRunAnalysis(
        const DWRITE_GLYPH_RUN *run,
        const DWRITE_MATRIX *transform,
        DWRITE_RENDERING_MODE renderingMode,
        DWRITE_MEASURING_MODE measuringMode,
        DWRITE_GRID_FIT_MODE gridFitMode,
        DWRITE_TEXT_ANTIALIAS_MODE antialiasMode,
        FLOAT originX,
        FLOAT originY,
        IDWriteGlyphRunAnalysis **analysis) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(IDWriteFactory2, 0x0439fc60, 0xca44, 0x4994, 0x8d,0xee, 0x3a,0x9a,0xf7,0xb7,0x32,0xec)
#endif
#else
typedef struct IDWriteFactory2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        IDWriteFactory2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        IDWriteFactory2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        IDWriteFactory2 *This);

    /*** IDWriteFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSystemFontCollection)(
        IDWriteFactory2 *This,
        IDWriteFontCollection **collection,
        WINBOOL check_for_updates);

    HRESULT (STDMETHODCALLTYPE *CreateCustomFontCollection)(
        IDWriteFactory2 *This,
        IDWriteFontCollectionLoader *loader,
        const void *key,
        UINT32 key_size,
        IDWriteFontCollection **collection);

    HRESULT (STDMETHODCALLTYPE *RegisterFontCollectionLoader)(
        IDWriteFactory2 *This,
        IDWriteFontCollectionLoader *loader);

    HRESULT (STDMETHODCALLTYPE *UnregisterFontCollectionLoader)(
        IDWriteFactory2 *This,
        IDWriteFontCollectionLoader *loader);

    HRESULT (STDMETHODCALLTYPE *CreateFontFileReference)(
        IDWriteFactory2 *This,
        const WCHAR *path,
        const FILETIME *writetime,
        IDWriteFontFile **font_file);

    HRESULT (STDMETHODCALLTYPE *CreateCustomFontFileReference)(
        IDWriteFactory2 *This,
        const void *reference_key,
        UINT32 key_size,
        IDWriteFontFileLoader *loader,
        IDWriteFontFile **font_file);

    HRESULT (STDMETHODCALLTYPE *CreateFontFace)(
        IDWriteFactory2 *This,
        DWRITE_FONT_FACE_TYPE facetype,
        UINT32 files_number,
        IDWriteFontFile *const *font_files,
        UINT32 index,
        DWRITE_FONT_SIMULATIONS sim_flags,
        IDWriteFontFace **font_face);

    HRESULT (STDMETHODCALLTYPE *CreateRenderingParams)(
        IDWriteFactory2 *This,
        IDWriteRenderingParams **params);

    HRESULT (STDMETHODCALLTYPE *CreateMonitorRenderingParams)(
        IDWriteFactory2 *This,
        HMONITOR monitor,
        IDWriteRenderingParams **params);

    HRESULT (STDMETHODCALLTYPE *CreateCustomRenderingParams)(
        IDWriteFactory2 *This,
        FLOAT gamma,
        FLOAT enhancedContrast,
        FLOAT cleartype_level,
        DWRITE_PIXEL_GEOMETRY geometry,
        DWRITE_RENDERING_MODE mode,
        IDWriteRenderingParams **params);

    HRESULT (STDMETHODCALLTYPE *RegisterFontFileLoader)(
        IDWriteFactory2 *This,
        IDWriteFontFileLoader *loader);

    HRESULT (STDMETHODCALLTYPE *UnregisterFontFileLoader)(
        IDWriteFactory2 *This,
        IDWriteFontFileLoader *loader);

    HRESULT (STDMETHODCALLTYPE *CreateTextFormat)(
        IDWriteFactory2 *This,
        const WCHAR *family_name,
        IDWriteFontCollection *collection,
        DWRITE_FONT_WEIGHT weight,
        DWRITE_FONT_STYLE style,
        DWRITE_FONT_STRETCH stretch,
        FLOAT size,
        const WCHAR *locale,
        IDWriteTextFormat **format);

    HRESULT (STDMETHODCALLTYPE *CreateTypography)(
        IDWriteFactory2 *This,
        IDWriteTypography **typography);

    HRESULT (STDMETHODCALLTYPE *GetGdiInterop)(
        IDWriteFactory2 *This,
        IDWriteGdiInterop **gdi_interop);

    HRESULT (STDMETHODCALLTYPE *CreateTextLayout)(
        IDWriteFactory2 *This,
        const WCHAR *string,
        UINT32 len,
        IDWriteTextFormat *format,
        FLOAT max_width,
        FLOAT max_height,
        IDWriteTextLayout **layout);

    HRESULT (STDMETHODCALLTYPE *CreateGdiCompatibleTextLayout)(
        IDWriteFactory2 *This,
        const WCHAR *string,
        UINT32 len,
        IDWriteTextFormat *format,
        FLOAT layout_width,
        FLOAT layout_height,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        WINBOOL use_gdi_natural,
        IDWriteTextLayout **layout);

    HRESULT (STDMETHODCALLTYPE *CreateEllipsisTrimmingSign)(
        IDWriteFactory2 *This,
        IDWriteTextFormat *format,
        IDWriteInlineObject **trimming_sign);

    HRESULT (STDMETHODCALLTYPE *CreateTextAnalyzer)(
        IDWriteFactory2 *This,
        IDWriteTextAnalyzer **analyzer);

    HRESULT (STDMETHODCALLTYPE *CreateNumberSubstitution)(
        IDWriteFactory2 *This,
        DWRITE_NUMBER_SUBSTITUTION_METHOD method,
        const WCHAR *locale,
        WINBOOL ignore_user_override,
        IDWriteNumberSubstitution **substitution);

    HRESULT (STDMETHODCALLTYPE *CreateGlyphRunAnalysis)(
        IDWriteFactory2 *This,
        const DWRITE_GLYPH_RUN *glyph_run,
        FLOAT pixels_per_dip,
        const DWRITE_MATRIX *transform,
        DWRITE_RENDERING_MODE rendering_mode,
        DWRITE_MEASURING_MODE measuring_mode,
        FLOAT baseline_x,
        FLOAT baseline_y,
        IDWriteGlyphRunAnalysis **analysis);

    /*** IDWriteFactory1 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetEudcFontCollection)(
        IDWriteFactory2 *This,
        IDWriteFontCollection **collection,
        WINBOOL check_for_updates);

    HRESULT (STDMETHODCALLTYPE *IDWriteFactory1_CreateCustomRenderingParams)(
        IDWriteFactory2 *This,
        FLOAT gamma,
        FLOAT enhcontrast,
        FLOAT enhcontrast_grayscale,
        FLOAT cleartype_level,
        DWRITE_PIXEL_GEOMETRY geometry,
        DWRITE_RENDERING_MODE mode,
        IDWriteRenderingParams1 **params);

    /*** IDWriteFactory2 methods ***/
    HRESULT (STDMETHODCALLTYPE *GetSystemFontFallback)(
        IDWriteFactory2 *This,
        IDWriteFontFallback **fallback);

    HRESULT (STDMETHODCALLTYPE *CreateFontFallbackBuilder)(
        IDWriteFactory2 *This,
        IDWriteFontFallbackBuilder **fallbackbuilder);

    HRESULT (STDMETHODCALLTYPE *TranslateColorGlyphRun)(
        IDWriteFactory2 *This,
        FLOAT originX,
        FLOAT originY,
        const DWRITE_GLYPH_RUN *run,
        const DWRITE_GLYPH_RUN_DESCRIPTION *rundescr,
        DWRITE_MEASURING_MODE mode,
        const DWRITE_MATRIX *transform,
        UINT32 palette_index,
        IDWriteColorGlyphRunEnumerator **colorlayers);

    HRESULT (STDMETHODCALLTYPE *IDWriteFactory2_CreateCustomRenderingParams)(
        IDWriteFactory2 *This,
        FLOAT gamma,
        FLOAT contrast,
        FLOAT grayscalecontrast,
        FLOAT cleartypeLevel,
        DWRITE_PIXEL_GEOMETRY pixelGeometry,
        DWRITE_RENDERING_MODE renderingMode,
        DWRITE_GRID_FIT_MODE gridFitMode,
        IDWriteRenderingParams2 **params);

    HRESULT (STDMETHODCALLTYPE *IDWriteFactory2_CreateGlyphRunAnalysis)(
        IDWriteFactory2 *This,
        const DWRITE_GLYPH_RUN *run,
        const DWRITE_MATRIX *transform,
        DWRITE_RENDERING_MODE renderingMode,
        DWRITE_MEASURING_MODE measuringMode,
        DWRITE_GRID_FIT_MODE gridFitMode,
        DWRITE_TEXT_ANTIALIAS_MODE antialiasMode,
        FLOAT originX,
        FLOAT originY,
        IDWriteGlyphRunAnalysis **analysis);

    END_INTERFACE
} IDWriteFactory2Vtbl;

interface IDWriteFactory2 {
    CONST_VTBL IDWriteFactory2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define IDWriteFactory2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define IDWriteFactory2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define IDWriteFactory2_Release(This) (This)->lpVtbl->Release(This)
/*** IDWriteFactory methods ***/
#define IDWriteFactory2_GetSystemFontCollection(This,collection,check_for_updates) (This)->lpVtbl->GetSystemFontCollection(This,collection,check_for_updates)
#define IDWriteFactory2_CreateCustomFontCollection(This,loader,key,key_size,collection) (This)->lpVtbl->CreateCustomFontCollection(This,loader,key,key_size,collection)
#define IDWriteFactory2_RegisterFontCollectionLoader(This,loader) (This)->lpVtbl->RegisterFontCollectionLoader(This,loader)
#define IDWriteFactory2_UnregisterFontCollectionLoader(This,loader) (This)->lpVtbl->UnregisterFontCollectionLoader(This,loader)
#define IDWriteFactory2_CreateFontFileReference(This,path,writetime,font_file) (This)->lpVtbl->CreateFontFileReference(This,path,writetime,font_file)
#define IDWriteFactory2_CreateCustomFontFileReference(This,reference_key,key_size,loader,font_file) (This)->lpVtbl->CreateCustomFontFileReference(This,reference_key,key_size,loader,font_file)
#define IDWriteFactory2_CreateFontFace(This,facetype,files_number,font_files,index,sim_flags,font_face) (This)->lpVtbl->CreateFontFace(This,facetype,files_number,font_files,index,sim_flags,font_face)
#define IDWriteFactory2_CreateRenderingParams(This,params) (This)->lpVtbl->CreateRenderingParams(This,params)
#define IDWriteFactory2_CreateMonitorRenderingParams(This,monitor,params) (This)->lpVtbl->CreateMonitorRenderingParams(This,monitor,params)
#define IDWriteFactory2_RegisterFontFileLoader(This,loader) (This)->lpVtbl->RegisterFontFileLoader(This,loader)
#define IDWriteFactory2_UnregisterFontFileLoader(This,loader) (This)->lpVtbl->UnregisterFontFileLoader(This,loader)
#define IDWriteFactory2_CreateTextFormat(This,family_name,collection,weight,style,stretch,size,locale,format) (This)->lpVtbl->CreateTextFormat(This,family_name,collection,weight,style,stretch,size,locale,format)
#define IDWriteFactory2_CreateTypography(This,typography) (This)->lpVtbl->CreateTypography(This,typography)
#define IDWriteFactory2_GetGdiInterop(This,gdi_interop) (This)->lpVtbl->GetGdiInterop(This,gdi_interop)
#define IDWriteFactory2_CreateTextLayout(This,string,len,format,max_width,max_height,layout) (This)->lpVtbl->CreateTextLayout(This,string,len,format,max_width,max_height,layout)
#define IDWriteFactory2_CreateGdiCompatibleTextLayout(This,string,len,format,layout_width,layout_height,pixels_per_dip,transform,use_gdi_natural,layout) (This)->lpVtbl->CreateGdiCompatibleTextLayout(This,string,len,format,layout_width,layout_height,pixels_per_dip,transform,use_gdi_natural,layout)
#define IDWriteFactory2_CreateEllipsisTrimmingSign(This,format,trimming_sign) (This)->lpVtbl->CreateEllipsisTrimmingSign(This,format,trimming_sign)
#define IDWriteFactory2_CreateTextAnalyzer(This,analyzer) (This)->lpVtbl->CreateTextAnalyzer(This,analyzer)
#define IDWriteFactory2_CreateNumberSubstitution(This,method,locale,ignore_user_override,substitution) (This)->lpVtbl->CreateNumberSubstitution(This,method,locale,ignore_user_override,substitution)
/*** IDWriteFactory1 methods ***/
#define IDWriteFactory2_GetEudcFontCollection(This,collection,check_for_updates) (This)->lpVtbl->GetEudcFontCollection(This,collection,check_for_updates)
/*** IDWriteFactory2 methods ***/
#define IDWriteFactory2_GetSystemFontFallback(This,fallback) (This)->lpVtbl->GetSystemFontFallback(This,fallback)
#define IDWriteFactory2_CreateFontFallbackBuilder(This,fallbackbuilder) (This)->lpVtbl->CreateFontFallbackBuilder(This,fallbackbuilder)
#define IDWriteFactory2_TranslateColorGlyphRun(This,originX,originY,run,rundescr,mode,transform,palette_index,colorlayers) (This)->lpVtbl->TranslateColorGlyphRun(This,originX,originY,run,rundescr,mode,transform,palette_index,colorlayers)
#define IDWriteFactory2_CreateCustomRenderingParams(This,gamma,contrast,grayscalecontrast,cleartypeLevel,pixelGeometry,renderingMode,gridFitMode,params) (This)->lpVtbl->IDWriteFactory2_CreateCustomRenderingParams(This,gamma,contrast,grayscalecontrast,cleartypeLevel,pixelGeometry,renderingMode,gridFitMode,params)
#define IDWriteFactory2_CreateGlyphRunAnalysis(This,run,transform,renderingMode,measuringMode,gridFitMode,antialiasMode,originX,originY,analysis) (This)->lpVtbl->IDWriteFactory2_CreateGlyphRunAnalysis(This,run,transform,renderingMode,measuringMode,gridFitMode,antialiasMode,originX,originY,analysis)
#else
/*** IUnknown methods ***/
static inline HRESULT IDWriteFactory2_QueryInterface(IDWriteFactory2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG IDWriteFactory2_AddRef(IDWriteFactory2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG IDWriteFactory2_Release(IDWriteFactory2* This) {
    return This->lpVtbl->Release(This);
}
/*** IDWriteFactory methods ***/
static inline HRESULT IDWriteFactory2_GetSystemFontCollection(IDWriteFactory2* This,IDWriteFontCollection **collection,WINBOOL check_for_updates) {
    return This->lpVtbl->GetSystemFontCollection(This,collection,check_for_updates);
}
static inline HRESULT IDWriteFactory2_CreateCustomFontCollection(IDWriteFactory2* This,IDWriteFontCollectionLoader *loader,const void *key,UINT32 key_size,IDWriteFontCollection **collection) {
    return This->lpVtbl->CreateCustomFontCollection(This,loader,key,key_size,collection);
}
static inline HRESULT IDWriteFactory2_RegisterFontCollectionLoader(IDWriteFactory2* This,IDWriteFontCollectionLoader *loader) {
    return This->lpVtbl->RegisterFontCollectionLoader(This,loader);
}
static inline HRESULT IDWriteFactory2_UnregisterFontCollectionLoader(IDWriteFactory2* This,IDWriteFontCollectionLoader *loader) {
    return This->lpVtbl->UnregisterFontCollectionLoader(This,loader);
}
static inline HRESULT IDWriteFactory2_CreateFontFileReference(IDWriteFactory2* This,const WCHAR *path,const FILETIME *writetime,IDWriteFontFile **font_file) {
    return This->lpVtbl->CreateFontFileReference(This,path,writetime,font_file);
}
static inline HRESULT IDWriteFactory2_CreateCustomFontFileReference(IDWriteFactory2* This,const void *reference_key,UINT32 key_size,IDWriteFontFileLoader *loader,IDWriteFontFile **font_file) {
    return This->lpVtbl->CreateCustomFontFileReference(This,reference_key,key_size,loader,font_file);
}
static inline HRESULT IDWriteFactory2_CreateFontFace(IDWriteFactory2* This,DWRITE_FONT_FACE_TYPE facetype,UINT32 files_number,IDWriteFontFile *const *font_files,UINT32 index,DWRITE_FONT_SIMULATIONS sim_flags,IDWriteFontFace **font_face) {
    return This->lpVtbl->CreateFontFace(This,facetype,files_number,font_files,index,sim_flags,font_face);
}
static inline HRESULT IDWriteFactory2_CreateRenderingParams(IDWriteFactory2* This,IDWriteRenderingParams **params) {
    return This->lpVtbl->CreateRenderingParams(This,params);
}
static inline HRESULT IDWriteFactory2_CreateMonitorRenderingParams(IDWriteFactory2* This,HMONITOR monitor,IDWriteRenderingParams **params) {
    return This->lpVtbl->CreateMonitorRenderingParams(This,monitor,params);
}
static inline HRESULT IDWriteFactory2_RegisterFontFileLoader(IDWriteFactory2* This,IDWriteFontFileLoader *loader) {
    return This->lpVtbl->RegisterFontFileLoader(This,loader);
}
static inline HRESULT IDWriteFactory2_UnregisterFontFileLoader(IDWriteFactory2* This,IDWriteFontFileLoader *loader) {
    return This->lpVtbl->UnregisterFontFileLoader(This,loader);
}
static inline HRESULT IDWriteFactory2_CreateTextFormat(IDWriteFactory2* This,const WCHAR *family_name,IDWriteFontCollection *collection,DWRITE_FONT_WEIGHT weight,DWRITE_FONT_STYLE style,DWRITE_FONT_STRETCH stretch,FLOAT size,const WCHAR *locale,IDWriteTextFormat **format) {
    return This->lpVtbl->CreateTextFormat(This,family_name,collection,weight,style,stretch,size,locale,format);
}
static inline HRESULT IDWriteFactory2_CreateTypography(IDWriteFactory2* This,IDWriteTypography **typography) {
    return This->lpVtbl->CreateTypography(This,typography);
}
static inline HRESULT IDWriteFactory2_GetGdiInterop(IDWriteFactory2* This,IDWriteGdiInterop **gdi_interop) {
    return This->lpVtbl->GetGdiInterop(This,gdi_interop);
}
static inline HRESULT IDWriteFactory2_CreateTextLayout(IDWriteFactory2* This,const WCHAR *string,UINT32 len,IDWriteTextFormat *format,FLOAT max_width,FLOAT max_height,IDWriteTextLayout **layout) {
    return This->lpVtbl->CreateTextLayout(This,string,len,format,max_width,max_height,layout);
}
static inline HRESULT IDWriteFactory2_CreateGdiCompatibleTextLayout(IDWriteFactory2* This,const WCHAR *string,UINT32 len,IDWriteTextFormat *format,FLOAT layout_width,FLOAT layout_height,FLOAT pixels_per_dip,const DWRITE_MATRIX *transform,WINBOOL use_gdi_natural,IDWriteTextLayout **layout) {
    return This->lpVtbl->CreateGdiCompatibleTextLayout(This,string,len,format,layout_width,layout_height,pixels_per_dip,transform,use_gdi_natural,layout);
}
static inline HRESULT IDWriteFactory2_CreateEllipsisTrimmingSign(IDWriteFactory2* This,IDWriteTextFormat *format,IDWriteInlineObject **trimming_sign) {
    return This->lpVtbl->CreateEllipsisTrimmingSign(This,format,trimming_sign);
}
static inline HRESULT IDWriteFactory2_CreateTextAnalyzer(IDWriteFactory2* This,IDWriteTextAnalyzer **analyzer) {
    return This->lpVtbl->CreateTextAnalyzer(This,analyzer);
}
static inline HRESULT IDWriteFactory2_CreateNumberSubstitution(IDWriteFactory2* This,DWRITE_NUMBER_SUBSTITUTION_METHOD method,const WCHAR *locale,WINBOOL ignore_user_override,IDWriteNumberSubstitution **substitution) {
    return This->lpVtbl->CreateNumberSubstitution(This,method,locale,ignore_user_override,substitution);
}
/*** IDWriteFactory1 methods ***/
static inline HRESULT IDWriteFactory2_GetEudcFontCollection(IDWriteFactory2* This,IDWriteFontCollection **collection,WINBOOL check_for_updates) {
    return This->lpVtbl->GetEudcFontCollection(This,collection,check_for_updates);
}
/*** IDWriteFactory2 methods ***/
static inline HRESULT IDWriteFactory2_GetSystemFontFallback(IDWriteFactory2* This,IDWriteFontFallback **fallback) {
    return This->lpVtbl->GetSystemFontFallback(This,fallback);
}
static inline HRESULT IDWriteFactory2_CreateFontFallbackBuilder(IDWriteFactory2* This,IDWriteFontFallbackBuilder **fallbackbuilder) {
    return This->lpVtbl->CreateFontFallbackBuilder(This,fallbackbuilder);
}
static inline HRESULT IDWriteFactory2_TranslateColorGlyphRun(IDWriteFactory2* This,FLOAT originX,FLOAT originY,const DWRITE_GLYPH_RUN *run,const DWRITE_GLYPH_RUN_DESCRIPTION *rundescr,DWRITE_MEASURING_MODE mode,const DWRITE_MATRIX *transform,UINT32 palette_index,IDWriteColorGlyphRunEnumerator **colorlayers) {
    return This->lpVtbl->TranslateColorGlyphRun(This,originX,originY,run,rundescr,mode,transform,palette_index,colorlayers);
}
static inline HRESULT IDWriteFactory2_CreateCustomRenderingParams(IDWriteFactory2* This,FLOAT gamma,FLOAT contrast,FLOAT grayscalecontrast,FLOAT cleartypeLevel,DWRITE_PIXEL_GEOMETRY pixelGeometry,DWRITE_RENDERING_MODE renderingMode,DWRITE_GRID_FIT_MODE gridFitMode,IDWriteRenderingParams2 **params) {
    return This->lpVtbl->IDWriteFactory2_CreateCustomRenderingParams(This,gamma,contrast,grayscalecontrast,cleartypeLevel,pixelGeometry,renderingMode,gridFitMode,params);
}
static inline HRESULT IDWriteFactory2_CreateGlyphRunAnalysis(IDWriteFactory2* This,const DWRITE_GLYPH_RUN *run,const DWRITE_MATRIX *transform,DWRITE_RENDERING_MODE renderingMode,DWRITE_MEASURING_MODE measuringMode,DWRITE_GRID_FIT_MODE gridFitMode,DWRITE_TEXT_ANTIALIAS_MODE antialiasMode,FLOAT originX,FLOAT originY,IDWriteGlyphRunAnalysis **analysis) {
    return This->lpVtbl->IDWriteFactory2_CreateGlyphRunAnalysis(This,run,transform,renderingMode,measuringMode,gridFitMode,antialiasMode,originX,originY,analysis);
}
#endif
#endif

#endif


#endif  /* __IDWriteFactory2_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __dwrite_2_h__ */
