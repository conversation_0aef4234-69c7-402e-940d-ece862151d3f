/*** Autogenerated by WIDL 10.12 from include/d3d12video.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __d3d12video_h__
#define __d3d12video_h__

/* Forward declarations */

#ifndef __ID3D12VideoDecoderHeap_FWD_DEFINED__
#define __ID3D12VideoDecoderHeap_FWD_DEFINED__
typedef interface ID3D12VideoDecoderHeap ID3D12VideoDecoderHeap;
#ifdef __cplusplus
interface ID3D12VideoDecoderHeap;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoDevice_FWD_DEFINED__
#define __ID3D12VideoDevice_FWD_DEFINED__
typedef interface ID3D12VideoDevice ID3D12VideoDevice;
#ifdef __cplusplus
interface ID3D12VideoDevice;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoDecoder_FWD_DEFINED__
#define __ID3D12VideoDecoder_FWD_DEFINED__
typedef interface ID3D12VideoDecoder ID3D12VideoDecoder;
#ifdef __cplusplus
interface ID3D12VideoDecoder;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoDecodeCommandList_FWD_DEFINED__
#define __ID3D12VideoDecodeCommandList_FWD_DEFINED__
typedef interface ID3D12VideoDecodeCommandList ID3D12VideoDecodeCommandList;
#ifdef __cplusplus
interface ID3D12VideoDecodeCommandList;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoDecodeCommandList1_FWD_DEFINED__
#define __ID3D12VideoDecodeCommandList1_FWD_DEFINED__
typedef interface ID3D12VideoDecodeCommandList1 ID3D12VideoDecodeCommandList1;
#ifdef __cplusplus
interface ID3D12VideoDecodeCommandList1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoMotionEstimator_FWD_DEFINED__
#define __ID3D12VideoMotionEstimator_FWD_DEFINED__
typedef interface ID3D12VideoMotionEstimator ID3D12VideoMotionEstimator;
#ifdef __cplusplus
interface ID3D12VideoMotionEstimator;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoMotionVectorHeap_FWD_DEFINED__
#define __ID3D12VideoMotionVectorHeap_FWD_DEFINED__
typedef interface ID3D12VideoMotionVectorHeap ID3D12VideoMotionVectorHeap;
#ifdef __cplusplus
interface ID3D12VideoMotionVectorHeap;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoDevice1_FWD_DEFINED__
#define __ID3D12VideoDevice1_FWD_DEFINED__
typedef interface ID3D12VideoDevice1 ID3D12VideoDevice1;
#ifdef __cplusplus
interface ID3D12VideoDevice1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoEncodeCommandList_FWD_DEFINED__
#define __ID3D12VideoEncodeCommandList_FWD_DEFINED__
typedef interface ID3D12VideoEncodeCommandList ID3D12VideoEncodeCommandList;
#ifdef __cplusplus
interface ID3D12VideoEncodeCommandList;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoExtensionCommand_FWD_DEFINED__
#define __ID3D12VideoExtensionCommand_FWD_DEFINED__
typedef interface ID3D12VideoExtensionCommand ID3D12VideoExtensionCommand;
#ifdef __cplusplus
interface ID3D12VideoExtensionCommand;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoDevice2_FWD_DEFINED__
#define __ID3D12VideoDevice2_FWD_DEFINED__
typedef interface ID3D12VideoDevice2 ID3D12VideoDevice2;
#ifdef __cplusplus
interface ID3D12VideoDevice2;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoEncodeCommandList1_FWD_DEFINED__
#define __ID3D12VideoEncodeCommandList1_FWD_DEFINED__
typedef interface ID3D12VideoEncodeCommandList1 ID3D12VideoEncodeCommandList1;
#ifdef __cplusplus
interface ID3D12VideoEncodeCommandList1;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoEncoder_FWD_DEFINED__
#define __ID3D12VideoEncoder_FWD_DEFINED__
typedef interface ID3D12VideoEncoder ID3D12VideoEncoder;
#ifdef __cplusplus
interface ID3D12VideoEncoder;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoEncoderHeap_FWD_DEFINED__
#define __ID3D12VideoEncoderHeap_FWD_DEFINED__
typedef interface ID3D12VideoEncoderHeap ID3D12VideoEncoderHeap;
#ifdef __cplusplus
interface ID3D12VideoEncoderHeap;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoDevice3_FWD_DEFINED__
#define __ID3D12VideoDevice3_FWD_DEFINED__
typedef interface ID3D12VideoDevice3 ID3D12VideoDevice3;
#ifdef __cplusplus
interface ID3D12VideoDevice3;
#endif /* __cplusplus */
#endif

#ifndef __ID3D12VideoEncodeCommandList2_FWD_DEFINED__
#define __ID3D12VideoEncodeCommandList2_FWD_DEFINED__
typedef interface ID3D12VideoEncodeCommandList2 ID3D12VideoEncodeCommandList2;
#ifdef __cplusplus
interface ID3D12VideoEncodeCommandList2;
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <oaidl.h>
#include <ocidl.h>
#include <dxgicommon.h>
#include <d3d12.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef enum D3D12_VIDEO_FIELD_TYPE {
    D3D12_VIDEO_FIELD_TYPE_NONE = 0,
    D3D12_VIDEO_FIELD_TYPE_INTERLACED_TOP_FIELD_FIRST = 1,
    D3D12_VIDEO_FIELD_TYPE_INTERLACED_BOTTOM_FIELD_FIRST = 2
} D3D12_VIDEO_FIELD_TYPE;
typedef enum D3D12_VIDEO_FRAME_STEREO_FORMAT {
    D3D12_VIDEO_FRAME_STEREO_FORMAT_NONE = 0,
    D3D12_VIDEO_FRAME_STEREO_FORMAT_MONO = 1,
    D3D12_VIDEO_FRAME_STEREO_FORMAT_HORIZONTAL = 2,
    D3D12_VIDEO_FRAME_STEREO_FORMAT_VERTICAL = 3,
    D3D12_VIDEO_FRAME_STEREO_FORMAT_SEPARATE = 4
} D3D12_VIDEO_FRAME_STEREO_FORMAT;
typedef enum D3D12_VIDEO_FRAME_CODED_INTERLACE_TYPE {
    D3D12_VIDEO_FRAME_CODED_INTERLACE_TYPE_NONE = 0,
    D3D12_VIDEO_FRAME_CODED_INTERLACE_TYPE_FIELD_BASED = 1
} D3D12_VIDEO_FRAME_CODED_INTERLACE_TYPE;
typedef enum D3D12_FEATURE_VIDEO {
    D3D12_FEATURE_VIDEO_DECODE_SUPPORT = 0,
    D3D12_FEATURE_VIDEO_DECODE_PROFILES = 1,
    D3D12_FEATURE_VIDEO_DECODE_FORMATS = 2,
    D3D12_FEATURE_VIDEO_DECODE_CONVERSION_SUPPORT = 3,
    D3D12_FEATURE_VIDEO_PROCESS_SUPPORT = 5,
    D3D12_FEATURE_VIDEO_PROCESS_MAX_INPUT_STREAMS = 6,
    D3D12_FEATURE_VIDEO_PROCESS_REFERENCE_INFO = 7,
    D3D12_FEATURE_VIDEO_DECODER_HEAP_SIZE = 8,
    D3D12_FEATURE_VIDEO_PROCESSOR_SIZE = 9,
    D3D12_FEATURE_VIDEO_DECODE_PROFILE_COUNT = 10,
    D3D12_FEATURE_VIDEO_DECODE_FORMAT_COUNT = 11,
    D3D12_FEATURE_VIDEO_ARCHITECTURE = 17,
    D3D12_FEATURE_VIDEO_DECODE_HISTOGRAM = 18,
    D3D12_FEATURE_VIDEO_FEATURE_AREA_SUPPORT = 19,
    D3D12_FEATURE_VIDEO_MOTION_ESTIMATOR = 20,
    D3D12_FEATURE_VIDEO_MOTION_ESTIMATOR_SIZE = 21,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMAND_COUNT = 22,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMANDS = 23,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMAND_PARAMETER_COUNT = 24,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMAND_PARAMETERS = 25,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMAND_SUPPORT = 26,
    D3D12_FEATURE_VIDEO_EXTENSION_COMMAND_SIZE = 27,
    D3D12_FEATURE_VIDEO_DECODE_PROTECTED_RESOURCES = 28,
    D3D12_FEATURE_VIDEO_PROCESS_PROTECTED_RESOURCES = 29,
    D3D12_FEATURE_VIDEO_MOTION_ESTIMATOR_PROTECTED_RESOURCES = 30,
    D3D12_FEATURE_VIDEO_DECODER_HEAP_SIZE1 = 31,
    D3D12_FEATURE_VIDEO_PROCESSOR_SIZE1 = 32,
    D3D12_FEATURE_VIDEO_ENCODER_CODEC = 33,
    D3D12_FEATURE_VIDEO_ENCODER_PROFILE_LEVEL = 34,
    D3D12_FEATURE_VIDEO_ENCODER_OUTPUT_RESOLUTION_RATIOS_COUNT = 35,
    D3D12_FEATURE_VIDEO_ENCODER_OUTPUT_RESOLUTION = 36,
    D3D12_FEATURE_VIDEO_ENCODER_INPUT_FORMAT = 37,
    D3D12_FEATURE_VIDEO_ENCODER_RATE_CONTROL_MODE = 38,
    D3D12_FEATURE_VIDEO_ENCODER_INTRA_REFRESH_MODE = 39,
    D3D12_FEATURE_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE = 40,
    D3D12_FEATURE_VIDEO_ENCODER_HEAP_SIZE = 41,
    D3D12_FEATURE_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT = 42,
    D3D12_FEATURE_VIDEO_ENCODER_SUPPORT = 43,
    D3D12_FEATURE_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT = 44,
    D3D12_FEATURE_VIDEO_ENCODER_RESOURCE_REQUIREMENTS = 45
} D3D12_FEATURE_VIDEO;
typedef enum D3D12_BITSTREAM_ENCRYPTION_TYPE {
    D3D12_BITSTREAM_ENCRYPTION_TYPE_NONE = 0
} D3D12_BITSTREAM_ENCRYPTION_TYPE;
typedef struct D3D12_VIDEO_DECODE_CONFIGURATION {
    GUID DecodeProfile;
    D3D12_BITSTREAM_ENCRYPTION_TYPE BitstreamEncryption;
    D3D12_VIDEO_FRAME_CODED_INTERLACE_TYPE InterlaceType;
} D3D12_VIDEO_DECODE_CONFIGURATION;
typedef struct D3D12_VIDEO_DECODER_DESC {
    UINT NodeMask;
    D3D12_VIDEO_DECODE_CONFIGURATION Configuration;
} D3D12_VIDEO_DECODER_DESC;
typedef struct D3D12_VIDEO_DECODER_HEAP_DESC {
    UINT NodeMask;
    D3D12_VIDEO_DECODE_CONFIGURATION Configuration;
    UINT DecodeWidth;
    UINT DecodeHeight;
    DXGI_FORMAT Format;
    DXGI_RATIONAL FrameRate;
    UINT BitRate;
    UINT MaxDecodePictureBufferCount;
} D3D12_VIDEO_DECODER_HEAP_DESC;
typedef struct D3D12_VIDEO_SIZE_RANGE {
    UINT MaxWidth;
    UINT MaxHeight;
    UINT MinWidth;
    UINT MinHeight;
} D3D12_VIDEO_SIZE_RANGE;
typedef enum D3D12_VIDEO_PROCESS_FILTER {
    D3D12_VIDEO_PROCESS_FILTER_BRIGHTNESS = 0,
    D3D12_VIDEO_PROCESS_FILTER_CONTRAST = 1,
    D3D12_VIDEO_PROCESS_FILTER_HUE = 2,
    D3D12_VIDEO_PROCESS_FILTER_SATURATION = 3,
    D3D12_VIDEO_PROCESS_FILTER_NOISE_REDUCTION = 4,
    D3D12_VIDEO_PROCESS_FILTER_EDGE_ENHANCEMENT = 5,
    D3D12_VIDEO_PROCESS_FILTER_ANAMORPHIC_SCALING = 6,
    D3D12_VIDEO_PROCESS_FILTER_STEREO_ADJUSTMENT = 7
} D3D12_VIDEO_PROCESS_FILTER;
typedef enum D3D12_VIDEO_PROCESS_FILTER_FLAGS {
    D3D12_VIDEO_PROCESS_FILTER_FLAG_NONE = 0x0,
    D3D12_VIDEO_PROCESS_FILTER_FLAG_BRIGHTNESS = 1 << D3D12_VIDEO_PROCESS_FILTER_BRIGHTNESS,
    D3D12_VIDEO_PROCESS_FILTER_FLAG_CONTRAST = 1 << D3D12_VIDEO_PROCESS_FILTER_CONTRAST,
    D3D12_VIDEO_PROCESS_FILTER_FLAG_HUE = 1 << D3D12_VIDEO_PROCESS_FILTER_HUE,
    D3D12_VIDEO_PROCESS_FILTER_FLAG_SATURATION = 1 << D3D12_VIDEO_PROCESS_FILTER_SATURATION,
    D3D12_VIDEO_PROCESS_FILTER_FLAG_NOISE_REDUCTION = 1 << D3D12_VIDEO_PROCESS_FILTER_NOISE_REDUCTION,
    D3D12_VIDEO_PROCESS_FILTER_FLAG_EDGE_ENHANCEMENT = 1 << D3D12_VIDEO_PROCESS_FILTER_EDGE_ENHANCEMENT,
    D3D12_VIDEO_PROCESS_FILTER_FLAG_ANAMORPHIC_SCALING = 1 << D3D12_VIDEO_PROCESS_FILTER_ANAMORPHIC_SCALING,
    D3D12_VIDEO_PROCESS_FILTER_FLAG_STEREO_ADJUSTMENT = 1 << D3D12_VIDEO_PROCESS_FILTER_STEREO_ADJUSTMENT
} D3D12_VIDEO_PROCESS_FILTER_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_PROCESS_FILTER_FLAGS);
typedef enum D3D12_VIDEO_PROCESS_DEINTERLACE_FLAGS {
    D3D12_VIDEO_PROCESS_DEINTERLACE_FLAG_NONE = 0x0,
    D3D12_VIDEO_PROCESS_DEINTERLACE_FLAG_BOB = 0x1,
    D3D12_VIDEO_PROCESS_DEINTERLACE_FLAG_CUSTOM = 0x80000000
} D3D12_VIDEO_PROCESS_DEINTERLACE_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_PROCESS_DEINTERLACE_FLAGS );
typedef struct D3D12_VIDEO_PROCESS_LUMA_KEY {
    WINBOOL Enable;
    FLOAT Lower;
    FLOAT Upper;
} D3D12_VIDEO_PROCESS_LUMA_KEY;
typedef struct D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC {
    DXGI_FORMAT Format;
    DXGI_COLOR_SPACE_TYPE ColorSpace;
    DXGI_RATIONAL SourceAspectRatio;
    DXGI_RATIONAL DestinationAspectRatio;
    DXGI_RATIONAL FrameRate;
    D3D12_VIDEO_SIZE_RANGE SourceSizeRange;
    D3D12_VIDEO_SIZE_RANGE DestinationSizeRange;
    WINBOOL EnableOrientation;
    D3D12_VIDEO_PROCESS_FILTER_FLAGS FilterFlags;
    D3D12_VIDEO_FRAME_STEREO_FORMAT StereoFormat;
    D3D12_VIDEO_FIELD_TYPE FieldType;
    D3D12_VIDEO_PROCESS_DEINTERLACE_FLAGS DeinterlaceMode;
    WINBOOL EnableAlphaBlending;
    D3D12_VIDEO_PROCESS_LUMA_KEY LumaKey;
    UINT NumPastFrames;
    UINT NumFutureFrames;
    WINBOOL EnableAutoProcessing;
} D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC;
typedef enum D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE {
    D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE_OPAQUE = 0,
    D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE_BACKGROUND = 1,
    D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE_DESTINATION = 2,
    D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE_SOURCE_STREAM = 3
} D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE;
typedef struct D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC {
    DXGI_FORMAT Format;
    DXGI_COLOR_SPACE_TYPE ColorSpace;
    D3D12_VIDEO_PROCESS_ALPHA_FILL_MODE AlphaFillMode;
    UINT AlphaFillModeSourceStreamIndex;
    FLOAT BackgroundColor[4];
    DXGI_RATIONAL FrameRate;
    WINBOOL EnableStereo;
} D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC;
/*****************************************************************************
 * ID3D12VideoDecoderHeap interface
 */
#ifndef __ID3D12VideoDecoderHeap_INTERFACE_DEFINED__
#define __ID3D12VideoDecoderHeap_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoDecoderHeap, 0x0946b7c9, 0xebf6, 0x4047, 0xbb,0x73, 0x86,0x83,0xe2,0x7d,0xbb,0x1f);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("0946b7c9-ebf6-4047-bb73-8683e27dbb1f")
ID3D12VideoDecoderHeap : public ID3D12Pageable
{
#ifdef WIDL_EXPLICIT_AGGREGATE_RETURNS
    virtual D3D12_VIDEO_DECODER_HEAP_DESC* STDMETHODCALLTYPE GetDesc(
        D3D12_VIDEO_DECODER_HEAP_DESC *__ret) = 0;
    D3D12_VIDEO_DECODER_HEAP_DESC STDMETHODCALLTYPE GetDesc(
        )
    {
        D3D12_VIDEO_DECODER_HEAP_DESC __ret;
        return *GetDesc(&__ret);
    }
#else
    virtual D3D12_VIDEO_DECODER_HEAP_DESC STDMETHODCALLTYPE GetDesc(
        ) = 0;
#endif

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoDecoderHeap, 0x0946b7c9, 0xebf6, 0x4047, 0xbb,0x73, 0x86,0x83,0xe2,0x7d,0xbb,0x1f)
#endif
#else
typedef struct ID3D12VideoDecoderHeapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoDecoderHeap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoDecoderHeap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoDecoderHeap *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoDecoderHeap *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoDecoderHeap *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoDecoderHeap *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoDecoderHeap *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoDecoderHeap *This,
        REFIID riid,
        void **device);

    /*** ID3D12VideoDecoderHeap methods ***/
    D3D12_VIDEO_DECODER_HEAP_DESC * (STDMETHODCALLTYPE *GetDesc)(
        ID3D12VideoDecoderHeap *This,
        D3D12_VIDEO_DECODER_HEAP_DESC *__ret);

    END_INTERFACE
} ID3D12VideoDecoderHeapVtbl;

interface ID3D12VideoDecoderHeap {
    CONST_VTBL ID3D12VideoDecoderHeapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoDecoderHeap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoDecoderHeap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoDecoderHeap_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoDecoderHeap_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoDecoderHeap_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoDecoderHeap_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoDecoderHeap_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoDecoderHeap_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12VideoDecoderHeap methods ***/
#define ID3D12VideoDecoderHeap_GetDesc(This) ID3D12VideoDecoderHeap_GetDesc_define_WIDL_C_INLINE_WRAPPERS_for_aggregate_return_support
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoDecoderHeap_QueryInterface(ID3D12VideoDecoderHeap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoDecoderHeap_AddRef(ID3D12VideoDecoderHeap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoDecoderHeap_Release(ID3D12VideoDecoderHeap* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoDecoderHeap_GetPrivateData(ID3D12VideoDecoderHeap* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoDecoderHeap_SetPrivateData(ID3D12VideoDecoderHeap* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoDecoderHeap_SetPrivateDataInterface(ID3D12VideoDecoderHeap* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoDecoderHeap_SetName(ID3D12VideoDecoderHeap* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoDecoderHeap_GetDevice(ID3D12VideoDecoderHeap* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12VideoDecoderHeap methods ***/
static inline D3D12_VIDEO_DECODER_HEAP_DESC ID3D12VideoDecoderHeap_GetDesc(ID3D12VideoDecoderHeap* This) {
    D3D12_VIDEO_DECODER_HEAP_DESC __ret;
    return *This->lpVtbl->GetDesc(This,&__ret);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoDecoderHeap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12VideoDevice interface
 */
#ifndef __ID3D12VideoDevice_INTERFACE_DEFINED__
#define __ID3D12VideoDevice_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoDevice, 0x1f052807, 0x0b46, 0x4acc, 0x8a,0x89, 0x36,0x4f,0x79,0x37,0x18,0xa4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("1f052807-0b46-4acc-8a89-364f793718a4")
ID3D12VideoDevice : public IUnknown
{
    virtual HRESULT STDMETHODCALLTYPE CheckFeatureSupport(
        D3D12_FEATURE_VIDEO feature_video,
        void *feature_support_data,
        UINT feature_support_data_size) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoDecoder(
        const D3D12_VIDEO_DECODER_DESC *desc,
        REFIID riid,
        void **video_decoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoDecoderHeap(
        const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,
        REFIID riid,
        void **video_decoder_heap) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoProcessor(
        UINT node_mask,
        const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,
        UINT input_stream_descs_count,
        const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,
        REFIID riid,
        void **video_processor) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoDevice, 0x1f052807, 0x0b46, 0x4acc, 0x8a,0x89, 0x36,0x4f,0x79,0x37,0x18,0xa4)
#endif
#else
typedef struct ID3D12VideoDeviceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoDevice *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoDevice *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoDevice *This);

    /*** ID3D12VideoDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        ID3D12VideoDevice *This,
        D3D12_FEATURE_VIDEO feature_video,
        void *feature_support_data,
        UINT feature_support_data_size);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoder)(
        ID3D12VideoDevice *This,
        const D3D12_VIDEO_DECODER_DESC *desc,
        REFIID riid,
        void **video_decoder);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoderHeap)(
        ID3D12VideoDevice *This,
        const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,
        REFIID riid,
        void **video_decoder_heap);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessor)(
        ID3D12VideoDevice *This,
        UINT node_mask,
        const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,
        UINT input_stream_descs_count,
        const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,
        REFIID riid,
        void **video_processor);

    END_INTERFACE
} ID3D12VideoDeviceVtbl;

interface ID3D12VideoDevice {
    CONST_VTBL ID3D12VideoDeviceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoDevice_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoDevice_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoDevice_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12VideoDevice methods ***/
#define ID3D12VideoDevice_CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size) (This)->lpVtbl->CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size)
#define ID3D12VideoDevice_CreateVideoDecoder(This,desc,riid,video_decoder) (This)->lpVtbl->CreateVideoDecoder(This,desc,riid,video_decoder)
#define ID3D12VideoDevice_CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap) (This)->lpVtbl->CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap)
#define ID3D12VideoDevice_CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor) (This)->lpVtbl->CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoDevice_QueryInterface(ID3D12VideoDevice* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoDevice_AddRef(ID3D12VideoDevice* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoDevice_Release(ID3D12VideoDevice* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12VideoDevice methods ***/
static inline HRESULT ID3D12VideoDevice_CheckFeatureSupport(ID3D12VideoDevice* This,D3D12_FEATURE_VIDEO feature_video,void *feature_support_data,UINT feature_support_data_size) {
    return This->lpVtbl->CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size);
}
static inline HRESULT ID3D12VideoDevice_CreateVideoDecoder(ID3D12VideoDevice* This,const D3D12_VIDEO_DECODER_DESC *desc,REFIID riid,void **video_decoder) {
    return This->lpVtbl->CreateVideoDecoder(This,desc,riid,video_decoder);
}
static inline HRESULT ID3D12VideoDevice_CreateVideoDecoderHeap(ID3D12VideoDevice* This,const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,REFIID riid,void **video_decoder_heap) {
    return This->lpVtbl->CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap);
}
static inline HRESULT ID3D12VideoDevice_CreateVideoProcessor(ID3D12VideoDevice* This,UINT node_mask,const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,UINT input_stream_descs_count,const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,REFIID riid,void **video_processor) {
    return This->lpVtbl->CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoDevice_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12VideoDecoder interface
 */
#ifndef __ID3D12VideoDecoder_INTERFACE_DEFINED__
#define __ID3D12VideoDecoder_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoDecoder, 0xc59b6bdc, 0x7720, 0x4074, 0xa1,0x36, 0x17,0xa1,0x56,0x03,0x74,0x70);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("c59b6bdc-7720-4074-a136-17a156037470")
ID3D12VideoDecoder : public ID3D12Pageable
{
#ifdef WIDL_EXPLICIT_AGGREGATE_RETURNS
    virtual D3D12_VIDEO_DECODER_DESC* STDMETHODCALLTYPE GetDesc(
        D3D12_VIDEO_DECODER_DESC *__ret) = 0;
    D3D12_VIDEO_DECODER_DESC STDMETHODCALLTYPE GetDesc(
        )
    {
        D3D12_VIDEO_DECODER_DESC __ret;
        return *GetDesc(&__ret);
    }
#else
    virtual D3D12_VIDEO_DECODER_DESC STDMETHODCALLTYPE GetDesc(
        ) = 0;
#endif

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoDecoder, 0xc59b6bdc, 0x7720, 0x4074, 0xa1,0x36, 0x17,0xa1,0x56,0x03,0x74,0x70)
#endif
#else
typedef struct ID3D12VideoDecoderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoDecoder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoDecoder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoDecoder *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoDecoder *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoDecoder *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoDecoder *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoDecoder *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoDecoder *This,
        REFIID riid,
        void **device);

    /*** ID3D12VideoDecoder methods ***/
    D3D12_VIDEO_DECODER_DESC * (STDMETHODCALLTYPE *GetDesc)(
        ID3D12VideoDecoder *This,
        D3D12_VIDEO_DECODER_DESC *__ret);

    END_INTERFACE
} ID3D12VideoDecoderVtbl;

interface ID3D12VideoDecoder {
    CONST_VTBL ID3D12VideoDecoderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoDecoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoDecoder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoDecoder_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoDecoder_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoDecoder_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoDecoder_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoDecoder_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoDecoder_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12VideoDecoder methods ***/
#define ID3D12VideoDecoder_GetDesc(This) ID3D12VideoDecoder_GetDesc_define_WIDL_C_INLINE_WRAPPERS_for_aggregate_return_support
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoDecoder_QueryInterface(ID3D12VideoDecoder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoDecoder_AddRef(ID3D12VideoDecoder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoDecoder_Release(ID3D12VideoDecoder* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoDecoder_GetPrivateData(ID3D12VideoDecoder* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoDecoder_SetPrivateData(ID3D12VideoDecoder* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoDecoder_SetPrivateDataInterface(ID3D12VideoDecoder* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoDecoder_SetName(ID3D12VideoDecoder* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoDecoder_GetDevice(ID3D12VideoDecoder* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12VideoDecoder methods ***/
static inline D3D12_VIDEO_DECODER_DESC ID3D12VideoDecoder_GetDesc(ID3D12VideoDecoder* This) {
    D3D12_VIDEO_DECODER_DESC __ret;
    return *This->lpVtbl->GetDesc(This,&__ret);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoDecoder_INTERFACE_DEFINED__ */

typedef enum D3D12_VIDEO_DECODE_TIER {
    D3D12_VIDEO_DECODE_TIER_NOT_SUPPORTED = 0,
    D3D12_VIDEO_DECODE_TIER_1 = 1,
    D3D12_VIDEO_DECODE_TIER_2 = 2,
    D3D12_VIDEO_DECODE_TIER_3 = 3
} D3D12_VIDEO_DECODE_TIER;
typedef enum D3D12_VIDEO_DECODE_SUPPORT_FLAGS {
    D3D12_VIDEO_DECODE_SUPPORT_FLAG_NONE = 0x0,
    D3D12_VIDEO_DECODE_SUPPORT_FLAG_SUPPORTED = 0x1
} D3D12_VIDEO_DECODE_SUPPORT_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_DECODE_SUPPORT_FLAGS);
typedef enum D3D12_VIDEO_DECODE_CONFIGURATION_FLAGS {
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAG_NONE = 0x0,
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAG_HEIGHT_ALIGNMENT_MULTIPLE_32_REQUIRED = 0x1,
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAG_POST_PROCESSING_SUPPORTED = 0x2,
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAG_REFERENCE_ONLY_ALLOCATIONS_REQUIRED = 0x4,
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAG_ALLOW_RESOLUTION_CHANGE_ON_NON_KEY_FRAME = 0x8
} D3D12_VIDEO_DECODE_CONFIGURATION_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_DECODE_CONFIGURATION_FLAGS);
typedef enum D3D12_VIDEO_DECODE_ARGUMENT_TYPE {
    D3D12_VIDEO_DECODE_ARGUMENT_TYPE_PICTURE_PARAMETERS = 0,
    D3D12_VIDEO_DECODE_ARGUMENT_TYPE_INVERSE_QUANTIZATION_MATRIX = 1,
    D3D12_VIDEO_DECODE_ARGUMENT_TYPE_SLICE_CONTROL = 2,
    D3D12_VIDEO_DECODE_ARGUMENT_TYPE_MAX_VALID = 3
} D3D12_VIDEO_DECODE_ARGUMENT_TYPE;
typedef struct D3D12_FEATURE_DATA_VIDEO_DECODE_SUPPORT {
    UINT NodeIndex;
    D3D12_VIDEO_DECODE_CONFIGURATION Configuration;
    UINT Width;
    UINT Height;
    DXGI_FORMAT DecodeFormat;
    DXGI_RATIONAL FrameRate;
    UINT BitRate;
    D3D12_VIDEO_DECODE_SUPPORT_FLAGS SupportFlags;
    D3D12_VIDEO_DECODE_CONFIGURATION_FLAGS ConfigurationFlags;
    D3D12_VIDEO_DECODE_TIER DecodeTier;
} D3D12_FEATURE_DATA_VIDEO_DECODE_SUPPORT;
typedef struct D3D12_VIDEO_DECODE_FRAME_ARGUMENT {
    D3D12_VIDEO_DECODE_ARGUMENT_TYPE Type;
    UINT Size;
    void *pData;
} D3D12_VIDEO_DECODE_FRAME_ARGUMENT;
typedef struct D3D12_VIDEO_DECODE_REFERENCE_FRAMES {
    UINT NumTexture2Ds;
    ID3D12Resource **ppTexture2Ds;
    UINT *pSubresources;
    ID3D12VideoDecoderHeap **ppHeaps;
} D3D12_VIDEO_DECODE_REFERENCE_FRAMES;
typedef struct D3D12_VIDEO_DECODE_COMPRESSED_BITSTREAM {
    ID3D12Resource *pBuffer;
    UINT64 Offset;
    UINT64 Size;
} D3D12_VIDEO_DECODE_COMPRESSED_BITSTREAM;
typedef struct D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS {
    WINBOOL Enable;
    ID3D12Resource *pReferenceTexture2D;
    UINT ReferenceSubresource;
    DXGI_COLOR_SPACE_TYPE OutputColorSpace;
    DXGI_COLOR_SPACE_TYPE DecodeColorSpace;
} D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS;
typedef struct D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS {
    UINT NumFrameArguments;
    D3D12_VIDEO_DECODE_FRAME_ARGUMENT FrameArguments[10];
    D3D12_VIDEO_DECODE_REFERENCE_FRAMES ReferenceFrames;
    D3D12_VIDEO_DECODE_COMPRESSED_BITSTREAM CompressedBitstream;
    ID3D12VideoDecoderHeap *pHeap;
} D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS;
typedef struct D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS {
    ID3D12Resource *pOutputTexture2D;
    UINT OutputSubresource;
    D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS ConversionArguments;
} D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS;
/*****************************************************************************
 * ID3D12VideoDecodeCommandList interface
 */
#ifndef __ID3D12VideoDecodeCommandList_INTERFACE_DEFINED__
#define __ID3D12VideoDecodeCommandList_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoDecodeCommandList, 0x3b60536e, 0xad29, 0x4e64, 0xa2,0x69, 0xf8,0x53,0x83,0x7e,0x5e,0x53);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("3b60536e-ad29-4e64-a269-f853837e5e53")
ID3D12VideoDecodeCommandList : public ID3D12CommandList
{
    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ID3D12CommandAllocator *allocator) = 0;

    virtual void STDMETHODCALLTYPE ClearState(
        ) = 0;

    virtual void STDMETHODCALLTYPE ResourceBarrier(
        UINT barriers_count,
        const D3D12_RESOURCE_BARRIER *barriers) = 0;

    virtual void STDMETHODCALLTYPE DiscardResource(
        ID3D12Resource *resource,
        const D3D12_DISCARD_REGION *region) = 0;

    virtual void STDMETHODCALLTYPE BeginQuery(
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index) = 0;

    virtual void STDMETHODCALLTYPE EndQuery(
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index) = 0;

    virtual void STDMETHODCALLTYPE ResolveQueryData(
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT start_index,
        UINT queries_count,
        ID3D12Resource *destination_buffer,
        UINT64 aligned_destination_buffer_offset) = 0;

    virtual void STDMETHODCALLTYPE SetPredication(
        ID3D12Resource *buffer,
        UINT64 aligned_buffer_offset,
        D3D12_PREDICATION_OP operation) = 0;

    virtual void STDMETHODCALLTYPE SetMarker(
        UINT metadata,
        const void *data,
        UINT size) = 0;

    virtual void STDMETHODCALLTYPE BeginEvent(
        UINT metadata,
        const void *data,
        UINT size) = 0;

    virtual void STDMETHODCALLTYPE EndEvent(
        ) = 0;

    virtual void STDMETHODCALLTYPE DecodeFrame(
        ID3D12VideoDecoder *decoder,
        const D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS *output_arguments,
        const D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS *input_arguments) = 0;

    virtual void STDMETHODCALLTYPE WriteBufferImmediate(
        UINT count,
        const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,
        const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoDecodeCommandList, 0x3b60536e, 0xad29, 0x4e64, 0xa2,0x69, 0xf8,0x53,0x83,0x7e,0x5e,0x53)
#endif
#else
typedef struct ID3D12VideoDecodeCommandListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoDecodeCommandList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoDecodeCommandList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoDecodeCommandList *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoDecodeCommandList *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoDecodeCommandList *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoDecodeCommandList *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoDecodeCommandList *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoDecodeCommandList *This,
        REFIID riid,
        void **device);

    /*** ID3D12CommandList methods ***/
    D3D12_COMMAND_LIST_TYPE (STDMETHODCALLTYPE *GetType)(
        ID3D12VideoDecodeCommandList *This);

    /*** ID3D12VideoDecodeCommandList methods ***/
    HRESULT (STDMETHODCALLTYPE *Close)(
        ID3D12VideoDecodeCommandList *This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        ID3D12VideoDecodeCommandList *This,
        ID3D12CommandAllocator *allocator);

    void (STDMETHODCALLTYPE *ClearState)(
        ID3D12VideoDecodeCommandList *This);

    void (STDMETHODCALLTYPE *ResourceBarrier)(
        ID3D12VideoDecodeCommandList *This,
        UINT barriers_count,
        const D3D12_RESOURCE_BARRIER *barriers);

    void (STDMETHODCALLTYPE *DiscardResource)(
        ID3D12VideoDecodeCommandList *This,
        ID3D12Resource *resource,
        const D3D12_DISCARD_REGION *region);

    void (STDMETHODCALLTYPE *BeginQuery)(
        ID3D12VideoDecodeCommandList *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index);

    void (STDMETHODCALLTYPE *EndQuery)(
        ID3D12VideoDecodeCommandList *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index);

    void (STDMETHODCALLTYPE *ResolveQueryData)(
        ID3D12VideoDecodeCommandList *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT start_index,
        UINT queries_count,
        ID3D12Resource *destination_buffer,
        UINT64 aligned_destination_buffer_offset);

    void (STDMETHODCALLTYPE *SetPredication)(
        ID3D12VideoDecodeCommandList *This,
        ID3D12Resource *buffer,
        UINT64 aligned_buffer_offset,
        D3D12_PREDICATION_OP operation);

    void (STDMETHODCALLTYPE *SetMarker)(
        ID3D12VideoDecodeCommandList *This,
        UINT metadata,
        const void *data,
        UINT size);

    void (STDMETHODCALLTYPE *BeginEvent)(
        ID3D12VideoDecodeCommandList *This,
        UINT metadata,
        const void *data,
        UINT size);

    void (STDMETHODCALLTYPE *EndEvent)(
        ID3D12VideoDecodeCommandList *This);

    void (STDMETHODCALLTYPE *DecodeFrame)(
        ID3D12VideoDecodeCommandList *This,
        ID3D12VideoDecoder *decoder,
        const D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS *output_arguments,
        const D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS *input_arguments);

    void (STDMETHODCALLTYPE *WriteBufferImmediate)(
        ID3D12VideoDecodeCommandList *This,
        UINT count,
        const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,
        const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes);

    END_INTERFACE
} ID3D12VideoDecodeCommandListVtbl;

interface ID3D12VideoDecodeCommandList {
    CONST_VTBL ID3D12VideoDecodeCommandListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoDecodeCommandList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoDecodeCommandList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoDecodeCommandList_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoDecodeCommandList_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoDecodeCommandList_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoDecodeCommandList_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoDecodeCommandList_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoDecodeCommandList_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12CommandList methods ***/
#define ID3D12VideoDecodeCommandList_GetType(This) (This)->lpVtbl->GetType(This)
/*** ID3D12VideoDecodeCommandList methods ***/
#define ID3D12VideoDecodeCommandList_Close(This) (This)->lpVtbl->Close(This)
#define ID3D12VideoDecodeCommandList_Reset(This,allocator) (This)->lpVtbl->Reset(This,allocator)
#define ID3D12VideoDecodeCommandList_ClearState(This) (This)->lpVtbl->ClearState(This)
#define ID3D12VideoDecodeCommandList_ResourceBarrier(This,barriers_count,barriers) (This)->lpVtbl->ResourceBarrier(This,barriers_count,barriers)
#define ID3D12VideoDecodeCommandList_DiscardResource(This,resource,region) (This)->lpVtbl->DiscardResource(This,resource,region)
#define ID3D12VideoDecodeCommandList_BeginQuery(This,query_heap,type,index) (This)->lpVtbl->BeginQuery(This,query_heap,type,index)
#define ID3D12VideoDecodeCommandList_EndQuery(This,query_heap,type,index) (This)->lpVtbl->EndQuery(This,query_heap,type,index)
#define ID3D12VideoDecodeCommandList_ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset) (This)->lpVtbl->ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset)
#define ID3D12VideoDecodeCommandList_SetPredication(This,buffer,aligned_buffer_offset,operation) (This)->lpVtbl->SetPredication(This,buffer,aligned_buffer_offset,operation)
#define ID3D12VideoDecodeCommandList_SetMarker(This,metadata,data,size) (This)->lpVtbl->SetMarker(This,metadata,data,size)
#define ID3D12VideoDecodeCommandList_BeginEvent(This,metadata,data,size) (This)->lpVtbl->BeginEvent(This,metadata,data,size)
#define ID3D12VideoDecodeCommandList_EndEvent(This) (This)->lpVtbl->EndEvent(This)
#define ID3D12VideoDecodeCommandList_DecodeFrame(This,decoder,output_arguments,input_arguments) (This)->lpVtbl->DecodeFrame(This,decoder,output_arguments,input_arguments)
#define ID3D12VideoDecodeCommandList_WriteBufferImmediate(This,count,params,modes) (This)->lpVtbl->WriteBufferImmediate(This,count,params,modes)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoDecodeCommandList_QueryInterface(ID3D12VideoDecodeCommandList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoDecodeCommandList_AddRef(ID3D12VideoDecodeCommandList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoDecodeCommandList_Release(ID3D12VideoDecodeCommandList* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoDecodeCommandList_GetPrivateData(ID3D12VideoDecodeCommandList* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoDecodeCommandList_SetPrivateData(ID3D12VideoDecodeCommandList* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoDecodeCommandList_SetPrivateDataInterface(ID3D12VideoDecodeCommandList* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoDecodeCommandList_SetName(ID3D12VideoDecodeCommandList* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoDecodeCommandList_GetDevice(ID3D12VideoDecodeCommandList* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12CommandList methods ***/
static inline D3D12_COMMAND_LIST_TYPE ID3D12VideoDecodeCommandList_GetType(ID3D12VideoDecodeCommandList* This) {
    return This->lpVtbl->GetType(This);
}
/*** ID3D12VideoDecodeCommandList methods ***/
static inline HRESULT ID3D12VideoDecodeCommandList_Close(ID3D12VideoDecodeCommandList* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT ID3D12VideoDecodeCommandList_Reset(ID3D12VideoDecodeCommandList* This,ID3D12CommandAllocator *allocator) {
    return This->lpVtbl->Reset(This,allocator);
}
static inline void ID3D12VideoDecodeCommandList_ClearState(ID3D12VideoDecodeCommandList* This) {
    This->lpVtbl->ClearState(This);
}
static inline void ID3D12VideoDecodeCommandList_ResourceBarrier(ID3D12VideoDecodeCommandList* This,UINT barriers_count,const D3D12_RESOURCE_BARRIER *barriers) {
    This->lpVtbl->ResourceBarrier(This,barriers_count,barriers);
}
static inline void ID3D12VideoDecodeCommandList_DiscardResource(ID3D12VideoDecodeCommandList* This,ID3D12Resource *resource,const D3D12_DISCARD_REGION *region) {
    This->lpVtbl->DiscardResource(This,resource,region);
}
static inline void ID3D12VideoDecodeCommandList_BeginQuery(ID3D12VideoDecodeCommandList* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT index) {
    This->lpVtbl->BeginQuery(This,query_heap,type,index);
}
static inline void ID3D12VideoDecodeCommandList_EndQuery(ID3D12VideoDecodeCommandList* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT index) {
    This->lpVtbl->EndQuery(This,query_heap,type,index);
}
static inline void ID3D12VideoDecodeCommandList_ResolveQueryData(ID3D12VideoDecodeCommandList* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT start_index,UINT queries_count,ID3D12Resource *destination_buffer,UINT64 aligned_destination_buffer_offset) {
    This->lpVtbl->ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset);
}
static inline void ID3D12VideoDecodeCommandList_SetPredication(ID3D12VideoDecodeCommandList* This,ID3D12Resource *buffer,UINT64 aligned_buffer_offset,D3D12_PREDICATION_OP operation) {
    This->lpVtbl->SetPredication(This,buffer,aligned_buffer_offset,operation);
}
static inline void ID3D12VideoDecodeCommandList_SetMarker(ID3D12VideoDecodeCommandList* This,UINT metadata,const void *data,UINT size) {
    This->lpVtbl->SetMarker(This,metadata,data,size);
}
static inline void ID3D12VideoDecodeCommandList_BeginEvent(ID3D12VideoDecodeCommandList* This,UINT metadata,const void *data,UINT size) {
    This->lpVtbl->BeginEvent(This,metadata,data,size);
}
static inline void ID3D12VideoDecodeCommandList_EndEvent(ID3D12VideoDecodeCommandList* This) {
    This->lpVtbl->EndEvent(This);
}
static inline void ID3D12VideoDecodeCommandList_DecodeFrame(ID3D12VideoDecodeCommandList* This,ID3D12VideoDecoder *decoder,const D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS *output_arguments,const D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS *input_arguments) {
    This->lpVtbl->DecodeFrame(This,decoder,output_arguments,input_arguments);
}
static inline void ID3D12VideoDecodeCommandList_WriteBufferImmediate(ID3D12VideoDecodeCommandList* This,UINT count,const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes) {
    This->lpVtbl->WriteBufferImmediate(This,count,params,modes);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoDecodeCommandList_INTERFACE_DEFINED__ */

typedef struct D3D12_VIDEO_DECODE_OUTPUT_HISTOGRAM {
    UINT64 Offset;
    ID3D12Resource *pBuffer;
} D3D12_VIDEO_DECODE_OUTPUT_HISTOGRAM;
typedef struct D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS1 {
    WINBOOL Enable;
    ID3D12Resource *pReferenceTexture2D;
    UINT ReferenceSubresource;
    DXGI_COLOR_SPACE_TYPE OutputColorSpace;
    DXGI_COLOR_SPACE_TYPE DecodeColorSpace;
    UINT OutputWidth;
    UINT OutputHeight;
} D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS1;
typedef struct D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS1 {
    ID3D12Resource *pOutputTexture2D;
    UINT OutputSubresource;
    D3D12_VIDEO_DECODE_CONVERSION_ARGUMENTS1 ConversionArguments;
    D3D12_VIDEO_DECODE_OUTPUT_HISTOGRAM Histograms[4];
} D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS1;
/*****************************************************************************
 * ID3D12VideoDecodeCommandList1 interface
 */
#ifndef __ID3D12VideoDecodeCommandList1_INTERFACE_DEFINED__
#define __ID3D12VideoDecodeCommandList1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoDecodeCommandList1, 0xd52f011b, 0xb56e, 0x453c, 0xa0,0x5a, 0xa7,0xf3,0x11,0xc8,0xf4,0x72);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("d52f011b-b56e-453c-a05a-a7f311c8f472")
ID3D12VideoDecodeCommandList1 : public ID3D12VideoDecodeCommandList
{
    virtual void STDMETHODCALLTYPE DecodeFrame1(
        ID3D12VideoDecoder *decoder,
        const D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS1 *output_arguments,
        const D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS *input_arguments) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoDecodeCommandList1, 0xd52f011b, 0xb56e, 0x453c, 0xa0,0x5a, 0xa7,0xf3,0x11,0xc8,0xf4,0x72)
#endif
#else
typedef struct ID3D12VideoDecodeCommandList1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoDecodeCommandList1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoDecodeCommandList1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoDecodeCommandList1 *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoDecodeCommandList1 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoDecodeCommandList1 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoDecodeCommandList1 *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoDecodeCommandList1 *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoDecodeCommandList1 *This,
        REFIID riid,
        void **device);

    /*** ID3D12CommandList methods ***/
    D3D12_COMMAND_LIST_TYPE (STDMETHODCALLTYPE *GetType)(
        ID3D12VideoDecodeCommandList1 *This);

    /*** ID3D12VideoDecodeCommandList methods ***/
    HRESULT (STDMETHODCALLTYPE *Close)(
        ID3D12VideoDecodeCommandList1 *This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        ID3D12VideoDecodeCommandList1 *This,
        ID3D12CommandAllocator *allocator);

    void (STDMETHODCALLTYPE *ClearState)(
        ID3D12VideoDecodeCommandList1 *This);

    void (STDMETHODCALLTYPE *ResourceBarrier)(
        ID3D12VideoDecodeCommandList1 *This,
        UINT barriers_count,
        const D3D12_RESOURCE_BARRIER *barriers);

    void (STDMETHODCALLTYPE *DiscardResource)(
        ID3D12VideoDecodeCommandList1 *This,
        ID3D12Resource *resource,
        const D3D12_DISCARD_REGION *region);

    void (STDMETHODCALLTYPE *BeginQuery)(
        ID3D12VideoDecodeCommandList1 *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index);

    void (STDMETHODCALLTYPE *EndQuery)(
        ID3D12VideoDecodeCommandList1 *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index);

    void (STDMETHODCALLTYPE *ResolveQueryData)(
        ID3D12VideoDecodeCommandList1 *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT start_index,
        UINT queries_count,
        ID3D12Resource *destination_buffer,
        UINT64 aligned_destination_buffer_offset);

    void (STDMETHODCALLTYPE *SetPredication)(
        ID3D12VideoDecodeCommandList1 *This,
        ID3D12Resource *buffer,
        UINT64 aligned_buffer_offset,
        D3D12_PREDICATION_OP operation);

    void (STDMETHODCALLTYPE *SetMarker)(
        ID3D12VideoDecodeCommandList1 *This,
        UINT metadata,
        const void *data,
        UINT size);

    void (STDMETHODCALLTYPE *BeginEvent)(
        ID3D12VideoDecodeCommandList1 *This,
        UINT metadata,
        const void *data,
        UINT size);

    void (STDMETHODCALLTYPE *EndEvent)(
        ID3D12VideoDecodeCommandList1 *This);

    void (STDMETHODCALLTYPE *DecodeFrame)(
        ID3D12VideoDecodeCommandList1 *This,
        ID3D12VideoDecoder *decoder,
        const D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS *output_arguments,
        const D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS *input_arguments);

    void (STDMETHODCALLTYPE *WriteBufferImmediate)(
        ID3D12VideoDecodeCommandList1 *This,
        UINT count,
        const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,
        const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes);

    /*** ID3D12VideoDecodeCommandList1 methods ***/
    void (STDMETHODCALLTYPE *DecodeFrame1)(
        ID3D12VideoDecodeCommandList1 *This,
        ID3D12VideoDecoder *decoder,
        const D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS1 *output_arguments,
        const D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS *input_arguments);

    END_INTERFACE
} ID3D12VideoDecodeCommandList1Vtbl;

interface ID3D12VideoDecodeCommandList1 {
    CONST_VTBL ID3D12VideoDecodeCommandList1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoDecodeCommandList1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoDecodeCommandList1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoDecodeCommandList1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoDecodeCommandList1_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoDecodeCommandList1_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoDecodeCommandList1_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoDecodeCommandList1_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoDecodeCommandList1_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12CommandList methods ***/
#define ID3D12VideoDecodeCommandList1_GetType(This) (This)->lpVtbl->GetType(This)
/*** ID3D12VideoDecodeCommandList methods ***/
#define ID3D12VideoDecodeCommandList1_Close(This) (This)->lpVtbl->Close(This)
#define ID3D12VideoDecodeCommandList1_Reset(This,allocator) (This)->lpVtbl->Reset(This,allocator)
#define ID3D12VideoDecodeCommandList1_ClearState(This) (This)->lpVtbl->ClearState(This)
#define ID3D12VideoDecodeCommandList1_ResourceBarrier(This,barriers_count,barriers) (This)->lpVtbl->ResourceBarrier(This,barriers_count,barriers)
#define ID3D12VideoDecodeCommandList1_DiscardResource(This,resource,region) (This)->lpVtbl->DiscardResource(This,resource,region)
#define ID3D12VideoDecodeCommandList1_BeginQuery(This,query_heap,type,index) (This)->lpVtbl->BeginQuery(This,query_heap,type,index)
#define ID3D12VideoDecodeCommandList1_EndQuery(This,query_heap,type,index) (This)->lpVtbl->EndQuery(This,query_heap,type,index)
#define ID3D12VideoDecodeCommandList1_ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset) (This)->lpVtbl->ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset)
#define ID3D12VideoDecodeCommandList1_SetPredication(This,buffer,aligned_buffer_offset,operation) (This)->lpVtbl->SetPredication(This,buffer,aligned_buffer_offset,operation)
#define ID3D12VideoDecodeCommandList1_SetMarker(This,metadata,data,size) (This)->lpVtbl->SetMarker(This,metadata,data,size)
#define ID3D12VideoDecodeCommandList1_BeginEvent(This,metadata,data,size) (This)->lpVtbl->BeginEvent(This,metadata,data,size)
#define ID3D12VideoDecodeCommandList1_EndEvent(This) (This)->lpVtbl->EndEvent(This)
#define ID3D12VideoDecodeCommandList1_DecodeFrame(This,decoder,output_arguments,input_arguments) (This)->lpVtbl->DecodeFrame(This,decoder,output_arguments,input_arguments)
#define ID3D12VideoDecodeCommandList1_WriteBufferImmediate(This,count,params,modes) (This)->lpVtbl->WriteBufferImmediate(This,count,params,modes)
/*** ID3D12VideoDecodeCommandList1 methods ***/
#define ID3D12VideoDecodeCommandList1_DecodeFrame1(This,decoder,output_arguments,input_arguments) (This)->lpVtbl->DecodeFrame1(This,decoder,output_arguments,input_arguments)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoDecodeCommandList1_QueryInterface(ID3D12VideoDecodeCommandList1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoDecodeCommandList1_AddRef(ID3D12VideoDecodeCommandList1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoDecodeCommandList1_Release(ID3D12VideoDecodeCommandList1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoDecodeCommandList1_GetPrivateData(ID3D12VideoDecodeCommandList1* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoDecodeCommandList1_SetPrivateData(ID3D12VideoDecodeCommandList1* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoDecodeCommandList1_SetPrivateDataInterface(ID3D12VideoDecodeCommandList1* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoDecodeCommandList1_SetName(ID3D12VideoDecodeCommandList1* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoDecodeCommandList1_GetDevice(ID3D12VideoDecodeCommandList1* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12CommandList methods ***/
static inline D3D12_COMMAND_LIST_TYPE ID3D12VideoDecodeCommandList1_GetType(ID3D12VideoDecodeCommandList1* This) {
    return This->lpVtbl->GetType(This);
}
/*** ID3D12VideoDecodeCommandList methods ***/
static inline HRESULT ID3D12VideoDecodeCommandList1_Close(ID3D12VideoDecodeCommandList1* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT ID3D12VideoDecodeCommandList1_Reset(ID3D12VideoDecodeCommandList1* This,ID3D12CommandAllocator *allocator) {
    return This->lpVtbl->Reset(This,allocator);
}
static inline void ID3D12VideoDecodeCommandList1_ClearState(ID3D12VideoDecodeCommandList1* This) {
    This->lpVtbl->ClearState(This);
}
static inline void ID3D12VideoDecodeCommandList1_ResourceBarrier(ID3D12VideoDecodeCommandList1* This,UINT barriers_count,const D3D12_RESOURCE_BARRIER *barriers) {
    This->lpVtbl->ResourceBarrier(This,barriers_count,barriers);
}
static inline void ID3D12VideoDecodeCommandList1_DiscardResource(ID3D12VideoDecodeCommandList1* This,ID3D12Resource *resource,const D3D12_DISCARD_REGION *region) {
    This->lpVtbl->DiscardResource(This,resource,region);
}
static inline void ID3D12VideoDecodeCommandList1_BeginQuery(ID3D12VideoDecodeCommandList1* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT index) {
    This->lpVtbl->BeginQuery(This,query_heap,type,index);
}
static inline void ID3D12VideoDecodeCommandList1_EndQuery(ID3D12VideoDecodeCommandList1* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT index) {
    This->lpVtbl->EndQuery(This,query_heap,type,index);
}
static inline void ID3D12VideoDecodeCommandList1_ResolveQueryData(ID3D12VideoDecodeCommandList1* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT start_index,UINT queries_count,ID3D12Resource *destination_buffer,UINT64 aligned_destination_buffer_offset) {
    This->lpVtbl->ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset);
}
static inline void ID3D12VideoDecodeCommandList1_SetPredication(ID3D12VideoDecodeCommandList1* This,ID3D12Resource *buffer,UINT64 aligned_buffer_offset,D3D12_PREDICATION_OP operation) {
    This->lpVtbl->SetPredication(This,buffer,aligned_buffer_offset,operation);
}
static inline void ID3D12VideoDecodeCommandList1_SetMarker(ID3D12VideoDecodeCommandList1* This,UINT metadata,const void *data,UINT size) {
    This->lpVtbl->SetMarker(This,metadata,data,size);
}
static inline void ID3D12VideoDecodeCommandList1_BeginEvent(ID3D12VideoDecodeCommandList1* This,UINT metadata,const void *data,UINT size) {
    This->lpVtbl->BeginEvent(This,metadata,data,size);
}
static inline void ID3D12VideoDecodeCommandList1_EndEvent(ID3D12VideoDecodeCommandList1* This) {
    This->lpVtbl->EndEvent(This);
}
static inline void ID3D12VideoDecodeCommandList1_DecodeFrame(ID3D12VideoDecodeCommandList1* This,ID3D12VideoDecoder *decoder,const D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS *output_arguments,const D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS *input_arguments) {
    This->lpVtbl->DecodeFrame(This,decoder,output_arguments,input_arguments);
}
static inline void ID3D12VideoDecodeCommandList1_WriteBufferImmediate(ID3D12VideoDecodeCommandList1* This,UINT count,const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes) {
    This->lpVtbl->WriteBufferImmediate(This,count,params,modes);
}
/*** ID3D12VideoDecodeCommandList1 methods ***/
static inline void ID3D12VideoDecodeCommandList1_DecodeFrame1(ID3D12VideoDecodeCommandList1* This,ID3D12VideoDecoder *decoder,const D3D12_VIDEO_DECODE_OUTPUT_STREAM_ARGUMENTS1 *output_arguments,const D3D12_VIDEO_DECODE_INPUT_STREAM_ARGUMENTS *input_arguments) {
    This->lpVtbl->DecodeFrame1(This,decoder,output_arguments,input_arguments);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoDecodeCommandList1_INTERFACE_DEFINED__ */

typedef enum D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE {
    D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE_8X8 = 0,
    D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE_16X16 = 1
} D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE;
typedef enum D3D12_VIDEO_MOTION_ESTIMATOR_VECTOR_PRECISION {
    D3D12_VIDEO_MOTION_ESTIMATOR_VECTOR_PRECISION_QUARTER_PEL = 0
} D3D12_VIDEO_MOTION_ESTIMATOR_VECTOR_PRECISION;
typedef struct D3D12_FEATURE_DATA_VIDEO_FEATURE_AREA_SUPPORT {
    UINT NodeIndex;
    WINBOOL VideoDecodeSupport;
    WINBOOL VideoProcessSupport;
    WINBOOL VideoEncodeSupport;
} D3D12_FEATURE_DATA_VIDEO_FEATURE_AREA_SUPPORT;
typedef struct D3D12_VIDEO_MOTION_ESTIMATOR_DESC {
    UINT NodeMask;
    DXGI_FORMAT InputFormat;
    D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE BlockSize;
    D3D12_VIDEO_MOTION_ESTIMATOR_VECTOR_PRECISION Precision;
    D3D12_VIDEO_SIZE_RANGE SizeRange;
} D3D12_VIDEO_MOTION_ESTIMATOR_DESC;
/*****************************************************************************
 * ID3D12VideoMotionEstimator interface
 */
#ifndef __ID3D12VideoMotionEstimator_INTERFACE_DEFINED__
#define __ID3D12VideoMotionEstimator_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoMotionEstimator, 0x33fdae0e, 0x098b, 0x428f, 0x87,0xbb, 0x34,0xb6,0x95,0xde,0x08,0xf8);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("33fdae0e-098b-428f-87bb-34b695de08f8")
ID3D12VideoMotionEstimator : public ID3D12Pageable
{
#ifdef WIDL_EXPLICIT_AGGREGATE_RETURNS
    virtual D3D12_VIDEO_MOTION_ESTIMATOR_DESC* STDMETHODCALLTYPE GetDesc(
        D3D12_VIDEO_MOTION_ESTIMATOR_DESC *__ret) = 0;
    D3D12_VIDEO_MOTION_ESTIMATOR_DESC STDMETHODCALLTYPE GetDesc(
        )
    {
        D3D12_VIDEO_MOTION_ESTIMATOR_DESC __ret;
        return *GetDesc(&__ret);
    }
#else
    virtual D3D12_VIDEO_MOTION_ESTIMATOR_DESC STDMETHODCALLTYPE GetDesc(
        ) = 0;
#endif

    virtual HRESULT STDMETHODCALLTYPE GetProtectedResourceSession(
        REFIID riid,
        void **protected_session) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoMotionEstimator, 0x33fdae0e, 0x098b, 0x428f, 0x87,0xbb, 0x34,0xb6,0x95,0xde,0x08,0xf8)
#endif
#else
typedef struct ID3D12VideoMotionEstimatorVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoMotionEstimator *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoMotionEstimator *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoMotionEstimator *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoMotionEstimator *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoMotionEstimator *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoMotionEstimator *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoMotionEstimator *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoMotionEstimator *This,
        REFIID riid,
        void **device);

    /*** ID3D12VideoMotionEstimator methods ***/
    D3D12_VIDEO_MOTION_ESTIMATOR_DESC * (STDMETHODCALLTYPE *GetDesc)(
        ID3D12VideoMotionEstimator *This,
        D3D12_VIDEO_MOTION_ESTIMATOR_DESC *__ret);

    HRESULT (STDMETHODCALLTYPE *GetProtectedResourceSession)(
        ID3D12VideoMotionEstimator *This,
        REFIID riid,
        void **protected_session);

    END_INTERFACE
} ID3D12VideoMotionEstimatorVtbl;

interface ID3D12VideoMotionEstimator {
    CONST_VTBL ID3D12VideoMotionEstimatorVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoMotionEstimator_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoMotionEstimator_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoMotionEstimator_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoMotionEstimator_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoMotionEstimator_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoMotionEstimator_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoMotionEstimator_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoMotionEstimator_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12VideoMotionEstimator methods ***/
#define ID3D12VideoMotionEstimator_GetDesc(This) ID3D12VideoMotionEstimator_GetDesc_define_WIDL_C_INLINE_WRAPPERS_for_aggregate_return_support
#define ID3D12VideoMotionEstimator_GetProtectedResourceSession(This,riid,protected_session) (This)->lpVtbl->GetProtectedResourceSession(This,riid,protected_session)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoMotionEstimator_QueryInterface(ID3D12VideoMotionEstimator* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoMotionEstimator_AddRef(ID3D12VideoMotionEstimator* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoMotionEstimator_Release(ID3D12VideoMotionEstimator* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoMotionEstimator_GetPrivateData(ID3D12VideoMotionEstimator* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoMotionEstimator_SetPrivateData(ID3D12VideoMotionEstimator* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoMotionEstimator_SetPrivateDataInterface(ID3D12VideoMotionEstimator* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoMotionEstimator_SetName(ID3D12VideoMotionEstimator* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoMotionEstimator_GetDevice(ID3D12VideoMotionEstimator* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12VideoMotionEstimator methods ***/
static inline D3D12_VIDEO_MOTION_ESTIMATOR_DESC ID3D12VideoMotionEstimator_GetDesc(ID3D12VideoMotionEstimator* This) {
    D3D12_VIDEO_MOTION_ESTIMATOR_DESC __ret;
    return *This->lpVtbl->GetDesc(This,&__ret);
}
static inline HRESULT ID3D12VideoMotionEstimator_GetProtectedResourceSession(ID3D12VideoMotionEstimator* This,REFIID riid,void **protected_session) {
    return This->lpVtbl->GetProtectedResourceSession(This,riid,protected_session);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoMotionEstimator_INTERFACE_DEFINED__ */

typedef struct D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC {
    UINT NodeMask;
    DXGI_FORMAT InputFormat;
    D3D12_VIDEO_MOTION_ESTIMATOR_SEARCH_BLOCK_SIZE BlockSize;
    D3D12_VIDEO_MOTION_ESTIMATOR_VECTOR_PRECISION Precision;
    D3D12_VIDEO_SIZE_RANGE SizeRange;
} D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC;
/*****************************************************************************
 * ID3D12VideoMotionVectorHeap interface
 */
#ifndef __ID3D12VideoMotionVectorHeap_INTERFACE_DEFINED__
#define __ID3D12VideoMotionVectorHeap_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoMotionVectorHeap, 0x5be17987, 0x743a, 0x4061, 0x83,0x4b, 0x23,0xd2,0x2d,0xae,0xa5,0x05);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("5be17987-743a-4061-834b-23d22daea505")
ID3D12VideoMotionVectorHeap : public ID3D12Pageable
{
#ifdef WIDL_EXPLICIT_AGGREGATE_RETURNS
    virtual D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC* STDMETHODCALLTYPE GetDesc(
        D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC *__ret) = 0;
    D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC STDMETHODCALLTYPE GetDesc(
        )
    {
        D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC __ret;
        return *GetDesc(&__ret);
    }
#else
    virtual D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC STDMETHODCALLTYPE GetDesc(
        ) = 0;
#endif

    virtual HRESULT STDMETHODCALLTYPE GetProtectedResourceSession(
        REFIID riid,
        void **protected_session) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoMotionVectorHeap, 0x5be17987, 0x743a, 0x4061, 0x83,0x4b, 0x23,0xd2,0x2d,0xae,0xa5,0x05)
#endif
#else
typedef struct ID3D12VideoMotionVectorHeapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoMotionVectorHeap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoMotionVectorHeap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoMotionVectorHeap *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoMotionVectorHeap *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoMotionVectorHeap *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoMotionVectorHeap *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoMotionVectorHeap *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoMotionVectorHeap *This,
        REFIID riid,
        void **device);

    /*** ID3D12VideoMotionVectorHeap methods ***/
    D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC * (STDMETHODCALLTYPE *GetDesc)(
        ID3D12VideoMotionVectorHeap *This,
        D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC *__ret);

    HRESULT (STDMETHODCALLTYPE *GetProtectedResourceSession)(
        ID3D12VideoMotionVectorHeap *This,
        REFIID riid,
        void **protected_session);

    END_INTERFACE
} ID3D12VideoMotionVectorHeapVtbl;

interface ID3D12VideoMotionVectorHeap {
    CONST_VTBL ID3D12VideoMotionVectorHeapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoMotionVectorHeap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoMotionVectorHeap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoMotionVectorHeap_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoMotionVectorHeap_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoMotionVectorHeap_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoMotionVectorHeap_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoMotionVectorHeap_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoMotionVectorHeap_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12VideoMotionVectorHeap methods ***/
#define ID3D12VideoMotionVectorHeap_GetDesc(This) ID3D12VideoMotionVectorHeap_GetDesc_define_WIDL_C_INLINE_WRAPPERS_for_aggregate_return_support
#define ID3D12VideoMotionVectorHeap_GetProtectedResourceSession(This,riid,protected_session) (This)->lpVtbl->GetProtectedResourceSession(This,riid,protected_session)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoMotionVectorHeap_QueryInterface(ID3D12VideoMotionVectorHeap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoMotionVectorHeap_AddRef(ID3D12VideoMotionVectorHeap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoMotionVectorHeap_Release(ID3D12VideoMotionVectorHeap* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoMotionVectorHeap_GetPrivateData(ID3D12VideoMotionVectorHeap* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoMotionVectorHeap_SetPrivateData(ID3D12VideoMotionVectorHeap* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoMotionVectorHeap_SetPrivateDataInterface(ID3D12VideoMotionVectorHeap* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoMotionVectorHeap_SetName(ID3D12VideoMotionVectorHeap* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoMotionVectorHeap_GetDevice(ID3D12VideoMotionVectorHeap* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12VideoMotionVectorHeap methods ***/
static inline D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC ID3D12VideoMotionVectorHeap_GetDesc(ID3D12VideoMotionVectorHeap* This) {
    D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC __ret;
    return *This->lpVtbl->GetDesc(This,&__ret);
}
static inline HRESULT ID3D12VideoMotionVectorHeap_GetProtectedResourceSession(ID3D12VideoMotionVectorHeap* This,REFIID riid,void **protected_session) {
    return This->lpVtbl->GetProtectedResourceSession(This,riid,protected_session);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoMotionVectorHeap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12VideoDevice1 interface
 */
#ifndef __ID3D12VideoDevice1_INTERFACE_DEFINED__
#define __ID3D12VideoDevice1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoDevice1, 0x981611ad, 0xa144, 0x4c83, 0x98,0x90, 0xf3,0x0e,0x26,0xd6,0x58,0xab);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("981611ad-a144-4c83-9890-f30e26d658ab")
ID3D12VideoDevice1 : public ID3D12VideoDevice
{
    virtual HRESULT STDMETHODCALLTYPE CreateVideoMotionEstimator(
        const D3D12_VIDEO_MOTION_ESTIMATOR_DESC *desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_motion_estimator) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoMotionVectorHeap(
        const D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC *desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_motion_vector_heap) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoDevice1, 0x981611ad, 0xa144, 0x4c83, 0x98,0x90, 0xf3,0x0e,0x26,0xd6,0x58,0xab)
#endif
#else
typedef struct ID3D12VideoDevice1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoDevice1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoDevice1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoDevice1 *This);

    /*** ID3D12VideoDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        ID3D12VideoDevice1 *This,
        D3D12_FEATURE_VIDEO feature_video,
        void *feature_support_data,
        UINT feature_support_data_size);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoder)(
        ID3D12VideoDevice1 *This,
        const D3D12_VIDEO_DECODER_DESC *desc,
        REFIID riid,
        void **video_decoder);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoderHeap)(
        ID3D12VideoDevice1 *This,
        const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,
        REFIID riid,
        void **video_decoder_heap);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessor)(
        ID3D12VideoDevice1 *This,
        UINT node_mask,
        const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,
        UINT input_stream_descs_count,
        const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,
        REFIID riid,
        void **video_processor);

    /*** ID3D12VideoDevice1 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateVideoMotionEstimator)(
        ID3D12VideoDevice1 *This,
        const D3D12_VIDEO_MOTION_ESTIMATOR_DESC *desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_motion_estimator);

    HRESULT (STDMETHODCALLTYPE *CreateVideoMotionVectorHeap)(
        ID3D12VideoDevice1 *This,
        const D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC *desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_motion_vector_heap);

    END_INTERFACE
} ID3D12VideoDevice1Vtbl;

interface ID3D12VideoDevice1 {
    CONST_VTBL ID3D12VideoDevice1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoDevice1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoDevice1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoDevice1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12VideoDevice methods ***/
#define ID3D12VideoDevice1_CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size) (This)->lpVtbl->CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size)
#define ID3D12VideoDevice1_CreateVideoDecoder(This,desc,riid,video_decoder) (This)->lpVtbl->CreateVideoDecoder(This,desc,riid,video_decoder)
#define ID3D12VideoDevice1_CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap) (This)->lpVtbl->CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap)
#define ID3D12VideoDevice1_CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor) (This)->lpVtbl->CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor)
/*** ID3D12VideoDevice1 methods ***/
#define ID3D12VideoDevice1_CreateVideoMotionEstimator(This,desc,protected_resource_session,riid,video_motion_estimator) (This)->lpVtbl->CreateVideoMotionEstimator(This,desc,protected_resource_session,riid,video_motion_estimator)
#define ID3D12VideoDevice1_CreateVideoMotionVectorHeap(This,desc,protected_resource_session,riid,video_motion_vector_heap) (This)->lpVtbl->CreateVideoMotionVectorHeap(This,desc,protected_resource_session,riid,video_motion_vector_heap)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoDevice1_QueryInterface(ID3D12VideoDevice1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoDevice1_AddRef(ID3D12VideoDevice1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoDevice1_Release(ID3D12VideoDevice1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12VideoDevice methods ***/
static inline HRESULT ID3D12VideoDevice1_CheckFeatureSupport(ID3D12VideoDevice1* This,D3D12_FEATURE_VIDEO feature_video,void *feature_support_data,UINT feature_support_data_size) {
    return This->lpVtbl->CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size);
}
static inline HRESULT ID3D12VideoDevice1_CreateVideoDecoder(ID3D12VideoDevice1* This,const D3D12_VIDEO_DECODER_DESC *desc,REFIID riid,void **video_decoder) {
    return This->lpVtbl->CreateVideoDecoder(This,desc,riid,video_decoder);
}
static inline HRESULT ID3D12VideoDevice1_CreateVideoDecoderHeap(ID3D12VideoDevice1* This,const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,REFIID riid,void **video_decoder_heap) {
    return This->lpVtbl->CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap);
}
static inline HRESULT ID3D12VideoDevice1_CreateVideoProcessor(ID3D12VideoDevice1* This,UINT node_mask,const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,UINT input_stream_descs_count,const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,REFIID riid,void **video_processor) {
    return This->lpVtbl->CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor);
}
/*** ID3D12VideoDevice1 methods ***/
static inline HRESULT ID3D12VideoDevice1_CreateVideoMotionEstimator(ID3D12VideoDevice1* This,const D3D12_VIDEO_MOTION_ESTIMATOR_DESC *desc,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_motion_estimator) {
    return This->lpVtbl->CreateVideoMotionEstimator(This,desc,protected_resource_session,riid,video_motion_estimator);
}
static inline HRESULT ID3D12VideoDevice1_CreateVideoMotionVectorHeap(ID3D12VideoDevice1* This,const D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC *desc,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_motion_vector_heap) {
    return This->lpVtbl->CreateVideoMotionVectorHeap(This,desc,protected_resource_session,riid,video_motion_vector_heap);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoDevice1_INTERFACE_DEFINED__ */

typedef struct D3D12_RESOURCE_COORDINATE {
    UINT64 X;
    UINT Y;
    UINT Z;
    UINT SubresourceIndex;
} D3D12_RESOURCE_COORDINATE;
typedef struct D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT {
    ID3D12VideoMotionVectorHeap *pMotionVectorHeap;
} D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT;
typedef struct D3D12_VIDEO_MOTION_ESTIMATOR_INPUT {
    ID3D12Resource *pInputTexture2D;
    UINT InputSubresourceIndex;
    ID3D12Resource *pReferenceTexture2D;
    UINT ReferenceSubresourceIndex;
    ID3D12VideoMotionVectorHeap *pHintMotionVectorHeap;
} D3D12_VIDEO_MOTION_ESTIMATOR_INPUT;
typedef struct D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT {
    ID3D12Resource *pMotionVectorTexture2D;
    D3D12_RESOURCE_COORDINATE MotionVectorCoordinate;
} D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT;
typedef struct D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT {
    ID3D12VideoMotionVectorHeap *pMotionVectorHeap;
    UINT PixelWidth;
    UINT PixelHeight;
} D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT;
/*****************************************************************************
 * ID3D12VideoEncodeCommandList interface
 */
#ifndef __ID3D12VideoEncodeCommandList_INTERFACE_DEFINED__
#define __ID3D12VideoEncodeCommandList_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoEncodeCommandList, 0x8455293a, 0x0cbd, 0x4831, 0x9b,0x39, 0xfb,0xdb,0xab,0x72,0x47,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("8455293a-0cbd-4831-9b39-fbdbab724723")
ID3D12VideoEncodeCommandList : public ID3D12CommandList
{
    virtual HRESULT STDMETHODCALLTYPE Close(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE Reset(
        ID3D12CommandAllocator *allocator) = 0;

    virtual void STDMETHODCALLTYPE ClearState(
        ) = 0;

    virtual void STDMETHODCALLTYPE ResourceBarrier(
        UINT barriers_count,
        const D3D12_RESOURCE_BARRIER *barriers) = 0;

    virtual void STDMETHODCALLTYPE DiscardResource(
        ID3D12Resource *resource,
        const D3D12_DISCARD_REGION *region) = 0;

    virtual void STDMETHODCALLTYPE BeginQuery(
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index) = 0;

    virtual void STDMETHODCALLTYPE EndQuery(
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index) = 0;

    virtual void STDMETHODCALLTYPE ResolveQueryData(
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT start_index,
        UINT queries_count,
        ID3D12Resource *destination_buffer,
        UINT64 aligned_destination_buffer_offset) = 0;

    virtual void STDMETHODCALLTYPE SetPredication(
        ID3D12Resource *buffer,
        UINT64 aligned_buffer_offset,
        D3D12_PREDICATION_OP operation) = 0;

    virtual void STDMETHODCALLTYPE SetMarker(
        UINT metadata,
        const void *data,
        UINT size) = 0;

    virtual void STDMETHODCALLTYPE BeginEvent(
        UINT metadata,
        const void *data,
        UINT size) = 0;

    virtual void STDMETHODCALLTYPE EndEvent(
        ) = 0;

    virtual void STDMETHODCALLTYPE EstimateMotion(
        ID3D12VideoMotionEstimator *motion_estimator,
        const D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT *output_arguments,
        const D3D12_VIDEO_MOTION_ESTIMATOR_INPUT *input_arguments) = 0;

    virtual void STDMETHODCALLTYPE ResolveMotionVectorHeap(
        const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT *output_arguments,
        const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT *input_arguments) = 0;

    virtual void STDMETHODCALLTYPE WriteBufferImmediate(
        UINT count,
        const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,
        const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes) = 0;

    virtual void STDMETHODCALLTYPE SetProtectedResourceSession(
        ID3D12ProtectedResourceSession *protected_resource_session) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoEncodeCommandList, 0x8455293a, 0x0cbd, 0x4831, 0x9b,0x39, 0xfb,0xdb,0xab,0x72,0x47,0x23)
#endif
#else
typedef struct ID3D12VideoEncodeCommandListVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoEncodeCommandList *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoEncodeCommandList *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoEncodeCommandList *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoEncodeCommandList *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoEncodeCommandList *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoEncodeCommandList *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoEncodeCommandList *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoEncodeCommandList *This,
        REFIID riid,
        void **device);

    /*** ID3D12CommandList methods ***/
    D3D12_COMMAND_LIST_TYPE (STDMETHODCALLTYPE *GetType)(
        ID3D12VideoEncodeCommandList *This);

    /*** ID3D12VideoEncodeCommandList methods ***/
    HRESULT (STDMETHODCALLTYPE *Close)(
        ID3D12VideoEncodeCommandList *This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        ID3D12VideoEncodeCommandList *This,
        ID3D12CommandAllocator *allocator);

    void (STDMETHODCALLTYPE *ClearState)(
        ID3D12VideoEncodeCommandList *This);

    void (STDMETHODCALLTYPE *ResourceBarrier)(
        ID3D12VideoEncodeCommandList *This,
        UINT barriers_count,
        const D3D12_RESOURCE_BARRIER *barriers);

    void (STDMETHODCALLTYPE *DiscardResource)(
        ID3D12VideoEncodeCommandList *This,
        ID3D12Resource *resource,
        const D3D12_DISCARD_REGION *region);

    void (STDMETHODCALLTYPE *BeginQuery)(
        ID3D12VideoEncodeCommandList *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index);

    void (STDMETHODCALLTYPE *EndQuery)(
        ID3D12VideoEncodeCommandList *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index);

    void (STDMETHODCALLTYPE *ResolveQueryData)(
        ID3D12VideoEncodeCommandList *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT start_index,
        UINT queries_count,
        ID3D12Resource *destination_buffer,
        UINT64 aligned_destination_buffer_offset);

    void (STDMETHODCALLTYPE *SetPredication)(
        ID3D12VideoEncodeCommandList *This,
        ID3D12Resource *buffer,
        UINT64 aligned_buffer_offset,
        D3D12_PREDICATION_OP operation);

    void (STDMETHODCALLTYPE *SetMarker)(
        ID3D12VideoEncodeCommandList *This,
        UINT metadata,
        const void *data,
        UINT size);

    void (STDMETHODCALLTYPE *BeginEvent)(
        ID3D12VideoEncodeCommandList *This,
        UINT metadata,
        const void *data,
        UINT size);

    void (STDMETHODCALLTYPE *EndEvent)(
        ID3D12VideoEncodeCommandList *This);

    void (STDMETHODCALLTYPE *EstimateMotion)(
        ID3D12VideoEncodeCommandList *This,
        ID3D12VideoMotionEstimator *motion_estimator,
        const D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT *output_arguments,
        const D3D12_VIDEO_MOTION_ESTIMATOR_INPUT *input_arguments);

    void (STDMETHODCALLTYPE *ResolveMotionVectorHeap)(
        ID3D12VideoEncodeCommandList *This,
        const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT *output_arguments,
        const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT *input_arguments);

    void (STDMETHODCALLTYPE *WriteBufferImmediate)(
        ID3D12VideoEncodeCommandList *This,
        UINT count,
        const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,
        const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes);

    void (STDMETHODCALLTYPE *SetProtectedResourceSession)(
        ID3D12VideoEncodeCommandList *This,
        ID3D12ProtectedResourceSession *protected_resource_session);

    END_INTERFACE
} ID3D12VideoEncodeCommandListVtbl;

interface ID3D12VideoEncodeCommandList {
    CONST_VTBL ID3D12VideoEncodeCommandListVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoEncodeCommandList_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoEncodeCommandList_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoEncodeCommandList_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoEncodeCommandList_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoEncodeCommandList_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoEncodeCommandList_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoEncodeCommandList_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoEncodeCommandList_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12CommandList methods ***/
#define ID3D12VideoEncodeCommandList_GetType(This) (This)->lpVtbl->GetType(This)
/*** ID3D12VideoEncodeCommandList methods ***/
#define ID3D12VideoEncodeCommandList_Close(This) (This)->lpVtbl->Close(This)
#define ID3D12VideoEncodeCommandList_Reset(This,allocator) (This)->lpVtbl->Reset(This,allocator)
#define ID3D12VideoEncodeCommandList_ClearState(This) (This)->lpVtbl->ClearState(This)
#define ID3D12VideoEncodeCommandList_ResourceBarrier(This,barriers_count,barriers) (This)->lpVtbl->ResourceBarrier(This,barriers_count,barriers)
#define ID3D12VideoEncodeCommandList_DiscardResource(This,resource,region) (This)->lpVtbl->DiscardResource(This,resource,region)
#define ID3D12VideoEncodeCommandList_BeginQuery(This,query_heap,type,index) (This)->lpVtbl->BeginQuery(This,query_heap,type,index)
#define ID3D12VideoEncodeCommandList_EndQuery(This,query_heap,type,index) (This)->lpVtbl->EndQuery(This,query_heap,type,index)
#define ID3D12VideoEncodeCommandList_ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset) (This)->lpVtbl->ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset)
#define ID3D12VideoEncodeCommandList_SetPredication(This,buffer,aligned_buffer_offset,operation) (This)->lpVtbl->SetPredication(This,buffer,aligned_buffer_offset,operation)
#define ID3D12VideoEncodeCommandList_SetMarker(This,metadata,data,size) (This)->lpVtbl->SetMarker(This,metadata,data,size)
#define ID3D12VideoEncodeCommandList_BeginEvent(This,metadata,data,size) (This)->lpVtbl->BeginEvent(This,metadata,data,size)
#define ID3D12VideoEncodeCommandList_EndEvent(This) (This)->lpVtbl->EndEvent(This)
#define ID3D12VideoEncodeCommandList_EstimateMotion(This,motion_estimator,output_arguments,input_arguments) (This)->lpVtbl->EstimateMotion(This,motion_estimator,output_arguments,input_arguments)
#define ID3D12VideoEncodeCommandList_ResolveMotionVectorHeap(This,output_arguments,input_arguments) (This)->lpVtbl->ResolveMotionVectorHeap(This,output_arguments,input_arguments)
#define ID3D12VideoEncodeCommandList_WriteBufferImmediate(This,count,params,modes) (This)->lpVtbl->WriteBufferImmediate(This,count,params,modes)
#define ID3D12VideoEncodeCommandList_SetProtectedResourceSession(This,protected_resource_session) (This)->lpVtbl->SetProtectedResourceSession(This,protected_resource_session)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList_QueryInterface(ID3D12VideoEncodeCommandList* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoEncodeCommandList_AddRef(ID3D12VideoEncodeCommandList* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoEncodeCommandList_Release(ID3D12VideoEncodeCommandList* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList_GetPrivateData(ID3D12VideoEncodeCommandList* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoEncodeCommandList_SetPrivateData(ID3D12VideoEncodeCommandList* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoEncodeCommandList_SetPrivateDataInterface(ID3D12VideoEncodeCommandList* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoEncodeCommandList_SetName(ID3D12VideoEncodeCommandList* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList_GetDevice(ID3D12VideoEncodeCommandList* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12CommandList methods ***/
static inline D3D12_COMMAND_LIST_TYPE ID3D12VideoEncodeCommandList_GetType(ID3D12VideoEncodeCommandList* This) {
    return This->lpVtbl->GetType(This);
}
/*** ID3D12VideoEncodeCommandList methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList_Close(ID3D12VideoEncodeCommandList* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT ID3D12VideoEncodeCommandList_Reset(ID3D12VideoEncodeCommandList* This,ID3D12CommandAllocator *allocator) {
    return This->lpVtbl->Reset(This,allocator);
}
static inline void ID3D12VideoEncodeCommandList_ClearState(ID3D12VideoEncodeCommandList* This) {
    This->lpVtbl->ClearState(This);
}
static inline void ID3D12VideoEncodeCommandList_ResourceBarrier(ID3D12VideoEncodeCommandList* This,UINT barriers_count,const D3D12_RESOURCE_BARRIER *barriers) {
    This->lpVtbl->ResourceBarrier(This,barriers_count,barriers);
}
static inline void ID3D12VideoEncodeCommandList_DiscardResource(ID3D12VideoEncodeCommandList* This,ID3D12Resource *resource,const D3D12_DISCARD_REGION *region) {
    This->lpVtbl->DiscardResource(This,resource,region);
}
static inline void ID3D12VideoEncodeCommandList_BeginQuery(ID3D12VideoEncodeCommandList* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT index) {
    This->lpVtbl->BeginQuery(This,query_heap,type,index);
}
static inline void ID3D12VideoEncodeCommandList_EndQuery(ID3D12VideoEncodeCommandList* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT index) {
    This->lpVtbl->EndQuery(This,query_heap,type,index);
}
static inline void ID3D12VideoEncodeCommandList_ResolveQueryData(ID3D12VideoEncodeCommandList* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT start_index,UINT queries_count,ID3D12Resource *destination_buffer,UINT64 aligned_destination_buffer_offset) {
    This->lpVtbl->ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset);
}
static inline void ID3D12VideoEncodeCommandList_SetPredication(ID3D12VideoEncodeCommandList* This,ID3D12Resource *buffer,UINT64 aligned_buffer_offset,D3D12_PREDICATION_OP operation) {
    This->lpVtbl->SetPredication(This,buffer,aligned_buffer_offset,operation);
}
static inline void ID3D12VideoEncodeCommandList_SetMarker(ID3D12VideoEncodeCommandList* This,UINT metadata,const void *data,UINT size) {
    This->lpVtbl->SetMarker(This,metadata,data,size);
}
static inline void ID3D12VideoEncodeCommandList_BeginEvent(ID3D12VideoEncodeCommandList* This,UINT metadata,const void *data,UINT size) {
    This->lpVtbl->BeginEvent(This,metadata,data,size);
}
static inline void ID3D12VideoEncodeCommandList_EndEvent(ID3D12VideoEncodeCommandList* This) {
    This->lpVtbl->EndEvent(This);
}
static inline void ID3D12VideoEncodeCommandList_EstimateMotion(ID3D12VideoEncodeCommandList* This,ID3D12VideoMotionEstimator *motion_estimator,const D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT *output_arguments,const D3D12_VIDEO_MOTION_ESTIMATOR_INPUT *input_arguments) {
    This->lpVtbl->EstimateMotion(This,motion_estimator,output_arguments,input_arguments);
}
static inline void ID3D12VideoEncodeCommandList_ResolveMotionVectorHeap(ID3D12VideoEncodeCommandList* This,const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT *output_arguments,const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT *input_arguments) {
    This->lpVtbl->ResolveMotionVectorHeap(This,output_arguments,input_arguments);
}
static inline void ID3D12VideoEncodeCommandList_WriteBufferImmediate(ID3D12VideoEncodeCommandList* This,UINT count,const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes) {
    This->lpVtbl->WriteBufferImmediate(This,count,params,modes);
}
static inline void ID3D12VideoEncodeCommandList_SetProtectedResourceSession(ID3D12VideoEncodeCommandList* This,ID3D12ProtectedResourceSession *protected_resource_session) {
    This->lpVtbl->SetProtectedResourceSession(This,protected_resource_session);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoEncodeCommandList_INTERFACE_DEFINED__ */

typedef struct D3D12_VIDEO_EXTENSION_COMMAND_DESC {
    UINT NodeMask;
    GUID CommandId;
} D3D12_VIDEO_EXTENSION_COMMAND_DESC;
/*****************************************************************************
 * ID3D12VideoExtensionCommand interface
 */
#ifndef __ID3D12VideoExtensionCommand_INTERFACE_DEFINED__
#define __ID3D12VideoExtensionCommand_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoExtensionCommand, 0x554e41e8, 0xae8e, 0x4a8c, 0xb7,0xd2, 0x5b,0x4f,0x27,0x4a,0x30,0xe4);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("554e41e8-ae8e-4a8c-b7d2-5b4f274a30e4")
ID3D12VideoExtensionCommand : public ID3D12Pageable
{
#ifdef WIDL_EXPLICIT_AGGREGATE_RETURNS
    virtual D3D12_VIDEO_EXTENSION_COMMAND_DESC* STDMETHODCALLTYPE GetDesc(
        D3D12_VIDEO_EXTENSION_COMMAND_DESC *__ret) = 0;
    D3D12_VIDEO_EXTENSION_COMMAND_DESC STDMETHODCALLTYPE GetDesc(
        )
    {
        D3D12_VIDEO_EXTENSION_COMMAND_DESC __ret;
        return *GetDesc(&__ret);
    }
#else
    virtual D3D12_VIDEO_EXTENSION_COMMAND_DESC STDMETHODCALLTYPE GetDesc(
        ) = 0;
#endif

    virtual HRESULT STDMETHODCALLTYPE GetProtectedResourceSession(
        REFIID riid,
        void **protected_session) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoExtensionCommand, 0x554e41e8, 0xae8e, 0x4a8c, 0xb7,0xd2, 0x5b,0x4f,0x27,0x4a,0x30,0xe4)
#endif
#else
typedef struct ID3D12VideoExtensionCommandVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoExtensionCommand *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoExtensionCommand *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoExtensionCommand *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoExtensionCommand *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoExtensionCommand *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoExtensionCommand *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoExtensionCommand *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoExtensionCommand *This,
        REFIID riid,
        void **device);

    /*** ID3D12VideoExtensionCommand methods ***/
    D3D12_VIDEO_EXTENSION_COMMAND_DESC * (STDMETHODCALLTYPE *GetDesc)(
        ID3D12VideoExtensionCommand *This,
        D3D12_VIDEO_EXTENSION_COMMAND_DESC *__ret);

    HRESULT (STDMETHODCALLTYPE *GetProtectedResourceSession)(
        ID3D12VideoExtensionCommand *This,
        REFIID riid,
        void **protected_session);

    END_INTERFACE
} ID3D12VideoExtensionCommandVtbl;

interface ID3D12VideoExtensionCommand {
    CONST_VTBL ID3D12VideoExtensionCommandVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoExtensionCommand_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoExtensionCommand_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoExtensionCommand_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoExtensionCommand_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoExtensionCommand_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoExtensionCommand_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoExtensionCommand_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoExtensionCommand_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12VideoExtensionCommand methods ***/
#define ID3D12VideoExtensionCommand_GetDesc(This) ID3D12VideoExtensionCommand_GetDesc_define_WIDL_C_INLINE_WRAPPERS_for_aggregate_return_support
#define ID3D12VideoExtensionCommand_GetProtectedResourceSession(This,riid,protected_session) (This)->lpVtbl->GetProtectedResourceSession(This,riid,protected_session)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoExtensionCommand_QueryInterface(ID3D12VideoExtensionCommand* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoExtensionCommand_AddRef(ID3D12VideoExtensionCommand* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoExtensionCommand_Release(ID3D12VideoExtensionCommand* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoExtensionCommand_GetPrivateData(ID3D12VideoExtensionCommand* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoExtensionCommand_SetPrivateData(ID3D12VideoExtensionCommand* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoExtensionCommand_SetPrivateDataInterface(ID3D12VideoExtensionCommand* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoExtensionCommand_SetName(ID3D12VideoExtensionCommand* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoExtensionCommand_GetDevice(ID3D12VideoExtensionCommand* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12VideoExtensionCommand methods ***/
static inline D3D12_VIDEO_EXTENSION_COMMAND_DESC ID3D12VideoExtensionCommand_GetDesc(ID3D12VideoExtensionCommand* This) {
    D3D12_VIDEO_EXTENSION_COMMAND_DESC __ret;
    return *This->lpVtbl->GetDesc(This,&__ret);
}
static inline HRESULT ID3D12VideoExtensionCommand_GetProtectedResourceSession(ID3D12VideoExtensionCommand* This,REFIID riid,void **protected_session) {
    return This->lpVtbl->GetProtectedResourceSession(This,riid,protected_session);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoExtensionCommand_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12VideoDevice2 interface
 */
#ifndef __ID3D12VideoDevice2_INTERFACE_DEFINED__
#define __ID3D12VideoDevice2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoDevice2, 0xf019ac49, 0xf838, 0x4a95, 0x9b,0x17, 0x57,0x94,0x37,0xc8,0xf5,0x13);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("f019ac49-f838-4a95-9b17-579437c8f513")
ID3D12VideoDevice2 : public ID3D12VideoDevice1
{
    virtual HRESULT STDMETHODCALLTYPE CreateVideoDecoder1(
        const D3D12_VIDEO_DECODER_DESC *desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_decoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoDecoderHeap1(
        const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_decoder_heap) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoProcessor1(
        UINT node_mask,
        const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,
        UINT input_stream_descs_count,
        const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_processor) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoExtensionCommand(
        const D3D12_VIDEO_EXTENSION_COMMAND_DESC *desc,
        const void *creation_parameters,
        SIZE_T creation_parameters_data_size_in_bytes,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_extension_command) = 0;

    virtual HRESULT STDMETHODCALLTYPE ExecuteExtensionCommand(
        ID3D12VideoExtensionCommand *extension_command,
        const void *execution_parameters,
        SIZE_T execution_parameters_size_in_bytes,
        void *output_data,
        SIZE_T output_data_size_in_bytes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoDevice2, 0xf019ac49, 0xf838, 0x4a95, 0x9b,0x17, 0x57,0x94,0x37,0xc8,0xf5,0x13)
#endif
#else
typedef struct ID3D12VideoDevice2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoDevice2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoDevice2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoDevice2 *This);

    /*** ID3D12VideoDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        ID3D12VideoDevice2 *This,
        D3D12_FEATURE_VIDEO feature_video,
        void *feature_support_data,
        UINT feature_support_data_size);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoder)(
        ID3D12VideoDevice2 *This,
        const D3D12_VIDEO_DECODER_DESC *desc,
        REFIID riid,
        void **video_decoder);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoderHeap)(
        ID3D12VideoDevice2 *This,
        const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,
        REFIID riid,
        void **video_decoder_heap);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessor)(
        ID3D12VideoDevice2 *This,
        UINT node_mask,
        const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,
        UINT input_stream_descs_count,
        const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,
        REFIID riid,
        void **video_processor);

    /*** ID3D12VideoDevice1 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateVideoMotionEstimator)(
        ID3D12VideoDevice2 *This,
        const D3D12_VIDEO_MOTION_ESTIMATOR_DESC *desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_motion_estimator);

    HRESULT (STDMETHODCALLTYPE *CreateVideoMotionVectorHeap)(
        ID3D12VideoDevice2 *This,
        const D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC *desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_motion_vector_heap);

    /*** ID3D12VideoDevice2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoder1)(
        ID3D12VideoDevice2 *This,
        const D3D12_VIDEO_DECODER_DESC *desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_decoder);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoderHeap1)(
        ID3D12VideoDevice2 *This,
        const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_decoder_heap);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessor1)(
        ID3D12VideoDevice2 *This,
        UINT node_mask,
        const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,
        UINT input_stream_descs_count,
        const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_processor);

    HRESULT (STDMETHODCALLTYPE *CreateVideoExtensionCommand)(
        ID3D12VideoDevice2 *This,
        const D3D12_VIDEO_EXTENSION_COMMAND_DESC *desc,
        const void *creation_parameters,
        SIZE_T creation_parameters_data_size_in_bytes,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_extension_command);

    HRESULT (STDMETHODCALLTYPE *ExecuteExtensionCommand)(
        ID3D12VideoDevice2 *This,
        ID3D12VideoExtensionCommand *extension_command,
        const void *execution_parameters,
        SIZE_T execution_parameters_size_in_bytes,
        void *output_data,
        SIZE_T output_data_size_in_bytes);

    END_INTERFACE
} ID3D12VideoDevice2Vtbl;

interface ID3D12VideoDevice2 {
    CONST_VTBL ID3D12VideoDevice2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoDevice2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoDevice2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoDevice2_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12VideoDevice methods ***/
#define ID3D12VideoDevice2_CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size) (This)->lpVtbl->CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size)
#define ID3D12VideoDevice2_CreateVideoDecoder(This,desc,riid,video_decoder) (This)->lpVtbl->CreateVideoDecoder(This,desc,riid,video_decoder)
#define ID3D12VideoDevice2_CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap) (This)->lpVtbl->CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap)
#define ID3D12VideoDevice2_CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor) (This)->lpVtbl->CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor)
/*** ID3D12VideoDevice1 methods ***/
#define ID3D12VideoDevice2_CreateVideoMotionEstimator(This,desc,protected_resource_session,riid,video_motion_estimator) (This)->lpVtbl->CreateVideoMotionEstimator(This,desc,protected_resource_session,riid,video_motion_estimator)
#define ID3D12VideoDevice2_CreateVideoMotionVectorHeap(This,desc,protected_resource_session,riid,video_motion_vector_heap) (This)->lpVtbl->CreateVideoMotionVectorHeap(This,desc,protected_resource_session,riid,video_motion_vector_heap)
/*** ID3D12VideoDevice2 methods ***/
#define ID3D12VideoDevice2_CreateVideoDecoder1(This,desc,protected_resource_session,riid,video_decoder) (This)->lpVtbl->CreateVideoDecoder1(This,desc,protected_resource_session,riid,video_decoder)
#define ID3D12VideoDevice2_CreateVideoDecoderHeap1(This,video_decoder_heap_desc,protected_resource_session,riid,video_decoder_heap) (This)->lpVtbl->CreateVideoDecoderHeap1(This,video_decoder_heap_desc,protected_resource_session,riid,video_decoder_heap)
#define ID3D12VideoDevice2_CreateVideoProcessor1(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,protected_resource_session,riid,video_processor) (This)->lpVtbl->CreateVideoProcessor1(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,protected_resource_session,riid,video_processor)
#define ID3D12VideoDevice2_CreateVideoExtensionCommand(This,desc,creation_parameters,creation_parameters_data_size_in_bytes,protected_resource_session,riid,video_extension_command) (This)->lpVtbl->CreateVideoExtensionCommand(This,desc,creation_parameters,creation_parameters_data_size_in_bytes,protected_resource_session,riid,video_extension_command)
#define ID3D12VideoDevice2_ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes,output_data,output_data_size_in_bytes) (This)->lpVtbl->ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes,output_data,output_data_size_in_bytes)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoDevice2_QueryInterface(ID3D12VideoDevice2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoDevice2_AddRef(ID3D12VideoDevice2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoDevice2_Release(ID3D12VideoDevice2* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12VideoDevice methods ***/
static inline HRESULT ID3D12VideoDevice2_CheckFeatureSupport(ID3D12VideoDevice2* This,D3D12_FEATURE_VIDEO feature_video,void *feature_support_data,UINT feature_support_data_size) {
    return This->lpVtbl->CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size);
}
static inline HRESULT ID3D12VideoDevice2_CreateVideoDecoder(ID3D12VideoDevice2* This,const D3D12_VIDEO_DECODER_DESC *desc,REFIID riid,void **video_decoder) {
    return This->lpVtbl->CreateVideoDecoder(This,desc,riid,video_decoder);
}
static inline HRESULT ID3D12VideoDevice2_CreateVideoDecoderHeap(ID3D12VideoDevice2* This,const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,REFIID riid,void **video_decoder_heap) {
    return This->lpVtbl->CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap);
}
static inline HRESULT ID3D12VideoDevice2_CreateVideoProcessor(ID3D12VideoDevice2* This,UINT node_mask,const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,UINT input_stream_descs_count,const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,REFIID riid,void **video_processor) {
    return This->lpVtbl->CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor);
}
/*** ID3D12VideoDevice1 methods ***/
static inline HRESULT ID3D12VideoDevice2_CreateVideoMotionEstimator(ID3D12VideoDevice2* This,const D3D12_VIDEO_MOTION_ESTIMATOR_DESC *desc,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_motion_estimator) {
    return This->lpVtbl->CreateVideoMotionEstimator(This,desc,protected_resource_session,riid,video_motion_estimator);
}
static inline HRESULT ID3D12VideoDevice2_CreateVideoMotionVectorHeap(ID3D12VideoDevice2* This,const D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC *desc,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_motion_vector_heap) {
    return This->lpVtbl->CreateVideoMotionVectorHeap(This,desc,protected_resource_session,riid,video_motion_vector_heap);
}
/*** ID3D12VideoDevice2 methods ***/
static inline HRESULT ID3D12VideoDevice2_CreateVideoDecoder1(ID3D12VideoDevice2* This,const D3D12_VIDEO_DECODER_DESC *desc,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_decoder) {
    return This->lpVtbl->CreateVideoDecoder1(This,desc,protected_resource_session,riid,video_decoder);
}
static inline HRESULT ID3D12VideoDevice2_CreateVideoDecoderHeap1(ID3D12VideoDevice2* This,const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_decoder_heap) {
    return This->lpVtbl->CreateVideoDecoderHeap1(This,video_decoder_heap_desc,protected_resource_session,riid,video_decoder_heap);
}
static inline HRESULT ID3D12VideoDevice2_CreateVideoProcessor1(ID3D12VideoDevice2* This,UINT node_mask,const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,UINT input_stream_descs_count,const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_processor) {
    return This->lpVtbl->CreateVideoProcessor1(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,protected_resource_session,riid,video_processor);
}
static inline HRESULT ID3D12VideoDevice2_CreateVideoExtensionCommand(ID3D12VideoDevice2* This,const D3D12_VIDEO_EXTENSION_COMMAND_DESC *desc,const void *creation_parameters,SIZE_T creation_parameters_data_size_in_bytes,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_extension_command) {
    return This->lpVtbl->CreateVideoExtensionCommand(This,desc,creation_parameters,creation_parameters_data_size_in_bytes,protected_resource_session,riid,video_extension_command);
}
static inline HRESULT ID3D12VideoDevice2_ExecuteExtensionCommand(ID3D12VideoDevice2* This,ID3D12VideoExtensionCommand *extension_command,const void *execution_parameters,SIZE_T execution_parameters_size_in_bytes,void *output_data,SIZE_T output_data_size_in_bytes) {
    return This->lpVtbl->ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes,output_data,output_data_size_in_bytes);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoDevice2_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12VideoEncodeCommandList1 interface
 */
#ifndef __ID3D12VideoEncodeCommandList1_INTERFACE_DEFINED__
#define __ID3D12VideoEncodeCommandList1_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoEncodeCommandList1, 0x94971eca, 0x2bdb, 0x4769, 0x88,0xcf, 0x36,0x75,0xea,0x75,0x7e,0xbc);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("94971eca-2bdb-4769-88cf-3675ea757ebc")
ID3D12VideoEncodeCommandList1 : public ID3D12VideoEncodeCommandList
{
    virtual void STDMETHODCALLTYPE InitializeExtensionCommand(
        ID3D12VideoExtensionCommand *extension_command,
        const void *initialization_parameters,
        SIZE_T initialization_parameters_size_in_bytes) = 0;

    virtual void STDMETHODCALLTYPE ExecuteExtensionCommand(
        ID3D12VideoExtensionCommand *extension_command,
        const void *execution_parameters,
        SIZE_T execution_parameters_size_in_bytes) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoEncodeCommandList1, 0x94971eca, 0x2bdb, 0x4769, 0x88,0xcf, 0x36,0x75,0xea,0x75,0x7e,0xbc)
#endif
#else
typedef struct ID3D12VideoEncodeCommandList1Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoEncodeCommandList1 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoEncodeCommandList1 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoEncodeCommandList1 *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoEncodeCommandList1 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoEncodeCommandList1 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoEncodeCommandList1 *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoEncodeCommandList1 *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoEncodeCommandList1 *This,
        REFIID riid,
        void **device);

    /*** ID3D12CommandList methods ***/
    D3D12_COMMAND_LIST_TYPE (STDMETHODCALLTYPE *GetType)(
        ID3D12VideoEncodeCommandList1 *This);

    /*** ID3D12VideoEncodeCommandList methods ***/
    HRESULT (STDMETHODCALLTYPE *Close)(
        ID3D12VideoEncodeCommandList1 *This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        ID3D12VideoEncodeCommandList1 *This,
        ID3D12CommandAllocator *allocator);

    void (STDMETHODCALLTYPE *ClearState)(
        ID3D12VideoEncodeCommandList1 *This);

    void (STDMETHODCALLTYPE *ResourceBarrier)(
        ID3D12VideoEncodeCommandList1 *This,
        UINT barriers_count,
        const D3D12_RESOURCE_BARRIER *barriers);

    void (STDMETHODCALLTYPE *DiscardResource)(
        ID3D12VideoEncodeCommandList1 *This,
        ID3D12Resource *resource,
        const D3D12_DISCARD_REGION *region);

    void (STDMETHODCALLTYPE *BeginQuery)(
        ID3D12VideoEncodeCommandList1 *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index);

    void (STDMETHODCALLTYPE *EndQuery)(
        ID3D12VideoEncodeCommandList1 *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index);

    void (STDMETHODCALLTYPE *ResolveQueryData)(
        ID3D12VideoEncodeCommandList1 *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT start_index,
        UINT queries_count,
        ID3D12Resource *destination_buffer,
        UINT64 aligned_destination_buffer_offset);

    void (STDMETHODCALLTYPE *SetPredication)(
        ID3D12VideoEncodeCommandList1 *This,
        ID3D12Resource *buffer,
        UINT64 aligned_buffer_offset,
        D3D12_PREDICATION_OP operation);

    void (STDMETHODCALLTYPE *SetMarker)(
        ID3D12VideoEncodeCommandList1 *This,
        UINT metadata,
        const void *data,
        UINT size);

    void (STDMETHODCALLTYPE *BeginEvent)(
        ID3D12VideoEncodeCommandList1 *This,
        UINT metadata,
        const void *data,
        UINT size);

    void (STDMETHODCALLTYPE *EndEvent)(
        ID3D12VideoEncodeCommandList1 *This);

    void (STDMETHODCALLTYPE *EstimateMotion)(
        ID3D12VideoEncodeCommandList1 *This,
        ID3D12VideoMotionEstimator *motion_estimator,
        const D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT *output_arguments,
        const D3D12_VIDEO_MOTION_ESTIMATOR_INPUT *input_arguments);

    void (STDMETHODCALLTYPE *ResolveMotionVectorHeap)(
        ID3D12VideoEncodeCommandList1 *This,
        const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT *output_arguments,
        const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT *input_arguments);

    void (STDMETHODCALLTYPE *WriteBufferImmediate)(
        ID3D12VideoEncodeCommandList1 *This,
        UINT count,
        const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,
        const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes);

    void (STDMETHODCALLTYPE *SetProtectedResourceSession)(
        ID3D12VideoEncodeCommandList1 *This,
        ID3D12ProtectedResourceSession *protected_resource_session);

    /*** ID3D12VideoEncodeCommandList1 methods ***/
    void (STDMETHODCALLTYPE *InitializeExtensionCommand)(
        ID3D12VideoEncodeCommandList1 *This,
        ID3D12VideoExtensionCommand *extension_command,
        const void *initialization_parameters,
        SIZE_T initialization_parameters_size_in_bytes);

    void (STDMETHODCALLTYPE *ExecuteExtensionCommand)(
        ID3D12VideoEncodeCommandList1 *This,
        ID3D12VideoExtensionCommand *extension_command,
        const void *execution_parameters,
        SIZE_T execution_parameters_size_in_bytes);

    END_INTERFACE
} ID3D12VideoEncodeCommandList1Vtbl;

interface ID3D12VideoEncodeCommandList1 {
    CONST_VTBL ID3D12VideoEncodeCommandList1Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoEncodeCommandList1_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoEncodeCommandList1_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoEncodeCommandList1_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoEncodeCommandList1_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoEncodeCommandList1_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoEncodeCommandList1_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoEncodeCommandList1_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoEncodeCommandList1_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12CommandList methods ***/
#define ID3D12VideoEncodeCommandList1_GetType(This) (This)->lpVtbl->GetType(This)
/*** ID3D12VideoEncodeCommandList methods ***/
#define ID3D12VideoEncodeCommandList1_Close(This) (This)->lpVtbl->Close(This)
#define ID3D12VideoEncodeCommandList1_Reset(This,allocator) (This)->lpVtbl->Reset(This,allocator)
#define ID3D12VideoEncodeCommandList1_ClearState(This) (This)->lpVtbl->ClearState(This)
#define ID3D12VideoEncodeCommandList1_ResourceBarrier(This,barriers_count,barriers) (This)->lpVtbl->ResourceBarrier(This,barriers_count,barriers)
#define ID3D12VideoEncodeCommandList1_DiscardResource(This,resource,region) (This)->lpVtbl->DiscardResource(This,resource,region)
#define ID3D12VideoEncodeCommandList1_BeginQuery(This,query_heap,type,index) (This)->lpVtbl->BeginQuery(This,query_heap,type,index)
#define ID3D12VideoEncodeCommandList1_EndQuery(This,query_heap,type,index) (This)->lpVtbl->EndQuery(This,query_heap,type,index)
#define ID3D12VideoEncodeCommandList1_ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset) (This)->lpVtbl->ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset)
#define ID3D12VideoEncodeCommandList1_SetPredication(This,buffer,aligned_buffer_offset,operation) (This)->lpVtbl->SetPredication(This,buffer,aligned_buffer_offset,operation)
#define ID3D12VideoEncodeCommandList1_SetMarker(This,metadata,data,size) (This)->lpVtbl->SetMarker(This,metadata,data,size)
#define ID3D12VideoEncodeCommandList1_BeginEvent(This,metadata,data,size) (This)->lpVtbl->BeginEvent(This,metadata,data,size)
#define ID3D12VideoEncodeCommandList1_EndEvent(This) (This)->lpVtbl->EndEvent(This)
#define ID3D12VideoEncodeCommandList1_EstimateMotion(This,motion_estimator,output_arguments,input_arguments) (This)->lpVtbl->EstimateMotion(This,motion_estimator,output_arguments,input_arguments)
#define ID3D12VideoEncodeCommandList1_ResolveMotionVectorHeap(This,output_arguments,input_arguments) (This)->lpVtbl->ResolveMotionVectorHeap(This,output_arguments,input_arguments)
#define ID3D12VideoEncodeCommandList1_WriteBufferImmediate(This,count,params,modes) (This)->lpVtbl->WriteBufferImmediate(This,count,params,modes)
#define ID3D12VideoEncodeCommandList1_SetProtectedResourceSession(This,protected_resource_session) (This)->lpVtbl->SetProtectedResourceSession(This,protected_resource_session)
/*** ID3D12VideoEncodeCommandList1 methods ***/
#define ID3D12VideoEncodeCommandList1_InitializeExtensionCommand(This,extension_command,initialization_parameters,initialization_parameters_size_in_bytes) (This)->lpVtbl->InitializeExtensionCommand(This,extension_command,initialization_parameters,initialization_parameters_size_in_bytes)
#define ID3D12VideoEncodeCommandList1_ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes) (This)->lpVtbl->ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList1_QueryInterface(ID3D12VideoEncodeCommandList1* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoEncodeCommandList1_AddRef(ID3D12VideoEncodeCommandList1* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoEncodeCommandList1_Release(ID3D12VideoEncodeCommandList1* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList1_GetPrivateData(ID3D12VideoEncodeCommandList1* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoEncodeCommandList1_SetPrivateData(ID3D12VideoEncodeCommandList1* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoEncodeCommandList1_SetPrivateDataInterface(ID3D12VideoEncodeCommandList1* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoEncodeCommandList1_SetName(ID3D12VideoEncodeCommandList1* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList1_GetDevice(ID3D12VideoEncodeCommandList1* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12CommandList methods ***/
static inline D3D12_COMMAND_LIST_TYPE ID3D12VideoEncodeCommandList1_GetType(ID3D12VideoEncodeCommandList1* This) {
    return This->lpVtbl->GetType(This);
}
/*** ID3D12VideoEncodeCommandList methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList1_Close(ID3D12VideoEncodeCommandList1* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT ID3D12VideoEncodeCommandList1_Reset(ID3D12VideoEncodeCommandList1* This,ID3D12CommandAllocator *allocator) {
    return This->lpVtbl->Reset(This,allocator);
}
static inline void ID3D12VideoEncodeCommandList1_ClearState(ID3D12VideoEncodeCommandList1* This) {
    This->lpVtbl->ClearState(This);
}
static inline void ID3D12VideoEncodeCommandList1_ResourceBarrier(ID3D12VideoEncodeCommandList1* This,UINT barriers_count,const D3D12_RESOURCE_BARRIER *barriers) {
    This->lpVtbl->ResourceBarrier(This,barriers_count,barriers);
}
static inline void ID3D12VideoEncodeCommandList1_DiscardResource(ID3D12VideoEncodeCommandList1* This,ID3D12Resource *resource,const D3D12_DISCARD_REGION *region) {
    This->lpVtbl->DiscardResource(This,resource,region);
}
static inline void ID3D12VideoEncodeCommandList1_BeginQuery(ID3D12VideoEncodeCommandList1* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT index) {
    This->lpVtbl->BeginQuery(This,query_heap,type,index);
}
static inline void ID3D12VideoEncodeCommandList1_EndQuery(ID3D12VideoEncodeCommandList1* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT index) {
    This->lpVtbl->EndQuery(This,query_heap,type,index);
}
static inline void ID3D12VideoEncodeCommandList1_ResolveQueryData(ID3D12VideoEncodeCommandList1* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT start_index,UINT queries_count,ID3D12Resource *destination_buffer,UINT64 aligned_destination_buffer_offset) {
    This->lpVtbl->ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset);
}
static inline void ID3D12VideoEncodeCommandList1_SetPredication(ID3D12VideoEncodeCommandList1* This,ID3D12Resource *buffer,UINT64 aligned_buffer_offset,D3D12_PREDICATION_OP operation) {
    This->lpVtbl->SetPredication(This,buffer,aligned_buffer_offset,operation);
}
static inline void ID3D12VideoEncodeCommandList1_SetMarker(ID3D12VideoEncodeCommandList1* This,UINT metadata,const void *data,UINT size) {
    This->lpVtbl->SetMarker(This,metadata,data,size);
}
static inline void ID3D12VideoEncodeCommandList1_BeginEvent(ID3D12VideoEncodeCommandList1* This,UINT metadata,const void *data,UINT size) {
    This->lpVtbl->BeginEvent(This,metadata,data,size);
}
static inline void ID3D12VideoEncodeCommandList1_EndEvent(ID3D12VideoEncodeCommandList1* This) {
    This->lpVtbl->EndEvent(This);
}
static inline void ID3D12VideoEncodeCommandList1_EstimateMotion(ID3D12VideoEncodeCommandList1* This,ID3D12VideoMotionEstimator *motion_estimator,const D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT *output_arguments,const D3D12_VIDEO_MOTION_ESTIMATOR_INPUT *input_arguments) {
    This->lpVtbl->EstimateMotion(This,motion_estimator,output_arguments,input_arguments);
}
static inline void ID3D12VideoEncodeCommandList1_ResolveMotionVectorHeap(ID3D12VideoEncodeCommandList1* This,const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT *output_arguments,const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT *input_arguments) {
    This->lpVtbl->ResolveMotionVectorHeap(This,output_arguments,input_arguments);
}
static inline void ID3D12VideoEncodeCommandList1_WriteBufferImmediate(ID3D12VideoEncodeCommandList1* This,UINT count,const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes) {
    This->lpVtbl->WriteBufferImmediate(This,count,params,modes);
}
static inline void ID3D12VideoEncodeCommandList1_SetProtectedResourceSession(ID3D12VideoEncodeCommandList1* This,ID3D12ProtectedResourceSession *protected_resource_session) {
    This->lpVtbl->SetProtectedResourceSession(This,protected_resource_session);
}
/*** ID3D12VideoEncodeCommandList1 methods ***/
static inline void ID3D12VideoEncodeCommandList1_InitializeExtensionCommand(ID3D12VideoEncodeCommandList1* This,ID3D12VideoExtensionCommand *extension_command,const void *initialization_parameters,SIZE_T initialization_parameters_size_in_bytes) {
    This->lpVtbl->InitializeExtensionCommand(This,extension_command,initialization_parameters,initialization_parameters_size_in_bytes);
}
static inline void ID3D12VideoEncodeCommandList1_ExecuteExtensionCommand(ID3D12VideoEncodeCommandList1* This,ID3D12VideoExtensionCommand *extension_command,const void *execution_parameters,SIZE_T execution_parameters_size_in_bytes) {
    This->lpVtbl->ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoEncodeCommandList1_INTERFACE_DEFINED__ */

DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_MPEG2, 0xee27417f, 0x5e28, 0x4e65, 0xbe, 0xea, 0x1d, 0x26, 0xb5, 0x08, 0xad, 0xc9);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_MPEG1_AND_MPEG2, 0x86695f12, 0x340e, 0x4f04, 0x9f, 0xd3, 0x92, 0x53, 0xdd, 0x32, 0x74, 0x60);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_H264, 0x1b81be68, 0xa0c7, 0x11d3, 0xb9, 0x84, 0x00, 0xc0, 0x4f, 0x2e, 0x73, 0xc5);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_H264_STEREO_PROGRESSIVE, 0xd79be8da, 0x0cf1, 0x4c81, 0xb8, 0x2a, 0x69, 0xa4, 0xe2, 0x36, 0xf4, 0x3d);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_H264_STEREO, 0xf9aaccbb, 0xc2b6, 0x4cfc, 0x87, 0x79, 0x57, 0x07, 0xb1, 0x76, 0x05, 0x52);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_H264_MULTIVIEW, 0x705b9d82, 0x76cf, 0x49d6, 0xb7, 0xe6, 0xac, 0x88, 0x72, 0xdb, 0x01, 0x3c);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_VC1, 0x1b81beA3, 0xa0c7, 0x11d3, 0xb9, 0x84, 0x00, 0xc0, 0x4f, 0x2e, 0x73, 0xc5);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_VC1_D2010, 0x1b81beA4, 0xa0c7, 0x11d3, 0xb9, 0x84, 0x00, 0xc0, 0x4f, 0x2e, 0x73, 0xc5);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_MPEG4PT2_SIMPLE, 0xefd64d74, 0xc9e8,0x41d7,0xa5,0xe9,0xe9,0xb0,0xe3,0x9f,0xa3,0x19);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_MPEG4PT2_ADVSIMPLE_NOGMC, 0xed418a9f, 0x010d, 0x4eda, 0x9a, 0xe3, 0x9a, 0x65, 0x35, 0x8d, 0x8d, 0x2e);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_HEVC_MAIN, 0x5b11d51b, 0x2f4c, 0x4452, 0xbc, 0xc3, 0x09, 0xf2, 0xa1, 0x16, 0x0c, 0xc0);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_HEVC_MAIN10, 0x107af0e0, 0xef1a, 0x4d19, 0xab, 0xa8, 0x67, 0xa1, 0x63, 0x07, 0x3d, 0x13);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_VP9, 0x463707f8, 0xa1d0, 0x4585, 0x87, 0x6d, 0x83, 0xaa, 0x6d, 0x60, 0xb8, 0x9e);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_VP9_10BIT_PROFILE2, 0xa4c749ef, 0x6ecf, 0x48aa, 0x84, 0x48, 0x50, 0xa7, 0xa1, 0x16, 0x5f, 0xf7);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_VP8, 0x90b899ea, 0x3a62, 0x4705, 0x88, 0xb3, 0x8d, 0xf0, 0x4b, 0x27, 0x44, 0xe7);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_AV1_PROFILE0, 0xb8be4ccb, 0xcf53, 0x46ba, 0x8d, 0x59, 0xd6, 0xb8, 0xa6, 0xda, 0x5d, 0x2a);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_AV1_PROFILE1, 0x6936ff0f, 0x45b1, 0x4163, 0x9c, 0xc1, 0x64, 0x6e, 0xf6, 0x94, 0x61, 0x08);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_AV1_PROFILE2, 0x0c5f2aa1, 0xe541, 0x4089, 0xbb, 0x7b, 0x98, 0x11, 0x0a, 0x19, 0xd7, 0xc8);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_AV1_12BIT_PROFILE2, 0x17127009, 0xa00f, 0x4ce1, 0x99, 0x4e, 0xbf, 0x40, 0x81, 0xf6, 0xf3, 0xf0);
DEFINE_GUID(D3D12_VIDEO_DECODE_PROFILE_AV1_12BIT_PROFILE2_420, 0x2d80bed6, 0x9cac, 0x4835, 0x9e, 0x91, 0x32, 0x7b, 0xbc, 0x4f, 0x9e, 0xe8);
typedef enum D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE {
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE_ABSOLUTE_QP_MAP = 0,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE_CQP = 1,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE_CBR = 2,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE_VBR = 3,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE_QVBR = 4
} D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE;
typedef enum D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAGS {
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_DELTA_QP = 0x1,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_FRAME_ANALYSIS = 0x2,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_QP_RANGE = 0x4,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_INITIAL_QP = 0x8,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_MAX_FRAME_SIZE = 0x10,
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAG_ENABLE_VBV_SIZES = 0x20
} D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAGS);
typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL_CQP {
    UINT ConstantQP_FullIntracodedFrame;
    UINT ConstantQP_InterPredictedFrame_PrevRefOnly;
    UINT ConstantQP_InterPredictedFrame_BiDirectionalRef;
} D3D12_VIDEO_ENCODER_RATE_CONTROL_CQP;
typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL_CBR {
    UINT InitialQP;
    UINT MinQP;
    UINT MaxQP;
    UINT64 MaxFrameBitSize;
    UINT64 TargetBitRate;
    UINT64 VBVCapacity;
    UINT64 InitialVBVFullness;
} D3D12_VIDEO_ENCODER_RATE_CONTROL_CBR;
typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL_VBR {
    UINT InitialQP;
    UINT MinQP;
    UINT MaxQP;
    UINT64 MaxFrameBitSize;
    UINT64 TargetAvgBitRate;
    UINT64 PeakBitRate;
    UINT64 VBVCapacity;
    UINT64 InitialVBVFullness;
} D3D12_VIDEO_ENCODER_RATE_CONTROL_VBR;
typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL_QVBR {
    UINT InitialQP;
    UINT MinQP;
    UINT MaxQP;
    UINT64 MaxFrameBitSize;
    UINT64 TargetAvgBitRate;
    UINT64 PeakBitRate;
    UINT ConstantQualityTarget;
} D3D12_VIDEO_ENCODER_RATE_CONTROL_QVBR;
typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL_CONFIGURATION_PARAMS {
    UINT DataSize;
    __C89_NAMELESS union {
        const D3D12_VIDEO_ENCODER_RATE_CONTROL_CQP *pConfiguration_CQP;
        const D3D12_VIDEO_ENCODER_RATE_CONTROL_CBR *pConfiguration_CBR;
        const D3D12_VIDEO_ENCODER_RATE_CONTROL_VBR *pConfiguration_VBR;
        const D3D12_VIDEO_ENCODER_RATE_CONTROL_QVBR *pConfiguration_QVBR;
    } __C89_NAMELESSUNIONNAME;
} D3D12_VIDEO_ENCODER_RATE_CONTROL_CONFIGURATION_PARAMS;
typedef struct D3D12_VIDEO_ENCODER_RATE_CONTROL {
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE Mode;
    D3D12_VIDEO_ENCODER_RATE_CONTROL_FLAGS Flags;
    D3D12_VIDEO_ENCODER_RATE_CONTROL_CONFIGURATION_PARAMS ConfigParams;
    DXGI_RATIONAL TargetFrameRate;
} D3D12_VIDEO_ENCODER_RATE_CONTROL;
typedef enum D3D12_VIDEO_ENCODER_CODEC {
    D3D12_VIDEO_ENCODER_CODEC_H264 = 0,
    D3D12_VIDEO_ENCODER_CODEC_HEVC = 1
} D3D12_VIDEO_ENCODER_CODEC;
typedef enum D3D12_VIDEO_ENCODER_PROFILE_H264 {
    D3D12_VIDEO_ENCODER_PROFILE_H264_MAIN = 0,
    D3D12_VIDEO_ENCODER_PROFILE_H264_HIGH = 1,
    D3D12_VIDEO_ENCODER_PROFILE_H264_HIGH_10 = 2
} D3D12_VIDEO_ENCODER_PROFILE_H264;
typedef enum D3D12_VIDEO_ENCODER_PROFILE_HEVC {
    D3D12_VIDEO_ENCODER_PROFILE_HEVC_MAIN = 0,
    D3D12_VIDEO_ENCODER_PROFILE_HEVC_MAIN10 = 1
} D3D12_VIDEO_ENCODER_PROFILE_HEVC;
typedef struct D3D12_VIDEO_ENCODER_PROFILE_DESC {
    UINT DataSize;
    __C89_NAMELESS union {
        D3D12_VIDEO_ENCODER_PROFILE_H264 *pH264Profile;
        D3D12_VIDEO_ENCODER_PROFILE_HEVC *pHEVCProfile;
    } __C89_NAMELESSUNIONNAME;
} D3D12_VIDEO_ENCODER_PROFILE_DESC;
typedef enum D3D12_VIDEO_ENCODER_LEVELS_H264 {
    D3D12_VIDEO_ENCODER_LEVELS_H264_1 = 0,
    D3D12_VIDEO_ENCODER_LEVELS_H264_1b = 1,
    D3D12_VIDEO_ENCODER_LEVELS_H264_11 = 2,
    D3D12_VIDEO_ENCODER_LEVELS_H264_12 = 3,
    D3D12_VIDEO_ENCODER_LEVELS_H264_13 = 4,
    D3D12_VIDEO_ENCODER_LEVELS_H264_2 = 5,
    D3D12_VIDEO_ENCODER_LEVELS_H264_21 = 6,
    D3D12_VIDEO_ENCODER_LEVELS_H264_22 = 7,
    D3D12_VIDEO_ENCODER_LEVELS_H264_3 = 8,
    D3D12_VIDEO_ENCODER_LEVELS_H264_31 = 9,
    D3D12_VIDEO_ENCODER_LEVELS_H264_32 = 10,
    D3D12_VIDEO_ENCODER_LEVELS_H264_4 = 11,
    D3D12_VIDEO_ENCODER_LEVELS_H264_41 = 12,
    D3D12_VIDEO_ENCODER_LEVELS_H264_42 = 13,
    D3D12_VIDEO_ENCODER_LEVELS_H264_5 = 14,
    D3D12_VIDEO_ENCODER_LEVELS_H264_51 = 15,
    D3D12_VIDEO_ENCODER_LEVELS_H264_52 = 16,
    D3D12_VIDEO_ENCODER_LEVELS_H264_6 = 17,
    D3D12_VIDEO_ENCODER_LEVELS_H264_61 = 18,
    D3D12_VIDEO_ENCODER_LEVELS_H264_62 = 19
} D3D12_VIDEO_ENCODER_LEVELS_H264;
typedef enum D3D12_VIDEO_ENCODER_TIER_HEVC {
    D3D12_VIDEO_ENCODER_TIER_HEVC_MAIN = 0,
    D3D12_VIDEO_ENCODER_TIER_HEVC_HIGH = 1
} D3D12_VIDEO_ENCODER_TIER_HEVC;
typedef enum D3D12_VIDEO_ENCODER_LEVELS_HEVC {
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_1 = 0,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_2 = 1,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_21 = 2,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_3 = 3,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_31 = 4,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_4 = 5,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_41 = 6,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_5 = 7,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_51 = 8,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_52 = 9,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_6 = 10,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_61 = 11,
    D3D12_VIDEO_ENCODER_LEVELS_HEVC_62 = 12
} D3D12_VIDEO_ENCODER_LEVELS_HEVC;
typedef struct D3D12_VIDEO_ENCODER_LEVEL_TIER_CONSTRAINTS_HEVC {
    D3D12_VIDEO_ENCODER_LEVELS_HEVC Level;
    D3D12_VIDEO_ENCODER_TIER_HEVC Tier;
} D3D12_VIDEO_ENCODER_LEVEL_TIER_CONSTRAINTS_HEVC;
typedef struct D3D12_VIDEO_ENCODER_LEVEL_SETTING {
    UINT DataSize;
    __C89_NAMELESS union {
        D3D12_VIDEO_ENCODER_LEVELS_H264 *pH264LevelSetting;
        D3D12_VIDEO_ENCODER_LEVEL_TIER_CONSTRAINTS_HEVC *pHEVCLevelSetting;
    } __C89_NAMELESSUNIONNAME;
} D3D12_VIDEO_ENCODER_LEVEL_SETTING;
typedef struct D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC {
    UINT Width;
    UINT Height;
} D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC;
typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_RATE_CONTROL_MODE {
    UINT NodeIndex;
    D3D12_VIDEO_ENCODER_CODEC Codec;
    D3D12_VIDEO_ENCODER_RATE_CONTROL_MODE RateControlMode;
    WINBOOL IsSupported;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_RATE_CONTROL_MODE;
typedef enum D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE {
    D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE_NONE = 0,
    D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE_ROW_BASED = 1
} D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE;
typedef enum D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE {
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE_FULL_FRAME = 0,
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE_BYTES_PER_SUBREGION = 1,
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE_SQUARE_UNITS_PER_SUBREGION_ROW_UNALIGNED = 2,
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE_UNIFORM_PARTITIONING_ROWS_PER_SUBREGION = 3,
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE_UNIFORM_PARTITIONING_SUBREGIONS_PER_FRAME = 4
} D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE;
typedef enum D3D12_VIDEO_ENCODER_HEAP_FLAGS {
    D3D12_VIDEO_ENCODER_HEAP_FLAG_NONE = 0x0
} D3D12_VIDEO_ENCODER_HEAP_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_HEAP_FLAGS);
typedef struct D3D12_VIDEO_ENCODER_HEAP_DESC {
    UINT NodeMask;
    D3D12_VIDEO_ENCODER_HEAP_FLAGS Flags;
    D3D12_VIDEO_ENCODER_CODEC EncodeCodec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC EncodeProfile;
    D3D12_VIDEO_ENCODER_LEVEL_SETTING EncodeLevel;
    UINT ResolutionsListCount;
    const D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC *pResolutionList;
} D3D12_VIDEO_ENCODER_HEAP_DESC;
typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAGS {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_CABAC_ENCODING_SUPPORT = 0x1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_INTRA_SLICE_CONSTRAINED_ENCODING_SUPPORT = 0x2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_BFRAME_LTR_COMBINED_SUPPORT = 0x4,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_ADAPTIVE_8x8_TRANSFORM_ENCODING_SUPPORT = 0x8,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_DIRECT_SPATIAL_ENCODING_SUPPORT = 0x10,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_DIRECT_TEMPORAL_ENCODING_SUPPORT = 0x20,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAG_CONSTRAINED_INTRAPREDICTION_SUPPORT = 0x40
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAGS);
typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODES {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_0_ALL_LUMA_CHROMA_SLICE_BLOCK_EDGES_ALWAYS_FILTERED = 0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_1_DISABLE_ALL_SLICE_BLOCK_EDGES = 1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_2_DISABLE_SLICE_BOUNDARIES_BLOCKS = 2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_3_USE_TWO_STAGE_DEBLOCKING = 3,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_4_DISABLE_CHROMA_BLOCK_EDGES = 4,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_5_DISABLE_CHROMA_BLOCK_EDGES_AND_LUMA_BOUNDARIES = 5,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_6_DISABLE_CHROMA_BLOCK_EDGES_AND_USE_LUMA_TWO_STAGE_DEBLOCKING = 6
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODES;
typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAGS {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_0_ALL_LUMA_CHROMA_SLICE_BLOCK_EDGES_ALWAYS_FILTERED = 1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_0_ALL_LUMA_CHROMA_SLICE_BLOCK_EDGES_ALWAYS_FILTERED,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_1_DISABLE_ALL_SLICE_BLOCK_EDGES = 1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_1_DISABLE_ALL_SLICE_BLOCK_EDGES,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_2_DISABLE_SLICE_BOUNDARIES_BLOCKS = 1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_2_DISABLE_SLICE_BOUNDARIES_BLOCKS,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_3_USE_TWO_STAGE_DEBLOCKING = 1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_3_USE_TWO_STAGE_DEBLOCKING,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_4_DISABLE_CHROMA_BLOCK_EDGES = 1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_4_DISABLE_CHROMA_BLOCK_EDGES,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_5_DISABLE_CHROMA_BLOCK_EDGES_AND_LUMA_BOUNDARIES = 1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_5_DISABLE_CHROMA_BLOCK_EDGES_AND_LUMA_BOUNDARIES,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAG_6_DISABLE_CHROMA_BLOCK_EDGES_AND_USE_LUMA_TWO_STAGE_DEBLOCKING = 1 << D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_6_DISABLE_CHROMA_BLOCK_EDGES_AND_USE_LUMA_TWO_STAGE_DEBLOCKING
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAGS);
typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264 {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264_FLAGS SupportFlags;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODE_FLAGS DisableDeblockingFilterSupportedModes;
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264;
typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAGS {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_BFRAME_LTR_COMBINED_SUPPORT = 0x1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_INTRA_SLICE_CONSTRAINED_ENCODING_SUPPORT = 0x2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_CONSTRAINED_INTRAPREDICTION_SUPPORT = 0x4,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_SAO_FILTER_SUPPORT = 0x8,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_ASYMETRIC_MOTION_PARTITION_SUPPORT = 0x10,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_ASYMETRIC_MOTION_PARTITION_REQUIRED = 0x20,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_TRANSFORM_SKIP_SUPPORT = 0x40,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_DISABLING_LOOP_FILTER_ACROSS_SLICES_SUPPORT = 0x80,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAG_P_FRAMES_IMPLEMENTED_AS_LOW_DELAY_B_FRAMES = 0x100
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAGS);
typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE_8x8 = 0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE_16x16 = 1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE_32x32 = 2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE_64x64 = 3
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE;
typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE_4x4 = 0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE_8x8 = 1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE_16x16 = 2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE_32x32 = 3
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE;
typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC_FLAGS SupportFlags;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE MinLumaCodingUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE MaxLumaCodingUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE MinLumaTransformUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE MaxLumaTransformUnitSize;
    UCHAR max_transform_hierarchy_depth_inter;
    UCHAR max_transform_hierarchy_depth_intra;
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC;
typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT {
    UINT DataSize;
    __C89_NAMELESS union {
        D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_H264 *pH264Support;
        D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT_HEVC *pHEVCSupport;
    } __C89_NAMELESSUNIONNAME;
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT;
typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT {
    UINT NodeIndex;
    D3D12_VIDEO_ENCODER_CODEC Codec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC Profile;
    WINBOOL IsSupported;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT CodecSupportLimits;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_CODEC_CONFIGURATION_SUPPORT;
typedef struct D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_H264 {
    UINT MaxL0ReferencesForP;
    UINT MaxL0ReferencesForB;
    UINT MaxL1ReferencesForB;
    UINT MaxLongTermReferences;
    UINT MaxDPBCapacity;
} D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_H264;
typedef struct D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_HEVC {
    UINT MaxL0ReferencesForP;
    UINT MaxL0ReferencesForB;
    UINT MaxL1ReferencesForB;
    UINT MaxLongTermReferences;
    UINT MaxDPBCapacity;
} D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_HEVC;
typedef struct D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT {
    UINT DataSize;
    __C89_NAMELESS union {
        D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_H264 *pH264Support;
        D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT_HEVC *pHEVCSupport;
    } __C89_NAMELESSUNIONNAME;
} D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT;
typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT {
    UINT NodeIndex;
    D3D12_VIDEO_ENCODER_CODEC Codec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC Profile;
    WINBOOL IsSupported;
    D3D12_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT PictureSupport;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_CODEC_PICTURE_CONTROL_SUPPORT;
typedef enum D3D12_VIDEO_ENCODER_SUPPORT_FLAGS {
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_GENERAL_SUPPORT_OK = 0x1,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_RECONFIGURATION_AVAILABLE = 0x2,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RESOLUTION_RECONFIGURATION_AVAILABLE = 0x4,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_VBV_SIZE_CONFIG_AVAILABLE = 0x8,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_FRAME_ANALYSIS_AVAILABLE = 0x10,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RECONSTRUCTED_FRAMES_REQUIRE_TEXTURE_ARRAYS = 0x20,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_DELTA_QP_AVAILABLE = 0x40,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_SUBREGION_LAYOUT_RECONFIGURATION_AVAILABLE = 0x80,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_ADJUSTABLE_QP_RANGE_AVAILABLE = 0x100,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_INITIAL_QP_AVAILABLE = 0x200,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_RATE_CONTROL_MAX_FRAME_SIZE_AVAILABLE = 0x400,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_SEQUENCE_GOP_RECONFIGURATION_AVAILABLE = 0x800,
    D3D12_VIDEO_ENCODER_SUPPORT_FLAG_MOTION_ESTIMATION_PRECISION_MODE_LIMIT_AVAILABLE = 0x1000
} D3D12_VIDEO_ENCODER_SUPPORT_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_SUPPORT_FLAGS);
typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAGS {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAG_USE_CONSTRAINED_INTRAPREDICTION = 0x1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAG_USE_ADAPTIVE_8x8_TRANSFORM = 0x2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAG_ENABLE_CABAC_ENCODING = 0x4,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAG_ALLOW_REQUEST_INTRA_CONSTRAINED_SLICES = 0x8
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAGS);
typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES_DISABLED = 0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES_TEMPORAL = 1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES_SPATIAL = 2
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES;
typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264 {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_FLAGS ConfigurationFlags;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_DIRECT_MODES DirectModeConfig;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264_SLICES_DEBLOCKING_MODES DisableDeblockingFilterConfig;
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264;
typedef enum D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAGS {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_DISABLE_LOOP_FILTER_ACROSS_SLICES = 0x1,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_ALLOW_REQUEST_INTRA_CONSTRAINED_SLICES = 0x2,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_ENABLE_SAO_FILTER = 0x4,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_ENABLE_LONG_TERM_REFERENCES = 0x8,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_USE_ASYMETRIC_MOTION_PARTITION = 0x10,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_ENABLE_TRANSFORM_SKIPPING = 0x20,
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAG_USE_CONSTRAINED_INTRAPREDICTION = 0x40
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAGS);
typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC {
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_FLAGS ConfigurationFlags;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE MinLumaCodingUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_CUSIZE MaxLumaCodingUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE MinLumaTransformUnitSize;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC_TUSIZE MaxLumaTransformUnitSize;
    UCHAR max_transform_hierarchy_depth_inter;
    UCHAR max_transform_hierarchy_depth_intra;
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC;
typedef struct D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION {
    UINT DataSize;
    __C89_NAMELESS union {
        D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_H264 *pH264Config;
        D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION_HEVC *pHEVCConfig;
    } __C89_NAMELESSUNIONNAME;
} D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION;
typedef struct D3D12_VIDEO_ENCODER_INTRA_REFRESH {
    D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE Mode;
    UINT IntraRefreshDuration;
} D3D12_VIDEO_ENCODER_INTRA_REFRESH;
typedef enum D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE {
    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE_MAXIMUM = 0,
    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE_FULL_PIXEL = 1,
    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE_HALF_PIXEL = 2,
    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE_QUARTER_PIXEL = 3
} D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE;
typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_RESOLUTION_SUPPORT_LIMITS {
    UINT MaxSubregionsNumber;
    UINT MaxIntraRefreshFrameDuration;
    UINT SubregionBlockPixelsSize;
    UINT QPMapRegionPixelsSize;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_RESOLUTION_SUPPORT_LIMITS;
typedef enum D3D12_VIDEO_ENCODER_VALIDATION_FLAGS {
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_CODEC_NOT_SUPPORTED = 0x1,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_INPUT_FORMAT_NOT_SUPPORTED = 0x8,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_CODEC_CONFIGURATION_NOT_SUPPORTED = 0x10,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_RATE_CONTROL_MODE_NOT_SUPPORTED = 0x20,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_RATE_CONTROL_CONFIGURATION_NOT_SUPPORTED = 0x40,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_INTRA_REFRESH_MODE_NOT_SUPPORTED = 0x80,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_SUBREGION_LAYOUT_MODE_NOT_SUPPORTED = 0x100,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_RESOLUTION_NOT_SUPPORTED_IN_LIST = 0x200,
    D3D12_VIDEO_ENCODER_VALIDATION_FLAG_GOP_STRUCTURE_NOT_SUPPORTED = 0x800
} D3D12_VIDEO_ENCODER_VALIDATION_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_VALIDATION_FLAGS);
typedef struct D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_H264 {
    UINT GOPLength;
    UINT PPicturePeriod;
    UCHAR pic_order_cnt_type;
    UCHAR log2_max_frame_num_minus4;
    UCHAR log2_max_pic_order_cnt_lsb_minus4;
} D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_H264;
typedef struct D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_HEVC {
    UINT GOPLength;
    UINT PPicturePeriod;
    UCHAR log2_max_pic_order_cnt_lsb_minus4;
} D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_HEVC;
typedef struct D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE {
    UINT DataSize;
    __C89_NAMELESS union {
        D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_H264 *pH264GroupOfPictures;
        D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE_HEVC *pHEVCGroupOfPictures;
    } __C89_NAMELESSUNIONNAME;
} D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE;
typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_SUPPORT {
    UINT NodeIndex;
    D3D12_VIDEO_ENCODER_CODEC Codec;
    DXGI_FORMAT InputFormat;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION CodecConfiguration;
    D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE CodecGopSequence;
    D3D12_VIDEO_ENCODER_RATE_CONTROL RateControl;
    D3D12_VIDEO_ENCODER_INTRA_REFRESH_MODE IntraRefresh;
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE SubregionFrameEncoding;
    UINT ResolutionsListCount;
    const D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC *pResolutionList;
    UINT MaxReferenceFramesInDPB;
    D3D12_VIDEO_ENCODER_VALIDATION_FLAGS ValidationFlags;
    D3D12_VIDEO_ENCODER_SUPPORT_FLAGS SupportFlags;
    D3D12_VIDEO_ENCODER_PROFILE_DESC SuggestedProfile;
    D3D12_VIDEO_ENCODER_LEVEL_SETTING SuggestedLevel;
    D3D12_FEATURE_DATA_VIDEO_ENCODER_RESOLUTION_SUPPORT_LIMITS *pResolutionDependentSupport;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_SUPPORT;
typedef struct D3D12_FEATURE_DATA_VIDEO_ENCODER_RESOURCE_REQUIREMENTS {
    UINT NodeIndex;
    D3D12_VIDEO_ENCODER_CODEC Codec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC Profile;
    DXGI_FORMAT InputFormat;
    D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC PictureTargetResolution;
    WINBOOL IsSupported;
    UINT CompressedBitstreamBufferAccessAlignment;
    UINT EncoderMetadataBufferAccessAlignment;
    UINT MaxEncoderOutputMetadataBufferSize;
} D3D12_FEATURE_DATA_VIDEO_ENCODER_RESOURCE_REQUIREMENTS;
typedef enum D3D12_VIDEO_ENCODER_FLAGS {
    D3D12_VIDEO_ENCODER_FLAG_NONE = 0x0
} D3D12_VIDEO_ENCODER_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_FLAGS);
typedef struct D3D12_VIDEO_ENCODER_DESC {
    UINT NodeMask;
    D3D12_VIDEO_ENCODER_FLAGS Flags;
    D3D12_VIDEO_ENCODER_CODEC EncodeCodec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC EncodeProfile;
    DXGI_FORMAT InputFormat;
    D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION CodecConfiguration;
    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE MaxMotionEstimationPrecision;
} D3D12_VIDEO_ENCODER_DESC;
/*****************************************************************************
 * ID3D12VideoEncoder interface
 */
#ifndef __ID3D12VideoEncoder_INTERFACE_DEFINED__
#define __ID3D12VideoEncoder_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoEncoder, 0x2e0d212d, 0x8df9, 0x44a6, 0xa7,0x70, 0xbb,0x28,0x9b,0x18,0x27,0x37);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("2e0d212d-8df9-44a6-a770-bb289b182737")
ID3D12VideoEncoder : public ID3D12Pageable
{
    virtual UINT STDMETHODCALLTYPE GetNodeMask(
        ) = 0;

    virtual D3D12_VIDEO_ENCODER_FLAGS STDMETHODCALLTYPE GetEncoderFlags(
        ) = 0;

    virtual D3D12_VIDEO_ENCODER_CODEC STDMETHODCALLTYPE GetCodec(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodecProfile(
        D3D12_VIDEO_ENCODER_PROFILE_DESC dst_profile) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodecConfiguration(
        D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION dst_codec_config) = 0;

    virtual DXGI_FORMAT STDMETHODCALLTYPE GetInputFormat(
        ) = 0;

    virtual D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE STDMETHODCALLTYPE GetMaxMotionEstimationPrecision(
        ) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoEncoder, 0x2e0d212d, 0x8df9, 0x44a6, 0xa7,0x70, 0xbb,0x28,0x9b,0x18,0x27,0x37)
#endif
#else
typedef struct ID3D12VideoEncoderVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoEncoder *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoEncoder *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoEncoder *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoEncoder *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoEncoder *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoEncoder *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoEncoder *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoEncoder *This,
        REFIID riid,
        void **device);

    /*** ID3D12VideoEncoder methods ***/
    UINT (STDMETHODCALLTYPE *GetNodeMask)(
        ID3D12VideoEncoder *This);

    D3D12_VIDEO_ENCODER_FLAGS (STDMETHODCALLTYPE *GetEncoderFlags)(
        ID3D12VideoEncoder *This);

    D3D12_VIDEO_ENCODER_CODEC (STDMETHODCALLTYPE *GetCodec)(
        ID3D12VideoEncoder *This);

    HRESULT (STDMETHODCALLTYPE *GetCodecProfile)(
        ID3D12VideoEncoder *This,
        D3D12_VIDEO_ENCODER_PROFILE_DESC dst_profile);

    HRESULT (STDMETHODCALLTYPE *GetCodecConfiguration)(
        ID3D12VideoEncoder *This,
        D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION dst_codec_config);

    DXGI_FORMAT (STDMETHODCALLTYPE *GetInputFormat)(
        ID3D12VideoEncoder *This);

    D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE (STDMETHODCALLTYPE *GetMaxMotionEstimationPrecision)(
        ID3D12VideoEncoder *This);

    END_INTERFACE
} ID3D12VideoEncoderVtbl;

interface ID3D12VideoEncoder {
    CONST_VTBL ID3D12VideoEncoderVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoEncoder_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoEncoder_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoEncoder_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoEncoder_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoEncoder_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoEncoder_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoEncoder_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoEncoder_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12VideoEncoder methods ***/
#define ID3D12VideoEncoder_GetNodeMask(This) (This)->lpVtbl->GetNodeMask(This)
#define ID3D12VideoEncoder_GetEncoderFlags(This) (This)->lpVtbl->GetEncoderFlags(This)
#define ID3D12VideoEncoder_GetCodec(This) (This)->lpVtbl->GetCodec(This)
#define ID3D12VideoEncoder_GetCodecProfile(This,dst_profile) (This)->lpVtbl->GetCodecProfile(This,dst_profile)
#define ID3D12VideoEncoder_GetCodecConfiguration(This,dst_codec_config) (This)->lpVtbl->GetCodecConfiguration(This,dst_codec_config)
#define ID3D12VideoEncoder_GetInputFormat(This) (This)->lpVtbl->GetInputFormat(This)
#define ID3D12VideoEncoder_GetMaxMotionEstimationPrecision(This) (This)->lpVtbl->GetMaxMotionEstimationPrecision(This)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoEncoder_QueryInterface(ID3D12VideoEncoder* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoEncoder_AddRef(ID3D12VideoEncoder* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoEncoder_Release(ID3D12VideoEncoder* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoEncoder_GetPrivateData(ID3D12VideoEncoder* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoEncoder_SetPrivateData(ID3D12VideoEncoder* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoEncoder_SetPrivateDataInterface(ID3D12VideoEncoder* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoEncoder_SetName(ID3D12VideoEncoder* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoEncoder_GetDevice(ID3D12VideoEncoder* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12VideoEncoder methods ***/
static inline UINT ID3D12VideoEncoder_GetNodeMask(ID3D12VideoEncoder* This) {
    return This->lpVtbl->GetNodeMask(This);
}
static inline D3D12_VIDEO_ENCODER_FLAGS ID3D12VideoEncoder_GetEncoderFlags(ID3D12VideoEncoder* This) {
    return This->lpVtbl->GetEncoderFlags(This);
}
static inline D3D12_VIDEO_ENCODER_CODEC ID3D12VideoEncoder_GetCodec(ID3D12VideoEncoder* This) {
    return This->lpVtbl->GetCodec(This);
}
static inline HRESULT ID3D12VideoEncoder_GetCodecProfile(ID3D12VideoEncoder* This,D3D12_VIDEO_ENCODER_PROFILE_DESC dst_profile) {
    return This->lpVtbl->GetCodecProfile(This,dst_profile);
}
static inline HRESULT ID3D12VideoEncoder_GetCodecConfiguration(ID3D12VideoEncoder* This,D3D12_VIDEO_ENCODER_CODEC_CONFIGURATION dst_codec_config) {
    return This->lpVtbl->GetCodecConfiguration(This,dst_codec_config);
}
static inline DXGI_FORMAT ID3D12VideoEncoder_GetInputFormat(ID3D12VideoEncoder* This) {
    return This->lpVtbl->GetInputFormat(This);
}
static inline D3D12_VIDEO_ENCODER_MOTION_ESTIMATION_PRECISION_MODE ID3D12VideoEncoder_GetMaxMotionEstimationPrecision(ID3D12VideoEncoder* This) {
    return This->lpVtbl->GetMaxMotionEstimationPrecision(This);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoEncoder_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12VideoEncoderHeap interface
 */
#ifndef __ID3D12VideoEncoderHeap_INTERFACE_DEFINED__
#define __ID3D12VideoEncoderHeap_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoEncoderHeap, 0x22b35d96, 0x876a, 0x44c0, 0xb2,0x5e, 0xfb,0x8c,0x9c,0x7f,0x1c,0x4a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("22b35d96-876a-44c0-b25e-fb8c9c7f1c4a")
ID3D12VideoEncoderHeap : public ID3D12Pageable
{
    virtual UINT STDMETHODCALLTYPE GetNodeMask(
        ) = 0;

    virtual D3D12_VIDEO_ENCODER_HEAP_FLAGS STDMETHODCALLTYPE GetEncoderHeapFlags(
        ) = 0;

    virtual D3D12_VIDEO_ENCODER_CODEC STDMETHODCALLTYPE GetCodec(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodecProfile(
        D3D12_VIDEO_ENCODER_PROFILE_DESC dst_profile) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetCodecLevel(
        D3D12_VIDEO_ENCODER_LEVEL_SETTING dst_level) = 0;

    virtual UINT STDMETHODCALLTYPE GetResolutionListCount(
        ) = 0;

    virtual HRESULT STDMETHODCALLTYPE GetResolutionList(
        const UINT resolutions_list_count,
        D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC *resolution_list) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoEncoderHeap, 0x22b35d96, 0x876a, 0x44c0, 0xb2,0x5e, 0xfb,0x8c,0x9c,0x7f,0x1c,0x4a)
#endif
#else
typedef struct ID3D12VideoEncoderHeapVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoEncoderHeap *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoEncoderHeap *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoEncoderHeap *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoEncoderHeap *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoEncoderHeap *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoEncoderHeap *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoEncoderHeap *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoEncoderHeap *This,
        REFIID riid,
        void **device);

    /*** ID3D12VideoEncoderHeap methods ***/
    UINT (STDMETHODCALLTYPE *GetNodeMask)(
        ID3D12VideoEncoderHeap *This);

    D3D12_VIDEO_ENCODER_HEAP_FLAGS (STDMETHODCALLTYPE *GetEncoderHeapFlags)(
        ID3D12VideoEncoderHeap *This);

    D3D12_VIDEO_ENCODER_CODEC (STDMETHODCALLTYPE *GetCodec)(
        ID3D12VideoEncoderHeap *This);

    HRESULT (STDMETHODCALLTYPE *GetCodecProfile)(
        ID3D12VideoEncoderHeap *This,
        D3D12_VIDEO_ENCODER_PROFILE_DESC dst_profile);

    HRESULT (STDMETHODCALLTYPE *GetCodecLevel)(
        ID3D12VideoEncoderHeap *This,
        D3D12_VIDEO_ENCODER_LEVEL_SETTING dst_level);

    UINT (STDMETHODCALLTYPE *GetResolutionListCount)(
        ID3D12VideoEncoderHeap *This);

    HRESULT (STDMETHODCALLTYPE *GetResolutionList)(
        ID3D12VideoEncoderHeap *This,
        const UINT resolutions_list_count,
        D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC *resolution_list);

    END_INTERFACE
} ID3D12VideoEncoderHeapVtbl;

interface ID3D12VideoEncoderHeap {
    CONST_VTBL ID3D12VideoEncoderHeapVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoEncoderHeap_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoEncoderHeap_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoEncoderHeap_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoEncoderHeap_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoEncoderHeap_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoEncoderHeap_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoEncoderHeap_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoEncoderHeap_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12VideoEncoderHeap methods ***/
#define ID3D12VideoEncoderHeap_GetNodeMask(This) (This)->lpVtbl->GetNodeMask(This)
#define ID3D12VideoEncoderHeap_GetEncoderHeapFlags(This) (This)->lpVtbl->GetEncoderHeapFlags(This)
#define ID3D12VideoEncoderHeap_GetCodec(This) (This)->lpVtbl->GetCodec(This)
#define ID3D12VideoEncoderHeap_GetCodecProfile(This,dst_profile) (This)->lpVtbl->GetCodecProfile(This,dst_profile)
#define ID3D12VideoEncoderHeap_GetCodecLevel(This,dst_level) (This)->lpVtbl->GetCodecLevel(This,dst_level)
#define ID3D12VideoEncoderHeap_GetResolutionListCount(This) (This)->lpVtbl->GetResolutionListCount(This)
#define ID3D12VideoEncoderHeap_GetResolutionList(This,resolutions_list_count,resolution_list) (This)->lpVtbl->GetResolutionList(This,resolutions_list_count,resolution_list)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoEncoderHeap_QueryInterface(ID3D12VideoEncoderHeap* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoEncoderHeap_AddRef(ID3D12VideoEncoderHeap* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoEncoderHeap_Release(ID3D12VideoEncoderHeap* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoEncoderHeap_GetPrivateData(ID3D12VideoEncoderHeap* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoEncoderHeap_SetPrivateData(ID3D12VideoEncoderHeap* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoEncoderHeap_SetPrivateDataInterface(ID3D12VideoEncoderHeap* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoEncoderHeap_SetName(ID3D12VideoEncoderHeap* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoEncoderHeap_GetDevice(ID3D12VideoEncoderHeap* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12VideoEncoderHeap methods ***/
static inline UINT ID3D12VideoEncoderHeap_GetNodeMask(ID3D12VideoEncoderHeap* This) {
    return This->lpVtbl->GetNodeMask(This);
}
static inline D3D12_VIDEO_ENCODER_HEAP_FLAGS ID3D12VideoEncoderHeap_GetEncoderHeapFlags(ID3D12VideoEncoderHeap* This) {
    return This->lpVtbl->GetEncoderHeapFlags(This);
}
static inline D3D12_VIDEO_ENCODER_CODEC ID3D12VideoEncoderHeap_GetCodec(ID3D12VideoEncoderHeap* This) {
    return This->lpVtbl->GetCodec(This);
}
static inline HRESULT ID3D12VideoEncoderHeap_GetCodecProfile(ID3D12VideoEncoderHeap* This,D3D12_VIDEO_ENCODER_PROFILE_DESC dst_profile) {
    return This->lpVtbl->GetCodecProfile(This,dst_profile);
}
static inline HRESULT ID3D12VideoEncoderHeap_GetCodecLevel(ID3D12VideoEncoderHeap* This,D3D12_VIDEO_ENCODER_LEVEL_SETTING dst_level) {
    return This->lpVtbl->GetCodecLevel(This,dst_level);
}
static inline UINT ID3D12VideoEncoderHeap_GetResolutionListCount(ID3D12VideoEncoderHeap* This) {
    return This->lpVtbl->GetResolutionListCount(This);
}
static inline HRESULT ID3D12VideoEncoderHeap_GetResolutionList(ID3D12VideoEncoderHeap* This,const UINT resolutions_list_count,D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC *resolution_list) {
    return This->lpVtbl->GetResolutionList(This,resolutions_list_count,resolution_list);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoEncoderHeap_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ID3D12VideoDevice3 interface
 */
#ifndef __ID3D12VideoDevice3_INTERFACE_DEFINED__
#define __ID3D12VideoDevice3_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoDevice3, 0x4243adb4, 0x3a32, 0x4666, 0x97,0x3c, 0x0c,0xcc,0x56,0x25,0xdc,0x44);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("4243adb4-3a32-4666-973c-0ccc5625dc44")
ID3D12VideoDevice3 : public ID3D12VideoDevice2
{
    virtual HRESULT STDMETHODCALLTYPE CreateVideoEncoder(
        const D3D12_VIDEO_ENCODER_DESC *desc,
        REFIID riid,
        void **video_encoder) = 0;

    virtual HRESULT STDMETHODCALLTYPE CreateVideoEncoderHeap(
        const D3D12_VIDEO_ENCODER_HEAP_DESC *desc,
        REFIID riid,
        void **video_encoder_heap) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoDevice3, 0x4243adb4, 0x3a32, 0x4666, 0x97,0x3c, 0x0c,0xcc,0x56,0x25,0xdc,0x44)
#endif
#else
typedef struct ID3D12VideoDevice3Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoDevice3 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoDevice3 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoDevice3 *This);

    /*** ID3D12VideoDevice methods ***/
    HRESULT (STDMETHODCALLTYPE *CheckFeatureSupport)(
        ID3D12VideoDevice3 *This,
        D3D12_FEATURE_VIDEO feature_video,
        void *feature_support_data,
        UINT feature_support_data_size);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoder)(
        ID3D12VideoDevice3 *This,
        const D3D12_VIDEO_DECODER_DESC *desc,
        REFIID riid,
        void **video_decoder);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoderHeap)(
        ID3D12VideoDevice3 *This,
        const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,
        REFIID riid,
        void **video_decoder_heap);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessor)(
        ID3D12VideoDevice3 *This,
        UINT node_mask,
        const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,
        UINT input_stream_descs_count,
        const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,
        REFIID riid,
        void **video_processor);

    /*** ID3D12VideoDevice1 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateVideoMotionEstimator)(
        ID3D12VideoDevice3 *This,
        const D3D12_VIDEO_MOTION_ESTIMATOR_DESC *desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_motion_estimator);

    HRESULT (STDMETHODCALLTYPE *CreateVideoMotionVectorHeap)(
        ID3D12VideoDevice3 *This,
        const D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC *desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_motion_vector_heap);

    /*** ID3D12VideoDevice2 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoder1)(
        ID3D12VideoDevice3 *This,
        const D3D12_VIDEO_DECODER_DESC *desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_decoder);

    HRESULT (STDMETHODCALLTYPE *CreateVideoDecoderHeap1)(
        ID3D12VideoDevice3 *This,
        const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_decoder_heap);

    HRESULT (STDMETHODCALLTYPE *CreateVideoProcessor1)(
        ID3D12VideoDevice3 *This,
        UINT node_mask,
        const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,
        UINT input_stream_descs_count,
        const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_processor);

    HRESULT (STDMETHODCALLTYPE *CreateVideoExtensionCommand)(
        ID3D12VideoDevice3 *This,
        const D3D12_VIDEO_EXTENSION_COMMAND_DESC *desc,
        const void *creation_parameters,
        SIZE_T creation_parameters_data_size_in_bytes,
        ID3D12ProtectedResourceSession *protected_resource_session,
        REFIID riid,
        void **video_extension_command);

    HRESULT (STDMETHODCALLTYPE *ExecuteExtensionCommand)(
        ID3D12VideoDevice3 *This,
        ID3D12VideoExtensionCommand *extension_command,
        const void *execution_parameters,
        SIZE_T execution_parameters_size_in_bytes,
        void *output_data,
        SIZE_T output_data_size_in_bytes);

    /*** ID3D12VideoDevice3 methods ***/
    HRESULT (STDMETHODCALLTYPE *CreateVideoEncoder)(
        ID3D12VideoDevice3 *This,
        const D3D12_VIDEO_ENCODER_DESC *desc,
        REFIID riid,
        void **video_encoder);

    HRESULT (STDMETHODCALLTYPE *CreateVideoEncoderHeap)(
        ID3D12VideoDevice3 *This,
        const D3D12_VIDEO_ENCODER_HEAP_DESC *desc,
        REFIID riid,
        void **video_encoder_heap);

    END_INTERFACE
} ID3D12VideoDevice3Vtbl;

interface ID3D12VideoDevice3 {
    CONST_VTBL ID3D12VideoDevice3Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoDevice3_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoDevice3_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoDevice3_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12VideoDevice methods ***/
#define ID3D12VideoDevice3_CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size) (This)->lpVtbl->CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size)
#define ID3D12VideoDevice3_CreateVideoDecoder(This,desc,riid,video_decoder) (This)->lpVtbl->CreateVideoDecoder(This,desc,riid,video_decoder)
#define ID3D12VideoDevice3_CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap) (This)->lpVtbl->CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap)
#define ID3D12VideoDevice3_CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor) (This)->lpVtbl->CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor)
/*** ID3D12VideoDevice1 methods ***/
#define ID3D12VideoDevice3_CreateVideoMotionEstimator(This,desc,protected_resource_session,riid,video_motion_estimator) (This)->lpVtbl->CreateVideoMotionEstimator(This,desc,protected_resource_session,riid,video_motion_estimator)
#define ID3D12VideoDevice3_CreateVideoMotionVectorHeap(This,desc,protected_resource_session,riid,video_motion_vector_heap) (This)->lpVtbl->CreateVideoMotionVectorHeap(This,desc,protected_resource_session,riid,video_motion_vector_heap)
/*** ID3D12VideoDevice2 methods ***/
#define ID3D12VideoDevice3_CreateVideoDecoder1(This,desc,protected_resource_session,riid,video_decoder) (This)->lpVtbl->CreateVideoDecoder1(This,desc,protected_resource_session,riid,video_decoder)
#define ID3D12VideoDevice3_CreateVideoDecoderHeap1(This,video_decoder_heap_desc,protected_resource_session,riid,video_decoder_heap) (This)->lpVtbl->CreateVideoDecoderHeap1(This,video_decoder_heap_desc,protected_resource_session,riid,video_decoder_heap)
#define ID3D12VideoDevice3_CreateVideoProcessor1(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,protected_resource_session,riid,video_processor) (This)->lpVtbl->CreateVideoProcessor1(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,protected_resource_session,riid,video_processor)
#define ID3D12VideoDevice3_CreateVideoExtensionCommand(This,desc,creation_parameters,creation_parameters_data_size_in_bytes,protected_resource_session,riid,video_extension_command) (This)->lpVtbl->CreateVideoExtensionCommand(This,desc,creation_parameters,creation_parameters_data_size_in_bytes,protected_resource_session,riid,video_extension_command)
#define ID3D12VideoDevice3_ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes,output_data,output_data_size_in_bytes) (This)->lpVtbl->ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes,output_data,output_data_size_in_bytes)
/*** ID3D12VideoDevice3 methods ***/
#define ID3D12VideoDevice3_CreateVideoEncoder(This,desc,riid,video_encoder) (This)->lpVtbl->CreateVideoEncoder(This,desc,riid,video_encoder)
#define ID3D12VideoDevice3_CreateVideoEncoderHeap(This,desc,riid,video_encoder_heap) (This)->lpVtbl->CreateVideoEncoderHeap(This,desc,riid,video_encoder_heap)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoDevice3_QueryInterface(ID3D12VideoDevice3* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoDevice3_AddRef(ID3D12VideoDevice3* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoDevice3_Release(ID3D12VideoDevice3* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12VideoDevice methods ***/
static inline HRESULT ID3D12VideoDevice3_CheckFeatureSupport(ID3D12VideoDevice3* This,D3D12_FEATURE_VIDEO feature_video,void *feature_support_data,UINT feature_support_data_size) {
    return This->lpVtbl->CheckFeatureSupport(This,feature_video,feature_support_data,feature_support_data_size);
}
static inline HRESULT ID3D12VideoDevice3_CreateVideoDecoder(ID3D12VideoDevice3* This,const D3D12_VIDEO_DECODER_DESC *desc,REFIID riid,void **video_decoder) {
    return This->lpVtbl->CreateVideoDecoder(This,desc,riid,video_decoder);
}
static inline HRESULT ID3D12VideoDevice3_CreateVideoDecoderHeap(ID3D12VideoDevice3* This,const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,REFIID riid,void **video_decoder_heap) {
    return This->lpVtbl->CreateVideoDecoderHeap(This,video_decoder_heap_desc,riid,video_decoder_heap);
}
static inline HRESULT ID3D12VideoDevice3_CreateVideoProcessor(ID3D12VideoDevice3* This,UINT node_mask,const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,UINT input_stream_descs_count,const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,REFIID riid,void **video_processor) {
    return This->lpVtbl->CreateVideoProcessor(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,riid,video_processor);
}
/*** ID3D12VideoDevice1 methods ***/
static inline HRESULT ID3D12VideoDevice3_CreateVideoMotionEstimator(ID3D12VideoDevice3* This,const D3D12_VIDEO_MOTION_ESTIMATOR_DESC *desc,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_motion_estimator) {
    return This->lpVtbl->CreateVideoMotionEstimator(This,desc,protected_resource_session,riid,video_motion_estimator);
}
static inline HRESULT ID3D12VideoDevice3_CreateVideoMotionVectorHeap(ID3D12VideoDevice3* This,const D3D12_VIDEO_MOTION_VECTOR_HEAP_DESC *desc,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_motion_vector_heap) {
    return This->lpVtbl->CreateVideoMotionVectorHeap(This,desc,protected_resource_session,riid,video_motion_vector_heap);
}
/*** ID3D12VideoDevice2 methods ***/
static inline HRESULT ID3D12VideoDevice3_CreateVideoDecoder1(ID3D12VideoDevice3* This,const D3D12_VIDEO_DECODER_DESC *desc,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_decoder) {
    return This->lpVtbl->CreateVideoDecoder1(This,desc,protected_resource_session,riid,video_decoder);
}
static inline HRESULT ID3D12VideoDevice3_CreateVideoDecoderHeap1(ID3D12VideoDevice3* This,const D3D12_VIDEO_DECODER_HEAP_DESC *video_decoder_heap_desc,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_decoder_heap) {
    return This->lpVtbl->CreateVideoDecoderHeap1(This,video_decoder_heap_desc,protected_resource_session,riid,video_decoder_heap);
}
static inline HRESULT ID3D12VideoDevice3_CreateVideoProcessor1(ID3D12VideoDevice3* This,UINT node_mask,const D3D12_VIDEO_PROCESS_OUTPUT_STREAM_DESC *output_stream_desc,UINT input_stream_descs_count,const D3D12_VIDEO_PROCESS_INPUT_STREAM_DESC *input_stream_descs,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_processor) {
    return This->lpVtbl->CreateVideoProcessor1(This,node_mask,output_stream_desc,input_stream_descs_count,input_stream_descs,protected_resource_session,riid,video_processor);
}
static inline HRESULT ID3D12VideoDevice3_CreateVideoExtensionCommand(ID3D12VideoDevice3* This,const D3D12_VIDEO_EXTENSION_COMMAND_DESC *desc,const void *creation_parameters,SIZE_T creation_parameters_data_size_in_bytes,ID3D12ProtectedResourceSession *protected_resource_session,REFIID riid,void **video_extension_command) {
    return This->lpVtbl->CreateVideoExtensionCommand(This,desc,creation_parameters,creation_parameters_data_size_in_bytes,protected_resource_session,riid,video_extension_command);
}
static inline HRESULT ID3D12VideoDevice3_ExecuteExtensionCommand(ID3D12VideoDevice3* This,ID3D12VideoExtensionCommand *extension_command,const void *execution_parameters,SIZE_T execution_parameters_size_in_bytes,void *output_data,SIZE_T output_data_size_in_bytes) {
    return This->lpVtbl->ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes,output_data,output_data_size_in_bytes);
}
/*** ID3D12VideoDevice3 methods ***/
static inline HRESULT ID3D12VideoDevice3_CreateVideoEncoder(ID3D12VideoDevice3* This,const D3D12_VIDEO_ENCODER_DESC *desc,REFIID riid,void **video_encoder) {
    return This->lpVtbl->CreateVideoEncoder(This,desc,riid,video_encoder);
}
static inline HRESULT ID3D12VideoDevice3_CreateVideoEncoderHeap(ID3D12VideoDevice3* This,const D3D12_VIDEO_ENCODER_HEAP_DESC *desc,REFIID riid,void **video_encoder_heap) {
    return This->lpVtbl->CreateVideoEncoderHeap(This,desc,riid,video_encoder_heap);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoDevice3_INTERFACE_DEFINED__ */

typedef enum D3D12_VIDEO_ENCODER_FRAME_TYPE_H264 {
    D3D12_VIDEO_ENCODER_FRAME_TYPE_H264_I_FRAME = 0,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_H264_P_FRAME = 1,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_H264_B_FRAME = 2,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_H264_IDR_FRAME = 3
} D3D12_VIDEO_ENCODER_FRAME_TYPE_H264;
typedef struct D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_H264 {
    UINT ReconstructedPictureResourceIndex;
    WINBOOL IsLongTermReference;
    UINT LongTermPictureIdx;
    UINT PictureOrderCountNumber;
    UINT FrameDecodingOrderNumber;
    UINT TemporalLayerIndex;
} D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_H264;
typedef enum D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAGS {
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAG_REQUEST_INTRA_CONSTRAINED_SLICES = 0x1
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAGS);
typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_MARKING_OPERATION {
    UCHAR memory_management_control_operation;
    UINT difference_of_pic_nums_minus1;
    UINT long_term_pic_num;
    UINT long_term_frame_idx;
    UINT max_long_term_frame_idx_plus1;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_MARKING_OPERATION;
typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_LIST_MODIFICATION_OPERATION {
    UCHAR modification_of_pic_nums_idc;
    UINT abs_diff_pic_num_minus1;
    UINT long_term_pic_num;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_LIST_MODIFICATION_OPERATION;
typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264 {
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_FLAGS Flags;
    D3D12_VIDEO_ENCODER_FRAME_TYPE_H264 FrameType;
    UINT pic_parameter_set_id;
    UINT idr_pic_id;
    UINT PictureOrderCountNumber;
    UINT FrameDecodingOrderNumber;
    UINT TemporalLayerIndex;
    UINT List0ReferenceFramesCount;
    UINT *pList0ReferenceFrames;
    UINT List1ReferenceFramesCount;
    UINT *pList1ReferenceFrames;
    UINT ReferenceFramesReconPictureDescriptorsCount;
    D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_H264 *pReferenceFramesReconPictureDescriptors;
    UCHAR adaptive_ref_pic_marking_mode_flag;
    UINT RefPicMarkingOperationsCommandsCount;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_MARKING_OPERATION *pRefPicMarkingOperationsCommands;
    UINT List0RefPicModificationsCount;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_LIST_MODIFICATION_OPERATION *pList0RefPicModifications;
    UINT List1RefPicModificationsCount;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264_REFERENCE_PICTURE_LIST_MODIFICATION_OPERATION *pList1RefPicModifications;
    UINT QPMapValuesCount;
    INT8 *pRateControlQPMap;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264;
typedef enum D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC {
    D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC_I_FRAME = 0,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC_P_FRAME = 1,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC_B_FRAME = 2,
    D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC_IDR_FRAME = 3
} D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC;
typedef struct D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_HEVC {
    UINT ReconstructedPictureResourceIndex;
    WINBOOL IsRefUsedByCurrentPic;
    WINBOOL IsLongTermReference;
    UINT PictureOrderCountNumber;
    UINT TemporalLayerIndex;
} D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_HEVC;
typedef enum D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAGS {
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAG_REQUEST_INTRA_CONSTRAINED_SLICES = 0x1
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAGS);
typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC {
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC_FLAGS Flags;
    D3D12_VIDEO_ENCODER_FRAME_TYPE_HEVC FrameType;
    UINT slice_pic_parameter_set_id;
    UINT PictureOrderCountNumber;
    UINT TemporalLayerIndex;
    UINT List0ReferenceFramesCount;
    UINT *pList0ReferenceFrames;
    UINT List1ReferenceFramesCount;
    UINT *pList1ReferenceFrames;
    UINT ReferenceFramesReconPictureDescriptorsCount;
    D3D12_VIDEO_ENCODER_REFERENCE_PICTURE_DESCRIPTOR_HEVC *pReferenceFramesReconPictureDescriptors;
    UINT List0RefPicModificationsCount;
    UINT *pList0RefPicModifications;
    UINT List1RefPicModificationsCount;
    UINT *pList1RefPicModifications;
    UINT QPMapValuesCount;
    INT8 *pRateControlQPMap;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC;
typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA {
    UINT DataSize;
    __C89_NAMELESS union {
        D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_H264 *pH264PicData;
        D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA_HEVC *pHEVCPicData;
    } __C89_NAMELESSUNIONNAME;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA;
typedef struct D3D12_VIDEO_ENCODE_REFERENCE_FRAMES {
    UINT NumTexture2Ds;
    ID3D12Resource **ppTexture2Ds;
    UINT *pSubresources;
} D3D12_VIDEO_ENCODE_REFERENCE_FRAMES;
typedef enum D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAGS {
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAG_USED_AS_REFERENCE_PICTURE = 0x1
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAGS);
typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_DESC {
    UINT IntraRefreshFrameIndex;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_FLAGS Flags;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_CODEC_DATA PictureControlCodecData;
    D3D12_VIDEO_ENCODE_REFERENCE_FRAMES ReferenceFrames;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_DESC;
typedef enum D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAGS {
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_NONE = 0x0,
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_RESOLUTION_CHANGE = 0x1,
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_RATE_CONTROL_CHANGE = 0x2,
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_SUBREGION_LAYOUT_CHANGE = 0x4,
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_REQUEST_INTRA_REFRESH = 0x8,
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAG_GOP_SEQUENCE_CHANGE = 0x10
} D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAGS);
typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA_SLICES {
    __C89_NAMELESS union {
        UINT MaxBytesPerSlice;
        UINT NumberOfCodingUnitsPerSlice;
        UINT NumberOfRowsPerSlice;
        UINT NumberOfSlicesPerFrame;
    } __C89_NAMELESSUNIONNAME;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA_SLICES;
typedef struct D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA {
    UINT DataSize;
    __C89_NAMELESS union {
        const D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA_SLICES *pSlicesPartition_H264;
        const D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA_SLICES *pSlicesPartition_HEVC;
    } __C89_NAMELESSUNIONNAME;
} D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA;
typedef struct D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_DESC {
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_FLAGS Flags;
    D3D12_VIDEO_ENCODER_INTRA_REFRESH IntraRefreshConfig;
    D3D12_VIDEO_ENCODER_RATE_CONTROL RateControl;
    D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC PictureTargetResolution;
    D3D12_VIDEO_ENCODER_FRAME_SUBREGION_LAYOUT_MODE SelectedLayoutMode;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_SUBREGIONS_LAYOUT_DATA FrameSubregionsLayoutData;
    D3D12_VIDEO_ENCODER_SEQUENCE_GOP_STRUCTURE CodecGopSequence;
} D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_DESC;
typedef struct D3D12_VIDEO_ENCODER_ENCODEFRAME_INPUT_ARGUMENTS {
    D3D12_VIDEO_ENCODER_SEQUENCE_CONTROL_DESC SequenceControlDesc;
    D3D12_VIDEO_ENCODER_PICTURE_CONTROL_DESC PictureControlDesc;
    ID3D12Resource *pInputFrame;
    UINT InputFrameSubresource;
    UINT CurrentFrameBitstreamMetadataSize;
} D3D12_VIDEO_ENCODER_ENCODEFRAME_INPUT_ARGUMENTS;
typedef struct D3D12_VIDEO_ENCODER_COMPRESSED_BITSTREAM {
    ID3D12Resource *pBuffer;
    UINT64 FrameStartOffset;
} D3D12_VIDEO_ENCODER_COMPRESSED_BITSTREAM;
typedef struct D3D12_VIDEO_ENCODER_RECONSTRUCTED_PICTURE {
    ID3D12Resource *pReconstructedPicture;
    UINT ReconstructedPictureSubresource;
} D3D12_VIDEO_ENCODER_RECONSTRUCTED_PICTURE;
typedef struct D3D12_VIDEO_ENCODER_FRAME_SUBREGION_METADATA {
    UINT64 bSize;
    UINT64 bStartOffset;
    UINT64 bHeaderSize;
} D3D12_VIDEO_ENCODER_FRAME_SUBREGION_METADATA;
typedef enum D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAGS {
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_NO_ERROR = 0x0,
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_CODEC_PICTURE_CONTROL_NOT_SUPPORTED = 0x1,
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_SUBREGION_LAYOUT_CONFIGURATION_NOT_SUPPORTED = 0x2,
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_INVALID_REFERENCE_PICTURES = 0x4,
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_RECONFIGURATION_REQUEST_NOT_SUPPORTED = 0x8,
    D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAG_INVALID_METADATA_BUFFER_SOURCE = 0x10
} D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAGS;
DEFINE_ENUM_FLAG_OPERATORS(D3D12_VIDEO_ENCODER_ENCODE_ERROR_FLAGS);
typedef struct D3D12_VIDEO_ENCODER_OUTPUT_METADATA_STATISTICS {
    UINT64 AverageQP;
    UINT64 IntraCodingUnitsCount;
    UINT64 InterCodingUnitsCount;
    UINT64 SkipCodingUnitsCount;
    UINT64 AverageMotionEstimationXDirection;
    UINT64 AverageMotionEstimationYDirection;
} D3D12_VIDEO_ENCODER_OUTPUT_METADATA_STATISTICS;
typedef struct D3D12_VIDEO_ENCODER_OUTPUT_METADATA {
    UINT64 EncodeErrorFlags;
    D3D12_VIDEO_ENCODER_OUTPUT_METADATA_STATISTICS EncodeStats;
    UINT64 EncodedBitstreamWrittenBytesCount;
    UINT64 WrittenSubregionsCount;
} D3D12_VIDEO_ENCODER_OUTPUT_METADATA;
typedef struct D3D12_VIDEO_ENCODER_ENCODE_OPERATION_METADATA_BUFFER {
    ID3D12Resource *pBuffer;
    UINT64 Offset;
} D3D12_VIDEO_ENCODER_ENCODE_OPERATION_METADATA_BUFFER;
typedef struct D3D12_VIDEO_ENCODER_RESOLVE_METADATA_INPUT_ARGUMENTS {
    D3D12_VIDEO_ENCODER_CODEC EncoderCodec;
    D3D12_VIDEO_ENCODER_PROFILE_DESC EncoderProfile;
    DXGI_FORMAT EncoderInputFormat;
    D3D12_VIDEO_ENCODER_PICTURE_RESOLUTION_DESC EncodedPictureEffectiveResolution;
    D3D12_VIDEO_ENCODER_ENCODE_OPERATION_METADATA_BUFFER HWLayoutMetadata;
} D3D12_VIDEO_ENCODER_RESOLVE_METADATA_INPUT_ARGUMENTS;
typedef struct D3D12_VIDEO_ENCODER_RESOLVE_METADATA_OUTPUT_ARGUMENTS {
    D3D12_VIDEO_ENCODER_ENCODE_OPERATION_METADATA_BUFFER ResolvedLayoutMetadata;
} D3D12_VIDEO_ENCODER_RESOLVE_METADATA_OUTPUT_ARGUMENTS;
typedef struct D3D12_VIDEO_ENCODER_ENCODEFRAME_OUTPUT_ARGUMENTS {
    D3D12_VIDEO_ENCODER_COMPRESSED_BITSTREAM Bitstream;
    D3D12_VIDEO_ENCODER_RECONSTRUCTED_PICTURE ReconstructedPicture;
    D3D12_VIDEO_ENCODER_ENCODE_OPERATION_METADATA_BUFFER EncoderOutputMetadata;
} D3D12_VIDEO_ENCODER_ENCODEFRAME_OUTPUT_ARGUMENTS;
/*****************************************************************************
 * ID3D12VideoEncodeCommandList2 interface
 */
#ifndef __ID3D12VideoEncodeCommandList2_INTERFACE_DEFINED__
#define __ID3D12VideoEncodeCommandList2_INTERFACE_DEFINED__

DEFINE_GUID(IID_ID3D12VideoEncodeCommandList2, 0x895491e2, 0xe701, 0x46a9, 0x9a,0x1f, 0x8d,0x34,0x80,0xed,0x86,0x7a);
#if defined(__cplusplus) && !defined(CINTERFACE)
MIDL_INTERFACE("895491e2-e701-46a9-9a1f-8d3480ed867a")
ID3D12VideoEncodeCommandList2 : public ID3D12VideoEncodeCommandList1
{
    virtual void STDMETHODCALLTYPE EncodeFrame(
        ID3D12VideoEncoder *encoder,
        ID3D12VideoEncoderHeap *heap,
        const D3D12_VIDEO_ENCODER_ENCODEFRAME_INPUT_ARGUMENTS *input_arguments,
        const D3D12_VIDEO_ENCODER_ENCODEFRAME_OUTPUT_ARGUMENTS *output_arguments) = 0;

    virtual void STDMETHODCALLTYPE ResolveEncoderOutputMetadata(
        const D3D12_VIDEO_ENCODER_RESOLVE_METADATA_INPUT_ARGUMENTS *input_arguments,
        const D3D12_VIDEO_ENCODER_RESOLVE_METADATA_OUTPUT_ARGUMENTS *output_arguments) = 0;

};
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(ID3D12VideoEncodeCommandList2, 0x895491e2, 0xe701, 0x46a9, 0x9a,0x1f, 0x8d,0x34,0x80,0xed,0x86,0x7a)
#endif
#else
typedef struct ID3D12VideoEncodeCommandList2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        ID3D12VideoEncodeCommandList2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        ID3D12VideoEncodeCommandList2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        ID3D12VideoEncodeCommandList2 *This);

    /*** ID3D12Object methods ***/
    HRESULT (STDMETHODCALLTYPE *GetPrivateData)(
        ID3D12VideoEncodeCommandList2 *This,
        REFGUID guid,
        UINT *data_size,
        void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateData)(
        ID3D12VideoEncodeCommandList2 *This,
        REFGUID guid,
        UINT data_size,
        const void *data);

    HRESULT (STDMETHODCALLTYPE *SetPrivateDataInterface)(
        ID3D12VideoEncodeCommandList2 *This,
        REFGUID guid,
        const IUnknown *data);

    HRESULT (STDMETHODCALLTYPE *SetName)(
        ID3D12VideoEncodeCommandList2 *This,
        const WCHAR *name);

    /*** ID3D12DeviceChild methods ***/
    HRESULT (STDMETHODCALLTYPE *GetDevice)(
        ID3D12VideoEncodeCommandList2 *This,
        REFIID riid,
        void **device);

    /*** ID3D12CommandList methods ***/
    D3D12_COMMAND_LIST_TYPE (STDMETHODCALLTYPE *GetType)(
        ID3D12VideoEncodeCommandList2 *This);

    /*** ID3D12VideoEncodeCommandList methods ***/
    HRESULT (STDMETHODCALLTYPE *Close)(
        ID3D12VideoEncodeCommandList2 *This);

    HRESULT (STDMETHODCALLTYPE *Reset)(
        ID3D12VideoEncodeCommandList2 *This,
        ID3D12CommandAllocator *allocator);

    void (STDMETHODCALLTYPE *ClearState)(
        ID3D12VideoEncodeCommandList2 *This);

    void (STDMETHODCALLTYPE *ResourceBarrier)(
        ID3D12VideoEncodeCommandList2 *This,
        UINT barriers_count,
        const D3D12_RESOURCE_BARRIER *barriers);

    void (STDMETHODCALLTYPE *DiscardResource)(
        ID3D12VideoEncodeCommandList2 *This,
        ID3D12Resource *resource,
        const D3D12_DISCARD_REGION *region);

    void (STDMETHODCALLTYPE *BeginQuery)(
        ID3D12VideoEncodeCommandList2 *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index);

    void (STDMETHODCALLTYPE *EndQuery)(
        ID3D12VideoEncodeCommandList2 *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT index);

    void (STDMETHODCALLTYPE *ResolveQueryData)(
        ID3D12VideoEncodeCommandList2 *This,
        ID3D12QueryHeap *query_heap,
        D3D12_QUERY_TYPE type,
        UINT start_index,
        UINT queries_count,
        ID3D12Resource *destination_buffer,
        UINT64 aligned_destination_buffer_offset);

    void (STDMETHODCALLTYPE *SetPredication)(
        ID3D12VideoEncodeCommandList2 *This,
        ID3D12Resource *buffer,
        UINT64 aligned_buffer_offset,
        D3D12_PREDICATION_OP operation);

    void (STDMETHODCALLTYPE *SetMarker)(
        ID3D12VideoEncodeCommandList2 *This,
        UINT metadata,
        const void *data,
        UINT size);

    void (STDMETHODCALLTYPE *BeginEvent)(
        ID3D12VideoEncodeCommandList2 *This,
        UINT metadata,
        const void *data,
        UINT size);

    void (STDMETHODCALLTYPE *EndEvent)(
        ID3D12VideoEncodeCommandList2 *This);

    void (STDMETHODCALLTYPE *EstimateMotion)(
        ID3D12VideoEncodeCommandList2 *This,
        ID3D12VideoMotionEstimator *motion_estimator,
        const D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT *output_arguments,
        const D3D12_VIDEO_MOTION_ESTIMATOR_INPUT *input_arguments);

    void (STDMETHODCALLTYPE *ResolveMotionVectorHeap)(
        ID3D12VideoEncodeCommandList2 *This,
        const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT *output_arguments,
        const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT *input_arguments);

    void (STDMETHODCALLTYPE *WriteBufferImmediate)(
        ID3D12VideoEncodeCommandList2 *This,
        UINT count,
        const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,
        const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes);

    void (STDMETHODCALLTYPE *SetProtectedResourceSession)(
        ID3D12VideoEncodeCommandList2 *This,
        ID3D12ProtectedResourceSession *protected_resource_session);

    /*** ID3D12VideoEncodeCommandList1 methods ***/
    void (STDMETHODCALLTYPE *InitializeExtensionCommand)(
        ID3D12VideoEncodeCommandList2 *This,
        ID3D12VideoExtensionCommand *extension_command,
        const void *initialization_parameters,
        SIZE_T initialization_parameters_size_in_bytes);

    void (STDMETHODCALLTYPE *ExecuteExtensionCommand)(
        ID3D12VideoEncodeCommandList2 *This,
        ID3D12VideoExtensionCommand *extension_command,
        const void *execution_parameters,
        SIZE_T execution_parameters_size_in_bytes);

    /*** ID3D12VideoEncodeCommandList2 methods ***/
    void (STDMETHODCALLTYPE *EncodeFrame)(
        ID3D12VideoEncodeCommandList2 *This,
        ID3D12VideoEncoder *encoder,
        ID3D12VideoEncoderHeap *heap,
        const D3D12_VIDEO_ENCODER_ENCODEFRAME_INPUT_ARGUMENTS *input_arguments,
        const D3D12_VIDEO_ENCODER_ENCODEFRAME_OUTPUT_ARGUMENTS *output_arguments);

    void (STDMETHODCALLTYPE *ResolveEncoderOutputMetadata)(
        ID3D12VideoEncodeCommandList2 *This,
        const D3D12_VIDEO_ENCODER_RESOLVE_METADATA_INPUT_ARGUMENTS *input_arguments,
        const D3D12_VIDEO_ENCODER_RESOLVE_METADATA_OUTPUT_ARGUMENTS *output_arguments);

    END_INTERFACE
} ID3D12VideoEncodeCommandList2Vtbl;

interface ID3D12VideoEncodeCommandList2 {
    CONST_VTBL ID3D12VideoEncodeCommandList2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define ID3D12VideoEncodeCommandList2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define ID3D12VideoEncodeCommandList2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define ID3D12VideoEncodeCommandList2_Release(This) (This)->lpVtbl->Release(This)
/*** ID3D12Object methods ***/
#define ID3D12VideoEncodeCommandList2_GetPrivateData(This,guid,data_size,data) (This)->lpVtbl->GetPrivateData(This,guid,data_size,data)
#define ID3D12VideoEncodeCommandList2_SetPrivateData(This,guid,data_size,data) (This)->lpVtbl->SetPrivateData(This,guid,data_size,data)
#define ID3D12VideoEncodeCommandList2_SetPrivateDataInterface(This,guid,data) (This)->lpVtbl->SetPrivateDataInterface(This,guid,data)
#define ID3D12VideoEncodeCommandList2_SetName(This,name) (This)->lpVtbl->SetName(This,name)
/*** ID3D12DeviceChild methods ***/
#define ID3D12VideoEncodeCommandList2_GetDevice(This,riid,device) (This)->lpVtbl->GetDevice(This,riid,device)
/*** ID3D12CommandList methods ***/
#define ID3D12VideoEncodeCommandList2_GetType(This) (This)->lpVtbl->GetType(This)
/*** ID3D12VideoEncodeCommandList methods ***/
#define ID3D12VideoEncodeCommandList2_Close(This) (This)->lpVtbl->Close(This)
#define ID3D12VideoEncodeCommandList2_Reset(This,allocator) (This)->lpVtbl->Reset(This,allocator)
#define ID3D12VideoEncodeCommandList2_ClearState(This) (This)->lpVtbl->ClearState(This)
#define ID3D12VideoEncodeCommandList2_ResourceBarrier(This,barriers_count,barriers) (This)->lpVtbl->ResourceBarrier(This,barriers_count,barriers)
#define ID3D12VideoEncodeCommandList2_DiscardResource(This,resource,region) (This)->lpVtbl->DiscardResource(This,resource,region)
#define ID3D12VideoEncodeCommandList2_BeginQuery(This,query_heap,type,index) (This)->lpVtbl->BeginQuery(This,query_heap,type,index)
#define ID3D12VideoEncodeCommandList2_EndQuery(This,query_heap,type,index) (This)->lpVtbl->EndQuery(This,query_heap,type,index)
#define ID3D12VideoEncodeCommandList2_ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset) (This)->lpVtbl->ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset)
#define ID3D12VideoEncodeCommandList2_SetPredication(This,buffer,aligned_buffer_offset,operation) (This)->lpVtbl->SetPredication(This,buffer,aligned_buffer_offset,operation)
#define ID3D12VideoEncodeCommandList2_SetMarker(This,metadata,data,size) (This)->lpVtbl->SetMarker(This,metadata,data,size)
#define ID3D12VideoEncodeCommandList2_BeginEvent(This,metadata,data,size) (This)->lpVtbl->BeginEvent(This,metadata,data,size)
#define ID3D12VideoEncodeCommandList2_EndEvent(This) (This)->lpVtbl->EndEvent(This)
#define ID3D12VideoEncodeCommandList2_EstimateMotion(This,motion_estimator,output_arguments,input_arguments) (This)->lpVtbl->EstimateMotion(This,motion_estimator,output_arguments,input_arguments)
#define ID3D12VideoEncodeCommandList2_ResolveMotionVectorHeap(This,output_arguments,input_arguments) (This)->lpVtbl->ResolveMotionVectorHeap(This,output_arguments,input_arguments)
#define ID3D12VideoEncodeCommandList2_WriteBufferImmediate(This,count,params,modes) (This)->lpVtbl->WriteBufferImmediate(This,count,params,modes)
#define ID3D12VideoEncodeCommandList2_SetProtectedResourceSession(This,protected_resource_session) (This)->lpVtbl->SetProtectedResourceSession(This,protected_resource_session)
/*** ID3D12VideoEncodeCommandList1 methods ***/
#define ID3D12VideoEncodeCommandList2_InitializeExtensionCommand(This,extension_command,initialization_parameters,initialization_parameters_size_in_bytes) (This)->lpVtbl->InitializeExtensionCommand(This,extension_command,initialization_parameters,initialization_parameters_size_in_bytes)
#define ID3D12VideoEncodeCommandList2_ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes) (This)->lpVtbl->ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes)
/*** ID3D12VideoEncodeCommandList2 methods ***/
#define ID3D12VideoEncodeCommandList2_EncodeFrame(This,encoder,heap,input_arguments,output_arguments) (This)->lpVtbl->EncodeFrame(This,encoder,heap,input_arguments,output_arguments)
#define ID3D12VideoEncodeCommandList2_ResolveEncoderOutputMetadata(This,input_arguments,output_arguments) (This)->lpVtbl->ResolveEncoderOutputMetadata(This,input_arguments,output_arguments)
#else
/*** IUnknown methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList2_QueryInterface(ID3D12VideoEncodeCommandList2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG ID3D12VideoEncodeCommandList2_AddRef(ID3D12VideoEncodeCommandList2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG ID3D12VideoEncodeCommandList2_Release(ID3D12VideoEncodeCommandList2* This) {
    return This->lpVtbl->Release(This);
}
/*** ID3D12Object methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList2_GetPrivateData(ID3D12VideoEncodeCommandList2* This,REFGUID guid,UINT *data_size,void *data) {
    return This->lpVtbl->GetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoEncodeCommandList2_SetPrivateData(ID3D12VideoEncodeCommandList2* This,REFGUID guid,UINT data_size,const void *data) {
    return This->lpVtbl->SetPrivateData(This,guid,data_size,data);
}
static inline HRESULT ID3D12VideoEncodeCommandList2_SetPrivateDataInterface(ID3D12VideoEncodeCommandList2* This,REFGUID guid,const IUnknown *data) {
    return This->lpVtbl->SetPrivateDataInterface(This,guid,data);
}
static inline HRESULT ID3D12VideoEncodeCommandList2_SetName(ID3D12VideoEncodeCommandList2* This,const WCHAR *name) {
    return This->lpVtbl->SetName(This,name);
}
/*** ID3D12DeviceChild methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList2_GetDevice(ID3D12VideoEncodeCommandList2* This,REFIID riid,void **device) {
    return This->lpVtbl->GetDevice(This,riid,device);
}
/*** ID3D12CommandList methods ***/
static inline D3D12_COMMAND_LIST_TYPE ID3D12VideoEncodeCommandList2_GetType(ID3D12VideoEncodeCommandList2* This) {
    return This->lpVtbl->GetType(This);
}
/*** ID3D12VideoEncodeCommandList methods ***/
static inline HRESULT ID3D12VideoEncodeCommandList2_Close(ID3D12VideoEncodeCommandList2* This) {
    return This->lpVtbl->Close(This);
}
static inline HRESULT ID3D12VideoEncodeCommandList2_Reset(ID3D12VideoEncodeCommandList2* This,ID3D12CommandAllocator *allocator) {
    return This->lpVtbl->Reset(This,allocator);
}
static inline void ID3D12VideoEncodeCommandList2_ClearState(ID3D12VideoEncodeCommandList2* This) {
    This->lpVtbl->ClearState(This);
}
static inline void ID3D12VideoEncodeCommandList2_ResourceBarrier(ID3D12VideoEncodeCommandList2* This,UINT barriers_count,const D3D12_RESOURCE_BARRIER *barriers) {
    This->lpVtbl->ResourceBarrier(This,barriers_count,barriers);
}
static inline void ID3D12VideoEncodeCommandList2_DiscardResource(ID3D12VideoEncodeCommandList2* This,ID3D12Resource *resource,const D3D12_DISCARD_REGION *region) {
    This->lpVtbl->DiscardResource(This,resource,region);
}
static inline void ID3D12VideoEncodeCommandList2_BeginQuery(ID3D12VideoEncodeCommandList2* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT index) {
    This->lpVtbl->BeginQuery(This,query_heap,type,index);
}
static inline void ID3D12VideoEncodeCommandList2_EndQuery(ID3D12VideoEncodeCommandList2* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT index) {
    This->lpVtbl->EndQuery(This,query_heap,type,index);
}
static inline void ID3D12VideoEncodeCommandList2_ResolveQueryData(ID3D12VideoEncodeCommandList2* This,ID3D12QueryHeap *query_heap,D3D12_QUERY_TYPE type,UINT start_index,UINT queries_count,ID3D12Resource *destination_buffer,UINT64 aligned_destination_buffer_offset) {
    This->lpVtbl->ResolveQueryData(This,query_heap,type,start_index,queries_count,destination_buffer,aligned_destination_buffer_offset);
}
static inline void ID3D12VideoEncodeCommandList2_SetPredication(ID3D12VideoEncodeCommandList2* This,ID3D12Resource *buffer,UINT64 aligned_buffer_offset,D3D12_PREDICATION_OP operation) {
    This->lpVtbl->SetPredication(This,buffer,aligned_buffer_offset,operation);
}
static inline void ID3D12VideoEncodeCommandList2_SetMarker(ID3D12VideoEncodeCommandList2* This,UINT metadata,const void *data,UINT size) {
    This->lpVtbl->SetMarker(This,metadata,data,size);
}
static inline void ID3D12VideoEncodeCommandList2_BeginEvent(ID3D12VideoEncodeCommandList2* This,UINT metadata,const void *data,UINT size) {
    This->lpVtbl->BeginEvent(This,metadata,data,size);
}
static inline void ID3D12VideoEncodeCommandList2_EndEvent(ID3D12VideoEncodeCommandList2* This) {
    This->lpVtbl->EndEvent(This);
}
static inline void ID3D12VideoEncodeCommandList2_EstimateMotion(ID3D12VideoEncodeCommandList2* This,ID3D12VideoMotionEstimator *motion_estimator,const D3D12_VIDEO_MOTION_ESTIMATOR_OUTPUT *output_arguments,const D3D12_VIDEO_MOTION_ESTIMATOR_INPUT *input_arguments) {
    This->lpVtbl->EstimateMotion(This,motion_estimator,output_arguments,input_arguments);
}
static inline void ID3D12VideoEncodeCommandList2_ResolveMotionVectorHeap(ID3D12VideoEncodeCommandList2* This,const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_OUTPUT *output_arguments,const D3D12_RESOLVE_VIDEO_MOTION_VECTOR_HEAP_INPUT *input_arguments) {
    This->lpVtbl->ResolveMotionVectorHeap(This,output_arguments,input_arguments);
}
static inline void ID3D12VideoEncodeCommandList2_WriteBufferImmediate(ID3D12VideoEncodeCommandList2* This,UINT count,const D3D12_WRITEBUFFERIMMEDIATE_PARAMETER *params,const D3D12_WRITEBUFFERIMMEDIATE_MODE *modes) {
    This->lpVtbl->WriteBufferImmediate(This,count,params,modes);
}
static inline void ID3D12VideoEncodeCommandList2_SetProtectedResourceSession(ID3D12VideoEncodeCommandList2* This,ID3D12ProtectedResourceSession *protected_resource_session) {
    This->lpVtbl->SetProtectedResourceSession(This,protected_resource_session);
}
/*** ID3D12VideoEncodeCommandList1 methods ***/
static inline void ID3D12VideoEncodeCommandList2_InitializeExtensionCommand(ID3D12VideoEncodeCommandList2* This,ID3D12VideoExtensionCommand *extension_command,const void *initialization_parameters,SIZE_T initialization_parameters_size_in_bytes) {
    This->lpVtbl->InitializeExtensionCommand(This,extension_command,initialization_parameters,initialization_parameters_size_in_bytes);
}
static inline void ID3D12VideoEncodeCommandList2_ExecuteExtensionCommand(ID3D12VideoEncodeCommandList2* This,ID3D12VideoExtensionCommand *extension_command,const void *execution_parameters,SIZE_T execution_parameters_size_in_bytes) {
    This->lpVtbl->ExecuteExtensionCommand(This,extension_command,execution_parameters,execution_parameters_size_in_bytes);
}
/*** ID3D12VideoEncodeCommandList2 methods ***/
static inline void ID3D12VideoEncodeCommandList2_EncodeFrame(ID3D12VideoEncodeCommandList2* This,ID3D12VideoEncoder *encoder,ID3D12VideoEncoderHeap *heap,const D3D12_VIDEO_ENCODER_ENCODEFRAME_INPUT_ARGUMENTS *input_arguments,const D3D12_VIDEO_ENCODER_ENCODEFRAME_OUTPUT_ARGUMENTS *output_arguments) {
    This->lpVtbl->EncodeFrame(This,encoder,heap,input_arguments,output_arguments);
}
static inline void ID3D12VideoEncodeCommandList2_ResolveEncoderOutputMetadata(ID3D12VideoEncodeCommandList2* This,const D3D12_VIDEO_ENCODER_RESOLVE_METADATA_INPUT_ARGUMENTS *input_arguments,const D3D12_VIDEO_ENCODER_RESOLVE_METADATA_OUTPUT_ARGUMENTS *output_arguments) {
    This->lpVtbl->ResolveEncoderOutputMetadata(This,input_arguments,output_arguments);
}
#endif
#endif

#endif


#endif  /* __ID3D12VideoEncodeCommandList2_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */


/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __d3d12video_h__ */
