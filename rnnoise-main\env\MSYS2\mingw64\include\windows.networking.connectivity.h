/*** Autogenerated by WIDL 10.12 from include/windows.networking.connectivity.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_networking_connectivity_h__
#define __windows_networking_connectivity_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler ABI::Windows::Networking::Connectivity::INetworkStatusChangedEventHandler
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface INetworkStatusChangedEventHandler;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost ABI::Windows::Networking::Connectivity::IConnectionCost
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface IConnectionCost;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile ABI::Windows::Networking::Connectivity::IConnectionProfile
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface IConnectionProfile;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 ABI::Windows::Networking::Connectivity::IConnectionProfile2
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface IConnectionProfile2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus ABI::Windows::Networking::Connectivity::IDataPlanStatus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface IDataPlanStatus;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage ABI::Windows::Networking::Connectivity::IDataPlanUsage
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface IDataPlanUsage;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage ABI::Windows::Networking::Connectivity::IDataUsage
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface IDataUsage;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation ABI::Windows::Networking::Connectivity::IIPInformation
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface IIPInformation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier ABI::Windows::Networking::Connectivity::ILanIdentifier
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface ILanIdentifier;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData ABI::Windows::Networking::Connectivity::ILanIdentifierData
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface ILanIdentifierData;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter ABI::Windows::Networking::Connectivity::INetworkAdapter
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface INetworkAdapter;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics ABI::Windows::Networking::Connectivity::INetworkInformationStatics
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface INetworkInformationStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem ABI::Windows::Networking::Connectivity::INetworkItem
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface INetworkItem;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings ABI::Windows::Networking::Connectivity::INetworkSecuritySettings
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface INetworkSecuritySettings;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration ABI::Windows::Networking::Connectivity::IProxyConfiguration
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface IProxyConfiguration;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage ABI::Windows::Networking::Connectivity::INetworkUsage
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface INetworkUsage;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval ABI::Windows::Networking::Connectivity::IConnectivityInterval
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface IConnectivityInterval;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails ABI::Windows::Networking::Connectivity::IWwanConnectionProfileDetails
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface IWwanConnectionProfileDetails;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails;
#ifdef __cplusplus
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails ABI::Windows::Networking::Connectivity::IWlanConnectionProfileDetails
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                interface IWlanConnectionProfileDetails;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CConnectionCost_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CConnectionCost_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class ConnectionCost;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CConnectionCost __x_ABI_CWindows_CNetworking_CConnectivity_CConnectionCost;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CConnectionCost_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CConnectionProfile_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CConnectionProfile_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class ConnectionProfile;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CConnectionProfile __x_ABI_CWindows_CNetworking_CConnectivity_CConnectionProfile;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CConnectionProfile_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CDataPlanStatus_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CDataPlanStatus_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class DataPlanStatus;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CDataPlanStatus __x_ABI_CWindows_CNetworking_CConnectivity_CDataPlanStatus;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CDataPlanStatus_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CDataPlanUsage_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CDataPlanUsage_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class DataPlanUsage;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CDataPlanUsage __x_ABI_CWindows_CNetworking_CConnectivity_CDataPlanUsage;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CDataPlanUsage_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CDataUsage_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CDataUsage_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class DataUsage;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CDataUsage __x_ABI_CWindows_CNetworking_CConnectivity_CDataUsage;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CDataUsage_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIPInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIPInformation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class IPInformation;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIPInformation __x_ABI_CWindows_CNetworking_CConnectivity_CIPInformation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CIPInformation_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CLanIdentifier_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CLanIdentifier_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class LanIdentifier;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CLanIdentifier __x_ABI_CWindows_CNetworking_CConnectivity_CLanIdentifier;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CLanIdentifier_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CLanIdentifierData_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CLanIdentifierData_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class LanIdentifierData;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CLanIdentifierData __x_ABI_CWindows_CNetworking_CConnectivity_CLanIdentifierData;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CLanIdentifierData_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAdapter_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAdapter_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class NetworkAdapter;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAdapter __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAdapter;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAdapter_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkInformation_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkInformation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class NetworkInformation;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkInformation __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkInformation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkInformation_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkItem_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkItem_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class NetworkItem;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkItem __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkItem;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkItem_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkSecuritySettings_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkSecuritySettings_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class NetworkSecuritySettings;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkSecuritySettings __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkSecuritySettings;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkSecuritySettings_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CProxyConfiguration_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CProxyConfiguration_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class ProxyConfiguration;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CProxyConfiguration __x_ABI_CWindows_CNetworking_CConnectivity_CProxyConfiguration;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CProxyConfiguration_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CWwanConnectionProfileDetails_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CWwanConnectionProfileDetails_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class WwanConnectionProfileDetails;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CWwanConnectionProfileDetails __x_ABI_CWindows_CNetworking_CConnectivity_CWwanConnectionProfileDetails;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CWwanConnectionProfileDetails_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CWlanConnectionProfileDetails_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CWlanConnectionProfileDetails_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class WlanConnectionProfileDetails;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CWlanConnectionProfileDetails __x_ABI_CWindows_CNetworking_CConnectivity_CWlanConnectionProfileDetails;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CWlanConnectionProfileDetails_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsage_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsage_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class NetworkUsage;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsage __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsage;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsage_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CConnectivityInterval_FWD_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CConnectivityInterval_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                class ConnectivityInterval;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CConnectivityInterval __x_ABI_CWindows_CNetworking_CConnectivity_CConnectivityInterval;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CNetworking_CConnectivity_CConnectivityInterval_FWD_DEFINED__ */

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ConnectionProfile* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ProxyConfiguration* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Networking::Connectivity::ConnectionProfile* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Networking::Connectivity::ProxyConfiguration* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIIterable_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CNetworking__CEndpointPair __FIIterable_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Networking::EndpointPair* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectionProfile* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::LanIdentifier* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CHostName_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CHostName_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CHostName __FIVectorView_1_Windows__CNetworking__CHostName;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CHostName ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::HostName* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CEndpointPair __FIVectorView_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.networking.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef ____x_ABI_CWindows_CFoundation_CIClosable_FWD_DEFINED__
#define ____x_ABI_CWindows_CFoundation_CIClosable_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CFoundation_CIClosable __x_ABI_CWindows_CFoundation_CIClosable;
#ifdef __cplusplus
#define __x_ABI_CWindows_CFoundation_CIClosable ABI::Windows::Foundation::IClosable
namespace ABI {
    namespace Windows {
        namespace Foundation {
            interface IClosable;
        }
    }
}
#endif /* __cplusplus */
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CDomainNameType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CDomainNameType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            enum DomainNameType {
                DomainNameType_Suffix = 0,
                DomainNameType_FullyQualified = 1
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CDomainNameType {
    DomainNameType_Suffix = 0,
    DomainNameType_FullyQualified = 1
};
#ifdef WIDL_using_Windows_Networking
#define DomainNameType __x_ABI_CWindows_CNetworking_CDomainNameType
#endif /* WIDL_using_Windows_Networking */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CDomainNameType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CDomainNameType __x_ABI_CWindows_CNetworking_CDomainNameType;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CHostNameSortOptions_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CHostNameSortOptions_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            enum HostNameSortOptions {
                HostNameSortOptions_None = 0x0,
                HostNameSortOptions_OptimizeForLongConnections = 0x2
            };
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CHostNameSortOptions {
    HostNameSortOptions_None = 0x0,
    HostNameSortOptions_OptimizeForLongConnections = 0x2
};
#ifdef WIDL_using_Windows_Networking
#define HostNameSortOptions __x_ABI_CWindows_CNetworking_CHostNameSortOptions
#endif /* WIDL_using_Windows_Networking */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CHostNameSortOptions_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CHostNameSortOptions __x_ABI_CWindows_CNetworking_CHostNameSortOptions;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAuthenticationType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAuthenticationType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                enum NetworkAuthenticationType {
                    NetworkAuthenticationType_None = 0,
                    NetworkAuthenticationType_Unknown = 1,
                    NetworkAuthenticationType_Open80211 = 2,
                    NetworkAuthenticationType_SharedKey80211 = 3,
                    NetworkAuthenticationType_Wpa = 4,
                    NetworkAuthenticationType_WpaPsk = 5,
                    NetworkAuthenticationType_WpaNone = 6,
                    NetworkAuthenticationType_Rsna = 7,
                    NetworkAuthenticationType_RsnaPsk = 8,
                    NetworkAuthenticationType_Ihv = 9
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAuthenticationType {
    NetworkAuthenticationType_None = 0,
    NetworkAuthenticationType_Unknown = 1,
    NetworkAuthenticationType_Open80211 = 2,
    NetworkAuthenticationType_SharedKey80211 = 3,
    NetworkAuthenticationType_Wpa = 4,
    NetworkAuthenticationType_WpaPsk = 5,
    NetworkAuthenticationType_WpaNone = 6,
    NetworkAuthenticationType_Rsna = 7,
    NetworkAuthenticationType_RsnaPsk = 8,
    NetworkAuthenticationType_Ihv = 9
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define NetworkAuthenticationType __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAuthenticationType
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAuthenticationType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAuthenticationType __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAuthenticationType;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkConnectivityLevel_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkConnectivityLevel_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                enum NetworkConnectivityLevel {
                    NetworkConnectivityLevel_None = 0,
                    NetworkConnectivityLevel_LocalAccess = 1,
                    NetworkConnectivityLevel_ConstrainedInternetAccess = 2,
                    NetworkConnectivityLevel_InternetAccess = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkConnectivityLevel {
    NetworkConnectivityLevel_None = 0,
    NetworkConnectivityLevel_LocalAccess = 1,
    NetworkConnectivityLevel_ConstrainedInternetAccess = 2,
    NetworkConnectivityLevel_InternetAccess = 3
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define NetworkConnectivityLevel __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkConnectivityLevel
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkConnectivityLevel_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkConnectivityLevel __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkConnectivityLevel;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkCostType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkCostType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                enum NetworkCostType {
                    NetworkCostType_Unknown = 0,
                    NetworkCostType_Unrestricted = 1,
                    NetworkCostType_Fixed = 2,
                    NetworkCostType_Variable = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkCostType {
    NetworkCostType_Unknown = 0,
    NetworkCostType_Unrestricted = 1,
    NetworkCostType_Fixed = 2,
    NetworkCostType_Variable = 3
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define NetworkCostType __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkCostType
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkCostType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkCostType __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkCostType;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkEncryptionType_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkEncryptionType_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                enum NetworkEncryptionType {
                    NetworkEncryptionType_None = 0,
                    NetworkEncryptionType_Unknown = 1,
                    NetworkEncryptionType_Wep = 2,
                    NetworkEncryptionType_Wep40 = 3,
                    NetworkEncryptionType_Wep104 = 4,
                    NetworkEncryptionType_Tkip = 5,
                    NetworkEncryptionType_Ccmp = 6,
                    NetworkEncryptionType_WpaUseGroup = 7,
                    NetworkEncryptionType_RsnUseGroup = 8,
                    NetworkEncryptionType_Ihv = 9
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkEncryptionType {
    NetworkEncryptionType_None = 0,
    NetworkEncryptionType_Unknown = 1,
    NetworkEncryptionType_Wep = 2,
    NetworkEncryptionType_Wep40 = 3,
    NetworkEncryptionType_Wep104 = 4,
    NetworkEncryptionType_Tkip = 5,
    NetworkEncryptionType_Ccmp = 6,
    NetworkEncryptionType_WpaUseGroup = 7,
    NetworkEncryptionType_RsnUseGroup = 8,
    NetworkEncryptionType_Ihv = 9
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define NetworkEncryptionType __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkEncryptionType
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkEncryptionType_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkEncryptionType __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkEncryptionType;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkTypes_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkTypes_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                enum NetworkTypes {
                    NetworkTypes_None = 0x0,
                    NetworkTypes_Internet = 0x1,
                    NetworkTypes_PrivateNetwork = 0x2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkTypes {
    NetworkTypes_None = 0x0,
    NetworkTypes_Internet = 0x1,
    NetworkTypes_PrivateNetwork = 0x2
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define NetworkTypes __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkTypes
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CConnectivity_CNetworkTypes_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkTypes __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkTypes;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CRoamingStates_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CRoamingStates_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                enum RoamingStates {
                    RoamingStates_None = 0x0,
                    RoamingStates_NotRoaming = 0x1,
                    RoamingStates_Roaming = 0x2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CConnectivity_CRoamingStates {
    RoamingStates_None = 0x0,
    RoamingStates_NotRoaming = 0x1,
    RoamingStates_Roaming = 0x2
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define RoamingStates __x_ABI_CWindows_CNetworking_CConnectivity_CRoamingStates
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CConnectivity_CRoamingStates_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CConnectivity_CRoamingStates __x_ABI_CWindows_CNetworking_CConnectivity_CRoamingStates;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CDomainConnectivityLevel_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CDomainConnectivityLevel_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                enum DomainConnectivityLevel {
                    DomainConnectivityLevel_None = 0,
                    DomainConnectivityLevel_Unauthenticated = 1,
                    DomainConnectivityLevel_Authenticated = 2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CConnectivity_CDomainConnectivityLevel {
    DomainConnectivityLevel_None = 0,
    DomainConnectivityLevel_Unauthenticated = 1,
    DomainConnectivityLevel_Authenticated = 2
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define DomainConnectivityLevel __x_ABI_CWindows_CNetworking_CConnectivity_CDomainConnectivityLevel
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CConnectivity_CDomainConnectivityLevel_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CConnectivity_CDomainConnectivityLevel __x_ABI_CWindows_CNetworking_CConnectivity_CDomainConnectivityLevel;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CDataUsageGranularity_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CDataUsageGranularity_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                enum DataUsageGranularity {
                    DataUsageGranularity_PerMinute = 0,
                    DataUsageGranularity_PerHour = 1,
                    DataUsageGranularity_PerDay = 2,
                    DataUsageGranularity_Total = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CConnectivity_CDataUsageGranularity {
    DataUsageGranularity_PerMinute = 0,
    DataUsageGranularity_PerHour = 1,
    DataUsageGranularity_PerDay = 2,
    DataUsageGranularity_Total = 3
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define DataUsageGranularity __x_ABI_CWindows_CNetworking_CConnectivity_CDataUsageGranularity
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CConnectivity_CDataUsageGranularity_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CConnectivity_CDataUsageGranularity __x_ABI_CWindows_CNetworking_CConnectivity_CDataUsageGranularity;
#endif /* __cplusplus */

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CTriStates_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CTriStates_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                enum TriStates {
                    TriStates_DoNotCare = 0,
                    TriStates_No = 1,
                    TriStates_Yes = 2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CConnectivity_CTriStates {
    TriStates_DoNotCare = 0,
    TriStates_No = 1,
    TriStates_Yes = 2
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define TriStates __x_ABI_CWindows_CNetworking_CConnectivity_CTriStates
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CConnectivity_CTriStates_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CConnectivity_CTriStates __x_ABI_CWindows_CNetworking_CConnectivity_CTriStates;
#endif /* __cplusplus */

#if !defined(WINDOWS_NETWORKING_CONNECTIVITY_WWANCONTRACT_VERSION)
#define WINDOWS_NETWORKING_CONNECTIVITY_WWANCONTRACT_VERSION 0x20000
#endif // defined(WINDOWS_NETWORKING_CONNECTIVITY_WWANCONTRACT_VERSION)

#if WINDOWS_NETWORKING_CONNECTIVITY_WWANCONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CWwanNetworkRegistrationState_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CWwanNetworkRegistrationState_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                enum WwanNetworkRegistrationState {
                    WwanNetworkRegistrationState_None = 0,
                    WwanNetworkRegistrationState_Deregistered = 1,
                    WwanNetworkRegistrationState_Searching = 2,
                    WwanNetworkRegistrationState_Home = 3,
                    WwanNetworkRegistrationState_Roaming = 4,
                    WwanNetworkRegistrationState_Partner = 5,
                    WwanNetworkRegistrationState_Denied = 6
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CConnectivity_CWwanNetworkRegistrationState {
    WwanNetworkRegistrationState_None = 0,
    WwanNetworkRegistrationState_Deregistered = 1,
    WwanNetworkRegistrationState_Searching = 2,
    WwanNetworkRegistrationState_Home = 3,
    WwanNetworkRegistrationState_Roaming = 4,
    WwanNetworkRegistrationState_Partner = 5,
    WwanNetworkRegistrationState_Denied = 6
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define WwanNetworkRegistrationState __x_ABI_CWindows_CNetworking_CConnectivity_CWwanNetworkRegistrationState
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CConnectivity_CWwanNetworkRegistrationState_ENUM_DEFINED__ */
#endif /* WINDOWS_NETWORKING_CONNECTIVITY_WWANCONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CConnectivity_CWwanNetworkRegistrationState __x_ABI_CWindows_CNetworking_CConnectivity_CWwanNetworkRegistrationState;
#endif /* __cplusplus */

#if WINDOWS_NETWORKING_CONNECTIVITY_WWANCONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CWwanDataClass_ENUM_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CWwanDataClass_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                enum WwanDataClass {
                    WwanDataClass_None = 0x0,
                    WwanDataClass_Gprs = 0x1,
                    WwanDataClass_Edge = 0x2,
                    WwanDataClass_Umts = 0x4,
                    WwanDataClass_Hsdpa = 0x8,
                    WwanDataClass_Hsupa = 0x10,
                    WwanDataClass_LteAdvanced = 0x20,
                    WwanDataClass_Cdma1xRtt = 0x10000,
                    WwanDataClass_Cdma1xEvdo = 0x20000,
                    WwanDataClass_Cdma1xEvdoRevA = 0x40000,
                    WwanDataClass_Cdma1xEvdv = 0x80000,
                    WwanDataClass_Cdma3xRtt = 0x100000,
                    WwanDataClass_Cdma1xEvdoRevB = 0x200000,
                    WwanDataClass_CdmaUmb = 0x400000,
                    WwanDataClass_Custom = 0x80000000
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CNetworking_CConnectivity_CWwanDataClass {
    WwanDataClass_None = 0x0,
    WwanDataClass_Gprs = 0x1,
    WwanDataClass_Edge = 0x2,
    WwanDataClass_Umts = 0x4,
    WwanDataClass_Hsdpa = 0x8,
    WwanDataClass_Hsupa = 0x10,
    WwanDataClass_LteAdvanced = 0x20,
    WwanDataClass_Cdma1xRtt = 0x10000,
    WwanDataClass_Cdma1xEvdo = 0x20000,
    WwanDataClass_Cdma1xEvdoRevA = 0x40000,
    WwanDataClass_Cdma1xEvdv = 0x80000,
    WwanDataClass_Cdma3xRtt = 0x100000,
    WwanDataClass_Cdma1xEvdoRevB = 0x200000,
    WwanDataClass_CdmaUmb = 0x400000,
    WwanDataClass_Custom = 0x80000000
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define WwanDataClass __x_ABI_CWindows_CNetworking_CConnectivity_CWwanDataClass
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* ____x_ABI_CWindows_CNetworking_CConnectivity_CWwanDataClass_ENUM_DEFINED__ */
#endif /* WINDOWS_NETWORKING_CONNECTIVITY_WWANCONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CNetworking_CConnectivity_CWwanDataClass __x_ABI_CWindows_CNetworking_CConnectivity_CWwanDataClass;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsageStates __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsageStates;
#else /* __cplusplus */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                typedef struct NetworkUsageStates NetworkUsageStates;
            }
        }
    }
}
#endif /* __cplusplus */

#ifndef ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Networking::Connectivity::ConnectionProfile* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Networking::Connectivity::ProxyConfiguration* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIIterable_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CNetworking__CEndpointPair __FIIterable_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Networking::EndpointPair* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectionProfile* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::LanIdentifier* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CHostName_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CHostName_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CHostName __FIVectorView_1_Windows__CNetworking__CHostName;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CHostName ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::HostName* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CEndpointPair_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CEndpointPair __FIVectorView_1_Windows__CNetworking__CEndpointPair;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                struct NetworkUsageStates {
                    ABI::Windows::Networking::Connectivity::TriStates Roaming;
                    ABI::Windows::Networking::Connectivity::TriStates Shared;
                };
            }
        }
    }
}
extern "C" {
#else
struct __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsageStates {
    __x_ABI_CWindows_CNetworking_CConnectivity_CTriStates Roaming;
    __x_ABI_CWindows_CNetworking_CConnectivity_CTriStates Shared;
};
#ifdef WIDL_using_Windows_Networking_Connectivity
#define NetworkUsageStates __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsageStates
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * INetworkStatusChangedEventHandler interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler, 0x71ba143f, 0x598e, 0x49d0, 0x84,0xeb, 0x8f,0xeb,0xae,0xdc,0xc1,0x95);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("71ba143f-598e-49d0-84eb-8febaedcc195")
                INetworkStatusChangedEventHandler : public IUnknown
                {
                    virtual HRESULT STDMETHODCALLTYPE Invoke(
                        IInspectable *sender) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler, 0x71ba143f, 0x598e, 0x49d0, 0x84,0xeb, 0x8f,0xeb,0xae,0xdc,0xc1,0x95)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandlerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler *This);

    /*** INetworkStatusChangedEventHandler methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler *This,
        IInspectable *sender);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandlerVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandlerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_Release(This) (This)->lpVtbl->Release(This)
/*** INetworkStatusChangedEventHandler methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_Invoke(This,sender) (This)->lpVtbl->Invoke(This,sender)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler* This) {
    return This->lpVtbl->Release(This);
}
/*** INetworkStatusChangedEventHandler methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_Invoke(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler* This,IInspectable *sender) {
    return This->lpVtbl->Invoke(This,sender);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_INetworkStatusChangedEventHandler IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler
#define INetworkStatusChangedEventHandlerVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandlerVtbl
#define INetworkStatusChangedEventHandler __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler
#define INetworkStatusChangedEventHandler_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_QueryInterface
#define INetworkStatusChangedEventHandler_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_AddRef
#define INetworkStatusChangedEventHandler_Release __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_Release
#define INetworkStatusChangedEventHandler_Invoke __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_Invoke
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IConnectionCost interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost, 0xbad7d829, 0x3416, 0x4b10, 0xa2,0x02, 0xba,0xc0,0xb0,0x75,0xbd,0xae);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("bad7d829-3416-4b10-a202-bac0b075bdae")
                IConnectionCost : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_NetworkCostType(
                        ABI::Windows::Networking::Connectivity::NetworkCostType *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Roaming(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_OverDataLimit(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ApproachingDataLimit(
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost, 0xbad7d829, 0x3416, 0x4b10, 0xa2,0x02, 0xba,0xc0,0xb0,0x75,0xbd,0xae)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCostVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost *This,
        TrustLevel *trustLevel);

    /*** IConnectionCost methods ***/
    HRESULT (STDMETHODCALLTYPE *get_NetworkCostType)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkCostType *value);

    HRESULT (STDMETHODCALLTYPE *get_Roaming)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_OverDataLimit)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_ApproachingDataLimit)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCostVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCostVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IConnectionCost methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_NetworkCostType(This,value) (This)->lpVtbl->get_NetworkCostType(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_Roaming(This,value) (This)->lpVtbl->get_Roaming(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_OverDataLimit(This,value) (This)->lpVtbl->get_OverDataLimit(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_ApproachingDataLimit(This,value) (This)->lpVtbl->get_ApproachingDataLimit(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IConnectionCost methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_NetworkCostType(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost* This,__x_ABI_CWindows_CNetworking_CConnectivity_CNetworkCostType *value) {
    return This->lpVtbl->get_NetworkCostType(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_Roaming(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost* This,boolean *value) {
    return This->lpVtbl->get_Roaming(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_OverDataLimit(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost* This,boolean *value) {
    return This->lpVtbl->get_OverDataLimit(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_ApproachingDataLimit(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost* This,boolean *value) {
    return This->lpVtbl->get_ApproachingDataLimit(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_IConnectionCost IID___x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost
#define IConnectionCostVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCostVtbl
#define IConnectionCost __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost
#define IConnectionCost_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_QueryInterface
#define IConnectionCost_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_AddRef
#define IConnectionCost_Release __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_Release
#define IConnectionCost_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_GetIids
#define IConnectionCost_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_GetRuntimeClassName
#define IConnectionCost_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_GetTrustLevel
#define IConnectionCost_get_NetworkCostType __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_NetworkCostType
#define IConnectionCost_get_Roaming __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_Roaming
#define IConnectionCost_get_OverDataLimit __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_OverDataLimit
#define IConnectionCost_get_ApproachingDataLimit __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_get_ApproachingDataLimit
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IConnectionProfile interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile, 0x71ba143c, 0x598e, 0x49d0, 0x84,0xeb, 0x8f,0xeb,0xae,0xdc,0xc1,0x95);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("71ba143c-598e-49d0-84eb-8febaedcc195")
                IConnectionProfile : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_ProfileName(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNetworkConnectivityLevel(
                        ABI::Windows::Networking::Connectivity::NetworkConnectivityLevel *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNetworkNames(
                        ABI::Windows::Foundation::Collections::IVectorView<HSTRING > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetConnectionCost(
                        ABI::Windows::Networking::Connectivity::IConnectionCost **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDataPlanStatus(
                        ABI::Windows::Networking::Connectivity::IDataPlanStatus **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_NetworkAdapter(
                        ABI::Windows::Networking::Connectivity::INetworkAdapter **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetLocalUsage(
                        ABI::Windows::Foundation::DateTime start,
                        ABI::Windows::Foundation::DateTime end,
                        ABI::Windows::Networking::Connectivity::IDataUsage **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetLocalUsagePerRoamingStates(
                        ABI::Windows::Foundation::DateTime start,
                        ABI::Windows::Foundation::DateTime end,
                        ABI::Windows::Networking::Connectivity::RoamingStates states,
                        ABI::Windows::Networking::Connectivity::IDataUsage **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_NetworkSecuritySettings(
                        ABI::Windows::Networking::Connectivity::INetworkSecuritySettings **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile, 0x71ba143c, 0x598e, 0x49d0, 0x84,0xeb, 0x8f,0xeb,0xae,0xdc,0xc1,0x95)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        TrustLevel *trustLevel);

    /*** IConnectionProfile methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ProfileName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetNetworkConnectivityLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkConnectivityLevel *value);

    HRESULT (STDMETHODCALLTYPE *GetNetworkNames)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        __FIVectorView_1_HSTRING **value);

    HRESULT (STDMETHODCALLTYPE *GetConnectionCost)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost **value);

    HRESULT (STDMETHODCALLTYPE *GetDataPlanStatus)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus **value);

    HRESULT (STDMETHODCALLTYPE *get_NetworkAdapter)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter **value);

    HRESULT (STDMETHODCALLTYPE *GetLocalUsage)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        __x_ABI_CWindows_CFoundation_CDateTime start,
        __x_ABI_CWindows_CFoundation_CDateTime end,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage **value);

    HRESULT (STDMETHODCALLTYPE *GetLocalUsagePerRoamingStates)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        __x_ABI_CWindows_CFoundation_CDateTime start,
        __x_ABI_CWindows_CFoundation_CDateTime end,
        __x_ABI_CWindows_CNetworking_CConnectivity_CRoamingStates states,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage **value);

    HRESULT (STDMETHODCALLTYPE *get_NetworkSecuritySettings)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings **value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfileVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IConnectionProfile methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_get_ProfileName(This,value) (This)->lpVtbl->get_ProfileName(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetNetworkConnectivityLevel(This,value) (This)->lpVtbl->GetNetworkConnectivityLevel(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetNetworkNames(This,value) (This)->lpVtbl->GetNetworkNames(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetConnectionCost(This,value) (This)->lpVtbl->GetConnectionCost(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetDataPlanStatus(This,value) (This)->lpVtbl->GetDataPlanStatus(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_get_NetworkAdapter(This,value) (This)->lpVtbl->get_NetworkAdapter(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetLocalUsage(This,start,end,value) (This)->lpVtbl->GetLocalUsage(This,start,end,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetLocalUsagePerRoamingStates(This,start,end,states,value) (This)->lpVtbl->GetLocalUsagePerRoamingStates(This,start,end,states,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_get_NetworkSecuritySettings(This,value) (This)->lpVtbl->get_NetworkSecuritySettings(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IConnectionProfile methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_get_ProfileName(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,HSTRING *value) {
    return This->lpVtbl->get_ProfileName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetNetworkConnectivityLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,__x_ABI_CWindows_CNetworking_CConnectivity_CNetworkConnectivityLevel *value) {
    return This->lpVtbl->GetNetworkConnectivityLevel(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetNetworkNames(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,__FIVectorView_1_HSTRING **value) {
    return This->lpVtbl->GetNetworkNames(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetConnectionCost(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionCost **value) {
    return This->lpVtbl->GetConnectionCost(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetDataPlanStatus(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus **value) {
    return This->lpVtbl->GetDataPlanStatus(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_get_NetworkAdapter(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter **value) {
    return This->lpVtbl->get_NetworkAdapter(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetLocalUsage(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,__x_ABI_CWindows_CFoundation_CDateTime start,__x_ABI_CWindows_CFoundation_CDateTime end,__x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage **value) {
    return This->lpVtbl->GetLocalUsage(This,start,end,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetLocalUsagePerRoamingStates(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,__x_ABI_CWindows_CFoundation_CDateTime start,__x_ABI_CWindows_CFoundation_CDateTime end,__x_ABI_CWindows_CNetworking_CConnectivity_CRoamingStates states,__x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage **value) {
    return This->lpVtbl->GetLocalUsagePerRoamingStates(This,start,end,states,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_get_NetworkSecuritySettings(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile* This,__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings **value) {
    return This->lpVtbl->get_NetworkSecuritySettings(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_IConnectionProfile IID___x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile
#define IConnectionProfileVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfileVtbl
#define IConnectionProfile __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile
#define IConnectionProfile_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_QueryInterface
#define IConnectionProfile_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_AddRef
#define IConnectionProfile_Release __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_Release
#define IConnectionProfile_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetIids
#define IConnectionProfile_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetRuntimeClassName
#define IConnectionProfile_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetTrustLevel
#define IConnectionProfile_get_ProfileName __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_get_ProfileName
#define IConnectionProfile_GetNetworkConnectivityLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetNetworkConnectivityLevel
#define IConnectionProfile_GetNetworkNames __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetNetworkNames
#define IConnectionProfile_GetConnectionCost __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetConnectionCost
#define IConnectionProfile_GetDataPlanStatus __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetDataPlanStatus
#define IConnectionProfile_get_NetworkAdapter __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_get_NetworkAdapter
#define IConnectionProfile_GetLocalUsage __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetLocalUsage
#define IConnectionProfile_GetLocalUsagePerRoamingStates __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_GetLocalUsagePerRoamingStates
#define IConnectionProfile_get_NetworkSecuritySettings __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_get_NetworkSecuritySettings
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IConnectionProfile2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2, 0xe2045145, 0x4c9f, 0x400c, 0x91,0x50, 0x7e,0xc7,0xd6,0xe2,0x88,0x8a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("e2045145-4c9f-400c-9150-7ec7d6e2888a")
                IConnectionProfile2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_IsWwanConnectionProfile(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsWlanConnectionProfile(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_WwanConnectionProfileDetails(
                        ABI::Windows::Networking::Connectivity::IWwanConnectionProfileDetails **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_WlanConnectionProfileDetails(
                        ABI::Windows::Networking::Connectivity::IWlanConnectionProfileDetails **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ServiceProviderGuid(
                        ABI::Windows::Foundation::IReference<GUID > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetSignalBars(
                        ABI::Windows::Foundation::IReference<BYTE > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetDomainConnectivityLevel(
                        ABI::Windows::Networking::Connectivity::DomainConnectivityLevel *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNetworkUsageAsync(
                        ABI::Windows::Foundation::DateTime time_start,
                        ABI::Windows::Foundation::DateTime time_end,
                        ABI::Windows::Networking::Connectivity::DataUsageGranularity granularity,
                        ABI::Windows::Networking::Connectivity::NetworkUsageStates states,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetConnectivityIntervalsAsync(
                        ABI::Windows::Foundation::DateTime time_start,
                        ABI::Windows::Foundation::DateTime time_end,
                        ABI::Windows::Networking::Connectivity::NetworkUsageStates states,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2, 0xe2045145, 0x4c9f, 0x400c, 0x91,0x50, 0x7e,0xc7,0xd6,0xe2,0x88,0x8a)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        TrustLevel *trustLevel);

    /*** IConnectionProfile2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IsWwanConnectionProfile)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_IsWlanConnectionProfile)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *get_WwanConnectionProfileDetails)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails **value);

    HRESULT (STDMETHODCALLTYPE *get_WlanConnectionProfileDetails)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails **value);

    HRESULT (STDMETHODCALLTYPE *get_ServiceProviderGuid)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        __FIReference_1_GUID **value);

    HRESULT (STDMETHODCALLTYPE *GetSignalBars)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        __FIReference_1_BYTE **value);

    HRESULT (STDMETHODCALLTYPE *GetDomainConnectivityLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CDomainConnectivityLevel *value);

    HRESULT (STDMETHODCALLTYPE *GetNetworkUsageAsync)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        __x_ABI_CWindows_CFoundation_CDateTime time_start,
        __x_ABI_CWindows_CFoundation_CDateTime time_end,
        __x_ABI_CWindows_CNetworking_CConnectivity_CDataUsageGranularity granularity,
        __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsageStates states,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage **value);

    HRESULT (STDMETHODCALLTYPE *GetConnectivityIntervalsAsync)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 *This,
        __x_ABI_CWindows_CFoundation_CDateTime time_start,
        __x_ABI_CWindows_CFoundation_CDateTime time_end,
        __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsageStates states,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval **value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2Vtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2 {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IConnectionProfile2 methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_IsWwanConnectionProfile(This,value) (This)->lpVtbl->get_IsWwanConnectionProfile(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_IsWlanConnectionProfile(This,value) (This)->lpVtbl->get_IsWlanConnectionProfile(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_WwanConnectionProfileDetails(This,value) (This)->lpVtbl->get_WwanConnectionProfileDetails(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_WlanConnectionProfileDetails(This,value) (This)->lpVtbl->get_WlanConnectionProfileDetails(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_ServiceProviderGuid(This,value) (This)->lpVtbl->get_ServiceProviderGuid(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetSignalBars(This,value) (This)->lpVtbl->GetSignalBars(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetDomainConnectivityLevel(This,value) (This)->lpVtbl->GetDomainConnectivityLevel(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetNetworkUsageAsync(This,time_start,time_end,granularity,states,value) (This)->lpVtbl->GetNetworkUsageAsync(This,time_start,time_end,granularity,states,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetConnectivityIntervalsAsync(This,time_start,time_end,states,value) (This)->lpVtbl->GetConnectivityIntervalsAsync(This,time_start,time_end,states,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IConnectionProfile2 methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_IsWwanConnectionProfile(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,boolean *value) {
    return This->lpVtbl->get_IsWwanConnectionProfile(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_IsWlanConnectionProfile(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,boolean *value) {
    return This->lpVtbl->get_IsWlanConnectionProfile(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_WwanConnectionProfileDetails(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails **value) {
    return This->lpVtbl->get_WwanConnectionProfileDetails(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_WlanConnectionProfileDetails(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,__x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails **value) {
    return This->lpVtbl->get_WlanConnectionProfileDetails(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_ServiceProviderGuid(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,__FIReference_1_GUID **value) {
    return This->lpVtbl->get_ServiceProviderGuid(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetSignalBars(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,__FIReference_1_BYTE **value) {
    return This->lpVtbl->GetSignalBars(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetDomainConnectivityLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,__x_ABI_CWindows_CNetworking_CConnectivity_CDomainConnectivityLevel *value) {
    return This->lpVtbl->GetDomainConnectivityLevel(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetNetworkUsageAsync(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,__x_ABI_CWindows_CFoundation_CDateTime time_start,__x_ABI_CWindows_CFoundation_CDateTime time_end,__x_ABI_CWindows_CNetworking_CConnectivity_CDataUsageGranularity granularity,__x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsageStates states,__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage **value) {
    return This->lpVtbl->GetNetworkUsageAsync(This,time_start,time_end,granularity,states,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetConnectivityIntervalsAsync(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2* This,__x_ABI_CWindows_CFoundation_CDateTime time_start,__x_ABI_CWindows_CFoundation_CDateTime time_end,__x_ABI_CWindows_CNetworking_CConnectivity_CNetworkUsageStates states,__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval **value) {
    return This->lpVtbl->GetConnectivityIntervalsAsync(This,time_start,time_end,states,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_IConnectionProfile2 IID___x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2
#define IConnectionProfile2Vtbl __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2Vtbl
#define IConnectionProfile2 __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2
#define IConnectionProfile2_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_QueryInterface
#define IConnectionProfile2_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_AddRef
#define IConnectionProfile2_Release __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_Release
#define IConnectionProfile2_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetIids
#define IConnectionProfile2_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetRuntimeClassName
#define IConnectionProfile2_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetTrustLevel
#define IConnectionProfile2_get_IsWwanConnectionProfile __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_IsWwanConnectionProfile
#define IConnectionProfile2_get_IsWlanConnectionProfile __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_IsWlanConnectionProfile
#define IConnectionProfile2_get_WwanConnectionProfileDetails __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_WwanConnectionProfileDetails
#define IConnectionProfile2_get_WlanConnectionProfileDetails __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_WlanConnectionProfileDetails
#define IConnectionProfile2_get_ServiceProviderGuid __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_get_ServiceProviderGuid
#define IConnectionProfile2_GetSignalBars __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetSignalBars
#define IConnectionProfile2_GetDomainConnectivityLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetDomainConnectivityLevel
#define IConnectionProfile2_GetNetworkUsageAsync __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetNetworkUsageAsync
#define IConnectionProfile2_GetConnectivityIntervalsAsync __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_GetConnectivityIntervalsAsync
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDataPlanStatus interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus, 0x977a8b8c, 0x3885, 0x40f3, 0x88,0x51, 0x42,0xcd,0x2b,0xd5,0x68,0xbb);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("977a8b8c-3885-40f3-8851-42cd2bd568bb")
                IDataPlanStatus : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_DataPlanUsage(
                        ABI::Windows::Networking::Connectivity::IDataPlanUsage **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_DataLimitInMegabytes(
                        ABI::Windows::Foundation::IReference<UINT32 > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_InboundBitsPerSecond(
                        ABI::Windows::Foundation::IReference<UINT64 > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_OutboundBitsPerSecond(
                        ABI::Windows::Foundation::IReference<UINT64 > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_NextBillingCycle(
                        ABI::Windows::Foundation::IReference<ABI::Windows::Foundation::DateTime > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_MaxTransferSizeInMegabytes(
                        ABI::Windows::Foundation::IReference<UINT32 > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus, 0x977a8b8c, 0x3885, 0x40f3, 0x88,0x51, 0x42,0xcd,0x2b,0xd5,0x68,0xbb)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatusVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This,
        TrustLevel *trustLevel);

    /*** IDataPlanStatus methods ***/
    HRESULT (STDMETHODCALLTYPE *get_DataPlanUsage)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage **value);

    HRESULT (STDMETHODCALLTYPE *get_DataLimitInMegabytes)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This,
        __FIReference_1_UINT32 **value);

    HRESULT (STDMETHODCALLTYPE *get_InboundBitsPerSecond)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This,
        __FIReference_1_UINT64 **value);

    HRESULT (STDMETHODCALLTYPE *get_OutboundBitsPerSecond)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This,
        __FIReference_1_UINT64 **value);

    HRESULT (STDMETHODCALLTYPE *get_NextBillingCycle)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This,
        __FIReference_1_DateTime **value);

    HRESULT (STDMETHODCALLTYPE *get_MaxTransferSizeInMegabytes)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus *This,
        __FIReference_1_UINT32 **value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatusVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatusVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDataPlanStatus methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_DataPlanUsage(This,value) (This)->lpVtbl->get_DataPlanUsage(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_DataLimitInMegabytes(This,value) (This)->lpVtbl->get_DataLimitInMegabytes(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_InboundBitsPerSecond(This,value) (This)->lpVtbl->get_InboundBitsPerSecond(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_OutboundBitsPerSecond(This,value) (This)->lpVtbl->get_OutboundBitsPerSecond(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_NextBillingCycle(This,value) (This)->lpVtbl->get_NextBillingCycle(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_MaxTransferSizeInMegabytes(This,value) (This)->lpVtbl->get_MaxTransferSizeInMegabytes(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDataPlanStatus methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_DataPlanUsage(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This,__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage **value) {
    return This->lpVtbl->get_DataPlanUsage(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_DataLimitInMegabytes(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This,__FIReference_1_UINT32 **value) {
    return This->lpVtbl->get_DataLimitInMegabytes(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_InboundBitsPerSecond(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This,__FIReference_1_UINT64 **value) {
    return This->lpVtbl->get_InboundBitsPerSecond(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_OutboundBitsPerSecond(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This,__FIReference_1_UINT64 **value) {
    return This->lpVtbl->get_OutboundBitsPerSecond(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_NextBillingCycle(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This,__FIReference_1_DateTime **value) {
    return This->lpVtbl->get_NextBillingCycle(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_MaxTransferSizeInMegabytes(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus* This,__FIReference_1_UINT32 **value) {
    return This->lpVtbl->get_MaxTransferSizeInMegabytes(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_IDataPlanStatus IID___x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus
#define IDataPlanStatusVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatusVtbl
#define IDataPlanStatus __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus
#define IDataPlanStatus_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_QueryInterface
#define IDataPlanStatus_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_AddRef
#define IDataPlanStatus_Release __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_Release
#define IDataPlanStatus_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_GetIids
#define IDataPlanStatus_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_GetRuntimeClassName
#define IDataPlanStatus_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_GetTrustLevel
#define IDataPlanStatus_get_DataPlanUsage __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_DataPlanUsage
#define IDataPlanStatus_get_DataLimitInMegabytes __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_DataLimitInMegabytes
#define IDataPlanStatus_get_InboundBitsPerSecond __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_InboundBitsPerSecond
#define IDataPlanStatus_get_OutboundBitsPerSecond __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_OutboundBitsPerSecond
#define IDataPlanStatus_get_NextBillingCycle __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_NextBillingCycle
#define IDataPlanStatus_get_MaxTransferSizeInMegabytes __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_get_MaxTransferSizeInMegabytes
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanStatus_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDataPlanUsage interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage, 0xb921492d, 0x3b44, 0x47ff, 0xb3,0x61, 0xbe,0x59,0xe6,0x9e,0xd1,0xb0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("b921492d-3b44-47ff-b361-be59e69ed1b0")
                IDataPlanUsage : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_MegabytesUsed(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_LastSyncTime(
                        ABI::Windows::Foundation::DateTime *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage, 0xb921492d, 0x3b44, 0x47ff, 0xb3,0x61, 0xbe,0x59,0xe6,0x9e,0xd1,0xb0)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage *This,
        TrustLevel *trustLevel);

    /*** IDataPlanUsage methods ***/
    HRESULT (STDMETHODCALLTYPE *get_MegabytesUsed)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_LastSyncTime)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage *This,
        __x_ABI_CWindows_CFoundation_CDateTime *value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsageVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDataPlanUsage methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_get_MegabytesUsed(This,value) (This)->lpVtbl->get_MegabytesUsed(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_get_LastSyncTime(This,value) (This)->lpVtbl->get_LastSyncTime(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDataPlanUsage methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_get_MegabytesUsed(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage* This,UINT32 *value) {
    return This->lpVtbl->get_MegabytesUsed(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_get_LastSyncTime(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage* This,__x_ABI_CWindows_CFoundation_CDateTime *value) {
    return This->lpVtbl->get_LastSyncTime(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_IDataPlanUsage IID___x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage
#define IDataPlanUsageVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsageVtbl
#define IDataPlanUsage __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage
#define IDataPlanUsage_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_QueryInterface
#define IDataPlanUsage_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_AddRef
#define IDataPlanUsage_Release __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_Release
#define IDataPlanUsage_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_GetIids
#define IDataPlanUsage_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_GetRuntimeClassName
#define IDataPlanUsage_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_GetTrustLevel
#define IDataPlanUsage_get_MegabytesUsed __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_get_MegabytesUsed
#define IDataPlanUsage_get_LastSyncTime __x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_get_LastSyncTime
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataPlanUsage_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IDataUsage interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage, 0xc1431dd3, 0xb146, 0x4d39, 0xb9,0x59, 0x0c,0x69,0xb0,0x96,0xc5,0x12);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("c1431dd3-b146-4d39-b959-0c69b096c512")
                IDataUsage : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_BytesSent(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_BytesReceived(
                        UINT64 *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage, 0xc1431dd3, 0xb146, 0x4d39, 0xb9,0x59, 0x0c,0x69,0xb0,0x96,0xc5,0x12)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage *This,
        TrustLevel *trustLevel);

    /*** IDataUsage methods ***/
    HRESULT (STDMETHODCALLTYPE *get_BytesSent)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *get_BytesReceived)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage *This,
        UINT64 *value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsageVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IDataUsage methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_get_BytesSent(This,value) (This)->lpVtbl->get_BytesSent(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_get_BytesReceived(This,value) (This)->lpVtbl->get_BytesReceived(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IDataUsage methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_get_BytesSent(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage* This,UINT64 *value) {
    return This->lpVtbl->get_BytesSent(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_get_BytesReceived(__x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage* This,UINT64 *value) {
    return This->lpVtbl->get_BytesReceived(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_IDataUsage IID___x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage
#define IDataUsageVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsageVtbl
#define IDataUsage __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage
#define IDataUsage_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_QueryInterface
#define IDataUsage_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_AddRef
#define IDataUsage_Release __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_Release
#define IDataUsage_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_GetIids
#define IDataUsage_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_GetRuntimeClassName
#define IDataUsage_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_GetTrustLevel
#define IDataUsage_get_BytesSent __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_get_BytesSent
#define IDataUsage_get_BytesReceived __x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_get_BytesReceived
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CIDataUsage_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIPInformation interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation, 0xd85145e0, 0x138f, 0x47d7, 0x9b,0x3a, 0x36,0xbb,0x48,0x8c,0xef,0x33);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("d85145e0-138f-47d7-9b3a-36bb488cef33")
                IIPInformation : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_NetworkAdapter(
                        ABI::Windows::Networking::Connectivity::INetworkAdapter **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PrefixLength(
                        ABI::Windows::Foundation::IReference<BYTE > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation, 0xd85145e0, 0x138f, 0x47d7, 0x9b,0x3a, 0x36,0xbb,0x48,0x8c,0xef,0x33)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation *This,
        TrustLevel *trustLevel);

    /*** IIPInformation methods ***/
    HRESULT (STDMETHODCALLTYPE *get_NetworkAdapter)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter **value);

    HRESULT (STDMETHODCALLTYPE *get_PrefixLength)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation *This,
        __FIReference_1_BYTE **value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformationVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIPInformation methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_get_NetworkAdapter(This,value) (This)->lpVtbl->get_NetworkAdapter(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_get_PrefixLength(This,value) (This)->lpVtbl->get_PrefixLength(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIPInformation methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_get_NetworkAdapter(__x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation* This,__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter **value) {
    return This->lpVtbl->get_NetworkAdapter(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_get_PrefixLength(__x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation* This,__FIReference_1_BYTE **value) {
    return This->lpVtbl->get_PrefixLength(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_IIPInformation IID___x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation
#define IIPInformationVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformationVtbl
#define IIPInformation __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation
#define IIPInformation_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_QueryInterface
#define IIPInformation_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_AddRef
#define IIPInformation_Release __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_Release
#define IIPInformation_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_GetIids
#define IIPInformation_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_GetRuntimeClassName
#define IIPInformation_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_GetTrustLevel
#define IIPInformation_get_NetworkAdapter __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_get_NetworkAdapter
#define IIPInformation_get_PrefixLength __x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_get_PrefixLength
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CIIPInformation_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ILanIdentifier interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier, 0x48aa53aa, 0x1108, 0x4546, 0xa6,0xcb, 0x9a,0x74,0xda,0x4b,0x7b,0xa0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("48aa53aa-1108-4546-a6cb-9a74da4b7ba0")
                ILanIdentifier : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_InfrastructureId(
                        ABI::Windows::Networking::Connectivity::ILanIdentifierData **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PortId(
                        ABI::Windows::Networking::Connectivity::ILanIdentifierData **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_NetworkAdapterId(
                        GUID *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier, 0x48aa53aa, 0x1108, 0x4546, 0xa6,0xcb, 0x9a,0x74,0xda,0x4b,0x7b,0xa0)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier *This,
        TrustLevel *trustLevel);

    /*** ILanIdentifier methods ***/
    HRESULT (STDMETHODCALLTYPE *get_InfrastructureId)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData **value);

    HRESULT (STDMETHODCALLTYPE *get_PortId)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData **value);

    HRESULT (STDMETHODCALLTYPE *get_NetworkAdapterId)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier *This,
        GUID *value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ILanIdentifier methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_get_InfrastructureId(This,value) (This)->lpVtbl->get_InfrastructureId(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_get_PortId(This,value) (This)->lpVtbl->get_PortId(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_get_NetworkAdapterId(This,value) (This)->lpVtbl->get_NetworkAdapterId(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ILanIdentifier methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_get_InfrastructureId(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier* This,__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData **value) {
    return This->lpVtbl->get_InfrastructureId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_get_PortId(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier* This,__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData **value) {
    return This->lpVtbl->get_PortId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_get_NetworkAdapterId(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier* This,GUID *value) {
    return This->lpVtbl->get_NetworkAdapterId(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_ILanIdentifier IID___x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier
#define ILanIdentifierVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierVtbl
#define ILanIdentifier __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier
#define ILanIdentifier_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_QueryInterface
#define ILanIdentifier_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_AddRef
#define ILanIdentifier_Release __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_Release
#define ILanIdentifier_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_GetIids
#define ILanIdentifier_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_GetRuntimeClassName
#define ILanIdentifier_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_GetTrustLevel
#define ILanIdentifier_get_InfrastructureId __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_get_InfrastructureId
#define ILanIdentifier_get_PortId __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_get_PortId
#define ILanIdentifier_get_NetworkAdapterId __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_get_NetworkAdapterId
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ILanIdentifierData interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData, 0xa74e83c3, 0xd639, 0x45be, 0xa3,0x6a, 0xc4,0xe4,0xae,0xaf,0x6d,0x9b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("a74e83c3-d639-45be-a36a-c4e4aeaf6d9b")
                ILanIdentifierData : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Type(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Value(
                        ABI::Windows::Foundation::Collections::IVectorView<BYTE > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData, 0xa74e83c3, 0xd639, 0x45be, 0xa3,0x6a, 0xc4,0xe4,0xae,0xaf,0x6d,0x9b)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierDataVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData *This,
        TrustLevel *trustLevel);

    /*** ILanIdentifierData methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Type)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_Value)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData *This,
        __FIVectorView_1_BYTE **value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierDataVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierDataVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ILanIdentifierData methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_get_Type(This,value) (This)->lpVtbl->get_Type(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_get_Value(This,value) (This)->lpVtbl->get_Value(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ILanIdentifierData methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_get_Type(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData* This,UINT32 *value) {
    return This->lpVtbl->get_Type(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_get_Value(__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData* This,__FIVectorView_1_BYTE **value) {
    return This->lpVtbl->get_Value(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_ILanIdentifierData IID___x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData
#define ILanIdentifierDataVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierDataVtbl
#define ILanIdentifierData __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData
#define ILanIdentifierData_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_QueryInterface
#define ILanIdentifierData_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_AddRef
#define ILanIdentifierData_Release __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_Release
#define ILanIdentifierData_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_GetIids
#define ILanIdentifierData_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_GetRuntimeClassName
#define ILanIdentifierData_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_GetTrustLevel
#define ILanIdentifierData_get_Type __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_get_Type
#define ILanIdentifierData_get_Value __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_get_Value
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifierData_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * INetworkAdapter interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter, 0x3b542e03, 0x5388, 0x496c, 0xa8,0xa3, 0xaf,0xfd,0x39,0xae,0xc2,0xe6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("3b542e03-5388-496c-a8a3-affd39aec2e6")
                INetworkAdapter : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_OutboundMaxBitsPerSecond(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_InboundMaxBitsPerSecond(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IanaInterfaceType(
                        UINT32 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_NetworkItem(
                        ABI::Windows::Networking::Connectivity::INetworkItem **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_NetworkAdapterId(
                        GUID *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetConnectedProfileAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Networking::Connectivity::ConnectionProfile* > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter, 0x3b542e03, 0x5388, 0x496c, 0xa8,0xa3, 0xaf,0xfd,0x39,0xae,0xc2,0xe6)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapterVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This,
        TrustLevel *trustLevel);

    /*** INetworkAdapter methods ***/
    HRESULT (STDMETHODCALLTYPE *get_OutboundMaxBitsPerSecond)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *get_InboundMaxBitsPerSecond)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *get_IanaInterfaceType)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *get_NetworkItem)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem **value);

    HRESULT (STDMETHODCALLTYPE *get_NetworkAdapterId)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This,
        GUID *value);

    HRESULT (STDMETHODCALLTYPE *GetConnectedProfileAsync)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter *This,
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile **value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapterVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapterVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** INetworkAdapter methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_OutboundMaxBitsPerSecond(This,value) (This)->lpVtbl->get_OutboundMaxBitsPerSecond(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_InboundMaxBitsPerSecond(This,value) (This)->lpVtbl->get_InboundMaxBitsPerSecond(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_IanaInterfaceType(This,value) (This)->lpVtbl->get_IanaInterfaceType(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_NetworkItem(This,value) (This)->lpVtbl->get_NetworkItem(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_NetworkAdapterId(This,value) (This)->lpVtbl->get_NetworkAdapterId(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetConnectedProfileAsync(This,value) (This)->lpVtbl->GetConnectedProfileAsync(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** INetworkAdapter methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_OutboundMaxBitsPerSecond(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This,UINT64 *value) {
    return This->lpVtbl->get_OutboundMaxBitsPerSecond(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_InboundMaxBitsPerSecond(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This,UINT64 *value) {
    return This->lpVtbl->get_InboundMaxBitsPerSecond(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_IanaInterfaceType(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This,UINT32 *value) {
    return This->lpVtbl->get_IanaInterfaceType(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_NetworkItem(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This,__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem **value) {
    return This->lpVtbl->get_NetworkItem(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_NetworkAdapterId(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This,GUID *value) {
    return This->lpVtbl->get_NetworkAdapterId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetConnectedProfileAsync(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter* This,__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile **value) {
    return This->lpVtbl->GetConnectedProfileAsync(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_INetworkAdapter IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter
#define INetworkAdapterVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapterVtbl
#define INetworkAdapter __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter
#define INetworkAdapter_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_QueryInterface
#define INetworkAdapter_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_AddRef
#define INetworkAdapter_Release __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_Release
#define INetworkAdapter_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetIids
#define INetworkAdapter_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetRuntimeClassName
#define INetworkAdapter_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetTrustLevel
#define INetworkAdapter_get_OutboundMaxBitsPerSecond __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_OutboundMaxBitsPerSecond
#define INetworkAdapter_get_InboundMaxBitsPerSecond __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_InboundMaxBitsPerSecond
#define INetworkAdapter_get_IanaInterfaceType __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_IanaInterfaceType
#define INetworkAdapter_get_NetworkItem __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_NetworkItem
#define INetworkAdapter_get_NetworkAdapterId __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_get_NetworkAdapterId
#define INetworkAdapter_GetConnectedProfileAsync __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_GetConnectedProfileAsync
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkAdapter_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * INetworkInformationStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics, 0x5074f851, 0x950d, 0x4165, 0x9c,0x15, 0x36,0x56,0x19,0x48,0x1e,0xea);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("5074f851-950d-4165-9c15-365619481eea")
                INetworkInformationStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetConnectionProfiles(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectionProfile* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetInternetConnectionProfile(
                        ABI::Windows::Networking::Connectivity::IConnectionProfile **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetLanIdentifiers(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::LanIdentifier* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetHostNames(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::HostName* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetProxyConfigurationAsync(
                        ABI::Windows::Foundation::IUriRuntimeClass *uri,
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Networking::Connectivity::ProxyConfiguration* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetSortedEndpointPairs(
                        ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Networking::EndpointPair* > *endpoint,
                        ABI::Windows::Networking::HostNameSortOptions options,
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::EndpointPair* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_NetworkStatusChanged(
                        ABI::Windows::Networking::Connectivity::INetworkStatusChangedEventHandler *handler,
                        EventRegistrationToken *cookie) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_NetworkStatusChanged(
                        EventRegistrationToken cookie) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics, 0x5074f851, 0x950d, 0x4165, 0x9c,0x15, 0x36,0x56,0x19,0x48,0x1e,0xea)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        TrustLevel *trustLevel);

    /*** INetworkInformationStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *GetConnectionProfiles)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile **value);

    HRESULT (STDMETHODCALLTYPE *GetInternetConnectionProfile)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile **value);

    HRESULT (STDMETHODCALLTYPE *GetLanIdentifiers)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier **value);

    HRESULT (STDMETHODCALLTYPE *GetHostNames)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        __FIVectorView_1_Windows__CNetworking__CHostName **value);

    HRESULT (STDMETHODCALLTYPE *GetProxyConfigurationAsync)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        __x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration **value);

    HRESULT (STDMETHODCALLTYPE *GetSortedEndpointPairs)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        __FIIterable_1_Windows__CNetworking__CEndpointPair *endpoint,
        __x_ABI_CWindows_CNetworking_CHostNameSortOptions options,
        __FIVectorView_1_Windows__CNetworking__CEndpointPair **value);

    HRESULT (STDMETHODCALLTYPE *add_NetworkStatusChanged)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler *handler,
        EventRegistrationToken *cookie);

    HRESULT (STDMETHODCALLTYPE *remove_NetworkStatusChanged)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics *This,
        EventRegistrationToken cookie);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStaticsVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** INetworkInformationStatics methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetConnectionProfiles(This,value) (This)->lpVtbl->GetConnectionProfiles(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetInternetConnectionProfile(This,value) (This)->lpVtbl->GetInternetConnectionProfile(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetLanIdentifiers(This,value) (This)->lpVtbl->GetLanIdentifiers(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetHostNames(This,value) (This)->lpVtbl->GetHostNames(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetProxyConfigurationAsync(This,uri,value) (This)->lpVtbl->GetProxyConfigurationAsync(This,uri,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetSortedEndpointPairs(This,endpoint,options,value) (This)->lpVtbl->GetSortedEndpointPairs(This,endpoint,options,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_add_NetworkStatusChanged(This,handler,cookie) (This)->lpVtbl->add_NetworkStatusChanged(This,handler,cookie)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_remove_NetworkStatusChanged(This,cookie) (This)->lpVtbl->remove_NetworkStatusChanged(This,cookie)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** INetworkInformationStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetConnectionProfiles(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile **value) {
    return This->lpVtbl->GetConnectionProfiles(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetInternetConnectionProfile(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile **value) {
    return This->lpVtbl->GetInternetConnectionProfile(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetLanIdentifiers(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier **value) {
    return This->lpVtbl->GetLanIdentifiers(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetHostNames(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,__FIVectorView_1_Windows__CNetworking__CHostName **value) {
    return This->lpVtbl->GetHostNames(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetProxyConfigurationAsync(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,__x_ABI_CWindows_CFoundation_CIUriRuntimeClass *uri,__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration **value) {
    return This->lpVtbl->GetProxyConfigurationAsync(This,uri,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetSortedEndpointPairs(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,__FIIterable_1_Windows__CNetworking__CEndpointPair *endpoint,__x_ABI_CWindows_CNetworking_CHostNameSortOptions options,__FIVectorView_1_Windows__CNetworking__CEndpointPair **value) {
    return This->lpVtbl->GetSortedEndpointPairs(This,endpoint,options,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_add_NetworkStatusChanged(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkStatusChangedEventHandler *handler,EventRegistrationToken *cookie) {
    return This->lpVtbl->add_NetworkStatusChanged(This,handler,cookie);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_remove_NetworkStatusChanged(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics* This,EventRegistrationToken cookie) {
    return This->lpVtbl->remove_NetworkStatusChanged(This,cookie);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_INetworkInformationStatics IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics
#define INetworkInformationStaticsVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStaticsVtbl
#define INetworkInformationStatics __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics
#define INetworkInformationStatics_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_QueryInterface
#define INetworkInformationStatics_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_AddRef
#define INetworkInformationStatics_Release __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_Release
#define INetworkInformationStatics_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetIids
#define INetworkInformationStatics_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetRuntimeClassName
#define INetworkInformationStatics_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetTrustLevel
#define INetworkInformationStatics_GetConnectionProfiles __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetConnectionProfiles
#define INetworkInformationStatics_GetInternetConnectionProfile __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetInternetConnectionProfile
#define INetworkInformationStatics_GetLanIdentifiers __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetLanIdentifiers
#define INetworkInformationStatics_GetHostNames __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetHostNames
#define INetworkInformationStatics_GetProxyConfigurationAsync __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetProxyConfigurationAsync
#define INetworkInformationStatics_GetSortedEndpointPairs __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_GetSortedEndpointPairs
#define INetworkInformationStatics_add_NetworkStatusChanged __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_add_NetworkStatusChanged
#define INetworkInformationStatics_remove_NetworkStatusChanged __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_remove_NetworkStatusChanged
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkInformationStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * INetworkItem interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem, 0x01bc4d39, 0xf5e0, 0x4567, 0xa2,0x8c, 0x42,0x08,0x0c,0x83,0x1b,0x2b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("01bc4d39-f5e0-4567-a28c-42080c831b2b")
                INetworkItem : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_NetworkId(
                        GUID *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNetworkTypes(
                        ABI::Windows::Networking::Connectivity::NetworkTypes *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem, 0x01bc4d39, 0xf5e0, 0x4567, 0xa2,0x8c, 0x42,0x08,0x0c,0x83,0x1b,0x2b)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItemVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem *This,
        TrustLevel *trustLevel);

    /*** INetworkItem methods ***/
    HRESULT (STDMETHODCALLTYPE *get_NetworkId)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem *This,
        GUID *value);

    HRESULT (STDMETHODCALLTYPE *GetNetworkTypes)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkTypes *value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItemVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItemVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** INetworkItem methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_get_NetworkId(This,value) (This)->lpVtbl->get_NetworkId(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetNetworkTypes(This,value) (This)->lpVtbl->GetNetworkTypes(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** INetworkItem methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_get_NetworkId(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem* This,GUID *value) {
    return This->lpVtbl->get_NetworkId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetNetworkTypes(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem* This,__x_ABI_CWindows_CNetworking_CConnectivity_CNetworkTypes *value) {
    return This->lpVtbl->GetNetworkTypes(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_INetworkItem IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem
#define INetworkItemVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItemVtbl
#define INetworkItem __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem
#define INetworkItem_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_QueryInterface
#define INetworkItem_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_AddRef
#define INetworkItem_Release __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_Release
#define INetworkItem_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetIids
#define INetworkItem_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetRuntimeClassName
#define INetworkItem_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetTrustLevel
#define INetworkItem_get_NetworkId __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_get_NetworkId
#define INetworkItem_GetNetworkTypes __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_GetNetworkTypes
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkItem_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * INetworkSecuritySettings interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings, 0x7ca07e8d, 0x917b, 0x4b5f, 0xb8,0x4d, 0x28,0xf7,0xa5,0xac,0x54,0x02);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("7ca07e8d-917b-4b5f-b84d-28f7a5ac5402")
                INetworkSecuritySettings : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_NetworkAuthenticationType(
                        ABI::Windows::Networking::Connectivity::NetworkAuthenticationType *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_NetworkEncryptionType(
                        ABI::Windows::Networking::Connectivity::NetworkEncryptionType *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings, 0x7ca07e8d, 0x917b, 0x4b5f, 0xb8,0x4d, 0x28,0xf7,0xa5,0xac,0x54,0x02)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettingsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings *This,
        TrustLevel *trustLevel);

    /*** INetworkSecuritySettings methods ***/
    HRESULT (STDMETHODCALLTYPE *get_NetworkAuthenticationType)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAuthenticationType *value);

    HRESULT (STDMETHODCALLTYPE *get_NetworkEncryptionType)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CNetworkEncryptionType *value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettingsVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettingsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** INetworkSecuritySettings methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_get_NetworkAuthenticationType(This,value) (This)->lpVtbl->get_NetworkAuthenticationType(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_get_NetworkEncryptionType(This,value) (This)->lpVtbl->get_NetworkEncryptionType(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** INetworkSecuritySettings methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_get_NetworkAuthenticationType(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings* This,__x_ABI_CWindows_CNetworking_CConnectivity_CNetworkAuthenticationType *value) {
    return This->lpVtbl->get_NetworkAuthenticationType(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_get_NetworkEncryptionType(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings* This,__x_ABI_CWindows_CNetworking_CConnectivity_CNetworkEncryptionType *value) {
    return This->lpVtbl->get_NetworkEncryptionType(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_INetworkSecuritySettings IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings
#define INetworkSecuritySettingsVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettingsVtbl
#define INetworkSecuritySettings __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings
#define INetworkSecuritySettings_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_QueryInterface
#define INetworkSecuritySettings_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_AddRef
#define INetworkSecuritySettings_Release __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_Release
#define INetworkSecuritySettings_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_GetIids
#define INetworkSecuritySettings_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_GetRuntimeClassName
#define INetworkSecuritySettings_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_GetTrustLevel
#define INetworkSecuritySettings_get_NetworkAuthenticationType __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_get_NetworkAuthenticationType
#define INetworkSecuritySettings_get_NetworkEncryptionType __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_get_NetworkEncryptionType
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkSecuritySettings_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IProxyConfiguration interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration, 0xef3a60b4, 0x9004, 0x4dd6, 0xb7,0xd8, 0xb3,0xe5,0x02,0xf4,0xaa,0xd0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("ef3a60b4-9004-4dd6-b7d8-b3e502f4aad0")
                IProxyConfiguration : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_ProxyUris(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Foundation::Uri* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_CanConnectDirectly(
                        boolean *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration, 0xef3a60b4, 0x9004, 0x4dd6, 0xb7,0xd8, 0xb3,0xe5,0x02,0xf4,0xaa,0xd0)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfigurationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration *This,
        TrustLevel *trustLevel);

    /*** IProxyConfiguration methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ProxyUris)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration *This,
        __FIVectorView_1_Windows__CFoundation__CUri **value);

    HRESULT (STDMETHODCALLTYPE *get_CanConnectDirectly)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration *This,
        boolean *value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfigurationVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfigurationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IProxyConfiguration methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_get_ProxyUris(This,value) (This)->lpVtbl->get_ProxyUris(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_get_CanConnectDirectly(This,value) (This)->lpVtbl->get_CanConnectDirectly(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IProxyConfiguration methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_get_ProxyUris(__x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration* This,__FIVectorView_1_Windows__CFoundation__CUri **value) {
    return This->lpVtbl->get_ProxyUris(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_get_CanConnectDirectly(__x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration* This,boolean *value) {
    return This->lpVtbl->get_CanConnectDirectly(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_IProxyConfiguration IID___x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration
#define IProxyConfigurationVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfigurationVtbl
#define IProxyConfiguration __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration
#define IProxyConfiguration_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_QueryInterface
#define IProxyConfiguration_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_AddRef
#define IProxyConfiguration_Release __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_Release
#define IProxyConfiguration_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_GetIids
#define IProxyConfiguration_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_GetRuntimeClassName
#define IProxyConfiguration_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_GetTrustLevel
#define IProxyConfiguration_get_ProxyUris __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_get_ProxyUris
#define IProxyConfiguration_get_CanConnectDirectly __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_get_CanConnectDirectly
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * INetworkUsage interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage, 0x49da8fce, 0x9985, 0x4927, 0xbf,0x5b, 0x07,0x2b,0x5c,0x65,0xf8,0xd9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("49da8fce-9985-4927-bf5b-072b5c65f8d9")
                INetworkUsage : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_BytesSent(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_BytesReceived(
                        UINT64 *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ConnectionDuration(
                        ABI::Windows::Foundation::TimeSpan *duration) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage, 0x49da8fce, 0x9985, 0x4927, 0xbf,0x5b, 0x07,0x2b,0x5c,0x65,0xf8,0xd9)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage *This,
        TrustLevel *trustLevel);

    /*** INetworkUsage methods ***/
    HRESULT (STDMETHODCALLTYPE *get_BytesSent)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *get_BytesReceived)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage *This,
        UINT64 *value);

    HRESULT (STDMETHODCALLTYPE *get_ConnectionDuration)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *duration);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsageVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** INetworkUsage methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_get_BytesSent(This,value) (This)->lpVtbl->get_BytesSent(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_get_BytesReceived(This,value) (This)->lpVtbl->get_BytesReceived(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_get_ConnectionDuration(This,duration) (This)->lpVtbl->get_ConnectionDuration(This,duration)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** INetworkUsage methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_get_BytesSent(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage* This,UINT64 *value) {
    return This->lpVtbl->get_BytesSent(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_get_BytesReceived(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage* This,UINT64 *value) {
    return This->lpVtbl->get_BytesReceived(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_get_ConnectionDuration(__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage* This,__x_ABI_CWindows_CFoundation_CTimeSpan *duration) {
    return This->lpVtbl->get_ConnectionDuration(This,duration);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_INetworkUsage IID___x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage
#define INetworkUsageVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsageVtbl
#define INetworkUsage __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage
#define INetworkUsage_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_QueryInterface
#define INetworkUsage_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_AddRef
#define INetworkUsage_Release __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_Release
#define INetworkUsage_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_GetIids
#define INetworkUsage_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_GetRuntimeClassName
#define INetworkUsage_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_GetTrustLevel
#define INetworkUsage_get_BytesSent __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_get_BytesSent
#define INetworkUsage_get_BytesReceived __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_get_BytesReceived
#define INetworkUsage_get_ConnectionDuration __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_get_ConnectionDuration
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IConnectivityInterval interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval, 0x4faa3fff, 0x6746, 0x4824, 0xa9,0x64, 0xee,0xd8,0xe8,0x7f,0x87,0x09);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("4faa3fff-6746-4824-a964-eed8e87f8709")
                IConnectivityInterval : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_StartTime(
                        ABI::Windows::Foundation::DateTime *time_start) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ConnectionDuration(
                        ABI::Windows::Foundation::TimeSpan *duration) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval, 0x4faa3fff, 0x6746, 0x4824, 0xa9,0x64, 0xee,0xd8,0xe8,0x7f,0x87,0x09)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityIntervalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval *This,
        TrustLevel *trustLevel);

    /*** IConnectivityInterval methods ***/
    HRESULT (STDMETHODCALLTYPE *get_StartTime)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval *This,
        __x_ABI_CWindows_CFoundation_CDateTime *time_start);

    HRESULT (STDMETHODCALLTYPE *get_ConnectionDuration)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval *This,
        __x_ABI_CWindows_CFoundation_CTimeSpan *duration);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityIntervalVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityIntervalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IConnectivityInterval methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_get_StartTime(This,time_start) (This)->lpVtbl->get_StartTime(This,time_start)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_get_ConnectionDuration(This,duration) (This)->lpVtbl->get_ConnectionDuration(This,duration)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IConnectivityInterval methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_get_StartTime(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval* This,__x_ABI_CWindows_CFoundation_CDateTime *time_start) {
    return This->lpVtbl->get_StartTime(This,time_start);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_get_ConnectionDuration(__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval* This,__x_ABI_CWindows_CFoundation_CTimeSpan *duration) {
    return This->lpVtbl->get_ConnectionDuration(This,duration);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_IConnectivityInterval IID___x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval
#define IConnectivityIntervalVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityIntervalVtbl
#define IConnectivityInterval __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval
#define IConnectivityInterval_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_QueryInterface
#define IConnectivityInterval_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_AddRef
#define IConnectivityInterval_Release __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_Release
#define IConnectivityInterval_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_GetIids
#define IConnectivityInterval_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_GetRuntimeClassName
#define IConnectivityInterval_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_GetTrustLevel
#define IConnectivityInterval_get_StartTime __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_get_StartTime
#define IConnectivityInterval_get_ConnectionDuration __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_get_ConnectionDuration
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IWwanConnectionProfileDetails interface
 */
#if WINDOWS_NETWORKING_CONNECTIVITY_WWANCONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails, 0x0e4da8fe, 0x835f, 0x4df3, 0x82,0xfd, 0xdf,0x55,0x6e,0xbc,0x09,0xef);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("0e4da8fe-835f-4df3-82fd-df556ebc09ef")
                IWwanConnectionProfileDetails : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_HomeProviderId(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_AccessPointName(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetNetworkRegistrationState(
                        ABI::Windows::Networking::Connectivity::WwanNetworkRegistrationState *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetCurrentDataClass(
                        ABI::Windows::Networking::Connectivity::WwanDataClass *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails, 0x0e4da8fe, 0x835f, 0x4df3, 0x82,0xfd, 0xdf,0x55,0x6e,0xbc,0x09,0xef)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetailsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails *This,
        TrustLevel *trustLevel);

    /*** IWwanConnectionProfileDetails methods ***/
    HRESULT (STDMETHODCALLTYPE *get_HomeProviderId)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_AccessPointName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *GetNetworkRegistrationState)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CWwanNetworkRegistrationState *value);

    HRESULT (STDMETHODCALLTYPE *GetCurrentDataClass)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CWwanDataClass *value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetailsVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetailsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IWwanConnectionProfileDetails methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_get_HomeProviderId(This,value) (This)->lpVtbl->get_HomeProviderId(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_get_AccessPointName(This,value) (This)->lpVtbl->get_AccessPointName(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetNetworkRegistrationState(This,value) (This)->lpVtbl->GetNetworkRegistrationState(This,value)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetCurrentDataClass(This,value) (This)->lpVtbl->GetCurrentDataClass(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IWwanConnectionProfileDetails methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_get_HomeProviderId(__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails* This,HSTRING *value) {
    return This->lpVtbl->get_HomeProviderId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_get_AccessPointName(__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails* This,HSTRING *value) {
    return This->lpVtbl->get_AccessPointName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetNetworkRegistrationState(__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails* This,__x_ABI_CWindows_CNetworking_CConnectivity_CWwanNetworkRegistrationState *value) {
    return This->lpVtbl->GetNetworkRegistrationState(This,value);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetCurrentDataClass(__x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails* This,__x_ABI_CWindows_CNetworking_CConnectivity_CWwanDataClass *value) {
    return This->lpVtbl->GetCurrentDataClass(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_IWwanConnectionProfileDetails IID___x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails
#define IWwanConnectionProfileDetailsVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetailsVtbl
#define IWwanConnectionProfileDetails __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails
#define IWwanConnectionProfileDetails_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_QueryInterface
#define IWwanConnectionProfileDetails_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_AddRef
#define IWwanConnectionProfileDetails_Release __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_Release
#define IWwanConnectionProfileDetails_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetIids
#define IWwanConnectionProfileDetails_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetRuntimeClassName
#define IWwanConnectionProfileDetails_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetTrustLevel
#define IWwanConnectionProfileDetails_get_HomeProviderId __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_get_HomeProviderId
#define IWwanConnectionProfileDetails_get_AccessPointName __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_get_AccessPointName
#define IWwanConnectionProfileDetails_GetNetworkRegistrationState __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetNetworkRegistrationState
#define IWwanConnectionProfileDetails_GetCurrentDataClass __x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_GetCurrentDataClass
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CIWwanConnectionProfileDetails_INTERFACE_DEFINED__ */
#endif /* WINDOWS_NETWORKING_CONNECTIVITY_WWANCONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IWlanConnectionProfileDetails interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails, 0x562098cb, 0xb35a, 0x4bf1, 0xa8,0x84, 0xb7,0x55,0x7e,0x88,0xff,0x86);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Networking {
            namespace Connectivity {
                MIDL_INTERFACE("562098cb-b35a-4bf1-a884-b7557e88ff86")
                IWlanConnectionProfileDetails : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE GetConnectedSsid(
                        HSTRING *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails, 0x562098cb, 0xb35a, 0x4bf1, 0xa8,0x84, 0xb7,0x55,0x7e,0x88,0xff,0x86)
#endif
#else
typedef struct __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetailsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails *This,
        TrustLevel *trustLevel);

    /*** IWlanConnectionProfileDetails methods ***/
    HRESULT (STDMETHODCALLTYPE *GetConnectedSsid)(
        __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetailsVtbl;

interface __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails {
    CONST_VTBL __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetailsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IWlanConnectionProfileDetails methods ***/
#define __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetConnectedSsid(This,value) (This)->lpVtbl->GetConnectedSsid(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_QueryInterface(__x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_AddRef(__x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_Release(__x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetIids(__x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetRuntimeClassName(__x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetTrustLevel(__x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IWlanConnectionProfileDetails methods ***/
static inline HRESULT __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetConnectedSsid(__x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails* This,HSTRING *value) {
    return This->lpVtbl->GetConnectedSsid(This,value);
}
#endif
#ifdef WIDL_using_Windows_Networking_Connectivity
#define IID_IWlanConnectionProfileDetails IID___x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails
#define IWlanConnectionProfileDetailsVtbl __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetailsVtbl
#define IWlanConnectionProfileDetails __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails
#define IWlanConnectionProfileDetails_QueryInterface __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_QueryInterface
#define IWlanConnectionProfileDetails_AddRef __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_AddRef
#define IWlanConnectionProfileDetails_Release __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_Release
#define IWlanConnectionProfileDetails_GetIids __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetIids
#define IWlanConnectionProfileDetails_GetRuntimeClassName __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetRuntimeClassName
#define IWlanConnectionProfileDetails_GetTrustLevel __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetTrustLevel
#define IWlanConnectionProfileDetails_GetConnectedSsid __x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_GetConnectedSsid
#endif /* WIDL_using_Windows_Networking_Connectivity */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CNetworking_CConnectivity_CIWlanConnectionProfileDetails_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.ConnectionCost
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_ConnectionCost_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_ConnectionCost_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_ConnectionCost[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','C','o','n','n','e','c','t','i','o','n','C','o','s','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_ConnectionCost[] = L"Windows.Networking.Connectivity.ConnectionCost";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_ConnectionCost[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','C','o','n','n','e','c','t','i','o','n','C','o','s','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_ConnectionCost_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.ConnectionProfile
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_ConnectionProfile_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_ConnectionProfile_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_ConnectionProfile[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','C','o','n','n','e','c','t','i','o','n','P','r','o','f','i','l','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_ConnectionProfile[] = L"Windows.Networking.Connectivity.ConnectionProfile";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_ConnectionProfile[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','C','o','n','n','e','c','t','i','o','n','P','r','o','f','i','l','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_ConnectionProfile_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.DataPlanStatus
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_DataPlanStatus_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_DataPlanStatus_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_DataPlanStatus[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','D','a','t','a','P','l','a','n','S','t','a','t','u','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_DataPlanStatus[] = L"Windows.Networking.Connectivity.DataPlanStatus";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_DataPlanStatus[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','D','a','t','a','P','l','a','n','S','t','a','t','u','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_DataPlanStatus_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.DataPlanUsage
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_DataPlanUsage_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_DataPlanUsage_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_DataPlanUsage[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','D','a','t','a','P','l','a','n','U','s','a','g','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_DataPlanUsage[] = L"Windows.Networking.Connectivity.DataPlanUsage";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_DataPlanUsage[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','D','a','t','a','P','l','a','n','U','s','a','g','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_DataPlanUsage_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.DataUsage
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_DataUsage_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_DataUsage_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_DataUsage[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','D','a','t','a','U','s','a','g','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_DataUsage[] = L"Windows.Networking.Connectivity.DataUsage";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_DataUsage[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','D','a','t','a','U','s','a','g','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_DataUsage_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.IPInformation
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_IPInformation_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_IPInformation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_IPInformation[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','I','P','I','n','f','o','r','m','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_IPInformation[] = L"Windows.Networking.Connectivity.IPInformation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_IPInformation[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','I','P','I','n','f','o','r','m','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_IPInformation_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.LanIdentifier
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_LanIdentifier_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_LanIdentifier_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_LanIdentifier[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','L','a','n','I','d','e','n','t','i','f','i','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_LanIdentifier[] = L"Windows.Networking.Connectivity.LanIdentifier";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_LanIdentifier[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','L','a','n','I','d','e','n','t','i','f','i','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_LanIdentifier_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.LanIdentifierData
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_LanIdentifierData_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_LanIdentifierData_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_LanIdentifierData[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','L','a','n','I','d','e','n','t','i','f','i','e','r','D','a','t','a',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_LanIdentifierData[] = L"Windows.Networking.Connectivity.LanIdentifierData";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_LanIdentifierData[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','L','a','n','I','d','e','n','t','i','f','i','e','r','D','a','t','a',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_LanIdentifierData_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.NetworkAdapter
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_NetworkAdapter_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_NetworkAdapter_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkAdapter[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','N','e','t','w','o','r','k','A','d','a','p','t','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkAdapter[] = L"Windows.Networking.Connectivity.NetworkAdapter";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkAdapter[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','N','e','t','w','o','r','k','A','d','a','p','t','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_NetworkAdapter_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.NetworkInformation
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_NetworkInformation_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_NetworkInformation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkInformation[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','N','e','t','w','o','r','k','I','n','f','o','r','m','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkInformation[] = L"Windows.Networking.Connectivity.NetworkInformation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkInformation[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','N','e','t','w','o','r','k','I','n','f','o','r','m','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_NetworkInformation_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.NetworkItem
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_NetworkItem_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_NetworkItem_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkItem[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','N','e','t','w','o','r','k','I','t','e','m',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkItem[] = L"Windows.Networking.Connectivity.NetworkItem";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkItem[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','N','e','t','w','o','r','k','I','t','e','m',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_NetworkItem_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.NetworkSecuritySettings
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_NetworkSecuritySettings_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_NetworkSecuritySettings_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkSecuritySettings[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','N','e','t','w','o','r','k','S','e','c','u','r','i','t','y','S','e','t','t','i','n','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkSecuritySettings[] = L"Windows.Networking.Connectivity.NetworkSecuritySettings";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkSecuritySettings[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','N','e','t','w','o','r','k','S','e','c','u','r','i','t','y','S','e','t','t','i','n','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_NetworkSecuritySettings_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.ProxyConfiguration
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_ProxyConfiguration_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_ProxyConfiguration_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_ProxyConfiguration[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','P','r','o','x','y','C','o','n','f','i','g','u','r','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_ProxyConfiguration[] = L"Windows.Networking.Connectivity.ProxyConfiguration";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_ProxyConfiguration[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','P','r','o','x','y','C','o','n','f','i','g','u','r','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_ProxyConfiguration_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.WwanConnectionProfileDetails
 */
#if WINDOWS_NETWORKING_CONNECTIVITY_WWANCONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_WwanConnectionProfileDetails_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_WwanConnectionProfileDetails_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_WwanConnectionProfileDetails[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','W','w','a','n','C','o','n','n','e','c','t','i','o','n','P','r','o','f','i','l','e','D','e','t','a','i','l','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_WwanConnectionProfileDetails[] = L"Windows.Networking.Connectivity.WwanConnectionProfileDetails";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_WwanConnectionProfileDetails[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','W','w','a','n','C','o','n','n','e','c','t','i','o','n','P','r','o','f','i','l','e','D','e','t','a','i','l','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_WwanConnectionProfileDetails_DEFINED */
#endif /* WINDOWS_NETWORKING_CONNECTIVITY_WWANCONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.WlanConnectionProfileDetails
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_WlanConnectionProfileDetails_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_WlanConnectionProfileDetails_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_WlanConnectionProfileDetails[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','W','l','a','n','C','o','n','n','e','c','t','i','o','n','P','r','o','f','i','l','e','D','e','t','a','i','l','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_WlanConnectionProfileDetails[] = L"Windows.Networking.Connectivity.WlanConnectionProfileDetails";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_WlanConnectionProfileDetails[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','W','l','a','n','C','o','n','n','e','c','t','i','o','n','P','r','o','f','i','l','e','D','e','t','a','i','l','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_WlanConnectionProfileDetails_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.NetworkUsage
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_NetworkUsage_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_NetworkUsage_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkUsage[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','N','e','t','w','o','r','k','U','s','a','g','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkUsage[] = L"Windows.Networking.Connectivity.NetworkUsage";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_NetworkUsage[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','N','e','t','w','o','r','k','U','s','a','g','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_NetworkUsage_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Networking.Connectivity.ConnectivityInterval
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Networking_Connectivity_ConnectivityInterval_DEFINED
#define RUNTIMECLASS_Windows_Networking_Connectivity_ConnectivityInterval_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Networking_Connectivity_ConnectivityInterval[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','C','o','n','n','e','c','t','i','v','i','t','y','I','n','t','e','r','v','a','l',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_ConnectivityInterval[] = L"Windows.Networking.Connectivity.ConnectivityInterval";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Networking_Connectivity_ConnectivityInterval[] = {'W','i','n','d','o','w','s','.','N','e','t','w','o','r','k','i','n','g','.','C','o','n','n','e','c','t','i','v','i','t','y','.','C','o','n','n','e','c','t','i','v','i','t','y','I','n','t','e','r','v','a','l',0};
#endif
#endif /* RUNTIMECLASS_Windows_Networking_Connectivity_ConnectivityInterval_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ConnectionProfile* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile, 0xe4f0c96a, 0x0571, 0x59f4, 0xa9,0xa9, 0xaf,0xac,0x3e,0x61,0xca,0xa0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("e4f0c96a-0571-59f4-a9a9-afac3e61caa0")
            IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ConnectionProfile* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::Connectivity::ConnectionProfile*, ABI::Windows::Networking::Connectivity::IConnectionProfile* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile, 0xe4f0c96a, 0x0571, 0x59f4, 0xa9,0xa9, 0xaf,0xac,0x3e,0x61,0xca,0xa0)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ConnectionProfile* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ConnectionProfile* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Release(__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ConnectionProfile* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_ConnectionProfile IID___FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile
#define IAsyncOperationCompletedHandler_ConnectionProfileVtbl __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl
#define IAsyncOperationCompletedHandler_ConnectionProfile __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile
#define IAsyncOperationCompletedHandler_ConnectionProfile_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_QueryInterface
#define IAsyncOperationCompletedHandler_ConnectionProfile_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_AddRef
#define IAsyncOperationCompletedHandler_ConnectionProfile_Release __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Release
#define IAsyncOperationCompletedHandler_ConnectionProfile_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ProxyConfiguration* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration, 0x035b2567, 0xefb9, 0x5bc3, 0xb6,0x09, 0xf9,0xa8,0xc2,0x0b,0x70,0x01);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("035b2567-efb9-5bc3-b609-f9a8c20b7001")
            IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ProxyConfiguration* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::Connectivity::ProxyConfiguration*, ABI::Windows::Networking::Connectivity::IProxyConfiguration* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration, 0x035b2567, 0xefb9, 0x5bc3, 0xb6,0x09, 0xf9,0xa8,0xc2,0x0b,0x70,0x01)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfigurationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ProxyConfiguration* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This,
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfigurationVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfigurationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ProxyConfiguration* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_Release(__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Networking::Connectivity::ProxyConfiguration* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This,__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_ProxyConfiguration IID___FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration
#define IAsyncOperationCompletedHandler_ProxyConfigurationVtbl __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfigurationVtbl
#define IAsyncOperationCompletedHandler_ProxyConfiguration __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration
#define IAsyncOperationCompletedHandler_ProxyConfiguration_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_QueryInterface
#define IAsyncOperationCompletedHandler_ProxyConfiguration_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_AddRef
#define IAsyncOperationCompletedHandler_ProxyConfiguration_Release __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_Release
#define IAsyncOperationCompletedHandler_ProxyConfiguration_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Networking::Connectivity::ConnectionProfile* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile, 0x5bf519ca, 0x8adb, 0x5ab5, 0xab,0xb8, 0xff,0x1b,0xbe,0x5d,0x2d,0xe8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("5bf519ca-8adb-5ab5-abb8-ff1bbe5d2de8")
            IAsyncOperation<ABI::Windows::Networking::Connectivity::ConnectionProfile* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::Connectivity::ConnectionProfile*, ABI::Windows::Networking::Connectivity::IConnectionProfile* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile, 0x5bf519ca, 0x8adb, 0x5ab5, 0xab,0xb8, 0xff,0x1b,0xbe,0x5d,0x2d,0xe8)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Networking::Connectivity::ConnectionProfile* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl;

interface __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile {
    CONST_VTBL __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Networking::Connectivity::ConnectionProfile* > methods ***/
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_QueryInterface(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_AddRef(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Release(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetIids(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetTrustLevel(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Networking::Connectivity::ConnectionProfile* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_put_Completed(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_get_Completed(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CConnectionProfile **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetResults(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_ConnectionProfile IID___FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile
#define IAsyncOperation_ConnectionProfileVtbl __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl
#define IAsyncOperation_ConnectionProfile __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile
#define IAsyncOperation_ConnectionProfile_QueryInterface __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_QueryInterface
#define IAsyncOperation_ConnectionProfile_AddRef __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_AddRef
#define IAsyncOperation_ConnectionProfile_Release __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Release
#define IAsyncOperation_ConnectionProfile_GetIids __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetIids
#define IAsyncOperation_ConnectionProfile_GetRuntimeClassName __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetRuntimeClassName
#define IAsyncOperation_ConnectionProfile_GetTrustLevel __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetTrustLevel
#define IAsyncOperation_ConnectionProfile_put_Completed __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_put_Completed
#define IAsyncOperation_ConnectionProfile_get_Completed __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_get_Completed
#define IAsyncOperation_ConnectionProfile_GetResults __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CConnectionProfile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Networking::Connectivity::ProxyConfiguration* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration, 0x1e7651f6, 0x6562, 0x59c7, 0x9a,0xf3, 0x87,0x56,0x63,0x6e,0xee,0xe2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("1e7651f6-6562-59c7-9af3-8756636eeee2")
            IAsyncOperation<ABI::Windows::Networking::Connectivity::ProxyConfiguration* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::Connectivity::ProxyConfiguration*, ABI::Windows::Networking::Connectivity::IProxyConfiguration* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration, 0x1e7651f6, 0x6562, 0x59c7, 0x9a,0xf3, 0x87,0x56,0x63,0x6e,0xee,0xe2)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfigurationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Networking::Connectivity::ProxyConfiguration* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfigurationVtbl;

interface __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration {
    CONST_VTBL __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfigurationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Networking::Connectivity::ProxyConfiguration* > methods ***/
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_QueryInterface(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_AddRef(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_Release(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetIids(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetTrustLevel(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Networking::Connectivity::ProxyConfiguration* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_put_Completed(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This,__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_get_Completed(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This,__FIAsyncOperationCompletedHandler_1_Windows__CNetworking__CConnectivity__CProxyConfiguration **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetResults(__FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration* This,__x_ABI_CWindows_CNetworking_CConnectivity_CIProxyConfiguration **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_ProxyConfiguration IID___FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration
#define IAsyncOperation_ProxyConfigurationVtbl __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfigurationVtbl
#define IAsyncOperation_ProxyConfiguration __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration
#define IAsyncOperation_ProxyConfiguration_QueryInterface __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_QueryInterface
#define IAsyncOperation_ProxyConfiguration_AddRef __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_AddRef
#define IAsyncOperation_ProxyConfiguration_Release __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_Release
#define IAsyncOperation_ProxyConfiguration_GetIids __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetIids
#define IAsyncOperation_ProxyConfiguration_GetRuntimeClassName __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetRuntimeClassName
#define IAsyncOperation_ProxyConfiguration_GetTrustLevel __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetTrustLevel
#define IAsyncOperation_ProxyConfiguration_put_Completed __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_put_Completed
#define IAsyncOperation_ProxyConfiguration_get_Completed __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_get_Completed
#define IAsyncOperation_ProxyConfiguration_GetResults __FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CNetworking__CConnectivity__CProxyConfiguration_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Networking::EndpointPair* > interface
 */
#ifndef ____FIIterable_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CNetworking__CEndpointPair, 0xd7ec83c4, 0xa17b, 0x51bf, 0x89,0x97, 0xaa,0x33,0xb9,0x10,0x2d,0xc9);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("d7ec83c4-a17b-51bf-8997-aa33b9102dc9")
                IIterable<ABI::Windows::Networking::EndpointPair* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::EndpointPair*, ABI::Windows::Networking::IEndpointPair* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CNetworking__CEndpointPair, 0xd7ec83c4, 0xa17b, 0x51bf, 0x89,0x97, 0xaa,0x33,0xb9,0x10,0x2d,0xc9)
#endif
#else
typedef struct __FIIterable_1_Windows__CNetworking__CEndpointPairVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Networking::EndpointPair* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CNetworking__CEndpointPair *This,
        __FIIterator_1_Windows__CNetworking__CEndpointPair **value);

    END_INTERFACE
} __FIIterable_1_Windows__CNetworking__CEndpointPairVtbl;

interface __FIIterable_1_Windows__CNetworking__CEndpointPair {
    CONST_VTBL __FIIterable_1_Windows__CNetworking__CEndpointPairVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Networking::EndpointPair* > methods ***/
#define __FIIterable_1_Windows__CNetworking__CEndpointPair_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIIterable_1_Windows__CNetworking__CEndpointPair_QueryInterface(__FIIterable_1_Windows__CNetworking__CEndpointPair* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIIterable_1_Windows__CNetworking__CEndpointPair_AddRef(__FIIterable_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIIterable_1_Windows__CNetworking__CEndpointPair_Release(__FIIterable_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIIterable_1_Windows__CNetworking__CEndpointPair_GetIids(__FIIterable_1_Windows__CNetworking__CEndpointPair* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIIterable_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(__FIIterable_1_Windows__CNetworking__CEndpointPair* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIIterable_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(__FIIterable_1_Windows__CNetworking__CEndpointPair* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Networking::EndpointPair* > methods ***/
static inline HRESULT __FIIterable_1_Windows__CNetworking__CEndpointPair_First(__FIIterable_1_Windows__CNetworking__CEndpointPair* This,__FIIterator_1_Windows__CNetworking__CEndpointPair **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_EndpointPair IID___FIIterable_1_Windows__CNetworking__CEndpointPair
#define IIterable_EndpointPairVtbl __FIIterable_1_Windows__CNetworking__CEndpointPairVtbl
#define IIterable_EndpointPair __FIIterable_1_Windows__CNetworking__CEndpointPair
#define IIterable_EndpointPair_QueryInterface __FIIterable_1_Windows__CNetworking__CEndpointPair_QueryInterface
#define IIterable_EndpointPair_AddRef __FIIterable_1_Windows__CNetworking__CEndpointPair_AddRef
#define IIterable_EndpointPair_Release __FIIterable_1_Windows__CNetworking__CEndpointPair_Release
#define IIterable_EndpointPair_GetIids __FIIterable_1_Windows__CNetworking__CEndpointPair_GetIids
#define IIterable_EndpointPair_GetRuntimeClassName __FIIterable_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName
#define IIterable_EndpointPair_GetTrustLevel __FIIterable_1_Windows__CNetworking__CEndpointPair_GetTrustLevel
#define IIterable_EndpointPair_First __FIIterable_1_Windows__CNetworking__CEndpointPair_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Networking::Connectivity::ConnectionProfile* > interface
 */
#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile, 0x6db1b492, 0x3852, 0x5df8, 0xa2,0x9d, 0x69,0x44,0x00,0x2f,0x58,0xd4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("6db1b492-3852-5df8-a29d-6944002f58d4")
                IVectorView<ABI::Windows::Networking::Connectivity::ConnectionProfile* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::Connectivity::ConnectionProfile*, ABI::Windows::Networking::Connectivity::IConnectionProfile* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile, 0x6db1b492, 0x3852, 0x5df8, 0xa2,0x9d, 0x69,0x44,0x00,0x2f,0x58,0xd4)
#endif
#else
typedef struct __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Networking::Connectivity::ConnectionProfile* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        UINT32 index,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl;

interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile {
    CONST_VTBL __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Networking::Connectivity::ConnectionProfile* > methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_QueryInterface(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_AddRef(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Release(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetIids(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetRuntimeClassName(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetTrustLevel(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Networking::Connectivity::ConnectionProfile* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetAt(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,UINT32 index,__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_get_Size(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_IndexOf(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetMany(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectionProfile **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_ConnectionProfile IID___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile
#define IVectorView_ConnectionProfileVtbl __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfileVtbl
#define IVectorView_ConnectionProfile __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile
#define IVectorView_ConnectionProfile_QueryInterface __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_QueryInterface
#define IVectorView_ConnectionProfile_AddRef __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_AddRef
#define IVectorView_ConnectionProfile_Release __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_Release
#define IVectorView_ConnectionProfile_GetIids __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetIids
#define IVectorView_ConnectionProfile_GetRuntimeClassName __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetRuntimeClassName
#define IVectorView_ConnectionProfile_GetTrustLevel __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetTrustLevel
#define IVectorView_ConnectionProfile_GetAt __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetAt
#define IVectorView_ConnectionProfile_get_Size __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_get_Size
#define IVectorView_ConnectionProfile_IndexOf __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_IndexOf
#define IVectorView_ConnectionProfile_GetMany __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectionProfile_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Networking::Connectivity::LanIdentifier* > interface
 */
#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier, 0x41286159, 0xb91d, 0x5736, 0xad,0x8b, 0xe1,0x6f,0xcf,0x8a,0xce,0xd0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("41286159-b91d-5736-ad8b-e16fcf8aced0")
                IVectorView<ABI::Windows::Networking::Connectivity::LanIdentifier* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::Connectivity::LanIdentifier*, ABI::Windows::Networking::Connectivity::ILanIdentifier* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier, 0x41286159, 0xb91d, 0x5736, 0xad,0x8b, 0xe1,0x6f,0xcf,0x8a,0xce,0xd0)
#endif
#else
typedef struct __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifierVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Networking::Connectivity::LanIdentifier* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier *This,
        UINT32 index,
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifierVtbl;

interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier {
    CONST_VTBL __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifierVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Networking::Connectivity::LanIdentifier* > methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_QueryInterface(__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_AddRef(__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_Release(__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetIids(__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetRuntimeClassName(__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetTrustLevel(__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Networking::Connectivity::LanIdentifier* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetAt(__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier* This,UINT32 index,__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_get_Size(__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_IndexOf(__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier* This,__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetMany(__FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CNetworking_CConnectivity_CILanIdentifier **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_LanIdentifier IID___FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier
#define IVectorView_LanIdentifierVtbl __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifierVtbl
#define IVectorView_LanIdentifier __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier
#define IVectorView_LanIdentifier_QueryInterface __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_QueryInterface
#define IVectorView_LanIdentifier_AddRef __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_AddRef
#define IVectorView_LanIdentifier_Release __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_Release
#define IVectorView_LanIdentifier_GetIids __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetIids
#define IVectorView_LanIdentifier_GetRuntimeClassName __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetRuntimeClassName
#define IVectorView_LanIdentifier_GetTrustLevel __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetTrustLevel
#define IVectorView_LanIdentifier_GetAt __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetAt
#define IVectorView_LanIdentifier_get_Size __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_get_Size
#define IVectorView_LanIdentifier_IndexOf __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_IndexOf
#define IVectorView_LanIdentifier_GetMany __FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CNetworking__CConnectivity__CLanIdentifier_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Networking::HostName* > interface
 */
#ifndef ____FIVectorView_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CNetworking__CHostName, 0xf4706ab1, 0x55a3, 0x5270, 0xaf,0xb2, 0x73,0x29,0x88,0xfe,0x82,0x27);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("f4706ab1-55a3-5270-afb2-732988fe8227")
                IVectorView<ABI::Windows::Networking::HostName* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::HostName*, ABI::Windows::Networking::IHostName* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CNetworking__CHostName, 0xf4706ab1, 0x55a3, 0x5270, 0xaf,0xb2, 0x73,0x29,0x88,0xfe,0x82,0x27)
#endif
#else
typedef struct __FIVectorView_1_Windows__CNetworking__CHostNameVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Networking::HostName* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        UINT32 index,
        __x_ABI_CWindows_CNetworking_CIHostName **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        __x_ABI_CWindows_CNetworking_CIHostName *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CNetworking__CHostName *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CNetworking_CIHostName **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CNetworking__CHostNameVtbl;

interface __FIVectorView_1_Windows__CNetworking__CHostName {
    CONST_VTBL __FIVectorView_1_Windows__CNetworking__CHostNameVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CNetworking__CHostName_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CNetworking__CHostName_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CNetworking__CHostName_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CNetworking__CHostName_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CNetworking__CHostName_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CNetworking__CHostName_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Networking::HostName* > methods ***/
#define __FIVectorView_1_Windows__CNetworking__CHostName_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CNetworking__CHostName_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CNetworking__CHostName_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CNetworking__CHostName_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_QueryInterface(__FIVectorView_1_Windows__CNetworking__CHostName* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CHostName_AddRef(__FIVectorView_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CHostName_Release(__FIVectorView_1_Windows__CNetworking__CHostName* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_GetIids(__FIVectorView_1_Windows__CNetworking__CHostName* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_GetRuntimeClassName(__FIVectorView_1_Windows__CNetworking__CHostName* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_GetTrustLevel(__FIVectorView_1_Windows__CNetworking__CHostName* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Networking::HostName* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_GetAt(__FIVectorView_1_Windows__CNetworking__CHostName* This,UINT32 index,__x_ABI_CWindows_CNetworking_CIHostName **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_get_Size(__FIVectorView_1_Windows__CNetworking__CHostName* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_IndexOf(__FIVectorView_1_Windows__CNetworking__CHostName* This,__x_ABI_CWindows_CNetworking_CIHostName *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CHostName_GetMany(__FIVectorView_1_Windows__CNetworking__CHostName* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CNetworking_CIHostName **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_HostName IID___FIVectorView_1_Windows__CNetworking__CHostName
#define IVectorView_HostNameVtbl __FIVectorView_1_Windows__CNetworking__CHostNameVtbl
#define IVectorView_HostName __FIVectorView_1_Windows__CNetworking__CHostName
#define IVectorView_HostName_QueryInterface __FIVectorView_1_Windows__CNetworking__CHostName_QueryInterface
#define IVectorView_HostName_AddRef __FIVectorView_1_Windows__CNetworking__CHostName_AddRef
#define IVectorView_HostName_Release __FIVectorView_1_Windows__CNetworking__CHostName_Release
#define IVectorView_HostName_GetIids __FIVectorView_1_Windows__CNetworking__CHostName_GetIids
#define IVectorView_HostName_GetRuntimeClassName __FIVectorView_1_Windows__CNetworking__CHostName_GetRuntimeClassName
#define IVectorView_HostName_GetTrustLevel __FIVectorView_1_Windows__CNetworking__CHostName_GetTrustLevel
#define IVectorView_HostName_GetAt __FIVectorView_1_Windows__CNetworking__CHostName_GetAt
#define IVectorView_HostName_get_Size __FIVectorView_1_Windows__CNetworking__CHostName_get_Size
#define IVectorView_HostName_IndexOf __FIVectorView_1_Windows__CNetworking__CHostName_IndexOf
#define IVectorView_HostName_GetMany __FIVectorView_1_Windows__CNetworking__CHostName_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CNetworking__CHostName_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Networking::EndpointPair* > interface
 */
#ifndef ____FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CNetworking__CEndpointPair, 0x8780a851, 0x6d48, 0x5006, 0x92,0x88, 0x81,0xf3,0xd7,0x04,0x5a,0x96);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("8780a851-6d48-5006-9288-81f3d7045a96")
                IVectorView<ABI::Windows::Networking::EndpointPair* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::EndpointPair*, ABI::Windows::Networking::IEndpointPair* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CNetworking__CEndpointPair, 0x8780a851, 0x6d48, 0x5006, 0x92,0x88, 0x81,0xf3,0xd7,0x04,0x5a,0x96)
#endif
#else
typedef struct __FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Networking::EndpointPair* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        UINT32 index,
        __x_ABI_CWindows_CNetworking_CIEndpointPair **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        __x_ABI_CWindows_CNetworking_CIEndpointPair *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CNetworking__CEndpointPair *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CNetworking_CIEndpointPair **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl;

interface __FIVectorView_1_Windows__CNetworking__CEndpointPair {
    CONST_VTBL __FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Networking::EndpointPair* > methods ***/
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CEndpointPair_Release(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetIids(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetTrustLevel(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Networking::EndpointPair* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetAt(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,UINT32 index,__x_ABI_CWindows_CNetworking_CIEndpointPair **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_get_Size(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_IndexOf(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,__x_ABI_CWindows_CNetworking_CIEndpointPair *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetMany(__FIVectorView_1_Windows__CNetworking__CEndpointPair* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CNetworking_CIEndpointPair **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_EndpointPair IID___FIVectorView_1_Windows__CNetworking__CEndpointPair
#define IVectorView_EndpointPairVtbl __FIVectorView_1_Windows__CNetworking__CEndpointPairVtbl
#define IVectorView_EndpointPair __FIVectorView_1_Windows__CNetworking__CEndpointPair
#define IVectorView_EndpointPair_QueryInterface __FIVectorView_1_Windows__CNetworking__CEndpointPair_QueryInterface
#define IVectorView_EndpointPair_AddRef __FIVectorView_1_Windows__CNetworking__CEndpointPair_AddRef
#define IVectorView_EndpointPair_Release __FIVectorView_1_Windows__CNetworking__CEndpointPair_Release
#define IVectorView_EndpointPair_GetIids __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetIids
#define IVectorView_EndpointPair_GetRuntimeClassName __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetRuntimeClassName
#define IVectorView_EndpointPair_GetTrustLevel __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetTrustLevel
#define IVectorView_EndpointPair_GetAt __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetAt
#define IVectorView_EndpointPair_get_Size __FIVectorView_1_Windows__CNetworking__CEndpointPair_get_Size
#define IVectorView_EndpointPair_IndexOf __FIVectorView_1_Windows__CNetworking__CEndpointPair_IndexOf
#define IVectorView_EndpointPair_GetMany __FIVectorView_1_Windows__CNetworking__CEndpointPair_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CNetworking__CEndpointPair_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* > interface
 */
#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage, 0xb3853391, 0x40b6, 0x5cf5, 0x8f,0x46, 0x48,0x82,0x69,0x1d,0x1f,0xf7);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("b3853391-40b6-5cf5-8f46-4882691d1ff7")
                IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::Connectivity::NetworkUsage*, ABI::Windows::Networking::Connectivity::INetworkUsage* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage, 0xb3853391, 0x40b6, 0x5cf5, 0x8f,0x46, 0x48,0x82,0x69,0x1d,0x1f,0xf7)
#endif
#else
typedef struct __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        UINT32 index,
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl;

interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage {
    CONST_VTBL __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* > methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_QueryInterface(__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_AddRef(__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Release(__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetIids(__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetRuntimeClassName(__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetTrustLevel(__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetAt(__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,UINT32 index,__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_get_Size(__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_IndexOf(__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetMany(__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CNetworking_CConnectivity_CINetworkUsage **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_NetworkUsage IID___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage
#define IVectorView_NetworkUsageVtbl __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl
#define IVectorView_NetworkUsage __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage
#define IVectorView_NetworkUsage_QueryInterface __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_QueryInterface
#define IVectorView_NetworkUsage_AddRef __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_AddRef
#define IVectorView_NetworkUsage_Release __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Release
#define IVectorView_NetworkUsage_GetIids __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetIids
#define IVectorView_NetworkUsage_GetRuntimeClassName __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetRuntimeClassName
#define IVectorView_NetworkUsage_GetTrustLevel __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetTrustLevel
#define IVectorView_NetworkUsage_GetAt __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetAt
#define IVectorView_NetworkUsage_get_Size __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_get_Size
#define IVectorView_NetworkUsage_IndexOf __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_IndexOf
#define IVectorView_NetworkUsage_GetMany __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage, 0x05c9e081, 0x6229, 0x5049, 0x8e,0xea, 0xa4,0x98,0x40,0x7c,0x00,0xd5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("05c9e081-6229-5049-8eea-a498407c00d5")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage, 0x05c9e081, 0x6229, 0x5049, 0x8e,0xea, 0xa4,0x98,0x40,0x7c,0x00,0xd5)
#endif
#else
typedef struct __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl;

interface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage {
    CONST_VTBL __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* > methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_QueryInterface(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_AddRef(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Release(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetIids(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetRuntimeClassName(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetTrustLevel(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_put_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_get_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetResults(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,__FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IVectorView_NetworkUsage IID___FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage
#define IAsyncOperation_IVectorView_NetworkUsageVtbl __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl
#define IAsyncOperation_IVectorView_NetworkUsage __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage
#define IAsyncOperation_IVectorView_NetworkUsage_QueryInterface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_QueryInterface
#define IAsyncOperation_IVectorView_NetworkUsage_AddRef __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_AddRef
#define IAsyncOperation_IVectorView_NetworkUsage_Release __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Release
#define IAsyncOperation_IVectorView_NetworkUsage_GetIids __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetIids
#define IAsyncOperation_IVectorView_NetworkUsage_GetRuntimeClassName __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetRuntimeClassName
#define IAsyncOperation_IVectorView_NetworkUsage_GetTrustLevel __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetTrustLevel
#define IAsyncOperation_IVectorView_NetworkUsage_put_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_put_Completed
#define IAsyncOperation_IVectorView_NetworkUsage_get_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_get_Completed
#define IAsyncOperation_IVectorView_NetworkUsage_GetResults __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* > interface
 */
#ifndef ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval, 0xa3d0d117, 0x9e21, 0x5919, 0xb7,0xa0, 0xc8,0x19,0x0b,0xd5,0x5a,0xc5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("a3d0d117-9e21-5919-b7a0-c8190bd55ac5")
                IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Networking::Connectivity::ConnectivityInterval*, ABI::Windows::Networking::Connectivity::IConnectivityInterval* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval, 0xa3d0d117, 0x9e21, 0x5919, 0xb7,0xa0, 0xc8,0x19,0x0b,0xd5,0x5a,0xc5)
#endif
#else
typedef struct __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        UINT32 index,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl;

interface __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval {
    CONST_VTBL __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* > methods ***/
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_QueryInterface(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_AddRef(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Release(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetIids(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetRuntimeClassName(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetTrustLevel(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* > methods ***/
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetAt(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,UINT32 index,__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_get_Size(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_IndexOf(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static inline HRESULT __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetMany(__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CNetworking_CConnectivity_CIConnectivityInterval **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_ConnectivityInterval IID___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval
#define IVectorView_ConnectivityIntervalVtbl __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl
#define IVectorView_ConnectivityInterval __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval
#define IVectorView_ConnectivityInterval_QueryInterface __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_QueryInterface
#define IVectorView_ConnectivityInterval_AddRef __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_AddRef
#define IVectorView_ConnectivityInterval_Release __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Release
#define IVectorView_ConnectivityInterval_GetIids __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetIids
#define IVectorView_ConnectivityInterval_GetRuntimeClassName __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetRuntimeClassName
#define IVectorView_ConnectivityInterval_GetTrustLevel __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetTrustLevel
#define IVectorView_ConnectivityInterval_GetAt __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetAt
#define IVectorView_ConnectivityInterval_get_Size __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_get_Size
#define IVectorView_ConnectivityInterval_IndexOf __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_IndexOf
#define IVectorView_ConnectivityInterval_GetMany __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval, 0xaf96d70b, 0x41c7, 0x5dc6, 0x98,0x95, 0xea,0x04,0x3a,0x88,0x5d,0x8d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("af96d70b-41c7-5dc6-9895-ea043a885d8d")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval, 0xaf96d70b, 0x41c7, 0x5dc6, 0x98,0x95, 0xea,0x04,0x3a,0x88,0x5d,0x8d)
#endif
#else
typedef struct __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        __FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl;

interface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval {
    CONST_VTBL __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* > methods ***/
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_QueryInterface(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_AddRef(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Release(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetIids(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetRuntimeClassName(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetTrustLevel(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_put_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_get_Completed(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetResults(__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,__FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IVectorView_ConnectivityInterval IID___FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval
#define IAsyncOperation_IVectorView_ConnectivityIntervalVtbl __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl
#define IAsyncOperation_IVectorView_ConnectivityInterval __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval
#define IAsyncOperation_IVectorView_ConnectivityInterval_QueryInterface __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_QueryInterface
#define IAsyncOperation_IVectorView_ConnectivityInterval_AddRef __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_AddRef
#define IAsyncOperation_IVectorView_ConnectivityInterval_Release __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Release
#define IAsyncOperation_IVectorView_ConnectivityInterval_GetIids __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetIids
#define IAsyncOperation_IVectorView_ConnectivityInterval_GetRuntimeClassName __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetRuntimeClassName
#define IAsyncOperation_IVectorView_ConnectivityInterval_GetTrustLevel __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetTrustLevel
#define IAsyncOperation_IVectorView_ConnectivityInterval_put_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_put_Completed
#define IAsyncOperation_IVectorView_ConnectivityInterval_get_Completed __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_get_Completed
#define IAsyncOperation_IVectorView_ConnectivityInterval_GetResults __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval, 0xb475014c, 0x95f1, 0x5310, 0xb5,0xd1, 0xc2,0x30,0x9d,0x94,0x44,0x40);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("b475014c-95f1-5310-b5d1-c2309d944440")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval, 0xb475014c, 0x95f1, 0x5310, 0xb5,0xd1, 0xc2,0x30,0x9d,0x94,0x44,0x40)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_AddRef(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Release(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::ConnectivityInterval* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Invoke(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IVectorView_ConnectivityInterval IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval
#define IAsyncOperationCompletedHandler_IVectorView_ConnectivityIntervalVtbl __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityIntervalVtbl
#define IAsyncOperationCompletedHandler_IVectorView_ConnectivityInterval __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval
#define IAsyncOperationCompletedHandler_IVectorView_ConnectivityInterval_QueryInterface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_QueryInterface
#define IAsyncOperationCompletedHandler_IVectorView_ConnectivityInterval_AddRef __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_AddRef
#define IAsyncOperationCompletedHandler_IVectorView_ConnectivityInterval_Release __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Release
#define IAsyncOperationCompletedHandler_IVectorView_ConnectivityInterval_Invoke __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CConnectivityInterval_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage, 0xe31d7e7e, 0x4173, 0x5c71, 0xb0,0x4b, 0xa0,0x96,0x58,0x00,0x25,0x90);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("e31d7e7e-4173-5c71-b04b-a09658002590")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage, 0xe31d7e7e, 0x4173, 0x5c71, 0xb0,0x4b, 0xa0,0x96,0x58,0x00,0x25,0x90)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *This,
        __FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_AddRef(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Release(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Networking::Connectivity::NetworkUsage* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Invoke(__FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage* This,__FIAsyncOperation_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IVectorView_NetworkUsage IID___FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage
#define IAsyncOperationCompletedHandler_IVectorView_NetworkUsageVtbl __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsageVtbl
#define IAsyncOperationCompletedHandler_IVectorView_NetworkUsage __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage
#define IAsyncOperationCompletedHandler_IVectorView_NetworkUsage_QueryInterface __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_QueryInterface
#define IAsyncOperationCompletedHandler_IVectorView_NetworkUsage_AddRef __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_AddRef
#define IAsyncOperationCompletedHandler_IVectorView_NetworkUsage_Release __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Release
#define IAsyncOperationCompletedHandler_IVectorView_NetworkUsage_Invoke __FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIVectorView_1_Windows__CNetworking__CConnectivity__CNetworkUsage_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_networking_connectivity_h__ */
