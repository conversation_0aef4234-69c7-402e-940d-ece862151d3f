/*** Autogenerated by WIDL 10.12 from include/windows.devices.bluetooth.rfcomm.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_devices_bluetooth_rfcomm_h__
#define __windows_devices_bluetooth_rfcomm_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    interface IRfcommDeviceService;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceServiceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    interface IRfcommDeviceServiceStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CRfcommDeviceService_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CRfcommDeviceService_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    class RfcommDeviceService;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CRfcommDeviceService __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CRfcommDeviceService;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CRfcommDeviceService_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CRfcommServiceId_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CRfcommServiceId_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    class RfcommServiceId;
                }
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CRfcommServiceId __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CRfcommServiceId;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CRfcommServiceId_FWD_DEFINED__ */

#ifndef ____FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
#define ____FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
typedef interface __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer;
#ifdef __cplusplus
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.devices.bluetooth.h>
#include <windows.networking.h>
#include <windows.networking.sockets.h>
#include <windows.storage.streams.h>

#ifdef __cplusplus
extern "C" {
#endif

#if !defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)
#define WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION 0xe0000
#endif // defined(WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION)

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode_ENUM_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode_ENUM_DEFINED__
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                enum BluetoothCacheMode {
                    BluetoothCacheMode_Cached = 0,
                    BluetoothCacheMode_Uncached = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode {
    BluetoothCacheMode_Cached = 0,
    BluetoothCacheMode_Uncached = 1
};
#ifdef WIDL_using_Windows_Devices_Bluetooth
#define BluetoothCacheMode __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode
#endif /* WIDL_using_Windows_Devices_Bluetooth */
#endif

#endif /* ____x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode_ENUM_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    interface IRfcommDeviceService;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService2 __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService2 ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    interface IRfcommDeviceService2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService3_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService3_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService3 __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService3;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService3 ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService3
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    interface IRfcommDeviceService3;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceServiceStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    interface IRfcommDeviceServiceStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics2 __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics2 ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceServiceStatics2
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    interface IRfcommDeviceServiceStatics2;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceId_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceId_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceId __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceId;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceId ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommServiceId
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    interface IRfcommServiceId;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceIdStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceIdStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceIdStatics __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceIdStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceIdStatics ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommServiceIdStatics
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    interface IRfcommServiceIdStatics;
                }
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
#define ____FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
typedef interface __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer;
#ifdef __cplusplus
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
#define ____FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_FWD_DEFINED__
typedef interface __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer;
#ifdef __cplusplus
#define __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* >
#endif /* __cplusplus */
#endif

/*****************************************************************************
 * IRfcommDeviceService interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService, 0xae81ff1f, 0xc5a1, 0x4c40, 0x8c,0x28, 0xf3,0xef,0xd6,0x90,0x62,0xf3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    MIDL_INTERFACE("ae81ff1f-c5a1-4c40-8c28-f3efd69062f3")
                    IRfcommDeviceService : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE get_ConnectionHostName(
                            ABI::Windows::Networking::IHostName **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ConnectionServiceName(
                            HSTRING *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ServiceId(
                            ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommServiceId **value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_ProtectionLevel(
                            ABI::Windows::Networking::Sockets::SocketProtectionLevel *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE get_MaxProtectionLevel(
                            ABI::Windows::Networking::Sockets::SocketProtectionLevel *value) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetSdpRawAttributesAsync(
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > **operation) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetSdpRawAttributesWithCacheModeAsync(
                            ABI::Windows::Devices::Bluetooth::BluetoothCacheMode cache_mode,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > **operation) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService, 0xae81ff1f, 0xc5a1, 0x4c40, 0x8c,0x28, 0xf3,0xef,0xd6,0x90,0x62,0xf3)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This,
        TrustLevel *trustLevel);

    /*** IRfcommDeviceService methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ConnectionHostName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This,
        __x_ABI_CWindows_CNetworking_CIHostName **value);

    HRESULT (STDMETHODCALLTYPE *get_ConnectionServiceName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_ServiceId)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceId **value);

    HRESULT (STDMETHODCALLTYPE *get_ProtectionLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This,
        __x_ABI_CWindows_CNetworking_CSockets_CSocketProtectionLevel *value);

    HRESULT (STDMETHODCALLTYPE *get_MaxProtectionLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This,
        __x_ABI_CWindows_CNetworking_CSockets_CSocketProtectionLevel *value);

    HRESULT (STDMETHODCALLTYPE *GetSdpRawAttributesAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This,
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **operation);

    HRESULT (STDMETHODCALLTYPE *GetSdpRawAttributesWithCacheModeAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode cache_mode,
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **operation);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRfcommDeviceService methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ConnectionHostName(This,value) (This)->lpVtbl->get_ConnectionHostName(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ConnectionServiceName(This,value) (This)->lpVtbl->get_ConnectionServiceName(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ServiceId(This,value) (This)->lpVtbl->get_ServiceId(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ProtectionLevel(This,value) (This)->lpVtbl->get_ProtectionLevel(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_MaxProtectionLevel(This,value) (This)->lpVtbl->get_MaxProtectionLevel(This,value)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetSdpRawAttributesAsync(This,operation) (This)->lpVtbl->GetSdpRawAttributesAsync(This,operation)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetSdpRawAttributesWithCacheModeAsync(This,cache_mode,operation) (This)->lpVtbl->GetSdpRawAttributesWithCacheModeAsync(This,cache_mode,operation)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_Release(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRfcommDeviceService methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ConnectionHostName(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This,__x_ABI_CWindows_CNetworking_CIHostName **value) {
    return This->lpVtbl->get_ConnectionHostName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ConnectionServiceName(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This,HSTRING *value) {
    return This->lpVtbl->get_ConnectionServiceName(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ServiceId(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This,__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceId **value) {
    return This->lpVtbl->get_ServiceId(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ProtectionLevel(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This,__x_ABI_CWindows_CNetworking_CSockets_CSocketProtectionLevel *value) {
    return This->lpVtbl->get_ProtectionLevel(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_MaxProtectionLevel(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This,__x_ABI_CWindows_CNetworking_CSockets_CSocketProtectionLevel *value) {
    return This->lpVtbl->get_MaxProtectionLevel(This,value);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetSdpRawAttributesAsync(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This,__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **operation) {
    return This->lpVtbl->GetSdpRawAttributesAsync(This,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetSdpRawAttributesWithCacheModeAsync(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService* This,__x_ABI_CWindows_CDevices_CBluetooth_CBluetoothCacheMode cache_mode,__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **operation) {
    return This->lpVtbl->GetSdpRawAttributesWithCacheModeAsync(This,cache_mode,operation);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth_Rfcomm
#define IID_IRfcommDeviceService IID___x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService
#define IRfcommDeviceServiceVtbl __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceVtbl
#define IRfcommDeviceService __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService
#define IRfcommDeviceService_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_QueryInterface
#define IRfcommDeviceService_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_AddRef
#define IRfcommDeviceService_Release __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_Release
#define IRfcommDeviceService_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetIids
#define IRfcommDeviceService_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetRuntimeClassName
#define IRfcommDeviceService_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetTrustLevel
#define IRfcommDeviceService_get_ConnectionHostName __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ConnectionHostName
#define IRfcommDeviceService_get_ConnectionServiceName __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ConnectionServiceName
#define IRfcommDeviceService_get_ServiceId __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ServiceId
#define IRfcommDeviceService_get_ProtectionLevel __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_ProtectionLevel
#define IRfcommDeviceService_get_MaxProtectionLevel __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_get_MaxProtectionLevel
#define IRfcommDeviceService_GetSdpRawAttributesAsync __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetSdpRawAttributesAsync
#define IRfcommDeviceService_GetSdpRawAttributesWithCacheModeAsync __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_GetSdpRawAttributesWithCacheModeAsync
#endif /* WIDL_using_Windows_Devices_Bluetooth_Rfcomm */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IRfcommDeviceServiceStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics, 0xa4a149ef, 0x626d, 0x41ac, 0xb2,0x53, 0x87,0xac,0x5c,0x27,0xe2,0x8a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Devices {
            namespace Bluetooth {
                namespace Rfcomm {
                    MIDL_INTERFACE("a4a149ef-626d-41ac-b253-87ac5c27e28a")
                    IRfcommDeviceServiceStatics : public IInspectable
                    {
                        virtual HRESULT STDMETHODCALLTYPE FromIdAsync(
                            HSTRING id,
                            ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > **operation) = 0;

                        virtual HRESULT STDMETHODCALLTYPE GetDeviceSelector(
                            ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommServiceId *id,
                            HSTRING *selector) = 0;

                    };
                }
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics, 0xa4a149ef, 0x626d, 0x41ac, 0xb2,0x53, 0x87,0xac,0x5c,0x27,0xe2,0x8a)
#endif
#else
typedef struct __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics *This,
        TrustLevel *trustLevel);

    /*** IRfcommDeviceServiceStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *FromIdAsync)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics *This,
        HSTRING id,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService **operation);

    HRESULT (STDMETHODCALLTYPE *GetDeviceSelector)(
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceId *id,
        HSTRING *selector);

    END_INTERFACE
} __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStaticsVtbl;

interface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics {
    CONST_VTBL __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IRfcommDeviceServiceStatics methods ***/
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_FromIdAsync(This,id,operation) (This)->lpVtbl->FromIdAsync(This,id,operation)
#define __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetDeviceSelector(This,id,selector) (This)->lpVtbl->GetDeviceSelector(This,id,selector)
#else
/*** IUnknown methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_QueryInterface(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_AddRef(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_Release(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetIids(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetRuntimeClassName(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetTrustLevel(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IRfcommDeviceServiceStatics methods ***/
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_FromIdAsync(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics* This,HSTRING id,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService **operation) {
    return This->lpVtbl->FromIdAsync(This,id,operation);
}
static inline HRESULT __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetDeviceSelector(__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics* This,__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommServiceId *id,HSTRING *selector) {
    return This->lpVtbl->GetDeviceSelector(This,id,selector);
}
#endif
#ifdef WIDL_using_Windows_Devices_Bluetooth_Rfcomm
#define IID_IRfcommDeviceServiceStatics IID___x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics
#define IRfcommDeviceServiceStaticsVtbl __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStaticsVtbl
#define IRfcommDeviceServiceStatics __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics
#define IRfcommDeviceServiceStatics_QueryInterface __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_QueryInterface
#define IRfcommDeviceServiceStatics_AddRef __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_AddRef
#define IRfcommDeviceServiceStatics_Release __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_Release
#define IRfcommDeviceServiceStatics_GetIids __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetIids
#define IRfcommDeviceServiceStatics_GetRuntimeClassName __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetRuntimeClassName
#define IRfcommDeviceServiceStatics_GetTrustLevel __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetTrustLevel
#define IRfcommDeviceServiceStatics_FromIdAsync __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_FromIdAsync
#define IRfcommDeviceServiceStatics_GetDeviceSelector __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_GetDeviceSelector
#endif /* WIDL_using_Windows_Devices_Bluetooth_Rfcomm */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceServiceStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Bluetooth.Rfcomm.RfcommDeviceService
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Bluetooth_Rfcomm_RfcommDeviceService_DEFINED
#define RUNTIMECLASS_Windows_Devices_Bluetooth_Rfcomm_RfcommDeviceService_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Bluetooth_Rfcomm_RfcommDeviceService[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','R','f','c','o','m','m','.','R','f','c','o','m','m','D','e','v','i','c','e','S','e','r','v','i','c','e',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_Rfcomm_RfcommDeviceService[] = L"Windows.Devices.Bluetooth.Rfcomm.RfcommDeviceService";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_Rfcomm_RfcommDeviceService[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','R','f','c','o','m','m','.','R','f','c','o','m','m','D','e','v','i','c','e','S','e','r','v','i','c','e',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Bluetooth_Rfcomm_RfcommDeviceService_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Devices.Bluetooth.Rfcomm.RfcommServiceId
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Devices_Bluetooth_Rfcomm_RfcommServiceId_DEFINED
#define RUNTIMECLASS_Windows_Devices_Bluetooth_Rfcomm_RfcommServiceId_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Devices_Bluetooth_Rfcomm_RfcommServiceId[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','R','f','c','o','m','m','.','R','f','c','o','m','m','S','e','r','v','i','c','e','I','d',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_Rfcomm_RfcommServiceId[] = L"Windows.Devices.Bluetooth.Rfcomm.RfcommServiceId";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Devices_Bluetooth_Rfcomm_RfcommServiceId[] = {'W','i','n','d','o','w','s','.','D','e','v','i','c','e','s','.','B','l','u','e','t','o','o','t','h','.','R','f','c','o','m','m','.','R','f','c','o','m','m','S','e','r','v','i','c','e','I','d',0};
#endif
#endif /* RUNTIMECLASS_Windows_Devices_Bluetooth_Rfcomm_RfcommServiceId_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* > interface
 */
#ifndef ____FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__
#define ____FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer, 0x57dc41e6, 0x8b4d, 0x5910, 0x97,0x03, 0xd7,0xc6,0x68,0x43,0x68,0x52);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("57dc41e6-8b4d-5910-9703-d7c668436852")
                IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* > : IMapView_impl<UINT32, ABI::Windows::Storage::Streams::IBuffer* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer, 0x57dc41e6, 0x8b4d, 0x5910, 0x97,0x03, 0xd7,0xc6,0x68,0x43,0x68,0x52)
#endif
#else
typedef struct __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        TrustLevel *trustLevel);

    /*** IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Lookup)(
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        UINT32 key,
        __x_ABI_CWindows_CStorage_CStreams_CIBuffer **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        unsigned int *size);

    HRESULT (STDMETHODCALLTYPE *HasKey)(
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        UINT32 key,
        boolean *found);

    HRESULT (STDMETHODCALLTYPE *Split)(
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **first,
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **second);

    END_INTERFACE
} __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl;

interface __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer {
    CONST_VTBL __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* > methods ***/
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Lookup(This,key,value) (This)->lpVtbl->Lookup(This,key,value)
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_get_Size(This,size) (This)->lpVtbl->get_Size(This,size)
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_HasKey(This,key,found) (This)->lpVtbl->HasKey(This,key,found)
#define __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Split(This,first,second) (This)->lpVtbl->Split(This,first,second)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_QueryInterface(__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_AddRef(__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Release(__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetIids(__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName(__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel(__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* > methods ***/
static inline HRESULT __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Lookup(__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,UINT32 key,__x_ABI_CWindows_CStorage_CStreams_CIBuffer **value) {
    return This->lpVtbl->Lookup(This,key,value);
}
static inline HRESULT __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_get_Size(__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,unsigned int *size) {
    return This->lpVtbl->get_Size(This,size);
}
static inline HRESULT __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_HasKey(__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,UINT32 key,boolean *found) {
    return This->lpVtbl->HasKey(This,key,found);
}
static inline HRESULT __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Split(__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **first,__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **second) {
    return This->lpVtbl->Split(This,first,second);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IMapView_UINT32_IBuffer IID___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer
#define IMapView_UINT32_IBufferVtbl __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl
#define IMapView_UINT32_IBuffer __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer
#define IMapView_UINT32_IBuffer_QueryInterface __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_QueryInterface
#define IMapView_UINT32_IBuffer_AddRef __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_AddRef
#define IMapView_UINT32_IBuffer_Release __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Release
#define IMapView_UINT32_IBuffer_GetIids __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetIids
#define IMapView_UINT32_IBuffer_GetRuntimeClassName __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName
#define IMapView_UINT32_IBuffer_GetTrustLevel __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel
#define IMapView_UINT32_IBuffer_Lookup __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Lookup
#define IMapView_UINT32_IBuffer_get_Size __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_get_Size
#define IMapView_UINT32_IBuffer_HasKey __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_HasKey
#define IMapView_UINT32_IBuffer_Split __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Split
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer, 0x92c2e4d0, 0x7c25, 0x596b, 0x91,0x35, 0x10,0xd1,0x47,0x2e,0x69,0x68);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("92c2e4d0-7c25-596b-9135-10d1472e6968")
            IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer, 0x92c2e4d0, 0x7c25, 0x596b, 0x91,0x35, 0x10,0xd1,0x47,0x2e,0x69,0x68)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl;

interface __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > methods ***/
#define __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_QueryInterface(__FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_AddRef(__FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Release(__FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Invoke(__FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_IMapView_UINT32_IBuffer IID___FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer
#define IAsyncOperationCompletedHandler_IMapView_UINT32_IBufferVtbl __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl
#define IAsyncOperationCompletedHandler_IMapView_UINT32_IBuffer __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer
#define IAsyncOperationCompletedHandler_IMapView_UINT32_IBuffer_QueryInterface __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_QueryInterface
#define IAsyncOperationCompletedHandler_IMapView_UINT32_IBuffer_AddRef __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_AddRef
#define IAsyncOperationCompletedHandler_IMapView_UINT32_IBuffer_Release __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Release
#define IAsyncOperationCompletedHandler_IMapView_UINT32_IBuffer_Invoke __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > interface
 */
#ifndef ____FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer, 0xd4904ded, 0xbc1d, 0x5933, 0xae,0xcf, 0xe4,0x2c,0x5d,0x46,0x5b,0xff);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d4904ded-bc1d-5933-aecf-e42c5d465bff")
            IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > : IAsyncOperation_impl<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer, 0xd4904ded, 0xbc1d, 0x5933, 0xae,0xcf, 0xe4,0x2c,0x5d,0x46,0x5b,0xff)
#endif
#else
typedef struct __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        __FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *This,
        __FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **results);

    END_INTERFACE
} __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl;

interface __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer {
    CONST_VTBL __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > methods ***/
#define __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_QueryInterface(__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_AddRef(__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Release(__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetIids(__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName(__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel(__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Foundation::Collections::IMapView<UINT32,ABI::Windows::Storage::Streams::IBuffer* >* > methods ***/
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_put_Completed(__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,__FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_get_Completed(__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,__FIAsyncOperationCompletedHandler_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetResults(__FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer* This,__FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_IMapView_UINT32_IBuffer IID___FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer
#define IAsyncOperation_IMapView_UINT32_IBufferVtbl __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBufferVtbl
#define IAsyncOperation_IMapView_UINT32_IBuffer __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer
#define IAsyncOperation_IMapView_UINT32_IBuffer_QueryInterface __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_QueryInterface
#define IAsyncOperation_IMapView_UINT32_IBuffer_AddRef __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_AddRef
#define IAsyncOperation_IMapView_UINT32_IBuffer_Release __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_Release
#define IAsyncOperation_IMapView_UINT32_IBuffer_GetIids __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetIids
#define IAsyncOperation_IMapView_UINT32_IBuffer_GetRuntimeClassName __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetRuntimeClassName
#define IAsyncOperation_IMapView_UINT32_IBuffer_GetTrustLevel __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetTrustLevel
#define IAsyncOperation_IMapView_UINT32_IBuffer_put_Completed __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_put_Completed
#define IAsyncOperation_IMapView_UINT32_IBuffer_get_Completed __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_get_Completed
#define IAsyncOperation_IMapView_UINT32_IBuffer_GetResults __FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1___FIMapView_2_UINT32_Windows__CStorage__CStreams__CIBuffer_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService, 0x5c772518, 0x442f, 0x58ed, 0x80,0xcb, 0x53,0x8d,0x34,0xb8,0x82,0x95);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("5c772518-442f-58ed-80cb-538d34b88295")
            IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService*, ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService, 0x5c772518, 0x442f, 0x58ed, 0x80,0xcb, 0x53,0x8d,0x34,0xb8,0x82,0x95)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Release(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > methods ***/
static inline HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_RfcommDeviceService IID___FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService
#define IAsyncOperationCompletedHandler_RfcommDeviceServiceVtbl __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl
#define IAsyncOperationCompletedHandler_RfcommDeviceService __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService
#define IAsyncOperationCompletedHandler_RfcommDeviceService_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_QueryInterface
#define IAsyncOperationCompletedHandler_RfcommDeviceService_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_AddRef
#define IAsyncOperationCompletedHandler_RfcommDeviceService_Release __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Release
#define IAsyncOperationCompletedHandler_RfcommDeviceService_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService, 0x0df56bd7, 0xc8f6, 0x5c32, 0x96,0x44, 0xaa,0x0b,0xcf,0x28,0xd7,0x8c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("0df56bd7-c8f6-5c32-9644-aa0bcf28d78c")
            IAsyncOperation<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService*, ABI::Windows::Devices::Bluetooth::Rfcomm::IRfcommDeviceService* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService, 0x0df56bd7, 0xc8f6, 0x5c32, 0x96,0x44, 0xaa,0x0b,0xcf,0x28,0xd7,0x8c)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *This,
        __x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl;

interface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService {
    CONST_VTBL __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > methods ***/
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_QueryInterface(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_AddRef(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This) {
    return This->lpVtbl->AddRef(This);
}
static inline ULONG __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Release(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetIids(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetTrustLevel(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Devices::Bluetooth::Rfcomm::RfcommDeviceService* > methods ***/
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_put_Completed(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_get_Completed(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,__FIAsyncOperationCompletedHandler_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static inline HRESULT __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetResults(__FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService* This,__x_ABI_CWindows_CDevices_CBluetooth_CRfcomm_CIRfcommDeviceService **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_RfcommDeviceService IID___FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService
#define IAsyncOperation_RfcommDeviceServiceVtbl __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceServiceVtbl
#define IAsyncOperation_RfcommDeviceService __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService
#define IAsyncOperation_RfcommDeviceService_QueryInterface __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_QueryInterface
#define IAsyncOperation_RfcommDeviceService_AddRef __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_AddRef
#define IAsyncOperation_RfcommDeviceService_Release __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_Release
#define IAsyncOperation_RfcommDeviceService_GetIids __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetIids
#define IAsyncOperation_RfcommDeviceService_GetRuntimeClassName __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetRuntimeClassName
#define IAsyncOperation_RfcommDeviceService_GetTrustLevel __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetTrustLevel
#define IAsyncOperation_RfcommDeviceService_put_Completed __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_put_Completed
#define IAsyncOperation_RfcommDeviceService_get_Completed __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_get_Completed
#define IAsyncOperation_RfcommDeviceService_GetResults __FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CDevices__CBluetooth__CRfcomm__CRfcommDeviceService_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_devices_bluetooth_rfcomm_h__ */
